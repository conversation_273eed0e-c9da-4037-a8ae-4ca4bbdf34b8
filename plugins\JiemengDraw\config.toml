[JiemengDraw]
enable = true
command = ["即梦", "jm"]
international_enable = true
international_command = ["即梦国际", "jmi"]
command-format = """
⚙️即梦AI绘画使用说明：

🎨生成图片：
即梦 <详细描述文本> [比例] [大小]
jm <详细描述文本> [比例] [大小]

📝参数说明：
- 比例: 直接输入如 1:1, 16:9, 9:16 等，可放在命令任意位置
- 大小: 直接输入数字(256-2048)，可放在命令任意位置

📝描述建议：
- 尽可能详细描述场景、人物、动作、环境等
- 提示词越详细，生成效果越好
- 建议使用中文描述

📝示例：
即梦 画个美女
即梦 画个美女 9:16
即梦 16:9 画个风景
即梦 画个 1024 动漫少女 9:16
"""

# 自然化响应设置
natural_response = true  # 启用自然化响应，避免机械化回复

# 图片发送配置
[JiemengDraw.image_send]
max_retries = 3  # 图片发送最大重试次数
timeout = 30  # 图片发送超时时间（秒）

# API配置
[JiemengDraw.API]
cookies = "这里填写国内版cookie"

# 设备参数
device_id = "86229099759449"
iid = "86229099218793"

# 令牌桶限流配置
[JiemengDraw.rate_limit]
tokens_per_second = 0.1  # 每10秒生成1个令牌
bucket_size = 3  # 令牌桶容量

# 国际版API配置
[JiemengDraw.InternationalAPI]
# 区域设置
region = "HK"
# web_id参数
web_id = "7483366943875171841"
# sign值
sign = "b02ef4ea67e3ee84d14fbcd75a79b4ff"

# 国际版Cookie (从您提供的信息中提取)
cookies = "uifid_temp=1271a53f98291054236b7318474789dd1d8d14280a987e025c8429e5572a81ad33a3712d1823417457d1b4aa94bb6324b742b634a34cf7323c125f1ce1e86a5ec3c54d4958c00decd5cd9a86c4e33d98; passport_csrf_token=e86926f27ae02347350559d6112005ab; passport_csrf_token_default=e86926f27ae02347350559d6112005ab; passport_csrf_token_wap_state=0aa0a25b3gAToVCgoVPZIGNjYmZmMjI2OGI1MTZkZjQ3YjFlZjdjNjQyNTg3N2M5oU7ZP2h0dHBzOi8vZHJlYW1pbmEuY2FwY3V0LmNvbS9sdi92MS91c2VyL3dlYi9sb2dpbi90aGlyZF9jYWxsYmFja6FWAaFJAKFEAKFB0gAH1mmhTQChSLNkcmVhbWluYS5jYXBjdXQuY29toVIColBM0QjPpkFDVElPTqpsb2dpbl9vbmx5oUzZOmh0dHBzOi8vZHJlYW1pbmEuY2FwY3V0LmNvbS9haS10b29sL2xvZ2luP3NvdXJjZT11cmxfbG9naW6hVNkgMDk0MmQyMjdhZDE4YmNmZGI1YWMzYWQ3YjdjMjAzZGGhVwChRgCiU0EAoVXCok1Mwg%253D%253D; sid_guard=c657e540e40adf4e2ff72d046748cffe%7C1742357230%7C5183999%7CSun%2C+18-May-2025+04%3A07%3A09+GMT; uid_tt=46816871d7e3f487d5224f3348f915173ad7d4e6e38bebb4e36b75ce61b3720f; uid_tt_ss=46816871d7e3f487d5224f3348f915173ad7d4e6e38bebb4e36b75ce61b3720f; sid_tt=c657e540e40adf4e2ff72d046748cffe; sessionid=c657e540e40adf4e2ff72d046748cffe; sessionid_ss=c657e540e40adf4e2ff72d046748cffe; sid_ucp_v1=1.0.0-KDVhODdhODJhMmRkOTdlN2MyNTQ5ZTJkZGZiZmY4OTY4YjM4MTg1OGIKIAiBiODG7eSL7WcQ7oXpvgYY6awfIAwwxt7ovgY4CEASEAMaA3NnMSIgYzY1N2U1NDBlNDBhZGY0ZTJmZjcyZDA0Njc0OGNmZmU; ssid_ucp_v1=1.0.0-KDVhODdhODJhMmRkOTdlN2MyNTQ5ZTJkZGZiZmY4OTY4YjM4MTg1OGIKIAiBiODG7eSL7WcQ7oXpvgYY6awfIAwwxt7ovgY4CEASEAMaA3NnMSIgYzY1N2U1NDBlNDBhZGY0ZTJmZjcyZDA0Njc0OGNmZmU; store-idc=alisg; store-country-code=hk; store-country-code-src=uid; dm_auid=fP0VP2/W4+n+W1labBCunUMjltnVZUFOhnIuREISPHc=; target-store-country-code=hk; _uetsid=ce68b0b0047711f0a2ec51a5cd162a46; _uetvid=ce69b900047711f0b401c9d287c2c4c0; _clck=pqc78x%7C2%7Cfuc%7C0%7C1904; ttwid=1|rKF6Hw6MRGsoo18VCw14lz3jqpdmTix53mwe8Q6DbHI|1742357377|f7678541e236334ebc5c5a2fc8d0c2ce33b362bb6f1b346a0f78f9f2cb04127e; odin_tt=a541930524ff5c5ad76529d63ffcad9482882b31733a91c381430489895e997f09679833b1e76fa1f16fc11b55d6a9d3fe05cfe3d069b5df6c97b436c29db867; msToken=i_2BCccuLLkmoBlyC20IYpimIynozp20DIpXgLDLtTtQEvu8jzKU6INa8naiJ5DzpZdzJQZoGET3KmiqhLxNvE2pjboiiDTS5evRF1DP0ezWQ4RMGpDcgz23qkrrns9YF7z6D66f"