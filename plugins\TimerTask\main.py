import os, json, asyncio, time, re, random, base64
from datetime import datetime, timedelta
from typing import Dict, List, Optional
try:
    import tomllib
except ModuleNotFoundError:
    import tomli as tomllib
from loguru import logger
from pathlib import Path
from WechatAPI import WechatAPIClient
from utils.decorators import on_text_message, on_image_message, on_quote_message, schedule
from utils.plugin_base import PluginBase
from database.keyvalDB import KeyvalDB


class TimerTask(PluginBase):
    description = "定时发送文本或图片"
    author = "XYBot"
    version = "3.0.0"
    plugin_name = "TimerTask"

    def __init__(self):
        super().__init__()
        self.temp_dir = Path("plugins/TimerTask/temp")
        self.temp_dir.mkdir(parents=True, exist_ok=True)
        self._load_config()
        self.user_last_request = {}

        # 任务存储
        self.tasks: Dict[str, dict] = {}
        self.task_counter = 0

        # 数据库键前缀
        self.db_prefix = "timer_task_"
        self.counter_key = "timer_task_counter"

        # 图片消息缓存 - 用于引用消息处理
        self.image_cache: Dict[str, dict] = {}

        # 类型49消息缓存 - 用于引用消息处理
        self.type49_cache: Dict[str, dict] = {}

        # 等待设置内容的任务
        self.pending_tasks: Dict[str, dict] = {}

    def _load_config(self):
        try:
            with open(f"plugins/{self.plugin_name}/config.toml", "rb") as f:
                config = tomllib.load(f).get(self.plugin_name, {})
        except:
            config = {}

        self.enable = config.get("enable", True)
        self.command = config.get("command", ["提醒我", "定时", "闹钟"])
        self.command_format = config.get("command-format", "使用说明")
        self.cooldown = config.get("rate_limit", {}).get("cooldown", 2)
        self.natural_response = config.get("natural_response", False)

    async def on_enable(self, bot=None):
        """插件启用时调用"""
        await super().on_enable(bot)
        await self._load_tasks_from_db()


    async def _load_tasks_from_db(self):
        """从数据库加载任务"""
        try:
            keyval_db = KeyvalDB()
            
            # 加载任务计数器
            counter_str = await keyval_db.get(self.counter_key)
            if counter_str:
                self.task_counter = int(counter_str)
            
            # 加载所有任务
            task_keys = await keyval_db.keys(f"{self.db_prefix}*")
            for key in task_keys:
                if key == self.counter_key:
                    continue
                    
                task_data = await keyval_db.get(key)
                if task_data:
                    try:
                        task = json.loads(task_data)
                        task_id = key.replace(self.db_prefix, "")
                        self.tasks[task_id] = task

                    except json.JSONDecodeError as e:
                        logger.error(f"[{self.plugin_name}] 解析任务数据失败: {e}")
                        
        except Exception as e:
            logger.error(f"[{self.plugin_name}] 加载任务失败: {e}")

    async def _save_task_to_db(self, task_id: str, task_data: dict):
        """保存任务到数据库"""
        try:
            keyval_db = KeyvalDB()
            await keyval_db.set(f"{self.db_prefix}{task_id}", json.dumps(task_data, ensure_ascii=False))
        except Exception as e:
            logger.error(f"[{self.plugin_name}] 保存任务失败: {e}")

    async def _delete_task_from_db(self, task_id: str):
        """从数据库删除任务"""
        try:
            keyval_db = KeyvalDB()
            await keyval_db.delete(f"{self.db_prefix}{task_id}")
        except Exception as e:
            logger.error(f"[{self.plugin_name}] 删除任务失败: {e}")

    async def _save_counter_to_db(self):
        """保存计数器到数据库"""
        try:
            keyval_db = KeyvalDB()
            await keyval_db.set(self.counter_key, str(self.task_counter))
        except Exception as e:
            logger.error(f"[{self.plugin_name}] 保存计数器失败: {e}")

    def _generate_task_id(self) -> str:
        """生成任务ID"""
        self.task_counter += 1
        return f"task_{self.task_counter:03d}"

    async def _simple_confirm(self, bot, wxid):
        """简单确认回复，不艾特人"""
        if self.natural_response:
            confirm_msg = random.choice(self.confirm_msgs)
            await bot.send_text_message(wxid, confirm_msg)

    async def _success_response(self, bot, wxid):
        """成功回复"""
        if self.natural_response:
            success_msg = random.choice(self.success_msgs)
            await bot.send_text_message(wxid, success_msg)

    def _parse_time_expression(self, text: str) -> Optional[dict]:
        """智能解析时间表达式，返回任务信息"""
        text = text.strip()
        now = datetime.now()

        # 1. 相对时间：X分钟后、X小时后、X秒后、X天后
        relative_patterns = [
            (r'(\d+)秒后', 'seconds'),
            (r'(\d+)分钟?后', 'minutes'),
            (r'(\d+)小时后', 'hours'),
            (r'(\d+)天后', 'days')
        ]

        for pattern, unit in relative_patterns:
            match = re.search(pattern, text)
            if match:
                num = int(match.group(1))
                kwargs = {unit: num}
                target_time = now + timedelta(**kwargs)
                return {
                    'type': 'one_time',
                    'target_time': target_time,
                    'description': f"{num}{unit[:-1]}后"
                }

        # 2. 重复任务：每天X点、每小时、每周X（优先处理，避免被通用时间模式匹配）
        if text.startswith('每') or '每周' in text or '每天' in text:
            if '每天' in text:
                # 每天HH:MM 或 每天HH点
                time_match = re.search(r'(\d{1,2}):(\d{2})', text)
                if time_match:
                    hour = int(time_match.group(1))
                    minute = int(time_match.group(2))
                    return {
                        'type': 'repeat',
                        'repeat_type': 'daily',
                        'hour': hour,
                        'minute': minute,
                        'description': f"每天{hour}:{minute:02d}"
                    }

                time_match = re.search(r'(\d{1,2})点?', text)
                if time_match:
                    hour = int(time_match.group(1))
                    return {
                        'type': 'repeat',
                        'repeat_type': 'daily',
                        'hour': hour,
                        'minute': 0,
                        'description': f"每天{hour}点"
                    }

            elif '每周' in text:
                # 每周X HH:MM 或 每周X HH点
                weekday_map = {
                    '一': 0, '二': 1, '三': 2, '四': 3, '五': 4, '六': 5, '日': 6, '天': 6
                }

                for day_name, day_num in weekday_map.items():
                    if f'每周{day_name}' in text:
                        # 匹配时间 HH:MM
                        time_match = re.search(r'(\d{1,2}):(\d{2})', text)
                        if time_match:
                            hour = int(time_match.group(1))
                            minute = int(time_match.group(2))
                            return {
                                'type': 'repeat',
                                'repeat_type': 'weekly',
                                'weekday': day_num,
                                'hour': hour,
                                'minute': minute,
                                'description': f"每周{day_name} {hour}:{minute:02d}"
                            }

                        # 匹配时间 HH点
                        time_match = re.search(r'(\d{1,2})点?', text)
                        if time_match:
                            hour = int(time_match.group(1))
                            return {
                                'type': 'repeat',
                                'repeat_type': 'weekly',
                                'weekday': day_num,
                                'hour': hour,
                                'minute': 0,
                                'description': f"每周{day_name} {hour}点"
                            }

            elif '每小时' in text:
                return {
                    'type': 'repeat',
                    'repeat_type': 'hourly',
                    'description': "每小时"
                }

            # 每X分钟
            minute_match = re.search(r'每(\d+)分钟', text)
            if minute_match:
                minutes = int(minute_match.group(1))
                return {
                    'type': 'repeat',
                    'repeat_type': 'interval',
                    'minutes': minutes,
                    'description': f"每{minutes}分钟"
                }

        # 3. 今天时间：18点、下午6点、晚上8点
        time_patterns = [
            (r'(\d{1,2})点', lambda h: h),
            (r'上午(\d{1,2})点?', lambda h: h),
            (r'下午(\d{1,2})点?', lambda h: h + 12 if h < 12 else h),
            (r'晚上(\d{1,2})点?', lambda h: h + 12 if h < 12 else h),
            (r'(\d{1,2}):(\d{2})', lambda h, m=0: h)
        ]

        for pattern, hour_func in time_patterns:
            match = re.search(pattern, text)
            if match:
                if ':' in pattern:
                    hour = int(match.group(1))
                    minute = int(match.group(2))
                else:
                    hour = hour_func(int(match.group(1)))
                    minute = 0

                target_time = now.replace(hour=hour, minute=minute, second=0, microsecond=0)
                if target_time <= now:
                    target_time += timedelta(days=1)

                return {
                    'type': 'one_time',
                    'target_time': target_time,
                    'description': f"今天{hour}:{minute:02d}" if target_time.date() == now.date() else f"明天{hour}:{minute:02d}"
                }

        # 4. 明天时间：明天8点、明天上午9点
        if '明天' in text:
            time_part = re.sub(r'明天\s*', '', text)
            tomorrow = now.date() + timedelta(days=1)

            # 明天X点
            match = re.search(r'(\d{1,2})点?', time_part)
            if match:
                hour = int(match.group(1))
                target_time = datetime.combine(tomorrow, datetime.min.time().replace(hour=hour))
                return {
                    'type': 'one_time',
                    'target_time': target_time,
                    'description': f"明天{hour}点"
                }

        return None

    def _parse_repeat_expression(self, repeat_str: str) -> Optional[dict]:
        """解析重复表达式"""
        repeat_str = repeat_str.strip()
        
        # 每天 HH:MM
        if repeat_str.startswith('每天'):
            time_part = repeat_str.replace('每天', '').strip()
            try:
                time_obj = datetime.strptime(time_part, "%H:%M").time()
                return {
                    'type': 'daily',
                    'time': time_obj.strftime("%H:%M")
                }
            except ValueError:
                pass
        
        # 每小时
        if repeat_str == '每小时':
            return {
                'type': 'hourly',
                'minute': 0
            }
        
        # 每X分钟
        minute_pattern = r'每(\d+)分钟'
        match = re.search(minute_pattern, repeat_str)
        if match:
            minutes = int(match.group(1))
            return {
                'type': 'interval',
                'minutes': minutes
            }
        
        # 每周X
        weekday_map = {
            '一': 0, '二': 1, '三': 2, '四': 3, '五': 4, '六': 5, '日': 6, '天': 6
        }
        for day_name, day_num in weekday_map.items():
            if f'每周{day_name}' in repeat_str:
                time_part = repeat_str.replace(f'每周{day_name}', '').strip()
                try:
                    time_obj = datetime.strptime(time_part, "%H:%M").time()
                    return {
                        'type': 'weekly',
                        'weekday': day_num,
                        'time': time_obj.strftime("%H:%M")
                    }
                except ValueError:
                    pass
        
        return None

    async def _create_task(self, wxid: str, user_wxid: str, text: str, content_type: str = "text", content_data: dict = None) -> str:
        """创建定时任务"""
        # 智能提取时间
        time_info = self._parse_time_expression(text)
        if not time_info:
            return "时间格式不正确\n支持格式：10分钟后、明天8点、每天18点等"

        task_id = self._generate_task_id()

        # 根据内容类型设置任务数据
        if content_type == "text":
            # 提取消息内容（去掉时间部分）
            message = self._extract_message(text, time_info)
            if not message:
                message = "提醒"

            content_info = {
                'type': 'text',
                'content': message
            }
        elif content_type == "image":
            if not content_data:
                return "图片数据缺失"

            content_info = {
                'type': 'image',
                'image_base64': content_data.get('image_base64'),
                'image_path': content_data.get('image_path'),
                'xml_content': content_data.get('xml_content')
            }
        else:
            return "不支持的内容类型"

        if time_info['type'] == 'one_time':
            target_time = time_info['target_time']
            if target_time <= datetime.now():
                return "设置的时间已经过去了"

            task_data = {
                'id': task_id,
                'type': 'one_time',
                'wxid': wxid,
                'user_wxid': user_wxid,
                'content': content_info,
                'target_time': target_time.isoformat(),
                'created_time': datetime.now().isoformat(),
                'status': 'active',
                'description': time_info['description']
            }

            result = "好的，已设置"

        else:  # repeat
            task_data = {
                'id': task_id,
                'type': 'repeat',
                'wxid': wxid,
                'user_wxid': user_wxid,
                'content': content_info,
                'repeat_config': time_info,
                'created_time': datetime.now().isoformat(),
                'status': 'active',
                'last_run': None,
                'run_count': 0,
                'description': time_info['description']
            }

            result = "好的，已设置"

        self.tasks[task_id] = task_data
        await self._save_task_to_db(task_id, task_data)
        await self._save_counter_to_db()

        # 记录任务创建信息
        logger.info(f"[{self.plugin_name}] 创建任务 {task_id} - 群组: {wxid}, 用户: {user_wxid}, 类型: {content_type}")

        return result

    def _extract_message(self, text: str, time_info: dict) -> str:
        """从文本中提取消息内容 - 简化版本，只移除命令词和时间表达式"""

        message = text

        # 1. 移除命令词
        for cmd in self.command:
            if message.startswith(cmd):
                message = message[len(cmd):].strip()
                break

        # 2. 移除时间表达式 - 只移除开头的时间表达式
        # 相对时间
        message = re.sub(r'^\d+秒后\s*', '', message)
        message = re.sub(r'^\d+分钟?后\s*', '', message)
        message = re.sub(r'^\d+小时后\s*', '', message)
        message = re.sub(r'^\d+天后\s*', '', message)

        # 绝对时间
        message = re.sub(r'^明天\s*\d{1,2}点?\s*', '', message)
        message = re.sub(r'^\d{1,2}点\s*', '', message)
        message = re.sub(r'^上午\s*\d{1,2}点?\s*', '', message)
        message = re.sub(r'^下午\s*\d{1,2}点?\s*', '', message)
        message = re.sub(r'^晚上\s*\d{1,2}点?\s*', '', message)
        message = re.sub(r'^\d{1,2}:\d{2}\s*', '', message)

        # 重复时间
        message = re.sub(r'^每天\s*\d{1,2}:\d{2}\s*', '', message)
        message = re.sub(r'^每天\s*\d{1,2}点?\s*', '', message)
        message = re.sub(r'^每周[一二三四五六日天]\s*\d{1,2}:\d{2}\s*', '', message)
        message = re.sub(r'^每周[一二三四五六日天]\s*\d{1,2}点?\s*', '', message)
        message = re.sub(r'^每小时\s*', '', message)
        message = re.sub(r'^每\d+分钟\s*', '', message)

        # 3. 保持原始格式，只清理首尾空白
        message = message.strip()

        return message if message else "提醒"

    def _format_time_diff(self, time_diff: timedelta) -> str:
        """格式化时间差"""
        total_seconds = int(time_diff.total_seconds())

        if total_seconds < 60:
            return f"{total_seconds}秒后"
        elif total_seconds < 3600:
            minutes = total_seconds // 60
            return f"{minutes}分钟后"
        elif total_seconds < 86400:
            hours = total_seconds // 3600
            minutes = (total_seconds % 3600) // 60
            if minutes > 0:
                return f"{hours}小时{minutes}分钟后"
            return f"{hours}小时后"
        else:
            days = total_seconds // 86400
            hours = (total_seconds % 86400) // 3600
            if hours > 0:
                return f"{days}天{hours}小时后"
            return f"{days}天后"

    def _calculate_next_repeat_run(self, repeat_config: dict) -> Optional[datetime]:
        """计算重复任务的下次执行时间"""
        now = datetime.now()

        if repeat_config['repeat_type'] == 'daily':
            hour = repeat_config['hour']
            minute = repeat_config.get('minute', 0)
            next_run = now.replace(hour=hour, minute=minute, second=0, microsecond=0)
            if next_run <= now:
                next_run += timedelta(days=1)
            return next_run

        elif repeat_config['repeat_type'] == 'weekly':
            target_weekday = repeat_config['weekday']  # 0=周一, 6=周日
            hour = repeat_config['hour']
            minute = repeat_config.get('minute', 0)

            # 计算当前是周几 (0=周一, 6=周日)
            current_weekday = now.weekday()

            # 计算距离目标星期几还有几天
            days_ahead = target_weekday - current_weekday

            # 如果是今天但时间已过，或者是之前的日期，则计算下周
            if days_ahead < 0 or (days_ahead == 0 and now.time() >= datetime.min.time().replace(hour=hour, minute=minute)):
                days_ahead += 7

            next_run = now.replace(hour=hour, minute=minute, second=0, microsecond=0) + timedelta(days=days_ahead)
            return next_run

        elif repeat_config['repeat_type'] == 'hourly':
            next_run = now.replace(minute=0, second=0, microsecond=0) + timedelta(hours=1)
            return next_run

        elif repeat_config['repeat_type'] == 'interval':
            minutes = repeat_config['minutes']
            next_run = now + timedelta(minutes=minutes)
            return next_run

        return None

    async def _list_tasks(self, wxid: str, user_wxid: str) -> str:
        """列出用户在当前群组的所有任务"""
        # 只显示当前群组的任务，确保群组隔离
        user_tasks = [task for task in self.tasks.values()
                     if task['wxid'] == wxid and task['user_wxid'] == user_wxid and task['status'] == 'active']

        if not user_tasks:
            return "📝 本群还没有设置任何定时任务"

        result = f"📋 本群定时任务（{len(user_tasks)}个）\n\n"

        for i, task in enumerate(user_tasks, 1):
            content = task.get('content', {})
            content_desc = content.get('content', '[图片]') if content.get('type') == 'text' else '[图片]'

            # 简化显示，只显示关键信息
            if len(content_desc) > 20:
                content_desc = content_desc[:20] + "..."

            # 检查是否有提醒用户
            reminder_users = task.get('reminder_users', [])
            reminder_indicator = f" 📢{len(reminder_users)}" if reminder_users else ""

            if task['type'] == 'one_time':
                target_time = datetime.fromisoformat(task['target_time'])
                time_diff = target_time - datetime.now()
                if time_diff.total_seconds() > 0:
                    time_desc = self._format_time_diff(time_diff)
                    result += f"{i}. ⏰ {task['description']} ({time_desc}) - {content_desc}{reminder_indicator}\n"
                else:
                    result += f"{i}. ⏰ {task['description']} (即将执行) - {content_desc}{reminder_indicator}\n"
            else:
                next_run = self._calculate_next_repeat_run(task['repeat_config'])
                next_desc = next_run.strftime('%m-%d %H:%M') if next_run else "计算中"
                run_count = task.get('run_count', 0)
                result += f"{i}. 🔄 {task['description']} (下次: {next_desc}) - {content_desc}{reminder_indicator}\n"
                if run_count > 0:
                    result += f"    已执行{run_count}次\n"

        result += "💡 管理命令：\n"
        result += "• 删除群任务[编号] - 删除指定任务\n"
        result += "• 修改群任务[编号][新时间] - 修改任务时间\n"
        result += "• 任务[编号]提醒我 - 设置个人艾特提醒\n"
        result += "• 清空群任务 - 删除所有任务"
        return result.strip()

    async def _delete_task_by_index(self, wxid: str, user_wxid: str, index: int) -> str:
        """按编号删除任务（仅限当前群组）"""
        user_tasks = [task for task in self.tasks.values()
                     if task['wxid'] == wxid and task['user_wxid'] == user_wxid and task['status'] == 'active']

        if not user_tasks:
            return "本群没有任何任务可以删除"

        if index < 1 or index > len(user_tasks):
            return f"编号错误，请输入1-{len(user_tasks)}之间的数字"

        task = user_tasks[index - 1]
        task['status'] = 'deleted'
        await self._save_task_to_db(task['id'], task)

        content = task.get('content', {})
        content_desc = content.get('content', '[图片]') if content.get('type') == 'text' else '[图片]'
        return f"✅ 已删除任务：{task['description']} - {content_desc}"

    async def _clear_all_tasks(self, wxid: str, user_wxid: str) -> str:
        """清空用户在当前群组的所有任务"""
        user_tasks = [task for task in self.tasks.values()
                     if task['wxid'] == wxid and task['user_wxid'] == user_wxid and task['status'] == 'active']

        if not user_tasks:
            return "本群没有任何任务需要清空"

        count = 0
        for task in user_tasks:
            task['status'] = 'deleted'
            await self._save_task_to_db(task['id'], task)
            count += 1

        return f"✅ 已清空本群的所有任务（{count}个）"

    async def _modify_task_time(self, wxid: str, user_wxid: str, index: int, new_time_text: str) -> str:
        """修改任务的时间（仅限当前群组）"""
        user_tasks = [task for task in self.tasks.values()
                     if task['wxid'] == wxid and task['user_wxid'] == user_wxid and task['status'] == 'active']

        if not user_tasks:
            return "本群没有任何任务可以修改"

        if index < 1 or index > len(user_tasks):
            return f"编号错误，请输入1-{len(user_tasks)}之间的数字"

        task = user_tasks[index - 1]

        # 解析新的时间信息
        new_time_info = self._parse_time_expression(new_time_text)
        if not new_time_info:
            return "新时间格式不正确\n支持格式：10分钟后、明天8点、每周五18:00等"

        # 保存原始信息用于回滚
        old_description = task['description']

        try:
            # 更新任务时间信息
            if new_time_info['type'] == 'one_time':
                target_time = new_time_info['target_time']
                if target_time <= datetime.now():
                    return "设置的时间已经过去了"

                task['type'] = 'one_time'
                task['target_time'] = target_time.isoformat()
                task['description'] = new_time_info['description']

                # 移除重复任务相关字段
                task.pop('repeat_config', None)
                task.pop('last_run', None)
                task.pop('run_count', None)

                time_diff = target_time - datetime.now()
                time_desc = self._format_time_diff(time_diff)
                result_msg = f"⏰ {new_time_info['description']} ({time_desc})"

            else:  # repeat
                task['type'] = 'repeat'
                task['repeat_config'] = new_time_info
                task['description'] = new_time_info['description']
                task['last_run'] = None
                task['run_count'] = 0

                # 移除一次性任务相关字段
                task.pop('target_time', None)

                next_run = self._calculate_next_repeat_run(new_time_info)
                next_desc = next_run.strftime('%m-%d %H:%M') if next_run else "计算中"
                result_msg = f"🔄 {new_time_info['description']}\n⏰ 下次: {next_desc}"

            # 保存到数据库
            await self._save_task_to_db(task['id'], task)

            content = task.get('content', {})
            content_desc = content.get('content', '[图片]') if content.get('type') == 'text' else '[图片]'

            return f"✅ 任务时间已修改\n📝 内容: {content_desc}\n{result_msg}"

        except Exception as e:
            # 回滚
            task['description'] = old_description
            logger.error(f"[{self.plugin_name}] 修改任务时间失败: {e}")
            return "修改失败，请重试"

    async def _modify_task_by_keyword(self, wxid: str, user_wxid: str, new_time_text: str, keyword: str) -> str:
        """通过关键词修改任务时间（仅限当前群组）"""
        user_tasks = [task for task in self.tasks.values()
                     if task['wxid'] == wxid and task['user_wxid'] == user_wxid and task['status'] == 'active']

        if not user_tasks:
            return "本群没有任何任务可以修改"

        # 查找包含关键词的任务
        matching_tasks = []
        for i, task in enumerate(user_tasks):
            content = task.get('content', {})
            content_text = content.get('content', '') if content.get('type') == 'text' else ''

            if keyword and keyword in content_text:
                matching_tasks.append((i + 1, task))
            elif not keyword:  # 如果没有关键词，匹配最近的任务
                matching_tasks.append((i + 1, task))

        if not matching_tasks:
            return f"未找到包含关键词 '{keyword}' 的任务"

        if len(matching_tasks) > 1 and keyword:
            # 多个匹配，显示选项
            result = f"找到 {len(matching_tasks)} 个包含 '{keyword}' 的任务：\n\n"
            for index, task in matching_tasks:
                content = task.get('content', {})
                content_desc = content.get('content', '[图片]') if content.get('type') == 'text' else '[图片]'
                result += f"{index}. {task['description']} - {content_desc[:30]}...\n"
            result += f"\n请使用：修改任务 [编号] {new_time_text}"
            return result

        # 只有一个匹配或没有关键词时，直接修改第一个
        index, task = matching_tasks[0]
        return await self._modify_task_time(wxid, user_wxid, index, new_time_text)

    async def _set_task_reminder(self, wxid: str, user_wxid: str, task_index: int) -> str:
        """为指定任务设置个人提醒"""
        # 获取当前群组的任务列表
        user_tasks = [task for task in self.tasks.values()
                     if task['wxid'] == wxid and task['status'] == 'active']

        if not user_tasks:
            return "本群没有任何任务"

        if task_index < 1 or task_index > len(user_tasks):
            return f"任务编号错误，本群只有{len(user_tasks)}个任务"

        task = user_tasks[task_index - 1]

        # 检查任务是否已经有提醒用户
        if 'reminder_users' not in task:
            task['reminder_users'] = []

        # 检查用户是否已经设置了提醒
        if user_wxid in task['reminder_users']:
            return f"您已经设置了任务{task_index}的提醒"

        # 添加提醒用户
        task['reminder_users'].append(user_wxid)

        # 保存到数据库
        await self._save_task_to_db(task['id'], task)

        # 获取任务描述
        content = task.get('content', {})
        content_desc = content.get('content', '[图片]') if content.get('type') == 'text' else '[图片]'
        if len(content_desc) > 20:
            content_desc = content_desc[:20] + "..."

        return f"好的，任务{task_index}执行时会单独艾特您\n📝 {task['description']} - {content_desc}"

    def _check_user_limit(self, wxid: str, user_wxid: str) -> float:
        user_key = f"{wxid}_{user_wxid}"
        last_request = self.user_last_request.get(user_key, 0)
        current_time = time.time()
        elapsed = current_time - last_request
        self.user_last_request[user_key] = current_time

        if elapsed < self.cooldown:
            return self.cooldown - elapsed

        return 0

    @on_text_message
    async def handle_text(self, bot: WechatAPIClient, message: dict):
        if not self.enable:
            return

        content = str(message["Content"]).strip()
        wxid = message["FromWxid"]
        user_wxid = message["SenderWxid"]

        # 处理查看和管理命令
        if content in ["群任务", "任务列表", "我的任务", "我的提醒"]:  # 保留旧命令兼容性
            result = await self._list_tasks(wxid, user_wxid)
            await bot.send_text_message(wxid, result)
            return

        if content in ["清空群任务", "清空任务", "清空提醒"]:  # 保留旧命令兼容性
            result = await self._clear_all_tasks(wxid, user_wxid)
            await bot.send_text_message(wxid, result)
            return

        if (content.startswith("删除群任务") or content.startswith("删除任务") or
            content.startswith("删除提醒")):  # 保留旧命令兼容性
            try:
                # 提取编号
                cmd_text = (content.replace("删除群任务", "")
                           .replace("删除任务", "")
                           .replace("删除提醒", "").strip())
                index = int(cmd_text)
                result = await self._delete_task_by_index(wxid, user_wxid, index)
                await bot.send_text_message(wxid, result)
            except ValueError:
                await bot.send_text_message(wxid, "请输入正确的任务编号，如：删除群任务1")
            return

        # 处理修改任务时间命令
        if (content.startswith("修改群任务") or content.startswith("修改任务") or
            content.startswith("改时间")):  # 保留旧命令兼容性
            try:
                # 智能解析：支持多种格式
                cmd_text = (content.replace("修改群任务", "")
                           .replace("修改任务", "")
                           .replace("改时间", "").strip())

                # 使用正则提取编号和时间
                match = re.match(r'(\d+)\s*(.*)', cmd_text)
                if not match:
                    await bot.send_text_message(wxid, "格式：修改群任务[编号][新时间]\n例如：修改群任务1每周五18:00")
                    return

                index = int(match.group(1))
                new_time_text = match.group(2).strip()

                if not new_time_text:
                    await bot.send_text_message(wxid, "请指定新时间，如：修改群任务1每周五18:00")
                    return

                result = await self._modify_task_time(wxid, user_wxid, index, new_time_text)
                await bot.send_text_message(wxid, result)
            except ValueError:
                await bot.send_text_message(wxid, "请输入正确的格式，如：修改群任务1每周五18:00")
            return

        # 处理快速修改命令（通过关键词）
        if content.startswith("改成"):
            try:
                # 智能解析：改成每周五18:00钻石 / 改成 每周五 18:00 钻石
                cmd_text = content[2:].strip()  # 移除"改成"

                # 使用更智能的解析方式
                # 先尝试找到时间模式，然后分离关键词
                time_patterns = [
                    r'(每周[一二三四五六日天]\s*\d{1,2}:\d{2})',
                    r'(每周[一二三四五六日天]\s*\d{1,2}点?)',
                    r'(每天\s*\d{1,2}:\d{2})',
                    r'(每天\s*\d{1,2}点?)',
                    r'(明天\s*\d{1,2}:\d{2})',
                    r'(明天\s*\d{1,2}点?)',
                    r'(\d{1,2}:\d{2})',
                    r'(\d{1,2}点?)',
                    r'(\d+分钟后)',
                    r'(\d+小时后)',
                    r'(\d+秒后)'
                ]

                new_time_text = ""
                keyword = ""

                for pattern in time_patterns:
                    match = re.search(pattern, cmd_text)
                    if match:
                        new_time_text = match.group(1)
                        # 提取关键词（时间表达式之后的部分）
                        keyword = cmd_text.replace(new_time_text, "").strip()
                        break

                if not new_time_text:
                    await bot.send_text_message(wxid, "格式：改成[新时间][关键词]\n例如：改成每周五18:00钻石 或 改成 每周五 18:00 钻石")
                    return

                result = await self._modify_task_by_keyword(wxid, user_wxid, new_time_text, keyword)
                await bot.send_text_message(wxid, result)
            except Exception as e:
                logger.error(f"[{self.plugin_name}] 快速修改失败: {e}")
                await bot.send_text_message(wxid, "修改失败，请检查格式")
            return

        # 处理任务提醒命令（任务1提醒我）
        if re.match(r'任务\d+提醒我', content):
            try:
                # 提取任务编号
                match = re.search(r'任务(\d+)提醒我', content)
                if match:
                    task_index = int(match.group(1))
                    result = await self._set_task_reminder(wxid, user_wxid, task_index)
                    await bot.send_text_message(wxid, result)
                    return
            except Exception as e:
                logger.error(f"[{self.plugin_name}] 设置任务提醒失败: {e}")
                await bot.send_text_message(wxid, "设置提醒失败")
                return

        # 处理创建文本任务命令
        for cmd in self.command:
            if content.startswith(cmd):
                # 限流检查
                wait_time = self._check_user_limit(wxid, user_wxid)
                if wait_time > 0:
                    await bot.send_text_message(wxid, f"请等待 {wait_time:.1f} 秒")
                    return

                try:
                    # 提取时间和消息内容
                    text = content[len(cmd):].strip()
                    if not text:
                        await bot.send_text_message(wxid, self.command_format)
                        return

                    result = await self._create_task(wxid, user_wxid, text, "text")
                    await bot.send_text_message(wxid, result)

                except Exception as e:
                    logger.error(f"[{self.plugin_name}] 异常: {e}")
                    await bot.send_text_message(wxid, "创建任务失败，请检查格式")
                return

    @on_image_message
    async def handle_image(self, bot: WechatAPIClient, message: dict):
        """处理图片消息，缓存图片信息"""
        if not self.enable:
            return

        try:
            msg_id = message.get("MsgId", "")
            xml_content = message.get("Content", "")
            wxid = message.get("FromWxid", "")
            sender_wxid = message.get("SenderWxid", "")

            # 缓存图片信息，用于后续引用
            self.image_cache[msg_id] = {
                'xml_content': xml_content,
                'wxid': wxid,
                'sender_wxid': sender_wxid,
                'timestamp': time.time()
            }

            logger.info(f"[{self.plugin_name}] 缓存图片消息: {msg_id}")

        except Exception as e:
            logger.error(f"[{self.plugin_name}] 处理图片消息失败: {e}")

    async def handle_message(self, bot: WechatAPIClient, message: dict):
        """处理所有消息，缓存类型49消息"""
        if not self.enable:
            return

        try:
            msg_type = message.get("Type", "")
            msg_id = message.get("MsgId", "")

            # 只处理类型49消息
            if msg_type == 49:
                content = message.get("Content", "")
                wxid = message.get("FromWxid", "")
                sender_wxid = message.get("SenderWxid", "")

                # 缓存类型49消息信息
                self.type49_cache[msg_id] = {
                    'content': content,
                    'wxid': wxid,
                    'sender_wxid': sender_wxid,
                    'timestamp': time.time(),
                    'type': msg_type
                }

                logger.info(f"[{self.plugin_name}] 缓存类型49消息: {msg_id}")
                logger.info(f"[{self.plugin_name}] 类型49消息内容预览: {content[:200]}...")

        except Exception as e:
            logger.error(f"[{self.plugin_name}] 处理类型49消息失败: {e}")

    @on_quote_message
    async def handle_quote(self, bot: WechatAPIClient, message: dict):
        """处理引用消息，支持引用图片和类型49消息设置定时任务"""
        if not self.enable:
            return

        try:
            content = str(message.get("Content", "")).strip()
            wxid = message.get("FromWxid", "")
            user_wxid = message.get("SenderWxid", "")
            quote_info = message.get("Quote", {})

            # 添加详细的调试信息
            logger.info(f"[{self.plugin_name}] 收到引用消息调试信息:")
            logger.info(f"  - 消息内容: {content}")
            logger.info(f"  - 群组ID: {wxid}")
            logger.info(f"  - 发送人: {user_wxid}")
            logger.info(f"  - 引用信息: {quote_info}")

            # 获取引用消息的详细信息
            quoted_msg_id = quote_info.get("MsgId", "")
            quoted_type = quote_info.get("Type", "")
            quoted_content = quote_info.get("Content", "")
            quoted_sender = quote_info.get("SenderWxid", "")

            logger.info(f"  - 引用消息ID: {quoted_msg_id}")
            logger.info(f"  - 引用消息类型: {quoted_type}")
            logger.info(f"  - 引用消息内容: {quoted_content}")
            logger.info(f"  - 引用消息发送人: {quoted_sender}")

            # 检查是否是定时任务命令
            is_timer_command = False
            for cmd in self.command:
                if content.startswith(cmd):
                    is_timer_command = True
                    break

            if not is_timer_command:
                return

            # 限流检查
            wait_time = self._check_user_limit(wxid, user_wxid)
            if wait_time > 0:
                await bot.send_text_message(wxid, f"请等待 {wait_time:.1f} 秒")
                return

            if not quoted_msg_id:
                await bot.send_text_message(wxid, "无法获取引用消息信息")
                return

            # 检查引用消息类型并处理
            if quoted_msg_id in self.image_cache:
                # 处理图片消息
                await self._handle_quoted_image_task(bot, wxid, user_wxid, content, quoted_msg_id)
            elif quoted_type == "49":
                # 处理类型49消息
                logger.info(f"[{self.plugin_name}] 检测到类型49消息，准备处理定时任务")
                await self._handle_quoted_type49_task(bot, wxid, user_wxid, content, quote_info)
            else:
                await bot.send_text_message(wxid, f"暂不支持该类型消息的定时发送\n引用消息类型: {quoted_type}")

        except Exception as e:
            logger.error(f"[{self.plugin_name}] 处理引用消息失败: {e}")
            await bot.send_text_message(wxid, "处理引用消息失败")

    async def _handle_quoted_image_task(self, bot: WechatAPIClient, wxid: str, user_wxid: str, content: str, quoted_msg_id: str):
        """处理引用图片的定时任务"""
        try:
            # 获取缓存的图片信息
            image_info = self.image_cache.get(quoted_msg_id)
            if not image_info:
                await bot.send_text_message(wxid, "图片信息已过期，请重新发送图片后引用")
                return

            # 下载图片
            image_data = await self._download_and_save_image(bot, image_info)
            if not image_data:
                await bot.send_text_message(wxid, "图片下载失败")
                return

            # 提取时间信息
            for cmd in self.command:
                if content.startswith(cmd):
                    time_text = content[len(cmd):].strip()
                    if not time_text:
                        await bot.send_text_message(wxid, "请指定时间，如：定时 10分钟后")
                        return

                    # 创建图片任务
                    result = await self._create_task(wxid, user_wxid, time_text, "image", image_data)
                    await bot.send_text_message(wxid, result)
                    return

        except Exception as e:
            logger.error(f"[{self.plugin_name}] 处理引用图片任务失败: {e}")
            await bot.send_text_message(wxid, "创建图片任务失败")

    async def _download_and_save_image(self, bot: WechatAPIClient, image_info: dict) -> Optional[dict]:
        """下载并保存图片"""
        try:
            xml_content = image_info['xml_content']

            # 解析XML获取图片信息
            import xml.etree.ElementTree as ET
            root = ET.fromstring(xml_content)
            img_node = root.find('img')

            if img_node is None:
                return None

            aeskey = img_node.get('aeskey')
            cdnmidimgurl = img_node.get('cdnmidimgurl')

            if not aeskey or not cdnmidimgurl:
                return None

            # 下载图片
            image_base64 = await self._download_image_from_cdn(bot, aeskey, cdnmidimgurl)
            if not image_base64:
                return None

            # 保存图片到本地
            timestamp = int(time.time())
            filename = f"timer_image_{timestamp}.jpg"
            filepath = self.temp_dir / filename

            # 保存base64图片到文件
            image_bytes = base64.b64decode(image_base64)
            with open(filepath, 'wb') as f:
                f.write(image_bytes)

            return {
                'image_base64': image_base64,
                'image_path': str(filepath),
                'xml_content': xml_content
            }

        except Exception as e:
            logger.error(f"[{self.plugin_name}] 下载保存图片失败: {e}")
            return None

    async def _download_image_from_cdn(self, bot: WechatAPIClient, aeskey: str, cdnurl: str) -> Optional[str]:
        """从CDN下载图片"""
        try:
            # 使用机器人的下载图片API
            if hasattr(bot, 'download_image'):
                return await bot.download_image(aeskey, cdnurl)
            else:
                logger.warning(f"[{self.plugin_name}] 机器人不支持下载图片API")
                return None
        except Exception as e:
            logger.error(f"[{self.plugin_name}] CDN下载图片失败: {e}")
            return None

    @schedule('interval', seconds=10)  # 10秒检查一次，确保时间准确性
    async def check_tasks(self, bot: WechatAPIClient):
        """定时检查任务执行 - 高精度时间检查"""
        if not self.enable:
            return

        now = datetime.now()
        executed_tasks = []

        for task_id, task in list(self.tasks.items()):
            if task['status'] != 'active':
                continue

            try:
                if task['type'] == 'one_time':
                    target_time = datetime.fromisoformat(task['target_time'])
                    # 精确到秒的时间检查
                    if now >= target_time:
                        await self._execute_task(bot, task)
                        task['status'] = 'completed'
                        task['completed_time'] = now.isoformat()
                        executed_tasks.append(task_id)


                elif task['type'] == 'repeat':
                    # 检查是否到了执行时间
                    should_execute = self._should_execute_repeat_task(task, now)
                    if should_execute:
                        await self._execute_task(bot, task)
                        task['last_run'] = now.isoformat()
                        task['run_count'] = task.get('run_count', 0) + 1
                        executed_tasks.append(task_id)


            except Exception as e:
                logger.error(f"[{self.plugin_name}] 执行任务 {task_id} 失败: {e}")

        # 保存已执行的任务
        for task_id in executed_tasks:
            await self._save_task_to_db(task_id, self.tasks[task_id])

        # 每分钟清理一次
        if now.second == 0:
            await self._cleanup_tasks()
            self._cleanup_image_cache()

    def _should_execute_repeat_task(self, task: dict, now: datetime) -> bool:
        """判断重复任务是否应该执行"""
        repeat_config = task['repeat_config']
        last_run_str = task.get('last_run')

        if repeat_config['repeat_type'] == 'daily':
            target_hour = repeat_config['hour']
            target_minute = repeat_config.get('minute', 0)

            # 检查是否到了指定时间（精确到分钟）
            if now.hour == target_hour and now.minute == target_minute:
                # 检查今天是否已经执行过
                if last_run_str:
                    last_run = datetime.fromisoformat(last_run_str)
                    if last_run.date() == now.date():
                        return False
                return True

        elif repeat_config['repeat_type'] == 'weekly':
            target_weekday = repeat_config['weekday']  # 0=周一, 6=周日
            target_hour = repeat_config['hour']
            target_minute = repeat_config.get('minute', 0)

            # 检查是否是目标星期几和时间
            current_weekday = now.weekday()
            if (current_weekday == target_weekday and
                now.hour == target_hour and
                now.minute == target_minute):

                # 检查本周是否已经执行过
                if last_run_str:
                    last_run = datetime.fromisoformat(last_run_str)
                    # 计算本周的开始时间（周一00:00）
                    week_start = now - timedelta(days=now.weekday())
                    week_start = week_start.replace(hour=0, minute=0, second=0, microsecond=0)

                    if last_run >= week_start:
                        return False
                return True

        elif repeat_config['repeat_type'] == 'hourly':
            # 每小时的整点执行
            if now.minute == 0:
                if last_run_str:
                    last_run = datetime.fromisoformat(last_run_str)
                    # 检查这个小时是否已经执行过
                    if last_run.hour == now.hour and last_run.date() == now.date():
                        return False
                return True

        elif repeat_config['repeat_type'] == 'interval':
            minutes = repeat_config['minutes']
            if last_run_str:
                last_run = datetime.fromisoformat(last_run_str)
                time_diff = now - last_run
                if time_diff.total_seconds() >= minutes * 60:
                    return True
            else:
                # 首次执行
                return True

        return False

    async def _execute_task(self, bot: WechatAPIClient, task: dict):
        """执行任务 - 严格按群组隔离"""
        try:
            wxid = task['wxid']  # 任务创建时的群组ID
            user_wxid = task['user_wxid']  # 任务创建者
            content = task.get('content', {})
            content_type = content.get('type', 'text')

            # 记录任务执行信息
            logger.info(f"[{self.plugin_name}] 执行任务 {task['id']} - 群组: {wxid}, 用户: {user_wxid}, 类型: {content_type}")

            if content_type == 'text':
                # 发送文本消息到指定群组
                message = content.get('content', '提醒')
                await bot.send_text_message(wxid, message)
                logger.info(f"[{self.plugin_name}] 文本任务执行成功 - 群组: {wxid}")

                # 检查是否有需要单独艾特的用户
                reminder_users = task.get('reminder_users', [])
                if reminder_users:
                    # 发送艾特消息
                    at_message = f"⏰ 任务提醒"
                    await bot.send_at_message(wxid, at_message, reminder_users)
                    logger.info(f"[{self.plugin_name}] 发送艾特提醒成功 - 群组: {wxid}, 用户: {reminder_users}")

            elif content_type == 'image':
                # 发送图片消息到指定群组
                image_base64 = content.get('image_base64')
                image_path = content.get('image_path')

                if image_base64:
                    # 优先使用base64
                    await bot.send_image_message(wxid, image_base64)
                    logger.info(f"[{self.plugin_name}] 图片任务执行成功(base64) - 群组: {wxid}")
                elif image_path and os.path.exists(image_path):
                    # 使用文件路径
                    await bot.send_image_message(wxid, image_path)
                    logger.info(f"[{self.plugin_name}] 图片任务执行成功(文件) - 群组: {wxid}")
                else:
                    # 图片数据丢失，发送错误提醒到原群组
                    await bot.send_text_message(wxid, "⚠️ 定时图片已丢失，无法发送")
                    logger.warning(f"[{self.plugin_name}] 图片数据丢失 - 群组: {wxid}")

                # 检查是否有需要单独艾特的用户（图片任务也支持艾特）
                reminder_users = task.get('reminder_users', [])
                if reminder_users:
                    # 发送艾特消息
                    at_message = f"⏰ 图片任务提醒"
                    await bot.send_at_message(wxid, at_message, reminder_users)
                    logger.info(f"[{self.plugin_name}] 发送图片艾特提醒成功 - 群组: {wxid}, 用户: {reminder_users}")

        except Exception as e:
            logger.error(f"[{self.plugin_name}] 任务执行失败 - 群组: {task.get('wxid', 'unknown')}, 错误: {e}")
            try:
                # 错误提醒也发送到原群组
                await bot.send_text_message(task['wxid'], "⚠️ 定时任务执行失败")
            except:
                pass

    async def _cleanup_tasks(self):
        """清理已删除和过期的任务"""
        try:
            to_remove = []
            now = datetime.now()

            for task_id, task in self.tasks.items():
                # 删除已标记为删除的任务
                if task['status'] == 'deleted':
                    to_remove.append(task_id)
                    await self._delete_task_from_db(task_id)

                # 删除已完成的一次性任务（保留1小时）
                elif task['status'] == 'completed' and task['type'] == 'one_time':
                    completed_time = datetime.fromisoformat(task.get('completed_time', task['target_time']))
                    if now - completed_time > timedelta(hours=1):
                        to_remove.append(task_id)
                        await self._delete_task_from_db(task_id)

            # 从内存中移除
            for task_id in to_remove:
                if task_id in self.tasks:
                    del self.tasks[task_id]

            if to_remove:
                pass

        except Exception as e:
            logger.error(f"[{self.plugin_name}] 清理任务失败: {e}")

    def _cleanup_image_cache(self):
        """清理过期的图片缓存"""
        try:
            current_time = time.time()
            expired_keys = []

            # 清理超过1小时的图片缓存
            for msg_id, cache_info in self.image_cache.items():
                if current_time - cache_info['timestamp'] > 3600:  # 1小时
                    expired_keys.append(msg_id)

            for key in expired_keys:
                del self.image_cache[key]

            if expired_keys:
                logger.info(f"[{self.plugin_name}] 清理过期图片缓存: {len(expired_keys)}个")

        except Exception as e:
            logger.error(f"[{self.plugin_name}] 清理图片缓存失败: {e}")
