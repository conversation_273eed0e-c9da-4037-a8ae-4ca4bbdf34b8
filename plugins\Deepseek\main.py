import json
import re
import tomllib
import traceback
import asyncio
import os
import time
import uuid
import hmac
import hashlib
import base64
import random
from typing import Optional, Dict, Any

import httpx
import aiofiles
from loguru import logger

from WechatAPI import WechatAPIClient
from utils.decorators import *
from utils.plugin_base import PluginBase
from utils.temp_file_manager import cleanup_file
from plugins.text2card_project.text_card_service import TextCardService, TextCardError

class DeepseekPlugin(PluginBase):
    """Deepseek模型插件"""

    description = "Deepseek大模型对话插件"
    author = "Claude"
    version = "1.0.0"

    def __init__(self):
        super().__init__()

        # 读取配置
        with open("plugins/Deepseek/config.toml", "rb") as f:
            plugin_config = tomllib.load(f)
        config = plugin_config.get("Deepseek", {})

        # 基本配置
        self.enable = config.get("enable", True)
        self.command = config.get("command", ["deepseek", "ds"])
        self.natural_response = config.get("natural_response", True)
        self.update_command = config.get("update_command", ["更新deepseek", "更新ds"])

        # 读取管理员配置
        self._load_admin_config()

        # API配置
        self.api_url = config.get("api-url", "https://chatgpt.vivo.com.cn/chatgpt-api/chat/public/completions")
        self.headers = {
            "Connection": "keep-alive",
            "X-AI-GATEWAY-TIMESTAMP": config.get("timestamp", ""),
            "X-uid": config.get("uid", ""),
            "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/116.0.0.0 Safari/537.36",
            "X-AI-GATEWAY-NONCE": config.get("nonce", ""),
            "Content-Type": "application/json",
            "X-AI-GATEWAY-SIGNATURE": config.get("signature", ""),
            "X-AI-GATEWAY-APP-ID": config.get("app-id", ""),
            "X-AI-GATEWAY-SIGNED-HEADERS": "x-ai-gateway-app-id;x-ai-gateway-timestamp;x-ai-gateway-nonce",
            "uucToken": config.get("uuc-token", ""),
            "uuid": config.get("uuid", ""),
            "platform": "web",
            "provider": "auto",
            "Accept": "*/*",
            "Origin": "https://chatgpt.vivo.com.cn",
            "X-Requested-With": "mark.via",
            "Sec-Fetch-Site": "same-origin",
            "Sec-Fetch-Mode": "cors",
            "Sec-Fetch-Dest": "empty",
            "Referer": "https://chatgpt.vivo.com.cn/",
            "Accept-Encoding": "gzip, deflate",
            "Accept-Language": "zh-CN,zh;q=0.9,en-US;q=0.8,en;q=0.7"
        }



        # 初始化TextCardService
        self.text_card = TextCardService()

        # 会话管理 - 使用固定的sessionId
        self._session_id = "9765b271-b132-4007-ab6e-676383924bdf"
        self._sessions = {}

        # 临时存储
        self._temp_dir = "temp/deepseek"
        os.makedirs(self._temp_dir, exist_ok=True)

        # 异步加载配置任务
        asyncio.create_task(self._async_load_config())

        # 初始化自然化回复词库
        self._init_natural_responses()

    def _load_admin_config(self):
        """加载管理员配置"""
        try:
            # 读取主配置文件
            with open("main_config.toml", "rb") as f:
                main_config = tomllib.load(f)

            # 获取管理员列表
            self.admins = main_config.get("XYBot", {}).get("admins", [])

        except Exception as e:
            logger.error(f"[Deepseek] 加载管理员配置失败: {str(e)}")
            self.admins = []

    def _is_admin(self, wxid: str) -> bool:
        """检查用户是否是管理员"""
        return wxid in self.admins

    def _init_natural_responses(self):
        """初始化自然化回复词库"""
        self.confirm_msgs = [
            "好的", "收到", "明白了", "嗯", "知道了", "行", "OK"
        ]

        self.error_msgs = [
            "这可把我难住了", "搞不定了", "这个有点难", "弄不了",
            "出问题了", "搞不出来", "这个不行"
        ]

        self.processing_msgs = [
            "马上就好", "稍等一下", "正在想", "等等哈",
            "马上", "在想了", "思考中"
        ]

        self.config_error_msgs = [
            "配置有问题", "设置不对", "需要重新配置", "配置错了"
        ]

        self.network_error_msgs = [
            "网络有问题", "连不上", "网络不行", "连接失败了"
        ]

        self.update_success_msgs = [
            "更新好了", "配置更新了", "设置完成", "更新成功"
        ]

        self.permission_msgs = [
            "这个我管不了", "没权限", "不是管理员", "权限不够",
            "管理员才能操作", "你不是管理员"
        ]

    async def _update_config(self, uuid_value: str, uuc_token: str) -> bool:
        """更新配置文件中的API凭证"""
        try:
            # 读取当前配置
            async with aiofiles.open("plugins/Deepseek/config.toml", "r", encoding="utf-8") as f:
                content = await f.read()

            # 更新uuid和uuc-token
            import re
            content = re.sub(r'uuid = ".*?"', f'uuid = "{uuid_value}"', content)
            content = re.sub(r'uuc-token = ".*?"', f'uuc-token = "{uuc_token}"', content)

            # 写回配置文件
            async with aiofiles.open("plugins/Deepseek/config.toml", "w", encoding="utf-8") as f:
                await f.write(content)

            # 更新内存中的配置
            self.headers["uuid"] = uuid_value
            self.headers["uucToken"] = uuc_token


            return True
        except Exception as e:
            logger.error(f"[Deepseek] 更新配置失败: {str(e)}")
            return False

    async def _async_load_config(self):
        """异步加载配置"""
        try:
            async with aiofiles.open("plugins/Deepseek/config.toml", "r", encoding="utf-8") as f:
                content = await f.read()
                plugin_config = tomllib.loads(content)
            config = plugin_config.get("Deepseek", {})

            # 更新配置
            self.enable = config.get("enable", True)
            self.command = config.get("command", ["deepseek", "ds"])
            self.api_url = config.get("api-url", "https://chatgpt.vivo.com.cn/chatgpt-api/chat/public/completions")

            # 检查必要的配置项
            uuid_value = config.get("uuid", "")
            uuc_token = config.get("uuc-token", "")

            if not uuid_value or not uuc_token:
                logger.warning("[Deepseek] 配置缺少必要的API凭证: uuid或uucToken为空")

            # 更新headers
            self.headers = {
                "Connection": "keep-alive",
                "X-AI-GATEWAY-TIMESTAMP": config.get("timestamp", ""),
                "X-uid": config.get("uid", ""),
                "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/116.0.0.0 Safari/537.36",
                "X-AI-GATEWAY-NONCE": config.get("nonce", ""),
                "Content-Type": "application/json",
                "X-AI-GATEWAY-SIGNATURE": config.get("signature", ""),
                "X-AI-GATEWAY-APP-ID": config.get("app-id", ""),
                "X-AI-GATEWAY-SIGNED-HEADERS": "x-ai-gateway-app-id;x-ai-gateway-timestamp;x-ai-gateway-nonce",
                "uucToken": uuc_token,
                "uuid": uuid_value,
                "platform": "web",
                "provider": "auto",
                "Accept": "*/*",
                "Origin": "https://chatgpt.vivo.com.cn",
                "X-Requested-With": "mark.via",
                "Sec-Fetch-Site": "same-origin",
                "Sec-Fetch-Mode": "cors",
                "Sec-Fetch-Dest": "empty",
                "Referer": "https://chatgpt.vivo.com.cn/",
                "Accept-Encoding": "gzip, deflate",
                "Accept-Language": "zh-CN,zh;q=0.9,en-US;q=0.8,en;q=0.7"
            }


        except Exception as e:
            logger.error(f"[Deepseek] 异步加载配置失败: {str(e)}")
            logger.error(traceback.format_exc())

    def _generate_headers(self) -> dict:
        """生成请求头"""
        timestamp = str(int(time.time()))
        nonce = base64.b64encode(os.urandom(6)).decode('utf-8')[:8]

        headers = self.headers.copy()
        headers.update({
            "X-AI-GATEWAY-TIMESTAMP": timestamp,
            "X-AI-GATEWAY-NONCE": nonce,
        })

        # 生成签名
        signed_content = f"x-ai-gateway-app-id={headers['X-AI-GATEWAY-APP-ID']}&x-ai-gateway-timestamp={timestamp}&x-ai-gateway-nonce={nonce}"
        signature = base64.b64encode(
            hmac.new(
                "your_secret_key".encode('utf-8'),
                signed_content.encode('utf-8'),
                hashlib.sha256
            ).digest()
        ).decode('utf-8')

        headers["X-AI-GATEWAY-SIGNATURE"] = signature

        return headers

    async def _process_stream(self, response: httpx.Response) -> str:
        """处理SSE流式响应"""
        buffer = []
        current_event = None

        try:
            async for line in response.aiter_lines():
                if line:
                    # 根据类型修正解码逻辑
                    if isinstance(line, bytes):
                        line = line.decode('utf-8').strip()
                    else:
                        line = line.strip()



                    if line.startswith('event:'):
                        current_event = line[6:].strip()
                        # 检测认证失败事件
                        if current_event == 'authentication_fail':
                            logger.error("[Deepseek] API认证失败，需要重新登录")
                            raise ValueError("API认证失败，请联系管理员更新API凭证")
                        continue

                    if line.startswith('data:'):
                        data = line[5:].strip()

                        if data == "[DONE]":
                            break

                        if current_event == 'error':
                            logger.error(f"[Deepseek] 服务器返回错误: {data}")
                            raise ValueError(f"API错误: {data}")

                        try:
                            json_data = json.loads(data)
                            if json_data.get("type") in ["thinking", "text"]:
                                message = json_data.get("message", "")
                                if message and message not in buffer:
                                    buffer.append(message)
                        except json.JSONDecodeError as e:
                            logger.warning(f"[Deepseek] JSON解析失败: {e}, 原始数据: {data}")
                            continue

        except asyncio.TimeoutError:
            logger.error("[Deepseek] 响应流读取超时")
            if buffer:
                return buffer[-1]
            raise TimeoutError("响应流读取超时，请重试")

        except Exception as e:
            logger.error(f"[Deepseek] 处理响应流时出错: {str(e)}")
            logger.error(traceback.format_exc())
            if buffer:
                return buffer[-1]
            raise

        if not buffer:
            logger.warning("[Deepseek] 没有从响应中获取到有效内容")
            return "很抱歉，我无法处理这个请求。请稍后再试。"

        result = buffer[-1]
        return result

    async def _get_header_image_path(self) -> Optional[str]:
        """获取头部图片路径"""
        # 使用本地图片路径
        local_image_path = r"C:\XYBotV2\data\default picture\deepseek.jpg"

        if os.path.exists(local_image_path):

            return local_image_path
        else:
            logger.warning(f"[Deepseek] 本地头部图片不存在: {local_image_path}")
            return None

    async def _generate_card(self, text: str, header_image_path: Optional[str] = None) -> Optional[str]:
        """生成图片卡片"""
        try:
            temp_path = f"{self._temp_dir}/card_{int(time.time())}.png"


            if header_image_path:
                # 使用本地头部图片
                kwargs = {
                    'title_image': header_image_path,
                    'width': 720,
                    'is_dark': False
                }
                await self.text_card.generate_card(text=text, output_path=temp_path, **kwargs)
            else:
                # 没有图片时创建简单卡片
                await self.text_card.generate_card(
                    text=text,
                    output_path=temp_path,
                    width=720,
                    is_dark=False
                )


            return temp_path
        except TextCardError as e:
            logger.error(f"[Deepseek] 生成图片失败: {e}")
            return None

    def _get_thinking_message(self) -> str:
        """生成随机的思考消息"""
        if self.natural_response:
            return random.choice(self.processing_msgs)
        else:
            thinking_messages = [
                "让我思考一下这个问题... 🤔",
                "这是个有趣的问题,我需要好好想想 💭",
                "正在深入思考中... ⚡",
                "让我整理一下相关的信息... 📚",
                "这个问题很有意思,让我仔细分析一下 🔍",
                "需要认真思考一下,稍等... ⏳",
                "正在进行深度分析... 🧠",
                "让我从多个角度来思考这个问题... 🎯"
            ]
            return random.choice(thinking_messages)

    async def _handle_update_command(self, bot: WechatAPIClient, message: dict, content: str):
        """处理更新命令"""
        user_wxid = message["SenderWxid"]
        group_wxid = message["FromWxid"]

        # 检查管理员权限
        if not self._is_admin(user_wxid):
            if self.natural_response:
                permission_msg = random.choice(self.permission_msgs)
                await bot.send_text_message(group_wxid, permission_msg)
            else:
                await bot.send_text_message(group_wxid, "❌ 只有管理员才能更新配置")
            return

        # 解析更新命令
        # 格式: 更新deepseek uuid=xxx uuc-token=xxx
        try:
            # 移除命令前缀
            params_str = re.sub(f"^({'|'.join(self.update_command)})", "", content).strip()

            if not params_str:
                if self.natural_response:
                    error_msg = random.choice(self.error_msgs)
                    await bot.send_text_message(group_wxid, error_msg)
                else:
                    await bot.send_text_message(
                        group_wxid,
                        "请提供更新参数，格式：更新deepseek uuid=xxx uuc-token=xxx"
                    )
                return

            # 解析参数
            uuid_match = re.search(r'uuid=([^\s]+)', params_str)
            token_match = re.search(r'uuc-token=([^\s]+)', params_str)

            if not uuid_match or not token_match:
                if self.natural_response:
                    error_msg = random.choice(self.error_msgs)
                    await bot.send_text_message(group_wxid, error_msg)
                else:
                    await bot.send_text_message(
                        group_wxid,
                        "参数格式错误，请使用：更新deepseek uuid=xxx uuc-token=xxx"
                    )
                return

            uuid_value = uuid_match.group(1)
            uuc_token = token_match.group(1)

            # 更新配置
            success = await self._update_config(uuid_value, uuc_token)

            if success:
                if self.natural_response:
                    success_msg = random.choice(self.update_success_msgs)
                    await bot.send_text_message(group_wxid, success_msg)
                else:
                    await bot.send_text_message(group_wxid, "✅ Deepseek配置更新成功")
            else:
                if self.natural_response:
                    error_msg = random.choice(self.error_msgs)
                    await bot.send_text_message(group_wxid, error_msg)
                else:
                    await bot.send_text_message(group_wxid, "❌ 配置更新失败，请检查日志")

        except Exception as e:
            logger.error(f"[Deepseek] 处理更新命令失败: {str(e)}")
            if self.natural_response:
                error_msg = random.choice(self.error_msgs)
                await bot.send_text_message(group_wxid, error_msg)
            else:
                await bot.send_text_message(group_wxid, "❌ 更新配置时发生错误")

    @on_text_message
    async def handle_text(self, bot: WechatAPIClient, message: dict):
        """处理文本消息"""
        if not self.enable:
            return

        content = message["Content"].strip()

        # 检查是否是更新命令
        if content.startswith(tuple(self.update_command)):
            await self._handle_update_command(bot, message, content)
            return

        if not content.startswith(tuple(self.command)):
            return

        question = re.sub(f"^({'|'.join(self.command)})", "", content).strip()
        if not question:
            if self.natural_response:
                error_msg = random.choice(self.error_msgs)
                await bot.send_text_message(message["FromWxid"], error_msg)
            else:
                await bot.send_text_message(
                    message["FromWxid"],
                    "请输入要询问的内容"
                )
            return



        # 首先检查API凭证是否完整
        if not self.headers.get("uuid") or not self.headers.get("uucToken"):
            if self.natural_response:
                config_error_msg = random.choice(self.config_error_msgs)
                await bot.send_text_message(message["FromWxid"], config_error_msg)
            else:
                await bot.send_text_message(
                    message["FromWxid"],
                    "⚠️ Deepseek服务尚未配置完成，请联系管理员设置有效的API凭证"
                )
            logger.error("[Deepseek] API凭证不完整，uuid或uucToken为空")
            return

        thinking_message = self._get_thinking_message()
        await bot.send_text_message(
            message["FromWxid"],
            thinking_message
        )

        # 恢复使用异步任务来获取图片路径
        header_image_task = asyncio.create_task(self._get_header_image_path())

        try:
            if self._session_id not in self._sessions:
                self._sessions[self._session_id] = {
                    "messages": []
                }

            request_data = {
                "sessionId": self._session_id,
                "requestId": str(uuid.uuid4()),
                "appName": "chat-demo-public",
                "plugins": [],
                "model": "deepseek-r1",
                "task_type": "chatgpt",
                "prompt": question,
                "extra": {
                    "search_mode": "required"
                }
            }



            timeout = httpx.Timeout(timeout=300.0)
            # 初始化text变量
            text = ""

            async with httpx.AsyncClient(timeout=timeout) as client:
                try:
                    response = await client.post(
                        self.api_url,
                        headers=self.headers,
                        json=request_data
                    )



                    if response.status_code != 200:
                        error_text = response.text
                        logger.error(f"[Deepseek] API请求失败: {response.status_code}, {error_text}")
                        raise ValueError(f"API请求失败，状态码: {response.status_code}, 错误信息: {error_text}")

                    # 记录响应头信息以便调试


                    text = await self._process_stream(response)
                except httpx.RequestError as e:
                    logger.error(f"[Deepseek] 请求错误: {e}")
                    raise ValueError(f"API请求错误: {str(e)}")

                except TimeoutError as e:
                    if self.natural_response:
                        error_msg = random.choice(self.error_msgs)
                        await bot.send_text_message(message["FromWxid"], error_msg)
                    else:
                        await bot.send_text_message(
                            message["FromWxid"],
                            "⚠️ 响应超时，但我会继续尝试处理已收到的内容"
                        )
                    if not text:
                        raise

                # 等待头部图片路径获取完成
                header_image_path = await header_image_task

                # 检查是否有有效的返回内容
                if not text:
                    raise ValueError("未获取到有效响应内容")

                image_path = await self._generate_card(text, header_image_path)

                if image_path:
                    # 直接发送图片，不需要额外的完成提示
                    with open(image_path, 'rb') as f:
                        image_data = f.read()
                    # 发送图片数据
                    await bot.send_image_message(
                        message["FromWxid"],
                        image_data
                    )
                    # 延迟60秒后清理合成图片 - 使用统一管理器
                    cleanup_file(image_path, delay_seconds=60)
                else:
                    # 直接发送文本，不需要额外的完成提示
                    await bot.send_text_message(
                        message["FromWxid"],
                        text
                    )

        except httpx.HTTPError as e:
            logger.error(f"[Deepseek] 网络请求错误: {e}")
            if self.natural_response:
                network_error_msg = random.choice(self.network_error_msgs)
                await bot.send_text_message(message["FromWxid"], network_error_msg)
            else:
                await bot.send_text_message(
                    message["FromWxid"],
                    "❌网络请求失败，请稍后重试"
                )
        except Exception as e:
            logger.error(f"[Deepseek] 处理失败: {str(e)}")
            logger.error(f"[Deepseek] 错误详情: {traceback.format_exc()}")

            if self.natural_response:
                # 根据错误类型选择不同的自然化回复
                if "API请求失败" in str(e) or "header uuid和uucToken不能为空" in str(e):
                    error_msg = random.choice(self.config_error_msgs)
                elif "API认证失败" in str(e):
                    error_msg = random.choice(self.config_error_msgs)
                elif "网络" in str(e) or "连接" in str(e):
                    error_msg = random.choice(self.network_error_msgs)
                else:
                    error_msg = random.choice(self.error_msgs)
                await bot.send_text_message(message["FromWxid"], error_msg)
            else:
                # 为用户提供更友好的错误信息
                friendly_error = "服务用不了，等会再试试"
                if "API请求失败" in str(e):
                    if "header uuid和uucToken不能为空" in str(e):
                        friendly_error = "API配置错误，请联系管理员检查凭证设置"
                    else:
                        friendly_error = "Deepseek连不上，等会再试试"
                elif "API认证失败" in str(e):
                    friendly_error = "Deepseek服务认证失败，请联系管理员更新API凭证"
                elif "未获取到有效响应" in str(e) or "未获取到有效响应内容" in str(e):
                    friendly_error = "Deepseek没回复，可能在忙"

                await bot.send_text_message(
                    message["FromWxid"],
                    f"❌ {friendly_error}"
                )
        finally:
            # 确保处理异步任务
            try:
                if not header_image_task.done():
                    header_image_task.cancel()
                # 异步任务已经完成，不需要清理本地图片
            except:
                pass

    async def on_disable(self):
        """插件禁用时清理资源"""
        await super().on_disable()

        # 临时文件清理现在由统一的TempFileManager处理