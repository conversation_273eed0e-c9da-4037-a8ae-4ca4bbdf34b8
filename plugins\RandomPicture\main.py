import tomllib
import traceback

import httpx
from loguru import logger

from WechatAPI import WechatAPIClient
from utils.decorators import *
from utils.plugin_base import PluginBase


class RandomPicture(PluginBase):
    description = "随机图片"
    author = "HenryXiaoYang"
    version = "1.0.0"

    def __init__(self):
        super().__init__()

        with open("plugins/RandomPicture/config.toml", "rb") as f:
            plugin_config = tomllib.load(f)

        config = plugin_config["RandomPicture"]

        self.enable = config["enable"]
        self.command = config["command"]

    @on_text_message
    async def handle_text(self, bot: WechatAPIClient, message: dict):
        if not self.enable:
            return

        content = str(message["Content"]).strip()
        command = content.split(" ")

        if not len(command) or command[0] not in self.command:
            return

        api_url = "https://api.52vmy.cn/api/img/tu/man?type=text"

        try:
            async with httpx.AsyncClient(verify=False) as client:
                response = await client.get(api_url)
                pic_url = response.text

                img_response = await client.get(pic_url)
                content = img_response.content

            await bot.send_image_message(message["FromWxid"], image=content)

        except Exception as error:
            out_message = f"-----XYBot-----\n出现错误❌！\n{error}"
            logger.error(traceback.format_exc())

            await bot.send_text_message(message["FromWxid"], out_message)
