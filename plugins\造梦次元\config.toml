[造梦次元]
enable = true
command = ["造梦", "次元", "造梦次元"]
command-format = "🎭 造梦次元 - AI角色对话\n发送「造梦次元」开始选择角色\n🎵 优先语音回复，不支持时发送文字"
natural_response = false  # 角色选择阶段不需要自然化响应
session_timeout = 1800  # 会话超时时间（秒），默认30分钟

# Ideaflow API配置
token = "eyJhbGciOiJIUzUxMiIsInR5cCI6IkpXVCJ9.eyJzdWIiOiIwMDAwMDQ1Mzg5Mjk2NTc1MDQzNjY1OTIiLCJuaWNrbmFtZSI6IuaipuWunThVSFNYVVZWNyIsImV4cCI6MTc1MDczODk1MCwidXNlcklkIjoiMDAwMDA0NTM4OTI5NjU3NTA0MzY2NTkyIiwianRpIjoiMDAwMDA0NTM4OTI5NjU3NTA0MzY2NTkyIiwiYXV0aG9yaXRpZXMiOiJbXSJ9.W3bayv9LDTWKyy3TkWxGpLds_zx0Qj9MtKf3gpCtmByHTdSV1WrsrjOu-oV19q0SJFCjWiVAWcwsoBfVmbAOAg"
uid = "000004538929657504366592"

# 语音回复配置
voice_priority = true  # 优先使用语音回复
voice_fallback_text = false  # 默认只发送语音，不回退到文字

[造梦次元.rate_limit]
cooldown = 10  # 冷却时间（秒），AI对话类插件建议较长冷却时间
