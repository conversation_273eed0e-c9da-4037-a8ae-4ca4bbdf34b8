2025-07-28 00:01:18 | DEBUG | 收到消息: {'MsgId': 1893033784, 'FromUserName': {'string': '48097389945@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 49, 'Content': {'string': 'xiaomaochong:\n<?xml version="1.0"?>\n<msg>\n\t<appmsg appid="wx485a97c844086dc9" sdkver="0">\n\t\t<title>不变的音乐</title>\n\t\t<des>小爱</des>\n\t\t<username />\n\t\t<action>view</action>\n\t\t<type>3</type>\n\t\t<showtype>0</showtype>\n\t\t<content />\n\t\t<url />\n\t\t<lowurl />\n\t\t<forwardflag>0</forwardflag>\n\t\t<dataurl>https://er-sycdn.kuwo.cn/5ced60a2024605a4e6336ea0723f3437/68864d57/resource/30106/trackmedia/O8M1000xOJse4df9kmc9e35e5ead026635912d13c8254fb8a4.mgg</dataurl>\n\t\t<lowdataurl />\n\t\t<contentattr>0</contentattr>\n\t\t<streamvideo>\n\t\t\t<streamvideourl />\n\t\t\t<streamvideototaltime>0</streamvideototaltime>\n\t\t\t<streamvideotitle />\n\t\t\t<streamvideowording />\n\t\t\t<streamvideoweburl />\n\t\t\t<streamvideothumburl />\n\t\t\t<streamvideoaduxinfo />\n\t\t\t<streamvideopublishid />\n\t\t</streamvideo>\n\t\t<canvasPageItem>\n\t\t\t<canvasPageXml><![CDATA[]]></canvasPageXml>\n\t\t</canvasPageItem>\n\t\t<appattach>\n\t\t\t<totallen>0</totallen>\n\t\t\t<attachid />\n\t\t\t<cdnattachurl />\n\t\t\t<emoticonmd5></emoticonmd5>\n\t\t\t<aeskey></aeskey>\n\t\t\t<fileext />\n\t\t\t<islargefilemsg>0</islargefilemsg>\n\t\t</appattach>\n\t\t<extinfo />\n\t\t<androidsource>2</androidsource>\n\t\t<thumburl />\n\t\t<mediatagname />\n\t\t<messageaction><![CDATA[]]></messageaction>\n\t\t<messageext><![CDATA[]]></messageext>\n\t\t<emoticongift>\n\t\t\t<packageflag>0</packageflag>\n\t\t\t<packageid />\n\t\t</emoticongift>\n\t\t<emoticonshared>\n\t\t\t<packageflag>0</packageflag>\n\t\t\t<packageid />\n\t\t</emoticonshared>\n\t\t<designershared>\n\t\t\t<designeruin>0</designeruin>\n\t\t\t<designername>null</designername>\n\t\t\t<designerrediretcturl><![CDATA[null]]></designerrediretcturl>\n\t\t</designershared>\n\t\t<emotionpageshared>\n\t\t\t<tid>0</tid>\n\t\t\t<title>null</title>\n\t\t\t<desc>null</desc>\n\t\t\t<iconUrl><![CDATA[null]]></iconUrl>\n\t\t\t<secondUrl>null</secondUrl>\n\t\t\t<pageType>0</pageType>\n\t\t\t<setKey>null</setKey>\n\t\t</emotionpageshared>\n\t\t<webviewshared>\n\t\t\t<shareUrlOriginal />\n\t\t\t<shareUrlOpen />\n\t\t\t<jsAppId />\n\t\t\t<publisherId />\n\t\t\t<publisherReqId />\n\t\t</webviewshared>\n\t\t<template_id />\n\t\t<md5 />\n\t\t<websearch>\n\t\t\t<rec_category>0</rec_category>\n\t\t\t<channelId>0</channelId>\n\t\t</websearch>\n\t\t<weappinfo>\n\t\t\t<username />\n\t\t\t<appid />\n\t\t\t<appservicetype>0</appservicetype>\n\t\t\t<secflagforsinglepagemode>0</secflagforsinglepagemode>\n\t\t\t<videopageinfo>\n\t\t\t\t<thumbwidth>0</thumbwidth>\n\t\t\t\t<thumbheight>0</thumbheight>\n\t\t\t\t<fromopensdk>0</fromopensdk>\n\t\t\t</videopageinfo>\n\t\t</weappinfo>\n\t\t<statextstr>GhQKEnd4NDg1YTk3Yzg0NDA4NmRjOQ==</statextstr>\n\t\t<songalbumurl>https://wx.qlogo.cn/mmhead/ver_1/6iaSQLKjLK6YMpTGwdaFEJHr0OKpQPK5HR2zUmibE6f8W4tsSjkT8RbjHCBOnoIdKv28tarjHhJic6qRhiczIdOzbEUTMQebeT1XouSEMct9ofiaha74cyibDl8vxtUztxWIicB/0</songalbumurl>\n\t\t<songlyric>[00:00.00]不变的音乐 - 王绎龙 (Sunny Wang)\n[00:19.94]词：djsunny\n[00:39.89]曲：djsunny\n[00:59.83]风中的节奏\n[01:01.64]就像你的温柔\n[01:03.52]紧握住我的手\n[01:05.43]带我去环绕地球\n[01:07.26]整个世界在变\n[01:09.13]我的音乐不会变\n[01:11.02]忘我的境界\n[01:12.91]只有自己最了解\n[01:15.24]迷人的天\n[01:17.12]燃烧的夜\n[01:18.77]去感觉电音无限的季节\n[01:22.78]不知不觉 让我听见\n[01:26.27]那不变的音乐\n[01:29.79]让我们唱起不变的音乐\n[01:33.52]Can you feel my music crazy for tonight\n[01:37.28]让我们唱起不变的音乐\n[01:41.02]Dj sunny make you high!\n[01:44.89]让我们唱起不变的音乐\n[01:48.54]Can you feel my music crazy for tonight\n[01:52.31]让我们唱起不变的音乐\n[01:56.06]Dj sunny make you high!\n[02:15.38]Come on baby!\n[02:16.45]我的音乐我的生命我的世界\n[02:19.17]在这个电音无限的季节\n[02:20.92]有没有想过开始改变\n[02:22.74]把你的烦恼抛在脑后\n[02:24.59]去感受我的电音节奏\n[02:26.43]把你的双手开始舞动 here we go!yo~\n[02:30.08]风中的节奏\n[02:31.53]就像你的温柔\n[02:33.47]紧握住我的手\n[02:35.33]带我去环绕地球\n[02:37.22]整个世界在变\n[02:39.10]我的音乐不会变\n[02:41.01]忘我的境界\n[02:42.91]只有自己最了解\n[02:45.17]迷人的天\n[02:47.10]燃烧的夜\n[02:48.70]去感觉电音无限的季节\n[02:52.71]不知不觉 让我听见\n[02:56.30]那不变的音乐\n[02:59.72]让我们唱起不变的音乐\n[03:03.61]Can you feel my music crazy for tonight\n[03:07.27]让我们唱起不变的音乐\n[03:11.07]Dj sunny make you high!\n[03:14.69]让我们唱起不变的音乐\n[03:18.54]Can you feel my music crazy for tonight\n[03:22.30]让我们唱起不变的音乐\n[03:26.03]Dj sunny make you high!\n</songlyric>\n\t\t<musicShareItem>\n\t\t\t<musicDuration>0</musicDuration>\n\t\t</musicShareItem>\n\t\t<finderLiveProductShare>\n\t\t\t<finderLiveID><![CDATA[]]></finderLiveID>\n\t\t\t<finderUsername><![CDATA[]]></finderUsername>\n\t\t\t<finderObjectID><![CDATA[]]></finderObjectID>\n\t\t\t<finderNonceID><![CDATA[]]></finderNonceID>\n\t\t\t<liveStatus><![CDATA[]]></liveStatus>\n\t\t\t<appId><![CDATA[]]></appId>\n\t\t\t<pagePath><![CDATA[]]></pagePath>\n\t\t\t<productId><![CDATA[]]></productId>\n\t\t\t<coverUrl><![CDATA[]]></coverUrl>\n\t\t\t<productTitle><![CDATA[]]></productTitle>\n\t\t\t<marketPrice><![CDATA[0]]></marketPrice>\n\t\t\t<sellingPrice><![CDATA[0]]></sellingPrice>\n\t\t\t<platformHeadImg><![CDATA[]]></platformHeadImg>\n\t\t\t<platformName><![CDATA[]]></platformName>\n\t\t\t<shopWindowId><![CDATA[]]></shopWindowId>\n\t\t\t<flashSalePrice><![CDATA[0]]></flashSalePrice>\n\t\t\t<flashSaleEndTime><![CDATA[0]]></flashSaleEndTime>\n\t\t\t<ecSource><![CDATA[]]></ecSource>\n\t\t\t<sellingPriceWording><![CDATA[]]></sellingPriceWording>\n\t\t\t<platformIconURL><![CDATA[]]></platformIconURL>\n\t\t\t<firstProductTagURL><![CDATA[]]></firstProductTagURL>\n\t\t\t<firstProductTagAspectRatioString><![CDATA[0.0]]></firstProductTagAspectRatioString>\n\t\t\t<secondProductTagURL><![CDATA[]]></secondProductTagURL>\n\t\t\t<secondProductTagAspectRatioString><![CDATA[0.0]]></secondProductTagAspectRatioString>\n\t\t\t<firstGuaranteeWording><![CDATA[]]></firstGuaranteeWording>\n\t\t\t<secondGuaranteeWording><![CDATA[]]></secondGuaranteeWording>\n\t\t\t<thirdGuaranteeWording><![CDATA[]]></thirdGuaranteeWording>\n\t\t\t<isPriceBeginShow>false</isPriceBeginShow>\n\t\t\t<lastGMsgID><![CDATA[]]></lastGMsgID>\n\t\t\t<promoterKey><![CDATA[]]></promoterKey>\n\t\t\t<discountWording><![CDATA[]]></discountWording>\n\t\t\t<priceSuffixDescription><![CDATA[]]></priceSuffixDescription>\n\t\t\t<showBoxItemStringList />\n\t\t</finderLiveProductShare>\n\t\t<finderOrder>\n\t\t\t<appID><![CDATA[]]></appID>\n\t\t\t<orderID><![CDATA[]]></orderID>\n\t\t\t<path><![CDATA[]]></path>\n\t\t\t<priceWording><![CDATA[]]></priceWording>\n\t\t\t<stateWording><![CDATA[]]></stateWording>\n\t\t\t<productImageURL><![CDATA[]]></productImageURL>\n\t\t\t<products><![CDATA[]]></products>\n\t\t\t<productsCount><![CDATA[0]]></productsCount>\n\t\t</finderOrder>\n\t\t<finderShopWindowShare>\n\t\t\t<finderUsername><![CDATA[]]></finderUsername>\n\t\t\t<avatar><![CDATA[]]></avatar>\n\t\t\t<nickname><![CDATA[]]></nickname>\n\t\t\t<commodityInStockCount><![CDATA[]]></commodityInStockCount>\n\t\t\t<appId><![CDATA[]]></appId>\n\t\t\t<path><![CDATA[]]></path>\n\t\t\t<appUsername><![CDATA[]]></appUsername>\n\t\t\t<query><![CDATA[]]></query>\n\t\t\t<liteAppId><![CDATA[]]></liteAppId>\n\t\t\t<liteAppPath><![CDATA[]]></liteAppPath>\n\t\t\t<liteAppQuery><![CDATA[]]></liteAppQuery>\n\t\t\t<platformTagURL><![CDATA[]]></platformTagURL>\n\t\t\t<saleWording><![CDATA[]]></saleWording>\n\t\t\t<lastGMsgID><![CDATA[]]></lastGMsgID>\n\t\t\t<profileTypeWording><![CDATA[]]></profileTypeWording>\n\t\t\t<reputationInfo>\n\t\t\t\t<hasReputationInfo>0</hasReputationInfo>\n\t\t\t\t<reputationScore>0</reputationScore>\n\t\t\t\t<reputationWording />\n\t\t\t\t<reputationTextColor />\n\t\t\t\t<reputationLevelWording />\n\t\t\t\t<reputationBackgroundColor />\n\t\t\t</reputationInfo>\n\t\t\t<productImageURLList />\n\t\t</finderShopWindowShare>\n\t\t<findernamecard>\n\t\t\t<username />\n\t\t\t<avatar><![CDATA[]]></avatar>\n\t\t\t<nickname />\n\t\t\t<auth_job />\n\t\t\t<auth_icon>0</auth_icon>\n\t\t\t<auth_icon_url />\n\t\t\t<ecSource><![CDATA[]]></ecSource>\n\t\t\t<lastGMsgID><![CDATA[]]></lastGMsgID>\n\t\t</findernamecard>\n\t\t<finderGuarantee>\n\t\t\t<scene><![CDATA[0]]></scene>\n\t\t</finderGuarantee>\n\t\t<directshare>0</directshare>\n\t\t<gamecenter>\n\t\t\t<namecard>\n\t\t\t\t<iconUrl />\n\t\t\t\t<name />\n\t\t\t\t<desc />\n\t\t\t\t<tail />\n\t\t\t\t<jumpUrl />\n\t\t\t</namecard>\n\t\t</gamecenter>\n\t\t<patMsg>\n\t\t\t<chatUser />\n\t\t\t<records>\n\t\t\t\t<recordNum>0</recordNum>\n\t\t\t</records>\n\t\t</patMsg>\n\t\t<secretmsg>\n\t\t\t<issecretmsg>0</issecretmsg>\n\t\t</secretmsg>\n\t\t<referfromscene>0</referfromscene>\n\t\t<gameshare>\n\t\t\t<liteappext>\n\t\t\t\t<liteappbizdata />\n\t\t\t\t<priority>0</priority>\n\t\t\t</liteappext>\n\t\t\t<appbrandext>\n\t\t\t\t<litegameinfo />\n\t\t\t\t<priority>-1</priority>\n\t\t\t</appbrandext>\n\t\t\t<gameshareid />\n\t\t\t<sharedata />\n\t\t\t<isvideo>0</isvideo>\n\t\t\t<duration>-1</duration>\n\t\t\t<isexposed>0</isexposed>\n\t\t\t<readtext />\n\t\t</gameshare>\n\t\t<mpsharetrace>\n\t\t\t<hasfinderelement>0</hasfinderelement>\n\t\t\t<lastgmsgid />\n\t\t</mpsharetrace>\n\t\t<wxgamecard>\n\t\t\t<framesetname />\n\t\t\t<mbcarddata />\n\t\t\t<minpkgversion />\n\t\t\t<mbcardheight>0</mbcardheight>\n\t\t\t<isoldversion>0</isoldversion>\n\t\t</wxgamecard>\n\t\t<liteapp>\n\t\t\t<id>null</id>\n\t\t\t<path />\n\t\t\t<query />\n\t\t</liteapp>\n\t</appmsg>\n\t<fromusername>xiaomaochong</fromusername>\n\t<scene>0</scene>\n\t<appinfo>\n\t\t<version>29</version>\n\t\t<appname>摇一摇搜歌</appname>\n\t</appinfo>\n\t<commenturl />\n</msg>\n'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1753632087, 'MsgSource': '<msgsource>\n\t<sec_msg_node>\n\t\t<uuid>6693c4848f2dcd7d014df04469f505e9_</uuid>\n\t\t<risk-file-flag />\n\t\t<risk-file-md5-list />\n\t</sec_msg_node>\n\t<silence>0</silence>\n\t<membercount>73</membercount>\n\t<signature>N0_V1_3h4vw8G/|v1_5efjWMCB</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'PushContent': '你收到了一条消息', 'NewMsgId': 6399671198852546123, 'MsgSeq': 871404347}
2025-07-28 00:01:18 | DEBUG | 从群聊消息中提取发送者: xiaomaochong
2025-07-28 00:01:19 | DEBUG | XML消息完整内容:
<?xml version="1.0"?>
<msg>
	<appmsg appid="wx485a97c844086dc9" sdkver="0">
		<title>不变的音乐</title>
		<des>小爱</des>
		<username />
		<action>view</action>
		<type>3</type>
		<showtype>0</showtype>
		<content />
		<url />
		<lowurl />
		<forwardflag>0</forwardflag>
		<dataurl>https://er-sycdn.kuwo.cn/5ced60a2024605a4e6336ea0723f3437/68864d57/resource/30106/trackmedia/O8M1000xOJse4df9kmc9e35e5ead026635912d13c8254fb8a4.mgg</dataurl>
		<lowdataurl />
		<contentattr>0</contentattr>
		<streamvideo>
			<streamvideourl />
			<streamvideototaltime>0</streamvideototaltime>
			<streamvideotitle />
			<streamvideowording />
			<streamvideoweburl />
			<streamvideothumburl />
			<streamvideoaduxinfo />
			<streamvideopublishid />
		</streamvideo>
		<canvasPageItem>
			<canvasPageXml><![CDATA[]]></canvasPageXml>
		</canvasPageItem>
		<appattach>
			<totallen>0</totallen>
			<attachid />
			<cdnattachurl />
			<emoticonmd5></emoticonmd5>
			<aeskey></aeskey>
			<fileext />
			<islargefilemsg>0</islargefilemsg>
		</appattach>
		<extinfo />
		<androidsource>2</androidsource>
		<thumburl />
		<mediatagname />
		<messageaction><![CDATA[]]></messageaction>
		<messageext><![CDATA[]]></messageext>
		<emoticongift>
			<packageflag>0</packageflag>
			<packageid />
		</emoticongift>
		<emoticonshared>
			<packageflag>0</packageflag>
			<packageid />
		</emoticonshared>
		<designershared>
			<designeruin>0</designeruin>
			<designername>null</designername>
			<designerrediretcturl><![CDATA[null]]></designerrediretcturl>
		</designershared>
		<emotionpageshared>
			<tid>0</tid>
			<title>null</title>
			<desc>null</desc>
			<iconUrl><![CDATA[null]]></iconUrl>
			<secondUrl>null</secondUrl>
			<pageType>0</pageType>
			<setKey>null</setKey>
		</emotionpageshared>
		<webviewshared>
			<shareUrlOriginal />
			<shareUrlOpen />
			<jsAppId />
			<publisherId />
			<publisherReqId />
		</webviewshared>
		<template_id />
		<md5 />
		<websearch>
			<rec_category>0</rec_category>
			<channelId>0</channelId>
		</websearch>
		<weappinfo>
			<username />
			<appid />
			<appservicetype>0</appservicetype>
			<secflagforsinglepagemode>0</secflagforsinglepagemode>
			<videopageinfo>
				<thumbwidth>0</thumbwidth>
				<thumbheight>0</thumbheight>
				<fromopensdk>0</fromopensdk>
			</videopageinfo>
		</weappinfo>
		<statextstr>GhQKEnd4NDg1YTk3Yzg0NDA4NmRjOQ==</statextstr>
		<songalbumurl>https://wx.qlogo.cn/mmhead/ver_1/6iaSQLKjLK6YMpTGwdaFEJHr0OKpQPK5HR2zUmibE6f8W4tsSjkT8RbjHCBOnoIdKv28tarjHhJic6qRhiczIdOzbEUTMQebeT1XouSEMct9ofiaha74cyibDl8vxtUztxWIicB/0</songalbumurl>
		<songlyric>[00:00.00]不变的音乐 - 王绎龙 (Sunny Wang)
[00:19.94]词：djsunny
[00:39.89]曲：djsunny
[00:59.83]风中的节奏
[01:01.64]就像你的温柔
[01:03.52]紧握住我的手
[01:05.43]带我去环绕地球
[01:07.26]整个世界在变
[01:09.13]我的音乐不会变
[01:11.02]忘我的境界
[01:12.91]只有自己最了解
[01:15.24]迷人的天
[01:17.12]燃烧的夜
[01:18.77]去感觉电音无限的季节
[01:22.78]不知不觉 让我听见
[01:26.27]那不变的音乐
[01:29.79]让我们唱起不变的音乐
[01:33.52]Can you feel my music crazy for tonight
[01:37.28]让我们唱起不变的音乐
[01:41.02]Dj sunny make you high!
[01:44.89]让我们唱起不变的音乐
[01:48.54]Can you feel my music crazy for tonight
[01:52.31]让我们唱起不变的音乐
[01:56.06]Dj sunny make you high!
[02:15.38]Come on baby!
[02:16.45]我的音乐我的生命我的世界
[02:19.17]在这个电音无限的季节
[02:20.92]有没有想过开始改变
[02:22.74]把你的烦恼抛在脑后
[02:24.59]去感受我的电音节奏
[02:26.43]把你的双手开始舞动 here we go!yo~
[02:30.08]风中的节奏
[02:31.53]就像你的温柔
[02:33.47]紧握住我的手
[02:35.33]带我去环绕地球
[02:37.22]整个世界在变
[02:39.10]我的音乐不会变
[02:41.01]忘我的境界
[02:42.91]只有自己最了解
[02:45.17]迷人的天
[02:47.10]燃烧的夜
[02:48.70]去感觉电音无限的季节
[02:52.71]不知不觉 让我听见
[02:56.30]那不变的音乐
[02:59.72]让我们唱起不变的音乐
[03:03.61]Can you feel my music crazy for tonight
[03:07.27]让我们唱起不变的音乐
[03:11.07]Dj sunny make you high!
[03:14.69]让我们唱起不变的音乐
[03:18.54]Can you feel my music crazy for tonight
[03:22.30]让我们唱起不变的音乐
[03:26.03]Dj sunny make you high!
</songlyric>
		<musicShareItem>
			<musicDuration>0</musicDuration>
		</musicShareItem>
		<finderLiveProductShare>
			<finderLiveID><![CDATA[]]></finderLiveID>
			<finderUsername><![CDATA[]]></finderUsername>
			<finderObjectID><![CDATA[]]></finderObjectID>
			<finderNonceID><![CDATA[]]></finderNonceID>
			<liveStatus><![CDATA[]]></liveStatus>
			<appId><![CDATA[]]></appId>
			<pagePath><![CDATA[]]></pagePath>
			<productId><![CDATA[]]></productId>
			<coverUrl><![CDATA[]]></coverUrl>
			<productTitle><![CDATA[]]></productTitle>
			<marketPrice><![CDATA[0]]></marketPrice>
			<sellingPrice><![CDATA[0]]></sellingPrice>
			<platformHeadImg><![CDATA[]]></platformHeadImg>
			<platformName><![CDATA[]]></platformName>
			<shopWindowId><![CDATA[]]></shopWindowId>
			<flashSalePrice><![CDATA[0]]></flashSalePrice>
			<flashSaleEndTime><![CDATA[0]]></flashSaleEndTime>
			<ecSource><![CDATA[]]></ecSource>
			<sellingPriceWording><![CDATA[]]></sellingPriceWording>
			<platformIconURL><![CDATA[]]></platformIconURL>
			<firstProductTagURL><![CDATA[]]></firstProductTagURL>
			<firstProductTagAspectRatioString><![CDATA[0.0]]></firstProductTagAspectRatioString>
			<secondProductTagURL><![CDATA[]]></secondProductTagURL>
			<secondProductTagAspectRatioString><![CDATA[0.0]]></secondProductTagAspectRatioString>
			<firstGuaranteeWording><![CDATA[]]></firstGuaranteeWording>
			<secondGuaranteeWording><![CDATA[]]></secondGuaranteeWording>
			<thirdGuaranteeWording><![CDATA[]]></thirdGuaranteeWording>
			<isPriceBeginShow>false</isPriceBeginShow>
			<lastGMsgID><![CDATA[]]></lastGMsgID>
			<promoterKey><![CDATA[]]></promoterKey>
			<discountWording><![CDATA[]]></discountWording>
			<priceSuffixDescription><![CDATA[]]></priceSuffixDescription>
			<showBoxItemStringList />
		</finderLiveProductShare>
		<finderOrder>
			<appID><![CDATA[]]></appID>
			<orderID><![CDATA[]]></orderID>
			<path><![CDATA[]]></path>
			<priceWording><![CDATA[]]></priceWording>
			<stateWording><![CDATA[]]></stateWording>
			<productImageURL><![CDATA[]]></productImageURL>
			<products><![CDATA[]]></products>
			<productsCount><![CDATA[0]]></productsCount>
		</finderOrder>
		<finderShopWindowShare>
			<finderUsername><![CDATA[]]></finderUsername>
			<avatar><![CDATA[]]></avatar>
			<nickname><![CDATA[]]></nickname>
			<commodityInStockCount><![CDATA[]]></commodityInStockCount>
			<appId><![CDATA[]]></appId>
			<path><![CDATA[]]></path>
			<appUsername><![CDATA[]]></appUsername>
			<query><![CDATA[]]></query>
			<liteAppId><![CDATA[]]></liteAppId>
			<liteAppPath><![CDATA[]]></liteAppPath>
			<liteAppQuery><![CDATA[]]></liteAppQuery>
			<platformTagURL><![CDATA[]]></platformTagURL>
			<saleWording><![CDATA[]]></saleWording>
			<lastGMsgID><![CDATA[]]></lastGMsgID>
			<profileTypeWording><![CDATA[]]></profileTypeWording>
			<reputationInfo>
				<hasReputationInfo>0</hasReputationInfo>
				<reputationScore>0</reputationScore>
				<reputationWording />
				<reputationTextColor />
				<reputationLevelWording />
				<reputationBackgroundColor />
			</reputationInfo>
			<productImageURLList />
		</finderShopWindowShare>
		<findernamecard>
			<username />
			<avatar><![CDATA[]]></avatar>
			<nickname />
			<auth_job />
			<auth_icon>0</auth_icon>
			<auth_icon_url />
			<ecSource><![CDATA[]]></ecSource>
			<lastGMsgID><![CDATA[]]></lastGMsgID>
		</findernamecard>
		<finderGuarantee>
			<scene><![CDATA[0]]></scene>
		</finderGuarantee>
		<directshare>0</directshare>
		<gamecenter>
			<namecard>
				<iconUrl />
				<name />
				<desc />
				<tail />
				<jumpUrl />
			</namecard>
		</gamecenter>
		<patMsg>
			<chatUser />
			<records>
				<recordNum>0</recordNum>
			</records>
		</patMsg>
		<secretmsg>
			<issecretmsg>0</issecretmsg>
		</secretmsg>
		<referfromscene>0</referfromscene>
		<gameshare>
			<liteappext>
				<liteappbizdata />
				<priority>0</priority>
			</liteappext>
			<appbrandext>
				<litegameinfo />
				<priority>-1</priority>
			</appbrandext>
			<gameshareid />
			<sharedata />
			<isvideo>0</isvideo>
			<duration>-1</duration>
			<isexposed>0</isexposed>
			<readtext />
		</gameshare>
		<mpsharetrace>
			<hasfinderelement>0</hasfinderelement>
			<lastgmsgid />
		</mpsharetrace>
		<wxgamecard>
			<framesetname />
			<mbcarddata />
			<minpkgversion />
			<mbcardheight>0</mbcardheight>
			<isoldversion>0</isoldversion>
		</wxgamecard>
		<liteapp>
			<id>null</id>
			<path />
			<query />
		</liteapp>
	</appmsg>
	<fromusername>xiaomaochong</fromusername>
	<scene>0</scene>
	<appinfo>
		<version>29</version>
		<appname>摇一摇搜歌</appname>
	</appinfo>
	<commenturl />
</msg>

2025-07-28 00:01:19 | DEBUG | XML消息类型: 3
2025-07-28 00:01:19 | DEBUG | XML消息标题: 不变的音乐
2025-07-28 00:01:19 | DEBUG | XML消息描述: 小爱
2025-07-28 00:01:19 | DEBUG | 附件信息 totallen: 0
2025-07-28 00:01:19 | DEBUG | 附件信息 islargefilemsg: 0
2025-07-28 00:01:19 | INFO | 收到红包消息: 标题:不变的音乐 描述:小爱 来自:48097389945@chatroom 发送人:wxid_4usgcju5ey9q29
2025-07-28 00:01:23 | DEBUG | 收到消息: {'MsgId': 2022046956, 'FromUserName': {'string': '48097389945@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 34, 'Content': {'string': 'xiaomaochong:\n<msg><voicemsg endflag="1" cancelflag="0" forwardflag="0" voiceformat="4" voicelength="1000" length="181998" bufid="0" aeskey="6171766e70707465726c6868706d766d" voiceurl="3052020100044b304902010002049363814102033d14ba02049b26949d020468864d5c042463323638323839632d616663352d346434352d396635312d65316265383738313230366102040528000f02010004001dc74187" voicemd5="1f352af0bbce131fb2a8186524725e91" clientmsgid="41386366366231333863396431366400280001072825d67e8f970a9100" fromusername="xiaomaochong" /></msg>'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1753632092, 'MsgSource': '<msgsource>\n\t<silence>0</silence>\n\t<membercount>73</membercount>\n\t<signature>N0_V1_OHMdbAlN|v1_/6l+afau</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'PushContent': '小爱在群聊中发了一段语音', 'NewMsgId': 1255869983273240103, 'MsgSeq': 871404348}
2025-07-28 00:01:23 | INFO | 收到语音消息: 消息ID:2022046956 来自:48097389945@chatroom 发送人:xiaomaochong XML:
<msg><voicemsg endflag="1" cancelflag="0" forwardflag="0" voiceformat="4" voicelength="1000" length="181998" bufid="0" aeskey="6171766e70707465726c6868706d766d" voiceurl="3052020100044b304902010002049363814102033d14ba02049b26949d020468864d5c042463323638323839632d616663352d346434352d396635312d65316265383738313230366102040528000f02010004001dc74187" voicemd5="1f352af0bbce131fb2a8186524725e91" clientmsgid="41386366366231333863396431366400280001072825d67e8f970a9100" fromusername="xiaomaochong" /></msg>
2025-07-28 00:01:34 | DEBUG | 收到消息: {'MsgId': 1721425238, 'FromUserName': {'string': '51595225532@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 1, 'Content': {'string': 'wxid_qk4ejsujsz9a21:\n我在打终极'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1753632103, 'MsgSource': '<msgsource>\n\t<pua>1</pua>\n\t<eggIncluded>1</eggIncluded>\n\t<silence>0</silence>\n\t<membercount>10</membercount>\n\t<signature>N0_V1_a7CaQoiD|v1_GbSqdp4i</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'PushContent': '\xa0  Ooo゛🍭 : 我在打终极', 'NewMsgId': 6684795550021371039, 'MsgSeq': 871404349}
2025-07-28 00:01:34 | INFO | 收到文本消息: 消息ID:1721425238 来自:51595225532@chatroom 发送人:wxid_qk4ejsujsz9a21 @:[] 内容:我在打终极
2025-07-28 00:01:34 | DEBUG | 处理消息内容: '我在打终极'
2025-07-28 00:01:34 | DEBUG | 消息内容 '我在打终极' 不匹配任何命令，忽略
2025-07-28 00:01:43 | DEBUG | 收到消息: {'MsgId': 938361712, 'FromUserName': {'string': '48097389945@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 10002, 'Content': {'string': 'xiaomaochong:\n<sysmsg type="revokemsg"><revokemsg><session>48097389945@chatroom</session><msgid>75328338</msgid><newmsgid>6399671198852546123</newmsgid><replacemsg><![CDATA["小爱" 撤回了一条消息]]></replacemsg><announcement_id><![CDATA[]]></announcement_id></revokemsg></sysmsg>'}, 'Status': 4, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1753632109, 'MsgSource': '<msgsource>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'NewMsgId': 7035385917296631267, 'MsgSeq': 871404350}
2025-07-28 00:01:43 | DEBUG | 系统消息类型: revokemsg
2025-07-28 00:01:43 | INFO | 未知的系统消息类型: {'MsgId': 938361712, 'ToWxid': 'wxid_4usgcju5ey9q29', 'MsgType': 10002, 'Content': '\n<sysmsg type="revokemsg"><revokemsg><session>48097389945@chatroom</session><msgid>75328338</msgid><newmsgid>6399671198852546123</newmsgid><replacemsg><![CDATA["小爱" 撤回了一条消息]]></replacemsg><announcement_id><![CDATA[]]></announcement_id></revokemsg></sysmsg>', 'Status': 4, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1753632109, 'MsgSource': '<msgsource>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'NewMsgId': 7035385917296631267, 'MsgSeq': 871404350, 'FromWxid': '48097389945@chatroom', 'IsGroup': True, 'SenderWxid': 'xiaomaochong'}
2025-07-28 00:02:31 | DEBUG | 收到消息: {'MsgId': 2081497679, 'FromUserName': {'string': '48097389945@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 3, 'Content': {'string': 'xiaomaochong:\n<?xml version="1.0"?>\n<msg>\n\t<img aeskey="12386103f5128846b951b66c8f2a854c" encryver="1" cdnthumbaeskey="12386103f5128846b951b66c8f2a854c" cdnthumburl="3057020100044b304902010002049363814102032f51490204f4312275020468864d9f042436343337626338312d636666362d343862652d626162312d316265313133363765376337020405292a010201000405004c57c100" cdnthumblength="3007" cdnthumbheight="58" cdnthumbwidth="144" cdnmidheight="0" cdnmidwidth="0" cdnhdheight="0" cdnhdwidth="0" cdnmidimgurl="3057020100044b304902010002049363814102032f51490204f4312275020468864d9f042436343337626338312d636666362d343862652d626162312d316265313133363765376337020405292a010201000405004c57c100" length="2689" cdnbigimgurl="3057020100044b304902010002049363814102032f51490204f4312275020468864d9f042436343337626338312d636666362d343862652d626162312d316265313133363765376337020405292a010201000405004c57c100" hdlength="5646" md5="1869eb6ba027c8cdafb7967052437eb7" hevc_mid_size="2689" originsourcemd5="1869eb6ba027c8cdafb7967052437eb7">\n\t\t<secHashInfoBase64 />\n\t\t<live>\n\t\t\t<duration>0</duration>\n\t\t\t<size>0</size>\n\t\t\t<md5 />\n\t\t\t<fileid />\n\t\t\t<hdsize>0</hdsize>\n\t\t\t<hdmd5 />\n\t\t\t<hdfileid />\n\t\t\t<stillimagetimems>0</stillimagetimems>\n\t\t</live>\n\t</img>\n\t<appinfo>\n\t\t<appid>wxf0a80d0ac2e82aa7</appid>\n\t\t<version>65</version>\n\t\t<appname>QQ分享</appname>\n\t\t<isforceupdate>0</isforceupdate>\n\t\t<messageaction />\n\t\t<messageext />\n\t\t<mediatagname />\n\t</appinfo>\n\t<platform_signature />\n\t<imgdatahash />\n\t<ImgSourceInfo>\n\t\t<ImgSourceUrl />\n\t\t<BizType>0</BizType>\n\t</ImgSourceInfo>\n</msg>\n'}, 'Status': 3, 'ImgStatus': 2, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1753632160, 'MsgSource': '<msgsource>\n\t<alnode>\n\t\t<fr>4</fr>\n\t</alnode>\n\t<sec_msg_node>\n\t\t<uuid>8e0af9e50fdd79aa4bce4a99a421910f_</uuid>\n\t\t<risk-file-flag />\n\t\t<risk-file-md5-list />\n\t</sec_msg_node>\n\t<silence>0</silence>\n\t<membercount>73</membercount>\n\t<signature>N0_V1_16GarBgW|v1_bbwdN1gt</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'PushContent': '小爱在群聊中发了一张图片', 'NewMsgId': 1663972515742627299, 'MsgSeq': 871404351}
2025-07-28 00:02:31 | INFO | 收到图片消息: 消息ID:2081497679 来自:48097389945@chatroom 发送人:xiaomaochong XML:<?xml version="1.0"?><msg><img aeskey="12386103f5128846b951b66c8f2a854c" encryver="1" cdnthumbaeskey="12386103f5128846b951b66c8f2a854c" cdnthumburl="3057020100044b304902010002049363814102032f51490204f4312275020468864d9f042436343337626338312d636666362d343862652d626162312d316265313133363765376337020405292a010201000405004c57c100" cdnthumblength="3007" cdnthumbheight="58" cdnthumbwidth="144" cdnmidheight="0" cdnmidwidth="0" cdnhdheight="0" cdnhdwidth="0" cdnmidimgurl="3057020100044b304902010002049363814102032f51490204f4312275020468864d9f042436343337626338312d636666362d343862652d626162312d316265313133363765376337020405292a010201000405004c57c100" length="2689" cdnbigimgurl="3057020100044b304902010002049363814102032f51490204f4312275020468864d9f042436343337626338312d636666362d343862652d626162312d316265313133363765376337020405292a010201000405004c57c100" hdlength="5646" md5="1869eb6ba027c8cdafb7967052437eb7" hevc_mid_size="2689" originsourcemd5="1869eb6ba027c8cdafb7967052437eb7"><secHashInfoBase64 /><live><duration>0</duration><size>0</size><md5 /><fileid /><hdsize>0</hdsize><hdmd5 /><hdfileid /><stillimagetimems>0</stillimagetimems></live></img><appinfo><appid>wxf0a80d0ac2e82aa7</appid><version>65</version><appname>QQ分享</appname><isforceupdate>0</isforceupdate><messageaction /><messageext /><mediatagname /></appinfo><platform_signature /><imgdatahash /><ImgSourceInfo><ImgSourceUrl /><BizType>0</BizType></ImgSourceInfo></msg>
2025-07-28 00:02:32 | INFO | [ImageEcho] 保存图片信息成功，当前群 48097389945@chatroom 已存储 5 张图片
2025-07-28 00:02:32 | INFO | [TimerTask] 缓存图片消息: 2081497679
2025-07-28 00:04:04 | DEBUG | 收到消息: {'MsgId': 1183814086, 'FromUserName': {'string': '48097389945@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 43, 'Content': {'string': 'xiaomaochong:\n<?xml version="1.0"?>\n<msg>\n\t<videomsg aeskey="d28cd2a00022d48a9662ab2bdcbd31db" cdnvideourl="3057020100044b304902010002049363814102032f51490204f4312275020468864dfc042432313638616236392d336336342d346532652d383565612d6566623839346432313733390204052808040201000405004c4d3500" cdnthumbaeskey="d28cd2a00022d48a9662ab2bdcbd31db" cdnthumburl="3057020100044b304902010002049363814102032f51490204f4312275020468864dfc042432313638616236392d336336342d346532652d383565612d6566623839346432313733390204052808040201000405004c4d3500" length="1337811" playlength="23" cdnthumblength="20762" cdnthumbwidth="304" cdnthumbheight="540" fromusername="xiaomaochong" md5="517a467cbfce4527926da9e6eea93ccf" newmd5="" isplaceholder="0" rawmd5="" rawlength="0" cdnrawvideourl="" cdnrawvideoaeskey="" overwritenewmsgid="0" originsourcemd5="" isad="0" />\n</msg>\n'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1753632252, 'MsgSource': '<msgsource>\n\t<alnode>\n\t\t<fr>5</fr>\n\t</alnode>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n\t<videopreloadlen>292175</videopreloadlen>\n\t<sec_msg_node>\n\t\t<uuid>5b1b941053e2c70517a322be99c64e41_</uuid>\n\t\t<risk-file-flag />\n\t\t<risk-file-md5-list />\n\t</sec_msg_node>\n\t<silence>0</silence>\n\t<membercount>73</membercount>\n\t<signature>N0_V1_IS40mrTz|v1_LWwqb7HK</signature>\n</msgsource>\n', 'PushContent': '小爱在群聊中发了一段视频', 'NewMsgId': 4087151532751988698, 'MsgSeq': 871404352}
2025-07-28 00:04:04 | INFO | 收到视频消息: 消息ID:1183814086 来自:48097389945@chatroom 发送人:xiaomaochong XML:
<?xml version="1.0"?>
<msg>
	<videomsg aeskey="d28cd2a00022d48a9662ab2bdcbd31db" cdnvideourl="3057020100044b304902010002049363814102032f51490204f4312275020468864dfc042432313638616236392d336336342d346532652d383565612d6566623839346432313733390204052808040201000405004c4d3500" cdnthumbaeskey="d28cd2a00022d48a9662ab2bdcbd31db" cdnthumburl="3057020100044b304902010002049363814102032f51490204f4312275020468864dfc042432313638616236392d336336342d346532652d383565612d6566623839346432313733390204052808040201000405004c4d3500" length="1337811" playlength="23" cdnthumblength="20762" cdnthumbwidth="304" cdnthumbheight="540" fromusername="xiaomaochong" md5="517a467cbfce4527926da9e6eea93ccf" newmd5="" isplaceholder="0" rawmd5="" rawlength="0" cdnrawvideourl="" cdnrawvideoaeskey="" overwritenewmsgid="0" originsourcemd5="" isad="0" />
</msg>

2025-07-28 00:05:50 | INFO | [TempFileManager] 开始清理临时文件...
2025-07-28 00:05:51 | DEBUG | 开始检查群成员变化，当前监控 2 个群
2025-07-28 00:05:52 | DEBUG | 群成员变化检查完成
2025-07-28 00:35:51 | INFO | [TempFileManager] 开始清理临时文件...
2025-07-28 00:49:22 | DEBUG | 收到消息: {'MsgId': 1082202778, 'FromUserName': {'string': '51595225532@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 1, 'Content': {'string': 'wxid_m1lh16d8hfl221:\n不眠之夜'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1753634972, 'MsgSource': '<msgsource>\n\t<bizflag>0</bizflag>\n\t<pua>1</pua>\n\t<eggIncluded>1</eggIncluded>\n\t<silence>0</silence>\n\t<membercount>10</membercount>\n\t<signature>N0_V1_VtYPey2N|v1_YVCN8jhY</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'PushContent': 'Aurora : 不眠之夜', 'NewMsgId': 232479554090545266, 'MsgSeq': 871404353}
2025-07-28 00:49:22 | INFO | 收到文本消息: 消息ID:1082202778 来自:51595225532@chatroom 发送人:wxid_m1lh16d8hfl221 @:[] 内容:不眠之夜
2025-07-28 00:49:22 | DEBUG | 处理消息内容: '不眠之夜'
2025-07-28 00:49:22 | DEBUG | 消息内容 '不眠之夜' 不匹配任何命令，忽略
2025-07-28 00:49:29 | DEBUG | 收到消息: {'MsgId': 67096546, 'FromUserName': {'string': '51595225532@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 1, 'Content': {'string': 'wxid_m1lh16d8hfl221:\n出来嗨皮一下'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1753634978, 'MsgSource': '<msgsource>\n\t<bizflag>0</bizflag>\n\t<pua>1</pua>\n\t<eggIncluded>1</eggIncluded>\n\t<silence>0</silence>\n\t<membercount>10</membercount>\n\t<signature>N0_V1_tPKrvE44|v1_ZN06veWd</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'PushContent': 'Aurora : 出来嗨皮一下', 'NewMsgId': 1770896560776455667, 'MsgSeq': 871404354}
2025-07-28 00:49:29 | INFO | 收到文本消息: 消息ID:67096546 来自:51595225532@chatroom 发送人:wxid_m1lh16d8hfl221 @:[] 内容:出来嗨皮一下
2025-07-28 00:49:29 | DEBUG | 处理消息内容: '出来嗨皮一下'
2025-07-28 00:49:29 | DEBUG | 消息内容 '出来嗨皮一下' 不匹配任何命令，忽略
2025-07-28 00:52:32 | DEBUG | 收到消息: {'MsgId': 2051368281, 'FromUserName': {'string': '48097389945@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 1, 'Content': {'string': 'wxid_wlnzvr8ivgd422:\n该睡觉了'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1753635161, 'MsgSource': '<msgsource>\n\t<pua>1</pua>\n\t<eggIncluded>1</eggIncluded>\n\t<silence>0</silence>\n\t<membercount>73</membercount>\n\t<signature>N0_V1_h4IiDMpa|v1_iBrKm0cl</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'PushContent': '锦岚 : 该睡觉了', 'NewMsgId': 4970006920578101968, 'MsgSeq': 871404355}
2025-07-28 00:52:32 | INFO | 收到文本消息: 消息ID:2051368281 来自:48097389945@chatroom 发送人:wxid_wlnzvr8ivgd422 @:[] 内容:该睡觉了
2025-07-28 00:52:33 | DEBUG | 处理消息内容: '该睡觉了'
2025-07-28 00:52:33 | DEBUG | 消息内容 '该睡觉了' 不匹配任何命令，忽略
2025-07-28 00:52:35 | DEBUG | 收到消息: {'MsgId': 1392126999, 'FromUserName': {'string': '48097389945@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 1, 'Content': {'string': 'wxid_wlnzvr8ivgd422:\n早安'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1753635163, 'MsgSource': '<msgsource>\n\t<pua>1</pua>\n\t<eggIncluded>1</eggIncluded>\n\t<silence>0</silence>\n\t<membercount>73</membercount>\n\t<signature>N0_V1_ZrOOlmJ1|v1_rmZ/gfLo</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'PushContent': '锦岚 : 早安', 'NewMsgId': 6686455353311900480, 'MsgSeq': 871404356}
2025-07-28 00:52:35 | INFO | 收到文本消息: 消息ID:1392126999 来自:48097389945@chatroom 发送人:wxid_wlnzvr8ivgd422 @:[] 内容:早安
2025-07-28 00:52:35 | DEBUG | 处理消息内容: '早安'
2025-07-28 00:52:36 | DEBUG | 消息内容 '早安' 不匹配任何命令，忽略
2025-07-28 00:52:37 | DEBUG | 收到消息: {'MsgId': 2021668596, 'FromUserName': {'string': '48097389945@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 1, 'Content': {'string': 'wxid_lneb7n23o4lg12:\n现在偷的每个懒，都是给未来挖的坑，以后的日子要付出数倍的努力来填补这个坑。现在吃的每个苦，都是给未来添砖加瓦，以后的日子会加倍享受苦所带来的乐。新的一天，早安??'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1753635165, 'MsgSource': '<msgsource>\n\t<pua>1</pua>\n\t<silence>0</silence>\n\t<membercount>73</membercount>\n\t<signature>N0_V1_mgnk2Dfn|v1_DnQO2VUz</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'PushContent': 'ᡣ : 现在偷的每个懒，都是给未来挖的坑，以后的日子要付出数倍的努力...', 'NewMsgId': 6202836102470252194, 'MsgSeq': 871404357}
2025-07-28 00:52:37 | INFO | 收到文本消息: 消息ID:2021668596 来自:48097389945@chatroom 发送人:wxid_lneb7n23o4lg12 @:[] 内容:现在偷的每个懒，都是给未来挖的坑，以后的日子要付出数倍的努力来填补这个坑。现在吃的每个苦，都是给未来添砖加瓦，以后的日子会加倍享受苦所带来的乐。新的一天，早安??
2025-07-28 00:52:38 | DEBUG | 处理消息内容: '现在偷的每个懒，都是给未来挖的坑，以后的日子要付出数倍的努力来填补这个坑。现在吃的每个苦，都是给未来添砖加瓦，以后的日子会加倍享受苦所带来的乐。新的一天，早安??'
2025-07-28 00:52:38 | DEBUG | 消息内容 '现在偷的每个懒，都是给未来挖的坑，以后的日子要付出数倍的努力来填补这个坑。现在吃的每个苦，都是给未来添砖加瓦，以后的日子会加倍享受苦所带来的乐。新的一天，早安??' 不匹配任何命令，忽略
2025-07-28 00:52:40 | DEBUG | 收到消息: {'MsgId': 909189399, 'FromUserName': {'string': '48097389945@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 1, 'Content': {'string': 'wxid_wlnzvr8ivgd422:\n晚安'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1753635169, 'MsgSource': '<msgsource>\n\t<pua>1</pua>\n\t<eggIncluded>1</eggIncluded>\n\t<silence>0</silence>\n\t<membercount>73</membercount>\n\t<signature>N0_V1_8LOLwa+V|v1_ZDdBgwJ5</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'PushContent': '锦岚 : 晚安', 'NewMsgId': 3331168860026618564, 'MsgSeq': 871404358}
2025-07-28 00:52:40 | INFO | 收到文本消息: 消息ID:909189399 来自:48097389945@chatroom 发送人:wxid_wlnzvr8ivgd422 @:[] 内容:晚安
2025-07-28 00:52:40 | DEBUG | 处理消息内容: '晚安'
2025-07-28 00:52:40 | DEBUG | 消息内容 '晚安' 不匹配任何命令，忽略
2025-07-28 00:52:42 | DEBUG | 收到消息: {'MsgId': 1158028786, 'FromUserName': {'string': '48097389945@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 3, 'Content': {'string': 'wxid_lneb7n23o4lg12:\n<?xml version="1.0"?>\n<msg>\n\t<img aeskey="2a655fe292d12e9489b816b55e9f64a5" encryver="1" cdnthumbaeskey="2a655fe292d12e9489b816b55e9f64a5" cdnthumburl="3057020100044b304902010002046b3e4bb802032f51490204d9312275020468865960042437656262393530302d373564612d346431652d613430642d623864356333306563613537020405250a020201000405004c4d9900" cdnthumblength="3886" cdnthumbheight="120" cdnthumbwidth="68" cdnmidheight="0" cdnmidwidth="0" cdnhdheight="0" cdnhdwidth="0" cdnmidimgurl="3057020100044b304902010002046b3e4bb802032f51490204d9312275020468865960042437656262393530302d373564612d346431652d613430642d623864356333306563613537020405250a020201000405004c4d9900" length="97167" md5="6e40a723a4e7392d68bb4ae24926ba58" originsourcemd5="0572ac279df480e238585b39da209652">\n\t\t<secHashInfoBase64 />\n\t\t<live>\n\t\t\t<duration>0</duration>\n\t\t\t<size>0</size>\n\t\t\t<md5 />\n\t\t\t<fileid />\n\t\t\t<hdsize>0</hdsize>\n\t\t\t<hdmd5 />\n\t\t\t<hdfileid />\n\t\t\t<stillimagetimems>0</stillimagetimems>\n\t\t</live>\n\t</img>\n\t<platform_signature />\n\t<imgdatahash />\n\t<ImgSourceInfo>\n\t\t<ImgSourceUrl />\n\t\t<BizType>0</BizType>\n\t</ImgSourceInfo>\n</msg>\n'}, 'Status': 3, 'ImgStatus': 2, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1753635169, 'MsgSource': '<msgsource>\n\t<alnode>\n\t\t<fr>2</fr>\n\t</alnode>\n\t<sec_msg_node>\n\t\t<uuid>641fa9d05ff336cda960e6da7fee7406_</uuid>\n\t\t<risk-file-flag />\n\t\t<risk-file-md5-list />\n\t</sec_msg_node>\n\t<silence>0</silence>\n\t<membercount>73</membercount>\n\t<signature>N0_V1_fCSWjILi|v1_sJndkHsk</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'PushContent': 'ᡣ在群聊中发了一张图片', 'NewMsgId': 1381496507823388003, 'MsgSeq': 871404359}
2025-07-28 00:52:42 | INFO | 收到图片消息: 消息ID:1158028786 来自:48097389945@chatroom 发送人:wxid_lneb7n23o4lg12 XML:<?xml version="1.0"?><msg><img aeskey="2a655fe292d12e9489b816b55e9f64a5" encryver="1" cdnthumbaeskey="2a655fe292d12e9489b816b55e9f64a5" cdnthumburl="3057020100044b304902010002046b3e4bb802032f51490204d9312275020468865960042437656262393530302d373564612d346431652d613430642d623864356333306563613537020405250a020201000405004c4d9900" cdnthumblength="3886" cdnthumbheight="120" cdnthumbwidth="68" cdnmidheight="0" cdnmidwidth="0" cdnhdheight="0" cdnhdwidth="0" cdnmidimgurl="3057020100044b304902010002046b3e4bb802032f51490204d9312275020468865960042437656262393530302d373564612d346431652d613430642d623864356333306563613537020405250a020201000405004c4d9900" length="97167" md5="6e40a723a4e7392d68bb4ae24926ba58" originsourcemd5="0572ac279df480e238585b39da209652"><secHashInfoBase64 /><live><duration>0</duration><size>0</size><md5 /><fileid /><hdsize>0</hdsize><hdmd5 /><hdfileid /><stillimagetimems>0</stillimagetimems></live></img><platform_signature /><imgdatahash /><ImgSourceInfo><ImgSourceUrl /><BizType>0</BizType></ImgSourceInfo></msg>
2025-07-28 00:52:43 | INFO | [ImageEcho] 保存图片信息成功，当前群 48097389945@chatroom 已存储 5 张图片
2025-07-28 00:52:43 | INFO | [TimerTask] 缓存图片消息: 1158028786
2025-07-28 00:52:45 | DEBUG | 收到消息: {'MsgId': 1372581986, 'FromUserName': {'string': '48097389945@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 1, 'Content': {'string': 'wxid_wlnzvr8ivgd422:\n好无聊啊'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1753635174, 'MsgSource': '<msgsource>\n\t<pua>1</pua>\n\t<eggIncluded>1</eggIncluded>\n\t<silence>0</silence>\n\t<membercount>73</membercount>\n\t<signature>N0_V1_ltdKD0p7|v1_bCt28FuD</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'PushContent': '锦岚 : 好无聊啊', 'NewMsgId': 8890681624720861130, 'MsgSeq': 871404360}
2025-07-28 00:52:45 | INFO | 收到文本消息: 消息ID:1372581986 来自:48097389945@chatroom 发送人:wxid_wlnzvr8ivgd422 @:[] 内容:好无聊啊
2025-07-28 00:52:45 | DEBUG | 处理消息内容: '好无聊啊'
2025-07-28 00:52:45 | DEBUG | 消息内容 '好无聊啊' 不匹配任何命令，忽略
2025-07-28 00:52:52 | DEBUG | 收到消息: {'MsgId': 1913208736, 'FromUserName': {'string': '48097389945@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 47, 'Content': {'string': 'wxid_wlnzvr8ivgd422:\n<msg><emoji fromusername = "wxid_wlnzvr8ivgd422" tousername = "48097389945@chatroom" type="2" idbuffer="media:0_0" md5="e9274f992a3708c1ee6751bd1f54cb8d" len = "341750" productid="" androidmd5="e9274f992a3708c1ee6751bd1f54cb8d" androidlen="341750" s60v3md5 = "e9274f992a3708c1ee6751bd1f54cb8d" s60v3len="341750" s60v5md5 = "e9274f992a3708c1ee6751bd1f54cb8d" s60v5len="341750" cdnurl = "http://vweixinf.tc.qq.com/110/20401/stodownload?m=e9274f992a3708c1ee6751bd1f54cb8d&amp;filekey=30440201010430302e02016e040253480420653932373466393932613337303863316565363735316264316635346362386402030536f6040d00000004627466730000000132&amp;hy=SH&amp;storeid=268490e700007ab7540015af20000006e01004fb153480a33203156935a24c&amp;ef=1&amp;bizid=1022" designerid = "" thumburl = "" encrypturl = "http://vweixinf.tc.qq.com/110/20402/stodownload?m=919b8492244ac249fd9779579a9ab681&amp;filekey=30440201010430302e02016e04025348042039313962383439323234346163323439666439373739353739613961623638310203053700040d00000004627466730000000132&amp;hy=SH&amp;storeid=268490e700008d35040015af20000006e02004fb253480a33203156935a257&amp;ef=2&amp;bizid=1022" aeskey= "86a38143347b4e8bb2d2fa540cf3d2a3" externurl = "http://vweixinf.tc.qq.com/110/20403/stodownload?m=03577113a548d58b306938b5629402c1&amp;filekey=30440201010430302e02016e040253480420303335373731313361353438643538623330363933386235363239343032633102030093d0040d00000004627466730000000132&amp;hy=SH&amp;storeid=268490e70000a922740015af20000006e03004fb353480a33203156935a279&amp;ef=3&amp;bizid=1022" externmd5 = "736709373b268fbbf96b39726d17c0fe" width= "400" height= "400" tpurl= "" tpauthkey= "" attachedtext= "" attachedtextcolor= "" lensid= "" emojiattr= "" linkid= "" desc= "" ></emoji>  </msg>'}, 'Status': 3, 'ImgStatus': 2, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1753635182, 'MsgSource': '<msgsource>\n\t<silence>0</silence>\n\t<membercount>73</membercount>\n\t<signature>N0_V1_x7i0HTPZ|v1_brWwrvvX</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'PushContent': '锦岚在群聊中发了一个表情', 'NewMsgId': 2388713956710929444, 'MsgSeq': 871404361}
2025-07-28 00:52:52 | INFO | 收到表情消息: 消息ID:1913208736 来自:48097389945@chatroom 发送人:wxid_wlnzvr8ivgd422 MD5:e9274f992a3708c1ee6751bd1f54cb8d 大小:341750
2025-07-28 00:52:53 | DEBUG | [DoubaoImageRecognition] 缓存表情消息: 2388713956710929444
2025-07-28 00:53:33 | DEBUG | 收到消息: {'MsgId': 805832917, 'FromUserName': {'string': '51595225532@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 10002, 'Content': {'string': '51595225532@chatroom:\n<sysmsg type="pat">\n<pat>\n  <fromusername>wxid_wogoxis9jdfq22</fromusername>\n  <chatusername>51595225532@chatroom</chatusername>\n  <pattedusername>wxid_m1lh16d8hfl221</pattedusername>\n  <patsuffix><![CDATA[我们是好朋友]]></patsuffix>\n  <patsuffixversion>2</patsuffixversion>\n\n\n\n\n\n\n  <template><![CDATA["${wxid_wogoxis9jdfq22}" 拍了拍 "${wxid_m1lh16d8hfl221}" 我们是好朋友]]></template>\n\n\n\n\n\n\n\n</pat>\n</sysmsg>'}, 'Status': 4, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1753635222, 'MsgSource': '<msgsource>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'NewMsgId': 5220879728268359450, 'MsgSeq': 871404362}
2025-07-28 00:53:33 | DEBUG | 系统消息类型: pat
2025-07-28 00:53:33 | INFO | 收到拍一拍消息: 消息ID:805832917 来自:51595225532@chatroom 发送人:51595225532@chatroom 拍者:wxid_wogoxis9jdfq22 被拍:wxid_m1lh16d8hfl221 后缀:我们是好朋友
2025-07-28 00:53:33 | DEBUG | [PatReply] 被拍者 wxid_m1lh16d8hfl221 不是目标用户 wxid_4usgcju5ey9q29，跳过
2025-07-28 00:54:45 | DEBUG | 收到消息: {'MsgId': 284346002, 'FromUserName': {'string': '51595225532@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 1, 'Content': {'string': 'wxid_wogoxis9jdfq22:\n看吃播看饿了'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1753635294, 'MsgSource': '<msgsource>\n\t<bizflag>0</bizflag>\n\t<pua>1</pua>\n\t<eggIncluded>1</eggIncluded>\n\t<silence>0</silence>\n\t<membercount>10</membercount>\n\t<signature>N0_V1_Wd7Lr437|v1_RBnwneeH</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'PushContent': '晚. : 看吃播看饿了', 'NewMsgId': 2400881522307775473, 'MsgSeq': 871404363}
2025-07-28 00:54:45 | INFO | 收到文本消息: 消息ID:284346002 来自:51595225532@chatroom 发送人:wxid_wogoxis9jdfq22 @:[] 内容:看吃播看饿了
2025-07-28 00:54:45 | DEBUG | 处理消息内容: '看吃播看饿了'
2025-07-28 00:54:45 | DEBUG | 消息内容 '看吃播看饿了' 不匹配任何命令，忽略
2025-07-28 00:54:57 | DEBUG | 收到消息: {'MsgId': 1655663467, 'FromUserName': {'string': '51595225532@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 1, 'Content': {'string': 'wxid_m1lh16d8hfl221:\n饿了就吃'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1753635306, 'MsgSource': '<msgsource>\n\t<bizflag>0</bizflag>\n\t<pua>1</pua>\n\t<eggIncluded>1</eggIncluded>\n\t<silence>0</silence>\n\t<membercount>10</membercount>\n\t<signature>N0_V1_w+EIf/rk|v1_LNpPOiNQ</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'PushContent': 'Aurora : 饿了就吃', 'NewMsgId': 4307545502159445417, 'MsgSeq': 871404364}
2025-07-28 00:54:57 | INFO | 收到文本消息: 消息ID:1655663467 来自:51595225532@chatroom 发送人:wxid_m1lh16d8hfl221 @:[] 内容:饿了就吃
2025-07-28 00:54:57 | DEBUG | 处理消息内容: '饿了就吃'
2025-07-28 00:54:57 | DEBUG | 消息内容 '饿了就吃' 不匹配任何命令，忽略
2025-07-28 00:56:39 | DEBUG | 收到消息: {'MsgId': 1389733986, 'FromUserName': {'string': '51595225532@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 1, 'Content': {'string': 'wxid_wogoxis9jdfq22:\n懒得点了'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1753635408, 'MsgSource': '<msgsource>\n\t<bizflag>0</bizflag>\n\t<pua>1</pua>\n\t<eggIncluded>1</eggIncluded>\n\t<silence>0</silence>\n\t<membercount>10</membercount>\n\t<signature>N0_V1_74Do8eIR|v1_86nrVS+H</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'PushContent': '晚. : 懒得点了', 'NewMsgId': 4794076643951923431, 'MsgSeq': 871404365}
2025-07-28 00:56:39 | INFO | 收到文本消息: 消息ID:1389733986 来自:51595225532@chatroom 发送人:wxid_wogoxis9jdfq22 @:[] 内容:懒得点了
2025-07-28 00:56:39 | DEBUG | 处理消息内容: '懒得点了'
2025-07-28 00:56:39 | DEBUG | 消息内容 '懒得点了' 不匹配任何命令，忽略
2025-07-28 00:59:37 | DEBUG | 收到消息: {'MsgId': 1302279230, 'FromUserName': {'string': '27852221909@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 1, 'Content': {'string': 'wxid_1ielyjve42x122:\n@每天在等待前任的路上无法自拔\u2005'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1753635586, 'MsgSource': '<msgsource>\n\t<atuserlist>wxid_bmzp9achod6922</atuserlist>\n\t<bizflag>0</bizflag>\n\t<pua>1</pua>\n\t<alnode>\n\t\t<inlenlist>16</inlenlist>\n\t</alnode>\n\t<eggIncluded>1</eggIncluded>\n\t<silence>1</silence>\n\t<membercount>144</membercount>\n\t<signature>N0_V1_QS9RHXNI|v1_Lvh7ZTFE</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'NewMsgId': 5712705860964256966, 'MsgSeq': 871404366}
2025-07-28 00:59:37 | INFO | 收到文本消息: 消息ID:1302279230 来自:27852221909@chatroom 发送人:wxid_1ielyjve42x122 @:['wxid_bmzp9achod6922'] 内容:@每天在等待前任的路上无法自拔 
2025-07-28 00:59:37 | DEBUG | 处理消息内容: '@每天在等待前任的路上无法自拔'
2025-07-28 00:59:37 | DEBUG | 消息内容 '@每天在等待前任的路上无法自拔' 不匹配任何命令，忽略
2025-07-28 00:59:52 | DEBUG | 收到消息: {'MsgId': 813175648, 'FromUserName': {'string': '27852221909@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 3, 'Content': {'string': 'wxid_1ielyjve42x122:\n<?xml version="1.0"?>\n<msg>\n\t<img aeskey="6f9f2d58b5e8d758cd962db03baf7e5a" encryver="1" cdnthumbaeskey="6f9f2d58b5e8d758cd962db03baf7e5a" cdnthumburl="3057020100044b304902010002043d7ac55802032df66b0204d8a19624020468865774042432623935386461632d336432372d346465632d383835372d323866313065376632396665020405150a020201000405004c55cf00" cdnthumblength="3286" cdnthumbheight="55" cdnthumbwidth="120" cdnmidheight="0" cdnmidwidth="0" cdnhdheight="0" cdnhdwidth="0" cdnmidimgurl="3057020100044b304902010002043d7ac55802032df66b0204d8a19624020468865774042432623935386461632d336432372d346465632d383835372d323866313065376632396665020405150a020201000405004c55cf00" length="88486" md5="b9e9c35ca7c148444bd6e642cc9a42db" hevc_mid_size="88486" originsourcemd5="1685f80977ca435d51f7af65afa7e26a">\n\t\t<secHashInfoBase64>eyJwaGFzaCI6IjU1MTA1MTAwNTAwMDUwMDAiLCJwZHFoYXNoIjoiZGViNDdhMzAwZDY5Mjk2ODIwMmZiNWNiYjA4YWQwMWZhMTRiNjg2Y2UxN2Y5YWJjZjA3ZTk2YjRlMDJmMjdjMyJ9</secHashInfoBase64>\n\t\t<live>\n\t\t\t<duration>0</duration>\n\t\t\t<size>0</size>\n\t\t\t<md5 />\n\t\t\t<fileid />\n\t\t\t<hdsize>0</hdsize>\n\t\t\t<hdmd5 />\n\t\t\t<hdfileid />\n\t\t\t<stillimagetimems>0</stillimagetimems>\n\t\t</live>\n\t</img>\n\t<platform_signature />\n\t<imgdatahash />\n\t<ImgSourceInfo>\n\t\t<ImgSourceUrl />\n\t\t<BizType>0</BizType>\n\t</ImgSourceInfo>\n</msg>\n'}, 'Status': 3, 'ImgStatus': 2, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1753635601, 'MsgSource': '<msgsource>\n\t<bizflag>0</bizflag>\n\t<sec_msg_node>\n\t\t<uuid>d2ec2edc9a09d1399d3fecd1cf8a6b6a_</uuid>\n\t\t<risk-file-flag />\n\t\t<risk-file-md5-list />\n\t</sec_msg_node>\n\t<silence>1</silence>\n\t<membercount>144</membercount>\n\t<signature>N0_V1_0gLcW74x|v1_CkPi9cLN</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'NewMsgId': 3388697953223637461, 'MsgSeq': 871404367}
2025-07-28 00:59:52 | INFO | 收到图片消息: 消息ID:813175648 来自:27852221909@chatroom 发送人:wxid_1ielyjve42x122 XML:<?xml version="1.0"?><msg><img aeskey="6f9f2d58b5e8d758cd962db03baf7e5a" encryver="1" cdnthumbaeskey="6f9f2d58b5e8d758cd962db03baf7e5a" cdnthumburl="3057020100044b304902010002043d7ac55802032df66b0204d8a19624020468865774042432623935386461632d336432372d346465632d383835372d323866313065376632396665020405150a020201000405004c55cf00" cdnthumblength="3286" cdnthumbheight="55" cdnthumbwidth="120" cdnmidheight="0" cdnmidwidth="0" cdnhdheight="0" cdnhdwidth="0" cdnmidimgurl="3057020100044b304902010002043d7ac55802032df66b0204d8a19624020468865774042432623935386461632d336432372d346465632d383835372d323866313065376632396665020405150a020201000405004c55cf00" length="88486" md5="b9e9c35ca7c148444bd6e642cc9a42db" hevc_mid_size="88486" originsourcemd5="1685f80977ca435d51f7af65afa7e26a"><secHashInfoBase64>eyJwaGFzaCI6IjU1MTA1MTAwNTAwMDUwMDAiLCJwZHFoYXNoIjoiZGViNDdhMzAwZDY5Mjk2ODIwMmZiNWNiYjA4YWQwMWZhMTRiNjg2Y2UxN2Y5YWJjZjA3ZTk2YjRlMDJmMjdjMyJ9</secHashInfoBase64><live><duration>0</duration><size>0</size><md5 /><fileid /><hdsize>0</hdsize><hdmd5 /><hdfileid /><stillimagetimems>0</stillimagetimems></live></img><platform_signature /><imgdatahash /><ImgSourceInfo><ImgSourceUrl /><BizType>0</BizType></ImgSourceInfo></msg>
2025-07-28 00:59:52 | INFO | [ImageEcho] 保存图片信息成功，当前群 27852221909@chatroom 已存储 5 张图片
2025-07-28 00:59:52 | INFO | [TimerTask] 缓存图片消息: 813175648
2025-07-28 01:00:01 | DEBUG | 收到消息: {'MsgId': 1498054084, 'FromUserName': {'string': '27852221909@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 1, 'Content': {'string': 'wxid_1ielyjve42x122:\n感觉你还是挺白的'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1753635611, 'MsgSource': '<msgsource>\n\t<bizflag>0</bizflag>\n\t<pua>1</pua>\n\t<eggIncluded>1</eggIncluded>\n\t<silence>1</silence>\n\t<membercount>144</membercount>\n\t<signature>N0_V1_HwalKpJ1|v1_yYjlbGUu</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'NewMsgId': 6708014506165195991, 'MsgSeq': 871404368}
2025-07-28 01:00:01 | INFO | 收到文本消息: 消息ID:1498054084 来自:27852221909@chatroom 发送人:wxid_1ielyjve42x122 @:[] 内容:感觉你还是挺白的
2025-07-28 01:00:01 | DEBUG | 处理消息内容: '感觉你还是挺白的'
2025-07-28 01:00:02 | DEBUG | 消息内容 '感觉你还是挺白的' 不匹配任何命令，忽略
2025-07-28 01:00:58 | DEBUG | 收到消息: {'MsgId': 1509807107, 'FromUserName': {'string': '27852221909@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 1, 'Content': {'string': 'wxid_bmzp9achod6922:\n这不是刚好……'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1753635667, 'MsgSource': '<msgsource>\n\t<bizflag>0</bizflag>\n\t<pua>1</pua>\n\t<eggIncluded>1</eggIncluded>\n\t<silence>1</silence>\n\t<membercount>144</membercount>\n\t<signature>N0_V1_+3VjEjWj|v1_IkgjEr8+</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'NewMsgId': 2622678048379474435, 'MsgSeq': 871404369}
2025-07-28 01:00:58 | INFO | 收到文本消息: 消息ID:1509807107 来自:27852221909@chatroom 发送人:wxid_bmzp9achod6922 @:[] 内容:这不是刚好……
2025-07-28 01:00:58 | DEBUG | 处理消息内容: '这不是刚好……'
2025-07-28 01:00:58 | DEBUG | 消息内容 '这不是刚好……' 不匹配任何命令，忽略
2025-07-28 01:01:01 | DEBUG | 收到消息: {'MsgId': 1902444480, 'FromUserName': {'string': '27852221909@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 1, 'Content': {'string': 'wxid_bmzp9achod6922:\n赶紧换'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1753635670, 'MsgSource': '<msgsource>\n\t<bizflag>0</bizflag>\n\t<pua>1</pua>\n\t<eggIncluded>1</eggIncluded>\n\t<silence>1</silence>\n\t<membercount>144</membercount>\n\t<signature>N0_V1_nFUHl0Bs|v1_1pyHZGCP</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'NewMsgId': 8623489685779766676, 'MsgSeq': 871404370}
2025-07-28 01:01:01 | INFO | 收到文本消息: 消息ID:1902444480 来自:27852221909@chatroom 发送人:wxid_bmzp9achod6922 @:[] 内容:赶紧换
2025-07-28 01:01:01 | DEBUG | 处理消息内容: '赶紧换'
2025-07-28 01:01:01 | DEBUG | 消息内容 '赶紧换' 不匹配任何命令，忽略
2025-07-28 01:01:11 | DEBUG | 收到消息: {'MsgId': 501779288, 'FromUserName': {'string': '27852221909@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 3, 'Content': {'string': 'wxid_vuywamzgu2z012:\n<?xml version="1.0"?>\n<msg>\n\t<img aeskey="d85affc8db36fef002414aeca47cdd0a" encryver="1" cdnthumbaeskey="d85affc8db36fef002414aeca47cdd0a" cdnthumburl="3057020100044b304902010002048aa2fd4802032e6c6302042eab016f020468864fc3042433336431353837372d333966632d343266392d393162662d393238656364643739376462020405290a020201000405004c505700" cdnthumblength="4040" cdnthumbheight="162" cdnthumbwidth="288" cdnmidheight="0" cdnmidwidth="0" cdnhdheight="0" cdnhdwidth="0" cdnmidimgurl="3057020100044b304902010002048aa2fd4802032e6c6302042eab016f020468864fc3042433336431353837372d333966632d343266392d393162662d393238656364643739376462020405290a020201000405004c505700" length="251522" md5="7139e99f46d9c250459f202cfa1f31af" hevc_mid_size="18427" originsourcemd5="15323d19b1f090f307bd1504abf28a58">\n\t\t<secHashInfoBase64>eyJwaGFzaCI6IjcwNTE3MDExMDAwMDAwMTAiLCJwZHFIYXNoIjoiYWI1MjEwYTcwNzAzY2UyNTY3\nYWZlMjU2YWE1ZjMxYzdjYzM5OGNjYjUzOTNlZDU4NWFiNTE0Y2JlODMwYWQxYiJ9\n</secHashInfoBase64>\n\t\t<live>\n\t\t\t<duration>0</duration>\n\t\t\t<size>0</size>\n\t\t\t<md5 />\n\t\t\t<fileid />\n\t\t\t<hdsize>0</hdsize>\n\t\t\t<hdmd5 />\n\t\t\t<hdfileid />\n\t\t\t<stillimagetimems>0</stillimagetimems>\n\t\t</live>\n\t</img>\n\t<platform_signature />\n\t<imgdatahash />\n\t<ImgSourceInfo>\n\t\t<ImgSourceUrl />\n\t\t<BizType>0</BizType>\n\t</ImgSourceInfo>\n</msg>\n'}, 'Status': 3, 'ImgStatus': 2, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1753635679, 'MsgSource': '<msgsource>\n\t<sec_msg_node>\n\t\t<uuid>3fd2afd0669a2cc8f98fd3608331369e_</uuid>\n\t\t<risk-file-flag />\n\t\t<risk-file-md5-list />\n\t</sec_msg_node>\n\t<silence>1</silence>\n\t<membercount>144</membercount>\n\t<signature>N0_V1_ED4tkUCQ|v1_6bphorkR</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'NewMsgId': 4479818446536286337, 'MsgSeq': 871404371}
2025-07-28 01:01:11 | INFO | 收到图片消息: 消息ID:501779288 来自:27852221909@chatroom 发送人:wxid_vuywamzgu2z012 XML:<?xml version="1.0"?><msg><img aeskey="d85affc8db36fef002414aeca47cdd0a" encryver="1" cdnthumbaeskey="d85affc8db36fef002414aeca47cdd0a" cdnthumburl="3057020100044b304902010002048aa2fd4802032e6c6302042eab016f020468864fc3042433336431353837372d333966632d343266392d393162662d393238656364643739376462020405290a020201000405004c505700" cdnthumblength="4040" cdnthumbheight="162" cdnthumbwidth="288" cdnmidheight="0" cdnmidwidth="0" cdnhdheight="0" cdnhdwidth="0" cdnmidimgurl="3057020100044b304902010002048aa2fd4802032e6c6302042eab016f020468864fc3042433336431353837372d333966632d343266392d393162662d393238656364643739376462020405290a020201000405004c505700" length="251522" md5="7139e99f46d9c250459f202cfa1f31af" hevc_mid_size="18427" originsourcemd5="15323d19b1f090f307bd1504abf28a58"><secHashInfoBase64>eyJwaGFzaCI6IjcwNTE3MDExMDAwMDAwMTAiLCJwZHFIYXNoIjoiYWI1MjEwYTcwNzAzY2UyNTY3YWZlMjU2YWE1ZjMxYzdjYzM5OGNjYjUzOTNlZDU4NWFiNTE0Y2JlODMwYWQxYiJ9</secHashInfoBase64><live><duration>0</duration><size>0</size><md5 /><fileid /><hdsize>0</hdsize><hdmd5 /><hdfileid /><stillimagetimems>0</stillimagetimems></live></img><platform_signature /><imgdatahash /><ImgSourceInfo><ImgSourceUrl /><BizType>0</BizType></ImgSourceInfo></msg>
2025-07-28 01:01:11 | INFO | [ImageEcho] 保存图片信息成功，当前群 27852221909@chatroom 已存储 5 张图片
2025-07-28 01:01:11 | INFO | [TimerTask] 缓存图片消息: 501779288
2025-07-28 01:01:12 | DEBUG | 收到消息: {'MsgId': 2000250224, 'FromUserName': {'string': '27852221909@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 47, 'Content': {'string': 'wxid_vuywamzgu2z012:\n<msg><emoji fromusername = "wxid_vuywamzgu2z012" tousername = "27852221909@chatroom" type="2" idbuffer="media:0_0" md5="314db052c1847c0b51794ce3eff22482" len = "5167" productid="" androidmd5="314db052c1847c0b51794ce3eff22482" androidlen="5167" s60v3md5 = "314db052c1847c0b51794ce3eff22482" s60v3len="5167" s60v5md5 = "314db052c1847c0b51794ce3eff22482" s60v5len="5167" cdnurl = "http://wxapp.tc.qq.com/262/20304/stodownload?m=314db052c1847c0b51794ce3eff22482&amp;filekey=30340201010420301e02020106040253480410314db052c1847c0b51794ce3eff224820202142f040d00000004627466730000000132&amp;hy=SH&amp;storeid=26303a1b5000ee6ad950c9c370000010600004f50534828034b00b6d05aeac&amp;bizid=1023" designerid = "" thumburl = "" encrypturl = "http://wxapp.tc.qq.com/262/20304/stodownload?m=30521179addfd3e55a1a8baeb38feff6&amp;filekey=30340201010420301e0202010604025348041030521179addfd3e55a1a8baeb38feff602021430040d00000004627466730000000132&amp;hy=SH&amp;storeid=26303a1b60000df53950c9c370000010600004f5053481bd34b00b6cfd4d17&amp;bizid=1023" aeskey= "1cd9b41a5bdf1d6ba4d514bdc1e24df4" externurl = "" externmd5 = "" width= "80" height= "80" tpurl= "" tpauthkey= "" attachedtext= "" attachedtextcolor= "" lensid= "" emojiattr= "" linkid= "" desc= "" ></emoji>  </msg>'}, 'Status': 3, 'ImgStatus': 2, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1753635682, 'MsgSource': '<msgsource>\n\t<silence>1</silence>\n\t<membercount>144</membercount>\n\t<signature>N0_V1_6dMGiSG/|v1_5Bbn7qAH</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'NewMsgId': 3312962049442181298, 'MsgSeq': 871404372}
2025-07-28 01:01:12 | INFO | 收到表情消息: 消息ID:2000250224 来自:27852221909@chatroom 发送人:wxid_vuywamzgu2z012 MD5:314db052c1847c0b51794ce3eff22482 大小:5167
2025-07-28 01:01:12 | DEBUG | [DoubaoImageRecognition] 缓存表情消息: 3312962049442181298
2025-07-28 01:01:36 | DEBUG | 收到消息: {'MsgId': 121357866, 'FromUserName': {'string': '27852221909@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 47, 'Content': {'string': 'wxid_bmzp9achod6922:\n<msg><emoji fromusername="wxid_bmzp9achod6922" tousername="27852221909@chatroom" type="2" idbuffer="media:0_0" md5="d0365ad5e696216921a7c4e0d47bb338" len="91742" productid="" androidmd5="d0365ad5e696216921a7c4e0d47bb338" androidlen="91742" s60v3md5="d0365ad5e696216921a7c4e0d47bb338" s60v3len="91742" s60v5md5="d0365ad5e696216921a7c4e0d47bb338" s60v5len="91742" cdnurl="http://vweixinf.tc.qq.com/110/20401/stodownload?m=d0365ad5e696216921a7c4e0d47bb338&amp;filekey=30440201010430302e02016e0402534804206430333635616435653639363231363932316137633465306434376262333338020301665e040d00000004627466730000000132&amp;hy=SH&amp;storeid=268862fa20001a52778c92ecc0000006e01004fb1534827782bc1e6b0402b5&amp;ef=1&amp;bizid=1022" designerid="" thumburl="" encrypturl="http://vweixinf.tc.qq.com/110/20402/stodownload?m=8a248b0a345d049524c8831f4ba77400&amp;filekey=30440201010430302e02016e04025348042038613234386230613334356430343935323463383833316634626137373430300203016660040d00000004627466730000000132&amp;hy=SH&amp;storeid=268862fa20002613178c92ecc0000006e02004fb2534827782bc1e6b0402d0&amp;ef=2&amp;bizid=1022" aeskey="f76c9f40832d41cc8ba84e2712e20b32" externurl="http://vweixinf.tc.qq.com/110/20403/stodownload?m=33147c509de082ba60749f8b84129e12&amp;filekey=3043020101042f302d02016e040253480420333331343763353039646530383262613630373439663862383431323965313202025e20040d00000004627466730000000132&amp;hy=SH&amp;storeid=268862fa2000322af78c92ecc0000006e03004fb3534827782bc1e6b0402e1&amp;ef=3&amp;bizid=1022" externmd5="0e0407118b90ec33388053108a00963f" width="640" height="640" tpurl="" tpauthkey="" attachedtext="" attachedtextcolor="" lensid="" emojiattr="" linkid="" desc=""></emoji><gameext type="0" content="0"></gameext></msg>'}, 'Status': 3, 'ImgStatus': 2, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1753635705, 'MsgSource': '<msgsource>\n\t<silence>1</silence>\n\t<membercount>144</membercount>\n\t<signature>N0_V1_KzhBorE4|v1_qmdQECXv</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'NewMsgId': 6830787173718651668, 'MsgSeq': 871404373}
2025-07-28 01:01:36 | INFO | 收到表情消息: 消息ID:121357866 来自:27852221909@chatroom 发送人:wxid_bmzp9achod6922 MD5:d0365ad5e696216921a7c4e0d47bb338 大小:91742
2025-07-28 01:01:36 | DEBUG | [DoubaoImageRecognition] 缓存表情消息: 6830787173718651668
2025-07-28 01:01:46 | DEBUG | 收到消息: {'MsgId': 653968956, 'FromUserName': {'string': '27852221909@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 10002, 'Content': {'string': 'wxid_vuywamzgu2z012:\n<sysmsg type="revokemsg"><revokemsg><session>27852221909@chatroom</session><msgid>1827597040</msgid><newmsgid>4479818446536286337</newmsgid><replacemsg><![CDATA["꒰ঌ玖弦໒꒱" 撤回了一条消息]]></replacemsg><announcement_id><![CDATA[]]></announcement_id></revokemsg></sysmsg>'}, 'Status': 4, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1753635709, 'MsgSource': '<msgsource>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'NewMsgId': 9000083262495125631, 'MsgSeq': 871404374}
2025-07-28 01:01:46 | DEBUG | 系统消息类型: revokemsg
2025-07-28 01:01:46 | INFO | 未知的系统消息类型: {'MsgId': 653968956, 'ToWxid': 'wxid_4usgcju5ey9q29', 'MsgType': 10002, 'Content': '\n<sysmsg type="revokemsg"><revokemsg><session>27852221909@chatroom</session><msgid>1827597040</msgid><newmsgid>4479818446536286337</newmsgid><replacemsg><![CDATA["꒰ঌ玖弦໒꒱" 撤回了一条消息]]></replacemsg><announcement_id><![CDATA[]]></announcement_id></revokemsg></sysmsg>', 'Status': 4, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1753635709, 'MsgSource': '<msgsource>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'NewMsgId': 9000083262495125631, 'MsgSeq': 871404374, 'FromWxid': '27852221909@chatroom', 'IsGroup': True, 'SenderWxid': 'wxid_vuywamzgu2z012'}
2025-07-28 01:02:15 | DEBUG | 收到消息: {'MsgId': 530265839, 'FromUserName': {'string': '27852221909@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 47, 'Content': {'string': 'wxid_1ielyjve42x122:\n<msg><emoji fromusername="wxid_1ielyjve42x122" tousername="27852221909@chatroom" type="2" idbuffer="media:0_0" md5="78d0384cefb2953e8764f75a2d6b16f3" len="32909" productid="com.tencent.xin.emoticon.person.stiker_15925608461d6d4e512a235148" androidmd5="78d0384cefb2953e8764f75a2d6b16f3" androidlen="32909" s60v3md5="78d0384cefb2953e8764f75a2d6b16f3" s60v3len="32909" s60v5md5="78d0384cefb2953e8764f75a2d6b16f3" s60v5len="32909" cdnurl="http://wxapp.tc.qq.com/262/20304/stodownload?m=78d0384cefb2953e8764f75a2d6b16f3&amp;filekey=30350201010421301f0202010604025348041078d0384cefb2953e8764f75a2d6b16f3020300808d040d00000004627466730000000132&amp;hy=SH&amp;storeid=263141f36000ef621000000000000010600004f50534828e67b40b713b0388&amp;bizid=1023" designerid="" thumburl="http://wxapp.tc.qq.com/275/20304/stodownload?m=3a6775fac5ef00aa3f4fc552a6cf8d4a&amp;filekey=30340201010420301e020201130402534804103a6775fac5ef00aa3f4fc552a6cf8d4a02023799040d00000004627466730000000132&amp;hy=SH&amp;storeid=26479d573000c515e000000000000011300004f505348000098e0b6bdbcecc&amp;bizid=1023" encrypturl="http://wxapp.tc.qq.com/262/20304/stodownload?m=7adff2f54ca8b7a163dce744ed84ecac&amp;filekey=30350201010421301f020201060402534804107adff2f54ca8b7a163dce744ed84ecac0203008090040d00000004627466730000000132&amp;hy=SH&amp;storeid=263141f370003228c000000000000010600004f50534829c65b40b712321ee&amp;bizid=1023" aeskey="7fb6006eacfd5fcf0dbe8f0ede438120" externurl="http://wxapp.tc.qq.com/262/20304/stodownload?m=b0928c579051d90634c1dba6f224d91d&amp;filekey=30340201010420301e02020106040253480410b0928c579051d90634c1dba6f224d91d02022820040d00000004627466730000000132&amp;hy=SH&amp;storeid=263141f370005ebc5000000000000010600004f5053480056fb40b7118fd99&amp;bizid=1023" externmd5="b77afb782aeed31b28588a14c5ede132" width="240" height="240" tpurl="" tpauthkey="" attachedtext="" attachedtextcolor="" lensid="" emojiattr="" linkid="" desc="Cg8KBXpoX2NuEgbmirHmirEKCQoFemhfdHcSAAoLCgdkZWZhdWx0EgA="></emoji><gameext type="0" content="0"></gameext></msg>'}, 'Status': 3, 'ImgStatus': 2, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1753635744, 'MsgSource': '<msgsource>\n\t<silence>1</silence>\n\t<membercount>144</membercount>\n\t<signature>N0_V1_Vm6+73Pn|v1_M00FA8B+</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'NewMsgId': 185174030048506577, 'MsgSeq': 871404375}
2025-07-28 01:02:15 | INFO | 收到表情消息: 消息ID:530265839 来自:27852221909@chatroom 发送人:wxid_1ielyjve42x122 MD5:78d0384cefb2953e8764f75a2d6b16f3 大小:32909
2025-07-28 01:02:15 | DEBUG | [DoubaoImageRecognition] 缓存表情消息: 185174030048506577
2025-07-28 01:05:51 | INFO | [TempFileManager] 开始清理临时文件...
2025-07-28 01:05:51 | DEBUG | 开始检查群成员变化，当前监控 2 个群
2025-07-28 01:05:52 | DEBUG | 群成员变化检查完成
2025-07-28 01:35:51 | INFO | [TempFileManager] 开始清理临时文件...
2025-07-28 02:05:51 | INFO | [TempFileManager] 开始清理临时文件...
2025-07-28 02:05:51 | DEBUG | 开始检查群成员变化，当前监控 2 个群
2025-07-28 02:05:52 | DEBUG | 群成员变化检查完成
2025-07-28 02:35:51 | INFO | [TempFileManager] 开始清理临时文件...
2025-07-28 03:05:51 | INFO | [TempFileManager] 开始清理临时文件...
2025-07-28 03:05:51 | DEBUG | 开始检查群成员变化，当前监控 2 个群
2025-07-28 03:05:52 | DEBUG | 群成员变化检查完成
2025-07-28 03:35:51 | INFO | [TempFileManager] 开始清理临时文件...
2025-07-28 04:05:51 | INFO | [TempFileManager] 开始清理临时文件...
2025-07-28 04:05:51 | DEBUG | 开始检查群成员变化，当前监控 2 个群
2025-07-28 04:05:52 | DEBUG | 群成员变化检查完成
2025-07-28 04:35:51 | INFO | [TempFileManager] 开始清理临时文件...
2025-07-28 04:46:54 | DEBUG | 收到消息: {'MsgId': 1640034, 'FromUserName': {'string': '51891329927@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 1, 'Content': {'string': 'wxid_84hqcidv21m222:\n签到'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1753649223, 'MsgSource': '<msgsource>\n\t<pua>1</pua>\n\t<eggIncluded>1</eggIncluded>\n\t<silence>0</silence>\n\t<membercount>17</membercount>\n\t<signature>N0_V1_qCiyjg19|v1_VJZHUT4J</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'PushContent': '全明星-书山压力大 : 签到', 'NewMsgId': 7576514628142814812, 'MsgSeq': 871404376}
2025-07-28 04:46:54 | INFO | 收到文本消息: 消息ID:1640034 来自:51891329927@chatroom 发送人:wxid_84hqcidv21m222 @:[] 内容:签到
2025-07-28 04:46:54 | DEBUG | 处理消息内容: '签到'
2025-07-28 04:46:54 | DEBUG | 消息内容 '签到' 不匹配任何命令，忽略
2025-07-28 04:46:54 | INFO | 数据库: 用户wxid_84hqcidv21m222登录时间设置为2025-07-28 00:00:00+08:00
2025-07-28 04:46:54 | INFO | 数据库: 用户wxid_84hqcidv21m222连续签到天数设置为4
2025-07-28 04:46:54 | INFO | 数据库: 用户wxid_84hqcidv21m222积分增加13
2025-07-28 04:46:55 | INFO | 发送文字消息: 对方wxid:51891329927@chatroom at:['wxid_84hqcidv21m222'] 内容:@眠凛 
-----XYBot-----
签到成功！你领到了 13 个积分！✅
你是今天第 1 个签到的！🎉
你连续签到了 4 天！[爱心]
2025-07-28 05:05:51 | INFO | [TempFileManager] 开始清理临时文件...
2025-07-28 05:05:51 | DEBUG | 开始检查群成员变化，当前监控 2 个群
2025-07-28 05:05:52 | DEBUG | 群成员变化检查完成
2025-07-28 05:35:51 | INFO | [TempFileManager] 开始清理临时文件...
2025-07-28 06:05:51 | INFO | [TempFileManager] 开始清理临时文件...
2025-07-28 06:05:51 | DEBUG | 开始检查群成员变化，当前监控 2 个群
2025-07-28 06:05:52 | DEBUG | 群成员变化检查完成
2025-07-28 06:18:42 | DEBUG | 收到消息: {'MsgId': 549108840, 'FromUserName': {'string': '27852221909@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 1, 'Content': {'string': 'wxid_zbh5p28da1si22:\n@纯牛nǎi\u2005你隔三差五把我删除，你以后就别加我了哈'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1753654732, 'MsgSource': '<msgsource>\n\t<atuserlist><![CDATA[wxid_nxqotg58k6nb12]]></atuserlist>\n\t<pua>1</pua>\n\t<eggIncluded>1</eggIncluded>\n\t<silence>1</silence>\n\t<membercount>144</membercount>\n\t<signature>N0_V1_69YWS4Sf|v1_KCTM0iM4</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'NewMsgId': 465372382371931598, 'MsgSeq': 871404379}
2025-07-28 06:18:42 | INFO | 收到文本消息: 消息ID:549108840 来自:27852221909@chatroom 发送人:wxid_zbh5p28da1si22 @:['wxid_nxqotg58k6nb12'] 内容:@纯牛nǎi 你隔三差五把我删除，你以后就别加我了哈
2025-07-28 06:18:42 | DEBUG | 处理消息内容: '@纯牛nǎi 你隔三差五把我删除，你以后就别加我了哈'
2025-07-28 06:18:42 | DEBUG | 消息内容 '@纯牛nǎi 你隔三差五把我删除，你以后就别加我了哈' 不匹配任何命令，忽略
2025-07-28 06:19:30 | DEBUG | 收到消息: {'MsgId': 1874382162, 'FromUserName': {'string': '27852221909@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 1, 'Content': {'string': 'wxid_zbh5p28da1si22:\n每回都说清理错人，'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1753654780, 'MsgSource': '<msgsource>\n\t<pua>1</pua>\n\t<eggIncluded>1</eggIncluded>\n\t<silence>1</silence>\n\t<membercount>144</membercount>\n\t<signature>N0_V1_7ZNeYkQp|v1_wefRhbgx</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'NewMsgId': 9128818395476054140, 'MsgSeq': 871404380}
2025-07-28 06:19:30 | INFO | 收到文本消息: 消息ID:1874382162 来自:27852221909@chatroom 发送人:wxid_zbh5p28da1si22 @:[] 内容:每回都说清理错人，
2025-07-28 06:19:30 | DEBUG | 处理消息内容: '每回都说清理错人，'
2025-07-28 06:19:30 | DEBUG | 消息内容 '每回都说清理错人，' 不匹配任何命令，忽略
2025-07-28 06:23:52 | DEBUG | 收到消息: {'MsgId': 1352207893, 'FromUserName': {'string': '48097389945@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 3, 'Content': {'string': 'zll953369865:\n<?xml version="1.0"?>\n<msg>\n\t<img aeskey="041ffc29b819834f078501dc78d78f45" encryver="1" cdnthumbaeskey="041ffc29b819834f078501dc78d78f45" cdnthumburl="3057020100044b30490201000204d801834002032f55950204ca41f7df02046886a5d7042431363764333264632d353837622d343736372d616438312d616561323535343935383133020405252a010201000405004c511d00" cdnthumblength="2665" cdnthumbheight="472" cdnthumbwidth="464" cdnmidheight="0" cdnmidwidth="0" cdnhdheight="0" cdnhdwidth="0" cdnmidimgurl="3057020100044b30490201000204d801834002032f55950204ca41f7df02046886a5d7042431363764333264632d353837622d343736372d616438312d616561323535343935383133020405252a010201000405004c511d00" length="54421" cdnbigimgurl="3057020100044b30490201000204d801834002032f55950204ca41f7df02046886a5d7042431363764333264632d353837622d343736372d616438312d616561323535343935383133020405252a010201000405004c511d00" hdlength="117859" md5="5c2cbd206377145c9e20347dc8e4ae5b" hevc_mid_size="16681" originsourcemd5="853185a7116f6a97126742f0c3abdb1c">\n\t\t<secHashInfoBase64>eyJwaGFzaCI6IjEwMDAwMDAwMDAwMDAwMDAiLCJwZHFIYXNoIjoiNjk5OGFlZGNhNjE4ZGE2MGVj\nYzY3OWI2OTY3OGNlNjY3OTY2YTY5OWU0OTk1OTQ5NDE5YmU1YTUxYTRmMTI0NyJ9\n</secHashInfoBase64>\n\t\t<live>\n\t\t\t<duration>0</duration>\n\t\t\t<size>0</size>\n\t\t\t<md5 />\n\t\t\t<fileid />\n\t\t\t<hdsize>0</hdsize>\n\t\t\t<hdmd5 />\n\t\t\t<hdfileid />\n\t\t\t<stillimagetimems>0</stillimagetimems>\n\t\t</live>\n\t</img>\n\t<platform_signature />\n\t<imgdatahash />\n\t<ImgSourceInfo>\n\t\t<ImgSourceUrl />\n\t\t<BizType>0</BizType>\n\t</ImgSourceInfo>\n</msg>\n'}, 'Status': 3, 'ImgStatus': 2, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1753655041, 'MsgSource': '<msgsource>\n\t<sec_msg_node>\n\t\t<uuid>cebcff229a3e8638b43958213a4aca40_</uuid>\n\t\t<risk-file-flag />\n\t\t<risk-file-md5-list />\n\t</sec_msg_node>\n\t<silence>0</silence>\n\t<membercount>73</membercount>\n\t<signature>N0_V1_cGZad8T4|v1_TLwBhYj9</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'PushContent': '【微信红包】收到一个微信红包在群聊中发了一张图片', 'NewMsgId': 9090075759184959223, 'MsgSeq': 871404381}
2025-07-28 06:23:52 | INFO | 收到图片消息: 消息ID:1352207893 来自:48097389945@chatroom 发送人:zll953369865 XML:<?xml version="1.0"?><msg><img aeskey="041ffc29b819834f078501dc78d78f45" encryver="1" cdnthumbaeskey="041ffc29b819834f078501dc78d78f45" cdnthumburl="3057020100044b30490201000204d801834002032f55950204ca41f7df02046886a5d7042431363764333264632d353837622d343736372d616438312d616561323535343935383133020405252a010201000405004c511d00" cdnthumblength="2665" cdnthumbheight="472" cdnthumbwidth="464" cdnmidheight="0" cdnmidwidth="0" cdnhdheight="0" cdnhdwidth="0" cdnmidimgurl="3057020100044b30490201000204d801834002032f55950204ca41f7df02046886a5d7042431363764333264632d353837622d343736372d616438312d616561323535343935383133020405252a010201000405004c511d00" length="54421" cdnbigimgurl="3057020100044b30490201000204d801834002032f55950204ca41f7df02046886a5d7042431363764333264632d353837622d343736372d616438312d616561323535343935383133020405252a010201000405004c511d00" hdlength="117859" md5="5c2cbd206377145c9e20347dc8e4ae5b" hevc_mid_size="16681" originsourcemd5="853185a7116f6a97126742f0c3abdb1c"><secHashInfoBase64>eyJwaGFzaCI6IjEwMDAwMDAwMDAwMDAwMDAiLCJwZHFIYXNoIjoiNjk5OGFlZGNhNjE4ZGE2MGVjYzY3OWI2OTY3OGNlNjY3OTY2YTY5OWU0OTk1OTQ5NDE5YmU1YTUxYTRmMTI0NyJ9</secHashInfoBase64><live><duration>0</duration><size>0</size><md5 /><fileid /><hdsize>0</hdsize><hdmd5 /><hdfileid /><stillimagetimems>0</stillimagetimems></live></img><platform_signature /><imgdatahash /><ImgSourceInfo><ImgSourceUrl /><BizType>0</BizType></ImgSourceInfo></msg>
2025-07-28 06:23:53 | INFO | [ImageEcho] 保存图片信息成功，当前群 48097389945@chatroom 已存储 5 张图片
2025-07-28 06:23:53 | INFO | [TimerTask] 缓存图片消息: 1352207893
2025-07-28 06:23:55 | DEBUG | 收到消息: {'MsgId': 1823222413, 'FromUserName': {'string': '48097389945@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 1, 'Content': {'string': 'zll953369865:\n118.80'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1753655045, 'MsgSource': '<msgsource>\n\t<pua>1</pua>\n\t<alnode>\n\t\t<cf>2</cf>\n\t</alnode>\n\t<eggIncluded>1</eggIncluded>\n\t<silence>0</silence>\n\t<membercount>73</membercount>\n\t<signature>N0_V1_4JAH61WB|v1_T+AmcfT/</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'PushContent': '【微信红包】收到一个微信红包 : 118.80', 'NewMsgId': 3731044413120238606, 'MsgSeq': 871404382}
2025-07-28 06:23:55 | INFO | 收到文本消息: 消息ID:1823222413 来自:48097389945@chatroom 发送人:zll953369865 @:[] 内容:118.80
2025-07-28 06:23:55 | DEBUG | 处理消息内容: '118.80'
2025-07-28 06:23:55 | DEBUG | 消息内容 '118.80' 不匹配任何命令，忽略
2025-07-28 06:35:51 | INFO | [TempFileManager] 开始清理临时文件...
2025-07-28 06:45:02 | DEBUG | 收到消息: {'MsgId': 862186732, 'FromUserName': {'string': '48097389945@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 47, 'Content': {'string': 'last--exile:\n<msg><emoji fromusername="last--exile" tousername="48097389945@chatroom" type="2" idbuffer="media:0_0" md5="67252bd05818c5ee5176584600ac1917" len="85707" productid="" androidmd5="67252bd05818c5ee5176584600ac1917" androidlen="85707" s60v3md5="67252bd05818c5ee5176584600ac1917" s60v3len="85707" s60v5md5="67252bd05818c5ee5176584600ac1917" s60v5len="85707" cdnurl="http://wxapp.tc.qq.com/262/20304/stodownload?m=67252bd05818c5ee5176584600ac1917&amp;filekey=30350201010421301f020201060402535a041067252bd05818c5ee5176584600ac19170203014ecb040d00000004627466730000000131&amp;hy=SZ&amp;storeid=32303231303632363032333634353030306432633263346563663936643364396461356630393030303030313036&amp;bizid=1023" designerid="" thumburl="" encrypturl="http://wxapp.tc.qq.com/262/20304/stodownload?m=54b3cf0340c762d3b5d787e57939e616&amp;filekey=30350201010421301f020201060402535a041054b3cf0340c762d3b5d787e57939e6160203014ed0040d00000004627466730000000131&amp;hy=SZ&amp;storeid=32303231303632363032333634363030303239613634346563663936643330363132356630393030303030313036&amp;bizid=1023" aeskey="c63e8c1e826f4fc1799833cbfd4de235" externurl="http://wxapp.tc.qq.com/262/20304/stodownload?m=fb62aad8cd6015aa9a63f71b5e66196b&amp;filekey=30340201010420301e020201060402535a0410fb62aad8cd6015aa9a63f71b5e66196b02024440040d00000004627466730000000131&amp;hy=SZ&amp;storeid=32303231303632363032333634363030303533626261346563663936643330363132356630393030303030313036&amp;bizid=1023" externmd5="097f35be933e72ddb90e81e2e8e4418f" width="360" height="360" tpurl="" tpauthkey="" attachedtext="" attachedtextcolor="" lensid="" emojiattr="CgzmiJHmnaXllabvvIE=" linkid="" desc=""></emoji></msg>'}, 'Status': 3, 'ImgStatus': 2, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1753656312, 'MsgSource': '<msgsource>\n\t<silence>0</silence>\n\t<membercount>73</membercount>\n\t<signature>N0_V1_91Bmkbpp|v1_IkHvpJw+</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'PushContent': '亮在群聊中发了一个表情', 'NewMsgId': 7048581459782492384, 'MsgSeq': 871404383}
2025-07-28 06:45:02 | INFO | 收到表情消息: 消息ID:862186732 来自:48097389945@chatroom 发送人:last--exile MD5:67252bd05818c5ee5176584600ac1917 大小:85707
2025-07-28 06:45:03 | DEBUG | [DoubaoImageRecognition] 缓存表情消息: 7048581459782492384
2025-07-28 07:01:29 | DEBUG | 收到消息: {'MsgId': 1791361872, 'FromUserName': {'string': '48097389945@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 3, 'Content': {'string': 'wxid_n5c0aekjceu621:\n<?xml version="1.0"?>\n<msg>\n\t<img aeskey="aa83396a0b743db115bb98c8e50689d6" encryver="1" cdnthumbaeskey="aa83396a0b743db115bb98c8e50689d6" cdnthumburl="3057020100044b304902010002045f9677f402032dd21502042090f2b602046886afd2042438633039366236312d353934652d343962362d623536612d646664636164366263656139020405252a010201000405004c55cd00" cdnthumblength="3558" cdnthumbheight="120" cdnthumbwidth="68" cdnmidheight="0" cdnmidwidth="0" cdnhdheight="0" cdnhdwidth="0" cdnmidimgurl="3057020100044b304902010002045f9677f402032dd21502042090f2b602046886afd2042438633039366236312d353934652d343962362d623536612d646664636164366263656139020405252a010201000405004c55cd00" length="9188" cdnbigimgurl="3057020100044b304902010002045f9677f402032dd21502042090f2b602046886afd2042438633039366236312d353934652d343962362d623536612d646664636164366263656139020405252a010201000405004c55cd00" hdlength="1242338" md5="379610b4e3b65e2ee8e998a0ac464211" hevc_mid_size="9188" originsourcemd5="379610b4e3b65e2ee8e998a0ac464211">\n\t\t<secHashInfoBase64 />\n\t\t<live>\n\t\t\t<duration>0</duration>\n\t\t\t<size>0</size>\n\t\t\t<md5 />\n\t\t\t<fileid />\n\t\t\t<hdsize>0</hdsize>\n\t\t\t<hdmd5 />\n\t\t\t<hdfileid />\n\t\t\t<stillimagetimems>0</stillimagetimems>\n\t\t</live>\n\t</img>\n\t<platform_signature />\n\t<imgdatahash />\n\t<ImgSourceInfo>\n\t\t<ImgSourceUrl />\n\t\t<BizType>0</BizType>\n\t</ImgSourceInfo>\n</msg>\n'}, 'Status': 3, 'ImgStatus': 2, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1753657299, 'MsgSource': '<msgsource>\n\t<alnode>\n\t\t<fr>4</fr>\n\t</alnode>\n\t<sec_msg_node>\n\t\t<uuid>8fc95a4238fe0de8f4220dfb1694c612_</uuid>\n\t\t<risk-file-flag />\n\t\t<risk-file-md5-list />\n\t</sec_msg_node>\n\t<silence>0</silence>\n\t<membercount>73</membercount>\n\t<signature>N0_V1_xw2Gvzso|v1_X/Bv9h/4</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'PushContent': 'ㅤㅤㅤㅤㅤㅤㅤㅤㅤㅤㅤㅤㅤㅤㅤㅤ在群聊中发了一张图片', 'NewMsgId': 1130989230961872130, 'MsgSeq': 871404384}
2025-07-28 07:01:29 | INFO | 收到图片消息: 消息ID:1791361872 来自:48097389945@chatroom 发送人:wxid_n5c0aekjceu621 XML:<?xml version="1.0"?><msg><img aeskey="aa83396a0b743db115bb98c8e50689d6" encryver="1" cdnthumbaeskey="aa83396a0b743db115bb98c8e50689d6" cdnthumburl="3057020100044b304902010002045f9677f402032dd21502042090f2b602046886afd2042438633039366236312d353934652d343962362d623536612d646664636164366263656139020405252a010201000405004c55cd00" cdnthumblength="3558" cdnthumbheight="120" cdnthumbwidth="68" cdnmidheight="0" cdnmidwidth="0" cdnhdheight="0" cdnhdwidth="0" cdnmidimgurl="3057020100044b304902010002045f9677f402032dd21502042090f2b602046886afd2042438633039366236312d353934652d343962362d623536612d646664636164366263656139020405252a010201000405004c55cd00" length="9188" cdnbigimgurl="3057020100044b304902010002045f9677f402032dd21502042090f2b602046886afd2042438633039366236312d353934652d343962362d623536612d646664636164366263656139020405252a010201000405004c55cd00" hdlength="1242338" md5="379610b4e3b65e2ee8e998a0ac464211" hevc_mid_size="9188" originsourcemd5="379610b4e3b65e2ee8e998a0ac464211"><secHashInfoBase64 /><live><duration>0</duration><size>0</size><md5 /><fileid /><hdsize>0</hdsize><hdmd5 /><hdfileid /><stillimagetimems>0</stillimagetimems></live></img><platform_signature /><imgdatahash /><ImgSourceInfo><ImgSourceUrl /><BizType>0</BizType></ImgSourceInfo></msg>
2025-07-28 07:01:30 | INFO | [ImageEcho] 保存图片信息成功，当前群 48097389945@chatroom 已存储 5 张图片
2025-07-28 07:01:30 | INFO | [TimerTask] 缓存图片消息: 1791361872
2025-07-28 07:02:00 | DEBUG | 收到消息: {'MsgId': 434472751, 'FromUserName': {'string': '48097389945@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 1, 'Content': {'string': 'wxid_n5c0aekjceu621:\n清晨的太阳如此金黄'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1753657330, 'MsgSource': '<msgsource>\n\t<pua>1</pua>\n\t<silence>0</silence>\n\t<membercount>73</membercount>\n\t<signature>N0_V1_M3Q+bM5z|v1_SeC0mNu2</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'PushContent': 'ㅤㅤㅤㅤㅤㅤㅤㅤㅤㅤㅤㅤㅤㅤㅤㅤ : 清晨的太阳如此金黄', 'NewMsgId': 4997846050042777872, 'MsgSeq': 871404385}
2025-07-28 07:02:00 | INFO | 收到文本消息: 消息ID:434472751 来自:48097389945@chatroom 发送人:wxid_n5c0aekjceu621 @:[] 内容:清晨的太阳如此金黄
2025-07-28 07:02:01 | DEBUG | 处理消息内容: '清晨的太阳如此金黄'
2025-07-28 07:02:01 | DEBUG | 消息内容 '清晨的太阳如此金黄' 不匹配任何命令，忽略
2025-07-28 07:05:51 | INFO | [TempFileManager] 开始清理临时文件...
2025-07-28 07:05:51 | DEBUG | 开始检查群成员变化，当前监控 2 个群
2025-07-28 07:05:52 | DEBUG | 群成员变化检查完成
2025-07-28 07:13:05 | DEBUG | 收到消息: {'MsgId': 1160938690, 'FromUserName': {'string': '27852221909@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 1, 'Content': {'string': 'wxid_z9qsv7556b7822:\n谁有J啊'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1753657995, 'MsgSource': '<msgsource>\n\t<bizflag>0</bizflag>\n\t<pua>1</pua>\n\t<eggIncluded>1</eggIncluded>\n\t<silence>1</silence>\n\t<membercount>144</membercount>\n\t<signature>N0_V1_bnUHxAT/|v1_N7ws1pZz</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'NewMsgId': 8022693982441238104, 'MsgSeq': 871404386}
2025-07-28 07:13:05 | INFO | 收到文本消息: 消息ID:1160938690 来自:27852221909@chatroom 发送人:wxid_z9qsv7556b7822 @:[] 内容:谁有J啊
2025-07-28 07:13:05 | DEBUG | 处理消息内容: '谁有J啊'
2025-07-28 07:13:05 | DEBUG | 消息内容 '谁有J啊' 不匹配任何命令，忽略
2025-07-28 07:13:10 | DEBUG | 收到消息: {'MsgId': 1826491920, 'FromUserName': {'string': '27852221909@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 47, 'Content': {'string': 'wxid_z9qsv7556b7822:\n<msg><emoji fromusername = "wxid_z9qsv7556b7822" tousername = "27852221909@chatroom" type="3" idbuffer="media:0_0" md5="7233193f6c716256b42c664af4b2a66c" len = "9754" productid="" androidmd5="7233193f6c716256b42c664af4b2a66c" androidlen="9754" s60v3md5 = "7233193f6c716256b42c664af4b2a66c" s60v3len="9754" s60v5md5 = "7233193f6c716256b42c664af4b2a66c" s60v5len="9754" cdnurl = "http://vweixinf.tc.qq.com/110/20401/stodownload?m=7233193f6c716256b42c664af4b2a66c&amp;filekey=3043020101042f302d02016e0402535a042037323333313933663663373136323536623432633636346166346232613636630202261a040d00000004627466730000000132&amp;hy=SZ&amp;storeid=2645d685a00090d3f60557ae80000006e01004fb1535a1f506d0097c768f4e&amp;ef=1&amp;bizid=1022" designerid = "" thumburl = "" encrypturl = "http://vweixinf.tc.qq.com/110/20402/stodownload?m=7af71e99aa429180afa6c917b0203f76&amp;filekey=3043020101042f302d02016e0402535a0420376166373165393961613432393138306166613663393137623032303366373602022620040d00000004627466730000000132&amp;hy=SZ&amp;storeid=2645d685a000a80ab60557ae80000006e02004fb2535a1f506d0097c768f63&amp;ef=2&amp;bizid=1022" aeskey= "037d2bb05c184d79a24595879248ed89" externurl = "http://vweixinf.tc.qq.com/110/20403/stodownload?m=a78ca02a448398d97e0cdf2efd70cee5&amp;filekey=3043020101042f302d02016e0402535a04206137386361303261343438333938643937653063646632656664373063656535020213f0040d00000004627466730000000132&amp;hy=SZ&amp;storeid=2645d685a000bbd3060557ae80000006e03004fb3535a1f506d0097c768f72&amp;ef=3&amp;bizid=1022" externmd5 = "2adea523afb7a16f1e8aefd7c7c6a922" width= "300" height= "300" tpurl= "" tpauthkey= "" attachedtext= "" attachedtextcolor= "" lensid= "" emojiattr= "" linkid= "" desc= "" ></emoji> <gameext type="0" content="0" ></gameext> <extcommoninfo></extcommoninfo></msg>'}, 'Status': 3, 'ImgStatus': 2, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1753658000, 'MsgSource': '<msgsource>\n\t<silence>1</silence>\n\t<membercount>144</membercount>\n\t<signature>N0_V1_St3Vd01Q|v1_bf8BCTOw</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'NewMsgId': 8978725538683815568, 'MsgSeq': 871404387}
2025-07-28 07:13:10 | INFO | 收到表情消息: 消息ID:1826491920 来自:27852221909@chatroom 发送人:wxid_z9qsv7556b7822 MD5:7233193f6c716256b42c664af4b2a66c 大小:9754
2025-07-28 07:13:10 | DEBUG | [DoubaoImageRecognition] 缓存表情消息: 8978725538683815568
2025-07-28 07:13:58 | DEBUG | 收到消息: {'MsgId': 1265284373, 'FromUserName': {'string': '27852221909@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 1, 'Content': {'string': 'wxid_ctp9qffuf14b21:\n@55\u2005送你'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1753658048, 'MsgSource': '<msgsource>\n\t<atuserlist><![CDATA[wxid_z9qsv7556b7822]]></atuserlist>\n\t<pua>1</pua>\n\t<eggIncluded>1</eggIncluded>\n\t<silence>1</silence>\n\t<membercount>144</membercount>\n\t<signature>N0_V1_Huh1msHp|v1_MCVQ/HSz</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'NewMsgId': 5500440146788907030, 'MsgSeq': 871404388}
2025-07-28 07:13:58 | INFO | 收到文本消息: 消息ID:1265284373 来自:27852221909@chatroom 发送人:wxid_ctp9qffuf14b21 @:['wxid_z9qsv7556b7822'] 内容:@55 送你
2025-07-28 07:13:58 | DEBUG | 处理消息内容: '@55 送你'
2025-07-28 07:13:58 | DEBUG | 消息内容 '@55 送你' 不匹配任何命令，忽略
2025-07-28 07:14:02 | DEBUG | 收到消息: {'MsgId': 1687366734, 'FromUserName': {'string': '27852221909@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 1, 'Content': {'string': 'wxid_ctp9qffuf14b21:\n啥名'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1753658051, 'MsgSource': '<msgsource>\n\t<pua>1</pua>\n\t<eggIncluded>1</eggIncluded>\n\t<silence>1</silence>\n\t<membercount>144</membercount>\n\t<signature>N0_V1_Y+aJFP4O|v1_dOi7iQgF</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'NewMsgId': 1163925308442329253, 'MsgSeq': 871404389}
2025-07-28 07:14:02 | INFO | 收到文本消息: 消息ID:1687366734 来自:27852221909@chatroom 发送人:wxid_ctp9qffuf14b21 @:[] 内容:啥名
2025-07-28 07:14:02 | DEBUG | 处理消息内容: '啥名'
2025-07-28 07:14:02 | DEBUG | 消息内容 '啥名' 不匹配任何命令，忽略
2025-07-28 07:14:07 | DEBUG | 收到消息: {'MsgId': 574643248, 'FromUserName': {'string': '27852221909@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 1, 'Content': {'string': 'wangchunmeng7291:\n有'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1753658057, 'MsgSource': '<msgsource>\n\t<bizflag>0</bizflag>\n\t<pua>1</pua>\n\t<eggIncluded>1</eggIncluded>\n\t<silence>1</silence>\n\t<membercount>144</membercount>\n\t<signature>N0_V1_f6yvp2jP|v1_ICjUKaxG</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'NewMsgId': 3712681884921443925, 'MsgSeq': 871404390}
2025-07-28 07:14:07 | INFO | 收到文本消息: 消息ID:574643248 来自:27852221909@chatroom 发送人:wangchunmeng7291 @:[] 内容:有
2025-07-28 07:14:07 | DEBUG | 处理消息内容: '有'
2025-07-28 07:14:07 | DEBUG | 消息内容 '有' 不匹配任何命令，忽略
2025-07-28 07:14:15 | DEBUG | 收到消息: {'MsgId': 887766528, 'FromUserName': {'string': '27852221909@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 1, 'Content': {'string': 'wxid_ctp9qffuf14b21:\n@璟⃪訫⃪°\u2005早宝贝'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1753658065, 'MsgSource': '<msgsource>\n\t<atuserlist><![CDATA[wangchunmeng7291]]></atuserlist>\n\t<pua>1</pua>\n\t<eggIncluded>1</eggIncluded>\n\t<silence>1</silence>\n\t<membercount>144</membercount>\n\t<signature>N0_V1_nqUz7QCH|v1_ol9ZkHEW</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'NewMsgId': 2479399452375913659, 'MsgSeq': 871404391}
2025-07-28 07:14:15 | INFO | 收到文本消息: 消息ID:887766528 来自:27852221909@chatroom 发送人:wxid_ctp9qffuf14b21 @:['wangchunmeng7291'] 内容:@璟⃪訫⃪° 早宝贝
2025-07-28 07:14:15 | DEBUG | 处理消息内容: '@璟⃪訫⃪° 早宝贝'
2025-07-28 07:14:15 | DEBUG | 消息内容 '@璟⃪訫⃪° 早宝贝' 不匹配任何命令，忽略
2025-07-28 07:14:20 | DEBUG | 收到消息: {'MsgId': 1236783260, 'FromUserName': {'string': '27852221909@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 10002, 'Content': {'string': '27852221909@chatroom:\n<sysmsg type="pat">\n<pat>\n  <fromusername>wxid_ctp9qffuf14b21</fromusername>\n  <chatusername>27852221909@chatroom</chatusername>\n  <pattedusername>wangchunmeng7291</pattedusername>\n  <patsuffix><![CDATA[拍我，我就跟你走喔~]]></patsuffix>\n  <patsuffixversion>2</patsuffixversion>\n\n\n\n\n\n\n  <template><![CDATA["${wxid_ctp9qffuf14b21}" 拍了拍 "${wangchunmeng7291}" 拍我，我就跟你走喔~]]></template>\n\n\n\n\n\n\n\n</pat>\n</sysmsg>'}, 'Status': 4, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1753658066, 'MsgSource': '<msgsource>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'NewMsgId': 6679459772939940772, 'MsgSeq': 871404392}
2025-07-28 07:14:20 | DEBUG | 系统消息类型: pat
2025-07-28 07:14:20 | INFO | 收到拍一拍消息: 消息ID:1236783260 来自:27852221909@chatroom 发送人:27852221909@chatroom 拍者:wxid_ctp9qffuf14b21 被拍:wangchunmeng7291 后缀:拍我，我就跟你走喔~
2025-07-28 07:14:20 | DEBUG | [PatReply] 被拍者 wangchunmeng7291 不是目标用户 wxid_4usgcju5ey9q29，跳过
2025-07-28 07:14:24 | DEBUG | 收到消息: {'MsgId': 128245656, 'FromUserName': {'string': '27852221909@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 49, 'Content': {'string': 'wxid_z9qsv7556b7822:\n<?xml version="1.0"?>\n<msg>\n\t<appmsg appid="" sdkver="0">\n\t\t<title>没事没事我有啦谢谢你</title>\n\t\t<type>57</type>\n\t\t<appattach>\n\t\t\t<cdnthumbaeskey />\n\t\t\t<aeskey />\n\t\t</appattach>\n\t\t<refermsg>\n\t\t\t<type>1</type>\n\t\t\t<svrid>5500440146788907030</svrid>\n\t\t\t<fromusr>27852221909@chatroom</fromusr>\n\t\t\t<chatusr>wxid_ctp9qffuf14b21</chatusr>\n\t\t\t<displayname>Z⁰</displayname>\n\t\t\t<content>@55\u2005送你</content>\n\t\t\t<msgsource>&lt;msgsource&gt;&lt;sequence_id&gt;834243271&lt;/sequence_id&gt;\n\t&lt;atuserlist&gt;&lt;![CDATA[wxid_z9qsv7556b7822]]&gt;&lt;/atuserlist&gt;\n\t&lt;pua&gt;1&lt;/pua&gt;\n\t&lt;eggIncluded&gt;1&lt;/eggIncluded&gt;\n\t&lt;silence&gt;0&lt;/silence&gt;\n\t&lt;membercount&gt;144&lt;/membercount&gt;\n\t&lt;signature&gt;N0_V1_Rmd6WQ3D|v1_gPA4fYAa&lt;/signature&gt;\n\t&lt;tmp_node&gt;\n\t\t&lt;publisher-id&gt;&lt;/publisher-id&gt;\n\t&lt;/tmp_node&gt;\n&lt;/msgsource&gt;\n</msgsource>\n\t\t\t<createtime>1753658048</createtime>\n\t\t</refermsg>\n\t</appmsg>\n\t<fromusername>wxid_z9qsv7556b7822</fromusername>\n\t<scene>0</scene>\n\t<appinfo>\n\t\t<version>1</version>\n\t\t<appname></appname>\n\t</appinfo>\n\t<commenturl></commenturl>\n</msg>\n'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1753658074, 'MsgSource': '<msgsource>\n\t<bizflag>0</bizflag>\n\t<pua>1</pua>\n\t<eggIncluded>1</eggIncluded>\n\t<sec_msg_node>\n\t\t<uuid>a4ffa08abb2a7f44ffb17eefed824319_</uuid>\n\t\t<risk-file-flag />\n\t\t<risk-file-md5-list />\n\t</sec_msg_node>\n\t<silence>1</silence>\n\t<membercount>144</membercount>\n\t<signature>N0_V1_Jt/th/kr|v1_CRWF8V5A</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'NewMsgId': 70580910868349202, 'MsgSeq': 871404393}
2025-07-28 07:14:24 | DEBUG | 从群聊消息中提取发送者: wxid_z9qsv7556b7822
2025-07-28 07:14:24 | DEBUG | 使用已解析的XML处理引用消息
2025-07-28 07:14:24 | INFO | 收到引用消息: 消息ID:128245656 来自:27852221909@chatroom 发送人:wxid_z9qsv7556b7822 内容:没事没事我有啦谢谢你 引用类型:1
2025-07-28 07:14:24 | INFO | [DouBaoImageToImage] 收到引用消息: 没事没事我有啦谢谢你
2025-07-28 07:14:24 | INFO | [TimerTask] 收到引用消息调试信息:
2025-07-28 07:14:24 | INFO |   - 消息内容: 没事没事我有啦谢谢你
2025-07-28 07:14:24 | INFO |   - 群组ID: 27852221909@chatroom
2025-07-28 07:14:24 | INFO |   - 发送人: wxid_z9qsv7556b7822
2025-07-28 07:14:24 | INFO |   - 引用信息: {'MsgType': 1, 'Content': '@55\u2005送你', 'Msgid': '5500440146788907030', 'NewMsgId': '5500440146788907030', 'ToWxid': 'wxid_4usgcju5ey9q29', 'FromWxid': '27852221909@chatroom', 'Nickname': 'Z⁰', 'MsgSource': '<msgsource><sequence_id>834243271</sequence_id>\n\t<atuserlist><![CDATA[wxid_z9qsv7556b7822]]></atuserlist>\n\t<pua>1</pua>\n\t<eggIncluded>1</eggIncluded>\n\t<silence>0</silence>\n\t<membercount>144</membercount>\n\t<signature>N0_V1_Rmd6WQ3D|v1_gPA4fYAa</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'Createtime': '1753658048', 'SenderWxid': 'wxid_z9qsv7556b7822'}
2025-07-28 07:14:24 | INFO |   - 引用消息ID: 
2025-07-28 07:14:24 | INFO |   - 引用消息类型: 
2025-07-28 07:14:24 | INFO |   - 引用消息内容: @55 送你
2025-07-28 07:14:24 | INFO |   - 引用消息发送人: wxid_z9qsv7556b7822
2025-07-28 07:14:28 | DEBUG | 收到消息: {'MsgId': 22774056, 'FromUserName': {'string': '27852221909@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 47, 'Content': {'string': 'wxid_z9qsv7556b7822:\n<msg><emoji fromusername = "wxid_z9qsv7556b7822" tousername = "27852221909@chatroom" type="2" idbuffer="media:0_0" md5="81e75ec17f83e80e520dfb3369b3ffd2" len = "35924" productid="" androidmd5="81e75ec17f83e80e520dfb3369b3ffd2" androidlen="35924" s60v3md5 = "81e75ec17f83e80e520dfb3369b3ffd2" s60v3len="35924" s60v5md5 = "81e75ec17f83e80e520dfb3369b3ffd2" s60v5len="35924" cdnurl = "http://vweixinf.tc.qq.com/110/20401/stodownload?m=81e75ec17f83e80e520dfb3369b3ffd2&amp;filekey=30440201010430302e02016e04025348042038316537356563313766383365383065353230646662333336396233666664320203008c54040d00000004627466730000000132&amp;hy=SH&amp;storeid=265b1f7b20004b61397c9a5dc0000006e01004fb1534803e841b15673d2117&amp;ef=1&amp;bizid=1022" designerid = "" thumburl = "" encrypturl = "http://vweixinf.tc.qq.com/110/20402/stodownload?m=b5391e4365ceb08322fff498a1e399a8&amp;filekey=30440201010430302e02016e04025348042062353339316534333635636562303833323266666634393861316533393961380203008c60040d00000004627466730000000132&amp;hy=SH&amp;storeid=265b1f7b20005526f97c9a5dc0000006e02004fb2534803e841b15673d211e&amp;ef=2&amp;bizid=1022" aeskey= "05d6144ec2c14bfe9ac6c38a6551460b" externurl = "http://vweixinf.tc.qq.com/110/20403/stodownload?m=2ca718d7d6c810b4bf5c6cca9f58e7c1&amp;filekey=3043020101042f302d02016e040253480420326361373138643764366338313062346266356336636361396635386537633102024840040d00000004627466730000000132&amp;hy=SH&amp;storeid=265b1f7b20005c34997c9a5dc0000006e03004fb3534803e841b15673d2125&amp;ef=3&amp;bizid=1022" externmd5 = "6ed5e2b750faacc76f528135fb557ba0" width= "600" height= "600" tpurl= "" tpauthkey= "" attachedtext= "" attachedtextcolor= "" lensid= "" emojiattr= "" linkid= "" desc= "" ></emoji> <gameext type="0" content="0" ></gameext> <extcommoninfo></extcommoninfo></msg>'}, 'Status': 3, 'ImgStatus': 2, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1753658077, 'MsgSource': '<msgsource>\n\t<silence>1</silence>\n\t<membercount>144</membercount>\n\t<signature>N0_V1_hWFM/lEe|v1_/QpmPKQm</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'NewMsgId': 2306699009392839121, 'MsgSeq': 871404394}
2025-07-28 07:14:28 | INFO | 收到表情消息: 消息ID:22774056 来自:27852221909@chatroom 发送人:wxid_z9qsv7556b7822 MD5:81e75ec17f83e80e520dfb3369b3ffd2 大小:35924
2025-07-28 07:14:28 | DEBUG | [DoubaoImageRecognition] 缓存表情消息: 2306699009392839121
2025-07-28 07:14:39 | DEBUG | 收到消息: {'MsgId': 1907778689, 'FromUserName': {'string': '27852221909@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 1, 'Content': {'string': 'wangchunmeng7291:\n@Z⁰\u2005早啊 老大[坏笑]'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1753658089, 'MsgSource': '<msgsource>\n\t<atuserlist>wxid_ctp9qffuf14b21</atuserlist>\n\t<bizflag>0</bizflag>\n\t<pua>1</pua>\n\t<eggIncluded>1</eggIncluded>\n\t<silence>1</silence>\n\t<membercount>144</membercount>\n\t<signature>N0_V1_GTABZ5WF|v1_viArxPvt</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'NewMsgId': 6393368103504785974, 'MsgSeq': 871404395}
2025-07-28 07:14:39 | INFO | 收到文本消息: 消息ID:1907778689 来自:27852221909@chatroom 发送人:wangchunmeng7291 @:['wxid_ctp9qffuf14b21'] 内容:@Z⁰ 早啊 老大[坏笑]
2025-07-28 07:14:39 | DEBUG | 处理消息内容: '@Z⁰ 早啊 老大[坏笑]'
2025-07-28 07:14:39 | DEBUG | 消息内容 '@Z⁰ 早啊 老大[坏笑]' 不匹配任何命令，忽略
2025-07-28 07:15:10 | DEBUG | 收到消息: {'MsgId': 110174684, 'FromUserName': {'string': '27852221909@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 1, 'Content': {'string': 'wxid_ctp9qffuf14b21:\n爬不起来。好困🥱'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1753658120, 'MsgSource': '<msgsource>\n\t<pua>1</pua>\n\t<eggIncluded>1</eggIncluded>\n\t<silence>1</silence>\n\t<membercount>144</membercount>\n\t<signature>N0_V1_3l2830iJ|v1_syTzVIxS</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'NewMsgId': 3263244904600869567, 'MsgSeq': 871404396}
2025-07-28 07:15:10 | INFO | 收到文本消息: 消息ID:110174684 来自:27852221909@chatroom 发送人:wxid_ctp9qffuf14b21 @:[] 内容:爬不起来。好困🥱
2025-07-28 07:15:10 | DEBUG | 处理消息内容: '爬不起来。好困🥱'
2025-07-28 07:15:10 | DEBUG | 消息内容 '爬不起来。好困🥱' 不匹配任何命令，忽略
2025-07-28 07:15:12 | DEBUG | 收到消息: {'MsgId': 1138689762, 'FromUserName': {'string': '48097389945@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 1, 'Content': {'string': 'last--exile:\n。。。。。'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1753658120, 'MsgSource': '<msgsource>\n\t<pua>1</pua>\n\t<silence>0</silence>\n\t<membercount>73</membercount>\n\t<signature>N0_V1_etr+j6Px|v1_Gr3vVYDx</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'PushContent': '亮 : 。。。。。', 'NewMsgId': 7253773409096027763, 'MsgSeq': 871404397}
2025-07-28 07:15:12 | INFO | 收到文本消息: 消息ID:1138689762 来自:48097389945@chatroom 发送人:last--exile @:[] 内容:。。。。。
2025-07-28 07:15:12 | DEBUG | 处理消息内容: '。。。。。'
2025-07-28 07:15:12 | DEBUG | 消息内容 '。。。。。' 不匹配任何命令，忽略
2025-07-28 07:15:29 | DEBUG | 收到消息: {'MsgId': 1085848555, 'FromUserName': {'string': '27852221909@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 1, 'Content': {'string': 'wangchunmeng7291:\n@55\u2005送了，小猴子'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1753658140, 'MsgSource': '<msgsource>\n\t<atuserlist>wxid_z9qsv7556b7822</atuserlist>\n\t<bizflag>0</bizflag>\n\t<pua>1</pua>\n\t<eggIncluded>1</eggIncluded>\n\t<silence>1</silence>\n\t<membercount>144</membercount>\n\t<signature>N0_V1_5OXmY4dK|v1_2NZUYdqs</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'NewMsgId': 7924708321230024335, 'MsgSeq': 871404398}
2025-07-28 07:15:29 | INFO | 收到文本消息: 消息ID:1085848555 来自:27852221909@chatroom 发送人:wangchunmeng7291 @:['wxid_z9qsv7556b7822'] 内容:@55 送了，小猴子
2025-07-28 07:15:29 | DEBUG | 处理消息内容: '@55 送了，小猴子'
2025-07-28 07:15:29 | DEBUG | 消息内容 '@55 送了，小猴子' 不匹配任何命令，忽略
2025-07-28 07:15:37 | DEBUG | 收到消息: {'MsgId': 2142532485, 'FromUserName': {'string': '27852221909@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 1, 'Content': {'string': 'wxid_z9qsv7556b7822:\nOKOK'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1753658146, 'MsgSource': '<msgsource>\n\t<bizflag>0</bizflag>\n\t<pua>1</pua>\n\t<eggIncluded>1</eggIncluded>\n\t<silence>1</silence>\n\t<membercount>144</membercount>\n\t<signature>N0_V1_mqpI0IH6|v1_8o0EuMYZ</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'NewMsgId': 844030161927552007, 'MsgSeq': 871404399}
2025-07-28 07:15:37 | INFO | 收到文本消息: 消息ID:2142532485 来自:27852221909@chatroom 发送人:wxid_z9qsv7556b7822 @:[] 内容:OKOK
2025-07-28 07:15:37 | DEBUG | 处理消息内容: 'OKOK'
2025-07-28 07:15:37 | DEBUG | 消息内容 'OKOK' 不匹配任何命令，忽略
2025-07-28 07:15:43 | DEBUG | 收到消息: {'MsgId': 1423180450, 'FromUserName': {'string': '27852221909@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 49, 'Content': {'string': 'wxid_z9qsv7556b7822:\n<?xml version="1.0"?>\n<msg>\n\t<appmsg appid="" sdkver="0">\n\t\t<title />\n\t\t<type>8</type>\n\t\t<appattach>\n\t\t\t<totallen>1221019</totallen>\n\t\t\t<emoticonmd5>98f3acd63f2a96f515b823c9be044650</emoticonmd5>\n\t\t\t<fileext>pic</fileext>\n\t\t\t<attachid>0:0:98f3acd63f2a96f515b823c9be044650</attachid>\n\t\t\t<cdnthumbaeskey>636d79726a65666c7766706c77686a74</cdnthumbaeskey>\n\t\t\t<aeskey />\n\t\t\t<cdnthumburl>3057020100044b304902010002040923b11202033d11fe020427f4039902046885749d042433636330643437302d366139662d343033662d396463362d3337396662343461653061630204052408030201000405004c55ce008b2b1a5b</cdnthumburl>\n\t\t\t<cdnthumblength>10104</cdnthumblength>\n\t\t\t<cdnthumbwidth>273</cdnthumbwidth>\n\t\t\t<cdnthumbheight>266</cdnthumbheight>\n\t\t\t<cdnthumbmd5>12ded54520fabd0c19f83bf4f7d53e90</cdnthumbmd5>\n\t\t\t<emojiinfo>CiA5OGYzYWNkNjNmMmE5NmY1MTViODIzYzliZTA0NDY1MBLEAmh0dHA6Ly92d2VpeGluZi50Yy5xcS5jb20vMTEwLzIwNDAxL3N0b2Rvd25sb2FkP209OThmM2FjZDYzZjJhOTZmNTE1YjgyM2M5YmUwNDQ2NTAmZmlsZWtleT0zMDQ0MDIwMTAxMDQzMDMwMmUwMjAxNmUwNDAyNTM1YTA0MjAzOTM4NjYzMzYxNjM2NDM2MzM2NjMyNjEzOTM2NjYzNTMxMzU2MjM4MzIzMzYzMzk2MjY1MzAzNDM0MzYzNTMwMDIwMzEyYTE5YjA0MGQwMDAwMDAwNDYyNzQ2NjczMDAwMDAwMDEzMiZoeT1TWiZzdG9yZWlkPTI2NWQ4NzRjMDAwMGFkNjM0NGE4NTgwZWIwMDAwMDA2ZTAxMDA0ZmIxNTM1YTExMGI3MTgxNTY3YzlkZTI1JmVmPTEmYml6aWQ9MTAyMirEAmh0dHA6Ly92d2VpeGluZi50Yy5xcS5jb20vMTEwLzIwNDAyL3N0b2Rvd25sb2FkP209Y2JkMDdiMjNmYzAxNGM5YTViM2U0NzJiNTAyYzRmZGEmZmlsZWtleT0zMDQ0MDIwMTAxMDQzMDMwMmUwMjAxNmUwNDAyNTM1YTA0MjA2MzYyNjQzMDM3NjIzMjMzNjY2MzMwMzEzNDYzMzk2MTM1NjIzMzY1MzQzNzMyNjIzNTMwMzI2MzM0NjY2NDYxMDIwMzEyYTFhMDA0MGQwMDAwMDAwNDYyNzQ2NjczMDAwMDAwMDEzMiZoeT1TWiZzdG9yZWlkPTI2NWQ4NzRjMDAwMGM3NmQ0NGE4NTgwZWIwMDAwMDA2ZTAyMDA0ZmIyNTM1YTExMGI3MTgxNTY3YzlkZTM4JmVmPTImYml6aWQ9MTAyMjIgYjUwOTZkMzhjMDhmNDNiMDliMzljMGU3ZTIxZjU5NGI6AELEAmh0dHA6Ly92d2VpeGluZi50Yy5xcS5jb20vMTEwLzIwNDAzL3N0b2Rvd25sb2FkP209NzIyYzkzNjE1YmZkMzY5NDU4NjdhNDhlZTEzNzQ4NGUmZmlsZWtleT0zMDQ0MDIwMTAxMDQzMDMwMmUwMjAxNmUwNDAyNTM1YTA0MjAzNzMyMzI2MzM5MzMzNjMxMzU2MjY2NjQzMzM2MzkzNDM1MzgzNjM3NjEzNDM4NjU2NTMxMzMzNzM0MzgzNDY1MDIwMzAyMWI2MDA0MGQwMDAwMDAwNDYyNzQ2NjczMDAwMDAwMDEzMiZoeT1TWiZzdG9yZWlkPTI2NWQ4NzRjMDAwMGU0MjI5NGE4NTgwZWIwMDAwMDA2ZTAzMDA0ZmIzNTM1YTExMGI3MTgxNTY3YzlkZTQ2JmVmPTMmYml6aWQ9MTAyMkogOTk4YzM5N2M5ODllOWJlNzQ1N2RjNGU4MTgzYzEzYzlaAGIAagCCAQA=</emojiinfo>\n\t\t</appattach>\n\t\t<percent>99</percent>\n\t</appmsg><extcommoninfo></extcommoninfo>\n\t<fromusername>wxid_z9qsv7556b7822</fromusername>\n\t<scene>0</scene>\n\t<appinfo>\n\t\t<version>1</version>\n\t\t<appname></appname>\n\t</appinfo>\n\t<commenturl></commenturl>\n</msg>\n'}, 'Status': 3, 'ImgStatus': 2, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1753658153, 'MsgSource': '<msgsource>\n\t<sec_msg_node>\n\t\t<uuid>fa81bb334a5c280c9bd3e5e4438fb4c7_</uuid>\n\t\t<risk-file-flag />\n\t\t<risk-file-md5-list />\n\t</sec_msg_node>\n\t<silence>1</silence>\n\t<membercount>144</membercount>\n\t<signature>N0_V1_iqn4PD2b|v1_l00DpUw6</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'NewMsgId': 1189840089693055676, 'MsgSeq': 871404400}
2025-07-28 07:15:43 | DEBUG | 从群聊消息中提取发送者: wxid_z9qsv7556b7822
2025-07-28 07:15:43 | DEBUG | XML消息完整内容:
<?xml version="1.0"?>
<msg>
	<appmsg appid="" sdkver="0">
		<title />
		<type>8</type>
		<appattach>
			<totallen>1221019</totallen>
			<emoticonmd5>98f3acd63f2a96f515b823c9be044650</emoticonmd5>
			<fileext>pic</fileext>
			<attachid>0:0:98f3acd63f2a96f515b823c9be044650</attachid>
			<cdnthumbaeskey>636d79726a65666c7766706c77686a74</cdnthumbaeskey>
			<aeskey />
			<cdnthumburl>3057020100044b304902010002040923b11202033d11fe020427f4039902046885749d042433636330643437302d366139662d343033662d396463362d3337396662343461653061630204052408030201000405004c55ce008b2b1a5b</cdnthumburl>
			<cdnthumblength>10104</cdnthumblength>
			<cdnthumbwidth>273</cdnthumbwidth>
			<cdnthumbheight>266</cdnthumbheight>
			<cdnthumbmd5>12ded54520fabd0c19f83bf4f7d53e90</cdnthumbmd5>
			<emojiinfo>CiA5OGYzYWNkNjNmMmE5NmY1MTViODIzYzliZTA0NDY1MBLEAmh0dHA6Ly92d2VpeGluZi50Yy5xcS5jb20vMTEwLzIwNDAxL3N0b2Rvd25sb2FkP209OThmM2FjZDYzZjJhOTZmNTE1YjgyM2M5YmUwNDQ2NTAmZmlsZWtleT0zMDQ0MDIwMTAxMDQzMDMwMmUwMjAxNmUwNDAyNTM1YTA0MjAzOTM4NjYzMzYxNjM2NDM2MzM2NjMyNjEzOTM2NjYzNTMxMzU2MjM4MzIzMzYzMzk2MjY1MzAzNDM0MzYzNTMwMDIwMzEyYTE5YjA0MGQwMDAwMDAwNDYyNzQ2NjczMDAwMDAwMDEzMiZoeT1TWiZzdG9yZWlkPTI2NWQ4NzRjMDAwMGFkNjM0NGE4NTgwZWIwMDAwMDA2ZTAxMDA0ZmIxNTM1YTExMGI3MTgxNTY3YzlkZTI1JmVmPTEmYml6aWQ9MTAyMirEAmh0dHA6Ly92d2VpeGluZi50Yy5xcS5jb20vMTEwLzIwNDAyL3N0b2Rvd25sb2FkP209Y2JkMDdiMjNmYzAxNGM5YTViM2U0NzJiNTAyYzRmZGEmZmlsZWtleT0zMDQ0MDIwMTAxMDQzMDMwMmUwMjAxNmUwNDAyNTM1YTA0MjA2MzYyNjQzMDM3NjIzMjMzNjY2MzMwMzEzNDYzMzk2MTM1NjIzMzY1MzQzNzMyNjIzNTMwMzI2MzM0NjY2NDYxMDIwMzEyYTFhMDA0MGQwMDAwMDAwNDYyNzQ2NjczMDAwMDAwMDEzMiZoeT1TWiZzdG9yZWlkPTI2NWQ4NzRjMDAwMGM3NmQ0NGE4NTgwZWIwMDAwMDA2ZTAyMDA0ZmIyNTM1YTExMGI3MTgxNTY3YzlkZTM4JmVmPTImYml6aWQ9MTAyMjIgYjUwOTZkMzhjMDhmNDNiMDliMzljMGU3ZTIxZjU5NGI6AELEAmh0dHA6Ly92d2VpeGluZi50Yy5xcS5jb20vMTEwLzIwNDAzL3N0b2Rvd25sb2FkP209NzIyYzkzNjE1YmZkMzY5NDU4NjdhNDhlZTEzNzQ4NGUmZmlsZWtleT0zMDQ0MDIwMTAxMDQzMDMwMmUwMjAxNmUwNDAyNTM1YTA0MjAzNzMyMzI2MzM5MzMzNjMxMzU2MjY2NjQzMzM2MzkzNDM1MzgzNjM3NjEzNDM4NjU2NTMxMzMzNzM0MzgzNDY1MDIwMzAyMWI2MDA0MGQwMDAwMDAwNDYyNzQ2NjczMDAwMDAwMDEzMiZoeT1TWiZzdG9yZWlkPTI2NWQ4NzRjMDAwMGU0MjI5NGE4NTgwZWIwMDAwMDA2ZTAzMDA0ZmIzNTM1YTExMGI3MTgxNTY3YzlkZTQ2JmVmPTMmYml6aWQ9MTAyMkogOTk4YzM5N2M5ODllOWJlNzQ1N2RjNGU4MTgzYzEzYzlaAGIAagCCAQA=</emojiinfo>
		</appattach>
		<percent>99</percent>
	</appmsg><extcommoninfo></extcommoninfo>
	<fromusername>wxid_z9qsv7556b7822</fromusername>
	<scene>0</scene>
	<appinfo>
		<version>1</version>
		<appname></appname>
	</appinfo>
	<commenturl></commenturl>
</msg>

2025-07-28 07:15:43 | DEBUG | XML消息类型: 8
2025-07-28 07:15:43 | DEBUG | XML消息标题: None
2025-07-28 07:15:43 | DEBUG | 附件信息 totallen: 1221019
2025-07-28 07:15:43 | DEBUG | 附件信息 emoticonmd5: 98f3acd63f2a96f515b823c9be044650
2025-07-28 07:15:43 | DEBUG | 附件信息 fileext: pic
2025-07-28 07:15:43 | DEBUG | 附件信息 attachid: 0:0:98f3acd63f2a96f515b823c9be044650
2025-07-28 07:15:43 | DEBUG | 附件信息 cdnthumbaeskey: 636d79726a65666c7766706c77686a74
2025-07-28 07:15:43 | DEBUG | 附件信息 cdnthumburl: 3057020100044b304902010002040923b11202033d11fe020427f4039902046885749d042433636330643437302d366139662d343033662d396463362d3337396662343461653061630204052408030201000405004c55ce008b2b1a5b
2025-07-28 07:15:43 | DEBUG | 附件信息 cdnthumblength: 10104
2025-07-28 07:15:43 | DEBUG | 附件信息 cdnthumbwidth: 273
2025-07-28 07:15:43 | DEBUG | 附件信息 cdnthumbheight: 266
2025-07-28 07:15:43 | DEBUG | 附件信息 cdnthumbmd5: 12ded54520fabd0c19f83bf4f7d53e90
2025-07-28 07:15:43 | DEBUG | 附件信息 emojiinfo: CiA5OGYzYWNkNjNmMmE5NmY1MTViODIzYzliZTA0NDY1MBLEAmh0dHA6Ly92d2VpeGluZi50Yy5xcS5jb20vMTEwLzIwNDAxL3N0b2Rvd25sb2FkP209OThmM2FjZDYzZjJhOTZmNTE1YjgyM2M5YmUwNDQ2NTAmZmlsZWtleT0zMDQ0MDIwMTAxMDQzMDMwMmUwMjAxNmUwNDAyNTM1YTA0MjAzOTM4NjYzMzYxNjM2NDM2MzM2NjMyNjEzOTM2NjYzNTMxMzU2MjM4MzIzMzYzMzk2MjY1MzAzNDM0MzYzNTMwMDIwMzEyYTE5YjA0MGQwMDAwMDAwNDYyNzQ2NjczMDAwMDAwMDEzMiZoeT1TWiZzdG9yZWlkPTI2NWQ4NzRjMDAwMGFkNjM0NGE4NTgwZWIwMDAwMDA2ZTAxMDA0ZmIxNTM1YTExMGI3MTgxNTY3YzlkZTI1JmVmPTEmYml6aWQ9MTAyMirEAmh0dHA6Ly92d2VpeGluZi50Yy5xcS5jb20vMTEwLzIwNDAyL3N0b2Rvd25sb2FkP209Y2JkMDdiMjNmYzAxNGM5YTViM2U0NzJiNTAyYzRmZGEmZmlsZWtleT0zMDQ0MDIwMTAxMDQzMDMwMmUwMjAxNmUwNDAyNTM1YTA0MjA2MzYyNjQzMDM3NjIzMjMzNjY2MzMwMzEzNDYzMzk2MTM1NjIzMzY1MzQzNzMyNjIzNTMwMzI2MzM0NjY2NDYxMDIwMzEyYTFhMDA0MGQwMDAwMDAwNDYyNzQ2NjczMDAwMDAwMDEzMiZoeT1TWiZzdG9yZWlkPTI2NWQ4NzRjMDAwMGM3NmQ0NGE4NTgwZWIwMDAwMDA2ZTAyMDA0ZmIyNTM1YTExMGI3MTgxNTY3YzlkZTM4JmVmPTImYml6aWQ9MTAyMjIgYjUwOTZkMzhjMDhmNDNiMDliMzljMGU3ZTIxZjU5NGI6AELEAmh0dHA6Ly92d2VpeGluZi50Yy5xcS5jb20vMTEwLzIwNDAzL3N0b2Rvd25sb2FkP209NzIyYzkzNjE1YmZkMzY5NDU4NjdhNDhlZTEzNzQ4NGUmZmlsZWtleT0zMDQ0MDIwMTAxMDQzMDMwMmUwMjAxNmUwNDAyNTM1YTA0MjAzNzMyMzI2MzM5MzMzNjMxMzU2MjY2NjQzMzM2MzkzNDM1MzgzNjM3NjEzNDM4NjU2NTMxMzMzNzM0MzgzNDY1MDIwMzAyMWI2MDA0MGQwMDAwMDAwNDYyNzQ2NjczMDAwMDAwMDEzMiZoeT1TWiZzdG9yZWlkPTI2NWQ4NzRjMDAwMGU0MjI5NGE4NTgwZWIwMDAwMDA2ZTAzMDA0ZmIzNTM1YTExMGI3MTgxNTY3YzlkZTQ2JmVmPTMmYml6aWQ9MTAyMkogOTk4YzM5N2M5ODllOWJlNzQ1N2RjNGU4MTgzYzEzYzlaAGIAagCCAQA=
2025-07-28 07:15:43 | INFO | 未知的XML消息类型: 8
2025-07-28 07:15:43 | INFO | 消息标题: None
2025-07-28 07:15:43 | INFO | 消息描述: N/A
2025-07-28 07:15:43 | INFO | 消息URL: N/A
2025-07-28 07:15:43 | INFO | 完整XML内容:
<?xml version="1.0"?>
<msg>
	<appmsg appid="" sdkver="0">
		<title />
		<type>8</type>
		<appattach>
			<totallen>1221019</totallen>
			<emoticonmd5>98f3acd63f2a96f515b823c9be044650</emoticonmd5>
			<fileext>pic</fileext>
			<attachid>0:0:98f3acd63f2a96f515b823c9be044650</attachid>
			<cdnthumbaeskey>636d79726a65666c7766706c77686a74</cdnthumbaeskey>
			<aeskey />
			<cdnthumburl>3057020100044b304902010002040923b11202033d11fe020427f4039902046885749d042433636330643437302d366139662d343033662d396463362d3337396662343461653061630204052408030201000405004c55ce008b2b1a5b</cdnthumburl>
			<cdnthumblength>10104</cdnthumblength>
			<cdnthumbwidth>273</cdnthumbwidth>
			<cdnthumbheight>266</cdnthumbheight>
			<cdnthumbmd5>12ded54520fabd0c19f83bf4f7d53e90</cdnthumbmd5>
			<emojiinfo>CiA5OGYzYWNkNjNmMmE5NmY1MTViODIzYzliZTA0NDY1MBLEAmh0dHA6Ly92d2VpeGluZi50Yy5xcS5jb20vMTEwLzIwNDAxL3N0b2Rvd25sb2FkP209OThmM2FjZDYzZjJhOTZmNTE1YjgyM2M5YmUwNDQ2NTAmZmlsZWtleT0zMDQ0MDIwMTAxMDQzMDMwMmUwMjAxNmUwNDAyNTM1YTA0MjAzOTM4NjYzMzYxNjM2NDM2MzM2NjMyNjEzOTM2NjYzNTMxMzU2MjM4MzIzMzYzMzk2MjY1MzAzNDM0MzYzNTMwMDIwMzEyYTE5YjA0MGQwMDAwMDAwNDYyNzQ2NjczMDAwMDAwMDEzMiZoeT1TWiZzdG9yZWlkPTI2NWQ4NzRjMDAwMGFkNjM0NGE4NTgwZWIwMDAwMDA2ZTAxMDA0ZmIxNTM1YTExMGI3MTgxNTY3YzlkZTI1JmVmPTEmYml6aWQ9MTAyMirEAmh0dHA6Ly92d2VpeGluZi50Yy5xcS5jb20vMTEwLzIwNDAyL3N0b2Rvd25sb2FkP209Y2JkMDdiMjNmYzAxNGM5YTViM2U0NzJiNTAyYzRmZGEmZmlsZWtleT0zMDQ0MDIwMTAxMDQzMDMwMmUwMjAxNmUwNDAyNTM1YTA0MjA2MzYyNjQzMDM3NjIzMjMzNjY2MzMwMzEzNDYzMzk2MTM1NjIzMzY1MzQzNzMyNjIzNTMwMzI2MzM0NjY2NDYxMDIwMzEyYTFhMDA0MGQwMDAwMDAwNDYyNzQ2NjczMDAwMDAwMDEzMiZoeT1TWiZzdG9yZWlkPTI2NWQ4NzRjMDAwMGM3NmQ0NGE4NTgwZWIwMDAwMDA2ZTAyMDA0ZmIyNTM1YTExMGI3MTgxNTY3YzlkZTM4JmVmPTImYml6aWQ9MTAyMjIgYjUwOTZkMzhjMDhmNDNiMDliMzljMGU3ZTIxZjU5NGI6AELEAmh0dHA6Ly92d2VpeGluZi50Yy5xcS5jb20vMTEwLzIwNDAzL3N0b2Rvd25sb2FkP209NzIyYzkzNjE1YmZkMzY5NDU4NjdhNDhlZTEzNzQ4NGUmZmlsZWtleT0zMDQ0MDIwMTAxMDQzMDMwMmUwMjAxNmUwNDAyNTM1YTA0MjAzNzMyMzI2MzM5MzMzNjMxMzU2MjY2NjQzMzM2MzkzNDM1MzgzNjM3NjEzNDM4NjU2NTMxMzMzNzM0MzgzNDY1MDIwMzAyMWI2MDA0MGQwMDAwMDAwNDYyNzQ2NjczMDAwMDAwMDEzMiZoeT1TWiZzdG9yZWlkPTI2NWQ4NzRjMDAwMGU0MjI5NGE4NTgwZWIwMDAwMDA2ZTAzMDA0ZmIzNTM1YTExMGI3MTgxNTY3YzlkZTQ2JmVmPTMmYml6aWQ9MTAyMkogOTk4YzM5N2M5ODllOWJlNzQ1N2RjNGU4MTgzYzEzYzlaAGIAagCCAQA=</emojiinfo>
		</appattach>
		<percent>99</percent>
	</appmsg><extcommoninfo></extcommoninfo>
	<fromusername>wxid_z9qsv7556b7822</fromusername>
	<scene>0</scene>
	<appinfo>
		<version>1</version>
		<appname></appname>
	</appinfo>
	<commenturl></commenturl>
</msg>

2025-07-28 07:16:35 | DEBUG | 收到消息: {'MsgId': *********, 'FromUserName': {'string': '27852221909@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 1, 'Content': {'string': 'wangchunmeng7291:\n@Z⁰\u2005一看昨天就没少喝[偷笑]'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1753658205, 'MsgSource': '<msgsource>\n\t<atuserlist>wxid_ctp9qffuf14b21</atuserlist>\n\t<bizflag>0</bizflag>\n\t<pua>1</pua>\n\t<eggIncluded>1</eggIncluded>\n\t<silence>1</silence>\n\t<membercount>144</membercount>\n\t<signature>N0_V1_4bxeIFTO|v1_AZv/rh+s</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'NewMsgId': 6177721054902098967, 'MsgSeq': 871404401}
2025-07-28 07:16:35 | INFO | 收到文本消息: 消息ID:********* 来自:27852221909@chatroom 发送人:wangchunmeng7291 @:['wxid_ctp9qffuf14b21'] 内容:@Z⁰ 一看昨天就没少喝[偷笑]
2025-07-28 07:16:35 | DEBUG | 处理消息内容: '@Z⁰ 一看昨天就没少喝[偷笑]'
2025-07-28 07:16:35 | DEBUG | 消息内容 '@Z⁰ 一看昨天就没少喝[偷笑]' 不匹配任何命令，忽略
2025-07-28 07:17:06 | DEBUG | 收到消息: {'MsgId': 377778751, 'FromUserName': {'string': '27852221909@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 47, 'Content': {'string': 'wxid_ctp9qffuf14b21:\n<msg><emoji fromusername = "wxid_ctp9qffuf14b21" tousername = "27852221909@chatroom" type="1" idbuffer="media:0_0" md5="f35e1b8d29f6fef685f0e7a82737c6c9" len = "38245" productid="com.tencent.xin.emoticon.person.stiker_16100205125c25d30dfc61d84e" androidmd5="f35e1b8d29f6fef685f0e7a82737c6c9" androidlen="38245" s60v3md5 = "f35e1b8d29f6fef685f0e7a82737c6c9" s60v3len="38245" s60v5md5 = "f35e1b8d29f6fef685f0e7a82737c6c9" s60v5len="38245" cdnurl = "http://wxapp.tc.qq.com/275/20304/stodownload?m=f35e1b8d29f6fef685f0e7a82737c6c9&amp;filekey=30350201010421301f02020113040253480410f35e1b8d29f6fef685f0e7a82737c6c90203009565040d00000004627466730000000132&amp;hy=SH&amp;storeid=26479b8b50004c1c0000000000000011300004f505348129438b0b68620318&amp;bizid=1023" designerid = "" thumburl = "http://wxapp.tc.qq.com/275/20304/stodownload?m=8be1ec680596d64350d1caedb03f104d&amp;filekey=30340201010420301e020201130402534804108be1ec680596d64350d1caedb03f104d02027df7040d00000004627466730000000132&amp;hy=SH&amp;storeid=26479b8b50006ca74000000000000011300004f505348212188e0b69ad7889&amp;bizid=1023" encrypturl = "http://wxapp.tc.qq.com/262/20304/stodownload?m=639e58ca601b173f75ae0aaf549149d9&amp;filekey=30350201010421301f02020106040253480410639e58ca601b173f75ae0aaf549149d90203009570040d00000004627466730000000132&amp;hy=SH&amp;storeid=2632319af00040edd000000000000010600004f50534813cd2a00b0f5f0fed&amp;bizid=1023" aeskey= "1a506785e3994cf936e3f8dbca49c940" externurl = "http://wxapp.tc.qq.com/262/20304/stodownload?m=e015c7689f87df68f4fe7f4164d670ce&amp;filekey=30340201010420301e02020106040253480410e015c7689f87df68f4fe7f4164d670ce020258b0040d00000004627466730000000132&amp;hy=SH&amp;storeid=2632319af00064fa4000000000000010600004f5053481ec67b40b7cc2f1ff&amp;bizid=1023" externmd5 = "ac99194651169fa317628c699ad2436e" width= "240" height= "240" tpurl= "" tpauthkey= "" attachedtext= "" attachedtextcolor= "" lensid= "" emojiattr= "CgblkbzlkbwQvvvugAY=" linkid= "" desc= "Cg8KBXpoX2NuEgblkbzlkbwKCQoFemhfdHcSAAoLCgdkZWZhdWx0EgA=" ></emoji>  </msg>'}, 'Status': 3, 'ImgStatus': 2, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1753658236, 'MsgSource': '<msgsource>\n\t<silence>1</silence>\n\t<membercount>144</membercount>\n\t<signature>N0_V1_HzkbASSS|v1_ufe0yu5+</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'NewMsgId': 3246165649835203333, 'MsgSeq': 871404402}
2025-07-28 07:17:06 | INFO | 收到表情消息: 消息ID:377778751 来自:27852221909@chatroom 发送人:wxid_ctp9qffuf14b21 MD5:f35e1b8d29f6fef685f0e7a82737c6c9 大小:38245
2025-07-28 07:17:06 | DEBUG | [DoubaoImageRecognition] 缓存表情消息: 3246165649835203333
2025-07-28 07:17:36 | DEBUG | 收到消息: {'MsgId': 23943152, 'FromUserName': {'string': '27852221909@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 47, 'Content': {'string': 'wangchunmeng7291:\n<msg><emoji fromusername = "wangchunmeng7291" tousername = "27852221909@chatroom" type="2" idbuffer="media:0_0" md5="6362afd66a5f14cbf0d90b741a69f881" len = "65265" productid="" androidmd5="6362afd66a5f14cbf0d90b741a69f881" androidlen="65265" s60v3md5 = "6362afd66a5f14cbf0d90b741a69f881" s60v3len="65265" s60v5md5 = "6362afd66a5f14cbf0d90b741a69f881" s60v5len="65265" cdnurl = "http://vweixinf.tc.qq.com/110/20401/stodownload?m=6362afd66a5f14cbf0d90b741a69f881&amp;filekey=30440201010430302e02016e0402534804203633363261666436366135663134636266306439306237343161363966383831020300fef1040d00000004627466730000000132&amp;hy=SH&amp;storeid=268592a470009ff5020221aad0000006e01004fb153481f43d1f1570697072&amp;ef=1&amp;bizid=1022" designerid = "" thumburl = "" encrypturl = "http://vweixinf.tc.qq.com/110/20402/stodownload?m=73ae33196ddc2bfec67463caa0dacd37&amp;filekey=30440201010430302e02016e0402534804203733616533333139366464633262666563363734363363616130646163643337020300ff00040d00000004627466730000000132&amp;hy=SH&amp;storeid=268592a47000b65c520221aad0000006e02004fb253481f43d1f1570697087&amp;ef=2&amp;bizid=1022" aeskey= "9135bf691d314990967080eb9ce80581" externurl = "http://vweixinf.tc.qq.com/110/20403/stodownload?m=3e78a7709a94a724b2ef451809683643&amp;filekey=3043020101042f302d02016e040253480420336537386137373039613934613732346232656634353138303936383336343302024dd0040d00000004627466730000000132&amp;hy=SH&amp;storeid=268592a47000cca9520221aad0000006e03004fb353481f43d1f1570697099&amp;ef=3&amp;bizid=1022" externmd5 = "5786ef455e129395a25af3bfbbfec37b" width= "440" height= "440" tpurl= "" tpauthkey= "" attachedtext= "" attachedtextcolor= "" lensid= "" emojiattr= "" linkid= "" desc= "" ></emoji> <gameext type="0" content="0" ></gameext> </msg>'}, 'Status': 3, 'ImgStatus': 2, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1753658266, 'MsgSource': '<msgsource>\n\t<silence>1</silence>\n\t<membercount>144</membercount>\n\t<signature>N0_V1_L425WjlP|v1_Ggs5T9eX</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'NewMsgId': 5881832679455257264, 'MsgSeq': 871404403}
2025-07-28 07:17:36 | INFO | 收到表情消息: 消息ID:23943152 来自:27852221909@chatroom 发送人:wangchunmeng7291 MD5:6362afd66a5f14cbf0d90b741a69f881 大小:65265
2025-07-28 07:17:36 | DEBUG | [DoubaoImageRecognition] 缓存表情消息: 5881832679455257264
2025-07-28 07:17:57 | DEBUG | 收到消息: {'MsgId': 86600946, 'FromUserName': {'string': '48097389945@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 47, 'Content': {'string': 'wxid_n5c0aekjceu621:\n<msg><emoji fromusername = "wxid_n5c0aekjceu621" tousername = "48097389945@chatroom" type="1" idbuffer="media:0_0" md5="684e456ce967da12cac8afd5dd2247c8" len = "2626337" productid="" androidmd5="684e456ce967da12cac8afd5dd2247c8" androidlen="2626337" s60v3md5 = "684e456ce967da12cac8afd5dd2247c8" s60v3len="2626337" s60v5md5 = "684e456ce967da12cac8afd5dd2247c8" s60v5len="2626337" cdnurl = "http://vweixinf.tc.qq.com/110/20402/stodownload?m=684e456ce967da12cac8afd5dd2247c8&amp;filekey=30440201010430302e02016e0402535a042036383465343536636539363764613132636163386166643564643232343763380203281321040d00000004627466730000000131&amp;hy=SZ&amp;storeid=323032303033333130373335323430303032666164376463396566376366316165663566303930303030303036653031303034666232&amp;ef=1&amp;bizid=1022" designerid = "" thumburl = "" encrypturl = "http://vweixinf.tc.qq.com/110/20402/stodownload?m=bc7071b24ccc4340dbdf2842ac6abfd0&amp;filekey=30440201010430302e02016e0402535a042062633730373162323463636334333430646264663238343261633661626664300203281330040d00000004627466730000000131&amp;hy=SZ&amp;storeid=323032303033333130373335323430303062623566306463396566376366316165663566303930303030303036653032303034666232&amp;ef=2&amp;bizid=1022" aeskey= "d28260dba63e4aed906b7b3c2fe0c3dc" externurl = "http://vweixinf.tc.qq.com/110/20403/stodownload?m=1e3e67f8c4159b5f2ab6e587aa5b9277&amp;filekey=30440201010430302e02016e0402535a042031653365363766386334313539623566326162366535383761613562393237370203027fb0040d00000004627466730000000131&amp;hy=SZ&amp;storeid=323032303033333130373335323530303035616632666463396566376366316165663566303930303030303036653033303034666233&amp;ef=3&amp;bizid=1022" externmd5 = "1af109a2cd0947c5f3cf2546a6178f21" width= "320" height= "320" tpurl= "" tpauthkey= "" attachedtext= "早上好" attachedtextcolor= "FFFFFF" lensid= "" emojiattr= "Cgnml6nkuIrlpb0=" linkid= "" desc= "" activityid = "Selfie: a0abb60893062c6eb4f8daaa69ad3a8f" ></emoji>  </msg>'}, 'Status': 3, 'ImgStatus': 2, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1753658287, 'MsgSource': '<msgsource>\n\t<silence>0</silence>\n\t<membercount>73</membercount>\n\t<signature>N0_V1_ptMXfPX7|v1_tcab21b5</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'PushContent': 'ㅤㅤㅤㅤㅤㅤㅤㅤㅤㅤㅤㅤㅤㅤㅤㅤ在群聊中发了一个表情', 'NewMsgId': 9097007933127704932, 'MsgSeq': 871404404}
2025-07-28 07:17:57 | INFO | 收到表情消息: 消息ID:86600946 来自:48097389945@chatroom 发送人:wxid_n5c0aekjceu621 MD5:684e456ce967da12cac8afd5dd2247c8 大小:2626337
2025-07-28 07:17:57 | DEBUG | [DoubaoImageRecognition] 缓存表情消息: 9097007933127704932
2025-07-28 07:21:31 | DEBUG | 收到消息: {'MsgId': 1951127385, 'FromUserName': {'string': '27852221909@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 1, 'Content': {'string': 'wxid_ctp9qffuf14b21:\n出门上课去了，你睡吧@璟⃪訫⃪°\u2005'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1753658501, 'MsgSource': '<msgsource>\n\t<atuserlist><![CDATA[wangchunmeng7291]]></atuserlist>\n\t<pua>1</pua>\n\t<eggIncluded>1</eggIncluded>\n\t<silence>1</silence>\n\t<membercount>144</membercount>\n\t<signature>N0_V1_D71BGxKS|v1_eXCMZtuf</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'NewMsgId': 2241933165134093350, 'MsgSeq': 871404405}
2025-07-28 07:21:31 | INFO | 收到文本消息: 消息ID:1951127385 来自:27852221909@chatroom 发送人:wxid_ctp9qffuf14b21 @:['wangchunmeng7291'] 内容:出门上课去了，你睡吧@璟⃪訫⃪° 
2025-07-28 07:21:31 | DEBUG | 处理消息内容: '出门上课去了，你睡吧@璟⃪訫⃪°'
2025-07-28 07:21:31 | DEBUG | 消息内容 '出门上课去了，你睡吧@璟⃪訫⃪°' 不匹配任何命令，忽略
2025-07-28 07:22:47 | DEBUG | 收到消息: {'MsgId': 837127164, 'FromUserName': {'string': '48097389945@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 49, 'Content': {'string': 'wxid_e3o8s2nf9u2o22:\n<?xml version="1.0"?>\n<msg>\n\t<appmsg appid="wx3ba80a4a60c4329d" sdkver="0">\n\t\t<title>猜你喜欢～萝莉 双马尾 甜妹统治世界</title>\n\t\t<des>我发现了一条内容：【猜你喜欢～萝莉 双马...】，一起去看看。</des>\n\t\t<username />\n\t\t<action>view</action>\n\t\t<type>5</type>\n\t\t<showtype>0</showtype>\n\t\t<content />\n\t\t<url>https://m.externalcoop.com/a7500177263015903753/?a_l=1&amp;a_t=AzuC3V7qbc6mHxwJi66wNE18N5zakQe8Af54hv4U3QHg5misu2YAwVo3GP8fYciRXDeL6NrouURPWPP7k6FBJYjDtMALhF&amp;biz_log=BCNjrqxb6kXdztku1ypAQtjWRoZbKMNfPUfF5tHo8hyR1rzV66dujpwH4JwwU8NYBDGUfmbBbKEjtyPhMD4dWmAmyvgirQ6ubtjYBV8rFytWysSrRs9451MvgEFkbDQZbC5M96Mi8NW3f4jt&amp;crypt=9506&amp;dt=vivo+iQOO+Neo8+Pro&amp;group_id=7500177263015903753&amp;gy=68281fb90f3394df43f9f1d31bc1a5c4d08ee849c6bd987d2fd0cab27a55b1291c5a55c65ce18b56b0136c8f485efe85db97b1c44508b1455dfd823c9121055b397cd4988664389c9e104ad919183929b010f8c05330b8f4b0b72fcd270eb8fa78b1829055f11a22eed60187cbe2ea1e8469c8a76a143215deea917e8858ea77239b2f7467c06308113198f1a182fd2fd6f13f5c1d712561115a606f93e1414fd6465a96b9bb50bd8d4021d798fc2249fc7241393a84d5abd80ed90f2d9b3c64&amp;isNews=1&amp;label=share&amp;req_id=20250728072224AFE604B7BFF804F3491E&amp;utm_campaign=open&amp;utm_medium=webview&amp;utm_source=vivoliulanqi&amp;_appSource=ugcvideo&amp;sourceType=1</url>\n\t\t<lowurl />\n\t\t<forwardflag>0</forwardflag>\n\t\t<dataurl />\n\t\t<lowdataurl />\n\t\t<contentattr>0</contentattr>\n\t\t<streamvideo>\n\t\t\t<streamvideourl />\n\t\t\t<streamvideototaltime>0</streamvideototaltime>\n\t\t\t<streamvideotitle />\n\t\t\t<streamvideowording />\n\t\t\t<streamvideoweburl />\n\t\t\t<streamvideothumburl />\n\t\t\t<streamvideoaduxinfo />\n\t\t\t<streamvideopublishid />\n\t\t</streamvideo>\n\t\t<canvasPageItem>\n\t\t\t<canvasPageXml><![CDATA[]]></canvasPageXml>\n\t\t</canvasPageItem>\n\t\t<appattach>\n\t\t\t<attachid />\n\t\t\t<cdnthumburl>3057020100044b3049020100020435b9d70b02032f50e602041b4e8b7b02046886b4d1042431623638643164662d326539662d343735612d623235632d3761336637396465666366370204051808030201000405004c505600</cdnthumburl>\n\t\t\t<cdnthumbmd5>4dd30cb0b92901423fcdf216fe5fb564</cdnthumbmd5>\n\t\t\t<cdnthumblength>1011</cdnthumblength>\n\t\t\t<cdnthumbheight>200</cdnthumbheight>\n\t\t\t<cdnthumbwidth>200</cdnthumbwidth>\n\t\t\t<cdnthumbaeskey>178b1fc3f9785ac6a74824c857df9e07</cdnthumbaeskey>\n\t\t\t<aeskey>178b1fc3f9785ac6a74824c857df9e07</aeskey>\n\t\t\t<encryver>1</encryver>\n\t\t\t<fileext />\n\t\t\t<islargefilemsg>0</islargefilemsg>\n\t\t</appattach>\n\t\t<extinfo />\n\t\t<androidsource>2</androidsource>\n\t\t<thumburl />\n\t\t<mediatagname />\n\t\t<messageaction><![CDATA[]]></messageaction>\n\t\t<messageext><![CDATA[]]></messageext>\n\t\t<emoticongift>\n\t\t\t<packageflag>0</packageflag>\n\t\t\t<packageid />\n\t\t</emoticongift>\n\t\t<emoticonshared>\n\t\t\t<packageflag>0</packageflag>\n\t\t\t<packageid />\n\t\t</emoticonshared>\n\t\t<designershared>\n\t\t\t<designeruin>0</designeruin>\n\t\t\t<designername>null</designername>\n\t\t\t<designerrediretcturl><![CDATA[null]]></designerrediretcturl>\n\t\t</designershared>\n\t\t<emotionpageshared>\n\t\t\t<tid>0</tid>\n\t\t\t<title>null</title>\n\t\t\t<desc>null</desc>\n\t\t\t<iconUrl><![CDATA[null]]></iconUrl>\n\t\t\t<secondUrl>null</secondUrl>\n\t\t\t<pageType>0</pageType>\n\t\t\t<setKey>null</setKey>\n\t\t</emotionpageshared>\n\t\t<webviewshared>\n\t\t\t<shareUrlOriginal />\n\t\t\t<shareUrlOpen />\n\t\t\t<jsAppId />\n\t\t\t<publisherId />\n\t\t\t<publisherReqId />\n\t\t</webviewshared>\n\t\t<template_id />\n\t\t<md5>4dd30cb0b92901423fcdf216fe5fb564</md5>\n\t\t<websearch>\n\t\t\t<rec_category>0</rec_category>\n\t\t\t<channelId>0</channelId>\n\t\t</websearch>\n\t\t<weappinfo>\n\t\t\t<username />\n\t\t\t<appid />\n\t\t\t<appservicetype>0</appservicetype>\n\t\t\t<secflagforsinglepagemode>0</secflagforsinglepagemode>\n\t\t\t<videopageinfo>\n\t\t\t\t<thumbwidth>200</thumbwidth>\n\t\t\t\t<thumbheight>200</thumbheight>\n\t\t\t\t<fromopensdk>0</fromopensdk>\n\t\t\t</videopageinfo>\n\t\t</weappinfo>\n\t\t<statextstr>GhQKEnd4M2JhODBhNGE2MGM0MzI5ZA==</statextstr>\n\t\t<musicShareItem>\n\t\t\t<musicDuration>0</musicDuration>\n\t\t</musicShareItem>\n\t\t<finderLiveProductShare>\n\t\t\t<finderLiveID><![CDATA[]]></finderLiveID>\n\t\t\t<finderUsername><![CDATA[]]></finderUsername>\n\t\t\t<finderObjectID><![CDATA[]]></finderObjectID>\n\t\t\t<finderNonceID><![CDATA[]]></finderNonceID>\n\t\t\t<liveStatus><![CDATA[]]></liveStatus>\n\t\t\t<appId><![CDATA[]]></appId>\n\t\t\t<pagePath><![CDATA[]]></pagePath>\n\t\t\t<productId><![CDATA[]]></productId>\n\t\t\t<coverUrl><![CDATA[]]></coverUrl>\n\t\t\t<productTitle><![CDATA[]]></productTitle>\n\t\t\t<marketPrice><![CDATA[0]]></marketPrice>\n\t\t\t<sellingPrice><![CDATA[0]]></sellingPrice>\n\t\t\t<platformHeadImg><![CDATA[]]></platformHeadImg>\n\t\t\t<platformName><![CDATA[]]></platformName>\n\t\t\t<shopWindowId><![CDATA[]]></shopWindowId>\n\t\t\t<flashSalePrice><![CDATA[0]]></flashSalePrice>\n\t\t\t<flashSaleEndTime><![CDATA[0]]></flashSaleEndTime>\n\t\t\t<ecSource><![CDATA[]]></ecSource>\n\t\t\t<sellingPriceWording><![CDATA[]]></sellingPriceWording>\n\t\t\t<platformIconURL><![CDATA[]]></platformIconURL>\n\t\t\t<firstProductTagURL><![CDATA[]]></firstProductTagURL>\n\t\t\t<firstProductTagAspectRatioString><![CDATA[0.0]]></firstProductTagAspectRatioString>\n\t\t\t<secondProductTagURL><![CDATA[]]></secondProductTagURL>\n\t\t\t<secondProductTagAspectRatioString><![CDATA[0.0]]></secondProductTagAspectRatioString>\n\t\t\t<firstGuaranteeWording><![CDATA[]]></firstGuaranteeWording>\n\t\t\t<secondGuaranteeWording><![CDATA[]]></secondGuaranteeWording>\n\t\t\t<thirdGuaranteeWording><![CDATA[]]></thirdGuaranteeWording>\n\t\t\t<isPriceBeginShow>false</isPriceBeginShow>\n\t\t\t<lastGMsgID><![CDATA[]]></lastGMsgID>\n\t\t\t<promoterKey><![CDATA[]]></promoterKey>\n\t\t\t<discountWording><![CDATA[]]></discountWording>\n\t\t\t<priceSuffixDescription><![CDATA[]]></priceSuffixDescription>\n\t\t\t<productCardKey><![CDATA[]]></productCardKey>\n\t\t\t<isWxShop><![CDATA[]]></isWxShop>\n\t\t\t<brandIconUrl><![CDATA[]]></brandIconUrl>\n\t\t\t<showBoxItemStringList />\n\t\t</finderLiveProductShare>\n\t\t<finderOrder>\n\t\t\t<appID><![CDATA[]]></appID>\n\t\t\t<orderID><![CDATA[]]></orderID>\n\t\t\t<path><![CDATA[]]></path>\n\t\t\t<priceWording><![CDATA[]]></priceWording>\n\t\t\t<stateWording><![CDATA[]]></stateWording>\n\t\t\t<productImageURL><![CDATA[]]></productImageURL>\n\t\t\t<products><![CDATA[]]></products>\n\t\t\t<productsCount><![CDATA[0]]></productsCount>\n\t\t\t<orderType><![CDATA[0]]></orderType>\n\t\t\t<newPriceWording><![CDATA[]]></newPriceWording>\n\t\t\t<newStateWording><![CDATA[]]></newStateWording>\n\t\t\t<useNewWording><![CDATA[0]]></useNewWording>\n\t\t</finderOrder>\n\t\t<finderShopWindowShare>\n\t\t\t<finderUsername><![CDATA[]]></finderUsername>\n\t\t\t<avatar><![CDATA[]]></avatar>\n\t\t\t<nickname><![CDATA[]]></nickname>\n\t\t\t<commodityInStockCount><![CDATA[]]></commodityInStockCount>\n\t\t\t<appId><![CDATA[]]></appId>\n\t\t\t<path><![CDATA[]]></path>\n\t\t\t<appUsername><![CDATA[]]></appUsername>\n\t\t\t<query><![CDATA[]]></query>\n\t\t\t<liteAppId><![CDATA[]]></liteAppId>\n\t\t\t<liteAppPath><![CDATA[]]></liteAppPath>\n\t\t\t<liteAppQuery><![CDATA[]]></liteAppQuery>\n\t\t\t<platformTagURL><![CDATA[]]></platformTagURL>\n\t\t\t<saleWording><![CDATA[]]></saleWording>\n\t\t\t<lastGMsgID><![CDATA[]]></lastGMsgID>\n\t\t\t<profileTypeWording><![CDATA[]]></profileTypeWording>\n\t\t\t<saleWordingExtra><![CDATA[]]></saleWordingExtra>\n\t\t\t<isWxShop><![CDATA[]]></isWxShop>\n\t\t\t<platformIconUrl><![CDATA[]]></platformIconUrl>\n\t\t\t<brandIconUrl><![CDATA[]]></brandIconUrl>\n\t\t\t<description><![CDATA[]]></description>\n\t\t\t<backgroundUrl><![CDATA[]]></backgroundUrl>\n\t\t\t<darkModePlatformIconUrl><![CDATA[]]></darkModePlatformIconUrl>\n\t\t\t<reputationInfo>\n\t\t\t\t<hasReputationInfo>0</hasReputationInfo>\n\t\t\t\t<reputationScore>0</reputationScore>\n\t\t\t\t<reputationWording />\n\t\t\t\t<reputationTextColor />\n\t\t\t\t<reputationLevelWording />\n\t\t\t\t<reputationBackgroundColor />\n\t\t\t</reputationInfo>\n\t\t\t<productImageURLList />\n\t\t</finderShopWindowShare>\n\t\t<findernamecard>\n\t\t\t<username />\n\t\t\t<avatar><![CDATA[]]></avatar>\n\t\t\t<nickname />\n\t\t\t<auth_job />\n\t\t\t<auth_icon>0</auth_icon>\n\t\t\t<auth_icon_url />\n\t\t\t<ecSource><![CDATA[]]></ecSource>\n\t\t\t<lastGMsgID><![CDATA[]]></lastGMsgID>\n\t\t</findernamecard>\n\t\t<finderGuarantee>\n\t\t\t<scene><![CDATA[0]]></scene>\n\t\t</finderGuarantee>\n\t\t<directshare>0</directshare>\n\t\t<gamecenter>\n\t\t\t<namecard>\n\t\t\t\t<iconUrl />\n\t\t\t\t<name />\n\t\t\t\t<desc />\n\t\t\t\t<tail />\n\t\t\t\t<jumpUrl />\n\t\t\t</namecard>\n\t\t</gamecenter>\n\t\t<patMsg>\n\t\t\t<chatUser />\n\t\t\t<records>\n\t\t\t\t<recordNum>0</recordNum>\n\t\t\t</records>\n\t\t</patMsg>\n\t\t<secretmsg>\n\t\t\t<issecretmsg>0</issecretmsg>\n\t\t</secretmsg>\n\t\t<referfromscene>0</referfromscene>\n\t\t<gameshare>\n\t\t\t<liteappext>\n\t\t\t\t<liteappbizdata />\n\t\t\t\t<priority>0</priority>\n\t\t\t</liteappext>\n\t\t\t<appbrandext>\n\t\t\t\t<litegameinfo />\n\t\t\t\t<priority>-1</priority>\n\t\t\t</appbrandext>\n\t\t\t<gameshareid />\n\t\t\t<sharedata />\n\t\t\t<isvideo>0</isvideo>\n\t\t\t<duration>-1</duration>\n\t\t\t<isexposed>0</isexposed>\n\t\t\t<readtext />\n\t\t</gameshare>\n\t\t<mpsharetrace>\n\t\t\t<hasfinderelement>0</hasfinderelement>\n\t\t\t<lastgmsgid />\n\t\t</mpsharetrace>\n\t\t<wxgamecard>\n\t\t\t<framesetname />\n\t\t\t<mbcarddata />\n\t\t\t<minpkgversion />\n\t\t\t<clientextinfo />\n\t\t\t<mbcardheight>0</mbcardheight>\n\t\t\t<isoldversion>0</isoldversion>\n\t\t</wxgamecard>\n\t\t<liteapp>\n\t\t\t<id>null</id>\n\t\t\t<path />\n\t\t\t<query />\n\t\t\t<istransparent>0</istransparent>\n\t\t\t<hideicon>0</hideicon>\n\t\t</liteapp>\n\t\t<opensdk_share_is_modified>0</opensdk_share_is_modified>\n\t</appmsg>\n\t<fromusername>wxid_e3o8s2nf9u2o22</fromusername>\n\t<scene>0</scene>\n\t<appinfo>\n\t\t<version>8</version>\n\t\t<appname>vivo浏览器</appname>\n\t</appinfo>\n\t<commenturl />\n</msg>\n'}, 'Status': 3, 'ImgStatus': 2, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1753658577, 'MsgSource': '<msgsource>\n\t<sec_msg_node>\n\t\t<uuid>9f49a8bbadcdcc93c25b8aea051f9053_</uuid>\n\t\t<risk-file-flag />\n\t\t<risk-file-md5-list />\n\t</sec_msg_node>\n\t<silence>0</silence>\n\t<membercount>73</membercount>\n\t<signature>N0_V1_Nz4ygnJE|v1_e16QqZA0</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'PushContent': '星空- 梦 : [链接]猜你喜欢～萝莉 双马尾 甜妹统治世界', 'NewMsgId': 8172513130279858444, 'MsgSeq': 871404406}
2025-07-28 07:22:47 | DEBUG | 从群聊消息中提取发送者: wxid_e3o8s2nf9u2o22
2025-07-28 07:22:47 | INFO | 收到公众号文章消息: 消息ID:837127164 来自:48097389945@chatroom
2025-07-28 07:22:47 | ERROR | 解析XML失败: mismatched tag: line 1, column 970
2025-07-28 07:22:47 | DEBUG | [ArticleForwarder] 检测到XML开头被截断，尝试修复
2025-07-28 07:22:47 | DEBUG | [ArticleForwarder] 已修复XML开头截断问题
2025-07-28 07:22:47 | DEBUG | 尝试使用修复后的XML重新解析
2025-07-28 07:22:47 | DEBUG | 从appinfo/appname提取到公众号: vivo浏览器
2025-07-28 07:22:47 | DEBUG | 公众号「vivo浏览器」不在监控列表中，跳过处理
2025-07-28 07:22:48 | DEBUG | 收到消息: {'MsgId': 1529553352, 'FromUserName': {'string': '48097389945@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 1, 'Content': {'string': 'last--exile:\n6'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1753658578, 'MsgSource': '<msgsource>\n\t<pua>1</pua>\n\t<silence>0</silence>\n\t<membercount>73</membercount>\n\t<signature>N0_V1_XKIXpbum|v1_HwQmHhiu</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'PushContent': '亮 : 6', 'NewMsgId': 8550273890571929915, 'MsgSeq': 871404407}
2025-07-28 07:22:48 | INFO | 收到文本消息: 消息ID:1529553352 来自:48097389945@chatroom 发送人:last--exile @:[] 内容:6
2025-07-28 07:22:49 | DEBUG | 处理消息内容: '6'
2025-07-28 07:22:49 | DEBUG | 消息内容 '6' 不匹配任何命令，忽略
2025-07-28 07:22:51 | DEBUG | 收到消息: {'MsgId': 1014251309, 'FromUserName': {'string': '48097389945@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 49, 'Content': {'string': 'wxid_q35rkzgkjvlv12:\n<?xml version="1.0"?>\n<msg>\n\t<appmsg appid="" sdkver="0">\n\t\t<title>动态广场</title>\n\t\t<des>[点击链接进入发布动态😎]</des>\n\t\t<action>view</action>\n\t\t<type>5</type>\n\t\t<showtype>0</showtype>\n\t\t<content />\n\t\t<url>http://sfl2.jidifei1.store/h5/#/pages/trends/index</url>\n\t\t<thumburl>http://shmmsns.qpic.cn/mmsns/2qVsMficEBficicAjy7aTCgic80Mib9hExPmHmcd9vTpnxMeEC4TG7tLIpFbHjMKC7wglgha0m3zSdog/0 </thumburl>\n\t\t<lowurl />\n\t\t<appattach>\n\t\t\t<totallen>0</totallen>\n\t\t\t<attachid />\n\t\t\t<fileext />\n\t\t</appattach>\n\t\t<extinfo />\n\t</appmsg>\n\t<fromusername>wxid_q35rkzgkjvlv12</fromusername>\n\t<scene>0</scene>\n\t<appinfo>\n\t\t<version>1</version>\n\t\t<appname />\n\t</appinfo>\n\t<commenturl />\n</msg>\n'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1753658579, 'MsgSource': '<msgsource>\n\t<sec_msg_node>\n\t\t<uuid>ee377f8cd0a88c2b8e8e7df3f0a0b3d4_</uuid>\n\t\t<risk-file-flag />\n\t\t<risk-file-md5-list />\n\t</sec_msg_node>\n\t<silence>0</silence>\n\t<membercount>73</membercount>\n\t<signature>N0_V1_nPtYU4AA|v1_0b1g3pV0</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'PushContent': '摇月 : [链接]动态广场', 'NewMsgId': 5113457774525580806, 'MsgSeq': 871404408}
2025-07-28 07:22:51 | DEBUG | 从群聊消息中提取发送者: wxid_q35rkzgkjvlv12
2025-07-28 07:22:51 | INFO | 收到公众号文章消息: 消息ID:1014251309 来自:48097389945@chatroom
2025-07-28 07:22:51 | ERROR | 解析XML失败: mismatched tag: line 1, column 52
2025-07-28 07:22:51 | DEBUG | [ArticleForwarder] 检测到XML开头被截断，尝试修复
2025-07-28 07:22:51 | DEBUG | [ArticleForwarder] 已修复XML开头截断问题
2025-07-28 07:22:51 | DEBUG | 尝试使用修复后的XML重新解析
2025-07-28 07:22:51 | WARNING | 未能从XML中提取到公众号ID
2025-07-28 07:22:51 | DEBUG | 未提取到公众号ID，跳过处理
2025-07-28 07:22:52 | DEBUG | 收到消息: {'MsgId': 128855862, 'FromUserName': {'string': '48097389945@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 34, 'Content': {'string': 'wxid_lneb7n23o4lg12:\n<msg><voicemsg endflag="1" cancelflag="0" forwardflag="0" voiceformat="4" voicelength="1" length="15360" bufid="0" aeskey="746171727864636f786977687879736c" voiceurl="3052020100044b304902010002046b3e4bb802033d11fe0204ed81507002046886b4d4042438383232376431372d663534642d343135332d396166322d33393862663864366564653402040524000f02010004002a5cd9b3" voicemd5="04ec4753020d41a2f807786678dba78d" clientmsgid="41653132633234663932353735336100580722072825a7b9a8ca75f104" fromusername="wxid_lneb7n23o4lg12" /></msg>'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1753658580, 'MsgSource': '<msgsource>\n\t<silence>0</silence>\n\t<membercount>73</membercount>\n\t<signature>N0_V1_nn0nX/df|v1_r6/b2tL1</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'PushContent': 'ᡣ在群聊中发了一段语音', 'NewMsgId': 7795053552864388253, 'MsgSeq': 871404409}
2025-07-28 07:22:52 | INFO | 收到语音消息: 消息ID:128855862 来自:48097389945@chatroom 发送人:wxid_lneb7n23o4lg12 XML:
<msg><voicemsg endflag="1" cancelflag="0" forwardflag="0" voiceformat="4" voicelength="1" length="15360" bufid="0" aeskey="746171727864636f786977687879736c" voiceurl="3052020100044b304902010002046b3e4bb802033d11fe0204ed81507002046886b4d4042438383232376431372d663534642d343135332d396166322d33393862663864366564653402040524000f02010004002a5cd9b3" voicemd5="04ec4753020d41a2f807786678dba78d" clientmsgid="41653132633234663932353735336100580722072825a7b9a8ca75f104" fromusername="wxid_lneb7n23o4lg12" /></msg>
2025-07-28 07:23:14 | DEBUG | 收到消息: {'MsgId': 1654999970, 'FromUserName': {'string': '27852221909@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 1, 'Content': {'string': 'wangchunmeng7291:\n@Z⁰\u2005不睡了 一会儿就出去了 老大注意安全喔~'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1753658604, 'MsgSource': '<msgsource>\n\t<atuserlist>wxid_ctp9qffuf14b21</atuserlist>\n\t<bizflag>0</bizflag>\n\t<pua>1</pua>\n\t<eggIncluded>1</eggIncluded>\n\t<silence>1</silence>\n\t<membercount>144</membercount>\n\t<signature>N0_V1_boMkwrE2|v1_pQRO8FfK</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'NewMsgId': 8938135829538553456, 'MsgSeq': 871404410}
2025-07-28 07:23:14 | INFO | 收到文本消息: 消息ID:1654999970 来自:27852221909@chatroom 发送人:wangchunmeng7291 @:['wxid_ctp9qffuf14b21'] 内容:@Z⁰ 不睡了 一会儿就出去了 老大注意安全喔~
2025-07-28 07:23:14 | DEBUG | 处理消息内容: '@Z⁰ 不睡了 一会儿就出去了 老大注意安全喔~'
2025-07-28 07:23:14 | DEBUG | 消息内容 '@Z⁰ 不睡了 一会儿就出去了 老大注意安全喔~' 不匹配任何命令，忽略
2025-07-28 07:23:46 | DEBUG | 收到消息: {'MsgId': 1001420514, 'FromUserName': {'string': '27852221909@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 47, 'Content': {'string': 'wxid_ctp9qffuf14b21:\n<msg><emoji fromusername = "wxid_ctp9qffuf14b21" tousername = "27852221909@chatroom" type="1" idbuffer="media:0_0" md5="fcf534328b4fca7c4b8ab52acefd026e" len = "120857" productid="" androidmd5="fcf534328b4fca7c4b8ab52acefd026e" androidlen="120857" s60v3md5 = "fcf534328b4fca7c4b8ab52acefd026e" s60v3len="120857" s60v5md5 = "fcf534328b4fca7c4b8ab52acefd026e" s60v5len="120857" cdnurl = "http://wxapp.tc.qq.com/262/20304/stodownload?m=fcf534328b4fca7c4b8ab52acefd026e&amp;filekey=30350201010421301f02020106040253480410fcf534328b4fca7c4b8ab52acefd026e020301d819040d00000004627466730000000132&amp;hy=SH&amp;storeid=26324635e000ce27e000000000000010600004f50534814ac596097d51a60e&amp;bizid=1023" designerid = "" thumburl = "" encrypturl = "http://wxapp.tc.qq.com/262/20304/stodownload?m=cb3c92dd4334fc57ecd8bb6530c78c4f&amp;filekey=30350201010421301f02020106040253480410cb3c92dd4334fc57ecd8bb6530c78c4f020301d820040d00000004627466730000000132&amp;hy=SH&amp;storeid=26324635f00011932000000000000010600004f50534818865b40b7df703a3&amp;bizid=1023" aeskey= "c4377a3dd1a50450f698130c36e45ae0" externurl = "http://wxapp.tc.qq.com/262/20304/stodownload?m=0b4d94c7c0538158d17faec5d7bbde38&amp;filekey=30350201010421301f020201060402534804100b4d94c7c0538158d17faec5d7bbde38020300ad60040d00000004627466730000000132&amp;hy=SH&amp;storeid=26324635f000323fe000000000000010600004f5053480d4c596097d4b0061&amp;bizid=1023" externmd5 = "a744ef916508ddbb21af98c50e0eabdb" width= "240" height= "240" tpurl= "" tpauthkey= "" attachedtext= "" attachedtextcolor= "" lensid= "" emojiattr= "CgNtdWE=" linkid= "" desc= "" ></emoji>  </msg>'}, 'Status': 3, 'ImgStatus': 2, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1753658635, 'MsgSource': '<msgsource>\n\t<silence>1</silence>\n\t<membercount>144</membercount>\n\t<signature>N0_V1_VEuZAW2Z|v1_D6RHBqNV</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'NewMsgId': 8530359844598164178, 'MsgSeq': 871404411}
2025-07-28 07:23:46 | INFO | 收到表情消息: 消息ID:1001420514 来自:27852221909@chatroom 发送人:wxid_ctp9qffuf14b21 MD5:fcf534328b4fca7c4b8ab52acefd026e 大小:120857
2025-07-28 07:23:46 | DEBUG | [DoubaoImageRecognition] 缓存表情消息: 8530359844598164178
2025-07-28 07:30:09 | WARNING | 获取新消息失败 Error：Post "http://short.weixin.qq.com/mmtls/1753659008": read tcp 20.20.20.21:54971->101.227.131.167:80: wsarecv: invalid argument
2025-07-28 07:35:51 | INFO | [TempFileManager] 开始清理临时文件...
2025-07-28 07:36:48 | DEBUG | 收到消息: {'MsgId': 1781437461, 'FromUserName': {'string': '48097389945@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 3, 'Content': {'string': 'xiaomaochong:\n<?xml version="1.0"?>\n<msg>\n\t<img aeskey="20631dbb6bcaf8145d56fe4586721561" encryver="1" cdnthumbaeskey="20631dbb6bcaf8145d56fe4586721561" cdnthumburl="3057020100044b304902010002049363814102032f514902042131227502046886b81a042464383431313962612d306361622d343561652d626166342d613634623561393264343436020405252a010201000405004c51e500" cdnthumblength="2997" cdnthumbheight="120" cdnthumbwidth="68" cdnmidheight="0" cdnmidwidth="0" cdnhdheight="0" cdnhdwidth="0" cdnmidimgurl="3057020100044b304902010002049363814102032f514902042131227502046886b81a042464383431313962612d306361622d343561652d626166342d613634623561393264343436020405252a010201000405004c51e500" length="13331" cdnbigimgurl="3057020100044b304902010002049363814102032f514902042131227502046886b81a042464383431313962612d306361622d343561652d626166342d613634623561393264343436020405252a010201000405004c51e500" hdlength="145646" md5="a31d8da8354b2ff2528238727a803747" hevc_mid_size="13331" originsourcemd5="a31d8da8354b2ff2528238727a803747">\n\t\t<secHashInfoBase64 />\n\t\t<live>\n\t\t\t<duration>0</duration>\n\t\t\t<size>0</size>\n\t\t\t<md5 />\n\t\t\t<fileid />\n\t\t\t<hdsize>0</hdsize>\n\t\t\t<hdmd5 />\n\t\t\t<hdfileid />\n\t\t\t<stillimagetimems>0</stillimagetimems>\n\t\t</live>\n\t</img>\n\t<appinfo>\n\t\t<appid>wxca25444c1e5649c5</appid>\n\t\t<version>4</version>\n\t\t<appname>iPhome Xi Max工程机</appname>\n\t\t<isforceupdate>0</isforceupdate>\n\t\t<messageaction />\n\t\t<messageext />\n\t\t<mediatagname />\n\t</appinfo>\n\t<platform_signature />\n\t<imgdatahash />\n\t<ImgSourceInfo>\n\t\t<ImgSourceUrl />\n\t\t<BizType>0</BizType>\n\t</ImgSourceInfo>\n</msg>\n'}, 'Status': 3, 'ImgStatus': 2, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1753659418, 'MsgSource': '<msgsource>\n\t<alnode>\n\t\t<fr>2</fr>\n\t</alnode>\n\t<sec_msg_node>\n\t\t<uuid>2ef66b9c3a5bf2e75e8405b129d76700_</uuid>\n\t\t<risk-file-flag />\n\t\t<risk-file-md5-list />\n\t</sec_msg_node>\n\t<silence>0</silence>\n\t<membercount>73</membercount>\n\t<signature>N0_V1_LWmJiQTQ|v1_STiv4Me0</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'PushContent': '小爱在群聊中发了一张图片', 'NewMsgId': 2831977837830092093, 'MsgSeq': 871404412}
2025-07-28 07:36:48 | INFO | 收到图片消息: 消息ID:1781437461 来自:48097389945@chatroom 发送人:xiaomaochong XML:<?xml version="1.0"?><msg><img aeskey="20631dbb6bcaf8145d56fe4586721561" encryver="1" cdnthumbaeskey="20631dbb6bcaf8145d56fe4586721561" cdnthumburl="3057020100044b304902010002049363814102032f514902042131227502046886b81a042464383431313962612d306361622d343561652d626166342d613634623561393264343436020405252a010201000405004c51e500" cdnthumblength="2997" cdnthumbheight="120" cdnthumbwidth="68" cdnmidheight="0" cdnmidwidth="0" cdnhdheight="0" cdnhdwidth="0" cdnmidimgurl="3057020100044b304902010002049363814102032f514902042131227502046886b81a042464383431313962612d306361622d343561652d626166342d613634623561393264343436020405252a010201000405004c51e500" length="13331" cdnbigimgurl="3057020100044b304902010002049363814102032f514902042131227502046886b81a042464383431313962612d306361622d343561652d626166342d613634623561393264343436020405252a010201000405004c51e500" hdlength="145646" md5="a31d8da8354b2ff2528238727a803747" hevc_mid_size="13331" originsourcemd5="a31d8da8354b2ff2528238727a803747"><secHashInfoBase64 /><live><duration>0</duration><size>0</size><md5 /><fileid /><hdsize>0</hdsize><hdmd5 /><hdfileid /><stillimagetimems>0</stillimagetimems></live></img><appinfo><appid>wxca25444c1e5649c5</appid><version>4</version><appname>iPhome Xi Max工程机</appname><isforceupdate>0</isforceupdate><messageaction /><messageext /><mediatagname /></appinfo><platform_signature /><imgdatahash /><ImgSourceInfo><ImgSourceUrl /><BizType>0</BizType></ImgSourceInfo></msg>
2025-07-28 07:36:49 | INFO | [ImageEcho] 保存图片信息成功，当前群 48097389945@chatroom 已存储 5 张图片
2025-07-28 07:36:49 | INFO | [TimerTask] 缓存图片消息: 1781437461
2025-07-28 07:43:58 | DEBUG | 收到消息: {'MsgId': 1730303978, 'FromUserName': {'string': '48097389945@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 43, 'Content': {'string': 'xiaomaochong:\n<?xml version="1.0"?>\n<msg>\n\t<videomsg aeskey="a5c7a7b672374b8595dfd2faa9deda49" cdnvideourl="3057020100044b30490201000204aaec78d102032e6c63020416ab016f02046885e24e042437366437643132652d356138312d346436332d396431342d3932616433353831626431650204052408040201000405004c550700" cdnthumbaeskey="a5c7a7b672374b8595dfd2faa9deda49" cdnthumburl="3057020100044b30490201000204aaec78d102032e6c63020416ab016f02046885e24e042437366437643132652d356138312d346436332d396431342d3932616433353831626431650204052408040201000405004c550700" length="16672936" playlength="41" cdnthumblength="26367" cdnthumbwidth="442" cdnthumbheight="786" fromusername="xiaomaochong" md5="930ef42db592163df42f97c8c0c9cf98" newmd5="25e38b71c795bf51a1e9e7588ab40552" isplaceholder="0" rawmd5="" rawlength="0" cdnrawvideourl="" cdnrawvideoaeskey="" overwritenewmsgid="0" originsourcemd5="" isad="0" />\n</msg>\n'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1753659847, 'MsgSource': '<msgsource>\n\t<alnode>\n\t\t<fr>5</fr>\n\t</alnode>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n\t<sec_msg_node>\n\t\t<uuid>e4fdd2ccf54cf9a76f46a8a5a6a8b638_</uuid>\n\t\t<risk-file-flag />\n\t\t<risk-file-md5-list />\n\t</sec_msg_node>\n\t<silence>0</silence>\n\t<membercount>73</membercount>\n\t<signature>N0_V1_du+FCZh8|v1_NFJApfX3</signature>\n</msgsource>\n', 'PushContent': '小爱在群聊中发了一段视频', 'NewMsgId': 1476415393547055554, 'MsgSeq': 871404413}
2025-07-28 07:43:58 | INFO | 收到视频消息: 消息ID:1730303978 来自:48097389945@chatroom 发送人:xiaomaochong XML:
<?xml version="1.0"?>
<msg>
	<videomsg aeskey="a5c7a7b672374b8595dfd2faa9deda49" cdnvideourl="3057020100044b30490201000204aaec78d102032e6c63020416ab016f02046885e24e042437366437643132652d356138312d346436332d396431342d3932616433353831626431650204052408040201000405004c550700" cdnthumbaeskey="a5c7a7b672374b8595dfd2faa9deda49" cdnthumburl="3057020100044b30490201000204aaec78d102032e6c63020416ab016f02046885e24e042437366437643132652d356138312d346436332d396431342d3932616433353831626431650204052408040201000405004c550700" length="16672936" playlength="41" cdnthumblength="26367" cdnthumbwidth="442" cdnthumbheight="786" fromusername="xiaomaochong" md5="930ef42db592163df42f97c8c0c9cf98" newmd5="25e38b71c795bf51a1e9e7588ab40552" isplaceholder="0" rawmd5="" rawlength="0" cdnrawvideourl="" cdnrawvideoaeskey="" overwritenewmsgid="0" originsourcemd5="" isad="0" />
</msg>

2025-07-28 07:48:11 | DEBUG | 收到消息: {'MsgId': 1725037574, 'FromUserName': {'string': '48097389945@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 1, 'Content': {'string': 'xiaomaochong:\n群指令'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1753660101, 'MsgSource': '<msgsource>\n\t<pua>1</pua>\n\t<silence>0</silence>\n\t<membercount>73</membercount>\n\t<signature>N0_V1_yvBJd1io|v1_TjZ86d6B</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'PushContent': '小爱 : 群指令', 'NewMsgId': 2682406938966619865, 'MsgSeq': 871404414}
2025-07-28 07:48:11 | INFO | 收到文本消息: 消息ID:1725037574 来自:48097389945@chatroom 发送人:xiaomaochong @:[] 内容:群指令
2025-07-28 07:48:12 | DEBUG | 处理消息内容: '群指令'
2025-07-28 07:48:12 | DEBUG | 消息内容 '群指令' 不匹配任何命令，忽略
2025-07-28 07:48:14 | DEBUG | 收到消息: {'MsgId': 1715938215, 'FromUserName': {'string': '48097389945@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 49, 'Content': {'string': 'wxid_q35rkzgkjvlv12:\n<?xml version="1.0"?>\n<msg>\n\t<appmsg appid="" sdkver="0">\n\t\t<title>超管群指令</title>\n\t\t<des />\n\t\t<action>view</action>\n\t\t<type>5</type>\n\t\t<showtype>0</showtype>\n\t\t<content />\n\t\t<url>https://mp.weixin.qq.com/s/PKLNMNWBmFwogU8lSKjUDw</url>\n\t\t<thumburl>http://vweixinf.tc.qq.com/110/20402/stodownload?m=775e8a1a9c77b8e1f476fd26b2cc614f&amp;filekey=30440201010430302e02016e04025348042037373565386131613963373762386531663437366664323662326363363134660203400ca8040d00000004627466730000000132&amp;hy=SH&amp;storeid=26560160b0007986734d723270000006e01004fb25348078380315661cd83a&amp;ef=1&amp;bizid=1022\n\n775e8a1a9c77b8e1f476fd26b2cc614f@4197544</thumburl>\n\t\t<lowurl />\n\t\t<appattach>\n\t\t\t<totallen>0</totallen>\n\t\t\t<attachid />\n\t\t\t<fileext />\n\t\t</appattach>\n\t\t<extinfo />\n\t\t<sourceusername>gh_57cc48390b14</sourceusername>\n\t\t<sourcedisplayname>瓜子君</sourcedisplayname>\n\t\t<mmreadershare>\n\t\t\t<itemshowtype>0</itemshowtype>\n\t\t</mmreadershare>\n\t</appmsg>\n\t<fromusername>wxid_q35rkzgkjvlv12</fromusername>\n\t<scene>0</scene>\n\t<appinfo>\n\t\t<version>1</version>\n\t\t<appname />\n\t</appinfo>\n\t<commenturl />\n</msg>\n'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1753660102, 'MsgSource': '<msgsource>\n\t<sec_msg_node>\n\t\t<uuid>66ff044002fed93205c721a7e1c05a92_</uuid>\n\t\t<risk-file-flag />\n\t\t<risk-file-md5-list />\n\t</sec_msg_node>\n\t<silence>0</silence>\n\t<membercount>73</membercount>\n\t<signature>N0_V1_3z8EGQWN|v1_6snUhPrH</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'PushContent': '摇月 : [链接]超管群指令', 'NewMsgId': 2741785358514734921, 'MsgSeq': 871404415}
2025-07-28 07:48:14 | DEBUG | 从群聊消息中提取发送者: wxid_q35rkzgkjvlv12
2025-07-28 07:48:14 | INFO | 收到公众号文章消息: 消息ID:1715938215 来自:48097389945@chatroom
2025-07-28 07:48:14 | ERROR | 解析XML失败: mismatched tag: line 1, column 50
2025-07-28 07:48:14 | DEBUG | [ArticleForwarder] 检测到XML开头被截断，尝试修复
2025-07-28 07:48:14 | DEBUG | [ArticleForwarder] 已修复XML开头截断问题
2025-07-28 07:48:14 | DEBUG | 尝试使用修复后的XML重新解析
2025-07-28 07:48:14 | DEBUG | 从sourcedisplayname提取到公众号: 瓜子君
2025-07-28 07:48:14 | DEBUG | 公众号「瓜子君」不在监控列表中，跳过处理
2025-07-28 07:50:08 | DEBUG | 收到消息: {'MsgId': 1517447925, 'FromUserName': {'string': '48097389945@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 1, 'Content': {'string': 'xiaomaochong:\n关闭电影'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1753660218, 'MsgSource': '<msgsource>\n\t<pua>1</pua>\n\t<silence>0</silence>\n\t<membercount>73</membercount>\n\t<signature>N0_V1_3GYyolSP|v1_cU7yo+ol</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'PushContent': '小爱 : 关闭电影', 'NewMsgId': 4814776178147829202, 'MsgSeq': 871404416}
2025-07-28 07:50:08 | INFO | 收到文本消息: 消息ID:1517447925 来自:48097389945@chatroom 发送人:xiaomaochong @:[] 内容:关闭电影
2025-07-28 07:50:09 | DEBUG | 处理消息内容: '关闭电影'
2025-07-28 07:50:09 | DEBUG | 消息内容 '关闭电影' 不匹配任何命令，忽略
2025-07-28 07:50:12 | DEBUG | 收到消息: {'MsgId': 1379718879, 'FromUserName': {'string': '48097389945@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 1, 'Content': {'string': 'xiaomaochong:\n关闭签名'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1753660222, 'MsgSource': '<msgsource>\n\t<pua>1</pua>\n\t<silence>0</silence>\n\t<membercount>73</membercount>\n\t<signature>N0_V1_v63jdSQj|v1_l9owEcot</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'PushContent': '小爱 : 关闭签名', 'NewMsgId': 5953976607548072015, 'MsgSeq': 871404418}
2025-07-28 07:50:12 | INFO | 收到文本消息: 消息ID:1379718879 来自:48097389945@chatroom 发送人:xiaomaochong @:[] 内容:关闭签名
2025-07-28 07:50:13 | DEBUG | 处理消息内容: '关闭签名'
2025-07-28 07:50:13 | DEBUG | 消息内容 '关闭签名' 不匹配任何命令，忽略
2025-07-28 07:50:15 | DEBUG | 收到消息: {'MsgId': 109751448, 'FromUserName': {'string': '48097389945@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 1, 'Content': {'string': 'wxid_8l9ymg1mafud12:\n签名关闭成功'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1753660224, 'MsgSource': '<msgsource>\n\t<silence>0</silence>\n\t<membercount>73</membercount>\n\t<signature>N0_V1_OveAMAUZ|v1_jpGUMXZx</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'PushContent': '不吃香菜\ue110 : 签名关闭成功', 'NewMsgId': 5005655211022164377, 'MsgSeq': 871404419}
2025-07-28 07:50:15 | INFO | 收到文本消息: 消息ID:109751448 来自:48097389945@chatroom 发送人:wxid_8l9ymg1mafud12 @:[] 内容:签名关闭成功
2025-07-28 07:50:15 | DEBUG | 处理消息内容: '签名关闭成功'
2025-07-28 07:50:15 | DEBUG | 消息内容 '签名关闭成功' 不匹配任何命令，忽略
2025-07-28 07:50:17 | DEBUG | 收到消息: {'MsgId': 1743002868, 'FromUserName': {'string': '48097389945@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 1, 'Content': {'string': 'wxid_q35rkzgkjvlv12:\n@不吃香菜🌿 \n需要积分：1积分\n当前积分：0'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1753660226, 'MsgSource': '<msgsource>\n\t<silence>0</silence>\n\t<membercount>73</membercount>\n\t<signature>N0_V1_avYGUw/1|v1_j+fWq60r</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'PushContent': '摇月 : @不吃香菜\ue110 \n需要积分：1积分\n当前积分：0', 'NewMsgId': 3711570030207587534, 'MsgSeq': 871404420}
2025-07-28 07:50:17 | INFO | 收到文本消息: 消息ID:1743002868 来自:48097389945@chatroom 发送人:wxid_q35rkzgkjvlv12 @:[] 内容:@不吃香菜🌿 
需要积分：1积分
当前积分：0
2025-07-28 07:50:18 | DEBUG | 处理消息内容: '@不吃香菜🌿 
需要积分：1积分
当前积分：0'
2025-07-28 07:50:18 | DEBUG | 消息内容 '@不吃香菜🌿 
需要积分：1积分
当前积分：0' 不匹配任何命令，忽略
2025-07-28 07:53:46 | DEBUG | 收到消息: {'MsgId': 875767155, 'FromUserName': {'string': '48097389945@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 1, 'Content': {'string': 'zll953369865:\n喵老板 周六和周日没出现'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1753660436, 'MsgSource': '<msgsource>\n\t<pua>1</pua>\n\t<eggIncluded>1</eggIncluded>\n\t<silence>0</silence>\n\t<membercount>73</membercount>\n\t<signature>N0_V1_oQSD/uZS|v1_JCJdQL4D</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'PushContent': '【微信红包】收到一个微信红包 : 喵老板 周六和周日没出现', 'NewMsgId': 5651908110598151348, 'MsgSeq': 871404421}
2025-07-28 07:53:46 | INFO | 收到文本消息: 消息ID:875767155 来自:48097389945@chatroom 发送人:zll953369865 @:[] 内容:喵老板 周六和周日没出现
2025-07-28 07:53:47 | DEBUG | 处理消息内容: '喵老板 周六和周日没出现'
2025-07-28 07:53:47 | DEBUG | 消息内容 '喵老板 周六和周日没出现' 不匹配任何命令，忽略
2025-07-28 07:54:13 | DEBUG | 收到消息: {'MsgId': 1612157858, 'FromUserName': {'string': '48097389945@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 1, 'Content': {'string': 'zll953369865:\n@小爱\u2005'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1753660463, 'MsgSource': '<msgsource>\n\t<atuserlist><![CDATA[xiaomaochong]]></atuserlist>\n\t<pua>1</pua>\n\t<eggIncluded>1</eggIncluded>\n\t<silence>0</silence>\n\t<membercount>73</membercount>\n\t<signature>N0_V1_x3sF8qft|v1_z/Y8xhGA</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'PushContent': '【微信红包】收到一个微信红包 : @小爱\u2005', 'NewMsgId': 1156926393998018352, 'MsgSeq': 871404422}
2025-07-28 07:54:13 | INFO | 收到文本消息: 消息ID:1612157858 来自:48097389945@chatroom 发送人:zll953369865 @:['xiaomaochong'] 内容:@小爱 
2025-07-28 07:54:13 | DEBUG | 处理消息内容: '@小爱'
2025-07-28 07:54:13 | DEBUG | 消息内容 '@小爱' 不匹配任何命令，忽略
2025-07-28 07:54:20 | DEBUG | 收到消息: {'MsgId': 1900980416, 'FromUserName': {'string': '48097389945@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 1, 'Content': {'string': 'zll953369865:\n是不是退群了'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1753660471, 'MsgSource': '<msgsource>\n\t<pua>1</pua>\n\t<eggIncluded>1</eggIncluded>\n\t<silence>0</silence>\n\t<membercount>73</membercount>\n\t<signature>N0_V1_JQSScAcS|v1_SO5u5FtU</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'PushContent': '【微信红包】收到一个微信红包 : 是不是退群了', 'NewMsgId': 1529058415539569473, 'MsgSeq': 871404423}
2025-07-28 07:54:20 | INFO | 收到文本消息: 消息ID:1900980416 来自:48097389945@chatroom 发送人:zll953369865 @:[] 内容:是不是退群了
2025-07-28 07:54:21 | DEBUG | 处理消息内容: '是不是退群了'
2025-07-28 07:54:21 | DEBUG | 消息内容 '是不是退群了' 不匹配任何命令，忽略
2025-07-28 07:54:26 | DEBUG | 收到消息: {'MsgId': 304446778, 'FromUserName': {'string': '48097389945@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 34, 'Content': {'string': 'xiaomaochong:\n<msg><voicemsg endflag="1" cancelflag="0" forwardflag="0" voiceformat="4" voicelength="10000" length="28396" bufid="0" aeskey="767877626d7877616673716a62657474" voiceurl="3052020100044b304902010002049363814102033d14ba0204fa02ff9d02046886bc3c042464326566646362662d346439342d346634312d626261362d31373433643161623332386302040528000f02010004001dc74187" voicemd5="3d9a5f4aacd29342dd03bbc80b50dc09" clientmsgid="494cd5716bb4ce7e67f76aaff6aa3e5648097389945@chatroom_50309_1753660475" fromusername="xiaomaochong" /></msg>'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1753660476, 'MsgSource': '<msgsource>\n\t<silence>0</silence>\n\t<membercount>73</membercount>\n\t<signature>N0_V1_rrf/O224|v1_t3S4tO5w</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'PushContent': '小爱在群聊中发了一段语音', 'NewMsgId': 2553590295927415154, 'MsgSeq': 871404424}
2025-07-28 07:54:26 | INFO | 收到语音消息: 消息ID:304446778 来自:48097389945@chatroom 发送人:xiaomaochong XML:
<msg><voicemsg endflag="1" cancelflag="0" forwardflag="0" voiceformat="4" voicelength="10000" length="28396" bufid="0" aeskey="767877626d7877616673716a62657474" voiceurl="3052020100044b304902010002049363814102033d14ba0204fa02ff9d02046886bc3c042464326566646362662d346439342d346634312d626261362d31373433643161623332386302040528000f02010004001dc74187" voicemd5="3d9a5f4aacd29342dd03bbc80b50dc09" clientmsgid="494cd5716bb4ce7e67f76aaff6aa3e5648097389945@chatroom_50309_1753660475" fromusername="xiaomaochong" /></msg>
2025-07-28 07:58:03 | DEBUG | 收到消息: {'MsgId': 1962431560, 'FromUserName': {'string': '48097389945@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 1, 'Content': {'string': 'xiaomaochong:\n小叶退了'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1753660693, 'MsgSource': '<msgsource>\n\t<pua>1</pua>\n\t<silence>0</silence>\n\t<membercount>73</membercount>\n\t<signature>N0_V1_cjlA/UBD|v1_dnlES3sP</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'PushContent': '小爱 : 小叶退了', 'NewMsgId': 353592602147667622, 'MsgSeq': 871404425}
2025-07-28 07:58:03 | INFO | 收到文本消息: 消息ID:1962431560 来自:48097389945@chatroom 发送人:xiaomaochong @:[] 内容:小叶退了
2025-07-28 07:58:03 | DEBUG | 处理消息内容: '小叶退了'
2025-07-28 07:58:03 | DEBUG | 消息内容 '小叶退了' 不匹配任何命令，忽略
2025-07-28 07:58:47 | DEBUG | 收到消息: {'MsgId': 1082832128, 'FromUserName': {'string': '48097389945@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 1, 'Content': {'string': 'zll953369865:\n拉进来啊'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1753660737, 'MsgSource': '<msgsource>\n\t<pua>1</pua>\n\t<eggIncluded>1</eggIncluded>\n\t<silence>0</silence>\n\t<membercount>73</membercount>\n\t<signature>N0_V1_KEcu9gyt|v1_4qWkG2yp</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'PushContent': '【微信红包】收到一个微信红包 : 拉进来啊', 'NewMsgId': 6505104344993333187, 'MsgSeq': 871404426}
2025-07-28 07:58:47 | INFO | 收到文本消息: 消息ID:1082832128 来自:48097389945@chatroom 发送人:zll953369865 @:[] 内容:拉进来啊
2025-07-28 07:58:48 | DEBUG | 处理消息内容: '拉进来啊'
2025-07-28 07:58:48 | DEBUG | 消息内容 '拉进来啊' 不匹配任何命令，忽略
2025-07-28 07:58:59 | DEBUG | 收到消息: {'MsgId': 1703561058, 'FromUserName': {'string': '48097389945@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 1, 'Content': {'string': 'zll953369865:\n@小爱\u2005你不有他微信吗'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1753660748, 'MsgSource': '<msgsource>\n\t<atuserlist><![CDATA[xiaomaochong]]></atuserlist>\n\t<pua>1</pua>\n\t<eggIncluded>1</eggIncluded>\n\t<silence>0</silence>\n\t<membercount>73</membercount>\n\t<signature>N0_V1_GqpJEzq9|v1_VCwjyafQ</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'PushContent': '【微信红包】收到一个微信红包 : @小爱\u2005你不有他微信吗', 'NewMsgId': 5810723758300663648, 'MsgSeq': 871404427}
2025-07-28 07:58:59 | INFO | 收到文本消息: 消息ID:1703561058 来自:48097389945@chatroom 发送人:zll953369865 @:['xiaomaochong'] 内容:@小爱 你不有他微信吗
2025-07-28 07:58:59 | DEBUG | 处理消息内容: '@小爱 你不有他微信吗'
2025-07-28 07:58:59 | DEBUG | 消息内容 '@小爱 你不有他微信吗' 不匹配任何命令，忽略
2025-07-28 07:59:20 | DEBUG | 收到消息: {'MsgId': 65739043, 'FromUserName': {'string': '48097389945@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 34, 'Content': {'string': 'xiaomaochong:\n<msg><voicemsg endflag="1" cancelflag="0" forwardflag="0" voiceformat="4" voicelength="25000" length="72986" bufid="0" aeskey="656f6775646d6161737a7a707477646a" voiceurl="3052020100044b304902010002049363814102033d14ba0204de39949d02046886bd62042437383266373164622d316162662d346530342d623439372d63316632323832316539316202040528000f02010004001dc74187" voicemd5="0c064f5fb3ba1d97897a534a473d1368" clientmsgid="494cd5716bb4ce7e67f76aaff6aa3e5648097389945@chatroom_50314_1753660768" fromusername="xiaomaochong" /></msg>'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1753660770, 'MsgSource': '<msgsource>\n\t<silence>0</silence>\n\t<membercount>73</membercount>\n\t<signature>N0_V1_X1lN09XI|v1_ySn0JSao</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'PushContent': '小爱在群聊中发了一段语音', 'NewMsgId': 8189234223727466444, 'MsgSeq': 871404428}
2025-07-28 07:59:20 | INFO | 收到语音消息: 消息ID:65739043 来自:48097389945@chatroom 发送人:xiaomaochong XML:
<msg><voicemsg endflag="1" cancelflag="0" forwardflag="0" voiceformat="4" voicelength="25000" length="72986" bufid="0" aeskey="656f6775646d6161737a7a707477646a" voiceurl="3052020100044b304902010002049363814102033d14ba0204de39949d02046886bd62042437383266373164622d316162662d346530342d623439372d63316632323832316539316202040528000f02010004001dc74187" voicemd5="0c064f5fb3ba1d97897a534a473d1368" clientmsgid="494cd5716bb4ce7e67f76aaff6aa3e5648097389945@chatroom_50314_1753660768" fromusername="xiaomaochong" /></msg>
2025-07-28 08:00:00 | INFO | [QuarkSignIn] 开始执行定时自动签到任务
2025-07-28 08:00:01 | DEBUG | [QuarkSignIn] API响应状态码: 200
2025-07-28 08:00:01 | DEBUG | [QuarkSignIn] 签到响应状态码: 200
2025-07-28 08:00:02 | INFO | 发送文字消息: 对方wxid:wxid_ubbh6q832tcs21 at: 内容:🤖 夸克网盘自动签到完成

🌟 夸克网盘签到开始
📊 检测到 1 个账号

🔄 第1个账号签到中...
👤 普通用户 夸父4527
💾 网盘总容量：19.79 GB
📈 签到累计容量：5.79 GB
🎉 今日签到成功+20.00 MB，连签进度(5/7)

✨ 夸克网盘签到完成
2025-07-28 08:00:02 | INFO | [QuarkSignIn] 已发送签到通知到: wxid_ubbh6q832tcs21
2025-07-28 08:00:07 | DEBUG | 收到消息: {'MsgId': *********, 'FromUserName': {'string': '48097389945@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 3, 'Content': {'string': 'zuoledd:\n<?xml version="1.0"?>\n<msg>\n\t<img aeskey="6871d7dbdd64d90028fce312da000680" encryver="1" cdnthumbaeskey="6871d7dbdd64d90028fce312da000680" cdnthumburl="3057020100044b304902010002049fdf868102032f77930204deb8206f020468864b69042437633762623930382d643362392d346161622d623034302d373433356432643039376133020405290a020201000405004c57c300" cdnthumblength="4145" cdnthumbheight="180" cdnthumbwidth="100" cdnmidheight="0" cdnmidwidth="0" cdnhdheight="0" cdnhdwidth="0" cdnmidimgurl="3057020100044b304902010002049fdf868102032f77930204deb8206f020468864b69042437633762623930382d643362392d346161622d623034302d373433356432643039376133020405290a020201000405004c57c300" length="85594" md5="2c56043e9fa4ad196b8b7a6fa710fd18" hevc_mid_size="85594" originsourcemd5="46ad6672a8c98696bce6fbb1d1306f85">\n\t\t<secHashInfoBase64>eyJwaGFzaCI6IjU4MTJhMDUwMjQwMTAxMjAiLCJwZHFoYXNoIjoiMjg3YjE1MWJiNTEyNzFmMjA3ZmUyZTM0YTVhNWFmNmM3ODZjZTJjMTRlOTMxY2Q2ODA0NmM3MGRiOTdiYjI5MyJ9</secHashInfoBase64>\n\t\t<live>\n\t\t\t<duration>0</duration>\n\t\t\t<size>0</size>\n\t\t\t<md5 />\n\t\t\t<fileid />\n\t\t\t<hdsize>0</hdsize>\n\t\t\t<hdmd5 />\n\t\t\t<hdfileid />\n\t\t\t<stillimagetimems>0</stillimagetimems>\n\t\t</live>\n\t</img>\n\t<platform_signature />\n\t<imgdatahash />\n\t<ImgSourceInfo>\n\t\t<ImgSourceUrl />\n\t\t<BizType>0</BizType>\n\t</ImgSourceInfo>\n</msg>\n'}, 'Status': 3, 'ImgStatus': 2, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1753660816, 'MsgSource': '<msgsource>\n\t<bizflag>0</bizflag>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n\t<alnode>\n\t\t<fr>5</fr>\n\t</alnode>\n\t<sec_msg_node>\n\t\t<uuid>ea7ac008f2764b05fc3fc4aae7b533cf_</uuid>\n\t\t<risk-file-flag />\n\t\t<risk-file-md5-list />\n\t</sec_msg_node>\n\t<silence>0</silence>\n\t<membercount>73</membercount>\n\t<signature>N0_V1_mlftDiGL|v1_prF2RFke</signature>\n</msgsource>\n', 'PushContent': '作乐多端在群聊中发了一张图片', 'NewMsgId': 5115641304518925049, 'MsgSeq': 871404431}
2025-07-28 08:00:07 | INFO | 收到图片消息: 消息ID:********* 来自:48097389945@chatroom 发送人:zuoledd XML:<?xml version="1.0"?><msg><img aeskey="6871d7dbdd64d90028fce312da000680" encryver="1" cdnthumbaeskey="6871d7dbdd64d90028fce312da000680" cdnthumburl="3057020100044b304902010002049fdf868102032f77930204deb8206f020468864b69042437633762623930382d643362392d346161622d623034302d373433356432643039376133020405290a020201000405004c57c300" cdnthumblength="4145" cdnthumbheight="180" cdnthumbwidth="100" cdnmidheight="0" cdnmidwidth="0" cdnhdheight="0" cdnhdwidth="0" cdnmidimgurl="3057020100044b304902010002049fdf868102032f77930204deb8206f020468864b69042437633762623930382d643362392d346161622d623034302d373433356432643039376133020405290a020201000405004c57c300" length="85594" md5="2c56043e9fa4ad196b8b7a6fa710fd18" hevc_mid_size="85594" originsourcemd5="46ad6672a8c98696bce6fbb1d1306f85"><secHashInfoBase64>eyJwaGFzaCI6IjU4MTJhMDUwMjQwMTAxMjAiLCJwZHFoYXNoIjoiMjg3YjE1MWJiNTEyNzFmMjA3ZmUyZTM0YTVhNWFmNmM3ODZjZTJjMTRlOTMxY2Q2ODA0NmM3MGRiOTdiYjI5MyJ9</secHashInfoBase64><live><duration>0</duration><size>0</size><md5 /><fileid /><hdsize>0</hdsize><hdmd5 /><hdfileid /><stillimagetimems>0</stillimagetimems></live></img><platform_signature /><imgdatahash /><ImgSourceInfo><ImgSourceUrl /><BizType>0</BizType></ImgSourceInfo></msg>
2025-07-28 08:00:07 | INFO | [ImageEcho] 保存图片信息成功，当前群 48097389945@chatroom 已存储 5 张图片
2025-07-28 08:00:07 | INFO | [TimerTask] 缓存图片消息: *********
2025-07-28 08:00:36 | DEBUG | 收到消息: {'MsgId': *********, 'FromUserName': {'string': '48097389945@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 1, 'Content': {'string': 'xiaomaochong:\n你找她私聊，她退有原因的'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1753660846, 'MsgSource': '<msgsource>\n\t<pua>1</pua>\n\t<silence>0</silence>\n\t<membercount>73</membercount>\n\t<signature>N0_V1_VBAA03Jn|v1_a/md/xyO</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'PushContent': '小爱 : 你找她私聊，她退有原因的', 'NewMsgId': 1571473381410927227, 'MsgSeq': 871404432}
2025-07-28 08:00:36 | INFO | 收到文本消息: 消息ID:********* 来自:48097389945@chatroom 发送人:xiaomaochong @:[] 内容:你找她私聊，她退有原因的
2025-07-28 08:00:37 | DEBUG | 处理消息内容: '你找她私聊，她退有原因的'
2025-07-28 08:00:37 | DEBUG | 消息内容 '你找她私聊，她退有原因的' 不匹配任何命令，忽略
2025-07-28 08:00:52 | DEBUG | 收到消息: {'MsgId': 1897938338, 'FromUserName': {'string': '48097389945@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 1, 'Content': {'string': 'zll953369865:\n没她微信啊'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1753660862, 'MsgSource': '<msgsource>\n\t<pua>1</pua>\n\t<eggIncluded>1</eggIncluded>\n\t<silence>0</silence>\n\t<membercount>73</membercount>\n\t<signature>N0_V1_aZM1lVPf|v1_6qMeO8ay</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'PushContent': '【微信红包】收到一个微信红包 : 没她微信啊', 'NewMsgId': 7220401116401665786, 'MsgSeq': 871404433}
2025-07-28 08:00:52 | INFO | 收到文本消息: 消息ID:1897938338 来自:48097389945@chatroom 发送人:zll953369865 @:[] 内容:没她微信啊
2025-07-28 08:00:53 | DEBUG | 处理消息内容: '没她微信啊'
2025-07-28 08:00:53 | DEBUG | 消息内容 '没她微信啊' 不匹配任何命令，忽略
2025-07-28 08:00:56 | DEBUG | 收到消息: {'MsgId': 594488627, 'FromUserName': {'string': '48097389945@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 1, 'Content': {'string': 'zll953369865:\n你有啊'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1753660866, 'MsgSource': '<msgsource>\n\t<pua>1</pua>\n\t<eggIncluded>1</eggIncluded>\n\t<silence>0</silence>\n\t<membercount>73</membercount>\n\t<signature>N0_V1_pIisk8Tn|v1_GmPT7fdR</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'PushContent': '【微信红包】收到一个微信红包 : 你有啊', 'NewMsgId': 5463447983443255274, 'MsgSeq': 871404434}
2025-07-28 08:00:56 | INFO | 收到文本消息: 消息ID:594488627 来自:48097389945@chatroom 发送人:zll953369865 @:[] 内容:你有啊
2025-07-28 08:00:57 | DEBUG | 处理消息内容: '你有啊'
2025-07-28 08:00:57 | DEBUG | 消息内容 '你有啊' 不匹配任何命令，忽略
2025-07-28 08:01:55 | DEBUG | 收到消息: {'MsgId': 1579430645, 'FromUserName': {'string': '48097389945@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 1, 'Content': {'string': 'xiaomaochong:\n你都没微信还惦记人家干嘛[抠鼻]'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1753660925, 'MsgSource': '<msgsource>\n\t<pua>1</pua>\n\t<silence>0</silence>\n\t<membercount>73</membercount>\n\t<signature>N0_V1_eDJCoRNv|v1_51r6y/g6</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'PushContent': '小爱 : 你都没微信还惦记人家干嘛[抠鼻]', 'NewMsgId': 2653675008607185469, 'MsgSeq': 871404435}
2025-07-28 08:01:55 | INFO | 收到文本消息: 消息ID:1579430645 来自:48097389945@chatroom 发送人:xiaomaochong @:[] 内容:你都没微信还惦记人家干嘛[抠鼻]
2025-07-28 08:01:56 | DEBUG | 处理消息内容: '你都没微信还惦记人家干嘛[抠鼻]'
2025-07-28 08:01:56 | DEBUG | 消息内容 '你都没微信还惦记人家干嘛[抠鼻]' 不匹配任何命令，忽略
2025-07-28 08:02:20 | DEBUG | 收到消息: {'MsgId': 1589148283, 'FromUserName': {'string': '48097389945@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 1, 'Content': {'string': 'wxid_wlnzvr8ivgd422:\n睁不开眼'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1753660951, 'MsgSource': '<msgsource>\n\t<pua>1</pua>\n\t<eggIncluded>1</eggIncluded>\n\t<silence>0</silence>\n\t<membercount>73</membercount>\n\t<signature>N0_V1_qGnsf4Rs|v1_IP4uvt1d</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'PushContent': '锦岚 : 睁不开眼', 'NewMsgId': 6597276638067228106, 'MsgSeq': 871404436}
2025-07-28 08:02:20 | INFO | 收到文本消息: 消息ID:1589148283 来自:48097389945@chatroom 发送人:wxid_wlnzvr8ivgd422 @:[] 内容:睁不开眼
2025-07-28 08:02:21 | DEBUG | 处理消息内容: '睁不开眼'
2025-07-28 08:02:21 | DEBUG | 消息内容 '睁不开眼' 不匹配任何命令，忽略
2025-07-28 08:02:27 | DEBUG | 收到消息: {'MsgId': 1968377233, 'FromUserName': {'string': '48097389945@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 1, 'Content': {'string': 'wxid_wlnzvr8ivgd422:\n早安'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1753660957, 'MsgSource': '<msgsource>\n\t<pua>1</pua>\n\t<eggIncluded>1</eggIncluded>\n\t<silence>0</silence>\n\t<membercount>73</membercount>\n\t<signature>N0_V1_AY/KK3UL|v1_gqSbsR5b</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'PushContent': '锦岚 : 早安', 'NewMsgId': 6927206397415985575, 'MsgSeq': 871404437}
2025-07-28 08:02:27 | INFO | 收到文本消息: 消息ID:1968377233 来自:48097389945@chatroom 发送人:wxid_wlnzvr8ivgd422 @:[] 内容:早安
2025-07-28 08:02:28 | DEBUG | 处理消息内容: '早安'
2025-07-28 08:02:28 | DEBUG | 消息内容 '早安' 不匹配任何命令，忽略
2025-07-28 08:02:30 | DEBUG | 收到消息: {'MsgId': 92750301, 'FromUserName': {'string': '48097389945@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 1, 'Content': {'string': 'wxid_lneb7n23o4lg12:\n障碍与失败，是通往成功最稳靠的踏脚石，肯研究利用它们，便能从失败中培养出成功。早安！'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1753660960, 'MsgSource': '<msgsource>\n\t<pua>1</pua>\n\t<silence>0</silence>\n\t<membercount>73</membercount>\n\t<signature>N0_V1_ogkGcKos|v1_pZ+RitxT</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'PushContent': 'ᡣ : 障碍与失败，是通往成功最稳靠的踏脚石，肯研究利用它们，便能从...', 'NewMsgId': 8586579068316230502, 'MsgSeq': 871404438}
2025-07-28 08:02:30 | INFO | 收到文本消息: 消息ID:92750301 来自:48097389945@chatroom 发送人:wxid_lneb7n23o4lg12 @:[] 内容:障碍与失败，是通往成功最稳靠的踏脚石，肯研究利用它们，便能从失败中培养出成功。早安！
2025-07-28 08:02:30 | DEBUG | 处理消息内容: '障碍与失败，是通往成功最稳靠的踏脚石，肯研究利用它们，便能从失败中培养出成功。早安！'
2025-07-28 08:02:30 | DEBUG | 消息内容 '障碍与失败，是通往成功最稳靠的踏脚石，肯研究利用它们，便能从失败中培养出成功。早安！' 不匹配任何命令，忽略
2025-07-28 08:02:33 | DEBUG | 收到消息: {'MsgId': 2138659415, 'FromUserName': {'string': '48097389945@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 3, 'Content': {'string': 'wxid_lneb7n23o4lg12:\n<?xml version="1.0"?>\n<msg>\n\t<img aeskey="92523e8627329c7eeb12f1558e18a675" encryver="1" cdnthumbaeskey="92523e8627329c7eeb12f1558e18a675" cdnthumburl="3057020100044b304902010002046b3e4bb802032f51490204d931227502046886be22042431343065356132622d613034622d343866322d613162342d356166663534386634353437020405250a020201000405004c4e6100" cdnthumblength="3415" cdnthumbheight="120" cdnthumbwidth="68" cdnmidheight="0" cdnmidwidth="0" cdnhdheight="0" cdnhdwidth="0" cdnmidimgurl="3057020100044b304902010002046b3e4bb802032f51490204d931227502046886be22042431343065356132622d613034622d343866322d613162342d356166663534386634353437020405250a020201000405004c4e6100" length="59094" md5="c5956e686bb05e98c83603ded85e0256" originsourcemd5="670212efb7d9e900811f6069b0cbf358">\n\t\t<secHashInfoBase64 />\n\t\t<live>\n\t\t\t<duration>0</duration>\n\t\t\t<size>0</size>\n\t\t\t<md5 />\n\t\t\t<fileid />\n\t\t\t<hdsize>0</hdsize>\n\t\t\t<hdmd5 />\n\t\t\t<hdfileid />\n\t\t\t<stillimagetimems>0</stillimagetimems>\n\t\t</live>\n\t</img>\n\t<platform_signature />\n\t<imgdatahash />\n\t<ImgSourceInfo>\n\t\t<ImgSourceUrl />\n\t\t<BizType>0</BizType>\n\t</ImgSourceInfo>\n</msg>\n'}, 'Status': 3, 'ImgStatus': 2, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1753660962, 'MsgSource': '<msgsource>\n\t<alnode>\n\t\t<fr>2</fr>\n\t</alnode>\n\t<sec_msg_node>\n\t\t<uuid>b63f982b2dc5d7a3303cfd40f2e17df4_</uuid>\n\t\t<risk-file-flag />\n\t\t<risk-file-md5-list />\n\t</sec_msg_node>\n\t<silence>0</silence>\n\t<membercount>73</membercount>\n\t<signature>N0_V1_U/wrigKF|v1_fE7g1M2H</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'PushContent': 'ᡣ在群聊中发了一张图片', 'NewMsgId': 4052445239278792144, 'MsgSeq': 871404439}
2025-07-28 08:02:33 | INFO | 收到图片消息: 消息ID:2138659415 来自:48097389945@chatroom 发送人:wxid_lneb7n23o4lg12 XML:<?xml version="1.0"?><msg><img aeskey="92523e8627329c7eeb12f1558e18a675" encryver="1" cdnthumbaeskey="92523e8627329c7eeb12f1558e18a675" cdnthumburl="3057020100044b304902010002046b3e4bb802032f51490204d931227502046886be22042431343065356132622d613034622d343866322d613162342d356166663534386634353437020405250a020201000405004c4e6100" cdnthumblength="3415" cdnthumbheight="120" cdnthumbwidth="68" cdnmidheight="0" cdnmidwidth="0" cdnhdheight="0" cdnhdwidth="0" cdnmidimgurl="3057020100044b304902010002046b3e4bb802032f51490204d931227502046886be22042431343065356132622d613034622d343866322d613162342d356166663534386634353437020405250a020201000405004c4e6100" length="59094" md5="c5956e686bb05e98c83603ded85e0256" originsourcemd5="670212efb7d9e900811f6069b0cbf358"><secHashInfoBase64 /><live><duration>0</duration><size>0</size><md5 /><fileid /><hdsize>0</hdsize><hdmd5 /><hdfileid /><stillimagetimems>0</stillimagetimems></live></img><platform_signature /><imgdatahash /><ImgSourceInfo><ImgSourceUrl /><BizType>0</BizType></ImgSourceInfo></msg>
2025-07-28 08:02:33 | INFO | [ImageEcho] 保存图片信息成功，当前群 48097389945@chatroom 已存储 5 张图片
2025-07-28 08:02:33 | INFO | [TimerTask] 缓存图片消息: 2138659415
2025-07-28 08:02:33 | DEBUG | 收到消息: {'MsgId': 1184544199, 'FromUserName': {'string': '48097389945@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 1, 'Content': {'string': 'wxid_n5c0aekjceu621:\n？喵退了'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1753660963, 'MsgSource': '<msgsource>\n\t<pua>1</pua>\n\t<silence>0</silence>\n\t<membercount>73</membercount>\n\t<signature>N0_V1_5YEZpR2l|v1_iuQ2FlNA</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'PushContent': 'ㅤㅤㅤㅤㅤㅤㅤㅤㅤㅤㅤㅤㅤㅤㅤㅤ : ？喵退了', 'NewMsgId': 4867308163929869882, 'MsgSeq': 871404440}
2025-07-28 08:02:33 | INFO | 收到文本消息: 消息ID:1184544199 来自:48097389945@chatroom 发送人:wxid_n5c0aekjceu621 @:[] 内容:？喵退了
2025-07-28 08:02:34 | DEBUG | 处理消息内容: '？喵退了'
2025-07-28 08:02:34 | DEBUG | 消息内容 '？喵退了' 不匹配任何命令，忽略
2025-07-28 08:02:36 | DEBUG | 收到消息: {'MsgId': 224067478, 'FromUserName': {'string': '48097389945@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 1, 'Content': {'string': 'wxid_wlnzvr8ivgd422:\n签到'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1753660964, 'MsgSource': '<msgsource>\n\t<pua>1</pua>\n\t<eggIncluded>1</eggIncluded>\n\t<silence>0</silence>\n\t<membercount>73</membercount>\n\t<signature>N0_V1_uLr4Zcmn|v1_75qyJ5Sv</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'PushContent': '锦岚 : 签到', 'NewMsgId': 282523488507681315, 'MsgSeq': 871404441}
2025-07-28 08:02:36 | INFO | 收到文本消息: 消息ID:224067478 来自:48097389945@chatroom 发送人:wxid_wlnzvr8ivgd422 @:[] 内容:签到
2025-07-28 08:02:36 | DEBUG | 处理消息内容: '签到'
2025-07-28 08:02:36 | DEBUG | 消息内容 '签到' 不匹配任何命令，忽略
2025-07-28 08:02:37 | INFO | 发送文字消息: 对方wxid:48097389945@chatroom at:['wxid_wlnzvr8ivgd422'] 内容:@无妄 
-----XYBot-----
你今天已经签到过了！😠
2025-07-28 08:02:38 | DEBUG | 收到消息: {'MsgId': 1394641425, 'FromUserName': {'string': '48097389945@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 3, 'Content': {'string': 'wxid_srknxij3jka022:\n<?xml version="1.0"?>\n<msg>\n\t<img aeskey="56d85638dea830f5b98694c7d1e8ca9b" encryver="1" cdnthumbaeskey="56d85638dea830f5b98694c7d1e8ca9b" cdnthumburl="3057020100044b304902010002049c5da34302032e6c63020498ab016f02046886be24042466386434663664612d633230662d343232352d383638342d656336646361626334313038020405252a010201000405004c53db00" cdnthumblength="4841" cdnthumbheight="120" cdnthumbwidth="68" cdnmidheight="0" cdnmidwidth="0" cdnhdheight="0" cdnhdwidth="0" cdnmidimgurl="3057020100044b304902010002049c5da34302032e6c63020498ab016f02046886be24042466386434663664612d633230662d343232352d383638342d656336646361626334313038020405252a010201000405004c53db00" length="27563" cdnbigimgurl="3057020100044b304902010002049c5da34302032e6c63020498ab016f02046886be24042466386434663664612d633230662d343232352d383638342d656336646361626334313038020405252a010201000405004c53db00" hdlength="240963" md5="7bd66f739ef1101099598d1065f863c6" hevc_mid_size="27563" originsourcemd5="7bd66f739ef1101099598d1065f863c6">\n\t\t<secHashInfoBase64 />\n\t\t<live>\n\t\t\t<duration>0</duration>\n\t\t\t<size>0</size>\n\t\t\t<md5 />\n\t\t\t<fileid />\n\t\t\t<hdsize>0</hdsize>\n\t\t\t<hdmd5 />\n\t\t\t<hdfileid />\n\t\t\t<stillimagetimems>0</stillimagetimems>\n\t\t</live>\n\t</img>\n\t<appinfo>\n\t\t<appid>wxf0a80d0ac2e82aa7</appid>\n\t\t<version>65</version>\n\t\t<appname>QQ分享</appname>\n\t\t<isforceupdate>0</isforceupdate>\n\t\t<messageaction />\n\t\t<messageext />\n\t\t<mediatagname />\n\t</appinfo>\n\t<platform_signature />\n\t<imgdatahash />\n\t<ImgSourceInfo>\n\t\t<ImgSourceUrl />\n\t\t<BizType>0</BizType>\n\t</ImgSourceInfo>\n</msg>\n'}, 'Status': 3, 'ImgStatus': 2, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1753660964, 'MsgSource': '<msgsource>\n\t<alnode>\n\t\t<fr>2</fr>\n\t</alnode>\n\t<sec_msg_node>\n\t\t<uuid>8cff8bf2213edc7a4c18535ea6a2af74_</uuid>\n\t\t<risk-file-flag />\n\t\t<risk-file-md5-list />\n\t</sec_msg_node>\n\t<silence>0</silence>\n\t<membercount>73</membercount>\n\t<signature>N0_V1_7hIwpjIW|v1_9rR5z36r</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'PushContent': '她在群聊中发了一张图片', 'NewMsgId': 3872773442081863334, 'MsgSeq': 871404442}
2025-07-28 08:02:38 | INFO | 收到图片消息: 消息ID:1394641425 来自:48097389945@chatroom 发送人:wxid_srknxij3jka022 XML:<?xml version="1.0"?><msg><img aeskey="56d85638dea830f5b98694c7d1e8ca9b" encryver="1" cdnthumbaeskey="56d85638dea830f5b98694c7d1e8ca9b" cdnthumburl="3057020100044b304902010002049c5da34302032e6c63020498ab016f02046886be24042466386434663664612d633230662d343232352d383638342d656336646361626334313038020405252a010201000405004c53db00" cdnthumblength="4841" cdnthumbheight="120" cdnthumbwidth="68" cdnmidheight="0" cdnmidwidth="0" cdnhdheight="0" cdnhdwidth="0" cdnmidimgurl="3057020100044b304902010002049c5da34302032e6c63020498ab016f02046886be24042466386434663664612d633230662d343232352d383638342d656336646361626334313038020405252a010201000405004c53db00" length="27563" cdnbigimgurl="3057020100044b304902010002049c5da34302032e6c63020498ab016f02046886be24042466386434663664612d633230662d343232352d383638342d656336646361626334313038020405252a010201000405004c53db00" hdlength="240963" md5="7bd66f739ef1101099598d1065f863c6" hevc_mid_size="27563" originsourcemd5="7bd66f739ef1101099598d1065f863c6"><secHashInfoBase64 /><live><duration>0</duration><size>0</size><md5 /><fileid /><hdsize>0</hdsize><hdmd5 /><hdfileid /><stillimagetimems>0</stillimagetimems></live></img><appinfo><appid>wxf0a80d0ac2e82aa7</appid><version>65</version><appname>QQ分享</appname><isforceupdate>0</isforceupdate><messageaction /><messageext /><mediatagname /></appinfo><platform_signature /><imgdatahash /><ImgSourceInfo><ImgSourceUrl /><BizType>0</BizType></ImgSourceInfo></msg>
2025-07-28 08:02:38 | INFO | [ImageEcho] 保存图片信息成功，当前群 48097389945@chatroom 已存储 5 张图片
2025-07-28 08:02:38 | INFO | [TimerTask] 缓存图片消息: 1394641425
2025-07-28 08:02:38 | DEBUG | 收到消息: {'MsgId': 1693348653, 'FromUserName': {'string': '48097389945@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 1, 'Content': {'string': 'wxid_8l9ymg1mafud12:\n签到未开启'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1753660965, 'MsgSource': '<msgsource>\n\t<silence>0</silence>\n\t<membercount>73</membercount>\n\t<signature>N0_V1_723ttHVI|v1_OwDdIb4w</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'PushContent': '不吃香菜\ue110 : 签到未开启', 'NewMsgId': 8990125309966085109, 'MsgSeq': 871404443}
2025-07-28 08:02:38 | INFO | 收到文本消息: 消息ID:1693348653 来自:48097389945@chatroom 发送人:wxid_8l9ymg1mafud12 @:[] 内容:签到未开启
2025-07-28 08:02:39 | DEBUG | 处理消息内容: '签到未开启'
2025-07-28 08:02:39 | DEBUG | 消息内容 '签到未开启' 不匹配任何命令，忽略
2025-07-28 08:02:41 | DEBUG | 收到消息: {'MsgId': 1958414779, 'FromUserName': {'string': '48097389945@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 3, 'Content': {'string': 'wxid_lneb7n23o4lg12:\n<?xml version="1.0"?>\n<msg>\n\t<img aeskey="5d6627e2fa3a2c65e335cdb9ea0e9e6b" encryver="1" cdnthumbaeskey="5d6627e2fa3a2c65e335cdb9ea0e9e6b" cdnthumburl="3057020100044b304902010002046b3e4bb802032f51490204d931227502046886be27042435386133323338622d386236632d343263392d616338372d613261326136396465353564020405250a020201000405004c55cd00" cdnthumblength="1895" cdnthumbheight="144" cdnthumbwidth="67" cdnmidheight="0" cdnmidwidth="0" cdnhdheight="0" cdnhdwidth="0" cdnmidimgurl="3057020100044b304902010002046b3e4bb802032f51490204d931227502046886be27042435386133323338622d386236632d343263392d616338372d613261326136396465353564020405250a020201000405004c55cd00" length="20796" md5="0d9df0877eaf15e3aadc552ad92f4088" originsourcemd5="7a0992c622c3a1846e2d454486996114">\n\t\t<secHashInfoBase64 />\n\t\t<live>\n\t\t\t<duration>0</duration>\n\t\t\t<size>0</size>\n\t\t\t<md5 />\n\t\t\t<fileid />\n\t\t\t<hdsize>0</hdsize>\n\t\t\t<hdmd5 />\n\t\t\t<hdfileid />\n\t\t\t<stillimagetimems>0</stillimagetimems>\n\t\t</live>\n\t</img>\n\t<platform_signature />\n\t<imgdatahash />\n\t<ImgSourceInfo>\n\t\t<ImgSourceUrl />\n\t\t<BizType>0</BizType>\n\t</ImgSourceInfo>\n</msg>\n'}, 'Status': 3, 'ImgStatus': 2, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1753660967, 'MsgSource': '<msgsource>\n\t<alnode>\n\t\t<fr>2</fr>\n\t</alnode>\n\t<sec_msg_node>\n\t\t<uuid>cee5af66a205746901e2ef187c27da16_</uuid>\n\t\t<risk-file-flag />\n\t\t<risk-file-md5-list />\n\t</sec_msg_node>\n\t<silence>0</silence>\n\t<membercount>73</membercount>\n\t<signature>N0_V1_H6OcnLRt|v1_ZMc5d8l6</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'PushContent': 'ᡣ在群聊中发了一张图片', 'NewMsgId': 4267068391642900360, 'MsgSeq': 871404445}
2025-07-28 08:02:41 | INFO | 收到图片消息: 消息ID:1958414779 来自:48097389945@chatroom 发送人:wxid_lneb7n23o4lg12 XML:<?xml version="1.0"?><msg><img aeskey="5d6627e2fa3a2c65e335cdb9ea0e9e6b" encryver="1" cdnthumbaeskey="5d6627e2fa3a2c65e335cdb9ea0e9e6b" cdnthumburl="3057020100044b304902010002046b3e4bb802032f51490204d931227502046886be27042435386133323338622d386236632d343263392d616338372d613261326136396465353564020405250a020201000405004c55cd00" cdnthumblength="1895" cdnthumbheight="144" cdnthumbwidth="67" cdnmidheight="0" cdnmidwidth="0" cdnhdheight="0" cdnhdwidth="0" cdnmidimgurl="3057020100044b304902010002046b3e4bb802032f51490204d931227502046886be27042435386133323338622d386236632d343263392d616338372d613261326136396465353564020405250a020201000405004c55cd00" length="20796" md5="0d9df0877eaf15e3aadc552ad92f4088" originsourcemd5="7a0992c622c3a1846e2d454486996114"><secHashInfoBase64 /><live><duration>0</duration><size>0</size><md5 /><fileid /><hdsize>0</hdsize><hdmd5 /><hdfileid /><stillimagetimems>0</stillimagetimems></live></img><platform_signature /><imgdatahash /><ImgSourceInfo><ImgSourceUrl /><BizType>0</BizType></ImgSourceInfo></msg>
2025-07-28 08:02:41 | INFO | [ImageEcho] 保存图片信息成功，当前群 48097389945@chatroom 已存储 5 张图片
2025-07-28 08:02:41 | INFO | [TimerTask] 缓存图片消息: 1958414779
2025-07-28 08:02:42 | DEBUG | 收到消息: {'MsgId': 133167608, 'FromUserName': {'string': '48097389945@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 1, 'Content': {'string': 'wxid_wlnzvr8ivgd422:\n洞房'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1753660970, 'MsgSource': '<msgsource>\n\t<pua>1</pua>\n\t<eggIncluded>1</eggIncluded>\n\t<silence>0</silence>\n\t<membercount>73</membercount>\n\t<signature>N0_V1_3BHYvLPr|v1_sTvXjhGa</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'PushContent': '锦岚 : 洞房', 'NewMsgId': 1469751528194898214, 'MsgSeq': 871404447}
2025-07-28 08:02:42 | INFO | 收到文本消息: 消息ID:133167608 来自:48097389945@chatroom 发送人:wxid_wlnzvr8ivgd422 @:[] 内容:洞房
2025-07-28 08:02:42 | DEBUG | 处理消息内容: '洞房'
2025-07-28 08:02:42 | DEBUG | 消息内容 '洞房' 不匹配任何命令，忽略
2025-07-28 08:02:44 | DEBUG | 收到消息: {'MsgId': 2110316390, 'FromUserName': {'string': '48097389945@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 1, 'Content': {'string': 'wxid_q35rkzgkjvlv12:\n 💕 [锦岚]👩\u200d❤\u200d👨[小爱]💕\n💓═☘︎═•洞💗房•═☘︎═💓\n👩\u200d❤\u200d👨关系：情侣\n⛺地点：洗手间\n😍姿势：观音坐莲\n😮\u200d💨结果：失败\n❤\u200d🔥状态：骨折\n💓═☘︎══•💗•══☘︎═💓\n🔮魅力:-23\n[玫瑰]恩爱:-8 \n🕒下次:2025-07-28 08:22:52'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1753660972, 'MsgSource': '<msgsource>\n\t<silence>0</silence>\n\t<membercount>73</membercount>\n\t<signature>N0_V1_cGK9gJx7|v1_z25HevT5</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'PushContent': '摇月 :  \ue327 [锦岚]\ue005\u200d\ue022\u200d\ue004[小爱]\ue327\n\ue327═☘︎═•洞\ue328房•═☘︎═...', 'NewMsgId': 1649858871231934354, 'MsgSeq': 871404448}
2025-07-28 08:02:44 | INFO | 收到文本消息: 消息ID:2110316390 来自:48097389945@chatroom 发送人:wxid_q35rkzgkjvlv12 @:[] 内容: 💕 [锦岚]👩‍❤‍👨[小爱]💕
💓═☘︎═•洞💗房•═☘︎═💓
👩‍❤‍👨关系：情侣
⛺地点：洗手间
😍姿势：观音坐莲
😮‍💨结果：失败
❤‍🔥状态：骨折
💓═☘︎══•💗•══☘︎═💓
🔮魅力:-23
[玫瑰]恩爱:-8 
🕒下次:2025-07-28 08:22:52
2025-07-28 08:02:45 | DEBUG | 处理消息内容: '💕 [锦岚]👩‍❤‍👨[小爱]💕
💓═☘︎═•洞💗房•═☘︎═💓
👩‍❤‍👨关系：情侣
⛺地点：洗手间
😍姿势：观音坐莲
😮‍💨结果：失败
❤‍🔥状态：骨折
💓═☘︎══•💗•══☘︎═💓
🔮魅力:-23
[玫瑰]恩爱:-8 
🕒下次:2025-07-28 08:22:52'
2025-07-28 08:02:45 | DEBUG | 消息内容 '💕 [锦岚]👩‍❤‍👨[小爱]💕
💓═☘︎═•洞💗房•═☘︎═💓
👩‍❤‍👨关系：情侣
⛺地点：洗手间
😍姿势：观音坐莲
😮‍💨结果：失败
❤‍🔥状态：骨折
💓═☘︎══•💗•══☘︎═💓
🔮魅力:-23
[玫瑰]恩爱:-8 
🕒下次:2025-07-28 08:22:52' 不匹配任何命令，忽略
2025-07-28 08:02:46 | DEBUG | 收到消息: {'MsgId': 1700320259, 'FromUserName': {'string': '48097389945@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 1, 'Content': {'string': 'wxid_8l9ymg1mafud12:\n「锦岚」[爱心]「小爱」\n地点：卫生间\n活动：双修\n结果：成功\n羞羞：嘿嘿嘿~\n恩爱值增加100\n\n下次:2025-07-28 08:12:51'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1753660972, 'MsgSource': '<msgsource>\n\t<silence>0</silence>\n\t<membercount>73</membercount>\n\t<signature>N0_V1_Qrx5VZ4u|v1_MeTbIc3V</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'PushContent': '不吃香菜\ue110 : 「锦岚」[爱心]「小爱」\n地点：卫生间\n活动：双修\n结果：成功\n羞羞...', 'NewMsgId': 1421413527197132738, 'MsgSeq': 871404449}
2025-07-28 08:02:46 | INFO | 收到文本消息: 消息ID:1700320259 来自:48097389945@chatroom 发送人:wxid_8l9ymg1mafud12 @:[] 内容:「锦岚」[爱心]「小爱」
地点：卫生间
活动：双修
结果：成功
羞羞：嘿嘿嘿~
恩爱值增加100

下次:2025-07-28 08:12:51
2025-07-28 08:02:47 | DEBUG | 处理消息内容: '「锦岚」[爱心]「小爱」
地点：卫生间
活动：双修
结果：成功
羞羞：嘿嘿嘿~
恩爱值增加100

下次:2025-07-28 08:12:51'
2025-07-28 08:02:47 | DEBUG | 消息内容 '「锦岚」[爱心]「小爱」
地点：卫生间
活动：双修
结果：成功
羞羞：嘿嘿嘿~
恩爱值增加100

下次:2025-07-28 08:12:51' 不匹配任何命令，忽略
2025-07-28 08:02:49 | DEBUG | 收到消息: {'MsgId': 1362220445, 'FromUserName': {'string': '48097389945@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 47, 'Content': {'string': 'last--exile:\n<msg><emoji fromusername="last--exile" tousername="48097389945@chatroom" type="2" idbuffer="media:0_0" md5="455c7859ba566dc6767cb330625d52d6" len="958428" productid="" androidmd5="455c7859ba566dc6767cb330625d52d6" androidlen="958428" s60v3md5="455c7859ba566dc6767cb330625d52d6" s60v3len="958428" s60v5md5="455c7859ba566dc6767cb330625d52d6" s60v5len="958428" cdnurl="http://vweixinf.tc.qq.com/110/20401/stodownload?m=455c7859ba566dc6767cb330625d52d6&amp;filekey=30440201010430302e02016e0402535a0420343535633738353962613536366463363736376362333330363235643532643602030e9fdc040d00000004627466730000000132&amp;hy=SZ&amp;storeid=2683fd4400007d5ee7de4992c0000006e01004fb1535a0026dbc1e685e41f4&amp;ef=1&amp;bizid=1022" designerid="" thumburl="" encrypturl="http://vweixinf.tc.qq.com/110/20402/stodownload?m=6c3eb07a198aa37553bec96501dc99d2&amp;filekey=30440201010430302e02016e0402535a0420366333656230376131393861613337353533626563393635303164633939643202030e9fe0040d00000004627466730000000132&amp;hy=SZ&amp;storeid=2683fd44000096e1c7de4992c0000006e02004fb2535a0026dbc1e685e4221&amp;ef=2&amp;bizid=1022" aeskey="9c352ce584ac4e9583c3f43f720e0c92" externurl="http://vweixinf.tc.qq.com/110/20403/stodownload?m=964a333b05a3898e7259f68f9964a19c&amp;filekey=30440201010430302e02016e0402535a04203936346133333362303561333839386537323539663638663939363461313963020300add0040d00000004627466730000000132&amp;hy=SZ&amp;storeid=2683fd440000ae6d67de4992c0000006e03004fb3535a0026dbc1e685e423d&amp;ef=3&amp;bizid=1022" externmd5="53f492c27fc62e0f60b39ff7b0d2677a" width="400" height="400" tpurl="" tpauthkey="" attachedtext="" attachedtextcolor="" lensid="" emojiattr="" linkid="" desc=""></emoji></msg>'}, 'Status': 3, 'ImgStatus': 2, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1753660975, 'MsgSource': '<msgsource>\n\t<silence>0</silence>\n\t<membercount>73</membercount>\n\t<signature>N0_V1_2lgHJ0Fl|v1_En9ITXgO</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'PushContent': '亮在群聊中发了一个表情', 'NewMsgId': 5832789328140638889, 'MsgSeq': 871404450}
2025-07-28 08:02:49 | INFO | 收到表情消息: 消息ID:1362220445 来自:48097389945@chatroom 发送人:last--exile MD5:455c7859ba566dc6767cb330625d52d6 大小:958428
2025-07-28 08:02:49 | DEBUG | [DoubaoImageRecognition] 缓存表情消息: 5832789328140638889
2025-07-28 08:02:58 | DEBUG | 收到消息: {'MsgId': 1404942248, 'FromUserName': {'string': '48097389945@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 49, 'Content': {'string': 'zll953369865:\n<?xml version="1.0"?>\n<msg>\n\t<appmsg appid="" sdkver="0">\n\t\t<title>没有她群不热闹啊</title>\n\t\t<des />\n\t\t<username />\n\t\t<action>view</action>\n\t\t<type>57</type>\n\t\t<showtype>0</showtype>\n\t\t<content />\n\t\t<url />\n\t\t<lowurl />\n\t\t<forwardflag>0</forwardflag>\n\t\t<dataurl />\n\t\t<lowdataurl />\n\t\t<contentattr>0</contentattr>\n\t\t<streamvideo>\n\t\t\t<streamvideourl />\n\t\t\t<streamvideototaltime>0</streamvideototaltime>\n\t\t\t<streamvideotitle />\n\t\t\t<streamvideowording />\n\t\t\t<streamvideoweburl />\n\t\t\t<streamvideothumburl />\n\t\t\t<streamvideoaduxinfo />\n\t\t\t<streamvideopublishid />\n\t\t</streamvideo>\n\t\t<canvasPageItem>\n\t\t\t<canvasPageXml><![CDATA[]]></canvasPageXml>\n\t\t</canvasPageItem>\n\t\t<refermsg>\n\t\t\t<type>1</type>\n\t\t\t<svrid>2653675008607185469</svrid>\n\t\t\t<fromusr>48097389945@chatroom</fromusr>\n\t\t\t<chatusr>xiaomaochong</chatusr>\n\t\t\t<displayname>小爱</displayname>\n\t\t\t<msgsource>&lt;msgsource&gt;\n\t&lt;pua&gt;1&lt;/pua&gt;\n\t&lt;silence&gt;1&lt;/silence&gt;\n\t&lt;membercount&gt;73&lt;/membercount&gt;\n\t&lt;signature&gt;N0_V1_CVFk+atk|v1_zCLAOfAz&lt;/signature&gt;\n\t&lt;tmp_node&gt;\n\t\t&lt;publisher-id&gt;&lt;/publisher-id&gt;\n\t&lt;/tmp_node&gt;\n&lt;/msgsource&gt;\n</msgsource>\n\t\t\t<content>\n你都没微信还惦记人家干嘛[抠鼻]</content>\n\t\t\t<strid />\n\t\t\t<createtime>1753660925</createtime>\n\t\t</refermsg>\n\t\t<appattach>\n\t\t\t<totallen>0</totallen>\n\t\t\t<attachid />\n\t\t\t<cdnattachurl />\n\t\t\t<emoticonmd5 />\n\t\t\t<aeskey />\n\t\t\t<fileext />\n\t\t\t<islargefilemsg>0</islargefilemsg>\n\t\t</appattach>\n\t\t<extinfo />\n\t\t<androidsource>0</androidsource>\n\t\t<thumburl />\n\t\t<mediatagname />\n\t\t<messageaction><![CDATA[]]></messageaction>\n\t\t<messageext><![CDATA[]]></messageext>\n\t\t<emoticongift>\n\t\t\t<packageflag>0</packageflag>\n\t\t\t<packageid />\n\t\t</emoticongift>\n\t\t<emoticonshared>\n\t\t\t<packageflag>0</packageflag>\n\t\t\t<packageid />\n\t\t</emoticonshared>\n\t\t<designershared>\n\t\t\t<designeruin>0</designeruin>\n\t\t\t<designername>null</designername>\n\t\t\t<designerrediretcturl><![CDATA[null]]></designerrediretcturl>\n\t\t</designershared>\n\t\t<emotionpageshared>\n\t\t\t<tid>0</tid>\n\t\t\t<title>null</title>\n\t\t\t<desc>null</desc>\n\t\t\t<iconUrl><![CDATA[null]]></iconUrl>\n\t\t\t<secondUrl>null</secondUrl>\n\t\t\t<pageType>0</pageType>\n\t\t\t<setKey>null</setKey>\n\t\t</emotionpageshared>\n\t\t<webviewshared>\n\t\t\t<shareUrlOriginal />\n\t\t\t<shareUrlOpen />\n\t\t\t<jsAppId />\n\t\t\t<publisherId />\n\t\t\t<publisherReqId />\n\t\t</webviewshared>\n\t\t<template_id />\n\t\t<md5 />\n\t\t<websearch>\n\t\t\t<rec_category>0</rec_category>\n\t\t\t<channelId>0</channelId>\n\t\t</websearch>\n\t\t<weappinfo>\n\t\t\t<username />\n\t\t\t<appid />\n\t\t\t<appservicetype>0</appservicetype>\n\t\t\t<secflagforsinglepagemode>0</secflagforsinglepagemode>\n\t\t\t<videopageinfo>\n\t\t\t\t<thumbwidth>0</thumbwidth>\n\t\t\t\t<thumbheight>0</thumbheight>\n\t\t\t\t<fromopensdk>0</fromopensdk>\n\t\t\t</videopageinfo>\n\t\t</weappinfo>\n\t\t<statextstr />\n\t\t<musicShareItem>\n\t\t\t<musicDuration>0</musicDuration>\n\t\t</musicShareItem>\n\t\t<finderLiveProductShare>\n\t\t\t<finderLiveID><![CDATA[]]></finderLiveID>\n\t\t\t<finderUsername><![CDATA[]]></finderUsername>\n\t\t\t<finderObjectID><![CDATA[]]></finderObjectID>\n\t\t\t<finderNonceID><![CDATA[]]></finderNonceID>\n\t\t\t<liveStatus><![CDATA[]]></liveStatus>\n\t\t\t<appId><![CDATA[]]></appId>\n\t\t\t<pagePath><![CDATA[]]></pagePath>\n\t\t\t<productId><![CDATA[]]></productId>\n\t\t\t<coverUrl><![CDATA[]]></coverUrl>\n\t\t\t<productTitle><![CDATA[]]></productTitle>\n\t\t\t<marketPrice><![CDATA[0]]></marketPrice>\n\t\t\t<sellingPrice><![CDATA[0]]></sellingPrice>\n\t\t\t<platformHeadImg><![CDATA[]]></platformHeadImg>\n\t\t\t<platformName><![CDATA[]]></platformName>\n\t\t\t<shopWindowId><![CDATA[]]></shopWindowId>\n\t\t\t<flashSalePrice><![CDATA[0]]></flashSalePrice>\n\t\t\t<flashSaleEndTime><![CDATA[0]]></flashSaleEndTime>\n\t\t\t<ecSource><![CDATA[]]></ecSource>\n\t\t\t<sellingPriceWording><![CDATA[]]></sellingPriceWording>\n\t\t\t<platformIconURL><![CDATA[]]></platformIconURL>\n\t\t\t<firstProductTagURL><![CDATA[]]></firstProductTagURL>\n\t\t\t<firstProductTagAspectRatioString><![CDATA[0.0]]></firstProductTagAspectRatioString>\n\t\t\t<secondProductTagURL><![CDATA[]]></secondProductTagURL>\n\t\t\t<secondProductTagAspectRatioString><![CDATA[0.0]]></secondProductTagAspectRatioString>\n\t\t\t<firstGuaranteeWording><![CDATA[]]></firstGuaranteeWording>\n\t\t\t<secondGuaranteeWording><![CDATA[]]></secondGuaranteeWording>\n\t\t\t<thirdGuaranteeWording><![CDATA[]]></thirdGuaranteeWording>\n\t\t\t<isPriceBeginShow>false</isPriceBeginShow>\n\t\t\t<lastGMsgID><![CDATA[]]></lastGMsgID>\n\t\t\t<promoterKey><![CDATA[]]></promoterKey>\n\t\t\t<discountWording><![CDATA[]]></discountWording>\n\t\t\t<priceSuffixDescription><![CDATA[]]></priceSuffixDescription>\n\t\t\t<productCardKey><![CDATA[]]></productCardKey>\n\t\t\t<isWxShop><![CDATA[]]></isWxShop>\n\t\t\t<brandIconUrl><![CDATA[]]></brandIconUrl>\n\t\t\t<rIconUrl><![CDATA[]]></rIconUrl>\n\t\t\t<rIconUrlDarkMode><![CDATA[]]></rIconUrlDarkMode>\n\t\t\t<topShopIconUrl><![CDATA[]]></topShopIconUrl>\n\t\t\t<topShopIconUrlDarkMode><![CDATA[]]></topShopIconUrlDarkMode>\n\t\t\t<simplifyTopShopIconUrl><![CDATA[]]></simplifyTopShopIconUrl>\n\t\t\t<simplifyTopShopIconUrlDarkmode><![CDATA[]]></simplifyTopShopIconUrlDarkmode>\n\t\t\t<topShopIconWidth><![CDATA[0]]></topShopIconWidth>\n\t\t\t<topShopIconHeight><![CDATA[0]]></topShopIconHeight>\n\t\t\t<simplifyTopShopIconWidth><![CDATA[0]]></simplifyTopShopIconWidth>\n\t\t\t<simplifyTopShopIconHeight><![CDATA[0]]></simplifyTopShopIconHeight>\n\t\t\t<showBoxItemStringList />\n\t\t</finderLiveProductShare>\n\t\t<finderOrder>\n\t\t\t<appID><![CDATA[]]></appID>\n\t\t\t<orderID><![CDATA[]]></orderID>\n\t\t\t<path><![CDATA[]]></path>\n\t\t\t<priceWording><![CDATA[]]></priceWording>\n\t\t\t<stateWording><![CDATA[]]></stateWording>\n\t\t\t<productImageURL><![CDATA[]]></productImageURL>\n\t\t\t<products><![CDATA[]]></products>\n\t\t\t<productsCount><![CDATA[0]]></productsCount>\n\t\t\t<orderType><![CDATA[0]]></orderType>\n\t\t\t<newPriceWording><![CDATA[]]></newPriceWording>\n\t\t\t<newStateWording><![CDATA[]]></newStateWording>\n\t\t\t<useNewWording><![CDATA[0]]></useNewWording>\n\t\t</finderOrder>\n\t\t<finderShopWindowShare>\n\t\t\t<finderUsername><![CDATA[]]></finderUsername>\n\t\t\t<avatar><![CDATA[]]></avatar>\n\t\t\t<nickname><![CDATA[]]></nickname>\n\t\t\t<commodityInStockCount><![CDATA[]]></commodityInStockCount>\n\t\t\t<appId><![CDATA[]]></appId>\n\t\t\t<path><![CDATA[]]></path>\n\t\t\t<appUsername><![CDATA[]]></appUsername>\n\t\t\t<query><![CDATA[]]></query>\n\t\t\t<liteAppId><![CDATA[]]></liteAppId>\n\t\t\t<liteAppPath><![CDATA[]]></liteAppPath>\n\t\t\t<liteAppQuery><![CDATA[]]></liteAppQuery>\n\t\t\t<platformTagURL><![CDATA[]]></platformTagURL>\n\t\t\t<saleWording><![CDATA[]]></saleWording>\n\t\t\t<lastGMsgID><![CDATA[]]></lastGMsgID>\n\t\t\t<profileTypeWording><![CDATA[]]></profileTypeWording>\n\t\t\t<saleWordingExtra><![CDATA[]]></saleWordingExtra>\n\t\t\t<isWxShop><![CDATA[]]></isWxShop>\n\t\t\t<platformIconUrl><![CDATA[]]></platformIconUrl>\n\t\t\t<brandIconUrl><![CDATA[]]></brandIconUrl>\n\t\t\t<description><![CDATA[]]></description>\n\t\t\t<backgroundUrl><![CDATA[]]></backgroundUrl>\n\t\t\t<darkModePlatformIconUrl><![CDATA[]]></darkModePlatformIconUrl>\n\t\t\t<rIconUrl><![CDATA[]]></rIconUrl>\n\t\t\t<rIconUrlDarkMode><![CDATA[]]></rIconUrlDarkMode>\n\t\t\t<rWords><![CDATA[]]></rWords>\n\t\t\t<topShopIconUrl><![CDATA[]]></topShopIconUrl>\n\t\t\t<topShopIconUrlDarkMode><![CDATA[]]></topShopIconUrlDarkMode>\n\t\t\t<simplifyTopShopIconUrl><![CDATA[]]></simplifyTopShopIconUrl>\n\t\t\t<simplifyTopShopIconUrlDarkmode><![CDATA[]]></simplifyTopShopIconUrlDarkmode>\n\t\t\t<topShopIconWidth><![CDATA[0]]></topShopIconWidth>\n\t\t\t<topShopIconHeight><![CDATA[0]]></topShopIconHeight>\n\t\t\t<simplifyTopShopIconWidth><![CDATA[0]]></simplifyTopShopIconWidth>\n\t\t\t<simplifyTopShopIconHeight><![CDATA[0]]></simplifyTopShopIconHeight>\n\t\t\t<reputationInfo>\n\t\t\t\t<hasReputationInfo>0</hasReputationInfo>\n\t\t\t\t<reputationScore>0</reputationScore>\n\t\t\t\t<reputationWording />\n\t\t\t\t<reputationTextColor />\n\t\t\t\t<reputationLevelWording />\n\t\t\t\t<reputationBackgroundColor />\n\t\t\t</reputationInfo>\n\t\t\t<productImageURLList />\n\t\t</finderShopWindowShare>\n\t\t<findernamecard>\n\t\t\t<username />\n\t\t\t<avatar><![CDATA[]]></avatar>\n\t\t\t<nickname />\n\t\t\t<auth_job />\n\t\t\t<auth_icon>0</auth_icon>\n\t\t\t<auth_icon_url />\n\t\t\t<ecSource><![CDATA[]]></ecSource>\n\t\t\t<lastGMsgID><![CDATA[]]></lastGMsgID>\n\t\t</findernamecard>\n\t\t<finderGuarantee>\n\t\t\t<scene><![CDATA[0]]></scene>\n\t\t</finderGuarantee>\n\t\t<directshare>0</directshare>\n\t\t<gamecenter>\n\t\t\t<namecard>\n\t\t\t\t<iconUrl />\n\t\t\t\t<name />\n\t\t\t\t<desc />\n\t\t\t\t<tail />\n\t\t\t\t<jumpUrl />\n\t\t\t\t<liteappId />\n\t\t\t\t<liteappPath />\n\t\t\t\t<liteappQuery />\n\t\t\t\t<liteappMinVersion />\n\t\t\t</namecard>\n\t\t</gamecenter>\n\t\t<patMsg>\n\t\t\t<chatUser />\n\t\t\t<records>\n\t\t\t\t<recordNum>0</recordNum>\n\t\t\t</records>\n\t\t</patMsg>\n\t\t<secretmsg>\n\t\t\t<issecretmsg>0</issecretmsg>\n\t\t</secretmsg>\n\t\t<referfromscene>0</referfromscene>\n\t\t<gameshare>\n\t\t\t<liteappext>\n\t\t\t\t<liteappbizdata />\n\t\t\t\t<priority>0</priority>\n\t\t\t</liteappext>\n\t\t\t<appbrandext>\n\t\t\t\t<litegameinfo />\n\t\t\t\t<priority>-1</priority>\n\t\t\t</appbrandext>\n\t\t\t<gameshareid />\n\t\t\t<sharedata />\n\t\t\t<isvideo>0</isvideo>\n\t\t\t<duration>-1</duration>\n\t\t\t<isexposed>0</isexposed>\n\t\t\t<readtext />\n\t\t</gameshare>\n\t\t<tingChatRoomItem>\n\t\t\t<type>0</type>\n\t\t\t<categoryItem>null</categoryItem>\n\t\t\t<categoryId />\n\t\t</tingChatRoomItem>\n\t\t<mpsharetrace>\n\t\t\t<hasfinderelement>0</hasfinderelement>\n\t\t\t<lastgmsgid />\n\t\t</mpsharetrace>\n\t\t<wxgamecard>\n\t\t\t<framesetname />\n\t\t\t<mbcarddata />\n\t\t\t<minpkgversion />\n\t\t\t<clientextinfo />\n\t\t\t<mbcardheight>0</mbcardheight>\n\t\t\t<isoldversion>0</isoldversion>\n\t\t</wxgamecard>\n\t\t<ecskfcard>\n\t\t\t<framesetname />\n\t\t\t<mbcarddata />\n\t\t\t<minupdateunixtimestamp>0</minupdateunixtimestamp>\n\t\t\t<needheader>false</needheader>\n\t\t\t<summary />\n\t\t</ecskfcard>\n\t\t<liteapp>\n\t\t\t<id>null</id>\n\t\t\t<path />\n\t\t\t<query />\n\t\t\t<istransparent>0</istransparent>\n\t\t\t<hideicon>0</hideicon>\n\t\t\t<forbidforward>0</forbidforward>\n\t\t</liteapp>\n\t\t<opensdk_share_is_modified>0</opensdk_share_is_modified>\n\t</appmsg>\n\t<fromusername>zll953369865</fromusername>\n\t<scene>0</scene>\n\t<appinfo>\n\t\t<version>1</version>\n\t\t<appname></appname>\n\t</appinfo>\n\t<commenturl></commenturl>\n</msg>\n'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1753660988, 'MsgSource': '<msgsource>\n\t<alnode>\n\t\t<fr>4</fr>\n\t</alnode>\n\t<eggIncluded>1</eggIncluded>\n\t<sec_msg_node>\n\t\t<uuid>dd726390d1f764aaec39c1c98f773061_</uuid>\n\t\t<risk-file-flag />\n\t\t<risk-file-md5-list />\n\t</sec_msg_node>\n\t<silence>0</silence>\n\t<membercount>73</membercount>\n\t<signature>N0_V1_ALE70vOi|v1_YmdCe4q6</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'PushContent': '【微信红包】收到一个微信红包 : 没有她群不热闹啊', 'NewMsgId': 5722555710902462677, 'MsgSeq': 871404451}
2025-07-28 08:02:58 | DEBUG | 从群聊消息中提取发送者: zll953369865
2025-07-28 08:02:58 | DEBUG | 使用已解析的XML处理引用消息
2025-07-28 08:02:58 | INFO | 收到引用消息: 消息ID:1404942248 来自:48097389945@chatroom 发送人:zll953369865 内容:没有她群不热闹啊 引用类型:1
2025-07-28 08:02:59 | INFO | [DouBaoImageToImage] 收到引用消息: 没有她群不热闹啊
2025-07-28 08:02:59 | INFO | [TimerTask] 收到引用消息调试信息:
2025-07-28 08:02:59 | INFO |   - 消息内容: 没有她群不热闹啊
2025-07-28 08:02:59 | INFO |   - 群组ID: 48097389945@chatroom
2025-07-28 08:02:59 | INFO |   - 发送人: zll953369865
2025-07-28 08:02:59 | INFO |   - 引用信息: {'MsgType': 1, 'Content': '\n你都没微信还惦记人家干嘛[抠鼻]', 'Msgid': '2653675008607185469', 'NewMsgId': '2653675008607185469', 'ToWxid': 'wxid_4usgcju5ey9q29', 'FromWxid': '48097389945@chatroom', 'Nickname': '小爱', 'MsgSource': '<msgsource>\n\t<pua>1</pua>\n\t<silence>1</silence>\n\t<membercount>73</membercount>\n\t<signature>N0_V1_CVFk+atk|v1_zCLAOfAz</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'Createtime': '1753660925', 'SenderWxid': 'zll953369865'}
2025-07-28 08:02:59 | INFO |   - 引用消息ID: 
2025-07-28 08:02:59 | INFO |   - 引用消息类型: 
2025-07-28 08:02:59 | INFO |   - 引用消息内容: 
你都没微信还惦记人家干嘛[抠鼻]
2025-07-28 08:02:59 | INFO |   - 引用消息发送人: zll953369865
2025-07-28 08:03:00 | DEBUG | 收到消息: {'MsgId': 1377501958, 'FromUserName': {'string': '48097389945@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 47, 'Content': {'string': 'zll953369865:\n<msg><emoji fromusername = "zll953369865" tousername = "48097389945@chatroom" type="2" idbuffer="media:0_0" md5="f12fb0d245dee01ba932751f51d240a6" len = "6419748" productid="" androidmd5="f12fb0d245dee01ba932751f51d240a6" androidlen="6419748" s60v3md5 = "f12fb0d245dee01ba932751f51d240a6" s60v3len="6419748" s60v5md5 = "f12fb0d245dee01ba932751f51d240a6" s60v5len="6419748" cdnurl = "http://wxapp.tc.qq.com/262/20304/stodownload?m=f12fb0d245dee01ba932751f51d240a6&amp;filekey=30350201010421301f02020106040253480410f12fb0d245dee01ba932751f51d240a6020361f524040d00000004627466730000000132&amp;hy=SH&amp;storeid=26322e04e0000ae7c000000000000010600004f50534815668b40b0f4f0d6a&amp;bizid=1023" designerid = "" thumburl = "" encrypturl = "http://wxapp.tc.qq.com/262/20304/stodownload?m=8c06c374f7202ef358c51d729cb31060&amp;filekey=30350201010421301f020201060402535a04108c06c374f7202ef358c51d729cb31060020361f530040d00000004627466730000000132&amp;hy=SZ&amp;storeid=26322e04f000dc5d5000000000000010600004f50535a01b2788096b20ea44&amp;bizid=1023" aeskey= "c3beea06a4ddc660b4b4dc9c29e2008b" externurl = "http://wxapp.tc.qq.com/262/20304/stodownload?m=88d038f3e3a9841ffaff3245393da7d2&amp;filekey=30350201010421301f0202010604025348041088d038f3e3a9841ffaff3245393da7d202030746d0040d00000004627466730000000132&amp;hy=SH&amp;storeid=26322e0500008cc77000000000000010600004f5053482cd67b40b7c4e0c54&amp;bizid=1023" externmd5 = "e18bac9593d2deee5d71380595416fc7" width= "300" height= "300" tpurl= "" tpauthkey= "" attachedtext= "" attachedtextcolor= "" lensid= "" emojiattr= "" linkid= "" desc= "" activityid = "Selfie:3d06bfe8492aad408d25a13b2bf51505" ></emoji>  </msg>'}, 'Status': 3, 'ImgStatus': 2, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1753660990, 'MsgSource': '<msgsource>\n\t<silence>0</silence>\n\t<membercount>73</membercount>\n\t<signature>N0_V1_PgdNQ6bJ|v1_u9C8Dojm</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'PushContent': '【微信红包】收到一个微信红包在群聊中发了一个表情', 'NewMsgId': 464260209220662311, 'MsgSeq': 871404452}
2025-07-28 08:03:00 | INFO | 收到表情消息: 消息ID:1377501958 来自:48097389945@chatroom 发送人:zll953369865 MD5:f12fb0d245dee01ba932751f51d240a6 大小:6419748
2025-07-28 08:03:01 | DEBUG | [DoubaoImageRecognition] 缓存表情消息: 464260209220662311
2025-07-28 08:03:05 | DEBUG | 收到消息: {'MsgId': 1451540289, 'FromUserName': {'string': '48097389945@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 1, 'Content': {'string': 'xiaomaochong:\n洞房'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1753660995, 'MsgSource': '<msgsource>\n\t<pua>1</pua>\n\t<silence>0</silence>\n\t<membercount>73</membercount>\n\t<signature>N0_V1_4yj2Q0se|v1_QqL4RH6f</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'PushContent': '小爱 : 洞房', 'NewMsgId': 2339968958889990686, 'MsgSeq': 871404453}
2025-07-28 08:03:05 | INFO | 收到文本消息: 消息ID:1451540289 来自:48097389945@chatroom 发送人:xiaomaochong @:[] 内容:洞房
2025-07-28 08:03:05 | DEBUG | 处理消息内容: '洞房'
2025-07-28 08:03:05 | DEBUG | 消息内容 '洞房' 不匹配任何命令，忽略
2025-07-28 08:03:07 | DEBUG | 收到消息: {'MsgId': 1366173915, 'FromUserName': {'string': '48097389945@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 1, 'Content': {'string': 'wxid_q35rkzgkjvlv12:\n 💕 [小爱]👩\u200d❤\u200d👨[锦岚]💕\n💓═☘︎═•洞💗房•═☘︎═💓\n👩\u200d❤\u200d👨关系：情侣\n⛺地点：沙发\n😍姿势：老树盘根\n😮\u200d💨结果：失败\n❤\u200d🔥状态：羊萎早鞋\n💓═☘︎══•💗•══☘︎═💓\n🔮魅力:-28\n[玫瑰]恩爱:-6\n🕒下次:2025-07-28 08:23:17'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1753660996, 'MsgSource': '<msgsource>\n\t<silence>0</silence>\n\t<membercount>73</membercount>\n\t<signature>N0_V1_FiM6xwFp|v1_moQL8rP/</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'PushContent': '摇月 :  \ue327 [小爱]\ue005\u200d\ue022\u200d\ue004[锦岚]\ue327\n\ue327═☘︎═•洞\ue328房•═☘︎═...', 'NewMsgId': 5012885167699215730, 'MsgSeq': 871404454}
2025-07-28 08:03:07 | INFO | 收到文本消息: 消息ID:1366173915 来自:48097389945@chatroom 发送人:wxid_q35rkzgkjvlv12 @:[] 内容: 💕 [小爱]👩‍❤‍👨[锦岚]💕
💓═☘︎═•洞💗房•═☘︎═💓
👩‍❤‍👨关系：情侣
⛺地点：沙发
😍姿势：老树盘根
😮‍💨结果：失败
❤‍🔥状态：羊萎早鞋
💓═☘︎══•💗•══☘︎═💓
🔮魅力:-28
[玫瑰]恩爱:-6
🕒下次:2025-07-28 08:23:17
2025-07-28 08:03:08 | DEBUG | 处理消息内容: '💕 [小爱]👩‍❤‍👨[锦岚]💕
💓═☘︎═•洞💗房•═☘︎═💓
👩‍❤‍👨关系：情侣
⛺地点：沙发
😍姿势：老树盘根
😮‍💨结果：失败
❤‍🔥状态：羊萎早鞋
💓═☘︎══•💗•══☘︎═💓
🔮魅力:-28
[玫瑰]恩爱:-6
🕒下次:2025-07-28 08:23:17'
2025-07-28 08:03:08 | DEBUG | 消息内容 '💕 [小爱]👩‍❤‍👨[锦岚]💕
💓═☘︎═•洞💗房•═☘︎═💓
👩‍❤‍👨关系：情侣
⛺地点：沙发
😍姿势：老树盘根
😮‍💨结果：失败
❤‍🔥状态：羊萎早鞋
💓═☘︎══•💗•══☘︎═💓
🔮魅力:-28
[玫瑰]恩爱:-6
🕒下次:2025-07-28 08:23:17' 不匹配任何命令，忽略
2025-07-28 08:03:10 | DEBUG | 收到消息: {'MsgId': 1840843140, 'FromUserName': {'string': '48097389945@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 1, 'Content': {'string': 'wxid_8l9ymg1mafud12:\n「小爱」[爱心]「锦岚」\n地点：小旅馆\n活动：啪啪\n结果：失败\n羞羞：卧槽！被偷拍，慌得一批~\n恩爱值减少100\n\n下次:2025-07-28 08:13:16'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1753660996, 'MsgSource': '<msgsource>\n\t<silence>0</silence>\n\t<membercount>73</membercount>\n\t<signature>N0_V1_LClPPEww|v1_/9e/iTSr</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'PushContent': '不吃香菜\ue110 : 「小爱」[爱心]「锦岚」\n地点：小旅馆\n活动：啪啪\n结果：失败\n羞羞...', 'NewMsgId': 4425143148869898261, 'MsgSeq': 871404455}
2025-07-28 08:03:10 | INFO | 收到文本消息: 消息ID:1840843140 来自:48097389945@chatroom 发送人:wxid_8l9ymg1mafud12 @:[] 内容:「小爱」[爱心]「锦岚」
地点：小旅馆
活动：啪啪
结果：失败
羞羞：卧槽！被偷拍，慌得一批~
恩爱值减少100

下次:2025-07-28 08:13:16
2025-07-28 08:03:10 | DEBUG | 处理消息内容: '「小爱」[爱心]「锦岚」
地点：小旅馆
活动：啪啪
结果：失败
羞羞：卧槽！被偷拍，慌得一批~
恩爱值减少100

下次:2025-07-28 08:13:16'
2025-07-28 08:03:10 | DEBUG | 消息内容 '「小爱」[爱心]「锦岚」
地点：小旅馆
活动：啪啪
结果：失败
羞羞：卧槽！被偷拍，慌得一批~
恩爱值减少100

下次:2025-07-28 08:13:16' 不匹配任何命令，忽略
2025-07-28 08:03:27 | DEBUG | 收到消息: {'MsgId': 840051784, 'FromUserName': {'string': '48097389945@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 1, 'Content': {'string': 'zll953369865:\n她可以跟这些群友斗嘴聊天'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1753661017, 'MsgSource': '<msgsource>\n\t<pua>1</pua>\n\t<eggIncluded>1</eggIncluded>\n\t<silence>0</silence>\n\t<membercount>73</membercount>\n\t<signature>N0_V1_eziXjUv4|v1_wr33ee6W</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'PushContent': '【微信红包】收到一个微信红包 : 她可以跟这些群友斗嘴聊天', 'NewMsgId': 5363398991166586032, 'MsgSeq': 871404456}
2025-07-28 08:03:27 | INFO | 收到文本消息: 消息ID:840051784 来自:48097389945@chatroom 发送人:zll953369865 @:[] 内容:她可以跟这些群友斗嘴聊天
2025-07-28 08:03:28 | DEBUG | 处理消息内容: '她可以跟这些群友斗嘴聊天'
2025-07-28 08:03:28 | DEBUG | 消息内容 '她可以跟这些群友斗嘴聊天' 不匹配任何命令，忽略
2025-07-28 08:03:38 | DEBUG | 收到消息: {'MsgId': 333440428, 'FromUserName': {'string': '48097389945@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 47, 'Content': {'string': 'xiaomaochong:\n<msg><emoji fromusername="xiaomaochong" tousername="48097389945@chatroom" type="2" idbuffer="media:0_0" md5="f896856186a6dce50896bcfae9887b92" len="919913" productid="" androidmd5="f896856186a6dce50896bcfae9887b92" androidlen="919913" s60v3md5="f896856186a6dce50896bcfae9887b92" s60v3len="919913" s60v5md5="f896856186a6dce50896bcfae9887b92" s60v5len="919913" cdnurl="http://vweixinf.tc.qq.com/110/20401/stodownload?m=f896856186a6dce50896bcfae9887b92&amp;filekey=30440201010430302e02016e040253480420663839363835363138366136646365353038393662636661653938383762393202030e0969040d00000004627466730000000132&amp;hy=SH&amp;storeid=267d929d600071d2c8084a5030000006e01004fb1534807a7c0d157708e32a&amp;ef=1&amp;bizid=1022" designerid="" thumburl="" encrypturl="http://vweixinf.tc.qq.com/110/20402/stodownload?m=99f0940febe3e9714a09d3ffa40c312e&amp;filekey=30440201010430302e02016e040253480420393966303934306665626533653937313461303964336666613430633331326502030e0970040d00000004627466730000000132&amp;hy=SH&amp;storeid=267d929d60008ad568084a5030000006e02004fb2534807a7c0d157708e344&amp;ef=2&amp;bizid=1022" aeskey="05c726ab67c8404d96e369e1c17407fc" externurl="http://vweixinf.tc.qq.com/110/20403/stodownload?m=cc64ff7875ccc8b21d681f34197a2936&amp;filekey=30440201010430302e02016e04025348042063633634666637383735636363386232316436383166333431393761323933360203011e90040d00000004627466730000000132&amp;hy=SH&amp;storeid=267d929d6000aca1a8084a5030000006e03004fb3534807a7c0d157708e363&amp;ef=3&amp;bizid=1022" externmd5="75d84fcfec675b3c4963393da32dd46f" width="240" height="240" tpurl="" tpauthkey="" attachedtext="" attachedtextcolor="" lensid="" emojiattr="" linkid="" desc=""></emoji></msg>'}, 'Status': 3, 'ImgStatus': 2, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1753661028, 'MsgSource': '<msgsource>\n\t<silence>0</silence>\n\t<membercount>73</membercount>\n\t<signature>N0_V1_zfed/Hp5|v1_C9iLXDdy</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'PushContent': '小爱在群聊中发了一个表情', 'NewMsgId': 4412743610717334456, 'MsgSeq': 871404457}
2025-07-28 08:03:38 | INFO | 收到表情消息: 消息ID:333440428 来自:48097389945@chatroom 发送人:xiaomaochong MD5:f896856186a6dce50896bcfae9887b92 大小:919913
2025-07-28 08:03:38 | DEBUG | [DoubaoImageRecognition] 缓存表情消息: 4412743610717334456
2025-07-28 08:03:39 | DEBUG | 收到消息: {'MsgId': 1523805570, 'FromUserName': {'string': '48097389945@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 3, 'Content': {'string': 'xiaomaochong:\n<?xml version="1.0"?>\n<msg>\n\t<img aeskey="d8faad34003c00b5a59adf57c9394da9" encryver="1" cdnthumbaeskey="d8faad34003c00b5a59adf57c9394da9" cdnthumburl="3057020100044b304902010002049363814102032f514902042a328e7102046886be46042430333332346232612d386535642d346135372d626263642d363865623733366332353134020405252a010201000405004c55cd00" cdnthumblength="5083" cdnthumbheight="120" cdnthumbwidth="68" cdnmidheight="0" cdnmidwidth="0" cdnhdheight="0" cdnhdwidth="0" cdnmidimgurl="3057020100044b304902010002049363814102032f514902042a328e7102046886be46042430333332346232612d386535642d346135372d626263642d363865623733366332353134020405252a010201000405004c55cd00" length="45956" cdnbigimgurl="3057020100044b304902010002049363814102032f514902042a328e7102046886be46042430333332346232612d386535642d346135372d626263642d363865623733366332353134020405252a010201000405004c55cd00" hdlength="444829" md5="c6d388953b05dd3f2965f42e6c95d839" hevc_mid_size="45956" originsourcemd5="c6d388953b05dd3f2965f42e6c95d839">\n\t\t<secHashInfoBase64 />\n\t\t<live>\n\t\t\t<duration>0</duration>\n\t\t\t<size>0</size>\n\t\t\t<md5 />\n\t\t\t<fileid />\n\t\t\t<hdsize>0</hdsize>\n\t\t\t<hdmd5 />\n\t\t\t<hdfileid />\n\t\t\t<stillimagetimems>0</stillimagetimems>\n\t\t</live>\n\t</img>\n\t<appinfo>\n\t\t<appid>wx92398516de814096</appid>\n\t\t<version>7</version>\n\t\t<appname>梅赛德斯一奔驰</appname>\n\t\t<isforceupdate>0</isforceupdate>\n\t\t<messageaction />\n\t\t<messageext />\n\t\t<mediatagname />\n\t</appinfo>\n\t<platform_signature />\n\t<imgdatahash />\n\t<ImgSourceInfo>\n\t\t<ImgSourceUrl />\n\t\t<BizType>0</BizType>\n\t</ImgSourceInfo>\n</msg>\n'}, 'Status': 3, 'ImgStatus': 2, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1753661029, 'MsgSource': '<msgsource>\n\t<alnode>\n\t\t<fr>4</fr>\n\t</alnode>\n\t<sec_msg_node>\n\t\t<uuid>83bec56f8839ca1308eb902810468d80_</uuid>\n\t\t<risk-file-flag />\n\t\t<risk-file-md5-list />\n\t</sec_msg_node>\n\t<silence>0</silence>\n\t<membercount>73</membercount>\n\t<signature>N0_V1_PiHC17zs|v1_T9BX77qs</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'PushContent': '小爱在群聊中发了一张图片', 'NewMsgId': 7174283321182038846, 'MsgSeq': 871404458}
2025-07-28 08:03:39 | INFO | 收到图片消息: 消息ID:1523805570 来自:48097389945@chatroom 发送人:xiaomaochong XML:<?xml version="1.0"?><msg><img aeskey="d8faad34003c00b5a59adf57c9394da9" encryver="1" cdnthumbaeskey="d8faad34003c00b5a59adf57c9394da9" cdnthumburl="3057020100044b304902010002049363814102032f514902042a328e7102046886be46042430333332346232612d386535642d346135372d626263642d363865623733366332353134020405252a010201000405004c55cd00" cdnthumblength="5083" cdnthumbheight="120" cdnthumbwidth="68" cdnmidheight="0" cdnmidwidth="0" cdnhdheight="0" cdnhdwidth="0" cdnmidimgurl="3057020100044b304902010002049363814102032f514902042a328e7102046886be46042430333332346232612d386535642d346135372d626263642d363865623733366332353134020405252a010201000405004c55cd00" length="45956" cdnbigimgurl="3057020100044b304902010002049363814102032f514902042a328e7102046886be46042430333332346232612d386535642d346135372d626263642d363865623733366332353134020405252a010201000405004c55cd00" hdlength="444829" md5="c6d388953b05dd3f2965f42e6c95d839" hevc_mid_size="45956" originsourcemd5="c6d388953b05dd3f2965f42e6c95d839"><secHashInfoBase64 /><live><duration>0</duration><size>0</size><md5 /><fileid /><hdsize>0</hdsize><hdmd5 /><hdfileid /><stillimagetimems>0</stillimagetimems></live></img><appinfo><appid>wx92398516de814096</appid><version>7</version><appname>梅赛德斯一奔驰</appname><isforceupdate>0</isforceupdate><messageaction /><messageext /><mediatagname /></appinfo><platform_signature /><imgdatahash /><ImgSourceInfo><ImgSourceUrl /><BizType>0</BizType></ImgSourceInfo></msg>
2025-07-28 08:03:39 | INFO | [ImageEcho] 保存图片信息成功，当前群 48097389945@chatroom 已存储 5 张图片
2025-07-28 08:03:39 | INFO | [TimerTask] 缓存图片消息: 1523805570
2025-07-28 08:04:07 | DEBUG | 收到消息: {'MsgId': 644237049, 'FromUserName': {'string': '48097389945@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 1, 'Content': {'string': 'wxid_cwxauba42ki122:\n人家都不加你好友，惦记(⊙o⊙)啥？'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1753661057, 'MsgSource': '<msgsource>\n\t<pua>1</pua>\n\t<silence>0</silence>\n\t<membercount>73</membercount>\n\t<signature>N0_V1_EBWHQ9Zh|v1_Yn6eOhNr</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'PushContent': 'ن初心ᯤ⁶ᴳ คิดถึงن : 人家都不加你好友，惦记(⊙o⊙)啥？', 'NewMsgId': 1335230113975062858, 'MsgSeq': 871404459}
2025-07-28 08:04:07 | INFO | 收到文本消息: 消息ID:644237049 来自:48097389945@chatroom 发送人:wxid_cwxauba42ki122 @:[] 内容:人家都不加你好友，惦记(⊙o⊙)啥？
2025-07-28 08:04:08 | DEBUG | 处理消息内容: '人家都不加你好友，惦记(⊙o⊙)啥？'
2025-07-28 08:04:08 | DEBUG | 消息内容 '人家都不加你好友，惦记(⊙o⊙)啥？' 不匹配任何命令，忽略
2025-07-28 08:04:11 | DEBUG | 收到消息: {'MsgId': 631972415, 'FromUserName': {'string': '48097389945@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 47, 'Content': {'string': 'last--exile:\n<msg><emoji fromusername="last--exile" tousername="48097389945@chatroom" type="2" idbuffer="media:0_0" md5="ef074a71654bf149b26b5f09737e1baa" len="4227054" productid="" androidmd5="ef074a71654bf149b26b5f09737e1baa" androidlen="4227054" s60v3md5="ef074a71654bf149b26b5f09737e1baa" s60v3len="4227054" s60v5md5="ef074a71654bf149b26b5f09737e1baa" s60v5len="4227054" cdnurl="http://wxapp.tc.qq.com/262/20304/stodownload?m=ef074a71654bf149b26b5f09737e1baa&amp;filekey=30350201010421301f02020106040253480410ef074a71654bf149b26b5f09737e1baa0203407fee040d00000004627466730000000132&amp;hy=SH&amp;storeid=26370b2cd000c893280bd5f310000010600004f50534813b258e0b6a871af7&amp;bizid=1023" designerid="" thumburl="" encrypturl="http://wxapp.tc.qq.com/262/20304/stodownload?m=9b1d555300b91dde779b7ff90ffd26c4&amp;filekey=30350201010421301f020201060402534804109b1d555300b91dde779b7ff90ffd26c40203407ff0040d00000004627466730000000132&amp;hy=SH&amp;storeid=26370b2ce0009c8b980bd5f310000010600004f505348245108e0b6ef015d1&amp;bizid=1023" aeskey="7253c127170da011201f01de7c82316f" externurl="http://wxapp.tc.qq.com/262/20304/stodownload?m=8f3e7a54c1cc432188c8ce7186eba010&amp;filekey=30350201010421301f020201060402534804108f3e7a54c1cc432188c8ce7186eba010020301a700040d00000004627466730000000132&amp;hy=SH&amp;storeid=26370b8eb000ef555703030340000010600004f50534828c1c8e0b6f0213e3&amp;bizid=1023" externmd5="5d1507a7cb3fe3bd0c296dc0348daf9d" width="579" height="572" tpurl="" tpauthkey="" attachedtext="" attachedtextcolor="" lensid="" emojiattr="" linkid="" desc=""></emoji></msg>'}, 'Status': 3, 'ImgStatus': 2, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1753661061, 'MsgSource': '<msgsource>\n\t<silence>0</silence>\n\t<membercount>73</membercount>\n\t<signature>N0_V1_2V6O8I1c|v1_41xB7i6O</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'PushContent': '亮在群聊中发了一个表情', 'NewMsgId': 5990194165107841668, 'MsgSeq': 871404460}
2025-07-28 08:04:11 | INFO | 收到表情消息: 消息ID:631972415 来自:48097389945@chatroom 发送人:last--exile MD5:ef074a71654bf149b26b5f09737e1baa 大小:4227054
2025-07-28 08:04:12 | DEBUG | [DoubaoImageRecognition] 缓存表情消息: 5990194165107841668
2025-07-28 08:04:20 | DEBUG | 收到消息: {'MsgId': 2006413305, 'FromUserName': {'string': '48097389945@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 1, 'Content': {'string': 'zll953369865:\n没她 群不热闹啊'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1753661071, 'MsgSource': '<msgsource>\n\t<pua>1</pua>\n\t<eggIncluded>1</eggIncluded>\n\t<silence>0</silence>\n\t<membercount>73</membercount>\n\t<signature>N0_V1_mgU4zL2V|v1_O0Cy7ds6</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'PushContent': '【微信红包】收到一个微信红包 : 没她 群不热闹啊', 'NewMsgId': 8706807170205569179, 'MsgSeq': 871404461}
2025-07-28 08:04:20 | INFO | 收到文本消息: 消息ID:2006413305 来自:48097389945@chatroom 发送人:zll953369865 @:[] 内容:没她 群不热闹啊
2025-07-28 08:04:21 | DEBUG | 处理消息内容: '没她 群不热闹啊'
2025-07-28 08:04:21 | DEBUG | 消息内容 '没她 群不热闹啊' 不匹配任何命令，忽略
2025-07-28 08:04:25 | DEBUG | 收到消息: {'MsgId': 2125083606, 'FromUserName': {'string': '48097389945@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 1, 'Content': {'string': 'xiaomaochong:\n自古多情空余恨'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1753661075, 'MsgSource': '<msgsource>\n\t<pua>1</pua>\n\t<silence>0</silence>\n\t<membercount>73</membercount>\n\t<signature>N0_V1_KGr+nTot|v1_m62WX7rh</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'PushContent': '小爱 : 自古多情空余恨', 'NewMsgId': 1269223466927416568, 'MsgSeq': 871404462}
2025-07-28 08:04:25 | INFO | 收到文本消息: 消息ID:2125083606 来自:48097389945@chatroom 发送人:xiaomaochong @:[] 内容:自古多情空余恨
2025-07-28 08:04:26 | DEBUG | 处理消息内容: '自古多情空余恨'
2025-07-28 08:04:26 | DEBUG | 消息内容 '自古多情空余恨' 不匹配任何命令，忽略
2025-07-28 08:04:29 | DEBUG | 收到消息: {'MsgId': 297103488, 'FromUserName': {'string': '48097389945@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 1, 'Content': {'string': 'wxid_cwxauba42ki122:\n[偷笑]'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1753661079, 'MsgSource': '<msgsource>\n\t<pua>1</pua>\n\t<silence>0</silence>\n\t<membercount>73</membercount>\n\t<signature>N0_V1_S2tl9x0s|v1_pjej2W/u</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'PushContent': 'ن初心ᯤ⁶ᴳ คิดถึงن : [偷笑]', 'NewMsgId': 839023514268096837, 'MsgSeq': 871404463}
2025-07-28 08:04:29 | INFO | 收到表情消息: 消息ID:297103488 来自:48097389945@chatroom 发送人:wxid_cwxauba42ki122 @:[] 内容:[偷笑]
2025-07-28 08:04:30 | DEBUG | [DoubaoImageRecognition] 缓存表情消息: 839023514268096837
2025-07-28 08:04:49 | DEBUG | 收到消息: {'MsgId': 258903949, 'FromUserName': {'string': '48097389945@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 1, 'Content': {'string': 'wxid_cwxauba42ki122:\n[偷笑]'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1753661100, 'MsgSource': '<msgsource>\n\t<pua>1</pua>\n\t<silence>0</silence>\n\t<membercount>73</membercount>\n\t<signature>N0_V1_nBpxURKD|v1_Iuv84bfX</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'PushContent': 'ن初心ᯤ⁶ᴳ คิดถึงن : [偷笑]', 'NewMsgId': 839023514268096837, 'MsgSeq': 871404464}
2025-07-28 08:04:49 | INFO | 收到表情消息: 消息ID:258903949 来自:48097389945@chatroom 发送人:wxid_cwxauba42ki122 @:[] 内容:[偷笑]
2025-07-28 08:04:50 | DEBUG | [DoubaoImageRecognition] 缓存表情消息: 839023514268096837
2025-07-28 08:05:04 | DEBUG | 收到消息: {'MsgId': 1516033848, 'FromUserName': {'string': '48097389945@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 1, 'Content': {'string': 'zll953369865:\n此恨绵绵无绝期'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1753661114, 'MsgSource': '<msgsource>\n\t<pua>1</pua>\n\t<eggIncluded>1</eggIncluded>\n\t<silence>0</silence>\n\t<membercount>73</membercount>\n\t<signature>N0_V1_rgFRnKu1|v1_Yqw72uqu</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'PushContent': '【微信红包】收到一个微信红包 : 此恨绵绵无绝期', 'NewMsgId': 509258944759175680, 'MsgSeq': 871404465}
2025-07-28 08:05:04 | INFO | 收到文本消息: 消息ID:1516033848 来自:48097389945@chatroom 发送人:zll953369865 @:[] 内容:此恨绵绵无绝期
2025-07-28 08:05:05 | DEBUG | 处理消息内容: '此恨绵绵无绝期'
2025-07-28 08:05:05 | DEBUG | 消息内容 '此恨绵绵无绝期' 不匹配任何命令，忽略
2025-07-28 08:05:08 | DEBUG | 收到消息: {'MsgId': 1186002594, 'FromUserName': {'string': '48097389945@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 1, 'Content': {'string': 'wxid_wlnzvr8ivgd422:\n不知道斗嘴把你踢出群的时候你还觉得好玩吗☺️'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1753661118, 'MsgSource': '<msgsource>\n\t<pua>1</pua>\n\t<eggIncluded>1</eggIncluded>\n\t<silence>0</silence>\n\t<membercount>73</membercount>\n\t<signature>N0_V1_Hd1DeBAh|v1_E6Ii5X6g</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'PushContent': '锦岚 : 不知道斗嘴把你踢出群的时候你还觉得好玩吗\ue414️', 'NewMsgId': 5821273606779560148, 'MsgSeq': 871404466}
2025-07-28 08:05:08 | INFO | 收到文本消息: 消息ID:1186002594 来自:48097389945@chatroom 发送人:wxid_wlnzvr8ivgd422 @:[] 内容:不知道斗嘴把你踢出群的时候你还觉得好玩吗☺️
2025-07-28 08:05:08 | DEBUG | 处理消息内容: '不知道斗嘴把你踢出群的时候你还觉得好玩吗☺️'
2025-07-28 08:05:08 | DEBUG | 消息内容 '不知道斗嘴把你踢出群的时候你还觉得好玩吗☺️' 不匹配任何命令，忽略
2025-07-28 08:05:26 | DEBUG | 收到消息: {'MsgId': 463453404, 'FromUserName': {'string': '48097389945@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 1, 'Content': {'string': 'last--exile:\n+1'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1753661137, 'MsgSource': '<msgsource>\n\t<pua>1</pua>\n\t<silence>0</silence>\n\t<membercount>73</membercount>\n\t<signature>N0_V1_xHAXbC44|v1_7acsyIs6</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'PushContent': '亮 : +1', 'NewMsgId': 269142698382406422, 'MsgSeq': 871404467}
2025-07-28 08:05:26 | INFO | 收到文本消息: 消息ID:463453404 来自:48097389945@chatroom 发送人:last--exile @:[] 内容:+1
2025-07-28 08:05:27 | DEBUG | 处理消息内容: '+1'
2025-07-28 08:05:27 | DEBUG | 消息内容 '+1' 不匹配任何命令，忽略
2025-07-28 08:05:43 | DEBUG | 收到消息: {'MsgId': 348193090, 'FromUserName': {'string': '48097389945@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 1, 'Content': {'string': 'last--exile:\n不喜欢双标狗'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1753661153, 'MsgSource': '<msgsource>\n\t<pua>1</pua>\n\t<silence>0</silence>\n\t<membercount>73</membercount>\n\t<signature>N0_V1_GXZERF66|v1_srnKnqw+</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'PushContent': '亮 : 不喜欢双标狗', 'NewMsgId': 9107776270332611584, 'MsgSeq': 871404468}
2025-07-28 08:05:43 | INFO | 收到文本消息: 消息ID:348193090 来自:48097389945@chatroom 发送人:last--exile @:[] 内容:不喜欢双标狗
2025-07-28 08:05:44 | DEBUG | 处理消息内容: '不喜欢双标狗'
2025-07-28 08:05:44 | DEBUG | 消息内容 '不喜欢双标狗' 不匹配任何命令，忽略
2025-07-28 08:05:50 | DEBUG | 收到消息: {'MsgId': 1751588076, 'FromUserName': {'string': '48097389945@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 1, 'Content': {'string': 'last--exile:\n@锦岚\u2005她说'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1753661160, 'MsgSource': '<msgsource>\n\t<atuserlist><![CDATA[wxid_wlnzvr8ivgd422]]></atuserlist>\n\t<pua>1</pua>\n\t<silence>0</silence>\n\t<membercount>73</membercount>\n\t<signature>N0_V1_zwmeBf+x|v1_Apw5U7sl</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'PushContent': '亮 : @锦岚\u2005她说', 'NewMsgId': 6717177149975817967, 'MsgSeq': 871404469}
2025-07-28 08:05:50 | INFO | 收到文本消息: 消息ID:1751588076 来自:48097389945@chatroom 发送人:last--exile @:['wxid_wlnzvr8ivgd422'] 内容:@锦岚 她说
2025-07-28 08:05:50 | DEBUG | 处理消息内容: '@锦岚 她说'
2025-07-28 08:05:50 | DEBUG | 消息内容 '@锦岚 她说' 不匹配任何命令，忽略
2025-07-28 08:05:51 | INFO | [TempFileManager] 开始清理临时文件...
2025-07-28 08:05:51 | DEBUG | 开始检查群成员变化，当前监控 2 个群
2025-07-28 08:05:52 | DEBUG | 群成员变化检查完成
2025-07-28 08:06:05 | DEBUG | 收到消息: {'MsgId': 963276405, 'FromUserName': {'string': '48097389945@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 1, 'Content': {'string': 'wxid_wlnzvr8ivgd422:\n翻聊天记录加她啊，不行你加小熊，让他把微信推给你'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1753661175, 'MsgSource': '<msgsource>\n\t<pua>1</pua>\n\t<eggIncluded>1</eggIncluded>\n\t<silence>0</silence>\n\t<membercount>73</membercount>\n\t<signature>N0_V1_Str1ECce|v1_7ubFfJbb</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'PushContent': '锦岚 : 翻聊天记录加她啊，不行你加小熊，让他把微信推给你', 'NewMsgId': 4503721077754075393, 'MsgSeq': 871404470}
2025-07-28 08:06:05 | INFO | 收到文本消息: 消息ID:963276405 来自:48097389945@chatroom 发送人:wxid_wlnzvr8ivgd422 @:[] 内容:翻聊天记录加她啊，不行你加小熊，让他把微信推给你
2025-07-28 08:06:06 | DEBUG | 处理消息内容: '翻聊天记录加她啊，不行你加小熊，让他把微信推给你'
2025-07-28 08:06:06 | DEBUG | 消息内容 '翻聊天记录加她啊，不行你加小熊，让他把微信推给你' 不匹配任何命令，忽略
2025-07-28 08:06:08 | DEBUG | 收到消息: {'MsgId': 1405437566, 'FromUserName': {'string': '48097389945@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 1, 'Content': {'string': 'wxid_cwxauba42ki122:\n[捂脸]'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1753661177, 'MsgSource': '<msgsource>\n\t<pua>1</pua>\n\t<silence>0</silence>\n\t<membercount>73</membercount>\n\t<signature>N0_V1_xT5GFtg0|v1_mVb/6Yv/</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'PushContent': 'ن初心ᯤ⁶ᴳ คิดถึงن : [捂脸]', 'NewMsgId': 3307899221167491154, 'MsgSeq': 871404471}
2025-07-28 08:06:08 | INFO | 收到表情消息: 消息ID:1405437566 来自:48097389945@chatroom 发送人:wxid_cwxauba42ki122 @:[] 内容:[捂脸]
2025-07-28 08:06:08 | DEBUG | [DoubaoImageRecognition] 缓存表情消息: 3307899221167491154
2025-07-28 08:06:27 | DEBUG | 收到消息: {'MsgId': 2068166295, 'FromUserName': {'string': '48097389945@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 1, 'Content': {'string': 'zll953369865:\n@锦岚\u2005你都退了还几次 依然在群'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1753661197, 'MsgSource': '<msgsource>\n\t<atuserlist><![CDATA[wxid_wlnzvr8ivgd422]]></atuserlist>\n\t<pua>1</pua>\n\t<eggIncluded>1</eggIncluded>\n\t<silence>0</silence>\n\t<membercount>73</membercount>\n\t<signature>N0_V1_bbc/+bZu|v1_7sCNO91k</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'PushContent': '【微信红包】收到一个微信红包 : @锦岚\u2005你都退了还几次 依然在群', 'NewMsgId': 2067699808580621049, 'MsgSeq': 871404472}
2025-07-28 08:06:27 | INFO | 收到文本消息: 消息ID:2068166295 来自:48097389945@chatroom 发送人:zll953369865 @:['wxid_wlnzvr8ivgd422'] 内容:@锦岚 你都退了还几次 依然在群
2025-07-28 08:06:28 | DEBUG | 处理消息内容: '@锦岚 你都退了还几次 依然在群'
2025-07-28 08:06:28 | DEBUG | 消息内容 '@锦岚 你都退了还几次 依然在群' 不匹配任何命令，忽略
2025-07-28 08:06:34 | DEBUG | 收到消息: {'MsgId': 88172479, 'FromUserName': {'string': '48097389945@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 1, 'Content': {'string': 'wxid_cwxauba42ki122:\n我恨你一辈子，你把我的嫂嫂夺走了'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1753661204, 'MsgSource': '<msgsource>\n\t<pua>1</pua>\n\t<silence>0</silence>\n\t<membercount>73</membercount>\n\t<signature>N0_V1_zv3psuTA|v1_UZq+g10Q</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'PushContent': 'ن初心ᯤ⁶ᴳ คิดถึงن : 我恨你一辈子，你把我的嫂嫂夺走了', 'NewMsgId': 5971514735222762540, 'MsgSeq': 871404473}
2025-07-28 08:06:34 | INFO | 收到文本消息: 消息ID:88172479 来自:48097389945@chatroom 发送人:wxid_cwxauba42ki122 @:[] 内容:我恨你一辈子，你把我的嫂嫂夺走了
2025-07-28 08:06:35 | DEBUG | 处理消息内容: '我恨你一辈子，你把我的嫂嫂夺走了'
2025-07-28 08:06:35 | DEBUG | 消息内容 '我恨你一辈子，你把我的嫂嫂夺走了' 不匹配任何命令，忽略
2025-07-28 08:06:37 | DEBUG | 收到消息: {'MsgId': 709583421, 'FromUserName': {'string': '48097389945@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 1, 'Content': {'string': 'zll953369865:\n说明有缘'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1753661207, 'MsgSource': '<msgsource>\n\t<pua>1</pua>\n\t<eggIncluded>1</eggIncluded>\n\t<silence>0</silence>\n\t<membercount>73</membercount>\n\t<signature>N0_V1_0aYQ0SXe|v1_VvMil8SA</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'PushContent': '【微信红包】收到一个微信红包 : 说明有缘', 'NewMsgId': 8957722060413308235, 'MsgSeq': 871404474}
2025-07-28 08:06:37 | INFO | 收到文本消息: 消息ID:709583421 来自:48097389945@chatroom 发送人:zll953369865 @:[] 内容:说明有缘
2025-07-28 08:06:37 | DEBUG | 处理消息内容: '说明有缘'
2025-07-28 08:06:37 | DEBUG | 消息内容 '说明有缘' 不匹配任何命令，忽略
2025-07-28 08:06:40 | DEBUG | 收到消息: {'MsgId': 1732355563, 'FromUserName': {'string': '48097389945@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 47, 'Content': {'string': 'wxid_lneb7n23o4lg12:\n<msg><emoji fromusername = "wxid_lneb7n23o4lg12" tousername = "48097389945@chatroom" type="2" idbuffer="media:0_0" md5="f1522d3860f33f5afe82ea0c9a0d4457" len = "50698" productid="" androidmd5="f1522d3860f33f5afe82ea0c9a0d4457" androidlen="50698" s60v3md5 = "f1522d3860f33f5afe82ea0c9a0d4457" s60v3len="50698" s60v5md5 = "f1522d3860f33f5afe82ea0c9a0d4457" s60v5len="50698" cdnurl = "http://vweixinf.tc.qq.com/110/20401/stodownload?m=f1522d3860f33f5afe82ea0c9a0d4457&amp;filekey=30440201010430302e02016e0402534804206631353232643338363066333366356166653832656130633961306434343537020300c60a040d00000004627466730000000132&amp;hy=SH&amp;storeid=26886bf1a0000a6d37d2d13e40000006e01004fb1534822fee17156baea27c&amp;ef=1&amp;bizid=1022" designerid = "" thumburl = "" encrypturl = "http://vweixinf.tc.qq.com/110/20402/stodownload?m=77d63c65327b4c6cc72873ba802757b2&amp;filekey=30440201010430302e02016e0402534804203737643633633635333237623463366363373238373362613830323735376232020300c610040d00000004627466730000000132&amp;hy=SH&amp;storeid=26886bf1a000184367d2d13e40000006e02004fb2534822fee17156baea28b&amp;ef=2&amp;bizid=1022" aeskey= "01dcc741fe7c46ffb09bda744f2745b7" externurl = "http://vweixinf.tc.qq.com/110/20403/stodownload?m=a027e51100ceec016b2854cae31be2ea&amp;filekey=3043020101042f302d02016e040253480420613032376535313130306365656330313662323835346361653331626532656102026b20040d00000004627466730000000132&amp;hy=SH&amp;storeid=26886bf1a0002293c7d2d13e40000006e03004fb3534822fee17156baea296&amp;ef=3&amp;bizid=1022" externmd5 = "1441ce293c789d9e88d217982eafc776" width= "360" height= "300" tpurl= "" tpauthkey= "" attachedtext= "" attachedtextcolor= "" lensid= "" emojiattr= "" linkid= "" desc= "" ></emoji>  </msg>'}, 'Status': 3, 'ImgStatus': 2, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1753661210, 'MsgSource': '<msgsource>\n\t<silence>0</silence>\n\t<membercount>73</membercount>\n\t<signature>N0_V1_Q0p/q0yu|v1_vL8DZnMv</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'PushContent': 'ᡣ在群聊中发了一个表情', 'NewMsgId': 662354920052536965, 'MsgSeq': 871404475}
2025-07-28 08:06:40 | INFO | 收到表情消息: 消息ID:1732355563 来自:48097389945@chatroom 发送人:wxid_lneb7n23o4lg12 MD5:f1522d3860f33f5afe82ea0c9a0d4457 大小:50698
2025-07-28 08:06:41 | DEBUG | [DoubaoImageRecognition] 缓存表情消息: 662354920052536965
2025-07-28 08:06:42 | DEBUG | 收到消息: {'MsgId': 1775540114, 'FromUserName': {'string': '48097389945@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 34, 'Content': {'string': 'wxid_lneb7n23o4lg12:\n<msg><voicemsg endflag="1" cancelflag="0" forwardflag="0" voiceformat="4" voicelength="1176" length="3072" bufid="0" aeskey="7a696b72736b7267746e796d61746f76" voiceurl="3052020100044b304902010002046b3e4bb802033d11fe0204d191507002046886bf1c042463363630663931382d356666622d343635322d623861362d32613264656439333664316302040524000f02010004002a5cd9b3" voicemd5="88a1ff3d40bd798a29faffdb2b470feb" clientmsgid="41653132633234663932353735336100510806072825a7b9a8cd3c1103" fromusername="wxid_lneb7n23o4lg12" /></msg>'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1753661212, 'MsgSource': '<msgsource>\n\t<silence>0</silence>\n\t<membercount>73</membercount>\n\t<signature>N0_V1_qMk7j7UH|v1_zdmCGnFt</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'PushContent': 'ᡣ在群聊中发了一段语音', 'NewMsgId': 8256330165666134761, 'MsgSeq': 871404476}
2025-07-28 08:06:42 | INFO | 收到语音消息: 消息ID:1775540114 来自:48097389945@chatroom 发送人:wxid_lneb7n23o4lg12 XML:
<msg><voicemsg endflag="1" cancelflag="0" forwardflag="0" voiceformat="4" voicelength="1176" length="3072" bufid="0" aeskey="7a696b72736b7267746e796d61746f76" voiceurl="3052020100044b304902010002046b3e4bb802033d11fe0204d191507002046886bf1c042463363630663931382d356666622d343635322d623861362d32613264656439333664316302040524000f02010004002a5cd9b3" voicemd5="88a1ff3d40bd798a29faffdb2b470feb" clientmsgid="41653132633234663932353735336100510806072825a7b9a8cd3c1103" fromusername="wxid_lneb7n23o4lg12" /></msg>
2025-07-28 08:06:51 | DEBUG | 收到消息: {'MsgId': 611426050, 'FromUserName': {'string': '48097389945@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 1, 'Content': {'string': 'wxid_wlnzvr8ivgd422:\n我退了好几次我可没踢人出去'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1753661221, 'MsgSource': '<msgsource>\n\t<pua>1</pua>\n\t<eggIncluded>1</eggIncluded>\n\t<silence>0</silence>\n\t<membercount>73</membercount>\n\t<signature>N0_V1_0+OzN2Ks|v1_wVfetUjr</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'PushContent': '锦岚 : 我退了好几次我可没踢人出去', 'NewMsgId': 3470721349139776608, 'MsgSeq': 871404477}
2025-07-28 08:06:51 | INFO | 收到文本消息: 消息ID:611426050 来自:48097389945@chatroom 发送人:wxid_wlnzvr8ivgd422 @:[] 内容:我退了好几次我可没踢人出去
2025-07-28 08:06:52 | DEBUG | 处理消息内容: '我退了好几次我可没踢人出去'
2025-07-28 08:06:52 | DEBUG | 消息内容 '我退了好几次我可没踢人出去' 不匹配任何命令，忽略
2025-07-28 08:06:54 | DEBUG | 收到消息: {'MsgId': 550313423, 'FromUserName': {'string': '48097389945@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 1, 'Content': {'string': 'xiaomaochong:\n真相是人家讨厌你才和你斗嘴，你这直接舔上了[捂脸]'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1753661221, 'MsgSource': '<msgsource>\n\t<pua>1</pua>\n\t<silence>0</silence>\n\t<membercount>73</membercount>\n\t<signature>N0_V1_LYTx+7Cl|v1_LDKz0mxp</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'PushContent': '小爱 : 真相是人家讨厌你才和你斗嘴，你这直接舔上了[捂脸]', 'NewMsgId': 1897306904307323268, 'MsgSeq': 871404478}
2025-07-28 08:06:54 | INFO | 收到文本消息: 消息ID:550313423 来自:48097389945@chatroom 发送人:xiaomaochong @:[] 内容:真相是人家讨厌你才和你斗嘴，你这直接舔上了[捂脸]
2025-07-28 08:06:54 | DEBUG | 处理消息内容: '真相是人家讨厌你才和你斗嘴，你这直接舔上了[捂脸]'
2025-07-28 08:06:54 | DEBUG | 消息内容 '真相是人家讨厌你才和你斗嘴，你这直接舔上了[捂脸]' 不匹配任何命令，忽略
2025-07-28 08:07:11 | DEBUG | 收到消息: {'MsgId': 688265769, 'FromUserName': {'string': '48097389945@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 47, 'Content': {'string': 'last--exile:\n<msg><emoji fromusername="last--exile" tousername="48097389945@chatroom" type="2" idbuffer="media:0_0" md5="c4a688cfb58e7f6e442ce59d6a690fe8" len="70404" productid="" androidmd5="c4a688cfb58e7f6e442ce59d6a690fe8" androidlen="70404" s60v3md5="c4a688cfb58e7f6e442ce59d6a690fe8" s60v3len="70404" s60v5md5="c4a688cfb58e7f6e442ce59d6a690fe8" s60v5len="70404" cdnurl="http://vweixinf.tc.qq.com/110/20401/stodownload?m=c4a688cfb58e7f6e442ce59d6a690fe8&amp;filekey=30440201010430302e02016e04025348042063346136383863666235386537663665343432636535396436613639306665380203011304040d00000004627466730000000132&amp;hy=SH&amp;storeid=2673d18ee000e2a8c085a2baa0000006e01004fb1534824707bd1e67b4645f&amp;ef=1&amp;bizid=1022" designerid="" thumburl="" encrypturl="http://vweixinf.tc.qq.com/110/20402/stodownload?m=dac34991584546129826f2aaebe32504&amp;filekey=30440201010430302e02016e04025348042064616333343939313538343534363132393832366632616165626533323530340203011310040d00000004627466730000000132&amp;hy=SH&amp;storeid=2673d18ee000edf48085a2baa0000006e02004fb2534824707bd1e67b4646c&amp;ef=2&amp;bizid=1022" aeskey="bb99af7474cb48a58def9b72d5f7c7d4" externurl="http://vweixinf.tc.qq.com/110/20403/stodownload?m=def7cfd5331b61ef83de1f81e7785157&amp;filekey=3043020101042f302d02016e040253480420646566376366643533333162363165663833646531663831653737383531353702021930040d00000004627466730000000132&amp;hy=SH&amp;storeid=2673d18ef000047e2085a2baa0000006e03004fb3534824707bd1e67b4647b&amp;ef=3&amp;bizid=1022" externmd5="daad8cf50d5c39ac447bc4ba6eb88e18" width="300" height="298" tpurl="" tpauthkey="" attachedtext="" attachedtextcolor="" lensid="" emojiattr="" linkid="" desc=""></emoji></msg>'}, 'Status': 3, 'ImgStatus': 2, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1753661241, 'MsgSource': '<msgsource>\n\t<silence>0</silence>\n\t<membercount>73</membercount>\n\t<signature>N0_V1_X6Em24ZS|v1_tpcKSBWJ</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'PushContent': '亮在群聊中发了一个表情', 'NewMsgId': 3931607719785881853, 'MsgSeq': 871404479}
2025-07-28 08:07:11 | INFO | 收到表情消息: 消息ID:688265769 来自:48097389945@chatroom 发送人:last--exile MD5:c4a688cfb58e7f6e442ce59d6a690fe8 大小:70404
2025-07-28 08:07:12 | DEBUG | [DoubaoImageRecognition] 缓存表情消息: 3931607719785881853
2025-07-28 08:07:19 | DEBUG | 收到消息: {'MsgId': 738348353, 'FromUserName': {'string': '48097389945@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 47, 'Content': {'string': 'wxid_wlnzvr8ivgd422:\n<msg><emoji fromusername = "wxid_wlnzvr8ivgd422" tousername = "48097389945@chatroom" type="2" idbuffer="media:0_0" md5="c4a688cfb58e7f6e442ce59d6a690fe8" len = "70404" productid="" androidmd5="c4a688cfb58e7f6e442ce59d6a690fe8" androidlen="70404" s60v3md5 = "c4a688cfb58e7f6e442ce59d6a690fe8" s60v3len="70404" s60v5md5 = "c4a688cfb58e7f6e442ce59d6a690fe8" s60v5len="70404" cdnurl = "http://vweixinf.tc.qq.com/110/20401/stodownload?m=c4a688cfb58e7f6e442ce59d6a690fe8&amp;filekey=30440201010430302e02016e04025348042063346136383863666235386537663665343432636535396436613639306665380203011304040d00000004627466730000000132&amp;hy=SH&amp;storeid=2673d18ee000e2a8c085a2baa0000006e01004fb1534824707bd1e67b4645f&amp;ef=1&amp;bizid=1022" designerid = "" thumburl = "" encrypturl = "http://vweixinf.tc.qq.com/110/20402/stodownload?m=dac34991584546129826f2aaebe32504&amp;filekey=30440201010430302e02016e04025348042064616333343939313538343534363132393832366632616165626533323530340203011310040d00000004627466730000000132&amp;hy=SH&amp;storeid=2673d18ee000edf48085a2baa0000006e02004fb2534824707bd1e67b4646c&amp;ef=2&amp;bizid=1022" aeskey= "bb99af7474cb48a58def9b72d5f7c7d4" externurl = "http://vweixinf.tc.qq.com/110/20403/stodownload?m=def7cfd5331b61ef83de1f81e7785157&amp;filekey=3043020101042f302d02016e040253480420646566376366643533333162363165663833646531663831653737383531353702021930040d00000004627466730000000132&amp;hy=SH&amp;storeid=2673d18ef000047e2085a2baa0000006e03004fb3534824707bd1e67b4647b&amp;ef=3&amp;bizid=1022" externmd5 = "daad8cf50d5c39ac447bc4ba6eb88e18" width= "300" height= "298" tpurl= "" tpauthkey= "" attachedtext= "" attachedtextcolor= "" lensid= "" emojiattr= "" linkid= "" desc= "" ></emoji>  </msg>'}, 'Status': 3, 'ImgStatus': 2, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1753661250, 'MsgSource': '<msgsource>\n\t<silence>0</silence>\n\t<membercount>73</membercount>\n\t<signature>N0_V1_MgtqZU3z|v1_ZiFqOwwO</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'PushContent': '锦岚在群聊中发了一个表情', 'NewMsgId': 7802635700667929459, 'MsgSeq': 871404480}
2025-07-28 08:07:19 | INFO | 收到表情消息: 消息ID:738348353 来自:48097389945@chatroom 发送人:wxid_wlnzvr8ivgd422 MD5:c4a688cfb58e7f6e442ce59d6a690fe8 大小:70404
2025-07-28 08:07:20 | DEBUG | [DoubaoImageRecognition] 缓存表情消息: 7802635700667929459
2025-07-28 08:07:23 | DEBUG | 收到消息: {'MsgId': 818771614, 'FromUserName': {'string': '48097389945@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 1, 'Content': {'string': 'last--exile:\n舔狗无敌'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1753661253, 'MsgSource': '<msgsource>\n\t<pua>1</pua>\n\t<silence>0</silence>\n\t<membercount>73</membercount>\n\t<signature>N0_V1_ydscmmp7|v1_PO+ZURA6</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'PushContent': '亮 : 舔狗无敌', 'NewMsgId': 7672599748822896120, 'MsgSeq': 871404481}
2025-07-28 08:07:23 | INFO | 收到文本消息: 消息ID:818771614 来自:48097389945@chatroom 发送人:last--exile @:[] 内容:舔狗无敌
2025-07-28 08:07:24 | DEBUG | 处理消息内容: '舔狗无敌'
2025-07-28 08:07:24 | DEBUG | 消息内容 '舔狗无敌' 不匹配任何命令，忽略
2025-07-28 08:07:25 | DEBUG | 收到消息: {'MsgId': 1131050995, 'FromUserName': {'string': '48097389945@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 47, 'Content': {'string': 'xiaomaochong:\n<msg><emoji fromusername="xiaomaochong" tousername="48097389945@chatroom" type="2" idbuffer="media:0_0" md5="c4a688cfb58e7f6e442ce59d6a690fe8" len="70404" productid="" androidmd5="c4a688cfb58e7f6e442ce59d6a690fe8" androidlen="70404" s60v3md5="c4a688cfb58e7f6e442ce59d6a690fe8" s60v3len="70404" s60v5md5="c4a688cfb58e7f6e442ce59d6a690fe8" s60v5len="70404" cdnurl="http://vweixinf.tc.qq.com/110/20401/stodownload?m=c4a688cfb58e7f6e442ce59d6a690fe8&amp;filekey=30440201010430302e02016e04025348042063346136383863666235386537663665343432636535396436613639306665380203011304040d00000004627466730000000132&amp;hy=SH&amp;storeid=2673d18ee000e2a8c085a2baa0000006e01004fb1534824707bd1e67b4645f&amp;ef=1&amp;bizid=1022" designerid="" thumburl="" encrypturl="http://vweixinf.tc.qq.com/110/20402/stodownload?m=dac34991584546129826f2aaebe32504&amp;filekey=30440201010430302e02016e04025348042064616333343939313538343534363132393832366632616165626533323530340203011310040d00000004627466730000000132&amp;hy=SH&amp;storeid=2673d18ee000edf48085a2baa0000006e02004fb2534824707bd1e67b4646c&amp;ef=2&amp;bizid=1022" aeskey="bb99af7474cb48a58def9b72d5f7c7d4" externurl="http://vweixinf.tc.qq.com/110/20403/stodownload?m=def7cfd5331b61ef83de1f81e7785157&amp;filekey=3043020101042f302d02016e040253480420646566376366643533333162363165663833646531663831653737383531353702021930040d00000004627466730000000132&amp;hy=SH&amp;storeid=2673d18ef000047e2085a2baa0000006e03004fb3534824707bd1e67b4647b&amp;ef=3&amp;bizid=1022" externmd5="daad8cf50d5c39ac447bc4ba6eb88e18" width="300" height="298" tpurl="" tpauthkey="" attachedtext="" attachedtextcolor="" lensid="" emojiattr="" linkid="" desc=""></emoji></msg>'}, 'Status': 3, 'ImgStatus': 2, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1753661255, 'MsgSource': '<msgsource>\n\t<silence>0</silence>\n\t<membercount>73</membercount>\n\t<signature>N0_V1_D0OK3vA9|v1_deEWWXXZ</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'PushContent': '小爱在群聊中发了一个表情', 'NewMsgId': 710893574422262014, 'MsgSeq': 871404482}
2025-07-28 08:07:25 | INFO | 收到表情消息: 消息ID:1131050995 来自:48097389945@chatroom 发送人:xiaomaochong MD5:c4a688cfb58e7f6e442ce59d6a690fe8 大小:70404
2025-07-28 08:07:26 | DEBUG | [DoubaoImageRecognition] 缓存表情消息: 710893574422262014
2025-07-28 08:07:35 | DEBUG | 收到消息: {'MsgId': 1809869459, 'FromUserName': {'string': '48097389945@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 10002, 'Content': {'string': 'xiaomaochong:\n<sysmsg type="revokemsg"><revokemsg><session>48097389945@chatroom</session><msgid>1515443531</msgid><newmsgid>1897306904307323268</newmsgid><replacemsg><![CDATA["小爱" 撤回了一条消息]]></replacemsg><announcement_id><![CDATA[]]></announcement_id></revokemsg></sysmsg>'}, 'Status': 4, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1753661263, 'MsgSource': '<msgsource>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'NewMsgId': 2790734386119406705, 'MsgSeq': 871404483}
2025-07-28 08:07:35 | DEBUG | 系统消息类型: revokemsg
2025-07-28 08:07:35 | INFO | 未知的系统消息类型: {'MsgId': 1809869459, 'ToWxid': 'wxid_4usgcju5ey9q29', 'MsgType': 10002, 'Content': '\n<sysmsg type="revokemsg"><revokemsg><session>48097389945@chatroom</session><msgid>1515443531</msgid><newmsgid>1897306904307323268</newmsgid><replacemsg><![CDATA["小爱" 撤回了一条消息]]></replacemsg><announcement_id><![CDATA[]]></announcement_id></revokemsg></sysmsg>', 'Status': 4, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1753661263, 'MsgSource': '<msgsource>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'NewMsgId': 2790734386119406705, 'MsgSeq': 871404483, 'FromWxid': '48097389945@chatroom', 'IsGroup': True, 'SenderWxid': 'xiaomaochong'}
2025-07-28 08:07:44 | DEBUG | 收到消息: {'MsgId': 1920575414, 'FromUserName': {'string': '48097389945@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 1, 'Content': {'string': 'xiaomaochong:\n看破不说破'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1753661274, 'MsgSource': '<msgsource>\n\t<pua>1</pua>\n\t<silence>0</silence>\n\t<membercount>73</membercount>\n\t<signature>N0_V1_Ni421ETR|v1_d78Vl6q+</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'PushContent': '小爱 : 看破不说破', 'NewMsgId': 4257232358966150826, 'MsgSeq': 871404484}
2025-07-28 08:07:44 | INFO | 收到文本消息: 消息ID:1920575414 来自:48097389945@chatroom 发送人:xiaomaochong @:[] 内容:看破不说破
2025-07-28 08:07:44 | DEBUG | 处理消息内容: '看破不说破'
2025-07-28 08:07:44 | DEBUG | 消息内容 '看破不说破' 不匹配任何命令，忽略
2025-07-28 08:08:11 | DEBUG | 收到消息: {'MsgId': 1103553605, 'FromUserName': {'string': '48097389945@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 47, 'Content': {'string': 'xiaomaochong:\n<msg><emoji fromusername="xiaomaochong" tousername="48097389945@chatroom" type="1" idbuffer="media:0_0" md5="0a01d5a5928717ab6ad17019d7e64395" len="5264126" productid="" androidmd5="0a01d5a5928717ab6ad17019d7e64395" androidlen="5264126" s60v3md5="0a01d5a5928717ab6ad17019d7e64395" s60v3len="5264126" s60v5md5="0a01d5a5928717ab6ad17019d7e64395" s60v5len="5264126" cdnurl="http://vweixinf.tc.qq.com/110/20402/stodownload?m=0a01d5a5928717ab6ad17019d7e64395&amp;filekey=30440201010430302e02016e040253480420306130316435613539323837313761623661643137303139643765363433393502035052fe040d00000004627466730000000132&amp;hy=SH&amp;storeid=268623aeb000ac1621a46224c0000006e01004fb253482c9320315686a67a3&amp;ef=1&amp;bizid=1022" designerid="" thumburl="" encrypturl="http://vweixinf.tc.qq.com/110/20402/stodownload?m=87a104689b190ec9364a9fa5a576dd48&amp;filekey=30440201010430302e02016e04025348042038376131303436383962313930656339333634613966613561353736646434380203505300040d00000004627466730000000132&amp;hy=SH&amp;storeid=268623aec0000dae61a46224c0000006e02004fb253482c9320315686a67df&amp;ef=2&amp;bizid=1022" aeskey="92fcbc7c5cba4b799ecd4975ec112dd7" externurl="http://vweixinf.tc.qq.com/110/20403/stodownload?m=5e47300922dcd03c13c10f6d2641c564&amp;filekey=30440201010430302e02016e04025348042035653437333030393232646364303363313363313066366432363431633536340203035130040d00000004627466730000000132&amp;hy=SH&amp;storeid=268623aec0005ec6b1a46224c0000006e03004fb353482c9320315686a6831&amp;ef=3&amp;bizid=1022" externmd5="f46d0d2768bfa24bb27e5d0bee895b8a" width="300" height="300" tpurl="" tpauthkey="" attachedtext="" attachedtextcolor="" lensid="" emojiattr="" linkid="" desc=""></emoji></msg>'}, 'Status': 3, 'ImgStatus': 2, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1753661301, 'MsgSource': '<msgsource>\n\t<silence>0</silence>\n\t<membercount>73</membercount>\n\t<signature>N0_V1_rTcR/qC/|v1_57A25WDA</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'PushContent': '小爱在群聊中发了一个表情', 'NewMsgId': 8935761946104377830, 'MsgSeq': 871404485}
2025-07-28 08:08:11 | INFO | 收到表情消息: 消息ID:1103553605 来自:48097389945@chatroom 发送人:xiaomaochong MD5:0a01d5a5928717ab6ad17019d7e64395 大小:5264126
2025-07-28 08:08:11 | DEBUG | [DoubaoImageRecognition] 缓存表情消息: 8935761946104377830
2025-07-28 08:08:12 | DEBUG | 收到消息: {'MsgId': 1707904254, 'FromUserName': {'string': '48097389945@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 49, 'Content': {'string': 'wxid_cwxauba42ki122:\n<?xml version="1.0"?>\n<msg>\n\t<appmsg appid="" sdkver="0">\n\t\t<title>是的</title>\n\t\t<des />\n\t\t<username />\n\t\t<action>view</action>\n\t\t<type>57</type>\n\t\t<showtype>0</showtype>\n\t\t<content />\n\t\t<url />\n\t\t<lowurl />\n\t\t<forwardflag>0</forwardflag>\n\t\t<dataurl />\n\t\t<lowdataurl />\n\t\t<contentattr>0</contentattr>\n\t\t<streamvideo>\n\t\t\t<streamvideourl />\n\t\t\t<streamvideototaltime>0</streamvideototaltime>\n\t\t\t<streamvideotitle />\n\t\t\t<streamvideowording />\n\t\t\t<streamvideoweburl />\n\t\t\t<streamvideothumburl />\n\t\t\t<streamvideoaduxinfo />\n\t\t\t<streamvideopublishid />\n\t\t</streamvideo>\n\t\t<canvasPageItem>\n\t\t\t<canvasPageXml><![CDATA[]]></canvasPageXml>\n\t\t</canvasPageItem>\n\t\t<refermsg>\n\t\t\t<type>1</type>\n\t\t\t<svrid>3470721349139776608</svrid>\n\t\t\t<fromusr>48097389945@chatroom</fromusr>\n\t\t\t<chatusr>wxid_wlnzvr8ivgd422</chatusr>\n\t\t\t<displayname>锦岚</displayname>\n\t\t\t<msgsource>&lt;msgsource&gt;\n\t&lt;pua&gt;1&lt;/pua&gt;\n\t&lt;eggIncluded&gt;1&lt;/eggIncluded&gt;\n\t&lt;silence&gt;1&lt;/silence&gt;\n\t&lt;membercount&gt;73&lt;/membercount&gt;\n\t&lt;signature&gt;N0_V1_ElOkAkaT|v1_DpK+Ga5v&lt;/signature&gt;\n\t&lt;tmp_node&gt;\n\t\t&lt;publisher-id&gt;&lt;/publisher-id&gt;\n\t&lt;/tmp_node&gt;\n&lt;/msgsource&gt;\n</msgsource>\n\t\t\t<content>\n我退了好几次我可没踢人出去</content>\n\t\t\t<strid />\n\t\t\t<createtime>1753661221</createtime>\n\t\t</refermsg>\n\t\t<appattach>\n\t\t\t<totallen>0</totallen>\n\t\t\t<attachid />\n\t\t\t<cdnattachurl />\n\t\t\t<emoticonmd5></emoticonmd5>\n\t\t\t<aeskey></aeskey>\n\t\t\t<fileext />\n\t\t\t<islargefilemsg>0</islargefilemsg>\n\t\t</appattach>\n\t\t<extinfo />\n\t\t<androidsource>0</androidsource>\n\t\t<thumburl />\n\t\t<mediatagname />\n\t\t<messageaction><![CDATA[]]></messageaction>\n\t\t<messageext><![CDATA[]]></messageext>\n\t\t<emoticongift>\n\t\t\t<packageflag>0</packageflag>\n\t\t\t<packageid />\n\t\t</emoticongift>\n\t\t<emoticonshared>\n\t\t\t<packageflag>0</packageflag>\n\t\t\t<packageid />\n\t\t</emoticonshared>\n\t\t<designershared>\n\t\t\t<designeruin>0</designeruin>\n\t\t\t<designername>null</designername>\n\t\t\t<designerrediretcturl><![CDATA[null]]></designerrediretcturl>\n\t\t</designershared>\n\t\t<emotionpageshared>\n\t\t\t<tid>0</tid>\n\t\t\t<title>null</title>\n\t\t\t<desc>null</desc>\n\t\t\t<iconUrl><![CDATA[null]]></iconUrl>\n\t\t\t<secondUrl>null</secondUrl>\n\t\t\t<pageType>0</pageType>\n\t\t\t<setKey>null</setKey>\n\t\t</emotionpageshared>\n\t\t<webviewshared>\n\t\t\t<shareUrlOriginal />\n\t\t\t<shareUrlOpen />\n\t\t\t<jsAppId />\n\t\t\t<publisherId />\n\t\t\t<publisherReqId />\n\t\t</webviewshared>\n\t\t<template_id />\n\t\t<md5 />\n\t\t<websearch>\n\t\t\t<rec_category>0</rec_category>\n\t\t\t<channelId>0</channelId>\n\t\t</websearch>\n\t\t<weappinfo>\n\t\t\t<username />\n\t\t\t<appid />\n\t\t\t<appservicetype>0</appservicetype>\n\t\t\t<secflagforsinglepagemode>0</secflagforsinglepagemode>\n\t\t\t<videopageinfo>\n\t\t\t\t<thumbwidth>0</thumbwidth>\n\t\t\t\t<thumbheight>0</thumbheight>\n\t\t\t\t<fromopensdk>0</fromopensdk>\n\t\t\t</videopageinfo>\n\t\t</weappinfo>\n\t\t<statextstr />\n\t\t<musicShareItem>\n\t\t\t<musicDuration>0</musicDuration>\n\t\t</musicShareItem>\n\t\t<finderLiveProductShare>\n\t\t\t<finderLiveID><![CDATA[]]></finderLiveID>\n\t\t\t<finderUsername><![CDATA[]]></finderUsername>\n\t\t\t<finderObjectID><![CDATA[]]></finderObjectID>\n\t\t\t<finderNonceID><![CDATA[]]></finderNonceID>\n\t\t\t<liveStatus><![CDATA[]]></liveStatus>\n\t\t\t<appId><![CDATA[]]></appId>\n\t\t\t<pagePath><![CDATA[]]></pagePath>\n\t\t\t<productId><![CDATA[]]></productId>\n\t\t\t<coverUrl><![CDATA[]]></coverUrl>\n\t\t\t<productTitle><![CDATA[]]></productTitle>\n\t\t\t<marketPrice><![CDATA[0]]></marketPrice>\n\t\t\t<sellingPrice><![CDATA[0]]></sellingPrice>\n\t\t\t<platformHeadImg><![CDATA[]]></platformHeadImg>\n\t\t\t<platformName><![CDATA[]]></platformName>\n\t\t\t<shopWindowId><![CDATA[]]></shopWindowId>\n\t\t\t<flashSalePrice><![CDATA[0]]></flashSalePrice>\n\t\t\t<flashSaleEndTime><![CDATA[0]]></flashSaleEndTime>\n\t\t\t<ecSource><![CDATA[]]></ecSource>\n\t\t\t<sellingPriceWording><![CDATA[]]></sellingPriceWording>\n\t\t\t<platformIconURL><![CDATA[]]></platformIconURL>\n\t\t\t<firstProductTagURL><![CDATA[]]></firstProductTagURL>\n\t\t\t<firstProductTagAspectRatioString><![CDATA[0.0]]></firstProductTagAspectRatioString>\n\t\t\t<secondProductTagURL><![CDATA[]]></secondProductTagURL>\n\t\t\t<secondProductTagAspectRatioString><![CDATA[0.0]]></secondProductTagAspectRatioString>\n\t\t\t<firstGuaranteeWording><![CDATA[]]></firstGuaranteeWording>\n\t\t\t<secondGuaranteeWording><![CDATA[]]></secondGuaranteeWording>\n\t\t\t<thirdGuaranteeWording><![CDATA[]]></thirdGuaranteeWording>\n\t\t\t<isPriceBeginShow>false</isPriceBeginShow>\n\t\t\t<lastGMsgID><![CDATA[]]></lastGMsgID>\n\t\t\t<promoterKey><![CDATA[]]></promoterKey>\n\t\t\t<discountWording><![CDATA[]]></discountWording>\n\t\t\t<priceSuffixDescription><![CDATA[]]></priceSuffixDescription>\n\t\t\t<showBoxItemStringList />\n\t\t</finderLiveProductShare>\n\t\t<finderOrder>\n\t\t\t<appID><![CDATA[]]></appID>\n\t\t\t<orderID><![CDATA[]]></orderID>\n\t\t\t<path><![CDATA[]]></path>\n\t\t\t<priceWording><![CDATA[]]></priceWording>\n\t\t\t<stateWording><![CDATA[]]></stateWording>\n\t\t\t<productImageURL><![CDATA[]]></productImageURL>\n\t\t\t<products><![CDATA[]]></products>\n\t\t\t<productsCount><![CDATA[0]]></productsCount>\n\t\t</finderOrder>\n\t\t<finderShopWindowShare>\n\t\t\t<finderUsername><![CDATA[]]></finderUsername>\n\t\t\t<avatar><![CDATA[]]></avatar>\n\t\t\t<nickname><![CDATA[]]></nickname>\n\t\t\t<commodityInStockCount><![CDATA[]]></commodityInStockCount>\n\t\t\t<appId><![CDATA[]]></appId>\n\t\t\t<path><![CDATA[]]></path>\n\t\t\t<appUsername><![CDATA[]]></appUsername>\n\t\t\t<query><![CDATA[]]></query>\n\t\t\t<liteAppId><![CDATA[]]></liteAppId>\n\t\t\t<liteAppPath><![CDATA[]]></liteAppPath>\n\t\t\t<liteAppQuery><![CDATA[]]></liteAppQuery>\n\t\t\t<platformTagURL><![CDATA[]]></platformTagURL>\n\t\t\t<saleWording><![CDATA[]]></saleWording>\n\t\t\t<lastGMsgID><![CDATA[]]></lastGMsgID>\n\t\t\t<profileTypeWording><![CDATA[]]></profileTypeWording>\n\t\t\t<reputationInfo>\n\t\t\t\t<hasReputationInfo>0</hasReputationInfo>\n\t\t\t\t<reputationScore>0</reputationScore>\n\t\t\t\t<reputationWording />\n\t\t\t\t<reputationTextColor />\n\t\t\t\t<reputationLevelWording />\n\t\t\t\t<reputationBackgroundColor />\n\t\t\t</reputationInfo>\n\t\t\t<productImageURLList />\n\t\t</finderShopWindowShare>\n\t\t<findernamecard>\n\t\t\t<username />\n\t\t\t<avatar><![CDATA[]]></avatar>\n\t\t\t<nickname />\n\t\t\t<auth_job />\n\t\t\t<auth_icon>0</auth_icon>\n\t\t\t<auth_icon_url />\n\t\t\t<ecSource><![CDATA[]]></ecSource>\n\t\t\t<lastGMsgID><![CDATA[]]></lastGMsgID>\n\t\t</findernamecard>\n\t\t<finderGuarantee>\n\t\t\t<scene><![CDATA[0]]></scene>\n\t\t</finderGuarantee>\n\t\t<directshare>0</directshare>\n\t\t<gamecenter>\n\t\t\t<namecard>\n\t\t\t\t<iconUrl />\n\t\t\t\t<name />\n\t\t\t\t<desc />\n\t\t\t\t<tail />\n\t\t\t\t<jumpUrl />\n\t\t\t</namecard>\n\t\t</gamecenter>\n\t\t<patMsg>\n\t\t\t<chatUser />\n\t\t\t<records>\n\t\t\t\t<recordNum>0</recordNum>\n\t\t\t</records>\n\t\t</patMsg>\n\t\t<secretmsg>\n\t\t\t<issecretmsg>0</issecretmsg>\n\t\t</secretmsg>\n\t\t<referfromscene>0</referfromscene>\n\t\t<gameshare>\n\t\t\t<liteappext>\n\t\t\t\t<liteappbizdata />\n\t\t\t\t<priority>0</priority>\n\t\t\t</liteappext>\n\t\t\t<appbrandext>\n\t\t\t\t<litegameinfo />\n\t\t\t\t<priority>-1</priority>\n\t\t\t</appbrandext>\n\t\t\t<gameshareid />\n\t\t\t<sharedata />\n\t\t\t<isvideo>0</isvideo>\n\t\t\t<duration>-1</duration>\n\t\t\t<isexposed>0</isexposed>\n\t\t\t<readtext />\n\t\t</gameshare>\n\t\t<mpsharetrace>\n\t\t\t<hasfinderelement>0</hasfinderelement>\n\t\t\t<lastgmsgid />\n\t\t</mpsharetrace>\n\t\t<wxgamecard>\n\t\t\t<framesetname />\n\t\t\t<mbcarddata />\n\t\t\t<minpkgversion />\n\t\t\t<mbcardheight>0</mbcardheight>\n\t\t\t<isoldversion>0</isoldversion>\n\t\t</wxgamecard>\n\t\t<liteapp>\n\t\t\t<id>null</id>\n\t\t\t<path />\n\t\t\t<query />\n\t\t</liteapp>\n\t</appmsg>\n\t<fromusername>wxid_cwxauba42ki122</fromusername>\n\t<scene>0</scene>\n\t<appinfo>\n\t\t<version>1</version>\n\t\t<appname />\n\t</appinfo>\n\t<commenturl />\n</msg>\n'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1753661301, 'MsgSource': '<msgsource>\n\t<alnode>\n\t\t<fr>4</fr>\n\t</alnode>\n\t<sec_msg_node>\n\t\t<uuid>3afc218297e7128bf9bc3da7f92a397e_</uuid>\n\t\t<risk-file-flag />\n\t\t<risk-file-md5-list />\n\t</sec_msg_node>\n\t<silence>0</silence>\n\t<membercount>73</membercount>\n\t<signature>N0_V1_vc0JMDSe|v1_XceZu9vb</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'PushContent': 'ن初心ᯤ⁶ᴳ คิดถึงن : 是的', 'NewMsgId': 6180961595769603565, 'MsgSeq': 871404486}
2025-07-28 08:08:12 | DEBUG | 从群聊消息中提取发送者: wxid_cwxauba42ki122
2025-07-28 08:08:12 | DEBUG | 使用已解析的XML处理引用消息
2025-07-28 08:08:12 | INFO | 收到引用消息: 消息ID:1707904254 来自:48097389945@chatroom 发送人:wxid_cwxauba42ki122 内容:是的 引用类型:1
2025-07-28 08:08:12 | INFO | [DouBaoImageToImage] 收到引用消息: 是的
2025-07-28 08:08:12 | INFO | [TimerTask] 收到引用消息调试信息:
2025-07-28 08:08:12 | INFO |   - 消息内容: 是的
2025-07-28 08:08:12 | INFO |   - 群组ID: 48097389945@chatroom
2025-07-28 08:08:12 | INFO |   - 发送人: wxid_cwxauba42ki122
2025-07-28 08:08:12 | INFO |   - 引用信息: {'MsgType': 1, 'Content': '\n我退了好几次我可没踢人出去', 'Msgid': '3470721349139776608', 'NewMsgId': '3470721349139776608', 'ToWxid': 'wxid_4usgcju5ey9q29', 'FromWxid': '48097389945@chatroom', 'Nickname': '锦岚', 'MsgSource': '<msgsource>\n\t<pua>1</pua>\n\t<eggIncluded>1</eggIncluded>\n\t<silence>1</silence>\n\t<membercount>73</membercount>\n\t<signature>N0_V1_ElOkAkaT|v1_DpK+Ga5v</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'Createtime': '1753661221', 'SenderWxid': 'wxid_cwxauba42ki122'}
2025-07-28 08:08:12 | INFO |   - 引用消息ID: 
2025-07-28 08:08:12 | INFO |   - 引用消息类型: 
2025-07-28 08:08:12 | INFO |   - 引用消息内容: 
我退了好几次我可没踢人出去
2025-07-28 08:08:12 | INFO |   - 引用消息发送人: wxid_cwxauba42ki122
2025-07-28 08:08:19 | DEBUG | 收到消息: {'MsgId': 1545403851, 'FromUserName': {'string': '48097389945@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 3, 'Content': {'string': 'wxid_wlnzvr8ivgd422:\n<?xml version="1.0"?>\n<msg>\n\t<img aeskey="d314b6185d2a6d509938ebbfccd2919c" encryver="1" cdnthumbaeskey="d314b6185d2a6d509938ebbfccd2919c" cdnthumburl="3057020100044b3049020100020468cde53a02032f501e020415845ad302046886bf7d042437666136303135352d643439632d346630662d383638342d393663623137343535303030020405290a020201000405004c4cd200" cdnthumblength="2808" cdnthumbheight="172" cdnthumbwidth="432" cdnmidheight="0" cdnmidwidth="0" cdnhdheight="0" cdnhdwidth="0" cdnmidimgurl="3057020100044b3049020100020468cde53a02032f501e020415845ad302046886bf7d042437666136303135352d643439632d346630662d383638342d393663623137343535303030020405290a020201000405004c4cd200" length="55969" md5="4480083817f201e0f6988fb088177fd1" hevc_mid_size="8274" originsourcemd5="fbc8b81c298c347cab48aa2e097703d5">\n\t\t<secHashInfoBase64>eyJwaGFzaCI6IjcwNzAxMDAwMTAwMDEwMTAiLCJwZHFIYXNoIjoiYzgwN2UwMGQ5ZWYzN2Q3MGVl\nYTk5MTY2MmE4ZjA1NzAwMWM2ZmMxODhmYTdjMWZlZmM3ODc1ZTAwZThmOGEwZiJ9\n</secHashInfoBase64>\n\t\t<live>\n\t\t\t<duration>0</duration>\n\t\t\t<size>0</size>\n\t\t\t<md5 />\n\t\t\t<fileid />\n\t\t\t<hdsize>0</hdsize>\n\t\t\t<hdmd5 />\n\t\t\t<hdfileid />\n\t\t\t<stillimagetimems>0</stillimagetimems>\n\t\t</live>\n\t</img>\n\t<platform_signature />\n\t<imgdatahash />\n\t<ImgSourceInfo>\n\t\t<ImgSourceUrl />\n\t\t<BizType>0</BizType>\n\t</ImgSourceInfo>\n</msg>\n'}, 'Status': 3, 'ImgStatus': 2, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1753661309, 'MsgSource': '<msgsource>\n\t<sec_msg_node>\n\t\t<uuid>c5b73a0d40f71992d43f461458c2fd5a_</uuid>\n\t\t<risk-file-flag />\n\t\t<risk-file-md5-list />\n\t</sec_msg_node>\n\t<silence>0</silence>\n\t<membercount>73</membercount>\n\t<signature>N0_V1_UIPFa4tR|v1_+9zo3e+e</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'PushContent': '锦岚在群聊中发了一张图片', 'NewMsgId': 762927139507504590, 'MsgSeq': 871404488}
2025-07-28 08:08:19 | INFO | 收到图片消息: 消息ID:1545403851 来自:48097389945@chatroom 发送人:wxid_wlnzvr8ivgd422 XML:<?xml version="1.0"?><msg><img aeskey="d314b6185d2a6d509938ebbfccd2919c" encryver="1" cdnthumbaeskey="d314b6185d2a6d509938ebbfccd2919c" cdnthumburl="3057020100044b3049020100020468cde53a02032f501e020415845ad302046886bf7d042437666136303135352d643439632d346630662d383638342d393663623137343535303030020405290a020201000405004c4cd200" cdnthumblength="2808" cdnthumbheight="172" cdnthumbwidth="432" cdnmidheight="0" cdnmidwidth="0" cdnhdheight="0" cdnhdwidth="0" cdnmidimgurl="3057020100044b3049020100020468cde53a02032f501e020415845ad302046886bf7d042437666136303135352d643439632d346630662d383638342d393663623137343535303030020405290a020201000405004c4cd200" length="55969" md5="4480083817f201e0f6988fb088177fd1" hevc_mid_size="8274" originsourcemd5="fbc8b81c298c347cab48aa2e097703d5"><secHashInfoBase64>eyJwaGFzaCI6IjcwNzAxMDAwMTAwMDEwMTAiLCJwZHFIYXNoIjoiYzgwN2UwMGQ5ZWYzN2Q3MGVlYTk5MTY2MmE4ZjA1NzAwMWM2ZmMxODhmYTdjMWZlZmM3ODc1ZTAwZThmOGEwZiJ9</secHashInfoBase64><live><duration>0</duration><size>0</size><md5 /><fileid /><hdsize>0</hdsize><hdmd5 /><hdfileid /><stillimagetimems>0</stillimagetimems></live></img><platform_signature /><imgdatahash /><ImgSourceInfo><ImgSourceUrl /><BizType>0</BizType></ImgSourceInfo></msg>
2025-07-28 08:08:20 | INFO | [ImageEcho] 保存图片信息成功，当前群 48097389945@chatroom 已存储 5 张图片
2025-07-28 08:08:20 | INFO | [TimerTask] 缓存图片消息: 1545403851
2025-07-28 08:08:29 | DEBUG | 收到消息: {'MsgId': 1227888857, 'FromUserName': {'string': '48097389945@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 1, 'Content': {'string': 'wxid_cwxauba42ki122:\n@锦岚\u2005因为我们都爱你'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1753661319, 'MsgSource': '<msgsource>\n\t<atuserlist><![CDATA[wxid_wlnzvr8ivgd422]]></atuserlist>\n\t<pua>1</pua>\n\t<silence>0</silence>\n\t<membercount>73</membercount>\n\t<signature>N0_V1_fEVmCVpd|v1_nEYtg1KX</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'PushContent': 'ن初心ᯤ⁶ᴳ คิดถึงن : @锦岚\u2005因为我们都爱你', 'NewMsgId': 2535023521508522395, 'MsgSeq': 871404489}
2025-07-28 08:08:29 | INFO | 收到文本消息: 消息ID:1227888857 来自:48097389945@chatroom 发送人:wxid_cwxauba42ki122 @:['wxid_wlnzvr8ivgd422'] 内容:@锦岚 因为我们都爱你
2025-07-28 08:08:29 | DEBUG | 处理消息内容: '@锦岚 因为我们都爱你'
2025-07-28 08:08:29 | DEBUG | 消息内容 '@锦岚 因为我们都爱你' 不匹配任何命令，忽略
2025-07-28 08:08:37 | DEBUG | 收到消息: {'MsgId': 1969907009, 'FromUserName': {'string': 'wxid_ubbh6q832tcs21'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 1, 'Content': {'string': '夸克签到'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1753661328, 'MsgSource': '<msgsource>\n\t<pua>1</pua>\n\t<eggIncluded>1</eggIncluded>\n\t<signature>N0_V1_0uImd9j2|v1_GA/SjrUi</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'PushContent': '郭 : 夸克签到', 'NewMsgId': 3098408344923420818, 'MsgSeq': 871404490}
2025-07-28 08:08:37 | INFO | 收到文本消息: 消息ID:1969907009 来自:wxid_ubbh6q832tcs21 发送人:wxid_ubbh6q832tcs21 @:[] 内容:夸克签到
2025-07-28 08:08:38 | DEBUG | [QuarkSignIn] API响应状态码: 200
2025-07-28 08:08:39 | INFO | 发送文字消息: 对方wxid:wxid_ubbh6q832tcs21 at: 内容:🌟 夸克网盘签到开始
📊 检测到 1 个账号

🔄 第1个账号签到中...
👤 普通用户 夸父4527
💾 网盘总容量：19.81 GB
📈 签到累计容量：5.81 GB
✅ 今日已签到+20.00 MB，连签进度(5/7)

✨ 夸克网盘签到完成
2025-07-28 08:08:39 | DEBUG | 非群聊消息，忽略
2025-07-28 08:08:39 | WARNING | [YaoyaoPlugin] 未配置账号密码，无法自动刷新登录凭证
2025-07-28 08:08:52 | INFO | 发送图片消息: 对方wxid:wxid_ubbh6q832tcs21 图片base64略
2025-07-28 08:08:52 | DEBUG | [TempFileManager] 已清理文件: temp\yaoyao\card_1753661328.png
2025-07-28 08:09:01 | DEBUG | 收到消息: {'MsgId': 873791165, 'FromUserName': {'string': '48097389945@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 1, 'Content': {'string': 'wxid_wlnzvr8ivgd422:\n是吗，转账看看实力'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1753661351, 'MsgSource': '<msgsource>\n\t<pua>1</pua>\n\t<eggIncluded>1</eggIncluded>\n\t<silence>0</silence>\n\t<membercount>73</membercount>\n\t<signature>N0_V1_+yLA4Gcu|v1_bDQzy/Hi</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'PushContent': '锦岚 : 是吗，转账看看实力', 'NewMsgId': 8933967207254532444, 'MsgSeq': 871404497}
2025-07-28 08:09:01 | INFO | 收到文本消息: 消息ID:873791165 来自:48097389945@chatroom 发送人:wxid_wlnzvr8ivgd422 @:[] 内容:是吗，转账看看实力
2025-07-28 08:09:01 | DEBUG | 处理消息内容: '是吗，转账看看实力'
2025-07-28 08:09:01 | DEBUG | 消息内容 '是吗，转账看看实力' 不匹配任何命令，忽略
2025-07-28 08:09:19 | DEBUG | 收到消息: {'MsgId': 835057261, 'FromUserName': {'string': '48097389945@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 47, 'Content': {'string': 'last--exile:\n<msg><emoji fromusername="last--exile" tousername="48097389945@chatroom" type="2" idbuffer="media:0_0" md5="29143307de0a54475abce652dec619f3" len="4804984" productid="" androidmd5="29143307de0a54475abce652dec619f3" androidlen="4804984" s60v3md5="29143307de0a54475abce652dec619f3" s60v3len="4804984" s60v5md5="29143307de0a54475abce652dec619f3" s60v5len="4804984" cdnurl="http://wxapp.tc.qq.com/262/20304/stodownload?m=29143307de0a54475abce652dec619f3&amp;filekey=30350201010421301f0202010604025348041029143307de0a54475abce652dec619f30203495178040d00000004627466730000000131&amp;hy=SH&amp;storeid=32303232303532393230343234353030303630623762316361623664323363333636623430623030303030313036&amp;bizid=1023" designerid="" thumburl="" encrypturl="http://wxapp.tc.qq.com/262/20304/stodownload?m=87cca88b1883de7f51f00e2f5cdbd95b&amp;filekey=30350201010421301f0202010604025348041087cca88b1883de7f51f00e2f5cdbd95b0203495180040d00000004627466730000000131&amp;hy=SH&amp;storeid=32303232303532393230343234373030303539383337316361623664323331336332393630393030303030313036&amp;bizid=1023" aeskey="55db813271ea8c40b44369408c3364bc" externurl="http://wxapp.tc.qq.com/262/20304/stodownload?m=30bbcd3695f453bae9d21899aa6e0070&amp;filekey=30350201010421301f020201060402535a041030bbcd3695f453bae9d21899aa6e0070020303d8d0040d00000004627466730000000131&amp;hy=SZ&amp;storeid=32303232303532393232333432343030303163346537313437316266656639353536383830393030303030313036&amp;bizid=1023" externmd5="d1c001489bce3ec18387637d2480bc14" width="400" height="400" tpurl="" tpauthkey="" attachedtext="" attachedtextcolor="" lensid="" emojiattr="" linkid="" desc=""></emoji></msg>'}, 'Status': 3, 'ImgStatus': 2, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1753661369, 'MsgSource': '<msgsource>\n\t<silence>0</silence>\n\t<membercount>73</membercount>\n\t<signature>N0_V1_uKcU9OfU|v1_w2npGxn6</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'PushContent': '亮在群聊中发了一个表情', 'NewMsgId': 5511077851027571679, 'MsgSeq': 871404498}
2025-07-28 08:09:19 | INFO | 收到表情消息: 消息ID:835057261 来自:48097389945@chatroom 发送人:last--exile MD5:29143307de0a54475abce652dec619f3 大小:4804984
2025-07-28 08:09:20 | DEBUG | [DoubaoImageRecognition] 缓存表情消息: 5511077851027571679
2025-07-28 08:09:41 | DEBUG | 收到消息: {'MsgId': 1020628855, 'FromUserName': {'string': '48097389945@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 47, 'Content': {'string': 'wxid_cwxauba42ki122:\n<msg><emoji fromusername="wxid_cwxauba42ki122" tousername="48097389945@chatroom" type="2" idbuffer="media:0_0" md5="177cc93f487b12d4302a87c5a0ee70fe" len="3390994" productid="" androidmd5="177cc93f487b12d4302a87c5a0ee70fe" androidlen="3390994" s60v3md5="177cc93f487b12d4302a87c5a0ee70fe" s60v3len="3390994" s60v5md5="177cc93f487b12d4302a87c5a0ee70fe" s60v5len="3390994" cdnurl="http://vweixinf.tc.qq.com/110/20402/stodownload?m=177cc93f487b12d4302a87c5a0ee70fe&amp;filekey=30440201010430302e02016e0402534804203137376363393366343837623132643433303261383763356130656537306665020333be12040d00000004627466730000000132&amp;hy=SH&amp;storeid=26628fc66000e0320935f2ebf0000006e01004fb253482da30031568f0cf8e&amp;ef=1&amp;bizid=1022" designerid="" thumburl="" encrypturl="http://vweixinf.tc.qq.com/110/20402/stodownload?m=86e17d397b87cbc9e5fa1c29475d9811&amp;filekey=30440201010430302e02016e0402534804203836653137643339376238376362633965356661316332393437356439383131020333be20040d00000004627466730000000132&amp;hy=SH&amp;storeid=26628fc670002bb25935f2ebf0000006e02004fb253482da30031568f0cfd7&amp;ef=2&amp;bizid=1022" aeskey="2a0ccd1cf12242a284967186416da0d4" externurl="http://vweixinf.tc.qq.com/110/20403/stodownload?m=0f3b618d0e094d2695e4fe7159aa68a6&amp;filekey=30440201010430302e02016e0402534804203066336236313864306530393464323639356534666537313539616136386136020300d7d0040d00000004627466730000000132&amp;hy=SH&amp;storeid=26628fc6700065f35935f2ebf0000006e03004fb353482da30031568f0d00c&amp;ef=3&amp;bizid=1022" externmd5="e75a5929b9b1b8cb7b3682a82c5b0194" width="300" height="300" tpurl="" tpauthkey="" attachedtext="" attachedtextcolor="" lensid="" emojiattr="" linkid="" desc=""></emoji></msg>'}, 'Status': 3, 'ImgStatus': 2, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1753661391, 'MsgSource': '<msgsource>\n\t<silence>0</silence>\n\t<membercount>73</membercount>\n\t<signature>N0_V1_QoTAyfhZ|v1_m8ZMitRE</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'PushContent': 'ن初心ᯤ⁶ᴳ คิดถึงن在群聊中发了一个表情', 'NewMsgId': 7179372381786471206, 'MsgSeq': 871404499}
2025-07-28 08:09:41 | INFO | 收到表情消息: 消息ID:1020628855 来自:48097389945@chatroom 发送人:wxid_cwxauba42ki122 MD5:177cc93f487b12d4302a87c5a0ee70fe 大小:3390994
2025-07-28 08:09:41 | DEBUG | [DoubaoImageRecognition] 缓存表情消息: 7179372381786471206
2025-07-28 08:09:47 | DEBUG | 收到消息: {'MsgId': 310456622, 'FromUserName': {'string': '48097389945@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 47, 'Content': {'string': 'wxid_1569585694414:\n<msg><emoji fromusername = "wxid_1569585694414" tousername = "48097389945@chatroom" type="2" idbuffer="media:0_0" md5="4db9439730db261ba8d24efe5e311bdc" len = "4961482" productid="" androidmd5="4db9439730db261ba8d24efe5e311bdc" androidlen="4961482" s60v3md5 = "4db9439730db261ba8d24efe5e311bdc" s60v3len="4961482" s60v5md5 = "4db9439730db261ba8d24efe5e311bdc" s60v5len="4961482" cdnurl = "http://vweixinf.tc.qq.com/110/20402/stodownload?m=4db9439730db261ba8d24efe5e311bdc&amp;filekey=30440201010430302e02016e0402535a0420346462393433393733306462323631626138643234656665356533313162646302034bb4ca040d00000004627466730000000132&amp;hy=SZ&amp;storeid=2669d53c1000d33a4f31b8d8f0000006e01004fb2535a13165bc1e765e60ef&amp;ef=1&amp;bizid=1022" designerid = "" thumburl = "" encrypturl = "http://vweixinf.tc.qq.com/110/20402/stodownload?m=aaf50b7e8764900bbda92f9e9ad51bf9&amp;filekey=30440201010430302e02016e0402535a0420616166353062376538373634393030626264613932663965396164353162663902034bb4d0040d00000004627466730000000132&amp;hy=SZ&amp;storeid=2669d53c200031fd4f31b8d8f0000006e02004fb2535a13165bc1e765e6125&amp;ef=2&amp;bizid=1022" aeskey= "8587444f561a40e78742d7e4109316f3" externurl = "http://vweixinf.tc.qq.com/110/20403/stodownload?m=0c9334070c3cac9ad1b9219377656c2d&amp;filekey=30440201010430302e02016e0402535a04203063393333343037306333636163396164316239323139333737363536633264020302e160040d00000004627466730000000132&amp;hy=SZ&amp;storeid=2669d53c200078f9af31b8d8f0000006e03004fb3535a13165bc1e765e6144&amp;ef=3&amp;bizid=1022" externmd5 = "9c9ea95bc9dd18323f6f423780c3037e" width= "300" height= "300" tpurl= "" tpauthkey= "" attachedtext= "" attachedtextcolor= "" lensid= "" emojiattr= "" linkid= "" desc= "" ></emoji>  </msg>'}, 'Status': 3, 'ImgStatus': 2, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1753661397, 'MsgSource': '<msgsource>\n\t<silence>0</silence>\n\t<membercount>73</membercount>\n\t<signature>N0_V1_ejWu66Ua|v1_zsn05LhS</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'PushContent': '空白在群聊中发了一个表情', 'NewMsgId': 1155890276786969521, 'MsgSeq': 871404500}
2025-07-28 08:09:47 | INFO | 收到表情消息: 消息ID:310456622 来自:48097389945@chatroom 发送人:wxid_1569585694414 MD5:4db9439730db261ba8d24efe5e311bdc 大小:4961482
2025-07-28 08:09:47 | DEBUG | [DoubaoImageRecognition] 缓存表情消息: 1155890276786969521
2025-07-28 08:09:56 | DEBUG | 收到消息: {'MsgId': 1537412887, 'FromUserName': {'string': '48097389945@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 10002, 'Content': {'string': 'wxid_1569585694414:\n<sysmsg type="revokemsg"><revokemsg><session>48097389945@chatroom</session><msgid>1505171655</msgid><newmsgid>1155890276786969521</newmsgid><replacemsg><![CDATA["空白" 撤回了一条消息]]></replacemsg><announcement_id><![CDATA[]]></announcement_id></revokemsg></sysmsg>'}, 'Status': 4, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1753661404, 'MsgSource': '<msgsource>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'NewMsgId': 5733176118548849010, 'MsgSeq': 871404501}
2025-07-28 08:09:56 | DEBUG | 系统消息类型: revokemsg
2025-07-28 08:09:56 | INFO | 未知的系统消息类型: {'MsgId': 1537412887, 'ToWxid': 'wxid_4usgcju5ey9q29', 'MsgType': 10002, 'Content': '\n<sysmsg type="revokemsg"><revokemsg><session>48097389945@chatroom</session><msgid>1505171655</msgid><newmsgid>1155890276786969521</newmsgid><replacemsg><![CDATA["空白" 撤回了一条消息]]></replacemsg><announcement_id><![CDATA[]]></announcement_id></revokemsg></sysmsg>', 'Status': 4, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1753661404, 'MsgSource': '<msgsource>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'NewMsgId': 5733176118548849010, 'MsgSeq': 871404501, 'FromWxid': '48097389945@chatroom', 'IsGroup': True, 'SenderWxid': 'wxid_1569585694414'}
2025-07-28 08:10:10 | DEBUG | 收到消息: {'MsgId': 1669237732, 'FromUserName': {'string': '48097389945@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 47, 'Content': {'string': 'xiaomaochong:\n<msg><emoji fromusername="xiaomaochong" tousername="48097389945@chatroom" type="2" idbuffer="media:0_0" md5="89abe7b10e3bbbbbd9744bacaceb58f1" len="2860171" productid="" androidmd5="89abe7b10e3bbbbbd9744bacaceb58f1" androidlen="2860171" s60v3md5="89abe7b10e3bbbbbd9744bacaceb58f1" s60v3len="2860171" s60v5md5="89abe7b10e3bbbbbd9744bacaceb58f1" s60v5len="2860171" cdnurl="http://vweixinf.tc.qq.com/110/20401/stodownload?m=89abe7b10e3bbbbbd9744bacaceb58f1&amp;filekey=30440201010430302e02016e0402535a0420383961626537623130653362626262626439373434626163616365623538663102032ba48b040d00000004627466730000000132&amp;hy=SZ&amp;storeid=26841cc04000ce4f6293b1ae80000006e01004fb1535a03a6bae1e69b92465&amp;ef=1&amp;bizid=1022" designerid="" thumburl="" encrypturl="http://vweixinf.tc.qq.com/110/20402/stodownload?m=42a840cf5120c1ecec9999e348ea4003&amp;filekey=30440201010430302e02016e0402535a0420343261383430636635313230633165636563393939396533343865613430303302032ba490040d00000004627466730000000132&amp;hy=SZ&amp;storeid=26841cc0500006a6d293b1ae80000006e02004fb2535a03a6bae1e69b92489&amp;ef=2&amp;bizid=1022" aeskey="69f288a879624adeb13982051165b73c" externurl="http://vweixinf.tc.qq.com/110/20403/stodownload?m=4f45f7993f8c46323606fc6b1d7c5f0f&amp;filekey=30440201010430302e02016e0402535a042034663435663739393366386334363332333630366663366231643763356630660203015250040d00000004627466730000000132&amp;hy=SZ&amp;storeid=26841cc050003a85d293b1ae80000006e03004fb3535a03a6bae1e69b924b2&amp;ef=3&amp;bizid=1022" externmd5="2834b5b9dc8dd0da3b83a502807357fb" width="300" height="300" tpurl="" tpauthkey="" attachedtext="" attachedtextcolor="" lensid="" emojiattr="" linkid="" desc=""></emoji></msg>'}, 'Status': 3, 'ImgStatus': 2, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1753661420, 'MsgSource': '<msgsource>\n\t<silence>0</silence>\n\t<membercount>73</membercount>\n\t<signature>N0_V1_wqunXmoJ|v1_hi0s+nkY</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'PushContent': '小爱在群聊中发了一个表情', 'NewMsgId': 6270932885554897900, 'MsgSeq': 871404502}
2025-07-28 08:10:10 | INFO | 收到表情消息: 消息ID:1669237732 来自:48097389945@chatroom 发送人:xiaomaochong MD5:89abe7b10e3bbbbbd9744bacaceb58f1 大小:2860171
2025-07-28 08:10:10 | DEBUG | [DoubaoImageRecognition] 缓存表情消息: 6270932885554897900
2025-07-28 08:11:01 | DEBUG | 收到消息: {'MsgId': 756325314, 'FromUserName': {'string': '48097389945@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 49, 'Content': {'string': 'xiaomaochong:\n<?xml version="1.0"?>\n<msg>\n\t<appmsg appid="wx485a97c844086dc9" sdkver="0">\n\t\t<title>Extended Version</title>\n\t\t<des>小爱</des>\n\t\t<username />\n\t\t<action>view</action>\n\t\t<type>3</type>\n\t\t<showtype>0</showtype>\n\t\t<content />\n\t\t<url />\n\t\t<lowurl />\n\t\t<forwardflag>0</forwardflag>\n\t\t<dataurl>https://v3-luna.douyinvod.com/72744df15bff4f435574eeec6d15538d/688821bb/video/tos/cn/tos-cn-ve-2774/oAeEVQbk1CRNBerQDWacLZwBQKVbMV9GtPrgIn/</dataurl>\n\t\t<lowdataurl />\n\t\t<contentattr>0</contentattr>\n\t\t<streamvideo>\n\t\t\t<streamvideourl />\n\t\t\t<streamvideototaltime>0</streamvideototaltime>\n\t\t\t<streamvideotitle />\n\t\t\t<streamvideowording />\n\t\t\t<streamvideoweburl />\n\t\t\t<streamvideothumburl />\n\t\t\t<streamvideoaduxinfo />\n\t\t\t<streamvideopublishid />\n\t\t</streamvideo>\n\t\t<canvasPageItem>\n\t\t\t<canvasPageXml><![CDATA[]]></canvasPageXml>\n\t\t</canvasPageItem>\n\t\t<appattach>\n\t\t\t<totallen>0</totallen>\n\t\t\t<attachid />\n\t\t\t<cdnattachurl />\n\t\t\t<emoticonmd5></emoticonmd5>\n\t\t\t<aeskey></aeskey>\n\t\t\t<fileext />\n\t\t\t<islargefilemsg>0</islargefilemsg>\n\t\t</appattach>\n\t\t<extinfo />\n\t\t<androidsource>2</androidsource>\n\t\t<thumburl />\n\t\t<mediatagname />\n\t\t<messageaction><![CDATA[]]></messageaction>\n\t\t<messageext><![CDATA[]]></messageext>\n\t\t<emoticongift>\n\t\t\t<packageflag>0</packageflag>\n\t\t\t<packageid />\n\t\t</emoticongift>\n\t\t<emoticonshared>\n\t\t\t<packageflag>0</packageflag>\n\t\t\t<packageid />\n\t\t</emoticonshared>\n\t\t<designershared>\n\t\t\t<designeruin>0</designeruin>\n\t\t\t<designername>null</designername>\n\t\t\t<designerrediretcturl><![CDATA[null]]></designerrediretcturl>\n\t\t</designershared>\n\t\t<emotionpageshared>\n\t\t\t<tid>0</tid>\n\t\t\t<title>null</title>\n\t\t\t<desc>null</desc>\n\t\t\t<iconUrl><![CDATA[null]]></iconUrl>\n\t\t\t<secondUrl>null</secondUrl>\n\t\t\t<pageType>0</pageType>\n\t\t\t<setKey>null</setKey>\n\t\t</emotionpageshared>\n\t\t<webviewshared>\n\t\t\t<shareUrlOriginal />\n\t\t\t<shareUrlOpen />\n\t\t\t<jsAppId />\n\t\t\t<publisherId />\n\t\t\t<publisherReqId />\n\t\t</webviewshared>\n\t\t<template_id />\n\t\t<md5 />\n\t\t<websearch>\n\t\t\t<rec_category>0</rec_category>\n\t\t\t<channelId>0</channelId>\n\t\t</websearch>\n\t\t<weappinfo>\n\t\t\t<username />\n\t\t\t<appid />\n\t\t\t<appservicetype>0</appservicetype>\n\t\t\t<secflagforsinglepagemode>0</secflagforsinglepagemode>\n\t\t\t<videopageinfo>\n\t\t\t\t<thumbwidth>0</thumbwidth>\n\t\t\t\t<thumbheight>0</thumbheight>\n\t\t\t\t<fromopensdk>0</fromopensdk>\n\t\t\t</videopageinfo>\n\t\t</weappinfo>\n\t\t<statextstr>GhQKEnd4NDg1YTk3Yzg0NDA4NmRjOQ==</statextstr>\n\t\t<songalbumurl>https://wx.qlogo.cn/mmhead/ver_1/6iaSQLKjLK6YMpTGwdaFEJHr0OKpQPK5HR2zUmibE6f8W4tsSjkT8RbjHCBOnoIdKv28tarjHhJic6qRhiczIdOzbEUTMQebeT1XouSEMct9ofiaha74cyibDl8vxtUztxWIicB/0</songalbumurl>\n\t\t<songlyric>[99:99.99]小爱提醒您该歌曲暂无歌词！</songlyric>\n\t\t<musicShareItem>\n\t\t\t<musicDuration>0</musicDuration>\n\t\t</musicShareItem>\n\t\t<finderLiveProductShare>\n\t\t\t<finderLiveID><![CDATA[]]></finderLiveID>\n\t\t\t<finderUsername><![CDATA[]]></finderUsername>\n\t\t\t<finderObjectID><![CDATA[]]></finderObjectID>\n\t\t\t<finderNonceID><![CDATA[]]></finderNonceID>\n\t\t\t<liveStatus><![CDATA[]]></liveStatus>\n\t\t\t<appId><![CDATA[]]></appId>\n\t\t\t<pagePath><![CDATA[]]></pagePath>\n\t\t\t<productId><![CDATA[]]></productId>\n\t\t\t<coverUrl><![CDATA[]]></coverUrl>\n\t\t\t<productTitle><![CDATA[]]></productTitle>\n\t\t\t<marketPrice><![CDATA[0]]></marketPrice>\n\t\t\t<sellingPrice><![CDATA[0]]></sellingPrice>\n\t\t\t<platformHeadImg><![CDATA[]]></platformHeadImg>\n\t\t\t<platformName><![CDATA[]]></platformName>\n\t\t\t<shopWindowId><![CDATA[]]></shopWindowId>\n\t\t\t<flashSalePrice><![CDATA[0]]></flashSalePrice>\n\t\t\t<flashSaleEndTime><![CDATA[0]]></flashSaleEndTime>\n\t\t\t<ecSource><![CDATA[]]></ecSource>\n\t\t\t<sellingPriceWording><![CDATA[]]></sellingPriceWording>\n\t\t\t<platformIconURL><![CDATA[]]></platformIconURL>\n\t\t\t<firstProductTagURL><![CDATA[]]></firstProductTagURL>\n\t\t\t<firstProductTagAspectRatioString><![CDATA[0.0]]></firstProductTagAspectRatioString>\n\t\t\t<secondProductTagURL><![CDATA[]]></secondProductTagURL>\n\t\t\t<secondProductTagAspectRatioString><![CDATA[0.0]]></secondProductTagAspectRatioString>\n\t\t\t<firstGuaranteeWording><![CDATA[]]></firstGuaranteeWording>\n\t\t\t<secondGuaranteeWording><![CDATA[]]></secondGuaranteeWording>\n\t\t\t<thirdGuaranteeWording><![CDATA[]]></thirdGuaranteeWording>\n\t\t\t<isPriceBeginShow>false</isPriceBeginShow>\n\t\t\t<lastGMsgID><![CDATA[]]></lastGMsgID>\n\t\t\t<promoterKey><![CDATA[]]></promoterKey>\n\t\t\t<discountWording><![CDATA[]]></discountWording>\n\t\t\t<priceSuffixDescription><![CDATA[]]></priceSuffixDescription>\n\t\t\t<showBoxItemStringList />\n\t\t</finderLiveProductShare>\n\t\t<finderOrder>\n\t\t\t<appID><![CDATA[]]></appID>\n\t\t\t<orderID><![CDATA[]]></orderID>\n\t\t\t<path><![CDATA[]]></path>\n\t\t\t<priceWording><![CDATA[]]></priceWording>\n\t\t\t<stateWording><![CDATA[]]></stateWording>\n\t\t\t<productImageURL><![CDATA[]]></productImageURL>\n\t\t\t<products><![CDATA[]]></products>\n\t\t\t<productsCount><![CDATA[0]]></productsCount>\n\t\t</finderOrder>\n\t\t<finderShopWindowShare>\n\t\t\t<finderUsername><![CDATA[]]></finderUsername>\n\t\t\t<avatar><![CDATA[]]></avatar>\n\t\t\t<nickname><![CDATA[]]></nickname>\n\t\t\t<commodityInStockCount><![CDATA[]]></commodityInStockCount>\n\t\t\t<appId><![CDATA[]]></appId>\n\t\t\t<path><![CDATA[]]></path>\n\t\t\t<appUsername><![CDATA[]]></appUsername>\n\t\t\t<query><![CDATA[]]></query>\n\t\t\t<liteAppId><![CDATA[]]></liteAppId>\n\t\t\t<liteAppPath><![CDATA[]]></liteAppPath>\n\t\t\t<liteAppQuery><![CDATA[]]></liteAppQuery>\n\t\t\t<platformTagURL><![CDATA[]]></platformTagURL>\n\t\t\t<saleWording><![CDATA[]]></saleWording>\n\t\t\t<lastGMsgID><![CDATA[]]></lastGMsgID>\n\t\t\t<profileTypeWording><![CDATA[]]></profileTypeWording>\n\t\t\t<reputationInfo>\n\t\t\t\t<hasReputationInfo>0</hasReputationInfo>\n\t\t\t\t<reputationScore>0</reputationScore>\n\t\t\t\t<reputationWording />\n\t\t\t\t<reputationTextColor />\n\t\t\t\t<reputationLevelWording />\n\t\t\t\t<reputationBackgroundColor />\n\t\t\t</reputationInfo>\n\t\t\t<productImageURLList />\n\t\t</finderShopWindowShare>\n\t\t<findernamecard>\n\t\t\t<username />\n\t\t\t<avatar><![CDATA[]]></avatar>\n\t\t\t<nickname />\n\t\t\t<auth_job />\n\t\t\t<auth_icon>0</auth_icon>\n\t\t\t<auth_icon_url />\n\t\t\t<ecSource><![CDATA[]]></ecSource>\n\t\t\t<lastGMsgID><![CDATA[]]></lastGMsgID>\n\t\t</findernamecard>\n\t\t<finderGuarantee>\n\t\t\t<scene><![CDATA[0]]></scene>\n\t\t</finderGuarantee>\n\t\t<directshare>0</directshare>\n\t\t<gamecenter>\n\t\t\t<namecard>\n\t\t\t\t<iconUrl />\n\t\t\t\t<name />\n\t\t\t\t<desc />\n\t\t\t\t<tail />\n\t\t\t\t<jumpUrl />\n\t\t\t</namecard>\n\t\t</gamecenter>\n\t\t<patMsg>\n\t\t\t<chatUser />\n\t\t\t<records>\n\t\t\t\t<recordNum>0</recordNum>\n\t\t\t</records>\n\t\t</patMsg>\n\t\t<secretmsg>\n\t\t\t<issecretmsg>0</issecretmsg>\n\t\t</secretmsg>\n\t\t<referfromscene>0</referfromscene>\n\t\t<gameshare>\n\t\t\t<liteappext>\n\t\t\t\t<liteappbizdata />\n\t\t\t\t<priority>0</priority>\n\t\t\t</liteappext>\n\t\t\t<appbrandext>\n\t\t\t\t<litegameinfo />\n\t\t\t\t<priority>-1</priority>\n\t\t\t</appbrandext>\n\t\t\t<gameshareid />\n\t\t\t<sharedata />\n\t\t\t<isvideo>0</isvideo>\n\t\t\t<duration>-1</duration>\n\t\t\t<isexposed>0</isexposed>\n\t\t\t<readtext />\n\t\t</gameshare>\n\t\t<mpsharetrace>\n\t\t\t<hasfinderelement>0</hasfinderelement>\n\t\t\t<lastgmsgid />\n\t\t</mpsharetrace>\n\t\t<wxgamecard>\n\t\t\t<framesetname />\n\t\t\t<mbcarddata />\n\t\t\t<minpkgversion />\n\t\t\t<mbcardheight>0</mbcardheight>\n\t\t\t<isoldversion>0</isoldversion>\n\t\t</wxgamecard>\n\t\t<liteapp>\n\t\t\t<id>null</id>\n\t\t\t<path />\n\t\t\t<query />\n\t\t</liteapp>\n\t</appmsg>\n\t<fromusername>xiaomaochong</fromusername>\n\t<scene>0</scene>\n\t<appinfo>\n\t\t<version>29</version>\n\t\t<appname>摇一摇搜歌</appname>\n\t</appinfo>\n\t<commenturl />\n</msg>\n'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1753661471, 'MsgSource': '<msgsource>\n\t<sec_msg_node>\n\t\t<uuid>9d92994c6494d636c6c4fdb193968379_</uuid>\n\t\t<risk-file-flag />\n\t\t<risk-file-md5-list />\n\t</sec_msg_node>\n\t<silence>0</silence>\n\t<membercount>73</membercount>\n\t<signature>N0_V1_mGTBm8QL|v1_xKV+TiTS</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'PushContent': '你收到了一条消息', 'NewMsgId': 9051914278402808297, 'MsgSeq': 871404503}
2025-07-28 08:11:01 | DEBUG | 从群聊消息中提取发送者: xiaomaochong
2025-07-28 08:11:01 | DEBUG | XML消息完整内容:
<?xml version="1.0"?>
<msg>
	<appmsg appid="wx485a97c844086dc9" sdkver="0">
		<title>Extended Version</title>
		<des>小爱</des>
		<username />
		<action>view</action>
		<type>3</type>
		<showtype>0</showtype>
		<content />
		<url />
		<lowurl />
		<forwardflag>0</forwardflag>
		<dataurl>https://v3-luna.douyinvod.com/72744df15bff4f435574eeec6d15538d/688821bb/video/tos/cn/tos-cn-ve-2774/oAeEVQbk1CRNBerQDWacLZwBQKVbMV9GtPrgIn/</dataurl>
		<lowdataurl />
		<contentattr>0</contentattr>
		<streamvideo>
			<streamvideourl />
			<streamvideototaltime>0</streamvideototaltime>
			<streamvideotitle />
			<streamvideowording />
			<streamvideoweburl />
			<streamvideothumburl />
			<streamvideoaduxinfo />
			<streamvideopublishid />
		</streamvideo>
		<canvasPageItem>
			<canvasPageXml><![CDATA[]]></canvasPageXml>
		</canvasPageItem>
		<appattach>
			<totallen>0</totallen>
			<attachid />
			<cdnattachurl />
			<emoticonmd5></emoticonmd5>
			<aeskey></aeskey>
			<fileext />
			<islargefilemsg>0</islargefilemsg>
		</appattach>
		<extinfo />
		<androidsource>2</androidsource>
		<thumburl />
		<mediatagname />
		<messageaction><![CDATA[]]></messageaction>
		<messageext><![CDATA[]]></messageext>
		<emoticongift>
			<packageflag>0</packageflag>
			<packageid />
		</emoticongift>
		<emoticonshared>
			<packageflag>0</packageflag>
			<packageid />
		</emoticonshared>
		<designershared>
			<designeruin>0</designeruin>
			<designername>null</designername>
			<designerrediretcturl><![CDATA[null]]></designerrediretcturl>
		</designershared>
		<emotionpageshared>
			<tid>0</tid>
			<title>null</title>
			<desc>null</desc>
			<iconUrl><![CDATA[null]]></iconUrl>
			<secondUrl>null</secondUrl>
			<pageType>0</pageType>
			<setKey>null</setKey>
		</emotionpageshared>
		<webviewshared>
			<shareUrlOriginal />
			<shareUrlOpen />
			<jsAppId />
			<publisherId />
			<publisherReqId />
		</webviewshared>
		<template_id />
		<md5 />
		<websearch>
			<rec_category>0</rec_category>
			<channelId>0</channelId>
		</websearch>
		<weappinfo>
			<username />
			<appid />
			<appservicetype>0</appservicetype>
			<secflagforsinglepagemode>0</secflagforsinglepagemode>
			<videopageinfo>
				<thumbwidth>0</thumbwidth>
				<thumbheight>0</thumbheight>
				<fromopensdk>0</fromopensdk>
			</videopageinfo>
		</weappinfo>
		<statextstr>GhQKEnd4NDg1YTk3Yzg0NDA4NmRjOQ==</statextstr>
		<songalbumurl>https://wx.qlogo.cn/mmhead/ver_1/6iaSQLKjLK6YMpTGwdaFEJHr0OKpQPK5HR2zUmibE6f8W4tsSjkT8RbjHCBOnoIdKv28tarjHhJic6qRhiczIdOzbEUTMQebeT1XouSEMct9ofiaha74cyibDl8vxtUztxWIicB/0</songalbumurl>
		<songlyric>[99:99.99]小爱提醒您该歌曲暂无歌词！</songlyric>
		<musicShareItem>
			<musicDuration>0</musicDuration>
		</musicShareItem>
		<finderLiveProductShare>
			<finderLiveID><![CDATA[]]></finderLiveID>
			<finderUsername><![CDATA[]]></finderUsername>
			<finderObjectID><![CDATA[]]></finderObjectID>
			<finderNonceID><![CDATA[]]></finderNonceID>
			<liveStatus><![CDATA[]]></liveStatus>
			<appId><![CDATA[]]></appId>
			<pagePath><![CDATA[]]></pagePath>
			<productId><![CDATA[]]></productId>
			<coverUrl><![CDATA[]]></coverUrl>
			<productTitle><![CDATA[]]></productTitle>
			<marketPrice><![CDATA[0]]></marketPrice>
			<sellingPrice><![CDATA[0]]></sellingPrice>
			<platformHeadImg><![CDATA[]]></platformHeadImg>
			<platformName><![CDATA[]]></platformName>
			<shopWindowId><![CDATA[]]></shopWindowId>
			<flashSalePrice><![CDATA[0]]></flashSalePrice>
			<flashSaleEndTime><![CDATA[0]]></flashSaleEndTime>
			<ecSource><![CDATA[]]></ecSource>
			<sellingPriceWording><![CDATA[]]></sellingPriceWording>
			<platformIconURL><![CDATA[]]></platformIconURL>
			<firstProductTagURL><![CDATA[]]></firstProductTagURL>
			<firstProductTagAspectRatioString><![CDATA[0.0]]></firstProductTagAspectRatioString>
			<secondProductTagURL><![CDATA[]]></secondProductTagURL>
			<secondProductTagAspectRatioString><![CDATA[0.0]]></secondProductTagAspectRatioString>
			<firstGuaranteeWording><![CDATA[]]></firstGuaranteeWording>
			<secondGuaranteeWording><![CDATA[]]></secondGuaranteeWording>
			<thirdGuaranteeWording><![CDATA[]]></thirdGuaranteeWording>
			<isPriceBeginShow>false</isPriceBeginShow>
			<lastGMsgID><![CDATA[]]></lastGMsgID>
			<promoterKey><![CDATA[]]></promoterKey>
			<discountWording><![CDATA[]]></discountWording>
			<priceSuffixDescription><![CDATA[]]></priceSuffixDescription>
			<showBoxItemStringList />
		</finderLiveProductShare>
		<finderOrder>
			<appID><![CDATA[]]></appID>
			<orderID><![CDATA[]]></orderID>
			<path><![CDATA[]]></path>
			<priceWording><![CDATA[]]></priceWording>
			<stateWording><![CDATA[]]></stateWording>
			<productImageURL><![CDATA[]]></productImageURL>
			<products><![CDATA[]]></products>
			<productsCount><![CDATA[0]]></productsCount>
		</finderOrder>
		<finderShopWindowShare>
			<finderUsername><![CDATA[]]></finderUsername>
			<avatar><![CDATA[]]></avatar>
			<nickname><![CDATA[]]></nickname>
			<commodityInStockCount><![CDATA[]]></commodityInStockCount>
			<appId><![CDATA[]]></appId>
			<path><![CDATA[]]></path>
			<appUsername><![CDATA[]]></appUsername>
			<query><![CDATA[]]></query>
			<liteAppId><![CDATA[]]></liteAppId>
			<liteAppPath><![CDATA[]]></liteAppPath>
			<liteAppQuery><![CDATA[]]></liteAppQuery>
			<platformTagURL><![CDATA[]]></platformTagURL>
			<saleWording><![CDATA[]]></saleWording>
			<lastGMsgID><![CDATA[]]></lastGMsgID>
			<profileTypeWording><![CDATA[]]></profileTypeWording>
			<reputationInfo>
				<hasReputationInfo>0</hasReputationInfo>
				<reputationScore>0</reputationScore>
				<reputationWording />
				<reputationTextColor />
				<reputationLevelWording />
				<reputationBackgroundColor />
			</reputationInfo>
			<productImageURLList />
		</finderShopWindowShare>
		<findernamecard>
			<username />
			<avatar><![CDATA[]]></avatar>
			<nickname />
			<auth_job />
			<auth_icon>0</auth_icon>
			<auth_icon_url />
			<ecSource><![CDATA[]]></ecSource>
			<lastGMsgID><![CDATA[]]></lastGMsgID>
		</findernamecard>
		<finderGuarantee>
			<scene><![CDATA[0]]></scene>
		</finderGuarantee>
		<directshare>0</directshare>
		<gamecenter>
			<namecard>
				<iconUrl />
				<name />
				<desc />
				<tail />
				<jumpUrl />
			</namecard>
		</gamecenter>
		<patMsg>
			<chatUser />
			<records>
				<recordNum>0</recordNum>
			</records>
		</patMsg>
		<secretmsg>
			<issecretmsg>0</issecretmsg>
		</secretmsg>
		<referfromscene>0</referfromscene>
		<gameshare>
			<liteappext>
				<liteappbizdata />
				<priority>0</priority>
			</liteappext>
			<appbrandext>
				<litegameinfo />
				<priority>-1</priority>
			</appbrandext>
			<gameshareid />
			<sharedata />
			<isvideo>0</isvideo>
			<duration>-1</duration>
			<isexposed>0</isexposed>
			<readtext />
		</gameshare>
		<mpsharetrace>
			<hasfinderelement>0</hasfinderelement>
			<lastgmsgid />
		</mpsharetrace>
		<wxgamecard>
			<framesetname />
			<mbcarddata />
			<minpkgversion />
			<mbcardheight>0</mbcardheight>
			<isoldversion>0</isoldversion>
		</wxgamecard>
		<liteapp>
			<id>null</id>
			<path />
			<query />
		</liteapp>
	</appmsg>
	<fromusername>xiaomaochong</fromusername>
	<scene>0</scene>
	<appinfo>
		<version>29</version>
		<appname>摇一摇搜歌</appname>
	</appinfo>
	<commenturl />
</msg>

2025-07-28 08:11:01 | DEBUG | XML消息类型: 3
2025-07-28 08:11:01 | DEBUG | XML消息标题: Extended Version
2025-07-28 08:11:01 | DEBUG | XML消息描述: 小爱
2025-07-28 08:11:01 | DEBUG | 附件信息 totallen: 0
2025-07-28 08:11:01 | DEBUG | 附件信息 islargefilemsg: 0
2025-07-28 08:11:01 | INFO | 收到红包消息: 标题:Extended Version 描述:小爱 来自:48097389945@chatroom 发送人:wxid_4usgcju5ey9q29
2025-07-28 08:11:44 | DEBUG | 收到消息: {'MsgId': 1641432572, 'FromUserName': {'string': '48097389945@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 3, 'Content': {'string': 'wxid_lneb7n23o4lg12:\n<?xml version="1.0"?>\n<msg>\n\t<img aeskey="f838c1f614ad87170971a9f452d18493" encryver="1" cdnthumbaeskey="f838c1f614ad87170971a9f452d18493" cdnthumburl="3057020100044b304902010002046b3e4bb802032f51490204d931227502046886c049042430333461633430302d396462392d346630332d616235632d663732303637633166666633020405290a020201000405004c537500" cdnthumblength="6091" cdnthumbheight="144" cdnthumbwidth="57" cdnmidheight="0" cdnmidwidth="0" cdnhdheight="0" cdnhdwidth="0" cdnmidimgurl="3057020100044b304902010002046b3e4bb802032f51490204d931227502046886c049042430333461633430302d396462392d346630332d616235632d663732303637633166666633020405290a020201000405004c537500" length="243441" md5="5aeb2e4e0516cc86927f7a5a4a317320" originsourcemd5="4990a86da802de18bcfaf2fb4676928e">\n\t\t<secHashInfoBase64 />\n\t\t<live>\n\t\t\t<duration>0</duration>\n\t\t\t<size>0</size>\n\t\t\t<md5 />\n\t\t\t<fileid />\n\t\t\t<hdsize>0</hdsize>\n\t\t\t<hdmd5 />\n\t\t\t<hdfileid />\n\t\t\t<stillimagetimems>0</stillimagetimems>\n\t\t</live>\n\t</img>\n\t<platform_signature />\n\t<imgdatahash />\n\t<ImgSourceInfo>\n\t\t<ImgSourceUrl />\n\t\t<BizType>0</BizType>\n\t</ImgSourceInfo>\n</msg>\n'}, 'Status': 3, 'ImgStatus': 2, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1753661513, 'MsgSource': '<msgsource>\n\t<alnode>\n\t\t<fr>2</fr>\n\t</alnode>\n\t<sec_msg_node>\n\t\t<uuid>15e1046f0d795535013e2d8a1c20da89_</uuid>\n\t\t<risk-file-flag />\n\t\t<risk-file-md5-list />\n\t</sec_msg_node>\n\t<silence>0</silence>\n\t<membercount>73</membercount>\n\t<signature>N0_V1_EPu4XdYD|v1_XWs0WhIN</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'PushContent': 'ᡣ在群聊中发了一张图片', 'NewMsgId': 3538648359176284645, 'MsgSeq': 871404504}
2025-07-28 08:11:44 | INFO | 收到图片消息: 消息ID:1641432572 来自:48097389945@chatroom 发送人:wxid_lneb7n23o4lg12 XML:<?xml version="1.0"?><msg><img aeskey="f838c1f614ad87170971a9f452d18493" encryver="1" cdnthumbaeskey="f838c1f614ad87170971a9f452d18493" cdnthumburl="3057020100044b304902010002046b3e4bb802032f51490204d931227502046886c049042430333461633430302d396462392d346630332d616235632d663732303637633166666633020405290a020201000405004c537500" cdnthumblength="6091" cdnthumbheight="144" cdnthumbwidth="57" cdnmidheight="0" cdnmidwidth="0" cdnhdheight="0" cdnhdwidth="0" cdnmidimgurl="3057020100044b304902010002046b3e4bb802032f51490204d931227502046886c049042430333461633430302d396462392d346630332d616235632d663732303637633166666633020405290a020201000405004c537500" length="243441" md5="5aeb2e4e0516cc86927f7a5a4a317320" originsourcemd5="4990a86da802de18bcfaf2fb4676928e"><secHashInfoBase64 /><live><duration>0</duration><size>0</size><md5 /><fileid /><hdsize>0</hdsize><hdmd5 /><hdfileid /><stillimagetimems>0</stillimagetimems></live></img><platform_signature /><imgdatahash /><ImgSourceInfo><ImgSourceUrl /><BizType>0</BizType></ImgSourceInfo></msg>
2025-07-28 08:11:44 | INFO | [ImageEcho] 保存图片信息成功，当前群 48097389945@chatroom 已存储 5 张图片
2025-07-28 08:11:44 | INFO | [TimerTask] 缓存图片消息: 1641432572
2025-07-28 08:12:42 | DEBUG | 收到消息: {'MsgId': 301586704, 'FromUserName': {'string': '43607022446@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 3, 'Content': {'string': 'wxid_lneb7n23o4lg12:\n<?xml version="1.0"?>\n<msg>\n\t<img aeskey="6afb2e30ba747fa85694846aa654bb2e" encryver="1" cdnthumbaeskey="6afb2e30ba747fa85694846aa654bb2e" cdnthumburl="3057020100044b304902010002046b3e4bb802032f514902041d328e7102046886c083042463616631376164352d356632332d343265652d396537652d326462623363393933633564020405290a020201000405004c537500" cdnthumblength="6091" cdnthumbheight="144" cdnthumbwidth="57" cdnmidheight="0" cdnmidwidth="0" cdnhdheight="0" cdnhdwidth="0" cdnmidimgurl="3057020100044b304902010002046b3e4bb802032f514902041d328e7102046886c083042463616631376164352d356632332d343265652d396537652d326462623363393933633564020405290a020201000405004c537500" length="243441" md5="5aeb2e4e0516cc86927f7a5a4a317320" originsourcemd5="4990a86da802de18bcfaf2fb4676928e">\n\t\t<secHashInfoBase64 />\n\t\t<live>\n\t\t\t<duration>0</duration>\n\t\t\t<size>0</size>\n\t\t\t<md5 />\n\t\t\t<fileid />\n\t\t\t<hdsize>0</hdsize>\n\t\t\t<hdmd5 />\n\t\t\t<hdfileid />\n\t\t\t<stillimagetimems>0</stillimagetimems>\n\t\t</live>\n\t</img>\n\t<platform_signature />\n\t<imgdatahash />\n\t<ImgSourceInfo>\n\t\t<ImgSourceUrl />\n\t\t<BizType>0</BizType>\n\t</ImgSourceInfo>\n</msg>\n'}, 'Status': 3, 'ImgStatus': 2, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1753661572, 'MsgSource': '<msgsource>\n\t<alnode>\n\t\t<fr>2</fr>\n\t</alnode>\n\t<sec_msg_node>\n\t\t<uuid>56a5ee65292b4de9ea9a668913341b18_</uuid>\n\t\t<risk-file-flag />\n\t\t<risk-file-md5-list />\n\t</sec_msg_node>\n\t<silence>0</silence>\n\t<membercount>76</membercount>\n\t<signature>N0_V1_nYJ9y52t|v1_aTWaVEuq</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'PushContent': '吃瓜小爱\ue124\ue124\u2005在群聊中发了一张图片', 'NewMsgId': 4322344685646724406, 'MsgSeq': 871404505}
2025-07-28 08:12:42 | INFO | 收到图片消息: 消息ID:301586704 来自:43607022446@chatroom 发送人:wxid_lneb7n23o4lg12 XML:<?xml version="1.0"?><msg><img aeskey="6afb2e30ba747fa85694846aa654bb2e" encryver="1" cdnthumbaeskey="6afb2e30ba747fa85694846aa654bb2e" cdnthumburl="3057020100044b304902010002046b3e4bb802032f514902041d328e7102046886c083042463616631376164352d356632332d343265652d396537652d326462623363393933633564020405290a020201000405004c537500" cdnthumblength="6091" cdnthumbheight="144" cdnthumbwidth="57" cdnmidheight="0" cdnmidwidth="0" cdnhdheight="0" cdnhdwidth="0" cdnmidimgurl="3057020100044b304902010002046b3e4bb802032f514902041d328e7102046886c083042463616631376164352d356632332d343265652d396537652d326462623363393933633564020405290a020201000405004c537500" length="243441" md5="5aeb2e4e0516cc86927f7a5a4a317320" originsourcemd5="4990a86da802de18bcfaf2fb4676928e"><secHashInfoBase64 /><live><duration>0</duration><size>0</size><md5 /><fileid /><hdsize>0</hdsize><hdmd5 /><hdfileid /><stillimagetimems>0</stillimagetimems></live></img><platform_signature /><imgdatahash /><ImgSourceInfo><ImgSourceUrl /><BizType>0</BizType></ImgSourceInfo></msg>
2025-07-28 08:12:42 | INFO | [ImageEcho] 保存图片信息成功，当前群 43607022446@chatroom 已存储 5 张图片
2025-07-28 08:12:42 | INFO | [TimerTask] 缓存图片消息: 301586704
2025-07-28 08:15:08 | DEBUG | 收到消息: {'MsgId': 2031445580, 'FromUserName': {'string': '48097389945@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 49, 'Content': {'string': 'wxid_e3o8s2nf9u2o22:\n<?xml version="1.0"?>\n<msg>\n\t<appmsg appid="" sdkver="0">\n\t\t<title>纯欲壁纸\xa0|\xa0腿控\xa0和颜悦色</title>\n\t\t<des />\n\t\t<username />\n\t\t<action>view</action>\n\t\t<type>5</type>\n\t\t<showtype>0</showtype>\n\t\t<content />\n\t\t<url>https://mp.weixin.qq.com/s?__biz=Mzk1NzE3NDYzNA==&amp;mid=2247489294&amp;idx=1&amp;sn=b9b901424c9684b9110bda2246eb00cf&amp;chksm=c24b90968da01220ec1dfb543f3c53b497159d6e95c8a6ef560a203c63935072746fc5ebddf6&amp;mpshare=1&amp;scene=1&amp;srcid=0728pLOEJDIEuwmpMABmxPkw&amp;sharer_shareinfo=6f0476aaabc4d66909ad58a028460b87&amp;sharer_shareinfo_first=6f0476aaabc4d66909ad58a028460b87#rd</url>\n\t\t<lowurl />\n\t\t<forwardflag>0</forwardflag>\n\t\t<dataurl />\n\t\t<lowdataurl />\n\t\t<contentattr>0</contentattr>\n\t\t<streamvideo>\n\t\t\t<streamvideourl />\n\t\t\t<streamvideototaltime>0</streamvideototaltime>\n\t\t\t<streamvideotitle />\n\t\t\t<streamvideowording />\n\t\t\t<streamvideoweburl />\n\t\t\t<streamvideothumburl />\n\t\t\t<streamvideoaduxinfo />\n\t\t\t<streamvideopublishid />\n\t\t</streamvideo>\n\t\t<canvasPageItem>\n\t\t\t<canvasPageXml><![CDATA[]]></canvasPageXml>\n\t\t</canvasPageItem>\n\t\t<appattach>\n\t\t\t<attachid />\n\t\t\t<cdnthumburl>3057020100044b3049020100020435b9d70b02032f50e60204214e8b7b02046886c116042439356539356130342d323639332d343665382d616361322d3666326361613734633933610204051408030201000405004c50ba00</cdnthumburl>\n\t\t\t<cdnthumbmd5>fe26640fc6f902dc0af8548f4132974a</cdnthumbmd5>\n\t\t\t<cdnthumblength>28461</cdnthumblength>\n\t\t\t<cdnthumbheight>120</cdnthumbheight>\n\t\t\t<cdnthumbwidth>120</cdnthumbwidth>\n\t\t\t<cdnthumbaeskey>2218b830b05ac9ff6eca16ccc75fb85b</cdnthumbaeskey>\n\t\t\t<aeskey>2218b830b05ac9ff6eca16ccc75fb85b</aeskey>\n\t\t\t<encryver>1</encryver>\n\t\t\t<fileext />\n\t\t\t<islargefilemsg>0</islargefilemsg>\n\t\t</appattach>\n\t\t<extinfo />\n\t\t<androidsource>2</androidsource>\n\t\t<sourceusername>gh_fe8a58118cc4</sourceusername>\n\t\t<sourcedisplayname>拾七图馆</sourcedisplayname>\n\t\t<commenturl />\n\t\t<thumburl />\n\t\t<mediatagname />\n\t\t<messageaction><![CDATA[]]></messageaction>\n\t\t<messageext><![CDATA[]]></messageext>\n\t\t<emoticongift>\n\t\t\t<packageflag>0</packageflag>\n\t\t\t<packageid />\n\t\t</emoticongift>\n\t\t<emoticonshared>\n\t\t\t<packageflag>0</packageflag>\n\t\t\t<packageid />\n\t\t</emoticonshared>\n\t\t<designershared>\n\t\t\t<designeruin>0</designeruin>\n\t\t\t<designername>null</designername>\n\t\t\t<designerrediretcturl><![CDATA[null]]></designerrediretcturl>\n\t\t</designershared>\n\t\t<emotionpageshared>\n\t\t\t<tid>0</tid>\n\t\t\t<title>null</title>\n\t\t\t<desc>null</desc>\n\t\t\t<iconUrl><![CDATA[null]]></iconUrl>\n\t\t\t<secondUrl>null</secondUrl>\n\t\t\t<pageType>0</pageType>\n\t\t\t<setKey>null</setKey>\n\t\t</emotionpageshared>\n\t\t<webviewshared>\n\t\t\t<shareUrlOriginal>http://mp.weixin.qq.com/s?__biz=Mzk1NzE3NDYzNA==&amp;mid=2247489294&amp;idx=1&amp;sn=b9b901424c9684b9110bda2246eb00cf&amp;chksm=c29d177faa1928beb52ff6d102c115a21129b084f8dc89560367bd0cab8fa731ec15e1c05136&amp;xtrack=1&amp;scene=90&amp;subscene=93&amp;sessionid=1753661638&amp;flutter_pos=1&amp;clicktime=1753661681&amp;enterid=1753661681&amp;finder_biz_enter_id=4&amp;ranksessionid=1753661178&amp;jumppath=HalfScreenTransparentActivity_1753661670192%2CGestureGalleryUI_1753661671060%2C20020_1753661672518%2C50094_1753661680924&amp;jumppathdepth=4&amp;ascene=56&amp;fasttmpl_type=0&amp;fasttmpl_fullversion=7835612-zh_CN-zip&amp;fasttmpl_flag=0&amp;realreporttime=1753661681868#rd</shareUrlOriginal>\n\t\t\t<shareUrlOpen>https://mp.weixin.qq.com/s?__biz=Mzk1NzE3NDYzNA==&amp;mid=2247489294&amp;idx=1&amp;sn=b9b901424c9684b9110bda2246eb00cf&amp;chksm=c29d177faa1928beb52ff6d102c115a21129b084f8dc89560367bd0cab8fa731ec15e1c05136&amp;xtrack=1&amp;scene=90&amp;subscene=93&amp;sessionid=1753661638&amp;flutter_pos=1&amp;clicktime=1753661681&amp;enterid=1753661681&amp;finder_biz_enter_id=4&amp;ranksessionid=1753661178&amp;jumppath=HalfScreenTransparentActivity_1753661670192%2CGestureGalleryUI_1753661671060%2C20020_1753661672518%2C50094_1753661680924&amp;jumppathdepth=4&amp;ascene=56&amp;fasttmpl_type=0&amp;fasttmpl_fullversion=7835612-zh_CN-zip&amp;fasttmpl_flag=0&amp;realreporttime=1753661681868&amp;devicetype=android-35&amp;version=28003956&amp;nettype=3gnet&amp;lang=zh_CN&amp;session_us=gh_fe8a58118cc4&amp;countrycode=CN&amp;exportkey=n_ChQIAhIQ0fm%2BxyXQlbUghU0FrNOkuRLxAQIE97dBBAEAAAAAAJ4MDs03fqQAAAAOpnltbLcz9gKNyK89dVj0bvT%2BWU8Fif0OQtjXR%2BsD3sAk%2BE0Xb6t%2BuzSrDdf%2BtbGFwK7GWNAWGHkjtRkFG%2FDkp1mszFy0Rcoe4WJGYK1w9B5ypKQpxnnVrifkWB9DhAqbtqRNB9JaJwtqii%2Bo6l4ehzPBjLsMavD5q9VmczVg3qZWLrLUzBU9gaODaTpJq8p9ZXxXcVJB1AvpCC4h6%2FIiFzbUXtRptqAi6T489hr4b78n7n02l4U0WxhiRS%2B4xViICJO8415iZCJA6k5qpvSwo%2FX2XBpsQeRheu8%3D&amp;pass_ticket=R2oB5%2B%2FumTC8ewI%2B62o8QP5ax1cc7IvhiWRLPGTPMasIaeLlERTOis670w%2BuWmiF&amp;wx_header=3</shareUrlOpen>\n\t\t\t<jsAppId />\n\t\t\t<publisherId>msg_563096</publisherId>\n\t\t\t<publisherReqId>1315005428</publisherReqId>\n\t\t</webviewshared>\n\t\t<template_id />\n\t\t<md5>fe26640fc6f902dc0af8548f4132974a</md5>\n\t\t<websearch>\n\t\t\t<rec_category>0</rec_category>\n\t\t\t<channelId>0</channelId>\n\t\t</websearch>\n\t\t<weappinfo>\n\t\t\t<username />\n\t\t\t<appid />\n\t\t\t<appservicetype>0</appservicetype>\n\t\t\t<secflagforsinglepagemode>0</secflagforsinglepagemode>\n\t\t\t<videopageinfo>\n\t\t\t\t<thumbwidth>120</thumbwidth>\n\t\t\t\t<thumbheight>120</thumbheight>\n\t\t\t\t<fromopensdk>0</fromopensdk>\n\t\t\t</videopageinfo>\n\t\t</weappinfo>\n\t\t<statextstr />\n\t\t<mmreadershare>\n\t\t\t<itemshowtype>0</itemshowtype>\n\t\t\t<ispaysubscribe>0</ispaysubscribe>\n\t\t</mmreadershare>\n\t\t<musicShareItem>\n\t\t\t<musicDuration>0</musicDuration>\n\t\t</musicShareItem>\n\t\t<finderLiveProductShare>\n\t\t\t<finderLiveID><![CDATA[]]></finderLiveID>\n\t\t\t<finderUsername><![CDATA[]]></finderUsername>\n\t\t\t<finderObjectID><![CDATA[]]></finderObjectID>\n\t\t\t<finderNonceID><![CDATA[]]></finderNonceID>\n\t\t\t<liveStatus><![CDATA[]]></liveStatus>\n\t\t\t<appId><![CDATA[]]></appId>\n\t\t\t<pagePath><![CDATA[]]></pagePath>\n\t\t\t<productId><![CDATA[]]></productId>\n\t\t\t<coverUrl><![CDATA[]]></coverUrl>\n\t\t\t<productTitle><![CDATA[]]></productTitle>\n\t\t\t<marketPrice><![CDATA[0]]></marketPrice>\n\t\t\t<sellingPrice><![CDATA[0]]></sellingPrice>\n\t\t\t<platformHeadImg><![CDATA[]]></platformHeadImg>\n\t\t\t<platformName><![CDATA[]]></platformName>\n\t\t\t<shopWindowId><![CDATA[]]></shopWindowId>\n\t\t\t<flashSalePrice><![CDATA[0]]></flashSalePrice>\n\t\t\t<flashSaleEndTime><![CDATA[0]]></flashSaleEndTime>\n\t\t\t<ecSource><![CDATA[]]></ecSource>\n\t\t\t<sellingPriceWording><![CDATA[]]></sellingPriceWording>\n\t\t\t<platformIconURL><![CDATA[]]></platformIconURL>\n\t\t\t<firstProductTagURL><![CDATA[]]></firstProductTagURL>\n\t\t\t<firstProductTagAspectRatioString><![CDATA[0.0]]></firstProductTagAspectRatioString>\n\t\t\t<secondProductTagURL><![CDATA[]]></secondProductTagURL>\n\t\t\t<secondProductTagAspectRatioString><![CDATA[0.0]]></secondProductTagAspectRatioString>\n\t\t\t<firstGuaranteeWording><![CDATA[]]></firstGuaranteeWording>\n\t\t\t<secondGuaranteeWording><![CDATA[]]></secondGuaranteeWording>\n\t\t\t<thirdGuaranteeWording><![CDATA[]]></thirdGuaranteeWording>\n\t\t\t<isPriceBeginShow>false</isPriceBeginShow>\n\t\t\t<lastGMsgID><![CDATA[]]></lastGMsgID>\n\t\t\t<promoterKey><![CDATA[]]></promoterKey>\n\t\t\t<discountWording><![CDATA[]]></discountWording>\n\t\t\t<priceSuffixDescription><![CDATA[]]></priceSuffixDescription>\n\t\t\t<productCardKey><![CDATA[]]></productCardKey>\n\t\t\t<isWxShop><![CDATA[]]></isWxShop>\n\t\t\t<brandIconUrl><![CDATA[]]></brandIconUrl>\n\t\t\t<showBoxItemStringList />\n\t\t</finderLiveProductShare>\n\t\t<finderOrder>\n\t\t\t<appID><![CDATA[]]></appID>\n\t\t\t<orderID><![CDATA[]]></orderID>\n\t\t\t<path><![CDATA[]]></path>\n\t\t\t<priceWording><![CDATA[]]></priceWording>\n\t\t\t<stateWording><![CDATA[]]></stateWording>\n\t\t\t<productImageURL><![CDATA[]]></productImageURL>\n\t\t\t<products><![CDATA[]]></products>\n\t\t\t<productsCount><![CDATA[0]]></productsCount>\n\t\t\t<orderType><![CDATA[0]]></orderType>\n\t\t\t<newPriceWording><![CDATA[]]></newPriceWording>\n\t\t\t<newStateWording><![CDATA[]]></newStateWording>\n\t\t\t<useNewWording><![CDATA[0]]></useNewWording>\n\t\t</finderOrder>\n\t\t<finderShopWindowShare>\n\t\t\t<finderUsername><![CDATA[]]></finderUsername>\n\t\t\t<avatar><![CDATA[]]></avatar>\n\t\t\t<nickname><![CDATA[]]></nickname>\n\t\t\t<commodityInStockCount><![CDATA[]]></commodityInStockCount>\n\t\t\t<appId><![CDATA[]]></appId>\n\t\t\t<path><![CDATA[]]></path>\n\t\t\t<appUsername><![CDATA[]]></appUsername>\n\t\t\t<query><![CDATA[]]></query>\n\t\t\t<liteAppId><![CDATA[]]></liteAppId>\n\t\t\t<liteAppPath><![CDATA[]]></liteAppPath>\n\t\t\t<liteAppQuery><![CDATA[]]></liteAppQuery>\n\t\t\t<platformTagURL><![CDATA[]]></platformTagURL>\n\t\t\t<saleWording><![CDATA[]]></saleWording>\n\t\t\t<lastGMsgID><![CDATA[]]></lastGMsgID>\n\t\t\t<profileTypeWording><![CDATA[]]></profileTypeWording>\n\t\t\t<saleWordingExtra><![CDATA[]]></saleWordingExtra>\n\t\t\t<isWxShop><![CDATA[]]></isWxShop>\n\t\t\t<platformIconUrl><![CDATA[]]></platformIconUrl>\n\t\t\t<brandIconUrl><![CDATA[]]></brandIconUrl>\n\t\t\t<description><![CDATA[]]></description>\n\t\t\t<backgroundUrl><![CDATA[]]></backgroundUrl>\n\t\t\t<darkModePlatformIconUrl><![CDATA[]]></darkModePlatformIconUrl>\n\t\t\t<reputationInfo>\n\t\t\t\t<hasReputationInfo>0</hasReputationInfo>\n\t\t\t\t<reputationScore>0</reputationScore>\n\t\t\t\t<reputationWording />\n\t\t\t\t<reputationTextColor />\n\t\t\t\t<reputationLevelWording />\n\t\t\t\t<reputationBackgroundColor />\n\t\t\t</reputationInfo>\n\t\t\t<productImageURLList />\n\t\t</finderShopWindowShare>\n\t\t<findernamecard>\n\t\t\t<username />\n\t\t\t<avatar><![CDATA[]]></avatar>\n\t\t\t<nickname />\n\t\t\t<auth_job />\n\t\t\t<auth_icon>0</auth_icon>\n\t\t\t<auth_icon_url />\n\t\t\t<ecSource><![CDATA[]]></ecSource>\n\t\t\t<lastGMsgID><![CDATA[]]></lastGMsgID>\n\t\t</findernamecard>\n\t\t<finderGuarantee>\n\t\t\t<scene><![CDATA[0]]></scene>\n\t\t</finderGuarantee>\n\t\t<directshare>0</directshare>\n\t\t<gamecenter>\n\t\t\t<namecard>\n\t\t\t\t<iconUrl />\n\t\t\t\t<name />\n\t\t\t\t<desc />\n\t\t\t\t<tail />\n\t\t\t\t<jumpUrl />\n\t\t\t</namecard>\n\t\t</gamecenter>\n\t\t<patMsg>\n\t\t\t<chatUser />\n\t\t\t<records>\n\t\t\t\t<recordNum>0</recordNum>\n\t\t\t</records>\n\t\t</patMsg>\n\t\t<secretmsg>\n\t\t\t<issecretmsg>0</issecretmsg>\n\t\t</secretmsg>\n\t\t<referfromscene>0</referfromscene>\n\t\t<gameshare>\n\t\t\t<liteappext>\n\t\t\t\t<liteappbizdata />\n\t\t\t\t<priority>0</priority>\n\t\t\t</liteappext>\n\t\t\t<appbrandext>\n\t\t\t\t<litegameinfo />\n\t\t\t\t<priority>-1</priority>\n\t\t\t</appbrandext>\n\t\t\t<gameshareid />\n\t\t\t<sharedata />\n\t\t\t<isvideo>0</isvideo>\n\t\t\t<duration>-1</duration>\n\t\t\t<isexposed>0</isexposed>\n\t\t\t<readtext />\n\t\t</gameshare>\n\t\t<mpsharetrace>\n\t\t\t<hasfinderelement>0</hasfinderelement>\n\t\t\t<lastgmsgid />\n\t\t</mpsharetrace>\n\t\t<wxgamecard>\n\t\t\t<framesetname />\n\t\t\t<mbcarddata />\n\t\t\t<minpkgversion />\n\t\t\t<clientextinfo />\n\t\t\t<mbcardheight>0</mbcardheight>\n\t\t\t<isoldversion>0</isoldversion>\n\t\t</wxgamecard>\n\t\t<liteapp>\n\t\t\t<id>null</id>\n\t\t\t<path />\n\t\t\t<query />\n\t\t\t<istransparent>0</istransparent>\n\t\t\t<hideicon>0</hideicon>\n\t\t</liteapp>\n\t\t<opensdk_share_is_modified>0</opensdk_share_is_modified>\n\t</appmsg>\n\t<fromusername>wxid_e3o8s2nf9u2o22</fromusername>\n\t<scene>0</scene>\n\t<appinfo>\n\t\t<version>1</version>\n\t\t<appname />\n\t</appinfo>\n\t<commenturl />\n</msg>\n'}, 'Status': 3, 'ImgStatus': 2, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1753661718, 'MsgSource': '<msgsource>\n\t<sec_msg_node>\n\t\t<uuid>9f634279928ad52119858bf852d82787_</uuid>\n\t\t<risk-file-flag />\n\t\t<risk-file-md5-list />\n\t</sec_msg_node>\n\t<silence>0</silence>\n\t<membercount>73</membercount>\n\t<signature>N0_V1_eEaPwGev|v1_twudl5/4</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'PushContent': '星空- 梦 : [链接]纯欲壁纸\xa0|\xa0腿控\xa0和颜悦色', 'NewMsgId': 6891306426850550866, 'MsgSeq': 871404506}
2025-07-28 08:15:08 | DEBUG | 从群聊消息中提取发送者: wxid_e3o8s2nf9u2o22
2025-07-28 08:15:08 | INFO | 收到公众号文章消息: 消息ID:2031445580 来自:48097389945@chatroom
2025-07-28 08:15:08 | ERROR | 解析XML失败: mismatched tag: line 1, column 384
2025-07-28 08:15:08 | DEBUG | [ArticleForwarder] 检测到XML开头被截断，尝试修复
2025-07-28 08:15:08 | DEBUG | [ArticleForwarder] 已修复XML开头截断问题
2025-07-28 08:15:08 | DEBUG | 尝试使用修复后的XML重新解析
2025-07-28 08:15:08 | DEBUG | 从sourcedisplayname提取到公众号: 拾七图馆
2025-07-28 08:15:08 | DEBUG | 公众号「拾七图馆」不在监控列表中，跳过处理
2025-07-28 08:15:16 | DEBUG | 收到消息: {'MsgId': 2012777039, 'FromUserName': {'string': '48097389945@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 47, 'Content': {'string': 'xiaomaochong:\n<msg><emoji fromusername="xiaomaochong" tousername="48097389945@chatroom" type="2" idbuffer="media:0_0" md5="d59b91247a40ccf864db95dafb17ce14" len="3563447" productid="" androidmd5="d59b91247a40ccf864db95dafb17ce14" androidlen="3563447" s60v3md5="d59b91247a40ccf864db95dafb17ce14" s60v3len="3563447" s60v5md5="d59b91247a40ccf864db95dafb17ce14" s60v5len="3563447" cdnurl="http://vweixinf.tc.qq.com/110/20402/stodownload?m=d59b91247a40ccf864db95dafb17ce14&amp;filekey=30440201010430302e02016e0402535a042064353962393132343761343063636638363464623935646166623137636531340203365fb7040d00000004627466730000000132&amp;hy=SZ&amp;storeid=2680294de000a132bcdcbadb00000006e01004fb2535a24469bc1e763314c6&amp;ef=1&amp;bizid=1022" designerid="" thumburl="" encrypturl="http://vweixinf.tc.qq.com/110/20402/stodownload?m=4420e6a7ee3c80b23f76a37fd9ee7182&amp;filekey=30440201010430302e02016e0402535a042034343230653661376565336338306232336637366133376664396565373138320203365fc0040d00000004627466730000000132&amp;hy=SZ&amp;storeid=2680294de000dcfe4cdcbadb00000006e02004fb2535a24469bc1e763314e1&amp;ef=2&amp;bizid=1022" aeskey="77fca316fc9647ceb56c2591ef7989c5" externurl="http://vweixinf.tc.qq.com/110/20403/stodownload?m=a97d5283b88cd0380387252d3b26ae75&amp;filekey=30440201010430302e02016e0402535a04206139376435323833623838636430333830333837323532643362323661653735020302ff70040d00000004627466730000000132&amp;hy=SZ&amp;storeid=2680294df000241cfcdcbadb00000006e03004fb3535a24469bc1e76331503&amp;ef=3&amp;bizid=1022" externmd5="6b601c880138b6501626e3323ee1a3b3" width="299" height="300" tpurl="" tpauthkey="" attachedtext="" attachedtextcolor="" lensid="" emojiattr="" linkid="" desc=""></emoji></msg>'}, 'Status': 3, 'ImgStatus': 2, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1753661726, 'MsgSource': '<msgsource>\n\t<silence>0</silence>\n\t<membercount>73</membercount>\n\t<signature>N0_V1_T5E3I8eO|v1_ibpWYQm3</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'PushContent': '小爱在群聊中发了一个表情', 'NewMsgId': 4617046981694113108, 'MsgSeq': 871404507}
2025-07-28 08:15:16 | INFO | 收到表情消息: 消息ID:2012777039 来自:48097389945@chatroom 发送人:xiaomaochong MD5:d59b91247a40ccf864db95dafb17ce14 大小:3563447
2025-07-28 08:15:17 | DEBUG | [DoubaoImageRecognition] 缓存表情消息: 4617046981694113108
2025-07-28 08:16:28 | DEBUG | 收到消息: {'MsgId': 1211753867, 'FromUserName': {'string': '48097389945@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 49, 'Content': {'string': 'wxid_8h3f8g8eam4d22:\n<?xml version="1.0"?>\n<msg>\n\t<appmsg appid="" sdkver="0">\n\t\t<title>车牌是啥</title>\n\t\t<des />\n\t\t<username />\n\t\t<action>view</action>\n\t\t<type>57</type>\n\t\t<showtype>0</showtype>\n\t\t<content />\n\t\t<url />\n\t\t<lowurl />\n\t\t<forwardflag>0</forwardflag>\n\t\t<dataurl />\n\t\t<lowdataurl />\n\t\t<contentattr>0</contentattr>\n\t\t<streamvideo>\n\t\t\t<streamvideourl />\n\t\t\t<streamvideototaltime>0</streamvideototaltime>\n\t\t\t<streamvideotitle />\n\t\t\t<streamvideowording />\n\t\t\t<streamvideoweburl />\n\t\t\t<streamvideothumburl />\n\t\t\t<streamvideoaduxinfo />\n\t\t\t<streamvideopublishid />\n\t\t</streamvideo>\n\t\t<canvasPageItem>\n\t\t\t<canvasPageXml><![CDATA[]]></canvasPageXml>\n\t\t</canvasPageItem>\n\t\t<refermsg>\n\t\t\t<type>43</type>\n\t\t\t<svrid>452063707557268267</svrid>\n\t\t\t<fromusr>48097389945@chatroom</fromusr>\n\t\t\t<chatusr>zuoledd</chatusr>\n\t\t\t<displayname>作乐多端</displayname>\n\t\t\t<msgsource>&lt;msgsource&gt;\n\t&lt;bizflag&gt;0&lt;/bizflag&gt;\n\t&lt;tmp_node&gt;\n\t\t&lt;publisher-id&gt;&lt;/publisher-id&gt;\n\t&lt;/tmp_node&gt;\n\t&lt;alnode&gt;\n\t\t&lt;fr&gt;5&lt;/fr&gt;\n\t&lt;/alnode&gt;\n\t&lt;weappsourceUsername&gt;(null),wxid_5p9omyb8krpj19,wxid_3c4a1rbvs91321,zuoledd&lt;/weappsourceUsername&gt;\n\t&lt;sec_msg_node&gt;\n\t\t&lt;uuid&gt;a12ba8d668d77d581278a45d22e4fad3_&lt;/uuid&gt;\n\t\t&lt;risk-file-flag /&gt;\n\t\t&lt;risk-file-md5-list /&gt;\n\t&lt;/sec_msg_node&gt;\n\t&lt;silence&gt;1&lt;/silence&gt;\n\t&lt;membercount&gt;73&lt;/membercount&gt;\n\t&lt;signature&gt;N0_V1_rNUCBo9q|v1_Yk/2jTcq&lt;/signature&gt;\n&lt;/msgsource&gt;\n</msgsource>\n\t\t\t<content>37:0\n</content>\n\t\t\t<strid />\n\t\t\t<createtime>1753624353</createtime>\n\t\t</refermsg>\n\t\t<appattach>\n\t\t\t<totallen>0</totallen>\n\t\t\t<attachid />\n\t\t\t<cdnattachurl />\n\t\t\t<emoticonmd5 />\n\t\t\t<aeskey />\n\t\t\t<fileext />\n\t\t\t<islargefilemsg>0</islargefilemsg>\n\t\t</appattach>\n\t\t<extinfo />\n\t\t<androidsource>0</androidsource>\n\t\t<thumburl />\n\t\t<mediatagname />\n\t\t<messageaction><![CDATA[]]></messageaction>\n\t\t<messageext><![CDATA[]]></messageext>\n\t\t<emoticongift>\n\t\t\t<packageflag>0</packageflag>\n\t\t\t<packageid />\n\t\t</emoticongift>\n\t\t<emoticonshared>\n\t\t\t<packageflag>0</packageflag>\n\t\t\t<packageid />\n\t\t</emoticonshared>\n\t\t<designershared>\n\t\t\t<designeruin>0</designeruin>\n\t\t\t<designername>null</designername>\n\t\t\t<designerrediretcturl><![CDATA[null]]></designerrediretcturl>\n\t\t</designershared>\n\t\t<emotionpageshared>\n\t\t\t<tid>0</tid>\n\t\t\t<title>null</title>\n\t\t\t<desc>null</desc>\n\t\t\t<iconUrl><![CDATA[null]]></iconUrl>\n\t\t\t<secondUrl>null</secondUrl>\n\t\t\t<pageType>0</pageType>\n\t\t\t<setKey>null</setKey>\n\t\t</emotionpageshared>\n\t\t<webviewshared>\n\t\t\t<shareUrlOriginal />\n\t\t\t<shareUrlOpen />\n\t\t\t<jsAppId />\n\t\t\t<publisherId />\n\t\t\t<publisherReqId />\n\t\t</webviewshared>\n\t\t<template_id />\n\t\t<md5 />\n\t\t<websearch>\n\t\t\t<rec_category>0</rec_category>\n\t\t\t<channelId>0</channelId>\n\t\t</websearch>\n\t\t<weappinfo>\n\t\t\t<username />\n\t\t\t<appid />\n\t\t\t<appservicetype>0</appservicetype>\n\t\t\t<secflagforsinglepagemode>0</secflagforsinglepagemode>\n\t\t\t<videopageinfo>\n\t\t\t\t<thumbwidth>0</thumbwidth>\n\t\t\t\t<thumbheight>0</thumbheight>\n\t\t\t\t<fromopensdk>0</fromopensdk>\n\t\t\t</videopageinfo>\n\t\t</weappinfo>\n\t\t<statextstr />\n\t\t<musicShareItem>\n\t\t\t<musicDuration>0</musicDuration>\n\t\t</musicShareItem>\n\t\t<finderLiveProductShare>\n\t\t\t<finderLiveID><![CDATA[]]></finderLiveID>\n\t\t\t<finderUsername><![CDATA[]]></finderUsername>\n\t\t\t<finderObjectID><![CDATA[]]></finderObjectID>\n\t\t\t<finderNonceID><![CDATA[]]></finderNonceID>\n\t\t\t<liveStatus><![CDATA[]]></liveStatus>\n\t\t\t<appId><![CDATA[]]></appId>\n\t\t\t<pagePath><![CDATA[]]></pagePath>\n\t\t\t<productId><![CDATA[]]></productId>\n\t\t\t<coverUrl><![CDATA[]]></coverUrl>\n\t\t\t<productTitle><![CDATA[]]></productTitle>\n\t\t\t<marketPrice><![CDATA[0]]></marketPrice>\n\t\t\t<sellingPrice><![CDATA[0]]></sellingPrice>\n\t\t\t<platformHeadImg><![CDATA[]]></platformHeadImg>\n\t\t\t<platformName><![CDATA[]]></platformName>\n\t\t\t<shopWindowId><![CDATA[]]></shopWindowId>\n\t\t\t<flashSalePrice><![CDATA[0]]></flashSalePrice>\n\t\t\t<flashSaleEndTime><![CDATA[0]]></flashSaleEndTime>\n\t\t\t<ecSource><![CDATA[]]></ecSource>\n\t\t\t<sellingPriceWording><![CDATA[]]></sellingPriceWording>\n\t\t\t<platformIconURL><![CDATA[]]></platformIconURL>\n\t\t\t<firstProductTagURL><![CDATA[]]></firstProductTagURL>\n\t\t\t<firstProductTagAspectRatioString><![CDATA[0.0]]></firstProductTagAspectRatioString>\n\t\t\t<secondProductTagURL><![CDATA[]]></secondProductTagURL>\n\t\t\t<secondProductTagAspectRatioString><![CDATA[0.0]]></secondProductTagAspectRatioString>\n\t\t\t<firstGuaranteeWording><![CDATA[]]></firstGuaranteeWording>\n\t\t\t<secondGuaranteeWording><![CDATA[]]></secondGuaranteeWording>\n\t\t\t<thirdGuaranteeWording><![CDATA[]]></thirdGuaranteeWording>\n\t\t\t<isPriceBeginShow>false</isPriceBeginShow>\n\t\t\t<lastGMsgID><![CDATA[]]></lastGMsgID>\n\t\t\t<promoterKey><![CDATA[]]></promoterKey>\n\t\t\t<discountWording><![CDATA[]]></discountWording>\n\t\t\t<priceSuffixDescription><![CDATA[]]></priceSuffixDescription>\n\t\t\t<productCardKey><![CDATA[]]></productCardKey>\n\t\t\t<isWxShop><![CDATA[]]></isWxShop>\n\t\t\t<brandIconUrl><![CDATA[]]></brandIconUrl>\n\t\t\t<showBoxItemStringList />\n\t\t</finderLiveProductShare>\n\t\t<finderOrder>\n\t\t\t<appID><![CDATA[]]></appID>\n\t\t\t<orderID><![CDATA[]]></orderID>\n\t\t\t<path><![CDATA[]]></path>\n\t\t\t<priceWording><![CDATA[]]></priceWording>\n\t\t\t<stateWording><![CDATA[]]></stateWording>\n\t\t\t<productImageURL><![CDATA[]]></productImageURL>\n\t\t\t<products><![CDATA[]]></products>\n\t\t\t<productsCount><![CDATA[0]]></productsCount>\n\t\t\t<orderType><![CDATA[0]]></orderType>\n\t\t\t<newPriceWording><![CDATA[]]></newPriceWording>\n\t\t\t<newStateWording><![CDATA[]]></newStateWording>\n\t\t\t<useNewWording><![CDATA[0]]></useNewWording>\n\t\t</finderOrder>\n\t\t<finderShopWindowShare>\n\t\t\t<finderUsername><![CDATA[]]></finderUsername>\n\t\t\t<avatar><![CDATA[]]></avatar>\n\t\t\t<nickname><![CDATA[]]></nickname>\n\t\t\t<commodityInStockCount><![CDATA[]]></commodityInStockCount>\n\t\t\t<appId><![CDATA[]]></appId>\n\t\t\t<path><![CDATA[]]></path>\n\t\t\t<appUsername><![CDATA[]]></appUsername>\n\t\t\t<query><![CDATA[]]></query>\n\t\t\t<liteAppId><![CDATA[]]></liteAppId>\n\t\t\t<liteAppPath><![CDATA[]]></liteAppPath>\n\t\t\t<liteAppQuery><![CDATA[]]></liteAppQuery>\n\t\t\t<platformTagURL><![CDATA[]]></platformTagURL>\n\t\t\t<saleWording><![CDATA[]]></saleWording>\n\t\t\t<lastGMsgID><![CDATA[]]></lastGMsgID>\n\t\t\t<profileTypeWording><![CDATA[]]></profileTypeWording>\n\t\t\t<saleWordingExtra><![CDATA[]]></saleWordingExtra>\n\t\t\t<isWxShop><![CDATA[]]></isWxShop>\n\t\t\t<platformIconUrl><![CDATA[]]></platformIconUrl>\n\t\t\t<brandIconUrl><![CDATA[]]></brandIconUrl>\n\t\t\t<description><![CDATA[]]></description>\n\t\t\t<backgroundUrl><![CDATA[]]></backgroundUrl>\n\t\t\t<darkModePlatformIconUrl><![CDATA[]]></darkModePlatformIconUrl>\n\t\t\t<reputationInfo>\n\t\t\t\t<hasReputationInfo>0</hasReputationInfo>\n\t\t\t\t<reputationScore>0</reputationScore>\n\t\t\t\t<reputationWording />\n\t\t\t\t<reputationTextColor />\n\t\t\t\t<reputationLevelWording />\n\t\t\t\t<reputationBackgroundColor />\n\t\t\t</reputationInfo>\n\t\t\t<productImageURLList />\n\t\t</finderShopWindowShare>\n\t\t<findernamecard>\n\t\t\t<username />\n\t\t\t<avatar><![CDATA[]]></avatar>\n\t\t\t<nickname />\n\t\t\t<auth_job />\n\t\t\t<auth_icon>0</auth_icon>\n\t\t\t<auth_icon_url />\n\t\t\t<ecSource><![CDATA[]]></ecSource>\n\t\t\t<lastGMsgID><![CDATA[]]></lastGMsgID>\n\t\t</findernamecard>\n\t\t<finderGuarantee>\n\t\t\t<scene><![CDATA[0]]></scene>\n\t\t</finderGuarantee>\n\t\t<directshare>0</directshare>\n\t\t<gamecenter>\n\t\t\t<namecard>\n\t\t\t\t<iconUrl />\n\t\t\t\t<name />\n\t\t\t\t<desc />\n\t\t\t\t<tail />\n\t\t\t\t<jumpUrl />\n\t\t\t</namecard>\n\t\t</gamecenter>\n\t\t<patMsg>\n\t\t\t<chatUser />\n\t\t\t<records>\n\t\t\t\t<recordNum>0</recordNum>\n\t\t\t</records>\n\t\t</patMsg>\n\t\t<secretmsg>\n\t\t\t<issecretmsg>0</issecretmsg>\n\t\t</secretmsg>\n\t\t<referfromscene>0</referfromscene>\n\t\t<gameshare>\n\t\t\t<liteappext>\n\t\t\t\t<liteappbizdata />\n\t\t\t\t<priority>0</priority>\n\t\t\t</liteappext>\n\t\t\t<appbrandext>\n\t\t\t\t<litegameinfo />\n\t\t\t\t<priority>-1</priority>\n\t\t\t</appbrandext>\n\t\t\t<gameshareid />\n\t\t\t<sharedata />\n\t\t\t<isvideo>0</isvideo>\n\t\t\t<duration>-1</duration>\n\t\t\t<isexposed>0</isexposed>\n\t\t\t<readtext />\n\t\t</gameshare>\n\t\t<tingChatRoomItem>\n\t\t\t<type>0</type>\n\t\t\t<categoryItem>null</categoryItem>\n\t\t\t<categoryId />\n\t\t</tingChatRoomItem>\n\t\t<mpsharetrace>\n\t\t\t<hasfinderelement>0</hasfinderelement>\n\t\t\t<lastgmsgid />\n\t\t</mpsharetrace>\n\t\t<wxgamecard>\n\t\t\t<framesetname />\n\t\t\t<mbcarddata />\n\t\t\t<minpkgversion />\n\t\t\t<clientextinfo />\n\t\t\t<mbcardheight>0</mbcardheight>\n\t\t\t<isoldversion>0</isoldversion>\n\t\t</wxgamecard>\n\t\t<ecskfcard>\n\t\t\t<framesetname />\n\t\t\t<mbcarddata />\n\t\t\t<minupdateunixtimestamp>0</minupdateunixtimestamp>\n\t\t\t<needheader>false</needheader>\n\t\t\t<summary />\n\t\t</ecskfcard>\n\t\t<liteapp>\n\t\t\t<id>null</id>\n\t\t\t<path />\n\t\t\t<query />\n\t\t\t<istransparent>0</istransparent>\n\t\t\t<hideicon>0</hideicon>\n\t\t</liteapp>\n\t\t<opensdk_share_is_modified>0</opensdk_share_is_modified>\n\t</appmsg>\n\t<fromusername>wxid_8h3f8g8eam4d22</fromusername>\n\t<scene>0</scene>\n\t<appinfo>\n\t\t<version>1</version>\n\t\t<appname></appname>\n\t</appinfo>\n\t<commenturl></commenturl>\n</msg>\n'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1753661797, 'MsgSource': '<msgsource>\n\t<alnode>\n\t\t<fr>4</fr>\n\t</alnode>\n\t<eggIncluded>1</eggIncluded>\n\t<sec_msg_node>\n\t\t<uuid>a12ba8d668d77d581278a45d22e4fad3_</uuid>\n\t\t<risk-file-flag />\n\t\t<risk-file-md5-list />\n\t</sec_msg_node>\n\t<silence>0</silence>\n\t<membercount>73</membercount>\n\t<signature>N0_V1_uz37FQ78|v1_LHKgfpOL</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'PushContent': '违忌（办流量卡找我） : 车牌是啥', 'NewMsgId': 5469449999570522007, 'MsgSeq': 871404508}
2025-07-28 08:16:28 | DEBUG | 从群聊消息中提取发送者: wxid_8h3f8g8eam4d22
2025-07-28 08:16:28 | DEBUG | 使用已解析的XML处理引用消息
2025-07-28 08:16:28 | INFO | 收到引用消息: 消息ID:1211753867 来自:48097389945@chatroom 发送人:wxid_8h3f8g8eam4d22 内容:车牌是啥 引用类型:43
2025-07-28 08:16:29 | INFO | [DouBaoImageToImage] 收到引用消息: 车牌是啥
2025-07-28 08:16:29 | INFO | [TimerTask] 收到引用消息调试信息:
2025-07-28 08:16:29 | INFO |   - 消息内容: 车牌是啥
2025-07-28 08:16:29 | INFO |   - 群组ID: 48097389945@chatroom
2025-07-28 08:16:29 | INFO |   - 发送人: wxid_8h3f8g8eam4d22
2025-07-28 08:16:29 | INFO |   - 引用信息: {'MsgType': 43, 'Content': '37:0\n', 'Msgid': '452063707557268267', 'NewMsgId': '452063707557268267', 'ToWxid': 'wxid_4usgcju5ey9q29', 'FromWxid': '48097389945@chatroom', 'Nickname': '作乐多端', 'MsgSource': '<msgsource>\n\t<bizflag>0</bizflag>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n\t<alnode>\n\t\t<fr>5</fr>\n\t</alnode>\n\t<weappsourceUsername>(null),wxid_5p9omyb8krpj19,wxid_3c4a1rbvs91321,zuoledd</weappsourceUsername>\n\t<sec_msg_node>\n\t\t<uuid>a12ba8d668d77d581278a45d22e4fad3_</uuid>\n\t\t<risk-file-flag />\n\t\t<risk-file-md5-list />\n\t</sec_msg_node>\n\t<silence>1</silence>\n\t<membercount>73</membercount>\n\t<signature>N0_V1_rNUCBo9q|v1_Yk/2jTcq</signature>\n</msgsource>\n', 'Createtime': '1753624353', 'SenderWxid': 'wxid_8h3f8g8eam4d22'}
2025-07-28 08:16:29 | INFO |   - 引用消息ID: 
2025-07-28 08:16:29 | INFO |   - 引用消息类型: 
2025-07-28 08:16:29 | INFO |   - 引用消息内容: 37:0

2025-07-28 08:16:29 | INFO |   - 引用消息发送人: wxid_8h3f8g8eam4d22
2025-07-28 08:17:49 | DEBUG | 收到消息: {'MsgId': 597215129, 'FromUserName': {'string': '48097389945@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 49, 'Content': {'string': 'wxid_wlnzvr8ivgd422:\n<?xml version="1.0"?>\n<msg>\n\t<appmsg appid="" sdkver="0">\n\t\t<title>当前版本不支持展示该内容，请升级至最新版本。</title>\n\t\t<des />\n\t\t<username />\n\t\t<action>view</action>\n\t\t<type>51</type>\n\t\t<showtype>0</showtype>\n\t\t<content />\n\t\t<url>https://support.weixin.qq.com/security/readtemplate?t=w_security_center_website/upgrade</url>\n\t\t<lowurl />\n\t\t<forwardflag>0</forwardflag>\n\t\t<dataurl />\n\t\t<lowdataurl />\n\t\t<contentattr>0</contentattr>\n\t\t<streamvideo>\n\t\t\t<streamvideourl />\n\t\t\t<streamvideototaltime>0</streamvideototaltime>\n\t\t\t<streamvideotitle />\n\t\t\t<streamvideowording />\n\t\t\t<streamvideoweburl />\n\t\t\t<streamvideothumburl />\n\t\t\t<streamvideoaduxinfo />\n\t\t\t<streamvideopublishid />\n\t\t</streamvideo>\n\t\t<canvasPageItem>\n\t\t\t<canvasPageXml><![CDATA[]]></canvasPageXml>\n\t\t</canvasPageItem>\n\t\t<appattach>\n\t\t\t<totallen>0</totallen>\n\t\t\t<attachid />\n\t\t\t<cdnattachurl />\n\t\t\t<emoticonmd5 />\n\t\t\t<aeskey />\n\t\t\t<fileext />\n\t\t\t<islargefilemsg>0</islargefilemsg>\n\t\t</appattach>\n\t\t<extinfo />\n\t\t<androidsource>0</androidsource>\n\t\t<thumburl />\n\t\t<mediatagname />\n\t\t<messageaction><![CDATA[]]></messageaction>\n\t\t<messageext><![CDATA[]]></messageext>\n\t\t<emoticongift>\n\t\t\t<packageflag>0</packageflag>\n\t\t\t<packageid />\n\t\t</emoticongift>\n\t\t<emoticonshared>\n\t\t\t<packageflag>0</packageflag>\n\t\t\t<packageid />\n\t\t</emoticonshared>\n\t\t<designershared>\n\t\t\t<designeruin>0</designeruin>\n\t\t\t<designername>null</designername>\n\t\t\t<designerrediretcturl><![CDATA[null]]></designerrediretcturl>\n\t\t</designershared>\n\t\t<emotionpageshared>\n\t\t\t<tid>0</tid>\n\t\t\t<title>null</title>\n\t\t\t<desc>null</desc>\n\t\t\t<iconUrl><![CDATA[null]]></iconUrl>\n\t\t\t<secondUrl>null</secondUrl>\n\t\t\t<pageType>0</pageType>\n\t\t\t<setKey>null</setKey>\n\t\t</emotionpageshared>\n\t\t<webviewshared>\n\t\t\t<shareUrlOriginal />\n\t\t\t<shareUrlOpen />\n\t\t\t<jsAppId />\n\t\t\t<publisherId />\n\t\t\t<publisherReqId />\n\t\t</webviewshared>\n\t\t<template_id />\n\t\t<md5 />\n\t\t<websearch>\n\t\t\t<rec_category>0</rec_category>\n\t\t\t<channelId>0</channelId>\n\t\t</websearch>\n\t\t<weappinfo>\n\t\t\t<username />\n\t\t\t<appid />\n\t\t\t<appservicetype>0</appservicetype>\n\t\t\t<secflagforsinglepagemode>0</secflagforsinglepagemode>\n\t\t\t<videopageinfo>\n\t\t\t\t<thumbwidth>0</thumbwidth>\n\t\t\t\t<thumbheight>0</thumbheight>\n\t\t\t\t<fromopensdk>0</fromopensdk>\n\t\t\t</videopageinfo>\n\t\t</weappinfo>\n\t\t<statextstr />\n\t\t<musicShareItem>\n\t\t\t<musicDuration>0</musicDuration>\n\t\t</musicShareItem>\n\t\t<finderFeed>\n\t\t\t<objectId>14710495124684408874</objectId>\n\t\t\t<objectNonceId>15318336085899807238_4_20_13_1_1753660912906647_1139d708-6b46-11f0-962a-2d97986cb727</objectNonceId>\n\t\t\t<feedType>4</feedType>\n\t\t\t<nickname>薪儿姐师尊</nickname>\n\t\t\t<username>v2_060000231003b20faec8c4e18d18c7d0cb02ec31b0779b934bbfbadef1607a5981f59a0d8e8a@finder</username>\n\t\t\t<avatar><![CDATA[https://wx.qlogo.cn/finderhead/ver_1/3qqlqR0iapeI6we8oheRXMpaX5oxCjQVhv8vD5J2K9ib55rBGBNOibZ9m28am1d3XPw6FE9RClmiaEic5Rye30iaSbv48lEVfjQhBbCvicWnth94LU/0]]></avatar>\n\t\t\t<desc>请再次挑选您的英雄</desc>\n\t\t\t<mediaCount>1</mediaCount>\n\t\t\t<localId>0</localId>\n\t\t\t<authIconType>1</authIconType>\n\t\t\t<authIconUrl><![CDATA[https://dldir1v6.qq.com/weixin/checkresupdate/auth_icon_level3_2e2f94615c1e4651a25a7e0446f63135.png]]></authIconUrl>\n\t\t\t<mediaList>\n\t\t\t\t<media>\n\t\t\t\t\t<mediaType>4</mediaType>\n\t\t\t\t\t<url><![CDATA[http://wxapp.tc.qq.com/251/20302/stodownload?encfilekey=Cvvj5Ix3eewK0tHtibORqcsqchXNh0Gf3sJcaYqC2rQA0MSQs75Y0GZcGrk1dChm3MiafGKh1pvbdBovWd9KGOLkrxcnn9VqHFrp5qqucpB1lH8vlsxveMEgG5tcJlsTqC&bizid=1023&dotrans=0&hy=SH&idx=1&m=&uzid=7a170&token=cztXnd9GyrGBLJXOnqKwzApv5Zq1tISSz30Le2tbMricQ7Tibib0uLibfbaTBaMuC1LxBibGgUc7toNia90kYfABHcbAUXn8aqNkOTFWz5tQ81wYfnp7oPMLPnbuYyI41hzuWw8rTVKDyUJIbJ5icOsAMpjLxIsz09HtsGQNdKyfO12Gok&basedata=CAESBnhXVDEyNhoGeFdUMTExGgZ4V1QxMDIaBnhXVDExMhoGeFdUMTEzGgZ4V1QxMjYaBnhXVDEyNxoGeFdUMTI4IgwKCgoGeFdUMTEyEAEqBwi9HhAAGAI&sign=PaOiQc-VLWER1nPoJKmGXF3PVkTh8UiqN4DoM9p_PGZjEkEDUvOneOyYRgrgP-bQNTZNPV2neqNl5-_UC3kGAA&ctsc=20&extg=108bd00&fck=100010&svrbypass=AAuL%2FQsFAAABAAAAAABIBoOFl4Zvk%2Byf872GaBAAAADnaHZTnGbFfAj9RgZXfw6V%2BsBW9I9VXX689ILcoyt7co4zxiZfyRF%2FdWU1O9aotLVVF4srSMo7h9s%3D&svrnonce=1753660915]]></url>\n\t\t\t\t\t<thumbUrl><![CDATA[http://wxapp.tc.qq.com/251/20304/stodownload?encfilekey=rjD5jyTuFrIpZ2ibE8T7YmwgiahniaXswqzqY0RudZNsFHQwWxDjicYibdnoicUL4ml0qVSO5X0LmlicHmVPG7jtNorMicaDKmml9JDmsXyAL4j9IYZgrY897Is0lw&hy=SH&idx=1&m=&scene=2&uzid=1&picformat=200&wxampicformat=503&token=Cvvj5Ix3eeyD0TVgRZ2eE5ETQ4jRJdybe5SwoliaWuYMDqcmaCfPYcibO9JN32NO4bmBvucy0vxYYfCfN7PRYa0o0QVbvlRAIDYZzDniamMGjy9CxCPDiaCXsuTnS9HgPanawJuldFFkPGhEBZv265ia4ibfy0xsjicUibnG&ctsc=2-20]]></thumbUrl>\n\t\t\t\t\t<coverUrl><![CDATA[http://wxapp.tc.qq.com/251/20304/stodownload?encfilekey=rjD5jyTuFrIpZ2ibE8T7YmwgiahniaXswqzqY0RudZNsFHQwWxDjicYibdnoicUL4ml0qVSO5X0LmlicHmVPG7jtNorMicaDKmml9JDmsXyAL4j9IYZgrY897Is0lw&hy=SH&idx=1&m=&scene=2&uzid=1&picformat=200&wxampicformat=503&token=Cvvj5Ix3eeyD0TVgRZ2eE5ETQ4jRJdybe5SwoliaWuYMDqcmaCfPYcibO9JN32NO4bmBvucy0vxYYfCfN7PRYa0o0QVbvlRAIDYZzDniamMGjy9CxCPDiaCXsuTnS9HgPanawJuldFFkPGhEBZv265ia4ibfy0xsjicUibnG&ctsc=2-20]]></coverUrl>\n\t\t\t\t\t<fullCoverUrl><![CDATA[]]></fullCoverUrl>\n\t\t\t\t\t<fullClipInset><![CDATA[]]></fullClipInset>\n\t\t\t\t\t<width>1080.0</width>\n\t\t\t\t\t<height>1920.0</height>\n\t\t\t\t\t<videoPlayDuration>261</videoPlayDuration>\n\t\t\t\t</media>\n\t\t\t</mediaList>\n\t\t\t<megaVideo>\n\t\t\t\t<objectId />\n\t\t\t\t<objectNonceId />\n\t\t\t</megaVideo>\n\t\t\t<bizUsername />\n\t\t\t<bizNickname />\n\t\t\t<bizAvatar><![CDATA[]]></bizAvatar>\n\t\t\t<bizUsernameV2 />\n\t\t\t<bizAuthIconType>0</bizAuthIconType>\n\t\t\t<bizAuthIconUrl><![CDATA[]]></bizAuthIconUrl>\n\t\t\t<coverEffectType>0</coverEffectType>\n\t\t\t<coverEffectText><![CDATA[]]></coverEffectText>\n\t\t\t<finderForwardSource><![CDATA[]]></finderForwardSource>\n\t\t\t<contactJumpInfoStr><![CDATA[]]></contactJumpInfoStr>\n\t\t\t<ecSource><![CDATA[]]></ecSource>\n\t\t\t<lastGMsgID><![CDATA[]]></lastGMsgID>\n\t\t\t<sourceCommentScene>20</sourceCommentScene>\n\t\t\t<finderShareExtInfo><![CDATA[{"hasInput":false,"tabContextId":"4-1753661463626","contextId":"1-1-20-24e7544531b14642ae28261eb0aa51a7","shareSrcScene":4}]]></finderShareExtInfo>\n\t\t</finderFeed>\n\t\t<finderLiveProductShare>\n\t\t\t<finderLiveID><![CDATA[]]></finderLiveID>\n\t\t\t<finderUsername><![CDATA[]]></finderUsername>\n\t\t\t<finderObjectID><![CDATA[]]></finderObjectID>\n\t\t\t<finderNonceID><![CDATA[]]></finderNonceID>\n\t\t\t<liveStatus><![CDATA[]]></liveStatus>\n\t\t\t<appId><![CDATA[]]></appId>\n\t\t\t<pagePath><![CDATA[]]></pagePath>\n\t\t\t<productId><![CDATA[]]></productId>\n\t\t\t<coverUrl><![CDATA[]]></coverUrl>\n\t\t\t<productTitle><![CDATA[]]></productTitle>\n\t\t\t<marketPrice><![CDATA[0]]></marketPrice>\n\t\t\t<sellingPrice><![CDATA[0]]></sellingPrice>\n\t\t\t<platformHeadImg><![CDATA[]]></platformHeadImg>\n\t\t\t<platformName><![CDATA[]]></platformName>\n\t\t\t<shopWindowId><![CDATA[]]></shopWindowId>\n\t\t\t<flashSalePrice><![CDATA[0]]></flashSalePrice>\n\t\t\t<flashSaleEndTime><![CDATA[0]]></flashSaleEndTime>\n\t\t\t<ecSource><![CDATA[]]></ecSource>\n\t\t\t<sellingPriceWording><![CDATA[]]></sellingPriceWording>\n\t\t\t<platformIconURL><![CDATA[]]></platformIconURL>\n\t\t\t<firstProductTagURL><![CDATA[]]></firstProductTagURL>\n\t\t\t<firstProductTagAspectRatioString><![CDATA[0.0]]></firstProductTagAspectRatioString>\n\t\t\t<secondProductTagURL><![CDATA[]]></secondProductTagURL>\n\t\t\t<secondProductTagAspectRatioString><![CDATA[0.0]]></secondProductTagAspectRatioString>\n\t\t\t<firstGuaranteeWording><![CDATA[]]></firstGuaranteeWording>\n\t\t\t<secondGuaranteeWording><![CDATA[]]></secondGuaranteeWording>\n\t\t\t<thirdGuaranteeWording><![CDATA[]]></thirdGuaranteeWording>\n\t\t\t<isPriceBeginShow>false</isPriceBeginShow>\n\t\t\t<lastGMsgID><![CDATA[]]></lastGMsgID>\n\t\t\t<promoterKey><![CDATA[]]></promoterKey>\n\t\t\t<discountWording><![CDATA[]]></discountWording>\n\t\t\t<priceSuffixDescription><![CDATA[]]></priceSuffixDescription>\n\t\t\t<productCardKey><![CDATA[]]></productCardKey>\n\t\t\t<isWxShop><![CDATA[]]></isWxShop>\n\t\t\t<brandIconUrl><![CDATA[]]></brandIconUrl>\n\t\t\t<rIconUrl><![CDATA[]]></rIconUrl>\n\t\t\t<rIconUrlDarkMode><![CDATA[]]></rIconUrlDarkMode>\n\t\t\t<topShopIconUrl><![CDATA[]]></topShopIconUrl>\n\t\t\t<topShopIconUrlDarkMode><![CDATA[]]></topShopIconUrlDarkMode>\n\t\t\t<simplifyTopShopIconUrl><![CDATA[]]></simplifyTopShopIconUrl>\n\t\t\t<simplifyTopShopIconUrlDarkmode><![CDATA[]]></simplifyTopShopIconUrlDarkmode>\n\t\t\t<topShopIconWidth><![CDATA[0]]></topShopIconWidth>\n\t\t\t<topShopIconHeight><![CDATA[0]]></topShopIconHeight>\n\t\t\t<simplifyTopShopIconWidth><![CDATA[0]]></simplifyTopShopIconWidth>\n\t\t\t<simplifyTopShopIconHeight><![CDATA[0]]></simplifyTopShopIconHeight>\n\t\t\t<showBoxItemStringList />\n\t\t</finderLiveProductShare>\n\t\t<finderOrder>\n\t\t\t<appID><![CDATA[]]></appID>\n\t\t\t<orderID><![CDATA[]]></orderID>\n\t\t\t<path><![CDATA[]]></path>\n\t\t\t<priceWording><![CDATA[]]></priceWording>\n\t\t\t<stateWording><![CDATA[]]></stateWording>\n\t\t\t<productImageURL><![CDATA[]]></productImageURL>\n\t\t\t<products><![CDATA[]]></products>\n\t\t\t<productsCount><![CDATA[0]]></productsCount>\n\t\t\t<orderType><![CDATA[0]]></orderType>\n\t\t\t<newPriceWording><![CDATA[]]></newPriceWording>\n\t\t\t<newStateWording><![CDATA[]]></newStateWording>\n\t\t\t<useNewWording><![CDATA[0]]></useNewWording>\n\t\t</finderOrder>\n\t\t<finderShopWindowShare>\n\t\t\t<finderUsername><![CDATA[]]></finderUsername>\n\t\t\t<avatar><![CDATA[]]></avatar>\n\t\t\t<nickname><![CDATA[]]></nickname>\n\t\t\t<commodityInStockCount><![CDATA[]]></commodityInStockCount>\n\t\t\t<appId><![CDATA[]]></appId>\n\t\t\t<path><![CDATA[]]></path>\n\t\t\t<appUsername><![CDATA[]]></appUsername>\n\t\t\t<query><![CDATA[]]></query>\n\t\t\t<liteAppId><![CDATA[]]></liteAppId>\n\t\t\t<liteAppPath><![CDATA[]]></liteAppPath>\n\t\t\t<liteAppQuery><![CDATA[]]></liteAppQuery>\n\t\t\t<platformTagURL><![CDATA[]]></platformTagURL>\n\t\t\t<saleWording><![CDATA[]]></saleWording>\n\t\t\t<lastGMsgID><![CDATA[]]></lastGMsgID>\n\t\t\t<profileTypeWording><![CDATA[]]></profileTypeWording>\n\t\t\t<saleWordingExtra><![CDATA[]]></saleWordingExtra>\n\t\t\t<isWxShop><![CDATA[]]></isWxShop>\n\t\t\t<platformIconUrl><![CDATA[]]></platformIconUrl>\n\t\t\t<brandIconUrl><![CDATA[]]></brandIconUrl>\n\t\t\t<description><![CDATA[]]></description>\n\t\t\t<backgroundUrl><![CDATA[]]></backgroundUrl>\n\t\t\t<darkModePlatformIconUrl><![CDATA[]]></darkModePlatformIconUrl>\n\t\t\t<rIconUrl><![CDATA[]]></rIconUrl>\n\t\t\t<rIconUrlDarkMode><![CDATA[]]></rIconUrlDarkMode>\n\t\t\t<rWords><![CDATA[]]></rWords>\n\t\t\t<topShopIconUrl><![CDATA[]]></topShopIconUrl>\n\t\t\t<topShopIconUrlDarkMode><![CDATA[]]></topShopIconUrlDarkMode>\n\t\t\t<simplifyTopShopIconUrl><![CDATA[]]></simplifyTopShopIconUrl>\n\t\t\t<simplifyTopShopIconUrlDarkmode><![CDATA[]]></simplifyTopShopIconUrlDarkmode>\n\t\t\t<topShopIconWidth><![CDATA[0]]></topShopIconWidth>\n\t\t\t<topShopIconHeight><![CDATA[0]]></topShopIconHeight>\n\t\t\t<simplifyTopShopIconWidth><![CDATA[0]]></simplifyTopShopIconWidth>\n\t\t\t<simplifyTopShopIconHeight><![CDATA[0]]></simplifyTopShopIconHeight>\n\t\t\t<reputationInfo>\n\t\t\t\t<hasReputationInfo>0</hasReputationInfo>\n\t\t\t\t<reputationScore>0</reputationScore>\n\t\t\t\t<reputationWording />\n\t\t\t\t<reputationTextColor />\n\t\t\t\t<reputationLevelWording />\n\t\t\t\t<reputationBackgroundColor />\n\t\t\t</reputationInfo>\n\t\t\t<productImageURLList />\n\t\t</finderShopWindowShare>\n\t\t<findernamecard>\n\t\t\t<username />\n\t\t\t<avatar><![CDATA[]]></avatar>\n\t\t\t<nickname />\n\t\t\t<auth_job />\n\t\t\t<auth_icon>0</auth_icon>\n\t\t\t<auth_icon_url />\n\t\t\t<ecSource><![CDATA[]]></ecSource>\n\t\t\t<lastGMsgID><![CDATA[]]></lastGMsgID>\n\t\t</findernamecard>\n\t\t<finderGuarantee>\n\t\t\t<scene><![CDATA[0]]></scene>\n\t\t</finderGuarantee>\n\t\t<directshare>0</directshare>\n\t\t<gamecenter>\n\t\t\t<namecard>\n\t\t\t\t<iconUrl />\n\t\t\t\t<name />\n\t\t\t\t<desc />\n\t\t\t\t<tail />\n\t\t\t\t<jumpUrl />\n\t\t\t\t<liteappId />\n\t\t\t\t<liteappPath />\n\t\t\t\t<liteappQuery />\n\t\t\t\t<liteappMinVersion />\n\t\t\t</namecard>\n\t\t</gamecenter>\n\t\t<patMsg>\n\t\t\t<chatUser />\n\t\t\t<records>\n\t\t\t\t<recordNum>0</recordNum>\n\t\t\t</records>\n\t\t</patMsg>\n\t\t<secretmsg>\n\t\t\t<issecretmsg>0</issecretmsg>\n\t\t</secretmsg>\n\t\t<referfromscene>0</referfromscene>\n\t\t<gameshare>\n\t\t\t<liteappext>\n\t\t\t\t<liteappbizdata />\n\t\t\t\t<priority>0</priority>\n\t\t\t</liteappext>\n\t\t\t<appbrandext>\n\t\t\t\t<litegameinfo />\n\t\t\t\t<priority>-1</priority>\n\t\t\t</appbrandext>\n\t\t\t<gameshareid />\n\t\t\t<sharedata />\n\t\t\t<isvideo>0</isvideo>\n\t\t\t<duration>-1</duration>\n\t\t\t<isexposed>0</isexposed>\n\t\t\t<readtext />\n\t\t</gameshare>\n\t\t<tingChatRoomItem>\n\t\t\t<type>0</type>\n\t\t\t<categoryItem>null</categoryItem>\n\t\t\t<categoryId />\n\t\t</tingChatRoomItem>\n\t\t<mpsharetrace>\n\t\t\t<hasfinderelement>0</hasfinderelement>\n\t\t\t<lastgmsgid />\n\t\t</mpsharetrace>\n\t\t<wxgamecard>\n\t\t\t<framesetname />\n\t\t\t<mbcarddata />\n\t\t\t<minpkgversion />\n\t\t\t<clientextinfo />\n\t\t\t<mbcardheight>0</mbcardheight>\n\t\t\t<isoldversion>0</isoldversion>\n\t\t</wxgamecard>\n\t\t<ecskfcard>\n\t\t\t<framesetname />\n\t\t\t<mbcarddata />\n\t\t\t<minupdateunixtimestamp>0</minupdateunixtimestamp>\n\t\t\t<needheader>false</needheader>\n\t\t\t<summary />\n\t\t</ecskfcard>\n\t\t<liteapp>\n\t\t\t<id>null</id>\n\t\t\t<path />\n\t\t\t<query />\n\t\t\t<istransparent>0</istransparent>\n\t\t\t<hideicon>0</hideicon>\n\t\t\t<forbidforward>0</forbidforward>\n\t\t</liteapp>\n\t\t<opensdk_share_is_modified>0</opensdk_share_is_modified>\n\t</appmsg>\n\t<fromusername>wxid_wlnzvr8ivgd422</fromusername>\n\t<scene>0</scene>\n\t<appinfo>\n\t\t<version>1</version>\n\t\t<appname></appname>\n\t</appinfo>\n\t<commenturl></commenturl>\n</msg>\n'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1753661879, 'MsgSource': '<msgsource>\n\t<alnode>\n\t\t<fr>4</fr>\n\t</alnode>\n\t<sec_msg_node>\n\t\t<uuid>cc2ffc625d29b6a5cd0b0b32d1c18bf9_</uuid>\n\t\t<risk-file-flag />\n\t\t<risk-file-md5-list />\n\t</sec_msg_node>\n\t<silence>0</silence>\n\t<membercount>73</membercount>\n\t<signature>N0_V1_mVoqfqr2|v1_ZJT2Dqek</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'PushContent': '你收到了一条消息', 'NewMsgId': 670116771665707810, 'MsgSeq': 871404509}
2025-07-28 08:17:49 | DEBUG | 从群聊消息中提取发送者: wxid_wlnzvr8ivgd422
2025-07-28 08:17:49 | DEBUG | XML消息完整内容:
<?xml version="1.0"?>
<msg>
	<appmsg appid="" sdkver="0">
		<title>当前版本不支持展示该内容，请升级至最新版本。</title>
		<des />
		<username />
		<action>view</action>
		<type>51</type>
		<showtype>0</showtype>
		<content />
		<url>https://support.weixin.qq.com/security/readtemplate?t=w_security_center_website/upgrade</url>
		<lowurl />
		<forwardflag>0</forwardflag>
		<dataurl />
		<lowdataurl />
		<contentattr>0</contentattr>
		<streamvideo>
			<streamvideourl />
			<streamvideototaltime>0</streamvideototaltime>
			<streamvideotitle />
			<streamvideowording />
			<streamvideoweburl />
			<streamvideothumburl />
			<streamvideoaduxinfo />
			<streamvideopublishid />
		</streamvideo>
		<canvasPageItem>
			<canvasPageXml><![CDATA[]]></canvasPageXml>
		</canvasPageItem>
		<appattach>
			<totallen>0</totallen>
			<attachid />
			<cdnattachurl />
			<emoticonmd5 />
			<aeskey />
			<fileext />
			<islargefilemsg>0</islargefilemsg>
		</appattach>
		<extinfo />
		<androidsource>0</androidsource>
		<thumburl />
		<mediatagname />
		<messageaction><![CDATA[]]></messageaction>
		<messageext><![CDATA[]]></messageext>
		<emoticongift>
			<packageflag>0</packageflag>
			<packageid />
		</emoticongift>
		<emoticonshared>
			<packageflag>0</packageflag>
			<packageid />
		</emoticonshared>
		<designershared>
			<designeruin>0</designeruin>
			<designername>null</designername>
			<designerrediretcturl><![CDATA[null]]></designerrediretcturl>
		</designershared>
		<emotionpageshared>
			<tid>0</tid>
			<title>null</title>
			<desc>null</desc>
			<iconUrl><![CDATA[null]]></iconUrl>
			<secondUrl>null</secondUrl>
			<pageType>0</pageType>
			<setKey>null</setKey>
		</emotionpageshared>
		<webviewshared>
			<shareUrlOriginal />
			<shareUrlOpen />
			<jsAppId />
			<publisherId />
			<publisherReqId />
		</webviewshared>
		<template_id />
		<md5 />
		<websearch>
			<rec_category>0</rec_category>
			<channelId>0</channelId>
		</websearch>
		<weappinfo>
			<username />
			<appid />
			<appservicetype>0</appservicetype>
			<secflagforsinglepagemode>0</secflagforsinglepagemode>
			<videopageinfo>
				<thumbwidth>0</thumbwidth>
				<thumbheight>0</thumbheight>
				<fromopensdk>0</fromopensdk>
			</videopageinfo>
		</weappinfo>
		<statextstr />
		<musicShareItem>
			<musicDuration>0</musicDuration>
		</musicShareItem>
		<finderFeed>
			<objectId>14710495124684408874</objectId>
			<objectNonceId>15318336085899807238_4_20_13_1_1753660912906647_1139d708-6b46-11f0-962a-2d97986cb727</objectNonceId>
			<feedType>4</feedType>
			<nickname>薪儿姐师尊</nickname>
			<username>v2_060000231003b20faec8c4e18d18c7d0cb02ec31b0779b934bbfbadef1607a5981f59a0d8e8a@finder</username>
			<avatar><![CDATA[https://wx.qlogo.cn/finderhead/ver_1/3qqlqR0iapeI6we8oheRXMpaX5oxCjQVhv8vD5J2K9ib55rBGBNOibZ9m28am1d3XPw6FE9RClmiaEic5Rye30iaSbv48lEVfjQhBbCvicWnth94LU/0]]></avatar>
			<desc>请再次挑选您的英雄</desc>
			<mediaCount>1</mediaCount>
			<localId>0</localId>
			<authIconType>1</authIconType>
			<authIconUrl><![CDATA[https://dldir1v6.qq.com/weixin/checkresupdate/auth_icon_level3_2e2f94615c1e4651a25a7e0446f63135.png]]></authIconUrl>
			<mediaList>
				<media>
					<mediaType>4</mediaType>
					<url><![CDATA[http://wxapp.tc.qq.com/251/20302/stodownload?encfilekey=Cvvj5Ix3eewK0tHtibORqcsqchXNh0Gf3sJcaYqC2rQA0MSQs75Y0GZcGrk1dChm3MiafGKh1pvbdBovWd9KGOLkrxcnn9VqHFrp5qqucpB1lH8vlsxveMEgG5tcJlsTqC&bizid=1023&dotrans=0&hy=SH&idx=1&m=&uzid=7a170&token=cztXnd9GyrGBLJXOnqKwzApv5Zq1tISSz30Le2tbMricQ7Tibib0uLibfbaTBaMuC1LxBibGgUc7toNia90kYfABHcbAUXn8aqNkOTFWz5tQ81wYfnp7oPMLPnbuYyI41hzuWw8rTVKDyUJIbJ5icOsAMpjLxIsz09HtsGQNdKyfO12Gok&basedata=CAESBnhXVDEyNhoGeFdUMTExGgZ4V1QxMDIaBnhXVDExMhoGeFdUMTEzGgZ4V1QxMjYaBnhXVDEyNxoGeFdUMTI4IgwKCgoGeFdUMTEyEAEqBwi9HhAAGAI&sign=PaOiQc-VLWER1nPoJKmGXF3PVkTh8UiqN4DoM9p_PGZjEkEDUvOneOyYRgrgP-bQNTZNPV2neqNl5-_UC3kGAA&ctsc=20&extg=108bd00&fck=100010&svrbypass=AAuL%2FQsFAAABAAAAAABIBoOFl4Zvk%2Byf872GaBAAAADnaHZTnGbFfAj9RgZXfw6V%2BsBW9I9VXX689ILcoyt7co4zxiZfyRF%2FdWU1O9aotLVVF4srSMo7h9s%3D&svrnonce=1753660915]]></url>
					<thumbUrl><![CDATA[http://wxapp.tc.qq.com/251/20304/stodownload?encfilekey=rjD5jyTuFrIpZ2ibE8T7YmwgiahniaXswqzqY0RudZNsFHQwWxDjicYibdnoicUL4ml0qVSO5X0LmlicHmVPG7jtNorMicaDKmml9JDmsXyAL4j9IYZgrY897Is0lw&hy=SH&idx=1&m=&scene=2&uzid=1&picformat=200&wxampicformat=503&token=Cvvj5Ix3eeyD0TVgRZ2eE5ETQ4jRJdybe5SwoliaWuYMDqcmaCfPYcibO9JN32NO4bmBvucy0vxYYfCfN7PRYa0o0QVbvlRAIDYZzDniamMGjy9CxCPDiaCXsuTnS9HgPanawJuldFFkPGhEBZv265ia4ibfy0xsjicUibnG&ctsc=2-20]]></thumbUrl>
					<coverUrl><![CDATA[http://wxapp.tc.qq.com/251/20304/stodownload?encfilekey=rjD5jyTuFrIpZ2ibE8T7YmwgiahniaXswqzqY0RudZNsFHQwWxDjicYibdnoicUL4ml0qVSO5X0LmlicHmVPG7jtNorMicaDKmml9JDmsXyAL4j9IYZgrY897Is0lw&hy=SH&idx=1&m=&scene=2&uzid=1&picformat=200&wxampicformat=503&token=Cvvj5Ix3eeyD0TVgRZ2eE5ETQ4jRJdybe5SwoliaWuYMDqcmaCfPYcibO9JN32NO4bmBvucy0vxYYfCfN7PRYa0o0QVbvlRAIDYZzDniamMGjy9CxCPDiaCXsuTnS9HgPanawJuldFFkPGhEBZv265ia4ibfy0xsjicUibnG&ctsc=2-20]]></coverUrl>
					<fullCoverUrl><![CDATA[]]></fullCoverUrl>
					<fullClipInset><![CDATA[]]></fullClipInset>
					<width>1080.0</width>
					<height>1920.0</height>
					<videoPlayDuration>261</videoPlayDuration>
				</media>
			</mediaList>
			<megaVideo>
				<objectId />
				<objectNonceId />
			</megaVideo>
			<bizUsername />
			<bizNickname />
			<bizAvatar><![CDATA[]]></bizAvatar>
			<bizUsernameV2 />
			<bizAuthIconType>0</bizAuthIconType>
			<bizAuthIconUrl><![CDATA[]]></bizAuthIconUrl>
			<coverEffectType>0</coverEffectType>
			<coverEffectText><![CDATA[]]></coverEffectText>
			<finderForwardSource><![CDATA[]]></finderForwardSource>
			<contactJumpInfoStr><![CDATA[]]></contactJumpInfoStr>
			<ecSource><![CDATA[]]></ecSource>
			<lastGMsgID><![CDATA[]]></lastGMsgID>
			<sourceCommentScene>20</sourceCommentScene>
			<finderShareExtInfo><![CDATA[{"hasInput":false,"tabContextId":"4-1753661463626","contextId":"1-1-20-24e7544531b14642ae28261eb0aa51a7","shareSrcScene":4}]]></finderShareExtInfo>
		</finderFeed>
		<finderLiveProductShare>
			<finderLiveID><![CDATA[]]></finderLiveID>
			<finderUsername><![CDATA[]]></finderUsername>
			<finderObjectID><![CDATA[]]></finderObjectID>
			<finderNonceID><![CDATA[]]></finderNonceID>
			<liveStatus><![CDATA[]]></liveStatus>
			<appId><![CDATA[]]></appId>
			<pagePath><![CDATA[]]></pagePath>
			<productId><![CDATA[]]></productId>
			<coverUrl><![CDATA[]]></coverUrl>
			<productTitle><![CDATA[]]></productTitle>
			<marketPrice><![CDATA[0]]></marketPrice>
			<sellingPrice><![CDATA[0]]></sellingPrice>
			<platformHeadImg><![CDATA[]]></platformHeadImg>
			<platformName><![CDATA[]]></platformName>
			<shopWindowId><![CDATA[]]></shopWindowId>
			<flashSalePrice><![CDATA[0]]></flashSalePrice>
			<flashSaleEndTime><![CDATA[0]]></flashSaleEndTime>
			<ecSource><![CDATA[]]></ecSource>
			<sellingPriceWording><![CDATA[]]></sellingPriceWording>
			<platformIconURL><![CDATA[]]></platformIconURL>
			<firstProductTagURL><![CDATA[]]></firstProductTagURL>
			<firstProductTagAspectRatioString><![CDATA[0.0]]></firstProductTagAspectRatioString>
			<secondProductTagURL><![CDATA[]]></secondProductTagURL>
			<secondProductTagAspectRatioString><![CDATA[0.0]]></secondProductTagAspectRatioString>
			<firstGuaranteeWording><![CDATA[]]></firstGuaranteeWording>
			<secondGuaranteeWording><![CDATA[]]></secondGuaranteeWording>
			<thirdGuaranteeWording><![CDATA[]]></thirdGuaranteeWording>
			<isPriceBeginShow>false</isPriceBeginShow>
			<lastGMsgID><![CDATA[]]></lastGMsgID>
			<promoterKey><![CDATA[]]></promoterKey>
			<discountWording><![CDATA[]]></discountWording>
			<priceSuffixDescription><![CDATA[]]></priceSuffixDescription>
			<productCardKey><![CDATA[]]></productCardKey>
			<isWxShop><![CDATA[]]></isWxShop>
			<brandIconUrl><![CDATA[]]></brandIconUrl>
			<rIconUrl><![CDATA[]]></rIconUrl>
			<rIconUrlDarkMode><![CDATA[]]></rIconUrlDarkMode>
			<topShopIconUrl><![CDATA[]]></topShopIconUrl>
			<topShopIconUrlDarkMode><![CDATA[]]></topShopIconUrlDarkMode>
			<simplifyTopShopIconUrl><![CDATA[]]></simplifyTopShopIconUrl>
			<simplifyTopShopIconUrlDarkmode><![CDATA[]]></simplifyTopShopIconUrlDarkmode>
			<topShopIconWidth><![CDATA[0]]></topShopIconWidth>
			<topShopIconHeight><![CDATA[0]]></topShopIconHeight>
			<simplifyTopShopIconWidth><![CDATA[0]]></simplifyTopShopIconWidth>
			<simplifyTopShopIconHeight><![CDATA[0]]></simplifyTopShopIconHeight>
			<showBoxItemStringList />
		</finderLiveProductShare>
		<finderOrder>
			<appID><![CDATA[]]></appID>
			<orderID><![CDATA[]]></orderID>
			<path><![CDATA[]]></path>
			<priceWording><![CDATA[]]></priceWording>
			<stateWording><![CDATA[]]></stateWording>
			<productImageURL><![CDATA[]]></productImageURL>
			<products><![CDATA[]]></products>
			<productsCount><![CDATA[0]]></productsCount>
			<orderType><![CDATA[0]]></orderType>
			<newPriceWording><![CDATA[]]></newPriceWording>
			<newStateWording><![CDATA[]]></newStateWording>
			<useNewWording><![CDATA[0]]></useNewWording>
		</finderOrder>
		<finderShopWindowShare>
			<finderUsername><![CDATA[]]></finderUsername>
			<avatar><![CDATA[]]></avatar>
			<nickname><![CDATA[]]></nickname>
			<commodityInStockCount><![CDATA[]]></commodityInStockCount>
			<appId><![CDATA[]]></appId>
			<path><![CDATA[]]></path>
			<appUsername><![CDATA[]]></appUsername>
			<query><![CDATA[]]></query>
			<liteAppId><![CDATA[]]></liteAppId>
			<liteAppPath><![CDATA[]]></liteAppPath>
			<liteAppQuery><![CDATA[]]></liteAppQuery>
			<platformTagURL><![CDATA[]]></platformTagURL>
			<saleWording><![CDATA[]]></saleWording>
			<lastGMsgID><![CDATA[]]></lastGMsgID>
			<profileTypeWording><![CDATA[]]></profileTypeWording>
			<saleWordingExtra><![CDATA[]]></saleWordingExtra>
			<isWxShop><![CDATA[]]></isWxShop>
			<platformIconUrl><![CDATA[]]></platformIconUrl>
			<brandIconUrl><![CDATA[]]></brandIconUrl>
			<description><![CDATA[]]></description>
			<backgroundUrl><![CDATA[]]></backgroundUrl>
			<darkModePlatformIconUrl><![CDATA[]]></darkModePlatformIconUrl>
			<rIconUrl><![CDATA[]]></rIconUrl>
			<rIconUrlDarkMode><![CDATA[]]></rIconUrlDarkMode>
			<rWords><![CDATA[]]></rWords>
			<topShopIconUrl><![CDATA[]]></topShopIconUrl>
			<topShopIconUrlDarkMode><![CDATA[]]></topShopIconUrlDarkMode>
			<simplifyTopShopIconUrl><![CDATA[]]></simplifyTopShopIconUrl>
			<simplifyTopShopIconUrlDarkmode><![CDATA[]]></simplifyTopShopIconUrlDarkmode>
			<topShopIconWidth><![CDATA[0]]></topShopIconWidth>
			<topShopIconHeight><![CDATA[0]]></topShopIconHeight>
			<simplifyTopShopIconWidth><![CDATA[0]]></simplifyTopShopIconWidth>
			<simplifyTopShopIconHeight><![CDATA[0]]></simplifyTopShopIconHeight>
			<reputationInfo>
				<hasReputationInfo>0</hasReputationInfo>
				<reputationScore>0</reputationScore>
				<reputationWording />
				<reputationTextColor />
				<reputationLevelWording />
				<reputationBackgroundColor />
			</reputationInfo>
			<productImageURLList />
		</finderShopWindowShare>
		<findernamecard>
			<username />
			<avatar><![CDATA[]]></avatar>
			<nickname />
			<auth_job />
			<auth_icon>0</auth_icon>
			<auth_icon_url />
			<ecSource><![CDATA[]]></ecSource>
			<lastGMsgID><![CDATA[]]></lastGMsgID>
		</findernamecard>
		<finderGuarantee>
			<scene><![CDATA[0]]></scene>
		</finderGuarantee>
		<directshare>0</directshare>
		<gamecenter>
			<namecard>
				<iconUrl />
				<name />
				<desc />
				<tail />
				<jumpUrl />
				<liteappId />
				<liteappPath />
				<liteappQuery />
				<liteappMinVersion />
			</namecard>
		</gamecenter>
		<patMsg>
			<chatUser />
			<records>
				<recordNum>0</recordNum>
			</records>
		</patMsg>
		<secretmsg>
			<issecretmsg>0</issecretmsg>
		</secretmsg>
		<referfromscene>0</referfromscene>
		<gameshare>
			<liteappext>
				<liteappbizdata />
				<priority>0</priority>
			</liteappext>
			<appbrandext>
				<litegameinfo />
				<priority>-1</priority>
			</appbrandext>
			<gameshareid />
			<sharedata />
			<isvideo>0</isvideo>
			<duration>-1</duration>
			<isexposed>0</isexposed>
			<readtext />
		</gameshare>
		<tingChatRoomItem>
			<type>0</type>
			<categoryItem>null</categoryItem>
			<categoryId />
		</tingChatRoomItem>
		<mpsharetrace>
			<hasfinderelement>0</hasfinderelement>
			<lastgmsgid />
		</mpsharetrace>
		<wxgamecard>
			<framesetname />
			<mbcarddata />
			<minpkgversion />
			<clientextinfo />
			<mbcardheight>0</mbcardheight>
			<isoldversion>0</isoldversion>
		</wxgamecard>
		<ecskfcard>
			<framesetname />
			<mbcarddata />
			<minupdateunixtimestamp>0</minupdateunixtimestamp>
			<needheader>false</needheader>
			<summary />
		</ecskfcard>
		<liteapp>
			<id>null</id>
			<path />
			<query />
			<istransparent>0</istransparent>
			<hideicon>0</hideicon>
			<forbidforward>0</forbidforward>
		</liteapp>
		<opensdk_share_is_modified>0</opensdk_share_is_modified>
	</appmsg>
	<fromusername>wxid_wlnzvr8ivgd422</fromusername>
	<scene>0</scene>
	<appinfo>
		<version>1</version>
		<appname></appname>
	</appinfo>
	<commenturl></commenturl>
</msg>

2025-07-28 08:17:49 | DEBUG | XML消息类型: 51
2025-07-28 08:17:49 | DEBUG | XML消息标题: 当前版本不支持展示该内容，请升级至最新版本。
2025-07-28 08:17:49 | DEBUG | XML消息描述: None
2025-07-28 08:17:49 | DEBUG | 附件信息 totallen: 0
2025-07-28 08:17:49 | DEBUG | 附件信息 islargefilemsg: 0
2025-07-28 08:17:49 | DEBUG | XML消息URL: https://support.weixin.qq.com/security/readtemplate?t=w_security_center_website/upgrade
2025-07-28 08:17:49 | INFO | 未知的XML消息类型: 51
2025-07-28 08:17:49 | INFO | 消息标题: 当前版本不支持展示该内容，请升级至最新版本。
2025-07-28 08:17:49 | INFO | 消息描述: None
2025-07-28 08:17:49 | INFO | 消息URL: https://support.weixin.qq.com/security/readtemplate?t=w_security_center_website/upgrade
2025-07-28 08:17:49 | INFO | 完整XML内容:
<?xml version="1.0"?>
<msg>
	<appmsg appid="" sdkver="0">
		<title>当前版本不支持展示该内容，请升级至最新版本。</title>
		<des />
		<username />
		<action>view</action>
		<type>51</type>
		<showtype>0</showtype>
		<content />
		<url>https://support.weixin.qq.com/security/readtemplate?t=w_security_center_website/upgrade</url>
		<lowurl />
		<forwardflag>0</forwardflag>
		<dataurl />
		<lowdataurl />
		<contentattr>0</contentattr>
		<streamvideo>
			<streamvideourl />
			<streamvideototaltime>0</streamvideototaltime>
			<streamvideotitle />
			<streamvideowording />
			<streamvideoweburl />
			<streamvideothumburl />
			<streamvideoaduxinfo />
			<streamvideopublishid />
		</streamvideo>
		<canvasPageItem>
			<canvasPageXml><![CDATA[]]></canvasPageXml>
		</canvasPageItem>
		<appattach>
			<totallen>0</totallen>
			<attachid />
			<cdnattachurl />
			<emoticonmd5 />
			<aeskey />
			<fileext />
			<islargefilemsg>0</islargefilemsg>
		</appattach>
		<extinfo />
		<androidsource>0</androidsource>
		<thumburl />
		<mediatagname />
		<messageaction><![CDATA[]]></messageaction>
		<messageext><![CDATA[]]></messageext>
		<emoticongift>
			<packageflag>0</packageflag>
			<packageid />
		</emoticongift>
		<emoticonshared>
			<packageflag>0</packageflag>
			<packageid />
		</emoticonshared>
		<designershared>
			<designeruin>0</designeruin>
			<designername>null</designername>
			<designerrediretcturl><![CDATA[null]]></designerrediretcturl>
		</designershared>
		<emotionpageshared>
			<tid>0</tid>
			<title>null</title>
			<desc>null</desc>
			<iconUrl><![CDATA[null]]></iconUrl>
			<secondUrl>null</secondUrl>
			<pageType>0</pageType>
			<setKey>null</setKey>
		</emotionpageshared>
		<webviewshared>
			<shareUrlOriginal />
			<shareUrlOpen />
			<jsAppId />
			<publisherId />
			<publisherReqId />
		</webviewshared>
		<template_id />
		<md5 />
		<websearch>
			<rec_category>0</rec_category>
			<channelId>0</channelId>
		</websearch>
		<weappinfo>
			<username />
			<appid />
			<appservicetype>0</appservicetype>
			<secflagforsinglepagemode>0</secflagforsinglepagemode>
			<videopageinfo>
				<thumbwidth>0</thumbwidth>
				<thumbheight>0</thumbheight>
				<fromopensdk>0</fromopensdk>
			</videopageinfo>
		</weappinfo>
		<statextstr />
		<musicShareItem>
			<musicDuration>0</musicDuration>
		</musicShareItem>
		<finderFeed>
			<objectId>14710495124684408874</objectId>
			<objectNonceId>15318336085899807238_4_20_13_1_1753660912906647_1139d708-6b46-11f0-962a-2d97986cb727</objectNonceId>
			<feedType>4</feedType>
			<nickname>薪儿姐师尊</nickname>
			<username>v2_060000231003b20faec8c4e18d18c7d0cb02ec31b0779b934bbfbadef1607a5981f59a0d8e8a@finder</username>
			<avatar><![CDATA[https://wx.qlogo.cn/finderhead/ver_1/3qqlqR0iapeI6we8oheRXMpaX5oxCjQVhv8vD5J2K9ib55rBGBNOibZ9m28am1d3XPw6FE9RClmiaEic5Rye30iaSbv48lEVfjQhBbCvicWnth94LU/0]]></avatar>
			<desc>请再次挑选您的英雄</desc>
			<mediaCount>1</mediaCount>
			<localId>0</localId>
			<authIconType>1</authIconType>
			<authIconUrl><![CDATA[https://dldir1v6.qq.com/weixin/checkresupdate/auth_icon_level3_2e2f94615c1e4651a25a7e0446f63135.png]]></authIconUrl>
			<mediaList>
				<media>
					<mediaType>4</mediaType>
					<url><![CDATA[http://wxapp.tc.qq.com/251/20302/stodownload?encfilekey=Cvvj5Ix3eewK0tHtibORqcsqchXNh0Gf3sJcaYqC2rQA0MSQs75Y0GZcGrk1dChm3MiafGKh1pvbdBovWd9KGOLkrxcnn9VqHFrp5qqucpB1lH8vlsxveMEgG5tcJlsTqC&bizid=1023&dotrans=0&hy=SH&idx=1&m=&uzid=7a170&token=cztXnd9GyrGBLJXOnqKwzApv5Zq1tISSz30Le2tbMricQ7Tibib0uLibfbaTBaMuC1LxBibGgUc7toNia90kYfABHcbAUXn8aqNkOTFWz5tQ81wYfnp7oPMLPnbuYyI41hzuWw8rTVKDyUJIbJ5icOsAMpjLxIsz09HtsGQNdKyfO12Gok&basedata=CAESBnhXVDEyNhoGeFdUMTExGgZ4V1QxMDIaBnhXVDExMhoGeFdUMTEzGgZ4V1QxMjYaBnhXVDEyNxoGeFdUMTI4IgwKCgoGeFdUMTEyEAEqBwi9HhAAGAI&sign=PaOiQc-VLWER1nPoJKmGXF3PVkTh8UiqN4DoM9p_PGZjEkEDUvOneOyYRgrgP-bQNTZNPV2neqNl5-_UC3kGAA&ctsc=20&extg=108bd00&fck=100010&svrbypass=AAuL%2FQsFAAABAAAAAABIBoOFl4Zvk%2Byf872GaBAAAADnaHZTnGbFfAj9RgZXfw6V%2BsBW9I9VXX689ILcoyt7co4zxiZfyRF%2FdWU1O9aotLVVF4srSMo7h9s%3D&svrnonce=1753660915]]></url>
					<thumbUrl><![CDATA[http://wxapp.tc.qq.com/251/20304/stodownload?encfilekey=rjD5jyTuFrIpZ2ibE8T7YmwgiahniaXswqzqY0RudZNsFHQwWxDjicYibdnoicUL4ml0qVSO5X0LmlicHmVPG7jtNorMicaDKmml9JDmsXyAL4j9IYZgrY897Is0lw&hy=SH&idx=1&m=&scene=2&uzid=1&picformat=200&wxampicformat=503&token=Cvvj5Ix3eeyD0TVgRZ2eE5ETQ4jRJdybe5SwoliaWuYMDqcmaCfPYcibO9JN32NO4bmBvucy0vxYYfCfN7PRYa0o0QVbvlRAIDYZzDniamMGjy9CxCPDiaCXsuTnS9HgPanawJuldFFkPGhEBZv265ia4ibfy0xsjicUibnG&ctsc=2-20]]></thumbUrl>
					<coverUrl><![CDATA[http://wxapp.tc.qq.com/251/20304/stodownload?encfilekey=rjD5jyTuFrIpZ2ibE8T7YmwgiahniaXswqzqY0RudZNsFHQwWxDjicYibdnoicUL4ml0qVSO5X0LmlicHmVPG7jtNorMicaDKmml9JDmsXyAL4j9IYZgrY897Is0lw&hy=SH&idx=1&m=&scene=2&uzid=1&picformat=200&wxampicformat=503&token=Cvvj5Ix3eeyD0TVgRZ2eE5ETQ4jRJdybe5SwoliaWuYMDqcmaCfPYcibO9JN32NO4bmBvucy0vxYYfCfN7PRYa0o0QVbvlRAIDYZzDniamMGjy9CxCPDiaCXsuTnS9HgPanawJuldFFkPGhEBZv265ia4ibfy0xsjicUibnG&ctsc=2-20]]></coverUrl>
					<fullCoverUrl><![CDATA[]]></fullCoverUrl>
					<fullClipInset><![CDATA[]]></fullClipInset>
					<width>1080.0</width>
					<height>1920.0</height>
					<videoPlayDuration>261</videoPlayDuration>
				</media>
			</mediaList>
			<megaVideo>
				<objectId />
				<objectNonceId />
			</megaVideo>
			<bizUsername />
			<bizNickname />
			<bizAvatar><![CDATA[]]></bizAvatar>
			<bizUsernameV2 />
			<bizAuthIconType>0</bizAuthIconType>
			<bizAuthIconUrl><![CDATA[]]></bizAuthIconUrl>
			<coverEffectType>0</coverEffectType>
			<coverEffectText><![CDATA[]]></coverEffectText>
			<finderForwardSource><![CDATA[]]></finderForwardSource>
			<contactJumpInfoStr><![CDATA[]]></contactJumpInfoStr>
			<ecSource><![CDATA[]]></ecSource>
			<lastGMsgID><![CDATA[]]></lastGMsgID>
			<sourceCommentScene>20</sourceCommentScene>
			<finderShareExtInfo><![CDATA[{"hasInput":false,"tabContextId":"4-1753661463626","contextId":"1-1-20-24e7544531b14642ae28261eb0aa51a7","shareSrcScene":4}]]></finderShareExtInfo>
		</finderFeed>
		<finderLiveProductShare>
			<finderLiveID><![CDATA[]]></finderLiveID>
			<finderUsername><![CDATA[]]></finderUsername>
			<finderObjectID><![CDATA[]]></finderObjectID>
			<finderNonceID><![CDATA[]]></finderNonceID>
			<liveStatus><![CDATA[]]></liveStatus>
			<appId><![CDATA[]]></appId>
			<pagePath><![CDATA[]]></pagePath>
			<productId><![CDATA[]]></productId>
			<coverUrl><![CDATA[]]></coverUrl>
			<productTitle><![CDATA[]]></productTitle>
			<marketPrice><![CDATA[0]]></marketPrice>
			<sellingPrice><![CDATA[0]]></sellingPrice>
			<platformHeadImg><![CDATA[]]></platformHeadImg>
			<platformName><![CDATA[]]></platformName>
			<shopWindowId><![CDATA[]]></shopWindowId>
			<flashSalePrice><![CDATA[0]]></flashSalePrice>
			<flashSaleEndTime><![CDATA[0]]></flashSaleEndTime>
			<ecSource><![CDATA[]]></ecSource>
			<sellingPriceWording><![CDATA[]]></sellingPriceWording>
			<platformIconURL><![CDATA[]]></platformIconURL>
			<firstProductTagURL><![CDATA[]]></firstProductTagURL>
			<firstProductTagAspectRatioString><![CDATA[0.0]]></firstProductTagAspectRatioString>
			<secondProductTagURL><![CDATA[]]></secondProductTagURL>
			<secondProductTagAspectRatioString><![CDATA[0.0]]></secondProductTagAspectRatioString>
			<firstGuaranteeWording><![CDATA[]]></firstGuaranteeWording>
			<secondGuaranteeWording><![CDATA[]]></secondGuaranteeWording>
			<thirdGuaranteeWording><![CDATA[]]></thirdGuaranteeWording>
			<isPriceBeginShow>false</isPriceBeginShow>
			<lastGMsgID><![CDATA[]]></lastGMsgID>
			<promoterKey><![CDATA[]]></promoterKey>
			<discountWording><![CDATA[]]></discountWording>
			<priceSuffixDescription><![CDATA[]]></priceSuffixDescription>
			<productCardKey><![CDATA[]]></productCardKey>
			<isWxShop><![CDATA[]]></isWxShop>
			<brandIconUrl><![CDATA[]]></brandIconUrl>
			<rIconUrl><![CDATA[]]></rIconUrl>
			<rIconUrlDarkMode><![CDATA[]]></rIconUrlDarkMode>
			<topShopIconUrl><![CDATA[]]></topShopIconUrl>
			<topShopIconUrlDarkMode><![CDATA[]]></topShopIconUrlDarkMode>
			<simplifyTopShopIconUrl><![CDATA[]]></simplifyTopShopIconUrl>
			<simplifyTopShopIconUrlDarkmode><![CDATA[]]></simplifyTopShopIconUrlDarkmode>
			<topShopIconWidth><![CDATA[0]]></topShopIconWidth>
			<topShopIconHeight><![CDATA[0]]></topShopIconHeight>
			<simplifyTopShopIconWidth><![CDATA[0]]></simplifyTopShopIconWidth>
			<simplifyTopShopIconHeight><![CDATA[0]]></simplifyTopShopIconHeight>
			<showBoxItemStringList />
		</finderLiveProductShare>
		<finderOrder>
			<appID><![CDATA[]]></appID>
			<orderID><![CDATA[]]></orderID>
			<path><![CDATA[]]></path>
			<priceWording><![CDATA[]]></priceWording>
			<stateWording><![CDATA[]]></stateWording>
			<productImageURL><![CDATA[]]></productImageURL>
			<products><![CDATA[]]></products>
			<productsCount><![CDATA[0]]></productsCount>
			<orderType><![CDATA[0]]></orderType>
			<newPriceWording><![CDATA[]]></newPriceWording>
			<newStateWording><![CDATA[]]></newStateWording>
			<useNewWording><![CDATA[0]]></useNewWording>
		</finderOrder>
		<finderShopWindowShare>
			<finderUsername><![CDATA[]]></finderUsername>
			<avatar><![CDATA[]]></avatar>
			<nickname><![CDATA[]]></nickname>
			<commodityInStockCount><![CDATA[]]></commodityInStockCount>
			<appId><![CDATA[]]></appId>
			<path><![CDATA[]]></path>
			<appUsername><![CDATA[]]></appUsername>
			<query><![CDATA[]]></query>
			<liteAppId><![CDATA[]]></liteAppId>
			<liteAppPath><![CDATA[]]></liteAppPath>
			<liteAppQuery><![CDATA[]]></liteAppQuery>
			<platformTagURL><![CDATA[]]></platformTagURL>
			<saleWording><![CDATA[]]></saleWording>
			<lastGMsgID><![CDATA[]]></lastGMsgID>
			<profileTypeWording><![CDATA[]]></profileTypeWording>
			<saleWordingExtra><![CDATA[]]></saleWordingExtra>
			<isWxShop><![CDATA[]]></isWxShop>
			<platformIconUrl><![CDATA[]]></platformIconUrl>
			<brandIconUrl><![CDATA[]]></brandIconUrl>
			<description><![CDATA[]]></description>
			<backgroundUrl><![CDATA[]]></backgroundUrl>
			<darkModePlatformIconUrl><![CDATA[]]></darkModePlatformIconUrl>
			<rIconUrl><![CDATA[]]></rIconUrl>
			<rIconUrlDarkMode><![CDATA[]]></rIconUrlDarkMode>
			<rWords><![CDATA[]]></rWords>
			<topShopIconUrl><![CDATA[]]></topShopIconUrl>
			<topShopIconUrlDarkMode><![CDATA[]]></topShopIconUrlDarkMode>
			<simplifyTopShopIconUrl><![CDATA[]]></simplifyTopShopIconUrl>
			<simplifyTopShopIconUrlDarkmode><![CDATA[]]></simplifyTopShopIconUrlDarkmode>
			<topShopIconWidth><![CDATA[0]]></topShopIconWidth>
			<topShopIconHeight><![CDATA[0]]></topShopIconHeight>
			<simplifyTopShopIconWidth><![CDATA[0]]></simplifyTopShopIconWidth>
			<simplifyTopShopIconHeight><![CDATA[0]]></simplifyTopShopIconHeight>
			<reputationInfo>
				<hasReputationInfo>0</hasReputationInfo>
				<reputationScore>0</reputationScore>
				<reputationWording />
				<reputationTextColor />
				<reputationLevelWording />
				<reputationBackgroundColor />
			</reputationInfo>
			<productImageURLList />
		</finderShopWindowShare>
		<findernamecard>
			<username />
			<avatar><![CDATA[]]></avatar>
			<nickname />
			<auth_job />
			<auth_icon>0</auth_icon>
			<auth_icon_url />
			<ecSource><![CDATA[]]></ecSource>
			<lastGMsgID><![CDATA[]]></lastGMsgID>
		</findernamecard>
		<finderGuarantee>
			<scene><![CDATA[0]]></scene>
		</finderGuarantee>
		<directshare>0</directshare>
		<gamecenter>
			<namecard>
				<iconUrl />
				<name />
				<desc />
				<tail />
				<jumpUrl />
				<liteappId />
				<liteappPath />
				<liteappQuery />
				<liteappMinVersion />
			</namecard>
		</gamecenter>
		<patMsg>
			<chatUser />
			<records>
				<recordNum>0</recordNum>
			</records>
		</patMsg>
		<secretmsg>
			<issecretmsg>0</issecretmsg>
		</secretmsg>
		<referfromscene>0</referfromscene>
		<gameshare>
			<liteappext>
				<liteappbizdata />
				<priority>0</priority>
			</liteappext>
			<appbrandext>
				<litegameinfo />
				<priority>-1</priority>
			</appbrandext>
			<gameshareid />
			<sharedata />
			<isvideo>0</isvideo>
			<duration>-1</duration>
			<isexposed>0</isexposed>
			<readtext />
		</gameshare>
		<tingChatRoomItem>
			<type>0</type>
			<categoryItem>null</categoryItem>
			<categoryId />
		</tingChatRoomItem>
		<mpsharetrace>
			<hasfinderelement>0</hasfinderelement>
			<lastgmsgid />
		</mpsharetrace>
		<wxgamecard>
			<framesetname />
			<mbcarddata />
			<minpkgversion />
			<clientextinfo />
			<mbcardheight>0</mbcardheight>
			<isoldversion>0</isoldversion>
		</wxgamecard>
		<ecskfcard>
			<framesetname />
			<mbcarddata />
			<minupdateunixtimestamp>0</minupdateunixtimestamp>
			<needheader>false</needheader>
			<summary />
		</ecskfcard>
		<liteapp>
			<id>null</id>
			<path />
			<query />
			<istransparent>0</istransparent>
			<hideicon>0</hideicon>
			<forbidforward>0</forbidforward>
		</liteapp>
		<opensdk_share_is_modified>0</opensdk_share_is_modified>
	</appmsg>
	<fromusername>wxid_wlnzvr8ivgd422</fromusername>
	<scene>0</scene>
	<appinfo>
		<version>1</version>
		<appname></appname>
	</appinfo>
	<commenturl></commenturl>
</msg>

2025-07-28 08:20:47 | DEBUG | 收到消息: {'MsgId': 2038028305, 'FromUserName': {'string': '48097389945@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 1, 'Content': {'string': 'wxid_ubbh6q832tcs21:\n好'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1753662058, 'MsgSource': '<msgsource>\n\t<pua>1</pua>\n\t<eggIncluded>1</eggIncluded>\n\t<silence>0</silence>\n\t<membercount>73</membercount>\n\t<signature>N0_V1_ckELbwqz|v1_KEFIxbat</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'PushContent': '郭 : 好', 'NewMsgId': 7592158122027281494, 'MsgSeq': 871404510}
2025-07-28 08:20:47 | INFO | 收到文本消息: 消息ID:2038028305 来自:48097389945@chatroom 发送人:wxid_ubbh6q832tcs21 @:[] 内容:好
2025-07-28 08:20:48 | INFO | 发送表情消息: 对方wxid:48097389945@chatroom md5:ec659f94db17b05434544a0c377ddfc0 总长度:9992069
2025-07-28 08:20:48 | DEBUG | 处理消息内容: '好'
2025-07-28 08:20:48 | DEBUG | 消息内容 '好' 不匹配任何命令，忽略
