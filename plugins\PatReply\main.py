import os, time, traceback
try:
    import tomllib
except ModuleNotFoundError:
    import tomli as tomllib
from loguru import logger
from pathlib import Path
from WechatAPI import WechatAPIClient
from utils.decorators import on_pat_message
from utils.plugin_base import PluginBase

class PatReply(PluginBase):
    description = "拍一拍语音回复插件"
    author = "XYBot"
    version = "1.0.0"
    plugin_name = "PatReply"

    def __init__(self):
        super().__init__()
        self.temp_dir = Path("plugins/PatReply/temp")
        self.temp_dir.mkdir(parents=True, exist_ok=True)
        self._load_config()
        self.user_last_request = {}
        self._prepare_voice_file()
        # 预加载base64缓存到内存
        self._load_base64_cache()

    def _load_config(self):
        try:
            with open(f"plugins/{self.plugin_name}/config.toml", "rb") as f:
                config = tomllib.load(f).get(self.plugin_name, {})
        except:
            config = {}

        self.enable = config.get("enable", True)
        self.natural_response = config.get("natural_response", False)
        self.voice_file_path = config.get("voice_file_path", "C:\\XYBotV2\\data\\paiyipai\\1_BFB5258B-2B13-4CF4-A40C-5BCCE74E5414.mp3")
        self.cooldown = config.get("rate_limit", {}).get("cooldown", 3)
        # 只有被拍的是这个wxid时才触发
        self.target_wxid = config.get("target_wxid", "wxid_4usgcju5ey9q29")

        # 转换后的AMR文件路径
        self.amr_file_path = self.voice_file_path.replace(".mp3", "_converted.amr")
        # 缓存的base64字符串路径
        self.amr_base64_cache_path = self.voice_file_path.replace(".mp3", "_base64.txt")
        # 内存中的base64缓存
        self.amr_base64_cache = None

    def _prepare_voice_file(self):
        """预处理语音文件，将WAV转换为AMR格式并保存"""
        try:
            # 检查原始文件是否存在
            if not os.path.exists(self.voice_file_path):
                logger.error(f"[{self.plugin_name}] 原始语音文件不存在: {self.voice_file_path}")
                return

            # 检查转换后的AMR文件是否已存在且比原文件新
            if os.path.exists(self.amr_file_path):
                original_mtime = os.path.getmtime(self.voice_file_path)
                amr_mtime = os.path.getmtime(self.amr_file_path)
                if amr_mtime >= original_mtime:
                    logger.info(f"[{self.plugin_name}] AMR文件已存在且是最新的: {self.amr_file_path}")
                    return

            # 需要转换文件
            logger.info(f"[{self.plugin_name}] 开始转换语音文件为AMR格式...")

            # 使用pydub直接转换
            from pydub import AudioSegment

            # 使用pydub转换
            audio = AudioSegment.from_file(self.voice_file_path, format="wav")
            audio = audio.set_frame_rate(8000).set_channels(1)  # AMR标准参数

            # 保存为AMR文件
            audio.export(self.amr_file_path, format="amr")

            # 生成base64缓存以提高发送速度
            self._generate_base64_cache()

            logger.info(f"[{self.plugin_name}] 语音文件转换完成: {self.amr_file_path}")

        except Exception as e:
            logger.error(f"[{self.plugin_name}] 预处理语音文件失败: {e}")
            logger.error(f"[{self.plugin_name}] 将在运行时进行转换")

    def _generate_base64_cache(self):
        """生成AMR文件的base64缓存"""
        try:
            import base64

            # 读取AMR文件
            with open(self.amr_file_path, "rb") as f:
                amr_data = f.read()

            # 转换为base64
            amr_base64 = base64.b64encode(amr_data).decode()

            # 保存到缓存文件
            with open(self.amr_base64_cache_path, "w", encoding="utf-8") as f:
                f.write(amr_base64)

            # 加载到内存缓存
            self.amr_base64_cache = amr_base64

            logger.debug(f"[{self.plugin_name}] Base64缓存生成完成: {len(amr_base64)} 字符")

        except Exception as e:
            logger.error(f"[{self.plugin_name}] 生成base64缓存失败: {e}")

    def _load_base64_cache(self):
        """加载base64缓存到内存"""
        try:
            # 如果内存中已有缓存，直接返回
            if self.amr_base64_cache:
                return self.amr_base64_cache

            # 检查缓存文件是否存在且比AMR文件新
            if os.path.exists(self.amr_base64_cache_path) and os.path.exists(self.amr_file_path):
                cache_mtime = os.path.getmtime(self.amr_base64_cache_path)
                amr_mtime = os.path.getmtime(self.amr_file_path)

                if cache_mtime >= amr_mtime:
                    # 从缓存文件加载
                    with open(self.amr_base64_cache_path, "r", encoding="utf-8") as f:
                        self.amr_base64_cache = f.read().strip()

                    logger.debug(f"[{self.plugin_name}] 从缓存加载base64: {len(self.amr_base64_cache)} 字符")
                    return self.amr_base64_cache

            # 缓存不存在或过期，重新生成
            self._generate_base64_cache()
            return self.amr_base64_cache

        except Exception as e:
            logger.error(f"[{self.plugin_name}] 加载base64缓存失败: {e}")
            return None

    @on_pat_message
    async def handle_pat(self, bot: WechatAPIClient, message: dict):
        """处理拍一拍消息"""
        if not self.enable:
            return

        try:
            wxid = message["FromWxid"]  # 消息来源（群聊或私聊）
            user_wxid = message["SenderWxid"]  # 发送者ID

            # 检查被拍者是否是目标wxid
            patted_wxid = message.get("Patted", "")
            if patted_wxid != self.target_wxid:
                logger.debug(f"[{self.plugin_name}] 被拍者 {patted_wxid} 不是目标用户 {self.target_wxid}，跳过")
                return

            # 限流检查
            wait_time = self._check_user_limit(wxid, user_wxid)
            if wait_time > 0:
                logger.info(f"[{self.plugin_name}] 用户 {user_wxid} 触发限流，需等待 {wait_time:.1f} 秒")
                return

            # 优先使用base64缓存（最快）
            amr_base64 = self._load_base64_cache()
            if amr_base64:
                logger.debug(f"[{self.plugin_name}] 使用base64缓存发送语音")
                await bot.send_voice_message(wxid, amr_base64, format="amr")
            elif os.path.exists(self.amr_file_path):
                logger.debug(f"[{self.plugin_name}] 使用预转换的AMR文件: {self.amr_file_path}")
                await bot.send_voice_message(wxid, Path(self.amr_file_path), format="amr")
            elif os.path.exists(self.voice_file_path):
                logger.debug(f"[{self.plugin_name}] AMR文件不存在，尝试实时转换WAV文件")
                # 实时转换WAV为AMR格式发送
                try:
                    with open(self.voice_file_path, "rb") as f:
                        wav_data = f.read()

                    # 使用系统的转换方法将WAV转换为AMR
                    amr_base64 = bot.wav_byte_to_amr_base64(wav_data)
                    await bot.send_voice_message(wxid, amr_base64, format="amr")
                except Exception as convert_error:
                    logger.error(f"[{self.plugin_name}] 实时转换失败: {convert_error}")
                    # 如果转换失败，尝试直接发送原文件
                    await bot.send_voice_message(wxid, Path(self.voice_file_path), format="wav")
            else:
                logger.error(f"[{self.plugin_name}] 语音文件不存在: {self.voice_file_path}")
                return
            logger.info(f"[{self.plugin_name}] 已向 {wxid} 发送拍一拍语音回复")

        except Exception as e:
            logger.error(f"[{self.plugin_name}] 处理拍一拍消息异常: {e}")
            logger.error(f"[{self.plugin_name}] 异常详情: {traceback.format_exc()}")

    def _check_user_limit(self, wxid: str, user_wxid: str) -> float:
        """检查用户限流"""
        user_key = f"{wxid}_{user_wxid}"
        last_request = self.user_last_request.get(user_key, 0)
        current_time = time.time()
        elapsed = current_time - last_request
        self.user_last_request[user_key] = current_time

        if elapsed < self.cooldown:
            return self.cooldown - elapsed

        return 0
