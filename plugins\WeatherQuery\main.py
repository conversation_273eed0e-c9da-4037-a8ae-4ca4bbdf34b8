import os
import json
import asyncio
import time
import urllib.parse
import base64
import re
try:
    import tomllib
except ModuleNotFoundError:
    import tomli as tomllib
from loguru import logger
from pathlib import Path
from typing import Optional, Dict, Any, List, Tuple, Union

import httpx
from WechatAPI import WechatAPIClient
from utils.decorators import on_text_message
from utils.plugin_base import PluginBase

class WeatherQuery(PluginBase):
    description = "查询全国各地天气预报"
    author = "XYBot开发者"
    version = "1.0.2"  # 更新版本号
    plugin_name = "WeatherQuery"

    def __init__(self):
        # 必须先调用父类初始化
        super().__init__()
        
        # 初始化临时目录
        self.temp_dir = Path("plugins/WeatherQuery/temp")
        self.temp_dir.mkdir(parents=True, exist_ok=True)
        
        # 读取配置
        config = {}
        try:
            config_path = f"plugins/{self.plugin_name}/config.toml"
            if not os.path.exists(config_path):
                logger.warning(f"[{self.plugin_name}] 配置文件 {config_path} 不存在，将使用默认配置")
                config = {}
            else:
                with open(config_path, "rb") as f:
                    plugin_config = tomllib.load(f)
                config = plugin_config.get(self.plugin_name, {})
        except Exception as e:
            logger.error(f"[{self.plugin_name}] 配置文件读取失败: {str(e)}")
            config = {}

        # 基本配置初始化
        self.enable = config.get("enable", True)
        self.command = config.get("command", ["天气", "查天气", "weather"])
        self.command_format = config.get("command-format", "默认使用说明")
        
        # ===== 天气API配置 =====
        # 从配置文件读取API URL，保持与卡片API相同的配置逻辑
        self.api_config = config.get("api", {})
        self.api_url = self.api_config.get("url", "http://182.92.124.103:7891/process/")
        self.api_timeout = self.api_config.get("timeout", 10)
        

        
        # 天气卡片API配置
        self.card_api_config = config.get("card_api", {})
        # 获取完整的API URL，不再拆分路径
        self.card_api_url = self.card_api_config.get("url", "http://1.95.39.48:5000/api/weather")
        self.card_api_timeout = self.card_api_config.get("timeout", 10)
        self.card_width = self.card_api_config.get("width", 420)
        self.card_height = self.card_api_config.get("height", 0)
        self.card_scale = self.card_api_config.get("scale", 2.0)
        
        # ===== 百度天气API配置 =====
        self.baidu_api_config = config.get("baidu_api", {})
        self.baidu_api_url = self.baidu_api_config.get("url", "https://mini.s-shot.ru/1000/PNG/1000/")
        self.baidu_base_url = self.baidu_api_config.get("base_url", "https://weathernew.pae.baidu.com/weathernew/pc")
        self.baidu_api_timeout = self.baidu_api_config.get("timeout", 20)
        

        
        # 初始化令牌桶限流
        rate_limit_config = config.get("rate_limit", {})
        self.tokens_per_second = rate_limit_config.get("tokens_per_second", 0.1)
        self.bucket_size = rate_limit_config.get("bucket_size", 3)
        self.tokens = self.bucket_size
        self.last_token_time = time.time()
        
        # 用户限流字典
        self.user_last_request = {}
        

    
    @on_text_message
    async def handle_text(self, bot: WechatAPIClient, message: dict):
        """处理文本消息"""
        if not self.enable:
            return
            
        content = str(message["Content"]).strip()
        wxid = message["FromWxid"]
        user_wxid = message["SenderWxid"]
        
        # 检查是否为 "[地名]天气" 格式
        location_weather_match = re.search(r'^([^天气]+)天气$', content)
        use_baidu_api = False
        
        if location_weather_match:
            location = location_weather_match.group(1).strip()
            use_baidu_api = True
            logger.info(f"[{self.plugin_name}] 检测到 '[地名]天气' 格式请求，将使用百度天气API")
        else:
            # 检查是否是插件命令
            command_parts = content.split(" ", 1)
            if command_parts[0] not in self.command:
                return
                
            # 如果没有提供参数
            if len(command_parts) == 1:
                await bot.send_at_message(
                    wxid,
                    self.command_format,
                    [user_wxid]
                )
                return
            
            location = command_parts[1].strip()

        try:
            # 检查用户请求限制
            wait_time = self._check_user_limit(wxid, user_wxid)
            if wait_time > 0:
                await bot.send_at_message(
                    wxid,
                    f"慢点慢点，等 {wait_time:.1f} 秒再来",
                    [user_wxid]
                )
                return
                
            # 检查令牌桶
            if not await self._acquire_token():
                await bot.send_at_message(
                    wxid,
                    "忙不过来了，歇会儿",
                    [user_wxid]
                )
                return

            # 检查地名是否为空
            if not location:
                await bot.send_at_message(
                    wxid,
                    "❌ 请输入要查询的城市名称",
                    [user_wxid]
                )
                return
                
            # 发送提示消息
            await bot.send_at_message(
                wxid,
                f"🔍 正在查询 {location} 的天气，稍等...",
                [user_wxid]
            )

            logger.info(f"[{self.plugin_name}] 收到天气查询请求，用户: {user_wxid}, 城市: {location}")
            
            if use_baidu_api:
                # 使用百度天气API
                weather_image = await self._query_baidu_weather(location)
                
                if weather_image:
                    # 使用base64编码发送图片
                    image_base64 = base64.b64encode(weather_image).decode('utf-8')
                    
                    # 发送百度天气图片
                    logger.info(f"[{self.plugin_name}] 发送百度天气图片...")
                    result = await bot.send_image_message(
                        wxid,
                        image_base64
                    )
                    logger.info(f"[{self.plugin_name}] 百度天气图片发送完成")
                else:
                    logger.error(f"[{self.plugin_name}] 查询 {location} 天气失败，百度API未返回图片")
                    await bot.send_at_message(
                        wxid,
                        f"查不到 {location} 的天气，检查一下城市名字",
                        [user_wxid]
                    )
                    return
            else:
                # ==== 使用原有的天气API查询 ====
                weather_data = await self._query_weather(location)
                
                if not weather_data:
                    logger.error(f"[{self.plugin_name}] 查询 {location} 天气失败，API返回为空或无效")
                    await bot.send_at_message(
                        wxid,
                        f"查不到 {location} 的天气，检查一下城市名字",
                        [user_wxid]
                    )
                    return
                    
                # 获取天气信息
                if weather_data.get("code") == 200:
                    # 生成天气卡片
                    try:
                        logger.info(f"[{self.plugin_name}] 开始生成天气卡片...")
                        # 生成天气卡片图片
                        card_image = await self._generate_weather_card(weather_data, location)
                        
                        if card_image:
                            logger.info(f"[{self.plugin_name}] 天气卡片生成成功，大小: {len(card_image)} 字节")
                            
                            # 使用base64编码发送图片
                            image_base64 = base64.b64encode(card_image).decode('utf-8')
                            
                            # 发送天气卡片图片
                            logger.info(f"[{self.plugin_name}] 发送天气卡片图片...")
                            result = await bot.send_image_message(
                                wxid,
                                image_base64
                            )
                            logger.info(f"[{self.plugin_name}] 天气卡片发送完成")
                        else:
                            # 图片生成失败，直接发送文本天气信息
                            logger.warning(f"[{self.plugin_name}] 天气卡片生成失败，发送文本天气信息")
                            weather_text = weather_data.get("msg", "")
                            await bot.send_at_message(
                                wxid,
                                f"{weather_text}",
                                [user_wxid]
                            )
                    except Exception as e:
                        logger.error(f"[{self.plugin_name}] 生成天气卡片异常: {str(e)}", exc_info=True)
                        # 发生异常时发送文本天气信息
                        weather_text = weather_data.get("msg", "")
                        await bot.send_at_message(
                            wxid,
                            f"{weather_text}",
                            [user_wxid]
                        )
                else:
                    err_msg = weather_data.get("msg", "查询天气失败")
                    logger.error(f"[{self.plugin_name}] 查询天气API返回错误: {weather_data}")
                    await bot.send_at_message(
                        wxid,
                        f"❌ {err_msg}",
                        [user_wxid]
                    )
                
        except Exception as e:
            logger.error(f"[{self.plugin_name}] 处理异常: {str(e)}", exc_info=True)
            await bot.send_at_message(
                wxid,
                "出问题了，等会再试试",
                [user_wxid]
            )
            
    async def _query_baidu_weather(self, location: str) -> Optional[bytes]:
        """
        查询百度天气API并获取天气图片
        
        :param location: 城市名称
        :return: 图片二进制数据或None
        """
        try:
            # 对地名进行URL编码
            encoded_location = urllib.parse.quote(f"{location}天气")
            
            # 构建百度天气查询URL - 直接使用硬编码确保格式一致
            baidu_query_url = f"https://weathernew.pae.baidu.com/weathernew/pc?query={encoded_location}&srcid=4982&forecast=long_day_forecast"
            
            # 完整URL直接构建，不再使用额外的编码
            full_url = f"https://mini.s-shot.ru/1000/PNG/1000/?{baidu_query_url}"
            
            logger.info(f"[{self.plugin_name}] 百度天气完整URL: {full_url}")
            
            # 生成临时文件路径
            timestamp = int(time.time())
            output_path = self.temp_dir / f"baidu_weather_{timestamp}.png"
            
            # 下载截图
            async with httpx.AsyncClient() as client:
                response = await client.get(
                    full_url,
                    timeout=self.baidu_api_timeout
                )
                
                if response.status_code == 200:
                    # 检查是否是图片
                    content_type = response.headers.get("content-type", "")
                    if "image" in content_type:
                        # 保存图片
                        with open(output_path, "wb") as f:
                            f.write(response.content)
                        
                        logger.info(f"[{self.plugin_name}] 百度天气图片已保存到: {output_path}")
                        
                        # 读取并返回图片数据
                        with open(output_path, "rb") as f:
                            return f.read()
                    else:
                        logger.error(f"[{self.plugin_name}] 返回内容不是图片: {content_type}")
                else:
                    logger.error(f"[{self.plugin_name}] 请求百度天气截图失败: {response.status_code}")
                    logger.error(f"[{self.plugin_name}] 错误内容: {response.text[:200]}")
            
            return None
            
        except Exception as e:
            logger.error(f"[{self.plugin_name}] 查询百度天气异常: {str(e)}", exc_info=True)
            return None
    
    async def _query_weather(self, location: str) -> Dict[str, Any]:
        """
        查询天气信息
        
        :param location: 城市名称
        :return: 天气数据或None
        """
        try:
            # 构建请求参数 - 新API格式
            params = {
                "content": f"{location}+天气"
            }
            
            # 使用从配置读取的API URL
            logger.info(f"[{self.plugin_name}] 使用天气API查询: {self.api_url}")
            logger.debug(f"[{self.plugin_name}] 天气API参数: {params}")
            
            # 发送请求
            async with httpx.AsyncClient() as client:
                response = await client.get(
                    self.api_url,  # 使用实例变量，从配置读取的URL
                    params=params,
                    timeout=self.api_timeout
                )
                
                # 检查响应状态码
                response.raise_for_status()
                
                # 解析JSON响应
                weather_data = response.json()
                logger.info(f"[{self.plugin_name}] 天气API响应: {weather_data}")
                
                # 检查天气API返回的内容是否有效
                if weather_data.get("code") != 200:
                    logger.error(f"[{self.plugin_name}] 天气API返回错误: {weather_data}")
                    return None
                
                # 验证天气数据字段是否存在且有效 - 使用msg字段
                msg = weather_data.get("msg", "")
                if not msg or len(msg) < 10:
                    logger.error(f"[{self.plugin_name}] 天气API返回内容可能无效: {msg[:50] if msg else '空'}")
                    return None
                
                return weather_data
                
        except httpx.RequestError as e:
            logger.error(f"[{self.plugin_name}] 网络请求错误: {e}")
        except httpx.HTTPStatusError as e:
            logger.error(f"[{self.plugin_name}] HTTP状态错误: {e}")
        except Exception as e:
            logger.error(f"[{self.plugin_name}] 查询天气异常: {e}", exc_info=True)
            
        return None

    async def _generate_weather_card(self, weather_data: Dict[str, Any], location: str = "") -> Optional[bytes]:
        """
        调用API生成天气卡片图片
        
        :param weather_data: 完整的天气API响应数据
        :param location: 原始查询的城市名称
        :return: 图片二进制数据或None
        """
        try:
            # 保存临时文件路径
            timestamp = int(time.time())
            output_path = self.temp_dir / f"weather_{timestamp}.png"
            
            # 使用API端点
            api_url = self.card_api_url
            
            logger.info(f"[{self.plugin_name}] 请求天气卡片API: {api_url}")
            
            # 检查weather_data是否有效
            if not weather_data or not isinstance(weather_data, dict):
                logger.error(f"[{self.plugin_name}] 传递给天气卡片API的数据无效")
                return None
                
            # 完全按照示例代码构建请求数据
            # API请求数据 - 直接使用天气API返回的完整数据
            payload = {
                "text": weather_data,  # 直接传递完整的天气API响应
                "width": self.card_width,
                "height": self.card_height,
                "scale": self.card_scale
            }
            
            # 添加更详细的日志记录
            logger.info(f"[{self.plugin_name}] 发送请求到 {api_url} 端点，超时设置: {self.card_api_timeout}秒")
            logger.debug(f"[{self.plugin_name}] 天气卡片请求参数: width={self.card_width}, height={self.card_height}, scale={self.card_scale}")
            logger.debug(f"[{self.plugin_name}] 传递完整的天气API响应数据给卡片API")
            
            # 发送请求
            async with httpx.AsyncClient() as client:
                try:
                    logger.info(f"[{self.plugin_name}] 开始发送API请求...")
                    start_time = time.time()
                    
                    response = await client.post(
                        api_url,
                        json=payload,
                        timeout=self.card_api_timeout
                    )
                    
                    elapsed = time.time() - start_time
                    logger.info(f"[{self.plugin_name}] API请求完成，耗时: {elapsed:.2f}秒，响应状态码: {response.status_code}")
                    logger.debug(f"[{self.plugin_name}] API响应头: {response.headers}")
                    
                    if response.status_code == 200:
                        content_type = response.headers.get("content-type", "")
                        logger.info(f"[{self.plugin_name}] 响应内容类型: {content_type}")
                        
                        # 检查是否返回图片
                        if "image" in content_type:
                            # 保存图片
                            with open(output_path, "wb") as f:
                                f.write(response.content)
                            
                            logger.info(f"[{self.plugin_name}] 天气卡片已保存到: {output_path}")
                            
                            # 读取并返回图片数据
                            with open(output_path, "rb") as f:
                                return f.read()
                        else:
                            logger.error(f"[{self.plugin_name}] 响应不是图片: {response.text[:200]}")
                    else:
                        logger.error(f"[{self.plugin_name}] API请求失败: {response.status_code}")
                        logger.error(f"[{self.plugin_name}] 错误信息: {response.text[:200]}")
                        
                        # 不再尝试备用API端点
                        
                except Exception as e:
                    logger.error(f"[{self.plugin_name}] 请求异常: {str(e)}")
            
            return None
                
        except Exception as e:
            logger.error(f"[{self.plugin_name}] 生成天气卡片失败: {str(e)}", exc_info=True)
            return None

    async def _acquire_token(self) -> bool:
        """尝试获取令牌桶中的令牌"""
        current_time = time.time()
        time_elapsed = current_time - self.last_token_time
        self.last_token_time = current_time
        
        # 添加新令牌
        self.tokens = min(self.bucket_size, self.tokens + time_elapsed * self.tokens_per_second)
        
        # 尝试获取令牌
        if self.tokens < 1:
            return False
            
        self.tokens -= 1
        return True
        
    def _check_user_limit(self, wxid: str, user_wxid: str) -> float:
        """检查用户请求限制，返回需要等待的时间（秒）"""
        # 创建用户标识符 (群+用户ID)
        user_key = f"{wxid}_{user_wxid}"
        
        # 获取用户上次请求时间
        last_request = self.user_last_request.get(user_key, 0)
        current_time = time.time()
        
        # 计算冷却时间 (5秒)
        cooldown = 5
        elapsed = current_time - last_request
        
        # 更新最后请求时间
        self.user_last_request[user_key] = current_time
        
        # 如果冷却时间未到，返回需要等待的时间
        if elapsed < cooldown:
            return cooldown - elapsed
            
        return 0 