2025-07-21 18:52:33 | SUCCESS | 读取主设置成功
2025-07-21 18:52:33 | DEBUG | 最终使用的 Redis 主机地址: 127.0.0.1
2025-07-21 18:52:33 | INFO | 2025/07/21 18:52:33 GetRedisAddr: 127.0.0.1:6379
2025-07-21 18:52:33 | INFO | 2025/07/21 18:52:33 Redis连接成功 | 地址:127.0.0.1:6379 DB:0
2025-07-21 18:52:33 | INFO | 2025/07/21 18:52:33 Server start at :9000
2025-07-21 18:52:34 | SUCCESS | WechatAPI服务已启动
2025-07-21 18:52:34 | INFO | 登录账号信息: wxid: wxid_4usgcju5ey9q29  昵称: 瑶瑶  微信号: Today-KFC  手机号: None
2025-07-21 18:52:34 | INFO | 登录设备信息: device_name: <PERSON>'s Pad  device_id: 49d050a7e94d1ecc40790d74b1c4369f
2025-07-21 18:52:34 | SUCCESS | 登录成功
2025-07-21 18:52:34 | SUCCESS | 已开启自动心跳
2025-07-21 18:52:34 | INFO | 成功加载表情映射文件，共 521 条记录
2025-07-21 18:52:34 | SUCCESS | 数据库初始化成功
2025-07-21 18:52:34 | SUCCESS | 定时任务已启动
2025-07-21 18:52:34 | SUCCESS | 插件 AISignIn 已加载定时任务: {'plugins.AISignIn.main.AISignIn.auto_signin_task'}
2025-07-21 18:52:34 | INFO | 已加载管理员列表: ['wxid_ubbh6q832tcs21', 'admin-wxid']
2025-07-21 18:52:35 | INFO | 播客API初始化成功
2025-07-21 18:52:35 | INFO | 加载配置完成，转换后的数据: {'gh_7057516c9e71': {'name': '唱舞全明星', 'groups': ['27852221909@chatroom', '55878994168@chatroom', '51891329927@chatroom']}, 'gh_d4d21a0e4623': {'name': '两颗桃', 'groups': ['48097389945@chatroom']}, 'gh_410ff2fa6ec9': {'name': 'NGC660 Ai研究院', 'groups': ['47442567074@chatroom']}, 'gh_f78c53c507c0': {'name': '不正常吐槽君', 'groups': ['48097389945@chatroom']}, 'gh_ba3381c847b3': {'name': '唱舞星计划', 'groups': ['51891329927@chatroom']}, 'gh_1a102fb4cad9': {'name': '我在娱乐圈的唯一人脉', 'groups': ['48097389945@chatroom', '27852221909@chatroom']}}
2025-07-21 18:52:35 | INFO | ArticleForwarder插件初始化完成 - 监控配置: {'gh_7057516c9e71': {'name': '唱舞全明星', 'groups': ['27852221909@chatroom', '55878994168@chatroom', '51891329927@chatroom']}, 'gh_d4d21a0e4623': {'name': '两颗桃', 'groups': ['48097389945@chatroom']}, 'gh_410ff2fa6ec9': {'name': 'NGC660 Ai研究院', 'groups': ['47442567074@chatroom']}, 'gh_f78c53c507c0': {'name': '不正常吐槽君', 'groups': ['48097389945@chatroom']}, 'gh_ba3381c847b3': {'name': '唱舞星计划', 'groups': ['51891329927@chatroom']}, 'gh_1a102fb4cad9': {'name': '我在娱乐圈的唯一人脉', 'groups': ['48097389945@chatroom', '27852221909@chatroom']}}
2025-07-21 18:52:35 | DEBUG | [TempFileManager] 添加清理规则: default
2025-07-21 18:52:35 | DEBUG | [TempFileManager] 添加清理规则: images
2025-07-21 18:52:35 | DEBUG | [TempFileManager] 添加清理规则: videos
2025-07-21 18:52:35 | DEBUG | [TempFileManager] 添加清理规则: audio
2025-07-21 18:52:35 | DEBUG | [TempFileManager] 添加清理规则: temp
2025-07-21 18:52:35 | DEBUG | [TempFileManager] 添加清理规则: large_files
2025-07-21 18:52:35 | INFO | [TempFileManager] 临时文件清理任务已启动
2025-07-21 18:52:35 | INFO | [ChatSummary] 数据库初始化成功
2025-07-21 18:52:36 | INFO | 成功加载表情映射文件，共 521 条记录
2025-07-21 18:52:36 | SUCCESS | 插件 GoodMorning 已加载定时任务: {'plugins.GoodMorning.main.GoodMorning.daily_task'}
2025-07-21 18:52:36 | SUCCESS | 插件 News 已加载定时任务: {'plugins.News.main.News.night_news', 'plugins.News.main.News.noon_news'}
2025-07-21 18:52:36 | INFO | [PatReply] AMR文件已存在且是最新的: C:\XYBotV2\data\paiyipai\1_BFB5258B-2B13-4CF4-A40C-5BCCE74E5414_converted.amr
2025-07-21 18:52:36 | DEBUG | [PatReply] 从缓存加载base64: 18228 字符
2025-07-21 18:52:36 | SUCCESS | 插件 QuarkSignIn 已加载定时任务: {'plugins.QuarkSignIn.main.QuarkSignIn.auto_signin_task'}
2025-07-21 18:52:36 | INFO | [QuarkSignIn] 已设置自动签到时间: 08:00
2025-07-21 18:52:36 | INFO | 插件状态: 启用, 检查间隔: 3600秒, 最大重试次数: 3, 重试延迟: 5秒
2025-07-21 18:52:36 | INFO | 已加载管理员列表: ['wxid_ubbh6q832tcs21', 'admin-wxid']
2025-07-21 18:52:36 | INFO | 已加载 2 个改名监控群和 1 个退群监控群
2025-07-21 18:52:36 | INFO | [RenameReminder] 开始启用插件...
2025-07-21 18:52:36 | SUCCESS | 插件 RenameReminder 已加载定时任务: {'plugins.RenameReminder.main.RenameReminder.check_name_changes'}
2025-07-21 18:52:36 | INFO | [RenameReminder] 插件已启用，开始初始化...
2025-07-21 18:52:36 | INFO | [RenameReminder] 支持的命令: 开启改名提醒, 关闭改名提醒, 开启退群提醒, 关闭退群提醒, 查看提醒状态
2025-07-21 18:52:36 | INFO | 已设置检查间隔为 3600 秒
2025-07-21 18:52:36 | INFO | 已加载 2 个监控群，开始更新成员列表
2025-07-21 18:52:36 | DEBUG | 已更新群 27852221909@chatroom 的成员列表
2025-07-21 18:52:37 | DEBUG | 已更新群 51891329927@chatroom 的成员列表
2025-07-21 18:52:37 | SUCCESS | 插件 TimerTask 已加载定时任务: {'plugins.TimerTask.main.TimerTask.check_tasks'}
2025-07-21 18:52:37 | INFO | [VideoDemand] 加载了 128 个视频类别配置
2025-07-21 18:52:37 | INFO | [YaoyaoPlugin] 私聊黑名单用户: ['gh_a5cf07d474f1', 'gh_ba3381c847b3']
2025-07-21 18:52:37 | INFO | [YaoyaoPlugin] 已加载管理员列表: ['wxid_ubbh6q832tcs21', 'admin-wxid']
2025-07-21 18:52:37 | INFO | [yuanbao] 插件初始化完成
2025-07-21 18:52:37 | DEBUG | [yuanbao] 指令: ['元宝', 'yuanbao']
2025-07-21 18:52:37 | DEBUG | [yuanbao] 绘图模型: gpt_175B_0404
2025-07-21 18:52:37 | DEBUG | [yuanbao] 认证信息加载状态: hy_token=True, hy_user=True, agent_id=True, x_uskey=True
2025-07-21 18:52:37 | SUCCESS | 已加载插件: ['AdminPoint', 'AdminSignInReset', 'AdminWhitelist', 'AISignIn', 'AppMessageTester', 'ArticleForwarder', 'BaiduAgentsPlugin', 'BaiduDraw', 'BotStatus', 'ChatSummary', 'DanceSignInPlugin', 'DeepseekPlugin', 'Doubao', 'DoubaoDrawing', 'DoubaoImageRecognition', 'DouBaoImageToImage', 'DouBaoImageToVideo', 'DoubaoVideoSearch', 'DouHui', 'EmojiTestPlugin', 'FixedQuoteTest', 'GetContact', 'Gomoku', 'GoodMorning', 'HunyuanDraw', 'ImageEcho', 'ImageToImage', 'JiemengDraw', 'KeLingImageToImage', 'KlingAI', 'Leaderboard', 'LuckyDraw', 'ManagePlugin', 'Menu', 'MiniProgramTester', 'Music', 'News', 'PatReply', 'PointTrade', 'QuarkSignIn', 'QueryPoint', 'RandomMember', 'RandomPicture', 'RenameReminder', 'RevokePlugin', 'RoboNeo', 'SignIn', 'TempFileManagerPlugin', 'TencentLke', 'TimerTask', 'TongyiDraw', 'URLShortener', 'VideoDemand', 'VideoParserPlugin', 'VideoTest', 'VivoAgentsPlugin', 'VoiceTest', 'Warthunder', 'WeatherQuery', 'WelcomePlugin', 'Xunfei', 'YaoyaoPlugin', 'Yuanbao', '抽签', '造梦次元']
2025-07-21 18:52:37 | INFO | 处理堆积消息中
2025-07-21 18:52:38 | DEBUG | 接受到 1 条消息
2025-07-21 18:52:39 | SUCCESS | 处理堆积消息完毕
2025-07-21 18:52:39 | SUCCESS | 开始处理消息
2025-07-21 18:53:11 | DEBUG | 收到消息: {'MsgId': 20113678, 'FromUserName': {'string': '55878994168@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 1, 'Content': {'string': 'wxid_ubbh6q832tcs21:\n搜歌 起风了'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1753095201, 'MsgSource': '<msgsource>\n\t<pua>1</pua>\n\t<eggIncluded>1</eggIncluded>\n\t<silence>0</silence>\n\t<membercount>3</membercount>\n\t<signature>N0_V1_KXxzQqkH|v1_tlkhz959</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'PushContent': '郭子 : 搜歌 起风了', 'NewMsgId': 4195492638017823434, 'MsgSeq': 871388477}
2025-07-21 18:53:11 | INFO | 收到文本消息: 消息ID:20113678 来自:55878994168@chatroom 发送人:wxid_ubbh6q832tcs21 @:[] 内容:搜歌 起风了
2025-07-21 18:53:13 | INFO | 发送app消息: 对方wxid:55878994168@chatroom 类型:19 xml:<appmsg appid="" sdkver="0"><title>🎵 起风了 - 搜索结果</title><des>🎵 音乐助手: 为您找到 "起风了" 的相关歌曲：🎶 搜索结果: 1、起风了 (Live) -- 黄誉博🎶 搜索结果: 2、起风了 -- 买辣椒也用券...</des><action>view</action><type>19</type><showtype>0</showtype><content/><url>https://support.weixin.qq.com/cgi-bin/mmsupport-bin/readtemplate?t=page/favorite_record__w_unsupport&amp;from=singlemessage&amp;isappinstalled=0</url><lowurl/><dataurl/><lowdataurl/><recorditem><![CDATA[<recordinfo><title>🎵 音乐搜索结果</title><desc>🎵 音乐助手:&#x20;为您找到 "起风了" 的相关歌曲：&#x0A;🎶 搜索结果:&#x20;1、起风了 (Live) -- 黄誉博&#x0A;🎶 搜索结果:&#x20;2、起风了 -- 买辣椒也用券&#x0A;🎶 搜索结果:&#x20;3、起风了 -- 周深&#x0A;🎶 搜索结果:&#x20;4、起风了 -- 买辣椒也用券&#x0A;🎶 搜索结果:&#x20;5、起风了 (Live) -- 林俊杰&#x0A;🎶 搜索结果:&#x20;6、起风了 (DJ旋律) -- 寻梦、Mukyo木西&#x0A;🎶 搜索结果:&#x20;7、起风了 -- 吴青峰&#x0A;🎶 搜索结果:&#x20;8、起风了 (2019歌手第三期现场) -- 吴青峰&#x0A;💡 使用提示:&#x20;发送 "听1" 到 "听8" 选择对应歌曲</desc><datalist count="10"><dataitem datatype="1" dataid="ee80e36b8a1aa9a05e02939b1d67dbc2" datasourceid="1012873447205704116"><datadesc>为您找到 "起风了" 的相关歌曲：</datadesc><sourcename>🎵 音乐助手</sourcename><sourceheadurl>https://wx.qlogo.cn/mmhead/ver_1/icq2icXbhxGESInLwSiaTeZFSiaic8CXB3obiaQMoGVOtXeial73slIhfQ7w1KhFjfDZecibrNYjEOlKWicOzRVWoTmQrHB3xOWpQESaZ91d5fbjfNz99iaictzSnYNl6nddd5wXwY0E3zPKq4NA3EOLqJyrzPAtQ/132</sourceheadurl><sourcetime>2025-07-21&#x20;12:00:00</sourcetime><srcMsgCreateTime>1753095192</srcMsgCreateTime><fromnewmsgid>1012873447205704116</fromnewmsgid><dataitemsource><hashusername>47117a242116be9ab5e02f7331cd04041140f0caf32a9bedff8b44eb7e0c1cb7</hashusername></dataitemsource></dataitem><dataitem datatype="1" dataid="88a0f08f700d7956c330a087000f776a" datasourceid="8814895712125313418"><datadesc>1、起风了 (Live) -- 黄誉博</datadesc><sourcename>🎶 搜索结果</sourcename><sourceheadurl>https://wx.qlogo.cn/mmhead/ver_1/PCUwVKYvh0icQrEmB5CiaoyoPaAY9X93CtB0OeA6QmOUml3ljscUgvL3rVRwD2iacibn6oDaaA7xzmibrmt9ibcaNR27Sl8sawD9ZUDgAenibnicpoecdGEaRubkMkImzibwjoddx/96</sourceheadurl><sourcetime>2025-07-21&#x20;12:01:00</sourcetime><srcMsgCreateTime>1753095222</srcMsgCreateTime><fromnewmsgid>8814895712125313418</fromnewmsgid><dataitemsource><hashusername>e6b73ff27565cff370109f8fcda0e65fdab070d2463b159472ee7e7f54f7d313</hashusername></dataitemsource></dataitem><dataitem datatype="1" dataid="ff0e6234426dbb3733bcca32d718c175" datasourceid="8245956230036614448"><datadesc>2、起风了 -- 买辣椒也用券</datadesc><sourcename>🎶 搜索结果</sourcename><sourceheadurl>https://wx.qlogo.cn/mmhead/ver_1/PCUwVKYvh0icQrEmB5CiaoyoPaAY9X93CtB0OeA6QmOUml3ljscUgvL3rVRwD2iacibn6oDaaA7xzmibrmt9ibcaNR27Sl8sawD9ZUDgAenibnicpoecdGEaRubkMkImzibwjoddx/96</sourceheadurl><sourcetime>2025-07-21&#x20;12:02:00</sourcetime><srcMsgCreateTime>1753095252</srcMsgCreateTime><fromnewmsgid>8245956230036614448</fromnewmsgid><dataitemsource><hashusername>e6b73ff27565cff370109f8fcda0e65fdab070d2463b159472ee7e7f54f7d313</hashusername></dataitemsource></dataitem><dataitem datatype="1" dataid="b320cc2672196de52f8f64efb1af0a02" datasourceid="9480729589856516015"><datadesc>3、起风了 -- 周深</datadesc><sourcename>🎶 搜索结果</sourcename><sourceheadurl>https://wx.qlogo.cn/mmhead/ver_1/PCUwVKYvh0icQrEmB5CiaoyoPaAY9X93CtB0OeA6QmOUml3ljscUgvL3rVRwD2iacibn6oDaaA7xzmibrmt9ibcaNR27Sl8sawD9ZUDgAenibnicpoecdGEaRubkMkImzibwjoddx/96</sourceheadurl><sourcetime>2025-07-21&#x20;12:03:00</sourcetime><srcMsgCreateTime>1753095282</srcMsgCreateTime><fromnewmsgid>9480729589856516015</fromnewmsgid><dataitemsource><hashusername>e6b73ff27565cff370109f8fcda0e65fdab070d2463b159472ee7e7f54f7d313</hashusername></dataitemsource></dataitem><dataitem datatype="1" dataid="6dcaebb12a2eaedc8021891ade1266c8" datasourceid="8469942873246555126"><datadesc>4、起风了 -- 买辣椒也用券</datadesc><sourcename>🎶 搜索结果</sourcename><sourceheadurl>https://wx.qlogo.cn/mmhead/ver_1/PCUwVKYvh0icQrEmB5CiaoyoPaAY9X93CtB0OeA6QmOUml3ljscUgvL3rVRwD2iacibn6oDaaA7xzmibrmt9ibcaNR27Sl8sawD9ZUDgAenibnicpoecdGEaRubkMkImzibwjoddx/96</sourceheadurl><sourcetime>2025-07-21&#x20;12:04:00</sourcetime><srcMsgCreateTime>1753095312</srcMsgCreateTime><fromnewmsgid>8469942873246555126</fromnewmsgid><dataitemsource><hashusername>e6b73ff27565cff370109f8fcda0e65fdab070d2463b159472ee7e7f54f7d313</hashusername></dataitemsource></dataitem><dataitem datatype="1" dataid="cc9cae54991ceb69f39ebc857a9b14c9" datasourceid="9159056078084457740"><datadesc>5、起风了 (Live) -- 林俊杰</datadesc><sourcename>🎶 搜索结果</sourcename><sourceheadurl>https://wx.qlogo.cn/mmhead/ver_1/PCUwVKYvh0icQrEmB5CiaoyoPaAY9X93CtB0OeA6QmOUml3ljscUgvL3rVRwD2iacibn6oDaaA7xzmibrmt9ibcaNR27Sl8sawD9ZUDgAenibnicpoecdGEaRubkMkImzibwjoddx/96</sourceheadurl><sourcetime>2025-07-21&#x20;12:05:00</sourcetime><srcMsgCreateTime>1753095342</srcMsgCreateTime><fromnewmsgid>9159056078084457740</fromnewmsgid><dataitemsource><hashusername>e6b73ff27565cff370109f8fcda0e65fdab070d2463b159472ee7e7f54f7d313</hashusername></dataitemsource></dataitem><dataitem datatype="1" dataid="4aee239dc629bd7c7328481cfb33f760" datasourceid="7985438413595354896"><datadesc>6、起风了 (DJ旋律) -- 寻梦、Mukyo木西</datadesc><sourcename>🎶 搜索结果</sourcename><sourceheadurl>https://wx.qlogo.cn/mmhead/ver_1/PCUwVKYvh0icQrEmB5CiaoyoPaAY9X93CtB0OeA6QmOUml3ljscUgvL3rVRwD2iacibn6oDaaA7xzmibrmt9ibcaNR27Sl8sawD9ZUDgAenibnicpoecdGEaRubkMkImzibwjoddx/96</sourceheadurl><sourcetime>2025-07-21&#x20;12:06:00</sourcetime><srcMsgCreateTime>1753095372</srcMsgCreateTime><fromnewmsgid>7985438413595354896</fromnewmsgid><dataitemsource><hashusername>e6b73ff27565cff370109f8fcda0e65fdab070d2463b159472ee7e7f54f7d313</hashusername></dataitemsource></dataitem><dataitem datatype="1" dataid="a8ca887d5fc2adb2537272c45b28bd37" datasourceid="4342950196755621742"><datadesc>7、起风了 -- 吴青峰</datadesc><sourcename>🎶 搜索结果</sourcename><sourceheadurl>https://wx.qlogo.cn/mmhead/ver_1/PCUwVKYvh0icQrEmB5CiaoyoPaAY9X93CtB0OeA6QmOUml3ljscUgvL3rVRwD2iacibn6oDaaA7xzmibrmt9ibcaNR27Sl8sawD9ZUDgAenibnicpoecdGEaRubkMkImzibwjoddx/96</sourceheadurl><sourcetime>2025-07-21&#x20;12:07:00</sourcetime><srcMsgCreateTime>1753095402</srcMsgCreateTime><fromnewmsgid>4342950196755621742</fromnewmsgid><dataitemsource><hashusername>e6b73ff27565cff370109f8fcda0e65fdab070d2463b159472ee7e7f54f7d313</hashusername></dataitemsource></dataitem><dataitem datatype="1" dataid="a672e90d69f5fa6651911e1702cd093f" datasourceid="8016600326773976051"><datadesc>8、起风了 (2019歌手第三期现场) -- 吴青峰</datadesc><sourcename>🎶 搜索结果</sourcename><sourceheadurl>https://wx.qlogo.cn/mmhead/ver_1/PCUwVKYvh0icQrEmB5CiaoyoPaAY9X93CtB0OeA6QmOUml3ljscUgvL3rVRwD2iacibn6oDaaA7xzmibrmt9ibcaNR27Sl8sawD9ZUDgAenibnicpoecdGEaRubkMkImzibwjoddx/96</sourceheadurl><sourcetime>2025-07-21&#x20;12:08:00</sourcetime><srcMsgCreateTime>1753095432</srcMsgCreateTime><fromnewmsgid>8016600326773976051</fromnewmsgid><dataitemsource><hashusername>e6b73ff27565cff370109f8fcda0e65fdab070d2463b159472ee7e7f54f7d313</hashusername></dataitemsource></dataitem><dataitem datatype="1" dataid="f718b82b4c608a2de84fe607c43d312c" datasourceid="4615928415534206777"><datadesc>发送 "听1" 到 "听8" 选择对应歌曲</datadesc><sourcename>💡 使用提示</sourcename><sourceheadurl>https://wx.qlogo.cn/mmhead/ver_1/rTPg4GeXEic6oQSZ2jcAI6ic6JEhFtYT40Eu3rUIjZpTKTATSgCEiaMJlUuFibkfxN4IpDCv0qxCA5WgowqT0BGRTQAVeDTn1PTGj1LUYbl2bRuJiaCb1ukH4e1TPIibyUiaX6hFKgria9aX7NM5SJFHDq3pBA/132</sourceheadurl><sourcetime>2025-07-21&#x20;12:09:00</sourcetime><srcMsgCreateTime>1753095462</srcMsgCreateTime><fromnewmsgid>4615928415534206777</fromnewmsgid><dataitemsource><hashusername>8f57acad5c854c4004fd00435c305ac427418e4ac2f7816798d3db2db6eff1eb</hashusername></dataitemsource></dataitem></datalist><favcreatetime>1753095192790</favcreatetime></recordinfo>]]></recorditem><appattach>    <totallen>0</totallen>    <attachid/>    <emoticonmd5/>    <fileext/>    <aeskey/></appattach><extinfo/><sourceusername/><sourcedisplayname/><thumburl/><md5/></appmsg><fromusername>wxid_4usgcju5ey9q29</fromusername><scene>0</scene><appinfo>    <version>1</version>    <appname></appname></appinfo><commenturl/>
2025-07-21 18:53:13 | DEBUG | 处理消息内容: '搜歌 起风了'
2025-07-21 18:53:13 | DEBUG | 消息内容 '搜歌 起风了' 不匹配任何命令，忽略
2025-07-21 18:53:53 | DEBUG | 收到消息: {'MsgId': 763596693, 'FromUserName': {'string': '47442567074@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 1, 'Content': {'string': 'wxid_ubbh6q832tcs21:\n搜歌 起风了'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1753095242, 'MsgSource': '<msgsource>\n\t<pua>1</pua>\n\t<eggIncluded>1</eggIncluded>\n\t<silence>0</silence>\n\t<membercount>495</membercount>\n\t<signature>N0_V1_5OEvI1bs|v1_pv0GwwGp</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'PushContent': '郭 : 搜歌 起风了', 'NewMsgId': 7960922161421216136, 'MsgSeq': 871388480}
2025-07-21 18:53:53 | INFO | 收到文本消息: 消息ID:763596693 来自:47442567074@chatroom 发送人:wxid_ubbh6q832tcs21 @:[] 内容:搜歌 起风了
2025-07-21 18:53:54 | INFO | 发送app消息: 对方wxid:47442567074@chatroom 类型:19 xml:<appmsg appid="" sdkver="0"><title>🎵 起风了 - 搜索结果</title><des>🎵 音乐助手: 为您找到 "起风了" 的相关歌曲：🎶 搜索结果: 1、起风了 (Live) -- 黄誉博🎶 搜索结果: 2、起风了 -- 买辣椒也用券...</des><action>view</action><type>19</type><showtype>0</showtype><content/><url>https://support.weixin.qq.com/cgi-bin/mmsupport-bin/readtemplate?t=page/favorite_record__w_unsupport&amp;from=singlemessage&amp;isappinstalled=0</url><lowurl/><dataurl/><lowdataurl/><recorditem><![CDATA[<recordinfo><title>🎵 音乐搜索结果</title><desc>🎵 音乐助手:&#x20;为您找到 "起风了" 的相关歌曲：&#x0A;🎶 搜索结果:&#x20;1、起风了 (Live) -- 黄誉博&#x0A;🎶 搜索结果:&#x20;2、起风了 -- 买辣椒也用券&#x0A;🎶 搜索结果:&#x20;3、起风了 -- 周深&#x0A;🎶 搜索结果:&#x20;4、起风了 -- 买辣椒也用券&#x0A;🎶 搜索结果:&#x20;5、起风了 (Live) -- 林俊杰&#x0A;🎶 搜索结果:&#x20;6、起风了 (DJ旋律) -- 寻梦、Mukyo木西&#x0A;🎶 搜索结果:&#x20;7、起风了 -- 吴青峰&#x0A;🎶 搜索结果:&#x20;8、起风了 (2019歌手第三期现场) -- 吴青峰&#x0A;💡 使用提示:&#x20;发送 "听1" 到 "听8" 选择对应歌曲</desc><datalist count="10"><dataitem datatype="1" dataid="ee80e36b8a1aa9a05e02939b1d67dbc2" datasourceid="2239821933475148268"><datadesc>为您找到 "起风了" 的相关歌曲：</datadesc><sourcename>🎵 音乐助手</sourcename><sourceheadurl>https://wx.qlogo.cn/mmhead/ver_1/icq2icXbhxGESInLwSiaTeZFSiaic8CXB3obiaQMoGVOtXeial73slIhfQ7w1KhFjfDZecibrNYjEOlKWicOzRVWoTmQrHB3xOWpQESaZ91d5fbjfNz99iaictzSnYNl6nddd5wXwY0E3zPKq4NA3EOLqJyrzPAtQ/132</sourceheadurl><sourcetime>2025-07-21&#x20;12:00:00</sourcetime><srcMsgCreateTime>1753095234</srcMsgCreateTime><fromnewmsgid>2239821933475148268</fromnewmsgid><dataitemsource><hashusername>47117a242116be9ab5e02f7331cd04041140f0caf32a9bedff8b44eb7e0c1cb7</hashusername></dataitemsource></dataitem><dataitem datatype="1" dataid="88a0f08f700d7956c330a087000f776a" datasourceid="8292796891126441241"><datadesc>1、起风了 (Live) -- 黄誉博</datadesc><sourcename>🎶 搜索结果</sourcename><sourceheadurl>https://wx.qlogo.cn/mmhead/ver_1/PCUwVKYvh0icQrEmB5CiaoyoPaAY9X93CtB0OeA6QmOUml3ljscUgvL3rVRwD2iacibn6oDaaA7xzmibrmt9ibcaNR27Sl8sawD9ZUDgAenibnicpoecdGEaRubkMkImzibwjoddx/96</sourceheadurl><sourcetime>2025-07-21&#x20;12:01:00</sourcetime><srcMsgCreateTime>1753095264</srcMsgCreateTime><fromnewmsgid>8292796891126441241</fromnewmsgid><dataitemsource><hashusername>e6b73ff27565cff370109f8fcda0e65fdab070d2463b159472ee7e7f54f7d313</hashusername></dataitemsource></dataitem><dataitem datatype="1" dataid="ff0e6234426dbb3733bcca32d718c175" datasourceid="7229721287288189649"><datadesc>2、起风了 -- 买辣椒也用券</datadesc><sourcename>🎶 搜索结果</sourcename><sourceheadurl>https://wx.qlogo.cn/mmhead/ver_1/PCUwVKYvh0icQrEmB5CiaoyoPaAY9X93CtB0OeA6QmOUml3ljscUgvL3rVRwD2iacibn6oDaaA7xzmibrmt9ibcaNR27Sl8sawD9ZUDgAenibnicpoecdGEaRubkMkImzibwjoddx/96</sourceheadurl><sourcetime>2025-07-21&#x20;12:02:00</sourcetime><srcMsgCreateTime>1753095294</srcMsgCreateTime><fromnewmsgid>7229721287288189649</fromnewmsgid><dataitemsource><hashusername>e6b73ff27565cff370109f8fcda0e65fdab070d2463b159472ee7e7f54f7d313</hashusername></dataitemsource></dataitem><dataitem datatype="1" dataid="b320cc2672196de52f8f64efb1af0a02" datasourceid="8758055319635186575"><datadesc>3、起风了 -- 周深</datadesc><sourcename>🎶 搜索结果</sourcename><sourceheadurl>https://wx.qlogo.cn/mmhead/ver_1/PCUwVKYvh0icQrEmB5CiaoyoPaAY9X93CtB0OeA6QmOUml3ljscUgvL3rVRwD2iacibn6oDaaA7xzmibrmt9ibcaNR27Sl8sawD9ZUDgAenibnicpoecdGEaRubkMkImzibwjoddx/96</sourceheadurl><sourcetime>2025-07-21&#x20;12:03:00</sourcetime><srcMsgCreateTime>1753095324</srcMsgCreateTime><fromnewmsgid>8758055319635186575</fromnewmsgid><dataitemsource><hashusername>e6b73ff27565cff370109f8fcda0e65fdab070d2463b159472ee7e7f54f7d313</hashusername></dataitemsource></dataitem><dataitem datatype="1" dataid="6dcaebb12a2eaedc8021891ade1266c8" datasourceid="7260702948374232220"><datadesc>4、起风了 -- 买辣椒也用券</datadesc><sourcename>🎶 搜索结果</sourcename><sourceheadurl>https://wx.qlogo.cn/mmhead/ver_1/PCUwVKYvh0icQrEmB5CiaoyoPaAY9X93CtB0OeA6QmOUml3ljscUgvL3rVRwD2iacibn6oDaaA7xzmibrmt9ibcaNR27Sl8sawD9ZUDgAenibnicpoecdGEaRubkMkImzibwjoddx/96</sourceheadurl><sourcetime>2025-07-21&#x20;12:04:00</sourcetime><srcMsgCreateTime>1753095354</srcMsgCreateTime><fromnewmsgid>7260702948374232220</fromnewmsgid><dataitemsource><hashusername>e6b73ff27565cff370109f8fcda0e65fdab070d2463b159472ee7e7f54f7d313</hashusername></dataitemsource></dataitem><dataitem datatype="1" dataid="cc9cae54991ceb69f39ebc857a9b14c9" datasourceid="9348743829585036452"><datadesc>5、起风了 (Live) -- 林俊杰</datadesc><sourcename>🎶 搜索结果</sourcename><sourceheadurl>https://wx.qlogo.cn/mmhead/ver_1/PCUwVKYvh0icQrEmB5CiaoyoPaAY9X93CtB0OeA6QmOUml3ljscUgvL3rVRwD2iacibn6oDaaA7xzmibrmt9ibcaNR27Sl8sawD9ZUDgAenibnicpoecdGEaRubkMkImzibwjoddx/96</sourceheadurl><sourcetime>2025-07-21&#x20;12:05:00</sourcetime><srcMsgCreateTime>1753095384</srcMsgCreateTime><fromnewmsgid>9348743829585036452</fromnewmsgid><dataitemsource><hashusername>e6b73ff27565cff370109f8fcda0e65fdab070d2463b159472ee7e7f54f7d313</hashusername></dataitemsource></dataitem><dataitem datatype="1" dataid="4aee239dc629bd7c7328481cfb33f760" datasourceid="8236872827075185009"><datadesc>6、起风了 (DJ旋律) -- 寻梦、Mukyo木西</datadesc><sourcename>🎶 搜索结果</sourcename><sourceheadurl>https://wx.qlogo.cn/mmhead/ver_1/PCUwVKYvh0icQrEmB5CiaoyoPaAY9X93CtB0OeA6QmOUml3ljscUgvL3rVRwD2iacibn6oDaaA7xzmibrmt9ibcaNR27Sl8sawD9ZUDgAenibnicpoecdGEaRubkMkImzibwjoddx/96</sourceheadurl><sourcetime>2025-07-21&#x20;12:06:00</sourcetime><srcMsgCreateTime>1753095414</srcMsgCreateTime><fromnewmsgid>8236872827075185009</fromnewmsgid><dataitemsource><hashusername>e6b73ff27565cff370109f8fcda0e65fdab070d2463b159472ee7e7f54f7d313</hashusername></dataitemsource></dataitem><dataitem datatype="1" dataid="a8ca887d5fc2adb2537272c45b28bd37" datasourceid="9102928092172252156"><datadesc>7、起风了 -- 吴青峰</datadesc><sourcename>🎶 搜索结果</sourcename><sourceheadurl>https://wx.qlogo.cn/mmhead/ver_1/PCUwVKYvh0icQrEmB5CiaoyoPaAY9X93CtB0OeA6QmOUml3ljscUgvL3rVRwD2iacibn6oDaaA7xzmibrmt9ibcaNR27Sl8sawD9ZUDgAenibnicpoecdGEaRubkMkImzibwjoddx/96</sourceheadurl><sourcetime>2025-07-21&#x20;12:07:00</sourcetime><srcMsgCreateTime>1753095444</srcMsgCreateTime><fromnewmsgid>9102928092172252156</fromnewmsgid><dataitemsource><hashusername>e6b73ff27565cff370109f8fcda0e65fdab070d2463b159472ee7e7f54f7d313</hashusername></dataitemsource></dataitem><dataitem datatype="1" dataid="a672e90d69f5fa6651911e1702cd093f" datasourceid="5900454076073617764"><datadesc>8、起风了 (2019歌手第三期现场) -- 吴青峰</datadesc><sourcename>🎶 搜索结果</sourcename><sourceheadurl>https://wx.qlogo.cn/mmhead/ver_1/PCUwVKYvh0icQrEmB5CiaoyoPaAY9X93CtB0OeA6QmOUml3ljscUgvL3rVRwD2iacibn6oDaaA7xzmibrmt9ibcaNR27Sl8sawD9ZUDgAenibnicpoecdGEaRubkMkImzibwjoddx/96</sourceheadurl><sourcetime>2025-07-21&#x20;12:08:00</sourcetime><srcMsgCreateTime>1753095474</srcMsgCreateTime><fromnewmsgid>5900454076073617764</fromnewmsgid><dataitemsource><hashusername>e6b73ff27565cff370109f8fcda0e65fdab070d2463b159472ee7e7f54f7d313</hashusername></dataitemsource></dataitem><dataitem datatype="1" dataid="f718b82b4c608a2de84fe607c43d312c" datasourceid="1887721998117850827"><datadesc>发送 "听1" 到 "听8" 选择对应歌曲</datadesc><sourcename>💡 使用提示</sourcename><sourceheadurl>https://wx.qlogo.cn/mmhead/ver_1/rTPg4GeXEic6oQSZ2jcAI6ic6JEhFtYT40Eu3rUIjZpTKTATSgCEiaMJlUuFibkfxN4IpDCv0qxCA5WgowqT0BGRTQAVeDTn1PTGj1LUYbl2bRuJiaCb1ukH4e1TPIibyUiaX6hFKgria9aX7NM5SJFHDq3pBA/132</sourceheadurl><sourcetime>2025-07-21&#x20;12:09:00</sourcetime><srcMsgCreateTime>1753095504</srcMsgCreateTime><fromnewmsgid>1887721998117850827</fromnewmsgid><dataitemsource><hashusername>8f57acad5c854c4004fd00435c305ac427418e4ac2f7816798d3db2db6eff1eb</hashusername></dataitemsource></dataitem></datalist><favcreatetime>1753095234119</favcreatetime></recordinfo>]]></recorditem><appattach>    <totallen>0</totallen>    <attachid/>    <emoticonmd5/>    <fileext/>    <aeskey/></appattach><extinfo/><sourceusername/><sourcedisplayname/><thumburl/><md5/></appmsg><fromusername>wxid_4usgcju5ey9q29</fromusername><scene>0</scene><appinfo>    <version>1</version>    <appname></appname></appinfo><commenturl/>
2025-07-21 18:53:54 | DEBUG | 处理消息内容: '搜歌 起风了'
2025-07-21 18:53:54 | DEBUG | 消息内容 '搜歌 起风了' 不匹配任何命令，忽略
2025-07-21 18:54:01 | DEBUG | 收到消息: {'MsgId': 181509444, 'FromUserName': {'string': '47442567074@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 1, 'Content': {'string': 'wxid_ubbh6q832tcs21:\n[破涕为笑]'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1753095250, 'MsgSource': '<msgsource>\n\t<pua>1</pua>\n\t<eggIncluded>1</eggIncluded>\n\t<silence>0</silence>\n\t<membercount>495</membercount>\n\t<signature>N0_V1_jvKweSZC|v1_Zr73q5fu</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'PushContent': '郭 : [破涕为笑]', 'NewMsgId': 8443388879301729558, 'MsgSeq': 871388483}
2025-07-21 18:54:01 | INFO | 收到表情消息: 消息ID:181509444 来自:47442567074@chatroom 发送人:wxid_ubbh6q832tcs21 @:[] 内容:[破涕为笑]
2025-07-21 18:54:01 | DEBUG | [DoubaoImageRecognition] 缓存表情消息: 8443388879301729558
2025-07-21 18:54:25 | DEBUG | 收到消息: {'MsgId': 1867674178, 'FromUserName': {'string': '47442567074@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 1, 'Content': {'string': 'wxid_ubbh6q832tcs21:\n应该合并成一个消息的'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1753095274, 'MsgSource': '<msgsource>\n\t<pua>1</pua>\n\t<eggIncluded>1</eggIncluded>\n\t<silence>0</silence>\n\t<membercount>495</membercount>\n\t<signature>N0_V1_eKk/cI5G|v1_++PRUdMf</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'PushContent': '郭 : 应该合并成一个消息的', 'NewMsgId': 45808359821109565, 'MsgSeq': 871388484}
2025-07-21 18:54:25 | INFO | 收到文本消息: 消息ID:1867674178 来自:47442567074@chatroom 发送人:wxid_ubbh6q832tcs21 @:[] 内容:应该合并成一个消息的
2025-07-21 18:54:25 | DEBUG | 处理消息内容: '应该合并成一个消息的'
2025-07-21 18:54:25 | DEBUG | 消息内容 '应该合并成一个消息的' 不匹配任何命令，忽略
