2025-07-30 14:32:30 | SUCCESS | 读取主设置成功
2025-07-30 14:32:30 | DEBUG | 最终使用的 Redis 主机地址: 127.0.0.1
2025-07-30 14:32:31 | INFO | 2025/07/30 14:32:31 GetRedisAddr: 127.0.0.1:6379
2025-07-30 14:32:31 | INFO | 2025/07/30 14:32:31 Redis连接成功 | 地址:127.0.0.1:6379 DB:0
2025-07-30 14:32:31 | INFO | 2025/07/30 14:32:31 Server start at :9000
2025-07-30 14:32:31 | SUCCESS | WechatAPI服务已启动
2025-07-30 14:32:32 | INFO | 登录账号信息: wxid: wxid_4usgcju5ey9q29  昵称: 瑶瑶  微信号: Today-KFC  手机号: None
2025-07-30 14:32:32 | INFO | 登录设备信息: device_name: <PERSON>'s Pad  device_id: 49d050a7e94d1ecc40790d74b1c4369f
2025-07-30 14:32:32 | SUCCESS | 登录成功
2025-07-30 14:32:32 | SUCCESS | 已开启自动心跳
2025-07-30 14:32:32 | INFO | 成功加载表情映射文件，共 546 条记录
2025-07-30 14:32:32 | SUCCESS | 数据库初始化成功
2025-07-30 14:32:32 | SUCCESS | 定时任务已启动
2025-07-30 14:32:32 | SUCCESS | 插件 AISignIn 已加载定时任务: {'plugins.AISignIn.main.AISignIn.auto_signin_task'}
2025-07-30 14:32:32 | INFO | 已加载管理员列表: ['wxid_ubbh6q832tcs21', 'admin-wxid']
2025-07-30 14:32:32 | INFO | 播客API初始化成功
2025-07-30 14:32:32 | INFO | 加载配置完成，转换后的数据: {'gh_7057516c9e71': {'name': '唱舞全明星', 'groups': ['27852221909@chatroom', '51891329927@chatroom']}, 'gh_d4d21a0e4623': {'name': '两颗桃', 'groups': ['48097389945@chatroom']}, 'gh_410ff2fa6ec9': {'name': 'NGC660 Ai研究院', 'groups': ['47442567074@chatroom']}, 'gh_f78c53c507c0': {'name': '不正常吐槽君', 'groups': ['48097389945@chatroom']}, 'gh_ba3381c847b3': {'name': '唱舞星计划', 'groups': ['51891329927@chatroom']}, 'gh_1a102fb4cad9': {'name': '我在娱乐圈的唯一人脉', 'groups': ['48097389945@chatroom', '27852221909@chatroom']}}
2025-07-30 14:32:32 | INFO | ArticleForwarder插件初始化完成 - 监控配置: {'gh_7057516c9e71': {'name': '唱舞全明星', 'groups': ['27852221909@chatroom', '51891329927@chatroom']}, 'gh_d4d21a0e4623': {'name': '两颗桃', 'groups': ['48097389945@chatroom']}, 'gh_410ff2fa6ec9': {'name': 'NGC660 Ai研究院', 'groups': ['47442567074@chatroom']}, 'gh_f78c53c507c0': {'name': '不正常吐槽君', 'groups': ['48097389945@chatroom']}, 'gh_ba3381c847b3': {'name': '唱舞星计划', 'groups': ['51891329927@chatroom']}, 'gh_1a102fb4cad9': {'name': '我在娱乐圈的唯一人脉', 'groups': ['48097389945@chatroom', '27852221909@chatroom']}}
2025-07-30 14:32:32 | DEBUG | [TempFileManager] 添加清理规则: default
2025-07-30 14:32:32 | DEBUG | [TempFileManager] 添加清理规则: images
2025-07-30 14:32:32 | DEBUG | [TempFileManager] 添加清理规则: videos
2025-07-30 14:32:32 | DEBUG | [TempFileManager] 添加清理规则: audio
2025-07-30 14:32:32 | DEBUG | [TempFileManager] 添加清理规则: temp
2025-07-30 14:32:32 | DEBUG | [TempFileManager] 添加清理规则: large_files
2025-07-30 14:32:32 | INFO | [TempFileManager] 临时文件清理任务已启动
2025-07-30 14:32:33 | INFO | [ChatSummary] 数据库初始化成功
2025-07-30 14:32:33 | SUCCESS | 插件 GoodMorning 已加载定时任务: {'plugins.GoodMorning.main.GoodMorning.daily_task'}
2025-07-30 14:32:33 | SUCCESS | 插件 News 已加载定时任务: {'plugins.News.main.News.noon_news', 'plugins.News.main.News.night_news'}
2025-07-30 14:32:33 | INFO | [PatReply] AMR文件已存在且是最新的: C:\XYBotV2\data\paiyipai\1_BFB5258B-2B13-4CF4-A40C-5BCCE74E5414_converted.amr
2025-07-30 14:32:33 | DEBUG | [PatReply] 从缓存加载base64: 18228 字符
2025-07-30 14:32:33 | SUCCESS | 插件 QuarkSignIn 已加载定时任务: {'plugins.QuarkSignIn.main.QuarkSignIn.auto_signin_task'}
2025-07-30 14:32:33 | INFO | [QuarkSignIn] 已设置自动签到时间: 08:00
2025-07-30 14:32:33 | INFO | 插件状态: 启用, 检查间隔: 3600秒, 最大重试次数: 3, 重试延迟: 5秒
2025-07-30 14:32:33 | INFO | 已加载管理员列表: ['wxid_ubbh6q832tcs21', 'admin-wxid']
2025-07-30 14:32:33 | INFO | 已加载 2 个改名监控群和 1 个退群监控群
2025-07-30 14:32:33 | INFO | [RenameReminder] 开始启用插件...
2025-07-30 14:32:33 | SUCCESS | 插件 RenameReminder 已加载定时任务: {'plugins.RenameReminder.main.RenameReminder.check_name_changes'}
2025-07-30 14:32:33 | INFO | [RenameReminder] 插件已启用，开始初始化...
2025-07-30 14:32:33 | INFO | [RenameReminder] 支持的命令: 开启改名提醒, 关闭改名提醒, 开启退群提醒, 关闭退群提醒, 查看提醒状态
2025-07-30 14:32:33 | INFO | 已设置检查间隔为 3600 秒
2025-07-30 14:32:33 | INFO | 已加载 2 个监控群，开始更新成员列表
2025-07-30 14:32:34 | DEBUG | 已更新群 27852221909@chatroom 的成员列表
2025-07-30 14:32:34 | DEBUG | 已更新群 51891329927@chatroom 的成员列表
2025-07-30 14:32:34 | SUCCESS | 插件 TimerTask 已加载定时任务: {'plugins.TimerTask.main.TimerTask.check_tasks'}
2025-07-30 14:32:34 | INFO | [VideoDemand] 加载了 128 个视频类别配置
2025-07-30 14:32:34 | INFO | [YaoyaoPlugin] 私聊黑名单用户: ['gh_a5cf07d474f1', 'gh_ba3381c847b3']
2025-07-30 14:32:34 | INFO | [YaoyaoPlugin] 已加载管理员列表: ['wxid_ubbh6q832tcs21', 'admin-wxid']
2025-07-30 14:32:34 | INFO | [yuanbao] 插件初始化完成
2025-07-30 14:32:34 | DEBUG | [yuanbao] 指令: ['元宝', 'yuanbao']
2025-07-30 14:32:34 | DEBUG | [yuanbao] 绘图模型: gpt_175B_0404
2025-07-30 14:32:34 | DEBUG | [yuanbao] 认证信息加载状态: hy_token=True, hy_user=True, agent_id=True, x_uskey=True
2025-07-30 14:32:34 | SUCCESS | 已加载插件: ['AdminPoint', 'AdminSignInReset', 'AdminWhitelist', 'AISignIn', 'AppMessageTester', 'ArticleForwarder', 'BaiduAgentsPlugin', 'BaiduDraw', 'BotStatus', 'ChatSummary', 'DanceSignInPlugin', 'DeepseekPlugin', 'Doubao', 'DoubaoDrawing', 'DoubaoImageRecognition', 'DouBaoImageToImage', 'DouBaoImageToVideo', 'DoubaoVideoSearch', 'DouHui', 'EmojiTestPlugin', 'FixedQuoteTest', 'GetContact', 'Gomoku', 'GoodMorning', 'HunyuanDraw', 'ImageEcho', 'ImageToImage', 'JiemengDraw', 'KeLingImageToImage', 'KlingAI', 'Leaderboard', 'LuckyDraw', 'ManagePlugin', 'MeituAI', 'Menu', 'MiniProgramTester', 'Music', 'News', 'PatReply', 'PointTrade', 'QuarkSignIn', 'QueryPoint', 'RandomMember', 'RandomPicture', 'RenameReminder', 'RevokePlugin', 'RoboNeo', 'SignIn', 'TempFileManagerPlugin', 'TencentLke', 'TimerTask', 'TongyiDraw', 'URLShortener', 'VideoDemand', 'VideoParserPlugin', 'VideoTest', 'VivoAgentsPlugin', 'VoiceTest', 'Warthunder', 'WeatherQuery', 'WelcomePlugin', 'Xunfei', 'YaoyaoPlugin', 'Yuanbao', '抽签', '造梦次元']
2025-07-30 14:32:34 | INFO | 处理堆积消息中
2025-07-30 14:32:35 | SUCCESS | 处理堆积消息完毕
2025-07-30 14:32:35 | SUCCESS | 开始处理消息
2025-07-30 14:32:45 | DEBUG | 收到消息: {'MsgId': 551174156, 'FromUserName': {'string': '55878994168@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 49, 'Content': {'string': 'wxid_ubbh6q832tcs21:\n<?xml version="1.0"?>\n<msg>\n\t<appmsg appid="" sdkver="0">\n\t\t<title>总结一下</title>\n\t\t<des />\n\t\t<username />\n\t\t<action>view</action>\n\t\t<type>57</type>\n\t\t<showtype>0</showtype>\n\t\t<content />\n\t\t<url />\n\t\t<lowurl />\n\t\t<forwardflag>0</forwardflag>\n\t\t<dataurl />\n\t\t<lowdataurl />\n\t\t<contentattr>0</contentattr>\n\t\t<streamvideo>\n\t\t\t<streamvideourl />\n\t\t\t<streamvideototaltime>0</streamvideototaltime>\n\t\t\t<streamvideotitle />\n\t\t\t<streamvideowording />\n\t\t\t<streamvideoweburl />\n\t\t\t<streamvideothumburl />\n\t\t\t<streamvideoaduxinfo />\n\t\t\t<streamvideopublishid />\n\t\t</streamvideo>\n\t\t<canvasPageItem>\n\t\t\t<canvasPageXml><![CDATA[]]></canvasPageXml>\n\t\t</canvasPageItem>\n\t\t<refermsg>\n\t\t\t<type>49</type>\n\t\t\t<svrid>3185879552775828120</svrid>\n\t\t\t<fromusr>55878994168@chatroom</fromusr>\n\t\t\t<chatusr>wxid_4usgcju5ey9q29</chatusr>\n\t\t\t<displayname>瑶瑶</displayname>\n\t\t\t<msgsource>&lt;msgsource&gt;\n\t&lt;bizflag&gt;0&lt;/bizflag&gt;\n\t&lt;sec_msg_node&gt;\n\t\t&lt;uuid&gt;89f015842f7b1e81d27179c97bd9dddd_&lt;/uuid&gt;\n\t\t&lt;risk-file-flag /&gt;\n\t\t&lt;risk-file-md5-list /&gt;\n\t&lt;/sec_msg_node&gt;\n\t&lt;silence&gt;1&lt;/silence&gt;\n\t&lt;membercount&gt;3&lt;/membercount&gt;\n\t&lt;signature&gt;N0_V1_bwhDeMKO|v1_XgBSqzXP&lt;/signature&gt;\n\t&lt;tmp_node&gt;\n\t\t&lt;publisher-id&gt;&lt;/publisher-id&gt;\n\t&lt;/tmp_node&gt;\n&lt;/msgsource&gt;\n</msgsource>\n\t\t\t<content>&lt;msg&gt;&lt;appmsg sdkver="1"&gt;&lt;title&gt;CJ2025&amp;#x20;|&amp;#x20;速通！ChinaJoy&amp;#x20;2025请收下这份指南&lt;/title&gt;&lt;des&gt;《唱舞全明星》ChinaJoy&amp;#x20;2025活动指南公开！&lt;/des&gt;&lt;type&gt;5&lt;/type&gt;&lt;url&gt;http://mp.weixin.qq.com/s?__biz=MzU5NTA5OTMxMA==&amp;amp;mid=2247526700&amp;amp;idx=1&amp;amp;sn=58b0ee2f492d7b43176b25c5726957d1&amp;amp;chksm=ff40fbe33e10bfc873e7686d52ae166e5d56f54b075b5b51c95a882b2e5fd36dd59505a3f0a8&amp;amp;scene=0&amp;amp;xtrack=1#rd&lt;/url&gt;&lt;sourceusername&gt;gh_7057516c9e71&lt;/sourceusername&gt;&lt;sourcedisplayname&gt;唱舞全明星&lt;/sourcedisplayname&gt;&lt;thumburl&gt;https://mmbiz.qpic.cn/mmbiz_jpg/RicuJvxibRtUAdsULuhGBmLHDXr7xdBVkP0qsXJMDLzic6ohNRABEMHBPSnkfHBUAqVsq8OhWrN0AM3AT6wwnCiaoA/640?wxtype=jpeg&amp;amp;wxfrom=0&lt;/thumburl&gt;&lt;mmreadershare&gt;&lt;itemshowtype&gt;0&lt;/itemshowtype&gt;&lt;showsourceinfo&gt;0&lt;/showsourceinfo&gt;&lt;/mmreadershare&gt;&lt;/appmsg&gt;&lt;fromusername&gt;wxid_4usgcju5ey9q29&lt;/fromusername&gt;&lt;appinfo&gt;&lt;version&gt;1&lt;/version&gt;&lt;/appinfo&gt;&lt;/msg&gt;</content>\n\t\t\t<strid />\n\t\t\t<createtime>1753502850</createtime>\n\t\t</refermsg>\n\t\t<appattach>\n\t\t\t<totallen>0</totallen>\n\t\t\t<attachid />\n\t\t\t<cdnattachurl />\n\t\t\t<emoticonmd5 />\n\t\t\t<aeskey />\n\t\t\t<fileext />\n\t\t\t<islargefilemsg>0</islargefilemsg>\n\t\t</appattach>\n\t\t<extinfo />\n\t\t<androidsource>0</androidsource>\n\t\t<thumburl />\n\t\t<mediatagname />\n\t\t<messageaction><![CDATA[]]></messageaction>\n\t\t<messageext><![CDATA[]]></messageext>\n\t\t<emoticongift>\n\t\t\t<packageflag>0</packageflag>\n\t\t\t<packageid />\n\t\t</emoticongift>\n\t\t<emoticonshared>\n\t\t\t<packageflag>0</packageflag>\n\t\t\t<packageid />\n\t\t</emoticonshared>\n\t\t<designershared>\n\t\t\t<designeruin>0</designeruin>\n\t\t\t<designername>null</designername>\n\t\t\t<designerrediretcturl><![CDATA[null]]></designerrediretcturl>\n\t\t</designershared>\n\t\t<emotionpageshared>\n\t\t\t<tid>0</tid>\n\t\t\t<title>null</title>\n\t\t\t<desc>null</desc>\n\t\t\t<iconUrl><![CDATA[null]]></iconUrl>\n\t\t\t<secondUrl>null</secondUrl>\n\t\t\t<pageType>0</pageType>\n\t\t\t<setKey>null</setKey>\n\t\t</emotionpageshared>\n\t\t<webviewshared>\n\t\t\t<shareUrlOriginal />\n\t\t\t<shareUrlOpen />\n\t\t\t<jsAppId />\n\t\t\t<publisherId />\n\t\t\t<publisherReqId />\n\t\t</webviewshared>\n\t\t<template_id />\n\t\t<md5 />\n\t\t<websearch>\n\t\t\t<rec_category>0</rec_category>\n\t\t\t<channelId>0</channelId>\n\t\t</websearch>\n\t\t<weappinfo>\n\t\t\t<username />\n\t\t\t<appid />\n\t\t\t<appservicetype>0</appservicetype>\n\t\t\t<secflagforsinglepagemode>0</secflagforsinglepagemode>\n\t\t\t<videopageinfo>\n\t\t\t\t<thumbwidth>0</thumbwidth>\n\t\t\t\t<thumbheight>0</thumbheight>\n\t\t\t\t<fromopensdk>0</fromopensdk>\n\t\t\t</videopageinfo>\n\t\t</weappinfo>\n\t\t<statextstr />\n\t\t<musicShareItem>\n\t\t\t<musicDuration>0</musicDuration>\n\t\t</musicShareItem>\n\t\t<finderLiveProductShare>\n\t\t\t<finderLiveID><![CDATA[]]></finderLiveID>\n\t\t\t<finderUsername><![CDATA[]]></finderUsername>\n\t\t\t<finderObjectID><![CDATA[]]></finderObjectID>\n\t\t\t<finderNonceID><![CDATA[]]></finderNonceID>\n\t\t\t<liveStatus><![CDATA[]]></liveStatus>\n\t\t\t<appId><![CDATA[]]></appId>\n\t\t\t<pagePath><![CDATA[]]></pagePath>\n\t\t\t<productId><![CDATA[]]></productId>\n\t\t\t<coverUrl><![CDATA[]]></coverUrl>\n\t\t\t<productTitle><![CDATA[]]></productTitle>\n\t\t\t<marketPrice><![CDATA[0]]></marketPrice>\n\t\t\t<sellingPrice><![CDATA[0]]></sellingPrice>\n\t\t\t<platformHeadImg><![CDATA[]]></platformHeadImg>\n\t\t\t<platformName><![CDATA[]]></platformName>\n\t\t\t<shopWindowId><![CDATA[]]></shopWindowId>\n\t\t\t<flashSalePrice><![CDATA[0]]></flashSalePrice>\n\t\t\t<flashSaleEndTime><![CDATA[0]]></flashSaleEndTime>\n\t\t\t<ecSource><![CDATA[]]></ecSource>\n\t\t\t<sellingPriceWording><![CDATA[]]></sellingPriceWording>\n\t\t\t<platformIconURL><![CDATA[]]></platformIconURL>\n\t\t\t<firstProductTagURL><![CDATA[]]></firstProductTagURL>\n\t\t\t<firstProductTagAspectRatioString><![CDATA[0.0]]></firstProductTagAspectRatioString>\n\t\t\t<secondProductTagURL><![CDATA[]]></secondProductTagURL>\n\t\t\t<secondProductTagAspectRatioString><![CDATA[0.0]]></secondProductTagAspectRatioString>\n\t\t\t<firstGuaranteeWording><![CDATA[]]></firstGuaranteeWording>\n\t\t\t<secondGuaranteeWording><![CDATA[]]></secondGuaranteeWording>\n\t\t\t<thirdGuaranteeWording><![CDATA[]]></thirdGuaranteeWording>\n\t\t\t<isPriceBeginShow>false</isPriceBeginShow>\n\t\t\t<lastGMsgID><![CDATA[]]></lastGMsgID>\n\t\t\t<promoterKey><![CDATA[]]></promoterKey>\n\t\t\t<discountWording><![CDATA[]]></discountWording>\n\t\t\t<priceSuffixDescription><![CDATA[]]></priceSuffixDescription>\n\t\t\t<productCardKey><![CDATA[]]></productCardKey>\n\t\t\t<isWxShop><![CDATA[]]></isWxShop>\n\t\t\t<brandIconUrl><![CDATA[]]></brandIconUrl>\n\t\t\t<showBoxItemStringList />\n\t\t</finderLiveProductShare>\n\t\t<finderOrder>\n\t\t\t<appID><![CDATA[]]></appID>\n\t\t\t<orderID><![CDATA[]]></orderID>\n\t\t\t<path><![CDATA[]]></path>\n\t\t\t<priceWording><![CDATA[]]></priceWording>\n\t\t\t<stateWording><![CDATA[]]></stateWording>\n\t\t\t<productImageURL><![CDATA[]]></productImageURL>\n\t\t\t<products><![CDATA[]]></products>\n\t\t\t<productsCount><![CDATA[0]]></productsCount>\n\t\t\t<orderType><![CDATA[0]]></orderType>\n\t\t\t<newPriceWording><![CDATA[]]></newPriceWording>\n\t\t\t<newStateWording><![CDATA[]]></newStateWording>\n\t\t\t<useNewWording><![CDATA[0]]></useNewWording>\n\t\t</finderOrder>\n\t\t<finderShopWindowShare>\n\t\t\t<finderUsername><![CDATA[]]></finderUsername>\n\t\t\t<avatar><![CDATA[]]></avatar>\n\t\t\t<nickname><![CDATA[]]></nickname>\n\t\t\t<commodityInStockCount><![CDATA[]]></commodityInStockCount>\n\t\t\t<appId><![CDATA[]]></appId>\n\t\t\t<path><![CDATA[]]></path>\n\t\t\t<appUsername><![CDATA[]]></appUsername>\n\t\t\t<query><![CDATA[]]></query>\n\t\t\t<liteAppId><![CDATA[]]></liteAppId>\n\t\t\t<liteAppPath><![CDATA[]]></liteAppPath>\n\t\t\t<liteAppQuery><![CDATA[]]></liteAppQuery>\n\t\t\t<platformTagURL><![CDATA[]]></platformTagURL>\n\t\t\t<saleWording><![CDATA[]]></saleWording>\n\t\t\t<lastGMsgID><![CDATA[]]></lastGMsgID>\n\t\t\t<profileTypeWording><![CDATA[]]></profileTypeWording>\n\t\t\t<saleWordingExtra><![CDATA[]]></saleWordingExtra>\n\t\t\t<isWxShop><![CDATA[]]></isWxShop>\n\t\t\t<platformIconUrl><![CDATA[]]></platformIconUrl>\n\t\t\t<brandIconUrl><![CDATA[]]></brandIconUrl>\n\t\t\t<description><![CDATA[]]></description>\n\t\t\t<backgroundUrl><![CDATA[]]></backgroundUrl>\n\t\t\t<darkModePlatformIconUrl><![CDATA[]]></darkModePlatformIconUrl>\n\t\t\t<reputationInfo>\n\t\t\t\t<hasReputationInfo>0</hasReputationInfo>\n\t\t\t\t<reputationScore>0</reputationScore>\n\t\t\t\t<reputationWording />\n\t\t\t\t<reputationTextColor />\n\t\t\t\t<reputationLevelWording />\n\t\t\t\t<reputationBackgroundColor />\n\t\t\t</reputationInfo>\n\t\t\t<productImageURLList />\n\t\t</finderShopWindowShare>\n\t\t<findernamecard>\n\t\t\t<username />\n\t\t\t<avatar><![CDATA[]]></avatar>\n\t\t\t<nickname />\n\t\t\t<auth_job />\n\t\t\t<auth_icon>0</auth_icon>\n\t\t\t<auth_icon_url />\n\t\t\t<ecSource><![CDATA[]]></ecSource>\n\t\t\t<lastGMsgID><![CDATA[]]></lastGMsgID>\n\t\t</findernamecard>\n\t\t<finderGuarantee>\n\t\t\t<scene><![CDATA[0]]></scene>\n\t\t</finderGuarantee>\n\t\t<directshare>0</directshare>\n\t\t<gamecenter>\n\t\t\t<namecard>\n\t\t\t\t<iconUrl />\n\t\t\t\t<name />\n\t\t\t\t<desc />\n\t\t\t\t<tail />\n\t\t\t\t<jumpUrl />\n\t\t\t</namecard>\n\t\t</gamecenter>\n\t\t<patMsg>\n\t\t\t<chatUser />\n\t\t\t<records>\n\t\t\t\t<recordNum>0</recordNum>\n\t\t\t</records>\n\t\t</patMsg>\n\t\t<secretmsg>\n\t\t\t<issecretmsg>0</issecretmsg>\n\t\t</secretmsg>\n\t\t<referfromscene>0</referfromscene>\n\t\t<gameshare>\n\t\t\t<liteappext>\n\t\t\t\t<liteappbizdata />\n\t\t\t\t<priority>0</priority>\n\t\t\t</liteappext>\n\t\t\t<appbrandext>\n\t\t\t\t<litegameinfo />\n\t\t\t\t<priority>-1</priority>\n\t\t\t</appbrandext>\n\t\t\t<gameshareid />\n\t\t\t<sharedata />\n\t\t\t<isvideo>0</isvideo>\n\t\t\t<duration>-1</duration>\n\t\t\t<isexposed>0</isexposed>\n\t\t\t<readtext />\n\t\t</gameshare>\n\t\t<mpsharetrace>\n\t\t\t<hasfinderelement>0</hasfinderelement>\n\t\t\t<lastgmsgid />\n\t\t</mpsharetrace>\n\t\t<wxgamecard>\n\t\t\t<framesetname />\n\t\t\t<mbcarddata />\n\t\t\t<minpkgversion />\n\t\t\t<clientextinfo />\n\t\t\t<mbcardheight>0</mbcardheight>\n\t\t\t<isoldversion>0</isoldversion>\n\t\t</wxgamecard>\n\t\t<liteapp>\n\t\t\t<id>null</id>\n\t\t\t<path />\n\t\t\t<query />\n\t\t\t<istransparent>0</istransparent>\n\t\t\t<hideicon>0</hideicon>\n\t\t</liteapp>\n\t\t<opensdk_share_is_modified>0</opensdk_share_is_modified>\n\t</appmsg>\n\t<fromusername>wxid_ubbh6q832tcs21</fromusername>\n\t<scene>0</scene>\n\t<appinfo>\n\t\t<version>1</version>\n\t\t<appname></appname>\n\t</appinfo>\n\t<commenturl></commenturl>\n</msg>\n'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1753857178, 'MsgSource': '<msgsource>\n\t<alnode>\n\t\t<fr>4</fr>\n\t</alnode>\n\t<eggIncluded>1</eggIncluded>\n\t<sec_msg_node>\n\t\t<uuid>89f015842f7b1e81d27179c97bd9dddd_</uuid>\n\t\t<risk-file-flag />\n\t\t<risk-file-md5-list />\n\t</sec_msg_node>\n\t<silence>0</silence>\n\t<membercount>3</membercount>\n\t<signature>N0_V1_nyAZYu7U|v1_d9RR6mHL</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'PushContent': '郭 : 总结一下', 'NewMsgId': 8787174606336866226, 'MsgSeq': 871411832}
2025-07-30 14:32:45 | DEBUG | 从群聊消息中提取发送者: wxid_ubbh6q832tcs21
2025-07-30 14:32:45 | DEBUG | 使用已解析的XML处理引用消息
2025-07-30 14:32:45 | INFO | 收到引用消息: 消息ID:551174156 来自:55878994168@chatroom 发送人:wxid_ubbh6q832tcs21 内容:总结一下 引用类型:49
2025-07-30 14:32:45 | INFO | [DouBaoImageToImage] 收到引用消息: 总结一下
2025-07-30 14:32:45 | INFO | 成功加载表情映射文件，共 546 条记录
2025-07-30 14:32:45 | INFO | [TimerTask] 收到引用消息调试信息:
2025-07-30 14:32:45 | INFO |   - 消息内容: 总结一下
2025-07-30 14:32:45 | INFO |   - 群组ID: 55878994168@chatroom
2025-07-30 14:32:45 | INFO |   - 发送人: wxid_ubbh6q832tcs21
2025-07-30 14:32:45 | INFO |   - 引用信息: {'MsgType': 49, 'Content': '<msg><appmsg sdkver="1"><title>CJ2025&#x20;|&#x20;速通！ChinaJoy&#x20;2025请收下这份指南</title><des>《唱舞全明星》ChinaJoy&#x20;2025活动指南公开！</des><type>5</type><url>http://mp.weixin.qq.com/s?__biz=MzU5NTA5OTMxMA==&amp;mid=2247526700&amp;idx=1&amp;sn=58b0ee2f492d7b43176b25c5726957d1&amp;chksm=ff40fbe33e10bfc873e7686d52ae166e5d56f54b075b5b51c95a882b2e5fd36dd59505a3f0a8&amp;scene=0&amp;xtrack=1#rd</url><sourceusername>gh_7057516c9e71</sourceusername><sourcedisplayname>唱舞全明星</sourcedisplayname><thumburl>https://mmbiz.qpic.cn/mmbiz_jpg/RicuJvxibRtUAdsULuhGBmLHDXr7xdBVkP0qsXJMDLzic6ohNRABEMHBPSnkfHBUAqVsq8OhWrN0AM3AT6wwnCiaoA/640?wxtype=jpeg&amp;wxfrom=0</thumburl><mmreadershare><itemshowtype>0</itemshowtype><showsourceinfo>0</showsourceinfo></mmreadershare></appmsg><fromusername>wxid_4usgcju5ey9q29</fromusername><appinfo><version>1</version></appinfo></msg>', 'Msgid': '3185879552775828120', 'NewMsgId': '3185879552775828120', 'ToWxid': 'wxid_4usgcju5ey9q29', 'FromWxid': '55878994168@chatroom', 'Nickname': '瑶瑶', 'MsgSource': '<msgsource>\n\t<bizflag>0</bizflag>\n\t<sec_msg_node>\n\t\t<uuid>89f015842f7b1e81d27179c97bd9dddd_</uuid>\n\t\t<risk-file-flag />\n\t\t<risk-file-md5-list />\n\t</sec_msg_node>\n\t<silence>1</silence>\n\t<membercount>3</membercount>\n\t<signature>N0_V1_bwhDeMKO|v1_XgBSqzXP</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'Createtime': '1753502850', 'SenderWxid': 'wxid_ubbh6q832tcs21'}
2025-07-30 14:32:45 | INFO |   - 引用消息ID: 
2025-07-30 14:32:45 | INFO |   - 引用消息类型: 
2025-07-30 14:32:45 | INFO |   - 引用消息内容: <msg><appmsg sdkver="1"><title>CJ2025&#x20;|&#x20;速通！ChinaJoy&#x20;2025请收下这份指南</title><des>《唱舞全明星》ChinaJoy&#x20;2025活动指南公开！</des><type>5</type><url>http://mp.weixin.qq.com/s?__biz=MzU5NTA5OTMxMA==&amp;mid=2247526700&amp;idx=1&amp;sn=58b0ee2f492d7b43176b25c5726957d1&amp;chksm=ff40fbe33e10bfc873e7686d52ae166e5d56f54b075b5b51c95a882b2e5fd36dd59505a3f0a8&amp;scene=0&amp;xtrack=1#rd</url><sourceusername>gh_7057516c9e71</sourceusername><sourcedisplayname>唱舞全明星</sourcedisplayname><thumburl>https://mmbiz.qpic.cn/mmbiz_jpg/RicuJvxibRtUAdsULuhGBmLHDXr7xdBVkP0qsXJMDLzic6ohNRABEMHBPSnkfHBUAqVsq8OhWrN0AM3AT6wwnCiaoA/640?wxtype=jpeg&amp;wxfrom=0</thumburl><mmreadershare><itemshowtype>0</itemshowtype><showsourceinfo>0</showsourceinfo></mmreadershare></appmsg><fromusername>wxid_4usgcju5ey9q29</fromusername><appinfo><version>1</version></appinfo></msg>
2025-07-30 14:32:45 | INFO |   - 引用消息发送人: wxid_ubbh6q832tcs21
2025-07-30 14:32:45 | DEBUG | [VideoParser] 处理引用消息命令[总结一下]: {'MsgType': 49, 'Content': '<msg><appmsg sdkver="1"><title>CJ2025&#x20;|&#x20;速通！ChinaJoy&#x20;2025请收下这份指南</title><des>《唱舞全明星》ChinaJoy&#x20;2025活动指南公开！</des><type>5</type><url>http://mp.weixin.qq.com/s?__biz=MzU5NTA5OTMxMA==&amp;mid=2247526700&amp;idx=1&amp;sn=58b0ee2f492d7b43176b25c5726957d1&amp;chksm=ff40fbe33e10bfc873e7686d52ae166e5d56f54b075b5b51c95a882b2e5fd36dd59505a3f0a8&amp;scene=0&amp;xtrack=1#rd</url><sourceusername>gh_7057516c9e71</sourceusername><sourcedisplayname>唱舞全明星</sourcedisplayname><thumburl>https://mmbiz.qpic.cn/mmbiz_jpg/RicuJvxibRtUAdsULuhGBmLHDXr7xdBVkP0qsXJMDLzic6ohNRABEMHBPSnkfHBUAqVsq8OhWrN0AM3AT6wwnCiaoA/640?wxtype=jpeg&amp;wxfrom=0</thumburl><mmreadershare><itemshowtype>0</itemshowtype><showsourceinfo>0</showsourceinfo></mmreadershare></appmsg><fromusername>wxid_4usgcju5ey9q29</fromusername><appinfo><version>1</version></appinfo></msg>', 'Msgid': '3185879552775828120', 'NewMsgId': '3185879552775828120', 'ToWxid': 'wxid_4usgcju5ey9q29', 'FromWxid': '55878994168@chatroom', 'Nickname': '瑶瑶', 'MsgSource': '<msgsource>\n\t<bizflag>0</bizflag>\n\t<sec_msg_node>\n\t\t<uuid>89f015842f7b1e81d27179c97bd9dddd_</uuid>\n\t\t<risk-file-flag />\n\t\t<risk-file-md5-list />\n\t</sec_msg_node>\n\t<silence>1</silence>\n\t<membercount>3</membercount>\n\t<signature>N0_V1_bwhDeMKO|v1_XgBSqzXP</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'Createtime': '1753502850', 'SenderWxid': 'wxid_ubbh6q832tcs21'}
2025-07-30 14:32:46 | INFO | 发送文字消息: 对方wxid:55878994168@chatroom at: 内容:❌ 总结内容为空，请稍后重试
2025-07-30 14:32:46 | DEBUG | 收到消息: {'MsgId': 101119362, 'FromUserName': {'string': '27852221909@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 49, 'Content': {'string': 'wxid_xrb90gbzzu5q22:\n<?xml version="1.0"?>\n<msg>\n\t<appmsg appid="" sdkver="0">\n\t\t<title>就等一会</title>\n\t\t<des />\n\t\t<username />\n\t\t<action>view</action>\n\t\t<type>57</type>\n\t\t<showtype>0</showtype>\n\t\t<content />\n\t\t<url />\n\t\t<lowurl />\n\t\t<forwardflag>0</forwardflag>\n\t\t<dataurl />\n\t\t<lowdataurl />\n\t\t<contentattr>0</contentattr>\n\t\t<streamvideo>\n\t\t\t<streamvideourl />\n\t\t\t<streamvideototaltime>0</streamvideototaltime>\n\t\t\t<streamvideotitle />\n\t\t\t<streamvideowording />\n\t\t\t<streamvideoweburl />\n\t\t\t<streamvideothumburl />\n\t\t\t<streamvideoaduxinfo />\n\t\t\t<streamvideopublishid />\n\t\t</streamvideo>\n\t\t<canvasPageItem>\n\t\t\t<canvasPageXml><![CDATA[]]></canvasPageXml>\n\t\t</canvasPageItem>\n\t\t<refermsg>\n\t\t\t<type>1</type>\n\t\t\t<svrid>5966618826031554285</svrid>\n\t\t\t<fromusr>27852221909@chatroom</fromusr>\n\t\t\t<chatusr>wxid_ikxxrwasicud11</chatusr>\n\t\t\t<displayname>゛花落ོ.°</displayname>\n\t\t\t<msgsource>&lt;msgsource&gt;\n\t&lt;atuserlist&gt;wxid_xrb90gbzzu5q22&lt;/atuserlist&gt;\n\t&lt;alnode&gt;\n\t\t&lt;fr&gt;1&lt;/fr&gt;\n\t&lt;/alnode&gt;\n\t&lt;silence&gt;1&lt;/silence&gt;\n\t&lt;membercount&gt;145&lt;/membercount&gt;\n\t&lt;signature&gt;N0_V1_SPCcHiUk|v1_myed3CHK&lt;/signature&gt;\n\t&lt;tmp_node&gt;\n\t\t&lt;publisher-id&gt;&lt;/publisher-id&gt;\n\t&lt;/tmp_node&gt;\n&lt;/msgsource&gt;\n</msgsource>\n\t\t\t<content>\n@阿棉\u2005拉进群之后做什么</content>\n\t\t\t<strid />\n\t\t\t<createtime>1753857000</createtime>\n\t\t</refermsg>\n\t\t<appattach>\n\t\t\t<totallen>0</totallen>\n\t\t\t<attachid />\n\t\t\t<cdnattachurl />\n\t\t\t<emoticonmd5></emoticonmd5>\n\t\t\t<aeskey></aeskey>\n\t\t\t<fileext />\n\t\t\t<islargefilemsg>0</islargefilemsg>\n\t\t</appattach>\n\t\t<extinfo />\n\t\t<androidsource>0</androidsource>\n\t\t<thumburl />\n\t\t<mediatagname />\n\t\t<messageaction><![CDATA[]]></messageaction>\n\t\t<messageext><![CDATA[]]></messageext>\n\t\t<emoticongift>\n\t\t\t<packageflag>0</packageflag>\n\t\t\t<packageid />\n\t\t</emoticongift>\n\t\t<emoticonshared>\n\t\t\t<packageflag>0</packageflag>\n\t\t\t<packageid />\n\t\t</emoticonshared>\n\t\t<designershared>\n\t\t\t<designeruin>0</designeruin>\n\t\t\t<designername>null</designername>\n\t\t\t<designerrediretcturl><![CDATA[null]]></designerrediretcturl>\n\t\t</designershared>\n\t\t<emotionpageshared>\n\t\t\t<tid>0</tid>\n\t\t\t<title>null</title>\n\t\t\t<desc>null</desc>\n\t\t\t<iconUrl><![CDATA[null]]></iconUrl>\n\t\t\t<secondUrl>null</secondUrl>\n\t\t\t<pageType>0</pageType>\n\t\t\t<setKey>null</setKey>\n\t\t</emotionpageshared>\n\t\t<webviewshared>\n\t\t\t<shareUrlOriginal />\n\t\t\t<shareUrlOpen />\n\t\t\t<jsAppId />\n\t\t\t<publisherId />\n\t\t\t<publisherReqId />\n\t\t</webviewshared>\n\t\t<template_id />\n\t\t<md5 />\n\t\t<websearch>\n\t\t\t<rec_category>0</rec_category>\n\t\t\t<channelId>0</channelId>\n\t\t</websearch>\n\t\t<weappinfo>\n\t\t\t<username />\n\t\t\t<appid />\n\t\t\t<appservicetype>0</appservicetype>\n\t\t\t<secflagforsinglepagemode>0</secflagforsinglepagemode>\n\t\t\t<videopageinfo>\n\t\t\t\t<thumbwidth>0</thumbwidth>\n\t\t\t\t<thumbheight>0</thumbheight>\n\t\t\t\t<fromopensdk>0</fromopensdk>\n\t\t\t</videopageinfo>\n\t\t</weappinfo>\n\t\t<statextstr />\n\t\t<musicShareItem>\n\t\t\t<musicDuration>0</musicDuration>\n\t\t</musicShareItem>\n\t\t<finderLiveProductShare>\n\t\t\t<finderLiveID><![CDATA[]]></finderLiveID>\n\t\t\t<finderUsername><![CDATA[]]></finderUsername>\n\t\t\t<finderObjectID><![CDATA[]]></finderObjectID>\n\t\t\t<finderNonceID><![CDATA[]]></finderNonceID>\n\t\t\t<liveStatus><![CDATA[]]></liveStatus>\n\t\t\t<appId><![CDATA[]]></appId>\n\t\t\t<pagePath><![CDATA[]]></pagePath>\n\t\t\t<productId><![CDATA[]]></productId>\n\t\t\t<coverUrl><![CDATA[]]></coverUrl>\n\t\t\t<productTitle><![CDATA[]]></productTitle>\n\t\t\t<marketPrice><![CDATA[0]]></marketPrice>\n\t\t\t<sellingPrice><![CDATA[0]]></sellingPrice>\n\t\t\t<platformHeadImg><![CDATA[]]></platformHeadImg>\n\t\t\t<platformName><![CDATA[]]></platformName>\n\t\t\t<shopWindowId><![CDATA[]]></shopWindowId>\n\t\t\t<flashSalePrice><![CDATA[0]]></flashSalePrice>\n\t\t\t<flashSaleEndTime><![CDATA[0]]></flashSaleEndTime>\n\t\t\t<ecSource><![CDATA[]]></ecSource>\n\t\t\t<sellingPriceWording><![CDATA[]]></sellingPriceWording>\n\t\t\t<platformIconURL><![CDATA[]]></platformIconURL>\n\t\t\t<firstProductTagURL><![CDATA[]]></firstProductTagURL>\n\t\t\t<firstProductTagAspectRatioString><![CDATA[0.0]]></firstProductTagAspectRatioString>\n\t\t\t<secondProductTagURL><![CDATA[]]></secondProductTagURL>\n\t\t\t<secondProductTagAspectRatioString><![CDATA[0.0]]></secondProductTagAspectRatioString>\n\t\t\t<firstGuaranteeWording><![CDATA[]]></firstGuaranteeWording>\n\t\t\t<secondGuaranteeWording><![CDATA[]]></secondGuaranteeWording>\n\t\t\t<thirdGuaranteeWording><![CDATA[]]></thirdGuaranteeWording>\n\t\t\t<isPriceBeginShow>false</isPriceBeginShow>\n\t\t\t<lastGMsgID><![CDATA[]]></lastGMsgID>\n\t\t\t<promoterKey><![CDATA[]]></promoterKey>\n\t\t\t<discountWording><![CDATA[]]></discountWording>\n\t\t\t<priceSuffixDescription><![CDATA[]]></priceSuffixDescription>\n\t\t\t<productCardKey><![CDATA[]]></productCardKey>\n\t\t\t<isWxShop><![CDATA[]]></isWxShop>\n\t\t\t<brandIconUrl><![CDATA[]]></brandIconUrl>\n\t\t\t<rIconUrl><![CDATA[]]></rIconUrl>\n\t\t\t<rIconUrlDarkMode><![CDATA[]]></rIconUrlDarkMode>\n\t\t\t<topShopIconUrl><![CDATA[]]></topShopIconUrl>\n\t\t\t<topShopIconUrlDarkMode><![CDATA[]]></topShopIconUrlDarkMode>\n\t\t\t<simplifyTopShopIconUrl><![CDATA[]]></simplifyTopShopIconUrl>\n\t\t\t<simplifyTopShopIconUrlDarkmode><![CDATA[]]></simplifyTopShopIconUrlDarkmode>\n\t\t\t<topShopIconWidth><![CDATA[0]]></topShopIconWidth>\n\t\t\t<topShopIconHeight><![CDATA[0]]></topShopIconHeight>\n\t\t\t<simplifyTopShopIconWidth><![CDATA[0]]></simplifyTopShopIconWidth>\n\t\t\t<simplifyTopShopIconHeight><![CDATA[0]]></simplifyTopShopIconHeight>\n\t\t\t<showBoxItemStringList />\n\t\t</finderLiveProductShare>\n\t\t<finderOrder>\n\t\t\t<appID><![CDATA[]]></appID>\n\t\t\t<orderID><![CDATA[]]></orderID>\n\t\t\t<path><![CDATA[]]></path>\n\t\t\t<priceWording><![CDATA[]]></priceWording>\n\t\t\t<stateWording><![CDATA[]]></stateWording>\n\t\t\t<productImageURL><![CDATA[]]></productImageURL>\n\t\t\t<products><![CDATA[]]></products>\n\t\t\t<productsCount><![CDATA[0]]></productsCount>\n\t\t\t<orderType><![CDATA[0]]></orderType>\n\t\t\t<newPriceWording><![CDATA[]]></newPriceWording>\n\t\t\t<newStateWording><![CDATA[]]></newStateWording>\n\t\t\t<useNewWording><![CDATA[0]]></useNewWording>\n\t\t</finderOrder>\n\t\t<finderShopWindowShare>\n\t\t\t<finderUsername><![CDATA[]]></finderUsername>\n\t\t\t<avatar><![CDATA[]]></avatar>\n\t\t\t<nickname><![CDATA[]]></nickname>\n\t\t\t<commodityInStockCount><![CDATA[]]></commodityInStockCount>\n\t\t\t<appId><![CDATA[]]></appId>\n\t\t\t<path><![CDATA[]]></path>\n\t\t\t<appUsername><![CDATA[]]></appUsername>\n\t\t\t<query><![CDATA[]]></query>\n\t\t\t<liteAppId><![CDATA[]]></liteAppId>\n\t\t\t<liteAppPath><![CDATA[]]></liteAppPath>\n\t\t\t<liteAppQuery><![CDATA[]]></liteAppQuery>\n\t\t\t<platformTagURL><![CDATA[]]></platformTagURL>\n\t\t\t<saleWording><![CDATA[]]></saleWording>\n\t\t\t<lastGMsgID><![CDATA[]]></lastGMsgID>\n\t\t\t<profileTypeWording><![CDATA[]]></profileTypeWording>\n\t\t\t<saleWordingExtra><![CDATA[]]></saleWordingExtra>\n\t\t\t<isWxShop><![CDATA[]]></isWxShop>\n\t\t\t<platformIconUrl><![CDATA[]]></platformIconUrl>\n\t\t\t<brandIconUrl><![CDATA[]]></brandIconUrl>\n\t\t\t<description><![CDATA[]]></description>\n\t\t\t<backgroundUrl><![CDATA[]]></backgroundUrl>\n\t\t\t<darkModePlatformIconUrl><![CDATA[]]></darkModePlatformIconUrl>\n\t\t\t<rIconUrl><![CDATA[]]></rIconUrl>\n\t\t\t<rIconUrlDarkMode><![CDATA[]]></rIconUrlDarkMode>\n\t\t\t<rWords><![CDATA[]]></rWords>\n\t\t\t<topShopIconUrl><![CDATA[]]></topShopIconUrl>\n\t\t\t<topShopIconUrlDarkMode><![CDATA[]]></topShopIconUrlDarkMode>\n\t\t\t<simplifyTopShopIconUrl><![CDATA[]]></simplifyTopShopIconUrl>\n\t\t\t<simplifyTopShopIconUrlDarkmode><![CDATA[]]></simplifyTopShopIconUrlDarkmode>\n\t\t\t<topShopIconWidth><![CDATA[0]]></topShopIconWidth>\n\t\t\t<topShopIconHeight><![CDATA[0]]></topShopIconHeight>\n\t\t\t<simplifyTopShopIconWidth><![CDATA[0]]></simplifyTopShopIconWidth>\n\t\t\t<simplifyTopShopIconHeight><![CDATA[0]]></simplifyTopShopIconHeight>\n\t\t\t<reputationInfo>\n\t\t\t\t<hasReputationInfo>0</hasReputationInfo>\n\t\t\t\t<reputationScore>0</reputationScore>\n\t\t\t\t<reputationWording />\n\t\t\t\t<reputationTextColor />\n\t\t\t\t<reputationLevelWording />\n\t\t\t\t<reputationBackgroundColor />\n\t\t\t</reputationInfo>\n\t\t\t<productImageURLList />\n\t\t</finderShopWindowShare>\n\t\t<findernamecard>\n\t\t\t<username />\n\t\t\t<avatar><![CDATA[]]></avatar>\n\t\t\t<nickname />\n\t\t\t<auth_job />\n\t\t\t<auth_icon>0</auth_icon>\n\t\t\t<auth_icon_url />\n\t\t\t<ecSource><![CDATA[]]></ecSource>\n\t\t\t<lastGMsgID><![CDATA[]]></lastGMsgID>\n\t\t</findernamecard>\n\t\t<finderGuarantee>\n\t\t\t<scene><![CDATA[0]]></scene>\n\t\t</finderGuarantee>\n\t\t<directshare>0</directshare>\n\t\t<gamecenter>\n\t\t\t<namecard>\n\t\t\t\t<iconUrl />\n\t\t\t\t<name />\n\t\t\t\t<desc />\n\t\t\t\t<tail />\n\t\t\t\t<jumpUrl />\n\t\t\t\t<liteappId />\n\t\t\t\t<liteappPath />\n\t\t\t\t<liteappQuery />\n\t\t\t\t<liteappMinVersion />\n\t\t\t</namecard>\n\t\t</gamecenter>\n\t\t<patMsg>\n\t\t\t<chatUser />\n\t\t\t<records>\n\t\t\t\t<recordNum>0</recordNum>\n\t\t\t</records>\n\t\t</patMsg>\n\t\t<secretmsg>\n\t\t\t<issecretmsg>0</issecretmsg>\n\t\t</secretmsg>\n\t\t<referfromscene>0</referfromscene>\n\t\t<gameshare>\n\t\t\t<liteappext>\n\t\t\t\t<liteappbizdata />\n\t\t\t\t<priority>0</priority>\n\t\t\t</liteappext>\n\t\t\t<appbrandext>\n\t\t\t\t<litegameinfo />\n\t\t\t\t<priority>-1</priority>\n\t\t\t</appbrandext>\n\t\t\t<gameshareid />\n\t\t\t<sharedata />\n\t\t\t<isvideo>0</isvideo>\n\t\t\t<duration>-1</duration>\n\t\t\t<isexposed>0</isexposed>\n\t\t\t<readtext />\n\t\t</gameshare>\n\t\t<tingChatRoomItem>\n\t\t\t<type>0</type>\n\t\t\t<categoryItem>null</categoryItem>\n\t\t\t<categoryId />\n\t\t</tingChatRoomItem>\n\t\t<mpsharetrace>\n\t\t\t<hasfinderelement>0</hasfinderelement>\n\t\t\t<lastgmsgid />\n\t\t</mpsharetrace>\n\t\t<wxgamecard>\n\t\t\t<framesetname />\n\t\t\t<mbcarddata />\n\t\t\t<minpkgversion />\n\t\t\t<clientextinfo />\n\t\t\t<mbcardheight>0</mbcardheight>\n\t\t\t<isoldversion>0</isoldversion>\n\t\t</wxgamecard>\n\t\t<ecskfcard>\n\t\t\t<framesetname />\n\t\t\t<mbcarddata />\n\t\t\t<minupdateunixtimestamp>0</minupdateunixtimestamp>\n\t\t\t<needheader>false</needheader>\n\t\t\t<summary />\n\t\t</ecskfcard>\n\t\t<liteapp>\n\t\t\t<id>null</id>\n\t\t\t<path />\n\t\t\t<query />\n\t\t\t<istransparent>0</istransparent>\n\t\t\t<hideicon>0</hideicon>\n\t\t\t<forbidforward>0</forbidforward>\n\t\t</liteapp>\n\t\t<opensdk_share_is_modified>0</opensdk_share_is_modified>\n\t</appmsg>\n\t<fromusername>wxid_xrb90gbzzu5q22</fromusername>\n\t<scene>0</scene>\n\t<appinfo>\n\t\t<version>1</version>\n\t\t<appname />\n\t</appinfo>\n\t<commenturl />\n</msg>\n'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1753857178, 'MsgSource': '<msgsource>\n\t<alnode>\n\t\t<fr>4</fr>\n\t</alnode>\n\t<eggIncluded>1</eggIncluded>\n\t<sec_msg_node>\n\t\t<uuid>24a712f915a2cc711ba1d2b0e0d47295_</uuid>\n\t\t<risk-file-flag />\n\t\t<risk-file-md5-list />\n\t</sec_msg_node>\n\t<silence>1</silence>\n\t<membercount>145</membercount>\n\t<signature>N0_V1_bi4DXNac|v1_YlN4Vuyy</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'NewMsgId': 8410230425641949526, 'MsgSeq': 871411833}
2025-07-30 14:32:46 | DEBUG | 从群聊消息中提取发送者: wxid_xrb90gbzzu5q22
2025-07-30 14:32:46 | DEBUG | 使用已解析的XML处理引用消息
2025-07-30 14:32:46 | INFO | 收到引用消息: 消息ID:101119362 来自:27852221909@chatroom 发送人:wxid_xrb90gbzzu5q22 内容:就等一会 引用类型:1
2025-07-30 14:32:46 | INFO | [DouBaoImageToImage] 收到引用消息: 就等一会
2025-07-30 14:32:46 | INFO | [TimerTask] 收到引用消息调试信息:
2025-07-30 14:32:46 | INFO |   - 消息内容: 就等一会
2025-07-30 14:32:46 | INFO |   - 群组ID: 27852221909@chatroom
2025-07-30 14:32:46 | INFO |   - 发送人: wxid_xrb90gbzzu5q22
2025-07-30 14:32:46 | INFO |   - 引用信息: {'MsgType': 1, 'Content': '\n@阿棉\u2005拉进群之后做什么', 'Msgid': '5966618826031554285', 'NewMsgId': '5966618826031554285', 'ToWxid': 'wxid_4usgcju5ey9q29', 'FromWxid': '27852221909@chatroom', 'Nickname': '゛花落ོ.°', 'MsgSource': '<msgsource>\n\t<atuserlist>wxid_xrb90gbzzu5q22</atuserlist>\n\t<alnode>\n\t\t<fr>1</fr>\n\t</alnode>\n\t<silence>1</silence>\n\t<membercount>145</membercount>\n\t<signature>N0_V1_SPCcHiUk|v1_myed3CHK</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'Createtime': '1753857000', 'SenderWxid': 'wxid_xrb90gbzzu5q22'}
2025-07-30 14:32:46 | INFO |   - 引用消息ID: 
2025-07-30 14:32:46 | INFO |   - 引用消息类型: 
2025-07-30 14:32:46 | INFO |   - 引用消息内容: 
@阿棉 拉进群之后做什么
2025-07-30 14:32:46 | INFO |   - 引用消息发送人: wxid_xrb90gbzzu5q22
2025-07-30 14:32:52 | DEBUG | 收到消息: {'MsgId': 86061106, 'FromUserName': {'string': '27852221909@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 1, 'Content': {'string': 'wxid_xrb90gbzzu5q22:\n他说自己就能领'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1753857184, 'MsgSource': '<msgsource>\n\t<pua>1</pua>\n\t<eggIncluded>1</eggIncluded>\n\t<silence>1</silence>\n\t<membercount>145</membercount>\n\t<signature>N0_V1_UIKvbmJC|v1_um5siKB4</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'NewMsgId': 6481552934980805876, 'MsgSeq': 871411836}
2025-07-30 14:32:52 | INFO | 收到文本消息: 消息ID:86061106 来自:27852221909@chatroom 发送人:wxid_xrb90gbzzu5q22 @:[] 内容:他说自己就能领
2025-07-30 14:32:52 | DEBUG | 处理消息内容: '他说自己就能领'
2025-07-30 14:32:52 | DEBUG | 消息内容 '他说自己就能领' 不匹配任何命令，忽略
2025-07-30 14:32:53 | DEBUG | 收到消息: {'MsgId': 2049341959, 'FromUserName': {'string': '48097389945@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 1, 'Content': {'string': 'xiaomaochong:\n离谱啊，化完妆直接变大片'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1753857184, 'MsgSource': '<msgsource>\n\t<pua>1</pua>\n\t<alnode>\n\t\t<cf>5</cf>\n\t\t<inlenlist>398</inlenlist>\n\t</alnode>\n\t<silence>0</silence>\n\t<membercount>73</membercount>\n\t<signature>N0_V1_tv5rhk1u|v1_H/MawxRU</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'PushContent': '小爱 : 离谱啊，化完妆直接变大片', 'NewMsgId': 4357163257277193569, 'MsgSeq': 871411837}
2025-07-30 14:32:53 | INFO | 收到文本消息: 消息ID:2049341959 来自:48097389945@chatroom 发送人:xiaomaochong @:[] 内容:离谱啊，化完妆直接变大片
2025-07-30 14:32:54 | DEBUG | 处理消息内容: '离谱啊，化完妆直接变大片'
2025-07-30 14:32:54 | DEBUG | 消息内容 '离谱啊，化完妆直接变大片' 不匹配任何命令，忽略
2025-07-30 14:32:56 | DEBUG | 收到消息: {'MsgId': 1247247607, 'FromUserName': {'string': '48097389945@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 43, 'Content': {'string': 'xiaomaochong:\n<?xml version="1.0"?>\n<msg>\n\t<videomsg aeskey="a693d4dcc9c59941be92035fc6508d2e" cdnvideourl="3057020100044b304902010002049363814102032f51490204a731227502046889bca0042466333666656133632d333530322d346136372d383932632d3364356437633637633930310204052808040201000405004c537500" cdnthumbaeskey="a693d4dcc9c59941be92035fc6508d2e" cdnthumburl="3057020100044b304902010002049363814102032f51490204a731227502046889bca0042466333666656133632d333530322d346136372d383932632d3364356437633637633930310204052808040201000405004c537500" length="5557148" playlength="28" cdnthumblength="18674" cdnthumbwidth="405" cdnthumbheight="720" fromusername="xiaomaochong" md5="7e3a8466ac6c60df0488aaef50f17d80" newmd5="bb11183be813c53db921db79300f16e6" isplaceholder="0" rawmd5="8492efb1b4eb2dc08b0e12e122c7975b" rawlength="30193987" cdnrawvideourl="3056020100044a304802010002035a663f02032f77f502046b5b90db02046889bca0042434373932626236332d643962342d343566632d616164312d3035323236396466643830380204059400040201000405004c57c100" cdnrawvideoaeskey="f83c094df3727c46582da3bf4a4db1b0" overwritenewmsgid="0" originsourcemd5="0ed17879609fe9ba09c700101ed97f1e" isad="0" />\n</msg>\n'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1753857188, 'MsgSource': '<msgsource>\n\t<videopreloadlen>1017940</videopreloadlen>\n\t<sec_msg_node>\n\t\t<uuid>f52c02c1df28ce64137d557e18f55a74_</uuid>\n\t\t<risk-file-flag />\n\t\t<risk-file-md5-list />\n\t</sec_msg_node>\n\t<silence>0</silence>\n\t<membercount>73</membercount>\n\t<signature>N0_V1_MWWEvmnf|v1_4A8mxtUH</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'PushContent': '小爱在群聊中发了一段视频', 'NewMsgId': 7732782944833232668, 'MsgSeq': 871411838}
2025-07-30 14:32:56 | INFO | 收到视频消息: 消息ID:1247247607 来自:48097389945@chatroom 发送人:xiaomaochong XML:
<?xml version="1.0"?>
<msg>
	<videomsg aeskey="a693d4dcc9c59941be92035fc6508d2e" cdnvideourl="3057020100044b304902010002049363814102032f51490204a731227502046889bca0042466333666656133632d333530322d346136372d383932632d3364356437633637633930310204052808040201000405004c537500" cdnthumbaeskey="a693d4dcc9c59941be92035fc6508d2e" cdnthumburl="3057020100044b304902010002049363814102032f51490204a731227502046889bca0042466333666656133632d333530322d346136372d383932632d3364356437633637633930310204052808040201000405004c537500" length="5557148" playlength="28" cdnthumblength="18674" cdnthumbwidth="405" cdnthumbheight="720" fromusername="xiaomaochong" md5="7e3a8466ac6c60df0488aaef50f17d80" newmd5="bb11183be813c53db921db79300f16e6" isplaceholder="0" rawmd5="8492efb1b4eb2dc08b0e12e122c7975b" rawlength="30193987" cdnrawvideourl="3056020100044a304802010002035a663f02032f77f502046b5b90db02046889bca0042434373932626236332d643962342d343566632d616164312d3035323236396466643830380204059400040201000405004c57c100" cdnrawvideoaeskey="f83c094df3727c46582da3bf4a4db1b0" overwritenewmsgid="0" originsourcemd5="0ed17879609fe9ba09c700101ed97f1e" isad="0" />
</msg>

2025-07-30 14:32:59 | DEBUG | 收到消息: {'MsgId': 980289136, 'FromUserName': {'string': '27852221909@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 1, 'Content': {'string': 'wxid_xrb90gbzzu5q22:\n好像还要后面改名字之类的'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1753857192, 'MsgSource': '<msgsource>\n\t<pua>1</pua>\n\t<eggIncluded>1</eggIncluded>\n\t<silence>1</silence>\n\t<membercount>145</membercount>\n\t<signature>N0_V1_3bMYiqKN|v1_OOXxG8f2</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'NewMsgId': 5297043170580618315, 'MsgSeq': 871411839}
2025-07-30 14:32:59 | INFO | 收到文本消息: 消息ID:980289136 来自:27852221909@chatroom 发送人:wxid_xrb90gbzzu5q22 @:[] 内容:好像还要后面改名字之类的
2025-07-30 14:32:59 | DEBUG | 处理消息内容: '好像还要后面改名字之类的'
2025-07-30 14:32:59 | DEBUG | 消息内容 '好像还要后面改名字之类的' 不匹配任何命令，忽略
