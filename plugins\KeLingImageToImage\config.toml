[KeLingImageToImage]
enable = true
command = ["可灵测试", "keling"]
command-format = """
🎨 可灵AI功能

📝 图生图使用方法：
• 引用图片 + 可灵 [提示词] - 处理引用的图片
• 可灵测试 [提示词] - 使用默认测试图片
• 可灵测试 - 使用默认提示词和默认测试图片

📁 测试图片位置: C:\\DBE25C6475AF6852691B040206E94167.jpg

⚙️ 处理步骤：
1. 上传图片到可灵AI
2. 提交AI处理任务
3. 等待处理结果
4. 返回处理结果

每一步都会显示详细状态信息，方便调试。
"""

[KeLingImageToImage.quote]
command = ["可灵"]
command-format = "引用图片并发送: 可灵 [提示词]"

[KeLingImageToImage.api]
# 可灵AI相关配置
base_url = "https://klingai.kuaishou.com"  # 可灵AI网站地址
api_key = "__risk_web_device_id=u207y5ze1742175243903y91; did=web_20837da80a348f94b53562717fc82ff2559b; _uetvid=e9918d3002cf11f0bcd85d4c08744fb2; _clck=vw6pi4%7C2%7Cfvq%7C0%7C1902; KLING_LAST_ACCESS_REGION=cn; userId=6217807; kuaishou.ai.portal_st=ChVrdWFpc2hvdS5haS5wb3J0YWwuc3QSoAE7U7ChMsodizGDT0Q1R7LNRSDKMgUinlU03suhOirOg0qskLg_quweY-MWVLxzggOO0n1xNpF8InVWfgG13UKz7gmnLKo8-7bmXjVfxYsdasOC2ivIaDVMfzz_QD6ivMKYqjjaPD7TOaEyv6W6u3Jna-VgwXhb2BGf3RNi5KIihfkDH6Kxe5Ql8hAYUZm2ZBNIixug3UHayxJM6hewjzOUGhJaTSf_z6v_i70Q1ZuLG30vAZsiIJibv75oMRBmFqtTHEO5QRZyxpU_k9PIOTsP6TidOQNXKAUwAQ"  # 可灵AI的Cookie字符串
model = "keling-image-generation"  # 可灵AI的图生图模型名称

[KeLingImageToImage.rate_limit]
cooldown = 15  # 请求冷却时间

# 自然化响应设置
natural_response = true  # 启用自然化响应，避免机械化回复
