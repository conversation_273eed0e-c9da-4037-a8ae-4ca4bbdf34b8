import os
import json
import time
import random
import uuid
import hmac
import hashlib
import zlib
import asyncio
import base64
from datetime import datetime, timezone
import calendar
from urllib.parse import urlencode, urlparse
from pathlib import Path
import concurrent.futures

try:
    import tomllib
except ModuleNotFoundError:
    import tomli as tomllib

import httpx
from loguru import logger

from WechatAPI import WechatAPIClient
from utils.decorators import on_text_message, on_quote_message, on_emoji_message
from utils.plugin_base import PluginBase
from utils.temp_file_manager import create_temp_file, cleanup_file, mark_file_active, mark_file_inactive


class DoubaoImageRecognizer:
    """豆包AI图片识别核心类

    负责与豆包AI API进行交互，实现图片上传和识别功能。
    包含完整的AWS签名算法实现，用于图片上传认证。
    """

    def __init__(self, cookies):
        self.cookies = self._parse_cookies(cookies)
        self.base_headers = {
            'User-Agent': 'Mozilla/5.0 (Linux; Android 10; V2002A Build/QP1A.190711.020) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/83.0.4103.106 Mobile Safari/537.36',
            'Accept': '*/*',
            'Accept-Language': 'zh-CN,zh;q=0.9,en-US;q=0.8,en;q=0.7',
            'Accept-Encoding': 'gzip, deflate',
            'Origin': 'https://www.doubao.com',
            'Referer': 'https://www.doubao.com/chat/create-image',
            'X-Requested-With': 'mark.via'
        }
        self.service_id = 'a9rns2rl98'

    def _parse_cookies(self, cookie_string):
        """解析Cookie字符串"""
        cookies = {}
        if cookie_string:
            for item in cookie_string.split(';'):
                if '=' in item:
                    key, value = item.strip().split('=', 1)
                    cookies[key] = value
        return cookies

    def _calculate_crc32(self, data):
        """计算CRC32校验值"""
        return zlib.crc32(data) & 0xffffffff

    async def get_upload_auth(self, client: httpx.AsyncClient):
        """获取上传认证token"""
        url = "https://www.doubao.com/alice/resource/prepare_upload"
        params = {
            'version_code': '20800',
            'language': 'zh',
            'device_platform': 'web',
            'aid': '497858',
            'real_aid': '497858',
            'pkg_type': 'release_version',
            'device_id': '7468716989062841895',
            'web_id': '7468716986638386703',
            'tea_uuid': '7468716986638386703',
            'use-olympus-account': '1',
            'region': 'CN',
            'sys_region': 'CN',
            'samantha_web': '1',
            'pc_version': '2.24.2'
        }
        data = {
            "tenant_id": "5",
            "scene_id": "5",
            "resource_type": 2
        }

        response = await client.post(url, params=params, json=data, timeout=30)
        logger.debug(f"上传认证请求状态码: {response.status_code}")
        logger.debug(f"上传认证响应头: {dict(response.headers)}")
        logger.debug(f"上传认证响应内容: {response.text[:500]}")

        if response.status_code != 200:
            raise Exception(f"HTTP {response.status_code}: {response.text[:200]}")

        try:
            result = response.json()
        except Exception as e:
            raise Exception(f"JSON解析失败: {str(e)}, 响应内容: {response.text[:200]}")

        if result.get('code') != 0:
            raise Exception(f"获取上传认证失败: {result}")

        return result['data']['upload_auth_token']

    async def upload_image(self, client: httpx.AsyncClient, image_path):
        """上传图片到豆包服务器"""
        try:
            with open(image_path, 'rb') as f:
                image_data = f.read()

            file_size = len(image_data)
            file_ext = os.path.splitext(image_path)[1]
            crc32_value = self._calculate_crc32(image_data)

            # 获取上传认证
            upload_auth = await self.get_upload_auth(client)

            # 申请上传
            upload_params = {
                'Action': 'ApplyImageUpload',
                'Version': '2018-08-01',
                'ServiceId': self.service_id,
                'NeedFallback': 'true',
                'FileSize': str(file_size),
                'FileExtension': file_ext,
                's': 'yy49d6n7o6p'
            }
            apply_url = f"https://imagex.bytedanceapi.com/?{urlencode(upload_params)}"

            headers = self.base_headers.copy()
            headers.update(self.generate_aws_signature('GET', apply_url, upload_auth))

            response = await client.get(apply_url, headers=headers)
            if response.status_code != 200 or 'Result' not in response.json():
                return None

            result = response.json()['Result']
            store_info = result['UploadAddress']['StoreInfos'][0]
            upload_host = result['UploadAddress']['UploadHosts'][0]

            # 上传图片数据
            upload_headers = {
                'Authorization': store_info['Auth'],
                'Content-CRC32': f"{crc32_value:08x}",
                'Content-Type': 'application/octet-stream',
                'X-Storage-U': store_info['UploadID'],
                'Origin': 'https://www.doubao.com',
                'Referer': 'https://www.doubao.com/chat/',
                'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'
            }

            response = await client.post(
                f"https://{upload_host}/upload/v1/{store_info['StoreUri']}",
                content=image_data,
                headers=upload_headers
            )

            if response.status_code != 200 or response.json().get('code') != 2000:
                return None

            # 提交上传
            commit_params = {
                'Action': 'CommitImageUpload',
                'Version': '2018-08-01',
                'ServiceId': self.service_id
            }
            commit_url = f"https://imagex.bytedanceapi.com/?{urlencode(commit_params)}"
            commit_payload = json.dumps({"SessionKey": result['UploadAddress']['SessionKey']})

            commit_headers = {
                'Content-Type': 'application/json',
                'Accept': '*/*',
                'Origin': 'https://www.doubao.com',
                'Referer': 'https://www.doubao.com/chat/',
                'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'
            }
            commit_headers.update(self.generate_aws_signature('POST', commit_url, upload_auth, commit_payload))

            response = await client.post(commit_url, content=commit_payload, headers=commit_headers)

            if response.status_code == 200 and 'Result' in response.json():
                return response.json()['Result']['Results'][0]['Uri']

            return None

        except Exception as e:
            logger.error(f"上传图片失败: {str(e)}")
            return None

    def generate_aws_signature(self, method, url, upload_auth, payload=""):
        """生成AWS签名"""
        access_key = upload_auth['access_key']
        secret_key = upload_auth['secret_key']
        session_token = upload_auth['session_token']

        parsed = urlparse(url)
        host = parsed.netloc
        path = parsed.path or '/'
        query = parsed.query

        # 使用UTC时间
        t = datetime.now(timezone.utc)
        amz_date = t.strftime('%Y%m%dT%H%M%SZ')
        date_stamp = t.strftime('%Y%m%d')

        # 构建规范查询字符串
        if query:
            query_params = []
            for param in query.split('&'):
                if '=' in param:
                    k, v = param.split('=', 1)
                    query_params.append((k, v))
                else:
                    query_params.append((param, ''))
            canonical_querystring = '&'.join([f'{k}={v}' for k, v in sorted(query_params)])
        else:
            canonical_querystring = ''

        # 构建规范头部
        canonical_headers = f'host:{host}\nx-amz-date:{amz_date}\nx-amz-security-token:{session_token}\n'
        signed_headers = 'host;x-amz-date;x-amz-security-token'

        # 计算载荷哈希
        payload_hash = hashlib.sha256(payload.encode('utf-8')).hexdigest()

        # 构建规范请求
        canonical_request = f'{method}\n{path}\n{canonical_querystring}\n{canonical_headers}\n{signed_headers}\n{payload_hash}'

        # 构建签名字符串
        algorithm = 'AWS4-HMAC-SHA256'
        credential_scope = f'{date_stamp}/cn-north-1/imagex/aws4_request'
        string_to_sign = f'{algorithm}\n{amz_date}\n{credential_scope}\n{hashlib.sha256(canonical_request.encode()).hexdigest()}'

        # 计算签名
        def sign(key, msg):
            return hmac.new(key.encode('utf-8') if isinstance(key, str) else key, msg.encode('utf-8'), hashlib.sha256).digest()

        def get_signature_key(key, date_stamp, region_name, service_name):
            kDate = sign('AWS4' + key, date_stamp)
            kRegion = sign(kDate, region_name)
            kService = sign(kRegion, service_name)
            kSigning = sign(kService, 'aws4_request')
            return kSigning

        signing_key = get_signature_key(secret_key, date_stamp, 'cn-north-1', 'imagex')
        signature = hmac.new(signing_key, string_to_sign.encode('utf-8'), hashlib.sha256).hexdigest()

        # 构建授权头部
        authorization_header = f'{algorithm} Credential={access_key}/{credential_scope}, SignedHeaders={signed_headers}, Signature={signature}'

        return {
            'Authorization': authorization_header,
            'X-Amz-Date': amz_date,
            'x-amz-security-token': session_token
        }

    async def recognize_image(self, client: httpx.AsyncClient, image_uri, prompt="解释图片"):
        """识别图片内容"""
        try:
            # 基础参数
            base_params = {
                'version_code': '20800',
                'language': 'zh',
                'device_platform': 'web',
                'aid': '497858',
                'real_aid': '497858',
                'pkg_type': 'release_version',
                'device_id': '7468716989062841895',
                'web_id': '7468716986638386703',
                'tea_uuid': '7468716986638386703',
                'use-olympus-account': '1',
                'region': 'CN',
                'sys_region': 'CN',
                'samantha_web': '1',
                'pc_version': '2.24.2'
            }

            # 构建请求数据
            request_data = {
                "messages": [{
                    "content": json.dumps({"text": prompt}),
                    "content_type": 2001,
                    "attachments": [{
                        "type": "vlm_image",
                        "identifier": str(uuid.uuid4()),
                        "name": f"image_{int(time.time())}.jpg",
                        "key": image_uri,
                        "file_review_state": 3,
                        "file_parse_state": 3,
                        "option": {"width": 480, "height": 480}
                    }],
                    "references": []
                }],
                "completion_option": {
                    "is_regen": False,
                    "with_suggest": True,
                    "need_create_conversation": True,
                    "launch_stage": 1,
                    "is_replace": False,
                    "is_delete": False,
                    "message_from": 0,
                    "use_deep_think": False,
                    "use_auto_cot": False,
                    "resend_for_regen": False,
                    "event_id": "0"
                },
                "conversation_id": "0",
                "local_conversation_id": f"local_{int(time.time() * 1000000)}",
                "local_message_id": str(uuid.uuid4())
            }

            # 设置请求头
            headers = self.base_headers.copy()
            headers.update({
                'Content-Type': 'application/json',
                'Accept': 'application/json, text/plain, */*',
                'x-flow-trace': f"04-{uuid.uuid4().hex[:16]}-{uuid.uuid4().hex[:16]}-01",
                'Agw-Js-Conv': 'str',
                'last-event-id': 'undefined',
                'Sec-Fetch-Site': 'same-origin',
                'Sec-Fetch-Mode': 'cors',
                'Sec-Fetch-Dest': 'empty'
            })

            # 发送请求
            async with client.stream(
                'POST',
                f"https://www.doubao.com/samantha/chat/completion?{urlencode(base_params)}",
                params=base_params,
                json=request_data,
                headers=headers,
                timeout=300
            ) as response:

                if response.status_code != 200:
                    return None

                # 处理流式响应
                start_time = time.time()
                text_response = ""

                async for raw_line in response.aiter_lines():
                    if time.time() - start_time > 60:  # 60秒超时
                        break

                    try:
                        line = raw_line
                    except:
                        line = str(raw_line, errors='replace')

                    if not line.strip() or not line.startswith('data: '):
                        continue

                    try:
                        data_str = line[6:].strip()
                        if not data_str or data_str == '[DONE]':
                            continue

                        event_data = json.loads(data_str)
                        if 'event_data' in event_data and event_data.get('event_type') == 2001:
                            try:
                                inner_data = json.loads(event_data['event_data'])
                            except:
                                continue

                            if 'message' in inner_data:
                                message = inner_data['message']
                                if message.get('content_type') == 2001:
                                    try:
                                        content = json.loads(message['content'])
                                        if 'text' in content and content['text']:
                                            text_response += content['text']
                                    except:
                                        pass

                                # 检查是否完成
                                if (inner_data.get('status') == 1 and
                                    inner_data.get('is_finish') and
                                    'tts_content' in inner_data):
                                    return inner_data['tts_content'].strip() if inner_data['tts_content'] else text_response.strip()

                    except:
                        continue

                return text_response.strip() if text_response.strip() else None

        except Exception as e:
            logger.error(f"识别图片失败: {str(e)}")
            return None

    async def process_recognition(self, image_path, prompt="解释图片"):
        """完整的图片识别流程"""
        async with httpx.AsyncClient(cookies=self.cookies, timeout=300) as client:
            image_uri = await self.upload_image(client, image_path)
            return await self.recognize_image(client, image_uri, prompt) if image_uri else None


class DoubaoImageRecognition(PluginBase):
    """豆包AI图片识别插件主类

    从DouBaoImageToImage插件中移植的图片识别功能，
    专门用于图片内容识别，支持文本命令和引用消息识别。

    主要功能：
    - 文本命令识图：识图 [提示词]
    - 引用图片识图：引用图片 + 识图 [提示词]
    - 表情包识图：引用表情 + 识图 [提示词]
    - 自然化响应和限流控制
    """
    
    description = "豆包AI图片识别功能，支持文本命令和引用图片识别"
    author = "XYBot"
    version = "1.0.0"
    plugin_name = "DoubaoImageRecognition"

    def __init__(self):
        super().__init__()
        self.temp_dir = Path("temp/doubao_image_recognition")
        self.temp_dir.mkdir(parents=True, exist_ok=True)
        self._load_config()
        self.user_last_request = {}
        self.user_request_count = {}
        self.emoji_message_cache = {}  # 缓存表情消息信息

    def _load_config(self):
        """加载配置文件"""
        try:
            config_path = f"plugins/{self.plugin_name}/config.toml"
            with open(config_path, "rb") as f:
                config = tomllib.load(f).get(self.plugin_name, {})
        except Exception as e:
            logger.error(f"[{self.plugin_name}] 配置文件加载失败: {str(e)}")
            config = {}
        
        # 基础配置
        self.enable = config.get("enable", True)
        self.command = config.get("command", ["识图", "豆包识图"])
        self.command_format = config.get("command-format", "识图 [提示词] 或 引用图片并发送: 识图 [提示词]")
        
        # API配置
        self.api_config = config.get("api", {})
        self.base_url = self.api_config.get("base_url", "https://www.doubao.com")
        self.cookies = self.api_config.get("api_key", "")
        self.model = self.api_config.get("model", "doubao-image-recognition")
        
        # 限流配置
        self.cooldown = config.get("rate_limit", {}).get("cooldown", 15)
        
        # 自然响应配置
        self.natural_response = config.get("natural_response", True)
        self._init_natural_responses()

    def _init_natural_responses(self):
        """初始化自然响应消息"""
        self.confirm_msgs = ["好的", "收到", "明白了", "嗯", "知道了", "行", "OK"]
        self.error_msgs = ["这可把我难住了", "搞不定了", "这个有点难", "弄不了", "出问题了", "搞不出来", "这个不行"]
        self.rate_limit_msgs = ["你搁这刷bug呢", "慢点慢点", "别这么急", "一个一个来", "太快了太快了", "歇会儿", "别刷了"]

    def _check_rate_limit(self, user_wxid):
        """检查用户是否触发限流"""
        current_time = time.time()

        # 检查用户上次请求时间
        if user_wxid in self.user_last_request:
            time_diff = current_time - self.user_last_request[user_wxid]
            if time_diff < self.cooldown:
                return True

        # 更新用户请求时间
        self.user_last_request[user_wxid] = current_time
        return False

    async def _simple_confirm(self, bot, wxid, custom_message=None):
        """发送确认消息"""
        if custom_message:
            await bot.send_text_message(wxid, custom_message)
        elif self.natural_response:
            await bot.send_text_message(wxid, random.choice(self.confirm_msgs))

    async def _send_usage_instructions(self, bot, wxid, user_wxid):
        """发送使用说明"""
        usage_message = f"""🔍 豆包AI图片识别使用说明

📝 使用方法：
• {' / '.join(self.command)} [提示词] - 使用默认测试图片识别
• 引用图片 + {self.command[0]} [提示词] - 识别引用的图片
• {self.command[0]} - 使用默认提示词"解释图片"

💡 示例：
• 识图 这是什么
• 识图 详细描述图片内容
• 识图 分析图片中的文字

📁 测试图片位置: C:\\DBE25C6475AF6852691B040206E94167.jpg"""

        await bot.send_at_message(wxid, usage_message, [user_wxid])

    @on_text_message
    async def handle_text(self, bot: WechatAPIClient, message: dict):
        """处理文本消息"""
        if not self.enable:
            return

        content = str(message["Content"]).strip()
        wxid = message["FromWxid"]
        user_wxid = message["SenderWxid"]

        # 检查是否是帮助命令
        if content in ["识图帮助", "识图说明", "识图指令", "豆包识图帮助", "豆包识图说明", "豆包识图指令"]:
            await self._send_usage_instructions(bot, wxid, user_wxid)
            return

        # 检查是否是插件命令
        command_parts = content.split(" ", 2)  # 分割成三部分：命令、提示词、图片路径

        # 识图命令处理
        if command_parts[0] in self.command:
            # 默认测试图片路径
            default_image_path = "C:\\DBE25C6475AF6852691B040206E94167.jpg"

            if len(command_parts) == 1:
                # 如果只有"识图"，使用默认提示词
                prompt = "解释图片"
                image_path = default_image_path
            elif len(command_parts) == 2:
                # 如果是"识图 [提示词]"，使用指定提示词和默认图片路径
                prompt = command_parts[1].strip()
                image_path = default_image_path
            else:
                # 如果是完整格式"识图 [提示词] [图片路径]"
                prompt = command_parts[1].strip()
                image_path = command_parts[2].strip()

            # 检查限流
            if self._check_rate_limit(user_wxid):
                if self.natural_response:
                    rate_limit_msg = random.choice(self.rate_limit_msgs)
                    await bot.send_text_message(wxid, rate_limit_msg)
                else:
                    await bot.send_at_message(wxid, "⏳ 请求太频繁，请稍后再试", [user_wxid])
                return

            # 检查图片是否存在
            if not os.path.exists(image_path):
                await bot.send_at_message(
                    wxid,
                    f"❌ 图片不存在: {image_path}\n请确认图片路径是否正确",
                    [user_wxid]
                )
                # 发送使用说明
                await self._send_usage_instructions(bot, wxid, user_wxid)
                return

            try:
                # 执行识图流程
                await self._recognition_flow(bot, wxid, user_wxid, prompt, image_path)
            except Exception as e:
                logger.error(f"[{self.plugin_name}] 识图处理失败: {str(e)}")
                import traceback
                logger.error(f"[{self.plugin_name}] 异常详情: {traceback.format_exc()}")
                await bot.send_at_message(
                    wxid,
                    f"❌ 识图处理失败: {str(e)}",
                    [user_wxid]
                )
            return

    @on_quote_message
    async def handle_quote(self, bot: WechatAPIClient, message: dict):
        """处理引用消息"""
        if not self.enable:
            return

        content = str(message.get("Content", "")).strip()
        wxid = message.get("FromWxid", "")
        user_wxid = message.get("SenderWxid", "")

        # 检查是否是识图命令
        command_parts = content.split(maxsplit=1)
        if not command_parts:
            return

        # 检查是否是识图命令
        if command_parts[0] not in self.command:
            return

        # 检查限流
        if self._check_rate_limit(user_wxid):
            if self.natural_response:
                rate_limit_msg = random.choice(self.rate_limit_msgs)
                await bot.send_text_message(wxid, rate_limit_msg)
            else:
                await bot.send_at_message(wxid, "请求太频繁，等会再试试", [user_wxid])
            return

        # 设置提示词
        default_prompt = "解释图片"
        prompt = default_prompt
        if len(command_parts) > 1:
            prompt = command_parts[1].strip()

        logger.info(f"[{self.plugin_name}] 引用图片识图 - 提示词: '{prompt}'")

        # 获取引用的消息信息
        quote_info = message.get("Quote", {})
        quoted_msg_id = quote_info.get("Msgid") or quote_info.get("NewMsgId")

        # 检查被引用消息的类型 (3表示图片消息，47表示表情消息)
        quoted_msg_type = quote_info.get("MsgType")

        if quoted_msg_type not in [3, 47]:
            await bot.send_at_message(
                wxid,
                "❌ 请引用图片消息或表情消息",
                [user_wxid]
            )
            return

        try:
            # 下载引用的图片
            if quoted_msg_type == 3:
                # 图片消息 - 传入引用消息的内容
                quote_content = quote_info.get("Content", "")
                image_path = await self._download_quoted_image(bot, quote_content)
            else:
                # 表情消息
                image_path = await self._download_quoted_emoji(bot, quoted_msg_id)

            if not image_path:
                await bot.send_at_message(
                    wxid,
                    "❌ 图片下载失败，请重试",
                    [user_wxid]
                )
                return

            # 执行识图流程
            if quoted_msg_type == 3:
                await self._recognition_flow(bot, wxid, user_wxid, prompt, image_path)
            else:
                await self._recognition_flow_without_notification(bot, wxid, user_wxid, prompt, image_path)

        except Exception as e:
            logger.error(f"[{self.plugin_name}] 引用消息处理失败: {str(e)}")
            import traceback
            logger.error(f"[{self.plugin_name}] 异常详情: {traceback.format_exc()}")
            await bot.send_at_message(
                wxid,
                f"❌ 处理失败: {str(e)}",
                [user_wxid]
            )

    async def _download_quoted_image(self, bot: WechatAPIClient, quote_content: str) -> str:
        """下载引用的图片消息"""
        try:
            if not quote_content:
                logger.error(f"[{self.plugin_name}] 引用消息内容为空")
                return None

            # 解析XML内容获取图片参数
            import xml.etree.ElementTree as ET
            try:
                root = ET.fromstring(quote_content)
                img_node = root.find('.//img')

                if img_node is None:
                    # 尝试从refermsg中获取
                    refermsg = root.find('.//refermsg')
                    if refermsg is not None and refermsg.find('content') is not None:
                        content_text = refermsg.find('content').text
                        if content_text:
                            try:
                                inner_root = ET.fromstring(content_text)
                                img_node = inner_root.find('.//img')
                            except:
                                pass

                if img_node is None:
                    logger.error(f"[{self.plugin_name}] 无法找到图片节点")
                    return None

                # 提取图片参数
                aeskey = img_node.get('aeskey')
                cdnmidimgurl = img_node.get('cdnmidimgurl')

                if not aeskey or not cdnmidimgurl:
                    logger.error(f"[{self.plugin_name}] 无法提取图片下载参数")
                    return None

                # 使用API下载图片
                image_base64 = await bot.download_image(aeskey, cdnmidimgurl)
                if not image_base64:
                    logger.error(f"[{self.plugin_name}] 下载图片失败")
                    return None

                # 保存图片到临时文件
                image_data = base64.b64decode(image_base64)
                temp_file = self.temp_dir / f"quoted_image_{int(time.time())}.jpg"
                with open(temp_file, "wb") as f:
                    f.write(image_data)

                return str(temp_file)

            except ET.ParseError as e:
                logger.error(f"[{self.plugin_name}] 解析XML失败: {str(e)}")
                return None

        except Exception as e:
            logger.error(f"[{self.plugin_name}] 下载引用图片失败: {str(e)}")
            return None

    async def _download_quoted_emoji(self, bot: WechatAPIClient, quoted_msg_id: str) -> str:
        """下载引用的表情消息"""
        try:
            # 从缓存中获取表情信息
            if str(quoted_msg_id) not in getattr(self, 'emoji_message_cache', {}):
                logger.warning(f"[{self.plugin_name}] 未找到表情消息缓存: {quoted_msg_id}")
                return None

            emoji_info = self.emoji_message_cache[str(quoted_msg_id)]
            emoji_url = emoji_info.get('EmojiUrl')
            emoji_md5 = emoji_info.get('EmojiMD5')
            aeskey = emoji_info.get('aeskey')

            if not emoji_url or not emoji_md5:
                logger.error(f"[{self.plugin_name}] 表情信息不完整")
                return None

            # 使用微信API下载表情
            if aeskey:
                # 如果有aeskey，使用加密下载
                emoji_base64 = await bot.download_image(aeskey, emoji_url)
            else:
                # 直接下载
                try:
                    async with httpx.AsyncClient() as client:
                        response = await client.get(emoji_url)
                        if response.status_code == 200:
                            emoji_base64 = base64.b64encode(response.content).decode()
                        else:
                            logger.error(f"[{self.plugin_name}] 下载表情失败: HTTP {response.status_code}")
                            return None
                except Exception as e:
                    logger.error(f"[{self.plugin_name}] 直接下载表情失败: {str(e)}")
                    return None

            if not emoji_base64:
                logger.error(f"[{self.plugin_name}] 表情下载失败")
                return None

            # 保存表情文件
            temp_file = self.temp_dir / f"emoji_{emoji_md5}_{int(time.time())}.bin"
            emoji_data = base64.b64decode(emoji_base64)
            with open(temp_file, "wb") as f:
                f.write(emoji_data)

            return str(temp_file)

        except Exception as e:
            logger.error(f"[{self.plugin_name}] 下载表情时出错: {str(e)}")
            return None

    async def _recognition_flow(self, bot, wxid, user_wxid, prompt, image_path):
        """识图流程（带通知）"""
        try:
            logger.info(f"[{self.plugin_name}] 开始豆包AI识图流程，用户: {user_wxid}, 提示词: {prompt}")

            # 步骤1: 验证图片文件
            logger.info(f"[{self.plugin_name}] 步骤1: 验证图片文件...")
            if not os.path.exists(image_path):
                logger.error(f"[{self.plugin_name}] 图片文件不存在: {image_path}")
                await bot.send_at_message(
                    wxid,
                    "❌ 图片文件不存在",
                    [user_wxid]
                )
                return

            # 发送开始处理的确认消息
            await self._simple_confirm(bot, wxid)

            # 步骤2: 调用豆包AI识图接口
            logger.info(f"[{self.plugin_name}] 步骤2: 调用豆包AI识图接口...")
            text_response = await self._call_doubao_recognition(prompt, image_path)

            if text_response:
                logger.info(f"[{self.plugin_name}] 豆包AI识图完成")
                # 发送识图结果
                await bot.send_at_message(wxid, text_response, [user_wxid])
            else:
                logger.warning(f"[{self.plugin_name}] 豆包AI识图失败")
                await bot.send_at_message(
                    wxid,
                    "豆包AI识图失败，等会再试试吧",
                    [user_wxid]
                )

        except Exception as e:
            logger.error(f"[{self.plugin_name}] 识图处理失败: {str(e)}")
            import traceback
            logger.error(f"[{self.plugin_name}] 异常详情: {traceback.format_exc()}")
            await bot.send_at_message(
                wxid,
                f"❌ 识图处理失败: {str(e)}",
                [user_wxid]
            )

    async def _recognition_flow_without_notification(self, bot, wxid, user_wxid, prompt, image_path):
        """识图流程（无通知）"""
        try:
            logger.info(f"[{self.plugin_name}] 开始豆包AI识图流程(无通知)，用户: {user_wxid}, 提示词: {prompt}")

            # 步骤1: 验证图片文件
            logger.info(f"[{self.plugin_name}] 步骤1: 验证图片文件...")
            if not os.path.exists(image_path):
                logger.error(f"[{self.plugin_name}] 图片文件不存在: {image_path}")
                await bot.send_at_message(
                    wxid,
                    "❌ 图片文件不存在",
                    [user_wxid]
                )
                return

            # 步骤2: 调用豆包AI识图接口
            logger.info(f"[{self.plugin_name}] 步骤2: 调用豆包AI识图接口...")
            text_response = await self._call_doubao_recognition(prompt, image_path)

            if text_response:
                logger.info(f"[{self.plugin_name}] 豆包AI识图完成")
                # 发送识图结果
                await bot.send_text_message(wxid, text_response)
            else:
                logger.warning(f"[{self.plugin_name}] 豆包AI识图失败")
                await bot.send_at_message(
                    wxid,
                    "豆包AI识图失败，等会再试试吧",
                    [user_wxid]
                )

        except Exception as e:
            logger.error(f"[{self.plugin_name}] 识图处理失败: {str(e)}")
            import traceback
            logger.error(f"[{self.plugin_name}] 异常详情: {traceback.format_exc()}")
            await bot.send_at_message(
                wxid,
                f"❌ 识图处理失败: {str(e)}",
                [user_wxid]
            )

    async def _call_doubao_recognition(self, prompt, image_path):
        """调用豆包AI识图接口"""
        try:
            # 检查Cookie配置
            if not self.cookies:
                logger.error(f"[{self.plugin_name}] 豆包AI Cookie未配置")
                return None

            # 创建豆包AI识别器实例
            recognizer = DoubaoImageRecognizer(self.cookies)

            # 异步调用识图接口
            text_response = await recognizer.process_recognition(image_path, prompt)

            # 识图完成后清理临时文件（延迟5秒，确保处理完成）
            if image_path.startswith(str(self.temp_dir)):
                cleanup_file(image_path, delay_seconds=5)

            if text_response:
                logger.info(f"[{self.plugin_name}] 豆包AI识图成功")
                return text_response
            else:
                logger.warning(f"[{self.plugin_name}] 豆包AI识图失败")
                return None

        except Exception as e:
            logger.error(f"[{self.plugin_name}] 调用豆包AI识图接口异常: {str(e)}")
            import traceback
            logger.error(f"[{self.plugin_name}] 异常详情: {traceback.format_exc()}")
            return None

    @on_emoji_message
    async def handle_emoji(self, bot: WechatAPIClient, message: dict):
        """处理表情消息，缓存表情信息以供后续引用使用"""
        if not self.enable:
            return

        try:
            msg_id = message.get('NewMsgId') or message.get('MsgId')
            if not msg_id:
                return

            # 提取表情信息
            emoji_url = message.get('EmojiUrl', '')
            emoji_md5 = message.get('EmojiMD5', '')

            # 从Content中提取aeskey（如果有的话）
            content = message.get('Content', '')
            aeskey = None
            if 'aeskey=' in content:
                try:
                    import re
                    aeskey_match = re.search(r'aeskey="([^"]*)"', content)
                    if aeskey_match:
                        aeskey = aeskey_match.group(1)
                except:
                    pass

            # 缓存表情信息
            emoji_info = {
                'EmojiUrl': emoji_url,
                'EmojiMD5': emoji_md5,
                'aeskey': aeskey,
                'timestamp': time.time()
            }

            self.emoji_message_cache[str(msg_id)] = emoji_info
            logger.debug(f"[{self.plugin_name}] 缓存表情消息: {msg_id}")

            # 清理过期的缓存（保留最近1小时的）
            current_time = time.time()
            expired_keys = []
            for key, info in self.emoji_message_cache.items():
                if current_time - info.get('timestamp', 0) > 3600:  # 1小时
                    expired_keys.append(key)

            for key in expired_keys:
                del self.emoji_message_cache[key]

        except Exception as e:
            logger.error(f"[{self.plugin_name}] 处理表情消息失败: {str(e)}")
            import traceback
            logger.error(f"[{self.plugin_name}] 异常详情: {traceback.format_exc()}")
