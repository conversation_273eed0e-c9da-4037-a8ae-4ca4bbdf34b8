import asyncio
from pathlib import Path
import sys
from os.path import dirname, abspath

# 添加项目根目录到Python路径
root_dir = dirname(dirname(dirname(abspath(__file__))))
sys.path.append(root_dir)

from plugins.text2card_project.image_merge_service import ImageMergeService

async def test_merge():
    """测试图片合并效果"""
    
    # 创建服务实例
    merger = ImageMergeService()
    
    # 获取当前目录
    current_dir = Path(__file__).parent
    
    # 垂直合并测试
    try:
        result = await merger.merge_images(
            images=[
                current_dir / "test_images/1.jpg",
                current_dir / "test_images/2.jpg"
            ],
            output_path=current_dir / "test_output/merged_vertical.png",
            direction='vertical',  # 改为垂直合并
            spacing=50,  # 图片间距
            enhance=True,
            radius=30,
            align='center'  # 水平居中对齐
        )
        print(f"垂直合并完成，输出: {result}")
        
    except Exception as e:
        print(f"合并失败: {e}")

if __name__ == "__main__":
    asyncio.run(test_merge()) 