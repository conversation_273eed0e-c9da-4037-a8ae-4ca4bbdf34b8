[VideoParserPlugin]
# 基本配置
enable = true
command = ["解析"]
cache-time = 3600  # 缓存时间(秒)
max-retries = 3  # 最大重试次数

# 语音转换API配置
voice-api = "http://www.yx520.ltd/API/wzzyy/zmp3.php"

# API配置
video-number-api = "https://api.dudunas.top/api/vxdown"
video-number-secret = "bf91ad9748b3be2cdd48cca599d5446d"
local-api = "http://192.168.100.1:8080/video/share/url/parse?url="
dragon-api = "https://www.hhlqilongzhu.cn/api/sp_jx/sp.php?url="
tuji-api = "http://192.168.100.1:8080/video/share/url/parse?url="
summary-api = "http://www.yx520.ltd/API/doubao/db.php"
summary-qq = "741628350"
summary-key = "wVV5tJhF041I6kj2"

# 备用API列表
backup-apis = [
    "https://api.example1.com/parse?url=",
    "https://api.example2.com/parse?url="
]

# 播放器配置
player-prefix = "http://www.yx520.ltd/bfq/api.php?url="
backup-players = [
    "http://play1.example.com/player?url=",
    "http://play2.example.com/player?url="
]

# 短链接API配置
short-url-api = "https://api.sumt.cn/api/url/"
short-url-token = "your_token_here"

command-format = """
⚙️视频解析插件使用说明：

🎬 视频解析:
引用视频 + "解析" - 解析视频链接
引用视频号 + "视频号" - 解析视频号内容

📷 图片相关:
引用图片 + "识图" - 识别图片内容
引用图集 + "取图集" - 提取图集内容

📝 文章处理:
引用文章 + "总结一下" - 总结文章内容

🎯 支持平台:
抖音、快手、B站、视频号、微博视频、小红书、皮皮虾、微视、全民K歌、腾讯视频、优酷、爱奇艺、西瓜视频、最右、快影
"""