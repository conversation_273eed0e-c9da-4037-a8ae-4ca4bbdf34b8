[YaoyaoPlugin]
enable = true
command = ["瑶瑶", "yy"]
command-format = """
我是群聊的管家瑶妹，专门负责让咱们的聊天环境更加和谐有趣。

💡使用说明：
- 群聊中发送"瑶瑶"进入会话模式
- 直接说话就可以和我聊天
- 说"瑶瑶再见"结束对话

需要帮忙或者有什么疑问，随时可以找我哦！✨
"""

# TTS配置
tts-url = "http://www.yx520.ltd/API/wzzyy/zmp3.php"
tts-voice = "318"  # 默认音色

# API配置
api-url = "https://agents.vivo.com.cn/gpts-v2-api/chat/v1/completions/web/stream/public"
app-id = "**********"
app-name = "瑶妹"
agent-id = ********
uid = "d41d8cd98f00b204e9800998ecf8427e"

# 登录凭证
csrf-token = "631e52060fcb7b3da08350978e76d765.*************"  # 登录凭证，自动更新
cookies = "vivo_account_cookie_iqoo_deviceid=wb_84d83ded-fcee-4a37-a0de-ae917b23bb3f; clientId=199; findPwdRandomNum=f9350811f485a826; question1=%E6%82%A8%E6%9C%80%E7%86%9F%E6%82%89%E7%9A%84%E7%AB%A5%E5%B9%B4%E5%A5%BD%E5%8F%8B%E7%9A%84%E5%90%8D%E5%AD%97%EF%BC%9F; question2=%E6%82%A8%E9%AB%98%E4%B8%AD%E7%8F%AD%E4%B8%BB%E4%BB%BB%E7%9A%84%E5%90%8D%E5%AD%97%EF%BC%9F; question3=%E5%90%AF%E8%92%99%E8%80%81%E5%B8%88%E7%9A%84%E5%90%8D%E5%AD%97%EF%BC%9F; findpwdType=8; account=MTM2OTY0MjY3MTM=; log_r_flow_no=PASSPORT20250218105707747; log_r_reuslt=HIGHRISK; pwd=aGdnR2ZpOUwybmRiMEJHTUdnOVc5aWFBaGQ5b1o4dnBiNzJVemQ3T3hkQVV6MXZjMllHSXpxNHdiZDJieEd5QysyQjJPSGxsaEJlN2UvcmFobXlzRkhZVWNMd1BmVThzejZjWHFOcWRWNnVnR0J0aTNaNmRMYkladHBQbE5WNzVmdzNhSnV6eVN2Uy96b0xYWHFBR2RFSlY0YmNvTS9qZnp2Mld0cytSWWVlTQ==; lr_wb_84d83ded-fcee-4a37-a0de-ae917b23bb3f=3f048382bd8fd2a98c13c9006b5f3354; openid=427d539267f04864; vivo_account_cookie_iqoo_checksum=631e52060fcb7b3da08350978e76d765.*************; vivo_account_cookie_iqoo_openid=427d539267f04864; vivo_account_cookie_iqoo_authtoken=**********************************************************; vivo_account_cookie_iqoo_vivotoken=631e52060fcb7b3da08350978e76d765.*************; vivo_account_cookie_iqoo_regioncode=CN"    # 登录凭证，自动更新

# 自动更新配置
username = ""  # vivo账号
password = ""  # vivo密码
auto-refresh = true  # 是否启用自动刷新

# 私聊黑名单配置
# 在此列表中的用户将无法在私聊中触发瑶瑶回复
# 管理员可以通过以下命令动态管理黑名单：
# - 瑶瑶黑名单添加 wxid_xxx
# - 瑶瑶黑名单移除 wxid_xxx
# - 瑶瑶黑名单查看
# - 瑶瑶管理帮助
private-blacklist = [
    "gh_a5cf07d474f1",  # 公众号：不回复此公众号的私聊消息
    "gh_ba3381c847b3",  # 公众号：唱舞星计划相关公众号
    # "wxid_ubbh6q832tcs21",  # 示例：不回复此用户的私聊消息
    # "wxid_example2",  # 可以添加多个用户的wxid
]

# 限流配置
[YaoyaoPlugin.rate_limit]
tokens_per_second = 0.1  # 每10秒生成1个令牌
bucket_size = 3  # 令牌桶容量 