2025-07-22 18:49:23 | SUCCESS | 读取主设置成功
2025-07-22 18:49:23 | DEBUG | 最终使用的 Redis 主机地址: 127.0.0.1
2025-07-22 18:49:23 | INFO | 2025/07/22 18:49:23 GetRedisAddr: 127.0.0.1:6379
2025-07-22 18:49:23 | INFO | 2025/07/22 18:49:23 Redis连接成功 | 地址:127.0.0.1:6379 DB:0
2025-07-22 18:49:23 | INFO | 2025/07/22 18:49:23 Server start at :9000
2025-07-22 18:49:24 | SUCCESS | WechatAPI服务已启动
2025-07-22 18:49:24 | INFO | 登录账号信息: wxid: wxid_4usgcju5ey9q29  昵称: 瑶瑶  微信号: Today-KFC  手机号: None
2025-07-22 18:49:24 | INFO | 登录设备信息: device_name: <PERSON>'s Pad  device_id: 49d050a7e94d1ecc40790d74b1c4369f
2025-07-22 18:49:24 | SUCCESS | 登录成功
2025-07-22 18:49:24 | SUCCESS | 已开启自动心跳
2025-07-22 18:49:24 | INFO | 成功加载表情映射文件，共 521 条记录
2025-07-22 18:49:24 | SUCCESS | 数据库初始化成功
2025-07-22 18:49:24 | SUCCESS | 定时任务已启动
2025-07-22 18:49:24 | SUCCESS | 插件 AISignIn 已加载定时任务: {'plugins.AISignIn.main.AISignIn.auto_signin_task'}
2025-07-22 18:49:24 | INFO | 已加载管理员列表: ['wxid_ubbh6q832tcs21', 'admin-wxid']
2025-07-22 18:49:25 | INFO | 播客API初始化成功
2025-07-22 18:49:25 | INFO | 加载配置完成，转换后的数据: {'gh_7057516c9e71': {'name': '唱舞全明星', 'groups': ['27852221909@chatroom', '55878994168@chatroom', '51891329927@chatroom']}, 'gh_d4d21a0e4623': {'name': '两颗桃', 'groups': ['48097389945@chatroom']}, 'gh_410ff2fa6ec9': {'name': 'NGC660 Ai研究院', 'groups': ['47442567074@chatroom']}, 'gh_f78c53c507c0': {'name': '不正常吐槽君', 'groups': ['48097389945@chatroom']}, 'gh_ba3381c847b3': {'name': '唱舞星计划', 'groups': ['51891329927@chatroom']}, 'gh_1a102fb4cad9': {'name': '我在娱乐圈的唯一人脉', 'groups': ['48097389945@chatroom', '27852221909@chatroom']}}
2025-07-22 18:49:25 | INFO | ArticleForwarder插件初始化完成 - 监控配置: {'gh_7057516c9e71': {'name': '唱舞全明星', 'groups': ['27852221909@chatroom', '55878994168@chatroom', '51891329927@chatroom']}, 'gh_d4d21a0e4623': {'name': '两颗桃', 'groups': ['48097389945@chatroom']}, 'gh_410ff2fa6ec9': {'name': 'NGC660 Ai研究院', 'groups': ['47442567074@chatroom']}, 'gh_f78c53c507c0': {'name': '不正常吐槽君', 'groups': ['48097389945@chatroom']}, 'gh_ba3381c847b3': {'name': '唱舞星计划', 'groups': ['51891329927@chatroom']}, 'gh_1a102fb4cad9': {'name': '我在娱乐圈的唯一人脉', 'groups': ['48097389945@chatroom', '27852221909@chatroom']}}
2025-07-22 18:49:25 | DEBUG | [TempFileManager] 添加清理规则: default
2025-07-22 18:49:25 | DEBUG | [TempFileManager] 添加清理规则: images
2025-07-22 18:49:25 | DEBUG | [TempFileManager] 添加清理规则: videos
2025-07-22 18:49:25 | DEBUG | [TempFileManager] 添加清理规则: audio
2025-07-22 18:49:25 | DEBUG | [TempFileManager] 添加清理规则: temp
2025-07-22 18:49:25 | DEBUG | [TempFileManager] 添加清理规则: large_files
2025-07-22 18:49:25 | INFO | [TempFileManager] 临时文件清理任务已启动
2025-07-22 18:49:25 | INFO | [ChatSummary] 数据库初始化成功
2025-07-22 18:49:25 | INFO | 成功加载表情映射文件，共 521 条记录
2025-07-22 18:49:25 | SUCCESS | 插件 GoodMorning 已加载定时任务: {'plugins.GoodMorning.main.GoodMorning.daily_task'}
2025-07-22 18:49:25 | SUCCESS | 插件 News 已加载定时任务: {'plugins.News.main.News.night_news', 'plugins.News.main.News.noon_news'}
2025-07-22 18:49:25 | INFO | [PatReply] AMR文件已存在且是最新的: C:\XYBotV2\data\paiyipai\1_BFB5258B-2B13-4CF4-A40C-5BCCE74E5414_converted.amr
2025-07-22 18:49:25 | DEBUG | [PatReply] 从缓存加载base64: 18228 字符
2025-07-22 18:49:25 | SUCCESS | 插件 QuarkSignIn 已加载定时任务: {'plugins.QuarkSignIn.main.QuarkSignIn.auto_signin_task'}
2025-07-22 18:49:25 | INFO | [QuarkSignIn] 已设置自动签到时间: 08:00
2025-07-22 18:49:25 | INFO | 插件状态: 启用, 检查间隔: 3600秒, 最大重试次数: 3, 重试延迟: 5秒
2025-07-22 18:49:25 | INFO | 已加载管理员列表: ['wxid_ubbh6q832tcs21', 'admin-wxid']
2025-07-22 18:49:25 | INFO | 已加载 2 个改名监控群和 1 个退群监控群
2025-07-22 18:49:25 | INFO | [RenameReminder] 开始启用插件...
2025-07-22 18:49:25 | SUCCESS | 插件 RenameReminder 已加载定时任务: {'plugins.RenameReminder.main.RenameReminder.check_name_changes'}
2025-07-22 18:49:25 | INFO | [RenameReminder] 插件已启用，开始初始化...
2025-07-22 18:49:25 | INFO | [RenameReminder] 支持的命令: 开启改名提醒, 关闭改名提醒, 开启退群提醒, 关闭退群提醒, 查看提醒状态
2025-07-22 18:49:25 | INFO | 已设置检查间隔为 3600 秒
2025-07-22 18:49:25 | INFO | 已加载 2 个监控群，开始更新成员列表
2025-07-22 18:49:26 | DEBUG | 已更新群 27852221909@chatroom 的成员列表
2025-07-22 18:49:26 | DEBUG | 已更新群 51891329927@chatroom 的成员列表
2025-07-22 18:49:26 | SUCCESS | 插件 TimerTask 已加载定时任务: {'plugins.TimerTask.main.TimerTask.check_tasks'}
2025-07-22 18:49:26 | INFO | [VideoDemand] 加载了 128 个视频类别配置
2025-07-22 18:49:27 | INFO | [YaoyaoPlugin] 私聊黑名单用户: ['gh_a5cf07d474f1', 'gh_ba3381c847b3']
2025-07-22 18:49:27 | INFO | [YaoyaoPlugin] 已加载管理员列表: ['wxid_ubbh6q832tcs21', 'admin-wxid']
2025-07-22 18:49:27 | INFO | [yuanbao] 插件初始化完成
2025-07-22 18:49:27 | DEBUG | [yuanbao] 指令: ['元宝', 'yuanbao']
2025-07-22 18:49:27 | DEBUG | [yuanbao] 绘图模型: gpt_175B_0404
2025-07-22 18:49:27 | DEBUG | [yuanbao] 认证信息加载状态: hy_token=True, hy_user=True, agent_id=True, x_uskey=True
2025-07-22 18:49:27 | SUCCESS | 已加载插件: ['AdminPoint', 'AdminSignInReset', 'AdminWhitelist', 'AISignIn', 'AppMessageTester', 'ArticleForwarder', 'BaiduAgentsPlugin', 'BaiduDraw', 'BotStatus', 'ChatSummary', 'DanceSignInPlugin', 'DeepseekPlugin', 'Doubao', 'DoubaoDrawing', 'DoubaoImageRecognition', 'DouBaoImageToImage', 'DouBaoImageToVideo', 'DoubaoVideoSearch', 'DouHui', 'EmojiTestPlugin', 'FixedQuoteTest', 'GetContact', 'Gomoku', 'GoodMorning', 'HunyuanDraw', 'ImageEcho', 'ImageToImage', 'JiemengDraw', 'KeLingImageToImage', 'KlingAI', 'Leaderboard', 'LuckyDraw', 'ManagePlugin', 'MeituAI', 'Menu', 'MiniProgramTester', 'Music', 'News', 'PatReply', 'PointTrade', 'QuarkSignIn', 'QueryPoint', 'RandomMember', 'RandomPicture', 'RenameReminder', 'RevokePlugin', 'RoboNeo', 'SignIn', 'TempFileManagerPlugin', 'TencentLke', 'TimerTask', 'TongyiDraw', 'URLShortener', 'VideoDemand', 'VideoParserPlugin', 'VideoTest', 'VivoAgentsPlugin', 'VoiceTest', 'Warthunder', 'WeatherQuery', 'WelcomePlugin', 'Xunfei', 'YaoyaoPlugin', 'Yuanbao', '抽签', '造梦次元']
2025-07-22 18:49:27 | INFO | 处理堆积消息中
2025-07-22 18:49:27 | SUCCESS | 处理堆积消息完毕
2025-07-22 18:49:27 | SUCCESS | 开始处理消息
2025-07-22 18:49:44 | DEBUG | 收到消息: {'MsgId': 500127987, 'FromUserName': {'string': '48097389945@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 49, 'Content': {'string': 'wxid_wlnzvr8ivgd422:\n<?xml version="1.0"?>\n<msg>\n\t<appmsg appid="" sdkver="0">\n\t\t<title>真惨</title>\n\t\t<des />\n\t\t<username />\n\t\t<action>view</action>\n\t\t<type>57</type>\n\t\t<showtype>0</showtype>\n\t\t<content />\n\t\t<url />\n\t\t<lowurl />\n\t\t<forwardflag>0</forwardflag>\n\t\t<dataurl />\n\t\t<lowdataurl />\n\t\t<contentattr>0</contentattr>\n\t\t<streamvideo>\n\t\t\t<streamvideourl />\n\t\t\t<streamvideototaltime>0</streamvideototaltime>\n\t\t\t<streamvideotitle />\n\t\t\t<streamvideowording />\n\t\t\t<streamvideoweburl />\n\t\t\t<streamvideothumburl />\n\t\t\t<streamvideoaduxinfo />\n\t\t\t<streamvideopublishid />\n\t\t</streamvideo>\n\t\t<canvasPageItem>\n\t\t\t<canvasPageXml><![CDATA[]]></canvasPageXml>\n\t\t</canvasPageItem>\n\t\t<refermsg>\n\t\t\t<type>1</type>\n\t\t\t<svrid>5544964129008274541</svrid>\n\t\t\t<fromusr>48097389945@chatroom</fromusr>\n\t\t\t<chatusr>wxid_hqdtktnqvw8e21</chatusr>\n\t\t\t<displayname>Ritz</displayname>\n\t\t\t<msgsource>&lt;msgsource&gt;\n\t&lt;bizflag&gt;0&lt;/bizflag&gt;\n\t&lt;pua&gt;1&lt;/pua&gt;\n\t&lt;silence&gt;1&lt;/silence&gt;\n\t&lt;membercount&gt;62&lt;/membercount&gt;\n\t&lt;signature&gt;N0_V1_Sl6CLIid|v1_Oaxtn75h&lt;/signature&gt;\n\t&lt;tmp_node&gt;\n\t\t&lt;publisher-id&gt;&lt;/publisher-id&gt;\n\t&lt;/tmp_node&gt;\n&lt;/msgsource&gt;\n</msgsource>\n\t\t\t<content>\n还没下班</content>\n\t\t\t<strid />\n\t\t\t<createtime>1753181239</createtime>\n\t\t</refermsg>\n\t\t<appattach>\n\t\t\t<totallen>0</totallen>\n\t\t\t<attachid />\n\t\t\t<cdnattachurl />\n\t\t\t<emoticonmd5 />\n\t\t\t<aeskey />\n\t\t\t<fileext />\n\t\t\t<islargefilemsg>0</islargefilemsg>\n\t\t</appattach>\n\t\t<extinfo />\n\t\t<androidsource>0</androidsource>\n\t\t<thumburl />\n\t\t<mediatagname />\n\t\t<messageaction><![CDATA[]]></messageaction>\n\t\t<messageext><![CDATA[]]></messageext>\n\t\t<emoticongift>\n\t\t\t<packageflag>0</packageflag>\n\t\t\t<packageid />\n\t\t</emoticongift>\n\t\t<emoticonshared>\n\t\t\t<packageflag>0</packageflag>\n\t\t\t<packageid />\n\t\t</emoticonshared>\n\t\t<designershared>\n\t\t\t<designeruin>0</designeruin>\n\t\t\t<designername>null</designername>\n\t\t\t<designerrediretcturl><![CDATA[null]]></designerrediretcturl>\n\t\t</designershared>\n\t\t<emotionpageshared>\n\t\t\t<tid>0</tid>\n\t\t\t<title>null</title>\n\t\t\t<desc>null</desc>\n\t\t\t<iconUrl><![CDATA[null]]></iconUrl>\n\t\t\t<secondUrl>null</secondUrl>\n\t\t\t<pageType>0</pageType>\n\t\t\t<setKey>null</setKey>\n\t\t</emotionpageshared>\n\t\t<webviewshared>\n\t\t\t<shareUrlOriginal />\n\t\t\t<shareUrlOpen />\n\t\t\t<jsAppId />\n\t\t\t<publisherId />\n\t\t\t<publisherReqId />\n\t\t</webviewshared>\n\t\t<template_id />\n\t\t<md5 />\n\t\t<websearch>\n\t\t\t<rec_category>0</rec_category>\n\t\t\t<channelId>0</channelId>\n\t\t</websearch>\n\t\t<weappinfo>\n\t\t\t<username />\n\t\t\t<appid />\n\t\t\t<appservicetype>0</appservicetype>\n\t\t\t<secflagforsinglepagemode>0</secflagforsinglepagemode>\n\t\t\t<videopageinfo>\n\t\t\t\t<thumbwidth>0</thumbwidth>\n\t\t\t\t<thumbheight>0</thumbheight>\n\t\t\t\t<fromopensdk>0</fromopensdk>\n\t\t\t</videopageinfo>\n\t\t</weappinfo>\n\t\t<statextstr />\n\t\t<musicShareItem>\n\t\t\t<musicDuration>0</musicDuration>\n\t\t</musicShareItem>\n\t\t<finderLiveProductShare>\n\t\t\t<finderLiveID><![CDATA[]]></finderLiveID>\n\t\t\t<finderUsername><![CDATA[]]></finderUsername>\n\t\t\t<finderObjectID><![CDATA[]]></finderObjectID>\n\t\t\t<finderNonceID><![CDATA[]]></finderNonceID>\n\t\t\t<liveStatus><![CDATA[]]></liveStatus>\n\t\t\t<appId><![CDATA[]]></appId>\n\t\t\t<pagePath><![CDATA[]]></pagePath>\n\t\t\t<productId><![CDATA[]]></productId>\n\t\t\t<coverUrl><![CDATA[]]></coverUrl>\n\t\t\t<productTitle><![CDATA[]]></productTitle>\n\t\t\t<marketPrice><![CDATA[0]]></marketPrice>\n\t\t\t<sellingPrice><![CDATA[0]]></sellingPrice>\n\t\t\t<platformHeadImg><![CDATA[]]></platformHeadImg>\n\t\t\t<platformName><![CDATA[]]></platformName>\n\t\t\t<shopWindowId><![CDATA[]]></shopWindowId>\n\t\t\t<flashSalePrice><![CDATA[0]]></flashSalePrice>\n\t\t\t<flashSaleEndTime><![CDATA[0]]></flashSaleEndTime>\n\t\t\t<ecSource><![CDATA[]]></ecSource>\n\t\t\t<sellingPriceWording><![CDATA[]]></sellingPriceWording>\n\t\t\t<platformIconURL><![CDATA[]]></platformIconURL>\n\t\t\t<firstProductTagURL><![CDATA[]]></firstProductTagURL>\n\t\t\t<firstProductTagAspectRatioString><![CDATA[0.0]]></firstProductTagAspectRatioString>\n\t\t\t<secondProductTagURL><![CDATA[]]></secondProductTagURL>\n\t\t\t<secondProductTagAspectRatioString><![CDATA[0.0]]></secondProductTagAspectRatioString>\n\t\t\t<firstGuaranteeWording><![CDATA[]]></firstGuaranteeWording>\n\t\t\t<secondGuaranteeWording><![CDATA[]]></secondGuaranteeWording>\n\t\t\t<thirdGuaranteeWording><![CDATA[]]></thirdGuaranteeWording>\n\t\t\t<isPriceBeginShow>false</isPriceBeginShow>\n\t\t\t<lastGMsgID><![CDATA[]]></lastGMsgID>\n\t\t\t<promoterKey><![CDATA[]]></promoterKey>\n\t\t\t<discountWording><![CDATA[]]></discountWording>\n\t\t\t<priceSuffixDescription><![CDATA[]]></priceSuffixDescription>\n\t\t\t<productCardKey><![CDATA[]]></productCardKey>\n\t\t\t<isWxShop><![CDATA[]]></isWxShop>\n\t\t\t<brandIconUrl><![CDATA[]]></brandIconUrl>\n\t\t\t<rIconUrl><![CDATA[]]></rIconUrl>\n\t\t\t<rIconUrlDarkMode><![CDATA[]]></rIconUrlDarkMode>\n\t\t\t<topShopIconUrl><![CDATA[]]></topShopIconUrl>\n\t\t\t<topShopIconUrlDarkMode><![CDATA[]]></topShopIconUrlDarkMode>\n\t\t\t<simplifyTopShopIconUrl><![CDATA[]]></simplifyTopShopIconUrl>\n\t\t\t<simplifyTopShopIconUrlDarkmode><![CDATA[]]></simplifyTopShopIconUrlDarkmode>\n\t\t\t<topShopIconWidth><![CDATA[0]]></topShopIconWidth>\n\t\t\t<topShopIconHeight><![CDATA[0]]></topShopIconHeight>\n\t\t\t<simplifyTopShopIconWidth><![CDATA[0]]></simplifyTopShopIconWidth>\n\t\t\t<simplifyTopShopIconHeight><![CDATA[0]]></simplifyTopShopIconHeight>\n\t\t\t<showBoxItemStringList />\n\t\t</finderLiveProductShare>\n\t\t<finderOrder>\n\t\t\t<appID><![CDATA[]]></appID>\n\t\t\t<orderID><![CDATA[]]></orderID>\n\t\t\t<path><![CDATA[]]></path>\n\t\t\t<priceWording><![CDATA[]]></priceWording>\n\t\t\t<stateWording><![CDATA[]]></stateWording>\n\t\t\t<productImageURL><![CDATA[]]></productImageURL>\n\t\t\t<products><![CDATA[]]></products>\n\t\t\t<productsCount><![CDATA[0]]></productsCount>\n\t\t\t<orderType><![CDATA[0]]></orderType>\n\t\t\t<newPriceWording><![CDATA[]]></newPriceWording>\n\t\t\t<newStateWording><![CDATA[]]></newStateWording>\n\t\t\t<useNewWording><![CDATA[0]]></useNewWording>\n\t\t</finderOrder>\n\t\t<finderShopWindowShare>\n\t\t\t<finderUsername><![CDATA[]]></finderUsername>\n\t\t\t<avatar><![CDATA[]]></avatar>\n\t\t\t<nickname><![CDATA[]]></nickname>\n\t\t\t<commodityInStockCount><![CDATA[]]></commodityInStockCount>\n\t\t\t<appId><![CDATA[]]></appId>\n\t\t\t<path><![CDATA[]]></path>\n\t\t\t<appUsername><![CDATA[]]></appUsername>\n\t\t\t<query><![CDATA[]]></query>\n\t\t\t<liteAppId><![CDATA[]]></liteAppId>\n\t\t\t<liteAppPath><![CDATA[]]></liteAppPath>\n\t\t\t<liteAppQuery><![CDATA[]]></liteAppQuery>\n\t\t\t<platformTagURL><![CDATA[]]></platformTagURL>\n\t\t\t<saleWording><![CDATA[]]></saleWording>\n\t\t\t<lastGMsgID><![CDATA[]]></lastGMsgID>\n\t\t\t<profileTypeWording><![CDATA[]]></profileTypeWording>\n\t\t\t<saleWordingExtra><![CDATA[]]></saleWordingExtra>\n\t\t\t<isWxShop><![CDATA[]]></isWxShop>\n\t\t\t<platformIconUrl><![CDATA[]]></platformIconUrl>\n\t\t\t<brandIconUrl><![CDATA[]]></brandIconUrl>\n\t\t\t<description><![CDATA[]]></description>\n\t\t\t<backgroundUrl><![CDATA[]]></backgroundUrl>\n\t\t\t<darkModePlatformIconUrl><![CDATA[]]></darkModePlatformIconUrl>\n\t\t\t<rIconUrl><![CDATA[]]></rIconUrl>\n\t\t\t<rIconUrlDarkMode><![CDATA[]]></rIconUrlDarkMode>\n\t\t\t<rWords><![CDATA[]]></rWords>\n\t\t\t<topShopIconUrl><![CDATA[]]></topShopIconUrl>\n\t\t\t<topShopIconUrlDarkMode><![CDATA[]]></topShopIconUrlDarkMode>\n\t\t\t<simplifyTopShopIconUrl><![CDATA[]]></simplifyTopShopIconUrl>\n\t\t\t<simplifyTopShopIconUrlDarkmode><![CDATA[]]></simplifyTopShopIconUrlDarkmode>\n\t\t\t<topShopIconWidth><![CDATA[0]]></topShopIconWidth>\n\t\t\t<topShopIconHeight><![CDATA[0]]></topShopIconHeight>\n\t\t\t<simplifyTopShopIconWidth><![CDATA[0]]></simplifyTopShopIconWidth>\n\t\t\t<simplifyTopShopIconHeight><![CDATA[0]]></simplifyTopShopIconHeight>\n\t\t\t<reputationInfo>\n\t\t\t\t<hasReputationInfo>0</hasReputationInfo>\n\t\t\t\t<reputationScore>0</reputationScore>\n\t\t\t\t<reputationWording />\n\t\t\t\t<reputationTextColor />\n\t\t\t\t<reputationLevelWording />\n\t\t\t\t<reputationBackgroundColor />\n\t\t\t</reputationInfo>\n\t\t\t<productImageURLList />\n\t\t</finderShopWindowShare>\n\t\t<findernamecard>\n\t\t\t<username />\n\t\t\t<avatar><![CDATA[]]></avatar>\n\t\t\t<nickname />\n\t\t\t<auth_job />\n\t\t\t<auth_icon>0</auth_icon>\n\t\t\t<auth_icon_url />\n\t\t\t<ecSource><![CDATA[]]></ecSource>\n\t\t\t<lastGMsgID><![CDATA[]]></lastGMsgID>\n\t\t</findernamecard>\n\t\t<finderGuarantee>\n\t\t\t<scene><![CDATA[0]]></scene>\n\t\t</finderGuarantee>\n\t\t<directshare>0</directshare>\n\t\t<gamecenter>\n\t\t\t<namecard>\n\t\t\t\t<iconUrl />\n\t\t\t\t<name />\n\t\t\t\t<desc />\n\t\t\t\t<tail />\n\t\t\t\t<jumpUrl />\n\t\t\t\t<liteappId />\n\t\t\t\t<liteappPath />\n\t\t\t\t<liteappQuery />\n\t\t\t\t<liteappMinVersion />\n\t\t\t</namecard>\n\t\t</gamecenter>\n\t\t<patMsg>\n\t\t\t<chatUser />\n\t\t\t<records>\n\t\t\t\t<recordNum>0</recordNum>\n\t\t\t</records>\n\t\t</patMsg>\n\t\t<secretmsg>\n\t\t\t<issecretmsg>0</issecretmsg>\n\t\t</secretmsg>\n\t\t<referfromscene>0</referfromscene>\n\t\t<gameshare>\n\t\t\t<liteappext>\n\t\t\t\t<liteappbizdata />\n\t\t\t\t<priority>0</priority>\n\t\t\t</liteappext>\n\t\t\t<appbrandext>\n\t\t\t\t<litegameinfo />\n\t\t\t\t<priority>-1</priority>\n\t\t\t</appbrandext>\n\t\t\t<gameshareid />\n\t\t\t<sharedata />\n\t\t\t<isvideo>0</isvideo>\n\t\t\t<duration>-1</duration>\n\t\t\t<isexposed>0</isexposed>\n\t\t\t<readtext />\n\t\t</gameshare>\n\t\t<tingChatRoomItem>\n\t\t\t<type>0</type>\n\t\t\t<categoryItem>null</categoryItem>\n\t\t\t<categoryId />\n\t\t</tingChatRoomItem>\n\t\t<mpsharetrace>\n\t\t\t<hasfinderelement>0</hasfinderelement>\n\t\t\t<lastgmsgid />\n\t\t</mpsharetrace>\n\t\t<wxgamecard>\n\t\t\t<framesetname />\n\t\t\t<mbcarddata />\n\t\t\t<minpkgversion />\n\t\t\t<clientextinfo />\n\t\t\t<mbcardheight>0</mbcardheight>\n\t\t\t<isoldversion>0</isoldversion>\n\t\t</wxgamecard>\n\t\t<ecskfcard>\n\t\t\t<framesetname />\n\t\t\t<mbcarddata />\n\t\t\t<minupdateunixtimestamp>0</minupdateunixtimestamp>\n\t\t\t<needheader>false</needheader>\n\t\t\t<summary />\n\t\t</ecskfcard>\n\t\t<liteapp>\n\t\t\t<id>null</id>\n\t\t\t<path />\n\t\t\t<query />\n\t\t\t<istransparent>0</istransparent>\n\t\t\t<hideicon>0</hideicon>\n\t\t\t<forbidforward>0</forbidforward>\n\t\t</liteapp>\n\t\t<opensdk_share_is_modified>0</opensdk_share_is_modified>\n\t</appmsg>\n\t<fromusername>wxid_wlnzvr8ivgd422</fromusername>\n\t<scene>0</scene>\n\t<appinfo>\n\t\t<version>1</version>\n\t\t<appname></appname>\n\t</appinfo>\n\t<commenturl></commenturl>\n</msg>\n'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1753181397, 'MsgSource': '<msgsource>\n\t<alnode>\n\t\t<fr>4</fr>\n\t</alnode>\n\t<eggIncluded>1</eggIncluded>\n\t<sec_msg_node>\n\t\t<uuid>03c8bb89216de4e9cb362c98b9e464e4_</uuid>\n\t\t<risk-file-flag />\n\t\t<risk-file-md5-list />\n\t</sec_msg_node>\n\t<silence>0</silence>\n\t<membercount>62</membercount>\n\t<signature>N0_V1_aHfU1ksf|v1_8LZr2Eke</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'PushContent': '锦岚（开玩笑就退群版） : 真惨', 'NewMsgId': 6892096375398843336, 'MsgSeq': 871392713}
2025-07-22 18:49:44 | DEBUG | 从群聊消息中提取发送者: wxid_wlnzvr8ivgd422
2025-07-22 18:49:44 | DEBUG | 使用已解析的XML处理引用消息
2025-07-22 18:49:44 | INFO | 收到引用消息: 消息ID:500127987 来自:48097389945@chatroom 发送人:wxid_wlnzvr8ivgd422 内容:真惨 引用类型:1
2025-07-22 18:49:44 | INFO | [DouBaoImageToImage] 收到引用消息: 真惨
2025-07-22 18:49:44 | INFO | [TimerTask] 收到引用消息调试信息:
2025-07-22 18:49:44 | INFO |   - 消息内容: 真惨
2025-07-22 18:49:44 | INFO |   - 群组ID: 48097389945@chatroom
2025-07-22 18:49:44 | INFO |   - 发送人: wxid_wlnzvr8ivgd422
2025-07-22 18:49:44 | INFO |   - 引用信息: {'MsgType': 1, 'Content': '\n还没下班', 'Msgid': '5544964129008274541', 'NewMsgId': '5544964129008274541', 'ToWxid': 'wxid_4usgcju5ey9q29', 'FromWxid': '48097389945@chatroom', 'Nickname': 'Ritz', 'MsgSource': '<msgsource>\n\t<bizflag>0</bizflag>\n\t<pua>1</pua>\n\t<silence>1</silence>\n\t<membercount>62</membercount>\n\t<signature>N0_V1_Sl6CLIid|v1_Oaxtn75h</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'Createtime': '1753181239', 'SenderWxid': 'wxid_wlnzvr8ivgd422'}
2025-07-22 18:49:44 | INFO |   - 引用消息ID: 
2025-07-22 18:49:44 | INFO |   - 引用消息类型: 
2025-07-22 18:49:44 | INFO |   - 引用消息内容: 
还没下班
2025-07-22 18:49:44 | INFO |   - 引用消息发送人: wxid_wlnzvr8ivgd422
2025-07-22 18:49:52 | DEBUG | 收到消息: {'MsgId': 1225713836, 'FromUserName': {'string': '48097389945@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 49, 'Content': {'string': 'wxid_wlnzvr8ivgd422:\n<?xml version="1.0"?>\n<msg>\n\t<appmsg appid="" sdkver="0">\n\t\t<title>那就晚上</title>\n\t\t<des />\n\t\t<username />\n\t\t<action>view</action>\n\t\t<type>57</type>\n\t\t<showtype>0</showtype>\n\t\t<content />\n\t\t<url />\n\t\t<lowurl />\n\t\t<forwardflag>0</forwardflag>\n\t\t<dataurl />\n\t\t<lowdataurl />\n\t\t<contentattr>0</contentattr>\n\t\t<streamvideo>\n\t\t\t<streamvideourl />\n\t\t\t<streamvideototaltime>0</streamvideototaltime>\n\t\t\t<streamvideotitle />\n\t\t\t<streamvideowording />\n\t\t\t<streamvideoweburl />\n\t\t\t<streamvideothumburl />\n\t\t\t<streamvideoaduxinfo />\n\t\t\t<streamvideopublishid />\n\t\t</streamvideo>\n\t\t<canvasPageItem>\n\t\t\t<canvasPageXml><![CDATA[]]></canvasPageXml>\n\t\t</canvasPageItem>\n\t\t<refermsg>\n\t\t\t<type>1</type>\n\t\t\t<svrid>3194654621982671091</svrid>\n\t\t\t<fromusr>48097389945@chatroom</fromusr>\n\t\t\t<chatusr>xiaomaochong</chatusr>\n\t\t\t<displayname>小爱</displayname>\n\t\t\t<msgsource>&lt;msgsource&gt;\n\t&lt;pua&gt;1&lt;/pua&gt;\n\t&lt;silence&gt;1&lt;/silence&gt;\n\t&lt;membercount&gt;62&lt;/membercount&gt;\n\t&lt;signature&gt;N0_V1_AYIexRuC|v1_SlwT/nNg&lt;/signature&gt;\n\t&lt;tmp_node&gt;\n\t\t&lt;publisher-id&gt;&lt;/publisher-id&gt;\n\t&lt;/tmp_node&gt;\n&lt;/msgsource&gt;\n</msgsource>\n\t\t\t<content>\n这会不行</content>\n\t\t\t<strid />\n\t\t\t<createtime>1753181262</createtime>\n\t\t</refermsg>\n\t\t<appattach>\n\t\t\t<totallen>0</totallen>\n\t\t\t<attachid />\n\t\t\t<cdnattachurl />\n\t\t\t<emoticonmd5 />\n\t\t\t<aeskey />\n\t\t\t<fileext />\n\t\t\t<islargefilemsg>0</islargefilemsg>\n\t\t</appattach>\n\t\t<extinfo />\n\t\t<androidsource>0</androidsource>\n\t\t<thumburl />\n\t\t<mediatagname />\n\t\t<messageaction><![CDATA[]]></messageaction>\n\t\t<messageext><![CDATA[]]></messageext>\n\t\t<emoticongift>\n\t\t\t<packageflag>0</packageflag>\n\t\t\t<packageid />\n\t\t</emoticongift>\n\t\t<emoticonshared>\n\t\t\t<packageflag>0</packageflag>\n\t\t\t<packageid />\n\t\t</emoticonshared>\n\t\t<designershared>\n\t\t\t<designeruin>0</designeruin>\n\t\t\t<designername>null</designername>\n\t\t\t<designerrediretcturl><![CDATA[null]]></designerrediretcturl>\n\t\t</designershared>\n\t\t<emotionpageshared>\n\t\t\t<tid>0</tid>\n\t\t\t<title>null</title>\n\t\t\t<desc>null</desc>\n\t\t\t<iconUrl><![CDATA[null]]></iconUrl>\n\t\t\t<secondUrl>null</secondUrl>\n\t\t\t<pageType>0</pageType>\n\t\t\t<setKey>null</setKey>\n\t\t</emotionpageshared>\n\t\t<webviewshared>\n\t\t\t<shareUrlOriginal />\n\t\t\t<shareUrlOpen />\n\t\t\t<jsAppId />\n\t\t\t<publisherId />\n\t\t\t<publisherReqId />\n\t\t</webviewshared>\n\t\t<template_id />\n\t\t<md5 />\n\t\t<websearch>\n\t\t\t<rec_category>0</rec_category>\n\t\t\t<channelId>0</channelId>\n\t\t</websearch>\n\t\t<weappinfo>\n\t\t\t<username />\n\t\t\t<appid />\n\t\t\t<appservicetype>0</appservicetype>\n\t\t\t<secflagforsinglepagemode>0</secflagforsinglepagemode>\n\t\t\t<videopageinfo>\n\t\t\t\t<thumbwidth>0</thumbwidth>\n\t\t\t\t<thumbheight>0</thumbheight>\n\t\t\t\t<fromopensdk>0</fromopensdk>\n\t\t\t</videopageinfo>\n\t\t</weappinfo>\n\t\t<statextstr />\n\t\t<musicShareItem>\n\t\t\t<musicDuration>0</musicDuration>\n\t\t</musicShareItem>\n\t\t<finderLiveProductShare>\n\t\t\t<finderLiveID><![CDATA[]]></finderLiveID>\n\t\t\t<finderUsername><![CDATA[]]></finderUsername>\n\t\t\t<finderObjectID><![CDATA[]]></finderObjectID>\n\t\t\t<finderNonceID><![CDATA[]]></finderNonceID>\n\t\t\t<liveStatus><![CDATA[]]></liveStatus>\n\t\t\t<appId><![CDATA[]]></appId>\n\t\t\t<pagePath><![CDATA[]]></pagePath>\n\t\t\t<productId><![CDATA[]]></productId>\n\t\t\t<coverUrl><![CDATA[]]></coverUrl>\n\t\t\t<productTitle><![CDATA[]]></productTitle>\n\t\t\t<marketPrice><![CDATA[0]]></marketPrice>\n\t\t\t<sellingPrice><![CDATA[0]]></sellingPrice>\n\t\t\t<platformHeadImg><![CDATA[]]></platformHeadImg>\n\t\t\t<platformName><![CDATA[]]></platformName>\n\t\t\t<shopWindowId><![CDATA[]]></shopWindowId>\n\t\t\t<flashSalePrice><![CDATA[0]]></flashSalePrice>\n\t\t\t<flashSaleEndTime><![CDATA[0]]></flashSaleEndTime>\n\t\t\t<ecSource><![CDATA[]]></ecSource>\n\t\t\t<sellingPriceWording><![CDATA[]]></sellingPriceWording>\n\t\t\t<platformIconURL><![CDATA[]]></platformIconURL>\n\t\t\t<firstProductTagURL><![CDATA[]]></firstProductTagURL>\n\t\t\t<firstProductTagAspectRatioString><![CDATA[0.0]]></firstProductTagAspectRatioString>\n\t\t\t<secondProductTagURL><![CDATA[]]></secondProductTagURL>\n\t\t\t<secondProductTagAspectRatioString><![CDATA[0.0]]></secondProductTagAspectRatioString>\n\t\t\t<firstGuaranteeWording><![CDATA[]]></firstGuaranteeWording>\n\t\t\t<secondGuaranteeWording><![CDATA[]]></secondGuaranteeWording>\n\t\t\t<thirdGuaranteeWording><![CDATA[]]></thirdGuaranteeWording>\n\t\t\t<isPriceBeginShow>false</isPriceBeginShow>\n\t\t\t<lastGMsgID><![CDATA[]]></lastGMsgID>\n\t\t\t<promoterKey><![CDATA[]]></promoterKey>\n\t\t\t<discountWording><![CDATA[]]></discountWording>\n\t\t\t<priceSuffixDescription><![CDATA[]]></priceSuffixDescription>\n\t\t\t<productCardKey><![CDATA[]]></productCardKey>\n\t\t\t<isWxShop><![CDATA[]]></isWxShop>\n\t\t\t<brandIconUrl><![CDATA[]]></brandIconUrl>\n\t\t\t<rIconUrl><![CDATA[]]></rIconUrl>\n\t\t\t<rIconUrlDarkMode><![CDATA[]]></rIconUrlDarkMode>\n\t\t\t<topShopIconUrl><![CDATA[]]></topShopIconUrl>\n\t\t\t<topShopIconUrlDarkMode><![CDATA[]]></topShopIconUrlDarkMode>\n\t\t\t<simplifyTopShopIconUrl><![CDATA[]]></simplifyTopShopIconUrl>\n\t\t\t<simplifyTopShopIconUrlDarkmode><![CDATA[]]></simplifyTopShopIconUrlDarkmode>\n\t\t\t<topShopIconWidth><![CDATA[0]]></topShopIconWidth>\n\t\t\t<topShopIconHeight><![CDATA[0]]></topShopIconHeight>\n\t\t\t<simplifyTopShopIconWidth><![CDATA[0]]></simplifyTopShopIconWidth>\n\t\t\t<simplifyTopShopIconHeight><![CDATA[0]]></simplifyTopShopIconHeight>\n\t\t\t<showBoxItemStringList />\n\t\t</finderLiveProductShare>\n\t\t<finderOrder>\n\t\t\t<appID><![CDATA[]]></appID>\n\t\t\t<orderID><![CDATA[]]></orderID>\n\t\t\t<path><![CDATA[]]></path>\n\t\t\t<priceWording><![CDATA[]]></priceWording>\n\t\t\t<stateWording><![CDATA[]]></stateWording>\n\t\t\t<productImageURL><![CDATA[]]></productImageURL>\n\t\t\t<products><![CDATA[]]></products>\n\t\t\t<productsCount><![CDATA[0]]></productsCount>\n\t\t\t<orderType><![CDATA[0]]></orderType>\n\t\t\t<newPriceWording><![CDATA[]]></newPriceWording>\n\t\t\t<newStateWording><![CDATA[]]></newStateWording>\n\t\t\t<useNewWording><![CDATA[0]]></useNewWording>\n\t\t</finderOrder>\n\t\t<finderShopWindowShare>\n\t\t\t<finderUsername><![CDATA[]]></finderUsername>\n\t\t\t<avatar><![CDATA[]]></avatar>\n\t\t\t<nickname><![CDATA[]]></nickname>\n\t\t\t<commodityInStockCount><![CDATA[]]></commodityInStockCount>\n\t\t\t<appId><![CDATA[]]></appId>\n\t\t\t<path><![CDATA[]]></path>\n\t\t\t<appUsername><![CDATA[]]></appUsername>\n\t\t\t<query><![CDATA[]]></query>\n\t\t\t<liteAppId><![CDATA[]]></liteAppId>\n\t\t\t<liteAppPath><![CDATA[]]></liteAppPath>\n\t\t\t<liteAppQuery><![CDATA[]]></liteAppQuery>\n\t\t\t<platformTagURL><![CDATA[]]></platformTagURL>\n\t\t\t<saleWording><![CDATA[]]></saleWording>\n\t\t\t<lastGMsgID><![CDATA[]]></lastGMsgID>\n\t\t\t<profileTypeWording><![CDATA[]]></profileTypeWording>\n\t\t\t<saleWordingExtra><![CDATA[]]></saleWordingExtra>\n\t\t\t<isWxShop><![CDATA[]]></isWxShop>\n\t\t\t<platformIconUrl><![CDATA[]]></platformIconUrl>\n\t\t\t<brandIconUrl><![CDATA[]]></brandIconUrl>\n\t\t\t<description><![CDATA[]]></description>\n\t\t\t<backgroundUrl><![CDATA[]]></backgroundUrl>\n\t\t\t<darkModePlatformIconUrl><![CDATA[]]></darkModePlatformIconUrl>\n\t\t\t<rIconUrl><![CDATA[]]></rIconUrl>\n\t\t\t<rIconUrlDarkMode><![CDATA[]]></rIconUrlDarkMode>\n\t\t\t<rWords><![CDATA[]]></rWords>\n\t\t\t<topShopIconUrl><![CDATA[]]></topShopIconUrl>\n\t\t\t<topShopIconUrlDarkMode><![CDATA[]]></topShopIconUrlDarkMode>\n\t\t\t<simplifyTopShopIconUrl><![CDATA[]]></simplifyTopShopIconUrl>\n\t\t\t<simplifyTopShopIconUrlDarkmode><![CDATA[]]></simplifyTopShopIconUrlDarkmode>\n\t\t\t<topShopIconWidth><![CDATA[0]]></topShopIconWidth>\n\t\t\t<topShopIconHeight><![CDATA[0]]></topShopIconHeight>\n\t\t\t<simplifyTopShopIconWidth><![CDATA[0]]></simplifyTopShopIconWidth>\n\t\t\t<simplifyTopShopIconHeight><![CDATA[0]]></simplifyTopShopIconHeight>\n\t\t\t<reputationInfo>\n\t\t\t\t<hasReputationInfo>0</hasReputationInfo>\n\t\t\t\t<reputationScore>0</reputationScore>\n\t\t\t\t<reputationWording />\n\t\t\t\t<reputationTextColor />\n\t\t\t\t<reputationLevelWording />\n\t\t\t\t<reputationBackgroundColor />\n\t\t\t</reputationInfo>\n\t\t\t<productImageURLList />\n\t\t</finderShopWindowShare>\n\t\t<findernamecard>\n\t\t\t<username />\n\t\t\t<avatar><![CDATA[]]></avatar>\n\t\t\t<nickname />\n\t\t\t<auth_job />\n\t\t\t<auth_icon>0</auth_icon>\n\t\t\t<auth_icon_url />\n\t\t\t<ecSource><![CDATA[]]></ecSource>\n\t\t\t<lastGMsgID><![CDATA[]]></lastGMsgID>\n\t\t</findernamecard>\n\t\t<finderGuarantee>\n\t\t\t<scene><![CDATA[0]]></scene>\n\t\t</finderGuarantee>\n\t\t<directshare>0</directshare>\n\t\t<gamecenter>\n\t\t\t<namecard>\n\t\t\t\t<iconUrl />\n\t\t\t\t<name />\n\t\t\t\t<desc />\n\t\t\t\t<tail />\n\t\t\t\t<jumpUrl />\n\t\t\t\t<liteappId />\n\t\t\t\t<liteappPath />\n\t\t\t\t<liteappQuery />\n\t\t\t\t<liteappMinVersion />\n\t\t\t</namecard>\n\t\t</gamecenter>\n\t\t<patMsg>\n\t\t\t<chatUser />\n\t\t\t<records>\n\t\t\t\t<recordNum>0</recordNum>\n\t\t\t</records>\n\t\t</patMsg>\n\t\t<secretmsg>\n\t\t\t<issecretmsg>0</issecretmsg>\n\t\t</secretmsg>\n\t\t<referfromscene>0</referfromscene>\n\t\t<gameshare>\n\t\t\t<liteappext>\n\t\t\t\t<liteappbizdata />\n\t\t\t\t<priority>0</priority>\n\t\t\t</liteappext>\n\t\t\t<appbrandext>\n\t\t\t\t<litegameinfo />\n\t\t\t\t<priority>-1</priority>\n\t\t\t</appbrandext>\n\t\t\t<gameshareid />\n\t\t\t<sharedata />\n\t\t\t<isvideo>0</isvideo>\n\t\t\t<duration>-1</duration>\n\t\t\t<isexposed>0</isexposed>\n\t\t\t<readtext />\n\t\t</gameshare>\n\t\t<tingChatRoomItem>\n\t\t\t<type>0</type>\n\t\t\t<categoryItem>null</categoryItem>\n\t\t\t<categoryId />\n\t\t</tingChatRoomItem>\n\t\t<mpsharetrace>\n\t\t\t<hasfinderelement>0</hasfinderelement>\n\t\t\t<lastgmsgid />\n\t\t</mpsharetrace>\n\t\t<wxgamecard>\n\t\t\t<framesetname />\n\t\t\t<mbcarddata />\n\t\t\t<minpkgversion />\n\t\t\t<clientextinfo />\n\t\t\t<mbcardheight>0</mbcardheight>\n\t\t\t<isoldversion>0</isoldversion>\n\t\t</wxgamecard>\n\t\t<ecskfcard>\n\t\t\t<framesetname />\n\t\t\t<mbcarddata />\n\t\t\t<minupdateunixtimestamp>0</minupdateunixtimestamp>\n\t\t\t<needheader>false</needheader>\n\t\t\t<summary />\n\t\t</ecskfcard>\n\t\t<liteapp>\n\t\t\t<id>null</id>\n\t\t\t<path />\n\t\t\t<query />\n\t\t\t<istransparent>0</istransparent>\n\t\t\t<hideicon>0</hideicon>\n\t\t\t<forbidforward>0</forbidforward>\n\t\t</liteapp>\n\t\t<opensdk_share_is_modified>0</opensdk_share_is_modified>\n\t</appmsg>\n\t<fromusername>wxid_wlnzvr8ivgd422</fromusername>\n\t<scene>0</scene>\n\t<appinfo>\n\t\t<version>1</version>\n\t\t<appname></appname>\n\t</appinfo>\n\t<commenturl></commenturl>\n</msg>\n'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1753181405, 'MsgSource': '<msgsource>\n\t<alnode>\n\t\t<fr>4</fr>\n\t</alnode>\n\t<eggIncluded>1</eggIncluded>\n\t<sec_msg_node>\n\t\t<uuid>fca1bbe12d4e34ef9fe965f2077ca8f4_</uuid>\n\t\t<risk-file-flag />\n\t\t<risk-file-md5-list />\n\t</sec_msg_node>\n\t<silence>0</silence>\n\t<membercount>62</membercount>\n\t<signature>N0_V1_DYDkd6Ah|v1_wly3j6Ok</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'PushContent': '锦岚（开玩笑就退群版） : 那就晚上', 'NewMsgId': 1576309322778541221, 'MsgSeq': 871392714}
2025-07-22 18:49:52 | DEBUG | 从群聊消息中提取发送者: wxid_wlnzvr8ivgd422
2025-07-22 18:49:52 | DEBUG | 使用已解析的XML处理引用消息
2025-07-22 18:49:52 | INFO | 收到引用消息: 消息ID:1225713836 来自:48097389945@chatroom 发送人:wxid_wlnzvr8ivgd422 内容:那就晚上 引用类型:1
2025-07-22 18:49:53 | INFO | [DouBaoImageToImage] 收到引用消息: 那就晚上
2025-07-22 18:49:53 | INFO | [TimerTask] 收到引用消息调试信息:
2025-07-22 18:49:53 | INFO |   - 消息内容: 那就晚上
2025-07-22 18:49:53 | INFO |   - 群组ID: 48097389945@chatroom
2025-07-22 18:49:53 | INFO |   - 发送人: wxid_wlnzvr8ivgd422
2025-07-22 18:49:53 | INFO |   - 引用信息: {'MsgType': 1, 'Content': '\n这会不行', 'Msgid': '3194654621982671091', 'NewMsgId': '3194654621982671091', 'ToWxid': 'wxid_4usgcju5ey9q29', 'FromWxid': '48097389945@chatroom', 'Nickname': '小爱', 'MsgSource': '<msgsource>\n\t<pua>1</pua>\n\t<silence>1</silence>\n\t<membercount>62</membercount>\n\t<signature>N0_V1_AYIexRuC|v1_SlwT/nNg</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'Createtime': '1753181262', 'SenderWxid': 'wxid_wlnzvr8ivgd422'}
2025-07-22 18:49:53 | INFO |   - 引用消息ID: 
2025-07-22 18:49:53 | INFO |   - 引用消息类型: 
2025-07-22 18:49:53 | INFO |   - 引用消息内容: 
这会不行
2025-07-22 18:49:53 | INFO |   - 引用消息发送人: wxid_wlnzvr8ivgd422
2025-07-22 18:50:16 | DEBUG | 收到消息: {'MsgId': 142742791, 'FromUserName': {'string': '47442567074@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 1, 'Content': {'string': 'wxid_ubbh6q832tcs21:\n搜歌 大鹏'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1753181429, 'MsgSource': '<msgsource>\n\t<pua>1</pua>\n\t<eggIncluded>1</eggIncluded>\n\t<silence>0</silence>\n\t<membercount>494</membercount>\n\t<signature>N0_V1_mlwuw3gY|v1_YNIr7ood</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'PushContent': '郭 : 搜歌 大鹏', 'NewMsgId': 1793996226781824878, 'MsgSeq': 871392715}
2025-07-22 18:50:16 | INFO | 收到文本消息: 消息ID:142742791 来自:47442567074@chatroom 发送人:wxid_ubbh6q832tcs21 @:[] 内容:搜歌 大鹏
2025-07-22 18:50:17 | DEBUG | [Music] 成功获取发送者 wxid_ubbh6q832tcs21 的头像: https://wx.qlogo.cn/mmhead/ver_1/8wGAWmpuprTQGmWf3R4kiayO7ibj5rNx2s2JMZEHgRwicoeMTgXgNNibmNjHYPYD7R1ASNnDFza4TIvrF52zFEQve4R8HQcUzjPl5XNUe3DjhVHkJHPDiag6hvibFdic2wM3O2G/132
2025-07-22 18:50:18 | INFO | 发送app消息: 对方wxid:47442567074@chatroom 类型:19 xml:<appmsg appid="" sdkver="0"><title>🎵 大鹏 - 搜索结果</title><des>🎵 音乐助手: 为您找到 "大鹏" 的相关歌曲：🎶 搜索结果: 1、再无她 -- 大鹏2、庞龙《你是我的玫瑰花》 -- 恒俞TRAVIS3、那丝也有春天 -- 大鹏4、当爱情离开的时候 -- 王麟5、我要让你心碎 -- 达闻西乐队6、一人饮酒醉 (Live) -- 华晨宇7、那丝也有春天 -- GG啵8、When You Love Me -- 猪猪💡 使用提示: 发送 "听1" 到 "听8" 选择对应歌曲</des><action>view</action><type>19</type><showtype>0</showtype><content/><url>https://support.weixin.qq.com/cgi-bin/mmsupport-bin/readtemplate?t=page/favorite_record__w_unsupport&amp;from=singlemessage&amp;isappinstalled=0</url><lowurl/><dataurl/><lowdataurl/><recorditem><![CDATA[<recordinfo><title>🎵 音乐搜索结果</title><desc>🎵 音乐助手:&#x20;为您找到 "大鹏" 的相关歌曲：&#x0A;🎶 搜索结果:&#x20;1、再无她 -- 大鹏2、庞龙《你是我的玫瑰花》 -- 恒俞TRAVIS3、那丝也有春天 -- 大鹏4、当爱情离开的时候 -- 王麟5、我要让你心碎 -- 达闻西乐队6、一人饮酒醉 (Live) -- 华晨宇7、那丝也有春天 -- GG啵8、When You Love Me -- 猪猪&#x0A;💡 使用提示:&#x20;发送 "听1" 到 "听8" 选择对应歌曲</desc><datalist count="3"><dataitem datatype="1" dataid="64c6c0e5e72d32b0f0f386a100b57a62" datasourceid="5661221320293391771"><datadesc>为您找到 "大鹏" 的相关歌曲：</datadesc><sourcename>🎵 音乐助手</sourcename><sourceheadurl>https://wx.qlogo.cn/mmhead/ver_1/8wGAWmpuprTQGmWf3R4kiayO7ibj5rNx2s2JMZEHgRwicoeMTgXgNNibmNjHYPYD7R1ASNnDFza4TIvrF52zFEQve4R8HQcUzjPl5XNUe3DjhVHkJHPDiag6hvibFdic2wM3O2G/132</sourceheadurl><sourcetime>2025-07-21&#x20;12:00:00</sourcetime><srcMsgCreateTime>1753181417</srcMsgCreateTime><fromnewmsgid>5661221320293391771</fromnewmsgid><dataitemsource><hashusername>47117a242116be9ab5e02f7331cd04041140f0caf32a9bedff8b44eb7e0c1cb7</hashusername></dataitemsource></dataitem><dataitem datatype="1" dataid="d86217a03d83b3c38c7e5b5f02d16fcf" datasourceid="5874289693918554791"><datadesc>1、再无她 -- 大鹏2、庞龙《你是我的玫瑰花》 -- 恒俞TRAVIS3、那丝也有春天 -- 大鹏4、当爱情离开的时候 -- 王麟5、我要让你心碎 -- 达闻西乐队6、一人饮酒醉 (Live) -- 华晨宇7、那丝也有春天 -- GG啵8、When You Love Me -- 猪猪</datadesc><sourcename>🎶 搜索结果</sourcename><sourceheadurl>https://wx.qlogo.cn/mmhead/ver_1/8wGAWmpuprTQGmWf3R4kiayO7ibj5rNx2s2JMZEHgRwicoeMTgXgNNibmNjHYPYD7R1ASNnDFza4TIvrF52zFEQve4R8HQcUzjPl5XNUe3DjhVHkJHPDiag6hvibFdic2wM3O2G/132</sourceheadurl><sourcetime>2025-07-21&#x20;12:01:00</sourcetime><srcMsgCreateTime>1753181447</srcMsgCreateTime><fromnewmsgid>5874289693918554791</fromnewmsgid><dataitemsource><hashusername>e6b73ff27565cff370109f8fcda0e65fdab070d2463b159472ee7e7f54f7d313</hashusername></dataitemsource></dataitem><dataitem datatype="1" dataid="60a9d586e3f24b8c1418bbb8db6db29b" datasourceid="8034207384982812410"><datadesc>发送 "听1" 到 "听8" 选择对应歌曲</datadesc><sourcename>💡 使用提示</sourcename><sourceheadurl>https://wx.qlogo.cn/mmhead/ver_1/rTPg4GeXEic6oQSZ2jcAI6ic6JEhFtYT40Eu3rUIjZpTKTATSgCEiaMJlUuFibkfxN4IpDCv0qxCA5WgowqT0BGRTQAVeDTn1PTGj1LUYbl2bRuJiaCb1ukH4e1TPIibyUiaX6hFKgria9aX7NM5SJFHDq3pBA/132</sourceheadurl><sourcetime>2025-07-21&#x20;12:02:00</sourcetime><srcMsgCreateTime>1753181477</srcMsgCreateTime><fromnewmsgid>8034207384982812410</fromnewmsgid><dataitemsource><hashusername>8f57acad5c854c4004fd00435c305ac427418e4ac2f7816798d3db2db6eff1eb</hashusername></dataitemsource></dataitem></datalist><favcreatetime>1753181417803</favcreatetime></recordinfo>]]></recorditem><appattach>    <totallen>0</totallen>    <attachid/>    <emoticonmd5/>    <fileext/>    <aeskey/></appattach><extinfo/><sourceusername/><sourcedisplayname/><thumburl/><md5/></appmsg><fromusername>wxid_4usgcju5ey9q29</fromusername><scene>0</scene><appinfo>    <version>1</version>    <appname></appname></appinfo><commenturl/>
2025-07-22 18:50:18 | DEBUG | 处理消息内容: '搜歌 大鹏'
2025-07-22 18:50:18 | DEBUG | 消息内容 '搜歌 大鹏' 不匹配任何命令，忽略
2025-07-22 18:50:28 | DEBUG | 收到消息: {'MsgId': 1647157631, 'FromUserName': {'string': '48097389945@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 3, 'Content': {'string': 'wxid_jegyk4i3v7zg22:\n<?xml version="1.0"?>\n<msg>\n\t<img aeskey="280da45195d005c8644bdb43ead23697" encryver="1" cdnthumbaeskey="280da45195d005c8644bdb43ead23697" cdnthumburl="3057020100044b30490201000204f53349af02032e1d7b02042e3a9c240204687f6d01042464376266613366302d363366352d343836632d393537342d303063336334663066386136020405250a020201000405004c543f00" cdnthumblength="5969" cdnthumbheight="90" cdnthumbwidth="120" cdnmidheight="0" cdnmidwidth="0" cdnhdheight="0" cdnhdwidth="0" cdnmidimgurl="3057020100044b30490201000204f53349af02032e1d7b02042e3a9c240204687f6d01042464376266613366302d363366352d343836632d393537342d303063336334663066386136020405250a020201000405004c543f00" length="364307" md5="d3d5a7ac168989a748e365b9d641c0d0" hevc_mid_size="364307" originsourcemd5="ca432599463ed8bac0eb6b7b41f42e03">\n\t\t<secHashInfoBase64>eyJwaGFzaCI6IjMyMzA1MDUwMzgxMTkwMTAiLCJwZHFoYXNoIjoiZjI1OTg0OTczNDFkZThjZjBjMzI3MGYyZWVmOTFkOWQzNzY3OGIwMmFjZDEzNGM3NWQxNGFhMzhmMzI0OWIzMSJ9</secHashInfoBase64>\n\t\t<live>\n\t\t\t<duration>0</duration>\n\t\t\t<size>0</size>\n\t\t\t<md5 />\n\t\t\t<fileid />\n\t\t\t<hdsize>0</hdsize>\n\t\t\t<hdmd5 />\n\t\t\t<hdfileid />\n\t\t\t<stillimagetimems>0</stillimagetimems>\n\t\t</live>\n\t</img>\n\t<platform_signature />\n\t<imgdatahash />\n\t<ImgSourceInfo>\n\t\t<ImgSourceUrl />\n\t\t<BizType>0</BizType>\n\t</ImgSourceInfo>\n</msg>\n'}, 'Status': 3, 'ImgStatus': 2, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1753181441, 'MsgSource': '<msgsource>\n\t<bizflag>0</bizflag>\n\t<sec_msg_node>\n\t\t<uuid>a9cb75aa0cece1b051b7e7091b69d5b6_</uuid>\n\t\t<risk-file-flag />\n\t\t<risk-file-md5-list />\n\t</sec_msg_node>\n\t<silence>0</silence>\n\t<membercount>62</membercount>\n\t<signature>N0_V1_5RJdHBOW|v1_3b0CrIEx</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'PushContent': '阿尼亚与她在群聊中发了一张图片', 'NewMsgId': 1001410173795474639, 'MsgSeq': 871392718}
2025-07-22 18:50:28 | INFO | 收到图片消息: 消息ID:1647157631 来自:48097389945@chatroom 发送人:wxid_jegyk4i3v7zg22 XML:<?xml version="1.0"?><msg><img aeskey="280da45195d005c8644bdb43ead23697" encryver="1" cdnthumbaeskey="280da45195d005c8644bdb43ead23697" cdnthumburl="3057020100044b30490201000204f53349af02032e1d7b02042e3a9c240204687f6d01042464376266613366302d363366352d343836632d393537342d303063336334663066386136020405250a020201000405004c543f00" cdnthumblength="5969" cdnthumbheight="90" cdnthumbwidth="120" cdnmidheight="0" cdnmidwidth="0" cdnhdheight="0" cdnhdwidth="0" cdnmidimgurl="3057020100044b30490201000204f53349af02032e1d7b02042e3a9c240204687f6d01042464376266613366302d363366352d343836632d393537342d303063336334663066386136020405250a020201000405004c543f00" length="364307" md5="d3d5a7ac168989a748e365b9d641c0d0" hevc_mid_size="364307" originsourcemd5="ca432599463ed8bac0eb6b7b41f42e03"><secHashInfoBase64>eyJwaGFzaCI6IjMyMzA1MDUwMzgxMTkwMTAiLCJwZHFoYXNoIjoiZjI1OTg0OTczNDFkZThjZjBjMzI3MGYyZWVmOTFkOWQzNzY3OGIwMmFjZDEzNGM3NWQxNGFhMzhmMzI0OWIzMSJ9</secHashInfoBase64><live><duration>0</duration><size>0</size><md5 /><fileid /><hdsize>0</hdsize><hdmd5 /><hdfileid /><stillimagetimems>0</stillimagetimems></live></img><platform_signature /><imgdatahash /><ImgSourceInfo><ImgSourceUrl /><BizType>0</BizType></ImgSourceInfo></msg>
2025-07-22 18:50:29 | INFO | [ImageEcho] 保存图片信息成功，当前群 48097389945@chatroom 已存储 5 张图片
2025-07-22 18:50:29 | INFO | [TimerTask] 缓存图片消息: 1647157631
2025-07-22 18:50:35 | DEBUG | 收到消息: {'MsgId': 4972405, 'FromUserName': {'string': '47442567074@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 1, 'Content': {'string': 'wxid_ubbh6q832tcs21:\n可以了'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1753181448, 'MsgSource': '<msgsource>\n\t<pua>1</pua>\n\t<eggIncluded>1</eggIncluded>\n\t<silence>0</silence>\n\t<membercount>494</membercount>\n\t<signature>N0_V1_h5pCauiV|v1_89roERc8</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'PushContent': '郭 : 可以了', 'NewMsgId': 7593952771450441090, 'MsgSeq': 871392719}
2025-07-22 18:50:35 | INFO | 收到文本消息: 消息ID:4972405 来自:47442567074@chatroom 发送人:wxid_ubbh6q832tcs21 @:[] 内容:可以了
2025-07-22 18:50:35 | DEBUG | 处理消息内容: '可以了'
2025-07-22 18:50:35 | DEBUG | 消息内容 '可以了' 不匹配任何命令，忽略
2025-07-22 18:50:36 | DEBUG | 收到消息: {'MsgId': 43396887, 'FromUserName': {'string': '48097389945@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 1, 'Content': {'string': 'wxid_jegyk4i3v7zg22:\n一个人带六娃'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1753181449, 'MsgSource': '<msgsource>\n\t<bizflag>0</bizflag>\n\t<pua>1</pua>\n\t<eggIncluded>1</eggIncluded>\n\t<silence>0</silence>\n\t<membercount>62</membercount>\n\t<signature>N0_V1_3OBSphRJ|v1_oMXQEtq7</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'PushContent': '阿尼亚与她 : 一个人带六娃', 'NewMsgId': 8965851345330874283, 'MsgSeq': 871392720}
2025-07-22 18:50:36 | INFO | 收到文本消息: 消息ID:43396887 来自:48097389945@chatroom 发送人:wxid_jegyk4i3v7zg22 @:[] 内容:一个人带六娃
2025-07-22 18:50:37 | DEBUG | 处理消息内容: '一个人带六娃'
2025-07-22 18:50:37 | DEBUG | 消息内容 '一个人带六娃' 不匹配任何命令，忽略
2025-07-22 18:50:55 | DEBUG | 收到消息: {'MsgId': 873776612, 'FromUserName': {'string': '48097389945@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 47, 'Content': {'string': 'wxid_jegyk4i3v7zg22:\n<msg><emoji fromusername="wxid_jegyk4i3v7zg22" tousername="48097389945@chatroom" type="2" idbuffer="media:0_0" md5="fc6f96a2c164bfafa3035dc86c50ee0a" len="15085" productid="" androidmd5="fc6f96a2c164bfafa3035dc86c50ee0a" androidlen="15085" s60v3md5="fc6f96a2c164bfafa3035dc86c50ee0a" s60v3len="15085" s60v5md5="fc6f96a2c164bfafa3035dc86c50ee0a" s60v5len="15085" cdnurl="http://wxapp.tc.qq.com/262/20304/stodownload?m=fc6f96a2c164bfafa3035dc86c50ee0a&amp;filekey=30340201010420301e020201060402535a0410fc6f96a2c164bfafa3035dc86c50ee0a02023aed040d00000004627466730000000131&amp;hy=SZ&amp;storeid=32303231303632353132313230383030306139646333336333306630306333363133356630393030303030313036&amp;bizid=1023" designerid="" thumburl="" encrypturl="http://wxapp.tc.qq.com/262/20304/stodownload?m=21fd37523a3bd1fa10c4851b77392e5c&amp;filekey=30340201010420301e020201060402535a041021fd37523a3bd1fa10c4851b77392e5c02023af0040d00000004627466730000000131&amp;hy=SZ&amp;storeid=32303231303632353132313230383030306430383838336333306630306362633338356630393030303030313036&amp;bizid=1023" aeskey="65c3e647eac3b5f32e31222f18340fc1" externurl="http://wxapp.tc.qq.com/262/20304/stodownload?m=b99f3505aa55860aadc2a49cf81acf2b&amp;filekey=30340201010420301e020201060402535a0410b99f3505aa55860aadc2a49cf81acf2b02021930040d00000004627466730000000131&amp;hy=SZ&amp;storeid=32303231303632353132313230383030306564623230336333306630306363653539356630393030303030313036&amp;bizid=1023" externmd5="41fec80a408cc4ab8ba3683f5dda7774" width="39" height="53" tpurl="" tpauthkey="" attachedtext="" attachedtextcolor="" lensid="" emojiattr="" linkid="" desc=""></emoji><gameext type="0" content="0"></gameext><extcommoninfo></extcommoninfo></msg>'}, 'Status': 3, 'ImgStatus': 2, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1753181468, 'MsgSource': '<msgsource>\n\t<silence>0</silence>\n\t<membercount>62</membercount>\n\t<signature>N0_V1_YrQh/CsD|v1_JG6WoHGA</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'PushContent': '阿尼亚与她在群聊中发了一个表情', 'NewMsgId': 1918945851818942063, 'MsgSeq': 871392721}
2025-07-22 18:50:55 | INFO | 收到表情消息: 消息ID:873776612 来自:48097389945@chatroom 发送人:wxid_jegyk4i3v7zg22 MD5:fc6f96a2c164bfafa3035dc86c50ee0a 大小:15085
2025-07-22 18:50:56 | DEBUG | [DoubaoImageRecognition] 缓存表情消息: 1918945851818942063
2025-07-22 18:51:03 | DEBUG | 收到消息: {'MsgId': 444437218, 'FromUserName': {'string': '47442567074@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 3, 'Content': {'string': 'wxid_i73nrnun919k12:\n<?xml version="1.0"?>\n<msg>\n\t<img aeskey="4b2641128540a11c38915b71faf23cdd" encryver="1" cdnthumbaeskey="4b2641128540a11c38915b71faf23cdd" cdnthumburl="3057020100044b30490201000204a3ed3f9802032f7793020494b8206f0204687f6d23042434313761333739622d643366352d343132352d383566382d353033636466353235623638020405250a020201000405004c4e6300" cdnthumblength="1979" cdnthumbheight="120" cdnthumbwidth="55" cdnmidheight="0" cdnmidwidth="0" cdnhdheight="0" cdnhdwidth="0" cdnmidimgurl="3057020100044b30490201000204a3ed3f9802032f7793020494b8206f0204687f6d23042434313761333739622d643366352d343132352d383566382d353033636466353235623638020405250a020201000405004c4e6300" length="58842" md5="0b52819fd24abfe6f0b393215339407a" hevc_mid_size="58842" originsourcemd5="b7b42fa7e41aebdff025c8c39dd50b49">\n\t\t<secHashInfoBase64>eyJwaGFzaCI6IjEwMTAwMDAwMDAwMDAwMDAiLCJwZHFoYXNoIjoiZmI2OGZmMjhmZjgwZmY5MGU3OTA0MDNkMDA3ZDkyNjkwMDZmMDAyZjAwOWY0MGJmZjZlMWQyNjFkOTYxZmRhZSJ9</secHashInfoBase64>\n\t\t<live>\n\t\t\t<duration>0</duration>\n\t\t\t<size>0</size>\n\t\t\t<md5 />\n\t\t\t<fileid />\n\t\t\t<hdsize>0</hdsize>\n\t\t\t<hdmd5 />\n\t\t\t<hdfileid />\n\t\t\t<stillimagetimems>0</stillimagetimems>\n\t\t</live>\n\t</img>\n\t<platform_signature />\n\t<imgdatahash />\n\t<ImgSourceInfo>\n\t\t<ImgSourceUrl />\n\t\t<BizType>0</BizType>\n\t</ImgSourceInfo>\n</msg>\n'}, 'Status': 3, 'ImgStatus': 2, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1753181476, 'MsgSource': '<msgsource>\n\t<bizflag>0</bizflag>\n\t<sec_msg_node>\n\t\t<uuid>011dcd4a18edac11b7d11b8558c4c02f_</uuid>\n\t\t<risk-file-flag />\n\t\t<risk-file-md5-list />\n\t</sec_msg_node>\n\t<silence>0</silence>\n\t<membercount>494</membercount>\n\t<signature>N0_V1_PkXiCkS2|v1_nTrSyoVu</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'PushContent': '  在群聊中发了一张图片', 'NewMsgId': 959812270694018780, 'MsgSeq': 871392722}
2025-07-22 18:51:03 | INFO | 收到图片消息: 消息ID:444437218 来自:47442567074@chatroom 发送人:wxid_i73nrnun919k12 XML:<?xml version="1.0"?><msg><img aeskey="4b2641128540a11c38915b71faf23cdd" encryver="1" cdnthumbaeskey="4b2641128540a11c38915b71faf23cdd" cdnthumburl="3057020100044b30490201000204a3ed3f9802032f7793020494b8206f0204687f6d23042434313761333739622d643366352d343132352d383566382d353033636466353235623638020405250a020201000405004c4e6300" cdnthumblength="1979" cdnthumbheight="120" cdnthumbwidth="55" cdnmidheight="0" cdnmidwidth="0" cdnhdheight="0" cdnhdwidth="0" cdnmidimgurl="3057020100044b30490201000204a3ed3f9802032f7793020494b8206f0204687f6d23042434313761333739622d643366352d343132352d383566382d353033636466353235623638020405250a020201000405004c4e6300" length="58842" md5="0b52819fd24abfe6f0b393215339407a" hevc_mid_size="58842" originsourcemd5="b7b42fa7e41aebdff025c8c39dd50b49"><secHashInfoBase64>eyJwaGFzaCI6IjEwMTAwMDAwMDAwMDAwMDAiLCJwZHFoYXNoIjoiZmI2OGZmMjhmZjgwZmY5MGU3OTA0MDNkMDA3ZDkyNjkwMDZmMDAyZjAwOWY0MGJmZjZlMWQyNjFkOTYxZmRhZSJ9</secHashInfoBase64><live><duration>0</duration><size>0</size><md5 /><fileid /><hdsize>0</hdsize><hdmd5 /><hdfileid /><stillimagetimems>0</stillimagetimems></live></img><platform_signature /><imgdatahash /><ImgSourceInfo><ImgSourceUrl /><BizType>0</BizType></ImgSourceInfo></msg>
2025-07-22 18:51:03 | INFO | [ImageEcho] 保存图片信息成功，当前群 47442567074@chatroom 已存储 5 张图片
2025-07-22 18:51:03 | INFO | [TimerTask] 缓存图片消息: 444437218
2025-07-22 18:51:24 | DEBUG | 收到消息: {'MsgId': 885215207, 'FromUserName': {'string': '48097389945@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 1, 'Content': {'string': 'wxid_7o4g44suf20t22:\n全职煮夫？'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1753181497, 'MsgSource': '<msgsource>\n\t<pua>1</pua>\n\t<eggIncluded>1</eggIncluded>\n\t<silence>0</silence>\n\t<membercount>62</membercount>\n\t<signature>N0_V1_b1GiIIJ3|v1_S/CKV7K0</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'PushContent': '与其，不惹 : 全职煮夫？', 'NewMsgId': 135936670428030849, 'MsgSeq': 871392723}
2025-07-22 18:51:24 | INFO | 收到文本消息: 消息ID:885215207 来自:48097389945@chatroom 发送人:wxid_7o4g44suf20t22 @:[] 内容:全职煮夫？
2025-07-22 18:51:24 | DEBUG | 处理消息内容: '全职煮夫？'
2025-07-22 18:51:24 | DEBUG | 消息内容 '全职煮夫？' 不匹配任何命令，忽略
2025-07-22 18:51:28 | DEBUG | 收到消息: {'MsgId': 1375640883, 'FromUserName': {'string': '48097389945@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 1, 'Content': {'string': 'wxid_84mmq4cu7ita22:\nAniya'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1753181501, 'MsgSource': '<msgsource>\n\t<bizflag>0</bizflag>\n\t<pua>1</pua>\n\t<eggIncluded>1</eggIncluded>\n\t<silence>0</silence>\n\t<membercount>62</membercount>\n\t<signature>N0_V1_bhmd4UZW|v1_wLhCLlvT</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'PushContent': '喵小叶 : Aniya', 'NewMsgId': 5409146274123684670, 'MsgSeq': 871392724}
2025-07-22 18:51:28 | INFO | 收到文本消息: 消息ID:1375640883 来自:48097389945@chatroom 发送人:wxid_84mmq4cu7ita22 @:[] 内容:Aniya
2025-07-22 18:51:28 | DEBUG | 处理消息内容: 'Aniya'
2025-07-22 18:51:28 | DEBUG | 消息内容 'Aniya' 不匹配任何命令，忽略
2025-07-22 18:51:33 | DEBUG | 收到消息: {'MsgId': 31315390, 'FromUserName': {'string': '48097389945@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 1, 'Content': {'string': 'wxid_hqdtktnqvw8e21:\n小爱又换头像了'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1753181506, 'MsgSource': '<msgsource>\n\t<bizflag>0</bizflag>\n\t<pua>1</pua>\n\t<silence>0</silence>\n\t<membercount>62</membercount>\n\t<signature>N0_V1_igJjhT2S|v1_9JinTsf1</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'PushContent': 'Ritz : 小爱又换头像了', 'NewMsgId': 731855763383876144, 'MsgSeq': 871392725}
2025-07-22 18:51:33 | INFO | 收到文本消息: 消息ID:31315390 来自:48097389945@chatroom 发送人:wxid_hqdtktnqvw8e21 @:[] 内容:小爱又换头像了
2025-07-22 18:51:34 | DEBUG | 处理消息内容: '小爱又换头像了'
2025-07-22 18:51:34 | DEBUG | 消息内容 '小爱又换头像了' 不匹配任何命令，忽略
2025-07-22 18:51:36 | DEBUG | 收到消息: {'MsgId': 393143545, 'FromUserName': {'string': '48097389945@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 1, 'Content': {'string': 'wxid_84mmq4cu7ita22:\n你家后继有人啊'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1753181510, 'MsgSource': '<msgsource>\n\t<bizflag>0</bizflag>\n\t<pua>1</pua>\n\t<eggIncluded>1</eggIncluded>\n\t<silence>0</silence>\n\t<membercount>62</membercount>\n\t<signature>N0_V1_RnOlFlZm|v1_rjOBtr0h</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'PushContent': '喵小叶 : 你家后继有人啊', 'NewMsgId': 3949481417031586278, 'MsgSeq': 871392726}
2025-07-22 18:51:36 | INFO | 收到文本消息: 消息ID:393143545 来自:48097389945@chatroom 发送人:wxid_84mmq4cu7ita22 @:[] 内容:你家后继有人啊
2025-07-22 18:51:37 | DEBUG | 处理消息内容: '你家后继有人啊'
2025-07-22 18:51:37 | DEBUG | 消息内容 '你家后继有人啊' 不匹配任何命令，忽略
2025-07-22 18:51:47 | DEBUG | 收到消息: {'MsgId': 220371869, 'FromUserName': {'string': '48097389945@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 1, 'Content': {'string': 'wxid_hqdtktnqvw8e21:\n全是女孩子'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1753181521, 'MsgSource': '<msgsource>\n\t<bizflag>0</bizflag>\n\t<pua>1</pua>\n\t<silence>0</silence>\n\t<membercount>62</membercount>\n\t<signature>N0_V1_BdgJ/XTU|v1_502QDcmB</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'PushContent': 'Ritz : 全是女孩子', 'NewMsgId': 5854685530400709331, 'MsgSeq': 871392727}
2025-07-22 18:51:48 | INFO | 收到文本消息: 消息ID:220371869 来自:48097389945@chatroom 发送人:wxid_hqdtktnqvw8e21 @:[] 内容:全是女孩子
2025-07-22 18:51:48 | DEBUG | 处理消息内容: '全是女孩子'
2025-07-22 18:51:48 | DEBUG | 消息内容 '全是女孩子' 不匹配任何命令，忽略
2025-07-22 18:51:50 | DEBUG | 收到消息: {'MsgId': 311885342, 'FromUserName': {'string': '48097389945@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 1, 'Content': {'string': 'wxid_hqdtktnqvw8e21:\n离谱'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1753181523, 'MsgSource': '<msgsource>\n\t<bizflag>0</bizflag>\n\t<pua>1</pua>\n\t<silence>0</silence>\n\t<membercount>62</membercount>\n\t<signature>N0_V1_8OHPpU6v|v1_pemJImCY</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'PushContent': 'Ritz : 离谱', 'NewMsgId': 7176134916310526538, 'MsgSeq': 871392728}
2025-07-22 18:51:50 | INFO | 收到文本消息: 消息ID:311885342 来自:48097389945@chatroom 发送人:wxid_hqdtktnqvw8e21 @:[] 内容:离谱
2025-07-22 18:51:51 | DEBUG | 处理消息内容: '离谱'
2025-07-22 18:51:51 | DEBUG | 消息内容 '离谱' 不匹配任何命令，忽略
2025-07-22 18:51:55 | DEBUG | 收到消息: {'MsgId': 100083597, 'FromUserName': {'string': '48097389945@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 1, 'Content': {'string': 'wxid_7o4g44suf20t22:\n生6个'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1753181528, 'MsgSource': '<msgsource>\n\t<pua>1</pua>\n\t<eggIncluded>1</eggIncluded>\n\t<silence>0</silence>\n\t<membercount>62</membercount>\n\t<signature>N0_V1_7SQmbF9k|v1_+UcsRSs1</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'PushContent': '与其，不惹 : 生6个', 'NewMsgId': 2418205470519599068, 'MsgSeq': 871392729}
2025-07-22 18:51:55 | INFO | 收到文本消息: 消息ID:100083597 来自:48097389945@chatroom 发送人:wxid_7o4g44suf20t22 @:[] 内容:生6个
2025-07-22 18:51:55 | DEBUG | 处理消息内容: '生6个'
2025-07-22 18:51:55 | DEBUG | 消息内容 '生6个' 不匹配任何命令，忽略
2025-07-22 18:52:08 | DEBUG | 收到消息: {'MsgId': 947169116, 'FromUserName': {'string': '47442567074@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 1, 'Content': {'string': 'wxid_ubbh6q832tcs21:\n[旺柴]'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1753181541, 'MsgSource': '<msgsource>\n\t<pua>1</pua>\n\t<eggIncluded>1</eggIncluded>\n\t<silence>0</silence>\n\t<membercount>494</membercount>\n\t<signature>N0_V1_mQrpRQIA|v1_Cp5pPzJp</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'PushContent': '郭 : [旺柴]', 'NewMsgId': 842236210170926198, 'MsgSeq': 871392730}
2025-07-22 18:52:08 | INFO | 收到表情消息: 消息ID:947169116 来自:47442567074@chatroom 发送人:wxid_ubbh6q832tcs21 @:[] 内容:[旺柴]
2025-07-22 18:52:08 | DEBUG | [DoubaoImageRecognition] 缓存表情消息: 842236210170926198
2025-07-22 18:52:11 | DEBUG | 收到消息: {'MsgId': 436892088, 'FromUserName': {'string': '47442567074@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 1, 'Content': {'string': 'wxid_ubbh6q832tcs21:\n刚改的'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1753181544, 'MsgSource': '<msgsource>\n\t<pua>1</pua>\n\t<eggIncluded>1</eggIncluded>\n\t<silence>0</silence>\n\t<membercount>494</membercount>\n\t<signature>N0_V1_rkGBstl3|v1_LreIXTQF</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'PushContent': '郭 : 刚改的', 'NewMsgId': 1049854109420445068, 'MsgSeq': 871392731}
2025-07-22 18:52:11 | INFO | 收到文本消息: 消息ID:436892088 来自:47442567074@chatroom 发送人:wxid_ubbh6q832tcs21 @:[] 内容:刚改的
2025-07-22 18:52:11 | DEBUG | 处理消息内容: '刚改的'
2025-07-22 18:52:11 | DEBUG | 消息内容 '刚改的' 不匹配任何命令，忽略
2025-07-22 18:52:24 | DEBUG | 收到消息: {'MsgId': 863560665, 'FromUserName': {'string': '47442567074@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 1, 'Content': {'string': 'wxid_hqdtktnqvw8e21:\n1'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1753181557, 'MsgSource': '<msgsource>\n\t<bizflag>0</bizflag>\n\t<pua>1</pua>\n\t<silence>0</silence>\n\t<membercount>494</membercount>\n\t<signature>N0_V1_qPNFLh8F|v1_5mKySH1S</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'PushContent': 'Ritz : 1', 'NewMsgId': 3101030901582160500, 'MsgSeq': 871392732}
2025-07-22 18:52:24 | INFO | 收到文本消息: 消息ID:863560665 来自:47442567074@chatroom 发送人:wxid_hqdtktnqvw8e21 @:[] 内容:1
2025-07-22 18:52:24 | DEBUG | 处理消息内容: '1'
2025-07-22 18:52:24 | DEBUG | 消息内容 '1' 不匹配任何命令，忽略
2025-07-22 18:52:28 | DEBUG | 收到消息: {'MsgId': 491997354, 'FromUserName': {'string': '47442567074@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 1, 'Content': {'string': 'wxid_i73nrnun919k12:\n搜歌 小郭'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1753181561, 'MsgSource': '<msgsource>\n\t<bizflag>0</bizflag>\n\t<pua>1</pua>\n\t<alnode>\n\t\t<cf>2</cf>\n\t</alnode>\n\t<eggIncluded>1</eggIncluded>\n\t<silence>0</silence>\n\t<membercount>494</membercount>\n\t<signature>N0_V1_go1Ngcaz|v1_mhCQBA7F</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'PushContent': '   : 搜歌 小郭', 'NewMsgId': 932022597752892120, 'MsgSeq': 871392733}
2025-07-22 18:52:28 | INFO | 收到文本消息: 消息ID:491997354 来自:47442567074@chatroom 发送人:wxid_i73nrnun919k12 @:[] 内容:搜歌 小郭
2025-07-22 18:52:29 | DEBUG | [Music] 成功获取发送者 wxid_i73nrnun919k12 的头像: https://wx.qlogo.cn/mmhead/ver_1/scnn8NbIhXDGiaibiagdVVV4JYiaeLg7eibibM9oUO5boxKKo7Uericotuib6ppaDuWT62Xj2O52g0dHond73tjQ1HO0mUcVneRoccOPeMoZUnxsHyuY1cXeniaUicDEticebDpXMQF/132
2025-07-22 18:52:29 | INFO | 发送app消息: 对方wxid:47442567074@chatroom 类型:19 xml:<appmsg appid="" sdkver="0"><title>🎵 小郭 - 搜索结果</title><des>🎵 音乐助手: 为您找到 "小郭" 的相关歌曲：🎶 搜索结果: 1、矛盾综合体 -- 花朝2、你突然 (Demo) -- 郭彦彤、小李同学。3、三年之约 -- 郭富祥4、小丑的真爱舞台 -- 郭富祥5、裁剪WeChat2 (Remix) -- 郭亚文6、一程山路 -- 小郭同学7、心酸 -- 尚雨晨8、号我 -- 郭彦彤、小李同学。💡 使用提示: 发送 "听1" 到 "听8" 选择对应歌曲</des><action>view</action><type>19</type><showtype>0</showtype><content/><url>https://support.weixin.qq.com/cgi-bin/mmsupport-bin/readtemplate?t=page/favorite_record__w_unsupport&amp;from=singlemessage&amp;isappinstalled=0</url><lowurl/><dataurl/><lowdataurl/><recorditem><![CDATA[<recordinfo><title>🎵 音乐搜索结果</title><desc>🎵 音乐助手:&#x20;为您找到 "小郭" 的相关歌曲：&#x0A;🎶 搜索结果:&#x20;1、矛盾综合体 -- 花朝2、你突然 (Demo) -- 郭彦彤、小李同学。3、三年之约 -- 郭富祥4、小丑的真爱舞台 -- 郭富祥5、裁剪WeChat2 (Remix) -- 郭亚文6、一程山路 -- 小郭同学7、心酸 -- 尚雨晨8、号我 -- 郭彦彤、小李同学。&#x0A;💡 使用提示:&#x20;发送 "听1" 到 "听8" 选择对应歌曲</desc><datalist count="3"><dataitem datatype="1" dataid="43f5983d2618302a03a06f2e622dcc62" datasourceid="7693762878851529029"><datadesc>为您找到 "小郭" 的相关歌曲：</datadesc><sourcename>🎵 音乐助手</sourcename><sourceheadurl>https://wx.qlogo.cn/mmhead/ver_1/scnn8NbIhXDGiaibiagdVVV4JYiaeLg7eibibM9oUO5boxKKo7Uericotuib6ppaDuWT62Xj2O52g0dHond73tjQ1HO0mUcVneRoccOPeMoZUnxsHyuY1cXeniaUicDEticebDpXMQF/132</sourceheadurl><sourcetime>2025-07-21&#x20;12:00:00</sourcetime><srcMsgCreateTime>1753181549</srcMsgCreateTime><fromnewmsgid>7693762878851529029</fromnewmsgid><dataitemsource><hashusername>47117a242116be9ab5e02f7331cd04041140f0caf32a9bedff8b44eb7e0c1cb7</hashusername></dataitemsource></dataitem><dataitem datatype="1" dataid="0cc8fbbe6ec1081bdfb4ab6093295f20" datasourceid="1588298130854834497"><datadesc>1、矛盾综合体 -- 花朝2、你突然 (Demo) -- 郭彦彤、小李同学。3、三年之约 -- 郭富祥4、小丑的真爱舞台 -- 郭富祥5、裁剪WeChat2 (Remix) -- 郭亚文6、一程山路 -- 小郭同学7、心酸 -- 尚雨晨8、号我 -- 郭彦彤、小李同学。</datadesc><sourcename>🎶 搜索结果</sourcename><sourceheadurl>https://wx.qlogo.cn/mmhead/ver_1/scnn8NbIhXDGiaibiagdVVV4JYiaeLg7eibibM9oUO5boxKKo7Uericotuib6ppaDuWT62Xj2O52g0dHond73tjQ1HO0mUcVneRoccOPeMoZUnxsHyuY1cXeniaUicDEticebDpXMQF/132</sourceheadurl><sourcetime>2025-07-21&#x20;12:01:00</sourcetime><srcMsgCreateTime>1753181579</srcMsgCreateTime><fromnewmsgid>1588298130854834497</fromnewmsgid><dataitemsource><hashusername>e6b73ff27565cff370109f8fcda0e65fdab070d2463b159472ee7e7f54f7d313</hashusername></dataitemsource></dataitem><dataitem datatype="1" dataid="60a9d586e3f24b8c1418bbb8db6db29b" datasourceid="5441973572065967122"><datadesc>发送 "听1" 到 "听8" 选择对应歌曲</datadesc><sourcename>💡 使用提示</sourcename><sourceheadurl>https://wx.qlogo.cn/mmhead/ver_1/rTPg4GeXEic6oQSZ2jcAI6ic6JEhFtYT40Eu3rUIjZpTKTATSgCEiaMJlUuFibkfxN4IpDCv0qxCA5WgowqT0BGRTQAVeDTn1PTGj1LUYbl2bRuJiaCb1ukH4e1TPIibyUiaX6hFKgria9aX7NM5SJFHDq3pBA/132</sourceheadurl><sourcetime>2025-07-21&#x20;12:02:00</sourcetime><srcMsgCreateTime>1753181609</srcMsgCreateTime><fromnewmsgid>5441973572065967122</fromnewmsgid><dataitemsource><hashusername>8f57acad5c854c4004fd00435c305ac427418e4ac2f7816798d3db2db6eff1eb</hashusername></dataitemsource></dataitem></datalist><favcreatetime>1753181549068</favcreatetime></recordinfo>]]></recorditem><appattach>    <totallen>0</totallen>    <attachid/>    <emoticonmd5/>    <fileext/>    <aeskey/></appattach><extinfo/><sourceusername/><sourcedisplayname/><thumburl/><md5/></appmsg><fromusername>wxid_4usgcju5ey9q29</fromusername><scene>0</scene><appinfo>    <version>1</version>    <appname></appname></appinfo><commenturl/>
2025-07-22 18:52:29 | DEBUG | 处理消息内容: '搜歌 小郭'
2025-07-22 18:52:29 | DEBUG | 消息内容 '搜歌 小郭' 不匹配任何命令，忽略
2025-07-22 18:52:33 | DEBUG | 收到消息: {'MsgId': 1131385133, 'FromUserName': {'string': '47442567074@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 1, 'Content': {'string': 'wxid_hqdtktnqvw8e21:\n搜歌 简单爱'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1753181566, 'MsgSource': '<msgsource>\n\t<bizflag>0</bizflag>\n\t<pua>1</pua>\n\t<silence>0</silence>\n\t<membercount>494</membercount>\n\t<signature>N0_V1_apS5DfF7|v1_jVdFAisr</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'PushContent': 'Ritz : 搜歌 简单爱', 'NewMsgId': 3595877740350790901, 'MsgSeq': 871392736}
2025-07-22 18:52:33 | INFO | 收到文本消息: 消息ID:1131385133 来自:47442567074@chatroom 发送人:wxid_hqdtktnqvw8e21 @:[] 内容:搜歌 简单爱
2025-07-22 18:52:34 | DEBUG | [Music] 成功获取发送者 wxid_hqdtktnqvw8e21 的头像: https://wx.qlogo.cn/mmhead/ver_1/5iambe0OkTICGUj1iaV9QqkFu9hy0k4onulRlYW4QiaYTo9iaLwx0xx3couV5rwUM8z1e0X4Kh5nz3T5aF79eQGkoxdVhOatJercCPjzhsxyC13uz3n7Md2sq13mtFfyJ5ddTazreASnwhueM3BW3EicaLg/132
2025-07-22 18:52:34 | INFO | 发送app消息: 对方wxid:47442567074@chatroom 类型:19 xml:<appmsg appid="" sdkver="0"><title>🎵 简单爱 - 搜索结果</title><des>🎵 音乐助手: 为您找到 "简单爱" 的相关歌曲：🎶 搜索结果: 1、简单爱 -- 周杰伦2、简单爱 -- 封茗囧菌3、再度重相逢 -- 陈饼饼4、简单爱 (Live) -- 周杰伦5、简单爱 (Live片段) -- 梓渝6、简单爱 (Live) -- 周杰伦7、简单爱 -- 小F48、简单爱 -- 白允y💡 使用提示: 发送 "听1" 到 "听8" 选择对应歌曲</des><action>view</action><type>19</type><showtype>0</showtype><content/><url>https://support.weixin.qq.com/cgi-bin/mmsupport-bin/readtemplate?t=page/favorite_record__w_unsupport&amp;from=singlemessage&amp;isappinstalled=0</url><lowurl/><dataurl/><lowdataurl/><recorditem><![CDATA[<recordinfo><title>🎵 音乐搜索结果</title><desc>🎵 音乐助手:&#x20;为您找到 "简单爱" 的相关歌曲：&#x0A;🎶 搜索结果:&#x20;1、简单爱 -- 周杰伦2、简单爱 -- 封茗囧菌3、再度重相逢 -- 陈饼饼4、简单爱 (Live) -- 周杰伦5、简单爱 (Live片段) -- 梓渝6、简单爱 (Live) -- 周杰伦7、简单爱 -- 小F48、简单爱 -- 白允y&#x0A;💡 使用提示:&#x20;发送 "听1" 到 "听8" 选择对应歌曲</desc><datalist count="3"><dataitem datatype="1" dataid="9ef2d61265c3ec04a8429f01fd582782" datasourceid="8608392872029641356"><datadesc>为您找到 "简单爱" 的相关歌曲：</datadesc><sourcename>🎵 音乐助手</sourcename><sourceheadurl>https://wx.qlogo.cn/mmhead/ver_1/5iambe0OkTICGUj1iaV9QqkFu9hy0k4onulRlYW4QiaYTo9iaLwx0xx3couV5rwUM8z1e0X4Kh5nz3T5aF79eQGkoxdVhOatJercCPjzhsxyC13uz3n7Md2sq13mtFfyJ5ddTazreASnwhueM3BW3EicaLg/132</sourceheadurl><sourcetime>2025-07-21&#x20;12:00:00</sourcetime><srcMsgCreateTime>1753181554</srcMsgCreateTime><fromnewmsgid>8608392872029641356</fromnewmsgid><dataitemsource><hashusername>47117a242116be9ab5e02f7331cd04041140f0caf32a9bedff8b44eb7e0c1cb7</hashusername></dataitemsource></dataitem><dataitem datatype="1" dataid="68cc599fd3ff42830ec60c8fa1706232" datasourceid="4496954997109050608"><datadesc>1、简单爱 -- 周杰伦2、简单爱 -- 封茗囧菌3、再度重相逢 -- 陈饼饼4、简单爱 (Live) -- 周杰伦5、简单爱 (Live片段) -- 梓渝6、简单爱 (Live) -- 周杰伦7、简单爱 -- 小F48、简单爱 -- 白允y</datadesc><sourcename>🎶 搜索结果</sourcename><sourceheadurl>https://wx.qlogo.cn/mmhead/ver_1/5iambe0OkTICGUj1iaV9QqkFu9hy0k4onulRlYW4QiaYTo9iaLwx0xx3couV5rwUM8z1e0X4Kh5nz3T5aF79eQGkoxdVhOatJercCPjzhsxyC13uz3n7Md2sq13mtFfyJ5ddTazreASnwhueM3BW3EicaLg/132</sourceheadurl><sourcetime>2025-07-21&#x20;12:01:00</sourcetime><srcMsgCreateTime>1753181584</srcMsgCreateTime><fromnewmsgid>4496954997109050608</fromnewmsgid><dataitemsource><hashusername>e6b73ff27565cff370109f8fcda0e65fdab070d2463b159472ee7e7f54f7d313</hashusername></dataitemsource></dataitem><dataitem datatype="1" dataid="60a9d586e3f24b8c1418bbb8db6db29b" datasourceid="3139753669710404044"><datadesc>发送 "听1" 到 "听8" 选择对应歌曲</datadesc><sourcename>💡 使用提示</sourcename><sourceheadurl>https://wx.qlogo.cn/mmhead/ver_1/rTPg4GeXEic6oQSZ2jcAI6ic6JEhFtYT40Eu3rUIjZpTKTATSgCEiaMJlUuFibkfxN4IpDCv0qxCA5WgowqT0BGRTQAVeDTn1PTGj1LUYbl2bRuJiaCb1ukH4e1TPIibyUiaX6hFKgria9aX7NM5SJFHDq3pBA/132</sourceheadurl><sourcetime>2025-07-21&#x20;12:02:00</sourcetime><srcMsgCreateTime>1753181614</srcMsgCreateTime><fromnewmsgid>3139753669710404044</fromnewmsgid><dataitemsource><hashusername>8f57acad5c854c4004fd00435c305ac427418e4ac2f7816798d3db2db6eff1eb</hashusername></dataitemsource></dataitem></datalist><favcreatetime>1753181554289</favcreatetime></recordinfo>]]></recorditem><appattach>    <totallen>0</totallen>    <attachid/>    <emoticonmd5/>    <fileext/>    <aeskey/></appattach><extinfo/><sourceusername/><sourcedisplayname/><thumburl/><md5/></appmsg><fromusername>wxid_4usgcju5ey9q29</fromusername><scene>0</scene><appinfo>    <version>1</version>    <appname></appname></appinfo><commenturl/>
2025-07-22 18:52:34 | DEBUG | 处理消息内容: '搜歌 简单爱'
2025-07-22 18:52:34 | DEBUG | 消息内容 '搜歌 简单爱' 不匹配任何命令，忽略
2025-07-22 18:52:39 | DEBUG | 收到消息: {'MsgId': 136919141, 'FromUserName': {'string': '47442567074@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 1, 'Content': {'string': 'wxid_hqdtktnqvw8e21:\n1'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1753181572, 'MsgSource': '<msgsource>\n\t<bizflag>0</bizflag>\n\t<pua>1</pua>\n\t<silence>0</silence>\n\t<membercount>494</membercount>\n\t<signature>N0_V1_Mo8bKgQb|v1_iuhjhL1d</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'PushContent': 'Ritz : 1', 'NewMsgId': 3483382258780789856, 'MsgSeq': 871392739}
2025-07-22 18:52:39 | INFO | 收到文本消息: 消息ID:136919141 来自:47442567074@chatroom 发送人:wxid_hqdtktnqvw8e21 @:[] 内容:1
2025-07-22 18:52:39 | DEBUG | 处理消息内容: '1'
2025-07-22 18:52:39 | DEBUG | 消息内容 '1' 不匹配任何命令，忽略
2025-07-22 18:52:48 | DEBUG | 收到消息: {'MsgId': 329838346, 'FromUserName': {'string': '47442567074@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 1, 'Content': {'string': 'wxid_ubbh6q832tcs21:\n感觉没必要用户头像'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1753181581, 'MsgSource': '<msgsource>\n\t<pua>1</pua>\n\t<eggIncluded>1</eggIncluded>\n\t<silence>0</silence>\n\t<membercount>494</membercount>\n\t<signature>N0_V1_TxeAINtb|v1_x7YVyEuY</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'PushContent': '郭 : 感觉没必要用户头像', 'NewMsgId': 3216260440504166198, 'MsgSeq': 871392740}
2025-07-22 18:52:48 | INFO | 收到文本消息: 消息ID:329838346 来自:47442567074@chatroom 发送人:wxid_ubbh6q832tcs21 @:[] 内容:感觉没必要用户头像
2025-07-22 18:52:48 | DEBUG | 处理消息内容: '感觉没必要用户头像'
2025-07-22 18:52:48 | DEBUG | 消息内容 '感觉没必要用户头像' 不匹配任何命令，忽略
2025-07-22 18:52:49 | DEBUG | 收到消息: {'MsgId': 147830444, 'FromUserName': {'string': '47442567074@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 1, 'Content': {'string': 'wxid_hqdtktnqvw8e21:\n听1'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1753181582, 'MsgSource': '<msgsource>\n\t<bizflag>0</bizflag>\n\t<pua>1</pua>\n\t<silence>0</silence>\n\t<membercount>494</membercount>\n\t<signature>N0_V1_nYiErtNY|v1_OTh3ct1o</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'PushContent': 'Ritz : 听1', 'NewMsgId': 17089297840099537, 'MsgSeq': 871392741}
2025-07-22 18:52:49 | INFO | 收到文本消息: 消息ID:147830444 来自:47442567074@chatroom 发送人:wxid_hqdtktnqvw8e21 @:[] 内容:听1
2025-07-22 18:52:49 | DEBUG | [Music] 处理选择音乐命令，用户: wxid_hqdtktnqvw8e21, 选择序号: 1
2025-07-22 18:52:49 | DEBUG | [Music] 用户搜索数据: {'result': '简单爱', 'timestamp': 1753181553.8790925, 'group_id': '47442567074@chatroom'}
2025-07-22 18:52:49 | DEBUG | [Music] 开始获取歌曲，关键词: 简单爱, 序号: 1
2025-07-22 18:52:50 | DEBUG | [Music] API请求成功，状态码: 200
2025-07-22 18:52:50 | DEBUG | [Music] API原始响应: {
    "code": 200,
    "msg": "若音频链接为空或只能播放一半请稍后再用",
    "title": "简单爱",
    "singer": "周杰伦",
    "cover": "http://imge.kugou.com/stdmusic/400/20250221/20250221180749429614.jpg",
    "link": "https://www.kugou.com/song/#hash=6E44CE19BE5872384FA184B995CCE23B",
    "music_url": "https://sy-sycdn.kuwo.cn/4a5545166085eacf1620a345e79417f1/687f6d90/resource/s1/19/10/1391330433.flac?src=无损音质 FLAC_30.82 MB?src=flac_30.82 MB?from=longzhu_api",
    "lyrics": "﻿[id:$00000000]\r\n[ar:周杰伦]\r\n[ti:简单爱]\r\n[by:]\r\n[hash:6e44ce19be5872384fa184b995cce23b]\r\n[al:范特西]\r\n[sign:]\r\n[qq:]\r\n[total:0]\r\n[offset:0]\r\n[00:00.00]周杰伦 - 简单爱\r\n[00:06.71]词：徐若瑄\r\n[00:13.43]曲：周杰伦\r\n[00:20.15]编曲：林迈可\r\n[00:26.87]说不上为什么 我变得很主动\r\n[00:31.79]若爱上一个人 什么都会值得去做\r\n[00:36.79]我想大声宣布 对你依依不舍\r\n[00:41.71]连隔壁邻居都猜到我现在的感受\r\n[00:46.68]河边的风 在吹着头发 飘动\r\n[00:51.62]牵着你的手 一阵莫名感动\r\n[00:56.53]我想带你 回我的外婆家\r\n[01:00.29]一起看着日落 一直到我们都睡着\r\n[01:06.43]我想就这样牵着你的手不放开\r\n[01:11.34]爱能不能够永远单纯没有悲哀\r\n[01:16.06]我 想带你骑单车\r\n[01:18.46]我 想和你看棒球\r\n[01:21.61]想这样没担忧\r\n[01:23.47]唱着歌 一直走\r\n[01:26.16]我想就这样牵着你的手不放开\r\n[01:31.17]爱可不可以简简单单没有伤害\r\n[01:35.75]你 靠着我的肩膀\r\n[01:38.31]你 在我胸口睡着\r\n[01:41.31]像这样的生活\r\n[01:43.22]我爱你 你爱我\r\n[01:45.63]想 简简单单 爱\r\n[01:55.51]想 简简单单 爱\r\n[02:05.83]我想大声宣布 对你依依不舍\r\n[02:10.67]连隔壁邻居都猜到我现在的感受\r\n[02:15.68]河边的风 在吹着头发 飘动\r\n[02:20.61]牵着你的手 一阵莫名感动\r\n[02:25.65]我想带你 回我的外婆家\r\n[02:29.31]一起看着日落 一直到我们都睡着\r\n[02:35.43]我想就这样牵着你的手不放开\r\n[02:40.44]爱能不能够永远单纯没有悲哀\r\n[02:44.95]我 想带你骑单车\r\n[02:47.53]我 想和你看棒球\r\n[02:50.68]想这样没担忧\r\n[02:52.50]唱着歌 一直走\r\n[02:55.35]我想就这样牵着你的手不放开\r\n[03:00.21]爱可不可以简简单单没有伤害\r\n[03:04.77]你 靠着我的肩膀\r\n[03:07.36]你 在我胸口睡着\r\n[03:10.44]像这样的生活\r\n[03:12.32]我爱你 你爱我\r\n[03:14.72]想 简简单单 爱\r\n[03:24.63]想 简简单单 爱\r\n[03:34.98]我想就这样牵着你的手不放开\r\n[03:39.84]爱能不能够永远单纯没有悲哀\r\n[03:44.37]我 想带你骑单车\r\n[03:46.95]我 想和你看棒球\r\n[03:50.04]想这样没担忧\r\n[03:51.93]唱着歌 一直走\r\n[03:54.72]我想就这样牵着你的手不放开\r\n[03:59.66]爱可不可以简简单单没有伤害\r\n[04:04.17]你 靠着我的肩膀\r\n[04:06.78]你 在我胸口睡着\r\n[04:09.85]像这样的生活\r\n[04:11.72]我爱你 你爱我\r\n"
}
2025-07-22 18:52:50 | DEBUG | [Music] API响应JSON解析成功: {'code': 200, 'msg': '若音频链接为空或只能播放一半请稍后再用', 'title': '简单爱', 'singer': '周杰伦', 'cover': 'http://imge.kugou.com/stdmusic/400/20250221/20250221180749429614.jpg', 'link': 'https://www.kugou.com/song/#hash=6E44CE19BE5872384FA184B995CCE23B', 'music_url': 'https://sy-sycdn.kuwo.cn/4a5545166085eacf1620a345e79417f1/687f6d90/resource/s1/19/10/1391330433.flac?src=无损音质 FLAC_30.82 MB?src=flac_30.82 MB?from=longzhu_api', 'lyrics': '\ufeff[id:$00000000]\r\n[ar:周杰伦]\r\n[ti:简单爱]\r\n[by:]\r\n[hash:6e44ce19be5872384fa184b995cce23b]\r\n[al:范特西]\r\n[sign:]\r\n[qq:]\r\n[total:0]\r\n[offset:0]\r\n[00:00.00]周杰伦 - 简单爱\r\n[00:06.71]词：徐若瑄\r\n[00:13.43]曲：周杰伦\r\n[00:20.15]编曲：林迈可\r\n[00:26.87]说不上为什么 我变得很主动\r\n[00:31.79]若爱上一个人 什么都会值得去做\r\n[00:36.79]我想大声宣布 对你依依不舍\r\n[00:41.71]连隔壁邻居都猜到我现在的感受\r\n[00:46.68]河边的风 在吹着头发 飘动\r\n[00:51.62]牵着你的手 一阵莫名感动\r\n[00:56.53]我想带你 回我的外婆家\r\n[01:00.29]一起看着日落 一直到我们都睡着\r\n[01:06.43]我想就这样牵着你的手不放开\r\n[01:11.34]爱能不能够永远单纯没有悲哀\r\n[01:16.06]我 想带你骑单车\r\n[01:18.46]我 想和你看棒球\r\n[01:21.61]想这样没担忧\r\n[01:23.47]唱着歌 一直走\r\n[01:26.16]我想就这样牵着你的手不放开\r\n[01:31.17]爱可不可以简简单单没有伤害\r\n[01:35.75]你 靠着我的肩膀\r\n[01:38.31]你 在我胸口睡着\r\n[01:41.31]像这样的生活\r\n[01:43.22]我爱你 你爱我\r\n[01:45.63]想 简简单单 爱\r\n[01:55.51]想 简简单单 爱\r\n[02:05.83]我想大声宣布 对你依依不舍\r\n[02:10.67]连隔壁邻居都猜到我现在的感受\r\n[02:15.68]河边的风 在吹着头发 飘动\r\n[02:20.61]牵着你的手 一阵莫名感动\r\n[02:25.65]我想带你 回我的外婆家\r\n[02:29.31]一起看着日落 一直到我们都睡着\r\n[02:35.43]我想就这样牵着你的手不放开\r\n[02:40.44]爱能不能够永远单纯没有悲哀\r\n[02:44.95]我 想带你骑单车\r\n[02:47.53]我 想和你看棒球\r\n[02:50.68]想这样没担忧\r\n[02:52.50]唱着歌 一直走\r\n[02:55.35]我想就这样牵着你的手不放开\r\n[03:00.21]爱可不可以简简单单没有伤害\r\n[03:04.77]你 靠着我的肩膀\r\n[03:07.36]你 在我胸口睡着\r\n[03:10.44]像这样的生活\r\n[03:12.32]我爱你 你爱我\r\n[03:14.72]想 简简单单 爱\r\n[03:24.63]想 简简单单 爱\r\n[03:34.98]我想就这样牵着你的手不放开\r\n[03:39.84]爱能不能够永远单纯没有悲哀\r\n[03:44.37]我 想带你骑单车\r\n[03:46.95]我 想和你看棒球\r\n[03:50.04]想这样没担忧\r\n[03:51.93]唱着歌 一直走\r\n[03:54.72]我想就这样牵着你的手不放开\r\n[03:59.66]爱可不可以简简单单没有伤害\r\n[04:04.17]你 靠着我的肩膀\r\n[04:06.78]你 在我胸口睡着\r\n[04:09.85]像这样的生活\r\n[04:11.72]我爱你 你爱我\r\n'}
2025-07-22 18:52:50 | DEBUG | [Music] 成功解析歌曲信息: 标题=简单爱, 歌手=周杰伦
2025-07-22 18:52:50 | DEBUG | [Music] 开始生成音乐XML
2025-07-22 18:52:50 | DEBUG | [Music] 音乐XML生成成功，准备发送
2025-07-22 18:52:51 | INFO | 发送app消息: 对方wxid:47442567074@chatroom 类型:3 xml:<appmsg appid="wx485a97c844086dc9" sdkver="0"><title>简单爱</title><des>周杰伦</des><action>view</action><type>3</type><showtype>0</showtype><content/><url>https://weixin.qq.com</url><dataurl>https://sy-sycdn.kuwo.cn/4a5545166085eacf1620a345e79417f1/687f6d90/resource/s1/19/10/1391330433.flac?src=无损音质 FLAC_30.82 MB?src=flac_30.82 MB?from=longzhu_api</dataurl><lowurl>https://weixin.qq.com</lowurl><lowdataurl>https://sy-sycdn.kuwo.cn/4a5545166085eacf1620a345e79417f1/687f6d90/resource/s1/19/10/1391330433.flac?src=无损音质 FLAC_30.82 MB?src=flac_30.82 MB?from=longzhu_api</lowdataurl><recorditem/><thumburl>http://imge.kugou.com/stdmusic/400/20250221/20250221180749429614.jpg</thumburl><messageaction/><laninfo/><extinfo/><sourceusername/><sourcedisplayname/><songlyric>﻿[id:$00000000]
[ar:周杰伦]
[ti:简单爱]
[by:]
[hash:6e44ce19be5872384fa184b995cce23b]
[al:范特西]
[sign:]
[qq:]
[total:0]
[offset:0]
[00:00.00]周杰伦 - 简单爱
[00:06.71]词：徐若瑄
[00:13.43]曲：周杰伦
[00:20.15]编曲：林迈可
[00:26.87]说不上为什么 我变得很主动
[00:31.79]若爱上一个人 什么都会值得去做
[00:36.79]我想大声宣布 对你依依不舍
[00:41.71]连隔壁邻居都猜到我现在的感受
[00:46.68]河边的风 在吹着头发 飘动
[00:51.62]牵着你的手 一阵莫名感动
[00:56.53]我想带你 回我的外婆家
[01:00.29]一起看着日落 一直到我们都睡着
[01:06.43]我想就这样牵着你的手不放开
[01:11.34]爱能不能够永远单纯没有悲哀
[01:16.06]我 想带你骑单车
[01:18.46]我 想和你看棒球
[01:21.61]想这样没担忧
[01:23.47]唱着歌 一直走
[01:26.16]我想就这样牵着你的手不放开
[01:31.17]爱可不可以简简单单没有伤害
[01:35.75]你 靠着我的肩膀
[01:38.31]你 在我胸口睡着
[01:41.31]像这样的生活
[01:43.22]我爱你 你爱我
[01:45.63]想 简简单单 爱
[01:55.51]想 简简单单 爱
[02:05.83]我想大声宣布 对你依依不舍
[02:10.67]连隔壁邻居都猜到我现在的感受
[02:15.68]河边的风 在吹着头发 飘动
[02:20.61]牵着你的手 一阵莫名感动
[02:25.65]我想带你 回我的外婆家
[02:29.31]一起看着日落 一直到我们都睡着
[02:35.43]我想就这样牵着你的手不放开
[02:40.44]爱能不能够永远单纯没有悲哀
[02:44.95]我 想带你骑单车
[02:47.53]我 想和你看棒球
[02:50.68]想这样没担忧
[02:52.50]唱着歌 一直走
[02:55.35]我想就这样牵着你的手不放开
[03:00.21]爱可不可以简简单单没有伤害
[03:04.77]你 靠着我的肩膀
[03:07.36]你 在我胸口睡着
[03:10.44]像这样的生活
[03:12.32]我爱你 你爱我
[03:14.72]想 简简单单 爱
[03:24.63]想 简简单单 爱
[03:34.98]我想就这样牵着你的手不放开
[03:39.84]爱能不能够永远单纯没有悲哀
[03:44.37]我 想带你骑单车
[03:46.95]我 想和你看棒球
[03:50.04]想这样没担忧
[03:51.93]唱着歌 一直走
[03:54.72]我想就这样牵着你的手不放开
[03:59.66]爱可不可以简简单单没有伤害
[04:04.17]你 靠着我的肩膀
[04:06.78]你 在我胸口睡着
[04:09.85]像这样的生活
[04:11.72]我爱你 你爱我
</songlyric><commenturl/><appattach><totallen>0</totallen><attachid/><emoticonmd5/><fileext/><aeskey/></appattach><webviewshared><publisherId/><publisherReqId>0</publisherReqId></webviewshared><weappinfo><pagepath/><username/><appid/><appservicetype>0</appservicetype></weappinfo><websearch/><songalbumurl>http://imge.kugou.com/stdmusic/400/20250221/20250221180749429614.jpg</songalbumurl></appmsg><fromusername>wxid_4usgcju5ey9q29</fromusername><scene>0</scene><appinfo><version>1</version><appname></appname></appinfo><commenturl/>
2025-07-22 18:52:51 | DEBUG | [Music] 音乐消息发送完成
2025-07-22 18:52:51 | DEBUG | 处理消息内容: '听1'
2025-07-22 18:52:51 | DEBUG | 消息内容 '听1' 不匹配任何命令，忽略
2025-07-22 18:53:01 | DEBUG | 收到消息: {'MsgId': 1227525524, 'FromUserName': {'string': '47442567074@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 1, 'Content': {'string': 'wxid_ubbh6q832tcs21:\n直接默认瑶瑶就好了'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1753181593, 'MsgSource': '<msgsource>\n\t<pua>1</pua>\n\t<eggIncluded>1</eggIncluded>\n\t<silence>0</silence>\n\t<membercount>494</membercount>\n\t<signature>N0_V1_aUzF3x/Z|v1_dmY8mf5/</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'PushContent': '郭 : 直接默认瑶瑶就好了', 'NewMsgId': 7640451559743353292, 'MsgSeq': 871392744}
2025-07-22 18:53:01 | INFO | 收到文本消息: 消息ID:1227525524 来自:47442567074@chatroom 发送人:wxid_ubbh6q832tcs21 @:[] 内容:直接默认瑶瑶就好了
2025-07-22 18:53:01 | DEBUG | 处理消息内容: '直接默认瑶瑶就好了'
2025-07-22 18:53:01 | DEBUG | 消息内容 '直接默认瑶瑶就好了' 不匹配任何命令，忽略
2025-07-22 18:53:58 | DEBUG | 收到消息: {'MsgId': 465758174, 'FromUserName': {'string': '47442567074@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 1, 'Content': {'string': 'wxid_tan3ye5x2jiu22:\n你发折叠信息默认机器人'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1753181651, 'MsgSource': '<msgsource>\n\t<pua>1</pua>\n\t<eggIncluded>1</eggIncluded>\n\t<silence>0</silence>\n\t<membercount>494</membercount>\n\t<signature>N0_V1_5z8WaptW|v1_tIACTlf0</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'PushContent': 'Airky : 你发折叠信息默认机器人', 'NewMsgId': 273958072129488300, 'MsgSeq': 871392745}
2025-07-22 18:53:58 | INFO | 收到文本消息: 消息ID:465758174 来自:47442567074@chatroom 发送人:wxid_tan3ye5x2jiu22 @:[] 内容:你发折叠信息默认机器人
2025-07-22 18:53:58 | DEBUG | 处理消息内容: '你发折叠信息默认机器人'
2025-07-22 18:53:58 | DEBUG | 消息内容 '你发折叠信息默认机器人' 不匹配任何命令，忽略
2025-07-22 18:54:05 | DEBUG | 收到消息: {'MsgId': 634953770, 'FromUserName': {'string': '47442567074@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 1, 'Content': {'string': 'wxid_tan3ye5x2jiu22:\n伪造才有必要换头像'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1753181657, 'MsgSource': '<msgsource>\n\t<pua>1</pua>\n\t<eggIncluded>1</eggIncluded>\n\t<silence>0</silence>\n\t<membercount>494</membercount>\n\t<signature>N0_V1_TZvOgnf4|v1_l43cvLSl</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'PushContent': 'Airky : 伪造才有必要换头像', 'NewMsgId': 4639838591566236118, 'MsgSeq': 871392746}
2025-07-22 18:54:05 | INFO | 收到文本消息: 消息ID:634953770 来自:47442567074@chatroom 发送人:wxid_tan3ye5x2jiu22 @:[] 内容:伪造才有必要换头像
2025-07-22 18:54:05 | DEBUG | 处理消息内容: '伪造才有必要换头像'
2025-07-22 18:54:05 | DEBUG | 消息内容 '伪造才有必要换头像' 不匹配任何命令，忽略
2025-07-22 18:54:06 | DEBUG | 收到消息: {'MsgId': 1716603916, 'FromUserName': {'string': '48097389945@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 49, 'Content': {'string': 'wxid_jegyk4i3v7zg22:\n<?xml version="1.0"?>\n<msg>\n\t<appmsg appid="" sdkver="0">\n\t\t<title>还有三个</title>\n\t\t<type>57</type>\n\t\t<appattach>\n\t\t\t<cdnthumbaeskey />\n\t\t\t<aeskey></aeskey>\n\t\t</appattach>\n\t\t<refermsg>\n\t\t\t<type>1</type>\n\t\t\t<svrid>5854685530400709331</svrid>\n\t\t\t<fromusr>48097389945@chatroom</fromusr>\n\t\t\t<chatusr>wxid_hqdtktnqvw8e21</chatusr>\n\t\t\t<displayname>Ritz</displayname>\n\t\t\t<content>全是女孩子</content>\n\t\t\t<msgsource>&lt;msgsource&gt;&lt;sequence_id&gt;836211231&lt;/sequence_id&gt;\n\t&lt;bizflag&gt;0&lt;/bizflag&gt;\n\t&lt;pua&gt;1&lt;/pua&gt;\n\t&lt;silence&gt;1&lt;/silence&gt;\n\t&lt;membercount&gt;62&lt;/membercount&gt;\n\t&lt;signature&gt;N0_V1_/Zc3Xjqm|v1_cfLXgCNI&lt;/signature&gt;\n\t&lt;tmp_node&gt;\n\t\t&lt;publisher-id&gt;&lt;/publisher-id&gt;\n\t&lt;/tmp_node&gt;\n&lt;/msgsource&gt;\n</msgsource>\n\t\t\t<createtime>1753181521</createtime>\n\t\t</refermsg>\n\t</appmsg>\n\t<fromusername>wxid_jegyk4i3v7zg22</fromusername>\n\t<scene>0</scene>\n\t<appinfo>\n\t\t<version>1</version>\n\t\t<appname />\n\t</appinfo>\n\t<commenturl />\n</msg>\n'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1753181659, 'MsgSource': '<msgsource>\n\t<bizflag>0</bizflag>\n\t<pua>1</pua>\n\t<eggIncluded>1</eggIncluded>\n\t<sec_msg_node>\n\t\t<uuid>d9a0ecae3807804a4acabfeb70edcaa7_</uuid>\n\t\t<risk-file-flag />\n\t\t<risk-file-md5-list />\n\t</sec_msg_node>\n\t<silence>0</silence>\n\t<membercount>62</membercount>\n\t<signature>N0_V1_h2uwUiEE|v1_NvvRqNqF</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'PushContent': '阿尼亚与她 : 还有三个', 'NewMsgId': 5392536501394124673, 'MsgSeq': 871392747}
2025-07-22 18:54:06 | DEBUG | 从群聊消息中提取发送者: wxid_jegyk4i3v7zg22
2025-07-22 18:54:06 | DEBUG | 使用已解析的XML处理引用消息
2025-07-22 18:54:06 | INFO | 收到引用消息: 消息ID:1716603916 来自:48097389945@chatroom 发送人:wxid_jegyk4i3v7zg22 内容:还有三个 引用类型:1
2025-07-22 18:54:07 | INFO | [DouBaoImageToImage] 收到引用消息: 还有三个
2025-07-22 18:54:07 | INFO | [TimerTask] 收到引用消息调试信息:
2025-07-22 18:54:07 | INFO |   - 消息内容: 还有三个
2025-07-22 18:54:07 | INFO |   - 群组ID: 48097389945@chatroom
2025-07-22 18:54:07 | INFO |   - 发送人: wxid_jegyk4i3v7zg22
2025-07-22 18:54:07 | INFO |   - 引用信息: {'MsgType': 1, 'Content': '全是女孩子', 'Msgid': '5854685530400709331', 'NewMsgId': '5854685530400709331', 'ToWxid': 'wxid_4usgcju5ey9q29', 'FromWxid': '48097389945@chatroom', 'Nickname': 'Ritz', 'MsgSource': '<msgsource><sequence_id>836211231</sequence_id>\n\t<bizflag>0</bizflag>\n\t<pua>1</pua>\n\t<silence>1</silence>\n\t<membercount>62</membercount>\n\t<signature>N0_V1_/Zc3Xjqm|v1_cfLXgCNI</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'Createtime': '1753181521', 'SenderWxid': 'wxid_jegyk4i3v7zg22'}
2025-07-22 18:54:07 | INFO |   - 引用消息ID: 
2025-07-22 18:54:07 | INFO |   - 引用消息类型: 
2025-07-22 18:54:07 | INFO |   - 引用消息内容: 全是女孩子
2025-07-22 18:54:07 | INFO |   - 引用消息发送人: wxid_jegyk4i3v7zg22
2025-07-22 18:54:15 | DEBUG | 收到消息: {'MsgId': 1465295220, 'FromUserName': {'string': '48097389945@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 1, 'Content': {'string': 'wxid_jegyk4i3v7zg22:\n被我姐赶回老家了，不然九个'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1753181668, 'MsgSource': '<msgsource>\n\t<bizflag>0</bizflag>\n\t<pua>1</pua>\n\t<eggIncluded>1</eggIncluded>\n\t<silence>0</silence>\n\t<membercount>62</membercount>\n\t<signature>N0_V1_2QqbFUj0|v1_2oilGuP/</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'PushContent': '阿尼亚与她 : 被我姐赶回老家了，不然九个', 'NewMsgId': 8869526980226165844, 'MsgSeq': 871392748}
2025-07-22 18:54:15 | INFO | 收到文本消息: 消息ID:1465295220 来自:48097389945@chatroom 发送人:wxid_jegyk4i3v7zg22 @:[] 内容:被我姐赶回老家了，不然九个
2025-07-22 18:54:16 | DEBUG | 处理消息内容: '被我姐赶回老家了，不然九个'
2025-07-22 18:54:16 | DEBUG | 消息内容 '被我姐赶回老家了，不然九个' 不匹配任何命令，忽略
2025-07-22 18:54:43 | DEBUG | 收到消息: {'MsgId': 692360388, 'FromUserName': {'string': '47442567074@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 1, 'Content': {'string': 'wxid_ubbh6q832tcs21:\n听2'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1753181696, 'MsgSource': '<msgsource>\n\t<pua>1</pua>\n\t<eggIncluded>1</eggIncluded>\n\t<silence>0</silence>\n\t<membercount>494</membercount>\n\t<signature>N0_V1_hM0TZfQu|v1_mpaYQQmT</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'PushContent': '郭 : 听2', 'NewMsgId': 7754349414351469939, 'MsgSeq': 871392749}
2025-07-22 18:54:43 | INFO | 收到文本消息: 消息ID:692360388 来自:47442567074@chatroom 发送人:wxid_ubbh6q832tcs21 @:[] 内容:听2
2025-07-22 18:54:43 | DEBUG | [Music] 处理选择音乐命令，用户: wxid_ubbh6q832tcs21, 选择序号: 2
2025-07-22 18:54:43 | DEBUG | [Music] 用户搜索数据: {'result': '大鹏', 'timestamp': 1753181417.3728116, 'group_id': '47442567074@chatroom'}
2025-07-22 18:54:43 | DEBUG | [Music] 开始获取歌曲，关键词: 大鹏, 序号: 2
2025-07-22 18:54:45 | DEBUG | [Music] API请求成功，状态码: 200
2025-07-22 18:54:45 | DEBUG | [Music] API原始响应: {
    "code": 200,
    "msg": "若音频链接为空或只能播放一半请稍后再用",
    "title": "庞龙《你是我的玫瑰花》",
    "singer": "恒俞TRAVIS",
    "cover": "http://imge.kugou.com/stdmusic/400/20220310/20220310192847289017.jpg",
    "link": "https://www.kugou.com/song/#hash=CF1127077B8171F88C4BCB94AE2A066F",
    "music_url": "https://lx-sycdn.kuwo.cn/a3a835367afa1923a7aed1975445002d/687f6e02/resource/a2/6/79/1290609150.aac?src=无损音质 FLAC_3.61 MB?src=128_3.61 MB?from=longzhu_api",
    "lyrics": "[id:$00000000]\r\n[ar:恒俞TRAVIS]\r\n[ti:庞龙《你是我的玫瑰花》]\r\n[by:千逐-_]\r\n[hash:cf1127077b8171f88c4bcb94ae2a066f]\r\n[al:]\r\n[sign:]\r\n[qq:]\r\n[total:236000]\r\n[offset:0]\r\n[00:19.41]一朵花儿开 就有一朵花儿败\r\n[00:36.09]满山的鲜花 只有你是我的真爱\r\n[00:40.92]好好的等待 等你这朵玫瑰开\r\n[00:45.70]满山的鲜花 只有你最可爱\r\n[00:55.36]你是我的玫瑰 你是我的花\r\n[01:00.14]你是我的爱人 是我的牵挂\r\n[01:04.94]你是我的玫瑰\r\n[01:07.34]你是我的花\r\n[01:09.74]你是我的爱人\r\n[01:12.09]是我一生永远 爱着的玫瑰花\r\n[01:37.38]一朵花儿开 就有一朵花儿败\r\n[01:42.10]满山的鲜花 只有你是我的真\r\n[01:46.89]好好的等待 等你结果玫瑰开\r\n[01:51.71]满山的鲜花 只有你最可爱\r\n[02:01.35]你是我的玫瑰 你是我的花\r\n[02:06.12]你是我的爱人 是我的牵挂\r\n[02:10.93]你是我的玫瑰\r\n[02:13.33]你是我的花\r\n[02:15.74]你是我的爱人\r\n[02:18.07]是我一生永远爱着的\r\n[02:23.47]玫瑰花不管风雨有多大\r\n[02:31.95]我只爱你这一朵玫瑰花\r\n[02:40.92]你是我的玫瑰 你是我的花\r\n[02:45.71]你是我的爱人 是我的牵挂\r\n[02:50.54]你是我的玫瑰 你是我的花\r\n[02:55.34]你是我的爱人 是我的牵挂\r\n[03:00.13]你是我的玫瑰 你是我的花\r\n[03:04.93]你是我的爱人 是我的牵挂\r\n[03:09.72]你是我的玫瑰\r\n[03:12.16]你是我的花\r\n[03:14.53]你是我的爱人\r\n[03:16.88]是我一生永远 爱着的玫瑰花\r\n"
}
2025-07-22 18:54:45 | DEBUG | [Music] API响应JSON解析成功: {'code': 200, 'msg': '若音频链接为空或只能播放一半请稍后再用', 'title': '庞龙《你是我的玫瑰花》', 'singer': '恒俞TRAVIS', 'cover': 'http://imge.kugou.com/stdmusic/400/20220310/20220310192847289017.jpg', 'link': 'https://www.kugou.com/song/#hash=CF1127077B8171F88C4BCB94AE2A066F', 'music_url': 'https://lx-sycdn.kuwo.cn/a3a835367afa1923a7aed1975445002d/687f6e02/resource/a2/6/79/1290609150.aac?src=无损音质 FLAC_3.61 MB?src=128_3.61 MB?from=longzhu_api', 'lyrics': '[id:$00000000]\r\n[ar:恒俞TRAVIS]\r\n[ti:庞龙《你是我的玫瑰花》]\r\n[by:千逐-_]\r\n[hash:cf1127077b8171f88c4bcb94ae2a066f]\r\n[al:]\r\n[sign:]\r\n[qq:]\r\n[total:236000]\r\n[offset:0]\r\n[00:19.41]一朵花儿开 就有一朵花儿败\r\n[00:36.09]满山的鲜花 只有你是我的真爱\r\n[00:40.92]好好的等待 等你这朵玫瑰开\r\n[00:45.70]满山的鲜花 只有你最可爱\r\n[00:55.36]你是我的玫瑰 你是我的花\r\n[01:00.14]你是我的爱人 是我的牵挂\r\n[01:04.94]你是我的玫瑰\r\n[01:07.34]你是我的花\r\n[01:09.74]你是我的爱人\r\n[01:12.09]是我一生永远 爱着的玫瑰花\r\n[01:37.38]一朵花儿开 就有一朵花儿败\r\n[01:42.10]满山的鲜花 只有你是我的真\r\n[01:46.89]好好的等待 等你结果玫瑰开\r\n[01:51.71]满山的鲜花 只有你最可爱\r\n[02:01.35]你是我的玫瑰 你是我的花\r\n[02:06.12]你是我的爱人 是我的牵挂\r\n[02:10.93]你是我的玫瑰\r\n[02:13.33]你是我的花\r\n[02:15.74]你是我的爱人\r\n[02:18.07]是我一生永远爱着的\r\n[02:23.47]玫瑰花不管风雨有多大\r\n[02:31.95]我只爱你这一朵玫瑰花\r\n[02:40.92]你是我的玫瑰 你是我的花\r\n[02:45.71]你是我的爱人 是我的牵挂\r\n[02:50.54]你是我的玫瑰 你是我的花\r\n[02:55.34]你是我的爱人 是我的牵挂\r\n[03:00.13]你是我的玫瑰 你是我的花\r\n[03:04.93]你是我的爱人 是我的牵挂\r\n[03:09.72]你是我的玫瑰\r\n[03:12.16]你是我的花\r\n[03:14.53]你是我的爱人\r\n[03:16.88]是我一生永远 爱着的玫瑰花\r\n'}
2025-07-22 18:54:45 | DEBUG | [Music] 成功解析歌曲信息: 标题=庞龙《你是我的玫瑰花》, 歌手=恒俞TRAVIS
2025-07-22 18:54:45 | DEBUG | [Music] 开始生成音乐XML
2025-07-22 18:54:45 | DEBUG | [Music] 音乐XML生成成功，准备发送
2025-07-22 18:54:45 | INFO | 发送app消息: 对方wxid:47442567074@chatroom 类型:3 xml:<appmsg appid="wx485a97c844086dc9" sdkver="0"><title>庞龙《你是我的玫瑰花》</title><des>恒俞TRAVIS</des><action>view</action><type>3</type><showtype>0</showtype><content/><url>https://weixin.qq.com</url><dataurl>https://lx-sycdn.kuwo.cn/a3a835367afa1923a7aed1975445002d/687f6e02/resource/a2/6/79/1290609150.aac?src=无损音质 FLAC_3.61 MB?src=128_3.61 MB?from=longzhu_api</dataurl><lowurl>https://weixin.qq.com</lowurl><lowdataurl>https://lx-sycdn.kuwo.cn/a3a835367afa1923a7aed1975445002d/687f6e02/resource/a2/6/79/1290609150.aac?src=无损音质 FLAC_3.61 MB?src=128_3.61 MB?from=longzhu_api</lowdataurl><recorditem/><thumburl>http://imge.kugou.com/stdmusic/400/20220310/20220310192847289017.jpg</thumburl><messageaction/><laninfo/><extinfo/><sourceusername/><sourcedisplayname/><songlyric>[id:$00000000]
[ar:恒俞TRAVIS]
[ti:庞龙《你是我的玫瑰花》]
[by:千逐-_]
[hash:cf1127077b8171f88c4bcb94ae2a066f]
[al:]
[sign:]
[qq:]
[total:236000]
[offset:0]
[00:19.41]一朵花儿开 就有一朵花儿败
[00:36.09]满山的鲜花 只有你是我的真爱
[00:40.92]好好的等待 等你这朵玫瑰开
[00:45.70]满山的鲜花 只有你最可爱
[00:55.36]你是我的玫瑰 你是我的花
[01:00.14]你是我的爱人 是我的牵挂
[01:04.94]你是我的玫瑰
[01:07.34]你是我的花
[01:09.74]你是我的爱人
[01:12.09]是我一生永远 爱着的玫瑰花
[01:37.38]一朵花儿开 就有一朵花儿败
[01:42.10]满山的鲜花 只有你是我的真
[01:46.89]好好的等待 等你结果玫瑰开
[01:51.71]满山的鲜花 只有你最可爱
[02:01.35]你是我的玫瑰 你是我的花
[02:06.12]你是我的爱人 是我的牵挂
[02:10.93]你是我的玫瑰
[02:13.33]你是我的花
[02:15.74]你是我的爱人
[02:18.07]是我一生永远爱着的
[02:23.47]玫瑰花不管风雨有多大
[02:31.95]我只爱你这一朵玫瑰花
[02:40.92]你是我的玫瑰 你是我的花
[02:45.71]你是我的爱人 是我的牵挂
[02:50.54]你是我的玫瑰 你是我的花
[02:55.34]你是我的爱人 是我的牵挂
[03:00.13]你是我的玫瑰 你是我的花
[03:04.93]你是我的爱人 是我的牵挂
[03:09.72]你是我的玫瑰
[03:12.16]你是我的花
[03:14.53]你是我的爱人
[03:16.88]是我一生永远 爱着的玫瑰花
</songlyric><commenturl/><appattach><totallen>0</totallen><attachid/><emoticonmd5/><fileext/><aeskey/></appattach><webviewshared><publisherId/><publisherReqId>0</publisherReqId></webviewshared><weappinfo><pagepath/><username/><appid/><appservicetype>0</appservicetype></weappinfo><websearch/><songalbumurl>http://imge.kugou.com/stdmusic/400/20220310/20220310192847289017.jpg</songalbumurl></appmsg><fromusername>wxid_4usgcju5ey9q29</fromusername><scene>0</scene><appinfo><version>1</version><appname></appname></appinfo><commenturl/>
2025-07-22 18:54:45 | DEBUG | [Music] 音乐消息发送完成
2025-07-22 18:54:45 | DEBUG | 处理消息内容: '听2'
2025-07-22 18:54:45 | DEBUG | 消息内容 '听2' 不匹配任何命令，忽略
2025-07-22 18:55:11 | DEBUG | 收到消息: {'MsgId': 471825916, 'FromUserName': {'string': '48097389945@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 49, 'Content': {'string': 'wxid_jegyk4i3v7zg22:\n<?xml version="1.0"?>\n<msg>\n\t<appmsg appid="" sdkver="0">\n\t\t<title>[Lol]</title>\n\t\t<type>57</type>\n\t\t<appattach>\n\t\t\t<cdnthumbaeskey />\n\t\t\t<aeskey></aeskey>\n\t\t</appattach>\n\t\t<refermsg>\n\t\t\t<type>1</type>\n\t\t\t<svrid>3949481417031586278</svrid>\n\t\t\t<fromusr>48097389945@chatroom</fromusr>\n\t\t\t<chatusr>wxid_84mmq4cu7ita22</chatusr>\n\t\t\t<displayname>喵小叶</displayname>\n\t\t\t<content>你家后继有人啊</content>\n\t\t\t<msgsource>&lt;msgsource&gt;&lt;sequence_id&gt;836211229&lt;/sequence_id&gt;\n\t&lt;bizflag&gt;0&lt;/bizflag&gt;\n\t&lt;pua&gt;1&lt;/pua&gt;\n\t&lt;eggIncluded&gt;1&lt;/eggIncluded&gt;\n\t&lt;silence&gt;1&lt;/silence&gt;\n\t&lt;membercount&gt;62&lt;/membercount&gt;\n\t&lt;signature&gt;N0_V1_9LdhdzYI|v1_9O1840HK&lt;/signature&gt;\n\t&lt;tmp_node&gt;\n\t\t&lt;publisher-id&gt;&lt;/publisher-id&gt;\n\t&lt;/tmp_node&gt;\n&lt;/msgsource&gt;\n</msgsource>\n\t\t\t<createtime>1753181510</createtime>\n\t\t</refermsg>\n\t</appmsg>\n\t<fromusername>wxid_jegyk4i3v7zg22</fromusername>\n\t<scene>0</scene>\n\t<appinfo>\n\t\t<version>1</version>\n\t\t<appname />\n\t</appinfo>\n\t<commenturl />\n</msg>\n'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1753181724, 'MsgSource': '<msgsource>\n\t<bizflag>0</bizflag>\n\t<pua>1</pua>\n\t<eggIncluded>1</eggIncluded>\n\t<sec_msg_node>\n\t\t<uuid>8fedd644b5bb525b9e8e8c56d4acea7b_</uuid>\n\t\t<risk-file-flag />\n\t\t<risk-file-md5-list />\n\t</sec_msg_node>\n\t<silence>0</silence>\n\t<membercount>62</membercount>\n\t<signature>N0_V1_BnpAtwB9|v1_arPqCdnZ</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'PushContent': '阿尼亚与她 : [破涕为笑]', 'NewMsgId': 3853631262847451049, 'MsgSeq': 871392752}
2025-07-22 18:55:11 | DEBUG | 从群聊消息中提取发送者: wxid_jegyk4i3v7zg22
2025-07-22 18:55:11 | DEBUG | 使用已解析的XML处理引用消息
2025-07-22 18:55:11 | INFO | 收到引用消息: 消息ID:471825916 来自:48097389945@chatroom 发送人:wxid_jegyk4i3v7zg22 内容:[Lol] 引用类型:1
2025-07-22 18:55:12 | INFO | [DouBaoImageToImage] 收到引用消息: [Lol]
2025-07-22 18:55:12 | INFO | [TimerTask] 收到引用消息调试信息:
2025-07-22 18:55:12 | INFO |   - 消息内容: [Lol]
2025-07-22 18:55:12 | INFO |   - 群组ID: 48097389945@chatroom
2025-07-22 18:55:12 | INFO |   - 发送人: wxid_jegyk4i3v7zg22
2025-07-22 18:55:12 | INFO |   - 引用信息: {'MsgType': 1, 'Content': '你家后继有人啊', 'Msgid': '3949481417031586278', 'NewMsgId': '3949481417031586278', 'ToWxid': 'wxid_4usgcju5ey9q29', 'FromWxid': '48097389945@chatroom', 'Nickname': '喵小叶', 'MsgSource': '<msgsource><sequence_id>836211229</sequence_id>\n\t<bizflag>0</bizflag>\n\t<pua>1</pua>\n\t<eggIncluded>1</eggIncluded>\n\t<silence>1</silence>\n\t<membercount>62</membercount>\n\t<signature>N0_V1_9LdhdzYI|v1_9O1840HK</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'Createtime': '1753181510', 'SenderWxid': 'wxid_jegyk4i3v7zg22'}
2025-07-22 18:55:12 | INFO |   - 引用消息ID: 
2025-07-22 18:55:12 | INFO |   - 引用消息类型: 
2025-07-22 18:55:12 | INFO |   - 引用消息内容: 你家后继有人啊
2025-07-22 18:55:12 | INFO |   - 引用消息发送人: wxid_jegyk4i3v7zg22
2025-07-22 18:55:14 | DEBUG | 收到消息: {'MsgId': 1675737109, 'FromUserName': {'string': '48097389945@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 1, 'Content': {'string': 'wxid_7o4g44suf20t22:\n你都带他们干啥了啊'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1753181727, 'MsgSource': '<msgsource>\n\t<pua>1</pua>\n\t<eggIncluded>1</eggIncluded>\n\t<silence>0</silence>\n\t<membercount>62</membercount>\n\t<signature>N0_V1_2GxsiP6Z|v1_eiCqf9UK</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'PushContent': '与其，不惹 : 你都带他们干啥了啊', 'NewMsgId': 5330534566182528111, 'MsgSeq': 871392753}
2025-07-22 18:55:14 | INFO | 收到文本消息: 消息ID:1675737109 来自:48097389945@chatroom 发送人:wxid_7o4g44suf20t22 @:[] 内容:你都带他们干啥了啊
2025-07-22 18:55:15 | DEBUG | 处理消息内容: '你都带他们干啥了啊'
2025-07-22 18:55:15 | DEBUG | 消息内容 '你都带他们干啥了啊' 不匹配任何命令，忽略
2025-07-22 18:55:23 | DEBUG | 收到消息: {'MsgId': 1756030390, 'FromUserName': {'string': '48097389945@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 1, 'Content': {'string': 'wxid_jegyk4i3v7zg22:\n抓娃娃'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1753181736, 'MsgSource': '<msgsource>\n\t<bizflag>0</bizflag>\n\t<pua>1</pua>\n\t<eggIncluded>1</eggIncluded>\n\t<silence>0</silence>\n\t<membercount>62</membercount>\n\t<signature>N0_V1_J1Y69oy7|v1_qyuwdqPe</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'PushContent': '阿尼亚与她 : 抓娃娃', 'NewMsgId': 2331718695008226526, 'MsgSeq': 871392754}
2025-07-22 18:55:23 | INFO | 收到文本消息: 消息ID:1756030390 来自:48097389945@chatroom 发送人:wxid_jegyk4i3v7zg22 @:[] 内容:抓娃娃
2025-07-22 18:55:23 | DEBUG | 处理消息内容: '抓娃娃'
2025-07-22 18:55:23 | DEBUG | 消息内容 '抓娃娃' 不匹配任何命令，忽略
2025-07-22 18:55:31 | DEBUG | 收到消息: {'MsgId': 1686983510, 'FromUserName': {'string': '48097389945@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 1, 'Content': {'string': 'wxid_jegyk4i3v7zg22:\n主要我姐他们去逛街'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1753181744, 'MsgSource': '<msgsource>\n\t<bizflag>0</bizflag>\n\t<pua>1</pua>\n\t<eggIncluded>1</eggIncluded>\n\t<silence>0</silence>\n\t<membercount>62</membercount>\n\t<signature>N0_V1_XfrV/Oyq|v1_CVB8KYa/</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'PushContent': '阿尼亚与她 : 主要我姐他们去逛街', 'NewMsgId': 7354474139620414267, 'MsgSeq': 871392755}
2025-07-22 18:55:31 | INFO | 收到文本消息: 消息ID:1686983510 来自:48097389945@chatroom 发送人:wxid_jegyk4i3v7zg22 @:[] 内容:主要我姐他们去逛街
2025-07-22 18:55:32 | DEBUG | 处理消息内容: '主要我姐他们去逛街'
2025-07-22 18:55:32 | DEBUG | 消息内容 '主要我姐他们去逛街' 不匹配任何命令，忽略
2025-07-22 18:55:42 | DEBUG | 收到消息: {'MsgId': 5998372, 'FromUserName': {'string': '48097389945@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 1, 'Content': {'string': 'wxid_jegyk4i3v7zg22:\n然后我带着他们去广场玩'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1753181755, 'MsgSource': '<msgsource>\n\t<bizflag>0</bizflag>\n\t<pua>1</pua>\n\t<eggIncluded>1</eggIncluded>\n\t<silence>0</silence>\n\t<membercount>62</membercount>\n\t<signature>N0_V1_VkWekODK|v1_oqivL5xE</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'PushContent': '阿尼亚与她 : 然后我带着他们去广场玩', 'NewMsgId': 8950765444848540315, 'MsgSeq': 871392756}
2025-07-22 18:55:42 | INFO | 收到文本消息: 消息ID:5998372 来自:48097389945@chatroom 发送人:wxid_jegyk4i3v7zg22 @:[] 内容:然后我带着他们去广场玩
2025-07-22 18:55:43 | DEBUG | 处理消息内容: '然后我带着他们去广场玩'
2025-07-22 18:55:43 | DEBUG | 消息内容 '然后我带着他们去广场玩' 不匹配任何命令，忽略
2025-07-22 18:56:10 | DEBUG | 收到消息: {'MsgId': 528049576, 'FromUserName': {'string': '48097389945@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 1, 'Content': {'string': 'wxid_7o4g44suf20t22:\n然后呢'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1753181783, 'MsgSource': '<msgsource>\n\t<pua>1</pua>\n\t<eggIncluded>1</eggIncluded>\n\t<silence>0</silence>\n\t<membercount>62</membercount>\n\t<signature>N0_V1_meDZPqev|v1_U4DzLjQ2</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'PushContent': '与其，不惹 : 然后呢', 'NewMsgId': 762747047164662960, 'MsgSeq': 871392757}
2025-07-22 18:56:10 | INFO | 收到文本消息: 消息ID:528049576 来自:48097389945@chatroom 发送人:wxid_7o4g44suf20t22 @:[] 内容:然后呢
2025-07-22 18:56:10 | DEBUG | 处理消息内容: '然后呢'
2025-07-22 18:56:10 | DEBUG | 消息内容 '然后呢' 不匹配任何命令，忽略
2025-07-22 18:56:17 | DEBUG | 收到消息: {'MsgId': 413994225, 'FromUserName': {'string': '48097389945@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 1, 'Content': {'string': 'wxid_7o4g44suf20t22:\n现在是中场休息么'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1753181790, 'MsgSource': '<msgsource>\n\t<pua>1</pua>\n\t<eggIncluded>1</eggIncluded>\n\t<silence>0</silence>\n\t<membercount>62</membercount>\n\t<signature>N0_V1_Ti1PWTaD|v1_FaPQssWA</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'PushContent': '与其，不惹 : 现在是中场休息么', 'NewMsgId': 128389060080469921, 'MsgSeq': 871392758}
2025-07-22 18:56:17 | INFO | 收到文本消息: 消息ID:413994225 来自:48097389945@chatroom 发送人:wxid_7o4g44suf20t22 @:[] 内容:现在是中场休息么
2025-07-22 18:56:17 | DEBUG | 处理消息内容: '现在是中场休息么'
2025-07-22 18:56:17 | DEBUG | 消息内容 '现在是中场休息么' 不匹配任何命令，忽略
2025-07-22 18:56:46 | DEBUG | 收到消息: {'MsgId': 927073508, 'FromUserName': {'string': '48097389945@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 1, 'Content': {'string': 'wxid_jegyk4i3v7zg22:\n坐等吃饭'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1753181819, 'MsgSource': '<msgsource>\n\t<bizflag>0</bizflag>\n\t<pua>1</pua>\n\t<eggIncluded>1</eggIncluded>\n\t<silence>0</silence>\n\t<membercount>62</membercount>\n\t<signature>N0_V1_MmzaDE6E|v1_P+k76y4Z</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'PushContent': '阿尼亚与她 : 坐等吃饭', 'NewMsgId': 3241935512873199605, 'MsgSeq': 871392759}
2025-07-22 18:56:46 | INFO | 收到文本消息: 消息ID:927073508 来自:48097389945@chatroom 发送人:wxid_jegyk4i3v7zg22 @:[] 内容:坐等吃饭
2025-07-22 18:56:46 | DEBUG | 处理消息内容: '坐等吃饭'
2025-07-22 18:56:46 | DEBUG | 消息内容 '坐等吃饭' 不匹配任何命令，忽略
2025-07-22 18:56:55 | DEBUG | 收到消息: {'MsgId': 2129565164, 'FromUserName': {'string': '48097389945@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 1, 'Content': {'string': 'wxid_jegyk4i3v7zg22:\n回去得加班了，还有好多事要做'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1753181828, 'MsgSource': '<msgsource>\n\t<bizflag>0</bizflag>\n\t<pua>1</pua>\n\t<eggIncluded>1</eggIncluded>\n\t<silence>0</silence>\n\t<membercount>62</membercount>\n\t<signature>N0_V1_+0htTlNd|v1_T0r3V6Bx</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'PushContent': '阿尼亚与她 : 回去得加班了，还有好多事要做', 'NewMsgId': 7170823769884435122, 'MsgSeq': 871392760}
2025-07-22 18:56:55 | INFO | 收到文本消息: 消息ID:2129565164 来自:48097389945@chatroom 发送人:wxid_jegyk4i3v7zg22 @:[] 内容:回去得加班了，还有好多事要做
2025-07-22 18:56:55 | DEBUG | 处理消息内容: '回去得加班了，还有好多事要做'
2025-07-22 18:56:55 | DEBUG | 消息内容 '回去得加班了，还有好多事要做' 不匹配任何命令，忽略
2025-07-22 18:57:00 | DEBUG | 收到消息: {'MsgId': 1055589068, 'FromUserName': {'string': '48097389945@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 1, 'Content': {'string': 'wxid_jegyk4i3v7zg22:\n明天又没空'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1753181833, 'MsgSource': '<msgsource>\n\t<bizflag>0</bizflag>\n\t<pua>1</pua>\n\t<eggIncluded>1</eggIncluded>\n\t<silence>0</silence>\n\t<membercount>62</membercount>\n\t<signature>N0_V1_7zeNkR4B|v1_pAxx4uRQ</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'PushContent': '阿尼亚与她 : 明天又没空', 'NewMsgId': 7682010699128169226, 'MsgSeq': 871392761}
2025-07-22 18:57:00 | INFO | 收到文本消息: 消息ID:1055589068 来自:48097389945@chatroom 发送人:wxid_jegyk4i3v7zg22 @:[] 内容:明天又没空
2025-07-22 18:57:00 | DEBUG | 处理消息内容: '明天又没空'
2025-07-22 18:57:00 | DEBUG | 消息内容 '明天又没空' 不匹配任何命令，忽略
2025-07-22 18:57:04 | DEBUG | 收到消息: {'MsgId': 526366068, 'FromUserName': {'string': '48097389945@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 1, 'Content': {'string': 'wxid_7o4g44suf20t22:\n哦'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1753181837, 'MsgSource': '<msgsource>\n\t<pua>1</pua>\n\t<eggIncluded>1</eggIncluded>\n\t<silence>0</silence>\n\t<membercount>62</membercount>\n\t<signature>N0_V1_tCjhfkxQ|v1_OCXQpiAj</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'PushContent': '与其，不惹 : 哦', 'NewMsgId': 2150070795317069233, 'MsgSeq': 871392762}
2025-07-22 18:57:04 | INFO | 收到文本消息: 消息ID:526366068 来自:48097389945@chatroom 发送人:wxid_7o4g44suf20t22 @:[] 内容:哦
2025-07-22 18:57:05 | DEBUG | 处理消息内容: '哦'
2025-07-22 18:57:05 | DEBUG | 消息内容 '哦' 不匹配任何命令，忽略
2025-07-22 18:57:11 | DEBUG | 收到消息: {'MsgId': 1091988733, 'FromUserName': {'string': '48097389945@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 47, 'Content': {'string': 'wxid_7o4g44suf20t22:\n<msg><emoji fromusername="wxid_7o4g44suf20t22" tousername="48097389945@chatroom" type="2" idbuffer="media:0_0" md5="89abe7b10e3bbbbbd9744bacaceb58f1" len="2860171" productid="" androidmd5="89abe7b10e3bbbbbd9744bacaceb58f1" androidlen="2860171" s60v3md5="89abe7b10e3bbbbbd9744bacaceb58f1" s60v3len="2860171" s60v5md5="89abe7b10e3bbbbbd9744bacaceb58f1" s60v5len="2860171" cdnurl="http://vweixinf.tc.qq.com/110/20401/stodownload?m=89abe7b10e3bbbbbd9744bacaceb58f1&amp;filekey=30440201010430302e02016e0402535a0420383961626537623130653362626262626439373434626163616365623538663102032ba48b040d00000004627466730000000132&amp;hy=SZ&amp;storeid=26841cc04000ce4f6293b1ae80000006e01004fb1535a03a6bae1e69b92465&amp;ef=1&amp;bizid=1022" designerid="" thumburl="" encrypturl="http://vweixinf.tc.qq.com/110/20402/stodownload?m=42a840cf5120c1ecec9999e348ea4003&amp;filekey=30440201010430302e02016e0402535a0420343261383430636635313230633165636563393939396533343865613430303302032ba490040d00000004627466730000000132&amp;hy=SZ&amp;storeid=26841cc0500006a6d293b1ae80000006e02004fb2535a03a6bae1e69b92489&amp;ef=2&amp;bizid=1022" aeskey="69f288a879624adeb13982051165b73c" externurl="http://vweixinf.tc.qq.com/110/20403/stodownload?m=4f45f7993f8c46323606fc6b1d7c5f0f&amp;filekey=30440201010430302e02016e0402535a042034663435663739393366386334363332333630366663366231643763356630660203015250040d00000004627466730000000132&amp;hy=SZ&amp;storeid=26841cc050003a85d293b1ae80000006e03004fb3535a03a6bae1e69b924b2&amp;ef=3&amp;bizid=1022" externmd5="2834b5b9dc8dd0da3b83a502807357fb" width="300" height="300" tpurl="" tpauthkey="" attachedtext="" attachedtextcolor="" lensid="" emojiattr="" linkid="" desc=""></emoji></msg>'}, 'Status': 3, 'ImgStatus': 2, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1753181844, 'MsgSource': '<msgsource>\n\t<silence>0</silence>\n\t<membercount>62</membercount>\n\t<signature>N0_V1_lBHpZ47d|v1_OgspgOK8</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'PushContent': '与其，不惹在群聊中发了一个表情', 'NewMsgId': 5213002383173514513, 'MsgSeq': 871392763}
2025-07-22 18:57:11 | INFO | 收到表情消息: 消息ID:1091988733 来自:48097389945@chatroom 发送人:wxid_7o4g44suf20t22 MD5:89abe7b10e3bbbbbd9744bacaceb58f1 大小:2860171
2025-07-22 18:57:11 | DEBUG | [DoubaoImageRecognition] 缓存表情消息: 5213002383173514513
2025-07-22 18:57:11 | DEBUG | 收到消息: {'MsgId': 1451648428, 'FromUserName': {'string': '48097389945@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 47, 'Content': {'string': 'wxid_jegyk4i3v7zg22:\n<msg><emoji fromusername="wxid_jegyk4i3v7zg22" tousername="48097389945@chatroom" type="2" idbuffer="media:0_0" md5="5c12a034bf5f5425ea2679375c1a5d45" len="324393" productid="" androidmd5="5c12a034bf5f5425ea2679375c1a5d45" androidlen="324393" s60v3md5="5c12a034bf5f5425ea2679375c1a5d45" s60v3len="324393" s60v5md5="5c12a034bf5f5425ea2679375c1a5d45" s60v5len="324393" cdnurl="http://wxapp.tc.qq.com/262/20304/stodownload?m=5c12a034bf5f5425ea2679375c1a5d45&amp;filekey=30350201010421301f020201060402535a04105c12a034bf5f5425ea2679375c1a5d45020304f329040d00000004627466730000000132&amp;hy=SZ&amp;storeid=2685114de0004b6a83f4e1a720000010600004f50535a2a4fc15156beb55d4&amp;bizid=1023" designerid="" thumburl="" encrypturl="http://wxapp.tc.qq.com/262/20304/stodownload?m=2220223f7773dc5681d03c78b6073865&amp;filekey=30350201010421301f020201060402535a04102220223f7773dc5681d03c78b6073865020304f330040d00000004627466730000000132&amp;hy=SZ&amp;storeid=2685114de0006dd173f4e1a720000010600004f50535a0a0f001156ef5ad16&amp;bizid=1023" aeskey="6d10e5c233e234c025249909fe3a1316" externurl="http://wxapp.tc.qq.com/262/20304/stodownload?m=b68bd6728cbd4859934544eb1fd5e5b8&amp;filekey=30350201010421301f020201060402535a0410b68bd6728cbd4859934544eb1fd5e5b8020300c1f0040d00000004627466730000000132&amp;hy=SZ&amp;storeid=2685115e8000621f89b12ddcc0000010600004f50535a0cf68bc1e6c5fbe64&amp;bizid=1023" externmd5="1dbadb0a2b6794a6f89ea965326b2fa4" width="400" height="400" tpurl="" tpauthkey="" attachedtext="" attachedtextcolor="" lensid="" emojiattr="" linkid="" desc=""></emoji><gameext type="0" content="0"></gameext><extcommoninfo></extcommoninfo></msg>'}, 'Status': 3, 'ImgStatus': 2, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1753181844, 'MsgSource': '<msgsource>\n\t<silence>0</silence>\n\t<membercount>62</membercount>\n\t<signature>N0_V1_Wlujdqaa|v1_Yltx3yoC</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'PushContent': '阿尼亚与她在群聊中发了一个表情', 'NewMsgId': 4497131441919592617, 'MsgSeq': 871392764}
2025-07-22 18:57:11 | INFO | 收到表情消息: 消息ID:1451648428 来自:48097389945@chatroom 发送人:wxid_jegyk4i3v7zg22 MD5:5c12a034bf5f5425ea2679375c1a5d45 大小:324393
2025-07-22 18:57:12 | DEBUG | [DoubaoImageRecognition] 缓存表情消息: 4497131441919592617
2025-07-22 18:59:00 | DEBUG | 收到消息: {'MsgId': 1651678233, 'FromUserName': {'string': '48097389945@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 49, 'Content': {'string': 'wxid_wlnzvr8ivgd422:\n<?xml version="1.0"?>\n<msg>\n\t<appmsg appid="" sdkver="0">\n\t\t<title>德华</title>\n\t\t<des />\n\t\t<username />\n\t\t<action>view</action>\n\t\t<type>57</type>\n\t\t<showtype>0</showtype>\n\t\t<content />\n\t\t<url />\n\t\t<lowurl />\n\t\t<forwardflag>0</forwardflag>\n\t\t<dataurl />\n\t\t<lowdataurl />\n\t\t<contentattr>0</contentattr>\n\t\t<streamvideo>\n\t\t\t<streamvideourl />\n\t\t\t<streamvideototaltime>0</streamvideototaltime>\n\t\t\t<streamvideotitle />\n\t\t\t<streamvideowording />\n\t\t\t<streamvideoweburl />\n\t\t\t<streamvideothumburl />\n\t\t\t<streamvideoaduxinfo />\n\t\t\t<streamvideopublishid />\n\t\t</streamvideo>\n\t\t<canvasPageItem>\n\t\t\t<canvasPageXml><![CDATA[]]></canvasPageXml>\n\t\t</canvasPageItem>\n\t\t<refermsg>\n\t\t\t<type>1</type>\n\t\t\t<svrid>8965851345330874283</svrid>\n\t\t\t<fromusr>48097389945@chatroom</fromusr>\n\t\t\t<chatusr>wxid_jegyk4i3v7zg22</chatusr>\n\t\t\t<displayname>阿尼亚与她</displayname>\n\t\t\t<msgsource>&lt;msgsource&gt;\n\t&lt;bizflag&gt;0&lt;/bizflag&gt;\n\t&lt;pua&gt;1&lt;/pua&gt;\n\t&lt;eggIncluded&gt;1&lt;/eggIncluded&gt;\n\t&lt;silence&gt;1&lt;/silence&gt;\n\t&lt;membercount&gt;62&lt;/membercount&gt;\n\t&lt;signature&gt;N0_V1_RZIQJbOp|v1_gtRH2J32&lt;/signature&gt;\n\t&lt;tmp_node&gt;\n\t\t&lt;publisher-id&gt;&lt;/publisher-id&gt;\n\t&lt;/tmp_node&gt;\n&lt;/msgsource&gt;\n</msgsource>\n\t\t\t<content>\n一个人带六娃</content>\n\t\t\t<strid />\n\t\t\t<createtime>1753181449</createtime>\n\t\t</refermsg>\n\t\t<appattach>\n\t\t\t<totallen>0</totallen>\n\t\t\t<attachid />\n\t\t\t<cdnattachurl />\n\t\t\t<emoticonmd5 />\n\t\t\t<aeskey />\n\t\t\t<fileext />\n\t\t\t<islargefilemsg>0</islargefilemsg>\n\t\t</appattach>\n\t\t<extinfo />\n\t\t<androidsource>0</androidsource>\n\t\t<thumburl />\n\t\t<mediatagname />\n\t\t<messageaction><![CDATA[]]></messageaction>\n\t\t<messageext><![CDATA[]]></messageext>\n\t\t<emoticongift>\n\t\t\t<packageflag>0</packageflag>\n\t\t\t<packageid />\n\t\t</emoticongift>\n\t\t<emoticonshared>\n\t\t\t<packageflag>0</packageflag>\n\t\t\t<packageid />\n\t\t</emoticonshared>\n\t\t<designershared>\n\t\t\t<designeruin>0</designeruin>\n\t\t\t<designername>null</designername>\n\t\t\t<designerrediretcturl><![CDATA[null]]></designerrediretcturl>\n\t\t</designershared>\n\t\t<emotionpageshared>\n\t\t\t<tid>0</tid>\n\t\t\t<title>null</title>\n\t\t\t<desc>null</desc>\n\t\t\t<iconUrl><![CDATA[null]]></iconUrl>\n\t\t\t<secondUrl>null</secondUrl>\n\t\t\t<pageType>0</pageType>\n\t\t\t<setKey>null</setKey>\n\t\t</emotionpageshared>\n\t\t<webviewshared>\n\t\t\t<shareUrlOriginal />\n\t\t\t<shareUrlOpen />\n\t\t\t<jsAppId />\n\t\t\t<publisherId />\n\t\t\t<publisherReqId />\n\t\t</webviewshared>\n\t\t<template_id />\n\t\t<md5 />\n\t\t<websearch>\n\t\t\t<rec_category>0</rec_category>\n\t\t\t<channelId>0</channelId>\n\t\t</websearch>\n\t\t<weappinfo>\n\t\t\t<username />\n\t\t\t<appid />\n\t\t\t<appservicetype>0</appservicetype>\n\t\t\t<secflagforsinglepagemode>0</secflagforsinglepagemode>\n\t\t\t<videopageinfo>\n\t\t\t\t<thumbwidth>0</thumbwidth>\n\t\t\t\t<thumbheight>0</thumbheight>\n\t\t\t\t<fromopensdk>0</fromopensdk>\n\t\t\t</videopageinfo>\n\t\t</weappinfo>\n\t\t<statextstr />\n\t\t<musicShareItem>\n\t\t\t<musicDuration>0</musicDuration>\n\t\t</musicShareItem>\n\t\t<finderLiveProductShare>\n\t\t\t<finderLiveID><![CDATA[]]></finderLiveID>\n\t\t\t<finderUsername><![CDATA[]]></finderUsername>\n\t\t\t<finderObjectID><![CDATA[]]></finderObjectID>\n\t\t\t<finderNonceID><![CDATA[]]></finderNonceID>\n\t\t\t<liveStatus><![CDATA[]]></liveStatus>\n\t\t\t<appId><![CDATA[]]></appId>\n\t\t\t<pagePath><![CDATA[]]></pagePath>\n\t\t\t<productId><![CDATA[]]></productId>\n\t\t\t<coverUrl><![CDATA[]]></coverUrl>\n\t\t\t<productTitle><![CDATA[]]></productTitle>\n\t\t\t<marketPrice><![CDATA[0]]></marketPrice>\n\t\t\t<sellingPrice><![CDATA[0]]></sellingPrice>\n\t\t\t<platformHeadImg><![CDATA[]]></platformHeadImg>\n\t\t\t<platformName><![CDATA[]]></platformName>\n\t\t\t<shopWindowId><![CDATA[]]></shopWindowId>\n\t\t\t<flashSalePrice><![CDATA[0]]></flashSalePrice>\n\t\t\t<flashSaleEndTime><![CDATA[0]]></flashSaleEndTime>\n\t\t\t<ecSource><![CDATA[]]></ecSource>\n\t\t\t<sellingPriceWording><![CDATA[]]></sellingPriceWording>\n\t\t\t<platformIconURL><![CDATA[]]></platformIconURL>\n\t\t\t<firstProductTagURL><![CDATA[]]></firstProductTagURL>\n\t\t\t<firstProductTagAspectRatioString><![CDATA[0.0]]></firstProductTagAspectRatioString>\n\t\t\t<secondProductTagURL><![CDATA[]]></secondProductTagURL>\n\t\t\t<secondProductTagAspectRatioString><![CDATA[0.0]]></secondProductTagAspectRatioString>\n\t\t\t<firstGuaranteeWording><![CDATA[]]></firstGuaranteeWording>\n\t\t\t<secondGuaranteeWording><![CDATA[]]></secondGuaranteeWording>\n\t\t\t<thirdGuaranteeWording><![CDATA[]]></thirdGuaranteeWording>\n\t\t\t<isPriceBeginShow>false</isPriceBeginShow>\n\t\t\t<lastGMsgID><![CDATA[]]></lastGMsgID>\n\t\t\t<promoterKey><![CDATA[]]></promoterKey>\n\t\t\t<discountWording><![CDATA[]]></discountWording>\n\t\t\t<priceSuffixDescription><![CDATA[]]></priceSuffixDescription>\n\t\t\t<productCardKey><![CDATA[]]></productCardKey>\n\t\t\t<isWxShop><![CDATA[]]></isWxShop>\n\t\t\t<brandIconUrl><![CDATA[]]></brandIconUrl>\n\t\t\t<rIconUrl><![CDATA[]]></rIconUrl>\n\t\t\t<rIconUrlDarkMode><![CDATA[]]></rIconUrlDarkMode>\n\t\t\t<topShopIconUrl><![CDATA[]]></topShopIconUrl>\n\t\t\t<topShopIconUrlDarkMode><![CDATA[]]></topShopIconUrlDarkMode>\n\t\t\t<simplifyTopShopIconUrl><![CDATA[]]></simplifyTopShopIconUrl>\n\t\t\t<simplifyTopShopIconUrlDarkmode><![CDATA[]]></simplifyTopShopIconUrlDarkmode>\n\t\t\t<topShopIconWidth><![CDATA[0]]></topShopIconWidth>\n\t\t\t<topShopIconHeight><![CDATA[0]]></topShopIconHeight>\n\t\t\t<simplifyTopShopIconWidth><![CDATA[0]]></simplifyTopShopIconWidth>\n\t\t\t<simplifyTopShopIconHeight><![CDATA[0]]></simplifyTopShopIconHeight>\n\t\t\t<showBoxItemStringList />\n\t\t</finderLiveProductShare>\n\t\t<finderOrder>\n\t\t\t<appID><![CDATA[]]></appID>\n\t\t\t<orderID><![CDATA[]]></orderID>\n\t\t\t<path><![CDATA[]]></path>\n\t\t\t<priceWording><![CDATA[]]></priceWording>\n\t\t\t<stateWording><![CDATA[]]></stateWording>\n\t\t\t<productImageURL><![CDATA[]]></productImageURL>\n\t\t\t<products><![CDATA[]]></products>\n\t\t\t<productsCount><![CDATA[0]]></productsCount>\n\t\t\t<orderType><![CDATA[0]]></orderType>\n\t\t\t<newPriceWording><![CDATA[]]></newPriceWording>\n\t\t\t<newStateWording><![CDATA[]]></newStateWording>\n\t\t\t<useNewWording><![CDATA[0]]></useNewWording>\n\t\t</finderOrder>\n\t\t<finderShopWindowShare>\n\t\t\t<finderUsername><![CDATA[]]></finderUsername>\n\t\t\t<avatar><![CDATA[]]></avatar>\n\t\t\t<nickname><![CDATA[]]></nickname>\n\t\t\t<commodityInStockCount><![CDATA[]]></commodityInStockCount>\n\t\t\t<appId><![CDATA[]]></appId>\n\t\t\t<path><![CDATA[]]></path>\n\t\t\t<appUsername><![CDATA[]]></appUsername>\n\t\t\t<query><![CDATA[]]></query>\n\t\t\t<liteAppId><![CDATA[]]></liteAppId>\n\t\t\t<liteAppPath><![CDATA[]]></liteAppPath>\n\t\t\t<liteAppQuery><![CDATA[]]></liteAppQuery>\n\t\t\t<platformTagURL><![CDATA[]]></platformTagURL>\n\t\t\t<saleWording><![CDATA[]]></saleWording>\n\t\t\t<lastGMsgID><![CDATA[]]></lastGMsgID>\n\t\t\t<profileTypeWording><![CDATA[]]></profileTypeWording>\n\t\t\t<saleWordingExtra><![CDATA[]]></saleWordingExtra>\n\t\t\t<isWxShop><![CDATA[]]></isWxShop>\n\t\t\t<platformIconUrl><![CDATA[]]></platformIconUrl>\n\t\t\t<brandIconUrl><![CDATA[]]></brandIconUrl>\n\t\t\t<description><![CDATA[]]></description>\n\t\t\t<backgroundUrl><![CDATA[]]></backgroundUrl>\n\t\t\t<darkModePlatformIconUrl><![CDATA[]]></darkModePlatformIconUrl>\n\t\t\t<rIconUrl><![CDATA[]]></rIconUrl>\n\t\t\t<rIconUrlDarkMode><![CDATA[]]></rIconUrlDarkMode>\n\t\t\t<rWords><![CDATA[]]></rWords>\n\t\t\t<topShopIconUrl><![CDATA[]]></topShopIconUrl>\n\t\t\t<topShopIconUrlDarkMode><![CDATA[]]></topShopIconUrlDarkMode>\n\t\t\t<simplifyTopShopIconUrl><![CDATA[]]></simplifyTopShopIconUrl>\n\t\t\t<simplifyTopShopIconUrlDarkmode><![CDATA[]]></simplifyTopShopIconUrlDarkmode>\n\t\t\t<topShopIconWidth><![CDATA[0]]></topShopIconWidth>\n\t\t\t<topShopIconHeight><![CDATA[0]]></topShopIconHeight>\n\t\t\t<simplifyTopShopIconWidth><![CDATA[0]]></simplifyTopShopIconWidth>\n\t\t\t<simplifyTopShopIconHeight><![CDATA[0]]></simplifyTopShopIconHeight>\n\t\t\t<reputationInfo>\n\t\t\t\t<hasReputationInfo>0</hasReputationInfo>\n\t\t\t\t<reputationScore>0</reputationScore>\n\t\t\t\t<reputationWording />\n\t\t\t\t<reputationTextColor />\n\t\t\t\t<reputationLevelWording />\n\t\t\t\t<reputationBackgroundColor />\n\t\t\t</reputationInfo>\n\t\t\t<productImageURLList />\n\t\t</finderShopWindowShare>\n\t\t<findernamecard>\n\t\t\t<username />\n\t\t\t<avatar><![CDATA[]]></avatar>\n\t\t\t<nickname />\n\t\t\t<auth_job />\n\t\t\t<auth_icon>0</auth_icon>\n\t\t\t<auth_icon_url />\n\t\t\t<ecSource><![CDATA[]]></ecSource>\n\t\t\t<lastGMsgID><![CDATA[]]></lastGMsgID>\n\t\t</findernamecard>\n\t\t<finderGuarantee>\n\t\t\t<scene><![CDATA[0]]></scene>\n\t\t</finderGuarantee>\n\t\t<directshare>0</directshare>\n\t\t<gamecenter>\n\t\t\t<namecard>\n\t\t\t\t<iconUrl />\n\t\t\t\t<name />\n\t\t\t\t<desc />\n\t\t\t\t<tail />\n\t\t\t\t<jumpUrl />\n\t\t\t\t<liteappId />\n\t\t\t\t<liteappPath />\n\t\t\t\t<liteappQuery />\n\t\t\t\t<liteappMinVersion />\n\t\t\t</namecard>\n\t\t</gamecenter>\n\t\t<patMsg>\n\t\t\t<chatUser />\n\t\t\t<records>\n\t\t\t\t<recordNum>0</recordNum>\n\t\t\t</records>\n\t\t</patMsg>\n\t\t<secretmsg>\n\t\t\t<issecretmsg>0</issecretmsg>\n\t\t</secretmsg>\n\t\t<referfromscene>0</referfromscene>\n\t\t<gameshare>\n\t\t\t<liteappext>\n\t\t\t\t<liteappbizdata />\n\t\t\t\t<priority>0</priority>\n\t\t\t</liteappext>\n\t\t\t<appbrandext>\n\t\t\t\t<litegameinfo />\n\t\t\t\t<priority>-1</priority>\n\t\t\t</appbrandext>\n\t\t\t<gameshareid />\n\t\t\t<sharedata />\n\t\t\t<isvideo>0</isvideo>\n\t\t\t<duration>-1</duration>\n\t\t\t<isexposed>0</isexposed>\n\t\t\t<readtext />\n\t\t</gameshare>\n\t\t<tingChatRoomItem>\n\t\t\t<type>0</type>\n\t\t\t<categoryItem>null</categoryItem>\n\t\t\t<categoryId />\n\t\t</tingChatRoomItem>\n\t\t<mpsharetrace>\n\t\t\t<hasfinderelement>0</hasfinderelement>\n\t\t\t<lastgmsgid />\n\t\t</mpsharetrace>\n\t\t<wxgamecard>\n\t\t\t<framesetname />\n\t\t\t<mbcarddata />\n\t\t\t<minpkgversion />\n\t\t\t<clientextinfo />\n\t\t\t<mbcardheight>0</mbcardheight>\n\t\t\t<isoldversion>0</isoldversion>\n\t\t</wxgamecard>\n\t\t<ecskfcard>\n\t\t\t<framesetname />\n\t\t\t<mbcarddata />\n\t\t\t<minupdateunixtimestamp>0</minupdateunixtimestamp>\n\t\t\t<needheader>false</needheader>\n\t\t\t<summary />\n\t\t</ecskfcard>\n\t\t<liteapp>\n\t\t\t<id>null</id>\n\t\t\t<path />\n\t\t\t<query />\n\t\t\t<istransparent>0</istransparent>\n\t\t\t<hideicon>0</hideicon>\n\t\t\t<forbidforward>0</forbidforward>\n\t\t</liteapp>\n\t\t<opensdk_share_is_modified>0</opensdk_share_is_modified>\n\t</appmsg>\n\t<fromusername>wxid_wlnzvr8ivgd422</fromusername>\n\t<scene>0</scene>\n\t<appinfo>\n\t\t<version>1</version>\n\t\t<appname></appname>\n\t</appinfo>\n\t<commenturl></commenturl>\n</msg>\n'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1753181953, 'MsgSource': '<msgsource>\n\t<alnode>\n\t\t<fr>4</fr>\n\t</alnode>\n\t<eggIncluded>1</eggIncluded>\n\t<sec_msg_node>\n\t\t<uuid>1dacd2dfcc61f475be5726cadef489d3_</uuid>\n\t\t<risk-file-flag />\n\t\t<risk-file-md5-list />\n\t</sec_msg_node>\n\t<silence>0</silence>\n\t<membercount>62</membercount>\n\t<signature>N0_V1_SohQUD55|v1_xw2ZvKGy</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'PushContent': '锦岚（开玩笑就退群版） : 德华', 'NewMsgId': 4451421892355845097, 'MsgSeq': 871392765}
2025-07-22 18:59:00 | DEBUG | 从群聊消息中提取发送者: wxid_wlnzvr8ivgd422
2025-07-22 18:59:00 | DEBUG | 使用已解析的XML处理引用消息
2025-07-22 18:59:00 | INFO | 收到引用消息: 消息ID:1651678233 来自:48097389945@chatroom 发送人:wxid_wlnzvr8ivgd422 内容:德华 引用类型:1
2025-07-22 18:59:00 | INFO | [DouBaoImageToImage] 收到引用消息: 德华
2025-07-22 18:59:00 | INFO | [TimerTask] 收到引用消息调试信息:
2025-07-22 18:59:00 | INFO |   - 消息内容: 德华
2025-07-22 18:59:00 | INFO |   - 群组ID: 48097389945@chatroom
2025-07-22 18:59:00 | INFO |   - 发送人: wxid_wlnzvr8ivgd422
2025-07-22 18:59:00 | INFO |   - 引用信息: {'MsgType': 1, 'Content': '\n一个人带六娃', 'Msgid': '8965851345330874283', 'NewMsgId': '8965851345330874283', 'ToWxid': 'wxid_4usgcju5ey9q29', 'FromWxid': '48097389945@chatroom', 'Nickname': '阿尼亚与她', 'MsgSource': '<msgsource>\n\t<bizflag>0</bizflag>\n\t<pua>1</pua>\n\t<eggIncluded>1</eggIncluded>\n\t<silence>1</silence>\n\t<membercount>62</membercount>\n\t<signature>N0_V1_RZIQJbOp|v1_gtRH2J32</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'Createtime': '1753181449', 'SenderWxid': 'wxid_wlnzvr8ivgd422'}
2025-07-22 18:59:00 | INFO |   - 引用消息ID: 
2025-07-22 18:59:00 | INFO |   - 引用消息类型: 
2025-07-22 18:59:00 | INFO |   - 引用消息内容: 
一个人带六娃
2025-07-22 18:59:00 | INFO |   - 引用消息发送人: wxid_wlnzvr8ivgd422
2025-07-22 18:59:12 | DEBUG | 收到消息: {'MsgId': 1777539507, 'FromUserName': {'string': '48097389945@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 1, 'Content': {'string': 'wxid_wlnzvr8ivgd422:\n回家就哄孩子哈哈哈哈哈哈'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1753181965, 'MsgSource': '<msgsource>\n\t<pua>1</pua>\n\t<eggIncluded>1</eggIncluded>\n\t<silence>0</silence>\n\t<membercount>62</membercount>\n\t<signature>N0_V1_VAC+FgZV|v1_r0ZuvxKk</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'PushContent': '锦岚（开玩笑就退群版） : 回家就哄孩子哈哈哈哈哈哈', 'NewMsgId': 4860272999436947975, 'MsgSeq': 871392766}
2025-07-22 18:59:12 | INFO | 收到文本消息: 消息ID:1777539507 来自:48097389945@chatroom 发送人:wxid_wlnzvr8ivgd422 @:[] 内容:回家就哄孩子哈哈哈哈哈哈
2025-07-22 18:59:12 | DEBUG | 处理消息内容: '回家就哄孩子哈哈哈哈哈哈'
2025-07-22 18:59:12 | DEBUG | 消息内容 '回家就哄孩子哈哈哈哈哈哈' 不匹配任何命令，忽略
2025-07-22 19:00:21 | DEBUG | 收到消息: {'MsgId': 1755992129, 'FromUserName': {'string': '48097389945@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 1, 'Content': {'string': 'wxid_hqdtktnqvw8e21:\n德华是啥意思'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1753182034, 'MsgSource': '<msgsource>\n\t<bizflag>0</bizflag>\n\t<pua>1</pua>\n\t<silence>0</silence>\n\t<membercount>62</membercount>\n\t<signature>N0_V1_iN/xPYlB|v1_UQJJ7iQM</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'PushContent': 'Ritz : 德华是啥意思', 'NewMsgId': 2228389998317687028, 'MsgSeq': 871392767}
2025-07-22 19:00:21 | INFO | 收到文本消息: 消息ID:1755992129 来自:48097389945@chatroom 发送人:wxid_hqdtktnqvw8e21 @:[] 内容:德华是啥意思
2025-07-22 19:00:22 | DEBUG | 处理消息内容: '德华是啥意思'
2025-07-22 19:00:22 | DEBUG | 消息内容 '德华是啥意思' 不匹配任何命令，忽略
2025-07-22 19:00:32 | DEBUG | 收到消息: {'MsgId': 1492560285, 'FromUserName': {'string': '48097389945@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 1, 'Content': {'string': 'wxid_hqdtktnqvw8e21:\n我都不知道这个梗'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1753182045, 'MsgSource': '<msgsource>\n\t<bizflag>0</bizflag>\n\t<pua>1</pua>\n\t<silence>0</silence>\n\t<membercount>62</membercount>\n\t<signature>N0_V1_139/0Hs/|v1_/LPQvJJ7</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'PushContent': 'Ritz : 我都不知道这个梗', 'NewMsgId': 7171821637339262057, 'MsgSeq': 871392768}
2025-07-22 19:00:32 | INFO | 收到文本消息: 消息ID:1492560285 来自:48097389945@chatroom 发送人:wxid_hqdtktnqvw8e21 @:[] 内容:我都不知道这个梗
2025-07-22 19:00:32 | DEBUG | 处理消息内容: '我都不知道这个梗'
2025-07-22 19:00:32 | DEBUG | 消息内容 '我都不知道这个梗' 不匹配任何命令，忽略
2025-07-22 19:00:59 | DEBUG | 收到消息: {'MsgId': 810037792, 'FromUserName': {'string': '48097389945@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 1, 'Content': {'string': 'wxid_jegyk4i3v7zg22:\n我本来也不知道，后来才知道'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1753182072, 'MsgSource': '<msgsource>\n\t<bizflag>0</bizflag>\n\t<pua>1</pua>\n\t<alnode>\n\t\t<inlenlist>7</inlenlist>\n\t</alnode>\n\t<eggIncluded>1</eggIncluded>\n\t<silence>0</silence>\n\t<membercount>62</membercount>\n\t<signature>N0_V1_uZeIVyC/|v1_8gwZ5jl4</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'PushContent': '阿尼亚与她 : 我本来也不知道，后来才知道', 'NewMsgId': 6087155437556440677, 'MsgSeq': 871392769}
2025-07-22 19:00:59 | INFO | 收到文本消息: 消息ID:810037792 来自:48097389945@chatroom 发送人:wxid_jegyk4i3v7zg22 @:[] 内容:我本来也不知道，后来才知道
2025-07-22 19:00:59 | DEBUG | 处理消息内容: '我本来也不知道，后来才知道'
2025-07-22 19:00:59 | DEBUG | 消息内容 '我本来也不知道，后来才知道' 不匹配任何命令，忽略
2025-07-22 19:04:55 | DEBUG | 收到消息: {'MsgId': 143117920, 'FromUserName': {'string': '47442567074@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 1, 'Content': {'string': 'wxid_ubbh6q832tcs21:\n改回去了，默认机器人就好了'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1753182308, 'MsgSource': '<msgsource>\n\t<pua>1</pua>\n\t<eggIncluded>1</eggIncluded>\n\t<silence>0</silence>\n\t<membercount>494</membercount>\n\t<signature>N0_V1_25q8/SAs|v1_J/EfHraK</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'PushContent': '郭 : 改回去了，默认机器人就好了', 'NewMsgId': 4187514545784286486, 'MsgSeq': 871392770}
2025-07-22 19:04:55 | INFO | 收到文本消息: 消息ID:143117920 来自:47442567074@chatroom 发送人:wxid_ubbh6q832tcs21 @:[] 内容:改回去了，默认机器人就好了
2025-07-22 19:04:55 | DEBUG | 处理消息内容: '改回去了，默认机器人就好了'
2025-07-22 19:04:55 | DEBUG | 消息内容 '改回去了，默认机器人就好了' 不匹配任何命令，忽略
2025-07-22 19:05:44 | DEBUG | 收到消息: {'MsgId': 1766603730, 'FromUserName': {'string': '27852221909@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 3, 'Content': {'string': 'wxid_yxdvl6zp4er522:\n<?xml version="1.0"?>\n<msg>\n\t<img aeskey="a8ae0a89cb15e62d33e31a8e3c270d6c" encryver="1" cdnthumbaeskey="a8ae0a89cb15e62d33e31a8e3c270d6c" cdnthumburl="3057020100044b30490201000204ab73270402032f54cf020438c6ccb70204687f7094042439326139386532352d633339322d343431302d393062632d633361623431663861336163020405290a020201000405004c57c300" cdnthumblength="6239" cdnthumbheight="173" cdnthumbwidth="432" cdnmidheight="0" cdnmidwidth="0" cdnhdheight="0" cdnhdwidth="0" cdnmidimgurl="3057020100044b30490201000204ab73270402032f54cf020438c6ccb70204687f7094042439326139386532352d633339322d343431302d393062632d633361623431663861336163020405290a020201000405004c57c300" length="29035" md5="5bf9e86c5c2dcb5ef38d20a72df2bd17" hevc_mid_size="19611" originsourcemd5="625c4e14b5fd9441472333b9ba7b00a7">\n\t\t<secHashInfoBase64>eyJwaGFzaCI6Ijc3NDExMTIwMzEwMDEwMTAiLCJwZHFIYXNoIjoiZmZjZmRjYWZjNjU0ZTFiMGJl\nZjVhYTQ1YjFlNjc4MDg2NDQwMTYwODllZWY4MjQwNWEyODgyMDA5OWVmN2ZlZiJ9\n</secHashInfoBase64>\n\t\t<live>\n\t\t\t<duration>0</duration>\n\t\t\t<size>0</size>\n\t\t\t<md5 />\n\t\t\t<fileid />\n\t\t\t<hdsize>0</hdsize>\n\t\t\t<hdmd5 />\n\t\t\t<hdfileid />\n\t\t\t<stillimagetimems>0</stillimagetimems>\n\t\t</live>\n\t</img>\n\t<platform_signature />\n\t<imgdatahash />\n\t<ImgSourceInfo>\n\t\t<ImgSourceUrl />\n\t\t<BizType>0</BizType>\n\t</ImgSourceInfo>\n</msg>\n'}, 'Status': 3, 'ImgStatus': 2, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1753182357, 'MsgSource': '<msgsource>\n\t<sec_msg_node>\n\t\t<uuid>333abffc8adb9693e3e545d4a09c17d1_</uuid>\n\t\t<risk-file-flag />\n\t\t<risk-file-md5-list />\n\t</sec_msg_node>\n\t<silence>1</silence>\n\t<membercount>146</membercount>\n\t<signature>N0_V1_uINUoHVY|v1_2EyDQI2+</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'NewMsgId': 5353358777754864441, 'MsgSeq': 871392771}
2025-07-22 19:05:44 | INFO | 收到图片消息: 消息ID:1766603730 来自:27852221909@chatroom 发送人:wxid_yxdvl6zp4er522 XML:<?xml version="1.0"?><msg><img aeskey="a8ae0a89cb15e62d33e31a8e3c270d6c" encryver="1" cdnthumbaeskey="a8ae0a89cb15e62d33e31a8e3c270d6c" cdnthumburl="3057020100044b30490201000204ab73270402032f54cf020438c6ccb70204687f7094042439326139386532352d633339322d343431302d393062632d633361623431663861336163020405290a020201000405004c57c300" cdnthumblength="6239" cdnthumbheight="173" cdnthumbwidth="432" cdnmidheight="0" cdnmidwidth="0" cdnhdheight="0" cdnhdwidth="0" cdnmidimgurl="3057020100044b30490201000204ab73270402032f54cf020438c6ccb70204687f7094042439326139386532352d633339322d343431302d393062632d633361623431663861336163020405290a020201000405004c57c300" length="29035" md5="5bf9e86c5c2dcb5ef38d20a72df2bd17" hevc_mid_size="19611" originsourcemd5="625c4e14b5fd9441472333b9ba7b00a7"><secHashInfoBase64>eyJwaGFzaCI6Ijc3NDExMTIwMzEwMDEwMTAiLCJwZHFIYXNoIjoiZmZjZmRjYWZjNjU0ZTFiMGJlZjVhYTQ1YjFlNjc4MDg2NDQwMTYwODllZWY4MjQwNWEyODgyMDA5OWVmN2ZlZiJ9</secHashInfoBase64><live><duration>0</duration><size>0</size><md5 /><fileid /><hdsize>0</hdsize><hdmd5 /><hdfileid /><stillimagetimems>0</stillimagetimems></live></img><platform_signature /><imgdatahash /><ImgSourceInfo><ImgSourceUrl /><BizType>0</BizType></ImgSourceInfo></msg>
2025-07-22 19:05:44 | INFO | [ImageEcho] 保存图片信息成功，当前群 27852221909@chatroom 已存储 5 张图片
2025-07-22 19:05:44 | INFO | [TimerTask] 缓存图片消息: 1766603730
2025-07-22 19:06:08 | DEBUG | 收到消息: {'MsgId': 1527129913, 'FromUserName': {'string': '27852221909@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 1, 'Content': {'string': 'wxid_yxdvl6zp4er522:\n@゛花落ོ.°\u2005师傅又要抢房了'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1753182381, 'MsgSource': '<msgsource>\n\t<atuserlist><![CDATA[wxid_ikxxrwasicud11]]></atuserlist>\n\t<pua>1</pua>\n\t<eggIncluded>1</eggIncluded>\n\t<silence>1</silence>\n\t<membercount>146</membercount>\n\t<signature>N0_V1_bT3ChWHl|v1_YnfFiiLX</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'NewMsgId': 5034813163033159754, 'MsgSeq': 871392772}
2025-07-22 19:06:08 | INFO | 收到文本消息: 消息ID:1527129913 来自:27852221909@chatroom 发送人:wxid_yxdvl6zp4er522 @:['wxid_ikxxrwasicud11'] 内容:@゛花落ོ.° 师傅又要抢房了
2025-07-22 19:06:08 | DEBUG | 处理消息内容: '@゛花落ོ.° 师傅又要抢房了'
2025-07-22 19:06:08 | DEBUG | 消息内容 '@゛花落ོ.° 师傅又要抢房了' 不匹配任何命令，忽略
2025-07-22 19:07:09 | DEBUG | 收到消息: {'MsgId': 1196325004, 'FromUserName': {'string': '27852221909@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 1, 'Content': {'string': 'wxid_ubbh6q832tcs21:\n[破涕为笑]'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1753182442, 'MsgSource': '<msgsource>\n\t<pua>1</pua>\n\t<eggIncluded>1</eggIncluded>\n\t<silence>1</silence>\n\t<membercount>146</membercount>\n\t<signature>N0_V1_8A61Lf9q|v1_bP5oIM/p</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'NewMsgId': 9179996944254437251, 'MsgSeq': 871392773}
2025-07-22 19:07:09 | INFO | 收到表情消息: 消息ID:1196325004 来自:27852221909@chatroom 发送人:wxid_ubbh6q832tcs21 @:[] 内容:[破涕为笑]
2025-07-22 19:07:09 | DEBUG | [DoubaoImageRecognition] 缓存表情消息: 9179996944254437251
2025-07-22 19:12:51 | DEBUG | 收到消息: {'MsgId': 976018148, 'FromUserName': {'string': '27852221909@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 47, 'Content': {'string': 'wxid_u0o5075vpaun22:\n<msg><emoji fromusername="wxid_u0o5075vpaun22" tousername="27852221909@chatroom" type="2" idbuffer="media:0_0" md5="9e9ce4157038d339a0dd89d18b96b076" len="26156" productid="" androidmd5="9e9ce4157038d339a0dd89d18b96b076" androidlen="26156" s60v3md5="9e9ce4157038d339a0dd89d18b96b076" s60v3len="26156" s60v5md5="9e9ce4157038d339a0dd89d18b96b076" s60v5len="26156" cdnurl="http://wxapp.tc.qq.com/262/20304/stodownload?m=9e9ce4157038d339a0dd89d18b96b076&amp;filekey=30340201010420301e020201060402535a04109e9ce4157038d339a0dd89d18b96b0760202662c040d00000004627466730000000132&amp;hy=SZ&amp;storeid=267a5760b0006218b5cb5964e0000010600004f50535a2b5ae1c1569e2cdaf&amp;bizid=1023" designerid="" thumburl="" encrypturl="http://wxapp.tc.qq.com/262/20304/stodownload?m=e23f73d167ec65f1b5e9d157e55fbf85&amp;filekey=30340201010420301e020201060402535a0410e23f73d167ec65f1b5e9d157e55fbf8502026630040d00000004627466730000000132&amp;hy=SZ&amp;storeid=267a5760b0007091b5cb5964e0000010600004f50535a0f1ed151569f99715&amp;bizid=1023" aeskey="7bbbd96001d7ff15a0ad401f5c5a9064" externurl="http://wxapp.tc.qq.com/262/20304/stodownload?m=507cd6ad36d520963c7885be25d1c987&amp;filekey=30340201010420301e020201060402535a0410507cd6ad36d520963c7885be25d1c98702021050040d00000004627466730000000132&amp;hy=SZ&amp;storeid=267a5760b000d84845cb5964e0000010600004f50535a239f201156a578d36&amp;bizid=1023" externmd5="32544dd17a6f0aa21ff338a7bbdf5a79" width="240" height="226" tpurl="" tpauthkey="" attachedtext="" attachedtextcolor="" lensid="" emojiattr="" linkid="" desc=""></emoji><gameext type="0" content="0"></gameext></msg>'}, 'Status': 3, 'ImgStatus': 2, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1753182784, 'MsgSource': '<msgsource>\n\t<silence>1</silence>\n\t<membercount>146</membercount>\n\t<signature>N0_V1_/ZXcVAFD|v1_abimG8XL</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'NewMsgId': 772024878728181737, 'MsgSeq': 871392774}
2025-07-22 19:12:51 | INFO | 收到表情消息: 消息ID:976018148 来自:27852221909@chatroom 发送人:wxid_u0o5075vpaun22 MD5:9e9ce4157038d339a0dd89d18b96b076 大小:26156
2025-07-22 19:12:51 | DEBUG | [DoubaoImageRecognition] 缓存表情消息: 772024878728181737
2025-07-22 19:13:46 | DEBUG | 收到消息: {'MsgId': 1682814150, 'FromUserName': {'string': '48097389945@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 1, 'Content': {'string': 'wxid_wlnzvr8ivgd422:\n[旺柴]'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1753182839, 'MsgSource': '<msgsource>\n\t<pua>1</pua>\n\t<eggIncluded>1</eggIncluded>\n\t<silence>0</silence>\n\t<membercount>62</membercount>\n\t<signature>N0_V1_SHD/HP82|v1_8ikLAhF3</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'PushContent': '锦岚（开玩笑就退群版） : [旺柴]', 'NewMsgId': 8639213987528663576, 'MsgSeq': 871392775}
2025-07-22 19:13:46 | INFO | 收到表情消息: 消息ID:1682814150 来自:48097389945@chatroom 发送人:wxid_wlnzvr8ivgd422 @:[] 内容:[旺柴]
2025-07-22 19:13:46 | DEBUG | [DoubaoImageRecognition] 缓存表情消息: 8639213987528663576
2025-07-22 19:16:45 | DEBUG | 收到消息: {'MsgId': 1843221295, 'FromUserName': {'string': '27852221909@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 47, 'Content': {'string': 'wxid_ikxxrwasicud11:\n<msg><emoji fromusername = "wxid_ikxxrwasicud11" tousername = "27852221909@chatroom" type="2" idbuffer="media:0_0" md5="135281c146a6626673acd2f91fe4197f" len = "73673" productid="" androidmd5="135281c146a6626673acd2f91fe4197f" androidlen="73673" s60v3md5 = "135281c146a6626673acd2f91fe4197f" s60v3len="73673" s60v5md5 = "135281c146a6626673acd2f91fe4197f" s60v5len="73673" cdnurl = "http://vweixinf.tc.qq.com/110/20401/stodownload?m=135281c146a6626673acd2f91fe4197f&amp;filekey=30440201010430302e02016e04025348042031333532383163313436613636323636373361636432663931666534313937660203011fc9040d00000004627466730000000132&amp;hy=SH&amp;storeid=265a5f06e000277d9cf1983430000006e01004fb153481c6401b156774b8aa&amp;ef=1&amp;bizid=1022" designerid = "" thumburl = "" encrypturl = "http://vweixinf.tc.qq.com/110/20402/stodownload?m=c8f001b642f96c837ba6c9b64733ab9e&amp;filekey=30440201010430302e02016e04025348042063386630303162363432663936633833376261366339623634373333616239650203011fd0040d00000004627466730000000132&amp;hy=SH&amp;storeid=265a5f06e000338d7cf1983430000006e02004fb253481c6401b156774b8bd&amp;ef=2&amp;bizid=1022" aeskey= "ea2b26f2be434901b6c9aff94d1d4f98" externurl = "http://vweixinf.tc.qq.com/110/20403/stodownload?m=086e278f04ec986d9ac936769abf03fb&amp;filekey=3043020101042f302d02016e040253480420303836653237386630346563393836643961633933363736396162663033666202020da0040d00000004627466730000000132&amp;hy=SH&amp;storeid=265a5f06e0003c756cf1983430000006e03004fb353481c6401b156774b8c4&amp;ef=3&amp;bizid=1022" externmd5 = "2e4f9b062eb4834d5cfd1447a31ad287" width= "240" height= "240" tpurl= "" tpauthkey= "" attachedtext= "" attachedtextcolor= "" lensid= "" emojiattr= "" linkid= "" desc= "" ></emoji>  </msg>'}, 'Status': 3, 'ImgStatus': 2, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1753183018, 'MsgSource': '<sec_msg_node>\n\t<alnode>\n\t\t<fr>2</fr>\n\t</alnode>\n</sec_msg_node>\n<msgsource>\n\t<silence>1</silence>\n\t<membercount>146</membercount>\n\t<signature>N0_V1_fk1Dff2H|v1_D3NrbOuo</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'NewMsgId': 3662690184121258794, 'MsgSeq': 871392776}
2025-07-22 19:16:45 | INFO | 收到表情消息: 消息ID:1843221295 来自:27852221909@chatroom 发送人:wxid_ikxxrwasicud11 MD5:135281c146a6626673acd2f91fe4197f 大小:73673
2025-07-22 19:16:45 | DEBUG | [DoubaoImageRecognition] 缓存表情消息: 3662690184121258794
