Metadata-Version: 2.1
Name: captcha
Version: 0.6.0
Summary: A captcha library that generates audio and image CAPTCHAs.
Author-email: <PERSON><PERSON><PERSON><PERSON> <<EMAIL>>
License: BSD-3-Clause
Project-URL: Documentation, https://captcha.lepture.com/
Project-URL: Source, https://github.com/lepture/captcha
Classifier: Development Status :: 4 - Beta
Classifier: Environment :: Console
Classifier: Environment :: Web Environment
Classifier: Intended Audience :: Developers
Classifier: License :: OSI Approved :: BSD License
Classifier: Operating System :: OS Independent
Classifier: Programming Language :: Python
Classifier: Programming Language :: Python :: 3
Classifier: Programming Language :: Python :: 3.8
Classifier: Programming Language :: Python :: 3.9
Classifier: Programming Language :: Python :: 3.10
Classifier: Programming Language :: Python :: 3.11
Classifier: Programming Language :: Python :: Implementation :: CPython
Classifier: Topic :: Security
Requires-Python: >=3.8
Description-Content-Type: text/x-rst
License-File: LICENSE
Requires-Dist: Pillow

Captcha
=======

A captcha library that generates audio and image CAPTCHAs.

Features
--------

1. Audio CAPTCHAs
2. Image CAPTCHAs


Installation
------------

Install captcha with pip::

    $ pip install captcha

Usage
-----

Audio and Image CAPTCHAs are in separated modules:

.. code-block:: python

    from captcha.audio import AudioCaptcha
    from captcha.image import ImageCaptcha

    audio = AudioCaptcha(voicedir='/path/to/voices')
    image = ImageCaptcha(fonts=['/path/A.ttf', '/path/B.ttf'])

    data = audio.generate('1234')
    audio.write('1234', 'out.wav')

    data = image.generate('1234')
    image.write('1234', 'out.png')

This is the APIs for your daily works. We do have built-in voice data and font
data. But it is suggested that you use your own voice and font data.

Useful Links
------------

1. GitHub: https://github.com/lepture/captcha
2. Docs: https://captcha.lepture.com/


License
-------

Licensed under BSD. Please see LICENSE for licensing details.
