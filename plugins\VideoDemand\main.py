import json, re, tomllib, asyncio, os, time, base64
from pathlib import Path
import httpx, cv2
from loguru import logger
from io import BytesIO
from PIL import Image
from WechatAPI import WechatAPIClient
from utils.decorators import *
from utils.plugin_base import PluginBase
class VideoDemand(PluginBase):
    """视频点播插件"""
    
    description = "视频点播插件 - 支持多类型视频点播"
    author = "XYBot"
    version = "1.0.0"

    def __init__(self):
        super().__init__()
        
        # 初始化临时目录
        self.plugin_dir = Path(os.path.dirname(os.path.abspath(__file__)))
        self.temp_dir = self.plugin_dir / "temp"
        try:
            self.temp_dir.mkdir(parents=True, exist_ok=True)
        except Exception:
            import tempfile
            self.temp_dir = Path(tempfile.gettempdir()) / "VideoDemand"
            self.temp_dir.mkdir(parents=True, exist_ok=True)
        
        # 读取配置
        try:
            config_path = os.path.join(os.path.dirname(__file__), "config.toml")
            with open(config_path, "rb") as f:
                plugin_config = tomllib.load(f)
            config = plugin_config["VideoDemand"]
        except Exception as e:
            logger.error(f"[VideoDemand] 配置文件读取失败: {e}")
            raise
        
        # 基本配置
        self.enable = config.get("enable", True)
        self.command = config.get("command", ["视频菜单"])
        self.random_command = config.get("random-command", ["随机视频"])  # 添加随机视频命令
        self.random_video_url = config.get("random-video-url", "http://tucdn.wpon.cn/api-girl/index.php?wpon=json")  # 随机视频URL
        self.menu_image = config.get("menu-image", "https://d.kstore.dev/download/8150/shipin.jpg")
        self.cache_time = config.get("cache-time", 300)  # 菜单有效期5分钟
        
        # 房间状态
        self.room_status = {}
        
        # 并发控制
        self.video_semaphore = asyncio.Semaphore(3)

        # 从配置文件读取API配置，确保键为整数
        api_config = config.get("api_mapping", {})
        self.api_mapping = {int(k): v for k, v in api_config.items()}
        logger.info(f"[VideoDemand] 加载了 {len(self.api_mapping)} 个视频类别配置")
        



    async def _get_redirect_url(self, url: str):
        """获取重定向URL"""
        headers = {
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
            'Accept': '*/*',
            'Accept-Encoding': 'gzip, deflate',
            'Connection': 'keep-alive'
        }
        
        try:
            async with httpx.AsyncClient(timeout=30.0, verify=False, follow_redirects=False) as client:
                response = await client.get(url, headers=headers)
                if response.status_code in (301, 302):
                    return response.headers.get('Location')
        except Exception:
            return None
            
        return None
        

        
    async def _get_random_video(self) -> dict:
        """获取随机视频

        Returns:
            dict: 包含视频信息的字典，成功时包含URL，失败时包含错误信息
        """
        max_retries = 3
        retry_count = 0

        headers = {
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
            'Accept': 'application/json, text/plain, */*',
            'Accept-Encoding': 'gzip, deflate',
            'Accept-Language': 'zh-CN,zh;q=0.9,en-US;q=0.8,en;q=0.7',
            'Connection': 'keep-alive'
        }

        while retry_count < max_retries:
            try:
                # 获取API URL
                api_url = self.random_video_url

                # 发送请求获取JSON响应
                async with httpx.AsyncClient(timeout=30.0, verify=False) as client:
                    response = await client.get(api_url, headers=headers)
                    response.raise_for_status()

                    # 解析JSON响应
                    try:
                        json_data = response.json()


                        # 检查响应格式
                        if json_data.get("error") == 0 and json_data.get("result") == 200:
                            mp4_path = json_data.get("mp4", "")
                            if mp4_path:
                                # 构建完整的视频URL
                                if mp4_path.startswith("//"):
                                    video_url = f"https:{mp4_path}"
                                elif mp4_path.startswith("/"):
                                    video_url = f"https://tucdn.wpon.cn{mp4_path}"
                                else:
                                    video_url = mp4_path

                                return {"success": True, "url": video_url}
                            else:
                                retry_count += 1
                        else:
                            retry_count += 1

                    except json.JSONDecodeError:
                        retry_count += 1

            except (httpx.HTTPError, asyncio.TimeoutError):
                retry_count += 1
                await asyncio.sleep(1)

            except Exception:
                retry_count += 1
                await asyncio.sleep(1)

        return {"success": False, "message": "获取随机视频失败，请稍后重试"}

    async def _get_video(self, category_name: str):
        """获取视频URL"""
        category_id = next((k for k, v in self.api_mapping.items() if v['name'] == category_name), None)
        if category_id is None:
            return {"success": False, "message": f"未找到匹配的视频类别: {category_name}"}
            
        urls = self.api_mapping[category_id]['urls'].copy()
        max_error_count = 3
        
        headers = {
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
            'Accept': '*/*',
            'Accept-Encoding': 'gzip, deflate',
            'Connection': 'keep-alive'
        }
        
        while urls:
            url = urls[0]
            error_count = 0

            
            while error_count < max_error_count:
                try:
                    async with httpx.AsyncClient(timeout=30.0, verify=False, follow_redirects=True) as client:
                        response = await client.get(url, headers=headers)
                        response.raise_for_status()
                        final_url = str(response.url)
                        return {"success": True, "url": final_url}
                            
                except (httpx.HTTPError, asyncio.TimeoutError):
                    error_count += 1
                    if error_count >= max_error_count:
                        urls.pop(0)
                        break
                    await asyncio.sleep(2)

                except Exception:
                    error_count += 1
                    if error_count >= max_error_count:
                        urls.pop(0)
                        break
                    await asyncio.sleep(2)
                    
        return {"success": False, "message": f"获取视频失败: 所有URL均无法获取视频，类别: {category_name}"}

    async def _download_video(self, url: str, category: str):
        """下载视频到本地
        Returns:
            str: 本地视频文件路径,下载失败返回None
        """
        try:
            # 生成唯一文件名
            timestamp = int(time.time())
            filename = f"{category}_{timestamp}.mp4"
            filepath = self.temp_dir / filename
            
            headers = {
                'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
                'Accept': '*/*',
                'Accept-Encoding': 'gzip, deflate',
                'Connection': 'keep-alive'
            }
            
            # 下载视频
            
            async with httpx.AsyncClient(timeout=300.0, verify=False) as client:  # 5分钟超时
                response = await client.get(url, headers=headers)
                
                if response.status_code != 200:
                    return None
                    

                
                # 写入文件
                with open(filepath, 'wb') as f:
                    f.write(response.content)
                
            # 尝试修复视频元数据，确保时长信息正确
            try:
                await self._fix_video_metadata(filepath)
            except Exception as e:
                logger.warning(f"修复视频元数据失败，但继续使用原视频", exception=e)
                    
            return str(filepath)
            
        except Exception as e:
            logger.error(f"下载视频失败: {url}, 类别: {category}", exception=e)
            return None

    async def _fix_video_metadata(self, video_path: str) -> None:
        """修复视频元数据，确保时长信息正确
        
        Args:
            video_path: 视频文件路径
        """
        try:
            # 创建临时文件路径
            temp_path = f"{video_path}.temp.mp4"
            
            # 检查文件是否存在
            if not os.path.exists(video_path) or os.path.getsize(video_path) == 0:
                logger.warning(f"要修复的视频文件不存在或为空: {video_path}")
                return
                
            # 验证原始文件是否可以正常读取
            verify_orig_cmd = f'ffprobe -v error "{video_path}"'
            verify_orig_process = await asyncio.create_subprocess_shell(
                verify_orig_cmd,
                stdout=asyncio.subprocess.PIPE,
                stderr=asyncio.subprocess.PIPE
            )
            await verify_orig_process.communicate()
            
            if verify_orig_process.returncode != 0:
                return  # 如果原始文件有问题，不进行修复

            # 1. 首先尝试最快的方式：直接复制流并优化元数据位置
            simple_cmd = f'ffmpeg -i "{video_path}" -c copy -movflags +faststart -y "{temp_path}"'
            
            simple_process = await asyncio.create_subprocess_shell(
                simple_cmd,
                stdout=asyncio.subprocess.PIPE,
                stderr=asyncio.subprocess.PIPE
            )
            
            await simple_process.communicate()
            
            # 检查临时文件是否生成
            if simple_process.returncode == 0 and os.path.exists(temp_path) and os.path.getsize(temp_path) > 0:
                # 验证新文件是否可以正常打开
                verify_cmd = f'ffprobe -v error -select_streams v:0 -show_entries stream=duration -of default=noprint_wrappers=1:nokey=1 "{temp_path}"'

                verify_process = await asyncio.create_subprocess_shell(
                    verify_cmd,
                    stdout=asyncio.subprocess.PIPE,
                    stderr=asyncio.subprocess.PIPE
                )
                await verify_process.communicate()

                if verify_process.returncode == 0:
                    # 替换原文件前确保有足够的磁盘空间
                    try:
                        # 确保原文件存在且临时文件有内容
                        if os.path.exists(video_path) and os.path.exists(temp_path) and os.path.getsize(temp_path) > 0:
                            os.remove(video_path)
                            os.rename(temp_path, video_path)
                            return
                    except Exception:
                        pass
                
                # 清理可能存在的临时文件
                if os.path.exists(temp_path):
                    try:
                        os.remove(temp_path)
                    except:
                        pass
            
            # 2. 如果简单方法失败，尝试获取视频信息并进行最小必要的处理
            probe_cmd = f'ffprobe -v quiet -print_format json -show_format -show_streams "{video_path}"'

            probe_process = await asyncio.create_subprocess_shell(
                probe_cmd,
                stdout=asyncio.subprocess.PIPE,
                stderr=asyncio.subprocess.PIPE
            )
            probe_stdout, _ = await probe_process.communicate()

            if probe_process.returncode == 0:
                try:
                    probe_data = json.loads(probe_stdout.decode())
                    # 获取视频时长（秒）
                    if 'format' in probe_data and 'duration' in probe_data['format']:
                        duration = float(probe_data['format']['duration'])

                        # 使用最小必要的处理参数
                        cmd = f'ffmpeg -i "{video_path}" -c copy -movflags +faststart -metadata duration="{duration}" -y "{temp_path}"'

                        process = await asyncio.create_subprocess_shell(
                            cmd,
                            stdout=asyncio.subprocess.PIPE,
                            stderr=asyncio.subprocess.PIPE
                        )

                        await process.communicate()

                        if process.returncode == 0 and os.path.exists(temp_path) and os.path.getsize(temp_path) > 0:
                            try:
                                os.remove(video_path)
                                os.rename(temp_path, video_path)
                            except Exception:
                                if os.path.exists(temp_path):
                                    try: os.remove(temp_path)
                                    except: pass
                        else:
                            if os.path.exists(temp_path):
                                try: os.remove(temp_path)
                                except: pass

                except (json.JSONDecodeError, KeyError, ValueError):
                    if os.path.exists(temp_path):
                        try: os.remove(temp_path)
                        except: pass

            else:
                if os.path.exists(temp_path):
                    try: os.remove(temp_path)
                    except: pass
                
        except Exception as e:
            logger.error(f"修复视频元数据时出错: {e}", exc_info=True)
            # 确保临时文件被删除
            if 'temp_path' in locals() and os.path.exists(temp_path):
                try:
                    os.remove(temp_path)
                except Exception as cleanup_error:
                    logger.warning(f"清理临时文件失败: {cleanup_error}")

    async def _encode_video(self, video_path: str):
        """将视频编码为base64
        Returns:
            str: base64编码后的视频数据,失败返回None
        """
        try:
            # 获取文件大小
            file_size = os.path.getsize(video_path)
            
            # 如果文件大于50MB，尝试压缩
            if file_size > 50 * 1024 * 1024:
                logger.warning(f"视频文件过大 ({file_size / (1024*1024):.2f}MB)，尝试压缩")
                compressed_path = f"{video_path}.compressed.mp4"
                
                # 使用ffmpeg压缩视频
                try:
                    compress_cmd = f'ffmpeg -i "{video_path}" -c:v libx264 -crf 28 -preset faster -c:a aac -b:a 128k "{compressed_path}" -y'

                    process = await asyncio.create_subprocess_shell(
                        compress_cmd,
                        stdout=asyncio.subprocess.PIPE,
                        stderr=asyncio.subprocess.PIPE
                    )

                    await process.communicate()

                    if process.returncode == 0 and os.path.exists(compressed_path):
                        compressed_size = os.path.getsize(compressed_path)

                        # 检查如果压缩后比原始更大，则使用原始文件
                        if compressed_size < file_size:
                            video_path = compressed_path
                            file_size = compressed_size
                        else:
                            os.remove(compressed_path)
                except Exception:
                    if os.path.exists(compressed_path):
                        try: os.remove(compressed_path)
                        except: pass

            # 读取文件并编码
            with open(video_path, 'rb') as f:
                video_data = f.read()

            base64_data = base64.b64encode(video_data).decode('utf-8')
            
            return base64_data
            
        except MemoryError:
            logger.error(f"视频编码内存不足: {video_path}, 大小: {os.path.getsize(video_path) / (1024*1024):.2f}MB")
            return None
        except Exception:
            logger.error(f"视频编码失败: {video_path}", exc_info=True)
            return None

    def _extract_first_frame(self, video_path: str):
        """从视频中提取第一帧并转换为base64
        Returns:
            str: base64编码的图片数据,失败返回None
        """
        try:
            # 使用ffmpeg提取第一帧
            try:
                temp_img_path = f"{video_path}.thumb.jpg"
                extract_cmd = f'ffmpeg -i "{video_path}" -vframes 1 -q:v 2 -y "{temp_img_path}"'
                os.system(extract_cmd)

                if os.path.exists(temp_img_path) and os.path.getsize(temp_img_path) > 0:
                    # 读取并编码图片
                    with open(temp_img_path, 'rb') as f:
                        image_data = f.read()

                    # 删除临时文件
                    try: os.remove(temp_img_path)
                    except: pass

                    # 返回base64编码
                    return base64.b64encode(image_data).decode('utf-8')
            except Exception:
                pass

            # 如果ffmpeg方法失败，尝试使用OpenCV
            
            # 打开视频文件
            cap = cv2.VideoCapture(video_path)
            if not cap.isOpened():
                return None

            # 读取第一帧
            ret, frame = cap.read()
            if not ret:
                cap.release()
                return None
                
            # 转换为PIL Image
            frame_rgb = cv2.cvtColor(frame, cv2.COLOR_BGR2RGB)
            image = Image.fromarray(frame_rgb)
            
            # 调整图片大小
            max_size = (800, 800)
            image.thumbnail(max_size, Image.Resampling.LANCZOS)

            # 转换为base64
            buffer = BytesIO()
            image.save(buffer, format="JPEG", quality=95)
            image_data = buffer.getvalue()

            image_base64 = base64.b64encode(image_data).decode('utf-8')

            # 释放资源
            cap.release()
            
            return image_base64
            
        except Exception:
            return None

    async def _download_menu_image(self):
        """加载菜单图片,返回图片二进制数据"""
        try:
            # 从本地文件加载图片
            local_image_path = "C:\\XYBotV2\\data\\default picture\\shipincaidan.jpg"
            if os.path.exists(local_image_path):
                with open(local_image_path, 'rb') as f:
                    return f.read()
            else:
                logger.error(f"本地菜单图片不存在: {local_image_path}")
                # 如果本地文件不存在，尝试从网络下载
                async with httpx.AsyncClient(timeout=30.0) as client:
                    response = await client.get(self.menu_image)
                    if response.status_code == 200:
                        return response.content
                    else:
                        return None
        except Exception as e:
            logger.error(f"加载菜单图片失败: {e}")
            return None

    @on_text_message
    async def handle_menu(self, bot: WechatAPIClient, message: dict):
        """处理菜单命令"""
        content = message.get("Content", "").strip()
        if content not in self.command:
            return
            
        wxid = message.get("FromWxid")
        roomid = message.get("FromGroup", wxid)
        
        # 检查菜单状态
        room_status = self.room_status.get(roomid, {})
        if room_status and time.time() - room_status.get("time", 0) < 5:  # 5秒内不重复发送
            await bot.send_text_message(roomid, "菜单已发送,请勿重复请求")
            return
            
        # 发送菜单图片
        try:
            # 下载菜单图片
            image_data = await self._download_menu_image()
            if not image_data:
                await bot.send_text_message(roomid, "获取菜单失败,等会再试试")
                return
                
            # 直接发送二进制数据
            await bot.send_image_message(roomid, image_data)
            
            self.room_status[roomid] = {
                "time": time.time(),
                "expire": time.time() + self.cache_time,
                "notified": False
            }
                
        except Exception as e:
            logger.error(f"发送菜单失败: {e}")
            await bot.send_text_message(roomid, "发送菜单失败,等会再试试")

    @on_text_message
    async def handle_video(self, bot: WechatAPIClient, message: dict):
        """处理视频请求"""
        content = message.get("Content", "").strip()
        
        # 严格匹配"看+数字"的格式
        if not re.match(r'^看\d+$', content):
            return
            
        wxid = message.get("FromWxid")
        roomid = message.get("FromGroup", wxid)
        
        # 检查该房间是否发送过菜单且菜单在有效期内
        room_status = self.room_status.get(roomid)
        if not room_status:
            # 如果没有发送过菜单,直接忽略命令
            return
            
        if time.time() > room_status["expire"]:
            # 如果菜单已过期,提示重新获取菜单(只提示一次)
            if not room_status["notified"]:
                await bot.send_text_message(roomid, "菜单已过期,请重新发送「视频菜单」")
                room_status["notified"] = True
            return
        
        # 检查当前并发数    
        if self.video_semaphore.locked():
            await bot.send_text_message(roomid, "正在处理其他视频，等会再试...")
            return
            
        video_path = None
        try:
            # 使用信号量控制并发
            async with self.video_semaphore:

                    
                # 解析序号
                sequence = int(content[1:])  # 去掉"看"后转为数字
                logger.debug(f"[VideoDemand] 请求序号: {sequence}, API映射中是否存在: {sequence in self.api_mapping}")
                if sequence < 1 or sequence > 128 or sequence not in self.api_mapping:
                    await bot.send_text_message(roomid, "无效的序号,请输入正确的序号(1-128)")
                    return

                category = self.api_mapping[sequence]["name"]
                
                await bot.send_text_message(
                    roomid, 
                    f"正在获取{category}视频,请稍等..."
                )
                
                # 获取视频URL
                result = await self._get_video(category)
                if not result["success"]:
                    await bot.send_text_message(roomid, result["message"])
                    return
                
                video_url = result["url"]

                    
                # 下载视频到本地
                video_path = await self._download_video(video_url, category)
                if not video_path:
                    await bot.send_text_message(roomid, "下载视频失败,请稍后重试")
                    return
                

                
                # 尝试获取视频时长信息
                video_duration = None
                try:
                    probe_cmd = f'ffprobe -v error -show_entries format=duration -of default=noprint_wrappers=1:nokey=1 "{video_path}"'

                    
                    probe_process = await asyncio.create_subprocess_shell(
                        probe_cmd,
                        stdout=asyncio.subprocess.PIPE,
                        stderr=asyncio.subprocess.PIPE
                    )
                    probe_stdout, _ = await probe_process.communicate()
                    
                    if probe_process.returncode == 0:
                        stdout_content = probe_stdout.decode().strip()
                        
                        duration = float(stdout_content)
                        # 修改: 尝试以秒为单位传递时长，而不是毫秒
                        video_duration = int(duration)

                        
                except Exception as e:
                    logger.warning(f"获取视频时长失败: {e}", exc_info=True)
                
                # 编码视频数据
                try:
                    video_base64 = await self._encode_video(video_path)
                    if not video_base64:
                        await bot.send_text_message(roomid, "处理视频失败,请稍后重试")
                        return

                except Exception as e:
                    logger.error(f"视频编码失败", exc_info=True)
                    await bot.send_text_message(roomid, "处理视频失败,请稍后重试")
                    return
                
                # 提取视频首帧作为封面
                try:
                    cover_base64 = self._extract_first_frame(video_path)
                    if not cover_base64:

                        # 使用空封面
                        cover_base64 = None
                except Exception as e:
                    logger.warning(f"提取视频首帧失败，将使用空封面", exc_info=True)
                    cover_base64 = None
                
                # 发送视频
                try:
                    # 添加重试机制
                    max_send_retries = 2
                    current_retry = 0
                    send_success = False
                    
                    # 直接手动设置时长（秒），而不依赖API进行计算
                    video_duration = video_duration or 20
                    
                    while current_retry <= max_send_retries and not send_success:
                        try:
                            if current_retry > 0:
                                await asyncio.sleep(2)
                            
                            # 发送视频，添加时长参数
                            try:
                                # 首先尝试创建完整的请求
                                json_param = {
                                    "Wxid": bot.wxid, 
                                    "ToWxid": roomid, 
                                    "Base64": video_base64, 
                                    "ImageBase64": cover_base64, 
                                    "PlayLength": video_duration
                                }
                                
                                # 使用httpx直接发送请求，避免可能的API问题
                                async with httpx.AsyncClient(timeout=300.0) as client:  # 设置足够长的超时
                                    response = await client.post(
                                        f'http://{bot.ip}:{bot.port}/SendVideoMsg', 
                                        json=json_param,
                                        timeout=300.0
                                    )
                                    json_resp = response.json()
                                    
                                    if json_resp.get("Success"):
                                        data = json_resp.get("Data")
                                        client_msg_id = data.get("clientMsgId")
                                        new_msg_id = data.get("newMsgId")


                                        # 通知撤回插件存储消息信息
                                        try:
                                            from plugins.RevokePlugin.main import RevokePlugin
                                            import time as time_module
                                            create_time = int(time_module.time())
                                            RevokePlugin.notify_message_sent(roomid, client_msg_id, create_time, new_msg_id)
                                        except Exception:
                                            pass
                                    else:
                                        error_msg = json_resp.get("Message", "未知错误")
                                        logger.warning(f"API返回错误: {error_msg}")
                                        raise Exception(f"API返回错误: {error_msg}")
                            except Exception:

                                # 如果直接请求失败，尝试使用API
                                client_msg_id, new_msg_id = await bot.send_video_message(
                                    roomid,
                                    video_base64,
                                    cover_base64,  # 使用视频首帧作为封面
                                    video_duration  # 传递处理后的视频时长
                                )
                            

                            
                            # 检查发送结果
                            if new_msg_id != 0 and client_msg_id:
                                send_success = True
                                # 简单清理文件
                                try: os.remove(video_path)
                                except: pass
                            else:
                                logger.warning(f"发送视频返回值异常 - client_msg_id: {client_msg_id}, new_msg_id: {new_msg_id}")
                                # 尝试下一次重试
                                current_retry += 1
                            
                        except Exception as retry_e:
                            logger.warning(f"发送视频异常，准备重试: {str(retry_e)}", exc_info=True)
                            current_retry += 1
                    
                    # 检查最终发送结果
                    if not send_success:
                        logger.error(f"发送视频失败，已重试{max_send_retries}次")
                        await bot.send_text_message(roomid, "发送视频失败,请稍后重试")
                        # 发送失败的文件由定时清理任务处理
                        
                except Exception as e:
                    logger.error(f"发送视频失败: {str(e)}", exc_info=True)
                    await bot.send_text_message(roomid, "发送视频失败,请稍后重试")
                
        except ValueError as e:
            logger.error(f"处理视频请求出现值错误", exc_info=True)
            await bot.send_text_message(roomid, "请输入正确的序号(1-128)")
        except Exception as e:
            logger.error(f"处理视频请求失败: {content}, 错误: {str(e)}", exc_info=True)
            await bot.send_text_message(roomid, "处理请求失败,请稍后重试")
            # 发生异常时不立即删除文件,由定时清理任务处理

    @on_text_message
    async def handle_random_video(self, bot: WechatAPIClient, message: dict):
        """处理随机视频命令"""
        content = message.get("Content", "").strip()
        
        # 检查命令是否匹配
        if content not in self.random_command:
            return
            
        wxid = message.get("FromWxid")
        roomid = message.get("FromGroup", wxid)
        
        # 检查并发数
        if self.video_semaphore.locked():
            await bot.send_text_message(roomid, "正在处理其他视频，等会再试...")
            return
            
        video_path = None
        try:
            # 使用信号量控制并发
            async with self.video_semaphore:

                    
                # 发送提示消息
                await bot.send_text_message(roomid, "正在获取随机视频，请稍等...")
                
                # 获取随机视频URL
                result = await self._get_random_video()
                if not result["success"]:
                    await bot.send_text_message(roomid, result["message"])
                    return
                
                video_url = result["url"]
                
                # 下载视频到本地
                video_path = await self._download_video(video_url, "随机视频")
                if not video_path:
                    await bot.send_text_message(roomid, "下载视频失败，请稍后重试")
                    return
                
                # 编码视频数据
                video_base64 = await self._encode_video(video_path)
                if not video_base64:
                    await bot.send_text_message(roomid, "处理视频失败，请稍后重试")
                    return
                
                # 提取视频首帧作为封面
                cover_base64 = self._extract_first_frame(video_path)
                if cover_base64:
                    pass

                # 尝试获取视频时长信息
                video_duration = None
                try:
                    probe_cmd = f'ffprobe -v error -show_entries format=duration -of default=noprint_wrappers=1:nokey=1 "{video_path}"'
                    probe_process = await asyncio.create_subprocess_shell(
                        probe_cmd,
                        stdout=asyncio.subprocess.PIPE,
                        stderr=asyncio.subprocess.PIPE
                    )
                    probe_stdout, _ = await probe_process.communicate()
                    if probe_process.returncode == 0:
                        duration = float(probe_stdout.decode().strip())
                        # 修改: 尝试以秒为单位传递时长，而不是毫秒
                        video_duration = int(duration)
                except Exception as e:
                    logger.warning(f"获取随机视频时长失败: {e}")

                # 发送视频 - 使用与普通视频点播相同的逻辑
                try:
                    # 添加重试机制
                    max_send_retries = 2
                    current_retry = 0
                    send_success = False

                    # 直接手动设置时长（秒），而不依赖API进行计算
                    if video_duration is None:
                        # 如果获取不到，设置一个默认值（大部分短视频在15-30秒之间）

                        video_duration = 20

                    while current_retry <= max_send_retries and not send_success:
                        try:
                            if current_retry > 0:
                                logger.debug(f"尝试重新发送随机视频，第{current_retry}次重试")
                                # 重试前等待一段时间
                                await asyncio.sleep(2)

                            send_start_time = time.time()
                            logger.debug(f"开始发送随机视频 - 时长: {video_duration}秒, 封面大小: {len(cover_base64) if cover_base64 else 0}字节")

                            # 发送视频，添加时长参数
                            try:
                                # 首先尝试创建完整的请求
                                json_param = {
                                    "Wxid": bot.wxid,
                                    "ToWxid": roomid,
                                    "Base64": video_base64,
                                    "ImageBase64": cover_base64,
                                    "PlayLength": video_duration
                                }

                                # 使用httpx直接发送请求，避免可能的API问题
                                logger.debug("使用httpx发送随机视频请求")
                                async with httpx.AsyncClient(timeout=300.0) as client:  # 设置足够长的超时
                                    response = await client.post(
                                        f'http://{bot.ip}:{bot.port}/SendVideoMsg',
                                        json=json_param,
                                        timeout=300.0
                                    )
                                    json_resp = response.json()

                                    if json_resp.get("Success"):
                                        data = json_resp.get("Data")
                                        client_msg_id = data.get("clientMsgId")
                                        new_msg_id = data.get("newMsgId")
                                        logger.debug(f"随机视频发送成功，响应: clientMsgId={client_msg_id}, newMsgId={new_msg_id}")

                                        # 通知撤回插件存储消息信息
                                        try:
                                            from plugins.RevokePlugin.main import RevokePlugin
                                            import time as time_module
                                            create_time = int(time_module.time())
                                            RevokePlugin.notify_message_sent(roomid, client_msg_id, create_time, new_msg_id)
                                        except Exception as e:
                                            logger.debug(f"通知撤回插件失败: {e}")
                                    else:
                                        error_msg = json_resp.get("Message", "未知错误")
                                        logger.warning(f"API返回错误: {error_msg}")
                                        raise Exception(f"API返回错误: {error_msg}")
                            except Exception as direct_e:
                                logger.debug(f"直接HTTP请求失败，尝试使用API: {direct_e}")
                                # 如果直接请求失败，尝试使用API（不传递时长参数）
                                client_msg_id, new_msg_id = await bot.send_video_message(
                                    roomid,
                                    video_base64,
                                    cover_base64  # 使用视频首帧作为封面
                                )

                            send_duration = time.time() - send_start_time
                            logger.debug(f"发送随机视频耗时: {send_duration:.2f}秒, client_msg_id: {client_msg_id}, new_msg_id: {new_msg_id}")

                            # 检查发送结果
                            if new_msg_id != 0 and client_msg_id:
                                send_success = True
                                # 简单清理文件
                                try: os.remove(video_path)
                                except: pass
                            else:
                                logger.warning(f"发送随机视频返回值异常 - client_msg_id: {client_msg_id}, new_msg_id: {new_msg_id}")
                                # 尝试下一次重试
                                current_retry += 1

                        except Exception as retry_e:
                            logger.warning(f"发送随机视频异常，准备重试: {str(retry_e)}", exc_info=True)
                            current_retry += 1

                    # 检查最终发送结果
                    if not send_success:
                        logger.error(f"发送随机视频失败，已重试{max_send_retries}次")
                        await bot.send_text_message(roomid, "发送视频失败,请稍后重试")
                        # 发送失败的文件由定时清理任务处理

                except Exception as e:
                    logger.error(f"发送随机视频失败: {str(e)}", exc_info=True)
                    await bot.send_text_message(roomid, "发送视频失败,请稍后重试")
                    
        except Exception as e:
            logger.error(f"处理随机视频请求失败: {str(e)}", exc_info=True)
            await bot.send_text_message(roomid, "处理请求失败，请稍后重试")
            # 发生异常时不立即删除文件，由定时清理任务处理

