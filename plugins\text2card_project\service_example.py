import os
import sys
import logging
import asyncio

# 添加当前目录到Python路径
current_dir = os.path.dirname(os.path.abspath(__file__))
if current_dir not in sys.path:
    sys.path.append(current_dir)

from text_card_service import TextCardService

# 配置日志
logging.basicConfig(level=logging.INFO)

async def main():
    # 创建服务实例（可以指定缓存目录）
    cache_dir = os.path.join(current_dir, "cache")
    text_card = TextCardService(cache_dir=cache_dir)
    
    # 示例1：基本使用
    text = """**这是一个标题**

这是正文内容，支持 *斜体* 和 **粗体**。

- 支持列表
- 支持emoji 🎉
- 支持多级标题

## 二级标题
正文内容...

### 三级标题
更多内容..."""

    # 生成并保存卡片
    try:
        # 示例1：基本卡片
        output_path = "example_card.png"
        await text_card.generate_card(
            text=text,
            output_path=output_path,
            width=720,
            is_dark=False
        )
        print(f"示例1：基本卡片已保存到 {output_path}")
        
        # 示例2：简单模式
        simple_text = "这是一个简单的文本消息 🌟"
        simple_output = "simple_card.png"
        await text_card.generate_simple_card(
            text=simple_text,
            output_path=simple_output,
            theme='light'
        )
        print(f"示例2：简单卡片已保存到 {simple_output}")
        
        # 示例3：暗色主题
        dark_output = "dark_card.png"
        await text_card.generate_simple_card(
            text=text,
            output_path=dark_output,
            theme='dark'
        )
        print(f"示例3：暗色主题卡片已保存到 {dark_output}")
        
        # 示例4：自定义样式
        style = {
            'width': 1000,
            'is_dark': False
        }
        custom_output = "custom_card.png"
        await text_card.generate_card(
            text=text,
            output_path=custom_output,
            **style
        )
        print(f"示例4：自定义样式卡片已保存到 {custom_output}")
        
        # 示例5：带头部图片（增强效果）
        header_image_url = "http://www.yx520.ltd/API/iptz/image.php"
        header_output = "header_card.png"
        await text_card.generate_card_with_header(
            text=text,
            header_image_url=header_image_url,
            output_path=header_output,
            width=720,
            is_dark=False,
            enhance_header=True
        )
        print(f"示例5：带头部图片的卡片已保存到 {header_output}")
        
        # 示例6：带头部图片（原始效果）
        header_simple_output = "header_simple_card.png"
        await text_card.generate_card_with_header(
            text=simple_text,
            header_image_url=header_image_url,
            output_path=header_simple_output,
            enhance_header=False,
            width=720,
            is_dark=True
        )
        print(f"示例6：简单头部图片卡片已保存到 {header_simple_output}")
        
    except Exception as e:
        print(f"生成卡片时出错: {e}")

async def demo_inner_styles():
    """演示各种内边框样式效果"""
    service = TextCardService()
    sample_text = "# 内边框样式演示\n\n这是一个用于展示不同内边框样式效果的示例文本。\n\n内边框样式仅在双层边框(double)样式下生效，可以设置为点状(dotted)、虚线(dashed)、波浪(wavy)或实线(solid)样式。"
    
    # 演示不同的内边框样式
    inner_styles = ['solid', 'dotted', 'dashed', 'wavy']
    
    for style in inner_styles:
        output_path = f"test_output/inner_style_{style}.png"
        await service.generate_fancy_border_card(
            sample_text,
            output_path=output_path,
            border_style='double',  # 设置双层边框
            theme_color='#4DC4FF',  # 蓝色主题
            inner_style=style  # 设置内边框样式
        )
        print(f"生成了{style}内边框样式演示: {output_path}")

if __name__ == "__main__":
    asyncio.run(main())