import json
import tomllib
import asyncio
import httpx
from pathlib import Path
import xml.etree.ElementTree as ET
import traceback
import time
import uuid
from urllib.parse import urlencode

from loguru import logger
from WechatAPI import WechatAPIClient
from utils.decorators import on_text_message, on_quote_message, on_article_message
from utils.plugin_base import PluginBase


class ArticleForwarder(PluginBase):
    description = "公众号文章转发"
    author = "Assistant"
    version = "1.0.0"

    def __init__(self):
        super().__init__()
        self.data_dir = Path("data/article_forwarder")
        self.data_dir.mkdir(parents=True, exist_ok=True)
        self.accounts = {}  # 公众号ID -> {name: 名称, groups: 群列表} 的映射

        # 加载插件配置
        try:
            with open("plugins/ArticleForwarder/config.toml", "rb") as f:
                config = tomllib.load(f)
                self.plugin_config = config.get("ArticleForwarder", {})
                self.enable = self.plugin_config.get("enabled", True)
                self.commands = self.plugin_config.get("commands", ["添加监控", "删除监控", "查看配置", "播客"])

                # 播客功能配置
                podcast_config = self.plugin_config.get("podcast", {})
                self.podcast_enabled = podcast_config.get("enabled", True)
                self.podcast_cookies = podcast_config.get("cookies", "")
                self.podcast_initial_wait = podcast_config.get("initial_wait", 180)
                self.podcast_max_attempts = podcast_config.get("max_attempts", 60)
                self.podcast_poll_interval = podcast_config.get("poll_interval", 30)

        except Exception as e:
            logger.error(f"加载插件配置出错: {e}")
            self.enable = True
            self.commands = ["添加监控", "删除监控", "查看配置", "播客"]
            self.podcast_enabled = True
            self.podcast_cookies = ""
            self.podcast_initial_wait = 180
            self.podcast_max_attempts = 60
            self.podcast_poll_interval = 30

        # 加载管理员配置
        try:
            with open("main_config.toml", "rb") as f:
                config = tomllib.load(f)
                self.admins = config["XYBot"]["admins"]
                logger.info(f"已加载管理员列表: {self.admins}")
        except Exception as e:
            logger.error(f"加载管理员配置出错: {e}")
            self.admins = []

        # 初始化播客相关配置
        self.podcast_session = None
        self.podcast_headers = None
        self.podcast_device_params = None

        if self.podcast_enabled and self.podcast_cookies:
            try:
                self._init_podcast_api()
                logger.info("播客API初始化成功")
            except Exception as e:
                logger.error(f"播客API初始化失败: {e}")
                self.podcast_session = None

        # 加载转发配置
        self._load_config()
        logger.info(f"ArticleForwarder插件初始化完成 - 监控配置: {self.accounts}")

        # 异步加载配置任务
        asyncio.create_task(self._async_load_config())

    def _init_podcast_api(self):
        """初始化播客API配置"""
        # 解析Cookie
        cookies_dict = {}
        for cookie in self.podcast_cookies.split(';'):
            if '=' in cookie:
                key, value = cookie.strip().split('=', 1)
                cookies_dict[key] = value

        self.podcast_session = httpx.Client(
            timeout=30.0,
            cookies=cookies_dict
        )

        # 设置请求头
        self.podcast_headers = {
            "User-Agent": "Mozilla/5.0 (Linux; Android 10; V2002A Build/QP1A.190711.020) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/83.0.4103.106 Mobile Safari/537.36",
            "Accept": "application/json, text/plain, */*",
            "Content-Type": "application/json",
            "Origin": "https://www.doubao.com",
            "X-Requested-With": "mark.via",
            "Sec-Fetch-Site": "same-origin",
            "Sec-Fetch-Mode": "cors",
            "Sec-Fetch-Dest": "empty",
            "Accept-Encoding": "gzip, deflate",
            "Accept-Language": "zh-CN,zh;q=0.9,en-US;q=0.8,en;q=0.7"
        }

        # 设备参数 - 与原始API保持一致
        self.podcast_device_params = {
            "version_code": "20800",
            "language": "zh",
            "device_platform": "web",
            "aid": "497858",
            "real_aid": "497858",
            "pkg_type": "release_version",
            "device_id": "7468716989062841895",
            "web_id": "7468716986638386703",
            "tea_uuid": "7468716986638386703",
            "use-olympus-account": "1",
            "region": "CN",
            "sys_region": "CN",
            "samantha_web": "1",
            "pc_version": "2.24.2"
        }

    async def _async_load_config(self):
        """异步加载配置"""
        try:
            await self._async_load_plugin_config()
            await self._async_load_admin_config()
            await self._async_load_forward_config()
        except Exception as e:
            logger.error(f"[ArticleForwarder] 异步加载配置失败: {str(e)}")
            logger.error(traceback.format_exc())

    async def _async_load_plugin_config(self):
        """异步加载插件配置"""
        try:
            with open("plugins/ArticleForwarder/config.toml", "rb") as f:
                content = f.read().decode('utf-8')
                config = tomllib.loads(content)
                self.plugin_config = config.get("ArticleForwarder", {})
                self.enable = self.plugin_config.get("enabled", True)
                self.commands = self.plugin_config.get("commands", ["添加监控", "删除监控", "查看配置"])
        except Exception as e:
            logger.error(f"[ArticleForwarder] 异步加载插件配置出错: {e}")

    async def _async_load_admin_config(self):
        """异步加载管理员配置"""
        try:
            with open("main_config.toml", "rb") as f:
                content = f.read().decode('utf-8')
                config = tomllib.loads(content)
                self.admins = config["XYBot"]["admins"]
        except Exception as e:
            logger.error(f"[ArticleForwarder] 异步加载管理员配置出错: {e}")

    async def _async_load_forward_config(self):
        """异步加载转发配置"""
        config_file = self.data_dir / "config.json"
        try:
            if config_file.exists():
                with open(config_file, "r", encoding="utf-8") as f:
                    content = f.read()
                    data = json.loads(content)
                    old_accounts = data.get("accounts", {})

                    # 转换旧格式配置
                    self.accounts = {}
                    for account_id, value in old_accounts.items():
                        if isinstance(value, list):  # 旧格式: account_id -> [groups]
                            display_name = account_id if account_id.startswith("gh_") else account_id
                            self.accounts[account_id] = {
                                "name": display_name,
                                "groups": value
                            }
                        elif isinstance(value, dict):  # 新格式: account_id -> {name, groups}
                            if "name" not in value or not value["name"]:
                                # 如果name字段为空，使用ID作为名称
                                value["name"] = account_id
                            if "groups" not in value:
                                # 如果groups字段不存在，初始化为空列表
                                value["groups"] = []
                            self.accounts[account_id] = value
                        else:
                            logger.warning(f"未知的配置格式: {account_id} -> {value}")
                            continue
        except Exception as e:
            logger.error(f"[ArticleForwarder] 异步加载转发配置出错: {e}")

    def _load_config(self):
        """加载配置"""
        config_file = self.data_dir / "config.json"
        try:
            if config_file.exists():
                with open(config_file, "r", encoding="utf-8") as f:
                    data = json.load(f)
                    old_accounts = data.get("accounts", {})


                    # 转换旧格式配置
                    self.accounts = {}
                    for account_id, value in old_accounts.items():
                        if isinstance(value, list):  # 旧格式: account_id -> [groups]

                            # 尝试从ID中提取名称（如果ID是gh_开头，则使用ID作为名称）
                            display_name = account_id if account_id.startswith("gh_") else account_id
                            self.accounts[account_id] = {
                                "name": display_name,
                                "groups": value
                            }
                        elif isinstance(value, dict):  # 新格式: account_id -> {name, groups}

                            if "name" not in value or not value["name"]:
                                # 如果name字段为空，使用ID作为名称
                                value["name"] = account_id
                            if "groups" not in value:
                                # 如果groups字段不存在，初始化为空列表
                                value["groups"] = []
                            # 移除旧的mode字段
                            if "mode" in value:
                                del value["mode"]
                            self.accounts[account_id] = value
                        else:
                            logger.warning(f"未知的配置格式: {account_id} -> {value}")
                            continue

                    logger.info(f"加载配置完成，转换后的数据: {self.accounts}")
            else:
                logger.info("配置文件不存在，使用空配置")
                # 创建空配置文件
                self._save_config()
        except Exception as e:
            logger.error(f"加载转发配置出错: {e}")
            logger.error(f"错误详情: {traceback.format_exc()}")
            # 发生错误时使用空配置
            self.accounts = {}

    def _save_config(self):
        """保存配置"""
        config_file = self.data_dir / "config.json"
        data = {
            "accounts": self.accounts
        }
        with open(config_file, "w", encoding="utf-8") as f:
            json.dump(data, f, ensure_ascii=False, indent=2)


    async def _check_is_admin(self, bot: WechatAPIClient, group_id: str, member_wxid: str) -> bool:
        """检查用户是否是管理员"""
        # bot和group_id参数保留用于未来扩展
        return member_wxid in self.admins

    def _fix_xml_content(self, content):
        """修复XML内容中的常见错误"""
        import re

        # 尝试修复常见的XML错误模式
        fixed_content = content

        # 修复模式0: 处理XML开头被截断的情况
        # 如果XML以<msg>开头但紧接着是URL而不是<appmsg>，说明开头被截断了
        if fixed_content.startswith('<msg>//') or fixed_content.startswith('<msg>http'):
            logger.debug("[ArticleForwarder] 检测到XML开头被截断，尝试修复")
            # 提取URL部分
            url_match = re.match(r'<msg>(.*?)</url>', fixed_content, re.DOTALL)
            if url_match:
                url_content = url_match.group(1)
                # 如果URL不是以http开头，添加协议
                if not url_content.startswith('http'):
                    url_content = 'https:' + url_content
                # 重构XML开头
                fixed_content = f'<msg><appmsg><title>文章标题</title><des>文章描述</des><type>5</type><url>{url_content}</url>' + fixed_content[url_match.end():]
                logger.debug("[ArticleForwarder] 已修复XML开头截断问题")

        # 修复模式1: <des>content<type> -> <des>content</des><type>
        fixed_content = re.sub(r'(<des[^>]*>[^<]*)<(type|url|title|/appmsg)', r'\1</des><\2', fixed_content)

        # 修复模式2: <title>content<des> -> <title>content</title><des>
        fixed_content = re.sub(r'(<title[^>]*>[^<]*)<(des|type|url|/appmsg)', r'\1</title><\2', fixed_content)

        # 修复模式3: <url>content<type> -> <url>content</url><type>
        fixed_content = re.sub(r'(<url[^>]*>[^<]*)<(type|des|title|/appmsg)', r'\1</url><\2', fixed_content)

        # 修复模式4: <type>content<other> -> <type>content</type><other>
        fixed_content = re.sub(r'(<type[^>]*>[^<]*)<(/appmsg|/msg)', r'\1</type><\2', fixed_content)

        # 检查并添加缺失的闭合标签
        def add_missing_close_tags(text):
            # 查找所有开标签
            open_tags = re.findall(r'<([^/][^>\s]*)[^>]*>', text)
            # 查找所有闭标签
            close_tags = re.findall(r'</([^>\s]+)>', text)

            # 统计标签出现次数
            open_count = {}
            close_count = {}

            for tag in open_tags:
                open_count[tag] = open_count.get(tag, 0) + 1

            for tag in close_tags:
                close_count[tag] = close_count.get(tag, 0) + 1

            # 按照XML层级顺序添加缺失的闭合标签
            result = text
            tag_order = ["type", "url", "des", "title", "appmsg", "msg"]  # 从内到外的顺序

            for tag in tag_order:
                if tag in open_count and open_count[tag] > close_count.get(tag, 0):
                    missing_count = open_count[tag] - close_count.get(tag, 0)
                    for _ in range(missing_count):
                        result += f"</{tag}>"
                        logger.debug(f"[ArticleForwarder] 为标签 {tag} 添加了缺失的闭合标签")

            return result

        fixed_content = add_missing_close_tags(fixed_content)

        return fixed_content

    async def _save_failed_xml(self, content, msg_id, error_info):
        """保存解析失败的XML内容到文件"""
        try:
            import os
            # 创建临时目录（如果不存在）
            os.makedirs("temp/xml", exist_ok=True)

            # 生成唯一的文件名，包含错误信息
            error_type = error_info.split(':')[0] if ':' in error_info else 'unknown'
            xml_file = f"temp/xml/failed_xml_{msg_id}_{error_type}.xml"

            # 写入XML内容到文件
            with open(xml_file, "w", encoding="utf-8") as f:
                f.write(content)

            # 记录文件路径
            logger.debug(f"[ArticleForwarder] 解析失败的XML内容已保存到文件: {xml_file}")
            return True
        except Exception as e:
            logger.error(f"[ArticleForwarder] 保存解析失败的XML内容到文件失败: {e}")
            return False

    async def _send_podcast_music(self, bot: WechatAPIClient, wxid: str, title: str, singer: str,
                                  url: str, music_url: str, cover_url: str = "") -> bool:
        """发送播客音乐消息"""
        try:
            lyric = ""

            xml = f'''<appmsg appid="wx485a97c844086dc9" sdkver="0">
<title>{title}</title>
<des>{singer}</des>
<action>view</action>
<type>3</type>
<showtype>0</showtype>
<content/>
<url>{url}</url>
<dataurl>{music_url}</dataurl>
<lowurl>{url}</lowurl>
<lowdataurl>{music_url}</lowdataurl>
<recorditem/>
<thumburl>{cover_url}</thumburl>
<messageaction/>
<laninfo/>
<extinfo/>
<sourceusername/>
<sourcedisplayname/>
<songlyric>{lyric}</songlyric>
<commenturl/>
<appattach>
    <totallen>0</totallen>
    <attachid/>
    <emoticonmd5/>
    <fileext/>
    <aeskey/>
</appattach>
<webviewshared>
    <publisherId/>
    <publisherReqId>0</publisherReqId>
</webviewshared>
<weappinfo>
    <pagepath/>
    <username/>
    <appid/>
    <appservicetype>0</appservicetype>
</weappinfo>
<websearch/>
<songalbumurl>{cover_url}</songalbumurl>
</appmsg>
<fromusername>{bot.wxid}</fromusername>
<scene>0</scene>
<appinfo>
    <version>1</version>
    <appname></appname>
</appinfo>
<commenturl/>'''



            client_msg_id, _, new_msg_id = await bot.send_app_message(
                wxid,
                xml,
                3  # 音乐消息类型
            )

            if new_msg_id != 0 and client_msg_id:

                return True
            else:
                logger.error(f"[ArticleForwarder] 播客音乐消息发送失败")
                return False

        except Exception as e:
            logger.error(f"[ArticleForwarder] 播客音乐消息发送异常: {str(e)}")
            return False

    def _generate_podcast(self, url: str) -> dict:
        """生成播客"""
        if not self.podcast_session:
            logger.warning("[ArticleForwarder] 播客API未初始化")
            return {}

        try:
            # 生成播客请求 - 使用正确的API路径
            api_url = f"https://www.doubao.com/samantha/chat/completion?" + urlencode(self.podcast_device_params)

            # 生成本地会话ID
            local_conversation_id = f"local_{int(time.time() * 1000)}"
            local_message_id = str(uuid.uuid4())

            # 使用正确的请求体结构
            payload = {
                "messages": [{
                    "content": json.dumps({"text": "生成播客"}),
                    "content_type": 2034,
                    "attachments": [{
                        "type": "link",
                        "key": url,
                        "url": url
                    }]
                }],
                "completion_option": {
                    "is_regen": False,
                    "with_suggest": True,
                    "need_create_conversation": True,
                    "launch_stage": 1,
                    "is_replace": False,
                    "is_delete": False,
                    "message_from": 0,
                    "use_auto_cot": False,
                    "resend_for_regen": False,
                    "event_id": "0"
                },
                "conversation_id": "0",
                "local_conversation_id": local_conversation_id,
                "local_message_id": local_message_id
            }

            headers = self.podcast_headers.copy()
            headers.update({
                "content-type": "application/json",
                "last-event-id": "undefined",
                "Agw-Js-Conv": "str",
                "Accept": "*/*",
                "Referer": f"https://www.doubao.com/chat/{local_conversation_id}?type=2"
            })



            with self.podcast_session.stream('POST', api_url, json=payload, headers=headers) as response:
                if response.status_code != 200:
                    logger.error(f"[ArticleForwarder] 请求失败，状态码: {response.status_code}")
                    logger.error(f"[ArticleForwarder] 响应内容: {response.text[:500]}")
                    return {}

                logger.info(f"[ArticleForwarder] 播客生成请求发送成功")

                # 从SSE响应中提取conversation_id
                conversation_id = None
                for line in response.iter_lines():
                    line_text = line.decode('utf-8') if isinstance(line, bytes) else line
                    if line_text and line_text.startswith('data: '):
                        try:
                            data_str = line_text[6:]  # 去掉 'data: ' 前缀
                            if data_str and data_str != '{}':
                                data = json.loads(data_str)
                                if 'event_data' in data:
                                    event_data = json.loads(data.get('event_data', '{}'))
                                    if 'conversation_id' in event_data:
                                        conversation_id = event_data['conversation_id']
                                        logger.info(f"[ArticleForwarder] 提取到对话ID: {conversation_id}")
                                        break
                        except:
                            continue

            if conversation_id:
                logger.info(f"[ArticleForwarder] 播客生成请求成功，conversation_id: {conversation_id}")
                return {
                    'conversation_id': conversation_id,
                    'local_conversation_id': local_conversation_id,
                    'local_message_id': local_message_id
                }
            else:
                logger.error("[ArticleForwarder] 未获取到conversation_id")
                return {}

        except Exception as e:
            logger.error(f"[ArticleForwarder] 播客生成请求失败: {e}")
            return {}

    async def _generate_podcast_async(self, url: str) -> dict:
        """异步生成播客音频链接"""
        if not self.podcast_session:
            logger.warning("[ArticleForwarder] 播客API未初始化")
            return {}

        try:
            logger.info(f"[ArticleForwarder] 开始生成播客: {url}")

            # 在线程池中执行播客生成
            loop = asyncio.get_event_loop()

            # 生成播客
            generation_result = await loop.run_in_executor(
                None, self._generate_podcast, url
            )

            if not generation_result or not generation_result.get('conversation_id'):
                logger.error("[ArticleForwarder] 播客生成请求失败")
                return {}

            conversation_id = generation_result.get('conversation_id')
            logger.info(f"[ArticleForwarder] 播客生成请求成功，对话ID: {conversation_id}")

            # 轮询获取播客信息（使用优化后的流程）
            podcast_info = await loop.run_in_executor(
                None,
                self._poll_for_podcast_completion,
                conversation_id,
                self.podcast_initial_wait,
                self.podcast_max_attempts,
                self.podcast_poll_interval
            )

            if podcast_info and podcast_info.get('audio_link'):
                audio_link = podcast_info.get('audio_link')
                title = podcast_info.get('title', '')
                duration = podcast_info.get('duration', 0)
                logger.info(f"[ArticleForwarder] 播客生成成功")
                logger.info(f"[ArticleForwarder] 标题: {title}")
                logger.info(f"[ArticleForwarder] 时长: {duration}秒")
                logger.info(f"[ArticleForwarder] 音频链接: {audio_link}")
                return podcast_info
            else:
                logger.error("[ArticleForwarder] 播客生成失败或超时")
                return {}

        except Exception as e:
            logger.error(f"[ArticleForwarder] 播客生成异常: {str(e)}")
            return {}

    def _poll_for_podcast_completion(self, conversation_id: str, initial_wait: int = 180, max_attempts: int = 60, interval: int = 30) -> dict:
        """轮询播客生成完成状态"""
        if not self.podcast_session:
            logger.warning("[ArticleForwarder] 播客API未初始化")
            return {}

        try:
            logger.info(f"[ArticleForwarder] 开始轮询播客生成状态，conversation_id: {conversation_id}")
            logger.info(f"[ArticleForwarder] 初始等待: {initial_wait}秒，最大尝试: {max_attempts}次，间隔: {interval}秒")

            # 初始等待
            logger.info(f"[ArticleForwarder] 等待 {initial_wait} 秒后开始轮询...")
            time.sleep(initial_wait)

            for attempt in range(max_attempts):
                logger.info(f"[ArticleForwarder] 第 {attempt + 1}/{max_attempts} 次轮询")

                # 获取对话消息
                podcast_info = self._get_conversation_messages(conversation_id)

                if podcast_info:
                    episode_id = podcast_info.get('episode_id')

                    if episode_id:
                        logger.info(f"[ArticleForwarder] 找到播客episode_id: {episode_id}，正在获取详细信息...")

                        # 调用GetGenPodcastDetail获取最终的音频链接
                        detail_info = self._get_podcast_detail(episode_id)

                        if detail_info and detail_info.get('audio_link'):
                            audio_link = detail_info.get('audio_link')
                            if audio_link and audio_link.startswith('https://'):
                                logger.info(f"[ArticleForwarder] 播客生成完成！获取到最终音频链接")
                                return detail_info
                            else:
                                logger.info(f"[ArticleForwarder] 播客正在生成中，音频链接未就绪: {audio_link}")
                        else:
                            logger.info(f"[ArticleForwarder] 播客正在生成中，详细信息未就绪")
                    else:
                        logger.info(f"[ArticleForwarder] 播客正在生成中，episode_id未就绪")
                else:
                    logger.info(f"[ArticleForwarder] 播客正在生成中，未找到播客消息")

                # 等待下次轮询
                if attempt < max_attempts - 1:
                    logger.info(f"[ArticleForwarder] 等待 {interval} 秒后进行下次轮询...")
                    time.sleep(interval)

            logger.error(f"[ArticleForwarder] 播客生成超时，已尝试 {max_attempts} 次")
            return {}

        except Exception as e:
            logger.error(f"[ArticleForwarder] 轮询播客生成状态时发生错误: {str(e)}")
            return {}

    def _get_conversation_messages(self, conversation_id: str) -> dict:
        """获取对话消息"""
        if not self.podcast_session:
            return {}

        try:
            api_url = f"https://www.doubao.com/alice/message/list?" + urlencode(self.podcast_device_params)

            payload = {
                "conversation_id": conversation_id,
                "cursor": 9007199254740991,  # 从最新消息开始
                "batch_size": 50
            }

            headers = self.podcast_headers.copy()
            headers["Referer"] = f"https://www.doubao.com/chat/{conversation_id}"

            response = self.podcast_session.post(api_url, json=payload, headers=headers)
            response.raise_for_status()

            result = response.json()

            if result.get("code") == 0:
                messages = result.get("data", {}).get("message_list", [])

                # 查找包含播客信息的消息
                for message in messages:
                    if message.get("content_type") == 100:  # 播客widget消息
                        content_str = message.get("content", "{}")
                        try:
                            content = json.loads(content_str)
                            if content.get("applet_id") == "com.flow.podcast":
                                # 解析播客数据
                                widget_data_str = content.get("widget_data", "{}")
                                widget_data = json.loads(widget_data_str)
                                data_str = widget_data.get("data", "{}")
                                data = json.loads(data_str)

                                episode_list = data.get("episodeList", {})
                                episodes = episode_list.get("episodes", [])

                                if episodes:
                                    episode = episodes[0]
                                    podcast_info = {
                                        'episode_id': episode.get('id'),
                                        'conversation_id': message.get('conversation_id'),
                                        'message_id': message.get('message_id')
                                    }

                                    logger.info(f"[ArticleForwarder] 找到播客信息，ID: {podcast_info['episode_id']}")
                                    return podcast_info

                        except (json.JSONDecodeError, KeyError):
                            continue

                logger.info("[ArticleForwarder] 未在消息中找到播客信息")
                return {}
            else:
                logger.error(f"[ArticleForwarder] 获取消息列表失败: {result.get('msg', '未知错误')}")
                return {}

        except Exception as e:
            logger.error(f"[ArticleForwarder] 获取对话消息时发生错误: {str(e)}")
            return {}

    def _get_podcast_detail(self, episode_id: str) -> dict:
        """获取播客详细信息，包括最终的音频链接"""
        if not self.podcast_session:
            return {}

        try:
            api_url = f"https://www.doubao.com/api/doubao/do_action_v2?" + urlencode(self.podcast_device_params)

            payload = {
                "scene": "FPA_Podcast",
                "payload": json.dumps({
                    "api_name": "GetGenPodcastDetail",
                    "params": json.dumps({
                        "id": episode_id
                    })
                })
            }

            headers = self.podcast_headers.copy()
            headers["Referer"] = f"https://www.doubao.com/chat/9742980049534466"

            response = self.podcast_session.post(api_url, json=payload, headers=headers)
            response.raise_for_status()

            result = response.json()
            logger.info(f"[ArticleForwarder] GetGenPodcastDetail响应: {json.dumps(result, ensure_ascii=False, indent=2)}")

            if result.get("code") == 0 and result.get("data", {}).get("success"):
                resp_str = result.get("data", {}).get("resp", "{}")
                resp_data = json.loads(resp_str)

                episode = resp_data.get("episode", {})
                if episode:
                    meta = episode.get("meta", {})
                    playback_model = meta.get("playback_model", {})

                    podcast_info = {
                        'episode_id': episode.get('id'),
                        'title': meta.get('title'),
                        'cover_link': meta.get('cover_link'),
                        'audio_link': playback_model.get('audio_link'),
                        'duration': playback_model.get('duration'),
                        'podcast_title': meta.get('podcast_title'),
                        'podcast_cover': meta.get('podcast_cover')
                    }

                    logger.info(f"[ArticleForwarder] 获取播客详细信息成功")
                    logger.info(f"[ArticleForwarder] 标题: {podcast_info['title']}")
                    logger.info(f"[ArticleForwarder] 时长: {podcast_info['duration']}秒")
                    logger.info(f"[ArticleForwarder] 音频链接: {podcast_info['audio_link']}")

                    return podcast_info
                else:
                    logger.error("[ArticleForwarder] 响应中没有episode信息")
                    return {}
            else:
                logger.error(f"[ArticleForwarder] 获取播客详细信息失败: {result.get('msg', '未知错误')}")
                return {}

        except Exception as e:
            logger.error(f"[ArticleForwarder] 获取播客详细信息时发生错误: {str(e)}")
            return {}

    def _extract_account_id(self, xml_content: str) -> str:
        """从XML中提取公众号ID"""
        try:
            root = ET.fromstring(xml_content)

            # 优先使用sourcedisplayname（公众号显示名称）
            appmsg = root.find(".//appmsg")
            if appmsg:
                sourcedisplayname = appmsg.find("sourcedisplayname")
                if sourcedisplayname is not None and sourcedisplayname.text:
                    logger.debug(f"从sourcedisplayname提取到公众号: {sourcedisplayname.text}")
                    return sourcedisplayname.text

                # 其次使用sourceusername（公众号原始ID）
                sourceusername = appmsg.find("sourceusername")
                if sourceusername is not None and sourceusername.text:
                    logger.debug(f"从sourceusername提取到公众号: {sourceusername.text}")
                    return sourceusername.text

            # 尝试从publisher节点获取
            publisher = root.find(".//publisher")
            if publisher:
                nickname = publisher.find("nickname")
                if nickname is not None and nickname.text:
                    logger.debug(f"从publisher/nickname提取到公众号: {nickname.text}")
                    return nickname.text

                username = publisher.find("username")
                if username is not None and username.text:
                    logger.debug(f"从publisher/username提取到公众号: {username.text}")
                    return username.text

            # 尝试从mmreader/category/name获取
            mmreader = root.find(".//mmreader")
            if mmreader:
                category = mmreader.find("category")
                if category:
                    name = category.find("name")
                    if name is not None and name.text:
                        logger.debug(f"从category/name提取到公众号: {name.text}")
                        return name.text

            # 最后尝试从appinfo/appname获取
            appinfo = root.find(".//appinfo")
            if appinfo:
                appname = appinfo.find("appname")
                if appname is not None and appname.text:
                    logger.debug(f"从appinfo/appname提取到公众号: {appname.text}")
                    return appname.text

            logger.warning("未能从XML中提取到公众号ID")
            return ""

        except Exception as e:
            logger.error(f"提取公众号ID失败: {e}")
            logger.error(f"XML内容: {xml_content[:200]}...")  # 只记录前200个字符避免日志过大
            return ""

    @on_text_message
    async def handle_text(self, bot: WechatAPIClient, message: dict):
        """处理文本消息"""
        if not message["IsGroup"]:
            return

        content = message["Content"].strip()
        # 只处理"查看配置"命令
        if content != "查看配置":
            return

        group_id = message["FromWxid"]
        sender_wxid = message["SenderWxid"]

        # 检查权限
        is_admin = await self._check_is_admin(bot, group_id, sender_wxid)
        if not is_admin:
            await bot.send_text_message(group_id, "⚠️ 抱歉，只有管理员才能操作文章转发配置")
            return

        # 处理"查看配置"命令
        await self._handle_show_config(bot, message)

    async def _handle_show_config(self, bot: WechatAPIClient, message: dict):
        """处理查看配置命令"""
        group_id = message["FromWxid"]

        if not self.accounts:
            await bot.send_text_message(group_id, "📊 当前没有监控任何公众号")
            return

        # 统计当前群监控的公众号
        monitoring = []
        for _, info in self.accounts.items():
            if group_id in info["groups"]:
                monitoring.append(info['name'])

        # 生成配置信息
        msg = "📊 当前群监控的公众号\n\n"
        if monitoring:
            msg += "\n".join([f"✦ {name}" for name in monitoring])
            msg += f"\n\n共监控 {len(monitoring)} 个公众号"
            if self.podcast_session:
                msg += "\n\n🎵 播客功能已启用，新文章将同时发送卡片和播客"
            else:
                msg += "\n\n📄 仅发送卡片消息（播客功能未启用）"
        else:
            msg += "暂未监控任何公众号"

        await bot.send_text_message(group_id, msg)



    async def _handle_add_monitor(self, bot: WechatAPIClient, message: dict, quote: dict):
        """处理添加监控命令"""
        group_id = message["FromWxid"]
        logger.debug(f"开始处理添加监控命令 - 群ID: {group_id}")

        # 检查引用消息类型
        msg_type = quote.get("MsgType")
        if msg_type != 49:  # 49是公众号文章类型
            await bot.send_text_message(group_id, "⚠️ 请引用一条公众号文章消息")
            return

        # 获取引用消息内容
        content = quote.get("Content", "")
        if not content:
            await bot.send_text_message(group_id, "⚠️ 引用消息内容为空")
            return

        try:
            # 解析XML内容
            root = ET.fromstring(content)

            # 检查是否是公众号文章
            appmsg = root.find(".//appmsg")
            if appmsg is None:
                await bot.send_text_message(group_id, "⚠️ 不是公众号文章消息")
                return

            # 检查消息类型
            msg_type = appmsg.find("type")
            logger.debug(f"文章类型节点: {msg_type}, 内容: {msg_type.text if msg_type is not None else 'None'}")

            if msg_type is None or msg_type.text != "5":  # 5是公众号文章类型
                logger.debug(f"消息子类型不是公众号文章: {msg_type.text if msg_type is not None else 'None'}")
                return

            # 提取公众号ID和名称
            account_id = ""
            account_name = ""

            # 优先使用sourceusername作为ID
            sourceusername = appmsg.find("sourceusername")
            if sourceusername is not None and sourceusername.text:
                account_id = sourceusername.text
                logger.debug(f"从sourceusername提取到公众号ID: {account_id}")

            # 使用sourcedisplayname作为名称
            sourcedisplayname = appmsg.find("sourcedisplayname")
            if sourcedisplayname is not None and sourcedisplayname.text:
                account_name = sourcedisplayname.text
                logger.debug(f"从sourcedisplayname提取到公众号名称: {account_name}")

            if not account_id:
                logger.warning("未能提取到公众号ID")
                await bot.send_text_message(group_id, "⚠️ 无法获取公众号ID，请确保引用的是公众号文章")
                return

            if not account_name:
                account_name = account_id

            # 检查公众号是否已经在监控列表中
            if account_id in self.accounts:
                # 检查群是否已经在转发列表中
                if group_id in self.accounts[account_id]["groups"]:
                    logger.info(f"群 {group_id} 已经在公众号 {account_name} 的转发列表中")
                    await bot.send_text_message(group_id, f"此群已经在公众号「{account_name}」的转发列表中了哦 😊")
                    return
                # 添加群到转发列表
                self.accounts[account_id]["groups"].append(group_id)
                logger.info(f"将群 {group_id} 添加到公众号 {account_name} 的转发列表中")
            else:
                # 新建公众号配置
                self.accounts[account_id] = {
                    "name": account_name,
                    "groups": [group_id]
                }
                logger.info(f"新增公众号 {account_name} 的监控，转发到群 {group_id}")

            # 保存配置
            self._save_config()

            await bot.send_text_message(group_id, f"✅ 已添加公众号「{account_name}」的监控\n此群将接收该公众号的新文章")

        except ET.ParseError as e:
            logger.error(f"解析XML失败: {e}")
            logger.debug(f"错误的XML内容: {content[:200]}...")  # 只记录前200个字符避免日志过大
            await bot.send_text_message(group_id, "⚠️ 消息格式错误，请确保引用的是公众号文章")
        except Exception as e:
            logger.error(f"处理添加监控命令失败: {e}")
            logger.error(f"错误详情: {traceback.format_exc()}")
            await bot.send_text_message(group_id, "⚠️ 处理失败，请稍后重试")

    async def _handle_remove_monitor(self, bot: WechatAPIClient, message: dict, quote: dict):
        """处理删除监控命令"""
        group_id = message["FromWxid"]

        # 检查引用的消息
        if not quote or quote.get("MsgType") != 49:
            await bot.send_text_message(group_id, "⚠️ 请引用一条公众号文章消息")
            return

        # 获取引用消息内容
        content = quote.get("Content", "")
        if not content:
            await bot.send_text_message(group_id, "⚠️ 引用消息内容为空")
            return

        try:
            # 解析XML内容
            root = ET.fromstring(content)

            # 检查是否是公众号文章
            appmsg = root.find(".//appmsg")
            if appmsg is None:
                await bot.send_text_message(group_id, "⚠️ 不是公众号文章消息")
                return

            # 检查消息类型
            msg_type = appmsg.find("type")
            if msg_type is None or msg_type.text != "5":  # 5是公众号文章类型
                await bot.send_text_message(group_id, "⚠️ 不是公众号文章消息")
                return

            # 提取公众号ID和名称
            account_id = ""
            account_name = ""

            # 优先使用sourceusername作为ID
            sourceusername = appmsg.find("sourceusername")
            if sourceusername is not None and sourceusername.text:
                account_id = sourceusername.text
                logger.debug(f"从sourceusername提取到公众号ID: {account_id}")

            # 使用sourcedisplayname作为名称
            sourcedisplayname = appmsg.find("sourcedisplayname")
            if sourcedisplayname is not None and sourcedisplayname.text:
                account_name = sourcedisplayname.text
                logger.debug(f"从sourcedisplayname提取到公众号名称: {account_name}")

            if not account_id:
                logger.warning("未能提取到公众号ID")
                await bot.send_text_message(group_id, "⚠️ 无法获取公众号ID，请确保引用的是公众号文章")
                return

            if not account_name:
                account_name = account_id

            # 检查公众号是否在监控列表中
            if account_id not in self.accounts:
                await bot.send_text_message(group_id, f"公众号「{account_name}」不在监控列表中哦 😊")
                return

            # 检查群是否在该公众号的转发列表中
            if group_id not in self.accounts[account_id]["groups"]:
                await bot.send_text_message(group_id, f"此群没有监控公众号「{account_name}」哦 😊")
                return

            # 从转发列表中移除群
            self.accounts[account_id]["groups"].remove(group_id)

            # 如果转发列表为空，删除该公众号的配置
            if not self.accounts[account_id]["groups"]:
                del self.accounts[account_id]
                logger.info(f"公众号 {account_name} 已没有转发群，移除监控")

            # 保存配置
            self._save_config()

            logger.info(f"群 {group_id} 取消了公众号 {account_name} 的监控")
            await bot.send_text_message(group_id, f"✅ 已取消公众号「{account_name}」的监控")

        except ET.ParseError as e:
            logger.error(f"解析XML失败: {e}")
            logger.debug(f"错误的XML内容: {content[:200]}...")  # 只记录前200个字符避免日志过大
            await bot.send_text_message(group_id, "⚠️ 消息格式错误，请确保引用的是公众号文章")
        except Exception as e:
            logger.error(f"处理删除监控命令失败: {e}")
            logger.error(f"错误详情: {traceback.format_exc()}")
            await bot.send_text_message(group_id, "⚠️ 处理失败，请稍后重试")

    @on_article_message
    async def handle_article(self, bot: WechatAPIClient, message: dict):
        """处理公众号文章消息"""
        # 检查消息类型
        if message.get("MsgType") != 49:  # 49是公众号文章消息类型
            return

        # 获取消息内容
        content = message.get("Content")
        if isinstance(content, dict):
            content = content.get("string", "")

        # 如果内容不是以<msg>开头，但包含XML内容，尝试修复
        if content and not content.startswith("<msg>"):
            if "<msg>" in content:
                # 找到<msg>的位置并截取
                start_index = content.find("<msg>")
                content = content[start_index:]
            elif "</msg>" in content and "//" in content:
                # 如果内容看起来是XML但缺少开头，尝试修复
                content = f"<msg>{content}"

        # 检查是否是XML格式的消息
        if not content or not content.startswith("<msg>"):
            return

        # 检查是否是公众号文章
        try:
            root = ET.fromstring(content)
            appmsg = root.find(".//appmsg")
            if appmsg is None:
                return

            # 检查消息类型
            msg_type = appmsg.find("type")
            if msg_type is None or msg_type.text != "5":  # 5是公众号文章类型
                return

            # 提取公众号ID和名称
            account_id = ""
            account_name = ""

            # 首先检查是否是直接从公众号接收的消息
            if message.get("IsDirectFromMP", False) and message.get("PublicAccountID", ""):
                # 直接使用XYBot添加的额外信息
                account_id = message.get("PublicAccountID")
                logger.debug(f"使用直接提供的公众号ID: {account_id}")

                # 尝试从XML获取名称
                publisher = root.find(".//publisher")
                if publisher is not None:
                    nickname = publisher.find("nickname")
                    if nickname is not None and nickname.text:
                        account_name = nickname.text
                        logger.debug(f"从publisher/nickname获取到公众号名称: {account_name}")
            else:
                # 使用现有的提取方法
                account_id = self._extract_account_id(content)

            # 如果没有从上述途径获取到ID，则尝试其他方法
            if not account_id:
                # 优先使用sourceusername作为ID
                sourceusername = appmsg.find("sourceusername")
                if sourceusername is not None and sourceusername.text:
                    account_id = sourceusername.text
                    logger.debug(f"从sourceusername提取到公众号ID: {account_id}")

                # 使用sourcedisplayname作为名称
                sourcedisplayname = appmsg.find("sourcedisplayname")
                if sourcedisplayname is not None and sourcedisplayname.text:
                    account_name = sourcedisplayname.text
                    logger.debug(f"从sourcedisplayname提取到公众号名称: {account_name}")

                # 如果仍然没有获取到ID，对于直接来自公众号的消息，可以使用FromWxid
                if not account_id and not message.get("IsGroup", False) and message.get("FromWxid", "").startswith("gh_"):
                    account_id = message.get("FromWxid")
                    logger.debug(f"从FromWxid提取到公众号ID: {account_id}")

            if not account_id:
                logger.debug("未提取到公众号ID，跳过处理")
                return

            if not account_name:
                account_name = account_id

            if account_id not in self.accounts:
                logger.debug(f"公众号「{account_name}」不在监控列表中，跳过处理")
                return

            # 检查是否是直接从公众号接收的消息
            is_direct_msg = message.get("IsDirectFromMP", False) or (not message.get("IsGroup", False) and message.get("FromWxid", "").startswith("gh_"))
            if is_direct_msg:
                logger.info(f"直接接收到监控公众号「{account_name}」的文章，准备转发")
            else:
                logger.info(f"从群聊接收到监控公众号「{account_name}」的文章，准备转发")

            # 提取文章信息
            title = appmsg.find(".//title")
            title = title.text if title is not None else "无标题"

            # 获取URL（优先使用webviewshared中的URL）
            url = None
            webviewshared = appmsg.find(".//webviewshared")
            if webviewshared:
                # 优先使用shareUrlOpen
                url_node = webviewshared.find("shareUrlOpen")
                if url_node is not None and url_node.text:
                    url = url_node.text
                else:
                    # 备选shareUrlOriginal
                    url_node = webviewshared.find("shareUrlOriginal")
                    if url_node is not None and url_node.text:
                        url = url_node.text

            # 如果webviewshared中没有找到URL，使用appmsg/url
            if not url:
                url_node = appmsg.find(".//url")
                url = url_node.text if url_node is not None else None

            if not url:
                logger.error("未找到文章URL")
                return

            # 获取缩略图URL
            thumb_url = None
            # 优先使用thumburl
            thumb_node = appmsg.find(".//thumburl")
            if thumb_node is not None and thumb_node.text:
                thumb_url = thumb_node.text
            else:
                # 尝试使用cover
                cover_node = appmsg.find(".//cover")
                if cover_node is not None and cover_node.text:
                    thumb_url = cover_node.text

            if not thumb_url:
                logger.warning("未找到缩略图URL")

            # 获取文章摘要
            summary = ""
            mmreader = appmsg.find(".//mmreader")
            if mmreader:
                category = mmreader.find("category")
                if category:
                    item = category.find("item")
                    if item:
                        summary_node = item.find("summary")
                        if summary_node is not None:
                            summary = summary_node.text or ""

            if not summary:
                # 尝试从des字段获取摘要
                des_node = appmsg.find(".//des")
                if des_node is not None:
                    summary = des_node.text or ""

            if not summary:
                # 如果还是没有摘要，尝试从item/digest获取
                digest_node = appmsg.find(".//item/digest")
                if digest_node is not None:
                    summary = digest_node.text or ""

            # 转发到目标群
            success_count = 0
            target_groups = self.accounts[account_id]["groups"]

            # 只发送卡片消息，不自动生成播客
            for group_id in target_groups:
                try:
                    await bot.send_link_message(
                        group_id,
                        url=url,
                        title=title,
                        description=summary[:100] + "..." if summary else "",  # 限制描述长度
                        thumb_url=thumb_url or ""  # 如果没有缩略图也不影响发送
                    )
                    success_count += 1
                    logger.info(f"已发送卡片到群 {group_id}: {title}")
                except Exception as e:
                    logger.error(f"发送卡片到群 {group_id} 失败: {e}")

            logger.info(f"文章转发完成，卡片成功: {success_count}/{len(target_groups)} 个群")

        except ET.ParseError as e:
            logger.error(f"解析XML失败: {e}")
            # 尝试修复XML并重新解析
            try:
                fixed_content = self._fix_xml_content(content)
                if fixed_content != content:
                    logger.debug("尝试使用修复后的XML重新解析")
                    # 递归调用自己，使用修复后的内容
                    fixed_message = message.copy()
                    fixed_message["Content"] = fixed_content
                    await self.handle_article(bot, fixed_message)
                    return
                else:
                    logger.debug("XML修复未产生变化")
            except Exception as fix_error:
                logger.error(f"XML修复失败: {fix_error}")

            # 保存失败的XML内容到文件用于调试
            await self._save_failed_xml(content, message.get("MsgId", "unknown"), str(e))
            logger.debug("无法解析XML内容")
        except Exception as e:
            logger.error(f"处理公众号文章失败: {e}")
            logger.error(f"错误详情: {traceback.format_exc()}")

    @on_quote_message
    async def handle_quote(self, bot: WechatAPIClient, message: dict):
        """处理引用消息"""
        # 安全地检查消息是否来自群聊
        if not message.get("IsGroup", False):
            return

        content = message.get("Content", "").strip()

        # 直接判断具体的命令，不使用self.commands
        if content not in ["添加监控", "删除监控", "播客"]:  # 处理需要引用的命令
            return

        # 获取引用消息
        quote = message.get("Quote", {})
        logger.debug(f"处理引用消息命令[{content}]: {quote}")

        group_id = message["FromWxid"]
        sender_wxid = message["SenderWxid"]

        # 播客命令不需要管理员权限，其他命令需要
        if content != "播客":
            # 检查权限
            is_admin = await self._check_is_admin(bot, group_id, sender_wxid)
            if not is_admin:
                await bot.send_text_message(group_id, "⚠️ 抱歉，只有管理员才能操作文章转发配置")
                return

        # 检查引用消息是否存在
        if not quote:
            await bot.send_text_message(group_id, "⚠️ 请引用一条消息")
            return

        # 检查引用消息类型
        msg_type = quote.get("MsgType")
        quote_content = quote.get("Content", "")
        logger.debug(f"引用消息类型: {msg_type}, 内容长度: {len(quote_content) if quote_content else 0}")

        # 根据命令调用对应的处理方法
        if content == "添加监控":
            await self._handle_add_monitor(bot, message, quote)
        elif content == "删除监控":
            await self._handle_remove_monitor(bot, message, quote)
        elif content == "播客":
            await self._handle_manual_podcast(bot, message, quote)

    async def _handle_manual_podcast(self, bot: WechatAPIClient, message: dict, quote: dict):
        """处理手动播客生成命令"""
        group_id = message["FromWxid"]

        # 检查播客功能是否可用
        if not self.podcast_session:
            await bot.send_text_message(group_id, "⚠️ 播客功能未启用或配置错误")
            return

        # 检查引用消息类型
        msg_type = quote.get("MsgType")
        if msg_type != 49:  # 49是公众号文章类型
            await bot.send_text_message(group_id, "⚠️ 请引用一条公众号文章消息")
            return

        # 获取引用消息内容
        content = quote.get("Content", "")
        if not content:
            await bot.send_text_message(group_id, "⚠️ 引用消息内容为空")
            return

        try:
            # 解析XML内容
            root = ET.fromstring(content)

            # 检查是否是公众号文章
            appmsg = root.find(".//appmsg")
            if appmsg is None:
                await bot.send_text_message(group_id, "⚠️ 不是公众号文章消息")
                return

            # 检查消息类型
            msg_type = appmsg.find("type")
            if msg_type is None or msg_type.text != "5":  # 5是公众号文章类型
                await bot.send_text_message(group_id, "⚠️ 不是公众号文章消息")
                return

            # 提取文章信息
            title = appmsg.find(".//title")
            title = title.text if title is not None else "无标题"

            # 提取公众号名称
            account_name = ""
            sourcedisplayname = appmsg.find("sourcedisplayname")
            if sourcedisplayname is not None and sourcedisplayname.text:
                account_name = sourcedisplayname.text
            else:
                account_name = "未知公众号"

            # 获取URL
            url = None
            webviewshared = appmsg.find(".//webviewshared")
            if webviewshared:
                url_node = webviewshared.find("shareUrlOpen")
                if url_node is not None and url_node.text:
                    url = url_node.text
                else:
                    url_node = webviewshared.find("shareUrlOriginal")
                    if url_node is not None and url_node.text:
                        url = url_node.text

            if not url:
                url_node = appmsg.find(".//url")
                url = url_node.text if url_node is not None else None

            if not url:
                await bot.send_text_message(group_id, "⚠️ 未找到文章链接")
                return

            # 获取缩略图URL
            thumb_url = ""
            thumb_node = appmsg.find(".//thumburl")
            if thumb_node is not None and thumb_node.text:
                thumb_url = thumb_node.text
            else:
                cover_node = appmsg.find(".//cover")
                if cover_node is not None and cover_node.text:
                    thumb_url = cover_node.text

            # 发送处理中提示
            await bot.send_text_message(group_id, f"🎵 正在为文章「{title}」生成播客，请稍候...")

            logger.info(f"[ArticleForwarder] 手动生成播客: {title} - {url}")

            # 生成播客
            podcast_info = await self._generate_podcast_async(url)

            if podcast_info and podcast_info.get('audio_link'):
                audio_link = podcast_info.get('audio_link')
                # 优先使用播客生成的封面，备选原文章封面
                podcast_cover = podcast_info.get('cover_link') or thumb_url or ""

                # 发送播客音乐消息
                success = await self._send_podcast_music(
                    bot, group_id, title, account_name, url, audio_link, podcast_cover
                )
                if success:
                    logger.info(f"[ArticleForwarder] 手动播客生成成功: {title}")
                    # 不发送完成提示，直接发送播客音乐消息即可
                else:
                    await bot.send_text_message(group_id, "❌ 播客发送失败，请稍后重试")
            else:
                await bot.send_text_message(group_id, "❌ 播客生成失败，请稍后重试")

        except ET.ParseError as e:
            logger.error(f"解析XML失败: {e}")
            await bot.send_text_message(group_id, "⚠️ 消息格式错误，请确保引用的是公众号文章")
        except Exception as e:
            logger.error(f"处理手动播客命令失败: {e}")
            logger.error(f"错误详情: {traceback.format_exc()}")
            await bot.send_text_message(group_id, "⚠️ 处理失败，请稍后重试")