2025-07-22 01:22:51 | SUCCESS | 读取主设置成功
2025-07-22 01:22:51 | DEBUG | 最终使用的 Redis 主机地址: 127.0.0.1
2025-07-22 01:22:51 | INFO | 2025/07/22 01:22:51 GetRedisAddr: 127.0.0.1:6379
2025-07-22 01:22:51 | INFO | 2025/07/22 01:22:51 Redis连接成功 | 地址:127.0.0.1:6379 DB:0
2025-07-22 01:22:51 | INFO | 2025/07/22 01:22:51 Server start at :9000
2025-07-22 01:22:51 | SUCCESS | WechatAPI服务已启动
2025-07-22 01:22:52 | INFO | 登录账号信息: wxid: wxid_4usgcju5ey9q29  昵称: 瑶瑶  微信号: Today-KFC  手机号: None
2025-07-22 01:22:52 | INFO | 登录设备信息: device_name: <PERSON>'s Pad  device_id: 49d050a7e94d1ecc40790d74b1c4369f
2025-07-22 01:22:52 | SUCCESS | 登录成功
2025-07-22 01:22:52 | SUCCESS | 已开启自动心跳
2025-07-22 01:22:52 | INFO | 成功加载表情映射文件，共 521 条记录
2025-07-22 01:22:52 | SUCCESS | 数据库初始化成功
2025-07-22 01:22:52 | SUCCESS | 定时任务已启动
2025-07-22 01:22:52 | SUCCESS | 插件 AISignIn 已加载定时任务: {'plugins.AISignIn.main.AISignIn.auto_signin_task'}
2025-07-22 01:22:52 | INFO | 已加载管理员列表: ['wxid_ubbh6q832tcs21', 'admin-wxid']
2025-07-22 01:22:52 | INFO | 播客API初始化成功
2025-07-22 01:22:52 | INFO | 加载配置完成，转换后的数据: {'gh_7057516c9e71': {'name': '唱舞全明星', 'groups': ['27852221909@chatroom', '55878994168@chatroom', '51891329927@chatroom']}, 'gh_d4d21a0e4623': {'name': '两颗桃', 'groups': ['48097389945@chatroom']}, 'gh_410ff2fa6ec9': {'name': 'NGC660 Ai研究院', 'groups': ['47442567074@chatroom']}, 'gh_f78c53c507c0': {'name': '不正常吐槽君', 'groups': ['48097389945@chatroom']}, 'gh_ba3381c847b3': {'name': '唱舞星计划', 'groups': ['51891329927@chatroom']}, 'gh_1a102fb4cad9': {'name': '我在娱乐圈的唯一人脉', 'groups': ['48097389945@chatroom', '27852221909@chatroom']}}
2025-07-22 01:22:52 | INFO | ArticleForwarder插件初始化完成 - 监控配置: {'gh_7057516c9e71': {'name': '唱舞全明星', 'groups': ['27852221909@chatroom', '55878994168@chatroom', '51891329927@chatroom']}, 'gh_d4d21a0e4623': {'name': '两颗桃', 'groups': ['48097389945@chatroom']}, 'gh_410ff2fa6ec9': {'name': 'NGC660 Ai研究院', 'groups': ['47442567074@chatroom']}, 'gh_f78c53c507c0': {'name': '不正常吐槽君', 'groups': ['48097389945@chatroom']}, 'gh_ba3381c847b3': {'name': '唱舞星计划', 'groups': ['51891329927@chatroom']}, 'gh_1a102fb4cad9': {'name': '我在娱乐圈的唯一人脉', 'groups': ['48097389945@chatroom', '27852221909@chatroom']}}
2025-07-22 01:22:52 | DEBUG | [TempFileManager] 添加清理规则: default
2025-07-22 01:22:52 | DEBUG | [TempFileManager] 添加清理规则: images
2025-07-22 01:22:52 | DEBUG | [TempFileManager] 添加清理规则: videos
2025-07-22 01:22:52 | DEBUG | [TempFileManager] 添加清理规则: audio
2025-07-22 01:22:52 | DEBUG | [TempFileManager] 添加清理规则: temp
2025-07-22 01:22:52 | DEBUG | [TempFileManager] 添加清理规则: large_files
2025-07-22 01:22:52 | INFO | [TempFileManager] 临时文件清理任务已启动
2025-07-22 01:22:53 | INFO | [ChatSummary] 数据库初始化成功
2025-07-22 01:22:53 | INFO | 成功加载表情映射文件，共 521 条记录
2025-07-22 01:22:53 | SUCCESS | 插件 GoodMorning 已加载定时任务: {'plugins.GoodMorning.main.GoodMorning.daily_task'}
2025-07-22 01:22:53 | SUCCESS | 插件 News 已加载定时任务: {'plugins.News.main.News.night_news', 'plugins.News.main.News.noon_news'}
2025-07-22 01:22:53 | INFO | [PatReply] AMR文件已存在且是最新的: C:\XYBotV2\data\paiyipai\1_BFB5258B-2B13-4CF4-A40C-5BCCE74E5414_converted.amr
2025-07-22 01:22:53 | DEBUG | [PatReply] 从缓存加载base64: 18228 字符
2025-07-22 01:22:53 | SUCCESS | 插件 QuarkSignIn 已加载定时任务: {'plugins.QuarkSignIn.main.QuarkSignIn.auto_signin_task'}
2025-07-22 01:22:53 | INFO | [QuarkSignIn] 已设置自动签到时间: 08:00
2025-07-22 01:22:53 | INFO | 插件状态: 启用, 检查间隔: 3600秒, 最大重试次数: 3, 重试延迟: 5秒
2025-07-22 01:22:53 | INFO | 已加载管理员列表: ['wxid_ubbh6q832tcs21', 'admin-wxid']
2025-07-22 01:22:53 | INFO | 已加载 2 个改名监控群和 1 个退群监控群
2025-07-22 01:22:53 | INFO | [RenameReminder] 开始启用插件...
2025-07-22 01:22:53 | SUCCESS | 插件 RenameReminder 已加载定时任务: {'plugins.RenameReminder.main.RenameReminder.check_name_changes'}
2025-07-22 01:22:53 | INFO | [RenameReminder] 插件已启用，开始初始化...
2025-07-22 01:22:53 | INFO | [RenameReminder] 支持的命令: 开启改名提醒, 关闭改名提醒, 开启退群提醒, 关闭退群提醒, 查看提醒状态
2025-07-22 01:22:53 | INFO | 已设置检查间隔为 3600 秒
2025-07-22 01:22:53 | INFO | 已加载 2 个监控群，开始更新成员列表
2025-07-22 01:22:54 | DEBUG | 已更新群 51891329927@chatroom 的成员列表
2025-07-22 01:22:54 | DEBUG | 已更新群 27852221909@chatroom 的成员列表
2025-07-22 01:22:54 | SUCCESS | 插件 TimerTask 已加载定时任务: {'plugins.TimerTask.main.TimerTask.check_tasks'}
2025-07-22 01:22:54 | INFO | [VideoDemand] 加载了 128 个视频类别配置
2025-07-22 01:22:55 | INFO | [YaoyaoPlugin] 私聊黑名单用户: ['gh_a5cf07d474f1', 'gh_ba3381c847b3']
2025-07-22 01:22:55 | INFO | [YaoyaoPlugin] 已加载管理员列表: ['wxid_ubbh6q832tcs21', 'admin-wxid']
2025-07-22 01:22:55 | INFO | [yuanbao] 插件初始化完成
2025-07-22 01:22:55 | DEBUG | [yuanbao] 指令: ['元宝', 'yuanbao']
2025-07-22 01:22:55 | DEBUG | [yuanbao] 绘图模型: gpt_175B_0404
2025-07-22 01:22:55 | DEBUG | [yuanbao] 认证信息加载状态: hy_token=True, hy_user=True, agent_id=True, x_uskey=True
2025-07-22 01:22:55 | SUCCESS | 已加载插件: ['AdminPoint', 'AdminSignInReset', 'AdminWhitelist', 'AISignIn', 'AppMessageTester', 'ArticleForwarder', 'BaiduAgentsPlugin', 'BaiduDraw', 'BotStatus', 'ChatSummary', 'DanceSignInPlugin', 'DeepseekPlugin', 'Doubao', 'DoubaoDrawing', 'DoubaoImageRecognition', 'DouBaoImageToImage', 'DouBaoImageToVideo', 'DoubaoVideoSearch', 'DouHui', 'EmojiTestPlugin', 'FixedQuoteTest', 'GetContact', 'Gomoku', 'GoodMorning', 'HunyuanDraw', 'ImageEcho', 'ImageToImage', 'JiemengDraw', 'KeLingImageToImage', 'KlingAI', 'Leaderboard', 'LuckyDraw', 'ManagePlugin', 'MeituAI', 'Menu', 'MiniProgramTester', 'Music', 'News', 'PatReply', 'PointTrade', 'QuarkSignIn', 'QueryPoint', 'RandomMember', 'RandomPicture', 'RenameReminder', 'RevokePlugin', 'RoboNeo', 'SignIn', 'TempFileManagerPlugin', 'TencentLke', 'TimerTask', 'TongyiDraw', 'URLShortener', 'VideoDemand', 'VideoParserPlugin', 'VideoTest', 'VivoAgentsPlugin', 'VoiceTest', 'Warthunder', 'WeatherQuery', 'WelcomePlugin', 'Xunfei', 'YaoyaoPlugin', 'Yuanbao', '抽签', '造梦次元']
2025-07-22 01:22:55 | INFO | 处理堆积消息中
2025-07-22 01:22:55 | SUCCESS | 处理堆积消息完毕
2025-07-22 01:22:55 | SUCCESS | 开始处理消息
2025-07-22 01:23:19 | DEBUG | 收到消息: {'MsgId': 1988043577, 'FromUserName': {'string': '55878994168@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 1, 'Content': {'string': 'wxid_ubbh6q832tcs21:\n开启美图模式'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1753118610, 'MsgSource': '<msgsource>\n\t<pua>1</pua>\n\t<eggIncluded>1</eggIncluded>\n\t<silence>0</silence>\n\t<membercount>3</membercount>\n\t<signature>N0_V1_20RhOGwn|v1_8bIlA3ul</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'PushContent': '郭 : 开启美图模式', 'NewMsgId': 1461634240179041285, 'MsgSeq': 871389767}
2025-07-22 01:23:19 | INFO | 收到文本消息: 消息ID:1988043577 来自:55878994168@chatroom 发送人:wxid_ubbh6q832tcs21 @:[] 内容:开启美图模式
2025-07-22 01:23:20 | INFO | 发送文字消息: 对方wxid:55878994168@chatroom at: 内容:✅ 已进入美图AI模式，正在初始化...
2025-07-22 01:23:28 | INFO | 发送文字消息: 对方wxid:55878994168@chatroom at: 内容:你好呀！我是你的影像创作小助手RoboNeo~ 无论是修图、做设计还是剪视频，只要告诉我你的想法，我就能帮你轻松实现！今天想创作点什么呢？✨
2025-07-22 01:23:28 | DEBUG | 处理消息内容: '开启美图模式'
2025-07-22 01:23:28 | DEBUG | 消息内容 '开启美图模式' 不匹配任何命令，忽略
2025-07-22 01:23:39 | DEBUG | 收到消息: {'MsgId': 466234390, 'FromUserName': {'string': '55878994168@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 1, 'Content': {'string': 'wxid_ubbh6q832tcs21:\n画一个美女'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1753118630, 'MsgSource': '<msgsource>\n\t<pua>1</pua>\n\t<eggIncluded>1</eggIncluded>\n\t<silence>0</silence>\n\t<membercount>3</membercount>\n\t<signature>N0_V1_NmCXbP83|v1_Tbg5HgKf</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'PushContent': '郭 : 画一个美女', 'NewMsgId': 707928247833139821, 'MsgSeq': 871389772}
2025-07-22 01:23:39 | INFO | 收到文本消息: 消息ID:466234390 来自:55878994168@chatroom 发送人:wxid_ubbh6q832tcs21 @:[] 内容:画一个美女
2025-07-22 01:24:12 | INFO | 发送文字消息: 对方wxid:55878994168@chatroom at: 内容:好的，我来帮你生成一个漂亮的美女图像，让我好好构思一下~我正在构思美女肖像的创意方向，稍等片刻哦~

2025-07-22 01:24:13 | INFO | 发送图片消息: 对方wxid:55878994168@chatroom 图片base64略
2025-07-22 01:24:20 | INFO | 发送文字消息: 对方wxid:55878994168@chatroom at: 内容:为你创作了一张时尚风格的亚洲美女半身像，采用了柔和的蝴蝶光来突出面部特征，精致的妆容和发型搭配简约时尚的服装，背景是虚化的城市夜景，整体呈现高端时尚摄影的感觉。希望你喜欢这个作品！如果对风格或细节有其他想法，可以告诉我继续调整哦~
2025-07-22 01:24:20 | DEBUG | 处理消息内容: '画一个美女'
2025-07-22 01:24:20 | DEBUG | 消息内容 '画一个美女' 不匹配任何命令，忽略
2025-07-22 01:25:44 | DEBUG | 收到消息: {'MsgId': 1714265914, 'FromUserName': {'string': '55878994168@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 1, 'Content': {'string': 'wxid_ubbh6q832tcs21:\n改成汉服风格'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1753118754, 'MsgSource': '<msgsource>\n\t<pua>1</pua>\n\t<eggIncluded>1</eggIncluded>\n\t<silence>0</silence>\n\t<membercount>3</membercount>\n\t<signature>N0_V1_RaZyLV7J|v1_px53jW0D</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'PushContent': '郭 : 改成汉服风格', 'NewMsgId': 7812757812619667829, 'MsgSeq': 871389778}
2025-07-22 01:25:44 | INFO | 收到文本消息: 消息ID:1714265914 来自:55878994168@chatroom 发送人:wxid_ubbh6q832tcs21 @:[] 内容:改成汉服风格
2025-07-22 01:26:14 | INFO | 发送文字消息: 对方wxid:55878994168@chatroom at: 内容:好的，我马上为你把人物服装改成汉服风格，让我好好构思一下~正在将图片中的现代服装改为精致的唐代汉服风格，会保持人物的面部特征和整体构图不变。

2025-07-22 01:26:15 | INFO | 发送图片消息: 对方wxid:55878994168@chatroom 图片base64略
2025-07-22 01:26:21 | INFO | 发送文字消息: 对方wxid:55878994168@chatroom at: 内容:已经将图片成功转换为汉服风格！保留了原图人物的面部特征和表情，服装改为了精致的粉色唐代汉服，包括宽袖和高腰线设计，整体依然保持了高端时尚感。效果很自然，希望你喜欢这个汉服风格的版本！如果还需要其他调整，可以随时告诉我。
2025-07-22 01:26:21 | DEBUG | 处理消息内容: '改成汉服风格'
2025-07-22 01:26:21 | DEBUG | 消息内容 '改成汉服风格' 不匹配任何命令，忽略
2025-07-22 01:26:22 | INFO | 发送文字消息: 对方wxid:55878994168@chatroom at: 内容:格式：改成[新时间][关键词]
例如：改成每周五18:00钻石 或 改成 每周五 18:00 钻石
