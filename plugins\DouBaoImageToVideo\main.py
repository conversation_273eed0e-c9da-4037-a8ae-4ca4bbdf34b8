import os
import json
import asyncio
import time
import random
import base64
import uuid
import hmac
import hashlib
import zlib

from datetime import datetime, timezone
from urllib.parse import urlencode, urlparse
from pathlib import Path
from typing import Optional, Dict
try:
    import tomllib
except ModuleNotFoundError:
    import tomli as tomllib

import httpx
from loguru import logger

from WechatAPI import WechatAPIClient
from utils.decorators import on_text_message, on_quote_message, on_emoji_message
from utils.plugin_base import PluginBase
from utils.temp_file_manager import cleanup_file

# 不再导入外部文件，所有逻辑都在main.py中





class DouBaoImageToVideo(PluginBase):
    description = "豆包AI图生视频功能，支持将图片转换为视频"
    author = "XYBot"
    version = "1.0.0"
    plugin_name = "DouBaoImageToVideo"

    def __init__(self):
        super().__init__()

        # 初始化临时目录
        self.temp_dir = Path("temp/doubao_image_to_video")
        self.temp_dir.mkdir(parents=True, exist_ok=True)

        # 读取配置
        self._load_config()

        # 用户限流管理
        self.user_last_request = {}

        # 表情消息缓存 - 用于支持表情包处理
        self.emoji_message_cache = {}  # 存储 {msg_id: emoji_info}
        # 引用图片命令（将在配置加载后更新）
        self.quote_command = ["豆包视频"]

        # 视频处理配置
        self.video_semaphore = asyncio.Semaphore(2)  # 限制并发视频处理数量



    def _load_config(self):
        """加载配置文件"""
        defaults = {
            "enable": True,
            "command": ["豆包图生视频"],
            "command-format": "豆包图生视频 [提示词] [图片路径]",
            "quote": {"command": ["豆包视频"], "command-format": "引用图片并发送: 豆包视频 [提示词]"},
            "api": {"base_url": "https://www.doubao.com", "api_key": "", "model": "doubao-video-generation"},
            "rate_limit": {"cooldown": 30},
            "natural_response": True
        }

        config = defaults
        try:
            config_path = f"plugins/{self.plugin_name}/config.toml"
            if os.path.exists(config_path):
                with open(config_path, "rb") as f:
                    plugin_config = tomllib.load(f)
                config.update(plugin_config.get(self.plugin_name, {}))
        except Exception as e:
            logger.error(f"[{self.plugin_name}] 配置文件读取失败: {str(e)}")

        # 应用配置
        self.enable = config["enable"]
        self.command = config["command"]
        self.command_format = config["command-format"]
        self.quote_command = config["quote"]["command"]
        self.quote_command_format = config["quote"]["command-format"]
        self.base_url = config["api"]["base_url"]
        self.cookies = config["api"]["api_key"]
        self.model = config["api"]["model"]
        self.cooldown = config["rate_limit"]["cooldown"]
        self.natural_response = config["natural_response"]

        if self.natural_response:
            self._init_natural_responses()

    def _init_natural_responses(self):
        """初始化自然化回复词库"""
        self.confirm_msgs = ["好的", "收到", "明白了", "嗯", "知道了", "行", "OK"]
        self.error_msgs = ["这可把我难住了", "搞不定了", "这个有点难", "弄不了", "出问题了", "搞不出来", "这个不行"]
        self.rate_limit_msgs = ["你搁这刷bug呢", "慢点慢点", "别这么急", "一个一个来", "太快了太快了", "歇会儿", "别刷了"]

    def _check_rate_limit(self, user_wxid: str) -> bool:
        """检查用户是否触发限流"""
        current_time = time.time()
        
        if user_wxid in self.user_last_request:
            time_diff = current_time - self.user_last_request[user_wxid]
            if time_diff < self.cooldown:
                return True
        
        self.user_last_request[user_wxid] = current_time
        return False



    async def _send_usage_instructions(self, bot, wxid, user_wxid):
        """发送使用说明"""
        usage_text = f"🎬 命令格式：{' / '.join(self.command)} [提示词] [图片路径]\n或引用图片后发送：{' / '.join(self.quote_command)} [提示词]"
        await bot.send_at_message(wxid, usage_text, [user_wxid])

    def _parse_cookies(self, cookie_string: str) -> Dict[str, str]:
        """解析Cookie字符串"""
        cookies = {}
        for item in cookie_string.split(';'):
            if '=' in item:
                key, value = item.strip().split('=', 1)
                cookies[key] = value
        return cookies

    def _get_base_headers(self) -> Dict[str, str]:
        """获取基础HTTP请求头"""
        return {
            'User-Agent': 'Mozilla/5.0 (Linux; Android 10; V2002A Build/QP1A.190711.020) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/83.0.4103.106 Mobile Safari/537.36',
            'Accept': '*/*',
            'Accept-Language': 'zh-CN,zh;q=0.9,en-US;q=0.8,en;q=0.7',
            'Accept-Encoding': 'gzip, deflate',
            'Origin': 'https://www.doubao.com',
            'Referer': 'https://www.doubao.com/chat/create-image',
            'X-Requested-With': 'mark.via',
        }

    def _get_api_params(self) -> Dict[str, str]:
        """获取API请求参数"""
        return {
            'aid': '497858',
            'device_id': '7468716989062841895',
            'device_platform': 'web',
            'language': 'zh',
            'pc_version': '2.24.5',
            'pkg_type': 'release_version',
            'real_aid': '497858',
            'region': 'CN',
            'samantha_web': '1',
            'sys_region': 'CN',
            'tea_uuid': '7468716986638386703',
            'use-olympus-account': '1',
            'version_code': '20800',
            'web_id': '7468716986638386703'
        }











    async def _get_upload_auth(self, client: httpx.AsyncClient) -> Dict[str, str]:
        """获取上传认证信息"""
        print("🔑 获取上传认证信息...")

        url = "https://www.doubao.com/alice/resource/prepare_upload"
        params = {
            'version_code': '20800',
            'language': 'zh',
            'device_platform': 'web',
            'aid': '497858',
            'real_aid': '497858',
            'pkg_type': 'release_version',
            'device_id': '7468716989062841895',
            'web_id': '7468716986638386703',
            'tea_uuid': '7468716986638386703',
            'use-olympus-account': '1',
            'region': 'CN',
            'sys_region': 'CN',
            'samantha_web': '1',
            'pc_version': '2.24.2',
        }

        data = {
            "tenant_id": "5",
            "scene_id": "5",
            "resource_type": 2
        }

        try:
            response = await client.post(url, params=params, json=data, timeout=30)
            print(f"🔍 上传认证响应状态码: {response.status_code}")

            if response.status_code == 200:
                result = response.json()
                if result.get('code') == 0:
                    upload_auth = result['data']['upload_auth_token']
                    print("✅ 获取上传认证成功")
                    return upload_auth
                else:
                    print(f"❌ 获取上传认证失败: {result}")
                    return {}
            else:
                print(f"❌ 获取上传认证请求失败: {response.status_code}")
                return {}

        except Exception as e:
            print(f"❌ 获取上传认证时发生错误: {e}")
            return {}

    def _generate_aws_signature(self, method: str, url: str, upload_auth: Dict[str, str], payload: str = "") -> Dict[str, str]:
        """生成AWS签名"""
        try:
            access_key_id = upload_auth['access_key']
            secret_access_key = upload_auth['secret_key']
            session_token = upload_auth['session_token']

            # 解析URL
            parsed_url = urlparse(url)
            host = parsed_url.netloc
            path = parsed_url.path
            query = parsed_url.query

            # 生成时间戳
            now = datetime.now(timezone.utc)
            amz_date = now.strftime('%Y%m%dT%H%M%SZ')
            date_stamp = now.strftime('%Y%m%d')

            # 构建规范请求
            canonical_uri = path if path else '/'
            # 规范化查询字符串
            if query:
                query_params = []
                for param in query.split('&'):
                    if '=' in param:
                        key, value = param.split('=', 1)
                        query_params.append((key, value))
                    else:
                        query_params.append((param, ''))
                query_params.sort()
                canonical_querystring = '&'.join([f'{k}={v}' for k, v in query_params])
            else:
                canonical_querystring = ''
            canonical_headers = f'host:{host}\nx-amz-date:{amz_date}\nx-amz-security-token:{session_token}\n'
            signed_headers = 'host;x-amz-date;x-amz-security-token'
            payload_hash = hashlib.sha256(payload.encode('utf-8')).hexdigest()

            canonical_request = f'{method}\n{canonical_uri}\n{canonical_querystring}\n{canonical_headers}\n{signed_headers}\n{payload_hash}'

            # 构建签名字符串
            algorithm = 'AWS4-HMAC-SHA256'
            credential_scope = f'{date_stamp}/cn-north-1/imagex/aws4_request'
            string_to_sign = f'{algorithm}\n{amz_date}\n{credential_scope}\n{hashlib.sha256(canonical_request.encode()).hexdigest()}'

            # 计算签名
            def sign(key, msg):
                if isinstance(key, str):
                    key = key.encode('utf-8')
                return hmac.new(key, msg.encode('utf-8'), hashlib.sha256).digest()

            def get_signature_key(key, date_stamp, region_name, service_name):
                k_date = sign('AWS4' + key, date_stamp)
                k_region = sign(k_date, region_name)
                k_service = sign(k_region, service_name)
                k_signing = sign(k_service, 'aws4_request')
                return k_signing

            signing_key = get_signature_key(secret_access_key, date_stamp, 'cn-north-1', 'imagex')
            signature = hmac.new(signing_key, string_to_sign.encode('utf-8'), hashlib.sha256).hexdigest()

            # 构建Authorization头
            authorization_header = f'{algorithm} Credential={access_key_id}/{credential_scope}, SignedHeaders={signed_headers}, Signature={signature}'

            return {
                'Authorization': authorization_header,
                'X-Amz-Date': amz_date,
                'x-amz-security-token': session_token
            }

        except Exception as e:
            print(f"❌ 生成AWS签名失败: {e}")
            return {}

    async def _process_video(self, cookies: str, image_path: str, prompt: str) -> Optional[str]:
        """
        完整的图生视频处理流程

        Args:
            cookies: Cookie字符串
            image_path: 本地图片文件路径
            prompt: 视频生成提示词

        Returns:
            生成成功返回视频URL，失败返回None
        """
        if not os.path.exists(image_path):
            return None

        # 创建配置好的httpx客户端
        parsed_cookies = self._parse_cookies(cookies)
        async with httpx.AsyncClient(cookies=parsed_cookies, timeout=300.0) as client:
            # 1. 上传图片
            image_uri = await self._upload_image(client, image_path)
            if not image_uri:
                return None

            # 2. 生成视频
            return await self._generate_video(client, image_uri, prompt)

    async def _upload_image(self, client: httpx.AsyncClient, image_path: str) -> Optional[str]:
        """
        上传图片到豆包服务器

        Args:
            client: 已配置好cookies的httpx客户端
            image_path: 本地图片文件路径

        Returns:
            上传成功返回图片URI，失败返回None
        """
        try:
            # 读取图片文件
            with open(image_path, 'rb') as f:
                image_data = f.read()

            file_size = len(image_data)
            file_ext = os.path.splitext(image_path)[1]
            crc32_value = zlib.crc32(image_data) & 0xffffffff

            print(f"🚀 开始上传图片: {image_path}, 大小: {file_size} 字节, CRC32: {crc32_value:08x}")

            # 1. 获取上传认证信息
            upload_auth = await self._get_upload_auth(client)
            if not upload_auth:
                return None

            # 2. 申请上传权限
            service_id = "a9rns2rl98"
            upload_params = {
                'Action': 'ApplyImageUpload',
                'Version': '2018-08-01',
                'ServiceId': service_id,
                'NeedFallback': 'true',
                'FileSize': str(file_size),
                'FileExtension': file_ext,
                's': 'yy49d6n7o6p'
            }

            apply_url = f"https://imagex.bytedanceapi.com/?{urlencode(upload_params)}"

            # 3. 生成AWS签名
            aws_headers = self._generate_aws_signature('GET', apply_url, upload_auth)

            base_headers = self._get_base_headers()
            headers = base_headers.copy()
            headers.update(aws_headers)

            response = await client.get(apply_url, headers=headers)
            if response.status_code != 200:
                print(f"申请上传失败: {response.status_code}")
                return None

            upload_info = response.json()
            if 'Result' not in upload_info:
                print(f"申请上传响应异常: {upload_info}")
                return None

            result = upload_info['Result']
            store_info = result['UploadAddress']['StoreInfos'][0]
            upload_host = result['UploadAddress']['UploadHosts'][0]

            store_uri = store_info['StoreUri']
            auth_token = store_info['Auth']
            upload_id = store_info['UploadID']

            print(f"✅ 获取上传权限成功: {store_uri}")

            # 4. 上传文件到指定地址
            upload_url = f"https://{upload_host}/upload/v1/{store_uri}"
            upload_headers = {
                'Authorization': auth_token,
                'Content-CRC32': f"{crc32_value:08x}",
                'Content-Type': 'application/octet-stream',
                'X-Storage-U': upload_id,
                'Origin': 'https://www.doubao.com',
                'Referer': 'https://www.doubao.com/chat/',
                'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
            }

            upload_response = await client.post(upload_url, content=image_data, headers=upload_headers)
            if upload_response.status_code != 200:
                print(f"文件上传失败: {upload_response.status_code}")
                return None

            upload_result = upload_response.json()
            if upload_result.get('code') != 2000:
                print(f"上传文件响应异常: {upload_result}")
                return None

            print("✅ 文件上传成功")

            # 5. 提交上传完成
            commit_params = {
                'Action': 'CommitImageUpload',
                'Version': '2018-08-01',
                'ServiceId': service_id
            }

            commit_url = f"https://imagex.bytedanceapi.com/?{urlencode(commit_params)}"

            session_key = result['UploadAddress']['SessionKey']
            commit_payload = json.dumps({"SessionKey": session_key})

            commit_aws_headers = self._generate_aws_signature('POST', commit_url, upload_auth, commit_payload)
            commit_headers = {
                'Content-Type': 'application/json',
                'Accept': '*/*',
                'Origin': 'https://www.doubao.com',
                'Referer': 'https://www.doubao.com/chat/',
                'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
            }
            commit_headers.update(commit_aws_headers)

            commit_response = await client.post(commit_url, content=commit_payload, headers=commit_headers)
            if commit_response.status_code != 200:
                print(f"提交上传完成失败: {commit_response.status_code}")
                return None

            commit_result = commit_response.json()
            if 'Result' not in commit_result:
                print(f"提交上传完成响应异常: {commit_result}")
                return None

            # 获取最终的图片URI
            image_uri = commit_result['Result']['Results'][0]['Uri']
            print(f"✅ 图片上传完成: {image_uri}")
            return image_uri

        except Exception as e:
            print(f"上传图片时发生错误: {e}")
            return None



    async def _generate_video(self, client: httpx.AsyncClient, image_uri: str, prompt: str = "生成视频") -> Optional[str]:
        """
        生成视频

        Args:
            client: 已配置好cookies的httpx客户端
            image_uri: 上传的图片URI
            prompt: 视频生成提示词

        Returns:
            生成成功返回视频URL，失败返回None
        """
        try:
            print(f"🎬 开始生成视频，提示词: {prompt}, 图片URI: {image_uri}")

            # 使用公共方法获取请求头和参数
            base_headers = self._get_base_headers()
            base_params = self._get_api_params()

            # 构建请求数据（基于抓包的图生视频格式）
            local_conversation_id = f"local_{int(time.time() * 1000000)}"
            local_message_id = str(uuid.uuid4())

            request_data = {
                "messages": [{
                    "content": json.dumps({"text": prompt}),
                    "content_type": 2020,  # 图生视频内容类型
                    "attachments": [{
                        "type": "image",
                        "key": image_uri,
                        "identifier": str(uuid.uuid4()),
                        "extra": {
                            "refer_types": "overall"
                        }
                    }]
                }],
                "completion_option": {
                    "is_regen": False,
                    "with_suggest": False,
                    "need_create_conversation": True,
                    "launch_stage": 1,
                    "is_replace": False,
                    "is_delete": False,
                    "message_from": 0,
                    "use_auto_cot": False,
                    "resend_for_regen": False,
                    "event_id": "0"
                },
                "conversation_id": "0",
                "local_conversation_id": local_conversation_id,
                "local_message_id": local_message_id
            }

            # 构建请求头
            headers = base_headers.copy()
            headers.update({
                'Content-Type': 'application/json',
                'x-flow-trace': f"04-{uuid.uuid4().hex[:16]}-{uuid.uuid4().hex[:16]}-01",
                'last-event-id': 'undefined',
                'Agw-Js-Conv': 'str, str',
                'Accept': '*/*',
                'Sec-Fetch-Site': 'same-origin',
                'Sec-Fetch-Mode': 'cors',
                'Sec-Fetch-Dest': 'empty'
            })

            # 发送请求
            api_url = "https://www.doubao.com/samantha/chat/completion"
            print("🚀 发送图生视频请求...")

            async with client.stream('POST', api_url, params=base_params, json=request_data, headers=headers) as response:
                if response.status_code == 200:
                    print("✅ 请求成功！正在解析流式响应...")

                    conversation_id = None
                    count = 0
                    start_time = time.time()

                    async for line in response.aiter_lines():
                        if line.startswith('data: '):
                            count += 1
                            current_time = time.time()
                            elapsed = int(current_time - start_time)

                            try:
                                data_str = line[6:]  # 去掉 'data: ' 前缀
                                if data_str.strip():
                                    event_data = json.loads(data_str)

                                    if 'event_data' in event_data and event_data['event_data']:
                                        inner_data = json.loads(event_data['event_data'])

                                        # 保存conversation_id
                                        if 'conversation_id' in inner_data:
                                            conversation_id = inner_data['conversation_id']
                                            print(f"🔍 [DEBUG] 获取到会话ID: {conversation_id}")

                                        # 检查是否是视频生成消息
                                        if 'message' in inner_data:
                                            message = inner_data['message']
                                            if message.get('content_type') == 2021:
                                                print("🔄 [PROGRESS] 检测到视频生成响应")

                                                # 获取conversation_id用于轮询
                                                conv_id = inner_data.get('conversation_id') or conversation_id
                                                message_id = inner_data.get('message_id')

                                                if conv_id:
                                                    print(f"🔍 [DEBUG] 会话ID: {conv_id}")
                                                    print("🔄 [PROGRESS] 开始轮询消息状态...")

                                                    # 开始轮询消息状态（流式响应中检测到，使用较短延迟）
                                                    video_url = await self._poll_message_status(client, conv_id, message_id, initial_delay=60)
                                                    if video_url:
                                                        print(f"🎬 [SUCCESS] 视频生成完成！总耗时 {elapsed} 秒")
                                                        return video_url
                                                    else:
                                                        print(f"❌ [ERROR] 消息轮询失败")
                                                        return None

                            except json.JSONDecodeError as e:
                                print(f"🔍 [DEBUG] JSON解析失败: {e}")
                            except Exception as e:
                                print(f"🔍 [DEBUG] 处理响应行时出错: {e}")

                            # 超时检查
                            if elapsed > 600:  # 10分钟超时
                                print("⏰ [TIMEOUT] 已达到最大等待时间（10分钟），停止等待")
                                break

                    # 如果流式响应结束但有conversation_id，尝试轮询
                    if conversation_id:
                        print(f"🔄 [PROGRESS] 流式响应结束，使用会话ID开始轮询: {conversation_id}")
                        video_url = await self._poll_message_status(client, conversation_id, None)
                        if video_url:
                            print(f"🎬 [SUCCESS] 视频生成完成！")
                            return video_url

                    print("⚠️ [WARNING] 流式响应结束，但未获取到视频URL")
                    return None
                else:
                    print(f"❌ [ERROR] 请求失败: {response.status_code}")
                    return None

        except Exception as e:
            print(f"❌ [ERROR] 生成视频时发生错误: {e}")
            return None



    async def _poll_message_status(self, client: httpx.AsyncClient, conversation_id: str, message_id: str = None, max_attempts: int = 60, interval: int = 15, initial_delay: int = 180) -> Optional[str]:
        """
        轮询消息状态，检查视频生成进度

        Args:
            client: 已配置好cookies的httpx客户端
            conversation_id: 会话ID
            message_id: 消息ID（可选）
            max_attempts: 最大轮询次数
            interval: 轮询间隔（秒）
            initial_delay: 开始轮询前的等待时间（秒）

        Returns:
            成功返回视频URL，失败返回None
        """
        # 初始延迟，等待视频生成
        await asyncio.sleep(initial_delay)

        # 使用公共方法获取请求头和参数
        base_headers = self._get_base_headers()
        base_headers.update({
            'Accept': 'application/json, text/plain, */*',
            'Content-Type': 'application/json',
            'Agw-Js-Conv': 'str'
        })

        base_params = self._get_api_params()

        # 构建请求数据
        request_data = {
            "conversation_id": conversation_id,
            "cursor": 2,  # 从第2条消息开始（基于抓包数据）
            "batch_size": 50
        }

        api_url = "https://www.doubao.com/alice/message/list"

        for attempt in range(1, max_attempts + 1):
            try:
                response = await client.post(api_url, params=base_params, json=request_data, headers=base_headers)

                if response.status_code == 200:
                    result = response.json()
                    if result.get('code') == 0 and 'data' in result:
                        message_list = result['data'].get('message_list', [])

                        # 检查是否是生成失败的消息 - 与doubao_image_generator.py一致的处理
                        for msg in message_list:
                            if msg.get('content_type') == 1:  # 普通文本消息
                                content = msg.get('content', '{}')
                                try:
                                    content_data = json.loads(content)
                                    text = content_data.get('text', '')
                                    if '生成失败' in text or '请重试' in text:
                                        print(f"❌ [ERROR] 豆包AI返回生成失败: {text}")
                                        return None
                                except json.JSONDecodeError:
                                    pass

                            # 查找视频生成完成的消息 - 与doubao_image_generator.py一致
                            if msg.get('content_type') == 2021:  # 视频生成响应
                                content_str = msg.get('content', '{}')
                                try:
                                    content = json.loads(content_str)
                                    video_status = content.get('video_status')

                                    # 检查是否有视频ID，无论状态如何都尝试获取
                                    video_id = content.get('vid')  # 注意这里是vid，不是video_id
                                    if video_id:
                                        # 直接调用播放信息API
                                        video_url = await self._get_video_play_info(client, video_id)
                                        if video_url:
                                            return video_url

                                    # 如果没有视频ID，根据状态处理
                                    if video_status == 3:  # 生成失败
                                        return None

                                except json.JSONDecodeError:
                                    pass

                        await asyncio.sleep(interval)
                    else:
                        await asyncio.sleep(interval)
                else:
                    await asyncio.sleep(interval)

            except Exception:
                await asyncio.sleep(interval)

        return None



    async def _get_video_play_info(self, client: httpx.AsyncClient, video_id: str) -> Optional[str]:
        """
        获取视频播放信息

        Args:
            client: 已配置好cookies的httpx客户端
            video_id: 视频ID

        Returns:
            成功返回视频URL，失败返回None
        """
        try:
            # 使用公共方法获取请求头和参数
            base_headers = self._get_base_headers()
            base_headers.update({
                'Accept': 'application/json, text/plain, */*',
                'Content-Type': 'application/json',
                'Agw-Js-Conv': 'str'
            })

            base_params = self._get_api_params()
            request_data = {"vid": video_id}
            api_url = "https://www.doubao.com/samantha/video/get_play_info"

            response = await client.post(api_url, params=base_params, json=request_data, headers=base_headers)

            if response.status_code == 200:
                result = response.json()
                if result.get('code') == 0 and 'data' in result:
                    data = result['data']
                    play_infos = data.get('play_infos', [])

                    if play_infos:
                        # 选择最高分辨率的视频
                        best_play_info = max(play_infos, key=lambda x: x.get('width', 0) * x.get('height', 0), default=play_infos[0])
                        # 优先使用main链接，不可用时使用backup链接
                        video_url = best_play_info.get('main') or best_play_info.get('backup')
                        if video_url:
                            return video_url
        except Exception:
            pass

        return None



    async def _video_generation_flow(self, bot: WechatAPIClient, wxid: str, user_wxid: str, prompt: str, image_path: str):
        """完整的视频生成流程"""
        try:
            # 使用信号量控制并发
            async with self.video_semaphore:

                # 硬编码Cookie
                cookies = "d_ticket=c387aa7a25690088d32e0f295a9faa5828090; odin_tt=adc3604237fa735eef5d9b2726da716e7ea5dd617e5184147618251da72231aa77815405b56975c824529748a009f1f85cf25c9d17928d11725b0ef3d9e34d33; uid_tt=71dd29fd50dbf8f1a1d7b3090d765234; sessionid=9b6a807194da06d0c111bb246ff92247; sid_guard=9b6a807194da06d0c111bb246ff92247%7C1750354436%7C5184000%7CMon%2C+18-Aug-2025+17%3A33%3A56+GMT"

                # 发送确认消息
                if self.natural_response:
                    await bot.send_text_message(wxid, random.choice(self.confirm_msgs))

                # 执行完整的视频处理管道：生成->下载->发送
                video_path = None
                try:
                    # 1. 生成视频
                    video_url = await self._process_video(cookies, image_path, prompt)
                    if not video_url:
                        await bot.send_at_message(wxid, "❌ 视频生成失败，请稍后重试", [user_wxid])
                        return

                    # 2. 下载视频
                    video_path = await self._download_video(video_url, "豆包生成视频")
                    if not video_path:
                        await bot.send_at_message(wxid, "❌ 视频下载失败，请稍后重试", [user_wxid])
                        return

                    # 3. 发送视频
                    success = await self._send_video_message(bot, wxid, video_path)
                    if not success:
                        await bot.send_at_message(wxid, "❌ 视频发送失败，请稍后重试", [user_wxid])

                finally:
                    # 清理临时文件
                    if video_path and os.path.exists(video_path):
                        try:
                            cleanup_file(video_path)
                        except Exception as e:
                            logger.warning(f"[{self.plugin_name}] 清理临时文件失败: {e}")

        except Exception as e:
            logger.error(f"[{self.plugin_name}] 视频生成流程失败: {str(e)}")
            await bot.send_at_message(wxid, f"❌ 视频生成失败: {str(e)}", [user_wxid])

    async def _handle_quoted_image_video(self, bot: WechatAPIClient, wxid: str, user_wxid: str, message: dict, prompt: str):
        """处理引用图片的视频生成"""
        try:
            # 获取引用的消息信息
            quote_info = message.get("Quote", {})
            quoted_msg_id = quote_info.get("Msgid") or quote_info.get("NewMsgId")

            if not quoted_msg_id:
                await bot.send_at_message(wxid, "❌ 未找到引用的消息", [user_wxid])
                return

            # 检查被引用消息的类型 (3表示图片消息，47表示表情消息)
            quoted_msg_type = quote_info.get("MsgType")
            logger.info(f"[{self.plugin_name}] 被引用消息类型: {quoted_msg_type}")

            if quoted_msg_type not in [3, 47]:  # 支持图片和表情消息
                await bot.send_at_message(
                    wxid,
                    f"❌ 请引用图片或表情消息 (当前引用消息类型: {quoted_msg_type})",
                    [user_wxid]
                )
                return

            # 检查是否是机器人自己发的图片
            quoted_sender = quote_info.get("SenderWxid") or quote_info.get("FromWxid")
            bot_wxid = getattr(bot, 'wxid', None)

            if quoted_sender == bot_wxid:
                await bot.send_at_message(
                    wxid,
                    "❌ 暂不支持处理机器人发送的图片\n请引用其他用户发送的图片",
                    [user_wxid]
                )
                return

            # 下载引用的图片
            image_path = await self._download_quoted_image_from_quote_info(bot, quote_info)
            if not image_path:
                await bot.send_at_message(wxid, "❌ 下载引用图片失败", [user_wxid])
                return

            # 执行视频生成流程
            await self._video_generation_flow(bot, wxid, user_wxid, prompt, image_path)

        except Exception as e:
            logger.error(f"[{self.plugin_name}] 处理引用图片视频生成失败: {str(e)}")
            await bot.send_at_message(wxid, f"❌ 处理失败: {str(e)}", [user_wxid])

    async def _download_quoted_image_from_quote_info(self, bot: WechatAPIClient, quote_info: dict) -> Optional[str]:
        """从引用信息中下载图片 - 使用与豆包插件相同的逻辑"""
        try:
            quoted_msg_id = quote_info.get("Msgid") or quote_info.get("NewMsgId")
            quoted_msg_type = quote_info.get("MsgType")

            # 处理表情消息引用
            if quoted_msg_type == 47:
                if str(quoted_msg_id) in self.emoji_message_cache:
                    emoji_info = self.emoji_message_cache[str(quoted_msg_id)]
                    return await self._download_emoji_image(emoji_info)
                else:
                    logger.warning(f"[{self.plugin_name}] 未找到表情消息缓存: {quoted_msg_id}")
                    return None

            # 处理图片消息引用 (MsgType 3) - 完全按照豆包图生图插件的方式
            if quoted_msg_type == 3:
                # 从Quote.Content中提取XML
                quote_content = quote_info.get("Content", "")

                if not quote_content:
                    logger.error(f"[{self.plugin_name}] 无法获取引用的图片内容")
                    return None

                # 解析XML内容 - 完全按照豆包插件的逻辑
                import xml.etree.ElementTree as ET
                try:
                    # 解析引用内容XML
                    root = ET.fromstring(quote_content)

                    # 首先尝试直接查找img节点
                    img_node = root.find('.//img')

                    # 如果没有直接的img节点，尝试查找refermsg节点中的content
                    if img_node is None:
                        # 查找refermsg节点
                        refermsg = root.find('.//refermsg')
                        if refermsg is not None and refermsg.find('content') is not None:
                            # 提取refermsg中的content内容
                            content_text = refermsg.find('content').text
                            if content_text:
                                # content中的内容是XML格式，但被HTML编码，需要解码
                                content_text = content_text.replace('&lt;', '<').replace('&gt;', '>')
                                # 尝试解析内部的img标签
                                try:
                                    inner_root = ET.fromstring(content_text)
                                    img_node = inner_root.find('img')
                                except ET.ParseError:
                                    logger.error(f"[{self.plugin_name}] 解析内部XML失败: {content_text[:100]}")

                    # 如果仍然未找到img节点
                    if img_node is None:
                        # 尝试直接从content中提取aeskey和cdnmidimgurl
                        if "aeskey=" in quote_content and "cdnmidimgurl=" in quote_content:
                            aeskey_start = quote_content.find('aeskey="') + 8
                            aeskey_end = quote_content.find('"', aeskey_start)
                            aeskey = quote_content[aeskey_start:aeskey_end]

                            cdnmidimgurl_start = quote_content.find('cdnmidimgurl="') + 14
                            cdnmidimgurl_end = quote_content.find('"', cdnmidimgurl_start)
                            cdnmidimgurl = quote_content[cdnmidimgurl_start:cdnmidimgurl_end]

                        elif "cdnthumbaeskey=" in quote_content and "cdnthumburl=" in quote_content:
                            aeskey_start = quote_content.find('cdnthumbaeskey="') + 16
                            aeskey_end = quote_content.find('"', aeskey_start)
                            aeskey = quote_content[aeskey_start:aeskey_end]

                            cdnmidimgurl_start = quote_content.find('cdnthumburl="') + 13
                            cdnmidimgurl_end = quote_content.find('"', cdnmidimgurl_start)
                            cdnmidimgurl = quote_content[cdnmidimgurl_start:cdnmidimgurl_end]

                        else:
                            logger.error(f"[{self.plugin_name}] 无法从引用消息中提取图片信息")
                            return None
                    else:
                        # 提取aeskey和cdnmidimgurl
                        aeskey = img_node.get('aeskey')
                        cdnmidimgurl = img_node.get('cdnmidimgurl')

                        if not aeskey or not cdnmidimgurl:
                            # 尝试提取缩略图信息
                            aeskey = img_node.get('cdnthumbaeskey')
                            cdnmidimgurl = img_node.get('cdnthumburl')

                    if not aeskey or not cdnmidimgurl:
                        logger.error(f"[{self.plugin_name}] 无法提取图片下载参数")
                        return None

                    # 使用API下载图片 - 完全按照豆包插件的方式
                    image_base64 = await bot.download_image(aeskey, cdnmidimgurl)
                    if not image_base64:
                        logger.error(f"[{self.plugin_name}] 下载图片失败")
                        return None

                    # 保存图片到临时文件
                    image_data = base64.b64decode(image_base64)
                    temp_file = self.temp_dir / f"quoted_image_{int(time.time())}.jpg"
                    with open(temp_file, "wb") as f:
                        f.write(image_data)
                    return str(temp_file)

                except ET.ParseError as e:
                    logger.error(f"[{self.plugin_name}] 解析XML失败: {str(e)}")
                    logger.error(f"[{self.plugin_name}] XML内容: {quote_content[:200]}...")
                    return None

            logger.error(f"[{self.plugin_name}] 不支持的消息类型: {quoted_msg_type}")
            return None

        except Exception as e:
            logger.error(f"[{self.plugin_name}] 下载引用图片失败: {str(e)}")
            return None



    async def _download_emoji_image(self, emoji_info: dict) -> Optional[str]:
        """下载表情包图片"""
        try:
            emoji_url = emoji_info.get('EmojiUrl')
            if not emoji_url:
                self._log_progress("表情包URL为空", "error")
                return None

            # 下载表情包
            async with httpx.AsyncClient(timeout=30.0) as client:
                response = await client.get(emoji_url)
                if response.status_code != 200:
                    self._log_progress(f"下载表情包失败: {response.status_code}", "error")
                    return None

                # 保存表情包文件
                emoji_path = self.temp_dir / f"emoji_{int(time.time())}.bin"
                with open(emoji_path, "wb") as f:
                    f.write(response.content)

                # 转换表情包为图片
                image_path = self._process_emoji_to_image(str(emoji_path))
                # 清理原始表情包文件
                try:
                    os.remove(emoji_path)
                except:
                    pass
                return image_path

        except Exception as e:
            self._handle_exception("下载表情包", e)
            return None

    def _process_emoji_to_image(self, emoji_path: str) -> Optional[str]:
        """将表情包转换为可处理的图片"""
        try:
            # 将.bin文件重命名为.gif
            gif_path = emoji_path.replace('.bin', '.gif')
            if emoji_path != gif_path:
                os.rename(emoji_path, gif_path)
            else:
                gif_path = emoji_path

            # 提取第一帧并转换为jpg
            try:
                from PIL import Image
                with Image.open(gif_path) as img:
                    # 获取第一帧
                    img.seek(0)
                    # 转换为RGB模式（去除透明通道）
                    if img.mode in ('RGBA', 'LA', 'P'):
                        # 创建白色背景
                        background = Image.new('RGB', img.size, (255, 255, 255))
                        if img.mode == 'P':
                            img = img.convert('RGBA')
                        background.paste(img, mask=img.split()[-1] if img.mode in ('RGBA', 'LA') else None)
                        img = background
                    elif img.mode != 'RGB':
                        img = img.convert('RGB')

                    # 保存为JPG
                    jpg_path = gif_path.replace('.gif', '.jpg')
                    img.save(jpg_path, 'JPEG', quality=95)

                    # 清理gif文件
                    try:
                        os.remove(gif_path)
                    except:
                        pass

                    logger.info(f"[{self.plugin_name}] 表情包转换成功: {jpg_path}")
                    return jpg_path

            except ImportError:
                logger.warning(f"[{self.plugin_name}] PIL不可用，无法转换表情包")
                return None
            except Exception as e:
                logger.error(f"[{self.plugin_name}] 表情包转换失败: {str(e)}")
                return None

        except Exception as e:
            logger.error(f"[{self.plugin_name}] 处理表情包失败: {str(e)}")
            return None

    async def _download_video(self, url: str, category: str) -> Optional[str]:
        """下载视频到本地"""
        try:
            # 生成唯一文件名
            timestamp = int(time.time())
            filename = f"{category}_{timestamp}.mp4"
            filepath = self.temp_dir / filename

            headers = {
                'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
                'Accept': '*/*',
                'Accept-Encoding': 'gzip, deflate',
                'Connection': 'keep-alive'
            }

            # 下载视频
            start_time = time.time()

            async with httpx.AsyncClient(timeout=300.0, verify=False) as client:  # 5分钟超时
                response = await client.get(url, headers=headers)

                if response.status_code != 200:
                    logger.error(f"[{self.plugin_name}] 下载视频失败，状态码: {response.status_code}")
                    return None

                content_length = len(response.content)
                duration = time.time() - start_time
                download_speed = content_length / duration / 1024 if duration > 0 else 0  # KB/s

                # 写入文件
                with open(filepath, 'wb') as f:
                    f.write(response.content)

                logger.info(f"[{self.plugin_name}] 视频下载完成: {filepath}, 大小: {content_length/1024/1024:.2f}MB, 速度: {download_speed:.2f}KB/s")

            return str(filepath)

        except Exception as e:
            logger.error(f"[{self.plugin_name}] 下载视频失败: {url}, 类别: {category}, 错误: {str(e)}")
            return None

    async def _send_video_message(self, bot: WechatAPIClient, wxid: str, video_path: str) -> bool:
        """发送视频消息"""
        try:
            # 编码视频数据
            video_base64 = await self._encode_video(video_path)
            if not video_base64:
                logger.error(f"[{self.plugin_name}] 视频编码失败: {video_path}")
                return False

            # 提取视频封面
            cover_base64 = self._extract_first_frame(video_path)
            if not cover_base64:
                logger.warning(f"[{self.plugin_name}] 提取视频封面失败，使用默认封面")
                # 使用默认封面或跳过封面
                cover_base64 = ""

            # 获取视频真实时长 - 使用ffprobe
            video_duration = await self._get_video_duration(video_path)
            if video_duration is None:
                logger.warning(f"[{self.plugin_name}] 无法获取视频时长，使用默认值")
                video_duration = 5  # 豆包生成的视频通常是5秒

            logger.debug(f"[{self.plugin_name}] 视频时长: {video_duration}秒")

            # 发送视频
            max_send_retries = 2
            current_retry = 0
            send_success = False

            while current_retry <= max_send_retries and not send_success:
                try:
                    if current_retry > 0:
                        logger.debug(f"[{self.plugin_name}] 尝试重新发送视频，第{current_retry}次重试")
                        await asyncio.sleep(2)

                    # 首先尝试使用httpx直接发送请求，包含正确的时长信息
                    try:
                        json_param = {
                            "Wxid": bot.wxid,
                            "ToWxid": wxid,
                            "Base64": video_base64,
                            "ImageBase64": cover_base64,
                            "PlayLength": video_duration
                        }

                        logger.debug(f"[{self.plugin_name}] 使用httpx发送视频请求，时长: {video_duration}秒")
                        async with httpx.AsyncClient(timeout=300.0) as client:
                            response = await client.post(
                                f'http://{bot.ip}:{bot.port}/SendVideoMsg',
                                json=json_param,
                                timeout=300.0
                            )
                            json_resp = response.json()

                            if json_resp.get("Success"):
                                data = json_resp.get("Data", {})
                                client_msg_id = data.get("clientMsgId")
                                new_msg_id = data.get("newMsgId")
                                logger.info(f"[{self.plugin_name}] 视频发送成功，clientMsgId={client_msg_id}, newMsgId={new_msg_id}")
                                send_success = True
                            else:
                                error_msg = json_resp.get("Message", "未知错误")
                                logger.warning(f"[{self.plugin_name}] 直接HTTP请求失败: {error_msg}")
                                raise Exception(f"API返回错误: {error_msg}")

                    except Exception as direct_e:
                        logger.debug(f"[{self.plugin_name}] 直接HTTP请求失败，尝试使用API: {direct_e}")
                        # 如果直接请求失败，使用API方法（不包含时长参数）
                        await bot.send_video_message(
                            wxid,
                            video_base64,
                            cover_base64
                        )
                        send_success = True
                        logger.info(f"[{self.plugin_name}] 视频发送成功（使用API方法）")

                except Exception as send_error:
                    current_retry += 1
                    logger.error(f"[{self.plugin_name}] 视频发送失败 (尝试 {current_retry}/{max_send_retries + 1}): {str(send_error)}")

                    if current_retry > max_send_retries:
                        logger.error(f"[{self.plugin_name}] 视频发送最终失败，已达到最大重试次数")
                        return False

            return send_success

        except Exception as e:
            logger.error(f"[{self.plugin_name}] 发送视频消息失败: {str(e)}")
            return False

    async def _get_video_duration(self, video_path: str) -> Optional[int]:
        """获取视频真实时长"""
        try:
            probe_cmd = f'ffprobe -v error -show_entries format=duration -of default=noprint_wrappers=1:nokey=1 "{video_path}"'
            logger.debug(f"[{self.plugin_name}] 执行命令: {probe_cmd}")

            probe_process = await asyncio.create_subprocess_shell(
                probe_cmd,
                stdout=asyncio.subprocess.PIPE,
                stderr=asyncio.subprocess.PIPE
            )
            probe_stdout, probe_stderr = await probe_process.communicate()

            if probe_process.returncode == 0:
                stdout_content = probe_stdout.decode().strip()
                duration = float(stdout_content)
                # 以秒为单位返回时长
                video_duration = int(duration)
                logger.debug(f"[{self.plugin_name}] 视频时长: {video_duration}秒")
                return video_duration
            else:
                stderr_content = probe_stderr.decode()
                logger.debug(f"[{self.plugin_name}] 获取视频时长失败，进程返回码: {probe_process.returncode}, 错误: {stderr_content}")
                return None

        except Exception as e:
            logger.warning(f"[{self.plugin_name}] 获取视频时长失败: {e}")
            return None

    async def _encode_video(self, video_path: str) -> Optional[str]:
        """编码视频为base64"""
        try:
            file_size = os.path.getsize(video_path)

            # 检查文件大小限制 (100MB)
            max_size = 100 * 1024 * 1024
            if file_size > max_size:
                logger.error(f"[{self.plugin_name}] 视频文件过大: {file_size / (1024*1024):.2f}MB > {max_size / (1024*1024):.2f}MB")
                return None

            # 使用分块读取和编码，避免内存问题
            logger.debug(f"[{self.plugin_name}] 开始编码视频: {video_path}，大小: {file_size / (1024*1024):.2f}MB")
            start_time = time.time()

            # 读取文件并编码
            with open(video_path, 'rb') as f:
                video_data = f.read()

            base64_data = base64.b64encode(video_data).decode('utf-8')

            duration = time.time() - start_time
            logger.debug(f"[{self.plugin_name}] 视频编码完成，耗时: {duration:.2f}秒")

            return base64_data

        except MemoryError:
            logger.error(f"[{self.plugin_name}] 视频编码内存不足: {video_path}, 大小: {os.path.getsize(video_path) / (1024*1024):.2f}MB")
            return None
        except Exception as e:
            logger.error(f"[{self.plugin_name}] 视频编码失败: {video_path}, 错误: {str(e)}")
            return None

    def _extract_first_frame(self, video_path: str) -> Optional[str]:
        """从视频中提取第一帧并转换为base64"""
        try:
            logger.debug(f"[{self.plugin_name}] 尝试提取视频首帧: {video_path}")

            # 使用ffmpeg提取第一帧
            try:
                temp_img_path = f"{video_path}.thumb.jpg"

                # 使用ffmpeg提取第一帧并保存为jpg
                extract_cmd = f'ffmpeg -i "{video_path}" -vframes 1 -q:v 2 -y "{temp_img_path}"'
                logger.debug(f"[{self.plugin_name}] 执行首帧提取命令: {extract_cmd}")

                extract_process = os.system(extract_cmd)

                if extract_process == 0 and os.path.exists(temp_img_path):
                    # 读取提取的图片并转换为base64
                    with open(temp_img_path, 'rb') as f:
                        img_data = f.read()

                    base64_data = base64.b64encode(img_data).decode('utf-8')

                    # 清理临时图片文件
                    try:
                        os.remove(temp_img_path)
                    except:
                        pass

                    logger.debug(f"[{self.plugin_name}] 视频首帧提取成功")
                    return base64_data
                else:
                    logger.warning(f"[{self.plugin_name}] ffmpeg提取首帧失败")

            except Exception as ffmpeg_error:
                logger.warning(f"[{self.plugin_name}] ffmpeg提取首帧异常: {ffmpeg_error}")

            # 如果ffmpeg失败，尝试使用PIL（如果可用）
            try:
                from PIL import Image
                import cv2

                # 使用OpenCV读取视频第一帧
                cap = cv2.VideoCapture(video_path)
                ret, frame = cap.read()
                cap.release()

                if ret:
                    # 转换为PIL Image
                    frame_rgb = cv2.cvtColor(frame, cv2.COLOR_BGR2RGB)
                    pil_image = Image.fromarray(frame_rgb)

                    # 保存为临时文件
                    temp_img_path = f"{video_path}.thumb.jpg"
                    pil_image.save(temp_img_path, 'JPEG', quality=85)

                    # 读取并转换为base64
                    with open(temp_img_path, 'rb') as f:
                        img_data = f.read()

                    base64_data = base64.b64encode(img_data).decode('utf-8')

                    # 清理临时文件
                    try:
                        os.remove(temp_img_path)
                    except:
                        pass

                    logger.debug(f"[{self.plugin_name}] 使用OpenCV提取首帧成功")
                    return base64_data

            except ImportError:
                logger.debug(f"[{self.plugin_name}] PIL或OpenCV不可用，跳过首帧提取")
            except Exception as cv_error:
                logger.warning(f"[{self.plugin_name}] OpenCV提取首帧失败: {cv_error}")

            logger.warning(f"[{self.plugin_name}] 所有首帧提取方法都失败")
            return None

        except Exception as e:
            logger.error(f"[{self.plugin_name}] 提取视频首帧时发生错误: {str(e)}")
            return None



    async def _process_text_command(self, bot: WechatAPIClient, wxid: str, user_wxid: str, command_parts: list):
        """处理文本命令"""
        # 处理测试命令
        if command_parts[0] in ["豆包视频测试"]:
            default_image_path = "C:\\DBE25C6475AF6852691B040206E94167.jpg"

            if len(command_parts) == 1:
                prompt, image_path = "让这张图片动起来", default_image_path
            elif len(command_parts) == 2:
                prompt, image_path = command_parts[1].strip(), default_image_path
            else:
                prompt, image_path = command_parts[1].strip(), command_parts[2].strip()

            if not os.path.exists(image_path):
                await bot.send_at_message(wxid, f"❌ 图片不存在: {image_path}\n请确认测试图片路径是否正确", [user_wxid])
                await self._send_usage_instructions(bot, wxid, user_wxid)
                return

            await self._video_generation_flow(bot, wxid, user_wxid, prompt, image_path)
            return

        # 标准命令处理
        if len(command_parts) < 3:
            await bot.send_at_message(wxid, f"❌ 命令格式错误，正确格式: {self.command_format}", [user_wxid])
            await self._send_usage_instructions(bot, wxid, user_wxid)
            return

        prompt, image_path = command_parts[1].strip(), command_parts[2].strip()

        if not os.path.exists(image_path):
            await bot.send_at_message(wxid, f"❌ 图片不存在: {image_path}", [user_wxid])
            await self._send_usage_instructions(bot, wxid, user_wxid)
            return

        await self._video_generation_flow(bot, wxid, user_wxid, prompt, image_path)





    @on_text_message
    async def handle_text(self, bot: WechatAPIClient, message: dict):
        """处理文本消息"""
        if not self.enable:
            return

        content = str(message["Content"]).strip()
        wxid = message["FromWxid"]
        user_wxid = message["SenderWxid"]

        # 检查是否是帮助命令
        if content in ["豆包图生视频帮助", "豆包视频帮助", "豆包图生视频说明", "豆包视频说明"]:
            await self._send_usage_instructions(bot, wxid, user_wxid)
            return

        # 检查是否是插件命令
        command_parts = content.split(" ", 2)  # 分割成三部分：命令、提示词、图片路径

        # 标准图生视频命令处理
        if command_parts[0] in self.command:
            # 检查限流
            if self._check_rate_limit(user_wxid):
                if self.natural_response:
                    rate_limit_msg = random.choice(self.rate_limit_msgs)
                    await bot.send_text_message(wxid, rate_limit_msg)
                else:
                    await bot.send_at_message(wxid, "⏳ 请求太频繁，请稍后再试", [user_wxid])
                return

            try:
                await self._process_text_command(bot, wxid, user_wxid, command_parts)
            except Exception as e:
                logger.error(f"[{self.plugin_name}] 处理失败: {str(e)}")
                await bot.send_at_message(wxid, f"❌ 处理失败: {str(e)}", [user_wxid])
            return

    @on_quote_message
    async def handle_quote(self, bot: WechatAPIClient, message: dict):
        """处理引用消息"""
        if not self.enable:
            return

        content = str(message["Content"]).strip()
        wxid = message["FromWxid"]
        user_wxid = message["SenderWxid"]

        # 解析引用命令
        command_parts = content.split(" ", 1)
        if len(command_parts) == 0 or command_parts[0] not in self.quote_command:
            return

        # 获取提示词
        prompt = "让图片动起来" if len(command_parts) == 1 else command_parts[1].strip()[:50]

        # 检查限流
        if self._check_rate_limit(user_wxid):
            if self.natural_response:
                rate_limit_msg = random.choice(self.rate_limit_msgs)
                await bot.send_text_message(wxid, rate_limit_msg)
            else:
                await bot.send_at_message(wxid, "⏳ 请求太频繁，请稍后再试", [user_wxid])
            return

        try:
            await self._handle_quoted_image_video(bot, wxid, user_wxid, message, prompt)
        except Exception as e:
            logger.error(f"[{self.plugin_name}] 引用处理失败: {str(e)}")
            await bot.send_at_message(wxid, f"❌ 处理失败: {str(e)}", [user_wxid])

    @on_emoji_message
    async def handle_emoji(self, bot: WechatAPIClient, message: dict):
        """处理表情消息，缓存表情信息以支持后续的引用处理"""
        if not self.enable:
            return

        try:
            msg_id = message.get('NewMsgId') or message.get('MsgId')
            if not msg_id:
                return

            # 提取表情信息
            emoji_info = {
                'EmojiUrl': message.get('EmojiUrl'),
                'EmojiMD5': message.get('EmojiMD5'),
                'EmojiLen': message.get('EmojiLen'),
                'Content': message.get('Content', ''),
                'CreateTime': message.get('CreateTime'),
                'SenderWxid': message.get('SenderWxid')
            }

            # 从Content中提取aeskey
            content = message.get('Content', '')
            if 'aeskey=' in content:
                try:
                    aeskey_start = content.find('aeskey="') + 8
                    aeskey_end = content.find('"', aeskey_start)
                    if aeskey_start > 7 and aeskey_end > aeskey_start:
                        emoji_info['aeskey'] = content[aeskey_start:aeskey_end]
                except Exception:
                    pass

            # 缓存表情信息
            self.emoji_message_cache[str(msg_id)] = emoji_info

            # 限制缓存大小，避免内存泄漏
            if len(self.emoji_message_cache) > 1000:
                # 删除最旧的100个缓存项
                oldest_keys = list(self.emoji_message_cache.keys())[:100]
                for key in oldest_keys:
                    del self.emoji_message_cache[key]

        except Exception as e:
            logger.error(f"[{self.plugin_name}] 处理表情消息缓存时出错: {str(e)}")


