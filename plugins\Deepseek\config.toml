[Deepseek]
enable = true
natural_response = true  # 启用自然化响应，避免机械化回复
command = ["deepseek", "ds"]
update_command = ["更新deepseek", "更新ds"]  # 更新配置命令（仅管理员可用）
command-format = """
🤖 Deepseek AI助手使用说明:
- deepseek <问题>  # 向AI提问
- ds <问题>       # 简写命令

🔧 管理员命令:
- 更新deepseek uuid=xxx uuc-token=xxx  # 更新API凭证
"""

# API配置
api-url = "https://chatgpt.vivo.com.cn/chatgpt-api/chat/public/completions"
timestamp = "1748416585"
uid = "2d3ae4195eb9de657bdb48a383551d1c"
nonce = "95e84518"
signature = "9z3XhR40NhPebsPdxYbQiy6uPZ27XRM9168OwdUEZtA="
app-id = "4483725646"

# API凭证
uuid = "WXKd06ya8gAUEq1k8Yu0eyc5fI88YukLkh45GnxXNWG87HeZ4m1Awg%3D%3D"
uuc-token = "QW43rBLDKKya7GBbdZIUlVuKzdovFuarm543pzavHMnXN%2BxDe6cZjPYUywmVjsxp"