# XYBotV2 配置文件

XYBotV2 有两个主要配置文件需要修改：

- `main_config.toml`：主配置文件
- `plugins/all_in_one_config.toml`：插件配置文件

## 不同系统下的配置文件修改方法

### Windows 系统

1. 直接使用记事本或其他文本编辑器（推荐使用 VSCode、Sublime Text 等）打开配置文件：

```bash
# 主配置文件位置
XYBotV2/main_config.toml

# 插件配置文件位置
XYBotV2/plugins/all_in_one_config.toml
```

### Linux 系统

1. 使用命令行文本编辑器（如 vim、nano）编辑：

```bash
# 使用 vim 编辑
vim main_config.toml
vim plugins/all_in_one_config.toml

# 或使用 nano 编辑
nano main_config.toml
nano plugins/all_in_one_config.toml
```

2. 也可以使用图形界面编辑器（如果是桌面环境）：

```bash
# 使用 gedit（GNOME）
gedit main_config.toml
gedit plugins/all_in_one_config.toml

# 使用 kate（KDE）
kate main_config.toml
kate plugins/all_in_one_config.toml
```

### Docker 环境

1. 首先找到数据卷位置：

```bash
# 查看数据卷位置
docker volume inspect xybotv2
```

2. 进入数据卷目录编辑配置文件：

```bash
# 配置文件通常位于：
xybotv2-volumes-dir/_data/main_config.toml
xybotv2-volumes-dir/_data/plugins/all_in_one_config.toml
```

3. 修改后重启容器使配置生效：

```bash
docker-compose restart xybotv2
```

## 配置文件修改后生效方式

1. 主配置文件（`main_config.toml`）修改后：

- 需要重启机器人才能生效
- Windows/Linux：按 `Ctrl+C` 停止后重新运行 `python main.py`
- Docker：执行 `docker-compose restart xybotv2`

2. 插件配置文件（`plugins/all_in_one_config.toml`）修改后：

- 可以使用热重载命令，无需重启机器人
- 在聊天中发送以下命令之一（需要机器人管理员权限）：
    - `重载插件 插件名`
    - `重载所有插件`
- 也可以重启机器人来生效

## 注意事项

1. 确保配置文件格式正确：

- 使用 `UTF-8` 编码
- 遵循 `TOML` 格式规范
- 修改后检查是否有语法错误

2. 权限问题：

- Linux/Docker 环境下确保有正确的读写权限
- 如遇权限问题，可使用 sudo 或调整文件权限：

```bash
sudo chmod 644 main_config.toml
sudo chmod 644 plugins/all_in_one_config.toml
```

3. 管理员权限说明：

- 可在主配置文件中设置管理员

4. Docker 环境特别说明：

- 配置文件位于数据卷中，修改后会持久保存
- 重建容器不会影响配置文件
- 确保数据卷正确挂载

## 配置说明

# main_config.toml 配置说明

```toml
[WechatAPIServer]
port = 9000                # WechatAPI服务器端口，默认9000，如有冲突可修改
mode = "release"           # 运行模式：release(生产环境)，debug(调试模式)
redis-host = "127.0.0.1"   # Redis服务器地址，本地使用127.0.0.1
redis-port = 6379          # Redis端口，默认6379
redis-password = ""        # Redis密码，如果有设置密码则填写
redis-db = 0               # Redis数据库编号，默认0

# XYBot 核心设置
[XYBot]
version = "v1.0.0"                    # 版本号，请勿修改
ignore-protection = false             # 是否忽略风控保护机制，建议保持false
database-url = "sqlite:///xybot.db"   # SQLite数据库地址，一般无需修改

# 管理员设置
admins = ["admin-wxid", "admin-wxid"]  # 管理员的wxid列表，可从消息日志中获取
disabled-plugins = ["ExamplePlugin"]   # 禁用的插件列表，不需要的插件名称填在这里
timezone = "Asia/Shanghai"             # 时区设置，中国用户使用 Asia/Shanghai

# 实验性功能，如果main_config.toml配置改动，或者plugins文件夹有改动，自动重启。可以在开发时使用，不建议在生产环境使用。
auto-restart = false                 # 仅建议在开发时启用，生产环境保持false

# 消息过滤设置
ignore-mode = "Whitelist"
# 消息处理模式：
# "None" - 处理所有消息
# "Whitelist" - 仅处理白名单消息
# "Blacklist" - 屏蔽黑名单消息

whitelist = [# 白名单列表
    "wxid_1", # 个人用户微信ID
    "wxid_2",
    "chatroom@111", # 群聊ID
    "chatroom@222"
]

blacklist = [# 黑名单列表
    "wxid_3", # 个人用户微信ID
    "wxid_4",
    "chatroom@333", # 群聊ID
    "chatroom@444"
]


# OpenAI格式API设置
[OpenAI]
api-key = "sk-xxxx"                    # OpenAI格式API密钥，必须填写有效的API Key
base-url = "https://api.openai.com/v1" # API接口地址
# 使用OpanAI官方API填写 https://api.openai.com/v1
# 使用其他API或者代理API需要修改为对应地址
```

## 说明

1. **管理员设置**
    - 管理员ID获取方法：
        1. 先启动机器人
        2. 私聊机器人任意消息
        3. 在日志中找到自己的 `wxid`

2. **消息过滤模式**
    - `None` 模式：处理所有消息
    - `Whitelist` 模式：仅处理白名单中的用户/群消息
    - `Blacklist` 模式：屏蔽黑名单中的用户/群消息

3. **数据安全**
    - 建议定期备份数据库文件(`xybot.db`)
    - 请勿泄露配置文件中的敏感信息（如 `API` 密钥）

# plugins/all_in_one_config.toml 配置说明

以下是需要额外配置的插件，其他插件可以不配置。

## Ai插件 [Ai]

### 基础设置

```toml
[Ai]
enable = true              # 总开关
database-url = "resource/ai.db"  # AI数据库路径
enable-command = true      # 是否开启指令调用
enable-at = true          # 是否响应@机器人
enable-private = true     # 是否处理私聊消息
```

### 积分设置 [Ai.Point]

```toml
[Ai.Point]
mode = "None"             # 积分模式："None"-不扣积分, "Together"-统一扣除
price = 5                 # Together模式下每次请求扣除的积分
admin-ignore = true       # 管理员是否免费使用
whitelist-ignore = true   # 白名单是否免费使用
```

### 主模型设置 [Ai.MainModel]

```toml
[Ai.MainModel]
base-url = ""           # API地址，留空则使用主配置
api-key = ""            # API密钥，留空则使用主配置
model-name = ""         # 模型名称

# 输入支持
text-input = true       # 是否支持文本输入
image-input = false     # 是否支持图片输入
image-formats = ["jpg", "jpeg", "png", "gif"]  # 支持的图片格式
voice-input = "None"    # 语音输入："None"-不支持, "Native"-原生支持, "NonNative"-需用语音转文字

# 输出支持
text-output = true      # 是否支持文本输出
image-output = false    # 是否支持图片输出
voice-output = "None"   # 语音输出："None"-不支持, "Native"-原生支持, "NonNative"-需用TTS

# 模型参数
temperature = 0.7       # 温度参数
max-history-messages = 5  # 最大历史消息数

# 提示词
prompt = """你是一个叫XYBot友好的微信AI机器人。请用简洁、专业的方式回答问题。
- 回答不能以换行符开头
- 回答要用纯文本，不能使用Markdown语法
- 用户用什么语言问，就用什么语言回答
- 如果用户问了关于微信机器人的问题，例如：怎么获取积分？怎么使用ChatGPT功能？，统一回答请查阅菜单中的帮助
- 对于代码相关问题，请提供清晰的示例和解释"""
```

### 生成图片设置 [Ai.GenerateImage]

```toml
# 接口需要是OpenAI API格式
base-url = ""   # API地址，留空则使用主配置
api-key = ""    # API密钥，留空则使用主配置

# 模型名
model-name = ""

size = "1024x1024"     # 生成的图片的大小
additional-param = { } # 额外自定义设置

# 开启图片生成后，会自动添加到提示词末尾
add-prompt = "\n- 您可以使用GenerateImage函数在适当的时候生成图像。当用户请求图像时，请确保使用此功能。"
```

### 语音转文字 [Ai.SpeechToText]

```toml
# 接口需要是OpenAI API格式
base-url = ""   # API地址，留空则使用主配置
api-key = ""    # API密钥，留空则使用主配置

# 模型名
model-name = ""
```

### 文字转语音 [Ai.TextToSpeech]

```toml
# 接口需要是OpenAI API格式
base-url = ""   # API地址，留空则使用主配置
api-key = ""    # API密钥，留空则使用主配置

# 模型名
model-name = ""

voice = "" # 音色信息，可查阅服务商文档查看模型支持的音色
speed = "1.0" # 语速
additional-param = { } # 额外自定义设置
```

## 获取天气 [GetWeather]

用于查询城市天气信息的功能模块。

```toml
enable = true      # 是否启用此功能
command-format = """⚙️获取天气：    # 支持的命令格式说明
天气 城市名
天气城市名
城市名天气
城市名 天气"""
api-key = "api-key"    # 和风天气API密钥
# 申请方法：
# 1. 访问 https://dev.qweather.com/
# 2. 注册账号并选择免费订阅
# 3. 获取 Private KEY（注意不是 Public ID）
```

