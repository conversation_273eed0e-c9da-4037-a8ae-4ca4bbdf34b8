2025-07-22 02:08:49 | SUCCESS | 读取主设置成功
2025-07-22 02:08:49 | DEBUG | 最终使用的 Redis 主机地址: 127.0.0.1
2025-07-22 02:08:49 | INFO | 2025/07/22 02:08:49 GetRedisAddr: 127.0.0.1:6379
2025-07-22 02:08:49 | INFO | 2025/07/22 02:08:49 Redis连接成功 | 地址:127.0.0.1:6379 DB:0
2025-07-22 02:08:49 | INFO | 2025/07/22 02:08:49 Server start at :9000
2025-07-22 02:08:49 | SUCCESS | WechatAPI服务已启动
2025-07-22 02:08:50 | INFO | 登录账号信息: wxid: wxid_4usgcju5ey9q29  昵称: 瑶瑶  微信号: Today-KFC  手机号: None
2025-07-22 02:08:50 | INFO | 登录设备信息: device_name: <PERSON>'s Pad  device_id: 49d050a7e94d1ecc40790d74b1c4369f
2025-07-22 02:08:50 | SUCCESS | 登录成功
2025-07-22 02:08:50 | SUCCESS | 已开启自动心跳
2025-07-22 02:08:50 | INFO | 成功加载表情映射文件，共 521 条记录
2025-07-22 02:08:50 | SUCCESS | 数据库初始化成功
2025-07-22 02:08:50 | SUCCESS | 定时任务已启动
2025-07-22 02:08:50 | SUCCESS | 插件 AISignIn 已加载定时任务: {'plugins.AISignIn.main.AISignIn.auto_signin_task'}
2025-07-22 02:08:50 | INFO | 已加载管理员列表: ['wxid_ubbh6q832tcs21', 'admin-wxid']
2025-07-22 02:08:51 | INFO | 播客API初始化成功
2025-07-22 02:08:51 | INFO | 加载配置完成，转换后的数据: {'gh_7057516c9e71': {'name': '唱舞全明星', 'groups': ['27852221909@chatroom', '55878994168@chatroom', '51891329927@chatroom']}, 'gh_d4d21a0e4623': {'name': '两颗桃', 'groups': ['48097389945@chatroom']}, 'gh_410ff2fa6ec9': {'name': 'NGC660 Ai研究院', 'groups': ['47442567074@chatroom']}, 'gh_f78c53c507c0': {'name': '不正常吐槽君', 'groups': ['48097389945@chatroom']}, 'gh_ba3381c847b3': {'name': '唱舞星计划', 'groups': ['51891329927@chatroom']}, 'gh_1a102fb4cad9': {'name': '我在娱乐圈的唯一人脉', 'groups': ['48097389945@chatroom', '27852221909@chatroom']}}
2025-07-22 02:08:51 | INFO | ArticleForwarder插件初始化完成 - 监控配置: {'gh_7057516c9e71': {'name': '唱舞全明星', 'groups': ['27852221909@chatroom', '55878994168@chatroom', '51891329927@chatroom']}, 'gh_d4d21a0e4623': {'name': '两颗桃', 'groups': ['48097389945@chatroom']}, 'gh_410ff2fa6ec9': {'name': 'NGC660 Ai研究院', 'groups': ['47442567074@chatroom']}, 'gh_f78c53c507c0': {'name': '不正常吐槽君', 'groups': ['48097389945@chatroom']}, 'gh_ba3381c847b3': {'name': '唱舞星计划', 'groups': ['51891329927@chatroom']}, 'gh_1a102fb4cad9': {'name': '我在娱乐圈的唯一人脉', 'groups': ['48097389945@chatroom', '27852221909@chatroom']}}
2025-07-22 02:08:51 | DEBUG | [TempFileManager] 添加清理规则: default
2025-07-22 02:08:51 | DEBUG | [TempFileManager] 添加清理规则: images
2025-07-22 02:08:51 | DEBUG | [TempFileManager] 添加清理规则: videos
2025-07-22 02:08:51 | DEBUG | [TempFileManager] 添加清理规则: audio
2025-07-22 02:08:51 | DEBUG | [TempFileManager] 添加清理规则: temp
2025-07-22 02:08:51 | DEBUG | [TempFileManager] 添加清理规则: large_files
2025-07-22 02:08:51 | INFO | [TempFileManager] 临时文件清理任务已启动
2025-07-22 02:08:51 | INFO | [ChatSummary] 数据库初始化成功
2025-07-22 02:08:52 | INFO | 成功加载表情映射文件，共 521 条记录
2025-07-22 02:08:52 | SUCCESS | 插件 GoodMorning 已加载定时任务: {'plugins.GoodMorning.main.GoodMorning.daily_task'}
2025-07-22 02:08:52 | SUCCESS | 插件 News 已加载定时任务: {'plugins.News.main.News.night_news', 'plugins.News.main.News.noon_news'}
2025-07-22 02:08:52 | INFO | [PatReply] AMR文件已存在且是最新的: C:\XYBotV2\data\paiyipai\1_BFB5258B-2B13-4CF4-A40C-5BCCE74E5414_converted.amr
2025-07-22 02:08:52 | DEBUG | [PatReply] 从缓存加载base64: 18228 字符
2025-07-22 02:08:52 | SUCCESS | 插件 QuarkSignIn 已加载定时任务: {'plugins.QuarkSignIn.main.QuarkSignIn.auto_signin_task'}
2025-07-22 02:08:52 | INFO | [QuarkSignIn] 已设置自动签到时间: 08:00
2025-07-22 02:08:52 | INFO | 插件状态: 启用, 检查间隔: 3600秒, 最大重试次数: 3, 重试延迟: 5秒
2025-07-22 02:08:52 | INFO | 已加载管理员列表: ['wxid_ubbh6q832tcs21', 'admin-wxid']
2025-07-22 02:08:52 | INFO | 已加载 2 个改名监控群和 1 个退群监控群
2025-07-22 02:08:52 | INFO | [RenameReminder] 开始启用插件...
2025-07-22 02:08:52 | SUCCESS | 插件 RenameReminder 已加载定时任务: {'plugins.RenameReminder.main.RenameReminder.check_name_changes'}
2025-07-22 02:08:52 | INFO | [RenameReminder] 插件已启用，开始初始化...
2025-07-22 02:08:52 | INFO | [RenameReminder] 支持的命令: 开启改名提醒, 关闭改名提醒, 开启退群提醒, 关闭退群提醒, 查看提醒状态
2025-07-22 02:08:52 | INFO | 已设置检查间隔为 3600 秒
2025-07-22 02:08:52 | INFO | 已加载 2 个监控群，开始更新成员列表
2025-07-22 02:08:52 | DEBUG | 已更新群 51891329927@chatroom 的成员列表
2025-07-22 02:08:53 | DEBUG | 已更新群 27852221909@chatroom 的成员列表
2025-07-22 02:08:53 | SUCCESS | 插件 TimerTask 已加载定时任务: {'plugins.TimerTask.main.TimerTask.check_tasks'}
2025-07-22 02:08:53 | INFO | [VideoDemand] 加载了 128 个视频类别配置
2025-07-22 02:08:54 | INFO | [YaoyaoPlugin] 私聊黑名单用户: ['gh_a5cf07d474f1', 'gh_ba3381c847b3']
2025-07-22 02:08:54 | INFO | [YaoyaoPlugin] 已加载管理员列表: ['wxid_ubbh6q832tcs21', 'admin-wxid']
2025-07-22 02:08:54 | INFO | [yuanbao] 插件初始化完成
2025-07-22 02:08:54 | DEBUG | [yuanbao] 指令: ['元宝', 'yuanbao']
2025-07-22 02:08:54 | DEBUG | [yuanbao] 绘图模型: gpt_175B_0404
2025-07-22 02:08:54 | DEBUG | [yuanbao] 认证信息加载状态: hy_token=True, hy_user=True, agent_id=True, x_uskey=True
2025-07-22 02:08:54 | SUCCESS | 已加载插件: ['AdminPoint', 'AdminSignInReset', 'AdminWhitelist', 'AISignIn', 'AppMessageTester', 'ArticleForwarder', 'BaiduAgentsPlugin', 'BaiduDraw', 'BotStatus', 'ChatSummary', 'DanceSignInPlugin', 'DeepseekPlugin', 'Doubao', 'DoubaoDrawing', 'DoubaoImageRecognition', 'DouBaoImageToImage', 'DouBaoImageToVideo', 'DoubaoVideoSearch', 'DouHui', 'EmojiTestPlugin', 'FixedQuoteTest', 'GetContact', 'Gomoku', 'GoodMorning', 'HunyuanDraw', 'ImageEcho', 'ImageToImage', 'JiemengDraw', 'KeLingImageToImage', 'KlingAI', 'Leaderboard', 'LuckyDraw', 'ManagePlugin', 'MeituAI', 'Menu', 'MiniProgramTester', 'Music', 'News', 'PatReply', 'PointTrade', 'QuarkSignIn', 'QueryPoint', 'RandomMember', 'RandomPicture', 'RenameReminder', 'RevokePlugin', 'RoboNeo', 'SignIn', 'TempFileManagerPlugin', 'TencentLke', 'TimerTask', 'TongyiDraw', 'URLShortener', 'VideoDemand', 'VideoParserPlugin', 'VideoTest', 'VivoAgentsPlugin', 'VoiceTest', 'Warthunder', 'WeatherQuery', 'WelcomePlugin', 'Xunfei', 'YaoyaoPlugin', 'Yuanbao', '抽签', '造梦次元']
2025-07-22 02:08:54 | INFO | 处理堆积消息中
2025-07-22 02:08:54 | SUCCESS | 处理堆积消息完毕
2025-07-22 02:08:54 | SUCCESS | 开始处理消息
2025-07-22 02:09:08 | DEBUG | 收到消息: {'MsgId': 1771864872, 'FromUserName': {'string': '55878994168@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 1, 'Content': {'string': 'wxid_ubbh6q832tcs21:\n开启美图模式'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1753121359, 'MsgSource': '<msgsource>\n\t<pua>1</pua>\n\t<eggIncluded>1</eggIncluded>\n\t<silence>0</silence>\n\t<membercount>3</membercount>\n\t<signature>N0_V1_WoqqEgqq|v1_cuj3oTss</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'PushContent': '郭 : 开启美图模式', 'NewMsgId': 3692719322617340372, 'MsgSeq': 871389823}
2025-07-22 02:09:08 | INFO | 收到文本消息: 消息ID:1771864872 来自:55878994168@chatroom 发送人:wxid_ubbh6q832tcs21 @:[] 内容:开启美图模式
2025-07-22 02:09:09 | INFO | 发送文字消息: 对方wxid:55878994168@chatroom at: 内容:✅ 已进入美图AI模式，正在初始化...
2025-07-22 02:09:17 | INFO | 发送文字消息: 对方wxid:55878994168@chatroom at: 内容:你好呀！我是你的影像创作小助手RoboNeo~ 无论是修图、做设计还是剪视频，只要告诉我你的想法，我就能帮你轻松实现！今天想创作点什么呢？✨
2025-07-22 02:09:17 | DEBUG | 处理消息内容: '开启美图模式'
2025-07-22 02:09:17 | DEBUG | 消息内容 '开启美图模式' 不匹配任何命令，忽略
2025-07-22 02:09:23 | DEBUG | 收到消息: {'MsgId': 2122918454, 'FromUserName': {'string': '55878994168@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 1, 'Content': {'string': 'wxid_ubbh6q832tcs21:\n画个汉服美女'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1753121373, 'MsgSource': '<msgsource>\n\t<pua>1</pua>\n\t<eggIncluded>1</eggIncluded>\n\t<silence>0</silence>\n\t<membercount>3</membercount>\n\t<signature>N0_V1_tpLJq3E1|v1_nrDYslrE</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'PushContent': '郭 : 画个汉服美女', 'NewMsgId': 631584022690725554, 'MsgSeq': 871389828}
2025-07-22 02:09:23 | INFO | 收到文本消息: 消息ID:2122918454 来自:55878994168@chatroom 发送人:wxid_ubbh6q832tcs21 @:[] 内容:画个汉服美女
2025-07-22 02:09:51 | INFO | 发送文字消息: 对方wxid:55878994168@chatroom at: 内容:马上为您创作一个优雅的汉服美女形象，让我好好构思一下~正在为你创作一位穿着传统汉服的古典美女形象，会注重表现汉服的飘逸质感和东方美学特征。

2025-07-22 02:09:54 | INFO | 发送图片消息: 对方wxid:55878994168@chatroom 图片base64略
2025-07-22 02:09:54 | DEBUG | [RevokePlugin] 无法获取图片数据进行缓存，image类型: <class 'pathlib.WindowsPath'>
2025-07-22 02:09:56 | INFO | 发送文字消息: 对方wxid:55878994168@chatroom at: 内容:✅ 图片已生成
2025-07-22 02:09:57 | INFO | 发送文字消息: 对方wxid:55878994168@chatroom at: 内容:已完成汉服美女形象创作！这位古典美人身着精致刺绣的汉服，裙摆飘逸，姿态优雅端庄，背景融合了传统园林元素和水墨画风格。整体设计突出了东方美学韵味，你觉得这个形象符合你的预期吗？如果需要调整任何细节，比如服饰样式、姿势或背景，随时告诉我哦~
2025-07-22 02:09:57 | DEBUG | 处理消息内容: '画个汉服美女'
2025-07-22 02:09:57 | DEBUG | 消息内容 '画个汉服美女' 不匹配任何命令，忽略
2025-07-22 02:10:38 | DEBUG | 收到消息: {'MsgId': 2098140536, 'FromUserName': {'string': '55878994168@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 1, 'Content': {'string': 'wxid_ubbh6q832tcs21:\n换个背景，换成赛博朋克'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1753121448, 'MsgSource': '<msgsource>\n\t<pua>1</pua>\n\t<eggIncluded>1</eggIncluded>\n\t<silence>0</silence>\n\t<membercount>3</membercount>\n\t<signature>N0_V1_0cRY71d4|v1_JMw8WzUi</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'PushContent': '郭 : 换个背景，换成赛博朋克', 'NewMsgId': 7352794178726601070, 'MsgSeq': 871389837}
2025-07-22 02:10:38 | INFO | 收到文本消息: 消息ID:2098140536 来自:55878994168@chatroom 发送人:wxid_ubbh6q832tcs21 @:[] 内容:换个背景，换成赛博朋克
2025-07-22 02:11:14 | INFO | 发送文字消息: 对方wxid:55878994168@chatroom at: 内容:马上为您把片背景换成赛博朋克风格，让我好好调整一下~正在将背景更换为赛博朋克风格，会保持汉服美女的主体形象不变，同时调整光影使其与新背景完美融合。

2025-07-22 02:11:19 | INFO | 发送图片消息: 对方wxid:55878994168@chatroom 图片base64略
2025-07-22 02:11:19 | DEBUG | [RevokePlugin] 无法获取图片数据进行缓存，image类型: <class 'pathlib.WindowsPath'>
2025-07-22 02:11:21 | INFO | 发送文字消息: 对方wxid:55878994168@chatroom at: 内容:✅ 图片已生成
2025-07-22 02:11:22 | INFO | 发送文字消息: 对方wxid:55878994168@chatroom at: 内容:已完成背景更换！现在这位汉服美女置身于充满未来感的赛博朋克城市夜景中，霓虹灯光与古典服饰形成鲜明对比，同时保持了人物主体的所有细节特征。新背景的光影效果也与人物完美融合。如果需要进一步调整背景细节或人物形象，随时告诉我哦~
2025-07-22 02:11:22 | DEBUG | 处理消息内容: '换个背景，换成赛博朋克'
2025-07-22 02:11:22 | DEBUG | 消息内容 '换个背景，换成赛博朋克' 不匹配任何命令，忽略
2025-07-22 02:26:42 | DEBUG | 收到消息: {'MsgId': 729309608, 'FromUserName': {'string': '27852221909@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 47, 'Content': {'string': 'wxid_ubbh6q832tcs21:\n<msg><emoji fromusername = "wxid_ubbh6q832tcs21" tousername = "27852221909@chatroom" type="2" idbuffer="media:0_0" md5="ebffbabd614f18dc419a62056fc785a5" len = "21452" productid="" androidmd5="ebffbabd614f18dc419a62056fc785a5" androidlen="21452" s60v3md5 = "ebffbabd614f18dc419a62056fc785a5" s60v3len="21452" s60v5md5 = "ebffbabd614f18dc419a62056fc785a5" s60v5len="21452" cdnurl = "http://vweixinf.tc.qq.com/110/20401/stodownload?m=ebffbabd614f18dc419a62056fc785a5&amp;filekey=3043020101042f302d02016e0402535a04206562666662616264363134663138646334313961363230353666633738356135020253cc040d00000004627466730000000132&amp;hy=SZ&amp;storeid=2661ea29d0003c45d2877923c0000006e01004fb1535a23d48880970aa70e3&amp;ef=1&amp;bizid=1022" designerid = "" thumburl = "" encrypturl = "http://vweixinf.tc.qq.com/110/20402/stodownload?m=a5f5eeea66f4b6fcffa39bd62decd05a&amp;filekey=3043020101042f302d02016e0402535a04206135663565656561363666346236666366666133396264363264656364303561020253d0040d00000004627466730000000132&amp;hy=SZ&amp;storeid=2661ea29d000439b52877923c0000006e02004fb2535a23d48880970aa70f0&amp;ef=2&amp;bizid=1022" aeskey= "0499bcaaf0164c1f96fb1ec1b3017170" externurl = "http://vweixinf.tc.qq.com/110/20403/stodownload?m=d51bbaac45c33f1ffd3223fa352548b2&amp;filekey=3043020101042f302d02016e0402535a0420643531626261616334356333336631666664333232336661333532353438623202022e60040d00000004627466730000000132&amp;hy=SZ&amp;storeid=2661ea29d0004ca9e2877923c0000006e03004fb3535a23d48880970aa70fb&amp;ef=3&amp;bizid=1022" externmd5 = "823dab4f1bb5e5bb98f0fcceb5f4d55a" width= "240" height= "240" tpurl= "" tpauthkey= "" attachedtext= "" attachedtextcolor= "" lensid= "" emojiattr= "" linkid= "" desc= "" ></emoji>  </msg>'}, 'Status': 3, 'ImgStatus': 2, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1753122412, 'MsgSource': '<msgsource>\n\t<silence>1</silence>\n\t<membercount>146</membercount>\n\t<signature>N0_V1_Egq/aM/x|v1_NpKQ3rm7</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'NewMsgId': 4915581344992272194, 'MsgSeq': 871389846}
2025-07-22 02:26:42 | INFO | 收到表情消息: 消息ID:729309608 来自:27852221909@chatroom 发送人:wxid_ubbh6q832tcs21 MD5:ebffbabd614f18dc419a62056fc785a5 大小:21452
2025-07-22 02:26:42 | DEBUG | [DoubaoImageRecognition] 缓存表情消息: 4915581344992272194
