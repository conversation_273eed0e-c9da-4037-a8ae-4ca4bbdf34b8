import os
import json
import asyncio
import time
import uuid
import httpx
import base64
from io import BytesIO
from PIL import Image
try:
    import tomllib
except ModuleNotFoundError:
    import tomli as tomllib
from loguru import logger
from pathlib import Path
from typing import Optional, Dict, Any, List

from WechatAPI import WechatAPIClient
from utils.decorators import on_text_message
from utils.plugin_base import PluginBase

class Yuanbao(PluginBase):
    description = "元宝AI绘图插件"
    author = "XYBot社区"
    version = "1.0.0"
    plugin_name = "yuanbao"

    def __init__(self):
        # 必须先调用父类初始化
        super().__init__()
        
        # 初始化临时目录
        self.temp_dir = Path("plugins/yuanbao/temp")
        self.temp_dir.mkdir(parents=True, exist_ok=True)
        
        # 读取配置
        config = {}
        try:
            config_path = f"plugins/{self.plugin_name}/config.toml"
            if not os.path.exists(config_path):
                logger.warning(f"[{self.plugin_name}] 配置文件 {config_path} 不存在，将使用默认配置")
                config = {}
            else:
                with open(config_path, "rb") as f:
                    plugin_config = tomllib.load(f)
                config = plugin_config.get(self.plugin_name, {})
        except Exception as e:
            logger.error(f"[{self.plugin_name}] 配置文件读取失败: {str(e)}")
            config = {}

        # 基本配置初始化
        self.enable = config.get("enable", True)
        self.command = config.get("command", ["元宝", "yuanbao"])
        self.command_format = config.get("command-format", "元宝AI绘图插件\n用法: 元宝 <描述文本>")
        
        # 认证配置
        auth_config = config.get("auth", {})
        self.hy_token = auth_config.get("hy_token", "")
        self.hy_user = auth_config.get("hy_user", "")
        self.agent_id = auth_config.get("agent_id", "")
        self.x_uskey = auth_config.get("x_uskey", "")
        
        # 绘图配置
        draw_config = config.get("draw_config", {})
        self.model = draw_config.get("model", "gpt_175B_0404")
        self.plugin_type = draw_config.get("plugin", "Adaptive")
        self.host = draw_config.get("host", "yuanbao.tencent.com")
        
        # 初始化令牌桶限流
        rate_limit_config = config.get("rate_limit", {})
        self.tokens_per_second = rate_limit_config.get("tokens_per_second", 0.1)
        self.bucket_size = rate_limit_config.get("bucket_size", 3)
        self.tokens = self.bucket_size
        self.last_token_time = time.time()
        
        # 用户限流字典
        self.user_last_request = {}
        
        # 添加初始化日志
        logger.info(f"[{self.plugin_name}] 插件初始化完成")
        logger.debug(f"[{self.plugin_name}] 指令: {self.command}")
        logger.debug(f"[{self.plugin_name}] 绘图模型: {self.model}")
        logger.debug(f"[{self.plugin_name}] 认证信息加载状态: hy_token={bool(self.hy_token)}, hy_user={bool(self.hy_user)}, agent_id={bool(self.agent_id)}, x_uskey={bool(self.x_uskey)}")

    @on_text_message
    async def handle_text(self, bot: WechatAPIClient, message: dict):
        """处理文本消息"""
        if not self.enable:
            return
            
        content = str(message["Content"]).strip()
        wxid = message["FromWxid"]
        user_wxid = message["SenderWxid"]
        
        # 检查是否是插件命令
        command = content.split(" ", 1)
        if command[0] not in self.command:
            return
            
        # 如果没有提供参数
        if len(command) == 1:
            await bot.send_at_message(
                wxid,
                self.command_format,
                [user_wxid]
            )
            return

        try:
            # 检查用户请求限制
            wait_time = self._check_user_limit(wxid, user_wxid)
            if wait_time > 0:
                await bot.send_at_message(
                    wxid,
                    f"⏳ 请求太快啦，请等待 {wait_time:.1f} 秒后再试~",
                    [user_wxid]
                )
                return
                
            # 检查令牌桶
            if not await self._acquire_token():
                await bot.send_at_message(
                    wxid,
                    "⏳ 系统繁忙，请稍后再试~",
                    [user_wxid]
                )
                return

            # 发送提示消息
            await bot.send_at_message(
                wxid,
                "🎨 正在绘制中，请稍候...",
                [user_wxid]
            )

            # 获取参数
            prompt = command[1].strip()
            logger.info(f"[{self.plugin_name}] 收到绘图请求，用户: {user_wxid}, 提示词: {prompt}")
            
            # 处理绘图请求
            image_urls = await self._generate_image(prompt)
            
            if not image_urls:
                await bot.send_at_message(
                    wxid,
                    "❌ 绘图失败，请稍后重试",
                    [user_wxid]
                )
                return
                
            # 发送结果消息
            await bot.send_at_message(
                wxid,
                f"✅ 绘图完成，共生成 {len(image_urls)} 张图片:",
                [user_wxid]
            )
            
            # 发送图片
            for i, image_url in enumerate(image_urls):
                try:
                    # 下载图片
                    image_data = await self._download_image(image_url)
                    if image_data:
                        # 发送图片
                        await bot.send_image_message(wxid, image_data)
                        # 稍微延迟，避免发送过快
                        await asyncio.sleep(0.5)
                    else:
                        await bot.send_text_message(
                            wxid, 
                            f"第{i+1}张图片下载失败，URL: {image_url}"
                        )
                except Exception as e:
                    logger.error(f"[{self.plugin_name}] 发送图片失败: {str(e)}")
                    await bot.send_text_message(
                        wxid,
                        f"第{i+1}张图片发送失败: {str(e)}"
                    )
                
        except Exception as e:
            logger.error(f"[{self.plugin_name}] 处理异常: {str(e)}")
            await bot.send_at_message(
                wxid,
                f"❌ 绘图过程中出现错误: {str(e)}",
                [user_wxid]
            )
    
    async def _generate_image(self, prompt: str) -> List[str]:
        """生成图片，返回图片URL列表"""
        try:
            # 1. 创建会话
            conversation_id = await self._create_conversation()
            if not conversation_id:
                logger.error(f"[{self.plugin_name}] 创建会话失败")
                return []
                
            logger.info(f"[{self.plugin_name}] 创建会话成功: {conversation_id}")
            
            # 2. 发送绘图请求
            image_urls = await self._send_draw_request(conversation_id, prompt)
            return image_urls
            
        except Exception as e:
            logger.error(f"[{self.plugin_name}] 生成图片异常: {str(e)}")
            return []
            
    async def _create_conversation(self) -> str:
        """创建会话，返回会话ID"""
        url = f"https://{self.host}/api/user/agent/conversation/create"
        
        headers = {
            "X-ID": self.hy_user,
            "X-AgentID": self.agent_id,
            "T-UserID": self.hy_user,
            "X-Uskey": self.x_uskey,
            "Content-Type": "application/json",
            "X-Source": "web",
            "X-Requested-With": "XMLHttpRequest"
        }
        
        cookies = {
            "hy_user": self.hy_user,
            "hy_token": self.hy_token,
            "hy_source": "web"
        }
        
        data = {
            "agentId": self.agent_id
        }
        
        # 添加请求日志
        logger.debug(f"[{self.plugin_name}] 创建会话请求: URL={url}")
        logger.debug(f"[{self.plugin_name}] 请求头: X-ID={self.hy_user}, X-AgentID={self.agent_id}")
        
        try:
            async with httpx.AsyncClient() as client:
                # 添加发送请求日志
                logger.debug(f"[{self.plugin_name}] 正在发送创建会话请求...")
                
                response = await client.post(
                    url,
                    headers=headers,
                    cookies=cookies,
                    json=data,
                    timeout=10
                )
                
                # 添加响应状态日志
                logger.debug(f"[{self.plugin_name}] 创建会话响应状态码: {response.status_code}")
                
                response.raise_for_status()
                result = response.json()
                
                # 添加响应内容日志
                logger.debug(f"[{self.plugin_name}] 创建会话响应内容: {result}")
                
                return result.get("id", "")
        except Exception as e:
            logger.error(f"[{self.plugin_name}] 创建会话异常: {str(e)}")
            return ""
            
    async def _send_draw_request(self, conversation_id: str, prompt: str) -> List[str]:
        """发送绘图请求，解析SSE响应，返回图片URL列表"""
        url = f"https://{self.host}/api/chat/{conversation_id}"
        
        headers = {
            "X-Uskey": self.x_uskey,
            "X-AgentID": self.agent_id,
            "Content-Type": "text/plain;charset=UTF-8",
            "X-Source": "web",
            "X-Requested-With": "XMLHttpRequest",
            "chat_version": "v1"
        }
        
        cookies = {
            "hy_user": self.hy_user,
            "hy_token": self.hy_token,
            "hy_source": "web"
        }
        
        data = {
            "model": self.model,
            "prompt": prompt,
            "plugin": self.plugin_type,
            "displayPrompt": prompt,
            "displayPromptType": 1,
            "options": {
                "imageIntention": {
                    "needIntentionModel": True,
                    "backendUpdateFlag": 2,
                    "userIntention": {},
                    "intentionStatus": True
                }
            },
            "multimedia": [],
            "agentId": self.agent_id,
            "supportHint": 2,
            "version": "v2"
        }
        
        # 添加绘图请求日志
        logger.debug(f"[{self.plugin_name}] 发送绘图请求: URL={url}")
        logger.debug(f"[{self.plugin_name}] 会话ID: {conversation_id}")
        logger.debug(f"[{self.plugin_name}] 提示词: {prompt}")
        logger.debug(f"[{self.plugin_name}] 模型: {self.model}, 插件: {self.plugin_type}")
        
        image_urls = []
        
        try:
            async with httpx.AsyncClient() as client:
                logger.debug(f"[{self.plugin_name}] 开始发送绘图请求...")
                
                async with client.stream(
                    "POST",
                    url,
                    headers=headers,
                    cookies=cookies,
                    json=data,
                    timeout=60
                ) as response:
                    logger.debug(f"[{self.plugin_name}] 绘图请求响应状态码: {response.status_code}")
                    response.raise_for_status()
                    
                    # 处理SSE流式响应
                    logger.debug(f"[{self.plugin_name}] 开始处理SSE流式响应...")
                    buffer = ""
                    progress = 0.0
                    event_count = 0
                    async for chunk in response.aiter_text():
                        buffer += chunk
                        event_count += 1
                        
                        if event_count % 10 == 0:
                            logger.debug(f"[{self.plugin_name}] 已接收 {event_count} 个事件块")
                        
                        # 处理可能的多个数据块
                        while "data: {" in buffer:
                            try:
                                # 提取data块
                                start = buffer.find("data: {")
                                if start == -1:
                                    break
                                    
                                # 查找数据块结束
                                end = buffer.find("}", start)
                                if end == -1:
                                    break
                                    
                                # 完整的JSON对象需要包含结束括号
                                while buffer.count("{", start, end) > buffer.count("}", start, end+1):
                                    next_end = buffer.find("}", end+1)
                                    if next_end == -1:
                                        break
                                    end = next_end
                                
                                if end == -1:
                                    break
                                    
                                # 提取并解析JSON数据
                                data_chunk = buffer[start+6:end+1]
                                buffer = buffer[end+1:]
                                
                                try:
                                    data_json = json.loads(data_chunk)
                                    
                                    # 记录进度信息
                                    if data_json.get("type") == "progress":
                                        new_progress = data_json.get("value", 0)
                                        if new_progress > progress:
                                            progress = new_progress
                                            logger.debug(f"[{self.plugin_name}] 绘图进度: {progress:.2%}")
                                    
                                    # 检查是否是图片数据
                                    if data_json.get("type") == "image" and "imageUrlHigh" in data_json:
                                        image_url = data_json["imageUrlHigh"]
                                        if image_url not in image_urls:
                                            image_urls.append(image_url)
                                            logger.info(f"[{self.plugin_name}] 获取到图片URL({len(image_urls)}): {image_url[:100]}...")
                                            
                                            # 记录额外的图像元数据
                                            seed = data_json.get("seed", "未知")
                                            width = data_json.get("width", 0)
                                            height = data_json.get("height", 0)
                                            idx = data_json.get("idx", 0)
                                            logger.debug(f"[{self.plugin_name}] 图片 {len(image_urls)} 信息: {width}x{height}, seed={seed}, idx={idx}")
                                except json.JSONDecodeError:
                                    # 忽略无法解析的JSON数据
                                    logger.debug(f"[{self.plugin_name}] 跳过无效JSON数据: {data_chunk[:30]}...")
                                    pass
                            except Exception as e:
                                logger.error(f"[{self.plugin_name}] 处理数据块异常: {str(e)}")
                    
                    logger.info(f"[{self.plugin_name}] SSE流式响应处理完成，共处理 {event_count} 个事件块")
            
            logger.info(f"[{self.plugin_name}] 共获取到 {len(image_urls)} 张图片URL")
            return image_urls
                
        except Exception as e:
            logger.error(f"[{self.plugin_name}] 发送绘图请求异常: {str(e)}")
            return []
            
    async def _download_image(self, url: str) -> Optional[bytes]:
        """下载图片"""
        try:
            logger.debug(f"[{self.plugin_name}] 开始下载图片: {url[:100]}...")
            
            start_time = time.time()
            async with httpx.AsyncClient() as client:
                response = await client.get(url, timeout=30)
                response.raise_for_status()
                
                content_length = len(response.content)
                download_time = time.time() - start_time
                download_speed = content_length / download_time / 1024 if download_time > 0 else 0
                
                logger.info(f"[{self.plugin_name}] 图片下载完成: {content_length/1024:.2f}KB, 耗时{download_time:.2f}秒, 速度{download_speed:.2f}KB/s")
                
                return response.content
        except Exception as e:
            logger.error(f"[{self.plugin_name}] 下载图片异常: {str(e)}")
            return None
    
    async def _acquire_token(self) -> bool:
        """尝试获取令牌桶中的令牌"""
        current_time = time.time()
        time_elapsed = current_time - self.last_token_time
        self.last_token_time = current_time
        
        # 添加新令牌
        self.tokens = min(self.bucket_size, self.tokens + time_elapsed * self.tokens_per_second)
        
        # 尝试获取令牌
        if self.tokens < 1:
            return False
            
        self.tokens -= 1
        return True
        
    def _check_user_limit(self, wxid: str, user_wxid: str) -> float:
        """检查用户请求限制，返回需要等待的时间（秒）"""
        # 创建用户标识符 (群+用户ID)
        user_key = f"{wxid}_{user_wxid}"
        
        # 获取用户上次请求时间
        last_request = self.user_last_request.get(user_key, 0)
        current_time = time.time()
        
        # 计算冷却时间 (例如10秒)
        cooldown = 10
        elapsed = current_time - last_request
        
        # 更新最后请求时间
        self.user_last_request[user_key] = current_time
        
        # 如果冷却时间未到，返回需要等待的时间
        if elapsed < cooldown:
            return cooldown - elapsed
            
        return 0 