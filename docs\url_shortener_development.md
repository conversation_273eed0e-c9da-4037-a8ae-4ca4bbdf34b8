# 短链接服务开发指南

## 简介
`URLShortenerService` 是一个简单的短链接生成服务类,可以将长URL转换为短链接。目前使用 TinyURL 作为短链接服务提供商。

## 使用方法

### 1. 基本使用
```python
from plugins.url_shortener import URLShortenerService

# 创建服务实例
url_service = URLShortenerService()

# 生成短链接
async def generate_short_url(long_url: str):
    if short_url := await url_service.shorten_url(long_url):
        print(f"短链接生成成功: {short_url}")
    else:
        print("短链接生成失败")
```

### 2. 在插件中使用
```python
from plugins.url_shortener import URLShortenerService

class YourPlugin(PluginBase):
    def __init__(self):
        self.url_service = URLShortenerService()
        
    async def your_method(self, url: str):
        # 生成短链接
        if short_url := await self.url_service.shorten_url(url):
            # 使用生成的短链接
            ...
```

## 配置说明

服务类支持以下配置:
- `timeout`: 请求超时时间,默认10秒

## 注意事项

1. 服务限制
   - 使用 TinyURL 服务,无需 API key
   - 生成的短链接格式为: `https://tinyurl.com/xxxxx`
   - 短链接在微信中可能无法直接打开

2. 错误处理
   - 方法返回 `Optional[str]`,失败时返回 `None`
   - 会自动记录错误日志
   - 建议添加适当的错误处理

3. 最佳实践
   - 建议在非关键功能中使用
   - 可以考虑添加备用服务
   - 保存重要链接的原始URL

## 未来改进

1. 可能的改进方向:
   - 添加更多短链接服务
   - 支持自定义域名
   - 添加链接有效期
   - 支持链接统计

2. 如何贡献:
   - 提交 Issue 报告问题
   - 提交 PR 添加新功能
   - 完善文档和示例 