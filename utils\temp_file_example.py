"""
临时文件管理器使用示例
展示如何在插件中使用统一的临时文件管理
"""

import os
import asyncio
from pathlib import Path
from utils.temp_file_manager import (
    create_temp_file, 
    cleanup_file, 
    mark_file_active, 
    mark_file_inactive,
    temp_manager
)
from utils.logger import logger


class ExamplePlugin:
    """示例插件，展示临时文件管理的最佳实践"""
    
    def __init__(self):
        self.plugin_name = "ExamplePlugin"
    
    async def process_image(self, image_data: bytes) -> str:
        """处理图片的示例方法"""
        temp_file = None
        try:
            # 1. 创建临时文件
            temp_file = create_temp_file(
                plugin_name=self.plugin_name,
                suffix=".jpg",
                prefix="processed_"
            )
            
            # 2. 写入数据
            with open(temp_file, "wb") as f:
                f.write(image_data)
            
            # 3. 标记文件为活跃状态（正在使用）
            mark_file_active(temp_file)
            
            # 4. 处理文件（模拟耗时操作）
            await asyncio.sleep(1)
            processed_data = self._process_image_data(temp_file)
            
            # 5. 处理完成，标记为非活跃
            mark_file_inactive(temp_file)
            
            # 6. 延迟清理文件（60秒后删除）
            cleanup_file(temp_file, delay_seconds=60)
            
            return processed_data
            
        except Exception as e:
            logger.error(f"[{self.plugin_name}] 处理图片失败: {e}")
            
            # 出错时立即清理
            if temp_file:
                mark_file_inactive(temp_file)
                cleanup_file(temp_file)
            
            raise
    
    def _process_image_data(self, file_path: Path) -> str:
        """模拟图片处理"""
        # 这里是实际的图片处理逻辑
        return f"处理完成: {file_path.name}"
    
    async def batch_process_files(self, file_list: list) -> list:
        """批量处理文件的示例"""
        temp_files = []
        results = []
        
        try:
            # 创建多个临时文件
            for i, data in enumerate(file_list):
                temp_file = create_temp_file(
                    plugin_name=self.plugin_name,
                    suffix=f"_{i}.tmp",
                    prefix="batch_"
                )
                temp_files.append(temp_file)
                mark_file_active(temp_file)
                
                # 写入数据
                with open(temp_file, "wb") as f:
                    f.write(data)
            
            # 批量处理
            for temp_file in temp_files:
                result = self._process_file(temp_file)
                results.append(result)
                mark_file_inactive(temp_file)
            
            # 批量清理（延迟30秒）
            for temp_file in temp_files:
                cleanup_file(temp_file, delay_seconds=30)
            
            return results
            
        except Exception as e:
            logger.error(f"[{self.plugin_name}] 批量处理失败: {e}")
            
            # 出错时清理所有临时文件
            for temp_file in temp_files:
                mark_file_inactive(temp_file)
                cleanup_file(temp_file)
            
            raise
    
    def _process_file(self, file_path: Path) -> str:
        """处理单个文件"""
        return f"处理完成: {file_path.name}"
    
    async def download_and_process(self, url: str) -> str:
        """下载并处理文件的示例"""
        temp_file = None
        
        try:
            # 创建临时文件
            temp_file = create_temp_file(
                plugin_name=self.plugin_name,
                suffix=".download",
                prefix="dl_"
            )
            
            # 标记为活跃
            mark_file_active(temp_file)
            
            # 模拟下载
            await self._download_file(url, temp_file)
            
            # 处理文件
            result = self._process_downloaded_file(temp_file)
            
            # 标记为非活跃
            mark_file_inactive(temp_file)
            
            # 立即清理（因为已经处理完成）
            cleanup_file(temp_file)
            
            return result
            
        except Exception as e:
            logger.error(f"[{self.plugin_name}] 下载处理失败: {e}")
            
            if temp_file:
                mark_file_inactive(temp_file)
                cleanup_file(temp_file)
            
            raise
    
    async def _download_file(self, url: str, file_path: Path):
        """模拟文件下载"""
        # 这里是实际的下载逻辑
        await asyncio.sleep(2)  # 模拟下载时间
        
        # 写入模拟数据
        with open(file_path, "wb") as f:
            f.write(b"downloaded content")
    
    def _process_downloaded_file(self, file_path: Path) -> str:
        """处理下载的文件"""
        return f"下载并处理完成: {file_path.name}"


# 高级用法示例
class AdvancedTempFileUsage:
    """高级临时文件管理用法"""
    
    def __init__(self):
        self.plugin_name = "AdvancedExample"
    
    def add_custom_cleanup_rule(self):
        """添加自定义清理规则"""
        from utils.temp_file_manager import CleanupRule
        
        # 为特定插件添加自定义规则
        custom_rule = CleanupRule(
            max_age_hours=0.5,  # 30分钟
            file_patterns=["*.custom", "*.special"],
            exclude_patterns=["*_keep_*"],  # 排除包含_keep_的文件
            min_size_mb=1.0,  # 只清理大于1MB的文件
            max_size_mb=100.0  # 只清理小于100MB的文件
        )
        
        temp_manager.add_cleanup_rule(f"{self.plugin_name}_custom", custom_rule)
        logger.info(f"[{self.plugin_name}] 已添加自定义清理规则")
    
    def get_plugin_temp_stats(self):
        """获取插件专用的临时文件统计"""
        from utils.temp_file_manager import get_temp_stats
        
        all_stats = get_temp_stats()
        plugin_stats = all_stats.get(self.plugin_name, {})
        
        return plugin_stats
    
    def manual_cleanup_plugin_files(self):
        """手动清理插件的临时文件"""
        plugin_dir = Path("temp") / self.plugin_name
        
        if plugin_dir.exists():
            result = temp_manager.cleanup_directory(plugin_dir, "default")
            logger.info(f"[{self.plugin_name}] 手动清理结果: {result}")
            return result
        
        return {"cleaned": 0, "failed": 0, "size_mb": 0}


# 使用示例
async def example_usage():
    """使用示例"""
    plugin = ExamplePlugin()
    
    # 示例1: 处理单个图片
    image_data = b"fake image data"
    result1 = await plugin.process_image(image_data)
    print(f"图片处理结果: {result1}")
    
    # 示例2: 批量处理
    file_list = [b"data1", b"data2", b"data3"]
    results = await plugin.batch_process_files(file_list)
    print(f"批量处理结果: {results}")
    
    # 示例3: 下载并处理
    result3 = await plugin.download_and_process("http://example.com/file.jpg")
    print(f"下载处理结果: {result3}")
    
    # 高级用法
    advanced = AdvancedTempFileUsage()
    advanced.add_custom_cleanup_rule()
    stats = advanced.get_plugin_temp_stats()
    print(f"插件统计: {stats}")


if __name__ == "__main__":
    # 运行示例
    asyncio.run(example_usage())
