from typing import Union, overload

from Crypto.Util._raw_api import SmartPointer

Buffer = Union[bytes, bytearray, memoryview]

__all__ = ['CfbMode']


class CfbMode(object):
    block_size: int
    iv: Buffer
    IV: <PERSON><PERSON><PERSON>
    
    def __init__(self,
                 block_cipher: <PERSON>Pointer,
                 iv: <PERSON>uffer,
                 segment_size: int) -> None: ...
    @overload
    def encrypt(self, plaintext: Buffer) -> bytes: ...
    @overload
    def encrypt(self, plaintext: Buffer, output: Union[bytearray, memoryview]) -> None: ...
    @overload
    def decrypt(self, plaintext: <PERSON>uffer) -> bytes: ...
    @overload
    def decrypt(self, plaintext: Buffer, output: Union[bytearray, memoryview]) -> None: ...
