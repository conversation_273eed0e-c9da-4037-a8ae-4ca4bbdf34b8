import asyncio
import json
import os
import time
from pathlib import Path
from loguru import logger


class LoginMonitor:
    """
    登录监控器

    用于监控微信登录状态，定期执行心跳以保持在线状态。
    注意：二次登录功能已移除。
    """

    def __init__(self, client, check_interval=3600, max_login_attempts=3):
        """
        初始化登录监控器

        Args:
            client: WechatAPIClient实例
            check_interval (int): 检查间隔（秒），默认1小时
            max_login_attempts (int): 最大登录尝试次数
        """
        self.client = client
        self.check_interval = check_interval
        self.max_login_attempts = max_login_attempts
        self.is_running = False
        self.monitor_task = None
        self.login_attempts = 0
        self.last_check_time = 0
        self.last_login_time = 0
        self.robot_stat_path = Path(__file__).resolve().parent.parent / "resource" / "robot_stat.json"

    async def start(self):
        """启动登录监控"""
        print("\n\n===== LoginMonitor.start() 方法被调用 =====")
        print(f"===== 当前时间: {time.strftime('%Y-%m-%d %H:%M:%S')} =====")
        print(f"===== self.is_running: {self.is_running} =====")
        print(f"===== self.client 类型: {type(self.client)} =====")
        print(f"===== self.client.wxid: {self.client.wxid} =====")
        print("===== 二次登录功能已移除 =====")

        if self.is_running:
            print("===== 登录监控已在运行，不再重复启动 =====")
            logger.debug("登录监控已在运行")
            return

        self.is_running = True
        print("===== 设置 self.is_running = True =====")

        self.last_check_time = time.time()
        print(f"===== 设置 last_check_time: {time.strftime('%Y-%m-%d %H:%M:%S', time.localtime(self.last_check_time))} =====")

        logger.warning(f"===== 开始启动登录监控器，当前时间: {time.strftime('%Y-%m-%d %H:%M:%S')} =====")

        print("===== 创建 monitor_task =====")
        self.monitor_task = asyncio.create_task(self._monitor_loop())
        print("===== monitor_task 创建成功 =====")

        logger.warning(f"===== 登录监控器启动成功，将每3分钟执行一次心跳检测 =====")
        logger.info("登录监控已启动")
        print("===== 登录监控已启动 =====")

        # 立即执行一次心跳检测
        print("===== 立即执行一次心跳检测 =====")
        asyncio.create_task(self._check_heartbeat())

    async def stop(self):
        """停止登录监控"""
        if not self.is_running:
            logger.debug("登录监控未在运行")
            return

        self.is_running = False
        if self.monitor_task:
            self.monitor_task.cancel()
            try:
                await self.monitor_task
            except asyncio.CancelledError:
                pass
        logger.info("登录监控已停止")

    async def _monitor_loop(self):
        """监控循环"""
        # 测试模式：每3分钟执行一次心跳检测
        test_interval = 180  # 3分钟

        print("\n\n===== _monitor_loop 方法被调用 =====")
        print(f"===== 当前时间: {time.strftime('%Y-%m-%d %H:%M:%S')} =====")
        print(f"===== self.is_running: {self.is_running} =====")
        print(f"===== self.client 类型: {type(self.client)} =====")
        print(f"===== self.client.wxid: {self.client.wxid} =====")
        print("===== 二次登录功能已移除，改为心跳检测 =====")

        logger.warning(f"===== 登录监控器已启动，测试模式：将每{test_interval//60}分钟执行一次心跳检测 =====")
        logger.warning(f"===== 正常模式下应该每{self.check_interval//60}分钟检查一次登录状态 =====")

        while self.is_running:
            try:
                # 记录检查时间
                self.last_check_time = time.time()
                print(f"\n\n===== 开始新一轮心跳检测，当前时间: {time.strftime('%Y-%m-%d %H:%M:%S')} =====")

                # 执行心跳检测
                logger.warning(f"===== 开始执行定时心跳检测（测试模式：每{test_interval//60}分钟执行一次）=====")
                print("===== 调用 _check_heartbeat 方法 =====")
                success = await self._check_heartbeat()
                print(f"===== _check_heartbeat 返回结果: {success} =====")

                if success:
                    print("===== 定时心跳检测成功 =====")
                    logger.warning("===== 定时心跳检测成功 =====")
                else:
                    print("===== 定时心跳检测失败 =====")
                    logger.warning("===== 定时心跳检测失败 =====")

                # 重置登录尝试次数，确保下次能继续尝试
                self.login_attempts = 0
                print(f"===== 重置登录尝试次数: {self.login_attempts} =====")
            except Exception as e:
                print(f"===== 登录监控异常: {e} =====")
                import traceback
                print(f"===== 异常堆栈: {traceback.format_exc()} =====")
                logger.error(f"登录监控异常: {e}")
                logger.exception(e)  # 打印完整的异常堆栈

            # 等待下一次检查（测试模式：3分钟）
            print(f"===== 等待{test_interval//60}分钟后执行下一次心跳检测，当前时间: {time.strftime('%Y-%m-%d %H:%M:%S')} =====")
            logger.warning(f"===== 等待{test_interval//60}分钟后执行下一次心跳检测，当前时间: {time.strftime('%Y-%m-%d %H:%M:%S')} =====")
            await asyncio.sleep(test_interval)

    async def force_relogin(self):
        """
        强制执行心跳检测

        Returns:
            bool: 心跳检测是否成功
        """
        logger.info("强制执行心跳检测")
        # 重置登录尝试次数，确保能够尝试心跳
        self.login_attempts = 0
        return await self._check_heartbeat()

    async def _check_heartbeat(self):
        """执行心跳检测"""
        if self.login_attempts >= self.max_login_attempts:
            logger.error(f"已达到最大尝试次数 ({self.max_login_attempts})，停止心跳检测")
            return False

        self.login_attempts += 1
        logger.info(f"第 {self.login_attempts} 次尝试心跳检测")

        try:
            # 读取登录信息
            if not os.path.exists(self.robot_stat_path):
                logger.error("找不到登录信息文件")
                return False

            with open(self.robot_stat_path, "r") as f:
                robot_stat = json.load(f)

            wxid = robot_stat.get("wxid", "")
            if not wxid:
                logger.error("登录信息不完整")
                return False

            # 尝试心跳检测
            try:
                print(f"\n\n===== _check_heartbeat 方法被调用，wxid={wxid} =====")
                print(f"===== 当前时间: {time.strftime('%Y-%m-%d %H:%M:%S')} =====")
                print(f"===== self.client 类型: {type(self.client)} =====")
                print(f"===== self.client.wxid: {self.client.wxid} =====")

                logger.info(f"尝试执行心跳检测: {wxid}")

                # 检查自动心跳状态
                try:
                    print("===== 检查自动心跳状态 =====")
                    try:
                        status = await self.client.get_auto_heartbeat_status()
                        # 检查status是否为None或者是否有Running字段
                        if status is not None and isinstance(status, bool):
                            # 如果status直接是布尔值
                            is_running = status
                            print(f"===== 自动心跳状态: {is_running} (布尔值) =====")
                        elif status is not None and isinstance(status, dict) and "Running" in status:
                            # 如果status是字典且有Running字段
                            is_running = status.get("Running")
                            print(f"===== 自动心跳状态: {is_running} (字典中的Running字段) =====")
                        else:
                            # 如果status是None或者没有Running字段，假设已在运行
                            # 因为日志显示自动心跳已经启动，但API返回格式异常
                            is_running = True
                            print(f"===== 自动心跳状态未知: {status}，假设已在运行 =====")
                    except Exception as e:
                        print(f"===== 获取自动心跳状态失败: {e}，尝试直接发送心跳 =====")
                        # 如果获取状态失败，尝试直接发送心跳
                        try:
                            success = await self.client.heartbeat()
                            if success:
                                print("===== 心跳发送成功，假设自动心跳正在运行 =====")
                                is_running = True
                            else:
                                print("===== 心跳发送失败，假设自动心跳未运行 =====")
                                is_running = False
                        except Exception as e2:
                            print(f"===== 心跳发送异常: {e2}，假设自动心跳未运行 =====")
                            is_running = False

                    if is_running:
                        print("===== 自动心跳正在运行 =====")
                        logger.info("自动心跳正在运行")
                        # 自动心跳正在运行，直接返回成功
                        return True
                    else:
                        print("===== 自动心跳未运行，尝试启动 =====")
                        logger.warning("自动心跳未运行，尝试启动")
                        try:
                            success = await self.client.start_auto_heartbeat()
                            if success:
                                print("===== 自动心跳启动成功 =====")
                                logger.success("自动心跳启动成功")
                                return True
                            else:
                                print("===== 自动心跳启动失败，尝试直接发送心跳 =====")
                                logger.warning("自动心跳启动失败，尝试直接发送心跳")
                                # 如果启动失败，尝试直接发送心跳
                                success = await self.client.heartbeat()
                                if success:
                                    print("===== 心跳发送成功 =====")
                                    logger.success("心跳发送成功")
                                    return True
                                else:
                                    print("===== 心跳发送失败 =====")
                                    logger.warning("心跳发送失败")
                                    return False
                        except Exception as e:
                            print(f"===== 自动心跳启动异常: {e} =====")
                            logger.warning(f"自动心跳启动异常: {e}")
                            # 检查是否是"已在运行"的错误
                            if "已在运行" in str(e) or "already running" in str(e).lower():
                                print("===== 自动心跳已在运行，无需重新启动 =====")
                                logger.info("自动心跳已在运行，无需重新启动")
                                return True

                            # 如果启动异常，尝试直接发送心跳
                            try:
                                success = await self.client.heartbeat()
                                if success:
                                    print("===== 心跳发送成功 =====")
                                    logger.success("心跳发送成功")
                                    return True
                                else:
                                    print("===== 心跳发送失败 =====")
                                    logger.warning("心跳发送失败")
                                    return False
                            except Exception as e2:
                                print(f"===== 心跳发送异常: {e2} =====")
                                logger.error(f"心跳发送异常: {e2}")
                                return False
                except Exception as e:
                    print(f"===== 检查自动心跳状态异常: {e} =====")
                    logger.warning(f"检查自动心跳状态异常: {e}")
                    # 尝试直接发送心跳
                    try:
                        print("===== 尝试直接发送心跳 =====")
                        success = await self.client.heartbeat()
                        print(f"===== 心跳发送结果: {success} =====")
                        if success:
                            print("===== 心跳发送成功 =====")
                            logger.success("心跳发送成功")

                            # 尝试启动自动心跳
                            try:
                                print("===== 尝试启动自动心跳 =====")
                                auto_success = await self.client.start_auto_heartbeat()
                                print(f"===== 自动心跳启动结果: {auto_success} =====")
                                if auto_success:
                                    print("===== 自动心跳启动成功 =====")
                                    logger.success("自动心跳启动成功")
                                else:
                                    print("===== 自动心跳启动失败，但单次心跳成功 =====")
                                    logger.warning("自动心跳启动失败，但单次心跳成功")
                            except Exception as e3:
                                print(f"===== 自动心跳启动异常: {e3} =====")
                                logger.warning(f"自动心跳启动异常: {e3}")
                                print("===== 但单次心跳成功，继续执行 =====")
                                logger.info("但单次心跳成功，继续执行")

                            return True
                        else:
                            print("===== 心跳发送失败 =====")
                            logger.warning("心跳发送失败")
                            return False
                    except Exception as e2:
                        print(f"===== 心跳发送异常: {e2} =====")
                        logger.error(f"心跳发送异常: {e2}")
                        return False

                self.last_login_time = time.time()
                print(f"===== 更新最后心跳时间: {time.strftime('%Y-%m-%d %H:%M:%S', time.localtime(self.last_login_time))} =====")
                return True
            except Exception as e:
                logger.error(f"心跳检测过程中发生错误: {e}")
                return False
        except Exception as e:
            logger.error(f"心跳检测过程中发生错误: {e}")
            return False

        # 如果心跳检测失败，等待一段时间后再次尝试
        wait_time = min(300, self.login_attempts * 60)  # 最多等待5分钟
        logger.info(f"等待 {wait_time} 秒后再次尝试心跳检测")
        await asyncio.sleep(wait_time)
        return False

    async def _relogin(self):
        """
        尝试登录（已弃用，改为心跳检测）

        此方法已弃用，保留是为了兼容性。请使用 _check_heartbeat 方法代替。
        """
        logger.warning("_relogin 方法已弃用，改为使用 _check_heartbeat 方法")
        return await self._check_heartbeat()

    def get_status(self):
        """
        获取监控状态

        Returns:
            dict: 监控状态信息
        """
        current_time = time.time()
        return {
            "is_running": self.is_running,
            "login_attempts": self.login_attempts,
            "max_login_attempts": self.max_login_attempts,
            "last_check_time": self.last_check_time,
            "last_login_time": self.last_login_time,
            "time_since_last_login": current_time - self.last_login_time if self.last_login_time > 0 else None,
            "uptime": current_time - self.last_login_time if self.last_login_time > 0 else 0
        }
