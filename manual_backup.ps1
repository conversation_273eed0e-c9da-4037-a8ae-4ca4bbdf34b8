# 备份脚本代码块
$backupScriptBlock = {
    param($syncHash, $comment)
    
    try {
        $syncHash.IsRunning = $true
        $syncHash.StatusText = "正在准备备份..."
        $syncHash.ProgressValue = 0
        
        if ($syncHash.CancellationRequested) {
            throw "用户取消了备份操作"
        }
        
        # 执行实际的备份操作，只执行一次
        & "C:\Scripts\backup.ps1" -BackupType "manual" -Comment $comment
        
        $syncHash.StatusText = "备份完成！`n备份说明：$comment"
        $syncHash.ProgressValue = 100
    }
    catch {
        $syncHash.StatusText = "备份失败！`n错误信息：$($_.Exception.Message)"
    }
    finally {
        $syncHash.IsRunning = $false
        $syncHash.CancellationRequested = $false
    }
} 