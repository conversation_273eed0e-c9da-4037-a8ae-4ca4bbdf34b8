import os
import json
import asyncio
import time
import random
import base64
import xml.etree.ElementTree as ET
from pathlib import Path
from typing import Optional, Dict, List, Tuple
try:
    import tomllib
except ModuleNotFoundError:
    import tomli as tomllib

import httpx
from loguru import logger

from WechatAPI import WechatAPIClient
from utils.decorators import on_text_message, on_quote_message, on_emoji_message
from utils.plugin_base import PluginBase
from utils.temp_file_manager import cleanup_file


class DouHuiImageGenerator:
    """豆绘AI图生图生成器 - 基于IdeaFlow API"""

    def __init__(self, authorization_token: str = None):
        """
        初始化豆绘图生图生成器

        Args:
            authorization_token: Authorization token，如果为None则使用默认token
        """
        self.base_url = "https://cyapi.ideaflow.pro"
        self.upload_endpoint = "/attach/upload"
        self.token_flush_endpoint = "/user/token/flush"
        self.pipe_chat_endpoint = "/pipe/chat"
        self.album_endpoint = "/cutebox/pipe/album"

        # 默认Authorization token
        default_token = 'metatube-eyJhbGciOiJIUzUxMiIsInR5cCI6IkpXVCJ9.eyJzdWIiOiIwMDAwMDQ1Mzg5Mjk2NTc1MDQzNjY1OTIiLCJuaWNrbmFtZSI6IuaipuWunThVSFNYVVZWNyIsImV4cCI6MTc1MDczMTQxNSwidXNlcklkIjoiMDAwMDA0NTM4OTI5NjU3NTA0MzY2NTkyIiwianRpIjoiMDAwMDA0NTM4OTI5NjU3NTA0MzY2NTkyIiwiYXV0aG9yaXRpZXMiOiJbXSJ9.wfnmo1RG66yitpYwGI8eV2zYNaDjkqfZwIe-s7bcSWG32sB-chR-_b3zeiriYV2OzlOoonG_Vrm3W-QQZDDdmQ'

        # 请求头配置
        self.headers = {
            'Host': 'cyapi.ideaflow.pro',
            'Connection': 'keep-alive',
            'pg': '7',
            'uid': '000004538929657504366592',
            'Authorization': authorization_token or default_token,
            'c': 'h5',
            'env': 'ideaflow',
            'User-Agent': 'Mozilla/5.0 (Linux; Android 10; V2002A Build/QP1A.190711.020) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/83.0.4103.106 Mobile Safari/537.36',
            'Accept': '*/*',
            'Origin': 'https://ciyuan.ideaflow.pro',
            'X-Requested-With': 'mark.via',
            'Sec-Fetch-Site': 'same-site',
            'Sec-Fetch-Mode': 'cors',
            'Sec-Fetch-Dest': 'empty',
            'Referer': 'https://ciyuan.ideaflow.pro/preview.html?pipe_id=000004229528230275088385',
            'Accept-Encoding': 'gzip, deflate',
            'Accept-Language': 'zh-CN,zh;q=0.9,en-US;q=0.8,en;q=0.7'
        }

    async def flush_token(self, client: httpx.AsyncClient):
        """
        刷新token获取新的Authorization

        Returns:
            bool: 刷新是否成功
        """
        try:
            response = await client.post(
                url=f"{self.base_url}{self.token_flush_endpoint}",
                headers={
                    'Host': 'cyapi.ideaflow.pro',
                    'Connection': 'keep-alive',
                    'Content-Length': '0',
                    'pg': '7',
                    'uid': '000004538929657504366592',
                    'Authorization': self.headers['Authorization'],
                    'c': 'h5',
                    'env': 'ideaflow',
                    'User-Agent': 'Mozilla/5.0 (Linux; Android 10; V2002A Build/QP1A.190711.020) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/83.0.4103.106 Mobile Safari/537.36',
                    'Content-Type': 'application/json',
                    'Accept': '*/*',
                    'Origin': 'https://ciyuan.ideaflow.pro',
                    'X-Requested-With': 'mark.via',
                    'Sec-Fetch-Site': 'same-site',
                    'Sec-Fetch-Mode': 'cors',
                    'Sec-Fetch-Dest': 'empty',
                    'Referer': 'https://ciyuan.ideaflow.pro/preview.html?pipe_id=000004229528230275088385',
                    'Accept-Encoding': 'gzip, deflate',
                    'Accept-Language': 'zh-CN,zh;q=0.9,en-US;q=0.8,en;q=0.7'
                }
            )

            result = response.json()
            if result.get('code') == 0 and result.get('data', {}).get('token'):
                new_token = f"metatube-{result['data']['token']}"
                self.headers['Authorization'] = new_token
                return True
            else:
                logger.error(f"豆绘AI Token刷新失败: {result}")
                return False

        except Exception as e:
            logger.error(f"豆绘AI Token刷新异常: {str(e)}")
            return False

    def update_authorization(self, new_token):
        """
        手动更新Authorization token

        Args:
            new_token (str): 新的Authorization token
        """
        self.headers['Authorization'] = new_token

    async def upload_file(self, client: httpx.AsyncClient, file_path, auto_refresh_token=True):
        """
        上传文件到ideaflow服务器

        Args:
            file_path (str): 要上传的文件路径
            auto_refresh_token (bool): 是否自动刷新token

        Returns:
            dict: 服务器响应结果
        """
        if not os.path.exists(file_path):
            return {"error": f"文件不存在: {file_path}"}

        file_name = Path(file_path).name

        try:
            with open(file_path, 'rb') as file:
                files = {
                    'mFile': (file_name, file, self._get_content_type(file_path))
                }

                # 发送POST请求
                response = await client.post(
                    url=f"{self.base_url}{self.upload_endpoint}",
                    headers=self.headers,
                    files=files
                )

                # 检查是否是401错误（token过期）
                if response.status_code == 401 and auto_refresh_token:
                    if await self.flush_token(client):
                        # 重新打开文件上传
                        with open(file_path, 'rb') as file:
                            files = {
                                'mFile': (file_name, file, self._get_content_type(file_path))
                            }
                            response = await client.post(
                                url=f"{self.base_url}{self.upload_endpoint}",
                                headers=self.headers,
                                files=files
                            )
                    else:
                        return {"error": "Token刷新失败，请检查网络或手动更新token"}

                # 返回JSON响应
                return response.json()

        except Exception as e:
            return {"error": f"上传失败: {str(e)}"}

    def _get_content_type(self, file_path):
        """
        根据文件扩展名获取Content-Type

        Args:
            file_path (str): 文件路径

        Returns:
            str: Content-Type
        """
        ext = Path(file_path).suffix.lower()
        content_types = {
            '.jpg': 'image/jpeg',
            '.jpeg': 'image/jpeg',
            '.png': 'image/png',
            '.gif': 'image/gif',
            '.bmp': 'image/bmp',
            '.webp': 'image/webp',
            '.pdf': 'application/pdf',
            '.txt': 'text/plain',
            '.doc': 'application/msword',
            '.docx': 'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
        }
        return content_types.get(ext, 'application/octet-stream')

    async def image_to_image(self, client: httpx.AsyncClient, image_url, prompt, pipe_id="000004229528230275088385"):
        """
        图生图操作

        Args:
            image_url (str): 图片URL（通过upload_file获得）
            prompt (str): 提示词
            pipe_id (str): 管道ID，默认使用抓包中的ID

        Returns:
            dict: 服务器响应结果
        """
        try:
            payload = {
                "pipeId": pipe_id,
                "in": [
                    {
                        "name": "k_2",
                        "type": "img",
                        "val": image_url
                    },
                    {
                        "name": "k_1",
                        "type": "str",
                        "val": prompt
                    }
                ]
            }

            response = await client.post(
                url=f"{self.base_url}{self.pipe_chat_endpoint}",
                headers={
                    'Host': 'cyapi.ideaflow.pro',
                    'Connection': 'keep-alive',
                    'pg': '7',
                    'uid': '000004538929657504366592',
                    'Authorization': self.headers['Authorization'],
                    'c': 'h5',
                    'env': 'ideaflow',
                    'User-Agent': 'Mozilla/5.0 (Linux; Android 10; V2002A Build/QP1A.190711.020) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/83.0.4103.106 Mobile Safari/537.36',
                    'Content-Type': 'application/json',
                    'Accept': '*/*',
                    'Origin': 'https://ciyuan.ideaflow.pro',
                    'X-Requested-With': 'mark.via',
                    'Sec-Fetch-Site': 'same-site',
                    'Sec-Fetch-Mode': 'cors',
                    'Sec-Fetch-Dest': 'empty',
                    'Referer': 'https://ciyuan.ideaflow.pro/preview.html?pipe_id=000004229528230275088385',
                    'Accept-Encoding': 'gzip, deflate',
                    'Accept-Language': 'zh-CN,zh;q=0.9,en-US;q=0.8,en;q=0.7'
                },
                json=payload
            )

            # 检查是否是401错误（token过期）
            if response.status_code == 401:
                if await self.flush_token(client):
                    # 更新headers中的Authorization并重新请求
                    response = await client.post(
                        url=f"{self.base_url}{self.pipe_chat_endpoint}",
                        headers={
                            'Host': 'cyapi.ideaflow.pro',
                            'Connection': 'keep-alive',
                            'pg': '7',
                            'uid': '000004538929657504366592',
                            'Authorization': self.headers['Authorization'],
                            'c': 'h5',
                            'env': 'ideaflow',
                            'User-Agent': 'Mozilla/5.0 (Linux; Android 10; V2002A Build/QP1A.190711.020) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/83.0.4103.106 Mobile Safari/537.36',
                            'Content-Type': 'application/json',
                            'Accept': '*/*',
                            'Origin': 'https://ciyuan.ideaflow.pro',
                            'X-Requested-With': 'mark.via',
                            'Sec-Fetch-Site': 'same-site',
                            'Sec-Fetch-Mode': 'cors',
                            'Sec-Fetch-Dest': 'empty',
                            'Referer': 'https://ciyuan.ideaflow.pro/preview.html?pipe_id=000004229528230275088385',
                            'Accept-Encoding': 'gzip, deflate',
                            'Accept-Language': 'zh-CN,zh;q=0.9,en-US;q=0.8,en;q=0.7'
                        },
                        json=payload
                    )
                else:
                    return {"error": "Token刷新失败，请检查网络或手动更新token"}

            return response.json()

        except Exception as e:
            return {"error": f"图生图操作失败: {str(e)}"}

    async def poll_task_result(self, client: httpx.AsyncClient, task_id, max_wait_time=300, poll_interval=3):
        """
        轮询任务结果

        Args:
            task_id (str): 任务ID
            max_wait_time (int): 最大等待时间（秒），默认5分钟
            poll_interval (int): 轮询间隔（秒），默认3秒

        Returns:
            dict: 任务结果
        """
        start_time = time.time()

        while time.time() - start_time < max_wait_time:
            try:
                response = await client.get(
                    url=f"{self.base_url}{self.album_endpoint}",
                    params={"albumId": task_id},
                    headers={
                        'Host': 'cyapi.ideaflow.pro',
                        'Connection': 'keep-alive',
                        'appid': '000003523433300180353024',
                        'env': 'ideaflow',
                        'Authorization': self.headers['Authorization'],
                        'Content-Type': 'application/json',
                        'User-Agent': 'Mozilla/5.0 (Linux; Android 10; V2002A Build/QP1A.190711.020) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/83.0.4103.106 Mobile Safari/537.36',
                        'uid': '000004538929657504366592',
                        'c': 'h5',
                        'pg': '7',
                        'Accept': '*/*',
                        'Origin': 'https://ciyuan.ideaflow.pro',
                        'X-Requested-With': 'mark.via',
                        'Sec-Fetch-Site': 'same-site',
                        'Sec-Fetch-Mode': 'cors',
                        'Sec-Fetch-Dest': 'empty',
                        'Referer': 'https://ciyuan.ideaflow.pro/preview.html?pipe_id=000004229528230275088385',
                        'Accept-Encoding': 'gzip, deflate',
                        'Accept-Language': 'zh-CN,zh;q=0.9,en-US;q=0.8,en;q=0.7'
                    }
                )

                # 检查是否是401错误（token过期）
                if response.status_code == 401:
                    if await self.flush_token(client):
                        continue  # 刷新成功后继续轮询
                    else:
                        return {"error": "Token刷新失败，请检查网络或手动更新token"}

                result = response.json()

                if result.get('code') == 0 and result.get('data'):
                    progress = result['data'].get('progress')

                    if progress == "completed":
                        return result
                    elif progress == "handing":
                        await asyncio.sleep(poll_interval)
                    else:
                        # 其他状态，可能是错误
                        return result
                else:
                    return {"error": f"轮询请求失败: {result}"}

            except Exception as e:
                return {"error": f"轮询异常: {str(e)}"}

        return {"error": f"任务超时，等待时间超过 {max_wait_time} 秒"}

    def extract_image_urls(self, task_result):
        """
        从任务结果中提取图片URL

        Args:
            task_result (dict): 任务结果

        Returns:
            list: 图片URL列表
        """
        try:
            if task_result.get('data', {}).get('content'):
                content_str = task_result['data']['content']
                content_list = json.loads(content_str)

                image_urls = []
                for item in content_list:
                    if 'content' in item:
                        for content_item in item['content']:
                            if content_item.get('type') == 'img' and content_item.get('val'):
                                image_urls.append(content_item['val'])

                return image_urls

            return []

        except Exception as e:
            logger.error(f"提取图片URL失败: {str(e)}")
            return []

    async def process_image(self, image_path: str, prompt: str = "风格转换") -> Tuple[Optional[str], Optional[str]]:
        """
        完整的图片处理流程

        Args:
            image_path: 本地图片文件路径
            prompt: 生成提示词

        Returns:
            Tuple[图片URL, 文字响应]: 生成成功返回(图片URL, 文字响应)，失败返回(None, None)
        """
        try:
            async with httpx.AsyncClient(timeout=300) as client:
                # 步骤1: 上传图片
                upload_result = await self.upload_file(client, image_path, auto_refresh_token=True)

                if "error" in upload_result:
                    logger.error(f"上传失败: {upload_result['error']}")
                    return None, None

                if not (upload_result.get('data') and upload_result['data'].get('list')):
                    # 根据响应代码提供具体的错误信息
                    error_code = upload_result.get('code')
                    error_msg = upload_result.get('msg', '未知错误')

                    if error_code == 452:
                        error_detail = "上传图片涉及敏感内容，请重新上传其他图片"
                    elif error_code == 401:
                        error_detail = "认证失败，请检查API配置"
                    elif error_code == 403:
                        error_detail = "权限不足，请检查API权限"
                    elif error_code == 429:
                        error_detail = "请求过于频繁，请稍后再试"
                    else:
                        error_detail = f"上传失败: {error_msg} (错误代码: {error_code})"

                    logger.error(f"上传失败: {error_detail}")
                    return None, error_detail

                image_url = upload_result['data']['list'][0]

                # 步骤2: 图生图操作
                img2img_result = await self.image_to_image(client, image_url, prompt)

                if "error" in img2img_result:
                    error_detail = img2img_result['error']
                    logger.error(f"图生图失败: {error_detail}")
                    return None, error_detail

                task_id = img2img_result.get('data')

                if not task_id:
                    logger.error("未获取到任务ID")
                    return None, None

                # 步骤3: 等待任务完成
                task_result = await self.poll_task_result(client, task_id)

                if "error" in task_result:
                    error_detail = task_result['error']
                    logger.error(f"轮询失败: {error_detail}")
                    return None, error_detail

                # 提取生成的图片URL
                image_urls = self.extract_image_urls(task_result)

                if image_urls:
                    # 返回第一张图片的URL，不返回文字响应
                    return image_urls[0], None
                else:
                    logger.error("未找到生成的图片")
                    return None, None

        except Exception as e:
            logger.error(f"豆绘AI处理图片时发生错误: {str(e)}")
            return None, None


class DouHui(PluginBase):
    description = "豆绘AI图生图功能，支持图像编辑、风格转换等"
    author = "XYBot"
    version = "1.0.0"
    plugin_name = "DouHui"

    def __init__(self):
        super().__init__()

        # 初始化临时目录
        self.temp_dir = Path("temp/douhui")
        self.temp_dir.mkdir(parents=True, exist_ok=True)

        # 读取配置
        self._load_config()

        # 用户限流和会话管理
        self.user_last_request = {}
        self.user_request_count = {}

        # 表情消息缓存
        self.emoji_message_cache = {}

        # 注意：临时文件清理现在由统一的TempFileManager处理

        # 引用图片命令
        self.quote_command = ["豆绘"]

    def _load_config(self):
        """加载配置文件"""
        config = {}
        try:
            config_path = f"plugins/{self.plugin_name}/config.toml"
            if not os.path.exists(config_path):
                logger.warning(f"[{self.plugin_name}] 配置文件 {config_path} 不存在，将使用默认配置")
                config = {}
            else:
                with open(config_path, "rb") as f:
                    plugin_config = tomllib.load(f)
                config = plugin_config.get(self.plugin_name, {})
        except Exception as e:
            logger.error(f"[{self.plugin_name}] 配置文件读取失败: {str(e)}")
            config = {}

        # 基本配置
        self.enable = config.get("enable", True)
        self.command = config.get("command", ["豆绘测试"])
        self.command_format = config.get("command-format", "豆绘测试 [提示词] [图片路径]")

        # 引用图片命令配置
        quote_config = config.get("quote", {})
        self.quote_command = quote_config.get("command", ["豆绘"])
        self.quote_command_format = quote_config.get("command-format", "引用图片并发送: 豆绘 [提示词]")

        # API配置
        self.api_config = config.get("api", {})
        self.base_url = self.api_config.get("base_url", "https://api.douhui.com")
        self.api_key = self.api_config.get("api_key", "")
        self.model = self.api_config.get("model", "douhui-image-generation")

        # 限流配置
        rate_limit_config = config.get("rate_limit", {})
        self.cooldown = rate_limit_config.get("cooldown", 15)

        # 自然化响应配置
        self.natural_response = config.get("natural_response", True)
        self._init_natural_responses()

    def _init_natural_responses(self):
        """初始化自然化回复词库"""
        self.confirm_msgs = [
            "好的", "收到", "明白了", "嗯", "知道了", "行", "OK"
        ]

        self.error_msgs = [
            "这可把我难住了", "搞不定了", "这个有点难", "弄不了",
            "出问题了", "搞不出来", "这个不行"
        ]

        self.rate_limit_msgs = [
            "你搁这刷bug呢", "慢点慢点", "别这么急", "一个一个来",
            "太快了太快了", "歇会儿", "别刷了"
        ]

    # 注意：临时文件清理现在由统一的TempFileManager处理，此方法已移除

    def _check_rate_limit(self, user_wxid: str) -> bool:
        """检查用户请求频率限制"""
        current_time = time.time()

        if user_wxid not in self.user_last_request or \
           (current_time - self.user_last_request.get(user_wxid, 0)) > self.cooldown:
            self.user_last_request[user_wxid] = current_time
            self.user_request_count[user_wxid] = 1
            return False

        self.user_last_request[user_wxid] = current_time
        self.user_request_count[user_wxid] = self.user_request_count.get(user_wxid, 0) + 1
        return True

    async def _simple_confirm(self, bot, wxid, custom_message=None):
        """简单确认回复，不艾特人"""
        if custom_message:
            # 使用豆绘AI的文字响应
            await bot.send_text_message(wxid, custom_message)
        elif self.natural_response:
            confirm_msg = random.choice(self.confirm_msgs)
            await bot.send_text_message(wxid, confirm_msg)

    def _process_emoji_to_image(self, emoji_path: str) -> Optional[str]:
        """将表情包转换为可识图的图片

        Args:
            emoji_path: 下载的表情文件路径(.bin格式)

        Returns:
            转换后的图片路径，失败返回None
        """
        try:
            # 将.bin文件重命名为.gif
            gif_path = emoji_path.replace('.bin', '.gif')
            if emoji_path != gif_path:
                os.rename(emoji_path, gif_path)
            else:
                gif_path = emoji_path

            # 提取第一帧并转换为jpg
            try:
                from PIL import Image
                with Image.open(gif_path) as img:
                    # 获取第一帧
                    img.seek(0)
                    # 转换为RGB模式（去除透明通道）
                    if img.mode in ('RGBA', 'LA', 'P'):
                        # 创建白色背景
                        background = Image.new('RGB', img.size, (255, 255, 255))
                        if img.mode == 'P':
                            img = img.convert('RGBA')
                        background.paste(img, mask=img.split()[-1] if img.mode in ('RGBA', 'LA') else None)
                        img = background
                    elif img.mode != 'RGB':
                        img = img.convert('RGB')

                    # 保存为jpg格式
                    jpg_path = gif_path.replace('.gif', '_frame.jpg')
                    img.save(jpg_path, 'JPEG', quality=95)

                    return jpg_path

            except ImportError:
                logger.error(f"[{self.plugin_name}] 缺少PIL库，无法处理表情包")
                return None
            except Exception as e:
                logger.error(f"[{self.plugin_name}] 提取表情帧失败: {str(e)}")
                return None

        except Exception as e:
            logger.error(f"[{self.plugin_name}] 处理表情文件失败: {str(e)}")
            return None

    async def _download_emoji(self, bot: WechatAPIClient, emoji_info: dict) -> Optional[str]:
        """下载表情文件

        Args:
            emoji_info: 表情信息字典

        Returns:
            下载的文件路径，失败返回None
        """
        try:
            emoji_url = emoji_info.get('EmojiUrl')
            emoji_md5 = emoji_info.get('EmojiMD5')
            aeskey = emoji_info.get('aeskey')

            if not emoji_url or not emoji_md5:
                return None

            # 使用微信API下载表情
            if aeskey:
                # 如果有aeskey，使用加密下载
                emoji_base64 = await bot.download_image(aeskey, emoji_url)
            else:
                # 直接下载
                try:
                    async with httpx.AsyncClient() as client:
                        response = await client.get(emoji_url)
                        if response.status_code == 200:
                            emoji_base64 = base64.b64encode(response.content).decode()
                        else:
                            return None
                except Exception as e:
                    return None

            if not emoji_base64:
                return None

            # 保存表情文件
            temp_file = self.temp_dir / f"emoji_{emoji_md5}_{int(time.time())}.bin"
            emoji_data = base64.b64decode(emoji_base64)
            with open(temp_file, "wb") as f:
                f.write(emoji_data)

            return str(temp_file)

        except Exception as e:
            logger.error(f"[{self.plugin_name}] 下载表情时出错: {str(e)}")
            return None

    def _cleanup_temp_file(self, file_path: str, delay_seconds: int = 0):
        """清理临时文件 - 使用统一管理器

        Args:
            file_path: 要删除的文件路径
            delay_seconds: 延迟删除时间（秒），0表示立即删除
        """
        cleanup_file(file_path, delay_seconds)

    def _cleanup_temp_files(self, file_paths: list, delay_seconds: int = 0):
        """批量清理临时文件

        Args:
            file_paths: 要删除的文件路径列表
            delay_seconds: 延迟删除时间（秒），0表示立即删除
        """
        for file_path in file_paths:
            if file_path and isinstance(file_path, str):
                self._cleanup_temp_file(file_path, delay_seconds)

    @on_text_message
    async def handle_text(self, bot: WechatAPIClient, message: dict):
        """处理文本消息"""
        if not self.enable:
            return

        content = str(message["Content"]).strip()
        wxid = message["FromWxid"]
        user_wxid = message["SenderWxid"]

        # 检查是否是帮助命令
        if content in ["豆绘帮助", "豆绘说明", "豆绘指令"]:
            await self._send_usage_instructions(bot, wxid, user_wxid)
            return

        # 检查是否是插件命令
        command_parts = content.split(" ", 2)  # 分割成三部分：命令、提示词、图片路径

        # 标准图生图命令处理
        if command_parts[0] in self.command:
            # 检查是否是测试命令
            if command_parts[0] in ["豆绘测试", "douhui"]:
                # 默认测试图片路径
                default_image_path = "C:\\DBE25C6475AF6852691B040206E94167.jpg"

                if len(command_parts) == 1:
                    # 如果只有"豆绘测试"，使用默认提示词
                    prompt = "风格转换"
                    image_path = default_image_path
                elif len(command_parts) == 2:
                    # 如果是"豆绘测试 [提示词]"，使用指定提示词和默认图片路径
                    prompt = command_parts[1].strip()
                    image_path = default_image_path
                else:
                    # 如果是完整格式"豆绘测试 [提示词] [图片路径]"
                    prompt = command_parts[1].strip()
                    image_path = command_parts[2].strip()

                # 检查限流
                if self._check_rate_limit(user_wxid):
                    if self.natural_response:
                        rate_limit_msg = random.choice(self.rate_limit_msgs)
                        await bot.send_text_message(wxid, rate_limit_msg)
                    else:
                        await bot.send_at_message(wxid, "⏳ 请求太频繁，请稍后再试", [user_wxid])
                    return

                # 检查图片是否存在
                if not os.path.exists(image_path):
                    await bot.send_at_message(
                        wxid,
                        f"❌ 图片不存在: {image_path}\n请确认测试图片路径是否正确",
                        [user_wxid]
                    )
                    # 发送使用说明
                    await self._send_usage_instructions(bot, wxid, user_wxid)
                    return

                try:
                    # 执行图生图流程
                    await self._process_image_flow(bot, wxid, user_wxid, prompt, image_path)
                except Exception as e:
                    logger.error(f"[{self.plugin_name}] 处理失败: {str(e)}")
                    await bot.send_at_message(
                        wxid,
                        f"❌ 处理失败: {str(e)}",
                        [user_wxid]
                    )
                return

    @on_quote_message
    async def handle_quote(self, bot: WechatAPIClient, message: dict):
        """处理引用消息，支持引用图片进行AI处理"""
        if not self.enable:
            return

        content = str(message.get("Content", "")).strip()
        wxid = message.get("FromWxid", "")
        user_wxid = message.get("SenderWxid", "")

        # 检查是否是豆绘命令
        command_parts = content.split(maxsplit=1)
        if not command_parts or command_parts[0] not in self.quote_command:
            return

        # 检查限流
        if self._check_rate_limit(user_wxid):
            if self.natural_response:
                rate_limit_msg = random.choice(self.rate_limit_msgs)
                await bot.send_text_message(wxid, rate_limit_msg)
            else:
                await bot.send_at_message(wxid, "请求太频繁，等会再试试", [user_wxid])
            return

        # 获取提示词
        prompt = "风格转换"  # 默认提示词
        if len(command_parts) > 1:
            prompt = command_parts[1].strip()

        # 获取引用的消息信息
        quote_info = message.get("Quote", {})
        quoted_msg_id = quote_info.get("Msgid") or quote_info.get("NewMsgId")

        quoted_sender = quote_info.get("SenderWxid") or quote_info.get("FromWxid")
        bot_wxid = getattr(bot, 'wxid', None)


        # 检查被引用消息的类型 (3表示图片消息，47表示表情消息)
        quoted_msg_type = quote_info.get("MsgType")

        if quoted_msg_type not in [3, 47]:  # 支持图片和表情消息
            await bot.send_at_message(
                wxid,
                f"❌ 请引用图片或表情消息 (当前引用消息类型: {quoted_msg_type})",
                [user_wxid]
            )
            return

        # 检查是否是机器人自己发的图片，如果是，尝试从撤回插件获取缓存
        if quoted_sender == bot_wxid:
            try:
                # 尝试导入撤回插件
                from plugins.RevokePlugin.main import RevokePlugin
                if RevokePlugin._instance:
                    msg_info = RevokePlugin._instance.get_message_by_id(quoted_msg_id)
                    if msg_info:
                        # 检查是否有本地图片缓存路径
                        if 'local_image_path' in msg_info and os.path.exists(msg_info['local_image_path']):
                            await self._process_image_flow_without_notification(
                                bot, wxid, user_wxid, prompt, msg_info['local_image_path']
                            )
                            return
            except ImportError:
                pass
            except Exception as e:
                logger.error(f"[{self.plugin_name}] 从撤回插件获取缓存时出错: {str(e)}")

            # 如果无法从撤回插件获取缓存，提示用户
            await bot.send_at_message(
                wxid,
                "❌ 暂不支持处理机器人发送的图片\n请引用其他用户发送的图片",
                [user_wxid]
            )
            return

        # 根据消息类型选择处理方式
        if quoted_msg_type == 47:
            # 处理表情消息引用
            await self._handle_emoji_quote(bot, wxid, user_wxid, quoted_msg_id, prompt)
            return

        # 处理图片消息引用 (MsgType 3)
        try:
            # 从Quote.Content中提取XML
            quote_content = quote_info.get("Content", "")

            if not quote_content:
                if self.natural_response:
                    error_msg = random.choice(self.error_msgs)
                    await bot.send_text_message(wxid, error_msg)
                else:
                    await bot.send_at_message(wxid, "❌ 无法获取引用的图片内容", [user_wxid])
                return

            # 解析XML内容
            try:
                # 解析引用内容XML
                root = ET.fromstring(quote_content)

                # 首先尝试直接查找img节点
                img_node = root.find('.//img')

                # 如果没有直接的img节点，尝试查找refermsg节点中的content
                if img_node is None:
                    # 查找refermsg节点
                    refermsg = root.find('.//refermsg')
                    if refermsg is not None and refermsg.find('content') is not None:
                        # 提取refermsg中的content内容
                        content_text = refermsg.find('content').text
                        if content_text:
                            # content中的内容是XML格式，但被HTML编码，需要解码
                            content_text = content_text.replace('&lt;', '<').replace('&gt;', '>')
                            # 尝试解析内部的img标签
                            try:
                                inner_root = ET.fromstring(content_text)
                                img_node = inner_root.find('img')
                            except ET.ParseError:
                                pass

                # 如果仍然未找到img节点
                if img_node is None:
                    # 尝试直接从content中提取aeskey和cdnmidimgurl
                    if "aeskey=" in quote_content and "cdnmidimgurl=" in quote_content:
                        aeskey_start = quote_content.find('aeskey="') + 8
                        aeskey_end = quote_content.find('"', aeskey_start)
                        aeskey = quote_content[aeskey_start:aeskey_end]

                        cdnmidimgurl_start = quote_content.find('cdnmidimgurl="') + 14
                        cdnmidimgurl_end = quote_content.find('"', cdnmidimgurl_start)
                        cdnmidimgurl = quote_content[cdnmidimgurl_start:cdnmidimgurl_end]
                    elif "cdnthumbaeskey=" in quote_content and "cdnthumburl=" in quote_content:
                        aeskey_start = quote_content.find('cdnthumbaeskey="') + 16
                        aeskey_end = quote_content.find('"', aeskey_start)
                        aeskey = quote_content[aeskey_start:aeskey_end]

                        cdnmidimgurl_start = quote_content.find('cdnthumburl="') + 13
                        cdnmidimgurl_end = quote_content.find('"', cdnmidimgurl_start)
                        cdnmidimgurl = quote_content[cdnmidimgurl_start:cdnmidimgurl_end]
                    else:
                        if self.natural_response:
                            error_msg = random.choice(self.error_msgs)
                            await bot.send_text_message(wxid, error_msg)
                        else:
                            await bot.send_at_message(wxid, "❌ 无法从引用消息中提取图片信息", [user_wxid])
                        return
                else:
                    # 提取aeskey和cdnmidimgurl
                    aeskey = img_node.get('aeskey')
                    cdnmidimgurl = img_node.get('cdnmidimgurl')

                    if not aeskey or not cdnmidimgurl:
                        # 尝试提取缩略图信息
                        aeskey = img_node.get('cdnthumbaeskey')
                        cdnmidimgurl = img_node.get('cdnthumburl')

                if not aeskey or not cdnmidimgurl:
                    if self.natural_response:
                        error_msg = random.choice(self.error_msgs)
                        await bot.send_text_message(wxid, error_msg)
                    else:
                        await bot.send_at_message(wxid, "❌ 无法提取图片下载参数", [user_wxid])
                    return

                # 使用API下载图片
                image_base64 = await bot.download_image(aeskey, cdnmidimgurl)
                if not image_base64:
                    if self.natural_response:
                        error_msg = random.choice(self.error_msgs)
                        await bot.send_text_message(wxid, error_msg)
                    else:
                        await bot.send_at_message(wxid, "❌ 下载图片失败", [user_wxid])
                    return

                # 保存图片到临时文件
                temp_file = self.temp_dir / f"quoted_image_{int(time.time())}.jpg"
                image_data = base64.b64decode(image_base64)
                with open(temp_file, "wb") as f:
                    f.write(image_data)

                # 处理图片
                await self._process_image_flow_without_notification(bot, wxid, user_wxid, prompt, str(temp_file))

                # 处理完成后清理临时文件（延迟10秒）
                self._cleanup_temp_file(str(temp_file), delay_seconds=10)

            except ET.ParseError as e:
                logger.error(f"[{self.plugin_name}] 解析XML失败: {str(e)}")
                await bot.send_at_message(
                    wxid,
                    "❌ 解析图片信息失败",
                    [user_wxid]
                )
                return

        except Exception as e:
            logger.error(f"[{self.plugin_name}] 处理引用图片异常: {str(e)}")
            await bot.send_at_message(
                wxid,
                f"❌ 处理过程中出现错误: {str(e)}",
                [user_wxid]
            )

    @on_emoji_message
    async def handle_emoji(self, bot: WechatAPIClient, message: dict):
        """处理表情消息，缓存表情信息以支持后续的引用识图"""
        if not self.enable:
            return

        try:
            msg_id = message.get('NewMsgId') or message.get('MsgId')
            if not msg_id:
                return

            # 提取表情信息
            emoji_info = {
                'EmojiUrl': message.get('EmojiUrl'),
                'EmojiMD5': message.get('EmojiMD5'),
                'EmojiLen': message.get('EmojiLen'),
                'Content': message.get('Content', ''),
                'CreateTime': message.get('CreateTime'),
                'SenderWxid': message.get('SenderWxid')
            }

            # 从Content中提取aeskey
            content = message.get('Content', '')
            if 'aeskey=' in content:
                try:
                    aeskey_start = content.find('aeskey="') + 8
                    aeskey_end = content.find('"', aeskey_start)
                    if aeskey_start > 7 and aeskey_end > aeskey_start:
                        emoji_info['aeskey'] = content[aeskey_start:aeskey_end]
                except Exception:
                    pass

            # 缓存表情信息
            self.emoji_message_cache[str(msg_id)] = emoji_info

            # 限制缓存大小，避免内存泄漏
            if len(self.emoji_message_cache) > 1000:
                # 删除最旧的100个缓存项
                oldest_keys = list(self.emoji_message_cache.keys())[:100]
                for key in oldest_keys:
                    del self.emoji_message_cache[key]

        except Exception as e:
            logger.error(f"[{self.plugin_name}] 处理表情消息缓存时出错: {str(e)}")

    async def _handle_emoji_quote(self, bot: WechatAPIClient, wxid: str, user_wxid: str,
                                 quoted_msg_id: str, prompt: str):
        """处理表情消息引用

        Args:
            bot: 微信API客户端
            wxid: 聊天ID
            user_wxid: 用户微信ID
            quoted_msg_id: 被引用的消息ID
            prompt: 提示词
        """
        try:
            # 从缓存中获取表情信息
            emoji_info = self.emoji_message_cache.get(quoted_msg_id)
            if not emoji_info:
                await bot.send_at_message(
                    wxid,
                    "❌ 表情消息信息已过期，请重新发送表情后再引用",
                    [user_wxid]
                )
                return

            # 下载表情文件
            emoji_file = await self._download_emoji(bot, emoji_info)
            if not emoji_file:
                await bot.send_at_message(
                    wxid,
                    "❌ 表情下载失败",
                    [user_wxid]
                )
                return

            # 转换表情为可识图的图片
            image_file = self._process_emoji_to_image(emoji_file)
            if not image_file:
                await bot.send_at_message(
                    wxid,
                    "❌ 表情处理失败，无法提取图片帧",
                    [user_wxid]
                )
                return

            # 调用图生图处理流程
            await self._process_image_flow_without_notification(bot, wxid, user_wxid, prompt, image_file)

            # 处理完成后清理表情相关的临时文件（延迟15秒）
            files_to_cleanup = [emoji_file]
            if image_file != emoji_file:
                files_to_cleanup.append(image_file)
            self._cleanup_temp_files(files_to_cleanup, delay_seconds=15)

        except Exception as e:
            logger.error(f"[{self.plugin_name}] 处理表情引用时出错: {str(e)}")
            await bot.send_at_message(
                wxid,
                f"❌ 处理表情时出现错误: {str(e)}",
                [user_wxid]
            )

    async def _process_image_flow(self, bot, wxid, user_wxid, prompt, image_path):
        """豆绘AI图生图处理流程"""
        try:
            # 步骤1: 验证图片文件
            if not os.path.exists(image_path):
                raise Exception(f"图片文件不存在: {image_path}")

            # 步骤2: 调用豆绘AI图生图接口
            results, error_msg = await self._call_douhui_image_to_image(prompt, image_path)

            if results:
                # 直接发送图片，不发送文字响应
                await self._send_results(bot, wxid, user_wxid, results, prompt)
            else:
                # 根据具体错误信息提供用户友好的提示
                if error_msg:
                    await bot.send_at_message(wxid, f"❌ {error_msg}", [user_wxid])
                else:
                    await bot.send_at_message(
                        wxid,
                        "豆绘AI处理失败，等会再试试吧",
                        [user_wxid]
                    )

        except Exception as e:
            logger.error(f"[{self.plugin_name}] 处理失败: {str(e)}")
            await bot.send_at_message(
                wxid,
                f"❌ 处理失败: {str(e)}",
                [user_wxid]
            )

    async def _process_image_flow_without_notification(self, bot, wxid, user_wxid, prompt, image_path):
        """豆绘AI图生图处理流程(无通知版本)"""
        try:
            # 步骤1: 验证图片文件
            if not os.path.exists(image_path):
                raise Exception(f"图片文件不存在: {image_path}")

            # 步骤2: 调用豆绘AI图生图接口
            results, error_msg = await self._call_douhui_image_to_image(prompt, image_path)

            if results:
                # 直接发送图片，不发送文字响应
                await self._send_results(bot, wxid, user_wxid, results, prompt)
            else:
                # 根据具体错误信息提供用户友好的提示
                if error_msg:
                    await bot.send_at_message(wxid, f"❌ {error_msg}", [user_wxid])
                else:
                    await bot.send_at_message(
                        wxid,
                        "豆绘AI处理失败，等会再试试吧",
                        [user_wxid]
                    )

        except Exception as e:
            logger.error(f"[{self.plugin_name}] 处理失败: {str(e)}")
            await bot.send_at_message(
                wxid,
                f"❌ 处理失败: {str(e)}",
                [user_wxid]
            )

    async def _call_douhui_image_to_image(self, prompt: str, image_path: str) -> Tuple[Optional[List[Dict]], Optional[str]]:
        """调用豆绘AI图生图接口"""
        try:
            # 检查API配置
            if not self.api_key:
                logger.error(f"[{self.plugin_name}] 豆绘AI API密钥未配置")
                return None, "API密钥未配置"

            # 创建豆绘AI生成器实例
            generator = DouHuiImageGenerator(self.api_key)

            # 调用完整的图片处理流程
            result_url, error_msg = await generator.process_image(
                image_path,
                prompt
            )

            # 图生图完成后清理临时文件（延迟10秒，确保处理完成）
            if image_path.startswith(str(self.temp_dir)):
                self._cleanup_temp_file(image_path, delay_seconds=10)

            if result_url:
                return [{
                    "url": result_url,
                    "width": 1024,
                    "height": 1024,
                    "format": "jpg",
                    "text_response": None  # 不返回文字响应
                }], None
            else:
                return None, error_msg or "处理失败，原因未知"

        except Exception as e:
            logger.error(f"[{self.plugin_name}] 调用豆绘AI异常: {str(e)}")
            return None, f"处理异常: {str(e)}"

    async def _send_results(self, bot, wxid: str, user_wxid: str, results: List[Dict], prompt: str = None) -> None:
        """发送生成结果

        Args:
            bot: 微信机器人API客户端
            wxid: 聊天ID
            user_wxid: 用户微信ID
            results: 生成的图片结果列表
            prompt: 原始提示词
        """
        try:
            # 下载并发送每张图片，不发送提示信息
            success_count = 0
            for i, image_info in enumerate(results):
                image_url = image_info.get("url")
                if not image_url:
                    continue

                try:
                    # 下载图片
                    async with httpx.AsyncClient(timeout=30) as client:
                        response = await client.get(image_url)
                        response.raise_for_status()

                        # 获取图片二进制数据
                        image_data = response.content

                        # 验证图片大小
                        if len(image_data) < 1024:  # 小于1KB可能有问题
                            raise ValueError(f"下载的图片数据过小: {len(image_data)}字节")

                        # 使用send_image_message直接发送图片数据而不是文件路径
                        result = await bot.send_image_message(wxid, image_data)

                        if result and result[2] != 0:  # new_msg_id不为0表示发送成功
                            success_count += 1
                        else:
                            # 备用方案：保存到临时文件并使用send_file_message
                            temp_file = self.temp_dir / f"result_{int(time.time())}_{i}.jpg"
                            with open(temp_file, "wb") as f:
                                f.write(image_data)

                            file_result = await bot.send_file_message(wxid, str(temp_file))
                            if file_result and file_result[2] != 0:
                                success_count += 1

                        # 短暂延迟，避免发送过快
                        await asyncio.sleep(1.5)

                except Exception as e:
                    logger.error(f"[{self.plugin_name}] 发送图片 {i+1}/{len(results)} 失败: {str(e)}")

            # 如果所有图片都发送失败，才通知用户
            if success_count == 0:
                await bot.send_at_message(
                    wxid,
                    "图片都发不出去，网络有问题",
                    [user_wxid]
                )

        except Exception as e:
            logger.error(f"[{self.plugin_name}] 发送结果异常: {str(e)}")
            await bot.send_at_message(
                wxid,
                f"❌ 发送结果失败: {str(e)}",
                [user_wxid]
            )

    # 注意：临时文件清理现在由统一的TempFileManager处理，清理方法已移除

    async def _send_usage_instructions(self, bot, wxid, user_wxid):
        """发送使用说明"""
        usage_message = f"""🎨 豆绘AI插件使用说明

📝 图生图命令格式:
• {self.command_format} - 标准命令格式
• 豆绘测试 [提示词] - 使用默认测试图片
• 豆绘测试 - 使用默认提示词和默认测试图片
• {self.quote_command_format} - 引用图片进行处理

💡 提示词示例:
• 换成古装
• 变成动漫风格
• 添加花朵背景
• 变成黑白照片

🆕 使用示例:
• 豆绘测试 二次元风格
• 引用图片 + "豆绘 油画风格"
• 引用图片 + "豆绘 动漫风格"

📸 默认测试图片:
C:\\DBE25C6475AF6852691B040206E94167.jpg

⚡ 豆绘AI特色:
• 高质量图像生成
• 快速处理速度
• 多种风格支持

⏱️ 处理时间:
约30-60秒，请耐心等待结果"""

        await bot.send_at_message(
            wxid,
            usage_message,
            [user_wxid]
        )
