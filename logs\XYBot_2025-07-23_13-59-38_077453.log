2025-07-23 13:59:39 | SUCCESS | 读取主设置成功
2025-07-23 13:59:39 | DEBUG | 最终使用的 Redis 主机地址: 127.0.0.1
2025-07-23 13:59:39 | INFO | 2025/07/23 13:59:39 GetRedisAddr: 127.0.0.1:6379
2025-07-23 13:59:39 | INFO | 2025/07/23 13:59:39 Redis连接成功 | 地址:127.0.0.1:6379 DB:0
2025-07-23 13:59:39 | INFO | 2025/07/23 13:59:39 Server start at :9000
2025-07-23 13:59:40 | SUCCESS | WechatAPI服务已启动
2025-07-23 13:59:40 | INFO | 登录账号信息: wxid: wxid_4usgcju5ey9q29  昵称: 瑶瑶  微信号: Today-KFC  手机号: None
2025-07-23 13:59:40 | INFO | 登录设备信息: device_name: <PERSON>'s Pad  device_id: 49d050a7e94d1ecc40790d74b1c4369f
2025-07-23 13:59:40 | SUCCESS | 登录成功
2025-07-23 13:59:40 | SUCCESS | 已开启自动心跳
2025-07-23 13:59:40 | INFO | 成功加载表情映射文件，共 522 条记录
2025-07-23 13:59:40 | SUCCESS | 数据库初始化成功
2025-07-23 13:59:40 | SUCCESS | 定时任务已启动
2025-07-23 13:59:40 | SUCCESS | 插件 AISignIn 已加载定时任务: {'plugins.AISignIn.main.AISignIn.auto_signin_task'}
2025-07-23 13:59:41 | ERROR | 加载 AppImageTester 时发生错误: Traceback (most recent call last):
  File "C:\XYBotV2\utils\plugin_manager.py", line 94, in load_plugins_from_directory
    module = importlib.import_module(f"plugins.{dirname}.main")
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Program Files\Python311\Lib\importlib\__init__.py", line 126, in import_module
    return _bootstrap._gcd_import(name[level:], package, level)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "<frozen importlib._bootstrap>", line 1204, in _gcd_import
  File "<frozen importlib._bootstrap>", line 1176, in _find_and_load
  File "<frozen importlib._bootstrap>", line 1147, in _find_and_load_unlocked
  File "<frozen importlib._bootstrap>", line 690, in _load_unlocked
  File "<frozen importlib._bootstrap_external>", line 940, in exec_module
  File "<frozen importlib._bootstrap>", line 241, in _call_with_frames_removed
  File "C:\XYBotV2\plugins\AppImageTester\main.py", line 9, in <module>
    from resource.base.plugin_base import PluginBase
ModuleNotFoundError: No module named 'resource.base'

2025-07-23 13:59:41 | SUCCESS | 已加载插件: False
2025-07-23 13:59:41 | INFO | 处理堆积消息中
2025-07-23 13:59:41 | SUCCESS | 处理堆积消息完毕
2025-07-23 13:59:41 | SUCCESS | 开始处理消息
2025-07-23 14:00:17 | DEBUG | 收到消息: {'MsgId': 952333997, 'FromUserName': {'string': '51595225532@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 1, 'Content': {'string': 'wxid_m1lh16d8hfl221:\n@\xa0  Ooo゛🍭\u2005来不'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1753250433, 'MsgSource': '<msgsource>\n\t<atuserlist>wxid_qk4ejsujsz9a21</atuserlist>\n\t<bizflag>0</bizflag>\n\t<pua>1</pua>\n\t<alnode>\n\t\t<inlenlist>11</inlenlist>\n\t</alnode>\n\t<eggIncluded>1</eggIncluded>\n\t<silence>0</silence>\n\t<membercount>10</membercount>\n\t<signature>N0_V1_7cX9aZTK|v1_7OsK5LEG</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'PushContent': 'Aurora : @\xa0  Ooo゛🍭\u2005来不', 'NewMsgId': 5010183302729001911, 'MsgSeq': 871394256}
2025-07-23 14:00:17 | INFO | 收到文本消息: 消息ID:952333997 来自:51595225532@chatroom 发送人:wxid_m1lh16d8hfl221 @:['wxid_qk4ejsujsz9a21'] 内容:@   Ooo゛🍭 来不
2025-07-23 14:00:21 | DEBUG | 收到消息: {'MsgId': 1055875048, 'FromUserName': {'string': '51595225532@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 1, 'Content': {'string': 'wxid_m1lh16d8hfl221:\n两点了'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1753250437, 'MsgSource': '<msgsource>\n\t<bizflag>0</bizflag>\n\t<pua>1</pua>\n\t<eggIncluded>1</eggIncluded>\n\t<silence>0</silence>\n\t<membercount>10</membercount>\n\t<signature>N0_V1_jRGm5+Xm|v1_iEIDmd06</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'PushContent': 'Aurora : 两点了', 'NewMsgId': 5536590839626040002, 'MsgSeq': 871394257}
2025-07-23 14:00:21 | INFO | 收到文本消息: 消息ID:1055875048 来自:51595225532@chatroom 发送人:wxid_m1lh16d8hfl221 @:[] 内容:两点了
2025-07-23 14:00:41 | DEBUG | 收到消息: {'MsgId': 231974529, 'FromUserName': {'string': '51595225532@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 1, 'Content': {'string': 'wxid_ubbh6q832tcs21:\n真特么准时'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1753250457, 'MsgSource': '<msgsource>\n\t<pua>1</pua>\n\t<eggIncluded>1</eggIncluded>\n\t<silence>0</silence>\n\t<membercount>10</membercount>\n\t<signature>N0_V1_VH3rUXYg|v1_2QZRApmO</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'PushContent': '群最帅 : 真特么准时', 'NewMsgId': 5975322298579602021, 'MsgSeq': 871394258}
2025-07-23 14:00:41 | INFO | 收到文本消息: 消息ID:231974529 来自:51595225532@chatroom 发送人:wxid_ubbh6q832tcs21 @:[] 内容:真特么准时
2025-07-23 14:01:04 | DEBUG | 收到消息: {'MsgId': 2045691176, 'FromUserName': {'string': '51595225532@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 1, 'Content': {'string': 'wxid_m1lh16d8hfl221:\n@群最帅\u2005你上夜班还是白班'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1753250479, 'MsgSource': '<msgsource>\n\t<atuserlist>wxid_ubbh6q832tcs21</atuserlist>\n\t<bizflag>0</bizflag>\n\t<pua>1</pua>\n\t<eggIncluded>1</eggIncluded>\n\t<silence>0</silence>\n\t<membercount>10</membercount>\n\t<signature>N0_V1_NUZ6LMGU|v1_/X93ofaW</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'PushContent': 'Aurora : @群最帅\u2005你上夜班还是白班', 'NewMsgId': 5677372193194234582, 'MsgSeq': 871394259}
2025-07-23 14:01:04 | INFO | 收到文本消息: 消息ID:2045691176 来自:51595225532@chatroom 发送人:wxid_m1lh16d8hfl221 @:['wxid_ubbh6q832tcs21'] 内容:@群最帅 你上夜班还是白班
2025-07-23 14:01:11 | DEBUG | 收到消息: {'MsgId': 2062945638, 'FromUserName': {'string': '51595225532@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 1, 'Content': {'string': 'wxid_ubbh6q832tcs21:\n白'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1753250487, 'MsgSource': '<msgsource>\n\t<pua>1</pua>\n\t<eggIncluded>1</eggIncluded>\n\t<silence>0</silence>\n\t<membercount>10</membercount>\n\t<signature>N0_V1_Cv0PwB+E|v1_+rnCjbCe</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'PushContent': '群最帅 : 白', 'NewMsgId': 274104205710948836, 'MsgSeq': 871394260}
2025-07-23 14:01:11 | INFO | 收到文本消息: 消息ID:2062945638 来自:51595225532@chatroom 发送人:wxid_ubbh6q832tcs21 @:[] 内容:白
