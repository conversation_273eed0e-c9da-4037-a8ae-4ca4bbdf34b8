2025-07-20 02:56:44 | SUCCESS | 读取主设置成功
2025-07-20 02:56:44 | DEBUG | 最终使用的 Redis 主机地址: 127.0.0.1
2025-07-20 02:56:44 | INFO | 2025/07/20 02:56:44 GetRedisAddr: 127.0.0.1:6379
2025-07-20 02:56:44 | INFO | 2025/07/20 02:56:44 Redis连接成功 | 地址:127.0.0.1:6379 DB:0
2025-07-20 02:56:44 | INFO | 2025/07/20 02:56:44 Server start at :9000
2025-07-20 02:56:44 | SUCCESS | WechatAPI服务已启动
2025-07-20 02:56:45 | INFO | 登录账号信息: wxid: wxid_4usgcju5ey9q29  昵称: 瑶瑶  微信号: Today-KFC  手机号: None
2025-07-20 02:56:45 | INFO | 登录设备信息: device_name: <PERSON>'s Pad  device_id: 49d050a7e94d1ecc40790d74b1c4369f
2025-07-20 02:56:45 | SUCCESS | 登录成功
2025-07-20 02:56:45 | SUCCESS | 已开启自动心跳
2025-07-20 02:56:45 | INFO | 成功加载表情映射文件，共 515 条记录
2025-07-20 02:56:45 | SUCCESS | 数据库初始化成功
2025-07-20 02:56:45 | SUCCESS | 定时任务已启动
2025-07-20 02:56:45 | ERROR | 加载插件模块 admin_point 时发生错误: Traceback (most recent call last):
  File "C:\XYBotV2\utils\plugin_manager.py", line 102, in load_plugins_from_directory
    module = importlib.import_module(f"plugins.{module_name}")
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Program Files\Python311\Lib\importlib\__init__.py", line 126, in import_module
    return _bootstrap._gcd_import(name[level:], package, level)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "<frozen importlib._bootstrap>", line 1204, in _gcd_import
  File "<frozen importlib._bootstrap>", line 1176, in _find_and_load
  File "<frozen importlib._bootstrap>", line 1126, in _find_and_load_unlocked
  File "<frozen importlib._bootstrap>", line 241, in _call_with_frames_removed
  File "<frozen importlib._bootstrap>", line 1204, in _gcd_import
  File "<frozen importlib._bootstrap>", line 1176, in _find_and_load
  File "<frozen importlib._bootstrap>", line 1147, in _find_and_load_unlocked
  File "<frozen importlib._bootstrap>", line 690, in _load_unlocked
  File "<frozen importlib._bootstrap_external>", line 940, in exec_module
  File "<frozen importlib._bootstrap>", line 241, in _call_with_frames_removed
  File "C:\XYBotV2\plugins\__init__.py", line 6, in <module>
    from plugins.video_parser import VideoParserPlugin
  File "C:\XYBotV2\plugins\video_parser.py", line 23, in <module>
    from plugins.url_shortener import URLShortenerService
  File "C:\XYBotV2\plugins\url_shortener.py", line 3, in <module>
    import pyshorteners
ModuleNotFoundError: No module named 'pyshorteners'

2025-07-20 02:56:45 | SUCCESS | 已加载插件: False
2025-07-20 02:56:45 | INFO | 处理堆积消息中
2025-07-20 02:56:45 | SUCCESS | 处理堆积消息完毕
2025-07-20 02:56:45 | SUCCESS | 开始处理消息
