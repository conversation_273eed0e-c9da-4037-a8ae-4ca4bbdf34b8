
#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
豆包AI播客功能API调用脚本
基于抓包分析实现完整的播客生成和获取功能
"""

import requests
import json
import time
import uuid
import base64
from urllib.parse import urlencode, quote
import re


class DoubaopodcasterAPI:
    def __init__(self):
        self.base_url = "https://www.doubao.com"
        self.session = requests.Session()
        
        # 从抓包数据中提取的关键参数
        self.device_params = {
            "version_code": "20800",
            "language": "zh", 
            "device_platform": "web",
            "aid": "497858",
            "real_aid": "497858",
            "pkg_type": "release_version",
            "device_id": "7468716989062841895",
            "web_id": "7468716986638386703",
            "tea_uuid": "7468716986638386703",
            "use-olympus-account": "1",
            "region": "CN",
            "sys_region": "CN",
            "samantha_web": "1",
            "pc_version": "2.24.2"
        }
        
        # 请求头模板
        self.headers = {
            "User-Agent": "Mozilla/5.0 (Linux; Android 10; V2002A Build/QP1A.190711.020) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/83.0.4103.106 Mobile Safari/537.36",
            "Accept": "application/json, text/plain, */*",
            "Content-Type": "application/json",
            "Origin": "https://www.doubao.com",
            "X-Requested-With": "mark.via",
            "Sec-Fetch-Site": "same-origin",
            "Sec-Fetch-Mode": "cors",
            "Sec-Fetch-Dest": "empty",
            "Accept-Encoding": "gzip, deflate",
            "Accept-Language": "zh-CN,zh;q=0.9,en-US;q=0.8,en;q=0.7"
        }
        
        # 需要用户提供的认证信息
        self.auth_cookies = {}

    def set_auth_info(self, cookies_str):
        """
        设置认证信息

        Args:
            cookies_str: 完整的Cookie字符串
        """
        # 解析Cookie字符串
        for cookie in cookies_str.split(';'):
            if '=' in cookie:
                key, value = cookie.strip().split('=', 1)
                self.auth_cookies[key] = value

        # 设置session的cookies
        self.session.cookies.update(self.auth_cookies)

        print("认证信息设置完成")
    
    def get_link_metadata(self, url):
        """
        获取链接元数据

        Args:
            url: 要分析的链接URL

        Returns:
            dict: 链接元数据信息
        """
        params = self.device_params.copy()

        api_url = f"{self.base_url}/samantha/resource/link/sniff?" + urlencode(params)
        
        payload = {
            "links": [url],
            "with_link_meta": True
        }
        
        headers = self.headers.copy()
        headers["Referer"] = f"{self.base_url}/chat/local_7015820190817150?type=2"
        
        try:
            response = self.session.post(api_url, json=payload, headers=headers)
            response.raise_for_status()

            result = response.json()
            print(f"链接元数据响应: {json.dumps(result, ensure_ascii=False, indent=2)}")

            if result.get("code") == 0:
                return result.get("data", {})
            else:
                print(f"获取链接元数据失败: {result.get('msg', '未知错误')}")
                return None

        except Exception as e:
            print(f"获取链接元数据时发生错误: {str(e)}")
            return None
    
    def generate_podcast(self, url, prompt="生成播客"):
        """
        生成播客

        Args:
            url: 要生成播客的链接URL
            prompt: 生成提示词，默认为"生成播客"

        Returns:
            dict: 包含播客ID和相关信息的字典
        """
        params = self.device_params.copy()

        api_url = f"{self.base_url}/samantha/chat/completion?" + urlencode(params)
        
        # 生成本地会话ID
        local_conversation_id = f"local_{int(time.time() * 1000)}"
        local_message_id = str(uuid.uuid4())
        
        payload = {
            "messages": [{
                "content": json.dumps({"text": prompt}),
                "content_type": 2034,
                "attachments": [{
                    "type": "link",
                    "key": url,
                    "url": url
                }]
            }],
            "completion_option": {
                "is_regen": False,
                "with_suggest": True,
                "need_create_conversation": True,
                "launch_stage": 1,
                "is_replace": False,
                "is_delete": False,
                "message_from": 0,
                "use_auto_cot": False,
                "resend_for_regen": False,
                "event_id": "0"
            },
            "conversation_id": "0",
            "local_conversation_id": local_conversation_id,
            "local_message_id": local_message_id
        }
        
        headers = self.headers.copy()
        headers.update({
            "content-type": "application/json",
            "last-event-id": "undefined",
            "Agw-Js-Conv": "str",
            "Accept": "*/*",
            "Referer": f"{self.base_url}/chat/{local_conversation_id}?type=2"
        })
        
        try:
            print(f"正在发送播客生成请求...")

            response = self.session.post(api_url, json=payload, headers=headers, stream=True)
            print(f"响应状态码: {response.status_code}")

            if response.status_code != 200:
                print(f"请求失败，状态码: {response.status_code}")
                print(f"响应内容: {response.text[:500]}")
                return None

            print("✅ 播客生成请求发送成功")

            # 从SSE响应中提取conversation_id
            conversation_id = None
            for line in response.iter_lines(decode_unicode=True):
                if line and line.startswith('data: '):
                    try:
                        data_str = line[6:]  # 去掉 'data: ' 前缀
                        if data_str and data_str != '{}':
                            data = json.loads(data_str)
                            if 'event_data' in data:
                                event_data = json.loads(data.get('event_data', '{}'))
                                if 'conversation_id' in event_data:
                                    conversation_id = event_data['conversation_id']
                                    print(f"✅ 提取到对话ID: {conversation_id}")
                                    break
                    except:
                        continue

            print("✅ 播客生成完成，准备获取音频链接...")

            # 返回对话ID，用于后续获取消息
            return {
                'conversation_id': conversation_id,
                'local_conversation_id': local_conversation_id,
                'local_message_id': local_message_id
            }

        except Exception as e:
            print(f"生成播客时发生错误: {str(e)}")
            return None
    
    def _parse_sse_response(self, response):
        """
        解析Server-Sent Events响应

        Args:
            response: requests响应对象

        Returns:
            dict: 解析出的播客信息
        """
        podcast_info = {}
        line_count = 0

        print("开始解析SSE响应...")

        for line in response.iter_lines(decode_unicode=True):
            line_count += 1
            if line and line.startswith('data: '):
                try:
                    data_str = line[6:]  # 去掉 'data: ' 前缀
                    if not data_str or data_str == '{}':
                        continue

                    if line_count == 6:
                        print(f"=== 第6行完整数据 ===")
                        print(data_str)
                        print("=== 第6行数据结束 ===")
                    else:
                        print(f"处理第{line_count}行: {data_str[:100]}...")

                    data = json.loads(data_str)

                    # 检查是否有错误信息
                    if 'event_data' in data:
                        event_data_str = data.get('event_data', '{}')
                        if event_data_str:
                            event_data = json.loads(event_data_str)

                            # 检查错误信息
                            if 'code' in event_data and event_data.get('code') != 0:
                                print(f"❌ 服务器返回错误: {event_data}")
                                continue

                            # 查找播客widget信息
                            message = event_data.get('message', {})

                            if isinstance(message, dict) and message.get('content_type') == 2035:
                                print("✅ 找到播客widget信息")
                                content_str = message.get('content', '{}')
                                if not content_str or content_str == '{}':
                                    print("⚠️ content为空，跳过此条消息")
                                    continue

                                try:
                                    content = json.loads(content_str)
                                    widget_data_str = content.get('widget_data', '{}')
                                    if not widget_data_str:
                                        print("⚠️ widget_data为空，跳过此条消息")
                                        continue

                                    widget_data = json.loads(widget_data_str)
                                    data_inner_str = widget_data.get('data', '{}')
                                    if not data_inner_str:
                                        print("⚠️ data为空，跳过此条消息")
                                        continue

                                    data_inner = json.loads(data_inner_str)

                                    episode_list = data_inner.get('episodeList', {})
                                    episodes = episode_list.get('episodes', [])

                                    if episodes:
                                        episode = episodes[0]
                                        audio_link = episode.get('meta', {}).get('playback_model', {}).get('audio_link')
                                        podcast_info = {
                                            'episode_id': episode.get('id'),
                                            'audio_link': audio_link,
                                            'conversation_id': event_data.get('conversation_id'),
                                            'message_id': event_data.get('message_id')
                                        }
                                        print(f"✅ 播客生成成功，ID: {podcast_info['episode_id']}")
                                        print(f"✅ 音频链接: {audio_link}")
                                        break
                                    else:
                                        print("⚠️ episodes列表为空")

                                except json.JSONDecodeError as je:
                                    print(f"⚠️ JSON解析失败，但继续处理: {str(je)}")
                                    # 如果是空的content，继续处理下一条消息
                                    continue

                except (json.JSONDecodeError, KeyError, TypeError) as e:
                    print(f"解析第{line_count}行时出错: {str(e)}")

                    # 尝试从原始数据中提取播客信息
                    if 'com.flow.podcast' in data_str and 'episodeIds' in data_str:
                        print("🔍 尝试从原始数据中提取播客信息...")
                        try:
                            # 使用正则表达式提取播客ID
                            import re
                            episode_id_match = re.search(r'"episodeIds":\["(\d+)"\]', data_str)
                            audio_link_match = re.search(r'"audio_link":"([^"]+)"', data_str)

                            if episode_id_match and audio_link_match:
                                episode_id = episode_id_match.group(1)
                                audio_link = audio_link_match.group(1).replace('\\u0026', '&')

                                podcast_info = {
                                    'episode_id': episode_id,
                                    'audio_link': audio_link,
                                    'conversation_id': 'extracted',
                                    'message_id': 'extracted'
                                }
                                print(f"✅ 从原始数据提取播客成功，ID: {episode_id}")
                                print(f"✅ 音频链接: {audio_link}")
                                break
                        except Exception as extract_error:
                            print(f"❌ 原始数据提取失败: {str(extract_error)}")

                    continue

        print(f"总共处理了{line_count}行SSE数据")
        return podcast_info

    def get_conversation_messages(self, conversation_id):
        """
        获取对话消息列表，从中提取播客音频链接

        Args:
            conversation_id: 对话ID

        Returns:
            dict: 包含播客信息的字典
        """
        params = self.device_params.copy()

        api_url = f"{self.base_url}/alice/message/list?" + urlencode(params)

        payload = {
            "conversation_id": conversation_id,
            "cursor": 9007199254740991,  # 从最新消息开始
            "batch_size": 50
        }

        headers = self.headers.copy()
        headers["Referer"] = f"{self.base_url}/chat/{conversation_id}"

        try:
            print(f"正在获取对话消息...")
            response = self.session.post(api_url, json=payload, headers=headers)
            response.raise_for_status()

            result = response.json()
            if result.get("code") == 0:
                messages = result.get("data", {}).get("message_list", [])

                # 查找包含播客信息的消息
                for message in messages:
                    if message.get("content_type") == 100:  # 播客widget消息
                        content_str = message.get("content", "{}")
                        try:
                            content = json.loads(content_str)
                            if content.get("applet_id") == "com.flow.podcast":
                                # 解析播客数据
                                widget_data_str = content.get("widget_data", "{}")
                                widget_data = json.loads(widget_data_str)
                                data_str = widget_data.get("data", "{}")
                                data = json.loads(data_str)

                                episode_list = data.get("episodeList", {})
                                episodes = episode_list.get("episodes", [])

                                if episodes:
                                    episode = episodes[0]
                                    meta = episode.get("meta", {})
                                    playback_model = meta.get("playback_model", {})

                                    podcast_info = {
                                        'episode_id': episode.get('id'),
                                        'audio_link': playback_model.get('audio_link'),
                                        'duration': playback_model.get('duration'),
                                        'title': meta.get('title'),
                                        'conversation_id': message.get('conversation_id'),
                                        'message_id': message.get('message_id')
                                    }

                                    print(f"✅ 找到播客信息，ID: {podcast_info['episode_id']}")
                                    return podcast_info

                        except (json.JSONDecodeError, KeyError):
                            continue

                print("❌ 未在消息中找到播客信息")
                return None
            else:
                print(f"获取消息列表失败: {result.get('msg', '未知错误')}")
                return None

        except Exception as e:
            print(f"获取对话消息时发生错误: {str(e)}")
            return None

    def get_podcast_detail(self, episode_id):
        """
        获取播客详细信息，包括最终的音频链接

        Args:
            episode_id: 播客集ID

        Returns:
            dict: 包含完整播客信息的字典，包括最终音频链接
        """
        params = self.device_params.copy()
        api_url = f"{self.base_url}/api/doubao/do_action_v2?" + urlencode(params)

        payload = {
            "scene": "FPA_Podcast",
            "payload": json.dumps({
                "api_name": "GetGenPodcastDetail",
                "params": json.dumps({
                    "id": episode_id
                })
            })
        }

        headers = self.headers.copy()
        headers["Referer"] = f"{self.base_url}/chat/9742980049534466"

        try:
            print(f"正在获取播客详细信息，episode_id: {episode_id}")
            response = self.session.post(api_url, json=payload, headers=headers)
            response.raise_for_status()

            result = response.json()
            print(f"GetGenPodcastDetail响应: {json.dumps(result, ensure_ascii=False, indent=2)}")

            if result.get("code") == 0 and result.get("data", {}).get("success"):
                resp_str = result.get("data", {}).get("resp", "{}")
                resp_data = json.loads(resp_str)

                episode = resp_data.get("episode", {})
                if episode:
                    meta = episode.get("meta", {})
                    playback_model = meta.get("playback_model", {})

                    podcast_info = {
                        'episode_id': episode.get('id'),
                        'title': meta.get('title'),
                        'cover_link': meta.get('cover_link'),
                        'audio_link': playback_model.get('audio_link'),
                        'duration': playback_model.get('duration'),
                        'podcast_title': meta.get('podcast_title'),
                        'podcast_cover': meta.get('podcast_cover')
                    }

                    print(f"✅ 获取播客详细信息成功")
                    print(f"标题: {podcast_info['title']}")
                    print(f"时长: {podcast_info['duration']}秒")
                    print(f"音频链接: {podcast_info['audio_link']}")

                    return podcast_info
                else:
                    print("❌ 响应中没有episode信息")
                    return None
            else:
                print(f"获取播客详细信息失败: {result.get('msg', '未知错误')}")
                return None

        except Exception as e:
            print(f"获取播客详细信息时发生错误: {str(e)}")
            return None

    def poll_for_podcast_completion(self, conversation_id, initial_wait=180, max_attempts=30, interval=10):
        """
        轮询检测播客生成完成状态

        Args:
            conversation_id: 对话ID
            initial_wait: 初始等待时间（秒），默认3分钟
            max_attempts: 最大尝试次数
            interval: 轮询间隔（秒）

        Returns:
            dict: 包含完整播客信息的字典，如果超时则返回None
        """
        import time

        print(f"⏳ 等待{initial_wait}秒让播客充分生成...")
        time.sleep(initial_wait)

        print(f"开始轮询播客生成状态，最多尝试{max_attempts}次，间隔{interval}秒")

        for attempt in range(1, max_attempts + 1):
            print(f"第{attempt}次检查播客状态...")

            podcast_info = self.get_conversation_messages(conversation_id)

            if podcast_info:
                episode_id = podcast_info.get('episode_id')

                if episode_id:
                    print(f"✅ 找到播客episode_id: {episode_id}，正在获取详细信息...")

                    # 调用GetGenPodcastDetail获取最终的音频链接
                    detail_info = self.get_podcast_detail(episode_id)

                    if detail_info and detail_info.get('audio_link'):
                        audio_link = detail_info.get('audio_link')
                        if audio_link and audio_link.startswith('https://'):
                            print(f"✅ 播客生成完成！获取到最终音频链接")
                            return detail_info
                        else:
                            print(f"⏳ 播客正在生成中，音频链接未就绪: {audio_link}")
                    else:
                        print(f"⏳ 播客正在生成中，详细信息未就绪")
                else:
                    print(f"⏳ 播客正在生成中，episode_id未就绪")
            else:
                print(f"⏳ 播客正在生成中，未找到播客消息")

            if attempt < max_attempts:
                print(f"等待{interval}秒后重试...")
                time.sleep(interval)

        print(f"❌ 轮询超时，播客可能生成失败或需要更长时间")
        return None
    
    def get_podcast_audio(self, audio_url=None, episode_id=None, output_file="podcast_audio.mp3"):
        """
        获取播客音频数据

        Args:
            audio_url: 直接的音频下载链接（优先使用）
            episode_id: 播客集ID（备用方法）
            output_file: 输出文件名

        Returns:
            bool: 是否成功获取音频
        """
        # 方法1：直接下载音频文件（推荐）
        if audio_url:
            return self._download_audio_direct(audio_url, output_file)

        # 方法2：通过chunk端点获取（备用）
        elif episode_id:
            return self._download_audio_chunk(episode_id, output_file)

        else:
            print("需要提供 audio_url 或 episode_id")
            return False

    def _download_audio_direct(self, audio_url, output_file):
        """
        直接下载音频文件

        Args:
            audio_url: 音频文件URL
            output_file: 输出文件名

        Returns:
            bool: 是否成功下载
        """
        try:
            print(f"正在下载音频文件: {audio_url}")
            response = self.session.get(audio_url, stream=True)
            response.raise_for_status()

            with open(output_file, 'wb') as f:
                for chunk in response.iter_content(chunk_size=8192):
                    if chunk:
                        f.write(chunk)

            print(f"音频文件已保存为: {output_file}")
            return True

        except Exception as e:
            print(f"下载音频文件时发生错误: {str(e)}")
            return False

    def _download_audio_chunk(self, episode_id, output_file):
        """
        通过chunk端点获取音频数据（备用方法）

        Args:
            episode_id: 播客集ID
            output_file: 输出文件名

        Returns:
            bool: 是否成功获取音频
        """
        api_url = f"{self.base_url}/chat/podcast/chunk"
        params = {
            "resource_id": episode_id,
            "gen_type": "1",
            "encode_type": "text/event-stream"
        }

        headers = self.headers.copy()
        headers.update({
            "last-event-id": "undefined",
            "Accept": "*/*",
            "Referer": f"{self.base_url}/chat/9659892299310594"
        })

        try:
            response = self.session.post(f"{api_url}?{urlencode(params)}",
                                       headers=headers, stream=True)
            response.raise_for_status()

            # 解析音频数据流
            audio_data = self._parse_audio_stream(response)

            if audio_data:
                with open(output_file, 'wb') as f:
                    f.write(audio_data)
                print(f"音频文件已保存为: {output_file}")
                return True
            else:
                print("未能获取到音频数据")
                return False

        except Exception as e:
            print(f"获取播客音频时发生错误: {str(e)}")
            return False
    
    def _parse_audio_stream(self, response):
        """
        解析音频数据流
        
        Args:
            response: requests响应对象
            
        Returns:
            bytes: 音频数据
        """
        audio_chunks = []
        
        for line in response.iter_lines(decode_unicode=True):
            if line.startswith('data: '):
                # 音频数据通常是base64编码的
                audio_data = line[6:]  # 去掉 'data: ' 前缀
                if audio_data and audio_data != '{}':
                    try:
                        # 尝试解码base64数据
                        decoded_data = base64.b64decode(audio_data)
                        audio_chunks.append(decoded_data)
                    except:
                        # 如果不是base64，可能是其他格式的音频数据
                        audio_chunks.append(audio_data.encode())
        
        return b''.join(audio_chunks) if audio_chunks else None


def main():
    """
    主函数 - 使用示例
    """
    print("=== 豆包AI播客API测试脚本 ===")
    print()
    print("⚠️  重要提示：")
    print("1. 当前脚本使用的是示例认证信息，已经过期")
    print("2. 服务器返回了访问阻止错误 (code: 710022002)")
    print("3. 需要从浏览器获取最新的认证信息才能正常使用")
    print()
    print("📋 获取认证信息的步骤：")
    print("1. 在浏览器中登录豆包 (https://www.doubao.com)")
    print("2. 打开开发者工具 (F12)")
    print("3. 切换到 Network 标签页")
    print("4. 在豆包中生成一个播客")
    print("5. 找到任意一个请求")
    print("6. 复制请求头中的完整 Cookie 字符串")
    print("7. 替换脚本中的认证信息")
    print()
    print("🔧 API调用流程已实现：")
    print("✅ 1. 链接元数据获取 (/samantha/resource/link/sniff)")
    print("✅ 2. 播客生成 (/samantha/chat/completion)")
    print("✅ 3. 音频数据获取 (/chat/podcast/chunk)")
    print("✅ 4. SSE流响应解析")
    print("✅ 5. 音频文件保存")
    print()

    # 创建API实例
    api = DoubaopodcasterAPI()
    print("API实例创建成功")

    # 设置认证信息（使用最新的Cookie）
    cookies = "d_ticket=c387aa7a25690088d32e0f295a9faa5828090; odin_tt=adc3604237fa735eef5d9b2726da716e7ea5dd617e5184147618251da72231aa77815405b56975c824529748a009f1f85cf25c9d17928d11725b0ef3d9e34d33; uid_tt=71dd29fd50dbf8f1a1d7b3090d765234; uid_tt_ss=71dd29fd50dbf8f1a1d7b3090d765234; sid_tt=9b6a807194da06d0c111bb246ff92247; sessionid=9b6a807194da06d0c111bb246ff92247; sessionid_ss=9b6a807194da06d0c111bb246ff92247; is_staff_user=false; store-region=cn-cq; store-region-src=uid; x-web-secsdk-uid=619a41a9-98f6-4183-8efa-056da531a03c; i18next=zh; flow_ssr_timezone_offset=-480; sid_guard=9b6a807194da06d0c111bb246ff92247%7C1750354436%7C5184000%7CMon%2C+18-Aug-2025+17%3A33%3A56+GMT; sid_ucp_v1=1.0.0-KGRmMzU3ZjY5NDU1MmU0YzcyMDM1YzhiYzJiNDFiY2NlMjg5ZjI5ZGIKIAjZtNCbo82jBhCElNHCBhjCsR4gDDC57oy9BjgCQPEHGgJobCIgOWI2YTgwNzE5NGRhMDZkMGMxMTFiYjI0NmZmOTIyNDc; ssid_ucp_v1=1.0.0-KGRmMzU3ZjY5NDU1MmU0YzcyMDM1YzhiYzJiNDFiY2NlMjg5ZjI5ZGIKIAjZtNCbo82jBhCElNHCBhjCsR4gDDC57oy9BjgCQPEHGgJobCIgOWI2YTgwNzE5NGRhMDZkMGMxMTFiYjI0NmZmOTIyNDc; gd_random=eyJtYXRjaCI6dHJ1ZSwicGVyY2VudCI6MC43MzY3MDUwNjIwNjM5NTg2fQ==.uKgP5toZLh+25SHaZpshh4huBYJbX+IXwQLf8YIWQVM=; flow_ssr_sidebar_expand=1; ttwid=1%7Cnh818I2twXm5rLksuW8zNJtjX2M-xpfL2fl7tkNB2nU%7C1750567172%7Cf46a9d1540bcdb6ce2ffd1c32a9efde2d0216b96019d40383151f1b5cc8a364f; passport_fe_beating_status=true; tt_scid=H1B1dWf3TR.u.fg3-Fglo14HaFZYGdJzNzH2WWEOGZx.bFsc6uqHt9ZuX9MO-pVO72b8; msToken=n2Gqv563k6ebZr8P0JHhrrtP05V-eneewTQeo2PhO2c2Ox-08CGHIEuPG_G46EtcivGLJtbvabm9sn_SZvDXOhx7tD-28TsjcCz0PGNaE6eb4wMmpJyTTOPoLwDAKC2VtbgfqQkgAweUfg=="

    try:
        api.set_auth_info(cookies)

        # 要生成播客的链接
        url = "https://mp.weixin.qq.com/s/wjB64z7M0jJgu6ceWVry8w"
        print(f"目标链接: {url}")

        print("\n=== 步骤1: 获取链接元数据 ===")

        # 1. 获取链接元数据
        metadata = api.get_link_metadata(url)
        if metadata:
            results = metadata.get('results', [])
            if results:
                title = results[0].get('link_meta', {}).get('title', '未知')
                print(f"✅ 链接标题: {title}")
            else:
                print("❌ 未获取到链接元数据")
        else:
            print("❌ 获取链接元数据失败")

        print("\n=== 步骤2: 生成播客 ===")

        # 2. 生成播客
        generation_result = api.generate_podcast(url)
        if generation_result and generation_result.get('conversation_id'):
            print(f"✅ 播客生成请求成功！")

            # 3. 轮询获取播客信息
            print("\n=== 步骤3: 轮询获取播客音频链接 ===")

            # 使用从播客生成响应中提取的对话ID
            conversation_id = generation_result.get('conversation_id')
            print(f"使用对话ID: {conversation_id}")

            # 轮询检测播客生成状态（3分钟后开始，每30秒一次）
            podcast_info = api.poll_for_podcast_completion(conversation_id,
                                                          initial_wait=180,  # 3分钟
                                                          max_attempts=60,   # 60次尝试
                                                          interval=30)       # 每30秒

            if podcast_info and podcast_info.get('episode_id'):
                print(f"✅ 播客信息获取成功！")
                print(f"播客ID: {podcast_info['episode_id']}")
                print(f"标题: {podcast_info.get('title', '未知')}")
                print(f"时长: {podcast_info.get('duration', '未知')}秒")
                audio_link = podcast_info.get('audio_link', '未知')
                print(f"🎵 可直接播放的音频链接: {audio_link}")

                if audio_link != '未知':
                    print("\n✅ 获取到可直接播放的音频链接！")
                    print("您可以直接在浏览器或播放器中使用此链接")
                else:
                    print("\n❌ 未获取到音频链接")
            else:
                print("❌ 未能获取到播客信息")
        else:
            print("❌ 播客生成请求失败")

    except Exception as e:
        print(f"❌ 执行过程中发生错误: {str(e)}")
        import traceback
        traceback.print_exc()

    print("\n=== 脚本执行完成 ===")
    print("💡 提示：更新认证信息后即可正常使用所有功能")


if __name__ == "__main__":
    main()
