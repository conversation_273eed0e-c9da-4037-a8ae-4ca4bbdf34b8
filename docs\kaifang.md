# 发送图片和语音消息

## 发送图片
发送图片有三种方式：

2. 使用图片二进制数据：
with open("image.jpg", "rb") as f:
    image_data = f.read()
await bot.send_image_message(message["FromWxid"], image_data)

3. 使用base64编码：
image_base64 = "base64_encoded_string"
await bot.send_image_message(message["FromWxid"], image_base64)

## 发送语音
发送语音同样支持三种方式，并且可以指定格式(mp3/wav/amr)：

1. 使用语音二进制数据：
voice_data = await self._text_to_speech(text)
if voice_data:
    await bot.send_voice_message(
        message["FromWxid"], 
        voice_data,
        'mp3'
    )

2. 使用语音文件路径：
await bot.send_voice_message(
    message["FromWxid"],
    "path/to/voice.mp3",
    'mp3'
)

3. 使用base64编码：
voice_base64 = "base64_encoded_string"
await bot.send_voice_message(
    message["FromWxid"],
    voice_base64,
    'mp3'
)

注意事项：
1. 发送语音时，确保语音数据格式正确，支持 mp3/wav/amr 三种格式
2. 建议直接使用二进制数据发送，避免文件IO操作
3. 如果使用文件路径，确保文件存在且有读取权限
4. 发送成功后应及时清理临时文件
