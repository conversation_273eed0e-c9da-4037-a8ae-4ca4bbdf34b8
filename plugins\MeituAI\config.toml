[MeituAI]
enable = true
command = ["开启美图模式", "美图"]
command-format = """
🎨 美图AI插件使用说明：

1️⃣ 直接生图（推荐）：
美图 画一只可爱的小猫    # 直接生成图片
美图 生成一张风景图      # 直接生成图片
美图 画一个大美女        # 直接生成图片

2️⃣ 开启美图模式（连续创作）：
开启美图模式 - 进入美图AI创作模式

3️⃣ 美图模式下的功能：
- 直接发送描述生成图片
- AI会询问细节时直接回答
- 可以对当前图片进行连续编辑
- 发送"结束"退出美图模式

4️⃣ 使用示例：
美图 画一只可爱的小猫  # 直接生图
开启美图模式          # 进入连续创作模式
画一只可爱的小猫      # 模式内生成图片
改成橙色的           # 编辑当前图片
换成卡通风格         # 继续编辑
结束                # 退出美图模式

💡 提示：支持直接生图和连续创作两种模式
"""

# 自然化响应设置
natural_response = true

# 调试设置
debug_mode = true  # 启用调试模式，显示详细的响应信息

# 限流设置
[MeituAI.rate_limit]
tokens_per_second = 0.3  # 每秒令牌数
bucket_size = 3          # 令牌桶大小
min_request_interval = 3.0  # 最小请求间隔（秒）

# 健康检查设置
[MeituAI.health_check]
enable = true            # 启用健康检查
check_interval = 300     # 检查间隔（秒）
connection_max_age = 3600  # 连接最大存活时间（秒）
max_failures = 3         # 最大失败次数
stream_timeout = 30      # 流式响应超时（秒）
request_timeout = 120    # 请求总超时（秒）

# 智能重试设置
[MeituAI.retry]
enable = true            # 启用智能重试
max_retries = 2          # 最大重试次数
retry_delay = 2          # 重试延迟（秒）
auto_reset_connection = true  # 重试时自动重置连接

[MeituAI.API]
# 美图AI API配置 - 直接使用原py文件中的参数
access_token = "_v2NjUyYzUxNGMjMTc2MDg0NzIzNCM4Mzg4ODY1IzExIzExODlhYTMxNGRmY2Q5NGU1ZGFjNDNmYjg3ZThiYmQwYmYjSFVBV0VJX0NMT1VEI0JKX0hXIzY4N2RiZTgy"
base_url = "https://ai-engine-gateway-roboneo.meitu.com"
timeout = 60
max_retries = 3
