import json
import os
import tomllib
from pathlib import Path
import asyncio
from typing import List, Dict, Any

from loguru import logger

from WechatAPI import WechatAPIClient
from utils.decorators import *
from utils.plugin_base import PluginBase


class RenameReminder(PluginBase):
    description = "群成员改名和退群提醒"
    author = "Assistant"
    version = "1.2.0"
    plugin_name = "RenameReminder"

    def __init__(self):
        # 必须先调用父类初始化
        super().__init__()
        self.data_dir = Path("data/rename_reminder")
        self.data_dir.mkdir(parents=True, exist_ok=True)

        # 分别存储改名提醒和退群提醒的群组
        self.rename_monitored_groups = set()
        self.leave_monitored_groups = set()

        # 合并的监控群组集合
        self.monitored_groups = set()

        # 初始化检查状态
        self._is_checking_names = False

        # 加载插件配置
        config = {}
        try:
            config_path = f"plugins/{self.plugin_name}/config.toml"
            if not os.path.exists(config_path):
                logger.warning(f"[{self.plugin_name}] 配置文件 {config_path} 不存在，将使用默认配置")
                self.enable = False  # 默认不启用
                self.check_interval = 300  # 默认5分钟
                self.max_retries = 3  # 默认重试次数
                self.retry_delay = 5  # 默认重试延迟(秒)
            else:
                with open(config_path, "rb") as f:
                    plugin_config = tomllib.load(f)
                config = plugin_config.get(self.plugin_name, {})
                self.enable = config.get("enable", False)  # 默认不启用
                self.check_interval = config.get("check-interval", 300)
                self.max_retries = config.get("max-retries", 3)
                self.retry_delay = config.get("retry-delay", 5)
            logger.info(f"插件状态: {'启用' if self.enable else '禁用'}, 检查间隔: {self.check_interval}秒, 最大重试次数: {self.max_retries}, 重试延迟: {self.retry_delay}秒")
        except Exception as e:
            logger.error(f"加载插件配置出错: {e}")
            self.enable = False  # 默认不启用
            self.check_interval = 300  # 默认5分钟
            self.max_retries = 3  # 默认重试次数
            self.retry_delay = 5  # 默认重试延迟(秒)
            logger.info(f"使用默认配置: 插件状态: 禁用, 检查间隔 {self.check_interval}秒, 最大重试次数: {self.max_retries}, 重试延迟: {self.retry_delay}秒")

        # 加载管理员配置
        try:
            with open("main_config.toml", "rb") as f:
                main_config = tomllib.load(f)
                self.admins = main_config["XYBot"]["admins"]
                logger.info(f"已加载管理员列表: {self.admins}")
        except Exception as e:
            logger.error(f"加载管理员配置出错: {e}")
            self.admins = []

        # 加载监控群组
        if self.enable:
            self._load_monitored_groups()

    def _load_monitored_groups(self):
        """加载已监控的群聊"""
        config_file = self.data_dir / "monitored_groups_v2.json"
        if config_file.exists():
            try:
                with open(config_file, "r", encoding="utf-8") as f:
                    data = json.load(f)
                    self.rename_monitored_groups = set(data.get("rename_groups", []))
                    self.leave_monitored_groups = set(data.get("leave_groups", []))
                # 更新合并的监控群组集合
                self.monitored_groups = self.rename_monitored_groups.union(self.leave_monitored_groups)
                logger.info(f"已加载 {len(self.rename_monitored_groups)} 个改名监控群和 {len(self.leave_monitored_groups)} 个退群监控群")
            except Exception as e:
                logger.error(f"加载监控群配置失败: {e}")
                # 初始化为空集合
                self.rename_monitored_groups = set()
                self.leave_monitored_groups = set()
                self.monitored_groups = set()
        else:
            logger.info("监控群配置文件不存在，将创建新的配置文件")
            # 初始化为空集合
            self.rename_monitored_groups = set()
            self.leave_monitored_groups = set()
            self.monitored_groups = set()
            # 创建空的配置文件
            self._save_monitored_groups()

    def _save_monitored_groups(self):
        """保存监控的群聊列表"""
        config_file = self.data_dir / "monitored_groups_v2.json"
        data = {
            "rename_groups": list(self.rename_monitored_groups),
            "leave_groups": list(self.leave_monitored_groups)
        }
        with open(config_file, "w", encoding="utf-8") as f:
            json.dump(data, f)

        # 更新合并的监控群组集合
        self.monitored_groups = self.rename_monitored_groups.union(self.leave_monitored_groups)

    def _get_member_file(self, group_id: str) -> Path:
        """获取群成员数据文件路径"""
        return self.data_dir / f"{group_id}_members.json"

    async def _save_member_list(self, group_id: str, members: list):
        """保存群成员列表"""
        member_file = self._get_member_file(group_id)
        member_data = {}
        for member in members:
            # 使用 DisplayName（群昵称），如果没有则使用 NickName（微信昵称）
            display_name = member.get("DisplayName") or member["NickName"]
            member_data[member["UserName"]] = display_name

        with open(member_file, "w", encoding="utf-8") as f:
            json.dump(member_data, f, ensure_ascii=False, indent=2)

    def _load_member_list(self, group_id: str) -> dict:
        """加载群成员列表"""
        member_file = self._get_member_file(group_id)
        if not member_file.exists():
            return {}

        with open(member_file, "r", encoding="utf-8") as f:
            return json.load(f)

    @on_text_message
    async def handle_command(self, bot: WechatAPIClient, message: dict):
        """处理开启/关闭改名提醒和退群提醒命令"""
        # 添加调试日志


        # 如果插件未启用，直接返回
        if not self.enable:
    
            return

        if not message["IsGroup"]:
            logger.debug("非群聊消息，忽略")
            return

        content = message["Content"].strip()
        logger.debug(f"处理消息内容: '{content}'")

        if content not in ["开启改名提醒", "关闭改名提醒", "开启退群提醒", "关闭退群提醒", "查看提醒状态"]:
            logger.debug(f"消息内容 '{content}' 不匹配任何命令，忽略")
            return

        logger.info(f"收到有效命令: {content}")


        group_id = message["FromWxid"]
        sender_wxid = message["SenderWxid"]

        # 检查是否是管理员的命令
        is_admin = await self._check_is_admin(bot, group_id, sender_wxid)
        if not is_admin:
            # 临时允许所有用户使用命令，方便测试
            logger.info(f"非管理员用户 {sender_wxid} 尝试使用命令 {content}，临时允许")
            # await bot.send_text_message(group_id, "⚠️ 抱歉,只有管理员才能操作提醒功能")
            # return

        # 查看提醒状态
        if content == "查看提醒状态":
            rename_status = "✅ 已开启" if group_id in self.rename_monitored_groups else "❌ 未开启"
            leave_status = "✅ 已开启" if group_id in self.leave_monitored_groups else "❌ 未开启"

            status_message = f"📊 当前群提醒功能状态：\n\n👤 改名提醒：{rename_status}\n👋 退群提醒：{leave_status}"
            await bot.send_text_message(group_id, status_message)
            return

        # 开启改名提醒
        elif content == "开启改名提醒":
            if group_id in self.rename_monitored_groups:
                await bot.send_text_message(group_id, "改名提醒已经开启了哦 😊")
                return

            # 确保有成员数据文件
            if group_id not in self.monitored_groups:
                # 获取并保存当前群成员列表
                members = await bot.get_chatroom_member_list(group_id)
                await self._save_member_list(group_id, members)

            # 添加到改名监控列表
            self.rename_monitored_groups.add(group_id)
            self._save_monitored_groups()

            await bot.send_text_message(group_id, "已开启改名提醒功能 ✨\n我会在群成员改名时通知大家~")
            logger.info(f"群 {group_id} 开启了改名提醒")

        # 关闭改名提醒
        elif content == "关闭改名提醒":
            if group_id not in self.rename_monitored_groups:
                await bot.send_text_message(group_id, "改名提醒还没有开启呢 😊")
                return

            # 从改名监控列表移除
            self.rename_monitored_groups.remove(group_id)
            self._save_monitored_groups()

            # 如果两种提醒都已关闭，删除成员数据文件
            if group_id not in self.leave_monitored_groups:
                member_file = self._get_member_file(group_id)
                if member_file.exists():
                    member_file.unlink()

            await bot.send_text_message(group_id, "已关闭改名提醒功能 👋")
            logger.info(f"群 {group_id} 关闭了改名提醒")

        # 开启退群提醒
        elif content == "开启退群提醒":
            if group_id in self.leave_monitored_groups:
                await bot.send_text_message(group_id, "退群提醒已经开启了哦 😊")
                return

            # 确保有成员数据文件
            if group_id not in self.monitored_groups:
                # 获取并保存当前群成员列表
                members = await bot.get_chatroom_member_list(group_id)
                await self._save_member_list(group_id, members)

            # 添加到退群监控列表
            self.leave_monitored_groups.add(group_id)
            self._save_monitored_groups()

            await bot.send_text_message(group_id, "已开启退群提醒功能 ✨\n我会在群成员退群时通知大家~")
            logger.info(f"群 {group_id} 开启了退群提醒")

        # 关闭退群提醒
        elif content == "关闭退群提醒":
            if group_id not in self.leave_monitored_groups:
                await bot.send_text_message(group_id, "退群提醒还没有开启呢 😊")
                return

            # 从退群监控列表移除
            self.leave_monitored_groups.remove(group_id)
            self._save_monitored_groups()

            # 如果两种提醒都已关闭，删除成员数据文件
            if group_id not in self.rename_monitored_groups:
                member_file = self._get_member_file(group_id)
                if member_file.exists():
                    member_file.unlink()

            await bot.send_text_message(group_id, "已关闭退群提醒功能 👋")
            logger.info(f"群 {group_id} 关闭了退群提醒")

    async def _check_is_admin(self, bot: WechatAPIClient, group_id: str, member_wxid: str) -> bool:
        """检查用户是否是群管理员"""
        return member_wxid in self.admins

    async def _get_members_with_retry(self, bot: WechatAPIClient, group_id: str) -> List[Dict[str, Any]]:
        """带重试机制的获取群成员列表"""
        for attempt in range(self.max_retries):
            try:
                members = await bot.get_chatroom_member_list(group_id)
                return members
            except Exception as e:
                if attempt < self.max_retries - 1:
                    logger.warning(f"获取群 {group_id} 成员列表失败 (尝试 {attempt+1}/{self.max_retries}): {e}")
                    await asyncio.sleep(self.retry_delay)
                else:
                    logger.error(f"获取群 {group_id} 成员列表失败，已达最大重试次数: {e}")
                    raise
        return []  # 这行不应该被执行到，但为了类型检查添加

    async def _get_contact_with_retry(self, bot: WechatAPIClient, user_id: str) -> Dict[str, Any]:
        """带重试机制的获取联系人信息"""
        for attempt in range(self.max_retries):
            try:
                contact_info = await bot.get_contact(user_id)
                return contact_info
            except Exception as e:
                if attempt < self.max_retries - 1:
                    logger.warning(f"获取联系人 {user_id} 信息失败 (尝试 {attempt+1}/{self.max_retries}): {e}")
                    await asyncio.sleep(self.retry_delay)
                else:
                    logger.error(f"获取联系人 {user_id} 信息失败，已达最大重试次数: {e}")
                    raise
        return {}  # 这行不应该被执行到，但为了类型检查添加

    async def on_enable(self, bot: WechatAPIClient):
        """插件启动时的处理"""
        logger.info(f"[{self.plugin_name}] 开始启用插件...")
        await super().on_enable(bot)

        # 如果插件未启用，直接返回
        if not self.enable:
            logger.info(f"[{self.plugin_name}] 插件未启用，跳过初始化")
            return

        logger.info(f"[{self.plugin_name}] 插件已启用，开始初始化...")
        logger.info(f"[{self.plugin_name}] 支持的命令: 开启改名提醒, 关闭改名提醒, 开启退群提醒, 关闭退群提醒, 查看提醒状态")

        # 设置定时任务的间隔
        try:
            # 获取定时任务的ID
            job_id = f"{self.__class__.__module__}.{self.__class__.__name__}.check_name_changes"

            # 使用APScheduler的reschedule_job方法修改任务间隔
            from utils.decorators import scheduler
            scheduler.reschedule_job(
                job_id=job_id,
                trigger='interval',
                seconds=self.check_interval
            )
            logger.info(f"已设置检查间隔为 {self.check_interval} 秒")
        except Exception as e:
            logger.error(f"设置检查间隔失败: {e}")
            logger.info(f"使用默认检查间隔: 300 秒")

        # 为每个监控的群加载当前成员列表
        if self.monitored_groups:
            logger.info(f"已加载 {len(self.monitored_groups)} 个监控群，开始更新成员列表")
            for group_id in self.monitored_groups:
                try:
                    members = await self._get_members_with_retry(bot, group_id)
                    await self._save_member_list(group_id, members)
                    logger.debug(f"已更新群 {group_id} 的成员列表")
                except Exception as e:
                    logger.error(f"更新群 {group_id} 成员列表失败: {e}")

    @schedule('interval', seconds=300)  # 默认5分钟，将在on_enable中动态修改
    async def check_name_changes(self, bot: WechatAPIClient):
        """定时检查群成员名称变化"""
        # 如果插件未启用或没有任何监控的群，直接返回
        if not self.enable or not self.monitored_groups:
            return

        # 使用原子操作设置锁
        if self._is_checking_names:
            logger.debug("名称检查任务正在进行中，跳过本次检查")
            return

        self._is_checking_names = True
        logger.debug(f"开始检查群成员变化，当前监控 {len(self.monitored_groups)} 个群")

        try:
            # 遍历所有需要监控的群
            for group_id in self.monitored_groups:
                try:
                    # 如果该群没有开启任何提醒功能，跳过
                    if group_id not in self.rename_monitored_groups and group_id not in self.leave_monitored_groups:
                        logger.debug(f"群 {group_id} 未开启任何提醒功能，跳过检查")
                        continue

                    # 获取当前群成员列表
                    current_members = await self._get_members_with_retry(bot, group_id)
                    current_names = {}
                    for m in current_members:
                        display_name = m.get("DisplayName") or m["NickName"]
                        current_names[m["UserName"]] = display_name

                    # 加载之前的群成员列表
                    previous_names = self._load_member_list(group_id)

                    # 检查名称变化（仅当开启了改名提醒时）
                    changes = []
                    if group_id in self.rename_monitored_groups:
                        for user_id, new_name in current_names.items():
                            if user_id in previous_names and previous_names[user_id] != new_name:
                                changes.append({
                                    "user_id": user_id,
                                    "old_name": previous_names[user_id],
                                    "new_name": new_name
                                })

                    # 检查退群成员（仅当开启了退群提醒时）
                    left_members = []
                    if group_id in self.leave_monitored_groups:
                        for user_id, old_name in previous_names.items():
                            if user_id not in current_names:
                                left_members.append({
                                    "user_id": user_id,
                                    "name": old_name
                                })

                    # 发送改名提醒
                    if changes:
                        for change in changes:
                            try:
                                contact_info = await self._get_contact_with_retry(bot, change["user_id"])
                                avatar_url = contact_info["SmallHeadImgUrl"]

                                title = "👤 群成员改名提醒"
                                description = f"💫 群友换新装扮啦~\n👉 原昵称：{change['old_name']}\n👉 新昵称：{change['new_name']}"

                                await bot.send_link_message(
                                    group_id,
                                    url="weixin://",
                                    title=title,
                                    description=description,
                                    thumb_url=avatar_url
                                )
                            except Exception as e:
                                logger.error(f"发送群 {group_id} 改名提醒消息失败: {e}")
                                # 尝试发送简单文本消息作为备用
                                try:
                                    await bot.send_text_message(
                                        group_id,
                                        f"👤 群成员改名提醒\n群友 {change['old_name']} 改名为 {change['new_name']}"
                                    )
                                except:
                                    logger.error(f"发送备用文本消息也失败")

                        logger.info(f"群 {group_id} 发现 {len(changes)} 个成员改名")

                    # 发送退群提醒
                    if left_members:
                        for member in left_members:
                            try:
                                # 尝试获取退群成员的联系人信息（包括头像）
                                try:
                                    contact_info = await self._get_contact_with_retry(bot, member["user_id"])
                                    avatar_url = contact_info["SmallHeadImgUrl"]
                                except Exception as e:
                                    logger.warning(f"获取退群成员 {member['user_id']} 的头像失败: {e}")
                                    # 如果获取失败，使用默认头像
                                    avatar_url = "https://res.wx.qq.com/a/wx_fed/webwx/res/static/img/2KriyDK.png"

                                title = "👋 群成员退群提醒"
                                description = f"💔 群友已离开群聊\n👤 成员昵称：{member['name']}"

                                await bot.send_link_message(
                                    group_id,
                                    url="weixin://",
                                    title=title,
                                    description=description,
                                    thumb_url=avatar_url
                                )
                            except Exception as e:
                                logger.error(f"发送群 {group_id} 退群提醒消息失败: {e}")
                                # 尝试发送简单文本消息作为备用
                                try:
                                    await bot.send_text_message(
                                        group_id,
                                        f"👋 群成员退群提醒\n群友 {member['name']} 已离开群聊"
                                    )
                                except:
                                    logger.error(f"发送退群备用文本消息也失败")

                        logger.info(f"群 {group_id} 发现 {len(left_members)} 个成员退群")

                    # 保存新的成员列表（只要该群有任何一种提醒功能开启）
                    if group_id in self.rename_monitored_groups or group_id in self.leave_monitored_groups:
                        await self._save_member_list(group_id, current_members)

                except Exception as e:
                    logger.error(f"检查群 {group_id} 成员变动时出错: {e}")
        except Exception as e:
            logger.error(f"检查群成员变化时发生未处理的异常: {e}")
            import traceback
            logger.error(f"异常堆栈: {traceback.format_exc()}")
        finally:
            self._is_checking_names = False
            logger.debug("群成员变化检查完成")