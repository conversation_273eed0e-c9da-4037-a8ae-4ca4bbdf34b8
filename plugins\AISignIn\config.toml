[AISignIn]
enable = true
command = ["刷新状态"]
command-format = """
🎯 AI网站自动签到插件

使用方法：
刷新状态 - 手动执行AI网站签到

⚠️ 注意：
- 此操作会启动Chrome浏览器依次访问以下网站：
  1. 豆包AI对话页面
  2. 剪映智能创作页面
- 每个网站访问时长约60秒
- 使用您的默认Chrome配置文件保持登录状态
- 只会回复确认消息，不会回复执行结果

🕐 定时签到：
- 每天0点01分自动签到一次
- 可在配置文件中开启/关闭定时功能
- 可添加更多AI网站进行批量签到
"""
natural_response = true  # 启用自然化响应

[AISignIn.rate_limit]
cooldown = 30  # 冷却时间30秒，避免频繁签到

[AISignIn.chrome_settings]
urls = [
    "https://www.doubao.com/chat/4498442627796994",
    "https://jimeng.jianying.com/ai-tool/home"
]
duration = 60  # 每个网站的访问时长（秒）
window_size = "1920,1080"  # 窗口大小
interval_between_sites = 5  # 访问不同网站之间的间隔时间（秒）

[AISignIn.auto_signin]
enable = true  # 启用定时自动签到
hour = 0  # 签到时间：小时（0-23）
minute = 1  # 签到时间：分钟（0-59）
notify = false  # 是否发送签到通知到群
notify_groups = []  # 接收通知的群ID列表，为空则不发送通知
