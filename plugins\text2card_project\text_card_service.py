from pathlib import Path
from typing import Union, Optional, Dict, Any, Tuple
from PIL import Image, ImageFilter, ImageEnhance, ImageDraw
import logging
import httpx
import io
import os
import time
from .text2card import CardGenerator
import random
import math

class TextCardError(Exception):
    """文本卡片服务异常基类"""
    pass

class FontError(TextCardError):
    """字体相关错误"""
    pass

class ImageError(TextCardError):
    """图片处理错误"""
    pass

class NetworkError(TextCardError):
    """网络相关错误"""
    pass

class ConfigError(TextCardError):
    """配置参数错误"""
    pass

class ImageCache:
    """图片缓存管理"""
    
    def __init__(self, cache_dir: str, max_size: int = 100 * 1024 * 1024):  # 默认100MB
        self.cache_dir = Path(cache_dir)
        self.max_size = max_size
        self.cache_dir.mkdir(parents=True, exist_ok=True)
        
    def get_cache_path(self, url: str) -> Optional[Path]:
        """获取缓存路径"""
        import hashlib
        url_hash = hashlib.md5(url.encode()).hexdigest()
        cache_path = self.cache_dir / f"{url_hash}.png"
        if cache_path.exists():
            # 检查是否过期（24小时）
            if time.time() - cache_path.stat().st_mtime < 24 * 3600:
                return cache_path
            cache_path.unlink()
        return None
        
    def save_to_cache(self, url: str, image: Image.Image) -> Path:
        """保存到缓存"""
        self._cleanup_if_needed()
        cache_path = self.get_cache_path(url) or (self.cache_dir / f"{hash(url)}.png")
        image.save(cache_path)
        return cache_path
        
    def _cleanup_if_needed(self):
        """清理过期缓存"""
        total_size = sum(f.stat().st_size for f in self.cache_dir.glob('*'))
        if total_size > self.max_size:
            files = sorted(self.cache_dir.glob('*'), key=lambda x: x.stat().st_mtime)
            for f in files:
                f.unlink()
                total_size -= f.stat().st_size
                if total_size <= self.max_size * 0.8:  # 清理到80%
                    break

class TextCardService:
    """统一的文本转图片服务类
    
    这个类封装了text2card项目的功能，提供了统一的接口用于文本转图片处理。
    主要用于机器人插件开发中的文本转图片需求。
    """
    
    def __init__(self, custom_fonts_dir: str = None, cache_dir: str = None):
        """初始化TextCardService
        
        Args:
            custom_fonts_dir: 可选的自定义字体目录路径
            cache_dir: 可选的缓存目录路径
        """
        self._fonts_dir = Path(custom_fonts_dir) if custom_fonts_dir else Path(__file__).parent / 'fonts'
        self._cache_dir = Path(cache_dir) if cache_dir else Path(__file__).parent / 'cache'
        self._generator = None
        self._logger = logging.getLogger('TextCardService')
        self._cache = ImageCache(self._cache_dir)
        self._init_generator()
    
    def _init_generator(self) -> None:
        """初始化CardGenerator，包含错误处理"""
        try:
            if not self._fonts_dir.exists():
                raise FontError(f"字体目录不存在: {self._fonts_dir}")
            
            # 检查必要的字体文件
            required_fonts = {
                'SourceHanSansCN-Regular.otf': '思源黑体常规字体',
                'SourceHanSansCN-Bold.otf': '思源黑体粗体字体',
                'NotoColorEmoji.ttf': 'Emoji字体'
            }
            
            for font_file, desc in required_fonts.items():
                if not (self._fonts_dir / font_file).exists():
                    raise FontError(f"缺少{desc}: {font_file}")
            
            self._generator = CardGenerator(str(self._fonts_dir))
            self._logger.info("TextCardService初始化成功")
            
        except FontError as e:
            self._logger.error(f"字体初始化失败: {str(e)}")
            raise
        except Exception as e:
            self._logger.error(f"初始化CardGenerator失败: {str(e)}")
            raise TextCardError(f"TextCardService初始化失败: {str(e)}")
    
    async def _download_image(self, url: str) -> Optional[Image.Image]:
        """从URL下载图片，支持缓存
        
        Args:
            url: 图片URL
            
        Returns:
            PIL.Image对象，如果下载失败返回None
            
        Raises:
            NetworkError: 网络请求失败
            ImageError: 图片处理失败
        """
        try:
            # 检查缓存
            cache_path = self._cache.get_cache_path(url)
            if cache_path:
                return Image.open(cache_path)
            
            # 下载图片
            async with httpx.AsyncClient() as client:
                response = await client.get(url, timeout=10.0)
                if response.status_code != 200:
                    raise NetworkError(f"下载图片失败，状态码: {response.status_code}")
                
                content = response.content
                image = Image.open(io.BytesIO(content))
            
            # 保存到缓存
            self._cache.save_to_cache(url, image)
            
            return image
            
        except httpx.RequestError as e:
            raise NetworkError(f"网络请求失败: {str(e)}")
        except Exception as e:
            raise ImageError(f"图片处理失败: {str(e)}")
    
    def _process_header_image(self, header_image: Image.Image, target_width: int,
                            enhance: bool = True) -> Image.Image:
        """处理头部图片
        
        Args:
            header_image: 原始图片
            target_width: 目标宽度
            enhance: 是否增强图片
            
        Returns:
            处理后的图片
        """
        try:
            # 调整大小
            w_percent = target_width / float(header_image.size[0])
            target_height = int(float(header_image.size[1]) * float(w_percent))
            
            # 限制最大高度
            max_height = target_width // 2
            if target_height > max_height:
                target_height = max_height
                
            resized = header_image.resize((target_width, target_height), 
                                        Image.Resampling.LANCZOS)
            
            if enhance:
                # 增强对比度
                enhancer = ImageEnhance.Contrast(resized)
                resized = enhancer.enhance(1.2)
                
                # 增加锐度
                resized = resized.filter(ImageFilter.SHARPEN)
            
            # 添加圆角
            return self._add_corners(resized, 30)
            
        except Exception as e:
            raise ImageError(f"处理头部图片失败: {str(e)}")
    
    def _add_corners(self, image: Image.Image, radius: int) -> Image.Image:
        """为图片添加圆角
        
        Args:
            image: 原始图片
            radius: 圆角半径
            
        Returns:
            处理后的图片
        """
        circle = Image.new('L', (radius * 2, radius * 2), 0)
        draw = ImageDraw.Draw(circle)
        draw.ellipse((0, 0, radius * 2, radius * 2), fill=255)
        
        output = Image.new('RGBA', image.size, (255, 255, 255, 0))
        w, h = image.size
        
        # 添加四个圆角
        alpha = Image.new('L', image.size, 255)
        alpha.paste(circle.crop((0, 0, radius, radius)), (0, 0))
        alpha.paste(circle.crop((radius, 0, radius * 2, radius)), (w - radius, 0))
        alpha.paste(circle.crop((0, radius, radius, radius * 2)), (0, h - radius))
        alpha.paste(circle.crop((radius, radius, radius * 2, radius * 2)),
                   (w - radius, h - radius))
        
        output.paste(image, (0, 0))
        output.putalpha(alpha)
        
        return output
    
    def _combine_images(self, header_image: Image.Image, content_image: Image.Image,
                       spacing: int = 20, 
                       background_color: str = 'white',
                       border_radius: int = 30,
                       border_color: str = '#EEEEEE',
                       border_width: int = 2,
                       padding: int = 40,
                       shadow: bool = True,
                       gradient: bool = True) -> Image.Image:
        """合并头部图片和内容图片,支持美化效果
        
        Args:
            header_image: 头部图片
            content_image: 内容图片
            spacing: 图片间距
            background_color: 背景颜色
            border_radius: 边框圆角半径
            border_color: 边框颜色
            border_width: 边框宽度
            padding: 内边距
            shadow: 是否添加阴影
            gradient: 是否添加渐变背景
            
        Returns:
            合并后的图片
        """
        try:
            # 计算总尺寸(含padding)
            total_width = max(header_image.size[0], content_image.size[0]) + padding * 2
            total_height = header_image.size[1] + spacing + content_image.size[1] + padding * 2
            
            # 创建基础图片
            combined = Image.new('RGBA', (total_width, total_height), (0, 0, 0, 0))
            
            # 添加渐变背景
            if gradient:
                gradient_overlay = self._create_gradient_background(total_width, total_height, background_color)
                combined = Image.alpha_composite(combined, gradient_overlay)
            else:
                # 使用纯色背景
                background = Image.new('RGBA', (total_width, total_height), background_color)
                combined = Image.alpha_composite(combined, background)
                
            # 添加边框
            if border_width > 0:
                border = self._create_border(total_width, total_height, border_radius, border_color, border_width)
                combined = Image.alpha_composite(combined, border)
                
            # 添加阴影
            if shadow:
                shadow_layer = self._create_shadow(total_width, total_height, border_radius)
                combined = Image.alpha_composite(shadow_layer, combined)
                
            # 粘贴头部图片(居中)
            x_offset = (total_width - header_image.size[0]) // 2
            y_offset = padding
            combined.paste(header_image, (x_offset, y_offset), 
                          header_image if header_image.mode == 'RGBA' else None)
            
            # 粘贴内容图片(居中)
            x_offset = (total_width - content_image.size[0]) // 2
            y_offset = padding + header_image.size[1] + spacing
            combined.paste(content_image, (x_offset, y_offset),
                          content_image if content_image.mode == 'RGBA' else None)
            
            return combined
            
        except Exception as e:
            raise ImageError(f"合并图片失败: {str(e)}")
        
    def _create_gradient_background(self, width: int, height: int, base_color: str) -> Image.Image:
        """创建渐变背景"""
        from PIL import ImageColor
        import random
        
        # 解析基础颜色
        base_rgb = ImageColor.getrgb(base_color)
        
        # 创建更加鲜明的渐变终点色
        if base_color.startswith('#') and len(base_color) >= 7:
            # 如果是深色，创建一个更深的颜色
            if sum(base_rgb) < 384:  # 128*3
                end_rgb = tuple(max(0, c - 40) for c in base_rgb)
                accent_rgb = self._create_accent_color(base_rgb, brighter=True)
            else:
                # 如果是亮色，创建一个更亮的颜色
                end_rgb = tuple(min(255, c + 20) for c in base_rgb)
                accent_rgb = self._create_accent_color(base_rgb, brighter=False)
        else:
            # 默认深色渐变
            end_rgb = tuple(max(0, c - 40) for c in base_rgb)
            accent_rgb = (30, 144, 255)  # 默认蓝色为强调色
        
        gradient = Image.new('RGBA', (width, height), (0, 0, 0, 0))
        draw = ImageDraw.Draw(gradient)
        
        # 创建从上到下的主渐变
        for y in range(height):
            ratio = y / height
            current_color = tuple(
                int(base_rgb[i] + (end_rgb[i] - base_rgb[i]) * ratio)
                for i in range(3)
            )
            draw.line([(0, y), (width, y)], fill=current_color + (255,))
        
        # 添加一些随机点缀的亮度变化，让渐变更加生动
        for _ in range(width // 10):
            x = random.randint(0, width - 1)
            y = random.randint(0, height - 1)
            size = random.randint(30, 100)
            alpha = random.randint(10, 50)
            draw.ellipse(
                [(x - size, y - size), (x + size, y + size)],
                fill=accent_rgb + (alpha,)
            )
            
        return gradient
        
    def _create_accent_color(self, base_rgb: tuple, brighter: bool = True) -> tuple:
        """从基础颜色创建强调色"""
        if brighter:
            # 从深色创建明亮的强调色
            h, s, v = self._rgb_to_hsv(*base_rgb)
            # 增加饱和度和明度
            s = min(1.0, s + 0.3)
            v = min(1.0, v + 0.4)
            # 稍微改变色相
            h = (h + 30) % 360
            r, g, b = self._hsv_to_rgb(h, s, v)
            return (r, g, b)
        else:
            # 从亮色创建深色强调色
            h, s, v = self._rgb_to_hsv(*base_rgb)
            # 增加饱和度，降低明度
            s = min(1.0, s + 0.2)
            v = max(0.1, v - 0.3)
            # 稍微改变色相
            h = (h + 210) % 360
            r, g, b = self._hsv_to_rgb(h, s, v)
            return (r, g, b)
    
    def _rgb_to_hsv(self, r: int, g: int, b: int) -> tuple:
        """RGB转HSV颜色空间"""
        r, g, b = r/255.0, g/255.0, b/255.0
        mx = max(r, g, b)
        mn = min(r, g, b)
        df = mx - mn
        if mx == mn:
            h = 0
        elif mx == r:
            h = (60 * ((g-b)/df) + 360) % 360
        elif mx == g:
            h = (60 * ((b-r)/df) + 120) % 360
        elif mx == b:
            h = (60 * ((r-g)/df) + 240) % 360
        if mx == 0:
            s = 0
        else:
            s = df/mx
        v = mx
        return (h, s, v)
    
    def _hsv_to_rgb(self, h: float, s: float, v: float) -> tuple:
        """HSV转RGB颜色空间"""
        h = float(h)
        s = float(s)
        v = float(v)
        h60 = h / 60.0
        h60f = math.floor(h60)
        hi = int(h60f) % 6
        f = h60 - h60f
        p = v * (1 - s)
        q = v * (1 - f * s)
        t = v * (1 - (1 - f) * s)
        r, g, b = 0, 0, 0
        if hi == 0: r, g, b = v, t, p
        elif hi == 1: r, g, b = q, v, p
        elif hi == 2: r, g, b = p, v, t
        elif hi == 3: r, g, b = p, q, v
        elif hi == 4: r, g, b = t, p, v
        elif hi == 5: r, g, b = v, p, q
        r, g, b = int(r * 255), int(g * 255), int(b * 255)
        return (r, g, b)
    
    def _create_border(self, width: int, height: int, radius: int, 
                      color: str, border_width: int) -> Image.Image:
        """创建圆角边框"""
        border = Image.new('RGBA', (width, height), (0, 0, 0, 0))
        draw = ImageDraw.Draw(border)
        
        # 绘制圆角矩形
        draw.rounded_rectangle(
            [(border_width//2, border_width//2), 
             (width-border_width//2-1, height-border_width//2-1)],
            radius=radius,
            outline=color,
            width=border_width
        )
        
        return border
        
    def _create_shadow(self, width: int, height: int, radius: int) -> Image.Image:
        """创建阴影效果"""
        # 创建基础阴影形状
        shadow = Image.new('RGBA', (width, height), (0, 0, 0, 0))
        draw = ImageDraw.Draw(shadow)
        
        # 绘制更明显的阴影，增强视觉层次感
        # 主阴影
        draw.rounded_rectangle(
            [(12, 12), (width-8, height-8)],
            radius=radius,
            fill=(0, 0, 0, 50)  # 更深的阴影
        )
        
        # 模糊处理
        shadow = shadow.filter(ImageFilter.GaussianBlur(8))
        
        # 额外添加一个小一点的内阴影增强立体感
        inner_shadow = Image.new('RGBA', (width, height), (0, 0, 0, 0))
        inner_draw = ImageDraw.Draw(inner_shadow)
        inner_draw.rounded_rectangle(
            [(4, 4), (width-16, height-16)],
            radius=radius-2,
            fill=(0, 0, 0, 25)
        )
        inner_shadow = inner_shadow.filter(ImageFilter.GaussianBlur(3))
        
        # 合并两个阴影
        shadow = Image.alpha_composite(shadow, inner_shadow)
        
        return shadow
    
    async def generate_card(self,
                     text: str,
                     output_path: Optional[str] = None,
                     **kwargs) -> Union[str, Image.Image]:
        """生成文本卡片
        
        Args:
            text: 要转换的文本内容，支持Markdown格式
            output_path: 可选的输出路径，如果提供则保存到文件
            **kwargs: 其他配置参数，包括：
                - width: int, 图片宽度，默认720
                - is_dark: bool, 是否使用暗色主题，默认False
                - title_image: Optional[str], 标题图片路径，默认None
                - background_color: str, 背景颜色
                - text_color: str, 文本颜色
                - title_color: str, 标题颜色
                - border_color: str, 边框颜色
                - border_width: int, 边框宽度
                - border_radius: int, 边框圆角半径
                - padding: int, 内边距
                - font_size: int, 字体大小
                - title_font_size: int, 标题字体大小
                - line_spacing: float, 行间距
                - shadow: bool, 是否添加阴影
                - gradient: bool, 是否使用渐变背景
                - title_spacing: int, 标题与正文间距
                - enhance_contrast: bool, 是否增强对比度
                - enhance_emoji: bool, 是否增强emoji显示
                - tech_effect: bool, 是否添加科技感装饰效果
                - border_style: str, 边框样式，可选 'simple', 'gradient', 'double', 'neon', 'stripe', 'glow'
                - border_accent_color: str, 边框辅助颜色，用于渐变、双层等效果
                - inner_style: str, 内边框样式，可选 'solid', 'dotted', 'dashed', 'wavy'，仅当border_style为'double'时有效
        
        Returns:
            如果提供output_path，返回保存路径str
            否则返回PIL Image对象
        
        Raises:
            TextCardError: 生成过程中出现错误
            ValueError: 参数错误
        """
        if not text:
            raise ValueError("文本内容不能为空")
            
        if not self._generator:
            raise TextCardError("TextCardService未正确初始化")
        
        try:
            # 设置默认参数
            default_params = {
                'width': 720,
                'is_dark': False
            }
            
            # 更新参数，扩展支持的参数列表
            supported_params = {
                'width', 'is_dark', 'title_image', 
                'background_color', 'text_color', 'title_color', 
                'border_color', 'border_width', 'border_radius',
                'padding', 'font_size', 'title_font_size',
                'line_spacing', 'shadow', 'gradient',
                'title_spacing', 'enhance_contrast', 'enhance_emoji',
                'tech_effect', 'tech_theme', 'border_style', 'border_accent_color',
                'inner_style'  # 添加内边框样式参数
            }
            
            filtered_kwargs = {k: v for k, v in kwargs.items() if k in supported_params}
            params = {**default_params, **filtered_kwargs}
            
            # 处理增强对比度选项
            if params.pop('enhance_contrast', False):
                # 这里可以添加对比度增强的处理
                # 对于text2card库，可能需要添加相应的支持
                pass
                
            # 处理文本格式化以增强视觉效果
            if 'enhance_emoji' in params and params.pop('enhance_emoji'):
                # 增强emoji显示效果
                # 可以添加更多的文本预处理
                pass
            
            # 生成卡片
            self._logger.debug(f"开始生成卡片，参数: {params}")
            card = self._generator.generate_card(text, **params)
            
            # 保存或返回
            if output_path:
                card.save(output_path, quality=95, optimize=True)
                self._logger.info(f"卡片已保存到: {output_path}")
                return output_path
                
            return card
            
        except Exception as e:
            self._logger.error(f"生成卡片失败: {str(e)}")
            raise TextCardError(f"生成卡片失败: {str(e)}")
    
    async def generate_card_with_header(self,
                                text: str,
                                header_image_url: str,
                                output_path: Optional[str] = None,
                                enhance_header: bool = True,
                                **kwargs) -> Union[str, Image.Image]:
        """生成带有头部图片的文本卡片
        
        Args:
            text: 要转换的文本内容，支持Markdown格式
            header_image_url: 头部图片的URL
            output_path: 可选的输出路径，如果提供则保存到文件
            enhance_header: 是否增强头部图片
            **kwargs: 其他配置参数，与generate_card相同
        
        Returns:
            如果提供output_path，返回保存路径str
            否则返回PIL Image对象
        """
        try:
            # 下载头部图片
            header_image = await self._download_image(header_image_url)
            if not header_image:
                self._logger.warning("无法获取头部图片，将生成无头部图片的卡片")
                return await self.generate_card(text, output_path, **kwargs)
            
            # 保存为临时文件
            temp_header = os.path.join(self._cache_dir, f"temp_header_{int(time.time())}.png")
            if enhance_header:
                # 处理头部图片
                width = kwargs.get('width', 720)
                processed_header = self._process_header_image(header_image, width, enhance=True)
                processed_header.save(temp_header)
            else:
                header_image.save(temp_header)
            
            try:
                # 使用临时文件作为title_image参数
                kwargs['title_image'] = temp_header
                return await self.generate_card(text, output_path, **kwargs)
            finally:
                # 清理临时文件
                if os.path.exists(temp_header):
                    os.remove(temp_header)
            
        except (NetworkError, ImageError) as e:
            self._logger.error(str(e))
            self._logger.info("尝试生成无头部图片的卡片...")
            return await self.generate_card(text, output_path, **kwargs)
        except Exception as e:
            self._logger.error(f"生成带头部图片的卡片失败: {str(e)}")
            raise TextCardError(f"生成带头部图片的卡片失败: {str(e)}")
    
    async def generate_simple_card(self,
                           text: str,
                           output_path: Optional[str] = None,
                           theme: str = 'dark',
                           style: str = 'modern') -> Union[str, Image.Image]:
        """生成简单文本卡片的快捷方法
        
        Args:
            text: 要转换的文本内容
            output_path: 可选的输出路径
            theme: 主题，可选 'light', 'dark', 'neon', 'ocean', 'sunset', 'tech', 'cyberpunk'，默认 'dark'
            style: 卡片样式，可选 'modern', 'minimal', 'fancy', 'tech'，默认 'modern'
        
        Returns:
            如果提供output_path，返回保存路径str
            否则返回PIL Image对象
        """
        # 基础参数设置
        params = {
            'width': 1000,
            'gradient': True,
            'shadow': True,
            'border_radius': 35,
            'line_spacing': 1.8,
            'font_size': 34,
            'title_font_size': 52,
            'padding': 50,
            'tech_effect': style == 'tech'  # 根据样式自动开启科技效果
        }
        
        # 主题颜色设置
        themes = {
            'light': {
                'is_dark': False,
                'background_color': '#F8F8FA',
                'text_color': '#333333',
                'title_color': '#1976D2',
                'border_color': '#E0E0E0'
            },
            'dark': {
                'is_dark': True,
                'background_color': '#1E2C3C',
                'text_color': '#FFFFFF',
                'title_color': '#4DC4FF',
                'border_color': '#4DC4FF'
            },
            'neon': {
                'is_dark': True,
                'background_color': '#0F0F1A',
                'text_color': '#FFFFFF',
                'title_color': '#FF00FF',
                'border_color': '#00FFCC',
                'glow_effect': True
            },
            'ocean': {
                'is_dark': True,
                'background_color': '#0A2E52',
                'text_color': '#E0F7FA',
                'title_color': '#4FC3F7',
                'border_color': '#26C6DA'
            },
            'sunset': {
                'is_dark': True,
                'background_color': '#2C1E30',
                'text_color': '#FFFFFF',
                'title_color': '#FF9E80',
                'border_color': '#FF5252'
            },
            'tech': {
                'is_dark': True,
                'background_color': '#0D1117',
                'text_color': '#E6EDF3',
                'title_color': '#58A6FF',
                'border_color': '#30363D',
                'tech_effect': True
            },
            'cyberpunk': {
                'is_dark': True,
                'background_color': '#000000',
                'text_color': '#FFFFFF',
                'title_color': '#00FFFF',
                'border_color': '#FF00FF',
                'tech_effect': True,
                'glow_effect': True
            }
        }
        
        # 样式设置
        styles = {
            'modern': {
                'border_width': 3,
                'enhance_contrast': True
            },
            'minimal': {
                'border_width': 0,
                'shadow': False,
                'padding': 40
            },
            'fancy': {
                'border_width': 4,
                'enhance_contrast': True,
                'title_spacing': 35,
                'enhance_emoji': True
            },
            'tech': {
                'border_width': 2,
                'enhance_contrast': True,
                'tech_effect': True,
                'shadow': True,
                'border_radius': 40
            }
        }
        
        # 合并设置
        if theme in themes:
            params.update(themes[theme])
        if style in styles:
            params.update(styles[style])
            
        # 处理特殊效果
        if params.pop('glow_effect', False):
            # 荧光效果
            text = f"✨ {text} ✨"
        
        return await self.generate_card(text, output_path, **params)
    
    @staticmethod
    def get_default_style() -> Dict[str, Any]:
        return {
            'width': 850,                  # 增加宽度
            'font_size': 34,               # 增大字体
            'line_spacing': 1.8,           # 增加行距
            'background_color': '#1E2C3C', # 深色背景
            'text_color': '#FFFFFF',       # 白色文本
            'title_color': '#4DC4FF',      # 亮蓝色标题
            'border_color': '#4DC4FF',     # 亮蓝色边框
            'border_width': 3,             # 加粗边框
            'border_radius': 35,           # 更大的圆角
            'padding': 45,                 # 更大的内边距
            'title_font_size': 52,         # 更大的标题字体
            'shadow': True,
            'gradient': True,
            'enhance_contrast': True,      # 增强对比度
            'title_spacing': 30,           # 标题和内容间距
            'tech_effect': True,           # 启用科技视觉效果
            'border_style': 'neon'         # 默认使用霓虹发光边框
        }

    async def generate_weather_card(self,
                                text: str,
                                header_image_url: str,
                                title: str,
                                output_path: Optional[str] = None) -> Union[str, Image.Image]:
        """生成专门的天气信息卡片

        Args:
            text: 天气信息文本
            header_image_url: 头部图片URL
            title: 标题
            output_path: 可选的输出路径

        Returns:
            生成的图片对象或保存路径
        """
        # 天气卡片的特殊样式
        weather_style = {
            'width': 800,
            'font_size': 32,  # 更大的字体
            'line_spacing': 1.8,  # 更大的行距
            'background_color': '#F5F6FA',  # 浅色背景
            'text_color': '#2C3E50',
            'title_color': '#2980B9',  # 蓝色标题
            'border_color': '#E8F0FE',
            'border_width': 3,
            'border_radius': 35,
            'padding': 45,
            'title_font_size': 48,
            'shadow': True,
            'gradient': True,
            'title_spacing': 30,  # 标题和内容的间距
            'enhance_emoji': True  # 增强emoji显示
        }

        # 处理天气文本格式
        # 将天气信息分段，每段之间增加适当的空行
        sections = text.split('\n\n')
        formatted_text = '\n\n'.join(sections)

        # 生成带有特殊样式的卡片
        return await self.generate_card_with_header(
            formatted_text,
            header_image_url,
            title=title,
            output_path=output_path,
            enhance_header=True,
            **weather_style
        )

    async def generate_tech_card(self,
                           text: str,
                           output_path: Optional[str] = None,
                           tech_style: str = 'modern') -> Union[str, Image.Image]:
        """生成科技风格文本卡片
        
        Args:
            text: 要转换的文本内容
            output_path: 可选的输出路径
            tech_style: 科技风格，可选 'modern', 'matrix', 'hologram', 'cyberpunk'
        
        Returns:
            如果提供output_path，返回保存路径str
            否则返回PIL Image对象
        """
        # 科技风格卡片的基础样式
        tech_params = {
            'width': 850,
            'is_dark': True,
            'gradient': True,
            'shadow': True,
            'border_radius': 40,
            'line_spacing': 1.8,
            'font_size': 34,
            'title_font_size': 52,
            'padding': 50,
            'tech_effect': True,  # 启用科技装饰效果
            'enhance_emoji': True
        }
        
        # 不同科技风格的参数
        tech_styles = {
            'modern': {
                'background_color': '#0D1117',
                'text_color': '#E6EDF3',
                'title_color': '#58A6FF',
                'border_color': '#58A6FF',
                'border_width': 3,
                'border_style': 'neon'   # 使用霓虹发光边框
            },
            'matrix': {
                'background_color': '#000000',
                'text_color': '#00FF00',
                'title_color': '#00FF00',
                'border_color': '#00FF00',
                'border_width': 3,
                'border_style': 'stripe',   # 使用条纹边框
                'border_accent_color': '#88FF88'  # 浅绿色作为辅助颜色
            },
            'hologram': {
                'background_color': '#102040',
                'text_color': '#88CCFF',
                'title_color': '#00FFFF',
                'border_color': '#00FFFF',
                'border_width': 3,
                'border_style': 'glow',   # 使用发光边框
                'border_accent_color': '#0088FF'  # 蓝色作为辅助颜色
            },
            'cyberpunk': {
                'background_color': '#0F0F2D',
                'text_color': '#FFFFFF',
                'title_color': '#FF00FF',
                'border_color': '#FF00FF',
                'border_width': 4,
                'border_style': 'gradient',   # 使用渐变边框
                'border_accent_color': '#00FFFF'  # 青色作为辅助颜色
            }
        }
        
        # 选择风格，默认为modern
        style_params = tech_styles.get(tech_style, tech_styles['modern'])
        tech_params.update(style_params)
        
        # 可以添加一些前缀和后缀来增强科技感
        tech_prefix = {
            'modern': '',
            'matrix': '> ',
            'hologram': '◈ ',
            'cyberpunk': '⟪ '
        }
        
        tech_suffix = {
            'modern': '',
            'matrix': ' <',
            'hologram': ' ◈',
            'cyberpunk': ' ⟫'
        }
        
        prefix = tech_prefix.get(tech_style, '')
        suffix = tech_suffix.get(tech_style, '')
        
        # 处理文本 - 对标题行应用前缀和后缀
        if prefix or suffix:
            lines = text.split('\n')
            for i, line in enumerate(lines):
                if line.startswith('# '):
                    lines[i] = f"# {prefix}{line[2:]}{suffix}"
            text = '\n'.join(lines)
        
        return await self.generate_card(text, output_path, **tech_params)

    async def generate_fancy_border_card(self,
                                    text: str,
                                    output_path: Optional[str] = None,
                                    border_style: str = 'neon',
                                    theme_color: str = None,
                                    inner_style: str = 'solid') -> Union[str, Image.Image]:
        """生成带有特殊边框效果的文本卡片
        
        Args:
            text: 要转换的文本内容
            output_path: 可选的输出路径
            border_style: 边框样式，可选 'gradient', 'double', 'neon', 'stripe', 'glow'
            theme_color: 主色调，默认根据是否暗色主题自动选择
            inner_style: 内边框样式，可选 'solid', 'dotted', 'dashed', 'wavy'，仅当border_style为'double'时有效
        
        Returns:
            如果提供output_path，返回保存路径str
            否则返回PIL Image对象
        """
        # 基础样式
        params = {
            'width': 850,
            'is_dark': True,  # 特效边框在深色主题上效果更佳
            'gradient': True,
            'shadow': True,
            'border_radius': 40,
            'line_spacing': 1.8,
            'font_size': 34,
            'title_font_size': 48,
            'padding': 45,
            'border_width': 3
        }
        
        # 主题和边框风格
        if not theme_color:
            theme_color = '#4DC4FF'  # 默认主题色
        
        # 计算辅助颜色 - 互补色
        import colorsys
        
        def hex_to_rgb(hex_color):
            hex_color = hex_color.lstrip('#')
            return tuple(int(hex_color[i:i+2], 16) for i in (0, 2, 4))
        
        def rgb_to_hex(rgb):
            return '#{:02x}{:02x}{:02x}'.format(rgb[0], rgb[1], rgb[2])
        
        # 计算互补色
        if theme_color.startswith('#'):
            rgb = hex_to_rgb(theme_color)
            hsv = colorsys.rgb_to_hsv(rgb[0]/255, rgb[1]/255, rgb[2]/255)
            h = (hsv[0] + 0.5) % 1.0  # 色相偏移0.5，即180度
            complementary_rgb = tuple(int(x * 255) for x in colorsys.hsv_to_rgb(h, hsv[1], hsv[2]))
            complementary_color = rgb_to_hex(complementary_rgb)
        else:
            complementary_color = None
        
        # 边框样式设置
        params['border_style'] = border_style
        params['border_color'] = theme_color
        params['border_accent_color'] = complementary_color
        params['inner_style'] = inner_style  # 添加内边框样式参数
        
        # 根据边框样式调整其他参数
        if border_style == 'neon':
            params['title_color'] = theme_color
            params['text_color'] = '#FFFFFF'
            params['background_color'] = '#1A1A2A'
        elif border_style == 'gradient':
            params['title_color'] = theme_color
            params['text_color'] = '#FFFFFF'
            params['background_color'] = '#0D1117'
        elif border_style == 'glow':
            params['title_color'] = theme_color
            params['text_color'] = '#F0F0F0'
            params['background_color'] = '#0A0A14'
            params['shadow'] = False  # 关闭阴影，避免与发光效果冲突
        elif border_style == 'double':
            params['title_color'] = theme_color
            params['text_color'] = '#FFFFFF'
            params['background_color'] = '#131822'
        elif border_style == 'stripe':
            params['title_color'] = theme_color
            params['text_color'] = '#FFFFFF'
            params['background_color'] = '#0F1522'
        
        return await self.generate_card(text, output_path, **params)