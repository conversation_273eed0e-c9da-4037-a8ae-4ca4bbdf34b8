# utf-8
# image_generator.py
"""
Advanced image card generator with markdown and emoji support
"""
import math
import random
import os
from PIL import Image, ImageDraw, ImageFont, ImageOps, ImageFilter
import emoji
import re
from dataclasses import dataclass
from typing import List, Tuple, Optional, Dict
from datetime import datetime
from PIL import ImageColor


@dataclass
class TextStyle:
    """文本样式定义"""
    font_name: str = 'regular'  # regular, bold, emoji
    font_size: int = 30  # 字体大小
    indent: int = 0  # 缩进像素
    line_spacing: int = 15  # 行间距
    is_title: bool = False  # 是否为标题
    is_category: bool = False  # 是否为分类标题
    keep_with_next: bool = False  # 是否与下一行保持在一起


@dataclass
class TextSegment:
    """文本片段定义"""
    text: str  # 文本内容
    style: TextStyle  # 样式
    original_text: str = ''  # 原始文本（用于调试）


@dataclass
class ProcessedLine:
    """处理后的行信息"""
    text: str  # 实际文本内容
    style: TextStyle  # 样式信息
    height: int = 0  # 行高
    line_count: int = 1  # 实际占用行数


class FontManager:
    """字体管理器"""

    def __init__(self, font_paths: Dict[str, str]):
        self.fonts = {}
        self.font_paths = font_paths
        self._initialize_fonts()

    def _initialize_fonts(self):
        """初始化基础字体"""
        # 初始化常规字体
        sizes = [30, 32, 34, 35, 38, 40, 45, 48, 50, 52]  # 扩展字号范围
        for size in sizes:
            try:
                self.fonts[f'regular_{size}'] = ImageFont.truetype(self.font_paths['regular'], size)
                self.fonts[f'bold_{size}'] = ImageFont.truetype(self.font_paths['bold'], size)
            except Exception as e:
                print(f"Warning: Failed to load font size {size}: {e}")
        
        # 初始化 emoji 字体 - 尝试多种大小
        emoji_sizes = [30, 34, 38, 42]
        self.emoji_font = None
        
        for size in emoji_sizes:
            try:
                emoji_font = ImageFont.truetype(self.font_paths['emoji'], size)
                # 保存所有尺寸的emoji字体
                self.fonts[f'emoji_{size}'] = emoji_font
                # 默认使用30大小的作为主emoji字体
                if size == 30:
                    self.emoji_font = emoji_font
            except Exception as e:
                print(f"Warning: Failed to load emoji font size {size}: {e}")
        
        # 如果emoji字体加载失败，使用常规字体作为后备
        if not self.emoji_font and 'regular_30' in self.fonts:
            print("Using regular font as emoji fallback")
            self.emoji_font = self.fonts['regular_30']
        
        # 确保emoji_30存在
        if self.emoji_font:
            self.fonts['emoji_30'] = self.emoji_font
        elif 'regular_30' in self.fonts:
            self.fonts['emoji_30'] = self.fonts['regular_30']

    def get_font(self, style: TextStyle) -> ImageFont.FreeTypeFont:
        """获取对应样式的字体"""
        if style.font_name == 'emoji':
            return self.emoji_font

        base_name = 'bold' if style.font_name == 'bold' or style.is_title or style.is_category else 'regular'
        font_key = f'{base_name}_{style.font_size}'

        if font_key not in self.fonts:
            # 动态创建新字号的字体
            self.fonts[font_key] = ImageFont.truetype(
                self.font_paths['bold' if base_name == 'bold' else 'regular'],
                style.font_size
            )

        return self.fonts[font_key]


def get_gradient_styles() -> List[Dict[str, tuple]]:
    """
    获取精心设计的背景渐变样式
    """
    return [
        # 新增的炫酷渐变
        # 霓虹系列
        {
            "start_color": (0, 31, 63),  # 深蓝
            "end_color": (0, 116, 217)  # 亮蓝
        },
        {
            "start_color": (240, 18, 190),  # 亮紫红
            "end_color": (163, 11, 252)  # 紫色
        },
        {
            "start_color": (24, 90, 157),  # 深蓝
            "end_color": (67, 206, 162)  # 青绿
        },
        {
            "start_color": (29, 43, 100),  # 藏青
            "end_color": (248, 205, 218)  # 浅粉
        },
        
        # 未来科技系列
        {
            "start_color": (45, 45, 45),  # 深灰
            "end_color": (0, 150, 255)  # 亮蓝
        },
        {
            "start_color": (20, 30, 48),  # 深蓝
            "end_color": (36, 59, 85)  # 钢青色
        },
        {
            "start_color": (11, 72, 107),  # 深海蓝
            "end_color": (245, 98, 23)  # 橙色
        },
        {
            "start_color": (0, 0, 0),  # 纯黑
            "end_color": (117, 19, 93)  # 深紫
        },
        
        # 经典渐变
        {
            "start_color": (0, 123, 255),  # 蓝色
            "end_color": (0, 234, 231)  # 青绿色
        },
        {
            "start_color": (100, 43, 115),  # 紫色
            "end_color": (198, 66, 110)  # 粉色
        },
        
        # Mac 高级白
        {
            "start_color": (246, 246, 248),  # 珍珠白
            "end_color": (250, 250, 252)  # 云雾白
        },
        {
            "start_color": (245, 245, 247),  # 奶白色
            "end_color": (248, 248, 250)  # 象牙白
        },
        # macOS Monterey 风格
        {
            "start_color": (191, 203, 255),  # 淡蓝紫
            "end_color": (255, 203, 237)  # 浅粉红
        },
        {
            "start_color": (168, 225, 255),  # 天空蓝
            "end_color": (203, 255, 242)  # 清新薄荷
        },

        # 优雅渐变系列
        {
            "start_color": (255, 209, 209),  # 珊瑚粉
            "end_color": (243, 209, 255)  # 淡紫色
        },
        {
            "start_color": (255, 230, 209),  # 奶橘色
            "end_color": (255, 209, 247)  # 粉紫色
        },

        # 清新通透
        {
            "start_color": (213, 255, 219),  # 嫩绿色
            "end_color": (209, 247, 255)  # 浅蓝色
        },
        {
            "start_color": (255, 236, 209),  # 杏橘色
            "end_color": (255, 209, 216)  # 浅玫瑰
        },

        # 高级灰调
        {
            "start_color": (237, 240, 245),  # 珍珠灰
            "end_color": (245, 237, 245)  # 薰衣草灰
        },
        {
            "start_color": (240, 245, 255),  # 云雾蓝
            "end_color": (245, 240, 245)  # 淡紫灰
        },

        # 梦幻糖果色
        {
            "start_color": (255, 223, 242),  # 棉花糖粉
            "end_color": (242, 223, 255)  # 淡紫丁香
        },
        {
            "start_color": (223, 255, 247),  # 薄荷绿
            "end_color": (223, 242, 255)  # 天空蓝
        },

        # 高饱和度系列
        {
            "start_color": (255, 192, 203),  # 粉红色
            "end_color": (192, 203, 255)  # 淡紫蓝
        },
        {
            "start_color": (192, 255, 238),  # 碧绿色
            "end_color": (238, 192, 255)  # 淡紫色
        },

        # 静谧系列
        {
            "start_color": (230, 240, 255),  # 宁静蓝
            "end_color": (255, 240, 245)  # 柔粉色
        },
        {
            "start_color": (245, 240, 255),  # 淡紫色
            "end_color": (240, 255, 240)  # 清新绿
        },

        # 温柔渐变
        {
            "start_color": (255, 235, 235),  # 温柔粉
            "end_color": (235, 235, 255)  # 淡雅紫
        },
        {
            "start_color": (235, 255, 235),  # 嫩芽绿
            "end_color": (255, 235, 245)  # 浅粉红
        }
    ]


def create_gradient_background(width: int, height: int) -> Image.Image:
    """创建高级渐变背景 - 支持多种渐变效果"""
    gradient_styles = get_gradient_styles()
    style = random.choice(gradient_styles)
    start_color = style["start_color"]
    end_color = style["end_color"]

    # 创建RGBA模式的基础图像
    base = Image.new('RGBA', (width, height), (255, 255, 255, 0))
    draw = ImageDraw.Draw(base)

    # 随机选择渐变类型
    gradient_type = random.choice(["diagonal", "linear", "radial"])
    
    # 对角线渐变 (左上到右下)
    if gradient_type == "diagonal":
        max_distance = math.sqrt(width**2 + height**2)
        for y in range(height):
            for x in range(width):
                # 计算当前点到左上角的距离占总对角线的比例
                distance = math.sqrt(x**2 + y**2)
                position = distance / max_distance
                r = int(start_color[0] * (1 - position) + end_color[0] * position)
                g = int(start_color[1] * (1 - position) + end_color[1] * position)
                b = int(start_color[2] * (1 - position) + end_color[2] * position)
                # 设置完全不透明
                draw.point((x, y), fill=(r, g, b, 255))
    
    # 线性渐变 (上到下)
    elif gradient_type == "linear":
        for y in range(height):
            position = y / height
            r = int(start_color[0] * (1 - position) + end_color[0] * position)
            g = int(start_color[1] * (1 - position) + end_color[1] * position)
            b = int(start_color[2] * (1 - position) + end_color[2] * position)
            draw.line([(0, y), (width, y)], fill=(r, g, b, 255))
    
    # 径向渐变 (中心向外)
    elif gradient_type == "radial":
        center_x, center_y = width // 2, height // 2
        max_distance = math.sqrt((width // 2)**2 + (height // 2)**2)
        for y in range(height):
            for x in range(width):
                distance = math.sqrt((x - center_x)**2 + (y - center_y)**2)
                position = min(1.0, distance / max_distance)
                r = int(start_color[0] * (1 - position) + end_color[0] * position)
                g = int(start_color[1] * (1 - position) + end_color[1] * position)
                b = int(start_color[2] * (1 - position) + end_color[2] * position)
                draw.point((x, y), fill=(r, g, b, 255))
    
    # 添加一些随机的视觉效果
    add_effects = random.choice([True, False])
    if add_effects and sum(start_color) < 500:  # 对暗色背景添加效果
        # 添加一些随机的亮点
        num_dots = random.randint(20, 100)
        for _ in range(num_dots):
            x = random.randint(0, width - 1)
            y = random.randint(0, height - 1)
            size = random.randint(1, 3)
            brightness = random.randint(160, 255)
            alpha = random.randint(100, 200)
            draw.ellipse([(x-size, y-size), (x+size, y+size)], 
                         fill=(brightness, brightness, brightness, alpha))
        
        # 有时添加更大的光斑
        if random.random() > 0.7:
            for _ in range(3):
                x = random.randint(0, width - 1)
                y = random.randint(0, height - 1)
                size = random.randint(50, 150)
                brightness = random.randint(100, 180)
                alpha = random.randint(10, 40)
                draw.ellipse([(x-size, y-size), (x+size, y+size)], 
                             fill=(brightness, brightness, brightness, alpha))

    return base


def get_theme_colors() -> Tuple[tuple, str, bool]:
    """获取主题颜色配置"""
    current_hour = datetime.now().hour
    current_minute = datetime.now().minute

    if (current_hour == 8 and current_minute >= 30) or (9 <= current_hour < 19):
        use_dark = random.random() < 0.1
    else:
        use_dark = True

    if use_dark:
        # 深色毛玻璃效果: 深色半透明背景(50%透明度) + 白色文字
        return ((50, 50, 50, 128), "#FFFFFF", True)  # alpha值调整为128实现50%透明度
    else:
        # 浅色毛玻璃效果: 白色半透明背景(50%透明度) + 黑色文字
        return ((255, 255, 255, 128), "#000000", False)  # alpha值调整为128实现50%透明度


def create_rounded_rectangle(image: Image.Image, x: int, y: int, w: int, h: int, radius: int, bg_color: tuple):
    """创建圆角毛玻璃矩形"""
    # 创建透明背景的矩形
    rectangle = Image.new('RGBA', (int(w), int(h)), (0, 0, 0, 0))
    draw = ImageDraw.Draw(rectangle)

    # 绘制带透明度的圆角矩形
    draw.rounded_rectangle(
        [(0, 0), (int(w), int(h))],
        radius,
        fill=bg_color  # 使用带透明度的背景色
    )

    # 使用alpha通道混合方式粘贴到背景上
    image.paste(rectangle, (int(x), int(y)), rectangle)


def round_corner_image(image: Image.Image, radius: int) -> Image.Image:
    """将图片转为圆角"""
    # 创建一个带有圆角的蒙版
    circle = Image.new('L', (radius * 2, radius * 2), 0)
    draw = ImageDraw.Draw(circle)
    draw.ellipse((0, 0, radius * 2, radius * 2), fill=255)

    # 创建一个完整的蒙版
    mask = Image.new('L', image.size, 255)

    # 添加四个圆角
    mask.paste(circle.crop((0, 0, radius, radius)), (0, 0))  # 左上
    mask.paste(circle.crop((radius, 0, radius * 2, radius)), (image.width - radius, 0))  # 右上
    mask.paste(circle.crop((0, radius, radius, radius * 2)), (0, image.height - radius))  # 左下
    mask.paste(circle.crop((radius, radius, radius * 2, radius * 2)),
               (image.width - radius, image.height - radius))  # 右下

    # 创建一个空白的透明图像
    output = Image.new('RGBA', image.size, (0, 0, 0, 0))

    # 将原图和蒙版合并
    output.paste(image, (0, 0))
    output.putalpha(mask)

    return output


def add_title_image(background: Image.Image, title_image_path: str, rect_x: int, rect_y: int, rect_width: int) -> int:
    """添加标题图片"""
    try:
        with Image.open(title_image_path) as title_img:
            # 如果图片不是RGBA模式，转换为RGBA
            if title_img.mode != 'RGBA':
                title_img = title_img.convert('RGBA')

            # 设置图片宽度等于文字区域宽度
            target_width = rect_width - 40  # 左右各留20像素边距

            # 计算等比例缩放后的高度
            aspect_ratio = title_img.height / title_img.width
            target_height = int(target_width * aspect_ratio)

            # 调整图片大小
            resized_img = title_img.resize((int(target_width), target_height), Image.Resampling.LANCZOS)

            # 添加圆角
            rounded_img = round_corner_image(resized_img, radius=20)  # 可以调整圆角半径

            # 计算居中位置（水平方向）
            x = rect_x + 20  # 左边距20像素
            y = rect_y + 20  # 顶部边距20像素

            # 粘贴图片（使用图片自身的alpha通道）
            background.paste(rounded_img, (x, y), rounded_img)

            return y + target_height + 20  # 返回图片底部位置加上20像素间距
    except Exception as e:
        print(f"Error loading title image: {e}")
        return rect_y + 30


class MarkdownParser:
    """Markdown解析器"""

    def __init__(self):
        self.reset()

    def reset(self):
        self.segments = []
        self.current_section = None  # 当前处理的段落类型

    def parse(self, text: str) -> List[TextSegment]:
        """解析整个文本"""
        self.reset()
        segments = []
        lines = text.splitlines()

        for i, line in enumerate(lines):
            line = line.strip()
            if not line:
                # 只有当下一行有内容时才添加空行
                next_has_content = False
                for next_line in lines[i + 1:]:
                    if next_line.strip():
                        next_has_content = True
                        break
                if next_has_content:
                    style = TextStyle(
                        line_spacing=20 if segments and segments[-1].style.is_title else 15
                    )
                    segments.append(TextSegment(text='', style=style))
                continue

            # 处理常规行
            line_segments = self.parse_line(line)
            segments.extend(line_segments)

            # 只在确定有下一行内容时添加空行
            if i < len(lines) - 1:
                has_next_content = False
                for next_line in lines[i + 1:]:
                    if next_line.strip():
                        has_next_content = True
                        break
                if has_next_content:
                    style = line_segments[-1].style
                    segments.append(TextSegment(text='', style=TextStyle(line_spacing=style.line_spacing)))

        # 最后添加签名，添加适当的边距
        if segments:
            signature = TextSegment(
                text="                                         —By 瑶瑶",
                style=TextStyle(
                    font_name='regular', 
                    indent=0, 
                    line_spacing=30,  # 增加行间距，确保与边框有足够距离
                    font_size=30  # 设置合适的字体大小
                )
            )
            segments.append(signature)

        return segments

    def is_category_title(self, text: str) -> bool:
        """判断是否为分类标题"""
        return text.strip() in ['国内要闻', '国际动态']

    def process_title_marks(self, text: str) -> str:
        """处理标题标记"""
        # 移除 ** 标记
        text = re.sub(r'\*\*(.+?)\*\*', r'\1', text)
        # 统一中文冒号
        text = text.replace(':', '：')
        return text

    def split_number_and_content(self, text: str) -> Tuple[str, str]:
        """分离序号和内容"""
        match = re.match(r'(\d+)\.\s*(.+)', text)
        if match:
            return match.group(1), match.group(2)
        return '', text

    def split_title_and_content(self, text: str) -> Tuple[str, str]:
        """分离标题和内容"""
        parts = text.split('：', 1)
        if len(parts) == 2:
            return parts[0] + '：', parts[1].strip()
        return text, ''

    def parse_line(self, text: str) -> List[TextSegment]:
        """解析单行文本"""
        if not text.strip():
            return [TextSegment(text='', style=TextStyle())]

        # 处理一级标题
        if text.startswith('# '):
            style = TextStyle(
                font_name='bold',
                font_size=40,
                is_title=True,
                indent=0
            )
            return [TextSegment(text=text[2:].strip(), style=style)]

        # 处理二级标题
        if text.startswith('## '):
            style = TextStyle(
                font_name='bold',
                font_size=35,
                is_title=True,
                line_spacing=25,
                indent=0
            )
            self.current_section = text[3:].strip()
            return [TextSegment(text=self.current_section, style=style)]

        # 处理分类标题
        if self.is_category_title(text):
            style = TextStyle(
                font_name='bold',
                font_size=35,
                is_category=True,
                line_spacing=25,
                indent=0
            )
            return [TextSegment(text=text.strip(), style=style)]

        # 处理emoji标题格式
        if text.strip() and emoji.is_emoji(text[0]):
            # 移除文本中的加粗标记 **
            content = text.strip()
            if '**' in content:
                content = content.replace('**', '')

            style = TextStyle(
                font_name='bold',
                font_size=40,  # 使用H1的字体大小
                is_title=True,
                line_spacing=25,
                indent=0
            )
            return [TextSegment(text=content, style=style)]

        # 处理带序号的新闻条目
        number, content = self.split_number_and_content(text)
        if number:
            content = self.process_title_marks(content)
            title, body = self.split_title_and_content(content)
            segments = []

            title_style = TextStyle(
                font_name='bold',
                indent=0,
                is_title=True,
                line_spacing=15 if body else 20
            )
            segments.append(TextSegment(
                text=f"{number}. {title}",
                style=title_style
            ))

            if body:
                content_style = TextStyle(
                    font_name='regular',
                    indent=40,
                    line_spacing=20
                )
                segments.append(TextSegment(
                    text=body,
                    style=content_style
                ))
            return segments

        # 处理破折号开头的内容
        if text.strip().startswith('-'):
            style = TextStyle(
                font_name='regular',
                indent=40,
                line_spacing=15
            )
            return [TextSegment(text=text.strip(), style=style)]

        # 处理普通文本
        style = TextStyle(
            font_name='regular',
            indent=40 if self.current_section else 0,
            line_spacing=15
        )

        return [TextSegment(text=text.strip(), style=style)]


class TextRenderer:
    """文本渲染器"""

    def __init__(self, font_manager: FontManager, max_width: int):
        self.font_manager = font_manager
        self.max_width = max_width
        self.temp_image = Image.new('RGBA', (2000, 100))
        self.temp_draw = ImageDraw.Draw(self.temp_image)

    def measure_text(self, text: str, font: ImageFont.FreeTypeFont,
                     emoji_font: Optional[ImageFont.FreeTypeFont] = None) -> Tuple[int, int]:
        """测量文本尺寸，考虑emoji"""
        total_width = 0
        max_height = 0

        for char in text:
            if emoji.is_emoji(char) and emoji_font:
                bbox = self.temp_draw.textbbox((0, 0), char, font=emoji_font)
                width = bbox[2] - bbox[0]
                height = bbox[3] - bbox[1]
            else:
                bbox = self.temp_draw.textbbox((0, 0), char, font=font)
                width = bbox[2] - bbox[0]
                height = bbox[3] - bbox[1]

            total_width += width
            max_height = max(max_height, height)

        return total_width, max_height

    def draw_text_with_emoji(self, draw: ImageDraw.ImageDraw, pos: Tuple[int, int], text: str,
                             font: ImageFont.FreeTypeFont, emoji_font: ImageFont.FreeTypeFont,
                             fill: str = "white") -> int:
        """绘制包含emoji的文本，返回绘制宽度"""
        x, y = pos
        total_width = 0

        for char in text:
            if emoji.is_emoji(char):
                # 使用emoji字体，并启用彩色渲染
                bbox = draw.textbbox((x, y), char, font=emoji_font)
                draw.text((x, y), char, font=emoji_font, embedded_color=True)  # 添加 embedded_color=True
                char_width = bbox[2] - bbox[0]
            else:
                # 使用常规字体
                bbox = draw.textbbox((x, y), char, font=font)
                draw.text((x, y), char, font=font, fill=fill)
                char_width = bbox[2] - bbox[0]

            x += char_width
            total_width += char_width

        return total_width

    def calculate_height(self, processed_lines: List[ProcessedLine]) -> int:
        """计算总高度，确保不在最后添加额外间距"""
        total_height = 0
        prev_line = None

        for i, line in enumerate(processed_lines):
            if not line.text.strip():
                # 只有当不是最后一行，且后面还有内容时才添加间距
                if i < len(processed_lines) - 1 and any(l.text.strip() for l in processed_lines[i + 1:]):
                    if prev_line:
                        total_height += prev_line.style.line_spacing
                continue

            # 计算当前行高度
            line_height = line.height * line.line_count

            # 添加行间距，但对于最后一行（签名）特殊处理
            if prev_line:
                if i == len(processed_lines) - 1:  # 如果是最后一行（签名）
                    total_height += 30  # 确保签名与上文有足够间距
                elif prev_line.style.is_category:
                    total_height += 30
                elif prev_line.style.is_title and not line.style.is_title:
                    total_height += 20
                else:
                    total_height += line.style.line_spacing

            total_height += line_height
            prev_line = line

        # 为最后一行（签名）添加额外的底部边距
        if processed_lines and processed_lines[-1].text.strip():
            total_height += 30  # 确保签名与底部边框有足够间距

        return total_height

    def split_text_to_lines(self, segment: TextSegment, available_width: int) -> List[ProcessedLine]:
        """将文本分割成合适宽度的行，支持emoji"""
        if not segment.text.strip():
            return [ProcessedLine(text='', style=segment.style, height=0, line_count=1)]

        font = self.font_manager.get_font(segment.style)
        emoji_font = self.font_manager.fonts['emoji_30']
        words = []
        current_word = ''
        processed_lines = []

        # 分词处理
        for char in segment.text:
            if emoji.is_emoji(char):
                if current_word:
                    words.append(current_word)
                    current_word = ''
                words.append(char)  # emoji作为单独的词
            elif char in [' ', '，', '。', '：', '、', '！', '？', '；']:
                if current_word:
                    words.append(current_word)
                words.append(char)
                current_word = ''
            else:
                if ord(char) > 0x4e00:  # 中文字符
                    if current_word:
                        words.append(current_word)
                        current_word = ''
                    words.append(char)
                else:
                    current_word += char

        if current_word:
            words.append(current_word)

        current_line = ''
        line_height = 0

        for word in words:
            test_line = current_line + word
            width, height = self.measure_text(test_line, font, emoji_font)
            line_height = max(line_height, height)

            if width <= available_width:
                current_line = test_line
            else:
                if current_line:
                    processed_lines.append(ProcessedLine(
                        text=current_line,
                        style=segment.style,
                        height=line_height,
                        line_count=1
                    ))
                current_line = word

        if current_line:
            processed_lines.append(ProcessedLine(
                text=current_line,
                style=segment.style,
                height=line_height,
                line_count=1
            ))

        return processed_lines


def compress_image(image_path: str, output_path: str, max_size: int = 3145728):  # 3MB in bytes
    """
    Compress an image to ensure it's under a certain file size.

    :param image_path: The path to the image to be compressed.
    :param output_path: The path where the compressed image will be saved.
    :param max_size: The maximum file size in bytes (default is 3MB).
    """
    # Open the image
    with Image.open(image_path) as img:
        # Convert to RGB if it's not already
        if img.mode != 'RGB':
            img = img.convert('RGB')

        # Define the quality to start with
        quality = 95  # Start with a high quality

        # Save the image with different qualities until the file size is acceptable
        while True:
            # Save the image with the current quality
            img.save(output_path, "PNG", optimize=True, compress_level=0)

            # Check the file size
            if os.path.getsize(output_path) <= max_size:
                break  # The file size is acceptable, break the loop

            # If the file is still too large, decrease the quality
            quality -= 5
            if quality < 10:  # To prevent an infinite loop, set a minimum quality
                break

        # If the quality is too low, you might want to handle it here
        if quality < 10:
            print("The image could not be compressed enough to meet the size requirements.")


def add_tech_decorations(image: Image.Image, rect_x: int, rect_y: int, rect_width: int, rect_height: int, 
                        is_dark: bool = True, theme_color: tuple = None) -> Image.Image:
    """添加科技感装饰效果
    
    Args:
        image: 基础图像
        rect_x, rect_y: 卡片左上角坐标
        rect_width, rect_height: 卡片尺寸
        is_dark: 是否深色主题
        theme_color: 主题颜色RGB元组
    
    Returns:
        添加了装饰效果的图像
    """
    # 创建一个新图层用于绘制装饰
    decoration = Image.new('RGBA', image.size, (0, 0, 0, 0))
    draw = ImageDraw.Draw(decoration)
    
    # 使用卡片的主题颜色，或根据主题设置默认颜色
    if theme_color is None:
        theme_color = (77, 196, 255) if is_dark else (25, 118, 210)
    
    # 设置装饰元素的透明度
    tech_alpha = 60  # 基础透明度
    
    # 1. 添加四角装饰 - 类似科技界面的角标
    corner_size = min(rect_width, rect_height) // 10
    line_width = max(2, corner_size // 15)
    
    # 左上角
    draw.line([(rect_x, rect_y + corner_size), (rect_x, rect_y), (rect_x + corner_size, rect_y)], 
              fill=(*theme_color, tech_alpha), width=line_width)
    
    # 右上角
    draw.line([(rect_x + rect_width - corner_size, rect_y), (rect_x + rect_width, rect_y), 
               (rect_x + rect_width, rect_y + corner_size)], 
              fill=(*theme_color, tech_alpha), width=line_width)
    
    # 左下角
    draw.line([(rect_x, rect_y + rect_height - corner_size), (rect_x, rect_y + rect_height), 
               (rect_x + corner_size, rect_y + rect_height)], 
              fill=(*theme_color, tech_alpha), width=line_width)
    
    # 右下角
    draw.line([(rect_x + rect_width - corner_size, rect_y + rect_height), 
               (rect_x + rect_width, rect_y + rect_height), 
               (rect_x + rect_width, rect_y + rect_height - corner_size)], 
              fill=(*theme_color, tech_alpha), width=line_width)
    
    # 2. 添加小型图案和点缀
    # 添加一些随机的小点和线条
    num_dots = random.randint(15, 30)
    for _ in range(num_dots):
        # 确保装饰在卡片周围，而不是覆盖内容
        edge = random.choice(['top', 'bottom', 'left', 'right'])
        dot_size = random.randint(1, 3)
        dot_alpha = random.randint(tech_alpha - 20, tech_alpha + 20)
        
        if edge == 'top':
            x = random.randint(rect_x, rect_x + rect_width)
            y = random.randint(max(0, rect_y - 30), rect_y)
        elif edge == 'bottom':
            x = random.randint(rect_x, rect_x + rect_width)
            y = random.randint(rect_y + rect_height, min(rect_y + rect_height + 30, image.height))
        elif edge == 'left':
            x = random.randint(max(0, rect_x - 30), rect_x)
            y = random.randint(rect_y, rect_y + rect_height)
        else:  # right
            x = random.randint(rect_x + rect_width, min(rect_x + rect_width + 30, image.width))
            y = random.randint(rect_y, rect_y + rect_height)
            
        draw.ellipse([(x - dot_size, y - dot_size), (x + dot_size, y + dot_size)], 
                     fill=(*theme_color, dot_alpha))
    
    # 3. 添加网格线效果 - 仅在卡片边缘
    grid_spacing = max(rect_width, rect_height) // 20
    grid_alpha = tech_alpha // 2
    
    # 顶部网格线
    for x in range(rect_x, rect_x + rect_width, grid_spacing):
        draw.line([(x, rect_y - 10), (x, rect_y)], fill=(*theme_color, grid_alpha), width=1)
    
    # 底部网格线
    for x in range(rect_x, rect_x + rect_width, grid_spacing):
        draw.line([(x, rect_y + rect_height), (x, rect_y + rect_height + 10)], 
                  fill=(*theme_color, grid_alpha), width=1)
    
    # 左侧网格线
    for y in range(rect_y, rect_y + rect_height, grid_spacing):
        draw.line([(rect_x - 10, y), (rect_x, y)], fill=(*theme_color, grid_alpha), width=1)
    
    # 右侧网格线
    for y in range(rect_y, rect_y + rect_height, grid_spacing):
        draw.line([(rect_x + rect_width, y), (rect_x + rect_width + 10, y)], 
                  fill=(*theme_color, grid_alpha), width=1)
    
    # 4. 添加一些科技风的装饰线条
    num_lines = random.randint(3, 6)
    for _ in range(num_lines):
        line_alpha = random.randint(tech_alpha - 30, tech_alpha - 10)
        # 随机选择一个区域进行装饰
        section = random.choice(['top', 'bottom', 'left', 'right'])
        line_length = random.randint(30, 100)
        
        if section == 'top':
            x1 = random.randint(rect_x, rect_x + rect_width - line_length)
            y1 = rect_y - random.randint(5, 15)
            draw.line([(x1, y1), (x1 + line_length, y1)], 
                      fill=(*theme_color, line_alpha), width=1)
            # 有时添加一个垂直连接线
            if random.random() > 0.5:
                vert_length = random.randint(5, 10)
                mid_x = x1 + line_length // 2
                draw.line([(mid_x, y1), (mid_x, y1 - vert_length)], 
                          fill=(*theme_color, line_alpha), width=1)
                
        elif section == 'bottom':
            x1 = random.randint(rect_x, rect_x + rect_width - line_length)
            y1 = rect_y + rect_height + random.randint(5, 15)
            draw.line([(x1, y1), (x1 + line_length, y1)], 
                      fill=(*theme_color, line_alpha), width=1)
            if random.random() > 0.5:
                vert_length = random.randint(5, 10)
                mid_x = x1 + line_length // 2
                draw.line([(mid_x, y1), (mid_x, y1 + vert_length)], 
                          fill=(*theme_color, line_alpha), width=1)
                
        elif section == 'left':
            x1 = rect_x - random.randint(5, 15)
            y1 = random.randint(rect_y, rect_y + rect_height - line_length)
            draw.line([(x1, y1), (x1, y1 + line_length)], 
                      fill=(*theme_color, line_alpha), width=1)
            if random.random() > 0.5:
                horiz_length = random.randint(5, 10)
                mid_y = y1 + line_length // 2
                draw.line([(x1, mid_y), (x1 - horiz_length, mid_y)], 
                          fill=(*theme_color, line_alpha), width=1)
                
        else:  # right
            x1 = rect_x + rect_width + random.randint(5, 15)
            y1 = random.randint(rect_y, rect_y + rect_height - line_length)
            draw.line([(x1, y1), (x1, y1 + line_length)], 
                      fill=(*theme_color, line_alpha), width=1)
            if random.random() > 0.5:
                horiz_length = random.randint(5, 10)
                mid_y = y1 + line_length // 2
                draw.line([(x1, mid_y), (x1 + horiz_length, mid_y)], 
                          fill=(*theme_color, line_alpha), width=1)
    
    # 5. 添加圆形装饰 - 类似科技界面的扫描/雷达效果
    if random.random() > 0.5:
        # 在四个角之一添加圆形装饰
        corner = random.choice(['tl', 'tr', 'bl', 'br'])
        circle_size = min(rect_width, rect_height) // 6
        circle_alpha = tech_alpha - 20
        
        if corner == 'tl':
            cx, cy = rect_x, rect_y
        elif corner == 'tr':
            cx, cy = rect_x + rect_width, rect_y
        elif corner == 'bl':
            cx, cy = rect_x, rect_y + rect_height
        else:  # br
            cx, cy = rect_x + rect_width, rect_y + rect_height
        
        # 绘制两个同心圆
        for radius in [circle_size, circle_size * 0.7, circle_size * 0.4]:
            draw.arc([cx - radius, cy - radius, cx + radius, cy + radius], 
                    0, 360, fill=(*theme_color, circle_alpha), width=1)
        
        # 添加一些径向线
        for i in range(0, 360, 45):
            rad = math.radians(i)
            x2 = cx + math.cos(rad) * circle_size
            y2 = cy + math.sin(rad) * circle_size
            draw.line([(cx, cy), (x2, y2)], fill=(*theme_color, circle_alpha), width=1)
    
    # 合并装饰层与原图
    return Image.alpha_composite(image, decoration)


def create_gradient_border(width: int, height: int, radius: int, 
                         main_color: str, accent_color: str = None,
                         border_width: int = 4, border_style: str = 'gradient',
                         inner_style: str = 'solid') -> Image.Image:
    """创建高级渐变边框效果
    
    Args:
        width, height: 图像尺寸
        radius: 圆角半径
        main_color: 主要颜色 (HEX格式)
        accent_color: 辅助颜色 (HEX格式)，用于渐变效果
        border_width: 边框宽度
        border_style: 边框风格，可选 'gradient', 'double', 'neon', 'stripe', 'glow'
        inner_style: 内层边框样式，可选 'solid', 'dotted', 'dashed', 'wavy'
        
    Returns:
        边框图像
    """
    # 创建透明底图
    border = Image.new('RGBA', (width, height), (0, 0, 0, 0))
    draw = ImageDraw.Draw(border)
    
    # 解析颜色
    main_rgb = ImageColor.getrgb(main_color)
    
    # 如果没有提供辅助颜色，创建一个互补色
    if not accent_color:
        # 创建一个互补/对比色
        h, s, v = rgb_to_hsv(*main_rgb)
        h = (h + 180) % 360  # 互补色
        accent_rgb = hsv_to_rgb(h, s, v)
    else:
        accent_rgb = ImageColor.getrgb(accent_color)
    
    # 基础位置
    rect_pos = [(border_width//2, border_width//2), 
               (width-border_width//2-1, height-border_width//2-1)]
    
    # 渐变边框 - 水平渐变色
    if border_style == 'gradient':
        # 我们需要创建多条线段来实现渐变，不能直接画一个渐变矩形
        segments = 100  # 分段数
        
        # 上边框 - 从左到右的渐变
        for i in range(segments):
            x1 = rect_pos[0][0] + (rect_pos[1][0] - rect_pos[0][0]) * i / segments
            x2 = rect_pos[0][0] + (rect_pos[1][0] - rect_pos[0][0]) * (i + 1) / segments
            
            # 计算当前段的颜色
            r = int(main_rgb[0] + (accent_rgb[0] - main_rgb[0]) * i / segments)
            g = int(main_rgb[1] + (accent_rgb[1] - main_rgb[1]) * i / segments)
            b = int(main_rgb[2] + (accent_rgb[2] - main_rgb[2]) * i / segments)
            
            color = (r, g, b, 255)
            
            # 绘制上边框线段
            if i < segments - radius//(width//segments) and i > radius//(width//segments):
                draw.line([(x1, rect_pos[0][1]), (x2, rect_pos[0][1])], fill=color, width=border_width)
                
            # 绘制下边框线段 - 颜色反向
            color = (
                int(accent_rgb[0] + (main_rgb[0] - accent_rgb[0]) * i / segments),
                int(accent_rgb[1] + (main_rgb[1] - accent_rgb[1]) * i / segments),
                int(accent_rgb[2] + (main_rgb[2] - accent_rgb[2]) * i / segments),
                255
            )
            
            if i < segments - radius//(width//segments) and i > radius//(width//segments):
                draw.line([(x1, rect_pos[1][1]), (x2, rect_pos[1][1])], fill=color, width=border_width)
        
        # 左边框 - 从上到下的渐变
        for i in range(segments):
            y1 = rect_pos[0][1] + (rect_pos[1][1] - rect_pos[0][1]) * i / segments
            y2 = rect_pos[0][1] + (rect_pos[1][1] - rect_pos[0][1]) * (i + 1) / segments
            
            # 计算当前段的颜色
            r = int(main_rgb[0] + (accent_rgb[0] - main_rgb[0]) * i / segments)
            g = int(main_rgb[1] + (accent_rgb[1] - main_rgb[1]) * i / segments)
            b = int(main_rgb[2] + (accent_rgb[2] - main_rgb[2]) * i / segments)
            
            color = (r, g, b, 255)
            
            # 绘制左边框线段
            if i < segments - radius//(height//segments) and i > radius//(height//segments):
                draw.line([(rect_pos[0][0], y1), (rect_pos[0][0], y2)], fill=color, width=border_width)
                
            # 绘制右边框线段 - 颜色反向
            color = (
                int(accent_rgb[0] + (main_rgb[0] - accent_rgb[0]) * i / segments),
                int(accent_rgb[1] + (main_rgb[1] - accent_rgb[1]) * i / segments),
                int(accent_rgb[2] + (main_rgb[2] - accent_rgb[2]) * i / segments),
                255
            )
            
            if i < segments - radius//(height//segments) and i > radius//(height//segments):
                draw.line([(rect_pos[1][0], y1), (rect_pos[1][0], y2)], fill=color, width=border_width)
                
        # 绘制圆角部分
        draw.arc([rect_pos[0][0] - radius + border_width, rect_pos[0][1] - radius + border_width, 
                 rect_pos[0][0] + radius, rect_pos[0][1] + radius], 
                180, 270, fill=main_rgb+(255,), width=border_width)
        
        draw.arc([rect_pos[1][0] - radius, rect_pos[0][1] - radius + border_width, 
                 rect_pos[1][0] + radius - border_width, rect_pos[0][1] + radius], 
                270, 360, fill=accent_rgb+(255,), width=border_width)
        
        draw.arc([rect_pos[0][0] - radius + border_width, rect_pos[1][1] - radius, 
                 rect_pos[0][0] + radius, rect_pos[1][1] + radius - border_width], 
                90, 180, fill=accent_rgb+(255,), width=border_width)
        
        draw.arc([rect_pos[1][0] - radius, rect_pos[1][1] - radius, 
                 rect_pos[1][0] + radius - border_width, rect_pos[1][1] + radius - border_width], 
                0, 90, fill=main_rgb+(255,), width=border_width)
    
    # 双层边框效果 - 内外两层不同颜色
    elif border_style == 'double':
        # 外层边框
        outer_width = border_width
        draw.rounded_rectangle(rect_pos, radius=radius, 
                             outline=main_rgb+(255,), width=outer_width)
        
        # 内层边框
        inner_margin = border_width * 2  # 内边框的间距
        inner_rect = [(rect_pos[0][0] + inner_margin, rect_pos[0][1] + inner_margin),
                     (rect_pos[1][0] - inner_margin, rect_pos[1][1] - inner_margin)]
        inner_radius = max(radius - inner_margin, 1)
        inner_width = max(1, border_width//2)
        
        # 根据内层样式绘制不同类型的内边框
        if inner_style == 'solid':
            # 实线边框
            draw.rounded_rectangle(inner_rect, radius=inner_radius, 
                                 outline=accent_rgb+(255,), width=inner_width)
        
        elif inner_style == 'dotted':
            # 点状边框
            # 计算内边框四条边的路径点
            top_left = (inner_rect[0][0], inner_rect[0][1])
            top_right = (inner_rect[1][0], inner_rect[0][1])
            bottom_right = (inner_rect[1][0], inner_rect[1][1])
            bottom_left = (inner_rect[0][0], inner_rect[1][1])
            
            # 计算每个圆点之间的间距，确保在边框上均匀分布
            dot_size = max(2, inner_width)
            dot_spacing = dot_size * 3  # 点之间的间距
            
            # 绘制顶部边框点
            top_length = top_right[0] - top_left[0] - 2*inner_radius
            num_dots_top = max(1, int(top_length // dot_spacing))
            space_top = top_length / num_dots_top
            
            for i in range(num_dots_top):
                x = top_left[0] + inner_radius + i * space_top + space_top/2
                y = top_left[1]
                dot_radius = dot_size // 2
                draw.ellipse([(x-dot_radius, y-dot_radius), (x+dot_radius, y+dot_radius)], 
                           fill=accent_rgb+(255,))
            
            # 绘制右侧边框点
            right_length = bottom_right[1] - top_right[1] - 2*inner_radius
            num_dots_right = max(1, int(right_length // dot_spacing))
            space_right = right_length / num_dots_right
            
            for i in range(num_dots_right):
                x = top_right[0]
                y = top_right[1] + inner_radius + i * space_right + space_right/2
                dot_radius = dot_size // 2
                draw.ellipse([(x-dot_radius, y-dot_radius), (x+dot_radius, y+dot_radius)], 
                           fill=accent_rgb+(255,))
            
            # 绘制底部边框点
            bottom_length = bottom_right[0] - bottom_left[0] - 2*inner_radius
            num_dots_bottom = max(1, int(bottom_length // dot_spacing))
            space_bottom = bottom_length / num_dots_bottom
            
            for i in range(num_dots_bottom):
                x = bottom_left[0] + inner_radius + i * space_bottom + space_bottom/2
                y = bottom_left[1]
                dot_radius = dot_size // 2
                draw.ellipse([(x-dot_radius, y-dot_radius), (x+dot_radius, y+dot_radius)], 
                           fill=accent_rgb+(255,))
            
            # 绘制左侧边框点
            left_length = bottom_left[1] - top_left[1] - 2*inner_radius
            num_dots_left = max(1, int(left_length // dot_spacing))
            space_left = left_length / num_dots_left
            
            for i in range(num_dots_left):
                x = top_left[0]
                y = top_left[1] + inner_radius + i * space_left + space_left/2
                dot_radius = dot_size // 2
                draw.ellipse([(x-dot_radius, y-dot_radius), (x+dot_radius, y+dot_radius)], 
                           fill=accent_rgb+(255,))
                
            # 绘制四个角的圆弧
            draw.arc([inner_rect[0][0], inner_rect[0][1], 
                     inner_rect[0][0] + 2*inner_radius, inner_rect[0][1] + 2*inner_radius], 
                    180, 270, fill=accent_rgb+(255,), width=inner_width)
            
            draw.arc([inner_rect[1][0] - 2*inner_radius, inner_rect[0][1], 
                     inner_rect[1][0], inner_rect[0][1] + 2*inner_radius], 
                    270, 360, fill=accent_rgb+(255,), width=inner_width)
            
            draw.arc([inner_rect[0][0], inner_rect[1][1] - 2*inner_radius, 
                     inner_rect[0][0] + 2*inner_radius, inner_rect[1][1]], 
                    90, 180, fill=accent_rgb+(255,), width=inner_width)
            
            draw.arc([inner_rect[1][0] - 2*inner_radius, inner_rect[1][1] - 2*inner_radius, 
                     inner_rect[1][0], inner_rect[1][1]], 
                    0, 90, fill=accent_rgb+(255,), width=inner_width)
        
        elif inner_style == 'dashed':
            # 虚线边框
            dash_length = max(4, inner_width * 2)
            gap_length = dash_length
            
            # 计算每条边需要的虚线段数
            top_length = inner_rect[1][0] - inner_rect[0][0] - 2*inner_radius
            right_length = inner_rect[1][1] - inner_rect[0][1] - 2*inner_radius
            bottom_length = inner_rect[1][0] - inner_rect[0][0] - 2*inner_radius
            left_length = inner_rect[1][1] - inner_rect[0][1] - 2*inner_radius
            
            # 顶部虚线
            dash_count_top = int(top_length / (dash_length + gap_length))
            segment_length_top = top_length / dash_count_top if dash_count_top > 0 else top_length
            
            for i in range(dash_count_top):
                start_x = inner_rect[0][0] + inner_radius + i * segment_length_top
                end_x = start_x + dash_length
                if end_x > inner_rect[1][0] - inner_radius:
                    end_x = inner_rect[1][0] - inner_radius
                draw.line([(start_x, inner_rect[0][1]), (end_x, inner_rect[0][1])], 
                        fill=accent_rgb+(255,), width=inner_width)
            
            # 右侧虚线
            dash_count_right = int(right_length / (dash_length + gap_length))
            segment_length_right = right_length / dash_count_right if dash_count_right > 0 else right_length
            
            for i in range(dash_count_right):
                start_y = inner_rect[0][1] + inner_radius + i * segment_length_right
                end_y = start_y + dash_length
                if end_y > inner_rect[1][1] - inner_radius:
                    end_y = inner_rect[1][1] - inner_radius
                draw.line([(inner_rect[1][0], start_y), (inner_rect[1][0], end_y)], 
                        fill=accent_rgb+(255,), width=inner_width)
            
            # 底部虚线
            dash_count_bottom = int(bottom_length / (dash_length + gap_length))
            segment_length_bottom = bottom_length / dash_count_bottom if dash_count_bottom > 0 else bottom_length
            
            for i in range(dash_count_bottom):
                start_x = inner_rect[0][0] + inner_radius + i * segment_length_bottom
                end_x = start_x + dash_length
                if end_x > inner_rect[1][0] - inner_radius:
                    end_x = inner_rect[1][0] - inner_radius
                draw.line([(start_x, inner_rect[1][1]), (end_x, inner_rect[1][1])], 
                        fill=accent_rgb+(255,), width=inner_width)
            
            # 左侧虚线
            dash_count_left = int(left_length / (dash_length + gap_length))
            segment_length_left = left_length / dash_count_left if dash_count_left > 0 else left_length
            
            for i in range(dash_count_left):
                start_y = inner_rect[0][1] + inner_radius + i * segment_length_left
                end_y = start_y + dash_length
                if end_y > inner_rect[1][1] - inner_radius:
                    end_y = inner_rect[1][1] - inner_radius
                draw.line([(inner_rect[0][0], start_y), (inner_rect[0][0], end_y)], 
                        fill=accent_rgb+(255,), width=inner_width)
                
            # 绘制四个角的圆弧
            draw.arc([inner_rect[0][0], inner_rect[0][1], 
                     inner_rect[0][0] + 2*inner_radius, inner_rect[0][1] + 2*inner_radius], 
                    180, 270, fill=accent_rgb+(255,), width=inner_width)
            
            draw.arc([inner_rect[1][0] - 2*inner_radius, inner_rect[0][1], 
                     inner_rect[1][0], inner_rect[0][1] + 2*inner_radius], 
                    270, 360, fill=accent_rgb+(255,), width=inner_width)
            
            draw.arc([inner_rect[0][0], inner_rect[1][1] - 2*inner_radius, 
                     inner_rect[0][0] + 2*inner_radius, inner_rect[1][1]], 
                    90, 180, fill=accent_rgb+(255,), width=inner_width)
            
            draw.arc([inner_rect[1][0] - 2*inner_radius, inner_rect[1][1] - 2*inner_radius, 
                     inner_rect[1][0], inner_rect[1][1]], 
                    0, 90, fill=accent_rgb+(255,), width=inner_width)
        
        elif inner_style == 'wavy':
            # 波浪边框
            wave_amplitude = max(2, inner_width)
            wave_length = wave_amplitude * 4
            wave_segments = 20  # 分段数，越高越平滑
            
            # 计算内边框四条边的路径点
            top_left_x, top_left_y = inner_rect[0]
            bottom_right_x, bottom_right_y = inner_rect[1]
            
            # 顶部波浪
            top_points = []
            top_length = bottom_right_x - top_left_x - 2*inner_radius
            num_waves_top = max(1, int(top_length / wave_length))
            wave_step_top = top_length / (num_waves_top * wave_segments)
            
            for i in range(num_waves_top * wave_segments + 1):
                x = top_left_x + inner_radius + i * wave_step_top
                if x > bottom_right_x - inner_radius:
                    x = bottom_right_x - inner_radius
                wave_phase = (i % wave_segments) / wave_segments
                y = top_left_y + wave_amplitude * math.sin(wave_phase * 2 * math.pi)
                top_points.append((x, y))
            
            # 右侧波浪
            right_points = []
            right_length = bottom_right_y - top_left_y - 2*inner_radius
            num_waves_right = max(1, int(right_length / wave_length))
            wave_step_right = right_length / (num_waves_right * wave_segments)
            
            for i in range(num_waves_right * wave_segments + 1):
                y = top_left_y + inner_radius + i * wave_step_right
                if y > bottom_right_y - inner_radius:
                    y = bottom_right_y - inner_radius
                wave_phase = (i % wave_segments) / wave_segments
                x = bottom_right_x + wave_amplitude * math.sin(wave_phase * 2 * math.pi)
                right_points.append((x, y))
            
            # 底部波浪
            bottom_points = []
            bottom_length = bottom_right_x - top_left_x - 2*inner_radius
            num_waves_bottom = max(1, int(bottom_length / wave_length))
            wave_step_bottom = bottom_length / (num_waves_bottom * wave_segments)
            
            for i in range(num_waves_bottom * wave_segments + 1):
                x = bottom_right_x - inner_radius - i * wave_step_bottom
                if x < top_left_x + inner_radius:
                    x = top_left_x + inner_radius
                wave_phase = (i % wave_segments) / wave_segments
                y = bottom_right_y + wave_amplitude * math.sin(wave_phase * 2 * math.pi)
                bottom_points.append((x, y))
            
            # 左侧波浪
            left_points = []
            left_length = bottom_right_y - top_left_y - 2*inner_radius
            num_waves_left = max(1, int(left_length / wave_length))
            wave_step_left = left_length / (num_waves_left * wave_segments)
            
            for i in range(num_waves_left * wave_segments + 1):
                y = bottom_right_y - inner_radius - i * wave_step_left
                if y < top_left_y + inner_radius:
                    y = top_left_y + inner_radius
                wave_phase = (i % wave_segments) / wave_segments
                x = top_left_x + wave_amplitude * math.sin(wave_phase * 2 * math.pi)
                left_points.append((x, y))
            
            # 绘制波浪线
            for i in range(len(top_points) - 1):
                draw.line([top_points[i], top_points[i+1]], fill=accent_rgb+(255,), width=inner_width)
            
            for i in range(len(right_points) - 1):
                draw.line([right_points[i], right_points[i+1]], fill=accent_rgb+(255,), width=inner_width)
            
            for i in range(len(bottom_points) - 1):
                draw.line([bottom_points[i], bottom_points[i+1]], fill=accent_rgb+(255,), width=inner_width)
            
            for i in range(len(left_points) - 1):
                draw.line([left_points[i], left_points[i+1]], fill=accent_rgb+(255,), width=inner_width)
            
            # 绘制四个角的圆弧
            draw.arc([inner_rect[0][0], inner_rect[0][1], 
                     inner_rect[0][0] + 2*inner_radius, inner_rect[0][1] + 2*inner_radius], 
                    180, 270, fill=accent_rgb+(255,), width=inner_width)
            
            draw.arc([inner_rect[1][0] - 2*inner_radius, inner_rect[0][1], 
                     inner_rect[1][0], inner_rect[0][1] + 2*inner_radius], 
                    270, 360, fill=accent_rgb+(255,), width=inner_width)
            
            draw.arc([inner_rect[0][0], inner_rect[1][1] - 2*inner_radius, 
                     inner_rect[0][0] + 2*inner_radius, inner_rect[1][1]], 
                    90, 180, fill=accent_rgb+(255,), width=inner_width)
            
            draw.arc([inner_rect[1][0] - 2*inner_radius, inner_rect[1][1] - 2*inner_radius, 
                     inner_rect[1][0], inner_rect[1][1]], 
                    0, 90, fill=accent_rgb+(255,), width=inner_width)
        else:
            # 默认实线
            draw.rounded_rectangle(inner_rect, radius=inner_radius, 
                                 outline=accent_rgb+(255,), width=inner_width)
    
    # 霓虹灯发光效果
    elif border_style == 'neon':
        # 基础边框
        draw.rounded_rectangle(rect_pos, radius=radius, 
                             outline=main_rgb+(255,), width=border_width)
        
        # 创建发光层
        glow = Image.new('RGBA', (width, height), (0, 0, 0, 0))
        glow_draw = ImageDraw.Draw(glow)
        
        # 多重发光效果
        for i in range(1, 6):
            alpha = max(0, 200 - i * 40)  # 随着扩散逐渐降低透明度
            
            # 混合颜色
            glow_color = (
                int(main_rgb[0] * 0.7 + accent_rgb[0] * 0.3),
                int(main_rgb[1] * 0.7 + accent_rgb[1] * 0.3),
                int(main_rgb[2] * 0.7 + accent_rgb[2] * 0.3),
                alpha
            )
            
            # 绘制多个逐渐扩大的矩形形成辉光效果
            expanded_rect = [(rect_pos[0][0] - i*2, rect_pos[0][1] - i*2),
                           (rect_pos[1][0] + i*2, rect_pos[1][1] + i*2)]
            
            glow_draw.rounded_rectangle(expanded_rect, radius=radius + i*2, 
                                      outline=glow_color, width=1)
        
        # 模糊处理发光效果
        glow = glow.filter(ImageFilter.GaussianBlur(border_width))
        
        # 合并到边框图层
        border = Image.alpha_composite(glow, border)
    
    # 条纹边框效果
    elif border_style == 'stripe':
        stripe_count = 20  # 条纹数量
        stripe_length = min(width, height) // 8  # 条纹长度
        
        # 基础边框
        draw.rounded_rectangle(rect_pos, radius=radius, 
                             outline=main_rgb+(200,), width=border_width)
        
        # 添加条纹装饰
        for i in range(stripe_count):
            # 随机选择位置
            side = random.choice(['top', 'right', 'bottom', 'left'])
            
            # 随机选择颜色
            color_mix = random.random()
            stripe_color = (
                int(main_rgb[0] * color_mix + accent_rgb[0] * (1 - color_mix)),
                int(main_rgb[1] * color_mix + accent_rgb[1] * (1 - color_mix)),
                int(main_rgb[2] * color_mix + accent_rgb[2] * (1 - color_mix)),
                255
            )
            
            # 根据边确定起始点
            if side == 'top':
                x = random.randint(rect_pos[0][0] + radius, rect_pos[1][0] - radius)
                y = rect_pos[0][1]
                end_x = x
                end_y = y - stripe_length
            elif side == 'right':
                x = rect_pos[1][0]
                y = random.randint(rect_pos[0][1] + radius, rect_pos[1][1] - radius)
                end_x = x + stripe_length
                end_y = y
            elif side == 'bottom':
                x = random.randint(rect_pos[0][0] + radius, rect_pos[1][0] - radius)
                y = rect_pos[1][1]
                end_x = x
                end_y = y + stripe_length
            else:  # left
                x = rect_pos[0][0]
                y = random.randint(rect_pos[0][1] + radius, rect_pos[1][1] - radius)
                end_x = x - stripe_length
                end_y = y
            
            # 绘制条纹
            draw.line([(x, y), (end_x, end_y)], fill=stripe_color, width=max(1, border_width//3))
    
    # 炫光边框效果
    elif border_style == 'glow':
        # 创建主边框
        draw.rounded_rectangle(rect_pos, radius=radius, 
                             outline=main_rgb+(255,), width=border_width)
        
        # 创建发光基础
        glow_base = Image.new('RGBA', (width, height), (0, 0, 0, 0))
        glow_draw = ImageDraw.Draw(glow_base)
        
        # 绘制基础形状
        glow_draw.rounded_rectangle(rect_pos, radius=radius, fill=(0, 0, 0, 0),
                                  outline=main_rgb+(100,), width=border_width*3)
        
        # 应用模糊
        glow_effect = glow_base.filter(ImageFilter.GaussianBlur(border_width*2))
        
        # 创建第二层发光 - 使用辅助颜色
        glow_accent = Image.new('RGBA', (width, height), (0, 0, 0, 0))
        glow_accent_draw = ImageDraw.Draw(glow_accent)
        
        # 绘制略小的形状
        accent_rect = [(rect_pos[0][0] + border_width, rect_pos[0][1] + border_width),
                      (rect_pos[1][0] - border_width, rect_pos[1][1] - border_width)]
        
        glow_accent_draw.rounded_rectangle(accent_rect, radius=max(1, radius-border_width), 
                                         fill=(0, 0, 0, 0),
                                         outline=accent_rgb+(80,), width=border_width)
        
        # 应用模糊
        glow_accent_effect = glow_accent.filter(ImageFilter.GaussianBlur(border_width))
        
        # 合并所有效果
        border = Image.alpha_composite(glow_effect, border)
        border = Image.alpha_composite(glow_accent_effect, border)
    
    # 默认简单边框
    else:
        draw.rounded_rectangle(rect_pos, radius=radius, 
                             outline=main_rgb+(255,), width=border_width)
    
    return border

def rgb_to_hsv(r: int, g: int, b: int) -> tuple:
    """RGB转HSV颜色空间"""
    r, g, b = r/255.0, g/255.0, b/255.0
    mx = max(r, g, b)
    mn = min(r, g, b)
    df = mx - mn
    if mx == mn:
        h = 0
    elif mx == r:
        h = (60 * ((g-b)/df) + 360) % 360
    elif mx == g:
        h = (60 * ((b-r)/df) + 120) % 360
    elif mx == b:
        h = (60 * ((r-g)/df) + 240) % 360
    if mx == 0:
        s = 0
    else:
        s = df/mx
    v = mx
    return (h, s, v)

def hsv_to_rgb(h: float, s: float, v: float) -> tuple:
    """HSV转RGB颜色空间"""
    h = float(h)
    s = float(s)
    v = float(v)
    h60 = h / 60.0
    h60f = math.floor(h60)
    hi = int(h60f) % 6
    f = h60 - h60f
    p = v * (1 - s)
    q = v * (1 - f * s)
    t = v * (1 - (1 - f) * s)
    r, g, b = 0, 0, 0
    if hi == 0: r, g, b = v, t, p
    elif hi == 1: r, g, b = q, v, p
    elif hi == 2: r, g, b = p, v, t
    elif hi == 3: r, g, b = p, q, v
    elif hi == 4: r, g, b = t, p, v
    elif hi == 5: r, g, b = v, p, q
    r, g, b = int(r * 255), int(g * 255), int(b * 255)
    return (r, g, b)

def generate_image(text: str, output_path: str, title_image: str = None,
                  font_regular: str = None, font_bold: str = None, font_emoji: str = None, **kwargs):
    """生成图片的主函数"""
    # 使用传入的字体路径
    if not font_regular or not os.path.exists(font_regular):
        raise ValueError(f"Font file not found: {font_regular}")
    
    if not font_bold or not os.path.exists(font_bold):
        raise ValueError(f"Font file not found: {font_bold}")
    
    if not font_emoji or not os.path.exists(font_emoji):
        raise ValueError(f"Font file not found: {font_emoji}")
        
    try:
        # 提取样式参数 - 确保不与位置参数冲突
        if 'title_image' in kwargs:
            del kwargs['title_image']  # 避免与位置参数冲突
            
        width = kwargs.get('width', 720)
        is_dark = kwargs.get('is_dark', False)
        padding = kwargs.get('padding', 40)
        border_radius = kwargs.get('border_radius', 30)
        font_size_base = kwargs.get('font_size', 30)
        title_font_size = kwargs.get('title_font_size', 45)
        line_spacing_factor = kwargs.get('line_spacing', 1.5)
        
        # 添加科技感装饰选项
        add_tech_effect = kwargs.get('tech_effect', True)
        tech_theme = kwargs.get('tech_theme', 'modern')
        
        # 添加边框样式选项
        border_style = kwargs.get('border_style', 'simple')  # 默认简单边框
        border_accent_color = kwargs.get('border_accent_color', None)  # 边框辅助颜色
        inner_style = kwargs.get('inner_style', 'solid')  # 内边框样式
        
        # 使用传入的字体路径，而不是硬编码的路径
        font_paths = {
            'regular': font_regular,
            'bold': font_bold,
            'emoji': font_emoji
        }

        # 验证字体文件
        for font_type, path in font_paths.items():
            if not os.path.exists(path):
                raise FileNotFoundError(f"Font file not found: {path}")

        # 初始化组件
        font_manager = FontManager(font_paths)
        rect_width = width - (padding * 2)
        max_content_width = rect_width - (padding * 2)
        parser = MarkdownParser()
        renderer = TextRenderer(font_manager, max_content_width)
        
        # 更新字体大小和行间距
        default_style = TextStyle()
        default_style.font_size = font_size_base
        default_style.line_spacing = int(font_size_base * (line_spacing_factor - 1))
        
        # 更新标题字体大小
        for style_attr in dir(TextStyle):
            if style_attr.startswith('_'):
                continue
            if style_attr == 'font_size' and hasattr(default_style, style_attr):
                setattr(default_style, style_attr, font_size_base)
            if style_attr == 'is_title' and getattr(default_style, style_attr):
                default_style.font_size = title_font_size

        # 解析文本
        segments = parser.parse(text)
        for segment in segments:
            if segment.style.is_title:
                segment.style.font_size = title_font_size
            else:
                segment.style.font_size = font_size_base
            segment.style.line_spacing = int(segment.style.font_size * (line_spacing_factor - 1))
            
        processed_lines = []

        for segment in segments:
            available_width = max_content_width - segment.style.indent
            if segment.text.strip():
                lines = renderer.split_text_to_lines(segment, available_width)
                processed_lines.extend(lines)
            else:
                processed_lines.append(ProcessedLine(
                    text='',
                    style=segment.style,
                    height=0,
                    line_count=1
                ))

        # 计算高度
        title_height = 0
        if title_image:
            try:
                with Image.open(title_image) as img:
                    aspect_ratio = img.height / img.width
                    title_height = int((rect_width - 40) * aspect_ratio) + kwargs.get('title_spacing', 40)
            except Exception as e:
                print(f"Title image processing error: {e}")

        content_height = renderer.calculate_height(processed_lines)
        rect_height = content_height + title_height
        rect_x = (width - rect_width) // 2
        rect_y = padding
        total_height = rect_height + (padding * 2)

        # 创建背景
        use_gradient = kwargs.get('gradient', True)
        if use_gradient:
            background = create_gradient_background(width, total_height)
        else:
            # 纯色背景
            bg_color = kwargs.get('background_color', '#FFFFFF')
            background = Image.new('RGBA', (width, total_height), bg_color)
            
        # 确保背景是RGBA模式
        if background.mode != 'RGBA':
            background = background.convert('RGBA')
        draw = ImageDraw.Draw(background)

        # 设置颜色
        if is_dark:
            bg_color = kwargs.get('background_color', '#1E2C3C')
            text_color = kwargs.get('text_color', '#FFFFFF')
            border_color = kwargs.get('border_color', '#4DC4FF')
        else:
            bg_color = kwargs.get('background_color', '#FFFFFF')
            text_color = kwargs.get('text_color', '#333333')
            border_color = kwargs.get('border_color', '#E0E0E0')
        
        # 转换hex颜色到rgba
        def hex_to_rgba(hex_color, alpha=255):
            if hex_color.startswith('#'):
                hex_color = hex_color[1:]
            if len(hex_color) == 6:
                r, g, b = tuple(int(hex_color[i:i+2], 16) for i in (0, 2, 4))
                return (r, g, b, alpha)
            return (255, 255, 255, alpha)
            
        bg_rgba = hex_to_rgba(bg_color, 230)  # 半透明背景

        # 添加阴影
        if kwargs.get('shadow', True):
            shadow_offset = 8
            shadow_blur = 10
            shadow_opacity = 60
            shadow = Image.new('RGBA', background.size, (0, 0, 0, 0))
            shadow_draw = ImageDraw.Draw(shadow)
            shadow_draw.rounded_rectangle(
                [(rect_x + shadow_offset, rect_y + shadow_offset), 
                 (rect_x + rect_width + shadow_offset, rect_y + rect_height + shadow_offset)],
                border_radius, fill=(0, 0, 0, shadow_opacity)
            )
            shadow = shadow.filter(ImageFilter.GaussianBlur(shadow_blur))
            background = Image.alpha_composite(background, shadow)

        # 创建卡片背景
        card = Image.new('RGBA', background.size, (0, 0, 0, 0))
        card_draw = ImageDraw.Draw(card)
        card_draw.rounded_rectangle(
            [(rect_x, rect_y), (rect_x + rect_width, rect_y + rect_height)],
            border_radius, fill=bg_rgba
        )
        
        # 添加边框
        border_width = kwargs.get('border_width', 0)
        if border_width > 0:
            # 根据边框样式选择不同的边框生成方法
            if border_style in ['gradient', 'double', 'neon', 'stripe', 'glow']:
                # 使用高级边框效果
                border_color = kwargs.get('border_color', '#4DC4FF' if is_dark else '#EEEEEE')
                border_layer = create_gradient_border(
                    rect_width, rect_height, border_radius, 
                    border_color, border_accent_color,
                    border_width, border_style, inner_style
                )
                background = Image.alpha_composite(background, border_layer)
            else:
                # 使用传统边框
                card_draw.rounded_rectangle(
                    [(rect_x, rect_y), (rect_x + rect_width, rect_y + rect_height)],
                    border_radius, outline=hex_to_rgba(border_color), width=border_width
                )
                
        # 合并卡片到背景
        background = Image.alpha_composite(background, card)
        
        # 添加科技感装饰
        if add_tech_effect:
            # 解析主题颜色
            theme_rgb = None
            title_color_hex = kwargs.get('title_color', '#4DC4FF' if is_dark else '#1976D2')
            if title_color_hex.startswith('#') and len(title_color_hex) >= 7:
                r = int(title_color_hex[1:3], 16)
                g = int(title_color_hex[3:5], 16)
                b = int(title_color_hex[5:7], 16)
                theme_rgb = (r, g, b)
                
            background = add_tech_decorations(
                background, rect_x, rect_y, rect_width, rect_height, 
                is_dark=is_dark, theme_color=theme_rgb
            )
            
        draw = ImageDraw.Draw(background)

        # 绘制内容
        current_y = rect_y + padding
        if title_image:
            current_y = add_title_image(background, title_image, rect_x, rect_y, rect_width)

        # 获取标题颜色
        title_color = kwargs.get('title_color', '#4DC4FF' if is_dark else '#1976D2')

        # 逐字符绘制文本
        for i, line in enumerate(processed_lines):
            if not line.text.strip():
                if i < len(processed_lines) - 1 and any(l.text.strip() for l in processed_lines[i + 1:]):
                    current_y += line.style.line_spacing
                continue

            x = rect_x + padding + line.style.indent
            current_x = x

            # 决定文本颜色
            current_color = title_color if line.style.is_title else text_color

            # 逐字符渲染
            for char in line.text:
                if emoji.is_emoji(char):
                    try:
                        # emoji字体渲染 - 尝试使用30号大小的emoji
                        emoji_font = font_manager.fonts.get('emoji_30')
                        if emoji_font is None:
                            # 回退到常规字体
                            emoji_font = font_manager.get_font(line.style)
                            print(f"Warning: No emoji font available, using regular font for emoji: {char}")
                            
                        # 绘制emoji - 使用更大一点的尺寸
                        emoji_size = line.style.font_size + 4  # 稍微大一点
                        emoji_y_offset = -2  # 上移一点
                        
                        # 尝试获取指定大小的emoji字体
                        emoji_font_sized = font_manager.fonts.get(f'emoji_{emoji_size}')
                        if emoji_font_sized:
                            emoji_font = emoji_font_sized
                            
                        bbox = draw.textbbox((current_x, current_y + emoji_y_offset), char, font=emoji_font)
                        
                        # 使用embedded_color=True确保彩色emoji
                        draw.text(
                            (current_x, current_y + emoji_y_offset), 
                            char, 
                            font=emoji_font, 
                            embedded_color=True
                        )
                        
                        # 计算宽度并向前移动
                        char_width = bbox[2] - bbox[0]
                        current_x += char_width
                    except Exception as e:
                        print(f"Error rendering emoji '{char}': {e}")
                        # 出错时回退到常规字体渲染
                        font = font_manager.get_font(line.style)
                        bbox = draw.textbbox((current_x, current_y), char, font=font)
                        draw.text((current_x, current_y), char, font=font, fill=current_color)
                        current_x += bbox[2] - bbox[0]
                else:
                    # 普通文字渲染
                    font = font_manager.get_font(line.style)
                    bbox = draw.textbbox((current_x, current_y), char, font=font)
                    draw.text((current_x, current_y), char, font=font, fill=current_color)
                    current_x += bbox[2] - bbox[0]

            if i < len(processed_lines) - 1:
                current_y += line.height + line.style.line_spacing
            else:
                current_y += line.height

        # 直接保存为PNG，保持RGBA模式
        background.save(output_path, "PNG", optimize=False, compress_level=0)

    except Exception as e:
        print(f"Error generating image: {e}")
        raise


class CardGenerator:
    """卡片生成器类"""
    def __init__(self, fonts_dir: str = "fonts"):
        self.fonts_dir = fonts_dir
        # 字体配置
        self.font_paths = {
            'regular': os.path.join(fonts_dir, "SourceHanSansCN-Regular.otf"),
            'bold': os.path.join(fonts_dir, "SourceHanSansCN-Bold.otf"),
            'emoji': os.path.join(fonts_dir, "NotoColorEmoji.ttf")  # 默认从字体目录加载
        }
        
        # 尝试查找系统emoji字体
        windows_emoji_path = r"C:\Windows\Fonts\seguiemj.ttf"
        if os.path.exists(windows_emoji_path):
            # 如果Windows系统emoji字体存在，优先使用
            self.font_paths['emoji'] = windows_emoji_path
            print(f"使用Windows系统emoji字体: {windows_emoji_path}")
        elif not os.path.exists(self.font_paths['emoji']):
            # 如果指定的emoji字体不存在，尝试使用思源黑体作为后备
            print(f"警告: Emoji字体文件不存在: {self.font_paths['emoji']}")
            print(f"使用思源黑体作为Emoji后备字体")
            self.font_paths['emoji'] = self.font_paths['regular']

    def generate_card(self, text: str, width: int = 720, is_dark: bool = False, 
                     title_image: Optional[str] = None, 
                     background_color: Optional[str] = None,
                     text_color: Optional[str] = None,
                     title_color: Optional[str] = None,
                     border_color: Optional[str] = None,
                     border_width: Optional[int] = None,
                     border_radius: Optional[int] = None,
                     padding: Optional[int] = None,
                     font_size: Optional[int] = None,
                     title_font_size: Optional[int] = None,
                     line_spacing: Optional[float] = None,
                     shadow: Optional[bool] = None,
                     gradient: Optional[bool] = None,
                     title_spacing: Optional[int] = None,
                     tech_effect: Optional[bool] = None,
                     tech_theme: Optional[str] = None,
                     border_style: Optional[str] = None,
                     border_accent_color: Optional[str] = None,
                     inner_style: Optional[str] = None) -> Image.Image:
        """生成卡片图像

        Args:
            text: 要渲染的文本内容
            width: 卡片宽度（默认720像素）
            is_dark: 是否使用深色主题
            title_image: 可选的标题图片路径
            background_color: 背景颜色
            text_color: 文本颜色
            title_color: 标题颜色
            border_color: 边框颜色
            border_width: 边框宽度
            border_radius: 边框圆角半径
            padding: 内边距
            font_size: 文本字体大小
            title_font_size: 标题字体大小
            line_spacing: 行间距
            shadow: 是否添加阴影
            gradient: 是否使用渐变背景
            title_spacing: 标题与内容间距
            tech_effect: 是否添加科技感装饰效果
            tech_theme: 科技主题风格，可选 'modern', 'matrix', 'cyberpunk', 'hologram'
            border_style: 边框风格，可选 'simple', 'gradient', 'double', 'neon', 'stripe', 'glow'
            border_accent_color: 边框辅助颜色，用于渐变、双层等效果
            inner_style: 内边框样式，可选 'solid', 'dotted', 'dashed', 'wavy'，仅当border_style为'double'时有效

        Returns:
            PIL.Image: 生成的卡片图像
        """
        # 创建临时输出路径
        temp_output = f"temp_card_{random.randint(1000, 9999)}.png"
        
        # 构建额外参数
        extra_params = {
            "width": width,
            "is_dark": is_dark
        }
        
        # 添加其他可选参数
        if background_color is not None:
            extra_params["background_color"] = background_color
        if text_color is not None:
            extra_params["text_color"] = text_color
        if title_color is not None:
            extra_params["title_color"] = title_color
        if border_color is not None:
            extra_params["border_color"] = border_color
        if border_width is not None:
            extra_params["border_width"] = border_width
        if border_radius is not None:
            extra_params["border_radius"] = border_radius
        if padding is not None:
            extra_params["padding"] = padding
        if font_size is not None:
            extra_params["font_size"] = font_size
        if title_font_size is not None:
            extra_params["title_font_size"] = title_font_size
        if line_spacing is not None:
            extra_params["line_spacing"] = line_spacing
        if shadow is not None:
            extra_params["shadow"] = shadow
        if gradient is not None:
            extra_params["gradient"] = gradient
        if title_spacing is not None:
            extra_params["title_spacing"] = title_spacing
        if tech_effect is not None:
            extra_params["tech_effect"] = tech_effect
        if tech_theme is not None:
            extra_params["tech_theme"] = tech_theme
        if border_style is not None:
            extra_params["border_style"] = border_style
        if border_accent_color is not None:
            extra_params["border_accent_color"] = border_accent_color
        if inner_style is not None:
            extra_params["inner_style"] = inner_style
            
        try:
            # 调用现有的 generate_image 函数
            generate_image(
                text, 
                temp_output, 
                title_image,
                font_regular=self.font_paths['regular'],
                font_bold=self.font_paths['bold'],
                font_emoji=self.font_paths['emoji'],
                **extra_params
            )
            
            # 读取生成的图片
            with Image.open(temp_output) as img:
                return img.copy()
        finally:
            # 清理临时文件
            if os.path.exists(temp_output):
                os.remove(temp_output)