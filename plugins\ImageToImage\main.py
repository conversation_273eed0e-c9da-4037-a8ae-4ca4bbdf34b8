import os,json,asyncio,time,hashlib,uuid,hmac,random,urllib.parse,base64,xml.etree.ElementTree as ET,zlib
from datetime import datetime
from pathlib import Path
try:import tomllib
except:import tomli as tomllib
import httpx
from loguru import logger
from WechatAPI import WechatAPIClient
from utils.decorators import on_text_message,on_quote_message
from utils.plugin_base import PluginBase

class ImageToImage(PluginBase):
    description,author,version,plugin_name="AI图生图功能，支持图像编辑、换装、风格转换等","XYBot","1.1.0","ImageToImage"
    def __init__(self):
        super().__init__()
        self.temp_dir=Path("temp/image_to_image")
        self.temp_dir.mkdir(parents=True,exist_ok=True)
        self._load_config()
        self.user_last_request,self.user_request_count,self.user_sessions={},{},{}
        self.quote_command=["即梦"]
        self.device_info={"device_id":"754744470018299","iid":"1690381665447612","version_code":"1541600","version_name":"1.5.4","device_type":"ALN-AL10","device_brand":"HUAWEI","os_version":"10","resolution":"1080*2232","dpi":"480"}
        self.aspect_ratios={"1:1":{"width":1024,"height":1024,"resolution_type":"1k"},"3:4":{"width":936,"height":1248,"resolution_type":"1k"},"4:3":{"width":1248,"height":936,"resolution_type":"1k"},"9:16":{"width":936,"height":1664,"resolution_type":"1k"},"16:9":{"width":1664,"height":936,"resolution_type":"1k"},"2:3":{"width":832,"height":1248,"resolution_type":"1k"},"3:2":{"width":1248,"height":832,"resolution_type":"1k"}}

    def _load_config(self):
        try:config=tomllib.load(open(f"plugins/{self.plugin_name}/config.toml","rb")).get(self.plugin_name,{}) if os.path.exists(f"plugins/{self.plugin_name}/config.toml") else {}
        except:config={}
        self.enable,self.command,self.command_format=config.get("enable",True),config.get("command",["图生图"]),config.get("command-format","图生图 [提示词] [图片路径]")
        quote_config=config.get("quote",{})
        self.quote_command,self.quote_command_format=quote_config.get("command",["即梦"]),quote_config.get("command-format","引用图片并发送: 即梦+提示词")
        api_config=config.get("api",{})
        self.base_url,self.upload_url=api_config.get("base_url","https://dreamina-app-lq.jianying.com"),api_config.get("upload_url","https://imagex.bytedanceapi.com")
        self.cooldown=config.get("rate_limit",{}).get("cooldown",30)
        self.natural_response=config.get("natural_response",True)
        self._init_natural_responses()

    def _init_natural_responses(self):
        self.confirm_msgs,self.error_msgs,self.rate_limit_msgs=["好的","收到","明白了","嗯","知道了","行","OK"],["这可把我难住了","搞不定了","这个有点难","弄不了","出问题了","搞不出来","这个不行"],["你搁这刷bug呢","慢点慢点","别这么急","一个一个来","太快了太快了","歇会儿","别刷了"]

    def _parse_prompt_and_ratio(self,prompt_text):
        default_ratio=self.aspect_ratios["3:4"]
        prompt_text=prompt_text.strip()
        if "--ratio" in prompt_text:
            parts=prompt_text.split("--ratio")
            if len(parts)==2:
                clean_prompt,ratio_part=parts[0].strip(),parts[1].strip()
                return clean_prompt,self.aspect_ratios.get(ratio_part,default_ratio)
        words=prompt_text.split()
        if len(words)>1 and words[-1] in self.aspect_ratios:
            return " ".join(words[:-1]),self.aspect_ratios[words[-1]]
        return prompt_text,default_ratio

    async def _simple_confirm(self,bot,wxid):
        if self.natural_response:await bot.send_text_message(wxid,random.choice(self.confirm_msgs))

    @on_text_message
    async def handle_text(self,bot,message):
        if not self.enable:return
        content,wxid,user_wxid=str(message["Content"]).strip(),message["FromWxid"],message["SenderWxid"]
        if content in ["图生图帮助","图生图说明","图生图指令"]:
            await self._send_usage_instructions(bot,wxid,user_wxid)
            return
        command_parts=content.split(" ",2)
        if command_parts[0] in self.command:
            if len(command_parts)<3:
                await bot.send_at_message(wxid,f"❌ 命令格式错误，正确格式: {self.command_format}",[user_wxid])
                await self._send_usage_instructions(bot,wxid,user_wxid)
                return
            if self._check_rate_limit(user_wxid):
                await bot.send_text_message(wxid,random.choice(self.rate_limit_msgs)) if self.natural_response else await bot.send_at_message(wxid,"⏳ 请求太频繁，请稍后再试",[user_wxid])
                return
            raw_prompt,image_path=command_parts[1].strip(),command_parts[2].strip()
            prompt,ratio_config=self._parse_prompt_and_ratio(raw_prompt)
            if not os.path.exists(image_path):
                await bot.send_at_message(wxid,f"❌ 图片不存在: {image_path}",[user_wxid])
                await self._send_usage_instructions(bot,wxid,user_wxid)
                return
            try:await self._test_upload_flow(bot,wxid,user_wxid,prompt,image_path,ratio_config)
            except Exception as e:await bot.send_at_message(wxid,f"❌ 处理失败: {str(e)}",[user_wxid])

    @on_quote_message
    async def handle_quote(self,bot,message):
        if not self.enable:return
        content,wxid,user_wxid=str(message.get("Content","")).strip(),message.get("FromWxid",""),message.get("SenderWxid","")
        command_parts=content.split(maxsplit=1)
        if not command_parts or command_parts[0] not in self.quote_command:return
        if self._check_rate_limit(user_wxid):
            await bot.send_text_message(wxid,random.choice(self.rate_limit_msgs)) if self.natural_response else await bot.send_at_message(wxid,"请求太频繁，等会再试试",[user_wxid])
            return
        raw_prompt="风格转换"
        if len(command_parts)>1:
            full_command=command_parts[1].strip()
            raw_prompt=full_command[1:].strip() if full_command.startswith("+") else full_command
        prompt,ratio_config=self._parse_prompt_and_ratio(raw_prompt)


        quote_info=message.get("Quote",{})
        quoted_msg_id=quote_info.get("Msgid") or quote_info.get("NewMsgId")
        quoted_sender=quote_info.get("SenderWxid") or quote_info.get("FromWxid")
        bot_wxid=getattr(bot,'wxid',None)
        if quote_info.get("MsgType")!=3:
            await bot.send_at_message(wxid,"❌ 请引用图片消息",[user_wxid])
            return
        await self._simple_confirm(bot,wxid)
        if quoted_sender==bot_wxid:
            try:
                from plugins.RevokePlugin.main import RevokePlugin
                if RevokePlugin._instance:
                    msg_info=RevokePlugin._instance.get_message_by_id(quoted_msg_id)
                    if msg_info and 'local_image_path' in msg_info and os.path.exists(msg_info['local_image_path']):
                        await self._test_upload_flow_without_notification(bot,wxid,user_wxid,prompt,msg_info['local_image_path'],ratio_config)
                        return
            except:pass
            await bot.send_at_message(wxid,"❌ 暂不支持处理机器人发送的图片\n请引用其他用户发送的图片",[user_wxid])
            return

        try:
            quote_content=quote_info.get("Content","")
            if not quote_content:
                await bot.send_text_message(wxid,random.choice(self.error_msgs)) if self.natural_response else await bot.send_at_message(wxid,"❌ 无法获取引用的图片内容",[user_wxid])
                return
            try:
                root=ET.fromstring(quote_content)
                img_node=root.find('.//img')
                if img_node is None:
                    refermsg=root.find('.//refermsg')
                    if refermsg is not None and refermsg.find('content') is not None:
                        content_text=refermsg.find('content').text
                        if content_text:
                            content_text=content_text.replace('&lt;','<').replace('&gt;','>')
                            try:img_node=ET.fromstring(content_text).find('img')
                            except:pass
                if img_node is None:
                    if "aeskey=" in quote_content and "cdnmidimgurl=" in quote_content:
                        aeskey=quote_content[quote_content.find('aeskey="')+8:quote_content.find('"',quote_content.find('aeskey="')+8)]
                        cdnmidimgurl=quote_content[quote_content.find('cdnmidimgurl="')+14:quote_content.find('"',quote_content.find('cdnmidimgurl="')+14)]
                    elif "cdnthumbaeskey=" in quote_content and "cdnthumburl=" in quote_content:
                        aeskey=quote_content[quote_content.find('cdnthumbaeskey="')+16:quote_content.find('"',quote_content.find('cdnthumbaeskey="')+16)]
                        cdnmidimgurl=quote_content[quote_content.find('cdnthumburl="')+13:quote_content.find('"',quote_content.find('cdnthumburl="')+13)]
                    else:
                        await bot.send_text_message(wxid,random.choice(self.error_msgs)) if self.natural_response else await bot.send_at_message(wxid,"❌ 无法从引用消息中提取图片信息",[user_wxid])
                        return
                else:
                    aeskey,cdnmidimgurl=img_node.get('aeskey'),img_node.get('cdnmidimgurl')
                    if not aeskey or not cdnmidimgurl:
                        aeskey,cdnmidimgurl=img_node.get('cdnthumbaeskey'),img_node.get('cdnthumburl')
                if not aeskey or not cdnmidimgurl:
                    await bot.send_text_message(wxid,random.choice(self.error_msgs)) if self.natural_response else await bot.send_at_message(wxid,"❌ 无法提取图片下载参数",[user_wxid])
                    return
                image_base64=await bot.download_image(aeskey,cdnmidimgurl)
                if not image_base64:
                    await bot.send_text_message(wxid,random.choice(self.error_msgs)) if self.natural_response else await bot.send_at_message(wxid,"❌ 下载图片失败",[user_wxid])
                    return
                temp_file=self.temp_dir/f"quoted_image_{int(time.time())}.jpg"
                with open(temp_file,"wb") as f:f.write(base64.b64decode(image_base64))
                await self._test_upload_flow_without_notification(bot,wxid,user_wxid,prompt,str(temp_file),ratio_config)
            except:await bot.send_at_message(wxid,"❌ 解析图片信息失败",[user_wxid])
        except Exception as e:await bot.send_at_message(wxid,f"❌ 处理过程中出现错误: {str(e)}",[user_wxid])

    async def _test_upload_flow_without_notification(self,bot,wxid,user_wxid,prompt,image_path,ratio_config=None):
        try:
            ratio_config=ratio_config or self.aspect_ratios["3:4"]
            upload_token=await self._get_upload_token()
            if not upload_token:raise Exception("获取上传Token失败")
            upload_info=await self._apply_image_upload(upload_token) or await self._apply_image_upload_simple(upload_token)
            if not upload_info:
                await self._test_upload_flow_alternative_without_notification(bot,wxid,user_wxid,prompt,image_path)
                return
            image_uri=await self._upload_image(image_path,upload_info)
            if not image_uri:raise Exception("图片上传失败")
            await self._commit_image_upload(upload_token,upload_info.get("SessionKey",""))
            task_id=await self._submit_generation_task(prompt,image_uri,ratio_config)
            if not task_id:
                await bot.send_at_message(wxid,"任务提交不了，等会再试试",[user_wxid])
                return
            results=await self._wait_for_results(task_id)
            if results:await self._send_results(bot,wxid,user_wxid,results,prompt)
            else:await bot.send_at_message(wxid,"处理不了，等会再试试吧",[user_wxid])
        except Exception as e:await bot.send_at_message(wxid,f"❌ 处理失败: {str(e)}",[user_wxid])

    async def _test_upload_flow_alternative_without_notification(self,bot,wxid,user_wxid,prompt,image_path):
        try:
            with open(image_path,"rb") as f:image_data=f.read()
            try:
                from PIL import Image
                with Image.open(image_path) as img:width,height,format_name=img.size[0],img.size[1],img.format
            except:width=height=format_name="未知"
            await bot.send_at_message(wxid,f"❌ 上传服务暂时不可用，图片分析结果:\n📊 图片信息: {width}x{height} {format_name}\n📁 文件大小: {len(image_data)/1024:.1f} KB\n💡 提示词: {prompt}\n等会再试试或找管理员",[user_wxid])
        except Exception as e:await bot.send_at_message(wxid,f"❌ 图片分析失败: {str(e)}",[user_wxid])

    async def _test_upload_flow(self,bot,wxid,user_wxid,prompt,image_path,ratio_config=None):
        try:
            ratio_config=ratio_config or self.aspect_ratios["3:4"]
            await self._simple_confirm(bot,wxid)
            upload_token=await self._get_upload_token()
            if not upload_token:raise Exception("获取上传Token失败")
            upload_info=await self._apply_image_upload(upload_token) or await self._apply_image_upload_simple(upload_token)
            if not upload_info:
                await self._test_upload_flow_alternative_without_notification(bot,wxid,user_wxid,prompt,image_path)
                return
            image_uri=await self._upload_image(image_path,upload_info)
            if not image_uri:raise Exception("图片上传失败")
            await self._commit_image_upload(upload_token,upload_info.get("SessionKey",""))
            task_id=await self._submit_generation_task(prompt,image_uri,ratio_config)
            if not task_id:
                await bot.send_at_message(wxid,"任务提交不了，等会再试试",[user_wxid])
                return
            results=await self._wait_for_results(task_id)
            if results:await self._send_results(bot,wxid,user_wxid,results,prompt)
            else:await bot.send_at_message(wxid,"处理不了，等会再试试吧",[user_wxid])
        except Exception as e:await bot.send_at_message(wxid,f"❌ 处理失败: {str(e)}",[user_wxid])

    async def _send_results(self,bot,wxid,user_wxid,results,prompt):
        try:
            success_count=0
            for i,image_info in enumerate(results):
                image_url=image_info.get("url")
                if not image_url:continue
                try:
                    async with httpx.AsyncClient(timeout=30) as client:
                        response=await client.get(image_url)
                        response.raise_for_status()
                        image_data=response.content
                        if len(image_data)<1024:raise ValueError(f"下载的图片数据过小: {len(image_data)}字节")
                        result=await bot.send_image_message(wxid,image_data)
                        if result and result[2]!=0:success_count+=1
                        else:
                            temp_file=self.temp_dir/f"result_{int(time.time())}_{i}.jpg"
                            with open(temp_file,"wb") as f:f.write(image_data)
                            file_result=await bot.send_file_message(wxid,str(temp_file))
                            if file_result and file_result[2]!=0:success_count+=1
                        await asyncio.sleep(1.5)
                except:pass
            if success_count==0:await bot.send_at_message(wxid,"图片都发不出去，网络有问题",[user_wxid])
        except Exception as e:await bot.send_at_message(wxid,f"❌ 发送结果失败: {str(e)}",[user_wxid])

    async def download_image(self,bot,aeskey,cdnmidimgurl):
        if not aeskey or not cdnmidimgurl:return None
        return await bot.download_image(aeskey,cdnmidimgurl)

    async def download_and_save_image(self,bot,aeskey,cdnurl,msg_id=None,is_thumb=False):
        try:
            if is_thumb:
                json_param={"Wxid":bot.wxid,"AesKey":aeskey,"Cdnthumburl":cdnurl}
                api_url=f'http://{bot.ip}:{bot.port}/CdnDownloadThumbImg'
                async with httpx.AsyncClient() as client:
                    response=await client.post(api_url,json=json_param)
                    if response.status_code!=200:return None,None
                    json_resp=response.json()
                    if not json_resp.get("Success"):return None,None
                    image_base64=json_resp.get("Data")
                    if not image_base64:return None,None
            else:
                image_base64=await bot.download_image(aeskey,cdnurl)
                if not image_base64:return None,None
            os.makedirs(self.temp_dir,exist_ok=True)
            msg_id=msg_id or str(int(time.time()))
            file_path=os.path.join(self.temp_dir,f"{msg_id}.jpg")
            try:
                with open(file_path,"wb") as f:f.write(base64.b64decode(image_base64))
                return image_base64,file_path
            except:return image_base64,None
        except:return None,None

    def _generate_aws4_signature(self, access_key_id: str, secret_access_key: str,
                                session_token: str, method: str, url: str,
                                params: dict, headers: dict, payload: str = "") -> dict:
        """生成AWS4签名"""
        try:
            # 解析URL
            from urllib.parse import urlparse, quote
            parsed_url = urlparse(url)
            host = parsed_url.netloc
            path = parsed_url.path or "/"

            # 时间戳
            t = datetime.utcnow()
            amz_date = t.strftime('%Y%m%dT%H%M%SZ')
            datestamp = t.strftime('%Y%m%d')

            # 必需的headers
            canonical_headers_dict = {
                'host': host,
                'x-amz-date': amz_date,
            }

            if session_token:
                canonical_headers_dict['x-amz-security-token'] = session_token.strip()

            # 添加其他headers
            for k, v in headers.items():
                if k.lower() not in canonical_headers_dict:
                    canonical_headers_dict[k.lower()] = str(v).strip()

            # 规范化查询字符串
            canonical_querystring = '&'.join([
                f"{quote(str(k), safe='')}={quote(str(v), safe='')}"
                for k, v in sorted(params.items())
            ])

            # 规范化headers
            signed_headers = ';'.join(sorted(canonical_headers_dict.keys()))
            canonical_headers = '\n'.join([
                f"{k}:{canonical_headers_dict[k]}"
                for k in sorted(canonical_headers_dict.keys())
            ]) + '\n'

            # Payload hash
            payload_hash = hashlib.sha256(payload.encode('utf-8')).hexdigest()

            # 规范化请求
            canonical_request = '\n'.join([
                method.upper(),
                path,
                canonical_querystring,
                canonical_headers,
                signed_headers,
                payload_hash
            ])

            # 要签名的字符串
            algorithm = 'AWS4-HMAC-SHA256'
            credential_scope = f"{datestamp}/sdwdmwlll/imagex/aws4_request"
            string_to_sign = '\n'.join([
                algorithm,
                amz_date,
                credential_scope,
                hashlib.sha256(canonical_request.encode('utf-8')).hexdigest()
            ])

            # 计算签名
            def sign(key, msg):
                return hmac.new(key, msg.encode('utf-8'), hashlib.sha256).digest()

            def get_signature_key(key, date_stamp, region_name, service_name):
                k_date = sign(('AWS4' + key).encode('utf-8'), date_stamp)
                k_region = sign(k_date, region_name)
                k_service = sign(k_region, service_name)
                k_signing = sign(k_service, 'aws4_request')
                return k_signing

            signing_key = get_signature_key(secret_access_key, datestamp, 'sdwdmwlll', 'imagex')
            signature = hmac.new(signing_key, string_to_sign.encode('utf-8'), hashlib.sha256).hexdigest()

            # 构建Authorization头
            authorization_header = (
                f"{algorithm} "
                f"Credential={access_key_id}/{credential_scope}, "
                f"SignedHeaders={signed_headers}, "
                f"Signature={signature}"
            )

            # 构建最终headers
            final_headers = headers.copy()
            final_headers.update({
                'Host': host,
                'X-Amz-Date': amz_date,
                'Authorization': authorization_header
            })

            if session_token:
                final_headers['X-Amz-Security-Token'] = session_token.strip()

            return final_headers

        except Exception as e:
            logger.error(f"[{self.plugin_name}] AWS4签名生成失败: {str(e)}")
            return headers

    async def _get_upload_token(self):
        try:
            headers=self._get_common_headers()
            headers["Content-Type"]="application/json; charset=utf-8"
            async with httpx.AsyncClient(timeout=30) as client:
                response=await client.post(f"{self.base_url}/mweb/v1/get_upload_token",params=self._get_common_params(),headers=headers,json={"scene":2})
                result=response.json()
                response.raise_for_status()
                return result.get("data") if result.get("ret")=="0" else None
        except:return None

    async def _apply_image_upload(self,token_data):
        try:
            url,params=f"{self.upload_url}/top/v1",{"Action":"ApplyImageUpload","FileType":"image","ServiceId":token_data["space_name"],"UploadNum":"1","Version":"2018-08-01","device_platform":"android"}
            headers=self._generate_aws4_signature(token_data["access_key_id"],token_data["secret_access_key"],token_data["session_token"],"GET",url,params,{"User-Agent":"BDFileUpload(1748031167853)","Content-Type":"application/json"})
            async with httpx.AsyncClient(timeout=30) as client:
                result=(await client.get(url,params=params,headers=headers)).json()
                return result.get("Result",{}).get("UploadAddress") if result.get("ResponseMetadata",{}).get("Action")=="ApplyImageUpload" and not result.get("ResponseMetadata",{}).get("Error") else None
        except:return None

    async def _apply_image_upload_simple(self,token_data):
        try:
            url,params=f"{self.upload_url}/top/v1",{"Action":"ApplyImageUpload","FileType":"image","ServiceId":token_data["space_name"],"UploadNum":"1","Version":"2018-08-01","device_platform":"android"}
            now=datetime.utcnow()
            headers={"Host":"imagex.bytedanceapi.com","User-Agent":"BDFileUpload(1748031167853)","X-Amz-Date":now.strftime('%Y%m%dT%H%M%SZ'),"X-Amz-Security-Token":token_data["session_token"],"Content-Type":"application/json","Connection":"keep-alive","Authorization":f"AWS4-HMAC-SHA256 Credential={token_data['access_key_id']}/{now.strftime('%Y%m%d')}/sdwdmwlll/imagex/aws4_request, SignedHeaders=host;x-amz-date;x-amz-security-token, Signature=example_signature"}
            async with httpx.AsyncClient(timeout=30) as client:return (await client.get(url,params=params,headers=headers)).json()
        except:return None

    async def _upload_image(self,image_path,upload_info):
        try:
            if not (isinstance(upload_info,dict) and "StoreInfos" in upload_info):return None
            store_info,upload_host=upload_info["StoreInfos"][0],upload_info["UploadHosts"][0]
            with open(image_path,"rb") as f:image_data=f.read()
            crc32_hex=f"{zlib.crc32(image_data)&0xffffffff:08x}"
            headers={"Authorization":store_info["Auth"],"Content-Type":"application/octet-stream","User-Agent":"BDFileUpload(1748031169029)","X-Upload-Content-CRC32":crc32_hex,"Content-Length":str(len(image_data))}
            async with httpx.AsyncClient(timeout=60) as client:
                result=(await client.post(f"https://{upload_host}/upload/v1/{store_info['StoreUri']}",headers=headers,content=image_data)).json()
                return store_info["StoreUri"] if result.get("code")==2000 else None
        except:return None


    async def _commit_image_upload(self,token_data,session_key):
        try:
            if not session_key:return False
            url,params=f"{self.upload_url}/top/v1",{"Action":"CommitImageUpload","ServiceId":token_data["space_name"],"Version":"2018-08-01","device_platform":"android"}
            payload=json.dumps({"Functions":None,"SessionKey":session_key})
            headers=self._generate_aws4_signature(token_data["access_key_id"],token_data["secret_access_key"],token_data["session_token"],"POST",url,params,{"Content-Type":"application/json","User-Agent":"BDFileUpload(1748031170456)"},payload)
            async with httpx.AsyncClient(timeout=30) as client:
                result=(await client.post(url,params=params,headers=headers,content=payload)).json()
                return result.get("ResponseMetadata",{}).get("Action")=="CommitImageUpload" and not result.get("ResponseMetadata",{}).get("Error")
        except:return False

    async def _check_user_benefits_enhanced(self) -> bool:
        """增强版权益检查 - 已弃用"""
        return True

    def _build_task_data(self, prompt: str, image_uri: str, ratio_config: dict = None) -> dict:
        """构建AI任务数据 - 支持3.0模型和比例配置"""
        try:
            # 使用默认比例配置
            if ratio_config is None:
                ratio_config = self.aspect_ratios["3:4"]

            # 生成任务ID和提交ID
            submit_id = f"581595_{str(uuid.uuid4())}"
            draft_id = str(uuid.uuid4())
            component_id = str(uuid.uuid4())

            # 生成各种ID
            metadata_id = str(uuid.uuid4())
            abilities_id = str(uuid.uuid4())
            blend_id = str(uuid.uuid4())
            core_param_id = str(uuid.uuid4())
            large_image_info_id = str(uuid.uuid4())
            ability_item_id = str(uuid.uuid4())
            image_item_id = str(uuid.uuid4())
            prompt_placeholder_id = str(uuid.uuid4())
            postedit_param_id = str(uuid.uuid4())

            # 当前时间戳
            current_time_ms = str(int(time.time() * 1000))

            # 构建draft content - 升级到3.2.5版本
            draft_content = {
                "type": "draft",
                "id": draft_id,
                "min_version": "3.2.5",
                "min_features": [],
                "is_from_tsn": True,
                "version": "3.2.5",
                "main_component_id": component_id,
                "component_list": [
                    {
                        "type": "image_base_component",
                        "id": component_id,
                        "min_version": "3.0.2",
                        "gen_type": 12,
                        "metadata": {
                            "type": "",
                            "id": metadata_id,
                            "created_platform": 2,
                            "created_platform_version": self.device_info["version_code"],
                            "created_time_in_ms": current_time_ms,
                            "created_did": self.device_info["device_id"]
                        },
                        "generate_type": "blend",
                        "aigc_mode": "workbench",
                        "abilities": {
                            "type": "",
                            "id": abilities_id,
                            "blend": {
                                "type": "",
                                "id": blend_id,
                                "min_version": "3.2.5",
                                "min_features": [],
                                "core_param": {
                                    "type": "",
                                    "id": core_param_id,
                                    "model": "high_aes_general_v30l:general_v3.0_18b",  # 升级到3.0模型
                                    "prompt": f"##{prompt}",
                                    "sample_strength": 0.3,
                                    "image_ratio": 5,
                                    "large_image_info": {
                                        "type": "",
                                        "id": large_image_info_id,
                                        "height": ratio_config["height"],
                                        "width": ratio_config["width"],
                                        "format": "jpg",
                                        "resolution_type": ratio_config["resolution_type"]
                                    }
                                },
                                "ability_list": [
                                    {
                                        "type": "",
                                        "id": ability_item_id,
                                        "name": "byte_edit",
                                        "image_uri_list": [image_uri],
                                        "image_list": [
                                            {
                                                "type": "image",
                                                "id": image_item_id,
                                                "source_from": "upload",
                                                "platform_type": 1,
                                                "name": "",
                                                "image_uri": image_uri,
                                                "width": 0,
                                                "height": 0,
                                                "format": "",
                                                "uri": image_uri
                                            }
                                        ],
                                        "strength": 0.5
                                    }
                                ],
                                "prompt_placeholder_info_list": [
                                    {
                                        "type": "",
                                        "id": prompt_placeholder_id,
                                        "ability_index": 0
                                    }
                                ],
                                "postedit_param": {
                                    "type": "",
                                    "id": postedit_param_id,
                                    "generate_type": 12
                                }
                            }
                        }
                    }
                ]
            }

            return {
                "draft_content": json.dumps(draft_content),
                "submit_id": submit_id,
                "metrics_extra": json.dumps({"is_regenerate": False}),
                "extend": {
                    "root_model": "high_aes_general_v30l:general_v3.0_18b"  # 升级到3.0模型
                }
            }
        except Exception as e:
            logger.error(f"构建任务数据异常: {str(e)}")
            return {}

    async def _submit_generation_task(self,prompt,image_uri,ratio_config=None):
        """提交AI生成任务 - 支持比例配置"""
        try:
            max_retries = 3
            retry_delay = 1  # 秒

            for attempt in range(max_retries):
                try:
                    url = f"{self.base_url}/mweb/v1/aigc_draft/generate"

                    # 构建请求参数 - 更新到3.2.5版本
                    params = {
                        **self._get_common_params(),
                        "babi_param": urllib.parse.quote(json.dumps({
                            "scenario": "image_video_generation",
                            "feature_key": "image_to_image",
                            "feature_entrance": "to_image",
                            "feature_entrance_detail": "to_image-image_to_image"
                        })),
                        "iid": self.device_info["iid"],
                        "device_id": self.device_info["device_id"],
                        "ac": "wifi",
                        "channel": "oppo_64_581595",
                        "aid": "581595",
                        "app_name": "dreamina",
                        "ssmix": "a",
                        "language": "zh",
                        "os_api": "29",
                        "manifest_version_code": self.device_info["version_code"],
                        "update_version_code": self.device_info["version_code"],
                        "_rticket": str(int(time.time() * 1000)),
                        "cdid": str(uuid.uuid4()),
                        "region": "cn",
                        "aigc_flow_version": "3.2.5",  # 升级版本
                        "aigc_flow_support_features": "AIGC_BlendAbility_twoFace,AIGC_GenerateType_AI_Effect,AIGC_GenerateType_byteEditPainting,AIGC_GenerateType_byteEditPainting_v2,AIGC_BlendAbility_multiStyleReference,AIGC_BlendAbility_styleCode,AIGC_LipSync_PostEdit,AIGC_BlendAbility_mixIpKeepByteEdit"
                    }

                    headers = self._get_enhanced_headers()
                    task_data = self._build_task_data(prompt, image_uri, ratio_config)

                    if not task_data:
                        logger.error(f"[{self.plugin_name}] 构建任务数据失败")
                        return None

                    async with httpx.AsyncClient(timeout=60) as client:
                        response = await client.post(url, params=params, headers=headers, json=task_data)
                        result = response.json()

                        if result.get("ret") == "0":
                            data = result.get("data", {}).get("aigc_data", {})
                            return data.get("history_record_id")
                        elif result.get("ret") in ["1000", "1014"]:  # invalid parameter or system busy
                            if attempt < max_retries - 1:
                                logger.warning(f"[{self.plugin_name}] 提交任务失败，等待重试: {result}")
                                await asyncio.sleep(retry_delay)
                                continue
                            else:
                                logger.error(f"提交任务失败: {result}")
                                return None
                        else:
                            logger.error(f"提交任务失败: {result}")
                            return None

                except Exception as e:
                    logger.error(f"提交任务请求异常: {str(e)}")
                    if attempt < max_retries - 1:
                        await asyncio.sleep(retry_delay)
                        continue
                    return None

            return None

        except Exception as e:
            logger.error(f"提交任务异常: {str(e)}")
            return None

    def _get_enhanced_headers(self) -> dict:
        """获取增强的请求头，包含设备签名"""
        try:
            current_time = int(time.time())
            device_id = self.device_info["device_id"]

            # 基础headers
            base_headers = self._get_common_headers()

            # 生成各种签名
            device_sign = self._generate_device_sign()
            gorgon = self._generate_gorgon_v2(current_time, device_id)
            argus = self._generate_argus_v2(current_time, device_id)
            ladon = self._generate_ladon_v2(current_time, device_id)

            # 构建增强headers
            enhanced_headers = {
                **base_headers,
                "Content-Type": "application/json; charset=utf-8",
                "Cookie": self._generate_cookie(),
                "lan": "zh-hans",
                "loc": "cn",
                "pf": "0",
                "vr": self.device_info["version_code"],
                "appvr": self.device_info["version_name"],
                "device-time": str(current_time),
                "tdid": device_id,
                "sign-ver": "2",  # 更新签名版本
                "sign": device_sign,
                "appid": "581595",
                "ac": "wifi",
                "sysvr": "29",
                "ch": "oppo_64_581595",
                "uid": "329468416627917",
                "COMPRESSED": "1",
                "did": f"00000000-3963-0ef2-ffff-ffffef05ac4a",
                "model": base64.b64encode(self.device_info["device_type"].encode()).decode(),
                "manu": base64.b64encode(self.device_info["device_brand"].encode()).decode(),
                "GPURender": "QWRyZW5vIChUTSkgNzUw",
                "HDR-TDID": device_id,
                "HDR-TIID": self.device_info["iid"],
                "HDR-Device-Time": str(current_time),
                "version_code": self.device_info["version_code"],
                "HDR-Sign": device_sign,
                "HDR-Sign-Ver": "2",
                "x-vc-bdturing-sdk-version": "3.7.2.cn",
                "sdk-version": "2",
                "X-Tt-Token": self._generate_tt_token(),
                "passport-sdk-version": "50561",
                "commerce-sign-version": "v1",
                "X-SS-STUB": self._generate_ss_stub(),
                "X-SS-DP": "581595",
                "x-tt-trace-id": self._generate_trace_id(),
                "X-Argus": argus,
                "X-Gorgon": gorgon,
                "X-Helios": self._generate_helios(),
                "X-Khronos": str(current_time),
                "X-Ladon": ladon,
                "X-Medusa": self._generate_medusa(),
                "X-Device-Info": self._generate_device_info()
            }

            return enhanced_headers
        except Exception as e:
            logger.error(f"生成增强headers异常: {str(e)}")
            return self._get_common_headers()

    def _generate_device_sign(self):
        try:
            sign_data={"device_id":self.device_info["device_id"],"iid":self.device_info["iid"],"version_code":self.device_info["version_code"],"device_type":self.device_info["device_type"],"timestamp":str(int(time.time())),"channel":"oppo","app_name":"dreamina"}
            sign_str=''.join([f"{k}{sign_data[k]}" for k in sorted(sign_data.keys())])
            return hashlib.md5(f"dreamina_{sign_str}_bytedance".encode()).hexdigest()
        except:return ""
    def _generate_gorgon_v2(self,timestamp,device_id):
        try:
            hash_value=hashlib.md5(f"gorgon{device_id}{timestamp}dreamina".encode()).hexdigest()
            return f"0404{hash_value[:4]}0002{hash_value[4:12]}{hash_value[12:20]}{hash_value[20:28]}{hash_value[28:]}"
        except:return "0404"+"0"*32
    def _generate_argus_v2(self,timestamp,device_id):
        try:return base64.b64encode(f"argus_{device_id}_{timestamp}_dreamina".encode()).decode()
        except:return "YXJndXNfdjI="
    def _generate_ladon_v2(self,timestamp,device_id):
        try:return base64.b64encode(f"ladon_{device_id}_{timestamp}_dreamina".encode()).decode()
        except:return "bGFkb25fdjI="
    def _generate_device_info(self):
        try:return base64.b64encode(json.dumps({"device_id":self.device_info["device_id"],"device_type":self.device_info["device_type"],"device_brand":self.device_info["device_brand"],"os_version":self.device_info["os_version"],"app_version":self.device_info["version_name"],"resolution":self.device_info["resolution"],"dpi":self.device_info["dpi"],"timestamp":int(time.time())}).encode()).decode()
        except:return ""

    def _generate_cookie(self):
        return "; ".join([f"odin_tt=42772331de245ba95beaf04051447d4d01e7a09ca765898e81b92db9edefe385c5252b933cb5deadb3147dcdd730a2880a5c325bf1e10098d012829f6e4446873e973527428647558a3a16c86609a77d","n_mh=3bj4nRzRoXjC5RCx-DcOggwnWm7DfJmjOj8GghVtbiI","d_ticket=45658fada6cf0fbd2a7a3dcc8f4ffef45cc73","uid_tt=026eed7ed2491a6fe331a3d2644b16f4","uid_tt_ss=026eed7ed2491a6fe331a3d2644b16f4","sid_tt=a00b34aff3a919ca8990897d72a6685d","sessionid=a00b34aff3a919ca8990897d72a6685d","sessionid_ss=a00b34aff3a919ca8990897d72a6685d","is_staff_user=false","store-region=cn-sx","store-region-src=uid",f"sid_guard=a00b34aff3a919ca8990897d72a6685d%7C{int(time.time())}%7C5184000%7CTue%2C+22-Jul-2025+20%3A08%3A47+GMT"])
    def _generate_tt_token(self):
        return "00a00b34aff3a919ca8990897d72a6685d00e65c91a4e70096fdc917f496b9d5e3f50cdf70d1deb2758f80f4063b67821f585cf6f0ece6a5d70fc764af0e9644dfd15efed98fc7ecf5bfc2abecd99413341c04267fd602e7b4f43944ff8f38fba6f52--0a490a20a7a3597d28cc14441cec4566deaa5b6ba92ede4a269e5fb9ce4c7316edac21b91220661334da4594388a40a9be635656a5a6430e84fc758ab83b9aa99854a438883718f6b4d309-3.0.1"
    def _generate_ss_stub(self):
        return hashlib.md5(f"STUB{int(time.time())}{self.device_info['device_id']}".encode()).hexdigest().upper()
    def _generate_trace_id(self):
        trace_id=''.join([random.choice('0123456789abcdef') for _ in range(32)])
        return f"00-{trace_id[:16]}-{trace_id[16:]}-01"
    def _generate_helios(self):
        return base64.b64encode(f"helios{int(time.time())}{self.device_info['device_id']}".encode()).decode()[:52]
    def _generate_medusa(self):
        encoded=base64.b64encode(f"medusa{self.device_info['device_id']}{int(time.time())}".encode()).decode()
        return encoded+"A"*(800-len(encoded))+"=="

    def _check_rate_limit(self,user_wxid):
        current_time=time.time()
        if user_wxid not in self.user_last_request or (current_time-self.user_last_request.get(user_wxid,0))>self.cooldown:
            self.user_last_request[user_wxid],self.user_request_count[user_wxid]=current_time,1
            return False
        self.user_last_request[user_wxid]=current_time
        self.user_request_count[user_wxid]=self.user_request_count.get(user_wxid,0)+1
        return True

    def _get_common_params(self):
        return {"device_platform":"android","os":"android","app_name":"dreamina","version_code":self.device_info["version_code"],"version_name":self.device_info["version_name"],"device_brand":self.device_info["device_brand"],"device_type":self.device_info["device_type"],"os_version":self.device_info["os_version"],"channel":"oppo","aid":"581595","resolution":self.device_info["resolution"],"dpi":self.device_info["dpi"],"update_version_code":self.device_info["version_code"],"_rticket":str(int(time.time()*1000))}
    def _get_common_headers(self):
        return {"User-Agent":f"com.jianying.dreamina/{self.device_info['version_name']} (Linux; U; Android {self.device_info['os_version']}; {self.device_info['device_type']} Build/QP1A.190711.020; wv)","Accept-Encoding":"gzip, deflate","Connection":"keep-alive","Accept":"*/*","Host":"dreamina-app-lq.jianying.com"}

    async def _wait_for_results(self,task_id):
        try:
            url,params,data=f"{self.base_url}/mweb/v1/get_history_by_ids",self._get_common_params(),{"history_ids":[task_id]}
            headers=self._get_enhanced_headers()
            headers["Content-Type"]="application/json; charset=utf-8"
            for attempt in range(20):
                try:
                    async with httpx.AsyncClient(timeout=30) as client:
                        response=await client.post(url,params=params,headers=headers,json=data)
                        if response.status_code!=200:
                            await asyncio.sleep(3)
                            continue
                        try:result=response.json()
                        except:
                            await asyncio.sleep(3)
                            continue
                        if result.get("ret")=="0" and task_id in result.get("data",{}):
                            data_result=result.get("data",{}).get(task_id,{})
                            status=data_result.get("status") or data_result.get("task",{}).get("status")
                            if status==50:
                                item_list=data_result.get("item_list",[])
                                if item_list:
                                    processed_images=[]
                                    for item in item_list:
                                        for large_image in item.get("image",{}).get("large_images",[]):
                                            if large_image.get("image_url"):
                                                processed_images.append({"url":large_image.get("image_url"),"width":large_image.get("width"),"height":large_image.get("height"),"format":large_image.get("format")})
                                    return processed_images
                                return None
                            elif status==40:return None
                            elif status==20:
                                forecast_time=data_result.get("forecast_generate_cost",0)+data_result.get("forecast_queue_cost",0)
                                await asyncio.sleep(min(max(forecast_time/4,3),10))
                            else:await asyncio.sleep(3)
                        else:await asyncio.sleep(3)
                except:
                    await asyncio.sleep(3)
                    continue
            return None
        except:return None

    async def _send_usage_instructions(self,bot,wxid,user_wxid):
        await bot.send_at_message(wxid,f"🎨 图生图插件使用说明\n📝 命令格式:\n• {self.command_format}\n• {self.quote_command_format}\n📐 支持比例: 1:1,3:4,4:3,9:16,16:9,2:3,3:2\n💡 示例: 图生图 二次元风格 9:16 C:\\photo.jpg\n⏱️ 处理时间: 约10-30秒",[user_wxid])

