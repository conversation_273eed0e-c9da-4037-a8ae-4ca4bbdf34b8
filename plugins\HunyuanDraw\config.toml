[HunyuanDraw]
enable = true
command = ["混元画画", "混元绘画", "混元生图"]
command-format = """
🎨 腾讯混元AI绘画使用说明：

📝 基础用法：
混元画画 <描述文本>

💡 示例：
混元画画 穿着清凉的中国美女在游泳池
混元画画 可爱的小猫咪在花园里玩耍
混元画画 未来科技城市的夜景

⚙️ 参数说明：
- 支持中文描述
- 生成1024x1024像素图片
- 使用腾讯混元人像生成模型

⏱️ 生成时间：约10-30秒
🔄 请耐心等待生成完成
"""

[HunyuanDraw.API]
# 腾讯混元AI API配置
base_url = "https://hunyuan.tencent.com"
cookies = "hy_user=94499ff316764816ac223c37d5dae7b0; hy_token=m+VDk3x4afaVa00fMuIWHiMLmh99TbQd0Fz+k9eaT75iDGV5qAkrSVb0PeHlmgSCoSV0rktXpS70Tzjni+BQ/Q==; hy_source=web"

[HunyuanDraw.rate_limit]
cooldown = 30  # 用户冷却时间（秒）
