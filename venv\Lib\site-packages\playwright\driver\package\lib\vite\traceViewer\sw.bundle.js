var vr=Object.defineProperty;var Lr=(n,t,e)=>t in n?vr(n,t,{enumerable:!0,configurable:!0,writable:!0,value:e}):n[t]=e;var N=(n,t,e)=>Lr(n,typeof t!="symbol"?t+"":t,e);function Nr(n,t){const e=new Array(t.length).fill(0);return new Array(t.length).fill(0).map((r,s)=>(i,a)=>{e[s]=i/a*t[s]*1e3,n(e.reduce((o,f)=>o+f,0),1e3)})}const Tn={"&":"&amp;","<":"&lt;",">":"&gt;",'"':"&quot;","'":"&#39;"};function Fr(n){return n.replace(/[&<>"']/ug,t=>Tn[t])}function Ur(n){return n.replace(/[&<]/ug,t=>Tn[t])}function Ot(n,t,e){return n.find((r,s)=>{if(s===n.length-1)return!0;const i=n[s+1];return Math.abs(t(r)-e)<Math.abs(t(i)-e)})}function Sn(n){return Array.isArray(n)&&typeof n[0]=="string"}function Mr(n){return Array.isArray(n)&&Array.isArray(n[0])}class Wr{constructor(t,e,r,s,i){N(this,"_htmlCache");N(this,"_snapshots");N(this,"_index");N(this,"snapshotName");N(this,"_resources");N(this,"_snapshot");N(this,"_callId");N(this,"_screencastFrames");this._htmlCache=t,this._resources=e,this._snapshots=r,this._index=i,this._snapshot=r[i],this._callId=r[i].callId,this._screencastFrames=s,this.snapshotName=r[i].snapshotName}snapshot(){return this._snapshots[this._index]}viewport(){return this._snapshots[this._index].viewport}closestScreenshot(){var s;const{wallTime:t,timestamp:e}=this.snapshot(),r=t&&((s=this._screencastFrames[0])!=null&&s.frameSwapWallTime)?Ot(this._screencastFrames,i=>i.frameSwapWallTime,t):Ot(this._screencastFrames,i=>i.timestamp,e);return r==null?void 0:r.sha1}render(){const t=[],e=(i,a,o,f)=>{if(typeof i=="string"){o==="STYLE"||o==="style"?t.push(Gr(i)):t.push(Ur(i));return}if(Mr(i)){const h=a-i[0][0];if(h>=0&&h<=a){const u=Br(this._snapshots[h]),y=i[0][1];if(y>=0&&y<u.length)return e(u[y],h,o,f)}}else if(Sn(i)){const[h,u,...y]=i,b=h==="NOSCRIPT"?"X-NOSCRIPT":h,R=Object.entries(u||{});t.push("<",b);const C="__playwright_current_src__",l=b==="IFRAME"||b==="FRAME",c=b==="A",d=b==="IMG",p=d&&R.some(g=>g[0]===C),w=b==="SOURCE"&&o==="PICTURE"&&(f==null?void 0:f.some(g=>g[0]===C));for(const[g,T]of R){let _=g;l&&g.toLowerCase()==="src"&&(_="__playwright_src__"),d&&g===C&&(_="src"),["src","srcset"].includes(g.toLowerCase())&&(p||w)&&(_="_"+_);let S=T;c&&g.toLowerCase()==="href"?S="link://"+T:(g.toLowerCase()==="href"||g.toLowerCase()==="src"||g===C)&&(S=Qe(T)),t.push(" ",_,'="',Fr(S),'"')}t.push(">");for(const g of y)e(g,a,b,R);Hr.has(b)||t.push("</",b,">");return}else return},r=this._snapshot;return{html:this._htmlCache.getOrCompute(this,()=>{e(r.html,this._index,void 0,void 0);const a=(r.doctype?`<!DOCTYPE ${r.doctype}>`:"")+["<style>*,*::before,*::after { visibility: hidden }</style>",`<script>${jr(this.viewport(),this._callId,this.snapshotName)}<\/script>`].join("")+t.join("");return{value:a,size:a.length}}),pageId:r.pageId,frameId:r.frameId,index:this._index}}resourceByUrl(t,e){const r=this._snapshot;let s,i;for(const o of this._resources){if(typeof o._monotonicTime=="number"&&o._monotonicTime>=r.timestamp)break;o.response.status!==304&&o.request.url===t&&o.request.method===e&&(o._frameref===r.frameId?s=o:i=o)}let a=s??i;if(a&&e.toUpperCase()==="GET"){for(const o of r.resourceOverrides)if(t===o.url&&o.sha1){a={...a,response:{...a.response,content:{...a.response.content,_sha1:o.sha1}}};break}}return a}}const Hr=new Set(["AREA","BASE","BR","COL","COMMAND","EMBED","HR","IMG","INPUT","KEYGEN","LINK","MENUITEM","META","PARAM","SOURCE","TRACK","WBR"]);function Br(n){if(!n._nodes){const t=[],e=r=>{if(typeof r=="string")t.push(r);else if(Sn(r)){const[,,...s]=r;for(const i of s)e(i);t.push(r)}};e(n.html),n._nodes=t}return n._nodes}function jr(n,...t){function e(r,s,...i){const a=new URLSearchParams(location.search),o=a.has("shouldPopulateCanvasFromScreenshot"),f=a.has("isUnderTest"),h={viewport:s,frames:new WeakMap};window.__playwright_frame_bounding_rects__=h;const u="Recorded click position in absolute coordinates did not match the center of the clicked element. This is likely due to a difference between the test runner and the trace viewer operating systems.",y=[],b=[],R=[],C=[];let l=window;for(;l!==l.parent&&!l.location.pathname.match(/\/page@[a-z0-9]+$/);)l=l.parent;const c=w=>{for(const g of w.querySelectorAll("[__playwright_scroll_top_]"))y.push(g);for(const g of w.querySelectorAll("[__playwright_scroll_left_]"))b.push(g);for(const g of w.querySelectorAll("[__playwright_value_]")){const T=g;T.type!=="file"&&(T.value=T.getAttribute("__playwright_value_")),g.removeAttribute("__playwright_value_")}for(const g of w.querySelectorAll("[__playwright_checked_]"))g.checked=g.getAttribute("__playwright_checked_")==="true",g.removeAttribute("__playwright_checked_");for(const g of w.querySelectorAll("[__playwright_selected_]"))g.selected=g.getAttribute("__playwright_selected_")==="true",g.removeAttribute("__playwright_selected_");for(const g of w.querySelectorAll("[__playwright_popover_open_]")){try{g.showPopover()}catch{}g.removeAttribute("__playwright_popover_open_")}for(const g of i)for(const T of w.querySelectorAll(`[__playwright_target__="${g}"]`)){const _=T.style;_.outline="2px solid #006ab1",_.backgroundColor="#6fa8dc7f",R.push(T)}for(const g of w.querySelectorAll("iframe, frame")){const T=g.getAttribute("__playwright_bounding_rect__");g.removeAttribute("__playwright_bounding_rect__");const _=T?JSON.parse(T):void 0;_&&h.frames.set(g,{boundingRect:_,scrollLeft:0,scrollTop:0});const S=g.getAttribute("__playwright_src__");if(!S)g.setAttribute("src",'data:text/html,<body style="background: #ddd"></body>');else{const E=new URL(r(window.location.href)),A=E.pathname.lastIndexOf("/snapshot/");A!==-1&&(E.pathname=E.pathname.substring(0,A+1)),E.pathname+=S.substring(1),g.setAttribute("src",E.toString())}}{const g=w.querySelector("body[__playwright_custom_elements__]");if(g&&window.customElements){const T=(g.getAttribute("__playwright_custom_elements__")||"").split(",");for(const _ of T)window.customElements.define(_,class extends HTMLElement{})}}for(const g of w.querySelectorAll("template[__playwright_shadow_root_]")){const T=g,_=T.parentElement.attachShadow({mode:"open"});_.appendChild(T.content),T.remove(),c(_)}if("adoptedStyleSheets"in w){const g=[...w.adoptedStyleSheets];for(const T of w.querySelectorAll("template[__playwright_style_sheet_]")){const _=T,S=new CSSStyleSheet;S.replaceSync(_.getAttribute("__playwright_style_sheet_")),g.push(S)}w.adoptedStyleSheets=g}C.push(...w.querySelectorAll("canvas"))},d=()=>{window.removeEventListener("load",d);for(const T of y)T.scrollTop=+T.getAttribute("__playwright_scroll_top_"),T.removeAttribute("__playwright_scroll_top_"),h.frames.has(T)&&(h.frames.get(T).scrollTop=T.scrollTop);for(const T of b)T.scrollLeft=+T.getAttribute("__playwright_scroll_left_"),T.removeAttribute("__playwright_scroll_left_"),h.frames.has(T)&&(h.frames.get(T).scrollLeft=T.scrollTop);document.styleSheets[0].disabled=!0;const w=new URL(window.location.href).searchParams,g=window===l;if(w.get("pointX")&&w.get("pointY")){const T=+w.get("pointX"),_=+w.get("pointY"),S=w.has("hasInputTarget"),E=R.length>0,A=document.documentElement?[document.documentElement]:[];for(const x of E?R:A){const m=document.createElement("x-pw-pointer");if(m.style.position="fixed",m.style.backgroundColor="#f44336",m.style.width="20px",m.style.height="20px",m.style.borderRadius="10px",m.style.margin="-10px 0 0 -10px",m.style.zIndex="2147483646",m.style.display="flex",m.style.alignItems="center",m.style.justifyContent="center",E){const k=x.getBoundingClientRect(),O=k.left+k.width/2,v=k.top+k.height/2;if(m.style.left=O+"px",m.style.top=v+"px",g&&(Math.abs(O-T)>=10||Math.abs(v-_)>=10)){const D=document.createElement("x-pw-pointer-warning");D.textContent="⚠",D.style.fontSize="19px",D.style.color="white",D.style.marginTop="-3.5px",D.style.userSelect="none",m.appendChild(D),m.setAttribute("title",u)}document.documentElement.appendChild(m)}else g&&!S&&(m.style.left=T+"px",m.style.top=_+"px",document.documentElement.appendChild(m))}}if(C.length>0){let T=function(S,E){function A(){const x=document.createElement("canvas");x.width=x.width/Math.floor(x.width/24),x.height=x.height/Math.floor(x.height/24);const m=x.getContext("2d");return m.fillStyle="lightgray",m.fillRect(0,0,x.width,x.height),m.fillStyle="white",m.fillRect(0,0,x.width/2,x.height/2),m.fillRect(x.width/2,x.height/2,x.width,x.height),m.createPattern(x,"repeat")}S.fillStyle=A(),S.fillRect(0,0,E.width,E.height)};const _=new Image;_.onload=()=>{var S;for(const E of C){const A=E.getContext("2d"),x=E.getAttribute("__playwright_bounding_rect__");if(E.removeAttribute("__playwright_bounding_rect__"),!x)continue;let m;try{m=JSON.parse(x)}catch{continue}let k=window;for(;k!==l;){const U=k.frameElement;k=k.parent;const L=(S=k.__playwright_frame_bounding_rects__)==null?void 0:S.frames.get(U);if(!(L!=null&&L.boundingRect))break;const te=L.boundingRect.left-L.scrollLeft,P=L.boundingRect.top-L.scrollTop;m.left+=te,m.top+=P,m.right+=te,m.bottom+=P}const{width:O,height:v}=l.__playwright_frame_bounding_rects__.viewport;m.left=m.left/O,m.top=m.top/v,m.right=m.right/O,m.bottom=m.bottom/v;const D=m.right>1||m.bottom>1;if(m.left>1||m.top>1){E.title="Playwright couldn't capture canvas contents because it's located outside the viewport.";continue}T(A,E),o?(A.drawImage(_,m.left*_.width,m.top*_.height,(m.right-m.left)*_.width,(m.bottom-m.top)*_.height,0,0,E.width,E.height),D?E.title="Playwright couldn't capture full canvas contents because it's located partially outside the viewport.":E.title="Canvas contents are displayed on a best-effort basis based on viewport screenshots taken during test execution."):E.title="Canvas content display is disabled.",f&&console.log("canvas drawn:",JSON.stringify([m.left,m.top,m.right-m.left,m.bottom-m.top].map(U=>Math.floor(U*100))))}},_.onerror=()=>{for(const S of C){const E=S.getContext("2d");T(E,S),S.title="Playwright couldn't show canvas contents because the screenshot failed to load."}},_.src=location.href.replace("/snapshot","/closest-screenshot")}},p=()=>c(document);window.addEventListener("load",d),window.addEventListener("DOMContentLoaded",p)}return`
(${e.toString()})(${ze.toString()}, ${JSON.stringify(n)}${t.map(r=>`, "${r}"`).join("")})`}const Rn=["about:","blob:","data:","file:","ftp:","http:","https:","mailto:","sftp:","ws:","wss:"],kt="http://playwright.bloburl/#";function Qe(n){n.startsWith(kt)&&(n=n.substring(kt.length));try{const t=new URL(n);if(t.protocol==="javascript:"||t.protocol==="vbscript:")return"javascript:void(0)";const e=t.protocol==="blob:",r=t.protocol==="file:";if(!e&&!r&&Rn.includes(t.protocol))return n;const s="pw-"+t.protocol.slice(0,t.protocol.length-1);return r||(t.protocol="https:"),t.hostname=t.hostname?`${s}--${t.hostname}`:s,r&&(t.protocol="https:"),t.toString()}catch{return n}}const qr=/url\(['"]?([\w-]+:)\/\//ig;function Gr(n){return n.replace(qr,(t,e)=>!(e==="blob:")&&!(e==="file:")&&Rn.includes(e)?t:t.replace(e+"//",`https://pw-${e.slice(0,-1)}--`))}function ze(n){const t=new URL(n);return t.pathname.endsWith("/snapshot.html")?t.searchParams.get("r"):n}class Vr{constructor(t,e){N(this,"_snapshotStorage");N(this,"_resourceLoader");N(this,"_snapshotIds",new Map);this._snapshotStorage=t,this._resourceLoader=e}serveSnapshot(t,e,r){const s=this._snapshot(t.substring(9),e);if(!s)return new Response(null,{status:404});const i=s.render();return this._snapshotIds.set(r,s),new Response(i.html,{status:200,headers:{"Content-Type":"text/html; charset=utf-8"}})}async serveClosestScreenshot(t,e){const r=this._snapshot(t.substring(19),e),s=r==null?void 0:r.closestScreenshot();return s?new Response(await this._resourceLoader(s)):new Response(null,{status:404})}serveSnapshotInfo(t,e){const r=this._snapshot(t.substring(13),e);return this._respondWithJson(r?{viewport:r.viewport(),url:r.snapshot().frameUrl,timestamp:r.snapshot().timestamp,wallTime:r.snapshot().wallTime}:{error:"No snapshot found"})}_snapshot(t,e){const r=e.get("name");return this._snapshotStorage.snapshotByName(t.slice(1),r)}_respondWithJson(t){return new Response(JSON.stringify(t),{status:200,headers:{"Cache-Control":"public, max-age=31536000","Content-Type":"application/json"}})}async serveResource(t,e,r){let s;const i=this._snapshotIds.get(r);for(const R of t)if(s=i==null?void 0:i.resourceByUrl(Yr(R),e),s)break;if(!s)return new Response(null,{status:404});const a=s.response.content._sha1,o=a?await this._resourceLoader(a)||new Blob([]):new Blob([]);let f=s.response.content.mimeType;/^text\/|^application\/(javascript|json)/.test(f)&&!f.includes("charset")&&(f=`${f}; charset=utf-8`);const u=new Headers;f!=="x-unknown"&&u.set("Content-Type",f);for(const{name:R,value:C}of s.response.headers)u.set(R,C);u.delete("Content-Encoding"),u.delete("Access-Control-Allow-Origin"),u.set("Access-Control-Allow-Origin","*"),u.delete("Content-Length"),u.set("Content-Length",String(o.size)),u.set("Cache-Control","public, max-age=31536000");const{status:y}=s.response,b=y===101||y===204||y===205||y===304;return new Response(b?null:o,{headers:u,status:s.response.status,statusText:s.response.statusText})}}function Yr(n){try{const t=new URL(n);return t.hash="",t.toString()}catch{return n}}function Zr(n){const t=new Map,{files:e,stacks:r}=n;for(const s of r){const[i,a]=s;t.set(`call@${i}`,a.map(o=>({file:e[o[0]],line:o[1],column:o[2],function:o[3]})))}return t}class Kr{constructor(t){N(this,"_maxSize");N(this,"_map");N(this,"_size");this._maxSize=t,this._map=new Map,this._size=0}getOrCompute(t,e){if(this._map.has(t)){const s=this._map.get(t);return this._map.delete(t),this._map.set(t,s),s.value}const r=e();for(;this._map.size&&this._size+r.size>this._maxSize;){const[s,i]=this._map.entries().next().value;this._size-=i.size,this._map.delete(s)}return this._map.set(t,r),this._size+=r.size,r.value}}class Xr{constructor(){N(this,"_resources",[]);N(this,"_frameSnapshots",new Map);N(this,"_cache",new Kr(1e8))}addResource(t){t.request.url=Qe(t.request.url),this._resources.push(t)}addFrameSnapshot(t,e){for(const i of t.resourceOverrides)i.url=Qe(i.url);let r=this._frameSnapshots.get(t.frameId);r||(r={raw:[],renderers:[]},this._frameSnapshots.set(t.frameId,r),t.isMainFrame&&this._frameSnapshots.set(t.pageId,r)),r.raw.push(t);const s=new Wr(this._cache,this._resources,r.raw,e,r.raw.length-1);return r.renderers.push(s),s}snapshotByName(t,e){const r=this._frameSnapshots.get(t);return r==null?void 0:r.renderers.find(s=>s.snapshotName===e)}snapshotsForTest(){return[...this._frameSnapshots.keys()]}finalize(){this._resources.sort((t,e)=>(t._monotonicTime||0)-(e._monotonicTime||0))}}class An extends Error{constructor(t){super(t),this.name="TraceVersionError"}}const Dt=7;class $r{constructor(t,e){N(this,"_contextEntry");N(this,"_snapshotStorage");N(this,"_actionMap",new Map);N(this,"_version");N(this,"_pageEntries",new Map);N(this,"_jsHandles",new Map);N(this,"_consoleObjects",new Map);this._contextEntry=t,this._snapshotStorage=e}appendTrace(t){for(const e of t.split(`
`))this._appendEvent(e)}actions(){return[...this._actionMap.values()]}_pageEntry(t){let e=this._pageEntries.get(t);return e||(e={pageId:t,screencastFrames:[]},this._pageEntries.set(t,e),this._contextEntry.pages.push(e)),e}_appendEvent(t){if(!t)return;const e=this._modernize(JSON.parse(t));for(const r of e)this._innerAppendEvent(r)}_innerAppendEvent(t){const e=this._contextEntry;switch(t.type){case"context-options":{if(t.version>Dt)throw new An("The trace was created by a newer version of Playwright and is not supported by this version of the viewer. Please use latest Playwright to open the trace.");this._version=t.version,e.origin=t.origin,e.browserName=t.browserName,e.channel=t.channel,e.title=t.title,e.platform=t.platform,e.wallTime=t.wallTime,e.startTime=t.monotonicTime,e.sdkLanguage=t.sdkLanguage,e.options=t.options,e.testIdAttributeName=t.testIdAttributeName;break}case"screencast-frame":{this._pageEntry(t.pageId).screencastFrames.push(t);break}case"before":{this._actionMap.set(t.callId,{...t,type:"action",endTime:0,log:[]});break}case"input":{const r=this._actionMap.get(t.callId);r.inputSnapshot=t.inputSnapshot,r.point=t.point;break}case"log":{const r=this._actionMap.get(t.callId);if(!r)return;r.log.push({time:t.time,message:t.message});break}case"after":{const r=this._actionMap.get(t.callId);r.afterSnapshot=t.afterSnapshot,r.endTime=t.endTime,r.result=t.result,r.error=t.error,r.attachments=t.attachments,t.point&&(r.point=t.point);break}case"action":{this._actionMap.set(t.callId,{...t,log:[]});break}case"event":{e.events.push(t);break}case"stdout":{e.stdio.push(t);break}case"stderr":{e.stdio.push(t);break}case"error":{e.errors.push(t);break}case"console":{e.events.push(t);break}case"resource-snapshot":this._snapshotStorage.addResource(t.snapshot),e.resources.push(t.snapshot);break;case"frame-snapshot":this._snapshotStorage.addFrameSnapshot(t.snapshot,this._pageEntry(t.snapshot.pageId).screencastFrames);break}"pageId"in t&&t.pageId&&this._pageEntry(t.pageId),(t.type==="action"||t.type==="before")&&(e.startTime=Math.min(e.startTime,t.startTime)),(t.type==="action"||t.type==="after")&&(e.endTime=Math.max(e.endTime,t.endTime)),t.type==="event"&&(e.startTime=Math.min(e.startTime,t.time),e.endTime=Math.max(e.endTime,t.time)),t.type==="screencast-frame"&&(e.startTime=Math.min(e.startTime,t.timestamp),e.endTime=Math.max(e.endTime,t.timestamp))}_processedContextCreatedEvent(){return this._version!==void 0}_modernize(t){let e=this._version||t.version;if(e===void 0)return[t];let r=[t];for(;e<Dt;++e)r=this[`_modernize_${e}_to_${e+1}`].call(this,r);return r}_modernize_0_to_1(t){for(const e of t)e.type==="action"&&typeof e.metadata.error=="string"&&(e.metadata.error={error:{name:"Error",message:e.metadata.error}});return t}_modernize_1_to_2(t){var e;for(const r of t)r.type!=="frame-snapshot"||!r.snapshot.isMainFrame||(r.snapshot.viewport=((e=this._contextEntry.options)==null?void 0:e.viewport)||{width:1280,height:720});return t}_modernize_2_to_3(t){for(const e of t){if(e.type!=="resource-snapshot"||e.snapshot.request)continue;const r=e.snapshot;e.snapshot={_frameref:r.frameId,request:{url:r.url,method:r.method,headers:r.requestHeaders,postData:r.requestSha1?{_sha1:r.requestSha1}:void 0},response:{status:r.status,headers:r.responseHeaders,content:{mimeType:r.contentType,_sha1:r.responseSha1}},_monotonicTime:r.timestamp}}return t}_modernize_3_to_4(t){const e=[];for(const r of t){const s=this._modernize_event_3_to_4(r);s&&e.push(s)}return e}_modernize_event_3_to_4(t){var r,s,i,a;if(t.type!=="action"&&t.type!=="event")return t;const e=t.metadata;return e.internal||e.method.startsWith("tracing")?null:t.type==="event"?e.method==="__create__"&&e.type==="ConsoleMessage"?{type:"object",class:e.type,guid:e.params.guid,initializer:e.params.initializer}:{type:"event",time:e.startTime,class:e.type,method:e.method,params:e.params,pageId:e.pageId}:{type:"action",callId:e.id,startTime:e.startTime,endTime:e.endTime,apiName:e.apiName||e.type+"."+e.method,class:e.type,method:e.method,params:e.params,wallTime:e.wallTime||Date.now(),log:e.log,beforeSnapshot:(r=e.snapshots.find(o=>o.title==="before"))==null?void 0:r.snapshotName,inputSnapshot:(s=e.snapshots.find(o=>o.title==="input"))==null?void 0:s.snapshotName,afterSnapshot:(i=e.snapshots.find(o=>o.title==="after"))==null?void 0:i.snapshotName,error:(a=e.error)==null?void 0:a.error,result:e.result,point:e.point,pageId:e.pageId}}_modernize_4_to_5(t){const e=[];for(const r of t){const s=this._modernize_event_4_to_5(r);s&&e.push(s)}return e}_modernize_event_4_to_5(t){var e,r;if(t.type==="event"&&t.method==="__create__"&&t.class==="JSHandle"&&this._jsHandles.set(t.params.guid,t.params.initializer),t.type==="object"){if(t.class!=="ConsoleMessage")return null;const s=(e=t.initializer.args)==null?void 0:e.map(i=>{if(i.guid){const a=this._jsHandles.get(i.guid);return{preview:(a==null?void 0:a.preview)||"",value:""}}return{preview:i.preview||"",value:i.value||""}});return this._consoleObjects.set(t.guid,{type:t.initializer.type,text:t.initializer.text,location:t.initializer.location,args:s}),null}if(t.type==="event"&&t.method==="console"){const s=this._consoleObjects.get(((r=t.params.message)==null?void 0:r.guid)||"");return s?{type:"console",time:t.time,pageId:t.pageId,messageType:s.type,text:s.text,args:s.args,location:s.location}:null}return t}_modernize_5_to_6(t){const e=[];for(const r of t)if(e.push(r),!(r.type!=="after"||!r.log.length))for(const s of r.log)e.push({type:"log",callId:r.callId,message:s,time:-1});return e}_modernize_6_to_7(t){const e=[];if(!this._processedContextCreatedEvent()&&t[0].type!=="context-options"){const r={type:"context-options",origin:"testRunner",version:7,browserName:"",options:{},platform:process.platform,wallTime:0,monotonicTime:0,sdkLanguage:"javascript"};e.push(r)}for(const r of t){if(r.type==="context-options"){e.push({...r,monotonicTime:0,origin:"library"});continue}!this._contextEntry.wallTime&&r.type==="before"&&(this._contextEntry.wallTime=r.wallTime),!this._contextEntry.startTime&&r.type==="before"&&(this._contextEntry.startTime=r.startTime),e.push(r)}return e}}class Jr{constructor(){N(this,"contextEntries",[]);N(this,"_snapshotStorage");N(this,"_backend");N(this,"_resourceToContentType",new Map)}async load(t,e){var o,f;this._backend=t;const r=[];let s=!1;for(const h of await this._backend.entryNames()){const u=h.match(/(.+)\.trace/);u&&r.push(u[1]||""),h.includes("src@")&&(s=!0)}if(!r.length)throw new Error("Cannot find .trace file");this._snapshotStorage=new Xr;const i=r.length*3;let a=0;for(const h of r){const u=Qr();u.traceUrl=t.traceURL(),u.hasSource=s;const y=new $r(u,this._snapshotStorage),b=await this._backend.readText(h+".trace")||"";y.appendTrace(b),e(++a,i);const R=await this._backend.readText(h+".network")||"";if(y.appendTrace(R),e(++a,i),u.actions=y.actions().sort((l,c)=>l.startTime-c.startTime),!t.isLive()){for(const l of u.actions.slice().reverse())if(!l.endTime&&!l.error)for(const c of u.actions)c.parentId===l.callId&&l.endTime<c.endTime&&(l.endTime=c.endTime)}const C=await this._backend.readText(h+".stacks");if(C){const l=Zr(JSON.parse(C));for(const c of u.actions)c.stack=c.stack||l.get(c.callId)}e(++a,i);for(const l of u.resources)(o=l.request.postData)!=null&&o._sha1&&this._resourceToContentType.set(l.request.postData._sha1,It(l.request.postData.mimeType)),(f=l.response.content)!=null&&f._sha1&&this._resourceToContentType.set(l.response.content._sha1,It(l.response.content.mimeType));this.contextEntries.push(u)}this._snapshotStorage.finalize()}async hasEntry(t){return this._backend.hasEntry(t)}async resourceForSha1(t){const e=await this._backend.readBlob("resources/"+t),r=this._resourceToContentType.get(t);return!e||r===void 0||r==="x-unknown"?e:new Blob([e],{type:r})}storage(){return this._snapshotStorage}}function It(n){const t=n.match(/^(.*);\s*charset=.*$/);return t?t[1]:n}function Qr(){return{origin:"testRunner",traceUrl:"",startTime:Number.MAX_SAFE_INTEGER,wallTime:Number.MAX_SAFE_INTEGER,endTime:0,browserName:"",options:{deviceScaleFactor:1,isMobile:!1,viewport:{width:1280,height:800}},pages:[],resources:[],actions:[],events:[],errors:[],stdio:[],hasSource:!1}}const zr=15,F=0,z=1,es=2,Y=-2,H=-3,Pt=-4,ee=-5,Z=[0,1,3,7,15,31,63,127,255,511,1023,2047,4095,8191,16383,32767,65535],Cn=1440,ts=0,ns=4,rs=9,ss=5,is=[96,7,256,0,8,80,0,8,16,84,8,115,82,7,31,0,8,112,0,8,48,0,9,192,80,7,10,0,8,96,0,8,32,0,9,160,0,8,0,0,8,128,0,8,64,0,9,224,80,7,6,0,8,88,0,8,24,0,9,144,83,7,59,0,8,120,0,8,56,0,9,208,81,7,17,0,8,104,0,8,40,0,9,176,0,8,8,0,8,136,0,8,72,0,9,240,80,7,4,0,8,84,0,8,20,85,8,227,83,7,43,0,8,116,0,8,52,0,9,200,81,7,13,0,8,100,0,8,36,0,9,168,0,8,4,0,8,132,0,8,68,0,9,232,80,7,8,0,8,92,0,8,28,0,9,152,84,7,83,0,8,124,0,8,60,0,9,216,82,7,23,0,8,108,0,8,44,0,9,184,0,8,12,0,8,140,0,8,76,0,9,248,80,7,3,0,8,82,0,8,18,85,8,163,83,7,35,0,8,114,0,8,50,0,9,196,81,7,11,0,8,98,0,8,34,0,9,164,0,8,2,0,8,130,0,8,66,0,9,228,80,7,7,0,8,90,0,8,26,0,9,148,84,7,67,0,8,122,0,8,58,0,9,212,82,7,19,0,8,106,0,8,42,0,9,180,0,8,10,0,8,138,0,8,74,0,9,244,80,7,5,0,8,86,0,8,22,192,8,0,83,7,51,0,8,118,0,8,54,0,9,204,81,7,15,0,8,102,0,8,38,0,9,172,0,8,6,0,8,134,0,8,70,0,9,236,80,7,9,0,8,94,0,8,30,0,9,156,84,7,99,0,8,126,0,8,62,0,9,220,82,7,27,0,8,110,0,8,46,0,9,188,0,8,14,0,8,142,0,8,78,0,9,252,96,7,256,0,8,81,0,8,17,85,8,131,82,7,31,0,8,113,0,8,49,0,9,194,80,7,10,0,8,97,0,8,33,0,9,162,0,8,1,0,8,129,0,8,65,0,9,226,80,7,6,0,8,89,0,8,25,0,9,146,83,7,59,0,8,121,0,8,57,0,9,210,81,7,17,0,8,105,0,8,41,0,9,178,0,8,9,0,8,137,0,8,73,0,9,242,80,7,4,0,8,85,0,8,21,80,8,258,83,7,43,0,8,117,0,8,53,0,9,202,81,7,13,0,8,101,0,8,37,0,9,170,0,8,5,0,8,133,0,8,69,0,9,234,80,7,8,0,8,93,0,8,29,0,9,154,84,7,83,0,8,125,0,8,61,0,9,218,82,7,23,0,8,109,0,8,45,0,9,186,0,8,13,0,8,141,0,8,77,0,9,250,80,7,3,0,8,83,0,8,19,85,8,195,83,7,35,0,8,115,0,8,51,0,9,198,81,7,11,0,8,99,0,8,35,0,9,166,0,8,3,0,8,131,0,8,67,0,9,230,80,7,7,0,8,91,0,8,27,0,9,150,84,7,67,0,8,123,0,8,59,0,9,214,82,7,19,0,8,107,0,8,43,0,9,182,0,8,11,0,8,139,0,8,75,0,9,246,80,7,5,0,8,87,0,8,23,192,8,0,83,7,51,0,8,119,0,8,55,0,9,206,81,7,15,0,8,103,0,8,39,0,9,174,0,8,7,0,8,135,0,8,71,0,9,238,80,7,9,0,8,95,0,8,31,0,9,158,84,7,99,0,8,127,0,8,63,0,9,222,82,7,27,0,8,111,0,8,47,0,9,190,0,8,15,0,8,143,0,8,79,0,9,254,96,7,256,0,8,80,0,8,16,84,8,115,82,7,31,0,8,112,0,8,48,0,9,193,80,7,10,0,8,96,0,8,32,0,9,161,0,8,0,0,8,128,0,8,64,0,9,225,80,7,6,0,8,88,0,8,24,0,9,145,83,7,59,0,8,120,0,8,56,0,9,209,81,7,17,0,8,104,0,8,40,0,9,177,0,8,8,0,8,136,0,8,72,0,9,241,80,7,4,0,8,84,0,8,20,85,8,227,83,7,43,0,8,116,0,8,52,0,9,201,81,7,13,0,8,100,0,8,36,0,9,169,0,8,4,0,8,132,0,8,68,0,9,233,80,7,8,0,8,92,0,8,28,0,9,153,84,7,83,0,8,124,0,8,60,0,9,217,82,7,23,0,8,108,0,8,44,0,9,185,0,8,12,0,8,140,0,8,76,0,9,249,80,7,3,0,8,82,0,8,18,85,8,163,83,7,35,0,8,114,0,8,50,0,9,197,81,7,11,0,8,98,0,8,34,0,9,165,0,8,2,0,8,130,0,8,66,0,9,229,80,7,7,0,8,90,0,8,26,0,9,149,84,7,67,0,8,122,0,8,58,0,9,213,82,7,19,0,8,106,0,8,42,0,9,181,0,8,10,0,8,138,0,8,74,0,9,245,80,7,5,0,8,86,0,8,22,192,8,0,83,7,51,0,8,118,0,8,54,0,9,205,81,7,15,0,8,102,0,8,38,0,9,173,0,8,6,0,8,134,0,8,70,0,9,237,80,7,9,0,8,94,0,8,30,0,9,157,84,7,99,0,8,126,0,8,62,0,9,221,82,7,27,0,8,110,0,8,46,0,9,189,0,8,14,0,8,142,0,8,78,0,9,253,96,7,256,0,8,81,0,8,17,85,8,131,82,7,31,0,8,113,0,8,49,0,9,195,80,7,10,0,8,97,0,8,33,0,9,163,0,8,1,0,8,129,0,8,65,0,9,227,80,7,6,0,8,89,0,8,25,0,9,147,83,7,59,0,8,121,0,8,57,0,9,211,81,7,17,0,8,105,0,8,41,0,9,179,0,8,9,0,8,137,0,8,73,0,9,243,80,7,4,0,8,85,0,8,21,80,8,258,83,7,43,0,8,117,0,8,53,0,9,203,81,7,13,0,8,101,0,8,37,0,9,171,0,8,5,0,8,133,0,8,69,0,9,235,80,7,8,0,8,93,0,8,29,0,9,155,84,7,83,0,8,125,0,8,61,0,9,219,82,7,23,0,8,109,0,8,45,0,9,187,0,8,13,0,8,141,0,8,77,0,9,251,80,7,3,0,8,83,0,8,19,85,8,195,83,7,35,0,8,115,0,8,51,0,9,199,81,7,11,0,8,99,0,8,35,0,9,167,0,8,3,0,8,131,0,8,67,0,9,231,80,7,7,0,8,91,0,8,27,0,9,151,84,7,67,0,8,123,0,8,59,0,9,215,82,7,19,0,8,107,0,8,43,0,9,183,0,8,11,0,8,139,0,8,75,0,9,247,80,7,5,0,8,87,0,8,23,192,8,0,83,7,51,0,8,119,0,8,55,0,9,207,81,7,15,0,8,103,0,8,39,0,9,175,0,8,7,0,8,135,0,8,71,0,9,239,80,7,9,0,8,95,0,8,31,0,9,159,84,7,99,0,8,127,0,8,63,0,9,223,82,7,27,0,8,111,0,8,47,0,9,191,0,8,15,0,8,143,0,8,79,0,9,255],as=[80,5,1,87,5,257,83,5,17,91,5,4097,81,5,5,89,5,1025,85,5,65,93,5,16385,80,5,3,88,5,513,84,5,33,92,5,8193,82,5,9,90,5,2049,86,5,129,192,5,24577,80,5,2,87,5,385,83,5,25,91,5,6145,81,5,7,89,5,1537,85,5,97,93,5,24577,80,5,4,88,5,769,84,5,49,92,5,12289,82,5,13,90,5,3073,86,5,193,192,5,24577],os=[3,4,5,6,7,8,9,10,11,13,15,17,19,23,27,31,35,43,51,59,67,83,99,115,131,163,195,227,258,0,0],cs=[0,0,0,0,0,0,0,0,1,1,1,1,2,2,2,2,3,3,3,3,4,4,4,4,5,5,5,5,0,112,112],ls=[1,2,3,4,5,7,9,13,17,25,33,49,65,97,129,193,257,385,513,769,1025,1537,2049,3073,4097,6145,8193,12289,16385,24577],fs=[0,0,0,0,1,1,2,2,3,3,4,4,5,5,6,6,7,7,8,8,9,9,10,10,11,11,12,12,13,13],ne=15;function et(){const n=this;let t,e,r,s,i,a;function o(h,u,y,b,R,C,l,c,d,p,w){let g,T,_,S,E,A,x,m,k,O,v,D,I,U,L;O=0,E=y;do r[h[u+O]]++,O++,E--;while(E!==0);if(r[0]==y)return l[0]=-1,c[0]=0,F;for(m=c[0],A=1;A<=ne&&r[A]===0;A++);for(x=A,m<A&&(m=A),E=ne;E!==0&&r[E]===0;E--);for(_=E,m>E&&(m=E),c[0]=m,U=1<<A;A<E;A++,U<<=1)if((U-=r[A])<0)return H;if((U-=r[E])<0)return H;for(r[E]+=U,a[1]=A=0,O=1,I=2;--E!==0;)a[I]=A+=r[O],I++,O++;E=0,O=0;do(A=h[u+O])!==0&&(w[a[A]++]=E),O++;while(++E<y);for(y=a[_],a[0]=E=0,O=0,S=-1,D=-m,i[0]=0,v=0,L=0;x<=_;x++)for(g=r[x];g--!==0;){for(;x>D+m;){if(S++,D+=m,L=_-D,L=L>m?m:L,(T=1<<(A=x-D))>g+1&&(T-=g+1,I=x,A<L))for(;++A<L&&!((T<<=1)<=r[++I]);)T-=r[I];if(L=1<<A,p[0]+L>Cn)return H;i[S]=v=p[0],p[0]+=L,S!==0?(a[S]=E,s[0]=A,s[1]=m,A=E>>>D-m,s[2]=v-i[S-1]-A,d.set(s,(i[S-1]+A)*3)):l[0]=v}for(s[1]=x-D,O>=y?s[0]=192:w[O]<b?(s[0]=w[O]<256?0:96,s[2]=w[O++]):(s[0]=C[w[O]-b]+16+64,s[2]=R[w[O++]-b]),T=1<<x-D,A=E>>>D;A<L;A+=T)d.set(s,(v+A)*3);for(A=1<<x-1;E&A;A>>>=1)E^=A;for(E^=A,k=(1<<D)-1;(E&k)!=a[S];)S--,D-=m,k=(1<<D)-1}return U!==0&&_!=1?ee:F}function f(h){let u;for(t||(t=[],e=[],r=new Int32Array(ne+1),s=[],i=new Int32Array(ne),a=new Int32Array(ne+1)),e.length<h&&(e=[]),u=0;u<h;u++)e[u]=0;for(u=0;u<ne+1;u++)r[u]=0;for(u=0;u<3;u++)s[u]=0;i.set(r.subarray(0,ne),0),a.set(r.subarray(0,ne+1),0)}n.inflate_trees_bits=function(h,u,y,b,R){let C;return f(19),t[0]=0,C=o(h,0,19,19,null,null,y,u,b,t,e),C==H?R.msg="oversubscribed dynamic bit lengths tree":(C==ee||u[0]===0)&&(R.msg="incomplete dynamic bit lengths tree",C=H),C},n.inflate_trees_dynamic=function(h,u,y,b,R,C,l,c,d){let p;return f(288),t[0]=0,p=o(y,0,h,257,os,cs,C,b,c,t,e),p!=F||b[0]===0?(p==H?d.msg="oversubscribed literal/length tree":p!=Pt&&(d.msg="incomplete literal/length tree",p=H),p):(f(288),p=o(y,h,u,0,ls,fs,l,R,c,t,e),p!=F||R[0]===0&&h>257?(p==H?d.msg="oversubscribed distance tree":p==ee?(d.msg="incomplete distance tree",p=H):p!=Pt&&(d.msg="empty distance tree with lengths",p=H),p):F)}}et.inflate_trees_fixed=function(n,t,e,r){return n[0]=rs,t[0]=ss,e[0]=is,r[0]=as,F};const De=0,vt=1,Lt=2,Nt=3,Ft=4,Ut=5,Mt=6,qe=7,Wt=8,Ie=9;function us(){const n=this;let t,e=0,r,s=0,i=0,a=0,o=0,f=0,h=0,u=0,y,b=0,R,C=0;function l(c,d,p,w,g,T,_,S){let E,A,x,m,k,O,v,D,I,U,L,te,P,ue,M,W;v=S.next_in_index,D=S.avail_in,k=_.bitb,O=_.bitk,I=_.write,U=I<_.read?_.read-I-1:_.end-I,L=Z[c],te=Z[d];do{for(;O<20;)D--,k|=(S.read_byte(v++)&255)<<O,O+=8;if(E=k&L,A=p,x=w,W=(x+E)*3,(m=A[W])===0){k>>=A[W+1],O-=A[W+1],_.win[I++]=A[W+2],U--;continue}do{if(k>>=A[W+1],O-=A[W+1],m&16){for(m&=15,P=A[W+2]+(k&Z[m]),k>>=m,O-=m;O<15;)D--,k|=(S.read_byte(v++)&255)<<O,O+=8;E=k&te,A=g,x=T,W=(x+E)*3,m=A[W];do if(k>>=A[W+1],O-=A[W+1],m&16){for(m&=15;O<m;)D--,k|=(S.read_byte(v++)&255)<<O,O+=8;if(ue=A[W+2]+(k&Z[m]),k>>=m,O-=m,U-=P,I>=ue)M=I-ue,I-M>0&&2>I-M?(_.win[I++]=_.win[M++],_.win[I++]=_.win[M++],P-=2):(_.win.set(_.win.subarray(M,M+2),I),I+=2,M+=2,P-=2);else{M=I-ue;do M+=_.end;while(M<0);if(m=_.end-M,P>m){if(P-=m,I-M>0&&m>I-M)do _.win[I++]=_.win[M++];while(--m!==0);else _.win.set(_.win.subarray(M,M+m),I),I+=m,M+=m,m=0;M=0}}if(I-M>0&&P>I-M)do _.win[I++]=_.win[M++];while(--P!==0);else _.win.set(_.win.subarray(M,M+P),I),I+=P,M+=P,P=0;break}else if(!(m&64))E+=A[W+2],E+=k&Z[m],W=(x+E)*3,m=A[W];else return S.msg="invalid distance code",P=S.avail_in-D,P=O>>3<P?O>>3:P,D+=P,v-=P,O-=P<<3,_.bitb=k,_.bitk=O,S.avail_in=D,S.total_in+=v-S.next_in_index,S.next_in_index=v,_.write=I,H;while(!0);break}if(m&64)return m&32?(P=S.avail_in-D,P=O>>3<P?O>>3:P,D+=P,v-=P,O-=P<<3,_.bitb=k,_.bitk=O,S.avail_in=D,S.total_in+=v-S.next_in_index,S.next_in_index=v,_.write=I,z):(S.msg="invalid literal/length code",P=S.avail_in-D,P=O>>3<P?O>>3:P,D+=P,v-=P,O-=P<<3,_.bitb=k,_.bitk=O,S.avail_in=D,S.total_in+=v-S.next_in_index,S.next_in_index=v,_.write=I,H);if(E+=A[W+2],E+=k&Z[m],W=(x+E)*3,(m=A[W])===0){k>>=A[W+1],O-=A[W+1],_.win[I++]=A[W+2],U--;break}}while(!0)}while(U>=258&&D>=10);return P=S.avail_in-D,P=O>>3<P?O>>3:P,D+=P,v-=P,O-=P<<3,_.bitb=k,_.bitk=O,S.avail_in=D,S.total_in+=v-S.next_in_index,S.next_in_index=v,_.write=I,F}n.init=function(c,d,p,w,g,T){t=De,h=c,u=d,y=p,b=w,R=g,C=T,r=null},n.proc=function(c,d,p){let w,g,T,_=0,S=0,E=0,A,x,m,k;for(E=d.next_in_index,A=d.avail_in,_=c.bitb,S=c.bitk,x=c.write,m=x<c.read?c.read-x-1:c.end-x;;)switch(t){case De:if(m>=258&&A>=10&&(c.bitb=_,c.bitk=S,d.avail_in=A,d.total_in+=E-d.next_in_index,d.next_in_index=E,c.write=x,p=l(h,u,y,b,R,C,c,d),E=d.next_in_index,A=d.avail_in,_=c.bitb,S=c.bitk,x=c.write,m=x<c.read?c.read-x-1:c.end-x,p!=F)){t=p==z?qe:Ie;break}i=h,r=y,s=b,t=vt;case vt:for(w=i;S<w;){if(A!==0)p=F;else return c.bitb=_,c.bitk=S,d.avail_in=A,d.total_in+=E-d.next_in_index,d.next_in_index=E,c.write=x,c.inflate_flush(d,p);A--,_|=(d.read_byte(E++)&255)<<S,S+=8}if(g=(s+(_&Z[w]))*3,_>>>=r[g+1],S-=r[g+1],T=r[g],T===0){a=r[g+2],t=Mt;break}if(T&16){o=T&15,e=r[g+2],t=Lt;break}if(!(T&64)){i=T,s=g/3+r[g+2];break}if(T&32){t=qe;break}return t=Ie,d.msg="invalid literal/length code",p=H,c.bitb=_,c.bitk=S,d.avail_in=A,d.total_in+=E-d.next_in_index,d.next_in_index=E,c.write=x,c.inflate_flush(d,p);case Lt:for(w=o;S<w;){if(A!==0)p=F;else return c.bitb=_,c.bitk=S,d.avail_in=A,d.total_in+=E-d.next_in_index,d.next_in_index=E,c.write=x,c.inflate_flush(d,p);A--,_|=(d.read_byte(E++)&255)<<S,S+=8}e+=_&Z[w],_>>=w,S-=w,i=u,r=R,s=C,t=Nt;case Nt:for(w=i;S<w;){if(A!==0)p=F;else return c.bitb=_,c.bitk=S,d.avail_in=A,d.total_in+=E-d.next_in_index,d.next_in_index=E,c.write=x,c.inflate_flush(d,p);A--,_|=(d.read_byte(E++)&255)<<S,S+=8}if(g=(s+(_&Z[w]))*3,_>>=r[g+1],S-=r[g+1],T=r[g],T&16){o=T&15,f=r[g+2],t=Ft;break}if(!(T&64)){i=T,s=g/3+r[g+2];break}return t=Ie,d.msg="invalid distance code",p=H,c.bitb=_,c.bitk=S,d.avail_in=A,d.total_in+=E-d.next_in_index,d.next_in_index=E,c.write=x,c.inflate_flush(d,p);case Ft:for(w=o;S<w;){if(A!==0)p=F;else return c.bitb=_,c.bitk=S,d.avail_in=A,d.total_in+=E-d.next_in_index,d.next_in_index=E,c.write=x,c.inflate_flush(d,p);A--,_|=(d.read_byte(E++)&255)<<S,S+=8}f+=_&Z[w],_>>=w,S-=w,t=Ut;case Ut:for(k=x-f;k<0;)k+=c.end;for(;e!==0;){if(m===0&&(x==c.end&&c.read!==0&&(x=0,m=x<c.read?c.read-x-1:c.end-x),m===0&&(c.write=x,p=c.inflate_flush(d,p),x=c.write,m=x<c.read?c.read-x-1:c.end-x,x==c.end&&c.read!==0&&(x=0,m=x<c.read?c.read-x-1:c.end-x),m===0)))return c.bitb=_,c.bitk=S,d.avail_in=A,d.total_in+=E-d.next_in_index,d.next_in_index=E,c.write=x,c.inflate_flush(d,p);c.win[x++]=c.win[k++],m--,k==c.end&&(k=0),e--}t=De;break;case Mt:if(m===0&&(x==c.end&&c.read!==0&&(x=0,m=x<c.read?c.read-x-1:c.end-x),m===0&&(c.write=x,p=c.inflate_flush(d,p),x=c.write,m=x<c.read?c.read-x-1:c.end-x,x==c.end&&c.read!==0&&(x=0,m=x<c.read?c.read-x-1:c.end-x),m===0)))return c.bitb=_,c.bitk=S,d.avail_in=A,d.total_in+=E-d.next_in_index,d.next_in_index=E,c.write=x,c.inflate_flush(d,p);p=F,c.win[x++]=a,m--,t=De;break;case qe:if(S>7&&(S-=8,A++,E--),c.write=x,p=c.inflate_flush(d,p),x=c.write,m=x<c.read?c.read-x-1:c.end-x,c.read!=c.write)return c.bitb=_,c.bitk=S,d.avail_in=A,d.total_in+=E-d.next_in_index,d.next_in_index=E,c.write=x,c.inflate_flush(d,p);t=Wt;case Wt:return p=z,c.bitb=_,c.bitk=S,d.avail_in=A,d.total_in+=E-d.next_in_index,d.next_in_index=E,c.write=x,c.inflate_flush(d,p);case Ie:return p=H,c.bitb=_,c.bitk=S,d.avail_in=A,d.total_in+=E-d.next_in_index,d.next_in_index=E,c.write=x,c.inflate_flush(d,p);default:return p=Y,c.bitb=_,c.bitk=S,d.avail_in=A,d.total_in+=E-d.next_in_index,d.next_in_index=E,c.write=x,c.inflate_flush(d,p)}},n.free=function(){}}const Ht=[16,17,18,0,8,7,9,6,10,5,11,4,12,3,13,2,14,1,15],pe=0,Ge=1,Bt=2,jt=3,qt=4,Gt=5,Pe=6,ve=7,Vt=8,de=9;function ds(n,t){const e=this;let r=pe,s=0,i=0,a=0,o;const f=[0],h=[0],u=new us;let y=0,b=new Int32Array(Cn*3);const R=0,C=new et;e.bitk=0,e.bitb=0,e.win=new Uint8Array(t),e.end=t,e.read=0,e.write=0,e.reset=function(l,c){c&&(c[0]=R),r==Pe&&u.free(l),r=pe,e.bitk=0,e.bitb=0,e.read=e.write=0},e.reset(n,null),e.inflate_flush=function(l,c){let d,p,w;return p=l.next_out_index,w=e.read,d=(w<=e.write?e.write:e.end)-w,d>l.avail_out&&(d=l.avail_out),d!==0&&c==ee&&(c=F),l.avail_out-=d,l.total_out+=d,l.next_out.set(e.win.subarray(w,w+d),p),p+=d,w+=d,w==e.end&&(w=0,e.write==e.end&&(e.write=0),d=e.write-w,d>l.avail_out&&(d=l.avail_out),d!==0&&c==ee&&(c=F),l.avail_out-=d,l.total_out+=d,l.next_out.set(e.win.subarray(w,w+d),p),p+=d,w+=d),l.next_out_index=p,e.read=w,c},e.proc=function(l,c){let d,p,w,g,T,_,S,E;for(g=l.next_in_index,T=l.avail_in,p=e.bitb,w=e.bitk,_=e.write,S=_<e.read?e.read-_-1:e.end-_;;){let A,x,m,k,O,v,D,I;switch(r){case pe:for(;w<3;){if(T!==0)c=F;else return e.bitb=p,e.bitk=w,l.avail_in=T,l.total_in+=g-l.next_in_index,l.next_in_index=g,e.write=_,e.inflate_flush(l,c);T--,p|=(l.read_byte(g++)&255)<<w,w+=8}switch(d=p&7,y=d&1,d>>>1){case 0:p>>>=3,w-=3,d=w&7,p>>>=d,w-=d,r=Ge;break;case 1:A=[],x=[],m=[[]],k=[[]],et.inflate_trees_fixed(A,x,m,k),u.init(A[0],x[0],m[0],0,k[0],0),p>>>=3,w-=3,r=Pe;break;case 2:p>>>=3,w-=3,r=jt;break;case 3:return p>>>=3,w-=3,r=de,l.msg="invalid block type",c=H,e.bitb=p,e.bitk=w,l.avail_in=T,l.total_in+=g-l.next_in_index,l.next_in_index=g,e.write=_,e.inflate_flush(l,c)}break;case Ge:for(;w<32;){if(T!==0)c=F;else return e.bitb=p,e.bitk=w,l.avail_in=T,l.total_in+=g-l.next_in_index,l.next_in_index=g,e.write=_,e.inflate_flush(l,c);T--,p|=(l.read_byte(g++)&255)<<w,w+=8}if((~p>>>16&65535)!=(p&65535))return r=de,l.msg="invalid stored block lengths",c=H,e.bitb=p,e.bitk=w,l.avail_in=T,l.total_in+=g-l.next_in_index,l.next_in_index=g,e.write=_,e.inflate_flush(l,c);s=p&65535,p=w=0,r=s!==0?Bt:y!==0?ve:pe;break;case Bt:if(T===0||S===0&&(_==e.end&&e.read!==0&&(_=0,S=_<e.read?e.read-_-1:e.end-_),S===0&&(e.write=_,c=e.inflate_flush(l,c),_=e.write,S=_<e.read?e.read-_-1:e.end-_,_==e.end&&e.read!==0&&(_=0,S=_<e.read?e.read-_-1:e.end-_),S===0)))return e.bitb=p,e.bitk=w,l.avail_in=T,l.total_in+=g-l.next_in_index,l.next_in_index=g,e.write=_,e.inflate_flush(l,c);if(c=F,d=s,d>T&&(d=T),d>S&&(d=S),e.win.set(l.read_buf(g,d),_),g+=d,T-=d,_+=d,S-=d,(s-=d)!==0)break;r=y!==0?ve:pe;break;case jt:for(;w<14;){if(T!==0)c=F;else return e.bitb=p,e.bitk=w,l.avail_in=T,l.total_in+=g-l.next_in_index,l.next_in_index=g,e.write=_,e.inflate_flush(l,c);T--,p|=(l.read_byte(g++)&255)<<w,w+=8}if(i=d=p&16383,(d&31)>29||(d>>5&31)>29)return r=de,l.msg="too many length or distance symbols",c=H,e.bitb=p,e.bitk=w,l.avail_in=T,l.total_in+=g-l.next_in_index,l.next_in_index=g,e.write=_,e.inflate_flush(l,c);if(d=258+(d&31)+(d>>5&31),!o||o.length<d)o=[];else for(E=0;E<d;E++)o[E]=0;p>>>=14,w-=14,a=0,r=qt;case qt:for(;a<4+(i>>>10);){for(;w<3;){if(T!==0)c=F;else return e.bitb=p,e.bitk=w,l.avail_in=T,l.total_in+=g-l.next_in_index,l.next_in_index=g,e.write=_,e.inflate_flush(l,c);T--,p|=(l.read_byte(g++)&255)<<w,w+=8}o[Ht[a++]]=p&7,p>>>=3,w-=3}for(;a<19;)o[Ht[a++]]=0;if(f[0]=7,d=C.inflate_trees_bits(o,f,h,b,l),d!=F)return c=d,c==H&&(o=null,r=de),e.bitb=p,e.bitk=w,l.avail_in=T,l.total_in+=g-l.next_in_index,l.next_in_index=g,e.write=_,e.inflate_flush(l,c);a=0,r=Gt;case Gt:for(;d=i,!(a>=258+(d&31)+(d>>5&31));){let U,L;for(d=f[0];w<d;){if(T!==0)c=F;else return e.bitb=p,e.bitk=w,l.avail_in=T,l.total_in+=g-l.next_in_index,l.next_in_index=g,e.write=_,e.inflate_flush(l,c);T--,p|=(l.read_byte(g++)&255)<<w,w+=8}if(d=b[(h[0]+(p&Z[d]))*3+1],L=b[(h[0]+(p&Z[d]))*3+2],L<16)p>>>=d,w-=d,o[a++]=L;else{for(E=L==18?7:L-14,U=L==18?11:3;w<d+E;){if(T!==0)c=F;else return e.bitb=p,e.bitk=w,l.avail_in=T,l.total_in+=g-l.next_in_index,l.next_in_index=g,e.write=_,e.inflate_flush(l,c);T--,p|=(l.read_byte(g++)&255)<<w,w+=8}if(p>>>=d,w-=d,U+=p&Z[E],p>>>=E,w-=E,E=a,d=i,E+U>258+(d&31)+(d>>5&31)||L==16&&E<1)return o=null,r=de,l.msg="invalid bit length repeat",c=H,e.bitb=p,e.bitk=w,l.avail_in=T,l.total_in+=g-l.next_in_index,l.next_in_index=g,e.write=_,e.inflate_flush(l,c);L=L==16?o[E-1]:0;do o[E++]=L;while(--U!==0);a=E}}if(h[0]=-1,O=[],v=[],D=[],I=[],O[0]=9,v[0]=6,d=i,d=C.inflate_trees_dynamic(257+(d&31),1+(d>>5&31),o,O,v,D,I,b,l),d!=F)return d==H&&(o=null,r=de),c=d,e.bitb=p,e.bitk=w,l.avail_in=T,l.total_in+=g-l.next_in_index,l.next_in_index=g,e.write=_,e.inflate_flush(l,c);u.init(O[0],v[0],b,D[0],b,I[0]),r=Pe;case Pe:if(e.bitb=p,e.bitk=w,l.avail_in=T,l.total_in+=g-l.next_in_index,l.next_in_index=g,e.write=_,(c=u.proc(e,l,c))!=z)return e.inflate_flush(l,c);if(c=F,u.free(l),g=l.next_in_index,T=l.avail_in,p=e.bitb,w=e.bitk,_=e.write,S=_<e.read?e.read-_-1:e.end-_,y===0){r=pe;break}r=ve;case ve:if(e.write=_,c=e.inflate_flush(l,c),_=e.write,S=_<e.read?e.read-_-1:e.end-_,e.read!=e.write)return e.bitb=p,e.bitk=w,l.avail_in=T,l.total_in+=g-l.next_in_index,l.next_in_index=g,e.write=_,e.inflate_flush(l,c);r=Vt;case Vt:return c=z,e.bitb=p,e.bitk=w,l.avail_in=T,l.total_in+=g-l.next_in_index,l.next_in_index=g,e.write=_,e.inflate_flush(l,c);case de:return c=H,e.bitb=p,e.bitk=w,l.avail_in=T,l.total_in+=g-l.next_in_index,l.next_in_index=g,e.write=_,e.inflate_flush(l,c);default:return c=Y,e.bitb=p,e.bitk=w,l.avail_in=T,l.total_in+=g-l.next_in_index,l.next_in_index=g,e.write=_,e.inflate_flush(l,c)}}},e.free=function(l){e.reset(l,null),e.win=null,b=null},e.set_dictionary=function(l,c,d){e.win.set(l.subarray(c,c+d),0),e.read=e.write=d},e.sync_point=function(){return r==Ge?1:0}}const hs=32,_s=8,ps=0,Yt=1,Zt=2,Kt=3,Xt=4,$t=5,Ve=6,ye=7,Jt=12,re=13,ms=[0,0,255,255];function ws(){const n=this;n.mode=0,n.method=0,n.was=[0],n.need=0,n.marker=0,n.wbits=0;function t(e){return!e||!e.istate?Y:(e.total_in=e.total_out=0,e.msg=null,e.istate.mode=ye,e.istate.blocks.reset(e,null),F)}n.inflateEnd=function(e){return n.blocks&&n.blocks.free(e),n.blocks=null,F},n.inflateInit=function(e,r){return e.msg=null,n.blocks=null,r<8||r>15?(n.inflateEnd(e),Y):(n.wbits=r,e.istate.blocks=new ds(e,1<<r),t(e),F)},n.inflate=function(e,r){let s,i;if(!e||!e.istate||!e.next_in)return Y;const a=e.istate;for(r=r==ns?ee:F,s=ee;;)switch(a.mode){case ps:if(e.avail_in===0)return s;if(s=r,e.avail_in--,e.total_in++,((a.method=e.read_byte(e.next_in_index++))&15)!=_s){a.mode=re,e.msg="unknown compression method",a.marker=5;break}if((a.method>>4)+8>a.wbits){a.mode=re,e.msg="invalid win size",a.marker=5;break}a.mode=Yt;case Yt:if(e.avail_in===0)return s;if(s=r,e.avail_in--,e.total_in++,i=e.read_byte(e.next_in_index++)&255,((a.method<<8)+i)%31!==0){a.mode=re,e.msg="incorrect header check",a.marker=5;break}if(!(i&hs)){a.mode=ye;break}a.mode=Zt;case Zt:if(e.avail_in===0)return s;s=r,e.avail_in--,e.total_in++,a.need=(e.read_byte(e.next_in_index++)&255)<<24&4278190080,a.mode=Kt;case Kt:if(e.avail_in===0)return s;s=r,e.avail_in--,e.total_in++,a.need+=(e.read_byte(e.next_in_index++)&255)<<16&16711680,a.mode=Xt;case Xt:if(e.avail_in===0)return s;s=r,e.avail_in--,e.total_in++,a.need+=(e.read_byte(e.next_in_index++)&255)<<8&65280,a.mode=$t;case $t:return e.avail_in===0?s:(s=r,e.avail_in--,e.total_in++,a.need+=e.read_byte(e.next_in_index++)&255,a.mode=Ve,es);case Ve:return a.mode=re,e.msg="need dictionary",a.marker=0,Y;case ye:if(s=a.blocks.proc(e,s),s==H){a.mode=re,a.marker=0;break}if(s==F&&(s=r),s!=z)return s;s=r,a.blocks.reset(e,a.was),a.mode=Jt;case Jt:return e.avail_in=0,z;case re:return H;default:return Y}},n.inflateSetDictionary=function(e,r,s){let i=0,a=s;if(!e||!e.istate||e.istate.mode!=Ve)return Y;const o=e.istate;return a>=1<<o.wbits&&(a=(1<<o.wbits)-1,i=s-a),o.blocks.set_dictionary(r,i,a),o.mode=ye,F},n.inflateSync=function(e){let r,s,i,a,o;if(!e||!e.istate)return Y;const f=e.istate;if(f.mode!=re&&(f.mode=re,f.marker=0),(r=e.avail_in)===0)return ee;for(s=e.next_in_index,i=f.marker;r!==0&&i<4;)e.read_byte(s)==ms[i]?i++:e.read_byte(s)!==0?i=0:i=4-i,s++,r--;return e.total_in+=s-e.next_in_index,e.next_in_index=s,e.avail_in=r,f.marker=i,i!=4?H:(a=e.total_in,o=e.total_out,t(e),e.total_in=a,e.total_out=o,f.mode=ye,F)},n.inflateSyncPoint=function(e){return!e||!e.istate||!e.istate.blocks?Y:e.istate.blocks.sync_point()}}function On(){}On.prototype={inflateInit(n){const t=this;return t.istate=new ws,n||(n=zr),t.istate.inflateInit(t,n)},inflate(n){const t=this;return t.istate?t.istate.inflate(t,n):Y},inflateEnd(){const n=this;if(!n.istate)return Y;const t=n.istate.inflateEnd(n);return n.istate=null,t},inflateSync(){const n=this;return n.istate?n.istate.inflateSync(n):Y},inflateSetDictionary(n,t){const e=this;return e.istate?e.istate.inflateSetDictionary(e,n,t):Y},read_byte(n){return this.next_in[n]},read_buf(n,t){return this.next_in.subarray(n,n+t)}};function gs(n){const t=this,e=new On,r=n&&n.chunkSize?Math.floor(n.chunkSize*2):128*1024,s=ts,i=new Uint8Array(r);let a=!1;e.inflateInit(),e.next_out=i,t.append=function(o,f){const h=[];let u,y,b=0,R=0,C=0;if(o.length!==0){e.next_in_index=0,e.next_in=o,e.avail_in=o.length;do{if(e.next_out_index=0,e.avail_out=r,e.avail_in===0&&!a&&(e.next_in_index=0,a=!0),u=e.inflate(s),a&&u===ee){if(e.avail_in!==0)throw new Error("inflating: bad input")}else if(u!==F&&u!==z)throw new Error("inflating: "+e.msg);if((a||u===z)&&e.avail_in===o.length)throw new Error("inflating: bad input");e.next_out_index&&(e.next_out_index===r?h.push(new Uint8Array(i)):h.push(i.subarray(0,e.next_out_index))),C+=e.next_out_index,f&&e.next_in_index>0&&e.next_in_index!=b&&(f(e.next_in_index),b=e.next_in_index)}while(e.avail_in>0||e.avail_out===0);return h.length>1?(y=new Uint8Array(C),h.forEach(function(l){y.set(l,R),R+=l.length})):y=h[0]?new Uint8Array(h[0]):new Uint8Array,y}},t.flush=function(){e.inflateEnd()}}const he=4294967295,oe=65535,bs=8,ys=0,xs=99,Es=67324752,Ts=134695760,Qt=33639248,Ss=101010256,zt=101075792,Rs=117853008,me=22,Ye=20,Ze=56,As=1,Cs=39169,Os=10,ks=1,Ds=21589,Is=28789,Ps=25461,vs=6534,en=1,Ls=6,tn=8,nn=2048,rn=16,Ns="/",$=void 0,Fe="undefined",kn="function";class sn{constructor(t){return class extends TransformStream{constructor(e,r){const s=new t(r);super({transform(i,a){a.enqueue(s.append(i))},flush(i){const a=s.flush();a&&i.enqueue(a)}})}}}}const Fs=64;let Dn=2;try{typeof navigator!=Fe&&navigator.hardwareConcurrency&&(Dn=navigator.hardwareConcurrency)}catch{}const Us={chunkSize:512*1024,maxWorkers:Dn,terminateWorkerTimeout:5e3,useWebWorkers:!0,useCompressionStream:!0,workerScripts:$,CompressionStreamNative:typeof CompressionStream!=Fe&&CompressionStream,DecompressionStreamNative:typeof DecompressionStream!=Fe&&DecompressionStream},ce=Object.assign({},Us);function In(){return ce}function Ms(n){return Math.max(n.chunkSize,Fs)}function Pn(n){const{baseURL:t,chunkSize:e,maxWorkers:r,terminateWorkerTimeout:s,useCompressionStream:i,useWebWorkers:a,Deflate:o,Inflate:f,CompressionStream:h,DecompressionStream:u,workerScripts:y}=n;if(se("baseURL",t),se("chunkSize",e),se("maxWorkers",r),se("terminateWorkerTimeout",s),se("useCompressionStream",i),se("useWebWorkers",a),o&&(ce.CompressionStream=new sn(o)),f&&(ce.DecompressionStream=new sn(f)),se("CompressionStream",h),se("DecompressionStream",u),y!==$){const{deflate:b,inflate:R}=y;if((b||R)&&(ce.workerScripts||(ce.workerScripts={})),b){if(!Array.isArray(b))throw new Error("workerScripts.deflate must be an array");ce.workerScripts.deflate=b}if(R){if(!Array.isArray(R))throw new Error("workerScripts.inflate must be an array");ce.workerScripts.inflate=R}}}function se(n,t){t!==$&&(ce[n]=t)}function Ws(){return"application/octet-stream"}const vn=[];for(let n=0;n<256;n++){let t=n;for(let e=0;e<8;e++)t&1?t=t>>>1^3988292384:t=t>>>1;vn[n]=t}class Ue{constructor(t){this.crc=t||-1}append(t){let e=this.crc|0;for(let r=0,s=t.length|0;r<s;r++)e=e>>>8^vn[(e^t[r])&255];this.crc=e}get(){return~this.crc}}class Ln extends TransformStream{constructor(){let t;const e=new Ue;super({transform(r,s){e.append(r),s.enqueue(r)},flush(){const r=new Uint8Array(4);new DataView(r.buffer).setUint32(0,e.get()),t.value=r}}),t=this}}function Hs(n){if(typeof TextEncoder>"u"){n=unescape(encodeURIComponent(n));const t=new Uint8Array(n.length);for(let e=0;e<t.length;e++)t[e]=n.charCodeAt(e);return t}else return new TextEncoder().encode(n)}const V={concat(n,t){if(n.length===0||t.length===0)return n.concat(t);const e=n[n.length-1],r=V.getPartial(e);return r===32?n.concat(t):V._shiftRight(t,r,e|0,n.slice(0,n.length-1))},bitLength(n){const t=n.length;if(t===0)return 0;const e=n[t-1];return(t-1)*32+V.getPartial(e)},clamp(n,t){if(n.length*32<t)return n;n=n.slice(0,Math.ceil(t/32));const e=n.length;return t=t&31,e>0&&t&&(n[e-1]=V.partial(t,n[e-1]&2147483648>>t-1,1)),n},partial(n,t,e){return n===32?t:(e?t|0:t<<32-n)+n*1099511627776},getPartial(n){return Math.round(n/1099511627776)||32},_shiftRight(n,t,e,r){for(r===void 0&&(r=[]);t>=32;t-=32)r.push(e),e=0;if(t===0)return r.concat(n);for(let a=0;a<n.length;a++)r.push(e|n[a]>>>t),e=n[a]<<32-t;const s=n.length?n[n.length-1]:0,i=V.getPartial(s);return r.push(V.partial(t+i&31,t+i>32?e:r.pop(),1)),r}},Me={bytes:{fromBits(n){const e=V.bitLength(n)/8,r=new Uint8Array(e);let s;for(let i=0;i<e;i++)i&3||(s=n[i/4]),r[i]=s>>>24,s<<=8;return r},toBits(n){const t=[];let e,r=0;for(e=0;e<n.length;e++)r=r<<8|n[e],(e&3)===3&&(t.push(r),r=0);return e&3&&t.push(V.partial(8*(e&3),r)),t}}},Nn={};Nn.sha1=class{constructor(n){const t=this;t.blockSize=512,t._init=[1732584193,4023233417,2562383102,271733878,3285377520],t._key=[1518500249,1859775393,2400959708,3395469782],n?(t._h=n._h.slice(0),t._buffer=n._buffer.slice(0),t._length=n._length):t.reset()}reset(){const n=this;return n._h=n._init.slice(0),n._buffer=[],n._length=0,n}update(n){const t=this;typeof n=="string"&&(n=Me.utf8String.toBits(n));const e=t._buffer=V.concat(t._buffer,n),r=t._length,s=t._length=r+V.bitLength(n);if(s>9007199254740991)throw new Error("Cannot hash more than 2^53 - 1 bits");const i=new Uint32Array(e);let a=0;for(let o=t.blockSize+r-(t.blockSize+r&t.blockSize-1);o<=s;o+=t.blockSize)t._block(i.subarray(16*a,16*(a+1))),a+=1;return e.splice(0,16*a),t}finalize(){const n=this;let t=n._buffer;const e=n._h;t=V.concat(t,[V.partial(1,1)]);for(let r=t.length+2;r&15;r++)t.push(0);for(t.push(Math.floor(n._length/4294967296)),t.push(n._length|0);t.length;)n._block(t.splice(0,16));return n.reset(),e}_f(n,t,e,r){if(n<=19)return t&e|~t&r;if(n<=39)return t^e^r;if(n<=59)return t&e|t&r|e&r;if(n<=79)return t^e^r}_S(n,t){return t<<n|t>>>32-n}_block(n){const t=this,e=t._h,r=Array(80);for(let h=0;h<16;h++)r[h]=n[h];let s=e[0],i=e[1],a=e[2],o=e[3],f=e[4];for(let h=0;h<=79;h++){h>=16&&(r[h]=t._S(1,r[h-3]^r[h-8]^r[h-14]^r[h-16]));const u=t._S(5,s)+t._f(h,i,a,o)+f+r[h]+t._key[Math.floor(h/20)]|0;f=o,o=a,a=t._S(30,i),i=s,s=u}e[0]=e[0]+s|0,e[1]=e[1]+i|0,e[2]=e[2]+a|0,e[3]=e[3]+o|0,e[4]=e[4]+f|0}};const Fn={};Fn.aes=class{constructor(n){const t=this;t._tables=[[[],[],[],[],[]],[[],[],[],[],[]]],t._tables[0][0][0]||t._precompute();const e=t._tables[0][4],r=t._tables[1],s=n.length;let i,a,o,f=1;if(s!==4&&s!==6&&s!==8)throw new Error("invalid aes key size");for(t._key=[a=n.slice(0),o=[]],i=s;i<4*s+28;i++){let h=a[i-1];(i%s===0||s===8&&i%s===4)&&(h=e[h>>>24]<<24^e[h>>16&255]<<16^e[h>>8&255]<<8^e[h&255],i%s===0&&(h=h<<8^h>>>24^f<<24,f=f<<1^(f>>7)*283)),a[i]=a[i-s]^h}for(let h=0;i;h++,i--){const u=a[h&3?i:i-4];i<=4||h<4?o[h]=u:o[h]=r[0][e[u>>>24]]^r[1][e[u>>16&255]]^r[2][e[u>>8&255]]^r[3][e[u&255]]}}encrypt(n){return this._crypt(n,0)}decrypt(n){return this._crypt(n,1)}_precompute(){const n=this._tables[0],t=this._tables[1],e=n[4],r=t[4],s=[],i=[];let a,o,f,h;for(let u=0;u<256;u++)i[(s[u]=u<<1^(u>>7)*283)^u]=u;for(let u=a=0;!e[u];u^=o||1,a=i[a]||1){let y=a^a<<1^a<<2^a<<3^a<<4;y=y>>8^y&255^99,e[u]=y,r[y]=u,h=s[f=s[o=s[u]]];let b=h*16843009^f*65537^o*257^u*16843008,R=s[y]*257^y*16843008;for(let C=0;C<4;C++)n[C][u]=R=R<<24^R>>>8,t[C][y]=b=b<<24^b>>>8}for(let u=0;u<5;u++)n[u]=n[u].slice(0),t[u]=t[u].slice(0)}_crypt(n,t){if(n.length!==4)throw new Error("invalid aes block size");const e=this._key[t],r=e.length/4-2,s=[0,0,0,0],i=this._tables[t],a=i[0],o=i[1],f=i[2],h=i[3],u=i[4];let y=n[0]^e[0],b=n[t?3:1]^e[1],R=n[2]^e[2],C=n[t?1:3]^e[3],l=4,c,d,p;for(let w=0;w<r;w++)c=a[y>>>24]^o[b>>16&255]^f[R>>8&255]^h[C&255]^e[l],d=a[b>>>24]^o[R>>16&255]^f[C>>8&255]^h[y&255]^e[l+1],p=a[R>>>24]^o[C>>16&255]^f[y>>8&255]^h[b&255]^e[l+2],C=a[C>>>24]^o[y>>16&255]^f[b>>8&255]^h[R&255]^e[l+3],l+=4,y=c,b=d,R=p;for(let w=0;w<4;w++)s[t?3&-w:w]=u[y>>>24]<<24^u[b>>16&255]<<16^u[R>>8&255]<<8^u[C&255]^e[l++],c=y,y=b,b=R,R=C,C=c;return s}};const Bs={getRandomValues(n){const t=new Uint32Array(n.buffer),e=r=>{let s=987654321;const i=4294967295;return function(){return s=36969*(s&65535)+(s>>16)&i,r=18e3*(r&65535)+(r>>16)&i,(((s<<16)+r&i)/4294967296+.5)*(Math.random()>.5?1:-1)}};for(let r=0,s;r<n.length;r+=4){const i=e((s||Math.random())*4294967296);s=i()*987654071,t[r/4]=i()*4294967296|0}return n}},Un={};Un.ctrGladman=class{constructor(n,t){this._prf=n,this._initIv=t,this._iv=t}reset(){this._iv=this._initIv}update(n){return this.calculate(this._prf,n,this._iv)}incWord(n){if((n>>24&255)===255){let t=n>>16&255,e=n>>8&255,r=n&255;t===255?(t=0,e===255?(e=0,r===255?r=0:++r):++e):++t,n=0,n+=t<<16,n+=e<<8,n+=r}else n+=1<<24;return n}incCounter(n){(n[0]=this.incWord(n[0]))===0&&(n[1]=this.incWord(n[1]))}calculate(n,t,e){let r;if(!(r=t.length))return[];const s=V.bitLength(t);for(let i=0;i<r;i+=4){this.incCounter(e);const a=n.encrypt(e);t[i]^=a[0],t[i+1]^=a[1],t[i+2]^=a[2],t[i+3]^=a[3]}return V.clamp(t,s)}};const _e={importKey(n){return new _e.hmacSha1(Me.bytes.toBits(n))},pbkdf2(n,t,e,r){if(e=e||1e4,r<0||e<0)throw new Error("invalid params to pbkdf2");const s=(r>>5)+1<<2;let i,a,o,f,h;const u=new ArrayBuffer(s),y=new DataView(u);let b=0;const R=V;for(t=Me.bytes.toBits(t),h=1;b<(s||1);h++){for(i=a=n.encrypt(R.concat(t,[h])),o=1;o<e;o++)for(a=n.encrypt(a),f=0;f<a.length;f++)i[f]^=a[f];for(o=0;b<(s||1)&&o<i.length;o++)y.setInt32(b,i[o]),b+=4}return u.slice(0,r/8)}};_e.hmacSha1=class{constructor(n){const t=this,e=t._hash=Nn.sha1,r=[[],[]];t._baseHash=[new e,new e];const s=t._baseHash[0].blockSize/32;n.length>s&&(n=new e().update(n).finalize());for(let i=0;i<s;i++)r[0][i]=n[i]^909522486,r[1][i]=n[i]^1549556828;t._baseHash[0].update(r[0]),t._baseHash[1].update(r[1]),t._resultHash=new e(t._baseHash[0])}reset(){const n=this;n._resultHash=new n._hash(n._baseHash[0]),n._updated=!1}update(n){const t=this;t._updated=!0,t._resultHash.update(n)}digest(){const n=this,t=n._resultHash.finalize(),e=new n._hash(n._baseHash[1]).update(t).finalize();return n.reset(),e}encrypt(n){if(this._updated)throw new Error("encrypt on already updated hmac called!");return this.update(n),this.digest(n)}};const js=typeof crypto<"u"&&typeof crypto.getRandomValues=="function",dt="Invalid password",ht="Invalid signature",_t="zipjs-abort-check-password";function Mn(n){return js?crypto.getRandomValues(n):Bs.getRandomValues(n)}const we=16,qs="raw",Wn={name:"PBKDF2"},Gs={name:"HMAC"},Vs="SHA-1",Ys=Object.assign({hash:Gs},Wn),tt=Object.assign({iterations:1e3,hash:{name:Vs}},Wn),Zs=["deriveBits"],Te=[8,12,16],xe=[16,24,32],ie=10,Ks=[0,0,0,0],Hn="undefined",Bn="function",He=typeof crypto!=Hn,Oe=He&&crypto.subtle,jn=He&&typeof Oe!=Hn,J=Me.bytes,Xs=Fn.aes,$s=Un.ctrGladman,Js=_e.hmacSha1;let an=He&&jn&&typeof Oe.importKey==Bn,on=He&&jn&&typeof Oe.deriveBits==Bn;class Qs extends TransformStream{constructor({password:t,signed:e,encryptionStrength:r,checkPasswordOnly:s}){super({start(){Object.assign(this,{ready:new Promise(i=>this.resolveReady=i),password:t,signed:e,strength:r-1,pending:new Uint8Array})},async transform(i,a){const o=this,{password:f,strength:h,resolveReady:u,ready:y}=o;f?(await ei(o,h,f,X(i,0,Te[h]+2)),i=X(i,Te[h]+2),s?a.error(new Error(_t)):u()):await y;const b=new Uint8Array(i.length-ie-(i.length-ie)%we);a.enqueue(qn(o,i,b,0,ie,!0))},async flush(i){const{signed:a,ctr:o,hmac:f,pending:h,ready:u}=this;if(f&&o){await u;const y=X(h,0,h.length-ie),b=X(h,h.length-ie);let R=new Uint8Array;if(y.length){const C=Re(J,y);f.update(C);const l=o.update(C);R=Se(J,l)}if(a){const C=X(Se(J,f.digest()),0,ie);for(let l=0;l<ie;l++)if(C[l]!=b[l])throw new Error(ht)}i.enqueue(R)}}})}}class zs extends TransformStream{constructor({password:t,encryptionStrength:e}){let r;super({start(){Object.assign(this,{ready:new Promise(s=>this.resolveReady=s),password:t,strength:e-1,pending:new Uint8Array})},async transform(s,i){const a=this,{password:o,strength:f,resolveReady:h,ready:u}=a;let y=new Uint8Array;o?(y=await ti(a,f,o),h()):await u;const b=new Uint8Array(y.length+s.length-s.length%we);b.set(y,0),i.enqueue(qn(a,s,b,y.length,0))},async flush(s){const{ctr:i,hmac:a,pending:o,ready:f}=this;if(a&&i){await f;let h=new Uint8Array;if(o.length){const u=i.update(Re(J,o));a.update(u),h=Se(J,u)}r.signature=Se(J,a.digest()).slice(0,ie),s.enqueue(pt(h,r.signature))}}}),r=this}}function qn(n,t,e,r,s,i){const{ctr:a,hmac:o,pending:f}=n,h=t.length-s;f.length&&(t=pt(f,t),e=si(e,h-h%we));let u;for(u=0;u<=h-we;u+=we){const y=Re(J,X(t,u,u+we));i&&o.update(y);const b=a.update(y);i||o.update(b),e.set(Se(J,b),u+r)}return n.pending=X(t,u),e}async function ei(n,t,e,r){const s=await Gn(n,t,e,X(r,0,Te[t])),i=X(r,Te[t]);if(s[0]!=i[0]||s[1]!=i[1])throw new Error(dt)}async function ti(n,t,e){const r=Mn(new Uint8Array(Te[t])),s=await Gn(n,t,e,r);return pt(r,s)}async function Gn(n,t,e,r){n.password=null;const s=Hs(e),i=await ni(qs,s,Ys,!1,Zs),a=await ri(Object.assign({salt:r},tt),i,8*(xe[t]*2+2)),o=new Uint8Array(a),f=Re(J,X(o,0,xe[t])),h=Re(J,X(o,xe[t],xe[t]*2)),u=X(o,xe[t]*2);return Object.assign(n,{keys:{key:f,authentication:h,passwordVerification:u},ctr:new $s(new Xs(f),Array.from(Ks)),hmac:new Js(h)}),u}async function ni(n,t,e,r,s){if(an)try{return await Oe.importKey(n,t,e,r,s)}catch{return an=!1,_e.importKey(t)}else return _e.importKey(t)}async function ri(n,t,e){if(on)try{return await Oe.deriveBits(n,t,e)}catch{return on=!1,_e.pbkdf2(t,n.salt,tt.iterations,e)}else return _e.pbkdf2(t,n.salt,tt.iterations,e)}function pt(n,t){let e=n;return n.length+t.length&&(e=new Uint8Array(n.length+t.length),e.set(n,0),e.set(t,n.length)),e}function si(n,t){if(t&&t>n.length){const e=n;n=new Uint8Array(t),n.set(e,0)}return n}function X(n,t,e){return n.subarray(t,e)}function Se(n,t){return n.fromBits(t)}function Re(n,t){return n.toBits(t)}const ge=12;class ii extends TransformStream{constructor({password:t,passwordVerification:e,checkPasswordOnly:r}){super({start(){Object.assign(this,{password:t,passwordVerification:e}),Vn(this,t)},transform(s,i){const a=this;if(a.password){const o=cn(a,s.subarray(0,ge));if(a.password=null,o[ge-1]!=a.passwordVerification)throw new Error(dt);s=s.subarray(ge)}r?i.error(new Error(_t)):i.enqueue(cn(a,s))}})}}class ai extends TransformStream{constructor({password:t,passwordVerification:e}){super({start(){Object.assign(this,{password:t,passwordVerification:e}),Vn(this,t)},transform(r,s){const i=this;let a,o;if(i.password){i.password=null;const f=Mn(new Uint8Array(ge));f[ge-1]=i.passwordVerification,a=new Uint8Array(r.length+f.length),a.set(ln(i,f),0),o=ge}else a=new Uint8Array(r.length),o=0;a.set(ln(i,r),o),s.enqueue(a)}})}}function cn(n,t){const e=new Uint8Array(t.length);for(let r=0;r<t.length;r++)e[r]=Yn(n)^t[r],mt(n,e[r]);return e}function ln(n,t){const e=new Uint8Array(t.length);for(let r=0;r<t.length;r++)e[r]=Yn(n)^t[r],mt(n,t[r]);return e}function Vn(n,t){const e=[305419896,591751049,878082192];Object.assign(n,{keys:e,crcKey0:new Ue(e[0]),crcKey2:new Ue(e[2])});for(let r=0;r<t.length;r++)mt(n,t.charCodeAt(r))}function mt(n,t){let[e,r,s]=n.keys;n.crcKey0.append([t]),e=~n.crcKey0.get(),r=fn(Math.imul(fn(r+Zn(e)),134775813)+1),n.crcKey2.append([r>>>24]),s=~n.crcKey2.get(),n.keys=[e,r,s]}function Yn(n){const t=n.keys[2]|2;return Zn(Math.imul(t,t^1)>>>8)}function Zn(n){return n&255}function fn(n){return n&4294967295}const un="deflate-raw";class oi extends TransformStream{constructor(t,{chunkSize:e,CompressionStream:r,CompressionStreamNative:s}){super({});const{compressed:i,encrypted:a,useCompressionStream:o,zipCrypto:f,signed:h,level:u}=t,y=this;let b,R,C=Kn(super.readable);(!a||f)&&h&&(b=new Ln,C=Q(C,b)),i&&(C=$n(C,o,{level:u,chunkSize:e},s,r)),a&&(f?C=Q(C,new ai(t)):(R=new zs(t),C=Q(C,R))),Xn(y,C,()=>{let l;a&&!f&&(l=R.signature),(!a||f)&&h&&(l=new DataView(b.value.buffer).getUint32(0)),y.signature=l})}}class ci extends TransformStream{constructor(t,{chunkSize:e,DecompressionStream:r,DecompressionStreamNative:s}){super({});const{zipCrypto:i,encrypted:a,signed:o,signature:f,compressed:h,useCompressionStream:u}=t;let y,b,R=Kn(super.readable);a&&(i?R=Q(R,new ii(t)):(b=new Qs(t),R=Q(R,b))),h&&(R=$n(R,u,{chunkSize:e},s,r)),(!a||i)&&o&&(y=new Ln,R=Q(R,y)),Xn(this,R,()=>{if((!a||i)&&o){const C=new DataView(y.value.buffer);if(f!=C.getUint32(0,!1))throw new Error(ht)}})}}function Kn(n){return Q(n,new TransformStream({transform(t,e){t&&t.length&&e.enqueue(t)}}))}function Xn(n,t,e){t=Q(t,new TransformStream({flush:e})),Object.defineProperty(n,"readable",{get(){return t}})}function $n(n,t,e,r,s){try{const i=t&&r?r:s;n=Q(n,new i(un,e))}catch(i){if(t)n=Q(n,new s(un,e));else throw i}return n}function Q(n,t){return n.pipeThrough(t)}const li="message",fi="start",ui="pull",dn="data",di="ack",hi="close",_i="deflate",Jn="inflate";class pi extends TransformStream{constructor(t,e){super({});const r=this,{codecType:s}=t;let i;s.startsWith(_i)?i=oi:s.startsWith(Jn)&&(i=ci);let a=0;const o=new i(t,e),f=super.readable,h=new TransformStream({transform(u,y){u&&u.length&&(a+=u.length,y.enqueue(u))},flush(){const{signature:u}=o;Object.assign(r,{signature:u,size:a})}});Object.defineProperty(r,"readable",{get(){return f.pipeThrough(o).pipeThrough(h)}})}}const mi=typeof Worker!=Fe;class Ke{constructor(t,{readable:e,writable:r},{options:s,config:i,streamOptions:a,useWebWorkers:o,transferStreams:f,scripts:h},u){const{signal:y}=a;return Object.assign(t,{busy:!0,readable:e.pipeThrough(new wi(e,a,i),{signal:y}),writable:r,options:Object.assign({},s),scripts:h,transferStreams:f,terminate(){const{worker:b,busy:R}=t;b&&!R&&(b.terminate(),t.interface=null)},onTaskFinished(){t.busy=!1,u(t)}}),(o&&mi?bi:gi)(t,i)}}class wi extends TransformStream{constructor(t,{onstart:e,onprogress:r,size:s,onend:i},{chunkSize:a}){let o=0;super({start(){e&&Xe(e,s)},async transform(f,h){o+=f.length,r&&await Xe(r,o,s),h.enqueue(f)},flush(){t.size=o,i&&Xe(i,o)}},{highWaterMark:1,size:()=>a})}}async function Xe(n,...t){try{await n(...t)}catch{}}function gi(n,t){return{run:()=>yi(n,t)}}function bi(n,{baseURL:t,chunkSize:e}){return n.interface||Object.assign(n,{worker:Ti(n.scripts[0],t,n),interface:{run:()=>xi(n,{chunkSize:e})}}),n.interface}async function yi({options:n,readable:t,writable:e,onTaskFinished:r},s){const i=new pi(n,s);try{await t.pipeThrough(i).pipeTo(e,{preventClose:!0,preventAbort:!0});const{signature:a,size:o}=i;return{signature:a,size:o}}finally{r()}}async function xi(n,t){let e,r;const s=new Promise((b,R)=>{e=b,r=R});Object.assign(n,{reader:null,writer:null,resolveResult:e,rejectResult:r,result:s});const{readable:i,options:a,scripts:o}=n,{writable:f,closed:h}=Ei(n.writable);nt({type:fi,scripts:o.slice(1),options:a,config:t,readable:i,writable:f},n)||Object.assign(n,{reader:i.getReader(),writer:f.getWriter()});const y=await s;try{await f.getWriter().close()}catch{}return await h,y}function Ei(n){const t=n.getWriter();let e;const r=new Promise(i=>e=i);return{writable:new WritableStream({async write(i){await t.ready,await t.write(i)},close(){t.releaseLock(),e()},abort(i){return t.abort(i)}}),closed:r}}let hn=!0,_n=!0;function Ti(n,t,e){const r={type:"module"};let s,i;typeof n==kn&&(n=n());try{s=new URL(n,t)}catch{s=n}if(hn)try{i=new Worker(s)}catch{hn=!1,i=new Worker(s,r)}else i=new Worker(s,r);return i.addEventListener(li,a=>Si(a,e)),i}function nt(n,{worker:t,writer:e,onTaskFinished:r,transferStreams:s}){try{let{value:i,readable:a,writable:o}=n;const f=[];if(i&&(i.byteLength<i.buffer.byteLength?n.value=i.buffer.slice(0,i.byteLength):n.value=i.buffer,f.push(n.value)),s&&_n?(a&&f.push(a),o&&f.push(o)):n.readable=n.writable=null,f.length)try{return t.postMessage(n,f),!0}catch{_n=!1,n.readable=n.writable=null,t.postMessage(n)}else t.postMessage(n)}catch(i){throw e&&e.releaseLock(),r(),i}}async function Si({data:n},t){const{type:e,value:r,messageId:s,result:i,error:a}=n,{reader:o,writer:f,resolveResult:h,rejectResult:u,onTaskFinished:y}=t;try{if(a){const{message:R,stack:C,code:l,name:c}=a,d=new Error(R);Object.assign(d,{stack:C,code:l,name:c}),b(d)}else{if(e==ui){const{value:R,done:C}=await o.read();nt({type:dn,value:R,done:C,messageId:s},t)}e==dn&&(await f.ready,await f.write(new Uint8Array(r)),nt({type:di,messageId:s},t)),e==hi&&b(null,i)}}catch(R){b(R)}function b(R,C){R?u(R):h(C),f&&f.releaseLock(),y()}}let ae=[];const $e=[];let pn=0;async function Ri(n,t){const{options:e,config:r}=t,{transferStreams:s,useWebWorkers:i,useCompressionStream:a,codecType:o,compressed:f,signed:h,encrypted:u}=e,{workerScripts:y,maxWorkers:b,terminateWorkerTimeout:R}=r;t.transferStreams=s||s===$;const C=!f&&!h&&!u&&!t.transferStreams;t.useWebWorkers=!C&&(i||i===$&&r.useWebWorkers),t.scripts=t.useWebWorkers&&y?y[o]:[],e.useCompressionStream=a||a===$&&r.useCompressionStream;let l;const c=ae.find(p=>!p.busy);if(c)rt(c),l=new Ke(c,n,t,d);else if(ae.length<b){const p={indexWorker:pn};pn++,ae.push(p),l=new Ke(p,n,t,d)}else l=await new Promise(p=>$e.push({resolve:p,stream:n,workerOptions:t}));return l.run();function d(p){if($e.length){const[{resolve:w,stream:g,workerOptions:T}]=$e.splice(0,1);w(new Ke(p,g,T,d))}else p.worker?(rt(p),Number.isFinite(R)&&R>=0&&(p.terminateTimeout=setTimeout(()=>{ae=ae.filter(w=>w!=p),p.terminate()},R))):ae=ae.filter(w=>w!=p)}}function rt(n){const{terminateTimeout:t}=n;t&&(clearTimeout(t),n.terminateTimeout=null)}function Ai(){ae.forEach(n=>{rt(n),n.terminate()})}const Qn="HTTP error ",ke="HTTP Range not supported",zn="Writer iterator completed too soon",Ci="text/plain",Oi="Content-Length",ki="Content-Range",Di="Accept-Ranges",Ii="Range",Pi="Content-Type",vi="HEAD",wt="GET",er="bytes",Li=64*1024,gt="writable";class Be{constructor(){this.size=0}init(){this.initialized=!0}}class fe extends Be{get readable(){const t=this,{chunkSize:e=Li}=t,r=new ReadableStream({start(){this.chunkOffset=0},async pull(s){const{offset:i=0,size:a,diskNumberStart:o}=r,{chunkOffset:f}=this;s.enqueue(await G(t,i+f,Math.min(e,a-f),o)),f+e>a?s.close():this.chunkOffset+=e}});return r}}class bt extends Be{constructor(){super();const t=this,e=new WritableStream({write(r){return t.writeUint8Array(r)}});Object.defineProperty(t,gt,{get(){return e}})}writeUint8Array(){}}class Ni extends fe{constructor(t){super();let e=t.length;for(;t.charAt(e-1)=="=";)e--;const r=t.indexOf(",")+1;Object.assign(this,{dataURI:t,dataStart:r,size:Math.floor((e-r)*.75)})}readUint8Array(t,e){const{dataStart:r,dataURI:s}=this,i=new Uint8Array(e),a=Math.floor(t/3)*4,o=atob(s.substring(a+r,Math.ceil((t+e)/3)*4+r)),f=t-Math.floor(a/4)*3;for(let h=f;h<f+e;h++)i[h-f]=o.charCodeAt(h);return i}}class Fi extends bt{constructor(t){super(),Object.assign(this,{data:"data:"+(t||"")+";base64,",pending:[]})}writeUint8Array(t){const e=this;let r=0,s=e.pending;const i=e.pending.length;for(e.pending="",r=0;r<Math.floor((i+t.length)/3)*3-i;r++)s+=String.fromCharCode(t[r]);for(;r<t.length;r++)e.pending+=String.fromCharCode(t[r]);s.length>2?e.data+=btoa(s):e.pending=s}getData(){return this.data+btoa(this.pending)}}class yt extends fe{constructor(t){super(),Object.assign(this,{blob:t,size:t.size})}async readUint8Array(t,e){const r=this,s=t+e;let a=await(t||s<r.size?r.blob.slice(t,s):r.blob).arrayBuffer();return a.byteLength>e&&(a=a.slice(t,s)),new Uint8Array(a)}}class tr extends Be{constructor(t){super();const e=this,r=new TransformStream,s=[];t&&s.push([Pi,t]),Object.defineProperty(e,gt,{get(){return r.writable}}),e.blob=new Response(r.readable,{headers:s}).blob()}getData(){return this.blob}}class Ui extends yt{constructor(t){super(new Blob([t],{type:Ci}))}}class Mi extends tr{constructor(t){super(t),Object.assign(this,{encoding:t,utf8:!t||t.toLowerCase()=="utf-8"})}async getData(){const{encoding:t,utf8:e}=this,r=await super.getData();if(r.text&&e)return r.text();{const s=new FileReader;return new Promise((i,a)=>{Object.assign(s,{onload:({target:o})=>i(o.result),onerror:()=>a(s.error)}),s.readAsText(r,t)})}}}class Wi extends fe{constructor(t,e){super(),nr(this,t,e)}async init(){await rr(this,st,mn),super.init()}readUint8Array(t,e){return sr(this,t,e,st,mn)}}class Hi extends fe{constructor(t,e){super(),nr(this,t,e)}async init(){await rr(this,it,wn),super.init()}readUint8Array(t,e){return sr(this,t,e,it,wn)}}function nr(n,t,e){const{preventHeadRequest:r,useRangeHeader:s,forceRangeRequests:i}=e;e=Object.assign({},e),delete e.preventHeadRequest,delete e.useRangeHeader,delete e.forceRangeRequests,delete e.useXHR,Object.assign(n,{url:t,options:e,preventHeadRequest:r,useRangeHeader:s,forceRangeRequests:i})}async function rr(n,t,e){const{url:r,useRangeHeader:s,forceRangeRequests:i}=n;if(Gi(r)&&(s||i)){const{headers:a}=await t(wt,n,ir(n));if(!i&&a.get(Di)!=er)throw new Error(ke);{let o;const f=a.get(ki);if(f){const h=f.trim().split(/\s*\/\s*/);if(h.length){const u=h[1];u&&u!="*"&&(o=Number(u))}}o===$?await gn(n,t,e):n.size=o}}else await gn(n,t,e)}async function sr(n,t,e,r,s){const{useRangeHeader:i,forceRangeRequests:a,options:o}=n;if(i||a){const f=await r(wt,n,ir(n,t,e));if(f.status!=206)throw new Error(ke);return new Uint8Array(await f.arrayBuffer())}else{const{data:f}=n;return f||await s(n,o),new Uint8Array(n.data.subarray(t,t+e))}}function ir(n,t=0,e=1){return Object.assign({},xt(n),{[Ii]:er+"="+t+"-"+(t+e-1)})}function xt({options:n}){const{headers:t}=n;if(t)return Symbol.iterator in t?Object.fromEntries(t):t}async function mn(n){await ar(n,st)}async function wn(n){await ar(n,it)}async function ar(n,t){const e=await t(wt,n,xt(n));n.data=new Uint8Array(await e.arrayBuffer()),n.size||(n.size=n.data.length)}async function gn(n,t,e){if(n.preventHeadRequest)await e(n,n.options);else{const s=(await t(vi,n,xt(n))).headers.get(Oi);s?n.size=Number(s):await e(n,n.options)}}async function st(n,{options:t,url:e},r){const s=await fetch(e,Object.assign({},t,{method:n,headers:r}));if(s.status<400)return s;throw s.status==416?new Error(ke):new Error(Qn+(s.statusText||s.status))}function it(n,{url:t},e){return new Promise((r,s)=>{const i=new XMLHttpRequest;if(i.addEventListener("load",()=>{if(i.status<400){const a=[];i.getAllResponseHeaders().trim().split(/[\r\n]+/).forEach(o=>{const f=o.trim().split(/\s*:\s*/);f[0]=f[0].trim().replace(/^[a-z]|-[a-z]/g,h=>h.toUpperCase()),a.push(f)}),r({status:i.status,arrayBuffer:()=>i.response,headers:new Map(a)})}else s(i.status==416?new Error(ke):new Error(Qn+(i.statusText||i.status)))},!1),i.addEventListener("error",a=>s(a.detail?a.detail.error:new Error("Network error")),!1),i.open(n,t),e)for(const a of Object.entries(e))i.setRequestHeader(a[0],a[1]);i.responseType="arraybuffer",i.send()})}class or extends fe{constructor(t,e={}){super(),Object.assign(this,{url:t,reader:e.useXHR?new Hi(t,e):new Wi(t,e)})}set size(t){}get size(){return this.reader.size}async init(){await this.reader.init(),super.init()}readUint8Array(t,e){return this.reader.readUint8Array(t,e)}}class Bi extends or{constructor(t,e={}){e.useRangeHeader=!0,super(t,e)}}class ji extends fe{constructor(t){super(),Object.assign(this,{array:t,size:t.length})}readUint8Array(t,e){return this.array.slice(t,t+e)}}class qi extends bt{init(t=0){Object.assign(this,{offset:0,array:new Uint8Array(t)}),super.init()}writeUint8Array(t){const e=this;if(e.offset+t.length>e.array.length){const r=e.array;e.array=new Uint8Array(r.length+t.length),e.array.set(r)}e.array.set(t,e.offset),e.offset+=t.length}getData(){return this.array}}class Et extends fe{constructor(t){super(),this.readers=t}async init(){const t=this,{readers:e}=t;t.lastDiskNumber=0,t.lastDiskOffset=0,await Promise.all(e.map(async(r,s)=>{await r.init(),s!=e.length-1&&(t.lastDiskOffset+=r.size),t.size+=r.size})),super.init()}async readUint8Array(t,e,r=0){const s=this,{readers:i}=this;let a,o=r;o==-1&&(o=i.length-1);let f=t;for(;f>=i[o].size;)f-=i[o].size,o++;const h=i[o],u=h.size;if(f+e<=u)a=await G(h,f,e);else{const y=u-f;a=new Uint8Array(e),a.set(await G(h,f,y)),a.set(await s.readUint8Array(t+y,e-y,r),y)}return s.lastDiskNumber=Math.max(o,s.lastDiskNumber),a}}class We extends Be{constructor(t,e=4294967295){super();const r=this;Object.assign(r,{diskNumber:0,diskOffset:0,size:0,maxSize:e,availableSize:e});let s,i,a;const o=new WritableStream({async write(u){const{availableSize:y}=r;if(a)u.length>=y?(await f(u.slice(0,y)),await h(),r.diskOffset+=s.size,r.diskNumber++,a=null,await this.write(u.slice(y))):await f(u);else{const{value:b,done:R}=await t.next();if(R&&!b)throw new Error(zn);s=b,s.size=0,s.maxSize&&(r.maxSize=s.maxSize),r.availableSize=r.maxSize,await Ae(s),i=b.writable,a=i.getWriter(),await this.write(u)}},async close(){await a.ready,await h()}});Object.defineProperty(r,gt,{get(){return o}});async function f(u){const y=u.length;y&&(await a.ready,await a.write(u),s.size+=y,r.size+=y,r.availableSize-=y)}async function h(){i.size=s.size,await a.close()}}}function Gi(n){const{baseURL:t}=In(),{protocol:e}=new URL(n,t);return e=="http:"||e=="https:"}async function Ae(n,t){n.init&&!n.initialized&&await n.init(t)}function cr(n){return Array.isArray(n)&&(n=new Et(n)),n instanceof ReadableStream&&(n={readable:n}),n}function lr(n){n.writable===$&&typeof n.next==kn&&(n=new We(n)),n instanceof WritableStream&&(n={writable:n});const{writable:t}=n;return t.size===$&&(t.size=0),n instanceof We||Object.assign(n,{diskNumber:0,diskOffset:0,availableSize:1/0,maxSize:1/0}),n}function G(n,t,e,r){return n.readUint8Array(t,e,r)}const Vi=Et,Yi=We,fr="\0☺☻♥♦♣♠•◘○◙♂♀♪♫☼►◄↕‼¶§▬↨↑↓→←∟↔▲▼ !\"#$%&'()*+,-./0123456789:;<=>?@ABCDEFGHIJKLMNOPQRSTUVWXYZ[\\]^_`abcdefghijklmnopqrstuvwxyz{|}~⌂ÇüéâäàåçêëèïîìÄÅÉæÆôöòûùÿÖÜ¢£¥₧ƒáíóúñÑªº¿⌐¬½¼¡«»░▒▓│┤╡╢╖╕╣║╗╝╜╛┐└┴┬├─┼╞╟╚╔╩╦╠═╬╧╨╤╥╙╘╒╓╫╪┘┌█▄▌▐▀αßΓπΣσµτΦΘΩδ∞φε∩≡±≥≤⌠⌡÷≈°∙·√ⁿ²■ ".split(""),Zi=fr.length==256;function Ki(n){if(Zi){let t="";for(let e=0;e<n.length;e++)t+=fr[n[e]];return t}else return new TextDecoder().decode(n)}function at(n,t){return t&&t.trim().toLowerCase()=="cp437"?Ki(n):new TextDecoder(t).decode(n)}const ur="filename",dr="rawFilename",hr="comment",_r="rawComment",pr="uncompressedSize",mr="compressedSize",wr="offset",ot="diskNumberStart",ct="lastModDate",lt="rawLastModDate",gr="lastAccessDate",Xi="rawLastAccessDate",br="creationDate",$i="rawCreationDate",Ji="internalFileAttribute",Qi="externalFileAttribute",zi="msDosCompatible",ea="zip64",ta=[ur,dr,mr,pr,ct,lt,hr,_r,gr,br,wr,ot,ot,Ji,Qi,zi,ea,"directory","bitFlag","encrypted","signature","filenameUTF8","commentUTF8","compressionMethod","version","versionMadeBy","extraField","rawExtraField","extraFieldZip64","extraFieldUnicodePath","extraFieldUnicodeComment","extraFieldAES","extraFieldNTFS","extraFieldExtendedTimestamp"];class bn{constructor(t){ta.forEach(e=>this[e]=t[e])}}const Le="File format is not recognized",yr="End of central directory not found",xr="End of Zip64 central directory not found",Er="End of Zip64 central directory locator not found",Tr="Central directory header not found",Sr="Local file header not found",Rr="Zip64 extra field not found",Ar="File contains encrypted entry",Cr="Encryption method not supported",ft="Compression method not supported",ut="Split zip file",yn="utf-8",xn="cp437",na=[[pr,he],[mr,he],[wr,he],[ot,oe]],ra={[oe]:{getValue:B,bytes:4},[he]:{getValue:Ne,bytes:8}};class sa{constructor(t,e={}){Object.assign(this,{reader:cr(t),options:e,config:In()})}async*getEntriesGenerator(t={}){const e=this;let{reader:r}=e;const{config:s}=e;if(await Ae(r),(r.size===$||!r.readUint8Array)&&(r=new yt(await new Response(r.readable).blob()),await Ae(r)),r.size<me)throw new Error(Le);r.chunkSize=Ms(s);const i=await fa(r,Ss,r.size,me,oe*16);if(!i){const x=await G(r,0,4),m=q(x);throw B(m)==Ts?new Error(ut):new Error(yr)}const a=q(i);let o=B(a,12),f=B(a,16);const h=i.offset,u=j(a,20),y=h+me+u;let b=j(a,4);const R=r.lastDiskNumber||0;let C=j(a,6),l=j(a,8),c=0,d=0;if(f==he||o==he||l==oe||C==oe){const x=await G(r,i.offset-Ye,Ye),m=q(x);if(B(m,0)!=Rs)throw new Error(xr);f=Ne(m,8);let k=await G(r,f,Ze,-1),O=q(k);const v=i.offset-Ye-Ze;if(B(O,0)!=zt&&f!=v){const D=f;f=v,c=f-D,k=await G(r,f,Ze,-1),O=q(k)}if(B(O,0)!=zt)throw new Error(Er);b==oe&&(b=B(O,16)),C==oe&&(C=B(O,20)),l==oe&&(l=Ne(O,32)),o==he&&(o=Ne(O,40)),f-=o}if(f>=r.size&&(c=r.size-f-o-me,f=r.size-o-me),R!=b)throw new Error(ut);if(f<0)throw new Error(Le);let p=0,w=await G(r,f,o,C),g=q(w);if(o){const x=i.offset-o;if(B(g,p)!=Qt&&f!=x){const m=f;f=x,c+=f-m,w=await G(r,f,o,C),g=q(w)}}const T=i.offset-f-(r.lastDiskOffset||0);if(o!=T&&T>=0&&(o=T,w=await G(r,f,o,C),g=q(w)),f<0||f>=r.size)throw new Error(Le);const _=K(e,t,"filenameEncoding"),S=K(e,t,"commentEncoding");for(let x=0;x<l;x++){const m=new ia(r,s,e.options);if(B(g,p)!=Qt)throw new Error(Tr);Or(m,g,p+6);const k=!!m.bitFlag.languageEncodingFlag,O=p+46,v=O+m.filenameLength,D=v+m.extraFieldLength,I=j(g,p+4),U=(I&0)==0,L=w.subarray(O,v),te=j(g,p+32),P=D+te,ue=w.subarray(D,P),M=k,W=k,Tt=U&&(be(g,p+38)&rn)==rn,St=B(g,p+42)+c;Object.assign(m,{versionMadeBy:I,msDosCompatible:U,compressedSize:0,uncompressedSize:0,commentLength:te,directory:Tt,offset:St,diskNumberStart:j(g,p+34),internalFileAttribute:j(g,p+36),externalFileAttribute:B(g,p+38),rawFilename:L,filenameUTF8:M,commentUTF8:W,rawExtraField:w.subarray(v,D)});const[Rt,Ir]=await Promise.all([at(L,M?yn:_||xn),at(ue,W?yn:S||xn)]);Object.assign(m,{rawComment:ue,filename:Rt,comment:Ir,directory:Tt||Rt.endsWith(Ns)}),d=Math.max(St,d),await kr(m,m,g,p+6);const je=new bn(m);je.getData=(Ct,Pr)=>m.getData(Ct,je,Pr),p=P;const{onprogress:At}=t;if(At)try{await At(x+1,l,new bn(m))}catch{}yield je}const E=K(e,t,"extractPrependedData"),A=K(e,t,"extractAppendedData");return E&&(e.prependedData=d>0?await G(r,0,d):new Uint8Array),e.comment=u?await G(r,h+me,u):new Uint8Array,A&&(e.appendedData=y<r.size?await G(r,y,r.size-y):new Uint8Array),!0}async getEntries(t={}){const e=[];for await(const r of this.getEntriesGenerator(t))e.push(r);return e}async close(){}}class ia{constructor(t,e,r){Object.assign(this,{reader:t,config:e,options:r})}async getData(t,e,r={}){const s=this,{reader:i,offset:a,diskNumberStart:o,extraFieldAES:f,compressionMethod:h,config:u,bitFlag:y,signature:b,rawLastModDate:R,uncompressedSize:C,compressedSize:l}=s,c=e.localDirectory={},d=await G(i,a,30,o),p=q(d);let w=K(s,r,"password");if(w=w&&w.length&&w,f&&f.originalCompressionMethod!=xs)throw new Error(ft);if(h!=ys&&h!=bs)throw new Error(ft);if(B(p,0)!=Es)throw new Error(Sr);Or(c,p,4),c.rawExtraField=c.extraFieldLength?await G(i,a+30+c.filenameLength,c.extraFieldLength,o):new Uint8Array,await kr(s,c,p,4,!0),Object.assign(e,{lastAccessDate:c.lastAccessDate,creationDate:c.creationDate});const g=s.encrypted&&c.encrypted,T=g&&!f;if(g){if(!T&&f.strength===$)throw new Error(Cr);if(!w)throw new Error(Ar)}const _=a+30+c.filenameLength+c.extraFieldLength,S=l,E=i.readable;Object.assign(E,{diskNumberStart:o,offset:_,size:S});const A=K(s,r,"signal"),x=K(s,r,"checkPasswordOnly");x&&(t=new WritableStream),t=lr(t),await Ae(t,C);const{writable:m}=t,{onstart:k,onprogress:O,onend:v}=r,D={options:{codecType:Jn,password:w,zipCrypto:T,encryptionStrength:f&&f.strength,signed:K(s,r,"checkSignature"),passwordVerification:T&&(y.dataDescriptor?R>>>8&255:b>>>24&255),signature:b,compressed:h!=0,encrypted:g,useWebWorkers:K(s,r,"useWebWorkers"),useCompressionStream:K(s,r,"useCompressionStream"),transferStreams:K(s,r,"transferStreams"),checkPasswordOnly:x},config:u,streamOptions:{signal:A,size:S,onstart:k,onprogress:O,onend:v}};let I=0;try{({outputSize:I}=await Ri({readable:E,writable:m},D))}catch(U){if(!x||U.message!=_t)throw U}finally{const U=K(s,r,"preventClose");m.size+=I,!U&&!m.locked&&await m.getWriter().close()}return x?void 0:t.getData?t.getData():m}}function Or(n,t,e){const r=n.rawBitFlag=j(t,e+2),s=(r&en)==en,i=B(t,e+6);Object.assign(n,{encrypted:s,version:j(t,e),bitFlag:{level:(r&Ls)>>1,dataDescriptor:(r&tn)==tn,languageEncodingFlag:(r&nn)==nn},rawLastModDate:i,lastModDate:ua(i),filenameLength:j(t,e+22),extraFieldLength:j(t,e+24)})}async function kr(n,t,e,r,s){const{rawExtraField:i}=t,a=t.extraField=new Map,o=q(new Uint8Array(i));let f=0;try{for(;f<i.length;){const d=j(o,f),p=j(o,f+2);a.set(d,{type:d,data:i.slice(f+4,f+4+p)}),f+=4+p}}catch{}const h=j(e,r+4);Object.assign(t,{signature:B(e,r+10),uncompressedSize:B(e,r+18),compressedSize:B(e,r+14)});const u=a.get(As);u&&(aa(u,t),t.extraFieldZip64=u);const y=a.get(Is);y&&(await En(y,ur,dr,t,n),t.extraFieldUnicodePath=y);const b=a.get(Ps);b&&(await En(b,hr,_r,t,n),t.extraFieldUnicodeComment=b);const R=a.get(Cs);R?(oa(R,t,h),t.extraFieldAES=R):t.compressionMethod=h;const C=a.get(Os);C&&(ca(C,t),t.extraFieldNTFS=C);const l=a.get(Ds);l&&(la(l,t,s),t.extraFieldExtendedTimestamp=l);const c=a.get(vs);c&&(t.extraFieldUSDZ=c)}function aa(n,t){t.zip64=!0;const e=q(n.data),r=na.filter(([s,i])=>t[s]==i);for(let s=0,i=0;s<r.length;s++){const[a,o]=r[s];if(t[a]==o){const f=ra[o];t[a]=n[a]=f.getValue(e,i),i+=f.bytes}else if(n[a])throw new Error(Rr)}}async function En(n,t,e,r,s){const i=q(n.data),a=new Ue;a.append(s[e]);const o=q(new Uint8Array(4));o.setUint32(0,a.get(),!0);const f=B(i,1);Object.assign(n,{version:be(i,0),[t]:at(n.data.subarray(5)),valid:!s.bitFlag.languageEncodingFlag&&f==B(o,0)}),n.valid&&(r[t]=n[t],r[t+"UTF8"]=!0)}function oa(n,t,e){const r=q(n.data),s=be(r,4);Object.assign(n,{vendorVersion:be(r,0),vendorId:be(r,2),strength:s,originalCompressionMethod:e,compressionMethod:j(r,5)}),t.compressionMethod=n.compressionMethod}function ca(n,t){const e=q(n.data);let r=4,s;try{for(;r<n.data.length&&!s;){const i=j(e,r),a=j(e,r+2);i==ks&&(s=n.data.slice(r+4,r+4+a)),r+=4+a}}catch{}try{if(s&&s.length==24){const i=q(s),a=i.getBigUint64(0,!0),o=i.getBigUint64(8,!0),f=i.getBigUint64(16,!0);Object.assign(n,{rawLastModDate:a,rawLastAccessDate:o,rawCreationDate:f});const h=Je(a),u=Je(o),y=Je(f),b={lastModDate:h,lastAccessDate:u,creationDate:y};Object.assign(n,b),Object.assign(t,b)}}catch{}}function la(n,t,e){const r=q(n.data),s=be(r,0),i=[],a=[];e?((s&1)==1&&(i.push(ct),a.push(lt)),(s&2)==2&&(i.push(gr),a.push(Xi)),(s&4)==4&&(i.push(br),a.push($i))):n.data.length>=5&&(i.push(ct),a.push(lt));let o=1;i.forEach((f,h)=>{if(n.data.length>=o+4){const u=B(r,o);t[f]=n[f]=new Date(u*1e3);const y=a[h];n[y]=u}o+=4})}async function fa(n,t,e,r,s){const i=new Uint8Array(4),a=q(i);da(a,0,t);const o=r+s;return await f(r)||await f(Math.min(o,e));async function f(h){const u=e-h,y=await G(n,u,h);for(let b=y.length-r;b>=0;b--)if(y[b]==i[0]&&y[b+1]==i[1]&&y[b+2]==i[2]&&y[b+3]==i[3])return{offset:u+b,buffer:y.slice(b,b+r).buffer}}}function K(n,t,e){return t[e]===$?n.options[e]:t[e]}function ua(n){const t=(n&4294901760)>>16,e=n&65535;try{return new Date(1980+((t&65024)>>9),((t&480)>>5)-1,t&31,(e&63488)>>11,(e&2016)>>5,(e&31)*2,0)}catch{}}function Je(n){return new Date(Number(n/BigInt(1e4)-BigInt(116444736e5)))}function be(n,t){return n.getUint8(t)}function j(n,t){return n.getUint16(t,!0)}function B(n,t){return n.getUint32(t,!0)}function Ne(n,t){return Number(n.getBigUint64(t,!0))}function da(n,t,e){n.setUint32(t,e,!0)}function q(n){return new DataView(n.buffer)}Pn({Inflate:gs});const ha=Object.freeze(Object.defineProperty({__proto__:null,BlobReader:yt,BlobWriter:tr,Data64URIReader:Ni,Data64URIWriter:Fi,ERR_BAD_FORMAT:Le,ERR_CENTRAL_DIRECTORY_NOT_FOUND:Tr,ERR_ENCRYPTED:Ar,ERR_EOCDR_LOCATOR_ZIP64_NOT_FOUND:Er,ERR_EOCDR_NOT_FOUND:yr,ERR_EOCDR_ZIP64_NOT_FOUND:xr,ERR_EXTRAFIELD_ZIP64_NOT_FOUND:Rr,ERR_HTTP_RANGE:ke,ERR_INVALID_PASSWORD:dt,ERR_INVALID_SIGNATURE:ht,ERR_ITERATOR_COMPLETED_TOO_SOON:zn,ERR_LOCAL_FILE_HEADER_NOT_FOUND:Sr,ERR_SPLIT_ZIP_FILE:ut,ERR_UNSUPPORTED_COMPRESSION:ft,ERR_UNSUPPORTED_ENCRYPTION:Cr,HttpRangeReader:Bi,HttpReader:or,Reader:fe,SplitDataReader:Et,SplitDataWriter:We,SplitZipReader:Vi,SplitZipWriter:Yi,TextReader:Ui,TextWriter:Mi,Uint8ArrayReader:ji,Uint8ArrayWriter:qi,Writer:bt,ZipReader:sa,configure:Pn,getMimeType:Ws,initReader:cr,initStream:Ae,initWriter:lr,readUint8Array:G,terminateWorkers:Ai},Symbol.toStringTag,{value:"Module"})),Ee=ha;class _a{constructor(t,e,r){N(this,"_zipReader");N(this,"_entriesPromise");N(this,"_traceURL");this._traceURL=t,Ee.configure({baseURL:self.location.href}),this._zipReader=new Ee.ZipReader(new Ee.HttpReader(ma(t,e),{mode:"cors",preventHeadRequest:!0}),{useWebWorkers:!1}),this._entriesPromise=this._zipReader.getEntries({onprogress:r}).then(s=>{const i=new Map;for(const a of s)i.set(a.filename,a);return i})}isLive(){return!1}traceURL(){return this._traceURL}async entryNames(){return[...(await this._entriesPromise).keys()]}async hasEntry(t){return(await this._entriesPromise).has(t)}async readText(t){var i;const r=(await this._entriesPromise).get(t);if(!r)return;const s=new Ee.TextWriter;return await((i=r.getData)==null?void 0:i.call(r,s)),s.getData()}async readBlob(t){const r=(await this._entriesPromise).get(t);if(!r)return;const s=new Ee.BlobWriter;return await r.getData(s),s.getData()}}class pa{constructor(t,e){N(this,"_entriesPromise");N(this,"_path");N(this,"_server");this._path=t,this._server=e,this._entriesPromise=e.readFile(t).then(async r=>{if(!r)throw new Error("File not found");const s=await r.json(),i=new Map;for(const a of s.entries)i.set(a.name,a.path);return i})}isLive(){return!0}traceURL(){return this._path}async entryNames(){return[...(await this._entriesPromise).keys()]}async hasEntry(t){return(await this._entriesPromise).has(t)}async readText(t){const e=await this._readEntry(t);return e==null?void 0:e.text()}async readBlob(t){const e=await this._readEntry(t);return(e==null?void 0:e.status)===200?await(e==null?void 0:e.blob()):void 0}async _readEntry(t){const r=(await this._entriesPromise).get(t);if(r)return this._server.readFile(r)}}function ma(n,t){let e=n.startsWith("http")||n.startsWith("blob")?n:t.getFileURL(n).toString();return e.startsWith("https://www.dropbox.com/")&&(e="https://dl.dropboxusercontent.com/"+e.substring(24)),e}class wa{constructor(t){this.baseUrl=t}getFileURL(t){const e=new URL("trace/file",this.baseUrl);return e.searchParams.set("path",t),e}async readFile(t){const e=await fetch(this.getFileURL(t));if(e.status!==404)return e}}self.addEventListener("install",function(n){self.skipWaiting()});self.addEventListener("activate",function(n){n.waitUntil(self.clients.claim())});const ga=new URL(self.registration.scope).pathname,le=new Map,Ce=new Map;async function ba(n,t,e,r,s){var h;await Dr();const i=(e==null?void 0:e.id)??"";let a=Ce.get(i);if(!a){const u=new URL((e==null?void 0:e.url)??self.registration.scope),y=new URL(u.searchParams.get("server")??"../",u);a={limit:r,traceUrls:new Set,traceViewerServer:new wa(y)},Ce.set(i,a)}a.traceUrls.add(n);const o=new Jr;try{const[u,y]=Nr(s,[.5,.4,.1]),b=n.endsWith("json")?new pa(n,a.traceViewerServer):new _a(n,a.traceViewerServer,u);await o.load(b,y)}catch(u){throw console.error(u),(h=u==null?void 0:u.message)!=null&&h.includes("Cannot find .trace file")&&await o.hasEntry("index.html")?new Error("Could not load trace. Did you upload a Playwright HTML report instead? Make sure to extract the archive first and then double-click the index.html file or put it on a web server."):u instanceof An?new Error(`Could not load trace from ${t||n}. ${u.message}`):t?new Error(`Could not load trace from ${t}. Make sure to upload a valid Playwright trace.`):new Error(`Could not load trace from ${n}. Make sure a valid Playwright Trace is accessible over this url.`)}const f=new Vr(o.storage(),u=>o.resourceForSha1(u));return le.set(n,{traceModel:o,snapshotServer:f}),o}async function ya(n){var f;if(n.request.url.startsWith("chrome-extension://"))return fetch(n.request);const t=n.request,e=await self.clients.get(n.clientId),r=self.registration.scope.startsWith("https://");if(t.url.startsWith(self.registration.scope)){const h=new URL(ze(t.url)),u=h.pathname.substring(ga.length-1);if(u==="/ping")return await Dr(),new Response(null,{status:200});const y=h.searchParams.get("trace");if(u==="/contexts")try{const b=h.searchParams.has("limit")?+h.searchParams.get("limit"):void 0,R=await ba(y,h.searchParams.get("traceFileName"),e,b,(C,l)=>{e.postMessage({method:"progress",params:{done:C,total:l}})});return new Response(JSON.stringify(R.contextEntries),{status:200,headers:{"Content-Type":"application/json"}})}catch(b){return new Response(JSON.stringify({error:b==null?void 0:b.message}),{status:500,headers:{"Content-Type":"application/json"}})}if(u.startsWith("/snapshotInfo/")){const{snapshotServer:b}=le.get(y)||{};return b?b.serveSnapshotInfo(u,h.searchParams):new Response(null,{status:404})}if(u.startsWith("/snapshot/")){const{snapshotServer:b}=le.get(y)||{};if(!b)return new Response(null,{status:404});const R=b.serveSnapshot(u,h.searchParams,h.href);return r&&R.headers.set("Content-Security-Policy","upgrade-insecure-requests"),R}if(u.startsWith("/closest-screenshot/")){const{snapshotServer:b}=le.get(y)||{};return b?b.serveClosestScreenshot(u,h.searchParams):new Response(null,{status:404})}if(u.startsWith("/sha1/")){const b=u.slice(6);for(const R of le.values()){const C=await R.traceModel.resourceForSha1(b);if(C)return new Response(C,{status:200,headers:xa(h.searchParams)})}return new Response(null,{status:404})}if(u.startsWith("/file/")){const b=h.searchParams.get("path"),R=(f=Ce.get(n.clientId??""))==null?void 0:f.traceViewerServer;if(!R)throw new Error("client is not initialized");const C=await R.readFile(b);return C||new Response(null,{status:404})}return fetch(n.request)}const s=ze(e.url),i=new URL(s).searchParams.get("trace"),{snapshotServer:a}=le.get(i)||{};if(!a)return new Response(null,{status:404});const o=[t.url];return r&&t.url.startsWith("https://")&&o.push(t.url.replace(/^https/,"http")),a.serveResource(o,t.method,s)}function xa(n){const t=n.get("dn"),e=n.get("dct");if(!t)return;const r=new Headers;return r.set("Content-Disposition",`attachment; filename="attachment"; filename*=UTF-8''${encodeURIComponent(t)}`),e&&r.set("Content-Type",e),r}async function Dr(){const n=await self.clients.matchAll(),t=new Set;for(const[e,r]of Ce){if(!n.find(s=>s.id===e)){Ce.delete(e);continue}if(r.limit!==void 0){const s=[...r.traceUrls];r.traceUrls=new Set(s.slice(s.length-r.limit))}r.traceUrls.forEach(s=>t.add(s))}for(const e of le.keys())t.has(e)||le.delete(e)}self.addEventListener("fetch",function(n){n.respondWith(ya(n))});
