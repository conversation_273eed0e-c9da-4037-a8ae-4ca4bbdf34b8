import tomllib
import xml.etree.ElementTree as ET
from typing import Dict, Any, Optional
import traceback
import time
import base64
from pathlib import Path
import asyncio
import json
import os
import random
from functools import lru_cache
import re

from loguru import logger

from WechatAPI import WechatAPIClient
from WechatAPI.Client import protector
from utils.event_manager import EventManager


class XYBot:
    def __init__(self, bot_client: WechatAPIClient):
        self.bot = bot_client
        self.wxid = bot_client.wxid
        self.nickname = None
        self.alias = None
        self.phone = None

        with open("main_config.toml", "rb") as f:
            main_config = tomllib.load(f)

        self.ignore_mode = main_config.get("XYBot", {}).get("ignore-mode", "")
        self.whitelist = main_config.get("XYBot", {}).get("whitelist", [])
        self.blacklist = main_config.get("XYBot", {}).get("blacklist", [])

        self.protect_msg_sent = False

        # 初始化临时目录
        self.temp_dir = Path("temp/image")
        self.temp_dir.mkdir(parents=True, exist_ok=True)

        # 临时文件配置
        self.max_age = 3600  # 1小时
        self.cleanup_interval = 900  # 优化：从30分钟减少到15分钟
        self.last_cleanup = 0

        # 表情映射文件路径
        self.emoji_map_file = Path("data/emoji_map/emoji_map.json")
        # 加载表情映射
        self.emoji_size_map = self.load_emoji_map()

        # 保留消息处理缓存
        self.message_cache = {}
        self.cache_expiry = 1800  # 优化：从1小时减少到30分钟过期
        self.max_cache_size = 1000  # 优化：添加最大缓存条目限制

    def load_emoji_map(self) -> dict:
        """加载表情映射文件"""
        try:
            if self.emoji_map_file.exists():
                with open(self.emoji_map_file, 'r', encoding='utf-8') as f:
                    data = json.load(f)
                logger.info(f"成功加载表情映射文件，共 {len(data)} 条记录")
                return data
        except Exception as e:
            logger.error(f"加载表情映射文件失败: {e}")
        return {}

    def update_profile(self, wxid: str, nickname: str, alias: str, phone: str):
        """更新机器人信息"""
        self.wxid = wxid
        self.nickname = nickname
        self.alias = alias
        self.phone = phone

    async def process_message(self, message: Dict[str, Any]):
        """处理接收到的消息"""
        if protector.check(14400):
            if not self.protect_msg_sent:
                logger.warning("登录新设备后4小时内请不要操作以避免风控")
                self.protect_msg_sent = True
            return

        msg_type = message.get("MsgType")

        # 预处理消息
        message["FromWxid"] = message.get("FromUserName").get("string")
        message.pop("FromUserName")
        message["ToWxid"] = message.get("ToWxid").get("string")

        # 处理一下自己发的消息
        if message["FromWxid"] == self.wxid and message["ToWxid"].endswith("@chatroom"):  # 自己发发到群聊
            # 由于是自己发送的消息，所以对于自己来说，From和To是反的
            message["FromWxid"], message["ToWxid"] = message["ToWxid"], message["FromWxid"]

        # 根据消息类型触发不同的事件
        if msg_type == 1:  # 文本消息
            await self.process_text_message(message)

        elif msg_type == 3:  # 图片消息
            await self.process_image_message(message)

        elif msg_type == 34:  # 语音消息
            await self.process_voice_message(message)

        elif msg_type == 43:  # 视频消息
            await self.process_video_message(message)

        elif msg_type == 47:  # 表情消息
            await self.process_emoji_message(message)

        elif msg_type == 49:  # xml消息
            # 获取消息内容
            content = message.get("Content")

            # 确保内容是字符串
            if isinstance(content, dict):
                content = content.get("string", "")

            # 将内容保存以供日志记录
            original_content = content

            # 处理群聊消息中的wxid前缀
            if message["FromWxid"].endswith("@chatroom"):
                # 查找群聊消息的发送者前缀 (通常格式为 "wxid_xxx:\n")
                split_index = content.find(":\n")
                if split_index > 0:
                    sender_wxid = content[:split_index]
                    message["SenderWxid"] = sender_wxid
                    # 提取实际内容，跳过前缀
                    content = content[split_index+2:]  # +2 是为了跳过 ":\n"
                    logger.debug(f"从群聊消息中提取发送者: {sender_wxid}")
                else:
                    message["SenderWxid"] = self.wxid
            else:
                message["SenderWxid"] = message["FromWxid"]

            # 智能查找XML开始位置
            xml_start = -1
            for xml_marker in ["<?xml", "<msg", "<xml"]:
                xml_start = content.find(xml_marker)
                if xml_start >= 0:
                    if xml_start > 0:
                        logger.debug(f"XML消息中找到XML标签，从位置 {xml_start} 开始截取")
                        content = content[xml_start:]
                    break

            # 更新处理后的内容
            message["Content"] = content

            # 如果找不到XML开始标签，记录错误但继续尝试处理
            if xml_start < 0:
                logger.error(f"XML消息中无法找到XML标签，消息预览: {original_content[:50]}")

            # 检查是否是公众号文章
            try:
                if content.startswith("<"):
                    # 尝试解析XML
                    root = ET.fromstring(content)
                    message["ParsedXML"] = root  # 保存解析结果

                    appmsg = root.find("appmsg")
                    if appmsg is not None:
                        type_node = appmsg.find("type")
                        if type_node is not None:
                            xml_type = int(type_node.text.strip())
                            message["XmlType"] = xml_type

                            # 公众号文章类型为5
                            if xml_type == 5:
                                await self.process_article_message(message)
                                return
                            # 引用消息类型为57
                            elif xml_type == 57:
                                # 确保设置IsGroup标志
                                if message["FromWxid"].endswith("@chatroom"):
                                    message["IsGroup"] = True
                                else:
                                    message["IsGroup"] = False
                                await self.process_quote_message(message)
                                return
                else:
                    logger.warning(f"XML内容不以<开头: {content[:50]}")
            except ET.ParseError as e:
                logger.error(f"解析XML失败: {e}")
                # 记录部分内容用于调试，避免敏感信息
                safe_content = content[:100].replace('<', '[').replace('>', ']')
                logger.debug(f"XML解析失败内容预览: {safe_content}...")
            except Exception as e:
                logger.error(f"处理XML消息异常: {e}")
                logger.error(f"错误详情: {traceback.format_exc()}")

            # 如果不是公众号文章或引用消息，按普通XML消息处理
            await self.process_xml_message(message)

        elif msg_type == 10002:  # 系统消息
            await self.process_system_message(message)

        elif msg_type == 37:  # 好友请求
            await EventManager.emit("friend_request", self.bot, message)

        elif msg_type == 51:
            pass

        else:
            logger.info("未知的消息类型: {}", message)

    async def process_text_message(self, message: Dict[str, Any]):
        """处理文本消息"""
        # 预处理消息
        message["Content"] = message.get("Content").get("string")

        if message["FromWxid"].endswith("@chatroom"):  # 群聊消息
            message["IsGroup"] = True
            split_content = message["Content"].split(":\n", 1)
            if len(split_content) > 1:
                message["Content"] = split_content[1]
                message["SenderWxid"] = split_content[0]
            else:  # 绝对是自己发的消息! qwq
                message["Content"] = split_content[0]
                message["SenderWxid"] = self.wxid

        else:
            message["SenderWxid"] = message["FromWxid"]
            if message["FromWxid"] == self.wxid:  # 自己发的消息
                message["FromWxid"] = message["ToWxid"]
            message["IsGroup"] = False

        try:
            root = ET.fromstring(message["MsgSource"])
            ats = root.find("atuserlist").text if root.find("atuserlist") is not None else ""
        except Exception as e:
            logger.error("解析文本消息失败: {}", e)
            return

        if ats:
            ats = ats.strip(",").split(",")
        else:  # 修复
            ats = []
        message["Ats"] = ats if ats and ats[0] != "" else []

        # 检查是否是表情消息
        if message.get("ImgStatus") == 1 and message["Content"].startswith("[") and message["Content"].endswith("]"):
            logger.info("收到表情消息: 消息ID:{} 来自:{} 发送人:{} @:{} 内容:{}",
                        message["MsgId"],
                        message["FromWxid"],
                        message["SenderWxid"],
                        message["Ats"],
                        message["Content"])

            if self.ignore_check(message["FromWxid"], message["SenderWxid"]):
                await EventManager.emit("emoji_message", self.bot, message)
            return

        if self.wxid in ats:
            logger.info("收到被@消息: 消息ID:{} 来自:{} 发送人:{} @:{} 内容:{}",
                        message["MsgId"],
                        message["FromWxid"],
                        message["SenderWxid"],
                        message["Ats"],
                        message["Content"])

            if self.ignore_check(message["FromWxid"], message["SenderWxid"]):
                await EventManager.emit("at_message", self.bot, message)
            return

        logger.info("收到文本消息: 消息ID:{} 来自:{} 发送人:{} @:{} 内容:{}",
                    message["MsgId"],
                    message["FromWxid"],
                    message["SenderWxid"],
                    message["Ats"],
                    message["Content"])

        if self.ignore_check(message["FromWxid"], message["SenderWxid"]):
            await EventManager.emit("text_message", self.bot, message)

    async def _cleanup_temp_files(self):
        """清理临时文件"""
        now = time.time()

        # 如果距离上次清理未达到间隔时间，跳过
        if now - self.last_cleanup < self.cleanup_interval:
            return

        try:
            count = 0
            total_size = 0
            for file_path in self.temp_dir.glob("*"):
                if file_path.is_file():
                    file_stat = file_path.stat()
                    file_age = now - file_stat.st_mtime
                    file_size = file_stat.st_size

                    # 如果文件过期，删除
                    if file_age > self.max_age:
                        try:
                            file_path.unlink()
                            count += 1
                            total_size += file_size
                        except Exception as e:
                            logger.error(f"删除临时文件 {file_path} 失败: {e}")

            if count > 0:
                # 记录清理的文件数量和大小
                total_mb = total_size / 1024 / 1024  # 转换为MB
                logger.info(f"已清理 {count} 个临时文件，释放 {total_mb:.2f}MB 空间")

            # 更新上次清理时间
            self.last_cleanup = now
        except Exception as e:
            logger.error(f"清理临时文件时出错: {e}")

        # 同时清理消息缓存
        self._clean_cache()

    async def _save_image(self, image_data: str, msgid: str) -> Optional[str]:
        """保存图片到临时目录

        Args:
            image_data: base64编码的图片数据
            msgid: 消息ID

        Returns:
            str: 保存的图片路径,如果失败返回None
        """
        try:
            # 确保临时目录存在
            self.temp_dir.mkdir(parents=True, exist_ok=True)

            # 保存图片
            image_path = str(self.temp_dir / f"{msgid}.jpg")
            image_bytes = base64.b64decode(image_data)
            with open(image_path, "wb") as f:
                f.write(image_bytes)

            return image_path

        except Exception as e:
            logger.error(f"[XYBot] 保存图片失败: {e}")
            return None

    async def process_image_message(self, message: Dict[str, Any]):
        """处理图片消息"""
        try:
            # 确保Content是字符串
            if isinstance(message["Content"], dict):
                message["Content"] = message["Content"].get("string", "")

            content = message["Content"]

            # 处理群聊消息中的wxid前缀
            if message["FromWxid"].endswith("@chatroom"):  # 群聊消息
                message["IsGroup"] = True
                split_index = content.find(":\n")
                if split_index > 0:
                    sender_wxid = content[:split_index]
                    content = content[split_index+2:]
                    message["SenderWxid"] = sender_wxid
                    message["Content"] = content
                else:
                    message["SenderWxid"] = self.wxid
            else:
                message["SenderWxid"] = message["FromWxid"]
                message["IsGroup"] = False

            # 缓存图片消息，用于后续引用消息处理
            try:
                msg_id = message.get("MsgId")
                if msg_id:
                    cache_key = f"image_{msg_id}"
                    self.message_cache[cache_key] = {
                        "xml": content,
                        "timestamp": time.time()
                    }
                    logger.debug(f"[XYBot] 缓存图片消息: {msg_id}")
            except Exception as e:
                logger.warning(f"[XYBot] 缓存图片消息失败: {e}")

            logger.info(f"收到图片消息: 消息ID:{message.get('MsgId')} 来自:{message.get('FromWxid')} 发送人:{message.get('SenderWxid')} XML:{content[:200]}...")

            # 触发图片消息事件
            if self.ignore_check(message["FromWxid"], message["SenderWxid"]):
                await EventManager.emit("image_message", self.bot, message)
        except Exception as e:
            logger.error(f"处理图片消息异常: {e}")
            logger.error(traceback.format_exc())

    async def process_voice_message(self, message: Dict[str, Any]):
        """处理语音消息"""
        try:
            # 预处理消息，增加安全检查
            message["Content"] = message.get("Content", {})
            if isinstance(message["Content"], dict):
                message["Content"] = message["Content"].get("string", "")

            if message["FromWxid"].endswith("@chatroom"):  # 群聊消息
                message["IsGroup"] = True
                split_content = message["Content"].split(":", 1)
                if len(split_content) > 1:
                    message["Content"] = split_content[1]
                    message["SenderWxid"] = split_content[0]
                else:  # 自己发的消息
                    message["Content"] = split_content[0]
                    message["SenderWxid"] = self.wxid
            else:
                message["SenderWxid"] = message["FromWxid"]
                if message["FromWxid"] == self.wxid:  # 自己发的消息
                    message["FromWxid"] = message["ToWxid"]
                message["IsGroup"] = False

            logger.info("收到语音消息: 消息ID:{} 来自:{} 发送人:{} XML:{}",
                        message.get("MsgId", "unknown"),
                        message.get("FromWxid", "unknown"),
                        message.get("SenderWxid", "unknown"),
                        message.get("Content", ""))

            # 记录语音消息ID，但不下载
            message["IsVoiceDownloaded"] = False

            # 安全地触发事件
            if self.ignore_check(message["FromWxid"], message["SenderWxid"]):
                await EventManager.emit("voice_message", self.bot, message)

        except Exception as e:
            # 顶层异常捕获，确保不会导致程序崩溃
            logger.error(f"处理语音消息顶层异常: {e} - {traceback.format_exc()}")

    def _clean_cache(self):
        """清理过期的消息缓存"""
        now = time.time()
        expired_keys = []

        try:
            # 检查缓存大小
            if len(self.message_cache) > self.max_cache_size:
                # 如果超过最大缓存大小，按过期时间排序，删除最旧的条目
                logger.warning(f"消息缓存达到上限 ({len(self.message_cache)}条)，进行清理")
                cache_items = [(k, v.get('timestamp', 0)) for k, v in self.message_cache.items()]
                cache_items.sort(key=lambda x: x[1])  # 按时间戳排序

                # 计算需要删除的数量，保留80%的新条目
                delete_count = int(len(cache_items) * 0.2)
                for i in range(delete_count):
                    if i < len(cache_items):
                        expired_keys.append(cache_items[i][0])

            # 正常过期检查
            for key, data in self.message_cache.items():
                if now - data.get('timestamp', 0) > self.cache_expiry:
                    expired_keys.append(key)

            # 删除过期条目
            for key in expired_keys:
                self.message_cache.pop(key, None)

            if expired_keys:
                logger.debug(f"已清理 {len(expired_keys)} 条过期消息缓存")
        except Exception as e:
            logger.error(f"清理消息缓存时出错: {e}")

    async def process_xml_message(self, message: Dict[str, Any]):
        """处理xml消息"""
        # 获取消息内容
        content = message.get("Content")
        if isinstance(content, dict):
            content = content.get("string", "")

        # 处理群聊消息中的wxid前缀
        sender_wxid = None
        if message["FromWxid"].endswith("@chatroom"):  # 群聊消息
            message["IsGroup"] = True
            # 查找第一个换行符，wxid前缀通常是这样的格式: wxid_xxx:\n
            split_index = content.find(":\n")
            if split_index > 0:
                sender_wxid = content[:split_index]
                # 保留前缀后的实际内容
                content = content[split_index+2:]  # +2 是为了跳过 ":\n"
                message["SenderWxid"] = sender_wxid
            else:  # 可能是自己发的消息
                message["SenderWxid"] = self.wxid
        else:
            message["SenderWxid"] = message["FromWxid"]
            if message["FromWxid"] == self.wxid:  # 自己发的消息
                message["FromWxid"] = message["ToWxid"]
            message["IsGroup"] = False

        # 更新处理后的内容
        message["Content"] = content

        # 智能查找XML开始标签位置
        xml_start = -1
        for xml_marker in ["<?xml", "<msg", "<xml"]:
            xml_start = content.find(xml_marker)
            if xml_start >= 0:
                if xml_start > 0:
                    logger.debug(f"找到XML开始标签，截取内容从位置 {xml_start}")
                    content = content[xml_start:]
                break

        if xml_start < 0:  # 仍然找不到有效的XML开始标签
            logger.error(f"无法找到有效的XML开始标签，消息内容前50个字符: {content[:50]}")
            return

        # 再次更新处理后的内容
        message["Content"] = content

        # 记录一些基本信息，方便调试
        logger.debug(f"XML消息内容预览: {content[:100]}...")

        # 解析XML并提取类型
        message_type = None
        parsed_xml = None
        appmsg = None

        try:
            parsed_xml = ET.fromstring(content)
            appmsg = parsed_xml.find("appmsg")
            if appmsg is None:
                logger.error("未找到appmsg节点")
                return

            type_node = appmsg.find("type")
            if type_node is None:
                logger.error("未找到type节点")
                return

            message_type = int(type_node.text)
            # 保存解析结果，避免重复解析
            message["ParsedXML"] = parsed_xml
            message["XmlType"] = message_type
            logger.debug(f"XML消息类型: {message_type}")

        except ET.ParseError as e:
            logger.error(f"解析XML失败: {e}")
            # 保存前100个字符帮助调试，替换<>以避免日志可能的解析问题
            if len(content) > 0:
                safe_content = content[:100].replace('<', '[').replace('>', ']')
                logger.debug(f"XML内容预览: {safe_content}...")
            return
        except Exception as e:
            logger.error(f"处理XML消息异常: {e}")
            logger.error(f"错误详情: {traceback.format_exc()}")
            return

        # 根据消息类型路由到不同处理函数
        if message_type == 57:  # 引用消息
            await self.process_quote_message(message)
        elif message_type == 6:  # 文件消息
            await self.process_file_message(message)
        elif message_type == 74:  # 文件消息，但还在上传，不用管
            pass
        elif message_type == 5:  # 公众号文章
            await self.process_article_message(message)
        elif message_type == 3:  # 红包消息
            # 提取红包信息
            try:
                title = appmsg.find("title").text if appmsg.find("title") is not None else ""
                des = appmsg.find("des").text if appmsg.find("des") is not None else ""
                message["RedPacketTitle"] = title
                message["RedPacketDesc"] = des

                # 记录完整的XML内容
                logger.info(f"收到红包消息完整XML:\n{content}")
                logger.info(f"收到红包消息: 标题:{title} 描述:{des} 来自:{message['FromWxid']} 发送人:{message['SenderWxid']}")

                # 触发红包消息事件
                if self.ignore_check(message["FromWxid"], message["SenderWxid"]):
                    await EventManager.emit("xml_message", self.bot, message)
            except Exception as e:
                logger.error(f"处理红包消息失败: {e}")
        else:
            logger.info(f"未知的xml消息类型: {message_type}, 内容预览: {content[:50]}...")

    async def process_quote_message(self, message: Dict[str, Any]):
        """处理引用消息"""
        quote_messsage = {}
        original_content = message.get("Content", "")

        if isinstance(original_content, dict):
            original_content = original_content.get("string", "")

        # 1. 检查是否包含特殊指令
        is_repeat_command = False
        content_text = original_content
        if "@chatroom" in message.get("FromWxid", ""):
            if ":\n" in content_text:
                _, content_text = content_text.split(":\n", 1)

        if "复读" in content_text:
            is_repeat_command = True
            logger.debug("检测到复读命令")

        # 2. XML解析处理
        parsed_xml = message.get("ParsedXML")
        text = ""

        if parsed_xml is not None:
            logger.debug("使用已解析的XML处理引用消息")
            try:
                appmsg = parsed_xml.find("appmsg")

                if appmsg is not None:
                    # 获取引用消息标题
                    title = appmsg.find("title")
                    if title is not None:
                        text = title.text

                    # 获取引用消息内容
                    refermsg = appmsg.find("refermsg")
                    if refermsg is not None:
                        # 获取消息类型
                        msg_type = int(refermsg.find("type").text) if refermsg.find("type") is not None else 1
                        quote_messsage["MsgType"] = msg_type

                        # 对于图片消息,需要保留完整的XML内容
                        if msg_type == 3:  # 图片消息
                            quote_messsage["Content"] = original_content
                        else:
                            # 获取引用消息的完整内容
                            quote_content = refermsg.find("content")
                            if quote_content is not None and quote_content.text:
                                content_text = quote_content.text
                                # 如果是复读命令且是表情消息，尝试提取表情信息
                                if is_repeat_command and msg_type == 47:
                                    try:
                                        # 表情消息格式为 wxid:timestamp:0:md5::0
                                        parts = content_text.strip().split(":")
                                        if len(parts) >= 4:
                                            emoji_md5 = parts[3]
                                            # 检查是否是有效的MD5（32位十六进制字符串）
                                            if len(emoji_md5) == 32 and all(c in '0123456789abcdef' for c in emoji_md5.lower()):
                                                quote_messsage["EmojiMd5"] = emoji_md5
                                                logger.info(f"从引用消息提取到表情MD5: {emoji_md5}")
                                            else:
                                                logger.warning(f"提取到的MD5格式不正确: {emoji_md5}")
                                    except Exception as e:
                                        logger.error(f"提取表情MD5失败: {e}, 原始内容: {content_text}")

                                # HTML解码处理
                                if "&lt;" in content_text or "&gt;" in content_text:
                                    import html
                                    content_text = html.unescape(content_text)
                                    logger.debug("引用消息内容已HTML解码")
                                quote_messsage["Content"] = content_text
                            else:
                                quote_messsage["Content"] = original_content

                        # 提取元数据
                        quote_messsage["Msgid"] = refermsg.find("svrid").text if refermsg.find("svrid") is not None else ""
                        quote_messsage["NewMsgId"] = refermsg.find("svrid").text if refermsg.find("svrid") is not None else ""
                        quote_messsage["DisplayName"] = refermsg.find("displayname").text if refermsg.find("displayname") is not None else ""
                        quote_messsage["Fromusr"] = refermsg.find("fromusr").text if refermsg.find("fromusr") is not None else ""
                        quote_messsage["Chatusr"] = refermsg.find("chatusr").text if refermsg.find("chatusr") is not None else ""

                        # 处理引用视频号内容
                        if msg_type == 5:  # 视频号/公众号文章
                            # 检查是否是视频号文章
                            source = refermsg.find("source")
                            if source is not None and "视频号" in source.text:
                                quote_messsage["IsVideoChannel"] = True
                                logger.info("检测到引用视频号内容")
                    else:
                        # 如果找不到refermsg，使用原始内容
                        quote_messsage["Content"] = original_content
                        quote_messsage["MsgType"] = 1
                        quote_messsage["Msgid"] = ""
                else:
                    # 如果找不到appmsg，使用原始内容
                    quote_messsage["Content"] = original_content
                    quote_messsage["MsgType"] = 1
            except ET.ParseError as e:
                logger.warning(f"解析引用消息XML失败: {e}")
                # 屏蔽敏感信息，只展示前50个字符
                if original_content:
                    safe_content = original_content[:50].replace('<', '[').replace('>', ']')
                    logger.debug(f"引用消息内容预览: {safe_content}...")
                # 解析失败时使用原始内容
                quote_messsage["Content"] = original_content
                quote_messsage["MsgType"] = 1
                quote_messsage["Msgid"] = ""
            except Exception as e:
                logger.error(f"处理引用消息失败: {e}")
                logger.error(f"错误详情: {traceback.format_exc()}")
                return

        # 3. 设置引用信息
        message["Quote"] = quote_messsage

        # 记录引用消息信息
        logger.info("收到引用消息: 消息ID:{} 来自:{} 发送人:{} 引用类型:{} 引用内容:{}",
                    message.get("MsgId", "未知"),
                    message.get("FromWxid", "未知"),
                    message.get("SenderWxid", "未知"),
                    quote_messsage.get("MsgType", "未知"),
                    text[:50] if len(text) > 50 else text)

        # 4. 处理引用图片消息识别功能
        quote_type = message.get("Quote", {}).get("MsgType")

        # 检查是否是识别图片的请求
        content = message.get("Content", "").lower()
        is_analysis_request = any(keyword in content for keyword in ["识别图片", "这是什么", "分析图片", "这图是什么"])

        if quote_type == 3 and is_analysis_request:  # 如果是图片类型且是识别请求
            # 尝试从不同位置获取引用消息ID
            quoted_msg_id = (message.get("QuotedMsgId") or
                          message.get("Quote", {}).get("Msgid") or
                          message.get("Quote", {}).get("svrid"))

            if quoted_msg_id:
                user_key = f"{message['FromWxid']}_{message['SenderWxid']}"
                cache_key = f"image_{quoted_msg_id}"

                if cache_key in self.message_cache:
                    logger.debug(f"[XYBot] 从缓存中恢复引用图片信息: {quoted_msg_id}")
                    quoted_image_info = self.message_cache[cache_key]
                    message["QuotedXML"] = quoted_image_info["xml"]

                    # 标记这是一个图片分析请求
                    message["IsImageAnalysisRequest"] = True
                    logger.debug(f"[XYBot] 标记为图片分析请求: {message.get('MsgId')}")

        # 5. 处理特殊引用类型
        # 如果是引用视频号内容
        if quote_messsage.get("IsVideoChannel", False):
            message["IsVideoChannelQuote"] = True
            # 可以添加额外的视频号处理逻辑

        # 在所有处理完成后，统一触发一次事件
        if self.ignore_check(message["FromWxid"], message["SenderWxid"]):
            await EventManager.emit("quote_message", self.bot, message)

    async def process_article_message(self, message: Dict[str, Any]):
        """处理公众号文章消息"""
        # 预处理消息
        content = message.get("Content")
        if isinstance(content, dict):
            message["Content"] = content.get("string", "")

        # 设置消息来源信息
        if message["FromWxid"].endswith("@chatroom"):  # 群聊消息
            message["IsGroup"] = True
            split_content = message["Content"].split(":", 1)
            if len(split_content) > 1:
                message["Content"] = split_content[1]
                message["SenderWxid"] = split_content[0]
            else:  # 自己发的消息
                message["Content"] = split_content[0]
                message["SenderWxid"] = self.wxid
        else:
            message["SenderWxid"] = message["FromWxid"]
            if message["FromWxid"] == self.wxid:  # 自己发的消息
                message["FromWxid"] = message["ToWxid"]
            message["IsGroup"] = False

        # 添加额外的元数据，便于插件使用
        is_direct_from_mp = False
        if not message["IsGroup"] and message["FromWxid"].startswith("gh_"):
            is_direct_from_mp = True
            message["IsDirectFromMP"] = True
            message["PublicAccountID"] = message["FromWxid"]
            logger.debug(f"直接从公众号接收到文章，公众号ID: {message['FromWxid']}")

        # 只记录基本信息，不包含XML内容
        logger.info("收到公众号文章消息: 消息ID:{} 来自:{}",
                    message.get("MsgId"),
                    message.get("FromWxid"))

        if self.ignore_check(message["FromWxid"], message["SenderWxid"]):
            await EventManager.emit("article_message", self.bot, message)

    async def process_video_message(self, message):
        """处理视频消息"""
        try:
            # 预处理消息，增加安全检查
            message["Content"] = message.get("Content", {})
            if isinstance(message["Content"], dict):
                message["Content"] = message["Content"].get("string", "")

            if message["FromWxid"].endswith("@chatroom"):  # 群聊消息
                message["IsGroup"] = True
                split_content = message["Content"].split(":", 1)
                if len(split_content) > 1:
                    message["Content"] = split_content[1]
                    message["SenderWxid"] = split_content[0]
                else:  # 绝对是自己发的消息! qwq
                    message["Content"] = split_content[0]
                    message["SenderWxid"] = self.wxid
            else:
                message["SenderWxid"] = message["FromWxid"]
                if message["FromWxid"] == self.wxid:  # 自己发的消息
                    message["FromWxid"] = message["ToWxid"]
                message["IsGroup"] = False

            logger.info("收到视频消息: 消息ID:{} 来自:{} 发送人:{} XML:{}",
                        message.get("MsgId", "unknown"),
                        message.get("FromWxid", "unknown"),
                        message.get("SenderWxid", "unknown"),
                        message.get("Content", ""))

            # 记录视频消息ID，但不下载
            message["IsVideoDownloaded"] = False

            # 安全地触发事件
            if self.ignore_check(message["FromWxid"], message["SenderWxid"]):
                await EventManager.emit("video_message", self.bot, message)

        except Exception as e:
            # 顶层异常捕获，确保不会导致程序崩溃
            logger.error(f"处理视频消息顶层异常: {e} - {traceback.format_exc()}")

    async def process_file_message(self, message: Dict[str, Any]):
        """处理文件消息"""
        try:
            # 预处理消息，增加安全检查
            message["Content"] = message.get("Content", {})
            if isinstance(message["Content"], dict):
                message["Content"] = message["Content"].get("string", "")

            if message["FromWxid"].endswith("@chatroom"):  # 群聊消息
                message["IsGroup"] = True
                split_content = message["Content"].split(":", 1)
                if len(split_content) > 1:
                    message["Content"] = split_content[1]
                    message["SenderWxid"] = split_content[0]
                else:  # 自己发的消息
                    message["Content"] = split_content[0]
                    message["SenderWxid"] = self.wxid
            else:
                message["SenderWxid"] = message["FromWxid"]
                if message["FromWxid"] == self.wxid:  # 自己发的消息
                    message["FromWxid"] = message["ToWxid"]
                message["IsGroup"] = False

            # 解析文件信息
            try:
                root = ET.fromstring(message["Content"])
                appmsg = root.find("appmsg")
                if appmsg is not None:
                    message["Filename"] = appmsg.find("title").text if appmsg.find("title") is not None else ""
                    appattach = appmsg.find("appattach")
                    if appattach is not None:
                        message["AttachId"] = appattach.find("attachid").text if appattach.find("attachid") is not None else ""
                        message["FileExtend"] = appattach.find("fileext").text if appattach.find("fileext") is not None else ""
            except Exception as error:
                logger.error(f"[XYBot] 解析文件消息XML失败: {error}")
                return

            logger.info("收到文件消息: 消息ID:{} 来自:{} 发送人:{} 文件名:{}",
                        message.get("MsgId", "unknown"),
                        message.get("FromWxid", "unknown"),
                        message.get("SenderWxid", "unknown"),
                        message.get("Filename", "unknown"))

            # 记录文件未下载状态
            message["IsFileDownloaded"] = False

            # 安全地触发事件
            if self.ignore_check(message["FromWxid"], message["SenderWxid"]):
                await EventManager.emit("file_message", self.bot, message)

        except Exception as e:
            # 顶层异常捕获，确保不会导致程序崩溃
            logger.error(f"处理文件消息顶层异常: {e} - {traceback.format_exc()}")

    async def process_system_message(self, message: Dict[str, Any]):
        """处理系统消息"""
        # 预处理消息
        message["Content"] = message.get("Content").get("string")

        if message["FromWxid"].endswith("@chatroom"):  # 群聊消息
            message["IsGroup"] = True
            split_content = message["Content"].split(":", 1)
            if len(split_content) > 1:
                message["Content"] = split_content[1]
                message["SenderWxid"] = split_content[0]
            else:  # 绝对是自己发的消息! qwq
                message["Content"] = split_content[0]
                message["SenderWxid"] = self.wxid
        else:
            message["SenderWxid"] = message["FromWxid"]
            if message["FromWxid"] == self.wxid:  # 自己发的消息
                message["FromWxid"] = message["ToWxid"]
            message["IsGroup"] = False

        try:
            root = ET.fromstring(message["Content"])
            msg_type = root.attrib["type"]
            logger.debug(f"系统消息类型: {msg_type}")
        except Exception as e:
            logger.error(f"解析系统消息失败: {e}")
            return

        if msg_type == "pat":
            await self.process_pat_message(message)
        elif msg_type == "ClientCheckGetExtInfo":
            pass
        elif msg_type == "sysmsgtemplate":
            # 触发系统消息事件
            if self.ignore_check(message["FromWxid"], message["SenderWxid"]):
                await EventManager.emit("system_message", self.bot, message)
        else:
            logger.info("未知的系统消息类型: {}", message)

    async def process_pat_message(self, message: Dict[str, Any]):
        """处理拍一拍请求消息"""
        try:
            root = ET.fromstring(message["Content"])
            pat = root.find("pat")
            patter = pat.find("fromusername").text
            patted = pat.find("pattedusername").text
            pat_suffix = pat.find("patsuffix").text
        except Exception as e:
            logger.error(f"解析拍一拍消息失败: {e}")
            return

        message["Patter"] = patter
        message["Patted"] = patted
        message["PatSuffix"] = pat_suffix

        logger.info("收到拍一拍消息: 消息ID:{} 来自:{} 发送人:{} 拍者:{} 被拍:{} 后缀:{}",
                    message["MsgId"],
                    message["FromWxid"],
                    message["SenderWxid"],
                    message["Patter"],
                    message["Patted"],
                    message["PatSuffix"])

        if self.ignore_check(message["FromWxid"], message["SenderWxid"]):
            await EventManager.emit("pat_message", self.bot, message)

    async def process_emoji_message(self, message: Dict[str, Any]):
        """处理表情消息"""
        # 预处理消息
        message["Content"] = message.get("Content").get("string")

        if message["FromWxid"].endswith("@chatroom"):  # 群聊消息
            message["IsGroup"] = True
            split_content = message["Content"].split(":\n", 1)
            if len(split_content) > 1:
                message["Content"] = split_content[1]
                message["SenderWxid"] = split_content[0]
            else:  # 绝对是自己发的消息! qwq
                message["Content"] = split_content[0]
                message["SenderWxid"] = self.wxid
        else:
            message["SenderWxid"] = message["FromWxid"]
            if message["FromWxid"] == self.wxid:  # 自己发的消息
                message["FromWxid"] = message["ToWxid"]
            message["IsGroup"] = False

        try:
            # 解析表情XML
            root = ET.fromstring(message["Content"])
            emoji = root.find("emoji")
            if emoji is not None:
                message["EmojiMD5"] = emoji.get("md5")
                message["EmojiLen"] = emoji.get("len")
                message["EmojiUrl"] = emoji.get("cdnurl")
                message["EmojiAttr"] = emoji.get("emojiattr", "")

                logger.info("收到表情消息: 消息ID:{} 来自:{} 发送人:{} MD5:{} 大小:{}",
                            message["MsgId"],
                            message["FromWxid"],
                            message["SenderWxid"],
                            message["EmojiMD5"],
                            message["EmojiLen"])

                if self.ignore_check(message["FromWxid"], message["SenderWxid"]):
                    await EventManager.emit("emoji_message", self.bot, message)
                return

        except Exception as e:
            logger.error(f"解析表情消息失败: {e}")
            return

    def ignore_check(self, FromWxid: str, SenderWxid: str):
        if self.ignore_mode == "Whitelist":
            return (FromWxid in self.whitelist) or (SenderWxid in self.whitelist)
        elif self.ignore_mode == "blacklist":
            return (FromWxid not in self.blacklist) and (SenderWxid not in self.blacklist)
        else:
            return True

    async def download_voice_data(self, message: Dict[str, Any]) -> Optional[str]:
        """按需下载语音数据

        Args:
            message: 包含语音消息ID的消息对象

        Returns:
            str: 语音的base64编码字符串，如果下载失败则返回None
        """
        # 检查是否已经下载过
        if message.get("IsVoiceDownloaded", False) and message.get("Voice"):
            logger.debug(f"[XYBot] 语音已下载，直接返回缓存")
            return message["Voice"]

        # 获取语音消息ID
        message_id = message.get("MsgId", "")
        if not message_id:
            logger.error("[XYBot] 缺少语音下载所需的消息ID")
            return None

        # 检查缓存
        try:
            cache_key = f"voice_{message_id}"
            if (cache_key in self.message_cache and
                isinstance(self.message_cache[cache_key], dict) and
                "content" in self.message_cache[cache_key] and
                "timestamp" in self.message_cache[cache_key] and
                time.time() - self.message_cache[cache_key]["timestamp"] < self.cache_expiry):

                logger.debug(f"[XYBot] 使用缓存的语音: {message_id}")
                message["Voice"] = self.message_cache[cache_key]["content"]
                message["IsVoiceDownloaded"] = True
                return message["Voice"]
        except Exception as e:
            logger.warning(f"[XYBot] 检查语音缓存时出错: {e}")

        # 创建语音处理信号量，如果不存在的话
        if not hasattr(self.__class__, '_voice_semaphore'):
            self.__class__._voice_semaphore = asyncio.Semaphore(2)  # 最多同时处理2个语音

        # 下载语音
        try:
            async with self.__class__._voice_semaphore:
                try:
                    # 设置超时保护
                    voice_data = await asyncio.wait_for(
                        self.bot.download_voice(message_id),
                        timeout=30  # 30秒超时
                    )

                    if not voice_data:
                        logger.error(f"[XYBot] 下载语音失败或返回空: {message_id}")
                        return None

                    # 保存到消息对象
                    message["Voice"] = voice_data
                    message["IsVoiceDownloaded"] = True

                    # 添加到缓存
                    try:
                        self.message_cache[cache_key] = {
                            "content": voice_data,
                            "timestamp": time.time()
                        }
                        # 清理过期缓存
                        self._clean_cache()
                    except Exception as e:
                        logger.warning(f"[XYBot] 添加语音缓存时出错: {e}")

                    logger.debug(f"[XYBot] 按需下载语音成功: {message_id}")
                    return voice_data

                except asyncio.TimeoutError:
                    logger.error(f"[XYBot] 下载语音超时: {message_id}")
                    return None
                except Exception as e:
                    logger.error(f"[XYBot] 处理语音消息异常: {e} - {traceback.format_exc()}")
                    return None
        except Exception as e:
            logger.error(f"[XYBot] 获取语音处理信号量时出错: {e}")
            return None

    async def download_file_data(self, message: Dict[str, Any]) -> Optional[str]:
        """按需下载文件数据

        Args:
            message: 包含文件信息的消息对象

        Returns:
            str: 文件的base64编码字符串，如果下载失败则返回None
        """
        # 检查是否已经下载过
        if message.get("IsFileDownloaded", False) and message.get("File"):
            logger.debug(f"[XYBot] 文件已下载，直接返回缓存")
            return message["File"]

        # 获取文件附件ID
        attach_id = message.get("AttachId", "")
        if not attach_id:
            logger.error("[XYBot] 缺少文件下载所需的附件ID")
            return None

        # 检查缓存
        try:
            cache_key = f"file_{attach_id}"
            if (cache_key in self.message_cache and
                isinstance(self.message_cache[cache_key], dict) and
                "content" in self.message_cache[cache_key] and
                "timestamp" in self.message_cache[cache_key] and
                time.time() - self.message_cache[cache_key]["timestamp"] < self.cache_expiry):

                logger.debug(f"[XYBot] 使用缓存的文件: {attach_id}")
                message["File"] = self.message_cache[cache_key]["content"]
                message["IsFileDownloaded"] = True
                return message["File"]
        except Exception as e:
            logger.warning(f"[XYBot] 检查文件缓存时出错: {e}")

        # 创建文件处理信号量，如果不存在的话
        if not hasattr(self.__class__, '_file_semaphore'):
            self.__class__._file_semaphore = asyncio.Semaphore(2)  # 最多同时处理2个文件

        # 下载文件
        try:
            async with self.__class__._file_semaphore:
                try:
                    # 设置超时保护
                    file_data = await asyncio.wait_for(
                        self.bot.download_attach(attach_id),
                        timeout=300  # 5分钟超时，文件可能比较大
                    )

                    if not file_data:
                        logger.error(f"[XYBot] 下载文件失败或返回空: {attach_id}")
                        return None

                    # 保存到消息对象
                    message["File"] = file_data
                    message["IsFileDownloaded"] = True

                    # 添加到缓存
                    try:
                        self.message_cache[cache_key] = {
                            "content": file_data,
                            "timestamp": time.time()
                        }
                        # 清理过期缓存
                        self._clean_cache()
                    except Exception as e:
                        logger.warning(f"[XYBot] 添加文件缓存时出错: {e}")

                    logger.debug(f"[XYBot] 按需下载文件成功: {attach_id}")
                    return file_data

                except asyncio.TimeoutError:
                    logger.error(f"[XYBot] 下载文件超时: {attach_id}")
                    return None
                except Exception as e:
                    logger.error(f"[XYBot] 处理文件消息异常: {e} - {traceback.format_exc()}")
                    return None
        except Exception as e:
            logger.error(f"[XYBot] 获取文件处理信号量时出错: {e}")
            return None

    async def download_image_data(self, message: Dict[str, Any]) -> Optional[str]:
        """按需下载图片数据

        Args:
            message: 包含图片信息的消息对象

        Returns:
            str: 图片的本地路径，如果下载失败则返回None
        """
        # 检查是否已经下载过
        if message.get("IsImageDownloaded", False) and message.get("ImagePath"):
            logger.debug(f"[XYBot] 图片已下载，直接返回路径: {message['ImagePath']}")
            return message["ImagePath"]

        # 获取图片信息
        aeskey = message.get("aeskey")
        cdnmidimgurl = message.get("cdnmidimgurl")

        if not aeskey or not cdnmidimgurl:
            logger.error("[XYBot] 缺少图片下载所需的关键信息")
            return None

        # 下载图片
        image_data = await self.bot.download_image(aeskey, cdnmidimgurl)
        if not image_data:
            logger.error("[XYBot] 下载图片失败")
            return None

        # 保存图片
        image_path = await self._save_image(image_data, message["MsgId"])
        if not image_path:
            logger.error("[XYBot] 保存图片失败")
            return None

        # 更新消息对象
        message["ImageData"] = image_data
        message["ImagePath"] = image_path
        message["IsImageDownloaded"] = True

        logger.debug(f"[XYBot] 按需下载图片成功: {image_path}")
        return image_path

    async def download_video_data(self, message: Dict[str, Any]) -> Optional[str]:
        """按需下载视频数据

        Args:
            message: 包含视频消息ID的消息对象

        Returns:
            str: 视频的base64编码字符串，如果下载失败则返回None
        """
        # 检查是否已经下载过
        if message.get("IsVideoDownloaded", False) and message.get("Video"):
            logger.debug(f"[XYBot] 视频已下载，直接返回缓存")
            return message["Video"]

        # 获取视频消息ID
        message_id = message.get("MsgId", "")
        if not message_id:
            logger.error("[XYBot] 缺少视频下载所需的消息ID")
            return None

        # 检查缓存
        try:
            cache_key = f"video_{message_id}"
            if (cache_key in self.message_cache and
                isinstance(self.message_cache[cache_key], dict) and
                "content" in self.message_cache[cache_key] and
                "timestamp" in self.message_cache[cache_key] and
                time.time() - self.message_cache[cache_key]["timestamp"] < self.cache_expiry):

                logger.debug(f"[XYBot] 使用缓存的视频: {message_id}")
                message["Video"] = self.message_cache[cache_key]["content"]
                message["IsVideoDownloaded"] = True
                return message["Video"]
        except Exception as e:
            logger.warning(f"[XYBot] 检查视频缓存时出错: {e}")

        # 创建视频处理信号量，如果不存在的话
        if not hasattr(self.__class__, '_video_semaphore'):
            self.__class__._video_semaphore = asyncio.Semaphore(2)  # 最多同时处理2个视频

        # 下载视频
        try:
            async with self.__class__._video_semaphore:
                try:
                    # 设置超时保护
                    video_data = await asyncio.wait_for(
                        self.bot.download_video(message_id),
                        timeout=60  # 60秒超时
                    )

                    if not video_data:
                        logger.error(f"[XYBot] 下载视频失败或返回空: {message_id}")
                        return None

                    # 保存到消息对象
                    message["Video"] = video_data
                    message["IsVideoDownloaded"] = True

                    # 添加到缓存
                    try:
                        self.message_cache[cache_key] = {
                            "content": video_data,
                            "timestamp": time.time()
                        }
                        # 清理过期缓存
                        self._clean_cache()
                    except Exception as e:
                        logger.warning(f"[XYBot] 添加视频缓存时出错: {e}")

                    logger.debug(f"[XYBot] 按需下载视频成功: {message_id}")
                    return video_data

                except asyncio.TimeoutError:
                    logger.error(f"[XYBot] 下载视频超时: {message_id}")
                    return None
                except Exception as e:
                    logger.error(f"[XYBot] 处理视频消息异常: {e} - {traceback.format_exc()}")
                    return None
        except Exception as e:
            logger.error(f"[XYBot] 获取视频处理信号量时出错: {e}")
            return None
