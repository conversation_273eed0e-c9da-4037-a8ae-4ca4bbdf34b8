import os
import json
import asyncio
from loguru import logger
from pathlib import Path
import time
try:
    import tomllib
except ModuleNotFoundError:
    import tomli as tomllib

from utils.decorators import on_text_message
from utils.plugin_base import PluginBase

class Xunfei(PluginBase):
    """讯飞星火AI绘图插件"""
    
    description = "讯飞星火AI绘图"
    author = "Your Name"
    version = "1.0.0"
    plugin_name = "xunfei"

    def __init__(self):
        super().__init__()
        
        # 加载配置
        self.config = self._load_config()
        
        # 初始化API配置
        self.api_config = self.config.get("api", {})
        self.auth_config = self.config.get("auth", {})
        
        # 基本配置
        self.enable = self.config.get("enable", True)
        self.command = self.config.get("command", ["画图", "绘图"])
        self.command_format = self.config.get("command-format", "使用方法: 画图 <描述文本>")
        
        # 创建临时目录
        self.temp_dir = Path("temp/xunfei")
        self.temp_dir.mkdir(parents=True, exist_ok=True)

    def _load_config(self) -> dict:
        """加载配置文件"""
        try:
            config_path = f"plugins/{self.plugin_name}/config.toml"
            if not os.path.exists(config_path):
                logger.warning(f"[{self.plugin_name}] 配置文件 {config_path} 不存在")
                return {}
            with open(config_path, "rb") as f:
                config = tomllib.load(f)
            return config.get(self.plugin_name, {})
        except Exception as e:
            logger.error(f"[{self.plugin_name}] 加载配置文件失败: {e}")
            return {}

    async def _process_data(self, prompt: str) -> dict:
        """处理第一步请求"""
        import httpx
        
        # 完全按照原始请求构建，简化提示词
        # 移除一些可能导致审核的词语
        prompt_text = "画一张" + self._filter_sensitive_words(prompt)
        body = f'{{"question":"{prompt_text}","platform":"mobile"}}'
        
        # 确保请求体长度是正确的
        body_length = str(len(body.encode()))
        
        headers = {
            "user-agent": "Dart/3.2 (dart:io)",
            "accept-encoding": "gzip",
            "channel": "oppo",
            "os": "10/HUAWEI/ALN-AL10",
            "content-type": "application/json",
            "versioncode": "**********",
            "platform": "android",
            "app": "xinghuo",
            "cookie": f"account_id={self.auth_config.get('account_id')};ssoSessionId={self.auth_config.get('sso_session_id')};",
            "content-length": body_length,
            "host": "xinghuo.xfyun.cn",
            "usercookie": f"account_id={self.auth_config.get('account_id')};ssoSessionId={self.auth_config.get('sso_session_id')};",
            "clienttype": "4",
            "versionname": "4.0.31"
        }

        # 添加重试逻辑
        max_retries = 3
        retry_delay = 2  # 秒
        
        for attempt in range(max_retries):
            try:
                # 打印完整请求信息以便调试
                logger.info(f"[{self.plugin_name}] 发送请求: URL=https://xinghuo.xfyun.cn/rec/process_data")
                logger.info(f"[{self.plugin_name}] 请求头: {json.dumps(headers, indent=2)}")
                logger.info(f"[{self.plugin_name}] 请求体: {body}")
                
                # 使用httpx异步发送请求
                async with httpx.AsyncClient() as client:
                    response = await client.post(
                        "https://xinghuo.xfyun.cn/rec/process_data",
                        headers=headers,
                        content=body
                    )
                
                    logger.info(f"[{self.plugin_name}] 响应状态: {response.status_code}")
                    result = response.json()
                    logger.info(f"[{self.plugin_name}] 响应内容: {result}")
                    
                    # 检查是否成功
                    if result.get("code") == 0 and result.get("result"):
                        logger.info(f"[{self.plugin_name}] 成功获取绘图任务ID")
                        return result
                    else:
                        error_code = result.get("code")
                        logger.warning(f"[{self.plugin_name}] 请求未成功，错误码: {error_code}")
                        
                        if attempt < max_retries - 1:
                            # 如果不是最后一次尝试，等待后重试
                            logger.info(f"[{self.plugin_name}] 将在 {retry_delay} 秒后重试... (尝试 {attempt+1}/{max_retries})")
                            await asyncio.sleep(retry_delay)
                            retry_delay *= 2  # 指数退避策略
                        else:
                            # 最后一次尝试失败
                            logger.error(f"[{self.plugin_name}] 无法成功处理请求，达到最大重试次数")
                            return None
                
            except Exception as e:
                logger.error(f"[{self.plugin_name}] Process data request failed: {e}")
                
                if attempt < max_retries - 1:
                    # 如果不是最后一次尝试，等待后重试
                    logger.info(f"[{self.plugin_name}] 将在 {retry_delay} 秒后重试... (尝试 {attempt+1}/{max_retries})")
                    await asyncio.sleep(retry_delay)
                    retry_delay *= 2  # 指数退避策略
                else:
                    # 最后一次尝试失败
                    logger.error(f"[{self.plugin_name}] 无法成功处理请求，达到最大重试次数")
                    return None
        
        return None  # 所有重试都失败
        
    def _filter_sensitive_words(self, text: str) -> str:
        """过滤可能敏感的词语"""
        # 简单的替换策略，可以根据实际情况调整
        replacements = {
            "美女": "女性",
            "性感": "优雅",
            "裸体": "人物",
            "色情": "艺术",
            "暴力": "动作",
            "血腥": "场景"
        }
        
        result = text
        for word, replacement in replacements.items():
            result = result.replace(word, replacement)
            
        return result

    async def _chat_message(self, prompt: str, result: dict) -> str:
        """处理第二步请求,获取图片URL"""
        import httpx
        import base64
        
        try:
            # 使用固定的chatId和fd
            chat_id = "832772339"
            market_bot_id = "285878"
            
            logger.info(f"[{self.plugin_name}] 使用固定任务信息: chat_id={chat_id}, market_bot_id={market_bot_id}")
            
            # 完整的GtToken (这里省略展示)
            gt_token = "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"
            
            # 构建请求时不做人工计算，让httpx自动管理
            headers = {
                "user-agent": "Dart/3.2 (dart:io)",
                "cache-control": "no-cache",
                "accept": "text/event-stream",
                "accept-encoding": "gzip",
                "cookie": f"account_id={self.auth_config.get('account_id')};ssoSessionId={self.auth_config.get('sso_session_id')};",
                "host": "xinghuo.xfyun.cn",
                "clienttype": "4",
                "versioncode": "**********"
            }
            
            # 使用httpx的multipart/form-data原生支持
            form_data = {
                "chatId": chat_id,
                "fd": market_bot_id,
                "sid": "cht000a391d@hu1958fc80fd61312600",
                "clientType": "4",
                "text": f"画一张{prompt}",
                "quote": "",
                "pushMessage": "",
                "GtToken": gt_token,
                "isBot": "0",
                "edit": "false"
            }
            
            logger.info(f"[{self.plugin_name}] 开始第二步请求")
            
            async with httpx.AsyncClient(timeout=30.0) as client:
                # 使用httpx的文件上传方式发送表单
                response = await client.post(
                    "https://xinghuo.xfyun.cn/iflygpt-app/u/chat_message/v3/chat",
                    headers=headers,
                    data=form_data
                )
                
                logger.info(f"[{self.plugin_name}] 第二步响应状态: {response.status_code}")
                
                # 处理SSE响应
                image_url = None
                for line in response.text.split('\n'):
                    line = line.strip()
                    if not line:
                        continue
                        
                    if line.startswith("data:"):
                        data = line[5:].strip()
                        logger.debug(f"[{self.plugin_name}] SSE数据: {data}")
                        
                        if data.startswith("<sid>"):
                            continue
                            
                        # 检查是否包含图片URL
                        if "bXVsdGlfaW1hZ2VfdXJs" in data:  # "multi_image_url" 的base64部分
                            try:
                                # 直接解码base64数据
                                decoded = base64.b64decode(data).decode('utf-8')
                                logger.debug(f"[{self.plugin_name}] 解码数据: {decoded}")
                                
                                # 提取URL - 格式是: ```multi_image_url{"url":"https://..."}```
                                if "multi_image_url" in decoded:
                                    # 使用正则表达式提取JSON部分
                                    import re
                                    json_match = re.search(r'{.*}', decoded)
                                    if json_match:
                                        json_str = json_match.group(0)
                                        url_data = json.loads(json_str)
                                        image_url = url_data.get("url")
                                        
                                        if image_url:
                                            logger.info(f"[{self.plugin_name}] 成功获取图片URL: {image_url[:50]}...")
                                            break
                            except Exception as e:
                                logger.error(f"[{self.plugin_name}] 解析图片URL失败: {e}")
                                continue
                
                return image_url
                
        except (KeyError, IndexError) as e:
            logger.error(f"[{self.plugin_name}] 解析任务信息失败: {e}")
            return None
        except Exception as e:
            logger.error(f"[{self.plugin_name}] Chat message request failed: {e}")
            return None

    async def _download_image(self, url: str) -> bytes:
        """下载图片并返回二进制数据"""
        try:
            # 使用httpx代替aiohttp，并禁用SSL验证
            import httpx
            
            # 下载图片 - 禁用SSL验证
            async with httpx.AsyncClient(verify=False) as client:
                response = await client.get(url)
                
                if response.status_code != 200:
                    logger.error(f"[{self.plugin_name}] 下载图片失败: HTTP {response.status_code}")
                    return None
                    
                # 直接返回二进制数据
                image_data = response.content
                logger.info(f"[{self.plugin_name}] 图片已下载: {len(image_data)} 字节")
                return image_data
                
        except Exception as e:
            logger.error(f"[{self.plugin_name}] 下载图片失败: {e}")
            return None

    @on_text_message
    async def handle_text(self, bot, message):
        """处理文本消息"""
        if not self.enable:
            return
            
        content = str(message["Content"]).strip()
        wxid = message["FromWxid"]
        user_wxid = message["SenderWxid"]
        
        # 检查命令
        command = content.split(" ", 1)
        if command[0] not in self.command:
            return
            
        # 检查参数
        if len(command) == 1:
            await bot.send_at_message(
                wxid,
                self.command_format,
                [user_wxid]
            )
            return

        prompt = command[1].strip()
        
        try:
            # 发送处理中提示
            await bot.send_at_message(
                wxid,
                "🎨 正在绘制中，请稍候...",
                [user_wxid]
            )
            
            # 第一步请求
            result = await self._process_data(prompt)
            if not result or result.get("code") != 0:
                await bot.send_at_message(
                    wxid,
                    "❌ 处理请求失败，请稍后重试",
                    [user_wxid]
                )
                return
                
            logger.info(f"[{self.plugin_name}] 成功获取绘图任务ID")
            
            # 第二步请求
            image_url = await self._chat_message(prompt, result)
            if not image_url:
                await bot.send_at_message(
                    wxid,
                    "❌ 获取图片失败，请稍后重试",
                    [user_wxid]
                )
                return
                
            logger.info(f"[{self.plugin_name}] 成功获取图片URL: {image_url}")
            
            # 第三步 - 下载并发送图片
            image_data = await self._download_image(image_url)
            if not image_data:
                await bot.send_at_message(
                    wxid,
                    "❌ 下载图片失败，请稍后重试",
                    [user_wxid]
                )
                return
            
            # 直接发送原图
            client_msg_id, create_time, new_msg_id = await bot.send_image_message(
                wxid,
                image_data
            )
            
            if new_msg_id != 0 and client_msg_id:
                logger.info(f"[{self.plugin_name}] 图片发送成功")
            else:
                logger.error(f"[{self.plugin_name}] 图片发送失败")
                await bot.send_at_message(
                    wxid,
                    "❌ 发送图片失败，请稍后重试",
                    [user_wxid]
                )
            
        except Exception as e:
            logger.error(f"[{self.plugin_name}] Error: {e}")
            await bot.send_at_message(
                wxid,
                "❌ 处理过程中出现错误，请稍后重试",
                [user_wxid]
            )
            
    async def shutdown(self):
        """关闭时清理资源"""
        # 移除aiohttp相关清理，因为我们已经完全使用httpx
        # if self.session and not self.session.closed:
        #    await self.session.close() 