import os, time, httpx, asyncio, random, base64
from pathlib import Path
try: import tomllib
except: import tomli as tomllib
from utils.decorators import on_text_message
from utils.plugin_base import PluginBase
from WechatAPI import WechatAPIClient
class KlingAI(PluginBase):
    plugin_name = "KlingAI"
    def __init__(self):
        super().__init__(); self.temp_dir = Path("temp/KlingAI"); self.temp_dir.mkdir(parents=True, exist_ok=True)
        try: config = tomllib.load(open(f"plugins/{self.plugin_name}/config.toml", "rb")).get(self.plugin_name, {}) if os.path.exists(f"plugins/{self.plugin_name}/config.toml") else {}
        except: config = {}
        self.enable = config.get("enable", True); self.command = config.get("command", ["可灵", "可灵ai", "kl", "KL"]); self.command_format = config.get("command-format", "默认使用说明")
        ai_config = config.get("config", {}); self.aspect_ratio = ai_config.get("aspect_ratio", "9:16"); self.image_count = ai_config.get("image_count", "1"); self.kolors_version = ai_config.get("kolors_version", "1.5"); self.style = ai_config.get("style", "默认")
        api_config = config.get("api", {}); self.base_url = api_config.get("base_url", "https://klingai.kuaishou.com"); self.submit_endpoint = api_config.get("submit_endpoint", "/app/task/submit"); self.status_endpoint = api_config.get("status_endpoint", "/app/task/status")
        self.cookie = config.get("cookie", {}); self.user_last_request = {}; self.last_token_time = time.time(); self.tokens = 3; self.tokens_per_second = 0.05; self.bucket_size = 3
        self.international_enable = config.get("international_enable", True); self.international_command = config.get("international_command", ["可灵国际版", "可灵国际", "kli", "KLI"])
        international_api_config = config.get("international_api", {}); self.international_base_url = international_api_config.get("base_url", "https://klingai.com"); self.international_submit_endpoint = international_api_config.get("submit_endpoint", "/api/task/submit")
        self.international_auth = config.get("international_auth", {}); self.natural_response = config.get("natural_response", True)
        self.confirm_msgs = ["好的", "收到", "明白了", "嗯", "知道了", "行", "OK"]; self.error_msgs = ["这可把我难住了", "搞不定了", "这个有点难", "弄不了", "出问题了", "搞不出来", "这个不行"]; self.rate_limit_msgs = ["你搁这刷bug呢", "慢点慢点", "别这么急", "一个一个来", "太快了太快了", "歇会儿", "别刷了"]

    @on_text_message
    async def handle_text(self, bot, message):
        if not self.enable: return
        wxid, user_wxid, command = message["FromWxid"], message["SenderWxid"], str(message["Content"]).strip().split(" ", 1)
        async def process_command(is_international=False):
            if len(command) == 1: return await bot.send_at_message(wxid, self.command_format, [user_wxid])
            try:
                wait_time = self._check_user_limit(wxid, user_wxid)
                if wait_time > 0 or not self._acquire_token():
                    msg = random.choice(self.rate_limit_msgs) if self.natural_response else (f"请等待 {wait_time:.1f} 秒" if wait_time > 0 else "忙不过来了，歇会儿")
                    return await (bot.send_text_message(wxid, msg) if self.natural_response else bot.send_at_message(wxid, msg, [user_wxid]))
                prompt = command[1].strip()
                if self.natural_response: await bot.send_text_message(wxid, random.choice(self.confirm_msgs))
                image_path = await (self._process_international_drawing_request(prompt) if is_international else self._process_drawing_request(prompt))
                if image_path:
                    with open(image_path, "rb") as f: await bot.send_image_message(wxid, base64.b64encode(f.read()).decode('utf-8'))
                else:
                    title = "国际版画不出来，等会再试试" if is_international else "画不出来，等会再试试"
                    await (bot.send_text_message(wxid, random.choice(self.error_msgs)) if self.natural_response else bot.send_app_message(wxid, f'''<appmsg appid=""  sdkver="0"><title>{title}</title><type>57</type><refermsg><type>1</type><svrid>{message["NewMsgId"]}</svrid><fromusr>{user_wxid}</fromusr><chatusr>{wxid}</chatusr><displayname>{message.get("PushContent", "").split(" : ")[0]}</displayname><content>{prompt}</content></refermsg></appmsg>''', 57))
            except:
                msg = random.choice(self.error_msgs) if self.natural_response else "出问题了，等会再试试"
                await (bot.send_text_message(wxid, msg) if self.natural_response else bot.send_at_message(wxid, msg, [user_wxid]))
        if command[0] in self.command: await process_command()
        elif self.international_enable and command[0] in self.international_command: await process_command(True)

    async def _process_drawing_request(self, prompt):
        try:
            task_id = await self._submit_task(prompt)
            image_url = await self._poll_task_status(task_id) if task_id else ""
            image_path = os.path.join(self.temp_dir, f"{int(time.time())}_{prompt[:10].replace(' ', '_')}.png") if image_url else ""
            return image_path if image_path and await self._download_image(image_url, image_path) else ""
        except: return ""
    async def _submit_task(self, prompt):
        try:
            url_params = {"mod": "HUAWEI(ALN-AL10)", "appver": "2.2.10.126", "language": "zh-cn", "did_tag": "0", "egid": "DFP514B0C94F1B61D9141FC3B8FE74638C5038B1B252A5066284E218759EC6EE", "thermal": "10000", "net": "WIFI", "kcv": "5", "app": "0", "kpf": "ANDROID_PHONE", "ver": "2.2", "android_os": "0", "oDid": "ANDROID_c65c5f2e304e5870", "kpn": "KLING", "newOc": "OPPO", "androidApiLevel": "29", "country_code": "cn", "did_gt": "1741869521914", "sys": "ANDROID_10", "max_memory": "384", "client_key": "3c2cd3f3", "cold_launch_time_ms": "1741869521485", "oc": "OPPO", "sh": "2376", "sw": "1080", "c": "OPPO", "os": "android", "did": "ANDROID_c65c5f2e304e5870"}
            async with httpx.AsyncClient() as client:
                response = await client.post(f"{self.base_url}{self.submit_endpoint}?{'&'.join([f'{k}={v}' for k, v in url_params.items()])}", json={"callbackPayloads": [], "inputs": [], "arguments": [{"name": "prompt", "value": prompt}, {"name": "aspect_ratio", "value": self.aspect_ratio}, {"name": "imageCount", "value": self.image_count}, {"name": "biz", "value": "klingai"}, {"name": "style", "value": self.style}, {"name": "kolors_version", "value": self.kolors_version}], "type": "mmu_txt2img_aiweb"}, headers={"Connection": "keep-alive", "Content-Type": "application/json", "User-Agent": "kwai-android aegon/3.44.1", "Accept-Language": "zh-cn", "X-REQUESTID": f"{int(time.time()*1000)}{str(int(time.time()*10))[-4:]}", "Cookie": f"__NSWJ=;kuaishou.klingai.app_st={self.cookie.get('app_st', '')};userId={self.cookie.get('user_id', '')}"})
                return str(response.json().get("data", {}).get("task", {}).get("id", "")) if response.status_code == 200 and response.json().get("result") == 1 and response.json().get("status") == 200 else ""
        except: return ""

    async def _poll_task_status(self, task_id, max_retries=100, interval=2.0):
        try:
            url_params = {"taskId": task_id, "mod": "HUAWEI(ALN-AL10)", "appver": "2.2.10.126", "language": "zh-cn", "did_tag": "0", "egid": "DFP514B0C94F1B61D9141FC3B8FE74638C5038B1B252A5066284E218759EC6EE", "thermal": "10000", "net": "WIFI", "kcv": "5", "app": "0", "kpf": "ANDROID_PHONE", "ver": "2.2", "android_os": "0", "oDid": "ANDROID_c65c5f2e304e5870", "kpn": "KLING", "newOc": "OPPO", "androidApiLevel": "29", "country_code": "cn", "did_gt": "1741869521914", "sys": "ANDROID_10", "max_memory": "384", "client_key": "3c2cd3f3", "cold_launch_time_ms": "1741869521485", "oc": "OPPO", "sh": "2376", "sw": "1080", "c": "OPPO", "os": "android", "did": "ANDROID_c65c5f2e304e5870", "earphoneMode": "1"}
            for _ in range(max_retries):
                async with httpx.AsyncClient(timeout=30.0) as client:
                    response = await client.get(f"{self.base_url}{self.status_endpoint}?{'&'.join([f'{k}={v}' for k, v in url_params.items()])}", headers={"Connection": "keep-alive", "User-Agent": "kwai-android aegon/3.44.1", "Accept-Language": "zh-cn", "X-REQUESTID": f"{int(time.time()*1000)}{str(int(time.time()*10))[-4:]}", "Cookie": f"__NSWJ=;kuaishou.klingai.app_st={self.cookie.get('app_st', '')};userId={self.cookie.get('user_id', '')}"})
                    if response.status_code == 200:
                        status = response.json().get("data", {}).get("status", 0)
                        if status == 99: return (works := response.json().get("data", {}).get("works", [])) and works[0].get("resource", {}).get("resource", "") or ""
                        elif status not in [5, 10]: return ""
                await asyncio.sleep(interval)
            return ""
        except: return ""
    async def _download_image(self, url, file_path):
        try:
            async with httpx.AsyncClient(timeout=30.0) as client:
                response = await client.get(url, headers={"User-Agent": "Dalvik/2.1.0 (Linux; U; Android 10; ALN-AL10 Build/HUAWEIALN-AL10)", "Connection": "Keep-Alive", "Accept-Encoding": "gzip"})
                if response.status_code == 200:
                    with open(file_path, "wb") as f: f.write(response.content)
                    return True
            return False
        except: return False
    def _acquire_token(self):
        current_time = time.time(); self.tokens = min(self.bucket_size, self.tokens + (current_time - self.last_token_time) * self.tokens_per_second); self.last_token_time = current_time
        if self.tokens >= 1: self.tokens -= 1; return True
        return False
    def _check_user_limit(self, wxid, user_wxid):
        user_key, current_time = f"{wxid}_{user_wxid}", time.time(); elapsed = current_time - self.user_last_request.get(user_key, 0); self.user_last_request[user_key] = current_time
        return max(0, 30 - elapsed)

    async def _process_international_drawing_request(self, prompt):
        try:
            task_id = await self._submit_international_task(prompt)
            image_url = await self._poll_international_task_status(task_id) if task_id else ""
            image_path = os.path.join(self.temp_dir, f"intl_{int(time.time())}_{prompt[:10].replace(' ', '_')}.png") if image_url else ""
            return image_path if image_path and await self._download_image(image_url, image_path) else ""
        except: return ""
    async def _submit_international_task(self, prompt):
        try:
            auth = self.international_auth
            async with httpx.AsyncClient() as client:
                response = await client.post(f"{self.international_base_url}{self.international_submit_endpoint}?__NS_hxfalcon=HUDR_sFnX-DtsA0FXsbDPlXTMP-sk0issU-9b-1IBgPfX-wDoJJNSbTXrp9zMA6aemcVs6kSZai5tRu02GV8u7Pb41advhNIAwFYnIBTbWUak0se_kVS4O6t2dGwlwJ5lXmoEE6ldW0378q_LTHdM11uUDXvD5E9YjNpT-bX_hQif$HE_e0ff67020036158f023eaa9aeeacc22615aaabababaaae330711ea768f1376dbcfca3eaa30fd9571d0fd9543ab&caver=2", json={"type": "mmu_txt2img_aiweb", "inputs": [], "arguments": [{"name": "prompt", "value": prompt}, {"name": "aspect_ratio", "value": self.aspect_ratio}, {"name": "imageCount", "value": self.image_count}, {"name": "kolors_version", "value": self.kolors_version}, {"name": "style", "value": self.style}, {"name": "biz", "value": "klingai"}]}, headers={"Host": "klingai.com", "Connection": "keep-alive", "Accept": "application/json, text/plain, */*", "Accept-Language": "en", "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "Content-Type": "application/json", "Origin": "https://klingai.com", "X-Requested-With": "mark.via", "Sec-Fetch-Site": "same-origin", "Sec-Fetch-Mode": "cors", "Sec-Fetch-Dest": "empty", "Referer": "https://klingai.com/text-to-image/new", "Accept-Encoding": "gzip, deflate", "Cookie": f"weblogger_did=web_5434455154AC9AB0; __risk_web_device_id=u207y5ze1742175243903y91; did=web_20837da80a348f94b53562717fc82ff2559b; skip-background-questionnaire=true; dev-center-view-update-2025-01-07=true; _clck=vw6pi4%7C2%7Cfub%7C0%7C1902; reference-image-guide=true; motivate-publish-image=true; userId={auth.get('user_id', '')}; ksi18n.ai.portal_st={auth.get('ksi18n_ai_portal_st', '')}; ksi18n.ai.portal_ph={auth.get('ksi18n_ai_portal_ph', '')}; release-notes-2025-01-23=true; operation-popup-dialogs=true; _uetsid=3fef184003f111f08ba8fb7d17208bed; _uetvid=e9918d3002cf11f0bcd85d4c08744fb2; _clsk=jxx74m%7C1742300058496%7C4%7C0%7Cj.clarity.ms%2Fcollect"})
                return str(response.json().get("data", {}).get("task", {}).get("id", "")) if response.status_code == 200 and response.json().get("result") == 1 and response.json().get("status") == 200 else ""
        except: return ""
    async def _poll_international_task_status(self, task_id, max_retries=100, interval=2.0):
        try:
            auth = self.international_auth
            for _ in range(max_retries):
                try:
                    async with httpx.AsyncClient(timeout=30.0) as client:
                        response = await client.get(f"{self.international_base_url}/api/task/status?taskId={task_id}&__NS_hxfalcon=HUDR_sFnX-DtsA0FXsbDPlXTMP-sk0issU-9b-1IBgPfX-wDoJJNSbTXrp9zMA6aemcVs6kSZai5tRu02GV8u7Pb41advhNIAwFYnIBTbWUak0se_kVS4O6t2dGwlwJ5lXmoEE6ldW0378q_LTHdM11uUDXvD5E9YjNpT-bX_hQif$HE_e0ff67020036158f023eaa9aeeacc22615aaabababaaae330711ea768f1376dbcfca3eaa30fd9571d0fd9543ab&caver=2", headers={"Host": "klingai.com", "Connection": "keep-alive", "Accept": "application/json, text/plain, */*", "Accept-Language": "en", "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36", "Origin": "https://klingai.com", "X-Requested-With": "mark.via", "Sec-Fetch-Site": "same-origin", "Sec-Fetch-Mode": "cors", "Sec-Fetch-Dest": "empty", "Referer": "https://klingai.com/text-to-image/new", "Accept-Encoding": "gzip, deflate", "Cookie": f"weblogger_did=web_5434455154AC9AB0; __risk_web_device_id=u207y5ze1742175243903y91; did=web_20837da80a348f94b53562717fc82ff2559b; skip-background-questionnaire=true; dev-center-view-update-2025-01-07=true; _clck=vw6pi4%7C2%7Cfub%7C0%7C1902; reference-image-guide=true; motivate-publish-image=true; userId={auth.get('user_id', '')}; ksi18n.ai.portal_st={auth.get('ksi18n_ai_portal_st', '')}; ksi18n.ai.portal_ph={auth.get('ksi18n_ai_portal_ph', '')}; release-notes-2025-01-23=true; operation-popup-dialogs=true; _uetsid=3fef184003f111f08ba8fb7d17208bed; _uetvid=e9918d3002cf11f0bcd85d4c08744fb2; _clsk=jxx74m%7C1742300058496%7C4%7C0%7Cj.clarity.ms%2Fcollect"})
                        if response.status_code == 200:
                            status = response.json().get("data", {}).get("status", 0)
                            if status == 99: return (works := response.json().get("data", {}).get("works", [])) and works[0].get("resource", {}).get("resource", "") or ""
                            elif status not in [5, 10]: return ""
                except: pass
                await asyncio.sleep(interval)
            return ""
        except: return ""