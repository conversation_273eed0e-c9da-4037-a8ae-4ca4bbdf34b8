import asyncio
import time
from loguru import logger


class ConnectionMonitor:
    """
    连接监控器

    用于监控微信长连接状态，在连接断开时自动重连。
    如果多次重连失败，会触发心跳检测。
    注意：二次登录功能已移除。
    """

    def __init__(self, client, check_interval=60, max_reconnect_attempts=5, login_monitor=None, consecutive_failures_threshold=2):
        """
        初始化连接监控器

        Args:
            client: WechatAPIClient实例
            check_interval (int): 检查间隔（秒）
            max_reconnect_attempts (int): 最大重连尝试次数
            login_monitor: LoginMonitor实例，用于触发心跳检测
            consecutive_failures_threshold (int): 触发心跳检测的连续失败阈值
        """
        self.client = client
        self.check_interval = check_interval
        self.max_reconnect_attempts = max_reconnect_attempts
        self.login_monitor = login_monitor
        self.consecutive_failures_threshold = consecutive_failures_threshold
        self.is_running = False
        self.monitor_task = None
        self.reconnect_attempts = 0
        self.last_check_time = 0
        self.last_connected_time = 0
        self.consecutive_failures = 0  # 连续失败次数
        self.last_relogin_time = 0  # 上次心跳检测时间

    async def start(self):
        """启动连接监控"""
        print("\n\n===== ConnectionMonitor.start() 方法被调用 =====")
        print(f"===== 当前时间: {time.strftime('%Y-%m-%d %H:%M:%S')} =====")
        print(f"===== self.is_running: {self.is_running} =====")
        print(f"===== self.login_monitor: {self.login_monitor} =====")
        print(f"===== self.login_monitor 类型: {type(self.login_monitor)} =====")

        if self.is_running:
            print("===== 连接监控已在运行，不再重复启动 =====")
            logger.debug("连接监控已在运行")
            return

        self.is_running = True
        print("===== 设置 self.is_running = True =====")

        self.last_check_time = time.time()
        print(f"===== 设置 last_check_time: {time.strftime('%Y-%m-%d %H:%M:%S', time.localtime(self.last_check_time))} =====")

        self.monitor_task = asyncio.create_task(self._monitor_loop())
        print("===== monitor_task 创建成功 =====")

        logger.info("连接监控已启动")
        print("===== 连接监控已启动 =====")

        # 立即执行一次心跳检测
        if self.login_monitor:
            print("===== 立即执行一次心跳检测 =====")
            asyncio.create_task(self._test_heartbeat())
        else:
            print("===== 警告: self.login_monitor 为 None，无法执行心跳检测 =====")

    async def _test_heartbeat(self):
        """测试心跳功能"""
        print("\n\n===== _test_heartbeat 方法被调用 =====")
        print(f"===== 当前时间: {time.strftime('%Y-%m-%d %H:%M:%S')} =====")

        if not self.login_monitor:
            print("===== 错误: self.login_monitor 为 None =====")
            return

        print(f"===== self.login_monitor 类型: {type(self.login_monitor)} =====")
        print(f"===== self.login_monitor.client 类型: {type(self.login_monitor.client)} =====")

        # 直接调用心跳方法
        try:
            print("===== 直接调用心跳方法 =====")
            success = await self.login_monitor.force_relogin()
            print(f"===== 心跳检测返回结果: {success} =====")

            if success:
                print("===== 心跳检测成功 =====")
                logger.warning("===== 心跳检测成功 =====")
            else:
                print("===== 心跳检测失败 =====")
                logger.warning("===== 心跳检测失败 =====")
        except Exception as e:
            print(f"===== 心跳检测异常: {e} =====")
            import traceback
            print(f"===== 异常堆栈: {traceback.format_exc()} =====")
            logger.error(f"心跳检测异常: {e}")

    async def stop(self):
        """停止连接监控"""
        if not self.is_running:
            logger.debug("连接监控未在运行")
            return

        self.is_running = False
        if self.monitor_task:
            self.monitor_task.cancel()
            try:
                await self.monitor_task
            except asyncio.CancelledError:
                pass
        logger.info("连接监控已停止")

    async def _monitor_loop(self):
        """监控循环"""
        # 测试模式：每3分钟直接触发一次心跳检测
        test_mode = True  # 设置为True开启测试模式
        test_interval = 180  # 3分钟

        print("\n\n===== ConnectionMonitor._monitor_loop 方法被调用 =====")
        print(f"===== 当前时间: {time.strftime('%Y-%m-%d %H:%M:%S')} =====")
        print(f"===== 测试模式: {test_mode} =====")
        print(f"===== self.login_monitor: {self.login_monitor} =====")
        print(f"===== self.login_monitor 类型: {type(self.login_monitor)} =====")
        if self.login_monitor:
            print(f"===== self.login_monitor.client 类型: {type(self.login_monitor.client)} =====")
            print(f"===== self.login_monitor.client.wxid: {self.login_monitor.client.wxid} =====")
            print("===== 二次登录功能已移除，改为心跳检测 =====")

        if test_mode:
            logger.warning(f"===== 连接监控器已启动，测试模式：将每{test_interval//60}分钟直接触发一次心跳检测 =====")

        last_test_time = 0

        while self.is_running:
            try:
                # 检查连接状态
                status = await self.client.check_connection_status()
                self.last_check_time = time.time()

                if status.get("connected", False):
                    # 连接正常
                    self.last_connected_time = time.time()
                    self.reconnect_attempts = 0
                    logger.debug("连接状态正常")

                    # 测试模式：直接触发心跳检测
                    if test_mode and self.login_monitor:
                        current_time = time.time()
                        time_since_last_test = current_time - last_test_time

                        if last_test_time == 0 or time_since_last_test > test_interval:
                            print(f"\n\n===== 测试模式：直接触发心跳检测，当前时间: {time.strftime('%Y-%m-%d %H:%M:%S')} =====")
                            logger.warning("===== 测试模式：直接触发心跳检测 =====")

                            try:
                                print("===== 调用 login_monitor.force_relogin() =====")
                                success = await self.login_monitor.force_relogin()
                                print(f"===== force_relogin 返回结果: {success} =====")

                                if success:
                                    print("===== 心跳检测成功 =====")
                                    logger.warning("===== 心跳检测成功 =====")
                                else:
                                    print("===== 心跳检测失败 =====")
                                    logger.warning("===== 心跳检测失败 =====")
                            except Exception as e:
                                print(f"===== 心跳检测异常: {e} =====")
                                import traceback
                                print(f"===== 异常堆栈: {traceback.format_exc()} =====")
                                logger.error(f"心跳检测异常: {e}")

                            last_test_time = current_time
                            print(f"===== 等待{test_interval//60}分钟后执行下一次心跳检测 =====")
                else:
                    # 连接已断开
                    logger.warning("连接已断开，尝试重新连接")
                    await self._reconnect()
            except Exception as e:
                logger.error(f"连接监控异常: {e}")
                await self._reconnect()

            # 等待下一次检查
            await asyncio.sleep(self.check_interval)

    async def _reconnect(self):
        """尝试重新连接"""
        if self.reconnect_attempts >= self.max_reconnect_attempts:
            logger.error(f"已达到最大重连尝试次数 ({self.max_reconnect_attempts})，停止重连")
            self.consecutive_failures += 1  # 增加连续失败次数

            # 如果有登录监控器，尝试心跳检测
            if self.login_monitor and self.consecutive_failures >= self.consecutive_failures_threshold:
                # 检查距离上次心跳检测的时间，避免频繁触发
                current_time = time.time()
                time_since_last_relogin = current_time - self.last_relogin_time

                # 如果距离上次心跳检测超过30分钟，才触发新的心跳检测
                if self.last_relogin_time == 0 or time_since_last_relogin > 1800:
                    logger.warning(f"多次重连失败（连续失败{self.consecutive_failures}次），尝试心跳检测")
                    success = await self.login_monitor.force_relogin()
                    if success:
                        logger.success("心跳检测成功，重置连接状态")
                        self.consecutive_failures = 0  # 重置连续失败次数
                        self.reconnect_attempts = 0  # 重置重连尝试次数
                        self.last_relogin_time = current_time
                    else:
                        logger.error("心跳检测失败")
                else:
                    logger.warning(f"距离上次心跳检测仅{int(time_since_last_relogin)}秒，暂不触发新的心跳检测")

            return False

        self.reconnect_attempts += 1
        logger.info(f"第 {self.reconnect_attempts} 次尝试重连")

        try:
            # 使用客户端的reconnect方法，它已经包含了停止和重启心跳的逻辑
            success = await self.client.reconnect()
            if success:
                logger.success("重新连接成功")
                self.last_connected_time = time.time()
                self.consecutive_failures = 0  # 重置连续失败次数
                return True
            else:
                logger.error("重新连接失败")
                self.consecutive_failures += 1  # 增加连续失败次数
        except Exception as e:
            logger.error(f"重连过程中发生错误: {e}")
            self.consecutive_failures += 1  # 增加连续失败次数

        # 如果重连失败，等待一段时间后再次尝试
        wait_time = min(30, self.reconnect_attempts * 5)
        logger.info(f"等待 {wait_time} 秒后再次尝试重连")
        await asyncio.sleep(wait_time)
        return False

    def get_status(self):
        """
        获取监控状态

        Returns:
            dict: 监控状态信息
        """
        current_time = time.time()
        return {
            "is_running": self.is_running,
            "reconnect_attempts": self.reconnect_attempts,
            "consecutive_failures": self.consecutive_failures,
            "last_check_time": self.last_check_time,
            "last_connected_time": self.last_connected_time,
            "last_heartbeat_time": self.last_relogin_time,
            "time_since_last_heartbeat": current_time - self.last_relogin_time if self.last_relogin_time > 0 else None,
            "uptime": current_time - self.last_connected_time if self.last_connected_time > 0 else 0
        }
