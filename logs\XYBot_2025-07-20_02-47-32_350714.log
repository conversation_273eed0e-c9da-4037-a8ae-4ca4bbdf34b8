2025-07-20 02:47:33 | SUCCESS | 读取主设置成功
2025-07-20 02:47:33 | DEBUG | 最终使用的 Redis 主机地址: 127.0.0.1
2025-07-20 02:47:33 | INFO | 2025/07/20 02:47:33 GetRedisAddr: 127.0.0.1:6379
2025-07-20 02:47:33 | INFO | 2025/07/20 02:47:33 Redis连接成功 | 地址:127.0.0.1:6379 DB:0
2025-07-20 02:47:33 | INFO | 2025/07/20 02:47:33 Server start at :9000
2025-07-20 02:47:33 | SUCCESS | WechatAPI服务已启动
2025-07-20 02:47:34 | INFO | 登录账号信息: wxid: wxid_4usgcju5ey9q29  昵称: 瑶瑶  微信号: Today-KFC  手机号: None
2025-07-20 02:47:34 | INFO | 登录设备信息: device_name: <PERSON>'s Pad  device_id: 49d050a7e94d1ecc40790d74b1c4369f
2025-07-20 02:47:34 | SUCCESS | 登录成功
2025-07-20 02:47:34 | SUCCESS | 已开启自动心跳
2025-07-20 02:47:34 | INFO | 成功加载表情映射文件，共 515 条记录
2025-07-20 02:47:34 | SUCCESS | 数据库初始化成功
2025-07-20 02:47:34 | SUCCESS | 定时任务已启动
2025-07-20 02:47:34 | SUCCESS | 已加载插件: []
2025-07-20 02:47:34 | INFO | 处理堆积消息中
2025-07-20 02:47:34 | SUCCESS | 处理堆积消息完毕
2025-07-20 02:47:34 | SUCCESS | 开始处理消息
