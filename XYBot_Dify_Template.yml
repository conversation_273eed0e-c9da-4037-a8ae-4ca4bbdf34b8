app:
  description: 唱舞全明星游戏客服瑶瑶
  icon: 💃
  icon_background: '#FFB6C1'
  mode: advanced-chat
  name: 瑶瑶
  use_icon_as_answer_icon: true
kind: app
version: 0.1.5
workflow:
  conversation_variables: [ ]
  environment_variables: [ ]
  features:
    file_upload:
      allowed_file_extensions: [ ]
      allowed_file_types:
        - image
      allowed_file_upload_methods:
        - remote_url
        - local_file
      enabled: true
      fileUploadConfig:
        audio_file_size_limit: 50
        batch_count_limit: 5
        file_size_limit: 15
        image_file_size_limit: 10
        video_file_size_limit: 100
        workflow_file_upload_limit: 10
      image:
        enabled: true
        number_limits: 3
        transfer_methods:
          - local_file
          - remote_url
      number_limits: 1
    opening_statement: '你好呀,我是唱舞全明星的客服瑶瑶~ 有什么可以帮助你的吗? 💃✨'
    retriever_resource:
      enabled: true
    sensitive_word_avoidance:
      enabled: true
    speech_to_text:
      enabled: true
    suggested_questions: [ ]
    suggested_questions_after_answer:
      enabled: false
    text_to_speech:
      enabled: true
      language: zh-Hans
      voice: 'qingniandaxuesheng'
  graph:
    edges:
      - data:
          isInIteration: false
          sourceType: llm
          targetType: answer
        id: 1738917745853-source-1738918123165-target
        source: '1738917745853'
        sourceHandle: source
        target: '1738918123165'
        targetHandle: target
        type: custom
        zIndex: 0
      - data:
          isInIteration: false
          sourceType: start
          targetType: if-else
        id: 1738915019767-source-1739252680159-target
        source: '1738915019767'
        sourceHandle: source
        target: '1739252680159'
        targetHandle: target
        type: custom
        zIndex: 0
      - data:
          isInIteration: false
          sourceType: if-else
          targetType: llm
        id: 1739252680159-3a9541e0-b608-4bb5-a027-def256809e7a-1739252840296-target
        source: '1739252680159'
        sourceHandle: 3a9541e0-b608-4bb5-a027-def256809e7a
        target: '1739252840296'
        targetHandle: target
        type: custom
        zIndex: 0
      - data:
          isInIteration: false
          sourceType: llm
          targetType: answer
        id: 1739252840296-source-1739252888579-target
        source: '1739252840296'
        sourceHandle: source
        target: '1739252888579'
        targetHandle: target
        type: custom
        zIndex: 0
      - data:
          isInIteration: false
          sourceType: list-operator
          targetType: tool
        id: 1739254087701-source-1739252789163-target
        source: '1739254087701'
        sourceHandle: source
        target: '1739252789163'
        targetHandle: target
        type: custom
        zIndex: 0
      - data:
          isInIteration: false
          sourceType: if-else
          targetType: list-operator
        id: 1739252680159-458858e2-e643-442d-9bfd-2ef164189378-1739254087701-target
        source: '1739252680159'
        sourceHandle: 458858e2-e643-442d-9bfd-2ef164189378
        target: '1739254087701'
        targetHandle: target
        type: custom
        zIndex: 0
      - data:
          isInIteration: false
          sourceType: if-else
          targetType: llm
        id: 1739252680159-true-1738917745853-target
        source: '1739252680159'
        sourceHandle: 'true'
        target: '1738917745853'
        targetHandle: target
        type: custom
        zIndex: 0
      - data:
          isInIteration: false
          sourceType: tool
          targetType: llm
        id: 1739252789163-source-1739255059499-target
        source: '1739252789163'
        sourceHandle: source
        target: '1739255059499'
        targetHandle: target
        type: custom
        zIndex: 0
      - data:
          isInIteration: false
          sourceType: llm
          targetType: tool
        id: 1739255059499-source-1739257224364-target
        source: '1739255059499'
        sourceHandle: source
        target: '1739257224364'
        targetHandle: target
        type: custom
        zIndex: 0
      - data:
          isInIteration: false
          sourceType: tool
          targetType: answer
        id: 1739257224364-source-1739288133732-target
        source: '1739257224364'
        sourceHandle: source
        target: '1739288133732'
        targetHandle: target
        type: custom
        zIndex: 0
    nodes:
      - data:
          desc: ''
          selected: false
          title: 开始
          type: start
          variables: [ ]
        height: 54
        id: '1738915019767'
        position:
          x: -262.276922445997
          y: 406.16303131970574
        positionAbsolute:
          x: -262.276922445997
          y: 406.16303131970574
        selected: false
        sourcePosition: right
        targetPosition: left
        type: custom
        width: 244
      - data:
          context:
            enabled: false
            variable_selector: [ ]
          desc: ''
          memory:
            query_prompt_template: '{{#sys.query#}}

            '
            role_prefix:
              assistant: ''
              user: ''
            window:
              enabled: false
              size: 50
          model:
            completion_params:
              max_tokens: 4096
              temperature: 0.7
            mode: chat
            name: gpt-3.5-turbo
            provider: openai_api_compatible
          prompt_template:
            - edition_type: basic
              id: 6de35274-53f5-4e34-89c5-a9c30ffb5f64
              role: system
              text: '你是唱舞全明星游戏的客服人员瑶瑶,具备以下特点:

                # 角色设定
                1. 基本信息
                - 名字: 瑶瑶
                - 身份: 唱舞全明星官方客服
                - 性格: 活泼可爱、专业负责、热情周到
                
                2. 专业背景
                - 精通唱舞全明星游戏的所有玩法和规则
                - 熟悉游戏内所有道具、商城、活动等内容
                - 了解常见问题的解决方案
                - 具备基础的技术支持能力
                
                3. 服务特点
                - 使用可爱活泼的语气,适当使用表情符号
                - 称呼玩家为"亲"或"舞友"
                - 专业解答游戏相关问题
                - 积极处理玩家反馈
                - 及时跟进问题解决进度
                
                4. 回答准则
                - 始终保持专业和耐心
                - 优先解决玩家的核心问题
                - 提供清晰的解决步骤
                - 遇到不确定的问题及时报备
                - 注意保护玩家隐私
                
                5. 常用语气词和表情
                - 问候语: "亲爱的舞友你好呀~ 瑶瑶很高兴为你服务~"
                - 结束语: "还有其他问题随时找瑶瑶哦~"
                - 表情: 😊 💃 ✨ 💕 ⭐️
                
                6. 专业知识范围
                - 游戏基础玩法
                - 商城道具系统
                - 活动规则说明
                - 账号安全相关
                - 充值消费问题
                - 技术故障处理
                - 举报投诉处理
                
                7. 禁止事项
                - 不能透露其他玩家信息
                - 不能承诺无法确定的事情
                - 不能进行游戏外的交易
                - 不能提供违规操作建议

                请始终以这个身份和设定来回答问题,保持活泼可爱但专业的形象。
                
                对于每个问题的回答,都要包含:
                1. 亲切的称呼
                2. 专业的解答
                3. 温暖的关怀
                4. 可爱的表情
                5. 友好的结束语'
          selected: false
          title: LLM1
          type: llm
          variables: [ ]
          vision:
            configs:
              detail: low
              variable_selector:
                - sys
                - files
            enabled: false
        height: 98
        id: '1738917745853'
        position:
          x: 500.2723150319407
          y: 356.991212440781
        positionAbsolute:
          x: 500.2723150319407
          y: 356.991212440781
        selected: false
        sourcePosition: right
        targetPosition: left
        type: custom
        width: 244
      - data:
          answer: '{{#1738917745853.text#}}'
          desc: ''
          selected: false
          title: 直接回复
          type: answer
          variables: [ ]
        height: 103
        id: '1738918123165'
        position:
          x: 829.4029907903282
          y: 356.991212440781
        positionAbsolute:
          x: 829.4029907903282
          y: 356.991212440781
        selected: false
        sourcePosition: right
        targetPosition: left
        type: custom
        width: 244
      - data:
          cases:
            - case_id: 'true'
              conditions:
                - comparison_operator: empty
                  id: a6c51713-13ff-4c59-92ad-df1af61aa759
                  value: ''
                  varType: array[file]
                  variable_selector:
                    - sys
                    - files
              id: 'true'
              logical_operator: or
            - case_id: 3a9541e0-b608-4bb5-a027-def256809e7a
              conditions:
                - comparison_operator: contains
                  id: dda26e42-905b-4503-9e7e-aefe52b20e7e
                  sub_variable_condition:
                    case_id: a3472907-6671-4ff6-a40f-420943e154f3
                    conditions:
                      - comparison_operator: in
                        id: f2c55058-eabf-4fde-a2da-e6c07e64246c
                        key: type
                        value:
                          - image
                        varType: string
                    logical_operator: and
                  value: ''
                  varType: array[file]
                  variable_selector:
                    - sys
                    - files
              id: 3a9541e0-b608-4bb5-a027-def256809e7a
              logical_operator: and
            - case_id: 458858e2-e643-442d-9bfd-2ef164189378
              conditions:
                - comparison_operator: contains
                  id: d5b09e7e-a8ea-4018-8650-947d6d9b0ca1
                  sub_variable_condition:
                    case_id: 1e38440a-5660-41d5-9be3-7cfafa0a8b6a
                    conditions:
                      - comparison_operator: in
                        id: 864efcf6-b80a-4c3b-8842-b3c7c66074cb
                        key: type
                        value:
                          - audio
                        varType: string
                    logical_operator: and
                  value: ''
                  varType: array[file]
                  variable_selector:
                    - sys
                    - files
              id: 458858e2-e643-442d-9bfd-2ef164189378
              logical_operator: and
            - case_id: c47afde9-f052-4d4d-9efe-f498b3f94a80
              conditions:
                - comparison_operator: contains
                  id: 1a5eab00-9ed2-488c-8eb3-99d902c33ac3
                  sub_variable_condition:
                    case_id: b4eb1a99-7840-4d62-bffe-e40a073db948
                    conditions:
                      - comparison_operator: in
                        id: 280c0d9a-7e77-4c69-902c-36e9ccc466be
                        key: type
                        value:
                          - document
                        varType: string
                    logical_operator: and
                  value: ''
                  varType: array[file]
                  variable_selector:
                    - sys
                    - files
              logical_operator: and
            - case_id: 5e77a038-54bc-45a9-b99f-1606c8d13d6a
              conditions:
                - comparison_operator: contains
                  id: 33cb4ff7-f570-46b8-a358-2dc44e735754
                  sub_variable_condition:
                    case_id: 84bf66f6-5445-46ce-b036-90207abb4a98
                    conditions:
                      - comparison_operator: in
                        id: dc9f8cb7-668b-4545-b924-f58e6bbd4436
                        key: type
                        value:
                          - video
                        varType: string
                    logical_operator: and
                  value: ''
                  varType: array[file]
                  variable_selector:
                    - sys
                    - files
              logical_operator: and
          desc: ''
          selected: false
          title: 条件分支
          type: if-else
        height: 414
        id: '1739252680159'
        position:
          x: 92.72226561832565
          y: 406.16303131970574
        positionAbsolute:
          x: 92.72226561832565
          y: 406.16303131970574
        selected: false
        sourcePosition: right
        targetPosition: left
        type: custom
        width: 244
      - data:
          desc: ''
          provider_id: audio
          provider_name: audio
          provider_type: builtin
          selected: false
          title: Speech To Text
          tool_configurations:
            model: openai_api_compatible#step-asr
          tool_label: Speech To Text
          tool_name: asr
          tool_parameters:
            audio_file:
              type: variable
              value:
                - '1739254087701'
                - first_record
          type: tool
        height: 90
        id: '1739252789163'
        position:
          x: 818.0854081792563
          y: 637.9065614866665
        positionAbsolute:
          x: 818.0854081792563
          y: 637.9065614866665
        selected: false
        sourcePosition: right
        targetPosition: left
        type: custom
        width: 244
      - data:
          context:
            enabled: false
            variable_selector: [ ]
          desc: ''
          memory:
            query_prompt_template: '{{#sys.query#}}'
            role_prefix:
              assistant: ''
              user: ''
            window:
              enabled: false
              size: 50
          model:
            completion_params:
              temperature: 0.7
            mode: chat
            name: gpt-4-vision-preview
            provider: openai_api_compatible
          prompt_template:
            - id: 230c6990-e5b0-41d7-9dc8-70782d28bd8d
              role: system
              text: '你是游戏客服瑶瑶,请以专业、可爱的方式描述图片内容,并给出相关的游戏建议。

                回答要点:
                1. 准确描述图片内容
                2. 关联游戏相关内容
                3. 提供专业建议
                4. 使用活泼的语气
                5. 添加可爱的表情'
          selected: false
          title: 支持图片输入的LLM
          type: llm
          variables: [ ]
          vision:
            configs:
              detail: high
              variable_selector:
                - sys
                - files
            enabled: true
        height: 98
        id: '1739252840296'
        position:
          x: 507.5756009542859
          y: 498.0040880918219
        positionAbsolute:
          x: 507.5756009542859
          y: 498.0040880918219
        selected: false
        sourcePosition: right
        targetPosition: left
        type: custom
        width: 244
      - data:
          answer: '{{#1739252840296.text#}}'
          desc: ''
          selected: false
          title: 直接回复 3
          type: answer
          variables: [ ]
        height: 103
        id: '1739252888579'
        position:
          x: 818.0854081792563
          y: 498.0040880918219
        positionAbsolute:
          x: 818.0854081792563
          y: 498.0040880918219
        selected: false
        sourcePosition: right
        targetPosition: left
        type: custom
        width: 244
      - data:
          desc: ''
          extract_by:
            enabled: true
            serial: '1'
          filter_by:
            conditions:
              - comparison_operator: contains
                key: name
                value: ''
            enabled: false
          item_var_type: file
          limit:
            enabled: false
            size: 1
          order_by:
            enabled: false
            key: ''
            value: asc
          selected: false
          title: 列表操作
          type: list-operator
          var_type: array[file]
          variable:
            - sys
            - files
        height: 92
        id: '1739254087701'
        position:
          x: 507.5756009542859
          y: 630.2109361546167
        positionAbsolute:
          x: 507.5756009542859
          y: 630.2109361546167
        selected: false
        sourcePosition: right
        targetPosition: left
        type: custom
        width: 244
      - data:
          context:
            enabled: false
            variable_selector: [ ]
          desc: ''
          memory:
            query_prompt_template: '{{#sys.query#}}

            {{#1739252789163.text#}}'
            role_prefix:
              assistant: ''
              user: ''
            window:
              enabled: false
              size: 50
          model:
            completion_params:
              temperature: 0.7
            mode: chat
            name: deepseek-v3
            provider: openai_api_compatible
          prompt_template:
            - id: 6291138d-3a76-46e1-a7e0-d2670d6352f5
              role: system
              text: ''
          selected: true
          title: LLM
          type: llm
          variables: [ ]
          vision:
            enabled: false
        height: 98
        id: '1739255059499'
        position:
          x: 1115.57540385159
          y: 637.9065614866665
        positionAbsolute:
          x: 1115.57540385159
          y: 637.9065614866665
        selected: true
        sourcePosition: right
        targetPosition: left
        type: custom
        width: 244
      - data:
          desc: ''
          provider_id: audio
          provider_name: audio
          provider_type: builtin
          selected: false
          title: Text To Speech
          tool_configurations:
            model: openai_api_compatible#step-tts-mini
            voice#openai#tts-1: null
            voice#openai#tts-1-hd: null
            voice#openai_api_compatible#step-tts-mini: qingniandaxuesheng
            voice#siliconflow#fishaudio/fish-speech-1.4: null
            voice#siliconflow#fishaudio/fish-speech-1.5: null
          tool_label: Text To Speech
          tool_name: tts
          tool_parameters:
            text:
              type: mixed
              value: '{{#1739255059499.text#}}'
          type: tool
        height: 220
        id: '1739257224364'
        position:
          x: 1397.2265350019834
          y: 637.9065614866665
        positionAbsolute:
          x: 1397.2265350019834
          y: 637.9065614866665
        selected: false
        sourcePosition: right
        targetPosition: left
        type: custom
        width: 244
      - data:
          answer: '{{#1739257224364.files#}}'
          desc: ''
          selected: false
          title: 直接回复 3
          type: answer
          variables: [ ]
        height: 103
        id: '1739288133732'
        position:
          x: 1701.2265350019834
          y: 637.9065614866665
        positionAbsolute:
          x: 1701.2265350019834
          y: 637.9065614866665
        selected: false
        sourcePosition: right
        targetPosition: left
        type: custom
        width: 244
    viewport:
      x: 49.25140302119502
      y: -160.44690452784948
      zoom: 0.6997797002524726
