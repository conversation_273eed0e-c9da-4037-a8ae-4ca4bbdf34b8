"""
临时文件管理器初始化模块
在主程序启动时自动初始化临时文件管理
"""

import os
import tomllib
from pathlib import Path
from utils.temp_file_manager import temp_manager, CleanupRule
from loguru import logger


def init_temp_file_manager():
    """初始化临时文件管理器"""
    try:
        # 读取配置文件
        config_path = Path("plugins/TempFileManager/config.toml")
        config = {}
        
        if config_path.exists():
            try:
                with open(config_path, "rb") as f:
                    config = tomllib.load(f)
                logger.info("[TempFileManager] 已加载配置文件")
            except Exception as e:
                logger.warning(f"[TempFileManager] 读取配置文件失败，使用默认配置: {e}")
        else:
            logger.info("[TempFileManager] 配置文件不存在，使用默认配置")
        
        # 应用配置
        _apply_config(config)
        
        # 确保temp目录存在
        temp_dir = Path("temp")
        temp_dir.mkdir(exist_ok=True)
        
        # 执行一次初始清理
        logger.info("[TempFileManager] 执行初始清理...")
        results = temp_manager.cleanup_all_temp_files()
        
        if results:
            total_cleaned = sum(r["cleaned"] for r in results.values())
            total_size = sum(r["size_mb"] for r in results.values())
            logger.info(f"[TempFileManager] 初始清理完成: 删除 {total_cleaned} 个文件，释放 {total_size:.2f}MB 空间")
        else:
            logger.info("[TempFileManager] 初始清理完成: 没有需要清理的文件")
        
        logger.info("[TempFileManager] 临时文件管理器初始化完成")
        
    except Exception as e:
        logger.error(f"[TempFileManager] 初始化失败: {e}")


def _apply_config(config: dict):
    """应用配置到临时文件管理器"""
    try:
        cleanup_config = config.get("cleanup", {})
        rules_config = cleanup_config.get("rules", {})
        
        # 更新清理间隔
        if "auto_cleanup_interval" in cleanup_config:
            temp_manager.cleanup_interval = cleanup_config["auto_cleanup_interval"]
            logger.debug(f"[TempFileManager] 设置清理间隔: {temp_manager.cleanup_interval}秒")
        
        # 更新清理规则
        if rules_config:
            _update_cleanup_rules(rules_config)
        
    except Exception as e:
        logger.error(f"[TempFileManager] 应用配置失败: {e}")


def _update_cleanup_rules(rules_config: dict):
    """更新清理规则"""
    try:
        # 默认规则
        if "default_max_age" in rules_config:
            temp_manager.add_cleanup_rule("default", CleanupRule(
                max_age_hours=rules_config["default_max_age"],
                file_patterns=["*"]
            ))
        
        # 图片文件规则
        if "images_max_age" in rules_config:
            temp_manager.add_cleanup_rule("images", CleanupRule(
                max_age_hours=rules_config["images_max_age"],
                file_patterns=["*.jpg", "*.jpeg", "*.png", "*.gif", "*.webp", "*.bmp", "*.avif"]
            ))
        
        # 视频文件规则
        if "videos_max_age" in rules_config:
            temp_manager.add_cleanup_rule("videos", CleanupRule(
                max_age_hours=rules_config["videos_max_age"],
                file_patterns=["*.mp4", "*.avi", "*.mov", "*.mkv", "*.flv", "*.wmv"]
            ))
        
        # 音频文件规则
        if "audio_max_age" in rules_config:
            temp_manager.add_cleanup_rule("audio", CleanupRule(
                max_age_hours=rules_config["audio_max_age"],
                file_patterns=["*.mp3", "*.wav", "*.amr", "*.silk", "*.m4a", "*.aac"]
            ))
        
        # 临时文件规则
        if "temp_files_max_age" in rules_config:
            temp_manager.add_cleanup_rule("temp", CleanupRule(
                max_age_hours=rules_config["temp_files_max_age"],
                file_patterns=["*.tmp", "*.temp", "*.cache", "*.log"]
            ))
        
        # 大文件规则
        if "large_files_max_age" in rules_config:
            temp_manager.add_cleanup_rule("large_files", CleanupRule(
                max_age_hours=rules_config["large_files_max_age"],
                file_patterns=["*"],
                min_size_mb=50.0
            ))
        
        logger.debug("[TempFileManager] 清理规则已更新")
        
    except Exception as e:
        logger.error(f"[TempFileManager] 更新清理规则失败: {e}")


def get_temp_manager_status():
    """获取临时文件管理器状态"""
    try:
        from utils.temp_file_manager import get_temp_stats
        
        stats = get_temp_stats()
        status = {
            "enabled": True,
            "cleanup_interval": temp_manager.cleanup_interval,
            "last_cleanup": temp_manager.last_cleanup,
            "active_files_count": len(temp_manager.active_files),
            "directories": stats
        }
        
        return status
        
    except Exception as e:
        logger.error(f"[TempFileManager] 获取状态失败: {e}")
        return {"enabled": False, "error": str(e)}


# 自动初始化（当模块被导入时）
if __name__ != "__main__":
    # 延迟初始化，避免在导入时立即执行
    import threading
    
    def delayed_init():
        import time
        time.sleep(5)  # 等待5秒后初始化
        init_temp_file_manager()
    
    init_thread = threading.Thread(target=delayed_init, daemon=True)
    init_thread.start()
