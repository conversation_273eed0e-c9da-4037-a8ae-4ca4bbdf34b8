import importlib
import inspect
import os
import sys
import tomllib
import traceback
from typing import Dict, Type, List, Union

from loguru import logger

from WechatAPI import WechatAPIClient
from .event_manager import EventManager
from .plugin_base import PluginBase


class PluginManager:
    def __init__(self):
        self.plugins: Dict[str, PluginBase] = {}
        self.plugin_classes: Dict[str, Type[PluginBase]] = {}
        self.plugin_info: Dict[str, dict] = {}  # 新增：存储所有插件信息

        with open("main_config.toml", "rb") as f:
            main_config = tomllib.load(f)

        self.excluded_plugins = main_config["XYBot"]["disabled-plugins"]

    async def load_plugin(self, bot: WechatAPIClient, plugin_class: Type[PluginBase],
                          is_disabled: bool = False) -> bool:
        """加载单个插件"""
        try:
            plugin_name = plugin_class.__name__

            # 防止重复加载插件
            if plugin_name in self.plugins:
                return False

            # 记录插件信息，即使插件被禁用也会记录
            self.plugin_info[plugin_name] = {
                "name": plugin_name,
                "description": plugin_class.description,
                "author": plugin_class.author,
                "version": plugin_class.version,
                "enabled": False,
                "class": plugin_class
            }

            # 如果插件被禁用则不加载
            if is_disabled:
                return False

            plugin = plugin_class()
            EventManager.bind_instance(plugin)
            await plugin.on_enable(bot)
            self.plugins[plugin_name] = plugin
            self.plugin_classes[plugin_name] = plugin_class
            self.plugin_info[plugin_name]["enabled"] = True
            return True
        except:
            logger.error(f"加载插件时发生错误: {traceback.format_exc()}")
            return False

    async def unload_plugin(self, plugin_name: str) -> bool:
        """卸载单个插件"""
        if plugin_name not in self.plugins:
            return False

        # 防止卸载 ManagePlugin
        if plugin_name == "ManagePlugin":
            logger.warning("ManagePlugin 不能被卸载")
            return False

        try:
            plugin = self.plugins[plugin_name]
            await plugin.on_disable()
            EventManager.unbind_instance(plugin)
            del self.plugins[plugin_name]
            del self.plugin_classes[plugin_name]
            if plugin_name in self.plugin_info.keys():
                self.plugin_info[plugin_name]["enabled"] = False
            return True
        except:
            logger.error(f"卸载插件 {plugin_name} 时发生错误: {traceback.format_exc()}")
            return False

    async def load_plugins_from_directory(self, bot: WechatAPIClient, load_disabled_plugin: bool = True, excluded_plugins: List[str] = None) -> Union[List[str], bool]:
        """从plugins目录批量加载插件
        
        Args:
            bot: 机器人实例
            load_disabled_plugin: 是否加载被禁用的插件
            excluded_plugins: 不需要加载的插件列表
            
        Returns:
            Union[List[str], bool]: 成功加载的插件列表，或加载失败时返回 False
        """
        loaded_plugins = []

        for filename in os.listdir("plugins"):
            if filename.endswith('.py') and not filename.startswith('__'):
                module_name = filename[:-3]
                try:
                    module = importlib.import_module(f"plugins.{module_name}")
                    for name, obj in inspect.getmembers(module):
                        if inspect.isclass(obj) and issubclass(obj, PluginBase) and obj != PluginBase:
                            # 检查是否在排除列表中
                            if excluded_plugins and obj.__name__ in excluded_plugins:
                                continue
                                
                            is_disabled = False
                            if not load_disabled_plugin:
                                is_disabled = obj.__name__ in self.excluded_plugins

                            if await self.load_plugin(bot, obj, is_disabled=is_disabled):
                                loaded_plugins.append(obj.__name__)
                except:
                    logger.error(f"加载插件模块 {module_name} 时发生错误: {traceback.format_exc()}")
                    return False

        return loaded_plugins

    async def load_plugin_from_directory(self, bot: WechatAPIClient, plugin_name: str) -> bool:
        """从plugins目录加载单个插件

        Args:
            bot: 机器人实例
            plugin_name: 插件类名称（不是文件名）

        Returns:
            bool: 是否成功加载插件
        """
        try:
            # 遍历plugins目录查找插件
            for filename in os.listdir("plugins"):
                if not filename.endswith('.py') or filename.startswith('__'):
                    continue

                module_name = filename[:-3]
                try:
                    # 导入模块
                    module = importlib.import_module(f"plugins.{module_name}")
                    importlib.reload(module)  # 重新加载以确保获取最新版本

                    # 在模块中查找指定名称的插件类
                    for name, obj in inspect.getmembers(module):
                        if (inspect.isclass(obj) and
                                issubclass(obj, PluginBase) and
                                obj != PluginBase and
                                obj.__name__ == plugin_name):
                            return await self.load_plugin(bot, obj)

                except:
                    logger.error(f"检查模块 {module_name} 时发生错误: {traceback.format_exc()}")
                    continue

            logger.error(f"未找到插件类 {plugin_name}")
            return False

        except Exception as e:
            logger.error(f"加载插件 {plugin_name} 时发生错误: {traceback.format_exc()}")
            return False

    async def unload_all_plugins(self) -> tuple[List[str], List[str]]:
        """卸载所有插件"""
        unloaded_plugins = []
        failed_unloads = []
        for plugin_name in list(self.plugins.keys()):
            if await self.unload_plugin(plugin_name):
                unloaded_plugins.append(plugin_name)
            else:
                failed_unloads.append(plugin_name)
        return unloaded_plugins, failed_unloads

    async def reload_plugin(self, bot: WechatAPIClient, plugin_name: str) -> bool:
        """重载单个插件"""
        if plugin_name not in self.plugin_classes:
            return False

        # 防止重载 ManagePlugin
        if plugin_name == "ManagePlugin":
            logger.warning("ManagePlugin 不能被重载")
            return False

        try:
            # 获取插件类所在的模块
            plugin_class = self.plugin_classes[plugin_name]
            module_name = plugin_class.__module__

            # 先卸载插件
            if not await self.unload_plugin(plugin_name):
                return False

            # 重新导入模块
            module = importlib.import_module(module_name)
            importlib.reload(module)

            # 从重新加载的模块中获取插件类
            for name, obj in inspect.getmembers(module):
                if (inspect.isclass(obj) and
                        issubclass(obj, PluginBase) and
                        obj != PluginBase and
                        obj.__name__ == plugin_name):
                    # 使用新的插件类而不是旧的
                    return await self.load_plugin(bot, obj)

            return False
        except Exception as e:
            logger.error(f"重载插件 {plugin_name} 时发生错误: {e}")
            return False

    async def reload_all_plugins(self, bot: WechatAPIClient, excluded_plugins: List[str] = None) -> List[str]:
        """重载所有插件
        
        Args:
            bot: 机器人实例
            excluded_plugins: 不需要重载的插件列表，默认为 None
            
        Returns:
            List[str]: 成功重载的插件名称列表
        """
        try:
            if excluded_plugins is None:
                excluded_plugins = ["ManagePlugin"]
            else:
                excluded_plugins = list(set(excluded_plugins + ["ManagePlugin"]))

            # 记录当前加载的插件名称，排除不需要重载的插件
            original_plugins = [name for name in self.plugins.keys() if name not in excluded_plugins]

            # 卸载需要重载的插件
            for plugin_name in original_plugins:
                await self.unload_plugin(plugin_name)

            # 重新加载所有模块
            for module_name in list(sys.modules.keys()):
                if module_name.startswith('plugins.'):
                    module_file = module_name.split('.')[-1]
                    if not any(p.lower() in module_file.lower() for p in excluded_plugins):
                        del sys.modules[module_name]

            # 从目录重新加载插件，传递 excluded_plugins 参数
            return await self.load_plugins_from_directory(bot, excluded_plugins=excluded_plugins)

        except:
            logger.error(f"重载所有插件时发生错误: {traceback.format_exc()}")
            return []

    def get_plugin_info(self, plugin_name: str = None) -> Union[dict, List[dict]]:
        """获取插件信息
        
        Args:
            plugin_name: 插件名称，如果为None则返回所有插件信息
            
        Returns:
            如果指定插件名，返回单个插件信息字典；否则返回所有插件信息列表
        """
        if plugin_name:
            return self.plugin_info.get(plugin_name)
        return list(self.plugin_info.values())


plugin_manager = PluginManager()
