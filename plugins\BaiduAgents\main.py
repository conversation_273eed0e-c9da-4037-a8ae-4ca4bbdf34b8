﻿import json
import re
import tomllib
import tomli_w
import traceback
import asyncio
import os
import time
import uuid
import httpx
import wave
import random
from io import BytesIO
import aiofiles
from datetime import datetime, timedelta
from loguru import logger
from pathlib import Path
from typing import Optional, Dict, Any, List
from urllib.parse import quote

from WechatAPI import WechatAPIClient
from utils.decorators import *
from utils.plugin_base import PluginBase
from utils.temp_file_manager import cleanup_file
from plugins.text2card_project.text_card_service import TextCardService

class BaiduAgentsPlugin(PluginBase):
    """百度智能体插件"""

    plugin_name = "BaiduAgents"
    description = "百度智能体 - 多智能体聊天系统"
    author = "Claude"
    version = "1.0.0"

    def __init__(self):
        super().__init__()

        # 初始化临时目录
        self._temp_dir = Path("temp/baidu_agents")
        self._temp_dir.mkdir(parents=True, exist_ok=True)

        # 读取配置
        config = {}
        try:
            config_path = f"plugins/{self.plugin_name}/config.toml"
            if not os.path.exists(config_path):
                logger.warning(f"[{self.plugin_name}] 配置文件 {config_path} 不存在，将使用默认配置")
                config = {}
            else:
                with open(config_path, "rb") as f:
                    plugin_config = tomllib.load(f)
                config = plugin_config.get(self.plugin_name, {})
        except Exception as e:
            logger.error(f"[{self.plugin_name}] 配置文件读取失败: {str(e)}")
            config = {}

        # 基本配置
        self.enable = config.get("enable", True)
        self.command_format = config.get("command-format", "")
        self.natural_response = config.get("natural_response", True)

        # TTS配置
        self.tts_url = config.get("tts-url", "http://www.yx520.ltd/API/wzzyy/zmp3.php")
        self.tts_voice = config.get("tts-voice", "318")

        # API配置
        self.api_url = config.get("api-url", "https://agent-proxy-ws.baidu.com/agent/call/conversation")
        self.access_token = config.get("access-token", "")
        self.cookies = config.get("cookies", "")

        # 智能体配置
        self.agents = {}
        agents_config = config.get("agents", {})


        for agent_id, agent_config in agents_config.items():
            try:
                agent_data = {
                    "name": agent_config.get("name", ""),
                    "app_key": agent_config.get("app-key", ""),
                    "command": agent_config.get("command", []),
                    "description": agent_config.get("description", ""),
                    "tts-voice": str(agent_config.get("tts-voice", self.tts_voice))
                }

                # 检查必要字段
                if not agent_data["name"] or not agent_data["app_key"] or not agent_data["command"]:
                    logger.error(f"[BaiduAgents] 智能体 {agent_id} 缺少必要配置")
                    continue

                self.agents[agent_id] = agent_data
            except Exception as e:
                logger.error(f"[BaiduAgents] 加载智能体 {agent_id} 失败: {str(e)}")
                logger.error(traceback.format_exc())



        # 初始化TextCardService
        self.text_card = TextCardService()

        # 会话管理
        self._sessions = {}  # 存储会话ID，格式：{f"{group_wxid}_{sender_wxid}": session_id}

        # 会话模式管理
        self._chat_sessions = {}  # {f"{group_wxid}_{sender_wxid}": {"agent": agent, "last_active": datetime}}
        self.session_timeout = timedelta(minutes=5)

        # 启动会话清理任务
        self._cleanup_task = asyncio.create_task(self._clean_expired_sessions(), name="baidu_agents_cleanup")

        # 初始化自然化回复词库
        self._init_natural_responses()

    def _init_natural_responses(self):
        """初始化自然化回复词库"""
        self.confirm_msgs = [
            "好的", "收到", "明白了", "嗯", "知道了", "行", "OK"
        ]

        self.error_msgs = [
            "这可把我难住了", "搞不定了", "这个有点难", "弄不了",
            "出问题了", "搞不出来", "这个不行"
        ]

        self.rate_limit_msgs = [
            "你搁这刷bug呢", "慢点慢点", "别这么急", "一个一个来",
            "太快了太快了", "歇会儿", "别刷了"
        ]

        self.session_start_msgs = [
            "咱们开始聊吧", "来聊聊呗", "有啥想问的", "开始吧",
            "我来了", "准备好了", "开聊"
        ]

        self.session_end_msgs = [
            "好的，我先去休息啦", "拜拜～", "下次再聊", "先走了",
            "休息一下", "回头见", "溜了溜了"
        ]

    def _get_session_key(self, group_wxid: str, sender_wxid: str) -> str:
        """生成会话键"""
        return f"{group_wxid}_{sender_wxid}"

    async def _simple_confirm(self, bot: WechatAPIClient, group_wxid: str):
        """简单确认回复，不艾特人"""
        if self.natural_response:
            confirm_msg = random.choice(self.confirm_msgs)
            await bot.send_text_message(group_wxid, confirm_msg)

    async def _clean_expired_sessions(self):
        """定期清理过期会话"""
        while True:
            try:
                current_time = datetime.now()
                expired_sessions = []

                for session_key, session in self._chat_sessions.items():
                    if current_time - session["last_active"] > self.session_timeout:
                        expired_sessions.append(session_key)

                if expired_sessions:
                    bot = WechatAPIClient(ip="127.0.0.1", port=8680)
                    for session_key in expired_sessions:
                        session = self._chat_sessions.pop(session_key)
                        group_wxid = session_key.split("_")[0]
                        try:
                            if self.natural_response:
                                end_msg = random.choice(self.session_end_msgs)
                                await bot.send_text_message(group_wxid, end_msg)
                            else:
                                await bot.send_text_message(
                                    group_wxid,
                                    f"看起来你已经很久没说话啦～我先去休息了哦,需要我的时候随时叫我就好啦! 😴"
                                )
                        except:
                            pass


            except Exception as e:
                logger.error(f"[BaiduAgents] 清理过期会话出错: {e}")

            await asyncio.sleep(60)

    def _update_session_activity(self, group_wxid: str, sender_wxid: str):
        """更新会话活跃时间"""
        session_key = self._get_session_key(group_wxid, sender_wxid)
        if session_key in self._chat_sessions:
            self._chat_sessions[session_key]["last_active"] = datetime.now()

    async def _download_header_image(self, url: str) -> Optional[str]:
        """下载头部图片"""
        try:
            temp_path = f"{self._temp_dir}/header_{int(time.time())}.png"
            async with httpx.AsyncClient() as client:
                response = await client.get(url, timeout=30)
                if response.status_code == 200:
                    content = response.content
                    with open(temp_path, "wb") as f:
                        f.write(content)
                    return temp_path
                else:
                    logger.error(f"[BaiduAgents] 下载头部图片失败: {response.status_code}")
                    return None
        except Exception as e:
            logger.error(f"[BaiduAgents] 下载头部图片失败: {e}")
            return None

    async def _generate_text_image(self, text: str, width: int, font_size: int, line_spacing: float, title: str) -> Optional[str]:
        """生成文本图片"""
        try:
            temp_path = f"{self._temp_dir}/text_{int(time.time())}.png"
            await self.text_card.generate_card(
                text=text,
                output_path=temp_path,
                width=width,
                font_size=font_size,
                line_spacing=line_spacing,
                title=title,
                title_font_size=32,
                title_color="#333333",
                background_color="#FFFFFF",
                text_color="#333333",
                padding=30,
                radius=15
            )
            return temp_path
        except Exception as e:
            logger.error(f"[BaiduAgents] 生成文本图片失败: {e}")
            return None

    async def _merge_images(self, header_path: str, text_path: str) -> Optional[str]:
        """合并头部图片和文本图片"""
        try:
            output_path = f"{self._temp_dir}/final_{int(time.time())}.png"
            await self.text_card.merge_with_header(
                text_image_path=text_path,
                header_image_path=header_path,
                output_path=output_path,
                enhance_header=True
            )
            return output_path
        except Exception as e:
            logger.error(f"[BaiduAgents] 合并图片失败: {e}")
            return None

    async def _clean_temp_files(self, *files: str):
        """清理临时文件 - 使用统一管理器"""
        for file in files:
            if file:
                cleanup_file(file, delay_seconds=10)

    async def _generate_card(self, text: str) -> Optional[str]:
        """生成图片卡片"""
        try:
            # 创建临时文件路径
            final_path = f"{self._temp_dir}/final_{int(time.time())}.png"

            # 根据文本长度调整图片宽度和字体大小
            if len(text) <= 100:
                width = 600
                font_size = 24
                line_spacing = 1.6
            elif len(text) <= 300:
                width = 720
                font_size = 20
                line_spacing = 1.4
            else:
                width = 800
                font_size = 18
                line_spacing = 1.2

            # 使用generate_card_with_header直接生成图片
            logger.debug("[BaiduAgents] 开始生成图片")
            await self.text_card.generate_card_with_header(
                text=text,
                header_image_url="https://api.317ak.com/API/tp/dmdntp.php",  # 使用在线头部图片
                output_path=final_path,
                enhance_header=True,
                width=width,
                font_size=font_size,
                line_spacing=line_spacing,
                title="唐僧",
                title_font_size=32,
                title_color="#333333",
                background_color="#FFFFFF",
                text_color="#333333",
                padding=30,
                radius=15
            )

            if not os.path.exists(final_path):
                logger.error("[BaiduAgents] 图片生成失败")
                return None

            logger.debug(f"[BaiduAgents] 图片生成成功: {final_path}")
            return final_path

        except Exception as e:
            logger.error(f"[BaiduAgents] 生成图片失败: {e}")
            logger.error(f"[BaiduAgents] 错误详情: {traceback.format_exc()}")
            if final_path and os.path.exists(final_path):
                try:
                    os.remove(final_path)
                    logger.debug(f"[BaiduAgents] 清理临时文件: {final_path}")
                except Exception as e:
                    logger.warning(f"[BaiduAgents] 清理临时文件失败: {final_path}, {e}")
            return None

    async def _send_image(self, bot: WechatAPIClient, group_wxid: str, image_path: str) -> bool:
        """发送图片消息"""
        try:
            # 1. 确保文件存在
            if not os.path.exists(image_path):
                logger.error(f"[BaiduAgents] 图片文件不存在: {image_path}")
                return False

            # 2. 读取图片文件
            with open(image_path, "rb") as f:
                image_data = f.read()

            # 3. 发送图片
            logger.debug(f"[BaiduAgents] 开始发送图片: {image_path}")
            await bot.send_image_message(group_wxid, image_data)
            logger.debug("[BaiduAgents] 图片发送成功")

            # 4. 等待10秒后清理文件
            asyncio.create_task(self._clean_temp_files(image_path))

            return True
        except Exception as e:
            logger.error(f"[BaiduAgents] 发送图片失败: {e}")
            logger.error(traceback.format_exc())
            return False

    async def _process_text_to_speech(self, text: str, agent: Dict[str, Any]) -> List[bytes]:
        """将文本转换为语音"""
        try:
            voice = str(agent.get("tts-voice", self.tts_voice))
            encoded_text = quote(text)
            url = f"{self.tts_url}?text={encoded_text}&voice={voice}"

            async with httpx.AsyncClient(timeout=30.0) as client:
                response = await client.get(url)
                if response.status_code == 200:
                    voice_data = response.content
                    # 直接返回语音数据,不检查时长
                    return [voice_data]

            # 如果第一次请求失败,进行分段处理
            segments = []
            current_segment = ""

            # 分段标点符号
            puncs = ['。', '！', '？', '…', '\n\n', '\r\n\r\n', '.', '!', '?']

            # 按标点符号分段
            for char in text:
                current_segment += char
                if char in puncs:
                    if len(current_segment) >= 100:  # 大约50秒语音
                        segments.append(current_segment)
                        current_segment = ""

            # 处理最后一段
            if current_segment:
                segments.append(current_segment)

            # 如果没有合适的分段点,按固定长度分段
            if not segments:
                segments = [text[i:i+100] for i in range(0, len(text), 100)]

            # 转换每一段
            voice_segments = []
            async with httpx.AsyncClient(timeout=30.0) as client:
                for segment in segments:
                    try:
                        encoded_text = quote(segment)
                        url = f"{self.tts_url}?text={encoded_text}&voice={voice}"
                        response = await client.get(url)
                        if response.status_code == 200:
                            voice_segments.append(response.content)
                    except Exception as e:
                        logger.error(f"[BaiduAgents] 生成语音片段失败: {e}")
                        continue

            return voice_segments

        except Exception as e:
            logger.error(f"[BaiduAgents] 处理语音转换失败: {e}")
            return []

    async def _process_message(self, bot: WechatAPIClient, group_wxid: str, sender_wxid: str, content: str, agent: Dict[str, Any]) -> None:
        """处理消息"""
        try:
            # 确保 session ID 是字符串类型
            session_key = self._get_session_key(group_wxid, sender_wxid)
            if session_key not in self._sessions:
                self._sessions[session_key] = str(uuid.uuid4())
            session = str(self._sessions[session_key])

            request_data = {
                "appId": agent["app_key"],
                "sessionId": session,
                "versionCode": 1,
                "versionType": "online",
                "content": {
                    "query": {
                        "type": "text",
                        "value": {
                            "showText": content,
                            "isFirstConversation": session_key not in self._chat_sessions
                        }
                    }
                },
                "regenerateInfo": {
                    "isRegenerated": False
                },
                "inputMethod": "keyboard",
                "querySource": "input_box",
                "log": {
                    "channel_id": "3741000310000000",
                    "lid": "",
                    "tplname": "",
                    "srcid": "",
                    "order": "",
                    "csaitab_lid": ""
                },
                "chatRound": 1,
                "transData": [
                    {
                        "from": "agent_proxy",
                        "type": "json",
                        "key": "channel",
                        "value": "3741000310000000"
                    },
                    {
                        "from": "agent_proxy",
                        "type": "json",
                        "key": "action",
                        "value": "[\"\"]"
                    }
                ],
                "chatPerfLoggerInstance": {
                    "enabled": False,
                    "list": [],
                    "ext": {},
                    "commonExt": {
                        "agentName": agent["name"],
                        "platform": "pc"
                    },
                    "isEnd": False
                }
            }

            headers = self._generate_headers(agent)

            async with httpx.AsyncClient(timeout=60.0) as client:
                response = await client.post(
                    f"{self.api_url}?traceId={headers['X_BD_LOGID']}",
                    headers=headers,
                    json=request_data
                )
                logger.debug(f"[BaiduAgents] API响应状态码: {response.status_code}")
                if response.status_code != 200:
                    error_text = await response.text()
                    logger.error(f"[BaiduAgents] API请求失败: 状态码={response.status_code}, 响应内容={error_text}")
                    raise httpx.HTTPError(f"API请求失败: {response.status_code}")

                text = await self._process_stream(response)
                if not text:
                    raise ValueError("抱歉，我暂时无法回答，请稍后再试")

                # 1. 首选: 尝试语音
                voice_success = False
                voice_segments = await self._process_text_to_speech(text, agent)
                if voice_segments:
                    voice_success = True
                    for voice_data in voice_segments:
                        try:
                            await bot.send_voice_message(group_wxid, voice_data, 'mp3')
                            await asyncio.sleep(0.5)  # 语音之间稍微暂停一下
                        except Exception as e:
                            logger.error(f"[BaiduAgents] 发送语音失败: {e}")
                            voice_success = False
                            break

                # 2. 语音失败时: 尝试图片
                if not voice_success:
                    logger.debug("[BaiduAgents] 语音发送失败,尝试生成图片")
                    image_path = await self._generate_card(text)
                    if image_path:
                        if await self._send_image(bot, group_wxid, image_path):
                            return

                    # 3. 图片也失败时: 发送文本
                    logger.debug("[BaiduAgents] 图片发送失败,发送文本")
                    await bot.send_text_message(group_wxid, text)

        except ValueError as e:
            logger.error(f"[BaiduAgents] 处理失败: {str(e)}")
            if self.natural_response:
                error_msg = random.choice(self.error_msgs)
                await bot.send_text_message(group_wxid, error_msg)
            else:
                await bot.send_at_message(group_wxid, f"❌{str(e)}", [sender_wxid])
        except httpx.HTTPError as e:
            logger.error(f"[BaiduAgents] 网络请求错误: {e}")
            if self.natural_response:
                error_msg = random.choice(self.error_msgs)
                await bot.send_text_message(group_wxid, error_msg)
            else:
                await bot.send_at_message(group_wxid, "网络有点问题，等会再试试", [sender_wxid])
        except Exception as e:
            logger.error(f"[BaiduAgents] 处理失败: {str(e)}")
            logger.error(f"[BaiduAgents] 错误详情: {traceback.format_exc()}")
            if self.natural_response:
                error_msg = random.choice(self.error_msgs)
                await bot.send_text_message(group_wxid, error_msg)
            else:
                await bot.send_at_message(group_wxid, "抱歉，我遇到了一些问题，请稍后再试~ 🔧", [sender_wxid])

    async def _process_short_text(self, bot: WechatAPIClient, group_wxid: str, sender_wxid: str, text: str, agent: Dict[str, Any]) -> None:
        """处理短文本消息"""
        try:
            # 优先尝试生成图片
            image_path = await self._generate_card(text)
            if image_path:
                if await self._send_image(bot, group_wxid, image_path):
                    return

            # 图片发送失败则尝试语音
            voice_data = await self._text_to_speech(text, agent)
            if voice_data:
                try:
                    await bot.send_voice_message(group_wxid, voice_data, 'mp3')
                    return
                except Exception as e:
                    logger.error(f"[BaiduAgents] 发送语音失败: {e}")

            # 最后尝试发送文本
            await bot.send_text_message(group_wxid, text)

        except Exception as e:
            logger.error(f"[BaiduAgents] 处理短文本失败: {e}")
            await bot.send_text_message(group_wxid, text)

    async def _process_long_text(self, bot: WechatAPIClient, group_wxid: str, sender_wxid: str, text: str, agent: Dict[str, Any]) -> None:
        """处理长文本消息"""
        try:
            # 尝试生成图片
            image_path = await self._generate_card(text)
            if image_path:
                if await self._send_image(bot, group_wxid, image_path):
                    return

            # 图片发送失败则发送文本
            await bot.send_text_message(group_wxid, text)

        except Exception as e:
            logger.error(f"[BaiduAgents] 处理长文本失败: {e}")
            await bot.send_text_message(group_wxid, text)

    def _is_exit_command(self, content: str) -> bool:
        """检查是否是退出命令"""
        exit_commands = ["退出", "结束", "再见", "拜拜", "exit", "quit", "bye", "结束会话", "退出会话"]
        return content.lower() in exit_commands

    def _get_agent_by_command(self, command: str) -> Optional[Dict[str, Any]]:
        """根据命令获取智能体配置"""
        for agent_id, agent in self.agents.items():
            if command in agent["command"]:
                return agent
        return None

    async def _handle_session_exit(self, bot: WechatAPIClient, group_wxid: str, sender_wxid: str, current_agent: Dict[str, Any]) -> bool:
        """处理会话退出"""
        agent_name = current_agent["name"]
        self._chat_sessions.pop(self._get_session_key(group_wxid, sender_wxid))

        if self.natural_response:
            # 自然化退出回复
            end_msg = random.choice(self.session_end_msgs)
            await bot.send_text_message(group_wxid, end_msg)
        else:
            # 构建其他智能体列表
            other_agents = []
            for agent in self.agents.values():
                if agent["name"] != agent_name:
                    other_agents.append(f"- {agent['name']}: {agent['command'][0]}")

            # 如果有其他智能体,显示切换选项
            if other_agents:
                await bot.send_text_message(
                    group_wxid,
                    f"好的,{agent_name}先去休息啦～\n\n" + \
                    "如果想找其他智能体聊天:\n" + \
                    "\n".join(other_agents) + \
                    "\n\n需要的时候随时叫我哦! 👋"
                )
            else:
                await bot.send_text_message(
                    group_wxid,
                    f"好的,{agent_name}先去休息啦～需要的时候随时叫我哦! 👋"
                )
        return True

    async def _handle_agent_switch(self, bot: WechatAPIClient, group_wxid: str, sender_wxid: str, current_agent: Dict[str, Any], new_agent: Dict[str, Any]) -> bool:
        """处理智能体切换"""
        old_name = current_agent["name"]
        new_name = new_agent["name"]

        # 如果切换到相同的智能体,提示已经在对话中
        if old_name == new_name:
            if self.natural_response:
                confirm_msg = random.choice(self.confirm_msgs)
                await bot.send_text_message(group_wxid, confirm_msg)
            else:
                await bot.send_text_message(
                    group_wxid,
                    f"我们已经在聊天啦～有什么想问{new_name}的吗? 😊"
                )
            return True

        # 更新会话信息
        self._chat_sessions[self._get_session_key(group_wxid, sender_wxid)] = {
            "agent": new_agent,
            "last_active": datetime.now()
        }

        if self.natural_response:
            # 自然化切换回复
            start_msg = random.choice(self.session_start_msgs)
            await bot.send_text_message(group_wxid, start_msg)
        else:
            # 发送切换提示
            await bot.send_text_message(
                group_wxid,
                f"{old_name}先去休息啦～\n" + \
                f"你好,我是{new_name}! {new_agent['description']}\n" + \
                "让我们开始愉快的对话吧~ 😊"
            )
        return True

    @on_text_message
    async def handle_text(self, bot: WechatAPIClient, message: dict):
        if not self.enable:
            return  # 不返回True，允许其他插件继续处理

        group_wxid = message["FromWxid"]
        sender_wxid = message["SenderWxid"]
        content = message["Content"].strip()
        session_key = self._get_session_key(group_wxid, sender_wxid)

        # 检查是否在会话模式中
        if session_key in self._chat_sessions:
            session = self._chat_sessions[session_key]
            current_agent = session["agent"]

            # 检查是否要退出会话
            if self._is_exit_command(content):
                await self._handle_session_exit(bot, group_wxid, sender_wxid, current_agent)
                return True

            # 检查是否要切换智能体
            new_agent = self._get_agent_by_command(content)
            if new_agent:
                await self._handle_agent_switch(bot, group_wxid, sender_wxid, current_agent, new_agent)
                return True

            # 更新会话活跃时间并处理消息
            self._update_session_activity(group_wxid, sender_wxid)
            # 会话模式中直接处理消息，不需要确认
            await self._process_message(bot, group_wxid, sender_wxid, content, current_agent)
            return True

        # 检查是否是智能体命令
        new_agent = self._get_agent_by_command(content)
        if new_agent:
            self._chat_sessions[session_key] = {
                "agent": new_agent,
                "last_active": datetime.now()
            }

            if self.natural_response:
                # 自然化启动回复
                start_msg = random.choice(self.session_start_msgs)
                await bot.send_text_message(group_wxid, start_msg)
            else:
                await bot.send_text_message(
                    group_wxid,
                    f"你好～👋 我是{new_agent['name']}，{new_agent['description']}。\n\n" + \
                    "💡 温馨提示:\n" + \
                    "- 直接说话就可以和我聊天哦\n" + \
                    "- 输入'再见'可以结束对话\n" + \
                    "- 直接输入其他智能体的名字可以切换对话\n" + \
                    f"- 当前可用智能体: {', '.join([agent['name'] for agent in self.agents.values() if agent['name'] != new_agent['name']])}"
                )
            return True

        # 检查命令前缀
        for agent in self.agents.values():
            for cmd in agent["command"]:
                if content.startswith(cmd):
                    question = content[len(cmd):].strip()
                    if not question:
                        if self.natural_response:
                            # 自然化启动回复
                            start_msg = random.choice(self.session_start_msgs)
                            await bot.send_text_message(group_wxid, start_msg)
                        else:
                            await bot.send_text_message(
                                group_wxid,
                                f"你好,我是{agent['name']}。\n" + \
                                f"{agent['description']}\n\n" + \
                                "🤖 使用方式:\n" + \
                                f"1. 直接发送「{cmd}」进入会话模式\n" + \
                                f"2. 发送「{cmd} 问题」快速提问"
                            )
                        return True

                    logger.info(f"[BaiduAgents] 收到{agent['name']}的问题: {question}")
                    # 简单确认
                    await self._simple_confirm(bot, group_wxid)
                    await self._process_message(bot, group_wxid, sender_wxid, question, agent)
                    return True

        return  # 不匹配时直接return，不返回任何值，允许其他插件处理

    async def on_disable(self):
        """插件禁用时清理资源"""
        try:
            # 通知所有会话用户
            bot = WechatAPIClient(ip="127.0.0.1", port=8680)
            for session_key, session in self._chat_sessions.items():
                group_wxid = session_key.split("_")[0]
                try:
                    if self.natural_response:
                        end_msg = random.choice(self.session_end_msgs)
                        await bot.send_text_message(group_wxid, end_msg)
                    else:
                        await bot.send_text_message(
                            group_wxid,
                            "抱歉呀,我需要去休息一下下,很快就会回来的! 🔄"
                        )
                except Exception as e:
                    logger.error(f"[BaiduAgents] 发送禁用通知失败: {e}")

            # 清理会话
            self._chat_sessions.clear()
            self._sessions.clear()

            # 取消所有正在运行的任务
            for task in asyncio.all_tasks():
                if not task.done() and task.get_name().startswith("baidu_agents_"):
                    task.cancel()

            # 清理临时文件
            if os.path.exists(self._temp_dir):
                for file in os.listdir(self._temp_dir):
                    try:
                        file_path = os.path.join(self._temp_dir, file)
                        if os.path.isfile(file_path):
                            os.remove(file_path)
                    except Exception as e:
                        logger.error(f"[BaiduAgents] 清理临时文件失败: {file}, {e}")

            logger.info("[BaiduAgents] 插件已禁用,资源清理完成")

        except Exception as e:
            logger.error(f"[BaiduAgents] 禁用插件时发生错误: {e}")
            logger.error(traceback.format_exc())
        finally:
            await super().on_disable()

    def _generate_headers(self, agent: Dict[str, Any]) -> dict:
        """生成请求头"""
        trace_id = str(uuid.uuid4().hex)

        headers = {
            "Host": "agent-proxy-ws.baidu.com",
            "Connection": "keep-alive",
            "X-Ma-Web-Access-Token": self.access_token,
            "accept": "text/event-stream",
            "X_BD_LOGID": trace_id,
            "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36",
            "Content-Type": "application/json",
            "Origin": f"https://{agent['app_key'].lower()}.smartapps.baidu.com",
            "X-Requested-With": "mark.via",
            "Sec-Fetch-Site": "same-site",
            "Sec-Fetch-Mode": "cors",
            "Sec-Fetch-Dest": "empty",
            "Referer": f"https://{agent['app_key'].lower()}.smartapps.baidu.com/showmaster/?appKey={agent['app_key']}&_swebPkgVersion=1",
            "Accept-Encoding": "gzip, deflate",
            "Accept-Language": "zh-CN,zh;q=0.9,en-US;q=0.8,en;q=0.7",
            "Cookie": self.cookies
        }

        return headers

    async def _process_stream(self, response: httpx.Response) -> str:
        """处理SSE流式响应"""
        buffer = []
        current_text = ""

        try:
            logger.debug("[BaiduAgents] 开始处理响应流")
            async for line in response.aiter_lines():
                if line:
                    line = line.strip()
                    logger.debug(f"[BaiduAgents] 收到响应行: {line}")

                    if line == "event:ping":
                        continue

                    if line.startswith('data:'):
                        data = line[5:].strip()
                        try:
                            json_data = json.loads(data)
                            logger.debug(f"[BaiduAgents] 解析JSON数据: {json_data}")

                            if json_data.get("status") != 0:
                                error_msg = json_data.get("message", "未知错误")
                                logger.error(f"[BaiduAgents] API返回错误状态: {error_msg}")
                                raise ValueError(f"API返回错误: {error_msg}")

                            message = json_data.get("data", {}).get("message", {})
                            logger.debug(f"[BaiduAgents] 提取message数据: {message}")

                            content = message.get("content", [])
                            logger.debug(f"[BaiduAgents] 提取content数据: {content}")

                            if not content:
                                logger.warning("[BaiduAgents] content为空")
                                continue

                            for item in content:
                                if item.get("dataType") == "markdown":
                                    text = item.get("data", {}).get("text", "")
                                    if text and text != current_text:
                                        logger.debug(f"[BaiduAgents] 提取到新文本: {text}")
                                        current_text = text
                                        buffer.append(text)

                            if message.get("endTurn", False):
                                logger.debug("[BaiduAgents] 检测到对话结束标记")
                                break

                        except json.JSONDecodeError as e:
                            logger.warning(f"[BaiduAgents] JSON解析失败: {e}")
                            continue

        except Exception as e:
            logger.error(f"[BaiduAgents] 处理响应流时出错: {e}")
            logger.error(traceback.format_exc())
            raise

        result = "".join(buffer)
        logger.debug(f"[BaiduAgents] 处理完成,最终文本: {result}")
        return result