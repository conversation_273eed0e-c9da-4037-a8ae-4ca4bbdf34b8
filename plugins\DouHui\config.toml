[DouHui]
enable = true
command = ["豆绘测试", "douhui"]
command-format = """
🎨 豆绘AI功能

📝 图生图使用方法：
• 引用图片 + 豆绘 [提示词] - 处理引用的图片
• 豆绘测试 [提示词] - 使用默认测试图片
• 豆绘测试 - 使用默认提示词和默认测试图片

📁 测试图片位置: C:\\DBE25C6475AF6852691B040206E94167.jpg

⚙️ 处理步骤：
1. 上传图片到豆绘AI
2. 提交AI处理任务
3. 等待处理结果
4. 返回处理结果

每一步都会显示详细状态信息，方便调试。
"""

[DouHui.quote]
command = ["豆绘"]
command-format = "引用图片并发送: 豆绘 [提示词]"

[DouHui.api]
# 豆绘AI相关配置 - 基于IdeaFlow API
base_url = "https://cyapi.ideaflow.pro"  # IdeaFlow API地址
api_key = "metatube-eyJhbGciOiJIUzUxMiIsInR5cCI6IkpXVCJ9.eyJzdWIiOiIwMDAwMDQ1Mzg5Mjk2NTc1MDQzNjY1OTIiLCJuaWNrbmFtZSI6IuaipuWunThVSFNYVVZWNyIsImV4cCI6MTc1MDczMTQxNSwidXNlcklkIjoiMDAwMDA0NTM4OTI5NjU3NTA0MzY2NTkyIiwianRpIjoiMDAwMDA0NTM4OTI5NjU3NTA0MzY2NTkyIiwiYXV0aG9yaXRpZXMiOiJbXSJ9.wfnmo1RG66yitpYwGI8eV2zYNaDjkqfZwIe-s7bcSWG32sB-chR-_b3zeiriYV2OzlOoonG_Vrm3W-QQZDDdmQ"  # IdeaFlow Authorization token
model = "ideaflow-image-generation"  # 图生图模型名称
pipe_id = "000004229528230275088385"  # 管道ID

[DouHui.rate_limit]
cooldown = 15  # 请求冷却时间

# 自然化响应设置
natural_response = true  # 启用自然化响应，避免机械化回复
