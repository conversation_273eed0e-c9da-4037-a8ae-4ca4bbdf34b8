import os
import tomllib
from loguru import logger
import httpx
from typing import Optional
from utils.plugin_base import PluginBase
from utils.decorators import on_text_message
from WechatAPI import WechatAPIClient

class URLShortenerService:
    """短链接生成服务类"""
    
    def __init__(self):
        """初始化短链接服务"""
        self.timeout = 10
    
    async def shorten_url(self, url: str) -> Optional[str]:
        """生成短链接
        
        Args:
            url: 需要缩短的URL
            
        Returns:
            成功返回短链接,失败返回None
        """
        try:
            async with httpx.AsyncClient(timeout=self.timeout) as client:
                response = await client.get(f"http://tinyurl.com/api-create.php?url={url}")
                response.raise_for_status()
                return response.text
        except Exception as e:
            logger.error(f"[URLShortener] 生成短链接失败: {e}")
            return None

class URLShortener(PluginBase):
    """短链接生成插件"""
    
    description = "生成短链接的插件"
    author = "XYBot"
    version = "1.0.0"
    
    def __init__(self):
        super().__init__()
        self.url_service = URLShortenerService()
        
        # 从配置文件加载配置
        try:
            config_path = os.path.join(os.path.dirname(__file__), "config.toml")
            with open(config_path, "rb") as f:
                plugin_config = tomllib.load(f)
            config = plugin_config["URLShortener"]
        except Exception as e:
            logger.error(f"[URLShortener] 配置文件读取失败: {e}")
            raise
            
        # 基本配置
        self.enable = config["enable"]
        self.command = config["command"]
        self.command_format = config["command-format"]

    @on_text_message
    async def handle_text(self, bot: WechatAPIClient, message: dict):
        """处理文本消息"""
        if not self.enable:
            return
            
        content = str(message["Content"]).strip()
        command = content.split(" ", 1)
        
        if command[0] not in self.command:
            return
            
        if len(command) == 1:
            await bot.send_at_message(
                message["FromWxid"],
                f"❌命令格式错误！{self.command_format}",
                [message["SenderWxid"]]
            )
            return
            
        url = command[1].strip()
        
        try:
            if short_url := await self.url_service.shorten_url(url):
                await bot.send_at_message(
                    message["FromWxid"],
                    f"✅短链接生成成功:\n{short_url}",
                    [message["SenderWxid"]]
                )
            else:
                await bot.send_at_message(
                    message["FromWxid"],
                    "❌短链接生成失败,请稍后重试",
                    [message["SenderWxid"]]
                )
        except Exception as e:
            logger.error(f"[URLShortener] 处理失败: {e}")
            await bot.send_at_message(
                message["FromWxid"],
                "❌处理失败,请稍后重试",
                [message["SenderWxid"]]
            ) 