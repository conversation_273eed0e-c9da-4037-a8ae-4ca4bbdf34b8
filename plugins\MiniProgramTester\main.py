import os, json, httpx, asyncio, time, traceback, random
try:
    import tomllib
except ModuleNotFoundError:
    import tomli as tomllib
from loguru import logger
from pathlib import Path
from WechatAPI import WechatAPIClient
from utils.decorators import on_text_message
from utils.plugin_base import PluginBase

class MiniProgramTester(PluginBase):
    description = "测试发送微信小程序消息"
    author = "XYBot用户"
    version = "1.0.0"
    plugin_name = "MiniProgramTester"

    def __init__(self):
        super().__init__()
        self.temp_dir = Path("plugins/MiniProgramTester/temp")
        self.temp_dir.mkdir(parents=True, exist_ok=True)
        self._load_config()
        self.user_last_request = {}
        self._init_natural_responses()

    def _load_config(self):
        try:
            with open(f"plugins/{self.plugin_name}/config.toml", "rb") as f:
                config = tomllib.load(f).get(self.plugin_name, {})
        except:
            config = {}

        self.enable = config.get("enable", True)
        self.command = config.get("command", ["红包来了", "唱舞星愿站"])
        self.command_format = config.get("command-format", "发送唱舞星愿站小程序消息")
        self.cooldown = config.get("rate_limit", {}).get("cooldown", 5)
        self.natural_response = config.get("natural_response", True)

    def _init_natural_responses(self):
        """初始化自然化回复词库"""
        self.confirm_msgs = [
            "好的", "收到", "明白了", "嗯", "知道了", "行", "OK"
        ]

        self.error_msgs = [
            "这可把我难住了", "搞不定了", "这个有点难", "弄不了",
            "出问题了", "搞不出来", "这个不行"
        ]

        self.rate_limit_msgs = [
            "你搁这刷bug呢", "慢点慢点", "别这么急", "一个一个来",
            "太快了太快了", "歇会儿", "别刷了"
        ]

    async def _simple_confirm(self, bot, wxid):
        """简单确认回复，不艾特人"""
        if self.natural_response:
            confirm_msg = random.choice(self.confirm_msgs)
            await bot.send_text_message(wxid, confirm_msg)

    @on_text_message
    async def handle_text(self, bot: WechatAPIClient, message: dict):
        if not self.enable:
            return

        content = str(message["Content"]).strip()
        wxid = message["FromWxid"]
        user_wxid = message["SenderWxid"]

        command = content.split(" ", 1)
        if command[0] not in self.command:
            return

        # 直接发送小程序消息，不需要参数

        # 限流检查
        wait_time = self._check_user_limit(wxid, user_wxid)
        if wait_time > 0:
            if self.natural_response:
                rate_limit_msg = random.choice(self.rate_limit_msgs)
                await bot.send_text_message(wxid, rate_limit_msg)
            else:
                await bot.send_at_message(wxid, f"请等待 {wait_time:.1f} 秒", [user_wxid])
            return

        try:
            # 简单确认
            await self._simple_confirm(bot, wxid)

            # 发送小程序消息
            await self._send_miniprogram_message(bot, wxid, user_wxid)

        except Exception as e:
            logger.error(f"[{self.plugin_name}] 异常: {e}")
            if self.natural_response:
                error_msg = random.choice(self.error_msgs)
                await bot.send_text_message(wxid, error_msg)
            else:
                await bot.send_at_message(wxid, "处理失败", [user_wxid])

    async def _send_miniprogram_message(self, bot: WechatAPIClient, wxid: str, user_wxid: str):
        """发送小程序消息"""
        try:
            # 尝试多种简化的小程序XML格式

            # 方法1：包含缩略图的小程序格式
            xml1 = f'''<appmsg appid="wxa708de63ee4a2353" sdkver="0">
<title>唱舞星愿站（唱舞全明星）</title>
<des>星愿站</des>
<action>view</action>
<type>33</type>
<showtype>0</showtype>
<content/>
<url>https://weixin.qq.com</url>
<lowurl>https://weixin.qq.com</lowurl>
<dataurl/>
<lowdataurl/>
<appattach>
    <totallen>0</totallen>
    <attachid/>
    <emoticonmd5/>
    <fileext/>
    <cdnthumburl>3057020100044b30490201000204a95c809d02032df98b02048e009324020468464c38042433636534386265392d386261652d346637622d383130362d3433346461343836373162610204051808030201000405004c57c300</cdnthumburl>
    <cdnthumbmd5>53a81a7ea69f1857dc306224ec7bf272</cdnthumbmd5>
    <cdnthumblength>142524</cdnthumblength>
    <cdnthumbheight>360</cdnthumbheight>
    <cdnthumbwidth>450</cdnthumbwidth>
    <cdnthumbaeskey>35b62ce09aa2bfd9cb3d3c18d417ff8c</cdnthumbaeskey>
    <aeskey>35b62ce09aa2bfd9cb3d3c18d417ff8c</aeskey>
    <encryver>1</encryver>
    <islargefilemsg>0</islargefilemsg>
</appattach>
<extinfo/>
<sourceusername>wxa708de63ee4a2353</sourceusername>
<sourcedisplayname>唱舞星愿站</sourcedisplayname>
<thumburl>http://mmbiz.qpic.cn/mmbiz_png/Y4yibCmxrbX6aCNIhj0BH1CXuE8chunB9AN5fnaRJUMRGRicxkh8crn7P1VCQuhV4GB3KCkE4e5ibmXVEibxaoIJuA/640?wx_fmt=png&amp;wxfrom=200</thumburl>
<md5>53a81a7ea69f1857dc306224ec7bf272</md5>
<weappinfo>
    <pagepath><![CDATA[pages/pointsStroe/wares/index.html?key=abz3BM9k&unionid=oA7D81SRhmAwPJuDQ0Pw39LWcWoY&corpid=wwccefc778261bf00f&app_id=wxa708de63ee4a2353&plate_id=91&userid=40610459]]></pagepath>
    <username>gh_25eb09d7bc53@app</username>
    <appid>wxa708de63ee4a2353</appid>
    <version>14</version>
    <type>2</type>
    <weappiconurl>http://mmbiz.qpic.cn/mmbiz_png/Y4yibCmxrbX6aCNIhj0BH1CXuE8chunB9AN5fnaRJUMRGRicxkh8crn7P1VCQuhV4GB3KCkE4e5ibmXVEibxaoIJuA/640?wx_fmt=png&amp;wxfrom=200</weappiconurl>
    <appservicetype>0</appservicetype>
</weappinfo>
</appmsg>
<fromusername>{bot.wxid}</fromusername>
<scene>0</scene>
<appinfo>
    <version>1</version>
    <appname></appname>
</appinfo>
<commenturl/>'''



            client_msg_id, create_time, new_msg_id = await bot.send_app_message(
                wxid,
                xml1,
                33  # 小程序消息类型
            )

            if new_msg_id != 0 and client_msg_id:

                return
            else:
                logger.warning(f"[{self.plugin_name}] 第一种格式发送失败，尝试其他格式")

                # 方法2：尝试更简单的格式
                xml2 = f'''<appmsg appid="wxa708de63ee4a2353" sdkver="0">
<title>唱舞星愿站</title>
<des>星愿站</des>
<type>33</type>
<weappinfo>
    <username>gh_25eb09d7bc53@app</username>
    <appid>wxa708de63ee4a2353</appid>
</weappinfo>
</appmsg>'''

                client_msg_id, create_time, new_msg_id = await bot.send_app_message(
                    wxid,
                    xml2,
                    33
                )

                if new_msg_id != 0 and client_msg_id:

                    return
                else:
                    logger.error(f"[{self.plugin_name}] 所有小程序消息格式都发送失败")

        except Exception as e:
            logger.error(f"[{self.plugin_name}] 小程序消息发送异常: {str(e)}")

    def _check_user_limit(self, wxid: str, user_wxid: str) -> float:
        user_key = f"{wxid}_{user_wxid}"
        last_request = self.user_last_request.get(user_key, 0)
        current_time = time.time()
        elapsed = current_time - last_request
        self.user_last_request[user_key] = current_time

        if elapsed < self.cooldown:
            return self.cooldown - elapsed

        return 0
