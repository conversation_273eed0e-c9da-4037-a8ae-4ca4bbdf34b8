[ChatSummary]
enable = true
natural_response = true  # 启用自然化响应，避免机械化回复
command = ["群聊总结", "群聊统计", "开启群聊总结", "关闭群聊总结", "开启群聊统计", "关闭群聊统计"]
command-format = "默认使用说明"

[ChatSummary.rate_limit]
cooldown = 60

[ChatSummary.database]
path = "database/chat_summary.db"
max_storage_days = 1

[ChatSummary.features]
enable_summary = true
enable_statistics = true

# 豆包集成配置
[ChatSummary.doubao_integration]
enable = true  # 是否启用豆包插件生成总结报告
prompt = """
#角色设定
你是一位资深社群运营专家，擅长将杂乱群聊提炼为结构化报告。请用简洁口语化中文输出，保持专业但不失趣味性。

#核心任务
1. 标题必须使用当前微信群名称，格式为「📝【群名】群聊总结
2. 内容需满足：
▶️ 提炼3-5个核心讨论主题（带🔥emoji标注热度）
▶️ 每个主题下包含关键结论（用✔️标注）和待解决问题（用❓标注）
▶️ 每段开头添加情境化emoji（如💡知识分享/🎉活动预告/⚠️重要通知）
3. 结尾补充：
- 👥今日最活跃成员Top3
- 📅明日重点关注事项预告

#风格要求
• 段落间用「───」分隔保持呼吸感
• 每项列表前使用▸符号
• 关键数据/人名用**加粗**显示
• 每200字插入1个相关emoji（避免超过5个/段）
"""

[ChatSummary.upload]
enable = true
# 完整URL包含主机名
url = "https://chatglm.cn/chatglm/backend-api/assistant/file_upload"
# 此处填写您的ChatGLM访问令牌
token = "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJzdWIiOiI2NDI4ZDhjZWY4ZTg0NWEyOWMzMGQ5OWViZTZmN2Q2NiIsImV4cCI6MTc0NDIyNjIzOCwibmJmIjoxNzQ0MTM5ODM4LCJpYXQiOjE3NDQxMzk4MzgsImp0aSI6IjlmMjM5NWI5ZGJmZDQ5MjhhOTE1MDFlMDRiOTUxMGI0IiwidWlkIjoiNjdkMTY5MWYxYTdlNjY3Yjg1YTI3YzMwIiwidHlwZSI6ImFjY2VzcyJ9.GFZHaVp5pm0mvMdtmcZCYEB5r2aJv1PSw1-pHLc3a1Q"
# ChatGLM Assistant ID
assistant_id = "65940acff94777010aa6b796"
# 上传超时时间（秒）
timeout = 30
# 是否添加Cookie（可选）
add_cookies = true
# Cookie 值（已从实际请求中提取）
cookies = "sensorsdata2015jssdkchannel=%7B%22prop%22%3A%7B%22_sa_channel_landing_url%22%3A%22%22%7D%7D; chatglm_refresh_token=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJzdWIiOiI2NDI4ZDhjZWY4ZTg0NWEyOWMzMGQ5OWViZTZmN2Q2NiIsImV4cCI6MTc1NzMyOTE4MywibmJmIjoxNzQxNzc3MTgzLCJpYXQiOjE3NDE3NzcxODMsImp0aSI6IjcyYThhYzk5NjI0NzQwYzBiYzk3Mzk4YTY4NmE1MDUzIiwidWlkIjoiNjdkMTY5MWYxYTdlNjY3Yjg1YTI3YzMwIiwidHlwZSI6InJlZnJlc2gifQ.L2Z2ecxjWbJadfxUgRbybikks8K60PtIkxE5tgq4fuU; chatglm_user_id=67d1691f1a7e667b85a27c30; sensorsdata2015jssdkcross=%7B%22distinct_id%22%3A%2267d1691f1a7e667b85a27c30%22%2C%22first_id%22%3A%221958a02468840-012ccf0c267cdfe-26031f51-280800-1958a024689b2%22%2C%22props%22%3A%7B%22%24latest_traffic_source_type%22%3A%22%E7%9B%B4%E6%8E%A5%E6%B5%81%E9%87%8F%22%2C%22%24latest_search_keyword%22%3A%22%E6%9C%AA%E5%8F%96%E5%88%B0%E5%80%BC_%E7%9B%B4%E6%8E%A5%E6%89%93%E5%BC%80%22%2C%22%24latest_referrer%22%3A%22%22%2C%22_latest_wx_ad_click_id%22%3A%22%22%2C%22_latest_wx_ad_hash_key%22%3A%22%22%2C%22_latest_wx_ad_callbacks%22%3A%22%22%7D%2C%22identities%22%3A%22eyIkaWRlbnRpdHlfY29va2llX2lkIjoiMTk1OGEwMjQ2ODg0MC0wMTJjY2YwYzI2N2NkZmUtMjYwMzFmNTEtMjgwODAwLTE5NThhMDI0Njg5YjIiLCIkaWRlbnRpdHlfbG9naW5faWQiOiI2N2QxNjkxZjFhN2U2NjdiODVhMjdjMzAifQ%3D%3D%22%2C%22history_login_id%22%3A%7B%22name%22%3A%22%24identity_login_id%22%2C%22value%22%3A%2267d1691f1a7e667b85a27c30%22%7D%2C%22%24device_id%22%3A%221958a02468840-012ccf0c267cdfe-26031f51-280800-1958a024689b2%22%7D; acw_tc=0aef815617441398366978033e006d8d0cea475be20495886a3fde8666aa54; chatglm_token=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJzdWIiOiI2NDI4ZDhjZWY4ZTg0NWEyOWMzMGQ5OWViZTZmN2Q2NiIsImV4cCI6MTc0NDIyNjIzOCwibmJmIjoxNzQ0MTM5ODM4LCJpYXQiOjE3NDQxMzk4MzgsImp0aSI6IjlmMjM5NWI5ZGJmZDQ5MjhhOTE1MDFlMDRiOTUxMGI0IiwidWlkIjoiNjdkMTY5MWYxYTdlNjY3Yjg1YTI3YzMwIiwidHlwZSI6ImFjY2VzcyJ9.GFZHaVp5pm0mvMdtmcZCYEB5r2aJv1PSw1-pHLc3a1Q; chatglm_token_expires=2025-04-09%2005:17:18"

# 请求头部设置（完全匹配原始请求参数）
[ChatSummary.upload.headers]
x_app_platform = "pc"
x_device_brand = ""
x_exp_groups = "na_android_config:exp:NA,na_4o_config:exp:4o_A,na_glm4plus_config:exp:open,mainchat_server_app:exp:A,mobile_history_daycheck:exp:a,desktop_toolbar:exp:A,chat_drawing_server:exp:A,drawing_server_cogview:exp:cogview4,app_welcome_v2:exp:A,chat_drawing_streamv2:exp:A,mainchat_rm_fc:exp:add,mainchat_dr:exp:open,chat_auto_entrance:exp:A"
x_sign = "25f3920b9f6fa29d918f37e283643d0f"
x_lang = "zh"
x_app_version = "0.0.1"
user_agent = "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36"
app_name = "chatglm"
accept = "application/json, text/plain, */*"
x_device_model = ""
x_device_id = "83b89578f30c4fa598733d7bf4a0c9a8"
origin = "https://chatglm.cn"
x_requested_with = "mark.via"
sec_fetch_site = "same-origin"
sec_fetch_mode = "cors"
sec_fetch_dest = "empty"
accept_encoding = "gzip, deflate"
accept_language = "zh-CN,zh;q=0.9,en-US;q=0.8,en;q=0.7"

# 表单边界设置
[ChatSummary.upload.form]
boundary = "----WebKitFormBoundarygUFvkAUBsMr3iR2g"
