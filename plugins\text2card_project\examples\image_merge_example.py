import asyncio
from pathlib import Path
from ..image_merge_service import ImageMergeService

async def main():
    """图片合并服务使用示例"""
    
    # 创建服务实例
    merger = ImageMergeService()
    
    # 示例1: 水平合并网络图片(使用默认间距50px)
    try:
        result = await merger.merge_images(
            images=[
                "https://example.com/image1.jpg",
                "https://example.com/image2.jpg",
                "https://example.com/image3.jpg"
            ],
            output_path="merged_default_spacing.png",
            enhance=True,
            radius=20
        )
        print(f"默认间距合并完成，保存至: {result}")
    except Exception as e:
        print(f"合并失败: {e}")
    
    # 示例2: 水平合并本地图片(使用较大间距100px)
    try:
        current_dir = Path(__file__).parent
        result = await merger.merge_images(
            images=[
                current_dir / "test1.jpg",
                current_dir / "test2.jpg"
            ],
            output_path="merged_large_spacing.png",
            spacing=100,  # 使用较大间距
            align='center',  # 居中对齐
            enhance=True
        )
        print(f"大间距合并完成，保存至: {result}")
    except Exception as e:
        print(f"合并失败: {e}")
    
    # 示例3: 垂直合并图片(使用自定义间距30px)
    try:
        result = await merger.merge_images(
            images=[
                "https://example.com/image1.jpg",
                "https://example.com/image2.jpg"
            ],
            output_path="merged_vertical.png",
            direction='vertical',
            spacing=30,  # 自定义间距
            align='center',  # 居中对齐
            target_width=800  # 统一宽度
        )
        print(f"垂直合并完成，保存至: {result}")
    except Exception as e:
        print(f"合并失败: {e}")

if __name__ == "__main__":
    asyncio.run(main()) 