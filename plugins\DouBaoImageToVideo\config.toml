[DouBaoImageToVideo]
enable = true
command = ["豆包图生视频", "豆包视频测试"]
command-format = """
🎬 豆包AI图生视频功能

📝 图生视频使用方法：
• 引用图片 + 豆包视频 [提示词] - 处理引用的图片生成视频
• 豆包图生视频 [提示词] [图片路径] - 指定图片路径生成视频
• 豆包视频测试 [提示词] - 使用默认测试图片
• 豆包视频测试 - 使用默认提示词和默认测试图片

📁 测试图片位置: C:\\DBE25C6475AF6852691B040206E94167.jpg

⚙️ 处理步骤：
1. 上传图片到豆包AI
2. 提交视频生成任务
3. 等待视频生成完成
4. 下载并发送生成的视频

每一步都会显示详细状态信息，方便调试。
"""

[DouBaoImageToVideo.quote]
command = ["豆包视频"]
command-format = "引用图片并发送: 豆包视频 [提示词]"

[DouBaoImageToVideo.api]
# 豆包AI相关配置
base_url = "https://www.doubao.com"  # 豆包AI网站地址
api_key = "d_ticket=c387aa7a25690088d32e0f295a9faa5828090; odin_tt=adc3604237fa735eef5d9b2726da716e7ea5dd617e5184147618251da72231aa77815405b56975c824529748a009f1f85cf25c9d17928d11725b0ef3d9e34d33; uid_tt=71dd29fd50dbf8f1a1d7b3090d765234; sessionid=9b6a807194da06d0c111bb246ff92247; sid_guard=9b6a807194da06d0c111bb246ff92247%7C1750354436%7C5184000%7CMon%2C+18-Aug-2025+17%3A33%3A56+GMT"  # 豆包AI的Cookie字符串
model = "doubao-video-generation"  # 豆包AI的图生视频模型名称

[DouBaoImageToVideo.rate_limit]
cooldown = 30  # 视频生成需要更长的冷却时间

# 自然化响应设置
natural_response = true  # 启用自然化响应，避免机械化回复
