import os
import json
import tomllib
import time
import random
import uuid
import httpx
from urllib.parse import quote, urlparse, parse_qs
from pathlib import Path
import asyncio
import re

class HttpResponse:
    def __init__(self, status_code, body, headers=None):
        self.code = status_code
        self.status_code = status_code
        self.body = body
        self.headers = headers or {}
        self._text = None

    def json(self):
        if not self.body: return {}
        return json.loads(self.body if isinstance(self.body, str) else self.body.decode('utf-8'))
    @property
    def text(self):
        if self._text is None:
            self._text = self.body if isinstance(self.body, str) else self.body.decode('utf-8', errors='ignore')
        return self._text
    async def aread(self): return self.body

from WechatAPI import WechatAPIClient
from utils.decorators import on_text_message
from utils.plugin_base import PluginBase

class Doubao(PluginBase):
    description = "豆包AI聊天插件"
    author = "XYBot"
    version = "1.0.0"
    plugin_name = "Doubao"

    def __init__(self):
        super().__init__()
        self.temp_dir = Path("temp/doubao")
        self.temp_dir.mkdir(parents=True, exist_ok=True)
        self.max_age = 3600
        self.cleanup_interval = 1800
        self.last_cleanup = 0
        config = {}
        try:
            config_path = "plugins/Doubao/config.toml"
            if os.path.exists(config_path):
                with open(config_path, "rb") as f:
                    config = tomllib.load(f).get("Doubao", {})
        except: pass
        self.enable = config.get("enable", True)
        self.command = config.get("command", ["豆包"])
        self.command_format = config.get("command-format", "⚙️豆包插件使用说明：\n1️⃣ 普通对话：豆包 <内容>\n2️⃣ 总结内容：引用转发的文章 + \"总结一下\"\n3️⃣ 链接总结：豆包 总结一下 <微信文章链接>\n4️⃣ 连续对话：豆包 对话 - 进入连续对话模式\n豆包 结束 - 结束连续对话模式\n💡 自动精简微信文章链接，提升总结效果")
        self._conversation_mode = {}
        self._conversation_users = {}
        self._conversation_timeout = 300
        self._last_conversation_time = {}
        self.api_config = config.get("API", {})
        self.api_base_url = "https://www.doubao.com/samantha/chat/completion"
        self.cookies = self.api_config.get("cookies", "")
        self.device_id = "7468716989062841895"
        self.tea_uuid = "7468716986638386703"
        self.web_id = "7468716986638386703"
        self._client = None
        self._client_lock = asyncio.Lock()
        self.rate_limit = config.get("rate_limit", {"tokens_per_second": 0.5, "bucket_size": 5})
        self._token_bucket = self.rate_limit["bucket_size"]
        self._last_token_time = time.time()
        self._token_lock = asyncio.Lock()
        self._user_limits = {}
        self.min_request_interval = 2.0
        self._active_files = set()
        self._active_files_lock = asyncio.Lock()
        self.natural_response = config.get("natural_response", True)
        self._init_natural_responses()

    def _init_natural_responses(self):
        self.confirm_msgs = ["好的", "收到", "明白了", "嗯", "知道了", "行", "OK"]
        self.error_msgs = ["这可把我难住了", "搞不定了", "这个有点难", "弄不了", "出问题了", "搞不出来", "这个不行"]
        self.rate_limit_msgs = ["你搁这刷bug呢", "慢点慢点", "别这么急", "一个一个来", "太快了太快了", "歇会儿", "别刷了"]

    def _simplify_wechat_article_url(self, url):
        """
        精简微信公众号文章链接，只保留必要参数

        Args:
            url (str): 原始微信文章链接

        Returns:
            str: 精简后的链接
        """
        try:
            print(f"[DEBUG] 开始精简链接: {url[:100]}...")

            # 检查是否是微信公众号文章链接
            if not url or "mp.weixin.qq.com/s" not in url:
                print(f"[DEBUG] 不是微信文章链接，跳过精简")
                return url

            # 解析URL
            parsed = urlparse(url)
            query_params = parse_qs(parsed.query)
            print(f"[DEBUG] 解析到 {len(query_params)} 个参数")

            # 必须保留的参数
            required_params = ['__biz', 'mid', 'idx', 'sn', 'chksm']

            # 构建精简的查询参数
            simplified_params = {}
            for param in required_params:
                if param in query_params:
                    # parse_qs返回的是列表，取第一个值
                    simplified_params[param] = query_params[param][0]
                    print(f"[DEBUG] 保留参数 {param}: {query_params[param][0][:20]}...")
                else:
                    print(f"[DEBUG] 缺少参数: {param}")

            # 如果缺少必要参数，返回原链接
            if len(simplified_params) < 4:  # 至少需要__biz, mid, idx, sn
                print(f"[DEBUG] 必要参数不足({len(simplified_params)}/4)，返回原链接")
                return url

            # 构建精简的URL
            simplified_query = '&'.join([f"{k}={v}" for k, v in simplified_params.items()])
            simplified_url = f"{parsed.scheme}://{parsed.netloc}{parsed.path}?{simplified_query}"

            print(f"[DEBUG] 精简完成:")
            print(f"[DEBUG] 原始长度: {len(url)} 字符")
            print(f"[DEBUG] 精简长度: {len(simplified_url)} 字符")
            print(f"[DEBUG] 精简比例: {(len(url) - len(simplified_url)) / len(url) * 100:.1f}%")
            print(f"[DEBUG] 精简后链接: {simplified_url}")

            return simplified_url

        except Exception as e:
            print(f"[DEBUG] 精简失败: {str(e)}")
            # 如果处理失败，返回原链接
            return url

    def _process_wechat_urls_in_query(self, query):
        """
        处理查询文本中的微信文章链接，将其精简

        Args:
            query (str): 用户查询文本

        Returns:
            str: 处理后的查询文本
        """
        try:
            print(f"[DEBUG] 开始处理查询中的微信链接")
            print(f"[DEBUG] 原始查询长度: {len(query)} 字符")
            print(f"[DEBUG] 原始查询: {query[:200]}...")

            # 使用正则表达式查找微信文章链接
            wechat_url_pattern = r'https?://mp\.weixin\.qq\.com/s[^\s]*'
            urls = re.findall(wechat_url_pattern, query)

            print(f"[DEBUG] 找到 {len(urls)} 个微信文章链接")

            # 精简找到的每个链接
            for i, original_url in enumerate(urls):
                print(f"[DEBUG] 处理第 {i+1} 个链接...")
                simplified_url = self._simplify_wechat_article_url(original_url)
                if simplified_url != original_url:
                    print(f"[DEBUG] 替换链接成功")
                    query = query.replace(original_url, simplified_url)
                else:
                    print(f"[DEBUG] 链接无需替换")

            print(f"[DEBUG] 处理完成，最终查询长度: {len(query)} 字符")
            print(f"[DEBUG] 最终查询: {query[:200]}...")

            return query

        except Exception as e:
            print(f"[DEBUG] 处理查询失败: {str(e)}")
            # 如果处理失败，返回原查询
            return query
    async def _simple_confirm(self, bot, wxid):
        if self.natural_response:
            await bot.send_text_message(wxid, random.choice(self.confirm_msgs))
    async def _update_tokens(self):
        current_time = time.time()
        elapsed = current_time - self._last_token_time
        new_tokens = elapsed * self.rate_limit["tokens_per_second"]
        self._token_bucket = min(self.rate_limit["bucket_size"], self._token_bucket + new_tokens)
        self._last_token_time = current_time
    async def _acquire_token(self):
        async with self._token_lock:
            await self._update_tokens()
            if self._token_bucket >= 1:
                self._token_bucket -= 1
                return True
            return False
    def _check_user_limit(self, wxid, user_wxid):
        current_time = time.time()
        if wxid not in self._user_limits:
            self._user_limits[wxid] = {}
        last_request = self._user_limits[wxid].get(user_wxid, 0)
        wait_time = max(0, self.min_request_interval - (current_time - last_request))
        if wait_time == 0:
            self._user_limits[wxid][user_wxid] = current_time
        return wait_time
    async def call_doubao_api(self, bot, message, query):
        _ = bot, message

        print(f"[DEBUG] ===== 豆包API调用开始 =====")
        print(f"[DEBUG] 接收到查询: {query[:100]}...")

        # 检测并精简微信文章链接
        original_query = query
        query = self._process_wechat_urls_in_query(query)

        if query != original_query:
            print(f"[DEBUG] 查询已被精简处理")
        else:
            print(f"[DEBUG] 查询无需精简")

        max_retries = 3
        base_delay = 3
        max_delay = 10
        last_error = None
        conversation_id = None
        for retry in range(max_retries):
            try:
                # 构造请求URL参数
                params = {
                    "aid": "497858",
                    "device_id": self.device_id,
                    "device_platform": "web",
                    "language": "zh",
                    "pc_version": "2.16.4",  # 更新版本
                    "pkg_type": "release_version",
                    "real_aid": "497858",
                    "region": "CN",
                    "samantha_web": "1",
                    "sys_region": "CN",
                    "tea_uuid": self.tea_uuid,
                    "use-olympus-account": "1",
                    "version_code": "20800",
                    "web_id": self.web_id
                }
                conversation_id = f"38{int(time.time() * 10000)}8"
                section_id = f"{conversation_id[:10]}4{conversation_id[10:]}"
                message_id = f"ba0d6520-{uuid.uuid4().hex[:4]}-11f0-a188-0d4d{uuid.uuid4().hex[:8]}"
                search_query = query
                request_data = {
                    "messages": [{
                        "content": json.dumps({
                            "text": search_query
                        }),
                        "content_type": 2001,
                        "attachments": [],
                        "references": []
                    }],
                    "completion_option": {
                        "is_regen": False,
                        "with_suggest": True,
                        "need_create_conversation": True,  # 总是创建新会话
                        "launch_stage": 1,
                        "is_replace": False,
                        "is_delete": False,
                        "message_from": 0,
                        "use_deep_think": False,
                        "use_auto_cot": False,
                        "event_id": "0"
                    },
                    "section_id": section_id,
                    "conversation_id": conversation_id,
                    "local_message_id": message_id
                }

                # 获取httpx客户端
                client = await self.get_session()

                # 设置请求头
                headers = self._generate_headers()
                headers['Content-Type'] = 'application/json; charset=utf-8'

                # 发送异步请求
                try:
                    # 构造完整URL
                    url = f"{self.api_base_url}?" + "&".join([f"{k}={quote(str(v))}" for k,v in params.items()])

                    print(f"[DEBUG] 发送豆包API请求到: {url[:100]}...")
                    print(f"[DEBUG] 请求数据: {json.dumps(request_data, ensure_ascii=False)[:300]}...")

                    # 发送POST请求
                    response = await client.post(
                        url,
                        json=request_data,
                        headers=headers,
                        timeout=httpx.Timeout(connect=10.0, read=300.0, write=60.0, pool=10.0)
                    )

                    print(f"[DEBUG] 豆包API响应状态码: {response.status_code}")
                    print(f"[DEBUG] 豆包API响应头: {dict(response.headers)}")
                    print(f"[DEBUG] 豆包API响应内容: {response.text[:500]}...")

                    if response.status_code != 200:
                        print(f"[DEBUG] API请求失败，状态码: {response.status_code}")
                        raise ValueError(f"API请求失败: {response.status_code}")

                    # 创建自定义响应对象
                    http_response = HttpResponse(
                        status_code=response.status_code,
                        body=response.content,
                        headers=dict(response.headers)
                    )

                    # 处理响应
                    print(f"[DEBUG] 开始处理豆包API响应流...")
                    result = await self._process_stream(http_response)
                    print(f"[DEBUG] 豆包API处理结果: {result}")

                    if result:
                        # 请求完成后删除会话，防止服务器端资源泄漏
                        if conversation_id:
                            asyncio.create_task(self._delete_conversation(conversation_id))
                        return result

                    # 如果没有结果，但还有重试机会，继续重试
                    if retry < max_retries - 1:
                        wait_time = min(base_delay * (2 ** retry) + random.uniform(0, 2), max_delay)
                        await asyncio.sleep(wait_time)
                        continue

                    # 如果是最后一次重试，返回空结果而不是抛出异常
                    return {"type": "text", "text": ""}

                except httpx.RequestError as e:
                    raise ValueError(f"HTTP请求失败: {str(e)}")

            except ValueError as e:
                last_error = e
                if retry < max_retries - 1:
                    wait_time = min(base_delay * (2 ** retry) + random.uniform(0, 2), max_delay)
                    await asyncio.sleep(wait_time)
                    continue
                else:
                    # 请求失败后也尝试删除会话，但返回空结果而不是抛出异常
                    if conversation_id:
                        asyncio.create_task(self._delete_conversation(conversation_id))
                    return {"type": "text", "text": ""}
            except Exception as e:
                last_error = e
                if retry < max_retries - 1:
                    wait_time = min(base_delay * (2 ** retry) + random.uniform(0, 2), max_delay)
                    await asyncio.sleep(wait_time)
                    continue
                else:
                    # 请求失败后也尝试删除会话
                    if conversation_id:
                        asyncio.create_task(self._delete_conversation(conversation_id))
                    raise

        # 如果所有重试都失败，抛出最后一个错误
        if last_error:
            raise last_error

        raise ValueError("达到最大重试次数，等会再试试")

    async def _delete_conversation(self, conversation_id):
        """删除豆包会话

        Args:
            conversation_id: 会话ID

        Returns:
            bool: 删除是否成功
        """
        try:
            # 构造URL参数
            params = {
                "version_code": "20800",
                "language": "zh",
                "device_platform": "web",
                "aid": "497858",
                "real_aid": "497858",
                "pc_version": "2.16.1",
                "pkg_type": "release_version",
                "device_id": self.device_id,
                "web_id": self.web_id,
                "tea_uuid": self.tea_uuid,
                "use-olympus-account": "1",
                "region": "CN",
                "sys_region": "CN",
                "samantha_web": "1"
            }

            # 构造请求URL
            url = f"https://www.doubao.com/samantha/thread/delete?" + "&".join([f"{k}={quote(str(v))}" for k,v in params.items()])

            # 构造请求头
            headers = {
                "Accept": "application/json, text/plain, */*",
                "Content-Type": "application/json",
                "Origin": "https://www.doubao.com",
                "Referer": "https://www.doubao.com/chat/",
                "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36",
                "X-Requested-With": "mark.via",
                "Connection": "keep-alive",
                "Cookie": self.cookies
            }

            # 构造请求数据
            data = {"conversation_id": conversation_id}

            # 获取httpx客户端
            client = await self.get_session()

            # 发送异步请求
            try:
                response = await client.post(
                    url,
                    json=data,
                    headers=headers,
                    timeout=httpx.Timeout(connect=5.0, read=10.0, write=10.0, pool=5.0)
                )

                # 如果状态码为200，我们认为删除成功，即使响应为空
                if response.status_code == 200:
                    # 检查响应文本是否为空
                    text = response.text
                    if not text or text.isspace():
                        return True

                    # 尝试解析JSON
                    try:
                        resp_data = response.json()
                        if resp_data.get("code") == 0:
                            return True
                        else:
                            return False
                    except json.JSONDecodeError:
                        return True
                else:
                    return False

            except: return False
        except: return False

    async def _process_stream(self, response):
        """处理SSE响应流

        Args:
            response: HTTP响应对象

        Returns:
            处理后的响应数据字典
        """
        print(f"[DEBUG] 开始处理SSE响应流")
        print(f"[DEBUG] 响应状态码: {response.status_code}")
        print(f"[DEBUG] 响应体类型: {type(response.body)}")
        print(f"[DEBUG] 响应体长度: {len(response.body) if response.body else 0}")

        result_text = ""
        result_data = {"type": "text", "text": ""}

        try:
            # 添加超时控制
            timeout = 300  # 设置300秒超时
            start_time = time.time()

            # 添加结束标志
            is_completed = False
            last_update_time = time.time()
            max_idle_time = 10  # 最大空闲时间（秒）

            # 解析SSE流 - 在httpx中需要一次性处理整个响应
            buffer = ""

            # 获取响应数据 - 确保是bytes类型
            if isinstance(response.body, str):
                chunk = response.body.encode('utf-8')
            else:
                chunk = response.body

            try:
                # 安全解码，忽略错误的字节
                try:
                    decoded_chunk = chunk.decode('utf-8', errors='ignore')
                    buffer = decoded_chunk
                except UnicodeDecodeError:
                    return None

                # 处理完整的SSE事件
                while "\n\n" in buffer:
                    parts = buffer.split("\n\n", 1)
                    event = parts[0]
                    buffer = parts[1]

                    # 跳过空事件
                    if not event.strip():
                        continue

                    # 提取"data:"行
                    data_line = None
                    for line in event.split("\n"):
                        if line.startswith("data:"):
                            data_line = line[5:].strip()
                            break

                    if not data_line:
                        continue

                    # 解析数据
                    try:
                        event_data = json.loads(data_line)
                        if not isinstance(event_data, dict):
                            continue

                        # 处理错误消息
                        if "event_type" not in event_data:
                            continue

                        event_type = event_data["event_type"]

                        # 处理结束事件
                        if event_type == 2003:  # 结束事件
                            is_completed = True

                            # 检查最后一个事件是否包含完整内容
                            if "tts_content" in event_data:
                                full_text = event_data["tts_content"]
                                if full_text and len(full_text) > len(result_text):
                                    result_text = full_text
                                    result_data["text"] = result_text

                            # 否则返回文本结果
                            result_data["text"] = result_text
                            return result_data

                        # 错误事件
                        if event_type == 2005:
                            try:
                                error_data = json.loads(event_data["event_data"])
                                print(f"[DEBUG] 收到错误事件: {error_data}")

                                # 检查是否是会话记录不存在错误
                                if "code" in error_data and error_data["code"] == 710022001:
                                    print(f"[DEBUG] 会话记录不存在错误")
                                    # 这里不直接处理，而是让调用者通过_check_record_not_found感知到错误
                                    pass
                                elif "code" in error_data and error_data["code"] == 710022004:
                                    print(f"[DEBUG] 检测到限流错误 (710022004)")
                                    # 返回限流错误信息
                                    error_msg = error_data.get("error_detail", {}).get("message", "请求过于频繁")
                                    result_data["text"] = f"⚠️ {error_msg}，请稍后再试"
                                    result_data["error"] = "rate_limited"
                                    return result_data
                                else:
                                    print(f"[DEBUG] 其他错误: {error_data.get('code', 'unknown')}")
                                    error_msg = error_data.get("message", "处理失败")
                                    result_data["text"] = f"❌ {error_msg}"
                                    result_data["error"] = "api_error"
                                    return result_data
                            except Exception as e:
                                print(f"[DEBUG] 解析错误事件失败: {str(e)}")
                                pass

                        # 正常事件
                        if event_type == 2001 and "event_data" in event_data:  # 消息事件
                            try:
                                inner_data = json.loads(event_data["event_data"])

                                if "message" not in inner_data:
                                    continue

                                message = inner_data["message"]
                                is_finish = inner_data.get("is_finish", False)

                                if "content_type" not in message or "content" not in message:
                                    continue

                                content_type = message["content_type"]
                                try:
                                    content = json.loads(message["content"])
                                except json.JSONDecodeError:
                                    continue

                                # 处理不同类型的内容
                                if content_type == 2001:  # 文本内容
                                    if "text" in content:
                                        text = content["text"]
                                        result_text += text
                                        result_data["text"] = result_text
                                        last_update_time = time.time()

                                        # 当事件标记为完成时，检查tts_content
                                        if is_finish and "tts_content" in inner_data:
                                            full_text = inner_data["tts_content"]
                                            if full_text and len(full_text) > len(result_text):
                                                result_text = full_text
                                                result_data["text"] = result_text
                                elif content_type == 2002:  # 建议内容
                                    if "suggest" in content:
                                        result_data.setdefault("suggests", []).append(content["suggest"])
                                elif content_type == 2003:  # 加载状态
                                    # 跳过加载状态处理
                                    pass
                                elif content_type == 2030:  # 读取状态含文本内容
                                    if "text" in content:
                                        text = content["text"]
                                        result_text += text
                                        result_data["text"] = result_text
                                        last_update_time = time.time()

                                elif content_type == 2008 or content_type == 2018:  # 处理搜索和特殊响应内容
                                    if "text" in content:
                                        text = content["text"]
                                        result_text += text
                                        result_data["text"] = result_text
                                        last_update_time = time.time()

                                        # 如果有TTS内容，使用完整的TTS内容
                                        if is_finish and "tts_content" in inner_data:
                                            full_text = inner_data["tts_content"]
                                            if full_text and len(full_text) > len(result_text):
                                                result_text = full_text
                                                result_data["text"] = result_text

                            except: continue
                        last_update_time = time.time()
                    except: continue

                    # 检查是否超时
                    if time.time() - start_time > timeout:
                        if result_text:
                            result_data["text"] = result_text
                            return result_data
                        break

                    # 检查是否长时间没有新内容
                    if time.time() - last_update_time > max_idle_time:
                        if result_text:
                            result_data["text"] = result_text
                            return result_data

                # 处理完所有事件后，如果没有提前返回结果，继续执行后续代码

            except: pass

            # 如果有文本内容，返回文本结果
            print(f"[DEBUG] 处理完成，检查结果:")
            print(f"[DEBUG] result_text长度: {len(result_text) if result_text else 0}")
            print(f"[DEBUG] result_text内容: {result_text[:200] if result_text else 'None'}...")
            print(f"[DEBUG] is_completed: {is_completed}")

            if result_text:
                print(f"[DEBUG] 返回有效文本内容")
                result_data["text"] = result_text
                return result_data

            # 修改响应处理逻辑，避免误判为失败
            if is_completed:
                print(f"[DEBUG] 处理完成但无内容，返回空文本")
                # 即使没有内容也返回空字典，避免被误判为失败
                return {"type": "text", "text": ""}

            # 实在没有任何有效内容，返回None
            # 修改这里：返回空文本结果而不是None
            print(f"[DEBUG] 处理完成，但没有有效内容，返回空文本")
            result_data["text"] = ""
            return result_data

        except Exception as e:
            print(f"[DEBUG] 处理SSE流时发生异常: {str(e)}")
            if result_text:
                print(f"[DEBUG] 异常情况下返回已收集的文本: {result_text[:100]}...")
                result_data["text"] = result_text
                return result_data
            print(f"[DEBUG] 异常情况下返回None")
            return None

    async def handle_text_message(self, bot, message, result):
        try:
            if result["type"] == "text":
                text = result.get("text", "")
                if not text:
                    await bot.send_at_message(message["FromWxid"], "我理解您的问题，但目前回答不了。请重新表述一下，或等会再试试。", [message["SenderWxid"]])
                    return
                text_length = len(text)
                try:
                    if text_length <= 200:
                        voice_data = await self._text_to_speech(text)
                        if voice_data:
                            await bot.send_voice_message(message["FromWxid"], voice_data, 'mp3')
                            return
                        await bot.send_at_message(message["FromWxid"], text, [message["SenderWxid"]])
                    else:
                        segments = self._smart_split_text(text)
                        total_segments = len(segments)
                        for i, segment in enumerate(segments[:5], 1):
                            segment_text = f"[{i}/{min(total_segments, 5)}]\n{segment}"
                            await bot.send_at_message(message["FromWxid"], segment_text, [message["SenderWxid"]])
                            await asyncio.sleep(0.5)
                        if total_segments > 5:
                            await bot.send_at_message(message["FromWxid"], f"...(内容过长，已省略剩余{total_segments-5}段内容)", [message["SenderWxid"]])
                except:
                    if self.natural_response:
                        await bot.send_text_message(message["FromWxid"], random.choice(self.error_msgs))
                    else:
                        await bot.send_at_message(message["FromWxid"], "处理文本失败，等会再试试", [message["SenderWxid"]])
        except:
            if self.natural_response:
                await bot.send_text_message(message["FromWxid"], random.choice(self.error_msgs))
            else:
                await bot.send_at_message(message["FromWxid"], "处理不了，等会再试试", [message["SenderWxid"]])

    def _is_drawing_request(self, content):
        return content.startswith("画") or (content.startswith("豆包") and "画" in content) or any(keyword in content for keyword in ["绘制", "生成图片", "创建图像", "创建图片", "作画", "画出", "分镜", "插画", "漫画", "海报", "封面", "设计图", "AI绘画", "AI创作"])

    @on_text_message
    async def handle_text(self, bot: WechatAPIClient, message: dict):
        if not self.enable:
            return

        content = str(message["Content"]).strip()
        wxid = message["FromWxid"]
        user_wxid = message["SenderWxid"]
        if self._is_drawing_request(content): return
        command = content.split()
        command_simple = content.split(" ", 1)
        is_doubao_command = command[0] in self.command if command else False
        in_conversation = wxid in self._conversation_mode and self._conversation_mode[wxid]
        is_conversation_user = wxid in self._conversation_users and self._conversation_users[wxid] == user_wxid

        if in_conversation and time.time() - self._last_conversation_time.get(wxid, 0) > self._conversation_timeout:
            self._conversation_mode[wxid] = False
            self._conversation_users.pop(wxid, None)
            self._last_conversation_time.pop(wxid, None)
            in_conversation = False
            await bot.send_at_message(wxid, "⌛️ 连续对话已超时，已自动结束", [user_wxid])

        if is_doubao_command and len(command_simple) > 1:
            cmd = command_simple[1].strip()
            if cmd == "对话":
                if in_conversation and not is_conversation_user:
                    await bot.send_at_message(wxid, "❌ 当前已有其他用户在进行连续对话", [user_wxid])
                    return
                self._conversation_mode[wxid] = True
                self._conversation_users[wxid] = user_wxid
                self._last_conversation_time[wxid] = time.time()
                await bot.send_at_message(wxid, '✅ 已进入连续对话模式，可以直接发送消息与我对话\n发送"豆包 结束"可结束对话', [user_wxid])
                return
            elif cmd == "结束":
                if not in_conversation:
                    await bot.send_at_message(wxid, "❌ 当前不在连续对话模式", [user_wxid])
                    return
                if not is_conversation_user:
                    await bot.send_at_message(wxid, "❌ 只有发起对话的用户才能结束对话", [user_wxid])
                    return
                self._conversation_mode[wxid] = False
                self._conversation_users.pop(wxid, None)
                self._last_conversation_time.pop(wxid, None)
                await bot.send_at_message(wxid, "✅ 已结束连续对话模式", [user_wxid])
                return

        if in_conversation and is_conversation_user:
            self._last_conversation_time[wxid] = time.time()
            if not content.startswith(tuple(self.command)):
                try:
                    wait_time = self._check_user_limit(wxid, user_wxid)
                    if wait_time > 0:
                        await bot.send_text_message(wxid, random.choice(self.rate_limit_msgs)) if self.natural_response else await bot.send_at_message(wxid, f"慢点慢点，等 {wait_time:.1f} 秒再来", [user_wxid])
                        return
                    if not await self._acquire_token():
                        await bot.send_text_message(wxid, random.choice(self.rate_limit_msgs)) if self.natural_response else await bot.send_at_message(wxid, "忙不过来了，歇会儿", [user_wxid])
                        return
                    result = await self.call_doubao_api(bot, message, content)
                    if not result:
                        await bot.send_text_message(message["FromWxid"], random.choice(self.error_msgs)) if self.natural_response else await bot.send_at_message(message["FromWxid"], "调用不了，等会再试试", [message["SenderWxid"]])
                        return
                    await self.handle_text_message(bot, message, result, content)
                except:
                    await bot.send_text_message(message["FromWxid"], random.choice(self.error_msgs)) if self.natural_response else await bot.send_at_message(message["FromWxid"], "出问题了，等会再试试", [message["SenderWxid"]])
                return

        if not is_doubao_command: return
        if is_doubao_command and len(command) == 1:
            await bot.send_at_message(message["FromWxid"], f"{self.command_format}", [message["SenderWxid"]])
            return
        try:
            wait_time = self._check_user_limit(wxid, user_wxid)
            if wait_time > 0:
                await bot.send_text_message(wxid, random.choice(self.rate_limit_msgs)) if self.natural_response else await bot.send_at_message(wxid, f"慢点慢点，等 {wait_time:.1f} 秒再来", [user_wxid])
                return
            if not await self._acquire_token():
                await bot.send_text_message(wxid, random.choice(self.rate_limit_msgs)) if self.natural_response else await bot.send_at_message(wxid, "忙不过来了，歇会儿", [user_wxid])
                return
            query = command_simple[1].strip() if is_doubao_command else content
            result = await self.call_doubao_api(bot, message, query)
            if not result:
                await bot.send_text_message(message["FromWxid"], random.choice(self.error_msgs)) if self.natural_response else await bot.send_at_message(message["FromWxid"], "❌ 调用豆包API失败，请稍后重试", [message["SenderWxid"]])
                return
            query_content = command_simple[1].strip() if is_doubao_command else content
            await self.handle_text_message(bot, message, result, query_content)

        except:
            if self.natural_response:
                await bot.send_text_message(message["FromWxid"], random.choice(self.error_msgs))
            else:
                await bot.send_at_message(message["FromWxid"], "出问题了，等会再试试", [message["SenderWxid"]])

    async def on_disable(self):
        await self.close_session()

    async def get_session(self):
        async with self._client_lock:
            if self._client is None or self._client.is_closed:
                self._client = httpx.AsyncClient(timeout=httpx.Timeout(connect=10.0, read=20.0, write=20.0, pool=5.0), verify=False, follow_redirects=True, limits=httpx.Limits(max_connections=20, max_keepalive_connections=10))
            return self._client

    async def close_session(self):
        async with self._client_lock:
            if self._client and not self._client.is_closed:
                await self._client.aclose()
                self._client = None

    async def _cleanup_temp_files(self):
        current_time = time.time()

        # 检查是否需要清理
        if current_time - self.last_cleanup < self.cleanup_interval:
            return

        try:
            cleaned = 0
            failed = 0
            async with self._active_files_lock:
                active_files = self._active_files.copy()

            for f in self.temp_dir.glob("*"):
                try:
                    # 跳过活跃文件
                    if str(f) in active_files:
                        continue

                    if current_time - f.stat().st_mtime > self.max_age:
                        # 尝试修改文件权限
                        try:
                            os.chmod(str(f), 0o777)
                        except Exception:
                            pass

                        # 如果是文件被占用，多次重试
                        max_retries = 5  # 增加重试次数
                        base_delay = 0.5  # 基础延迟时间（秒）

                        for retry in range(max_retries):
                            try:
                                f.unlink()
                                cleaned += 1
                                break
                            except PermissionError:
                                if retry < max_retries - 1:
                                    await asyncio.sleep(base_delay * (2 ** retry))
                                else:
                                    failed += 1
                            except FileNotFoundError:
                                break
                            except:
                                failed += 1
                                break

                except:
                    failed += 1
                    continue
            self.last_cleanup = current_time
        except: pass

    async def get_media_sources(self, item_ids):
        """获取媒体资源信息

        Args:
            item_ids: 媒体资源ID列表

        Returns:
            包含媒体资源信息的字典,如果请求失败则返回None
        """
        try:
            # 构造请求URL和参数
            url = "https://www.doubao.com/alice/media/source/3"
            params = {
                "version_code": "20800",
                "language": "zh",
                "device_platform": "web",
                "aid": "497858",
                "real_aid": "497858",
                "pc_version": "1.51.90",
                "pkg_type": "release_version",
                "device_id": "7468716989062841895",
                "web_id": "7468716986638386703",
                "tea_uuid": "7468716986638386703",
                "use-olympus-account": "1",
                "region": "CN",
                "sys_region": "CN",
                "samantha_web": "1"
            }

            # 构造请求头
            headers = self._generate_headers()
            headers.update({
                "Origin": "https://www.doubao.com",
                "Referer": "https://www.doubao.com/chat/",
                "X-Requested-With": "mark.via"
            })

            # 构造请求数据
            data = {"item_ids": item_ids}

            # 获取httpx客户端
            client = await self.get_session()

            # 发送异步请求
            try:
                response = await client.post(
                    url,
                    params=params,
                    json=data,
                    headers=headers,
                    timeout=httpx.Timeout(connect=10.0, read=30.0, write=20.0, pool=5.0)
                )

                if response.status_code != 200:
                    return None

                # 解析响应内容
                result = response.json()
                return result

            except: return None
        except: return None

    def _smart_split_text(self, text, max_length=200):
        if len(text) <= max_length: return [text]
        segments = []
        current_segment = ""
        lines = text.split('\n')
        for line in lines:
            if not line.strip():
                current_segment += '\n'
                continue
            if len(current_segment) + len(line) > max_length:
                if current_segment:
                    segments.append(current_segment.strip())
                    current_segment = ""
                if len(line) > max_length:
                    pos = 0
                    while pos < len(line):
                        next_pos = min(pos + max_length, len(line))
                        segment = line[pos:next_pos].strip()
                        if segment: segments.append(segment)
                        pos = next_pos
                else:
                    current_segment = line
            else:
                if current_segment: current_segment += '\n'
                current_segment += line
        if current_segment: segments.append(current_segment.strip())
        return [seg for seg in segments if seg] or [text]

    async def _text_to_speech(self, text, voice=318):
        try:
            params = {"text": text, "voice": voice}
            url = "http://www.yx520.ltd/API/wzzyy/zmp3.php"
            client = await self.get_session()
            headers = {'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36'}
            try:
                response = await client.get(url, params=params, headers=headers, timeout=httpx.Timeout(connect=10.0, read=30.0, write=20.0, pool=5.0))
                if response.status_code != 200: return None
                audio_data = response.content
                if len(audio_data) < 100: return None
                return audio_data
            except: return None
        except: return None

    def _generate_headers(self):
        flow_trace = f"04-{uuid.uuid4().hex[:16]}-{uuid.uuid4().hex[:16]}-01"
        return {
            "Accept": "*/*",
            "Accept-Encoding": "gzip, deflate",
            "Accept-Language": "zh-CN,zh;q=0.9,en-US;q=0.8,en;q=0.7",
            "Connection": "keep-alive",
            "Content-Type": "application/json",
            "Cookie": self.cookies,
            "Host": "www.doubao.com",
            "Origin": "https://www.doubao.com",
            "Referer": "https://www.doubao.com/chat/",
            "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36",
            "x-flow-trace": flow_trace,
            "Agw-Js-Conv": "str",
            "X-Requested-With": "mark.via",
            "last-event-id": "undefined",
            "Sec-Fetch-Site": "same-origin",
            "Sec-Fetch-Mode": "cors",
            "Sec-Fetch-Dest": "empty"
        }

    async def _mark_file_active(self, filepath):
        async with self._active_files_lock:
            self._active_files.add(filepath)

    async def _mark_file_inactive(self, filepath):
        async with self._active_files_lock:
            self._active_files.discard(filepath)

    async def _delayed_delete_files(self, file_paths, delay_seconds):
        try:
            await asyncio.sleep(delay_seconds)
            for path in file_paths:
                try:
                    if path and os.path.exists(path):
                        await self._mark_file_inactive(path)
                        os.remove(path)
                except: pass
        except: pass
