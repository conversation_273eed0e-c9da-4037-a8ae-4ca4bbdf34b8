2025-07-23 14:06:25 | SUCCESS | 读取主设置成功
2025-07-23 14:06:25 | DEBUG | 最终使用的 Redis 主机地址: 127.0.0.1
2025-07-23 14:06:25 | INFO | 2025/07/23 14:06:25 GetRedisAddr: 127.0.0.1:6379
2025-07-23 14:06:25 | INFO | 2025/07/23 14:06:25 Redis连接成功 | 地址:127.0.0.1:6379 DB:0
2025-07-23 14:06:25 | INFO | 2025/07/23 14:06:25 Server start at :9000
2025-07-23 14:06:25 | SUCCESS | WechatAPI服务已启动
2025-07-23 14:06:26 | INFO | 登录账号信息: wxid: wxid_4usgcju5ey9q29  昵称: 瑶瑶  微信号: Today-KFC  手机号: None
2025-07-23 14:06:26 | INFO | 登录设备信息: device_name: <PERSON>'s Pad  device_id: 49d050a7e94d1ecc40790d74b1c4369f
2025-07-23 14:06:26 | SUCCESS | 登录成功
2025-07-23 14:06:26 | SUCCESS | 已开启自动心跳
2025-07-23 14:06:26 | INFO | 成功加载表情映射文件，共 522 条记录
2025-07-23 14:06:26 | SUCCESS | 数据库初始化成功
2025-07-23 14:06:26 | SUCCESS | 定时任务已启动
2025-07-23 14:06:26 | SUCCESS | 插件 AISignIn 已加载定时任务: {'plugins.AISignIn.main.AISignIn.auto_signin_task'}
2025-07-23 14:06:26 | INFO | 已加载管理员列表: ['wxid_ubbh6q832tcs21', 'admin-wxid']
2025-07-23 14:06:27 | INFO | 播客API初始化成功
2025-07-23 14:06:27 | INFO | 加载配置完成，转换后的数据: {'gh_7057516c9e71': {'name': '唱舞全明星', 'groups': ['27852221909@chatroom', '55878994168@chatroom', '51891329927@chatroom']}, 'gh_d4d21a0e4623': {'name': '两颗桃', 'groups': ['48097389945@chatroom']}, 'gh_410ff2fa6ec9': {'name': 'NGC660 Ai研究院', 'groups': ['47442567074@chatroom']}, 'gh_f78c53c507c0': {'name': '不正常吐槽君', 'groups': ['48097389945@chatroom']}, 'gh_ba3381c847b3': {'name': '唱舞星计划', 'groups': ['51891329927@chatroom']}, 'gh_1a102fb4cad9': {'name': '我在娱乐圈的唯一人脉', 'groups': ['48097389945@chatroom', '27852221909@chatroom']}}
2025-07-23 14:06:27 | INFO | ArticleForwarder插件初始化完成 - 监控配置: {'gh_7057516c9e71': {'name': '唱舞全明星', 'groups': ['27852221909@chatroom', '55878994168@chatroom', '51891329927@chatroom']}, 'gh_d4d21a0e4623': {'name': '两颗桃', 'groups': ['48097389945@chatroom']}, 'gh_410ff2fa6ec9': {'name': 'NGC660 Ai研究院', 'groups': ['47442567074@chatroom']}, 'gh_f78c53c507c0': {'name': '不正常吐槽君', 'groups': ['48097389945@chatroom']}, 'gh_ba3381c847b3': {'name': '唱舞星计划', 'groups': ['51891329927@chatroom']}, 'gh_1a102fb4cad9': {'name': '我在娱乐圈的唯一人脉', 'groups': ['48097389945@chatroom', '27852221909@chatroom']}}
2025-07-23 14:06:27 | DEBUG | [TempFileManager] 添加清理规则: default
2025-07-23 14:06:27 | DEBUG | [TempFileManager] 添加清理规则: images
2025-07-23 14:06:27 | DEBUG | [TempFileManager] 添加清理规则: videos
2025-07-23 14:06:27 | DEBUG | [TempFileManager] 添加清理规则: audio
2025-07-23 14:06:27 | DEBUG | [TempFileManager] 添加清理规则: temp
2025-07-23 14:06:27 | DEBUG | [TempFileManager] 添加清理规则: large_files
2025-07-23 14:06:27 | INFO | [TempFileManager] 临时文件清理任务已启动
2025-07-23 14:06:27 | INFO | [ChatSummary] 数据库初始化成功
2025-07-23 14:06:27 | INFO | 成功加载表情映射文件，共 522 条记录
2025-07-23 14:06:27 | SUCCESS | 插件 GoodMorning 已加载定时任务: {'plugins.GoodMorning.main.GoodMorning.daily_task'}
2025-07-23 14:06:27 | SUCCESS | 插件 News 已加载定时任务: {'plugins.News.main.News.noon_news', 'plugins.News.main.News.night_news'}
2025-07-23 14:06:27 | INFO | [PatReply] AMR文件已存在且是最新的: C:\XYBotV2\data\paiyipai\1_BFB5258B-2B13-4CF4-A40C-5BCCE74E5414_converted.amr
2025-07-23 14:06:27 | DEBUG | [PatReply] 从缓存加载base64: 18228 字符
2025-07-23 14:06:27 | SUCCESS | 插件 QuarkSignIn 已加载定时任务: {'plugins.QuarkSignIn.main.QuarkSignIn.auto_signin_task'}
2025-07-23 14:06:27 | INFO | [QuarkSignIn] 已设置自动签到时间: 08:00
2025-07-23 14:06:27 | INFO | 插件状态: 启用, 检查间隔: 3600秒, 最大重试次数: 3, 重试延迟: 5秒
2025-07-23 14:06:27 | INFO | 已加载管理员列表: ['wxid_ubbh6q832tcs21', 'admin-wxid']
2025-07-23 14:06:27 | INFO | 已加载 2 个改名监控群和 1 个退群监控群
2025-07-23 14:06:27 | INFO | [RenameReminder] 开始启用插件...
2025-07-23 14:06:27 | SUCCESS | 插件 RenameReminder 已加载定时任务: {'plugins.RenameReminder.main.RenameReminder.check_name_changes'}
2025-07-23 14:06:27 | INFO | [RenameReminder] 插件已启用，开始初始化...
2025-07-23 14:06:27 | INFO | [RenameReminder] 支持的命令: 开启改名提醒, 关闭改名提醒, 开启退群提醒, 关闭退群提醒, 查看提醒状态
2025-07-23 14:06:27 | INFO | 已设置检查间隔为 3600 秒
2025-07-23 14:06:27 | INFO | 已加载 2 个监控群，开始更新成员列表
2025-07-23 14:06:27 | DEBUG | 已更新群 51891329927@chatroom 的成员列表
2025-07-23 14:06:28 | DEBUG | 已更新群 27852221909@chatroom 的成员列表
2025-07-23 14:06:28 | SUCCESS | 插件 TimerTask 已加载定时任务: {'plugins.TimerTask.main.TimerTask.check_tasks'}
2025-07-23 14:06:28 | INFO | [VideoDemand] 加载了 128 个视频类别配置
2025-07-23 14:06:28 | INFO | [YaoyaoPlugin] 私聊黑名单用户: ['gh_a5cf07d474f1', 'gh_ba3381c847b3']
2025-07-23 14:06:28 | INFO | [YaoyaoPlugin] 已加载管理员列表: ['wxid_ubbh6q832tcs21', 'admin-wxid']
2025-07-23 14:06:28 | INFO | [yuanbao] 插件初始化完成
2025-07-23 14:06:28 | DEBUG | [yuanbao] 指令: ['元宝', 'yuanbao']
2025-07-23 14:06:28 | DEBUG | [yuanbao] 绘图模型: gpt_175B_0404
2025-07-23 14:06:28 | DEBUG | [yuanbao] 认证信息加载状态: hy_token=True, hy_user=True, agent_id=True, x_uskey=True
2025-07-23 14:06:28 | SUCCESS | 已加载插件: ['AdminPoint', 'AdminSignInReset', 'AdminWhitelist', 'AISignIn', 'AppImageTester', 'AppMessageTester', 'ArticleForwarder', 'BaiduAgentsPlugin', 'BaiduDraw', 'BotStatus', 'ChatSummary', 'DanceSignInPlugin', 'DeepseekPlugin', 'Doubao', 'DoubaoDrawing', 'DoubaoImageRecognition', 'DouBaoImageToImage', 'DouBaoImageToVideo', 'DoubaoVideoSearch', 'DouHui', 'EmojiTestPlugin', 'FixedQuoteTest', 'GetContact', 'Gomoku', 'GoodMorning', 'HunyuanDraw', 'ImageEcho', 'ImageToImage', 'JiemengDraw', 'KeLingImageToImage', 'KlingAI', 'Leaderboard', 'LuckyDraw', 'ManagePlugin', 'MeituAI', 'Menu', 'MiniProgramTester', 'Music', 'News', 'PatReply', 'PointTrade', 'QuarkSignIn', 'QueryPoint', 'RandomMember', 'RandomPicture', 'RenameReminder', 'RevokePlugin', 'RoboNeo', 'SignIn', 'TempFileManagerPlugin', 'TencentLke', 'TimerTask', 'TongyiDraw', 'URLShortener', 'VideoDemand', 'VideoParserPlugin', 'VideoTest', 'VivoAgentsPlugin', 'VoiceTest', 'Warthunder', 'WeatherQuery', 'WelcomePlugin', 'Xunfei', 'YaoyaoPlugin', 'Yuanbao', '抽签', '造梦次元']
2025-07-23 14:06:28 | INFO | 处理堆积消息中
2025-07-23 14:06:29 | SUCCESS | 处理堆积消息完毕
2025-07-23 14:06:29 | SUCCESS | 开始处理消息
2025-07-23 14:06:43 | DEBUG | 收到消息: {'MsgId': 1030662096, 'FromUserName': {'string': '55878994168@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 1, 'Content': {'string': 'wxid_ubbh6q832tcs21:\n测试应用图片'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1753250819, 'MsgSource': '<msgsource>\n\t<pua>1</pua>\n\t<eggIncluded>1</eggIncluded>\n\t<silence>0</silence>\n\t<membercount>3</membercount>\n\t<signature>N0_V1_HmPg3Bk8|v1_kDZWaEZ5</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'PushContent': '郭 : 测试应用图片', 'NewMsgId': 7621565202248184232, 'MsgSeq': 871394263}
2025-07-23 14:06:43 | INFO | 收到文本消息: 消息ID:1030662096 来自:55878994168@chatroom 发送人:wxid_ubbh6q832tcs21 @:[] 内容:测试应用图片
2025-07-23 14:06:43 | INFO | [AppImageTester] 创建测试图片: C:\XYBotV2\plugins\AppImageTester\test_image.jpg
2025-07-23 14:06:43 | INFO | [AppImageTester] 开始测试发送带应用信息的图片消息，应用名称: Kimi智能助手
2025-07-23 14:06:44 | INFO | 发送带应用信息的图片消息: 对方wxid:55878994168@chatroom 应用:Kimi智能助手
2025-07-23 14:06:46 | INFO | 发送文字消息: 对方wxid:55878994168@chatroom at:['wxid_ubbh6q832tcs21'] 内容:@郭 ✅ 带应用信息的图片消息发送成功！应用名称: Kimi智能助手
2025-07-23 14:06:46 | INFO | [AppImageTester] 发送成功: ('55878994168@chatroom_1753250803', 1753250820, 794746796714779694)
2025-07-23 14:06:47 | DEBUG | 处理消息内容: '测试应用图片'
2025-07-23 14:06:47 | DEBUG | 消息内容 '测试应用图片' 不匹配任何命令，忽略
2025-07-23 14:08:07 | DEBUG | 收到消息: {'MsgId': 1069608789, 'FromUserName': {'string': '27852221909@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 1, 'Content': {'string': 'wxid_x4s6k999g6qg22:\n所以夏日副本修复了吗'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1753250903, 'MsgSource': '<msgsource>\n\t<bizflag>0</bizflag>\n\t<pua>1</pua>\n\t<eggIncluded>1</eggIncluded>\n\t<silence>1</silence>\n\t<membercount>146</membercount>\n\t<signature>N0_V1_0FjQjlap|v1_EvAfSELU</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'NewMsgId': 6943010571794688975, 'MsgSeq': 871394268}
2025-07-23 14:08:07 | INFO | 收到文本消息: 消息ID:1069608789 来自:27852221909@chatroom 发送人:wxid_x4s6k999g6qg22 @:[] 内容:所以夏日副本修复了吗
2025-07-23 14:08:07 | DEBUG | 处理消息内容: '所以夏日副本修复了吗'
2025-07-23 14:08:07 | DEBUG | 消息内容 '所以夏日副本修复了吗' 不匹配任何命令，忽略
2025-07-23 14:08:16 | DEBUG | 收到消息: {'MsgId': 1534267704, 'FromUserName': {'string': '27852221909@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 47, 'Content': {'string': 'wxid_x4s6k999g6qg22:\n<msg><emoji fromusername="wxid_x4s6k999g6qg22" tousername="27852221909@chatroom" type="2" idbuffer="media:0_0" md5="4e99708038aca6f4b9effeb2c8c07d21" len="287369" productid="" androidmd5="4e99708038aca6f4b9effeb2c8c07d21" androidlen="287369" s60v3md5="4e99708038aca6f4b9effeb2c8c07d21" s60v3len="287369" s60v5md5="4e99708038aca6f4b9effeb2c8c07d21" s60v5len="287369" cdnurl="http://vweixinf.tc.qq.com/110/20401/stodownload?m=4e99708038aca6f4b9effeb2c8c07d21&amp;filekey=30440201010430302e02016e0402535a042034653939373038303338616361366634623965666665623263386330376432310203046289040d00000004627466730000000132&amp;hy=SZ&amp;storeid=264fb3feb00083eb95d4f8ce70000006e01004fb1535a0226cbc1e07c64b9f&amp;ef=1&amp;bizid=1022" designerid="" thumburl="" encrypturl="http://vweixinf.tc.qq.com/110/20402/stodownload?m=8c723329a68afb1d8409213214946c33&amp;filekey=30440201010430302e02016e0402535a042038633732333332396136386166623164383430393231333231343934366333330203046290040d00000004627466730000000132&amp;hy=SZ&amp;storeid=264fb3feb0008d3925d4f8ce70000006e02004fb2535a0226cbc1e07c64baa&amp;ef=2&amp;bizid=1022" aeskey="ae40479e143a4e17b7d43d3f1dc95639" externurl="http://vweixinf.tc.qq.com/110/20403/stodownload?m=818cfa0f75bfee79d387e9bbdef1581a&amp;filekey=30440201010430302e02016e0402535a042038313863666130663735626665653739643338376539626264656631353831610203008d30040d00000004627466730000000132&amp;hy=SZ&amp;storeid=264fb3feb0009877e5d4f8ce70000006e03004fb3535a0226cbc1e07c64bc3&amp;ef=3&amp;bizid=1022" externmd5="9b283bd08d8e5de7a26a4034ff84e12e" width="305" height="305" tpurl="" tpauthkey="" attachedtext="" attachedtextcolor="" lensid="" emojiattr="" linkid="" desc=""></emoji><gameext type="0" content="0"></gameext></msg>'}, 'Status': 3, 'ImgStatus': 2, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1753250912, 'MsgSource': '<msgsource>\n\t<silence>1</silence>\n\t<membercount>146</membercount>\n\t<signature>N0_V1_UtPJnT6z|v1_qlWSse4f</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'NewMsgId': 4642790860203718777, 'MsgSeq': 871394269}
2025-07-23 14:08:16 | INFO | 收到表情消息: 消息ID:1534267704 来自:27852221909@chatroom 发送人:wxid_x4s6k999g6qg22 MD5:4e99708038aca6f4b9effeb2c8c07d21 大小:287369
2025-07-23 14:08:16 | DEBUG | [DoubaoImageRecognition] 缓存表情消息: 4642790860203718777
2025-07-23 14:08:49 | DEBUG | 收到消息: {'MsgId': 1290794322, 'FromUserName': {'string': '27852221909@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 1, 'Content': {'string': 'wxid_ubbh6q832tcs21:\n你打一下不就知道了'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1753250945, 'MsgSource': '<msgsource>\n\t<pua>1</pua>\n\t<eggIncluded>1</eggIncluded>\n\t<silence>1</silence>\n\t<membercount>146</membercount>\n\t<signature>N0_V1_rhs8sXFd|v1_clpejYjX</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'NewMsgId': 724061038276268379, 'MsgSeq': 871394270}
2025-07-23 14:08:49 | INFO | 收到文本消息: 消息ID:1290794322 来自:27852221909@chatroom 发送人:wxid_ubbh6q832tcs21 @:[] 内容:你打一下不就知道了
2025-07-23 14:08:49 | DEBUG | 处理消息内容: '你打一下不就知道了'
2025-07-23 14:08:49 | DEBUG | 消息内容 '你打一下不就知道了' 不匹配任何命令，忽略
2025-07-23 14:08:53 | DEBUG | 收到消息: {'MsgId': 162999339, 'FromUserName': {'string': '27852221909@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 47, 'Content': {'string': 'wxid_ubbh6q832tcs21:\n<msg><emoji fromusername = "wxid_ubbh6q832tcs21" tousername = "27852221909@chatroom" type="2" idbuffer="media:0_0" md5="5e8ce6b1ffcb1c27ed2367faadb2c780" len = "11191" productid="" androidmd5="5e8ce6b1ffcb1c27ed2367faadb2c780" androidlen="11191" s60v3md5 = "5e8ce6b1ffcb1c27ed2367faadb2c780" s60v3len="11191" s60v5md5 = "5e8ce6b1ffcb1c27ed2367faadb2c780" s60v5len="11191" cdnurl = "http://wxapp.tc.qq.com/262/20304/stodownload?m=5e8ce6b1ffcb1c27ed2367faadb2c780&amp;filekey=30340201010420301e020201060402534804105e8ce6b1ffcb1c27ed2367faadb2c78002022bb7040d00000004627466730000000132&amp;hy=SH&amp;storeid=2630fc3870008d6c8000000000000010600004f5053482b86db40b654daf7c&amp;bizid=1023" designerid = "" thumburl = "" encrypturl = "http://wxapp.tc.qq.com/262/20304/stodownload?m=a7da7a5fb6d33601a15b2483b5eb4fbb&amp;filekey=30340201010420301e02020106040253480410a7da7a5fb6d33601a15b2483b5eb4fbb02022bc0040d00000004627466730000000132&amp;hy=SH&amp;storeid=2630fc387000b507c000000000000010600004f505348279d2a00b65511cbf&amp;bizid=1023" aeskey= "572b4151f51d4c7ff84594c495b8e45e" externurl = "http://wxapp.tc.qq.com/262/20304/stodownload?m=8d09ec763877d508874687cbdb56fe05&amp;filekey=30340201010420301e020201060402534804108d09ec763877d508874687cbdb56fe0502021cc0040d00000004627466730000000132&amp;hy=SH&amp;storeid=2630fc387000e2952000000000000010600004f5053482056fb40b65474de4&amp;bizid=1023" externmd5 = "2d93ebc2fd5a0200d1d16c87b28db76c" width= "80" height= "80" tpurl= "" tpauthkey= "" attachedtext= "" attachedtextcolor= "" lensid= "" emojiattr= "" linkid= "" desc= "" ></emoji>  </msg>'}, 'Status': 3, 'ImgStatus': 2, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1753250949, 'MsgSource': '<msgsource>\n\t<silence>1</silence>\n\t<membercount>146</membercount>\n\t<signature>N0_V1_3iY8K7vL|v1_AbBKwksC</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'NewMsgId': 4565394290310075316, 'MsgSeq': 871394271}
2025-07-23 14:08:53 | INFO | 收到表情消息: 消息ID:162999339 来自:27852221909@chatroom 发送人:wxid_ubbh6q832tcs21 MD5:5e8ce6b1ffcb1c27ed2367faadb2c780 大小:11191
2025-07-23 14:08:53 | DEBUG | [DoubaoImageRecognition] 缓存表情消息: 4565394290310075316
2025-07-23 14:08:59 | DEBUG | 收到消息: {'MsgId': 128400101, 'FromUserName': {'string': '48097389945@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 1, 'Content': {'string': 'wxid_wlnzvr8ivgd422:\n串门'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1753250955, 'MsgSource': '<msgsource>\n\t<pua>1</pua>\n\t<eggIncluded>1</eggIncluded>\n\t<silence>0</silence>\n\t<membercount>62</membercount>\n\t<signature>N0_V1_sMcEEWUl|v1_w4I0HKrh</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'PushContent': '锦岚（开玩笑就退群版） : 串门', 'NewMsgId': 4270024480293100611, 'MsgSeq': 871394272}
2025-07-23 14:08:59 | INFO | 收到文本消息: 消息ID:128400101 来自:48097389945@chatroom 发送人:wxid_wlnzvr8ivgd422 @:[] 内容:串门
2025-07-23 14:09:00 | INFO | 发送表情消息: 对方wxid:48097389945@chatroom md5:6bf139b37c4fc9a1ae052bedf4bdc8db 总长度:9992069
2025-07-23 14:09:00 | DEBUG | 处理消息内容: '串门'
2025-07-23 14:09:00 | DEBUG | 消息内容 '串门' 不匹配任何命令，忽略
2025-07-23 14:09:01 | DEBUG | 收到消息: {'MsgId': 1363825586, 'FromUserName': {'string': '27852221909@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 1, 'Content': {'string': 'wxid_ohq9p1qosjzq22:\n错误❌'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1753250956, 'MsgSource': '<msgsource>\n\t<pua>1</pua>\n\t<eggIncluded>1</eggIncluded>\n\t<silence>1</silence>\n\t<membercount>146</membercount>\n\t<signature>N0_V1_ws665OMO|v1_+pZqXZ89</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'NewMsgId': 2402529470511807944, 'MsgSeq': 871394275}
2025-07-23 14:09:01 | INFO | 收到文本消息: 消息ID:1363825586 来自:27852221909@chatroom 发送人:wxid_ohq9p1qosjzq22 @:[] 内容:错误❌
2025-07-23 14:09:01 | DEBUG | 处理消息内容: '错误❌'
2025-07-23 14:09:01 | DEBUG | 消息内容 '错误❌' 不匹配任何命令，忽略
2025-07-23 14:09:03 | DEBUG | 收到消息: {'MsgId': 260115250, 'FromUserName': {'string': '27852221909@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 1, 'Content': {'string': 'wxid_ohq9p1qosjzq22:\n唱舞签到'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1753250958, 'MsgSource': '<msgsource>\n\t<pua>1</pua>\n\t<eggIncluded>1</eggIncluded>\n\t<silence>1</silence>\n\t<membercount>146</membercount>\n\t<signature>N0_V1_TUIMmkgJ|v1_WL8GXfMm</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'NewMsgId': 5762951770804443817, 'MsgSeq': 871394276}
2025-07-23 14:09:03 | INFO | 收到文本消息: 消息ID:260115250 来自:27852221909@chatroom 发送人:wxid_ohq9p1qosjzq22 @:[] 内容:唱舞签到
2025-07-23 14:09:04 | INFO | 发送链接消息: 对方wxid:27852221909@chatroom 链接:https://open.weixin.qq.com/connect/oauth2/authorize?appid=wx6de432d4ad7e151c&redirect_uri=http%3A%2F%2Freserve.fhsj.xipu.com%2Fapi%2Fsign%2Findex&response_type=code&scope=snsapi_base&state=STATE#wechat_redirect 标题:唱舞全明星 描述:点击进入签到页面，领取专属福利 缩略图链接:https://mmbiz.qpic.cn/mmbiz_jpg/RicuJvxibRtUBlv9G75UTulanktDF1OxFO7Wyhzs1WS609tq1j9icfNhLkM6zB3lwM5ZlbgQia1ibIcxuj35WAm465w/640?wxtype=jpeg&wxfrom=0
2025-07-23 14:09:04 | DEBUG | 处理消息内容: '唱舞签到'
2025-07-23 14:09:04 | DEBUG | 消息内容 '唱舞签到' 不匹配任何命令，忽略
2025-07-23 14:09:05 | DEBUG | 收到消息: {'MsgId': 1184563745, 'FromUserName': {'string': '48097389945@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 1, 'Content': {'string': 'wxid_wlnzvr8ivgd422:\n打小郭'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1753250961, 'MsgSource': '<msgsource>\n\t<pua>1</pua>\n\t<eggIncluded>1</eggIncluded>\n\t<silence>0</silence>\n\t<membercount>62</membercount>\n\t<signature>N0_V1_rzhDiXTV|v1_2qwD8YMM</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'PushContent': '锦岚（开玩笑就退群版） : 打小郭', 'NewMsgId': 2546482190155823972, 'MsgSeq': 871394279}
2025-07-23 14:09:05 | INFO | 收到文本消息: 消息ID:1184563745 来自:48097389945@chatroom 发送人:wxid_wlnzvr8ivgd422 @:[] 内容:打小郭
2025-07-23 14:09:06 | INFO | 发送表情消息: 对方wxid:48097389945@chatroom md5:fda793ce68ae07b9633e416d89fdcc74 总长度:9992069
2025-07-23 14:09:06 | DEBUG | 处理消息内容: '打小郭'
2025-07-23 14:09:06 | DEBUG | 消息内容 '打小郭' 不匹配任何命令，忽略
2025-07-23 14:09:08 | DEBUG | 收到消息: {'MsgId': 1710088339, 'FromUserName': {'string': '27852221909@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 1, 'Content': {'string': 'wxid_x4s6k999g6qg22:\n群里肯定有人刷了'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1753250961, 'MsgSource': '<msgsource>\n\t<bizflag>0</bizflag>\n\t<pua>1</pua>\n\t<eggIncluded>1</eggIncluded>\n\t<silence>1</silence>\n\t<membercount>146</membercount>\n\t<signature>N0_V1_Zy9vB3W/|v1_0pyXgpve</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'NewMsgId': 4114416080425043225, 'MsgSeq': 871394280}
2025-07-23 14:09:08 | INFO | 收到文本消息: 消息ID:1710088339 来自:27852221909@chatroom 发送人:wxid_x4s6k999g6qg22 @:[] 内容:群里肯定有人刷了
2025-07-23 14:09:08 | DEBUG | 处理消息内容: '群里肯定有人刷了'
2025-07-23 14:09:08 | DEBUG | 消息内容 '群里肯定有人刷了' 不匹配任何命令，忽略
2025-07-23 14:09:11 | DEBUG | 收到消息: {'MsgId': 1781964173, 'FromUserName': {'string': '27852221909@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 10002, 'Content': {'string': 'wxid_ohq9p1qosjzq22:\n<sysmsg type="revokemsg"><revokemsg><session>27852221909@chatroom</session><msgid>1790688844</msgid><newmsgid>2402529470511807944</newmsgid><replacemsg><![CDATA["浅棠云雾" 撤回了一条消息]]></replacemsg><announcement_id><![CDATA[]]></announcement_id></revokemsg></sysmsg>'}, 'Status': 4, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1753250960, 'MsgSource': '<msgsource>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'NewMsgId': 534337520850414785, 'MsgSeq': 871394283}
2025-07-23 14:09:11 | DEBUG | 系统消息类型: revokemsg
2025-07-23 14:09:11 | INFO | 未知的系统消息类型: {'MsgId': 1781964173, 'ToWxid': 'wxid_4usgcju5ey9q29', 'MsgType': 10002, 'Content': '\n<sysmsg type="revokemsg"><revokemsg><session>27852221909@chatroom</session><msgid>1790688844</msgid><newmsgid>2402529470511807944</newmsgid><replacemsg><![CDATA["浅棠云雾" 撤回了一条消息]]></replacemsg><announcement_id><![CDATA[]]></announcement_id></revokemsg></sysmsg>', 'Status': 4, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1753250960, 'MsgSource': '<msgsource>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'NewMsgId': 534337520850414785, 'MsgSeq': 871394283, 'FromWxid': '27852221909@chatroom', 'IsGroup': True, 'SenderWxid': 'wxid_ohq9p1qosjzq22'}
2025-07-23 14:09:19 | DEBUG | 收到消息: {'MsgId': 1632372697, 'FromUserName': {'string': '48097389945@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 47, 'Content': {'string': 'wxid_ubbh6q832tcs21:\n<msg><emoji fromusername = "wxid_ubbh6q832tcs21" tousername = "48097389945@chatroom" type="2" idbuffer="media:0_0" md5="21d891c863e23d522e6f29e23099b98c" len = "7226" productid="" androidmd5="21d891c863e23d522e6f29e23099b98c" androidlen="7226" s60v3md5 = "21d891c863e23d522e6f29e23099b98c" s60v3len="7226" s60v5md5 = "21d891c863e23d522e6f29e23099b98c" s60v5len="7226" cdnurl = "http://vweixinf.tc.qq.com/110/20401/stodownload?m=21d891c863e23d522e6f29e23099b98c&amp;filekey=3043020101042f302d02016e0402535a0420323164383931633836336532336435323265366632396532333039396239386302021c3a040d00000004627466730000000131&amp;hy=SZ&amp;storeid=323032313039323631383230323730303061323837303036373032653333333731333566303930303030303036653031303034666231&amp;ef=1&amp;bizid=1022" designerid = "" thumburl = "" encrypturl = "http://vweixinf.tc.qq.com/110/20402/stodownload?m=69879124af544ed218216113ad39cb89&amp;filekey=3043020101042f302d02016e0402535a0420363938373931323461663534346564323138323136313133616433396362383902021c40040d00000004627466730000000131&amp;hy=SZ&amp;storeid=323032313039323631383230323730303061616435303036373032653333333731333566303930303030303036653032303034666232&amp;ef=2&amp;bizid=1022" aeskey= "6a712fcc6a5c4c06b68bcac6a6136ca4" externurl = "http://vweixinf.tc.qq.com/110/20403/stodownload?m=2944e8317e4b2f50809265b094086113&amp;filekey=3043020101042f302d02016e0402535a0420323934346538333137653462326635303830393236356230393430383631313302020e20040d00000004627466730000000131&amp;hy=SZ&amp;storeid=323032313039323631383230323730303062356439323036373032653333333731333566303930303030303036653033303034666233&amp;ef=3&amp;bizid=1022" externmd5 = "6290ed6fbd45e89fcd0c3ec7029a920d" width= "300" height= "224" tpurl= "" tpauthkey= "" attachedtext= "" attachedtextcolor= "" lensid= "" emojiattr= "" linkid= "" desc= "" ></emoji>  </msg>'}, 'Status': 3, 'ImgStatus': 2, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1753250975, 'MsgSource': '<msgsource>\n\t<silence>0</silence>\n\t<membercount>62</membercount>\n\t<signature>N0_V1_C04BXad2|v1_LsSe0DU2</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'PushContent': '郭在群聊中发了一个表情', 'NewMsgId': 3217042467518104382, 'MsgSeq': 871394284}
2025-07-23 14:09:19 | INFO | 收到表情消息: 消息ID:1632372697 来自:48097389945@chatroom 发送人:wxid_ubbh6q832tcs21 MD5:21d891c863e23d522e6f29e23099b98c 大小:7226
2025-07-23 14:09:19 | DEBUG | [DoubaoImageRecognition] 缓存表情消息: 3217042467518104382
2025-07-23 14:09:20 | DEBUG | 收到消息: {'MsgId': 1376699501, 'FromUserName': {'string': '48097389945@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 10002, 'Content': {'string': '48097389945@chatroom:\n<sysmsg type="pat">\n<pat>\n  <fromusername>wxid_wlnzvr8ivgd422</fromusername>\n  <chatusername>48097389945@chatroom</chatusername>\n  <pattedusername>wxid_ubbh6q832tcs21</pattedusername>\n  <patsuffix><![CDATA[▶ ı|ıı|ıı 3]]></patsuffix>\n  <patsuffixversion>2</patsuffixversion>\n\n\n\n\n\n\n  <template><![CDATA["${wxid_wlnzvr8ivgd422}" 拍了拍 "${wxid_ubbh6q832tcs21}" ▶ ı|ıı|ıı 3]]></template>\n\n\n\n\n\n\n\n</pat>\n</sysmsg>'}, 'Status': 4, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1753250974, 'MsgSource': '<msgsource>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'NewMsgId': 3587161300710626629, 'MsgSeq': 871394285}
2025-07-23 14:09:20 | DEBUG | 系统消息类型: pat
2025-07-23 14:09:20 | INFO | 收到拍一拍消息: 消息ID:1376699501 来自:48097389945@chatroom 发送人:48097389945@chatroom 拍者:wxid_wlnzvr8ivgd422 被拍:wxid_ubbh6q832tcs21 后缀:▶ ı|ıı|ıı 3
2025-07-23 14:09:20 | DEBUG | [PatReply] 被拍者 wxid_ubbh6q832tcs21 不是目标用户 wxid_4usgcju5ey9q29，跳过
2025-07-23 14:09:30 | DEBUG | 收到消息: {'MsgId': 670522665, 'FromUserName': {'string': '27852221909@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 49, 'Content': {'string': 'wxid_ohq9p1qosjzq22:\n<?xml version="1.0"?>\n<msg>\n\t<appmsg appid="" sdkver="0">\n\t\t<title>我还在1</title>\n\t\t<des />\n\t\t<username />\n\t\t<action>view</action>\n\t\t<type>57</type>\n\t\t<showtype>0</showtype>\n\t\t<content />\n\t\t<url />\n\t\t<lowurl />\n\t\t<forwardflag>0</forwardflag>\n\t\t<dataurl />\n\t\t<lowdataurl />\n\t\t<contentattr>0</contentattr>\n\t\t<streamvideo>\n\t\t\t<streamvideourl />\n\t\t\t<streamvideototaltime>0</streamvideototaltime>\n\t\t\t<streamvideotitle />\n\t\t\t<streamvideowording />\n\t\t\t<streamvideoweburl />\n\t\t\t<streamvideothumburl />\n\t\t\t<streamvideoaduxinfo />\n\t\t\t<streamvideopublishid />\n\t\t</streamvideo>\n\t\t<canvasPageItem>\n\t\t\t<canvasPageXml><![CDATA[]]></canvasPageXml>\n\t\t</canvasPageItem>\n\t\t<refermsg>\n\t\t\t<type>1</type>\n\t\t\t<svrid>4114416080425043225</svrid>\n\t\t\t<fromusr>27852221909@chatroom</fromusr>\n\t\t\t<chatusr>wxid_x4s6k999g6qg22</chatusr>\n\t\t\t<displayname>栀栀</displayname>\n\t\t\t<msgsource>&lt;msgsource&gt;\n\t&lt;bizflag&gt;0&lt;/bizflag&gt;\n\t&lt;pua&gt;1&lt;/pua&gt;\n\t&lt;eggIncluded&gt;1&lt;/eggIncluded&gt;\n\t&lt;silence&gt;1&lt;/silence&gt;\n\t&lt;membercount&gt;146&lt;/membercount&gt;\n\t&lt;signature&gt;N0_V1_UMdlVtkp|v1_F0ssEzWu&lt;/signature&gt;\n\t&lt;tmp_node&gt;\n\t\t&lt;publisher-id&gt;&lt;/publisher-id&gt;\n\t&lt;/tmp_node&gt;\n&lt;/msgsource&gt;\n</msgsource>\n\t\t\t<content>\n群里肯定有人刷了</content>\n\t\t\t<strid />\n\t\t\t<createtime>1753250961</createtime>\n\t\t</refermsg>\n\t\t<appattach>\n\t\t\t<totallen>0</totallen>\n\t\t\t<attachid />\n\t\t\t<cdnattachurl />\n\t\t\t<emoticonmd5></emoticonmd5>\n\t\t\t<aeskey></aeskey>\n\t\t\t<fileext />\n\t\t\t<islargefilemsg>0</islargefilemsg>\n\t\t</appattach>\n\t\t<extinfo />\n\t\t<androidsource>0</androidsource>\n\t\t<thumburl />\n\t\t<mediatagname />\n\t\t<messageaction><![CDATA[]]></messageaction>\n\t\t<messageext><![CDATA[]]></messageext>\n\t\t<emoticongift>\n\t\t\t<packageflag>0</packageflag>\n\t\t\t<packageid />\n\t\t</emoticongift>\n\t\t<emoticonshared>\n\t\t\t<packageflag>0</packageflag>\n\t\t\t<packageid />\n\t\t</emoticonshared>\n\t\t<designershared>\n\t\t\t<designeruin>0</designeruin>\n\t\t\t<designername>null</designername>\n\t\t\t<designerrediretcturl><![CDATA[null]]></designerrediretcturl>\n\t\t</designershared>\n\t\t<emotionpageshared>\n\t\t\t<tid>0</tid>\n\t\t\t<title>null</title>\n\t\t\t<desc>null</desc>\n\t\t\t<iconUrl><![CDATA[null]]></iconUrl>\n\t\t\t<secondUrl>null</secondUrl>\n\t\t\t<pageType>0</pageType>\n\t\t\t<setKey>null</setKey>\n\t\t</emotionpageshared>\n\t\t<webviewshared>\n\t\t\t<shareUrlOriginal />\n\t\t\t<shareUrlOpen />\n\t\t\t<jsAppId />\n\t\t\t<publisherId />\n\t\t\t<publisherReqId />\n\t\t</webviewshared>\n\t\t<template_id />\n\t\t<md5 />\n\t\t<websearch>\n\t\t\t<rec_category>0</rec_category>\n\t\t\t<channelId>0</channelId>\n\t\t</websearch>\n\t\t<weappinfo>\n\t\t\t<username />\n\t\t\t<appid />\n\t\t\t<appservicetype>0</appservicetype>\n\t\t\t<secflagforsinglepagemode>0</secflagforsinglepagemode>\n\t\t\t<videopageinfo>\n\t\t\t\t<thumbwidth>0</thumbwidth>\n\t\t\t\t<thumbheight>0</thumbheight>\n\t\t\t\t<fromopensdk>0</fromopensdk>\n\t\t\t</videopageinfo>\n\t\t</weappinfo>\n\t\t<statextstr />\n\t\t<musicShareItem>\n\t\t\t<musicDuration>0</musicDuration>\n\t\t</musicShareItem>\n\t\t<finderLiveProductShare>\n\t\t\t<finderLiveID><![CDATA[]]></finderLiveID>\n\t\t\t<finderUsername><![CDATA[]]></finderUsername>\n\t\t\t<finderObjectID><![CDATA[]]></finderObjectID>\n\t\t\t<finderNonceID><![CDATA[]]></finderNonceID>\n\t\t\t<liveStatus><![CDATA[]]></liveStatus>\n\t\t\t<appId><![CDATA[]]></appId>\n\t\t\t<pagePath><![CDATA[]]></pagePath>\n\t\t\t<productId><![CDATA[]]></productId>\n\t\t\t<coverUrl><![CDATA[]]></coverUrl>\n\t\t\t<productTitle><![CDATA[]]></productTitle>\n\t\t\t<marketPrice><![CDATA[0]]></marketPrice>\n\t\t\t<sellingPrice><![CDATA[0]]></sellingPrice>\n\t\t\t<platformHeadImg><![CDATA[]]></platformHeadImg>\n\t\t\t<platformName><![CDATA[]]></platformName>\n\t\t\t<shopWindowId><![CDATA[]]></shopWindowId>\n\t\t\t<flashSalePrice><![CDATA[0]]></flashSalePrice>\n\t\t\t<flashSaleEndTime><![CDATA[0]]></flashSaleEndTime>\n\t\t\t<ecSource><![CDATA[]]></ecSource>\n\t\t\t<sellingPriceWording><![CDATA[]]></sellingPriceWording>\n\t\t\t<platformIconURL><![CDATA[]]></platformIconURL>\n\t\t\t<firstProductTagURL><![CDATA[]]></firstProductTagURL>\n\t\t\t<firstProductTagAspectRatioString><![CDATA[0.0]]></firstProductTagAspectRatioString>\n\t\t\t<secondProductTagURL><![CDATA[]]></secondProductTagURL>\n\t\t\t<secondProductTagAspectRatioString><![CDATA[0.0]]></secondProductTagAspectRatioString>\n\t\t\t<firstGuaranteeWording><![CDATA[]]></firstGuaranteeWording>\n\t\t\t<secondGuaranteeWording><![CDATA[]]></secondGuaranteeWording>\n\t\t\t<thirdGuaranteeWording><![CDATA[]]></thirdGuaranteeWording>\n\t\t\t<isPriceBeginShow>false</isPriceBeginShow>\n\t\t\t<lastGMsgID><![CDATA[]]></lastGMsgID>\n\t\t\t<promoterKey><![CDATA[]]></promoterKey>\n\t\t\t<discountWording><![CDATA[]]></discountWording>\n\t\t\t<priceSuffixDescription><![CDATA[]]></priceSuffixDescription>\n\t\t\t<productCardKey><![CDATA[]]></productCardKey>\n\t\t\t<isWxShop><![CDATA[]]></isWxShop>\n\t\t\t<brandIconUrl><![CDATA[]]></brandIconUrl>\n\t\t\t<rIconUrl><![CDATA[]]></rIconUrl>\n\t\t\t<rIconUrlDarkMode><![CDATA[]]></rIconUrlDarkMode>\n\t\t\t<topShopIconUrl><![CDATA[]]></topShopIconUrl>\n\t\t\t<topShopIconUrlDarkMode><![CDATA[]]></topShopIconUrlDarkMode>\n\t\t\t<showBoxItemStringList />\n\t\t</finderLiveProductShare>\n\t\t<finderOrder>\n\t\t\t<appID><![CDATA[]]></appID>\n\t\t\t<orderID><![CDATA[]]></orderID>\n\t\t\t<path><![CDATA[]]></path>\n\t\t\t<priceWording><![CDATA[]]></priceWording>\n\t\t\t<stateWording><![CDATA[]]></stateWording>\n\t\t\t<productImageURL><![CDATA[]]></productImageURL>\n\t\t\t<products><![CDATA[]]></products>\n\t\t\t<productsCount><![CDATA[0]]></productsCount>\n\t\t\t<orderType><![CDATA[0]]></orderType>\n\t\t\t<newPriceWording><![CDATA[]]></newPriceWording>\n\t\t\t<newStateWording><![CDATA[]]></newStateWording>\n\t\t\t<useNewWording><![CDATA[0]]></useNewWording>\n\t\t</finderOrder>\n\t\t<finderShopWindowShare>\n\t\t\t<finderUsername><![CDATA[]]></finderUsername>\n\t\t\t<avatar><![CDATA[]]></avatar>\n\t\t\t<nickname><![CDATA[]]></nickname>\n\t\t\t<commodityInStockCount><![CDATA[]]></commodityInStockCount>\n\t\t\t<appId><![CDATA[]]></appId>\n\t\t\t<path><![CDATA[]]></path>\n\t\t\t<appUsername><![CDATA[]]></appUsername>\n\t\t\t<query><![CDATA[]]></query>\n\t\t\t<liteAppId><![CDATA[]]></liteAppId>\n\t\t\t<liteAppPath><![CDATA[]]></liteAppPath>\n\t\t\t<liteAppQuery><![CDATA[]]></liteAppQuery>\n\t\t\t<platformTagURL><![CDATA[]]></platformTagURL>\n\t\t\t<saleWording><![CDATA[]]></saleWording>\n\t\t\t<lastGMsgID><![CDATA[]]></lastGMsgID>\n\t\t\t<profileTypeWording><![CDATA[]]></profileTypeWording>\n\t\t\t<saleWordingExtra><![CDATA[]]></saleWordingExtra>\n\t\t\t<isWxShop><![CDATA[]]></isWxShop>\n\t\t\t<platformIconUrl><![CDATA[]]></platformIconUrl>\n\t\t\t<brandIconUrl><![CDATA[]]></brandIconUrl>\n\t\t\t<description><![CDATA[]]></description>\n\t\t\t<backgroundUrl><![CDATA[]]></backgroundUrl>\n\t\t\t<darkModePlatformIconUrl><![CDATA[]]></darkModePlatformIconUrl>\n\t\t\t<rIconUrl><![CDATA[]]></rIconUrl>\n\t\t\t<rIconUrlDarkMode><![CDATA[]]></rIconUrlDarkMode>\n\t\t\t<rWords><![CDATA[]]></rWords>\n\t\t\t<topShopIconUrl><![CDATA[]]></topShopIconUrl>\n\t\t\t<topShopIconUrlDarkMode><![CDATA[]]></topShopIconUrlDarkMode>\n\t\t\t<reputationInfo>\n\t\t\t\t<hasReputationInfo>0</hasReputationInfo>\n\t\t\t\t<reputationScore>0</reputationScore>\n\t\t\t\t<reputationWording />\n\t\t\t\t<reputationTextColor />\n\t\t\t\t<reputationLevelWording />\n\t\t\t\t<reputationBackgroundColor />\n\t\t\t</reputationInfo>\n\t\t\t<productImageURLList />\n\t\t</finderShopWindowShare>\n\t\t<findernamecard>\n\t\t\t<username />\n\t\t\t<avatar><![CDATA[]]></avatar>\n\t\t\t<nickname />\n\t\t\t<auth_job />\n\t\t\t<auth_icon>0</auth_icon>\n\t\t\t<auth_icon_url />\n\t\t\t<ecSource><![CDATA[]]></ecSource>\n\t\t\t<lastGMsgID><![CDATA[]]></lastGMsgID>\n\t\t</findernamecard>\n\t\t<finderGuarantee>\n\t\t\t<scene><![CDATA[0]]></scene>\n\t\t</finderGuarantee>\n\t\t<directshare>0</directshare>\n\t\t<gamecenter>\n\t\t\t<namecard>\n\t\t\t\t<iconUrl />\n\t\t\t\t<name />\n\t\t\t\t<desc />\n\t\t\t\t<tail />\n\t\t\t\t<jumpUrl />\n\t\t\t</namecard>\n\t\t</gamecenter>\n\t\t<patMsg>\n\t\t\t<chatUser />\n\t\t\t<records>\n\t\t\t\t<recordNum>0</recordNum>\n\t\t\t</records>\n\t\t</patMsg>\n\t\t<secretmsg>\n\t\t\t<issecretmsg>0</issecretmsg>\n\t\t</secretmsg>\n\t\t<referfromscene>0</referfromscene>\n\t\t<gameshare>\n\t\t\t<liteappext>\n\t\t\t\t<liteappbizdata />\n\t\t\t\t<priority>0</priority>\n\t\t\t</liteappext>\n\t\t\t<appbrandext>\n\t\t\t\t<litegameinfo />\n\t\t\t\t<priority>-1</priority>\n\t\t\t</appbrandext>\n\t\t\t<gameshareid />\n\t\t\t<sharedata />\n\t\t\t<isvideo>0</isvideo>\n\t\t\t<duration>-1</duration>\n\t\t\t<isexposed>0</isexposed>\n\t\t\t<readtext />\n\t\t</gameshare>\n\t\t<tingChatRoomItem>\n\t\t\t<type>0</type>\n\t\t\t<categoryItem>null</categoryItem>\n\t\t\t<categoryId />\n\t\t</tingChatRoomItem>\n\t\t<mpsharetrace>\n\t\t\t<hasfinderelement>0</hasfinderelement>\n\t\t\t<lastgmsgid />\n\t\t</mpsharetrace>\n\t\t<wxgamecard>\n\t\t\t<framesetname />\n\t\t\t<mbcarddata />\n\t\t\t<minpkgversion />\n\t\t\t<clientextinfo />\n\t\t\t<mbcardheight>0</mbcardheight>\n\t\t\t<isoldversion>0</isoldversion>\n\t\t</wxgamecard>\n\t\t<ecskfcard>\n\t\t\t<framesetname />\n\t\t\t<mbcarddata />\n\t\t\t<minupdateunixtimestamp>0</minupdateunixtimestamp>\n\t\t\t<needheader>false</needheader>\n\t\t\t<summary />\n\t\t</ecskfcard>\n\t\t<liteapp>\n\t\t\t<id>null</id>\n\t\t\t<path />\n\t\t\t<query />\n\t\t\t<istransparent>0</istransparent>\n\t\t\t<hideicon>0</hideicon>\n\t\t</liteapp>\n\t\t<opensdk_share_is_modified>0</opensdk_share_is_modified>\n\t</appmsg>\n\t<fromusername>wxid_ohq9p1qosjzq22</fromusername>\n\t<scene>0</scene>\n\t<appinfo>\n\t\t<version>1</version>\n\t\t<appname />\n\t</appinfo>\n\t<commenturl />\n</msg>\n'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1753250985, 'MsgSource': '<msgsource>\n\t<alnode>\n\t\t<fr>4</fr>\n\t</alnode>\n\t<eggIncluded>1</eggIncluded>\n\t<sec_msg_node>\n\t\t<uuid>13b54d76d4ee2f55e78996f8e4ea2426_</uuid>\n\t\t<risk-file-flag />\n\t\t<risk-file-md5-list />\n\t</sec_msg_node>\n\t<silence>1</silence>\n\t<membercount>146</membercount>\n\t<signature>N0_V1_qAOXOA8K|v1_vQFUxl9+</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'NewMsgId': 6767972415344120322, 'MsgSeq': 871394286}
2025-07-23 14:09:30 | DEBUG | 从群聊消息中提取发送者: wxid_ohq9p1qosjzq22
2025-07-23 14:09:30 | DEBUG | 使用已解析的XML处理引用消息
2025-07-23 14:09:30 | INFO | 收到引用消息: 消息ID:670522665 来自:27852221909@chatroom 发送人:wxid_ohq9p1qosjzq22 内容:我还在1 引用类型:1
2025-07-23 14:09:30 | INFO | [DouBaoImageToImage] 收到引用消息: 我还在1
2025-07-23 14:09:30 | INFO | [TimerTask] 收到引用消息调试信息:
2025-07-23 14:09:30 | INFO |   - 消息内容: 我还在1
2025-07-23 14:09:30 | INFO |   - 群组ID: 27852221909@chatroom
2025-07-23 14:09:30 | INFO |   - 发送人: wxid_ohq9p1qosjzq22
2025-07-23 14:09:30 | INFO |   - 引用信息: {'MsgType': 1, 'Content': '\n群里肯定有人刷了', 'Msgid': '4114416080425043225', 'NewMsgId': '4114416080425043225', 'ToWxid': 'wxid_4usgcju5ey9q29', 'FromWxid': '27852221909@chatroom', 'Nickname': '栀栀', 'MsgSource': '<msgsource>\n\t<bizflag>0</bizflag>\n\t<pua>1</pua>\n\t<eggIncluded>1</eggIncluded>\n\t<silence>1</silence>\n\t<membercount>146</membercount>\n\t<signature>N0_V1_UMdlVtkp|v1_F0ssEzWu</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'Createtime': '1753250961', 'SenderWxid': 'wxid_ohq9p1qosjzq22'}
2025-07-23 14:09:30 | INFO |   - 引用消息ID: 
2025-07-23 14:09:30 | INFO |   - 引用消息类型: 
2025-07-23 14:09:30 | INFO |   - 引用消息内容: 
群里肯定有人刷了
2025-07-23 14:09:30 | INFO |   - 引用消息发送人: wxid_ohq9p1qosjzq22
2025-07-23 14:09:31 | DEBUG | 收到消息: {'MsgId': 1135351534, 'FromUserName': {'string': '27852221909@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 1, 'Content': {'string': 'wxid_ohq9p1qosjzq22:\n[捂脸]'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1753250987, 'MsgSource': '<msgsource>\n\t<pua>1</pua>\n\t<eggIncluded>1</eggIncluded>\n\t<silence>1</silence>\n\t<membercount>146</membercount>\n\t<signature>N0_V1_wuKJdneN|v1_Oal+mJJb</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'NewMsgId': 3463653582337616795, 'MsgSeq': 871394287}
2025-07-23 14:09:31 | INFO | 收到表情消息: 消息ID:1135351534 来自:27852221909@chatroom 发送人:wxid_ohq9p1qosjzq22 @:[] 内容:[捂脸]
2025-07-23 14:09:31 | DEBUG | [DoubaoImageRecognition] 缓存表情消息: 3463653582337616795
2025-07-23 14:09:31 | DEBUG | 收到消息: {'MsgId': 127876262, 'FromUserName': {'string': '48097389945@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 10002, 'Content': {'string': '48097389945@chatroom:\n<sysmsg type="pat">\n<pat>\n  <fromusername>wxid_ubbh6q832tcs21</fromusername>\n  <chatusername>48097389945@chatroom</chatusername>\n  <pattedusername>wxid_wlnzvr8ivgd422</pattedusername>\n  <patsuffix><![CDATA[]]></patsuffix>\n  <patsuffixversion>2</patsuffixversion>\n\n\n\n\n  <template><![CDATA["${wxid_ubbh6q832tcs21}" 拍了拍 "${wxid_wlnzvr8ivgd422}"]]></template>\n\n\n\n\n</pat>\n</sysmsg>'}, 'Status': 4, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1753250987, 'MsgSource': '<msgsource>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'NewMsgId': 3364675653960056542, 'MsgSeq': 871394288}
2025-07-23 14:09:31 | DEBUG | 系统消息类型: pat
2025-07-23 14:09:31 | INFO | 收到拍一拍消息: 消息ID:127876262 来自:48097389945@chatroom 发送人:48097389945@chatroom 拍者:wxid_ubbh6q832tcs21 被拍:wxid_wlnzvr8ivgd422 后缀:None
2025-07-23 14:09:32 | DEBUG | [PatReply] 被拍者 wxid_wlnzvr8ivgd422 不是目标用户 wxid_4usgcju5ey9q29，跳过
2025-07-23 14:09:47 | DEBUG | 收到消息: {'MsgId': 1331280202, 'FromUserName': {'string': '27852221909@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 1, 'Content': {'string': 'wxid_x4s6k999g6qg22:\n加油'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1753251003, 'MsgSource': '<msgsource>\n\t<bizflag>0</bizflag>\n\t<pua>1</pua>\n\t<eggIncluded>1</eggIncluded>\n\t<silence>1</silence>\n\t<membercount>146</membercount>\n\t<signature>N0_V1_YYjycz7Y|v1_+k5YnsTQ</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'NewMsgId': 5731099254242615716, 'MsgSeq': 871394289}
2025-07-23 14:09:47 | INFO | 收到文本消息: 消息ID:1331280202 来自:27852221909@chatroom 发送人:wxid_x4s6k999g6qg22 @:[] 内容:加油
2025-07-23 14:09:47 | DEBUG | 处理消息内容: '加油'
2025-07-23 14:09:47 | DEBUG | 消息内容 '加油' 不匹配任何命令，忽略
2025-07-23 14:09:58 | DEBUG | 收到消息: {'MsgId': 160086231, 'FromUserName': {'string': '27852221909@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 1, 'Content': {'string': 'wxid_xv01lkcmn48l22:\n挂机能过吗'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1753251014, 'MsgSource': '<msgsource>\n\t<bizflag>0</bizflag>\n\t<pua>1</pua>\n\t<eggIncluded>1</eggIncluded>\n\t<silence>1</silence>\n\t<membercount>146</membercount>\n\t<signature>N0_V1_Z5E0zWxg|v1_0erW8aYJ</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'NewMsgId': 6144208254558183774, 'MsgSeq': 871394290}
2025-07-23 14:09:58 | INFO | 收到文本消息: 消息ID:160086231 来自:27852221909@chatroom 发送人:wxid_xv01lkcmn48l22 @:[] 内容:挂机能过吗
2025-07-23 14:09:58 | DEBUG | 处理消息内容: '挂机能过吗'
2025-07-23 14:09:58 | DEBUG | 消息内容 '挂机能过吗' 不匹配任何命令，忽略
