from os.path import join
from io import BufferedReader, BytesIO

from numpy.compat import isfileobj
from numpy.testing import assert_
from numpy.testing import tempdir


def test_isfileobj():
    with tempdir(prefix="numpy_test_compat_") as folder:
        filename = join(folder, 'a.bin')

        with open(filename, 'wb') as f:
            assert_(isfileobj(f))

        with open(filename, 'ab') as f:
            assert_(isfileobj(f))

        with open(filename, 'rb') as f:
            assert_(isfileobj(f))

        assert_(isfileobj(BufferedReader(BytesIO())) is False)
