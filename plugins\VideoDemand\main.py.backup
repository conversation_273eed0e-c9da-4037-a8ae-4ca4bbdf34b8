import json
import re
import tomllib
import traceback
import asyncio
import os
import time
import base64
from pathlib import Path
import httpx
from loguru import logger
from typing import Optional, Union
import cv2
import numpy as np
from io import BytesIO
from PIL import Image
import shutil
import random

from WechatAPI import WechatAPIClient
from utils.decorators import *
from utils.plugin_base import PluginBase

class VideoDemand(PluginBase):
    """视频点播插件"""
    
    description = "视频点播插件 - 支持多类型视频点播"
    author = "XYBot"
    version = "1.0.0"

    def __init__(self):
        super().__init__()
        
        # 初始化临时目录 - 使用绝对路径
        self.plugin_dir = Path(os.path.dirname(os.path.abspath(__file__)))
        self.temp_dir = self.plugin_dir / "temp"
        self._ensure_temp_dir()
        
        # 读取配置
        try:
            config_path = os.path.join(os.path.dirname(__file__), "config.toml")
            with open(config_path, "rb") as f:
                plugin_config = tomllib.load(f)
            config = plugin_config["VideoDemand"]
        except Exception as e:
            logger.error(f"[VideoDemand] 配置文件读取失败: {e}")
            raise
        
        # 基本配置
        self.enable = config.get("enable", True)
        self.command = config.get("command", ["视频菜单"])
        self.random_command = config.get("random-command", ["随机视频"])  # 添加随机视频命令
        self.random_video_url = config.get("random-video-url", "http://tucdn.wpon.cn/api-girl/index.php?wpon=json")  # 随机视频URL
        self.menu_image = config.get("menu-image", "https://d.kstore.dev/download/8150/shipin.jpg")
        self.cache_time = config.get("cache-time", 300)  # 菜单有效期5分钟
        
        # 房间状态
        self.room_status = {}
        
        # 并发控制
        self.video_semaphore = asyncio.Semaphore(3)  # 最多同时处理3个视频请求
        
        # 磁盘空间检查阈值（字节）
        self.min_disk_space = 1024 * 1024 * 1024  # 1GB
        
        # API配置
        self.api_mapping = {
            1: {'name': '热舞视频', 'urls': [
                'http://api.yujn.cn/api/rewu.php?type=video',
                'https://api.317ak.com/API/sp/rwxl.php'
            ]},
            2: {'name': '吧啦鲨系', 'urls': [
                'https://api.bi71t5.cn/api/wbsphj.php?xuanze=吧啦鲨系',
                'https://api.317ak.com/API/sp/blsx.php'
            ]},
            3: {'name': '丁璐系列', 'urls': [
                'https://api.bi71t5.cn/api/wbsphj.php?xuanze=丁璐系列',
                'https://api.317ak.com/API/sp/dlxl.php'
            ]},
            4: {'name': '不怪系列', 'urls': [
                'https://api.bi71t5.cn/api/wbsphj.php?xuanze=不怪系列',
                'https://api.317ak.com/API/sp/bgxl.php'
            ]},
            5: {'name': '不是小媛', 'urls': [
                'https://api.bi71t5.cn/api/wbsphj.php?xuanze=不是小媛',
                'https://api.317ak.com/API/sp/bsxy.php'
            ]},
            6: {'name': '不见花海', 'urls': [
                'https://api.bi71t5.cn/api/wbsphj.php?xuanze=不见花海',
                'https://api.317ak.com/API/sp/bjhh.php'
            ]},
            7: {'name': '二爷系列', 'urls': [
                'https://api.bi71t5.cn/api/wbsphj.php?xuanze=二爷系列',
                'https://api.317ak.com/API/sp/eyxl.php'
            ]},
            8: {'name': '二酱系列', 'urls': [
                'https://api.bi71t5.cn/api/wbsphj.php?xuanze=二酱系列',
                'https://api.317ak.com/API/sp/ejxl.php'
            ]},
            9: {'name': '二麻翻唱', 'urls': [
                'https://api.bi71t5.cn/api/wbsphj.php?xuanze=二麻翻唱',
                'https://api.317ak.com/API/sp/emfc.php'
            ]},
            10: {'name': '健身系列', 'urls': [
                'https://api.bi71t5.cn/api/wbsphj.php?xuanze=健身系列',
                'https://api.317ak.com/API/sp/jsxl.php'
            ]},
            11: {'name': '傲娇媛系', 'urls': [
                'https://api.bi71t5.cn/api/wbsphj.php?xuanze=傲娇媛系'
            ]},
            12: {'name': '凌凌七系', 'urls': [
                'https://api.bi71t5.cn/api/wbsphj.php?xuanze=凌凌七系'
            ]},
            13: {'name': '半斤系列', 'urls': [
                'https://api.bi71t5.cn/api/wbsphj.php?xuanze=半斤系列'
            ]},
            14: {'name': '半糖糖系', 'urls': [
                'https://api.bi71t5.cn/api/wbsphj.php?xuanze=半糖糖系'
            ]},
            15: {'name': '卿卿公主', 'urls': [
                'https://api.bi71t5.cn/api/wbsphj.php?xuanze=卿卿公主'
            ]},
            16: {'name': '呆萝系列', 'urls': [
                'https://api.bi71t5.cn/api/wbsphj.php?xuanze=呆萝系列'
            ]},
            17: {'name': '妲己系列', 'urls': [
                'https://api.bi71t5.cn/api/wbsphj.php?xuanze=妲己系列'
            ]},
            18: {'name': '安佳系列', 'urls': [
                'https://api.bi71t5.cn/api/wbsphj.php?xuanze=安佳系列'
            ]},
            19: {'name': '安然系列', 'urls': [
                'https://api.bi71t5.cn/api/wbsphj.php?xuanze=安然系列'
            ]},
            20: {'name': '宋熙雅系', 'urls': [
                'https://api.bi71t5.cn/api/wbsphj.php?xuanze=宋熙雅系'
            ]},
            21: {'name': '富儿系列', 'urls': [
                'https://api.bi71t5.cn/api/wbsphj.php?xuanze=富儿系列'
            ]},
            22: {'name': '小苏伊伊', 'urls': [
                'https://api.bi71t5.cn/api/wbsphj.php?xuanze=小苏伊伊'
            ]},
            23: {'name': '小落英系', 'urls': [
                'https://api.bi71t5.cn/api/wbsphj.php?xuanze=小落英系'
            ]},
            24: {'name': '巴啦魔仙', 'urls': [
                'https://api.bi71t5.cn/api/wbsphj.php?xuanze=巴啦魔仙'
            ]},
            25: {'name': '暴力美系', 'urls': [
                'https://api.bi71t5.cn/api/wbsphj.php?xuanze=暴力美系'
            ]},
            26: {'name': '梦瑶系列', 'urls': [
                'https://api.bi71t5.cn/api/wbsphj.php?xuanze=梦瑶系列'
            ]},
            27: {'name': '江小系列', 'urls': [
                'https://api.bi71t5.cn/api/wbsphj.php?xuanze=江小系列'
            ]},
            28: {'name': '江青系列', 'urls': [
                'https://api.bi71t5.cn/api/wbsphj.php?xuanze=江青系列'
            ]},
            29: {'name': '海绵翻唱', 'urls': [
                'https://api.bi71t5.cn/api/wbsphj.php?xuanze=海绵翻唱'
            ]},
            30: {'name': '涵涵系列', 'urls': [
                'https://api.bi71t5.cn/api/wbsphj.php?xuanze=涵涵系列'
            ]},
            31: {'name': '温柔以待', 'urls': [
                'https://api.bi71t5.cn/api/wbsphj.php?xuanze=温柔以待'
            ]},
            32: {'name': '爆笑阿衰', 'urls': [
                'https://api.bi71t5.cn/api/wbsphj.php?xuanze=爆笑阿衰'
            ]},
            33: {'name': '爱希系列', 'urls': [
                'https://api.bi71t5.cn/api/wbsphj.php?xuanze=爱希系列'
            ]},
            34: {'name': '白月光系', 'urls': [
                'https://api.bi71t5.cn/api/wbsphj.php?xuanze=白月光系'
            ]},
            35: {'name': '白璃系列', 'urls': [
                'https://api.bi71t5.cn/api/wbsphj.php?xuanze=白璃系列'
            ]},
            36: {'name': '白露系列', 'urls': [
                'https://api.bi71t5.cn/api/wbsphj.php?xuanze=白露系列'
            ]},
            37: {'name': '百变小晨', 'urls': [
                'https://api.bi71t5.cn/api/wbsphj.php?xuanze=百变小晨'
            ]},
            38: {'name': '等等系列', 'urls': [
                'https://api.bi71t5.cn/api/wbsphj.php?xuanze=等等系列'
            ]},
            39: {'name': '糕冷小王', 'urls': [
                'https://api.bi71t5.cn/api/wbsphj.php?xuanze=糕冷小王'
            ]},
            40: {'name': '红姐系列', 'urls': [
                'https://api.bi71t5.cn/api/wbsphj.php?xuanze=红姐系列'
            ]},
            41: {'name': '绷带很烦', 'urls': [
                'https://api.bi71t5.cn/api/wbsphj.php?xuanze=绷带很烦'
            ]},
            42: {'name': '美杜莎系', 'urls': [
                'https://api.bi71t5.cn/api/wbsphj.php?xuanze=美杜莎系'
            ]},
            43: {'name': '翠翠系列', 'urls': [
                'https://api.bi71t5.cn/api/wbsphj.php?xuanze=翠翠系列'
            ]},
            44: {'name': '背影变装', 'urls': [
                'https://api.bi71t5.cn/api/wbsphj.php?xuanze=背影变装'
            ]},
            45: {'name': '腹肌变装', 'urls': [
                'https://api.bi71t5.cn/api/wbsphj.php?xuanze=腹肌变装'
            ]},
            46: {'name': '花花姑娘', 'urls': [
                'https://api.bi71t5.cn/api/wbsphj.php?xuanze=花花姑娘'
            ]},
            47: {'name': '茶茶欧尼', 'urls': [
                'https://api.bi71t5.cn/api/wbsphj.php?xuanze=茶茶欧尼'
            ]},
            48: {'name': '菜小怡系', 'urls': [
                'https://api.bi71t5.cn/api/wbsphj.php?xuanze=菜小怡系'
            ]},
            49: {'name': '过肩出场', 'urls': [
                'https://api.bi71t5.cn/api/wbsphj.php?xuanze=过肩出场'
            ]},
            50: {'name': '陈和系列', 'urls': [
                'https://api.bi71t5.cn/api/wbsphj.php?xuanze=陈和系列'
            ]},
            51: {'name': '蛋儿系列', 'urls': [
                'https://api.bi71t5.cn/api/wbsphj.php?xuanze=蛋儿系列',
                'https://api.317ak.com/API/sp/dexl.php'
            ]},
            52: {'name': '美女视频', 'urls': [
                'https://api.71xk.com/api/video/v1',
                'http://www.yujn.cn/api/xjj.php',
                'http://www.yujn.cn/api/zzxjj.php',
                'http://api.yujn.cn/api/juhexjj.php?type=video',
                'https://api.cenguigui.cn/api/mp4/MP4_xiaojiejie.php',
                'https://api.kuleu.com/api/MP4_xiaojiejie?type=video',
                'https://api.pearktrue.cn/api/random/xjj/?type=video',
                'https://www.wudada.online/Api/NewSp',
                'https://v2.api-m.com/api/meinv?return=302'
            ]},
            53: {'name': '安琪系列', 'urls': [
                'https://api.317ak.com/API/sp/aqxl.php'
            ]},
            54: {'name': '双倍快乐', 'urls': [
                'http://api.yujn.cn/api/sbkl.php?type=video'
            ]},
            55: {'name': '变装系列', 'urls': [
                'https://api.317ak.com/API/sp/gjbz.php',
                'https://api.bi71t5.cn/api/wbsphj.php?xuanze=光剑变装',
                'https://api.317ak.com/API/sp/dmbz.php'
            ]},
            56: {'name': '女神系列', 'urls': [
                'https://api.suyanw.cn/api/kysp.php?lx=女神'
            ]},
            57: {'name': '动漫系列', 'urls': [
                'https://api.suyanw.cn/api/kysp.php?lx=动漫'
            ]},
            58: {'name': '动物系列', 'urls': [
                'https://api.suyanw.cn/api/kysp.php?lx=动物'
            ]},
            59: {'name': '风景系列', 'urls': [
                'https://api.suyanw.cn/api/kysp.php?lx=风景'
            ]},
            60: {'name': '情侣系列', 'urls': [
                'https://api.suyanw.cn/api/kysp.php?lx=情侣'
            ]},
            61: {'name': '姓氏特效', 'urls': [
                'https://api.suyanw.cn/api/kysp.php?lx=姓氏特效'
            ]},
            62: {'name': '酷炫特效', 'urls': [
                'https://api.suyanw.cn/api/kysp.php?lx=酷炫特效'
            ]},
            63: {'name': '动态壁纸', 'urls': [
                'https://api.suyanw.cn/api/kysp.php?lx=动态壁纸'
            ]},
            64: {'name': '热歌系列', 'urls': [
                'https://api.suyanw.cn/api/kysp.php?lx=热歌'
            ]},
            65: {'name': '男神系列', 'urls': [
                'https://api.suyanw.cn/api/kysp.php?lx=男神'
            ]},
            66: {'name': '明星系列', 'urls': [
                'https://api.suyanw.cn/api/kysp.php?lx=明星'
            ]},
            67: {'name': '节日系列', 'urls': [
                'https://api.suyanw.cn/api/kysp.php?lx=节日'
            ]},
            68: {'name': '充电系列', 'urls': [
                'https://api.suyanw.cn/api/kysp.php?lx=充电'
            ]},
            69: {'name': '闹钟系列', 'urls': [
                'https://api.suyanw.cn/api/kysp.php?lx=闹钟'
            ]},
            70: {'name': '萌娃系列', 'urls': [
                'https://api.317ak.com/API/sp/mwxl.php'
            ]},
            71: {'name': '桥本环菜', 'urls': [
                'https://api.317ak.com/API/sp/qbhc.php'
            ]},
            72: {'name': '燕酱系列', 'urls': [
                'https://api.317ak.com/API/sp/yjxl.php'
            ]},
            73: {'name': '自拍视频', 'urls': [
                'https://api.317ak.com/API/sp/zpsp.php'
            ]},
            74: {'name': '双马尾系', 'urls': [
                'https://api.317ak.com/API/sp/smwx.php'
            ]},
            75: {'name': '渔网系列', 'urls': [
                'https://api.317ak.com/API/sp/ywxl.php'
            ]},
            76: {'name': '鞠婧祎系', 'urls': [
                'https://api.317ak.com/API/sp/jjyx.php'
            ]},
            77: {'name': '漫展系列', 'urls': [
                'https://www.yujn.cn/api/manzhan.php'
            ]},
            78: {'name': '周扬青系', 'urls': [
                'https://api.317ak.com/API/sp/zyqx.php'
            ]},
            79: {'name': '周清欢系', 'urls': [
                'https://api.317ak.com/API/sp/xqx.php'
            ]},
            80: {'name': '极品狱卒', 'urls': [
                'https://api.317ak.com/API/sp/jpyz.php'
            ]},
            81: {'name': '纯情女高', 'urls': [
                'https://api.317ak.com/API/sp/cqng.php',
                'https://api.317ak.com/API/sp/ndxl.php'
            ]},
            82: {'name': '漫画芋系', 'urls': [
                'https://api.317ak.com/API/sp/mhyx.php'
            ]},
            83: {'name': '感觉至上', 'urls': [
                'https://api.dragonlongzhu.cn/api/MP4_xiaojiejie.php'
            ]},
            84: {'name': '开心锤锤', 'urls': [
                'http://abc.gykj.asia/API/kxcc.php'
            ]},
            85: {'name': '动漫卡点', 'urls': [
                'https://api.bi71t5.cn/api/wbsphj.php?xuanze=动漫系列'
            ]},
            86: {'name': '少萝系列', 'urls': [
                'https://api.317ak.com/API/sp/slmm.php',
                'https://api.317ak.com/API/sp/slxl.php'
            ]},
            87: {'name': '甩裙系列', 'urls': [
                'https://api.317ak.com/API/sp/sqxl.php'
            ]},
            88: {'name': '黑白双煞', 'urls': [
                'https://api.bi71t5.cn/api/wbsphj.php?xuanze=黑白双丝',
                'https://api.bi71t5.cn/api/wbsphj.php?xuanze=黑白双煞',
                'https://api.317ak.com/API/sp/hbss.php'
            ]},
            89: {'name': '吊带系列', 'urls': [
                'https://api.317ak.com/API/sp/ddxl.php',
                'https://api.bi71t5.cn/api/wbsphj.php?xuanze=吊带系列',
                'https://api.317ak.com/API/sp/ddsp.php'
            ]},
            90: {'name': '萝莉系列', 'urls': [
                'https://api.317ak.com/API/sp/llxl.php'
            ]},
            91: {'name': '甜妹系列', 'urls': [
                'https://api.317ak.com/API/sp/tmxl.php'
            ]},
            92: {'name': '白丝系列', 'urls': [
                'https://api.bi71t5.cn/api/wbsphj.php?xuanze=白丝系列',
                'https://api.317ak.com/API/sp/bssp.php'
            ]},
            93: {'name': '黑丝系列', 'urls': [
                'https://api.317ak.com/API/sp/hssp.php',
                'http://www.yujn.cn/api/heisis.php'
            ]},
            94: {'name': '小瑾系列', 'urls': [
                'https://api.317ak.com/API/sp/xjxl.php'
            ]},
            95: {'name': '穿搭系列', 'urls': [
                'http://api.yujn.cn/api/chuanda.php?type=video',
                'https://api.317ak.com/API/sp/cdxl.php',
                'https://api.317ak.com/API/sp/mncd.php'
            ]},
            96: {'name': '惠子系列', 'urls': [
                'https://api.bi71t5.cn/api/wbsphj.php?xuanze=惠子系列',
                'https://api.317ak.com/API/sp/hzxl.php'
            ]},
            97: {'name': '御姐系列', 'urls': [
                'https://api.317ak.com/API/sp/yjxl.php'
            ]},
            98: {'name': '女仆系列', 'urls': [
                'https://api.317ak.com/API/sp/npxl.php'
            ]},
            99: {'name': '微胖系列', 'urls': [
                'https://api.317ak.com/API/sp/wpxl.php'
            ]},
            100: {'name': '硬气卡点', 'urls': [
                'https://api.317ak.com/API/sp/yqkd.php'
            ]},
            101: {'name': '火车摇系', 'urls': [
                'https://api.317ak.com/API/sp/hcyx.php'
            ]},
            102: {'name': '安慕希系', 'urls': [
                'https://api.317ak.com/API/sp/amxx.php',
                'https://api.bi71t5.cn/api/wbsphj.php?xuanze=吊带系列'
            ]},
            103: {'name': '擦玻璃系', 'urls': [
                'https://api.bi71t5.cn/api/wbsphj.php?xuanze=擦玻璃系',
                'https://api.317ak.com/API/sp/cblx.php'
            ]},
            104: {'name': '蹲下变装', 'urls': [
                'https://api.bi71t5.cn/api/wbsphj.php?xuanze=蹲下变装',
                'https://api.317ak.com/API/sp/dxbz.php'
            ]},
            105: {'name': '背影变装', 'urls': [
                'https://api.317ak.com/API/sp/bybz.php'
            ]},
            106: {'name': '猫系女友', 'urls': [
                'https://api.317ak.com/API/sp/mxny.php'
            ]},
            107: {'name': '丝滑舞蹈', 'urls': [
                'http://api.yujn.cn/api/shwd.php?type=video'
            ]},
            108: {'name': '又纯又欲', 'urls': [
                'https://api.317ak.com/API/sp/ycyy.php'
            ]},
            109: {'name': '腹肌变装', 'urls': [
                'https://api.317ak.com/API/sp/fjbz.php'
            ]},
            110: {'name': '完美身材', 'urls': [
                'http://api.yujn.cn/api/wmsc.php?type=video',
                'https://api.317ak.com/API/sp/wmsc.php'
            ]},
            111: {'name': '蛇姐系列', 'urls': [
                'http://api.yujn.cn/api/shejie.php?type=video'
            ]},
            112: {'name': '章若楠系', 'urls': [
                'http://api.yujn.cn/api/zrn.php?type=video'
            ]},
            113: {'name': '汉服系列', 'urls': [
                'http://api.yujn.cn/api/hanfu.php?type=video'
            ]},
            114: {'name': '杂鱼川系', 'urls': [
                'https://api.317ak.com/API/sp/zycx.php'
            ]},
            115: {'name': '慢摇系列', 'urls': [
                'http://api.yujn.cn/api/manyao.php?type=video',
                'https://api.317ak.com/API/sp/myxl.php'
            ]},
            116: {'name': '清纯系列', 'urls': [
                'http://api.yujn.cn/api/qingchun.php?type=video',
                'https://api.317ak.com/API/sp/qcxl.php'
            ]},
            117: {'name': 'COS系列', 'urls': [
                'http://api.yujn.cn/api/COS.php?type=video',
                'https://api.317ak.com/API/sp/cosxl.php'
            ]},
            118: {'name': '街拍系列', 'urls': [
                'http://api.yujn.cn/api/jiepai.php?type=video'
            ]},
            119: {'name': '余震系列', 'urls': [
                'https://api.317ak.com/API/sp/yzxl.php'
            ]},
            120: {'name': '你的欲梦', 'urls': [
                'http://api.yujn.cn/api/ndym.php?type=video',
                'https://api.317ak.com/API/sp/ndym.php'
            ]},
            121: {'name': '洛丽塔系', 'urls': [
                'http://api.yujn.cn/api/jksp.php?type=video'
            ]},
            122: {'name': '玉足美腿', 'urls': [
                'http://api.yujn.cn/api/yuzu.php?type=video',
                'https://api.317ak.com/API/sp/yzmt.php'
            ]},
            123: {'name': '清风皓月', 'urls': [
                'https://api.317ak.com/API/sp/qfhy.php'
            ]},
            124: {'name': '帅哥系列', 'urls': [
                'http://api.yujn.cn/api/xgg.php?type=video',
                'https://api.317ak.com/API/sp/sgxl.php'
            ]},
            125: {'name': '潇潇系列', 'urls': [
                'http://api.yujn.cn/api/xiaoxiao.php?',
                'https://api.317ak.com/API/sp/xxxl.php'
            ]},
            126: {'name': '倾梦推荐', 'urls': [
                'https://api.317ak.com/API/sp/qmtj.php'
            ]},
            127: {'name': '晴天推荐', 'urls': [
                'https://api.317ak.com/API/sp/qttj.php'
            ]},
            128: {'name': '琳铛系列', 'urls': [
                'https://api.317ak.com/API/sp/ldxl.php'
            ]}
        }
        
        logger.debug(f"[VideoDemand] 初始化配置完成")
        
        # 启动清理任务
        asyncio.create_task(self._schedule_cleanup())

    def _ensure_temp_dir(self, log=True):
        """确保临时目录存在"""
        try:
            self.temp_dir.mkdir(parents=True, exist_ok=True)
        except Exception as e:
            logger.error(f"[VideoDemand] 创建临时目录失败: {e}")
            # 如果无法创建指定目录，尝试使用系统临时目录
            import tempfile
            self.temp_dir = Path(tempfile.gettempdir()) / "VideoDemand"
            self.temp_dir.mkdir(parents=True, exist_ok=True)
            logger.warning(f"[VideoDemand] 使用备用临时目录: {self.temp_dir}")

    async def _get_redirect_url(self, url: str) -> Optional[str]:
        """获取重定向URL"""
        headers = {
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
            'Accept': '*/*',
            'Accept-Encoding': 'gzip, deflate',
            'Connection': 'keep-alive'
        }
        
        try:
            async with httpx.AsyncClient(timeout=30.0, verify=False, follow_redirects=False) as client:
                response = await client.get(url, headers=headers)
                if response.status_code in (301, 302):
                    return response.headers.get('Location')
        except Exception:
            return None
            
        return None
        
    async def _get_random_video_url(self) -> str:
        """获取随机视频API的URL

        Returns:
            str: 随机视频API的URL
        """
        # 直接返回API URL，不需要添加随机参数
        return self.random_video_url
        
    async def _get_random_video(self) -> dict:
        """获取随机视频

        Returns:
            dict: 包含视频信息的字典，成功时包含URL，失败时包含错误信息
        """
        max_retries = 3
        retry_count = 0

        headers = {
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
            'Accept': 'application/json, text/plain, */*',
            'Accept-Encoding': 'gzip, deflate',
            'Accept-Language': 'zh-CN,zh;q=0.9,en-US;q=0.8,en;q=0.7',
            'Connection': 'keep-alive'
        }

        while retry_count < max_retries:
            try:
                # 获取API URL
                api_url = await self._get_random_video_url()

                # 发送请求获取JSON响应
                async with httpx.AsyncClient(timeout=30.0, verify=False) as client:
                    response = await client.get(api_url, headers=headers)
                    response.raise_for_status()

                    # 解析JSON响应
                    try:
                        json_data = response.json()
                        logger.debug(f"随机视频API响应: {json_data}")

                        # 检查响应格式
                        if json_data.get("error") == 0 and json_data.get("result") == 200:
                            mp4_path = json_data.get("mp4", "")
                            if mp4_path:
                                # 构建完整的视频URL
                                if mp4_path.startswith("//"):
                                    video_url = f"https:{mp4_path}"
                                elif mp4_path.startswith("/"):
                                    video_url = f"https://tucdn.wpon.cn{mp4_path}"
                                else:
                                    video_url = mp4_path

                                logger.debug(f"获取到随机视频URL: {video_url}")
                                return {"success": True, "url": video_url}
                            else:
                                logger.warning("API响应中没有找到mp4字段")
                                retry_count += 1
                        else:
                            logger.warning(f"API返回错误: error={json_data.get('error')}, result={json_data.get('result')}")
                            retry_count += 1

                    except json.JSONDecodeError as e:
                        logger.error(f"解析JSON响应失败: {e}")
                        retry_count += 1

            except (httpx.HTTPError, asyncio.TimeoutError) as e:
                logger.warning(f"请求随机视频API失败 - 重试次数: {retry_count+1}/{max_retries}: {e}")
                retry_count += 1
                await asyncio.sleep(1)

            except Exception as e:
                logger.error(f"获取随机视频时发生未知异常 - 重试次数: {retry_count+1}/{max_retries}", exception=e)
                retry_count += 1
                await asyncio.sleep(1)

        return {"success": False, "message": "获取随机视频失败，请稍后重试"}

    async def _get_video(self, category_name: str, retry=6) -> dict:
        """获取视频URL"""
        category_id = None
        for key, value in self.api_mapping.items():
            if value['name'] == category_name:
                category_id = key
                break
                
        if category_id is None:
            return {"success": False, "message": f"未找到匹配的视频类别: {category_name}"}
            
        urls = self.api_mapping[category_id]['urls'].copy()
        max_error_count = 3
        
        headers = {
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
            'Accept': '*/*',
            'Accept-Encoding': 'gzip, deflate',
            'Connection': 'keep-alive'
        }
        
        while urls:
            url = urls[0]
            error_count = 0
            logger.debug(f"尝试获取视频 - 类别: {category_name}, URL: {url}")
            
            while error_count < max_error_count:
                try:
                    async with httpx.AsyncClient(timeout=30.0, verify=False, follow_redirects=True) as client:
                        response = await client.get(url, headers=headers)
                        response.raise_for_status()
                        final_url = str(response.url)
                        return {"success": True, "url": final_url}
                            
                except (httpx.HTTPError, asyncio.TimeoutError) as e:
                    error_count += 1
                    if error_count >= max_error_count:
                        urls.pop(0)
                        break
                    await asyncio.sleep(2)
                    
                except Exception as e:
                    error_count += 1
                    if error_count >= max_error_count:
                        urls.pop(0)
                        break
                    await asyncio.sleep(2)
                    
        return {"success": False, "message": f"获取视频失败: 所有URL均无法获取视频，类别: {category_name}"}

    async def _download_video(self, url: str, category: str) -> Optional[str]:
        """下载视频到本地
        Returns:
            str: 本地视频文件路径,下载失败返回None
        """
        try:
            # 生成唯一文件名
            timestamp = int(time.time())
            filename = f"{category}_{timestamp}.mp4"
            filepath = self.temp_dir / filename
            
            headers = {
                'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
                'Accept': '*/*',
                'Accept-Encoding': 'gzip, deflate',
                'Connection': 'keep-alive'
            }
            
            # 下载视频
            start_time = time.time()
            
            async with httpx.AsyncClient(timeout=300.0, verify=False) as client:  # 5分钟超时
                response = await client.get(url, headers=headers)
                
                if response.status_code != 200:
                    return None
                    
                content_length = len(response.content)
                duration = time.time() - start_time
                download_speed = content_length / duration / 1024 if duration > 0 else 0  # KB/s
                
                # 写入文件
                with open(filepath, 'wb') as f:
                    f.write(response.content)
                
            # 尝试修复视频元数据，确保时长信息正确
            try:
                await self._fix_video_metadata(filepath)
            except Exception as e:
                logger.warning(f"修复视频元数据失败，但继续使用原视频", exception=e)
                    
            return str(filepath)
            
        except Exception as e:
            logger.error(f"下载视频失败: {url}, 类别: {category}", exception=e)
            return None

    async def _fix_video_metadata(self, video_path: str) -> None:
        """修复视频元数据，确保时长信息正确
        
        Args:
            video_path: 视频文件路径
        """
        try:
            # 创建临时文件路径
            temp_path = f"{video_path}.temp.mp4"
            
            # 检查文件是否存在
            if not os.path.exists(video_path) or os.path.getsize(video_path) == 0:
                logger.warning(f"要修复的视频文件不存在或为空: {video_path}")
                return
                
            # 验证原始文件是否可以正常读取
            verify_orig_cmd = f'ffprobe -v error "{video_path}"'
            verify_orig_process = await asyncio.create_subprocess_shell(
                verify_orig_cmd,
                stdout=asyncio.subprocess.PIPE,
                stderr=asyncio.subprocess.PIPE
            )
            _, verify_orig_stderr = await verify_orig_process.communicate()
            
            if verify_orig_process.returncode != 0:
                logger.warning(f"原始视频文件可能已损坏: {video_path}")
                logger.debug(f"ffprobe错误输出: {verify_orig_stderr.decode()}")
                return  # 如果原始文件有问题，不进行修复
            
            # 1. 首先尝试最快的方式：直接复制流并优化元数据位置
            simple_cmd = f'ffmpeg -i "{video_path}" -c copy -movflags +faststart -y "{temp_path}"'
            logger.debug(f"执行简单修复命令: {simple_cmd}")
            
            simple_process = await asyncio.create_subprocess_shell(
                simple_cmd,
                stdout=asyncio.subprocess.PIPE,
                stderr=asyncio.subprocess.PIPE
            )
            
            simple_stdout, simple_stderr = await simple_process.communicate()
            
            # 检查临时文件是否生成
            if simple_process.returncode == 0 and os.path.exists(temp_path) and os.path.getsize(temp_path) > 0:
                # 验证新文件是否可以正常打开
                verify_cmd = f'ffprobe -v error -select_streams v:0 -show_entries stream=duration -of default=noprint_wrappers=1:nokey=1 "{temp_path}"'
                logger.debug(f"执行验证命令: {verify_cmd}")
                
                verify_process = await asyncio.create_subprocess_shell(
                    verify_cmd,
                    stdout=asyncio.subprocess.PIPE,
                    stderr=asyncio.subprocess.PIPE
                )
                verify_stdout, verify_stderr = await verify_process.communicate()
                
                if verify_process.returncode == 0:
                    verify_duration = verify_stdout.decode().strip()
                    logger.debug(f"视频修复成功，时长: {verify_duration}秒")
                    
                    # 替换原文件前确保有足够的磁盘空间
                    try:
                        # 确保原文件存在且临时文件有内容
                        if os.path.exists(video_path) and os.path.exists(temp_path) and os.path.getsize(temp_path) > 0:
                            os.remove(video_path)
                            os.rename(temp_path, video_path)
                            logger.debug(f"成功替换修复后的视频文件")
                            return
                    except Exception as e:
                        logger.warning(f"替换视频文件失败: {e}")
                        # 失败时保留临时文件，继续尝试其他方法
                else:
                    verify_error = verify_stderr.decode()
                    logger.warning(f"简单处理后视频验证失败，尝试备用方法。错误: {verify_error}")
            else:
                simple_error = simple_stderr.decode()
                logger.warning(f"简单修复失败，尝试备用方法。错误: {simple_error}")
                
                # 清理可能存在的临时文件
                if os.path.exists(temp_path):
                    try:
                        os.remove(temp_path)
                    except:
                        pass
            
            # 2. 如果简单方法失败，尝试获取视频信息并进行最小必要的处理
            probe_cmd = f'ffprobe -v quiet -print_format json -show_format -show_streams "{video_path}"'
            logger.debug(f"执行探测命令: {probe_cmd}")
            
            probe_process = await asyncio.create_subprocess_shell(
                probe_cmd,
                stdout=asyncio.subprocess.PIPE,
                stderr=asyncio.subprocess.PIPE
            )
            probe_stdout, probe_stderr = await probe_process.communicate()
            
            if probe_process.returncode == 0:
                try:
                    probe_data = json.loads(probe_stdout.decode())
                    # 获取视频时长（秒）
                    if 'format' in probe_data and 'duration' in probe_data['format']:
                        duration = float(probe_data['format']['duration'])
                        
                        # 使用最小必要的处理参数
                        cmd = (
                            f'ffmpeg -i "{video_path}" '  # 输入文件
                            f'-c copy '  # 直接复制流，不重新编码
                            f'-movflags +faststart '  # 优化元数据位置
                            f'-metadata duration="{duration}" '  # 设置时长元数据
                            f'-y '  # 覆盖输出文件
                            f'"{temp_path}"'  # 输出文件
                        )
                        logger.debug(f"执行修复命令: {cmd}")
                        
                        process = await asyncio.create_subprocess_shell(
                            cmd,
                            stdout=asyncio.subprocess.PIPE,
                            stderr=asyncio.subprocess.PIPE
                        )
                        
                        stdout, stderr = await process.communicate()
                        
                        if process.returncode == 0 and os.path.exists(temp_path) and os.path.getsize(temp_path) > 0:
                            try:
                                os.remove(video_path)
                                os.rename(temp_path, video_path)
                                logger.debug(f"成功替换修复后的视频文件")
                            except Exception as e:
                                logger.warning(f"替换视频文件失败: {e}")
                                if os.path.exists(temp_path):
                                    try:
                                        os.remove(temp_path)
                                    except:
                                        pass
                        else:
                            stderr_output = stderr.decode()
                            logger.warning(f"视频处理失败")
                            logger.debug(f"FFmpeg错误输出: {stderr_output}")
                            if os.path.exists(temp_path):
                                try:
                                    os.remove(temp_path)
                                except:
                                    pass
                    else:
                        logger.warning(f"视频格式信息中没有持续时间数据")
                
                except (json.JSONDecodeError, KeyError, ValueError) as e:
                    logger.warning(f"解析视频信息失败: {e}")
                    if os.path.exists(temp_path):
                        try:
                            os.remove(temp_path)
                        except:
                            pass
            
            else:
                probe_stderr_output = probe_stderr.decode()
                logger.warning(f"获取视频信息失败: {probe_stderr_output}")
                if os.path.exists(temp_path):
                    try:
                        os.remove(temp_path)
                    except:
                        pass
                
        except Exception as e:
            logger.error(f"修复视频元数据时出错: {e}", exc_info=True)
            # 确保临时文件被删除
            if 'temp_path' in locals() and os.path.exists(temp_path):
                try:
                    os.remove(temp_path)
                except Exception as cleanup_error:
                    logger.warning(f"清理临时文件失败: {cleanup_error}")

    async def _encode_video(self, video_path: str) -> Optional[str]:
        """将视频编码为base64
        Returns:
            str: base64编码后的视频数据,失败返回None
        """
        try:
            # 获取文件大小
            file_size = os.path.getsize(video_path)
            
            # 如果文件大于50MB，尝试压缩
            if file_size > 50 * 1024 * 1024:
                logger.warning(f"视频文件过大 ({file_size / (1024*1024):.2f}MB)，尝试压缩")
                compressed_path = f"{video_path}.compressed.mp4"
                
                # 使用ffmpeg压缩视频
                try:
                    compress_cmd = f'ffmpeg -i "{video_path}" -c:v libx264 -crf 28 -preset faster -c:a aac -b:a 128k "{compressed_path}" -y'
                    logger.debug(f"执行压缩命令: {compress_cmd}")
                    
                    process = await asyncio.create_subprocess_shell(
                        compress_cmd,
                        stdout=asyncio.subprocess.PIPE,
                        stderr=asyncio.subprocess.PIPE
                    )
                    
                    stdout, stderr = await process.communicate()
                    
                    if process.returncode == 0 and os.path.exists(compressed_path):
                        compressed_size = os.path.getsize(compressed_path)
                        logger.debug(f"视频压缩成功: {file_size / (1024*1024):.2f}MB -> {compressed_size / (1024*1024):.2f}MB")
                        
                        # 检查如果压缩后比原始更大，则使用原始文件
                        if compressed_size < file_size:
                            video_path = compressed_path
                            file_size = compressed_size
                        else:
                            logger.debug("压缩后文件更大，使用原始文件")
                            os.remove(compressed_path)
                except Exception as e:
                    logger.warning(f"视频压缩失败，继续使用原始文件", exc_info=True)
                    if os.path.exists(compressed_path):
                        try:
                            os.remove(compressed_path)
                        except:
                            pass
            
            # 使用分块读取和编码，避免内存问题
            logger.debug(f"开始编码视频: {video_path}，大小: {file_size / (1024*1024):.2f}MB")
            start_time = time.time()
            
            # 读取文件并编码
            chunk_size = 4 * 1024 * 1024  # 4MB
            with open(video_path, 'rb') as f:
                video_data = f.read()
            
            base64_data = base64.b64encode(video_data).decode('utf-8')
            
            duration = time.time() - start_time
            logger.debug(f"视频编码完成，耗时: {duration:.2f}秒")
            
            return base64_data
            
        except MemoryError:
            logger.error(f"视频编码内存不足: {video_path}, 大小: {os.path.getsize(video_path) / (1024*1024):.2f}MB")
            return None
        except Exception as e:
            logger.error(f"视频编码失败: {video_path}", exc_info=True)
            return None

    def _extract_first_frame(self, video_path: str) -> Optional[str]:
        """从视频中提取第一帧并转换为base64
        Returns:
            str: base64编码的图片数据,失败返回None
        """
        try:
            logger.debug(f"尝试提取视频首帧: {video_path}")
            
            # 使用ffmpeg提取第一帧
            try:
                # 首先尝试使用ffmpeg提取首帧，这更可靠
                temp_img_path = f"{video_path}.thumb.jpg"
                
                # 使用ffmpeg提取第一帧并保存为jpg
                extract_cmd = f'ffmpeg -i "{video_path}" -vframes 1 -q:v 2 -y "{temp_img_path}"'
                logger.debug(f"执行首帧提取命令: {extract_cmd}")
                
                extract_process = os.system(extract_cmd)
                
                if os.path.exists(temp_img_path) and os.path.getsize(temp_img_path) > 0:
                    logger.debug(f"使用ffmpeg成功提取首帧")
                    # 读取并编码图片
                    with open(temp_img_path, 'rb') as f:
                        image_data = f.read()
                    
                    # 删除临时文件
                    try:
                        os.remove(temp_img_path)
                    except Exception as e:
                        logger.warning(f"删除临时封面文件失败: {temp_img_path}", exc_info=True)
                    
                    # 返回base64编码
                    image_base64 = base64.b64encode(image_data).decode('utf-8')
                    logger.debug(f"首帧提取成功，大小: {len(image_data) / 1024:.2f}KB")
                    return image_base64
            except Exception as ffmpeg_error:
                logger.warning(f"使用ffmpeg提取首帧失败，尝试备用方法", exc_info=True)
            
            # 如果ffmpeg方法失败，尝试使用OpenCV
            logger.debug("尝试使用OpenCV提取首帧")
            
            # 打开视频文件
            cap = cv2.VideoCapture(video_path)
            if not cap.isOpened():
                logger.warning(f"OpenCV无法打开视频: {video_path}")
                return None
                
            # 读取第一帧
            ret, frame = cap.read()
            if not ret:
                logger.warning(f"OpenCV无法读取视频首帧: {video_path}")
                cap.release()
                return None
                
            # 转换为PIL Image
            frame_rgb = cv2.cvtColor(frame, cv2.COLOR_BGR2RGB)
            image = Image.fromarray(frame_rgb)
            
            # 获取原始尺寸
            original_width, original_height = image.size
            logger.debug(f"视频首帧尺寸: {original_width}x{original_height}")
            
            # 调整图片大小
            max_size = (800, 800)  # 最大尺寸
            image.thumbnail(max_size, Image.Resampling.LANCZOS)
            
            # 获取调整后尺寸
            new_width, new_height = image.size
            
            # 转换为base64
            buffer = BytesIO()
            image.save(buffer, format="JPEG", quality=95)
            image_data = buffer.getvalue()
            image_size = len(image_data)
            
            image_base64 = base64.b64encode(image_data).decode('utf-8')
            
            # 释放资源
            cap.release()
            logger.debug(f"OpenCV提取首帧成功，压缩后大小: {image_size / 1024:.2f}KB")
            
            return image_base64
            
        except Exception as e:
            logger.error(f"提取视频首帧失败: {video_path}", exc_info=True)
            return None

    async def _download_menu_image(self) -> Optional[bytes]:
        """加载菜单图片,返回图片二进制数据"""
        try:
            # 从本地文件加载图片
            local_image_path = "C:\\XYBotV2\\data\\default picture\\shipincaidan.jpg"
            if os.path.exists(local_image_path):
                with open(local_image_path, 'rb') as f:
                    return f.read()
            else:
                logger.error(f"本地菜单图片不存在: {local_image_path}")
                # 如果本地文件不存在，尝试从网络下载
                async with httpx.AsyncClient(timeout=30.0) as client:
                    response = await client.get(self.menu_image)
                    if response.status_code == 200:
                        return response.content
                    else:
                        return None
        except Exception as e:
            logger.error(f"加载菜单图片失败: {e}")
            return None

    @on_text_message
    async def handle_menu(self, bot: WechatAPIClient, message: dict):
        """处理菜单命令"""
        content = message.get("Content", "").strip()
        if content not in self.command:
            return
            
        wxid = message.get("FromWxid")
        roomid = message.get("FromGroup", wxid)
        
        # 检查菜单状态
        room_status = self.room_status.get(roomid, {})
        if room_status and time.time() - room_status.get("time", 0) < 5:  # 5秒内不重复发送
            await bot.send_text_message(roomid, "菜单已发送,请勿重复请求")
            return
            
        # 发送菜单图片
        try:
            # 下载菜单图片
            image_data = await self._download_menu_image()
            if not image_data:
                await bot.send_text_message(roomid, "获取菜单失败,等会再试试")
                return
                
            # 直接发送二进制数据
            await bot.send_image_message(roomid, image_data)
            
            self.room_status[roomid] = {
                "time": time.time(),
                "expire": time.time() + self.cache_time,
                "notified": False
            }
                
        except Exception as e:
            logger.error(f"发送菜单失败: {e}")
            await bot.send_text_message(roomid, "发送菜单失败,等会再试试")

    @on_text_message
    async def handle_video(self, bot: WechatAPIClient, message: dict):
        """处理视频请求"""
        content = message.get("Content", "").strip()
        
        # 严格匹配"看+数字"的格式
        if not re.match(r'^看\d+$', content):
            return
            
        wxid = message.get("FromWxid")
        roomid = message.get("FromGroup", wxid)
        
        # 检查该房间是否发送过菜单且菜单在有效期内
        room_status = self.room_status.get(roomid)
        if not room_status:
            # 如果没有发送过菜单,直接忽略命令
            return
            
        if time.time() > room_status["expire"]:
            # 如果菜单已过期,提示重新获取菜单(只提示一次)
            if not room_status["notified"]:
                await bot.send_text_message(roomid, "菜单已过期,请重新发送「视频菜单」")
                room_status["notified"] = True
            return
        
        # 检查当前并发数    
        if self.video_semaphore.locked():
            await bot.send_text_message(roomid, "正在处理其他视频，等会再试...")
            return
            
        video_path = None
        try:
            # 使用信号量控制并发
            async with self.video_semaphore:
                # 检查磁盘空间
                if not self._check_disk_space():
                    await bot.send_text_message(roomid, "系统存储空间不足，请稍后再试...")
                    return
                    
                # 解析序号
                sequence = int(content[1:])  # 去掉"看"后转为数字
                if sequence < 1 or sequence > 128:
                    await bot.send_text_message(roomid, "无效的序号,请输入正确的序号(1-128)")
                    return
                    
                category = self.api_mapping[sequence]["name"]
                
                await bot.send_text_message(
                    roomid, 
                    f"正在获取{category}视频,请稍等..."
                )
                
                # 获取视频URL
                result = await self._get_video(category)
                if not result["success"]:
                    await bot.send_text_message(roomid, result["message"])
                    return
                
                video_url = result["url"]
                logger.debug(f"获取到视频URL: {video_url}")
                    
                # 下载视频到本地
                video_path = await self._download_video(video_url, category)
                if not video_path:
                    await bot.send_text_message(roomid, "下载视频失败,请稍后重试")
                    return
                
                logger.debug(f"视频已下载到: {video_path}")
                
                # 尝试获取视频时长信息
                video_duration = None
                try:
                    probe_cmd = f'ffprobe -v error -show_entries format=duration -of default=noprint_wrappers=1:nokey=1 "{video_path}"'
                    logger.debug(f"执行命令: {probe_cmd}")
                    
                    probe_process = await asyncio.create_subprocess_shell(
                        probe_cmd,
                        stdout=asyncio.subprocess.PIPE,
                        stderr=asyncio.subprocess.PIPE
                    )
                    probe_stdout, probe_stderr = await probe_process.communicate()
                    
                    if probe_process.returncode == 0:
                        stdout_content = probe_stdout.decode().strip()
                        
                        duration = float(stdout_content)
                        # 修改: 尝试以秒为单位传递时长，而不是毫秒
                        video_duration = int(duration)
                        logger.debug(f"视频时长: {video_duration}秒")
                    else:
                        stderr_content = probe_stderr.decode()
                        logger.debug(f"获取视频时长失败，进程返回码: {probe_process.returncode}, 错误: {stderr_content}")
                        
                except Exception as e:
                    logger.warning(f"获取视频时长失败: {e}", exc_info=True)
                
                # 编码视频数据
                try:
                    video_base64 = await self._encode_video(video_path)
                    if not video_base64:
                        await bot.send_text_message(roomid, "处理视频失败,请稍后重试")
                        return
                    logger.debug(f"视频编码完成，数据大小: {len(video_base64) if video_base64 else 0}")
                except Exception as e:
                    logger.error(f"视频编码失败", exc_info=True)
                    await bot.send_text_message(roomid, "处理视频失败,请稍后重试")
                    return
                
                # 提取视频首帧作为封面
                try:
                    cover_base64 = self._extract_first_frame(video_path)
                    if not cover_base64:
                        logger.debug(f"提取视频首帧失败，将使用空封面")
                        # 使用空封面
                        cover_base64 = None
                except Exception as e:
                    logger.warning(f"提取视频首帧失败，将使用空封面", exc_info=True)
                    cover_base64 = None
                
                # 发送视频
                try:
                    # 添加重试机制
                    max_send_retries = 2
                    current_retry = 0
                    send_success = False
                    
                    # 直接手动设置时长（秒），而不依赖API进行计算
                    if video_duration is None:
                        # 如果获取不到，设置一个默认值（大部分短视频在15-30秒之间）
                        logger.debug("无法获取视频时长，使用默认值")
                        video_duration = 20
                    
                    while current_retry <= max_send_retries and not send_success:
                        try:
                            if current_retry > 0:
                                logger.debug(f"尝试重新发送视频，第{current_retry}次重试")
                                # 重试前等待一段时间
                                await asyncio.sleep(2)
                            
                            send_start_time = time.time()
                            logger.debug(f"开始发送视频 - 时长: {video_duration}秒, 封面大小: {len(cover_base64) if cover_base64 else 0}字节")
                            
                            # 发送视频，添加时长参数
                            try:
                                # 首先尝试创建完整的请求
                                json_param = {
                                    "Wxid": bot.wxid, 
                                    "ToWxid": roomid, 
                                    "Base64": video_base64, 
                                    "ImageBase64": cover_base64, 
                                    "PlayLength": video_duration
                                }
                                
                                # 使用httpx直接发送请求，避免可能的API问题
                                logger.debug("使用httpx发送视频请求")
                                async with httpx.AsyncClient(timeout=300.0) as client:  # 设置足够长的超时
                                    response = await client.post(
                                        f'http://{bot.ip}:{bot.port}/SendVideoMsg', 
                                        json=json_param,
                                        timeout=300.0
                                    )
                                    json_resp = response.json()
                                    
                                    if json_resp.get("Success"):
                                        data = json_resp.get("Data")
                                        client_msg_id = data.get("clientMsgId")
                                        new_msg_id = data.get("newMsgId")
                                        logger.debug(f"视频发送成功，响应: clientMsgId={client_msg_id}, newMsgId={new_msg_id}")

                                        # 通知撤回插件存储消息信息
                                        try:
                                            from plugins.RevokePlugin.main import RevokePlugin
                                            import time as time_module
                                            create_time = int(time_module.time())
                                            RevokePlugin.notify_message_sent(roomid, client_msg_id, create_time, new_msg_id)
                                        except Exception as e:
                                            logger.debug(f"通知撤回插件失败: {e}")
                                    else:
                                        error_msg = json_resp.get("Message", "未知错误")
                                        logger.warning(f"API返回错误: {error_msg}")
                                        raise Exception(f"API返回错误: {error_msg}")
                            except Exception as direct_e:
                                logger.debug(f"直接HTTP请求失败，尝试使用API: {direct_e}")
                                # 如果直接请求失败，尝试使用API
                                client_msg_id, new_msg_id = await bot.send_video_message(
                                    roomid,
                                    video_base64,
                                    cover_base64,  # 使用视频首帧作为封面
                                    video_duration  # 传递处理后的视频时长
                                )
                            
                            send_duration = time.time() - send_start_time
                            logger.debug(f"发送视频耗时: {send_duration:.2f}秒, client_msg_id: {client_msg_id}, new_msg_id: {new_msg_id}")
                            
                            # 检查发送结果
                            if new_msg_id != 0 and client_msg_id:
                                send_success = True
                                logger.debug(f"视频发送成功")
                                # 发送成功后等待60秒再删除文件,确保视频已经完全发送
                                await asyncio.sleep(60)
                                try:
                                    if os.path.exists(video_path):
                                        os.remove(video_path)
                                        logger.debug(f"成功删除视频文件: {video_path}")
                                except Exception as e:
                                    logger.error(f"清理已发送的视频文件失败: {video_path}", exc_info=True)
                            else:
                                logger.warning(f"发送视频返回值异常 - client_msg_id: {client_msg_id}, new_msg_id: {new_msg_id}")
                                # 尝试下一次重试
                                current_retry += 1
                            
                        except Exception as retry_e:
                            logger.warning(f"发送视频异常，准备重试: {str(retry_e)}", exc_info=True)
                            current_retry += 1
                    
                    # 检查最终发送结果
                    if not send_success:
                        logger.error(f"发送视频失败，已重试{max_send_retries}次")
                        await bot.send_text_message(roomid, "发送视频失败,请稍后重试")
                        # 发送失败的文件由定时清理任务处理
                        
                except Exception as e:
                    logger.error(f"发送视频失败: {str(e)}", exc_info=True)
                    await bot.send_text_message(roomid, "发送视频失败,请稍后重试")
                
        except ValueError as e:
            logger.error(f"处理视频请求出现值错误", exc_info=True)
            await bot.send_text_message(roomid, "请输入正确的序号(1-128)")
        except Exception as e:
            logger.error(f"处理视频请求失败: {str(e)}", exc_info=True)
            await bot.send_text_message(roomid, "处理请求失败,请稍后重试")
            # 发生异常时不立即删除文件,由定时清理任务处理

    @on_text_message
    async def handle_random_video(self, bot: WechatAPIClient, message: dict):
        """处理随机视频命令"""
        content = message.get("Content", "").strip()
        
        # 检查命令是否匹配
        if content not in self.random_command:
            return
            
        wxid = message.get("FromWxid")
        roomid = message.get("FromGroup", wxid)
        
        # 检查并发数
        if self.video_semaphore.locked():
            await bot.send_text_message(roomid, "正在处理其他视频，等会再试...")
            return
            
        video_path = None
        try:
            # 使用信号量控制并发
            async with self.video_semaphore:
                # 检查磁盘空间
                if not self._check_disk_space():
                    await bot.send_text_message(roomid, "系统存储空间不足，请稍后再试...")
                    return
                    
                # 发送提示消息
                await bot.send_text_message(roomid, "正在获取随机视频，请稍等...")
                
                # 获取随机视频URL
                result = await self._get_random_video()
                if not result["success"]:
                    await bot.send_text_message(roomid, result["message"])
                    return
                
                video_url = result["url"]
                
                # 下载视频到本地
                video_path = await self._download_video(video_url, "随机视频")
                if not video_path:
                    await bot.send_text_message(roomid, "下载视频失败，请稍后重试")
                    return
                
                # 编码视频数据
                video_base64 = await self._encode_video(video_path)
                if not video_base64:
                    await bot.send_text_message(roomid, "处理视频失败，请稍后重试")
                    return
                
                # 提取视频首帧作为封面
                cover_base64 = self._extract_first_frame(video_path)
                if cover_base64:
                    pass
                else:
                    logger.debug(f"提取随机视频首帧失败，将使用空封面")
                
                # 尝试获取视频时长信息
                video_duration = None
                try:
                    probe_cmd = f'ffprobe -v error -show_entries format=duration -of default=noprint_wrappers=1:nokey=1 "{video_path}"'
                    probe_process = await asyncio.create_subprocess_shell(
                        probe_cmd,
                        stdout=asyncio.subprocess.PIPE,
                        stderr=asyncio.subprocess.PIPE
                    )
                    probe_stdout, probe_stderr = await probe_process.communicate()
                    if probe_process.returncode == 0:
                        duration = float(probe_stdout.decode().strip())
                        # 修改: 尝试以秒为单位传递时长，而不是毫秒
                        video_duration = int(duration)
                except Exception as e:
                    logger.warning(f"获取随机视频时长失败: {e}")

                # 发送视频 - 使用与普通视频点播相同的逻辑
                try:
                    # 添加重试机制
                    max_send_retries = 2
                    current_retry = 0
                    send_success = False

                    # 直接手动设置时长（秒），而不依赖API进行计算
                    if video_duration is None:
                        # 如果获取不到，设置一个默认值（大部分短视频在15-30秒之间）
                        logger.debug("无法获取随机视频时长，使用默认值")
                        video_duration = 20

                    while current_retry <= max_send_retries and not send_success:
                        try:
                            if current_retry > 0:
                                logger.debug(f"尝试重新发送随机视频，第{current_retry}次重试")
                                # 重试前等待一段时间
                                await asyncio.sleep(2)

                            send_start_time = time.time()
                            logger.debug(f"开始发送随机视频 - 时长: {video_duration}秒, 封面大小: {len(cover_base64) if cover_base64 else 0}字节")

                            # 发送视频，添加时长参数
                            try:
                                # 首先尝试创建完整的请求
                                json_param = {
                                    "Wxid": bot.wxid,
                                    "ToWxid": roomid,
                                    "Base64": video_base64,
                                    "ImageBase64": cover_base64,
                                    "PlayLength": video_duration
                                }

                                # 使用httpx直接发送请求，避免可能的API问题
                                logger.debug("使用httpx发送随机视频请求")
                                async with httpx.AsyncClient(timeout=300.0) as client:  # 设置足够长的超时
                                    response = await client.post(
                                        f'http://{bot.ip}:{bot.port}/SendVideoMsg',
                                        json=json_param,
                                        timeout=300.0
                                    )
                                    json_resp = response.json()

                                    if json_resp.get("Success"):
                                        data = json_resp.get("Data")
                                        client_msg_id = data.get("clientMsgId")
                                        new_msg_id = data.get("newMsgId")
                                        logger.debug(f"随机视频发送成功，响应: clientMsgId={client_msg_id}, newMsgId={new_msg_id}")

                                        # 通知撤回插件存储消息信息
                                        try:
                                            from plugins.RevokePlugin.main import RevokePlugin
                                            import time as time_module
                                            create_time = int(time_module.time())
                                            RevokePlugin.notify_message_sent(roomid, client_msg_id, create_time, new_msg_id)
                                        except Exception as e:
                                            logger.debug(f"通知撤回插件失败: {e}")
                                    else:
                                        error_msg = json_resp.get("Message", "未知错误")
                                        logger.warning(f"API返回错误: {error_msg}")
                                        raise Exception(f"API返回错误: {error_msg}")
                            except Exception as direct_e:
                                logger.debug(f"直接HTTP请求失败，尝试使用API: {direct_e}")
                                # 如果直接请求失败，尝试使用API（不传递时长参数）
                                client_msg_id, new_msg_id = await bot.send_video_message(
                                    roomid,
                                    video_base64,
                                    cover_base64  # 使用视频首帧作为封面
                                )

                            send_duration = time.time() - send_start_time
                            logger.debug(f"发送随机视频耗时: {send_duration:.2f}秒, client_msg_id: {client_msg_id}, new_msg_id: {new_msg_id}")

                            # 检查发送结果
                            if new_msg_id != 0 and client_msg_id:
                                send_success = True
                                logger.debug(f"随机视频发送成功")
                                # 发送成功后等待60秒再删除文件,确保视频已经完全发送
                                await asyncio.sleep(60)
                                try:
                                    if os.path.exists(video_path):
                                        os.remove(video_path)
                                        logger.debug(f"成功删除随机视频文件: {video_path}")
                                except Exception as e:
                                    logger.error(f"清理已发送的随机视频文件失败: {video_path}", exc_info=True)
                            else:
                                logger.warning(f"发送随机视频返回值异常 - client_msg_id: {client_msg_id}, new_msg_id: {new_msg_id}")
                                # 尝试下一次重试
                                current_retry += 1

                        except Exception as retry_e:
                            logger.warning(f"发送随机视频异常，准备重试: {str(retry_e)}", exc_info=True)
                            current_retry += 1

                    # 检查最终发送结果
                    if not send_success:
                        logger.error(f"发送随机视频失败，已重试{max_send_retries}次")
                        await bot.send_text_message(roomid, "发送视频失败,请稍后重试")
                        # 发送失败的文件由定时清理任务处理

                except Exception as e:
                    logger.error(f"发送随机视频失败: {str(e)}", exc_info=True)
                    await bot.send_text_message(roomid, "发送视频失败,请稍后重试")
                    
        except Exception as e:
            logger.error(f"处理随机视频请求失败: {str(e)}", exc_info=True)
            await bot.send_text_message(roomid, "处理请求失败，请稍后重试")
            # 发生异常时不立即删除文件，由定时清理任务处理

    def _check_disk_space(self) -> bool:
        """检查磁盘空间是否足够
        
        Returns:
            bool: 空间足够返回True，否则返回False
        """
        try:
            # 获取临时目录所在磁盘的可用空间
            free_space = shutil.disk_usage(self.temp_dir).free
            if free_space < self.min_disk_space:
                # 空间不足时，尝试紧急清理所有临时文件
                self._emergency_cleanup()
                # 重新检查空间
                free_space = shutil.disk_usage(self.temp_dir).free
                return free_space >= self.min_disk_space
            return True
        except Exception as e:
            logger.error(f"检查磁盘空间失败: {e}")
            # 发生异常时保守返回True
            return True
            
    def _emergency_cleanup(self):
        """紧急清理所有临时文件"""
        try:
            for file in self.temp_dir.glob("*.mp4"):
                try:
                    file.unlink()
                except Exception as e:
                    logger.error(f"紧急清理文件失败 {file}: {e}")
        except Exception as e:
            logger.error(f"紧急清理临时文件失败: {e}")
            
    async def cleanup_temp_files(self):
        """清理临时文件"""
        try:
            # 清理1小时前的临时文件
            current_time = time.time()
            for file in self.temp_dir.glob("*.mp4"):
                if current_time - file.stat().st_mtime > 3600:  # 1小时
                    try:
                        file.unlink()
                    except Exception as e:
                        logger.error(f"清理文件失败 {file}: {e}")
                        
        except Exception as e:
            logger.error(f"清理临时文件失败: {e}")
            # 发生异常时才检查目录是否存在
            self._ensure_temp_dir(log=False)  # 异常情况下不输出日志避免日志爆炸
            
    async def _schedule_cleanup(self):
        """调度清理任务"""
        while True:
            try:
                await asyncio.sleep(1800)  # 每30分钟清理一次
                await self.cleanup_temp_files()
            except Exception as e:
                logger.error(f"调度清理任务失败: {e}")
                # 出错后等待短暂时间再继续循环
                await asyncio.sleep(60) 