import os
import json
import tomllib
import time
import random
import uuid
import httpx
import idna
from loguru import logger
from urllib.parse import quote
from pathlib import Path
from typing import Optional, Dict, Any, List
import asyncio

# 创建异步HTTP客户端
async def get_httpx_client():
    return httpx.AsyncClient(
        timeout=httpx.Timeout(connect=10.0, read=20.0, write=20.0, pool=5.0),
        verify=False,  # 禁用SSL验证
        follow_redirects=True,
        limits=httpx.Limits(max_connections=20, max_keepalive_connections=10),
    )

# 定义HTTP响应类，用于兼容之前的代码
class HttpResponse:
    def __init__(self, status_code, body, headers=None):
        self.code = status_code
        self.status_code = status_code  # 兼容性
        self.body = body
        self.headers = headers or {}
        self._text = None

    def json(self):
        """解析 JSON 响应体"""
        if not self.body:
            return {}
        if isinstance(self.body, str):
            return json.loads(self.body)
        return json.loads(self.body.decode('utf-8'))

    @property
    def text(self):
        """获取响应文本"""
        if self._text is None:
            if isinstance(self.body, str):
                self._text = self.body
            else:
                self._text = self.body.decode('utf-8', errors='ignore')
        return self._text

    async def aread(self):
        """兼容异步读取方法"""
        return self.body

from WechatAPI import WechatAPIClient
from utils.decorators import on_text_message
from utils.plugin_base import PluginBase

# 性能监控功能已移除
PERFORMANCE_MONITORING_ENABLED = False


class DoubaoVideoSearch(PluginBase):
    description = "豆包AI视频搜索插件"
    author = "XYBot"
    version = "1.0.0"
    plugin_name = "DoubaoVideoSearch"

    def __init__(self):
        # 必须先调用父类初始化
        super().__init__()

        # 读取配置
        config = {}
        try:
            config_path = "plugins/DoubaoVideoSearch/config.toml"
            if os.path.exists(config_path):
                with open(config_path, "rb") as f:
                    plugin_config = tomllib.load(f)
                config = plugin_config.get("DoubaoVideoSearch", {})
            else:
                logger.warning(f"[{self.plugin_name}] 配置文件 {config_path} 不存在，将使用默认配置")
        except Exception as e:
            logger.warning(f"[{self.plugin_name}] 配置文件读取失败: {str(e)}，将使用默认配置")

        # 基本配置初始化
        self.enable = config.get("enable", True)
        self.command = config.get("command", ["找视频", "搜视频", "视频搜索"])
        self.admin_command = config.get("admin_command", ["视频搜索状态", "视频搜索报告"])  # 管理员命令
        self.command_format = config.get("command-format", """
🎬豆包视频搜索插件使用说明：
找视频 <关键词> - 搜索相关视频
""")

        # API配置
        self.api_config = config.get("API", {})
        self.api_base_url = "https://www.doubao.com/samantha/chat/completion"
        self.cookies = self.api_config.get("cookies", "")

        # 豆包API常量 - 使用固定的设备ID
        self.device_id = "7468716989062841895"
        self.tea_uuid = "7468716986638386703"
        self.web_id = "7468716986638386703"

        # httpx客户端管理
        self._client = None
        self._client_lock = asyncio.Lock()

        # 令牌桶配置
        self.rate_limit = config.get("rate_limit", {
            "tokens_per_second": 0.5,  # 每秒生成的令牌数（1个令牌/2秒）
            "bucket_size": 5  # 令牌桶容量
        })
        self._token_bucket = self.rate_limit["bucket_size"]  # 当前令牌数
        self._last_token_time = time.time()  # 上次更新令牌的时间
        self._token_lock = asyncio.Lock()  # 令牌桶锁

        # 用户级别的请求限制
        self._user_limits: Dict[str, Dict[str, float]] = {}  # wxid -> {user_wxid -> last_request_time}
        self.min_request_interval = 2.0  # 单个用户的最小请求间隔（秒）

        # 视频搜索去重缓存 - 存储每个查询关键词已发送过的视频URL
        self._video_cache = {}  # 格式: {query_key: {wxid: [sent_urls]}}
        self._video_cache_lock = asyncio.Lock()

        # 自然化响应配置
        self.natural_response = config.get("natural_response", True)

        self._init_natural_responses()

    def _init_natural_responses(self):
        """初始化自然化回复词库"""
        self.confirm_msgs = [
            "好的", "收到", "明白了", "嗯", "知道了", "行", "OK"
        ]

        self.error_msgs = [
            "这可把我难住了", "搞不定了", "这个有点难", "弄不了",
            "出问题了", "搞不出来", "这个不行"
        ]

        self.rate_limit_msgs = [
            "你搁这刷bug呢", "慢点慢点", "别这么急", "一个一个来",
            "太快了太快了", "歇会儿", "别刷了"
        ]

    async def _simple_confirm(self, bot, wxid):
        """简单确认回复，不艾特人"""
        if self.natural_response:
            confirm_msg = random.choice(self.confirm_msgs)
            await bot.send_text_message(wxid, confirm_msg)

    async def _get_unique_video(self, videos: list, query_key: str, wxid: str) -> Optional[dict]:
        """从视频列表中获取一个未发送过的视频

        Args:
            videos: 视频列表
            query_key: 查询关键词（用于缓存）
            wxid: 群组或用户ID

        Returns:
            未发送过的视频信息，如果都发送过则返回None
        """
        async with self._video_cache_lock:
            # 初始化缓存结构
            if query_key not in self._video_cache:
                self._video_cache[query_key] = {}
            if wxid not in self._video_cache[query_key]:
                self._video_cache[query_key][wxid] = []

            sent_urls = self._video_cache[query_key][wxid]

            # 查找未发送过的视频
            for video in videos:
                video_url = video.get("url", "")
                if video_url and video_url not in sent_urls:
                    # 记录已发送的视频URL
                    sent_urls.append(video_url)
                    # 限制缓存大小，只保留最近的20个URL
                    if len(sent_urls) > 20:
                        sent_urls.pop(0)
                    return video

            # 如果所有视频都发送过，清空缓存并返回第一个视频
            if videos:
                self._video_cache[query_key][wxid] = [videos[0].get("url", "")]
                return videos[0]

            return None

    async def _update_tokens(self):
        """更新令牌桶中的令牌数"""
        current_time = time.time()
        elapsed = current_time - self._last_token_time
        new_tokens = elapsed * self.rate_limit["tokens_per_second"]
        self._token_bucket = min(
            self.rate_limit["bucket_size"],
            self._token_bucket + new_tokens
        )
        self._last_token_time = current_time

    async def _acquire_token(self) -> bool:
        """尝试获取一个令牌

        Returns:
            bool: 是否成功获取令牌
        """
        async with self._token_lock:
            await self._update_tokens()
            if self._token_bucket >= 1:
                self._token_bucket -= 1
                return True
            return False

    def _check_user_limit(self, wxid: str, user_wxid: str) -> float:
        """检查用户是否可以发送请求

        Args:
            wxid: 群聊ID
            user_wxid: 用户ID

        Returns:
            float: 需要等待的时间（秒），0表示可以立即请求
        """
        current_time = time.time()

        # 初始化用户限制记录
        if wxid not in self._user_limits:
            self._user_limits[wxid] = {}

        # 获取用户上次请求时间
        last_request = self._user_limits[wxid].get(user_wxid, 0)

        # 计算需要等待的时间
        wait_time = max(0, self.min_request_interval - (current_time - last_request))

        # 如果可以请求，更新上次请求时间
        if wait_time == 0:
            self._user_limits[wxid][user_wxid] = current_time

        return wait_time

    async def call_doubao_video_search_api(self, bot: WechatAPIClient, message: dict, query: str) -> Optional[Dict[str, Any]]:
        """调用豆包视频搜索API

        Args:
            bot: 微信API客户端
            message: 消息对象
            query: 搜索查询关键词

        Returns:
            搜索结果字典，包含视频信息或错误信息
        """
        # 忽略未使用的参数
        _ = bot, message

        max_retries = 3  # 最大重试次数
        base_delay = 3  # 基础等待时间（秒）
        max_delay = 10  # 最大等待时间（秒）

        last_error = None
        conversation_id = None

        logger.info(f"[{self.plugin_name}] 开始视频搜索，关键词: {query}")

        for retry in range(max_retries):
            try:
                # 构造请求URL参数
                params = {
                    "aid": "497858",
                    "device_id": self.device_id,
                    "device_platform": "web",
                    "language": "zh",
                    "pc_version": "2.16.4",  # 更新版本
                    "pkg_type": "release_version",
                    "real_aid": "497858",
                    "region": "CN",
                    "samantha_web": "1",
                    "sys_region": "CN",
                    "tea_uuid": self.tea_uuid,
                    "use-olympus-account": "1",
                    "version_code": "20800",
                    "web_id": self.web_id
                }

                # 创建随机的会话ID和消息ID - 每次请求都使用新的会话ID
                conversation_id = f"38{int(time.time() * 10000)}8"
                section_id = f"{conversation_id[:10]}4{conversation_id[10:]}"
                message_id = f"ba0d6520-{uuid.uuid4().hex[:4]}-11f0-a188-0d4d{uuid.uuid4().hex[:8]}"

                # 构造视频搜索查询
                search_query = f"搜索抖音视频:{query}"

                # 记录当前视频查询关键词用于去重
                self._current_video_query = query

                # 构造请求数据
                request_data = {
                    "messages": [{
                        "content": json.dumps({
                            "text": search_query
                        }),
                        "content_type": 2001,
                        "attachments": [],
                        "references": []
                    }],
                    "completion_option": {
                        "is_regen": False,
                        "with_suggest": True,
                        "need_create_conversation": True,  # 总是创建新会话
                        "launch_stage": 1,
                        "is_replace": False,
                        "is_delete": False,
                        "message_from": 0,
                        "use_deep_think": False,
                        "use_auto_cot": False,
                        "event_id": "0"
                    },
                    "section_id": section_id,
                    "conversation_id": conversation_id,
                    "local_message_id": message_id
                }

                # 获取httpx客户端
                client = await self.get_session()

                # 设置请求头
                headers = self._generate_headers()
                headers['Content-Type'] = 'application/json; charset=utf-8'

                # 发送异步请求
                try:
                    # 构造完整URL
                    url = f"{self.api_base_url}?" + "&".join([f"{k}={quote(str(v))}" for k,v in params.items()])

                    # 发送POST请求
                    response = await client.post(
                        url,
                        json=request_data,
                        headers=headers,
                        timeout=httpx.Timeout(connect=10.0, read=300.0, write=60.0, pool=10.0)
                    )

                    # 检查状态码
                    if response.status_code != 200:
                        error_text = response.content
                        logger.error(f"[{self.plugin_name}] API请求失败: 状态码={response.status_code}, 响应内容={error_text}")
                        raise ValueError(f"API请求失败: {response.status_code}")

                    logger.debug(f"[{self.plugin_name}] API请求成功，开始处理响应流")

                    # 创建自定义响应对象
                    http_response = HttpResponse(
                        status_code=response.status_code,
                        body=response.content,
                        headers=dict(response.headers)
                    )

                    # 处理响应
                    result = await self._process_video_stream(http_response)

                    if result:
                        logger.info(f"[{self.plugin_name}] 视频搜索成功，结果类型: {result.get('type', 'unknown')}")
                        # 请求完成后删除会话，防止服务器端资源泄漏
                        if conversation_id:
                            asyncio.create_task(self._delete_conversation(conversation_id))
                        return result

                    # 如果没有结果，但还有重试机会，继续重试
                    if retry < max_retries - 1:
                        wait_time = min(base_delay * (2 ** retry) + random.uniform(0, 2), max_delay)
                        logger.warning(f"[{self.plugin_name}] 第{retry + 1}次请求无结果，{wait_time:.1f}秒后重试")
                        await asyncio.sleep(wait_time)
                        continue

                    # 如果是最后一次重试，返回空结果而不是抛出异常
                    logger.error(f"[{self.plugin_name}] 所有重试均无结果，返回空响应")
                    return {"type": "text", "text": ""}

                except httpx.RequestError as e:
                    raise ValueError(f"HTTP请求失败: {str(e)}")

            except ValueError as e:
                last_error = e
                logger.warning(f"[{self.plugin_name}] 第{retry + 1}次请求失败: {str(e)}")
                if retry < max_retries - 1:
                    wait_time = min(base_delay * (2 ** retry) + random.uniform(0, 2), max_delay)
                    logger.info(f"[{self.plugin_name}] {wait_time:.1f}秒后进行第{retry + 2}次重试")
                    await asyncio.sleep(wait_time)
                    continue
                else:
                    # 请求失败后也尝试删除会话，但返回空结果而不是抛出异常
                    logger.error(f"[{self.plugin_name}] 所有重试均失败，返回空响应")
                    if conversation_id:
                        asyncio.create_task(self._delete_conversation(conversation_id))
                    return {"type": "text", "text": ""}
            except Exception as e:
                last_error = e
                if retry < max_retries - 1:
                    wait_time = min(base_delay * (2 ** retry) + random.uniform(0, 2), max_delay)
                    await asyncio.sleep(wait_time)
                    continue
                else:
                    # 请求失败后也尝试删除会话
                    if conversation_id:
                        asyncio.create_task(self._delete_conversation(conversation_id))
                    raise

        # 如果所有重试都失败，抛出最后一个错误
        if last_error:
            raise last_error

        raise ValueError("达到最大重试次数，等会再试试")

    async def _delete_conversation(self, conversation_id: str) -> bool:
        """删除豆包会话

        Args:
            conversation_id: 会话ID

        Returns:
            bool: 删除是否成功
        """
        try:
            # 构造URL参数
            params = {
                "version_code": "20800",
                "language": "zh",
                "device_platform": "web",
                "aid": "497858",
                "real_aid": "497858",
                "pc_version": "2.16.1",
                "pkg_type": "release_version",
                "device_id": self.device_id,
                "web_id": self.web_id,
                "tea_uuid": self.tea_uuid,
                "use-olympus-account": "1",
                "region": "CN",
                "sys_region": "CN",
                "samantha_web": "1"
            }

            # 构造请求URL
            url = f"https://www.doubao.com/samantha/thread/delete?" + "&".join([f"{k}={quote(str(v))}" for k,v in params.items()])

            # 构造请求头
            headers = {
                "Accept": "application/json, text/plain, */*",
                "Content-Type": "application/json",
                "Origin": "https://www.doubao.com",
                "Referer": "https://www.doubao.com/chat/",
                "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36",
                "X-Requested-With": "mark.via",
                "Connection": "keep-alive",
                "Cookie": self.cookies
            }

            # 构造请求数据
            data = {"conversation_id": conversation_id}

            # 获取httpx客户端
            client = await self.get_session()

            # 发送异步请求
            try:
                response = await client.post(
                    url,
                    json=data,
                    headers=headers,
                    timeout=httpx.Timeout(connect=5.0, read=10.0, write=10.0, pool=5.0)
                )

                # 如果状态码为200，我们认为删除成功，即使响应为空
                if response.status_code == 200:
                    # 检查响应文本是否为空
                    text = response.text
                    if not text or text.isspace():
                        return True

                    # 尝试解析JSON
                    try:
                        resp_data = response.json()
                        if resp_data.get("code") == 0:
                            return True
                        else:
                            return False
                    except json.JSONDecodeError as e:
                        # 如果JSON解析失败，但状态码为200，仍视为成功
                        return True
                else:
                    return False

            except httpx.RequestError as e:
                return False

        except Exception as e:
            return False

    async def _process_video_stream(self, response: HttpResponse) -> Optional[Dict[str, Any]]:
        """处理视频搜索的SSE响应流

        Args:
            response: HTTP响应对象

        Returns:
            处理后的响应数据字典
        """
        result_data = {"type": "text", "text": ""}
        videos_data = []
        is_video_search = False  # 标记是否是视频搜索请求

        try:
            # 添加超时控制
            timeout = 300  # 设置300秒超时
            start_time = time.time()

            # 添加结束标志
            is_completed = False
            last_update_time = time.time()
            max_idle_time = 10  # 最大空闲时间（秒）

            # 解析SSE流 - 在httpx中需要一次性处理整个响应
            buffer = ""

            # 获取响应数据 - 确保是bytes类型
            if isinstance(response.body, str):
                chunk = response.body.encode('utf-8')
            else:
                chunk = response.body

            try:
                # 安全解码，忽略错误的字节
                try:
                    decoded_chunk = chunk.decode('utf-8', errors='ignore')
                    buffer = decoded_chunk
                except UnicodeDecodeError as e:
                    # 跳过无法解码的数据，继续处理
                    return None

                # 处理完整的SSE事件
                while "\n\n" in buffer:
                    parts = buffer.split("\n\n", 1)
                    event = parts[0]
                    buffer = parts[1]

                    # 跳过空事件
                    if not event.strip():
                        continue

                    # 提取"data:"行
                    data_line = None
                    for line in event.split("\n"):
                        if line.startswith("data:"):
                            data_line = line[5:].strip()
                            break

                    if not data_line:
                        continue

                    # 解析数据
                    try:
                        event_data = json.loads(data_line)
                        if not isinstance(event_data, dict):
                            continue

                        # 处理错误消息
                        if "event_type" not in event_data:
                            continue

                        event_type = event_data["event_type"]

                        # 处理结束事件
                        if event_type == 2003:  # 结束事件
                            is_completed = True

                            # 如果有视频数据，优先返回视频结果
                            if videos_data:
                                result_data = {
                                    "type": "videos",
                                    "videos": videos_data
                                }
                                return result_data

                            # 否则返回文本结果
                            return result_data

                        # 错误事件
                        if event_type == 2005:
                            try:
                                error_data = json.loads(event_data["event_data"])
                                # 记录错误，但不做特殊处理
                                pass
                            except Exception:
                                pass

                        # 正常事件 - seed_intention type
                        if event_type == 2010 and "event_data" in event_data:
                            try:
                                inner_data = json.loads(event_data["event_data"])
                                if "type" in inner_data and inner_data["type"] == "seed_intention":
                                    seed_info = inner_data.get("seed_intention", {})
                                    if seed_info.get("intention") == "browsing" and seed_info.get("detail") == "rich_media_only_video":
                                        is_video_search = True
                            except Exception as e:
                                continue

                        # 正常事件
                        if event_type == 2001 and "event_data" in event_data:  # 消息事件
                            try:
                                inner_data = json.loads(event_data["event_data"])

                                if "message" not in inner_data:
                                    continue

                                message = inner_data["message"]
                                is_finish = inner_data.get("is_finish", False)

                                if "content_type" not in message or "content" not in message:
                                    continue

                                content_type = message["content_type"]
                                try:
                                    content = json.loads(message["content"])
                                except json.JSONDecodeError:
                                    continue

                                # 处理视频搜索内容
                                if content_type == 2007:  # 视频搜索内容
                                    # 处理视频卡片数据
                                    if "search_result" in content and "video_card" in content["search_result"]:
                                        video_card = content["search_result"]["video_card"]
                                        if "card_list" in video_card:
                                            for video_item in video_card["card_list"]:
                                                # 提取视频信息
                                                video_info = {
                                                    "item_id": video_item.get("item_id", ""),
                                                    "title": video_item.get("video_captions", "未知标题"),
                                                    "url": video_item.get("main_site_url", ""),
                                                    "thumb_url": video_item.get("video_first_frame_image", video_item.get("album_image", "")),
                                                    "source": video_item.get("source_app_name", "抖音"),
                                                    "description": f"来源: {video_item.get('source_app_name', '抖音')}"
                                                }

                                                # 避免重复添加
                                                if video_info not in videos_data:
                                                    videos_data.append(video_info)

                                            # 标记为视频类型响应
                                            if videos_data:
                                                result_data = {
                                                    "type": "videos",
                                                    "videos": videos_data
                                                }

                                            # 如果是完成状态，立即返回结果
                                            if is_finish and videos_data:
                                                return result_data

                            except Exception as e:
                                continue

                        # 更新最后活动时间
                        last_update_time = time.time()

                    except json.JSONDecodeError as e:
                        continue
                    except Exception as e:
                        continue

                    # 检查是否超时
                    if time.time() - start_time > timeout:
                        if videos_data:
                            result_data = {
                                "type": "videos",
                                "videos": videos_data
                            }
                            return result_data
                        break

                    # 检查是否长时间没有新内容
                    if time.time() - last_update_time > max_idle_time:
                        if videos_data:
                            result_data = {
                                "type": "videos",
                                "videos": videos_data
                            }
                            return result_data

            except Exception as e:
                # 继续处理，尝试返回已收集的数据
                pass

            # 如果有视频数据，优先返回视频数据
            if videos_data:
                result_data = {
                    "type": "videos",
                    "videos": videos_data
                }
                return result_data

            # 如果是视频搜索但没有获取到视频数据
            if is_video_search and not videos_data:
                result_data = {
                    "type": "text",
                    "text": "视频搜索失败，请稍后重试"
                }
                return result_data

            # 修改响应处理逻辑，避免误判为失败
            if is_completed:
                # 即使没有内容也返回空字典，避免被误判为失败
                return {"type": "text", "text": ""}

            # 实在没有任何有效内容，返回None
            # 修改这里：返回空文本结果而不是None
            result_data["text"] = ""
            return result_data

        except Exception as e:
            # 如果已经有部分结果，尽可能返回
            if videos_data:
                result_data = {
                    "type": "videos",
                    "videos": videos_data
                }
                return result_data
            return None

    async def handle_video_message(self, bot: WechatAPIClient, message: dict, result: Dict[str, Any], query: str = ""):
        """处理视频消息

        Args:
            bot: 微信API客户端
            message: 消息对象
            result: 搜索结果
            query: 搜索关键词
        """
        try:
            if result["type"] == "videos":
                # 处理视频消息
                videos = result["videos"]
                logger.info(f"[{self.plugin_name}] 收到{len(videos)}个视频结果")
                if not videos:
                    logger.warning(f"[{self.plugin_name}] 视频列表为空")
                    return

                # 提取查询关键词用于去重
                query_key = query if query else "unknown"
                if hasattr(self, '_current_video_query'):
                    query_key = self._current_video_query

                logger.debug(f"[{self.plugin_name}] 使用查询关键词进行去重: {query_key}")

                # 获取一个未发送过的视频
                selected_video = await self._get_unique_video(videos, query_key, message["FromWxid"])
                if not selected_video:
                    logger.warning(f"[{self.plugin_name}] 未找到可发送的视频")
                    return

                url = selected_video.get("url")
                if not url:
                    logger.error(f"[{self.plugin_name}] 选中的视频缺少URL")
                    return

                logger.info(f"[{self.plugin_name}] 开始解析视频链接: {url}")

                # 解析视频链接
                video_info = await self.parse_douyin_video(url)
                if not video_info:
                    # 如果解析失败，使用原始信息发送XML视频分享消息
                    logger.warning(f"[{self.plugin_name}] 视频解析失败，使用原始信息发送")
                    await self._send_video_share_message(
                        bot=bot,
                        wxid=message["FromWxid"],
                        title=selected_video.get("title", "抖音视频"),
                        description=selected_video.get("description", "来源: 抖音"),
                        video_url=url,
                        thumb_url=selected_video.get("thumb_url", "")
                    )
                    return

                logger.info(f"[{self.plugin_name}] 视频解析成功: {video_info.get('song_name', '未知标题')}")

                # 使用解析后的信息发送XML视频分享消息
                title = video_info.get('song_name', '抖音视频')
                description = f"作者: {video_info.get('artist_name', '抖音用户')}"

                # 直接使用解析后的无水印视频URL
                video_url = video_info['media_url']

                # 发送解析后的视频分享消息
                await self._send_video_share_message(
                    bot=bot,
                    wxid=message["FromWxid"],
                    title=title,
                    description=description,
                    video_url=video_url,
                    thumb_url=video_info.get('img_url', '')
                )
            else:
                # 如果不是视频类型，发送错误消息
                if self.natural_response:
                    error_msg = random.choice(self.error_msgs)
                    await bot.send_text_message(message["FromWxid"], error_msg)
                else:
                    await bot.send_at_message(message["FromWxid"], "没有找到相关视频", [message["SenderWxid"]])

        except Exception as e:
            if self.natural_response:
                error_msg = random.choice(self.error_msgs)
                await bot.send_text_message(message["FromWxid"], error_msg)
            else:
                await bot.send_at_message(message["FromWxid"], "处理不了，等会再试试", [message["SenderWxid"]])

    async def _send_video_share_message(self, bot: WechatAPIClient, wxid: str, title: str, description: str, video_url: str, thumb_url: str = ""):
        """发送视频分享消息"""
        try:
            # 参考音乐消息格式的视频分享消息
            xml = f'''<appmsg appid="wx75f04c8595ccb9f6" sdkver="0">
<title>{title}</title>
<des>{description}</des>
<action>view</action>
<type>68</type>
<showtype>0</showtype>
<content/>
<url>{video_url}</url>
<dataurl/>
<lowurl>https://game.weixin.qq.com/</lowurl>
<lowdataurl/>
<recorditem/>
<thumburl>{thumb_url}</thumburl>
<messageaction/>
<laninfo/>
<extinfo/>
<sourceusername/>
<sourcedisplayname/>
<appattach>
    <totallen>0</totallen>
    <attachid/>
    <emoticonmd5/>
    <fileext/>
    <aeskey/>
</appattach>
<webviewshared>
    <publisherId/>
    <publisherReqId>0</publisherReqId>
</webviewshared>
<weappinfo>
    <pagepath/>
    <username/>
    <appid/>
    <appservicetype>0</appservicetype>
</weappinfo>
<websearch/>
</appmsg>
<fromusername>{bot.wxid}</fromusername>
<scene>0</scene>
<appinfo>
    <version>1</version>
    <appname></appname>
</appinfo>
<commenturl/>'''

            client_msg_id, _, new_msg_id = await bot.send_app_message(
                wxid,
                xml,
                68  # 视频分享消息类型
            )

            if new_msg_id == 0 or not client_msg_id:
                logger.error(f"[{self.plugin_name}] 视频分享消息发送失败")

        except Exception as e:
            logger.error(f"[{self.plugin_name}] 视频分享消息发送异常: {str(e)}")

    async def parse_douyin_video(self, video_url: str) -> Optional[dict]:
        """解析抖音视频链接

        Args:
            video_url: 抖音分享链接

        Returns:
            解析后的视频信息字典，包含以下字段：
            - media_url: 视频播放地址
            - artist_name: 作者名称
            - song_name: 视频标题
            - img_url: 视频封面
        """
        logger.debug(f"[{self.plugin_name}] 开始解析抖音视频: {video_url}")
        try:
            # 构造解析API URL（使用苏苏API，对域名进行编码处理）
            # 将中文域名转换为punycode编码
            try:
                # 尝试使用idna编码中文域名
                encoded_domain = idna.encode("api.苏苏.cn").decode('ascii')
                parse_url = f"http://{encoded_domain}/API/douyin.php?url={quote(video_url)}"
            except Exception:
                # 如果idna编码失败，使用IP地址或其他方式
                # 这里可以替换为实际的IP地址或其他可用域名
                parse_url = f"http://api.xn--9kr62g.cn/API/douyin.php?url={quote(video_url)}"

            # 构造请求头
            headers = {
                "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36",
                "Accept": "application/json, text/plain, */*",
                "Accept-Language": "zh-CN,zh;q=0.9,en;q=0.8",
                "Accept-Encoding": "gzip, deflate",
                "Connection": "keep-alive",
                "Referer": "http://api.xn--9kr62g.cn/",
                "Origin": "http://api.xn--9kr62g.cn"
            }

            # 获取httpx客户端
            client = await self.get_session()

            # 发送异步请求
            try:
                response = await client.get(
                    parse_url,
                    headers=headers,
                    timeout=httpx.Timeout(connect=10.0, read=30.0, write=20.0, pool=5.0)
                )

                if response.status_code != 200:
                    logger.warning(f"[{self.plugin_name}] 视频解析API返回错误状态码: {response.status_code}")
                    return None

                # 解析响应
                try:
                    result = response.json()

                    # 检查响应格式和状态码
                    if not isinstance(result, dict):
                        logger.warning(f"[{self.plugin_name}] 视频解析API返回格式错误")
                        return None

                    # 检查API返回的状态码
                    if result.get("code") != 200:
                        logger.warning(f"[{self.plugin_name}] 视频解析API返回错误代码: {result.get('code')}")
                        return None

                    # 提取data字段中的视频信息
                    data = result.get("data", {})
                    if not data:
                        logger.warning(f"[{self.plugin_name}] 视频解析API返回空数据")
                        return None

                    # 提取视频信息（适配苏苏API响应格式）
                    video_info = {
                        "media_url": data.get("url", ""),
                        "artist_name": data.get("author", "抖音用户"),  # 苏苏API提供作者信息
                        "song_name": data.get("title", "未知标题"),
                        "img_url": data.get("cover", "")
                    }

                    # 验证必要字段
                    if not video_info["media_url"]:
                        logger.warning(f"[{self.plugin_name}] 解析结果缺少视频URL")
                        return None

                    logger.info(f"[{self.plugin_name}] 视频解析成功: {video_info['song_name']} by {video_info['artist_name']}")
                    return video_info

                except json.JSONDecodeError as e:
                    return None

            except httpx.RequestError as e:
                return None

        except Exception as e:
            return None

    @on_text_message
    async def handle_text(self, bot: WechatAPIClient, message: dict):
        """处理文本消息"""
        if not self.enable:
            return

        start_time = time.time()  # 性能监控开始时间
        content = str(message["Content"]).strip()
        wxid = message["FromWxid"]
        user_wxid = message["SenderWxid"]

        # 检查是否是视频搜索命令或管理员命令
        command = content.split()
        is_video_command = command[0] in self.command if command else False
        is_admin_command = command[0] in self.admin_command if command else False

        if not is_video_command and not is_admin_command:
            return

        # 处理管理员命令
        if is_admin_command:
            await self._handle_admin_command(bot, message, command)
            return

        logger.info(f"[{self.plugin_name}] 收到视频搜索请求: {content} from {user_wxid}")

        # 如果是视频搜索命令但没有参数
        if is_video_command and len(command) == 1:
            await bot.send_at_message(
                message["FromWxid"],
                f"{self.command_format}",
                [message["SenderWxid"]]
            )
            return

        try:
            # 检查用户请求限制
            wait_time = self._check_user_limit(wxid, user_wxid)
            if wait_time > 0:
                if self.natural_response:
                    rate_limit_msg = random.choice(self.rate_limit_msgs)
                    await bot.send_text_message(wxid, rate_limit_msg)
                else:
                    await bot.send_at_message(wxid, f"慢点慢点，等 {wait_time:.1f} 秒再来", [user_wxid])
                return

            # 检查令牌桶
            if not await self._acquire_token():
                if self.natural_response:
                    rate_limit_msg = random.choice(self.rate_limit_msgs)
                    await bot.send_text_message(wxid, rate_limit_msg)
                else:
                    await bot.send_at_message(wxid, "忙不过来了，歇会儿", [user_wxid])
                return

            # 提取搜索关键词
            query = " ".join(command[1:]).strip()
            if not query:
                await bot.send_at_message(
                    message["FromWxid"],
                    "请输入要搜索的视频关键词",
                    [message["SenderWxid"]]
                )
                return

            # 调用视频搜索API
            result = await self.call_doubao_video_search_api(bot, message, query)

            if not result:
                if self.natural_response:
                    error_msg = random.choice(self.error_msgs)
                    await bot.send_text_message(message["FromWxid"], error_msg)
                else:
                    await bot.send_at_message(message["FromWxid"], "❌ 视频搜索失败，请稍后重试", [message["SenderWxid"]])
                return

            await self.handle_video_message(bot, message, result, query)

            # 记录处理完成时间和性能指标
            end_time = time.time()
            processing_time = end_time - start_time
            logger.info(f"[{self.plugin_name}] 视频搜索处理完成，耗时: {processing_time:.2f}秒")

            # 性能监控已移除

        except Exception as e:
            end_time = time.time()
            processing_time = end_time - start_time
            logger.error(f"[{self.plugin_name}] 视频搜索处理失败，耗时: {processing_time:.2f}秒，错误: {str(e)}")

            # 性能监控已移除

            if self.natural_response:
                error_msg = random.choice(self.error_msgs)
                await bot.send_text_message(message["FromWxid"], error_msg)
            else:
                await bot.send_at_message(message["FromWxid"], "出问题了，等会再试试", [message["SenderWxid"]])

    async def on_disable(self):
        """插件禁用时调用"""
        await self.close_session()

    async def get_session(self):
        """获取或创建httpx异步客户端"""
        async with self._client_lock:
            if self._client is None or self._client.is_closed:
                self._client = await get_httpx_client()
            return self._client

    async def close_session(self):
        """关闭httpx客户端"""
        async with self._client_lock:
            if self._client and not self._client.is_closed:
                await self._client.aclose()
                self._client = None

    def _generate_headers(self) -> dict:
        """生成请求头

        Returns:
            dict: 请求头字典
        """
        # 生成随机的x-flow-trace
        flow_trace = f"04-{uuid.uuid4().hex[:16]}-{uuid.uuid4().hex[:16]}-01"

        return {
            "Accept": "*/*",
            "Accept-Encoding": "gzip, deflate",
            "Accept-Language": "zh-CN,zh;q=0.9,en-US;q=0.8,en;q=0.7",
            "Connection": "keep-alive",
            "Content-Type": "application/json",
            "Cookie": self.cookies,
            "Host": "www.doubao.com",
            "Origin": "https://www.doubao.com",
            "Referer": "https://www.doubao.com/chat/",
            "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36",
            "x-flow-trace": flow_trace,  # 使用动态生成的值
            "Agw-Js-Conv": "str",
            "X-Requested-With": "mark.via",
            "last-event-id": "undefined",
            "Sec-Fetch-Site": "same-origin",
            "Sec-Fetch-Mode": "cors",
            "Sec-Fetch-Dest": "empty"
        }

    async def _handle_admin_command(self, bot: WechatAPIClient, message: dict, command: List[str]):
        """处理管理员命令

        Args:
            bot: 微信API客户端
            message: 消息对象
            command: 命令列表
        """
        if not PERFORMANCE_MONITORING_ENABLED:
            await bot.send_text_message(message["FromWxid"], "性能监控功能未启用")
            return

        cmd = command[0]

        if cmd in ["视频搜索状态", "视频搜索报告"]:
            try:
                # 生成性能报告
                report = await generate_performance_report()

                # 发送报告（分段发送，避免消息过长）
                lines = report.split('\n')
                current_message = []

                for line in lines:
                    current_message.append(line)

                    # 如果消息长度超过限制，发送当前消息并开始新消息
                    if len('\n'.join(current_message)) > 1500:
                        await bot.send_text_message(message["FromWxid"], '\n'.join(current_message))
                        current_message = []
                        await asyncio.sleep(0.5)  # 避免发送过快

                # 发送剩余消息
                if current_message:
                    await bot.send_text_message(message["FromWxid"], '\n'.join(current_message))

            except Exception as e:
                logger.error(f"[{self.plugin_name}] 生成性能报告失败: {str(e)}")
                await bot.send_text_message(message["FromWxid"], f"生成性能报告失败: {str(e)}")

        else:
            await bot.send_text_message(message["FromWxid"], "未知的管理员命令")
