# This file is dual licensed under the terms of the Apache License, Version
# 2.0, and the BSD License. See the LICENSE file in the root of this repository
# for complete details.

import typing

from cryptography.hazmat.primitives import padding

def check_ansix923_padding(data: bytes) -> bool: ...

class PKCS7PaddingContext(padding.PaddingContext):
    def __init__(self, block_size: int) -> None: ...
    def update(self, data: bytes) -> bytes: ...
    def finalize(self) -> bytes: ...

class PKCS7UnpaddingContext(padding.PaddingContext):
    def __init__(self, block_size: int) -> None: ...
    def update(self, data: bytes) -> bytes: ...
    def finalize(self) -> bytes: ...

class ObjectIdentifier:
    def __init__(self, val: str) -> None: ...
    @property
    def dotted_string(self) -> str: ...
    @property
    def _name(self) -> str: ...

T = typing.TypeVar("T")
