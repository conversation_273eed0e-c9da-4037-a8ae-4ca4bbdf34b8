# XYBot 插件开发指南

## 1. 插件开发必要条件

### 1.1 基本要求
1. **必须继承 PluginBase 类**
2. **必须实现以下类属性**：
   - `description`: 插件描述
   - `author`: 插件作者
   - `version`: 插件版本

### 1.2 必要的导入
```python
# 基础导入
import tomllib
import os
from loguru import logger

# 框架相关导入
from WechatAPI import WechatAPIClient
from utils.decorators import *  # 包含 @on_text_message 等装饰器
from utils.plugin_base import PluginBase

# 网络请求相关
import aiohttp
```

### 1.3 标准框架结构
```python
class YourPlugin(PluginBase):
    # 1. 必要的类属性
    description = "插件描述"
    author = "作者名"
    version = "1.0.0"

    def __init__(self):
        # 2. 必须调用父类初始化
        super().__init__()
        
        # 3. 配置文件读取
        try:
            with open("plugins/all_in_one_config.toml", "rb") as f:
                plugin_config = tomllib.load(f)
            config = plugin_config.get("YourPlugin", {})
        except Exception as e:
            logger.error(f"[{self.plugin_name}] 配置文件读取失败: {str(e)}")
            raise
        
        # 4. 基本配置初始化
        self.enable = config.get("enable", True)  # 启用状态
        self.command = config.get("command", ["命令1", "命令2"])  # 触发命令
        self.command_format = config.get("command-format", "使用方法说明")  # 命令格式
        
        # 5. session 管理（如果需要网络请求）
        self._session = None
        
        # 6. 其他必要资源初始化
        try:
            os.makedirs("temp", exist_ok=True)
        except Exception as e:
            logger.error(f"[{self.plugin_name}] 创建临时目录失败：{str(e)}")

    # 7. Session 管理方法
    async def get_session(self):
        """获取或创建 session"""
        if self._session is None:
            self._session = aiohttp.ClientSession(
                timeout=aiohttp.ClientTimeout(
                    total=300,     # 总超时时间
                    connect=30,    # 连接超时
                    sock_read=300  # 读取超时
                )
            )
        return self._session
        
    async def close_session(self):
        """关闭 session"""
        if self._session:
            await self._session.close()
            self._session = None

    # 8. 消息处理方法（必须使用装饰器）
    @on_text_message
    async def handle_text(self, bot: WechatAPIClient, message: dict):
        """处理文本消息"""
        # 8.1 检查插件是否启用
        if not self.enable:
            return
            
        # 8.2 获取消息内容
        content = str(message["Content"]).strip()
        command = content.split(" ", 1)  # 分割命令和参数
        
        # 8.3 检查是否是目标命令
        if command[0] not in self.command:
            return
            
        # 8.4 检查命令格式
        if len(command) == 1:
            await bot.send_at_message(
                message["FromWxid"],
                f"❌命令格式错误！{self.command_format}",
                [message["SenderWxid"]]
            )
            return
            
        # 8.5 处理业务逻辑
        try:
            # 获取参数
            params = command[1].strip()
            
            # 执行操作
            result = await self._process_command(bot, message, params)
            
            # 发送响应
            if result:
                await self._send_response(bot, message, result)
                
        except aiohttp.ClientError as e:
            logger.error(f"网络请求错误: {e}")
            await bot.send_at_message(
                message["FromWxid"],
                "❌网络请求失败，请稍后重试",
                [message["SenderWxid"]]
            )
        except Exception as e:
            logger.error(f"处理失败: {str(e)}")
            logger.error(traceback.format_exc())
            await bot.send_at_message(
                message["FromWxid"],
                "❌处理失败，请稍后重试",
                [message["SenderWxid"]]
            )

    # 9. 资源清理（必须实现）
    async def on_disable(self):
        """插件禁用时调用"""
        await self.close_session()

### 1.7 高级功能实现

1. **数据库支持**
```python
# 1. 数据库初始化
def __init__(self):
    super().__init__()
    self.db = BotDatabase()  # 机器人数据库
    self.sqlite_conn = None  # SQLite连接
    self.inited = False     # 初始化标志

# 2. 异步初始化
async def async_init(self):
    """异步初始化数据库连接"""
    try:
        if self.sqlite_conn:
            try:
                async with self.sqlite_conn.cursor() as cursor:
                    await cursor.execute("SELECT 1")
                return  # 连接有效，直接返回
            except Exception:
                # 连接无效，重新初始化
                if self.sqlite_conn:
                    await self.sqlite_conn.close()
                    
        # 创建新连接
        self.sqlite_conn = await aiosqlite.connect(self.db_url)
        self.inited = True
        logger.info(f"[{self.plugin_name}] 数据库初始化完成")
    except Exception as e:
        logger.error(f"数据库初始化失败: {str(e)}")
        raise

# 3. 资源清理
def __del__(self):
    """确保资源被正确释放"""
    try:
        if hasattr(self, 'sqlite_conn') and self.sqlite_conn:
            asyncio.run(self.sqlite_conn.close())
    except Exception as e:
        logger.error(f"关闭数据库连接时出错: {str(e)}")
```

2. **定时任务支持**
```python
# 使用schedule装饰器实现定时任务
@schedule('cron', hour=5)  # 每天5点执行
async def daily_task(self):
    """每日定时任务"""
    try:
        # 执行定时任务逻辑
        await self.some_task()
        logger.success(f"[{self.plugin_name}] 定时任务执行成功")
    except Exception as e:
        logger.error(f"定时任务执行失败: {str(e)}")
```

3. **多消息类型处理**
```python
# 1. 文本消息处理
@on_text_message
async def handle_text(self, bot: WechatAPIClient, message: dict):
    if not self.enable:
        return
    # 处理文本消息...

# 2. @消息处理
@on_at_message
async def handle_at(self, bot: WechatAPIClient, message: dict):
    if not self.enable:
        return
    # 处理@消息...

# 3. 语音消息处理
@on_voice_message
async def handle_voice(self, bot: WechatAPIClient, message: dict):
    if not self.enable:
        return
    # 处理语音消息...

# 4. 图片消息处理
@on_image_message
async def handle_image(self, bot: WechatAPIClient, message: dict):
    if not self.enable:
        return
    # 处理图片消息...
```

4. **配置文件多层级支持**
```toml
# plugins/all_in_one_config.toml

[YourPlugin]
enable = true
command = ["命令1", "命令2"]

[YourPlugin.SubModule1]
option1 = "value1"
option2 = 123

[YourPlugin.SubModule2]
setting1 = true
setting2 = ["item1", "item2"]
```

```python
def __init__(self):
    super().__init__()
    
    # 读取主配置和子模块配置
    with open("plugins/all_in_one_config.toml", "rb") as f:
        plugin_config = tomllib.load(f)
    
    # 主配置
    config = plugin_config.get("YourPlugin", {})
    self.enable = config.get("enable", True)
    
    # 子模块1配置
    sub1_config = plugin_config["YourPlugin"]["SubModule1"]
    self.option1 = sub1_config.get("option1")
    
    # 子模块2配置
    sub2_config = plugin_config["YourPlugin"]["SubModule2"]
    self.setting1 = sub2_config.get("setting1")
```

5. **权限控制**
```python
async def check_permission(self, bot: WechatAPIClient, message: dict) -> bool:
    """检查用户权限"""
    wxid = message["SenderWxid"]
    
    # 管理员权限
    if wxid in self.admins:
        return True
        
    # 白名单权限
    if self.db.get_whitelist(wxid):
        return True
        
    # 积分检查
    if self.point_mode == "Together":
        if self.db.get_points(wxid) < self.required_points:
            await bot.send_at_message(
                message["FromWxid"],
                f"积分不足，需要 {self.required_points} 积分",
                [wxid]
            )
            return False
            
    return True
```

6. **异常恢复机制**
```python
class YourPlugin(PluginBase):
    def __init__(self):
        super().__init__()
        self.retry_count = 0
        self.max_retries = 3
        
    async def safe_operation(self, func, *args, **kwargs):
        """带重试的安全操作"""
        while self.retry_count < self.max_retries:
            try:
                result = await func(*args, **kwargs)
                self.retry_count = 0  # 重置计数
                return result
            except aiohttp.ClientError as e:
                self.retry_count += 1
                logger.warning(f"操作失败，第 {self.retry_count} 次重试")
                if self.retry_count >= self.max_retries:
                    raise
                await asyncio.sleep(1)  # 重试前等待
```

7. **资源管理最佳实践**
```python
class YourPlugin(PluginBase):
    def __init__(self):
        super().__init__()
        self._resources = {}
        self._locks = {}
        
    async def get_resource(self, key: str):
        """获取资源（带缓存）"""
        if key not in self._resources:
            async with self._get_lock(key):
                if key not in self._resources:  # 双重检查
                    self._resources[key] = await self._load_resource(key)
        return self._resources[key]
        
    def _get_lock(self, key: str):
        """获取资源锁"""
        if key not in self._locks:
            self._locks[key] = asyncio.Lock()
        return self._locks[key]
        
    async def _load_resource(self, key: str):
        """加载资源"""
        # 实现资源加载逻辑
        pass
        
    async def on_disable(self):
        """清理资源"""
        for key, resource in self._resources.items():
            await self._cleanup_resource(resource)
        self._resources.clear()
        self._locks.clear()
```

8. **插件间通信机制**
```python
class YourPlugin(PluginBase):
    def __init__(self):
        super().__init__()
        # 获取其他插件的命令列表
        self.other_commands = []
        for plugin in plugin_config:
            if plugin != self.plugin_name:
                self.other_commands.extend(
                    plugin_config[plugin].get("command", [])
                )
                
    async def handle_text(self, bot: WechatAPIClient, message: dict):
        # 避免命令冲突
        if command[0] in self.other_commands:
            return
```

### 1.8 插件开发注意事项

1. **初始化顺序**
   - 必须先调用父类初始化
   - 配置读取在前，资源初始化在后
   - 异步资源初始化要在首次使用时进行

2. **资源管理**
   - 实现 `__del__` 方法确保资源释放
   - 使用 `on_disable` 进行异步资源清理
   - 使用 `try/finally` 确保临时资源清理

3. **错误处理**
   - 所有外部调用都要进行异常捕获
   - 网络请求要单独处理超时异常
   - 数据库操作要处理连接异常

4. **性能优化**
   - 使用连接池复用连接
   - 实现资源缓存机制
   - 避免重复初始化

5. **配置管理**
   - 所有配置项都要提供默认值
   - 配置读取失败要优雅降级
   - 关键配置要做类型检查

6. **消息处理**
   - 检查插件是否启用
   - 检查消息类型是否支持
   - 检查用户权限

7. **日志记录**
   - 记录关键操作日志
   - 记录异常详细信息
   - 使用适当的日志级别

8. **安全考虑**
   - 检查用户输入
   - 控制资源使用
   - 保护敏感信息

## 2. 网络请求规范
```python
# 1. 获取session（标准超时配置）
async def get_session(self):
    if self._session is None:
        self._session = aiohttp.ClientSession(
            timeout=aiohttp.ClientTimeout(
                total=300,     # 总超时时间（适用于下载大文件）
                connect=30,    # 连接超时
                sock_read=300  # 读取超时
            )
        )
    return self._session

# 2. 发起请求
async with session.get(url, params=params) as response:
    if response.status == 200:
        data = await response.json()  # JSON响应
        # 或
        content = await response.read()  # 二进制内容

# 3. 针对特定请求的超时控制
try:
    async with asyncio.timeout(30):  # 短超时用于API调用
        async with session.get(api_url) as response:
            data = await response.json()
except asyncio.TimeoutError:
    logger.error("API请求超时")
```

## 3. 文件处理规范

### 3.1 临时文件管理
```python
temp_path = f"temp/file_{int(time.time())}.ext"
try:
    # 使用临时文件
    ...
finally:
    # 清理临时文件
    if os.path.exists(temp_path):
        os.remove(temp_path)
```

### 3.2 文件下载
```python
async with session.get(url) as response:
    if response.status == 200:
        content = await response.read()
        with open(temp_path, 'wb') as f:
            f.write(content)
```

## 4. 消息发送规范

### 4.1 文本消息
```python
# 普通文本
await bot.send_text_message(wxid, content)

# @消息
await bot.send_at_message(wxid, content, [sender_wxid])
```

### 4.2 图片消息
```python
# 发送本地图片
await bot.send_image_message(wxid, image_path=image_path)

# 发送base64图片
await bot.send_image_message(wxid, image_base64=image_base64)
```

### 4.3 语音消息
```python
# 发送本地语音文件
await bot.send_voice_message(
    wxid, 
    voice_path=voice_path,
    format='mp3'  # 或 'wav', 'amr'
)

# 发送base64语音
await bot.send_voice_message(
    wxid,
    voice_base64=voice_base64,
    format='wav'
)
```

### 4.4 视频消息
```python
await bot.send_video_message(wxid, video_path=video_path)
```

### 4.5 文件消息
```python
await bot.send_cdn_file_msg(wxid, file_path=file_path)
```

## 5. 错误处理规范
```python
try:
    # 业务逻辑
except aiohttp.ClientError as e:
    logger.error(f"网络请求错误: {e}")
    await bot.send_text_message(wxid, "网络请求失败，请稍后重试")
except Exception as e:
    logger.error(f"发生错误: {str(e)}")
    logger.error(traceback.format_exc())
    await bot.send_text_message(wxid, "处理失败，请稍后重试")
```

## 6. 异步操作规范

### 6.1 并发控制
```python
# 限制并发数
semaphore = asyncio.Semaphore(5)
async with semaphore:
    # 限制并发的操作
```

### 6.2 超时控制
```python
try:
    async with asyncio.timeout(30):  # 30秒超时
        # 可能超时的操作
except asyncio.TimeoutError:
    logger.error("操作超时")
```

## 7. 配置文件规范

### 7.1 基础配置结构
```toml
[YourPlugin]
# 1. 基础配置（必需）
enable = true                    # 插件启用开关
command = ["命令1", "命令2"]     # 触发命令列表
command-format = """            # 命令使用说明（建议使用"""支持多行）
⚙️插件使用说明：
命令1 参数1 参数2
命令2 参数3
"""

# 2. 其他配置项...
```

### 7.2 常见配置模式

1. **简单模式**
```toml
[SimplePlugin]
enable = true
command = ["简单命令"]
```

2. **API模式**
```toml
[APIPlugin]
enable = true
command = ["api命令"]
api-key = "your-api-key"        # API密钥
base-url = "https://api.example.com"  # API基础URL
```

3. **分级权限模式**
```toml
[PermissionPlugin]
enable = true
command = ["权限命令"]

[PermissionPlugin.Point]
mode = "Together"               # 积分模式：None不扣积分，Together统一扣除
price = 5                      # Together模式下每次扣除积分
admin-ignore = true            # 管理员是否忽略扣除
whitelist-ignore = true        # 白名单是否忽略扣除
```

4. **多级命令模式**
```toml
[MultiCommandPlugin]
enable = true
command = ["主命令"]
create-commands = ["创建", "新建"]
delete-commands = ["删除", "移除"]
query-commands = ["查询", "搜索"]
```

5. **群组特定配置模式**
```toml
[GroupPlugin]
enable = true
command = ["群命令"]

# 群组特定设置
[GroupPlugin.groups]
"12345678@chatroom" = { enable = true, message = "群组1特定消息" }
"87654321@chatroom" = { enable = false, message = "群组2特定消息" }
```

6. **定时任务模式**
```toml
[TimerPlugin]
enable = true
schedule = "0 0 * * *"         # cron表达式
timeout = 60                   # 超时时间(秒)
retry-count = 3               # 重试次数
```

7. **资源限制模式**
```toml
[ResourcePlugin]
enable = true
command = ["资源命令"]
max-size = 10485760          # 最大文件大小(字节)
allowed-types = ["jpg", "png", "gif"]  # 允许的文件类型
max-duration = 300           # 最大处理时长(秒)
```

8. **AI/模型配置模式**
```toml
[AIPlugin]
enable = true
command = ["ai命令"]

[AIPlugin.MainModel]
base-url = ""                # API地址
api-key = ""                # API密钥
model-name = ""             # 模型名称
temperature = 0.7           # 温度参数
max-history = 5             # 最大历史消息数
model_kwargs = { }          # 模型其他参数

[AIPlugin.SubModel]
enable = true
model-name = ""
parameters = { }
```

### 7.3 配置文件最佳实践

1. **命名规范**
   - 插件配置节使用 PascalCase 命名法（如 `[YourPlugin]`）
   - 配置项使用 kebab-case 命名法（如 `api-key`）
   - 命令列表使用小写（如 `command = ["命令1"]`）

2. **文档化**
   - 使用注释说明配置项的用途
   - 提供配置项的默认值
   - 说明配置项的格式要求

3. **类型规范**
   ```toml
   # 布尔值
   enable = true/false
   
   # 字符串
   api-key = "string"
   
   # 数字
   timeout = 60
   
   # 数组
   command = ["item1", "item2"]
   
   # 表格数组
   [[arrays]]
   name = "name1"
   value = "value1"
   
   # 嵌套表格
   [parent.child]
   key = "value"
   ```

4. **默认值处理**
```python
def __init__(self):
    super().__init__()
    config = plugin_config.get("YourPlugin", {})
    
    # 基础配置
    self.enable = config.get("enable", True)
    self.command = config.get("command", ["默认命令"])
    self.command_format = config.get("command-format", "默认格式说明")
    
    # API配置
    api_config = config.get("API", {})
    self.api_key = api_config.get("api-key", "")
    self.base_url = api_config.get("base-url", "https://default.api.com")
    
    # 高级配置
    advanced = config.get("Advanced", {})
    self.timeout = advanced.get("timeout", 60)
    self.max_retries = advanced.get("max-retries", 3)
```

5. **配置验证**
```python
def validate_config(self):
    """验证配置有效性"""
    if not self.api_key:
        raise ValueError("API密钥不能为空")
        
    if not isinstance(self.timeout, int) or self.timeout <= 0:
        raise ValueError("超时时间必须是正整数")
        
    if not all(isinstance(cmd, str) for cmd in self.command):
        raise ValueError("命令必须是字符串列表")
```

6. **敏感信息处理**
```toml
# 使用环境变量或外部文件存储敏感信息
[SecurePlugin]
api-key = "${API_KEY}"        # 从环境变量读取
credentials-file = "creds.json"  # 从文件读取
```

7. **版本兼容**
```toml
[VersionedPlugin]
version = "1.0.0"            # 配置文件版本
min-bot-version = "2.0.0"    # 最低机器人版本要求
```

8. **分组配置**
```toml
[GroupedPlugin]
enable = true

# 功能分组
[GroupedPlugin.Feature1]
enable = true
options = { }

[GroupedPlugin.Feature2]
enable = false
options = { }

# 权限分组
[GroupedPlugin.Permissions]
admin = ["command1", "command2"]
user = ["command3"]
```

### 7.4 配置文件检查清单

1. **基础检查**
   - [ ] 配置节名称是否正确
   - [ ] 必需的配置项是否存在
   - [ ] 配置值的类型是否正确

2. **功能检查**
   - [ ] 命令列表是否有效
   - [ ] API配置是否完整
   - [ ] 权限设置是否合理

3. **安全检查**
   - [ ] 敏感信息是否安全存储
   - [ ] 权限控制是否完善
   - [ ] 资源限制是否合理

4. **兼容性检查**
   - [ ] 配置格式是否兼容
   - [ ] 版本要求是否满足
   - [ ] 依赖配置是否完整

## 8. 日志规范
```python
# 错误日志
logger.error(f"发生错误: {str(e)}")
logger.error(traceback.format_exc())

# 信息日志
logger.info(f"处理成功: {result}")

# 调试日志
logger.debug(f"调试信息: {debug_info}")
```

## 9. 文本处理规范

### 9.1 文本转图片服务

XYBot提供了统一的文本转图片服务类`TextCardService`，插件开发时应优先使用此服务进行文本转图片处理。

```python
from text2card_project.text_card_service import TextCardService

class YourPlugin(PluginBase):
    def __init__(self):
        super().__init__()
        self.text_card = TextCardService()
        
    async def handle_text(self, bot: WechatAPIClient, message: dict):
        # 1. 基础使用
        image = await self.text_card.generate_card(
            text="要转换的文本内容",
            width=720,
            is_dark=False
        )
        
        # 2. 保存到文件
        output_path = "temp/card.png"
        await self.text_card.generate_card(
            text="要转换的文本内容",
            output_path=output_path
        )
        
        # 3. 带头部图片的卡片
        image = await self.text_card.generate_card_with_header(
            text="要转换的文本内容",
            header_image_url="https://example.com/image.jpg",
            enhance_header=True
        )
        
        # 4. 快速生成简单卡片
        image = await self.text_card.generate_simple_card(
            text="要转换的文本内容",
            theme='light'  # 或 'dark'
        )
```

#### 主要特性

1. **多种生成方式**
   - 基础文本卡片
   - 带头部图片的卡片
   - 简单快速卡片

2. **格式支持**
   - 支持Markdown格式文本
   - 支持自定义字体
   - 支持暗色主题

3. **图片优化**
   - 自动图片缓存
   - 头部图片增强
   - 圆角处理

4. **错误处理**
   - 完善的异常体系
   - 优雅的降级处理
   - 详细的日志记录

#### 使用建议

1. **初始化时机**
```python
def __init__(self):
    super().__init__()
    # 推荐在插件初始化时创建TextCardService实例
    self.text_card = TextCardService(
        custom_fonts_dir="your_fonts_dir",  # 可选
        cache_dir="your_cache_dir"          # 可选
    )
```

2. **异常处理**
```python
from text2card_project.text_card_service import TextCardError

try:
    image = await self.text_card.generate_card(text)
except TextCardError as e:
    logger.error(f"生成图片失败: {e}")
    # 进行错误处理
```

3. **资源管理**
```python
# 临时文件处理
output_path = f"temp/card_{int(time.time())}.png"
try:
    await self.text_card.generate_card(text, output_path)
    # 使用生成的图片
finally:
    if os.path.exists(output_path):
        os.remove(output_path)
```

### 9.2 文本预处理

在使用TextCardService之前，可能需要对文本进行预处理：

```python
def preprocess_text(text: str) -> str:
    """文本预处理
    
    处理文本中的特殊字符、格式等，为生成图片做准备
    """
    # 1. 移除不必要的特殊字符
    text = re.sub(r'[\U0001F300-\U0001F9FF]', '', text)  # 移除表情符号
    text = re.sub(r'[^\u4e00-\u9fa5a-zA-Z0-9，。！？、：；''""（）\s]', '', text)
    
    # 2. 规范化标点符号
    text = text.replace('，', ',').replace('。', '.')
    text = text.replace('！', '!').replace('？', '?')
    
    # 3. 处理空白字符
    text = re.sub(r'\s+', ' ', text)
    text = text.strip()
    
    # 4. 处理连续标点
    text = re.sub(r'[,.]{2,}', ',', text)
    text = re.sub(r'[!！]{2,}', '!', text)
    text = re.sub(r'[?？]{2,}', '?', text)
    
    return text

def format_markdown(text: str) -> str:
    """Markdown格式处理
    
    将文本转换为规范的Markdown格式
    """
    # 1. 处理标题
    if '\n' in text:
        title, content = text.split('\n', 1)
        text = f"# {title}\n\n{content}"
    
    # 2. 处理列表
    text = re.sub(r'^(\d+)\.', r'1.', text, flags=re.MULTILINE)  # 有序列表标准化
    text = re.sub(r'^\s*[-*]\s', '- ', text, flags=re.MULTILINE)  # 无序列表标准化
    
    # 3. 处理强调语法
    text = re.sub(r'『(.*?)』', r'**\1**', text)  # 转换中文强调为Markdown加粗
    text = re.sub(r'「(.*?)」', r'`\1`', text)    # 转换中文引用为代码块
    
    # 4. 处理分隔线
    text = re.sub(r'[-─]{3,}', '---', text)
    
    # 5. 处理段落
    text = re.sub(r'\n{3,}', '\n\n', text)  # 规范化段落间距
    
    return text

class YourPlugin(PluginBase):
    async def handle_text(self, bot: WechatAPIClient, message: dict):
        text = message["Content"]
        
        # 1. 基础预处理
        text = preprocess_text(text)
        
        # 2. Markdown格式化（如果需要）
        text = format_markdown(text)
        
        # 3. 生成图片
        try:
            image = await self.text_card.generate_card(
                text=text,
                width=720,
                is_dark=False
            )
            
            # 4. 发送图片
            if isinstance(image, str):  # 如果是文件路径
                await bot.send_image_message(message["FromWxid"], image_path=image)
            else:  # 如果是PIL.Image对象
                temp_path = f"temp/card_{int(time.time())}.png"
                try:
                    image.save(temp_path, quality=95)
                    await bot.send_image_message(message["FromWxid"], image_path=temp_path)
                finally:
                    if os.path.exists(temp_path):
                        os.remove(temp_path)
                        
        except TextCardError as e:
            logger.error(f"生成图片失败: {e}")
            await bot.send_text_message(
                message["FromWxid"],
                "抱歉，图片生成失败，请稍后重试"
            )

### 9.3 长文本处理策略

对于长文本的处理，建议采用以下策略：

```python
async def handle_long_text(self, bot: WechatAPIClient, message: dict):
    text = message["Content"]
    text_length = len(text)
    
    # 1. 文本长度判断
    if text_length <= 150:  # 短文本
        await bot.send_text_message(message["FromWxid"], text)
    else:  # 长文本
        # 2. 预处理
        text = preprocess_text(text)
        text = format_markdown(text)
        
        # 3. 生成图片
        try:
            # 使用简单卡片样式
            image = await self.text_card.generate_simple_card(
                text=text,
                theme='light'
            )
            
            # 4. 发送图片
            if isinstance(image, str):
                await bot.send_image_message(message["FromWxid"], image_path=image)
            else:
                temp_path = f"temp/card_{int(time.time())}.png"
                try:
                    image.save(temp_path, quality=95)
                    await bot.send_image_message(message["FromWxid"], image_path=temp_path)
                finally:
                    if os.path.exists(temp_path):
                        os.remove(temp_path)
                        
        except TextCardError as e:
            logger.error(f"生成图片失败: {e}")
            # 5. 降级处理：分段发送
            segments = textwrap.wrap(text, 150)
            for segment in segments[:3]:  # 最多发送前3段
                await bot.send_text_message(message["FromWxid"], segment)
            if len(segments) > 3:
                await bot.send_text_message(
                    message["FromWxid"],
                    "...(内容过长，已省略剩余部分)"
                )
```

### 9.4 最佳实践

1. **文本预处理原则**
   - 在转换前进行必要的清理和格式化
   - 保持Markdown语法的规范性
   - 控制文本长度和复杂度

2. **性能优化**
   - 使用预编译的正则表达式
   - 避免重复的格式化操作
   - 合理使用缓存机制

3. **异常处理**
   - 捕获并处理所有可能的异常
   - 提供合适的降级方案
   - 给用户友好的错误提示

4. **资源管理**
   - 及时清理临时文件
   - 控制内存使用
   - 避免资源泄露

5. **用户体验**
   - 根据文本长度选择合适的处理方式
   - 提供清晰的处理进度反馈
   - 确保输出的一致性和可读性