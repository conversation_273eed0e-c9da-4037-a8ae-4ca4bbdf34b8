# 微信小程序消息测试插件

## 功能说明

这个插件用于测试发送微信小程序消息，使用你提供的"唱舞星愿站"小程序XML内容。

## 使用方法

### 基本命令
- `红包来了` - 发送唱舞星愿站小程序消息
- `唱舞星愿站` - 发送唱舞星愿站小程序消息

### 消息类型
- **消息类型**: 33 (小程序消息)
- **小程序名称**: 唱舞星愿站（唱舞全明星）
- **AppID**: wxa708de63ee4a2353
- **用户名**: gh_25eb09d7bc53@app

## 配置说明

### config.toml 配置项
```toml
[MiniProgramTester]
enable = true                          # 是否启用插件
command = ["红包来了", "唱舞星愿站"]      # 触发命令
natural_response = true                 # 启用自然化响应

[MiniProgramTester.rate_limit]
cooldown = 5  # 冷却时间（秒）
```

### 自然化响应
- **启用时**: 使用"好的"、"收到"等自然回复，静默发送消息
- **禁用时**: 静默发送消息，无额外回复

## 技术细节

### XML消息结构
- 使用简化但完整的微信小程序XML格式
- 包含小程序的所有必要字段：appid、username、pagepath等
- **包含缩略图信息**：CDN缩略图URL、MD5、尺寸等
- **修复URL问题**：使用 `https://weixin.qq.com` 作为主URL
- **XML转义**：正确处理URL中的&符号转义

### API调用
```python
client_msg_id, create_time, new_msg_id = await bot.send_app_message(
    wxid,    # 目标聊天ID
    xml,     # 小程序XML内容
    33       # 消息类型：小程序
)
```

## 测试验证

运行 `test_xml.py` 可以验证XML格式是否正确：
```bash
python plugins/MiniProgramTester/test_xml.py
```

## 注意事项

1. **消息类型**: 必须使用类型33，这是微信小程序消息的标准类型
2. **XML格式**: XML必须严格按照微信的格式要求，包含所有必要字段
3. **限流保护**: 插件内置5秒冷却时间，防止频繁发送
4. **错误处理**: 包含完整的异常处理和日志记录

## 日志输出

插件会记录以下信息：
- 发送请求的用户信息
- 消息发送结果（成功/失败）
- 返回的消息ID和时间戳
- 任何异常错误信息

## 开发说明

基于XYBot插件开发指南创建，遵循以下最佳实践：
- 使用自然化响应模式
- 实现用户级限流保护
- 完整的错误处理和日志记录
- 符合插件标准结构
