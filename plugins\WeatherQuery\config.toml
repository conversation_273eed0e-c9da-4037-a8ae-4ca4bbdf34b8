[WeatherQuery]
enable = true
command = ["天气", "查天气", "weather"]
command-format = """
天气查询插件使用说明：
1. 天气 [城市名]
2. 查天气 [城市名]
3. weather [城市名]
4. [城市名]天气

例如：天气 北京
      北京天气
"""

# 限流配置
[WeatherQuery.rate_limit]
tokens_per_second = 0.1  # 每10秒生成1个令牌
bucket_size = 3  # 令牌桶容量

# API配置
[WeatherQuery.api]
url = "http://182.92.124.103:7891/process/"
timeout = 10  # 请求超时时间（秒）

# 天气卡片API配置
[WeatherQuery.card_api]
url = "http://1.95.39.48:5000/api/weather"
timeout = 60  # 请求超时时间（秒），增加到60秒
width = 800   # 与示例代码保持一致
height = 0   # 图片高度，0为自动
scale = 2.0  # 图片缩放因子，提高清晰度 

# 百度天气API配置
[WeatherQuery.baidu_api]
url = "https://mini.s-shot.ru/1000/PNG/1000/"
base_url = "https://weathernew.pae.baidu.com/weathernew/pc"
timeout = 20  # 请求超时时间（秒） 