import os
import time
import sqlite3
import httpx
import traceback
import random
from datetime import datetime
from pathlib import Path
from typing import Optional, Dict, Any, List, Tuple

try:
    import tomllib
except ModuleNotFoundError:
    import tomli as tomllib
from loguru import logger

from WechatAPI import WechatAPIClient
from utils.decorators import (
    on_text_message, on_emoji_message, on_image_message, on_voice_message,
    on_video_message, on_file_message, on_quote_message, on_pat_message,
    on_at_message, on_system_message, on_article_message, on_xml_message
)
from utils.plugin_base import PluginBase

class ChatSummary(PluginBase):
    description = "群聊总结和统计插件"
    author = "XYBot"
    version = "1.0.0"
    plugin_name = "ChatSummary"

    def __init__(self):
        super().__init__()

        # 初始化临时目录
        self.temp_dir = Path("plugins/ChatSummary/temp")
        self.temp_dir.mkdir(parents=True, exist_ok=True)

        # 读取配置
        self._load_config()

        # 读取管理员配置
        self._load_admin_config()

        # 用户限流字典
        self.user_last_request = {}

        # 初始化数据库
        self._init_database()

        # 群聊功能开关字典
        self.enabled_groups = {}

        # 加载已启用的群聊
        self._load_enabled_groups()

        # 添加缓存字典
        self.group_name_cache = {}  # 缓存群聊名称
        self.sender_nickname_cache = {}  # 缓存发送者昵称

        # 初始化自然化回复词库
        self._init_natural_responses()

    def _init_natural_responses(self):
        """初始化自然化回复词库"""
        self.confirm_msgs = [
            "好的", "收到", "明白了", "嗯", "知道了", "行", "OK"
        ]

        self.error_msgs = [
            "这可把我难住了", "搞不定了", "这个有点难", "弄不了",
            "出问题了", "搞不出来", "这个不行"
        ]

        self.rate_limit_msgs = [
            "你搁这刷bug呢", "慢点慢点", "别这么急", "一个一个来",
            "太快了太快了", "歇会儿", "别刷了"
        ]

        self.processing_msgs = [
            "马上就好", "稍等一下", "正在处理", "等等哈",
            "马上", "在做了", "处理中"
        ]

        self.success_msgs = [
            "搞定了", "好了", "完成", "弄好了", "OK了"
        ]

        self.permission_msgs = [
            "这个我管不了", "没权限", "不是管理员", "权限不够",
            "管理员才能操作", "你不是管理员"
        ]

    def _load_admin_config(self):
        """加载管理员配置"""
        try:
            # 读取主配置文件
            with open("main_config.toml", "rb") as f:
                main_config = tomllib.load(f)

            # 获取管理员列表
            self.admins = main_config.get("XYBot", {}).get("admins", [])
        except Exception as e:
            logger.error(f"[{self.plugin_name}] 加载管理员配置失败: {str(e)}")
            self.admins = []

    def _is_admin(self, wxid: str) -> bool:
        """检查用户是否是管理员"""
        return wxid in self.admins

    def _load_config(self):
        """加载配置文件"""
        config = {}
        try:
            config_path = f"plugins/{self.plugin_name}/config.toml"
            if not os.path.exists(config_path):
                logger.warning(f"[{self.plugin_name}] 配置文件 {config_path} 不存在，将使用默认配置")
                config = {}
            else:
                with open(config_path, "rb") as f:
                    plugin_config = tomllib.load(f)
                config = plugin_config.get(self.plugin_name, {})
        except Exception as e:
            logger.error(f"[{self.plugin_name}] 配置文件读取失败: {str(e)}")
            config = {}

        # 基本配置初始化
        self.enable = config.get("enable", True)
        self.command = config.get("command", ["群聊总结", "群聊统计"])
        self.command_format = config.get("command-format", "默认使用说明")
        self.natural_response = config.get("natural_response", True)

        # 数据库配置
        db_config = config.get("database", {})
        self.db_path = db_config.get("path", "database/chat_summary.db")
        self.max_storage_days = db_config.get("max_storage_days", 7)

        # 功能开关配置
        features_config = config.get("features", {})
        self.enable_summary = features_config.get("enable_summary", True)
        self.enable_statistics = features_config.get("enable_statistics", True)

        # 豆包集成配置
        doubao_config = config.get("doubao_integration", {})
        self.enable_doubao = doubao_config.get("enable", False)
        self.doubao_prompt = doubao_config.get("prompt", """
#角色设定
你是一位资深社群运营专家，擅长将杂乱群聊提炼为结构化报告。请用简洁口语化中文输出，保持专业但不失趣味性。

#核心任务
1. 标题必须使用当前微信群名称，格式为「📝【群名】精华总结
2. 内容需满足：
▶️ 提炼3-5个核心讨论主题（带🔥emoji标注热度）
▶️ 每个主题下包含关键结论（用✔️标注）和待解决问题（用❓标注）
▶️ 每段开头添加情境化emoji（如💡知识分享/🎉活动预告/⚠️重要通知）
3. 结尾补充：
- 👥今日最活跃成员Top3
- 📅明日重点关注事项预告

#风格要求
• 段落间用「───」分隔保持呼吸感
• 每项列表前使用▸符号
• 关键数据/人名用**加粗**显示
• 每200字插入1个相关emoji（避免超过5个/段）
""")

        # 初始化用户限流
        rate_limit_config = config.get("rate_limit", {})
        self.cooldown = rate_limit_config.get("cooldown", 60)

        # 上传配置
        upload_config = config.get("upload", {})
        self.enable_upload = upload_config.get("enable", True)
        self.upload_url = upload_config.get("url", "https://chatglm.cn/chatglm/backend-api/assistant/file_upload")
        self.token = upload_config.get("token", "")
        self.assistant_id = upload_config.get("assistant_id", "65940acff94777010aa6b796")
        self.upload_timeout = upload_config.get("timeout", 30)
        # Cookie相关配置
        self.add_cookies = upload_config.get("add_cookies", False)
        self.cookies = upload_config.get("cookies", "")

        # 头部信息配置
        header_config = upload_config.get("headers", {})
        self.headers = {
            'x_app_platform': header_config.get('x_app_platform', 'pc'),
            'x_device_brand': header_config.get('x_device_brand', ''),
            'x_exp_groups': header_config.get('x_exp_groups', ''),
            'x_sign': header_config.get('x_sign', '25f3920b9f6fa29d918f37e283643d0f'),
            'x_lang': header_config.get('x_lang', 'zh'),
            'x_app_version': header_config.get('x_app_version', '0.0.1'),
            'user_agent': header_config.get('user_agent', 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/116.0.0.0 Safari/537.36'),
            'app_name': header_config.get('app_name', 'chatglm'),
            'accept': header_config.get('accept', 'application/json, text/plain, */*'),
            'x_device_model': header_config.get('x_device_model', ''),
            'x_device_id': header_config.get('x_device_id', '83b89578f30c4fa598733d7bf4a0c9a8'),
            'origin': header_config.get('origin', 'https://chatglm.cn'),
            'x_requested_with': header_config.get('x_requested_with', 'mark.via'),
            'sec_fetch_site': header_config.get('sec_fetch_site', 'same-origin'),
            'sec_fetch_mode': header_config.get('sec_fetch_mode', 'cors'),
            'sec_fetch_dest': header_config.get('sec_fetch_dest', 'empty'),
            'accept_encoding': header_config.get('accept_encoding', 'gzip, deflate'),
            'accept_language': header_config.get('accept_language', 'zh-CN,zh;q=0.9,en-US;q=0.8,en;q=0.7')
        }

        # 表单边界配置
        form_config = upload_config.get("form", {})
        self.form_boundary = form_config.get("boundary", "----WebKitFormBoundarygUFvkAUBsMr3iR2g")

    def _init_database(self):
        """初始化数据库"""
        try:
            # 确保数据库目录存在
            os.makedirs(os.path.dirname(self.db_path), exist_ok=True)

            # 连接数据库
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()

            # 创建消息表
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS chat_messages (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    group_id TEXT,
                    group_name TEXT,
                    sender_wxid TEXT,
                    sender_nickname TEXT,
                    content TEXT,
                    msg_type TEXT,
                    content_length INTEGER,
                    create_time TEXT
                )
            ''')

            # 创建索引
            cursor.execute('CREATE INDEX IF NOT EXISTS idx_group_time ON chat_messages(group_id, create_time)')
            cursor.execute('CREATE INDEX IF NOT EXISTS idx_sender ON chat_messages(sender_nickname)')
            cursor.execute('CREATE INDEX IF NOT EXISTS idx_sender_wxid ON chat_messages(sender_wxid)')

            conn.commit()
            conn.close()

            logger.info(f"[{self.plugin_name}] 数据库初始化成功")

        except Exception as e:
            logger.error(f"[{self.plugin_name}] 数据库初始化失败: {str(e)}")

    def _load_enabled_groups(self):
        """从数据库加载已启用的群聊"""
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()

            # 创建功能状态表（如果不存在）
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS feature_status (
                    group_id TEXT PRIMARY KEY,
                    summary_enabled INTEGER DEFAULT 0,
                    statistics_enabled INTEGER DEFAULT 0,
                    update_time TEXT
                )
            ''')

            # 加载已启用的群聊
            cursor.execute('SELECT group_id, summary_enabled, statistics_enabled FROM feature_status')
            for row in cursor.fetchall():
                group_id, summary_enabled, statistics_enabled = row
                if summary_enabled or statistics_enabled:
                    self.enabled_groups[group_id] = True

            conn.close()

        except Exception as e:
            logger.error(f"[{self.plugin_name}] 加载已启用群聊失败: {str(e)}")

    def _save_feature_status(self, group_id: str, feature: str, enabled: bool):
        """保存功能状态到数据库"""
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()

            # 获取当前状态
            cursor.execute('SELECT summary_enabled, statistics_enabled FROM feature_status WHERE group_id = ?', (group_id,))
            result = cursor.fetchone()

            if result:
                summary_enabled, statistics_enabled = result
                # 更新状态
                if feature == "summary":
                    summary_enabled = 1 if enabled else 0
                elif feature == "statistics":
                    statistics_enabled = 1 if enabled else 0

                cursor.execute('''
                    UPDATE feature_status
                    SET summary_enabled = ?, statistics_enabled = ?, update_time = ?
                    WHERE group_id = ?
                ''', (summary_enabled, statistics_enabled, datetime.now().strftime('%Y-%m-%d %H:%M:%S'), group_id))
            else:
                # 插入新记录
                summary_enabled = 1 if feature == "summary" and enabled else 0
                statistics_enabled = 1 if feature == "statistics" and enabled else 0

                cursor.execute('''
                    INSERT INTO feature_status (group_id, summary_enabled, statistics_enabled, update_time)
                    VALUES (?, ?, ?, ?)
                ''', (group_id, summary_enabled, statistics_enabled, datetime.now().strftime('%Y-%m-%d %H:%M:%S')))

            conn.commit()
            conn.close()

        except Exception as e:
            logger.error(f"[{self.plugin_name}] 保存功能状态失败: {str(e)}")

    def _check_user_limit(self, wxid: str, user_wxid: str) -> float:
        """检查用户请求限制，返回需要等待的时间（秒）"""
        user_key = f"{wxid}_{user_wxid}"
        last_request = self.user_last_request.get(user_key, 0)
        current_time = time.time()

        elapsed = current_time - last_request
        self.user_last_request[user_key] = current_time

        if elapsed < self.cooldown:
            return self.cooldown - elapsed

        return 0

    async def _save_message(self, bot: WechatAPIClient, message: dict) -> None:
        """保存消息到数据库"""
        try:
            # 检查必要的字段
            if not all(key in message for key in ["FromWxid", "SenderWxid"]):
                logger.error(f"[{self.plugin_name}] 消息缺少必要字段: {message}")
                return

            # 检查群聊是否启用了功能
            group_id = message["FromWxid"]
            if group_id not in self.enabled_groups:
                return

            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()

            # 获取群聊名称
            group_name = await self._get_group_name(bot, group_id)

            # 获取发送者昵称
            sender_wxid = message["SenderWxid"]
            sender_nickname = await self._get_sender_nickname(bot, group_id, sender_wxid)

            # 获取消息类型
            msg_type = message.get("Type", "text")

            # 获取消息内容
            content = message.get("Content", "")

            # 保存消息
            cursor.execute('''
                INSERT INTO chat_messages (
                    group_id, group_name, sender_wxid, sender_nickname, content,
                    msg_type, content_length, create_time
                ) VALUES (?, ?, ?, ?, ?, ?, ?, ?)
            ''', (
                group_id,
                group_name,
                sender_wxid,
                sender_nickname,
                content,
                msg_type,
                len(str(content)),
                datetime.now().strftime('%Y-%m-%d %H:%M:%S')
            ))

            conn.commit()
            conn.close()

        except Exception as e:
            logger.error(f"[{self.plugin_name}] 保存消息失败: {str(e)}")
            logger.error(f"[{self.plugin_name}] 消息内容: {message}")

    @on_text_message
    async def handle_text(self, bot: WechatAPIClient, message: dict):
        """处理文本消息"""
        if not self.enable:
            return

        content = str(message["Content"]).strip()
        wxid = message["FromWxid"]
        user_wxid = message["SenderWxid"]

        # 保存消息到数据库
        await self._save_message(bot, message)

        # 检查是否是插件命令
        if content not in self.command:
            return

        try:
            # 检查用户请求限制
            wait_time = self._check_user_limit(wxid, user_wxid)
            if wait_time > 0:
                if self.natural_response:
                    rate_limit_msg = random.choice(self.rate_limit_msgs)
                    await bot.send_text_message(wxid, rate_limit_msg)
                else:
                    await bot.send_text_message(
                        wxid,
                        f"⏳ 请求太快啦，请等待 {wait_time:.1f} 秒后再试~"
                    )
                return

            # 处理命令 - 检查管理员权限
            if content in ["开启群聊总结", "关闭群聊总结", "开启群聊统计", "关闭群聊统计"]:
                # 检查是否是管理员
                if not self._is_admin(user_wxid):
                    if self.natural_response:
                        permission_msg = random.choice(self.permission_msgs)
                        await bot.send_text_message(wxid, permission_msg)
                    else:
                        await bot.send_text_message(wxid, "❌ 只有管理员才能执行此操作")
                    return

                # 处理命令
                if content == "开启群聊总结":
                    result = await self._toggle_feature("summary", True, wxid, bot)
                    if self.natural_response:
                        if "成功" in result:
                            success_msg = random.choice(self.success_msgs)
                            await bot.send_text_message(wxid, success_msg)
                        else:
                            error_msg = random.choice(self.error_msgs)
                            await bot.send_text_message(wxid, error_msg)
                    else:
                        await bot.send_text_message(wxid, result)
                elif content == "关闭群聊总结":
                    result = await self._toggle_feature("summary", False, wxid, bot)
                    if self.natural_response:
                        if "成功" in result:
                            success_msg = random.choice(self.success_msgs)
                            await bot.send_text_message(wxid, success_msg)
                        else:
                            error_msg = random.choice(self.error_msgs)
                            await bot.send_text_message(wxid, error_msg)
                    else:
                        await bot.send_text_message(wxid, result)
                elif content == "开启群聊统计":
                    result = await self._toggle_feature("statistics", True, wxid, bot)
                    if self.natural_response:
                        if "成功" in result:
                            success_msg = random.choice(self.success_msgs)
                            await bot.send_text_message(wxid, success_msg)
                        else:
                            error_msg = random.choice(self.error_msgs)
                            await bot.send_text_message(wxid, error_msg)
                    else:
                        await bot.send_text_message(wxid, result)
                elif content == "关闭群聊统计":
                    result = await self._toggle_feature("statistics", False, wxid, bot)
                    if self.natural_response:
                        if "成功" in result:
                            success_msg = random.choice(self.success_msgs)
                            await bot.send_text_message(wxid, success_msg)
                        else:
                            error_msg = random.choice(self.error_msgs)
                            await bot.send_text_message(wxid, error_msg)
                    else:
                        await bot.send_text_message(wxid, result)
            elif content == "群聊总结":
                if wxid not in self.enabled_groups:
                    if self.natural_response:
                        error_msg = random.choice(self.error_msgs)
                        await bot.send_text_message(wxid, error_msg)
                    else:
                        await bot.send_text_message(wxid, "❌ 群聊总结功能已关闭")
                else:
                    # 直接调用_export_chat_summary方法，它会发送处理中的提示消息
                    result = await self._export_chat_summary(bot, wxid, "")
                    # 如果返回了非空结果，才发送文本消息
                    if result:
                        await bot.send_text_message(wxid, result)
            elif content == "群聊统计":
                if wxid not in self.enabled_groups:
                    if self.natural_response:
                        error_msg = random.choice(self.error_msgs)
                        await bot.send_text_message(wxid, error_msg)
                    else:
                        await bot.send_text_message(wxid, "❌ 群聊统计功能已关闭")
                else:
                    result = await self._generate_statistics(bot, wxid, "", None)
                    await bot.send_text_message(wxid, result)
            else:
                if self.natural_response:
                    error_msg = random.choice(self.error_msgs)
                    await bot.send_text_message(wxid, error_msg)
                else:
                    await bot.send_text_message(wxid, "❌ 未知命令")

        except Exception as e:
            logger.error(f"[{self.plugin_name}] 处理异常: {str(e)}")
            if self.natural_response:
                error_msg = random.choice(self.error_msgs)
                await bot.send_text_message(wxid, error_msg)
            else:
                await bot.send_text_message(
                    wxid,
                    "❌ 处理过程中出现错误，请稍后重试"
                )

    def _parse_time_range(self, param: str) -> Tuple[Optional[str], Optional[str]]:
        """解析时间范围参数"""
        try:
            # 如果没有提供时间范围，默认使用当天
            if not param:
                end_time = datetime.now()
                start_time = end_time.replace(hour=0, minute=0, second=0)
                return start_time.strftime('%Y-%m-%d %H:%M:%S'), end_time.strftime('%Y-%m-%d %H:%M:%S')

            # 解析时间范围
            dates = param.split()
            if len(dates) == 1:
                # 只提供了一个日期，使用当天
                date = datetime.strptime(dates[0], '%Y-%m-%d')
                start_time = date.replace(hour=0, minute=0, second=0)
                end_time = date.replace(hour=23, minute=59, second=59)
                return start_time.strftime('%Y-%m-%d %H:%M:%S'), end_time.strftime('%Y-%m-%d %H:%M:%S')
            elif len(dates) == 2:
                # 提供了开始和结束日期
                start_date = datetime.strptime(dates[0], '%Y-%m-%d')
                end_date = datetime.strptime(dates[1], '%Y-%m-%d')
                start_time = start_date.replace(hour=0, minute=0, second=0)
                end_time = end_date.replace(hour=23, minute=59, second=59)
                return start_time.strftime('%Y-%m-%d %H:%M:%S'), end_time.strftime('%Y-%m-%d %H:%M:%S')
            else:
                return None, None
        except ValueError:
            return None, None

    async def _get_group_name(self, bot: WechatAPIClient, group_id: str) -> str:
        """获取群聊名称"""
        try:
            # 清除缓存以确保每次获取最新数据
            if group_id in self.group_name_cache:
                del self.group_name_cache[group_id]

            # 使用 get_chatroom_info 获取群聊信息
            try:
                chatroom_info = await bot.get_chatroom_info(group_id)

                # 初始化群名称为空
                group_name = ""

                # 从群聊信息中获取群名称
                if isinstance(chatroom_info, dict):
                    if 'NickName' in chatroom_info:
                        if isinstance(chatroom_info['NickName'], dict) and 'string' in chatroom_info['NickName']:
                            group_name = chatroom_info['NickName']['string']
                        else:
                            group_name = str(chatroom_info['NickName'])

                # 如果仍然无法获取群名称，使用更友好的名称
                if not group_name:
                    group_name = f"群聊{group_id.split('@')[0]}"
                    logger.warning(f"[{self.plugin_name}] 无法获取群聊名称，使用友好名称: {group_name}")

            except Exception as e:
                logger.error(f"[{self.plugin_name}] 获取群聊信息失败: {str(e)}")
                # 使用更友好的名称
                group_name = f"群聊{group_id.split('@')[0]}"

            # 确保group_name不为空
            if not group_name:
                group_name = f"群聊{group_id.split('@')[0]}"
                logger.warning(f"[{self.plugin_name}] 群名称为空，使用友好名称: {group_name}")

            # 更新缓存
            self.group_name_cache[group_id] = group_name

            # 更新数据库中的群名称
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            cursor.execute('UPDATE chat_messages SET group_name = ? WHERE group_id = ?', (group_name, group_id))
            conn.commit()
            conn.close()

            return group_name
        except Exception as e:
            logger.error(f"[{self.plugin_name}] 获取群聊名称失败: {str(e)}")
            return f"群聊{group_id.split('@')[0]}"

    async def _update_user_nickname_history(self, group_id: str, wxid: str, new_nickname: str) -> None:
        """更新用户在群聊中的历史消息记录的昵称"""
        try:
            # 连接数据库
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()

            # 首先检查有多少条记录需要更新
            cursor.execute("""
                SELECT COUNT(*) FROM chat_messages
                WHERE group_id = ? AND sender_wxid = ? AND sender_nickname != ?
            """, (group_id, wxid, new_nickname))

            count = cursor.fetchone()[0]

            if count > 0:
                # 执行更新
                cursor.execute("""
                    UPDATE chat_messages
                    SET sender_nickname = ?
                    WHERE group_id = ? AND sender_wxid = ?
                """, (new_nickname, group_id, wxid))

                # 提交更改
                conn.commit()

                # 验证更新是否成功
                cursor.execute("""
                    SELECT COUNT(*) FROM chat_messages
                    WHERE group_id = ? AND sender_wxid = ? AND sender_nickname != ?
                """, (group_id, wxid, new_nickname))

                remaining = cursor.fetchone()[0]

                if remaining > 0:
                    logger.warning(f"[{self.plugin_name}] 昵称更新可能不完整，仍有 {remaining} 条记录未更新")

            # 关闭数据库连接
            conn.close()

        except Exception as e:
            logger.error(f"[{self.plugin_name}] 更新历史消息昵称失败: {str(e)}")
            raise

    async def _get_sender_nickname(self, bot: WechatAPIClient, group_id: str, sender_wxid: str) -> str:
        """获取发送者昵称"""
        try:
            # 首先检查缓存
            cache_key = f"{group_id}_{sender_wxid}"
            if cache_key in self.sender_nickname_cache:
                return self.sender_nickname_cache[cache_key]

            # 从数据库缓存中获取
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            cursor.execute('SELECT sender_nickname FROM chat_messages WHERE group_id = ? AND sender_wxid = ? AND sender_nickname != ? ORDER BY create_time DESC LIMIT 1',
                         (group_id, sender_wxid, sender_wxid))
            result = cursor.fetchone()
            conn.close()

            if result and result[0] and result[0] != sender_wxid:
                # 更新缓存
                self.sender_nickname_cache[cache_key] = result[0]
                return result[0]

            # 如果数据库中没有，使用 get_chatroom_member_list 获取
            try:
                member_list = await bot.get_chatroom_member_list(group_id)

                for member in member_list:
                    if member.get('UserName') == sender_wxid:
                        # 优先使用 DisplayName，如果没有则使用 NickName
                        nickname = member.get('DisplayName', '')
                        if not nickname:
                            nickname = member.get('NickName', '')

                        if nickname and nickname != sender_wxid:
                            # 更新缓存
                            self.sender_nickname_cache[cache_key] = nickname
                            # 更新数据库中的历史记录
                            await self._update_user_nickname_history(group_id, sender_wxid, nickname)
                            return nickname
            except Exception as e:
                logger.error(f"[{self.plugin_name}] 获取群成员昵称失败: {str(e)}")

            # 如果所有方法都失败，使用wxid
            self.sender_nickname_cache[cache_key] = sender_wxid
            return sender_wxid
        except Exception as e:
            logger.error(f"[{self.plugin_name}] 获取发送者昵称失败: {str(e)}")
            return sender_wxid

    async def _get_messages(self, group_id: str, start_time: str, end_time: str) -> List[Dict[str, Any]]:
        """从数据库获取消息"""
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()

            # 查询消息
            cursor.execute('''
                SELECT sender_nickname, content, msg_type, create_time
                FROM chat_messages
                WHERE group_id = ? AND create_time BETWEEN ? AND ?
                ORDER BY create_time ASC
            ''', (group_id, start_time, end_time))

            messages = []
            for row in cursor.fetchall():
                messages.append({
                    'sender_nickname': row[0],
                    'content': row[1],
                    'msg_type': row[2],
                    'create_time': row[3]
                })

            conn.close()
            return messages
        except Exception as e:
            logger.error(f"[{self.plugin_name}] 获取消息失败: {str(e)}")
            return []

    async def _upload_file_to_chatglm(self, filepath: Path, filename: str) -> Dict[str, Any]:
        """上传文件到ChatGLM服务器"""
        try:
            # 如果未配置token或禁用上传功能，则返回空结果
            if not self.enable_upload or (not self.token and not (self.add_cookies and self.cookies)):
                logger.warning(f"[{self.plugin_name}] 文件上传功能未启用或认证信息未配置")
                return {}

            # 确认文件存在并可读
            if not os.path.exists(filepath):
                logger.error(f"[{self.plugin_name}] 文件不存在: {filepath}")
                return {}

            # 检查文件大小
            file_size = os.path.getsize(filepath)
            if file_size == 0:
                logger.error(f"[{self.plugin_name}] 文件大小为0: {filepath}")
                return {}

            # 创建固定的请求ID和Nonce
            request_id = self._generate_random_id()
            nonce = self._generate_random_id()
            timestamp = str(int(time.time() * 1000))

            # 使用配置文件中的表单边界
            boundary = self.form_boundary

            # 创建头部参数
            headers = {
                'Authorization': f'Bearer {self.token}',
                'X-App-Platform': self.headers['x_app_platform'],
                'X-Device-Brand': self.headers['x_device_brand'],
                'X-Exp-Groups': self.headers['x_exp_groups'],
                'X-Sign': self.headers['x_sign'],
                'X-Lang': self.headers['x_lang'],
                'X-Request-Id': request_id,
                'X-App-Version': self.headers['x_app_version'],
                'User-Agent': self.headers['user_agent'],
                'App-Name': self.headers['app_name'],
                'Content-Type': f'multipart/form-data; boundary={boundary.replace("----", "")}',
                'Accept': self.headers['accept'],
                'X-Timestamp': timestamp,
                'X-Device-Model': self.headers['x_device_model'],
                'X-Nonce': nonce,
                'X-Device-Id': self.headers['x_device_id'],
                'Origin': self.headers['origin'],
                'X-Requested-With': self.headers['x_requested_with'],
                'Sec-Fetch-Site': self.headers['sec_fetch_site'],
                'Sec-Fetch-Mode': self.headers['sec_fetch_mode'],
                'Sec-Fetch-Dest': self.headers['sec_fetch_dest'],
                'Accept-Encoding': self.headers['accept_encoding'],
                'Accept-Language': self.headers['accept_language']
            }

            # 如果配置了添加Cookie
            if self.add_cookies and self.cookies:
                headers['Cookie'] = self.cookies

            # 使用完整URL
            full_url = self.upload_url
            if not full_url.startswith('http'):
                full_url = 'https://chatglm.cn' + ('' if full_url.startswith('/') else '/') + full_url

            # 使用httpx提供的文件上传功能而不是手动构建
            try:
                async with httpx.AsyncClient(timeout=self.upload_timeout) as client:
                    # 准备表单数据
                    files = {'file': (filename, open(filepath, 'rb'), 'text/plain')}
                    data = {'assistant_id': self.assistant_id}

                    # 发送请求
                    response = await client.post(
                        full_url,
                        headers=headers,
                        files=files,
                        data=data
                    )

                    # 关闭文件
                    files['file'][1].close()

                    if response.status_code != 200:
                        logger.error(f"[{self.plugin_name}] 文件上传失败，状态码: {response.status_code}")
                        logger.error(f"[{self.plugin_name}] 响应内容: {response.text}")
                        return {}

                    try:
                        result = response.json()

                        if result.get("status") == 0 and "result" in result:
                            return result["result"]
                        else:
                            error_msg = result.get("message", "未知错误")
                            logger.error(f"[{self.plugin_name}] 文件上传接口返回错误: {error_msg}")
                            return {}
                    except Exception as e:
                        logger.error(f"[{self.plugin_name}] 解析响应JSON失败: {str(e)}")
                        logger.error(f"[{self.plugin_name}] 原始响应内容: {response.text}")
                        return {}
            except Exception as file_error:
                logger.error(f"[{self.plugin_name}] 文件读取或请求过程中出错: {str(file_error)}")
                return {}

        except Exception as e:
            logger.error(f"[{self.plugin_name}] 上传文件过程中出现异常: {str(e)}")
            return {}

    def _generate_random_id(self) -> str:
        """生成随机ID字符串，用于请求头"""
        import uuid
        return str(uuid.uuid4()).replace('-', '')[:32]

    def _generate_random_str(self, length: int) -> str:
        """生成指定长度的随机字符串"""
        import random
        import string
        return ''.join(random.choices(string.ascii_letters + string.digits, k=length))

    async def _generate_summary_with_doubao(self, bot: WechatAPIClient, group_id: str, file_url: str, group_name: str) -> str:
        """使用豆包插件生成群聊总结

        Args:
            bot: WechatAPI客户端
            group_id: 群聊ID
            file_url: 导出文件的URL
            group_name: 群聊名称

        Returns:
            str: 总结结果 或 None表示失败
        """
        try:
            # 动态导入豆包插件
            try:
                from plugins.Doubao.main import Doubao
            except ImportError:
                logger.error(f"[{self.plugin_name}] 导入豆包插件失败，无法生成总结报告")
                await bot.send_text_message(group_id, "❌ 无法使用豆包插件，请检查插件是否安装")
                return None

            # 直接创建豆包插件实例，而不是从bot.plugins获取
            try:
                doubao_instance = Doubao()
            except Exception as e:
                logger.error(f"[{self.plugin_name}] 创建豆包插件实例失败: {str(e)}")
                await bot.send_text_message(group_id, f"❌ 创建豆包插件实例失败: {str(e)}")
                return None

            # 构建豆包提示词
            prompt = f"{self.doubao_prompt}\n{file_url}"

            # 构造消息对象
            fake_message = {
                "FromWxid": group_id,
                "SenderWxid": "system",
                "Content": prompt
            }

            # 调用豆包API
            result = await doubao_instance.call_doubao_api(bot, fake_message, prompt)

            # 检查结果
            if not result or "text" not in result or not result["text"]:
                logger.error(f"[{self.plugin_name}] 豆包API返回结果为空")
                await bot.send_text_message(group_id, "❌ 生成总结报告失败，豆包AI返回结果为空")
                return None

            # 成功生成总结文本
            summary_text = result["text"]

            try:
                # 调用豆包插件的方法生成文本卡片图片，但不自动发送
                card_path = await doubao_instance._generate_text_card(summary_text, width=800)

                if card_path and os.path.exists(card_path):
                    # 确保图片路径是绝对路径
                    abs_card_path = os.path.abspath(card_path)

                    # 只使用一种方式发送图片，优先使用base64方式
                    try:
                        import base64
                        with open(abs_card_path, "rb") as img_file:
                            img_data = img_file.read()
                            img_base64 = base64.b64encode(img_data).decode('utf-8')


                            # 使用base64发送图片
                            await bot.send_image_message(group_id, img_base64)
                    except Exception as base64_err:
                        logger.error(f"[{self.plugin_name}] base64编码发送失败: {str(base64_err)}")

                        # 如果base64发送失败，回退到直接发送图片文件
                        await bot.send_image_message(group_id, abs_card_path)

                    # 无论使用哪种方式发送图片，都返回文本结果
                    return summary_text
                else:
                    logger.warning(f"[{self.plugin_name}] 生成总结报告图片失败，将发送文本")
                    # 如果图片生成失败，发送文本
                    await bot.send_text_message(group_id, summary_text)
                    return summary_text
            except Exception as img_err:
                logger.error(f"[{self.plugin_name}] 转换为图片时出错: {str(img_err)}")
                logger.error(traceback.format_exc())

                # 图片生成失败，直接发送文本
                await bot.send_text_message(group_id, summary_text)
                return summary_text

        except Exception as e:
            logger.error(f"[{self.plugin_name}] 使用豆包生成总结失败: {str(e)}")
            logger.error(traceback.format_exc())
            await bot.send_text_message(group_id, f"❌ 生成总结报告时出现错误: {str(e)}")
            return None

    async def _export_chat_summary(self, bot: WechatAPIClient, group_id: str, param: str) -> str:
        """导出群聊总结"""
        try:
            # 解析时间范围
            start_time, end_time = self._parse_time_range(param)
            if not start_time or not end_time:
                if self.natural_response:
                    error_msg = random.choice(self.error_msgs)
                    return error_msg
                else:
                    return "❌ 时间格式错误，请使用 YYYY-MM-DD 格式，例如：2024-04-01 2024-04-08"

            # 获取群聊名称
            group_name = await self._get_group_name(bot, group_id)

            # 获取消息
            messages = await self._get_messages(group_id, start_time, end_time)
            if not messages:
                if self.natural_response:
                    error_msg = random.choice(self.error_msgs)
                    return error_msg
                else:
                    return f"❌ 在 {start_time} 到 {end_time} 期间没有找到消息记录"

            # 发送处理中的提示消息
            if self.natural_response:
                processing_msg = random.choice(self.processing_msgs)
                await bot.send_text_message(group_id, processing_msg)
            else:
                await bot.send_text_message(group_id, f"🔄 正在生成「{group_name}」群聊总结报告，请稍后……")

            # 生成导出内容
            export_content = f"[群聊] {group_name}\n"
            export_content += f"[时间范围] {start_time} 至 {end_time}\n\n"

            for msg in messages:
                export_content += f"[时间] {msg['create_time']}\n"
                export_content += f"[发送者] {msg['sender_nickname']}\n"
                export_content += f"[类型] {msg['msg_type']}\n"
                export_content += f"[内容] {msg['content']}\n\n"

            # 保存到文件
            timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
            filename = f"export_{group_id}_{timestamp}.txt"
            filepath = self.temp_dir / filename

            try:
                with open(filepath, "w", encoding="utf-8") as f:
                    f.write(export_content)
            except Exception as e:
                logger.error(f"[{self.plugin_name}] 文件导出失败: {str(e)}")
                if self.natural_response:
                    error_msg = random.choice(self.error_msgs)
                    return error_msg
                else:
                    return "❌ 文件导出失败，请稍后重试"

            # 文件导出成功后，再尝试上传文件
            file_url = ""
            if self.enable_upload:
                try:
                    upload_result = await self._upload_file_to_chatglm(filepath, filename)

                    if upload_result and "file_url" in upload_result:
                        file_url = upload_result["file_url"]

                        # 检查是否启用豆包插件生成总结
                        if self.enable_doubao and file_url:
                            # 调用豆包插件生成总结并发送图片
                            summary = await self._generate_summary_with_doubao(bot, group_id, file_url, group_name)

                            # 成功发送图片后，不再发送任何@消息
                            # 直接返回空字符串，表示不需要额外回复
                            return ""

                        # 如果未启用豆包总结，返回导出结果
                        return f"✅ 导出成功！\n\n文件已保存到：{filepath}\n\n文件下载链接：{file_url}"
                    else:
                        logger.warning(f"[{self.plugin_name}] 文件上传失败，将只返回本地路径")
                except Exception as e:
                    logger.error(f"[{self.plugin_name}] 文件上传过程中出现异常: {str(e)}")

            # 如果上传失败或未启用上传，返回本地路径
            return f"✅ 导出成功！\n文件已保存到：{filepath}"

        except Exception as e:
            logger.error(f"[{self.plugin_name}] 导出群聊总结失败: {str(e)}")
            if self.natural_response:
                error_msg = random.choice(self.error_msgs)
                return error_msg
            else:
                return "❌ 导出过程中出现错误，请稍后重试"

    async def _get_message_stats(self, group_id: str, start_time: str, end_time: str) -> Dict[str, Any]:
        """获取消息统计信息"""
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()

            # 获取消息总数和活跃用户数
            query = '''
                SELECT COUNT(*) as total_messages,
                       COUNT(DISTINCT sender_wxid) as active_users
                FROM chat_messages
                WHERE group_id = ? AND create_time BETWEEN ? AND ?
            '''
            cursor.execute(query, (group_id, start_time, end_time))
            total_stats = cursor.fetchone()

            # 获取消息类型分布
            query = '''
                SELECT msg_type, COUNT(*) as count
                FROM chat_messages
                WHERE group_id = ? AND create_time BETWEEN ? AND ?
                GROUP BY msg_type
                ORDER BY count DESC
            '''
            cursor.execute(query, (group_id, start_time, end_time))
            type_stats = cursor.fetchall()

            # 获取活跃用户排行榜（按wxid统计）
            query = '''
                SELECT sender_wxid, COUNT(*) as message_count
                FROM chat_messages
                WHERE group_id = ? AND create_time BETWEEN ? AND ?
                GROUP BY sender_wxid
                ORDER BY message_count DESC
                LIMIT 10
            '''
            cursor.execute(query, (group_id, start_time, end_time))
            user_stats = cursor.fetchall()

            # 获取消息时间分布（按小时）
            query = '''
                SELECT strftime('%H', create_time) as hour, COUNT(*) as count
                FROM chat_messages
                WHERE group_id = ? AND create_time BETWEEN ? AND ?
                GROUP BY hour
                ORDER BY hour
            '''
            cursor.execute(query, (group_id, start_time, end_time))
            time_stats = cursor.fetchall()

            conn.close()

            stats = {
                'total_messages': total_stats[0],
                'active_users': total_stats[1],
                'type_distribution': {row[0]: row[1] for row in type_stats},
                'user_ranking': [(row[0], row[1]) for row in user_stats],
                'time_distribution': {row[0]: row[1] for row in time_stats}
            }

            return stats
        except Exception as e:
            logger.error(f"[{self.plugin_name}] 获取统计信息失败: {str(e)}")
            return {}

    def _format_time_distribution(self, time_stats: Dict[str, int]) -> str:
        """格式化时间分布数据"""
        if not time_stats:
            return "暂无数据"

        # 计算每个时间段的消息数量
        time_ranges = {
            '凌晨 (00-06)': sum(time_stats.get(str(hour).zfill(2), 0) for hour in range(0, 6)),
            '上午 (06-12)': sum(time_stats.get(str(hour).zfill(2), 0) for hour in range(6, 12)),
            '下午 (12-18)': sum(time_stats.get(str(hour).zfill(2), 0) for hour in range(12, 18)),
            '晚上 (18-24)': sum(time_stats.get(str(hour).zfill(2), 0) for hour in range(18, 24))
        }

        # 计算总消息数
        total = sum(time_ranges.values())
        if total == 0:
            return "暂无数据"

        # 生成时间分布文本
        result = []
        for time_range, count in time_ranges.items():
            percentage = (count / total) * 100
            result.append(f"{time_range}: {count}条 ({percentage:.1f}%)")

        return "\n".join(result)

    async def _generate_statistics(self, bot: WechatAPIClient, group_id: str, param: str, contact_info: dict = None) -> str:
        """生成群聊统计"""
        try:
            # 检查群聊是否启用了功能
            if group_id not in self.enabled_groups:
                if self.natural_response:
                    error_msg = random.choice(self.error_msgs)
                    return error_msg
                else:
                    return "❌ 该群聊未启用统计功能，请先发送'开启群聊统计'"

            # 解析时间范围
            start_time, end_time = self._parse_time_range(param)
            if not start_time or not end_time:
                if self.natural_response:
                    error_msg = random.choice(self.error_msgs)
                    return error_msg
                else:
                    return "❌ 时间格式错误，请使用 YYYY-MM-DD 格式，例如：2024-04-01 2024-04-08"

            # 获取群聊名称
            group_name = await self._get_group_name(bot, group_id)

            # 获取群成员列表
            member_list = await bot.get_chatroom_member_list(group_id)

            # 创建成员字典 - 优先使用DisplayName，其次使用NickName
            member_dict = {}
            for member in member_list:
                wxid = member.get('UserName', '')
                if wxid:
                    # 优先使用DisplayName（群昵称），如果没有再使用NickName（微信昵称）
                    nickname = member.get('DisplayName', '')
                    if not nickname:
                        nickname = member.get('NickName', '')
                    if not nickname:
                        nickname = wxid

                    member_dict[wxid] = nickname

                    # 更新该用户的所有历史消息记录的昵称
                    await self._update_user_nickname_history(group_id, wxid, nickname)

            # 获取统计信息
            stats = await self._get_message_stats(group_id, start_time, end_time)
            if not stats:
                if self.natural_response:
                    error_msg = random.choice(self.error_msgs)
                    return error_msg
                else:
                    return f"❌ 在 {start_time} 到 {end_time} 期间没有找到消息记录"

            # 生成统计报告
            report = f"📊 群聊统计报告\n\n"
            report += f"群聊名称：{group_name}\n"
            report += f"统计时间：{start_time} 至 {end_time}\n\n"

            # 基本信息
            report += "📈 基本信息\n"
            report += f"总消息数：{stats['total_messages']}条\n"
            report += f"活跃用户数：{stats['active_users']}人\n\n"

            # 消息类型分布
            report += "📝 消息类型分布\n"
            if stats['type_distribution']:
                # 所有消息类型的友好名称映射
                type_name_map = {
                    'text': '文本消息',
                    'emoji': '表情消息',
                    'image': '图片消息',
                    'voice': '语音消息',
                    'video': '视频消息',
                    'file': '文件消息',
                    'quote': '引用消息',
                    'pat': '拍一拍消息',
                    'at': '@消息',
                    'system': '系统消息',
                    'article': '文章消息',
                    'xml': 'XML消息'
                }

                for msg_type, count in stats['type_distribution'].items():
                    # 显示更友好的消息类型名称
                    type_name = type_name_map.get(msg_type, msg_type)

                    percentage = (count / stats['total_messages']) * 100
                    report += f"{type_name}：{count}条 ({percentage:.1f}%)\n"
            else:
                report += "暂无消息类型数据\n"
            report += "\n"

            # 活跃用户排行榜
            report += "👥 活跃用户排行榜\n"
            if stats['user_ranking']:
                for i, (user, count) in enumerate(stats['user_ranking'], 1):
                    # 获取用户群昵称
                    user_nickname = member_dict.get(user, user)
                    percentage = (count / stats['total_messages']) * 100
                    report += f"{i}. {user_nickname}：{count}条 ({percentage:.1f}%)\n"
            else:
                report += "暂无用户活跃数据\n"
            report += "\n"

            # 消息时间分布
            report += "⏰ 消息时间分布\n"
            report += self._format_time_distribution(stats['time_distribution'])

            return report

        except Exception as e:
            logger.error(f"[{self.plugin_name}] 生成统计报告失败: {str(e)}")
            if self.natural_response:
                error_msg = random.choice(self.error_msgs)
                return error_msg
            else:
                return "❌ 生成统计报告过程中出现错误，请稍后重试"

    async def _toggle_feature(self, feature: str, enable: bool, group_id: str, bot: WechatAPIClient) -> str:
        """开启/关闭群聊功能"""
        try:
            # 更新群聊功能开关状态
            if feature == "summary":
                if enable:
                    self.enabled_groups[group_id] = True
                else:
                    # 检查是否还有其他功能启用
                    conn = sqlite3.connect(self.db_path)
                    cursor = conn.cursor()
                    cursor.execute('SELECT statistics_enabled FROM feature_status WHERE group_id = ?', (group_id,))
                    result = cursor.fetchone()
                    conn.close()

                    if not result or not result[0]:
                        self.enabled_groups.pop(group_id, None)
            elif feature == "statistics":
                if enable:
                    self.enabled_groups[group_id] = True
                else:
                    # 检查是否还有其他功能启用
                    conn = sqlite3.connect(self.db_path)
                    cursor = conn.cursor()
                    cursor.execute('SELECT summary_enabled FROM feature_status WHERE group_id = ?', (group_id,))
                    result = cursor.fetchone()
                    conn.close()

                    if not result or not result[0]:
                        self.enabled_groups.pop(group_id, None)

            # 保存功能状态到数据库
            self._save_feature_status(group_id, feature, enable)

            # 获取群聊名称
            group_name = await self._get_group_name(bot, group_id)

            status = "开启" if enable else "关闭"
            return f"✅ 群聊 {group_name} 的{feature}功能已{status}"

        except Exception as e:
            logger.error(f"[{self.plugin_name}] 更新群聊功能失败: {str(e)}")
            if self.natural_response:
                error_msg = random.choice(self.error_msgs)
                return error_msg
            else:
                return "❌ 更新群聊功能失败，请稍后重试"

    @on_emoji_message
    async def handle_emoji(self, bot: WechatAPIClient, message: dict):
        """处理表情消息"""
        if not self.enable:
            return

        try:
            # 检查必要的字段
            if not all(key in message for key in ["FromWxid", "SenderWxid"]):
                logger.error(f"[{self.plugin_name}] 表情消息缺少必要字段: {message}")
                return

            # 检查群聊是否启用了功能
            group_id = message["FromWxid"]
            if group_id not in self.enabled_groups:
                return

            # 为表情消息创建一个合适的Content
            content = "[表情消息]"
            if "Md5" in message:
                content = f"[表情消息:{message['Md5']}]"

            # 构建完整的消息对象
            emoji_message = message.copy()
            emoji_message["Content"] = content
            emoji_message["Type"] = "emoji"

            # 调用保存消息的方法
            await self._save_message(bot, emoji_message)

        except Exception as e:
            logger.error(f"[{self.plugin_name}] 处理表情消息失败: {str(e)}")
            logger.error(f"[{self.plugin_name}] 表情消息内容: {message}")

    @on_image_message
    async def handle_image(self, bot: WechatAPIClient, message: dict):
        """处理图片消息"""
        if not self.enable:
            return

        try:
            # 检查必要的字段
            if not all(key in message for key in ["FromWxid", "SenderWxid"]):
                logger.error(f"[{self.plugin_name}] 图片消息缺少必要字段: {message}")
                return

            # 检查群聊是否启用了功能
            group_id = message["FromWxid"]
            if group_id not in self.enabled_groups:
                return

            # 为图片消息创建一个合适的Content
            content = "[图片消息]"
            if "Md5" in message:
                content = f"[图片消息:{message['Md5']}]"

            # 构建完整的消息对象
            image_message = message.copy()
            image_message["Content"] = content
            image_message["Type"] = "image"

            # 调用保存消息的方法
            await self._save_message(bot, image_message)

        except Exception as e:
            logger.error(f"[{self.plugin_name}] 处理图片消息失败: {str(e)}")
            logger.error(f"[{self.plugin_name}] 图片消息内容: {message}")

    @on_voice_message
    async def handle_voice(self, bot: WechatAPIClient, message: dict):
        """处理语音消息"""
        if not self.enable:
            return

        try:
            # 检查必要的字段
            if not all(key in message for key in ["FromWxid", "SenderWxid"]):
                logger.error(f"[{self.plugin_name}] 语音消息缺少必要字段: {message}")
                return

            # 检查群聊是否启用了功能
            group_id = message["FromWxid"]
            if group_id not in self.enabled_groups:
                return

            # 为语音消息创建一个合适的Content
            content = "[语音消息]"
            if "VoiceLength" in message:
                content = f"[语音消息:{message['VoiceLength']}秒]"

            # 构建完整的消息对象
            voice_message = message.copy()
            voice_message["Content"] = content
            voice_message["Type"] = "voice"

            # 调用保存消息的方法
            await self._save_message(bot, voice_message)

        except Exception as e:
            logger.error(f"[{self.plugin_name}] 处理语音消息失败: {str(e)}")
            logger.error(f"[{self.plugin_name}] 语音消息内容: {message}")

    @on_video_message
    async def handle_video(self, bot: WechatAPIClient, message: dict):
        """处理视频消息"""
        if not self.enable:
            return

        try:
            # 检查必要的字段
            if not all(key in message for key in ["FromWxid", "SenderWxid"]):
                logger.error(f"[{self.plugin_name}] 视频消息缺少必要字段: {message}")
                return

            # 检查群聊是否启用了功能
            group_id = message["FromWxid"]
            if group_id not in self.enabled_groups:
                return

            # 为视频消息创建一个合适的Content
            content = "[视频消息]"
            if "Md5" in message:
                content = f"[视频消息:{message['Md5']}]"

            # 构建完整的消息对象
            video_message = message.copy()
            video_message["Content"] = content
            video_message["Type"] = "video"

            # 调用保存消息的方法
            await self._save_message(bot, video_message)

        except Exception as e:
            logger.error(f"[{self.plugin_name}] 处理视频消息失败: {str(e)}")
            logger.error(f"[{self.plugin_name}] 视频消息内容: {message}")

    @on_file_message
    async def handle_file(self, bot: WechatAPIClient, message: dict):
        """处理文件消息"""
        if not self.enable:
            return

        try:
            # 检查必要的字段
            if not all(key in message for key in ["FromWxid", "SenderWxid"]):
                logger.error(f"[{self.plugin_name}] 文件消息缺少必要字段: {message}")
                return

            # 检查群聊是否启用了功能
            group_id = message["FromWxid"]
            if group_id not in self.enabled_groups:
                return

            # 为文件消息创建一个合适的Content
            content = "[文件消息]"
            if "FileName" in message:
                content = f"[文件消息:{message['FileName']}]"

            # 构建完整的消息对象
            file_message = message.copy()
            file_message["Content"] = content
            file_message["Type"] = "file"

            # 调用保存消息的方法
            await self._save_message(bot, file_message)

        except Exception as e:
            logger.error(f"[{self.plugin_name}] 处理文件消息失败: {str(e)}")
            logger.error(f"[{self.plugin_name}] 文件消息内容: {message}")

    @on_quote_message
    async def handle_quote(self, bot: WechatAPIClient, message: dict):
        """处理引用消息"""
        if not self.enable:
            return

        try:
            # 检查必要的字段
            if not all(key in message for key in ["FromWxid", "SenderWxid"]):
                logger.error(f"[{self.plugin_name}] 引用消息缺少必要字段: {message}")
                return

            # 检查群聊是否启用了功能
            group_id = message["FromWxid"]
            if group_id not in self.enabled_groups:
                return

            # 为引用消息创建一个合适的Content
            content = "[引用消息]"
            if "Content" in message:
                content = f"[引用消息:{message['Content']}]"

            # 构建完整的消息对象
            quote_message = message.copy()
            quote_message["Content"] = content
            quote_message["Type"] = "quote"

            # 调用保存消息的方法
            await self._save_message(bot, quote_message)

        except Exception as e:
            logger.error(f"[{self.plugin_name}] 处理引用消息失败: {str(e)}")
            logger.error(f"[{self.plugin_name}] 引用消息内容: {message}")

    @on_pat_message
    async def handle_pat(self, bot: WechatAPIClient, message: dict):
        """处理拍一拍消息"""
        if not self.enable:
            return

        try:
            # 检查必要的字段
            if not all(key in message for key in ["FromWxid", "SenderWxid"]):
                logger.error(f"[{self.plugin_name}] 拍一拍消息缺少必要字段: {message}")
                return

            # 检查群聊是否启用了功能
            group_id = message["FromWxid"]
            if group_id not in self.enabled_groups:
                return

            # 为拍一拍消息创建一个合适的Content
            content = "[拍一拍消息]"
            if "PatContent" in message:
                content = f"[拍一拍消息:{message['PatContent']}]"

            # 构建完整的消息对象
            pat_message = message.copy()
            pat_message["Content"] = content
            pat_message["Type"] = "pat"

            # 调用保存消息的方法
            await self._save_message(bot, pat_message)

        except Exception as e:
            logger.error(f"[{self.plugin_name}] 处理拍一拍消息失败: {str(e)}")
            logger.error(f"[{self.plugin_name}] 拍一拍消息内容: {message}")

    @on_at_message
    async def handle_at(self, bot: WechatAPIClient, message: dict):
        """处理@消息"""
        if not self.enable:
            return

        try:
            # 检查必要的字段
            if not all(key in message for key in ["FromWxid", "SenderWxid"]):
                logger.error(f"[{self.plugin_name}] @消息缺少必要字段: {message}")
                return

            # 检查群聊是否启用了功能
            group_id = message["FromWxid"]
            if group_id not in self.enabled_groups:
                return

            # 为@消息创建一个合适的Content
            content = "[@消息]"
            if "Content" in message:
                content = f"[@消息:{message['Content']}]"

            # 构建完整的消息对象
            at_message = message.copy()
            at_message["Content"] = content
            at_message["Type"] = "at"

            # 调用保存消息的方法
            await self._save_message(bot, at_message)

        except Exception as e:
            logger.error(f"[{self.plugin_name}] 处理@消息失败: {str(e)}")
            logger.error(f"[{self.plugin_name}] @消息内容: {message}")

    @on_system_message
    async def handle_system(self, bot: WechatAPIClient, message: dict):
        """处理系统消息"""
        if not self.enable:
            return

        try:
            # 检查必要的字段
            if not all(key in message for key in ["FromWxid", "SenderWxid"]):
                logger.error(f"[{self.plugin_name}] 系统消息缺少必要字段: {message}")
                return

            # 检查群聊是否启用了功能
            group_id = message["FromWxid"]
            if group_id not in self.enabled_groups:
                return

            # 为系统消息创建一个合适的Content
            content = "[系统消息]"
            if "Content" in message:
                content = f"[系统消息:{message['Content']}]"

            # 构建完整的消息对象
            system_message = message.copy()
            system_message["Content"] = content
            system_message["Type"] = "system"

            # 调用保存消息的方法
            await self._save_message(bot, system_message)

        except Exception as e:
            logger.error(f"[{self.plugin_name}] 处理系统消息失败: {str(e)}")
            logger.error(f"[{self.plugin_name}] 系统消息内容: {message}")

    @on_article_message
    async def handle_article(self, bot: WechatAPIClient, message: dict):
        """处理文章消息"""
        if not self.enable:
            return

        try:
            # 检查必要的字段
            if not all(key in message for key in ["FromWxid", "SenderWxid"]):
                logger.error(f"[{self.plugin_name}] 文章消息缺少必要字段: {message}")
                return

            # 检查群聊是否启用了功能
            group_id = message["FromWxid"]
            if group_id not in self.enabled_groups:
                return

            # 为文章消息创建一个合适的Content
            content = "[文章消息]"
            if "Title" in message:
                content = f"[文章消息:{message['Title']}]"

            # 构建完整的消息对象
            article_message = message.copy()
            article_message["Content"] = content
            article_message["Type"] = "article"

            # 调用保存消息的方法
            await self._save_message(bot, article_message)

        except Exception as e:
            logger.error(f"[{self.plugin_name}] 处理文章消息失败: {str(e)}")
            logger.error(f"[{self.plugin_name}] 文章消息内容: {message}")

    @on_xml_message
    async def handle_xml(self, bot: WechatAPIClient, message: dict):
        """处理XML消息"""
        if not self.enable:
            return

        try:
            # 检查必要的字段
            if not all(key in message for key in ["FromWxid", "SenderWxid"]):
                logger.error(f"[{self.plugin_name}] XML消息缺少必要字段: {message}")
                return

            # 检查群聊是否启用了功能
            group_id = message["FromWxid"]
            if group_id not in self.enabled_groups:
                return

            # 为XML消息创建一个合适的Content
            content = "[XML消息]"
            if "XmlType" in message:
                content = f"[XML消息:类型{message['XmlType']}]"

            # 构建完整的消息对象
            xml_message = message.copy()
            xml_message["Content"] = content
            xml_message["Type"] = "xml"

            # 调用保存消息的方法
            await self._save_message(bot, xml_message)

        except Exception as e:
            logger.error(f"[{self.plugin_name}] 处理XML消息失败: {str(e)}")
            logger.error(f"[{self.plugin_name}] XML消息内容: {message}")