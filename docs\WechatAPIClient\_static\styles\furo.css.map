{"version": 3, "file": "styles/furo.css", "mappings": "AAAA,2EAA2E,CAU3E,KACE,gBAAiB,CACjB,6BACF,CASA,KACE,QACF,CAMA,KACE,aACF,CAOA,GACE,aAAc,CACd,cACF,CAUA,GACE,sBAAuB,CACvB,QAAS,CACT,gBACF,CAOA,IACE,+BAAiC,CACjC,aACF,CASA,EACE,4BACF,CAOA,YACE,kBAAmB,CACnB,yBAA0B,CAC1B,gCACF,CAMA,SAEE,kBACF,CAOA,cAGE,+BAAiC,CACjC,aACF,CAeA,QAEE,aAAc,CACd,aAAc,CACd,iBAAkB,CAClB,uBACF,CAEA,IACE,aACF,CAEA,IACE,SACF,CASA,IACE,iBACF,CAUA,sCAKE,mBAAoB,CACpB,cAAe,CACf,gBAAiB,CACjB,QACF,CAOA,aAEE,gBACF,CAOA,cAEE,mBACF,CAMA,gDAIE,yBACF,CAMA,wHAIE,iBAAkB,CAClB,SACF,CAMA,4GAIE,6BACF,CAMA,SACE,0BACF,CASA,OACE,qBAAsB,CACtB,aAAc,CACd,aAAc,CACd,cAAe,CACf,SAAU,CACV,kBACF,CAMA,SACE,uBACF,CAMA,SACE,aACF,CAOA,6BAEE,qBAAsB,CACtB,SACF,CAMA,kFAEE,WACF,CAOA,cACE,4BAA6B,CAC7B,mBACF,CAMA,yCACE,uBACF,CAOA,6BACE,yBAA0B,CAC1B,YACF,CASA,QACE,aACF,CAMA,QACE,iBACF,CAiBA,kBACE,YACF,CCvVA,aAcE,kEACE,uBAOF,WACE,iDAMF,kCACE,wBAEF,qCAEE,uBADA,uBACA,CAEF,SACE,wBAtBA,CCpBJ,iBAGE,qBAEA,sBACA,0BAFA,oBAHA,4BACA,oBAKA,6BAIA,2CAFA,mBACA,sCAFA,4BAGA,CAEF,gBACE,aCTF,KCGE,mHAEA,wGAEA,wCAAyC,CAEzC,wBAAyB,CACzB,wBAAyB,CACzB,4BAA6B,CAC7B,yBAA0B,CAC1B,2BAA4B,CAG5B,sDAAuD,CACvD,gDAAiD,CACjD,wDAAyD,CAGzD,0CAA2C,CAC3C,gDAAiD,CACjD,gDAAiD,CAKjD,gCAAiC,CACjC,sCAAuC,CAGvC,2CAA4C,CAG5C,uCAAwC,CCjCxC,+FAGA,uBAAwB,CAGxB,iCAAkC,CAClC,kCAAmC,CAEnC,+BAAgC,CAChC,sCAAuC,CACvC,sCAAuC,CACvC,qGAIA,mDAAoD,CAEpD,mCAAoC,CACpC,8CAA+C,CAC/C,gDAAiD,CACjD,kCAAmC,CACnC,6DAA8D,CAG9D,6BAA8B,CAC9B,6BAA8B,CAC9B,+BAAgC,CAChC,kCAAmC,CACnC,kCAAmC,CCPjC,+jBCYA,iqCAZF,iaCVA,8KAOA,4SAWA,4SAUA,0CACA,gEAGA,0CAGA,gEAGA,yCACA,+DAIA,4CACA,kEAGA,wCAUA,8DACA,uCAGA,4DACA,sCACA,2DAGA,4CACA,kEACA,uCAGA,6DACA,2GAGA,sHAEA,yFAEA,+CACA,+EAGA,4MAOA,gCACA,sHAIA,kCACA,uEACA,gEACA,4DACA,kEAGA,2DACA,sDACA,0CACA,8CACA,wGAGA,0BACA,iCAGA,+DACA,+BACA,sCACA,+DAEA,kGACA,oCACA,yDACA,sCL7HF,kCAEA,sDAIA,0CK2HE,kEAIA,oDACA,sDAGA,oCACA,oEAEA,0DACA,qDAIA,oDACA,6DAIA,iEAIA,2DAIA,2DAGA,4DACA,gEAIA,gEAEA,gFAEA,oNASA,qDLxKE,gFAGE,4DAIF,oEKkHF,yEAEA,6DAGA,0DAEA,uDACA,qDACA,wDAIA,6DAIA,yDACA,2DAIA,uCAGA,wCACA,sDAGA,+CAGA,6DAEA,iDACA,+DAEA,wDAEA,sEAMA,0DACA,sBACA,mEL9JI,wEAEA,iCACE,+BAMN,wEAGA,iCACE,kFAEA,uEAIF,gEACE,8BAGF,qEMvDA,sCAKA,wFAKA,iCAIA,0BAWA,iCACA,4BACA,mCAGA,+BAEA,sCACA,4BAEA,mCAEA,sCAKA,sDAIA,gCAEA,gEAQF,wCAME,sBACA,kCAKA,uBAEA,gEAIA,2BAIA,mCAEA,qCACA,iCAGE,+BACA,wEAEE,iCACA,kFAGF,6BACA,0CACF,kCAEE,8BACE,8BACA,qEAEE,sCACA,wFCnFN,iCAGF,2DAEE,4BACA,oCAGA,mIAGA,4HACE,gEAMJ,+CAGE,sBACA,yCAEF,uBAEE,sEAKA,gDACA,kEAGA,iFAGE,YAGF,EACA,4HAQF,mBACE,6BACA,mBACA,wCACA,wCACA,2CAIA,eAGA,mBAKE,mBAGA,CAJA,uCACA,iBAFF,gBACE,CAKE,mBACA,mBAGJ,oBAIF,+BAGE,kDACA,OADA,kBAGA,CAFA,gBAEA,mBACA,oBAEA,sCACA,OAGF,cAHE,WAGF,GAEE,oBACA,CAHF,gBAGE,CC9Gc,YDiHd,+CAIF,SAEE,CAPF,UACE,wBAMA,4BAEA,GAGA,uBACA,CAJA,yBAGA,CACA,iDAKA,2CAGA,2DAQA,iBACA,uCAGA,kEAKE,SAKJ,8BACE,yDACA,2BAEA,oBACA,8BAEA,yDAEE,4BAEJ,uCACE,CACA,iEAGA,CAEA,wCACE,uBACA,kDAEA,0DAEE,CAJF,oBAIE,0GAWN,aACE,CAHA,YAGA,4HASA,+CAGF,sBACE,WACA,WAQA,4BAFF,0CAEE,CARA,qCAsBA,CAdA,iBAEA,kBACE,aADF,4BACE,WAMF,2BAGF,qCAEE,CAXE,UAWF,+BAGA,uBAEA,SAEA,0CAIE,CANF,qCAEA,CAIE,2DACE,gBAIN,+CAIA,CAEA,kDAKE,CAPF,8BAEA,CAOE,YACA,CAjBI,2BAGN,CAHM,WAcJ,UAGA,CAEA,2GAIF,iCAGE,8BAIA,qBACA,oBACF,uBAOI,0CAIA,CATF,6DAKE,CALF,sBASE,qCAKF,CACE,cACA,CAFF,sBAEE,CACA,+BAEA,qBAEE,WAKN,aACE,sCAGA,mBAEA,6BAMA,kCACA,CAJA,sBACA,aAEA,CAJA,eACA,MAIA,2FAEA,UAGA,YACA,sBACE,8BAEA,CALF,aACA,WAIE,OACA,oBAEF,uBACE,WAEF,YAFE,UAEF,eAgBA,kBACE,CAhBA,qDAQF,qCAGF,CAGI,YACF,CAJF,2BAGI,CAEA,eACA,qBAGA,mEAEA,qBACA,8BAIA,kBADF,kBACE,yBAEJ,oCAGI,qDAIJ,+BAGI,oCAEA,+CAQF,4CACE,yBACF,2BAOE,sBACA,CAHA,WACA,CAFF,cACE,CAJA,YAGF,CAEE,SAEA,mBAGA,kDAEE,CAJF,cAEA,cAEE,sBAEA,mBADA,YACA,uBACA,mDACE,CADF,YACE,iDAEA,uCAEN,+DAOE,mBADF,sBACE,mBAGF,aACE,sCAIA,aADF,WACE,CAKF,SACE,CAHJ,kBAEE,CAJE,gBAEJ,CAHI,iBAMA,yFAKA,aACA,eACA,cElbJ,iBAEE,aADA,iBACA,6BAEA,kCAEA,SACA,UAIA,gCACA,CALA,SAEA,SAEA,CAJA,0EAEA,CAFA,OAKA,CAGA,mDACE,iBAGF,gCACE,CADF,UACE,aAEJ,iCAEE,CAFF,UAEE,wCAEA,WACA,WADA,UACA,CACA,4CAGA,MACA,CADA,KACA,wCACA,UAGA,CAJA,UAIA,6DAUA,0CACE,CAFF,mBAEE,wEACA,CAVA,YACA,CAMF,mBAJE,OAOA,gBAJJ,gCACE,CANE,cACA,CAHA,oBACA,CAGA,QAGJ,CAII,0BACA,CADA,UACA,wCAEJ,kBACE,0DACA,gCACE,kBACA,CADA,YACA,oEACA,2CAMF,mDAII,CALN,YACE,CANE,cAKJ,CACE,iBAII,kEACA,yCACE,kDACA,yDACE,+CACA,uBANN,CAMM,+BANN,uCACE,qDACA,4BAEE,mBADA,0CACA,CADA,qBACA,0DACE,wCACA,sGALJ,oCACA,sBACE,kBAFF,UAEE,2CACA,wFACE,cACA,kEANN,uBACE,iDACA,CADA,UACA,0DACE,wDAEE,iEACA,qEANN,sCACE,CAGE,iBAHF,gBAGE,qBACE,CAJJ,uBACA,gDACE,wDACA,6DAHF,2CACA,CADA,gBACA,eACE,CAGE,sBANN,8BACE,CAII,iBAFF,4DACA,WACE,YADF,uCACE,6EACA,2BANN,8CACE,kDACA,0CACE,8BACA,yFACE,sBACA,sFALJ,mEACA,sBACE,kEACA,6EACE,uCACA,kEALJ,qGAEE,kEACA,6EACE,uCACA,kEALJ,8CACA,uDACE,sEACA,2EACE,sCACA,iEALJ,mGACA,qCACE,oDACA,0DACE,6GACA,gDAGR,yDCrEA,sEACE,CACA,6GACE,gEACF,iGAIF,wFACE,qDAGA,mGAEE,2CAEF,4FACE,gCACF,wGACE,8DAEE,6FAIA,iJAKN,6GACE,gDAKF,yDACA,qCAGA,6BACA,kBACA,qDAKA,oCAEA,+DAGA,2CAGE,oDAIA,oEAEE,qBAGJ,wDAEE,uCAEF,kEAGA,8CAEA,uDAIF,gEAIE,6BACA,gEAIA,+CACE,0EAIF,sDAEE,+DAGF,sCACA,8BACE,oCAEJ,wBACE,4FAEE,gBAEJ,yGAGI,kBAGJ,CCnHE,2MCFF,oBAGE,wGAKA,iCACE,CADF,wBACE,8GAQA,mBCjBJ,2GAIE,mBACA,6HAMA,YACE,mIAYF,eACA,CAHF,YAGE,4FAGE,8BAKF,uBAkBE,sCACA,CADA,qBAbA,wCAIA,CALF,8BACE,CADF,gBAKE,wCACA,CAOA,kDACA,CACA,kCAKF,6BAGA,4CACE,kDACA,eAGF,cACE,aACA,iBACA,yBACA,8BACA,WAGJ,2BACE,cAGA,+BACA,CAHA,eAGA,wCACA,YACA,iBACA,uEAGA,0BACA,2CAEA,8EAGI,qBACA,CAFF,kBAEE,kBAGN,0CAGE,mCAGA,4BAIA,gEACE,qCACA,8BAEA,gBACA,+CACA,iCAEF,iCAEE,gEACA,qCAGF,8BAEE,+BAIA,yCAEE,qBADA,gBACA,yBAKF,eACA,CAFF,YACE,CACA,iBACA,qDAEA,mDCvIJ,2FAOE,iCACA,CAEA,eACA,CAHA,kBAEA,CAFA,wBAGA,8BACA,eACE,CAFF,YAEE,0BACA,8CAGA,oBACE,oCAGA,kBACE,8DAEA,iBAEN,UACE,8BAIJ,+CAEE,qDAEF,kDAIE,YAEF,CAFE,YAEF,CCpCE,mFADA,kBAKE,CAJF,IAGA,aACE,mCAGA,iDACE,+BAEJ,wBAEE,mBAMA,6CAEF,CAJE,mBAEA,CAEF,kCAGE,CARF,kBACE,CAHA,eAUA,YACA,mBACA,CADA,UACA,wCC9BF,oBDkCE,wBCnCJ,uCACE,+BACA,+DACA,sBAGA,qBCDA,6CAIE,CAPF,uBAGA,CDGE,oBACF,yDAEE,CCDE,2CAGF,CAJA,kCACE,CDJJ,YACE,CAIA,eCTF,CDKE,uBCMA,gCACE,YAEF,oCAEE,wBACA,0BAIF,iBAEA,cADF,UACE,uBAEA,iCAEA,wCAEA,6CAMA,CAYF,gCATI,4BASJ,CAZE,mCAEE,iCAUJ,4BAGE,4DADA,+BACA,CAHF,qBAGE,sCACE,OAEF,iBAHA,SAGA,iHACE,2DAKF,CANA,8EAMA,uSAEE,kBAEF,+FACE,yCCjEJ,WACA,yBAGA,uBACA,gBAEA,uCAIA,CAJA,iCAIA,uCAGA,UACE,gBACA,qBAEA,0CClBJ,gBACE,KAGF,qBACE,YAGF,CAHE,cAGF,gCAEE,mBACA,iEAEA,oCACA,wCAEA,sBACA,WAEA,CAFA,YAEA,8EAEA,mCAFA,iBAEA,6BAIA,wEAKA,sDAIE,CARF,mDAIA,CAIE,cAEF,8CAIA,oBAFE,iBAEF,8CAGE,eAEF,CAFE,YAEF,OAEE,kBAGJ,CAJI,eACA,CAFF,mBAKF,yCCjDE,oBACA,CAFA,iBAEA,uCAKE,iBACA,qCAGA,mBCZJ,CDWI,gBCXJ,6BAEE,eACA,sBAGA,eAEA,sBACA,oDACA,iGAMA,gBAFE,YAEF,8FAME,iJCnBF,YACA,gNAWE,gDAEF,iSAaE,kBACE,gHAKF,oCACE,eACF,CADE,UACF,8CACE,gDACF,wCACE,oBCxCJ,oBAEF,6BACE,QACE,kDAGF,yBACE,kDAmBA,kDAEF,CAhBA,+CAaA,CAbA,oBAaA,0FACE,CADF,gGAfF,cACE,gBACA,CAaA,0BAGA,mQACE,gBAGF,oMACE,iBACA,CAFF,eACE,CADF,gBAEE,aAGJ,iCAEE,CAFF,wCAEE,wBAUE,+VAIE,uEAHA,2BAGA,wXAKJ,iDAGF,CARM,+CACE,iDAIN,CALI,gBAQN,mHACE,gBAGF,2DACE,0EAOA,0EAGF,gBAEE,6DC/EA,kDACA,gCACA,qDAGA,qBACA,qDCFA,cACA,eAEA,yBAGF,sBAEE,iBACA,sNAWA,iBACE,kBACA,wRAgBA,kBAEA,iOAgBA,uCACE,uEAEA,kBAEF,qUAuBE,iDAIJ,CACA,geCxFF,4BAEE,CAQA,6JACA,iDAIA,sEAGA,mDAOF,iDAGE,4DAIA,8CACA,qDAEE,eAFF,cAEE,oBAEF,uBAFE,kCAGA,eACA,iBACA,mBAIA,mDACA,CAHA,uCAEA,CAJA,0CACA,CAIA,gBAJA,gBACA,oBADA,gBAIA,wBAEJ,gBAGE,6BACA,YAHA,iBAGA,gCACA,iEAEA,6CACA,sDACA,0BADA,wBACA,0BACA,oIAIA,mBAFA,YAEA,qBACA,0CAIE,uBAEF,CAHA,yBACE,CAEF,iDACE,mFAKJ,oCACE,CANE,aAKJ,CACE,qEAIA,YAFA,WAEA,CAHA,aACA,CAEA,gBACE,4BACA,sBADA,aACA,gCAMF,oCACA,yDACA,2CAEA,qBAGE,kBAEA,CACA,mCAIF,CARE,YACA,CAOF,iCAEE,CAPA,oBACA,CAQA,oBACE,uDAEJ,sDAGA,CAHA,cAGA,0BACE,oDAIA,oCACA,4BACA,sBAGA,cAEA,oFAGA,sBAEA,yDACE,CAIF,iBAJE,wBAIF,6CAHE,6CAKA,eACA,aACA,CADA,cACA,yCAGJ,kBACE,CAKA,iDAEA,CARF,aACE,4CAGA,kBAIA,wEAGA,wDAGA,kCAOA,iDAGA,CAPF,WAEE,sCAEA,CAJF,2CACE,CAMA,qCACA,+BARF,kBACE,qCAOA,iBAsBA,sBACE,CAvBF,WAKA,CACE,0DAIF,CALA,uDACE,CANF,sBAqBA,4CACA,CALA,gRAIA,YAEE,6CAEN,mCAEE,+CASA,6EAIA,4BChNA,SDmNA,qFCnNA,gDACA,sCAGA,qCACA,sDACA,CAKA,kDAGA,CARA,0CAQA,kBAGA,YACA,sBACA,iBAFA,gBADF,YACE,CAHA,SAKA,kBAEA,SAFA,iBAEA,uEAGA,CAEE,6CAFF,oCAgBI,CAdF,yBACE,qBACF,CAGF,oBACE,CAIF,WACE,CALA,2CAGA,uBACF,CACE,mFAGE,CALF,qBAEA,UAGE,gCAIF,sDAEA,CALE,oCAKF,yCC7CJ,oCACE,CD+CA,yXAQE,sCCrDJ,wCAGA,oCACE", "sources": ["webpack:///./node_modules/normalize.css/normalize.css", "webpack:///./src/furo/assets/styles/base/_print.sass", "webpack:///./src/furo/assets/styles/base/_screen-readers.sass", "webpack:///./src/furo/assets/styles/base/_theme.sass", "webpack:///./src/furo/assets/styles/variables/_fonts.scss", "webpack:///./src/furo/assets/styles/variables/_spacing.scss", "webpack:///./src/furo/assets/styles/variables/_icons.scss", "webpack:///./src/furo/assets/styles/variables/_admonitions.scss", "webpack:///./src/furo/assets/styles/variables/_colors.scss", "webpack:///./src/furo/assets/styles/base/_typography.sass", "webpack:///./src/furo/assets/styles/_scaffold.sass", "webpack:///./src/furo/assets/styles/variables/_layout.scss", "webpack:///./src/furo/assets/styles/content/_admonitions.sass", "webpack:///./src/furo/assets/styles/content/_api.sass", "webpack:///./src/furo/assets/styles/content/_blocks.sass", "webpack:///./src/furo/assets/styles/content/_captions.sass", "webpack:///./src/furo/assets/styles/content/_code.sass", "webpack:///./src/furo/assets/styles/content/_footnotes.sass", "webpack:///./src/furo/assets/styles/content/_images.sass", "webpack:///./src/furo/assets/styles/content/_indexes.sass", "webpack:///./src/furo/assets/styles/content/_lists.sass", "webpack:///./src/furo/assets/styles/content/_math.sass", "webpack:///./src/furo/assets/styles/content/_misc.sass", "webpack:///./src/furo/assets/styles/content/_rubrics.sass", "webpack:///./src/furo/assets/styles/content/_sidebar.sass", "webpack:///./src/furo/assets/styles/content/_tables.sass", "webpack:///./src/furo/assets/styles/content/_target.sass", "webpack:///./src/furo/assets/styles/content/_gui-labels.sass", "webpack:///./src/furo/assets/styles/components/_footer.sass", "webpack:///./src/furo/assets/styles/components/_sidebar.sass", "webpack:///./src/furo/assets/styles/components/_table_of_contents.sass", "webpack:///./src/furo/assets/styles/_shame.sass"], "sourcesContent": ["/*! normalize.css v8.0.1 | MIT License | github.com/necolas/normalize.css */\n\n/* Document\n   ========================================================================== */\n\n/**\n * 1. Correct the line height in all browsers.\n * 2. Prevent adjustments of font size after orientation changes in iOS.\n */\n\nhtml {\n  line-height: 1.15; /* 1 */\n  -webkit-text-size-adjust: 100%; /* 2 */\n}\n\n/* Sections\n   ========================================================================== */\n\n/**\n * Remove the margin in all browsers.\n */\n\nbody {\n  margin: 0;\n}\n\n/**\n * Render the `main` element consistently in IE.\n */\n\nmain {\n  display: block;\n}\n\n/**\n * Correct the font size and margin on `h1` elements within `section` and\n * `article` contexts in Chrome, Firefox, and Safari.\n */\n\nh1 {\n  font-size: 2em;\n  margin: 0.67em 0;\n}\n\n/* Grouping content\n   ========================================================================== */\n\n/**\n * 1. Add the correct box sizing in Firefox.\n * 2. Show the overflow in Edge and IE.\n */\n\nhr {\n  box-sizing: content-box; /* 1 */\n  height: 0; /* 1 */\n  overflow: visible; /* 2 */\n}\n\n/**\n * 1. Correct the inheritance and scaling of font size in all browsers.\n * 2. Correct the odd `em` font sizing in all browsers.\n */\n\npre {\n  font-family: monospace, monospace; /* 1 */\n  font-size: 1em; /* 2 */\n}\n\n/* Text-level semantics\n   ========================================================================== */\n\n/**\n * Remove the gray background on active links in IE 10.\n */\n\na {\n  background-color: transparent;\n}\n\n/**\n * 1. Remove the bottom border in Chrome 57-\n * 2. Add the correct text decoration in Chrome, Edge, IE, Opera, and Safari.\n */\n\nabbr[title] {\n  border-bottom: none; /* 1 */\n  text-decoration: underline; /* 2 */\n  text-decoration: underline dotted; /* 2 */\n}\n\n/**\n * Add the correct font weight in Chrome, Edge, and Safari.\n */\n\nb,\nstrong {\n  font-weight: bolder;\n}\n\n/**\n * 1. Correct the inheritance and scaling of font size in all browsers.\n * 2. Correct the odd `em` font sizing in all browsers.\n */\n\ncode,\nkbd,\nsamp {\n  font-family: monospace, monospace; /* 1 */\n  font-size: 1em; /* 2 */\n}\n\n/**\n * Add the correct font size in all browsers.\n */\n\nsmall {\n  font-size: 80%;\n}\n\n/**\n * Prevent `sub` and `sup` elements from affecting the line height in\n * all browsers.\n */\n\nsub,\nsup {\n  font-size: 75%;\n  line-height: 0;\n  position: relative;\n  vertical-align: baseline;\n}\n\nsub {\n  bottom: -0.25em;\n}\n\nsup {\n  top: -0.5em;\n}\n\n/* Embedded content\n   ========================================================================== */\n\n/**\n * Remove the border on images inside links in IE 10.\n */\n\nimg {\n  border-style: none;\n}\n\n/* Forms\n   ========================================================================== */\n\n/**\n * 1. Change the font styles in all browsers.\n * 2. Remove the margin in Firefox and Safari.\n */\n\nbutton,\ninput,\noptgroup,\nselect,\ntextarea {\n  font-family: inherit; /* 1 */\n  font-size: 100%; /* 1 */\n  line-height: 1.15; /* 1 */\n  margin: 0; /* 2 */\n}\n\n/**\n * Show the overflow in IE.\n * 1. Show the overflow in Edge.\n */\n\nbutton,\ninput { /* 1 */\n  overflow: visible;\n}\n\n/**\n * Remove the inheritance of text transform in Edge, Firefox, and IE.\n * 1. Remove the inheritance of text transform in Firefox.\n */\n\nbutton,\nselect { /* 1 */\n  text-transform: none;\n}\n\n/**\n * Correct the inability to style clickable types in iOS and Safari.\n */\n\nbutton,\n[type=\"button\"],\n[type=\"reset\"],\n[type=\"submit\"] {\n  -webkit-appearance: button;\n}\n\n/**\n * Remove the inner border and padding in Firefox.\n */\n\nbutton::-moz-focus-inner,\n[type=\"button\"]::-moz-focus-inner,\n[type=\"reset\"]::-moz-focus-inner,\n[type=\"submit\"]::-moz-focus-inner {\n  border-style: none;\n  padding: 0;\n}\n\n/**\n * Restore the focus styles unset by the previous rule.\n */\n\nbutton:-moz-focusring,\n[type=\"button\"]:-moz-focusring,\n[type=\"reset\"]:-moz-focusring,\n[type=\"submit\"]:-moz-focusring {\n  outline: 1px dotted ButtonText;\n}\n\n/**\n * Correct the padding in Firefox.\n */\n\nfieldset {\n  padding: 0.35em 0.75em 0.625em;\n}\n\n/**\n * 1. Correct the text wrapping in Edge and IE.\n * 2. Correct the color inheritance from `fieldset` elements in IE.\n * 3. Remove the padding so developers are not caught out when they zero out\n *    `fieldset` elements in all browsers.\n */\n\nlegend {\n  box-sizing: border-box; /* 1 */\n  color: inherit; /* 2 */\n  display: table; /* 1 */\n  max-width: 100%; /* 1 */\n  padding: 0; /* 3 */\n  white-space: normal; /* 1 */\n}\n\n/**\n * Add the correct vertical alignment in Chrome, Firefox, and Opera.\n */\n\nprogress {\n  vertical-align: baseline;\n}\n\n/**\n * Remove the default vertical scrollbar in IE 10+.\n */\n\ntextarea {\n  overflow: auto;\n}\n\n/**\n * 1. Add the correct box sizing in IE 10.\n * 2. Remove the padding in IE 10.\n */\n\n[type=\"checkbox\"],\n[type=\"radio\"] {\n  box-sizing: border-box; /* 1 */\n  padding: 0; /* 2 */\n}\n\n/**\n * Correct the cursor style of increment and decrement buttons in Chrome.\n */\n\n[type=\"number\"]::-webkit-inner-spin-button,\n[type=\"number\"]::-webkit-outer-spin-button {\n  height: auto;\n}\n\n/**\n * 1. Correct the odd appearance in Chrome and Safari.\n * 2. Correct the outline style in Safari.\n */\n\n[type=\"search\"] {\n  -webkit-appearance: textfield; /* 1 */\n  outline-offset: -2px; /* 2 */\n}\n\n/**\n * Remove the inner padding in Chrome and Safari on macOS.\n */\n\n[type=\"search\"]::-webkit-search-decoration {\n  -webkit-appearance: none;\n}\n\n/**\n * 1. Correct the inability to style clickable types in iOS and Safari.\n * 2. Change font properties to `inherit` in Safari.\n */\n\n::-webkit-file-upload-button {\n  -webkit-appearance: button; /* 1 */\n  font: inherit; /* 2 */\n}\n\n/* Interactive\n   ========================================================================== */\n\n/*\n * Add the correct display in Edge, IE 10+, and Firefox.\n */\n\ndetails {\n  display: block;\n}\n\n/*\n * Add the correct display in all browsers.\n */\n\nsummary {\n  display: list-item;\n}\n\n/* Misc\n   ========================================================================== */\n\n/**\n * Add the correct display in IE 10+.\n */\n\ntemplate {\n  display: none;\n}\n\n/**\n * Add the correct display in IE 10.\n */\n\n[hidden] {\n  display: none;\n}\n", "// This file contains styles for managing print media.\n\n////////////////////////////////////////////////////////////////////////////////\n// Hide elements not relevant to print media.\n////////////////////////////////////////////////////////////////////////////////\n@media print\n  // Hide icon container.\n  .content-icon-container\n    display: none !important\n\n  // Hide showing header links if hovering over when printing.\n  .headerlink\n    display: none !important\n\n  // Hide mobile header.\n  .mobile-header\n    display: none !important\n\n  // Hide navigation links.\n  .related-pages\n    display: none !important\n\n////////////////////////////////////////////////////////////////////////////////\n// Tweaks related to decolorization.\n////////////////////////////////////////////////////////////////////////////////\n@media print\n  // Apply a border around code which no longer have a color background.\n  .highlight\n    border: 0.1pt solid var(--color-foreground-border)\n\n////////////////////////////////////////////////////////////////////////////////\n// Avoid page break in some relevant cases.\n////////////////////////////////////////////////////////////////////////////////\n@media print\n  ul, ol, dl, a, table, pre, blockquote, p\n    page-break-inside: avoid\n\n  h1, h2, h3, h4, h5, h6, img, figure, caption\n    page-break-inside: avoid\n    page-break-after: avoid\n\n  ul, ol, dl\n    page-break-before: avoid\n", ".visually-hidden\n  position: absolute !important\n  width: 1px !important\n  height: 1px !important\n  padding: 0 !important\n  margin: -1px !important\n  overflow: hidden !important\n  clip: rect(0,0,0,0) !important\n  white-space: nowrap !important\n  border: 0 !important\n  color: var(--color-foreground-primary)\n  background: var(--color-background-primary)\n\n:-moz-focusring\n  outline: auto\n", "// This file serves as the \"skeleton\" of the theming logic.\n//\n// This contains the bulk of the logic for handling dark mode, color scheme\n// toggling and the handling of color-scheme-specific hiding of elements.\n\nbody\n  @include fonts\n  @include spacing\n  @include icons\n  @include admonitions\n  @include default-admonition(#651fff, \"abstract\")\n  @include default-topic(#14B8A6, \"pencil\")\n\n  @include colors\n\n.only-light\n  display: block !important\nhtml body .only-dark\n  display: none !important\n\n// Ignore dark-mode hints if print media.\n@media not print\n  // Enable dark-mode, if requested.\n  body[data-theme=\"dark\"]\n    @include colors-dark\n\n    html & .only-light\n      display: none !important\n    .only-dark\n      display: block !important\n\n  // Enable dark mode, unless explicitly told to avoid.\n  @media (prefers-color-scheme: dark)\n    body:not([data-theme=\"light\"])\n      @include colors-dark\n\n      html & .only-light\n        display: none !important\n      .only-dark\n        display: block !important\n\n//\n// Theme toggle presentation\n//\nbody[data-theme=\"auto\"]\n  .theme-toggle svg.theme-icon-when-auto-light\n    display: block\n\n  @media (prefers-color-scheme: dark)\n    .theme-toggle svg.theme-icon-when-auto-dark\n      display: block\n    .theme-toggle svg.theme-icon-when-auto-light\n      display: none\n\nbody[data-theme=\"dark\"]\n  .theme-toggle svg.theme-icon-when-dark\n    display: block\n\nbody[data-theme=\"light\"]\n  .theme-toggle svg.theme-icon-when-light\n    display: block\n", "// Fonts used by this theme.\n//\n// There are basically two things here -- using the system font stack and\n// defining sizes for various elements in %ages. We could have also used `em`\n// but %age is easier to reason about for me.\n\n@mixin fonts {\n  // These are adapted from https://systemfontstack.com/\n  --font-stack: -apple-system, BlinkMacSystemFont, Segoe UI, Helvetica, Arial,\n    sans-serif, Apple Color Emoji, Segoe UI Emoji;\n  --font-stack--monospace: \"SFMono-Regular\", Menlo, Consolas, Monaco,\n    Liberation Mono, Lucida Console, monospace;\n  --font-stack--headings: var(--font-stack);\n\n  --font-size--normal: 100%;\n  --font-size--small: 87.5%;\n  --font-size--small--2: 81.25%;\n  --font-size--small--3: 75%;\n  --font-size--small--4: 62.5%;\n\n  // Sidebar\n  --sidebar-caption-font-size: var(--font-size--small--2);\n  --sidebar-item-font-size: var(--font-size--small);\n  --sidebar-search-input-font-size: var(--font-size--small);\n\n  // Table of Contents\n  --toc-font-size: var(--font-size--small--3);\n  --toc-font-size--mobile: var(--font-size--normal);\n  --toc-title-font-size: var(--font-size--small--4);\n\n  // Admonitions\n  //\n  // These aren't defined in terms of %ages, since nesting these is permitted.\n  --admonition-font-size: 0.8125rem;\n  --admonition-title-font-size: 0.8125rem;\n\n  // Code\n  --code-font-size: var(--font-size--small--2);\n\n  // API\n  --api-font-size: var(--font-size--small);\n}\n", "// Spacing for various elements on the page\n//\n// If the user wants to tweak things in a certain way, they are permitted to.\n// They also have to deal with the consequences though!\n\n@mixin spacing {\n  // Header!\n  --header-height: calc(\n    var(--sidebar-item-line-height) + 4 * #{var(--sidebar-item-spacing-vertical)}\n  );\n  --header-padding: 0.5rem;\n\n  // Sidebar\n  --sidebar-tree-space-above: 1.5rem;\n  --sidebar-caption-space-above: 1rem;\n\n  --sidebar-item-line-height: 1rem;\n  --sidebar-item-spacing-vertical: 0.5rem;\n  --sidebar-item-spacing-horizontal: 1rem;\n  --sidebar-item-height: calc(\n    var(--sidebar-item-line-height) + 2 *#{var(--sidebar-item-spacing-vertical)}\n  );\n\n  --sidebar-expander-width: var(--sidebar-item-height); // be square\n\n  --sidebar-search-space-above: 0.5rem;\n  --sidebar-search-input-spacing-vertical: 0.5rem;\n  --sidebar-search-input-spacing-horizontal: 0.5rem;\n  --sidebar-search-input-height: 1rem;\n  --sidebar-search-icon-size: var(--sidebar-search-input-height);\n\n  // Table of Contents\n  --toc-title-padding: 0.25rem 0;\n  --toc-spacing-vertical: 1.5rem;\n  --toc-spacing-horizontal: 1.5rem;\n  --toc-item-spacing-vertical: 0.4rem;\n  --toc-item-spacing-horizontal: 1rem;\n}\n", "// Expose theme icons as CSS variables.\n\n$icons: (\n  // Adapted from tabler-icons\n  //    url: https://tablericons.com/\n  \"search\":\n    url('data:image/svg+xml;charset=utf-8,<svg xmlns=\"http://www.w3.org/2000/svg\" viewBox=\"0 0 24 24\" stroke-width=\"1.5\" stroke=\"currentColor\" fill=\"none\" stroke-linecap=\"round\" stroke-linejoin=\"round\"><path stroke=\"none\" d=\"M0 0h24v24H0z\"/><circle cx=\"10\" cy=\"10\" r=\"7\" /><line x1=\"21\" y1=\"21\" x2=\"15\" y2=\"15\" /></svg>'),\n  // Factored out from mkdocs-material on 24-Aug-2020.\n  //    url: https://squidfunk.github.io/mkdocs-material/reference/admonitions/\n  \"pencil\":\n    url('data:image/svg+xml;charset=utf-8,<svg xmlns=\"http://www.w3.org/2000/svg\" viewBox=\"0 0 24 24\"><path d=\"M20.71 7.04c.39-.39.39-1.04 0-1.41l-2.34-2.34c-.37-.39-1.02-.39-1.41 0l-1.84 1.83 3.75 3.75M3 17.25V21h3.75L17.81 9.93l-3.75-3.75L3 17.25z\"/></svg>'),\n  \"abstract\":\n    url('data:image/svg+xml;charset=utf-8,<svg xmlns=\"http://www.w3.org/2000/svg\" viewBox=\"0 0 24 24\"><path d=\"M4 5h16v2H4V5m0 4h16v2H4V9m0 4h16v2H4v-2m0 4h10v2H4v-2z\"/></svg>'),\n  \"info\":\n    url('data:image/svg+xml;charset=utf-8,<svg xmlns=\"http://www.w3.org/2000/svg\" viewBox=\"0 0 24 24\"><path d=\"M13 9h-2V7h2m0 10h-2v-6h2m-1-9A10 10 0 002 12a10 10 0 0010 10 10 10 0 0010-10A10 10 0 0012 2z\"/></svg>'),\n  \"flame\":\n    url('data:image/svg+xml;charset=utf-8,<svg xmlns=\"http://www.w3.org/2000/svg\" viewBox=\"0 0 24 24\"><path d=\"M17.55 11.2c-.23-.3-.5-.56-.76-.82-.65-.6-1.4-1.03-2.03-1.66C13.3 7.26 13 4.85 13.91 3c-.91.23-1.75.75-2.45 1.32-2.54 2.08-3.54 5.75-2.34 8.9.04.1.08.2.08.33 0 .22-.15.42-.35.5-.22.1-.46.04-.64-.12a.83.83 0 01-.15-.17c-1.1-1.43-1.28-3.48-.53-5.12C5.89 10 5 12.3 5.14 14.47c.04.5.1 1 .27 1.5.14.6.4 1.2.72 1.73 1.04 1.73 2.87 2.97 4.84 3.22 2.1.27 4.35-.12 5.96-1.6 1.8-1.66 2.45-4.32 1.5-6.6l-.13-.26c-.2-.46-.47-.87-.8-1.25l.05-.01m-3.1 6.3c-.28.24-.73.5-1.08.6-1.1.4-2.2-.16-2.87-.82 1.19-.28 1.89-1.16 2.09-2.05.17-.8-.14-1.46-.27-2.23-.12-.74-.1-1.37.18-2.06.17.38.37.76.6 1.06.76 1 1.95 1.44 2.2 2.8.04.14.06.28.06.43.03.82-.32 1.72-.92 2.27h.01z\"/></svg>'),\n  \"question\":\n    url('data:image/svg+xml;charset=utf-8,<svg xmlns=\"http://www.w3.org/2000/svg\" viewBox=\"0 0 24 24\"><path d=\"M15.07 11.25l-.9.92C13.45 12.89 13 13.5 13 15h-2v-.5c0-1.11.45-2.11 1.17-2.83l1.24-1.26c.37-.36.59-.86.59-1.41a2 2 0 00-2-2 2 2 0 00-2 2H8a4 4 0 014-4 4 4 0 014 4 3.2 3.2 0 01-.93 2.25M13 19h-2v-2h2M12 2A10 10 0 002 12a10 10 0 0010 10 10 10 0 0010-10c0-5.53-4.5-10-10-10z\"/></svg>'),\n  \"warning\":\n    url('data:image/svg+xml;charset=utf-8,<svg xmlns=\"http://www.w3.org/2000/svg\" viewBox=\"0 0 24 24\"><path d=\"M13 14h-2v-4h2m0 8h-2v-2h2M1 21h22L12 2 1 21z\"/></svg>'),\n  \"failure\":\n    url('data:image/svg+xml;charset=utf-8,<svg xmlns=\"http://www.w3.org/2000/svg\" viewBox=\"0 0 24 24\"><path d=\"M12 2c5.53 0 10 4.47 10 10s-4.47 10-10 10S2 17.53 2 12 6.47 2 12 2m3.59 5L12 10.59 8.41 7 7 8.41 10.59 12 7 15.59 8.41 17 12 13.41 15.59 17 17 15.59 13.41 12 17 8.41 15.59 7z\"/></svg>'),\n  \"spark\":\n    url('data:image/svg+xml;charset=utf-8,<svg xmlns=\"http://www.w3.org/2000/svg\" viewBox=\"0 0 24 24\"><path d=\"M11.5 20l4.86-9.73H13V4l-5 9.73h3.5V20M12 2c2.75 0 5.1 1 7.05 2.95C21 6.9 22 9.25 22 12s-1 5.1-2.95 7.05C17.1 21 14.75 22 12 22s-5.1-1-7.05-2.95C3 17.1 2 14.75 2 12s1-5.1 2.95-7.05C6.9 3 9.25 2 12 2z\"/></svg>')\n);\n\n@mixin icons {\n  @each $name, $glyph in $icons {\n    --icon-#{$name}: #{$glyph};\n  }\n}\n", "// Admonitions\n\n// Structure of these is:\n//    admonition-class: color \"icon-name\";\n//\n// The colors are translated into CSS variables below. The icons are\n// used directly in the main declarations to set the `mask-image` in\n// the title.\n\n// prettier-ignore\n$admonitions: (\n  // Each of these has an reST directives for it.\n  \"caution\":         #ff9100 \"spark\",\n  \"warning\":         #ff9100 \"warning\",\n  \"danger\":          #ff5252 \"spark\",\n  \"attention\":       #ff5252 \"warning\",\n  \"error\":           #ff5252 \"failure\",\n  \"hint\":            #00c852 \"question\",\n  \"tip\":             #00c852 \"info\",\n  \"important\":       #00bfa5 \"flame\",\n  \"note\":            #00b0ff \"pencil\",\n  \"seealso\":         #448aff \"info\",\n  \"admonition-todo\": #808080 \"pencil\"\n);\n\n@mixin default-admonition($color, $icon-name) {\n  --color-admonition-title: #{$color};\n  --color-admonition-title-background: #{rgba($color, 0.2)};\n\n  --icon-admonition-default: var(--icon-#{$icon-name});\n}\n\n@mixin default-topic($color, $icon-name) {\n  --color-topic-title: #{$color};\n  --color-topic-title-background: #{rgba($color, 0.2)};\n\n  --icon-topic-default: var(--icon-#{$icon-name});\n}\n\n@mixin admonitions {\n  @each $name, $values in $admonitions {\n    --color-admonition-title--#{$name}: #{nth($values, 1)};\n    --color-admonition-title-background--#{$name}: #{rgba(\n        nth($values, 1),\n        0.2\n      )};\n  }\n}\n", "// Colors used throughout this theme.\n//\n// The aim is to give the user more control. Thus, instead of hard-coding colors\n// in various parts of the stylesheet, the approach taken is to define all\n// colors as CSS variables and reusing them in all the places.\n//\n// `colors-dark` depends on `colors` being included at a lower specificity.\n\n@mixin colors {\n  --color-problematic: #b30000;\n\n  // Base Colors\n  --color-foreground-primary: black; // for main text and headings\n  --color-foreground-secondary: #5a5c63; // for secondary text\n  --color-foreground-muted: #6b6f76; // for muted text\n  --color-foreground-border: #878787; // for content borders\n\n  --color-background-primary: white; // for content\n  --color-background-secondary: #f8f9fb; // for navigation + ToC\n  --color-background-hover: #efeff4ff; // for navigation-item hover\n  --color-background-hover--transparent: #efeff400;\n  --color-background-border: #eeebee; // for UI borders\n  --color-background-item: #ccc; // for \"background\" items (eg: copybutton)\n\n  // Announcements\n  --color-announcement-background: #000000dd;\n  --color-announcement-text: #eeebee;\n\n  // Brand colors\n  --color-brand-primary: #0a4bff;\n  --color-brand-content: #2757dd;\n  --color-brand-visited: #872ee0;\n\n  // API documentation\n  --color-api-background: var(--color-background-hover--transparent);\n  --color-api-background-hover: var(--color-background-hover);\n  --color-api-overall: var(--color-foreground-secondary);\n  --color-api-name: var(--color-problematic);\n  --color-api-pre-name: var(--color-problematic);\n  --color-api-paren: var(--color-foreground-secondary);\n  --color-api-keyword: var(--color-foreground-primary);\n\n  --color-api-added: #21632c;\n  --color-api-added-border: #38a84d;\n  --color-api-changed: #046172;\n  --color-api-changed-border: #06a1bc;\n  --color-api-deprecated: #605706;\n  --color-api-deprecated-border: #f0d90f;\n  --color-api-removed: #b30000;\n  --color-api-removed-border: #ff5c5c;\n\n  --color-highlight-on-target: #ffffcc;\n\n  // Inline code background\n  --color-inline-code-background: var(--color-background-secondary);\n\n  // Highlighted text (search)\n  --color-highlighted-background: #ddeeff;\n  --color-highlighted-text: var(--color-foreground-primary);\n\n  // GUI Labels\n  --color-guilabel-background: #ddeeff80;\n  --color-guilabel-border: #bedaf580;\n  --color-guilabel-text: var(--color-foreground-primary);\n\n  // Admonitions!\n  --color-admonition-background: transparent;\n\n  //////////////////////////////////////////////////////////////////////////////\n  // Everything below this should be one of:\n  // - var(...)\n  // - *-gradient(...)\n  // - special literal values (eg: transparent, none)\n  //////////////////////////////////////////////////////////////////////////////\n\n  // Tables\n  --color-table-header-background: var(--color-background-secondary);\n  --color-table-border: var(--color-background-border);\n\n  // Cards\n  --color-card-border: var(--color-background-secondary);\n  --color-card-background: transparent;\n  --color-card-marginals-background: var(--color-background-secondary);\n\n  // Header\n  --color-header-background: var(--color-background-primary);\n  --color-header-border: var(--color-background-border);\n  --color-header-text: var(--color-foreground-primary);\n\n  // Sidebar (left)\n  --color-sidebar-background: var(--color-background-secondary);\n  --color-sidebar-background-border: var(--color-background-border);\n\n  --color-sidebar-brand-text: var(--color-foreground-primary);\n  --color-sidebar-caption-text: var(--color-foreground-muted);\n  --color-sidebar-link-text: var(--color-foreground-secondary);\n  --color-sidebar-link-text--top-level: var(--color-brand-primary);\n\n  --color-sidebar-item-background: var(--color-sidebar-background);\n  --color-sidebar-item-background--current: var(\n    --color-sidebar-item-background\n  );\n  --color-sidebar-item-background--hover: linear-gradient(\n    90deg,\n    var(--color-background-hover--transparent) 0%,\n    var(--color-background-hover) var(--sidebar-item-spacing-horizontal),\n    var(--color-background-hover) 100%\n  );\n\n  --color-sidebar-item-expander-background: transparent;\n  --color-sidebar-item-expander-background--hover: var(\n    --color-background-hover\n  );\n\n  --color-sidebar-search-text: var(--color-foreground-primary);\n  --color-sidebar-search-background: var(--color-background-secondary);\n  --color-sidebar-search-background--focus: var(--color-background-primary);\n  --color-sidebar-search-border: var(--color-background-border);\n  --color-sidebar-search-icon: var(--color-foreground-muted);\n\n  // Table of Contents (right)\n  --color-toc-background: var(--color-background-primary);\n  --color-toc-title-text: var(--color-foreground-muted);\n  --color-toc-item-text: var(--color-foreground-secondary);\n  --color-toc-item-text--hover: var(--color-foreground-primary);\n  --color-toc-item-text--active: var(--color-brand-primary);\n\n  // Actual page contents\n  --color-content-foreground: var(--color-foreground-primary);\n  --color-content-background: transparent;\n\n  // Links\n  --color-link: var(--color-brand-content);\n  --color-link-underline: var(--color-background-border);\n  --color-link--hover: var(--color-brand-content);\n  --color-link-underline--hover: var(--color-foreground-border);\n\n  --color-link--visited: var(--color-brand-visited);\n  --color-link-underline--visited: var(--color-background-border);\n  --color-link--visited--hover: var(--color-brand-visited);\n  --color-link-underline--visited--hover: var(--color-foreground-border);\n}\n\n@mixin colors-dark {\n  --color-problematic: #ee5151;\n\n  // Base Colors\n  --color-foreground-primary: #cfd0d0; // for main text and headings\n  --color-foreground-secondary: #9ca0a5; // for secondary text\n  --color-foreground-muted: #81868d; // for muted text\n  --color-foreground-border: #666666; // for content borders\n\n  --color-background-primary: #131416; // for content\n  --color-background-secondary: #1a1c1e; // for navigation + ToC\n  --color-background-hover: #1e2124ff; // for navigation-item hover\n  --color-background-hover--transparent: #1e212400;\n  --color-background-border: #303335; // for UI borders\n  --color-background-item: #444; // for \"background\" items (eg: copybutton)\n\n  // Announcements\n  --color-announcement-background: #000000dd;\n  --color-announcement-text: #eeebee;\n\n  // Brand colors\n  --color-brand-primary: #3d94ff;\n  --color-brand-content: #5ca5ff;\n  --color-brand-visited: #b27aeb;\n\n  // Highlighted text (search)\n  --color-highlighted-background: #083563;\n\n  // GUI Labels\n  --color-guilabel-background: #08356380;\n  --color-guilabel-border: #13395f80;\n\n  // API documentation\n  --color-api-keyword: var(--color-foreground-secondary);\n  --color-highlight-on-target: #333300;\n\n  --color-api-added: #3db854;\n  --color-api-added-border: #267334;\n  --color-api-changed: #09b0ce;\n  --color-api-changed-border: #056d80;\n  --color-api-deprecated: #b1a10b;\n  --color-api-deprecated-border: #6e6407;\n  --color-api-removed: #ff7575;\n  --color-api-removed-border: #b03b3b;\n\n  // Admonitions\n  --color-admonition-background: #18181a;\n\n  // Cards\n  --color-card-border: var(--color-background-secondary);\n  --color-card-background: #18181a;\n  --color-card-marginals-background: var(--color-background-hover);\n}\n", "// This file contains the styling for making the content throughout the page,\n// including fonts, paragraphs, headings and spacing among these elements.\n\nbody\n  font-family: var(--font-stack)\npre,\ncode,\nkbd,\nsamp\n  font-family: var(--font-stack--monospace)\n\n// Make fonts look slightly nicer.\nbody\n  -webkit-font-smoothing: antialiased\n  -moz-osx-font-smoothing: grayscale\n\n// Line height from Bootstrap 4.1\narticle\n  line-height: 1.5\n\n//\n// Headings\n//\nh1,\nh2,\nh3,\nh4,\nh5,\nh6\n  line-height: 1.25\n  font-family: var(--font-stack--headings)\n  font-weight: bold\n\n  border-radius: 0.5rem\n  margin-top: 0.5rem\n  margin-bottom: 0.5rem\n  margin-left: -0.5rem\n  margin-right: -0.5rem\n  padding-left: 0.5rem\n  padding-right: 0.5rem\n\n  + p\n    margin-top: 0\n\nh1\n  font-size: 2.5em\n  margin-top: 1.75rem\n  margin-bottom: 1rem\nh2\n  font-size: 2em\n  margin-top: 1.75rem\nh3\n  font-size: 1.5em\nh4\n  font-size: 1.25em\nh5\n  font-size: 1.125em\nh6\n  font-size: 1em\n\nsmall\n  opacity: 75%\n  font-size: 80%\n\n// Paragraph\np\n  margin-top: 0.5rem\n  margin-bottom: 0.75rem\n\n// Horizontal rules\nhr.docutils\n  height: 1px\n  padding: 0\n  margin: 2rem 0\n  background-color: var(--color-background-border)\n  border: 0\n\n.centered\n  text-align: center\n\n// Links\na\n  text-decoration: underline\n\n  color: var(--color-link)\n  text-decoration-color: var(--color-link-underline)\n\n  &:visited\n    color: var(--color-link--visited)\n    text-decoration-color: var(--color-link-underline--visited)\n    &:hover\n      color: var(--color-link--visited--hover)\n      text-decoration-color: var(--color-link-underline--visited--hover)\n\n  &:hover\n    color: var(--color-link--hover)\n    text-decoration-color: var(--color-link-underline--hover)\n  &.muted-link\n    color: inherit\n    &:hover\n      color: var(--color-link--hover)\n      text-decoration-color: var(--color-link-underline--hover)\n      &:visited\n        color: var(--color-link--visited--hover)\n        text-decoration-color: var(--color-link-underline--visited--hover)\n", "// This file contains the styles for the overall layouting of the documentation\n// skeleton, including the responsive changes as well as sidebar toggles.\n//\n// This is implemented as a mobile-last design, which isn't ideal, but it is\n// reasonably good-enough and I got pretty tired by the time I'd finished this\n// to move the rules around to fix this. Shouldn't take more than 3-4 hours,\n// if you know what you're doing tho.\n\n// HACK: Not all browsers account for the scrollbar width in media queries.\n// This results in horizontal scrollbars in the breakpoint where we go\n// from displaying everything to hiding the ToC. We accomodate for this by\n// adding a bit of padding to the TOC drawer, disabling the horizontal\n// scrollbar and allowing the scrollbars to cover the padding.\n// https://www.456bereastreet.com/archive/201301/media_query_width_and_vertical_scrollbars/\n\n// HACK: Always having the scrollbar visible, prevents certain browsers from\n// causing the content to stutter horizontally between taller-than-viewport and\n// not-taller-than-viewport pages.\n\nhtml\n  overflow-x: hidden\n  overflow-y: scroll\n  scroll-behavior: smooth\n\n.sidebar-scroll, .toc-scroll, article[role=main] *\n  // Override Firefox scrollbar style\n  scrollbar-width: thin\n  scrollbar-color: var(--color-foreground-border) transparent\n\n  // Override Chrome scrollbar styles\n  &::-webkit-scrollbar\n    width: 0.25rem\n    height: 0.25rem\n  &::-webkit-scrollbar-thumb\n    background-color: var(--color-foreground-border)\n    border-radius: 0.125rem\n\n//\n// Overalls\n//\nhtml,\nbody\n  height: 100%\n  color: var(--color-foreground-primary)\n  background: var(--color-background-primary)\n\n.skip-to-content\n  position: fixed\n  padding: 1rem\n  border-radius: 1rem\n  left: 0.25rem\n  top: 0.25rem\n  z-index: 40\n  background: var(--color-background-primary)\n  color: var(--color-foreground-primary)\n\n  transform: translateY(-200%)\n  transition: transform 300ms ease-in-out\n\n  &:focus-within\n    transform: translateY(0%)\n\narticle\n  color: var(--color-content-foreground)\n  background: var(--color-content-background)\n  overflow-wrap: break-word\n\n.page\n  display: flex\n  // fill the viewport for pages with little content.\n  min-height: 100%\n\n.mobile-header\n  width: 100%\n  height: var(--header-height)\n  background-color: var(--color-header-background)\n  color: var(--color-header-text)\n  border-bottom: 1px solid var(--color-header-border)\n\n  // Looks like sub-script/super-script have this, and we need this to\n  // be \"on top\" of those.\n  z-index: 10\n\n  // We don't show the header on large screens.\n  display: none\n\n  // Add shadow when scrolled\n  &.scrolled\n    border-bottom: none\n    box-shadow: 0 0 0.2rem rgba(0, 0, 0, 0.1), 0 0.2rem 0.4rem rgba(0, 0, 0, 0.2)\n\n  .header-center\n    a\n      color: var(--color-header-text)\n      text-decoration: none\n\n.main\n  display: flex\n  flex: 1\n\n// Sidebar (left) also covers the entire left portion of screen.\n.sidebar-drawer\n  box-sizing: border-box\n\n  border-right: 1px solid var(--color-sidebar-background-border)\n  background: var(--color-sidebar-background)\n\n  display: flex\n  justify-content: flex-end\n  // These next two lines took me two days to figure out.\n  width: calc((100% - #{$full-width}) / 2 + #{$sidebar-width})\n  min-width: $sidebar-width\n\n// Scroll-along sidebars\n.sidebar-container,\n.toc-drawer\n  box-sizing: border-box\n  width: $sidebar-width\n\n.toc-drawer\n  background: var(--color-toc-background)\n  // See HACK described on top of this document\n  padding-right: 1rem\n\n.sidebar-sticky,\n.toc-sticky\n  position: sticky\n  top: 0\n  height: min(100%, 100vh)\n  height: 100vh\n\n  display: flex\n  flex-direction: column\n\n.sidebar-scroll,\n.toc-scroll\n  flex-grow: 1\n  flex-shrink: 1\n\n  overflow: auto\n  scroll-behavior: smooth\n\n// Central items.\n.content\n  padding: 0 $content-padding\n  width: $content-width\n\n  display: flex\n  flex-direction: column\n  justify-content: space-between\n\n.icon\n  display: inline-block\n  height: 1rem\n  width: 1rem\n  svg\n    width: 100%\n    height: 100%\n\n//\n// Accommodate announcement banner\n//\n.announcement\n  background-color: var(--color-announcement-background)\n  color: var(--color-announcement-text)\n\n  height: var(--header-height)\n  display: flex\n  align-items: center\n  overflow-x: auto\n  & + .page\n    min-height: calc(100% - var(--header-height))\n\n.announcement-content\n  box-sizing: border-box\n  padding: 0.5rem\n  min-width: 100%\n  white-space: nowrap\n  text-align: center\n\n  a\n    color: var(--color-announcement-text)\n    text-decoration-color: var(--color-announcement-text)\n\n    &:hover\n      color: var(--color-announcement-text)\n      text-decoration-color: var(--color-link--hover)\n\n////////////////////////////////////////////////////////////////////////////////\n// Toggles for theme\n////////////////////////////////////////////////////////////////////////////////\n.no-js .theme-toggle-container  // don't show theme toggle if there's no JS\n  display: none\n\n.theme-toggle-container\n  display: flex\n\n.theme-toggle\n  display: flex\n  cursor: pointer\n  border: none\n  padding: 0\n  background: transparent\n\n.theme-toggle svg\n  height: 1.25rem\n  width: 1.25rem\n  color: var(--color-foreground-primary)\n  display: none\n\n.theme-toggle-header\n  display: flex\n  align-items: center\n  justify-content: center\n\n////////////////////////////////////////////////////////////////////////////////\n// Toggles for elements\n////////////////////////////////////////////////////////////////////////////////\n.toc-overlay-icon, .nav-overlay-icon\n  display: none\n  cursor: pointer\n\n  .icon\n    color: var(--color-foreground-secondary)\n    height: 1.5rem\n    width: 1.5rem\n\n.toc-header-icon, .nav-overlay-icon\n  // for when we set display: flex\n  justify-content: center\n  align-items: center\n\n.toc-content-icon\n  height: 1.5rem\n  width: 1.5rem\n\n.content-icon-container\n  float: right\n  display: flex\n  margin-top: 1.5rem\n  margin-left: 1rem\n  margin-bottom: 1rem\n  gap: 0.5rem\n\n  .edit-this-page, .view-this-page\n    svg\n      color: inherit\n      height: 1.25rem\n      width: 1.25rem\n\n.sidebar-toggle\n  position: absolute\n  display: none\n// <debugging things>\n.sidebar-toggle[name=\"__toc\"]\n  left: 20px\n.sidebar-toggle:checked\n  left: 40px\n// </debugging things>\n\n.overlay\n  position: fixed\n  top: 0\n  width: 0\n  height: 0\n\n  transition: width 0ms, height 0ms, opacity 250ms ease-out\n\n  opacity: 0\n  background-color: rgba(0, 0, 0, 0.54)\n.sidebar-overlay\n  z-index: 20\n.toc-overlay\n  z-index: 40\n\n// Keep things on top and smooth.\n.sidebar-drawer\n  z-index: 30\n  transition: left 250ms ease-in-out\n.toc-drawer\n  z-index: 50\n  transition: right 250ms ease-in-out\n\n// Show the Sidebar\n#__navigation:checked\n  & ~ .sidebar-overlay\n    width: 100%\n    height: 100%\n    opacity: 1\n  & ~ .page\n    .sidebar-drawer\n      top: 0\n      left: 0\n      // Show the toc sidebar\n#__toc:checked\n  & ~ .toc-overlay\n    width: 100%\n    height: 100%\n    opacity: 1\n  & ~ .page\n    .toc-drawer\n      top: 0\n      right: 0\n\n////////////////////////////////////////////////////////////////////////////////\n// Back to top\n////////////////////////////////////////////////////////////////////////////////\n.back-to-top\n  text-decoration: none\n\n  display: none\n  position: fixed\n  left: 0\n  top: 1rem\n  padding: 0.5rem\n  padding-right: 0.75rem\n  border-radius: 1rem\n  font-size: 0.8125rem\n\n  background: var(--color-background-primary)\n  box-shadow: 0 0.2rem 0.5rem rgba(0, 0, 0, 0.05), #6b728080 0px 0px 1px 0px\n\n  z-index: 10\n\n  margin-left: 50%\n  transform: translateX(-50%)\n  svg\n    height: 1rem\n    width: 1rem\n    fill: currentColor\n    display: inline-block\n\n  span\n    margin-left: 0.25rem\n\n  .show-back-to-top &\n    display: flex\n    align-items: center\n\n////////////////////////////////////////////////////////////////////////////////\n// Responsive layouting\n////////////////////////////////////////////////////////////////////////////////\n// Make things a bit bigger on bigger screens.\n@media (min-width: $full-width + $sidebar-width)\n  html\n    font-size: 110%\n\n@media (max-width: $full-width)\n  // Collapse \"toc\" into the icon.\n  .toc-content-icon\n    display: flex\n  .toc-drawer\n    position: fixed\n    height: 100vh\n    top: 0\n    right: -$sidebar-width\n    border-left: 1px solid var(--color-background-muted)\n  .toc-tree\n    border-left: none\n    font-size: var(--toc-font-size--mobile)\n\n  // Accomodate for a changed content width.\n  .sidebar-drawer\n    width: calc((100% - #{$full-width - $sidebar-width}) / 2 + #{$sidebar-width})\n\n@media (max-width: $content-padded-width + $sidebar-width)\n  // Center the page\n  .content\n    margin-left: auto\n    margin-right: auto\n    padding: 0 $content-padding--small\n\n@media (max-width: $content-padded-width--small + $sidebar-width)\n  // Collapse \"navigation\".\n  .nav-overlay-icon\n    display: flex\n  .sidebar-drawer\n    position: fixed\n    height: 100vh\n    width: $sidebar-width\n\n    top: 0\n    left: -$sidebar-width\n\n  // Swap which icon is visible.\n  .toc-header-icon, .theme-toggle-header\n    display: flex\n  .toc-content-icon, .theme-toggle-content\n    display: none\n\n  // Show the header.\n  .mobile-header\n    position: sticky\n    top: 0\n    display: flex\n    justify-content: space-between\n    align-items: center\n\n    .header-left,\n    .header-right\n      display: flex\n      height: var(--header-height)\n      padding: 0 var(--header-padding)\n      label\n        height: 100%\n        width: 100%\n        user-select: none\n\n  .nav-overlay-icon .icon,\n  .theme-toggle svg\n    height: 1.5rem\n    width: 1.5rem\n\n  // Add a scroll margin for the content\n  :target\n    scroll-margin-top: calc(var(--header-height) + 2.5rem)\n\n  // Show back-to-top below the header\n  .back-to-top\n    top: calc(var(--header-height) + 0.5rem)\n\n  // Accommodate for the header.\n  .page\n    flex-direction: column\n    justify-content: center\n\n@media (max-width: $content-width + 2* $content-padding--small)\n  // Content should respect window limits.\n  .content\n    width: 100%\n    overflow-x: auto\n\n@media (max-width: $content-width)\n  article[role=main] aside.sidebar\n    float: none\n    width: 100%\n    margin: 1rem 0\n", "// Overall Layout Variables\n//\n// Because CSS variables can't be used in media queries. The fact that this\n// makes the layout non-user-configurable is a good thing.\n$content-padding: 3em;\n$content-padding--small: 1em;\n$content-width: 46em;\n$sidebar-width: 15em;\n$content-padded-width: $content-width + 2 * $content-padding;\n$content-padded-width--small: $content-width + 2 * $content-padding--small;\n$full-width: $content-padded-width + 2 * $sidebar-width;\n", "//\n// The design here is strongly inspired by mkdocs-material.\n.admonition, .topic\n  margin: 1rem auto\n  padding: 0 0.5rem 0.5rem 0.5rem\n\n  background: var(--color-admonition-background)\n\n  border-radius: 0.2rem\n  box-shadow: 0 0.2rem 0.5rem rgba(0, 0, 0, 0.05), 0 0 0.0625rem rgba(0, 0, 0, 0.1)\n\n  font-size: var(--admonition-font-size)\n\n  overflow: hidden\n  page-break-inside: avoid\n\n  // First element should have no margin, since the title has it.\n  > :nth-child(2)\n    margin-top: 0\n\n  // Last item should have no margin, since we'll control that w/ padding\n  > :last-child\n    margin-bottom: 0\n\n.admonition p.admonition-title,\np.topic-title\n  position: relative\n  margin: 0 -0.5rem 0.5rem\n  padding-left: 2rem\n  padding-right: .5rem\n  padding-top: .4rem\n  padding-bottom: .4rem\n\n  font-weight: 500\n  font-size: var(--admonition-title-font-size)\n  line-height: 1.3\n\n    // Our fancy icon\n  &::before\n    content: \"\"\n    position: absolute\n    left: 0.5rem\n    width: 1rem\n    height: 1rem\n\n// Default styles\np.admonition-title\n  background-color: var(--color-admonition-title-background)\n  &::before\n    background-color: var(--color-admonition-title)\n    mask-image: var(--icon-admonition-default)\n    mask-repeat: no-repeat\n\np.topic-title\n  background-color: var(--color-topic-title-background)\n  &::before\n    background-color: var(--color-topic-title)\n    mask-image: var(--icon-topic-default)\n    mask-repeat: no-repeat\n\n//\n// Variants\n//\n.admonition\n  border-left: 0.2rem solid var(--color-admonition-title)\n\n  @each $type, $value in $admonitions\n    &.#{$type}\n      border-left-color: var(--color-admonition-title--#{$type})\n      > .admonition-title\n        background-color: var(--color-admonition-title-background--#{$type})\n        &::before\n          background-color: var(--color-admonition-title--#{$type})\n          mask-image: var(--icon-#{nth($value, 2)})\n\n.admonition-todo > .admonition-title\n  text-transform: uppercase\n", "// This file stylizes the API documentation (stuff generated by autodoc). It's\n// deeply nested due to how autodoc structures the HTML without enough classes\n// to select the relevant items.\n\n// API docs!\ndl[class]:not(.option-list):not(.field-list):not(.footnote):not(.glossary):not(.simple)\n  // Tweak the spacing of all the things!\n  dd\n    margin-left: 2rem\n    > :first-child\n      margin-top: 0.125rem\n    > :last-child\n      margin-bottom: 0.75rem\n\n  // This is used for the arguments\n  .field-list\n    margin-bottom: 0.75rem\n\n    // \"Headings\" (like \"Parameters\" and \"Return\")\n    > dt\n      text-transform: uppercase\n      font-size: var(--font-size--small)\n\n    dd:empty\n      margin-bottom: 0.5rem\n    dd > ul\n      margin-left: -1.2rem\n      > li\n        > p:nth-child(2)\n          margin-top: 0\n        // When the last-empty-paragraph follows a paragraph, it doesn't need\n        // to augument the existing spacing.\n        > p + p:last-child:empty\n          margin-top: 0\n          margin-bottom: 0\n\n  // Colorize the elements\n  > dt\n    color: var(--color-api-overall)\n\n.sig:not(.sig-inline)\n  font-weight: bold\n\n  font-size: var(--api-font-size)\n  font-family: var(--font-stack--monospace)\n\n  margin-left: -0.25rem\n  margin-right: -0.25rem\n  padding-top: 0.25rem\n  padding-bottom: 0.25rem\n  padding-right: 0.5rem\n\n  // These are intentionally em, to properly match the font size.\n  padding-left: 3em\n  text-indent: -2.5em\n\n  border-radius: 0.25rem\n\n  background: var(--color-api-background)\n  transition: background 100ms ease-out\n\n  &:hover\n    background: var(--color-api-background-hover)\n\n  // adjust the size of the [source] link on the right.\n  a.reference\n    .viewcode-link\n      font-weight: normal\n      width: 4.25rem\n\nem.property\n  font-style: normal\n  &:first-child\n    color: var(--color-api-keyword)\n.sig-name\n  color: var(--color-api-name)\n.sig-prename\n  font-weight: normal\n  color: var(--color-api-pre-name)\n.sig-paren\n  color: var(--color-api-paren)\n.sig-param\n  font-style: normal\n\ndiv.versionadded,\ndiv.versionchanged,\ndiv.deprecated,\ndiv.versionremoved\n  border-left: 0.1875rem solid\n  border-radius: 0.125rem\n\n  padding-left: 0.75rem\n\n  p\n    margin-top: 0.125rem\n    margin-bottom: 0.125rem\n\ndiv.versionadded\n  border-color: var(--color-api-added-border)\n  .versionmodified\n    color: var(--color-api-added)\n\ndiv.versionchanged\n  border-color: var(--color-api-changed-border)\n  .versionmodified\n    color: var(--color-api-changed)\n\ndiv.deprecated\n  border-color: var(--color-api-deprecated-border)\n  .versionmodified\n    color: var(--color-api-deprecated)\n\ndiv.versionremoved\n  border-color: var(--color-api-removed-border)\n  .versionmodified\n    color: var(--color-api-removed)\n\n// Align the [docs] and [source] to the right.\n.viewcode-link, .viewcode-back\n  float: right\n  text-align: right\n", ".line-block\n  margin-top: 0.5rem\n  margin-bottom: 0.75rem\n  .line-block\n    margin-top: 0rem\n    margin-bottom: 0rem\n    padding-left: 1rem\n", "// Captions\narticle p.caption,\ntable > caption,\n.code-block-caption\n  font-size: var(--font-size--small)\n  text-align: center\n\n// Caption above a TOCTree\n.toctree-wrapper.compound\n  .caption, :not(.caption) > .caption-text\n    font-size: var(--font-size--small)\n    text-transform: uppercase\n\n    text-align: initial\n    margin-bottom: 0\n\n  > ul\n    margin-top: 0\n    margin-bottom: 0\n", "// Inline code\ncode.literal, .sig-inline\n  background: var(--color-inline-code-background)\n  border-radius: 0.2em\n  // Make the font smaller, and use padding to recover.\n  font-size: var(--font-size--small--2)\n  padding: 0.1em 0.2em\n\n  pre.literal-block &\n    font-size: inherit\n    padding: 0\n\n  p &\n    border: 1px solid var(--color-background-border)\n\n.sig-inline\n  font-family: var(--font-stack--monospace)\n\n// Code and Literal Blocks\n$code-spacing-vertical: 0.625rem\n$code-spacing-horizontal: 0.875rem\n\n// Wraps every literal block + line numbers.\ndiv[class*=\" highlight-\"],\ndiv[class^=\"highlight-\"]\n  margin: 1em 0\n  display: flex\n\n  .table-wrapper\n    margin: 0\n    padding: 0\n\npre\n  margin: 0\n  padding: 0\n  overflow: auto\n\n  // Needed to have more specificity than pygments' \"pre\" selector. :(\n  article[role=\"main\"] .highlight &\n    line-height: 1.5\n\n  &.literal-block,\n  .highlight &\n    font-size: var(--code-font-size)\n    padding: $code-spacing-vertical $code-spacing-horizontal\n\n  // Make it look like all the other blocks.\n  &.literal-block\n    margin-top: 1rem\n    margin-bottom: 1rem\n\n    border-radius: 0.2rem\n    background-color: var(--color-code-background)\n    color: var(--color-code-foreground)\n\n// All code is always contained in this.\n.highlight\n  width: 100%\n  border-radius: 0.2rem\n\n  // Make line numbers and prompts un-selectable.\n  .gp, span.linenos\n    user-select: none\n    pointer-events: none\n\n  // Expand the line-highlighting.\n  .hll\n    display: block\n    margin-left: -$code-spacing-horizontal\n    margin-right: -$code-spacing-horizontal\n    padding-left: $code-spacing-horizontal\n    padding-right: $code-spacing-horizontal\n\n/* Make code block captions be nicely integrated */\n.code-block-caption\n  display: flex\n  padding: $code-spacing-vertical $code-spacing-horizontal\n\n  border-radius: 0.25rem\n  border-bottom-left-radius: 0\n  border-bottom-right-radius: 0\n  font-weight: 300\n  border-bottom: 1px solid\n\n  background-color: var(--color-code-background)\n  color: var(--color-code-foreground)\n  border-color: var(--color-background-border)\n\n  + div[class]\n    margin-top: 0\n    pre\n      border-top-left-radius: 0\n      border-top-right-radius: 0\n\n// When `html_codeblock_linenos_style` is table.\n.highlighttable\n  width: 100%\n  display: block\n  tbody\n    display: block\n\n  tr\n    display: flex\n\n  // Line numbers\n  td.linenos\n    background-color: var(--color-code-background)\n    color: var(--color-code-foreground)\n    padding: $code-spacing-vertical $code-spacing-horizontal\n    padding-right: 0\n    border-top-left-radius: 0.2rem\n    border-bottom-left-radius: 0.2rem\n\n  .linenodiv\n    padding-right: $code-spacing-horizontal\n    font-size: var(--code-font-size)\n    box-shadow: -0.0625rem 0 var(--color-foreground-border) inset\n\n  // Actual code\n  td.code\n    padding: 0\n    display: block\n    flex: 1\n    overflow: hidden\n\n    .highlight\n      border-top-left-radius: 0\n      border-bottom-left-radius: 0\n\n// When `html_codeblock_linenos_style` is inline.\n.highlight\n  span.linenos\n    display: inline-block\n    padding-left: 0\n    padding-right: $code-spacing-horizontal\n    margin-right: $code-spacing-horizontal\n    box-shadow: -0.0625rem 0 var(--color-foreground-border) inset\n", "// Inline Footnote Reference\n.footnote-reference\n  font-size: var(--font-size--small--4)\n  vertical-align: super\n\n// Definition list, listing the content of each note.\n// docutils <= 0.17\ndl.footnote.brackets\n  font-size: var(--font-size--small)\n  color: var(--color-foreground-secondary)\n\n  display: grid\n  grid-template-columns: max-content auto\n  dt\n    margin: 0\n    > .fn-backref\n      margin-left: 0.25rem\n\n    &:after\n      content: \":\"\n\n    .brackets\n      &:before\n        content: \"[\"\n      &:after\n        content: \"]\"\n\n  dd\n    margin: 0\n    padding: 0 1rem\n\n// docutils >= 0.18\naside.footnote\n  font-size: var(--font-size--small)\n  color: var(--color-foreground-secondary)\n\naside.footnote > span,\ndiv.citation > span\n  float: left\n  font-weight: 500\n  padding-right: 0.25rem\n\naside.footnote > *:not(span),\ndiv.citation > p\n  margin-left: 2rem\n", "//\n// Figures\n//\nimg\n  box-sizing: border-box\n  max-width: 100%\n  height: auto\n\narticle\n  figure, .figure\n    border-radius: 0.2rem\n\n    margin: 0\n    :last-child\n      margin-bottom: 0\n\n  .align-left\n    float: left\n    clear: left\n    margin: 0 1rem 1rem\n\n  .align-right\n    float: right\n    clear: right\n    margin: 0 1rem 1rem\n\n  .align-default,\n  .align-center\n    display: block\n    text-align: center\n    margin-left: auto\n    margin-right: auto\n\n  // WELL, table needs to be stylised like a table.\n  table.align-default\n    display: table\n    text-align: initial\n", ".genindex-jumpbox, .domainindex-jumpbox\n  border-top: 1px solid var(--color-background-border)\n  border-bottom: 1px solid var(--color-background-border)\n  padding: 0.25rem\n\n.genindex-section, .domainindex-section\n  h2\n    margin-top: 0.75rem\n    margin-bottom: 0.5rem\n  ul\n    margin-top: 0\n    margin-bottom: 0\n", "ul,\nol\n  padding-left: 1.2rem\n\n  // Space lists out like paragraphs\n  margin-top: 1rem\n  margin-bottom: 1rem\n  // reduce margins within li.\n  li\n    > p:first-child\n      margin-top: 0.25rem\n      margin-bottom: 0.25rem\n\n    > p:last-child\n      margin-top: 0.25rem\n\n    > ul,\n    > ol\n      margin-top: 0.5rem\n      margin-bottom: 0.5rem\n\nol\n  &.arabic\n    list-style: decimal\n  &.loweralpha\n    list-style: lower-alpha\n  &.upperalpha\n    list-style: upper-alpha\n  &.lowerroman\n    list-style: lower-roman\n  &.upperroman\n    list-style: upper-roman\n\n// Don't space lists out when they're \"simple\" or in a `.. toctree::`\n.simple,\n.toctree-wrapper\n  li\n    > ul,\n    > ol\n      margin-top: 0\n      margin-bottom: 0\n\n// Definition Lists\n.field-list,\n.option-list,\ndl:not([class]),\ndl.simple,\ndl.footnote,\ndl.glossary\n  dt\n    font-weight: 500\n    margin-top: 0.25rem\n    + dt\n      margin-top: 0\n\n    .classifier::before\n      content: \":\"\n      margin-left: 0.2rem\n      margin-right: 0.2rem\n\n  dd\n    > p:first-child,\n    ul\n      margin-top: 0.125rem\n\n    ul\n      margin-bottom: 0.125rem\n", ".math-wrapper\n  width: 100%\n  overflow-x: auto\n\ndiv.math\n  position: relative\n  text-align: center\n\n  .headerlink,\n  &:focus .headerlink\n    display: none\n\n  &:hover .headerlink\n    display: inline-block\n\n  span.eqno\n    position: absolute\n    right: 0.5rem\n    top: 50%\n    transform: translate(0, -50%)\n    z-index: 1\n", "// Abbreviations\nabbr[title]\n  cursor: help\n\n// \"Problematic\" content, as identified by <PERSON><PERSON><PERSON>\n.problematic\n  color: var(--color-problematic)\n\n// Keyboard / Mouse \"instructions\"\nkbd:not(.compound)\n  margin: 0 0.2rem\n  padding: 0 0.2rem\n  border-radius: 0.2rem\n  border: 1px solid var(--color-foreground-border)\n  color: var(--color-foreground-primary)\n  vertical-align: text-bottom\n\n  font-size: var(--font-size--small--3)\n  display: inline-block\n\n  box-shadow: 0 0.0625rem 0 rgba(0, 0, 0, 0.2), inset 0 0 0 0.125rem var(--color-background-primary)\n\n  background-color: var(--color-background-secondary)\n\n// Blockquote\nblockquote\n  border-left: 4px solid var(--color-background-border)\n  background: var(--color-background-secondary)\n\n  margin-left: 0\n  margin-right: 0\n  padding: 0.5rem 1rem\n\n  .attribution\n    font-weight: 600\n    text-align: right\n\n  &.pull-quote,\n  &.highlights\n    font-size: 1.25em\n\n  &.epigraph,\n  &.pull-quote\n    border-left-width: 0\n    border-radius: 0.5rem\n\n  &.highlights\n    border-left-width: 0\n    background: transparent\n\n// Center align embedded-in-text images\np .reference img\n  vertical-align: middle\n", "p.rubric\n  line-height: 1.25\n  font-weight: bold\n  font-size: 1.125em\n\n  // For Numpy-style documentation that's got rubrics within it.\n  // https://github.com/pradyunsg/furo/discussions/505\n  dd &\n    line-height: inherit\n    font-weight: inherit\n\n    font-size: var(--font-size--small)\n    text-transform: uppercase\n", "article .sidebar\n  float: right\n  clear: right\n  width: 30%\n\n  margin-left: 1rem\n  margin-right: 0\n\n  border-radius: 0.2rem\n  background-color: var(--color-background-secondary)\n  border: var(--color-background-border) 1px solid\n\n  > *\n    padding-left: 1rem\n    padding-right: 1rem\n\n  > ul, > ol  // lists need additional padding, because bullets.\n    padding-left: 2.2rem\n\n  .sidebar-title\n    margin: 0\n    padding: 0.5rem 1rem\n    border-bottom: var(--color-background-border) 1px solid\n\n    font-weight: 500\n\n// TODO: subtitle\n// TODO: dedicated variables?\n", "[role=main] .table-wrapper.container\n  width: 100%\n  overflow-x: auto\n  margin-top: 1rem\n  margin-bottom: 0.5rem\n  padding: 0.2rem 0.2rem 0.75rem\n\ntable.docutils\n  border-radius: 0.2rem\n  border-spacing: 0\n  border-collapse: collapse\n\n  box-shadow: 0 0.2rem 0.5rem rgba(0, 0, 0, 0.05), 0 0 0.0625rem rgba(0, 0, 0, 0.1)\n\n  th\n    background: var(--color-table-header-background)\n\n  td,\n  th\n    // Space things out properly\n    padding: 0 0.25rem\n\n    // Get the borders looking just-right.\n    border-left: 1px solid var(--color-table-border)\n    border-right: 1px solid var(--color-table-border)\n    border-bottom: 1px solid var(--color-table-border)\n\n    p\n      margin: 0.25rem\n\n    &:first-child\n      border-left: none\n    &:last-child\n      border-right: none\n\n    // MyST-parser tables set these classes for control of column alignment\n    &.text-left\n      text-align: left\n    &.text-right\n      text-align: right\n    &.text-center\n      text-align: center\n", ":target\n  scroll-margin-top: 2.5rem\n\n@media (max-width: $full-width - $sidebar-width)\n  :target\n    scroll-margin-top: calc(2.5rem + var(--header-height))\n\n  // When a heading is selected\n  section > span:target\n    scroll-margin-top: calc(2.8rem + var(--header-height))\n\n// Permalinks\n.headerlink\n  font-weight: 100\n  user-select: none\n\nh1,\nh2,\nh3,\nh4,\nh5,\nh6,\ndl dt,\np.caption,\nfigcaption p,\ntable > caption,\n.code-block-caption\n  > .headerlink\n    margin-left: 0.5rem\n    visibility: hidden\n  &:hover > .headerlink\n    visibility: visible\n\n  // Don't change to link-like, if someone adds the contents directive.\n  > .toc-backref\n    color: inherit\n    text-decoration-line: none\n\n// Figure and table captions are special.\nfigure:hover > figcaption > p > .headerlink,\ntable:hover > caption > .headerlink\n  visibility: visible\n\n:target >,  // Regular section[id] style anchors\nspan:target ~ // Non-regular span[id] style \"extra\" anchors\n  h1,\n  h2,\n  h3,\n  h4,\n  h5,\n  h6\n    &:nth-of-type(1)\n      background-color: var(--color-highlight-on-target)\n      // .headerlink\n      //   visibility: visible\n      code.literal\n        background-color: transparent\n\ntable:target > caption,\nfigure:target\n  background-color: var(--color-highlight-on-target)\n\n// Inline page contents\n.this-will-duplicate-information-and-it-is-still-useful-here li :target\n  background-color: var(--color-highlight-on-target)\n\n// Code block permalinks\n.literal-block-wrapper:target .code-block-caption\n  background-color: var(--color-highlight-on-target)\n\n// When a definition list item is selected\n//\n//   There isn't really an alternative to !important here, due to the\n//   high-specificity of API documentation's selector.\ndt:target\n  background-color: var(--color-highlight-on-target) !important\n\n// When a footnote reference is selected\n.footnote > dt:target + dd,\n.footnote-reference:target\n  background-color: var(--color-highlight-on-target)\n", ".guilabel\n  background-color: var(--color-guilabel-background)\n  border: 1px solid var(--color-guilabel-border)\n  color: var(--color-guilabel-text)\n\n  padding: 0 0.3em\n  border-radius: 0.5em\n  font-size: 0.9em\n", "// This file contains the styles used for stylizing the footer that's shown\n// below the content.\n\nfooter\n  font-size: var(--font-size--small)\n  display: flex\n  flex-direction: column\n\n  margin-top: 2rem\n\n// Bottom of page information\n.bottom-of-page\n  display: flex\n  align-items: center\n  justify-content: space-between\n\n  margin-top: 1rem\n  padding-top: 1rem\n  padding-bottom: 1rem\n\n  color: var(--color-foreground-secondary)\n  border-top: 1px solid var(--color-background-border)\n\n  line-height: 1.5\n\n  @media (max-width: $content-width)\n    text-align: center\n    flex-direction: column-reverse\n    gap: 0.25rem\n\n  .left-details\n    font-size: var(--font-size--small)\n\n  .right-details\n    display: flex\n    flex-direction: column\n    gap: 0.25rem\n    text-align: right\n\n  .icons\n    display: flex\n    justify-content: flex-end\n    gap: 0.25rem\n    font-size: 1rem\n\n    a\n      text-decoration: none\n\n    svg,\n    img\n      font-size: 1.125rem\n      height: 1em\n      width: 1em\n\n// Next/Prev page information\n.related-pages\n  a\n    display: flex\n    align-items: center\n\n    text-decoration: none\n    &:hover .page-info .title\n      text-decoration: underline\n      color: var(--color-link)\n      text-decoration-color: var(--color-link-underline)\n\n    svg.furo-related-icon,\n    svg.furo-related-icon > use\n      flex-shrink: 0\n\n      color: var(--color-foreground-border)\n\n      width: 0.75rem\n      height: 0.75rem\n      margin: 0 0.5rem\n\n    &.next-page\n      max-width: 50%\n\n      float: right\n      clear: right\n      text-align: right\n\n    &.prev-page\n      max-width: 50%\n\n      float: left\n      clear: left\n\n      svg\n        transform: rotate(180deg)\n\n.page-info\n  display: flex\n  flex-direction: column\n  overflow-wrap: anywhere\n\n  .next-page &\n    align-items: flex-end\n\n  .context\n    display: flex\n    align-items: center\n\n    padding-bottom: 0.1rem\n\n    color: var(--color-foreground-muted)\n    font-size: var(--font-size--small)\n    text-decoration: none\n", "// This file contains the styles for the contents of the left sidebar, which\n// contains the navigation tree, logo, search etc.\n\n////////////////////////////////////////////////////////////////////////////////\n// Brand on top of the scrollable tree.\n////////////////////////////////////////////////////////////////////////////////\n.sidebar-brand\n  display: flex\n  flex-direction: column\n  flex-shrink: 0\n\n  padding: var(--sidebar-item-spacing-vertical) var(--sidebar-item-spacing-horizontal)\n  text-decoration: none\n\n.sidebar-brand-text\n  color: var(--color-sidebar-brand-text)\n  overflow-wrap: break-word\n  margin: var(--sidebar-item-spacing-vertical) 0\n  font-size: 1.5rem\n\n.sidebar-logo-container\n  margin: var(--sidebar-item-spacing-vertical) 0\n\n.sidebar-logo\n  margin: 0 auto\n  display: block\n  max-width: 100%\n\n////////////////////////////////////////////////////////////////////////////////\n// Search\n////////////////////////////////////////////////////////////////////////////////\n.sidebar-search-container\n  display: flex\n  align-items: center\n  margin-top: var(--sidebar-search-space-above)\n\n  position: relative\n\n  background: var(--color-sidebar-search-background)\n  &:hover,\n  &:focus-within\n    background: var(--color-sidebar-search-background--focus)\n\n  &::before\n    content: \"\"\n    position: absolute\n    left: var(--sidebar-item-spacing-horizontal)\n    width: var(--sidebar-search-icon-size)\n    height: var(--sidebar-search-icon-size)\n\n    background-color: var(--color-sidebar-search-icon)\n    mask-image: var(--icon-search)\n\n.sidebar-search\n  box-sizing: border-box\n\n  border: none\n  border-top: 1px solid var(--color-sidebar-search-border)\n  border-bottom: 1px solid var(--color-sidebar-search-border)\n\n  padding-top: var(--sidebar-search-input-spacing-vertical)\n  padding-bottom: var(--sidebar-search-input-spacing-vertical)\n  padding-right: var(--sidebar-search-input-spacing-horizontal)\n  padding-left: calc(var(--sidebar-item-spacing-horizontal) + var(--sidebar-search-input-spacing-horizontal) + var(--sidebar-search-icon-size))\n\n  width: 100%\n\n  color: var(--color-sidebar-search-foreground)\n  background: transparent\n  z-index: 10\n\n  &:focus\n    outline: none\n\n  &::placeholder\n    font-size: var(--sidebar-search-input-font-size)\n\n//\n// Hide Search Matches link\n//\n#searchbox .highlight-link\n  padding: var(--sidebar-item-spacing-vertical) var(--sidebar-item-spacing-horizontal) 0\n  margin: 0\n  text-align: center\n\n  a\n    color: var(--color-sidebar-search-icon)\n    font-size: var(--font-size--small--2)\n\n////////////////////////////////////////////////////////////////////////////////\n// Structure/Skeleton of the navigation tree (left)\n////////////////////////////////////////////////////////////////////////////////\n.sidebar-tree\n  font-size: var(--sidebar-item-font-size)\n  margin-top: var(--sidebar-tree-space-above)\n  margin-bottom: var(--sidebar-item-spacing-vertical)\n\n  ul\n    padding: 0\n    margin-top: 0\n    margin-bottom: 0\n\n    display: flex\n    flex-direction: column\n\n    list-style: none\n\n  li\n    position: relative\n    margin: 0\n\n    > ul\n      margin-left: var(--sidebar-item-spacing-horizontal)\n\n  .icon\n    color: var(--color-sidebar-link-text)\n\n  .reference\n    box-sizing: border-box\n    color: var(--color-sidebar-link-text)\n\n    // Fill the parent.\n    display: inline-block\n    line-height: var(--sidebar-item-line-height)\n    text-decoration: none\n\n    // Don't allow long words to cause wrapping.\n    overflow-wrap: anywhere\n\n    height: 100%\n    width: 100%\n\n    padding: var(--sidebar-item-spacing-vertical) var(--sidebar-item-spacing-horizontal)\n\n    &:hover\n      color: var(--color-sidebar-link-text)\n      background: var(--color-sidebar-item-background--hover)\n\n    // Add a nice little \"external-link\" arrow here.\n    &.external::after\n      content: url('data:image/svg+xml,<svg width=\"12\" height=\"12\" xmlns=\"http://www.w3.org/2000/svg\" viewBox=\"0 0 24 24\" stroke-width=\"1.5\" stroke=\"%23607D8B\" fill=\"none\" stroke-linecap=\"round\" stroke-linejoin=\"round\"><path stroke=\"none\" d=\"M0 0h24v24H0z\"/><path d=\"M11 7h-5a2 2 0 0 0 -2 2v9a2 2 0 0 0 2 2h9a2 2 0 0 0 2 -2v-5\" /><line x1=\"10\" y1=\"14\" x2=\"20\" y2=\"4\" /><polyline points=\"15 4 20 4 20 9\" /></svg>')\n      margin: 0 0.25rem\n      vertical-align: middle\n      color: var(--color-sidebar-link-text)\n\n  // Make the current page reference bold.\n  .current-page > .reference\n    font-weight: bold\n\n  label\n    position: absolute\n    top: 0\n    right: 0\n    height: var(--sidebar-item-height)\n    width: var(--sidebar-expander-width)\n\n    cursor: pointer\n    user-select: none\n\n    display: flex\n    justify-content: center\n    align-items: center\n\n  .caption, :not(.caption) > .caption-text\n    font-size: var(--sidebar-caption-font-size)\n    color: var(--color-sidebar-caption-text)\n\n    font-weight: bold\n    text-transform: uppercase\n\n    margin: var(--sidebar-caption-space-above) 0 0 0\n    padding: var(--sidebar-item-spacing-vertical) var(--sidebar-item-spacing-horizontal)\n\n  // If it has children, add a bit more padding to wrap the content to avoid\n  // overlapping with the <label>\n  li.has-children\n    > .reference\n      padding-right: var(--sidebar-expander-width)\n\n  // Colorize the top-level list items and icon.\n  .toctree-l1\n    & > .reference,\n    & > label .icon\n      color: var(--color-sidebar-link-text--top-level)\n\n  // Color changes on hover\n  label\n    background: var(--color-sidebar-item-expander-background)\n    &:hover\n      background: var(--color-sidebar-item-expander-background--hover)\n\n  .current > .reference\n    background: var(--color-sidebar-item-background--current)\n    &:hover\n      background: var(--color-sidebar-item-background--hover)\n\n.toctree-checkbox\n  position: absolute\n  display: none\n\n////////////////////////////////////////////////////////////////////////////////\n// Togglable expand/collapse\n////////////////////////////////////////////////////////////////////////////////\n.toctree-checkbox\n  ~ ul\n    display: none\n\n  ~ label .icon svg\n    transform: rotate(90deg)\n\n.toctree-checkbox:checked\n  ~ ul\n    display: block\n\n  ~ label .icon svg\n    transform: rotate(-90deg)\n", "// This file contains the styles for the contents of the right sidebar, which\n// contains the table of contents for the current page.\n.toc-title-container\n  padding: var(--toc-title-padding)\n  padding-top: var(--toc-spacing-vertical)\n\n.toc-title\n  color: var(--color-toc-title-text)\n  font-size: var(--toc-title-font-size)\n  padding-left: var(--toc-spacing-horizontal)\n  text-transform: uppercase\n\n// If the ToC is not present, hide these elements coz they're not relevant.\n.no-toc\n  display: none\n\n.toc-tree-container\n  padding-bottom: var(--toc-spacing-vertical)\n\n.toc-tree\n  font-size: var(--toc-font-size)\n  line-height: 1.3\n  border-left: 1px solid var(--color-background-border)\n\n  padding-left: calc(var(--toc-spacing-horizontal) - var(--toc-item-spacing-horizontal))\n\n  // Hide the first \"top level\" bullet.\n  > ul > li:first-child\n    padding-top: 0\n    & > ul\n      padding-left: 0\n    & > a\n      display: none\n\n  ul\n    list-style-type: none\n    margin-top: 0\n    margin-bottom: 0\n    padding-left: var(--toc-item-spacing-horizontal)\n  li\n    padding-top: var(--toc-item-spacing-vertical)\n\n    &.scroll-current > .reference\n      color: var(--color-toc-item-text--active)\n      font-weight: bold\n\n  a.reference\n    color: var(--color-toc-item-text)\n    text-decoration: none\n    overflow-wrap: anywhere\n\n.toc-scroll\n  max-height: 100vh\n  overflow-y: scroll\n\n// Be very annoying when someone includes the table of contents\n.contents:not(.this-will-duplicate-information-and-it-is-still-useful-here)\n  color: var(--color-problematic)\n  background: rgba(255, 0, 0, 0.25)\n  &::before\n    content: \"ERROR: Adding a table of contents in Furo-based documentation is unnecessary, and does not work well with existing styling. Add a 'this-will-duplicate-information-and-it-is-still-useful-here' class, if you want an escape hatch.\"\n", "// Shameful hacks, to work around bugs.\n\n// MyST parser doesn't correctly generate classes, to align table contents.\n// https://github.com/executablebooks/MyST-Parser/issues/412\n.text-align\\:left > p\n  text-align: left\n\n.text-align\\:center > p\n  text-align: center\n\n.text-align\\:right > p\n  text-align: right\n"], "names": [], "sourceRoot": ""}