import tomllib
import xml.etree.ElementTree as ET
from typing import Dict, Any, Optional
import traceback
import time
import base64
from pathlib import Path

from loguru import logger

from WechatAPI import WechatAPIClient
from WechatAPI.Client import protector
from utils.event_manager import EventManager


class XYBot:
    def __init__(self, bot_client: WechatAPIClient):
        self.bot = bot_client
        self.wxid = bot_client.wxid
        self.nickname = None
        self.alias = None
        self.phone = None

        with open("main_config.toml", "rb") as f:
            main_config = tomllib.load(f)

        self.ignore_mode = main_config.get("XYBot", {}).get("ignore-mode", "")
        self.whitelist = main_config.get("XYBot", {}).get("whitelist", [])
        self.blacklist = main_config.get("XYBot", {}).get("blacklist", [])

        self.protect_msg_sent = False
        
        # 初始化临时目录
        self.temp_dir = Path("temp/image")
        self.temp_dir.mkdir(parents=True, exist_ok=True)
        
        # 临时文件配置
        self.max_age = 3600  # 1小时
        self.cleanup_interval = 1800  # 30分钟
        self.last_cleanup = 0

    def update_profile(self, wxid: str, nickname: str, alias: str, phone: str):
        """更新机器人信息"""
        self.wxid = wxid
        self.nickname = nickname
        self.alias = alias
        self.phone = phone

    async def process_message(self, message: Dict[str, Any]):
        """处理接收到的消息"""
        if protector.check(14400):
            if not self.protect_msg_sent:
                logger.warning("登录新设备后4小时内请不要操作以避免风控")
                self.protect_msg_sent = True
            return

        msg_type = message.get("MsgType")

        # 预处理消息
        message["FromWxid"] = message.get("FromUserName").get("string")
        message.pop("FromUserName")
        message["ToWxid"] = message.get("ToWxid").get("string")

        # 处理一下自己发的消息
        if message["FromWxid"] == self.wxid and message["ToWxid"].endswith("@chatroom"):  # 自己发发到群聊
            # 由于是自己发送的消息，所以对于自己来说，From和To是反的
            message["FromWxid"], message["ToWxid"] = message["ToWxid"], message["FromWxid"]


        # 根据消息类型触发不同的事件
        if msg_type == 1:  # 文本消息
            await self.process_text_message(message)

        elif msg_type == 3:  # 图片消息
            await self.process_image_message(message)

        elif msg_type == 34:  # 语音消息
            await self.process_voice_message(message)

        elif msg_type == 43:  # 视频消息
            await self.process_video_message(message)

        elif msg_type == 49:  # xml消息
            await self.process_xml_message(message)

        elif msg_type == 10002:  # 系统消息
            await self.process_system_message(message)

        elif msg_type == 37:  # 好友请求
            await EventManager.emit("friend_request", self.bot, message)

        elif msg_type == 51:
            pass

        else:
            logger.info("未知的消息类型: {}", message)

        # 可以继续添加更多消息类型的处理

    async def process_text_message(self, message: Dict[str, Any]):
        """处理文本消息"""
        # 预处理消息
        message["Content"] = message.get("Content").get("string")

        if message["FromWxid"].endswith("@chatroom"):  # 群聊消息
            message["IsGroup"] = True
            split_content = message["Content"].split(":\n", 1)
            if len(split_content) > 1:
                message["Content"] = split_content[1]
                message["SenderWxid"] = split_content[0]
            else:  # 绝对是自己发的消息! qwq
                message["Content"] = split_content[0]
                message["SenderWxid"] = self.wxid

        else:
            message["SenderWxid"] = message["FromWxid"]
            if message["FromWxid"] == self.wxid:  # 自己发的消息
                message["FromWxid"] = message["ToWxid"]
            message["IsGroup"] = False

        try:
            root = ET.fromstring(message["MsgSource"])
            ats = root.find("atuserlist").text if root.find("atuserlist") is not None else ""
        except Exception as e:
            logger.error("解析文本消息失败: {}", e)
            return

        if ats:
            ats = ats.strip(",").split(",")
        else:  # 修复
            ats = []
        message["Ats"] = ats if ats and ats[0] != "" else []

        if self.wxid in ats:
            logger.info("收到被@消息: 消息ID:{} 来自:{} 发送人:{} @:{} 内容:{}",
                        message["MsgId"],
                        message["FromWxid"],
                        message["SenderWxid"],
                        message["Ats"],
                        message["Content"])

            if self.ignore_check(message["FromWxid"], message["SenderWxid"]):
                await EventManager.emit("at_message", self.bot, message)
            return

        logger.info("收到文本消息: 消息ID:{} 来自:{} 发送人:{} @:{} 内容:{}",
                    message["MsgId"],
                    message["FromWxid"],
                    message["SenderWxid"],
                    message["Ats"],
                    message["Content"])

        if self.ignore_check(message["FromWxid"], message["SenderWxid"]):
            await EventManager.emit("text_message", self.bot, message)

    async def _cleanup_temp_files(self):
        """清理过期的临时文件"""
        current_time = time.time()
        
        # 检查是否需要清理
        if current_time - self.last_cleanup < self.cleanup_interval:
            return
            
        try:
            cleaned = 0
            for f in self.temp_dir.glob("*"):
                if current_time - f.stat().st_mtime > self.max_age:
                    f.unlink()
                    cleaned += 1
            
            if cleaned > 0:
                logger.info(f"[XYBot] 清理了 {cleaned} 个过期图片文件")
            self.last_cleanup = current_time
            
        except Exception as e:
            logger.error(f"[XYBot] 清理临时文件失败: {e}")

    async def _save_image(self, image_data: str, msgid: str) -> Optional[str]:
        """保存图片到临时目录
        
        Args:
            image_data: base64编码的图片数据
            msgid: 消息ID
            
        Returns:
            str: 保存的图片路径,如果失败返回None
        """
        try:
            # 确保临时目录存在
            self.temp_dir.mkdir(parents=True, exist_ok=True)
            
            # 保存图片
            image_path = str(self.temp_dir / f"{msgid}.jpg")
            image_bytes = base64.b64decode(image_data)
            with open(image_path, "wb") as f:
                f.write(image_bytes)
                
            return image_path
            
        except Exception as e:
            logger.error(f"[XYBot] 保存图片失败: {e}")
            return None

    async def process_image_message(self, message: Dict[str, Any]):
        """处理图片消息"""
        # 预处理消息
        message["Content"] = message.get("Content").get("string").replace("\n", "").replace("\t", "")

        if message["FromWxid"].endswith("@chatroom"):  # 群聊消息
            message["IsGroup"] = True
            split_content = message["Content"].split(":", 1)
            if len(split_content) > 1:
                message["Content"] = split_content[1]
                message["SenderWxid"] = split_content[0]
            else:  # 绝对是自己发的消息! qwq
                message["Content"] = split_content[0]
                message["SenderWxid"] = self.wxid
        else:
            message["SenderWxid"] = message["FromWxid"]
            if message["FromWxid"] == self.wxid:  # 自己发的消息
                message["FromWxid"] = message["ToWxid"]
            message["IsGroup"] = False

        logger.info("收到图片消息: 消息ID:{} 来自:{} 发送人:{} XML:{}",
                    message["MsgId"],
                    message["FromWxid"],
                    message["SenderWxid"],
                    message["Content"])

        # 清理过期文件
        await self._cleanup_temp_files()

        # 解析图片消息
        aeskey, cdnmidimgurl = None, None
        try:
            root = ET.fromstring(message["Content"])
            img_element = root.find('img')
            if img_element is not None:
                aeskey = img_element.get('aeskey')
                cdnmidimgurl = img_element.get('cdnmidimgurl')
        except Exception as e:
            logger.error("解析图片消息失败: {}", e)
            return

        # 下载并保存图片
        if aeskey and cdnmidimgurl:
            # 下载图片
            image_data = await self.bot.download_image(aeskey, cdnmidimgurl)
            if not image_data:
                logger.error("[XYBot] 下载图片失败")
                return
                
            # 保存图片
            image_path = await self._save_image(image_data, message["MsgId"])
            if not image_path:
                logger.error("[XYBot] 保存图片失败")
                return
                
            # 更新消息内容
            message["Content"] = image_data
            message["ImagePath"] = image_path

        if self.ignore_check(message["FromWxid"], message["SenderWxid"]):
            await EventManager.emit("image_message", self.bot, message)

    async def process_voice_message(self, message: Dict[str, Any]):
        """处理语音消息"""
        # 预处理消息
        message["Content"] = message.get("Content").get("string").replace("\n", "").replace("\t", "")

        if message["FromWxid"].endswith("@chatroom"):  # 群聊消息
            message["IsGroup"] = True
            split_content = message["Content"].split(":", 1)
            if len(split_content) > 1:
                message["Content"] = split_content[1]
                message["SenderWxid"] = split_content[0]
            else:  # 绝对是自己发的消息! qwq
                message["Content"] = split_content[0]
                message["SenderWxid"] = self.wxid
        else:
            message["SenderWxid"] = message["FromWxid"]
            if message["FromWxid"] == self.wxid:  # 自己发的消息
                message["FromWxid"] = message["ToWxid"]
            message["IsGroup"] = False

        logger.info("收到语音消息: 消息ID:{} 来自:{} 发送人:{} XML:{}",
                    message["MsgId"],
                    message["FromWxid"],
                    message["SenderWxid"],
                    message["Content"])

        if message["IsGroup"] or not message.get("ImgBuf", {}).get("buffer", ""):
            # 解析语音消息
            voiceurl, length = None, None
            try:
                root = ET.fromstring(message["Content"])
                voicemsg_element = root.find('voicemsg')
                if voicemsg_element is not None:
                    voiceurl = voicemsg_element.get('voiceurl')
                    length = int(voicemsg_element.get('length'))
            except Exception as e:
                logger.error("解析语音消息失败: {}", e)
                return

            # 下载语音
            if voiceurl and length:
                silk_base64 = await self.bot.download_voice(message["MsgId"], voiceurl, length)
                message["Content"] = await self.bot.silk_base64_to_wav_byte(silk_base64)
        else:
            silk_base64 = message["ImgBuf"]["buffer"]
            message["Content"] = await self.bot.silk_base64_to_wav_byte(silk_base64)

        if self.ignore_check(message["FromWxid"], message["SenderWxid"]):
            await EventManager.emit("voice_message", self.bot, message)

    async def process_xml_message(self, message: Dict[str, Any]):
        """处理xml消息"""
        message["Content"] = message.get("Content").get("string").replace("\n", "").replace("\t", "")

        if message["FromWxid"].endswith("@chatroom"):  # 群聊消息
            message["IsGroup"] = True
            split_content = message["Content"].split(":", 1)
            if len(split_content) > 1:
                message["Content"] = split_content[1]
                message["SenderWxid"] = split_content[0]
            else:  # 绝对是自己发的消息! qwq
                message["Content"] = split_content[0]
                message["SenderWxid"] = self.wxid
        else:
            message["SenderWxid"] = message["FromWxid"]
            if message["FromWxid"] == self.wxid:  # 自己发的消息
                message["FromWxid"] = message["ToWxid"]
            message["IsGroup"] = False

        try:
            root = ET.fromstring(message["Content"])
            type = int(root.find("appmsg").find("type").text)
        except Exception as e:
            logger.error(f"解析xml消息失败: {e}")
            return

        if type == 57:
            await self.process_quote_message(message)
        elif type == 6:
            await self.process_file_message(message)
        elif type == 74:  # 文件消息，但还在上传，不用管
            pass

        else:
            logger.info("未知的xml消息类型: {}", message)

    async def process_quote_message(self, message: Dict[str, Any]):
        """处理引用消息"""
        quote_messsage = {}
        original_content = message.get("Content", "")
        if isinstance(original_content, dict):
            original_content = original_content.get("string", "")
            
        try:
            root = ET.fromstring(original_content)
            appmsg = root.find("appmsg")
            
            if appmsg is not None:
                # 获取引用消息标题
                title = appmsg.find("title")
                if title is not None:
                    text = title.text
                
                # 获取引用消息内容
                refermsg = appmsg.find("refermsg")
                if refermsg is not None:
                    # 获取消息类型
                    msg_type = int(refermsg.find("type").text) if refermsg.find("type") is not None else 1
                    quote_messsage["MsgType"] = msg_type
                    
                    # 对于图片消息,需要保留完整的XML内容
                    if msg_type == 3:  # 图片消息
                        quote_messsage["Content"] = original_content
                    else:
                        # 获取引用消息的完整内容
                        quote_content = refermsg.find("content")
                        quote_messsage["Content"] = quote_content.text if quote_content is not None else original_content
                        
                    quote_messsage["Msgid"] = refermsg.find("svrid").text if refermsg.find("svrid") is not None else ""
                    quote_messsage["NewMsgId"] = refermsg.find("svrid").text if refermsg.find("svrid") is not None else ""
                    quote_messsage["ToWxid"] = refermsg.find("fromusr").text if refermsg.find("fromusr") is not None else ""
                    quote_messsage["FromWxid"] = refermsg.find("chatusr").text if refermsg.find("chatusr") is not None else ""
                    quote_messsage["Nickname"] = refermsg.find("displayname").text if refermsg.find("displayname") is not None else ""
                    quote_messsage["MsgSource"] = refermsg.find("msgsource").text if refermsg.find("msgsource") is not None else ""
                    quote_messsage["Createtime"] = refermsg.find("createtime").text if refermsg.find("createtime") is not None else ""
                else:
                    # 如果找不到refermsg，使用原始内容
                    quote_messsage["Content"] = original_content
                    quote_messsage["MsgType"] = 1
                    quote_messsage["Msgid"] = ""
            else:
                # 如果找不到appmsg，使用原始内容
                quote_messsage["Content"] = original_content
                quote_messsage["MsgType"] = 1
                quote_messsage["Msgid"] = ""
                
        except ET.ParseError as e:
            logger.warning(f"解析引用消息XML失败: {original_content[:200]}")
            # 解析失败时使用原始内容
            quote_messsage["Content"] = original_content
            quote_messsage["MsgType"] = 1
            quote_messsage["Msgid"] = ""
            
        except Exception as e:
            logger.error(f"处理引用消息失败: {e}")
            logger.error(f"错误详情: {traceback.format_exc()}")
            # 确保基本字段存在
            quote_messsage["Content"] = original_content
            quote_messsage["MsgType"] = 1
            quote_messsage["Msgid"] = ""
            
        # 设置基本字段
        quote_messsage["FromWxid"] = message.get("FromWxid", "")
        quote_messsage["ToWxid"] = message.get("ToWxid", "")
        quote_messsage["SenderWxid"] = message.get("SenderWxid", "")
        
        # 设置消息内容
        message["Content"] = text if "text" in locals() else quote_messsage.get("Content", "")
        message["Quote"] = quote_messsage
        
        logger.info("收到引用消息: 消息ID:{} 来自:{} 发送人:{} 内容:{} 引用:{}",
                    message.get("MsgId"),
                    message.get("FromWxid"),
                    message.get("SenderWxid"),
                    message.get("Content", ""),
                    message["Quote"])
                    
        if self.ignore_check(message["FromWxid"], message["SenderWxid"]):
            await EventManager.emit("quote_message", self.bot, message)

    async def process_video_message(self, message):
        # 预处理消息
        message["Content"] = message.get("Content").get("string")

        if message["FromWxid"].endswith("@chatroom"):  # 群聊消息
            message["IsGroup"] = True
            split_content = message["Content"].split(":", 1)
            if len(split_content) > 1:
                message["Content"] = split_content[1]
                message["SenderWxid"] = split_content[0]
            else:  # 绝对是自己发的消息! qwq
                message["Content"] = split_content[0]
                message["SenderWxid"] = self.wxid
        else:
            message["SenderWxid"] = message["FromWxid"]
            if message["FromWxid"] == self.wxid:  # 自己发的消息
                message["FromWxid"] = message["ToWxid"]
            message["IsGroup"] = False

        logger.info("收到视频消息: 消息ID:{} 来自:{} 发送人:{} XML:{}",
                    message["MsgId"],
                    message["FromWxid"],
                    message["SenderWxid"],
                    message["Content"])

        message["Video"] = await self.bot.download_video(message["MsgId"])

        if self.ignore_check(message["FromWxid"], message["SenderWxid"]):
            await EventManager.emit("video_message", self.bot, message)

    async def process_file_message(self, message: Dict[str, Any]):
        """处理文件消息"""
        try:
            root = ET.fromstring(message["Content"])
            filename = root.find("appmsg").find("title").text
            attach_id = root.find("appmsg").find("appattach").find("attachid").text
            file_extend = root.find("appmsg").find("appattach").find("fileext").text
        except Exception as error:
            logger.error(f"解析文件消息失败: {error}")
            return

        message["Filename"] = filename
        message["FileExtend"] = file_extend

        logger.info("收到文件消息: 消息ID:{} 来自:{} 发送人:{} XML:{}",
                    message["MsgId"],
                    message["FromWxid"],
                    message["SenderWxid"],
                    message["Content"])

        message["File"] = await self.bot.download_attach(attach_id)

        if self.ignore_check(message["FromWxid"], message["SenderWxid"]):
            await EventManager.emit("file_message", self.bot, message)

    async def process_system_message(self, message: Dict[str, Any]):
        """处理系统消息"""
        # 预处理消息
        message["Content"] = message.get("Content").get("string")

        if message["FromWxid"].endswith("@chatroom"):  # 群聊消息
            message["IsGroup"] = True
            split_content = message["Content"].split(":", 1)
            if len(split_content) > 1:
                message["Content"] = split_content[1]
                message["SenderWxid"] = split_content[0]
            else:  # 绝对是自己发的消息! qwq
                message["Content"] = split_content[0]
                message["SenderWxid"] = self.wxid
        else:
            message["SenderWxid"] = message["FromWxid"]
            if message["FromWxid"] == self.wxid:  # 自己发的消息
                message["FromWxid"] = message["ToWxid"]
            message["IsGroup"] = False

        try:
            root = ET.fromstring(message["Content"])
            msg_type = root.attrib["type"]
            logger.debug(f"系统消息类型: {msg_type}")
        except Exception as e:
            logger.error(f"解析系统消息失败: {e}")
            return

        if msg_type == "pat":
            await self.process_pat_message(message)
        elif msg_type == "ClientCheckGetExtInfo":
            pass
        elif msg_type == "sysmsgtemplate":
            # 触发系统消息事件
            if self.ignore_check(message["FromWxid"], message["SenderWxid"]):
                await EventManager.emit("system_message", self.bot, message)
        else:
            logger.info("未知的系统消息类型: {}", message)

    async def process_pat_message(self, message: Dict[str, Any]):
        """处理拍一拍请求消息"""
        try:
            root = ET.fromstring(message["Content"])
            pat = root.find("pat")
            patter = pat.find("fromusername").text
            patted = pat.find("pattedusername").text
            pat_suffix = pat.find("patsuffix").text
        except Exception as e:
            logger.error(f"解析拍一拍消息失败: {e}")
            return

        message["Patter"] = patter
        message["Patted"] = patted
        message["PatSuffix"] = pat_suffix

        logger.info("收到拍一拍消息: 消息ID:{} 来自:{} 发送人:{} 拍者:{} 被拍:{} 后缀:{}",
                    message["MsgId"],
                    message["FromWxid"],
                    message["SenderWxid"],
                    message["Patter"],
                    message["Patted"],
                    message["PatSuffix"])

        if self.ignore_check(message["FromWxid"], message["SenderWxid"]):
            await EventManager.emit("pat_message", self.bot, message)

    def ignore_check(self, FromWxid: str, SenderWxid: str):
        if self.ignore_mode == "Whitelist":
            return (FromWxid in self.whitelist) or (SenderWxid in self.whitelist)
        elif self.ignore_mode == "blacklist":
            return (FromWxid not in self.blacklist) and (SenderWxid not in self.blacklist)
        else:
            return True
