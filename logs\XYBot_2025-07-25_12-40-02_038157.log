2025-07-25 12:40:02 | SUCCESS | 读取主设置成功
2025-07-25 12:40:02 | DEBUG | 最终使用的 Redis 主机地址: 127.0.0.1
2025-07-25 12:40:03 | INFO | 2025/07/25 12:40:03 GetRedisAddr: 127.0.0.1:6379
2025-07-25 12:40:03 | INFO | 2025/07/25 12:40:03 Redis连接成功 | 地址:127.0.0.1:6379 DB:0
2025-07-25 12:40:03 | INFO | 2025/07/25 12:40:03 Server start at :9000
2025-07-25 12:40:03 | SUCCESS | WechatAPI服务已启动
2025-07-25 12:40:04 | INFO | 登录账号信息: wxid: wxid_4usgcju5ey9q29  昵称: 瑶瑶  微信号: Today-KFC  手机号: None
2025-07-25 12:40:04 | INFO | 登录设备信息: device_name: <PERSON>'s Pad  device_id: 49d050a7e94d1ecc40790d74b1c4369f
2025-07-25 12:40:04 | SUCCESS | 登录成功
2025-07-25 12:40:04 | SUCCESS | 已开启自动心跳
2025-07-25 12:40:04 | INFO | 成功加载表情映射文件，共 539 条记录
2025-07-25 12:40:04 | SUCCESS | 数据库初始化成功
2025-07-25 12:40:04 | SUCCESS | 定时任务已启动
2025-07-25 12:40:04 | SUCCESS | 插件 AISignIn 已加载定时任务: {'plugins.AISignIn.main.AISignIn.auto_signin_task'}
2025-07-25 12:40:04 | INFO | 已加载管理员列表: ['wxid_ubbh6q832tcs21', 'admin-wxid']
2025-07-25 12:40:04 | INFO | 播客API初始化成功
2025-07-25 12:40:04 | INFO | 加载配置完成，转换后的数据: {'gh_7057516c9e71': {'name': '唱舞全明星', 'groups': ['27852221909@chatroom', '55878994168@chatroom', '51891329927@chatroom']}, 'gh_d4d21a0e4623': {'name': '两颗桃', 'groups': ['48097389945@chatroom']}, 'gh_410ff2fa6ec9': {'name': 'NGC660 Ai研究院', 'groups': ['47442567074@chatroom']}, 'gh_f78c53c507c0': {'name': '不正常吐槽君', 'groups': ['48097389945@chatroom']}, 'gh_ba3381c847b3': {'name': '唱舞星计划', 'groups': ['51891329927@chatroom']}, 'gh_1a102fb4cad9': {'name': '我在娱乐圈的唯一人脉', 'groups': ['48097389945@chatroom', '27852221909@chatroom']}}
2025-07-25 12:40:04 | INFO | ArticleForwarder插件初始化完成 - 监控配置: {'gh_7057516c9e71': {'name': '唱舞全明星', 'groups': ['27852221909@chatroom', '55878994168@chatroom', '51891329927@chatroom']}, 'gh_d4d21a0e4623': {'name': '两颗桃', 'groups': ['48097389945@chatroom']}, 'gh_410ff2fa6ec9': {'name': 'NGC660 Ai研究院', 'groups': ['47442567074@chatroom']}, 'gh_f78c53c507c0': {'name': '不正常吐槽君', 'groups': ['48097389945@chatroom']}, 'gh_ba3381c847b3': {'name': '唱舞星计划', 'groups': ['51891329927@chatroom']}, 'gh_1a102fb4cad9': {'name': '我在娱乐圈的唯一人脉', 'groups': ['48097389945@chatroom', '27852221909@chatroom']}}
2025-07-25 12:40:04 | DEBUG | [TempFileManager] 添加清理规则: default
2025-07-25 12:40:04 | DEBUG | [TempFileManager] 添加清理规则: images
2025-07-25 12:40:04 | DEBUG | [TempFileManager] 添加清理规则: videos
2025-07-25 12:40:04 | DEBUG | [TempFileManager] 添加清理规则: audio
2025-07-25 12:40:04 | DEBUG | [TempFileManager] 添加清理规则: temp
2025-07-25 12:40:04 | DEBUG | [TempFileManager] 添加清理规则: large_files
2025-07-25 12:40:04 | INFO | [TempFileManager] 临时文件清理任务已启动
2025-07-25 12:40:05 | INFO | [ChatSummary] 数据库初始化成功
2025-07-25 12:40:05 | SUCCESS | 插件 GoodMorning 已加载定时任务: {'plugins.GoodMorning.main.GoodMorning.daily_task'}
2025-07-25 12:40:05 | SUCCESS | 插件 News 已加载定时任务: {'plugins.News.main.News.noon_news', 'plugins.News.main.News.night_news'}
2025-07-25 12:40:05 | INFO | [PatReply] AMR文件已存在且是最新的: C:\XYBotV2\data\paiyipai\1_BFB5258B-2B13-4CF4-A40C-5BCCE74E5414_converted.amr
2025-07-25 12:40:05 | DEBUG | [PatReply] 从缓存加载base64: 18228 字符
2025-07-25 12:40:05 | SUCCESS | 插件 QuarkSignIn 已加载定时任务: {'plugins.QuarkSignIn.main.QuarkSignIn.auto_signin_task'}
2025-07-25 12:40:05 | INFO | [QuarkSignIn] 已设置自动签到时间: 08:00
2025-07-25 12:40:05 | INFO | 插件状态: 启用, 检查间隔: 3600秒, 最大重试次数: 3, 重试延迟: 5秒
2025-07-25 12:40:05 | INFO | 已加载管理员列表: ['wxid_ubbh6q832tcs21', 'admin-wxid']
2025-07-25 12:40:05 | INFO | 已加载 2 个改名监控群和 1 个退群监控群
2025-07-25 12:40:05 | INFO | [RenameReminder] 开始启用插件...
2025-07-25 12:40:05 | SUCCESS | 插件 RenameReminder 已加载定时任务: {'plugins.RenameReminder.main.RenameReminder.check_name_changes'}
2025-07-25 12:40:05 | INFO | [RenameReminder] 插件已启用，开始初始化...
2025-07-25 12:40:05 | INFO | [RenameReminder] 支持的命令: 开启改名提醒, 关闭改名提醒, 开启退群提醒, 关闭退群提醒, 查看提醒状态
2025-07-25 12:40:05 | INFO | 已设置检查间隔为 3600 秒
2025-07-25 12:40:05 | INFO | 已加载 2 个监控群，开始更新成员列表
2025-07-25 12:40:05 | DEBUG | 已更新群 51891329927@chatroom 的成员列表
2025-07-25 12:40:06 | DEBUG | 已更新群 27852221909@chatroom 的成员列表
2025-07-25 12:40:06 | SUCCESS | 插件 TimerTask 已加载定时任务: {'plugins.TimerTask.main.TimerTask.check_tasks'}
2025-07-25 12:40:06 | INFO | [VideoDemand] 加载了 128 个视频类别配置
2025-07-25 12:40:06 | INFO | [YaoyaoPlugin] 私聊黑名单用户: ['gh_a5cf07d474f1', 'gh_ba3381c847b3']
2025-07-25 12:40:06 | INFO | [YaoyaoPlugin] 已加载管理员列表: ['wxid_ubbh6q832tcs21', 'admin-wxid']
2025-07-25 12:40:06 | INFO | [yuanbao] 插件初始化完成
2025-07-25 12:40:06 | DEBUG | [yuanbao] 指令: ['元宝', 'yuanbao']
2025-07-25 12:40:06 | DEBUG | [yuanbao] 绘图模型: gpt_175B_0404
2025-07-25 12:40:06 | DEBUG | [yuanbao] 认证信息加载状态: hy_token=True, hy_user=True, agent_id=True, x_uskey=True
2025-07-25 12:40:06 | SUCCESS | 已加载插件: ['AdminPoint', 'AdminSignInReset', 'AdminWhitelist', 'AISignIn', 'AppMessageTester', 'ArticleForwarder', 'BaiduAgentsPlugin', 'BaiduDraw', 'BotStatus', 'ChatSummary', 'DanceSignInPlugin', 'DeepseekPlugin', 'Doubao', 'DoubaoDrawing', 'DoubaoImageRecognition', 'DouBaoImageToImage', 'DouBaoImageToVideo', 'DoubaoVideoSearch', 'DouHui', 'EmojiTestPlugin', 'FixedQuoteTest', 'GetContact', 'Gomoku', 'GoodMorning', 'HunyuanDraw', 'ImageEcho', 'ImageToImage', 'JiemengDraw', 'KeLingImageToImage', 'KlingAI', 'Leaderboard', 'LuckyDraw', 'ManagePlugin', 'MeituAI', 'Menu', 'MiniProgramTester', 'Music', 'News', 'PatReply', 'PointTrade', 'QuarkSignIn', 'QueryPoint', 'RandomMember', 'RandomPicture', 'RenameReminder', 'RevokePlugin', 'RoboNeo', 'SignIn', 'TempFileManagerPlugin', 'TencentLke', 'TimerTask', 'TongyiDraw', 'URLShortener', 'VideoDemand', 'VideoParserPlugin', 'VideoTest', 'VivoAgentsPlugin', 'VoiceTest', 'Warthunder', 'WeatherQuery', 'WelcomePlugin', 'Xunfei', 'YaoyaoPlugin', 'Yuanbao', '抽签', '造梦次元']
2025-07-25 12:40:06 | INFO | 处理堆积消息中
2025-07-25 12:40:06 | DEBUG | 接受到 3 条消息
2025-07-25 12:40:08 | SUCCESS | 处理堆积消息完毕
2025-07-25 12:40:08 | SUCCESS | 开始处理消息
2025-07-25 12:40:18 | DEBUG | 收到消息: {'MsgId': 64657326, 'FromUserName': {'string': '48097389945@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 1, 'Content': {'string': 'wxid_l9koi6kli78i22:\n[吃瓜][吃瓜][吃瓜]'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1753418440, 'MsgSource': '<msgsource>\n\t<pua>1</pua>\n\t<silence>0</silence>\n\t<membercount>61</membercount>\n\t<signature>N0_V1_AR9DmFS+|v1_G2VvcXE/</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'PushContent': '十五 : [吃瓜][吃瓜][吃瓜]', 'NewMsgId': 534983713799737760, 'MsgSeq': 871398711}
2025-07-25 12:40:18 | INFO | 收到表情消息: 消息ID:64657326 来自:48097389945@chatroom 发送人:wxid_l9koi6kli78i22 @:[] 内容:[吃瓜][吃瓜][吃瓜]
2025-07-25 12:40:18 | DEBUG | [DoubaoImageRecognition] 缓存表情消息: 534983713799737760
2025-07-25 12:40:26 | DEBUG | 收到消息: {'MsgId': 1552415046, 'FromUserName': {'string': '48097389945@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 47, 'Content': {'string': 'wxid_1kmtp0tkdsdu22:\n<msg><emoji fromusername="wxid_1kmtp0tkdsdu22" tousername="48097389945@chatroom" type="2" idbuffer="media:0_0" md5="dd383557009c49d00d283fb7437e8df2" len="12938" productid="com.tencent.xin.emoticon.person.stiker_1474739821fdc543cf7af3f1a9" androidmd5="dd383557009c49d00d283fb7437e8df2" androidlen="12938" s60v3md5="dd383557009c49d00d283fb7437e8df2" s60v3len="12938" s60v5md5="dd383557009c49d00d283fb7437e8df2" s60v5len="12938" cdnurl="http://wxapp.tc.qq.com/262/20304/stodownload?m=dd383557009c49d00d283fb7437e8df2&amp;filekey=30340201010420301e020201060402535a0410dd383557009c49d00d283fb7437e8df20202328a040d00000004627466730000000131&amp;hy=SZ&amp;storeid=32303231303632353131353233333030303833316561336331353434623231623262353030393030303030313036&amp;bizid=1023" designerid="" thumburl="http://mmbiz.qpic.cn/mmemoticon/Q3auHgzwzM4pQElh3x6XDz3aLxwqdVMOGuFGHpiarib9BZRXWEjVyYkAUsZpJ9KahS/0" encrypturl="http://wxapp.tc.qq.com/262/20304/stodownload?m=60c3d31320e3a3f093479d9187693436&amp;filekey=30340201010420301e020201060402535a041060c3d31320e3a3f093479d918769343602023290040d00000004627466730000000131&amp;hy=SZ&amp;storeid=32303231303632353131353233333030306134666161336331353434623234656463356630393030303030313036&amp;bizid=1023" aeskey="672ba9f2f9d7ce249269f345b281c1f0" externurl="http://wxapp.tc.qq.com/262/20304/stodownload?m=6040115b2fe04ba574670239ec73a689&amp;filekey=30340201010420301e020201060402535a04106040115b2fe04ba574670239ec73a68902022430040d00000004627466730000000131&amp;hy=SZ&amp;storeid=32303231303632353131353233333030306337333338336331353434623266303339356630393030303030313036&amp;bizid=1023" externmd5="c4ac09f3f2df1424403786dca142498c" width="240" height="240" tpurl="" tpauthkey="" attachedtext="" attachedtextcolor="" lensid="" emojiattr="" linkid="" desc="Cg8KBXpoX2NuEgblkIPnk5wKEgoFemhfdHcSCeWQg+ilv+eTnAoVCgdkZWZhdWx0EgpXYXRlcm1lbG9u"></emoji><gameext type="0" content="0"></gameext></msg>'}, 'Status': 3, 'ImgStatus': 2, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1753418449, 'MsgSource': '<msgsource>\n\t<silence>0</silence>\n\t<membercount>61</membercount>\n\t<signature>N0_V1_RlxF8VOW|v1_ZhpQYOdd</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'PushContent': '☞在群聊中发了一个表情', 'NewMsgId': 8231291534029451591, 'MsgSeq': 871398712}
2025-07-25 12:40:26 | INFO | 收到表情消息: 消息ID:1552415046 来自:48097389945@chatroom 发送人:wxid_1kmtp0tkdsdu22 MD5:dd383557009c49d00d283fb7437e8df2 大小:12938
2025-07-25 12:40:27 | DEBUG | [DoubaoImageRecognition] 缓存表情消息: 8231291534029451591
2025-07-25 12:40:35 | DEBUG | 收到消息: {'MsgId': 94587301, 'FromUserName': {'string': '48097389945@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 1, 'Content': {'string': 'wxid_wlnzvr8ivgd422:\n开放群聊加好友了，需要的赶紧加吧，明天关掉'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1753418457, 'MsgSource': '<msgsource>\n\t<pua>1</pua>\n\t<eggIncluded>1</eggIncluded>\n\t<silence>0</silence>\n\t<membercount>61</membercount>\n\t<signature>N0_V1_UBrenfmU|v1_KnTcfHHK</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'PushContent': '锦岚（开玩笑就退群版） : 开放群聊加好友了，需要的赶紧加吧，明天关掉', 'NewMsgId': 6276174878581786479, 'MsgSeq': 871398713}
2025-07-25 12:40:35 | INFO | 收到文本消息: 消息ID:94587301 来自:48097389945@chatroom 发送人:wxid_wlnzvr8ivgd422 @:[] 内容:开放群聊加好友了，需要的赶紧加吧，明天关掉
2025-07-25 12:40:36 | INFO | 成功加载表情映射文件，共 539 条记录
2025-07-25 12:40:36 | DEBUG | 处理消息内容: '开放群聊加好友了，需要的赶紧加吧，明天关掉'
2025-07-25 12:40:36 | DEBUG | 消息内容 '开放群聊加好友了，需要的赶紧加吧，明天关掉' 不匹配任何命令，忽略
2025-07-25 12:40:51 | DEBUG | 收到消息: {'MsgId': 266111950, 'FromUserName': {'string': '48097389945@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 1, 'Content': {'string': 'wxid_l9koi6kli78i22:\n加好友就是🤡'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1753418474, 'MsgSource': '<msgsource>\n\t<pua>1</pua>\n\t<silence>0</silence>\n\t<membercount>61</membercount>\n\t<signature>N0_V1_WQNs8GPv|v1_B2LNogJB</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'PushContent': '十五 : 加好友就是🤡', 'NewMsgId': 7930735540245270298, 'MsgSeq': 871398714}
2025-07-25 12:40:51 | INFO | 收到文本消息: 消息ID:266111950 来自:48097389945@chatroom 发送人:wxid_l9koi6kli78i22 @:[] 内容:加好友就是🤡
2025-07-25 12:40:52 | DEBUG | 处理消息内容: '加好友就是🤡'
2025-07-25 12:40:52 | DEBUG | 消息内容 '加好友就是🤡' 不匹配任何命令，忽略
2025-07-25 12:40:55 | DEBUG | 收到消息: {'MsgId': 2038314717, 'FromUserName': {'string': '48097389945@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 47, 'Content': {'string': 'wxid_l9koi6kli78i22:\n<msg><emoji fromusername="wxid_l9koi6kli78i22" tousername="48097389945@chatroom" type="1" idbuffer="media:0_0" md5="70e645b7d52832feb842c363e8176288" len="58096" productid="" androidmd5="70e645b7d52832feb842c363e8176288" androidlen="58096" s60v3md5="70e645b7d52832feb842c363e8176288" s60v3len="58096" s60v5md5="70e645b7d52832feb842c363e8176288" s60v5len="58096" cdnurl="http://vweixinf.tc.qq.com/110/20401/stodownload?m=70e645b7d52832feb842c363e8176288&amp;filekey=30440201010430302e02016e0402534804203730653634356237643532383332666562383432633336336538313736323838020300e2f0040d00000004627466730000000132&amp;hy=SH&amp;storeid=2628705660006c95b0d68aaaa0000006e01004fb153482136bb40b6f638a56&amp;ef=1&amp;bizid=1022" designerid="" thumburl="" encrypturl="http://vweixinf.tc.qq.com/110/20402/stodownload?m=e7a721c7622fc455a228f585365475ad&amp;filekey=30440201010430302e02016e0402534804206537613732316337363232666334353561323238663538353336353437356164020300e300040d00000004627466730000000132&amp;hy=SH&amp;storeid=262870566000788590d68aaaa0000006e02004fb253482136bb40b6f638a67&amp;ef=2&amp;bizid=1022" aeskey="890b236d6bcd41cb913ff3e2ba7613c2" externurl="http://vweixinf.tc.qq.com/110/20403/stodownload?m=a204478aa0fb784f9c03e36031afaa66&amp;filekey=3043020101042f302d02016e040253480420613230343437386161306662373834663963303365333630333161666161363602020d00040d00000004627466730000000132&amp;hy=SH&amp;storeid=26287056600083f880d68aaaa0000006e03004fb353482136bb40b6f638a79&amp;ef=3&amp;bizid=1022" externmd5="95e07ee6e2eef328e18286895c2de4ef" width="270" height="235" tpurl="" tpauthkey="" attachedtext="" attachedtextcolor="" lensid="" emojiattr="" linkid="" desc=""></emoji></msg>'}, 'Status': 3, 'ImgStatus': 2, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1753418477, 'MsgSource': '<msgsource>\n\t<silence>0</silence>\n\t<membercount>60</membercount>\n\t<signature>N0_V1_FKnfmEwg|v1_jvlQU3l7</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'PushContent': '十五在群聊中发了一个表情', 'NewMsgId': 6061240780152026225, 'MsgSeq': 871398715}
2025-07-25 12:40:55 | INFO | 收到表情消息: 消息ID:2038314717 来自:48097389945@chatroom 发送人:wxid_l9koi6kli78i22 MD5:70e645b7d52832feb842c363e8176288 大小:58096
2025-07-25 12:40:55 | DEBUG | [DoubaoImageRecognition] 缓存表情消息: 6061240780152026225
2025-07-25 12:40:57 | DEBUG | 收到消息: {'MsgId': 763254396, 'FromUserName': {'string': '48097389945@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 49, 'Content': {'string': 'wxid_lneb7n23o4lg12:\n<?xml version="1.0"?>\n<msg>\n\t<appmsg appid="wx485a97c844086dc9" sdkver="0">\n\t\t<title>跑路了</title>\n\t\t<des>锦岚（开玩笑就退群版）</des>\n\t\t<username />\n\t\t<action>view</action>\n\t\t<type>3</type>\n\t\t<showtype>0</showtype>\n\t\t<content />\n\t\t<url />\n\t\t<lowurl />\n\t\t<forwardflag>0</forwardflag>\n\t\t<dataurl>https://v3-luna.douyinvod.com/fa9827e82d39587fecf868d832cca2d1/68846aab/video/tos/cn/tos-cn-ve-2774/o8DBlgCYn6AcMZsxBqFEgMDstWCZbfuYQAfsDI/</dataurl>\n\t\t<lowdataurl />\n\t\t<contentattr>0</contentattr>\n\t\t<streamvideo>\n\t\t\t<streamvideourl />\n\t\t\t<streamvideototaltime>0</streamvideototaltime>\n\t\t\t<streamvideotitle />\n\t\t\t<streamvideowording />\n\t\t\t<streamvideoweburl />\n\t\t\t<streamvideothumburl />\n\t\t\t<streamvideoaduxinfo />\n\t\t\t<streamvideopublishid />\n\t\t</streamvideo>\n\t\t<canvasPageItem>\n\t\t\t<canvasPageXml><![CDATA[]]></canvasPageXml>\n\t\t</canvasPageItem>\n\t\t<appattach>\n\t\t\t<totallen>0</totallen>\n\t\t\t<attachid />\n\t\t\t<cdnattachurl />\n\t\t\t<emoticonmd5 />\n\t\t\t<aeskey />\n\t\t\t<fileext />\n\t\t\t<islargefilemsg>0</islargefilemsg>\n\t\t</appattach>\n\t\t<extinfo />\n\t\t<androidsource>2</androidsource>\n\t\t<thumburl />\n\t\t<mediatagname />\n\t\t<messageaction><![CDATA[]]></messageaction>\n\t\t<messageext><![CDATA[]]></messageext>\n\t\t<emoticongift>\n\t\t\t<packageflag>0</packageflag>\n\t\t\t<packageid />\n\t\t</emoticongift>\n\t\t<emoticonshared>\n\t\t\t<packageflag>0</packageflag>\n\t\t\t<packageid />\n\t\t</emoticonshared>\n\t\t<designershared>\n\t\t\t<designeruin>0</designeruin>\n\t\t\t<designername>null</designername>\n\t\t\t<designerrediretcturl><![CDATA[null]]></designerrediretcturl>\n\t\t</designershared>\n\t\t<emotionpageshared>\n\t\t\t<tid>0</tid>\n\t\t\t<title>null</title>\n\t\t\t<desc>null</desc>\n\t\t\t<iconUrl><![CDATA[null]]></iconUrl>\n\t\t\t<secondUrl>null</secondUrl>\n\t\t\t<pageType>0</pageType>\n\t\t\t<setKey>null</setKey>\n\t\t</emotionpageshared>\n\t\t<webviewshared>\n\t\t\t<shareUrlOriginal />\n\t\t\t<shareUrlOpen />\n\t\t\t<jsAppId />\n\t\t\t<publisherId />\n\t\t\t<publisherReqId />\n\t\t</webviewshared>\n\t\t<template_id />\n\t\t<md5 />\n\t\t<websearch>\n\t\t\t<rec_category>0</rec_category>\n\t\t\t<channelId>0</channelId>\n\t\t</websearch>\n\t\t<weappinfo>\n\t\t\t<username />\n\t\t\t<appid />\n\t\t\t<appservicetype>0</appservicetype>\n\t\t\t<secflagforsinglepagemode>0</secflagforsinglepagemode>\n\t\t\t<videopageinfo>\n\t\t\t\t<thumbwidth>0</thumbwidth>\n\t\t\t\t<thumbheight>0</thumbheight>\n\t\t\t\t<fromopensdk>0</fromopensdk>\n\t\t\t</videopageinfo>\n\t\t</weappinfo>\n\t\t<statextstr>GhQKEnd4NDg1YTk3Yzg0NDA4NmRjOQ==</statextstr>\n\t\t<songalbumurl>http://wx.qlogo.cn/mmhead/ver_1/iaZCKGjtAic5icOnI1ibaY7DCzx0SVR3ic6NXwcAHpFTZIzDcvzMaeNlEYqezUL04icbD6ZylLwcGMYIzQy6kjY4AdicRyb39fASpxswdSeDrs5tomibd2sADOgpbRIF6jic2v1ibFafdgicYm69maZehIgNhg8Tw/0</songalbumurl>\n\t\t<songlyric>[00:01.63]闻 更漏咽\n[00:04.31]频教前尘辞长夜\n[00:08.35]久无眠 深坐对宫檐\n[00:15.19]多情最是春庭雪\n[00:18.83]年年落满离人苑\n[00:22.87]薛涛笺 上言若如初见\n[00:29.87]这一世\n[00:31.99]太漫长却止步咫尺天涯间\n[00:37.03]谁仍记 那梨花若雪时节\n</songlyric>\n\t\t<musicShareItem>\n\t\t\t<musicDuration>0</musicDuration>\n\t\t</musicShareItem>\n\t\t<finderLiveProductShare>\n\t\t\t<finderLiveID><![CDATA[]]></finderLiveID>\n\t\t\t<finderUsername><![CDATA[]]></finderUsername>\n\t\t\t<finderObjectID><![CDATA[]]></finderObjectID>\n\t\t\t<finderNonceID><![CDATA[]]></finderNonceID>\n\t\t\t<liveStatus><![CDATA[]]></liveStatus>\n\t\t\t<appId><![CDATA[]]></appId>\n\t\t\t<pagePath><![CDATA[]]></pagePath>\n\t\t\t<productId><![CDATA[]]></productId>\n\t\t\t<coverUrl><![CDATA[]]></coverUrl>\n\t\t\t<productTitle><![CDATA[]]></productTitle>\n\t\t\t<marketPrice><![CDATA[0]]></marketPrice>\n\t\t\t<sellingPrice><![CDATA[0]]></sellingPrice>\n\t\t\t<platformHeadImg><![CDATA[]]></platformHeadImg>\n\t\t\t<platformName><![CDATA[]]></platformName>\n\t\t\t<shopWindowId><![CDATA[]]></shopWindowId>\n\t\t\t<flashSalePrice><![CDATA[0]]></flashSalePrice>\n\t\t\t<flashSaleEndTime><![CDATA[0]]></flashSaleEndTime>\n\t\t\t<ecSource><![CDATA[]]></ecSource>\n\t\t\t<sellingPriceWording><![CDATA[]]></sellingPriceWording>\n\t\t\t<platformIconURL><![CDATA[]]></platformIconURL>\n\t\t\t<firstProductTagURL><![CDATA[]]></firstProductTagURL>\n\t\t\t<firstProductTagAspectRatioString><![CDATA[0.0]]></firstProductTagAspectRatioString>\n\t\t\t<secondProductTagURL><![CDATA[]]></secondProductTagURL>\n\t\t\t<secondProductTagAspectRatioString><![CDATA[0.0]]></secondProductTagAspectRatioString>\n\t\t\t<firstGuaranteeWording><![CDATA[]]></firstGuaranteeWording>\n\t\t\t<secondGuaranteeWording><![CDATA[]]></secondGuaranteeWording>\n\t\t\t<thirdGuaranteeWording><![CDATA[]]></thirdGuaranteeWording>\n\t\t\t<isPriceBeginShow>false</isPriceBeginShow>\n\t\t\t<lastGMsgID><![CDATA[]]></lastGMsgID>\n\t\t\t<promoterKey><![CDATA[]]></promoterKey>\n\t\t\t<discountWording><![CDATA[]]></discountWording>\n\t\t\t<priceSuffixDescription><![CDATA[]]></priceSuffixDescription>\n\t\t\t<showBoxItemStringList />\n\t\t</finderLiveProductShare>\n\t\t<finderOrder>\n\t\t\t<appID><![CDATA[]]></appID>\n\t\t\t<orderID><![CDATA[]]></orderID>\n\t\t\t<path><![CDATA[]]></path>\n\t\t\t<priceWording><![CDATA[]]></priceWording>\n\t\t\t<stateWording><![CDATA[]]></stateWording>\n\t\t\t<productImageURL><![CDATA[]]></productImageURL>\n\t\t\t<products><![CDATA[]]></products>\n\t\t\t<productsCount><![CDATA[0]]></productsCount>\n\t\t</finderOrder>\n\t\t<finderShopWindowShare>\n\t\t\t<finderUsername><![CDATA[]]></finderUsername>\n\t\t\t<avatar><![CDATA[]]></avatar>\n\t\t\t<nickname><![CDATA[]]></nickname>\n\t\t\t<commodityInStockCount><![CDATA[]]></commodityInStockCount>\n\t\t\t<appId><![CDATA[]]></appId>\n\t\t\t<path><![CDATA[]]></path>\n\t\t\t<appUsername><![CDATA[]]></appUsername>\n\t\t\t<query><![CDATA[]]></query>\n\t\t\t<liteAppId><![CDATA[]]></liteAppId>\n\t\t\t<liteAppPath><![CDATA[]]></liteAppPath>\n\t\t\t<liteAppQuery><![CDATA[]]></liteAppQuery>\n\t\t\t<platformTagURL><![CDATA[]]></platformTagURL>\n\t\t\t<saleWording><![CDATA[]]></saleWording>\n\t\t\t<lastGMsgID><![CDATA[]]></lastGMsgID>\n\t\t\t<profileTypeWording><![CDATA[]]></profileTypeWording>\n\t\t\t<reputationInfo>\n\t\t\t\t<hasReputationInfo>0</hasReputationInfo>\n\t\t\t\t<reputationScore>0</reputationScore>\n\t\t\t\t<reputationWording />\n\t\t\t\t<reputationTextColor />\n\t\t\t\t<reputationLevelWording />\n\t\t\t\t<reputationBackgroundColor />\n\t\t\t</reputationInfo>\n\t\t\t<productImageURLList />\n\t\t</finderShopWindowShare>\n\t\t<findernamecard>\n\t\t\t<username />\n\t\t\t<avatar><![CDATA[]]></avatar>\n\t\t\t<nickname />\n\t\t\t<auth_job />\n\t\t\t<auth_icon>0</auth_icon>\n\t\t\t<auth_icon_url />\n\t\t\t<ecSource><![CDATA[]]></ecSource>\n\t\t\t<lastGMsgID><![CDATA[]]></lastGMsgID>\n\t\t</findernamecard>\n\t\t<finderGuarantee>\n\t\t\t<scene><![CDATA[0]]></scene>\n\t\t</finderGuarantee>\n\t\t<directshare>0</directshare>\n\t\t<gamecenter>\n\t\t\t<namecard>\n\t\t\t\t<iconUrl />\n\t\t\t\t<name />\n\t\t\t\t<desc />\n\t\t\t\t<tail />\n\t\t\t\t<jumpUrl />\n\t\t\t</namecard>\n\t\t</gamecenter>\n\t\t<patMsg>\n\t\t\t<chatUser />\n\t\t\t<records>\n\t\t\t\t<recordNum>0</recordNum>\n\t\t\t</records>\n\t\t</patMsg>\n\t\t<secretmsg>\n\t\t\t<issecretmsg>0</issecretmsg>\n\t\t</secretmsg>\n\t\t<referfromscene>0</referfromscene>\n\t\t<gameshare>\n\t\t\t<liteappext>\n\t\t\t\t<liteappbizdata />\n\t\t\t\t<priority>0</priority>\n\t\t\t</liteappext>\n\t\t\t<appbrandext>\n\t\t\t\t<litegameinfo />\n\t\t\t\t<priority>-1</priority>\n\t\t\t</appbrandext>\n\t\t\t<gameshareid />\n\t\t\t<sharedata />\n\t\t\t<isvideo>0</isvideo>\n\t\t\t<duration>-1</duration>\n\t\t\t<isexposed>0</isexposed>\n\t\t\t<readtext />\n\t\t</gameshare>\n\t\t<mpsharetrace>\n\t\t\t<hasfinderelement>0</hasfinderelement>\n\t\t\t<lastgmsgid />\n\t\t</mpsharetrace>\n\t\t<wxgamecard>\n\t\t\t<framesetname />\n\t\t\t<mbcarddata />\n\t\t\t<minpkgversion />\n\t\t\t<mbcardheight>0</mbcardheight>\n\t\t\t<isoldversion>0</isoldversion>\n\t\t</wxgamecard>\n\t\t<liteapp>\n\t\t\t<id>null</id>\n\t\t\t<path />\n\t\t\t<query />\n\t\t</liteapp>\n\t</appmsg>\n\t<fromusername>wxid_lneb7n23o4lg12</fromusername>\n\t<scene>0</scene>\n\t<appinfo>\n\t\t<version>29</version>\n\t\t<appname>摇一摇搜歌</appname>\n\t</appinfo>\n\t<commenturl></commenturl>\n</msg>\n'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1753418479, 'MsgSource': '<msgsource>\n\t<sec_msg_node>\n\t\t<uuid>9489842f265e3f070fb2f5cba4870105_</uuid>\n\t\t<risk-file-flag />\n\t\t<risk-file-md5-list />\n\t</sec_msg_node>\n\t<silence>0</silence>\n\t<membercount>60</membercount>\n\t<signature>N0_V1_LbBfeG1A|v1_4PIosN84</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'PushContent': '你收到了一条消息', 'NewMsgId': 3685327032831078026, 'MsgSeq': 871398716}
2025-07-25 12:40:57 | DEBUG | 从群聊消息中提取发送者: wxid_lneb7n23o4lg12
2025-07-25 12:40:57 | DEBUG | XML消息完整内容:
<?xml version="1.0"?>
<msg>
	<appmsg appid="wx485a97c844086dc9" sdkver="0">
		<title>跑路了</title>
		<des>锦岚（开玩笑就退群版）</des>
		<username />
		<action>view</action>
		<type>3</type>
		<showtype>0</showtype>
		<content />
		<url />
		<lowurl />
		<forwardflag>0</forwardflag>
		<dataurl>https://v3-luna.douyinvod.com/fa9827e82d39587fecf868d832cca2d1/68846aab/video/tos/cn/tos-cn-ve-2774/o8DBlgCYn6AcMZsxBqFEgMDstWCZbfuYQAfsDI/</dataurl>
		<lowdataurl />
		<contentattr>0</contentattr>
		<streamvideo>
			<streamvideourl />
			<streamvideototaltime>0</streamvideototaltime>
			<streamvideotitle />
			<streamvideowording />
			<streamvideoweburl />
			<streamvideothumburl />
			<streamvideoaduxinfo />
			<streamvideopublishid />
		</streamvideo>
		<canvasPageItem>
			<canvasPageXml><![CDATA[]]></canvasPageXml>
		</canvasPageItem>
		<appattach>
			<totallen>0</totallen>
			<attachid />
			<cdnattachurl />
			<emoticonmd5 />
			<aeskey />
			<fileext />
			<islargefilemsg>0</islargefilemsg>
		</appattach>
		<extinfo />
		<androidsource>2</androidsource>
		<thumburl />
		<mediatagname />
		<messageaction><![CDATA[]]></messageaction>
		<messageext><![CDATA[]]></messageext>
		<emoticongift>
			<packageflag>0</packageflag>
			<packageid />
		</emoticongift>
		<emoticonshared>
			<packageflag>0</packageflag>
			<packageid />
		</emoticonshared>
		<designershared>
			<designeruin>0</designeruin>
			<designername>null</designername>
			<designerrediretcturl><![CDATA[null]]></designerrediretcturl>
		</designershared>
		<emotionpageshared>
			<tid>0</tid>
			<title>null</title>
			<desc>null</desc>
			<iconUrl><![CDATA[null]]></iconUrl>
			<secondUrl>null</secondUrl>
			<pageType>0</pageType>
			<setKey>null</setKey>
		</emotionpageshared>
		<webviewshared>
			<shareUrlOriginal />
			<shareUrlOpen />
			<jsAppId />
			<publisherId />
			<publisherReqId />
		</webviewshared>
		<template_id />
		<md5 />
		<websearch>
			<rec_category>0</rec_category>
			<channelId>0</channelId>
		</websearch>
		<weappinfo>
			<username />
			<appid />
			<appservicetype>0</appservicetype>
			<secflagforsinglepagemode>0</secflagforsinglepagemode>
			<videopageinfo>
				<thumbwidth>0</thumbwidth>
				<thumbheight>0</thumbheight>
				<fromopensdk>0</fromopensdk>
			</videopageinfo>
		</weappinfo>
		<statextstr>GhQKEnd4NDg1YTk3Yzg0NDA4NmRjOQ==</statextstr>
		<songalbumurl>http://wx.qlogo.cn/mmhead/ver_1/iaZCKGjtAic5icOnI1ibaY7DCzx0SVR3ic6NXwcAHpFTZIzDcvzMaeNlEYqezUL04icbD6ZylLwcGMYIzQy6kjY4AdicRyb39fASpxswdSeDrs5tomibd2sADOgpbRIF6jic2v1ibFafdgicYm69maZehIgNhg8Tw/0</songalbumurl>
		<songlyric>[00:01.63]闻 更漏咽
[00:04.31]频教前尘辞长夜
[00:08.35]久无眠 深坐对宫檐
[00:15.19]多情最是春庭雪
[00:18.83]年年落满离人苑
[00:22.87]薛涛笺 上言若如初见
[00:29.87]这一世
[00:31.99]太漫长却止步咫尺天涯间
[00:37.03]谁仍记 那梨花若雪时节
</songlyric>
		<musicShareItem>
			<musicDuration>0</musicDuration>
		</musicShareItem>
		<finderLiveProductShare>
			<finderLiveID><![CDATA[]]></finderLiveID>
			<finderUsername><![CDATA[]]></finderUsername>
			<finderObjectID><![CDATA[]]></finderObjectID>
			<finderNonceID><![CDATA[]]></finderNonceID>
			<liveStatus><![CDATA[]]></liveStatus>
			<appId><![CDATA[]]></appId>
			<pagePath><![CDATA[]]></pagePath>
			<productId><![CDATA[]]></productId>
			<coverUrl><![CDATA[]]></coverUrl>
			<productTitle><![CDATA[]]></productTitle>
			<marketPrice><![CDATA[0]]></marketPrice>
			<sellingPrice><![CDATA[0]]></sellingPrice>
			<platformHeadImg><![CDATA[]]></platformHeadImg>
			<platformName><![CDATA[]]></platformName>
			<shopWindowId><![CDATA[]]></shopWindowId>
			<flashSalePrice><![CDATA[0]]></flashSalePrice>
			<flashSaleEndTime><![CDATA[0]]></flashSaleEndTime>
			<ecSource><![CDATA[]]></ecSource>
			<sellingPriceWording><![CDATA[]]></sellingPriceWording>
			<platformIconURL><![CDATA[]]></platformIconURL>
			<firstProductTagURL><![CDATA[]]></firstProductTagURL>
			<firstProductTagAspectRatioString><![CDATA[0.0]]></firstProductTagAspectRatioString>
			<secondProductTagURL><![CDATA[]]></secondProductTagURL>
			<secondProductTagAspectRatioString><![CDATA[0.0]]></secondProductTagAspectRatioString>
			<firstGuaranteeWording><![CDATA[]]></firstGuaranteeWording>
			<secondGuaranteeWording><![CDATA[]]></secondGuaranteeWording>
			<thirdGuaranteeWording><![CDATA[]]></thirdGuaranteeWording>
			<isPriceBeginShow>false</isPriceBeginShow>
			<lastGMsgID><![CDATA[]]></lastGMsgID>
			<promoterKey><![CDATA[]]></promoterKey>
			<discountWording><![CDATA[]]></discountWording>
			<priceSuffixDescription><![CDATA[]]></priceSuffixDescription>
			<showBoxItemStringList />
		</finderLiveProductShare>
		<finderOrder>
			<appID><![CDATA[]]></appID>
			<orderID><![CDATA[]]></orderID>
			<path><![CDATA[]]></path>
			<priceWording><![CDATA[]]></priceWording>
			<stateWording><![CDATA[]]></stateWording>
			<productImageURL><![CDATA[]]></productImageURL>
			<products><![CDATA[]]></products>
			<productsCount><![CDATA[0]]></productsCount>
		</finderOrder>
		<finderShopWindowShare>
			<finderUsername><![CDATA[]]></finderUsername>
			<avatar><![CDATA[]]></avatar>
			<nickname><![CDATA[]]></nickname>
			<commodityInStockCount><![CDATA[]]></commodityInStockCount>
			<appId><![CDATA[]]></appId>
			<path><![CDATA[]]></path>
			<appUsername><![CDATA[]]></appUsername>
			<query><![CDATA[]]></query>
			<liteAppId><![CDATA[]]></liteAppId>
			<liteAppPath><![CDATA[]]></liteAppPath>
			<liteAppQuery><![CDATA[]]></liteAppQuery>
			<platformTagURL><![CDATA[]]></platformTagURL>
			<saleWording><![CDATA[]]></saleWording>
			<lastGMsgID><![CDATA[]]></lastGMsgID>
			<profileTypeWording><![CDATA[]]></profileTypeWording>
			<reputationInfo>
				<hasReputationInfo>0</hasReputationInfo>
				<reputationScore>0</reputationScore>
				<reputationWording />
				<reputationTextColor />
				<reputationLevelWording />
				<reputationBackgroundColor />
			</reputationInfo>
			<productImageURLList />
		</finderShopWindowShare>
		<findernamecard>
			<username />
			<avatar><![CDATA[]]></avatar>
			<nickname />
			<auth_job />
			<auth_icon>0</auth_icon>
			<auth_icon_url />
			<ecSource><![CDATA[]]></ecSource>
			<lastGMsgID><![CDATA[]]></lastGMsgID>
		</findernamecard>
		<finderGuarantee>
			<scene><![CDATA[0]]></scene>
		</finderGuarantee>
		<directshare>0</directshare>
		<gamecenter>
			<namecard>
				<iconUrl />
				<name />
				<desc />
				<tail />
				<jumpUrl />
			</namecard>
		</gamecenter>
		<patMsg>
			<chatUser />
			<records>
				<recordNum>0</recordNum>
			</records>
		</patMsg>
		<secretmsg>
			<issecretmsg>0</issecretmsg>
		</secretmsg>
		<referfromscene>0</referfromscene>
		<gameshare>
			<liteappext>
				<liteappbizdata />
				<priority>0</priority>
			</liteappext>
			<appbrandext>
				<litegameinfo />
				<priority>-1</priority>
			</appbrandext>
			<gameshareid />
			<sharedata />
			<isvideo>0</isvideo>
			<duration>-1</duration>
			<isexposed>0</isexposed>
			<readtext />
		</gameshare>
		<mpsharetrace>
			<hasfinderelement>0</hasfinderelement>
			<lastgmsgid />
		</mpsharetrace>
		<wxgamecard>
			<framesetname />
			<mbcarddata />
			<minpkgversion />
			<mbcardheight>0</mbcardheight>
			<isoldversion>0</isoldversion>
		</wxgamecard>
		<liteapp>
			<id>null</id>
			<path />
			<query />
		</liteapp>
	</appmsg>
	<fromusername>wxid_lneb7n23o4lg12</fromusername>
	<scene>0</scene>
	<appinfo>
		<version>29</version>
		<appname>摇一摇搜歌</appname>
	</appinfo>
	<commenturl></commenturl>
</msg>

2025-07-25 12:40:57 | DEBUG | XML消息类型: 3
2025-07-25 12:40:57 | DEBUG | XML消息标题: 跑路了
2025-07-25 12:40:57 | DEBUG | XML消息描述: 锦岚（开玩笑就退群版）
2025-07-25 12:40:57 | DEBUG | 附件信息 totallen: 0
2025-07-25 12:40:57 | DEBUG | 附件信息 islargefilemsg: 0
2025-07-25 12:40:57 | INFO | 收到红包消息: 标题:跑路了 描述:锦岚（开玩笑就退群版） 来自:48097389945@chatroom 发送人:wxid_4usgcju5ey9q29
2025-07-25 12:41:08 | DEBUG | 收到消息: {'MsgId': 1180584276, 'FromUserName': {'string': '27852221909@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 1, 'Content': {'string': 'wxid_idzryo4rneok22:\n打'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1753418490, 'MsgSource': '<msgsource>\n\t<pua>1</pua>\n\t<eggIncluded>1</eggIncluded>\n\t<silence>1</silence>\n\t<membercount>146</membercount>\n\t<signature>N0_V1_hMUsUVDu|v1_Mp5obt/T</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'NewMsgId': 4208400733704339846, 'MsgSeq': 871398717}
2025-07-25 12:41:08 | INFO | 收到文本消息: 消息ID:1180584276 来自:27852221909@chatroom 发送人:wxid_idzryo4rneok22 @:[] 内容:打
2025-07-25 12:41:08 | INFO | 发送表情消息: 对方wxid:27852221909@chatroom md5:fe3cd0728ea249c42967eca1a923ca19 总长度:9992069
2025-07-25 12:41:08 | DEBUG | 处理消息内容: '打'
2025-07-25 12:41:08 | DEBUG | 消息内容 '打' 不匹配任何命令，忽略
2025-07-25 12:41:15 | DEBUG | 收到消息: {'MsgId': 49139350, 'FromUserName': {'string': '48097389945@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 1, 'Content': {'string': 'wxid_l9koi6kli78i22:\n小阿姨跑了'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1753418497, 'MsgSource': '<msgsource>\n\t<pua>1</pua>\n\t<silence>0</silence>\n\t<membercount>60</membercount>\n\t<signature>N0_V1_PcQMRBUK|v1_tdn8aSyy</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'PushContent': '十五 : 小阿姨跑了', 'NewMsgId': 6129600005781960381, 'MsgSeq': 871398720}
2025-07-25 12:41:15 | INFO | 收到文本消息: 消息ID:49139350 来自:48097389945@chatroom 发送人:wxid_l9koi6kli78i22 @:[] 内容:小阿姨跑了
2025-07-25 12:41:15 | DEBUG | 处理消息内容: '小阿姨跑了'
2025-07-25 12:41:15 | DEBUG | 消息内容 '小阿姨跑了' 不匹配任何命令，忽略
2025-07-25 12:41:24 | DEBUG | 收到消息: {'MsgId': 203653885, 'FromUserName': {'string': '48097389945@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 47, 'Content': {'string': 'xiaomaochong:\n<msg><emoji fromusername="xiaomaochong" tousername="48097389945@chatroom" type="2" idbuffer="media:0_0" md5="02dd3138120243a6021c80a2fb8fda90" len="214989" productid="" androidmd5="02dd3138120243a6021c80a2fb8fda90" androidlen="214989" s60v3md5="02dd3138120243a6021c80a2fb8fda90" s60v3len="214989" s60v5md5="02dd3138120243a6021c80a2fb8fda90" s60v5len="214989" cdnurl="http://vweixinf.tc.qq.com/110/20401/stodownload?m=02dd3138120243a6021c80a2fb8fda90&amp;filekey=30440201010430302e02016e040253480420303264643331333831323032343361363032316338306132666238666461393002030347cd040d00000004627466730000000132&amp;hy=SH&amp;storeid=262b0b0ab000d708d5fae26b80000006e01004fb153480586cb40b6bca7476&amp;ef=1&amp;bizid=1022" designerid="" thumburl="" encrypturl="http://vweixinf.tc.qq.com/110/20402/stodownload?m=10d742c6202e6facb60f805e470619f5&amp;filekey=30440201010430302e02016e040253480420313064373432633632303265366661636236306638303565343730363139663502030347d0040d00000004627466730000000132&amp;hy=SH&amp;storeid=262b0b0ab000e2e205fae26b80000006e02004fb253480586cb40b6bca7480&amp;ef=2&amp;bizid=1022" aeskey="45b8a38a1ea842da9133e151a00c622f" externurl="" externmd5="" width="625" height="306" tpurl="" tpauthkey="" attachedtext="" attachedtextcolor="" lensid="" emojiattr="" linkid="" desc=""></emoji></msg>'}, 'Status': 3, 'ImgStatus': 2, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1753418507, 'MsgSource': '<msgsource>\n\t<silence>0</silence>\n\t<membercount>60</membercount>\n\t<signature>N0_V1_ITfFkd4A|v1_fiCLX7cw</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'PushContent': '小爱在群聊中发了一个表情', 'NewMsgId': 5533633873342106744, 'MsgSeq': 871398721}
2025-07-25 12:41:24 | INFO | 收到表情消息: 消息ID:203653885 来自:48097389945@chatroom 发送人:xiaomaochong MD5:02dd3138120243a6021c80a2fb8fda90 大小:214989
2025-07-25 12:41:25 | DEBUG | [DoubaoImageRecognition] 缓存表情消息: 5533633873342106744
2025-07-25 12:41:28 | DEBUG | 收到消息: {'MsgId': 489195926, 'FromUserName': {'string': '48097389945@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 1, 'Content': {'string': 'wxid_l9koi6kli78i22:\n发生了'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1753418510, 'MsgSource': '<msgsource>\n\t<pua>1</pua>\n\t<silence>0</silence>\n\t<membercount>60</membercount>\n\t<signature>N0_V1_qUHCCz4+|v1_WeW/sg3D</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'PushContent': '十五 : 发生了', 'NewMsgId': 6674091711837705409, 'MsgSeq': 871398722}
2025-07-25 12:41:28 | INFO | 收到文本消息: 消息ID:489195926 来自:48097389945@chatroom 发送人:wxid_l9koi6kli78i22 @:[] 内容:发生了
2025-07-25 12:41:28 | DEBUG | 处理消息内容: '发生了'
2025-07-25 12:41:28 | DEBUG | 消息内容 '发生了' 不匹配任何命令，忽略
2025-07-25 12:41:30 | DEBUG | 收到消息: {'MsgId': 1720593994, 'FromUserName': {'string': '48097389945@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 1, 'Content': {'string': 'wxid_l9koi6kli78i22:\n什么事'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1753418512, 'MsgSource': '<msgsource>\n\t<pua>1</pua>\n\t<silence>0</silence>\n\t<membercount>60</membercount>\n\t<signature>N0_V1_w8IhcCW7|v1_r8ih7DKf</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'PushContent': '十五 : 什么事', 'NewMsgId': 7448666490271650471, 'MsgSeq': 871398723}
2025-07-25 12:41:30 | INFO | 收到文本消息: 消息ID:1720593994 来自:48097389945@chatroom 发送人:wxid_l9koi6kli78i22 @:[] 内容:什么事
2025-07-25 12:41:30 | DEBUG | 处理消息内容: '什么事'
2025-07-25 12:41:30 | DEBUG | 消息内容 '什么事' 不匹配任何命令，忽略
2025-07-25 12:41:46 | DEBUG | 收到消息: {'MsgId': 130779268, 'FromUserName': {'string': '48097389945@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 1, 'Content': {'string': 'wxid_cwxauba42ki122:\n咋了'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1753418528, 'MsgSource': '<msgsource>\n\t<pua>1</pua>\n\t<silence>0</silence>\n\t<membercount>60</membercount>\n\t<signature>N0_V1_LTz6wtCR|v1_lKA3Uj6b</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'PushContent': 'ن初心ᯤ⁶ᴳ คิดถึงن : 咋了', 'NewMsgId': 5480001286419521950, 'MsgSeq': 871398724}
2025-07-25 12:41:46 | INFO | 收到文本消息: 消息ID:130779268 来自:48097389945@chatroom 发送人:wxid_cwxauba42ki122 @:[] 内容:咋了
2025-07-25 12:41:46 | DEBUG | 处理消息内容: '咋了'
2025-07-25 12:41:46 | DEBUG | 消息内容 '咋了' 不匹配任何命令，忽略
2025-07-25 12:41:48 | DEBUG | 收到消息: {'MsgId': 925946643, 'FromUserName': {'string': '48097389945@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 1, 'Content': {'string': 'wxid_ubbh6q832tcs21:\n亮不亮'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1753418531, 'MsgSource': '<msgsource>\n\t<pua>1</pua>\n\t<eggIncluded>1</eggIncluded>\n\t<silence>0</silence>\n\t<membercount>60</membercount>\n\t<signature>N0_V1_56zqnKLT|v1_9FCob7L/</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'PushContent': '郭 : 亮不亮', 'NewMsgId': 1876581827374621443, 'MsgSeq': 871398725}
2025-07-25 12:41:48 | INFO | 收到文本消息: 消息ID:925946643 来自:48097389945@chatroom 发送人:wxid_ubbh6q832tcs21 @:[] 内容:亮不亮
2025-07-25 12:41:49 | INFO | 发送表情消息: 对方wxid:48097389945@chatroom md5:3dbafb066d773b2c3f41dd01649f0187 总长度:9992069
2025-07-25 12:41:49 | DEBUG | 处理消息内容: '亮不亮'
2025-07-25 12:41:49 | DEBUG | 消息内容 '亮不亮' 不匹配任何命令，忽略
2025-07-25 12:41:59 | DEBUG | 收到消息: {'MsgId': 2062860390, 'FromUserName': {'string': '47325400669@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 1, 'Content': {'string': 'wxid_62fiham2pn7521:\n管你逆向还是官方api输入内容都是一样明文记录在服务器后台[奸笑]'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1753418541, 'MsgSource': '<msgsource>\n\t<pua>1</pua>\n\t<alnode>\n\t\t<cf>5</cf>\n\t\t<inlenlist>714,86,86</inlenlist>\n\t</alnode>\n\t<eggIncluded>1</eggIncluded>\n\t<silence>0</silence>\n\t<membercount>221</membercount>\n\t<signature>N0_V1_2vTqYBsJ|v1_XY01uJbf</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'PushContent': '八戒。 : 管你逆向还是官方api输入内容都是一样明文记录在服务器后台[奸笑]', 'NewMsgId': 3325241705065918711, 'MsgSeq': 871398728}
2025-07-25 12:41:59 | INFO | 收到文本消息: 消息ID:2062860390 来自:47325400669@chatroom 发送人:wxid_62fiham2pn7521 @:[] 内容:管你逆向还是官方api输入内容都是一样明文记录在服务器后台[奸笑]
2025-07-25 12:41:59 | DEBUG | 处理消息内容: '管你逆向还是官方api输入内容都是一样明文记录在服务器后台[奸笑]'
2025-07-25 12:41:59 | DEBUG | 消息内容 '管你逆向还是官方api输入内容都是一样明文记录在服务器后台[奸笑]' 不匹配任何命令，忽略
2025-07-25 12:42:14 | DEBUG | 收到消息: {'MsgId': 784406229, 'FromUserName': {'string': '47325400669@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 1, 'Content': {'string': 'wxid_62fiham2pn7521:\n不然怎么审查有没有违规，印度特色'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1753418556, 'MsgSource': '<msgsource>\n\t<pua>1</pua>\n\t<eggIncluded>1</eggIncluded>\n\t<silence>0</silence>\n\t<membercount>221</membercount>\n\t<signature>N0_V1_OfcITZYB|v1_QnCRomzr</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'PushContent': '八戒。 : 不然怎么审查有没有违规，印度特色', 'NewMsgId': 7101230029571978032, 'MsgSeq': 871398729}
2025-07-25 12:42:14 | INFO | 收到文本消息: 消息ID:784406229 来自:47325400669@chatroom 发送人:wxid_62fiham2pn7521 @:[] 内容:不然怎么审查有没有违规，印度特色
2025-07-25 12:42:14 | DEBUG | 处理消息内容: '不然怎么审查有没有违规，印度特色'
2025-07-25 12:42:14 | DEBUG | 消息内容 '不然怎么审查有没有违规，印度特色' 不匹配任何命令，忽略
2025-07-25 12:43:05 | DEBUG | 收到消息: {'MsgId': 670148174, 'FromUserName': {'string': '47325400669@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 1, 'Content': {'string': 'wxid_ubbh6q832tcs21:\n现在哪特么还有隐私'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1753418607, 'MsgSource': '<msgsource>\n\t<pua>1</pua>\n\t<eggIncluded>1</eggIncluded>\n\t<silence>0</silence>\n\t<membercount>221</membercount>\n\t<signature>N0_V1_qaqrUoot|v1_GeQdhsnW</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'PushContent': '郭 : 现在哪特么还有隐私', 'NewMsgId': 8150862103855664451, 'MsgSeq': 871398730}
2025-07-25 12:43:05 | INFO | 收到文本消息: 消息ID:670148174 来自:47325400669@chatroom 发送人:wxid_ubbh6q832tcs21 @:[] 内容:现在哪特么还有隐私
2025-07-25 12:43:05 | DEBUG | 处理消息内容: '现在哪特么还有隐私'
2025-07-25 12:43:05 | DEBUG | 消息内容 '现在哪特么还有隐私' 不匹配任何命令，忽略
2025-07-25 12:43:23 | DEBUG | 收到消息: {'MsgId': 1123870452, 'FromUserName': {'string': '48097389945@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 1, 'Content': {'string': 'wxid_cwxauba42ki122:\n咦，小阿姨为什么退群了'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1753418625, 'MsgSource': '<msgsource>\n\t<pua>1</pua>\n\t<silence>0</silence>\n\t<membercount>60</membercount>\n\t<signature>N0_V1_UjCIjVaj|v1_ulfMHy57</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'PushContent': 'ن初心ᯤ⁶ᴳ คิดถึงن : 咦，小阿姨为什么退群了', 'NewMsgId': 4829803596017324470, 'MsgSeq': 871398731}
2025-07-25 12:43:23 | INFO | 收到文本消息: 消息ID:1123870452 来自:48097389945@chatroom 发送人:wxid_cwxauba42ki122 @:[] 内容:咦，小阿姨为什么退群了
2025-07-25 12:43:23 | DEBUG | 处理消息内容: '咦，小阿姨为什么退群了'
2025-07-25 12:43:23 | DEBUG | 消息内容 '咦，小阿姨为什么退群了' 不匹配任何命令，忽略
2025-07-25 12:43:26 | DEBUG | 收到消息: {'MsgId': 42465594, 'FromUserName': {'string': '27852221909@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 1, 'Content': {'string': 'wxid_ubbh6q832tcs21:\n跑'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1753418629, 'MsgSource': '<msgsource>\n\t<pua>1</pua>\n\t<eggIncluded>1</eggIncluded>\n\t<silence>1</silence>\n\t<membercount>146</membercount>\n\t<signature>N0_V1_EI+GT2xA|v1_b5VFEnlw</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'NewMsgId': 8627499120063043533, 'MsgSeq': 871398732}
2025-07-25 12:43:26 | INFO | 收到文本消息: 消息ID:42465594 来自:27852221909@chatroom 发送人:wxid_ubbh6q832tcs21 @:[] 内容:跑
2025-07-25 12:43:26 | INFO | 发送表情消息: 对方wxid:27852221909@chatroom md5:c6f7107246af62c52f26daddedd56340 总长度:9992069
2025-07-25 12:43:26 | DEBUG | 处理消息内容: '跑'
2025-07-25 12:43:26 | DEBUG | 消息内容 '跑' 不匹配任何命令，忽略
2025-07-25 12:43:50 | DEBUG | 收到消息: {'MsgId': 2075198416, 'FromUserName': {'string': '48097389945@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 1, 'Content': {'string': 'wxid_l9koi6kli78i22:\n天天还有瓜吃就行了'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1753418653, 'MsgSource': '<msgsource>\n\t<pua>1</pua>\n\t<silence>0</silence>\n\t<membercount>60</membercount>\n\t<signature>N0_V1_R6OQIgnr|v1_ImAQtfv6</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'PushContent': '十五 : 天天还有瓜吃就行了', 'NewMsgId': 7114220481704823573, 'MsgSeq': 871398735}
2025-07-25 12:43:50 | INFO | 收到文本消息: 消息ID:2075198416 来自:48097389945@chatroom 发送人:wxid_l9koi6kli78i22 @:[] 内容:天天还有瓜吃就行了
2025-07-25 12:43:51 | DEBUG | 处理消息内容: '天天还有瓜吃就行了'
2025-07-25 12:43:51 | DEBUG | 消息内容 '天天还有瓜吃就行了' 不匹配任何命令，忽略
2025-07-25 12:44:29 | DEBUG | 收到消息: {'MsgId': 2030090193, 'FromUserName': {'string': '48097389945@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 1, 'Content': {'string': 'wxid_cwxauba42ki122:\n[擦汗]'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1753418692, 'MsgSource': '<msgsource>\n\t<pua>1</pua>\n\t<silence>0</silence>\n\t<membercount>60</membercount>\n\t<signature>N0_V1_RrraAtTA|v1_ro5rT94V</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'PushContent': 'ن初心ᯤ⁶ᴳ คิดถึงن : [擦汗]', 'NewMsgId': 3878556837779166170, 'MsgSeq': 871398736}
2025-07-25 12:44:29 | INFO | 收到表情消息: 消息ID:2030090193 来自:48097389945@chatroom 发送人:wxid_cwxauba42ki122 @:[] 内容:[擦汗]
2025-07-25 12:44:30 | DEBUG | [DoubaoImageRecognition] 缓存表情消息: 3878556837779166170
2025-07-25 12:44:59 | DEBUG | 收到消息: {'MsgId': 394493900, 'FromUserName': {'string': '48097389945@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 49, 'Content': {'string': 'wxid_l9koi6kli78i22:\n<?xml version="1.0"?>\n<msg>\n\t<appmsg appid="" sdkver="0">\n\t\t<title>可能因为这个退群了</title>\n\t\t<des />\n\t\t<username />\n\t\t<action>view</action>\n\t\t<type>57</type>\n\t\t<showtype>0</showtype>\n\t\t<content />\n\t\t<url />\n\t\t<lowurl />\n\t\t<forwardflag>0</forwardflag>\n\t\t<dataurl />\n\t\t<lowdataurl />\n\t\t<contentattr>0</contentattr>\n\t\t<streamvideo>\n\t\t\t<streamvideourl />\n\t\t\t<streamvideototaltime>0</streamvideototaltime>\n\t\t\t<streamvideotitle />\n\t\t\t<streamvideowording />\n\t\t\t<streamvideoweburl />\n\t\t\t<streamvideothumburl />\n\t\t\t<streamvideoaduxinfo />\n\t\t\t<streamvideopublishid />\n\t\t</streamvideo>\n\t\t<canvasPageItem>\n\t\t\t<canvasPageXml><![CDATA[]]></canvasPageXml>\n\t\t</canvasPageItem>\n\t\t<refermsg>\n\t\t\t<type>1</type>\n\t\t\t<svrid>6276174878581786479</svrid>\n\t\t\t<fromusr>48097389945@chatroom</fromusr>\n\t\t\t<chatusr>wxid_wlnzvr8ivgd422</chatusr>\n\t\t\t<displayname>无妄</displayname>\n\t\t\t<msgsource>&lt;msgsource&gt;\n\t&lt;pua&gt;1&lt;/pua&gt;\n\t&lt;eggIncluded&gt;1&lt;/eggIncluded&gt;\n\t&lt;silence&gt;1&lt;/silence&gt;\n\t&lt;membercount&gt;61&lt;/membercount&gt;\n\t&lt;signature&gt;N0_V1_woI872tF|v1_DCqJQzrJ&lt;/signature&gt;\n\t&lt;tmp_node&gt;\n\t\t&lt;publisher-id&gt;&lt;/publisher-id&gt;\n\t&lt;/tmp_node&gt;\n&lt;/msgsource&gt;\n</msgsource>\n\t\t\t<content>\n开放群聊加好友了，需要的赶紧加吧，明天关掉</content>\n\t\t\t<strid />\n\t\t\t<createtime>1753418457</createtime>\n\t\t</refermsg>\n\t\t<appattach>\n\t\t\t<totallen>0</totallen>\n\t\t\t<attachid />\n\t\t\t<cdnattachurl />\n\t\t\t<emoticonmd5></emoticonmd5>\n\t\t\t<aeskey></aeskey>\n\t\t\t<fileext />\n\t\t\t<islargefilemsg>0</islargefilemsg>\n\t\t</appattach>\n\t\t<extinfo />\n\t\t<androidsource>0</androidsource>\n\t\t<thumburl />\n\t\t<mediatagname />\n\t\t<messageaction><![CDATA[]]></messageaction>\n\t\t<messageext><![CDATA[]]></messageext>\n\t\t<emoticongift>\n\t\t\t<packageflag>0</packageflag>\n\t\t\t<packageid />\n\t\t</emoticongift>\n\t\t<emoticonshared>\n\t\t\t<packageflag>0</packageflag>\n\t\t\t<packageid />\n\t\t</emoticonshared>\n\t\t<designershared>\n\t\t\t<designeruin>0</designeruin>\n\t\t\t<designername>null</designername>\n\t\t\t<designerrediretcturl><![CDATA[null]]></designerrediretcturl>\n\t\t</designershared>\n\t\t<emotionpageshared>\n\t\t\t<tid>0</tid>\n\t\t\t<title>null</title>\n\t\t\t<desc>null</desc>\n\t\t\t<iconUrl><![CDATA[null]]></iconUrl>\n\t\t\t<secondUrl>null</secondUrl>\n\t\t\t<pageType>0</pageType>\n\t\t\t<setKey>null</setKey>\n\t\t</emotionpageshared>\n\t\t<webviewshared>\n\t\t\t<shareUrlOriginal />\n\t\t\t<shareUrlOpen />\n\t\t\t<jsAppId />\n\t\t\t<publisherId />\n\t\t\t<publisherReqId />\n\t\t</webviewshared>\n\t\t<template_id />\n\t\t<md5 />\n\t\t<finderGuarantee>\n\t\t\t<scene><![CDATA[0]]></scene>\n\t\t</finderGuarantee>\n\t\t<websearch>\n\t\t\t<rec_category>0</rec_category>\n\t\t\t<channelId>0</channelId>\n\t\t</websearch>\n\t\t<weappinfo>\n\t\t\t<username />\n\t\t\t<appid />\n\t\t\t<appservicetype>0</appservicetype>\n\t\t\t<secflagforsinglepagemode>0</secflagforsinglepagemode>\n\t\t\t<videopageinfo>\n\t\t\t\t<thumbwidth>0</thumbwidth>\n\t\t\t\t<thumbheight>0</thumbheight>\n\t\t\t\t<fromopensdk>0</fromopensdk>\n\t\t\t</videopageinfo>\n\t\t</weappinfo>\n\t\t<statextstr />\n\t\t<musicShareItem>\n\t\t\t<musicDuration>0</musicDuration>\n\t\t</musicShareItem>\n\t\t<finderLiveProductShare>\n\t\t\t<finderLiveID><![CDATA[]]></finderLiveID>\n\t\t\t<finderUsername><![CDATA[]]></finderUsername>\n\t\t\t<finderObjectID><![CDATA[]]></finderObjectID>\n\t\t\t<finderNonceID><![CDATA[]]></finderNonceID>\n\t\t\t<liveStatus><![CDATA[]]></liveStatus>\n\t\t\t<appId><![CDATA[]]></appId>\n\t\t\t<pagePath><![CDATA[]]></pagePath>\n\t\t\t<productId><![CDATA[]]></productId>\n\t\t\t<coverUrl><![CDATA[]]></coverUrl>\n\t\t\t<productTitle><![CDATA[]]></productTitle>\n\t\t\t<marketPrice><![CDATA[0]]></marketPrice>\n\t\t\t<sellingPrice><![CDATA[0]]></sellingPrice>\n\t\t\t<platformHeadImg><![CDATA[]]></platformHeadImg>\n\t\t\t<platformName><![CDATA[]]></platformName>\n\t\t\t<shopWindowId><![CDATA[]]></shopWindowId>\n\t\t\t<flashSalePrice><![CDATA[0]]></flashSalePrice>\n\t\t\t<flashSaleEndTime><![CDATA[0]]></flashSaleEndTime>\n\t\t\t<ecSource><![CDATA[]]></ecSource>\n\t\t\t<sellingPriceWording><![CDATA[]]></sellingPriceWording>\n\t\t\t<platformIconURL><![CDATA[]]></platformIconURL>\n\t\t\t<firstProductTagURL><![CDATA[]]></firstProductTagURL>\n\t\t\t<firstProductTagAspectRatioString><![CDATA[0.0]]></firstProductTagAspectRatioString>\n\t\t\t<secondProductTagURL><![CDATA[]]></secondProductTagURL>\n\t\t\t<secondProductTagAspectRatioString><![CDATA[0.0]]></secondProductTagAspectRatioString>\n\t\t\t<firstGuaranteeWording><![CDATA[]]></firstGuaranteeWording>\n\t\t\t<secondGuaranteeWording><![CDATA[]]></secondGuaranteeWording>\n\t\t\t<thirdGuaranteeWording><![CDATA[]]></thirdGuaranteeWording>\n\t\t\t<isPriceBeginShow>false</isPriceBeginShow>\n\t\t\t<lastGMsgID><![CDATA[]]></lastGMsgID>\n\t\t\t<promoterKey><![CDATA[]]></promoterKey>\n\t\t\t<discountWording><![CDATA[]]></discountWording>\n\t\t\t<priceSuffixDescription><![CDATA[]]></priceSuffixDescription>\n\t\t\t<showBoxItemStringList />\n\t\t</finderLiveProductShare>\n\t\t<finderOrder>\n\t\t\t<appID><![CDATA[]]></appID>\n\t\t\t<orderID><![CDATA[]]></orderID>\n\t\t\t<path><![CDATA[]]></path>\n\t\t\t<priceWording><![CDATA[]]></priceWording>\n\t\t\t<stateWording><![CDATA[]]></stateWording>\n\t\t\t<productImageURL><![CDATA[]]></productImageURL>\n\t\t\t<products><![CDATA[]]></products>\n\t\t\t<productsCount><![CDATA[0]]></productsCount>\n\t\t</finderOrder>\n\t\t<finderShopWindowShare>\n\t\t\t<finderUsername><![CDATA[]]></finderUsername>\n\t\t\t<avatar><![CDATA[]]></avatar>\n\t\t\t<nickname><![CDATA[]]></nickname>\n\t\t\t<commodityInStockCount><![CDATA[]]></commodityInStockCount>\n\t\t\t<appId><![CDATA[]]></appId>\n\t\t\t<path><![CDATA[]]></path>\n\t\t\t<appUsername><![CDATA[]]></appUsername>\n\t\t\t<query><![CDATA[]]></query>\n\t\t\t<liteAppId><![CDATA[]]></liteAppId>\n\t\t\t<liteAppPath><![CDATA[]]></liteAppPath>\n\t\t\t<liteAppQuery><![CDATA[]]></liteAppQuery>\n\t\t\t<platformTagURL><![CDATA[]]></platformTagURL>\n\t\t\t<saleWording><![CDATA[]]></saleWording>\n\t\t\t<lastGMsgID><![CDATA[]]></lastGMsgID>\n\t\t\t<profileTypeWording><![CDATA[]]></profileTypeWording>\n\t\t\t<reputationInfo>\n\t\t\t\t<hasReputationInfo>0</hasReputationInfo>\n\t\t\t\t<reputationScore>0</reputationScore>\n\t\t\t\t<reputationWording />\n\t\t\t\t<reputationTextColor />\n\t\t\t\t<reputationLevelWording />\n\t\t\t\t<reputationBackgroundColor />\n\t\t\t</reputationInfo>\n\t\t\t<productImageURLList />\n\t\t</finderShopWindowShare>\n\t\t<findernamecard>\n\t\t\t<username />\n\t\t\t<avatar><![CDATA[]]></avatar>\n\t\t\t<nickname />\n\t\t\t<auth_job />\n\t\t\t<auth_icon>0</auth_icon>\n\t\t\t<auth_icon_url />\n\t\t\t<ecSource><![CDATA[]]></ecSource>\n\t\t\t<lastGMsgID><![CDATA[]]></lastGMsgID>\n\t\t</findernamecard>\n\t\t<finderGuarantee>\n\t\t\t<scene><![CDATA[0]]></scene>\n\t\t</finderGuarantee>\n\t\t<directshare>0</directshare>\n\t\t<gamecenter>\n\t\t\t<namecard>\n\t\t\t\t<iconUrl />\n\t\t\t\t<name />\n\t\t\t\t<desc />\n\t\t\t\t<tail />\n\t\t\t\t<jumpUrl />\n\t\t\t</namecard>\n\t\t</gamecenter>\n\t\t<patMsg>\n\t\t\t<chatUser />\n\t\t\t<records>\n\t\t\t\t<recordNum>0</recordNum>\n\t\t\t</records>\n\t\t</patMsg>\n\t\t<secretmsg>\n\t\t\t<issecretmsg>0</issecretmsg>\n\t\t</secretmsg>\n\t\t<referfromscene>0</referfromscene>\n\t\t<gameshare>\n\t\t\t<liteappext>\n\t\t\t\t<liteappbizdata />\n\t\t\t\t<priority>0</priority>\n\t\t\t</liteappext>\n\t\t\t<appbrandext>\n\t\t\t\t<litegameinfo />\n\t\t\t\t<priority>-1</priority>\n\t\t\t</appbrandext>\n\t\t\t<gameshareid />\n\t\t\t<sharedata />\n\t\t\t<isvideo>0</isvideo>\n\t\t\t<duration>-1</duration>\n\t\t\t<isexposed>0</isexposed>\n\t\t\t<readtext />\n\t\t</gameshare>\n\t\t<mpsharetrace>\n\t\t\t<hasfinderelement>0</hasfinderelement>\n\t\t\t<lastgmsgid />\n\t\t</mpsharetrace>\n\t\t<wxgamecard>\n\t\t\t<framesetname />\n\t\t\t<mbcarddata />\n\t\t\t<minpkgversion />\n\t\t\t<mbcardheight>0</mbcardheight>\n\t\t\t<isoldversion>0</isoldversion>\n\t\t</wxgamecard>\n\t\t<liteapp>\n\t\t\t<id>null</id>\n\t\t\t<path />\n\t\t\t<query />\n\t\t</liteapp>\n\t</appmsg>\n\t<fromusername>wxid_l9koi6kli78i22</fromusername>\n\t<scene>0</scene>\n\t<appinfo>\n\t\t<version>1</version>\n\t\t<appname />\n\t</appinfo>\n\t<commenturl />\n</msg>\n'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1753418721, 'MsgSource': '<msgsource>\n\t<alnode>\n\t\t<fr>4</fr>\n\t</alnode>\n\t<sec_msg_node>\n\t\t<uuid>de5d725fbd8068799f296616936e5faa_</uuid>\n\t\t<risk-file-flag />\n\t\t<risk-file-md5-list />\n\t</sec_msg_node>\n\t<silence>0</silence>\n\t<membercount>60</membercount>\n\t<signature>N0_V1_ABI/oJBA|v1_9HfKSW+v</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'PushContent': '十五 : 可能因为这个退群了', 'NewMsgId': 8712637177425022201, 'MsgSeq': 871398737}
2025-07-25 12:44:59 | DEBUG | 从群聊消息中提取发送者: wxid_l9koi6kli78i22
2025-07-25 12:44:59 | DEBUG | 使用已解析的XML处理引用消息
2025-07-25 12:44:59 | INFO | 收到引用消息: 消息ID:394493900 来自:48097389945@chatroom 发送人:wxid_l9koi6kli78i22 内容:可能因为这个退群了 引用类型:1
2025-07-25 12:44:59 | INFO | [DouBaoImageToImage] 收到引用消息: 可能因为这个退群了
2025-07-25 12:44:59 | INFO | [TimerTask] 收到引用消息调试信息:
2025-07-25 12:44:59 | INFO |   - 消息内容: 可能因为这个退群了
2025-07-25 12:44:59 | INFO |   - 群组ID: 48097389945@chatroom
2025-07-25 12:44:59 | INFO |   - 发送人: wxid_l9koi6kli78i22
2025-07-25 12:44:59 | INFO |   - 引用信息: {'MsgType': 1, 'Content': '\n开放群聊加好友了，需要的赶紧加吧，明天关掉', 'Msgid': '6276174878581786479', 'NewMsgId': '6276174878581786479', 'ToWxid': 'wxid_4usgcju5ey9q29', 'FromWxid': '48097389945@chatroom', 'Nickname': '无妄', 'MsgSource': '<msgsource>\n\t<pua>1</pua>\n\t<eggIncluded>1</eggIncluded>\n\t<silence>1</silence>\n\t<membercount>61</membercount>\n\t<signature>N0_V1_woI872tF|v1_DCqJQzrJ</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'Createtime': '1753418457', 'SenderWxid': 'wxid_l9koi6kli78i22'}
2025-07-25 12:44:59 | INFO |   - 引用消息ID: 
2025-07-25 12:44:59 | INFO |   - 引用消息类型: 
2025-07-25 12:44:59 | INFO |   - 引用消息内容: 
开放群聊加好友了，需要的赶紧加吧，明天关掉
2025-07-25 12:44:59 | INFO |   - 引用消息发送人: wxid_l9koi6kli78i22
