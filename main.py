import asyncio
import os
import sys
import time
import tomllib
import traceback
import signal
from pathlib import Path

from loguru import logger
from watchdog.events import FileSystemEventHandler
from watchdog.observers import Observer

# 延迟导入bot_core以避免循环导入
# from bot_core import bot_core
# 移除系统监控导入
# from utils.monitoring import system_monitor

# 全局变量，用于控制程序退出
shutdown_event = asyncio.Event()


def is_api_message(record):
    return record["level"].name == "API"


class ConfigChangeHandler(FileSystemEventHandler):
    def __init__(self, restart_callback):
        self.restart_callback = restart_callback
        self.last_triggered = 0
        self.cooldown = 2  # 冷却时间(秒)
        self.waiting_for_change = False  # 是否在等待文件改变
        # 添加文件后缀白名单
        self.valid_extensions = {'.py', '.toml'}

    def on_modified(self, event):
        if not event.is_directory:
            try:
                current_time = time.time()
                if current_time - self.last_triggered < self.cooldown:
                    return

                file_path = Path(event.src_path).resolve()
                if (file_path.name == "main_config.toml" or
                        ("plugins" in file_path.parts and file_path.suffix in self.valid_extensions)):
                    # 只保留关键的变化通知
                    logger.info(f"检测到文件变化: {file_path}")
                    self.last_triggered = current_time
                    if self.waiting_for_change:
                        logger.info("正在重启...")  # 简化重启提示
                        self.waiting_for_change = False
                    self.restart_callback()
            except Exception as e:
                logger.error(f"处理文件变化时出错: {e}")


async def close_aiohttp_sessions():
    """关闭所有aiohttp会话"""
    try:
        import aiohttp
        if hasattr(aiohttp.ClientSession, "_instances"):
            for session in list(aiohttp.ClientSession._instances):
                if not session.closed:
                    await session.close()
            logger.info("已关闭所有aiohttp会话")
    except Exception as e:
        logger.warning(f"关闭aiohttp会话时出错: {e}")


def signal_handler():
    """处理信号，设置关闭事件"""
    logger.info("收到终止信号，正在优雅关闭...")
    shutdown_event.set()


async def main():
    # 设置工作目录为脚本所在目录
    script_dir = Path(__file__).resolve().parent
    os.chdir(script_dir)
    
    # 注册信号处理器
    if sys.platform == 'win32':
        # Windows 平台使用传统的 signal.signal 方法
        signal.signal(signal.SIGINT, lambda signum, frame: signal_handler())
        # Windows 不支持 SIGTERM
    else:
        # 非 Windows 平台使用 asyncio 的信号处理
        for sig in (signal.SIGINT, signal.SIGTERM):
            asyncio.get_event_loop().add_signal_handler(
                sig, signal_handler
            )
    
    # 延迟导入bot_core，避免循环导入问题
    from bot_core import bot_core
    
    try:
        # 读取配置文件
        config_path = script_dir / "main_config.toml"
        plugins_path = script_dir / "plugins"
        
        with open(config_path, "rb") as f:
            config = tomllib.load(f)
    except Exception as e:
        logger.error(f"读取配置文件失败: {e}")
        return

    # 检查是否启用自动重启
    auto_restart = config.get("XYBot", {}).get("auto-restart", False)

    if auto_restart:
        observer = Observer()

        def cleanup_resources():
            observer.stop()
            try:
                import multiprocessing.resource_tracker
                multiprocessing.resource_tracker._resource_tracker.clear()
            except Exception as e:
                logger.warning(f"清理资源时出错: {e}")
            observer.join()

        async def cleanup_and_exit():
            logger.info("正在关闭程序...")
            await close_aiohttp_sessions()  # 关闭aiohttp会话
            cleanup_resources()
            shutdown_event.set()

        def restart_program():
            logger.info("正在重启程序...")
            asyncio.run(close_aiohttp_sessions())  # 关闭aiohttp会话
            cleanup_resources()
            os.execv(sys.executable, [sys.executable] + sys.argv)

        handler = ConfigChangeHandler(restart_program)
        observer.schedule(handler, str(script_dir), recursive=False)  # 监控主配置文件
        if plugins_path.exists():  # 确保插件目录存在
            observer.schedule(handler, str(plugins_path), recursive=True)
        observer.start()

        try:
            # 移除系统监控任务
            
            # 启动主任务并等待关闭事件
            core_task = asyncio.create_task(bot_core())
            
            # 等待关闭事件或任务完成
            await asyncio.wait(
                [core_task, asyncio.create_task(shutdown_event.wait())],
                return_when=asyncio.FIRST_COMPLETED
            )
            
            if not core_task.done():
                logger.info("正在取消主任务...")
                core_task.cancel()
                try:
                    await core_task
                except asyncio.CancelledError:
                    pass
            
            await cleanup_and_exit()
        except KeyboardInterrupt:
            await cleanup_and_exit()
        except Exception as e:
            logger.error(f"程序发生错误: {e}")
            logger.error(traceback.format_exc())
            logger.info("等待文件改变后自动重启...")
            handler.waiting_for_change = True

            # 确保在等待重启过程中可以正常关闭程序
            try:
                while handler.waiting_for_change and not shutdown_event.is_set():
                    await asyncio.sleep(1)
            finally:
                if shutdown_event.is_set():
                    await cleanup_and_exit()
    else:
        try:
            # 移除系统监控任务
            async def graceful_exit_and_restart():
                """优雅退出程序并重启"""
                logger.info("正在优雅关闭程序并准备重启...")
                await close_aiohttp_sessions()  # 关闭aiohttp会话
                shutdown_event.set()
                # 在Windows上执行重启
                import subprocess
                import os
                logger.info("正在重启程序...")
                # 使用subprocess启动新进程执行当前脚本
                subprocess.Popen([sys.executable] + sys.argv, 
                                 creationflags=subprocess.CREATE_NEW_PROCESS_GROUP if sys.platform == 'win32' else 0)
                # 退出当前进程
                os._exit(0)
            
            # 启动主任务并等待关闭事件
            core_task = asyncio.create_task(bot_core())
            
            # 等待关闭事件或任务完成
            await asyncio.wait(
                [core_task, asyncio.create_task(shutdown_event.wait())],
                return_when=asyncio.FIRST_COMPLETED
            )
            
            if not core_task.done():
                logger.info("正在取消主任务...")
                core_task.cancel()
                try:
                    await core_task
                except asyncio.CancelledError:
                    pass
            
            # 确保所有资源正确关闭
            await close_aiohttp_sessions()
        except KeyboardInterrupt:
            logger.info("收到终止信号，正在关闭...")
            await close_aiohttp_sessions()  # 关闭aiohttp会话
        except Exception as e:
            logger.error(f"发生错误: {e}")
            logger.error(traceback.format_exc())
            await close_aiohttp_sessions()  # 确保出错时也能关闭会话


def check_python_version():
    MIN_PYTHON = (3, 11)
    if sys.version_info[:2] < MIN_PYTHON:
        print(f"需要Python {MIN_PYTHON[0]}.{MIN_PYTHON[1]}或更高版本")
        sys.exit(1)


def setup_logging():
    logger.remove()
    logger.level("API", no=1, color="<cyan>")

    # 文件日志
    logger.add(
        "logs/XYBot_{time}.log",
        format="{time:YYYY-MM-DD HH:mm:ss} | {level} | {message}",
        rotation="00:01",
        retention="2 weeks",
        enqueue=True,
        backtrace=True,
        diagnose=True,
        level="DEBUG",
        encoding="utf-8",
    )

    # 控制台日志
    logger.add(
        sys.stdout,
        colorize=True,
        format="<green>{time:YYYY-MM-DD HH:mm:ss}</green> | <level>{level}</level> | {message}",
        level="TRACE",
        enqueue=True,
        backtrace=True,
        diagnose=True,
    )


if __name__ == "__main__":
    check_python_version()
    
    print(
        "░▒▓█▓▒░░▒▓█▓▒░▒▓█▓▒░░▒▓█▓▒░▒▓███████▓▒░ ░▒▓██████▓▒░▒▓████████▓▒░      ░▒▓█▓▒░░▒▓█▓▒░▒▓███████▓▒░  \n"
        "░▒▓█▓▒░░▒▓█▓▒░▒▓█▓▒░░▒▓█▓▒░▒▓█▓▒░░▒▓█▓▒░▒▓█▓▒░░▒▓█▓▒░ ░▒▓█▓▒░          ░▒▓█▓▒░░▒▓█▓▒░      ░▒▓█▓▒░ \n"
        "░▒▓█▓▒░░▒▓█▓▒░▒▓█▓▒░░▒▓█▓▒░▒▓█▓▒░░▒▓█▓▒░▒▓█▓▒░░▒▓█▓▒░ ░▒▓█▓▒░           ░▒▓█▓▒▒▓█▓▒░       ░▒▓█▓▒░ \n"
        " ░▒▓██████▓▒░ ░▒▓██████▓▒░░▒▓███████▓▒░░▒▓█▓▒░░▒▓█▓▒░ ░▒▓█▓▒░           ░▒▓█▓▒▒▓█▓▒░ ░▒▓██████▓▒░  \n"
        "░▒▓█▓▒░░▒▓█▓▒░  ░▒▓█▓▒░   ░▒▓█▓▒░░▒▓█▓▒░▒▓█▓▒░░▒▓█▓▒░ ░▒▓█▓▒░            ░▒▓█▓▓█▓▒░ ░▒▓█▓▒░        \n"
        "░▒▓█▓▒░░▒▓█▓▒░  ░▒▓█▓▒░   ░▒▓█▓▒░░▒▓█▓▒░▒▓█▓▒░░▒▓█▓▒░ ░▒▓█▓▒░            ░▒▓█▓▓█▓▒░ ░▒▓█▓▒░        \n"
        "░▒▓█▓▒░░▒▓█▓▒░  ░▒▓█▓▒░   ░▒▓███████▓▒░ ░▒▓██████▓▒░  ░▒▓█▓▒░             ░▒▓██▓▒░  ░▒▓████████▓▒░\n")

    setup_logging()
    asyncio.run(main())
