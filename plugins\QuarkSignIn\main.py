import os, json, httpx, asyncio, time, traceback, random
try:
    import tomllib
except ModuleNotFoundError:
    import tomli as tomllib
from loguru import logger
from pathlib import Path
from WechatAPI import WechatAPIClient
from utils.decorators import on_text_message, schedule
from utils.plugin_base import PluginBase

class QuarkSignIn(PluginBase):
    description = "夸克网盘自动签到插件"
    author = "XYBot"
    version = "1.0.0"
    plugin_name = "QuarkSignIn"

    def __init__(self):
        super().__init__()
        self.temp_dir = Path("plugins/QuarkSignIn/temp")
        self.temp_dir.mkdir(parents=True, exist_ok=True)
        self._load_config()
        self.user_last_request = {}

    def _load_config(self):
        try:
            with open(f"plugins/{self.plugin_name}/config.toml", "rb") as f:
                config = tomllib.load(f).get(self.plugin_name, {})
        except:
            config = {}

        self.enable = config.get("enable", True)
        self.command = config.get("command", ["夸克签到", "夸克"])
        self.command_format = config.get("command-format", "使用方法：夸克签到 - 执行夸克网盘签到任务")
        self.cooldown = config.get("rate_limit", {}).get("cooldown", 10)
        self.natural_response = config.get("natural_response", False)

        # 自动签到配置
        self.auto_signin_enable = config.get("auto_signin_enable", True)
        self.auto_signin_hour = config.get("auto_signin_hour", 8)
        self.auto_signin_minute = config.get("auto_signin_minute", 0)
        self.notification_wxid = config.get("notification_wxid", "")

        # 获取Cookie配置
        self.cookie_list = config.get("cookies", {}).get("cookie_list", [])

    async def on_enable(self, bot=None):
        """插件启用时调用，动态设置定时任务时间"""
        await super().on_enable(bot)

        if self.auto_signin_enable:
            try:
                # 获取定时任务的ID
                job_id = f"{self.__class__.__module__}.{self.__class__.__name__}.auto_signin_task"

                # 使用APScheduler的reschedule_job方法修改任务时间
                from utils.decorators import scheduler
                scheduler.reschedule_job(
                    job_id=job_id,
                    trigger='cron',
                    hour=self.auto_signin_hour,
                    minute=self.auto_signin_minute
                )
                logger.info(f"[{self.plugin_name}] 已设置自动签到时间: {self.auto_signin_hour:02d}:{self.auto_signin_minute:02d}")
            except Exception as e:
                logger.error(f"[{self.plugin_name}] 设置定时任务时间失败: {e}")

    def convert_bytes(self, b):
        """将字节转换为 MB GB TB"""
        units = ("B", "KB", "MB", "GB", "TB", "PB", "EB", "ZB", "YB")
        i = 0
        while b >= 1024 and i < len(units) - 1:
            b /= 1024
            i += 1
        return f"{b:.2f} {units[i]}"

    async def get_growth_info(self, user_data):
        """获取用户当前的签到信息"""
        url = "https://drive-m.quark.cn/1/clouddrive/capacity/growth/info"
        querystring = {
            "pr": "ucpro",
            "fr": "android",
            "kps": user_data.get('kps'),
            "sign": user_data.get('sign'),
            "vcode": user_data.get('vcode')
        }

        headers = {
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
            'Accept': 'application/json, text/plain, */*',
            'Accept-Language': 'zh-CN,zh;q=0.9,en;q=0.8',
            'Referer': 'https://pan.quark.cn/',
        }

        try:
            async with httpx.AsyncClient(timeout=10) as client:
                response = await client.get(url=url, params=querystring, headers=headers)
                logger.debug(f"[{self.plugin_name}] API响应状态码: {response.status_code}")
                
                response_json = response.json()
                if response_json.get("data"):
                    return response_json["data"]
                else:
                    logger.error(f"[{self.plugin_name}] API返回错误: {response_json}")
                    return False
        except Exception as e:
            logger.error(f"[{self.plugin_name}] 请求异常: {e}")
            return False

    async def get_growth_sign(self, user_data):
        """执行签到"""
        url = "https://drive-m.quark.cn/1/clouddrive/capacity/growth/sign"
        querystring = {
            "pr": "ucpro",
            "fr": "android",
            "kps": user_data.get('kps'),
            "sign": user_data.get('sign'),
            "vcode": user_data.get('vcode')
        }

        headers = {
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36',
            'Accept': 'application/json, text/plain, */*',
            'Accept-Language': 'zh-CN,zh;q=0.9,en;q=0.8',
            'Referer': 'https://pan.quark.cn/',
            'Content-Type': 'application/json',
        }

        data = {"sign_cyclic": True}

        try:
            async with httpx.AsyncClient(timeout=10) as client:
                response = await client.post(url=url, json=data, params=querystring, headers=headers)
                logger.debug(f"[{self.plugin_name}] 签到响应状态码: {response.status_code}")
                
                response_json = response.json()
                if response_json.get("data"):
                    return True, response_json["data"]["sign_daily_reward"]
                else:
                    return False, response_json.get("message", "未知错误")
        except Exception as e:
            logger.error(f"[{self.plugin_name}] 签到请求异常: {e}")
            return False, str(e)

    async def do_sign_for_user(self, user_data):
        """为单个用户执行签到任务"""
        log = ""
        
        # 获取签到信息
        growth_info = await self.get_growth_info(user_data)
        if growth_info:
            user_type = "88VIP" if growth_info['88VIP'] else "普通用户"
            username = user_data.get('user', '未知用户')
            total_capacity = self.convert_bytes(growth_info['total_capacity'])
            
            log += f"👤 {user_type} {username}\n"
            log += f"💾 网盘总容量：{total_capacity}\n"
            
            # 签到累计容量
            if "sign_reward" in growth_info['cap_composition']:
                sign_capacity = self.convert_bytes(growth_info['cap_composition']['sign_reward'])
                log += f"📈 签到累计容量：{sign_capacity}\n"
            else:
                log += f"📈 签到累计容量：0 MB\n"
            
            # 检查今日是否已签到
            if growth_info["cap_sign"]["sign_daily"]:
                daily_reward = self.convert_bytes(growth_info['cap_sign']['sign_daily_reward'])
                progress = growth_info['cap_sign']['sign_progress']
                target = growth_info['cap_sign']['sign_target']
                log += f"✅ 今日已签到+{daily_reward}，连签进度({progress}/{target})\n"
            else:
                # 执行签到
                sign_success, sign_return = await self.get_growth_sign(user_data)
                if sign_success:
                    daily_reward = self.convert_bytes(sign_return)
                    progress = growth_info['cap_sign']['sign_progress'] + 1
                    target = growth_info['cap_sign']['sign_target']
                    log += f"🎉 今日签到成功+{daily_reward}，连签进度({progress}/{target})\n"
                else:
                    log += f"❌ 签到失败: {sign_return}\n"
        else:
            log += f"❌ 获取签到信息失败\n"
        
        return log

    @on_text_message
    async def handle_text(self, bot: WechatAPIClient, message: dict):
        if not self.enable:
            return

        content = str(message["Content"]).strip()
        wxid = message["FromWxid"]
        user_wxid = message["SenderWxid"]

        command = content.split(" ", 1)
        if command[0] not in self.command:
            return

        # 限流检查
        wait_time = self._check_user_limit(wxid, user_wxid)
        if wait_time > 0:
            await bot.send_at_message(wxid, f"请等待 {wait_time:.1f} 秒", [user_wxid])
            return

        try:
            # 执行签到任务
            result = await self._process_signin()
            
            # 发送结果
            await bot.send_text_message(wxid, result)

        except Exception as e:
            logger.error(f"[{self.plugin_name}] 异常: {e}")
            await bot.send_at_message(wxid, "签到处理失败", [user_wxid])

    async def _process_signin(self) -> str:
        """处理签到请求"""
        if not self.cookie_list:
            return "❌ 未配置夸克网盘Cookie信息"
        
        msg = f"🌟 夸克网盘签到开始\n"
        msg += f"📊 检测到 {len(self.cookie_list)} 个账号\n\n"
        
        for i, cookie_str in enumerate(self.cookie_list):
            # 解析Cookie
            user_data = {}
            for item in cookie_str.replace(" ", "").split(';'):
                if item and '=' in item:
                    key, value = item.split('=', 1)
                    user_data[key] = value
            
            msg += f"🔄 第{i + 1}个账号签到中...\n"
            
            try:
                log = await self.do_sign_for_user(user_data)
                msg += log + "\n"
            except Exception as e:
                error_msg = f"❌ 账号{i + 1}签到失败: {e}\n\n"
                logger.error(f"[{self.plugin_name}] {error_msg}")
                msg += error_msg
        
        msg += "✨ 夸克网盘签到完成"
        return msg

    def _check_user_limit(self, wxid: str, user_wxid: str) -> float:
        user_key = f"{wxid}_{user_wxid}"
        last_request = self.user_last_request.get(user_key, 0)
        current_time = time.time()
        elapsed = current_time - last_request
        self.user_last_request[user_key] = current_time

        if elapsed < self.cooldown:
            return self.cooldown - elapsed

        return 0

    @schedule('cron', hour=8, minute=0)  # 默认每天8点执行，将在on_enable中动态修改
    async def auto_signin_task(self, bot: WechatAPIClient):
        """定时自动签到任务"""
        if not self.enable or not self.auto_signin_enable:
            return

        try:
            logger.info(f"[{self.plugin_name}] 开始执行定时自动签到任务")

            # 执行签到任务
            result = await self._process_signin()

            # 发送通知到指定微信ID
            if self.notification_wxid:
                notification_msg = f"🤖 夸克网盘自动签到完成\n\n{result}"
                await bot.send_text_message(self.notification_wxid, notification_msg)
                logger.info(f"[{self.plugin_name}] 已发送签到通知到: {self.notification_wxid}")
            else:
                logger.warning(f"[{self.plugin_name}] 未配置通知微信ID，跳过通知发送")

        except Exception as e:
            logger.error(f"[{self.plugin_name}] 定时自动签到异常: {e}")

            # 发送错误通知
            if self.notification_wxid:
                error_msg = f"❌ 夸克网盘自动签到失败\n错误信息: {str(e)}"
                try:
                    await bot.send_text_message(self.notification_wxid, error_msg)
                except Exception as notify_error:
                    logger.error(f"[{self.plugin_name}] 发送错误通知失败: {notify_error}")
