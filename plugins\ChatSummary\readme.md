# ChatSummary 群聊总结和统计插件

## 插件介绍

ChatSummary 是一个用于微信群聊消息记录和统计分析的插件。该插件能够收集并存储群聊中的各类消息，并提供丰富的统计功能，帮助用户了解群聊的活跃度和互动情况。

### 主要功能

1. **消息记录**：自动保存群聊中的各类消息，包括文本、图片、语音、视频、文件等
2. **群聊统计**：统计群聊中的消息数量、活跃用户、消息类型分布等数据
3. **数据导出**：将收集到的消息记录导出为文本文件
4. **时间范围分析**：支持按时间范围查询和分析群聊数据
5. **文件上传分享**：支持将导出的文件上传到ChatGLM云服务，并提供下载链接
6. **AI智能总结**：通过豆包插件生成结构化的群聊精华内容总结

### 支持的消息类型

- 文本消息
- 表情消息
- 图片消息
- 语音消息
- 视频消息
- 文件消息
- 引用消息
- 拍一拍消息
- @消息
- 系统消息
- 文章消息
- XML消息

## 使用方法

### 基本命令

在启用了该插件的群聊中，可以使用以下命令：

- `开启群聊总结` - 启用群聊总结功能
- `关闭群聊总结` - 关闭群聊总结功能
- `开启群聊统计` - 启用群聊统计功能
- `关闭群聊统计` - 关闭群聊统计功能
- `群聊总结` - 导出群聊消息记录（默认导出最近7天），并提供文件下载链接
- `群聊总结 智能总结` - 使用豆包插件生成结构化的群聊精华内容总结
- `群聊统计` - 生成群聊统计报告（默认统计最近7天）

### 时间范围查询

可以在命令后附加日期参数，指定查询的时间范围：

- 单个日期：`群聊统计 2023-01-01`（统计指定日期的数据）
- 日期范围：`群聊统计 2023-01-01 2023-01-07`（统计指定日期范围内的数据）
- AI总结带日期：`群聊总结 智能总结 2023-01-01 2023-01-07`（AI总结指定日期范围内的群聊）

## 统计报告内容

统计报告包含以下内容：

1. **基本信息**
   - 总消息数
   - 活跃用户数

2. **消息类型分布**
   - 各类型消息的数量及占比

3. **活跃用户排行榜**
   - 前10名活跃用户及其发送消息数量

4. **消息时间分布**
   - 凌晨(00-06)、上午(06-12)、下午(12-18)、晚上(18-24)各时段的消息数量及占比

## AI智能总结功能

AI智能总结功能依赖于豆包插件(Doubao)，为群聊内容生成精美的结构化总结报告，包括：

1. **核心讨论主题**：提炼3-5个主要话题并标注热度
2. **关键结论与问题**：每个主题的主要结论和未解决的问题
3. **活跃成员分析**：识别并列出群聊中最活跃的成员
4. **重点关注预告**：预测下一步需要关注的事项

总结报告格式精美，使用emoji、分隔符和加粗文本使内容更加清晰易读。

## 配置说明

插件配置文件位于 `plugins/ChatSummary/config.toml`，主要配置项包括：

```toml
[ChatSummary]
# 是否启用插件
enable = true
# 触发命令
command = ["群聊总结", "群聊统计", "开启群聊总结", "关闭群聊总结", "开启群聊统计", "关闭群聊统计"]
# 命令格式说明
command-format = "默认使用说明"
```

> **注意**：开启/关闭群聊总结和统计功能的命令仅限管理员使用。管理员列表在 `main_config.toml` 文件的 `XYBot.admins` 配置项中设置。

# 数据库配置
[ChatSummary.database]
# 数据库路径
path = "database/chat_summary.db"
# 数据最大保存天数
max_storage_days = 7

# 功能开关配置
[ChatSummary.features]
# 是否启用群聊总结功能
enable_summary = true
# 是否启用群聊统计功能
enable_statistics = true

# 请求限制配置
[ChatSummary.rate_limit]
# 同一用户两次请求之间的最小间隔（秒）
cooldown = 60

# 文件上传配置
[ChatSummary.upload]
# 是否启用文件上传功能
enable = true
# 上传接口URL（完整URL，包含主机名）
url = "https://chatglm.cn/chatglm/backend-api/assistant/file_upload"
# ChatGLM API 令牌，需要填入你的有效令牌
token = ""
# ChatGLM Assistant ID
assistant_id = "65940acff94777010aa6b796"
# 上传超时时间（秒）
timeout = 30
# 是否添加Cookie（可选）
add_cookies = false
# Cookie 值（可选，仅当add_cookies=true时有效）
cookies = "chatglm_token=你的token值; chatglm_user_id=你的用户ID"
```

## 数据存储

插件使用SQLite数据库存储消息数据，默认路径为 `database/chat_summary.db`。存储的数据包括：

- 群聊ID和名称
- 发送者ID和昵称
- 消息内容
- 消息类型
- 消息长度
- 发送时间

## 文件上传

插件支持将导出的群聊记录上传到ChatGLM的文件服务器，以便于分享。要使用此功能，需要：

1. 在配置文件中启用上传功能 `enable = true`
2. 设置有效的ChatGLM API令牌 `token = "你的令牌"`
3. 确保网络环境能够正常访问ChatGLM服务器
4. 安装必要的依赖: `pip install httpx`

### 精确模拟ChatGLM上传请求

插件现已优化，能够完全模拟ChatGLM网页端的文件上传请求，包括所有请求头部信息、表单格式等。这提高了上传成功率和兼容性，确保即使ChatGLM接口有所变化，也能保持良好的兼容性。

### Cookie支持

如果通过令牌认证上传失败，可以尝试启用Cookie认证：

1. 在配置文件中设置 `add_cookies = true`
2. 从浏览器中复制ChatGLM网站的Cookie，填入 `cookies` 配置项
3. 必要的Cookie至少包括 `chatglm_token` 和 `chatglm_user_id`

上传成功后，插件会返回一个可访问的文件下载链接，便于用户分享和下载。

## AI智能总结

智能总结功能现在使用豆包插件(Doubao)进行处理，无需额外配置：

1. 确保已安装豆包插件(Doubao)并正确配置
2. 豆包插件会处理文本链接并生成结构化总结
3. ChatSummary插件负责消息收集、上传和结果展示

使用方法：
- 发送 `群聊总结 智能总结` 命令
- 插件会先导出聊天记录，上传至ChatGLM服务器
- 然后将文件URL发送给豆包插件进行智能总结
- 最后返回结构化的总结报告

## 注意事项

1. 该插件需要在群聊中单独启用，发送"开启群聊统计"或"开启群聊总结"命令来启用功能
2. 插件有请求频率限制，默认同一用户在同一群聊中两次请求间隔不能少于60秒
3. 为保证数据库性能，默认只保存最近7天的消息数据
4. 文件上传功能需要有效的ChatGLM API令牌或Cookie，请在配置文件中正确设置
5. 文件上传功能依赖于httpx库，请确保已正确安装
6. 如果上传失败，插件会自动回退到返回本地文件路径
7. AI智能总结功能需要安装并正确配置豆包插件(Doubao)