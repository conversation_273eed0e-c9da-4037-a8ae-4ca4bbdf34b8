#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
美图AI插件 - 对话生图改图工具
基于抓包分析实现的美图AI功能
支持对话、生图、改图等功能
"""

import os
import json
import tomllib
import time
import random
import uuid
import re
import asyncio
import httpx
from urllib.parse import quote
from pathlib import Path
from typing import Optional, Dict, Any, Generator, Union

from WechatAPI import WechatAPIClient
from utils.decorators import on_text_message
from utils.plugin_base import PluginBase


class MeituAI(PluginBase):
    description = "美图AI对话生图改图插件"
    author = "XYBot"
    version = "1.0.0"
    plugin_name = "MeituAI"

    def __init__(self):
        super().__init__()
        
        # 创建临时目录
        self.temp_dir = Path("temp/meituai")
        self.temp_dir.mkdir(parents=True, exist_ok=True)
        
        # 加载配置
        self._load_config()
        
        # 初始化API相关
        self._init_api_config()
        
        # 初始化会话管理
        self._init_session_management()
        
        # 初始化限流机制
        self._init_rate_limiting()
        
        # 初始化自然化响应
        self._init_natural_responses()
        
        # HTTP客户端管理
        self._client = None
        self._client_lock = asyncio.Lock()

        # 启动健康检查任务
        if self.health_check["enable"]:
            asyncio.create_task(self._health_check_loop())

    def _load_config(self):
        """加载配置文件"""
        config = {}
        try:
            config_path = "plugins/MeituAI/config.toml"
            if os.path.exists(config_path):
                with open(config_path, "rb") as f:
                    config = tomllib.load(f).get("MeituAI", {})
        except Exception as e:
            print(f"加载配置失败: {e}")
        
        # 基础配置
        self.enable = config.get("enable", True)
        self.command = config.get("command", ["开启美图模式"])
        self.command_format = config.get("command-format", "美图AI插件使用说明")
        self.natural_response = config.get("natural_response", True)
        self.debug_mode = config.get("debug_mode", False)
        
        # API配置（仅用于可选的覆盖设置）
        self.api_config = config.get("API", {})
        
        # 限流配置
        rate_limit_config = config.get("rate_limit", {})
        self.rate_limit = {
            "tokens_per_second": rate_limit_config.get("tokens_per_second", 0.3),
            "bucket_size": rate_limit_config.get("bucket_size", 3)
        }
        self.min_request_interval = rate_limit_config.get("min_request_interval", 3.0)

        # 健康检查配置
        health_check_config = config.get("health_check", {})
        self.health_check = {
            "enable": health_check_config.get("enable", True),
            "check_interval": health_check_config.get("check_interval", 300),
            "connection_max_age": health_check_config.get("connection_max_age", 3600),
            "max_failures": health_check_config.get("max_failures", 3),
            "stream_timeout": health_check_config.get("stream_timeout", 30),
            "request_timeout": health_check_config.get("request_timeout", 120)
        }

        # 智能重试配置
        retry_config = config.get("retry", {})
        self.retry_config = {
            "enable": retry_config.get("enable", True),
            "max_retries": retry_config.get("max_retries", 2),
            "retry_delay": retry_config.get("retry_delay", 2),
            "auto_reset_connection": retry_config.get("auto_reset_connection", True)
        }

    def _init_api_config(self):
        """初始化API配置 - 直接使用原py文件中的参数"""
        # 直接使用原文件中的参数，不从配置文件读取
        self.base_url = "https://ai-engine-gateway-roboneo.meitu.com"
        self.access_token = "_v2NjUyYzUxNGMjMTc2MDg0NzIzNCM4Mzg4ODY1IzExIzExODlhYTMxNGRmY2Q5NGU1ZGFjNDNmYjg3ZThiYmQwYmYjSFVBV0VJX0NMT1VEI0JKX0hXIzY4N2RiZTgy"
        self.timeout = 60
        self.max_retries = 3

        # 基础请求头 - 完全按照原文件
        self.headers = {
            "Access-Token": self.access_token,
            "Content-Type": "application/json; charset=utf-8",
            "Host": "ai-engine-gateway-roboneo.meitu.com",
            "Connection": "Keep-Alive",
            "Accept-Encoding": "gzip",
            "User-Agent": "okhttp/4.12.0",
            "traceIdHigh": "2886031057",
            "traceIdLow": str(int(time.time() * 1000000))
        }

        # 基础参数模板 - 完全按照原文件
        self.base_params = {
            "path_scene": "roboneo",
            "body": "",
            "features": "",
            "image_urls": [],
            "video_urls": [],
            "later_face": 0,
            "id": "",
            "content": "",
            "type": "",
            "task_id": "",
            "req_mode": "",
            "time_zone": "Asia/Shanghai",
            "platform": "android",
            "client_id": "1189857651",
            "client_os": "10",
            "client_channel_id": "And_NeoHomeMobile",
            "lang": "zh-Hans",
            "area_code": "CN",
            "version": "1.3.0",
            "uid": "1697403212",
            "gid": "2886031057",
            "theme": 2,
            "app_scene": "roboneo",
            "token": "7E14E5FF92D54108A074A34D2083C93F"
        }

    def _init_session_management(self):
        """初始化会话管理"""
        # 美图模式管理
        self._meitu_mode = {}  # 记录哪些群组开启了美图模式
        self._meitu_users = {}  # 记录开启美图模式的用户
        self._meitu_timeout = 600  # 10分钟超时
        self._last_meitu_time = {}

        # 房间ID管理（每个群/用户一个房间）
        self._room_ids = {}

        # 当前图片管理（用于编辑功能）
        self._current_images = {}

        # AI询问状态管理
        self._ai_asking = {}  # 记录AI是否在询问用户
        self._ai_questions = {}  # 记录AI的问题

        # 连接健康检查
        self._last_successful_request = {}  # 记录每个房间最后成功请求时间
        self._connection_failures = {}  # 记录连接失败次数
        self._max_failures = self.health_check["max_failures"]  # 最大失败次数，超过后重置连接

        # 智能重试机制
        self._retry_attempts = {}  # 记录重试次数

    def _init_rate_limiting(self):
        """初始化限流机制"""
        # 令牌桶限流
        self._token_bucket = self.rate_limit["bucket_size"]
        self._last_token_time = time.time()
        self._token_lock = asyncio.Lock()
        
        # 用户请求限制
        self._user_limits = {}

    def _init_natural_responses(self):
        """初始化自然化响应"""
        self.confirm_msgs = ["好的", "收到", "明白了", "嗯", "知道了", "开始处理", "马上来"]
        self.error_msgs = ["这可把我难住了", "搞不定了", "这个有点难", "弄不了", "出问题了", "AI罢工了"]
        self.rate_limit_msgs = ["慢点慢点", "别这么急", "一个一个来", "太快了太快了", "歇会儿", "让AI喘口气"]
        self.generating_msgs = ["正在创作中", "AI正在画画", "马上就好", "请稍等", "创意正在生成"]
        self.editing_msgs = ["正在修改", "AI正在调整", "马上改好", "稍等片刻"]
        self.failed_msgs = ["这个画不出来", "AI说不行", "内容有问题", "换个描述试试", "这个不符合规范"]
        self.empty_msgs = ["AI没反应", "好像没听懂", "再说一遍？", "换个说法试试", "描述再详细点"]
        self.no_response_msgs = ["AI走神了", "连接有问题", "稍后再试试", "网络不太好", "AI可能在忙"]


    async def _simple_confirm(self, bot, wxid):
        """简单确认回复"""
        if self.natural_response:
            await bot.send_text_message(wxid, random.choice(self.confirm_msgs))

    async def _processing_notify(self, bot, wxid):
        """处理中通知（通用）"""
        if self.natural_response:
            # 随机选择一个处理中的消息
            processing_msgs = self.generating_msgs + self.editing_msgs
            await bot.send_text_message(wxid, random.choice(processing_msgs))

    async def _update_tokens(self):
        """更新令牌桶"""
        current_time = time.time()
        elapsed = current_time - self._last_token_time
        new_tokens = elapsed * self.rate_limit["tokens_per_second"]
        self._token_bucket = min(self.rate_limit["bucket_size"], self._token_bucket + new_tokens)
        self._last_token_time = current_time

    async def _acquire_token(self):
        """获取令牌"""
        async with self._token_lock:
            await self._update_tokens()
            if self._token_bucket >= 1:
                self._token_bucket -= 1
                return True
            return False

    def _check_user_limit(self, wxid, user_wxid):
        """检查用户请求限制"""
        current_time = time.time()
        if wxid not in self._user_limits:
            self._user_limits[wxid] = {}
        last_request = self._user_limits[wxid].get(user_wxid, 0)
        wait_time = max(0, self.min_request_interval - (current_time - last_request))
        if wait_time == 0:
            self._user_limits[wxid][user_wxid] = current_time
        return wait_time

    def _get_room_id(self, wxid):
        """获取或创建房间ID"""
        if wxid not in self._room_ids:
            self._room_ids[wxid] = str(uuid.uuid4())
        return self._room_ids[wxid]

    async def get_session(self):
        """获取HTTP客户端"""
        async with self._client_lock:
            if self._client is None or self._client.is_closed:
                self._client = httpx.AsyncClient(
                    timeout=self.timeout,
                    headers=self.headers,
                    limits=httpx.Limits(max_keepalive_connections=20, max_connections=100)
                )
                self._client_created_time = time.time()  # 记录创建时间
            return self._client

    async def close_session(self):
        """关闭HTTP客户端"""
        async with self._client_lock:
            if self._client and not self._client.is_closed:
                await self._client.aclose()
                self._client = None

    async def _reset_connection(self):
        """重置HTTP连接"""
        print("重置HTTP连接...")
        await self.close_session()
        # 下次调用get_session时会自动创建新连接

    def _record_request_result(self, room_id: str, success: bool):
        """记录请求结果"""
        current_time = time.time()
        if success:
            self._last_successful_request[room_id] = current_time
            self._connection_failures[room_id] = 0
        else:
            self._connection_failures[room_id] = self._connection_failures.get(room_id, 0) + 1

    def _should_reset_connection(self, room_id: str) -> bool:
        """判断是否应该重置连接"""
        failures = self._connection_failures.get(room_id, 0)
        return failures >= self._max_failures

    async def _health_check_loop(self):
        """健康检查循环"""
        while True:
            try:
                await asyncio.sleep(self.health_check["check_interval"])
                current_time = time.time()

                # 检查长时间无响应的房间
                for room_id, last_success in list(self._last_successful_request.items()):
                    if current_time - last_success > 600:  # 10分钟无成功请求
                        failures = self._connection_failures.get(room_id, 0)
                        if failures > 0:
                            print(f"房间 {room_id} 长时间无响应，重置连接状态")
                            self._connection_failures[room_id] = 0

                # 定期重置HTTP连接以保持健康
                if self._client and not self._client.is_closed:
                    # 检查连接是否过老
                    if hasattr(self, '_client_created_time'):
                        if current_time - self._client_created_time > self.health_check["connection_max_age"]:
                            print("HTTP连接过老，主动重置")
                            await self._reset_connection()
                    else:
                        self._client_created_time = current_time

            except Exception as e:
                print(f"健康检查异常: {e}")
                await asyncio.sleep(60)  # 异常时等待1分钟再继续

    def _cleanup_session_state(self, wxid: str):
        """清理会话状态"""
        self._meitu_mode.pop(wxid, None)
        self._meitu_users.pop(wxid, None)
        self._last_meitu_time.pop(wxid, None)
        self._ai_asking.pop(wxid, None)
        self._ai_questions.pop(wxid, None)
        self._current_images.pop(wxid, None)
        # 清理连接状态
        room_id = self._room_ids.get(wxid)
        if room_id:
            self._connection_failures.pop(room_id, None)
            self._last_successful_request.pop(room_id, None)

    async def on_disable(self):
        """插件禁用时调用"""
        await self.close_session()

    async def _download_image(self, image_url: str) -> str:
        """使用httpx下载图片到本地"""
        try:
            import hashlib

            # 生成文件名
            url_hash = hashlib.md5(image_url.encode()).hexdigest()
            filename = f"meitu_{url_hash}.jpg"
            filepath = self.temp_dir / filename

            # 如果文件已存在，直接返回
            if filepath.exists():
                print(f"图片文件已存在: {filepath}")
                return str(filepath)

            print(f"开始下载图片: {image_url}")

            # 使用httpx下载图片，添加重试机制
            max_retries = 3
            for retry in range(max_retries):
                try:
                    async with httpx.AsyncClient(
                        timeout=30.0,
                        headers={
                            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'
                        }
                    ) as client:
                        response = await client.get(image_url)
                        if response.status_code == 200:
                            content = response.content
                            if len(content) > 0:
                                with open(filepath, 'wb') as f:
                                    f.write(content)
                                print(f"图片下载成功: {filepath}, 大小: {len(content)} bytes")
                                return str(filepath)
                            else:
                                print("下载的图片内容为空")
                        else:
                            print(f"下载图片失败，状态码: {response.status_code}")
                            if retry < max_retries - 1:
                                await asyncio.sleep(1)
                                continue
                except Exception as e:
                    print(f"下载图片异常 (重试 {retry + 1}/{max_retries}): {e}")
                    if retry < max_retries - 1:
                        await asyncio.sleep(1)
                        continue

            return None

        except Exception as e:
            print(f"下载图片异常: {e}")
            return None

    def _is_generate_request(self, content):
        """判断是否为生图请求"""
        generate_keywords = ['画', '生成', '创作', '制作', '绘制', '画出', '画一个', '画一张', '生成图片', '创建图片']
        return any(keyword in content for keyword in generate_keywords)

    def _is_edit_request(self, content):
        """判断是否为改图请求"""
        edit_keywords = ['改', '修改', '调整', '换成', '改成', '变成', '编辑', '更改']
        return any(keyword in content for keyword in edit_keywords)

    def _should_exit_meitu_mode(self, content):
        """判断是否应该退出美图模式"""
        exit_keywords = ['结束', '退出', 'quit', 'exit', '关闭', '停止']
        return content.strip().lower() in exit_keywords

    async def _send_welcome_message(self, bot, wxid, user_wxid):
        """发送欢迎消息 - 自动发送"你好"请求并返回AI响应"""
        try:
            # 发送初始的"你好"请求获取AI响应
            room_id = self._get_room_id(wxid)
            response = await self._send_message_and_get_response("你好", room_id)

            if response:
                await bot.send_text_message(wxid, response)
            else:
                await bot.send_text_message(wxid, "AI初始化失败，请重试")
        except Exception as e:
            await bot.send_text_message(wxid, "AI初始化失败，请重试")

    async def _send_message_to_ai(self, message: str, room_id: str, image_urls: list = None):
        """发送消息到美图AI并获取流式响应"""
        if image_urls is None:
            image_urls = []

        # 检查是否需要重置连接
        if self._should_reset_connection(room_id):
            await self._reset_connection()

        # 构建请求参数
        params = self.base_params.copy()
        params.update({
            "message": message,
            "image_urls": image_urls,
            "room_id": room_id,
            "trace_id": str(uuid.uuid4())
        })

        payload = {"parameter": params}

        max_retries = self.max_retries
        base_delay = 2

        for retry in range(max_retries):
            try:
                client = await self.get_session()

                # 发送POST请求 - 使用httpx的流式响应
                async with client.stream(
                    "POST",
                    f"{self.base_url}/roboneo/sync/request/stream",
                    json=payload,
                    timeout=self.timeout
                ) as response:

                    if response.status_code != 200:
                        self._record_request_result(room_id, False)
                        if retry < max_retries - 1:
                            wait_time = base_delay * (2 ** retry) + random.uniform(0, 1)
                            await asyncio.sleep(wait_time)
                            continue
                        else:
                            raise Exception(f"API请求失败: {response.status_code}")

                    # 解析SSE流式响应 - 异步处理，增加超时控制
                    buffer = ""
                    last_data_time = time.time()
                    stream_timeout = self.health_check["stream_timeout"]

                    async for chunk in response.aiter_bytes(1024):
                        chunk_str = chunk.decode('utf-8', errors='ignore')
                        buffer += chunk_str
                        last_data_time = time.time()  # 更新最后接收数据时间

                        # 按行处理
                        while '\n' in buffer:
                            line, buffer = buffer.split('\n', 1)
                            line = line.strip()

                            if line and line.startswith('data: '):
                                try:
                                    data_str = line[6:]  # 去掉 'data: ' 前缀
                                    if data_str.strip():
                                        data = json.loads(data_str)
                                        yield data
                                        # 让出控制权，避免阻塞
                                        await asyncio.sleep(0)
                                except json.JSONDecodeError:
                                    continue

                            # 检查流式响应超时
                            if time.time() - last_data_time > stream_timeout:
                                print(f"流式响应超时，房间ID: {room_id}")
                                raise Exception("流式响应超时")

                            # 定期让出控制权
                            await asyncio.sleep(0)

                    # 请求成功
                    self._record_request_result(room_id, True)
                    return

            except Exception as e:
                self._record_request_result(room_id, False)
                print(f"请求失败 (重试 {retry + 1}/{max_retries}): {e}")
                if retry < max_retries - 1:
                    wait_time = base_delay * (2 ** retry) + random.uniform(0, 1)
                    await asyncio.sleep(wait_time)
                    continue
                else:
                    raise e

    async def _send_message_and_get_response(self, message: str, room_id: str):
        """发送消息并获取简单文本响应（用于欢迎消息等）"""
        full_response = ""

        try:
            async for data in self._send_message_to_ai(message, room_id):
                if data.get("type") == "text":
                    content = data.get("content", "")
                    if content:
                        full_response += content
                elif data.get("type") == "status_change" and data.get("status") == "all_done":
                    break
        except Exception as e:
            return None

        return full_response if full_response else None

    def _should_retry(self, result: str, accumulated_text: str) -> bool:
        """判断是否应该重试"""
        if result in ["error", "empty"]:
            return True

        # 检查文本中是否包含临时性错误关键词
        temp_error_keywords = ["网络", "连接", "超时", "繁忙", "稍后", "重试"]
        if accumulated_text and any(keyword in accumulated_text for keyword in temp_error_keywords):
            return True

        return False

    async def _process_meitu_request_with_retry(self, bot, wxid, user_wxid, message: str, room_id: str, image_urls: list = None):
        """带重试的美图请求处理"""
        if not self.retry_config["enable"]:
            # 如果禁用重试，直接调用原方法
            return await self._process_meitu_request(bot, wxid, user_wxid, message, room_id, image_urls)

        max_retries = self.retry_config["max_retries"]
        retry_key = f"{wxid}_{message[:20]}"  # 使用wxid和消息前20字符作为重试键

        # 获取当前重试次数
        current_retries = self._retry_attempts.get(retry_key, 0)

        try:
            result = await self._process_meitu_request(bot, wxid, user_wxid, message, room_id, image_urls)

            # 如果成功，清除重试记录
            if result == "success":
                self._retry_attempts.pop(retry_key, None)
                return result

            # 如果失败且可以重试
            if self._should_retry(result, "") and current_retries < max_retries:
                self._retry_attempts[retry_key] = current_retries + 1
                print(f"请求失败，准备重试 ({current_retries + 1}/{max_retries}): {message[:30]}")

                # 等待一段时间后重试
                await asyncio.sleep(self.retry_config["retry_delay"] + current_retries)

                # 重置连接（如果配置启用）
                if self.retry_config["auto_reset_connection"]:
                    await self._reset_connection()

                # 发送重试提示
                if self.natural_response:
                    await bot.send_text_message(wxid, "再试一次...")

                # 递归重试
                return await self._process_meitu_request_with_retry(bot, wxid, user_wxid, message, room_id, image_urls)

            # 达到最大重试次数或不应该重试
            self._retry_attempts.pop(retry_key, None)
            return result

        except Exception as e:
            self._retry_attempts.pop(retry_key, None)
            print(f"重试处理异常: {e}")
            return "error"

    async def _process_meitu_request(self, bot, wxid, user_wxid, message: str, room_id: str, image_urls: list = None):
        """处理美图请求 - 增强错误处理和失败检测"""
        if image_urls is None:
            image_urls = []

        # 累积AI的文本响应
        accumulated_text = ""
        image_sent = False
        request_start_time = time.time()
        has_received_data = False  # 标记是否收到任何有效数据
        generation_failed = False  # 标记生成是否失败
        last_activity_time = time.time()  # 最后活动时间

        try:
            # 清除可能残留的AI询问状态
            if wxid in self._ai_asking:
                self._ai_asking[wxid] = False

            async for data in self._send_message_to_ai(message, room_id, image_urls):
                data_type = data.get("type")
                has_received_data = True
                last_activity_time = time.time()

                # 调试：记录所有收到的数据（除了常见的正常类型）
                if self.debug_mode and data_type not in ["text", "render_percent"]:
                    print(f"[调试] 收到数据类型: {data_type}, 完整数据: {data}")

                if data_type == "text":
                    content = data.get("content", "")
                    if content:
                        # 累积文本内容
                        accumulated_text += content
                        if self.debug_mode:
                            print(f"[调试] 收到文本内容: {content}")

                        # 检查是否包含失败关键词
                        failure_keywords = [
                            "无法生成", "生成失败", "违反", "不符合", "不能", "无法",
                            "抱歉", "错误", "失败", "不允许", "禁止", "拒绝",
                            "内容审核", "审核不通过", "不合规", "违规"
                        ]
                        if any(keyword in content for keyword in failure_keywords):
                            if self.debug_mode:
                                print(f"[调试] 检测到失败关键词，内容: {content}")
                            generation_failed = True

                elif data_type == "request":
                    # AI询问更多细节
                    question = data.get("question", "")
                    if question:
                        self._ai_asking[wxid] = True
                        self._ai_questions[wxid] = question
                        await bot.send_text_message(wxid, f"🤖 AI询问: {question}")
                        return "question"

                elif data_type == "design":
                    # 设计开始，记录设计ID
                    design_id = data.get("design_id", "")
                    print(f"开始设计，设计ID: {design_id}")

                elif data_type == "render_percent":
                    # 渲染进度
                    percent = data.get("percent", [0])
                    if isinstance(percent, list) and len(percent) > 0:
                        progress = percent[0]
                        print(f"渲染进度: {progress}%")

                elif data_type == "media":
                    media_item = data.get("media_item", {})
                    image_url = media_item.get("media_url")
                    if image_url and not image_sent:
                        # 先发送累积的文本（如果有的话）
                        if accumulated_text:
                            await bot.send_text_message(wxid, accumulated_text)
                            accumulated_text = ""  # 清空已发送的文本

                        # 保存当前图片用于后续编辑
                        self._current_images[wxid] = image_url

                        # 发送图片
                        image_sent_success = False
                        try:
                            # 必须先下载图片到本地，因为send_image_message不支持URL
                            image_path = await self._download_image(image_url)
                            if image_path:
                                # 使用本地文件路径发送图片
                                from pathlib import Path
                                await bot.send_image_message(wxid, Path(image_path))
                                image_sent_success = True
                                print(f"图片发送成功: {image_path}")
                            else:
                                print("图片下载失败，无法发送")
                                image_sent_success = False
                        except Exception as e:
                            print(f"图片发送失败: {e}")
                            image_sent_success = False

                        # 如果图片发送失败，发送图片链接作为备用
                        if not image_sent_success:
                            await bot.send_text_message(wxid, f"🖼️ 图片生成完成！\n图片地址: {image_url}")
                        # 图片发送成功时不需要额外提示

                        image_sent = True

                elif data_type == "error":
                    # 明确的错误响应 - 显示详细错误信息
                    print(f"收到错误响应，完整数据: {data}")

                    # 尝试提取各种可能的错误信息字段
                    error_msg = (
                        data.get("message") or
                        data.get("error_message") or
                        data.get("error") or
                        data.get("reason") or
                        data.get("detail") or
                        "生成过程中出现错误"
                    )

                    # 如果有错误代码，也显示出来
                    error_code = data.get("code") or data.get("error_code")
                    if error_code:
                        error_msg = f"[{error_code}] {error_msg}"

                    print(f"解析的错误信息: {error_msg}")
                    generation_failed = True

                    # 直接显示原始错误信息，不添加❌前缀
                    accumulated_text += error_msg

                elif data_type == "status_change":
                    status = data.get("status", "")
                    if self.debug_mode:
                        print(f"状态变更: {status}, 完整数据: {data}")

                    if status == "all_done":
                        break
                    elif status in ["failed", "error", "rejected"]:
                        generation_failed = True

                        # 尝试获取失败原因
                        failure_reason = (
                            data.get("reason") or
                            data.get("message") or
                            data.get("error_message") or
                            f"状态变更为失败: {status}"
                        )

                        print(f"生成失败，原因: {failure_reason}")
                        # 直接显示失败原因，不添加❌前缀
                        accumulated_text += failure_reason
                        break

                else:
                    # 未知的数据类型，记录下来
                    if self.debug_mode:
                        print(f"[调试] 未知数据类型: {data_type}, 数据: {data}")

                # 检查请求总时间，防止无限等待
                if time.time() - request_start_time > self.health_check["request_timeout"]:
                    print(f"请求总时间超时，房间ID: {room_id}")
                    break

                # 检查活动超时（30秒内没有新数据）
                if time.time() - last_activity_time > 30:
                    print(f"活动超时，30秒内没有新数据，房间ID: {room_id}")
                    break

        except Exception as e:
            print(f"处理美图请求异常: {e}")
            # 清除可能的异常状态
            self._ai_asking.pop(wxid, None)
            self._ai_questions.pop(wxid, None)
            await bot.send_text_message(wxid, f"❌ 处理失败: {str(e)}")
            return "error"

        # 处理结果
        if not has_received_data:
            # 完全没有收到数据
            if self.natural_response:
                await bot.send_text_message(wxid, random.choice(self.no_response_msgs))
            else:
                await bot.send_text_message(wxid, "❌ 没有收到AI响应，请稍后重试")
            return "error"

        elif generation_failed:
            # 生成明确失败
            if accumulated_text:
                await bot.send_text_message(wxid, accumulated_text)
            else:
                if self.natural_response:
                    await bot.send_text_message(wxid, random.choice(self.failed_msgs))
                else:
                    await bot.send_text_message(wxid, "❌ 图片生成失败，可能是内容不符合规范或其他原因")
            return "failed"

        elif not image_sent and not accumulated_text:
            # 没有图片也没有文本响应
            if self.natural_response:
                await bot.send_text_message(wxid, random.choice(self.empty_msgs))
            else:
                await bot.send_text_message(wxid, "❌ AI没有返回有效内容，请尝试重新描述或稍后重试")
            return "empty"

        else:
            # 有文本响应，发送它
            if accumulated_text:
                await bot.send_text_message(wxid, accumulated_text)

            # 如果是生图请求但没有收到图片，给出提示
            if self._is_generate_request(message) and not image_sent:
                if self.natural_response:
                    await bot.send_text_message(wxid, "💡 再详细描述一下试试？")
                else:
                    await bot.send_text_message(wxid, "💡 提示：如果需要生成图片，请尝试更详细的描述")

        return "success"



    @on_text_message
    async def handle_text(self, bot: WechatAPIClient, message: dict):
        """处理文本消息"""
        if not self.enable:
            return

        content = str(message["Content"]).strip()
        wxid = message["FromWxid"]
        user_wxid = message["SenderWxid"]

        # 检查美图模式超时
        if wxid in self._meitu_mode and self._meitu_mode[wxid]:
            if time.time() - self._last_meitu_time.get(wxid, 0) > self._meitu_timeout:
                # 清理所有相关状态
                self._cleanup_session_state(wxid)
                await bot.send_at_message(wxid, "⌛️ 美图模式已超时，已自动结束", [user_wxid])

        # 解析命令
        is_meitu_mode_command = content == "开启美图模式"
        is_direct_meitu_command = content.startswith("美图 ") and len(content) > 3
        in_meitu_mode = wxid in self._meitu_mode and self._meitu_mode[wxid]
        is_meitu_user = wxid in self._meitu_users and self._meitu_users[wxid] == user_wxid

        # 处理开启美图模式命令
        if is_meitu_mode_command:
            if in_meitu_mode and not is_meitu_user:
                await bot.send_text_message(wxid, "❌ 当前已有其他用户在使用美图模式")
                return False  # 阻止其他插件处理此消息

            # 开启美图模式
            self._meitu_mode[wxid] = True
            self._meitu_users[wxid] = user_wxid
            self._last_meitu_time[wxid] = time.time()
            self._ai_asking[wxid] = False

            await bot.send_text_message(wxid, '✅ 已进入美图AI模式，正在初始化...')
            # 发送欢迎消息
            await self._send_welcome_message(bot, wxid, user_wxid)
            return False  # 阻止其他插件处理此消息

        # 处理直接美图命令（美图 xxx）
        if is_direct_meitu_command:
            if in_meitu_mode and not is_meitu_user:
                await bot.send_text_message(wxid, "❌ 当前已有其他用户在使用美图模式")
                return False  # 阻止其他插件处理此消息

            # 提取生图内容
            prompt = content[3:].strip()  # 去掉"美图 "前缀

            # 自动进入美图模式
            self._meitu_mode[wxid] = True
            self._meitu_users[wxid] = user_wxid
            self._last_meitu_time[wxid] = time.time()
            self._ai_asking[wxid] = False

            # 直接处理生图请求
            room_id = self._get_room_id(wxid)
            await self._process_meitu_request_with_retry(bot, wxid, user_wxid, prompt, room_id)
            return False  # 阻止其他插件处理此消息

        # 处理美图模式下的消息
        if in_meitu_mode and is_meitu_user:
            self._last_meitu_time[wxid] = time.time()

            # 检查是否要退出美图模式
            if self._should_exit_meitu_mode(content):
                self._cleanup_session_state(wxid)
                await bot.send_text_message(wxid, "✅ 已退出美图AI模式")
                return False  # 阻止其他插件处理此消息

            # 处理美图模式下的请求
            await self._handle_meitu_mode_message(bot, message, content)
            return False  # 阻止其他插件处理此消息

        # 如果不是美图相关命令且不在美图模式下，忽略
        if not is_meitu_mode_command and not is_direct_meitu_command and not in_meitu_mode:
            return

    async def _handle_meitu_mode_message(self, bot: WechatAPIClient, message: dict, content: str):
        """处理美图模式下的消息"""
        wxid = message["FromWxid"]
        user_wxid = message["SenderWxid"]

        try:
            # 检查限流
            wait_time = self._check_user_limit(wxid, user_wxid)
            if wait_time > 0:
                if self.natural_response:
                    await bot.send_text_message(wxid, random.choice(self.rate_limit_msgs))
                else:
                    await bot.send_text_message(wxid, f"慢点慢点，等 {wait_time:.1f} 秒再来")
                return

            if not await self._acquire_token():
                if self.natural_response:
                    await bot.send_text_message(wxid, random.choice(self.rate_limit_msgs))
                else:
                    await bot.send_text_message(wxid, "忙不过来了，歇会儿")
                return

            room_id = self._get_room_id(wxid)

            # 如果AI正在询问，这是用户的回答
            if self._ai_asking.get(wxid, False):
                self._ai_asking[wxid] = False
                question = self._ai_questions.get(wxid, "")
                # 将用户回答作为新的请求处理，使用带重试的方法
                await self._process_meitu_request_with_retry(bot, wxid, user_wxid, content, room_id)
                return

            # 美图模式下，直接发送用户消息给AI，让AI决定如何处理
            # 如果有当前图片且用户的请求可能是编辑请求，则包含图片
            current_image = self._current_images.get(wxid)
            image_urls = []

            # 如果有当前图片且用户请求看起来像是编辑请求，则包含图片
            if current_image and self._is_edit_request(content):
                image_urls = [current_image]

            # 使用带重试的处理方法
            await self._process_meitu_request_with_retry(bot, wxid, user_wxid, content, room_id, image_urls)

        except Exception as e:
            if self.natural_response:
                await bot.send_text_message(wxid, random.choice(self.error_msgs))
            else:
                await bot.send_text_message(wxid, f"处理失败: {str(e)}")




