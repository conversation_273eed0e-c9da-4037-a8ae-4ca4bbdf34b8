"use strict";var ht=Object.create;var ae=Object.defineProperty;var pt=Object.getOwnPropertyDescriptor;var mt=Object.getOwnPropertyNames;var xt=Object.getPrototypeOf,vt=Object.prototype.hasOwnProperty;var E=(e,r)=>()=>(r||e((r={exports:{}}).exports,r),r.exports),Et=(e,r)=>{for(var t in r)ae(e,t,{get:r[t],enumerable:!0})},or=(e,r,t,n)=>{if(r&&typeof r=="object"||typeof r=="function")for(let i of mt(r))!vt.call(e,i)&&i!==t&&ae(e,i,{get:()=>r[i],enumerable:!(n=pt(r,i))||n.enumerable});return e};var Ue=(e,r,t)=>(t=e!=null?ht(xt(e)):{},or(r||!e||!e.__esModule?ae(t,"default",{value:e,enumerable:!0}):t,e)),wt=e=>or(ae({},"__esModule",{value:!0}),e);var Be=E((An,fr)=>{var T=require("buffer").Buffer,Te=[0,1996959894,3993919788,2567524794,124634137,1886057615,3915621685,2657392035,249268274,2044508324,3772115230,2547177864,162941995,2125561021,3887607047,2428444049,498536548,1789927666,4089016648,2227061214,450548861,1843258603,4107580753,2211677639,325883990,1684777152,4251122042,2321926636,335633487,1661365465,4195302755,2366115317,997073096,1281953886,3579855332,2724688242,1006888145,1258607687,3524101629,2768942443,901097722,1119000684,3686517206,2898065728,853044451,1172266101,3705015759,2882616665,651767980,1373503546,3369554304,3218104598,565507253,1454621731,3485111705,3099436303,671266974,1594198024,3322730930,2970347812,795835527,1483230225,3244367275,3060149565,1994146192,31158534,2563907772,4023717930,1907459465,112637215,2680153253,3904427059,2013776290,251722036,2517215374,3775830040,2137656763,141376813,2439277719,3865271297,1802195444,476864866,2238001368,4066508878,1812370925,453092731,2181625025,4111451223,1706088902,314042704,2344532202,4240017532,1658658271,366619977,2362670323,4224994405,1303535960,984961486,2747007092,3569037538,1256170817,1037604311,2765210733,3554079995,1131014506,879679996,2909243462,3663771856,1141124467,855842277,2852801631,3708648649,1342533948,654459306,3188396048,3373015174,1466479909,544179635,3110523913,3462522015,1591671054,702138776,2966460450,3352799412,1504918807,783551873,3082640443,3233442989,3988292384,2596254646,62317068,1957810842,3939845945,2647816111,81470997,1943803523,3814918930,2489596804,225274430,2053790376,3826175755,2466906013,167816743,2097651377,4027552580,2265490386,503444072,1762050814,4150417245,2154129355,426522225,1852507879,4275313526,2312317920,282753626,1742555852,4189708143,2394877945,397917763,1622183637,3604390888,2714866558,953729732,1340076626,3518719985,2797360999,1068828381,1219638859,3624741850,2936675148,906185462,1090812512,3747672003,2825379669,829329135,1181335161,3412177804,3160834842,628085408,1382605366,3423369109,3138078467,570562233,1426400815,3317316542,2998733608,733239954,1555261956,3268935591,3050360625,752459403,1541320221,2607071920,3965973030,1969922972,40735498,2617837225,3943577151,1913087877,83908371,2512341634,3803740692,2075208622,213261112,2463272603,3855990285,2094854071,198958881,2262029012,4057260610,1759359992,534414190,2176718541,4139329115,1873836001,414664567,2282248934,4279200368,1711684554,285281116,2405801727,4167216745,1634467795,376229701,2685067896,3608007406,1308918612,956543938,2808555105,3495958263,1231636301,1047427035,2932959818,3654703836,1088359270,936918e3,2847714899,3736837829,1202900863,817233897,3183342108,3401237130,1404277552,615818150,3134207493,3453421203,1423857449,601450431,3009837614,3294710456,1567103746,711928724,3020668471,3272380065,1510334235,755167117];typeof Int32Array!="undefined"&&(Te=new Int32Array(Te));function sr(e){if(T.isBuffer(e))return e;var r=typeof T.alloc=="function"&&typeof T.from=="function";if(typeof e=="number")return r?T.alloc(e):new T(e);if(typeof e=="string")return r?T.from(e):new T(e);throw new Error("input must be buffer, number, or string, received "+typeof e)}function gt(e){var r=sr(4);return r.writeInt32BE(e,0),r}function Ne(e,r){e=sr(e),T.isBuffer(r)&&(r=r.readUInt32BE(0));for(var t=~~r^-1,n=0;n<e.length;n++)t=Te[(t^e[n])&255]^t>>>8;return t^-1}function Me(){return gt(Ne.apply(null,arguments))}Me.signed=function(){return Ne.apply(null,arguments)};Me.unsigned=function(){return Ne.apply(null,arguments)>>>0};fr.exports=Me});var br=E(Ge=>{var ar=require("fs"),he=require("stream").Transform,ur=require("stream").PassThrough,cr=require("zlib"),We=require("util"),yt=require("events").EventEmitter,dr=Be();Ge.ZipFile=W;Ge.dateToDosDateTime=Cr;We.inherits(W,yt);function W(){this.outputStream=new ur,this.entries=[],this.outputStreamCursor=0,this.ended=!1,this.allDone=!1,this.forceZip64Eocd=!1}W.prototype.addFile=function(e,r,t){var n=this;r=pe(r,!1),t==null&&(t={});var i=new m(r,!1,t);n.entries.push(i),ar.stat(e,function(o,s){if(o)return n.emit("error",o);if(!s.isFile())return n.emit("error",new Error("not a file: "+e));i.uncompressedSize=s.size,t.mtime==null&&i.setLastModDate(s.mtime),t.mode==null&&i.setFileAttributesMode(s.mode),i.setFileDataPumpFunction(function(){var f=ar.createReadStream(e);i.state=m.FILE_DATA_IN_PROGRESS,f.on("error",function(u){n.emit("error",u)}),lr(n,i,f)}),M(n)})};W.prototype.addReadStream=function(e,r,t){var n=this;r=pe(r,!1),t==null&&(t={});var i=new m(r,!1,t);n.entries.push(i),i.setFileDataPumpFunction(function(){i.state=m.FILE_DATA_IN_PROGRESS,lr(n,i,e)}),M(n)};W.prototype.addBuffer=function(e,r,t){var n=this;if(r=pe(r,!1),e.length>1073741823)throw new Error("buffer too large: "+e.length+" > 1073741823");if(t==null&&(t={}),t.size!=null)throw new Error("options.size not allowed");var i=new m(r,!1,t);i.uncompressedSize=e.length,i.crc32=dr.unsigned(e),i.crcAndFileSizeKnown=!0,n.entries.push(i),i.compress?cr.deflateRaw(e,function(s,f){o(f)}):o(e);function o(s){i.compressedSize=s.length,i.setFileDataPumpFunction(function(){Z(n,s),Z(n,i.getDataDescriptor()),i.state=m.FILE_DATA_DONE,setImmediate(function(){M(n)})}),M(n)}};W.prototype.addEmptyDirectory=function(e,r){var t=this;if(e=pe(e,!0),r==null&&(r={}),r.size!=null)throw new Error("options.size not allowed");if(r.compress!=null)throw new Error("options.compress not allowed");var n=new m(e,!0,r);t.entries.push(n),n.setFileDataPumpFunction(function(){Z(t,n.getDataDescriptor()),n.state=m.FILE_DATA_DONE,M(t)}),M(t)};var Ct=N([80,75,5,6]);W.prototype.end=function(e,r){if(typeof e=="function"&&(r=e,e=null),e==null&&(e={}),!this.ended){if(this.ended=!0,this.finalSizeCallback=r,this.forceZip64Eocd=!!e.forceZip64Format,e.comment){if(typeof e.comment=="string"?this.comment=St(e.comment):this.comment=e.comment,this.comment.length>65535)throw new Error("comment is too large");if(te(this.comment,Ct))throw new Error("comment contains end of central directory record signature")}else this.comment=me;M(this)}};function Z(e,r){e.outputStream.write(r),e.outputStreamCursor+=r.length}function lr(e,r,t){var n=new He,i=new le,o=r.compress?new cr.DeflateRaw:new ur,s=new le;t.pipe(n).pipe(i).pipe(o).pipe(s).pipe(e.outputStream,{end:!1}),s.on("end",function(){if(r.crc32=n.crc32,r.uncompressedSize==null)r.uncompressedSize=i.byteCount;else if(r.uncompressedSize!==i.byteCount)return e.emit("error",new Error("file data stream has unexpected number of bytes"));r.compressedSize=s.byteCount,e.outputStreamCursor+=r.compressedSize,Z(e,r.getDataDescriptor()),r.state=m.FILE_DATA_DONE,M(e)})}function M(e){if(e.allDone)return;if(e.ended&&e.finalSizeCallback!=null){var r=bt(e);r!=null&&(e.finalSizeCallback(r),e.finalSizeCallback=null)}var t=n();function n(){for(var o=0;o<e.entries.length;o++){var s=e.entries[o];if(s.state<m.FILE_DATA_DONE)return s}return null}if(t!=null){if(t.state<m.READY_TO_PUMP_FILE_DATA||t.state===m.FILE_DATA_IN_PROGRESS)return;t.relativeOffsetOfLocalHeader=e.outputStreamCursor;var i=t.getLocalFileHeader();Z(e,i),t.doFileDataPump()}else e.ended&&(e.offsetOfStartOfCentralDirectory=e.outputStreamCursor,e.entries.forEach(function(o){var s=o.getCentralDirectoryRecord();Z(e,s)}),Z(e,Ft(e)),e.outputStream.end(),e.allDone=!0)}function bt(e){for(var r=0,t=0,n=0;n<e.entries.length;n++){var i=e.entries[n];if(i.compress)return-1;if(i.state>=m.READY_TO_PUMP_FILE_DATA){if(i.uncompressedSize==null)return-1}else if(i.uncompressedSize==null)return null;i.relativeOffsetOfLocalHeader=r;var o=i.useZip64Format();r+=hr+i.utf8FileName.length,r+=i.uncompressedSize,i.crcAndFileSizeKnown||(o?r+=gr:r+=wr),t+=yr+i.utf8FileName.length+i.fileComment.length,o&&(t+=Pe)}var s=0;return(e.forceZip64Eocd||e.entries.length>=65535||t>=65535||r>=4294967295)&&(s+=ce+qe),s+=de+e.comment.length,r+t+s}var ce=56,qe=20,de=22;function Ft(e,r){var t=!1,n=e.entries.length;(e.forceZip64Eocd||e.entries.length>=65535)&&(n=65535,t=!0);var i=e.outputStreamCursor-e.offsetOfStartOfCentralDirectory,o=i;(e.forceZip64Eocd||i>=4294967295)&&(o=4294967295,t=!0);var s=e.offsetOfStartOfCentralDirectory;if((e.forceZip64Eocd||e.offsetOfStartOfCentralDirectory>=4294967295)&&(s=4294967295,t=!0),r)return t?ce+qe+de:de;var f=F(de+e.comment.length);if(f.writeUInt32LE(101010256,0),f.writeUInt16LE(0,4),f.writeUInt16LE(0,6),f.writeUInt16LE(n,8),f.writeUInt16LE(n,10),f.writeUInt32LE(o,12),f.writeUInt32LE(s,16),f.writeUInt16LE(e.comment.length,20),e.comment.copy(f,22),!t)return f;var u=F(ce);u.writeUInt32LE(101075792,0),I(u,ce-12,4),u.writeUInt16LE(xr,12),u.writeUInt16LE(mr,14),u.writeUInt32LE(0,16),u.writeUInt32LE(0,20),I(u,e.entries.length,24),I(u,e.entries.length,32),I(u,i,40),I(u,e.offsetOfStartOfCentralDirectory,48);var d=F(qe);return d.writeUInt32LE(117853008,0),d.writeUInt32LE(0,4),I(d,e.outputStreamCursor,8),d.writeUInt32LE(1,16),Buffer.concat([u,d,f])}function pe(e,r){if(e==="")throw new Error("empty metadataPath");if(e=e.replace(/\\/g,"/"),/^[a-zA-Z]:/.test(e)||/^\//.test(e))throw new Error("absolute path: "+e);if(e.split("/").indexOf("..")!==-1)throw new Error("invalid relative path: "+e);var t=/\/$/.test(e);if(r)t||(e+="/");else if(t)throw new Error("file path cannot end with '/': "+e);return e}var me=F(0);function m(e,r,t){if(this.utf8FileName=N(e),this.utf8FileName.length>65535)throw new Error("utf8 file name too long. "+utf8FileName.length+" > 65535");if(this.isDirectory=r,this.state=m.WAITING_FOR_METADATA,this.setLastModDate(t.mtime!=null?t.mtime:new Date),t.mode!=null?this.setFileAttributesMode(t.mode):this.setFileAttributesMode(r?16893:33204),r?(this.crcAndFileSizeKnown=!0,this.crc32=0,this.uncompressedSize=0,this.compressedSize=0):(this.crcAndFileSizeKnown=!1,this.crc32=null,this.uncompressedSize=null,this.compressedSize=null,t.size!=null&&(this.uncompressedSize=t.size)),r?this.compress=!1:(this.compress=!0,t.compress!=null&&(this.compress=!!t.compress)),this.forceZip64Format=!!t.forceZip64Format,t.fileComment){if(typeof t.fileComment=="string"?this.fileComment=N(t.fileComment,"utf-8"):this.fileComment=t.fileComment,this.fileComment.length>65535)throw new Error("fileComment is too large")}else this.fileComment=me}m.WAITING_FOR_METADATA=0;m.READY_TO_PUMP_FILE_DATA=1;m.FILE_DATA_IN_PROGRESS=2;m.FILE_DATA_DONE=3;m.prototype.setLastModDate=function(e){var r=Cr(e);this.lastModFileTime=r.time,this.lastModFileDate=r.date};m.prototype.setFileAttributesMode=function(e){if((e&65535)!==e)throw new Error("invalid mode. expected: 0 <= "+e+" <= 65535");this.externalFileAttributes=e<<16>>>0};m.prototype.setFileDataPumpFunction=function(e){this.doFileDataPump=e,this.state=m.READY_TO_PUMP_FILE_DATA};m.prototype.useZip64Format=function(){return this.forceZip64Format||this.uncompressedSize!=null&&this.uncompressedSize>4294967294||this.compressedSize!=null&&this.compressedSize>4294967294||this.relativeOffsetOfLocalHeader!=null&&this.relativeOffsetOfLocalHeader>4294967294};var hr=30,pr=20,mr=45,xr=831,vr=2048,Er=8;m.prototype.getLocalFileHeader=function(){var e=0,r=0,t=0;this.crcAndFileSizeKnown&&(e=this.crc32,r=this.compressedSize,t=this.uncompressedSize);var n=F(hr),i=vr;return this.crcAndFileSizeKnown||(i|=Er),n.writeUInt32LE(67324752,0),n.writeUInt16LE(pr,4),n.writeUInt16LE(i,6),n.writeUInt16LE(this.getCompressionMethod(),8),n.writeUInt16LE(this.lastModFileTime,10),n.writeUInt16LE(this.lastModFileDate,12),n.writeUInt32LE(e,14),n.writeUInt32LE(r,18),n.writeUInt32LE(t,22),n.writeUInt16LE(this.utf8FileName.length,26),n.writeUInt16LE(0,28),Buffer.concat([n,this.utf8FileName])};var wr=16,gr=24;m.prototype.getDataDescriptor=function(){if(this.crcAndFileSizeKnown)return me;if(this.useZip64Format()){var e=F(gr);return e.writeUInt32LE(134695760,0),e.writeUInt32LE(this.crc32,4),I(e,this.compressedSize,8),I(e,this.uncompressedSize,16),e}else{var e=F(wr);return e.writeUInt32LE(134695760,0),e.writeUInt32LE(this.crc32,4),e.writeUInt32LE(this.compressedSize,8),e.writeUInt32LE(this.uncompressedSize,12),e}};var yr=46,Pe=28;m.prototype.getCentralDirectoryRecord=function(){var e=F(yr),r=vr;this.crcAndFileSizeKnown||(r|=Er);var t=this.compressedSize,n=this.uncompressedSize,i=this.relativeOffsetOfLocalHeader,o,s;return this.useZip64Format()?(t=4294967295,n=4294967295,i=4294967295,o=mr,s=F(Pe),s.writeUInt16LE(1,0),s.writeUInt16LE(Pe-4,2),I(s,this.uncompressedSize,4),I(s,this.compressedSize,12),I(s,this.relativeOffsetOfLocalHeader,20)):(o=pr,s=me),e.writeUInt32LE(33639248,0),e.writeUInt16LE(xr,4),e.writeUInt16LE(o,6),e.writeUInt16LE(r,8),e.writeUInt16LE(this.getCompressionMethod(),10),e.writeUInt16LE(this.lastModFileTime,12),e.writeUInt16LE(this.lastModFileDate,14),e.writeUInt32LE(this.crc32,16),e.writeUInt32LE(t,20),e.writeUInt32LE(n,24),e.writeUInt16LE(this.utf8FileName.length,28),e.writeUInt16LE(s.length,30),e.writeUInt16LE(this.fileComment.length,32),e.writeUInt16LE(0,34),e.writeUInt16LE(0,36),e.writeUInt32LE(this.externalFileAttributes,38),e.writeUInt32LE(i,42),Buffer.concat([e,this.utf8FileName,s,this.fileComment])};m.prototype.getCompressionMethod=function(){var e=0,r=8;return this.compress?r:e};function Cr(e){var r=0;r|=e.getDate()&31,r|=(e.getMonth()+1&15)<<5,r|=(e.getFullYear()-1980&127)<<9;var t=0;return t|=Math.floor(e.getSeconds()/2),t|=(e.getMinutes()&63)<<5,t|=(e.getHours()&31)<<11,{date:r,time:t}}function I(e,r,t){var n=Math.floor(r/4294967296),i=r%4294967296;e.writeUInt32LE(i,t),e.writeUInt32LE(n,t+4)}We.inherits(le,he);function le(e){he.call(this,e),this.byteCount=0}le.prototype._transform=function(e,r,t){this.byteCount+=e.length,t(null,e)};We.inherits(He,he);function He(e){he.call(this,e),this.crc32=0}He.prototype._transform=function(e,r,t){this.crc32=dr.unsigned(e,this.crc32),t(null,e)};var Ze="\0\u263A\u263B\u2665\u2666\u2663\u2660\u2022\u25D8\u25CB\u25D9\u2642\u2640\u266A\u266B\u263C\u25BA\u25C4\u2195\u203C\xB6\xA7\u25AC\u21A8\u2191\u2193\u2192\u2190\u221F\u2194\u25B2\u25BC !\"#$%&'()*+,-./**********:;<=>?@ABCDEFGHIJKLMNOPQRSTUVWXYZ[\\]^_`abcdefghijklmnopqrstuvwxyz{|}~\u2302\xC7\xFC\xE9\xE2\xE4\xE0\xE5\xE7\xEA\xEB\xE8\xEF\xEE\xEC\xC4\xC5\xC9\xE6\xC6\xF4\xF6\xF2\xFB\xF9\xFF\xD6\xDC\xA2\xA3\xA5\u20A7\u0192\xE1\xED\xF3\xFA\xF1\xD1\xAA\xBA\xBF\u2310\xAC\xBD\xBC\xA1\xAB\xBB\u2591\u2592\u2593\u2502\u2524\u2561\u2562\u2556\u2555\u2563\u2551\u2557\u255D\u255C\u255B\u2510\u2514\u2534\u252C\u251C\u2500\u253C\u255E\u255F\u255A\u2554\u2569\u2566\u2560\u2550\u256C\u2567\u2568\u2564\u2565\u2559\u2558\u2552\u2553\u256B\u256A\u2518\u250C\u2588\u2584\u258C\u2590\u2580\u03B1\xDF\u0393\u03C0\u03A3\u03C3\xB5\u03C4\u03A6\u0398\u03A9\u03B4\u221E\u03C6\u03B5\u2229\u2261\xB1\u2265\u2264\u2320\u2321\xF7\u2248\xB0\u2219\xB7\u221A\u207F\xB2\u25A0\xA0";if(Ze.length!==256)throw new Error("assertion failure");var ue=null;function St(e){if(/^[\x20-\x7e]*$/.test(e))return N(e,"utf-8");if(ue==null){ue={};for(var r=0;r<Ze.length;r++)ue[Ze[r]]=r}for(var t=F(e.length),r=0;r<e.length;r++){var n=ue[e[r]];if(n==null)throw new Error("character not encodable in CP437: "+JSON.stringify(e[r]));t[r]=n}return t}function F(e){F=r;try{return F(e)}catch{return F=t,F(e)}function r(n){return Buffer.allocUnsafe(n)}function t(n){return new Buffer(n)}}function N(e,r){N=t;try{return N(e,r)}catch{return N=n,N(e,r)}function t(i,o){return Buffer.from(i,o)}function n(i,o){return new Buffer(i,o)}}function te(e,r){te=t;try{return te(e,r)}catch{return te=n,te(e,r)}function t(i,o){return i.includes(o)}function n(i,o){for(var s=0;s<=i.length-o.length;s++)for(var f=0;;f++){if(f===o.length)return!0;if(i[s+f]!==o[f])break}return!1}}});var Lr=E((Dn,Ir)=>{Ir.exports=xe;function xe(){this.pending=0,this.max=1/0,this.listeners=[],this.waiting=[],this.error=null}xe.prototype.go=function(e){this.pending<this.max?Sr(this,e):this.waiting.push(e)};xe.prototype.wait=function(e){this.pending===0?e(this.error):this.listeners.push(e)};xe.prototype.hold=function(){return Fr(this)};function Fr(e){e.pending+=1;var r=!1;return t;function t(i){if(r)throw new Error("callback called twice");if(r=!0,e.error=e.error||i,e.pending-=1,e.waiting.length>0&&e.pending<e.max)Sr(e,e.waiting.shift());else if(e.pending===0){var o=e.listeners;e.listeners=[],o.forEach(n)}}function n(i){i(e.error)}}function Sr(e,r){r(Fr(e))}});var zr=E(ie=>{var ne=require("fs"),ve=require("util"),Ye=require("stream"),Or=Ye.Readable,$e=Ye.Writable,It=Ye.PassThrough,Lt=Lr(),Ee=require("events").EventEmitter;ie.createFromBuffer=Ot;ie.createFromFd=zt;ie.BufferSlicer=D;ie.FdSlicer=R;ve.inherits(R,Ee);function R(e,r){r=r||{},Ee.call(this),this.fd=e,this.pend=new Lt,this.pend.max=1,this.refCount=0,this.autoClose=!!r.autoClose}R.prototype.read=function(e,r,t,n,i){var o=this;o.pend.go(function(s){ne.read(o.fd,e,r,t,n,function(f,u,d){s(),i(f,u,d)})})};R.prototype.write=function(e,r,t,n,i){var o=this;o.pend.go(function(s){ne.write(o.fd,e,r,t,n,function(f,u,d){s(),i(f,u,d)})})};R.prototype.createReadStream=function(e){return new we(this,e)};R.prototype.createWriteStream=function(e){return new ge(this,e)};R.prototype.ref=function(){this.refCount+=1};R.prototype.unref=function(){var e=this;if(e.refCount-=1,e.refCount>0)return;if(e.refCount<0)throw new Error("invalid unref");e.autoClose&&ne.close(e.fd,r);function r(t){t?e.emit("error",t):e.emit("close")}};ve.inherits(we,Or);function we(e,r){r=r||{},Or.call(this,r),this.context=e,this.context.ref(),this.start=r.start||0,this.endOffset=r.end,this.pos=this.start,this.destroyed=!1}we.prototype._read=function(e){var r=this;if(!r.destroyed){var t=Math.min(r._readableState.highWaterMark,e);if(r.endOffset!=null&&(t=Math.min(t,r.endOffset-r.pos)),t<=0){r.destroyed=!0,r.push(null),r.context.unref();return}r.context.pend.go(function(n){if(r.destroyed)return n();var i=Buffer.alloc(t);ne.read(r.context.fd,i,0,t,r.pos,function(o,s){o?r.destroy(o):s===0?(r.destroyed=!0,r.push(null),r.context.unref()):(r.pos+=s,r.push(i.slice(0,s))),n()})})}};we.prototype.destroy=function(e){this.destroyed||(e=e||new Error("stream destroyed"),this.destroyed=!0,this.emit("error",e),this.context.unref())};ve.inherits(ge,$e);function ge(e,r){r=r||{},$e.call(this,r),this.context=e,this.context.ref(),this.start=r.start||0,this.endOffset=r.end==null?1/0:+r.end,this.bytesWritten=0,this.pos=this.start,this.destroyed=!1,this.on("finish",this.destroy.bind(this))}ge.prototype._write=function(e,r,t){var n=this;if(!n.destroyed){if(n.pos+e.length>n.endOffset){var i=new Error("maximum file length exceeded");i.code="ETOOBIG",n.destroy(),t(i);return}n.context.pend.go(function(o){if(n.destroyed)return o();ne.write(n.context.fd,e,0,e.length,n.pos,function(s,f){s?(n.destroy(),o(),t(s)):(n.bytesWritten+=f,n.pos+=f,n.emit("progress"),o(),t())})})}};ge.prototype.destroy=function(){this.destroyed||(this.destroyed=!0,this.context.unref())};ve.inherits(D,Ee);function D(e,r){Ee.call(this),r=r||{},this.refCount=0,this.buffer=e,this.maxChunkSize=r.maxChunkSize||Number.MAX_SAFE_INTEGER}D.prototype.read=function(e,r,t,n,i){var o=n+t,s=o-this.buffer.length,f=s>0?s:t;this.buffer.copy(e,r,n,o),setImmediate(function(){i(null,f)})};D.prototype.write=function(e,r,t,n,i){e.copy(this.buffer,n,r,r+t),setImmediate(function(){i(null,t,e)})};D.prototype.createReadStream=function(e){e=e||{};var r=new It(e);r.destroyed=!1,r.start=e.start||0,r.endOffset=e.end,r.pos=r.endOffset||this.buffer.length;for(var t=this.buffer.slice(r.start,r.pos),n=0;;){var i=n+this.maxChunkSize;if(i>=t.length){n<t.length&&r.write(t.slice(n,t.length));break}r.write(t.slice(n,i)),n=i}return r.end(),r.destroy=function(){r.destroyed=!0},r};D.prototype.createWriteStream=function(e){var r=this;e=e||{};var t=new $e(e);return t.start=e.start||0,t.endOffset=e.end==null?this.buffer.length:+e.end,t.bytesWritten=0,t.pos=t.start,t.destroyed=!1,t._write=function(n,i,o){if(!t.destroyed){var s=t.pos+n.length;if(s>t.endOffset){var f=new Error("maximum file length exceeded");f.code="ETOOBIG",t.destroyed=!0,o(f);return}n.copy(r.buffer,t.pos,0,n.length),t.bytesWritten+=n.length,t.pos=s,t.emit("progress"),o()}},t.destroy=function(){t.destroyed=!0},t};D.prototype.ref=function(){this.refCount+=1};D.prototype.unref=function(){if(this.refCount-=1,this.refCount<0)throw new Error("invalid unref")};function Ot(e,r){return new D(e,r)}function zt(e,r){return new R(e,r)}});var Xe=E(A=>{var je=require("fs"),_t=require("zlib"),_r=zr(),At=Be(),be=require("util"),Fe=require("events").EventEmitter,Ar=require("stream").Transform,Ve=require("stream").PassThrough,Rt=require("stream").Writable;A.open=Dt;A.fromFd=Rr;A.fromBuffer=Ut;A.fromRandomAccessReader=Ke;A.dosDateTimeToDate=Ur;A.validateFileName=Tr;A.ZipFile=B;A.Entry=oe;A.RandomAccessReader=q;function Dt(e,r,t){typeof r=="function"&&(t=r,r=null),r==null&&(r={}),r.autoClose==null&&(r.autoClose=!0),r.lazyEntries==null&&(r.lazyEntries=!1),r.decodeStrings==null&&(r.decodeStrings=!0),r.validateEntrySizes==null&&(r.validateEntrySizes=!0),r.strictFileNames==null&&(r.strictFileNames=!1),t==null&&(t=Ce),je.open(e,"r",function(n,i){if(n)return t(n);Rr(i,r,function(o,s){o&&je.close(i,Ce),t(o,s)})})}function Rr(e,r,t){typeof r=="function"&&(t=r,r=null),r==null&&(r={}),r.autoClose==null&&(r.autoClose=!1),r.lazyEntries==null&&(r.lazyEntries=!1),r.decodeStrings==null&&(r.decodeStrings=!0),r.validateEntrySizes==null&&(r.validateEntrySizes=!0),r.strictFileNames==null&&(r.strictFileNames=!1),t==null&&(t=Ce),je.fstat(e,function(n,i){if(n)return t(n);var o=_r.createFromFd(e,{autoClose:!0});Ke(o,i.size,r,t)})}function Ut(e,r,t){typeof r=="function"&&(t=r,r=null),r==null&&(r={}),r.autoClose=!1,r.lazyEntries==null&&(r.lazyEntries=!1),r.decodeStrings==null&&(r.decodeStrings=!0),r.validateEntrySizes==null&&(r.validateEntrySizes=!0),r.strictFileNames==null&&(r.strictFileNames=!1);var n=_r.createFromBuffer(e,{maxChunkSize:65536});Ke(n,e.length,r,t)}function Ke(e,r,t,n){typeof t=="function"&&(n=t,t=null),t==null&&(t={}),t.autoClose==null&&(t.autoClose=!0),t.lazyEntries==null&&(t.lazyEntries=!1),t.decodeStrings==null&&(t.decodeStrings=!0);var i=!!t.decodeStrings;if(t.validateEntrySizes==null&&(t.validateEntrySizes=!0),t.strictFileNames==null&&(t.strictFileNames=!1),n==null&&(n=Ce),typeof r!="number")throw new Error("expected totalSize parameter to be a number");if(r>Number.MAX_SAFE_INTEGER)throw new Error("zip file too large. only file sizes up to 2^52 are supported due to JavaScript's Number type being an IEEE 754 double.");e.ref();var o=22,s=65535,f=Math.min(o+s,r),u=_(f),d=r-u.length;$(e,u,0,f,d,function(a){if(a)return n(a);for(var c=f-o;c>=0;c-=1)if(u.readUInt32LE(c)===101010256){var l=u.slice(c),x=l.readUInt16LE(4);if(x!==0)return n(new Error("multi-disk zip files are not supported: found disk number: "+x));var g=l.readUInt16LE(10),p=l.readUInt32LE(16),h=l.readUInt16LE(20),v=l.length-o;if(h!==v)return n(new Error("invalid comment length. expected: "+v+". found: "+h));var w=i?ye(l,22,l.length,!1):l.slice(22);if(!(g===65535||p===4294967295))return n(null,new B(e,p,r,g,w,t.autoClose,t.lazyEntries,i,t.validateEntrySizes,t.strictFileNames));var b=_(20),U=d+c-b.length;$(e,b,0,b.length,U,function(Y){if(Y)return n(Y);if(b.readUInt32LE(0)!==117853008)return n(new Error("invalid zip64 end of central directory locator signature"));var ee=j(b,8),P=_(56);$(e,P,0,P.length,ee,function(re){return re?n(re):P.readUInt32LE(0)!==101075792?n(new Error("invalid zip64 end of central directory record signature")):(g=j(P,32),p=j(P,48),n(null,new B(e,p,r,g,w,t.autoClose,t.lazyEntries,i,t.validateEntrySizes,t.strictFileNames)))})});return}n(new Error("end of central directory record signature not found"))})}be.inherits(B,Fe);function B(e,r,t,n,i,o,s,f,u,d){var a=this;Fe.call(a),a.reader=e,a.reader.on("error",function(c){Dr(a,c)}),a.reader.once("close",function(){a.emit("close")}),a.readEntryCursor=r,a.fileSize=t,a.entryCount=n,a.comment=i,a.entriesRead=0,a.autoClose=!!o,a.lazyEntries=!!s,a.decodeStrings=!!f,a.validateEntrySizes=!!u,a.strictFileNames=!!d,a.isOpen=!0,a.emittedError=!1,a.lazyEntries||a._readEntry()}B.prototype.close=function(){this.isOpen&&(this.isOpen=!1,this.reader.unref())};function L(e,r){e.autoClose&&e.close(),Dr(e,r)}function Dr(e,r){e.emittedError||(e.emittedError=!0,e.emit("error",r))}B.prototype.readEntry=function(){if(!this.lazyEntries)throw new Error("readEntry() called without lazyEntries:true");this._readEntry()};B.prototype._readEntry=function(){var e=this;if(e.entryCount===e.entriesRead){setImmediate(function(){e.autoClose&&e.close(),!e.emittedError&&e.emit("end")});return}if(!e.emittedError){var r=_(46);$(e.reader,r,0,r.length,e.readEntryCursor,function(t){if(t)return L(e,t);if(!e.emittedError){var n=new oe,i=r.readUInt32LE(0);if(i!==33639248)return L(e,new Error("invalid central directory file header signature: 0x"+i.toString(16)));if(n.versionMadeBy=r.readUInt16LE(4),n.versionNeededToExtract=r.readUInt16LE(6),n.generalPurposeBitFlag=r.readUInt16LE(8),n.compressionMethod=r.readUInt16LE(10),n.lastModFileTime=r.readUInt16LE(12),n.lastModFileDate=r.readUInt16LE(14),n.crc32=r.readUInt32LE(16),n.compressedSize=r.readUInt32LE(20),n.uncompressedSize=r.readUInt32LE(24),n.fileNameLength=r.readUInt16LE(28),n.extraFieldLength=r.readUInt16LE(30),n.fileCommentLength=r.readUInt16LE(32),n.internalFileAttributes=r.readUInt16LE(36),n.externalFileAttributes=r.readUInt32LE(38),n.relativeOffsetOfLocalHeader=r.readUInt32LE(42),n.generalPurposeBitFlag&64)return L(e,new Error("strong encryption is not supported"));e.readEntryCursor+=46,r=_(n.fileNameLength+n.extraFieldLength+n.fileCommentLength),$(e.reader,r,0,r.length,e.readEntryCursor,function(o){if(o)return L(e,o);if(!e.emittedError){var s=(n.generalPurposeBitFlag&2048)!==0;n.fileName=e.decodeStrings?ye(r,0,n.fileNameLength,s):r.slice(0,n.fileNameLength);var f=n.fileNameLength+n.extraFieldLength,u=r.slice(n.fileNameLength,f);n.extraFields=[];for(var d=0;d<u.length-3;){var a=u.readUInt16LE(d+0),c=u.readUInt16LE(d+2),l=d+4,x=l+c;if(x>u.length)return L(e,new Error("extra field length exceeds extra field buffer size"));var g=_(c);u.copy(g,0,l,x),n.extraFields.push({id:a,data:g}),d=x}if(n.fileComment=e.decodeStrings?ye(r,f,f+n.fileCommentLength,s):r.slice(f,f+n.fileCommentLength),n.comment=n.fileComment,e.readEntryCursor+=r.length,e.entriesRead+=1,n.uncompressedSize===4294967295||n.compressedSize===4294967295||n.relativeOffsetOfLocalHeader===4294967295){for(var p=null,d=0;d<n.extraFields.length;d++){var h=n.extraFields[d];if(h.id===1){p=h.data;break}}if(p==null)return L(e,new Error("expected zip64 extended information extra field"));var v=0;if(n.uncompressedSize===4294967295){if(v+8>p.length)return L(e,new Error("zip64 extended information extra field does not include uncompressed size"));n.uncompressedSize=j(p,v),v+=8}if(n.compressedSize===4294967295){if(v+8>p.length)return L(e,new Error("zip64 extended information extra field does not include compressed size"));n.compressedSize=j(p,v),v+=8}if(n.relativeOffsetOfLocalHeader===4294967295){if(v+8>p.length)return L(e,new Error("zip64 extended information extra field does not include relative header offset"));n.relativeOffsetOfLocalHeader=j(p,v),v+=8}}if(e.decodeStrings)for(var d=0;d<n.extraFields.length;d++){var h=n.extraFields[d];if(h.id===28789){if(h.data.length<6||h.data.readUInt8(0)!==1)continue;var w=h.data.readUInt32LE(1);if(At.unsigned(r.slice(0,n.fileNameLength))!==w)continue;n.fileName=ye(h.data,5,h.data.length,!0);break}}if(e.validateEntrySizes&&n.compressionMethod===0){var b=n.uncompressedSize;if(n.isEncrypted()&&(b+=12),n.compressedSize!==b){var U="compressed/uncompressed size mismatch for stored file: "+n.compressedSize+" != "+n.uncompressedSize;return L(e,new Error(U))}}if(e.decodeStrings){e.strictFileNames||(n.fileName=n.fileName.replace(/\\/g,"/"));var Y=Tr(n.fileName,e.validateFileNameOptions);if(Y!=null)return L(e,new Error(Y))}e.emit("entry",n),e.lazyEntries||e._readEntry()}})}})}};B.prototype.openReadStream=function(e,r,t){var n=this,i=0,o=e.compressedSize;if(t==null)t=r,r={};else{if(r.decrypt!=null){if(!e.isEncrypted())throw new Error("options.decrypt can only be specified for encrypted entries");if(r.decrypt!==!1)throw new Error("invalid options.decrypt value: "+r.decrypt);if(e.isCompressed()&&r.decompress!==!1)throw new Error("entry is encrypted and compressed, and options.decompress !== false")}if(r.decompress!=null){if(!e.isCompressed())throw new Error("options.decompress can only be specified for compressed entries");if(!(r.decompress===!1||r.decompress===!0))throw new Error("invalid options.decompress value: "+r.decompress)}if(r.start!=null||r.end!=null){if(e.isCompressed()&&r.decompress!==!1)throw new Error("start/end range not allowed for compressed entry without options.decompress === false");if(e.isEncrypted()&&r.decrypt!==!1)throw new Error("start/end range not allowed for encrypted entry without options.decrypt === false")}if(r.start!=null){if(i=r.start,i<0)throw new Error("options.start < 0");if(i>e.compressedSize)throw new Error("options.start > entry.compressedSize")}if(r.end!=null){if(o=r.end,o<0)throw new Error("options.end < 0");if(o>e.compressedSize)throw new Error("options.end > entry.compressedSize");if(o<i)throw new Error("options.end < options.start")}}if(!n.isOpen)return t(new Error("closed"));if(e.isEncrypted()&&r.decrypt!==!1)return t(new Error("entry is encrypted, and options.decrypt !== false"));n.reader.ref();var s=_(30);$(n.reader,s,0,s.length,e.relativeOffsetOfLocalHeader,function(f){try{if(f)return t(f);var u=s.readUInt32LE(0);if(u!==67324752)return t(new Error("invalid local file header signature: 0x"+u.toString(16)));var d=s.readUInt16LE(26),a=s.readUInt16LE(28),c=e.relativeOffsetOfLocalHeader+s.length+d+a,l;if(e.compressionMethod===0)l=!1;else if(e.compressionMethod===8)l=r.decompress!=null?r.decompress:!0;else return t(new Error("unsupported compression method: "+e.compressionMethod));var x=c,g=x+e.compressedSize;if(e.compressedSize!==0&&g>n.fileSize)return t(new Error("file data overflows file bounds: "+x+" + "+e.compressedSize+" > "+n.fileSize));var p=n.reader.createReadStream({start:x+i,end:x+o}),h=p;if(l){var v=!1,w=_t.createInflateRaw();p.on("error",function(b){setImmediate(function(){v||w.emit("error",b)})}),p.pipe(w),n.validateEntrySizes?(h=new se(e.uncompressedSize),w.on("error",function(b){setImmediate(function(){v||h.emit("error",b)})}),w.pipe(h)):h=w,h.destroy=function(){v=!0,w!==h&&w.unpipe(h),p.unpipe(w),p.destroy()}}t(null,h)}finally{n.reader.unref()}})};function oe(){}oe.prototype.getLastModDate=function(){return Ur(this.lastModFileDate,this.lastModFileTime)};oe.prototype.isEncrypted=function(){return(this.generalPurposeBitFlag&1)!==0};oe.prototype.isCompressed=function(){return this.compressionMethod===8};function Ur(e,r){var t=e&31,n=(e>>5&15)-1,i=(e>>9&127)+1980,o=0,s=(r&31)*2,f=r>>5&63,u=r>>11&31;return new Date(i,n,t,u,f,s,o)}function Tr(e){return e.indexOf("\\")!==-1?"invalid characters in fileName: "+e:/^[a-zA-Z]:/.test(e)||/^\//.test(e)?"absolute path: "+e:e.split("/").indexOf("..")!==-1?"invalid relative path: "+e:null}function $(e,r,t,n,i,o){if(n===0)return setImmediate(function(){o(null,_(0))});e.read(r,t,n,i,function(s,f){if(s)return o(s);if(f<n)return o(new Error("unexpected EOF"));o()})}be.inherits(se,Ar);function se(e){Ar.call(this),this.actualByteCount=0,this.expectedByteCount=e}se.prototype._transform=function(e,r,t){if(this.actualByteCount+=e.length,this.actualByteCount>this.expectedByteCount){var n="too many bytes in the stream. expected "+this.expectedByteCount+". got at least "+this.actualByteCount;return t(new Error(n))}t(null,e)};se.prototype._flush=function(e){if(this.actualByteCount<this.expectedByteCount){var r="not enough bytes in the stream. expected "+this.expectedByteCount+". got only "+this.actualByteCount;return e(new Error(r))}e()};be.inherits(q,Fe);function q(){Fe.call(this),this.refCount=0}q.prototype.ref=function(){this.refCount+=1};q.prototype.unref=function(){var e=this;if(e.refCount-=1,e.refCount>0)return;if(e.refCount<0)throw new Error("invalid unref");e.close(r);function r(t){if(t)return e.emit("error",t);e.emit("close")}};q.prototype.createReadStream=function(e){var r=e.start,t=e.end;if(r===t){var n=new Ve;return setImmediate(function(){n.end()}),n}var i=this._readStreamForRange(r,t),o=!1,s=new Se(this);i.on("error",function(u){setImmediate(function(){o||s.emit("error",u)})}),s.destroy=function(){i.unpipe(s),s.unref(),i.destroy()};var f=new se(t-r);return s.on("error",function(u){setImmediate(function(){o||f.emit("error",u)})}),f.destroy=function(){o=!0,s.unpipe(f),s.destroy()},i.pipe(s).pipe(f)};q.prototype._readStreamForRange=function(e,r){throw new Error("not implemented")};q.prototype.read=function(e,r,t,n,i){var o=this.createReadStream({start:n,end:n+t}),s=new Rt,f=0;s._write=function(u,d,a){u.copy(e,r+f,0,u.length),f+=u.length,a()},s.on("finish",i),o.on("error",function(u){i(u)}),o.pipe(s)};q.prototype.close=function(e){setImmediate(e)};be.inherits(Se,Ve);function Se(e){Ve.call(this),this.context=e,this.context.ref(),this.unreffedYet=!1}Se.prototype._flush=function(e){this.unref(),e()};Se.prototype.unref=function(e){this.unreffedYet||(this.unreffedYet=!0,this.context.unref())};var Tt="\0\u263A\u263B\u2665\u2666\u2663\u2660\u2022\u25D8\u25CB\u25D9\u2642\u2640\u266A\u266B\u263C\u25BA\u25C4\u2195\u203C\xB6\xA7\u25AC\u21A8\u2191\u2193\u2192\u2190\u221F\u2194\u25B2\u25BC !\"#$%&'()*+,-./**********:;<=>?@ABCDEFGHIJKLMNOPQRSTUVWXYZ[\\]^_`abcdefghijklmnopqrstuvwxyz{|}~\u2302\xC7\xFC\xE9\xE2\xE4\xE0\xE5\xE7\xEA\xEB\xE8\xEF\xEE\xEC\xC4\xC5\xC9\xE6\xC6\xF4\xF6\xF2\xFB\xF9\xFF\xD6\xDC\xA2\xA3\xA5\u20A7\u0192\xE1\xED\xF3\xFA\xF1\xD1\xAA\xBA\xBF\u2310\xAC\xBD\xBC\xA1\xAB\xBB\u2591\u2592\u2593\u2502\u2524\u2561\u2562\u2556\u2555\u2563\u2551\u2557\u255D\u255C\u255B\u2510\u2514\u2534\u252C\u251C\u2500\u253C\u255E\u255F\u255A\u2554\u2569\u2566\u2560\u2550\u256C\u2567\u2568\u2564\u2565\u2559\u2558\u2552\u2553\u256B\u256A\u2518\u250C\u2588\u2584\u258C\u2590\u2580\u03B1\xDF\u0393\u03C0\u03A3\u03C3\xB5\u03C4\u03A6\u0398\u03A9\u03B4\u221E\u03C6\u03B5\u2229\u2261\xB1\u2265\u2264\u2320\u2321\xF7\u2248\xB0\u2219\xB7\u221A\u207F\xB2\u25A0\xA0";function ye(e,r,t,n){if(n)return e.toString("utf8",r,t);for(var i="",o=r;o<t;o++)i+=Tt[e[o]];return i}function j(e,r){var t=e.readUInt32LE(r),n=e.readUInt32LE(r+4);return n*4294967296+t}var _;typeof Buffer.allocUnsafe=="function"?_=function(e){return Buffer.allocUnsafe(e)}:_=function(e){return new Buffer(e)};function Ce(e){if(e)throw e}});var Mr=E((Nn,Nr)=>{var V=1e3,K=V*60,X=K*60,H=X*24,Nt=H*7,Mt=H*365.25;Nr.exports=function(e,r){r=r||{};var t=typeof e;if(t==="string"&&e.length>0)return Bt(e);if(t==="number"&&isFinite(e))return r.long?Pt(e):qt(e);throw new Error("val is not a non-empty string or a valid number. val="+JSON.stringify(e))};function Bt(e){if(e=String(e),!(e.length>100)){var r=/^(-?(?:\d+)?\.?\d+) *(milliseconds?|msecs?|ms|seconds?|secs?|s|minutes?|mins?|m|hours?|hrs?|h|days?|d|weeks?|w|years?|yrs?|y)?$/i.exec(e);if(r){var t=parseFloat(r[1]),n=(r[2]||"ms").toLowerCase();switch(n){case"years":case"year":case"yrs":case"yr":case"y":return t*Mt;case"weeks":case"week":case"w":return t*Nt;case"days":case"day":case"d":return t*H;case"hours":case"hour":case"hrs":case"hr":case"h":return t*X;case"minutes":case"minute":case"mins":case"min":case"m":return t*K;case"seconds":case"second":case"secs":case"sec":case"s":return t*V;case"milliseconds":case"millisecond":case"msecs":case"msec":case"ms":return t;default:return}}}}function qt(e){var r=Math.abs(e);return r>=H?Math.round(e/H)+"d":r>=X?Math.round(e/X)+"h":r>=K?Math.round(e/K)+"m":r>=V?Math.round(e/V)+"s":e+"ms"}function Pt(e){var r=Math.abs(e);return r>=H?Ie(e,r,H,"day"):r>=X?Ie(e,r,X,"hour"):r>=K?Ie(e,r,K,"minute"):r>=V?Ie(e,r,V,"second"):e+" ms"}function Ie(e,r,t,n){var i=r>=t*1.5;return Math.round(e/t)+" "+n+(i?"s":"")}});var Je=E((Mn,Br)=>{function Zt(e){t.debug=t,t.default=t,t.coerce=u,t.disable=o,t.enable=i,t.enabled=s,t.humanize=Mr(),t.destroy=d,Object.keys(e).forEach(a=>{t[a]=e[a]}),t.names=[],t.skips=[],t.formatters={};function r(a){let c=0;for(let l=0;l<a.length;l++)c=(c<<5)-c+a.charCodeAt(l),c|=0;return t.colors[Math.abs(c)%t.colors.length]}t.selectColor=r;function t(a){let c,l=null,x,g;function p(...h){if(!p.enabled)return;let v=p,w=Number(new Date),b=w-(c||w);v.diff=b,v.prev=c,v.curr=w,c=w,h[0]=t.coerce(h[0]),typeof h[0]!="string"&&h.unshift("%O");let U=0;h[0]=h[0].replace(/%([a-zA-Z%])/g,(ee,P)=>{if(ee==="%%")return"%";U++;let re=t.formatters[P];if(typeof re=="function"){let lt=h[U];ee=re.call(v,lt),h.splice(U,1),U--}return ee}),t.formatArgs.call(v,h),(v.log||t.log).apply(v,h)}return p.namespace=a,p.useColors=t.useColors(),p.color=t.selectColor(a),p.extend=n,p.destroy=t.destroy,Object.defineProperty(p,"enabled",{enumerable:!0,configurable:!1,get:()=>l!==null?l:(x!==t.namespaces&&(x=t.namespaces,g=t.enabled(a)),g),set:h=>{l=h}}),typeof t.init=="function"&&t.init(p),p}function n(a,c){let l=t(this.namespace+(typeof c=="undefined"?":":c)+a);return l.log=this.log,l}function i(a){t.save(a),t.namespaces=a,t.names=[],t.skips=[];let c,l=(typeof a=="string"?a:"").split(/[\s,]+/),x=l.length;for(c=0;c<x;c++)l[c]&&(a=l[c].replace(/\*/g,".*?"),a[0]==="-"?t.skips.push(new RegExp("^"+a.slice(1)+"$")):t.names.push(new RegExp("^"+a+"$")))}function o(){let a=[...t.names.map(f),...t.skips.map(f).map(c=>"-"+c)].join(",");return t.enable(""),a}function s(a){if(a[a.length-1]==="*")return!0;let c,l;for(c=0,l=t.skips.length;c<l;c++)if(t.skips[c].test(a))return!1;for(c=0,l=t.names.length;c<l;c++)if(t.names[c].test(a))return!0;return!1}function f(a){return a.toString().substring(2,a.toString().length-2).replace(/\.\*\?$/,"*")}function u(a){return a instanceof Error?a.stack||a.message:a}function d(){console.warn("Instance method `debug.destroy()` is deprecated and no longer does anything. It will be removed in the next major version of `debug`.")}return t.enable(t.load()),t}Br.exports=Zt});var qr=E((S,Le)=>{S.formatArgs=Ht;S.save=Gt;S.load=Yt;S.useColors=Wt;S.storage=$t();S.destroy=(()=>{let e=!1;return()=>{e||(e=!0,console.warn("Instance method `debug.destroy()` is deprecated and no longer does anything. It will be removed in the next major version of `debug`."))}})();S.colors=["#0000CC","#0000FF","#0033CC","#0033FF","#0066CC","#0066FF","#0099CC","#0099FF","#00CC00","#00CC33","#00CC66","#00CC99","#00CCCC","#00CCFF","#3300CC","#3300FF","#3333CC","#3333FF","#3366CC","#3366FF","#3399CC","#3399FF","#33CC00","#33CC33","#33CC66","#33CC99","#33CCCC","#33CCFF","#6600CC","#6600FF","#6633CC","#6633FF","#66CC00","#66CC33","#9900CC","#9900FF","#9933CC","#9933FF","#99CC00","#99CC33","#CC0000","#CC0033","#CC0066","#CC0099","#CC00CC","#CC00FF","#CC3300","#CC3333","#CC3366","#CC3399","#CC33CC","#CC33FF","#CC6600","#CC6633","#CC9900","#CC9933","#CCCC00","#CCCC33","#FF0000","#FF0033","#FF0066","#FF0099","#FF00CC","#FF00FF","#FF3300","#FF3333","#FF3366","#FF3399","#FF33CC","#FF33FF","#FF6600","#FF6633","#FF9900","#FF9933","#FFCC00","#FFCC33"];function Wt(){return typeof window!="undefined"&&window.process&&(window.process.type==="renderer"||window.process.__nwjs)?!0:typeof navigator!="undefined"&&navigator.userAgent&&navigator.userAgent.toLowerCase().match(/(edge|trident)\/(\d+)/)?!1:typeof document!="undefined"&&document.documentElement&&document.documentElement.style&&document.documentElement.style.WebkitAppearance||typeof window!="undefined"&&window.console&&(window.console.firebug||window.console.exception&&window.console.table)||typeof navigator!="undefined"&&navigator.userAgent&&navigator.userAgent.toLowerCase().match(/firefox\/(\d+)/)&&parseInt(RegExp.$1,10)>=31||typeof navigator!="undefined"&&navigator.userAgent&&navigator.userAgent.toLowerCase().match(/applewebkit\/(\d+)/)}function Ht(e){if(e[0]=(this.useColors?"%c":"")+this.namespace+(this.useColors?" %c":" ")+e[0]+(this.useColors?"%c ":" ")+"+"+Le.exports.humanize(this.diff),!this.useColors)return;let r="color: "+this.color;e.splice(1,0,r,"color: inherit");let t=0,n=0;e[0].replace(/%[a-zA-Z%]/g,i=>{i!=="%%"&&(t++,i==="%c"&&(n=t))}),e.splice(n,0,r)}S.log=console.debug||console.log||(()=>{});function Gt(e){try{e?S.storage.setItem("debug",e):S.storage.removeItem("debug")}catch{}}function Yt(){let e;try{e=S.storage.getItem("debug")}catch{}return!e&&typeof process!="undefined"&&"env"in process&&(e=process.env.DEBUG),e}function $t(){try{return localStorage}catch{}}Le.exports=Je()(S);var{formatters:jt}=Le.exports;jt.j=function(e){try{return JSON.stringify(e)}catch(r){return"[UnexpectedJSONParseError]: "+r.message}}});var Zr=E((Bn,Pr)=>{"use strict";Pr.exports=(e,r)=>{r=r||process.argv;let t=e.startsWith("-")?"":e.length===1?"-":"--",n=r.indexOf(t+e),i=r.indexOf("--");return n!==-1&&(i===-1?!0:n<i)}});var Hr=E((qn,Wr)=>{"use strict";var Vt=require("os"),O=Zr(),C=process.env,J;O("no-color")||O("no-colors")||O("color=false")?J=!1:(O("color")||O("colors")||O("color=true")||O("color=always"))&&(J=!0);"FORCE_COLOR"in C&&(J=C.FORCE_COLOR.length===0||parseInt(C.FORCE_COLOR,10)!==0);function Kt(e){return e===0?!1:{level:e,hasBasic:!0,has256:e>=2,has16m:e>=3}}function Xt(e){if(J===!1)return 0;if(O("color=16m")||O("color=full")||O("color=truecolor"))return 3;if(O("color=256"))return 2;if(e&&!e.isTTY&&J!==!0)return 0;let r=J?1:0;if(process.platform==="win32"){let t=Vt.release().split(".");return Number(process.versions.node.split(".")[0])>=8&&Number(t[0])>=10&&Number(t[2])>=10586?Number(t[2])>=14931?3:2:1}if("CI"in C)return["TRAVIS","CIRCLECI","APPVEYOR","GITLAB_CI"].some(t=>t in C)||C.CI_NAME==="codeship"?1:r;if("TEAMCITY_VERSION"in C)return/^(9\.(0*[1-9]\d*)\.|\d{2,}\.)/.test(C.TEAMCITY_VERSION)?1:0;if(C.COLORTERM==="truecolor")return 3;if("TERM_PROGRAM"in C){let t=parseInt((C.TERM_PROGRAM_VERSION||"").split(".")[0],10);switch(C.TERM_PROGRAM){case"iTerm.app":return t>=3?3:2;case"Apple_Terminal":return 2}}return/-256(color)?$/i.test(C.TERM)?2:/^screen|^xterm|^vt100|^vt220|^rxvt|color|ansi|cygwin|linux/i.test(C.TERM)||"COLORTERM"in C?1:(C.TERM==="dumb",r)}function Qe(e){let r=Xt(e);return Kt(r)}Wr.exports={supportsColor:Qe,stdout:Qe(process.stdout),stderr:Qe(process.stderr)}});var Yr=E((y,ze)=>{var Jt=require("tty"),Oe=require("util");y.init=on;y.log=rn;y.formatArgs=kt;y.save=tn;y.load=nn;y.useColors=Qt;y.destroy=Oe.deprecate(()=>{},"Instance method `debug.destroy()` is deprecated and no longer does anything. It will be removed in the next major version of `debug`.");y.colors=[6,2,3,4,5,1];try{let e=Hr();e&&(e.stderr||e).level>=2&&(y.colors=[20,21,26,27,32,33,38,39,40,41,42,43,44,45,56,57,62,63,68,69,74,75,76,77,78,79,80,81,92,93,98,99,112,113,128,129,134,135,148,149,160,161,162,163,164,165,166,167,168,169,170,171,172,173,178,179,184,185,196,197,198,199,200,201,202,203,204,205,206,207,208,209,214,215,220,221])}catch{}y.inspectOpts=Object.keys(process.env).filter(e=>/^debug_/i.test(e)).reduce((e,r)=>{let t=r.substring(6).toLowerCase().replace(/_([a-z])/g,(i,o)=>o.toUpperCase()),n=process.env[r];return/^(yes|on|true|enabled)$/i.test(n)?n=!0:/^(no|off|false|disabled)$/i.test(n)?n=!1:n==="null"?n=null:n=Number(n),e[t]=n,e},{});function Qt(){return"colors"in y.inspectOpts?!!y.inspectOpts.colors:Jt.isatty(process.stderr.fd)}function kt(e){let{namespace:r,useColors:t}=this;if(t){let n=this.color,i="\x1B[3"+(n<8?n:"8;5;"+n),o=`  ${i};1m${r} \x1B[0m`;e[0]=o+e[0].split(`
`).join(`
`+o),e.push(i+"m+"+ze.exports.humanize(this.diff)+"\x1B[0m")}else e[0]=en()+r+" "+e[0]}function en(){return y.inspectOpts.hideDate?"":new Date().toISOString()+" "}function rn(...e){return process.stderr.write(Oe.format(...e)+`
`)}function tn(e){e?process.env.DEBUG=e:delete process.env.DEBUG}function nn(){return process.env.DEBUG}function on(e){e.inspectOpts={};let r=Object.keys(y.inspectOpts);for(let t=0;t<r.length;t++)e.inspectOpts[r[t]]=y.inspectOpts[r[t]]}ze.exports=Je()(y);var{formatters:Gr}=ze.exports;Gr.o=function(e){return this.inspectOpts.colors=this.useColors,Oe.inspect(e,this.inspectOpts).split(`
`).map(r=>r.trim()).join(" ")};Gr.O=function(e){return this.inspectOpts.colors=this.useColors,Oe.inspect(e,this.inspectOpts)}});var $r=E((Pn,ke)=>{typeof process=="undefined"||process.type==="renderer"||process.browser===!0||process.__nwjs?ke.exports=qr():ke.exports=Yr()});var Kr=E((Zn,Vr)=>{Vr.exports=jr;function jr(e,r){if(e&&r)return jr(e)(r);if(typeof e!="function")throw new TypeError("need wrapper function");return Object.keys(e).forEach(function(n){t[n]=e[n]}),t;function t(){for(var n=new Array(arguments.length),i=0;i<n.length;i++)n[i]=arguments[i];var o=e.apply(this,n),s=n[n.length-1];return typeof o=="function"&&o!==s&&Object.keys(s).forEach(function(f){o[f]=s[f]}),o}}});var rr=E((Wn,er)=>{var Xr=Kr();er.exports=Xr(_e);er.exports.strict=Xr(Jr);_e.proto=_e(function(){Object.defineProperty(Function.prototype,"once",{value:function(){return _e(this)},configurable:!0}),Object.defineProperty(Function.prototype,"onceStrict",{value:function(){return Jr(this)},configurable:!0})});function _e(e){var r=function(){return r.called?r.value:(r.called=!0,r.value=e.apply(this,arguments))};return r.called=!1,r}function Jr(e){var r=function(){if(r.called)throw new Error(r.onceError);return r.called=!0,r.value=e.apply(this,arguments)},t=e.name||"Function wrapped with `once`";return r.onceError=t+" shouldn't be called more than once",r.called=!1,r}});var et=E((Hn,kr)=>{var sn=rr(),fn=function(){},an=function(e){return e.setHeader&&typeof e.abort=="function"},un=function(e){return e.stdio&&Array.isArray(e.stdio)&&e.stdio.length===3},Qr=function(e,r,t){if(typeof r=="function")return Qr(e,null,r);r||(r={}),t=sn(t||fn);var n=e._writableState,i=e._readableState,o=r.readable||r.readable!==!1&&e.readable,s=r.writable||r.writable!==!1&&e.writable,f=!1,u=function(){e.writable||d()},d=function(){s=!1,o||t.call(e)},a=function(){o=!1,s||t.call(e)},c=function(h){t.call(e,h?new Error("exited with error code: "+h):null)},l=function(h){t.call(e,h)},x=function(){process.nextTick(g)},g=function(){if(!f){if(o&&!(i&&i.ended&&!i.destroyed))return t.call(e,new Error("premature close"));if(s&&!(n&&n.ended&&!n.destroyed))return t.call(e,new Error("premature close"))}},p=function(){e.req.on("finish",d)};return an(e)?(e.on("complete",d),e.on("abort",x),e.req?p():e.on("request",p)):s&&!n&&(e.on("end",u),e.on("close",u)),un(e)&&e.on("exit",c),e.on("end",a),e.on("finish",d),r.error!==!1&&e.on("error",l),e.on("close",x),function(){f=!0,e.removeListener("complete",d),e.removeListener("abort",x),e.removeListener("request",p),e.req&&e.req.removeListener("finish",d),e.removeListener("end",u),e.removeListener("close",u),e.removeListener("finish",d),e.removeListener("exit",c),e.removeListener("end",a),e.removeListener("error",l),e.removeListener("close",x)}};kr.exports=Qr});var nt=E((Gn,tt)=>{var cn=rr(),dn=et(),tr=require("fs"),fe=function(){},ln=/^v?\.0/.test(process.version),Ae=function(e){return typeof e=="function"},hn=function(e){return!ln||!tr?!1:(e instanceof(tr.ReadStream||fe)||e instanceof(tr.WriteStream||fe))&&Ae(e.close)},pn=function(e){return e.setHeader&&Ae(e.abort)},mn=function(e,r,t,n){n=cn(n);var i=!1;e.on("close",function(){i=!0}),dn(e,{readable:r,writable:t},function(s){if(s)return n(s);i=!0,n()});var o=!1;return function(s){if(!i&&!o){if(o=!0,hn(e))return e.close(fe);if(pn(e))return e.abort();if(Ae(e.destroy))return e.destroy();n(s||new Error("stream was destroyed"))}}},rt=function(e){e()},xn=function(e,r){return e.pipe(r)},vn=function(){var e=Array.prototype.slice.call(arguments),r=Ae(e[e.length-1]||fe)&&e.pop()||fe;if(Array.isArray(e[0])&&(e=e[0]),e.length<2)throw new Error("pump requires two streams per minimum");var t,n=e.map(function(i,o){var s=o<e.length-1,f=o>0;return mn(i,s,f,function(u){t||(t=u),u&&n.forEach(rt),!s&&(n.forEach(rt),r(t))})});return e.reduce(xn)};tt.exports=vn});var ot=E((Yn,it)=>{"use strict";var{PassThrough:En}=require("stream");it.exports=e=>{e={...e};let{array:r}=e,{encoding:t}=e,n=t==="buffer",i=!1;r?i=!(t||n):t=t||"utf8",n&&(t=null);let o=new En({objectMode:i});t&&o.setEncoding(t);let s=0,f=[];return o.on("data",u=>{f.push(u),i?s=f.length:s+=u.length}),o.getBufferedValue=()=>r?f:n?Buffer.concat(f,s):f.join(""),o.getBufferedLength=()=>s,o}});var st=E(($n,Q)=>{"use strict";var{constants:wn}=require("buffer"),gn=nt(),yn=ot(),Re=class extends Error{constructor(){super("maxBuffer exceeded"),this.name="MaxBufferError"}};async function De(e,r){if(!e)return Promise.reject(new Error("Expected a stream"));r={maxBuffer:1/0,...r};let{maxBuffer:t}=r,n;return await new Promise((i,o)=>{let s=f=>{f&&n.getBufferedLength()<=wn.MAX_LENGTH&&(f.bufferedData=n.getBufferedValue()),o(f)};n=gn(e,yn(r),f=>{if(f){s(f);return}i()}),n.on("data",()=>{n.getBufferedLength()>t&&s(new Re)})}),n.getBufferedValue()}Q.exports=De;Q.exports.default=De;Q.exports.buffer=(e,r)=>De(e,{...r,encoding:"buffer"});Q.exports.array=(e,r)=>De(e,{...r,array:!0});Q.exports.MaxBufferError=Re});var at=E((jn,ft)=>{var z=$r()("extract-zip"),{createWriteStream:Cn,promises:k}=require("fs"),bn=st(),G=require("path"),{promisify:ir}=require("util"),Fn=require("stream"),Sn=Xe(),In=ir(Sn.open),Ln=ir(Fn.pipeline),nr=class{constructor(r,t){this.zipPath=r,this.opts=t}async extract(){return z("opening",this.zipPath,"with opts",this.opts),this.zipfile=await In(this.zipPath,{lazyEntries:!0}),this.canceled=!1,new Promise((r,t)=>{this.zipfile.on("error",n=>{this.canceled=!0,t(n)}),this.zipfile.readEntry(),this.zipfile.on("close",()=>{this.canceled||(z("zip extraction complete"),r())}),this.zipfile.on("entry",async n=>{if(this.canceled){z("skipping entry",n.fileName,{cancelled:this.canceled});return}if(z("zipfile entry",n.fileName),n.fileName.startsWith("__MACOSX/")){this.zipfile.readEntry();return}let i=G.dirname(G.join(this.opts.dir,n.fileName));try{await k.mkdir(i,{recursive:!0});let o=await k.realpath(i);if(G.relative(this.opts.dir,o).split(G.sep).includes(".."))throw new Error(`Out of bound path "${o}" found while processing file ${n.fileName}`);await this.extractEntry(n),z("finished processing",n.fileName),this.zipfile.readEntry()}catch(o){this.canceled=!0,this.zipfile.close(),t(o)}})})}async extractEntry(r){if(this.canceled){z("skipping entry extraction",r.fileName,{cancelled:this.canceled});return}this.opts.onEntry&&this.opts.onEntry(r,this.zipfile);let t=G.join(this.opts.dir,r.fileName),n=r.externalFileAttributes>>16&65535,i=61440,o=16384,s=40960,f=(n&i)===s,u=(n&i)===o;!u&&r.fileName.endsWith("/")&&(u=!0);let d=r.versionMadeBy>>8;u||(u=d===0&&r.externalFileAttributes===16),z("extracting entry",{filename:r.fileName,isDir:u,isSymlink:f});let a=this.getExtractedMode(n,u)&511,c=u?t:G.dirname(t),l={recursive:!0};if(u&&(l.mode=a),z("mkdir",{dir:c,...l}),await k.mkdir(c,l),u)return;z("opening read stream",t);let x=await ir(this.zipfile.openReadStream.bind(this.zipfile))(r);if(f){let g=await bn(x);z("creating symlink",g,t),await k.symlink(g,t)}else await Ln(x,Cn(t,{mode:a}))}getExtractedMode(r,t){let n=r;return n===0&&(t?(this.opts.defaultDirMode&&(n=parseInt(this.opts.defaultDirMode,10)),n||(n=493)):(this.opts.defaultFileMode&&(n=parseInt(this.opts.defaultFileMode,10)),n||(n=420))),n}};ft.exports=async function(e,r){if(z("creating target directory",r.dir),!G.isAbsolute(r.dir))throw new Error("Target directory is expected to be absolute");return await k.mkdir(r.dir,{recursive:!0}),r.dir=await k.realpath(r.dir),new nr(e,r).extract()}});var zn={};Et(zn,{extract:()=>On,yauzl:()=>dt,yazl:()=>ct});module.exports=wt(zn);var ct=Ue(br()),dt=Ue(Xe()),ut=Ue(at()),On=ut.default;0&&(module.exports={extract,yauzl,yazl});
