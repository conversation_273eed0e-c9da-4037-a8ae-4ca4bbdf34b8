import os, json, httpx, asyncio, time, traceback, random
try:
    import tomllib
except ModuleNotFoundError:
    import tomli as tomllib
from loguru import logger
from pathlib import Path
from WechatAPI import WechatAPIClient
from utils.decorators import on_text_message
from utils.plugin_base import PluginBase


class DanceSignInPlugin(PluginBase):
    description = "唱舞全明星签到插件"
    author = "XYBot"
    version = "1.0.0"
    plugin_name = "DanceSignIn"

    def __init__(self):
        super().__init__()
        self.temp_dir = Path("plugins/DanceSignIn/temp")
        self.temp_dir.mkdir(parents=True, exist_ok=True)
        self._load_config()
        self.user_last_request = {}
        self._init_natural_responses()

    def _load_config(self):
        """加载配置文件"""
        try:
            with open("plugins/DanceSignIn/config.toml", "rb") as f:
                config = tomllib.load(f)["DanceSignIn"]

            self.enable = config.get("enable", True)
            self.command = config.get("command", ["唱舞签到"])
            self.command_format = config.get("command-format", "唱舞签到 - 获取签到链接")
            self.natural_response = config.get("natural_response", True)

            # 限流配置
            rate_limit = config.get("rate_limit", {})
            self.cooldown = rate_limit.get("cooldown", 5)

            # 签到链接
            self.signin_link = "https://open.weixin.qq.com/connect/oauth2/authorize?appid=wx6de432d4ad7e151c&redirect_uri=http%3A%2F%2Freserve.fhsj.xipu.com%2Fapi%2Fsign%2Findex&response_type=code&scope=snsapi_base&state=STATE#wechat_redirect"



        except Exception as e:
            logger.error(f"[{self.plugin_name}] 配置加载失败: {e}")
            self.enable = False

    def _init_natural_responses(self):
        """初始化自然化回复词库"""
        self.error_msgs = [
            "这可把我难住了", "搞不定了", "这个有点难", "弄不了",
            "出问题了", "搞不出来", "这个不行"
        ]

        self.rate_limit_msgs = [
            "你搁这刷bug呢", "慢点慢点", "别着急", "等等再说",
            "刚刚不是发过了吗", "太快了太快了"
        ]

    def _check_user_limit(self, wxid: str, user_wxid: str) -> float:
        """检查用户限流，返回需要等待的时间"""
        current_time = time.time()
        last_time = self.user_last_request.get(user_wxid, 0)

        wait_time = self.cooldown - (current_time - last_time)
        if wait_time > 0:
            return wait_time

        self.user_last_request[user_wxid] = current_time
        return 0

    def _get_signin_link(self) -> str:
        """获取签到链接"""
        return self.signin_link

    def _get_card_info(self) -> tuple:
        """获取卡片信息"""
        title = "唱舞全明星"
        description = "点击进入签到页面，领取专属福利"
        thumb_url = "https://mmbiz.qpic.cn/mmbiz_jpg/RicuJvxibRtUBlv9G75UTulanktDF1OxFO7Wyhzs1WS609tq1j9icfNhLkM6zB3lwM5ZlbgQia1ibIcxuj35WAm465w/640?wxtype=jpeg&wxfrom=0"

        return title, description, thumb_url

    async def _process_signin_request(self, bot: WechatAPIClient, wxid: str) -> bool:
        """处理签到请求"""
        try:
            # 获取签到链接
            signin_link = self._get_signin_link()

            # 获取卡片信息
            title, description, thumb_url = self._get_card_info()

            # 发送链接卡片
            await bot.send_link_message(
                wxid,
                signin_link,
                title,
                description,
                thumb_url
            )


            return True

        except Exception as e:
            logger.error(f"[{self.plugin_name}] 发送签到链接失败: {e}")
            return False

    @on_text_message
    async def handle_text(self, bot: WechatAPIClient, message: dict):
        """处理文本消息"""
        if not self.enable:
            return

        content = message["Content"].strip()
        wxid = message["FromWxid"]
        user_wxid = message["SenderWxid"]

        # 检查是否是签到命令
        if not any(content.startswith(cmd) for cmd in self.command):
            return

        # 限流检查
        wait_time = self._check_user_limit(wxid, user_wxid)
        if wait_time > 0:
            if self.natural_response:
                rate_limit_msg = random.choice(self.rate_limit_msgs)
                await bot.send_text_message(wxid, rate_limit_msg)
            else:
                await bot.send_at_message(wxid, f"请等待 {wait_time:.1f} 秒", [user_wxid])
            return

        try:
            # 处理签到请求
            success = await self._process_signin_request(bot, wxid)

            if not success:
                raise Exception("签到链接发送失败")

        except Exception as e:
            logger.error(f"[{self.plugin_name}] 异常: {e}")
            if self.natural_response:
                error_msg = random.choice(self.error_msgs)
                await bot.send_text_message(wxid, error_msg)
            else:
                await bot.send_at_message(wxid, "处理失败", [user_wxid])
