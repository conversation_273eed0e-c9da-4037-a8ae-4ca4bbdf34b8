
程序退出时间: 2025-02-21 12:07:08
退出代码: 未知
没有捕获到异常信息
退出时的调用栈:
  File "C:\XYBotV2\main.py", line 108, in <lambda>
    atexit.register(lambda: log_exit_info())
  File "C:\XYBotV2\main.py", line 79, in log_exit_info
    exit_message += ''.join(traceback.format_stack())

==================================================

程序退出时间: 2025-02-21 21:28:39
退出代码: 未知
没有捕获到异常信息
退出时的调用栈:
  File "C:\XYBotV2\main.py", line 112, in <lambda>
    atexit.register(lambda: log_exit_info())
  File "C:\XYBotV2\main.py", line 80, in log_exit_info
    exit_message += ''.join(traceback.format_stack())

==================================================

程序退出时间: 2025-02-21 21:28:49
退出代码: 未知
没有捕获到异常信息
退出时的调用栈:
  File "C:\XYBotV2\main.py", line 112, in <lambda>
    atexit.register(lambda: log_exit_info())
  File "C:\XYBotV2\main.py", line 80, in log_exit_info
    exit_message += ''.join(traceback.format_stack())

==================================================

程序退出时间: 2025-02-21 21:28:58
退出代码: 未知
没有捕获到异常信息
退出时的调用栈:
  File "C:\XYBotV2\main.py", line 112, in <lambda>
    atexit.register(lambda: log_exit_info())
  File "C:\XYBotV2\main.py", line 80, in log_exit_info
    exit_message += ''.join(traceback.format_stack())

==================================================

程序退出时间: 2025-02-21 21:29:07
退出代码: 未知
没有捕获到异常信息
退出时的调用栈:
  File "C:\XYBotV2\main.py", line 112, in <lambda>
    atexit.register(lambda: log_exit_info())
  File "C:\XYBotV2\main.py", line 80, in log_exit_info
    exit_message += ''.join(traceback.format_stack())

==================================================

程序退出时间: 2025-02-21 21:29:16
退出代码: 未知
没有捕获到异常信息
退出时的调用栈:
  File "C:\XYBotV2\main.py", line 112, in <lambda>
    atexit.register(lambda: log_exit_info())
  File "C:\XYBotV2\main.py", line 80, in log_exit_info
    exit_message += ''.join(traceback.format_stack())

==================================================

程序退出时间: 2025-02-21 21:29:25
退出代码: 未知
没有捕获到异常信息
退出时的调用栈:
  File "C:\XYBotV2\main.py", line 112, in <lambda>
    atexit.register(lambda: log_exit_info())
  File "C:\XYBotV2\main.py", line 80, in log_exit_info
    exit_message += ''.join(traceback.format_stack())

==================================================

程序退出时间: 2025-02-21 21:29:34
退出代码: 未知
没有捕获到异常信息
退出时的调用栈:
  File "C:\XYBotV2\main.py", line 112, in <lambda>
    atexit.register(lambda: log_exit_info())
  File "C:\XYBotV2\main.py", line 80, in log_exit_info
    exit_message += ''.join(traceback.format_stack())

==================================================

程序退出时间: 2025-02-21 21:29:43
退出代码: 未知
没有捕获到异常信息
退出时的调用栈:
  File "C:\XYBotV2\main.py", line 112, in <lambda>
    atexit.register(lambda: log_exit_info())
  File "C:\XYBotV2\main.py", line 80, in log_exit_info
    exit_message += ''.join(traceback.format_stack())

==================================================

程序退出时间: 2025-02-21 21:29:52
退出代码: 未知
没有捕获到异常信息
退出时的调用栈:
  File "C:\XYBotV2\main.py", line 112, in <lambda>
    atexit.register(lambda: log_exit_info())
  File "C:\XYBotV2\main.py", line 80, in log_exit_info
    exit_message += ''.join(traceback.format_stack())

==================================================

程序退出时间: 2025-02-21 21:30:02
退出代码: 未知
没有捕获到异常信息
退出时的调用栈:
  File "C:\XYBotV2\main.py", line 112, in <lambda>
    atexit.register(lambda: log_exit_info())
  File "C:\XYBotV2\main.py", line 80, in log_exit_info
    exit_message += ''.join(traceback.format_stack())

==================================================

程序退出时间: 2025-02-21 21:30:02
退出代码: 未知
没有捕获到异常信息
退出时的调用栈:
  File "C:\XYBotV2\main.py", line 112, in <lambda>
    atexit.register(lambda: log_exit_info())
  File "C:\XYBotV2\main.py", line 80, in log_exit_info
    exit_message += ''.join(traceback.format_stack())

==================================================

程序退出时间: 2025-02-21 21:30:12
退出代码: 未知
没有捕获到异常信息
退出时的调用栈:
  File "C:\XYBotV2\main.py", line 112, in <lambda>
    atexit.register(lambda: log_exit_info())
  File "C:\XYBotV2\main.py", line 80, in log_exit_info
    exit_message += ''.join(traceback.format_stack())

==================================================

程序退出时间: 2025-02-21 21:30:12
退出代码: 未知
没有捕获到异常信息
退出时的调用栈:
  File "C:\XYBotV2\main.py", line 112, in <lambda>
    atexit.register(lambda: log_exit_info())
  File "C:\XYBotV2\main.py", line 80, in log_exit_info
    exit_message += ''.join(traceback.format_stack())

==================================================

程序退出时间: 2025-02-21 21:30:22
退出代码: 未知
没有捕获到异常信息
退出时的调用栈:
  File "C:\XYBotV2\main.py", line 112, in <lambda>
    atexit.register(lambda: log_exit_info())
  File "C:\XYBotV2\main.py", line 80, in log_exit_info
    exit_message += ''.join(traceback.format_stack())

==================================================

程序退出时间: 2025-02-21 21:30:22
退出代码: 未知
没有捕获到异常信息
退出时的调用栈:
  File "C:\XYBotV2\main.py", line 112, in <lambda>
    atexit.register(lambda: log_exit_info())
  File "C:\XYBotV2\main.py", line 80, in log_exit_info
    exit_message += ''.join(traceback.format_stack())

==================================================

程序退出时间: 2025-02-21 21:30:32
退出代码: 未知
没有捕获到异常信息
退出时的调用栈:
  File "C:\XYBotV2\main.py", line 112, in <lambda>
    atexit.register(lambda: log_exit_info())
  File "C:\XYBotV2\main.py", line 80, in log_exit_info
    exit_message += ''.join(traceback.format_stack())

==================================================

程序退出时间: 2025-02-21 21:30:32
退出代码: 未知
没有捕获到异常信息
退出时的调用栈:
  File "C:\XYBotV2\main.py", line 112, in <lambda>
    atexit.register(lambda: log_exit_info())
  File "C:\XYBotV2\main.py", line 80, in log_exit_info
    exit_message += ''.join(traceback.format_stack())

==================================================

程序退出时间: 2025-02-21 21:30:42
退出代码: 未知
没有捕获到异常信息
退出时的调用栈:
  File "C:\XYBotV2\main.py", line 112, in <lambda>
    atexit.register(lambda: log_exit_info())
  File "C:\XYBotV2\main.py", line 80, in log_exit_info
    exit_message += ''.join(traceback.format_stack())

==================================================

程序退出时间: 2025-02-21 21:30:52
退出代码: 未知
没有捕获到异常信息
退出时的调用栈:
  File "C:\XYBotV2\main.py", line 112, in <lambda>
    atexit.register(lambda: log_exit_info())
  File "C:\XYBotV2\main.py", line 80, in log_exit_info
    exit_message += ''.join(traceback.format_stack())

==================================================

程序退出时间: 2025-02-21 21:30:52
退出代码: 未知
没有捕获到异常信息
退出时的调用栈:
  File "C:\XYBotV2\main.py", line 112, in <lambda>
    atexit.register(lambda: log_exit_info())
  File "C:\XYBotV2\main.py", line 80, in log_exit_info
    exit_message += ''.join(traceback.format_stack())

==================================================

程序退出时间: 2025-02-21 21:31:02
退出代码: 未知
没有捕获到异常信息
退出时的调用栈:
  File "C:\XYBotV2\main.py", line 112, in <lambda>
    atexit.register(lambda: log_exit_info())
  File "C:\XYBotV2\main.py", line 80, in log_exit_info
    exit_message += ''.join(traceback.format_stack())

==================================================

程序退出时间: 2025-02-21 21:31:12
退出代码: 未知
没有捕获到异常信息
退出时的调用栈:
  File "C:\XYBotV2\main.py", line 112, in <lambda>
    atexit.register(lambda: log_exit_info())
  File "C:\XYBotV2\main.py", line 80, in log_exit_info
    exit_message += ''.join(traceback.format_stack())

==================================================

程序退出时间: 2025-02-21 21:31:12
退出代码: 未知
没有捕获到异常信息
退出时的调用栈:
  File "C:\XYBotV2\main.py", line 112, in <lambda>
    atexit.register(lambda: log_exit_info())
  File "C:\XYBotV2\main.py", line 80, in log_exit_info
    exit_message += ''.join(traceback.format_stack())

==================================================
