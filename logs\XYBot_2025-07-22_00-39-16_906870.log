2025-07-22 00:39:18 | SUCCESS | 读取主设置成功
2025-07-22 00:39:18 | DEBUG | 最终使用的 Redis 主机地址: 127.0.0.1
2025-07-22 00:39:18 | INFO | 2025/07/22 00:39:18 GetRedisAddr: 127.0.0.1:6379
2025-07-22 00:39:18 | INFO | 2025/07/22 00:39:18 Redis连接成功 | 地址:127.0.0.1:6379 DB:0
2025-07-22 00:39:18 | INFO | 2025/07/22 00:39:18 Server start at :9000
2025-07-22 00:39:18 | SUCCESS | WechatAPI服务已启动
2025-07-22 00:39:19 | INFO | 登录账号信息: wxid: wxid_4usgcju5ey9q29  昵称: 瑶瑶  微信号: Today-KFC  手机号: None
2025-07-22 00:39:19 | INFO | 登录设备信息: device_name: <PERSON>'s Pad  device_id: 49d050a7e94d1ecc40790d74b1c4369f
2025-07-22 00:39:19 | SUCCESS | 登录成功
2025-07-22 00:39:19 | SUCCESS | 已开启自动心跳
2025-07-22 00:39:19 | INFO | 成功加载表情映射文件，共 521 条记录
2025-07-22 00:39:19 | SUCCESS | 数据库初始化成功
2025-07-22 00:39:19 | SUCCESS | 定时任务已启动
2025-07-22 00:39:19 | SUCCESS | 插件 AISignIn 已加载定时任务: {'plugins.AISignIn.main.AISignIn.auto_signin_task'}
2025-07-22 00:39:19 | INFO | 已加载管理员列表: ['wxid_ubbh6q832tcs21', 'admin-wxid']
2025-07-22 00:39:20 | INFO | 播客API初始化成功
2025-07-22 00:39:20 | INFO | 加载配置完成，转换后的数据: {'gh_7057516c9e71': {'name': '唱舞全明星', 'groups': ['27852221909@chatroom', '55878994168@chatroom', '51891329927@chatroom']}, 'gh_d4d21a0e4623': {'name': '两颗桃', 'groups': ['48097389945@chatroom']}, 'gh_410ff2fa6ec9': {'name': 'NGC660 Ai研究院', 'groups': ['47442567074@chatroom']}, 'gh_f78c53c507c0': {'name': '不正常吐槽君', 'groups': ['48097389945@chatroom']}, 'gh_ba3381c847b3': {'name': '唱舞星计划', 'groups': ['51891329927@chatroom']}, 'gh_1a102fb4cad9': {'name': '我在娱乐圈的唯一人脉', 'groups': ['48097389945@chatroom', '27852221909@chatroom']}}
2025-07-22 00:39:20 | INFO | ArticleForwarder插件初始化完成 - 监控配置: {'gh_7057516c9e71': {'name': '唱舞全明星', 'groups': ['27852221909@chatroom', '55878994168@chatroom', '51891329927@chatroom']}, 'gh_d4d21a0e4623': {'name': '两颗桃', 'groups': ['48097389945@chatroom']}, 'gh_410ff2fa6ec9': {'name': 'NGC660 Ai研究院', 'groups': ['47442567074@chatroom']}, 'gh_f78c53c507c0': {'name': '不正常吐槽君', 'groups': ['48097389945@chatroom']}, 'gh_ba3381c847b3': {'name': '唱舞星计划', 'groups': ['51891329927@chatroom']}, 'gh_1a102fb4cad9': {'name': '我在娱乐圈的唯一人脉', 'groups': ['48097389945@chatroom', '27852221909@chatroom']}}
2025-07-22 00:39:20 | DEBUG | [TempFileManager] 添加清理规则: default
2025-07-22 00:39:20 | DEBUG | [TempFileManager] 添加清理规则: images
2025-07-22 00:39:20 | DEBUG | [TempFileManager] 添加清理规则: videos
2025-07-22 00:39:20 | DEBUG | [TempFileManager] 添加清理规则: audio
2025-07-22 00:39:20 | DEBUG | [TempFileManager] 添加清理规则: temp
2025-07-22 00:39:20 | DEBUG | [TempFileManager] 添加清理规则: large_files
2025-07-22 00:39:20 | INFO | [TempFileManager] 临时文件清理任务已启动
2025-07-22 00:39:20 | INFO | [ChatSummary] 数据库初始化成功
2025-07-22 00:39:21 | INFO | 成功加载表情映射文件，共 521 条记录
2025-07-22 00:39:21 | SUCCESS | 插件 GoodMorning 已加载定时任务: {'plugins.GoodMorning.main.GoodMorning.daily_task'}
2025-07-22 00:39:21 | SUCCESS | 插件 News 已加载定时任务: {'plugins.News.main.News.night_news', 'plugins.News.main.News.noon_news'}
2025-07-22 00:39:21 | INFO | [PatReply] AMR文件已存在且是最新的: C:\XYBotV2\data\paiyipai\1_BFB5258B-2B13-4CF4-A40C-5BCCE74E5414_converted.amr
2025-07-22 00:39:21 | DEBUG | [PatReply] 从缓存加载base64: 18228 字符
2025-07-22 00:39:21 | SUCCESS | 插件 QuarkSignIn 已加载定时任务: {'plugins.QuarkSignIn.main.QuarkSignIn.auto_signin_task'}
2025-07-22 00:39:21 | INFO | [QuarkSignIn] 已设置自动签到时间: 08:00
2025-07-22 00:39:21 | INFO | 插件状态: 启用, 检查间隔: 3600秒, 最大重试次数: 3, 重试延迟: 5秒
2025-07-22 00:39:21 | INFO | 已加载管理员列表: ['wxid_ubbh6q832tcs21', 'admin-wxid']
2025-07-22 00:39:21 | INFO | 已加载 2 个改名监控群和 1 个退群监控群
2025-07-22 00:39:21 | INFO | [RenameReminder] 开始启用插件...
2025-07-22 00:39:21 | SUCCESS | 插件 RenameReminder 已加载定时任务: {'plugins.RenameReminder.main.RenameReminder.check_name_changes'}
2025-07-22 00:39:21 | INFO | [RenameReminder] 插件已启用，开始初始化...
2025-07-22 00:39:21 | INFO | [RenameReminder] 支持的命令: 开启改名提醒, 关闭改名提醒, 开启退群提醒, 关闭退群提醒, 查看提醒状态
2025-07-22 00:39:21 | INFO | 已设置检查间隔为 3600 秒
2025-07-22 00:39:21 | INFO | 已加载 2 个监控群，开始更新成员列表
2025-07-22 00:39:21 | DEBUG | 已更新群 51891329927@chatroom 的成员列表
2025-07-22 00:39:22 | DEBUG | 已更新群 27852221909@chatroom 的成员列表
2025-07-22 00:39:22 | SUCCESS | 插件 TimerTask 已加载定时任务: {'plugins.TimerTask.main.TimerTask.check_tasks'}
2025-07-22 00:39:22 | INFO | [VideoDemand] 加载了 128 个视频类别配置
2025-07-22 00:39:23 | INFO | [YaoyaoPlugin] 私聊黑名单用户: ['gh_a5cf07d474f1', 'gh_ba3381c847b3']
2025-07-22 00:39:23 | INFO | [YaoyaoPlugin] 已加载管理员列表: ['wxid_ubbh6q832tcs21', 'admin-wxid']
2025-07-22 00:39:23 | INFO | [yuanbao] 插件初始化完成
2025-07-22 00:39:23 | DEBUG | [yuanbao] 指令: ['元宝', 'yuanbao']
2025-07-22 00:39:23 | DEBUG | [yuanbao] 绘图模型: gpt_175B_0404
2025-07-22 00:39:23 | DEBUG | [yuanbao] 认证信息加载状态: hy_token=True, hy_user=True, agent_id=True, x_uskey=True
2025-07-22 00:39:23 | SUCCESS | 已加载插件: ['AdminPoint', 'AdminSignInReset', 'AdminWhitelist', 'AISignIn', 'AppMessageTester', 'ArticleForwarder', 'BaiduAgentsPlugin', 'BaiduDraw', 'BotStatus', 'ChatSummary', 'DanceSignInPlugin', 'DeepseekPlugin', 'Doubao', 'DoubaoDrawing', 'DoubaoImageRecognition', 'DouBaoImageToImage', 'DouBaoImageToVideo', 'DoubaoVideoSearch', 'DouHui', 'EmojiTestPlugin', 'FixedQuoteTest', 'GetContact', 'Gomoku', 'GoodMorning', 'HunyuanDraw', 'ImageEcho', 'ImageToImage', 'JiemengDraw', 'KeLingImageToImage', 'KlingAI', 'Leaderboard', 'LuckyDraw', 'ManagePlugin', 'MeituAI', 'Menu', 'MiniProgramTester', 'Music', 'News', 'PatReply', 'PointTrade', 'QuarkSignIn', 'QueryPoint', 'RandomMember', 'RandomPicture', 'RenameReminder', 'RevokePlugin', 'RoboNeo', 'SignIn', 'TempFileManagerPlugin', 'TencentLke', 'TimerTask', 'TongyiDraw', 'URLShortener', 'VideoDemand', 'VideoParserPlugin', 'VideoTest', 'VivoAgentsPlugin', 'VoiceTest', 'Warthunder', 'WeatherQuery', 'WelcomePlugin', 'Xunfei', 'YaoyaoPlugin', 'Yuanbao', '抽签', '造梦次元']
2025-07-22 00:39:23 | INFO | 处理堆积消息中
2025-07-22 00:39:23 | SUCCESS | 处理堆积消息完毕
2025-07-22 00:39:23 | SUCCESS | 开始处理消息
2025-07-22 00:39:40 | DEBUG | 收到消息: {'MsgId': 1271066390, 'FromUserName': {'string': '55878994168@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 1, 'Content': {'string': 'wxid_ubbh6q832tcs21:\n美图 开启'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1753115991, 'MsgSource': '<msgsource>\n\t<pua>1</pua>\n\t<eggIncluded>1</eggIncluded>\n\t<silence>0</silence>\n\t<membercount>3</membercount>\n\t<signature>N0_V1_bP8USmtF|v1_ynzcZctu</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'PushContent': '郭 : 美图 开启', 'NewMsgId': 6853820090065335096, 'MsgSeq': 871389716}
2025-07-22 00:39:40 | INFO | 收到文本消息: 消息ID:1271066390 来自:55878994168@chatroom 发送人:wxid_ubbh6q832tcs21 @:[] 内容:美图 开启
2025-07-22 00:39:41 | INFO | 发送文字消息: 对方wxid:55878994168@chatroom at:['wxid_ubbh6q832tcs21'] 内容:@郭 ✅ 已进入美图AI模式，正在初始化...
2025-07-22 00:39:51 | INFO | 发送文字消息: 对方wxid:55878994168@chatroom at:['wxid_ubbh6q832tcs21'] 内容:@郭 你好呀！我是RoboNeo，你的影像创作小助手~ 无论是修图、做设计还是剪视频，只要告诉我你的想法，我就能帮你轻松实现！今天有什么创意想实现吗？😊
2025-07-22 00:39:51 | DEBUG | 处理消息内容: '美图 开启'
2025-07-22 00:39:51 | DEBUG | 消息内容 '美图 开启' 不匹配任何命令，忽略
2025-07-22 00:39:52 | INFO | 发送文字消息: 对方wxid:55878994168@chatroom at:['wxid_ubbh6q832tcs21'] 内容:@郭 ❌ 命令格式错误，正确格式: 🎨 美图RoboNeo插件使用说明

📝 使用方法：
• 引用图片消息并发送: 美图+提示词
• 例如: 美图换成泳衣
• 例如: 美图+换成古装

💡 提示词示例：
• 换成泳衣
• 换成古装
• 换成婚纱
• 换成职业装
• 换成运动装

⏱️ 处理时间: 约30-180秒
🔄 支持多种风格转换

2025-07-22 00:39:53 | INFO | 发送文字消息: 对方wxid:55878994168@chatroom at:['wxid_ubbh6q832tcs21'] 内容:@郭 🎨 美图RoboNeo插件使用说明
📝 命令格式:
• 🎨 美图RoboNeo插件使用说明

📝 使用方法：
• 引用图片消息并发送: 美图+提示词
• 例如: 美图换成泳衣
• 例如: 美图+换成古装

💡 提示词示例：
• 换成泳衣
• 换成古装
• 换成婚纱
• 换成职业装
• 换成运动装

⏱️ 处理时间: 约30-180秒
🔄 支持多种风格转换

• 引用图片并发送: 美图+提示词
💡 示例: 美图换成泳衣
⏱️ 处理时间: 约30-180秒
2025-07-22 00:39:54 | DEBUG | 收到消息: {'MsgId': 1590552902, 'FromUserName': {'string': '47325400669@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 1, 'Content': {'string': 'seraph333:\n@阿猪米德\u2005颜色是人类按照眼睛看到的来定义的，所以我看到它是红色，它就是红色'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1753115994, 'MsgSource': '<msgsource>\n\t<atuserlist>wxid_fh84okl6f5wp22</atuserlist>\n\t<bizflag>0</bizflag>\n\t<pua>1</pua>\n\t<eggIncluded>1</eggIncluded>\n\t<silence>0</silence>\n\t<membercount>219</membercount>\n\t<signature>N0_V1_8Pg1hjXd|v1_rtrN1m7W</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'PushContent': '酱爆\ue147 : @阿猪米德\u2005颜色是人类按照眼睛看到的来定义的，所以我看到它是...', 'NewMsgId': 2466562315798287708, 'MsgSeq': 871389719}
2025-07-22 00:39:54 | INFO | 收到文本消息: 消息ID:1590552902 来自:47325400669@chatroom 发送人:seraph333 @:['wxid_fh84okl6f5wp22'] 内容:@阿猪米德 颜色是人类按照眼睛看到的来定义的，所以我看到它是红色，它就是红色
2025-07-22 00:39:54 | DEBUG | 处理消息内容: '@阿猪米德 颜色是人类按照眼睛看到的来定义的，所以我看到它是红色，它就是红色'
2025-07-22 00:39:54 | DEBUG | 消息内容 '@阿猪米德 颜色是人类按照眼睛看到的来定义的，所以我看到它是红色，它就是红色' 不匹配任何命令，忽略
2025-07-22 00:40:09 | DEBUG | 收到消息: {'MsgId': 1431485841, 'FromUserName': {'string': '47325400669@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 1, 'Content': {'string': 'wxid_fh84okl6f5wp22:\n呃，所以你看到世界是红色，那它就是红色咯？🤔 大脑自己给自己加滤镜，还怪我咯？你高兴就好。🤷\u200d♀️'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1753116019, 'MsgSource': '<msgsource>\n\t<silence>0</silence>\n\t<membercount>219</membercount>\n\t<signature>N0_V1_Y4bfTZy0|v1_wGy+uQZx</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'PushContent': '阿猪米德 : 呃，所以你看到世界是红色，那它就是红色咯？🤔 大脑自己给自己...', 'NewMsgId': 1953767238602569974, 'MsgSeq': 871389726}
2025-07-22 00:40:09 | INFO | 收到文本消息: 消息ID:1431485841 来自:47325400669@chatroom 发送人:wxid_fh84okl6f5wp22 @:[] 内容:呃，所以你看到世界是红色，那它就是红色咯？🤔 大脑自己给自己加滤镜，还怪我咯？你高兴就好。🤷‍♀️
2025-07-22 00:40:09 | DEBUG | 处理消息内容: '呃，所以你看到世界是红色，那它就是红色咯？🤔 大脑自己给自己加滤镜，还怪我咯？你高兴就好。🤷‍♀️'
2025-07-22 00:40:09 | DEBUG | 消息内容 '呃，所以你看到世界是红色，那它就是红色咯？🤔 大脑自己给自己加滤镜，还怪我咯？你高兴就好。🤷‍♀️' 不匹配任何命令，忽略
2025-07-22 00:40:14 | DEBUG | 收到消息: {'MsgId': 145350743, 'FromUserName': {'string': '47325400669@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 1, 'Content': {'string': 'seraph333:\n老猪你个臭AI懂什么'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1753116024, 'MsgSource': '<msgsource>\n\t<bizflag>0</bizflag>\n\t<pua>1</pua>\n\t<eggIncluded>1</eggIncluded>\n\t<silence>0</silence>\n\t<membercount>219</membercount>\n\t<signature>N0_V1_k2/IvbGz|v1_nqrcguSx</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'PushContent': '酱爆\ue147 : 老猪你个臭AI懂什么', 'NewMsgId': 3363300260320198618, 'MsgSeq': 871389727}
2025-07-22 00:40:14 | INFO | 收到文本消息: 消息ID:145350743 来自:47325400669@chatroom 发送人:seraph333 @:[] 内容:老猪你个臭AI懂什么
2025-07-22 00:40:14 | DEBUG | 处理消息内容: '老猪你个臭AI懂什么'
2025-07-22 00:40:14 | DEBUG | 消息内容 '老猪你个臭AI懂什么' 不匹配任何命令，忽略
2025-07-22 00:40:17 | DEBUG | 收到消息: {'MsgId': 1867082185, 'FromUserName': {'string': '47325400669@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 1, 'Content': {'string': 'wxid_fh84okl6f5wp22:\n😓抱歉，AI助手遇到了一点小问题～'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1753116027, 'MsgSource': '<msgsource>\n\t<silence>0</silence>\n\t<membercount>219</membercount>\n\t<signature>N0_V1_nS4h9URH|v1_M4Oee8JL</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'PushContent': '阿猪米德 : \ue108抱歉，AI助手遇到了一点小问题～', 'NewMsgId': 3636646257548345268, 'MsgSeq': 871389728}
2025-07-22 00:40:17 | INFO | 收到文本消息: 消息ID:1867082185 来自:47325400669@chatroom 发送人:wxid_fh84okl6f5wp22 @:[] 内容:😓抱歉，AI助手遇到了一点小问题～
2025-07-22 00:40:17 | DEBUG | 处理消息内容: '😓抱歉，AI助手遇到了一点小问题～'
2025-07-22 00:40:17 | DEBUG | 消息内容 '😓抱歉，AI助手遇到了一点小问题～' 不匹配任何命令，忽略
2025-07-22 00:42:04 | DEBUG | 收到消息: {'MsgId': 132943607, 'FromUserName': {'string': '27852221909@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 47, 'Content': {'string': 'wxid_idzryo4rneok22:\n<msg><emoji fromusername="wxid_idzryo4rneok22" tousername="27852221909@chatroom" type="2" idbuffer="media:0_0" md5="0fcb1803ac4a12033c97b72c18f06851" len="21359" productid="" androidmd5="0fcb1803ac4a12033c97b72c18f06851" androidlen="21359" s60v3md5="0fcb1803ac4a12033c97b72c18f06851" s60v3len="21359" s60v5md5="0fcb1803ac4a12033c97b72c18f06851" s60v5len="21359" cdnurl="http://vweixinf.tc.qq.com/110/20401/stodownload?m=0fcb1803ac4a12033c97b72c18f06851&amp;filekey=3043020101042f302d02016e0402535a042030666362313830336163346131323033336339376237326331386630363835310202536f040d00000004627466730000000132&amp;hy=SZ&amp;storeid=264f87b81000be446ca9d4d8b0000006e01004fb1535a01a6fbc1e65263cca&amp;ef=1&amp;bizid=1022" designerid="" thumburl="" encrypturl="http://vweixinf.tc.qq.com/110/20402/stodownload?m=a31b223fa8e5aad149a70b69c9dae913&amp;filekey=3043020101042f302d02016e0402535a0420613331623232336661386535616164313439613730623639633964616539313302025370040d00000004627466730000000132&amp;hy=SZ&amp;storeid=264f87b81000c3f51ca9d4d8b0000006e02004fb2535a01a6fbc1e65263cd5&amp;ef=2&amp;bizid=1022" aeskey="6d5fb48af30748f9a12daaed518634a9" externurl="http://vweixinf.tc.qq.com/110/20403/stodownload?m=1dbb70a124a1ea10fc114fa32591815a&amp;filekey=3043020101042f302d02016e0402535a04203164626237306131323461316561313066633131346661333235393138313561020222d0040d00000004627466730000000132&amp;hy=SZ&amp;storeid=264f87b81000cb0bcca9d4d8b0000006e03004fb3535a01a6fbc1e65263ce0&amp;ef=3&amp;bizid=1022" externmd5="0b94e91eeaa91f5f15c0dcea76aff9ac" width="65" height="80" tpurl="" tpauthkey="" attachedtext="" attachedtextcolor="" lensid="" emojiattr="" linkid="" desc=""></emoji></msg>'}, 'Status': 3, 'ImgStatus': 2, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1753116134, 'MsgSource': '<msgsource>\n\t<silence>1</silence>\n\t<membercount>146</membercount>\n\t<signature>N0_V1_wvRhASzO|v1_SnDQjZBo</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'NewMsgId': 3830117895925341513, 'MsgSeq': 871389729}
2025-07-22 00:42:04 | INFO | 收到表情消息: 消息ID:132943607 来自:27852221909@chatroom 发送人:wxid_idzryo4rneok22 MD5:0fcb1803ac4a12033c97b72c18f06851 大小:21359
2025-07-22 00:42:04 | DEBUG | [DoubaoImageRecognition] 缓存表情消息: 3830117895925341513
