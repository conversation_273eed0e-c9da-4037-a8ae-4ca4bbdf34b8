import os
import json
import httpx
import asyncio
import time
import traceback
import uuid
try:
    import tomllib
except ModuleNotFoundError:
    import tomli as tomllib
from loguru import logger
from urllib.parse import quote, urlencode
from pathlib import Path
from typing import Optional, Dict, Any, List, Union
import random
import aiofiles

from WechatAPI import WechatAPIClient
from utils.decorators import on_text_message
from utils.plugin_base import PluginBase

class BaiduDraw(PluginBase):
    description = "百度AI绘画插件"
    author = "XYBot"
    version = "1.0.0"

    def __init__(self):
        # 必须先调用父类初始化
        super().__init__()

        # 初始化临时目录
        self.temp_dir = Path("plugins/BaiduDraw/temp")
        self.temp_dir.mkdir(parents=True, exist_ok=True)

        # 读取配置
        try:
            with open("plugins/BaiduDraw/config.toml", "rb") as f:
                plugin_config = tomllib.load(f)
            config = plugin_config.get("BaiduDraw", {})
        except Exception as e:
            logger.error(f"[{self.plugin_name}] 配置文件读取失败: {str(e)}")
            raise

        # 基本配置初始化
        self.enable = config.get("enable", True)
        self.command = config.get("command", ["画画", "绘画"])
        self.command_format = config.get("command-format", "")
        self.natural_response = config.get("natural_response", True)

        # API配置
        self.api_config = config.get("API", {})
        self.cookies = self.api_config.get("cookies", "")
        self.base_url = "https://image.baidu.com"

        # 用户级别的请求限制
        self._user_limits = {}  # wxid -> {user_wxid -> last_request_time}
        self.min_request_interval = 5.0  # 单个用户的最小请求间隔（秒）

        # 支持的模型
        self.models = {
            "通用": "1",
            "高级": "eb_pro",  # 用户输入"高级",实际使用eb_pro模型
            "iRAG": "eb_pro"   # 保留iRAG作为兼容选项
        }

        # 支持的画面比例
        self.ratios = {
            "1:1": {"width": 512, "height": 512},
            "3:4": {"width": 384, "height": 512},
            "4:3": {"width": 512, "height": 384},
            "16:9": {"width": 640, "height": 360},
            "9:16": {"width": 360, "height": 640}
        }

        # 支持的风格
        self.styles = [
            "二次元", "水彩画风", "涂鸦", "3D风格", "宫崎骏",
            "赛博朋克", "复古黑暗", "插画风格", "水墨画风", "电影效果",
            "纸制品", "超现实", "写实主义", "童话风格", "刺绣风格",
            "像素画", "琥珀色调", "3D涂鸦", "莫奈", "齐白石",
            "姜饼人风", "剪纸风格", "洛可可", "十字绣风", "太空朋克",
            "未来机械", "Q版动漫", "软糖风格", "毕加索", "卡通风格",
            "千禧粉红", "手稿画", "野兽派", "木制品风", "民族艺术",
            "鱼眼风格", "青铜风格", "LOMO效果", "后印象派", "多彩模式",
            "东方山水", "表现主义", "科幻风格", "印象派", "班克斯",
            "3D打印风", "法国艺术", "雾霾效果", "漫画风格", "陶瓷风格",
            "夜景模式"
        ]

        # 默认设置
        self.default_model = "通用"
        self.default_ratio = "1:1"
        self.default_style = None

        # 润色API的URL
        self.prompt_url = f"{self.base_url}/aigc/genPrompt"

        # 初始化自然化回复词库
        self._init_natural_responses()

    def _init_natural_responses(self):
        """初始化自然化回复词库"""
        self.confirm_msgs = [
            "好的", "收到", "明白了", "嗯", "知道了", "行", "OK"
        ]

        self.error_msgs = [
            "这可把我难住了", "搞不定了", "这个有点难", "弄不了",
            "出问题了", "搞不出来", "这个不行"
        ]

        self.rate_limit_msgs = [
            "你搁这刷bug呢", "慢点慢点", "别这么急", "一个一个来",
            "太快了太快了", "歇会儿", "别刷了"
        ]

        self.processing_msgs = [
            "马上就好", "稍等一下", "正在画", "画着呢",
            "等等哈", "马上", "在做了"
        ]

    async def _simple_confirm(self, bot: WechatAPIClient, group_wxid: str):
        """简单确认回复，不艾特人"""
        if self.natural_response:
            confirm_msg = random.choice(self.confirm_msgs)
            await bot.send_text_message(group_wxid, confirm_msg)

    async def _enhance_prompt(self, prompt: str, model_id: str = "1") -> Optional[str]:
        """润色提示文本"""
        try:
            data = {
                "taskid": "",
                "unique_id": str(int(time.time() * 1000)) + str(random.randint(100000, 999999)),
                "prompt": prompt,
                "opType": "3",
                "uploadImageSrc": "",
                "modelParameter[id]": model_id
            }

            headers = {
                "Accept": "application/json, text/plain, */*",
                "Content-Type": "application/x-www-form-urlencoded",
                "Cookie": self.cookies,
                "Origin": self.base_url,
                "Referer": f"{self.base_url}/front/aigc",
                "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36",
                "X-Requested-With": "mark.via"
            }

            async with httpx.AsyncClient() as client:
                response = await client.post(self.prompt_url, data=data, headers=headers)
                if response.status_code != 200:
                    logger.error(f"[BaiduDraw] 润色请求失败: {response.status_code}")
                    return None

                result = response.json()
                if result.get("status") != 0:
                    logger.error(f"[BaiduDraw] 润色失败: {result}")
                    return None

                return result["data"]["prompt"]

        except Exception as e:
            logger.error(f"[BaiduDraw] 润色文本异常: {e}")
            logger.error(traceback.format_exc())
            return None

    async def _generate_image(self, prompt: str, model: str = None, ratio: str = None, style: str = None) -> Optional[Dict[str, Any]]:
        """生成图片"""
        try:
            model_id = self.models.get(model or self.default_model)
            ratio_config = self.ratios.get(ratio or self.default_ratio)

            # 先润色提示文本
            enhanced_prompt = await self._enhance_prompt(prompt, model_id)
            if enhanced_prompt:
                prompt = enhanced_prompt


            if style and style in self.styles:
                prompt = f"{prompt}, {style}"

            data = {
                "query": prompt,
                "querycate": "8",
                "width": str(ratio_config["width"]),
                "height": str(ratio_config["height"]),
                "modelParameter[quality]": "1",
                "modelParameter[id]": model_id,
                "uploadPic": "",
                "productSource": "image"
            }

            headers = {
                "Accept": "application/json, text/plain, */*",
                "Content-Type": "application/x-www-form-urlencoded",
                "Cookie": self.cookies,
                "Origin": self.base_url,
                "Referer": f"{self.base_url}/front/aigc",
                "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36",
                "X-Requested-With": "mark.via",
                "Connection": "keep-alive",
                "Accept-Encoding": "gzip, deflate",
                "Accept-Language": "zh-CN,zh;q=0.9,en-US;q=0.8,en;q=0.7"
            }

            async with httpx.AsyncClient() as client:
                response = await client.post(f"{self.base_url}/aigc/generate", data=data, headers=headers)
                if response.status_code != 200:
                    logger.error(f"[BaiduDraw] 生成任务请求失败: {response.status_code}")
                    return None

                result = response.json()
                if result.get("status") != 0:
                    logger.error(f"[BaiduDraw] 生成任务创建失败: {result}")
                    return None

                # 获取任务信息
                taskid = result["taskid"]
                token = result["token"]
                timestamp = result["timestamp"]

                # 第二步：轮询查询生成结果
                query_url = f"{self.base_url}/aigc/query"
                params = {
                    "taskid": taskid,
                    "token": token,
                    "timestamp": timestamp,
                    "modelParameter[id]": model_id,
                    "modelParameter[quality]": "1",
                    "manual": "true",
                    "source": "wen_b_page",
                    "query": prompt,
                    "productSource": "image"
                }

                max_retries = 30  # 最大重试次数
                retry_interval = 2  # 重试间隔（秒）

                for _ in range(max_retries):
                    response = await client.get(query_url, params=params, headers=headers)
                    if response.status_code != 200:
                        continue

                    result = response.json()
                    if result.get("isGenerate") and result.get("progress") == 100:
                        return result

                    await asyncio.sleep(retry_interval)

                logger.error("[BaiduDraw] 生成超时")
                return None

        except Exception as e:
            logger.error(f"[BaiduDraw] 生成图片异常: {e}")
            logger.error(traceback.format_exc())
            return None

    async def _save_images(self, pic_arr: List[Dict[str, Any]]) -> List[str]:
        """保存生成的图片"""
        saved_paths = []

        try:
            async with httpx.AsyncClient() as client:
                for i, pic in enumerate(pic_arr):
                    src = pic.get("src")
                    if not src:
                        continue

                    # 下载图片
                    response = await client.get(src)
                    if response.status_code != 200:
                        continue

                    # 保存图片
                    image_data = response.content
                    filename = f"draw_{int(time.time())}_{i}.jpg"
                    image_path = self.temp_dir / filename

                    async with aiofiles.open(image_path, "wb") as f:
                        await f.write(image_data)

                    saved_paths.append(str(image_path))

        except Exception as e:
            logger.error(f"[BaiduDraw] 保存图片异常: {e}")
            logger.error(traceback.format_exc())

        return saved_paths

    @on_text_message
    async def handle_text(self, bot: WechatAPIClient, message: dict):
        """处理文本消息"""
        if not self.enable:
            return

        content = str(message["Content"]).strip()
        wxid = message["FromWxid"]
        user_wxid = message["SenderWxid"]

        # 检查是否是画画命令
        command = content.split(" ", 1)
        if command[0] not in self.command:
            return

        # 如果是"画画风格"命令，显示完整风格列表
        if command[0] == "画画风格":
            # 将风格列表分组展示，每行5个，以逗号分隔
            styled_list = []
            for i in range(0, len(self.styles), 5):
                group = self.styles[i:i+5]
                styled_list.append("、".join(group))

            styles_text = "\n".join(styled_list)
            await bot.send_at_message(
                wxid,
                f"🎨 百度AI绘画支持的风格列表：\n\n{styles_text}\n\n使用方法：画画 描述文本#模型#比例#风格",
                [user_wxid]
            )
            return

        # 如果没有提供描述文本
        if len(command) == 1:
            await bot.send_at_message(
                wxid,
                self.command_format,
                [user_wxid]
            )
            return

        try:
            # 检查用户请求限制
            wait_time = self._check_user_limit(wxid, user_wxid)
            if wait_time > 0:
                if self.natural_response:
                    rate_limit_msg = random.choice(self.rate_limit_msgs)
                    await bot.send_text_message(wxid, rate_limit_msg)
                else:
                    await bot.send_at_message(
                        wxid,
                        f"慢点慢点，等 {wait_time:.1f} 秒再来",
                        [user_wxid]
                    )
                return

            # 简单确认
            await self._simple_confirm(bot, wxid)

            # 发送处理中提示
            if not self.natural_response:
                await bot.send_at_message(
                    wxid,
                    "🎨 正在绘制中，稍等...",
                    [user_wxid]
                )

            # 解析命令参数
            parts = command[1].split("#") if len(command) > 1 else []
            if len(parts) >= 1:
                prompt = parts[0].strip()
                model = parts[1].strip() if len(parts) > 1 else None
                ratio = parts[2].strip() if len(parts) > 2 else None
                style = parts[3].strip() if len(parts) > 3 else None

                # 验证参数
                if model and model not in self.models:
                    if self.natural_response:
                        error_msg = random.choice(self.error_msgs)
                        await bot.send_text_message(wxid, error_msg)
                    else:
                        await bot.send_at_message(wxid, f"❌ 不支持的模型: {model}\n支持的模型: {', '.join(self.models.keys())}", [user_wxid])
                    return

                if ratio and ratio not in self.ratios:
                    if self.natural_response:
                        error_msg = random.choice(self.error_msgs)
                        await bot.send_text_message(wxid, error_msg)
                    else:
                        await bot.send_at_message(wxid, f"❌ 不支持的比例: {ratio}\n支持的比例: {', '.join(self.ratios.keys())}", [user_wxid])
                    return

                if style and style not in self.styles:
                    if self.natural_response:
                        error_msg = random.choice(self.error_msgs)
                        await bot.send_text_message(wxid, error_msg)
                    else:
                        await bot.send_at_message(wxid, f"❌ 不支持的风格: {style}\n支持的风格: {', '.join(self.styles)}", [user_wxid])
                    return

                # 生成图片
                result = await self._generate_image(prompt, model, ratio, style)

                if not result or not result.get("picArr"):
                    if self.natural_response:
                        error_msg = random.choice(self.error_msgs)
                        await bot.send_text_message(wxid, error_msg)
                    else:
                        await bot.send_at_message(
                            wxid,
                            "❌ 生成失败，请稍后重试",
                            [user_wxid]
                        )
                    return

                # 保存图片
                image_paths = await self._save_images(result["picArr"])

                if not image_paths:
                    if self.natural_response:
                        error_msg = random.choice(self.error_msgs)
                        await bot.send_text_message(wxid, error_msg)
                    else:
                        await bot.send_at_message(
                            wxid,
                            "❌ 保存图片失败，请稍后重试",
                            [user_wxid]
                        )
                    return

                # 发送图片
                for image_path in image_paths:
                    try:
                        # 将字符串路径转换为Path对象
                        path_obj = Path(image_path)
                        if not path_obj.exists():
                            logger.error(f"[BaiduDraw] 图片文件不存在: {image_path}")
                            continue

                        # 读取图片数据
                        async with aiofiles.open(path_obj, "rb") as f:
                            image_data = await f.read()

                        # 发送图片数据
                        await bot.send_image_message(wxid, image_data)
                        await asyncio.sleep(0.5)  # 添加发送间隔
                    except Exception as e:
                        logger.error(f"[BaiduDraw] 发送图片失败: {e}")
                        logger.error(traceback.format_exc())
                    finally:
                        # 删除临时文件
                        try:
                            if os.path.exists(image_path):
                                os.remove(image_path)
                        except Exception as e:
                            logger.warning(f"[BaiduDraw] 删除临时文件失败: {e}")

                # 图片发送完成，不需要额外的完成提示

        except Exception as e:
            logger.error(f"[BaiduDraw] 处理消息异常: {e}")
            logger.error(traceback.format_exc())
            if self.natural_response:
                error_msg = random.choice(self.error_msgs)
                await bot.send_text_message(wxid, error_msg)
            else:
                await bot.send_at_message(
                    wxid,
                    "出问题了，等会再试试",
                    [user_wxid]
                )

    def _check_user_limit(self, wxid: str, user_wxid: str) -> float:
        """检查用户是否可以发送请求"""
        current_time = time.time()

        if wxid not in self._user_limits:
            self._user_limits[wxid] = {}

        last_request = self._user_limits[wxid].get(user_wxid, 0)
        wait_time = max(0, self.min_request_interval - (current_time - last_request))

        if wait_time == 0:
            self._user_limits[wxid][user_wxid] = current_time

        return wait_time