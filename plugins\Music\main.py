import tomllib
import re
import httpx
import os
import time
import hashlib
import random
import asyncio
from loguru import logger

from WechatAPI import WechatAPIClient
from utils.decorators import *
from utils.plugin_base import PluginBase


class Music(PluginBase):
    description = "点歌与随机音乐"
    author = "XYBot"
    version = "1.1.0"

    def __init__(self):
        super().__init__()

        # 读取配置文件
        try:
            config_path = os.path.join(os.path.dirname(__file__), "config.toml")
            with open(config_path, "rb") as f:
                plugin_config = tomllib.load(f)
            config = plugin_config["Music"]
        except Exception as e:
            logger.error(f"[Music] 配置文件读取失败: {e}")
            raise

        # 基本配置初始化
        self.enable = config["enable"]
        self.command = config["command"]
        self.command_format = config["command-format"]
        self.random_command = config.get("random-command", ["随机音乐", "随机歌曲"])
        self.search_command = ["搜歌"]  # 新增搜歌命令

        # 搜索结果存储
        self.search_results = {}  # 用于存储每个群的搜索结果，格式：{wxid: {"result": result, "timestamp": time}}
        self.search_timeout = 300  # 搜索结果有效期（秒）

    @on_text_message
    async def handle_text(self, bot: WechatAPIClient, message: dict):
        """处理文本消息"""
        if not self.enable:
            return

        content = str(message["Content"]).strip()
        command = content.split(" ")

        # 处理随机音乐命令
        if command[0] in self.random_command:
            await self.handle_random_music(bot, message)
            return

        # 处理搜索结果的选择
        if content.startswith("听") and len(content) > 1:
            number = content[1:].strip()
            if not number.isdigit():
                return
            await self.handle_select_music(bot, message, int(number))
            return

        # 处理搜歌命令
        if command[0] in self.search_command:
            if len(command) == 1:
                await bot.send_at_message(message["FromWxid"],
                                      f"-----XYBot-----\n❌命令格式错误！\n搜歌 歌曲名",
                                      [message["SenderWxid"]])
                return
            song_name = content[len(command[0]):].strip()
            await self.handle_search_music(bot, message, song_name)
            return

        # 处理点歌命令
        if command[0] in self.command:
            if len(command) == 1:
                await bot.send_at_message(message["FromWxid"],
                                      f"-----XYBot-----\n❌命令格式错误！{self.command_format}",
                                      [message["SenderWxid"]])
                return

            song_name = content[len(command[0]):].strip()
            await self.handle_netease_music(bot, message, song_name)

    async def _generate_music_xml(self, title, singer, url, music_url, cover_url, lyric, bot_wxid):
        """生成统一格式的音乐XML消息"""
        # 使用微信官方域名
        official_url = "https://weixin.qq.com"
        xml = f"""<appmsg appid="wx485a97c844086dc9" sdkver="0"><title>{title}</title><des>{singer}</des><action>view</action><type>3</type><showtype>0</showtype><content/><url>{official_url}</url><dataurl>{music_url}</dataurl><lowurl>{official_url}</lowurl><lowdataurl>{music_url}</lowdataurl><recorditem/><thumburl>{cover_url}</thumburl><messageaction/><laninfo/><extinfo/><sourceusername/><sourcedisplayname/><songlyric>{lyric}</songlyric><commenturl/><appattach><totallen>0</totallen><attachid/><emoticonmd5/><fileext/><aeskey/></appattach><webviewshared><publisherId/><publisherReqId>0</publisherReqId></webviewshared><weappinfo><pagepath/><username/><appid/><appservicetype>0</appservicetype></weappinfo><websearch/><songalbumurl>{cover_url}</songalbumurl></appmsg><fromusername>{bot_wxid}</fromusername><scene>0</scene><appinfo><version>1</version><appname></appname></appinfo><commenturl/>"""
        return xml

    async def handle_netease_music(self, bot: WechatAPIClient, message: dict, song_name: str):
        """处理网易云音乐点歌"""
        try:
            # 直接使用搜歌API并选择第一首歌

            async with httpx.AsyncClient() as client:
                response = await client.get(
                    "https://api.dragonlongzhu.cn/api/dg_kugouSQ.php",
                    params={
                        "msg": song_name,
                        "n": "1",     # 直接指定选择第一首歌
                        "type": "json"
                    }
                )
                response.raise_for_status()
                # 记录原始响应内容
                raw_response = response.text

                data = response.json()

            # 检查响应结构
            if "code" not in data:
                logger.error(f"[Music] API响应缺少code字段: {data}")
                await bot.send_at_message(message["FromWxid"],
                                      f"-----XYBot-----\n❌点歌失败！API响应格式异常",
                                      [message["SenderWxid"]])
                return

            if data["code"] != 200:
                logger.error(f"[Music] API返回非成功状态码: {data['code']}")
                await bot.send_at_message(message["FromWxid"],
                                      f"-----XYBot-----\n❌点歌失败！\n{data}",
                                      [message["SenderWxid"]])
                return

            # 获取歌曲信息 - 适配新的API响应格式
            try:
                # 检查必要字段是否存在
                required_fields = ["title", "singer", "link", "music_url", "cover"]
                missing_fields = [field for field in required_fields if field not in data]

                if missing_fields:
                    logger.error(f"[Music] API响应缺少必要字段: {missing_fields}")
                    await bot.send_at_message(message["FromWxid"],
                                          f"-----XYBot-----\n❌点歌失败！歌曲信息不完整",
                                          [message["SenderWxid"]])
                    return

                title = data["title"]
                singer = data["singer"]
                url = data["link"]
                music_url = data["music_url"]
                cover_url = data["cover"]
                lyric = data.get("lyrics", "")  # 注意API返回的是lyrics而不是lyric


            except KeyError as ke:
                logger.error(f"[Music] 解析歌曲信息时缺少关键字段: {ke}")
                await bot.send_at_message(message["FromWxid"],
                                      f"-----XYBot-----\n❌点歌失败！歌曲信息不完整",
                                      [message["SenderWxid"]])
                return

            # 使用共用方法生成XML
            xml = await self._generate_music_xml(title, singer, url, music_url, cover_url, lyric, bot.wxid)
            await bot.send_app_message(message["FromWxid"], xml, 3)

        except httpx.HTTPError as e:
            logger.error(f"[Music] 音乐API请求失败: {e}")
            await bot.send_at_message(message["FromWxid"],
                                  f"-----XYBot-----\n❌点歌失败！请稍后重试",
                                  [message["SenderWxid"]])
        except ValueError as e:
            logger.error(f"[Music] JSON解析失败: {e}")
            await bot.send_at_message(message["FromWxid"],
                                  f"-----XYBot-----\n❌点歌失败！API返回数据格式错误",
                                  [message["SenderWxid"]])
        except Exception as e:
            logger.error(f"[Music] 点歌异常: {e}")
            logger.exception("[Music] 详细错误信息:")
            await bot.send_at_message(message["FromWxid"],
                                  f"-----XYBot-----\n❌点歌失败！发生未知错误",
                                  [message["SenderWxid"]])

    async def handle_search_music(self, bot: WechatAPIClient, message: dict, song_name: str):
        """处理搜歌命令"""
        # 使用发送人wxid作为key
        sender_wxid = message["SenderWxid"]
        # 清除该用户之前的搜索记录
        if sender_wxid in self.search_results:
            del self.search_results[sender_wxid]

        try:
            async with httpx.AsyncClient() as client:
                response = await client.get(
                    "https://api.dragonlongzhu.cn/api/dg_kugouSQ.php",
                    params={
                        "msg": song_name,
                        "type": "text",
                        "num": "8"
                    }
                )
                response.raise_for_status()
                search_result = response.text

            # 检查搜索结果
            if not search_result or "暂无搜索结果" in search_result:
                await bot.send_at_message(message["FromWxid"],
                                      f"❌未找到相关歌曲！",
                                      [message["SenderWxid"]])
                return

            # 保存搜索结果
            self.search_results[sender_wxid] = {
                "result": song_name,
                "timestamp": time.time(),
                "group_id": message["FromWxid"]
            }

            # 生成聊天记录形式的搜索结果
            await self._send_search_result_as_chat_record(bot, message, song_name, search_result)

        except httpx.HTTPError as e:
            logger.error(f"[Music] 搜索音乐API请求失败: {e}")
            await bot.send_at_message(message["FromWxid"],
                                  f"❌搜索失败！请稍后重试",
                                  [message["SenderWxid"]])
        except Exception as e:
            logger.error(f"[Music] 搜索音乐异常: {e}")
            await bot.send_at_message(message["FromWxid"],
                                  f"❌搜索失败！发生未知错误",
                                  [message["SenderWxid"]])

    async def handle_select_music(self, bot: WechatAPIClient, message: dict, number: int):
        """处理选择音乐命令"""
        sender_wxid = message["SenderWxid"]
        logger.debug(f"[Music] 处理选择音乐命令，用户: {sender_wxid}, 选择序号: {number}")

        # 检查是否有搜索结果
        if sender_wxid not in self.search_results:
            logger.debug(f"[Music] 用户 {sender_wxid} 没有搜索记录")
            await bot.send_at_message(message["FromWxid"],
                                  f"❌请先搜索歌曲！",
                                  [message["SenderWxid"]])
            return

        # 检查搜索结果是否过期
        search_data = self.search_results[sender_wxid]
        logger.debug(f"[Music] 用户搜索数据: {search_data}")

        if time.time() - search_data["timestamp"] > self.search_timeout:
            logger.debug(f"[Music] 搜索结果已过期，当前时间: {time.time()}, 搜索时间: {search_data['timestamp']}")
            del self.search_results[sender_wxid]
            await bot.send_at_message(message["FromWxid"],
                                  f"❌搜索结果已过期，请重新搜索！",
                                  [message["SenderWxid"]])
            return

        try:
            logger.debug(f"[Music] 开始获取歌曲，关键词: {search_data['result']}, 序号: {number}")
            async with httpx.AsyncClient() as client:
                response = await client.get(
                    "https://api.dragonlongzhu.cn/api/dg_kugouSQ.php",
                    params={
                        "msg": search_data["result"],
                        "n": str(number),
                        "type": "json"
                    }
                )
                response.raise_for_status()
                logger.debug(f"[Music] API请求成功，状态码: {response.status_code}")

                # 记录原始响应内容
                raw_response = response.text
                logger.debug(f"[Music] API原始响应: {raw_response}")

                data = response.json()
                logger.debug(f"[Music] API响应JSON解析成功: {data}")

            # 检查响应结构
            if "code" not in data:
                logger.error(f"[Music] API响应缺少code字段: {data}")
                await bot.send_at_message(message["FromWxid"],
                                      f"❌获取歌曲失败！API响应格式异常",
                                      [message["SenderWxid"]])
                return

            if data["code"] != 200:
                logger.error(f"[Music] API返回非成功状态码: {data['code']}")
                await bot.send_at_message(message["FromWxid"],
                                      f"❌获取歌曲失败！{data}",
                                      [message["SenderWxid"]])
                return

            # 获取歌曲信息 - 适配新的API响应格式
            try:
                # 检查必要字段是否存在
                required_fields = ["title", "singer", "link", "music_url", "cover"]
                missing_fields = [field for field in required_fields if field not in data]

                if missing_fields:
                    logger.error(f"[Music] API响应缺少必要字段: {missing_fields}")
                    await bot.send_at_message(message["FromWxid"],
                                          f"❌获取歌曲失败！歌曲信息不完整",
                                          [message["SenderWxid"]])
                    return

                title = data["title"]
                singer = data["singer"]
                url = data["link"]
                music_url = data["music_url"]
                cover_url = data["cover"]
                lyric = data.get("lyrics", "")  # 注意API返回的是lyrics而不是lyric

                logger.debug(f"[Music] 成功解析歌曲信息: 标题={title}, 歌手={singer}")
            except KeyError as ke:
                logger.error(f"[Music] 解析歌曲信息时缺少关键字段: {ke}")
                await bot.send_at_message(message["FromWxid"],
                                      f"❌获取歌曲失败！歌曲信息不完整",
                                      [message["SenderWxid"]])
                return

            # 使用共用方法生成XML
            logger.debug(f"[Music] 开始生成音乐XML")
            xml = await self._generate_music_xml(title, singer, url, music_url, cover_url, lyric, bot.wxid)
            logger.debug(f"[Music] 音乐XML生成成功，准备发送")
            await bot.send_app_message(message["FromWxid"], xml, 3)
            logger.debug(f"[Music] 音乐消息发送完成")

        except httpx.HTTPError as e:
            logger.error(f"[Music] 获取音乐API请求失败: {e}")
            await bot.send_at_message(message["FromWxid"],
                                  f"❌获取歌曲失败！请重试",
                                  [message["SenderWxid"]])
        except ValueError as e:
            logger.error(f"[Music] JSON解析失败: {e}")
            await bot.send_at_message(message["FromWxid"],
                                  f"❌获取歌曲失败！API返回数据格式错误",
                                  [message["SenderWxid"]])
        except Exception as e:
            logger.error(f"[Music] 获取音乐异常: {e}")
            logger.exception("[Music] 详细错误信息:")
            await bot.send_at_message(message["FromWxid"],
                                  f"❌获取歌曲失败！发生未知错误",
                                  [message["SenderWxid"]])

    async def handle_random_music(self, bot: WechatAPIClient, message: dict):
        """处理随机音乐命令"""
        try:
            logger.debug(f"[Music] 开始获取随机音乐")
            async with httpx.AsyncClient() as client:
                response = await client.get("https://www.hhlqilongzhu.cn/api/wangyi_hot_review.php")
                response.raise_for_status()
                logger.debug(f"[Music] 随机音乐API请求成功，状态码: {response.status_code}")

                # 记录原始响应内容
                raw_response = response.text
                logger.debug(f"[Music] 随机音乐API原始响应: {raw_response}")

                data = response.json()
                logger.debug(f"[Music] 随机音乐API响应JSON解析成功: {data}")

            # 检查响应结构
            if "code" not in data:
                logger.error(f"[Music] 随机音乐API响应缺少code字段: {data}")
                await bot.send_at_message(message["FromWxid"],
                                      f"-----XYBot-----\n❌获取随机音乐失败！API响应格式异常",
                                      [message["SenderWxid"]])
                return

            if data["code"] != 200:
                logger.error(f"[Music] 随机音乐API返回非成功状态码: {data['code']}")
                await bot.send_at_message(message["FromWxid"],
                                      f"-----XYBot-----\n❌获取随机音乐失败！\n{data}",
                                      [message["SenderWxid"]])
                return

            # 获取歌曲信息
            try:
                # 检查必要字段是否存在
                required_fields = ["song", "singer", "link", "url", "img"]
                for field in required_fields:
                    if field not in data:
                        logger.error(f"[Music] 随机音乐API响应缺少必要字段 {field}: {data}")
                        await bot.send_at_message(message["FromWxid"],
                                              f"-----XYBot-----\n❌获取随机音乐失败！歌曲信息不完整",
                                              [message["SenderWxid"]])
                        return

                title = data["song"]
                singer = data["singer"]
                url = data["link"]
                music_url = data["url"]
                cover_url = data["img"]
                lyric = ""  # 随机音乐API没有返回歌词

                logger.debug(f"[Music] 成功解析随机歌曲信息: 标题={title}, 歌手={singer}")
            except KeyError as ke:
                logger.error(f"[Music] 解析随机歌曲信息时缺少关键字段: {ke}")
                await bot.send_at_message(message["FromWxid"],
                                      f"-----XYBot-----\n❌获取随机音乐失败！歌曲信息不完整",
                                      [message["SenderWxid"]])
                return

            # 使用共用方法生成XML
            logger.debug(f"[Music] 开始生成随机音乐XML")
            xml = await self._generate_music_xml(title, singer, url, music_url, cover_url, lyric, bot.wxid)
            logger.debug(f"[Music] 随机音乐XML生成成功，准备发送")
            await bot.send_app_message(message["FromWxid"], xml, 3)
            logger.debug(f"[Music] 随机音乐消息发送完成")

        except httpx.HTTPError as e:
            logger.error(f"[Music] 随机音乐API请求失败: {e}")
            await bot.send_at_message(message["FromWxid"],
                                  f"-----XYBot-----\n❌获取随机音乐失败！请稍后重试",
                                  [message["SenderWxid"]])
        except ValueError as e:
            logger.error(f"[Music] 随机音乐JSON解析失败: {e}")
            await bot.send_at_message(message["FromWxid"],
                                  f"-----XYBot-----\n❌获取随机音乐失败！API返回数据格式错误",
                                  [message["SenderWxid"]])
        except Exception as e:
            logger.error(f"[Music] 随机音乐异常: {e}")
            logger.exception("[Music] 随机音乐详细错误信息:")
            await bot.send_at_message(message["FromWxid"],
                                  f"-----XYBot-----\n❌获取随机音乐失败！发生未知错误",
                                  [message["SenderWxid"]])

    async def _send_search_result_as_chat_record(self, bot: WechatAPIClient, message: dict, song_name: str, search_result: str):
        """将搜索结果以聊天记录形式发送"""
        try:
            # 解析搜索结果，生成聊天记录格式
            chat_records = self._parse_search_result_to_chat_records(search_result, song_name)

            # 生成聊天记录描述
            description = self._generate_search_description(chat_records)

            # 生成聊天记录XML
            record_xml = await self._generate_search_record_xml(chat_records)

            # 聊天记录消息XML格式 (Type 19)
            xml = f'''<appmsg appid="" sdkver="0">
<title>🎵 {song_name} - 搜索结果</title>
<des>{description}</des>
<action>view</action>
<type>19</type>
<showtype>0</showtype>
<content/>
<url>https://support.weixin.qq.com/cgi-bin/mmsupport-bin/readtemplate?t=page/favorite_record__w_unsupport&amp;from=singlemessage&amp;isappinstalled=0</url>
<lowurl/>
<dataurl/>
<lowdataurl/>
<recorditem><![CDATA[{record_xml}]]></recorditem>
<appattach>
    <totallen>0</totallen>
    <attachid/>
    <emoticonmd5/>
    <fileext/>
    <aeskey/>
</appattach>
<extinfo/>
<sourceusername/>
<sourcedisplayname/>
<thumburl/>
<md5/>
</appmsg>
<fromusername>{bot.wxid}</fromusername>
<scene>0</scene>
<appinfo>
    <version>1</version>
    <appname></appname>
</appinfo>
<commenturl/>'''

            await bot.send_app_message(message["FromWxid"], xml, 19)

        except Exception as e:
            logger.error(f"[Music] 发送搜索结果聊天记录失败: {e}")
            # 降级为文本消息
            await bot.send_at_message(message["FromWxid"],
                                  f"🎵搜索结果：\n{search_result}",
                                  [message["SenderWxid"]])

    def _parse_search_result_to_chat_records(self, search_result: str, song_name: str) -> list:
        """将搜索结果解析为聊天记录格式"""
        chat_records = []

        # 添加标题消息
        chat_records.append({
            'username': '音乐助手',
            'message': f'为您找到 "{song_name}" 的相关歌曲：',
            'index': 0
        })

        # 将所有搜索结果合并到一个消息中
        lines = search_result.strip().split('\n')
        merged_results = []
        for line in lines:
            if line.strip():
                clean_line = line.strip()
                if clean_line:
                    merged_results.append(clean_line)

        # 合并所有搜索结果到一个聊天记录
        if merged_results:
            chat_records.append({
                'username': '搜索结果',
                'message': '\n'.join(merged_results),
                'index': 1
            })

        # 添加使用说明
        chat_records.append({
            'username': '使用提示',
            'message': '发送 "听1" 到 "听8" 选择对应歌曲',
            'index': 2
        })

        return chat_records

    def _generate_search_description(self, chat_records: list) -> str:
        """生成搜索结果描述"""
        desc_lines = []
        for record in chat_records[:3]:  # 只显示前3条作为描述
            desc_lines.append(f"{record['username']}: {record['message']}")
        if len(chat_records) > 3:
            desc_lines.append("...")
        return '\n'.join(desc_lines)

    async def _generate_search_record_xml(self, chat_records: list) -> str:
        """生成搜索结果的聊天记录XML内容"""
        # 生成描述（HTML编码）
        desc_parts = []
        for record in chat_records:
            desc_parts.append(f"{record['username']}:&#x20;{record['message']}")
        desc = '&#x0A;'.join(desc_parts)

        # 预定义头像URL - 三个角色都使用同一个默认头像
        default_avatar = 'https://wx.qlogo.cn/mmhead/ver_1/icq2icXbhxGESInLwSiaTeZFSiaic8CXB3obiaQMoGVOtXeial73slIhfQ7w1KhFjfDZecibrNYjEOlKWicOzRVWoTmQrHB3xOWpQESaZ91d5fbjfNz99iaictzSnYNl6nddd5wXwY0E3zPKq4NA3EOLqJyrzPAtQ/132'

        avatar_urls = {
            '音乐助手': default_avatar,
            '� 搜索结果': default_avatar,
            '使用提示': default_avatar
        }

        # 生成数据项
        data_items = []
        current_time = int(time.time())

        for record in chat_records:
            # 生成随机ID
            data_id = hashlib.md5(f"{record['username']}{record['message']}{record['index']}".encode()).hexdigest()
            source_id = str(random.randint(1000000000000000000, 9999999999999999999))

            # 所有角色都使用同一个默认头像
            user_avatar = default_avatar

            # 生成用户hash
            user_hash = hashlib.sha256(f"{record['username']}_music_search".encode()).hexdigest()

            # 时间设置
            time_offset = record['index'] * 30  # 每条消息间隔30秒
            message_time = current_time + time_offset

            data_item = f'''<dataitem datatype="1" dataid="{data_id}" datasourceid="{source_id}">
<datadesc>{record['message']}</datadesc>
<sourcename>{record['username']}</sourcename>
<sourceheadurl>{user_avatar}</sourceheadurl>
<sourcetime>2025-07-21&#x20;{12 + record['index'] // 60:02d}:{record['index'] % 60:02d}:00</sourcetime>
<srcMsgCreateTime>{message_time}</srcMsgCreateTime>
<fromnewmsgid>{source_id}</fromnewmsgid>
<dataitemsource><hashusername>{user_hash}</hashusername></dataitemsource>
</dataitem>'''
            data_items.append(data_item)

        # 组装完整的recordinfo XML
        record_xml = f'''<recordinfo>
<title>🎵 音乐搜索结果</title>
<desc>{desc}</desc>
<datalist count="{len(chat_records)}">
{''.join(data_items)}
</datalist>
<favcreatetime>{int(time.time() * 1000)}</favcreatetime>
</recordinfo>'''

        return record_xml