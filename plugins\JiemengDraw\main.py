import os, json, httpx, asyncio, time, uuid, random
try: import tomllib
except: import tomli as tomllib
from pathlib import Path
from typing import Optional, Dict, Any, List
from WechatAPI import WechatAPIClient
from utils.decorators import on_text_message
from utils.plugin_base import PluginBase
from utils.temp_file_manager import cleanup_file

class JiemengDraw(PluginBase):
    description = "即梦AI绘画插件"
    author = "XYBot"
    version = "1.0.0"
    plugin_name = "JiemengDraw"

    def __init__(self):
        super().__init__()
        self.temp_dir = Path("temp/jiemeng_draw")
        self.temp_dir.mkdir(parents=True, exist_ok=True)

        try:
            with open("plugins/JiemengDraw/config.toml", "rb") as f:
                config = tomllib.load(f).get("JiemengDraw", {})
        except:
            config = {}

        self.enable = config.get("enable", True)
        self.command = config.get("command", ["即梦", "jm"])
        self.command_format = config.get("command-format", "⚙️即梦AI绘画使用说明：\n\n🎨生成图片：\n即梦 <详细描述文本>\njm <详细描述文本>\n\n📝描述建议：\n- 尽可能详细描述场景、人物、动作、环境等\n- 提示词越详细，生成效果越好\n- 建议使用中文描述")

        self.api_config = config.get("API", {})
        self.cookies = self._parse_cookies(self.api_config.get("cookies", ""))

        self.dreamina_api_url = "https://dreamina-app-hl.jianying.com"
        self.device_id = "86229099759449"
        self.iid = "86229099218793"
        self.base_headers = {"Connection": "keep-alive", "lan": "zh-hans", "loc": "cn", "pf": "0", "vr": "*********", "appvr": "1.3.8", "tdid": "86229099759449", "sign-ver": "1", "appid": "581595", "ac": "wifi", "Cache-Control": "no-cache", "sysvr": "29", "ch": "oppo_64_581595", "uid": "329468416627917", "COMPRESSED": "1", "did": "00000000-50f9-90d5-ffff-ffffef05ac4a", "model": "UGl4ZWw=", "manu": "R29vZ2xl", "GPURender": "QWRyZW5vIChUTSkgNzUw", "HDR-TDID": "86229099759449", "HDR-TIID": "86229099218793", "version_code": "*********", "HDR-Sign-Ver": "1", "x-vc-bdturing-sdk-version": "3.7.2.cn", "sdk-version": "2", "X-Tt-Token": "00a00b34aff3a919ca8990897d72a6685d04401ee9474e28c90221f554153c0bdd530fb8e99a032de30269120da2b4868ae53f9e54f0bd7e0dad469840d90199f9dcc3d5c00a9f1aa9a6f954669c4fcc557868d982f18a88c0b6303130a5f6bd2c9b5-1.0.1", "passport-sdk-version": "50561", "commerce-sign-version": "v1", "User-Agent": "com.bytedance.dreamina/1381600 (Linux; U; Android 10; zh_CN; Pixel; Build/NHG47O; Cronet/TTNetVersion:22f14a0c 2024-07-09 QuicVersion:46688bb4 2022-11-28)", "Accept-Encoding": "gzip, deflate"}

        self.rate_limit = config.get("rate_limit", {"tokens_per_second": 0.1, "bucket_size": 3})
        self._token_bucket = self.rate_limit["bucket_size"]
        self._last_token_time = time.time()
        self._token_lock = asyncio.Lock()
        self._user_limits = {}
        self.min_request_interval = 30.0

        self.international_enable = config.get("international_enable", False)
        self.international_command = config.get("international_command", ["即梦国际", "jmi"])
        self.international_api_config = config.get("InternationalAPI", {})
        self.international_cookies = self._parse_cookies(self.international_api_config.get("cookies", ""))
        self.international_region = self.international_api_config.get("region", "HK")
        self.international_api_url = "https://mweb-api-sg.capcut.com"
        self.international_headers = {"pf": "7", "User-Agent": "Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/96.0.4664.104 Safari/537.36 HeyTapBrowser/51.8.8", "Content-Type": "application/json", "Accept": "application/json, text/plain, */*", "sign-ver": "1", "appvr": "5.8.0", "Connection": "keep-alive", "Origin": "https://dreamina.capcut.com", "Referer": "https://dreamina.capcut.com/", "Sec-Fetch-Site": "same-site", "Sec-Fetch-Mode": "cors", "Sec-Fetch-Dest": "empty", "Accept-Encoding": "gzip, deflate, br", "Accept-Language": "zh-CN,zh;q=0.9,en-US;q=0.8,en;q=0.7", "X-Requested-With": "XMLHttpRequest"}
        self.international_api_params = {"aid": "513641", "device_platform": "web", "region": self.international_region}
        self.international_web_id = self.international_api_config.get("web_id", "")
        self.international_sign = self.international_api_config.get("sign", "")

        self.natural_response = config.get("natural_response", True)
        self._init_natural_responses()

        image_send_config = config.get("image_send", {})
        self.max_retries = image_send_config.get("max_retries", 3)
        self.send_timeout = image_send_config.get("timeout", 60)

    def _init_natural_responses(self):
        self.confirm_msgs = ["好的", "收到", "明白了", "嗯", "知道了", "行", "OK"]
        self.error_msgs = ["这可把我难住了", "搞不定了", "这个有点难", "弄不了", "出问题了", "搞不出来", "这个不行", "翻车了", "炸了炸了", "画不出来啊", "这下尴尬了", "画崩了", "暂时歇菜了", "我也没办法", "搞砸了", "这次不行", "失败了", "画不动了", "卡住了", "弄错了", "没成功"]
        self.rate_limit_msgs = ["你搁这刷bug呢", "慢点慢点", "别这么急", "一个一个来", "太快了太快了", "歇会儿", "别刷了"]

    async def _simple_confirm(self, bot, wxid):
        if self.natural_response:
            await bot.send_text_message(wxid, random.choice(self.confirm_msgs))

    def _parse_cookies(self, cookies_str: str) -> Dict[str, str]:
        if not cookies_str: return {}
        try:
            return {parts[0].strip(): parts[1].strip() for item in cookies_str.split(';') if item.strip() and len(parts := item.split('=', 1)) == 2}
        except:
            return {}

    def _get_base_params(self):
        return {"iid": self.iid, "device_id": self.device_id, "ac": "wifi", "channel": "oppo_64_581595", "aid": "581595", "app_name": "dreamina", "version_code": "1381600", "version_name": "1.3.8", "device_platform": "android", "os": "android", "ssmix": "a", "device_type": "Pixel", "device_brand": "Google", "language": "zh", "os_api": "29", "os_version": "10", "manifest_version_code": "1381600", "resolution": "1080*2232", "dpi": "480", "update_version_code": "1381600", "_rticket": str(int(time.time() * 1000)), "cdid": "21c50631-efc2-40e8-930d-c989ffa79a98", "region": "cn", "aigc_flow_version": "3.1.0", "aigc_flow_support_features": "AIGC_BlendAbility_twoFace,AIGC_GenerateType_AI_Effect"}

    async def _generate_image(self, prompt: str, ratio: float = 1, size: int = 1024) -> Optional[tuple]:
        try:
            params = self._get_base_params()
            params["babi_param"] = "%7B%22scenario%22%3A%22image_video_generation%22%2C%22feature_key%22%3A%22text_to_image%22%2C%22feature_entrance%22%3A%22to_image%22%2C%22feature_entrance_detail%22%3A%22to_image-text_to_image%22%7D"
            headers = self._update_headers_time(self.base_headers.copy())
            headers.update({"Content-Type": "application/json; charset=utf-8", "Host": "dreamina-app-hl.jianying.com"})

            seed = random.randint(1000000000, 2000000000)
            draft_id, component_id, ability_id, generate_id, core_param_id = [str(uuid.uuid4()) for _ in range(5)]
            width, height = (size, int(size / ratio)) if ratio >= 1 else (int(size * ratio), size)
            width, height = (width // 8) * 8, (height // 8) * 8

            draft_content = {"type": "draft", "id": draft_id, "min_version": "3.0.2", "min_features": [], "is_from_tsn": True, "version": "3.1.0", "main_component_id": component_id, "component_list": [{"type": "image_base_component", "id": component_id, "min_version": "3.0.2", "generate_type": "generate", "aigc_mode": "workbench", "abilities": {"type": "", "id": ability_id, "generate": {"type": "", "id": generate_id, "core_param": {"type": "", "id": core_param_id, "model": "high_aes_general_v30l_art_fangzhou:general_v3.0_18b", "prompt": prompt, "seed": seed, "sample_strength": 0.5, "image_ratio": 5, "large_image_info": {"type": "", "id": str(uuid.uuid4()), "height": height, "width": width}}}}}]}
            submit_id = f"581595_{str(uuid.uuid4())}"
            data = {"draft_content": json.dumps(draft_content), "extend": {"root_model": "high_aes_general_v30l_art_fangzhou:general_v3.0_18b"}, "submit_id": submit_id}

            async with httpx.AsyncClient(timeout=httpx.Timeout(30.0), verify=False) as client:
                response = await client.post(f"{self.dreamina_api_url}/mweb/v1/aigc_draft/generate", params=params, headers=headers, cookies=self.cookies, json=data)
                if response.status_code != 200: return None, None
                result = response.json()
                if result.get("ret") != "0": return None, None
                data = result.get("data", {})
                history_id = data.get("history_record_id") or data.get("task_id") or data.get("aigc_data", {}).get("history_record_id")
                return submit_id, history_id
        except:
            return None, None

    async def _get_history_id_by_submit_id(self, submit_id: str) -> Optional[str]:
        try:
            headers = self._update_headers_time(self.base_headers.copy())
            headers.update({"Content-Type": "application/json; charset=utf-8", "Host": "dreamina-app-hl.jianying.com"})
            async with httpx.AsyncClient(timeout=httpx.Timeout(30.0), verify=False) as client:
                response = await client.post(f"{self.dreamina_api_url}/mweb/v1/get_history_by_submit_id", params=self._get_base_params(), headers=headers, cookies=self.cookies, json={"submit_id": submit_id})
                if response.status_code != 200: return None
                result = response.json()
                return result.get("data", {}).get("history_id") if result.get("ret") == "0" else None
        except:
            return None

    async def _get_latest_history_id(self) -> Optional[str]:
        try:
            headers = self._update_headers_time(self.base_headers.copy())
            headers.update({"Content-Type": "application/json; charset=utf-8", "Host": "dreamina-app-hl.jianying.com"})
            async with httpx.AsyncClient(timeout=httpx.Timeout(30.0), verify=False) as client:
                response = await client.post(f"{self.dreamina_api_url}/mweb/v1/get_history_list", params=self._get_base_params(), headers=headers, cookies=self.cookies, json={"page_num": 1, "page_size": 5})
                if response.status_code != 200: return None
                result = response.json()
                if result.get("ret") != "0": return None
                history_list = result.get("data", {}).get("history_list", [])
                return history_list[0].get("history_record_id") if history_list else None
        except:
            return None

    async def _poll_task_status(self, history_id: str) -> Optional[Dict[str, Any]]:
        headers = self._update_headers_time(self.base_headers.copy())
        headers.update({"Content-Type": "application/json; charset=utf-8", "Host": "dreamina-app-hl.jianying.com"})

        for _ in range(30):
            try:
                async with httpx.AsyncClient(timeout=httpx.Timeout(30.0), verify=False) as client:
                    response = await client.post(f"{self.dreamina_api_url}/mweb/v1/get_history_by_ids", params=self._get_base_params(), headers=headers, cookies=self.cookies, json={"history_ids": [history_id]})
                    if response.status_code != 200:
                        await asyncio.sleep(2)
                        continue
                    result = response.json()
                    if result.get("ret") != "0": return None
                    history_data = result.get("data", {}).get(history_id, {})
                    if history_data.get("status") == 50: return history_data
                    await asyncio.sleep(2)
            except:
                await asyncio.sleep(2)
        return None

    async def _download_images(self, history_data: Dict[str, Any]) -> List[str]:
        item_list = history_data.get("item_list", [])
        if not item_list: return []

        image_paths = []
        async with httpx.AsyncClient(timeout=httpx.Timeout(30.0), verify=False) as client:
            for idx, item in enumerate(item_list):
                large_images = item.get("image", {}).get("large_images", [])
                if not large_images: continue

                # 获取最佳图片URL
                best_url = next((img.get("image_url") for img in large_images if img.get("image_url") and "aigc_resize:0:0" in img.get("image_url")), None)
                if not best_url:
                    cover_map = item.get("common_attr", {}).get("cover_url_map", {})
                    best_url = cover_map.get("2400") or cover_map.get("1080") or cover_map.get("720") or (large_images[0].get("image_url") if large_images else None)

                if not best_url: continue

                # 下载图片
                for _ in range(3):
                    try:
                        await asyncio.sleep(0.5 + random.random())
                        response = await client.get(best_url)
                        if response.status_code == 200 and len(response.content) > 100:
                            temp_path = await self._save_image(response.content, f"jiemeng_{idx}_")
                            if temp_path:
                                image_paths.append(temp_path)
                                break
                    except:
                        continue
        return image_paths



    async def _generate_and_download(self, prompt: str, ratio: float = 1, size: int = 1024) -> List[str]:
        submit_id, task_id = await self._generate_image(prompt, ratio, size)
        if not submit_id: return []

        await asyncio.sleep(5)
        history_id = task_id or await self._get_history_id_by_submit_id(submit_id) or await self._get_latest_history_id()
        if not history_id: return []

        history_data = await self._poll_task_status(history_id)
        return await self._download_images(history_data) if history_data else []

    def _update_headers_time(self, headers: Dict[str, str]) -> Dict[str, str]:
        timestamp = str(int(time.time()))
        headers.update({"device-time": timestamp, "HDR-Device-Time": timestamp})
        return headers

    async def _handle_command(self, bot, wxid, user_wxid, prompt, is_international=False):
        wait_time = self._check_user_limit(wxid, user_wxid)
        if wait_time > 0:
            msg = random.choice(self.rate_limit_msgs) if self.natural_response else f"慢点慢点，等 {wait_time:.1f} 秒再来"
            await (bot.send_text_message(wxid, msg) if self.natural_response else bot.send_at_message(wxid, msg, [user_wxid]))
            return

        if not await self._acquire_token():
            msg = random.choice(self.rate_limit_msgs) if self.natural_response else "忙不过来了，歇会儿"
            await (bot.send_text_message(wxid, msg) if self.natural_response else bot.send_at_message(wxid, msg, [user_wxid]))
            return

        await self._simple_confirm(bot, wxid)
        prompt, ratio, size = self._parse_command_args(prompt)

        try:
            if is_international:
                submit_id, history_id = await self._generate_image_international(prompt, ratio, size)
                if not submit_id or not history_id: raise Exception()
                history_data = await self._poll_task_status_international(history_id)
                if not history_data: raise Exception()
                image_paths = await self._download_images_international(history_data)
            else:
                image_paths = await self._generate_and_download(prompt, ratio, size)
        except:
            msg = random.choice(self.error_msgs) if self.natural_response else "画不出来了，等会再试试吧"
            await (bot.send_text_message(wxid, msg) if self.natural_response else bot.send_at_message(wxid, msg, [user_wxid]))
            return

        if not image_paths:
            msg = random.choice(self.error_msgs) if self.natural_response else "这次画废了，换个描述试试？"
            await (bot.send_text_message(wxid, msg) if self.natural_response else bot.send_at_message(wxid, msg, [user_wxid]))
            return

        success_count = 0
        for img_path in image_paths:
            for _ in range(self.max_retries):
                try:
                    if not os.path.exists(img_path) or os.path.getsize(img_path) < 1000: break
                    with open(img_path, 'rb') as f:
                        image_data = f.read()
                    await asyncio.sleep(1.0 + random.random() * 0.5)
                    if await bot.send_image_message(wxid, image_data):
                        success_count += 1
                        cleanup_file(img_path, delay_seconds=60)
                        break
                except:
                    await asyncio.sleep(2)

        if success_count == 0:
            msg = random.choice(self.error_msgs) if self.natural_response else "图片发不出去，网络有点问题"
            await (bot.send_text_message(wxid, msg) if self.natural_response else bot.send_at_message(wxid, msg, [user_wxid]))

    @on_text_message
    async def handle_text(self, bot: WechatAPIClient, message: dict):
        if not self.enable and not self.international_enable: return

        content = str(message["Content"]).strip()
        wxid, user_wxid = message["FromWxid"], message["SenderWxid"]
        command = content.split(" ", 1)

        if self.enable and command[0] in self.command:
            if len(command) == 1:
                await bot.send_at_message(wxid, self.command_format, [user_wxid])
                return
            await self._handle_command(bot, wxid, user_wxid, " ".join(command[1:]), False)

        if self.international_enable and command[0] in self.international_command:
            if len(command) == 1:
                await bot.send_at_message(wxid, f"⚙️即梦国际版AI绘画使用说明：\n\n🎨生成图片：\n{self.international_command[0]} <详细描述文本>\n\n📝描述建议：\n- 尽可能详细描述场景、人物、动作、环境等\n- 提示词越详细，生成效果越好", [user_wxid])
                return
            await self._handle_command(bot, wxid, user_wxid, " ".join(command[1:]), True)

    async def _acquire_token(self) -> bool:
        async with self._token_lock:
            current_time = time.time()
            elapsed = current_time - self._last_token_time
            self._token_bucket = min(self.rate_limit["bucket_size"], self._token_bucket + elapsed * self.rate_limit["tokens_per_second"])
            self._last_token_time = current_time
            if self._token_bucket >= 1:
                self._token_bucket -= 1
                return True
            return False

    def _check_user_limit(self, wxid: str, user_wxid: str) -> float:
        current_time = time.time()
        if wxid not in self._user_limits: self._user_limits[wxid] = {}
        last_request = self._user_limits[wxid].get(user_wxid, 0)
        wait_time = max(0, self.min_request_interval - (current_time - last_request))
        if wait_time == 0: self._user_limits[wxid][user_wxid] = current_time
        return wait_time

    async def _save_image(self, image_data: bytes, prefix: str = "jiemeng") -> str:
        import tempfile
        temp = tempfile.NamedTemporaryFile(prefix=prefix, suffix=".jpg", delete=False)
        temp.write(image_data)
        temp.close()
        return temp.name

    async def _generate_image_international(self, prompt: str, ratio: float = 1, size: int = 1024) -> Optional[tuple]:
        try:
            babi_param = {"scenario": "image_video_generation", "feature_key": "aigc_to_image", "feature_entrance": "to_image", "feature_entrance_detail": "to_image-high_aes_general_v30l_art_fangzhou:general_v3.0_18b"}
            params = {"babi_param": json.dumps(babi_param).replace(" ", ""), "aid": self.international_api_params["aid"], "device_platform": self.international_api_params["device_platform"], "region": self.international_api_params["region"], "web_id": self.international_web_id, "sign": self.international_sign, "_rticket": str(int(time.time() * 1000))}

            headers = self.international_headers.copy()
            headers.update({"device-time": str(int(time.time())), "web-id": self.international_web_id, "sign": self.international_sign})

            seed = random.randint(1000000000, 2000000000)
            draft_id, component_id, ability_id, generate_id, core_param_id, large_image_info_id, history_option_id = [str(uuid.uuid4()) for _ in range(7)]
            width, height = (size, int(size / ratio)) if ratio >= 1 else (int(size * ratio), size)
            width, height = (width // 8) * 8, (height // 8) * 8

            draft_content = {"type": "draft", "id": draft_id, "min_version": "3.0.2", "min_features": [], "is_from_tsn": True, "version": "3.1.3", "main_component_id": component_id, "component_list": [{"type": "image_base_component", "id": component_id, "min_version": "3.0.2", "generate_type": "generate", "aigc_mode": "workbench", "abilities": {"type": "", "id": ability_id, "generate": {"type": "", "id": generate_id, "core_param": {"type": "", "id": core_param_id, "model": "high_aes_general_v30l_art_fangzhou:general_v3.0_18b", "prompt": prompt, "negative_prompt": "", "seed": seed, "sample_strength": 0.5, "image_ratio": 1, "large_image_info": {"type": "", "id": large_image_info_id, "height": height, "width": width}}, "history_option": {"type": "", "id": history_option_id}}}}]}
            submit_id = str(uuid.uuid4())
            data = {"extend": {"root_model": "high_aes_general_v30l_art_fangzhou:general_v3.0_18b", "template_id": ""}, "submit_id": submit_id, "metrics_extra": json.dumps({"templateId": "", "generateCount": 3, "promptSource": "custom", "templateSource": "", "lastRequestId": "", "originRequestId": ""}), "draft_content": json.dumps(draft_content), "http_common_info": {"aid": int(self.international_api_params["aid"])}}

            async with httpx.AsyncClient(timeout=httpx.Timeout(30.0), verify=False) as client:
                response = await client.post(f"{self.international_api_url}/mweb/v1/aigc_draft/generate", params=params, headers=headers, cookies=self.international_cookies, json=data)
                if response.status_code != 200: return None, None
                result = response.json()
                if result.get("ret") != "0": return None, None
                data = result.get("data", {}).get("aigc_data", {})
                return submit_id, data.get("history_record_id")
        except:
            return None, None

    async def _poll_task_status_international(self, history_id: str) -> Optional[Dict[str, Any]]:
        params = {"aid": self.international_api_params["aid"], "device_platform": self.international_api_params["device_platform"], "region": self.international_api_params["region"], "web_id": self.international_web_id, "sign": self.international_sign, "_rticket": str(int(time.time() * 1000))}
        headers = self.international_headers.copy()
        headers.update({"device-time": str(int(time.time())), "web-id": self.international_web_id, "sign": self.international_sign})

        for _ in range(30):
            try:
                async with httpx.AsyncClient(timeout=httpx.Timeout(30.0), verify=False) as client:
                    response = await client.post(f"{self.international_api_url}/mweb/v1/get_history_by_ids", params=params, headers=headers, cookies=self.international_cookies, json={"history_ids": [history_id]})
                    if response.status_code != 200:
                        await asyncio.sleep(2)
                        continue
                    result = response.json()
                    if result.get("ret") != "0": return None
                    history_data = result.get("data", {}).get(history_id, {})
                    if history_data.get("status") == 50: return history_data
                    await asyncio.sleep(2)
            except:
                await asyncio.sleep(2)
        return None

    async def _download_images_international(self, history_data: Dict[str, Any]) -> List[str]:
        return await self._download_images(history_data)

    def _parse_command_args(self, command_text: str) -> tuple:
        ratio, size, prompt_parts = 1, 1024, []
        for part in command_text.split():
            if ":" in part and part.replace(":", "").isdigit():
                try: w, h = part.split(":"); ratio = float(w) / float(h)
                except: prompt_parts.append(part)
            elif part.isdigit() and 256 <= int(part) <= 2048:
                size = int(part)
            else:
                prompt_parts.append(part)
        return " ".join(prompt_parts), ratio, size