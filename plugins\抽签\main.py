import httpx, time, random, re
try:
    import tomllib
except ModuleNotFoundError:
    import tomli as tomllib
from loguru import logger
from pathlib import Path
from WechatAPI import WechatAPIClient
from utils.decorators import on_text_message
from utils.plugin_base import PluginBase

class 抽签(PluginBase):
    description = "抽签占卜插件"
    author = "XYBot开发者"
    version = "1.0.0"
    plugin_name = "抽签"

    def __init__(self):
        super().__init__()
        self.temp_dir = Path("plugins/抽签/temp")
        self.temp_dir.mkdir(parents=True, exist_ok=True)
        self._load_config()
        self.user_last_request = {}

    def _load_config(self):
        try:
            with open(f"plugins/{self.plugin_name}/config.toml", "rb") as f:
                config = tomllib.load(f).get(self.plugin_name, {})
        except:
            config = {}

        self.enable = config.get("enable", True)
        self.command = config.get("command", ["抽签"])
        self.command_format = config.get("command-format", "使用说明")
        self.cooldown = config.get("rate_limit", {}).get("cooldown", 5)
        
        # API配置
        api_config = config.get("api", {})
        self.api_url = api_config.get("url", "https://api.dragonlongzhu.cn/api/yl_qiuqian.php")
        self.api_timeout = api_config.get("timeout", 10)
        
        # 查询类插件，不启用自然响应
        self.natural_response = config.get("natural_response", False)

    @on_text_message
    async def handle_text(self, bot: WechatAPIClient, message: dict):
        if not self.enable:
            return

        content = str(message["Content"]).strip()
        wxid = message["FromWxid"]
        user_wxid = message["SenderWxid"]

        command = content.split(" ", 1)
        if command[0] not in self.command:
            return

        # 限流检查
        wait_time = self._check_user_limit(wxid, user_wxid)
        if wait_time > 0:
            await bot.send_at_message(wxid, f"请等待 {wait_time:.1f} 秒", [user_wxid])
            return

        try:
            # 处理抽签逻辑
            result = await self._process_fortune_telling()
            
            if result:
                # 发送结果
                await bot.send_at_message(wxid, result, [user_wxid])
            else:
                await bot.send_at_message(wxid, "抽签失败，请稍后重试", [user_wxid])

        except Exception as e:
            logger.error(f"[{self.plugin_name}] 异常: {e}")
            await bot.send_at_message(wxid, "抽签过程中出现问题，请稍后重试", [user_wxid])

    async def _process_fortune_telling(self) -> str:
        """处理抽签请求"""
        try:
            # 生成随机签号
            random_number = random.randint(1, 300)
            
            # 发送GET请求到抽签API
            async with httpx.AsyncClient() as client:
                response = await client.get(
                    self.api_url,
                    timeout=self.api_timeout
                )
                
                if response.status_code != 200:
                    logger.error(f"[{self.plugin_name}] API请求失败: {response.status_code}")
                    return None
                
                # 解析返回的文本数据
                data = response.text

                # 使用正则表达式分割签诗和解签内容
                fortune_match = re.search(r'「签诗」(.*?)「解签」', data)
                explanation_match = re.search(r'「解签」(.*?)$', data)
                
                if not fortune_match or not explanation_match:
                    logger.error(f"[{self.plugin_name}] 无法解析API返回数据")
                    return None
                
                fortune_telling = fortune_match.group(1).strip()
                fortune_explanation = explanation_match.group(1).strip()
                
                # 格式化返回的消息
                fortune_telling_msg = (
                    f'您抽到了第{random_number}签！\n'
                    f'----------------\n'
                    f'🎐「签诗」{fortune_telling}\n'
                    f'----------------\n'
                    f'🎐「解签」{fortune_explanation}'
                )

                return fortune_telling_msg
                
        except Exception as e:
            logger.error(f"[{self.plugin_name}] 处理抽签请求异常: {e}")
            return None

    def _check_user_limit(self, wxid: str, user_wxid: str) -> float:
        user_key = f"{wxid}_{user_wxid}"
        last_request = self.user_last_request.get(user_key, 0)
        current_time = time.time()
        elapsed = current_time - last_request
        self.user_last_request[user_key] = current_time

        if elapsed < self.cooldown:
            return self.cooldown - elapsed

        return 0
