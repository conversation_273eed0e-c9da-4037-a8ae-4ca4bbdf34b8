[ImageEcho]
enable = true
command = ["引用图片", "回显图片", "图片引用"]
command-format = "引用图片 [索引号] - 引用最近保存的图片"

# 存储设置
[ImageEcho.storage]
max_images = 5  # 每个群聊最多保存的图片数量

[ImageEcho.analysis]
enable = true
command = ["分析图片", "解析图片"]

[ImageEcho.analysis.chatglm_api]
api_url = "https://chatglm.cn/chatglm/backend-api"
token = "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJzdWIiOiI2NDI4ZDhjZWY4ZTg0NWEyOWMzMGQ5OWViZTZmN2Q2NiIsImV4cCI6MTc0Mjg4Nzc2OCwibmJmIjoxNzQyODAxMzY4LCJpYXQiOjE3NDI4MDEzNjgsImp0aSI6Ijc0MGYxMWY3MGZiZTQ3YTE4NDJhZDUzODVmYTgwMDQ1IiwidWlkIjoiNjdkMTY5MWYxYTdlNjY3Yjg1YTI3YzMwIiwidHlwZSI6ImFjY2VzcyJ9.RvFFQSiO8f_AvQV_cIAy56C6lW85y9ywh79DvrWxYEE"
assistant_id = "65940acff94777010aa6b796"

# 自然化响应设置
natural_response = true  # 启用自然化响应，避免机械化回复