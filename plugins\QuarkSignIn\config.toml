[QuarkSignIn]
enable = true
command = ["夸克签到", "夸克"]
command-format = "使用方法：夸克签到 - 执行夸克网盘签到任务"
natural_response = false  # 监控类插件，禁用自然响应

# 自动签到配置
auto_signin_enable = true  # 是否启用自动签到
auto_signin_hour = 8       # 自动签到时间（小时）
auto_signin_minute = 0     # 自动签到时间（分钟）
notification_wxid = "wxid_ubbh6q832tcs21"  # 签到结果通知的微信ID

[QuarkSignIn.rate_limit]
cooldown = 10  # 冷却时间（秒）

[QuarkSignIn.cookies]
# 夸克网盘Cookie配置
# 格式：["kps=xxx;sign=xxx;vcode=xxx;user=用户名1", "kps=xxx;sign=xxx;vcode=xxx;user=用户名2"]
cookie_list = [
    "kps=AATi6YCVbpLoPkGFnrK+eaPfUsyLRFn9xiKJVa8OFIfKKHDs+XhJqHJyfbIohk1P4LrTTKPHytR46f3/oGFi23j1BMOzAa7KsfjCbWELkD1unQ==;sign=AAT1O/ikIQaaVIFNeGbnHglvA5FiLMsYHxtJa0N6jBfwj3C2JANY30G663CURl3g2NU=;vcode=1751603195894;user=夸父4527"
]
