2025-07-23 14:01:58 | SUCCESS | 读取主设置成功
2025-07-23 14:01:58 | DEBUG | 最终使用的 Redis 主机地址: 127.0.0.1
2025-07-23 14:01:58 | INFO | 2025/07/23 14:01:58 GetRedisAddr: 127.0.0.1:6379
2025-07-23 14:01:58 | INFO | 2025/07/23 14:01:58 Redis连接成功 | 地址:127.0.0.1:6379 DB:0
2025-07-23 14:01:58 | INFO | 2025/07/23 14:01:58 Server start at :9000
2025-07-23 14:01:59 | SUCCESS | WechatAPI服务已启动
2025-07-23 14:01:59 | INFO | 登录账号信息: wxid: wxid_4usgcju5ey9q29  昵称: 瑶瑶  微信号: Today-KFC  手机号: None
2025-07-23 14:01:59 | INFO | 登录设备信息: device_name: <PERSON>'s Pad  device_id: 49d050a7e94d1ecc40790d74b1c4369f
2025-07-23 14:01:59 | SUCCESS | 登录成功
2025-07-23 14:01:59 | SUCCESS | 已开启自动心跳
2025-07-23 14:01:59 | INFO | 成功加载表情映射文件，共 522 条记录
2025-07-23 14:01:59 | SUCCESS | 数据库初始化成功
2025-07-23 14:01:59 | SUCCESS | 定时任务已启动
2025-07-23 14:01:59 | SUCCESS | 插件 AISignIn 已加载定时任务: {'plugins.AISignIn.main.AISignIn.auto_signin_task'}
2025-07-23 14:01:59 | INFO | 已加载管理员列表: ['wxid_ubbh6q832tcs21', 'admin-wxid']
2025-07-23 14:02:00 | INFO | 播客API初始化成功
2025-07-23 14:02:00 | INFO | 加载配置完成，转换后的数据: {'gh_7057516c9e71': {'name': '唱舞全明星', 'groups': ['27852221909@chatroom', '55878994168@chatroom', '51891329927@chatroom']}, 'gh_d4d21a0e4623': {'name': '两颗桃', 'groups': ['48097389945@chatroom']}, 'gh_410ff2fa6ec9': {'name': 'NGC660 Ai研究院', 'groups': ['47442567074@chatroom']}, 'gh_f78c53c507c0': {'name': '不正常吐槽君', 'groups': ['48097389945@chatroom']}, 'gh_ba3381c847b3': {'name': '唱舞星计划', 'groups': ['51891329927@chatroom']}, 'gh_1a102fb4cad9': {'name': '我在娱乐圈的唯一人脉', 'groups': ['48097389945@chatroom', '27852221909@chatroom']}}
2025-07-23 14:02:00 | INFO | ArticleForwarder插件初始化完成 - 监控配置: {'gh_7057516c9e71': {'name': '唱舞全明星', 'groups': ['27852221909@chatroom', '55878994168@chatroom', '51891329927@chatroom']}, 'gh_d4d21a0e4623': {'name': '两颗桃', 'groups': ['48097389945@chatroom']}, 'gh_410ff2fa6ec9': {'name': 'NGC660 Ai研究院', 'groups': ['47442567074@chatroom']}, 'gh_f78c53c507c0': {'name': '不正常吐槽君', 'groups': ['48097389945@chatroom']}, 'gh_ba3381c847b3': {'name': '唱舞星计划', 'groups': ['51891329927@chatroom']}, 'gh_1a102fb4cad9': {'name': '我在娱乐圈的唯一人脉', 'groups': ['48097389945@chatroom', '27852221909@chatroom']}}
2025-07-23 14:02:00 | DEBUG | [TempFileManager] 添加清理规则: default
2025-07-23 14:02:00 | DEBUG | [TempFileManager] 添加清理规则: images
2025-07-23 14:02:00 | DEBUG | [TempFileManager] 添加清理规则: videos
2025-07-23 14:02:00 | DEBUG | [TempFileManager] 添加清理规则: audio
2025-07-23 14:02:00 | DEBUG | [TempFileManager] 添加清理规则: temp
2025-07-23 14:02:00 | DEBUG | [TempFileManager] 添加清理规则: large_files
2025-07-23 14:02:00 | INFO | [TempFileManager] 临时文件清理任务已启动
2025-07-23 14:02:01 | INFO | [ChatSummary] 数据库初始化成功
2025-07-23 14:02:01 | INFO | 成功加载表情映射文件，共 522 条记录
2025-07-23 14:02:01 | SUCCESS | 插件 GoodMorning 已加载定时任务: {'plugins.GoodMorning.main.GoodMorning.daily_task'}
2025-07-23 14:02:01 | SUCCESS | 插件 News 已加载定时任务: {'plugins.News.main.News.noon_news', 'plugins.News.main.News.night_news'}
2025-07-23 14:02:01 | INFO | [PatReply] AMR文件已存在且是最新的: C:\XYBotV2\data\paiyipai\1_BFB5258B-2B13-4CF4-A40C-5BCCE74E5414_converted.amr
2025-07-23 14:02:01 | DEBUG | [PatReply] 从缓存加载base64: 18228 字符
2025-07-23 14:02:01 | SUCCESS | 插件 QuarkSignIn 已加载定时任务: {'plugins.QuarkSignIn.main.QuarkSignIn.auto_signin_task'}
2025-07-23 14:02:01 | INFO | [QuarkSignIn] 已设置自动签到时间: 08:00
2025-07-23 14:02:01 | INFO | 插件状态: 启用, 检查间隔: 3600秒, 最大重试次数: 3, 重试延迟: 5秒
2025-07-23 14:02:01 | INFO | 已加载管理员列表: ['wxid_ubbh6q832tcs21', 'admin-wxid']
2025-07-23 14:02:01 | INFO | 已加载 2 个改名监控群和 1 个退群监控群
2025-07-23 14:02:01 | INFO | [RenameReminder] 开始启用插件...
2025-07-23 14:02:01 | SUCCESS | 插件 RenameReminder 已加载定时任务: {'plugins.RenameReminder.main.RenameReminder.check_name_changes'}
2025-07-23 14:02:01 | INFO | [RenameReminder] 插件已启用，开始初始化...
2025-07-23 14:02:01 | INFO | [RenameReminder] 支持的命令: 开启改名提醒, 关闭改名提醒, 开启退群提醒, 关闭退群提醒, 查看提醒状态
2025-07-23 14:02:01 | INFO | 已设置检查间隔为 3600 秒
2025-07-23 14:02:01 | INFO | 已加载 2 个监控群，开始更新成员列表
2025-07-23 14:02:02 | DEBUG | 已更新群 51891329927@chatroom 的成员列表
2025-07-23 14:02:02 | DEBUG | 已更新群 27852221909@chatroom 的成员列表
2025-07-23 14:02:02 | SUCCESS | 插件 TimerTask 已加载定时任务: {'plugins.TimerTask.main.TimerTask.check_tasks'}
2025-07-23 14:02:03 | INFO | [VideoDemand] 加载了 128 个视频类别配置
2025-07-23 14:02:04 | INFO | [YaoyaoPlugin] 私聊黑名单用户: ['gh_a5cf07d474f1', 'gh_ba3381c847b3']
2025-07-23 14:02:04 | INFO | [YaoyaoPlugin] 已加载管理员列表: ['wxid_ubbh6q832tcs21', 'admin-wxid']
2025-07-23 14:02:04 | INFO | [yuanbao] 插件初始化完成
2025-07-23 14:02:04 | DEBUG | [yuanbao] 指令: ['元宝', 'yuanbao']
2025-07-23 14:02:04 | DEBUG | [yuanbao] 绘图模型: gpt_175B_0404
2025-07-23 14:02:04 | DEBUG | [yuanbao] 认证信息加载状态: hy_token=True, hy_user=True, agent_id=True, x_uskey=True
2025-07-23 14:02:04 | SUCCESS | 已加载插件: ['AdminPoint', 'AdminSignInReset', 'AdminWhitelist', 'AISignIn', 'AppImageTester', 'AppMessageTester', 'ArticleForwarder', 'BaiduAgentsPlugin', 'BaiduDraw', 'BotStatus', 'ChatSummary', 'DanceSignInPlugin', 'DeepseekPlugin', 'Doubao', 'DoubaoDrawing', 'DoubaoImageRecognition', 'DouBaoImageToImage', 'DouBaoImageToVideo', 'DoubaoVideoSearch', 'DouHui', 'EmojiTestPlugin', 'FixedQuoteTest', 'GetContact', 'Gomoku', 'GoodMorning', 'HunyuanDraw', 'ImageEcho', 'ImageToImage', 'JiemengDraw', 'KeLingImageToImage', 'KlingAI', 'Leaderboard', 'LuckyDraw', 'ManagePlugin', 'MeituAI', 'Menu', 'MiniProgramTester', 'Music', 'News', 'PatReply', 'PointTrade', 'QuarkSignIn', 'QueryPoint', 'RandomMember', 'RandomPicture', 'RenameReminder', 'RevokePlugin', 'RoboNeo', 'SignIn', 'TempFileManagerPlugin', 'TencentLke', 'TimerTask', 'TongyiDraw', 'URLShortener', 'VideoDemand', 'VideoParserPlugin', 'VideoTest', 'VivoAgentsPlugin', 'VoiceTest', 'Warthunder', 'WeatherQuery', 'WelcomePlugin', 'Xunfei', 'YaoyaoPlugin', 'Yuanbao', '抽签', '造梦次元']
2025-07-23 14:02:04 | INFO | 处理堆积消息中
2025-07-23 14:02:04 | SUCCESS | 处理堆积消息完毕
2025-07-23 14:02:04 | SUCCESS | 开始处理消息
2025-07-23 14:02:16 | DEBUG | 收到消息: {'MsgId': 1205073313, 'FromUserName': {'string': '48097389945@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 47, 'Content': {'string': 'last--exile:\n<msg><emoji fromusername="last--exile" tousername="48097389945@chatroom" type="2" idbuffer="media:0_0" md5="ef074a71654bf149b26b5f09737e1baa" len="4227054" productid="" androidmd5="ef074a71654bf149b26b5f09737e1baa" androidlen="4227054" s60v3md5="ef074a71654bf149b26b5f09737e1baa" s60v3len="4227054" s60v5md5="ef074a71654bf149b26b5f09737e1baa" s60v5len="4227054" cdnurl="http://wxapp.tc.qq.com/262/20304/stodownload?m=ef074a71654bf149b26b5f09737e1baa&amp;filekey=30350201010421301f02020106040253480410ef074a71654bf149b26b5f09737e1baa0203407fee040d00000004627466730000000132&amp;hy=SH&amp;storeid=26370b2cd000c893280bd5f310000010600004f50534813b258e0b6a871af7&amp;bizid=1023" designerid="" thumburl="" encrypturl="http://wxapp.tc.qq.com/262/20304/stodownload?m=9b1d555300b91dde779b7ff90ffd26c4&amp;filekey=30350201010421301f020201060402534804109b1d555300b91dde779b7ff90ffd26c40203407ff0040d00000004627466730000000132&amp;hy=SH&amp;storeid=26370b2ce0009c8b980bd5f310000010600004f505348245108e0b6ef015d1&amp;bizid=1023" aeskey="7253c127170da011201f01de7c82316f" externurl="http://wxapp.tc.qq.com/262/20304/stodownload?m=8f3e7a54c1cc432188c8ce7186eba010&amp;filekey=30350201010421301f020201060402534804108f3e7a54c1cc432188c8ce7186eba010020301a700040d00000004627466730000000132&amp;hy=SH&amp;storeid=26370b8eb000ef555703030340000010600004f50534828c1c8e0b6f0213e3&amp;bizid=1023" externmd5="5d1507a7cb3fe3bd0c296dc0348daf9d" width="579" height="572" tpurl="" tpauthkey="" attachedtext="" attachedtextcolor="" lensid="" emojiattr="" linkid="" desc=""></emoji></msg>'}, 'Status': 3, 'ImgStatus': 2, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1753250552, 'MsgSource': '<msgsource>\n\t<silence>0</silence>\n\t<membercount>62</membercount>\n\t<signature>N0_V1_CKhCAGs1|v1_3wmbET6K</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'PushContent': '亮在群聊中发了一个表情', 'NewMsgId': 8111219364573973531, 'MsgSeq': 871394261}
2025-07-23 14:02:16 | INFO | 收到表情消息: 消息ID:1205073313 来自:48097389945@chatroom 发送人:last--exile MD5:ef074a71654bf149b26b5f09737e1baa 大小:4227054
2025-07-23 14:02:16 | DEBUG | [DoubaoImageRecognition] 缓存表情消息: 8111219364573973531
2025-07-23 14:02:24 | DEBUG | 收到消息: {'MsgId': 279448688, 'FromUserName': {'string': '55878994168@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 1, 'Content': {'string': 'wxid_ubbh6q832tcs21:\n测试应用图片'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1753250560, 'MsgSource': '<msgsource>\n\t<pua>1</pua>\n\t<eggIncluded>1</eggIncluded>\n\t<silence>0</silence>\n\t<membercount>3</membercount>\n\t<signature>N0_V1_qY8ZrM3e|v1_1ZKRvIeo</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'PushContent': '郭 : 测试应用图片', 'NewMsgId': 6530136125592757385, 'MsgSeq': 871394262}
2025-07-23 14:02:24 | INFO | 收到文本消息: 消息ID:279448688 来自:55878994168@chatroom 发送人:wxid_ubbh6q832tcs21 @:[] 内容:测试应用图片
2025-07-23 14:02:24 | DEBUG | 处理消息内容: '测试应用图片'
2025-07-23 14:02:24 | DEBUG | 消息内容 '测试应用图片' 不匹配任何命令，忽略
