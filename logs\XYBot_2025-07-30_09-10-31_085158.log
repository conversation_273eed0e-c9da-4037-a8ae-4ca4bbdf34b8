2025-07-30 09:10:32 | SUCCESS | 读取主设置成功
2025-07-30 09:10:32 | DEBUG | 最终使用的 Redis 主机地址: 127.0.0.1
2025-07-30 09:10:33 | INFO | 2025/07/30 09:10:33 GetRedisAddr: 127.0.0.1:6379
2025-07-30 09:10:33 | INFO | 2025/07/30 09:10:33 Redis连接成功 | 地址:127.0.0.1:6379 DB:0
2025-07-30 09:10:33 | INFO | 2025/07/30 09:10:33 Server start at :9000
2025-07-30 09:10:33 | SUCCESS | WechatAPI服务已启动
2025-07-30 09:10:34 | INFO | 登录账号信息: wxid: wxid_4usgcju5ey9q29  昵称: 瑶瑶  微信号: Today-KFC  手机号: None
2025-07-30 09:10:34 | INFO | 登录设备信息: device_name: <PERSON>'s Pad  device_id: 49d050a7e94d1ecc40790d74b1c4369f
2025-07-30 09:10:34 | SUCCESS | 登录成功
2025-07-30 09:10:34 | SUCCESS | 已开启自动心跳
2025-07-30 09:10:34 | INFO | 成功加载表情映射文件，共 546 条记录
2025-07-30 09:10:34 | SUCCESS | 数据库初始化成功
2025-07-30 09:10:34 | SUCCESS | 定时任务已启动
2025-07-30 09:10:34 | SUCCESS | 插件 AISignIn 已加载定时任务: {'plugins.AISignIn.main.AISignIn.auto_signin_task'}
2025-07-30 09:10:34 | INFO | 已加载管理员列表: ['wxid_ubbh6q832tcs21', 'admin-wxid']
2025-07-30 09:10:35 | INFO | 播客API初始化成功
2025-07-30 09:10:35 | INFO | 加载配置完成，转换后的数据: {'gh_7057516c9e71': {'name': '唱舞全明星', 'groups': ['27852221909@chatroom', '51891329927@chatroom']}, 'gh_d4d21a0e4623': {'name': '两颗桃', 'groups': ['48097389945@chatroom']}, 'gh_410ff2fa6ec9': {'name': 'NGC660 Ai研究院', 'groups': ['47442567074@chatroom']}, 'gh_f78c53c507c0': {'name': '不正常吐槽君', 'groups': ['48097389945@chatroom']}, 'gh_ba3381c847b3': {'name': '唱舞星计划', 'groups': ['51891329927@chatroom']}, 'gh_1a102fb4cad9': {'name': '我在娱乐圈的唯一人脉', 'groups': ['48097389945@chatroom', '27852221909@chatroom']}}
2025-07-30 09:10:35 | INFO | ArticleForwarder插件初始化完成 - 监控配置: {'gh_7057516c9e71': {'name': '唱舞全明星', 'groups': ['27852221909@chatroom', '51891329927@chatroom']}, 'gh_d4d21a0e4623': {'name': '两颗桃', 'groups': ['48097389945@chatroom']}, 'gh_410ff2fa6ec9': {'name': 'NGC660 Ai研究院', 'groups': ['47442567074@chatroom']}, 'gh_f78c53c507c0': {'name': '不正常吐槽君', 'groups': ['48097389945@chatroom']}, 'gh_ba3381c847b3': {'name': '唱舞星计划', 'groups': ['51891329927@chatroom']}, 'gh_1a102fb4cad9': {'name': '我在娱乐圈的唯一人脉', 'groups': ['48097389945@chatroom', '27852221909@chatroom']}}
2025-07-30 09:10:35 | DEBUG | [TempFileManager] 添加清理规则: default
2025-07-30 09:10:35 | DEBUG | [TempFileManager] 添加清理规则: images
2025-07-30 09:10:35 | DEBUG | [TempFileManager] 添加清理规则: videos
2025-07-30 09:10:35 | DEBUG | [TempFileManager] 添加清理规则: audio
2025-07-30 09:10:35 | DEBUG | [TempFileManager] 添加清理规则: temp
2025-07-30 09:10:35 | DEBUG | [TempFileManager] 添加清理规则: large_files
2025-07-30 09:10:35 | INFO | [TempFileManager] 临时文件清理任务已启动
2025-07-30 09:10:36 | INFO | [ChatSummary] 数据库初始化成功
2025-07-30 09:10:36 | SUCCESS | 插件 GoodMorning 已加载定时任务: {'plugins.GoodMorning.main.GoodMorning.daily_task'}
2025-07-30 09:10:36 | SUCCESS | 插件 News 已加载定时任务: {'plugins.News.main.News.noon_news', 'plugins.News.main.News.night_news'}
2025-07-30 09:10:36 | INFO | [PatReply] AMR文件已存在且是最新的: C:\XYBotV2\data\paiyipai\1_BFB5258B-2B13-4CF4-A40C-5BCCE74E5414_converted.amr
2025-07-30 09:10:36 | DEBUG | [PatReply] 从缓存加载base64: 18228 字符
2025-07-30 09:10:36 | SUCCESS | 插件 QuarkSignIn 已加载定时任务: {'plugins.QuarkSignIn.main.QuarkSignIn.auto_signin_task'}
2025-07-30 09:10:36 | INFO | [QuarkSignIn] 已设置自动签到时间: 08:00
2025-07-30 09:10:36 | INFO | 插件状态: 启用, 检查间隔: 3600秒, 最大重试次数: 3, 重试延迟: 5秒
2025-07-30 09:10:36 | INFO | 已加载管理员列表: ['wxid_ubbh6q832tcs21', 'admin-wxid']
2025-07-30 09:10:36 | INFO | 已加载 2 个改名监控群和 1 个退群监控群
2025-07-30 09:10:36 | INFO | [RenameReminder] 开始启用插件...
2025-07-30 09:10:36 | SUCCESS | 插件 RenameReminder 已加载定时任务: {'plugins.RenameReminder.main.RenameReminder.check_name_changes'}
2025-07-30 09:10:36 | INFO | [RenameReminder] 插件已启用，开始初始化...
2025-07-30 09:10:36 | INFO | [RenameReminder] 支持的命令: 开启改名提醒, 关闭改名提醒, 开启退群提醒, 关闭退群提醒, 查看提醒状态
2025-07-30 09:10:36 | INFO | 已设置检查间隔为 3600 秒
2025-07-30 09:10:36 | INFO | 已加载 2 个监控群，开始更新成员列表
2025-07-30 09:10:37 | DEBUG | 已更新群 27852221909@chatroom 的成员列表
2025-07-30 09:10:37 | DEBUG | 已更新群 51891329927@chatroom 的成员列表
2025-07-30 09:10:37 | SUCCESS | 插件 TimerTask 已加载定时任务: {'plugins.TimerTask.main.TimerTask.check_tasks'}
2025-07-30 09:10:38 | INFO | [VideoDemand] 加载了 128 个视频类别配置
2025-07-30 09:10:38 | INFO | [YaoyaoPlugin] 私聊黑名单用户: ['gh_a5cf07d474f1', 'gh_ba3381c847b3']
2025-07-30 09:10:38 | INFO | [YaoyaoPlugin] 已加载管理员列表: ['wxid_ubbh6q832tcs21', 'admin-wxid']
2025-07-30 09:10:38 | INFO | [yuanbao] 插件初始化完成
2025-07-30 09:10:38 | DEBUG | [yuanbao] 指令: ['元宝', 'yuanbao']
2025-07-30 09:10:38 | DEBUG | [yuanbao] 绘图模型: gpt_175B_0404
2025-07-30 09:10:38 | DEBUG | [yuanbao] 认证信息加载状态: hy_token=True, hy_user=True, agent_id=True, x_uskey=True
2025-07-30 09:10:38 | SUCCESS | 已加载插件: ['AdminPoint', 'AdminSignInReset', 'AdminWhitelist', 'AISignIn', 'AppMessageTester', 'ArticleForwarder', 'BaiduAgentsPlugin', 'BaiduDraw', 'BotStatus', 'ChatSummary', 'DanceSignInPlugin', 'DeepseekPlugin', 'Doubao', 'DoubaoDrawing', 'DoubaoImageRecognition', 'DouBaoImageToImage', 'DouBaoImageToVideo', 'DoubaoVideoSearch', 'DouHui', 'EmojiTestPlugin', 'FixedQuoteTest', 'GetContact', 'Gomoku', 'GoodMorning', 'HunyuanDraw', 'ImageEcho', 'ImageToImage', 'JiemengDraw', 'KeLingImageToImage', 'KlingAI', 'Leaderboard', 'LuckyDraw', 'ManagePlugin', 'MeituAI', 'Menu', 'MiniProgramTester', 'Music', 'News', 'PatReply', 'PointTrade', 'QuarkSignIn', 'QueryPoint', 'RandomMember', 'RandomPicture', 'RenameReminder', 'RevokePlugin', 'RoboNeo', 'SignIn', 'TempFileManagerPlugin', 'TencentLke', 'TimerTask', 'TongyiDraw', 'URLShortener', 'VideoDemand', 'VideoParserPlugin', 'VideoTest', 'VivoAgentsPlugin', 'VoiceTest', 'Warthunder', 'WeatherQuery', 'WelcomePlugin', 'Xunfei', 'YaoyaoPlugin', 'Yuanbao', '抽签', '造梦次元']
2025-07-30 09:10:38 | INFO | 处理堆积消息中
2025-07-30 09:10:38 | SUCCESS | 处理堆积消息完毕
2025-07-30 09:10:38 | SUCCESS | 开始处理消息
2025-07-30 09:14:25 | DEBUG | 收到消息: {'MsgId': 593367656, 'FromUserName': {'string': '48097389945@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 1, 'Content': {'string': 'wxid_ubbh6q832tcs21:\n现在阿姨都不喜欢开麦了'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1753838076, 'MsgSource': '<msgsource>\n\t<pua>1</pua>\n\t<eggIncluded>1</eggIncluded>\n\t<silence>0</silence>\n\t<membercount>73</membercount>\n\t<signature>N0_V1_enf1Kmd/|v1_4EqggqLj</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'PushContent': '郭 : 现在阿姨都不喜欢开麦了', 'NewMsgId': 662340733016409949, 'MsgSeq': 871410888}
2025-07-30 09:14:25 | INFO | 收到文本消息: 消息ID:593367656 来自:48097389945@chatroom 发送人:wxid_ubbh6q832tcs21 @:[] 内容:现在阿姨都不喜欢开麦了
2025-07-30 09:14:25 | INFO | 成功加载表情映射文件，共 546 条记录
2025-07-30 09:14:25 | DEBUG | 处理消息内容: '现在阿姨都不喜欢开麦了'
2025-07-30 09:14:25 | DEBUG | 消息内容 '现在阿姨都不喜欢开麦了' 不匹配任何命令，忽略
2025-07-30 09:14:40 | DEBUG | 收到消息: {'MsgId': 605594175, 'FromUserName': {'string': '27852221909@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 1, 'Content': {'string': 'qianting1731076232:\n签到'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1753838092, 'MsgSource': '<msgsource>\n\t<pua>1</pua>\n\t<eggIncluded>1</eggIncluded>\n\t<silence>1</silence>\n\t<membercount>145</membercount>\n\t<signature>N0_V1_BsQjP1qz|v1_VDNl1kgc</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'NewMsgId': 5177813986557603128, 'MsgSeq': 871410889}
2025-07-30 09:14:40 | INFO | 收到文本消息: 消息ID:605594175 来自:27852221909@chatroom 发送人:qianting1731076232 @:[] 内容:签到
2025-07-30 09:14:40 | DEBUG | 处理消息内容: '签到'
2025-07-30 09:14:40 | DEBUG | 消息内容 '签到' 不匹配任何命令，忽略
2025-07-30 09:14:40 | INFO | 数据库: 用户qianting1731076232登录时间设置为2025-07-30 00:00:00+08:00
2025-07-30 09:14:40 | INFO | 数据库: 用户qianting1731076232连续签到天数设置为7
2025-07-30 09:14:40 | INFO | 数据库: 用户qianting1731076232积分增加17
2025-07-30 09:14:41 | INFO | 发送文字消息: 对方wxid:27852221909@chatroom at:['qianting1731076232'] 内容:@奈斯༩༧ 
-----XYBot-----
签到成功！你领到了 16 个积分！✅
你是今天第 1 个签到的！🎉
你连续签到了 7 天！ 再奖励 1 积分！[爱心]
2025-07-30 09:15:41 | DEBUG | 收到消息: {'MsgId': 1298455257, 'FromUserName': {'string': '48097389945@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 1, 'Content': {'string': 'wxid_wlnzvr8ivgd422:\n开麦能找到男朋友吗'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1753838152, 'MsgSource': '<msgsource>\n\t<pua>1</pua>\n\t<eggIncluded>1</eggIncluded>\n\t<silence>0</silence>\n\t<membercount>73</membercount>\n\t<signature>N0_V1_0km7Y4DG|v1_K36zgrx9</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'PushContent': '锦岚 : 开麦能找到男朋友吗', 'NewMsgId': 4561501865533024741, 'MsgSeq': 871410892}
2025-07-30 09:15:41 | INFO | 收到文本消息: 消息ID:1298455257 来自:48097389945@chatroom 发送人:wxid_wlnzvr8ivgd422 @:[] 内容:开麦能找到男朋友吗
2025-07-30 09:15:41 | DEBUG | 处理消息内容: '开麦能找到男朋友吗'
2025-07-30 09:15:41 | DEBUG | 消息内容 '开麦能找到男朋友吗' 不匹配任何命令，忽略
2025-07-30 09:16:25 | DEBUG | 收到消息: {'MsgId': 1265199991, 'FromUserName': {'string': '48097389945@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 1, 'Content': {'string': 'wxid_jegyk4i3v7zg22:\n雨水打湿的我的电动车'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1753838196, 'MsgSource': '<msgsource>\n\t<bizflag>0</bizflag>\n\t<pua>1</pua>\n\t<eggIncluded>1</eggIncluded>\n\t<silence>0</silence>\n\t<membercount>73</membercount>\n\t<signature>N0_V1_gwYKbUxr|v1_fDX1IMOJ</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'PushContent': '阿尼亚与她 : 雨水打湿的我的电动车', 'NewMsgId': 409700227184640469, 'MsgSeq': 871410893}
2025-07-30 09:16:25 | INFO | 收到文本消息: 消息ID:1265199991 来自:48097389945@chatroom 发送人:wxid_jegyk4i3v7zg22 @:[] 内容:雨水打湿的我的电动车
2025-07-30 09:16:25 | DEBUG | 处理消息内容: '雨水打湿的我的电动车'
2025-07-30 09:16:25 | DEBUG | 消息内容 '雨水打湿的我的电动车' 不匹配任何命令，忽略
2025-07-30 09:16:27 | DEBUG | 收到消息: {'MsgId': 1551801547, 'FromUserName': {'string': '27852221909@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 49, 'Content': {'string': 'wxid_ubbh6q832tcs21:\n<?xml version="1.0"?>\n<msg>\n\t<appmsg appid="" sdkver="0">\n\t\t<title>派对邀请函-签到解锁6周年专属称号</title>\n\t\t<des>唱舞星愿站</des>\n\t\t<username />\n\t\t<action>view</action>\n\t\t<type>33</type>\n\t\t<showtype>0</showtype>\n\t\t<content />\n\t\t<url>https://mp.weixin.qq.com/mp/waerrpage?appid=wxa708de63ee4a2353&amp;type=upgrade&amp;upgradetype=3#wechat_redirect</url>\n\t\t<lowurl />\n\t\t<forwardflag>0</forwardflag>\n\t\t<dataurl />\n\t\t<lowdataurl />\n\t\t<contentattr>0</contentattr>\n\t\t<streamvideo>\n\t\t\t<streamvideourl />\n\t\t\t<streamvideototaltime>0</streamvideototaltime>\n\t\t\t<streamvideotitle />\n\t\t\t<streamvideowording />\n\t\t\t<streamvideoweburl />\n\t\t\t<streamvideothumburl />\n\t\t\t<streamvideoaduxinfo />\n\t\t\t<streamvideopublishid />\n\t\t</streamvideo>\n\t\t<canvasPageItem>\n\t\t\t<canvasPageXml><![CDATA[]]></canvasPageXml>\n\t\t</canvasPageItem>\n\t\t<appattach>\n\t\t\t<attachid />\n\t\t\t<cdnthumburl>3057020100044b30490201000204a95c809d02032f53a30204de752d7002046888c43a042465363332343532632d613732322d343734382d383337312d3530653765643666616235620204051408030201000405004c53db00</cdnthumburl>\n\t\t\t<cdnthumbmd5>69b76813fe0b2a04149aee01978e42f7</cdnthumbmd5>\n\t\t\t<cdnthumblength>173353</cdnthumblength>\n\t\t\t<cdnthumbheight>576</cdnthumbheight>\n\t\t\t<cdnthumbwidth>720</cdnthumbwidth>\n\t\t\t<cdnthumbaeskey>b512284a5485c8f752cecb5e11883413</cdnthumbaeskey>\n\t\t\t<aeskey>b512284a5485c8f752cecb5e11883413</aeskey>\n\t\t\t<encryver>1</encryver>\n\t\t\t<fileext />\n\t\t\t<islargefilemsg>0</islargefilemsg>\n\t\t</appattach>\n\t\t<extinfo />\n\t\t<androidsource>3</androidsource>\n\t\t<sourceusername>gh_25eb09d7bc53@app</sourceusername>\n\t\t<sourcedisplayname>唱舞星愿站</sourcedisplayname>\n\t\t<commenturl />\n\t\t<thumburl />\n\t\t<mediatagname />\n\t\t<messageaction><![CDATA[]]></messageaction>\n\t\t<messageext><![CDATA[]]></messageext>\n\t\t<emoticongift>\n\t\t\t<packageflag>0</packageflag>\n\t\t\t<packageid />\n\t\t</emoticongift>\n\t\t<emoticonshared>\n\t\t\t<packageflag>0</packageflag>\n\t\t\t<packageid />\n\t\t</emoticonshared>\n\t\t<designershared>\n\t\t\t<designeruin>0</designeruin>\n\t\t\t<designername>null</designername>\n\t\t\t<designerrediretcturl><![CDATA[null]]></designerrediretcturl>\n\t\t</designershared>\n\t\t<emotionpageshared>\n\t\t\t<tid>0</tid>\n\t\t\t<title>null</title>\n\t\t\t<desc>null</desc>\n\t\t\t<iconUrl><![CDATA[null]]></iconUrl>\n\t\t\t<secondUrl />\n\t\t\t<pageType>0</pageType>\n\t\t\t<setKey>null</setKey>\n\t\t</emotionpageshared>\n\t\t<webviewshared>\n\t\t\t<shareUrlOriginal />\n\t\t\t<shareUrlOpen />\n\t\t\t<jsAppId />\n\t\t\t<publisherId />\n\t\t\t<publisherReqId />\n\t\t</webviewshared>\n\t\t<template_id />\n\t\t<md5>69b76813fe0b2a04149aee01978e42f7</md5>\n\t\t<websearch />\n\t\t<weappinfo>\n\t\t\t<pagepath><![CDATA[pages/Activity/signIn/index.html?key=jSqgSKXV&inviteId=oA7D81ZZCWDzx-UmiFhXMVnngr4M&taskId=]]></pagepath>\n\t\t\t<username>gh_25eb09d7bc53@app</username>\n\t\t\t<appid>wxa708de63ee4a2353</appid>\n\t\t\t<version>16</version>\n\t\t\t<type>2</type>\n\t\t\t<weappiconurl><![CDATA[http://mmbiz.qpic.cn/mmbiz_png/Y4yibCmxrbX6aCNIhj0BH1CXuE8chunB9AN5fnaRJUMRGRicxkh8crn7P1VCQuhV4GB3KCkE4e5ibmXVEibxaoIJuA/640?wx_fmt=png&wxfrom=200]]></weappiconurl>\n\t\t\t<weapppagethumbrawurl><![CDATA[https://scrmoss.excean.com/prod/lebian_sqgl/202507/310100100046_1948661431494291458/dYfvqMLGp5/dYfvqMLGp5.jpg]]></weapppagethumbrawurl>\n\t\t\t<shareId><![CDATA[0_wxa708de63ee4a2353_3c2a58020487dba191654db6f34404ec_1753532350_0]]></shareId>\n\t\t\t<appservicetype>0</appservicetype>\n\t\t\t<secflagforsinglepagemode>0</secflagforsinglepagemode>\n\t\t\t<videopageinfo>\n\t\t\t\t<thumbwidth>720</thumbwidth>\n\t\t\t\t<thumbheight>576</thumbheight>\n\t\t\t\t<fromopensdk>0</fromopensdk>\n\t\t\t</videopageinfo>\n\t\t\t<wxaTradeCommentScore>0</wxaTradeCommentScore>\n\t\t</weappinfo>\n\t\t<statextstr />\n\t\t<musicShareItem>\n\t\t\t<musicDuration>0</musicDuration>\n\t\t</musicShareItem>\n\t\t<finderLiveProductShare>\n\t\t\t<finderLiveID><![CDATA[]]></finderLiveID>\n\t\t\t<finderUsername><![CDATA[]]></finderUsername>\n\t\t\t<finderObjectID><![CDATA[]]></finderObjectID>\n\t\t\t<finderNonceID><![CDATA[]]></finderNonceID>\n\t\t\t<liveStatus><![CDATA[]]></liveStatus>\n\t\t\t<appId><![CDATA[]]></appId>\n\t\t\t<pagePath><![CDATA[]]></pagePath>\n\t\t\t<productId><![CDATA[]]></productId>\n\t\t\t<coverUrl><![CDATA[]]></coverUrl>\n\t\t\t<productTitle><![CDATA[]]></productTitle>\n\t\t\t<marketPrice><![CDATA[0]]></marketPrice>\n\t\t\t<sellingPrice><![CDATA[0]]></sellingPrice>\n\t\t\t<platformHeadImg><![CDATA[]]></platformHeadImg>\n\t\t\t<platformName><![CDATA[]]></platformName>\n\t\t\t<shopWindowId><![CDATA[]]></shopWindowId>\n\t\t\t<flashSalePrice><![CDATA[0]]></flashSalePrice>\n\t\t\t<flashSaleEndTime><![CDATA[0]]></flashSaleEndTime>\n\t\t\t<ecSource><![CDATA[]]></ecSource>\n\t\t\t<sellingPriceWording><![CDATA[]]></sellingPriceWording>\n\t\t\t<platformIconURL><![CDATA[]]></platformIconURL>\n\t\t\t<firstProductTagURL><![CDATA[]]></firstProductTagURL>\n\t\t\t<firstProductTagAspectRatioString><![CDATA[0.0]]></firstProductTagAspectRatioString>\n\t\t\t<secondProductTagURL><![CDATA[]]></secondProductTagURL>\n\t\t\t<secondProductTagAspectRatioString><![CDATA[0.0]]></secondProductTagAspectRatioString>\n\t\t\t<firstGuaranteeWording><![CDATA[]]></firstGuaranteeWording>\n\t\t\t<secondGuaranteeWording><![CDATA[]]></secondGuaranteeWording>\n\t\t\t<thirdGuaranteeWording><![CDATA[]]></thirdGuaranteeWording>\n\t\t\t<isPriceBeginShow>false</isPriceBeginShow>\n\t\t\t<lastGMsgID><![CDATA[]]></lastGMsgID>\n\t\t\t<promoterKey><![CDATA[]]></promoterKey>\n\t\t\t<discountWording><![CDATA[]]></discountWording>\n\t\t\t<priceSuffixDescription><![CDATA[]]></priceSuffixDescription>\n\t\t\t<productCardKey><![CDATA[]]></productCardKey>\n\t\t\t<isWxShop><![CDATA[]]></isWxShop>\n\t\t\t<brandIconUrl><![CDATA[]]></brandIconUrl>\n\t\t\t<showBoxItemStringList />\n\t\t</finderLiveProductShare>\n\t\t<finderOrder>\n\t\t\t<appID><![CDATA[]]></appID>\n\t\t\t<orderID><![CDATA[]]></orderID>\n\t\t\t<path><![CDATA[]]></path>\n\t\t\t<priceWording><![CDATA[]]></priceWording>\n\t\t\t<stateWording><![CDATA[]]></stateWording>\n\t\t\t<productImageURL><![CDATA[]]></productImageURL>\n\t\t\t<products><![CDATA[]]></products>\n\t\t\t<productsCount><![CDATA[0]]></productsCount>\n\t\t\t<orderType><![CDATA[0]]></orderType>\n\t\t\t<newPriceWording><![CDATA[]]></newPriceWording>\n\t\t\t<newStateWording><![CDATA[]]></newStateWording>\n\t\t\t<useNewWording><![CDATA[0]]></useNewWording>\n\t\t</finderOrder>\n\t\t<finderShopWindowShare>\n\t\t\t<finderUsername><![CDATA[]]></finderUsername>\n\t\t\t<avatar><![CDATA[]]></avatar>\n\t\t\t<nickname><![CDATA[]]></nickname>\n\t\t\t<commodityInStockCount><![CDATA[]]></commodityInStockCount>\n\t\t\t<appId><![CDATA[]]></appId>\n\t\t\t<path><![CDATA[]]></path>\n\t\t\t<appUsername><![CDATA[]]></appUsername>\n\t\t\t<query><![CDATA[]]></query>\n\t\t\t<liteAppId><![CDATA[]]></liteAppId>\n\t\t\t<liteAppPath><![CDATA[]]></liteAppPath>\n\t\t\t<liteAppQuery><![CDATA[]]></liteAppQuery>\n\t\t\t<platformTagURL><![CDATA[]]></platformTagURL>\n\t\t\t<saleWording><![CDATA[]]></saleWording>\n\t\t\t<lastGMsgID><![CDATA[]]></lastGMsgID>\n\t\t\t<profileTypeWording><![CDATA[]]></profileTypeWording>\n\t\t\t<saleWordingExtra><![CDATA[]]></saleWordingExtra>\n\t\t\t<isWxShop><![CDATA[]]></isWxShop>\n\t\t\t<platformIconUrl><![CDATA[]]></platformIconUrl>\n\t\t\t<brandIconUrl><![CDATA[]]></brandIconUrl>\n\t\t\t<description><![CDATA[]]></description>\n\t\t\t<backgroundUrl><![CDATA[]]></backgroundUrl>\n\t\t\t<darkModePlatformIconUrl><![CDATA[]]></darkModePlatformIconUrl>\n\t\t\t<reputationInfo>\n\t\t\t\t<hasReputationInfo>0</hasReputationInfo>\n\t\t\t\t<reputationScore>0</reputationScore>\n\t\t\t\t<reputationWording />\n\t\t\t\t<reputationTextColor />\n\t\t\t\t<reputationLevelWording />\n\t\t\t\t<reputationBackgroundColor />\n\t\t\t</reputationInfo>\n\t\t\t<productImageURLList />\n\t\t</finderShopWindowShare>\n\t\t<findernamecard>\n\t\t\t<username />\n\t\t\t<avatar><![CDATA[]]></avatar>\n\t\t\t<nickname />\n\t\t\t<auth_job />\n\t\t\t<auth_icon>0</auth_icon>\n\t\t\t<auth_icon_url />\n\t\t\t<ecSource><![CDATA[]]></ecSource>\n\t\t\t<lastGMsgID><![CDATA[]]></lastGMsgID>\n\t\t</findernamecard>\n\t\t<finderGuarantee>\n\t\t\t<scene><![CDATA[0]]></scene>\n\t\t</finderGuarantee>\n\t\t<directshare>0</directshare>\n\t\t<gamecenter>\n\t\t\t<namecard>\n\t\t\t\t<iconUrl />\n\t\t\t\t<name />\n\t\t\t\t<desc />\n\t\t\t\t<tail />\n\t\t\t\t<jumpUrl />\n\t\t\t</namecard>\n\t\t</gamecenter>\n\t\t<patMsg>\n\t\t\t<chatUser />\n\t\t\t<records>\n\t\t\t\t<recordNum>0</recordNum>\n\t\t\t</records>\n\t\t</patMsg>\n\t\t<secretmsg>\n\t\t\t<issecretmsg>0</issecretmsg>\n\t\t</secretmsg>\n\t\t<referfromscene>0</referfromscene>\n\t\t<gameshare>\n\t\t\t<liteappext>\n\t\t\t\t<liteappbizdata />\n\t\t\t\t<priority>0</priority>\n\t\t\t</liteappext>\n\t\t\t<appbrandext>\n\t\t\t\t<litegameinfo />\n\t\t\t\t<priority>-1</priority>\n\t\t\t</appbrandext>\n\t\t\t<gameshareid />\n\t\t\t<sharedata />\n\t\t\t<isvideo>0</isvideo>\n\t\t\t<duration>0</duration>\n\t\t\t<isexposed>0</isexposed>\n\t\t\t<readtext />\n\t\t</gameshare>\n\t\t<mpsharetrace>\n\t\t\t<hasfinderelement>0</hasfinderelement>\n\t\t\t<lastgmsgid />\n\t\t</mpsharetrace>\n\t\t<wxgamecard>\n\t\t\t<framesetname />\n\t\t\t<mbcarddata />\n\t\t\t<minpkgversion />\n\t\t\t<clientextinfo />\n\t\t\t<mbcardheight>0</mbcardheight>\n\t\t\t<isoldversion>0</isoldversion>\n\t\t</wxgamecard>\n\t\t<liteapp>\n\t\t\t<id>null</id>\n\t\t\t<path />\n\t\t\t<query />\n\t\t\t<istransparent>0</istransparent>\n\t\t\t<hideicon>0</hideicon>\n\t\t</liteapp>\n\t\t<opensdk_share_is_modified>0</opensdk_share_is_modified>\n\t</appmsg>\n\t<fromusername>wxid_ubbh6q832tcs21</fromusername>\n\t<scene>0</scene>\n\t<appinfo>\n\t\t<version>1</version>\n\t\t<appname></appname>\n\t</appinfo>\n\t<commenturl></commenturl>\n</msg>\n'}, 'Status': 3, 'ImgStatus': 2, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1753838198, 'MsgSource': '<msgsource>\n\t<alnode>\n\t\t<fr>4</fr>\n\t</alnode>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n\t<sec_msg_node>\n\t\t<uuid>a584aae0f79bade59009b5c2a08d68fb_</uuid>\n\t\t<risk-file-flag />\n\t\t<risk-file-md5-list />\n\t</sec_msg_node>\n\t<silence>1</silence>\n\t<membercount>145</membercount>\n\t<signature>N0_V1_k9fEwhIe|v1_A2LvgfWq</signature>\n</msgsource>\n', 'NewMsgId': *******************, 'MsgSeq': 871410894}
2025-07-30 09:16:27 | DEBUG | 从群聊消息中提取发送者: wxid_ubbh6q832tcs21
2025-07-30 09:16:27 | DEBUG | XML消息完整内容:
<?xml version="1.0"?>
<msg>
	<appmsg appid="" sdkver="0">
		<title>派对邀请函-签到解锁6周年专属称号</title>
		<des>唱舞星愿站</des>
		<username />
		<action>view</action>
		<type>33</type>
		<showtype>0</showtype>
		<content />
		<url>https://mp.weixin.qq.com/mp/waerrpage?appid=wxa708de63ee4a2353&amp;type=upgrade&amp;upgradetype=3#wechat_redirect</url>
		<lowurl />
		<forwardflag>0</forwardflag>
		<dataurl />
		<lowdataurl />
		<contentattr>0</contentattr>
		<streamvideo>
			<streamvideourl />
			<streamvideototaltime>0</streamvideototaltime>
			<streamvideotitle />
			<streamvideowording />
			<streamvideoweburl />
			<streamvideothumburl />
			<streamvideoaduxinfo />
			<streamvideopublishid />
		</streamvideo>
		<canvasPageItem>
			<canvasPageXml><![CDATA[]]></canvasPageXml>
		</canvasPageItem>
		<appattach>
			<attachid />
			<cdnthumburl>3057020100044b30490201000204a95c809d02032f53a30204de752d7002046888c43a042465363332343532632d613732322d343734382d383337312d3530653765643666616235620204051408030201000405004c53db00</cdnthumburl>
			<cdnthumbmd5>69b76813fe0b2a04149aee01978e42f7</cdnthumbmd5>
			<cdnthumblength>173353</cdnthumblength>
			<cdnthumbheight>576</cdnthumbheight>
			<cdnthumbwidth>720</cdnthumbwidth>
			<cdnthumbaeskey>b512284a5485c8f752cecb5e11883413</cdnthumbaeskey>
			<aeskey>b512284a5485c8f752cecb5e11883413</aeskey>
			<encryver>1</encryver>
			<fileext />
			<islargefilemsg>0</islargefilemsg>
		</appattach>
		<extinfo />
		<androidsource>3</androidsource>
		<sourceusername>gh_25eb09d7bc53@app</sourceusername>
		<sourcedisplayname>唱舞星愿站</sourcedisplayname>
		<commenturl />
		<thumburl />
		<mediatagname />
		<messageaction><![CDATA[]]></messageaction>
		<messageext><![CDATA[]]></messageext>
		<emoticongift>
			<packageflag>0</packageflag>
			<packageid />
		</emoticongift>
		<emoticonshared>
			<packageflag>0</packageflag>
			<packageid />
		</emoticonshared>
		<designershared>
			<designeruin>0</designeruin>
			<designername>null</designername>
			<designerrediretcturl><![CDATA[null]]></designerrediretcturl>
		</designershared>
		<emotionpageshared>
			<tid>0</tid>
			<title>null</title>
			<desc>null</desc>
			<iconUrl><![CDATA[null]]></iconUrl>
			<secondUrl />
			<pageType>0</pageType>
			<setKey>null</setKey>
		</emotionpageshared>
		<webviewshared>
			<shareUrlOriginal />
			<shareUrlOpen />
			<jsAppId />
			<publisherId />
			<publisherReqId />
		</webviewshared>
		<template_id />
		<md5>69b76813fe0b2a04149aee01978e42f7</md5>
		<websearch />
		<weappinfo>
			<pagepath><![CDATA[pages/Activity/signIn/index.html?key=jSqgSKXV&inviteId=oA7D81ZZCWDzx-UmiFhXMVnngr4M&taskId=]]></pagepath>
			<username>gh_25eb09d7bc53@app</username>
			<appid>wxa708de63ee4a2353</appid>
			<version>16</version>
			<type>2</type>
			<weappiconurl><![CDATA[http://mmbiz.qpic.cn/mmbiz_png/Y4yibCmxrbX6aCNIhj0BH1CXuE8chunB9AN5fnaRJUMRGRicxkh8crn7P1VCQuhV4GB3KCkE4e5ibmXVEibxaoIJuA/640?wx_fmt=png&wxfrom=200]]></weappiconurl>
			<weapppagethumbrawurl><![CDATA[https://scrmoss.excean.com/prod/lebian_sqgl/202507/310100100046_1948661431494291458/dYfvqMLGp5/dYfvqMLGp5.jpg]]></weapppagethumbrawurl>
			<shareId><![CDATA[0_wxa708de63ee4a2353_3c2a58020487dba191654db6f34404ec_1753532350_0]]></shareId>
			<appservicetype>0</appservicetype>
			<secflagforsinglepagemode>0</secflagforsinglepagemode>
			<videopageinfo>
				<thumbwidth>720</thumbwidth>
				<thumbheight>576</thumbheight>
				<fromopensdk>0</fromopensdk>
			</videopageinfo>
			<wxaTradeCommentScore>0</wxaTradeCommentScore>
		</weappinfo>
		<statextstr />
		<musicShareItem>
			<musicDuration>0</musicDuration>
		</musicShareItem>
		<finderLiveProductShare>
			<finderLiveID><![CDATA[]]></finderLiveID>
			<finderUsername><![CDATA[]]></finderUsername>
			<finderObjectID><![CDATA[]]></finderObjectID>
			<finderNonceID><![CDATA[]]></finderNonceID>
			<liveStatus><![CDATA[]]></liveStatus>
			<appId><![CDATA[]]></appId>
			<pagePath><![CDATA[]]></pagePath>
			<productId><![CDATA[]]></productId>
			<coverUrl><![CDATA[]]></coverUrl>
			<productTitle><![CDATA[]]></productTitle>
			<marketPrice><![CDATA[0]]></marketPrice>
			<sellingPrice><![CDATA[0]]></sellingPrice>
			<platformHeadImg><![CDATA[]]></platformHeadImg>
			<platformName><![CDATA[]]></platformName>
			<shopWindowId><![CDATA[]]></shopWindowId>
			<flashSalePrice><![CDATA[0]]></flashSalePrice>
			<flashSaleEndTime><![CDATA[0]]></flashSaleEndTime>
			<ecSource><![CDATA[]]></ecSource>
			<sellingPriceWording><![CDATA[]]></sellingPriceWording>
			<platformIconURL><![CDATA[]]></platformIconURL>
			<firstProductTagURL><![CDATA[]]></firstProductTagURL>
			<firstProductTagAspectRatioString><![CDATA[0.0]]></firstProductTagAspectRatioString>
			<secondProductTagURL><![CDATA[]]></secondProductTagURL>
			<secondProductTagAspectRatioString><![CDATA[0.0]]></secondProductTagAspectRatioString>
			<firstGuaranteeWording><![CDATA[]]></firstGuaranteeWording>
			<secondGuaranteeWording><![CDATA[]]></secondGuaranteeWording>
			<thirdGuaranteeWording><![CDATA[]]></thirdGuaranteeWording>
			<isPriceBeginShow>false</isPriceBeginShow>
			<lastGMsgID><![CDATA[]]></lastGMsgID>
			<promoterKey><![CDATA[]]></promoterKey>
			<discountWording><![CDATA[]]></discountWording>
			<priceSuffixDescription><![CDATA[]]></priceSuffixDescription>
			<productCardKey><![CDATA[]]></productCardKey>
			<isWxShop><![CDATA[]]></isWxShop>
			<brandIconUrl><![CDATA[]]></brandIconUrl>
			<showBoxItemStringList />
		</finderLiveProductShare>
		<finderOrder>
			<appID><![CDATA[]]></appID>
			<orderID><![CDATA[]]></orderID>
			<path><![CDATA[]]></path>
			<priceWording><![CDATA[]]></priceWording>
			<stateWording><![CDATA[]]></stateWording>
			<productImageURL><![CDATA[]]></productImageURL>
			<products><![CDATA[]]></products>
			<productsCount><![CDATA[0]]></productsCount>
			<orderType><![CDATA[0]]></orderType>
			<newPriceWording><![CDATA[]]></newPriceWording>
			<newStateWording><![CDATA[]]></newStateWording>
			<useNewWording><![CDATA[0]]></useNewWording>
		</finderOrder>
		<finderShopWindowShare>
			<finderUsername><![CDATA[]]></finderUsername>
			<avatar><![CDATA[]]></avatar>
			<nickname><![CDATA[]]></nickname>
			<commodityInStockCount><![CDATA[]]></commodityInStockCount>
			<appId><![CDATA[]]></appId>
			<path><![CDATA[]]></path>
			<appUsername><![CDATA[]]></appUsername>
			<query><![CDATA[]]></query>
			<liteAppId><![CDATA[]]></liteAppId>
			<liteAppPath><![CDATA[]]></liteAppPath>
			<liteAppQuery><![CDATA[]]></liteAppQuery>
			<platformTagURL><![CDATA[]]></platformTagURL>
			<saleWording><![CDATA[]]></saleWording>
			<lastGMsgID><![CDATA[]]></lastGMsgID>
			<profileTypeWording><![CDATA[]]></profileTypeWording>
			<saleWordingExtra><![CDATA[]]></saleWordingExtra>
			<isWxShop><![CDATA[]]></isWxShop>
			<platformIconUrl><![CDATA[]]></platformIconUrl>
			<brandIconUrl><![CDATA[]]></brandIconUrl>
			<description><![CDATA[]]></description>
			<backgroundUrl><![CDATA[]]></backgroundUrl>
			<darkModePlatformIconUrl><![CDATA[]]></darkModePlatformIconUrl>
			<reputationInfo>
				<hasReputationInfo>0</hasReputationInfo>
				<reputationScore>0</reputationScore>
				<reputationWording />
				<reputationTextColor />
				<reputationLevelWording />
				<reputationBackgroundColor />
			</reputationInfo>
			<productImageURLList />
		</finderShopWindowShare>
		<findernamecard>
			<username />
			<avatar><![CDATA[]]></avatar>
			<nickname />
			<auth_job />
			<auth_icon>0</auth_icon>
			<auth_icon_url />
			<ecSource><![CDATA[]]></ecSource>
			<lastGMsgID><![CDATA[]]></lastGMsgID>
		</findernamecard>
		<finderGuarantee>
			<scene><![CDATA[0]]></scene>
		</finderGuarantee>
		<directshare>0</directshare>
		<gamecenter>
			<namecard>
				<iconUrl />
				<name />
				<desc />
				<tail />
				<jumpUrl />
			</namecard>
		</gamecenter>
		<patMsg>
			<chatUser />
			<records>
				<recordNum>0</recordNum>
			</records>
		</patMsg>
		<secretmsg>
			<issecretmsg>0</issecretmsg>
		</secretmsg>
		<referfromscene>0</referfromscene>
		<gameshare>
			<liteappext>
				<liteappbizdata />
				<priority>0</priority>
			</liteappext>
			<appbrandext>
				<litegameinfo />
				<priority>-1</priority>
			</appbrandext>
			<gameshareid />
			<sharedata />
			<isvideo>0</isvideo>
			<duration>0</duration>
			<isexposed>0</isexposed>
			<readtext />
		</gameshare>
		<mpsharetrace>
			<hasfinderelement>0</hasfinderelement>
			<lastgmsgid />
		</mpsharetrace>
		<wxgamecard>
			<framesetname />
			<mbcarddata />
			<minpkgversion />
			<clientextinfo />
			<mbcardheight>0</mbcardheight>
			<isoldversion>0</isoldversion>
		</wxgamecard>
		<liteapp>
			<id>null</id>
			<path />
			<query />
			<istransparent>0</istransparent>
			<hideicon>0</hideicon>
		</liteapp>
		<opensdk_share_is_modified>0</opensdk_share_is_modified>
	</appmsg>
	<fromusername>wxid_ubbh6q832tcs21</fromusername>
	<scene>0</scene>
	<appinfo>
		<version>1</version>
		<appname></appname>
	</appinfo>
	<commenturl></commenturl>
</msg>

2025-07-30 09:16:27 | DEBUG | XML消息类型: 33
2025-07-30 09:16:27 | DEBUG | XML消息标题: 派对邀请函-签到解锁6周年专属称号
2025-07-30 09:16:27 | DEBUG | XML消息描述: 唱舞星愿站
2025-07-30 09:16:27 | DEBUG | 附件信息 cdnthumburl: 3057020100044b30490201000204a95c809d02032f53a30204de752d7002046888c43a042465363332343532632d613732322d343734382d383337312d3530653765643666616235620204051408030201000405004c53db00
2025-07-30 09:16:27 | DEBUG | 附件信息 cdnthumbmd5: 69b76813fe0b2a04149aee01978e42f7
2025-07-30 09:16:27 | DEBUG | 附件信息 cdnthumblength: 173353
2025-07-30 09:16:27 | DEBUG | 附件信息 cdnthumbheight: 576
2025-07-30 09:16:27 | DEBUG | 附件信息 cdnthumbwidth: 720
2025-07-30 09:16:27 | DEBUG | 附件信息 cdnthumbaeskey: b512284a5485c8f752cecb5e11883413
2025-07-30 09:16:27 | DEBUG | 附件信息 aeskey: b512284a5485c8f752cecb5e11883413
2025-07-30 09:16:27 | DEBUG | 附件信息 encryver: 1
2025-07-30 09:16:27 | DEBUG | 附件信息 islargefilemsg: 0
2025-07-30 09:16:27 | DEBUG | XML消息URL: https://mp.weixin.qq.com/mp/waerrpage?appid=wxa708de63ee4a2353&type=upgrade&upgradetype=3#wechat_redirect
2025-07-30 09:16:27 | INFO | 未知的XML消息类型: 33
2025-07-30 09:16:27 | INFO | 消息标题: 派对邀请函-签到解锁6周年专属称号
2025-07-30 09:16:27 | INFO | 消息描述: 唱舞星愿站
2025-07-30 09:16:27 | INFO | 消息URL: https://mp.weixin.qq.com/mp/waerrpage?appid=wxa708de63ee4a2353&type=upgrade&upgradetype=3#wechat_redirect
2025-07-30 09:16:27 | INFO | 完整XML内容:
<?xml version="1.0"?>
<msg>
	<appmsg appid="" sdkver="0">
		<title>派对邀请函-签到解锁6周年专属称号</title>
		<des>唱舞星愿站</des>
		<username />
		<action>view</action>
		<type>33</type>
		<showtype>0</showtype>
		<content />
		<url>https://mp.weixin.qq.com/mp/waerrpage?appid=wxa708de63ee4a2353&amp;type=upgrade&amp;upgradetype=3#wechat_redirect</url>
		<lowurl />
		<forwardflag>0</forwardflag>
		<dataurl />
		<lowdataurl />
		<contentattr>0</contentattr>
		<streamvideo>
			<streamvideourl />
			<streamvideototaltime>0</streamvideototaltime>
			<streamvideotitle />
			<streamvideowording />
			<streamvideoweburl />
			<streamvideothumburl />
			<streamvideoaduxinfo />
			<streamvideopublishid />
		</streamvideo>
		<canvasPageItem>
			<canvasPageXml><![CDATA[]]></canvasPageXml>
		</canvasPageItem>
		<appattach>
			<attachid />
			<cdnthumburl>3057020100044b30490201000204a95c809d02032f53a30204de752d7002046888c43a042465363332343532632d613732322d343734382d383337312d3530653765643666616235620204051408030201000405004c53db00</cdnthumburl>
			<cdnthumbmd5>69b76813fe0b2a04149aee01978e42f7</cdnthumbmd5>
			<cdnthumblength>173353</cdnthumblength>
			<cdnthumbheight>576</cdnthumbheight>
			<cdnthumbwidth>720</cdnthumbwidth>
			<cdnthumbaeskey>b512284a5485c8f752cecb5e11883413</cdnthumbaeskey>
			<aeskey>b512284a5485c8f752cecb5e11883413</aeskey>
			<encryver>1</encryver>
			<fileext />
			<islargefilemsg>0</islargefilemsg>
		</appattach>
		<extinfo />
		<androidsource>3</androidsource>
		<sourceusername>gh_25eb09d7bc53@app</sourceusername>
		<sourcedisplayname>唱舞星愿站</sourcedisplayname>
		<commenturl />
		<thumburl />
		<mediatagname />
		<messageaction><![CDATA[]]></messageaction>
		<messageext><![CDATA[]]></messageext>
		<emoticongift>
			<packageflag>0</packageflag>
			<packageid />
		</emoticongift>
		<emoticonshared>
			<packageflag>0</packageflag>
			<packageid />
		</emoticonshared>
		<designershared>
			<designeruin>0</designeruin>
			<designername>null</designername>
			<designerrediretcturl><![CDATA[null]]></designerrediretcturl>
		</designershared>
		<emotionpageshared>
			<tid>0</tid>
			<title>null</title>
			<desc>null</desc>
			<iconUrl><![CDATA[null]]></iconUrl>
			<secondUrl />
			<pageType>0</pageType>
			<setKey>null</setKey>
		</emotionpageshared>
		<webviewshared>
			<shareUrlOriginal />
			<shareUrlOpen />
			<jsAppId />
			<publisherId />
			<publisherReqId />
		</webviewshared>
		<template_id />
		<md5>69b76813fe0b2a04149aee01978e42f7</md5>
		<websearch />
		<weappinfo>
			<pagepath><![CDATA[pages/Activity/signIn/index.html?key=jSqgSKXV&inviteId=oA7D81ZZCWDzx-UmiFhXMVnngr4M&taskId=]]></pagepath>
			<username>gh_25eb09d7bc53@app</username>
			<appid>wxa708de63ee4a2353</appid>
			<version>16</version>
			<type>2</type>
			<weappiconurl><![CDATA[http://mmbiz.qpic.cn/mmbiz_png/Y4yibCmxrbX6aCNIhj0BH1CXuE8chunB9AN5fnaRJUMRGRicxkh8crn7P1VCQuhV4GB3KCkE4e5ibmXVEibxaoIJuA/640?wx_fmt=png&wxfrom=200]]></weappiconurl>
			<weapppagethumbrawurl><![CDATA[https://scrmoss.excean.com/prod/lebian_sqgl/202507/310100100046_1948661431494291458/dYfvqMLGp5/dYfvqMLGp5.jpg]]></weapppagethumbrawurl>
			<shareId><![CDATA[0_wxa708de63ee4a2353_3c2a58020487dba191654db6f34404ec_1753532350_0]]></shareId>
			<appservicetype>0</appservicetype>
			<secflagforsinglepagemode>0</secflagforsinglepagemode>
			<videopageinfo>
				<thumbwidth>720</thumbwidth>
				<thumbheight>576</thumbheight>
				<fromopensdk>0</fromopensdk>
			</videopageinfo>
			<wxaTradeCommentScore>0</wxaTradeCommentScore>
		</weappinfo>
		<statextstr />
		<musicShareItem>
			<musicDuration>0</musicDuration>
		</musicShareItem>
		<finderLiveProductShare>
			<finderLiveID><![CDATA[]]></finderLiveID>
			<finderUsername><![CDATA[]]></finderUsername>
			<finderObjectID><![CDATA[]]></finderObjectID>
			<finderNonceID><![CDATA[]]></finderNonceID>
			<liveStatus><![CDATA[]]></liveStatus>
			<appId><![CDATA[]]></appId>
			<pagePath><![CDATA[]]></pagePath>
			<productId><![CDATA[]]></productId>
			<coverUrl><![CDATA[]]></coverUrl>
			<productTitle><![CDATA[]]></productTitle>
			<marketPrice><![CDATA[0]]></marketPrice>
			<sellingPrice><![CDATA[0]]></sellingPrice>
			<platformHeadImg><![CDATA[]]></platformHeadImg>
			<platformName><![CDATA[]]></platformName>
			<shopWindowId><![CDATA[]]></shopWindowId>
			<flashSalePrice><![CDATA[0]]></flashSalePrice>
			<flashSaleEndTime><![CDATA[0]]></flashSaleEndTime>
			<ecSource><![CDATA[]]></ecSource>
			<sellingPriceWording><![CDATA[]]></sellingPriceWording>
			<platformIconURL><![CDATA[]]></platformIconURL>
			<firstProductTagURL><![CDATA[]]></firstProductTagURL>
			<firstProductTagAspectRatioString><![CDATA[0.0]]></firstProductTagAspectRatioString>
			<secondProductTagURL><![CDATA[]]></secondProductTagURL>
			<secondProductTagAspectRatioString><![CDATA[0.0]]></secondProductTagAspectRatioString>
			<firstGuaranteeWording><![CDATA[]]></firstGuaranteeWording>
			<secondGuaranteeWording><![CDATA[]]></secondGuaranteeWording>
			<thirdGuaranteeWording><![CDATA[]]></thirdGuaranteeWording>
			<isPriceBeginShow>false</isPriceBeginShow>
			<lastGMsgID><![CDATA[]]></lastGMsgID>
			<promoterKey><![CDATA[]]></promoterKey>
			<discountWording><![CDATA[]]></discountWording>
			<priceSuffixDescription><![CDATA[]]></priceSuffixDescription>
			<productCardKey><![CDATA[]]></productCardKey>
			<isWxShop><![CDATA[]]></isWxShop>
			<brandIconUrl><![CDATA[]]></brandIconUrl>
			<showBoxItemStringList />
		</finderLiveProductShare>
		<finderOrder>
			<appID><![CDATA[]]></appID>
			<orderID><![CDATA[]]></orderID>
			<path><![CDATA[]]></path>
			<priceWording><![CDATA[]]></priceWording>
			<stateWording><![CDATA[]]></stateWording>
			<productImageURL><![CDATA[]]></productImageURL>
			<products><![CDATA[]]></products>
			<productsCount><![CDATA[0]]></productsCount>
			<orderType><![CDATA[0]]></orderType>
			<newPriceWording><![CDATA[]]></newPriceWording>
			<newStateWording><![CDATA[]]></newStateWording>
			<useNewWording><![CDATA[0]]></useNewWording>
		</finderOrder>
		<finderShopWindowShare>
			<finderUsername><![CDATA[]]></finderUsername>
			<avatar><![CDATA[]]></avatar>
			<nickname><![CDATA[]]></nickname>
			<commodityInStockCount><![CDATA[]]></commodityInStockCount>
			<appId><![CDATA[]]></appId>
			<path><![CDATA[]]></path>
			<appUsername><![CDATA[]]></appUsername>
			<query><![CDATA[]]></query>
			<liteAppId><![CDATA[]]></liteAppId>
			<liteAppPath><![CDATA[]]></liteAppPath>
			<liteAppQuery><![CDATA[]]></liteAppQuery>
			<platformTagURL><![CDATA[]]></platformTagURL>
			<saleWording><![CDATA[]]></saleWording>
			<lastGMsgID><![CDATA[]]></lastGMsgID>
			<profileTypeWording><![CDATA[]]></profileTypeWording>
			<saleWordingExtra><![CDATA[]]></saleWordingExtra>
			<isWxShop><![CDATA[]]></isWxShop>
			<platformIconUrl><![CDATA[]]></platformIconUrl>
			<brandIconUrl><![CDATA[]]></brandIconUrl>
			<description><![CDATA[]]></description>
			<backgroundUrl><![CDATA[]]></backgroundUrl>
			<darkModePlatformIconUrl><![CDATA[]]></darkModePlatformIconUrl>
			<reputationInfo>
				<hasReputationInfo>0</hasReputationInfo>
				<reputationScore>0</reputationScore>
				<reputationWording />
				<reputationTextColor />
				<reputationLevelWording />
				<reputationBackgroundColor />
			</reputationInfo>
			<productImageURLList />
		</finderShopWindowShare>
		<findernamecard>
			<username />
			<avatar><![CDATA[]]></avatar>
			<nickname />
			<auth_job />
			<auth_icon>0</auth_icon>
			<auth_icon_url />
			<ecSource><![CDATA[]]></ecSource>
			<lastGMsgID><![CDATA[]]></lastGMsgID>
		</findernamecard>
		<finderGuarantee>
			<scene><![CDATA[0]]></scene>
		</finderGuarantee>
		<directshare>0</directshare>
		<gamecenter>
			<namecard>
				<iconUrl />
				<name />
				<desc />
				<tail />
				<jumpUrl />
			</namecard>
		</gamecenter>
		<patMsg>
			<chatUser />
			<records>
				<recordNum>0</recordNum>
			</records>
		</patMsg>
		<secretmsg>
			<issecretmsg>0</issecretmsg>
		</secretmsg>
		<referfromscene>0</referfromscene>
		<gameshare>
			<liteappext>
				<liteappbizdata />
				<priority>0</priority>
			</liteappext>
			<appbrandext>
				<litegameinfo />
				<priority>-1</priority>
			</appbrandext>
			<gameshareid />
			<sharedata />
			<isvideo>0</isvideo>
			<duration>0</duration>
			<isexposed>0</isexposed>
			<readtext />
		</gameshare>
		<mpsharetrace>
			<hasfinderelement>0</hasfinderelement>
			<lastgmsgid />
		</mpsharetrace>
		<wxgamecard>
			<framesetname />
			<mbcarddata />
			<minpkgversion />
			<clientextinfo />
			<mbcardheight>0</mbcardheight>
			<isoldversion>0</isoldversion>
		</wxgamecard>
		<liteapp>
			<id>null</id>
			<path />
			<query />
			<istransparent>0</istransparent>
			<hideicon>0</hideicon>
		</liteapp>
		<opensdk_share_is_modified>0</opensdk_share_is_modified>
	</appmsg>
	<fromusername>wxid_ubbh6q832tcs21</fromusername>
	<scene>0</scene>
	<appinfo>
		<version>1</version>
		<appname></appname>
	</appinfo>
	<commenturl></commenturl>
</msg>

2025-07-30 09:16:36 | DEBUG | 收到消息: {'MsgId': 1129892002, 'FromUserName': {'string': '27852221909@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 1, 'Content': {'string': 'wxid_ubbh6q832tcs21:\n快点签到啦'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1753838208, 'MsgSource': '<msgsource>\n\t<pua>1</pua>\n\t<eggIncluded>1</eggIncluded>\n\t<silence>1</silence>\n\t<membercount>145</membercount>\n\t<signature>N0_V1_5I2s7COQ|v1_oNcMjYyH</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'NewMsgId': 786059328515320067, 'MsgSeq': 871410895}
2025-07-30 09:16:36 | INFO | 收到文本消息: 消息ID:1129892002 来自:27852221909@chatroom 发送人:wxid_ubbh6q832tcs21 @:[] 内容:快点签到啦
2025-07-30 09:16:36 | DEBUG | 处理消息内容: '快点签到啦'
2025-07-30 09:16:36 | DEBUG | 消息内容 '快点签到啦' 不匹配任何命令，忽略
2025-07-30 09:16:43 | DEBUG | 收到消息: {'MsgId': 676119996, 'FromUserName': {'string': '48097389945@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 1, 'Content': {'string': 'wxid_wlnzvr8ivgd422:\n雨水打湿的我的电动车'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1753838215, 'MsgSource': '<msgsource>\n\t<pua>1</pua>\n\t<alnode>\n\t\t<cf>4</cf>\n\t\t<inlenlist>10</inlenlist>\n\t</alnode>\n\t<eggIncluded>1</eggIncluded>\n\t<silence>0</silence>\n\t<membercount>73</membercount>\n\t<signature>N0_V1_IgdZUkDm|v1_aI7M4vUo</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'PushContent': '锦岚 : 雨水打湿的我的电动车', 'NewMsgId': 5062718745815972602, 'MsgSeq': 871410896}
2025-07-30 09:16:43 | INFO | 收到文本消息: 消息ID:676119996 来自:48097389945@chatroom 发送人:wxid_wlnzvr8ivgd422 @:[] 内容:雨水打湿的我的电动车
2025-07-30 09:16:43 | DEBUG | 处理消息内容: '雨水打湿的我的电动车'
2025-07-30 09:16:43 | DEBUG | 消息内容 '雨水打湿的我的电动车' 不匹配任何命令，忽略
2025-07-30 09:16:55 | DEBUG | 收到消息: {'MsgId': 1448476109, 'FromUserName': {'string': '47325400669@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 1, 'Content': {'string': 'wxid_dg5xnz4s39ea21:\n武汉天气'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1753838226, 'MsgSource': '<msgsource>\n\t<pua>1</pua>\n\t<eggIncluded>1</eggIncluded>\n\t<silence>0</silence>\n\t<membercount>222</membercount>\n\t<signature>N0_V1_4SKlQLnU|v1_so5w1kdi</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'PushContent': '天 : 武汉天气', 'NewMsgId': 4485305222293928271, 'MsgSeq': 871410897}
2025-07-30 09:16:55 | INFO | 收到文本消息: 消息ID:1448476109 来自:47325400669@chatroom 发送人:wxid_dg5xnz4s39ea21 @:[] 内容:武汉天气
2025-07-30 09:16:55 | DEBUG | 处理消息内容: '武汉天气'
2025-07-30 09:16:55 | DEBUG | 消息内容 '武汉天气' 不匹配任何命令，忽略
2025-07-30 09:16:56 | INFO | [WeatherQuery] 检测到 '[地名]天气' 格式请求，将使用百度天气API
2025-07-30 09:16:57 | INFO | 发送文字消息: 对方wxid:47325400669@chatroom at:['wxid_dg5xnz4s39ea21'] 内容:@天 🔍 正在查询 武汉 的天气，稍等...
2025-07-30 09:16:57 | INFO | [WeatherQuery] 收到天气查询请求，用户: wxid_dg5xnz4s39ea21, 城市: 武汉
2025-07-30 09:16:57 | INFO | [WeatherQuery] 百度天气完整URL: https://mini.s-shot.ru/1000/PNG/1000/?https://weathernew.pae.baidu.com/weathernew/pc?query=%E6%AD%A6%E6%B1%89%E5%A4%A9%E6%B0%94&srcid=4982&forecast=long_day_forecast
2025-07-30 09:17:08 | INFO | [WeatherQuery] 百度天气图片已保存到: plugins\WeatherQuery\temp\baidu_weather_1753838217.png
2025-07-30 09:17:08 | INFO | [WeatherQuery] 发送百度天气图片...
2025-07-30 09:17:10 | INFO | 发送图片消息: 对方wxid:47325400669@chatroom 图片base64略
2025-07-30 09:17:10 | INFO | [WeatherQuery] 百度天气图片发送完成
2025-07-30 09:17:10 | DEBUG | 收到消息: {'MsgId': 1339723973, 'FromUserName': {'string': '47325400669@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 1, 'Content': {'string': 'wxid_16fck6g1b7ea22:\n@天\u2005🏙️ 城市: 武汉 (湖北)\n🕒 更新: 07-30 08:53\n🌦️ 天气: 多云\n🌡️ 温度: ↓26℃| 现32.6℃| ↑37℃\n🌬️ 风向: 北风\n💦 湿度: 64%\n🌅 日出/日落: 05:39 / 19:19\n\n⏳ 未来10小时的天气预报:\n     09:00 - 多云 - 32°C\n     10:00 - 多云 - 33°C\n     11:00 - 晴 - 34°C\n     12:00 - 晴 - 35°C\n     13:00 - 晴 - 36°C\n     14:00 - 晴 - 37°C\n     15:00 - 晴 - 37°C\n     16:00 - 晴 - 36°C\n     17:00 - 晴 - 36°C\n     18:00 - 晴 - 35°C'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1753838230, 'MsgSource': '<msgsource>\n\t<atuserlist>wxid_dg5xnz4s39ea21</atuserlist>\n\t<bizflag>0</bizflag>\n\t<silence>0</silence>\n\t<membercount>222</membercount>\n\t<signature>N0_V1_5WqcMqrc|v1_44IV//0i</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'PushContent': '阿天米德 : @天\u2005🏙️ 城市: 武汉 (湖北)\n\ue026 更新: 07-30 08:53\n🌦️ 天气: 多云\n...', 'NewMsgId': 4357908594746814006, 'MsgSeq': 871410900}
2025-07-30 09:17:10 | INFO | 收到文本消息: 消息ID:1339723973 来自:47325400669@chatroom 发送人:wxid_16fck6g1b7ea22 @:['wxid_dg5xnz4s39ea21'] 内容:@天 🏙️ 城市: 武汉 (湖北)
🕒 更新: 07-30 08:53
🌦️ 天气: 多云
🌡️ 温度: ↓26℃| 现32.6℃| ↑37℃
🌬️ 风向: 北风
💦 湿度: 64%
🌅 日出/日落: 05:39 / 19:19

⏳ 未来10小时的天气预报:
     09:00 - 多云 - 32°C
     10:00 - 多云 - 33°C
     11:00 - 晴 - 34°C
     12:00 - 晴 - 35°C
     13:00 - 晴 - 36°C
     14:00 - 晴 - 37°C
     15:00 - 晴 - 37°C
     16:00 - 晴 - 36°C
     17:00 - 晴 - 36°C
     18:00 - 晴 - 35°C
2025-07-30 09:17:10 | DEBUG | 处理消息内容: '@天 🏙️ 城市: 武汉 (湖北)
🕒 更新: 07-30 08:53
🌦️ 天气: 多云
🌡️ 温度: ↓26℃| 现32.6℃| ↑37℃
🌬️ 风向: 北风
💦 湿度: 64%
🌅 日出/日落: 05:39 / 19:19

⏳ 未来10小时的天气预报:
     09:00 - 多云 - 32°C
     10:00 - 多云 - 33°C
     11:00 - 晴 - 34°C
     12:00 - 晴 - 35°C
     13:00 - 晴 - 36°C
     14:00 - 晴 - 37°C
     15:00 - 晴 - 37°C
     16:00 - 晴 - 36°C
     17:00 - 晴 - 36°C
     18:00 - 晴 - 35°C'
2025-07-30 09:17:10 | DEBUG | 消息内容 '@天 🏙️ 城市: 武汉 (湖北)
🕒 更新: 07-30 08:53
🌦️ 天气: 多云
🌡️ 温度: ↓26℃| 现32.6℃| ↑37℃
🌬️ 风向: 北风
💦 湿度: 64%
🌅 日出/日落: 05:39 / 19:19

⏳ 未来10小时的天气预报:
     09:00 - 多云 - 32°C
     10:00 - 多云 - 33°C
     11:00 - 晴 - 34°C
     12:00 - 晴 - 35°C
     13:00 - 晴 - 36°C
     14:00 - 晴 - 37°C
     15:00 - 晴 - 37°C
     16:00 - 晴 - 36°C
     17:00 - 晴 - 36°C
     18:00 - 晴 - 35°C' 不匹配任何命令，忽略
2025-07-30 09:17:12 | DEBUG | 收到消息: {'MsgId': 846513581, 'FromUserName': {'string': '48097389945@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 1, 'Content': {'string': 'wxid_ubbh6q832tcs21:\n明年一定换小车'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1753838230, 'MsgSource': '<msgsource>\n\t<pua>1</pua>\n\t<eggIncluded>1</eggIncluded>\n\t<silence>0</silence>\n\t<membercount>73</membercount>\n\t<signature>N0_V1_5OxELYyX|v1_/LJKg2NN</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'PushContent': '郭 : 明年一定换小车', 'NewMsgId': 4632800514244896043, 'MsgSeq': 871410901}
2025-07-30 09:17:12 | INFO | 收到文本消息: 消息ID:846513581 来自:48097389945@chatroom 发送人:wxid_ubbh6q832tcs21 @:[] 内容:明年一定换小车
2025-07-30 09:17:13 | DEBUG | 处理消息内容: '明年一定换小车'
2025-07-30 09:17:13 | DEBUG | 消息内容 '明年一定换小车' 不匹配任何命令，忽略
2025-07-30 09:17:13 | DEBUG | 收到消息: {'MsgId': 1350452912, 'FromUserName': {'string': '47325400669@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 1, 'Content': {'string': 'wxid_fh84okl6f5wp22:\n🏙️ 城市: 武汉 (湖北)\n🕒 更新时间: 07-30\n🌤️ 天气: 多云\n🌡️ 温度: 当前 35℃, 最低 26℃, 最高 37℃\n🌬️ 风向: 北风, 风速: 7km/h\n💧 湿度: 64%\n🌅 日出/日落: 05:39 / 19:19\n😷 空气质量: 28 (PM2.5: 10)\n  - 建议: 空气很好，呼吸新鲜空气，拥抱大自然！\n\n💡 生活指数:\n  - 钓鱼指数: 适宜 (不冷不热全天钓，烈日炎炎早晚钓，夏末秋初阴雨钓，寒冬腊月中午钓，享受钓鱼乐趣同时还要注意安全)\n  - 感冒指数: 易发 (综合天气季节考虑，易发感冒)\n  - 过敏指数: 易过敏 (敏感人群容易过敏)\n  - 洗车指数: 适宜 (天气不错，非常适合洗车)\n  - 运动指数: 不建议 (风力稍强，户外可选择对风力要求不高的运动，推荐您进行室内运动)\n  - 紫外线指数: 最弱 (不需要采取防护措施)\n\n\n⏳ 未来 10 小时天气预报:\n  - 09:00 - 多云 - 32℃\n  - 10:00 - 多云 - 33℃\n  - 11:00 - 晴 - 34℃\n  - 12:00 - 晴 - 35℃\n  - 13:00 - 晴 - 36℃\n  - 14:00 - 晴 - 37℃\n  - 15:00 - 晴 - 37℃\n  - 16:00 - 晴 - 36℃\n  - 17:00 - 晴 - 36℃\n  - 18:00 - 晴 - 35℃'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1753838235, 'MsgSource': '<msgsource>\n\t<silence>0</silence>\n\t<membercount>222</membercount>\n\t<signature>N0_V1_eGAJefWG|v1_BMzhmCTW</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'PushContent': '阿猪米德 : 🏙️ 城市: 武汉 (湖北)\n\ue026 更新时间: 07-30\n🌤️ 天气: 多云\n🌡️ ...', 'NewMsgId': 2186330101302725711, 'MsgSeq': 871410902}
2025-07-30 09:17:13 | INFO | 收到文本消息: 消息ID:1350452912 来自:47325400669@chatroom 发送人:wxid_fh84okl6f5wp22 @:[] 内容:🏙️ 城市: 武汉 (湖北)
🕒 更新时间: 07-30
🌤️ 天气: 多云
🌡️ 温度: 当前 35℃, 最低 26℃, 最高 37℃
🌬️ 风向: 北风, 风速: 7km/h
💧 湿度: 64%
🌅 日出/日落: 05:39 / 19:19
😷 空气质量: 28 (PM2.5: 10)
  - 建议: 空气很好，呼吸新鲜空气，拥抱大自然！

💡 生活指数:
  - 钓鱼指数: 适宜 (不冷不热全天钓，烈日炎炎早晚钓，夏末秋初阴雨钓，寒冬腊月中午钓，享受钓鱼乐趣同时还要注意安全)
  - 感冒指数: 易发 (综合天气季节考虑，易发感冒)
  - 过敏指数: 易过敏 (敏感人群容易过敏)
  - 洗车指数: 适宜 (天气不错，非常适合洗车)
  - 运动指数: 不建议 (风力稍强，户外可选择对风力要求不高的运动，推荐您进行室内运动)
  - 紫外线指数: 最弱 (不需要采取防护措施)


⏳ 未来 10 小时天气预报:
  - 09:00 - 多云 - 32℃
  - 10:00 - 多云 - 33℃
  - 11:00 - 晴 - 34℃
  - 12:00 - 晴 - 35℃
  - 13:00 - 晴 - 36℃
  - 14:00 - 晴 - 37℃
  - 15:00 - 晴 - 37℃
  - 16:00 - 晴 - 36℃
  - 17:00 - 晴 - 36℃
  - 18:00 - 晴 - 35℃
2025-07-30 09:17:13 | DEBUG | 处理消息内容: '🏙️ 城市: 武汉 (湖北)
🕒 更新时间: 07-30
🌤️ 天气: 多云
🌡️ 温度: 当前 35℃, 最低 26℃, 最高 37℃
🌬️ 风向: 北风, 风速: 7km/h
💧 湿度: 64%
🌅 日出/日落: 05:39 / 19:19
😷 空气质量: 28 (PM2.5: 10)
  - 建议: 空气很好，呼吸新鲜空气，拥抱大自然！

💡 生活指数:
  - 钓鱼指数: 适宜 (不冷不热全天钓，烈日炎炎早晚钓，夏末秋初阴雨钓，寒冬腊月中午钓，享受钓鱼乐趣同时还要注意安全)
  - 感冒指数: 易发 (综合天气季节考虑，易发感冒)
  - 过敏指数: 易过敏 (敏感人群容易过敏)
  - 洗车指数: 适宜 (天气不错，非常适合洗车)
  - 运动指数: 不建议 (风力稍强，户外可选择对风力要求不高的运动，推荐您进行室内运动)
  - 紫外线指数: 最弱 (不需要采取防护措施)


⏳ 未来 10 小时天气预报:
  - 09:00 - 多云 - 32℃
  - 10:00 - 多云 - 33℃
  - 11:00 - 晴 - 34℃
  - 12:00 - 晴 - 35℃
  - 13:00 - 晴 - 36℃
  - 14:00 - 晴 - 37℃
  - 15:00 - 晴 - 37℃
  - 16:00 - 晴 - 36℃
  - 17:00 - 晴 - 36℃
  - 18:00 - 晴 - 35℃'
2025-07-30 09:17:13 | DEBUG | 消息内容 '🏙️ 城市: 武汉 (湖北)
🕒 更新时间: 07-30
🌤️ 天气: 多云
🌡️ 温度: 当前 35℃, 最低 26℃, 最高 37℃
🌬️ 风向: 北风, 风速: 7km/h
💧 湿度: 64%
🌅 日出/日落: 05:39 / 19:19
😷 空气质量: 28 (PM2.5: 10)
  - 建议: 空气很好，呼吸新鲜空气，拥抱大自然！

💡 生活指数:
  - 钓鱼指数: 适宜 (不冷不热全天钓，烈日炎炎早晚钓，夏末秋初阴雨钓，寒冬腊月中午钓，享受钓鱼乐趣同时还要注意安全)
  - 感冒指数: 易发 (综合天气季节考虑，易发感冒)
  - 过敏指数: 易过敏 (敏感人群容易过敏)
  - 洗车指数: 适宜 (天气不错，非常适合洗车)
  - 运动指数: 不建议 (风力稍强，户外可选择对风力要求不高的运动，推荐您进行室内运动)
  - 紫外线指数: 最弱 (不需要采取防护措施)


⏳ 未来 10 小时天气预报:
  - 09:00 - 多云 - 32℃
  - 10:00 - 多云 - 33℃
  - 11:00 - 晴 - 34℃
  - 12:00 - 晴 - 35℃
  - 13:00 - 晴 - 36℃
  - 14:00 - 晴 - 37℃
  - 15:00 - 晴 - 37℃
  - 16:00 - 晴 - 36℃
  - 17:00 - 晴 - 36℃
  - 18:00 - 晴 - 35℃' 不匹配任何命令，忽略
2025-07-30 09:17:15 | DEBUG | 收到消息: {'MsgId': 615876323, 'FromUserName': {'string': '48097389945@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 47, 'Content': {'string': 'wxid_jegyk4i3v7zg22:\n<msg><emoji fromusername="wxid_jegyk4i3v7zg22" tousername="48097389945@chatroom" type="2" idbuffer="media:0_0" md5="6a7bb44811f5639fb0c768c70fe298c0" len="479668" productid="com.tencent.xin.emoticon.person.stiker_17370056031693df064a067e6d" androidmd5="6a7bb44811f5639fb0c768c70fe298c0" androidlen="479668" s60v3md5="6a7bb44811f5639fb0c768c70fe298c0" s60v3len="479668" s60v5md5="6a7bb44811f5639fb0c768c70fe298c0" s60v5len="479668" cdnurl="http://wxapp.tc.qq.com/275/20304/stodownload?m=6a7bb44811f5639fb0c768c70fe298c0&amp;filekey=30350201010421301f020201130402534804106a7bb44811f5639fb0c768c70fe298c002030751b4040d00000004627466730000000132&amp;hy=SH&amp;storeid=267888267000a05bfb44b0f2a0000011300004f50534813d8b17156aa3a45a&amp;bizid=1023" designerid="" thumburl="http://wxapp.tc.qq.com/275/20304/stodownload?m=8b25f296f2ff36e7f2956bf4ca18ddbd&amp;filekey=30350201010421301f020201130402534804108b25f296f2ff36e7f2956bf4ca18ddbd0203008132040d00000004627466730000000132&amp;hy=SH&amp;storeid=26788833b000d3e76b44b0f2a0000011300004f50534829ee217156a5713c7&amp;bizid=1023" encrypturl="http://wxapp.tc.qq.com/262/20304/stodownload?m=9b8a1143b18426be92eee6c610ab622f&amp;filekey=30350201010421301f020201060402534804109b8a1143b18426be92eee6c610ab622f02030751c0040d00000004627466730000000132&amp;hy=SH&amp;storeid=2678e232c000ebb2eb44b0f2a0000010600004f5053482c87c0d1567cc8dca&amp;bizid=1023" aeskey="368e02c006174da8bf0f01c9ede2e919" externurl="http://wxapp.tc.qq.com/262/20304/stodownload?m=dcbd0132143cd132db269ead5d2ac2b6&amp;filekey=30350201010421301f020201060402535a0410dcbd0132143cd132db269ead5d2ac2b602030161a0040d00000004627466730000000132&amp;hy=SZ&amp;storeid=2678e2496000992b50801acb20000010600004f50535a0affb151567b9dcc3&amp;bizid=1023" externmd5="0132587bb8a40e32f95960c58a218fc9" width="240" height="240" tpurl="" tpauthkey="" attachedtext="" attachedtextcolor="" lensid="" emojiattr="" linkid="" desc="ChEKB2RlZmF1bHQSBumch+aDigoRCgdkZWZhdWx0EgbpnIfmg4o="></emoji><gameext type="0" content="0"></gameext><extcommoninfo></extcommoninfo></msg>'}, 'Status': 3, 'ImgStatus': 2, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1753838237, 'MsgSource': '<msgsource>\n\t<silence>0</silence>\n\t<membercount>73</membercount>\n\t<signature>N0_V1_BUNkALJY|v1_x3kzWaR/</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'PushContent': '阿尼亚与她在群聊中发了一个表情', 'NewMsgId': 2864977990263737623, 'MsgSeq': 871410903}
2025-07-30 09:17:15 | INFO | 收到表情消息: 消息ID:615876323 来自:48097389945@chatroom 发送人:wxid_jegyk4i3v7zg22 MD5:6a7bb44811f5639fb0c768c70fe298c0 大小:479668
2025-07-30 09:17:15 | DEBUG | [DoubaoImageRecognition] 缓存表情消息: 2864977990263737623
2025-07-30 09:17:16 | DEBUG | 收到消息: {'MsgId': 68812607, 'FromUserName': {'string': '48097389945@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 47, 'Content': {'string': 'wxid_wlnzvr8ivgd422:\n<msg><emoji fromusername = "wxid_wlnzvr8ivgd422" tousername = "48097389945@chatroom" type="2" idbuffer="media:0_0" md5="949c22ce23000c4fecb52cc8d2101e2d" len = "788489" productid="" androidmd5="949c22ce23000c4fecb52cc8d2101e2d" androidlen="788489" s60v3md5 = "949c22ce23000c4fecb52cc8d2101e2d" s60v3len="788489" s60v5md5 = "949c22ce23000c4fecb52cc8d2101e2d" s60v5len="788489" cdnurl = "http://vweixinf.tc.qq.com/110/20402/stodownload?m=949c22ce23000c4fecb52cc8d2101e2d&amp;filekey=30440201010430302e02016e040253480420393439633232636532333030306334666563623532636338643231303165326402030c0809040d00000004627466730000000132&amp;hy=SH&amp;storeid=2687371c0000b9154252daa890000006e01004fb2534800734031504b444c0&amp;ef=1&amp;bizid=1022" designerid = "" thumburl = "" encrypturl = "http://vweixinf.tc.qq.com/110/20402/stodownload?m=b3659f68af3b843fef7b0a3338980464&amp;filekey=30440201010430302e02016e040253480420623336353966363861663362383433666566376230613333333839383034363402030c0810040d00000004627466730000000132&amp;hy=SH&amp;storeid=2687371c0000cd23e252daa890000006e02004fb2534800734031504b444e3&amp;ef=2&amp;bizid=1022" aeskey= "4d39df5328cc4fe58abb05f8bc80f02c" externurl = "http://vweixinf.tc.qq.com/110/20403/stodownload?m=265d6f53c33fffff83ba067fe19778a9&amp;filekey=3043020101042f302d02016e0402534804203236356436663533633333666666666638336261303637666531393737386139020248e0040d00000004627466730000000132&amp;hy=SH&amp;storeid=2687371c0000f3d19252daa890000006e03004fb3534800734031504b4450d&amp;ef=3&amp;bizid=1022" externmd5 = "80b2f6ecfc78a63adbf5693cc4184f89" width= "300" height= "300" tpurl= "" tpauthkey= "" attachedtext= "" attachedtextcolor= "" lensid= "" emojiattr= "" linkid= "" desc= "" ></emoji>  </msg>'}, 'Status': 3, 'ImgStatus': 2, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1753838247, 'MsgSource': '<msgsource>\n\t<silence>0</silence>\n\t<membercount>73</membercount>\n\t<signature>N0_V1_BjioyTCB|v1_wBwcTHrK</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'PushContent': '锦岚在群聊中发了一个表情', 'NewMsgId': 2140388744654515095, 'MsgSeq': 871410906}
2025-07-30 09:17:16 | INFO | 收到表情消息: 消息ID:68812607 来自:48097389945@chatroom 发送人:wxid_wlnzvr8ivgd422 MD5:949c22ce23000c4fecb52cc8d2101e2d 大小:788489
2025-07-30 09:17:16 | DEBUG | [DoubaoImageRecognition] 缓存表情消息: 2140388744654515095
2025-07-30 09:18:10 | DEBUG | 收到消息: {'MsgId': 890235370, 'FromUserName': {'string': '48097389945@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 1, 'Content': {'string': 'wxid_ubbh6q832tcs21:\n雨水打湿我的小雅迪，明年一定换大奥迪'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1753838302, 'MsgSource': '<msgsource>\n\t<pua>1</pua>\n\t<eggIncluded>1</eggIncluded>\n\t<silence>0</silence>\n\t<membercount>73</membercount>\n\t<signature>N0_V1_kIVtYik7|v1_60TB/yqF</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'PushContent': '郭 : 雨水打湿我的小雅迪，明年一定换大奥迪', 'NewMsgId': 9204046414056036180, 'MsgSeq': 871410907}
2025-07-30 09:18:10 | INFO | 收到文本消息: 消息ID:890235370 来自:48097389945@chatroom 发送人:wxid_ubbh6q832tcs21 @:[] 内容:雨水打湿我的小雅迪，明年一定换大奥迪
2025-07-30 09:18:11 | DEBUG | 处理消息内容: '雨水打湿我的小雅迪，明年一定换大奥迪'
2025-07-30 09:18:11 | DEBUG | 消息内容 '雨水打湿我的小雅迪，明年一定换大奥迪' 不匹配任何命令，忽略
2025-07-30 09:18:41 | DEBUG | 收到消息: {'MsgId': 747784688, 'FromUserName': {'string': '47325400669@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 49, 'Content': {'string': 'wxid_ubbh6q832tcs21:\n<?xml version="1.0"?>\n<msg>\n\t<appmsg appid="" sdkver="0">\n\t\t<title>豆包 换成泳装</title>\n\t\t<des />\n\t\t<username />\n\t\t<action>view</action>\n\t\t<type>57</type>\n\t\t<showtype>0</showtype>\n\t\t<content />\n\t\t<url />\n\t\t<lowurl />\n\t\t<forwardflag>0</forwardflag>\n\t\t<dataurl />\n\t\t<lowdataurl />\n\t\t<contentattr>0</contentattr>\n\t\t<streamvideo>\n\t\t\t<streamvideourl />\n\t\t\t<streamvideototaltime>0</streamvideototaltime>\n\t\t\t<streamvideotitle />\n\t\t\t<streamvideowording />\n\t\t\t<streamvideoweburl />\n\t\t\t<streamvideothumburl />\n\t\t\t<streamvideoaduxinfo />\n\t\t\t<streamvideopublishid />\n\t\t</streamvideo>\n\t\t<canvasPageItem>\n\t\t\t<canvasPageXml><![CDATA[]]></canvasPageXml>\n\t\t</canvasPageItem>\n\t\t<refermsg>\n\t\t\t<type>3</type>\n\t\t\t<svrid>714496648952914170</svrid>\n\t\t\t<fromusr>47325400669@chatroom</fromusr>\n\t\t\t<chatusr>wxid_fh84okl6f5wp22</chatusr>\n\t\t\t<displayname>阿猪米德</displayname>\n\t\t\t<msgsource>&lt;msgsource&gt;\n\t&lt;sec_msg_node&gt;\n\t\t&lt;uuid&gt;a0edf93ac0f5bba3630cc1b4ccfca261_&lt;/uuid&gt;\n\t\t&lt;risk-file-flag /&gt;\n\t\t&lt;risk-file-md5-list /&gt;\n\t&lt;/sec_msg_node&gt;\n\t&lt;imgmsg_pd cdnmidimgurl_size="60450" cdnmidimgurl_pd_pri="50" cdnmidimgurl_pd="1" /&gt;\n\t&lt;silence&gt;1&lt;/silence&gt;\n\t&lt;membercount&gt;222&lt;/membercount&gt;\n\t&lt;NotAutoDownloadRange&gt;20:00-22:00;00:00-01:00&lt;/NotAutoDownloadRange&gt;\n\t&lt;signature&gt;N0_V1_VpmS2Kgp|v1_xxBolDsl&lt;/signature&gt;\n\t&lt;tmp_node&gt;\n\t\t&lt;publisher-id&gt;&lt;/publisher-id&gt;\n\t&lt;/tmp_node&gt;\n&lt;/msgsource&gt;\n</msgsource>\n\t\t\t<content>&lt;?xml version="1.0"?&gt;\n&lt;msg&gt;\n\t&lt;img aeskey="64706f757372756a6f74647173736f78" encryver="0" cdnthumbaeskey="64706f757372756a6f74647173736f78" cdnthumburl="3057020100044b30490201000204d6e1bab602033d14ba02048f06ff9d020468896c8d042437366439383062612d326338372d343761362d613865652d3762313337373565373862310204052828010201000405004c543e0054381738" cdnthumblength="3258" cdnthumbheight="100" cdnthumbwidth="66" cdnmidheight="0" cdnmidwidth="0" cdnhdheight="0" cdnhdwidth="0" cdnmidimgurl="3057020100044b30490201000204d6e1bab602033d14ba02048f06ff9d020468896c8d042437366439383062612d326338372d343761362d613865652d3762313337373565373862310204052828010201000405004c543e0054381738" length="60450" cdnbigimgurl="3057020100044b30490201000204d6e1bab602033d14ba02048f06ff9d020468896c8d042437366439383062612d326338372d343761362d613865652d3762313337373565373862310204052828010201000405004c543e0054381738" hdlength="196225" md5="4f2ea21d2d7ef077f433b2504011b6df"&gt;\n\t\t&lt;secHashInfoBase64 /&gt;\n\t\t&lt;live&gt;\n\t\t\t&lt;duration&gt;0&lt;/duration&gt;\n\t\t\t&lt;size&gt;0&lt;/size&gt;\n\t\t\t&lt;md5 /&gt;\n\t\t\t&lt;fileid /&gt;\n\t\t\t&lt;hdsize&gt;0&lt;/hdsize&gt;\n\t\t\t&lt;hdmd5 /&gt;\n\t\t\t&lt;hdfileid /&gt;\n\t\t\t&lt;stillimagetimems&gt;0&lt;/stillimagetimems&gt;\n\t\t&lt;/live&gt;\n\t&lt;/img&gt;\n\t&lt;platform_signature /&gt;\n\t&lt;imgdatahash /&gt;\n\t&lt;ImgSourceInfo&gt;\n\t\t&lt;ImgSourceUrl /&gt;\n\t\t&lt;BizType&gt;0&lt;/BizType&gt;\n\t&lt;/ImgSourceInfo&gt;\n&lt;/msg&gt;\n</content>\n\t\t\t<strid />\n\t\t\t<createtime>1753836684</createtime>\n\t\t</refermsg>\n\t\t<appattach>\n\t\t\t<totallen>0</totallen>\n\t\t\t<attachid />\n\t\t\t<cdnattachurl />\n\t\t\t<emoticonmd5 />\n\t\t\t<aeskey />\n\t\t\t<fileext />\n\t\t\t<islargefilemsg>0</islargefilemsg>\n\t\t</appattach>\n\t\t<extinfo />\n\t\t<androidsource>0</androidsource>\n\t\t<thumburl />\n\t\t<mediatagname />\n\t\t<messageaction><![CDATA[]]></messageaction>\n\t\t<messageext><![CDATA[]]></messageext>\n\t\t<emoticongift>\n\t\t\t<packageflag>0</packageflag>\n\t\t\t<packageid />\n\t\t</emoticongift>\n\t\t<emoticonshared>\n\t\t\t<packageflag>0</packageflag>\n\t\t\t<packageid />\n\t\t</emoticonshared>\n\t\t<designershared>\n\t\t\t<designeruin>0</designeruin>\n\t\t\t<designername>null</designername>\n\t\t\t<designerrediretcturl><![CDATA[null]]></designerrediretcturl>\n\t\t</designershared>\n\t\t<emotionpageshared>\n\t\t\t<tid>0</tid>\n\t\t\t<title>null</title>\n\t\t\t<desc>null</desc>\n\t\t\t<iconUrl><![CDATA[null]]></iconUrl>\n\t\t\t<secondUrl>null</secondUrl>\n\t\t\t<pageType>0</pageType>\n\t\t\t<setKey>null</setKey>\n\t\t</emotionpageshared>\n\t\t<webviewshared>\n\t\t\t<shareUrlOriginal />\n\t\t\t<shareUrlOpen />\n\t\t\t<jsAppId />\n\t\t\t<publisherId />\n\t\t\t<publisherReqId />\n\t\t</webviewshared>\n\t\t<template_id />\n\t\t<md5 />\n\t\t<websearch>\n\t\t\t<rec_category>0</rec_category>\n\t\t\t<channelId>0</channelId>\n\t\t</websearch>\n\t\t<weappinfo>\n\t\t\t<username />\n\t\t\t<appid />\n\t\t\t<appservicetype>0</appservicetype>\n\t\t\t<secflagforsinglepagemode>0</secflagforsinglepagemode>\n\t\t\t<videopageinfo>\n\t\t\t\t<thumbwidth>0</thumbwidth>\n\t\t\t\t<thumbheight>0</thumbheight>\n\t\t\t\t<fromopensdk>0</fromopensdk>\n\t\t\t</videopageinfo>\n\t\t</weappinfo>\n\t\t<statextstr />\n\t\t<musicShareItem>\n\t\t\t<musicDuration>0</musicDuration>\n\t\t</musicShareItem>\n\t\t<finderLiveProductShare>\n\t\t\t<finderLiveID><![CDATA[]]></finderLiveID>\n\t\t\t<finderUsername><![CDATA[]]></finderUsername>\n\t\t\t<finderObjectID><![CDATA[]]></finderObjectID>\n\t\t\t<finderNonceID><![CDATA[]]></finderNonceID>\n\t\t\t<liveStatus><![CDATA[]]></liveStatus>\n\t\t\t<appId><![CDATA[]]></appId>\n\t\t\t<pagePath><![CDATA[]]></pagePath>\n\t\t\t<productId><![CDATA[]]></productId>\n\t\t\t<coverUrl><![CDATA[]]></coverUrl>\n\t\t\t<productTitle><![CDATA[]]></productTitle>\n\t\t\t<marketPrice><![CDATA[0]]></marketPrice>\n\t\t\t<sellingPrice><![CDATA[0]]></sellingPrice>\n\t\t\t<platformHeadImg><![CDATA[]]></platformHeadImg>\n\t\t\t<platformName><![CDATA[]]></platformName>\n\t\t\t<shopWindowId><![CDATA[]]></shopWindowId>\n\t\t\t<flashSalePrice><![CDATA[0]]></flashSalePrice>\n\t\t\t<flashSaleEndTime><![CDATA[0]]></flashSaleEndTime>\n\t\t\t<ecSource><![CDATA[]]></ecSource>\n\t\t\t<sellingPriceWording><![CDATA[]]></sellingPriceWording>\n\t\t\t<platformIconURL><![CDATA[]]></platformIconURL>\n\t\t\t<firstProductTagURL><![CDATA[]]></firstProductTagURL>\n\t\t\t<firstProductTagAspectRatioString><![CDATA[0.0]]></firstProductTagAspectRatioString>\n\t\t\t<secondProductTagURL><![CDATA[]]></secondProductTagURL>\n\t\t\t<secondProductTagAspectRatioString><![CDATA[0.0]]></secondProductTagAspectRatioString>\n\t\t\t<firstGuaranteeWording><![CDATA[]]></firstGuaranteeWording>\n\t\t\t<secondGuaranteeWording><![CDATA[]]></secondGuaranteeWording>\n\t\t\t<thirdGuaranteeWording><![CDATA[]]></thirdGuaranteeWording>\n\t\t\t<isPriceBeginShow>false</isPriceBeginShow>\n\t\t\t<lastGMsgID><![CDATA[]]></lastGMsgID>\n\t\t\t<promoterKey><![CDATA[]]></promoterKey>\n\t\t\t<discountWording><![CDATA[]]></discountWording>\n\t\t\t<priceSuffixDescription><![CDATA[]]></priceSuffixDescription>\n\t\t\t<productCardKey><![CDATA[]]></productCardKey>\n\t\t\t<isWxShop><![CDATA[]]></isWxShop>\n\t\t\t<brandIconUrl><![CDATA[]]></brandIconUrl>\n\t\t\t<showBoxItemStringList />\n\t\t</finderLiveProductShare>\n\t\t<finderOrder>\n\t\t\t<appID><![CDATA[]]></appID>\n\t\t\t<orderID><![CDATA[]]></orderID>\n\t\t\t<path><![CDATA[]]></path>\n\t\t\t<priceWording><![CDATA[]]></priceWording>\n\t\t\t<stateWording><![CDATA[]]></stateWording>\n\t\t\t<productImageURL><![CDATA[]]></productImageURL>\n\t\t\t<products><![CDATA[]]></products>\n\t\t\t<productsCount><![CDATA[0]]></productsCount>\n\t\t\t<orderType><![CDATA[0]]></orderType>\n\t\t\t<newPriceWording><![CDATA[]]></newPriceWording>\n\t\t\t<newStateWording><![CDATA[]]></newStateWording>\n\t\t\t<useNewWording><![CDATA[0]]></useNewWording>\n\t\t</finderOrder>\n\t\t<finderShopWindowShare>\n\t\t\t<finderUsername><![CDATA[]]></finderUsername>\n\t\t\t<avatar><![CDATA[]]></avatar>\n\t\t\t<nickname><![CDATA[]]></nickname>\n\t\t\t<commodityInStockCount><![CDATA[]]></commodityInStockCount>\n\t\t\t<appId><![CDATA[]]></appId>\n\t\t\t<path><![CDATA[]]></path>\n\t\t\t<appUsername><![CDATA[]]></appUsername>\n\t\t\t<query><![CDATA[]]></query>\n\t\t\t<liteAppId><![CDATA[]]></liteAppId>\n\t\t\t<liteAppPath><![CDATA[]]></liteAppPath>\n\t\t\t<liteAppQuery><![CDATA[]]></liteAppQuery>\n\t\t\t<platformTagURL><![CDATA[]]></platformTagURL>\n\t\t\t<saleWording><![CDATA[]]></saleWording>\n\t\t\t<lastGMsgID><![CDATA[]]></lastGMsgID>\n\t\t\t<profileTypeWording><![CDATA[]]></profileTypeWording>\n\t\t\t<saleWordingExtra><![CDATA[]]></saleWordingExtra>\n\t\t\t<isWxShop><![CDATA[]]></isWxShop>\n\t\t\t<platformIconUrl><![CDATA[]]></platformIconUrl>\n\t\t\t<brandIconUrl><![CDATA[]]></brandIconUrl>\n\t\t\t<description><![CDATA[]]></description>\n\t\t\t<backgroundUrl><![CDATA[]]></backgroundUrl>\n\t\t\t<darkModePlatformIconUrl><![CDATA[]]></darkModePlatformIconUrl>\n\t\t\t<reputationInfo>\n\t\t\t\t<hasReputationInfo>0</hasReputationInfo>\n\t\t\t\t<reputationScore>0</reputationScore>\n\t\t\t\t<reputationWording />\n\t\t\t\t<reputationTextColor />\n\t\t\t\t<reputationLevelWording />\n\t\t\t\t<reputationBackgroundColor />\n\t\t\t</reputationInfo>\n\t\t\t<productImageURLList />\n\t\t</finderShopWindowShare>\n\t\t<findernamecard>\n\t\t\t<username />\n\t\t\t<avatar><![CDATA[]]></avatar>\n\t\t\t<nickname />\n\t\t\t<auth_job />\n\t\t\t<auth_icon>0</auth_icon>\n\t\t\t<auth_icon_url />\n\t\t\t<ecSource><![CDATA[]]></ecSource>\n\t\t\t<lastGMsgID><![CDATA[]]></lastGMsgID>\n\t\t</findernamecard>\n\t\t<finderGuarantee>\n\t\t\t<scene><![CDATA[0]]></scene>\n\t\t</finderGuarantee>\n\t\t<directshare>0</directshare>\n\t\t<gamecenter>\n\t\t\t<namecard>\n\t\t\t\t<iconUrl />\n\t\t\t\t<name />\n\t\t\t\t<desc />\n\t\t\t\t<tail />\n\t\t\t\t<jumpUrl />\n\t\t\t</namecard>\n\t\t</gamecenter>\n\t\t<patMsg>\n\t\t\t<chatUser />\n\t\t\t<records>\n\t\t\t\t<recordNum>0</recordNum>\n\t\t\t</records>\n\t\t</patMsg>\n\t\t<secretmsg>\n\t\t\t<issecretmsg>0</issecretmsg>\n\t\t</secretmsg>\n\t\t<referfromscene>0</referfromscene>\n\t\t<gameshare>\n\t\t\t<liteappext>\n\t\t\t\t<liteappbizdata />\n\t\t\t\t<priority>0</priority>\n\t\t\t</liteappext>\n\t\t\t<appbrandext>\n\t\t\t\t<litegameinfo />\n\t\t\t\t<priority>-1</priority>\n\t\t\t</appbrandext>\n\t\t\t<gameshareid />\n\t\t\t<sharedata />\n\t\t\t<isvideo>0</isvideo>\n\t\t\t<duration>-1</duration>\n\t\t\t<isexposed>0</isexposed>\n\t\t\t<readtext />\n\t\t</gameshare>\n\t\t<mpsharetrace>\n\t\t\t<hasfinderelement>0</hasfinderelement>\n\t\t\t<lastgmsgid />\n\t\t</mpsharetrace>\n\t\t<wxgamecard>\n\t\t\t<framesetname />\n\t\t\t<mbcarddata />\n\t\t\t<minpkgversion />\n\t\t\t<clientextinfo />\n\t\t\t<mbcardheight>0</mbcardheight>\n\t\t\t<isoldversion>0</isoldversion>\n\t\t</wxgamecard>\n\t\t<liteapp>\n\t\t\t<id>null</id>\n\t\t\t<path />\n\t\t\t<query />\n\t\t\t<istransparent>0</istransparent>\n\t\t\t<hideicon>0</hideicon>\n\t\t</liteapp>\n\t\t<opensdk_share_is_modified>0</opensdk_share_is_modified>\n\t</appmsg>\n\t<fromusername>wxid_ubbh6q832tcs21</fromusername>\n\t<scene>0</scene>\n\t<appinfo>\n\t\t<version>1</version>\n\t\t<appname></appname>\n\t</appinfo>\n\t<commenturl></commenturl>\n</msg>\n'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1753838332, 'MsgSource': '<msgsource>\n\t<alnode>\n\t\t<fr>4</fr>\n\t</alnode>\n\t<eggIncluded>1</eggIncluded>\n\t<sec_msg_node>\n\t\t<uuid>a0edf93ac0f5bba3630cc1b4ccfca261_</uuid>\n\t\t<risk-file-flag />\n\t\t<risk-file-md5-list />\n\t</sec_msg_node>\n\t<silence>0</silence>\n\t<membercount>222</membercount>\n\t<signature>N0_V1_wuwdhtRX|v1_DoTNlJes</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'PushContent': '郭 : 豆包 换成泳装', 'NewMsgId': 4608610668761478238, 'MsgSeq': 871410908}
2025-07-30 09:18:41 | DEBUG | 从群聊消息中提取发送者: wxid_ubbh6q832tcs21
2025-07-30 09:18:41 | DEBUG | 使用已解析的XML处理引用消息
2025-07-30 09:18:41 | INFO | 收到引用消息: 消息ID:747784688 来自:47325400669@chatroom 发送人:wxid_ubbh6q832tcs21 内容:豆包 换成泳装 引用类型:3
2025-07-30 09:18:41 | INFO | [DouBaoImageToImage] 收到引用消息: 豆包 换成泳装
2025-07-30 09:18:41 | INFO | [DouBaoImageToImage] 引用图片图生图 - 提示词: '换成泳装，比例「2:3」', 比例: 832x1248, 风格: None
2025-07-30 09:18:41 | INFO | [DouBaoImageToImage] 被引用消息类型: 3
2025-07-30 09:18:41 | INFO | [DouBaoImageToImage] 成功下载引用的图片并保存到: temp\doubao_image_to_image\quoted_image_1753838321.jpg
2025-07-30 09:18:41 | INFO | [DouBaoImageToImage] 开始豆包AI处理流程(无通知)，用户: wxid_ubbh6q832tcs21, 提示词: 换成泳装，比例「2:3」, 比例: 832x1248
2025-07-30 09:18:41 | INFO | [DouBaoImageToImage] 步骤1: 验证图片文件...
2025-07-30 09:18:41 | INFO | [DouBaoImageToImage] 图片验证成功，大小: 59.0KB
2025-07-30 09:18:41 | INFO | [DouBaoImageToImage] 步骤2: 调用豆包AI接口...
2025-07-30 09:18:41 | INFO | [DouBaoImageToImage] 开始豆包AI图生图处理，提示词: 换成泳装，比例「2:3」
2025-07-30 09:18:41 | INFO | [DouBaoImageToImage] 使用比例: 2:3 (832x1248)
2025-07-30 09:19:08 | ERROR | [DouBaoImageToImage] 豆包AI处理失败
2025-07-30 09:19:08 | WARNING | [DouBaoImageToImage] 豆包AI处理失败
2025-07-30 09:19:09 | INFO | 发送文字消息: 对方wxid:47325400669@chatroom at:['wxid_ubbh6q832tcs21'] 内容:@郭 豆包AI处理失败，等会再试试吧
2025-07-30 09:19:09 | INFO | [TimerTask] 收到引用消息调试信息:
2025-07-30 09:19:09 | INFO |   - 消息内容: 豆包 换成泳装
2025-07-30 09:19:09 | INFO |   - 群组ID: 47325400669@chatroom
2025-07-30 09:19:09 | INFO |   - 发送人: wxid_ubbh6q832tcs21
2025-07-30 09:19:09 | INFO |   - 引用信息: {'MsgType': 3, 'Content': '<?xml version="1.0"?>\n<msg>\n\t<appmsg appid="" sdkver="0">\n\t\t<title>豆包 换成泳装</title>\n\t\t<des />\n\t\t<username />\n\t\t<action>view</action>\n\t\t<type>57</type>\n\t\t<showtype>0</showtype>\n\t\t<content />\n\t\t<url />\n\t\t<lowurl />\n\t\t<forwardflag>0</forwardflag>\n\t\t<dataurl />\n\t\t<lowdataurl />\n\t\t<contentattr>0</contentattr>\n\t\t<streamvideo>\n\t\t\t<streamvideourl />\n\t\t\t<streamvideototaltime>0</streamvideototaltime>\n\t\t\t<streamvideotitle />\n\t\t\t<streamvideowording />\n\t\t\t<streamvideoweburl />\n\t\t\t<streamvideothumburl />\n\t\t\t<streamvideoaduxinfo />\n\t\t\t<streamvideopublishid />\n\t\t</streamvideo>\n\t\t<canvasPageItem>\n\t\t\t<canvasPageXml><![CDATA[]]></canvasPageXml>\n\t\t</canvasPageItem>\n\t\t<refermsg>\n\t\t\t<type>3</type>\n\t\t\t<svrid>714496648952914170</svrid>\n\t\t\t<fromusr>47325400669@chatroom</fromusr>\n\t\t\t<chatusr>wxid_fh84okl6f5wp22</chatusr>\n\t\t\t<displayname>阿猪米德</displayname>\n\t\t\t<msgsource>&lt;msgsource&gt;\n\t&lt;sec_msg_node&gt;\n\t\t&lt;uuid&gt;a0edf93ac0f5bba3630cc1b4ccfca261_&lt;/uuid&gt;\n\t\t&lt;risk-file-flag /&gt;\n\t\t&lt;risk-file-md5-list /&gt;\n\t&lt;/sec_msg_node&gt;\n\t&lt;imgmsg_pd cdnmidimgurl_size="60450" cdnmidimgurl_pd_pri="50" cdnmidimgurl_pd="1" /&gt;\n\t&lt;silence&gt;1&lt;/silence&gt;\n\t&lt;membercount&gt;222&lt;/membercount&gt;\n\t&lt;NotAutoDownloadRange&gt;20:00-22:00;00:00-01:00&lt;/NotAutoDownloadRange&gt;\n\t&lt;signature&gt;N0_V1_VpmS2Kgp|v1_xxBolDsl&lt;/signature&gt;\n\t&lt;tmp_node&gt;\n\t\t&lt;publisher-id&gt;&lt;/publisher-id&gt;\n\t&lt;/tmp_node&gt;\n&lt;/msgsource&gt;\n</msgsource>\n\t\t\t<content>&lt;?xml version="1.0"?&gt;\n&lt;msg&gt;\n\t&lt;img aeskey="64706f757372756a6f74647173736f78" encryver="0" cdnthumbaeskey="64706f757372756a6f74647173736f78" cdnthumburl="3057020100044b30490201000204d6e1bab602033d14ba02048f06ff9d020468896c8d042437366439383062612d326338372d343761362d613865652d3762313337373565373862310204052828010201000405004c543e0054381738" cdnthumblength="3258" cdnthumbheight="100" cdnthumbwidth="66" cdnmidheight="0" cdnmidwidth="0" cdnhdheight="0" cdnhdwidth="0" cdnmidimgurl="3057020100044b30490201000204d6e1bab602033d14ba02048f06ff9d020468896c8d042437366439383062612d326338372d343761362d613865652d3762313337373565373862310204052828010201000405004c543e0054381738" length="60450" cdnbigimgurl="3057020100044b30490201000204d6e1bab602033d14ba02048f06ff9d020468896c8d042437366439383062612d326338372d343761362d613865652d3762313337373565373862310204052828010201000405004c543e0054381738" hdlength="196225" md5="4f2ea21d2d7ef077f433b2504011b6df"&gt;\n\t\t&lt;secHashInfoBase64 /&gt;\n\t\t&lt;live&gt;\n\t\t\t&lt;duration&gt;0&lt;/duration&gt;\n\t\t\t&lt;size&gt;0&lt;/size&gt;\n\t\t\t&lt;md5 /&gt;\n\t\t\t&lt;fileid /&gt;\n\t\t\t&lt;hdsize&gt;0&lt;/hdsize&gt;\n\t\t\t&lt;hdmd5 /&gt;\n\t\t\t&lt;hdfileid /&gt;\n\t\t\t&lt;stillimagetimems&gt;0&lt;/stillimagetimems&gt;\n\t\t&lt;/live&gt;\n\t&lt;/img&gt;\n\t&lt;platform_signature /&gt;\n\t&lt;imgdatahash /&gt;\n\t&lt;ImgSourceInfo&gt;\n\t\t&lt;ImgSourceUrl /&gt;\n\t\t&lt;BizType&gt;0&lt;/BizType&gt;\n\t&lt;/ImgSourceInfo&gt;\n&lt;/msg&gt;\n</content>\n\t\t\t<strid />\n\t\t\t<createtime>1753836684</createtime>\n\t\t</refermsg>\n\t\t<appattach>\n\t\t\t<totallen>0</totallen>\n\t\t\t<attachid />\n\t\t\t<cdnattachurl />\n\t\t\t<emoticonmd5 />\n\t\t\t<aeskey />\n\t\t\t<fileext />\n\t\t\t<islargefilemsg>0</islargefilemsg>\n\t\t</appattach>\n\t\t<extinfo />\n\t\t<androidsource>0</androidsource>\n\t\t<thumburl />\n\t\t<mediatagname />\n\t\t<messageaction><![CDATA[]]></messageaction>\n\t\t<messageext><![CDATA[]]></messageext>\n\t\t<emoticongift>\n\t\t\t<packageflag>0</packageflag>\n\t\t\t<packageid />\n\t\t</emoticongift>\n\t\t<emoticonshared>\n\t\t\t<packageflag>0</packageflag>\n\t\t\t<packageid />\n\t\t</emoticonshared>\n\t\t<designershared>\n\t\t\t<designeruin>0</designeruin>\n\t\t\t<designername>null</designername>\n\t\t\t<designerrediretcturl><![CDATA[null]]></designerrediretcturl>\n\t\t</designershared>\n\t\t<emotionpageshared>\n\t\t\t<tid>0</tid>\n\t\t\t<title>null</title>\n\t\t\t<desc>null</desc>\n\t\t\t<iconUrl><![CDATA[null]]></iconUrl>\n\t\t\t<secondUrl>null</secondUrl>\n\t\t\t<pageType>0</pageType>\n\t\t\t<setKey>null</setKey>\n\t\t</emotionpageshared>\n\t\t<webviewshared>\n\t\t\t<shareUrlOriginal />\n\t\t\t<shareUrlOpen />\n\t\t\t<jsAppId />\n\t\t\t<publisherId />\n\t\t\t<publisherReqId />\n\t\t</webviewshared>\n\t\t<template_id />\n\t\t<md5 />\n\t\t<websearch>\n\t\t\t<rec_category>0</rec_category>\n\t\t\t<channelId>0</channelId>\n\t\t</websearch>\n\t\t<weappinfo>\n\t\t\t<username />\n\t\t\t<appid />\n\t\t\t<appservicetype>0</appservicetype>\n\t\t\t<secflagforsinglepagemode>0</secflagforsinglepagemode>\n\t\t\t<videopageinfo>\n\t\t\t\t<thumbwidth>0</thumbwidth>\n\t\t\t\t<thumbheight>0</thumbheight>\n\t\t\t\t<fromopensdk>0</fromopensdk>\n\t\t\t</videopageinfo>\n\t\t</weappinfo>\n\t\t<statextstr />\n\t\t<musicShareItem>\n\t\t\t<musicDuration>0</musicDuration>\n\t\t</musicShareItem>\n\t\t<finderLiveProductShare>\n\t\t\t<finderLiveID><![CDATA[]]></finderLiveID>\n\t\t\t<finderUsername><![CDATA[]]></finderUsername>\n\t\t\t<finderObjectID><![CDATA[]]></finderObjectID>\n\t\t\t<finderNonceID><![CDATA[]]></finderNonceID>\n\t\t\t<liveStatus><![CDATA[]]></liveStatus>\n\t\t\t<appId><![CDATA[]]></appId>\n\t\t\t<pagePath><![CDATA[]]></pagePath>\n\t\t\t<productId><![CDATA[]]></productId>\n\t\t\t<coverUrl><![CDATA[]]></coverUrl>\n\t\t\t<productTitle><![CDATA[]]></productTitle>\n\t\t\t<marketPrice><![CDATA[0]]></marketPrice>\n\t\t\t<sellingPrice><![CDATA[0]]></sellingPrice>\n\t\t\t<platformHeadImg><![CDATA[]]></platformHeadImg>\n\t\t\t<platformName><![CDATA[]]></platformName>\n\t\t\t<shopWindowId><![CDATA[]]></shopWindowId>\n\t\t\t<flashSalePrice><![CDATA[0]]></flashSalePrice>\n\t\t\t<flashSaleEndTime><![CDATA[0]]></flashSaleEndTime>\n\t\t\t<ecSource><![CDATA[]]></ecSource>\n\t\t\t<sellingPriceWording><![CDATA[]]></sellingPriceWording>\n\t\t\t<platformIconURL><![CDATA[]]></platformIconURL>\n\t\t\t<firstProductTagURL><![CDATA[]]></firstProductTagURL>\n\t\t\t<firstProductTagAspectRatioString><![CDATA[0.0]]></firstProductTagAspectRatioString>\n\t\t\t<secondProductTagURL><![CDATA[]]></secondProductTagURL>\n\t\t\t<secondProductTagAspectRatioString><![CDATA[0.0]]></secondProductTagAspectRatioString>\n\t\t\t<firstGuaranteeWording><![CDATA[]]></firstGuaranteeWording>\n\t\t\t<secondGuaranteeWording><![CDATA[]]></secondGuaranteeWording>\n\t\t\t<thirdGuaranteeWording><![CDATA[]]></thirdGuaranteeWording>\n\t\t\t<isPriceBeginShow>false</isPriceBeginShow>\n\t\t\t<lastGMsgID><![CDATA[]]></lastGMsgID>\n\t\t\t<promoterKey><![CDATA[]]></promoterKey>\n\t\t\t<discountWording><![CDATA[]]></discountWording>\n\t\t\t<priceSuffixDescription><![CDATA[]]></priceSuffixDescription>\n\t\t\t<productCardKey><![CDATA[]]></productCardKey>\n\t\t\t<isWxShop><![CDATA[]]></isWxShop>\n\t\t\t<brandIconUrl><![CDATA[]]></brandIconUrl>\n\t\t\t<showBoxItemStringList />\n\t\t</finderLiveProductShare>\n\t\t<finderOrder>\n\t\t\t<appID><![CDATA[]]></appID>\n\t\t\t<orderID><![CDATA[]]></orderID>\n\t\t\t<path><![CDATA[]]></path>\n\t\t\t<priceWording><![CDATA[]]></priceWording>\n\t\t\t<stateWording><![CDATA[]]></stateWording>\n\t\t\t<productImageURL><![CDATA[]]></productImageURL>\n\t\t\t<products><![CDATA[]]></products>\n\t\t\t<productsCount><![CDATA[0]]></productsCount>\n\t\t\t<orderType><![CDATA[0]]></orderType>\n\t\t\t<newPriceWording><![CDATA[]]></newPriceWording>\n\t\t\t<newStateWording><![CDATA[]]></newStateWording>\n\t\t\t<useNewWording><![CDATA[0]]></useNewWording>\n\t\t</finderOrder>\n\t\t<finderShopWindowShare>\n\t\t\t<finderUsername><![CDATA[]]></finderUsername>\n\t\t\t<avatar><![CDATA[]]></avatar>\n\t\t\t<nickname><![CDATA[]]></nickname>\n\t\t\t<commodityInStockCount><![CDATA[]]></commodityInStockCount>\n\t\t\t<appId><![CDATA[]]></appId>\n\t\t\t<path><![CDATA[]]></path>\n\t\t\t<appUsername><![CDATA[]]></appUsername>\n\t\t\t<query><![CDATA[]]></query>\n\t\t\t<liteAppId><![CDATA[]]></liteAppId>\n\t\t\t<liteAppPath><![CDATA[]]></liteAppPath>\n\t\t\t<liteAppQuery><![CDATA[]]></liteAppQuery>\n\t\t\t<platformTagURL><![CDATA[]]></platformTagURL>\n\t\t\t<saleWording><![CDATA[]]></saleWording>\n\t\t\t<lastGMsgID><![CDATA[]]></lastGMsgID>\n\t\t\t<profileTypeWording><![CDATA[]]></profileTypeWording>\n\t\t\t<saleWordingExtra><![CDATA[]]></saleWordingExtra>\n\t\t\t<isWxShop><![CDATA[]]></isWxShop>\n\t\t\t<platformIconUrl><![CDATA[]]></platformIconUrl>\n\t\t\t<brandIconUrl><![CDATA[]]></brandIconUrl>\n\t\t\t<description><![CDATA[]]></description>\n\t\t\t<backgroundUrl><![CDATA[]]></backgroundUrl>\n\t\t\t<darkModePlatformIconUrl><![CDATA[]]></darkModePlatformIconUrl>\n\t\t\t<reputationInfo>\n\t\t\t\t<hasReputationInfo>0</hasReputationInfo>\n\t\t\t\t<reputationScore>0</reputationScore>\n\t\t\t\t<reputationWording />\n\t\t\t\t<reputationTextColor />\n\t\t\t\t<reputationLevelWording />\n\t\t\t\t<reputationBackgroundColor />\n\t\t\t</reputationInfo>\n\t\t\t<productImageURLList />\n\t\t</finderShopWindowShare>\n\t\t<findernamecard>\n\t\t\t<username />\n\t\t\t<avatar><![CDATA[]]></avatar>\n\t\t\t<nickname />\n\t\t\t<auth_job />\n\t\t\t<auth_icon>0</auth_icon>\n\t\t\t<auth_icon_url />\n\t\t\t<ecSource><![CDATA[]]></ecSource>\n\t\t\t<lastGMsgID><![CDATA[]]></lastGMsgID>\n\t\t</findernamecard>\n\t\t<finderGuarantee>\n\t\t\t<scene><![CDATA[0]]></scene>\n\t\t</finderGuarantee>\n\t\t<directshare>0</directshare>\n\t\t<gamecenter>\n\t\t\t<namecard>\n\t\t\t\t<iconUrl />\n\t\t\t\t<name />\n\t\t\t\t<desc />\n\t\t\t\t<tail />\n\t\t\t\t<jumpUrl />\n\t\t\t</namecard>\n\t\t</gamecenter>\n\t\t<patMsg>\n\t\t\t<chatUser />\n\t\t\t<records>\n\t\t\t\t<recordNum>0</recordNum>\n\t\t\t</records>\n\t\t</patMsg>\n\t\t<secretmsg>\n\t\t\t<issecretmsg>0</issecretmsg>\n\t\t</secretmsg>\n\t\t<referfromscene>0</referfromscene>\n\t\t<gameshare>\n\t\t\t<liteappext>\n\t\t\t\t<liteappbizdata />\n\t\t\t\t<priority>0</priority>\n\t\t\t</liteappext>\n\t\t\t<appbrandext>\n\t\t\t\t<litegameinfo />\n\t\t\t\t<priority>-1</priority>\n\t\t\t</appbrandext>\n\t\t\t<gameshareid />\n\t\t\t<sharedata />\n\t\t\t<isvideo>0</isvideo>\n\t\t\t<duration>-1</duration>\n\t\t\t<isexposed>0</isexposed>\n\t\t\t<readtext />\n\t\t</gameshare>\n\t\t<mpsharetrace>\n\t\t\t<hasfinderelement>0</hasfinderelement>\n\t\t\t<lastgmsgid />\n\t\t</mpsharetrace>\n\t\t<wxgamecard>\n\t\t\t<framesetname />\n\t\t\t<mbcarddata />\n\t\t\t<minpkgversion />\n\t\t\t<clientextinfo />\n\t\t\t<mbcardheight>0</mbcardheight>\n\t\t\t<isoldversion>0</isoldversion>\n\t\t</wxgamecard>\n\t\t<liteapp>\n\t\t\t<id>null</id>\n\t\t\t<path />\n\t\t\t<query />\n\t\t\t<istransparent>0</istransparent>\n\t\t\t<hideicon>0</hideicon>\n\t\t</liteapp>\n\t\t<opensdk_share_is_modified>0</opensdk_share_is_modified>\n\t</appmsg>\n\t<fromusername>wxid_ubbh6q832tcs21</fromusername>\n\t<scene>0</scene>\n\t<appinfo>\n\t\t<version>1</version>\n\t\t<appname></appname>\n\t</appinfo>\n\t<commenturl></commenturl>\n</msg>\n', 'Msgid': '714496648952914170', 'NewMsgId': '714496648952914170', 'ToWxid': 'wxid_4usgcju5ey9q29', 'FromWxid': '47325400669@chatroom', 'Nickname': '阿猪米德', 'MsgSource': '<msgsource>\n\t<sec_msg_node>\n\t\t<uuid>a0edf93ac0f5bba3630cc1b4ccfca261_</uuid>\n\t\t<risk-file-flag />\n\t\t<risk-file-md5-list />\n\t</sec_msg_node>\n\t<imgmsg_pd cdnmidimgurl_size="60450" cdnmidimgurl_pd_pri="50" cdnmidimgurl_pd="1" />\n\t<silence>1</silence>\n\t<membercount>222</membercount>\n\t<NotAutoDownloadRange>20:00-22:00;00:00-01:00</NotAutoDownloadRange>\n\t<signature>N0_V1_VpmS2Kgp|v1_xxBolDsl</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'Createtime': '1753836684', 'SenderWxid': 'wxid_ubbh6q832tcs21'}
2025-07-30 09:19:09 | INFO |   - 引用消息ID: 
2025-07-30 09:19:09 | INFO |   - 引用消息类型: 
2025-07-30 09:19:09 | INFO |   - 引用消息内容: <?xml version="1.0"?>
<msg>
	<appmsg appid="" sdkver="0">
		<title>豆包 换成泳装</title>
		<des />
		<username />
		<action>view</action>
		<type>57</type>
		<showtype>0</showtype>
		<content />
		<url />
		<lowurl />
		<forwardflag>0</forwardflag>
		<dataurl />
		<lowdataurl />
		<contentattr>0</contentattr>
		<streamvideo>
			<streamvideourl />
			<streamvideototaltime>0</streamvideototaltime>
			<streamvideotitle />
			<streamvideowording />
			<streamvideoweburl />
			<streamvideothumburl />
			<streamvideoaduxinfo />
			<streamvideopublishid />
		</streamvideo>
		<canvasPageItem>
			<canvasPageXml><![CDATA[]]></canvasPageXml>
		</canvasPageItem>
		<refermsg>
			<type>3</type>
			<svrid>714496648952914170</svrid>
			<fromusr>47325400669@chatroom</fromusr>
			<chatusr>wxid_fh84okl6f5wp22</chatusr>
			<displayname>阿猪米德</displayname>
			<msgsource>&lt;msgsource&gt;
	&lt;sec_msg_node&gt;
		&lt;uuid&gt;a0edf93ac0f5bba3630cc1b4ccfca261_&lt;/uuid&gt;
		&lt;risk-file-flag /&gt;
		&lt;risk-file-md5-list /&gt;
	&lt;/sec_msg_node&gt;
	&lt;imgmsg_pd cdnmidimgurl_size="60450" cdnmidimgurl_pd_pri="50" cdnmidimgurl_pd="1" /&gt;
	&lt;silence&gt;1&lt;/silence&gt;
	&lt;membercount&gt;222&lt;/membercount&gt;
	&lt;NotAutoDownloadRange&gt;20:00-22:00;00:00-01:00&lt;/NotAutoDownloadRange&gt;
	&lt;signature&gt;N0_V1_VpmS2Kgp|v1_xxBolDsl&lt;/signature&gt;
	&lt;tmp_node&gt;
		&lt;publisher-id&gt;&lt;/publisher-id&gt;
	&lt;/tmp_node&gt;
&lt;/msgsource&gt;
</msgsource>
			<content>&lt;?xml version="1.0"?&gt;
&lt;msg&gt;
	&lt;img aeskey="64706f757372756a6f74647173736f78" encryver="0" cdnthumbaeskey="64706f757372756a6f74647173736f78" cdnthumburl="3057020100044b30490201000204d6e1bab602033d14ba02048f06ff9d020468896c8d042437366439383062612d326338372d343761362d613865652d3762313337373565373862310204052828010201000405004c543e0054381738" cdnthumblength="3258" cdnthumbheight="100" cdnthumbwidth="66" cdnmidheight="0" cdnmidwidth="0" cdnhdheight="0" cdnhdwidth="0" cdnmidimgurl="3057020100044b30490201000204d6e1bab602033d14ba02048f06ff9d020468896c8d042437366439383062612d326338372d343761362d613865652d3762313337373565373862310204052828010201000405004c543e0054381738" length="60450" cdnbigimgurl="3057020100044b30490201000204d6e1bab602033d14ba02048f06ff9d020468896c8d042437366439383062612d326338372d343761362d613865652d3762313337373565373862310204052828010201000405004c543e0054381738" hdlength="196225" md5="4f2ea21d2d7ef077f433b2504011b6df"&gt;
		&lt;secHashInfoBase64 /&gt;
		&lt;live&gt;
			&lt;duration&gt;0&lt;/duration&gt;
			&lt;size&gt;0&lt;/size&gt;
			&lt;md5 /&gt;
			&lt;fileid /&gt;
			&lt;hdsize&gt;0&lt;/hdsize&gt;
			&lt;hdmd5 /&gt;
			&lt;hdfileid /&gt;
			&lt;stillimagetimems&gt;0&lt;/stillimagetimems&gt;
		&lt;/live&gt;
	&lt;/img&gt;
	&lt;platform_signature /&gt;
	&lt;imgdatahash /&gt;
	&lt;ImgSourceInfo&gt;
		&lt;ImgSourceUrl /&gt;
		&lt;BizType&gt;0&lt;/BizType&gt;
	&lt;/ImgSourceInfo&gt;
&lt;/msg&gt;
</content>
			<strid />
			<createtime>1753836684</createtime>
		</refermsg>
		<appattach>
			<totallen>0</totallen>
			<attachid />
			<cdnattachurl />
			<emoticonmd5 />
			<aeskey />
			<fileext />
			<islargefilemsg>0</islargefilemsg>
		</appattach>
		<extinfo />
		<androidsource>0</androidsource>
		<thumburl />
		<mediatagname />
		<messageaction><![CDATA[]]></messageaction>
		<messageext><![CDATA[]]></messageext>
		<emoticongift>
			<packageflag>0</packageflag>
			<packageid />
		</emoticongift>
		<emoticonshared>
			<packageflag>0</packageflag>
			<packageid />
		</emoticonshared>
		<designershared>
			<designeruin>0</designeruin>
			<designername>null</designername>
			<designerrediretcturl><![CDATA[null]]></designerrediretcturl>
		</designershared>
		<emotionpageshared>
			<tid>0</tid>
			<title>null</title>
			<desc>null</desc>
			<iconUrl><![CDATA[null]]></iconUrl>
			<secondUrl>null</secondUrl>
			<pageType>0</pageType>
			<setKey>null</setKey>
		</emotionpageshared>
		<webviewshared>
			<shareUrlOriginal />
			<shareUrlOpen />
			<jsAppId />
			<publisherId />
			<publisherReqId />
		</webviewshared>
		<template_id />
		<md5 />
		<websearch>
			<rec_category>0</rec_category>
			<channelId>0</channelId>
		</websearch>
		<weappinfo>
			<username />
			<appid />
			<appservicetype>0</appservicetype>
			<secflagforsinglepagemode>0</secflagforsinglepagemode>
			<videopageinfo>
				<thumbwidth>0</thumbwidth>
				<thumbheight>0</thumbheight>
				<fromopensdk>0</fromopensdk>
			</videopageinfo>
		</weappinfo>
		<statextstr />
		<musicShareItem>
			<musicDuration>0</musicDuration>
		</musicShareItem>
		<finderLiveProductShare>
			<finderLiveID><![CDATA[]]></finderLiveID>
			<finderUsername><![CDATA[]]></finderUsername>
			<finderObjectID><![CDATA[]]></finderObjectID>
			<finderNonceID><![CDATA[]]></finderNonceID>
			<liveStatus><![CDATA[]]></liveStatus>
			<appId><![CDATA[]]></appId>
			<pagePath><![CDATA[]]></pagePath>
			<productId><![CDATA[]]></productId>
			<coverUrl><![CDATA[]]></coverUrl>
			<productTitle><![CDATA[]]></productTitle>
			<marketPrice><![CDATA[0]]></marketPrice>
			<sellingPrice><![CDATA[0]]></sellingPrice>
			<platformHeadImg><![CDATA[]]></platformHeadImg>
			<platformName><![CDATA[]]></platformName>
			<shopWindowId><![CDATA[]]></shopWindowId>
			<flashSalePrice><![CDATA[0]]></flashSalePrice>
			<flashSaleEndTime><![CDATA[0]]></flashSaleEndTime>
			<ecSource><![CDATA[]]></ecSource>
			<sellingPriceWording><![CDATA[]]></sellingPriceWording>
			<platformIconURL><![CDATA[]]></platformIconURL>
			<firstProductTagURL><![CDATA[]]></firstProductTagURL>
			<firstProductTagAspectRatioString><![CDATA[0.0]]></firstProductTagAspectRatioString>
			<secondProductTagURL><![CDATA[]]></secondProductTagURL>
			<secondProductTagAspectRatioString><![CDATA[0.0]]></secondProductTagAspectRatioString>
			<firstGuaranteeWording><![CDATA[]]></firstGuaranteeWording>
			<secondGuaranteeWording><![CDATA[]]></secondGuaranteeWording>
			<thirdGuaranteeWording><![CDATA[]]></thirdGuaranteeWording>
			<isPriceBeginShow>false</isPriceBeginShow>
			<lastGMsgID><![CDATA[]]></lastGMsgID>
			<promoterKey><![CDATA[]]></promoterKey>
			<discountWording><![CDATA[]]></discountWording>
			<priceSuffixDescription><![CDATA[]]></priceSuffixDescription>
			<productCardKey><![CDATA[]]></productCardKey>
			<isWxShop><![CDATA[]]></isWxShop>
			<brandIconUrl><![CDATA[]]></brandIconUrl>
			<showBoxItemStringList />
		</finderLiveProductShare>
		<finderOrder>
			<appID><![CDATA[]]></appID>
			<orderID><![CDATA[]]></orderID>
			<path><![CDATA[]]></path>
			<priceWording><![CDATA[]]></priceWording>
			<stateWording><![CDATA[]]></stateWording>
			<productImageURL><![CDATA[]]></productImageURL>
			<products><![CDATA[]]></products>
			<productsCount><![CDATA[0]]></productsCount>
			<orderType><![CDATA[0]]></orderType>
			<newPriceWording><![CDATA[]]></newPriceWording>
			<newStateWording><![CDATA[]]></newStateWording>
			<useNewWording><![CDATA[0]]></useNewWording>
		</finderOrder>
		<finderShopWindowShare>
			<finderUsername><![CDATA[]]></finderUsername>
			<avatar><![CDATA[]]></avatar>
			<nickname><![CDATA[]]></nickname>
			<commodityInStockCount><![CDATA[]]></commodityInStockCount>
			<appId><![CDATA[]]></appId>
			<path><![CDATA[]]></path>
			<appUsername><![CDATA[]]></appUsername>
			<query><![CDATA[]]></query>
			<liteAppId><![CDATA[]]></liteAppId>
			<liteAppPath><![CDATA[]]></liteAppPath>
			<liteAppQuery><![CDATA[]]></liteAppQuery>
			<platformTagURL><![CDATA[]]></platformTagURL>
			<saleWording><![CDATA[]]></saleWording>
			<lastGMsgID><![CDATA[]]></lastGMsgID>
			<profileTypeWording><![CDATA[]]></profileTypeWording>
			<saleWordingExtra><![CDATA[]]></saleWordingExtra>
			<isWxShop><![CDATA[]]></isWxShop>
			<platformIconUrl><![CDATA[]]></platformIconUrl>
			<brandIconUrl><![CDATA[]]></brandIconUrl>
			<description><![CDATA[]]></description>
			<backgroundUrl><![CDATA[]]></backgroundUrl>
			<darkModePlatformIconUrl><![CDATA[]]></darkModePlatformIconUrl>
			<reputationInfo>
				<hasReputationInfo>0</hasReputationInfo>
				<reputationScore>0</reputationScore>
				<reputationWording />
				<reputationTextColor />
				<reputationLevelWording />
				<reputationBackgroundColor />
			</reputationInfo>
			<productImageURLList />
		</finderShopWindowShare>
		<findernamecard>
			<username />
			<avatar><![CDATA[]]></avatar>
			<nickname />
			<auth_job />
			<auth_icon>0</auth_icon>
			<auth_icon_url />
			<ecSource><![CDATA[]]></ecSource>
			<lastGMsgID><![CDATA[]]></lastGMsgID>
		</findernamecard>
		<finderGuarantee>
			<scene><![CDATA[0]]></scene>
		</finderGuarantee>
		<directshare>0</directshare>
		<gamecenter>
			<namecard>
				<iconUrl />
				<name />
				<desc />
				<tail />
				<jumpUrl />
			</namecard>
		</gamecenter>
		<patMsg>
			<chatUser />
			<records>
				<recordNum>0</recordNum>
			</records>
		</patMsg>
		<secretmsg>
			<issecretmsg>0</issecretmsg>
		</secretmsg>
		<referfromscene>0</referfromscene>
		<gameshare>
			<liteappext>
				<liteappbizdata />
				<priority>0</priority>
			</liteappext>
			<appbrandext>
				<litegameinfo />
				<priority>-1</priority>
			</appbrandext>
			<gameshareid />
			<sharedata />
			<isvideo>0</isvideo>
			<duration>-1</duration>
			<isexposed>0</isexposed>
			<readtext />
		</gameshare>
		<mpsharetrace>
			<hasfinderelement>0</hasfinderelement>
			<lastgmsgid />
		</mpsharetrace>
		<wxgamecard>
			<framesetname />
			<mbcarddata />
			<minpkgversion />
			<clientextinfo />
			<mbcardheight>0</mbcardheight>
			<isoldversion>0</isoldversion>
		</wxgamecard>
		<liteapp>
			<id>null</id>
			<path />
			<query />
			<istransparent>0</istransparent>
			<hideicon>0</hideicon>
		</liteapp>
		<opensdk_share_is_modified>0</opensdk_share_is_modified>
	</appmsg>
	<fromusername>wxid_ubbh6q832tcs21</fromusername>
	<scene>0</scene>
	<appinfo>
		<version>1</version>
		<appname></appname>
	</appinfo>
	<commenturl></commenturl>
</msg>

2025-07-30 09:19:09 | INFO |   - 引用消息发送人: wxid_ubbh6q832tcs21
2025-07-30 09:19:09 | DEBUG | 收到消息: {'MsgId': 1257557647, 'FromUserName': {'string': '48097389945@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 1, 'Content': {'string': 'wxid_wlnzvr8ivgd422:\n看看雅迪'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1753838346, 'MsgSource': '<msgsource>\n\t<pua>1</pua>\n\t<eggIncluded>1</eggIncluded>\n\t<silence>0</silence>\n\t<membercount>73</membercount>\n\t<signature>N0_V1_pR0KUsCW|v1_+jCRRwsQ</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'PushContent': '锦岚 : 看看雅迪', 'NewMsgId': 2990997892930845748, 'MsgSeq': 871410909}
2025-07-30 09:19:09 | INFO | 收到文本消息: 消息ID:1257557647 来自:48097389945@chatroom 发送人:wxid_wlnzvr8ivgd422 @:[] 内容:看看雅迪
2025-07-30 09:19:10 | DEBUG | 处理消息内容: '看看雅迪'
2025-07-30 09:19:10 | DEBUG | 消息内容 '看看雅迪' 不匹配任何命令，忽略
2025-07-30 09:19:18 | DEBUG | [TempFileManager] 已清理文件: temp\doubao_image_to_image\quoted_image_1753838321.jpg
2025-07-30 09:19:52 | DEBUG | 收到消息: {'MsgId': 1148763699, 'FromUserName': {'string': '48097389945@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 3, 'Content': {'string': 'wxid_ubbh6q832tcs21:\n<?xml version="1.0"?>\n<msg>\n\t<img aeskey="4e93c024ddf6aa1bdbe476593d1ee717" encryver="1" cdnthumbaeskey="4e93c024ddf6aa1bdbe476593d1ee717" cdnthumburl="3057020100044b304902010002047fe06faa02032f53a30204d8752d70020468897341042464323763356639642d633039322d343635632d626239332d346431393139633338346637020405252a010201000405004c511f00" cdnthumblength="7010" cdnthumbheight="432" cdnthumbwidth="326" cdnmidheight="0" cdnmidwidth="0" cdnhdheight="0" cdnhdwidth="0" cdnmidimgurl="3057020100044b304902010002047fe06faa02032f53a30204d8752d70020468897341042464323763356639642d633039322d343635632d626239332d346431393139633338346637020405252a010201000405004c511f00" length="1153879" cdnbigimgurl="3057020100044b304902010002047fe06faa02032f53a30204d8752d70020468897341042464323763356639642d633039322d343635632d626239332d346431393139633338346637020405252a010201000405004c511f00" hdlength="922554" md5="09bcd7bb15d266245497a77e12c748e0" hevc_mid_size="126674" originsourcemd5="09bcd7bb15d266245497a77e12c748e0">\n\t\t<secHashInfoBase64>eyJwaGFzaCI6IjUxZTBiYTU0MzA1MDIwMDAiLCJwZHFIYXNoIjoiNDNjYmIzOTFlMGFmNDg4ZTk5\nMjQxMjc4NjA3YzViMzA3ZDQ2NzFkYWYwY2RlYjA3ZmJkNDYzYmE2MWIxYzFmNCJ9\n</secHashInfoBase64>\n\t\t<live>\n\t\t\t<duration>0</duration>\n\t\t\t<size>0</size>\n\t\t\t<md5 />\n\t\t\t<fileid />\n\t\t\t<hdsize>0</hdsize>\n\t\t\t<hdmd5 />\n\t\t\t<hdfileid />\n\t\t\t<stillimagetimems>0</stillimagetimems>\n\t\t</live>\n\t</img>\n\t<platform_signature />\n\t<imgdatahash />\n\t<ImgSourceInfo>\n\t\t<ImgSourceUrl />\n\t\t<BizType>0</BizType>\n\t</ImgSourceInfo>\n</msg>\n'}, 'Status': 3, 'ImgStatus': 2, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1753838404, 'MsgSource': '<msgsource>\n\t<alnode>\n\t\t<fr>2</fr>\n\t</alnode>\n\t<sec_msg_node>\n\t\t<uuid>1877a43034a01f9aa7b02fb3acf9d15d_</uuid>\n\t\t<risk-file-flag />\n\t\t<risk-file-md5-list />\n\t</sec_msg_node>\n\t<silence>0</silence>\n\t<membercount>73</membercount>\n\t<signature>N0_V1_3JlzwUAP|v1_fwicOMKh</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'PushContent': '郭在群聊中发了一张图片', 'NewMsgId': 5798753178988749274, 'MsgSeq': 871410912}
2025-07-30 09:19:52 | INFO | 收到图片消息: 消息ID:1148763699 来自:48097389945@chatroom 发送人:wxid_ubbh6q832tcs21 XML:<?xml version="1.0"?><msg><img aeskey="4e93c024ddf6aa1bdbe476593d1ee717" encryver="1" cdnthumbaeskey="4e93c024ddf6aa1bdbe476593d1ee717" cdnthumburl="3057020100044b304902010002047fe06faa02032f53a30204d8752d70020468897341042464323763356639642d633039322d343635632d626239332d346431393139633338346637020405252a010201000405004c511f00" cdnthumblength="7010" cdnthumbheight="432" cdnthumbwidth="326" cdnmidheight="0" cdnmidwidth="0" cdnhdheight="0" cdnhdwidth="0" cdnmidimgurl="3057020100044b304902010002047fe06faa02032f53a30204d8752d70020468897341042464323763356639642d633039322d343635632d626239332d346431393139633338346637020405252a010201000405004c511f00" length="1153879" cdnbigimgurl="3057020100044b304902010002047fe06faa02032f53a30204d8752d70020468897341042464323763356639642d633039322d343635632d626239332d346431393139633338346637020405252a010201000405004c511f00" hdlength="922554" md5="09bcd7bb15d266245497a77e12c748e0" hevc_mid_size="126674" originsourcemd5="09bcd7bb15d266245497a77e12c748e0"><secHashInfoBase64>eyJwaGFzaCI6IjUxZTBiYTU0MzA1MDIwMDAiLCJwZHFIYXNoIjoiNDNjYmIzOTFlMGFmNDg4ZTk5MjQxMjc4NjA3YzViMzA3ZDQ2NzFkYWYwY2RlYjA3ZmJkNDYzYmE2MWIxYzFmNCJ9</secHashInfoBase64><live><duration>0</duration><size>0</size><md5 /><fileid /><hdsize>0</hdsize><hdmd5 /><hdfileid /><stillimagetimems>0</stillimagetimems></live></img><platform_signature /><imgdatahash /><ImgSourceInfo><ImgSourceUrl /><BizType>0</BizType></ImgSourceInfo></msg>
2025-07-30 09:19:53 | INFO | [ImageEcho] 保存图片信息成功，当前群 48097389945@chatroom 已存储 5 张图片
2025-07-30 09:19:53 | INFO | [TimerTask] 缓存图片消息: 1148763699
2025-07-30 09:20:22 | DEBUG | 收到消息: {'MsgId': 1362573753, 'FromUserName': {'string': '47325400669@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 49, 'Content': {'string': 'wxid_ubbh6q832tcs21:\n<?xml version="1.0"?>\n<msg>\n\t<appmsg appid="" sdkver="0">\n\t\t<title>美图 换成泳装</title>\n\t\t<des />\n\t\t<username />\n\t\t<action>view</action>\n\t\t<type>57</type>\n\t\t<showtype>0</showtype>\n\t\t<content />\n\t\t<url />\n\t\t<lowurl />\n\t\t<forwardflag>0</forwardflag>\n\t\t<dataurl />\n\t\t<lowdataurl />\n\t\t<contentattr>0</contentattr>\n\t\t<streamvideo>\n\t\t\t<streamvideourl />\n\t\t\t<streamvideototaltime>0</streamvideototaltime>\n\t\t\t<streamvideotitle />\n\t\t\t<streamvideowording />\n\t\t\t<streamvideoweburl />\n\t\t\t<streamvideothumburl />\n\t\t\t<streamvideoaduxinfo />\n\t\t\t<streamvideopublishid />\n\t\t</streamvideo>\n\t\t<canvasPageItem>\n\t\t\t<canvasPageXml><![CDATA[]]></canvasPageXml>\n\t\t</canvasPageItem>\n\t\t<refermsg>\n\t\t\t<type>3</type>\n\t\t\t<svrid>714496648952914170</svrid>\n\t\t\t<fromusr>47325400669@chatroom</fromusr>\n\t\t\t<chatusr>wxid_fh84okl6f5wp22</chatusr>\n\t\t\t<displayname>阿猪米德</displayname>\n\t\t\t<msgsource>&lt;msgsource&gt;\n\t&lt;sec_msg_node&gt;\n\t\t&lt;uuid&gt;a0edf93ac0f5bba3630cc1b4ccfca261_&lt;/uuid&gt;\n\t\t&lt;risk-file-flag /&gt;\n\t\t&lt;risk-file-md5-list /&gt;\n\t&lt;/sec_msg_node&gt;\n\t&lt;imgmsg_pd cdnmidimgurl_size="60450" cdnmidimgurl_pd_pri="50" cdnmidimgurl_pd="1" /&gt;\n\t&lt;silence&gt;1&lt;/silence&gt;\n\t&lt;membercount&gt;222&lt;/membercount&gt;\n\t&lt;NotAutoDownloadRange&gt;20:00-22:00;00:00-01:00&lt;/NotAutoDownloadRange&gt;\n\t&lt;signature&gt;N0_V1_VpmS2Kgp|v1_xxBolDsl&lt;/signature&gt;\n\t&lt;tmp_node&gt;\n\t\t&lt;publisher-id&gt;&lt;/publisher-id&gt;\n\t&lt;/tmp_node&gt;\n&lt;/msgsource&gt;\n</msgsource>\n\t\t\t<content>&lt;?xml version="1.0"?&gt;\n&lt;msg&gt;\n\t&lt;img aeskey="64706f757372756a6f74647173736f78" encryver="0" cdnthumbaeskey="64706f757372756a6f74647173736f78" cdnthumburl="3057020100044b30490201000204d6e1bab602033d14ba02048f06ff9d020468896c8d042437366439383062612d326338372d343761362d613865652d3762313337373565373862310204052828010201000405004c543e0054381738" cdnthumblength="3258" cdnthumbheight="100" cdnthumbwidth="66" cdnmidheight="0" cdnmidwidth="0" cdnhdheight="0" cdnhdwidth="0" cdnmidimgurl="3057020100044b30490201000204d6e1bab602033d14ba02048f06ff9d020468896c8d042437366439383062612d326338372d343761362d613865652d3762313337373565373862310204052828010201000405004c543e0054381738" length="60450" cdnbigimgurl="3057020100044b30490201000204d6e1bab602033d14ba02048f06ff9d020468896c8d042437366439383062612d326338372d343761362d613865652d3762313337373565373862310204052828010201000405004c543e0054381738" hdlength="196225" md5="4f2ea21d2d7ef077f433b2504011b6df"&gt;\n\t\t&lt;secHashInfoBase64 /&gt;\n\t\t&lt;live&gt;\n\t\t\t&lt;duration&gt;0&lt;/duration&gt;\n\t\t\t&lt;size&gt;0&lt;/size&gt;\n\t\t\t&lt;md5 /&gt;\n\t\t\t&lt;fileid /&gt;\n\t\t\t&lt;hdsize&gt;0&lt;/hdsize&gt;\n\t\t\t&lt;hdmd5 /&gt;\n\t\t\t&lt;hdfileid /&gt;\n\t\t\t&lt;stillimagetimems&gt;0&lt;/stillimagetimems&gt;\n\t\t&lt;/live&gt;\n\t&lt;/img&gt;\n\t&lt;platform_signature /&gt;\n\t&lt;imgdatahash /&gt;\n\t&lt;ImgSourceInfo&gt;\n\t\t&lt;ImgSourceUrl /&gt;\n\t\t&lt;BizType&gt;0&lt;/BizType&gt;\n\t&lt;/ImgSourceInfo&gt;\n&lt;/msg&gt;\n</content>\n\t\t\t<strid />\n\t\t\t<createtime>1753836684</createtime>\n\t\t</refermsg>\n\t\t<appattach>\n\t\t\t<totallen>0</totallen>\n\t\t\t<attachid />\n\t\t\t<cdnattachurl />\n\t\t\t<emoticonmd5 />\n\t\t\t<aeskey />\n\t\t\t<fileext />\n\t\t\t<islargefilemsg>0</islargefilemsg>\n\t\t</appattach>\n\t\t<extinfo />\n\t\t<androidsource>0</androidsource>\n\t\t<thumburl />\n\t\t<mediatagname />\n\t\t<messageaction><![CDATA[]]></messageaction>\n\t\t<messageext><![CDATA[]]></messageext>\n\t\t<emoticongift>\n\t\t\t<packageflag>0</packageflag>\n\t\t\t<packageid />\n\t\t</emoticongift>\n\t\t<emoticonshared>\n\t\t\t<packageflag>0</packageflag>\n\t\t\t<packageid />\n\t\t</emoticonshared>\n\t\t<designershared>\n\t\t\t<designeruin>0</designeruin>\n\t\t\t<designername>null</designername>\n\t\t\t<designerrediretcturl><![CDATA[null]]></designerrediretcturl>\n\t\t</designershared>\n\t\t<emotionpageshared>\n\t\t\t<tid>0</tid>\n\t\t\t<title>null</title>\n\t\t\t<desc>null</desc>\n\t\t\t<iconUrl><![CDATA[null]]></iconUrl>\n\t\t\t<secondUrl>null</secondUrl>\n\t\t\t<pageType>0</pageType>\n\t\t\t<setKey>null</setKey>\n\t\t</emotionpageshared>\n\t\t<webviewshared>\n\t\t\t<shareUrlOriginal />\n\t\t\t<shareUrlOpen />\n\t\t\t<jsAppId />\n\t\t\t<publisherId />\n\t\t\t<publisherReqId />\n\t\t</webviewshared>\n\t\t<template_id />\n\t\t<md5 />\n\t\t<websearch>\n\t\t\t<rec_category>0</rec_category>\n\t\t\t<channelId>0</channelId>\n\t\t</websearch>\n\t\t<weappinfo>\n\t\t\t<username />\n\t\t\t<appid />\n\t\t\t<appservicetype>0</appservicetype>\n\t\t\t<secflagforsinglepagemode>0</secflagforsinglepagemode>\n\t\t\t<videopageinfo>\n\t\t\t\t<thumbwidth>0</thumbwidth>\n\t\t\t\t<thumbheight>0</thumbheight>\n\t\t\t\t<fromopensdk>0</fromopensdk>\n\t\t\t</videopageinfo>\n\t\t</weappinfo>\n\t\t<statextstr />\n\t\t<musicShareItem>\n\t\t\t<musicDuration>0</musicDuration>\n\t\t</musicShareItem>\n\t\t<finderLiveProductShare>\n\t\t\t<finderLiveID><![CDATA[]]></finderLiveID>\n\t\t\t<finderUsername><![CDATA[]]></finderUsername>\n\t\t\t<finderObjectID><![CDATA[]]></finderObjectID>\n\t\t\t<finderNonceID><![CDATA[]]></finderNonceID>\n\t\t\t<liveStatus><![CDATA[]]></liveStatus>\n\t\t\t<appId><![CDATA[]]></appId>\n\t\t\t<pagePath><![CDATA[]]></pagePath>\n\t\t\t<productId><![CDATA[]]></productId>\n\t\t\t<coverUrl><![CDATA[]]></coverUrl>\n\t\t\t<productTitle><![CDATA[]]></productTitle>\n\t\t\t<marketPrice><![CDATA[0]]></marketPrice>\n\t\t\t<sellingPrice><![CDATA[0]]></sellingPrice>\n\t\t\t<platformHeadImg><![CDATA[]]></platformHeadImg>\n\t\t\t<platformName><![CDATA[]]></platformName>\n\t\t\t<shopWindowId><![CDATA[]]></shopWindowId>\n\t\t\t<flashSalePrice><![CDATA[0]]></flashSalePrice>\n\t\t\t<flashSaleEndTime><![CDATA[0]]></flashSaleEndTime>\n\t\t\t<ecSource><![CDATA[]]></ecSource>\n\t\t\t<sellingPriceWording><![CDATA[]]></sellingPriceWording>\n\t\t\t<platformIconURL><![CDATA[]]></platformIconURL>\n\t\t\t<firstProductTagURL><![CDATA[]]></firstProductTagURL>\n\t\t\t<firstProductTagAspectRatioString><![CDATA[0.0]]></firstProductTagAspectRatioString>\n\t\t\t<secondProductTagURL><![CDATA[]]></secondProductTagURL>\n\t\t\t<secondProductTagAspectRatioString><![CDATA[0.0]]></secondProductTagAspectRatioString>\n\t\t\t<firstGuaranteeWording><![CDATA[]]></firstGuaranteeWording>\n\t\t\t<secondGuaranteeWording><![CDATA[]]></secondGuaranteeWording>\n\t\t\t<thirdGuaranteeWording><![CDATA[]]></thirdGuaranteeWording>\n\t\t\t<isPriceBeginShow>false</isPriceBeginShow>\n\t\t\t<lastGMsgID><![CDATA[]]></lastGMsgID>\n\t\t\t<promoterKey><![CDATA[]]></promoterKey>\n\t\t\t<discountWording><![CDATA[]]></discountWording>\n\t\t\t<priceSuffixDescription><![CDATA[]]></priceSuffixDescription>\n\t\t\t<productCardKey><![CDATA[]]></productCardKey>\n\t\t\t<isWxShop><![CDATA[]]></isWxShop>\n\t\t\t<brandIconUrl><![CDATA[]]></brandIconUrl>\n\t\t\t<showBoxItemStringList />\n\t\t</finderLiveProductShare>\n\t\t<finderOrder>\n\t\t\t<appID><![CDATA[]]></appID>\n\t\t\t<orderID><![CDATA[]]></orderID>\n\t\t\t<path><![CDATA[]]></path>\n\t\t\t<priceWording><![CDATA[]]></priceWording>\n\t\t\t<stateWording><![CDATA[]]></stateWording>\n\t\t\t<productImageURL><![CDATA[]]></productImageURL>\n\t\t\t<products><![CDATA[]]></products>\n\t\t\t<productsCount><![CDATA[0]]></productsCount>\n\t\t\t<orderType><![CDATA[0]]></orderType>\n\t\t\t<newPriceWording><![CDATA[]]></newPriceWording>\n\t\t\t<newStateWording><![CDATA[]]></newStateWording>\n\t\t\t<useNewWording><![CDATA[0]]></useNewWording>\n\t\t</finderOrder>\n\t\t<finderShopWindowShare>\n\t\t\t<finderUsername><![CDATA[]]></finderUsername>\n\t\t\t<avatar><![CDATA[]]></avatar>\n\t\t\t<nickname><![CDATA[]]></nickname>\n\t\t\t<commodityInStockCount><![CDATA[]]></commodityInStockCount>\n\t\t\t<appId><![CDATA[]]></appId>\n\t\t\t<path><![CDATA[]]></path>\n\t\t\t<appUsername><![CDATA[]]></appUsername>\n\t\t\t<query><![CDATA[]]></query>\n\t\t\t<liteAppId><![CDATA[]]></liteAppId>\n\t\t\t<liteAppPath><![CDATA[]]></liteAppPath>\n\t\t\t<liteAppQuery><![CDATA[]]></liteAppQuery>\n\t\t\t<platformTagURL><![CDATA[]]></platformTagURL>\n\t\t\t<saleWording><![CDATA[]]></saleWording>\n\t\t\t<lastGMsgID><![CDATA[]]></lastGMsgID>\n\t\t\t<profileTypeWording><![CDATA[]]></profileTypeWording>\n\t\t\t<saleWordingExtra><![CDATA[]]></saleWordingExtra>\n\t\t\t<isWxShop><![CDATA[]]></isWxShop>\n\t\t\t<platformIconUrl><![CDATA[]]></platformIconUrl>\n\t\t\t<brandIconUrl><![CDATA[]]></brandIconUrl>\n\t\t\t<description><![CDATA[]]></description>\n\t\t\t<backgroundUrl><![CDATA[]]></backgroundUrl>\n\t\t\t<darkModePlatformIconUrl><![CDATA[]]></darkModePlatformIconUrl>\n\t\t\t<reputationInfo>\n\t\t\t\t<hasReputationInfo>0</hasReputationInfo>\n\t\t\t\t<reputationScore>0</reputationScore>\n\t\t\t\t<reputationWording />\n\t\t\t\t<reputationTextColor />\n\t\t\t\t<reputationLevelWording />\n\t\t\t\t<reputationBackgroundColor />\n\t\t\t</reputationInfo>\n\t\t\t<productImageURLList />\n\t\t</finderShopWindowShare>\n\t\t<findernamecard>\n\t\t\t<username />\n\t\t\t<avatar><![CDATA[]]></avatar>\n\t\t\t<nickname />\n\t\t\t<auth_job />\n\t\t\t<auth_icon>0</auth_icon>\n\t\t\t<auth_icon_url />\n\t\t\t<ecSource><![CDATA[]]></ecSource>\n\t\t\t<lastGMsgID><![CDATA[]]></lastGMsgID>\n\t\t</findernamecard>\n\t\t<finderGuarantee>\n\t\t\t<scene><![CDATA[0]]></scene>\n\t\t</finderGuarantee>\n\t\t<directshare>0</directshare>\n\t\t<gamecenter>\n\t\t\t<namecard>\n\t\t\t\t<iconUrl />\n\t\t\t\t<name />\n\t\t\t\t<desc />\n\t\t\t\t<tail />\n\t\t\t\t<jumpUrl />\n\t\t\t</namecard>\n\t\t</gamecenter>\n\t\t<patMsg>\n\t\t\t<chatUser />\n\t\t\t<records>\n\t\t\t\t<recordNum>0</recordNum>\n\t\t\t</records>\n\t\t</patMsg>\n\t\t<secretmsg>\n\t\t\t<issecretmsg>0</issecretmsg>\n\t\t</secretmsg>\n\t\t<referfromscene>0</referfromscene>\n\t\t<gameshare>\n\t\t\t<liteappext>\n\t\t\t\t<liteappbizdata />\n\t\t\t\t<priority>0</priority>\n\t\t\t</liteappext>\n\t\t\t<appbrandext>\n\t\t\t\t<litegameinfo />\n\t\t\t\t<priority>-1</priority>\n\t\t\t</appbrandext>\n\t\t\t<gameshareid />\n\t\t\t<sharedata />\n\t\t\t<isvideo>0</isvideo>\n\t\t\t<duration>-1</duration>\n\t\t\t<isexposed>0</isexposed>\n\t\t\t<readtext />\n\t\t</gameshare>\n\t\t<mpsharetrace>\n\t\t\t<hasfinderelement>0</hasfinderelement>\n\t\t\t<lastgmsgid />\n\t\t</mpsharetrace>\n\t\t<wxgamecard>\n\t\t\t<framesetname />\n\t\t\t<mbcarddata />\n\t\t\t<minpkgversion />\n\t\t\t<clientextinfo />\n\t\t\t<mbcardheight>0</mbcardheight>\n\t\t\t<isoldversion>0</isoldversion>\n\t\t</wxgamecard>\n\t\t<liteapp>\n\t\t\t<id>null</id>\n\t\t\t<path />\n\t\t\t<query />\n\t\t\t<istransparent>0</istransparent>\n\t\t\t<hideicon>0</hideicon>\n\t\t</liteapp>\n\t\t<opensdk_share_is_modified>0</opensdk_share_is_modified>\n\t</appmsg>\n\t<fromusername>wxid_ubbh6q832tcs21</fromusername>\n\t<scene>0</scene>\n\t<appinfo>\n\t\t<version>1</version>\n\t\t<appname></appname>\n\t</appinfo>\n\t<commenturl></commenturl>\n</msg>\n'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1753838434, 'MsgSource': '<msgsource>\n\t<alnode>\n\t\t<fr>4</fr>\n\t</alnode>\n\t<eggIncluded>1</eggIncluded>\n\t<sec_msg_node>\n\t\t<uuid>a0edf93ac0f5bba3630cc1b4ccfca261_</uuid>\n\t\t<risk-file-flag />\n\t\t<risk-file-md5-list />\n\t</sec_msg_node>\n\t<silence>0</silence>\n\t<membercount>222</membercount>\n\t<signature>N0_V1_KTtnqu2N|v1_a/HC55i+</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'PushContent': '郭 : 美图 换成泳装', 'NewMsgId': 4516714990539066945, 'MsgSeq': 871410913}
2025-07-30 09:20:22 | DEBUG | 从群聊消息中提取发送者: wxid_ubbh6q832tcs21
2025-07-30 09:20:22 | DEBUG | 使用已解析的XML处理引用消息
2025-07-30 09:20:22 | INFO | 收到引用消息: 消息ID:1362573753 来自:47325400669@chatroom 发送人:wxid_ubbh6q832tcs21 内容:美图 换成泳装 引用类型:3
2025-07-30 09:20:22 | INFO | [DouBaoImageToImage] 收到引用消息: 美图 换成泳装
2025-07-30 09:20:23 | INFO | 发送文字消息: 对方wxid:47325400669@chatroom at: 内容:OK
2025-07-30 09:20:23 | INFO | [TimerTask] 收到引用消息调试信息:
2025-07-30 09:20:23 | INFO |   - 消息内容: 美图 换成泳装
2025-07-30 09:20:23 | INFO |   - 群组ID: 47325400669@chatroom
2025-07-30 09:20:23 | INFO |   - 发送人: wxid_ubbh6q832tcs21
2025-07-30 09:20:23 | INFO |   - 引用信息: {'MsgType': 3, 'Content': '<?xml version="1.0"?>\n<msg>\n\t<appmsg appid="" sdkver="0">\n\t\t<title>美图 换成泳装</title>\n\t\t<des />\n\t\t<username />\n\t\t<action>view</action>\n\t\t<type>57</type>\n\t\t<showtype>0</showtype>\n\t\t<content />\n\t\t<url />\n\t\t<lowurl />\n\t\t<forwardflag>0</forwardflag>\n\t\t<dataurl />\n\t\t<lowdataurl />\n\t\t<contentattr>0</contentattr>\n\t\t<streamvideo>\n\t\t\t<streamvideourl />\n\t\t\t<streamvideototaltime>0</streamvideototaltime>\n\t\t\t<streamvideotitle />\n\t\t\t<streamvideowording />\n\t\t\t<streamvideoweburl />\n\t\t\t<streamvideothumburl />\n\t\t\t<streamvideoaduxinfo />\n\t\t\t<streamvideopublishid />\n\t\t</streamvideo>\n\t\t<canvasPageItem>\n\t\t\t<canvasPageXml><![CDATA[]]></canvasPageXml>\n\t\t</canvasPageItem>\n\t\t<refermsg>\n\t\t\t<type>3</type>\n\t\t\t<svrid>714496648952914170</svrid>\n\t\t\t<fromusr>47325400669@chatroom</fromusr>\n\t\t\t<chatusr>wxid_fh84okl6f5wp22</chatusr>\n\t\t\t<displayname>阿猪米德</displayname>\n\t\t\t<msgsource>&lt;msgsource&gt;\n\t&lt;sec_msg_node&gt;\n\t\t&lt;uuid&gt;a0edf93ac0f5bba3630cc1b4ccfca261_&lt;/uuid&gt;\n\t\t&lt;risk-file-flag /&gt;\n\t\t&lt;risk-file-md5-list /&gt;\n\t&lt;/sec_msg_node&gt;\n\t&lt;imgmsg_pd cdnmidimgurl_size="60450" cdnmidimgurl_pd_pri="50" cdnmidimgurl_pd="1" /&gt;\n\t&lt;silence&gt;1&lt;/silence&gt;\n\t&lt;membercount&gt;222&lt;/membercount&gt;\n\t&lt;NotAutoDownloadRange&gt;20:00-22:00;00:00-01:00&lt;/NotAutoDownloadRange&gt;\n\t&lt;signature&gt;N0_V1_VpmS2Kgp|v1_xxBolDsl&lt;/signature&gt;\n\t&lt;tmp_node&gt;\n\t\t&lt;publisher-id&gt;&lt;/publisher-id&gt;\n\t&lt;/tmp_node&gt;\n&lt;/msgsource&gt;\n</msgsource>\n\t\t\t<content>&lt;?xml version="1.0"?&gt;\n&lt;msg&gt;\n\t&lt;img aeskey="64706f757372756a6f74647173736f78" encryver="0" cdnthumbaeskey="64706f757372756a6f74647173736f78" cdnthumburl="3057020100044b30490201000204d6e1bab602033d14ba02048f06ff9d020468896c8d042437366439383062612d326338372d343761362d613865652d3762313337373565373862310204052828010201000405004c543e0054381738" cdnthumblength="3258" cdnthumbheight="100" cdnthumbwidth="66" cdnmidheight="0" cdnmidwidth="0" cdnhdheight="0" cdnhdwidth="0" cdnmidimgurl="3057020100044b30490201000204d6e1bab602033d14ba02048f06ff9d020468896c8d042437366439383062612d326338372d343761362d613865652d3762313337373565373862310204052828010201000405004c543e0054381738" length="60450" cdnbigimgurl="3057020100044b30490201000204d6e1bab602033d14ba02048f06ff9d020468896c8d042437366439383062612d326338372d343761362d613865652d3762313337373565373862310204052828010201000405004c543e0054381738" hdlength="196225" md5="4f2ea21d2d7ef077f433b2504011b6df"&gt;\n\t\t&lt;secHashInfoBase64 /&gt;\n\t\t&lt;live&gt;\n\t\t\t&lt;duration&gt;0&lt;/duration&gt;\n\t\t\t&lt;size&gt;0&lt;/size&gt;\n\t\t\t&lt;md5 /&gt;\n\t\t\t&lt;fileid /&gt;\n\t\t\t&lt;hdsize&gt;0&lt;/hdsize&gt;\n\t\t\t&lt;hdmd5 /&gt;\n\t\t\t&lt;hdfileid /&gt;\n\t\t\t&lt;stillimagetimems&gt;0&lt;/stillimagetimems&gt;\n\t\t&lt;/live&gt;\n\t&lt;/img&gt;\n\t&lt;platform_signature /&gt;\n\t&lt;imgdatahash /&gt;\n\t&lt;ImgSourceInfo&gt;\n\t\t&lt;ImgSourceUrl /&gt;\n\t\t&lt;BizType&gt;0&lt;/BizType&gt;\n\t&lt;/ImgSourceInfo&gt;\n&lt;/msg&gt;\n</content>\n\t\t\t<strid />\n\t\t\t<createtime>1753836684</createtime>\n\t\t</refermsg>\n\t\t<appattach>\n\t\t\t<totallen>0</totallen>\n\t\t\t<attachid />\n\t\t\t<cdnattachurl />\n\t\t\t<emoticonmd5 />\n\t\t\t<aeskey />\n\t\t\t<fileext />\n\t\t\t<islargefilemsg>0</islargefilemsg>\n\t\t</appattach>\n\t\t<extinfo />\n\t\t<androidsource>0</androidsource>\n\t\t<thumburl />\n\t\t<mediatagname />\n\t\t<messageaction><![CDATA[]]></messageaction>\n\t\t<messageext><![CDATA[]]></messageext>\n\t\t<emoticongift>\n\t\t\t<packageflag>0</packageflag>\n\t\t\t<packageid />\n\t\t</emoticongift>\n\t\t<emoticonshared>\n\t\t\t<packageflag>0</packageflag>\n\t\t\t<packageid />\n\t\t</emoticonshared>\n\t\t<designershared>\n\t\t\t<designeruin>0</designeruin>\n\t\t\t<designername>null</designername>\n\t\t\t<designerrediretcturl><![CDATA[null]]></designerrediretcturl>\n\t\t</designershared>\n\t\t<emotionpageshared>\n\t\t\t<tid>0</tid>\n\t\t\t<title>null</title>\n\t\t\t<desc>null</desc>\n\t\t\t<iconUrl><![CDATA[null]]></iconUrl>\n\t\t\t<secondUrl>null</secondUrl>\n\t\t\t<pageType>0</pageType>\n\t\t\t<setKey>null</setKey>\n\t\t</emotionpageshared>\n\t\t<webviewshared>\n\t\t\t<shareUrlOriginal />\n\t\t\t<shareUrlOpen />\n\t\t\t<jsAppId />\n\t\t\t<publisherId />\n\t\t\t<publisherReqId />\n\t\t</webviewshared>\n\t\t<template_id />\n\t\t<md5 />\n\t\t<websearch>\n\t\t\t<rec_category>0</rec_category>\n\t\t\t<channelId>0</channelId>\n\t\t</websearch>\n\t\t<weappinfo>\n\t\t\t<username />\n\t\t\t<appid />\n\t\t\t<appservicetype>0</appservicetype>\n\t\t\t<secflagforsinglepagemode>0</secflagforsinglepagemode>\n\t\t\t<videopageinfo>\n\t\t\t\t<thumbwidth>0</thumbwidth>\n\t\t\t\t<thumbheight>0</thumbheight>\n\t\t\t\t<fromopensdk>0</fromopensdk>\n\t\t\t</videopageinfo>\n\t\t</weappinfo>\n\t\t<statextstr />\n\t\t<musicShareItem>\n\t\t\t<musicDuration>0</musicDuration>\n\t\t</musicShareItem>\n\t\t<finderLiveProductShare>\n\t\t\t<finderLiveID><![CDATA[]]></finderLiveID>\n\t\t\t<finderUsername><![CDATA[]]></finderUsername>\n\t\t\t<finderObjectID><![CDATA[]]></finderObjectID>\n\t\t\t<finderNonceID><![CDATA[]]></finderNonceID>\n\t\t\t<liveStatus><![CDATA[]]></liveStatus>\n\t\t\t<appId><![CDATA[]]></appId>\n\t\t\t<pagePath><![CDATA[]]></pagePath>\n\t\t\t<productId><![CDATA[]]></productId>\n\t\t\t<coverUrl><![CDATA[]]></coverUrl>\n\t\t\t<productTitle><![CDATA[]]></productTitle>\n\t\t\t<marketPrice><![CDATA[0]]></marketPrice>\n\t\t\t<sellingPrice><![CDATA[0]]></sellingPrice>\n\t\t\t<platformHeadImg><![CDATA[]]></platformHeadImg>\n\t\t\t<platformName><![CDATA[]]></platformName>\n\t\t\t<shopWindowId><![CDATA[]]></shopWindowId>\n\t\t\t<flashSalePrice><![CDATA[0]]></flashSalePrice>\n\t\t\t<flashSaleEndTime><![CDATA[0]]></flashSaleEndTime>\n\t\t\t<ecSource><![CDATA[]]></ecSource>\n\t\t\t<sellingPriceWording><![CDATA[]]></sellingPriceWording>\n\t\t\t<platformIconURL><![CDATA[]]></platformIconURL>\n\t\t\t<firstProductTagURL><![CDATA[]]></firstProductTagURL>\n\t\t\t<firstProductTagAspectRatioString><![CDATA[0.0]]></firstProductTagAspectRatioString>\n\t\t\t<secondProductTagURL><![CDATA[]]></secondProductTagURL>\n\t\t\t<secondProductTagAspectRatioString><![CDATA[0.0]]></secondProductTagAspectRatioString>\n\t\t\t<firstGuaranteeWording><![CDATA[]]></firstGuaranteeWording>\n\t\t\t<secondGuaranteeWording><![CDATA[]]></secondGuaranteeWording>\n\t\t\t<thirdGuaranteeWording><![CDATA[]]></thirdGuaranteeWording>\n\t\t\t<isPriceBeginShow>false</isPriceBeginShow>\n\t\t\t<lastGMsgID><![CDATA[]]></lastGMsgID>\n\t\t\t<promoterKey><![CDATA[]]></promoterKey>\n\t\t\t<discountWording><![CDATA[]]></discountWording>\n\t\t\t<priceSuffixDescription><![CDATA[]]></priceSuffixDescription>\n\t\t\t<productCardKey><![CDATA[]]></productCardKey>\n\t\t\t<isWxShop><![CDATA[]]></isWxShop>\n\t\t\t<brandIconUrl><![CDATA[]]></brandIconUrl>\n\t\t\t<showBoxItemStringList />\n\t\t</finderLiveProductShare>\n\t\t<finderOrder>\n\t\t\t<appID><![CDATA[]]></appID>\n\t\t\t<orderID><![CDATA[]]></orderID>\n\t\t\t<path><![CDATA[]]></path>\n\t\t\t<priceWording><![CDATA[]]></priceWording>\n\t\t\t<stateWording><![CDATA[]]></stateWording>\n\t\t\t<productImageURL><![CDATA[]]></productImageURL>\n\t\t\t<products><![CDATA[]]></products>\n\t\t\t<productsCount><![CDATA[0]]></productsCount>\n\t\t\t<orderType><![CDATA[0]]></orderType>\n\t\t\t<newPriceWording><![CDATA[]]></newPriceWording>\n\t\t\t<newStateWording><![CDATA[]]></newStateWording>\n\t\t\t<useNewWording><![CDATA[0]]></useNewWording>\n\t\t</finderOrder>\n\t\t<finderShopWindowShare>\n\t\t\t<finderUsername><![CDATA[]]></finderUsername>\n\t\t\t<avatar><![CDATA[]]></avatar>\n\t\t\t<nickname><![CDATA[]]></nickname>\n\t\t\t<commodityInStockCount><![CDATA[]]></commodityInStockCount>\n\t\t\t<appId><![CDATA[]]></appId>\n\t\t\t<path><![CDATA[]]></path>\n\t\t\t<appUsername><![CDATA[]]></appUsername>\n\t\t\t<query><![CDATA[]]></query>\n\t\t\t<liteAppId><![CDATA[]]></liteAppId>\n\t\t\t<liteAppPath><![CDATA[]]></liteAppPath>\n\t\t\t<liteAppQuery><![CDATA[]]></liteAppQuery>\n\t\t\t<platformTagURL><![CDATA[]]></platformTagURL>\n\t\t\t<saleWording><![CDATA[]]></saleWording>\n\t\t\t<lastGMsgID><![CDATA[]]></lastGMsgID>\n\t\t\t<profileTypeWording><![CDATA[]]></profileTypeWording>\n\t\t\t<saleWordingExtra><![CDATA[]]></saleWordingExtra>\n\t\t\t<isWxShop><![CDATA[]]></isWxShop>\n\t\t\t<platformIconUrl><![CDATA[]]></platformIconUrl>\n\t\t\t<brandIconUrl><![CDATA[]]></brandIconUrl>\n\t\t\t<description><![CDATA[]]></description>\n\t\t\t<backgroundUrl><![CDATA[]]></backgroundUrl>\n\t\t\t<darkModePlatformIconUrl><![CDATA[]]></darkModePlatformIconUrl>\n\t\t\t<reputationInfo>\n\t\t\t\t<hasReputationInfo>0</hasReputationInfo>\n\t\t\t\t<reputationScore>0</reputationScore>\n\t\t\t\t<reputationWording />\n\t\t\t\t<reputationTextColor />\n\t\t\t\t<reputationLevelWording />\n\t\t\t\t<reputationBackgroundColor />\n\t\t\t</reputationInfo>\n\t\t\t<productImageURLList />\n\t\t</finderShopWindowShare>\n\t\t<findernamecard>\n\t\t\t<username />\n\t\t\t<avatar><![CDATA[]]></avatar>\n\t\t\t<nickname />\n\t\t\t<auth_job />\n\t\t\t<auth_icon>0</auth_icon>\n\t\t\t<auth_icon_url />\n\t\t\t<ecSource><![CDATA[]]></ecSource>\n\t\t\t<lastGMsgID><![CDATA[]]></lastGMsgID>\n\t\t</findernamecard>\n\t\t<finderGuarantee>\n\t\t\t<scene><![CDATA[0]]></scene>\n\t\t</finderGuarantee>\n\t\t<directshare>0</directshare>\n\t\t<gamecenter>\n\t\t\t<namecard>\n\t\t\t\t<iconUrl />\n\t\t\t\t<name />\n\t\t\t\t<desc />\n\t\t\t\t<tail />\n\t\t\t\t<jumpUrl />\n\t\t\t</namecard>\n\t\t</gamecenter>\n\t\t<patMsg>\n\t\t\t<chatUser />\n\t\t\t<records>\n\t\t\t\t<recordNum>0</recordNum>\n\t\t\t</records>\n\t\t</patMsg>\n\t\t<secretmsg>\n\t\t\t<issecretmsg>0</issecretmsg>\n\t\t</secretmsg>\n\t\t<referfromscene>0</referfromscene>\n\t\t<gameshare>\n\t\t\t<liteappext>\n\t\t\t\t<liteappbizdata />\n\t\t\t\t<priority>0</priority>\n\t\t\t</liteappext>\n\t\t\t<appbrandext>\n\t\t\t\t<litegameinfo />\n\t\t\t\t<priority>-1</priority>\n\t\t\t</appbrandext>\n\t\t\t<gameshareid />\n\t\t\t<sharedata />\n\t\t\t<isvideo>0</isvideo>\n\t\t\t<duration>-1</duration>\n\t\t\t<isexposed>0</isexposed>\n\t\t\t<readtext />\n\t\t</gameshare>\n\t\t<mpsharetrace>\n\t\t\t<hasfinderelement>0</hasfinderelement>\n\t\t\t<lastgmsgid />\n\t\t</mpsharetrace>\n\t\t<wxgamecard>\n\t\t\t<framesetname />\n\t\t\t<mbcarddata />\n\t\t\t<minpkgversion />\n\t\t\t<clientextinfo />\n\t\t\t<mbcardheight>0</mbcardheight>\n\t\t\t<isoldversion>0</isoldversion>\n\t\t</wxgamecard>\n\t\t<liteapp>\n\t\t\t<id>null</id>\n\t\t\t<path />\n\t\t\t<query />\n\t\t\t<istransparent>0</istransparent>\n\t\t\t<hideicon>0</hideicon>\n\t\t</liteapp>\n\t\t<opensdk_share_is_modified>0</opensdk_share_is_modified>\n\t</appmsg>\n\t<fromusername>wxid_ubbh6q832tcs21</fromusername>\n\t<scene>0</scene>\n\t<appinfo>\n\t\t<version>1</version>\n\t\t<appname></appname>\n\t</appinfo>\n\t<commenturl></commenturl>\n</msg>\n', 'Msgid': '714496648952914170', 'NewMsgId': '714496648952914170', 'ToWxid': 'wxid_4usgcju5ey9q29', 'FromWxid': '47325400669@chatroom', 'Nickname': '阿猪米德', 'MsgSource': '<msgsource>\n\t<sec_msg_node>\n\t\t<uuid>a0edf93ac0f5bba3630cc1b4ccfca261_</uuid>\n\t\t<risk-file-flag />\n\t\t<risk-file-md5-list />\n\t</sec_msg_node>\n\t<imgmsg_pd cdnmidimgurl_size="60450" cdnmidimgurl_pd_pri="50" cdnmidimgurl_pd="1" />\n\t<silence>1</silence>\n\t<membercount>222</membercount>\n\t<NotAutoDownloadRange>20:00-22:00;00:00-01:00</NotAutoDownloadRange>\n\t<signature>N0_V1_VpmS2Kgp|v1_xxBolDsl</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'Createtime': '1753836684', 'SenderWxid': 'wxid_ubbh6q832tcs21'}
2025-07-30 09:20:23 | INFO |   - 引用消息ID: 
2025-07-30 09:20:23 | INFO |   - 引用消息类型: 
2025-07-30 09:20:23 | INFO |   - 引用消息内容: <?xml version="1.0"?>
<msg>
	<appmsg appid="" sdkver="0">
		<title>美图 换成泳装</title>
		<des />
		<username />
		<action>view</action>
		<type>57</type>
		<showtype>0</showtype>
		<content />
		<url />
		<lowurl />
		<forwardflag>0</forwardflag>
		<dataurl />
		<lowdataurl />
		<contentattr>0</contentattr>
		<streamvideo>
			<streamvideourl />
			<streamvideototaltime>0</streamvideototaltime>
			<streamvideotitle />
			<streamvideowording />
			<streamvideoweburl />
			<streamvideothumburl />
			<streamvideoaduxinfo />
			<streamvideopublishid />
		</streamvideo>
		<canvasPageItem>
			<canvasPageXml><![CDATA[]]></canvasPageXml>
		</canvasPageItem>
		<refermsg>
			<type>3</type>
			<svrid>714496648952914170</svrid>
			<fromusr>47325400669@chatroom</fromusr>
			<chatusr>wxid_fh84okl6f5wp22</chatusr>
			<displayname>阿猪米德</displayname>
			<msgsource>&lt;msgsource&gt;
	&lt;sec_msg_node&gt;
		&lt;uuid&gt;a0edf93ac0f5bba3630cc1b4ccfca261_&lt;/uuid&gt;
		&lt;risk-file-flag /&gt;
		&lt;risk-file-md5-list /&gt;
	&lt;/sec_msg_node&gt;
	&lt;imgmsg_pd cdnmidimgurl_size="60450" cdnmidimgurl_pd_pri="50" cdnmidimgurl_pd="1" /&gt;
	&lt;silence&gt;1&lt;/silence&gt;
	&lt;membercount&gt;222&lt;/membercount&gt;
	&lt;NotAutoDownloadRange&gt;20:00-22:00;00:00-01:00&lt;/NotAutoDownloadRange&gt;
	&lt;signature&gt;N0_V1_VpmS2Kgp|v1_xxBolDsl&lt;/signature&gt;
	&lt;tmp_node&gt;
		&lt;publisher-id&gt;&lt;/publisher-id&gt;
	&lt;/tmp_node&gt;
&lt;/msgsource&gt;
</msgsource>
			<content>&lt;?xml version="1.0"?&gt;
&lt;msg&gt;
	&lt;img aeskey="64706f757372756a6f74647173736f78" encryver="0" cdnthumbaeskey="64706f757372756a6f74647173736f78" cdnthumburl="3057020100044b30490201000204d6e1bab602033d14ba02048f06ff9d020468896c8d042437366439383062612d326338372d343761362d613865652d3762313337373565373862310204052828010201000405004c543e0054381738" cdnthumblength="3258" cdnthumbheight="100" cdnthumbwidth="66" cdnmidheight="0" cdnmidwidth="0" cdnhdheight="0" cdnhdwidth="0" cdnmidimgurl="3057020100044b30490201000204d6e1bab602033d14ba02048f06ff9d020468896c8d042437366439383062612d326338372d343761362d613865652d3762313337373565373862310204052828010201000405004c543e0054381738" length="60450" cdnbigimgurl="3057020100044b30490201000204d6e1bab602033d14ba02048f06ff9d020468896c8d042437366439383062612d326338372d343761362d613865652d3762313337373565373862310204052828010201000405004c543e0054381738" hdlength="196225" md5="4f2ea21d2d7ef077f433b2504011b6df"&gt;
		&lt;secHashInfoBase64 /&gt;
		&lt;live&gt;
			&lt;duration&gt;0&lt;/duration&gt;
			&lt;size&gt;0&lt;/size&gt;
			&lt;md5 /&gt;
			&lt;fileid /&gt;
			&lt;hdsize&gt;0&lt;/hdsize&gt;
			&lt;hdmd5 /&gt;
			&lt;hdfileid /&gt;
			&lt;stillimagetimems&gt;0&lt;/stillimagetimems&gt;
		&lt;/live&gt;
	&lt;/img&gt;
	&lt;platform_signature /&gt;
	&lt;imgdatahash /&gt;
	&lt;ImgSourceInfo&gt;
		&lt;ImgSourceUrl /&gt;
		&lt;BizType&gt;0&lt;/BizType&gt;
	&lt;/ImgSourceInfo&gt;
&lt;/msg&gt;
</content>
			<strid />
			<createtime>1753836684</createtime>
		</refermsg>
		<appattach>
			<totallen>0</totallen>
			<attachid />
			<cdnattachurl />
			<emoticonmd5 />
			<aeskey />
			<fileext />
			<islargefilemsg>0</islargefilemsg>
		</appattach>
		<extinfo />
		<androidsource>0</androidsource>
		<thumburl />
		<mediatagname />
		<messageaction><![CDATA[]]></messageaction>
		<messageext><![CDATA[]]></messageext>
		<emoticongift>
			<packageflag>0</packageflag>
			<packageid />
		</emoticongift>
		<emoticonshared>
			<packageflag>0</packageflag>
			<packageid />
		</emoticonshared>
		<designershared>
			<designeruin>0</designeruin>
			<designername>null</designername>
			<designerrediretcturl><![CDATA[null]]></designerrediretcturl>
		</designershared>
		<emotionpageshared>
			<tid>0</tid>
			<title>null</title>
			<desc>null</desc>
			<iconUrl><![CDATA[null]]></iconUrl>
			<secondUrl>null</secondUrl>
			<pageType>0</pageType>
			<setKey>null</setKey>
		</emotionpageshared>
		<webviewshared>
			<shareUrlOriginal />
			<shareUrlOpen />
			<jsAppId />
			<publisherId />
			<publisherReqId />
		</webviewshared>
		<template_id />
		<md5 />
		<websearch>
			<rec_category>0</rec_category>
			<channelId>0</channelId>
		</websearch>
		<weappinfo>
			<username />
			<appid />
			<appservicetype>0</appservicetype>
			<secflagforsinglepagemode>0</secflagforsinglepagemode>
			<videopageinfo>
				<thumbwidth>0</thumbwidth>
				<thumbheight>0</thumbheight>
				<fromopensdk>0</fromopensdk>
			</videopageinfo>
		</weappinfo>
		<statextstr />
		<musicShareItem>
			<musicDuration>0</musicDuration>
		</musicShareItem>
		<finderLiveProductShare>
			<finderLiveID><![CDATA[]]></finderLiveID>
			<finderUsername><![CDATA[]]></finderUsername>
			<finderObjectID><![CDATA[]]></finderObjectID>
			<finderNonceID><![CDATA[]]></finderNonceID>
			<liveStatus><![CDATA[]]></liveStatus>
			<appId><![CDATA[]]></appId>
			<pagePath><![CDATA[]]></pagePath>
			<productId><![CDATA[]]></productId>
			<coverUrl><![CDATA[]]></coverUrl>
			<productTitle><![CDATA[]]></productTitle>
			<marketPrice><![CDATA[0]]></marketPrice>
			<sellingPrice><![CDATA[0]]></sellingPrice>
			<platformHeadImg><![CDATA[]]></platformHeadImg>
			<platformName><![CDATA[]]></platformName>
			<shopWindowId><![CDATA[]]></shopWindowId>
			<flashSalePrice><![CDATA[0]]></flashSalePrice>
			<flashSaleEndTime><![CDATA[0]]></flashSaleEndTime>
			<ecSource><![CDATA[]]></ecSource>
			<sellingPriceWording><![CDATA[]]></sellingPriceWording>
			<platformIconURL><![CDATA[]]></platformIconURL>
			<firstProductTagURL><![CDATA[]]></firstProductTagURL>
			<firstProductTagAspectRatioString><![CDATA[0.0]]></firstProductTagAspectRatioString>
			<secondProductTagURL><![CDATA[]]></secondProductTagURL>
			<secondProductTagAspectRatioString><![CDATA[0.0]]></secondProductTagAspectRatioString>
			<firstGuaranteeWording><![CDATA[]]></firstGuaranteeWording>
			<secondGuaranteeWording><![CDATA[]]></secondGuaranteeWording>
			<thirdGuaranteeWording><![CDATA[]]></thirdGuaranteeWording>
			<isPriceBeginShow>false</isPriceBeginShow>
			<lastGMsgID><![CDATA[]]></lastGMsgID>
			<promoterKey><![CDATA[]]></promoterKey>
			<discountWording><![CDATA[]]></discountWording>
			<priceSuffixDescription><![CDATA[]]></priceSuffixDescription>
			<productCardKey><![CDATA[]]></productCardKey>
			<isWxShop><![CDATA[]]></isWxShop>
			<brandIconUrl><![CDATA[]]></brandIconUrl>
			<showBoxItemStringList />
		</finderLiveProductShare>
		<finderOrder>
			<appID><![CDATA[]]></appID>
			<orderID><![CDATA[]]></orderID>
			<path><![CDATA[]]></path>
			<priceWording><![CDATA[]]></priceWording>
			<stateWording><![CDATA[]]></stateWording>
			<productImageURL><![CDATA[]]></productImageURL>
			<products><![CDATA[]]></products>
			<productsCount><![CDATA[0]]></productsCount>
			<orderType><![CDATA[0]]></orderType>
			<newPriceWording><![CDATA[]]></newPriceWording>
			<newStateWording><![CDATA[]]></newStateWording>
			<useNewWording><![CDATA[0]]></useNewWording>
		</finderOrder>
		<finderShopWindowShare>
			<finderUsername><![CDATA[]]></finderUsername>
			<avatar><![CDATA[]]></avatar>
			<nickname><![CDATA[]]></nickname>
			<commodityInStockCount><![CDATA[]]></commodityInStockCount>
			<appId><![CDATA[]]></appId>
			<path><![CDATA[]]></path>
			<appUsername><![CDATA[]]></appUsername>
			<query><![CDATA[]]></query>
			<liteAppId><![CDATA[]]></liteAppId>
			<liteAppPath><![CDATA[]]></liteAppPath>
			<liteAppQuery><![CDATA[]]></liteAppQuery>
			<platformTagURL><![CDATA[]]></platformTagURL>
			<saleWording><![CDATA[]]></saleWording>
			<lastGMsgID><![CDATA[]]></lastGMsgID>
			<profileTypeWording><![CDATA[]]></profileTypeWording>
			<saleWordingExtra><![CDATA[]]></saleWordingExtra>
			<isWxShop><![CDATA[]]></isWxShop>
			<platformIconUrl><![CDATA[]]></platformIconUrl>
			<brandIconUrl><![CDATA[]]></brandIconUrl>
			<description><![CDATA[]]></description>
			<backgroundUrl><![CDATA[]]></backgroundUrl>
			<darkModePlatformIconUrl><![CDATA[]]></darkModePlatformIconUrl>
			<reputationInfo>
				<hasReputationInfo>0</hasReputationInfo>
				<reputationScore>0</reputationScore>
				<reputationWording />
				<reputationTextColor />
				<reputationLevelWording />
				<reputationBackgroundColor />
			</reputationInfo>
			<productImageURLList />
		</finderShopWindowShare>
		<findernamecard>
			<username />
			<avatar><![CDATA[]]></avatar>
			<nickname />
			<auth_job />
			<auth_icon>0</auth_icon>
			<auth_icon_url />
			<ecSource><![CDATA[]]></ecSource>
			<lastGMsgID><![CDATA[]]></lastGMsgID>
		</findernamecard>
		<finderGuarantee>
			<scene><![CDATA[0]]></scene>
		</finderGuarantee>
		<directshare>0</directshare>
		<gamecenter>
			<namecard>
				<iconUrl />
				<name />
				<desc />
				<tail />
				<jumpUrl />
			</namecard>
		</gamecenter>
		<patMsg>
			<chatUser />
			<records>
				<recordNum>0</recordNum>
			</records>
		</patMsg>
		<secretmsg>
			<issecretmsg>0</issecretmsg>
		</secretmsg>
		<referfromscene>0</referfromscene>
		<gameshare>
			<liteappext>
				<liteappbizdata />
				<priority>0</priority>
			</liteappext>
			<appbrandext>
				<litegameinfo />
				<priority>-1</priority>
			</appbrandext>
			<gameshareid />
			<sharedata />
			<isvideo>0</isvideo>
			<duration>-1</duration>
			<isexposed>0</isexposed>
			<readtext />
		</gameshare>
		<mpsharetrace>
			<hasfinderelement>0</hasfinderelement>
			<lastgmsgid />
		</mpsharetrace>
		<wxgamecard>
			<framesetname />
			<mbcarddata />
			<minpkgversion />
			<clientextinfo />
			<mbcardheight>0</mbcardheight>
			<isoldversion>0</isoldversion>
		</wxgamecard>
		<liteapp>
			<id>null</id>
			<path />
			<query />
			<istransparent>0</istransparent>
			<hideicon>0</hideicon>
		</liteapp>
		<opensdk_share_is_modified>0</opensdk_share_is_modified>
	</appmsg>
	<fromusername>wxid_ubbh6q832tcs21</fromusername>
	<scene>0</scene>
	<appinfo>
		<version>1</version>
		<appname></appname>
	</appinfo>
	<commenturl></commenturl>
</msg>

2025-07-30 09:20:23 | INFO |   - 引用消息发送人: wxid_ubbh6q832tcs21
2025-07-30 09:20:23 | INFO | [RoboNeo] 开始处理图像生成: 用户=wxid_ubbh6q832tcs21, 提示词=换成泳装, 图片=temp\roboneo\quoted_image_1753838423.jpg
2025-07-30 09:20:24 | ERROR | [RoboNeo] 上传异常: [Errno 11001] getaddrinfo failed
2025-07-30 09:20:25 | INFO | 发送文字消息: 对方wxid:47325400669@chatroom at:['wxid_ubbh6q832tcs21'] 内容:@郭 ❌ 图片上传失败
2025-07-30 09:20:27 | DEBUG | 收到消息: {'MsgId': 1659735643, 'FromUserName': {'string': '48097389945@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 1, 'Content': {'string': 'wxid_jegyk4i3v7zg22:\n我连雅迪都没有'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1753838439, 'MsgSource': '<msgsource>\n\t<bizflag>0</bizflag>\n\t<pua>1</pua>\n\t<eggIncluded>1</eggIncluded>\n\t<silence>0</silence>\n\t<membercount>73</membercount>\n\t<signature>N0_V1_qvGN0AQV|v1_ArilAWpO</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'PushContent': '阿尼亚与她 : 我连雅迪都没有', 'NewMsgId': 1130106038827534505, 'MsgSeq': 871410918}
2025-07-30 09:20:27 | INFO | 收到文本消息: 消息ID:1659735643 来自:48097389945@chatroom 发送人:wxid_jegyk4i3v7zg22 @:[] 内容:我连雅迪都没有
2025-07-30 09:20:28 | DEBUG | 处理消息内容: '我连雅迪都没有'
2025-07-30 09:20:28 | DEBUG | 消息内容 '我连雅迪都没有' 不匹配任何命令，忽略
2025-07-30 09:20:30 | DEBUG | 收到消息: {'MsgId': 1991446988, 'FromUserName': {'string': '47325400669@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 47, 'Content': {'string': 'wxid_ubbh6q832tcs21:\n<msg><emoji fromusername = "wxid_ubbh6q832tcs21" tousername = "47325400669@chatroom" type="2" idbuffer="media:0_0" md5="3c5d9173b23ecc025e1ec8c858142b57" len = "1005541" productid="" androidmd5="3c5d9173b23ecc025e1ec8c858142b57" androidlen="1005541" s60v3md5 = "3c5d9173b23ecc025e1ec8c858142b57" s60v3len="1005541" s60v5md5 = "3c5d9173b23ecc025e1ec8c858142b57" s60v5len="1005541" cdnurl = "http://vweixinf.tc.qq.com/110/20401/stodownload?m=3c5d9173b23ecc025e1ec8c858142b57&amp;filekey=30440201010430302e02016e040253480420336335643931373362323365636330323565316563386338353831343262353702030f57e5040d00000004627466730000000132&amp;hy=SH&amp;storeid=2683a773b00015f7c687175f00000006e01004fb153480fd65b01e02592370&amp;ef=1&amp;bizid=1022" designerid = "" thumburl = "" encrypturl = "http://vweixinf.tc.qq.com/110/20402/stodownload?m=4bab97cabbe1706f0a603389b3529eeb&amp;filekey=30440201010430302e02016e040253480420346261623937636162626531373036663061363033333839623335323965656202030f57f0040d00000004627466730000000132&amp;hy=SH&amp;storeid=2683a773b0003cd1f687175f00000006e02004fb253480fd65b01e02592390&amp;ef=2&amp;bizid=1022" aeskey= "0d82b033625c4bf18bc5c338aeb7333e" externurl = "http://vweixinf.tc.qq.com/110/20403/stodownload?m=0064acb5c83b81eef262bb4c27a223c8&amp;filekey=30440201010430302e02016e04025348042030303634616362356338336238316565663236326262346332376132323363380203020430040d00000004627466730000000132&amp;hy=SH&amp;storeid=2683a773b0005d930687175f00000006e03004fb353480fd65b01e025923ad&amp;ef=3&amp;bizid=1022" externmd5 = "ef10f433c759c4681e3dcaecf0b0a32d" width= "640" height= "640" tpurl= "" tpauthkey= "" attachedtext= "" attachedtextcolor= "" lensid= "" emojiattr= "" linkid= "" desc= "" ></emoji>  </msg>'}, 'Status': 3, 'ImgStatus': 2, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1753838441, 'MsgSource': '<msgsource>\n\t<silence>0</silence>\n\t<membercount>222</membercount>\n\t<signature>N0_V1_94+dhY5x|v1_pnHuAFIf</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'PushContent': '郭在群聊中发了一个表情', 'NewMsgId': 2054690162828733756, 'MsgSeq': 871410919}
2025-07-30 09:20:30 | INFO | 收到表情消息: 消息ID:1991446988 来自:47325400669@chatroom 发送人:wxid_ubbh6q832tcs21 MD5:3c5d9173b23ecc025e1ec8c858142b57 大小:1005541
2025-07-30 09:20:30 | DEBUG | [DoubaoImageRecognition] 缓存表情消息: 2054690162828733756
2025-07-30 09:20:41 | DEBUG | 收到消息: {'MsgId': 962125528, 'FromUserName': {'string': '48097389945@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 1, 'Content': {'string': 'wxid_w0rik7wwsvcy22:\n一样'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1753838453, 'MsgSource': '<msgsource>\n\t<bizflag>0</bizflag>\n\t<pua>1</pua>\n\t<eggIncluded>1</eggIncluded>\n\t<silence>0</silence>\n\t<membercount>73</membercount>\n\t<signature>N0_V1_rGgAoZE+|v1_GVc/a1aO</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'PushContent': '一罐小可乐 : 一样', 'NewMsgId': 5864427374309629430, 'MsgSeq': 871410920}
2025-07-30 09:20:41 | INFO | 收到文本消息: 消息ID:962125528 来自:48097389945@chatroom 发送人:wxid_w0rik7wwsvcy22 @:[] 内容:一样
2025-07-30 09:20:41 | DEBUG | 处理消息内容: '一样'
2025-07-30 09:20:41 | DEBUG | 消息内容 '一样' 不匹配任何命令，忽略
2025-07-30 09:20:46 | DEBUG | 收到消息: {'MsgId': 1392988974, 'FromUserName': {'string': '48097389945@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 47, 'Content': {'string': 'wxid_srknxij3jka022:\n<msg><emoji fromusername = "wxid_srknxij3jka022" tousername = "48097389945@chatroom" type="1" idbuffer="media:0_0" md5="6d53e79a7c05b3b648289d33cd986e01" len = "8669" productid="" androidmd5="6d53e79a7c05b3b648289d33cd986e01" androidlen="8669" s60v3md5 = "6d53e79a7c05b3b648289d33cd986e01" s60v3len="8669" s60v5md5 = "6d53e79a7c05b3b648289d33cd986e01" s60v5len="8669" cdnurl = "http://vweixinf.tc.qq.com/110/20401/stodownload?m=6d53e79a7c05b3b648289d33cd986e01&amp;filekey=3043020101042f302d02016e0402534804203664353365373961376330356233623634383238396433336364393836653031020221dd040d00000004627466730000000132&amp;hy=SH&amp;storeid=268897379000514baf33b8a710000006e01004fb1534817807bd1e6a1551fc&amp;ef=1&amp;bizid=1022" designerid = "" thumburl = "" encrypturl = "http://vweixinf.tc.qq.com/110/20402/stodownload?m=15c19a246e3a6f7712388f71501e0d82&amp;filekey=3043020101042f302d02016e0402534804203135633139613234366533613666373731323338386637313530316530643832020221e0040d00000004627466730000000132&amp;hy=SH&amp;storeid=26889737900059a3bf33b8a710000006e02004fb2534817807bd1e6a155205&amp;ef=2&amp;bizid=1022" aeskey= "85a0b8ad6c6e43eb89dafb795d258076" externurl = "http://vweixinf.tc.qq.com/110/20403/stodownload?m=59e7505335f323cc24f563b83c8f08e2&amp;filekey=3043020101042f302d02016e040253480420353965373530353333356633323363633234663536336238336338663038653202021390040d00000004627466730000000132&amp;hy=SH&amp;storeid=26889737900069360f33b8a710000006e03004fb3534817807bd1e6a15521c&amp;ef=3&amp;bizid=1022" externmd5 = "d18e0ba8bdbba938e8ffc8aa27ed517a" width= "470" height= "180" tpurl= "" tpauthkey= "" attachedtext= "" attachedtextcolor= "" lensid= "" emojiattr= "" linkid= "" desc= "" ></emoji>  </msg>'}, 'Status': 3, 'ImgStatus': 2, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1753838457, 'MsgSource': '<msgsource>\n\t<silence>0</silence>\n\t<membercount>73</membercount>\n\t<signature>N0_V1_+BiMMBgT|v1_KZESAT0H</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'PushContent': '她在群聊中发了一个表情', 'NewMsgId': 607904219144767127, 'MsgSeq': 871410921}
2025-07-30 09:20:46 | INFO | 收到表情消息: 消息ID:1392988974 来自:48097389945@chatroom 发送人:wxid_srknxij3jka022 MD5:6d53e79a7c05b3b648289d33cd986e01 大小:8669
2025-07-30 09:20:46 | DEBUG | [DoubaoImageRecognition] 缓存表情消息: 607904219144767127
2025-07-30 09:20:49 | DEBUG | 收到消息: {'MsgId': 2097744065, 'FromUserName': {'string': '48097389945@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 1, 'Content': {'string': 'wxid_jegyk4i3v7zg22:\n雨水打湿我的脚，明年一定换雅迪'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1753838460, 'MsgSource': '<msgsource>\n\t<bizflag>0</bizflag>\n\t<pua>1</pua>\n\t<alnode>\n\t\t<inlenlist>18</inlenlist>\n\t</alnode>\n\t<eggIncluded>1</eggIncluded>\n\t<silence>0</silence>\n\t<membercount>73</membercount>\n\t<signature>N0_V1_qE7VyMbq|v1_re3edgzh</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'PushContent': '阿尼亚与她 : 雨水打湿我的脚，明年一定换雅迪', 'NewMsgId': 2975375763650394854, 'MsgSeq': 871410922}
2025-07-30 09:20:49 | INFO | 收到文本消息: 消息ID:2097744065 来自:48097389945@chatroom 发送人:wxid_jegyk4i3v7zg22 @:[] 内容:雨水打湿我的脚，明年一定换雅迪
2025-07-30 09:20:49 | DEBUG | 处理消息内容: '雨水打湿我的脚，明年一定换雅迪'
2025-07-30 09:20:49 | DEBUG | 消息内容 '雨水打湿我的脚，明年一定换雅迪' 不匹配任何命令，忽略
2025-07-30 09:20:59 | DEBUG | 收到消息: {'MsgId': 533748658, 'FromUserName': {'string': '48097389945@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 1, 'Content': {'string': 'wxid_jegyk4i3v7zg22:\n一样'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1753838471, 'MsgSource': '<msgsource>\n\t<bizflag>0</bizflag>\n\t<pua>1</pua>\n\t<eggIncluded>1</eggIncluded>\n\t<silence>0</silence>\n\t<membercount>73</membercount>\n\t<signature>N0_V1_1Lt/A9qj|v1_lzd+yy7q</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'PushContent': '阿尼亚与她 : 一样', 'NewMsgId': 7007760261844736027, 'MsgSeq': 871410923}
2025-07-30 09:20:59 | INFO | 收到文本消息: 消息ID:533748658 来自:48097389945@chatroom 发送人:wxid_jegyk4i3v7zg22 @:[] 内容:一样
2025-07-30 09:20:59 | DEBUG | 处理消息内容: '一样'
2025-07-30 09:20:59 | DEBUG | 消息内容 '一样' 不匹配任何命令，忽略
2025-07-30 09:21:01 | DEBUG | 收到消息: {'MsgId': 1810968725, 'FromUserName': {'string': '48097389945@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 1, 'Content': {'string': 'wxid_w0rik7wwsvcy22:\n一样'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1753838471, 'MsgSource': '<msgsource>\n\t<bizflag>0</bizflag>\n\t<pua>1</pua>\n\t<eggIncluded>1</eggIncluded>\n\t<silence>0</silence>\n\t<membercount>73</membercount>\n\t<signature>N0_V1_LU+8/U7O|v1_9V0MMvtz</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'PushContent': '一罐小可乐 : 一样', 'NewMsgId': 7358509091865602314, 'MsgSeq': 871410924}
2025-07-30 09:21:01 | INFO | 收到文本消息: 消息ID:1810968725 来自:48097389945@chatroom 发送人:wxid_w0rik7wwsvcy22 @:[] 内容:一样
2025-07-30 09:21:02 | DEBUG | 处理消息内容: '一样'
2025-07-30 09:21:02 | DEBUG | 消息内容 '一样' 不匹配任何命令，忽略
2025-07-30 09:21:07 | DEBUG | 收到消息: {'MsgId': 370318014, 'FromUserName': {'string': '48097389945@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 47, 'Content': {'string': 'wxid_srknxij3jka022:\n<msg><emoji fromusername = "wxid_srknxij3jka022" tousername = "48097389945@chatroom" type="1" idbuffer="media:0_0" md5="0e02b61e0c21568f9c7a92d095fba591" len = "10580" productid="" androidmd5="0e02b61e0c21568f9c7a92d095fba591" androidlen="10580" s60v3md5 = "0e02b61e0c21568f9c7a92d095fba591" s60v3len="10580" s60v5md5 = "0e02b61e0c21568f9c7a92d095fba591" s60v5len="10580" cdnurl = "http://vweixinf.tc.qq.com/110/20401/stodownload?m=0e02b61e0c21568f9c7a92d095fba591&amp;filekey=3043020101042f302d02016e040253480420306530326236316530633231353638663963376139326430393566626135393102022954040d00000004627466730000000132&amp;hy=SH&amp;storeid=26889738e00014b4df33b8a710000006e01004fb153482ec7d0d156a42feae&amp;ef=1&amp;bizid=1022" designerid = "" thumburl = "" encrypturl = "http://vweixinf.tc.qq.com/110/20402/stodownload?m=f60fa2a0894ee2693c372a70b5240bf3&amp;filekey=3043020101042f302d02016e040253480420663630666132613038393465653236393363333732613730623532343062663302022960040d00000004627466730000000132&amp;hy=SH&amp;storeid=26889738e00022525f33b8a710000006e02004fb253482ec7d0d156a42febb&amp;ef=2&amp;bizid=1022" aeskey= "b258a930ec4e48e89dbbfcaacbcea699" externurl = "http://vweixinf.tc.qq.com/110/20403/stodownload?m=0787b475bdf4de2fd6be805a3715599d&amp;filekey=3043020101042f302d02016e040253480420303738376234373562646634646532666436626538303561333731353539396402021860040d00000004627466730000000132&amp;hy=SH&amp;storeid=26889738e0002d670f33b8a710000006e03004fb353482ec7d0d156a42fec6&amp;ef=3&amp;bizid=1022" externmd5 = "8c1c04c29e42a11f19513b2a767dfa16" width= "470" height= "180" tpurl= "" tpauthkey= "" attachedtext= "" attachedtextcolor= "" lensid= "" emojiattr= "" linkid= "" desc= "" ></emoji>  </msg>'}, 'Status': 3, 'ImgStatus': 2, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1753838478, 'MsgSource': '<msgsource>\n\t<silence>0</silence>\n\t<membercount>73</membercount>\n\t<signature>N0_V1_XQNKoYFm|v1_SxLEkiJN</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'PushContent': '她在群聊中发了一个表情', 'NewMsgId': 1345399025832989119, 'MsgSeq': 871410925}
2025-07-30 09:21:07 | INFO | 收到表情消息: 消息ID:370318014 来自:48097389945@chatroom 发送人:wxid_srknxij3jka022 MD5:0e02b61e0c21568f9c7a92d095fba591 大小:10580
2025-07-30 09:21:07 | DEBUG | [DoubaoImageRecognition] 缓存表情消息: 1345399025832989119
2025-07-30 09:21:09 | DEBUG | 收到消息: {'MsgId': 61793592, 'FromUserName': {'string': '48097389945@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 47, 'Content': {'string': 'wxid_srknxij3jka022:\n<msg><emoji fromusername = "wxid_srknxij3jka022" tousername = "48097389945@chatroom" type="1" idbuffer="media:0_0" md5="928b6c7ab038c48b0a342747767ac825" len = "8663" productid="" androidmd5="928b6c7ab038c48b0a342747767ac825" androidlen="8663" s60v3md5 = "928b6c7ab038c48b0a342747767ac825" s60v3len="8663" s60v5md5 = "928b6c7ab038c48b0a342747767ac825" s60v5len="8663" cdnurl = "http://vweixinf.tc.qq.com/110/20401/stodownload?m=928b6c7ab038c48b0a342747767ac825&amp;filekey=3043020101042f302d02016e0402534804203932386236633761623033386334386230613334323734373736376163383235020221d7040d00000004627466730000000132&amp;hy=SH&amp;storeid=2688973900003e4a0f33b8a710000006e01004fb1534817882bc1e6a452763&amp;ef=1&amp;bizid=1022" designerid = "" thumburl = "" encrypturl = "http://vweixinf.tc.qq.com/110/20402/stodownload?m=de50fc951f646f031b1cede90e3b2b62&amp;filekey=3043020101042f302d02016e0402534804206465353066633935316636343666303331623163656465393065336232623632020221e0040d00000004627466730000000132&amp;hy=SH&amp;storeid=2688973900004ae41f33b8a710000006e02004fb2534817882bc1e6a452772&amp;ef=2&amp;bizid=1022" aeskey= "aa12f3f06bfe421dbc4586a49de8aaaf" externurl = "http://vweixinf.tc.qq.com/110/20403/stodownload?m=f92601f0d3a7bcc2fe6a60b06a571a38&amp;filekey=3043020101042f302d02016e040253480420663932363031663064336137626363326665366136306230366135373161333802021390040d00000004627466730000000132&amp;hy=SH&amp;storeid=26889739000058b47f33b8a710000006e03004fb3534817882bc1e6a452781&amp;ef=3&amp;bizid=1022" externmd5 = "a7d6c1ae70f58d8d7efefbf1e16f1099" width= "470" height= "180" tpurl= "" tpauthkey= "" attachedtext= "" attachedtextcolor= "" lensid= "" emojiattr= "" linkid= "" desc= "" ></emoji>  </msg>'}, 'Status': 3, 'ImgStatus': 2, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1753838480, 'MsgSource': '<msgsource>\n\t<silence>0</silence>\n\t<membercount>73</membercount>\n\t<signature>N0_V1_4EvPYP/e|v1_NAcDVItL</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'PushContent': '她在群聊中发了一个表情', 'NewMsgId': 4067685017132277407, 'MsgSeq': 871410926}
2025-07-30 09:21:09 | INFO | 收到表情消息: 消息ID:61793592 来自:48097389945@chatroom 发送人:wxid_srknxij3jka022 MD5:928b6c7ab038c48b0a342747767ac825 大小:8663
2025-07-30 09:21:09 | DEBUG | [DoubaoImageRecognition] 缓存表情消息: 4067685017132277407
2025-07-30 09:21:27 | DEBUG | 收到消息: {'MsgId': 1471353279, 'FromUserName': {'string': '48097389945@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 49, 'Content': {'string': 'xiaomaochong:\n<?xml version="1.0"?>\n<msg>\n\t<appmsg appid="" sdkver="0">\n\t\t<title>当前版本不支持展示该内容，请升级至最新版本。</title>\n\t\t<des />\n\t\t<username />\n\t\t<action>view</action>\n\t\t<type>51</type>\n\t\t<showtype>0</showtype>\n\t\t<content />\n\t\t<url>https://support.weixin.qq.com/security/readtemplate?t=w_security_center_website/upgrade</url>\n\t\t<lowurl />\n\t\t<forwardflag>0</forwardflag>\n\t\t<dataurl />\n\t\t<lowdataurl />\n\t\t<contentattr>0</contentattr>\n\t\t<streamvideo>\n\t\t\t<streamvideourl />\n\t\t\t<streamvideototaltime>0</streamvideototaltime>\n\t\t\t<streamvideotitle />\n\t\t\t<streamvideowording />\n\t\t\t<streamvideoweburl />\n\t\t\t<streamvideothumburl />\n\t\t\t<streamvideoaduxinfo />\n\t\t\t<streamvideopublishid />\n\t\t</streamvideo>\n\t\t<canvasPageItem>\n\t\t\t<canvasPageXml><![CDATA[]]></canvasPageXml>\n\t\t</canvasPageItem>\n\t\t<appattach>\n\t\t\t<totallen>0</totallen>\n\t\t\t<attachid />\n\t\t\t<cdnattachurl />\n\t\t\t<emoticonmd5></emoticonmd5>\n\t\t\t<aeskey></aeskey>\n\t\t\t<fileext />\n\t\t\t<islargefilemsg>0</islargefilemsg>\n\t\t</appattach>\n\t\t<extinfo />\n\t\t<androidsource>0</androidsource>\n\t\t<thumburl />\n\t\t<mediatagname />\n\t\t<messageaction><![CDATA[]]></messageaction>\n\t\t<messageext><![CDATA[]]></messageext>\n\t\t<emoticongift>\n\t\t\t<packageflag>0</packageflag>\n\t\t\t<packageid />\n\t\t</emoticongift>\n\t\t<emoticonshared>\n\t\t\t<packageflag>0</packageflag>\n\t\t\t<packageid />\n\t\t</emoticonshared>\n\t\t<designershared>\n\t\t\t<designeruin>0</designeruin>\n\t\t\t<designername>null</designername>\n\t\t\t<designerrediretcturl><![CDATA[null]]></designerrediretcturl>\n\t\t</designershared>\n\t\t<emotionpageshared>\n\t\t\t<tid>0</tid>\n\t\t\t<title>null</title>\n\t\t\t<desc>null</desc>\n\t\t\t<iconUrl><![CDATA[null]]></iconUrl>\n\t\t\t<secondUrl>null</secondUrl>\n\t\t\t<pageType>0</pageType>\n\t\t\t<setKey>null</setKey>\n\t\t</emotionpageshared>\n\t\t<webviewshared>\n\t\t\t<shareUrlOriginal />\n\t\t\t<shareUrlOpen />\n\t\t\t<jsAppId />\n\t\t\t<publisherId />\n\t\t\t<publisherReqId />\n\t\t</webviewshared>\n\t\t<template_id />\n\t\t<md5 />\n\t\t<websearch>\n\t\t\t<rec_category>0</rec_category>\n\t\t\t<channelId>0</channelId>\n\t\t</websearch>\n\t\t<weappinfo>\n\t\t\t<username />\n\t\t\t<appid />\n\t\t\t<appservicetype>0</appservicetype>\n\t\t\t<secflagforsinglepagemode>0</secflagforsinglepagemode>\n\t\t\t<videopageinfo>\n\t\t\t\t<thumbwidth>0</thumbwidth>\n\t\t\t\t<thumbheight>0</thumbheight>\n\t\t\t\t<fromopensdk>0</fromopensdk>\n\t\t\t</videopageinfo>\n\t\t</weappinfo>\n\t\t<statextstr />\n\t\t<musicShareItem>\n\t\t\t<musicDuration>0</musicDuration>\n\t\t</musicShareItem>\n\t\t<finderFeed>\n\t\t\t<objectId>14598705382467705052</objectId>\n\t\t\t<objectNonceId>17798329878216367954_0_25_2_3_1753838483387466_81764af8-6ce3-11f0-a565-a367457e48aa</objectNonceId>\n\t\t\t<feedType>4</feedType>\n\t\t\t<nickname>猫少驾到</nickname>\n\t\t\t<username>v2_060000231003b20faec8c7e4811fc6d6c700ed37b077c3381147ac15c36684dd7b8d9a736b4c@finder</username>\n\t\t\t<avatar><![CDATA[https://wx.qlogo.cn/finderhead/ver_1/UY5JdCjPZx3DGRxjKeefYd7FlrUia4L9nKQsn4KUP3SvRibyDUiaQAVib2VwM23U30pJn6sKr8RtNE0KEXbX6s2yiaP9Z9EPehHsUhmc79y5jQDl0pxYepdcq0xPiaMRib2XFtS/0]]></avatar>\n\t\t\t<desc>那很有生活了#成功女人#抽象#猎奇</desc>\n\t\t\t<mediaCount>1</mediaCount>\n\t\t\t<localId>0</localId>\n\t\t\t<authIconType>0</authIconType>\n\t\t\t<authIconUrl><![CDATA[]]></authIconUrl>\n\t\t\t<mediaList>\n\t\t\t\t<media>\n\t\t\t\t\t<mediaType>4</mediaType>\n\t\t\t\t\t<url><![CDATA[http://wxapp.tc.qq.com/251/20302/stodownload?encfilekey=rjD5jyTuFrIpZ2ibE8T7YmwgiahniaXswqzr0iaqXAv8gHCLqhu8XNgZia7zqDKouMuZVxxr4Vq2JdPO1C2f4XNOBq8zYhiah49icTskpCQibYhQHX2JNnCrjCqMRA&hy=SH&idx=1&m=&uzid=2&token=6xykWLEnztJD1V02HxcJfY2x4WqhPT8mEhEOvjiaWyeG5A0Jl0Bk0BkW0hgJ4yLlQPdftjCv3TI3MPCbAeexmOB21mODdIb0qUr4dxkgh7kCN9cJ0vRlWV6Uau0pP1iaOcCysBM6GrJnbYoicaicQhgZZeTXxKS8wibe7Ca0VZtCZlqU&basedata=CAESBnhXVDE1NhoGeFdUMTExGgZ4V1QxMTIaBnhXVDE1NhoGeFdUMTU3IhgKCgoGeFdUMTEyEAEKCgoGeFdUMTU3EAEqBwiYHRAAGAI&sign=-aQ9X6aFH2u98P61mulxhec-J1dxw8thRRCvN8BYzybKofOUfHsk0qz8ZJdvzKeKEG3DGxoelV97NAxfFMJxcA&ctsc=25&extg=10ab100&ftype=605&svrbypass=AAuL%2FQsFAAABAAAAAAD8Qqz9SRpHRxAGk3OJaBAAAADnaHZTnGbFfAj9RgZXfw6VeMglF20BLAKz9q94TTleViAcqYLkt5nQMjNibSKDI5T0%2FfyC8nbJ4w%3D%3D&svrnonce=1753838483]]></url>\n\t\t\t\t\t<thumbUrl><![CDATA[http://wxapp.tc.qq.com/251/20304/stodownload?encfilekey=oibeqyX228riaCwo9STVsGLPj9UYCicgttvb31EDysan7BhH7waqsf5EJArUA0emib1E9srp6k0NDRJ9yG4uhsic7fPdibOEoZpUY2ZBjeXYWo09F5yfSrhe5xfEiahp7e0uIFa1UqmsrvgHKQ&hy=SH&idx=1&m=64c77438688b7c63c34cd6f05ed0ef13&uzid=2&picformat=200&wxampicformat=503&token=6xykWLEnztKIzBicPuvgFxp2kXS5aUrNQ0zuoA77blDZf2081icueCQb3WktRiaAbibyU0BCfJFQONow9iacV0qN9VWkPljfS94uSNbmcpwqvtzPTgQib8KdnB09HINxkAibRnVZqBCEBFRh9RXfU0JszON7XTNlTpdzrib6&ctsc=2-25]]></thumbUrl>\n\t\t\t\t\t<coverUrl><![CDATA[http://wxapp.tc.qq.com/251/20304/stodownload?encfilekey=oibeqyX228riaCwo9STVsGLPj9UYCicgttvb31EDysan7BhH7waqsf5EJArUA0emib1E9srp6k0NDRJ9yG4uhsic7fPdibOEoZpUY2ZBjeXYWo09F5yfSrhe5xfEiahp7e0uIFa1UqmsrvgHKQ&hy=SH&idx=1&m=64c77438688b7c63c34cd6f05ed0ef13&uzid=2&picformat=200&wxampicformat=503&token=6xykWLEnztKIzBicPuvgFxp2kXS5aUrNQ0zuoA77blDZf2081icueCQb3WktRiaAbibyU0BCfJFQONow9iacV0qN9VWkPljfS94uSNbmcpwqvtzPTgQib8KdnB09HINxkAibRnVZqBCEBFRh9RXfU0JszON7XTNlTpdzrib6&ctsc=2-25]]></coverUrl>\n\t\t\t\t\t<fullCoverUrl><![CDATA[]]></fullCoverUrl>\n\t\t\t\t\t<fullClipInset><![CDATA[]]></fullClipInset>\n\t\t\t\t\t<width>1920.0</width>\n\t\t\t\t\t<height>1072.0</height>\n\t\t\t\t\t<videoPlayDuration>8</videoPlayDuration>\n\t\t\t\t</media>\n\t\t\t</mediaList>\n\t\t\t<megaVideo>\n\t\t\t\t<objectId />\n\t\t\t\t<objectNonceId />\n\t\t\t</megaVideo>\n\t\t\t<bizUsername />\n\t\t\t<bizNickname />\n\t\t\t<bizAvatar><![CDATA[]]></bizAvatar>\n\t\t\t<bizUsernameV2 />\n\t\t\t<bizAuthIconType>0</bizAuthIconType>\n\t\t\t<bizAuthIconUrl><![CDATA[]]></bizAuthIconUrl>\n\t\t\t<coverEffectType>0</coverEffectType>\n\t\t\t<coverEffectText><![CDATA[]]></coverEffectText>\n\t\t\t<finderForwardSource><![CDATA[]]></finderForwardSource>\n\t\t\t<contactJumpInfoStr><![CDATA[]]></contactJumpInfoStr>\n\t\t\t<ecSource><![CDATA[]]></ecSource>\n\t\t\t<lastGMsgID><![CDATA[]]></lastGMsgID>\n\t\t\t<sourceCommentScene>1</sourceCommentScene>\n\t\t</finderFeed>\n\t\t<finderLiveProductShare>\n\t\t\t<finderLiveID><![CDATA[]]></finderLiveID>\n\t\t\t<finderUsername><![CDATA[]]></finderUsername>\n\t\t\t<finderObjectID><![CDATA[]]></finderObjectID>\n\t\t\t<finderNonceID><![CDATA[]]></finderNonceID>\n\t\t\t<liveStatus><![CDATA[]]></liveStatus>\n\t\t\t<appId><![CDATA[]]></appId>\n\t\t\t<pagePath><![CDATA[]]></pagePath>\n\t\t\t<productId><![CDATA[]]></productId>\n\t\t\t<coverUrl><![CDATA[]]></coverUrl>\n\t\t\t<productTitle><![CDATA[]]></productTitle>\n\t\t\t<marketPrice><![CDATA[0]]></marketPrice>\n\t\t\t<sellingPrice><![CDATA[0]]></sellingPrice>\n\t\t\t<platformHeadImg><![CDATA[]]></platformHeadImg>\n\t\t\t<platformName><![CDATA[]]></platformName>\n\t\t\t<shopWindowId><![CDATA[]]></shopWindowId>\n\t\t\t<flashSalePrice><![CDATA[0]]></flashSalePrice>\n\t\t\t<flashSaleEndTime><![CDATA[0]]></flashSaleEndTime>\n\t\t\t<ecSource><![CDATA[]]></ecSource>\n\t\t\t<sellingPriceWording><![CDATA[]]></sellingPriceWording>\n\t\t\t<platformIconURL><![CDATA[]]></platformIconURL>\n\t\t\t<firstProductTagURL><![CDATA[]]></firstProductTagURL>\n\t\t\t<firstProductTagAspectRatioString><![CDATA[0.0]]></firstProductTagAspectRatioString>\n\t\t\t<secondProductTagURL><![CDATA[]]></secondProductTagURL>\n\t\t\t<secondProductTagAspectRatioString><![CDATA[0.0]]></secondProductTagAspectRatioString>\n\t\t\t<firstGuaranteeWording><![CDATA[]]></firstGuaranteeWording>\n\t\t\t<secondGuaranteeWording><![CDATA[]]></secondGuaranteeWording>\n\t\t\t<thirdGuaranteeWording><![CDATA[]]></thirdGuaranteeWording>\n\t\t\t<isPriceBeginShow>false</isPriceBeginShow>\n\t\t\t<lastGMsgID><![CDATA[]]></lastGMsgID>\n\t\t\t<promoterKey><![CDATA[]]></promoterKey>\n\t\t\t<discountWording><![CDATA[]]></discountWording>\n\t\t\t<priceSuffixDescription><![CDATA[]]></priceSuffixDescription>\n\t\t\t<showBoxItemStringList />\n\t\t</finderLiveProductShare>\n\t\t<finderOrder>\n\t\t\t<appID><![CDATA[]]></appID>\n\t\t\t<orderID><![CDATA[]]></orderID>\n\t\t\t<path><![CDATA[]]></path>\n\t\t\t<priceWording><![CDATA[]]></priceWording>\n\t\t\t<stateWording><![CDATA[]]></stateWording>\n\t\t\t<productImageURL><![CDATA[]]></productImageURL>\n\t\t\t<products><![CDATA[]]></products>\n\t\t\t<productsCount><![CDATA[0]]></productsCount>\n\t\t</finderOrder>\n\t\t<finderShopWindowShare>\n\t\t\t<finderUsername><![CDATA[]]></finderUsername>\n\t\t\t<avatar><![CDATA[]]></avatar>\n\t\t\t<nickname><![CDATA[]]></nickname>\n\t\t\t<commodityInStockCount><![CDATA[]]></commodityInStockCount>\n\t\t\t<appId><![CDATA[]]></appId>\n\t\t\t<path><![CDATA[]]></path>\n\t\t\t<appUsername><![CDATA[]]></appUsername>\n\t\t\t<query><![CDATA[]]></query>\n\t\t\t<liteAppId><![CDATA[]]></liteAppId>\n\t\t\t<liteAppPath><![CDATA[]]></liteAppPath>\n\t\t\t<liteAppQuery><![CDATA[]]></liteAppQuery>\n\t\t\t<platformTagURL><![CDATA[]]></platformTagURL>\n\t\t\t<saleWording><![CDATA[]]></saleWording>\n\t\t\t<lastGMsgID><![CDATA[]]></lastGMsgID>\n\t\t\t<profileTypeWording><![CDATA[]]></profileTypeWording>\n\t\t\t<reputationInfo>\n\t\t\t\t<hasReputationInfo>0</hasReputationInfo>\n\t\t\t\t<reputationScore>0</reputationScore>\n\t\t\t\t<reputationWording />\n\t\t\t\t<reputationTextColor />\n\t\t\t\t<reputationLevelWording />\n\t\t\t\t<reputationBackgroundColor />\n\t\t\t</reputationInfo>\n\t\t\t<productImageURLList />\n\t\t</finderShopWindowShare>\n\t\t<findernamecard>\n\t\t\t<username />\n\t\t\t<avatar><![CDATA[]]></avatar>\n\t\t\t<nickname />\n\t\t\t<auth_job />\n\t\t\t<auth_icon>0</auth_icon>\n\t\t\t<auth_icon_url />\n\t\t\t<ecSource><![CDATA[]]></ecSource>\n\t\t\t<lastGMsgID><![CDATA[]]></lastGMsgID>\n\t\t</findernamecard>\n\t\t<finderGuarantee>\n\t\t\t<scene><![CDATA[0]]></scene>\n\t\t</finderGuarantee>\n\t\t<directshare>0</directshare>\n\t\t<gamecenter>\n\t\t\t<namecard>\n\t\t\t\t<iconUrl />\n\t\t\t\t<name />\n\t\t\t\t<desc />\n\t\t\t\t<tail />\n\t\t\t\t<jumpUrl />\n\t\t\t</namecard>\n\t\t</gamecenter>\n\t\t<patMsg>\n\t\t\t<chatUser />\n\t\t\t<records>\n\t\t\t\t<recordNum>0</recordNum>\n\t\t\t</records>\n\t\t</patMsg>\n\t\t<secretmsg>\n\t\t\t<issecretmsg>0</issecretmsg>\n\t\t</secretmsg>\n\t\t<referfromscene>0</referfromscene>\n\t\t<gameshare>\n\t\t\t<liteappext>\n\t\t\t\t<liteappbizdata />\n\t\t\t\t<priority>0</priority>\n\t\t\t</liteappext>\n\t\t\t<appbrandext>\n\t\t\t\t<litegameinfo />\n\t\t\t\t<priority>-1</priority>\n\t\t\t</appbrandext>\n\t\t\t<gameshareid />\n\t\t\t<sharedata />\n\t\t\t<isvideo>0</isvideo>\n\t\t\t<duration>-1</duration>\n\t\t\t<isexposed>0</isexposed>\n\t\t\t<readtext />\n\t\t</gameshare>\n\t\t<mpsharetrace>\n\t\t\t<hasfinderelement>0</hasfinderelement>\n\t\t\t<lastgmsgid />\n\t\t</mpsharetrace>\n\t\t<wxgamecard>\n\t\t\t<framesetname />\n\t\t\t<mbcarddata />\n\t\t\t<minpkgversion />\n\t\t\t<mbcardheight>0</mbcardheight>\n\t\t\t<isoldversion>0</isoldversion>\n\t\t</wxgamecard>\n\t\t<liteapp>\n\t\t\t<id>null</id>\n\t\t\t<path />\n\t\t\t<query />\n\t\t</liteapp>\n\t</appmsg>\n\t<fromusername>xiaomaochong</fromusername>\n\t<scene>0</scene>\n\t<appinfo>\n\t\t<version>1</version>\n\t\t<appname />\n\t</appinfo>\n\t<commenturl />\n</msg>\n'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1753838498, 'MsgSource': '<msgsource>\n\t<alnode>\n\t\t<fr>2</fr>\n\t</alnode>\n\t<sec_msg_node>\n\t\t<uuid>81826deed7c2b0b17d5f4eb2080d356f_</uuid>\n\t\t<risk-file-flag />\n\t\t<risk-file-md5-list />\n\t</sec_msg_node>\n\t<silence>0</silence>\n\t<membercount>73</membercount>\n\t<signature>N0_V1_1ve9dzbT|v1_eWtlOwqC</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'PushContent': '你收到了一条消息', 'NewMsgId': 7126304894239038703, 'MsgSeq': 871410927}
2025-07-30 09:21:27 | DEBUG | 从群聊消息中提取发送者: xiaomaochong
2025-07-30 09:21:27 | DEBUG | XML消息完整内容:
<?xml version="1.0"?>
<msg>
	<appmsg appid="" sdkver="0">
		<title>当前版本不支持展示该内容，请升级至最新版本。</title>
		<des />
		<username />
		<action>view</action>
		<type>51</type>
		<showtype>0</showtype>
		<content />
		<url>https://support.weixin.qq.com/security/readtemplate?t=w_security_center_website/upgrade</url>
		<lowurl />
		<forwardflag>0</forwardflag>
		<dataurl />
		<lowdataurl />
		<contentattr>0</contentattr>
		<streamvideo>
			<streamvideourl />
			<streamvideototaltime>0</streamvideototaltime>
			<streamvideotitle />
			<streamvideowording />
			<streamvideoweburl />
			<streamvideothumburl />
			<streamvideoaduxinfo />
			<streamvideopublishid />
		</streamvideo>
		<canvasPageItem>
			<canvasPageXml><![CDATA[]]></canvasPageXml>
		</canvasPageItem>
		<appattach>
			<totallen>0</totallen>
			<attachid />
			<cdnattachurl />
			<emoticonmd5></emoticonmd5>
			<aeskey></aeskey>
			<fileext />
			<islargefilemsg>0</islargefilemsg>
		</appattach>
		<extinfo />
		<androidsource>0</androidsource>
		<thumburl />
		<mediatagname />
		<messageaction><![CDATA[]]></messageaction>
		<messageext><![CDATA[]]></messageext>
		<emoticongift>
			<packageflag>0</packageflag>
			<packageid />
		</emoticongift>
		<emoticonshared>
			<packageflag>0</packageflag>
			<packageid />
		</emoticonshared>
		<designershared>
			<designeruin>0</designeruin>
			<designername>null</designername>
			<designerrediretcturl><![CDATA[null]]></designerrediretcturl>
		</designershared>
		<emotionpageshared>
			<tid>0</tid>
			<title>null</title>
			<desc>null</desc>
			<iconUrl><![CDATA[null]]></iconUrl>
			<secondUrl>null</secondUrl>
			<pageType>0</pageType>
			<setKey>null</setKey>
		</emotionpageshared>
		<webviewshared>
			<shareUrlOriginal />
			<shareUrlOpen />
			<jsAppId />
			<publisherId />
			<publisherReqId />
		</webviewshared>
		<template_id />
		<md5 />
		<websearch>
			<rec_category>0</rec_category>
			<channelId>0</channelId>
		</websearch>
		<weappinfo>
			<username />
			<appid />
			<appservicetype>0</appservicetype>
			<secflagforsinglepagemode>0</secflagforsinglepagemode>
			<videopageinfo>
				<thumbwidth>0</thumbwidth>
				<thumbheight>0</thumbheight>
				<fromopensdk>0</fromopensdk>
			</videopageinfo>
		</weappinfo>
		<statextstr />
		<musicShareItem>
			<musicDuration>0</musicDuration>
		</musicShareItem>
		<finderFeed>
			<objectId>14598705382467705052</objectId>
			<objectNonceId>17798329878216367954_0_25_2_3_1753838483387466_81764af8-6ce3-11f0-a565-a367457e48aa</objectNonceId>
			<feedType>4</feedType>
			<nickname>猫少驾到</nickname>
			<username>v2_060000231003b20faec8c7e4811fc6d6c700ed37b077c3381147ac15c36684dd7b8d9a736b4c@finder</username>
			<avatar><![CDATA[https://wx.qlogo.cn/finderhead/ver_1/UY5JdCjPZx3DGRxjKeefYd7FlrUia4L9nKQsn4KUP3SvRibyDUiaQAVib2VwM23U30pJn6sKr8RtNE0KEXbX6s2yiaP9Z9EPehHsUhmc79y5jQDl0pxYepdcq0xPiaMRib2XFtS/0]]></avatar>
			<desc>那很有生活了#成功女人#抽象#猎奇</desc>
			<mediaCount>1</mediaCount>
			<localId>0</localId>
			<authIconType>0</authIconType>
			<authIconUrl><![CDATA[]]></authIconUrl>
			<mediaList>
				<media>
					<mediaType>4</mediaType>
					<url><![CDATA[http://wxapp.tc.qq.com/251/20302/stodownload?encfilekey=rjD5jyTuFrIpZ2ibE8T7YmwgiahniaXswqzr0iaqXAv8gHCLqhu8XNgZia7zqDKouMuZVxxr4Vq2JdPO1C2f4XNOBq8zYhiah49icTskpCQibYhQHX2JNnCrjCqMRA&hy=SH&idx=1&m=&uzid=2&token=6xykWLEnztJD1V02HxcJfY2x4WqhPT8mEhEOvjiaWyeG5A0Jl0Bk0BkW0hgJ4yLlQPdftjCv3TI3MPCbAeexmOB21mODdIb0qUr4dxkgh7kCN9cJ0vRlWV6Uau0pP1iaOcCysBM6GrJnbYoicaicQhgZZeTXxKS8wibe7Ca0VZtCZlqU&basedata=CAESBnhXVDE1NhoGeFdUMTExGgZ4V1QxMTIaBnhXVDE1NhoGeFdUMTU3IhgKCgoGeFdUMTEyEAEKCgoGeFdUMTU3EAEqBwiYHRAAGAI&sign=-aQ9X6aFH2u98P61mulxhec-J1dxw8thRRCvN8BYzybKofOUfHsk0qz8ZJdvzKeKEG3DGxoelV97NAxfFMJxcA&ctsc=25&extg=10ab100&ftype=605&svrbypass=AAuL%2FQsFAAABAAAAAAD8Qqz9SRpHRxAGk3OJaBAAAADnaHZTnGbFfAj9RgZXfw6VeMglF20BLAKz9q94TTleViAcqYLkt5nQMjNibSKDI5T0%2FfyC8nbJ4w%3D%3D&svrnonce=1753838483]]></url>
					<thumbUrl><![CDATA[http://wxapp.tc.qq.com/251/20304/stodownload?encfilekey=oibeqyX228riaCwo9STVsGLPj9UYCicgttvb31EDysan7BhH7waqsf5EJArUA0emib1E9srp6k0NDRJ9yG4uhsic7fPdibOEoZpUY2ZBjeXYWo09F5yfSrhe5xfEiahp7e0uIFa1UqmsrvgHKQ&hy=SH&idx=1&m=64c77438688b7c63c34cd6f05ed0ef13&uzid=2&picformat=200&wxampicformat=503&token=6xykWLEnztKIzBicPuvgFxp2kXS5aUrNQ0zuoA77blDZf2081icueCQb3WktRiaAbibyU0BCfJFQONow9iacV0qN9VWkPljfS94uSNbmcpwqvtzPTgQib8KdnB09HINxkAibRnVZqBCEBFRh9RXfU0JszON7XTNlTpdzrib6&ctsc=2-25]]></thumbUrl>
					<coverUrl><![CDATA[http://wxapp.tc.qq.com/251/20304/stodownload?encfilekey=oibeqyX228riaCwo9STVsGLPj9UYCicgttvb31EDysan7BhH7waqsf5EJArUA0emib1E9srp6k0NDRJ9yG4uhsic7fPdibOEoZpUY2ZBjeXYWo09F5yfSrhe5xfEiahp7e0uIFa1UqmsrvgHKQ&hy=SH&idx=1&m=64c77438688b7c63c34cd6f05ed0ef13&uzid=2&picformat=200&wxampicformat=503&token=6xykWLEnztKIzBicPuvgFxp2kXS5aUrNQ0zuoA77blDZf2081icueCQb3WktRiaAbibyU0BCfJFQONow9iacV0qN9VWkPljfS94uSNbmcpwqvtzPTgQib8KdnB09HINxkAibRnVZqBCEBFRh9RXfU0JszON7XTNlTpdzrib6&ctsc=2-25]]></coverUrl>
					<fullCoverUrl><![CDATA[]]></fullCoverUrl>
					<fullClipInset><![CDATA[]]></fullClipInset>
					<width>1920.0</width>
					<height>1072.0</height>
					<videoPlayDuration>8</videoPlayDuration>
				</media>
			</mediaList>
			<megaVideo>
				<objectId />
				<objectNonceId />
			</megaVideo>
			<bizUsername />
			<bizNickname />
			<bizAvatar><![CDATA[]]></bizAvatar>
			<bizUsernameV2 />
			<bizAuthIconType>0</bizAuthIconType>
			<bizAuthIconUrl><![CDATA[]]></bizAuthIconUrl>
			<coverEffectType>0</coverEffectType>
			<coverEffectText><![CDATA[]]></coverEffectText>
			<finderForwardSource><![CDATA[]]></finderForwardSource>
			<contactJumpInfoStr><![CDATA[]]></contactJumpInfoStr>
			<ecSource><![CDATA[]]></ecSource>
			<lastGMsgID><![CDATA[]]></lastGMsgID>
			<sourceCommentScene>1</sourceCommentScene>
		</finderFeed>
		<finderLiveProductShare>
			<finderLiveID><![CDATA[]]></finderLiveID>
			<finderUsername><![CDATA[]]></finderUsername>
			<finderObjectID><![CDATA[]]></finderObjectID>
			<finderNonceID><![CDATA[]]></finderNonceID>
			<liveStatus><![CDATA[]]></liveStatus>
			<appId><![CDATA[]]></appId>
			<pagePath><![CDATA[]]></pagePath>
			<productId><![CDATA[]]></productId>
			<coverUrl><![CDATA[]]></coverUrl>
			<productTitle><![CDATA[]]></productTitle>
			<marketPrice><![CDATA[0]]></marketPrice>
			<sellingPrice><![CDATA[0]]></sellingPrice>
			<platformHeadImg><![CDATA[]]></platformHeadImg>
			<platformName><![CDATA[]]></platformName>
			<shopWindowId><![CDATA[]]></shopWindowId>
			<flashSalePrice><![CDATA[0]]></flashSalePrice>
			<flashSaleEndTime><![CDATA[0]]></flashSaleEndTime>
			<ecSource><![CDATA[]]></ecSource>
			<sellingPriceWording><![CDATA[]]></sellingPriceWording>
			<platformIconURL><![CDATA[]]></platformIconURL>
			<firstProductTagURL><![CDATA[]]></firstProductTagURL>
			<firstProductTagAspectRatioString><![CDATA[0.0]]></firstProductTagAspectRatioString>
			<secondProductTagURL><![CDATA[]]></secondProductTagURL>
			<secondProductTagAspectRatioString><![CDATA[0.0]]></secondProductTagAspectRatioString>
			<firstGuaranteeWording><![CDATA[]]></firstGuaranteeWording>
			<secondGuaranteeWording><![CDATA[]]></secondGuaranteeWording>
			<thirdGuaranteeWording><![CDATA[]]></thirdGuaranteeWording>
			<isPriceBeginShow>false</isPriceBeginShow>
			<lastGMsgID><![CDATA[]]></lastGMsgID>
			<promoterKey><![CDATA[]]></promoterKey>
			<discountWording><![CDATA[]]></discountWording>
			<priceSuffixDescription><![CDATA[]]></priceSuffixDescription>
			<showBoxItemStringList />
		</finderLiveProductShare>
		<finderOrder>
			<appID><![CDATA[]]></appID>
			<orderID><![CDATA[]]></orderID>
			<path><![CDATA[]]></path>
			<priceWording><![CDATA[]]></priceWording>
			<stateWording><![CDATA[]]></stateWording>
			<productImageURL><![CDATA[]]></productImageURL>
			<products><![CDATA[]]></products>
			<productsCount><![CDATA[0]]></productsCount>
		</finderOrder>
		<finderShopWindowShare>
			<finderUsername><![CDATA[]]></finderUsername>
			<avatar><![CDATA[]]></avatar>
			<nickname><![CDATA[]]></nickname>
			<commodityInStockCount><![CDATA[]]></commodityInStockCount>
			<appId><![CDATA[]]></appId>
			<path><![CDATA[]]></path>
			<appUsername><![CDATA[]]></appUsername>
			<query><![CDATA[]]></query>
			<liteAppId><![CDATA[]]></liteAppId>
			<liteAppPath><![CDATA[]]></liteAppPath>
			<liteAppQuery><![CDATA[]]></liteAppQuery>
			<platformTagURL><![CDATA[]]></platformTagURL>
			<saleWording><![CDATA[]]></saleWording>
			<lastGMsgID><![CDATA[]]></lastGMsgID>
			<profileTypeWording><![CDATA[]]></profileTypeWording>
			<reputationInfo>
				<hasReputationInfo>0</hasReputationInfo>
				<reputationScore>0</reputationScore>
				<reputationWording />
				<reputationTextColor />
				<reputationLevelWording />
				<reputationBackgroundColor />
			</reputationInfo>
			<productImageURLList />
		</finderShopWindowShare>
		<findernamecard>
			<username />
			<avatar><![CDATA[]]></avatar>
			<nickname />
			<auth_job />
			<auth_icon>0</auth_icon>
			<auth_icon_url />
			<ecSource><![CDATA[]]></ecSource>
			<lastGMsgID><![CDATA[]]></lastGMsgID>
		</findernamecard>
		<finderGuarantee>
			<scene><![CDATA[0]]></scene>
		</finderGuarantee>
		<directshare>0</directshare>
		<gamecenter>
			<namecard>
				<iconUrl />
				<name />
				<desc />
				<tail />
				<jumpUrl />
			</namecard>
		</gamecenter>
		<patMsg>
			<chatUser />
			<records>
				<recordNum>0</recordNum>
			</records>
		</patMsg>
		<secretmsg>
			<issecretmsg>0</issecretmsg>
		</secretmsg>
		<referfromscene>0</referfromscene>
		<gameshare>
			<liteappext>
				<liteappbizdata />
				<priority>0</priority>
			</liteappext>
			<appbrandext>
				<litegameinfo />
				<priority>-1</priority>
			</appbrandext>
			<gameshareid />
			<sharedata />
			<isvideo>0</isvideo>
			<duration>-1</duration>
			<isexposed>0</isexposed>
			<readtext />
		</gameshare>
		<mpsharetrace>
			<hasfinderelement>0</hasfinderelement>
			<lastgmsgid />
		</mpsharetrace>
		<wxgamecard>
			<framesetname />
			<mbcarddata />
			<minpkgversion />
			<mbcardheight>0</mbcardheight>
			<isoldversion>0</isoldversion>
		</wxgamecard>
		<liteapp>
			<id>null</id>
			<path />
			<query />
		</liteapp>
	</appmsg>
	<fromusername>xiaomaochong</fromusername>
	<scene>0</scene>
	<appinfo>
		<version>1</version>
		<appname />
	</appinfo>
	<commenturl />
</msg>

2025-07-30 09:21:27 | DEBUG | XML消息类型: 51
2025-07-30 09:21:27 | DEBUG | XML消息标题: 当前版本不支持展示该内容，请升级至最新版本。
2025-07-30 09:21:27 | DEBUG | XML消息描述: None
2025-07-30 09:21:27 | DEBUG | 附件信息 totallen: 0
2025-07-30 09:21:27 | DEBUG | 附件信息 islargefilemsg: 0
2025-07-30 09:21:27 | DEBUG | XML消息URL: https://support.weixin.qq.com/security/readtemplate?t=w_security_center_website/upgrade
2025-07-30 09:21:27 | INFO | 未知的XML消息类型: 51
2025-07-30 09:21:27 | INFO | 消息标题: 当前版本不支持展示该内容，请升级至最新版本。
2025-07-30 09:21:27 | INFO | 消息描述: None
2025-07-30 09:21:27 | INFO | 消息URL: https://support.weixin.qq.com/security/readtemplate?t=w_security_center_website/upgrade
2025-07-30 09:21:27 | INFO | 完整XML内容:
<?xml version="1.0"?>
<msg>
	<appmsg appid="" sdkver="0">
		<title>当前版本不支持展示该内容，请升级至最新版本。</title>
		<des />
		<username />
		<action>view</action>
		<type>51</type>
		<showtype>0</showtype>
		<content />
		<url>https://support.weixin.qq.com/security/readtemplate?t=w_security_center_website/upgrade</url>
		<lowurl />
		<forwardflag>0</forwardflag>
		<dataurl />
		<lowdataurl />
		<contentattr>0</contentattr>
		<streamvideo>
			<streamvideourl />
			<streamvideototaltime>0</streamvideototaltime>
			<streamvideotitle />
			<streamvideowording />
			<streamvideoweburl />
			<streamvideothumburl />
			<streamvideoaduxinfo />
			<streamvideopublishid />
		</streamvideo>
		<canvasPageItem>
			<canvasPageXml><![CDATA[]]></canvasPageXml>
		</canvasPageItem>
		<appattach>
			<totallen>0</totallen>
			<attachid />
			<cdnattachurl />
			<emoticonmd5></emoticonmd5>
			<aeskey></aeskey>
			<fileext />
			<islargefilemsg>0</islargefilemsg>
		</appattach>
		<extinfo />
		<androidsource>0</androidsource>
		<thumburl />
		<mediatagname />
		<messageaction><![CDATA[]]></messageaction>
		<messageext><![CDATA[]]></messageext>
		<emoticongift>
			<packageflag>0</packageflag>
			<packageid />
		</emoticongift>
		<emoticonshared>
			<packageflag>0</packageflag>
			<packageid />
		</emoticonshared>
		<designershared>
			<designeruin>0</designeruin>
			<designername>null</designername>
			<designerrediretcturl><![CDATA[null]]></designerrediretcturl>
		</designershared>
		<emotionpageshared>
			<tid>0</tid>
			<title>null</title>
			<desc>null</desc>
			<iconUrl><![CDATA[null]]></iconUrl>
			<secondUrl>null</secondUrl>
			<pageType>0</pageType>
			<setKey>null</setKey>
		</emotionpageshared>
		<webviewshared>
			<shareUrlOriginal />
			<shareUrlOpen />
			<jsAppId />
			<publisherId />
			<publisherReqId />
		</webviewshared>
		<template_id />
		<md5 />
		<websearch>
			<rec_category>0</rec_category>
			<channelId>0</channelId>
		</websearch>
		<weappinfo>
			<username />
			<appid />
			<appservicetype>0</appservicetype>
			<secflagforsinglepagemode>0</secflagforsinglepagemode>
			<videopageinfo>
				<thumbwidth>0</thumbwidth>
				<thumbheight>0</thumbheight>
				<fromopensdk>0</fromopensdk>
			</videopageinfo>
		</weappinfo>
		<statextstr />
		<musicShareItem>
			<musicDuration>0</musicDuration>
		</musicShareItem>
		<finderFeed>
			<objectId>14598705382467705052</objectId>
			<objectNonceId>17798329878216367954_0_25_2_3_1753838483387466_81764af8-6ce3-11f0-a565-a367457e48aa</objectNonceId>
			<feedType>4</feedType>
			<nickname>猫少驾到</nickname>
			<username>v2_060000231003b20faec8c7e4811fc6d6c700ed37b077c3381147ac15c36684dd7b8d9a736b4c@finder</username>
			<avatar><![CDATA[https://wx.qlogo.cn/finderhead/ver_1/UY5JdCjPZx3DGRxjKeefYd7FlrUia4L9nKQsn4KUP3SvRibyDUiaQAVib2VwM23U30pJn6sKr8RtNE0KEXbX6s2yiaP9Z9EPehHsUhmc79y5jQDl0pxYepdcq0xPiaMRib2XFtS/0]]></avatar>
			<desc>那很有生活了#成功女人#抽象#猎奇</desc>
			<mediaCount>1</mediaCount>
			<localId>0</localId>
			<authIconType>0</authIconType>
			<authIconUrl><![CDATA[]]></authIconUrl>
			<mediaList>
				<media>
					<mediaType>4</mediaType>
					<url><![CDATA[http://wxapp.tc.qq.com/251/20302/stodownload?encfilekey=rjD5jyTuFrIpZ2ibE8T7YmwgiahniaXswqzr0iaqXAv8gHCLqhu8XNgZia7zqDKouMuZVxxr4Vq2JdPO1C2f4XNOBq8zYhiah49icTskpCQibYhQHX2JNnCrjCqMRA&hy=SH&idx=1&m=&uzid=2&token=6xykWLEnztJD1V02HxcJfY2x4WqhPT8mEhEOvjiaWyeG5A0Jl0Bk0BkW0hgJ4yLlQPdftjCv3TI3MPCbAeexmOB21mODdIb0qUr4dxkgh7kCN9cJ0vRlWV6Uau0pP1iaOcCysBM6GrJnbYoicaicQhgZZeTXxKS8wibe7Ca0VZtCZlqU&basedata=CAESBnhXVDE1NhoGeFdUMTExGgZ4V1QxMTIaBnhXVDE1NhoGeFdUMTU3IhgKCgoGeFdUMTEyEAEKCgoGeFdUMTU3EAEqBwiYHRAAGAI&sign=-aQ9X6aFH2u98P61mulxhec-J1dxw8thRRCvN8BYzybKofOUfHsk0qz8ZJdvzKeKEG3DGxoelV97NAxfFMJxcA&ctsc=25&extg=10ab100&ftype=605&svrbypass=AAuL%2FQsFAAABAAAAAAD8Qqz9SRpHRxAGk3OJaBAAAADnaHZTnGbFfAj9RgZXfw6VeMglF20BLAKz9q94TTleViAcqYLkt5nQMjNibSKDI5T0%2FfyC8nbJ4w%3D%3D&svrnonce=1753838483]]></url>
					<thumbUrl><![CDATA[http://wxapp.tc.qq.com/251/20304/stodownload?encfilekey=oibeqyX228riaCwo9STVsGLPj9UYCicgttvb31EDysan7BhH7waqsf5EJArUA0emib1E9srp6k0NDRJ9yG4uhsic7fPdibOEoZpUY2ZBjeXYWo09F5yfSrhe5xfEiahp7e0uIFa1UqmsrvgHKQ&hy=SH&idx=1&m=64c77438688b7c63c34cd6f05ed0ef13&uzid=2&picformat=200&wxampicformat=503&token=6xykWLEnztKIzBicPuvgFxp2kXS5aUrNQ0zuoA77blDZf2081icueCQb3WktRiaAbibyU0BCfJFQONow9iacV0qN9VWkPljfS94uSNbmcpwqvtzPTgQib8KdnB09HINxkAibRnVZqBCEBFRh9RXfU0JszON7XTNlTpdzrib6&ctsc=2-25]]></thumbUrl>
					<coverUrl><![CDATA[http://wxapp.tc.qq.com/251/20304/stodownload?encfilekey=oibeqyX228riaCwo9STVsGLPj9UYCicgttvb31EDysan7BhH7waqsf5EJArUA0emib1E9srp6k0NDRJ9yG4uhsic7fPdibOEoZpUY2ZBjeXYWo09F5yfSrhe5xfEiahp7e0uIFa1UqmsrvgHKQ&hy=SH&idx=1&m=64c77438688b7c63c34cd6f05ed0ef13&uzid=2&picformat=200&wxampicformat=503&token=6xykWLEnztKIzBicPuvgFxp2kXS5aUrNQ0zuoA77blDZf2081icueCQb3WktRiaAbibyU0BCfJFQONow9iacV0qN9VWkPljfS94uSNbmcpwqvtzPTgQib8KdnB09HINxkAibRnVZqBCEBFRh9RXfU0JszON7XTNlTpdzrib6&ctsc=2-25]]></coverUrl>
					<fullCoverUrl><![CDATA[]]></fullCoverUrl>
					<fullClipInset><![CDATA[]]></fullClipInset>
					<width>1920.0</width>
					<height>1072.0</height>
					<videoPlayDuration>8</videoPlayDuration>
				</media>
			</mediaList>
			<megaVideo>
				<objectId />
				<objectNonceId />
			</megaVideo>
			<bizUsername />
			<bizNickname />
			<bizAvatar><![CDATA[]]></bizAvatar>
			<bizUsernameV2 />
			<bizAuthIconType>0</bizAuthIconType>
			<bizAuthIconUrl><![CDATA[]]></bizAuthIconUrl>
			<coverEffectType>0</coverEffectType>
			<coverEffectText><![CDATA[]]></coverEffectText>
			<finderForwardSource><![CDATA[]]></finderForwardSource>
			<contactJumpInfoStr><![CDATA[]]></contactJumpInfoStr>
			<ecSource><![CDATA[]]></ecSource>
			<lastGMsgID><![CDATA[]]></lastGMsgID>
			<sourceCommentScene>1</sourceCommentScene>
		</finderFeed>
		<finderLiveProductShare>
			<finderLiveID><![CDATA[]]></finderLiveID>
			<finderUsername><![CDATA[]]></finderUsername>
			<finderObjectID><![CDATA[]]></finderObjectID>
			<finderNonceID><![CDATA[]]></finderNonceID>
			<liveStatus><![CDATA[]]></liveStatus>
			<appId><![CDATA[]]></appId>
			<pagePath><![CDATA[]]></pagePath>
			<productId><![CDATA[]]></productId>
			<coverUrl><![CDATA[]]></coverUrl>
			<productTitle><![CDATA[]]></productTitle>
			<marketPrice><![CDATA[0]]></marketPrice>
			<sellingPrice><![CDATA[0]]></sellingPrice>
			<platformHeadImg><![CDATA[]]></platformHeadImg>
			<platformName><![CDATA[]]></platformName>
			<shopWindowId><![CDATA[]]></shopWindowId>
			<flashSalePrice><![CDATA[0]]></flashSalePrice>
			<flashSaleEndTime><![CDATA[0]]></flashSaleEndTime>
			<ecSource><![CDATA[]]></ecSource>
			<sellingPriceWording><![CDATA[]]></sellingPriceWording>
			<platformIconURL><![CDATA[]]></platformIconURL>
			<firstProductTagURL><![CDATA[]]></firstProductTagURL>
			<firstProductTagAspectRatioString><![CDATA[0.0]]></firstProductTagAspectRatioString>
			<secondProductTagURL><![CDATA[]]></secondProductTagURL>
			<secondProductTagAspectRatioString><![CDATA[0.0]]></secondProductTagAspectRatioString>
			<firstGuaranteeWording><![CDATA[]]></firstGuaranteeWording>
			<secondGuaranteeWording><![CDATA[]]></secondGuaranteeWording>
			<thirdGuaranteeWording><![CDATA[]]></thirdGuaranteeWording>
			<isPriceBeginShow>false</isPriceBeginShow>
			<lastGMsgID><![CDATA[]]></lastGMsgID>
			<promoterKey><![CDATA[]]></promoterKey>
			<discountWording><![CDATA[]]></discountWording>
			<priceSuffixDescription><![CDATA[]]></priceSuffixDescription>
			<showBoxItemStringList />
		</finderLiveProductShare>
		<finderOrder>
			<appID><![CDATA[]]></appID>
			<orderID><![CDATA[]]></orderID>
			<path><![CDATA[]]></path>
			<priceWording><![CDATA[]]></priceWording>
			<stateWording><![CDATA[]]></stateWording>
			<productImageURL><![CDATA[]]></productImageURL>
			<products><![CDATA[]]></products>
			<productsCount><![CDATA[0]]></productsCount>
		</finderOrder>
		<finderShopWindowShare>
			<finderUsername><![CDATA[]]></finderUsername>
			<avatar><![CDATA[]]></avatar>
			<nickname><![CDATA[]]></nickname>
			<commodityInStockCount><![CDATA[]]></commodityInStockCount>
			<appId><![CDATA[]]></appId>
			<path><![CDATA[]]></path>
			<appUsername><![CDATA[]]></appUsername>
			<query><![CDATA[]]></query>
			<liteAppId><![CDATA[]]></liteAppId>
			<liteAppPath><![CDATA[]]></liteAppPath>
			<liteAppQuery><![CDATA[]]></liteAppQuery>
			<platformTagURL><![CDATA[]]></platformTagURL>
			<saleWording><![CDATA[]]></saleWording>
			<lastGMsgID><![CDATA[]]></lastGMsgID>
			<profileTypeWording><![CDATA[]]></profileTypeWording>
			<reputationInfo>
				<hasReputationInfo>0</hasReputationInfo>
				<reputationScore>0</reputationScore>
				<reputationWording />
				<reputationTextColor />
				<reputationLevelWording />
				<reputationBackgroundColor />
			</reputationInfo>
			<productImageURLList />
		</finderShopWindowShare>
		<findernamecard>
			<username />
			<avatar><![CDATA[]]></avatar>
			<nickname />
			<auth_job />
			<auth_icon>0</auth_icon>
			<auth_icon_url />
			<ecSource><![CDATA[]]></ecSource>
			<lastGMsgID><![CDATA[]]></lastGMsgID>
		</findernamecard>
		<finderGuarantee>
			<scene><![CDATA[0]]></scene>
		</finderGuarantee>
		<directshare>0</directshare>
		<gamecenter>
			<namecard>
				<iconUrl />
				<name />
				<desc />
				<tail />
				<jumpUrl />
			</namecard>
		</gamecenter>
		<patMsg>
			<chatUser />
			<records>
				<recordNum>0</recordNum>
			</records>
		</patMsg>
		<secretmsg>
			<issecretmsg>0</issecretmsg>
		</secretmsg>
		<referfromscene>0</referfromscene>
		<gameshare>
			<liteappext>
				<liteappbizdata />
				<priority>0</priority>
			</liteappext>
			<appbrandext>
				<litegameinfo />
				<priority>-1</priority>
			</appbrandext>
			<gameshareid />
			<sharedata />
			<isvideo>0</isvideo>
			<duration>-1</duration>
			<isexposed>0</isexposed>
			<readtext />
		</gameshare>
		<mpsharetrace>
			<hasfinderelement>0</hasfinderelement>
			<lastgmsgid />
		</mpsharetrace>
		<wxgamecard>
			<framesetname />
			<mbcarddata />
			<minpkgversion />
			<mbcardheight>0</mbcardheight>
			<isoldversion>0</isoldversion>
		</wxgamecard>
		<liteapp>
			<id>null</id>
			<path />
			<query />
		</liteapp>
	</appmsg>
	<fromusername>xiaomaochong</fromusername>
	<scene>0</scene>
	<appinfo>
		<version>1</version>
		<appname />
	</appinfo>
	<commenturl />
</msg>

2025-07-30 09:21:28 | DEBUG | 收到消息: {'MsgId': 1286131902, 'FromUserName': {'string': '48097389945@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 47, 'Content': {'string': 'wxid_ubbh6q832tcs21:\n<msg><emoji fromusername = "wxid_ubbh6q832tcs21" tousername = "48097389945@chatroom" type="2" idbuffer="media:0_0" md5="3c5d9173b23ecc025e1ec8c858142b57" len = "1005541" productid="" androidmd5="3c5d9173b23ecc025e1ec8c858142b57" androidlen="1005541" s60v3md5 = "3c5d9173b23ecc025e1ec8c858142b57" s60v3len="1005541" s60v5md5 = "3c5d9173b23ecc025e1ec8c858142b57" s60v5len="1005541" cdnurl = "http://vweixinf.tc.qq.com/110/20401/stodownload?m=3c5d9173b23ecc025e1ec8c858142b57&amp;filekey=30440201010430302e02016e040253480420336335643931373362323365636330323565316563386338353831343262353702030f57e5040d00000004627466730000000132&amp;hy=SH&amp;storeid=2683a773b00015f7c687175f00000006e01004fb153480fd65b01e02592370&amp;ef=1&amp;bizid=1022" designerid = "" thumburl = "" encrypturl = "http://vweixinf.tc.qq.com/110/20402/stodownload?m=4bab97cabbe1706f0a603389b3529eeb&amp;filekey=30440201010430302e02016e040253480420346261623937636162626531373036663061363033333839623335323965656202030f57f0040d00000004627466730000000132&amp;hy=SH&amp;storeid=2683a773b0003cd1f687175f00000006e02004fb253480fd65b01e02592390&amp;ef=2&amp;bizid=1022" aeskey= "0d82b033625c4bf18bc5c338aeb7333e" externurl = "http://vweixinf.tc.qq.com/110/20403/stodownload?m=0064acb5c83b81eef262bb4c27a223c8&amp;filekey=30440201010430302e02016e04025348042030303634616362356338336238316565663236326262346332376132323363380203020430040d00000004627466730000000132&amp;hy=SH&amp;storeid=2683a773b0005d930687175f00000006e03004fb353480fd65b01e025923ad&amp;ef=3&amp;bizid=1022" externmd5 = "ef10f433c759c4681e3dcaecf0b0a32d" width= "640" height= "640" tpurl= "" tpauthkey= "" attachedtext= "" attachedtextcolor= "" lensid= "" emojiattr= "" linkid= "" desc= "" ></emoji>  </msg>'}, 'Status': 3, 'ImgStatus': 2, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1753838499, 'MsgSource': '<msgsource>\n\t<silence>0</silence>\n\t<membercount>73</membercount>\n\t<signature>N0_V1_IJM1p8fF|v1_sAj4qUP5</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'PushContent': '郭在群聊中发了一个表情', 'NewMsgId': 3277272899120957192, 'MsgSeq': 871410928}
2025-07-30 09:21:28 | INFO | 收到表情消息: 消息ID:1286131902 来自:48097389945@chatroom 发送人:wxid_ubbh6q832tcs21 MD5:3c5d9173b23ecc025e1ec8c858142b57 大小:1005541
2025-07-30 09:21:28 | DEBUG | [DoubaoImageRecognition] 缓存表情消息: 3277272899120957192
2025-07-30 09:21:35 | DEBUG | 收到消息: {'MsgId': 669633397, 'FromUserName': {'string': '48097389945@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 1, 'Content': {'string': 'wxid_jegyk4i3v7zg22:\n豆包今夕是何年'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1753838507, 'MsgSource': '<msgsource>\n\t<bizflag>0</bizflag>\n\t<pua>1</pua>\n\t<eggIncluded>1</eggIncluded>\n\t<silence>0</silence>\n\t<membercount>73</membercount>\n\t<signature>N0_V1_jzr4w5oP|v1_biTj5Iw2</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'PushContent': '阿尼亚与她 : 豆包今夕是何年', 'NewMsgId': 6993190731966770983, 'MsgSeq': 871410929}
2025-07-30 09:21:35 | INFO | 收到文本消息: 消息ID:669633397 来自:48097389945@chatroom 发送人:wxid_jegyk4i3v7zg22 @:[] 内容:豆包今夕是何年
2025-07-30 09:21:36 | DEBUG | 处理消息内容: '豆包今夕是何年'
2025-07-30 09:21:36 | DEBUG | 消息内容 '豆包今夕是何年' 不匹配任何命令，忽略
2025-07-30 09:21:47 | DEBUG | 收到消息: {'MsgId': 886635860, 'FromUserName': {'string': '48097389945@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 1, 'Content': {'string': 'wxid_srknxij3jka022:\n正在搜索找到 4 篇资料参考从公历角度来说，今夕是2025年。从中国传统干支纪年法来看，今年是乙巳年，也被称为木蛇年或绿蛇年。\n\n“今夕是何年”出自宋代苏轼的《水调歌头·明月几时有》，原句为“不知天上宫阙，今夕是何年”。此句以设问表达了对时间和宇宙的思考。此外，陆游在《戏作绝句以唐人句终之》中也有“回头问童子：今夕是何年？”的诗句，抒发了对时光流转的感慨。'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1753838519, 'MsgSource': '<msgsource>\n\t<pua>1</pua>\n\t<eggIncluded>1</eggIncluded>\n\t<silence>0</silence>\n\t<membercount>73</membercount>\n\t<signature>N0_V1_02qnzbAH|v1_9hj4Qsgc</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'PushContent': '她 : 正在搜索找到 4 篇资料参考从公历角度来说，今夕是2025年。从中国...', 'NewMsgId': 5817129492890180623, 'MsgSeq': 871410930}
2025-07-30 09:21:47 | INFO | 收到文本消息: 消息ID:886635860 来自:48097389945@chatroom 发送人:wxid_srknxij3jka022 @:[] 内容:正在搜索找到 4 篇资料参考从公历角度来说，今夕是2025年。从中国传统干支纪年法来看，今年是乙巳年，也被称为木蛇年或绿蛇年。

“今夕是何年”出自宋代苏轼的《水调歌头·明月几时有》，原句为“不知天上宫阙，今夕是何年”。此句以设问表达了对时间和宇宙的思考。此外，陆游在《戏作绝句以唐人句终之》中也有“回头问童子：今夕是何年？”的诗句，抒发了对时光流转的感慨。
2025-07-30 09:21:48 | DEBUG | 处理消息内容: '正在搜索找到 4 篇资料参考从公历角度来说，今夕是2025年。从中国传统干支纪年法来看，今年是乙巳年，也被称为木蛇年或绿蛇年。

“今夕是何年”出自宋代苏轼的《水调歌头·明月几时有》，原句为“不知天上宫阙，今夕是何年”。此句以设问表达了对时间和宇宙的思考。此外，陆游在《戏作绝句以唐人句终之》中也有“回头问童子：今夕是何年？”的诗句，抒发了对时光流转的感慨。'
2025-07-30 09:21:48 | DEBUG | 消息内容 '正在搜索找到 4 篇资料参考从公历角度来说，今夕是2025年。从中国传统干支纪年法来看，今年是乙巳年，也被称为木蛇年或绿蛇年。

“今夕是何年”出自宋代苏轼的《水调歌头·明月几时有》，原句为“不知天上宫阙，今夕是何年”。此句以设问表达了对时间和宇宙的思考。此外，陆游在《戏作绝句以唐人句终之》中也有“回头问童子：今夕是何年？”的诗句，抒发了对时光流转的感慨。' 不匹配任何命令，忽略
2025-07-30 09:22:16 | DEBUG | 收到消息: {'MsgId': 237001991, 'FromUserName': {'string': '48097389945@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 1, 'Content': {'string': 'wxid_ubbh6q832tcs21:\n找视频 变装'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1753838547, 'MsgSource': '<msgsource>\n\t<pua>1</pua>\n\t<eggIncluded>1</eggIncluded>\n\t<silence>0</silence>\n\t<membercount>73</membercount>\n\t<signature>N0_V1_PlPLywkP|v1_USddFwU/</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'PushContent': '郭 : 找视频 变装', 'NewMsgId': 197247306841681275, 'MsgSeq': 871410931}
2025-07-30 09:22:16 | INFO | 收到文本消息: 消息ID:237001991 来自:48097389945@chatroom 发送人:wxid_ubbh6q832tcs21 @:[] 内容:找视频 变装
2025-07-30 09:22:16 | INFO | [DoubaoVideoSearch] 收到视频搜索请求: 找视频 变装 from wxid_ubbh6q832tcs21
2025-07-30 09:22:16 | INFO | [DoubaoVideoSearch] 开始视频搜索，关键词: 变装
2025-07-30 09:22:23 | DEBUG | [DoubaoVideoSearch] API请求成功，开始处理响应流
2025-07-30 09:22:23 | INFO | [DoubaoVideoSearch] 视频搜索成功，结果类型: videos
2025-07-30 09:22:23 | INFO | [DoubaoVideoSearch] 收到18个视频结果
2025-07-30 09:22:23 | DEBUG | [DoubaoVideoSearch] 使用查询关键词进行去重: 变装
2025-07-30 09:22:23 | INFO | [DoubaoVideoSearch] 开始解析视频链接: https://m.douyin.com/share/video/7530100770836041017/?scene_from=douyin_h5_flow
2025-07-30 09:22:23 | DEBUG | [DoubaoVideoSearch] 开始解析抖音视频: https://m.douyin.com/share/video/7530100770836041017/?scene_from=douyin_h5_flow
2025-07-30 09:22:24 | INFO | [DoubaoVideoSearch] 视频解析成功: Al一键换古装特效教程，AI一键古装试穿特效入口#即梦ai #ai换装古风 #古装变身特效 #一键免费换装古装特效 #一键拍同款古装  一键变装特效古装 by Anden
2025-07-30 09:22:24 | INFO | [DoubaoVideoSearch] 视频解析成功: Al一键换古装特效教程，AI一键古装试穿特效入口#即梦ai #ai换装古风 #古装变身特效 #一键免费换装古装特效 #一键拍同款古装  一键变装特效古装
2025-07-30 09:22:25 | INFO | 发送app消息: 对方wxid:48097389945@chatroom 类型:68 xml:<appmsg appid="wx75f04c8595ccb9f6" sdkver="0"><title>Al一键换古装特效教程，AI一键古装试穿特效入口#即梦ai #ai换装古风 #古装变身特效 #一键免费换装古装特效 #一键拍同款古装  一键变装特效古装</title><des>作者: Anden</des><action>view</action><type>68</type><showtype>0</showtype><content/><url>https://aweme.snssdk.com/aweme/v1/play/?video_id=v1e00fgi0000d204kvvog65j3mao8e60&ratio=720p&line=0</url><dataurl/><lowurl>https://game.weixin.qq.com/</lowurl><lowdataurl/><recorditem/><thumburl>https://p26-sign.douyinpic.com/tos-cn-i-0813c000-ce/osAp3iife0i7CeEmEtBArViCAshQwA3EgXSAIA~tplv-dy-resize-walign-adapt-aq:540:q75.webp?lk3s=138a59ce&x-expires=**********&x-signature=96spYzCOCsSntHiB76II4tqT%2Buc%3D&from=327834062&s=PackSourceEnum_DOUYIN_REFLOW&se=false&sc=cover&biz_tag=aweme_video&l=2025073009223664E224CBE232E412DCCA</thumburl><messageaction/><laninfo/><extinfo/><sourceusername/><sourcedisplayname/><appattach>    <totallen>0</totallen>    <attachid/>    <emoticonmd5/>    <fileext/>    <aeskey/></appattach><webviewshared>    <publisherId/>    <publisherReqId>0</publisherReqId></webviewshared><weappinfo>    <pagepath/>    <username/>    <appid/>    <appservicetype>0</appservicetype></weappinfo><websearch/></appmsg><fromusername>wxid_4usgcju5ey9q29</fromusername><scene>0</scene><appinfo>    <version>1</version>    <appname></appname></appinfo><commenturl/>
2025-07-30 09:22:25 | INFO | [DoubaoVideoSearch] 视频搜索处理完成，耗时: 8.31秒
2025-07-30 09:22:25 | DEBUG | 处理消息内容: '找视频 变装'
2025-07-30 09:22:25 | DEBUG | 消息内容 '找视频 变装' 不匹配任何命令，忽略
2025-07-30 09:22:31 | DEBUG | 收到消息: {'MsgId': 119319141, 'FromUserName': {'string': '48097389945@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 1, 'Content': {'string': 'wxid_ubbh6q832tcs21:\n找视频 变装'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1753838563, 'MsgSource': '<msgsource>\n\t<pua>1</pua>\n\t<eggIncluded>1</eggIncluded>\n\t<silence>0</silence>\n\t<membercount>73</membercount>\n\t<signature>N0_V1_nx01IYcm|v1_NSGgT6aS</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'PushContent': '郭 : 找视频 变装', 'NewMsgId': 741012381537586848, 'MsgSeq': 871410934}
2025-07-30 09:22:31 | INFO | 收到文本消息: 消息ID:119319141 来自:48097389945@chatroom 发送人:wxid_ubbh6q832tcs21 @:[] 内容:找视频 变装
2025-07-30 09:22:32 | INFO | [DoubaoVideoSearch] 收到视频搜索请求: 找视频 变装 from wxid_ubbh6q832tcs21
2025-07-30 09:22:32 | INFO | [DoubaoVideoSearch] 开始视频搜索，关键词: 变装
2025-07-30 09:22:37 | DEBUG | [DoubaoVideoSearch] API请求成功，开始处理响应流
2025-07-30 09:22:37 | INFO | [DoubaoVideoSearch] 视频搜索成功，结果类型: videos
2025-07-30 09:22:37 | INFO | [DoubaoVideoSearch] 收到18个视频结果
2025-07-30 09:22:37 | DEBUG | [DoubaoVideoSearch] 使用查询关键词进行去重: 变装
2025-07-30 09:22:37 | INFO | [DoubaoVideoSearch] 开始解析视频链接: https://m.douyin.com/share/video/7532068484772678958/?scene_from=douyin_h5_flow
2025-07-30 09:22:37 | DEBUG | [DoubaoVideoSearch] 开始解析抖音视频: https://m.douyin.com/share/video/7532068484772678958/?scene_from=douyin_h5_flow
2025-07-30 09:22:38 | INFO | [DoubaoVideoSearch] 视频解析成功: ai高级感睡裙试穿换装教程来了！睡衣ai特效入口教程来了#ai高级睡裙试穿 #ai换装 #一键换装特效 #变装 #即梦ai 睡衣ai特效入口 ai高级睡裙试穿特效 男扮女装换装的教程 ai换裙子特效 ai睡衣特效一键生成 ai换装视频 免费ai一键换装 换睡衣ai ai睡衣换装特效 by 子柒
2025-07-30 09:22:38 | INFO | [DoubaoVideoSearch] 视频解析成功: ai高级感睡裙试穿换装教程来了！睡衣ai特效入口教程来了#ai高级睡裙试穿 #ai换装 #一键换装特效 #变装 #即梦ai 睡衣ai特效入口 ai高级睡裙试穿特效 男扮女装换装的教程 ai换裙子特效 ai睡衣特效一键生成 ai换装视频 免费ai一键换装 换睡衣ai ai睡衣换装特效
2025-07-30 09:22:39 | INFO | 发送app消息: 对方wxid:48097389945@chatroom 类型:68 xml:<appmsg appid="wx75f04c8595ccb9f6" sdkver="0"><title>ai高级感睡裙试穿换装教程来了！睡衣ai特效入口教程来了#ai高级睡裙试穿 #ai换装 #一键换装特效 #变装 #即梦ai 睡衣ai特效入口 ai高级睡裙试穿特效 男扮女装换装的教程 ai换裙子特效 ai睡衣特效一键生成 ai换装视频 免费ai一键换装 换睡衣ai ai睡衣换装特效</title><des>作者: 子柒</des><action>view</action><type>68</type><showtype>0</showtype><content/><url>https://aweme.snssdk.com/aweme/v1/play/?video_id=v0d00fg10000d23kg67og65lspp77360&ratio=720p&line=0</url><dataurl/><lowurl>https://game.weixin.qq.com/</lowurl><lowdataurl/><recorditem/><thumburl>https://p11-sign.douyinpic.com/tos-cn-i-0813c001/o0SxoBPQiItIARMAPCCIiAaCjHGdAIZEQaBAw~tplv-dy-resize-walign-adapt-aq:540:q75.webp?lk3s=138a59ce&x-expires=**********&x-signature=Nwyait5Gr6l%2BPZqQbXJ%2FKV7AJK4%3D&from=327834062&s=PackSourceEnum_DOUYIN_REFLOW&se=false&sc=cover&biz_tag=aweme_video&l=20250730092250D823EEF1235B3706582C</thumburl><messageaction/><laninfo/><extinfo/><sourceusername/><sourcedisplayname/><appattach>    <totallen>0</totallen>    <attachid/>    <emoticonmd5/>    <fileext/>    <aeskey/></appattach><webviewshared>    <publisherId/>    <publisherReqId>0</publisherReqId></webviewshared><weappinfo>    <pagepath/>    <username/>    <appid/>    <appservicetype>0</appservicetype></weappinfo><websearch/></appmsg><fromusername>wxid_4usgcju5ey9q29</fromusername><scene>0</scene><appinfo>    <version>1</version>    <appname></appname></appinfo><commenturl/>
2025-07-30 09:22:39 | INFO | [DoubaoVideoSearch] 视频搜索处理完成，耗时: 6.51秒
2025-07-30 09:22:39 | DEBUG | 处理消息内容: '找视频 变装'
2025-07-30 09:22:39 | DEBUG | 消息内容 '找视频 变装' 不匹配任何命令，忽略
2025-07-30 09:23:02 | DEBUG | 收到消息: {'MsgId': 1675736135, 'FromUserName': {'string': '48097389945@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 1, 'Content': {'string': 'wxid_ubbh6q832tcs21:\n找视频 变装系列'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1753838594, 'MsgSource': '<msgsource>\n\t<pua>1</pua>\n\t<eggIncluded>1</eggIncluded>\n\t<silence>0</silence>\n\t<membercount>73</membercount>\n\t<signature>N0_V1_1UxEfJaQ|v1_kUkLkWzj</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'PushContent': '郭 : 找视频 变装系列', 'NewMsgId': 5306912302650844614, 'MsgSeq': 871410937}
2025-07-30 09:23:02 | INFO | 收到文本消息: 消息ID:1675736135 来自:48097389945@chatroom 发送人:wxid_ubbh6q832tcs21 @:[] 内容:找视频 变装系列
2025-07-30 09:23:03 | INFO | [DoubaoVideoSearch] 收到视频搜索请求: 找视频 变装系列 from wxid_ubbh6q832tcs21
2025-07-30 09:23:03 | INFO | [DoubaoVideoSearch] 开始视频搜索，关键词: 变装系列
2025-07-30 09:23:09 | DEBUG | [DoubaoVideoSearch] API请求成功，开始处理响应流
2025-07-30 09:23:09 | INFO | [DoubaoVideoSearch] 视频搜索成功，结果类型: videos
2025-07-30 09:23:09 | INFO | [DoubaoVideoSearch] 收到16个视频结果
2025-07-30 09:23:09 | DEBUG | [DoubaoVideoSearch] 使用查询关键词进行去重: 变装系列
2025-07-30 09:23:09 | INFO | [DoubaoVideoSearch] 开始解析视频链接: https://m.douyin.com/share/video/7530100770836041017/?scene_from=douyin_h5_flow
2025-07-30 09:23:09 | DEBUG | [DoubaoVideoSearch] 开始解析抖音视频: https://m.douyin.com/share/video/7530100770836041017/?scene_from=douyin_h5_flow
2025-07-30 09:23:10 | INFO | [DoubaoVideoSearch] 视频解析成功: Al一键换古装特效教程，AI一键古装试穿特效入口#即梦ai #ai换装古风 #古装变身特效 #一键免费换装古装特效 #一键拍同款古装  一键变装特效古装 by Anden
2025-07-30 09:23:10 | INFO | [DoubaoVideoSearch] 视频解析成功: Al一键换古装特效教程，AI一键古装试穿特效入口#即梦ai #ai换装古风 #古装变身特效 #一键免费换装古装特效 #一键拍同款古装  一键变装特效古装
2025-07-30 09:23:11 | INFO | 发送app消息: 对方wxid:48097389945@chatroom 类型:68 xml:<appmsg appid="wx75f04c8595ccb9f6" sdkver="0"><title>Al一键换古装特效教程，AI一键古装试穿特效入口#即梦ai #ai换装古风 #古装变身特效 #一键免费换装古装特效 #一键拍同款古装  一键变装特效古装</title><des>作者: Anden</des><action>view</action><type>68</type><showtype>0</showtype><content/><url>https://aweme.snssdk.com/aweme/v1/play/?video_id=v1e00fgi0000d204kvvog65j3mao8e60&ratio=720p&line=0</url><dataurl/><lowurl>https://game.weixin.qq.com/</lowurl><lowdataurl/><recorditem/><thumburl>https://p26-sign.douyinpic.com/tos-cn-i-0813c000-ce/osAp3iife0i7CeEmEtBArViCAshQwA3EgXSAIA~tplv-dy-resize-walign-adapt-aq:540:q75.webp?lk3s=138a59ce&x-expires=**********&x-signature=96spYzCOCsSntHiB76II4tqT%2Buc%3D&from=327834062&s=PackSourceEnum_DOUYIN_REFLOW&se=false&sc=cover&biz_tag=aweme_video&l=2025073009232299E541041B4D42022696</thumburl><messageaction/><laninfo/><extinfo/><sourceusername/><sourcedisplayname/><appattach>    <totallen>0</totallen>    <attachid/>    <emoticonmd5/>    <fileext/>    <aeskey/></appattach><webviewshared>    <publisherId/>    <publisherReqId>0</publisherReqId></webviewshared><weappinfo>    <pagepath/>    <username/>    <appid/>    <appservicetype>0</appservicetype></weappinfo><websearch/></appmsg><fromusername>wxid_4usgcju5ey9q29</fromusername><scene>0</scene><appinfo>    <version>1</version>    <appname></appname></appinfo><commenturl/>
2025-07-30 09:23:11 | INFO | [DoubaoVideoSearch] 视频搜索处理完成，耗时: 7.70秒
2025-07-30 09:23:11 | DEBUG | 处理消息内容: '找视频 变装系列'
2025-07-30 09:23:11 | DEBUG | 消息内容 '找视频 变装系列' 不匹配任何命令，忽略
2025-07-30 09:23:19 | DEBUG | 收到消息: {'MsgId': 1974194415, 'FromUserName': {'string': '48097389945@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 1, 'Content': {'string': 'wxid_ubbh6q832tcs21:\n找视频 变装系列'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1753838611, 'MsgSource': '<msgsource>\n\t<pua>1</pua>\n\t<eggIncluded>1</eggIncluded>\n\t<silence>0</silence>\n\t<membercount>73</membercount>\n\t<signature>N0_V1_JnfCo91G|v1_0rbCn+D0</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'PushContent': '郭 : 找视频 变装系列', 'NewMsgId': 7115505843136011833, 'MsgSeq': 871410940}
2025-07-30 09:23:19 | INFO | 收到文本消息: 消息ID:1974194415 来自:48097389945@chatroom 发送人:wxid_ubbh6q832tcs21 @:[] 内容:找视频 变装系列
2025-07-30 09:23:20 | INFO | [DoubaoVideoSearch] 收到视频搜索请求: 找视频 变装系列 from wxid_ubbh6q832tcs21
2025-07-30 09:23:20 | INFO | [DoubaoVideoSearch] 开始视频搜索，关键词: 变装系列
2025-07-30 09:23:27 | DEBUG | [DoubaoVideoSearch] API请求成功，开始处理响应流
2025-07-30 09:23:27 | INFO | [DoubaoVideoSearch] 视频搜索成功，结果类型: videos
2025-07-30 09:23:27 | INFO | [DoubaoVideoSearch] 收到16个视频结果
2025-07-30 09:23:27 | DEBUG | [DoubaoVideoSearch] 使用查询关键词进行去重: 变装系列
2025-07-30 09:23:27 | INFO | [DoubaoVideoSearch] 开始解析视频链接: https://m.douyin.com/share/video/7532068484772678958/?scene_from=douyin_h5_flow
2025-07-30 09:23:27 | DEBUG | [DoubaoVideoSearch] 开始解析抖音视频: https://m.douyin.com/share/video/7532068484772678958/?scene_from=douyin_h5_flow
2025-07-30 09:23:28 | INFO | [DoubaoVideoSearch] 视频解析成功: ai高级感睡裙试穿换装教程来了！睡衣ai特效入口教程来了#ai高级睡裙试穿 #ai换装 #一键换装特效 #变装 #即梦ai 睡衣ai特效入口 ai高级睡裙试穿特效 男扮女装换装的教程 ai换裙子特效 ai睡衣特效一键生成 ai换装视频 免费ai一键换装 换睡衣ai ai睡衣换装特效 by 子柒
2025-07-30 09:23:28 | INFO | [DoubaoVideoSearch] 视频解析成功: ai高级感睡裙试穿换装教程来了！睡衣ai特效入口教程来了#ai高级睡裙试穿 #ai换装 #一键换装特效 #变装 #即梦ai 睡衣ai特效入口 ai高级睡裙试穿特效 男扮女装换装的教程 ai换裙子特效 ai睡衣特效一键生成 ai换装视频 免费ai一键换装 换睡衣ai ai睡衣换装特效
2025-07-30 09:23:28 | INFO | 发送app消息: 对方wxid:48097389945@chatroom 类型:68 xml:<appmsg appid="wx75f04c8595ccb9f6" sdkver="0"><title>ai高级感睡裙试穿换装教程来了！睡衣ai特效入口教程来了#ai高级睡裙试穿 #ai换装 #一键换装特效 #变装 #即梦ai 睡衣ai特效入口 ai高级睡裙试穿特效 男扮女装换装的教程 ai换裙子特效 ai睡衣特效一键生成 ai换装视频 免费ai一键换装 换睡衣ai ai睡衣换装特效</title><des>作者: 子柒</des><action>view</action><type>68</type><showtype>0</showtype><content/><url>https://aweme.snssdk.com/aweme/v1/play/?video_id=v0d00fg10000d23kg67og65lspp77360&ratio=720p&line=0</url><dataurl/><lowurl>https://game.weixin.qq.com/</lowurl><lowdataurl/><recorditem/><thumburl>https://p11-sign.douyinpic.com/tos-cn-i-0813c001/o0SxoBPQiItIARMAPCCIiAaCjHGdAIZEQaBAw~tplv-dy-resize-walign-adapt-aq:540:q75.webp?lk3s=138a59ce&x-expires=**********&x-signature=Nwyait5Gr6l%2BPZqQbXJ%2FKV7AJK4%3D&from=327834062&s=PackSourceEnum_DOUYIN_REFLOW&se=false&sc=cover&biz_tag=aweme_video&l=2025073009234018A5642B2122FC040A7B</thumburl><messageaction/><laninfo/><extinfo/><sourceusername/><sourcedisplayname/><appattach>    <totallen>0</totallen>    <attachid/>    <emoticonmd5/>    <fileext/>    <aeskey/></appattach><webviewshared>    <publisherId/>    <publisherReqId>0</publisherReqId></webviewshared><weappinfo>    <pagepath/>    <username/>    <appid/>    <appservicetype>0</appservicetype></weappinfo><websearch/></appmsg><fromusername>wxid_4usgcju5ey9q29</fromusername><scene>0</scene><appinfo>    <version>1</version>    <appname></appname></appinfo><commenturl/>
2025-07-30 09:23:28 | INFO | [DoubaoVideoSearch] 视频搜索处理完成，耗时: 7.93秒
2025-07-30 09:23:28 | DEBUG | 处理消息内容: '找视频 变装系列'
2025-07-30 09:23:28 | DEBUG | 消息内容 '找视频 变装系列' 不匹配任何命令，忽略
2025-07-30 09:23:34 | DEBUG | 收到消息: {'MsgId': 1777124448, 'FromUserName': {'string': '48097389945@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 47, 'Content': {'string': 'wxid_ubbh6q832tcs21:\n<msg><emoji fromusername = "wxid_ubbh6q832tcs21" tousername = "48097389945@chatroom" type="2" idbuffer="media:0_0" md5="3c5d9173b23ecc025e1ec8c858142b57" len = "1005541" productid="" androidmd5="3c5d9173b23ecc025e1ec8c858142b57" androidlen="1005541" s60v3md5 = "3c5d9173b23ecc025e1ec8c858142b57" s60v3len="1005541" s60v5md5 = "3c5d9173b23ecc025e1ec8c858142b57" s60v5len="1005541" cdnurl = "http://vweixinf.tc.qq.com/110/20401/stodownload?m=3c5d9173b23ecc025e1ec8c858142b57&amp;filekey=30440201010430302e02016e040253480420336335643931373362323365636330323565316563386338353831343262353702030f57e5040d00000004627466730000000132&amp;hy=SH&amp;storeid=2683a773b00015f7c687175f00000006e01004fb153480fd65b01e02592370&amp;ef=1&amp;bizid=1022" designerid = "" thumburl = "" encrypturl = "http://vweixinf.tc.qq.com/110/20402/stodownload?m=4bab97cabbe1706f0a603389b3529eeb&amp;filekey=30440201010430302e02016e040253480420346261623937636162626531373036663061363033333839623335323965656202030f57f0040d00000004627466730000000132&amp;hy=SH&amp;storeid=2683a773b0003cd1f687175f00000006e02004fb253480fd65b01e02592390&amp;ef=2&amp;bizid=1022" aeskey= "0d82b033625c4bf18bc5c338aeb7333e" externurl = "http://vweixinf.tc.qq.com/110/20403/stodownload?m=0064acb5c83b81eef262bb4c27a223c8&amp;filekey=30440201010430302e02016e04025348042030303634616362356338336238316565663236326262346332376132323363380203020430040d00000004627466730000000132&amp;hy=SH&amp;storeid=2683a773b0005d930687175f00000006e03004fb353480fd65b01e025923ad&amp;ef=3&amp;bizid=1022" externmd5 = "ef10f433c759c4681e3dcaecf0b0a32d" width= "640" height= "640" tpurl= "" tpauthkey= "" attachedtext= "" attachedtextcolor= "" lensid= "" emojiattr= "" linkid= "" desc= "" ></emoji>  </msg>'}, 'Status': 3, 'ImgStatus': 2, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1753838626, 'MsgSource': '<msgsource>\n\t<silence>0</silence>\n\t<membercount>73</membercount>\n\t<signature>N0_V1_aORZ9Ug/|v1_MoZaZDC3</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'PushContent': '郭在群聊中发了一个表情', 'NewMsgId': 7865306487831606292, 'MsgSeq': 871410943}
2025-07-30 09:23:34 | INFO | 收到表情消息: 消息ID:1777124448 来自:48097389945@chatroom 发送人:wxid_ubbh6q832tcs21 MD5:3c5d9173b23ecc025e1ec8c858142b57 大小:1005541
2025-07-30 09:23:35 | DEBUG | [DoubaoImageRecognition] 缓存表情消息: 7865306487831606292
2025-07-30 09:23:42 | DEBUG | 收到消息: {'MsgId': 1416263800, 'FromUserName': {'string': '48097389945@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 1, 'Content': {'string': 'wxid_ubbh6q832tcs21:\n找视频 包臀裙'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1753838634, 'MsgSource': '<msgsource>\n\t<pua>1</pua>\n\t<eggIncluded>1</eggIncluded>\n\t<silence>0</silence>\n\t<membercount>73</membercount>\n\t<signature>N0_V1_5gx77+2E|v1_Z5X7nFdF</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'PushContent': '郭 : 找视频 包臀裙', 'NewMsgId': 2220269265459841533, 'MsgSeq': 871410944}
2025-07-30 09:23:42 | INFO | 收到文本消息: 消息ID:1416263800 来自:48097389945@chatroom 发送人:wxid_ubbh6q832tcs21 @:[] 内容:找视频 包臀裙
2025-07-30 09:23:43 | INFO | [DoubaoVideoSearch] 收到视频搜索请求: 找视频 包臀裙 from wxid_ubbh6q832tcs21
2025-07-30 09:23:43 | INFO | [DoubaoVideoSearch] 开始视频搜索，关键词: 包臀裙
2025-07-30 09:23:50 | DEBUG | [DoubaoVideoSearch] API请求成功，开始处理响应流
2025-07-30 09:23:50 | INFO | [DoubaoVideoSearch] 视频搜索成功，结果类型: videos
2025-07-30 09:23:50 | INFO | [DoubaoVideoSearch] 收到21个视频结果
2025-07-30 09:23:50 | DEBUG | [DoubaoVideoSearch] 使用查询关键词进行去重: 包臀裙
2025-07-30 09:23:50 | INFO | [DoubaoVideoSearch] 开始解析视频链接: https://m.douyin.com/share/video/7522610089636875566/?scene_from=douyin_h5_flow
2025-07-30 09:23:50 | DEBUG | [DoubaoVideoSearch] 开始解析抖音视频: https://m.douyin.com/share/video/7522610089636875566/?scene_from=douyin_h5_flow
2025-07-30 09:23:51 | INFO | [DoubaoVideoSearch] 视频解析成功: 全网穿包臀裙最好看的少妇 #女神 #包臀裙 #美女 by 孟德带你看世界
2025-07-30 09:23:51 | INFO | [DoubaoVideoSearch] 视频解析成功: 全网穿包臀裙最好看的少妇 #女神 #包臀裙 #美女
2025-07-30 09:23:51 | INFO | 发送app消息: 对方wxid:48097389945@chatroom 类型:68 xml:<appmsg appid="wx75f04c8595ccb9f6" sdkver="0"><title>全网穿包臀裙最好看的少妇 #女神 #包臀裙 #美女</title><des>作者: 孟德带你看世界</des><action>view</action><type>68</type><showtype>0</showtype><content/><url>https://aweme.snssdk.com/aweme/v1/play/?video_id=v0200fg10000d1iqru7og65rcpgqd9ng&ratio=720p&line=0</url><dataurl/><lowurl>https://game.weixin.qq.com/</lowurl><lowdataurl/><recorditem/><thumburl>https://p3-sign.douyinpic.com/tos-cn-p-0015/o0irICPPtgpEGhAeerM0ZAibv6TBmhgAAm7tlB~tplv-dy-resize-walign-adapt-aq:540:q75.webp?lk3s=138a59ce&x-expires=**********&x-signature=EOKdIP3mhVvuT4nNnAbYKjyS6ds%3D&from=327834062&s=PackSourceEnum_DOUYIN_REFLOW&se=false&sc=cover&biz_tag=aweme_video&l=202507300924030A8BB6B67187CBF90F69</thumburl><messageaction/><laninfo/><extinfo/><sourceusername/><sourcedisplayname/><appattach>    <totallen>0</totallen>    <attachid/>    <emoticonmd5/>    <fileext/>    <aeskey/></appattach><webviewshared>    <publisherId/>    <publisherReqId>0</publisherReqId></webviewshared><weappinfo>    <pagepath/>    <username/>    <appid/>    <appservicetype>0</appservicetype></weappinfo><websearch/></appmsg><fromusername>wxid_4usgcju5ey9q29</fromusername><scene>0</scene><appinfo>    <version>1</version>    <appname></appname></appinfo><commenturl/>
2025-07-30 09:23:51 | INFO | [DoubaoVideoSearch] 视频搜索处理完成，耗时: 8.42秒
2025-07-30 09:23:51 | DEBUG | 处理消息内容: '找视频 包臀裙'
2025-07-30 09:23:51 | DEBUG | 消息内容 '找视频 包臀裙' 不匹配任何命令，忽略
2025-07-30 09:24:38 | DEBUG | 收到消息: {'MsgId': 1437518957, 'FromUserName': {'string': '48097389945@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 1, 'Content': {'string': 'wxid_ubbh6q832tcs21:\n找视频 少妇'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1753838690, 'MsgSource': '<msgsource>\n\t<pua>1</pua>\n\t<eggIncluded>1</eggIncluded>\n\t<silence>0</silence>\n\t<membercount>73</membercount>\n\t<signature>N0_V1_h0JtTU5o|v1_X9ObV1H2</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'PushContent': '郭 : 找视频 少妇', 'NewMsgId': 141452425591094912, 'MsgSeq': 871410947}
2025-07-30 09:24:38 | INFO | 收到文本消息: 消息ID:1437518957 来自:48097389945@chatroom 发送人:wxid_ubbh6q832tcs21 @:[] 内容:找视频 少妇
2025-07-30 09:24:39 | INFO | [DoubaoVideoSearch] 收到视频搜索请求: 找视频 少妇 from wxid_ubbh6q832tcs21
2025-07-30 09:24:39 | INFO | [DoubaoVideoSearch] 开始视频搜索，关键词: 少妇
2025-07-30 09:24:45 | DEBUG | [DoubaoVideoSearch] API请求成功，开始处理响应流
2025-07-30 09:24:45 | INFO | [DoubaoVideoSearch] 视频搜索成功，结果类型: text
2025-07-30 09:24:45 | INFO | 发送文字消息: 对方wxid:48097389945@chatroom at: 内容:出问题了
2025-07-30 09:24:45 | INFO | [DoubaoVideoSearch] 视频搜索处理完成，耗时: 6.79秒
2025-07-30 09:24:45 | DEBUG | 处理消息内容: '找视频 少妇'
2025-07-30 09:24:45 | DEBUG | 消息内容 '找视频 少妇' 不匹配任何命令，忽略
2025-07-30 09:24:51 | DEBUG | 收到消息: {'MsgId': 923573292, 'FromUserName': {'string': '48097389945@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 47, 'Content': {'string': 'wxid_ubbh6q832tcs21:\n<msg><emoji fromusername = "wxid_ubbh6q832tcs21" tousername = "48097389945@chatroom" type="2" idbuffer="media:0_0" md5="5a9d46343c33710624e07b9e17579da6" len = "1288544" productid="" androidmd5="5a9d46343c33710624e07b9e17579da6" androidlen="1288544" s60v3md5 = "5a9d46343c33710624e07b9e17579da6" s60v3len="1288544" s60v5md5 = "5a9d46343c33710624e07b9e17579da6" s60v5len="1288544" cdnurl = "http://vweixinf.tc.qq.com/110/20401/stodownload?m=5a9d46343c33710624e07b9e17579da6&amp;filekey=30440201010430302e02016e0402534804203561396434363334336333333731303632346530376239653137353739646136020313a960040d00000004627466730000000132&amp;hy=SH&amp;storeid=2677f74570002f31a50005f510000006e01004fb153480cc3f03156972eaba&amp;ef=1&amp;bizid=1022" designerid = "" thumburl = "" encrypturl = "http://vweixinf.tc.qq.com/110/20402/stodownload?m=d4af9e8376e6ffb443ffee614cb4a716&amp;filekey=30440201010430302e02016e0402534804206434616639653833373665366666623434336666656536313463623461373136020313a970040d00000004627466730000000132&amp;hy=SH&amp;storeid=2677f745700051d7950005f510000006e02004fb253480cc3f03156972eac2&amp;ef=2&amp;bizid=1022" aeskey= "687a3368f84d45e0b15e040bd6a43fbb" externurl = "http://vweixinf.tc.qq.com/110/20403/stodownload?m=bf2deb71537c1d75afc30e7bf731c442&amp;filekey=30440201010430302e02016e04025348042062663264656237313533376331643735616663333065376266373331633434320203076a80040d00000004627466730000000132&amp;hy=SH&amp;storeid=2677f74570007327850005f510000006e03004fb353480cc3f03156972ead5&amp;ef=3&amp;bizid=1022" externmd5 = "e01ff482b90f6569aad197aafbd8b835" width= "240" height= "312" tpurl= "" tpauthkey= "" attachedtext= "" attachedtextcolor= "" lensid= "" emojiattr= "" linkid= "" desc= "" ></emoji>  </msg>'}, 'Status': 3, 'ImgStatus': 2, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1753838702, 'MsgSource': '<msgsource>\n\t<silence>0</silence>\n\t<membercount>73</membercount>\n\t<signature>N0_V1_ECr0x29p|v1_PMX0yS5v</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'PushContent': '郭在群聊中发了一个表情', 'NewMsgId': 6754314335667097862, 'MsgSeq': 871410950}
2025-07-30 09:24:51 | INFO | 收到表情消息: 消息ID:923573292 来自:48097389945@chatroom 发送人:wxid_ubbh6q832tcs21 MD5:5a9d46343c33710624e07b9e17579da6 大小:1288544
2025-07-30 09:24:51 | DEBUG | [DoubaoImageRecognition] 缓存表情消息: 6754314335667097862
2025-07-30 09:25:03 | DEBUG | 收到消息: {'MsgId': 1140468884, 'FromUserName': {'string': '47325400669@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 1, 'Content': {'string': 'flyhunterl:\nClaude的公益站 https://b4u.qzz.io/register?aff=Oh0W 没注册的朋友用这个注册 送100刀'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1753838714, 'MsgSource': '<msgsource>\n\t<sec_msg_node>\n\t\t<alnode>\n\t\t\t<fr>1</fr>\n\t\t</alnode>\n\t</sec_msg_node>\n\t<pua>1</pua>\n\t<silence>0</silence>\n\t<membercount>222</membercount>\n\t<signature>N0_V1_4rbQVyN4|v1_rQapvHKo</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'PushContent': 'flynn : Claude的公益站 https://b4u.qzz.io/register?aff=Oh0W 没注册的朋友用这个注册 ...', 'NewMsgId': 467273718484037522, 'MsgSeq': 871410951}
2025-07-30 09:25:03 | INFO | 收到文本消息: 消息ID:1140468884 来自:47325400669@chatroom 发送人:flyhunterl @:[] 内容:Claude的公益站 https://b4u.qzz.io/register?aff=Oh0W 没注册的朋友用这个注册 送100刀
2025-07-30 09:25:03 | DEBUG | 处理消息内容: 'Claude的公益站 https://b4u.qzz.io/register?aff=Oh0W 没注册的朋友用这个注册 送100刀'
2025-07-30 09:25:03 | DEBUG | 消息内容 'Claude的公益站 https://b4u.qzz.io/register?aff=Oh0W 没注册的朋友用这个注册 送100刀' 不匹配任何命令，忽略
2025-07-30 09:25:14 | DEBUG | 收到消息: {'MsgId': 99085876, 'FromUserName': {'string': '48097389945@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 1, 'Content': {'string': 'wxid_ubbh6q832tcs21:\n找视频 豹纹系列'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1753838726, 'MsgSource': '<msgsource>\n\t<pua>1</pua>\n\t<eggIncluded>1</eggIncluded>\n\t<silence>0</silence>\n\t<membercount>73</membercount>\n\t<signature>N0_V1_4VekIigx|v1_MATgnlhN</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'PushContent': '郭 : 找视频 豹纹系列', 'NewMsgId': 5632858236049845151, 'MsgSeq': 871410952}
2025-07-30 09:25:14 | INFO | 收到文本消息: 消息ID:99085876 来自:48097389945@chatroom 发送人:wxid_ubbh6q832tcs21 @:[] 内容:找视频 豹纹系列
2025-07-30 09:25:15 | INFO | [DoubaoVideoSearch] 收到视频搜索请求: 找视频 豹纹系列 from wxid_ubbh6q832tcs21
2025-07-30 09:25:15 | INFO | [DoubaoVideoSearch] 开始视频搜索，关键词: 豹纹系列
2025-07-30 09:25:21 | DEBUG | [DoubaoVideoSearch] API请求成功，开始处理响应流
2025-07-30 09:25:21 | INFO | [DoubaoVideoSearch] 视频搜索成功，结果类型: videos
2025-07-30 09:25:21 | INFO | [DoubaoVideoSearch] 收到18个视频结果
2025-07-30 09:25:21 | DEBUG | [DoubaoVideoSearch] 使用查询关键词进行去重: 豹纹系列
2025-07-30 09:25:21 | INFO | [DoubaoVideoSearch] 开始解析视频链接: https://m.douyin.com/share/video/7444867142442372410/?scene_from=douyin_h5_flow
2025-07-30 09:25:21 | DEBUG | [DoubaoVideoSearch] 开始解析抖音视频: https://m.douyin.com/share/video/7444867142442372410/?scene_from=douyin_h5_flow
2025-07-30 09:25:22 | INFO | [DoubaoVideoSearch] 视频解析成功: 美女穿搭惊艳全场，时尚与性感完美融合😍 豹纹吊带出场，喜欢的扣1 #丝姬 #大长腿 #御姐 #美出高级感 #豹纹 by 康康
2025-07-30 09:25:22 | INFO | [DoubaoVideoSearch] 视频解析成功: 美女穿搭惊艳全场，时尚与性感完美融合😍 豹纹吊带出场，喜欢的扣1 #丝姬 #大长腿 #御姐 #美出高级感 #豹纹
2025-07-30 09:25:23 | INFO | 发送app消息: 对方wxid:48097389945@chatroom 类型:68 xml:<appmsg appid="wx75f04c8595ccb9f6" sdkver="0"><title>美女穿搭惊艳全场，时尚与性感完美融合😍 豹纹吊带出场，喜欢的扣1 #丝姬 #大长腿 #御姐 #美出高级感 #豹纹</title><des>作者: 康康</des><action>view</action><type>68</type><showtype>0</showtype><content/><url>https://aweme.snssdk.com/aweme/v1/play/?video_id=v1e00fgi0000ct8nkonog65uh525gb10&ratio=720p&line=0</url><dataurl/><lowurl>https://game.weixin.qq.com/</lowurl><lowdataurl/><recorditem/><thumburl>https://p3-sign.douyinpic.com/tos-cn-p-0015c000-ce/o0CWoiEf9nEkCeEwCiDiwpFXU0qUwA9IEMeHQA~tplv-dy-resize-walign-adapt-aq:540:q75.webp?lk3s=138a59ce&x-expires=**********&x-signature=JN7F3nuX2cZcmM%2FhNuzEWAwSvq4%3D&from=327834062&s=PackSourceEnum_DOUYIN_REFLOW&se=false&sc=cover&biz_tag=aweme_video&l=202507300925343BDFEFBBC713620007E3</thumburl><messageaction/><laninfo/><extinfo/><sourceusername/><sourcedisplayname/><appattach>    <totallen>0</totallen>    <attachid/>    <emoticonmd5/>    <fileext/>    <aeskey/></appattach><webviewshared>    <publisherId/>    <publisherReqId>0</publisherReqId></webviewshared><weappinfo>    <pagepath/>    <username/>    <appid/>    <appservicetype>0</appservicetype></weappinfo><websearch/></appmsg><fromusername>wxid_4usgcju5ey9q29</fromusername><scene>0</scene><appinfo>    <version>1</version>    <appname></appname></appinfo><commenturl/>
2025-07-30 09:25:23 | INFO | [DoubaoVideoSearch] 视频搜索处理完成，耗时: 8.03秒
2025-07-30 09:25:23 | DEBUG | 处理消息内容: '找视频 豹纹系列'
2025-07-30 09:25:23 | DEBUG | 消息内容 '找视频 豹纹系列' 不匹配任何命令，忽略
2025-07-30 09:26:47 | DEBUG | 收到消息: {'MsgId': 133658910, 'FromUserName': {'string': '47325400669@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 1, 'Content': {'string': 'wxid_ubbh6q832tcs21:\n找视频 双倍快乐系列'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1753838818, 'MsgSource': '<msgsource>\n\t<pua>1</pua>\n\t<eggIncluded>1</eggIncluded>\n\t<silence>0</silence>\n\t<membercount>222</membercount>\n\t<signature>N0_V1_KgNcyhMW|v1_+BJoZzDt</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'PushContent': '郭 : 找视频 双倍快乐系列', 'NewMsgId': 4722600856300001964, 'MsgSeq': 871410955}
2025-07-30 09:26:47 | INFO | 收到文本消息: 消息ID:133658910 来自:47325400669@chatroom 发送人:wxid_ubbh6q832tcs21 @:[] 内容:找视频 双倍快乐系列
2025-07-30 09:26:47 | INFO | [DoubaoVideoSearch] 收到视频搜索请求: 找视频 双倍快乐系列 from wxid_ubbh6q832tcs21
2025-07-30 09:26:47 | INFO | [DoubaoVideoSearch] 开始视频搜索，关键词: 双倍快乐系列
2025-07-30 09:26:54 | DEBUG | [DoubaoVideoSearch] API请求成功，开始处理响应流
2025-07-30 09:26:54 | INFO | [DoubaoVideoSearch] 视频搜索成功，结果类型: videos
2025-07-30 09:26:54 | INFO | [DoubaoVideoSearch] 收到19个视频结果
2025-07-30 09:26:54 | DEBUG | [DoubaoVideoSearch] 使用查询关键词进行去重: 双倍快乐系列
2025-07-30 09:26:54 | INFO | [DoubaoVideoSearch] 开始解析视频链接: https://m.douyin.com/share/video/7529635567169178939/?scene_from=douyin_h5_flow
2025-07-30 09:26:54 | DEBUG | [DoubaoVideoSearch] 开始解析抖音视频: https://m.douyin.com/share/video/7529635567169178939/?scene_from=douyin_h5_flow
2025-07-30 09:26:55 | INFO | [DoubaoVideoSearch] 视频解析成功: 你的双C女仆#感觉至上 #双倍快乐 by 小兔崽子
2025-07-30 09:26:55 | INFO | [DoubaoVideoSearch] 视频解析成功: 你的双C女仆#感觉至上 #双倍快乐
2025-07-30 09:26:56 | INFO | 发送app消息: 对方wxid:47325400669@chatroom 类型:68 xml:<appmsg appid="wx75f04c8595ccb9f6" sdkver="0"><title>你的双C女仆#感觉至上 #双倍快乐</title><des>作者: 小兔崽子</des><action>view</action><type>68</type><showtype>0</showtype><content/><url>https://aweme.snssdk.com/aweme/v1/play/?video_id=v1e00fgi0000d1va6inog65tjqlp0r20&ratio=720p&line=0</url><dataurl/><lowurl>https://game.weixin.qq.com/</lowurl><lowdataurl/><recorditem/><thumburl>https://p11-sign.douyinpic.com/tos-cn-i-0813c000-ce/ogjeymOEA97HAQJIYEFQAoDDgq8jAJM7fqfBYo~tplv-dy-resize-walign-adapt-aq:540:q75.webp?lk3s=138a59ce&x-expires=**********&x-signature=xsFyZoOH%2BRPb8SmdEossrxr4q%2BU%3D&from=327834062&s=PackSourceEnum_DOUYIN_REFLOW&se=false&sc=cover&biz_tag=aweme_video&l=202507300927079E1BFFFEC2D4DCE4B32A</thumburl><messageaction/><laninfo/><extinfo/><sourceusername/><sourcedisplayname/><appattach>    <totallen>0</totallen>    <attachid/>    <emoticonmd5/>    <fileext/>    <aeskey/></appattach><webviewshared>    <publisherId/>    <publisherReqId>0</publisherReqId></webviewshared><weappinfo>    <pagepath/>    <username/>    <appid/>    <appservicetype>0</appservicetype></weappinfo><websearch/></appmsg><fromusername>wxid_4usgcju5ey9q29</fromusername><scene>0</scene><appinfo>    <version>1</version>    <appname></appname></appinfo><commenturl/>
2025-07-30 09:26:56 | INFO | [DoubaoVideoSearch] 视频搜索处理完成，耗时: 9.03秒
2025-07-30 09:26:56 | DEBUG | 处理消息内容: '找视频 双倍快乐系列'
2025-07-30 09:26:56 | DEBUG | 消息内容 '找视频 双倍快乐系列' 不匹配任何命令，忽略
2025-07-30 09:26:56 | DEBUG | 收到消息: {'MsgId': 256111352, 'FromUserName': {'string': '48097389945@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 1, 'Content': {'string': 'wxid_jegyk4i3v7zg22:\n起床了[太阳]'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1753838823, 'MsgSource': '<msgsource>\n\t<bizflag>0</bizflag>\n\t<pua>1</pua>\n\t<eggIncluded>1</eggIncluded>\n\t<silence>0</silence>\n\t<membercount>73</membercount>\n\t<signature>N0_V1_peNuv+et|v1_MNv2llyg</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'PushContent': '阿尼亚与她 : 起床了[太阳]', 'NewMsgId': 6726420290800562864, 'MsgSeq': 871410956}
2025-07-30 09:26:56 | INFO | 收到文本消息: 消息ID:256111352 来自:48097389945@chatroom 发送人:wxid_jegyk4i3v7zg22 @:[] 内容:起床了[太阳]
2025-07-30 09:26:57 | DEBUG | 处理消息内容: '起床了[太阳]'
2025-07-30 09:26:57 | DEBUG | 消息内容 '起床了[太阳]' 不匹配任何命令，忽略
2025-07-30 09:26:59 | DEBUG | 收到消息: {'MsgId': 1817092212, 'FromUserName': {'string': '48097389945@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 47, 'Content': {'string': 'wxid_jegyk4i3v7zg22:\n<msg><emoji fromusername="wxid_jegyk4i3v7zg22" tousername="48097389945@chatroom" type="2" idbuffer="media:0_0" md5="6bf139b37c4fc9a1ae052bedf4bdc8db" len="1496415" productid="" androidmd5="6bf139b37c4fc9a1ae052bedf4bdc8db" androidlen="1496415" s60v3md5="6bf139b37c4fc9a1ae052bedf4bdc8db" s60v3len="1496415" s60v5md5="6bf139b37c4fc9a1ae052bedf4bdc8db" s60v5len="1496415" cdnurl="http://vweixinf.tc.qq.com/110/20401/stodownload?m=6bf139b37c4fc9a1ae052bedf4bdc8db&amp;filekey=30440201010430302e02016e0402535a04203662663133396233376334666339613161653035326265646634626463386462020316d55f040d00000004627466730000000132&amp;hy=SZ&amp;storeid=26826d4cc0000e2358df7c9dd0000006e01004fb1535a138f101157f23f6f7&amp;ef=1&amp;bizid=1022" designerid="" thumburl="" encrypturl="http://vweixinf.tc.qq.com/110/20402/stodownload?m=ce92703d21cab4950a12d1be3697156a&amp;filekey=30440201010430302e02016e0402535a04206365393237303364323163616234393530613132643162653336393731353661020316d560040d00000004627466730000000132&amp;hy=SZ&amp;storeid=26826d4cc000295c38df7c9dd0000006e02004fb2535a138f101157f23f714&amp;ef=2&amp;bizid=1022" aeskey="1b40d97d58cd43bea2309b7b815674cb" externurl="http://vweixinf.tc.qq.com/110/20403/stodownload?m=29edd20435bc8e1eb6e8e9764a2ce68b&amp;filekey=30440201010430302e02016e0402535a04203239656464323034333562633865316562366538653937363461326365363862020302b980040d00000004627466730000000132&amp;hy=SZ&amp;storeid=26826d4cc0004a3e58df7c9dd0000006e03004fb3535a138f101157f23f739&amp;ef=3&amp;bizid=1022" externmd5="7535fb135c9c83cc15722363b629f712" width="640" height="640" tpurl="" tpauthkey="" attachedtext="" attachedtextcolor="" lensid="" emojiattr="" linkid="" desc=""></emoji><gameext type="0" content="0"></gameext><extcommoninfo></extcommoninfo></msg>'}, 'Status': 3, 'ImgStatus': 2, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1753838826, 'MsgSource': '<msgsource>\n\t<silence>0</silence>\n\t<membercount>73</membercount>\n\t<signature>N0_V1_f3VQYfed|v1_iZ/58ddU</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'PushContent': '阿尼亚与她在群聊中发了一个表情', 'NewMsgId': 3896914754292011848, 'MsgSeq': 871410957}
2025-07-30 09:26:59 | INFO | 收到表情消息: 消息ID:1817092212 来自:48097389945@chatroom 发送人:wxid_jegyk4i3v7zg22 MD5:6bf139b37c4fc9a1ae052bedf4bdc8db 大小:1496415
2025-07-30 09:27:00 | DEBUG | [DoubaoImageRecognition] 缓存表情消息: 3896914754292011848
2025-07-30 09:27:17 | DEBUG | 收到消息: {'MsgId': 1432798741, 'FromUserName': {'string': '47325400669@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 1, 'Content': {'string': 'wxid_ubbh6q832tcs21:\n找视频 双倍快乐系列'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1753838849, 'MsgSource': '<msgsource>\n\t<pua>1</pua>\n\t<alnode>\n\t\t<cf>4</cf>\n\t\t<inlenlist>10</inlenlist>\n\t</alnode>\n\t<eggIncluded>1</eggIncluded>\n\t<silence>0</silence>\n\t<membercount>222</membercount>\n\t<signature>N0_V1_KfeMwulH|v1_MwB6EkrZ</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'PushContent': '郭 : 找视频 双倍快乐系列', 'NewMsgId': 4642396784254100388, 'MsgSeq': 871410960}
2025-07-30 09:27:17 | INFO | 收到文本消息: 消息ID:1432798741 来自:47325400669@chatroom 发送人:wxid_ubbh6q832tcs21 @:[] 内容:找视频 双倍快乐系列
2025-07-30 09:27:17 | INFO | [DoubaoVideoSearch] 收到视频搜索请求: 找视频 双倍快乐系列 from wxid_ubbh6q832tcs21
2025-07-30 09:27:17 | INFO | [DoubaoVideoSearch] 开始视频搜索，关键词: 双倍快乐系列
2025-07-30 09:27:24 | DEBUG | [DoubaoVideoSearch] API请求成功，开始处理响应流
2025-07-30 09:27:24 | INFO | [DoubaoVideoSearch] 视频搜索成功，结果类型: videos
2025-07-30 09:27:24 | INFO | [DoubaoVideoSearch] 收到19个视频结果
2025-07-30 09:27:24 | DEBUG | [DoubaoVideoSearch] 使用查询关键词进行去重: 双倍快乐系列
2025-07-30 09:27:24 | INFO | [DoubaoVideoSearch] 开始解析视频链接: https://m.douyin.com/share/video/7508351341421530426/?scene_from=douyin_h5_flow
2025-07-30 09:27:24 | DEBUG | [DoubaoVideoSearch] 开始解析抖音视频: https://m.douyin.com/share/video/7508351341421530426/?scene_from=douyin_h5_flow
2025-07-30 09:27:25 | INFO | [DoubaoVideoSearch] 视频解析成功: 姐妹成双 快乐加倍 #你要怎么抵抗我 #双倍快乐 by 卢小熙
2025-07-30 09:27:25 | INFO | [DoubaoVideoSearch] 视频解析成功: 姐妹成双 快乐加倍 #你要怎么抵抗我 #双倍快乐
2025-07-30 09:27:25 | INFO | 发送app消息: 对方wxid:47325400669@chatroom 类型:68 xml:<appmsg appid="wx75f04c8595ccb9f6" sdkver="0"><title>姐妹成双 快乐加倍 #你要怎么抵抗我 #双倍快乐</title><des>作者: 卢小熙</des><action>view</action><type>68</type><showtype>0</showtype><content/><url>https://aweme.snssdk.com/aweme/v1/play/?video_id=v1e00fgi0000d0pgavnog65p3rk542n0&ratio=720p&line=0</url><dataurl/><lowurl>https://game.weixin.qq.com/</lowurl><lowdataurl/><recorditem/><thumburl>https://p3-sign.douyinpic.com/tos-cn-i-0813c000-ce/o8AibBbcE80AehGE8zUBfEmeLiAiAAYwAPy0Ii~tplv-dy-resize-walign-adapt-aq:540:q75.webp?lk3s=138a59ce&x-expires=**********&x-signature=tS2Q7m5Y6r20kR%2BUnoEi4PAqq8A%3D&from=327834062&s=PackSourceEnum_DOUYIN_REFLOW&se=false&sc=cover&biz_tag=aweme_video&l=20250730092737C6C6911CA2171D0C2BA2</thumburl><messageaction/><laninfo/><extinfo/><sourceusername/><sourcedisplayname/><appattach>    <totallen>0</totallen>    <attachid/>    <emoticonmd5/>    <fileext/>    <aeskey/></appattach><webviewshared>    <publisherId/>    <publisherReqId>0</publisherReqId></webviewshared><weappinfo>    <pagepath/>    <username/>    <appid/>    <appservicetype>0</appservicetype></weappinfo><websearch/></appmsg><fromusername>wxid_4usgcju5ey9q29</fromusername><scene>0</scene><appinfo>    <version>1</version>    <appname></appname></appinfo><commenturl/>
2025-07-30 09:27:25 | INFO | [DoubaoVideoSearch] 视频搜索处理完成，耗时: 7.81秒
2025-07-30 09:27:25 | DEBUG | 处理消息内容: '找视频 双倍快乐系列'
2025-07-30 09:27:25 | DEBUG | 消息内容 '找视频 双倍快乐系列' 不匹配任何命令，忽略
2025-07-30 09:27:50 | DEBUG | 收到消息: {'MsgId': 2108078466, 'FromUserName': {'string': '48097389945@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 1, 'Content': {'string': 'wxid_ubbh6q832tcs21:\n[旺柴]'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1753838882, 'MsgSource': '<msgsource>\n\t<pua>1</pua>\n\t<eggIncluded>1</eggIncluded>\n\t<silence>0</silence>\n\t<membercount>73</membercount>\n\t<signature>N0_V1_seoTcSfJ|v1_tDvbzgTV</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'PushContent': '郭 : [旺柴]', 'NewMsgId': 2248509211085751442, 'MsgSeq': 871410963}
2025-07-30 09:27:50 | INFO | 收到表情消息: 消息ID:2108078466 来自:48097389945@chatroom 发送人:wxid_ubbh6q832tcs21 @:[] 内容:[旺柴]
2025-07-30 09:27:50 | DEBUG | [DoubaoImageRecognition] 缓存表情消息: 2248509211085751442
2025-07-30 09:32:26 | DEBUG | 收到消息: {'MsgId': 962063168, 'FromUserName': {'string': '55878994168@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 49, 'Content': {'string': 'wxid_ubbh6q832tcs21:\n<?xml version="1.0"?>\n<msg>\n\t<appmsg appid="" sdkver="0">\n\t\t<title>总结一下</title>\n\t\t<des />\n\t\t<username />\n\t\t<action>view</action>\n\t\t<type>57</type>\n\t\t<showtype>0</showtype>\n\t\t<content />\n\t\t<url />\n\t\t<lowurl />\n\t\t<forwardflag>0</forwardflag>\n\t\t<dataurl />\n\t\t<lowdataurl />\n\t\t<contentattr>0</contentattr>\n\t\t<streamvideo>\n\t\t\t<streamvideourl />\n\t\t\t<streamvideototaltime>0</streamvideototaltime>\n\t\t\t<streamvideotitle />\n\t\t\t<streamvideowording />\n\t\t\t<streamvideoweburl />\n\t\t\t<streamvideothumburl />\n\t\t\t<streamvideoaduxinfo />\n\t\t\t<streamvideopublishid />\n\t\t</streamvideo>\n\t\t<canvasPageItem>\n\t\t\t<canvasPageXml><![CDATA[]]></canvasPageXml>\n\t\t</canvasPageItem>\n\t\t<refermsg>\n\t\t\t<type>49</type>\n\t\t\t<svrid>6920378186069523961</svrid>\n\t\t\t<fromusr>55878994168@chatroom</fromusr>\n\t\t\t<chatusr>wxid_4usgcju5ey9q29</chatusr>\n\t\t\t<displayname>瑶瑶</displayname>\n\t\t\t<msgsource>&lt;msgsource&gt;\n\t&lt;bizflag&gt;0&lt;/bizflag&gt;\n\t&lt;sec_msg_node&gt;\n\t\t&lt;uuid&gt;05d9d7b6ebb33344efec26829c753d42_&lt;/uuid&gt;\n\t\t&lt;risk-file-flag /&gt;\n\t\t&lt;risk-file-md5-list /&gt;\n\t&lt;/sec_msg_node&gt;\n\t&lt;silence&gt;1&lt;/silence&gt;\n\t&lt;membercount&gt;3&lt;/membercount&gt;\n\t&lt;signature&gt;N0_V1_v0w1d829|v1_XQNBX7+5&lt;/signature&gt;\n\t&lt;tmp_node&gt;\n\t\t&lt;publisher-id&gt;&lt;/publisher-id&gt;\n\t&lt;/tmp_node&gt;\n&lt;/msgsource&gt;\n</msgsource>\n\t\t\t<content>&lt;msg&gt;&lt;appmsg sdkver="1"&gt;&lt;title&gt;福利&amp;#x20;|&amp;#x20;薅策划羊毛养活唱宝的第一天！六周年福利主题站开启&lt;/title&gt;&lt;des&gt;呼叫唱宝！呼叫唱宝！这里有一份遗落的钻石💎请选择是否拾取？&lt;/des&gt;&lt;type&gt;5&lt;/type&gt;&lt;url&gt;http://mp.weixin.qq.com/s?__biz=MzU5NTA5OTMxMA==&amp;amp;mid=2247526699&amp;amp;idx=1&amp;amp;sn=57487c776cc8c442cee0f90ee479e4e1&amp;amp;chksm=ffbbd21f3302405abd5d0de3ed545e58376a3155b3b702fd36ecaaab0f1badbb3b0fad26ead1&amp;amp;scene=0&amp;amp;xtrack=1#rd&lt;/url&gt;&lt;sourceusername&gt;gh_7057516c9e71&lt;/sourceusername&gt;&lt;sourcedisplayname&gt;唱舞全明星&lt;/sourcedisplayname&gt;&lt;thumburl&gt;https://mmbiz.qpic.cn/mmbiz_jpg/RicuJvxibRtUAdsULuhGBmLHDXr7xdBVkPD14Sc2fERalroCnnhVgze7EvJHbEEYfMKGUbpib4QhHpYQA7qpUrwFw/640?wxtype=jpeg&amp;amp;wxfrom=0&lt;/thumburl&gt;&lt;mmreadershare&gt;&lt;itemshowtype&gt;0&lt;/itemshowtype&gt;&lt;showsourceinfo&gt;0&lt;/showsourceinfo&gt;&lt;/mmreadershare&gt;&lt;/appmsg&gt;&lt;fromusername&gt;wxid_4usgcju5ey9q29&lt;/fromusername&gt;&lt;appinfo&gt;&lt;version&gt;1&lt;/version&gt;&lt;/appinfo&gt;&lt;/msg&gt;</content>\n\t\t\t<strid />\n\t\t\t<createtime>1753445191</createtime>\n\t\t</refermsg>\n\t\t<appattach>\n\t\t\t<totallen>0</totallen>\n\t\t\t<attachid />\n\t\t\t<cdnattachurl />\n\t\t\t<emoticonmd5 />\n\t\t\t<aeskey />\n\t\t\t<fileext />\n\t\t\t<islargefilemsg>0</islargefilemsg>\n\t\t</appattach>\n\t\t<extinfo />\n\t\t<androidsource>0</androidsource>\n\t\t<thumburl />\n\t\t<mediatagname />\n\t\t<messageaction><![CDATA[]]></messageaction>\n\t\t<messageext><![CDATA[]]></messageext>\n\t\t<emoticongift>\n\t\t\t<packageflag>0</packageflag>\n\t\t\t<packageid />\n\t\t</emoticongift>\n\t\t<emoticonshared>\n\t\t\t<packageflag>0</packageflag>\n\t\t\t<packageid />\n\t\t</emoticonshared>\n\t\t<designershared>\n\t\t\t<designeruin>0</designeruin>\n\t\t\t<designername>null</designername>\n\t\t\t<designerrediretcturl><![CDATA[null]]></designerrediretcturl>\n\t\t</designershared>\n\t\t<emotionpageshared>\n\t\t\t<tid>0</tid>\n\t\t\t<title>null</title>\n\t\t\t<desc>null</desc>\n\t\t\t<iconUrl><![CDATA[null]]></iconUrl>\n\t\t\t<secondUrl>null</secondUrl>\n\t\t\t<pageType>0</pageType>\n\t\t\t<setKey>null</setKey>\n\t\t</emotionpageshared>\n\t\t<webviewshared>\n\t\t\t<shareUrlOriginal />\n\t\t\t<shareUrlOpen />\n\t\t\t<jsAppId />\n\t\t\t<publisherId />\n\t\t\t<publisherReqId />\n\t\t</webviewshared>\n\t\t<template_id />\n\t\t<md5 />\n\t\t<websearch>\n\t\t\t<rec_category>0</rec_category>\n\t\t\t<channelId>0</channelId>\n\t\t</websearch>\n\t\t<weappinfo>\n\t\t\t<username />\n\t\t\t<appid />\n\t\t\t<appservicetype>0</appservicetype>\n\t\t\t<secflagforsinglepagemode>0</secflagforsinglepagemode>\n\t\t\t<videopageinfo>\n\t\t\t\t<thumbwidth>0</thumbwidth>\n\t\t\t\t<thumbheight>0</thumbheight>\n\t\t\t\t<fromopensdk>0</fromopensdk>\n\t\t\t</videopageinfo>\n\t\t</weappinfo>\n\t\t<statextstr />\n\t\t<musicShareItem>\n\t\t\t<musicDuration>0</musicDuration>\n\t\t</musicShareItem>\n\t\t<finderLiveProductShare>\n\t\t\t<finderLiveID><![CDATA[]]></finderLiveID>\n\t\t\t<finderUsername><![CDATA[]]></finderUsername>\n\t\t\t<finderObjectID><![CDATA[]]></finderObjectID>\n\t\t\t<finderNonceID><![CDATA[]]></finderNonceID>\n\t\t\t<liveStatus><![CDATA[]]></liveStatus>\n\t\t\t<appId><![CDATA[]]></appId>\n\t\t\t<pagePath><![CDATA[]]></pagePath>\n\t\t\t<productId><![CDATA[]]></productId>\n\t\t\t<coverUrl><![CDATA[]]></coverUrl>\n\t\t\t<productTitle><![CDATA[]]></productTitle>\n\t\t\t<marketPrice><![CDATA[0]]></marketPrice>\n\t\t\t<sellingPrice><![CDATA[0]]></sellingPrice>\n\t\t\t<platformHeadImg><![CDATA[]]></platformHeadImg>\n\t\t\t<platformName><![CDATA[]]></platformName>\n\t\t\t<shopWindowId><![CDATA[]]></shopWindowId>\n\t\t\t<flashSalePrice><![CDATA[0]]></flashSalePrice>\n\t\t\t<flashSaleEndTime><![CDATA[0]]></flashSaleEndTime>\n\t\t\t<ecSource><![CDATA[]]></ecSource>\n\t\t\t<sellingPriceWording><![CDATA[]]></sellingPriceWording>\n\t\t\t<platformIconURL><![CDATA[]]></platformIconURL>\n\t\t\t<firstProductTagURL><![CDATA[]]></firstProductTagURL>\n\t\t\t<firstProductTagAspectRatioString><![CDATA[0.0]]></firstProductTagAspectRatioString>\n\t\t\t<secondProductTagURL><![CDATA[]]></secondProductTagURL>\n\t\t\t<secondProductTagAspectRatioString><![CDATA[0.0]]></secondProductTagAspectRatioString>\n\t\t\t<firstGuaranteeWording><![CDATA[]]></firstGuaranteeWording>\n\t\t\t<secondGuaranteeWording><![CDATA[]]></secondGuaranteeWording>\n\t\t\t<thirdGuaranteeWording><![CDATA[]]></thirdGuaranteeWording>\n\t\t\t<isPriceBeginShow>false</isPriceBeginShow>\n\t\t\t<lastGMsgID><![CDATA[]]></lastGMsgID>\n\t\t\t<promoterKey><![CDATA[]]></promoterKey>\n\t\t\t<discountWording><![CDATA[]]></discountWording>\n\t\t\t<priceSuffixDescription><![CDATA[]]></priceSuffixDescription>\n\t\t\t<productCardKey><![CDATA[]]></productCardKey>\n\t\t\t<isWxShop><![CDATA[]]></isWxShop>\n\t\t\t<brandIconUrl><![CDATA[]]></brandIconUrl>\n\t\t\t<showBoxItemStringList />\n\t\t</finderLiveProductShare>\n\t\t<finderOrder>\n\t\t\t<appID><![CDATA[]]></appID>\n\t\t\t<orderID><![CDATA[]]></orderID>\n\t\t\t<path><![CDATA[]]></path>\n\t\t\t<priceWording><![CDATA[]]></priceWording>\n\t\t\t<stateWording><![CDATA[]]></stateWording>\n\t\t\t<productImageURL><![CDATA[]]></productImageURL>\n\t\t\t<products><![CDATA[]]></products>\n\t\t\t<productsCount><![CDATA[0]]></productsCount>\n\t\t\t<orderType><![CDATA[0]]></orderType>\n\t\t\t<newPriceWording><![CDATA[]]></newPriceWording>\n\t\t\t<newStateWording><![CDATA[]]></newStateWording>\n\t\t\t<useNewWording><![CDATA[0]]></useNewWording>\n\t\t</finderOrder>\n\t\t<finderShopWindowShare>\n\t\t\t<finderUsername><![CDATA[]]></finderUsername>\n\t\t\t<avatar><![CDATA[]]></avatar>\n\t\t\t<nickname><![CDATA[]]></nickname>\n\t\t\t<commodityInStockCount><![CDATA[]]></commodityInStockCount>\n\t\t\t<appId><![CDATA[]]></appId>\n\t\t\t<path><![CDATA[]]></path>\n\t\t\t<appUsername><![CDATA[]]></appUsername>\n\t\t\t<query><![CDATA[]]></query>\n\t\t\t<liteAppId><![CDATA[]]></liteAppId>\n\t\t\t<liteAppPath><![CDATA[]]></liteAppPath>\n\t\t\t<liteAppQuery><![CDATA[]]></liteAppQuery>\n\t\t\t<platformTagURL><![CDATA[]]></platformTagURL>\n\t\t\t<saleWording><![CDATA[]]></saleWording>\n\t\t\t<lastGMsgID><![CDATA[]]></lastGMsgID>\n\t\t\t<profileTypeWording><![CDATA[]]></profileTypeWording>\n\t\t\t<saleWordingExtra><![CDATA[]]></saleWordingExtra>\n\t\t\t<isWxShop><![CDATA[]]></isWxShop>\n\t\t\t<platformIconUrl><![CDATA[]]></platformIconUrl>\n\t\t\t<brandIconUrl><![CDATA[]]></brandIconUrl>\n\t\t\t<description><![CDATA[]]></description>\n\t\t\t<backgroundUrl><![CDATA[]]></backgroundUrl>\n\t\t\t<darkModePlatformIconUrl><![CDATA[]]></darkModePlatformIconUrl>\n\t\t\t<reputationInfo>\n\t\t\t\t<hasReputationInfo>0</hasReputationInfo>\n\t\t\t\t<reputationScore>0</reputationScore>\n\t\t\t\t<reputationWording />\n\t\t\t\t<reputationTextColor />\n\t\t\t\t<reputationLevelWording />\n\t\t\t\t<reputationBackgroundColor />\n\t\t\t</reputationInfo>\n\t\t\t<productImageURLList />\n\t\t</finderShopWindowShare>\n\t\t<findernamecard>\n\t\t\t<username />\n\t\t\t<avatar><![CDATA[]]></avatar>\n\t\t\t<nickname />\n\t\t\t<auth_job />\n\t\t\t<auth_icon>0</auth_icon>\n\t\t\t<auth_icon_url />\n\t\t\t<ecSource><![CDATA[]]></ecSource>\n\t\t\t<lastGMsgID><![CDATA[]]></lastGMsgID>\n\t\t</findernamecard>\n\t\t<finderGuarantee>\n\t\t\t<scene><![CDATA[0]]></scene>\n\t\t</finderGuarantee>\n\t\t<directshare>0</directshare>\n\t\t<gamecenter>\n\t\t\t<namecard>\n\t\t\t\t<iconUrl />\n\t\t\t\t<name />\n\t\t\t\t<desc />\n\t\t\t\t<tail />\n\t\t\t\t<jumpUrl />\n\t\t\t</namecard>\n\t\t</gamecenter>\n\t\t<patMsg>\n\t\t\t<chatUser />\n\t\t\t<records>\n\t\t\t\t<recordNum>0</recordNum>\n\t\t\t</records>\n\t\t</patMsg>\n\t\t<secretmsg>\n\t\t\t<issecretmsg>0</issecretmsg>\n\t\t</secretmsg>\n\t\t<referfromscene>0</referfromscene>\n\t\t<gameshare>\n\t\t\t<liteappext>\n\t\t\t\t<liteappbizdata />\n\t\t\t\t<priority>0</priority>\n\t\t\t</liteappext>\n\t\t\t<appbrandext>\n\t\t\t\t<litegameinfo />\n\t\t\t\t<priority>-1</priority>\n\t\t\t</appbrandext>\n\t\t\t<gameshareid />\n\t\t\t<sharedata />\n\t\t\t<isvideo>0</isvideo>\n\t\t\t<duration>-1</duration>\n\t\t\t<isexposed>0</isexposed>\n\t\t\t<readtext />\n\t\t</gameshare>\n\t\t<mpsharetrace>\n\t\t\t<hasfinderelement>0</hasfinderelement>\n\t\t\t<lastgmsgid />\n\t\t</mpsharetrace>\n\t\t<wxgamecard>\n\t\t\t<framesetname />\n\t\t\t<mbcarddata />\n\t\t\t<minpkgversion />\n\t\t\t<clientextinfo />\n\t\t\t<mbcardheight>0</mbcardheight>\n\t\t\t<isoldversion>0</isoldversion>\n\t\t</wxgamecard>\n\t\t<liteapp>\n\t\t\t<id>null</id>\n\t\t\t<path />\n\t\t\t<query />\n\t\t\t<istransparent>0</istransparent>\n\t\t\t<hideicon>0</hideicon>\n\t\t</liteapp>\n\t\t<opensdk_share_is_modified>0</opensdk_share_is_modified>\n\t</appmsg>\n\t<fromusername>wxid_ubbh6q832tcs21</fromusername>\n\t<scene>0</scene>\n\t<appinfo>\n\t\t<version>1</version>\n\t\t<appname></appname>\n\t</appinfo>\n\t<commenturl></commenturl>\n</msg>\n'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1753839158, 'MsgSource': '<msgsource>\n\t<alnode>\n\t\t<fr>4</fr>\n\t</alnode>\n\t<eggIncluded>1</eggIncluded>\n\t<sec_msg_node>\n\t\t<uuid>05d9d7b6ebb33344efec26829c753d42_</uuid>\n\t\t<risk-file-flag />\n\t\t<risk-file-md5-list />\n\t</sec_msg_node>\n\t<silence>0</silence>\n\t<membercount>3</membercount>\n\t<signature>N0_V1_wOSbTQWH|v1_jfb9vEE5</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'PushContent': '郭 : 总结一下', 'NewMsgId': 7048464420045726473, 'MsgSeq': 871410964}
2025-07-30 09:32:26 | DEBUG | 从群聊消息中提取发送者: wxid_ubbh6q832tcs21
2025-07-30 09:32:26 | DEBUG | 使用已解析的XML处理引用消息
2025-07-30 09:32:26 | INFO | 收到引用消息: 消息ID:962063168 来自:55878994168@chatroom 发送人:wxid_ubbh6q832tcs21 内容:总结一下 引用类型:49
2025-07-30 09:32:26 | INFO | [DouBaoImageToImage] 收到引用消息: 总结一下
2025-07-30 09:32:26 | INFO | [TimerTask] 收到引用消息调试信息:
2025-07-30 09:32:26 | INFO |   - 消息内容: 总结一下
2025-07-30 09:32:26 | INFO |   - 群组ID: 55878994168@chatroom
2025-07-30 09:32:26 | INFO |   - 发送人: wxid_ubbh6q832tcs21
2025-07-30 09:32:26 | INFO |   - 引用信息: {'MsgType': 49, 'Content': '<msg><appmsg sdkver="1"><title>福利&#x20;|&#x20;薅策划羊毛养活唱宝的第一天！六周年福利主题站开启</title><des>呼叫唱宝！呼叫唱宝！这里有一份遗落的钻石💎请选择是否拾取？</des><type>5</type><url>http://mp.weixin.qq.com/s?__biz=MzU5NTA5OTMxMA==&amp;mid=2247526699&amp;idx=1&amp;sn=57487c776cc8c442cee0f90ee479e4e1&amp;chksm=ffbbd21f3302405abd5d0de3ed545e58376a3155b3b702fd36ecaaab0f1badbb3b0fad26ead1&amp;scene=0&amp;xtrack=1#rd</url><sourceusername>gh_7057516c9e71</sourceusername><sourcedisplayname>唱舞全明星</sourcedisplayname><thumburl>https://mmbiz.qpic.cn/mmbiz_jpg/RicuJvxibRtUAdsULuhGBmLHDXr7xdBVkPD14Sc2fERalroCnnhVgze7EvJHbEEYfMKGUbpib4QhHpYQA7qpUrwFw/640?wxtype=jpeg&amp;wxfrom=0</thumburl><mmreadershare><itemshowtype>0</itemshowtype><showsourceinfo>0</showsourceinfo></mmreadershare></appmsg><fromusername>wxid_4usgcju5ey9q29</fromusername><appinfo><version>1</version></appinfo></msg>', 'Msgid': '6920378186069523961', 'NewMsgId': '6920378186069523961', 'ToWxid': 'wxid_4usgcju5ey9q29', 'FromWxid': '55878994168@chatroom', 'Nickname': '瑶瑶', 'MsgSource': '<msgsource>\n\t<bizflag>0</bizflag>\n\t<sec_msg_node>\n\t\t<uuid>05d9d7b6ebb33344efec26829c753d42_</uuid>\n\t\t<risk-file-flag />\n\t\t<risk-file-md5-list />\n\t</sec_msg_node>\n\t<silence>1</silence>\n\t<membercount>3</membercount>\n\t<signature>N0_V1_v0w1d829|v1_XQNBX7+5</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'Createtime': '1753445191', 'SenderWxid': 'wxid_ubbh6q832tcs21'}
2025-07-30 09:32:26 | INFO |   - 引用消息ID: 
2025-07-30 09:32:26 | INFO |   - 引用消息类型: 
2025-07-30 09:32:26 | INFO |   - 引用消息内容: <msg><appmsg sdkver="1"><title>福利&#x20;|&#x20;薅策划羊毛养活唱宝的第一天！六周年福利主题站开启</title><des>呼叫唱宝！呼叫唱宝！这里有一份遗落的钻石💎请选择是否拾取？</des><type>5</type><url>http://mp.weixin.qq.com/s?__biz=MzU5NTA5OTMxMA==&amp;mid=2247526699&amp;idx=1&amp;sn=57487c776cc8c442cee0f90ee479e4e1&amp;chksm=ffbbd21f3302405abd5d0de3ed545e58376a3155b3b702fd36ecaaab0f1badbb3b0fad26ead1&amp;scene=0&amp;xtrack=1#rd</url><sourceusername>gh_7057516c9e71</sourceusername><sourcedisplayname>唱舞全明星</sourcedisplayname><thumburl>https://mmbiz.qpic.cn/mmbiz_jpg/RicuJvxibRtUAdsULuhGBmLHDXr7xdBVkPD14Sc2fERalroCnnhVgze7EvJHbEEYfMKGUbpib4QhHpYQA7qpUrwFw/640?wxtype=jpeg&amp;wxfrom=0</thumburl><mmreadershare><itemshowtype>0</itemshowtype><showsourceinfo>0</showsourceinfo></mmreadershare></appmsg><fromusername>wxid_4usgcju5ey9q29</fromusername><appinfo><version>1</version></appinfo></msg>
2025-07-30 09:32:26 | INFO |   - 引用消息发送人: wxid_ubbh6q832tcs21
2025-07-30 09:32:26 | DEBUG | [VideoParser] 处理引用消息命令[总结一下]: {'MsgType': 49, 'Content': '<msg><appmsg sdkver="1"><title>福利&#x20;|&#x20;薅策划羊毛养活唱宝的第一天！六周年福利主题站开启</title><des>呼叫唱宝！呼叫唱宝！这里有一份遗落的钻石💎请选择是否拾取？</des><type>5</type><url>http://mp.weixin.qq.com/s?__biz=MzU5NTA5OTMxMA==&amp;mid=2247526699&amp;idx=1&amp;sn=57487c776cc8c442cee0f90ee479e4e1&amp;chksm=ffbbd21f3302405abd5d0de3ed545e58376a3155b3b702fd36ecaaab0f1badbb3b0fad26ead1&amp;scene=0&amp;xtrack=1#rd</url><sourceusername>gh_7057516c9e71</sourceusername><sourcedisplayname>唱舞全明星</sourcedisplayname><thumburl>https://mmbiz.qpic.cn/mmbiz_jpg/RicuJvxibRtUAdsULuhGBmLHDXr7xdBVkPD14Sc2fERalroCnnhVgze7EvJHbEEYfMKGUbpib4QhHpYQA7qpUrwFw/640?wxtype=jpeg&amp;wxfrom=0</thumburl><mmreadershare><itemshowtype>0</itemshowtype><showsourceinfo>0</showsourceinfo></mmreadershare></appmsg><fromusername>wxid_4usgcju5ey9q29</fromusername><appinfo><version>1</version></appinfo></msg>', 'Msgid': '6920378186069523961', 'NewMsgId': '6920378186069523961', 'ToWxid': 'wxid_4usgcju5ey9q29', 'FromWxid': '55878994168@chatroom', 'Nickname': '瑶瑶', 'MsgSource': '<msgsource>\n\t<bizflag>0</bizflag>\n\t<sec_msg_node>\n\t\t<uuid>05d9d7b6ebb33344efec26829c753d42_</uuid>\n\t\t<risk-file-flag />\n\t\t<risk-file-md5-list />\n\t</sec_msg_node>\n\t<silence>1</silence>\n\t<membercount>3</membercount>\n\t<signature>N0_V1_v0w1d829|v1_XQNBX7+5</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'Createtime': '1753445191', 'SenderWxid': 'wxid_ubbh6q832tcs21'}
2025-07-30 09:33:43 | INFO | 发送图片消息: 对方wxid:55878994168@chatroom 图片base64略
2025-07-30 09:33:43 | DEBUG | 收到消息: {'MsgId': 499573955, 'FromUserName': {'string': '48097389945@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 47, 'Content': {'string': 'wxid_wlnzvr8ivgd422:\n<msg><emoji fromusername = "wxid_wlnzvr8ivgd422" tousername = "48097389945@chatroom" type="2" idbuffer="media:0_0" md5="1af3e0f6b79ca9f09dd632fe21d35e39" len = "298285" productid="" androidmd5="1af3e0f6b79ca9f09dd632fe21d35e39" androidlen="298285" s60v3md5 = "1af3e0f6b79ca9f09dd632fe21d35e39" s60v3len="298285" s60v5md5 = "1af3e0f6b79ca9f09dd632fe21d35e39" s60v5len="298285" cdnurl = "http://vweixinf.tc.qq.com/110/20401/stodownload?m=1af3e0f6b79ca9f09dd632fe21d35e39&amp;filekey=30440201010430302e02016e04025348042031616633653066366237396361396630396464363332666532316433356533390203048d2d040d00000004627466730000000132&amp;hy=SH&amp;storeid=267d3f2e6000ac3c650b1a01c0000006e01004fb15348258f31715737dbe4c&amp;ef=1&amp;bizid=1022" designerid = "" thumburl = "" encrypturl = "http://vweixinf.tc.qq.com/110/20402/stodownload?m=5d3305731f754042a98ddb1138ffe982&amp;filekey=30440201010430302e02016e04025348042035643333303537333166373534303432613938646462313133386666653938320203048d30040d00000004627466730000000132&amp;hy=SH&amp;storeid=267d3f2e6000bdda150b1a01c0000006e02004fb25348258f31715737dbe67&amp;ef=2&amp;bizid=1022" aeskey= "d75882876af84311b003a486e3769a11" externurl = "http://vweixinf.tc.qq.com/110/20403/stodownload?m=278ab8e907ad639084160d2e576dbeec&amp;filekey=30440201010430302e02016e0402534804203237386162386539303761643633393038343136306432653537366462656563020300b4e0040d00000004627466730000000132&amp;hy=SH&amp;storeid=267d3f2e6000d2a2750b1a01c0000006e03004fb35348258f31715737dbe7b&amp;ef=3&amp;bizid=1022" externmd5 = "f730aeab514f4c8168d9b61ea58d7220" width= "396" height= "396" tpurl= "" tpauthkey= "" attachedtext= "" attachedtextcolor= "" lensid= "" emojiattr= "" linkid= "" desc= "" ></emoji>  </msg>'}, 'Status': 3, 'ImgStatus': 2, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1753839166, 'MsgSource': '<msgsource>\n\t<silence>0</silence>\n\t<membercount>73</membercount>\n\t<signature>N0_V1_8eISJwSe|v1_7pmfm85f</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'PushContent': '锦岚在群聊中发了一个表情', 'NewMsgId': 7542137348848384538, 'MsgSeq': 871410965}
2025-07-30 09:33:43 | INFO | 收到表情消息: 消息ID:499573955 来自:48097389945@chatroom 发送人:wxid_wlnzvr8ivgd422 MD5:1af3e0f6b79ca9f09dd632fe21d35e39 大小:298285
2025-07-30 09:33:44 | DEBUG | [DoubaoImageRecognition] 缓存表情消息: 7542137348848384538
2025-07-30 09:33:44 | DEBUG | 收到消息: {'MsgId': 1732745282, 'FromUserName': {'string': '48097389945@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 47, 'Content': {'string': 'wxid_wlnzvr8ivgd422:\n<msg><emoji fromusername = "wxid_wlnzvr8ivgd422" tousername = "48097389945@chatroom" type="2" idbuffer="media:0_0" md5="21c07f7359a8379f987a9968d63c9558" len = "505389" productid="" androidmd5="21c07f7359a8379f987a9968d63c9558" androidlen="505389" s60v3md5 = "21c07f7359a8379f987a9968d63c9558" s60v3len="505389" s60v5md5 = "21c07f7359a8379f987a9968d63c9558" s60v5len="505389" cdnurl = "http://vweixinf.tc.qq.com/110/20401/stodownload?m=21c07f7359a8379f987a9968d63c9558&amp;filekey=30440201010430302e02016e0402534804203231633037663733353961383337396639383761393936386436336339353538020307b62d040d00000004627466730000000132&amp;hy=SH&amp;storeid=267f3e93b000e0f1d4dfca1a00000006e01004fb15348255e11b15057dea29&amp;ef=1&amp;bizid=1022" designerid = "" thumburl = "" encrypturl = "http://vweixinf.tc.qq.com/110/20402/stodownload?m=825915b12167b72e3532d5dcc2730a3a&amp;filekey=30440201010430302e02016e0402534804203832353931356231323136376237326533353332643564636332373330613361020307b630040d00000004627466730000000132&amp;hy=SH&amp;storeid=267f3e93b000f30254dfca1a00000006e02004fb25348255e11b15057dea39&amp;ef=2&amp;bizid=1022" aeskey= "b41d8bdd4d9b4a0e8fcf2f33f6367963" externurl = "http://vweixinf.tc.qq.com/110/20403/stodownload?m=a2250ef67e5574177befb4ac0e6e067c&amp;filekey=30440201010430302e02016e04025348042061323235306566363765353537343137376265666234616330653665303637630203019310040d00000004627466730000000132&amp;hy=SH&amp;storeid=267f3e93c0001238e4dfca1a00000006e03004fb35348255e11b15057dea50&amp;ef=3&amp;bizid=1022" externmd5 = "d8e1a9b7374a6a2bf1ac9c0ee0f2f16e" width= "313" height= "313" tpurl= "" tpauthkey= "" attachedtext= "" attachedtextcolor= "" lensid= "" emojiattr= "" linkid= "" desc= "" ></emoji>  </msg>'}, 'Status': 3, 'ImgStatus': 2, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1753839172, 'MsgSource': '<msgsource>\n\t<silence>0</silence>\n\t<membercount>73</membercount>\n\t<signature>N0_V1_dXiS2J77|v1_s4JW0Kd5</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'PushContent': '锦岚在群聊中发了一个表情', 'NewMsgId': 7001521311629145832, 'MsgSeq': 871410966}
2025-07-30 09:33:44 | INFO | 收到表情消息: 消息ID:1732745282 来自:48097389945@chatroom 发送人:wxid_wlnzvr8ivgd422 MD5:21c07f7359a8379f987a9968d63c9558 大小:505389
2025-07-30 09:33:45 | DEBUG | [DoubaoImageRecognition] 缓存表情消息: 7001521311629145832
2025-07-30 09:33:45 | DEBUG | 收到消息: {'MsgId': 295505825, 'FromUserName': {'string': '47325400669@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 49, 'Content': {'string': 'wxid_62fiham2pn7521:\n<?xml version="1.0"?>\n<msg>\n\t<appmsg appid="" sdkver="0">\n\t\t<title>豆包 保持人物一致性，把图片人物衣服换成适合海边沙滩游玩的衣服</title>\n\t\t<des />\n\t\t<username />\n\t\t<action>view</action>\n\t\t<type>57</type>\n\t\t<showtype>0</showtype>\n\t\t<content />\n\t\t<url />\n\t\t<lowurl />\n\t\t<forwardflag>0</forwardflag>\n\t\t<dataurl />\n\t\t<lowdataurl />\n\t\t<contentattr>0</contentattr>\n\t\t<streamvideo>\n\t\t\t<streamvideourl />\n\t\t\t<streamvideototaltime>0</streamvideototaltime>\n\t\t\t<streamvideotitle />\n\t\t\t<streamvideowording />\n\t\t\t<streamvideoweburl />\n\t\t\t<streamvideothumburl />\n\t\t\t<streamvideoaduxinfo />\n\t\t\t<streamvideopublishid />\n\t\t</streamvideo>\n\t\t<canvasPageItem>\n\t\t\t<canvasPageXml><![CDATA[]]></canvasPageXml>\n\t\t</canvasPageItem>\n\t\t<refermsg>\n\t\t\t<type>3</type>\n\t\t\t<svrid>714496648952914170</svrid>\n\t\t\t<fromusr>47325400669@chatroom</fromusr>\n\t\t\t<chatusr>wxid_fh84okl6f5wp22</chatusr>\n\t\t\t<displayname>阿猪米德</displayname>\n\t\t\t<msgsource>&lt;msgsource&gt;\n\t&lt;sec_msg_node&gt;\n\t\t&lt;uuid&gt;a0edf93ac0f5bba3630cc1b4ccfca261_&lt;/uuid&gt;\n\t\t&lt;risk-file-flag /&gt;\n\t\t&lt;risk-file-md5-list /&gt;\n\t&lt;/sec_msg_node&gt;\n\t&lt;imgmsg_pd cdnmidimgurl_size="60450" cdnmidimgurl_pd_pri="50" cdnmidimgurl_pd="1" /&gt;\n\t&lt;silence&gt;1&lt;/silence&gt;\n\t&lt;membercount&gt;222&lt;/membercount&gt;\n\t&lt;NotAutoDownloadRange&gt;20:00-22:00;00:00-01:00&lt;/NotAutoDownloadRange&gt;\n\t&lt;signature&gt;N0_V1_CiuSS/Ep|v1_kzN/Ad9Q&lt;/signature&gt;\n\t&lt;tmp_node&gt;\n\t\t&lt;publisher-id&gt;&lt;/publisher-id&gt;\n\t&lt;/tmp_node&gt;\n&lt;/msgsource&gt;\n</msgsource>\n\t\t\t<content>&lt;?xml version="1.0"?&gt;\n&lt;msg&gt;\n\t&lt;img aeskey="64706f757372756a6f74647173736f78" encryver="0" cdnthumbaeskey="64706f757372756a6f74647173736f78" cdnthumburl="3057020100044b30490201000204d6e1bab602033d14ba02048f06ff9d020468896c8d042437366439383062612d326338372d343761362d613865652d3762313337373565373862310204052828010201000405004c543e0054381738" cdnthumblength="3258" cdnthumbheight="100" cdnthumbwidth="66" cdnmidheight="0" cdnmidwidth="0" cdnhdheight="0" cdnhdwidth="0" cdnmidimgurl="3057020100044b30490201000204d6e1bab602033d14ba02048f06ff9d020468896c8d042437366439383062612d326338372d343761362d613865652d3762313337373565373862310204052828010201000405004c543e0054381738" length="60450" cdnbigimgurl="3057020100044b30490201000204d6e1bab602033d14ba02048f06ff9d020468896c8d042437366439383062612d326338372d343761362d613865652d3762313337373565373862310204052828010201000405004c543e0054381738" hdlength="196225" md5="4f2ea21d2d7ef077f433b2504011b6df"&gt;\n\t\t&lt;secHashInfoBase64 /&gt;\n\t\t&lt;live&gt;\n\t\t\t&lt;duration&gt;0&lt;/duration&gt;\n\t\t\t&lt;size&gt;0&lt;/size&gt;\n\t\t\t&lt;md5 /&gt;\n\t\t\t&lt;fileid /&gt;\n\t\t\t&lt;hdsize&gt;0&lt;/hdsize&gt;\n\t\t\t&lt;hdmd5 /&gt;\n\t\t\t&lt;hdfileid /&gt;\n\t\t\t&lt;stillimagetimems&gt;0&lt;/stillimagetimems&gt;\n\t\t&lt;/live&gt;\n\t&lt;/img&gt;\n\t&lt;platform_signature /&gt;\n\t&lt;imgdatahash /&gt;\n\t&lt;ImgSourceInfo&gt;\n\t\t&lt;ImgSourceUrl /&gt;\n\t\t&lt;BizType&gt;0&lt;/BizType&gt;\n\t&lt;/ImgSourceInfo&gt;\n&lt;/msg&gt;\n</content>\n\t\t\t<strid />\n\t\t\t<createtime>1753836684</createtime>\n\t\t</refermsg>\n\t\t<appattach>\n\t\t\t<totallen>0</totallen>\n\t\t\t<attachid />\n\t\t\t<cdnattachurl />\n\t\t\t<emoticonmd5 />\n\t\t\t<aeskey />\n\t\t\t<fileext />\n\t\t\t<islargefilemsg>0</islargefilemsg>\n\t\t</appattach>\n\t\t<extinfo />\n\t\t<androidsource>0</androidsource>\n\t\t<thumburl />\n\t\t<mediatagname />\n\t\t<messageaction><![CDATA[]]></messageaction>\n\t\t<messageext><![CDATA[]]></messageext>\n\t\t<emoticongift>\n\t\t\t<packageflag>0</packageflag>\n\t\t\t<packageid />\n\t\t</emoticongift>\n\t\t<emoticonshared>\n\t\t\t<packageflag>0</packageflag>\n\t\t\t<packageid />\n\t\t</emoticonshared>\n\t\t<designershared>\n\t\t\t<designeruin>0</designeruin>\n\t\t\t<designername>null</designername>\n\t\t\t<designerrediretcturl><![CDATA[null]]></designerrediretcturl>\n\t\t</designershared>\n\t\t<emotionpageshared>\n\t\t\t<tid>0</tid>\n\t\t\t<title>null</title>\n\t\t\t<desc>null</desc>\n\t\t\t<iconUrl><![CDATA[null]]></iconUrl>\n\t\t\t<secondUrl>null</secondUrl>\n\t\t\t<pageType>0</pageType>\n\t\t\t<setKey>null</setKey>\n\t\t</emotionpageshared>\n\t\t<webviewshared>\n\t\t\t<shareUrlOriginal />\n\t\t\t<shareUrlOpen />\n\t\t\t<jsAppId />\n\t\t\t<publisherId />\n\t\t\t<publisherReqId />\n\t\t</webviewshared>\n\t\t<template_id />\n\t\t<md5 />\n\t\t<websearch>\n\t\t\t<rec_category>0</rec_category>\n\t\t\t<channelId>0</channelId>\n\t\t</websearch>\n\t\t<weappinfo>\n\t\t\t<username />\n\t\t\t<appid />\n\t\t\t<appservicetype>0</appservicetype>\n\t\t\t<secflagforsinglepagemode>0</secflagforsinglepagemode>\n\t\t\t<videopageinfo>\n\t\t\t\t<thumbwidth>0</thumbwidth>\n\t\t\t\t<thumbheight>0</thumbheight>\n\t\t\t\t<fromopensdk>0</fromopensdk>\n\t\t\t</videopageinfo>\n\t\t</weappinfo>\n\t\t<statextstr />\n\t\t<musicShareItem>\n\t\t\t<musicDuration>0</musicDuration>\n\t\t</musicShareItem>\n\t\t<finderLiveProductShare>\n\t\t\t<finderLiveID><![CDATA[]]></finderLiveID>\n\t\t\t<finderUsername><![CDATA[]]></finderUsername>\n\t\t\t<finderObjectID><![CDATA[]]></finderObjectID>\n\t\t\t<finderNonceID><![CDATA[]]></finderNonceID>\n\t\t\t<liveStatus><![CDATA[]]></liveStatus>\n\t\t\t<appId><![CDATA[]]></appId>\n\t\t\t<pagePath><![CDATA[]]></pagePath>\n\t\t\t<productId><![CDATA[]]></productId>\n\t\t\t<coverUrl><![CDATA[]]></coverUrl>\n\t\t\t<productTitle><![CDATA[]]></productTitle>\n\t\t\t<marketPrice><![CDATA[0]]></marketPrice>\n\t\t\t<sellingPrice><![CDATA[0]]></sellingPrice>\n\t\t\t<platformHeadImg><![CDATA[]]></platformHeadImg>\n\t\t\t<platformName><![CDATA[]]></platformName>\n\t\t\t<shopWindowId><![CDATA[]]></shopWindowId>\n\t\t\t<flashSalePrice><![CDATA[0]]></flashSalePrice>\n\t\t\t<flashSaleEndTime><![CDATA[0]]></flashSaleEndTime>\n\t\t\t<ecSource><![CDATA[]]></ecSource>\n\t\t\t<sellingPriceWording><![CDATA[]]></sellingPriceWording>\n\t\t\t<platformIconURL><![CDATA[]]></platformIconURL>\n\t\t\t<firstProductTagURL><![CDATA[]]></firstProductTagURL>\n\t\t\t<firstProductTagAspectRatioString><![CDATA[0.0]]></firstProductTagAspectRatioString>\n\t\t\t<secondProductTagURL><![CDATA[]]></secondProductTagURL>\n\t\t\t<secondProductTagAspectRatioString><![CDATA[0.0]]></secondProductTagAspectRatioString>\n\t\t\t<firstGuaranteeWording><![CDATA[]]></firstGuaranteeWording>\n\t\t\t<secondGuaranteeWording><![CDATA[]]></secondGuaranteeWording>\n\t\t\t<thirdGuaranteeWording><![CDATA[]]></thirdGuaranteeWording>\n\t\t\t<isPriceBeginShow>false</isPriceBeginShow>\n\t\t\t<lastGMsgID><![CDATA[]]></lastGMsgID>\n\t\t\t<promoterKey><![CDATA[]]></promoterKey>\n\t\t\t<discountWording><![CDATA[]]></discountWording>\n\t\t\t<priceSuffixDescription><![CDATA[]]></priceSuffixDescription>\n\t\t\t<productCardKey><![CDATA[]]></productCardKey>\n\t\t\t<isWxShop><![CDATA[]]></isWxShop>\n\t\t\t<brandIconUrl><![CDATA[]]></brandIconUrl>\n\t\t\t<rIconUrl><![CDATA[]]></rIconUrl>\n\t\t\t<rIconUrlDarkMode><![CDATA[]]></rIconUrlDarkMode>\n\t\t\t<topShopIconUrl><![CDATA[]]></topShopIconUrl>\n\t\t\t<topShopIconUrlDarkMode><![CDATA[]]></topShopIconUrlDarkMode>\n\t\t\t<simplifyTopShopIconUrl><![CDATA[]]></simplifyTopShopIconUrl>\n\t\t\t<simplifyTopShopIconUrlDarkmode><![CDATA[]]></simplifyTopShopIconUrlDarkmode>\n\t\t\t<topShopIconWidth><![CDATA[0]]></topShopIconWidth>\n\t\t\t<topShopIconHeight><![CDATA[0]]></topShopIconHeight>\n\t\t\t<simplifyTopShopIconWidth><![CDATA[0]]></simplifyTopShopIconWidth>\n\t\t\t<simplifyTopShopIconHeight><![CDATA[0]]></simplifyTopShopIconHeight>\n\t\t\t<showBoxItemStringList />\n\t\t</finderLiveProductShare>\n\t\t<finderOrder>\n\t\t\t<appID><![CDATA[]]></appID>\n\t\t\t<orderID><![CDATA[]]></orderID>\n\t\t\t<path><![CDATA[]]></path>\n\t\t\t<priceWording><![CDATA[]]></priceWording>\n\t\t\t<stateWording><![CDATA[]]></stateWording>\n\t\t\t<productImageURL><![CDATA[]]></productImageURL>\n\t\t\t<products><![CDATA[]]></products>\n\t\t\t<productsCount><![CDATA[0]]></productsCount>\n\t\t\t<orderType><![CDATA[0]]></orderType>\n\t\t\t<newPriceWording><![CDATA[]]></newPriceWording>\n\t\t\t<newStateWording><![CDATA[]]></newStateWording>\n\t\t\t<useNewWording><![CDATA[0]]></useNewWording>\n\t\t</finderOrder>\n\t\t<finderShopWindowShare>\n\t\t\t<finderUsername><![CDATA[]]></finderUsername>\n\t\t\t<avatar><![CDATA[]]></avatar>\n\t\t\t<nickname><![CDATA[]]></nickname>\n\t\t\t<commodityInStockCount><![CDATA[]]></commodityInStockCount>\n\t\t\t<appId><![CDATA[]]></appId>\n\t\t\t<path><![CDATA[]]></path>\n\t\t\t<appUsername><![CDATA[]]></appUsername>\n\t\t\t<query><![CDATA[]]></query>\n\t\t\t<liteAppId><![CDATA[]]></liteAppId>\n\t\t\t<liteAppPath><![CDATA[]]></liteAppPath>\n\t\t\t<liteAppQuery><![CDATA[]]></liteAppQuery>\n\t\t\t<platformTagURL><![CDATA[]]></platformTagURL>\n\t\t\t<saleWording><![CDATA[]]></saleWording>\n\t\t\t<lastGMsgID><![CDATA[]]></lastGMsgID>\n\t\t\t<profileTypeWording><![CDATA[]]></profileTypeWording>\n\t\t\t<saleWordingExtra><![CDATA[]]></saleWordingExtra>\n\t\t\t<isWxShop><![CDATA[]]></isWxShop>\n\t\t\t<platformIconUrl><![CDATA[]]></platformIconUrl>\n\t\t\t<brandIconUrl><![CDATA[]]></brandIconUrl>\n\t\t\t<description><![CDATA[]]></description>\n\t\t\t<backgroundUrl><![CDATA[]]></backgroundUrl>\n\t\t\t<darkModePlatformIconUrl><![CDATA[]]></darkModePlatformIconUrl>\n\t\t\t<rIconUrl><![CDATA[]]></rIconUrl>\n\t\t\t<rIconUrlDarkMode><![CDATA[]]></rIconUrlDarkMode>\n\t\t\t<rWords><![CDATA[]]></rWords>\n\t\t\t<topShopIconUrl><![CDATA[]]></topShopIconUrl>\n\t\t\t<topShopIconUrlDarkMode><![CDATA[]]></topShopIconUrlDarkMode>\n\t\t\t<simplifyTopShopIconUrl><![CDATA[]]></simplifyTopShopIconUrl>\n\t\t\t<simplifyTopShopIconUrlDarkmode><![CDATA[]]></simplifyTopShopIconUrlDarkmode>\n\t\t\t<topShopIconWidth><![CDATA[0]]></topShopIconWidth>\n\t\t\t<topShopIconHeight><![CDATA[0]]></topShopIconHeight>\n\t\t\t<simplifyTopShopIconWidth><![CDATA[0]]></simplifyTopShopIconWidth>\n\t\t\t<simplifyTopShopIconHeight><![CDATA[0]]></simplifyTopShopIconHeight>\n\t\t\t<reputationInfo>\n\t\t\t\t<hasReputationInfo>0</hasReputationInfo>\n\t\t\t\t<reputationScore>0</reputationScore>\n\t\t\t\t<reputationWording />\n\t\t\t\t<reputationTextColor />\n\t\t\t\t<reputationLevelWording />\n\t\t\t\t<reputationBackgroundColor />\n\t\t\t</reputationInfo>\n\t\t\t<productImageURLList />\n\t\t</finderShopWindowShare>\n\t\t<findernamecard>\n\t\t\t<username />\n\t\t\t<avatar><![CDATA[]]></avatar>\n\t\t\t<nickname />\n\t\t\t<auth_job />\n\t\t\t<auth_icon>0</auth_icon>\n\t\t\t<auth_icon_url />\n\t\t\t<ecSource><![CDATA[]]></ecSource>\n\t\t\t<lastGMsgID><![CDATA[]]></lastGMsgID>\n\t\t</findernamecard>\n\t\t<finderGuarantee>\n\t\t\t<scene><![CDATA[0]]></scene>\n\t\t</finderGuarantee>\n\t\t<directshare>0</directshare>\n\t\t<gamecenter>\n\t\t\t<namecard>\n\t\t\t\t<iconUrl />\n\t\t\t\t<name />\n\t\t\t\t<desc />\n\t\t\t\t<tail />\n\t\t\t\t<jumpUrl />\n\t\t\t\t<liteappId />\n\t\t\t\t<liteappPath />\n\t\t\t\t<liteappQuery />\n\t\t\t\t<liteappMinVersion />\n\t\t\t</namecard>\n\t\t</gamecenter>\n\t\t<patMsg>\n\t\t\t<chatUser />\n\t\t\t<records>\n\t\t\t\t<recordNum>0</recordNum>\n\t\t\t</records>\n\t\t</patMsg>\n\t\t<secretmsg>\n\t\t\t<issecretmsg>0</issecretmsg>\n\t\t</secretmsg>\n\t\t<referfromscene>0</referfromscene>\n\t\t<gameshare>\n\t\t\t<liteappext>\n\t\t\t\t<liteappbizdata />\n\t\t\t\t<priority>0</priority>\n\t\t\t</liteappext>\n\t\t\t<appbrandext>\n\t\t\t\t<litegameinfo />\n\t\t\t\t<priority>-1</priority>\n\t\t\t</appbrandext>\n\t\t\t<gameshareid />\n\t\t\t<sharedata />\n\t\t\t<isvideo>0</isvideo>\n\t\t\t<duration>-1</duration>\n\t\t\t<isexposed>0</isexposed>\n\t\t\t<readtext />\n\t\t</gameshare>\n\t\t<tingChatRoomItem>\n\t\t\t<type>0</type>\n\t\t\t<categoryItem>null</categoryItem>\n\t\t\t<categoryId />\n\t\t</tingChatRoomItem>\n\t\t<mpsharetrace>\n\t\t\t<hasfinderelement>0</hasfinderelement>\n\t\t\t<lastgmsgid />\n\t\t</mpsharetrace>\n\t\t<wxgamecard>\n\t\t\t<framesetname />\n\t\t\t<mbcarddata />\n\t\t\t<minpkgversion />\n\t\t\t<clientextinfo />\n\t\t\t<mbcardheight>0</mbcardheight>\n\t\t\t<isoldversion>0</isoldversion>\n\t\t</wxgamecard>\n\t\t<ecskfcard>\n\t\t\t<framesetname />\n\t\t\t<mbcarddata />\n\t\t\t<minupdateunixtimestamp>0</minupdateunixtimestamp>\n\t\t\t<needheader>false</needheader>\n\t\t\t<summary />\n\t\t</ecskfcard>\n\t\t<liteapp>\n\t\t\t<id>null</id>\n\t\t\t<path />\n\t\t\t<query />\n\t\t\t<istransparent>0</istransparent>\n\t\t\t<hideicon>0</hideicon>\n\t\t\t<forbidforward>0</forbidforward>\n\t\t</liteapp>\n\t\t<opensdk_share_is_modified>0</opensdk_share_is_modified>\n\t</appmsg>\n\t<fromusername>wxid_62fiham2pn7521</fromusername>\n\t<scene>0</scene>\n\t<appinfo>\n\t\t<version>1</version>\n\t\t<appname></appname>\n\t</appinfo>\n\t<commenturl></commenturl>\n</msg>\n'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1753839194, 'MsgSource': '<msgsource>\n\t<alnode>\n\t\t<fr>4</fr>\n\t</alnode>\n\t<eggIncluded>1</eggIncluded>\n\t<sec_msg_node>\n\t\t<uuid>a0edf93ac0f5bba3630cc1b4ccfca261_</uuid>\n\t\t<risk-file-flag />\n\t\t<risk-file-md5-list />\n\t</sec_msg_node>\n\t<silence>0</silence>\n\t<membercount>222</membercount>\n\t<signature>N0_V1_LszYYPAP|v1_7GEF3yzb</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'PushContent': '八戒。 : 豆包 保持人物一致性，把图片人物衣服换成适合海边沙滩游玩的衣...', 'NewMsgId': 1765274039407004380, 'MsgSeq': 871410967}
2025-07-30 09:33:45 | DEBUG | 从群聊消息中提取发送者: wxid_62fiham2pn7521
2025-07-30 09:33:45 | DEBUG | 使用已解析的XML处理引用消息
2025-07-30 09:33:45 | INFO | 收到引用消息: 消息ID:295505825 来自:47325400669@chatroom 发送人:wxid_62fiham2pn7521 内容:豆包 保持人物一致性，把图片人物衣服换成适合海边沙滩游玩的衣 引用类型:3
2025-07-30 09:33:45 | INFO | [DouBaoImageToImage] 收到引用消息: 豆包 保持人物一致性，把图片人物衣服换成适合海边沙滩游玩的衣服
2025-07-30 09:33:45 | INFO | [DouBaoImageToImage] 引用图片图生图 - 提示词: '保持人物一致性，把图片人物衣服换成适合海边沙滩游玩的衣服，比例「2:3」', 比例: 832x1248, 风格: None
2025-07-30 09:33:45 | INFO | [DouBaoImageToImage] 被引用消息类型: 3
2025-07-30 09:33:45 | INFO | [DouBaoImageToImage] 成功下载引用的图片并保存到: temp\doubao_image_to_image\quoted_image_1753839225.jpg
2025-07-30 09:33:45 | INFO | [DouBaoImageToImage] 开始豆包AI处理流程(无通知)，用户: wxid_62fiham2pn7521, 提示词: 保持人物一致性，把图片人物衣服换成适合海边沙滩游玩的衣服，比例「2:3」, 比例: 832x1248
2025-07-30 09:33:45 | INFO | [DouBaoImageToImage] 步骤1: 验证图片文件...
2025-07-30 09:33:45 | INFO | [DouBaoImageToImage] 图片验证成功，大小: 59.0KB
2025-07-30 09:33:45 | INFO | [DouBaoImageToImage] 步骤2: 调用豆包AI接口...
2025-07-30 09:33:45 | INFO | [DouBaoImageToImage] 开始豆包AI图生图处理，提示词: 保持人物一致性，把图片人物衣服换成适合海边沙滩游玩的衣服，比例「2:3」
2025-07-30 09:33:45 | INFO | [DouBaoImageToImage] 使用比例: 2:3 (832x1248)
2025-07-30 09:33:53 | DEBUG | [TempFileManager] 已清理文件: temp\content_1753839220.png
2025-07-30 09:33:53 | DEBUG | [TempFileManager] 已清理文件: temp\content_1753839220.png
2025-07-30 09:34:16 | INFO | [DouBaoImageToImage] 豆包AI处理成功: https://p9-flow-imagex-sign.byteimg.com/tos-cn-i-a9rns2rl98/rc/pc/creation_agent/54a8a8b951c74f2795d24cff7714dfa4~tplv-a9rns2rl98-image-dark-watermark.png?rk3s=8e244e95&rrcfp=5057214b&x-expires=2069199267&x-signature=S%2FQ%2BjnJkNJWPTQsdQq6%2FdB3Geyk%3D
2025-07-30 09:34:16 | INFO | [DouBaoImageToImage] 豆包AI处理完成，生成了1张图片
2025-07-30 09:34:16 | INFO | 发送文字消息: 对方wxid:47325400669@chatroom at: 内容:好的，我将为你修改第一张图片，保持人物不变，把衣服换成适合海边沙滩游玩的款式，比例保持2:3。
2025-07-30 09:34:19 | INFO | [DouBaoImageToImage] 准备发送图片 1/1, 大小: 1620.2KB
2025-07-30 09:34:26 | DEBUG | [TempFileManager] 已清理文件: temp\doubao_image_to_image\quoted_image_1753839225.jpg
2025-07-30 09:34:27 | INFO | 发送图片消息: 对方wxid:47325400669@chatroom 图片base64略
2025-07-30 09:34:27 | INFO | [DouBaoImageToImage] 图片 1/1 发送成功
2025-07-30 09:34:29 | INFO | [TimerTask] 收到引用消息调试信息:
2025-07-30 09:34:29 | INFO |   - 消息内容: 豆包 保持人物一致性，把图片人物衣服换成适合海边沙滩游玩的衣服
2025-07-30 09:34:29 | INFO |   - 群组ID: 47325400669@chatroom
2025-07-30 09:34:29 | INFO |   - 发送人: wxid_62fiham2pn7521
2025-07-30 09:34:29 | INFO |   - 引用信息: {'MsgType': 3, 'Content': '<?xml version="1.0"?>\n<msg>\n\t<appmsg appid="" sdkver="0">\n\t\t<title>豆包 保持人物一致性，把图片人物衣服换成适合海边沙滩游玩的衣服</title>\n\t\t<des />\n\t\t<username />\n\t\t<action>view</action>\n\t\t<type>57</type>\n\t\t<showtype>0</showtype>\n\t\t<content />\n\t\t<url />\n\t\t<lowurl />\n\t\t<forwardflag>0</forwardflag>\n\t\t<dataurl />\n\t\t<lowdataurl />\n\t\t<contentattr>0</contentattr>\n\t\t<streamvideo>\n\t\t\t<streamvideourl />\n\t\t\t<streamvideototaltime>0</streamvideototaltime>\n\t\t\t<streamvideotitle />\n\t\t\t<streamvideowording />\n\t\t\t<streamvideoweburl />\n\t\t\t<streamvideothumburl />\n\t\t\t<streamvideoaduxinfo />\n\t\t\t<streamvideopublishid />\n\t\t</streamvideo>\n\t\t<canvasPageItem>\n\t\t\t<canvasPageXml><![CDATA[]]></canvasPageXml>\n\t\t</canvasPageItem>\n\t\t<refermsg>\n\t\t\t<type>3</type>\n\t\t\t<svrid>714496648952914170</svrid>\n\t\t\t<fromusr>47325400669@chatroom</fromusr>\n\t\t\t<chatusr>wxid_fh84okl6f5wp22</chatusr>\n\t\t\t<displayname>阿猪米德</displayname>\n\t\t\t<msgsource>&lt;msgsource&gt;\n\t&lt;sec_msg_node&gt;\n\t\t&lt;uuid&gt;a0edf93ac0f5bba3630cc1b4ccfca261_&lt;/uuid&gt;\n\t\t&lt;risk-file-flag /&gt;\n\t\t&lt;risk-file-md5-list /&gt;\n\t&lt;/sec_msg_node&gt;\n\t&lt;imgmsg_pd cdnmidimgurl_size="60450" cdnmidimgurl_pd_pri="50" cdnmidimgurl_pd="1" /&gt;\n\t&lt;silence&gt;1&lt;/silence&gt;\n\t&lt;membercount&gt;222&lt;/membercount&gt;\n\t&lt;NotAutoDownloadRange&gt;20:00-22:00;00:00-01:00&lt;/NotAutoDownloadRange&gt;\n\t&lt;signature&gt;N0_V1_CiuSS/Ep|v1_kzN/Ad9Q&lt;/signature&gt;\n\t&lt;tmp_node&gt;\n\t\t&lt;publisher-id&gt;&lt;/publisher-id&gt;\n\t&lt;/tmp_node&gt;\n&lt;/msgsource&gt;\n</msgsource>\n\t\t\t<content>&lt;?xml version="1.0"?&gt;\n&lt;msg&gt;\n\t&lt;img aeskey="64706f757372756a6f74647173736f78" encryver="0" cdnthumbaeskey="64706f757372756a6f74647173736f78" cdnthumburl="3057020100044b30490201000204d6e1bab602033d14ba02048f06ff9d020468896c8d042437366439383062612d326338372d343761362d613865652d3762313337373565373862310204052828010201000405004c543e0054381738" cdnthumblength="3258" cdnthumbheight="100" cdnthumbwidth="66" cdnmidheight="0" cdnmidwidth="0" cdnhdheight="0" cdnhdwidth="0" cdnmidimgurl="3057020100044b30490201000204d6e1bab602033d14ba02048f06ff9d020468896c8d042437366439383062612d326338372d343761362d613865652d3762313337373565373862310204052828010201000405004c543e0054381738" length="60450" cdnbigimgurl="3057020100044b30490201000204d6e1bab602033d14ba02048f06ff9d020468896c8d042437366439383062612d326338372d343761362d613865652d3762313337373565373862310204052828010201000405004c543e0054381738" hdlength="196225" md5="4f2ea21d2d7ef077f433b2504011b6df"&gt;\n\t\t&lt;secHashInfoBase64 /&gt;\n\t\t&lt;live&gt;\n\t\t\t&lt;duration&gt;0&lt;/duration&gt;\n\t\t\t&lt;size&gt;0&lt;/size&gt;\n\t\t\t&lt;md5 /&gt;\n\t\t\t&lt;fileid /&gt;\n\t\t\t&lt;hdsize&gt;0&lt;/hdsize&gt;\n\t\t\t&lt;hdmd5 /&gt;\n\t\t\t&lt;hdfileid /&gt;\n\t\t\t&lt;stillimagetimems&gt;0&lt;/stillimagetimems&gt;\n\t\t&lt;/live&gt;\n\t&lt;/img&gt;\n\t&lt;platform_signature /&gt;\n\t&lt;imgdatahash /&gt;\n\t&lt;ImgSourceInfo&gt;\n\t\t&lt;ImgSourceUrl /&gt;\n\t\t&lt;BizType&gt;0&lt;/BizType&gt;\n\t&lt;/ImgSourceInfo&gt;\n&lt;/msg&gt;\n</content>\n\t\t\t<strid />\n\t\t\t<createtime>1753836684</createtime>\n\t\t</refermsg>\n\t\t<appattach>\n\t\t\t<totallen>0</totallen>\n\t\t\t<attachid />\n\t\t\t<cdnattachurl />\n\t\t\t<emoticonmd5 />\n\t\t\t<aeskey />\n\t\t\t<fileext />\n\t\t\t<islargefilemsg>0</islargefilemsg>\n\t\t</appattach>\n\t\t<extinfo />\n\t\t<androidsource>0</androidsource>\n\t\t<thumburl />\n\t\t<mediatagname />\n\t\t<messageaction><![CDATA[]]></messageaction>\n\t\t<messageext><![CDATA[]]></messageext>\n\t\t<emoticongift>\n\t\t\t<packageflag>0</packageflag>\n\t\t\t<packageid />\n\t\t</emoticongift>\n\t\t<emoticonshared>\n\t\t\t<packageflag>0</packageflag>\n\t\t\t<packageid />\n\t\t</emoticonshared>\n\t\t<designershared>\n\t\t\t<designeruin>0</designeruin>\n\t\t\t<designername>null</designername>\n\t\t\t<designerrediretcturl><![CDATA[null]]></designerrediretcturl>\n\t\t</designershared>\n\t\t<emotionpageshared>\n\t\t\t<tid>0</tid>\n\t\t\t<title>null</title>\n\t\t\t<desc>null</desc>\n\t\t\t<iconUrl><![CDATA[null]]></iconUrl>\n\t\t\t<secondUrl>null</secondUrl>\n\t\t\t<pageType>0</pageType>\n\t\t\t<setKey>null</setKey>\n\t\t</emotionpageshared>\n\t\t<webviewshared>\n\t\t\t<shareUrlOriginal />\n\t\t\t<shareUrlOpen />\n\t\t\t<jsAppId />\n\t\t\t<publisherId />\n\t\t\t<publisherReqId />\n\t\t</webviewshared>\n\t\t<template_id />\n\t\t<md5 />\n\t\t<websearch>\n\t\t\t<rec_category>0</rec_category>\n\t\t\t<channelId>0</channelId>\n\t\t</websearch>\n\t\t<weappinfo>\n\t\t\t<username />\n\t\t\t<appid />\n\t\t\t<appservicetype>0</appservicetype>\n\t\t\t<secflagforsinglepagemode>0</secflagforsinglepagemode>\n\t\t\t<videopageinfo>\n\t\t\t\t<thumbwidth>0</thumbwidth>\n\t\t\t\t<thumbheight>0</thumbheight>\n\t\t\t\t<fromopensdk>0</fromopensdk>\n\t\t\t</videopageinfo>\n\t\t</weappinfo>\n\t\t<statextstr />\n\t\t<musicShareItem>\n\t\t\t<musicDuration>0</musicDuration>\n\t\t</musicShareItem>\n\t\t<finderLiveProductShare>\n\t\t\t<finderLiveID><![CDATA[]]></finderLiveID>\n\t\t\t<finderUsername><![CDATA[]]></finderUsername>\n\t\t\t<finderObjectID><![CDATA[]]></finderObjectID>\n\t\t\t<finderNonceID><![CDATA[]]></finderNonceID>\n\t\t\t<liveStatus><![CDATA[]]></liveStatus>\n\t\t\t<appId><![CDATA[]]></appId>\n\t\t\t<pagePath><![CDATA[]]></pagePath>\n\t\t\t<productId><![CDATA[]]></productId>\n\t\t\t<coverUrl><![CDATA[]]></coverUrl>\n\t\t\t<productTitle><![CDATA[]]></productTitle>\n\t\t\t<marketPrice><![CDATA[0]]></marketPrice>\n\t\t\t<sellingPrice><![CDATA[0]]></sellingPrice>\n\t\t\t<platformHeadImg><![CDATA[]]></platformHeadImg>\n\t\t\t<platformName><![CDATA[]]></platformName>\n\t\t\t<shopWindowId><![CDATA[]]></shopWindowId>\n\t\t\t<flashSalePrice><![CDATA[0]]></flashSalePrice>\n\t\t\t<flashSaleEndTime><![CDATA[0]]></flashSaleEndTime>\n\t\t\t<ecSource><![CDATA[]]></ecSource>\n\t\t\t<sellingPriceWording><![CDATA[]]></sellingPriceWording>\n\t\t\t<platformIconURL><![CDATA[]]></platformIconURL>\n\t\t\t<firstProductTagURL><![CDATA[]]></firstProductTagURL>\n\t\t\t<firstProductTagAspectRatioString><![CDATA[0.0]]></firstProductTagAspectRatioString>\n\t\t\t<secondProductTagURL><![CDATA[]]></secondProductTagURL>\n\t\t\t<secondProductTagAspectRatioString><![CDATA[0.0]]></secondProductTagAspectRatioString>\n\t\t\t<firstGuaranteeWording><![CDATA[]]></firstGuaranteeWording>\n\t\t\t<secondGuaranteeWording><![CDATA[]]></secondGuaranteeWording>\n\t\t\t<thirdGuaranteeWording><![CDATA[]]></thirdGuaranteeWording>\n\t\t\t<isPriceBeginShow>false</isPriceBeginShow>\n\t\t\t<lastGMsgID><![CDATA[]]></lastGMsgID>\n\t\t\t<promoterKey><![CDATA[]]></promoterKey>\n\t\t\t<discountWording><![CDATA[]]></discountWording>\n\t\t\t<priceSuffixDescription><![CDATA[]]></priceSuffixDescription>\n\t\t\t<productCardKey><![CDATA[]]></productCardKey>\n\t\t\t<isWxShop><![CDATA[]]></isWxShop>\n\t\t\t<brandIconUrl><![CDATA[]]></brandIconUrl>\n\t\t\t<rIconUrl><![CDATA[]]></rIconUrl>\n\t\t\t<rIconUrlDarkMode><![CDATA[]]></rIconUrlDarkMode>\n\t\t\t<topShopIconUrl><![CDATA[]]></topShopIconUrl>\n\t\t\t<topShopIconUrlDarkMode><![CDATA[]]></topShopIconUrlDarkMode>\n\t\t\t<simplifyTopShopIconUrl><![CDATA[]]></simplifyTopShopIconUrl>\n\t\t\t<simplifyTopShopIconUrlDarkmode><![CDATA[]]></simplifyTopShopIconUrlDarkmode>\n\t\t\t<topShopIconWidth><![CDATA[0]]></topShopIconWidth>\n\t\t\t<topShopIconHeight><![CDATA[0]]></topShopIconHeight>\n\t\t\t<simplifyTopShopIconWidth><![CDATA[0]]></simplifyTopShopIconWidth>\n\t\t\t<simplifyTopShopIconHeight><![CDATA[0]]></simplifyTopShopIconHeight>\n\t\t\t<showBoxItemStringList />\n\t\t</finderLiveProductShare>\n\t\t<finderOrder>\n\t\t\t<appID><![CDATA[]]></appID>\n\t\t\t<orderID><![CDATA[]]></orderID>\n\t\t\t<path><![CDATA[]]></path>\n\t\t\t<priceWording><![CDATA[]]></priceWording>\n\t\t\t<stateWording><![CDATA[]]></stateWording>\n\t\t\t<productImageURL><![CDATA[]]></productImageURL>\n\t\t\t<products><![CDATA[]]></products>\n\t\t\t<productsCount><![CDATA[0]]></productsCount>\n\t\t\t<orderType><![CDATA[0]]></orderType>\n\t\t\t<newPriceWording><![CDATA[]]></newPriceWording>\n\t\t\t<newStateWording><![CDATA[]]></newStateWording>\n\t\t\t<useNewWording><![CDATA[0]]></useNewWording>\n\t\t</finderOrder>\n\t\t<finderShopWindowShare>\n\t\t\t<finderUsername><![CDATA[]]></finderUsername>\n\t\t\t<avatar><![CDATA[]]></avatar>\n\t\t\t<nickname><![CDATA[]]></nickname>\n\t\t\t<commodityInStockCount><![CDATA[]]></commodityInStockCount>\n\t\t\t<appId><![CDATA[]]></appId>\n\t\t\t<path><![CDATA[]]></path>\n\t\t\t<appUsername><![CDATA[]]></appUsername>\n\t\t\t<query><![CDATA[]]></query>\n\t\t\t<liteAppId><![CDATA[]]></liteAppId>\n\t\t\t<liteAppPath><![CDATA[]]></liteAppPath>\n\t\t\t<liteAppQuery><![CDATA[]]></liteAppQuery>\n\t\t\t<platformTagURL><![CDATA[]]></platformTagURL>\n\t\t\t<saleWording><![CDATA[]]></saleWording>\n\t\t\t<lastGMsgID><![CDATA[]]></lastGMsgID>\n\t\t\t<profileTypeWording><![CDATA[]]></profileTypeWording>\n\t\t\t<saleWordingExtra><![CDATA[]]></saleWordingExtra>\n\t\t\t<isWxShop><![CDATA[]]></isWxShop>\n\t\t\t<platformIconUrl><![CDATA[]]></platformIconUrl>\n\t\t\t<brandIconUrl><![CDATA[]]></brandIconUrl>\n\t\t\t<description><![CDATA[]]></description>\n\t\t\t<backgroundUrl><![CDATA[]]></backgroundUrl>\n\t\t\t<darkModePlatformIconUrl><![CDATA[]]></darkModePlatformIconUrl>\n\t\t\t<rIconUrl><![CDATA[]]></rIconUrl>\n\t\t\t<rIconUrlDarkMode><![CDATA[]]></rIconUrlDarkMode>\n\t\t\t<rWords><![CDATA[]]></rWords>\n\t\t\t<topShopIconUrl><![CDATA[]]></topShopIconUrl>\n\t\t\t<topShopIconUrlDarkMode><![CDATA[]]></topShopIconUrlDarkMode>\n\t\t\t<simplifyTopShopIconUrl><![CDATA[]]></simplifyTopShopIconUrl>\n\t\t\t<simplifyTopShopIconUrlDarkmode><![CDATA[]]></simplifyTopShopIconUrlDarkmode>\n\t\t\t<topShopIconWidth><![CDATA[0]]></topShopIconWidth>\n\t\t\t<topShopIconHeight><![CDATA[0]]></topShopIconHeight>\n\t\t\t<simplifyTopShopIconWidth><![CDATA[0]]></simplifyTopShopIconWidth>\n\t\t\t<simplifyTopShopIconHeight><![CDATA[0]]></simplifyTopShopIconHeight>\n\t\t\t<reputationInfo>\n\t\t\t\t<hasReputationInfo>0</hasReputationInfo>\n\t\t\t\t<reputationScore>0</reputationScore>\n\t\t\t\t<reputationWording />\n\t\t\t\t<reputationTextColor />\n\t\t\t\t<reputationLevelWording />\n\t\t\t\t<reputationBackgroundColor />\n\t\t\t</reputationInfo>\n\t\t\t<productImageURLList />\n\t\t</finderShopWindowShare>\n\t\t<findernamecard>\n\t\t\t<username />\n\t\t\t<avatar><![CDATA[]]></avatar>\n\t\t\t<nickname />\n\t\t\t<auth_job />\n\t\t\t<auth_icon>0</auth_icon>\n\t\t\t<auth_icon_url />\n\t\t\t<ecSource><![CDATA[]]></ecSource>\n\t\t\t<lastGMsgID><![CDATA[]]></lastGMsgID>\n\t\t</findernamecard>\n\t\t<finderGuarantee>\n\t\t\t<scene><![CDATA[0]]></scene>\n\t\t</finderGuarantee>\n\t\t<directshare>0</directshare>\n\t\t<gamecenter>\n\t\t\t<namecard>\n\t\t\t\t<iconUrl />\n\t\t\t\t<name />\n\t\t\t\t<desc />\n\t\t\t\t<tail />\n\t\t\t\t<jumpUrl />\n\t\t\t\t<liteappId />\n\t\t\t\t<liteappPath />\n\t\t\t\t<liteappQuery />\n\t\t\t\t<liteappMinVersion />\n\t\t\t</namecard>\n\t\t</gamecenter>\n\t\t<patMsg>\n\t\t\t<chatUser />\n\t\t\t<records>\n\t\t\t\t<recordNum>0</recordNum>\n\t\t\t</records>\n\t\t</patMsg>\n\t\t<secretmsg>\n\t\t\t<issecretmsg>0</issecretmsg>\n\t\t</secretmsg>\n\t\t<referfromscene>0</referfromscene>\n\t\t<gameshare>\n\t\t\t<liteappext>\n\t\t\t\t<liteappbizdata />\n\t\t\t\t<priority>0</priority>\n\t\t\t</liteappext>\n\t\t\t<appbrandext>\n\t\t\t\t<litegameinfo />\n\t\t\t\t<priority>-1</priority>\n\t\t\t</appbrandext>\n\t\t\t<gameshareid />\n\t\t\t<sharedata />\n\t\t\t<isvideo>0</isvideo>\n\t\t\t<duration>-1</duration>\n\t\t\t<isexposed>0</isexposed>\n\t\t\t<readtext />\n\t\t</gameshare>\n\t\t<tingChatRoomItem>\n\t\t\t<type>0</type>\n\t\t\t<categoryItem>null</categoryItem>\n\t\t\t<categoryId />\n\t\t</tingChatRoomItem>\n\t\t<mpsharetrace>\n\t\t\t<hasfinderelement>0</hasfinderelement>\n\t\t\t<lastgmsgid />\n\t\t</mpsharetrace>\n\t\t<wxgamecard>\n\t\t\t<framesetname />\n\t\t\t<mbcarddata />\n\t\t\t<minpkgversion />\n\t\t\t<clientextinfo />\n\t\t\t<mbcardheight>0</mbcardheight>\n\t\t\t<isoldversion>0</isoldversion>\n\t\t</wxgamecard>\n\t\t<ecskfcard>\n\t\t\t<framesetname />\n\t\t\t<mbcarddata />\n\t\t\t<minupdateunixtimestamp>0</minupdateunixtimestamp>\n\t\t\t<needheader>false</needheader>\n\t\t\t<summary />\n\t\t</ecskfcard>\n\t\t<liteapp>\n\t\t\t<id>null</id>\n\t\t\t<path />\n\t\t\t<query />\n\t\t\t<istransparent>0</istransparent>\n\t\t\t<hideicon>0</hideicon>\n\t\t\t<forbidforward>0</forbidforward>\n\t\t</liteapp>\n\t\t<opensdk_share_is_modified>0</opensdk_share_is_modified>\n\t</appmsg>\n\t<fromusername>wxid_62fiham2pn7521</fromusername>\n\t<scene>0</scene>\n\t<appinfo>\n\t\t<version>1</version>\n\t\t<appname></appname>\n\t</appinfo>\n\t<commenturl></commenturl>\n</msg>\n', 'Msgid': '714496648952914170', 'NewMsgId': '714496648952914170', 'ToWxid': 'wxid_4usgcju5ey9q29', 'FromWxid': '47325400669@chatroom', 'Nickname': '阿猪米德', 'MsgSource': '<msgsource>\n\t<sec_msg_node>\n\t\t<uuid>a0edf93ac0f5bba3630cc1b4ccfca261_</uuid>\n\t\t<risk-file-flag />\n\t\t<risk-file-md5-list />\n\t</sec_msg_node>\n\t<imgmsg_pd cdnmidimgurl_size="60450" cdnmidimgurl_pd_pri="50" cdnmidimgurl_pd="1" />\n\t<silence>1</silence>\n\t<membercount>222</membercount>\n\t<NotAutoDownloadRange>20:00-22:00;00:00-01:00</NotAutoDownloadRange>\n\t<signature>N0_V1_CiuSS/Ep|v1_kzN/Ad9Q</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'Createtime': '1753836684', 'SenderWxid': 'wxid_62fiham2pn7521'}
2025-07-30 09:34:29 | INFO |   - 引用消息ID: 
2025-07-30 09:34:29 | INFO |   - 引用消息类型: 
2025-07-30 09:34:29 | INFO |   - 引用消息内容: <?xml version="1.0"?>
<msg>
	<appmsg appid="" sdkver="0">
		<title>豆包 保持人物一致性，把图片人物衣服换成适合海边沙滩游玩的衣服</title>
		<des />
		<username />
		<action>view</action>
		<type>57</type>
		<showtype>0</showtype>
		<content />
		<url />
		<lowurl />
		<forwardflag>0</forwardflag>
		<dataurl />
		<lowdataurl />
		<contentattr>0</contentattr>
		<streamvideo>
			<streamvideourl />
			<streamvideototaltime>0</streamvideototaltime>
			<streamvideotitle />
			<streamvideowording />
			<streamvideoweburl />
			<streamvideothumburl />
			<streamvideoaduxinfo />
			<streamvideopublishid />
		</streamvideo>
		<canvasPageItem>
			<canvasPageXml><![CDATA[]]></canvasPageXml>
		</canvasPageItem>
		<refermsg>
			<type>3</type>
			<svrid>714496648952914170</svrid>
			<fromusr>47325400669@chatroom</fromusr>
			<chatusr>wxid_fh84okl6f5wp22</chatusr>
			<displayname>阿猪米德</displayname>
			<msgsource>&lt;msgsource&gt;
	&lt;sec_msg_node&gt;
		&lt;uuid&gt;a0edf93ac0f5bba3630cc1b4ccfca261_&lt;/uuid&gt;
		&lt;risk-file-flag /&gt;
		&lt;risk-file-md5-list /&gt;
	&lt;/sec_msg_node&gt;
	&lt;imgmsg_pd cdnmidimgurl_size="60450" cdnmidimgurl_pd_pri="50" cdnmidimgurl_pd="1" /&gt;
	&lt;silence&gt;1&lt;/silence&gt;
	&lt;membercount&gt;222&lt;/membercount&gt;
	&lt;NotAutoDownloadRange&gt;20:00-22:00;00:00-01:00&lt;/NotAutoDownloadRange&gt;
	&lt;signature&gt;N0_V1_CiuSS/Ep|v1_kzN/Ad9Q&lt;/signature&gt;
	&lt;tmp_node&gt;
		&lt;publisher-id&gt;&lt;/publisher-id&gt;
	&lt;/tmp_node&gt;
&lt;/msgsource&gt;
</msgsource>
			<content>&lt;?xml version="1.0"?&gt;
&lt;msg&gt;
	&lt;img aeskey="64706f757372756a6f74647173736f78" encryver="0" cdnthumbaeskey="64706f757372756a6f74647173736f78" cdnthumburl="3057020100044b30490201000204d6e1bab602033d14ba02048f06ff9d020468896c8d042437366439383062612d326338372d343761362d613865652d3762313337373565373862310204052828010201000405004c543e0054381738" cdnthumblength="3258" cdnthumbheight="100" cdnthumbwidth="66" cdnmidheight="0" cdnmidwidth="0" cdnhdheight="0" cdnhdwidth="0" cdnmidimgurl="3057020100044b30490201000204d6e1bab602033d14ba02048f06ff9d020468896c8d042437366439383062612d326338372d343761362d613865652d3762313337373565373862310204052828010201000405004c543e0054381738" length="60450" cdnbigimgurl="3057020100044b30490201000204d6e1bab602033d14ba02048f06ff9d020468896c8d042437366439383062612d326338372d343761362d613865652d3762313337373565373862310204052828010201000405004c543e0054381738" hdlength="196225" md5="4f2ea21d2d7ef077f433b2504011b6df"&gt;
		&lt;secHashInfoBase64 /&gt;
		&lt;live&gt;
			&lt;duration&gt;0&lt;/duration&gt;
			&lt;size&gt;0&lt;/size&gt;
			&lt;md5 /&gt;
			&lt;fileid /&gt;
			&lt;hdsize&gt;0&lt;/hdsize&gt;
			&lt;hdmd5 /&gt;
			&lt;hdfileid /&gt;
			&lt;stillimagetimems&gt;0&lt;/stillimagetimems&gt;
		&lt;/live&gt;
	&lt;/img&gt;
	&lt;platform_signature /&gt;
	&lt;imgdatahash /&gt;
	&lt;ImgSourceInfo&gt;
		&lt;ImgSourceUrl /&gt;
		&lt;BizType&gt;0&lt;/BizType&gt;
	&lt;/ImgSourceInfo&gt;
&lt;/msg&gt;
</content>
			<strid />
			<createtime>1753836684</createtime>
		</refermsg>
		<appattach>
			<totallen>0</totallen>
			<attachid />
			<cdnattachurl />
			<emoticonmd5 />
			<aeskey />
			<fileext />
			<islargefilemsg>0</islargefilemsg>
		</appattach>
		<extinfo />
		<androidsource>0</androidsource>
		<thumburl />
		<mediatagname />
		<messageaction><![CDATA[]]></messageaction>
		<messageext><![CDATA[]]></messageext>
		<emoticongift>
			<packageflag>0</packageflag>
			<packageid />
		</emoticongift>
		<emoticonshared>
			<packageflag>0</packageflag>
			<packageid />
		</emoticonshared>
		<designershared>
			<designeruin>0</designeruin>
			<designername>null</designername>
			<designerrediretcturl><![CDATA[null]]></designerrediretcturl>
		</designershared>
		<emotionpageshared>
			<tid>0</tid>
			<title>null</title>
			<desc>null</desc>
			<iconUrl><![CDATA[null]]></iconUrl>
			<secondUrl>null</secondUrl>
			<pageType>0</pageType>
			<setKey>null</setKey>
		</emotionpageshared>
		<webviewshared>
			<shareUrlOriginal />
			<shareUrlOpen />
			<jsAppId />
			<publisherId />
			<publisherReqId />
		</webviewshared>
		<template_id />
		<md5 />
		<websearch>
			<rec_category>0</rec_category>
			<channelId>0</channelId>
		</websearch>
		<weappinfo>
			<username />
			<appid />
			<appservicetype>0</appservicetype>
			<secflagforsinglepagemode>0</secflagforsinglepagemode>
			<videopageinfo>
				<thumbwidth>0</thumbwidth>
				<thumbheight>0</thumbheight>
				<fromopensdk>0</fromopensdk>
			</videopageinfo>
		</weappinfo>
		<statextstr />
		<musicShareItem>
			<musicDuration>0</musicDuration>
		</musicShareItem>
		<finderLiveProductShare>
			<finderLiveID><![CDATA[]]></finderLiveID>
			<finderUsername><![CDATA[]]></finderUsername>
			<finderObjectID><![CDATA[]]></finderObjectID>
			<finderNonceID><![CDATA[]]></finderNonceID>
			<liveStatus><![CDATA[]]></liveStatus>
			<appId><![CDATA[]]></appId>
			<pagePath><![CDATA[]]></pagePath>
			<productId><![CDATA[]]></productId>
			<coverUrl><![CDATA[]]></coverUrl>
			<productTitle><![CDATA[]]></productTitle>
			<marketPrice><![CDATA[0]]></marketPrice>
			<sellingPrice><![CDATA[0]]></sellingPrice>
			<platformHeadImg><![CDATA[]]></platformHeadImg>
			<platformName><![CDATA[]]></platformName>
			<shopWindowId><![CDATA[]]></shopWindowId>
			<flashSalePrice><![CDATA[0]]></flashSalePrice>
			<flashSaleEndTime><![CDATA[0]]></flashSaleEndTime>
			<ecSource><![CDATA[]]></ecSource>
			<sellingPriceWording><![CDATA[]]></sellingPriceWording>
			<platformIconURL><![CDATA[]]></platformIconURL>
			<firstProductTagURL><![CDATA[]]></firstProductTagURL>
			<firstProductTagAspectRatioString><![CDATA[0.0]]></firstProductTagAspectRatioString>
			<secondProductTagURL><![CDATA[]]></secondProductTagURL>
			<secondProductTagAspectRatioString><![CDATA[0.0]]></secondProductTagAspectRatioString>
			<firstGuaranteeWording><![CDATA[]]></firstGuaranteeWording>
			<secondGuaranteeWording><![CDATA[]]></secondGuaranteeWording>
			<thirdGuaranteeWording><![CDATA[]]></thirdGuaranteeWording>
			<isPriceBeginShow>false</isPriceBeginShow>
			<lastGMsgID><![CDATA[]]></lastGMsgID>
			<promoterKey><![CDATA[]]></promoterKey>
			<discountWording><![CDATA[]]></discountWording>
			<priceSuffixDescription><![CDATA[]]></priceSuffixDescription>
			<productCardKey><![CDATA[]]></productCardKey>
			<isWxShop><![CDATA[]]></isWxShop>
			<brandIconUrl><![CDATA[]]></brandIconUrl>
			<rIconUrl><![CDATA[]]></rIconUrl>
			<rIconUrlDarkMode><![CDATA[]]></rIconUrlDarkMode>
			<topShopIconUrl><![CDATA[]]></topShopIconUrl>
			<topShopIconUrlDarkMode><![CDATA[]]></topShopIconUrlDarkMode>
			<simplifyTopShopIconUrl><![CDATA[]]></simplifyTopShopIconUrl>
			<simplifyTopShopIconUrlDarkmode><![CDATA[]]></simplifyTopShopIconUrlDarkmode>
			<topShopIconWidth><![CDATA[0]]></topShopIconWidth>
			<topShopIconHeight><![CDATA[0]]></topShopIconHeight>
			<simplifyTopShopIconWidth><![CDATA[0]]></simplifyTopShopIconWidth>
			<simplifyTopShopIconHeight><![CDATA[0]]></simplifyTopShopIconHeight>
			<showBoxItemStringList />
		</finderLiveProductShare>
		<finderOrder>
			<appID><![CDATA[]]></appID>
			<orderID><![CDATA[]]></orderID>
			<path><![CDATA[]]></path>
			<priceWording><![CDATA[]]></priceWording>
			<stateWording><![CDATA[]]></stateWording>
			<productImageURL><![CDATA[]]></productImageURL>
			<products><![CDATA[]]></products>
			<productsCount><![CDATA[0]]></productsCount>
			<orderType><![CDATA[0]]></orderType>
			<newPriceWording><![CDATA[]]></newPriceWording>
			<newStateWording><![CDATA[]]></newStateWording>
			<useNewWording><![CDATA[0]]></useNewWording>
		</finderOrder>
		<finderShopWindowShare>
			<finderUsername><![CDATA[]]></finderUsername>
			<avatar><![CDATA[]]></avatar>
			<nickname><![CDATA[]]></nickname>
			<commodityInStockCount><![CDATA[]]></commodityInStockCount>
			<appId><![CDATA[]]></appId>
			<path><![CDATA[]]></path>
			<appUsername><![CDATA[]]></appUsername>
			<query><![CDATA[]]></query>
			<liteAppId><![CDATA[]]></liteAppId>
			<liteAppPath><![CDATA[]]></liteAppPath>
			<liteAppQuery><![CDATA[]]></liteAppQuery>
			<platformTagURL><![CDATA[]]></platformTagURL>
			<saleWording><![CDATA[]]></saleWording>
			<lastGMsgID><![CDATA[]]></lastGMsgID>
			<profileTypeWording><![CDATA[]]></profileTypeWording>
			<saleWordingExtra><![CDATA[]]></saleWordingExtra>
			<isWxShop><![CDATA[]]></isWxShop>
			<platformIconUrl><![CDATA[]]></platformIconUrl>
			<brandIconUrl><![CDATA[]]></brandIconUrl>
			<description><![CDATA[]]></description>
			<backgroundUrl><![CDATA[]]></backgroundUrl>
			<darkModePlatformIconUrl><![CDATA[]]></darkModePlatformIconUrl>
			<rIconUrl><![CDATA[]]></rIconUrl>
			<rIconUrlDarkMode><![CDATA[]]></rIconUrlDarkMode>
			<rWords><![CDATA[]]></rWords>
			<topShopIconUrl><![CDATA[]]></topShopIconUrl>
			<topShopIconUrlDarkMode><![CDATA[]]></topShopIconUrlDarkMode>
			<simplifyTopShopIconUrl><![CDATA[]]></simplifyTopShopIconUrl>
			<simplifyTopShopIconUrlDarkmode><![CDATA[]]></simplifyTopShopIconUrlDarkmode>
			<topShopIconWidth><![CDATA[0]]></topShopIconWidth>
			<topShopIconHeight><![CDATA[0]]></topShopIconHeight>
			<simplifyTopShopIconWidth><![CDATA[0]]></simplifyTopShopIconWidth>
			<simplifyTopShopIconHeight><![CDATA[0]]></simplifyTopShopIconHeight>
			<reputationInfo>
				<hasReputationInfo>0</hasReputationInfo>
				<reputationScore>0</reputationScore>
				<reputationWording />
				<reputationTextColor />
				<reputationLevelWording />
				<reputationBackgroundColor />
			</reputationInfo>
			<productImageURLList />
		</finderShopWindowShare>
		<findernamecard>
			<username />
			<avatar><![CDATA[]]></avatar>
			<nickname />
			<auth_job />
			<auth_icon>0</auth_icon>
			<auth_icon_url />
			<ecSource><![CDATA[]]></ecSource>
			<lastGMsgID><![CDATA[]]></lastGMsgID>
		</findernamecard>
		<finderGuarantee>
			<scene><![CDATA[0]]></scene>
		</finderGuarantee>
		<directshare>0</directshare>
		<gamecenter>
			<namecard>
				<iconUrl />
				<name />
				<desc />
				<tail />
				<jumpUrl />
				<liteappId />
				<liteappPath />
				<liteappQuery />
				<liteappMinVersion />
			</namecard>
		</gamecenter>
		<patMsg>
			<chatUser />
			<records>
				<recordNum>0</recordNum>
			</records>
		</patMsg>
		<secretmsg>
			<issecretmsg>0</issecretmsg>
		</secretmsg>
		<referfromscene>0</referfromscene>
		<gameshare>
			<liteappext>
				<liteappbizdata />
				<priority>0</priority>
			</liteappext>
			<appbrandext>
				<litegameinfo />
				<priority>-1</priority>
			</appbrandext>
			<gameshareid />
			<sharedata />
			<isvideo>0</isvideo>
			<duration>-1</duration>
			<isexposed>0</isexposed>
			<readtext />
		</gameshare>
		<tingChatRoomItem>
			<type>0</type>
			<categoryItem>null</categoryItem>
			<categoryId />
		</tingChatRoomItem>
		<mpsharetrace>
			<hasfinderelement>0</hasfinderelement>
			<lastgmsgid />
		</mpsharetrace>
		<wxgamecard>
			<framesetname />
			<mbcarddata />
			<minpkgversion />
			<clientextinfo />
			<mbcardheight>0</mbcardheight>
			<isoldversion>0</isoldversion>
		</wxgamecard>
		<ecskfcard>
			<framesetname />
			<mbcarddata />
			<minupdateunixtimestamp>0</minupdateunixtimestamp>
			<needheader>false</needheader>
			<summary />
		</ecskfcard>
		<liteapp>
			<id>null</id>
			<path />
			<query />
			<istransparent>0</istransparent>
			<hideicon>0</hideicon>
			<forbidforward>0</forbidforward>
		</liteapp>
		<opensdk_share_is_modified>0</opensdk_share_is_modified>
	</appmsg>
	<fromusername>wxid_62fiham2pn7521</fromusername>
	<scene>0</scene>
	<appinfo>
		<version>1</version>
		<appname></appname>
	</appinfo>
	<commenturl></commenturl>
</msg>

2025-07-30 09:34:29 | INFO |   - 引用消息发送人: wxid_62fiham2pn7521
2025-07-30 09:34:29 | DEBUG | 收到消息: {'MsgId': 672375998, 'FromUserName': {'string': '27852221909@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 1, 'Content': {'string': 'tianen532965049:\n多了个JJ sei要'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1753839249, 'MsgSource': '<msgsource>\n\t<bizflag>0</bizflag>\n\t<pua>1</pua>\n\t<eggIncluded>1</eggIncluded>\n\t<silence>1</silence>\n\t<membercount>145</membercount>\n\t<signature>N0_V1_GTCpB6v0|v1_LH0+ifyY</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'NewMsgId': 7748592875053638877, 'MsgSeq': 871410970}
2025-07-30 09:34:29 | INFO | 收到文本消息: 消息ID:672375998 来自:27852221909@chatroom 发送人:tianen532965049 @:[] 内容:多了个JJ sei要
2025-07-30 09:34:29 | DEBUG | 处理消息内容: '多了个JJ sei要'
2025-07-30 09:34:29 | DEBUG | 消息内容 '多了个JJ sei要' 不匹配任何命令，忽略
2025-07-30 09:34:31 | DEBUG | 收到消息: {'MsgId': 568624849, 'FromUserName': {'string': '48097389945@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 49, 'Content': {'string': 'wxid_laurnst5xn0q22:\n<?xml version="1.0"?>\n<msg>\n\t<appmsg appid="" sdkver="0">\n\t\t<title>瑶瑶</title>\n\t\t<des></des>\n\t\t<username />\n\t\t<action>view</action>\n\t\t<type>5</type>\n\t\t<showtype>0</showtype>\n\t\t<content />\n\t\t<url>https://mp.weixin.qq.com/s?__biz=Mzg3MDk0MjQ0Mw==&amp;mid=2247485597&amp;idx=1&amp;sn=a4d14e9c929a2ecffe8bacc10264d496&amp;chksm=cff6bd885c053a248120d8618c0e60dbf1542c640051f274678f24b605e93688cb3d225a985e&amp;mpshare=1&amp;scene=1&amp;srcid=0730KfiEh3eteSGB6SiKOmSA&amp;sharer_shareinfo=219f6f5483a5bb8ac221d1960494f925&amp;sharer_shareinfo_first=219f6f5483a5bb8ac221d1960494f925#rd</url>\n\t\t<lowurl />\n\t\t<forwardflag>0</forwardflag>\n\t\t<dataurl />\n\t\t<lowdataurl />\n\t\t<contentattr>0</contentattr>\n\t\t<streamvideo>\n\t\t\t<streamvideourl />\n\t\t\t<streamvideototaltime>0</streamvideototaltime>\n\t\t\t<streamvideotitle />\n\t\t\t<streamvideowording />\n\t\t\t<streamvideoweburl />\n\t\t\t<streamvideothumburl />\n\t\t\t<streamvideoaduxinfo />\n\t\t\t<streamvideopublishid />\n\t\t</streamvideo>\n\t\t<canvasPageItem>\n\t\t\t<canvasPageXml><![CDATA[]]></canvasPageXml>\n\t\t</canvasPageItem>\n\t\t<appattach>\n\t\t\t<attachid />\n\t\t\t<cdnthumburl>3057020100044b30490201000204425877ba02032df6cd0204fa9865b4020468897693042437376534663162612d363763312d346335642d396638612d3666326661393762363033660204051408030201000405004c4e6100</cdnthumburl>\n\t\t\t<cdnthumbmd5>2f8b67c96ec576da712d69efffcfe32d</cdnthumbmd5>\n\t\t\t<cdnthumblength>30047</cdnthumblength>\n\t\t\t<cdnthumbheight>120</cdnthumbheight>\n\t\t\t<cdnthumbwidth>120</cdnthumbwidth>\n\t\t\t<cdnthumbaeskey>4db4b86eb65eac883c3e0cd1384e1bb7</cdnthumbaeskey>\n\t\t\t<aeskey>4db4b86eb65eac883c3e0cd1384e1bb7</aeskey>\n\t\t\t<encryver>1</encryver>\n\t\t\t<fileext />\n\t\t\t<islargefilemsg>0</islargefilemsg>\n\t\t</appattach>\n\t\t<extinfo />\n\t\t<androidsource>2</androidsource>\n\t\t<sourceusername>gh_1fb924d4e9eb</sourceusername>\n\t\t<sourcedisplayname>TK痒模</sourcedisplayname>\n\t\t<commenturl />\n\t\t<thumburl />\n\t\t<mediatagname />\n\t\t<messageaction><![CDATA[]]></messageaction>\n\t\t<messageext><![CDATA[]]></messageext>\n\t\t<emoticongift>\n\t\t\t<packageflag>0</packageflag>\n\t\t\t<packageid />\n\t\t</emoticongift>\n\t\t<emoticonshared>\n\t\t\t<packageflag>0</packageflag>\n\t\t\t<packageid />\n\t\t</emoticonshared>\n\t\t<designershared>\n\t\t\t<designeruin>0</designeruin>\n\t\t\t<designername>null</designername>\n\t\t\t<designerrediretcturl><![CDATA[null]]></designerrediretcturl>\n\t\t</designershared>\n\t\t<emotionpageshared>\n\t\t\t<tid>0</tid>\n\t\t\t<title>null</title>\n\t\t\t<desc>null</desc>\n\t\t\t<iconUrl><![CDATA[null]]></iconUrl>\n\t\t\t<secondUrl>null</secondUrl>\n\t\t\t<pageType>0</pageType>\n\t\t\t<setKey>null</setKey>\n\t\t</emotionpageshared>\n\t\t<webviewshared>\n\t\t\t<shareUrlOriginal>http://mp.weixin.qq.com/s?__biz=Mzg3MDk0MjQ0Mw==&amp;mid=2247485597&amp;idx=1&amp;sn=a4d14e9c929a2ecffe8bacc10264d496&amp;chksm=cfcc22154befe237f7fbd69a19e1c1139c93c4e18737b1b8d44e3a8a5c9a23147e7ef7981eba&amp;sessionid=0&amp;scene=126&amp;clicktime=1753839244&amp;enterid=1753839244&amp;subscene=10000&amp;ascene=3&amp;fasttmpl_type=0&amp;fasttmpl_fullversion=7841392-zh_CN-zip&amp;fasttmpl_flag=0&amp;realreporttime=1753839244144#rd</shareUrlOriginal>\n\t\t\t<shareUrlOpen>https://mp.weixin.qq.com/s?__biz=Mzg3MDk0MjQ0Mw==&amp;mid=2247485597&amp;idx=1&amp;sn=a4d14e9c929a2ecffe8bacc10264d496&amp;chksm=cfcc22154befe237f7fbd69a19e1c1139c93c4e18737b1b8d44e3a8a5c9a23147e7ef7981eba&amp;sessionid=0&amp;scene=126&amp;clicktime=1753839244&amp;enterid=1753839244&amp;subscene=10000&amp;ascene=3&amp;fasttmpl_type=0&amp;fasttmpl_fullversion=7841392-zh_CN-zip&amp;fasttmpl_flag=0&amp;realreporttime=1753839244144&amp;devicetype=android-35&amp;version=28003e32&amp;nettype=WIFI&amp;lang=zh_CN&amp;session_us=gh_1fb924d4e9eb&amp;countrycode=CN&amp;exportkey=n_ChQIAhIQLV5%2B%2B0vIW8tfoCkIapWm6xLxAQIE97dBBAEAAAAAAKDjDDDJXcEAAAAOpnltbLcz9gKNyK89dVj0gBsxO1y2Z3azfX6N9OHx7tB%2FjMmV31qKOokD8AdtWjp2bP2v2ylNFWRf%2FfwfmLwnlJsNL%2BgYj%2BgWeBKRw9QeCuVPFGuP%2BcThsiOf3Y32H%2Fbz%2FIuRBuuR7bUWbNsMYndoDJHzg9OEJoWQCIp38A%2B7irjuN9eVLqiZgLhRL3v5VxDvFe%2B%2BHKud%2Fv0r%2B6VTrAMJlZNe%2FQFbmz5V6nbR%2BH1iXTsf3TC3qPdyIkGzC78ECLtW5AKuQkYbBVoTCIY9rYyohpM%2BYF1%2F15hXX20%3D&amp;pass_ticket=WlBRs0nhJNq69QD7TVc6KB2Ds1JS6UxJczf2b%2Fx1vq3%2Ffi18EldMA5ne5KP84waa&amp;wx_header=3</shareUrlOpen>\n\t\t\t<jsAppId />\n\t\t\t<publisherId>brand_profile</publisherId>\n\t\t\t<publisherReqId>1492576348</publisherReqId>\n\t\t</webviewshared>\n\t\t<template_id />\n\t\t<md5>2f8b67c96ec576da712d69efffcfe32d</md5>\n\t\t<websearch>\n\t\t\t<rec_category>0</rec_category>\n\t\t\t<channelId>0</channelId>\n\t\t</websearch>\n\t\t<weappinfo>\n\t\t\t<username />\n\t\t\t<appid />\n\t\t\t<appservicetype>0</appservicetype>\n\t\t\t<secflagforsinglepagemode>0</secflagforsinglepagemode>\n\t\t\t<videopageinfo>\n\t\t\t\t<thumbwidth>120</thumbwidth>\n\t\t\t\t<thumbheight>120</thumbheight>\n\t\t\t\t<fromopensdk>0</fromopensdk>\n\t\t\t</videopageinfo>\n\t\t</weappinfo>\n\t\t<statextstr />\n\t\t<mmreadershare>\n\t\t\t<itemshowtype>0</itemshowtype>\n\t\t\t<ispaysubscribe>0</ispaysubscribe>\n\t\t</mmreadershare>\n\t\t<musicShareItem>\n\t\t\t<musicDuration>0</musicDuration>\n\t\t</musicShareItem>\n\t\t<finderLiveProductShare>\n\t\t\t<finderLiveID><![CDATA[]]></finderLiveID>\n\t\t\t<finderUsername><![CDATA[]]></finderUsername>\n\t\t\t<finderObjectID><![CDATA[]]></finderObjectID>\n\t\t\t<finderNonceID><![CDATA[]]></finderNonceID>\n\t\t\t<liveStatus><![CDATA[]]></liveStatus>\n\t\t\t<appId><![CDATA[]]></appId>\n\t\t\t<pagePath><![CDATA[]]></pagePath>\n\t\t\t<productId><![CDATA[]]></productId>\n\t\t\t<coverUrl><![CDATA[]]></coverUrl>\n\t\t\t<productTitle><![CDATA[]]></productTitle>\n\t\t\t<marketPrice><![CDATA[0]]></marketPrice>\n\t\t\t<sellingPrice><![CDATA[0]]></sellingPrice>\n\t\t\t<platformHeadImg><![CDATA[]]></platformHeadImg>\n\t\t\t<platformName><![CDATA[]]></platformName>\n\t\t\t<shopWindowId><![CDATA[]]></shopWindowId>\n\t\t\t<flashSalePrice><![CDATA[0]]></flashSalePrice>\n\t\t\t<flashSaleEndTime><![CDATA[0]]></flashSaleEndTime>\n\t\t\t<ecSource><![CDATA[]]></ecSource>\n\t\t\t<sellingPriceWording><![CDATA[]]></sellingPriceWording>\n\t\t\t<platformIconURL><![CDATA[]]></platformIconURL>\n\t\t\t<firstProductTagURL><![CDATA[]]></firstProductTagURL>\n\t\t\t<firstProductTagAspectRatioString><![CDATA[0.0]]></firstProductTagAspectRatioString>\n\t\t\t<secondProductTagURL><![CDATA[]]></secondProductTagURL>\n\t\t\t<secondProductTagAspectRatioString><![CDATA[0.0]]></secondProductTagAspectRatioString>\n\t\t\t<firstGuaranteeWording><![CDATA[]]></firstGuaranteeWording>\n\t\t\t<secondGuaranteeWording><![CDATA[]]></secondGuaranteeWording>\n\t\t\t<thirdGuaranteeWording><![CDATA[]]></thirdGuaranteeWording>\n\t\t\t<isPriceBeginShow>false</isPriceBeginShow>\n\t\t\t<lastGMsgID><![CDATA[]]></lastGMsgID>\n\t\t\t<promoterKey><![CDATA[]]></promoterKey>\n\t\t\t<discountWording><![CDATA[]]></discountWording>\n\t\t\t<priceSuffixDescription><![CDATA[]]></priceSuffixDescription>\n\t\t\t<productCardKey><![CDATA[]]></productCardKey>\n\t\t\t<isWxShop><![CDATA[]]></isWxShop>\n\t\t\t<brandIconUrl><![CDATA[]]></brandIconUrl>\n\t\t\t<rIconUrl><![CDATA[]]></rIconUrl>\n\t\t\t<rIconUrlDarkMode><![CDATA[]]></rIconUrlDarkMode>\n\t\t\t<topShopIconUrl><![CDATA[]]></topShopIconUrl>\n\t\t\t<topShopIconUrlDarkMode><![CDATA[]]></topShopIconUrlDarkMode>\n\t\t\t<simplifyTopShopIconUrl><![CDATA[]]></simplifyTopShopIconUrl>\n\t\t\t<simplifyTopShopIconUrlDarkmode><![CDATA[]]></simplifyTopShopIconUrlDarkmode>\n\t\t\t<topShopIconWidth><![CDATA[0]]></topShopIconWidth>\n\t\t\t<topShopIconHeight><![CDATA[0]]></topShopIconHeight>\n\t\t\t<simplifyTopShopIconWidth><![CDATA[0]]></simplifyTopShopIconWidth>\n\t\t\t<simplifyTopShopIconHeight><![CDATA[0]]></simplifyTopShopIconHeight>\n\t\t\t<showBoxItemStringList />\n\t\t</finderLiveProductShare>\n\t\t<finderOrder>\n\t\t\t<appID><![CDATA[]]></appID>\n\t\t\t<orderID><![CDATA[]]></orderID>\n\t\t\t<path><![CDATA[]]></path>\n\t\t\t<priceWording><![CDATA[]]></priceWording>\n\t\t\t<stateWording><![CDATA[]]></stateWording>\n\t\t\t<productImageURL><![CDATA[]]></productImageURL>\n\t\t\t<products><![CDATA[]]></products>\n\t\t\t<productsCount><![CDATA[0]]></productsCount>\n\t\t\t<orderType><![CDATA[0]]></orderType>\n\t\t\t<newPriceWording><![CDATA[]]></newPriceWording>\n\t\t\t<newStateWording><![CDATA[]]></newStateWording>\n\t\t\t<useNewWording><![CDATA[0]]></useNewWording>\n\t\t</finderOrder>\n\t\t<finderShopWindowShare>\n\t\t\t<finderUsername><![CDATA[]]></finderUsername>\n\t\t\t<avatar><![CDATA[]]></avatar>\n\t\t\t<nickname><![CDATA[]]></nickname>\n\t\t\t<commodityInStockCount><![CDATA[]]></commodityInStockCount>\n\t\t\t<appId><![CDATA[]]></appId>\n\t\t\t<path><![CDATA[]]></path>\n\t\t\t<appUsername><![CDATA[]]></appUsername>\n\t\t\t<query><![CDATA[]]></query>\n\t\t\t<liteAppId><![CDATA[]]></liteAppId>\n\t\t\t<liteAppPath><![CDATA[]]></liteAppPath>\n\t\t\t<liteAppQuery><![CDATA[]]></liteAppQuery>\n\t\t\t<platformTagURL><![CDATA[]]></platformTagURL>\n\t\t\t<saleWording><![CDATA[]]></saleWording>\n\t\t\t<lastGMsgID><![CDATA[]]></lastGMsgID>\n\t\t\t<profileTypeWording><![CDATA[]]></profileTypeWording>\n\t\t\t<saleWordingExtra><![CDATA[]]></saleWordingExtra>\n\t\t\t<isWxShop><![CDATA[]]></isWxShop>\n\t\t\t<platformIconUrl><![CDATA[]]></platformIconUrl>\n\t\t\t<brandIconUrl><![CDATA[]]></brandIconUrl>\n\t\t\t<description><![CDATA[]]></description>\n\t\t\t<backgroundUrl><![CDATA[]]></backgroundUrl>\n\t\t\t<darkModePlatformIconUrl><![CDATA[]]></darkModePlatformIconUrl>\n\t\t\t<rIconUrl><![CDATA[]]></rIconUrl>\n\t\t\t<rIconUrlDarkMode><![CDATA[]]></rIconUrlDarkMode>\n\t\t\t<rWords><![CDATA[]]></rWords>\n\t\t\t<topShopIconUrl><![CDATA[]]></topShopIconUrl>\n\t\t\t<topShopIconUrlDarkMode><![CDATA[]]></topShopIconUrlDarkMode>\n\t\t\t<simplifyTopShopIconUrl><![CDATA[]]></simplifyTopShopIconUrl>\n\t\t\t<simplifyTopShopIconUrlDarkmode><![CDATA[]]></simplifyTopShopIconUrlDarkmode>\n\t\t\t<topShopIconWidth><![CDATA[0]]></topShopIconWidth>\n\t\t\t<topShopIconHeight><![CDATA[0]]></topShopIconHeight>\n\t\t\t<simplifyTopShopIconWidth><![CDATA[0]]></simplifyTopShopIconWidth>\n\t\t\t<simplifyTopShopIconHeight><![CDATA[0]]></simplifyTopShopIconHeight>\n\t\t\t<reputationInfo>\n\t\t\t\t<hasReputationInfo>0</hasReputationInfo>\n\t\t\t\t<reputationScore>0</reputationScore>\n\t\t\t\t<reputationWording />\n\t\t\t\t<reputationTextColor />\n\t\t\t\t<reputationLevelWording />\n\t\t\t\t<reputationBackgroundColor />\n\t\t\t</reputationInfo>\n\t\t\t<productImageURLList />\n\t\t</finderShopWindowShare>\n\t\t<findernamecard>\n\t\t\t<username />\n\t\t\t<avatar><![CDATA[]]></avatar>\n\t\t\t<nickname />\n\t\t\t<auth_job />\n\t\t\t<auth_icon>0</auth_icon>\n\t\t\t<auth_icon_url />\n\t\t\t<ecSource><![CDATA[]]></ecSource>\n\t\t\t<lastGMsgID><![CDATA[]]></lastGMsgID>\n\t\t</findernamecard>\n\t\t<finderGuarantee>\n\t\t\t<scene><![CDATA[0]]></scene>\n\t\t</finderGuarantee>\n\t\t<directshare>0</directshare>\n\t\t<gamecenter>\n\t\t\t<namecard>\n\t\t\t\t<iconUrl />\n\t\t\t\t<name />\n\t\t\t\t<desc />\n\t\t\t\t<tail />\n\t\t\t\t<jumpUrl />\n\t\t\t\t<liteappId />\n\t\t\t\t<liteappPath />\n\t\t\t\t<liteappQuery />\n\t\t\t\t<liteappMinVersion />\n\t\t\t</namecard>\n\t\t</gamecenter>\n\t\t<patMsg>\n\t\t\t<chatUser />\n\t\t\t<records>\n\t\t\t\t<recordNum>0</recordNum>\n\t\t\t</records>\n\t\t</patMsg>\n\t\t<secretmsg>\n\t\t\t<issecretmsg>0</issecretmsg>\n\t\t</secretmsg>\n\t\t<referfromscene>0</referfromscene>\n\t\t<gameshare>\n\t\t\t<liteappext>\n\t\t\t\t<liteappbizdata />\n\t\t\t\t<priority>0</priority>\n\t\t\t</liteappext>\n\t\t\t<appbrandext>\n\t\t\t\t<litegameinfo />\n\t\t\t\t<priority>-1</priority>\n\t\t\t</appbrandext>\n\t\t\t<gameshareid />\n\t\t\t<sharedata />\n\t\t\t<isvideo>0</isvideo>\n\t\t\t<duration>-1</duration>\n\t\t\t<isexposed>0</isexposed>\n\t\t\t<readtext />\n\t\t</gameshare>\n\t\t<tingChatRoomItem>\n\t\t\t<type>0</type>\n\t\t\t<categoryItem>null</categoryItem>\n\t\t\t<categoryId />\n\t\t\t<listenItem>null</listenItem>\n\t\t</tingChatRoomItem>\n\t\t<mpsharetrace>\n\t\t\t<hasfinderelement>0</hasfinderelement>\n\t\t\t<lastgmsgid />\n\t\t</mpsharetrace>\n\t\t<wxgamecard>\n\t\t\t<framesetname />\n\t\t\t<mbcarddata />\n\t\t\t<minpkgversion />\n\t\t\t<clientextinfo />\n\t\t\t<mbcardheight>0</mbcardheight>\n\t\t\t<isoldversion>0</isoldversion>\n\t\t</wxgamecard>\n\t\t<ecskfcard>\n\t\t\t<framesetname />\n\t\t\t<mbcarddata />\n\t\t\t<minupdateunixtimestamp>0</minupdateunixtimestamp>\n\t\t\t<needheader>false</needheader>\n\t\t\t<summary />\n\t\t</ecskfcard>\n\t\t<liteapp>\n\t\t\t<id>null</id>\n\t\t\t<path />\n\t\t\t<query />\n\t\t\t<istransparent>0</istransparent>\n\t\t\t<hideicon>0</hideicon>\n\t\t\t<forbidforward>0</forbidforward>\n\t\t</liteapp>\n\t\t<opensdk_share_is_modified>0</opensdk_share_is_modified>\n\t</appmsg>\n\t<fromusername>wxid_laurnst5xn0q22</fromusername>\n\t<scene>0</scene>\n\t<appinfo>\n\t\t<version>1</version>\n\t\t<appname></appname>\n\t</appinfo>\n\t<commenturl></commenturl>\n</msg>\n'}, 'Status': 3, 'ImgStatus': 2, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1753839252, 'MsgSource': '<msgsource>\n\t<sec_msg_node>\n\t\t<uuid>52098770bbf4212b33d748931ac7ae58_</uuid>\n\t\t<risk-file-flag />\n\t\t<risk-file-md5-list />\n\t</sec_msg_node>\n\t<silence>0</silence>\n\t<membercount>73</membercount>\n\t<signature>N0_V1_EcfbJJi7|v1_+rKTap/2</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'PushContent': 'متسول暗 : [链接]瑶瑶', 'NewMsgId': 5319699836444831885, 'MsgSeq': 871410971}
2025-07-30 09:34:31 | DEBUG | 从群聊消息中提取发送者: wxid_laurnst5xn0q22
2025-07-30 09:34:31 | INFO | 收到公众号文章消息: 消息ID:568624849 来自:48097389945@chatroom
2025-07-30 09:34:31 | ERROR | 解析XML失败: mismatched tag: line 1, column 384
2025-07-30 09:34:31 | DEBUG | [ArticleForwarder] 检测到XML开头被截断，尝试修复
2025-07-30 09:34:31 | DEBUG | [ArticleForwarder] 已修复XML开头截断问题
2025-07-30 09:34:31 | DEBUG | 尝试使用修复后的XML重新解析
2025-07-30 09:34:31 | DEBUG | 从sourcedisplayname提取到公众号: TK痒模
2025-07-30 09:34:31 | DEBUG | 公众号「TK痒模」不在监控列表中，跳过处理
2025-07-30 09:34:32 | DEBUG | 收到消息: {'MsgId': 1403833075, 'FromUserName': {'string': '47325400669@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 1, 'Content': {'string': 'wxid_62fiham2pn7521:\n好'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1753839257, 'MsgSource': '<msgsource>\n\t<pua>1</pua>\n\t<eggIncluded>1</eggIncluded>\n\t<silence>0</silence>\n\t<membercount>222</membercount>\n\t<signature>N0_V1_PCqxUwbv|v1_t0A/a7zz</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'PushContent': '八戒。 : 好', 'NewMsgId': 3931061789352820224, 'MsgSeq': 871410972}
2025-07-30 09:34:32 | INFO | 收到文本消息: 消息ID:1403833075 来自:47325400669@chatroom 发送人:wxid_62fiham2pn7521 @:[] 内容:好
2025-07-30 09:34:32 | DEBUG | 处理消息内容: '好'
2025-07-30 09:34:32 | DEBUG | 消息内容 '好' 不匹配任何命令，忽略
2025-07-30 09:34:34 | DEBUG | 收到消息: {'MsgId': 875221567, 'FromUserName': {'string': '47325400669@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 1, 'Content': {'string': 'wxid_62fiham2pn7521:\n居然不理我'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1753839260, 'MsgSource': '<msgsource>\n\t<pua>1</pua>\n\t<eggIncluded>1</eggIncluded>\n\t<silence>0</silence>\n\t<membercount>222</membercount>\n\t<signature>N0_V1_A6BVAa7Q|v1_BPCbLr7O</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'PushContent': '八戒。 : 居然不理我', 'NewMsgId': 5379970274090858454, 'MsgSeq': 871410973}
2025-07-30 09:34:34 | INFO | 收到文本消息: 消息ID:875221567 来自:47325400669@chatroom 发送人:wxid_62fiham2pn7521 @:[] 内容:居然不理我
2025-07-30 09:34:34 | DEBUG | 处理消息内容: '居然不理我'
2025-07-30 09:34:34 | DEBUG | 消息内容 '居然不理我' 不匹配任何命令，忽略
2025-07-30 09:34:36 | DEBUG | 收到消息: {'MsgId': 448063542, 'FromUserName': {'string': '47325400669@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 47, 'Content': {'string': 'wxid_62fiham2pn7521:\n<msg><emoji fromusername = "wxid_62fiham2pn7521" tousername = "47325400669@chatroom" type="2" idbuffer="media:0_0" md5="2b42c8682f7eeae8d1e43a7e07f083cb" len = "2364817" productid="" androidmd5="2b42c8682f7eeae8d1e43a7e07f083cb" androidlen="2364817" s60v3md5 = "2b42c8682f7eeae8d1e43a7e07f083cb" s60v3len="2364817" s60v5md5 = "2b42c8682f7eeae8d1e43a7e07f083cb" s60v5len="2364817" cdnurl = "http://vweixinf.tc.qq.com/110/20401/stodownload?m=2b42c8682f7eeae8d1e43a7e07f083cb&amp;filekey=30440201010430302e02016e04025348042032623432633836383266376565616538643165343361376530376630383363620203241591040d00000004627466730000000132&amp;hy=SH&amp;storeid=26821caf20003d6b84206bcae0000006e01004fb153482e92f1f1573ffdf27&amp;ef=1&amp;bizid=1022" designerid = "" thumburl = "" encrypturl = "http://vweixinf.tc.qq.com/110/20402/stodownload?m=8fb5a37135409a27df2dd330b07b1130&amp;filekey=30440201010430302e02016e040253480420386662356133373133353430396132376466326464333330623037623131333002032415a0040d00000004627466730000000132&amp;hy=SH&amp;storeid=26821caf20007011a4206bcae0000006e02004fb253482e92f1f1573ffdf4a&amp;ef=2&amp;bizid=1022" aeskey= "df28678d30a343e0aecdd77ff35df1dc" externurl = "http://vweixinf.tc.qq.com/110/20403/stodownload?m=58ae99bec620c8dab3fccb78e7a8578a&amp;filekey=30440201010430302e02016e04025348042035386165393962656336323063386461623366636362373865376138353738610203018c70040d00000004627466730000000132&amp;hy=SH&amp;storeid=26821caf2000a09544206bcae0000006e03004fb353482e92f1f1573ffdf65&amp;ef=3&amp;bizid=1022" externmd5 = "982bedfb64ba749f93005f599bfa0e39" width= "240" height= "240" tpurl= "" tpauthkey= "" attachedtext= "" attachedtextcolor= "" lensid= "" emojiattr= "" linkid= "" desc= "" ></emoji>  </msg>'}, 'Status': 3, 'ImgStatus': 2, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1753839261, 'MsgSource': '<msgsource>\n\t<silence>0</silence>\n\t<membercount>222</membercount>\n\t<signature>N0_V1_pHiqw1r/|v1_JvnvBB/x</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'PushContent': '八戒。在群聊中发了一个表情', 'NewMsgId': 6400407809797233009, 'MsgSeq': 871410974}
2025-07-30 09:34:36 | INFO | 收到表情消息: 消息ID:448063542 来自:47325400669@chatroom 发送人:wxid_62fiham2pn7521 MD5:2b42c8682f7eeae8d1e43a7e07f083cb 大小:2364817
2025-07-30 09:34:36 | DEBUG | [DoubaoImageRecognition] 缓存表情消息: 6400407809797233009
2025-07-30 09:35:44 | DEBUG | 收到消息: {'MsgId': 2033361039, 'FromUserName': {'string': '47325400669@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 1, 'Content': {'string': 'ticu23000:\n话说,企业微信的机器人可以在普通群使用么'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1753839356, 'MsgSource': '<msgsource>\n\t<alnode>\n\t\t<fr>1</fr>\n\t</alnode>\n\t<silence>0</silence>\n\t<membercount>222</membercount>\n\t<signature>N0_V1_yBjOK2PG|v1_4pY7MI2X</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'PushContent': 'Chris_叨叨叨 : 话说,企业微信的机器人可以在普通群使用么', 'NewMsgId': 129689049265198283, 'MsgSeq': 871410979}
2025-07-30 09:35:44 | INFO | 收到文本消息: 消息ID:2033361039 来自:47325400669@chatroom 发送人:ticu23000 @:[] 内容:话说,企业微信的机器人可以在普通群使用么
2025-07-30 09:35:44 | DEBUG | 处理消息内容: '话说,企业微信的机器人可以在普通群使用么'
2025-07-30 09:35:44 | DEBUG | 消息内容 '话说,企业微信的机器人可以在普通群使用么' 不匹配任何命令，忽略
2025-07-30 09:36:18 | DEBUG | 收到消息: {'MsgId': 1603418560, 'FromUserName': {'string': '47325400669@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 1, 'Content': {'string': 'wxid_ubbh6q832tcs21:\n[捂脸]'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1753839390, 'MsgSource': '<msgsource>\n\t<pua>1</pua>\n\t<eggIncluded>1</eggIncluded>\n\t<silence>0</silence>\n\t<membercount>222</membercount>\n\t<signature>N0_V1_zlZA//PX|v1_zcZiP+R1</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'PushContent': '郭 : [捂脸]', 'NewMsgId': 3225517026183311269, 'MsgSeq': 871410980}
2025-07-30 09:36:18 | INFO | 收到表情消息: 消息ID:1603418560 来自:47325400669@chatroom 发送人:wxid_ubbh6q832tcs21 @:[] 内容:[捂脸]
2025-07-30 09:36:18 | DEBUG | [DoubaoImageRecognition] 缓存表情消息: 3225517026183311269
2025-07-30 09:36:20 | DEBUG | 收到消息: {'MsgId': 1072782251, 'FromUserName': {'string': '47325400669@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 1, 'Content': {'string': 'telphy:\n人都进不来还机器人？'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1753839392, 'MsgSource': '<msgsource>\n\t<sec_msg_node>\n\t\t<alnode>\n\t\t\t<fr>1</fr>\n\t\t</alnode>\n\t</sec_msg_node>\n\t<pua>1</pua>\n\t<silence>0</silence>\n\t<membercount>222</membercount>\n\t<signature>N0_V1_CWu5JWVg|v1_+mZwvrll</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'PushContent': '飞天雾 : 人都进不来还机器人？', 'NewMsgId': 5782560015182596288, 'MsgSeq': 871410981}
2025-07-30 09:36:20 | INFO | 收到文本消息: 消息ID:1072782251 来自:47325400669@chatroom 发送人:telphy @:[] 内容:人都进不来还机器人？
2025-07-30 09:36:20 | DEBUG | 处理消息内容: '人都进不来还机器人？'
2025-07-30 09:36:20 | DEBUG | 消息内容 '人都进不来还机器人？' 不匹配任何命令，忽略
2025-07-30 09:36:39 | DEBUG | 收到消息: {'MsgId': 968432635, 'FromUserName': {'string': '27852221909@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 1, 'Content': {'string': 'wxid_558jw1jndlum22:\n签到'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1753839411, 'MsgSource': '<msgsource>\n\t<pua>1</pua>\n\t<eggIncluded>1</eggIncluded>\n\t<silence>1</silence>\n\t<membercount>145</membercount>\n\t<signature>N0_V1_IsVUPZBd|v1_m2n5s2gs</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'NewMsgId': 4030582329131982428, 'MsgSeq': 871410982}
2025-07-30 09:36:39 | INFO | 收到文本消息: 消息ID:968432635 来自:27852221909@chatroom 发送人:wxid_558jw1jndlum22 @:[] 内容:签到
2025-07-30 09:36:39 | DEBUG | 处理消息内容: '签到'
2025-07-30 09:36:39 | DEBUG | 消息内容 '签到' 不匹配任何命令，忽略
2025-07-30 09:36:39 | INFO | 数据库: 用户wxid_558jw1jndlum22登录时间设置为2025-07-30 00:00:00+08:00
2025-07-30 09:36:39 | INFO | 数据库: 用户wxid_558jw1jndlum22连续签到天数设置为1
2025-07-30 09:36:39 | INFO | 数据库: 用户wxid_558jw1jndlum22积分增加11
2025-07-30 09:36:40 | INFO | 发送文字消息: 对方wxid:27852221909@chatroom at:['wxid_558jw1jndlum22'] 内容:@上上签。 
-----XYBot-----
签到成功！你领到了 11 个积分！✅
你是今天第 2 个签到的！🎉

2025-07-30 09:36:40 | DEBUG | 收到消息: {'MsgId': 1771531296, 'FromUserName': {'string': '47325400669@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 47, 'Content': {'string': 'ticu23000:\n<msg><emoji fromusername = "ticu23000" tousername = "47325400669@chatroom" type="2" idbuffer="media:0_0" md5="f9219dbd7a7cf7cf8c05f98e9bbe30e3" len = "511922" productid="com.tencent.xin.emoticon.person.stiker_166857732603f2cdcebfbe021f" androidmd5="f9219dbd7a7cf7cf8c05f98e9bbe30e3" androidlen="511922" s60v3md5 = "f9219dbd7a7cf7cf8c05f98e9bbe30e3" s60v3len="511922" s60v5md5 = "f9219dbd7a7cf7cf8c05f98e9bbe30e3" s60v5len="511922" cdnurl = "http://wxapp.tc.qq.com/262/20304/stodownload?m=f9219dbd7a7cf7cf8c05f98e9bbe30e3&amp;filekey=30350201010421301f02020106040253480410f9219dbd7a7cf7cf8c05f98e9bbe30e3020307cfb2040d00000004627466730000000132&amp;hy=SH&amp;storeid=2637357a5000e13b7070103c70000010600004f5053482f00d8b0b63cc0c28&amp;bizid=1023" designerid = "" thumburl = "http://wxapp.tc.qq.com/275/20304/stodownload?m=326515bebb30eb9de874e8d6c9c791ad&amp;filekey=30350201010421301f02020113040253480410326515bebb30eb9de874e8d6c9c791ad0203008101040d00000004627466730000000132&amp;hy=SH&amp;storeid=26479c6aa0004f73d000000000000011300004f5053482c0e2b01e65401cc0&amp;bizid=1023" encrypturl = "http://wxapp.tc.qq.com/262/20304/stodownload?m=c1750e3aef936470a5f58ed7cd1d7bb5&amp;filekey=30350201010421301f02020106040253480410c1750e3aef936470a5f58ed7cd1d7bb5020307cfc0040d00000004627466730000000132&amp;hy=SH&amp;storeid=2637357a60001898f070103c70000010600004f5053482f3028e0b6ccb040d&amp;bizid=1023" aeskey= "4fbdc0334fd292ee427a7b565489695f" externurl = "http://wxapp.tc.qq.com/262/20304/stodownload?m=8a91412054fe8d9c97bb85383cd65d46&amp;filekey=30350201010421301f020201060402535a04108a91412054fe8d9c97bb85383cd65d460203012c40040d00000004627466730000000132&amp;hy=SZ&amp;storeid=26375fb2200003ede47b804930000010600004f50535a125b4970b03438cd9&amp;bizid=1023" externmd5 = "e6388a67fd4ff9d800b246d474fc6fff" width= "240" height= "240" tpurl= "" tpauthkey= "" attachedtext= "" attachedtextcolor= "" lensid= "" emojiattr= "" linkid= "" desc= "ChcKB2RlZmF1bHQSDOmAguWPr+iAjOatog==" ></emoji> <gameext type="0" content="0" ></gameext> </msg>'}, 'Status': 3, 'ImgStatus': 2, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1753839412, 'MsgSource': '<msgsource>\n\t<silence>0</silence>\n\t<membercount>222</membercount>\n\t<signature>N0_V1_ZNZ51fGo|v1_tbKgYQnu</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'PushContent': 'Chris_叨叨叨在群聊中发了一个表情', 'NewMsgId': 551261296774465047, 'MsgSeq': 871410985}
2025-07-30 09:36:40 | INFO | 收到表情消息: 消息ID:1771531296 来自:47325400669@chatroom 发送人:ticu23000 MD5:f9219dbd7a7cf7cf8c05f98e9bbe30e3 大小:511922
2025-07-30 09:36:40 | DEBUG | [DoubaoImageRecognition] 缓存表情消息: 551261296774465047
2025-07-30 09:36:46 | DEBUG | 收到消息: {'MsgId': 208845687, 'FromUserName': {'string': '27852221909@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 1, 'Content': {'string': 'wxid_95kdb9mikm3o22:\n签到'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1753839418, 'MsgSource': '<msgsource>\n\t<pua>1</pua>\n\t<eggIncluded>1</eggIncluded>\n\t<silence>1</silence>\n\t<membercount>145</membercount>\n\t<signature>N0_V1_h6aZZQl0|v1_Qp3hiz7j</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'NewMsgId': 1756290093060252707, 'MsgSeq': 871410986}
2025-07-30 09:36:46 | INFO | 收到文本消息: 消息ID:208845687 来自:27852221909@chatroom 发送人:wxid_95kdb9mikm3o22 @:[] 内容:签到
2025-07-30 09:36:46 | DEBUG | 处理消息内容: '签到'
2025-07-30 09:36:46 | DEBUG | 消息内容 '签到' 不匹配任何命令，忽略
2025-07-30 09:36:46 | INFO | 数据库: 用户wxid_95kdb9mikm3o22登录时间设置为2025-07-30 00:00:00+08:00
2025-07-30 09:36:46 | INFO | 数据库: 用户wxid_95kdb9mikm3o22连续签到天数设置为1
2025-07-30 09:36:46 | INFO | 数据库: 用户wxid_95kdb9mikm3o22积分增加15
2025-07-30 09:36:47 | INFO | 发送文字消息: 对方wxid:27852221909@chatroom at:['wxid_95kdb9mikm3o22'] 内容:@ཐིཋྀ 
-----XYBot-----
签到成功！你领到了 15 个积分！✅
你是今天第 3 个签到的！🎉

2025-07-30 09:36:51 | DEBUG | 收到消息: {'MsgId': 11443789, 'FromUserName': {'string': '48097389945@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 1, 'Content': {'string': 'wxid_laurnst5xn0q22:\n黑丝'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1753839423, 'MsgSource': '<msgsource>\n\t<pua>1</pua>\n\t<eggIncluded>1</eggIncluded>\n\t<silence>0</silence>\n\t<membercount>73</membercount>\n\t<signature>N0_V1_ijBXCikY|v1_3MBiRQJO</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'PushContent': 'متسول暗 : 黑丝', 'NewMsgId': 6389956040883472538, 'MsgSeq': 871410989}
2025-07-30 09:36:51 | INFO | 收到文本消息: 消息ID:11443789 来自:48097389945@chatroom 发送人:wxid_laurnst5xn0q22 @:[] 内容:黑丝
2025-07-30 09:36:52 | DEBUG | 处理消息内容: '黑丝'
2025-07-30 09:36:52 | DEBUG | 消息内容 '黑丝' 不匹配任何命令，忽略
2025-07-30 09:36:54 | DEBUG | 收到消息: {'MsgId': 94112782, 'FromUserName': {'string': '48097389945@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 3, 'Content': {'string': 'wxid_8l9ymg1mafud12:\n<?xml version="1.0"?>\n<msg>\n\t<img aeskey="767177776b6a6573736d7a6767777a75" encryver="0" cdnthumbaeskey="767177776b6a6573736d7a6767777a75" cdnthumburl="3057020100044b3049020100020499f11ce202033d11fe020478d0533b020468897742042462653538306339392d343638652d343330312d383230622d3566636333616536326539390204052428010201000405004c4d9a00b67695f5" cdnthumblength="3001" cdnthumbheight="100" cdnthumbwidth="56" cdnmidheight="0" cdnmidwidth="0" cdnhdheight="0" cdnhdwidth="0" cdnmidimgurl="3057020100044b3049020100020499f11ce202033d11fe020478d0533b020468897742042462653538306339392d343638652d343330312d383230622d3566636333616536326539390204052428010201000405004c4d9a00b67695f5" length="87223" cdnbigimgurl="3057020100044b3049020100020499f11ce202033d11fe020478d0533b020468897742042462653538306339392d343638652d343330312d383230622d3566636333616536326539390204052428010201000405004c4d9a00b67695f5" hdlength="149387" md5="7154e52416983ff2e707f06652593da4">\n\t\t<secHashInfoBase64 />\n\t\t<live>\n\t\t\t<duration>0</duration>\n\t\t\t<size>0</size>\n\t\t\t<md5 />\n\t\t\t<fileid />\n\t\t\t<hdsize>0</hdsize>\n\t\t\t<hdmd5 />\n\t\t\t<hdfileid />\n\t\t\t<stillimagetimems>0</stillimagetimems>\n\t\t</live>\n\t</img>\n\t<platform_signature />\n\t<imgdatahash />\n\t<ImgSourceInfo>\n\t\t<ImgSourceUrl />\n\t\t<BizType>0</BizType>\n\t</ImgSourceInfo>\n</msg>\n'}, 'Status': 3, 'ImgStatus': 2, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1753839426, 'MsgSource': '<msgsource>\n\t<sec_msg_node>\n\t\t<uuid>b8748f2354079024d564424d6175dc26_</uuid>\n\t\t<risk-file-flag />\n\t\t<risk-file-md5-list />\n\t</sec_msg_node>\n\t<silence>0</silence>\n\t<membercount>73</membercount>\n\t<signature>N0_V1_6rMjX7Xj|v1_oP5pJKCp</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'PushContent': '不吃香菜\ue110在群聊中发了一张图片', 'NewMsgId': 5675672693600584664, 'MsgSeq': 871410990}
2025-07-30 09:36:54 | INFO | 收到图片消息: 消息ID:94112782 来自:48097389945@chatroom 发送人:wxid_8l9ymg1mafud12 XML:<?xml version="1.0"?><msg><img aeskey="767177776b6a6573736d7a6767777a75" encryver="0" cdnthumbaeskey="767177776b6a6573736d7a6767777a75" cdnthumburl="3057020100044b3049020100020499f11ce202033d11fe020478d0533b020468897742042462653538306339392d343638652d343330312d383230622d3566636333616536326539390204052428010201000405004c4d9a00b67695f5" cdnthumblength="3001" cdnthumbheight="100" cdnthumbwidth="56" cdnmidheight="0" cdnmidwidth="0" cdnhdheight="0" cdnhdwidth="0" cdnmidimgurl="3057020100044b3049020100020499f11ce202033d11fe020478d0533b020468897742042462653538306339392d343638652d343330312d383230622d3566636333616536326539390204052428010201000405004c4d9a00b67695f5" length="87223" cdnbigimgurl="3057020100044b3049020100020499f11ce202033d11fe020478d0533b020468897742042462653538306339392d343638652d343330312d383230622d3566636333616536326539390204052428010201000405004c4d9a00b67695f5" hdlength="149387" md5="7154e52416983ff2e707f06652593da4"><secHashInfoBase64 /><live><duration>0</duration><size>0</size><md5 /><fileid /><hdsize>0</hdsize><hdmd5 /><hdfileid /><stillimagetimems>0</stillimagetimems></live></img><platform_signature /><imgdatahash /><ImgSourceInfo><ImgSourceUrl /><BizType>0</BizType></ImgSourceInfo></msg>
2025-07-30 09:36:55 | INFO | [ImageEcho] 保存图片信息成功，当前群 48097389945@chatroom 已存储 5 张图片
2025-07-30 09:36:55 | INFO | [TimerTask] 缓存图片消息: 94112782
2025-07-30 09:36:59 | DEBUG | 收到消息: {'MsgId': 1916645233, 'FromUserName': {'string': '48097389945@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 3, 'Content': {'string': 'wxid_srknxij3jka022:\n<?xml version="1.0"?>\n<msg>\n\t<img aeskey="69b35adca3a3f21736bd6540680e747c" encryver="1" cdnthumbaeskey="69b35adca3a3f21736bd6540680e747c" cdnthumburl="3057020100044b304902010002049c5da34302032e6c63020413ab016f020468897746042438343362306634382d633433302d346139312d393937352d616232313738646239323565020405292a010201000405004c54a300" cdnthumblength="4719" cdnthumbheight="120" cdnthumbwidth="95" cdnmidheight="0" cdnmidwidth="0" cdnhdheight="0" cdnhdwidth="0" cdnmidimgurl="3057020100044b304902010002049c5da34302032e6c63020413ab016f020468897746042438343362306634382d633433302d346139312d393937352d616232313738646239323565020405292a010201000405004c54a300" length="15755" cdnbigimgurl="3057020100044b304902010002049c5da34302032e6c63020413ab016f020468897746042438343362306634382d633433302d346139312d393937352d616232313738646239323565020405292a010201000405004c54a300" hdlength="145460" md5="f7140b95c4320302d494af68e1f1ad3a" hevc_mid_size="15755" originsourcemd5="f7140b95c4320302d494af68e1f1ad3a">\n\t\t<secHashInfoBase64 />\n\t\t<live>\n\t\t\t<duration>0</duration>\n\t\t\t<size>0</size>\n\t\t\t<md5 />\n\t\t\t<fileid />\n\t\t\t<hdsize>0</hdsize>\n\t\t\t<hdmd5 />\n\t\t\t<hdfileid />\n\t\t\t<stillimagetimems>0</stillimagetimems>\n\t\t</live>\n\t</img>\n\t<appinfo>\n\t\t<appid>wx0a022393a4dd43b2</appid>\n\t\t<version>5</version>\n\t\t<appname>小爱同学</appname>\n\t\t<isforceupdate>0</isforceupdate>\n\t\t<messageaction />\n\t\t<messageext />\n\t\t<mediatagname />\n\t</appinfo>\n\t<platform_signature />\n\t<imgdatahash />\n\t<ImgSourceInfo>\n\t\t<ImgSourceUrl />\n\t\t<BizType>0</BizType>\n\t</ImgSourceInfo>\n</msg>\n'}, 'Status': 3, 'ImgStatus': 2, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1753839430, 'MsgSource': '<msgsource>\n\t<alnode>\n\t\t<fr>2</fr>\n\t</alnode>\n\t<sec_msg_node>\n\t\t<uuid>3d09179fdc41bd380564631f10de4f76_</uuid>\n\t\t<risk-file-flag />\n\t\t<risk-file-md5-list />\n\t</sec_msg_node>\n\t<silence>0</silence>\n\t<membercount>73</membercount>\n\t<signature>N0_V1_EP52W2UN|v1_QgBMsB7i</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'PushContent': '她在群聊中发了一张图片', 'NewMsgId': 5141489383660936104, 'MsgSeq': 871410991}
2025-07-30 09:36:59 | INFO | 收到图片消息: 消息ID:1916645233 来自:48097389945@chatroom 发送人:wxid_srknxij3jka022 XML:<?xml version="1.0"?><msg><img aeskey="69b35adca3a3f21736bd6540680e747c" encryver="1" cdnthumbaeskey="69b35adca3a3f21736bd6540680e747c" cdnthumburl="3057020100044b304902010002049c5da34302032e6c63020413ab016f020468897746042438343362306634382d633433302d346139312d393937352d616232313738646239323565020405292a010201000405004c54a300" cdnthumblength="4719" cdnthumbheight="120" cdnthumbwidth="95" cdnmidheight="0" cdnmidwidth="0" cdnhdheight="0" cdnhdwidth="0" cdnmidimgurl="3057020100044b304902010002049c5da34302032e6c63020413ab016f020468897746042438343362306634382d633433302d346139312d393937352d616232313738646239323565020405292a010201000405004c54a300" length="15755" cdnbigimgurl="3057020100044b304902010002049c5da34302032e6c63020413ab016f020468897746042438343362306634382d633433302d346139312d393937352d616232313738646239323565020405292a010201000405004c54a300" hdlength="145460" md5="f7140b95c4320302d494af68e1f1ad3a" hevc_mid_size="15755" originsourcemd5="f7140b95c4320302d494af68e1f1ad3a"><secHashInfoBase64 /><live><duration>0</duration><size>0</size><md5 /><fileid /><hdsize>0</hdsize><hdmd5 /><hdfileid /><stillimagetimems>0</stillimagetimems></live></img><appinfo><appid>wx0a022393a4dd43b2</appid><version>5</version><appname>小爱同学</appname><isforceupdate>0</isforceupdate><messageaction /><messageext /><mediatagname /></appinfo><platform_signature /><imgdatahash /><ImgSourceInfo><ImgSourceUrl /><BizType>0</BizType></ImgSourceInfo></msg>
2025-07-30 09:36:59 | INFO | [ImageEcho] 保存图片信息成功，当前群 48097389945@chatroom 已存储 5 张图片
2025-07-30 09:36:59 | INFO | [TimerTask] 缓存图片消息: 1916645233
2025-07-30 09:38:58 | DEBUG | 收到消息: {'MsgId': 124366564, 'FromUserName': {'string': '47325400669@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 1, 'Content': {'string': 'wxid_ubbh6q832tcs21:\n把元宝拉来干活，消息转给元宝，再把元宝发的消息转过来'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1753839550, 'MsgSource': '<msgsource>\n\t<pua>1</pua>\n\t<eggIncluded>1</eggIncluded>\n\t<silence>0</silence>\n\t<membercount>222</membercount>\n\t<signature>N0_V1_qz6gJXEY|v1_X7/avvnY</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'PushContent': '郭 : 把元宝拉来干活，消息转给元宝，再把元宝发的消息转过来', 'NewMsgId': 5156984978793985168, 'MsgSeq': 871410992}
2025-07-30 09:38:58 | INFO | 收到文本消息: 消息ID:124366564 来自:47325400669@chatroom 发送人:wxid_ubbh6q832tcs21 @:[] 内容:把元宝拉来干活，消息转给元宝，再把元宝发的消息转过来
2025-07-30 09:38:58 | DEBUG | 处理消息内容: '把元宝拉来干活，消息转给元宝，再把元宝发的消息转过来'
2025-07-30 09:38:58 | DEBUG | 消息内容 '把元宝拉来干活，消息转给元宝，再把元宝发的消息转过来' 不匹配任何命令，忽略
2025-07-30 09:39:01 | DEBUG | 收到消息: {'MsgId': 18386018, 'FromUserName': {'string': '47325400669@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 47, 'Content': {'string': 'wxid_ubbh6q832tcs21:\n<msg><emoji fromusername = "wxid_ubbh6q832tcs21" tousername = "47325400669@chatroom" type="2" idbuffer="media:0_0" md5="3c5d9173b23ecc025e1ec8c858142b57" len = "1005541" productid="" androidmd5="3c5d9173b23ecc025e1ec8c858142b57" androidlen="1005541" s60v3md5 = "3c5d9173b23ecc025e1ec8c858142b57" s60v3len="1005541" s60v5md5 = "3c5d9173b23ecc025e1ec8c858142b57" s60v5len="1005541" cdnurl = "http://vweixinf.tc.qq.com/110/20401/stodownload?m=3c5d9173b23ecc025e1ec8c858142b57&amp;filekey=30440201010430302e02016e040253480420336335643931373362323365636330323565316563386338353831343262353702030f57e5040d00000004627466730000000132&amp;hy=SH&amp;storeid=2683a773b00015f7c687175f00000006e01004fb153480fd65b01e02592370&amp;ef=1&amp;bizid=1022" designerid = "" thumburl = "" encrypturl = "http://vweixinf.tc.qq.com/110/20402/stodownload?m=4bab97cabbe1706f0a603389b3529eeb&amp;filekey=30440201010430302e02016e040253480420346261623937636162626531373036663061363033333839623335323965656202030f57f0040d00000004627466730000000132&amp;hy=SH&amp;storeid=2683a773b0003cd1f687175f00000006e02004fb253480fd65b01e02592390&amp;ef=2&amp;bizid=1022" aeskey= "0d82b033625c4bf18bc5c338aeb7333e" externurl = "http://vweixinf.tc.qq.com/110/20403/stodownload?m=0064acb5c83b81eef262bb4c27a223c8&amp;filekey=30440201010430302e02016e04025348042030303634616362356338336238316565663236326262346332376132323363380203020430040d00000004627466730000000132&amp;hy=SH&amp;storeid=2683a773b0005d930687175f00000006e03004fb353480fd65b01e025923ad&amp;ef=3&amp;bizid=1022" externmd5 = "ef10f433c759c4681e3dcaecf0b0a32d" width= "640" height= "640" tpurl= "" tpauthkey= "" attachedtext= "" attachedtextcolor= "" lensid= "" emojiattr= "" linkid= "" desc= "" ></emoji>  </msg>'}, 'Status': 3, 'ImgStatus': 2, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1753839553, 'MsgSource': '<msgsource>\n\t<silence>0</silence>\n\t<membercount>222</membercount>\n\t<signature>N0_V1_VO4niUYV|v1_5uPfeSvC</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'PushContent': '郭在群聊中发了一个表情', 'NewMsgId': 7895128953809834590, 'MsgSeq': 871410993}
2025-07-30 09:39:01 | INFO | 收到表情消息: 消息ID:18386018 来自:47325400669@chatroom 发送人:wxid_ubbh6q832tcs21 MD5:3c5d9173b23ecc025e1ec8c858142b57 大小:1005541
2025-07-30 09:39:01 | DEBUG | [DoubaoImageRecognition] 缓存表情消息: 7895128953809834590
2025-07-30 09:40:35 | INFO | [TempFileManager] 开始清理临时文件...
2025-07-30 09:41:04 | DEBUG | 收到消息: {'MsgId': 254117832, 'FromUserName': {'string': '48097389945@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 43, 'Content': {'string': 'xiaomaochong:\n<?xml version="1.0"?>\n<msg>\n\t<videomsg aeskey="5e994c239d1cb81e3f84e6944aa1733a" cdnvideourl="3056020100044a304802010002035a663f02032f5149020498312275020468872f14042430386533396536372d303034382d343566322d393138362d6238633435316662306536640204059400040201000405004c50b900" cdnthumbaeskey="5e994c239d1cb81e3f84e6944aa1733a" cdnthumburl="3056020100044a304802010002035a663f02032f5149020498312275020468872f14042430386533396536372d303034382d343566322d393138362d6238633435316662306536640204059400040201000405004c50b900" length="2643200" playlength="14" cdnthumblength="7268" cdnthumbwidth="720" cdnthumbheight="1280" fromusername="xiaomaochong" md5="a3682e49b38a41b05eacea906ecef66b" newmd5="3d2c70b5ecd2b31845836b36a0d82b0b" isplaceholder="0" rawmd5="a3682e49b38a41b05eacea906ecef66b" rawlength="2643200" cdnrawvideourl="3056020100044a304802010002035a663f02032f514902047531227502046889783c042461666463386335632d373363372d343061392d623462622d6637656365653866366535630204059400040201000405004c55cd00" cdnrawvideoaeskey="5e994c239d1cb81e3f84e6944aa1733a" overwritenewmsgid="0" originsourcemd5="a3682e49b38a41b05eacea906ecef66b" isad="0" />\n</msg>\n'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1753839676, 'MsgSource': '<msgsource>\n\t<videopreloadlen>957647</videopreloadlen>\n\t<sec_msg_node>\n\t\t<uuid>6474bf619239e369ace8b1382bdd2457_</uuid>\n\t\t<risk-file-flag />\n\t\t<risk-file-md5-list />\n\t</sec_msg_node>\n\t<silence>0</silence>\n\t<membercount>73</membercount>\n\t<signature>N0_V1_tb46Pynn|v1_3+HOekAt</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'PushContent': '小爱在群聊中发了一段视频', 'NewMsgId': 3938462887846641781, 'MsgSeq': 871410994}
2025-07-30 09:41:04 | INFO | 收到视频消息: 消息ID:254117832 来自:48097389945@chatroom 发送人:xiaomaochong XML:
<?xml version="1.0"?>
<msg>
	<videomsg aeskey="5e994c239d1cb81e3f84e6944aa1733a" cdnvideourl="3056020100044a304802010002035a663f02032f5149020498312275020468872f14042430386533396536372d303034382d343566322d393138362d6238633435316662306536640204059400040201000405004c50b900" cdnthumbaeskey="5e994c239d1cb81e3f84e6944aa1733a" cdnthumburl="3056020100044a304802010002035a663f02032f5149020498312275020468872f14042430386533396536372d303034382d343566322d393138362d6238633435316662306536640204059400040201000405004c50b900" length="2643200" playlength="14" cdnthumblength="7268" cdnthumbwidth="720" cdnthumbheight="1280" fromusername="xiaomaochong" md5="a3682e49b38a41b05eacea906ecef66b" newmd5="3d2c70b5ecd2b31845836b36a0d82b0b" isplaceholder="0" rawmd5="a3682e49b38a41b05eacea906ecef66b" rawlength="2643200" cdnrawvideourl="3056020100044a304802010002035a663f02032f514902047531227502046889783c042461666463386335632d373363372d343061392d623462622d6637656365653866366535630204059400040201000405004c55cd00" cdnrawvideoaeskey="5e994c239d1cb81e3f84e6944aa1733a" overwritenewmsgid="0" originsourcemd5="a3682e49b38a41b05eacea906ecef66b" isad="0" />
</msg>

2025-07-30 09:47:15 | DEBUG | 收到消息: {'MsgId': 1917786835, 'FromUserName': {'string': '47325400669@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 3, 'Content': {'string': 'wxid_7oa94c4kg4se21:\n<?xml version="1.0"?>\n<msg>\n\t<img aeskey="b27f472d05bdcd4a56003bc2ec3722af" encryver="1" cdnthumbaeskey="b27f472d05bdcd4a56003bc2ec3722af" cdnthumburl="3057020100044b304902010002048223241e02032f9f070204e644306f0204688979ae042439326433653363322d373061342d343863382d613666392d393763663838636466356462020405250a020201000405004c4d9b00" cdnthumblength="3042" cdnthumbheight="70" cdnthumbwidth="144" cdnmidheight="0" cdnmidwidth="0" cdnhdheight="0" cdnhdwidth="0" cdnmidimgurl="3057020100044b304902010002048223241e02032f9f070204e644306f0204688979ae042439326433653363322d373061342d343863382d613666392d393763663838636466356462020405250a020201000405004c4d9b00" length="8874" md5="1dcd6b2fdee3f58e5f2234010e52c511" hevc_mid_size="8874" originsourcemd5="14e1559e578b5d0ee0245cfe38022e09">\n\t\t<secHashInfoBase64 />\n\t\t<live>\n\t\t\t<duration>0</duration>\n\t\t\t<size>0</size>\n\t\t\t<md5 />\n\t\t\t<fileid />\n\t\t\t<hdsize>0</hdsize>\n\t\t\t<hdmd5 />\n\t\t\t<hdfileid />\n\t\t\t<stillimagetimems>0</stillimagetimems>\n\t\t</live>\n\t</img>\n\t<platform_signature />\n\t<imgdatahash />\n\t<ImgSourceInfo>\n\t\t<ImgSourceUrl />\n\t\t<BizType>0</BizType>\n\t</ImgSourceInfo>\n</msg>\n'}, 'Status': 3, 'ImgStatus': 2, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1753840046, 'MsgSource': '<msgsource>\n\t<alnode>\n\t\t<fr>2</fr>\n\t</alnode>\n\t<sec_msg_node>\n\t\t<uuid>55d316e4a6725572df7144e303752c4c_</uuid>\n\t\t<risk-file-flag />\n\t\t<risk-file-md5-list />\n\t</sec_msg_node>\n\t<silence>0</silence>\n\t<membercount>222</membercount>\n\t<signature>N0_V1_r7wrJXiX|v1_08nUcRPY</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'PushContent': '錢在群聊中发了一张图片', 'NewMsgId': 321478377665787259, 'MsgSeq': 871410995}
2025-07-30 09:47:15 | INFO | 收到图片消息: 消息ID:1917786835 来自:47325400669@chatroom 发送人:wxid_7oa94c4kg4se21 XML:<?xml version="1.0"?><msg><img aeskey="b27f472d05bdcd4a56003bc2ec3722af" encryver="1" cdnthumbaeskey="b27f472d05bdcd4a56003bc2ec3722af" cdnthumburl="3057020100044b304902010002048223241e02032f9f070204e644306f0204688979ae042439326433653363322d373061342d343863382d613666392d393763663838636466356462020405250a020201000405004c4d9b00" cdnthumblength="3042" cdnthumbheight="70" cdnthumbwidth="144" cdnmidheight="0" cdnmidwidth="0" cdnhdheight="0" cdnhdwidth="0" cdnmidimgurl="3057020100044b304902010002048223241e02032f9f070204e644306f0204688979ae042439326433653363322d373061342d343863382d613666392d393763663838636466356462020405250a020201000405004c4d9b00" length="8874" md5="1dcd6b2fdee3f58e5f2234010e52c511" hevc_mid_size="8874" originsourcemd5="14e1559e578b5d0ee0245cfe38022e09"><secHashInfoBase64 /><live><duration>0</duration><size>0</size><md5 /><fileid /><hdsize>0</hdsize><hdmd5 /><hdfileid /><stillimagetimems>0</stillimagetimems></live></img><platform_signature /><imgdatahash /><ImgSourceInfo><ImgSourceUrl /><BizType>0</BizType></ImgSourceInfo></msg>
2025-07-30 09:47:15 | INFO | [ImageEcho] 保存图片信息成功，当前群 47325400669@chatroom 已存储 5 张图片
2025-07-30 09:47:15 | INFO | [TimerTask] 缓存图片消息: 1917786835
2025-07-30 09:47:22 | DEBUG | 收到消息: {'MsgId': 978100230, 'FromUserName': {'string': '47325400669@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 1, 'Content': {'string': 'wxid_7oa94c4kg4se21:\n看图猜成语，谁知道这个是什么'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1753840054, 'MsgSource': '<msgsource>\n\t<pua>1</pua>\n\t<silence>0</silence>\n\t<membercount>222</membercount>\n\t<signature>N0_V1_zEITYyee|v1_YWUz1Z/8</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'PushContent': '錢 : 看图猜成语，谁知道这个是什么', 'NewMsgId': 8154875967926926638, 'MsgSeq': 871410996}
2025-07-30 09:47:22 | INFO | 收到文本消息: 消息ID:978100230 来自:47325400669@chatroom 发送人:wxid_7oa94c4kg4se21 @:[] 内容:看图猜成语，谁知道这个是什么
2025-07-30 09:47:22 | DEBUG | 处理消息内容: '看图猜成语，谁知道这个是什么'
2025-07-30 09:47:22 | DEBUG | 消息内容 '看图猜成语，谁知道这个是什么' 不匹配任何命令，忽略
2025-07-30 09:50:02 | DEBUG | 收到消息: {'MsgId': 756841364, 'FromUserName': {'string': '47325400669@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 49, 'Content': {'string': 'gjuse11:\n<?xml version="1.0"?>\n<msg>\n\t<appmsg appid="" sdkver="0">\n\t\t<title>老猪猜谜语</title>\n\t\t<type>57</type>\n\t\t<appattach>\n\t\t\t<cdnthumbaeskey />\n\t\t\t<aeskey />\n\t\t</appattach>\n\t\t<refermsg>\n\t\t\t<type>3</type>\n\t\t\t<svrid>321478377665787259</svrid>\n\t\t\t<fromusr>47325400669@chatroom</fromusr>\n\t\t\t<chatusr>wxid_7oa94c4kg4se21</chatusr>\n\t\t\t<displayname>錢</displayname>\n\t\t\t<content>&lt;?xml version="1.0"?&gt;\n&lt;msg&gt;\n\t&lt;img aeskey="b27f472d05bdcd4a56003bc2ec3722af" encryver="1" cdnthumbaeskey="b27f472d05bdcd4a56003bc2ec3722af" cdnthumburl="3057020100044b304902010002048223241e02032f9f070204e644306f0204688979ae042439326433653363322d373061342d343863382d613666392d393763663838636466356462020405250a020201000405004c4d9b00" cdnthumblength="3042" cdnthumbheight="70" cdnthumbwidth="144" cdnmidheight="0" cdnmidwidth="0" cdnhdheight="0" cdnhdwidth="0" cdnmidimgurl="3057020100044b304902010002048223241e02032f9f070204e644306f0204688979ae042439326433653363322d373061342d343863382d613666392d393763663838636466356462020405250a020201000405004c4d9b00" length="8874" md5="1dcd6b2fdee3f58e5f2234010e52c511" hevc_mid_size="8874" originsourcemd5="14e1559e578b5d0ee0245cfe38022e09"&gt;\n\t\t&lt;secHashInfoBase64 /&gt;\n\t\t&lt;live&gt;\n\t\t\t&lt;duration&gt;0&lt;/duration&gt;\n\t\t\t&lt;size&gt;0&lt;/size&gt;\n\t\t\t&lt;md5 /&gt;\n\t\t\t&lt;fileid /&gt;\n\t\t\t&lt;hdsize&gt;0&lt;/hdsize&gt;\n\t\t\t&lt;hdmd5 /&gt;\n\t\t\t&lt;hdfileid /&gt;\n\t\t\t&lt;stillimagetimems&gt;0&lt;/stillimagetimems&gt;\n\t\t&lt;/live&gt;\n\t&lt;/img&gt;\n\t&lt;platform_signature /&gt;\n\t&lt;imgdatahash /&gt;\n\t&lt;ImgSourceInfo&gt;\n\t\t&lt;ImgSourceUrl /&gt;\n\t\t&lt;BizType&gt;0&lt;/BizType&gt;\n\t&lt;/ImgSourceInfo&gt;\n&lt;/msg&gt;\n</content>\n\t\t\t<msgsource>&lt;msgsource&gt;&lt;sequence_id&gt;772750741&lt;/sequence_id&gt;\n\t&lt;alnode&gt;\n\t\t&lt;fr&gt;2&lt;/fr&gt;\n\t&lt;/alnode&gt;\n\t&lt;sec_msg_node&gt;\n\t\t&lt;uuid&gt;55d316e4a6725572df7144e303752c4c_&lt;/uuid&gt;\n\t\t&lt;risk-file-flag /&gt;\n\t\t&lt;risk-file-md5-list /&gt;\n\t&lt;/sec_msg_node&gt;\n\t&lt;imgmsg_pd cdnmidimgurl_size="8874" cdnmidimgurl_pd_pri="50" cdnmidimgurl_pd="1" /&gt;\n\t&lt;silence&gt;0&lt;/silence&gt;\n\t&lt;membercount&gt;222&lt;/membercount&gt;\n\t&lt;NotAutoDownloadRange&gt;20:00-22:00;00:00-01:00&lt;/NotAutoDownloadRange&gt;\n\t&lt;signature&gt;N0_V1_ofLCpPyM|v1_tBhRMtub&lt;/signature&gt;\n\t&lt;tmp_node&gt;\n\t\t&lt;publisher-id&gt;&lt;/publisher-id&gt;\n\t&lt;/tmp_node&gt;\n&lt;/msgsource&gt;\n</msgsource>\n\t\t\t<createtime>1753840046</createtime>\n\t\t</refermsg>\n\t</appmsg>\n\t<fromusername>gjuse11</fromusername>\n\t<scene>0</scene>\n\t<appinfo>\n\t\t<version>1</version>\n\t\t<appname></appname>\n\t</appinfo>\n\t<commenturl></commenturl>\n</msg>\n'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1753840214, 'MsgSource': '<msgsource>\n\t<bizflag>0</bizflag>\n\t<pua>1</pua>\n\t<eggIncluded>1</eggIncluded>\n\t<sec_msg_node>\n\t\t<uuid>3fba56416664d41d0e3f7854af757405_</uuid>\n\t\t<risk-file-flag />\n\t\t<risk-file-md5-list />\n\t</sec_msg_node>\n\t<silence>0</silence>\n\t<membercount>222</membercount>\n\t<signature>N0_V1_6ZZ5FVKy|v1_onN2QigT</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'PushContent': 'Jayson先森 : 老猪猜谜语', 'NewMsgId': 4957086681679951464, 'MsgSeq': 871410997}
2025-07-30 09:50:02 | DEBUG | 从群聊消息中提取发送者: gjuse11
2025-07-30 09:50:02 | DEBUG | 使用已解析的XML处理引用消息
2025-07-30 09:50:02 | INFO | 收到引用消息: 消息ID:756841364 来自:47325400669@chatroom 发送人:gjuse11 内容:老猪猜谜语 引用类型:3
2025-07-30 09:50:02 | INFO | [DouBaoImageToImage] 收到引用消息: 老猪猜谜语
2025-07-30 09:50:02 | INFO | [TimerTask] 收到引用消息调试信息:
2025-07-30 09:50:02 | INFO |   - 消息内容: 老猪猜谜语
2025-07-30 09:50:02 | INFO |   - 群组ID: 47325400669@chatroom
2025-07-30 09:50:02 | INFO |   - 发送人: gjuse11
2025-07-30 09:50:02 | INFO |   - 引用信息: {'MsgType': 3, 'Content': '<?xml version="1.0"?>\n<msg>\n\t<appmsg appid="" sdkver="0">\n\t\t<title>老猪猜谜语</title>\n\t\t<type>57</type>\n\t\t<appattach>\n\t\t\t<cdnthumbaeskey />\n\t\t\t<aeskey />\n\t\t</appattach>\n\t\t<refermsg>\n\t\t\t<type>3</type>\n\t\t\t<svrid>321478377665787259</svrid>\n\t\t\t<fromusr>47325400669@chatroom</fromusr>\n\t\t\t<chatusr>wxid_7oa94c4kg4se21</chatusr>\n\t\t\t<displayname>錢</displayname>\n\t\t\t<content>&lt;?xml version="1.0"?&gt;\n&lt;msg&gt;\n\t&lt;img aeskey="b27f472d05bdcd4a56003bc2ec3722af" encryver="1" cdnthumbaeskey="b27f472d05bdcd4a56003bc2ec3722af" cdnthumburl="3057020100044b304902010002048223241e02032f9f070204e644306f0204688979ae042439326433653363322d373061342d343863382d613666392d393763663838636466356462020405250a020201000405004c4d9b00" cdnthumblength="3042" cdnthumbheight="70" cdnthumbwidth="144" cdnmidheight="0" cdnmidwidth="0" cdnhdheight="0" cdnhdwidth="0" cdnmidimgurl="3057020100044b304902010002048223241e02032f9f070204e644306f0204688979ae042439326433653363322d373061342d343863382d613666392d393763663838636466356462020405250a020201000405004c4d9b00" length="8874" md5="1dcd6b2fdee3f58e5f2234010e52c511" hevc_mid_size="8874" originsourcemd5="14e1559e578b5d0ee0245cfe38022e09"&gt;\n\t\t&lt;secHashInfoBase64 /&gt;\n\t\t&lt;live&gt;\n\t\t\t&lt;duration&gt;0&lt;/duration&gt;\n\t\t\t&lt;size&gt;0&lt;/size&gt;\n\t\t\t&lt;md5 /&gt;\n\t\t\t&lt;fileid /&gt;\n\t\t\t&lt;hdsize&gt;0&lt;/hdsize&gt;\n\t\t\t&lt;hdmd5 /&gt;\n\t\t\t&lt;hdfileid /&gt;\n\t\t\t&lt;stillimagetimems&gt;0&lt;/stillimagetimems&gt;\n\t\t&lt;/live&gt;\n\t&lt;/img&gt;\n\t&lt;platform_signature /&gt;\n\t&lt;imgdatahash /&gt;\n\t&lt;ImgSourceInfo&gt;\n\t\t&lt;ImgSourceUrl /&gt;\n\t\t&lt;BizType&gt;0&lt;/BizType&gt;\n\t&lt;/ImgSourceInfo&gt;\n&lt;/msg&gt;\n</content>\n\t\t\t<msgsource>&lt;msgsource&gt;&lt;sequence_id&gt;772750741&lt;/sequence_id&gt;\n\t&lt;alnode&gt;\n\t\t&lt;fr&gt;2&lt;/fr&gt;\n\t&lt;/alnode&gt;\n\t&lt;sec_msg_node&gt;\n\t\t&lt;uuid&gt;55d316e4a6725572df7144e303752c4c_&lt;/uuid&gt;\n\t\t&lt;risk-file-flag /&gt;\n\t\t&lt;risk-file-md5-list /&gt;\n\t&lt;/sec_msg_node&gt;\n\t&lt;imgmsg_pd cdnmidimgurl_size="8874" cdnmidimgurl_pd_pri="50" cdnmidimgurl_pd="1" /&gt;\n\t&lt;silence&gt;0&lt;/silence&gt;\n\t&lt;membercount&gt;222&lt;/membercount&gt;\n\t&lt;NotAutoDownloadRange&gt;20:00-22:00;00:00-01:00&lt;/NotAutoDownloadRange&gt;\n\t&lt;signature&gt;N0_V1_ofLCpPyM|v1_tBhRMtub&lt;/signature&gt;\n\t&lt;tmp_node&gt;\n\t\t&lt;publisher-id&gt;&lt;/publisher-id&gt;\n\t&lt;/tmp_node&gt;\n&lt;/msgsource&gt;\n</msgsource>\n\t\t\t<createtime>1753840046</createtime>\n\t\t</refermsg>\n\t</appmsg>\n\t<fromusername>gjuse11</fromusername>\n\t<scene>0</scene>\n\t<appinfo>\n\t\t<version>1</version>\n\t\t<appname></appname>\n\t</appinfo>\n\t<commenturl></commenturl>\n</msg>\n', 'Msgid': '321478377665787259', 'NewMsgId': '321478377665787259', 'ToWxid': 'wxid_4usgcju5ey9q29', 'FromWxid': '47325400669@chatroom', 'Nickname': '錢', 'MsgSource': '<msgsource><sequence_id>772750741</sequence_id>\n\t<alnode>\n\t\t<fr>2</fr>\n\t</alnode>\n\t<sec_msg_node>\n\t\t<uuid>55d316e4a6725572df7144e303752c4c_</uuid>\n\t\t<risk-file-flag />\n\t\t<risk-file-md5-list />\n\t</sec_msg_node>\n\t<imgmsg_pd cdnmidimgurl_size="8874" cdnmidimgurl_pd_pri="50" cdnmidimgurl_pd="1" />\n\t<silence>0</silence>\n\t<membercount>222</membercount>\n\t<NotAutoDownloadRange>20:00-22:00;00:00-01:00</NotAutoDownloadRange>\n\t<signature>N0_V1_ofLCpPyM|v1_tBhRMtub</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'Createtime': '1753840046', 'SenderWxid': 'gjuse11'}
2025-07-30 09:50:02 | INFO |   - 引用消息ID: 
2025-07-30 09:50:02 | INFO |   - 引用消息类型: 
2025-07-30 09:50:02 | INFO |   - 引用消息内容: <?xml version="1.0"?>
<msg>
	<appmsg appid="" sdkver="0">
		<title>老猪猜谜语</title>
		<type>57</type>
		<appattach>
			<cdnthumbaeskey />
			<aeskey />
		</appattach>
		<refermsg>
			<type>3</type>
			<svrid>321478377665787259</svrid>
			<fromusr>47325400669@chatroom</fromusr>
			<chatusr>wxid_7oa94c4kg4se21</chatusr>
			<displayname>錢</displayname>
			<content>&lt;?xml version="1.0"?&gt;
&lt;msg&gt;
	&lt;img aeskey="b27f472d05bdcd4a56003bc2ec3722af" encryver="1" cdnthumbaeskey="b27f472d05bdcd4a56003bc2ec3722af" cdnthumburl="3057020100044b304902010002048223241e02032f9f070204e644306f0204688979ae042439326433653363322d373061342d343863382d613666392d393763663838636466356462020405250a020201000405004c4d9b00" cdnthumblength="3042" cdnthumbheight="70" cdnthumbwidth="144" cdnmidheight="0" cdnmidwidth="0" cdnhdheight="0" cdnhdwidth="0" cdnmidimgurl="3057020100044b304902010002048223241e02032f9f070204e644306f0204688979ae042439326433653363322d373061342d343863382d613666392d393763663838636466356462020405250a020201000405004c4d9b00" length="8874" md5="1dcd6b2fdee3f58e5f2234010e52c511" hevc_mid_size="8874" originsourcemd5="14e1559e578b5d0ee0245cfe38022e09"&gt;
		&lt;secHashInfoBase64 /&gt;
		&lt;live&gt;
			&lt;duration&gt;0&lt;/duration&gt;
			&lt;size&gt;0&lt;/size&gt;
			&lt;md5 /&gt;
			&lt;fileid /&gt;
			&lt;hdsize&gt;0&lt;/hdsize&gt;
			&lt;hdmd5 /&gt;
			&lt;hdfileid /&gt;
			&lt;stillimagetimems&gt;0&lt;/stillimagetimems&gt;
		&lt;/live&gt;
	&lt;/img&gt;
	&lt;platform_signature /&gt;
	&lt;imgdatahash /&gt;
	&lt;ImgSourceInfo&gt;
		&lt;ImgSourceUrl /&gt;
		&lt;BizType&gt;0&lt;/BizType&gt;
	&lt;/ImgSourceInfo&gt;
&lt;/msg&gt;
</content>
			<msgsource>&lt;msgsource&gt;&lt;sequence_id&gt;772750741&lt;/sequence_id&gt;
	&lt;alnode&gt;
		&lt;fr&gt;2&lt;/fr&gt;
	&lt;/alnode&gt;
	&lt;sec_msg_node&gt;
		&lt;uuid&gt;55d316e4a6725572df7144e303752c4c_&lt;/uuid&gt;
		&lt;risk-file-flag /&gt;
		&lt;risk-file-md5-list /&gt;
	&lt;/sec_msg_node&gt;
	&lt;imgmsg_pd cdnmidimgurl_size="8874" cdnmidimgurl_pd_pri="50" cdnmidimgurl_pd="1" /&gt;
	&lt;silence&gt;0&lt;/silence&gt;
	&lt;membercount&gt;222&lt;/membercount&gt;
	&lt;NotAutoDownloadRange&gt;20:00-22:00;00:00-01:00&lt;/NotAutoDownloadRange&gt;
	&lt;signature&gt;N0_V1_ofLCpPyM|v1_tBhRMtub&lt;/signature&gt;
	&lt;tmp_node&gt;
		&lt;publisher-id&gt;&lt;/publisher-id&gt;
	&lt;/tmp_node&gt;
&lt;/msgsource&gt;
</msgsource>
			<createtime>1753840046</createtime>
		</refermsg>
	</appmsg>
	<fromusername>gjuse11</fromusername>
	<scene>0</scene>
	<appinfo>
		<version>1</version>
		<appname></appname>
	</appinfo>
	<commenturl></commenturl>
</msg>

2025-07-30 09:50:02 | INFO |   - 引用消息发送人: gjuse11
