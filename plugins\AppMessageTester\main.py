import os
import time
import httpx
import hashlib
import random
import asyncio
try:
    import tomllib
except ModuleNotFoundError:
    import tomli as tomllib
from loguru import logger

from WechatAPI import WechatAPIClient
from utils.decorators import on_text_message
from utils.plugin_base import PluginBase

class AppMessageTester(PluginBase):
    description = "测试发送应用消息（点歌消息）"
    author = "XYBot用户"
    version = "1.0.0"
    plugin_name = "AppMessageTester"

    def __init__(self):
        # 必须先调用父类初始化
        super().__init__()
        
        # 读取配置
        config = {}
        try:
            config_path = f"plugins/{self.plugin_name}/config.toml"
            if not os.path.exists(config_path):
                logger.warning(f"[{self.plugin_name}] 配置文件 {config_path} 不存在，将使用默认配置")
                config = {}
            else:
                with open(config_path, "rb") as f:
                    plugin_config = tomllib.load(f)
                config = plugin_config.get(self.plugin_name, {})
        except Exception as e:
            logger.error(f"[{self.plugin_name}] 配置文件读取失败: {str(e)}")
            config = {}

        # 基本配置初始化
        self.enable = config.get("enable", True)
        self.command = config.get("command", ["红包来了", "唱舞星愿站", "游戏分享", "链接测试", "视频号分享", "聊天记录", "接龙"])
        self.command_format = config.get("command-format", "发送应用消息测试")

        # 默认聊天记录内容配置（使用不同用户名，确保每条消息都有头像）
        self.default_chat_content = config.get("default_chat_content", "小明:今天天气真不错啊！|小红:是啊，我们去公园走走吧|小李:我也想去")
        
        # 初始化令牌桶限流
        rate_limit_config = config.get("rate_limit", {})
        self.tokens_per_second = rate_limit_config.get("tokens_per_second", 0.1)
        self.bucket_size = rate_limit_config.get("bucket_size", 3)
        self.tokens = self.bucket_size
        self.last_token_time = time.time()
        
        # 用户限流字典
        self.user_last_request = {}

    @on_text_message
    async def handle_text(self, bot: WechatAPIClient, message: dict):
        """处理文本消息"""
        if not self.enable:
            return
            
        content = str(message["Content"]).strip()
        wxid = message["FromWxid"]
        user_wxid = message["SenderWxid"]
        
        # 检查是否是插件命令
        command = content.split(" ", 1)
        if command[0] not in self.command:
            return

        try:
            # 检查用户请求限制
            wait_time = self._check_user_limit(wxid, user_wxid)
            if wait_time > 0:
                await bot.send_at_message(
                    wxid,
                    f"慢点慢点，等 {wait_time:.1f} 秒再来",
                    [user_wxid]
                )
                return

            # 检查令牌桶
            if not await self._acquire_token():
                await bot.send_at_message(
                    wxid,
                    "忙不过来了，歇会儿",
                    [user_wxid]
                )
                return

            # 根据命令发送不同类型的消息
            if command[0] == "红包来了":
                # 发送红包音乐消息
                await self._send_hongbao_music(bot, wxid, user_wxid)
            elif command[0] == "唱舞星愿站":
                # 发送小程序消息
                await self._send_miniprogram_message(bot, wxid, user_wxid)
            elif command[0] == "游戏分享":
                # 发送游戏分享消息
                await self._send_game_share_message(bot, wxid, user_wxid)
            elif command[0] == "链接测试":
                # 发送简单链接消息测试
                await self._send_link_test_message(bot, wxid, user_wxid)
            elif command[0] == "视频号分享":
                # 发送视频号分享消息
                await self._send_finder_share_message(bot, wxid, user_wxid)
            elif command[0] == "聊天记录":
                # 发送聊天记录消息
                custom_content = command[1] if len(command) > 1 else None
                await self._send_chat_record_message(bot, wxid, user_wxid, custom_content)
            elif command[0] == "接龙":
                # 发送接龙消息
                await self._send_solitaire_message(bot, wxid, user_wxid)


        except Exception as e:
            logger.error(f"[{self.plugin_name}] 处理异常: {str(e)}")
            await bot.send_at_message(
                wxid,
                f"❌ 处理过程中出现错误: {str(e)}",
                [user_wxid]
            )

    async def _send_hongbao_music(self, bot: WechatAPIClient, wxid: str, user_wxid: str):
        """发送红包音乐消息"""
        try:
            title = "恭喜发财,大吉大利！"
            singer = "领取红包"
            url = "https://weixin.qq.com"
            music_url = "https://sf3-cdn-tos.douyinstatic.com/obj/ies-music/7477140345122294565.mp3"
            cover_url = "http://q4.qlogo.cn/headimg_dl?dst_uin=1107621373&spec=640"
            lyric = ""

            xml = f'''<appmsg appid="wx485a97c844086dc9" sdkver="0">
<title>{title}</title>
<des>{singer}</des>
<action>view</action>
<type>3</type>
<showtype>0</showtype>
<content/>
<url>{url}</url>
<dataurl>{music_url}</dataurl>
<lowurl>{url}</lowurl>
<lowdataurl>{music_url}</lowdataurl>
<recorditem/>
<thumburl>{cover_url}</thumburl>
<messageaction/>
<laninfo/>
<extinfo/>
<sourceusername/>
<sourcedisplayname/>
<songlyric>{lyric}</songlyric>
<commenturl/>
<appattach>
    <totallen>0</totallen>
    <attachid/>
    <emoticonmd5/>
    <fileext/>
    <aeskey/>
</appattach>
<webviewshared>
    <publisherId/>
    <publisherReqId>0</publisherReqId>
</webviewshared>
<weappinfo>
    <pagepath/>
    <username/>
    <appid/>
    <appservicetype>0</appservicetype>
</weappinfo>
<websearch/>
<songalbumurl>{cover_url}</songalbumurl>
</appmsg>
<fromusername>{bot.wxid}</fromusername>
<scene>0</scene>
<appinfo>
    <version>1</version>
    <appname></appname>
</appinfo>
<commenturl/>'''

            client_msg_id, _, new_msg_id = await bot.send_app_message(
                wxid,
                xml,
                3  # 音乐消息类型
            )

            if new_msg_id == 0 or not client_msg_id:
                logger.error(f"[{self.plugin_name}] 红包音乐消息发送失败")

        except Exception as e:
            logger.error(f"[{self.plugin_name}] 红包音乐消息发送异常: {str(e)}")

    async def _send_miniprogram_message(self, bot: WechatAPIClient, wxid: str, user_wxid: str):
        """测试小程序消息（简化版本）"""
        try:
            # 尝试简化的小程序XML格式，包含缩略图信息
            xml = f'''<appmsg appid="wxa708de63ee4a2353" sdkver="0">
<title>唱舞星愿站（唱舞全明星）</title>
<des>星愿站</des>
<action>view</action>
<type>33</type>
<showtype>0</showtype>
<content/>
<url>https://weixin.qq.com</url>
<lowurl>https://weixin.qq.com</lowurl>
<dataurl/>
<lowdataurl/>
<appattach>
    <totallen>0</totallen>
    <attachid/>
    <emoticonmd5/>
    <fileext/>
    <cdnthumburl>3057020100044b30490201000204a95c809d02032df98b02048e009324020468464c38042433636534386265392d386261652d346637622d383130362d3433346461343836373162610204051808030201000405004c57c300</cdnthumburl>
    <cdnthumbmd5>53a81a7ea69f1857dc306224ec7bf272</cdnthumbmd5>
    <cdnthumblength>142524</cdnthumblength>
    <cdnthumbheight>360</cdnthumbheight>
    <cdnthumbwidth>450</cdnthumbwidth>
    <cdnthumbaeskey>35b62ce09aa2bfd9cb3d3c18d417ff8c</cdnthumbaeskey>
    <aeskey>35b62ce09aa2bfd9cb3d3c18d417ff8c</aeskey>
    <encryver>1</encryver>
    <islargefilemsg>0</islargefilemsg>
</appattach>
<extinfo/>
<sourceusername>wxa708de63ee4a2353</sourceusername>
<sourcedisplayname>唱舞星愿站</sourcedisplayname>
<thumburl>http://mmbiz.qpic.cn/mmbiz_png/Y4yibCmxrbX6aCNIhj0BH1CXuE8chunB9AN5fnaRJUMRGRicxkh8crn7P1VCQuhV4GB3KCkE4e5ibmXVEibxaoIJuA/640?wx_fmt=png&amp;wxfrom=200</thumburl>
<md5>53a81a7ea69f1857dc306224ec7bf272</md5>
<weappinfo>
    <pagepath><![CDATA[pages/pointsStroe/wares/index.html?key=abz3BM9k&unionid=oA7D81SRhmAwPJuDQ0Pw39LWcWoY&corpid=wwccefc778261bf00f&app_id=wxa708de63ee4a2353&plate_id=91&userid=40610459]]></pagepath>
    <username>gh_25eb09d7bc53@app</username>
    <appid>wxa708de63ee4a2353</appid>
    <version>14</version>
    <type>2</type>
    <weappiconurl>http://mmbiz.qpic.cn/mmbiz_png/Y4yibCmxrbX6aCNIhj0BH1CXuE8chunB9AN5fnaRJUMRGRicxkh8crn7P1VCQuhV4GB3KCkE4e5ibmXVEibxaoIJuA/640?wx_fmt=png&amp;wxfrom=200</weappiconurl>
    <appservicetype>0</appservicetype>
</weappinfo>
</appmsg>
<fromusername>{bot.wxid}</fromusername>
<scene>0</scene>
<appinfo>
    <version>1</version>
    <appname></appname>
</appinfo>
<commenturl/>'''

            client_msg_id, _, new_msg_id = await bot.send_app_message(
                wxid,
                xml,
                33  # 小程序消息类型
            )

            if new_msg_id == 0 or not client_msg_id:
                logger.error(f"[{self.plugin_name}] 小程序消息发送失败")

        except Exception as e:
            logger.error(f"[{self.plugin_name}] 小程序消息发送异常: {str(e)}")

    async def _send_game_share_message(self, bot: WechatAPIClient, wxid: str, user_wxid: str):
        """发送游戏分享消息"""
        try:
            # 参考音乐消息格式的游戏分享消息
            xml = f'''<appmsg appid="wx159a4ca39f5b534b" sdkver="0">
<title>更新公告&#x20;|&#x20;7月3日合区公告，游戏房间上线！</title>
<des>亲爱的唱宝们，【纯白恋曲】和【绯色月下】将于7月3日（周四）进行合区。</des>
<action>view</action>
<type>68</type>
<showtype>0</showtype>
<content/>
<url>https://aweme.snssdk.com/aweme/v1/play/?video_id=v0200fg10000d0nhhs7og65guo1u0qhg&ratio=720p&line=0</url>
<dataurl/>
<lowurl>https://game.weixin.qq.com/</lowurl>
<lowdataurl/>
<recorditem/>
<thumburl>https://p26-sign.douyinpic.com/tos-cn-p-0015/ocEavCGIiPhi82BIAwPjgPA0YIa9B8MgxRgsQ~tplv-dy-resize-walign-adapt-aq:540:q75.webp?lk3s=138a59ce&amp;x-expires=**********&amp;x-signature=6ak5NLqSevUqoMD2otjQm1OScqE%3D&amp;from=327834062&amp;s=PackSourceEnum_DOUYIN_REFLOW&amp;se=false&amp;sc=cover&amp;biz_tag=aweme_video&amp;l=2025062906443854B6E7900534A203C134</thumburl>
<messageaction/>
<laninfo/>
<extinfo/>
<sourceusername/>
<sourcedisplayname/>
<appattach>
    <totallen>0</totallen>
    <attachid/>
    <cdnthumburl>3057020100044b30490201000204a95c809d02032f53a302048e752d7002046864d044042435363964623766322d376561392d343662342d386630652d6633633034336161363935330204051808030201000405004c57c300</cdnthumburl>
    <cdnthumbmd5>3ef6b8adaceae89d52d150db340a8310</cdnthumbmd5>
    <cdnthumblength>20570</cdnthumblength>
    <cdnthumbheight>120</cdnthumbheight>
    <cdnthumbwidth>120</cdnthumbwidth>
    <cdnthumbaeskey>de973d271b766665276672f27980cf38</cdnthumbaeskey>
    <emoticonmd5/>
    <fileext/>
    <aeskey>de973d271b766665276672f27980cf38</aeskey>
    <encryver>1</encryver>
    <islargefilemsg>0</islargefilemsg>
</appattach>
<webviewshared>
    <publisherId/>
    <publisherReqId>0</publisherReqId>
</webviewshared>
<weappinfo>
    <pagepath/>
    <username/>
    <appid/>
    <appservicetype>0</appservicetype>
</weappinfo>
<websearch/>
</appmsg>
<fromusername>{bot.wxid}</fromusername>
<scene>0</scene>
<appinfo>
    <version>1</version>
    <appname></appname>
</appinfo>
<commenturl/>'''

            client_msg_id, _, new_msg_id = await bot.send_app_message(
                wxid,
                xml,
                68  # 游戏分享消息类型
            )

            if new_msg_id == 0 or not client_msg_id:
                logger.error(f"[{self.plugin_name}] 游戏分享消息发送失败")

        except Exception as e:
            logger.error(f"[{self.plugin_name}] 游戏分享消息发送异常: {str(e)}")

    async def _send_link_test_message(self, bot: WechatAPIClient, wxid: str, user_wxid: str):
        """发送简单链接消息测试"""
        try:
            # 最简单的链接消息格式
            xml = f'''<appmsg appid="" sdkver="0">
<title>游戏测试链接</title>
<des>这是一个测试链接消息</des>
<action>view</action>
<type>5</type>
<showtype>0</showtype>
<content/>
<url>https://game.weixin.qq.com/</url>
<lowurl>https://game.weixin.qq.com/</lowurl>
<dataurl/>
<lowdataurl/>
<appattach>
    <totallen>0</totallen>
    <attachid/>
    <emoticonmd5/>
    <fileext/>
    <aeskey/>
</appattach>
<extinfo/>
<sourceusername/>
<sourcedisplayname/>
<thumburl/>
<md5/>
</appmsg>
<fromusername>{bot.wxid}</fromusername>
<scene>0</scene>
<appinfo>
    <version>1</version>
    <appname></appname>
</appinfo>
<commenturl/>'''

            client_msg_id, _, new_msg_id = await bot.send_app_message(
                wxid,
                xml,
                5  # 链接消息类型
            )

            if new_msg_id == 0 or not client_msg_id:
                logger.error(f"[{self.plugin_name}] 链接测试消息发送失败")

        except Exception as e:
            logger.error(f"[{self.plugin_name}] 链接测试消息发送异常: {str(e)}")

    async def _send_finder_share_message(self, bot: WechatAPIClient, wxid: str, user_wxid: str):
        """发送视频号分享消息"""
        try:
            # 使用原始封面图的视频号分享消息XML格式 (Type 51)
            xml = f'''<appmsg appid="" sdkver="0">
<title>当前版本不支持展示该内容，请升级至最新版本。</title>
<des>生活分享阿边的视频</des>
<action>view</action>
<type>51</type>
<showtype>0</showtype>
<content/>
<url>https://support.weixin.qq.com/security/readtemplate?t=w_security_center_website/upgrade</url>
<lowurl/>
<dataurl>https://aweme.snssdk.com/aweme/v1/play/?video_id=v0200fg10000d0nhhs7og65guo1u0qhg&amp;ratio=720p&amp;line=0</dataurl>
<lowdataurl/>
<appattach>
    <totallen>0</totallen>
    <attachid/>
    <emoticonmd5/>
    <fileext/>
    <aeskey/>
</appattach>
<extinfo/>
<thumburl/>
<md5/>
<finderFeed>
    <objectId>14681940687548717274</objectId>
    <feedType>4</feedType>
    <nickname>瑶瑶</nickname>
    <avatar><![CDATA[https://wx.qlogo.cn/mmhead/ver_1/rTPg4GeXEic6oQSZ2jcAI6ic6JEhFtYT40Eu3rUIjZpTKTATSgCEiaMJlUuFibkfxN4IpDCv0qxCA5WgowqT0BGRTQAVeDTn1PTGj1LUYbl2bRuJiaCb1ukH4e1TPIibyUiaX6hFKgria9aX7NM5SJFHDq3pBA/132]]></avatar>
    <desc>男孩小野猫主场，搞笑全场</desc>
    <mediaCount>1</mediaCount>
    <mediaList>
        <media>
            <mediaType>4</mediaType>
            <url><![CDATA[https://aweme.snssdk.com/aweme/v1/play/?video_id=v0200fg10000d0nhhs7og65guo1u0qhg&amp;ratio=720p&amp;line=0]]></url>
            <thumbUrl><![CDATA[https://wx.qlogo.cn/mmhead/ver_1/rTPg4GeXEic6oQSZ2jcAI6ic6JEhFtYT40Eu3rUIjZpTKTATSgCEiaMJlUuFibkfxN4IpDCv0qxCA5WgowqT0BGRTQAVeDTn1PTGj1LUYbl2bRuJiaCb1ukH4e1TPIibyUiaX6hFKgria9aX7NM5SJFHDq3pBA/132]]></thumbUrl>
            <coverUrl><![CDATA[https://wx.qlogo.cn/mmhead/ver_1/rTPg4GeXEic6oQSZ2jcAI6ic6JEhFtYT40Eu3rUIjZpTKTATSgCEiaMJlUuFibkfxN4IpDCv0qxCA5WgowqT0BGRTQAVeDTn1PTGj1LUYbl2bRuJiaCb1ukH4e1TPIibyUiaX6hFKgria9aX7NM5SJFHDq3pBA/132]]></coverUrl>
            <width>1080.0</width>
            <height>1920.0</height>
            <videoPlayDuration>55</videoPlayDuration>
        </media>
    </mediaList>
</finderFeed>
</appmsg>
<fromusername>{bot.wxid}</fromusername>
<scene>0</scene>
<appinfo>
    <version>1</version>
    <appname></appname>
</appinfo>
<commenturl/>'''

            client_msg_id, _, new_msg_id = await bot.send_app_message(
                wxid,
                xml,
                51  # 视频号分享消息类型
            )

            if new_msg_id == 0 or not client_msg_id:
                logger.error(f"[{self.plugin_name}] 视频号分享消息发送失败")

        except Exception as e:
            logger.error(f"[{self.plugin_name}] 视频号分享消息发送异常: {str(e)}")

    async def _send_chat_record_message(self, bot: WechatAPIClient, wxid: str, user_wxid: str, custom_content: str = None):
        """发送聊天记录消息"""
        try:
            # 如果有自定义内容，解析并生成聊天记录
            if custom_content:
                chat_records = self._parse_custom_chat_content(custom_content)
                title = "群聊的聊天记录"
                description = self._generate_description(chat_records)
                record_xml = await self._generate_record_xml(chat_records)
            else:
                # 使用配置文件中的默认聊天记录内容，也使用随机头像
                chat_records = self._parse_custom_chat_content(self.default_chat_content)
                title = "群聊的聊天记录"
                description = self._generate_description(chat_records)
                record_xml = await self._generate_record_xml(chat_records)

            # 聊天记录消息XML格式 (Type 19)
            xml = f'''<appmsg appid="" sdkver="0">
<title>{title}</title>
<des>{description}</des>
<action>view</action>
<type>19</type>
<showtype>0</showtype>
<content/>
<url>https://support.weixin.qq.com/cgi-bin/mmsupport-bin/readtemplate?t=page/favorite_record__w_unsupport&amp;from=singlemessage&amp;isappinstalled=0</url>
<lowurl/>
<dataurl/>
<lowdataurl/>
<recorditem><![CDATA[{record_xml}]]></recorditem>
<appattach>
    <totallen>0</totallen>
    <attachid/>
    <emoticonmd5/>
    <fileext/>
    <aeskey/>
</appattach>
<extinfo/>
<sourceusername/>
<sourcedisplayname/>
<thumburl/>
<md5/>
</appmsg>
<fromusername>{bot.wxid}</fromusername>
<scene>0</scene>
<appinfo>
    <version>1</version>
    <appname></appname>
</appinfo>
<commenturl/>'''

            client_msg_id, _, new_msg_id = await bot.send_app_message(
                wxid,
                xml,
                19  # 聊天记录消息类型
            )

            if new_msg_id == 0 or not client_msg_id:
                logger.error(f"[{self.plugin_name}] 聊天记录消息发送失败")

        except Exception as e:
            logger.error(f"[{self.plugin_name}] 聊天记录消息发送异常: {str(e)}")

    async def _send_solitaire_message(self, bot: WechatAPIClient, wxid: str, user_wxid: str):
        """发送接龙消息"""
        try:
            # 接龙消息XML格式 (Type 53)
            xml = f'''<appmsg appid="" sdkver="0">
<title>#接龙

1. 瑶瑶</title>
<des />
<username />
<action>view</action>
<type>53</type>
<showtype>0</showtype>
<content />
<url />
<lowurl />
<forwardflag>0</forwardflag>
<dataurl />
<lowdataurl />
<contentattr>0</contentattr>
<streamvideo>
    <streamvideourl />
    <streamvideototaltime>0</streamvideototaltime>
    <streamvideotitle />
    <streamvideowording />
    <streamvideoweburl />
    <streamvideothumburl />
    <streamvideoaduxinfo />
    <streamvideopublishid />
</streamvideo>
<canvasPageItem>
    <canvasPageXml><![CDATA[]]></canvasPageXml>
</canvasPageItem>
<appattach>
    <totallen>0</totallen>
    <attachid />
    <cdnattachurl />
    <emoticonmd5 />
    <aeskey />
    <fileext />
    <islargefilemsg>0</islargefilemsg>
</appattach>
<extinfo>
    <solitaire_info><![CDATA[<solitaire><tt>0-3</tt><ex>0-0</ex><tl>0-0</tl><s>.</s><au>{bot.wxid}</au><hrt>1</hrt><loss>0</loss><content><s>1</s><i><u>{bot.wxid}</u><h>0</h><s>.</s><t>{int(time.time())}</t><r>8-2</r></i></content></solitaire>]]></solitaire_info>
</extinfo>
<androidsource>0</androidsource>
<thumburl />
<mediatagname />
<messageaction><![CDATA[]]></messageaction>
<messageext><![CDATA[]]></messageext>
<emoticongift>
    <packageflag>0</packageflag>
    <packageid />
</emoticongift>
<emoticonshared>
    <packageflag>0</packageflag>
    <packageid />
</emoticonshared>
<designershared>
    <designeruin>0</designeruin>
    <designername>null</designername>
    <designerrediretcturl><![CDATA[null]]></designerrediretcturl>
</designershared>
<emotionpageshared>
    <tid>0</tid>
    <title>null</title>
    <desc>null</desc>
    <iconUrl><![CDATA[null]]></iconUrl>
    <secondUrl>null</secondUrl>
    <pageType>0</pageType>
    <setKey>null</setKey>
</emotionpageshared>
<webviewshared>
    <shareUrlOriginal />
    <shareUrlOpen />
    <jsAppId />
    <publisherId />
    <publisherReqId />
</webviewshared>
<template_id />
<md5 />
<websearch>
    <rec_category>0</rec_category>
    <channelId>0</channelId>
</websearch>
<weappinfo>
    <username />
    <appid />
    <appservicetype>0</appservicetype>
    <secflagforsinglepagemode>0</secflagforsinglepagemode>
    <videopageinfo>
        <thumbwidth>0</thumbwidth>
        <thumbheight>0</thumbheight>
        <fromopensdk>0</fromopensdk>
    </videopageinfo>
</weappinfo>
<statextstr />
<musicShareItem>
    <musicDuration>0</musicDuration>
</musicShareItem>
<finderLiveProductShare>
    <finderLiveID><![CDATA[]]></finderLiveID>
    <finderUsername><![CDATA[]]></finderUsername>
    <finderObjectID><![CDATA[]]></finderObjectID>
    <finderNonceID><![CDATA[]]></finderNonceID>
    <liveStatus><![CDATA[]]></liveStatus>
    <appId><![CDATA[]]></appId>
    <pagePath><![CDATA[]]></pagePath>
    <productId><![CDATA[]]></productId>
    <coverUrl><![CDATA[]]></coverUrl>
    <productTitle><![CDATA[]]></productTitle>
    <marketPrice><![CDATA[0]]></marketPrice>
    <sellingPrice><![CDATA[0]]></sellingPrice>
    <platformHeadImg><![CDATA[]]></platformHeadImg>
    <platformName><![CDATA[]]></platformName>
    <shopWindowId><![CDATA[]]></shopWindowId>
    <flashSalePrice><![CDATA[0]]></flashSalePrice>
    <flashSaleEndTime><![CDATA[0]]></flashSaleEndTime>
    <ecSource><![CDATA[]]></ecSource>
    <sellingPriceWording><![CDATA[]]></sellingPriceWording>
    <platformIconURL><![CDATA[]]></platformIconURL>
    <firstProductTagURL><![CDATA[]]></firstProductTagURL>
    <firstProductTagAspectRatioString><![CDATA[0.0]]></firstProductTagAspectRatioString>
    <secondProductTagURL><![CDATA[]]></secondProductTagURL>
    <secondProductTagAspectRatioString><![CDATA[0.0]]></secondProductTagAspectRatioString>
    <firstGuaranteeWording><![CDATA[]]></firstGuaranteeWording>
    <secondGuaranteeWording><![CDATA[]]></secondGuaranteeWording>
    <thirdGuaranteeWording><![CDATA[]]></thirdGuaranteeWording>
    <isPriceBeginShow>false</isPriceBeginShow>
    <lastGMsgID><![CDATA[]]></lastGMsgID>
    <promoterKey><![CDATA[]]></promoterKey>
    <discountWording><![CDATA[]]></discountWording>
    <priceSuffixDescription><![CDATA[]]></priceSuffixDescription>
    <productCardKey><![CDATA[]]></productCardKey>
    <isWxShop><![CDATA[]]></isWxShop>
    <brandIconUrl><![CDATA[]]></brandIconUrl>
    <showBoxItemStringList />
</finderLiveProductShare>
<finderOrder>
    <appID><![CDATA[]]></appID>
    <orderID><![CDATA[]]></orderID>
    <path><![CDATA[]]></path>
    <priceWording><![CDATA[]]></priceWording>
    <stateWording><![CDATA[]]></stateWording>
    <productImageURL><![CDATA[]]></productImageURL>
    <products><![CDATA[]]></products>
    <productsCount><![CDATA[0]]></productsCount>
    <orderType><![CDATA[0]]></orderType>
    <newPriceWording><![CDATA[]]></newPriceWording>
    <newStateWording><![CDATA[]]></newStateWording>
    <useNewWording><![CDATA[0]]></useNewWording>
</finderOrder>
<finderShopWindowShare>
    <finderUsername><![CDATA[]]></finderUsername>
    <avatar><![CDATA[]]></avatar>
    <nickname><![CDATA[]]></nickname>
    <commodityInStockCount><![CDATA[]]></commodityInStockCount>
    <appId><![CDATA[]]></appId>
    <path><![CDATA[]]></path>
    <appUsername><![CDATA[]]></appUsername>
    <query><![CDATA[]]></query>
    <liteAppId><![CDATA[]]></liteAppId>
    <liteAppPath><![CDATA[]]></liteAppPath>
    <liteAppQuery><![CDATA[]]></liteAppQuery>
    <platformTagURL><![CDATA[]]></platformTagURL>
    <saleWording><![CDATA[]]></saleWording>
    <lastGMsgID><![CDATA[]]></lastGMsgID>
    <profileTypeWording><![CDATA[]]></profileTypeWording>
    <saleWordingExtra><![CDATA[]]></saleWordingExtra>
    <isWxShop><![CDATA[]]></isWxShop>
    <platformIconUrl><![CDATA[]]></platformIconUrl>
    <brandIconUrl><![CDATA[]]></brandIconUrl>
    <description><![CDATA[]]></description>
    <backgroundUrl><![CDATA[]]></backgroundUrl>
    <darkModePlatformIconUrl><![CDATA[]]></darkModePlatformIconUrl>
    <reputationInfo>
        <hasReputationInfo>0</hasReputationInfo>
        <reputationScore>0</reputationScore>
        <reputationWording />
        <reputationTextColor />
        <reputationLevelWording />
        <reputationBackgroundColor />
    </reputationInfo>
    <productImageURLList />
</finderShopWindowShare>
<findernamecard>
    <username />
    <avatar><![CDATA[]]></avatar>
    <nickname />
    <auth_job />
    <auth_icon>0</auth_icon>
    <auth_icon_url />
    <ecSource><![CDATA[]]></ecSource>
    <lastGMsgID><![CDATA[]]></lastGMsgID>
</findernamecard>
<finderGuarantee>
    <scene><![CDATA[0]]></scene>
</finderGuarantee>
<directshare>0</directshare>
<gamecenter>
    <namecard>
        <iconUrl />
        <name />
        <desc />
        <tail />
        <jumpUrl />
    </namecard>
</gamecenter>
<patMsg>
    <chatUser />
    <records>
        <recordNum>0</recordNum>
    </records>
</patMsg>
<secretmsg>
    <issecretmsg>0</issecretmsg>
</secretmsg>
<referfromscene>0</referfromscene>
<gameshare>
    <liteappext>
        <liteappbizdata />
        <priority>0</priority>
    </liteappext>
    <appbrandext>
        <litegameinfo />
        <priority>-1</priority>
    </appbrandext>
    <gameshareid />
    <sharedata />
    <isvideo>0</isvideo>
    <duration>-1</duration>
    <isexposed>0</isexposed>
    <readtext />
</gameshare>
<mpsharetrace>
    <hasfinderelement>0</hasfinderelement>
    <lastgmsgid />
</mpsharetrace>
<wxgamecard>
    <framesetname />
    <mbcarddata />
    <minpkgversion />
    <clientextinfo />
    <mbcardheight>0</mbcardheight>
    <isoldversion>0</isoldversion>
</wxgamecard>
<liteapp>
    <id>null</id>
    <path />
    <query />
    <istransparent>0</istransparent>
    <hideicon>0</hideicon>
</liteapp>
<opensdk_share_is_modified>0</opensdk_share_is_modified>
</appmsg>
<fromusername>{bot.wxid}</fromusername>
<scene>0</scene>
<appinfo>
    <version>1</version>
    <appname></appname>
</appinfo>
<commenturl></commenturl>'''

            client_msg_id, _, new_msg_id = await bot.send_app_message(
                wxid,
                xml,
                53  # 接龙消息类型
            )

            if new_msg_id == 0 or not client_msg_id:
                logger.error(f"[{self.plugin_name}] 接龙消息发送失败")

        except Exception as e:
            logger.error(f"[{self.plugin_name}] 接龙消息发送异常: {str(e)}")

    async def _get_random_avatar(self) -> str:
        """获取随机头像URL"""
        try:
            # 添加延时避免API限制
            await asyncio.sleep(0.5)  # 延时500毫秒

            async with httpx.AsyncClient(timeout=5.0) as client:
                response = await client.get("https://v2.api-m.com/api/head?")
                if response.status_code == 200:
                    data = response.json()
                    if data.get("code") == 200 and data.get("data"):
                        return data["data"]
        except Exception as e:
            logger.error(f"[{self.plugin_name}] 获取随机头像失败: {str(e)}")

        # 如果获取失败，返回默认头像
        return "https://wx.qlogo.cn/mmhead/ver_1/icq2icXbhxGESInLwSiaTeZFSiaic8CXB3obiaQMoGVOtXeial73slIhfQ7w1KhFjfDZecibrNYjEOlKWicOzRVWoTmQrHB3xOWpQESaZ91d5fbjfNz99iaictzSnYNl6nddd5wXwY0E3zPKq4NA3EOLqJyrzPAtQ/132"

    def _parse_custom_chat_content(self, content: str) -> list:
        """解析自定义聊天内容
        格式: 用户名1:消息1|用户名2:消息2|...
        """
        chat_records = []
        try:
            # 按 | 分割不同的聊天记录
            records = content.split('|')
            for i, record in enumerate(records):
                if ':' in record:
                    username, message = record.split(':', 1)
                    chat_records.append({
                        'username': username.strip(),
                        'message': message.strip(),
                        'index': i
                    })
                else:
                    # 如果没有用户名，使用默认用户名
                    chat_records.append({
                        'username': '用户',
                        'message': record.strip(),
                        'index': i
                    })
        except Exception as e:
            logger.error(f"[{self.plugin_name}] 解析自定义聊天内容失败: {str(e)}")
            # 返回默认内容
            chat_records = [
                {'username': '用户', 'message': content, 'index': 0}
            ]

        return chat_records

    def _generate_description(self, chat_records: list) -> str:
        """生成聊天记录描述"""
        desc_lines = []
        for record in chat_records:
            desc_lines.append(f"{record['username']}: {record['message']}")
        return '\n'.join(desc_lines)

    async def _generate_record_xml(self, chat_records: list) -> str:
        """生成聊天记录的XML内容"""
        # 生成描述（HTML编码）
        desc_parts = []
        for record in chat_records:
            desc_parts.append(f"{record['username']}:&#x20;{record['message']}")
        desc = '&#x0A;'.join(desc_parts)

        # 为每个用户获取随机头像
        user_avatars = {}
        for record in chat_records:
            username = record['username']
            if username not in user_avatars:
                # 为每个不同的用户获取一个随机头像
                avatar_url = await self._get_random_avatar()
                user_avatars[username] = avatar_url
                logger.info(f"[{self.plugin_name}] 为用户 {username} 获取头像: {avatar_url}")

        # 生成数据项
        data_items = []
        current_time = int(time.time())

        for record in chat_records:
            # 生成随机ID
            data_id = hashlib.md5(f"{record['username']}{record['message']}{record['index']}".encode()).hexdigest()
            source_id = str(random.randint(1000000000000000000, 9999999999999999999))

            # 使用该用户的随机头像
            user_avatar = user_avatars[record['username']]

            # 为每个用户生成64位长度的hashusername（参考真实聊天记录格式）
            user_hash = hashlib.sha256(f"{record['username']}_unique_user_identifier".encode()).hexdigest()

            # 简单的时间设置
            time_offset = record['index'] * 60  # 每条消息间隔1分钟
            message_time = current_time + time_offset

            data_item = f'''<dataitem datatype="1" dataid="{data_id}" datasourceid="{source_id}">
<datadesc>{record['message']}</datadesc>
<sourcename>{record['username']}</sourcename>
<sourceheadurl>{user_avatar}</sourceheadurl>
<sourcetime>2025-06-29&#x20;12:{record['index']:02d}:00</sourcetime>
<srcMsgCreateTime>{message_time}</srcMsgCreateTime>
<fromnewmsgid>{source_id}</fromnewmsgid>
<dataitemsource><hashusername>{user_hash}</hashusername></dataitemsource>
</dataitem>'''
            data_items.append(data_item)

        # 组装完整的recordinfo XML
        record_xml = f'''<recordinfo>
<title>群聊的聊天记录</title>
<desc>{desc}</desc>
<datalist count="{len(chat_records)}">
{''.join(data_items)}
</datalist>
<favcreatetime>{int(time.time() * 1000)}</favcreatetime>
</recordinfo>'''

        return record_xml

    def _generate_original_style_record_xml(self, chat_records: list) -> str:
        """生成保持原始头像风格的聊天记录XML"""
        # 生成描述（HTML编码）
        desc_parts = []
        for record in chat_records:
            desc_parts.append(f"{record['username']}:&#x20;{record['message']}")
        desc = '&#x0A;'.join(desc_parts)

        # 为不同用户准备不同的头像URL
        avatar_urls = {
            "瑶瑶": "https://wx.qlogo.cn/mmhead/ver_1/icq2icXbhxGESInLwSiaTeZFSiaic8CXB3obiaQMoGVOtXeial73slIhfQ7w1KhFjfDZecibrNYjEOlKWicOzRVWoTmQrHB3xOWpQESaZ91d5fbjfNz99iaictzSnYNl6nddd5wXwY0E3zPKq4NA3EOLqJyrzPAtQ/132",
            "郭": "https://wx.qlogo.cn/mmhead/ver_1/PCUwVKYvh0icQrEmB5CiaoyoPaAY9X93CtB0OeA6QmOUml3ljscUgvL3rVRwD2iacibn6oDaaA7xzmibrmt9ibcaNR27Sl8sawD9ZUDgAenibnicpoecdGEaRubkMkImzibwjoddx/96",
            # 默认头像（用于其他用户名）
            "default": "https://wx.qlogo.cn/mmhead/ver_1/icq2icXbhxGESInLwSiaTeZFSiaic8CXB3obiaQMoGVOtXeial73slIhfQ7w1KhFjfDZecibrNYjEOlKWicOzRVWoTmQrHB3xOWpQESaZ91d5fbjfNz99iaictzSnYNl6nddd5wXwY0E3zPKq4NA3EOLqJyrzPAtQ/132"
        }

        # 生成数据项，保持原始风格，确保所有消息都有头像
        data_items = []
        current_time = int(time.time())

        for i, record in enumerate(chat_records):
            import hashlib
            import random

            # 生成随机ID
            data_id = hashlib.md5(f"{record['username']}{record['message']}{i}".encode()).hexdigest()
            source_id = str(random.randint(1000000000000000000, 9999999999999999999))

            # 根据用户名选择头像
            user_avatar = avatar_urls.get(record['username'], avatar_urls['default'])

            # 所有消息都使用datatype="1"（普通文本消息），确保有头像显示
            data_item = f'''<dataitem datatype="1" dataid="{data_id}" datasourceid="{source_id}">
<datadesc>{record['message']}</datadesc>
<sourcename>{record['username']}</sourcename>
<sourceheadurl>{user_avatar}</sourceheadurl>
<sourcetime>2025-06-29&#x20;12:{i:02d}:{i:02d}</sourcetime>
<srcMsgCreateTime>{current_time + i}</srcMsgCreateTime>
<fromnewmsgid>{source_id}</fromnewmsgid>
<dataitemsource><hashusername>c003b2ed099e4eac3c7d9a0455380d1c00256e46bf58bfb49105995894f18cda</hashusername></dataitemsource>
</dataitem>'''
            data_items.append(data_item)

        # 组装完整的recordinfo XML
        record_xml = f'''<recordinfo>
<title>群聊的聊天记录</title>
<desc>{desc}</desc>
<datalist count="{len(chat_records)}">
{''.join(data_items)}
</datalist>
<favcreatetime>{int(time.time() * 1000)}</favcreatetime>
</recordinfo>'''

        return record_xml

    async def _acquire_token(self) -> bool:
        """尝试获取令牌桶中的令牌"""
        current_time = time.time()
        time_elapsed = current_time - self.last_token_time
        self.last_token_time = current_time
        
        # 添加新令牌
        self.tokens = min(self.bucket_size, self.tokens + time_elapsed * self.tokens_per_second)
        
        # 尝试获取令牌
        if self.tokens < 1:
            return False
            
        self.tokens -= 1
        return True
        
    def _check_user_limit(self, wxid: str, user_wxid: str) -> float:
        """检查用户请求限制，返回需要等待的时间（秒）"""
        # 创建用户标识符 (群+用户ID)
        user_key = f"{wxid}_{user_wxid}"
        
        # 获取用户上次请求时间
        last_request = self.user_last_request.get(user_key, 0)
        current_time = time.time()
        
        # 计算冷却时间 (例如5秒)
        cooldown = 5
        elapsed = current_time - last_request
        
        # 更新最后请求时间
        self.user_last_request[user_key] = current_time
        
        # 如果冷却时间未到，返回需要等待的时间
        if elapsed < cooldown:
            return cooldown - elapsed
            
        return 0 