#!/usr/bin/env python3
"""
测试XML格式是否正确
"""
import xml.etree.ElementTree as ET

def test_xml_format():
    xml_content = '''<?xml version="1.0"?>
<msg>
        <appmsg appid="" sdkver="0">
                <title>唱舞星愿站（唱舞全明星）</title>
                <des>星愿站</des>
                <username />
                <action>view</action>
                <type>33</type>
                <showtype>0</showtype>
                <content />
                <url />
                <lowurl />
                <forwardflag>0</forwardflag>
                <dataurl />
                <lowdataurl />
                <contentattr>0</contentattr>
                <streamvideo>
                        <streamvideourl />
                        <streamvideototaltime>0</streamvideototaltime>
                        <streamvideotitle />
                        <streamvideowording />
                        <streamvideoweburl />
                        <streamvideothumburl />
                        <streamvideoaduxinfo />
                        <streamvideopublishid />
                </streamvideo>
                <canvasPageItem>
                        <canvasPageXml><![CDATA[]]></canvasPageXml>
                </canvasPageItem>
                <appattach>
                        <attachid />
                        <cdnthumburl>3057020100044b30490201000204a95c809d02032df98b02048e009324020468464c38042433636534386265392d386261652d346637622d383130362d3433346461343836373162610204051808030201000405004c57c300</cdnthumburl>
                        <cdnthumbmd5>53a81a7ea69f1857dc306224ec7bf272</cdnthumbmd5>
                        <cdnthumblength>142524</cdnthumblength>
                        <cdnthumbheight>360</cdnthumbheight>
                        <cdnthumbwidth>450</cdnthumbwidth>
                        <cdnthumbaeskey>35b62ce09aa2bfd9cb3d3c18d417ff8c</cdnthumbaeskey>
                        <aeskey>35b62ce09aa2bfd9cb3d3c18d417ff8c</aeskey>
                        <encryver>1</encryver>
                        <fileext />
                        <islargefilemsg>0</islargefilemsg>
                </appattach>
                <extinfo />
                <androidsource>3</androidsource>
                <sourceusername>wxa708de63ee4a2353</sourceusername>
                <sourcedisplayname>唱舞星愿站</sourcedisplayname>
                <commenturl />
                <thumburl />
                <mediatagname />
                <messageaction><![CDATA[]]></messageaction>
                <messageext><![CDATA[]]></messageext>
                <emoticongift>
                        <packageflag>0</packageflag>
                        <packageid />
                </emoticongift>
                <emoticonshared>
                        <packageflag>0</packageflag>
                        <packageid />
                </emoticonshared>
                <designershared>
                        <designeruin>0</designeruin>
                        <designername>null</designername>
                        <designerrediretcturl><![CDATA[null]]></designerrediretcturl>
                </designershared>
                <emotionpageshared>
                        <tid>0</tid>
                        <title>null</title>
                        <desc>null</desc>
                        <iconUrl><![CDATA[null]]></iconUrl>
                        <secondUrl />
                        <pageType>0</pageType>
                        <setKey>null</setKey>
                </emotionpageshared>
                <webviewshared>
                        <shareUrlOriginal />
                        <shareUrlOpen />
                        <jsAppId />
                        <publisherId />
                        <publisherReqId />
                </webviewshared>
                <template_id />
                <md5>53a81a7ea69f1857dc306224ec7bf272</md5>
                <websearch />
                <weappinfo>
                        <pagepath><![CDATA[pages/pointsStroe/wares/index.html?key=abz3BM9k&unionid=oA7D81SRhmAwPJuDQ0Pw39LWcWoY&corpid=wwccefc778261bf00f&app_id=wxa708de63ee4a2353&plate_id=91&userid=40610459]]></pagepath>
                        <username>gh_25eb09d7bc53@app</username>
                        <appid>wxa708de63ee4a2353</appid>
                        <version>14</version>
                        <type>2</type>
                        <weappiconurl><![CDATA[http://mmbiz.qpic.cn/mmbiz_png/Y4yibCmxrbX6aCNIhj0BH1CXuE8chunB9AN5fnaRJUMRGRicxkh8crn7P1VCQuhV4GB3KCkE4e5ibmXVEibxaoIJuA/640?wx_fmt=png&wxfrom=200]]></weappiconurl>
                        <appservicetype>0</appservicetype>
                        <secflagforsinglepagemode>0</secflagforsinglepagemode>
                        <videopageinfo>
                                <thumbwidth>450</thumbwidth>
                                <thumbheight>360</thumbheight>
                                <fromopensdk>0</fromopensdk>
                        </videopageinfo>
                </weappinfo>
                <statextstr />
                <musicShareItem>
                        <musicDuration>0</musicDuration>
                </musicShareItem>
        </appmsg>
        <fromusername>wxid_ubbh6q832tcs21</fromusername>
        <scene>0</scene>
        <appinfo>
                <version>1</version>
                <appname></appname>
        </appinfo>
        <commenturl></commenturl>
</msg>'''

    try:
        # 尝试解析XML
        root = ET.fromstring(xml_content)
        print("✅ XML格式正确！")
        
        # 提取关键信息
        appmsg = root.find('appmsg')
        if appmsg is not None:
            title = appmsg.find('title')
            msg_type = appmsg.find('type')
            weappinfo = appmsg.find('weappinfo')
            
            print(f"标题: {title.text if title is not None else 'N/A'}")
            print(f"消息类型: {msg_type.text if msg_type is not None else 'N/A'}")
            
            if weappinfo is not None:
                appid = weappinfo.find('appid')
                username = weappinfo.find('username')
                print(f"小程序AppID: {appid.text if appid is not None else 'N/A'}")
                print(f"小程序用户名: {username.text if username is not None else 'N/A'}")
        
        return True
        
    except ET.ParseError as e:
        print(f"❌ XML格式错误: {e}")
        return False
    except Exception as e:
        print(f"❌ 其他错误: {e}")
        return False

if __name__ == "__main__":
    test_xml_format()
