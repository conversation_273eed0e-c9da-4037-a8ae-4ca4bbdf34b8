2025-07-22 11:17:23 | SUCCESS | 读取主设置成功
2025-07-22 11:17:23 | DEBUG | 最终使用的 Redis 主机地址: 127.0.0.1
2025-07-22 11:17:23 | INFO | 2025/07/22 11:17:23 GetRedisAddr: 127.0.0.1:6379
2025-07-22 11:17:23 | INFO | 2025/07/22 11:17:23 Redis连接成功 | 地址:127.0.0.1:6379 DB:0
2025-07-22 11:17:23 | INFO | 2025/07/22 11:17:23 Server start at :9000
2025-07-22 11:17:24 | SUCCESS | WechatAPI服务已启动
2025-07-22 11:17:24 | INFO | 登录账号信息: wxid: wxid_4usgcju5ey9q29  昵称: 瑶瑶  微信号: Today-KFC  手机号: None
2025-07-22 11:17:24 | INFO | 登录设备信息: device_name: <PERSON>'s Pad  device_id: 49d050a7e94d1ecc40790d74b1c4369f
2025-07-22 11:17:24 | SUCCESS | 登录成功
2025-07-22 11:17:24 | SUCCESS | 已开启自动心跳
2025-07-22 11:17:24 | INFO | 成功加载表情映射文件，共 521 条记录
2025-07-22 11:17:24 | SUCCESS | 数据库初始化成功
2025-07-22 11:17:24 | SUCCESS | 定时任务已启动
2025-07-22 11:17:24 | SUCCESS | 插件 AISignIn 已加载定时任务: {'plugins.AISignIn.main.AISignIn.auto_signin_task'}
2025-07-22 11:17:25 | INFO | 已加载管理员列表: ['wxid_ubbh6q832tcs21', 'admin-wxid']
2025-07-22 11:17:25 | INFO | 播客API初始化成功
2025-07-22 11:17:25 | INFO | 加载配置完成，转换后的数据: {'gh_7057516c9e71': {'name': '唱舞全明星', 'groups': ['27852221909@chatroom', '55878994168@chatroom', '51891329927@chatroom']}, 'gh_d4d21a0e4623': {'name': '两颗桃', 'groups': ['48097389945@chatroom']}, 'gh_410ff2fa6ec9': {'name': 'NGC660 Ai研究院', 'groups': ['47442567074@chatroom']}, 'gh_f78c53c507c0': {'name': '不正常吐槽君', 'groups': ['48097389945@chatroom']}, 'gh_ba3381c847b3': {'name': '唱舞星计划', 'groups': ['51891329927@chatroom']}, 'gh_1a102fb4cad9': {'name': '我在娱乐圈的唯一人脉', 'groups': ['48097389945@chatroom', '27852221909@chatroom']}}
2025-07-22 11:17:25 | INFO | ArticleForwarder插件初始化完成 - 监控配置: {'gh_7057516c9e71': {'name': '唱舞全明星', 'groups': ['27852221909@chatroom', '55878994168@chatroom', '51891329927@chatroom']}, 'gh_d4d21a0e4623': {'name': '两颗桃', 'groups': ['48097389945@chatroom']}, 'gh_410ff2fa6ec9': {'name': 'NGC660 Ai研究院', 'groups': ['47442567074@chatroom']}, 'gh_f78c53c507c0': {'name': '不正常吐槽君', 'groups': ['48097389945@chatroom']}, 'gh_ba3381c847b3': {'name': '唱舞星计划', 'groups': ['51891329927@chatroom']}, 'gh_1a102fb4cad9': {'name': '我在娱乐圈的唯一人脉', 'groups': ['48097389945@chatroom', '27852221909@chatroom']}}
2025-07-22 11:17:25 | DEBUG | [TempFileManager] 添加清理规则: default
2025-07-22 11:17:25 | DEBUG | [TempFileManager] 添加清理规则: images
2025-07-22 11:17:25 | DEBUG | [TempFileManager] 添加清理规则: videos
2025-07-22 11:17:25 | DEBUG | [TempFileManager] 添加清理规则: audio
2025-07-22 11:17:25 | DEBUG | [TempFileManager] 添加清理规则: temp
2025-07-22 11:17:25 | DEBUG | [TempFileManager] 添加清理规则: large_files
2025-07-22 11:17:25 | INFO | [TempFileManager] 临时文件清理任务已启动
2025-07-22 11:17:26 | INFO | [ChatSummary] 数据库初始化成功
2025-07-22 11:17:27 | INFO | 成功加载表情映射文件，共 521 条记录
2025-07-22 11:17:27 | SUCCESS | 插件 GoodMorning 已加载定时任务: {'plugins.GoodMorning.main.GoodMorning.daily_task'}
2025-07-22 11:17:27 | SUCCESS | 插件 News 已加载定时任务: {'plugins.News.main.News.noon_news', 'plugins.News.main.News.night_news'}
2025-07-22 11:17:27 | INFO | [PatReply] AMR文件已存在且是最新的: C:\XYBotV2\data\paiyipai\1_BFB5258B-2B13-4CF4-A40C-5BCCE74E5414_converted.amr
2025-07-22 11:17:27 | DEBUG | [PatReply] 从缓存加载base64: 18228 字符
2025-07-22 11:17:27 | SUCCESS | 插件 QuarkSignIn 已加载定时任务: {'plugins.QuarkSignIn.main.QuarkSignIn.auto_signin_task'}
2025-07-22 11:17:27 | INFO | [QuarkSignIn] 已设置自动签到时间: 08:00
2025-07-22 11:17:28 | INFO | 插件状态: 启用, 检查间隔: 3600秒, 最大重试次数: 3, 重试延迟: 5秒
2025-07-22 11:17:28 | INFO | 已加载管理员列表: ['wxid_ubbh6q832tcs21', 'admin-wxid']
2025-07-22 11:17:28 | INFO | 已加载 2 个改名监控群和 1 个退群监控群
2025-07-22 11:17:28 | INFO | [RenameReminder] 开始启用插件...
2025-07-22 11:17:28 | SUCCESS | 插件 RenameReminder 已加载定时任务: {'plugins.RenameReminder.main.RenameReminder.check_name_changes'}
2025-07-22 11:17:28 | INFO | [RenameReminder] 插件已启用，开始初始化...
2025-07-22 11:17:28 | INFO | [RenameReminder] 支持的命令: 开启改名提醒, 关闭改名提醒, 开启退群提醒, 关闭退群提醒, 查看提醒状态
2025-07-22 11:17:28 | INFO | 已设置检查间隔为 3600 秒
2025-07-22 11:17:28 | INFO | 已加载 2 个监控群，开始更新成员列表
2025-07-22 11:17:28 | DEBUG | 已更新群 51891329927@chatroom 的成员列表
2025-07-22 11:17:28 | DEBUG | 已更新群 27852221909@chatroom 的成员列表
2025-07-22 11:17:33 | SUCCESS | 插件 TimerTask 已加载定时任务: {'plugins.TimerTask.main.TimerTask.check_tasks'}
2025-07-22 11:17:33 | INFO | [VideoDemand] 加载了 128 个视频类别配置
2025-07-22 11:17:35 | INFO | [YaoyaoPlugin] 私聊黑名单用户: ['gh_a5cf07d474f1', 'gh_ba3381c847b3']
2025-07-22 11:17:35 | INFO | [YaoyaoPlugin] 已加载管理员列表: ['wxid_ubbh6q832tcs21', 'admin-wxid']
2025-07-22 11:17:35 | INFO | [yuanbao] 插件初始化完成
2025-07-22 11:17:35 | DEBUG | [yuanbao] 指令: ['元宝', 'yuanbao']
2025-07-22 11:17:35 | DEBUG | [yuanbao] 绘图模型: gpt_175B_0404
2025-07-22 11:17:35 | DEBUG | [yuanbao] 认证信息加载状态: hy_token=True, hy_user=True, agent_id=True, x_uskey=True
2025-07-22 11:17:35 | SUCCESS | 已加载插件: ['AdminPoint', 'AdminSignInReset', 'AdminWhitelist', 'AISignIn', 'AppMessageTester', 'ArticleForwarder', 'BaiduAgentsPlugin', 'BaiduDraw', 'BotStatus', 'ChatSummary', 'DanceSignInPlugin', 'DeepseekPlugin', 'Doubao', 'DoubaoDrawing', 'DoubaoImageRecognition', 'DouBaoImageToImage', 'DouBaoImageToVideo', 'DoubaoVideoSearch', 'DouHui', 'EmojiTestPlugin', 'FixedQuoteTest', 'GetContact', 'Gomoku', 'GoodMorning', 'HunyuanDraw', 'ImageEcho', 'ImageToImage', 'JiemengDraw', 'KeLingImageToImage', 'KlingAI', 'Leaderboard', 'LuckyDraw', 'ManagePlugin', 'MeituAI', 'Menu', 'MiniProgramTester', 'Music', 'News', 'PatReply', 'PointTrade', 'QuarkSignIn', 'QueryPoint', 'RandomMember', 'RandomPicture', 'RenameReminder', 'RevokePlugin', 'RoboNeo', 'SignIn', 'TempFileManagerPlugin', 'TencentLke', 'TimerTask', 'TongyiDraw', 'URLShortener', 'VideoDemand', 'VideoParserPlugin', 'VideoTest', 'VivoAgentsPlugin', 'VoiceTest', 'Warthunder', 'WeatherQuery', 'WelcomePlugin', 'Xunfei', 'YaoyaoPlugin', 'Yuanbao', '抽签', '造梦次元']
2025-07-22 11:17:35 | INFO | 处理堆积消息中
2025-07-22 11:17:35 | DEBUG | 接受到 3 条消息
2025-07-22 11:17:36 | SUCCESS | 处理堆积消息完毕
2025-07-22 11:17:36 | SUCCESS | 开始处理消息
2025-07-22 11:17:40 | DEBUG | 收到消息: {'MsgId': 1747691738, 'FromUserName': {'string': '48097389945@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 1, 'Content': {'string': 'wxid_laurnst5xn0q22:\n来财'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1753154272, 'MsgSource': '<msgsource>\n\t<pua>1</pua>\n\t<eggIncluded>1</eggIncluded>\n\t<silence>0</silence>\n\t<membercount>62</membercount>\n\t<signature>N0_V1_61MHFDOt|v1_Ylw2dLdl</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'PushContent': 'متسول暗 : 来财', 'NewMsgId': 4400965840694623921, 'MsgSeq': 871390547}
2025-07-22 11:17:40 | INFO | 收到文本消息: 消息ID:1747691738 来自:48097389945@chatroom 发送人:wxid_laurnst5xn0q22 @:[] 内容:来财
2025-07-22 11:17:41 | INFO | 发送表情消息: 对方wxid:48097389945@chatroom md5:512770d311d177911fa10e42d4162ffd 总长度:9992069
2025-07-22 11:17:41 | DEBUG | 处理消息内容: '来财'
2025-07-22 11:17:41 | DEBUG | 消息内容 '来财' 不匹配任何命令，忽略
2025-07-22 11:17:43 | DEBUG | 收到消息: {'MsgId': 1285212660, 'FromUserName': {'string': '48097389945@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 49, 'Content': {'string': 'wxid_lneb7n23o4lg12:\n<?xml version="1.0"?>\n<msg>\n\t<appmsg appid="wx485a97c844086dc9" sdkver="0">\n\t\t<title>恭喜发财，大吉大利</title>\n\t\t<des>领取红包</des>\n\t\t<username />\n\t\t<action>view</action>\n\t\t<type>3</type>\n\t\t<showtype>0</showtype>\n\t\t<content />\n\t\t<url />\n\t\t<lowurl />\n\t\t<forwardflag>0</forwardflag>\n\t\t<dataurl>https://sf3-cdn-tos.douyinstatic.com/obj/ies-music/7477140345122294565.mp3</dataurl>\n\t\t<lowdataurl />\n\t\t<contentattr>0</contentattr>\n\t\t<streamvideo>\n\t\t\t<streamvideourl />\n\t\t\t<streamvideototaltime>0</streamvideototaltime>\n\t\t\t<streamvideotitle />\n\t\t\t<streamvideowording />\n\t\t\t<streamvideoweburl />\n\t\t\t<streamvideothumburl />\n\t\t\t<streamvideoaduxinfo />\n\t\t\t<streamvideopublishid />\n\t\t</streamvideo>\n\t\t<canvasPageItem>\n\t\t\t<canvasPageXml><![CDATA[]]></canvasPageXml>\n\t\t</canvasPageItem>\n\t\t<appattach>\n\t\t\t<totallen>0</totallen>\n\t\t\t<attachid />\n\t\t\t<cdnattachurl />\n\t\t\t<emoticonmd5 />\n\t\t\t<aeskey />\n\t\t\t<fileext />\n\t\t\t<islargefilemsg>0</islargefilemsg>\n\t\t</appattach>\n\t\t<extinfo />\n\t\t<androidsource>2</androidsource>\n\t\t<thumburl />\n\t\t<mediatagname />\n\t\t<messageaction><![CDATA[]]></messageaction>\n\t\t<messageext><![CDATA[]]></messageext>\n\t\t<emoticongift>\n\t\t\t<packageflag>0</packageflag>\n\t\t\t<packageid />\n\t\t</emoticongift>\n\t\t<emoticonshared>\n\t\t\t<packageflag>0</packageflag>\n\t\t\t<packageid />\n\t\t</emoticonshared>\n\t\t<designershared>\n\t\t\t<designeruin>0</designeruin>\n\t\t\t<designername>null</designername>\n\t\t\t<designerrediretcturl><![CDATA[null]]></designerrediretcturl>\n\t\t</designershared>\n\t\t<emotionpageshared>\n\t\t\t<tid>0</tid>\n\t\t\t<title>null</title>\n\t\t\t<desc>null</desc>\n\t\t\t<iconUrl><![CDATA[null]]></iconUrl>\n\t\t\t<secondUrl>null</secondUrl>\n\t\t\t<pageType>0</pageType>\n\t\t\t<setKey>null</setKey>\n\t\t</emotionpageshared>\n\t\t<webviewshared>\n\t\t\t<shareUrlOriginal />\n\t\t\t<shareUrlOpen />\n\t\t\t<jsAppId />\n\t\t\t<publisherId />\n\t\t\t<publisherReqId />\n\t\t</webviewshared>\n\t\t<template_id />\n\t\t<md5 />\n\t\t<websearch>\n\t\t\t<rec_category>0</rec_category>\n\t\t\t<channelId>0</channelId>\n\t\t</websearch>\n\t\t<weappinfo>\n\t\t\t<username />\n\t\t\t<appid />\n\t\t\t<appservicetype>0</appservicetype>\n\t\t\t<secflagforsinglepagemode>0</secflagforsinglepagemode>\n\t\t\t<videopageinfo>\n\t\t\t\t<thumbwidth>0</thumbwidth>\n\t\t\t\t<thumbheight>0</thumbheight>\n\t\t\t\t<fromopensdk>0</fromopensdk>\n\t\t\t</videopageinfo>\n\t\t</weappinfo>\n\t\t<statextstr>GhQKEnd4NDg1YTk3Yzg0NDA4NmRjOQ==</statextstr>\n\t\t<songalbumurl>http://p.qlogo.cn/homework/0/hw_h_662bct5fhhk4w8k67f91525899b4/0/25632286</songalbumurl>\n\t\t<songlyric>[99:99.99]小爱机器人提醒您，该歌曲没有歌词</songlyric>\n\t\t<musicShareItem>\n\t\t\t<musicDuration>0</musicDuration>\n\t\t</musicShareItem>\n\t\t<finderLiveProductShare>\n\t\t\t<finderLiveID><![CDATA[]]></finderLiveID>\n\t\t\t<finderUsername><![CDATA[]]></finderUsername>\n\t\t\t<finderObjectID><![CDATA[]]></finderObjectID>\n\t\t\t<finderNonceID><![CDATA[]]></finderNonceID>\n\t\t\t<liveStatus><![CDATA[]]></liveStatus>\n\t\t\t<appId><![CDATA[]]></appId>\n\t\t\t<pagePath><![CDATA[]]></pagePath>\n\t\t\t<productId><![CDATA[]]></productId>\n\t\t\t<coverUrl><![CDATA[]]></coverUrl>\n\t\t\t<productTitle><![CDATA[]]></productTitle>\n\t\t\t<marketPrice><![CDATA[0]]></marketPrice>\n\t\t\t<sellingPrice><![CDATA[0]]></sellingPrice>\n\t\t\t<platformHeadImg><![CDATA[]]></platformHeadImg>\n\t\t\t<platformName><![CDATA[]]></platformName>\n\t\t\t<shopWindowId><![CDATA[]]></shopWindowId>\n\t\t\t<flashSalePrice><![CDATA[0]]></flashSalePrice>\n\t\t\t<flashSaleEndTime><![CDATA[0]]></flashSaleEndTime>\n\t\t\t<ecSource><![CDATA[]]></ecSource>\n\t\t\t<sellingPriceWording><![CDATA[]]></sellingPriceWording>\n\t\t\t<platformIconURL><![CDATA[]]></platformIconURL>\n\t\t\t<firstProductTagURL><![CDATA[]]></firstProductTagURL>\n\t\t\t<firstProductTagAspectRatioString><![CDATA[0.0]]></firstProductTagAspectRatioString>\n\t\t\t<secondProductTagURL><![CDATA[]]></secondProductTagURL>\n\t\t\t<secondProductTagAspectRatioString><![CDATA[0.0]]></secondProductTagAspectRatioString>\n\t\t\t<firstGuaranteeWording><![CDATA[]]></firstGuaranteeWording>\n\t\t\t<secondGuaranteeWording><![CDATA[]]></secondGuaranteeWording>\n\t\t\t<thirdGuaranteeWording><![CDATA[]]></thirdGuaranteeWording>\n\t\t\t<isPriceBeginShow>false</isPriceBeginShow>\n\t\t\t<lastGMsgID><![CDATA[]]></lastGMsgID>\n\t\t\t<promoterKey><![CDATA[]]></promoterKey>\n\t\t\t<discountWording><![CDATA[]]></discountWording>\n\t\t\t<priceSuffixDescription><![CDATA[]]></priceSuffixDescription>\n\t\t\t<showBoxItemStringList />\n\t\t</finderLiveProductShare>\n\t\t<finderOrder>\n\t\t\t<appID><![CDATA[]]></appID>\n\t\t\t<orderID><![CDATA[]]></orderID>\n\t\t\t<path><![CDATA[]]></path>\n\t\t\t<priceWording><![CDATA[]]></priceWording>\n\t\t\t<stateWording><![CDATA[]]></stateWording>\n\t\t\t<productImageURL><![CDATA[]]></productImageURL>\n\t\t\t<products><![CDATA[]]></products>\n\t\t\t<productsCount><![CDATA[0]]></productsCount>\n\t\t</finderOrder>\n\t\t<finderShopWindowShare>\n\t\t\t<finderUsername><![CDATA[]]></finderUsername>\n\t\t\t<avatar><![CDATA[]]></avatar>\n\t\t\t<nickname><![CDATA[]]></nickname>\n\t\t\t<commodityInStockCount><![CDATA[]]></commodityInStockCount>\n\t\t\t<appId><![CDATA[]]></appId>\n\t\t\t<path><![CDATA[]]></path>\n\t\t\t<appUsername><![CDATA[]]></appUsername>\n\t\t\t<query><![CDATA[]]></query>\n\t\t\t<liteAppId><![CDATA[]]></liteAppId>\n\t\t\t<liteAppPath><![CDATA[]]></liteAppPath>\n\t\t\t<liteAppQuery><![CDATA[]]></liteAppQuery>\n\t\t\t<platformTagURL><![CDATA[]]></platformTagURL>\n\t\t\t<saleWording><![CDATA[]]></saleWording>\n\t\t\t<lastGMsgID><![CDATA[]]></lastGMsgID>\n\t\t\t<profileTypeWording><![CDATA[]]></profileTypeWording>\n\t\t\t<reputationInfo>\n\t\t\t\t<hasReputationInfo>0</hasReputationInfo>\n\t\t\t\t<reputationScore>0</reputationScore>\n\t\t\t\t<reputationWording />\n\t\t\t\t<reputationTextColor />\n\t\t\t\t<reputationLevelWording />\n\t\t\t\t<reputationBackgroundColor />\n\t\t\t</reputationInfo>\n\t\t\t<productImageURLList />\n\t\t</finderShopWindowShare>\n\t\t<findernamecard>\n\t\t\t<username />\n\t\t\t<avatar><![CDATA[]]></avatar>\n\t\t\t<nickname />\n\t\t\t<auth_job />\n\t\t\t<auth_icon>0</auth_icon>\n\t\t\t<auth_icon_url />\n\t\t\t<ecSource><![CDATA[]]></ecSource>\n\t\t\t<lastGMsgID><![CDATA[]]></lastGMsgID>\n\t\t</findernamecard>\n\t\t<finderGuarantee>\n\t\t\t<scene><![CDATA[0]]></scene>\n\t\t</finderGuarantee>\n\t\t<directshare>0</directshare>\n\t\t<gamecenter>\n\t\t\t<namecard>\n\t\t\t\t<iconUrl />\n\t\t\t\t<name />\n\t\t\t\t<desc />\n\t\t\t\t<tail />\n\t\t\t\t<jumpUrl />\n\t\t\t</namecard>\n\t\t</gamecenter>\n\t\t<patMsg>\n\t\t\t<chatUser />\n\t\t\t<records>\n\t\t\t\t<recordNum>0</recordNum>\n\t\t\t</records>\n\t\t</patMsg>\n\t\t<secretmsg>\n\t\t\t<issecretmsg>0</issecretmsg>\n\t\t</secretmsg>\n\t\t<referfromscene>0</referfromscene>\n\t\t<gameshare>\n\t\t\t<liteappext>\n\t\t\t\t<liteappbizdata />\n\t\t\t\t<priority>0</priority>\n\t\t\t</liteappext>\n\t\t\t<appbrandext>\n\t\t\t\t<litegameinfo />\n\t\t\t\t<priority>-1</priority>\n\t\t\t</appbrandext>\n\t\t\t<gameshareid />\n\t\t\t<sharedata />\n\t\t\t<isvideo>0</isvideo>\n\t\t\t<duration>-1</duration>\n\t\t\t<isexposed>0</isexposed>\n\t\t\t<readtext />\n\t\t</gameshare>\n\t\t<mpsharetrace>\n\t\t\t<hasfinderelement>0</hasfinderelement>\n\t\t\t<lastgmsgid />\n\t\t</mpsharetrace>\n\t\t<wxgamecard>\n\t\t\t<framesetname />\n\t\t\t<mbcarddata />\n\t\t\t<minpkgversion />\n\t\t\t<mbcardheight>0</mbcardheight>\n\t\t\t<isoldversion>0</isoldversion>\n\t\t</wxgamecard>\n\t\t<liteapp>\n\t\t\t<id>null</id>\n\t\t\t<path />\n\t\t\t<query />\n\t\t</liteapp>\n\t</appmsg>\n\t<fromusername>wxid_lneb7n23o4lg12</fromusername>\n\t<scene>0</scene>\n\t<appinfo>\n\t\t<version>29</version>\n\t\t<appname>摇一摇搜歌</appname>\n\t</appinfo>\n\t<commenturl></commenturl>\n</msg>\n'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1753154273, 'MsgSource': '<msgsource>\n\t<sec_msg_node>\n\t\t<uuid>3334ec60e9fff91204e843f400f36384_</uuid>\n\t\t<risk-file-flag />\n\t\t<risk-file-md5-list />\n\t</sec_msg_node>\n\t<silence>0</silence>\n\t<membercount>62</membercount>\n\t<signature>N0_V1_N0VGTPe4|v1_NyvF0Nhw</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'PushContent': '你收到了一条消息', 'NewMsgId': 8865882751656250353, 'MsgSeq': 871390550}
2025-07-22 11:17:43 | DEBUG | 从群聊消息中提取发送者: wxid_lneb7n23o4lg12
2025-07-22 11:17:43 | DEBUG | XML消息完整内容:
<?xml version="1.0"?>
<msg>
	<appmsg appid="wx485a97c844086dc9" sdkver="0">
		<title>恭喜发财，大吉大利</title>
		<des>领取红包</des>
		<username />
		<action>view</action>
		<type>3</type>
		<showtype>0</showtype>
		<content />
		<url />
		<lowurl />
		<forwardflag>0</forwardflag>
		<dataurl>https://sf3-cdn-tos.douyinstatic.com/obj/ies-music/7477140345122294565.mp3</dataurl>
		<lowdataurl />
		<contentattr>0</contentattr>
		<streamvideo>
			<streamvideourl />
			<streamvideototaltime>0</streamvideototaltime>
			<streamvideotitle />
			<streamvideowording />
			<streamvideoweburl />
			<streamvideothumburl />
			<streamvideoaduxinfo />
			<streamvideopublishid />
		</streamvideo>
		<canvasPageItem>
			<canvasPageXml><![CDATA[]]></canvasPageXml>
		</canvasPageItem>
		<appattach>
			<totallen>0</totallen>
			<attachid />
			<cdnattachurl />
			<emoticonmd5 />
			<aeskey />
			<fileext />
			<islargefilemsg>0</islargefilemsg>
		</appattach>
		<extinfo />
		<androidsource>2</androidsource>
		<thumburl />
		<mediatagname />
		<messageaction><![CDATA[]]></messageaction>
		<messageext><![CDATA[]]></messageext>
		<emoticongift>
			<packageflag>0</packageflag>
			<packageid />
		</emoticongift>
		<emoticonshared>
			<packageflag>0</packageflag>
			<packageid />
		</emoticonshared>
		<designershared>
			<designeruin>0</designeruin>
			<designername>null</designername>
			<designerrediretcturl><![CDATA[null]]></designerrediretcturl>
		</designershared>
		<emotionpageshared>
			<tid>0</tid>
			<title>null</title>
			<desc>null</desc>
			<iconUrl><![CDATA[null]]></iconUrl>
			<secondUrl>null</secondUrl>
			<pageType>0</pageType>
			<setKey>null</setKey>
		</emotionpageshared>
		<webviewshared>
			<shareUrlOriginal />
			<shareUrlOpen />
			<jsAppId />
			<publisherId />
			<publisherReqId />
		</webviewshared>
		<template_id />
		<md5 />
		<websearch>
			<rec_category>0</rec_category>
			<channelId>0</channelId>
		</websearch>
		<weappinfo>
			<username />
			<appid />
			<appservicetype>0</appservicetype>
			<secflagforsinglepagemode>0</secflagforsinglepagemode>
			<videopageinfo>
				<thumbwidth>0</thumbwidth>
				<thumbheight>0</thumbheight>
				<fromopensdk>0</fromopensdk>
			</videopageinfo>
		</weappinfo>
		<statextstr>GhQKEnd4NDg1YTk3Yzg0NDA4NmRjOQ==</statextstr>
		<songalbumurl>http://p.qlogo.cn/homework/0/hw_h_662bct5fhhk4w8k67f91525899b4/0/25632286</songalbumurl>
		<songlyric>[99:99.99]小爱机器人提醒您，该歌曲没有歌词</songlyric>
		<musicShareItem>
			<musicDuration>0</musicDuration>
		</musicShareItem>
		<finderLiveProductShare>
			<finderLiveID><![CDATA[]]></finderLiveID>
			<finderUsername><![CDATA[]]></finderUsername>
			<finderObjectID><![CDATA[]]></finderObjectID>
			<finderNonceID><![CDATA[]]></finderNonceID>
			<liveStatus><![CDATA[]]></liveStatus>
			<appId><![CDATA[]]></appId>
			<pagePath><![CDATA[]]></pagePath>
			<productId><![CDATA[]]></productId>
			<coverUrl><![CDATA[]]></coverUrl>
			<productTitle><![CDATA[]]></productTitle>
			<marketPrice><![CDATA[0]]></marketPrice>
			<sellingPrice><![CDATA[0]]></sellingPrice>
			<platformHeadImg><![CDATA[]]></platformHeadImg>
			<platformName><![CDATA[]]></platformName>
			<shopWindowId><![CDATA[]]></shopWindowId>
			<flashSalePrice><![CDATA[0]]></flashSalePrice>
			<flashSaleEndTime><![CDATA[0]]></flashSaleEndTime>
			<ecSource><![CDATA[]]></ecSource>
			<sellingPriceWording><![CDATA[]]></sellingPriceWording>
			<platformIconURL><![CDATA[]]></platformIconURL>
			<firstProductTagURL><![CDATA[]]></firstProductTagURL>
			<firstProductTagAspectRatioString><![CDATA[0.0]]></firstProductTagAspectRatioString>
			<secondProductTagURL><![CDATA[]]></secondProductTagURL>
			<secondProductTagAspectRatioString><![CDATA[0.0]]></secondProductTagAspectRatioString>
			<firstGuaranteeWording><![CDATA[]]></firstGuaranteeWording>
			<secondGuaranteeWording><![CDATA[]]></secondGuaranteeWording>
			<thirdGuaranteeWording><![CDATA[]]></thirdGuaranteeWording>
			<isPriceBeginShow>false</isPriceBeginShow>
			<lastGMsgID><![CDATA[]]></lastGMsgID>
			<promoterKey><![CDATA[]]></promoterKey>
			<discountWording><![CDATA[]]></discountWording>
			<priceSuffixDescription><![CDATA[]]></priceSuffixDescription>
			<showBoxItemStringList />
		</finderLiveProductShare>
		<finderOrder>
			<appID><![CDATA[]]></appID>
			<orderID><![CDATA[]]></orderID>
			<path><![CDATA[]]></path>
			<priceWording><![CDATA[]]></priceWording>
			<stateWording><![CDATA[]]></stateWording>
			<productImageURL><![CDATA[]]></productImageURL>
			<products><![CDATA[]]></products>
			<productsCount><![CDATA[0]]></productsCount>
		</finderOrder>
		<finderShopWindowShare>
			<finderUsername><![CDATA[]]></finderUsername>
			<avatar><![CDATA[]]></avatar>
			<nickname><![CDATA[]]></nickname>
			<commodityInStockCount><![CDATA[]]></commodityInStockCount>
			<appId><![CDATA[]]></appId>
			<path><![CDATA[]]></path>
			<appUsername><![CDATA[]]></appUsername>
			<query><![CDATA[]]></query>
			<liteAppId><![CDATA[]]></liteAppId>
			<liteAppPath><![CDATA[]]></liteAppPath>
			<liteAppQuery><![CDATA[]]></liteAppQuery>
			<platformTagURL><![CDATA[]]></platformTagURL>
			<saleWording><![CDATA[]]></saleWording>
			<lastGMsgID><![CDATA[]]></lastGMsgID>
			<profileTypeWording><![CDATA[]]></profileTypeWording>
			<reputationInfo>
				<hasReputationInfo>0</hasReputationInfo>
				<reputationScore>0</reputationScore>
				<reputationWording />
				<reputationTextColor />
				<reputationLevelWording />
				<reputationBackgroundColor />
			</reputationInfo>
			<productImageURLList />
		</finderShopWindowShare>
		<findernamecard>
			<username />
			<avatar><![CDATA[]]></avatar>
			<nickname />
			<auth_job />
			<auth_icon>0</auth_icon>
			<auth_icon_url />
			<ecSource><![CDATA[]]></ecSource>
			<lastGMsgID><![CDATA[]]></lastGMsgID>
		</findernamecard>
		<finderGuarantee>
			<scene><![CDATA[0]]></scene>
		</finderGuarantee>
		<directshare>0</directshare>
		<gamecenter>
			<namecard>
				<iconUrl />
				<name />
				<desc />
				<tail />
				<jumpUrl />
			</namecard>
		</gamecenter>
		<patMsg>
			<chatUser />
			<records>
				<recordNum>0</recordNum>
			</records>
		</patMsg>
		<secretmsg>
			<issecretmsg>0</issecretmsg>
		</secretmsg>
		<referfromscene>0</referfromscene>
		<gameshare>
			<liteappext>
				<liteappbizdata />
				<priority>0</priority>
			</liteappext>
			<appbrandext>
				<litegameinfo />
				<priority>-1</priority>
			</appbrandext>
			<gameshareid />
			<sharedata />
			<isvideo>0</isvideo>
			<duration>-1</duration>
			<isexposed>0</isexposed>
			<readtext />
		</gameshare>
		<mpsharetrace>
			<hasfinderelement>0</hasfinderelement>
			<lastgmsgid />
		</mpsharetrace>
		<wxgamecard>
			<framesetname />
			<mbcarddata />
			<minpkgversion />
			<mbcardheight>0</mbcardheight>
			<isoldversion>0</isoldversion>
		</wxgamecard>
		<liteapp>
			<id>null</id>
			<path />
			<query />
		</liteapp>
	</appmsg>
	<fromusername>wxid_lneb7n23o4lg12</fromusername>
	<scene>0</scene>
	<appinfo>
		<version>29</version>
		<appname>摇一摇搜歌</appname>
	</appinfo>
	<commenturl></commenturl>
</msg>

2025-07-22 11:17:43 | DEBUG | XML消息类型: 3
2025-07-22 11:17:43 | DEBUG | XML消息标题: 恭喜发财，大吉大利
2025-07-22 11:17:43 | DEBUG | XML消息描述: 领取红包
2025-07-22 11:17:43 | DEBUG | 附件信息 totallen: 0
2025-07-22 11:17:43 | DEBUG | 附件信息 islargefilemsg: 0
2025-07-22 11:17:43 | INFO | 收到红包消息: 标题:恭喜发财，大吉大利 描述:领取红包 来自:48097389945@chatroom 发送人:wxid_4usgcju5ey9q29
2025-07-22 11:18:20 | DEBUG | 收到消息: {'MsgId': 244030544, 'FromUserName': {'string': '55878994168@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 1, 'Content': {'string': 'wxid_ubbh6q832tcs21:\n开启美图模式'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1753154312, 'MsgSource': '<msgsource>\n\t<pua>1</pua>\n\t<eggIncluded>1</eggIncluded>\n\t<silence>0</silence>\n\t<membercount>3</membercount>\n\t<signature>N0_V1_ag22Y4R3|v1_f0zLBqtW</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'PushContent': '郭 : 开启美图模式', 'NewMsgId': 3324332388526095764, 'MsgSeq': 871390551}
2025-07-22 11:18:20 | INFO | 收到文本消息: 消息ID:244030544 来自:55878994168@chatroom 发送人:wxid_ubbh6q832tcs21 @:[] 内容:开启美图模式
2025-07-22 11:18:21 | INFO | 发送文字消息: 对方wxid:55878994168@chatroom at: 内容:✅ 已进入美图AI模式，正在初始化...
2025-07-22 11:18:30 | INFO | 发送文字消息: 对方wxid:55878994168@chatroom at: 内容:你好呀！我是你的影像创作小助手RoboNeo~ 无论是修图、做设计还是视频创作，只要告诉我你的想法，我就能帮你轻松实现！今天想创作点什么呢？✨
2025-07-22 11:18:30 | DEBUG | 处理消息内容: '开启美图模式'
2025-07-22 11:18:30 | DEBUG | 消息内容 '开启美图模式' 不匹配任何命令，忽略
2025-07-22 11:18:35 | DEBUG | 收到消息: {'MsgId': 753304666, 'FromUserName': {'string': '55878994168@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 1, 'Content': {'string': 'wxid_ubbh6q832tcs21:\n画个人'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1753154327, 'MsgSource': '<msgsource>\n\t<pua>1</pua>\n\t<eggIncluded>1</eggIncluded>\n\t<silence>0</silence>\n\t<membercount>3</membercount>\n\t<signature>N0_V1_BF6Lpal5|v1_+9r+XU6z</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'PushContent': '郭 : 画个人', 'NewMsgId': 7652845534525807579, 'MsgSeq': 871390556}
2025-07-22 11:18:35 | INFO | 收到文本消息: 消息ID:753304666 来自:55878994168@chatroom 发送人:wxid_ubbh6q832tcs21 @:[] 内容:画个人
2025-07-22 11:18:49 | INFO | 发送文字消息: 对方wxid:55878994168@chatroom at: 内容:🤖 AI询问: 你希望这个人物是什么风格？（例如：写实风、二次元动漫、Q版通、赛博朋克等）
这个人物有什么特别的性格特征或身份背景吗？（例如：活泼开朗的少女、冷酷的战士、神秘的魔法师等）
需要特定姿势或表情吗？（例如：站立、战斗姿态、微笑等）
2025-07-22 11:18:49 | DEBUG | 处理消息内容: '画个人'
2025-07-22 11:18:49 | DEBUG | 消息内容 '画个人' 不匹配任何命令，忽略
2025-07-22 11:19:10 | DEBUG | 收到消息: {'MsgId': 133073674, 'FromUserName': {'string': '27852221909@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 3, 'Content': {'string': 'wxid_bmzp9achod6922:\n<?xml version="1.0"?>\n<msg>\n\t<img aeskey="b4d7fedb35acb846ebe44e80c3e93ca5" encryver="1" cdnthumbaeskey="b4d7fedb35acb846ebe44e80c3e93ca5" cdnthumburl="3057020100044b30490201000204fa8eeb7b02032e6c630204b0ab016f0204687f032f042463653564643336332d373262632d346163312d626632312d316661353537386163313037020405150a020201000405004c50bb00" cdnthumblength="4172" cdnthumbheight="55" cdnthumbwidth="120" cdnmidheight="0" cdnmidwidth="0" cdnhdheight="0" cdnhdwidth="0" cdnmidimgurl="3057020100044b30490201000204fa8eeb7b02032e6c630204b0ab016f0204687f032f042463653564643336332d373262632d346163312d626632312d316661353537386163313037020405150a020201000405004c50bb00" length="163777" md5="28a98ec73edb164fb6f1405f76d1de95" hevc_mid_size="163777" originsourcemd5="6a2faba419ce33012d7c3d051b05e9c2">\n\t\t<secHashInfoBase64>eyJwaGFzaCI6IjdjMDA3MTUwMDExMDAwMDAiLCJwZHFoYXNoIjoiY2Q5ZGFhYzY4NTE1YWE5NjE0Y2QxZWU2Mjc0YzVkNDJhYTRjOWY3MDBhZjVmNjg5NGRmMmFlOGQ3NDM2MDFiYiJ9</secHashInfoBase64>\n\t\t<live>\n\t\t\t<duration>0</duration>\n\t\t\t<size>0</size>\n\t\t\t<md5 />\n\t\t\t<fileid />\n\t\t\t<hdsize>0</hdsize>\n\t\t\t<hdmd5 />\n\t\t\t<hdfileid />\n\t\t\t<stillimagetimems>0</stillimagetimems>\n\t\t</live>\n\t</img>\n\t<platform_signature />\n\t<imgdatahash />\n\t<ImgSourceInfo>\n\t\t<ImgSourceUrl />\n\t\t<BizType>0</BizType>\n\t</ImgSourceInfo>\n</msg>\n'}, 'Status': 3, 'ImgStatus': 2, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1753154361, 'MsgSource': '<msgsource>\n\t<bizflag>0</bizflag>\n\t<sec_msg_node>\n\t\t<uuid>c4b155229bb9f23d8b3836731ab7d4f0_</uuid>\n\t\t<risk-file-flag />\n\t\t<risk-file-md5-list />\n\t</sec_msg_node>\n\t<silence>1</silence>\n\t<membercount>146</membercount>\n\t<signature>N0_V1_mOlr6HK7|v1_9cJ/VwEn</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'NewMsgId': 3307361233184645769, 'MsgSeq': 871390559}
2025-07-22 11:19:10 | INFO | 收到图片消息: 消息ID:133073674 来自:27852221909@chatroom 发送人:wxid_bmzp9achod6922 XML:<?xml version="1.0"?><msg><img aeskey="b4d7fedb35acb846ebe44e80c3e93ca5" encryver="1" cdnthumbaeskey="b4d7fedb35acb846ebe44e80c3e93ca5" cdnthumburl="3057020100044b30490201000204fa8eeb7b02032e6c630204b0ab016f0204687f032f042463653564643336332d373262632d346163312d626632312d316661353537386163313037020405150a020201000405004c50bb00" cdnthumblength="4172" cdnthumbheight="55" cdnthumbwidth="120" cdnmidheight="0" cdnmidwidth="0" cdnhdheight="0" cdnhdwidth="0" cdnmidimgurl="3057020100044b30490201000204fa8eeb7b02032e6c630204b0ab016f0204687f032f042463653564643336332d373262632d346163312d626632312d316661353537386163313037020405150a020201000405004c50bb00" length="163777" md5="28a98ec73edb164fb6f1405f76d1de95" hevc_mid_size="163777" originsourcemd5="6a2faba419ce33012d7c3d051b05e9c2"><secHashInfoBase64>eyJwaGFzaCI6IjdjMDA3MTUwMDExMDAwMDAiLCJwZHFoYXNoIjoiY2Q5ZGFhYzY4NTE1YWE5NjE0Y2QxZWU2Mjc0YzVkNDJhYTRjOWY3MDBhZjVmNjg5NGRmMmFlOGQ3NDM2MDFiYiJ9</secHashInfoBase64><live><duration>0</duration><size>0</size><md5 /><fileid /><hdsize>0</hdsize><hdmd5 /><hdfileid /><stillimagetimems>0</stillimagetimems></live></img><platform_signature /><imgdatahash /><ImgSourceInfo><ImgSourceUrl /><BizType>0</BizType></ImgSourceInfo></msg>
2025-07-22 11:19:10 | INFO | [ImageEcho] 保存图片信息成功，当前群 27852221909@chatroom 已存储 5 张图片
2025-07-22 11:19:10 | INFO | [TimerTask] 缓存图片消息: 133073674
2025-07-22 11:19:15 | DEBUG | 收到消息: {'MsgId': 1170017594, 'FromUserName': {'string': '27852221909@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 1, 'Content': {'string': 'wxid_bmzp9achod6922:\n打西瓜滴滴'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1753154366, 'MsgSource': '<msgsource>\n\t<bizflag>0</bizflag>\n\t<pua>1</pua>\n\t<eggIncluded>1</eggIncluded>\n\t<silence>1</silence>\n\t<membercount>146</membercount>\n\t<signature>N0_V1_MgoewUdU|v1_BQUhCOgB</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'NewMsgId': 9028602790212105391, 'MsgSeq': 871390560}
2025-07-22 11:19:15 | INFO | 收到文本消息: 消息ID:1170017594 来自:27852221909@chatroom 发送人:wxid_bmzp9achod6922 @:[] 内容:打西瓜滴滴
2025-07-22 11:19:15 | DEBUG | 处理消息内容: '打西瓜滴滴'
2025-07-22 11:19:15 | DEBUG | 消息内容 '打西瓜滴滴' 不匹配任何命令，忽略
2025-07-22 11:19:17 | DEBUG | 收到消息: {'MsgId': 984116242, 'FromUserName': {'string': '55878994168@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 1, 'Content': {'string': 'wxid_ubbh6q832tcs21:\n写实，战斗姿态'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1753154370, 'MsgSource': '<msgsource>\n\t<pua>1</pua>\n\t<eggIncluded>1</eggIncluded>\n\t<silence>0</silence>\n\t<membercount>3</membercount>\n\t<signature>N0_V1_qDqLqvi+|v1_4bTaeg8O</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'PushContent': '郭 : 写实，战斗姿态', 'NewMsgId': 4673667163209866553, 'MsgSeq': 871390561}
2025-07-22 11:19:17 | INFO | 收到文本消息: 消息ID:984116242 来自:55878994168@chatroom 发送人:wxid_ubbh6q832tcs21 @:[] 内容:写实，战斗姿态
2025-07-22 11:19:49 | INFO | 发送文字消息: 对方wxid:55878994168@chatroom at: 内容:我正在获取实战斗风格角色的设计要点，这将帮助我们创作出更专业的形象。
我正在根据你的要求创作一个写实战斗风格的虚拟战士形象，这将是一个充满力量感和动态感的角色设计。

2025-07-22 11:19:53 | INFO | 发送图片消息: 对方wxid:55878994168@chatroom 图片base64略
2025-07-22 11:19:53 | DEBUG | [RevokePlugin] 无法获取图片数据进行缓存，image类型: <class 'pathlib.WindowsPath'>
2025-07-22 11:19:54 | INFO | 发送文字消息: 对方wxid:55878994168@chatroom at: 内容:已完成一个写实风格的战斗姿态虚拟战士形象设计。这个角色身穿铠甲，手持长剑，表情坚毅，整体充满力量感和动态感。光影效果突出了铠甲的质感和肌肉结构，背景做了模糊处理以突出人物主体。你觉得这个设计如何？如果需要调整或添加细节，可以告诉我。
2025-07-22 11:19:54 | DEBUG | 处理消息内容: '写实，战斗姿态'
2025-07-22 11:19:54 | DEBUG | 消息内容 '写实，战斗姿态' 不匹配任何命令，忽略
2025-07-22 11:19:57 | DEBUG | 收到消息: {'MsgId': 393054453, 'FromUserName': {'string': '48097389945@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 49, 'Content': {'string': 'wxid_wlnzvr8ivgd422:\n<?xml version="1.0"?>\n<msg>\n\t<appmsg appid="" sdkver="0">\n\t\t<title>打小郭</title>\n\t\t<des />\n\t\t<username />\n\t\t<action>view</action>\n\t\t<type>57</type>\n\t\t<showtype>0</showtype>\n\t\t<content />\n\t\t<url />\n\t\t<lowurl />\n\t\t<forwardflag>0</forwardflag>\n\t\t<dataurl />\n\t\t<lowdataurl />\n\t\t<contentattr>0</contentattr>\n\t\t<streamvideo>\n\t\t\t<streamvideourl />\n\t\t\t<streamvideototaltime>0</streamvideototaltime>\n\t\t\t<streamvideotitle />\n\t\t\t<streamvideowording />\n\t\t\t<streamvideoweburl />\n\t\t\t<streamvideothumburl />\n\t\t\t<streamvideoaduxinfo />\n\t\t\t<streamvideopublishid />\n\t\t</streamvideo>\n\t\t<canvasPageItem>\n\t\t\t<canvasPageXml><![CDATA[]]></canvasPageXml>\n\t\t</canvasPageItem>\n\t\t<refermsg>\n\t\t\t<type>1</type>\n\t\t\t<svrid>5025699819623486837</svrid>\n\t\t\t<fromusr>48097389945@chatroom</fromusr>\n\t\t\t<chatusr>wxid_ubbh6q832tcs21</chatusr>\n\t\t\t<displayname>郭</displayname>\n\t\t\t<msgsource>&lt;msgsource&gt;\n\t&lt;pua&gt;1&lt;/pua&gt;\n\t&lt;eggIncluded&gt;1&lt;/eggIncluded&gt;\n\t&lt;silence&gt;1&lt;/silence&gt;\n\t&lt;membercount&gt;62&lt;/membercount&gt;\n\t&lt;signature&gt;N0_V1_PofE2U8G|v1_FstqnPpo&lt;/signature&gt;\n\t&lt;tmp_node&gt;\n\t\t&lt;publisher-id&gt;&lt;/publisher-id&gt;\n\t&lt;/tmp_node&gt;\n&lt;/msgsource&gt;\n</msgsource>\n\t\t\t<content>\n好听的话</content>\n\t\t\t<strid />\n\t\t\t<createtime>1753154191</createtime>\n\t\t</refermsg>\n\t\t<appattach>\n\t\t\t<totallen>0</totallen>\n\t\t\t<attachid />\n\t\t\t<cdnattachurl />\n\t\t\t<emoticonmd5 />\n\t\t\t<aeskey />\n\t\t\t<fileext />\n\t\t\t<islargefilemsg>0</islargefilemsg>\n\t\t</appattach>\n\t\t<extinfo />\n\t\t<androidsource>0</androidsource>\n\t\t<thumburl />\n\t\t<mediatagname />\n\t\t<messageaction><![CDATA[]]></messageaction>\n\t\t<messageext><![CDATA[]]></messageext>\n\t\t<emoticongift>\n\t\t\t<packageflag>0</packageflag>\n\t\t\t<packageid />\n\t\t</emoticongift>\n\t\t<emoticonshared>\n\t\t\t<packageflag>0</packageflag>\n\t\t\t<packageid />\n\t\t</emoticonshared>\n\t\t<designershared>\n\t\t\t<designeruin>0</designeruin>\n\t\t\t<designername>null</designername>\n\t\t\t<designerrediretcturl><![CDATA[null]]></designerrediretcturl>\n\t\t</designershared>\n\t\t<emotionpageshared>\n\t\t\t<tid>0</tid>\n\t\t\t<title>null</title>\n\t\t\t<desc>null</desc>\n\t\t\t<iconUrl><![CDATA[null]]></iconUrl>\n\t\t\t<secondUrl>null</secondUrl>\n\t\t\t<pageType>0</pageType>\n\t\t\t<setKey>null</setKey>\n\t\t</emotionpageshared>\n\t\t<webviewshared>\n\t\t\t<shareUrlOriginal />\n\t\t\t<shareUrlOpen />\n\t\t\t<jsAppId />\n\t\t\t<publisherId />\n\t\t\t<publisherReqId />\n\t\t</webviewshared>\n\t\t<template_id />\n\t\t<md5 />\n\t\t<websearch>\n\t\t\t<rec_category>0</rec_category>\n\t\t\t<channelId>0</channelId>\n\t\t</websearch>\n\t\t<weappinfo>\n\t\t\t<username />\n\t\t\t<appid />\n\t\t\t<appservicetype>0</appservicetype>\n\t\t\t<secflagforsinglepagemode>0</secflagforsinglepagemode>\n\t\t\t<videopageinfo>\n\t\t\t\t<thumbwidth>0</thumbwidth>\n\t\t\t\t<thumbheight>0</thumbheight>\n\t\t\t\t<fromopensdk>0</fromopensdk>\n\t\t\t</videopageinfo>\n\t\t</weappinfo>\n\t\t<statextstr />\n\t\t<musicShareItem>\n\t\t\t<musicDuration>0</musicDuration>\n\t\t</musicShareItem>\n\t\t<finderLiveProductShare>\n\t\t\t<finderLiveID><![CDATA[]]></finderLiveID>\n\t\t\t<finderUsername><![CDATA[]]></finderUsername>\n\t\t\t<finderObjectID><![CDATA[]]></finderObjectID>\n\t\t\t<finderNonceID><![CDATA[]]></finderNonceID>\n\t\t\t<liveStatus><![CDATA[]]></liveStatus>\n\t\t\t<appId><![CDATA[]]></appId>\n\t\t\t<pagePath><![CDATA[]]></pagePath>\n\t\t\t<productId><![CDATA[]]></productId>\n\t\t\t<coverUrl><![CDATA[]]></coverUrl>\n\t\t\t<productTitle><![CDATA[]]></productTitle>\n\t\t\t<marketPrice><![CDATA[0]]></marketPrice>\n\t\t\t<sellingPrice><![CDATA[0]]></sellingPrice>\n\t\t\t<platformHeadImg><![CDATA[]]></platformHeadImg>\n\t\t\t<platformName><![CDATA[]]></platformName>\n\t\t\t<shopWindowId><![CDATA[]]></shopWindowId>\n\t\t\t<flashSalePrice><![CDATA[0]]></flashSalePrice>\n\t\t\t<flashSaleEndTime><![CDATA[0]]></flashSaleEndTime>\n\t\t\t<ecSource><![CDATA[]]></ecSource>\n\t\t\t<sellingPriceWording><![CDATA[]]></sellingPriceWording>\n\t\t\t<platformIconURL><![CDATA[]]></platformIconURL>\n\t\t\t<firstProductTagURL><![CDATA[]]></firstProductTagURL>\n\t\t\t<firstProductTagAspectRatioString><![CDATA[0.0]]></firstProductTagAspectRatioString>\n\t\t\t<secondProductTagURL><![CDATA[]]></secondProductTagURL>\n\t\t\t<secondProductTagAspectRatioString><![CDATA[0.0]]></secondProductTagAspectRatioString>\n\t\t\t<firstGuaranteeWording><![CDATA[]]></firstGuaranteeWording>\n\t\t\t<secondGuaranteeWording><![CDATA[]]></secondGuaranteeWording>\n\t\t\t<thirdGuaranteeWording><![CDATA[]]></thirdGuaranteeWording>\n\t\t\t<isPriceBeginShow>false</isPriceBeginShow>\n\t\t\t<lastGMsgID><![CDATA[]]></lastGMsgID>\n\t\t\t<promoterKey><![CDATA[]]></promoterKey>\n\t\t\t<discountWording><![CDATA[]]></discountWording>\n\t\t\t<priceSuffixDescription><![CDATA[]]></priceSuffixDescription>\n\t\t\t<productCardKey><![CDATA[]]></productCardKey>\n\t\t\t<isWxShop><![CDATA[]]></isWxShop>\n\t\t\t<brandIconUrl><![CDATA[]]></brandIconUrl>\n\t\t\t<rIconUrl><![CDATA[]]></rIconUrl>\n\t\t\t<rIconUrlDarkMode><![CDATA[]]></rIconUrlDarkMode>\n\t\t\t<topShopIconUrl><![CDATA[]]></topShopIconUrl>\n\t\t\t<topShopIconUrlDarkMode><![CDATA[]]></topShopIconUrlDarkMode>\n\t\t\t<simplifyTopShopIconUrl><![CDATA[]]></simplifyTopShopIconUrl>\n\t\t\t<simplifyTopShopIconUrlDarkmode><![CDATA[]]></simplifyTopShopIconUrlDarkmode>\n\t\t\t<topShopIconWidth><![CDATA[0]]></topShopIconWidth>\n\t\t\t<topShopIconHeight><![CDATA[0]]></topShopIconHeight>\n\t\t\t<simplifyTopShopIconWidth><![CDATA[0]]></simplifyTopShopIconWidth>\n\t\t\t<simplifyTopShopIconHeight><![CDATA[0]]></simplifyTopShopIconHeight>\n\t\t\t<showBoxItemStringList />\n\t\t</finderLiveProductShare>\n\t\t<finderOrder>\n\t\t\t<appID><![CDATA[]]></appID>\n\t\t\t<orderID><![CDATA[]]></orderID>\n\t\t\t<path><![CDATA[]]></path>\n\t\t\t<priceWording><![CDATA[]]></priceWording>\n\t\t\t<stateWording><![CDATA[]]></stateWording>\n\t\t\t<productImageURL><![CDATA[]]></productImageURL>\n\t\t\t<products><![CDATA[]]></products>\n\t\t\t<productsCount><![CDATA[0]]></productsCount>\n\t\t\t<orderType><![CDATA[0]]></orderType>\n\t\t\t<newPriceWording><![CDATA[]]></newPriceWording>\n\t\t\t<newStateWording><![CDATA[]]></newStateWording>\n\t\t\t<useNewWording><![CDATA[0]]></useNewWording>\n\t\t</finderOrder>\n\t\t<finderShopWindowShare>\n\t\t\t<finderUsername><![CDATA[]]></finderUsername>\n\t\t\t<avatar><![CDATA[]]></avatar>\n\t\t\t<nickname><![CDATA[]]></nickname>\n\t\t\t<commodityInStockCount><![CDATA[]]></commodityInStockCount>\n\t\t\t<appId><![CDATA[]]></appId>\n\t\t\t<path><![CDATA[]]></path>\n\t\t\t<appUsername><![CDATA[]]></appUsername>\n\t\t\t<query><![CDATA[]]></query>\n\t\t\t<liteAppId><![CDATA[]]></liteAppId>\n\t\t\t<liteAppPath><![CDATA[]]></liteAppPath>\n\t\t\t<liteAppQuery><![CDATA[]]></liteAppQuery>\n\t\t\t<platformTagURL><![CDATA[]]></platformTagURL>\n\t\t\t<saleWording><![CDATA[]]></saleWording>\n\t\t\t<lastGMsgID><![CDATA[]]></lastGMsgID>\n\t\t\t<profileTypeWording><![CDATA[]]></profileTypeWording>\n\t\t\t<saleWordingExtra><![CDATA[]]></saleWordingExtra>\n\t\t\t<isWxShop><![CDATA[]]></isWxShop>\n\t\t\t<platformIconUrl><![CDATA[]]></platformIconUrl>\n\t\t\t<brandIconUrl><![CDATA[]]></brandIconUrl>\n\t\t\t<description><![CDATA[]]></description>\n\t\t\t<backgroundUrl><![CDATA[]]></backgroundUrl>\n\t\t\t<darkModePlatformIconUrl><![CDATA[]]></darkModePlatformIconUrl>\n\t\t\t<rIconUrl><![CDATA[]]></rIconUrl>\n\t\t\t<rIconUrlDarkMode><![CDATA[]]></rIconUrlDarkMode>\n\t\t\t<rWords><![CDATA[]]></rWords>\n\t\t\t<topShopIconUrl><![CDATA[]]></topShopIconUrl>\n\t\t\t<topShopIconUrlDarkMode><![CDATA[]]></topShopIconUrlDarkMode>\n\t\t\t<simplifyTopShopIconUrl><![CDATA[]]></simplifyTopShopIconUrl>\n\t\t\t<simplifyTopShopIconUrlDarkmode><![CDATA[]]></simplifyTopShopIconUrlDarkmode>\n\t\t\t<topShopIconWidth><![CDATA[0]]></topShopIconWidth>\n\t\t\t<topShopIconHeight><![CDATA[0]]></topShopIconHeight>\n\t\t\t<simplifyTopShopIconWidth><![CDATA[0]]></simplifyTopShopIconWidth>\n\t\t\t<simplifyTopShopIconHeight><![CDATA[0]]></simplifyTopShopIconHeight>\n\t\t\t<reputationInfo>\n\t\t\t\t<hasReputationInfo>0</hasReputationInfo>\n\t\t\t\t<reputationScore>0</reputationScore>\n\t\t\t\t<reputationWording />\n\t\t\t\t<reputationTextColor />\n\t\t\t\t<reputationLevelWording />\n\t\t\t\t<reputationBackgroundColor />\n\t\t\t</reputationInfo>\n\t\t\t<productImageURLList />\n\t\t</finderShopWindowShare>\n\t\t<findernamecard>\n\t\t\t<username />\n\t\t\t<avatar><![CDATA[]]></avatar>\n\t\t\t<nickname />\n\t\t\t<auth_job />\n\t\t\t<auth_icon>0</auth_icon>\n\t\t\t<auth_icon_url />\n\t\t\t<ecSource><![CDATA[]]></ecSource>\n\t\t\t<lastGMsgID><![CDATA[]]></lastGMsgID>\n\t\t</findernamecard>\n\t\t<finderGuarantee>\n\t\t\t<scene><![CDATA[0]]></scene>\n\t\t</finderGuarantee>\n\t\t<directshare>0</directshare>\n\t\t<gamecenter>\n\t\t\t<namecard>\n\t\t\t\t<iconUrl />\n\t\t\t\t<name />\n\t\t\t\t<desc />\n\t\t\t\t<tail />\n\t\t\t\t<jumpUrl />\n\t\t\t\t<liteappId />\n\t\t\t\t<liteappPath />\n\t\t\t\t<liteappQuery />\n\t\t\t\t<liteappMinVersion />\n\t\t\t</namecard>\n\t\t</gamecenter>\n\t\t<patMsg>\n\t\t\t<chatUser />\n\t\t\t<records>\n\t\t\t\t<recordNum>0</recordNum>\n\t\t\t</records>\n\t\t</patMsg>\n\t\t<secretmsg>\n\t\t\t<issecretmsg>0</issecretmsg>\n\t\t</secretmsg>\n\t\t<referfromscene>0</referfromscene>\n\t\t<gameshare>\n\t\t\t<liteappext>\n\t\t\t\t<liteappbizdata />\n\t\t\t\t<priority>0</priority>\n\t\t\t</liteappext>\n\t\t\t<appbrandext>\n\t\t\t\t<litegameinfo />\n\t\t\t\t<priority>-1</priority>\n\t\t\t</appbrandext>\n\t\t\t<gameshareid />\n\t\t\t<sharedata />\n\t\t\t<isvideo>0</isvideo>\n\t\t\t<duration>-1</duration>\n\t\t\t<isexposed>0</isexposed>\n\t\t\t<readtext />\n\t\t</gameshare>\n\t\t<tingChatRoomItem>\n\t\t\t<type>0</type>\n\t\t\t<categoryItem>null</categoryItem>\n\t\t\t<categoryId />\n\t\t</tingChatRoomItem>\n\t\t<mpsharetrace>\n\t\t\t<hasfinderelement>0</hasfinderelement>\n\t\t\t<lastgmsgid />\n\t\t</mpsharetrace>\n\t\t<wxgamecard>\n\t\t\t<framesetname />\n\t\t\t<mbcarddata />\n\t\t\t<minpkgversion />\n\t\t\t<clientextinfo />\n\t\t\t<mbcardheight>0</mbcardheight>\n\t\t\t<isoldversion>0</isoldversion>\n\t\t</wxgamecard>\n\t\t<ecskfcard>\n\t\t\t<framesetname />\n\t\t\t<mbcarddata />\n\t\t\t<minupdateunixtimestamp>0</minupdateunixtimestamp>\n\t\t\t<needheader>false</needheader>\n\t\t\t<summary />\n\t\t</ecskfcard>\n\t\t<liteapp>\n\t\t\t<id>null</id>\n\t\t\t<path />\n\t\t\t<query />\n\t\t\t<istransparent>0</istransparent>\n\t\t\t<hideicon>0</hideicon>\n\t\t\t<forbidforward>0</forbidforward>\n\t\t</liteapp>\n\t\t<opensdk_share_is_modified>0</opensdk_share_is_modified>\n\t</appmsg>\n\t<fromusername>wxid_wlnzvr8ivgd422</fromusername>\n\t<scene>0</scene>\n\t<appinfo>\n\t\t<version>1</version>\n\t\t<appname></appname>\n\t</appinfo>\n\t<commenturl></commenturl>\n</msg>\n'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1753154409, 'MsgSource': '<msgsource>\n\t<alnode>\n\t\t<fr>4</fr>\n\t</alnode>\n\t<eggIncluded>1</eggIncluded>\n\t<sec_msg_node>\n\t\t<uuid>f9cdc395cd47bd686fad5c5ea60ced27_</uuid>\n\t\t<risk-file-flag />\n\t\t<risk-file-md5-list />\n\t</sec_msg_node>\n\t<silence>0</silence>\n\t<membercount>62</membercount>\n\t<signature>N0_V1_iTHzsFZG|v1_TRUdXq3m</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'PushContent': '锦岚（开玩笑就退群版） : 打小郭', 'NewMsgId': 1609068295390649330, 'MsgSeq': 871390568}
2025-07-22 11:19:57 | DEBUG | 从群聊消息中提取发送者: wxid_wlnzvr8ivgd422
2025-07-22 11:19:57 | DEBUG | 使用已解析的XML处理引用消息
2025-07-22 11:19:57 | INFO | 收到引用消息: 消息ID:393054453 来自:48097389945@chatroom 发送人:wxid_wlnzvr8ivgd422 内容:打小郭 引用类型:1
2025-07-22 11:19:58 | INFO | [DouBaoImageToImage] 收到引用消息: 打小郭
2025-07-22 11:19:58 | INFO | 发送表情消息: 对方wxid:48097389945@chatroom md5:fda793ce68ae07b9633e416d89fdcc74 总长度:9992069
2025-07-22 11:19:58 | INFO | [TimerTask] 收到引用消息调试信息:
2025-07-22 11:19:58 | INFO |   - 消息内容: 打小郭
2025-07-22 11:19:58 | INFO |   - 群组ID: 48097389945@chatroom
2025-07-22 11:19:58 | INFO |   - 发送人: wxid_wlnzvr8ivgd422
2025-07-22 11:19:58 | INFO |   - 引用信息: {'MsgType': 1, 'Content': '\n好听的话', 'Msgid': '5025699819623486837', 'NewMsgId': '5025699819623486837', 'ToWxid': 'wxid_4usgcju5ey9q29', 'FromWxid': '48097389945@chatroom', 'Nickname': '郭', 'MsgSource': '<msgsource>\n\t<pua>1</pua>\n\t<eggIncluded>1</eggIncluded>\n\t<silence>1</silence>\n\t<membercount>62</membercount>\n\t<signature>N0_V1_PofE2U8G|v1_FstqnPpo</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'Createtime': '1753154191', 'SenderWxid': 'wxid_wlnzvr8ivgd422'}
2025-07-22 11:19:58 | INFO |   - 引用消息ID: 
2025-07-22 11:19:58 | INFO |   - 引用消息类型: 
2025-07-22 11:19:58 | INFO |   - 引用消息内容: 
好听的话
2025-07-22 11:19:58 | INFO |   - 引用消息发送人: wxid_wlnzvr8ivgd422
2025-07-22 11:20:00 | DEBUG | 收到消息: {'MsgId': 1691091040, 'FromUserName': {'string': '48097389945@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 1, 'Content': {'string': 'wxid_wlnzvr8ivgd422:\n打小郭'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1753154412, 'MsgSource': '<msgsource>\n\t<pua>1</pua>\n\t<eggIncluded>1</eggIncluded>\n\t<silence>0</silence>\n\t<membercount>62</membercount>\n\t<signature>N0_V1_lDdk7vj5|v1_ivhUJXGC</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'PushContent': '锦岚（开玩笑就退群版） : 打小郭', 'NewMsgId': 7388718303170785144, 'MsgSeq': 871390571}
2025-07-22 11:20:00 | INFO | 收到文本消息: 消息ID:1691091040 来自:48097389945@chatroom 发送人:wxid_wlnzvr8ivgd422 @:[] 内容:打小郭
2025-07-22 11:20:00 | INFO | 发送表情消息: 对方wxid:48097389945@chatroom md5:fda793ce68ae07b9633e416d89fdcc74 总长度:9992069
2025-07-22 11:20:00 | DEBUG | 处理消息内容: '打小郭'
2025-07-22 11:20:00 | DEBUG | 消息内容 '打小郭' 不匹配任何命令，忽略
2025-07-22 11:20:22 | DEBUG | 收到消息: {'MsgId': 664064742, 'FromUserName': {'string': '55878994168@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 1, 'Content': {'string': 'wxid_ubbh6q832tcs21:\n换成女战士，颜值担当'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1753154434, 'MsgSource': '<msgsource>\n\t<pua>1</pua>\n\t<eggIncluded>1</eggIncluded>\n\t<silence>0</silence>\n\t<membercount>3</membercount>\n\t<signature>N0_V1_S1ed64QA|v1_jWoSW3Yk</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'PushContent': '郭 : 换成女战士，颜值担当', 'NewMsgId': 1262099869573543368, 'MsgSeq': 871390574}
2025-07-22 11:20:22 | INFO | 收到文本消息: 消息ID:664064742 来自:55878994168@chatroom 发送人:wxid_ubbh6q832tcs21 @:[] 内容:换成女战士，颜值担当
2025-07-22 11:21:07 | INFO | 发送文字消息: 对方wxid:55878994168@chatroom at: 内容:好嘞，马上为你把这位战士“变身”成英姿飒爽又颜值超高的女战士，让我好好构思怎么调整最完美~正在将战斗姿态的男性战士形象转换为高颜值女性版本，会保持原有的铠甲装备和战斗动作不变，重点优化面部特征和发型。

2025-07-22 11:21:11 | INFO | 发送图片消息: 对方wxid:55878994168@chatroom 图片base64略
2025-07-22 11:21:11 | DEBUG | [RevokePlugin] 无法获取图片数据进行缓存，image类型: <class 'pathlib.WindowsPath'>
2025-07-22 11:22:08 | INFO | 发送文字消息: 对方wxid:55878994168@chatroom at: 内容:将对女性战士的面部进行精致美化，提升五官立体感和肤质，同时保持战斗场景的真实感。
正在调整整体气质与光影效果，使女性战士的面部与战斗装备的光影更加协调统一。
已完成将男性战士转换为高颜值女性战士的形象设计。新的形象保留了原有的战斗姿态、铠甲装备和武器，同时优化了面部特征：1) 调整为精致的女性五官；2) 提升了肤质和面部立体感；3) 调整了发型为适合战斗的束发造型；4) 统一了面部与铠甲的光影效果。整体既保持了战斗场景的真实感，又突出了女性战士的颜值担当特质。如果需要进一步调整任何细节，可以随时告诉我。
2025-07-22 11:22:08 | DEBUG | 处理消息内容: '换成女战士，颜值担当'
2025-07-22 11:22:08 | DEBUG | 消息内容 '换成女战士，颜值担当' 不匹配任何命令，忽略
2025-07-22 11:22:08 | DEBUG | 收到消息: {'MsgId': 443145628, 'FromUserName': {'string': '48097389945@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 49, 'Content': {'string': 'wxid_e3o8s2nf9u2o22:\n<?xml version="1.0"?>\n<msg>\n\t<appmsg appid="" sdkver="0">\n\t\t<title>搞笑的</title>\n\t\t<des />\n\t\t<username />\n\t\t<action>view</action>\n\t\t<type>57</type>\n\t\t<showtype>0</showtype>\n\t\t<content />\n\t\t<url />\n\t\t<lowurl />\n\t\t<forwardflag>0</forwardflag>\n\t\t<dataurl />\n\t\t<lowdataurl />\n\t\t<contentattr>0</contentattr>\n\t\t<streamvideo>\n\t\t\t<streamvideourl />\n\t\t\t<streamvideototaltime>0</streamvideototaltime>\n\t\t\t<streamvideotitle />\n\t\t\t<streamvideowording />\n\t\t\t<streamvideoweburl />\n\t\t\t<streamvideothumburl />\n\t\t\t<streamvideoaduxinfo />\n\t\t\t<streamvideopublishid />\n\t\t</streamvideo>\n\t\t<canvasPageItem>\n\t\t\t<canvasPageXml><![CDATA[]]></canvasPageXml>\n\t\t</canvasPageItem>\n\t\t<refermsg>\n\t\t\t<type>49</type>\n\t\t\t<svrid>981361057820205046</svrid>\n\t\t\t<fromusr>48097389945@chatroom</fromusr>\n\t\t\t<chatusr>wxid_laurnst5xn0q22</chatusr>\n\t\t\t<displayname>متسول暗</displayname>\n\t\t\t<msgsource>&lt;msgsource&gt;\n\t&lt;alnode&gt;\n\t\t&lt;fr&gt;4&lt;/fr&gt;\n\t&lt;/alnode&gt;\n\t&lt;eggIncluded&gt;1&lt;/eggIncluded&gt;\n\t&lt;sec_msg_node&gt;\n\t\t&lt;uuid&gt;87942da53127f8800ab2adddd13ca42d_&lt;/uuid&gt;\n\t\t&lt;risk-file-flag /&gt;\n\t\t&lt;risk-file-md5-list /&gt;\n\t&lt;/sec_msg_node&gt;\n\t&lt;silence&gt;1&lt;/silence&gt;\n\t&lt;membercount&gt;62&lt;/membercount&gt;\n\t&lt;signature&gt;N0_V1_eKv1ztUk|v1_vJiMZ8Fs&lt;/signature&gt;\n\t&lt;tmp_node&gt;\n\t\t&lt;publisher-id&gt;&lt;/publisher-id&gt;\n\t&lt;/tmp_node&gt;\n&lt;/msgsource&gt;\n</msgsource>\n\t\t\t<content>&lt;?xml version="1.0"?&gt;\n&lt;msg&gt;\n\t&lt;appmsg appid="" sdkver="0"&gt;\n\t\t&lt;title&gt;高尔夫啥的我觉得就不错了&lt;/title&gt;\n\t\t&lt;des /&gt;\n\t\t&lt;username /&gt;\n\t\t&lt;action&gt;view&lt;/action&gt;\n\t\t&lt;type&gt;57&lt;/type&gt;\n\t\t&lt;showtype&gt;0&lt;/showtype&gt;\n\t\t&lt;content /&gt;\n\t\t&lt;url /&gt;\n\t\t&lt;lowurl /&gt;\n\t\t&lt;forwardflag&gt;0&lt;/forwardflag&gt;\n\t\t&lt;dataurl /&gt;\n\t\t&lt;lowdataurl /&gt;\n\t\t&lt;contentattr&gt;0&lt;/contentattr&gt;\n\t\t&lt;streamvideo&gt;\n\t\t\t&lt;streamvideourl /&gt;\n\t\t\t&lt;streamvideototaltime&gt;0&lt;/streamvideototaltime&gt;\n\t\t\t&lt;streamvideotitle /&gt;\n\t\t\t&lt;streamvideowording /&gt;\n\t\t\t&lt;streamvideoweburl /&gt;\n\t\t\t&lt;streamvideothumburl /&gt;\n\t\t\t&lt;streamvideoaduxinfo /&gt;\n\t\t\t&lt;streamvideopublishid /&gt;\n\t\t&lt;/streamvideo&gt;\n\t\t&lt;canvasPageItem&gt;\n\t\t\t&lt;canvasPageXml&gt;&lt;![CDATA[]]&gt;&lt;/canvasPageXml&gt;\n\t\t&lt;/canvasPageItem&gt;\n\t\t&lt;refermsg&gt;&lt;/refermsg&gt;\n\t\t&lt;appattach&gt;\n\t\t\t&lt;totallen&gt;0&lt;/totallen&gt;\n\t\t\t&lt;attachid /&gt;\n\t\t\t&lt;cdnattachurl /&gt;\n\t\t\t&lt;emoticonmd5&gt;&lt;/emoticonmd5&gt;\n\t\t\t&lt;aeskey&gt;&lt;/aeskey&gt;\n\t\t\t&lt;fileext /&gt;\n\t\t\t&lt;islargefilemsg&gt;0&lt;/islargefilemsg&gt;\n\t\t&lt;/appattach&gt;\n\t\t&lt;extinfo /&gt;\n\t\t&lt;androidsource&gt;0&lt;/androidsource&gt;\n\t\t&lt;thumburl /&gt;\n\t\t&lt;mediatagname /&gt;\n\t\t&lt;messageaction&gt;&lt;![CDATA[]]&gt;&lt;/messageaction&gt;\n\t\t&lt;messageext&gt;&lt;![CDATA[]]&gt;&lt;/messageext&gt;\n\t\t&lt;emoticongift&gt;\n\t\t\t&lt;packageflag&gt;0&lt;/packageflag&gt;\n\t\t\t&lt;packageid /&gt;\n\t\t&lt;/emoticongift&gt;\n\t\t&lt;emoticonshared&gt;\n\t\t\t&lt;packageflag&gt;0&lt;/packageflag&gt;\n\t\t\t&lt;packageid /&gt;\n\t\t&lt;/emoticonshared&gt;\n\t\t&lt;designershared&gt;\n\t\t\t&lt;designeruin&gt;0&lt;/designeruin&gt;\n\t\t\t&lt;designername&gt;null&lt;/designername&gt;\n\t\t\t&lt;designerrediretcturl&gt;&lt;![CDATA[null]]&gt;&lt;/designerrediretcturl&gt;\n\t\t&lt;/designershared&gt;\n\t\t&lt;emotionpageshared&gt;\n\t\t\t&lt;tid&gt;0&lt;/tid&gt;\n\t\t\t&lt;title&gt;null&lt;/title&gt;\n\t\t\t&lt;desc&gt;null&lt;/desc&gt;\n\t\t\t&lt;iconUrl&gt;&lt;![CDATA[null]]&gt;&lt;/iconUrl&gt;\n\t\t\t&lt;secondUrl&gt;null&lt;/secondUrl&gt;\n\t\t\t&lt;pageType&gt;0&lt;/pageType&gt;\n\t\t\t&lt;setKey&gt;null&lt;/setKey&gt;\n\t\t&lt;/emotionpageshared&gt;\n\t\t&lt;webviewshared&gt;\n\t\t\t&lt;shareUrlOriginal /&gt;\n\t\t\t&lt;shareUrlOpen /&gt;\n\t\t\t&lt;jsAppId /&gt;\n\t\t\t&lt;publisherId /&gt;\n\t\t\t&lt;publisherReqId /&gt;\n\t\t&lt;/webviewshared&gt;\n\t\t&lt;template_id /&gt;\n\t\t&lt;md5 /&gt;\n\t\t&lt;websearch&gt;\n\t\t\t&lt;rec_category&gt;0&lt;/rec_category&gt;\n\t\t\t&lt;channelId&gt;0&lt;/channelId&gt;\n\t\t&lt;/websearch&gt;\n\t\t&lt;weappinfo&gt;\n\t\t\t&lt;username /&gt;\n\t\t\t&lt;appid /&gt;\n\t\t\t&lt;appservicetype&gt;0&lt;/appservicetype&gt;\n\t\t\t&lt;secflagforsinglepagemode&gt;0&lt;/secflagforsinglepagemode&gt;\n\t\t\t&lt;videopageinfo&gt;\n\t\t\t\t&lt;thumbwidth&gt;0&lt;/thumbwidth&gt;\n\t\t\t\t&lt;thumbheight&gt;0&lt;/thumbheight&gt;\n\t\t\t\t&lt;fromopensdk&gt;0&lt;/fromopensdk&gt;\n\t\t\t&lt;/videopageinfo&gt;\n\t\t&lt;/weappinfo&gt;\n\t\t&lt;statextstr /&gt;\n\t\t&lt;musicShareItem&gt;\n\t\t\t&lt;musicDuration&gt;0&lt;/musicDuration&gt;\n\t\t&lt;/musicShareItem&gt;\n\t\t&lt;finderLiveProductShare&gt;\n\t\t\t&lt;finderLiveID&gt;&lt;![CDATA[]]&gt;&lt;/finderLiveID&gt;\n\t\t\t&lt;finderUsername&gt;&lt;![CDATA[]]&gt;&lt;/finderUsername&gt;\n\t\t\t&lt;finderObjectID&gt;&lt;![CDATA[]]&gt;&lt;/finderObjectID&gt;\n\t\t\t&lt;finderNonceID&gt;&lt;![CDATA[]]&gt;&lt;/finderNonceID&gt;\n\t\t\t&lt;liveStatus&gt;&lt;![CDATA[]]&gt;&lt;/liveStatus&gt;\n\t\t\t&lt;appId&gt;&lt;![CDATA[]]&gt;&lt;/appId&gt;\n\t\t\t&lt;pagePath&gt;&lt;![CDATA[]]&gt;&lt;/pagePath&gt;\n\t\t\t&lt;productId&gt;&lt;![CDATA[]]&gt;&lt;/productId&gt;\n\t\t\t&lt;coverUrl&gt;&lt;![CDATA[]]&gt;&lt;/coverUrl&gt;\n\t\t\t&lt;productTitle&gt;&lt;![CDATA[]]&gt;&lt;/productTitle&gt;\n\t\t\t&lt;marketPrice&gt;&lt;![CDATA[0]]&gt;&lt;/marketPrice&gt;\n\t\t\t&lt;sellingPrice&gt;&lt;![CDATA[0]]&gt;&lt;/sellingPrice&gt;\n\t\t\t&lt;platformHeadImg&gt;&lt;![CDATA[]]&gt;&lt;/platformHeadImg&gt;\n\t\t\t&lt;platformName&gt;&lt;![CDATA[]]&gt;&lt;/platformName&gt;\n\t\t\t&lt;shopWindowId&gt;&lt;![CDATA[]]&gt;&lt;/shopWindowId&gt;\n\t\t\t&lt;flashSalePrice&gt;&lt;![CDATA[0]]&gt;&lt;/flashSalePrice&gt;\n\t\t\t&lt;flashSaleEndTime&gt;&lt;![CDATA[0]]&gt;&lt;/flashSaleEndTime&gt;\n\t\t\t&lt;ecSource&gt;&lt;![CDATA[]]&gt;&lt;/ecSource&gt;\n\t\t\t&lt;sellingPriceWording&gt;&lt;![CDATA[]]&gt;&lt;/sellingPriceWording&gt;\n\t\t\t&lt;platformIconURL&gt;&lt;![CDATA[]]&gt;&lt;/platformIconURL&gt;\n\t\t\t&lt;firstProductTagURL&gt;&lt;![CDATA[]]&gt;&lt;/firstProductTagURL&gt;\n\t\t\t&lt;firstProductTagAspectRatioString&gt;&lt;![CDATA[0.0]]&gt;&lt;/firstProductTagAspectRatioString&gt;\n\t\t\t&lt;secondProductTagURL&gt;&lt;![CDATA[]]&gt;&lt;/secondProductTagURL&gt;\n\t\t\t&lt;secondProductTagAspectRatioString&gt;&lt;![CDATA[0.0]]&gt;&lt;/secondProductTagAspectRatioString&gt;\n\t\t\t&lt;firstGuaranteeWording&gt;&lt;![CDATA[]]&gt;&lt;/firstGuaranteeWording&gt;\n\t\t\t&lt;secondGuaranteeWording&gt;&lt;![CDATA[]]&gt;&lt;/secondGuaranteeWording&gt;\n\t\t\t&lt;thirdGuaranteeWording&gt;&lt;![CDATA[]]&gt;&lt;/thirdGuaranteeWording&gt;\n\t\t\t&lt;isPriceBeginShow&gt;false&lt;/isPriceBeginShow&gt;\n\t\t\t&lt;lastGMsgID&gt;&lt;![CDATA[]]&gt;&lt;/lastGMsgID&gt;\n\t\t\t&lt;promoterKey&gt;&lt;![CDATA[]]&gt;&lt;/promoterKey&gt;\n\t\t\t&lt;discountWording&gt;&lt;![CDATA[]]&gt;&lt;/discountWording&gt;\n\t\t\t&lt;priceSuffixDescription&gt;&lt;![CDATA[]]&gt;&lt;/priceSuffixDescription&gt;\n\t\t\t&lt;productCardKey&gt;&lt;![CDATA[]]&gt;&lt;/productCardKey&gt;\n\t\t\t&lt;isWxShop&gt;&lt;![CDATA[]]&gt;&lt;/isWxShop&gt;\n\t\t\t&lt;brandIconUrl&gt;&lt;![CDATA[]]&gt;&lt;/brandIconUrl&gt;\n\t\t\t&lt;rIconUrl&gt;&lt;![CDATA[]]&gt;&lt;/rIconUrl&gt;\n\t\t\t&lt;rIconUrlDarkMode&gt;&lt;![CDATA[]]&gt;&lt;/rIconUrlDarkMode&gt;\n\t\t\t&lt;topShopIconUrl&gt;&lt;![CDATA[]]&gt;&lt;/topShopIconUrl&gt;\n\t\t\t&lt;topShopIconUrlDarkMode&gt;&lt;![CDATA[]]&gt;&lt;/topShopIconUrlDarkMode&gt;\n\t\t\t&lt;simplifyTopShopIconUrl&gt;&lt;![CDATA[]]&gt;&lt;/simplifyTopShopIconUrl&gt;\n\t\t\t&lt;simplifyTopShopIconUrlDarkmode&gt;&lt;![CDATA[]]&gt;&lt;/simplifyTopShopIconUrlDarkmode&gt;\n\t\t\t&lt;topShopIconWidth&gt;&lt;![CDATA[0]]&gt;&lt;/topShopIconWidth&gt;\n\t\t\t&lt;topShopIconHeight&gt;&lt;![CDATA[0]]&gt;&lt;/topShopIconHeight&gt;\n\t\t\t&lt;simplifyTopShopIconWidth&gt;&lt;![CDATA[0]]&gt;&lt;/simplifyTopShopIconWidth&gt;\n\t\t\t&lt;simplifyTopShopIconHeight&gt;&lt;![CDATA[0]]&gt;&lt;/simplifyTopShopIconHeight&gt;\n\t\t\t&lt;showBoxItemStringList /&gt;\n\t\t&lt;/finderLiveProductShare&gt;\n\t\t&lt;finderOrder&gt;\n\t\t\t&lt;appID&gt;&lt;![CDATA[]]&gt;&lt;/appID&gt;\n\t\t\t&lt;orderID&gt;&lt;![CDATA[]]&gt;&lt;/orderID&gt;\n\t\t\t&lt;path&gt;&lt;![CDATA[]]&gt;&lt;/path&gt;\n\t\t\t&lt;priceWording&gt;&lt;![CDATA[]]&gt;&lt;/priceWording&gt;\n\t\t\t&lt;stateWording&gt;&lt;![CDATA[]]&gt;&lt;/stateWording&gt;\n\t\t\t&lt;productImageURL&gt;&lt;![CDATA[]]&gt;&lt;/productImageURL&gt;\n\t\t\t&lt;products&gt;&lt;![CDATA[]]&gt;&lt;/products&gt;\n\t\t\t&lt;productsCount&gt;&lt;![CDATA[0]]&gt;&lt;/productsCount&gt;\n\t\t\t&lt;orderType&gt;&lt;![CDATA[0]]&gt;&lt;/orderType&gt;\n\t\t\t&lt;newPriceWording&gt;&lt;![CDATA[]]&gt;&lt;/newPriceWording&gt;\n\t\t\t&lt;newStateWording&gt;&lt;![CDATA[]]&gt;&lt;/newStateWording&gt;\n\t\t\t&lt;useNewWording&gt;&lt;![CDATA[0]]&gt;&lt;/useNewWording&gt;\n\t\t&lt;/finderOrder&gt;\n\t\t&lt;finderShopWindowShare&gt;\n\t\t\t&lt;finderUsername&gt;&lt;![CDATA[]]&gt;&lt;/finderUsername&gt;\n\t\t\t&lt;avatar&gt;&lt;![CDATA[]]&gt;&lt;/avatar&gt;\n\t\t\t&lt;nickname&gt;&lt;![CDATA[]]&gt;&lt;/nickname&gt;\n\t\t\t&lt;commodityInStockCount&gt;&lt;![CDATA[]]&gt;&lt;/commodityInStockCount&gt;\n\t\t\t&lt;appId&gt;&lt;![CDATA[]]&gt;&lt;/appId&gt;\n\t\t\t&lt;path&gt;&lt;![CDATA[]]&gt;&lt;/path&gt;\n\t\t\t&lt;appUsername&gt;&lt;![CDATA[]]&gt;&lt;/appUsername&gt;\n\t\t\t&lt;query&gt;&lt;![CDATA[]]&gt;&lt;/query&gt;\n\t\t\t&lt;liteAppId&gt;&lt;![CDATA[]]&gt;&lt;/liteAppId&gt;\n\t\t\t&lt;liteAppPath&gt;&lt;![CDATA[]]&gt;&lt;/liteAppPath&gt;\n\t\t\t&lt;liteAppQuery&gt;&lt;![CDATA[]]&gt;&lt;/liteAppQuery&gt;\n\t\t\t&lt;platformTagURL&gt;&lt;![CDATA[]]&gt;&lt;/platformTagURL&gt;\n\t\t\t&lt;saleWording&gt;&lt;![CDATA[]]&gt;&lt;/saleWording&gt;\n\t\t\t&lt;lastGMsgID&gt;&lt;![CDATA[]]&gt;&lt;/lastGMsgID&gt;\n\t\t\t&lt;profileTypeWording&gt;&lt;![CDATA[]]&gt;&lt;/profileTypeWording&gt;\n\t\t\t&lt;saleWordingExtra&gt;&lt;![CDATA[]]&gt;&lt;/saleWordingExtra&gt;\n\t\t\t&lt;isWxShop&gt;&lt;![CDATA[]]&gt;&lt;/isWxShop&gt;\n\t\t\t&lt;platformIconUrl&gt;&lt;![CDATA[]]&gt;&lt;/platformIconUrl&gt;\n\t\t\t&lt;brandIconUrl&gt;&lt;![CDATA[]]&gt;&lt;/brandIconUrl&gt;\n\t\t\t&lt;description&gt;&lt;![CDATA[]]&gt;&lt;/description&gt;\n\t\t\t&lt;backgroundUrl&gt;&lt;![CDATA[]]&gt;&lt;/backgroundUrl&gt;\n\t\t\t&lt;darkModePlatformIconUrl&gt;&lt;![CDATA[]]&gt;&lt;/darkModePlatformIconUrl&gt;\n\t\t\t&lt;rIconUrl&gt;&lt;![CDATA[]]&gt;&lt;/rIconUrl&gt;\n\t\t\t&lt;rIconUrlDarkMode&gt;&lt;![CDATA[]]&gt;&lt;/rIconUrlDarkMode&gt;\n\t\t\t&lt;rWords&gt;&lt;![CDATA[]]&gt;&lt;/rWords&gt;\n\t\t\t&lt;topShopIconUrl&gt;&lt;![CDATA[]]&gt;&lt;/topShopIconUrl&gt;\n\t\t\t&lt;topShopIconUrlDarkMode&gt;&lt;![CDATA[]]&gt;&lt;/topShopIconUrlDarkMode&gt;\n\t\t\t&lt;simplifyTopShopIconUrl&gt;&lt;![CDATA[]]&gt;&lt;/simplifyTopShopIconUrl&gt;\n\t\t\t&lt;simplifyTopShopIconUrlDarkmode&gt;&lt;![CDATA[]]&gt;&lt;/simplifyTopShopIconUrlDarkmode&gt;\n\t\t\t&lt;topShopIconWidth&gt;&lt;![CDATA[0]]&gt;&lt;/topShopIconWidth&gt;\n\t\t\t&lt;topShopIconHeight&gt;&lt;![CDATA[0]]&gt;&lt;/topShopIconHeight&gt;\n\t\t\t&lt;simplifyTopShopIconWidth&gt;&lt;![CDATA[0]]&gt;&lt;/simplifyTopShopIconWidth&gt;\n\t\t\t&lt;simplifyTopShopIconHeight&gt;&lt;![CDATA[0]]&gt;&lt;/simplifyTopShopIconHeight&gt;\n\t\t\t&lt;reputationInfo&gt;\n\t\t\t\t&lt;hasReputationInfo&gt;0&lt;/hasReputationInfo&gt;\n\t\t\t\t&lt;reputationScore&gt;0&lt;/reputationScore&gt;\n\t\t\t\t&lt;reputationWording /&gt;\n\t\t\t\t&lt;reputationTextColor /&gt;\n\t\t\t\t&lt;reputationLevelWording /&gt;\n\t\t\t\t&lt;reputationBackgroundColor /&gt;\n\t\t\t&lt;/reputationInfo&gt;\n\t\t\t&lt;productImageURLList /&gt;\n\t\t&lt;/finderShopWindowShare&gt;\n\t\t&lt;findernamecard&gt;\n\t\t\t&lt;username /&gt;\n\t\t\t&lt;avatar&gt;&lt;![CDATA[]]&gt;&lt;/avatar&gt;\n\t\t\t&lt;nickname /&gt;\n\t\t\t&lt;auth_job /&gt;\n\t\t\t&lt;auth_icon&gt;0&lt;/auth_icon&gt;\n\t\t\t&lt;auth_icon_url /&gt;\n\t\t\t&lt;ecSource&gt;&lt;![CDATA[]]&gt;&lt;/ecSource&gt;\n\t\t\t&lt;lastGMsgID&gt;&lt;![CDATA[]]&gt;&lt;/lastGMsgID&gt;\n\t\t&lt;/findernamecard&gt;\n\t\t&lt;finderGuarantee&gt;\n\t\t\t&lt;scene&gt;&lt;![CDATA[0]]&gt;&lt;/scene&gt;\n\t\t&lt;/finderGuarantee&gt;\n\t\t&lt;directshare&gt;0&lt;/directshare&gt;\n\t\t&lt;gamecenter&gt;\n\t\t\t&lt;namecard&gt;\n\t\t\t\t&lt;iconUrl /&gt;\n\t\t\t\t&lt;name /&gt;\n\t\t\t\t&lt;desc /&gt;\n\t\t\t\t&lt;tail /&gt;\n\t\t\t\t&lt;jumpUrl /&gt;\n\t\t\t\t&lt;liteappId /&gt;\n\t\t\t\t&lt;liteappPath /&gt;\n\t\t\t\t&lt;liteappQuery /&gt;\n\t\t\t\t&lt;liteappMinVersion /&gt;\n\t\t\t&lt;/namecard&gt;\n\t\t&lt;/gamecenter&gt;\n\t\t&lt;patMsg&gt;\n\t\t\t&lt;chatUser /&gt;\n\t\t\t&lt;records&gt;\n\t\t\t\t&lt;recordNum&gt;0&lt;/recordNum&gt;\n\t\t\t&lt;/records&gt;\n\t\t&lt;/patMsg&gt;\n\t\t&lt;secretmsg&gt;\n\t\t\t&lt;issecretmsg&gt;0&lt;/issecretmsg&gt;\n\t\t&lt;/secretmsg&gt;\n\t\t&lt;referfromscene&gt;0&lt;/referfromscene&gt;\n\t\t&lt;gameshare&gt;\n\t\t\t&lt;liteappext&gt;\n\t\t\t\t&lt;liteappbizdata /&gt;\n\t\t\t\t&lt;priority&gt;0&lt;/priority&gt;\n\t\t\t&lt;/liteappext&gt;\n\t\t\t&lt;appbrandext&gt;\n\t\t\t\t&lt;litegameinfo /&gt;\n\t\t\t\t&lt;priority&gt;-1&lt;/priority&gt;\n\t\t\t&lt;/appbrandext&gt;\n\t\t\t&lt;gameshareid /&gt;\n\t\t\t&lt;sharedata /&gt;\n\t\t\t&lt;isvideo&gt;0&lt;/isvideo&gt;\n\t\t\t&lt;duration&gt;-1&lt;/duration&gt;\n\t\t\t&lt;isexposed&gt;0&lt;/isexposed&gt;\n\t\t\t&lt;readtext /&gt;\n\t\t&lt;/gameshare&gt;\n\t\t&lt;tingChatRoomItem&gt;\n\t\t\t&lt;type&gt;0&lt;/type&gt;\n\t\t\t&lt;categoryItem&gt;null&lt;/categoryItem&gt;\n\t\t\t&lt;categoryId /&gt;\n\t\t&lt;/tingChatRoomItem&gt;\n\t\t&lt;mpsharetrace&gt;\n\t\t\t&lt;hasfinderelement&gt;0&lt;/hasfinderelement&gt;\n\t\t\t&lt;lastgmsgid /&gt;\n\t\t&lt;/mpsharetrace&gt;\n\t\t&lt;wxgamecard&gt;\n\t\t\t&lt;framesetname /&gt;\n\t\t\t&lt;mbcarddata /&gt;\n\t\t\t&lt;minpkgversion /&gt;\n\t\t\t&lt;clientextinfo /&gt;\n\t\t\t&lt;mbcardheight&gt;0&lt;/mbcardheight&gt;\n\t\t\t&lt;isoldversion&gt;0&lt;/isoldversion&gt;\n\t\t&lt;/wxgamecard&gt;\n\t\t&lt;ecskfcard&gt;\n\t\t\t&lt;framesetname /&gt;\n\t\t\t&lt;mbcarddata /&gt;\n\t\t\t&lt;minupdateunixtimestamp&gt;0&lt;/minupdateunixtimestamp&gt;\n\t\t\t&lt;needheader&gt;false&lt;/needheader&gt;\n\t\t\t&lt;summary /&gt;\n\t\t&lt;/ecskfcard&gt;\n\t\t&lt;liteapp&gt;\n\t\t\t&lt;id&gt;null&lt;/id&gt;\n\t\t\t&lt;path /&gt;\n\t\t\t&lt;query /&gt;\n\t\t\t&lt;istransparent&gt;0&lt;/istransparent&gt;\n\t\t\t&lt;hideicon&gt;0&lt;/hideicon&gt;\n\t\t\t&lt;forbidforward&gt;0&lt;/forbidforward&gt;\n\t\t&lt;/liteapp&gt;\n\t\t&lt;opensdk_share_is_modified&gt;0&lt;/opensdk_share_is_modified&gt;\n\t&lt;/appmsg&gt;\n\t&lt;fromusername&gt;wxid_laurnst5xn0q22&lt;/fromusername&gt;\n\t&lt;scene&gt;0&lt;/scene&gt;\n\t&lt;appinfo&gt;\n\t\t&lt;version&gt;1&lt;/version&gt;\n\t\t&lt;appname /&gt;\n\t&lt;/appinfo&gt;\n\t&lt;commenturl /&gt;\n&lt;/msg&gt;\n</content>\n\t\t\t<strid />\n\t\t\t<createtime>1753154267</createtime>\n\t\t</refermsg>\n\t\t<appattach>\n\t\t\t<totallen>0</totallen>\n\t\t\t<attachid />\n\t\t\t<cdnattachurl />\n\t\t\t<emoticonmd5></emoticonmd5>\n\t\t\t<aeskey></aeskey>\n\t\t\t<fileext />\n\t\t\t<islargefilemsg>0</islargefilemsg>\n\t\t</appattach>\n\t\t<extinfo />\n\t\t<androidsource>0</androidsource>\n\t\t<thumburl />\n\t\t<mediatagname />\n\t\t<messageaction><![CDATA[]]></messageaction>\n\t\t<messageext><![CDATA[]]></messageext>\n\t\t<emoticongift>\n\t\t\t<packageflag>0</packageflag>\n\t\t\t<packageid />\n\t\t</emoticongift>\n\t\t<emoticonshared>\n\t\t\t<packageflag>0</packageflag>\n\t\t\t<packageid />\n\t\t</emoticonshared>\n\t\t<designershared>\n\t\t\t<designeruin>0</designeruin>\n\t\t\t<designername>null</designername>\n\t\t\t<designerrediretcturl><![CDATA[null]]></designerrediretcturl>\n\t\t</designershared>\n\t\t<emotionpageshared>\n\t\t\t<tid>0</tid>\n\t\t\t<title>null</title>\n\t\t\t<desc>null</desc>\n\t\t\t<iconUrl><![CDATA[null]]></iconUrl>\n\t\t\t<secondUrl>null</secondUrl>\n\t\t\t<pageType>0</pageType>\n\t\t\t<setKey>null</setKey>\n\t\t</emotionpageshared>\n\t\t<webviewshared>\n\t\t\t<shareUrlOriginal />\n\t\t\t<shareUrlOpen />\n\t\t\t<jsAppId />\n\t\t\t<publisherId />\n\t\t\t<publisherReqId />\n\t\t</webviewshared>\n\t\t<template_id />\n\t\t<md5 />\n\t\t<websearch>\n\t\t\t<rec_category>0</rec_category>\n\t\t\t<channelId>0</channelId>\n\t\t</websearch>\n\t\t<weappinfo>\n\t\t\t<username />\n\t\t\t<appid />\n\t\t\t<appservicetype>0</appservicetype>\n\t\t\t<secflagforsinglepagemode>0</secflagforsinglepagemode>\n\t\t\t<videopageinfo>\n\t\t\t\t<thumbwidth>0</thumbwidth>\n\t\t\t\t<thumbheight>0</thumbheight>\n\t\t\t\t<fromopensdk>0</fromopensdk>\n\t\t\t</videopageinfo>\n\t\t</weappinfo>\n\t\t<statextstr />\n\t\t<musicShareItem>\n\t\t\t<musicDuration>0</musicDuration>\n\t\t</musicShareItem>\n\t\t<finderLiveProductShare>\n\t\t\t<finderLiveID><![CDATA[]]></finderLiveID>\n\t\t\t<finderUsername><![CDATA[]]></finderUsername>\n\t\t\t<finderObjectID><![CDATA[]]></finderObjectID>\n\t\t\t<finderNonceID><![CDATA[]]></finderNonceID>\n\t\t\t<liveStatus><![CDATA[]]></liveStatus>\n\t\t\t<appId><![CDATA[]]></appId>\n\t\t\t<pagePath><![CDATA[]]></pagePath>\n\t\t\t<productId><![CDATA[]]></productId>\n\t\t\t<coverUrl><![CDATA[]]></coverUrl>\n\t\t\t<productTitle><![CDATA[]]></productTitle>\n\t\t\t<marketPrice><![CDATA[0]]></marketPrice>\n\t\t\t<sellingPrice><![CDATA[0]]></sellingPrice>\n\t\t\t<platformHeadImg><![CDATA[]]></platformHeadImg>\n\t\t\t<platformName><![CDATA[]]></platformName>\n\t\t\t<shopWindowId><![CDATA[]]></shopWindowId>\n\t\t\t<flashSalePrice><![CDATA[0]]></flashSalePrice>\n\t\t\t<flashSaleEndTime><![CDATA[0]]></flashSaleEndTime>\n\t\t\t<ecSource><![CDATA[]]></ecSource>\n\t\t\t<sellingPriceWording><![CDATA[]]></sellingPriceWording>\n\t\t\t<platformIconURL><![CDATA[]]></platformIconURL>\n\t\t\t<firstProductTagURL><![CDATA[]]></firstProductTagURL>\n\t\t\t<firstProductTagAspectRatioString><![CDATA[0.0]]></firstProductTagAspectRatioString>\n\t\t\t<secondProductTagURL><![CDATA[]]></secondProductTagURL>\n\t\t\t<secondProductTagAspectRatioString><![CDATA[0.0]]></secondProductTagAspectRatioString>\n\t\t\t<firstGuaranteeWording><![CDATA[]]></firstGuaranteeWording>\n\t\t\t<secondGuaranteeWording><![CDATA[]]></secondGuaranteeWording>\n\t\t\t<thirdGuaranteeWording><![CDATA[]]></thirdGuaranteeWording>\n\t\t\t<isPriceBeginShow>false</isPriceBeginShow>\n\t\t\t<lastGMsgID><![CDATA[]]></lastGMsgID>\n\t\t\t<promoterKey><![CDATA[]]></promoterKey>\n\t\t\t<discountWording><![CDATA[]]></discountWording>\n\t\t\t<priceSuffixDescription><![CDATA[]]></priceSuffixDescription>\n\t\t\t<productCardKey><![CDATA[]]></productCardKey>\n\t\t\t<isWxShop><![CDATA[]]></isWxShop>\n\t\t\t<brandIconUrl><![CDATA[]]></brandIconUrl>\n\t\t\t<showBoxItemStringList />\n\t\t</finderLiveProductShare>\n\t\t<finderOrder>\n\t\t\t<appID><![CDATA[]]></appID>\n\t\t\t<orderID><![CDATA[]]></orderID>\n\t\t\t<path><![CDATA[]]></path>\n\t\t\t<priceWording><![CDATA[]]></priceWording>\n\t\t\t<stateWording><![CDATA[]]></stateWording>\n\t\t\t<productImageURL><![CDATA[]]></productImageURL>\n\t\t\t<products><![CDATA[]]></products>\n\t\t\t<productsCount><![CDATA[0]]></productsCount>\n\t\t\t<orderType><![CDATA[0]]></orderType>\n\t\t\t<newPriceWording><![CDATA[]]></newPriceWording>\n\t\t\t<newStateWording><![CDATA[]]></newStateWording>\n\t\t\t<useNewWording><![CDATA[0]]></useNewWording>\n\t\t</finderOrder>\n\t\t<finderShopWindowShare>\n\t\t\t<finderUsername><![CDATA[]]></finderUsername>\n\t\t\t<avatar><![CDATA[]]></avatar>\n\t\t\t<nickname><![CDATA[]]></nickname>\n\t\t\t<commodityInStockCount><![CDATA[]]></commodityInStockCount>\n\t\t\t<appId><![CDATA[]]></appId>\n\t\t\t<path><![CDATA[]]></path>\n\t\t\t<appUsername><![CDATA[]]></appUsername>\n\t\t\t<query><![CDATA[]]></query>\n\t\t\t<liteAppId><![CDATA[]]></liteAppId>\n\t\t\t<liteAppPath><![CDATA[]]></liteAppPath>\n\t\t\t<liteAppQuery><![CDATA[]]></liteAppQuery>\n\t\t\t<platformTagURL><![CDATA[]]></platformTagURL>\n\t\t\t<saleWording><![CDATA[]]></saleWording>\n\t\t\t<lastGMsgID><![CDATA[]]></lastGMsgID>\n\t\t\t<profileTypeWording><![CDATA[]]></profileTypeWording>\n\t\t\t<saleWordingExtra><![CDATA[]]></saleWordingExtra>\n\t\t\t<isWxShop><![CDATA[]]></isWxShop>\n\t\t\t<platformIconUrl><![CDATA[]]></platformIconUrl>\n\t\t\t<brandIconUrl><![CDATA[]]></brandIconUrl>\n\t\t\t<description><![CDATA[]]></description>\n\t\t\t<backgroundUrl><![CDATA[]]></backgroundUrl>\n\t\t\t<darkModePlatformIconUrl><![CDATA[]]></darkModePlatformIconUrl>\n\t\t\t<reputationInfo>\n\t\t\t\t<hasReputationInfo>0</hasReputationInfo>\n\t\t\t\t<reputationScore>0</reputationScore>\n\t\t\t\t<reputationWording />\n\t\t\t\t<reputationTextColor />\n\t\t\t\t<reputationLevelWording />\n\t\t\t\t<reputationBackgroundColor />\n\t\t\t</reputationInfo>\n\t\t\t<productImageURLList />\n\t\t</finderShopWindowShare>\n\t\t<findernamecard>\n\t\t\t<username />\n\t\t\t<avatar><![CDATA[]]></avatar>\n\t\t\t<nickname />\n\t\t\t<auth_job />\n\t\t\t<auth_icon>0</auth_icon>\n\t\t\t<auth_icon_url />\n\t\t\t<ecSource><![CDATA[]]></ecSource>\n\t\t\t<lastGMsgID><![CDATA[]]></lastGMsgID>\n\t\t</findernamecard>\n\t\t<finderGuarantee>\n\t\t\t<scene><![CDATA[0]]></scene>\n\t\t</finderGuarantee>\n\t\t<directshare>0</directshare>\n\t\t<gamecenter>\n\t\t\t<namecard>\n\t\t\t\t<iconUrl />\n\t\t\t\t<name />\n\t\t\t\t<desc />\n\t\t\t\t<tail />\n\t\t\t\t<jumpUrl />\n\t\t\t</namecard>\n\t\t</gamecenter>\n\t\t<patMsg>\n\t\t\t<chatUser />\n\t\t\t<records>\n\t\t\t\t<recordNum>0</recordNum>\n\t\t\t</records>\n\t\t</patMsg>\n\t\t<secretmsg>\n\t\t\t<issecretmsg>0</issecretmsg>\n\t\t</secretmsg>\n\t\t<referfromscene>0</referfromscene>\n\t\t<gameshare>\n\t\t\t<liteappext>\n\t\t\t\t<liteappbizdata />\n\t\t\t\t<priority>0</priority>\n\t\t\t</liteappext>\n\t\t\t<appbrandext>\n\t\t\t\t<litegameinfo />\n\t\t\t\t<priority>-1</priority>\n\t\t\t</appbrandext>\n\t\t\t<gameshareid />\n\t\t\t<sharedata />\n\t\t\t<isvideo>0</isvideo>\n\t\t\t<duration>-1</duration>\n\t\t\t<isexposed>0</isexposed>\n\t\t\t<readtext />\n\t\t</gameshare>\n\t\t<mpsharetrace>\n\t\t\t<hasfinderelement>0</hasfinderelement>\n\t\t\t<lastgmsgid />\n\t\t</mpsharetrace>\n\t\t<wxgamecard>\n\t\t\t<framesetname />\n\t\t\t<mbcarddata />\n\t\t\t<minpkgversion />\n\t\t\t<clientextinfo />\n\t\t\t<mbcardheight>0</mbcardheight>\n\t\t\t<isoldversion>0</isoldversion>\n\t\t</wxgamecard>\n\t\t<liteapp>\n\t\t\t<id>null</id>\n\t\t\t<path />\n\t\t\t<query />\n\t\t\t<istransparent>0</istransparent>\n\t\t\t<hideicon>0</hideicon>\n\t\t</liteapp>\n\t\t<opensdk_share_is_modified>0</opensdk_share_is_modified>\n\t</appmsg>\n\t<fromusername>wxid_e3o8s2nf9u2o22</fromusername>\n\t<scene>0</scene>\n\t<appinfo>\n\t\t<version>1</version>\n\t\t<appname />\n\t</appinfo>\n\t<commenturl />\n</msg>\n'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1753154459, 'MsgSource': '<msgsource>\n\t<alnode>\n\t\t<fr>4</fr>\n\t</alnode>\n\t<eggIncluded>1</eggIncluded>\n\t<sec_msg_node>\n\t\t<uuid>87942da53127f8800ab2adddd13ca42d_</uuid>\n\t\t<risk-file-flag />\n\t\t<risk-file-md5-list />\n\t</sec_msg_node>\n\t<silence>0</silence>\n\t<membercount>62</membercount>\n\t<signature>N0_V1_hKIpa/ja|v1_v18MW6Ae</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'PushContent': '星空- 梦 : 搞笑的', 'NewMsgId': 7734986608637317077, 'MsgSeq': 871390575}
2025-07-22 11:22:08 | DEBUG | 从群聊消息中提取发送者: wxid_e3o8s2nf9u2o22
2025-07-22 11:22:08 | DEBUG | 使用已解析的XML处理引用消息
2025-07-22 11:22:08 | INFO | 收到引用消息: 消息ID:443145628 来自:48097389945@chatroom 发送人:wxid_e3o8s2nf9u2o22 内容:搞笑的 引用类型:49
2025-07-22 11:22:09 | INFO | [DouBaoImageToImage] 收到引用消息: 搞笑的
2025-07-22 11:22:09 | INFO | [TimerTask] 收到引用消息调试信息:
2025-07-22 11:22:09 | INFO |   - 消息内容: 搞笑的
2025-07-22 11:22:09 | INFO |   - 群组ID: 48097389945@chatroom
2025-07-22 11:22:09 | INFO |   - 发送人: wxid_e3o8s2nf9u2o22
2025-07-22 11:22:09 | INFO |   - 引用信息: {'MsgType': 49, 'Content': '<?xml version="1.0"?>\n<msg>\n\t<appmsg appid="" sdkver="0">\n\t\t<title>高尔夫啥的我觉得就不错了</title>\n\t\t<des />\n\t\t<username />\n\t\t<action>view</action>\n\t\t<type>57</type>\n\t\t<showtype>0</showtype>\n\t\t<content />\n\t\t<url />\n\t\t<lowurl />\n\t\t<forwardflag>0</forwardflag>\n\t\t<dataurl />\n\t\t<lowdataurl />\n\t\t<contentattr>0</contentattr>\n\t\t<streamvideo>\n\t\t\t<streamvideourl />\n\t\t\t<streamvideototaltime>0</streamvideototaltime>\n\t\t\t<streamvideotitle />\n\t\t\t<streamvideowording />\n\t\t\t<streamvideoweburl />\n\t\t\t<streamvideothumburl />\n\t\t\t<streamvideoaduxinfo />\n\t\t\t<streamvideopublishid />\n\t\t</streamvideo>\n\t\t<canvasPageItem>\n\t\t\t<canvasPageXml><![CDATA[]]></canvasPageXml>\n\t\t</canvasPageItem>\n\t\t<refermsg></refermsg>\n\t\t<appattach>\n\t\t\t<totallen>0</totallen>\n\t\t\t<attachid />\n\t\t\t<cdnattachurl />\n\t\t\t<emoticonmd5></emoticonmd5>\n\t\t\t<aeskey></aeskey>\n\t\t\t<fileext />\n\t\t\t<islargefilemsg>0</islargefilemsg>\n\t\t</appattach>\n\t\t<extinfo />\n\t\t<androidsource>0</androidsource>\n\t\t<thumburl />\n\t\t<mediatagname />\n\t\t<messageaction><![CDATA[]]></messageaction>\n\t\t<messageext><![CDATA[]]></messageext>\n\t\t<emoticongift>\n\t\t\t<packageflag>0</packageflag>\n\t\t\t<packageid />\n\t\t</emoticongift>\n\t\t<emoticonshared>\n\t\t\t<packageflag>0</packageflag>\n\t\t\t<packageid />\n\t\t</emoticonshared>\n\t\t<designershared>\n\t\t\t<designeruin>0</designeruin>\n\t\t\t<designername>null</designername>\n\t\t\t<designerrediretcturl><![CDATA[null]]></designerrediretcturl>\n\t\t</designershared>\n\t\t<emotionpageshared>\n\t\t\t<tid>0</tid>\n\t\t\t<title>null</title>\n\t\t\t<desc>null</desc>\n\t\t\t<iconUrl><![CDATA[null]]></iconUrl>\n\t\t\t<secondUrl>null</secondUrl>\n\t\t\t<pageType>0</pageType>\n\t\t\t<setKey>null</setKey>\n\t\t</emotionpageshared>\n\t\t<webviewshared>\n\t\t\t<shareUrlOriginal />\n\t\t\t<shareUrlOpen />\n\t\t\t<jsAppId />\n\t\t\t<publisherId />\n\t\t\t<publisherReqId />\n\t\t</webviewshared>\n\t\t<template_id />\n\t\t<md5 />\n\t\t<websearch>\n\t\t\t<rec_category>0</rec_category>\n\t\t\t<channelId>0</channelId>\n\t\t</websearch>\n\t\t<weappinfo>\n\t\t\t<username />\n\t\t\t<appid />\n\t\t\t<appservicetype>0</appservicetype>\n\t\t\t<secflagforsinglepagemode>0</secflagforsinglepagemode>\n\t\t\t<videopageinfo>\n\t\t\t\t<thumbwidth>0</thumbwidth>\n\t\t\t\t<thumbheight>0</thumbheight>\n\t\t\t\t<fromopensdk>0</fromopensdk>\n\t\t\t</videopageinfo>\n\t\t</weappinfo>\n\t\t<statextstr />\n\t\t<musicShareItem>\n\t\t\t<musicDuration>0</musicDuration>\n\t\t</musicShareItem>\n\t\t<finderLiveProductShare>\n\t\t\t<finderLiveID><![CDATA[]]></finderLiveID>\n\t\t\t<finderUsername><![CDATA[]]></finderUsername>\n\t\t\t<finderObjectID><![CDATA[]]></finderObjectID>\n\t\t\t<finderNonceID><![CDATA[]]></finderNonceID>\n\t\t\t<liveStatus><![CDATA[]]></liveStatus>\n\t\t\t<appId><![CDATA[]]></appId>\n\t\t\t<pagePath><![CDATA[]]></pagePath>\n\t\t\t<productId><![CDATA[]]></productId>\n\t\t\t<coverUrl><![CDATA[]]></coverUrl>\n\t\t\t<productTitle><![CDATA[]]></productTitle>\n\t\t\t<marketPrice><![CDATA[0]]></marketPrice>\n\t\t\t<sellingPrice><![CDATA[0]]></sellingPrice>\n\t\t\t<platformHeadImg><![CDATA[]]></platformHeadImg>\n\t\t\t<platformName><![CDATA[]]></platformName>\n\t\t\t<shopWindowId><![CDATA[]]></shopWindowId>\n\t\t\t<flashSalePrice><![CDATA[0]]></flashSalePrice>\n\t\t\t<flashSaleEndTime><![CDATA[0]]></flashSaleEndTime>\n\t\t\t<ecSource><![CDATA[]]></ecSource>\n\t\t\t<sellingPriceWording><![CDATA[]]></sellingPriceWording>\n\t\t\t<platformIconURL><![CDATA[]]></platformIconURL>\n\t\t\t<firstProductTagURL><![CDATA[]]></firstProductTagURL>\n\t\t\t<firstProductTagAspectRatioString><![CDATA[0.0]]></firstProductTagAspectRatioString>\n\t\t\t<secondProductTagURL><![CDATA[]]></secondProductTagURL>\n\t\t\t<secondProductTagAspectRatioString><![CDATA[0.0]]></secondProductTagAspectRatioString>\n\t\t\t<firstGuaranteeWording><![CDATA[]]></firstGuaranteeWording>\n\t\t\t<secondGuaranteeWording><![CDATA[]]></secondGuaranteeWording>\n\t\t\t<thirdGuaranteeWording><![CDATA[]]></thirdGuaranteeWording>\n\t\t\t<isPriceBeginShow>false</isPriceBeginShow>\n\t\t\t<lastGMsgID><![CDATA[]]></lastGMsgID>\n\t\t\t<promoterKey><![CDATA[]]></promoterKey>\n\t\t\t<discountWording><![CDATA[]]></discountWording>\n\t\t\t<priceSuffixDescription><![CDATA[]]></priceSuffixDescription>\n\t\t\t<productCardKey><![CDATA[]]></productCardKey>\n\t\t\t<isWxShop><![CDATA[]]></isWxShop>\n\t\t\t<brandIconUrl><![CDATA[]]></brandIconUrl>\n\t\t\t<rIconUrl><![CDATA[]]></rIconUrl>\n\t\t\t<rIconUrlDarkMode><![CDATA[]]></rIconUrlDarkMode>\n\t\t\t<topShopIconUrl><![CDATA[]]></topShopIconUrl>\n\t\t\t<topShopIconUrlDarkMode><![CDATA[]]></topShopIconUrlDarkMode>\n\t\t\t<simplifyTopShopIconUrl><![CDATA[]]></simplifyTopShopIconUrl>\n\t\t\t<simplifyTopShopIconUrlDarkmode><![CDATA[]]></simplifyTopShopIconUrlDarkmode>\n\t\t\t<topShopIconWidth><![CDATA[0]]></topShopIconWidth>\n\t\t\t<topShopIconHeight><![CDATA[0]]></topShopIconHeight>\n\t\t\t<simplifyTopShopIconWidth><![CDATA[0]]></simplifyTopShopIconWidth>\n\t\t\t<simplifyTopShopIconHeight><![CDATA[0]]></simplifyTopShopIconHeight>\n\t\t\t<showBoxItemStringList />\n\t\t</finderLiveProductShare>\n\t\t<finderOrder>\n\t\t\t<appID><![CDATA[]]></appID>\n\t\t\t<orderID><![CDATA[]]></orderID>\n\t\t\t<path><![CDATA[]]></path>\n\t\t\t<priceWording><![CDATA[]]></priceWording>\n\t\t\t<stateWording><![CDATA[]]></stateWording>\n\t\t\t<productImageURL><![CDATA[]]></productImageURL>\n\t\t\t<products><![CDATA[]]></products>\n\t\t\t<productsCount><![CDATA[0]]></productsCount>\n\t\t\t<orderType><![CDATA[0]]></orderType>\n\t\t\t<newPriceWording><![CDATA[]]></newPriceWording>\n\t\t\t<newStateWording><![CDATA[]]></newStateWording>\n\t\t\t<useNewWording><![CDATA[0]]></useNewWording>\n\t\t</finderOrder>\n\t\t<finderShopWindowShare>\n\t\t\t<finderUsername><![CDATA[]]></finderUsername>\n\t\t\t<avatar><![CDATA[]]></avatar>\n\t\t\t<nickname><![CDATA[]]></nickname>\n\t\t\t<commodityInStockCount><![CDATA[]]></commodityInStockCount>\n\t\t\t<appId><![CDATA[]]></appId>\n\t\t\t<path><![CDATA[]]></path>\n\t\t\t<appUsername><![CDATA[]]></appUsername>\n\t\t\t<query><![CDATA[]]></query>\n\t\t\t<liteAppId><![CDATA[]]></liteAppId>\n\t\t\t<liteAppPath><![CDATA[]]></liteAppPath>\n\t\t\t<liteAppQuery><![CDATA[]]></liteAppQuery>\n\t\t\t<platformTagURL><![CDATA[]]></platformTagURL>\n\t\t\t<saleWording><![CDATA[]]></saleWording>\n\t\t\t<lastGMsgID><![CDATA[]]></lastGMsgID>\n\t\t\t<profileTypeWording><![CDATA[]]></profileTypeWording>\n\t\t\t<saleWordingExtra><![CDATA[]]></saleWordingExtra>\n\t\t\t<isWxShop><![CDATA[]]></isWxShop>\n\t\t\t<platformIconUrl><![CDATA[]]></platformIconUrl>\n\t\t\t<brandIconUrl><![CDATA[]]></brandIconUrl>\n\t\t\t<description><![CDATA[]]></description>\n\t\t\t<backgroundUrl><![CDATA[]]></backgroundUrl>\n\t\t\t<darkModePlatformIconUrl><![CDATA[]]></darkModePlatformIconUrl>\n\t\t\t<rIconUrl><![CDATA[]]></rIconUrl>\n\t\t\t<rIconUrlDarkMode><![CDATA[]]></rIconUrlDarkMode>\n\t\t\t<rWords><![CDATA[]]></rWords>\n\t\t\t<topShopIconUrl><![CDATA[]]></topShopIconUrl>\n\t\t\t<topShopIconUrlDarkMode><![CDATA[]]></topShopIconUrlDarkMode>\n\t\t\t<simplifyTopShopIconUrl><![CDATA[]]></simplifyTopShopIconUrl>\n\t\t\t<simplifyTopShopIconUrlDarkmode><![CDATA[]]></simplifyTopShopIconUrlDarkmode>\n\t\t\t<topShopIconWidth><![CDATA[0]]></topShopIconWidth>\n\t\t\t<topShopIconHeight><![CDATA[0]]></topShopIconHeight>\n\t\t\t<simplifyTopShopIconWidth><![CDATA[0]]></simplifyTopShopIconWidth>\n\t\t\t<simplifyTopShopIconHeight><![CDATA[0]]></simplifyTopShopIconHeight>\n\t\t\t<reputationInfo>\n\t\t\t\t<hasReputationInfo>0</hasReputationInfo>\n\t\t\t\t<reputationScore>0</reputationScore>\n\t\t\t\t<reputationWording />\n\t\t\t\t<reputationTextColor />\n\t\t\t\t<reputationLevelWording />\n\t\t\t\t<reputationBackgroundColor />\n\t\t\t</reputationInfo>\n\t\t\t<productImageURLList />\n\t\t</finderShopWindowShare>\n\t\t<findernamecard>\n\t\t\t<username />\n\t\t\t<avatar><![CDATA[]]></avatar>\n\t\t\t<nickname />\n\t\t\t<auth_job />\n\t\t\t<auth_icon>0</auth_icon>\n\t\t\t<auth_icon_url />\n\t\t\t<ecSource><![CDATA[]]></ecSource>\n\t\t\t<lastGMsgID><![CDATA[]]></lastGMsgID>\n\t\t</findernamecard>\n\t\t<finderGuarantee>\n\t\t\t<scene><![CDATA[0]]></scene>\n\t\t</finderGuarantee>\n\t\t<directshare>0</directshare>\n\t\t<gamecenter>\n\t\t\t<namecard>\n\t\t\t\t<iconUrl />\n\t\t\t\t<name />\n\t\t\t\t<desc />\n\t\t\t\t<tail />\n\t\t\t\t<jumpUrl />\n\t\t\t\t<liteappId />\n\t\t\t\t<liteappPath />\n\t\t\t\t<liteappQuery />\n\t\t\t\t<liteappMinVersion />\n\t\t\t</namecard>\n\t\t</gamecenter>\n\t\t<patMsg>\n\t\t\t<chatUser />\n\t\t\t<records>\n\t\t\t\t<recordNum>0</recordNum>\n\t\t\t</records>\n\t\t</patMsg>\n\t\t<secretmsg>\n\t\t\t<issecretmsg>0</issecretmsg>\n\t\t</secretmsg>\n\t\t<referfromscene>0</referfromscene>\n\t\t<gameshare>\n\t\t\t<liteappext>\n\t\t\t\t<liteappbizdata />\n\t\t\t\t<priority>0</priority>\n\t\t\t</liteappext>\n\t\t\t<appbrandext>\n\t\t\t\t<litegameinfo />\n\t\t\t\t<priority>-1</priority>\n\t\t\t</appbrandext>\n\t\t\t<gameshareid />\n\t\t\t<sharedata />\n\t\t\t<isvideo>0</isvideo>\n\t\t\t<duration>-1</duration>\n\t\t\t<isexposed>0</isexposed>\n\t\t\t<readtext />\n\t\t</gameshare>\n\t\t<tingChatRoomItem>\n\t\t\t<type>0</type>\n\t\t\t<categoryItem>null</categoryItem>\n\t\t\t<categoryId />\n\t\t</tingChatRoomItem>\n\t\t<mpsharetrace>\n\t\t\t<hasfinderelement>0</hasfinderelement>\n\t\t\t<lastgmsgid />\n\t\t</mpsharetrace>\n\t\t<wxgamecard>\n\t\t\t<framesetname />\n\t\t\t<mbcarddata />\n\t\t\t<minpkgversion />\n\t\t\t<clientextinfo />\n\t\t\t<mbcardheight>0</mbcardheight>\n\t\t\t<isoldversion>0</isoldversion>\n\t\t</wxgamecard>\n\t\t<ecskfcard>\n\t\t\t<framesetname />\n\t\t\t<mbcarddata />\n\t\t\t<minupdateunixtimestamp>0</minupdateunixtimestamp>\n\t\t\t<needheader>false</needheader>\n\t\t\t<summary />\n\t\t</ecskfcard>\n\t\t<liteapp>\n\t\t\t<id>null</id>\n\t\t\t<path />\n\t\t\t<query />\n\t\t\t<istransparent>0</istransparent>\n\t\t\t<hideicon>0</hideicon>\n\t\t\t<forbidforward>0</forbidforward>\n\t\t</liteapp>\n\t\t<opensdk_share_is_modified>0</opensdk_share_is_modified>\n\t</appmsg>\n\t<fromusername>wxid_laurnst5xn0q22</fromusername>\n\t<scene>0</scene>\n\t<appinfo>\n\t\t<version>1</version>\n\t\t<appname />\n\t</appinfo>\n\t<commenturl />\n</msg>\n', 'Msgid': '981361057820205046', 'NewMsgId': '981361057820205046', 'ToWxid': 'wxid_4usgcju5ey9q29', 'FromWxid': '48097389945@chatroom', 'Nickname': 'متسول暗', 'MsgSource': '<msgsource>\n\t<alnode>\n\t\t<fr>4</fr>\n\t</alnode>\n\t<eggIncluded>1</eggIncluded>\n\t<sec_msg_node>\n\t\t<uuid>87942da53127f8800ab2adddd13ca42d_</uuid>\n\t\t<risk-file-flag />\n\t\t<risk-file-md5-list />\n\t</sec_msg_node>\n\t<silence>1</silence>\n\t<membercount>62</membercount>\n\t<signature>N0_V1_eKv1ztUk|v1_vJiMZ8Fs</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'Createtime': '1753154267', 'SenderWxid': 'wxid_e3o8s2nf9u2o22'}
2025-07-22 11:22:09 | INFO |   - 引用消息ID: 
2025-07-22 11:22:09 | INFO |   - 引用消息类型: 
2025-07-22 11:22:09 | INFO |   - 引用消息内容: <?xml version="1.0"?>
<msg>
	<appmsg appid="" sdkver="0">
		<title>高尔夫啥的我觉得就不错了</title>
		<des />
		<username />
		<action>view</action>
		<type>57</type>
		<showtype>0</showtype>
		<content />
		<url />
		<lowurl />
		<forwardflag>0</forwardflag>
		<dataurl />
		<lowdataurl />
		<contentattr>0</contentattr>
		<streamvideo>
			<streamvideourl />
			<streamvideototaltime>0</streamvideototaltime>
			<streamvideotitle />
			<streamvideowording />
			<streamvideoweburl />
			<streamvideothumburl />
			<streamvideoaduxinfo />
			<streamvideopublishid />
		</streamvideo>
		<canvasPageItem>
			<canvasPageXml><![CDATA[]]></canvasPageXml>
		</canvasPageItem>
		<refermsg></refermsg>
		<appattach>
			<totallen>0</totallen>
			<attachid />
			<cdnattachurl />
			<emoticonmd5></emoticonmd5>
			<aeskey></aeskey>
			<fileext />
			<islargefilemsg>0</islargefilemsg>
		</appattach>
		<extinfo />
		<androidsource>0</androidsource>
		<thumburl />
		<mediatagname />
		<messageaction><![CDATA[]]></messageaction>
		<messageext><![CDATA[]]></messageext>
		<emoticongift>
			<packageflag>0</packageflag>
			<packageid />
		</emoticongift>
		<emoticonshared>
			<packageflag>0</packageflag>
			<packageid />
		</emoticonshared>
		<designershared>
			<designeruin>0</designeruin>
			<designername>null</designername>
			<designerrediretcturl><![CDATA[null]]></designerrediretcturl>
		</designershared>
		<emotionpageshared>
			<tid>0</tid>
			<title>null</title>
			<desc>null</desc>
			<iconUrl><![CDATA[null]]></iconUrl>
			<secondUrl>null</secondUrl>
			<pageType>0</pageType>
			<setKey>null</setKey>
		</emotionpageshared>
		<webviewshared>
			<shareUrlOriginal />
			<shareUrlOpen />
			<jsAppId />
			<publisherId />
			<publisherReqId />
		</webviewshared>
		<template_id />
		<md5 />
		<websearch>
			<rec_category>0</rec_category>
			<channelId>0</channelId>
		</websearch>
		<weappinfo>
			<username />
			<appid />
			<appservicetype>0</appservicetype>
			<secflagforsinglepagemode>0</secflagforsinglepagemode>
			<videopageinfo>
				<thumbwidth>0</thumbwidth>
				<thumbheight>0</thumbheight>
				<fromopensdk>0</fromopensdk>
			</videopageinfo>
		</weappinfo>
		<statextstr />
		<musicShareItem>
			<musicDuration>0</musicDuration>
		</musicShareItem>
		<finderLiveProductShare>
			<finderLiveID><![CDATA[]]></finderLiveID>
			<finderUsername><![CDATA[]]></finderUsername>
			<finderObjectID><![CDATA[]]></finderObjectID>
			<finderNonceID><![CDATA[]]></finderNonceID>
			<liveStatus><![CDATA[]]></liveStatus>
			<appId><![CDATA[]]></appId>
			<pagePath><![CDATA[]]></pagePath>
			<productId><![CDATA[]]></productId>
			<coverUrl><![CDATA[]]></coverUrl>
			<productTitle><![CDATA[]]></productTitle>
			<marketPrice><![CDATA[0]]></marketPrice>
			<sellingPrice><![CDATA[0]]></sellingPrice>
			<platformHeadImg><![CDATA[]]></platformHeadImg>
			<platformName><![CDATA[]]></platformName>
			<shopWindowId><![CDATA[]]></shopWindowId>
			<flashSalePrice><![CDATA[0]]></flashSalePrice>
			<flashSaleEndTime><![CDATA[0]]></flashSaleEndTime>
			<ecSource><![CDATA[]]></ecSource>
			<sellingPriceWording><![CDATA[]]></sellingPriceWording>
			<platformIconURL><![CDATA[]]></platformIconURL>
			<firstProductTagURL><![CDATA[]]></firstProductTagURL>
			<firstProductTagAspectRatioString><![CDATA[0.0]]></firstProductTagAspectRatioString>
			<secondProductTagURL><![CDATA[]]></secondProductTagURL>
			<secondProductTagAspectRatioString><![CDATA[0.0]]></secondProductTagAspectRatioString>
			<firstGuaranteeWording><![CDATA[]]></firstGuaranteeWording>
			<secondGuaranteeWording><![CDATA[]]></secondGuaranteeWording>
			<thirdGuaranteeWording><![CDATA[]]></thirdGuaranteeWording>
			<isPriceBeginShow>false</isPriceBeginShow>
			<lastGMsgID><![CDATA[]]></lastGMsgID>
			<promoterKey><![CDATA[]]></promoterKey>
			<discountWording><![CDATA[]]></discountWording>
			<priceSuffixDescription><![CDATA[]]></priceSuffixDescription>
			<productCardKey><![CDATA[]]></productCardKey>
			<isWxShop><![CDATA[]]></isWxShop>
			<brandIconUrl><![CDATA[]]></brandIconUrl>
			<rIconUrl><![CDATA[]]></rIconUrl>
			<rIconUrlDarkMode><![CDATA[]]></rIconUrlDarkMode>
			<topShopIconUrl><![CDATA[]]></topShopIconUrl>
			<topShopIconUrlDarkMode><![CDATA[]]></topShopIconUrlDarkMode>
			<simplifyTopShopIconUrl><![CDATA[]]></simplifyTopShopIconUrl>
			<simplifyTopShopIconUrlDarkmode><![CDATA[]]></simplifyTopShopIconUrlDarkmode>
			<topShopIconWidth><![CDATA[0]]></topShopIconWidth>
			<topShopIconHeight><![CDATA[0]]></topShopIconHeight>
			<simplifyTopShopIconWidth><![CDATA[0]]></simplifyTopShopIconWidth>
			<simplifyTopShopIconHeight><![CDATA[0]]></simplifyTopShopIconHeight>
			<showBoxItemStringList />
		</finderLiveProductShare>
		<finderOrder>
			<appID><![CDATA[]]></appID>
			<orderID><![CDATA[]]></orderID>
			<path><![CDATA[]]></path>
			<priceWording><![CDATA[]]></priceWording>
			<stateWording><![CDATA[]]></stateWording>
			<productImageURL><![CDATA[]]></productImageURL>
			<products><![CDATA[]]></products>
			<productsCount><![CDATA[0]]></productsCount>
			<orderType><![CDATA[0]]></orderType>
			<newPriceWording><![CDATA[]]></newPriceWording>
			<newStateWording><![CDATA[]]></newStateWording>
			<useNewWording><![CDATA[0]]></useNewWording>
		</finderOrder>
		<finderShopWindowShare>
			<finderUsername><![CDATA[]]></finderUsername>
			<avatar><![CDATA[]]></avatar>
			<nickname><![CDATA[]]></nickname>
			<commodityInStockCount><![CDATA[]]></commodityInStockCount>
			<appId><![CDATA[]]></appId>
			<path><![CDATA[]]></path>
			<appUsername><![CDATA[]]></appUsername>
			<query><![CDATA[]]></query>
			<liteAppId><![CDATA[]]></liteAppId>
			<liteAppPath><![CDATA[]]></liteAppPath>
			<liteAppQuery><![CDATA[]]></liteAppQuery>
			<platformTagURL><![CDATA[]]></platformTagURL>
			<saleWording><![CDATA[]]></saleWording>
			<lastGMsgID><![CDATA[]]></lastGMsgID>
			<profileTypeWording><![CDATA[]]></profileTypeWording>
			<saleWordingExtra><![CDATA[]]></saleWordingExtra>
			<isWxShop><![CDATA[]]></isWxShop>
			<platformIconUrl><![CDATA[]]></platformIconUrl>
			<brandIconUrl><![CDATA[]]></brandIconUrl>
			<description><![CDATA[]]></description>
			<backgroundUrl><![CDATA[]]></backgroundUrl>
			<darkModePlatformIconUrl><![CDATA[]]></darkModePlatformIconUrl>
			<rIconUrl><![CDATA[]]></rIconUrl>
			<rIconUrlDarkMode><![CDATA[]]></rIconUrlDarkMode>
			<rWords><![CDATA[]]></rWords>
			<topShopIconUrl><![CDATA[]]></topShopIconUrl>
			<topShopIconUrlDarkMode><![CDATA[]]></topShopIconUrlDarkMode>
			<simplifyTopShopIconUrl><![CDATA[]]></simplifyTopShopIconUrl>
			<simplifyTopShopIconUrlDarkmode><![CDATA[]]></simplifyTopShopIconUrlDarkmode>
			<topShopIconWidth><![CDATA[0]]></topShopIconWidth>
			<topShopIconHeight><![CDATA[0]]></topShopIconHeight>
			<simplifyTopShopIconWidth><![CDATA[0]]></simplifyTopShopIconWidth>
			<simplifyTopShopIconHeight><![CDATA[0]]></simplifyTopShopIconHeight>
			<reputationInfo>
				<hasReputationInfo>0</hasReputationInfo>
				<reputationScore>0</reputationScore>
				<reputationWording />
				<reputationTextColor />
				<reputationLevelWording />
				<reputationBackgroundColor />
			</reputationInfo>
			<productImageURLList />
		</finderShopWindowShare>
		<findernamecard>
			<username />
			<avatar><![CDATA[]]></avatar>
			<nickname />
			<auth_job />
			<auth_icon>0</auth_icon>
			<auth_icon_url />
			<ecSource><![CDATA[]]></ecSource>
			<lastGMsgID><![CDATA[]]></lastGMsgID>
		</findernamecard>
		<finderGuarantee>
			<scene><![CDATA[0]]></scene>
		</finderGuarantee>
		<directshare>0</directshare>
		<gamecenter>
			<namecard>
				<iconUrl />
				<name />
				<desc />
				<tail />
				<jumpUrl />
				<liteappId />
				<liteappPath />
				<liteappQuery />
				<liteappMinVersion />
			</namecard>
		</gamecenter>
		<patMsg>
			<chatUser />
			<records>
				<recordNum>0</recordNum>
			</records>
		</patMsg>
		<secretmsg>
			<issecretmsg>0</issecretmsg>
		</secretmsg>
		<referfromscene>0</referfromscene>
		<gameshare>
			<liteappext>
				<liteappbizdata />
				<priority>0</priority>
			</liteappext>
			<appbrandext>
				<litegameinfo />
				<priority>-1</priority>
			</appbrandext>
			<gameshareid />
			<sharedata />
			<isvideo>0</isvideo>
			<duration>-1</duration>
			<isexposed>0</isexposed>
			<readtext />
		</gameshare>
		<tingChatRoomItem>
			<type>0</type>
			<categoryItem>null</categoryItem>
			<categoryId />
		</tingChatRoomItem>
		<mpsharetrace>
			<hasfinderelement>0</hasfinderelement>
			<lastgmsgid />
		</mpsharetrace>
		<wxgamecard>
			<framesetname />
			<mbcarddata />
			<minpkgversion />
			<clientextinfo />
			<mbcardheight>0</mbcardheight>
			<isoldversion>0</isoldversion>
		</wxgamecard>
		<ecskfcard>
			<framesetname />
			<mbcarddata />
			<minupdateunixtimestamp>0</minupdateunixtimestamp>
			<needheader>false</needheader>
			<summary />
		</ecskfcard>
		<liteapp>
			<id>null</id>
			<path />
			<query />
			<istransparent>0</istransparent>
			<hideicon>0</hideicon>
			<forbidforward>0</forbidforward>
		</liteapp>
		<opensdk_share_is_modified>0</opensdk_share_is_modified>
	</appmsg>
	<fromusername>wxid_laurnst5xn0q22</fromusername>
	<scene>0</scene>
	<appinfo>
		<version>1</version>
		<appname />
	</appinfo>
	<commenturl />
</msg>

2025-07-22 11:22:09 | INFO |   - 引用消息发送人: wxid_e3o8s2nf9u2o22
2025-07-22 11:22:09 | DEBUG | 收到消息: {'MsgId': 629268468, 'FromUserName': {'string': '48097389945@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 47, 'Content': {'string': 'wxid_e3o8s2nf9u2o22:\n<msg><emoji fromusername="wxid_e3o8s2nf9u2o22" tousername="48097389945@chatroom" type="1" idbuffer="media:0_0" md5="0b32679281a8663df4a3376b3599e2ea" len="5927717" productid="" androidmd5="0b32679281a8663df4a3376b3599e2ea" androidlen="5927717" s60v3md5="0b32679281a8663df4a3376b3599e2ea" s60v3len="5927717" s60v5md5="0b32679281a8663df4a3376b3599e2ea" s60v5len="5927717" cdnurl="http://vweixinf.tc.qq.com/110/20402/stodownload?m=0b32679281a8663df4a3376b3599e2ea&amp;filekey=30440201010430302e02016e040253480420306233323637393238316138363633646634613333373662333539396532656102035a7325040d00000004627466730000000132&amp;hy=SH&amp;storeid=26862d5c60004a83a2ec3af040000006e01004fb253481853f031578431097&amp;ef=1&amp;bizid=1022" designerid="" thumburl="" encrypturl="http://vweixinf.tc.qq.com/110/20402/stodownload?m=83e738466d54a487909a92bebb908f5b&amp;filekey=30440201010430302e02016e040253480420383365373338343636643534613438373930396139326265626239303866356202035a7330040d00000004627466730000000132&amp;hy=SH&amp;storeid=26862d5c6000a89042ec3af040000006e02004fb253481853f0315784310c2&amp;ef=2&amp;bizid=1022" aeskey="169ca050981548f791823dafc9b5fbb0" externurl="http://vweixinf.tc.qq.com/110/20403/stodownload?m=53cfec97435f57ac5891e94463e44c0d&amp;filekey=30440201010430302e02016e04025348042035336366656339373433356635376163353839316539343436336534346330640203039820040d00000004627466730000000132&amp;hy=SH&amp;storeid=26862d5c700014c522ec3af040000006e03004fb353481853f0315784310ef&amp;ef=3&amp;bizid=1022" externmd5="34292d46849021c0262b0c8133092201" width="300" height="300" tpurl="" tpauthkey="" attachedtext="" attachedtextcolor="" lensid="" emojiattr="" linkid="" desc=""></emoji></msg>'}, 'Status': 3, 'ImgStatus': 2, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1753154465, 'MsgSource': '<msgsource>\n\t<silence>0</silence>\n\t<membercount>62</membercount>\n\t<signature>N0_V1_wfnpbzoZ|v1_+5hgZszH</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'PushContent': '星空- 梦在群聊中发了一个表情', 'NewMsgId': 4539079432709686347, 'MsgSeq': 871390576}
2025-07-22 11:22:09 | INFO | 收到表情消息: 消息ID:629268468 来自:48097389945@chatroom 发送人:wxid_e3o8s2nf9u2o22 MD5:0b32679281a8663df4a3376b3599e2ea 大小:5927717
2025-07-22 11:22:09 | DEBUG | [DoubaoImageRecognition] 缓存表情消息: 4539079432709686347
2025-07-22 11:22:10 | DEBUG | 收到消息: {'MsgId': 727833580, 'FromUserName': {'string': '48097389945@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 10002, 'Content': {'string': '48097389945@chatroom:\n<sysmsg type="pat">\n<pat>\n  <fromusername>wxid_e3o8s2nf9u2o22</fromusername>\n  <chatusername>48097389945@chatroom</chatusername>\n  <pattedusername>wxid_4usgcju5ey9q29</pattedusername>\n  <patsuffix><![CDATA[]]></patsuffix>\n  <patsuffixversion>2</patsuffixversion>\n\n\n\n\n  <template><![CDATA["${wxid_e3o8s2nf9u2o22}" 拍了拍我]]></template>\n\n\n\n\n</pat>\n</sysmsg>'}, 'Status': 4, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1753154465, 'MsgSource': '<msgsource>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'NewMsgId': 4561581540017721254, 'MsgSeq': 871390577}
2025-07-22 11:22:10 | DEBUG | 系统消息类型: pat
2025-07-22 11:22:10 | INFO | 收到拍一拍消息: 消息ID:727833580 来自:48097389945@chatroom 发送人:48097389945@chatroom 拍者:wxid_e3o8s2nf9u2o22 被拍:wxid_4usgcju5ey9q29 后缀:None
2025-07-22 11:22:10 | DEBUG | [PatReply] 使用base64缓存发送语音
2025-07-22 11:22:11 | INFO | 发送语音消息: 对方wxid:48097389945@chatroom 时长:8540 格式:amr 音频base64略
2025-07-22 11:22:11 | INFO | [PatReply] 已向 48097389945@chatroom 发送拍一拍语音回复
2025-07-22 11:22:11 | DEBUG | 收到消息: {'MsgId': 1008913168, 'FromUserName': {'string': '55878994168@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 1, 'Content': {'string': 'wxid_ubbh6q832tcs21:\n换个背景，在重庆观音桥'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1753154509, 'MsgSource': '<msgsource>\n\t<pua>1</pua>\n\t<eggIncluded>1</eggIncluded>\n\t<silence>0</silence>\n\t<membercount>3</membercount>\n\t<signature>N0_V1_EllKYGTE|v1_JyGZn3xF</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'PushContent': '郭 : 换个背景，在重庆观音桥', 'NewMsgId': 3353747371572776069, 'MsgSeq': 871390582}
2025-07-22 11:22:11 | INFO | 收到文本消息: 消息ID:1008913168 来自:55878994168@chatroom 发送人:wxid_ubbh6q832tcs21 @:[] 内容:换个背景，在重庆观音桥
2025-07-22 11:22:51 | INFO | 发送文字消息: 对方wxid:55878994168@chatroom at: 内容:正在为这位英姿飒爽的女战士更换背景到重庆观音桥的繁华街景，让我好好构思下怎融合最自然~✨正在将背景更换为重庆观音桥的繁华商业街景，会保持女战士的所有细节特征不变，并调整光影使其自然融入新环境。

2025-07-22 11:22:56 | INFO | 发送图片消息: 对方wxid:55878994168@chatroom 图片base64略
2025-07-22 11:22:56 | DEBUG | [RevokePlugin] 无法获取图片数据进行缓存，image类型: <class 'pathlib.WindowsPath'>
2025-07-22 11:22:58 | INFO | 发送文字消息: 对方wxid:55878994168@chatroom at: 内容:已完成背景更换，现在这位英姿飒爽的女战士置身于重庆观音桥的繁华商业街景中。我保持了人物原有的所有细节特征：1) 精致的女性五官和发型；2) 完整的铠甲装备和武器；3) 原有的战斗姿态。同时调整了人物身上的光影效果，使其与观音桥商业区的霓虹灯光环境自然融合。整体效果既突出了人物主体，又展现了重庆都市的现代感。如果需要进一步调整任何细节，可以随时告诉我。
2025-07-22 11:22:58 | DEBUG | 处理消息内容: '换个背景，在重庆观音桥'
2025-07-22 11:22:58 | DEBUG | 消息内容 '换个背景，在重庆观音桥' 不匹配任何命令，忽略
2025-07-22 11:22:58 | DEBUG | 收到消息: {'MsgId': 951679953, 'FromUserName': {'string': '48097389945@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 1, 'Content': {'string': 'wxid_wlnzvr8ivgd422:\n洞房'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1753154575, 'MsgSource': '<msgsource>\n\t<pua>1</pua>\n\t<eggIncluded>1</eggIncluded>\n\t<silence>0</silence>\n\t<membercount>62</membercount>\n\t<signature>N0_V1_/lyiNSV2|v1_YYqvpz74</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'PushContent': '锦岚（开玩笑就退群版） : 洞房', 'NewMsgId': 1867093613589939338, 'MsgSeq': 871390587}
2025-07-22 11:22:58 | INFO | 收到文本消息: 消息ID:951679953 来自:48097389945@chatroom 发送人:wxid_wlnzvr8ivgd422 @:[] 内容:洞房
2025-07-22 11:22:59 | DEBUG | 处理消息内容: '洞房'
2025-07-22 11:22:59 | DEBUG | 消息内容 '洞房' 不匹配任何命令，忽略
2025-07-22 11:23:01 | DEBUG | 收到消息: {'MsgId': 15977148, 'FromUserName': {'string': '48097389945@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 1, 'Content': {'string': 'wxid_q35rkzgkjvlv12:\n💕 [锦岚（开玩笑就退群版）]👩\u200d❤\u200d👨[小爱]💕锦岚（开玩笑就退群版）你在成人用品店给你最爱的小爱[666]买了电动玩具！\n 💓═☘︎═•洞💗房•═☘︎═💓\n👩\u200d❤\u200d👨关系：玩具夫付\n⛺地点：阳台\n😍活动：无数次\n😘结果：成功\n💓═☘︎══•💗•══☘︎═💓\n🔮魅力: +9\n[玫瑰]恩爱: +7 \n🕒下次:2025-07-22 11:42:57'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1753154576, 'MsgSource': '<msgsource>\n\t<silence>0</silence>\n\t<membercount>62</membercount>\n\t<signature>N0_V1_iW2xz1C/|v1_rkkcKv2j</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'PushContent': '摇月 : \ue327 [锦岚（开玩笑就退群版）]\ue005\u200d\ue022\u200d\ue004[小爱]\ue327锦岚（开玩笑就...', 'NewMsgId': 2663342884496092829, 'MsgSeq': 871390588}
2025-07-22 11:23:01 | INFO | 收到文本消息: 消息ID:15977148 来自:48097389945@chatroom 发送人:wxid_q35rkzgkjvlv12 @:[] 内容:💕 [锦岚（开玩笑就退群版）]👩‍❤‍👨[小爱]💕锦岚（开玩笑就退群版）你在成人用品店给你最爱的小爱[666]买了电动玩具！
 💓═☘︎═•洞💗房•═☘︎═💓
👩‍❤‍👨关系：玩具夫付
⛺地点：阳台
😍活动：无数次
😘结果：成功
💓═☘︎══•💗•══☘︎═💓
🔮魅力: +9
[玫瑰]恩爱: +7 
🕒下次:2025-07-22 11:42:57
2025-07-22 11:23:01 | DEBUG | 处理消息内容: '💕 [锦岚（开玩笑就退群版）]👩‍❤‍👨[小爱]💕锦岚（开玩笑就退群版）你在成人用品店给你最爱的小爱[666]买了电动玩具！
 💓═☘︎═•洞💗房•═☘︎═💓
👩‍❤‍👨关系：玩具夫付
⛺地点：阳台
😍活动：无数次
😘结果：成功
💓═☘︎══•💗•══☘︎═💓
🔮魅力: +9
[玫瑰]恩爱: +7 
🕒下次:2025-07-22 11:42:57'
2025-07-22 11:23:01 | DEBUG | 消息内容 '💕 [锦岚（开玩笑就退群版）]👩‍❤‍👨[小爱]💕锦岚（开玩笑就退群版）你在成人用品店给你最爱的小爱[666]买了电动玩具！
 💓═☘︎═•洞💗房•═☘︎═💓
👩‍❤‍👨关系：玩具夫付
⛺地点：阳台
😍活动：无数次
😘结果：成功
💓═☘︎══•💗•══☘︎═💓
🔮魅力: +9
[玫瑰]恩爱: +7 
🕒下次:2025-07-22 11:42:57' 不匹配任何命令，忽略
2025-07-22 11:23:03 | DEBUG | 收到消息: {'MsgId': 77025158, 'FromUserName': {'string': '48097389945@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 1, 'Content': {'string': 'wxid_8l9ymg1mafud12:\n「锦岚（开玩笑就退群版）」[爱心]「小爱」\n地点：小汽车\n活动：双修\n结果：失败\n羞羞：声音太大引起交警注意\n恩爱值减少500\n\n下次:2025-07-22 11:32:56'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1753154576, 'MsgSource': '<msgsource>\n\t<silence>0</silence>\n\t<membercount>62</membercount>\n\t<signature>N0_V1_ceZg0ztr|v1_w1YecVHm</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'PushContent': '不吃香菜\ue110 : 「锦岚（开玩笑就退群版）」[爱心]「小爱」\n地点：小汽车\n活动：双...', 'NewMsgId': 6655368173566341395, 'MsgSeq': 871390589}
2025-07-22 11:23:03 | INFO | 收到文本消息: 消息ID:77025158 来自:48097389945@chatroom 发送人:wxid_8l9ymg1mafud12 @:[] 内容:「锦岚（开玩笑就退群版）」[爱心]「小爱」
地点：小汽车
活动：双修
结果：失败
羞羞：声音太大引起交警注意
恩爱值减少500

下次:2025-07-22 11:32:56
2025-07-22 11:23:03 | DEBUG | 处理消息内容: '「锦岚（开玩笑就退群版）」[爱心]「小爱」
地点：小汽车
活动：双修
结果：失败
羞羞：声音太大引起交警注意
恩爱值减少500

下次:2025-07-22 11:32:56'
2025-07-22 11:23:03 | DEBUG | 消息内容 '「锦岚（开玩笑就退群版）」[爱心]「小爱」
地点：小汽车
活动：双修
结果：失败
羞羞：声音太大引起交警注意
恩爱值减少500

下次:2025-07-22 11:32:56' 不匹配任何命令，忽略
2025-07-22 11:23:05 | DEBUG | 收到消息: {'MsgId': 1843604922, 'FromUserName': {'string': '48097389945@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 1, 'Content': {'string': 'wxid_wlnzvr8ivgd422:\n洞房昨夜停红烛，待晓堂前拜舅姑。妆罢低声问夫婿，画眉深浅入时无。'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1753154585, 'MsgSource': '<msgsource>\n\t<pua>1</pua>\n\t<eggIncluded>1</eggIncluded>\n\t<silence>0</silence>\n\t<membercount>62</membercount>\n\t<signature>N0_V1_/wQ+UQ0d|v1_LfPMioaY</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'PushContent': '锦岚（开玩笑就退群版） : 洞房昨夜停红烛，待晓堂前拜舅姑。妆罢低声问夫婿，画眉深浅入时...', 'NewMsgId': 4206865223588117348, 'MsgSeq': 871390593}
2025-07-22 11:23:05 | INFO | 收到文本消息: 消息ID:1843604922 来自:48097389945@chatroom 发送人:wxid_wlnzvr8ivgd422 @:[] 内容:洞房昨夜停红烛，待晓堂前拜舅姑。妆罢低声问夫婿，画眉深浅入时无。
2025-07-22 11:23:05 | DEBUG | 处理消息内容: '洞房昨夜停红烛，待晓堂前拜舅姑。妆罢低声问夫婿，画眉深浅入时无。'
2025-07-22 11:23:05 | DEBUG | 消息内容 '洞房昨夜停红烛，待晓堂前拜舅姑。妆罢低声问夫婿，画眉深浅入时无。' 不匹配任何命令，忽略
2025-07-22 11:23:26 | DEBUG | 收到消息: {'MsgId': 805035152, 'FromUserName': {'string': '55878994168@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 1, 'Content': {'string': 'wxid_ubbh6q832tcs21:\n衣服换成汉服系列'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1753154618, 'MsgSource': '<msgsource>\n\t<pua>1</pua>\n\t<alnode>\n\t\t<cf>5</cf>\n\t\t<inlenlist>11</inlenlist>\n\t</alnode>\n\t<eggIncluded>1</eggIncluded>\n\t<silence>0</silence>\n\t<membercount>3</membercount>\n\t<signature>N0_V1_35hqIHi+|v1_+gzaX0wd</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'PushContent': '郭 : 衣服换成汉服系列', 'NewMsgId': 8100125907423620274, 'MsgSeq': 871390597}
2025-07-22 11:23:26 | INFO | 收到文本消息: 消息ID:805035152 来自:55878994168@chatroom 发送人:wxid_ubbh6q832tcs21 @:[] 内容:衣服换成汉服系列
2025-07-22 11:24:07 | INFO | 发送文字消息: 对方wxid:55878994168@chatroom at: 内容:马上为你把位女战士的铠甲衣服换成汉服系列，让我好好构思下怎么换最有古风韵味~正在将女战士的铠甲装备更换为精美的汉服系列，会保持人物原有的面部特征和战斗姿态不变，同时确保新服装与重庆观音桥的背景光影自然融合。

2025-07-22 11:24:12 | INFO | 发送图片消息: 对方wxid:55878994168@chatroom 图片base64略
2025-07-22 11:24:12 | DEBUG | [RevokePlugin] 无法获取图片数据进行缓存，image类型: <class 'pathlib.WindowsPath'>
2025-07-22 11:25:06 | INFO | 发送文字消息: 对方wxid:55878994168@chatroom at: 内容:正在对汉服女战士的形象进行精致美化，重点优化服装纹理和面部细节，保持战斗场景的真实感。
正在调整整体气质为"千金"风格，突出汉服的华贵感与女战士的英气结合。
已完成女战士的汉服造型改造：1) 将原有铠甲替换为精致的汉服系列，保留了传统汉服的形制特点；2) 保持了人物原有的面部特征、发型和战斗姿态；3) 优化了服装纹理和光影效果，使其与重庆观音桥的背景自然融合；4) 调整整体气质为"千金"风格，突出了华贵感与英气的完美结合。现在这位汉服女战士既保留了战斗场景的力量感，又展现了传统服饰的优雅魅力。如需进一步调整服装细节或风格，可以随时告诉我。
2025-07-22 11:25:06 | DEBUG | 处理消息内容: '衣服换成汉服系列'
2025-07-22 11:25:06 | DEBUG | 消息内容 '衣服换成汉服系列' 不匹配任何命令，忽略
2025-07-22 11:25:06 | DEBUG | 收到消息: {'MsgId': 1593816919, 'FromUserName': {'string': '48097389945@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 1, 'Content': {'string': 'wxid_ubbh6q832tcs21:\n[666]'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1753154638, 'MsgSource': '<msgsource>\n\t<pua>1</pua>\n\t<eggIncluded>1</eggIncluded>\n\t<silence>0</silence>\n\t<membercount>62</membercount>\n\t<signature>N0_V1_PTPYQrE2|v1_4qHSwtBe</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'PushContent': '郭 : [666]', 'NewMsgId': 5137643088027348433, 'MsgSeq': 871390598}
2025-07-22 11:25:06 | INFO | 收到表情消息: 消息ID:1593816919 来自:48097389945@chatroom 发送人:wxid_ubbh6q832tcs21 @:[] 内容:[666]
2025-07-22 11:25:06 | DEBUG | [DoubaoImageRecognition] 缓存表情消息: 5137643088027348433
2025-07-22 11:25:07 | DEBUG | 收到消息: {'MsgId': 1248760687, 'FromUserName': {'string': '48097389945@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 49, 'Content': {'string': 'wxid_wlnzvr8ivgd422:\n<?xml version="1.0"?>\n<msg>\n\t<appmsg appid="" sdkver="0">\n\t\t<title>当前版本不支持展示该内容，请升级至最新版本。</title>\n\t\t<des />\n\t\t<username />\n\t\t<action>view</action>\n\t\t<type>51</type>\n\t\t<showtype>0</showtype>\n\t\t<content />\n\t\t<url>https://support.weixin.qq.com/security/readtemplate?t=w_security_center_website/upgrade</url>\n\t\t<lowurl />\n\t\t<forwardflag>0</forwardflag>\n\t\t<dataurl />\n\t\t<lowdataurl />\n\t\t<contentattr>0</contentattr>\n\t\t<streamvideo>\n\t\t\t<streamvideourl />\n\t\t\t<streamvideototaltime>0</streamvideototaltime>\n\t\t\t<streamvideotitle />\n\t\t\t<streamvideowording />\n\t\t\t<streamvideoweburl />\n\t\t\t<streamvideothumburl />\n\t\t\t<streamvideoaduxinfo />\n\t\t\t<streamvideopublishid />\n\t\t</streamvideo>\n\t\t<canvasPageItem>\n\t\t\t<canvasPageXml><![CDATA[]]></canvasPageXml>\n\t\t</canvasPageItem>\n\t\t<appattach>\n\t\t\t<totallen>0</totallen>\n\t\t\t<attachid />\n\t\t\t<cdnattachurl />\n\t\t\t<emoticonmd5 />\n\t\t\t<aeskey />\n\t\t\t<fileext />\n\t\t\t<islargefilemsg>0</islargefilemsg>\n\t\t</appattach>\n\t\t<extinfo />\n\t\t<androidsource>0</androidsource>\n\t\t<thumburl />\n\t\t<mediatagname />\n\t\t<messageaction><![CDATA[]]></messageaction>\n\t\t<messageext><![CDATA[]]></messageext>\n\t\t<emoticongift>\n\t\t\t<packageflag>0</packageflag>\n\t\t\t<packageid />\n\t\t</emoticongift>\n\t\t<emoticonshared>\n\t\t\t<packageflag>0</packageflag>\n\t\t\t<packageid />\n\t\t</emoticonshared>\n\t\t<designershared>\n\t\t\t<designeruin>0</designeruin>\n\t\t\t<designername>null</designername>\n\t\t\t<designerrediretcturl><![CDATA[null]]></designerrediretcturl>\n\t\t</designershared>\n\t\t<emotionpageshared>\n\t\t\t<tid>0</tid>\n\t\t\t<title>null</title>\n\t\t\t<desc>null</desc>\n\t\t\t<iconUrl><![CDATA[null]]></iconUrl>\n\t\t\t<secondUrl>null</secondUrl>\n\t\t\t<pageType>0</pageType>\n\t\t\t<setKey>null</setKey>\n\t\t</emotionpageshared>\n\t\t<webviewshared>\n\t\t\t<shareUrlOriginal />\n\t\t\t<shareUrlOpen />\n\t\t\t<jsAppId />\n\t\t\t<publisherId />\n\t\t\t<publisherReqId />\n\t\t</webviewshared>\n\t\t<template_id />\n\t\t<md5 />\n\t\t<websearch>\n\t\t\t<rec_category>0</rec_category>\n\t\t\t<channelId>0</channelId>\n\t\t</websearch>\n\t\t<weappinfo>\n\t\t\t<username />\n\t\t\t<appid />\n\t\t\t<appservicetype>0</appservicetype>\n\t\t\t<secflagforsinglepagemode>0</secflagforsinglepagemode>\n\t\t\t<videopageinfo>\n\t\t\t\t<thumbwidth>0</thumbwidth>\n\t\t\t\t<thumbheight>0</thumbheight>\n\t\t\t\t<fromopensdk>0</fromopensdk>\n\t\t\t</videopageinfo>\n\t\t</weappinfo>\n\t\t<statextstr />\n\t\t<musicShareItem>\n\t\t\t<musicDuration>0</musicDuration>\n\t\t</musicShareItem>\n\t\t<finderFeed>\n\t\t\t<objectId>14706160101306992652</objectId>\n\t\t\t<objectNonceId>16478673311894527670_4_20_13_1_1753154419804690_cbcd1f94-66aa-11f0-98a2-4538b5d4825f</objectNonceId>\n\t\t\t<feedType>4</feedType>\n\t\t\t<nickname>皮皮鼠看世界</nickname>\n\t\t\t<username>v2_060000231003b20faec8cae3811dc6d6c80cef37b077a43b304f271d7947d35a0dc13afc5a37@finder</username>\n\t\t\t<avatar><![CDATA[https://wx.qlogo.cn/finderhead/ver_1/a8wbykR7RHm3xjTicRaPk7bpMFm78EdbACianiclG5GX2Rk24icoU1Te6c8nD992SUzFR5WD9Z2tF3E6QjHOhLvVpR9DKDR49RQbd6ZQA7upgY9eIZaDPQZJQaFib68MQUuhib/0]]></avatar>\n\t\t\t<desc>第一次知道……</desc>\n\t\t\t<mediaCount>1</mediaCount>\n\t\t\t<localId>0</localId>\n\t\t\t<authIconType>0</authIconType>\n\t\t\t<authIconUrl><![CDATA[]]></authIconUrl>\n\t\t\t<mediaList>\n\t\t\t\t<media>\n\t\t\t\t\t<mediaType>4</mediaType>\n\t\t\t\t\t<url><![CDATA[http://wxapp.tc.qq.com/251/20302/stodownload?encfilekey=rjD5jyTuFrIpZ2ibE8T7YmwgiahniaXswqzOYn1rMib2wbq8cCelh2vInP1OPTVde3gvPNdskMicNqMHx7a6d7jF0RpMibldf63N9D9OE6icjTc7lxpnqVgcfQybA&hy=SH&idx=1&m=&uzid=7a170&token=cztXnd9GyrHn5ckTVdLUEelGwQwwibgmuoZ17iaOaS3OrfmDfRAWWULMH3xsFLoOcVn34HU0tJGvNsRrjmP9AFJRSQ2CqjjJGJCsmh73zwicW6palXbIQjJzfxp0icOTdg1TWIyTHrviaOFuX21JcgLFqLiax6ePLkqzCibtzziamGvuEI0&basedata=CAESBnhXVDEyMRoGeFdUMTExGgZ4V1QxMTIaBnhXVDEyNhoGeFdUMTEzGgZ4V1QxMjEaBnhXVDEyNxoGeFdUMTI4IgwKCgoGeFdUMTEyEAEqBwi9HhAAGAI&sign=iE4VHVM5GGirUR98qOYPsi6TIqxxa73IeNKR5ABL186yUcIdO1_MZjxQ8VBBlOHqqICTM7sxblyeBllEhfjb9w&ctsc=20&extg=108bd00&ftype=201&svrbypass=AAuL%2FQsFAAABAAAAAAC303idLp%2Bbae0bdgN%2FaBAAAADnaHZTnGbFfAj9RgZXfw6V517ikzCbA9hJKGT6M9FRcuw%2BtrQgP5xZva1%2FupXwn3adDa5l3fxk%2FrQ%3D&svrnonce=1753154422]]></url>\n\t\t\t\t\t<thumbUrl><![CDATA[http://wxapp.tc.qq.com/251/20304/stodownload?encfilekey=WTva9YVXqXcSUicrMCercmDHmKYPBXC7eibFdNzic5KU47yxhmyviaVw39ias7AyibvQ4stvcBQocb6fO2zy6eicQylyL0de19chQjZwibdFlicZ9oj8bDb8Vp7gtY09wTPJBjTibsRiaHPxiaticXrs&hy=SH&idx=1&m=710f124dd9441635e3d10e39ab7a7892&uzid=1&picformat=200&wxampicformat=503&token=6xykWLEnztKIzBicPuvgFxnic21zcaPwwFtPtg72JrWczJ63X15NicKXTSTtZIpK4U9H6gTeIrRic0h3vuzMhFYf1wW1UrcHmCz0ehMQwQwHKWAsytbr21ibleMgVs9ibyd0S6AfRHGvPpmaQscU97HvNlBliaOC890wmWZ&ctsc=2-20]]></thumbUrl>\n\t\t\t\t\t<coverUrl><![CDATA[http://wxapp.tc.qq.com/251/20304/stodownload?encfilekey=WTva9YVXqXcSUicrMCercmDHmKYPBXC7eibFdNzic5KU47yxhmyviaVw39ias7AyibvQ4stvcBQocb6fO2zy6eicQylyL0de19chQjZwibdFlicZ9oj8bDb8Vp7gtY09wTPJBjTibsRiaHPxiaticXrs&hy=SH&idx=1&m=710f124dd9441635e3d10e39ab7a7892&uzid=1&picformat=200&wxampicformat=503&token=6xykWLEnztKIzBicPuvgFxnic21zcaPwwFtPtg72JrWczJ63X15NicKXTSTtZIpK4U9H6gTeIrRic0h3vuzMhFYf1wW1UrcHmCz0ehMQwQwHKWAsytbr21ibleMgVs9ibyd0S6AfRHGvPpmaQscU97HvNlBliaOC890wmWZ&ctsc=2-20]]></coverUrl>\n\t\t\t\t\t<fullCoverUrl><![CDATA[]]></fullCoverUrl>\n\t\t\t\t\t<fullClipInset><![CDATA[[0.0,0.0,0.0,0.0]]]></fullClipInset>\n\t\t\t\t\t<width>720.0</width>\n\t\t\t\t\t<height>1332.0</height>\n\t\t\t\t\t<videoPlayDuration>11</videoPlayDuration>\n\t\t\t\t</media>\n\t\t\t</mediaList>\n\t\t\t<megaVideo>\n\t\t\t\t<objectId />\n\t\t\t\t<objectNonceId />\n\t\t\t</megaVideo>\n\t\t\t<bizUsername />\n\t\t\t<bizNickname />\n\t\t\t<bizAvatar><![CDATA[]]></bizAvatar>\n\t\t\t<bizUsernameV2 />\n\t\t\t<bizAuthIconType>0</bizAuthIconType>\n\t\t\t<bizAuthIconUrl><![CDATA[]]></bizAuthIconUrl>\n\t\t\t<coverEffectType>0</coverEffectType>\n\t\t\t<coverEffectText><![CDATA[]]></coverEffectText>\n\t\t\t<finderForwardSource><![CDATA[]]></finderForwardSource>\n\t\t\t<contactJumpInfoStr><![CDATA[]]></contactJumpInfoStr>\n\t\t\t<ecSource><![CDATA[]]></ecSource>\n\t\t\t<lastGMsgID><![CDATA[]]></lastGMsgID>\n\t\t\t<sourceCommentScene>20</sourceCommentScene>\n\t\t\t<finderShareExtInfo><![CDATA[{"hasInput":false,"tabContextId":"4-1753154595311","contextId":"1-1-20-97307ae265fb4b92822859571c7fec39","shareSrcScene":4}]]></finderShareExtInfo>\n\t\t</finderFeed>\n\t\t<finderLiveProductShare>\n\t\t\t<finderLiveID><![CDATA[]]></finderLiveID>\n\t\t\t<finderUsername><![CDATA[]]></finderUsername>\n\t\t\t<finderObjectID><![CDATA[]]></finderObjectID>\n\t\t\t<finderNonceID><![CDATA[]]></finderNonceID>\n\t\t\t<liveStatus><![CDATA[]]></liveStatus>\n\t\t\t<appId><![CDATA[]]></appId>\n\t\t\t<pagePath><![CDATA[]]></pagePath>\n\t\t\t<productId><![CDATA[]]></productId>\n\t\t\t<coverUrl><![CDATA[]]></coverUrl>\n\t\t\t<productTitle><![CDATA[]]></productTitle>\n\t\t\t<marketPrice><![CDATA[0]]></marketPrice>\n\t\t\t<sellingPrice><![CDATA[0]]></sellingPrice>\n\t\t\t<platformHeadImg><![CDATA[]]></platformHeadImg>\n\t\t\t<platformName><![CDATA[]]></platformName>\n\t\t\t<shopWindowId><![CDATA[]]></shopWindowId>\n\t\t\t<flashSalePrice><![CDATA[0]]></flashSalePrice>\n\t\t\t<flashSaleEndTime><![CDATA[0]]></flashSaleEndTime>\n\t\t\t<ecSource><![CDATA[]]></ecSource>\n\t\t\t<sellingPriceWording><![CDATA[]]></sellingPriceWording>\n\t\t\t<platformIconURL><![CDATA[]]></platformIconURL>\n\t\t\t<firstProductTagURL><![CDATA[]]></firstProductTagURL>\n\t\t\t<firstProductTagAspectRatioString><![CDATA[0.0]]></firstProductTagAspectRatioString>\n\t\t\t<secondProductTagURL><![CDATA[]]></secondProductTagURL>\n\t\t\t<secondProductTagAspectRatioString><![CDATA[0.0]]></secondProductTagAspectRatioString>\n\t\t\t<firstGuaranteeWording><![CDATA[]]></firstGuaranteeWording>\n\t\t\t<secondGuaranteeWording><![CDATA[]]></secondGuaranteeWording>\n\t\t\t<thirdGuaranteeWording><![CDATA[]]></thirdGuaranteeWording>\n\t\t\t<isPriceBeginShow>false</isPriceBeginShow>\n\t\t\t<lastGMsgID><![CDATA[]]></lastGMsgID>\n\t\t\t<promoterKey><![CDATA[]]></promoterKey>\n\t\t\t<discountWording><![CDATA[]]></discountWording>\n\t\t\t<priceSuffixDescription><![CDATA[]]></priceSuffixDescription>\n\t\t\t<productCardKey><![CDATA[]]></productCardKey>\n\t\t\t<isWxShop><![CDATA[]]></isWxShop>\n\t\t\t<brandIconUrl><![CDATA[]]></brandIconUrl>\n\t\t\t<rIconUrl><![CDATA[]]></rIconUrl>\n\t\t\t<rIconUrlDarkMode><![CDATA[]]></rIconUrlDarkMode>\n\t\t\t<topShopIconUrl><![CDATA[]]></topShopIconUrl>\n\t\t\t<topShopIconUrlDarkMode><![CDATA[]]></topShopIconUrlDarkMode>\n\t\t\t<simplifyTopShopIconUrl><![CDATA[]]></simplifyTopShopIconUrl>\n\t\t\t<simplifyTopShopIconUrlDarkmode><![CDATA[]]></simplifyTopShopIconUrlDarkmode>\n\t\t\t<topShopIconWidth><![CDATA[0]]></topShopIconWidth>\n\t\t\t<topShopIconHeight><![CDATA[0]]></topShopIconHeight>\n\t\t\t<simplifyTopShopIconWidth><![CDATA[0]]></simplifyTopShopIconWidth>\n\t\t\t<simplifyTopShopIconHeight><![CDATA[0]]></simplifyTopShopIconHeight>\n\t\t\t<showBoxItemStringList />\n\t\t</finderLiveProductShare>\n\t\t<finderOrder>\n\t\t\t<appID><![CDATA[]]></appID>\n\t\t\t<orderID><![CDATA[]]></orderID>\n\t\t\t<path><![CDATA[]]></path>\n\t\t\t<priceWording><![CDATA[]]></priceWording>\n\t\t\t<stateWording><![CDATA[]]></stateWording>\n\t\t\t<productImageURL><![CDATA[]]></productImageURL>\n\t\t\t<products><![CDATA[]]></products>\n\t\t\t<productsCount><![CDATA[0]]></productsCount>\n\t\t\t<orderType><![CDATA[0]]></orderType>\n\t\t\t<newPriceWording><![CDATA[]]></newPriceWording>\n\t\t\t<newStateWording><![CDATA[]]></newStateWording>\n\t\t\t<useNewWording><![CDATA[0]]></useNewWording>\n\t\t</finderOrder>\n\t\t<finderShopWindowShare>\n\t\t\t<finderUsername><![CDATA[]]></finderUsername>\n\t\t\t<avatar><![CDATA[]]></avatar>\n\t\t\t<nickname><![CDATA[]]></nickname>\n\t\t\t<commodityInStockCount><![CDATA[]]></commodityInStockCount>\n\t\t\t<appId><![CDATA[]]></appId>\n\t\t\t<path><![CDATA[]]></path>\n\t\t\t<appUsername><![CDATA[]]></appUsername>\n\t\t\t<query><![CDATA[]]></query>\n\t\t\t<liteAppId><![CDATA[]]></liteAppId>\n\t\t\t<liteAppPath><![CDATA[]]></liteAppPath>\n\t\t\t<liteAppQuery><![CDATA[]]></liteAppQuery>\n\t\t\t<platformTagURL><![CDATA[]]></platformTagURL>\n\t\t\t<saleWording><![CDATA[]]></saleWording>\n\t\t\t<lastGMsgID><![CDATA[]]></lastGMsgID>\n\t\t\t<profileTypeWording><![CDATA[]]></profileTypeWording>\n\t\t\t<saleWordingExtra><![CDATA[]]></saleWordingExtra>\n\t\t\t<isWxShop><![CDATA[]]></isWxShop>\n\t\t\t<platformIconUrl><![CDATA[]]></platformIconUrl>\n\t\t\t<brandIconUrl><![CDATA[]]></brandIconUrl>\n\t\t\t<description><![CDATA[]]></description>\n\t\t\t<backgroundUrl><![CDATA[]]></backgroundUrl>\n\t\t\t<darkModePlatformIconUrl><![CDATA[]]></darkModePlatformIconUrl>\n\t\t\t<rIconUrl><![CDATA[]]></rIconUrl>\n\t\t\t<rIconUrlDarkMode><![CDATA[]]></rIconUrlDarkMode>\n\t\t\t<rWords><![CDATA[]]></rWords>\n\t\t\t<topShopIconUrl><![CDATA[]]></topShopIconUrl>\n\t\t\t<topShopIconUrlDarkMode><![CDATA[]]></topShopIconUrlDarkMode>\n\t\t\t<simplifyTopShopIconUrl><![CDATA[]]></simplifyTopShopIconUrl>\n\t\t\t<simplifyTopShopIconUrlDarkmode><![CDATA[]]></simplifyTopShopIconUrlDarkmode>\n\t\t\t<topShopIconWidth><![CDATA[0]]></topShopIconWidth>\n\t\t\t<topShopIconHeight><![CDATA[0]]></topShopIconHeight>\n\t\t\t<simplifyTopShopIconWidth><![CDATA[0]]></simplifyTopShopIconWidth>\n\t\t\t<simplifyTopShopIconHeight><![CDATA[0]]></simplifyTopShopIconHeight>\n\t\t\t<reputationInfo>\n\t\t\t\t<hasReputationInfo>0</hasReputationInfo>\n\t\t\t\t<reputationScore>0</reputationScore>\n\t\t\t\t<reputationWording />\n\t\t\t\t<reputationTextColor />\n\t\t\t\t<reputationLevelWording />\n\t\t\t\t<reputationBackgroundColor />\n\t\t\t</reputationInfo>\n\t\t\t<productImageURLList />\n\t\t</finderShopWindowShare>\n\t\t<findernamecard>\n\t\t\t<username />\n\t\t\t<avatar><![CDATA[]]></avatar>\n\t\t\t<nickname />\n\t\t\t<auth_job />\n\t\t\t<auth_icon>0</auth_icon>\n\t\t\t<auth_icon_url />\n\t\t\t<ecSource><![CDATA[]]></ecSource>\n\t\t\t<lastGMsgID><![CDATA[]]></lastGMsgID>\n\t\t</findernamecard>\n\t\t<finderGuarantee>\n\t\t\t<scene><![CDATA[0]]></scene>\n\t\t</finderGuarantee>\n\t\t<directshare>0</directshare>\n\t\t<gamecenter>\n\t\t\t<namecard>\n\t\t\t\t<iconUrl />\n\t\t\t\t<name />\n\t\t\t\t<desc />\n\t\t\t\t<tail />\n\t\t\t\t<jumpUrl />\n\t\t\t\t<liteappId />\n\t\t\t\t<liteappPath />\n\t\t\t\t<liteappQuery />\n\t\t\t\t<liteappMinVersion />\n\t\t\t</namecard>\n\t\t</gamecenter>\n\t\t<patMsg>\n\t\t\t<chatUser />\n\t\t\t<records>\n\t\t\t\t<recordNum>0</recordNum>\n\t\t\t</records>\n\t\t</patMsg>\n\t\t<secretmsg>\n\t\t\t<issecretmsg>0</issecretmsg>\n\t\t</secretmsg>\n\t\t<referfromscene>0</referfromscene>\n\t\t<gameshare>\n\t\t\t<liteappext>\n\t\t\t\t<liteappbizdata />\n\t\t\t\t<priority>0</priority>\n\t\t\t</liteappext>\n\t\t\t<appbrandext>\n\t\t\t\t<litegameinfo />\n\t\t\t\t<priority>-1</priority>\n\t\t\t</appbrandext>\n\t\t\t<gameshareid />\n\t\t\t<sharedata />\n\t\t\t<isvideo>0</isvideo>\n\t\t\t<duration>-1</duration>\n\t\t\t<isexposed>0</isexposed>\n\t\t\t<readtext />\n\t\t</gameshare>\n\t\t<tingChatRoomItem>\n\t\t\t<type>0</type>\n\t\t\t<categoryItem>null</categoryItem>\n\t\t\t<categoryId />\n\t\t</tingChatRoomItem>\n\t\t<mpsharetrace>\n\t\t\t<hasfinderelement>0</hasfinderelement>\n\t\t\t<lastgmsgid />\n\t\t</mpsharetrace>\n\t\t<wxgamecard>\n\t\t\t<framesetname />\n\t\t\t<mbcarddata />\n\t\t\t<minpkgversion />\n\t\t\t<clientextinfo />\n\t\t\t<mbcardheight>0</mbcardheight>\n\t\t\t<isoldversion>0</isoldversion>\n\t\t</wxgamecard>\n\t\t<ecskfcard>\n\t\t\t<framesetname />\n\t\t\t<mbcarddata />\n\t\t\t<minupdateunixtimestamp>0</minupdateunixtimestamp>\n\t\t\t<needheader>false</needheader>\n\t\t\t<summary />\n\t\t</ecskfcard>\n\t\t<liteapp>\n\t\t\t<id>null</id>\n\t\t\t<path />\n\t\t\t<query />\n\t\t\t<istransparent>0</istransparent>\n\t\t\t<hideicon>0</hideicon>\n\t\t\t<forbidforward>0</forbidforward>\n\t\t</liteapp>\n\t\t<opensdk_share_is_modified>0</opensdk_share_is_modified>\n\t</appmsg>\n\t<fromusername>wxid_wlnzvr8ivgd422</fromusername>\n\t<scene>0</scene>\n\t<appinfo>\n\t\t<version>1</version>\n\t\t<appname></appname>\n\t</appinfo>\n\t<commenturl></commenturl>\n</msg>\n'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1753154678, 'MsgSource': '<msgsource>\n\t<alnode>\n\t\t<fr>2</fr>\n\t</alnode>\n\t<sec_msg_node>\n\t\t<uuid>5e089b01e02b55cc320db8c1cd70e727_</uuid>\n\t\t<risk-file-flag />\n\t\t<risk-file-md5-list />\n\t</sec_msg_node>\n\t<silence>0</silence>\n\t<membercount>62</membercount>\n\t<signature>N0_V1_2fisoinp|v1_e3JCQfAj</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'PushContent': '你收到了一条消息', 'NewMsgId': 8020031116044941576, 'MsgSeq': 871390603}
2025-07-22 11:25:07 | DEBUG | 从群聊消息中提取发送者: wxid_wlnzvr8ivgd422
2025-07-22 11:25:07 | DEBUG | XML消息完整内容:
<?xml version="1.0"?>
<msg>
	<appmsg appid="" sdkver="0">
		<title>当前版本不支持展示该内容，请升级至最新版本。</title>
		<des />
		<username />
		<action>view</action>
		<type>51</type>
		<showtype>0</showtype>
		<content />
		<url>https://support.weixin.qq.com/security/readtemplate?t=w_security_center_website/upgrade</url>
		<lowurl />
		<forwardflag>0</forwardflag>
		<dataurl />
		<lowdataurl />
		<contentattr>0</contentattr>
		<streamvideo>
			<streamvideourl />
			<streamvideototaltime>0</streamvideototaltime>
			<streamvideotitle />
			<streamvideowording />
			<streamvideoweburl />
			<streamvideothumburl />
			<streamvideoaduxinfo />
			<streamvideopublishid />
		</streamvideo>
		<canvasPageItem>
			<canvasPageXml><![CDATA[]]></canvasPageXml>
		</canvasPageItem>
		<appattach>
			<totallen>0</totallen>
			<attachid />
			<cdnattachurl />
			<emoticonmd5 />
			<aeskey />
			<fileext />
			<islargefilemsg>0</islargefilemsg>
		</appattach>
		<extinfo />
		<androidsource>0</androidsource>
		<thumburl />
		<mediatagname />
		<messageaction><![CDATA[]]></messageaction>
		<messageext><![CDATA[]]></messageext>
		<emoticongift>
			<packageflag>0</packageflag>
			<packageid />
		</emoticongift>
		<emoticonshared>
			<packageflag>0</packageflag>
			<packageid />
		</emoticonshared>
		<designershared>
			<designeruin>0</designeruin>
			<designername>null</designername>
			<designerrediretcturl><![CDATA[null]]></designerrediretcturl>
		</designershared>
		<emotionpageshared>
			<tid>0</tid>
			<title>null</title>
			<desc>null</desc>
			<iconUrl><![CDATA[null]]></iconUrl>
			<secondUrl>null</secondUrl>
			<pageType>0</pageType>
			<setKey>null</setKey>
		</emotionpageshared>
		<webviewshared>
			<shareUrlOriginal />
			<shareUrlOpen />
			<jsAppId />
			<publisherId />
			<publisherReqId />
		</webviewshared>
		<template_id />
		<md5 />
		<websearch>
			<rec_category>0</rec_category>
			<channelId>0</channelId>
		</websearch>
		<weappinfo>
			<username />
			<appid />
			<appservicetype>0</appservicetype>
			<secflagforsinglepagemode>0</secflagforsinglepagemode>
			<videopageinfo>
				<thumbwidth>0</thumbwidth>
				<thumbheight>0</thumbheight>
				<fromopensdk>0</fromopensdk>
			</videopageinfo>
		</weappinfo>
		<statextstr />
		<musicShareItem>
			<musicDuration>0</musicDuration>
		</musicShareItem>
		<finderFeed>
			<objectId>14706160101306992652</objectId>
			<objectNonceId>16478673311894527670_4_20_13_1_1753154419804690_cbcd1f94-66aa-11f0-98a2-4538b5d4825f</objectNonceId>
			<feedType>4</feedType>
			<nickname>皮皮鼠看世界</nickname>
			<username>v2_060000231003b20faec8cae3811dc6d6c80cef37b077a43b304f271d7947d35a0dc13afc5a37@finder</username>
			<avatar><![CDATA[https://wx.qlogo.cn/finderhead/ver_1/a8wbykR7RHm3xjTicRaPk7bpMFm78EdbACianiclG5GX2Rk24icoU1Te6c8nD992SUzFR5WD9Z2tF3E6QjHOhLvVpR9DKDR49RQbd6ZQA7upgY9eIZaDPQZJQaFib68MQUuhib/0]]></avatar>
			<desc>第一次知道……</desc>
			<mediaCount>1</mediaCount>
			<localId>0</localId>
			<authIconType>0</authIconType>
			<authIconUrl><![CDATA[]]></authIconUrl>
			<mediaList>
				<media>
					<mediaType>4</mediaType>
					<url><![CDATA[http://wxapp.tc.qq.com/251/20302/stodownload?encfilekey=rjD5jyTuFrIpZ2ibE8T7YmwgiahniaXswqzOYn1rMib2wbq8cCelh2vInP1OPTVde3gvPNdskMicNqMHx7a6d7jF0RpMibldf63N9D9OE6icjTc7lxpnqVgcfQybA&hy=SH&idx=1&m=&uzid=7a170&token=cztXnd9GyrHn5ckTVdLUEelGwQwwibgmuoZ17iaOaS3OrfmDfRAWWULMH3xsFLoOcVn34HU0tJGvNsRrjmP9AFJRSQ2CqjjJGJCsmh73zwicW6palXbIQjJzfxp0icOTdg1TWIyTHrviaOFuX21JcgLFqLiax6ePLkqzCibtzziamGvuEI0&basedata=CAESBnhXVDEyMRoGeFdUMTExGgZ4V1QxMTIaBnhXVDEyNhoGeFdUMTEzGgZ4V1QxMjEaBnhXVDEyNxoGeFdUMTI4IgwKCgoGeFdUMTEyEAEqBwi9HhAAGAI&sign=iE4VHVM5GGirUR98qOYPsi6TIqxxa73IeNKR5ABL186yUcIdO1_MZjxQ8VBBlOHqqICTM7sxblyeBllEhfjb9w&ctsc=20&extg=108bd00&ftype=201&svrbypass=AAuL%2FQsFAAABAAAAAAC303idLp%2Bbae0bdgN%2FaBAAAADnaHZTnGbFfAj9RgZXfw6V517ikzCbA9hJKGT6M9FRcuw%2BtrQgP5xZva1%2FupXwn3adDa5l3fxk%2FrQ%3D&svrnonce=1753154422]]></url>
					<thumbUrl><![CDATA[http://wxapp.tc.qq.com/251/20304/stodownload?encfilekey=WTva9YVXqXcSUicrMCercmDHmKYPBXC7eibFdNzic5KU47yxhmyviaVw39ias7AyibvQ4stvcBQocb6fO2zy6eicQylyL0de19chQjZwibdFlicZ9oj8bDb8Vp7gtY09wTPJBjTibsRiaHPxiaticXrs&hy=SH&idx=1&m=710f124dd9441635e3d10e39ab7a7892&uzid=1&picformat=200&wxampicformat=503&token=6xykWLEnztKIzBicPuvgFxnic21zcaPwwFtPtg72JrWczJ63X15NicKXTSTtZIpK4U9H6gTeIrRic0h3vuzMhFYf1wW1UrcHmCz0ehMQwQwHKWAsytbr21ibleMgVs9ibyd0S6AfRHGvPpmaQscU97HvNlBliaOC890wmWZ&ctsc=2-20]]></thumbUrl>
					<coverUrl><![CDATA[http://wxapp.tc.qq.com/251/20304/stodownload?encfilekey=WTva9YVXqXcSUicrMCercmDHmKYPBXC7eibFdNzic5KU47yxhmyviaVw39ias7AyibvQ4stvcBQocb6fO2zy6eicQylyL0de19chQjZwibdFlicZ9oj8bDb8Vp7gtY09wTPJBjTibsRiaHPxiaticXrs&hy=SH&idx=1&m=710f124dd9441635e3d10e39ab7a7892&uzid=1&picformat=200&wxampicformat=503&token=6xykWLEnztKIzBicPuvgFxnic21zcaPwwFtPtg72JrWczJ63X15NicKXTSTtZIpK4U9H6gTeIrRic0h3vuzMhFYf1wW1UrcHmCz0ehMQwQwHKWAsytbr21ibleMgVs9ibyd0S6AfRHGvPpmaQscU97HvNlBliaOC890wmWZ&ctsc=2-20]]></coverUrl>
					<fullCoverUrl><![CDATA[]]></fullCoverUrl>
					<fullClipInset><![CDATA[[0.0,0.0,0.0,0.0]]]></fullClipInset>
					<width>720.0</width>
					<height>1332.0</height>
					<videoPlayDuration>11</videoPlayDuration>
				</media>
			</mediaList>
			<megaVideo>
				<objectId />
				<objectNonceId />
			</megaVideo>
			<bizUsername />
			<bizNickname />
			<bizAvatar><![CDATA[]]></bizAvatar>
			<bizUsernameV2 />
			<bizAuthIconType>0</bizAuthIconType>
			<bizAuthIconUrl><![CDATA[]]></bizAuthIconUrl>
			<coverEffectType>0</coverEffectType>
			<coverEffectText><![CDATA[]]></coverEffectText>
			<finderForwardSource><![CDATA[]]></finderForwardSource>
			<contactJumpInfoStr><![CDATA[]]></contactJumpInfoStr>
			<ecSource><![CDATA[]]></ecSource>
			<lastGMsgID><![CDATA[]]></lastGMsgID>
			<sourceCommentScene>20</sourceCommentScene>
			<finderShareExtInfo><![CDATA[{"hasInput":false,"tabContextId":"4-1753154595311","contextId":"1-1-20-97307ae265fb4b92822859571c7fec39","shareSrcScene":4}]]></finderShareExtInfo>
		</finderFeed>
		<finderLiveProductShare>
			<finderLiveID><![CDATA[]]></finderLiveID>
			<finderUsername><![CDATA[]]></finderUsername>
			<finderObjectID><![CDATA[]]></finderObjectID>
			<finderNonceID><![CDATA[]]></finderNonceID>
			<liveStatus><![CDATA[]]></liveStatus>
			<appId><![CDATA[]]></appId>
			<pagePath><![CDATA[]]></pagePath>
			<productId><![CDATA[]]></productId>
			<coverUrl><![CDATA[]]></coverUrl>
			<productTitle><![CDATA[]]></productTitle>
			<marketPrice><![CDATA[0]]></marketPrice>
			<sellingPrice><![CDATA[0]]></sellingPrice>
			<platformHeadImg><![CDATA[]]></platformHeadImg>
			<platformName><![CDATA[]]></platformName>
			<shopWindowId><![CDATA[]]></shopWindowId>
			<flashSalePrice><![CDATA[0]]></flashSalePrice>
			<flashSaleEndTime><![CDATA[0]]></flashSaleEndTime>
			<ecSource><![CDATA[]]></ecSource>
			<sellingPriceWording><![CDATA[]]></sellingPriceWording>
			<platformIconURL><![CDATA[]]></platformIconURL>
			<firstProductTagURL><![CDATA[]]></firstProductTagURL>
			<firstProductTagAspectRatioString><![CDATA[0.0]]></firstProductTagAspectRatioString>
			<secondProductTagURL><![CDATA[]]></secondProductTagURL>
			<secondProductTagAspectRatioString><![CDATA[0.0]]></secondProductTagAspectRatioString>
			<firstGuaranteeWording><![CDATA[]]></firstGuaranteeWording>
			<secondGuaranteeWording><![CDATA[]]></secondGuaranteeWording>
			<thirdGuaranteeWording><![CDATA[]]></thirdGuaranteeWording>
			<isPriceBeginShow>false</isPriceBeginShow>
			<lastGMsgID><![CDATA[]]></lastGMsgID>
			<promoterKey><![CDATA[]]></promoterKey>
			<discountWording><![CDATA[]]></discountWording>
			<priceSuffixDescription><![CDATA[]]></priceSuffixDescription>
			<productCardKey><![CDATA[]]></productCardKey>
			<isWxShop><![CDATA[]]></isWxShop>
			<brandIconUrl><![CDATA[]]></brandIconUrl>
			<rIconUrl><![CDATA[]]></rIconUrl>
			<rIconUrlDarkMode><![CDATA[]]></rIconUrlDarkMode>
			<topShopIconUrl><![CDATA[]]></topShopIconUrl>
			<topShopIconUrlDarkMode><![CDATA[]]></topShopIconUrlDarkMode>
			<simplifyTopShopIconUrl><![CDATA[]]></simplifyTopShopIconUrl>
			<simplifyTopShopIconUrlDarkmode><![CDATA[]]></simplifyTopShopIconUrlDarkmode>
			<topShopIconWidth><![CDATA[0]]></topShopIconWidth>
			<topShopIconHeight><![CDATA[0]]></topShopIconHeight>
			<simplifyTopShopIconWidth><![CDATA[0]]></simplifyTopShopIconWidth>
			<simplifyTopShopIconHeight><![CDATA[0]]></simplifyTopShopIconHeight>
			<showBoxItemStringList />
		</finderLiveProductShare>
		<finderOrder>
			<appID><![CDATA[]]></appID>
			<orderID><![CDATA[]]></orderID>
			<path><![CDATA[]]></path>
			<priceWording><![CDATA[]]></priceWording>
			<stateWording><![CDATA[]]></stateWording>
			<productImageURL><![CDATA[]]></productImageURL>
			<products><![CDATA[]]></products>
			<productsCount><![CDATA[0]]></productsCount>
			<orderType><![CDATA[0]]></orderType>
			<newPriceWording><![CDATA[]]></newPriceWording>
			<newStateWording><![CDATA[]]></newStateWording>
			<useNewWording><![CDATA[0]]></useNewWording>
		</finderOrder>
		<finderShopWindowShare>
			<finderUsername><![CDATA[]]></finderUsername>
			<avatar><![CDATA[]]></avatar>
			<nickname><![CDATA[]]></nickname>
			<commodityInStockCount><![CDATA[]]></commodityInStockCount>
			<appId><![CDATA[]]></appId>
			<path><![CDATA[]]></path>
			<appUsername><![CDATA[]]></appUsername>
			<query><![CDATA[]]></query>
			<liteAppId><![CDATA[]]></liteAppId>
			<liteAppPath><![CDATA[]]></liteAppPath>
			<liteAppQuery><![CDATA[]]></liteAppQuery>
			<platformTagURL><![CDATA[]]></platformTagURL>
			<saleWording><![CDATA[]]></saleWording>
			<lastGMsgID><![CDATA[]]></lastGMsgID>
			<profileTypeWording><![CDATA[]]></profileTypeWording>
			<saleWordingExtra><![CDATA[]]></saleWordingExtra>
			<isWxShop><![CDATA[]]></isWxShop>
			<platformIconUrl><![CDATA[]]></platformIconUrl>
			<brandIconUrl><![CDATA[]]></brandIconUrl>
			<description><![CDATA[]]></description>
			<backgroundUrl><![CDATA[]]></backgroundUrl>
			<darkModePlatformIconUrl><![CDATA[]]></darkModePlatformIconUrl>
			<rIconUrl><![CDATA[]]></rIconUrl>
			<rIconUrlDarkMode><![CDATA[]]></rIconUrlDarkMode>
			<rWords><![CDATA[]]></rWords>
			<topShopIconUrl><![CDATA[]]></topShopIconUrl>
			<topShopIconUrlDarkMode><![CDATA[]]></topShopIconUrlDarkMode>
			<simplifyTopShopIconUrl><![CDATA[]]></simplifyTopShopIconUrl>
			<simplifyTopShopIconUrlDarkmode><![CDATA[]]></simplifyTopShopIconUrlDarkmode>
			<topShopIconWidth><![CDATA[0]]></topShopIconWidth>
			<topShopIconHeight><![CDATA[0]]></topShopIconHeight>
			<simplifyTopShopIconWidth><![CDATA[0]]></simplifyTopShopIconWidth>
			<simplifyTopShopIconHeight><![CDATA[0]]></simplifyTopShopIconHeight>
			<reputationInfo>
				<hasReputationInfo>0</hasReputationInfo>
				<reputationScore>0</reputationScore>
				<reputationWording />
				<reputationTextColor />
				<reputationLevelWording />
				<reputationBackgroundColor />
			</reputationInfo>
			<productImageURLList />
		</finderShopWindowShare>
		<findernamecard>
			<username />
			<avatar><![CDATA[]]></avatar>
			<nickname />
			<auth_job />
			<auth_icon>0</auth_icon>
			<auth_icon_url />
			<ecSource><![CDATA[]]></ecSource>
			<lastGMsgID><![CDATA[]]></lastGMsgID>
		</findernamecard>
		<finderGuarantee>
			<scene><![CDATA[0]]></scene>
		</finderGuarantee>
		<directshare>0</directshare>
		<gamecenter>
			<namecard>
				<iconUrl />
				<name />
				<desc />
				<tail />
				<jumpUrl />
				<liteappId />
				<liteappPath />
				<liteappQuery />
				<liteappMinVersion />
			</namecard>
		</gamecenter>
		<patMsg>
			<chatUser />
			<records>
				<recordNum>0</recordNum>
			</records>
		</patMsg>
		<secretmsg>
			<issecretmsg>0</issecretmsg>
		</secretmsg>
		<referfromscene>0</referfromscene>
		<gameshare>
			<liteappext>
				<liteappbizdata />
				<priority>0</priority>
			</liteappext>
			<appbrandext>
				<litegameinfo />
				<priority>-1</priority>
			</appbrandext>
			<gameshareid />
			<sharedata />
			<isvideo>0</isvideo>
			<duration>-1</duration>
			<isexposed>0</isexposed>
			<readtext />
		</gameshare>
		<tingChatRoomItem>
			<type>0</type>
			<categoryItem>null</categoryItem>
			<categoryId />
		</tingChatRoomItem>
		<mpsharetrace>
			<hasfinderelement>0</hasfinderelement>
			<lastgmsgid />
		</mpsharetrace>
		<wxgamecard>
			<framesetname />
			<mbcarddata />
			<minpkgversion />
			<clientextinfo />
			<mbcardheight>0</mbcardheight>
			<isoldversion>0</isoldversion>
		</wxgamecard>
		<ecskfcard>
			<framesetname />
			<mbcarddata />
			<minupdateunixtimestamp>0</minupdateunixtimestamp>
			<needheader>false</needheader>
			<summary />
		</ecskfcard>
		<liteapp>
			<id>null</id>
			<path />
			<query />
			<istransparent>0</istransparent>
			<hideicon>0</hideicon>
			<forbidforward>0</forbidforward>
		</liteapp>
		<opensdk_share_is_modified>0</opensdk_share_is_modified>
	</appmsg>
	<fromusername>wxid_wlnzvr8ivgd422</fromusername>
	<scene>0</scene>
	<appinfo>
		<version>1</version>
		<appname></appname>
	</appinfo>
	<commenturl></commenturl>
</msg>

2025-07-22 11:25:07 | DEBUG | XML消息类型: 51
2025-07-22 11:25:07 | DEBUG | XML消息标题: 当前版本不支持展示该内容，请升级至最新版本。
2025-07-22 11:25:07 | DEBUG | XML消息描述: None
2025-07-22 11:25:07 | DEBUG | 附件信息 totallen: 0
2025-07-22 11:25:07 | DEBUG | 附件信息 islargefilemsg: 0
2025-07-22 11:25:07 | DEBUG | XML消息URL: https://support.weixin.qq.com/security/readtemplate?t=w_security_center_website/upgrade
2025-07-22 11:25:07 | INFO | 未知的XML消息类型: 51
2025-07-22 11:25:07 | INFO | 消息标题: 当前版本不支持展示该内容，请升级至最新版本。
2025-07-22 11:25:07 | INFO | 消息描述: None
2025-07-22 11:25:07 | INFO | 消息URL: https://support.weixin.qq.com/security/readtemplate?t=w_security_center_website/upgrade
2025-07-22 11:25:07 | INFO | 完整XML内容:
<?xml version="1.0"?>
<msg>
	<appmsg appid="" sdkver="0">
		<title>当前版本不支持展示该内容，请升级至最新版本。</title>
		<des />
		<username />
		<action>view</action>
		<type>51</type>
		<showtype>0</showtype>
		<content />
		<url>https://support.weixin.qq.com/security/readtemplate?t=w_security_center_website/upgrade</url>
		<lowurl />
		<forwardflag>0</forwardflag>
		<dataurl />
		<lowdataurl />
		<contentattr>0</contentattr>
		<streamvideo>
			<streamvideourl />
			<streamvideototaltime>0</streamvideototaltime>
			<streamvideotitle />
			<streamvideowording />
			<streamvideoweburl />
			<streamvideothumburl />
			<streamvideoaduxinfo />
			<streamvideopublishid />
		</streamvideo>
		<canvasPageItem>
			<canvasPageXml><![CDATA[]]></canvasPageXml>
		</canvasPageItem>
		<appattach>
			<totallen>0</totallen>
			<attachid />
			<cdnattachurl />
			<emoticonmd5 />
			<aeskey />
			<fileext />
			<islargefilemsg>0</islargefilemsg>
		</appattach>
		<extinfo />
		<androidsource>0</androidsource>
		<thumburl />
		<mediatagname />
		<messageaction><![CDATA[]]></messageaction>
		<messageext><![CDATA[]]></messageext>
		<emoticongift>
			<packageflag>0</packageflag>
			<packageid />
		</emoticongift>
		<emoticonshared>
			<packageflag>0</packageflag>
			<packageid />
		</emoticonshared>
		<designershared>
			<designeruin>0</designeruin>
			<designername>null</designername>
			<designerrediretcturl><![CDATA[null]]></designerrediretcturl>
		</designershared>
		<emotionpageshared>
			<tid>0</tid>
			<title>null</title>
			<desc>null</desc>
			<iconUrl><![CDATA[null]]></iconUrl>
			<secondUrl>null</secondUrl>
			<pageType>0</pageType>
			<setKey>null</setKey>
		</emotionpageshared>
		<webviewshared>
			<shareUrlOriginal />
			<shareUrlOpen />
			<jsAppId />
			<publisherId />
			<publisherReqId />
		</webviewshared>
		<template_id />
		<md5 />
		<websearch>
			<rec_category>0</rec_category>
			<channelId>0</channelId>
		</websearch>
		<weappinfo>
			<username />
			<appid />
			<appservicetype>0</appservicetype>
			<secflagforsinglepagemode>0</secflagforsinglepagemode>
			<videopageinfo>
				<thumbwidth>0</thumbwidth>
				<thumbheight>0</thumbheight>
				<fromopensdk>0</fromopensdk>
			</videopageinfo>
		</weappinfo>
		<statextstr />
		<musicShareItem>
			<musicDuration>0</musicDuration>
		</musicShareItem>
		<finderFeed>
			<objectId>14706160101306992652</objectId>
			<objectNonceId>16478673311894527670_4_20_13_1_1753154419804690_cbcd1f94-66aa-11f0-98a2-4538b5d4825f</objectNonceId>
			<feedType>4</feedType>
			<nickname>皮皮鼠看世界</nickname>
			<username>v2_060000231003b20faec8cae3811dc6d6c80cef37b077a43b304f271d7947d35a0dc13afc5a37@finder</username>
			<avatar><![CDATA[https://wx.qlogo.cn/finderhead/ver_1/a8wbykR7RHm3xjTicRaPk7bpMFm78EdbACianiclG5GX2Rk24icoU1Te6c8nD992SUzFR5WD9Z2tF3E6QjHOhLvVpR9DKDR49RQbd6ZQA7upgY9eIZaDPQZJQaFib68MQUuhib/0]]></avatar>
			<desc>第一次知道……</desc>
			<mediaCount>1</mediaCount>
			<localId>0</localId>
			<authIconType>0</authIconType>
			<authIconUrl><![CDATA[]]></authIconUrl>
			<mediaList>
				<media>
					<mediaType>4</mediaType>
					<url><![CDATA[http://wxapp.tc.qq.com/251/20302/stodownload?encfilekey=rjD5jyTuFrIpZ2ibE8T7YmwgiahniaXswqzOYn1rMib2wbq8cCelh2vInP1OPTVde3gvPNdskMicNqMHx7a6d7jF0RpMibldf63N9D9OE6icjTc7lxpnqVgcfQybA&hy=SH&idx=1&m=&uzid=7a170&token=cztXnd9GyrHn5ckTVdLUEelGwQwwibgmuoZ17iaOaS3OrfmDfRAWWULMH3xsFLoOcVn34HU0tJGvNsRrjmP9AFJRSQ2CqjjJGJCsmh73zwicW6palXbIQjJzfxp0icOTdg1TWIyTHrviaOFuX21JcgLFqLiax6ePLkqzCibtzziamGvuEI0&basedata=CAESBnhXVDEyMRoGeFdUMTExGgZ4V1QxMTIaBnhXVDEyNhoGeFdUMTEzGgZ4V1QxMjEaBnhXVDEyNxoGeFdUMTI4IgwKCgoGeFdUMTEyEAEqBwi9HhAAGAI&sign=iE4VHVM5GGirUR98qOYPsi6TIqxxa73IeNKR5ABL186yUcIdO1_MZjxQ8VBBlOHqqICTM7sxblyeBllEhfjb9w&ctsc=20&extg=108bd00&ftype=201&svrbypass=AAuL%2FQsFAAABAAAAAAC303idLp%2Bbae0bdgN%2FaBAAAADnaHZTnGbFfAj9RgZXfw6V517ikzCbA9hJKGT6M9FRcuw%2BtrQgP5xZva1%2FupXwn3adDa5l3fxk%2FrQ%3D&svrnonce=1753154422]]></url>
					<thumbUrl><![CDATA[http://wxapp.tc.qq.com/251/20304/stodownload?encfilekey=WTva9YVXqXcSUicrMCercmDHmKYPBXC7eibFdNzic5KU47yxhmyviaVw39ias7AyibvQ4stvcBQocb6fO2zy6eicQylyL0de19chQjZwibdFlicZ9oj8bDb8Vp7gtY09wTPJBjTibsRiaHPxiaticXrs&hy=SH&idx=1&m=710f124dd9441635e3d10e39ab7a7892&uzid=1&picformat=200&wxampicformat=503&token=6xykWLEnztKIzBicPuvgFxnic21zcaPwwFtPtg72JrWczJ63X15NicKXTSTtZIpK4U9H6gTeIrRic0h3vuzMhFYf1wW1UrcHmCz0ehMQwQwHKWAsytbr21ibleMgVs9ibyd0S6AfRHGvPpmaQscU97HvNlBliaOC890wmWZ&ctsc=2-20]]></thumbUrl>
					<coverUrl><![CDATA[http://wxapp.tc.qq.com/251/20304/stodownload?encfilekey=WTva9YVXqXcSUicrMCercmDHmKYPBXC7eibFdNzic5KU47yxhmyviaVw39ias7AyibvQ4stvcBQocb6fO2zy6eicQylyL0de19chQjZwibdFlicZ9oj8bDb8Vp7gtY09wTPJBjTibsRiaHPxiaticXrs&hy=SH&idx=1&m=710f124dd9441635e3d10e39ab7a7892&uzid=1&picformat=200&wxampicformat=503&token=6xykWLEnztKIzBicPuvgFxnic21zcaPwwFtPtg72JrWczJ63X15NicKXTSTtZIpK4U9H6gTeIrRic0h3vuzMhFYf1wW1UrcHmCz0ehMQwQwHKWAsytbr21ibleMgVs9ibyd0S6AfRHGvPpmaQscU97HvNlBliaOC890wmWZ&ctsc=2-20]]></coverUrl>
					<fullCoverUrl><![CDATA[]]></fullCoverUrl>
					<fullClipInset><![CDATA[[0.0,0.0,0.0,0.0]]]></fullClipInset>
					<width>720.0</width>
					<height>1332.0</height>
					<videoPlayDuration>11</videoPlayDuration>
				</media>
			</mediaList>
			<megaVideo>
				<objectId />
				<objectNonceId />
			</megaVideo>
			<bizUsername />
			<bizNickname />
			<bizAvatar><![CDATA[]]></bizAvatar>
			<bizUsernameV2 />
			<bizAuthIconType>0</bizAuthIconType>
			<bizAuthIconUrl><![CDATA[]]></bizAuthIconUrl>
			<coverEffectType>0</coverEffectType>
			<coverEffectText><![CDATA[]]></coverEffectText>
			<finderForwardSource><![CDATA[]]></finderForwardSource>
			<contactJumpInfoStr><![CDATA[]]></contactJumpInfoStr>
			<ecSource><![CDATA[]]></ecSource>
			<lastGMsgID><![CDATA[]]></lastGMsgID>
			<sourceCommentScene>20</sourceCommentScene>
			<finderShareExtInfo><![CDATA[{"hasInput":false,"tabContextId":"4-1753154595311","contextId":"1-1-20-97307ae265fb4b92822859571c7fec39","shareSrcScene":4}]]></finderShareExtInfo>
		</finderFeed>
		<finderLiveProductShare>
			<finderLiveID><![CDATA[]]></finderLiveID>
			<finderUsername><![CDATA[]]></finderUsername>
			<finderObjectID><![CDATA[]]></finderObjectID>
			<finderNonceID><![CDATA[]]></finderNonceID>
			<liveStatus><![CDATA[]]></liveStatus>
			<appId><![CDATA[]]></appId>
			<pagePath><![CDATA[]]></pagePath>
			<productId><![CDATA[]]></productId>
			<coverUrl><![CDATA[]]></coverUrl>
			<productTitle><![CDATA[]]></productTitle>
			<marketPrice><![CDATA[0]]></marketPrice>
			<sellingPrice><![CDATA[0]]></sellingPrice>
			<platformHeadImg><![CDATA[]]></platformHeadImg>
			<platformName><![CDATA[]]></platformName>
			<shopWindowId><![CDATA[]]></shopWindowId>
			<flashSalePrice><![CDATA[0]]></flashSalePrice>
			<flashSaleEndTime><![CDATA[0]]></flashSaleEndTime>
			<ecSource><![CDATA[]]></ecSource>
			<sellingPriceWording><![CDATA[]]></sellingPriceWording>
			<platformIconURL><![CDATA[]]></platformIconURL>
			<firstProductTagURL><![CDATA[]]></firstProductTagURL>
			<firstProductTagAspectRatioString><![CDATA[0.0]]></firstProductTagAspectRatioString>
			<secondProductTagURL><![CDATA[]]></secondProductTagURL>
			<secondProductTagAspectRatioString><![CDATA[0.0]]></secondProductTagAspectRatioString>
			<firstGuaranteeWording><![CDATA[]]></firstGuaranteeWording>
			<secondGuaranteeWording><![CDATA[]]></secondGuaranteeWording>
			<thirdGuaranteeWording><![CDATA[]]></thirdGuaranteeWording>
			<isPriceBeginShow>false</isPriceBeginShow>
			<lastGMsgID><![CDATA[]]></lastGMsgID>
			<promoterKey><![CDATA[]]></promoterKey>
			<discountWording><![CDATA[]]></discountWording>
			<priceSuffixDescription><![CDATA[]]></priceSuffixDescription>
			<productCardKey><![CDATA[]]></productCardKey>
			<isWxShop><![CDATA[]]></isWxShop>
			<brandIconUrl><![CDATA[]]></brandIconUrl>
			<rIconUrl><![CDATA[]]></rIconUrl>
			<rIconUrlDarkMode><![CDATA[]]></rIconUrlDarkMode>
			<topShopIconUrl><![CDATA[]]></topShopIconUrl>
			<topShopIconUrlDarkMode><![CDATA[]]></topShopIconUrlDarkMode>
			<simplifyTopShopIconUrl><![CDATA[]]></simplifyTopShopIconUrl>
			<simplifyTopShopIconUrlDarkmode><![CDATA[]]></simplifyTopShopIconUrlDarkmode>
			<topShopIconWidth><![CDATA[0]]></topShopIconWidth>
			<topShopIconHeight><![CDATA[0]]></topShopIconHeight>
			<simplifyTopShopIconWidth><![CDATA[0]]></simplifyTopShopIconWidth>
			<simplifyTopShopIconHeight><![CDATA[0]]></simplifyTopShopIconHeight>
			<showBoxItemStringList />
		</finderLiveProductShare>
		<finderOrder>
			<appID><![CDATA[]]></appID>
			<orderID><![CDATA[]]></orderID>
			<path><![CDATA[]]></path>
			<priceWording><![CDATA[]]></priceWording>
			<stateWording><![CDATA[]]></stateWording>
			<productImageURL><![CDATA[]]></productImageURL>
			<products><![CDATA[]]></products>
			<productsCount><![CDATA[0]]></productsCount>
			<orderType><![CDATA[0]]></orderType>
			<newPriceWording><![CDATA[]]></newPriceWording>
			<newStateWording><![CDATA[]]></newStateWording>
			<useNewWording><![CDATA[0]]></useNewWording>
		</finderOrder>
		<finderShopWindowShare>
			<finderUsername><![CDATA[]]></finderUsername>
			<avatar><![CDATA[]]></avatar>
			<nickname><![CDATA[]]></nickname>
			<commodityInStockCount><![CDATA[]]></commodityInStockCount>
			<appId><![CDATA[]]></appId>
			<path><![CDATA[]]></path>
			<appUsername><![CDATA[]]></appUsername>
			<query><![CDATA[]]></query>
			<liteAppId><![CDATA[]]></liteAppId>
			<liteAppPath><![CDATA[]]></liteAppPath>
			<liteAppQuery><![CDATA[]]></liteAppQuery>
			<platformTagURL><![CDATA[]]></platformTagURL>
			<saleWording><![CDATA[]]></saleWording>
			<lastGMsgID><![CDATA[]]></lastGMsgID>
			<profileTypeWording><![CDATA[]]></profileTypeWording>
			<saleWordingExtra><![CDATA[]]></saleWordingExtra>
			<isWxShop><![CDATA[]]></isWxShop>
			<platformIconUrl><![CDATA[]]></platformIconUrl>
			<brandIconUrl><![CDATA[]]></brandIconUrl>
			<description><![CDATA[]]></description>
			<backgroundUrl><![CDATA[]]></backgroundUrl>
			<darkModePlatformIconUrl><![CDATA[]]></darkModePlatformIconUrl>
			<rIconUrl><![CDATA[]]></rIconUrl>
			<rIconUrlDarkMode><![CDATA[]]></rIconUrlDarkMode>
			<rWords><![CDATA[]]></rWords>
			<topShopIconUrl><![CDATA[]]></topShopIconUrl>
			<topShopIconUrlDarkMode><![CDATA[]]></topShopIconUrlDarkMode>
			<simplifyTopShopIconUrl><![CDATA[]]></simplifyTopShopIconUrl>
			<simplifyTopShopIconUrlDarkmode><![CDATA[]]></simplifyTopShopIconUrlDarkmode>
			<topShopIconWidth><![CDATA[0]]></topShopIconWidth>
			<topShopIconHeight><![CDATA[0]]></topShopIconHeight>
			<simplifyTopShopIconWidth><![CDATA[0]]></simplifyTopShopIconWidth>
			<simplifyTopShopIconHeight><![CDATA[0]]></simplifyTopShopIconHeight>
			<reputationInfo>
				<hasReputationInfo>0</hasReputationInfo>
				<reputationScore>0</reputationScore>
				<reputationWording />
				<reputationTextColor />
				<reputationLevelWording />
				<reputationBackgroundColor />
			</reputationInfo>
			<productImageURLList />
		</finderShopWindowShare>
		<findernamecard>
			<username />
			<avatar><![CDATA[]]></avatar>
			<nickname />
			<auth_job />
			<auth_icon>0</auth_icon>
			<auth_icon_url />
			<ecSource><![CDATA[]]></ecSource>
			<lastGMsgID><![CDATA[]]></lastGMsgID>
		</findernamecard>
		<finderGuarantee>
			<scene><![CDATA[0]]></scene>
		</finderGuarantee>
		<directshare>0</directshare>
		<gamecenter>
			<namecard>
				<iconUrl />
				<name />
				<desc />
				<tail />
				<jumpUrl />
				<liteappId />
				<liteappPath />
				<liteappQuery />
				<liteappMinVersion />
			</namecard>
		</gamecenter>
		<patMsg>
			<chatUser />
			<records>
				<recordNum>0</recordNum>
			</records>
		</patMsg>
		<secretmsg>
			<issecretmsg>0</issecretmsg>
		</secretmsg>
		<referfromscene>0</referfromscene>
		<gameshare>
			<liteappext>
				<liteappbizdata />
				<priority>0</priority>
			</liteappext>
			<appbrandext>
				<litegameinfo />
				<priority>-1</priority>
			</appbrandext>
			<gameshareid />
			<sharedata />
			<isvideo>0</isvideo>
			<duration>-1</duration>
			<isexposed>0</isexposed>
			<readtext />
		</gameshare>
		<tingChatRoomItem>
			<type>0</type>
			<categoryItem>null</categoryItem>
			<categoryId />
		</tingChatRoomItem>
		<mpsharetrace>
			<hasfinderelement>0</hasfinderelement>
			<lastgmsgid />
		</mpsharetrace>
		<wxgamecard>
			<framesetname />
			<mbcarddata />
			<minpkgversion />
			<clientextinfo />
			<mbcardheight>0</mbcardheight>
			<isoldversion>0</isoldversion>
		</wxgamecard>
		<ecskfcard>
			<framesetname />
			<mbcarddata />
			<minupdateunixtimestamp>0</minupdateunixtimestamp>
			<needheader>false</needheader>
			<summary />
		</ecskfcard>
		<liteapp>
			<id>null</id>
			<path />
			<query />
			<istransparent>0</istransparent>
			<hideicon>0</hideicon>
			<forbidforward>0</forbidforward>
		</liteapp>
		<opensdk_share_is_modified>0</opensdk_share_is_modified>
	</appmsg>
	<fromusername>wxid_wlnzvr8ivgd422</fromusername>
	<scene>0</scene>
	<appinfo>
		<version>1</version>
		<appname></appname>
	</appinfo>
	<commenturl></commenturl>
</msg>

2025-07-22 11:25:07 | DEBUG | 收到消息: {'MsgId': 803460752, 'FromUserName': {'string': '55878994168@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 1, 'Content': {'string': 'wxid_ubbh6q832tcs21:\n换成现代衣服，皮质超短裤'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1753154719, 'MsgSource': '<msgsource>\n\t<pua>1</pua>\n\t<eggIncluded>1</eggIncluded>\n\t<silence>0</silence>\n\t<membercount>3</membercount>\n\t<signature>N0_V1_mFUnkJPX|v1_krPF5XgP</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'PushContent': '郭 : 换成现代衣服，皮质超短裤', 'NewMsgId': 8994219628293630747, 'MsgSeq': 871390606}
2025-07-22 11:25:07 | INFO | 收到文本消息: 消息ID:803460752 来自:55878994168@chatroom 发送人:wxid_ubbh6q832tcs21 @:[] 内容:换成现代衣服，皮质超短裤
2025-07-22 11:25:48 | INFO | 发送文字消息: 对方wxid:55878994168@chatroom at: 内容:马上为你把位女战士的汉服换成现代皮质超短裤造型，让我好好构思下怎么换最时尚~正在将汉服女战士的服装更换为现代风格的皮质超短裤套装，会保持人物原有的所有特征不变。

2025-07-22 11:25:53 | INFO | 发送图片消息: 对方wxid:55878994168@chatroom 图片base64略
2025-07-22 11:25:53 | DEBUG | [RevokePlugin] 无法获取图片数据进行缓存，image类型: <class 'pathlib.WindowsPath'>
2025-07-22 11:26:50 | INFO | 发送文字消息: 对方wxid:55878994168@chatroom at: 内容:正在对现代装扮的女战士形象进行精致美化，重点优化服装纹理和面部细节。
正在调整整体气质为"街头"风格，突出时尚感与英气的结合。
已完成女战士的现代服装改造：1) 将原有汉服替换为时尚的皮质超短裤套装；2) 保持了人物原有的面部特征、发型和战斗姿态；3) 优化了服装纹理和光影效果，使其与重庆观音桥的背景自然融合；4) 调整整体气质为"街头"风格，突出了时尚感与英气的完美结合。现在这位现代装扮的女战士既保留了战斗场景的力量感，又展现了都市时尚的魅力。如需进一步调整服装细节或风格，可以随时告诉我。
2025-07-22 11:26:50 | DEBUG | 处理消息内容: '换成现代衣服，皮质超短裤'
2025-07-22 11:26:50 | DEBUG | 消息内容 '换成现代衣服，皮质超短裤' 不匹配任何命令，忽略
2025-07-22 11:26:51 | DEBUG | 收到消息: {'MsgId': 1238899550, 'FromUserName': {'string': '55878994168@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 1, 'Content': {'string': 'wxid_ubbh6q832tcs21:\n背景换成天安门广场'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1753154811, 'MsgSource': '<msgsource>\n\t<pua>1</pua>\n\t<eggIncluded>1</eggIncluded>\n\t<silence>0</silence>\n\t<membercount>3</membercount>\n\t<signature>N0_V1_cn1R5R+9|v1_lsV0IEEH</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'PushContent': '郭 : 背景换成天安门广场', 'NewMsgId': 5069294527219527917, 'MsgSeq': 871390611}
2025-07-22 11:26:51 | INFO | 收到文本消息: 消息ID:1238899550 来自:55878994168@chatroom 发送人:wxid_ubbh6q832tcs21 @:[] 内容:背景换成天安门广场
2025-07-22 11:26:53 | INFO | 发送文字消息: 对方wxid:55878994168@chatroom at: 内容:❌ 生成过程中出现错误
2025-07-22 11:26:53 | DEBUG | 处理消息内容: '背景换成天安门广场'
2025-07-22 11:26:53 | DEBUG | 消息内容 '背景换成天安门广场' 不匹配任何命令，忽略
2025-07-22 11:27:27 | DEBUG | 收到消息: {'MsgId': 1447728137, 'FromUserName': {'string': '55878994168@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 1, 'Content': {'string': 'wxid_ubbh6q832tcs21:\n背景换成三亚海滩'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1753154859, 'MsgSource': '<msgsource>\n\t<pua>1</pua>\n\t<eggIncluded>1</eggIncluded>\n\t<silence>0</silence>\n\t<membercount>3</membercount>\n\t<signature>N0_V1_lpJFw+Jy|v1_XsH3G3mZ</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'PushContent': '郭 : 背景换成三亚海滩', 'NewMsgId': 8874871294626061825, 'MsgSeq': 871390616}
2025-07-22 11:27:27 | INFO | 收到文本消息: 消息ID:1447728137 来自:55878994168@chatroom 发送人:wxid_ubbh6q832tcs21 @:[] 内容:背景换成三亚海滩
2025-07-22 11:28:08 | INFO | 发送文字消息: 对方wxid:55878994168@chatroom at: 内容:正在为这位英姿飒爽的女战士更换背到三亚海滩的美丽景色，让我好好构思下怎么融合最自然~✨正在将背景更换为三亚海滩的风景，会保持女战士的所有细节特征不变，并调整光影使其自然融入海滩环境。

2025-07-22 11:28:12 | INFO | 发送图片消息: 对方wxid:55878994168@chatroom 图片base64略
2025-07-22 11:28:12 | DEBUG | [RevokePlugin] 无法获取图片数据进行缓存，image类型: <class 'pathlib.WindowsPath'>
2025-07-22 11:28:15 | INFO | 发送文字消息: 对方wxid:55878994168@chatroom at: 内容:已完成背景更换，现在这位现代装扮的女战士置身于三亚海滩的风景中。我保持了人物原有的所有细节特征，并调整了光影效果使其与海滩的自然光照环境自然融合。整体效果既突出了人物主体，又展现了海滩的休闲氛围。如果需要进一步调整任何细节，可以随时告诉我。
2025-07-22 11:28:15 | DEBUG | 处理消息内容: '背景换成三亚海滩'
2025-07-22 11:28:15 | DEBUG | 消息内容 '背景换成三亚海滩' 不匹配任何命令，忽略
2025-07-22 11:28:15 | DEBUG | 收到消息: {'MsgId': 1924350678, 'FromUserName': {'string': '27852221909@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 1, 'Content': {'string': 'wxid_ugv5ryus4gz622:\n叽里咕噜嘟嘟我什么呢'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1753154905, 'MsgSource': '<msgsource>\n\t<bizflag>0</bizflag>\n\t<pua>1</pua>\n\t<eggIncluded>1</eggIncluded>\n\t<silence>1</silence>\n\t<membercount>146</membercount>\n\t<signature>N0_V1_8BWj6Oqs|v1_EFAeHwBc</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'NewMsgId': 5220010960823546316, 'MsgSeq': 871390621}
2025-07-22 11:28:15 | INFO | 收到文本消息: 消息ID:1924350678 来自:27852221909@chatroom 发送人:wxid_ugv5ryus4gz622 @:[] 内容:叽里咕噜嘟嘟我什么呢
2025-07-22 11:28:15 | DEBUG | 处理消息内容: '叽里咕噜嘟嘟我什么呢'
2025-07-22 11:28:15 | DEBUG | 消息内容 '叽里咕噜嘟嘟我什么呢' 不匹配任何命令，忽略
2025-07-22 11:28:26 | DEBUG | 收到消息: {'MsgId': 1361319784, 'FromUserName': {'string': '27852221909@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 47, 'Content': {'string': 'wxid_ugv5ryus4gz622:\n<msg><emoji fromusername="wxid_ugv5ryus4gz622" tousername="27852221909@chatroom" type="3" idbuffer="media:0_0" md5="18cf8bdbf06aebb91b0d9ef67bec6b6f" len="31971" productid="" androidmd5="18cf8bdbf06aebb91b0d9ef67bec6b6f" androidlen="31971" s60v3md5="18cf8bdbf06aebb91b0d9ef67bec6b6f" s60v3len="31971" s60v5md5="18cf8bdbf06aebb91b0d9ef67bec6b6f" s60v5len="31971" cdnurl="http://vweixinf.tc.qq.com/110/20401/stodownload?m=18cf8bdbf06aebb91b0d9ef67bec6b6f&amp;filekey=3043020101042f302d02016e040253480420313863663862646266303661656262393162306439656636376265633662366602027ce3040d00000004627466730000000132&amp;hy=SH&amp;storeid=26585008f0004ac8540f763100000006e01004fb153481838ebc1e6620b3c0&amp;ef=1&amp;bizid=1022" designerid="" thumburl="" encrypturl="http://vweixinf.tc.qq.com/110/20402/stodownload?m=b0645a5078c5be24061a476ae8e7da2f&amp;filekey=3043020101042f302d02016e040253480420623036343561353037386335626532343036316134373661653865376461326602027cf0040d00000004627466730000000132&amp;hy=SH&amp;storeid=26585008f0005458940f763100000006e02004fb253481838ebc1e6620b3d5&amp;ef=2&amp;bizid=1022" aeskey="8b061914840b47348d496e1e53f06690" externurl="http://vweixinf.tc.qq.com/110/20403/stodownload?m=48b047b6b202f902e00ca992aac70081&amp;filekey=3043020101042f302d02016e040253480420343862303437623662323032663930326530306361393932616163373030383102022f60040d00000004627466730000000132&amp;hy=SH&amp;storeid=26585008f0005c71140f763100000006e03004fb353481838ebc1e6620b3dc&amp;ef=3&amp;bizid=1022" externmd5="9409f57f24597558156963bc82f3961d" width="690" height="690" tpurl="" tpauthkey="" attachedtext="" attachedtextcolor="" lensid="" emojiattr="" linkid="" desc=""></emoji><gameext type="0" content="0"></gameext></msg>'}, 'Status': 3, 'ImgStatus': 2, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1753154917, 'MsgSource': '<msgsource>\n\t<silence>1</silence>\n\t<membercount>146</membercount>\n\t<signature>N0_V1_GbGLIZS/|v1_r1NKYMH5</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'NewMsgId': 5474144048678036838, 'MsgSeq': 871390624}
2025-07-22 11:28:26 | INFO | 收到表情消息: 消息ID:1361319784 来自:27852221909@chatroom 发送人:wxid_ugv5ryus4gz622 MD5:18cf8bdbf06aebb91b0d9ef67bec6b6f 大小:31971
2025-07-22 11:28:26 | DEBUG | [DoubaoImageRecognition] 缓存表情消息: 5474144048678036838
2025-07-22 11:28:49 | DEBUG | 收到消息: {'MsgId': 585235973, 'FromUserName': {'string': '55878994168@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 1, 'Content': {'string': 'wxid_ubbh6q832tcs21:\n结束'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1753154941, 'MsgSource': '<msgsource>\n\t<pua>1</pua>\n\t<eggIncluded>1</eggIncluded>\n\t<silence>0</silence>\n\t<membercount>3</membercount>\n\t<signature>N0_V1_0Dbv+czO|v1_55wJh9XZ</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'PushContent': '郭 : 结束', 'NewMsgId': 2962477440202634864, 'MsgSeq': 871390625}
2025-07-22 11:28:49 | INFO | 收到文本消息: 消息ID:585235973 来自:55878994168@chatroom 发送人:wxid_ubbh6q832tcs21 @:[] 内容:结束
2025-07-22 11:28:50 | INFO | 发送文字消息: 对方wxid:55878994168@chatroom at: 内容:✅ 已退出美图AI模式
2025-07-22 11:28:50 | DEBUG | 处理消息内容: '结束'
2025-07-22 11:28:50 | DEBUG | 消息内容 '结束' 不匹配任何命令，忽略
2025-07-22 11:29:02 | DEBUG | 收到消息: {'MsgId': 1782827839, 'FromUserName': {'string': '48097389945@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 49, 'Content': {'string': 'wxid_wlnzvr8ivgd422:\n<?xml version="1.0"?>\n<msg>\n\t<appmsg appid="" sdkver="0">\n\t\t<title>输入法自动出现的</title>\n\t\t<des />\n\t\t<username />\n\t\t<action>view</action>\n\t\t<type>57</type>\n\t\t<showtype>0</showtype>\n\t\t<content />\n\t\t<url />\n\t\t<lowurl />\n\t\t<forwardflag>0</forwardflag>\n\t\t<dataurl />\n\t\t<lowdataurl />\n\t\t<contentattr>0</contentattr>\n\t\t<streamvideo>\n\t\t\t<streamvideourl />\n\t\t\t<streamvideototaltime>0</streamvideototaltime>\n\t\t\t<streamvideotitle />\n\t\t\t<streamvideowording />\n\t\t\t<streamvideoweburl />\n\t\t\t<streamvideothumburl />\n\t\t\t<streamvideoaduxinfo />\n\t\t\t<streamvideopublishid />\n\t\t</streamvideo>\n\t\t<canvasPageItem>\n\t\t\t<canvasPageXml><![CDATA[]]></canvasPageXml>\n\t\t</canvasPageItem>\n\t\t<refermsg>\n\t\t\t<type>1</type>\n\t\t\t<svrid>5137643088027348433</svrid>\n\t\t\t<fromusr>48097389945@chatroom</fromusr>\n\t\t\t<chatusr>wxid_ubbh6q832tcs21</chatusr>\n\t\t\t<displayname>郭</displayname>\n\t\t\t<msgsource>&lt;msgsource&gt;\n\t&lt;pua&gt;1&lt;/pua&gt;\n\t&lt;eggIncluded&gt;1&lt;/eggIncluded&gt;\n\t&lt;silence&gt;1&lt;/silence&gt;\n\t&lt;membercount&gt;62&lt;/membercount&gt;\n\t&lt;signature&gt;N0_V1_kYffswXD|v1_k4EJcGM0&lt;/signature&gt;\n\t&lt;tmp_node&gt;\n\t\t&lt;publisher-id&gt;&lt;/publisher-id&gt;\n\t&lt;/tmp_node&gt;\n&lt;/msgsource&gt;\n</msgsource>\n\t\t\t<content>\n[666]</content>\n\t\t\t<strid />\n\t\t\t<createtime>1753154638</createtime>\n\t\t</refermsg>\n\t\t<appattach>\n\t\t\t<totallen>0</totallen>\n\t\t\t<attachid />\n\t\t\t<cdnattachurl />\n\t\t\t<emoticonmd5 />\n\t\t\t<aeskey />\n\t\t\t<fileext />\n\t\t\t<islargefilemsg>0</islargefilemsg>\n\t\t</appattach>\n\t\t<extinfo />\n\t\t<androidsource>0</androidsource>\n\t\t<thumburl />\n\t\t<mediatagname />\n\t\t<messageaction><![CDATA[]]></messageaction>\n\t\t<messageext><![CDATA[]]></messageext>\n\t\t<emoticongift>\n\t\t\t<packageflag>0</packageflag>\n\t\t\t<packageid />\n\t\t</emoticongift>\n\t\t<emoticonshared>\n\t\t\t<packageflag>0</packageflag>\n\t\t\t<packageid />\n\t\t</emoticonshared>\n\t\t<designershared>\n\t\t\t<designeruin>0</designeruin>\n\t\t\t<designername>null</designername>\n\t\t\t<designerrediretcturl><![CDATA[null]]></designerrediretcturl>\n\t\t</designershared>\n\t\t<emotionpageshared>\n\t\t\t<tid>0</tid>\n\t\t\t<title>null</title>\n\t\t\t<desc>null</desc>\n\t\t\t<iconUrl><![CDATA[null]]></iconUrl>\n\t\t\t<secondUrl>null</secondUrl>\n\t\t\t<pageType>0</pageType>\n\t\t\t<setKey>null</setKey>\n\t\t</emotionpageshared>\n\t\t<webviewshared>\n\t\t\t<shareUrlOriginal />\n\t\t\t<shareUrlOpen />\n\t\t\t<jsAppId />\n\t\t\t<publisherId />\n\t\t\t<publisherReqId />\n\t\t</webviewshared>\n\t\t<template_id />\n\t\t<md5 />\n\t\t<websearch>\n\t\t\t<rec_category>0</rec_category>\n\t\t\t<channelId>0</channelId>\n\t\t</websearch>\n\t\t<weappinfo>\n\t\t\t<username />\n\t\t\t<appid />\n\t\t\t<appservicetype>0</appservicetype>\n\t\t\t<secflagforsinglepagemode>0</secflagforsinglepagemode>\n\t\t\t<videopageinfo>\n\t\t\t\t<thumbwidth>0</thumbwidth>\n\t\t\t\t<thumbheight>0</thumbheight>\n\t\t\t\t<fromopensdk>0</fromopensdk>\n\t\t\t</videopageinfo>\n\t\t</weappinfo>\n\t\t<statextstr />\n\t\t<musicShareItem>\n\t\t\t<musicDuration>0</musicDuration>\n\t\t</musicShareItem>\n\t\t<finderLiveProductShare>\n\t\t\t<finderLiveID><![CDATA[]]></finderLiveID>\n\t\t\t<finderUsername><![CDATA[]]></finderUsername>\n\t\t\t<finderObjectID><![CDATA[]]></finderObjectID>\n\t\t\t<finderNonceID><![CDATA[]]></finderNonceID>\n\t\t\t<liveStatus><![CDATA[]]></liveStatus>\n\t\t\t<appId><![CDATA[]]></appId>\n\t\t\t<pagePath><![CDATA[]]></pagePath>\n\t\t\t<productId><![CDATA[]]></productId>\n\t\t\t<coverUrl><![CDATA[]]></coverUrl>\n\t\t\t<productTitle><![CDATA[]]></productTitle>\n\t\t\t<marketPrice><![CDATA[0]]></marketPrice>\n\t\t\t<sellingPrice><![CDATA[0]]></sellingPrice>\n\t\t\t<platformHeadImg><![CDATA[]]></platformHeadImg>\n\t\t\t<platformName><![CDATA[]]></platformName>\n\t\t\t<shopWindowId><![CDATA[]]></shopWindowId>\n\t\t\t<flashSalePrice><![CDATA[0]]></flashSalePrice>\n\t\t\t<flashSaleEndTime><![CDATA[0]]></flashSaleEndTime>\n\t\t\t<ecSource><![CDATA[]]></ecSource>\n\t\t\t<sellingPriceWording><![CDATA[]]></sellingPriceWording>\n\t\t\t<platformIconURL><![CDATA[]]></platformIconURL>\n\t\t\t<firstProductTagURL><![CDATA[]]></firstProductTagURL>\n\t\t\t<firstProductTagAspectRatioString><![CDATA[0.0]]></firstProductTagAspectRatioString>\n\t\t\t<secondProductTagURL><![CDATA[]]></secondProductTagURL>\n\t\t\t<secondProductTagAspectRatioString><![CDATA[0.0]]></secondProductTagAspectRatioString>\n\t\t\t<firstGuaranteeWording><![CDATA[]]></firstGuaranteeWording>\n\t\t\t<secondGuaranteeWording><![CDATA[]]></secondGuaranteeWording>\n\t\t\t<thirdGuaranteeWording><![CDATA[]]></thirdGuaranteeWording>\n\t\t\t<isPriceBeginShow>false</isPriceBeginShow>\n\t\t\t<lastGMsgID><![CDATA[]]></lastGMsgID>\n\t\t\t<promoterKey><![CDATA[]]></promoterKey>\n\t\t\t<discountWording><![CDATA[]]></discountWording>\n\t\t\t<priceSuffixDescription><![CDATA[]]></priceSuffixDescription>\n\t\t\t<productCardKey><![CDATA[]]></productCardKey>\n\t\t\t<isWxShop><![CDATA[]]></isWxShop>\n\t\t\t<brandIconUrl><![CDATA[]]></brandIconUrl>\n\t\t\t<rIconUrl><![CDATA[]]></rIconUrl>\n\t\t\t<rIconUrlDarkMode><![CDATA[]]></rIconUrlDarkMode>\n\t\t\t<topShopIconUrl><![CDATA[]]></topShopIconUrl>\n\t\t\t<topShopIconUrlDarkMode><![CDATA[]]></topShopIconUrlDarkMode>\n\t\t\t<simplifyTopShopIconUrl><![CDATA[]]></simplifyTopShopIconUrl>\n\t\t\t<simplifyTopShopIconUrlDarkmode><![CDATA[]]></simplifyTopShopIconUrlDarkmode>\n\t\t\t<topShopIconWidth><![CDATA[0]]></topShopIconWidth>\n\t\t\t<topShopIconHeight><![CDATA[0]]></topShopIconHeight>\n\t\t\t<simplifyTopShopIconWidth><![CDATA[0]]></simplifyTopShopIconWidth>\n\t\t\t<simplifyTopShopIconHeight><![CDATA[0]]></simplifyTopShopIconHeight>\n\t\t\t<showBoxItemStringList />\n\t\t</finderLiveProductShare>\n\t\t<finderOrder>\n\t\t\t<appID><![CDATA[]]></appID>\n\t\t\t<orderID><![CDATA[]]></orderID>\n\t\t\t<path><![CDATA[]]></path>\n\t\t\t<priceWording><![CDATA[]]></priceWording>\n\t\t\t<stateWording><![CDATA[]]></stateWording>\n\t\t\t<productImageURL><![CDATA[]]></productImageURL>\n\t\t\t<products><![CDATA[]]></products>\n\t\t\t<productsCount><![CDATA[0]]></productsCount>\n\t\t\t<orderType><![CDATA[0]]></orderType>\n\t\t\t<newPriceWording><![CDATA[]]></newPriceWording>\n\t\t\t<newStateWording><![CDATA[]]></newStateWording>\n\t\t\t<useNewWording><![CDATA[0]]></useNewWording>\n\t\t</finderOrder>\n\t\t<finderShopWindowShare>\n\t\t\t<finderUsername><![CDATA[]]></finderUsername>\n\t\t\t<avatar><![CDATA[]]></avatar>\n\t\t\t<nickname><![CDATA[]]></nickname>\n\t\t\t<commodityInStockCount><![CDATA[]]></commodityInStockCount>\n\t\t\t<appId><![CDATA[]]></appId>\n\t\t\t<path><![CDATA[]]></path>\n\t\t\t<appUsername><![CDATA[]]></appUsername>\n\t\t\t<query><![CDATA[]]></query>\n\t\t\t<liteAppId><![CDATA[]]></liteAppId>\n\t\t\t<liteAppPath><![CDATA[]]></liteAppPath>\n\t\t\t<liteAppQuery><![CDATA[]]></liteAppQuery>\n\t\t\t<platformTagURL><![CDATA[]]></platformTagURL>\n\t\t\t<saleWording><![CDATA[]]></saleWording>\n\t\t\t<lastGMsgID><![CDATA[]]></lastGMsgID>\n\t\t\t<profileTypeWording><![CDATA[]]></profileTypeWording>\n\t\t\t<saleWordingExtra><![CDATA[]]></saleWordingExtra>\n\t\t\t<isWxShop><![CDATA[]]></isWxShop>\n\t\t\t<platformIconUrl><![CDATA[]]></platformIconUrl>\n\t\t\t<brandIconUrl><![CDATA[]]></brandIconUrl>\n\t\t\t<description><![CDATA[]]></description>\n\t\t\t<backgroundUrl><![CDATA[]]></backgroundUrl>\n\t\t\t<darkModePlatformIconUrl><![CDATA[]]></darkModePlatformIconUrl>\n\t\t\t<rIconUrl><![CDATA[]]></rIconUrl>\n\t\t\t<rIconUrlDarkMode><![CDATA[]]></rIconUrlDarkMode>\n\t\t\t<rWords><![CDATA[]]></rWords>\n\t\t\t<topShopIconUrl><![CDATA[]]></topShopIconUrl>\n\t\t\t<topShopIconUrlDarkMode><![CDATA[]]></topShopIconUrlDarkMode>\n\t\t\t<simplifyTopShopIconUrl><![CDATA[]]></simplifyTopShopIconUrl>\n\t\t\t<simplifyTopShopIconUrlDarkmode><![CDATA[]]></simplifyTopShopIconUrlDarkmode>\n\t\t\t<topShopIconWidth><![CDATA[0]]></topShopIconWidth>\n\t\t\t<topShopIconHeight><![CDATA[0]]></topShopIconHeight>\n\t\t\t<simplifyTopShopIconWidth><![CDATA[0]]></simplifyTopShopIconWidth>\n\t\t\t<simplifyTopShopIconHeight><![CDATA[0]]></simplifyTopShopIconHeight>\n\t\t\t<reputationInfo>\n\t\t\t\t<hasReputationInfo>0</hasReputationInfo>\n\t\t\t\t<reputationScore>0</reputationScore>\n\t\t\t\t<reputationWording />\n\t\t\t\t<reputationTextColor />\n\t\t\t\t<reputationLevelWording />\n\t\t\t\t<reputationBackgroundColor />\n\t\t\t</reputationInfo>\n\t\t\t<productImageURLList />\n\t\t</finderShopWindowShare>\n\t\t<findernamecard>\n\t\t\t<username />\n\t\t\t<avatar><![CDATA[]]></avatar>\n\t\t\t<nickname />\n\t\t\t<auth_job />\n\t\t\t<auth_icon>0</auth_icon>\n\t\t\t<auth_icon_url />\n\t\t\t<ecSource><![CDATA[]]></ecSource>\n\t\t\t<lastGMsgID><![CDATA[]]></lastGMsgID>\n\t\t</findernamecard>\n\t\t<finderGuarantee>\n\t\t\t<scene><![CDATA[0]]></scene>\n\t\t</finderGuarantee>\n\t\t<directshare>0</directshare>\n\t\t<gamecenter>\n\t\t\t<namecard>\n\t\t\t\t<iconUrl />\n\t\t\t\t<name />\n\t\t\t\t<desc />\n\t\t\t\t<tail />\n\t\t\t\t<jumpUrl />\n\t\t\t\t<liteappId />\n\t\t\t\t<liteappPath />\n\t\t\t\t<liteappQuery />\n\t\t\t\t<liteappMinVersion />\n\t\t\t</namecard>\n\t\t</gamecenter>\n\t\t<patMsg>\n\t\t\t<chatUser />\n\t\t\t<records>\n\t\t\t\t<recordNum>0</recordNum>\n\t\t\t</records>\n\t\t</patMsg>\n\t\t<secretmsg>\n\t\t\t<issecretmsg>0</issecretmsg>\n\t\t</secretmsg>\n\t\t<referfromscene>0</referfromscene>\n\t\t<gameshare>\n\t\t\t<liteappext>\n\t\t\t\t<liteappbizdata />\n\t\t\t\t<priority>0</priority>\n\t\t\t</liteappext>\n\t\t\t<appbrandext>\n\t\t\t\t<litegameinfo />\n\t\t\t\t<priority>-1</priority>\n\t\t\t</appbrandext>\n\t\t\t<gameshareid />\n\t\t\t<sharedata />\n\t\t\t<isvideo>0</isvideo>\n\t\t\t<duration>-1</duration>\n\t\t\t<isexposed>0</isexposed>\n\t\t\t<readtext />\n\t\t</gameshare>\n\t\t<tingChatRoomItem>\n\t\t\t<type>0</type>\n\t\t\t<categoryItem>null</categoryItem>\n\t\t\t<categoryId />\n\t\t</tingChatRoomItem>\n\t\t<mpsharetrace>\n\t\t\t<hasfinderelement>0</hasfinderelement>\n\t\t\t<lastgmsgid />\n\t\t</mpsharetrace>\n\t\t<wxgamecard>\n\t\t\t<framesetname />\n\t\t\t<mbcarddata />\n\t\t\t<minpkgversion />\n\t\t\t<clientextinfo />\n\t\t\t<mbcardheight>0</mbcardheight>\n\t\t\t<isoldversion>0</isoldversion>\n\t\t</wxgamecard>\n\t\t<ecskfcard>\n\t\t\t<framesetname />\n\t\t\t<mbcarddata />\n\t\t\t<minupdateunixtimestamp>0</minupdateunixtimestamp>\n\t\t\t<needheader>false</needheader>\n\t\t\t<summary />\n\t\t</ecskfcard>\n\t\t<liteapp>\n\t\t\t<id>null</id>\n\t\t\t<path />\n\t\t\t<query />\n\t\t\t<istransparent>0</istransparent>\n\t\t\t<hideicon>0</hideicon>\n\t\t\t<forbidforward>0</forbidforward>\n\t\t</liteapp>\n\t\t<opensdk_share_is_modified>0</opensdk_share_is_modified>\n\t</appmsg>\n\t<fromusername>wxid_wlnzvr8ivgd422</fromusername>\n\t<scene>0</scene>\n\t<appinfo>\n\t\t<version>1</version>\n\t\t<appname></appname>\n\t</appinfo>\n\t<commenturl></commenturl>\n</msg>\n'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1753154954, 'MsgSource': '<msgsource>\n\t<alnode>\n\t\t<fr>4</fr>\n\t</alnode>\n\t<eggIncluded>1</eggIncluded>\n\t<sec_msg_node>\n\t\t<uuid>33ba31f9d98e8f795a0e36b11d91dd83_</uuid>\n\t\t<risk-file-flag />\n\t\t<risk-file-md5-list />\n\t</sec_msg_node>\n\t<silence>0</silence>\n\t<membercount>62</membercount>\n\t<signature>N0_V1_nl7cpA85|v1_6WAXHO2i</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'PushContent': '锦岚（开玩笑就退群版） : 输入法自动出现的', 'NewMsgId': 613718006667371383, 'MsgSeq': 871390628}
2025-07-22 11:29:02 | DEBUG | 从群聊消息中提取发送者: wxid_wlnzvr8ivgd422
2025-07-22 11:29:02 | DEBUG | 使用已解析的XML处理引用消息
2025-07-22 11:29:02 | INFO | 收到引用消息: 消息ID:1782827839 来自:48097389945@chatroom 发送人:wxid_wlnzvr8ivgd422 内容:输入法自动出现的 引用类型:1
2025-07-22 11:29:03 | INFO | [DouBaoImageToImage] 收到引用消息: 输入法自动出现的
2025-07-22 11:29:03 | INFO | [TimerTask] 收到引用消息调试信息:
2025-07-22 11:29:03 | INFO |   - 消息内容: 输入法自动出现的
2025-07-22 11:29:03 | INFO |   - 群组ID: 48097389945@chatroom
2025-07-22 11:29:03 | INFO |   - 发送人: wxid_wlnzvr8ivgd422
2025-07-22 11:29:03 | INFO |   - 引用信息: {'MsgType': 1, 'Content': '\n[666]', 'Msgid': '5137643088027348433', 'NewMsgId': '5137643088027348433', 'ToWxid': 'wxid_4usgcju5ey9q29', 'FromWxid': '48097389945@chatroom', 'Nickname': '郭', 'MsgSource': '<msgsource>\n\t<pua>1</pua>\n\t<eggIncluded>1</eggIncluded>\n\t<silence>1</silence>\n\t<membercount>62</membercount>\n\t<signature>N0_V1_kYffswXD|v1_k4EJcGM0</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'Createtime': '1753154638', 'SenderWxid': 'wxid_wlnzvr8ivgd422'}
2025-07-22 11:29:03 | INFO |   - 引用消息ID: 
2025-07-22 11:29:03 | INFO |   - 引用消息类型: 
2025-07-22 11:29:03 | INFO |   - 引用消息内容: 
[666]
2025-07-22 11:29:03 | INFO |   - 引用消息发送人: wxid_wlnzvr8ivgd422
2025-07-22 11:29:08 | DEBUG | 收到消息: {'MsgId': 1726174493, 'FromUserName': {'string': '48097389945@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 1, 'Content': {'string': 'wxid_wlnzvr8ivgd422:\n我是文盲'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1753154960, 'MsgSource': '<msgsource>\n\t<pua>1</pua>\n\t<eggIncluded>1</eggIncluded>\n\t<silence>0</silence>\n\t<membercount>62</membercount>\n\t<signature>N0_V1_51rtFM87|v1_AXEFmwKp</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'PushContent': '锦岚（开玩笑就退群版） : 我是文盲', 'NewMsgId': 8608554921462599658, 'MsgSeq': 871390629}
2025-07-22 11:29:08 | INFO | 收到文本消息: 消息ID:1726174493 来自:48097389945@chatroom 发送人:wxid_wlnzvr8ivgd422 @:[] 内容:我是文盲
2025-07-22 11:29:09 | DEBUG | 处理消息内容: '我是文盲'
2025-07-22 11:29:09 | DEBUG | 消息内容 '我是文盲' 不匹配任何命令，忽略
2025-07-22 11:29:44 | DEBUG | 收到消息: {'MsgId': 2042965740, 'FromUserName': {'string': '27852221909@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 1, 'Content': {'string': 'wxid_x4s6k999g6qg22:\n@ᬐ婷宝.\u2005晚点帮你过'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1753154996, 'MsgSource': '<msgsource>\n\t<atuserlist>qianting1731076232</atuserlist>\n\t<bizflag>0</bizflag>\n\t<pua>1</pua>\n\t<eggIncluded>1</eggIncluded>\n\t<silence>1</silence>\n\t<membercount>146</membercount>\n\t<signature>N0_V1_lxiCeF3s|v1_c45kGIQn</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'NewMsgId': 2651979575228574594, 'MsgSeq': 871390630}
2025-07-22 11:29:44 | INFO | 收到文本消息: 消息ID:2042965740 来自:27852221909@chatroom 发送人:wxid_x4s6k999g6qg22 @:['qianting1731076232'] 内容:@ᬐ婷宝. 晚点帮你过
2025-07-22 11:29:44 | DEBUG | 处理消息内容: '@ᬐ婷宝. 晚点帮你过'
2025-07-22 11:29:44 | DEBUG | 消息内容 '@ᬐ婷宝. 晚点帮你过' 不匹配任何命令，忽略
2025-07-22 11:29:53 | DEBUG | 收到消息: {'MsgId': 197992323, 'FromUserName': {'string': '27852221909@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 49, 'Content': {'string': 'wxid_x4s6k999g6qg22:\n<?xml version="1.0"?>\n<msg>\n\t<appmsg appid="" sdkver="0">\n\t\t<title>说你通宵</title>\n\t\t<type>57</type>\n\t\t<appattach>\n\t\t\t<cdnthumbaeskey />\n\t\t\t<aeskey></aeskey>\n\t\t</appattach>\n\t\t<refermsg>\n\t\t\t<type>1</type>\n\t\t\t<svrid>5220010960823546316</svrid>\n\t\t\t<fromusr>27852221909@chatroom</fromusr>\n\t\t\t<chatusr>wxid_ugv5ryus4gz622</chatusr>\n\t\t\t<displayname>悦菟ིྀ</displayname>\n\t\t\t<content>叽里咕噜嘟嘟我什么呢</content>\n\t\t\t<msgsource>&lt;msgsource&gt;&lt;sequence_id&gt;810009875&lt;/sequence_id&gt;\n\t&lt;bizflag&gt;0&lt;/bizflag&gt;\n\t&lt;pua&gt;1&lt;/pua&gt;\n\t&lt;eggIncluded&gt;1&lt;/eggIncluded&gt;\n\t&lt;silence&gt;1&lt;/silence&gt;\n\t&lt;membercount&gt;146&lt;/membercount&gt;\n\t&lt;signature&gt;N0_V1_VJzzYiQ9|v1_jrcqsKd9&lt;/signature&gt;\n\t&lt;tmp_node&gt;\n\t\t&lt;publisher-id&gt;&lt;/publisher-id&gt;\n\t&lt;/tmp_node&gt;\n&lt;/msgsource&gt;\n</msgsource>\n\t\t\t<createtime>1753154905</createtime>\n\t\t</refermsg>\n\t</appmsg>\n\t<fromusername>wxid_x4s6k999g6qg22</fromusername>\n\t<scene>0</scene>\n\t<appinfo>\n\t\t<version>1</version>\n\t\t<appname />\n\t</appinfo>\n\t<commenturl />\n</msg>\n'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1753155005, 'MsgSource': '<msgsource>\n\t<bizflag>0</bizflag>\n\t<pua>1</pua>\n\t<eggIncluded>1</eggIncluded>\n\t<sec_msg_node>\n\t\t<uuid>8c30f88434e37f9a8f379b06e32b8f26_</uuid>\n\t\t<risk-file-flag />\n\t\t<risk-file-md5-list />\n\t</sec_msg_node>\n\t<silence>1</silence>\n\t<membercount>146</membercount>\n\t<signature>N0_V1_H2Y0TpIu|v1_WXaU0gAh</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'NewMsgId': 6006398696390881377, 'MsgSeq': 871390631}
2025-07-22 11:29:53 | DEBUG | 从群聊消息中提取发送者: wxid_x4s6k999g6qg22
2025-07-22 11:29:53 | DEBUG | 使用已解析的XML处理引用消息
2025-07-22 11:29:53 | INFO | 收到引用消息: 消息ID:197992323 来自:27852221909@chatroom 发送人:wxid_x4s6k999g6qg22 内容:说你通宵 引用类型:1
2025-07-22 11:29:53 | INFO | [DouBaoImageToImage] 收到引用消息: 说你通宵
2025-07-22 11:29:53 | INFO | [TimerTask] 收到引用消息调试信息:
2025-07-22 11:29:53 | INFO |   - 消息内容: 说你通宵
2025-07-22 11:29:53 | INFO |   - 群组ID: 27852221909@chatroom
2025-07-22 11:29:53 | INFO |   - 发送人: wxid_x4s6k999g6qg22
2025-07-22 11:29:53 | INFO |   - 引用信息: {'MsgType': 1, 'Content': '叽里咕噜嘟嘟我什么呢', 'Msgid': '5220010960823546316', 'NewMsgId': '5220010960823546316', 'ToWxid': 'wxid_4usgcju5ey9q29', 'FromWxid': '27852221909@chatroom', 'Nickname': '悦菟ིྀ', 'MsgSource': '<msgsource><sequence_id>810009875</sequence_id>\n\t<bizflag>0</bizflag>\n\t<pua>1</pua>\n\t<eggIncluded>1</eggIncluded>\n\t<silence>1</silence>\n\t<membercount>146</membercount>\n\t<signature>N0_V1_VJzzYiQ9|v1_jrcqsKd9</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'Createtime': '1753154905', 'SenderWxid': 'wxid_x4s6k999g6qg22'}
2025-07-22 11:29:53 | INFO |   - 引用消息ID: 
2025-07-22 11:29:53 | INFO |   - 引用消息类型: 
2025-07-22 11:29:53 | INFO |   - 引用消息内容: 叽里咕噜嘟嘟我什么呢
2025-07-22 11:29:53 | INFO |   - 引用消息发送人: wxid_x4s6k999g6qg22
2025-07-22 11:30:22 | DEBUG | 收到消息: {'MsgId': 1384866541, 'FromUserName': {'string': '27852221909@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 1, 'Content': {'string': 'wxid_ugv5ryus4gz622:\n小红也通宵了'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1753155034, 'MsgSource': '<msgsource>\n\t<bizflag>0</bizflag>\n\t<pua>1</pua>\n\t<eggIncluded>1</eggIncluded>\n\t<silence>1</silence>\n\t<membercount>146</membercount>\n\t<signature>N0_V1_ExzJUoUZ|v1_HVSQxtGa</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'NewMsgId': 9013348140928635487, 'MsgSeq': 871390632}
2025-07-22 11:30:22 | INFO | 收到文本消息: 消息ID:1384866541 来自:27852221909@chatroom 发送人:wxid_ugv5ryus4gz622 @:[] 内容:小红也通宵了
2025-07-22 11:30:22 | DEBUG | 处理消息内容: '小红也通宵了'
2025-07-22 11:30:22 | DEBUG | 消息内容 '小红也通宵了' 不匹配任何命令，忽略
2025-07-22 11:30:34 | DEBUG | 收到消息: {'MsgId': 2006962882, 'FromUserName': {'string': '27852221909@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 1, 'Content': {'string': 'wxid_ugv5ryus4gz622:\n不过她醒的早 没被嘟嘟'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1753155046, 'MsgSource': '<msgsource>\n\t<bizflag>0</bizflag>\n\t<pua>1</pua>\n\t<eggIncluded>1</eggIncluded>\n\t<silence>1</silence>\n\t<membercount>146</membercount>\n\t<signature>N0_V1_AhGim/VB|v1_Egit7Eq2</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'NewMsgId': 4210048443851383965, 'MsgSeq': 871390633}
2025-07-22 11:30:34 | INFO | 收到文本消息: 消息ID:2006962882 来自:27852221909@chatroom 发送人:wxid_ugv5ryus4gz622 @:[] 内容:不过她醒的早 没被嘟嘟
2025-07-22 11:30:34 | DEBUG | 处理消息内容: '不过她醒的早 没被嘟嘟'
2025-07-22 11:30:34 | DEBUG | 消息内容 '不过她醒的早 没被嘟嘟' 不匹配任何命令，忽略
2025-07-22 11:30:40 | DEBUG | 收到消息: {'MsgId': 742481737, 'FromUserName': {'string': '27852221909@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 47, 'Content': {'string': 'wxid_ugv5ryus4gz622:\n<msg><emoji fromusername="wxid_ugv5ryus4gz622" tousername="27852221909@chatroom" type="3" idbuffer="media:0_0" md5="dfeb101129656646b70d0303bb6c026a" len="9851" productid="" androidmd5="dfeb101129656646b70d0303bb6c026a" androidlen="9851" s60v3md5="dfeb101129656646b70d0303bb6c026a" s60v3len="9851" s60v5md5="dfeb101129656646b70d0303bb6c026a" s60v5len="9851" cdnurl="http://wxapp.tc.qq.com/262/20304/stodownload?m=dfeb101129656646b70d0303bb6c026a&amp;filekey=30340201010420301e020201060402535a0410dfeb101129656646b70d0303bb6c026a0202267b040d00000004627466730000000132&amp;hy=SZ&amp;storeid=2641e7d7100093b0e5361bae70000010600004f50535a02aae970b664b1e1b&amp;bizid=1023" designerid="" thumburl="" encrypturl="http://wxapp.tc.qq.com/262/20304/stodownload?m=1e393ff309a3d65a1bad81efa0b0fce8&amp;filekey=30340201010420301e020201060402535a04101e393ff309a3d65a1bad81efa0b0fce802022680040d00000004627466730000000132&amp;hy=SZ&amp;storeid=2641e7d710009e85d5361bae70000010600004f50535a1d481970b6677d103&amp;bizid=1023" aeskey="27d12b84e9781f45caaec7e2622ebc06" externurl="http://wxapp.tc.qq.com/262/20304/stodownload?m=030da71ebda711520c9c61a7e5083cbc&amp;filekey=30340201010420301e020201060402535a0410030da71ebda711520c9c61a7e5083cbc02021060040d00000004627466730000000132&amp;hy=SZ&amp;storeid=2641f15b9000825425361bae70000010600004f50535a0c68c910b66d08c43&amp;bizid=1023" externmd5="7de550551ffeabe7d8fb17a3a36d2680" width="360" height="360" tpurl="" tpauthkey="" attachedtext="" attachedtextcolor="" lensid="" emojiattr="" linkid="" desc=""></emoji><gameext type="0" content="0"></gameext></msg>'}, 'Status': 3, 'ImgStatus': 2, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1753155052, 'MsgSource': '<msgsource>\n\t<silence>1</silence>\n\t<membercount>146</membercount>\n\t<signature>N0_V1_w647o9Kb|v1_ec4JD+vw</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'NewMsgId': 1567028149579350668, 'MsgSeq': 871390634}
2025-07-22 11:30:40 | INFO | 收到表情消息: 消息ID:742481737 来自:27852221909@chatroom 发送人:wxid_ugv5ryus4gz622 MD5:dfeb101129656646b70d0303bb6c026a 大小:9851
2025-07-22 11:30:40 | DEBUG | [DoubaoImageRecognition] 缓存表情消息: 1567028149579350668
2025-07-22 11:31:34 | DEBUG | 收到消息: {'MsgId': 307408260, 'FromUserName': {'string': '27852221909@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 1, 'Content': {'string': 'wxid_x4s6k999g6qg22:\n今天副本上强度了'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1753155106, 'MsgSource': '<msgsource>\n\t<bizflag>0</bizflag>\n\t<pua>1</pua>\n\t<eggIncluded>1</eggIncluded>\n\t<silence>1</silence>\n\t<membercount>146</membercount>\n\t<signature>N0_V1_fCOHLPOp|v1_xMNjpOT0</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'NewMsgId': 512588038371433806, 'MsgSeq': 871390635}
2025-07-22 11:31:34 | INFO | 收到文本消息: 消息ID:307408260 来自:27852221909@chatroom 发送人:wxid_x4s6k999g6qg22 @:[] 内容:今天副本上强度了
2025-07-22 11:31:34 | DEBUG | 处理消息内容: '今天副本上强度了'
2025-07-22 11:31:34 | DEBUG | 消息内容 '今天副本上强度了' 不匹配任何命令，忽略
2025-07-22 11:32:25 | DEBUG | 收到消息: {'MsgId': 1300802414, 'FromUserName': {'string': '27852221909@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 1, 'Content': {'string': 'wxid_x4s6k999g6qg22:\n@悦菟ིྀ\u2005等你拖我'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1753155157, 'MsgSource': '<msgsource>\n\t<atuserlist>wxid_ugv5ryus4gz622</atuserlist>\n\t<bizflag>0</bizflag>\n\t<pua>1</pua>\n\t<eggIncluded>1</eggIncluded>\n\t<silence>1</silence>\n\t<membercount>146</membercount>\n\t<signature>N0_V1_B//THQY0|v1_nM3z9R5W</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'NewMsgId': 8611348660878762601, 'MsgSeq': 871390636}
2025-07-22 11:32:25 | INFO | 收到文本消息: 消息ID:1300802414 来自:27852221909@chatroom 发送人:wxid_x4s6k999g6qg22 @:['wxid_ugv5ryus4gz622'] 内容:@悦菟ིྀ 等你拖我
2025-07-22 11:32:25 | DEBUG | 处理消息内容: '@悦菟ིྀ 等你拖我'
2025-07-22 11:32:25 | DEBUG | 消息内容 '@悦菟ིྀ 等你拖我' 不匹配任何命令，忽略
2025-07-22 11:32:57 | DEBUG | 收到消息: {'MsgId': 29702069, 'FromUserName': {'string': '48097389945@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 47, 'Content': {'string': 'xiaomaochong:\n<msg><emoji fromusername="xiaomaochong" tousername="48097389945@chatroom" type="2" idbuffer="media:0_0" md5="94580a4eaa8d8f4796dc5e404b1f647b" len="2394796" productid="" androidmd5="94580a4eaa8d8f4796dc5e404b1f647b" androidlen="2394796" s60v3md5="94580a4eaa8d8f4796dc5e404b1f647b" s60v3len="2394796" s60v5md5="94580a4eaa8d8f4796dc5e404b1f647b" s60v5len="2394796" cdnurl="http://vweixinf.tc.qq.com/110/20401/stodownload?m=94580a4eaa8d8f4796dc5e404b1f647b&amp;filekey=30440201010430302e02016e04025348042039343538306134656161386438663437393664633565343034623166363437620203248aac040d00000004627466730000000132&amp;hy=SH&amp;storeid=264561cd3000a72a3206859440000006e01004fb153481c769b40b71a7076d&amp;ef=1&amp;bizid=1022" designerid="" thumburl="" encrypturl="http://vweixinf.tc.qq.com/110/20402/stodownload?m=e45077bbcc8493bad28cce5b2f3a5fe4&amp;filekey=30440201010430302e02016e04025348042065343530373762626363383439336261643238636365356232663361356665340203248ab0040d00000004627466730000000132&amp;hy=SH&amp;storeid=264561cd3000d0b21206859440000006e02004fb253481c769b40b71a707e0&amp;ef=2&amp;bizid=1022" aeskey="71d8da7091854f809b1847384b995850" externurl="http://vweixinf.tc.qq.com/110/20403/stodownload?m=b470051f2f89ca2e9334c23148971f91&amp;filekey=30440201010430302e02016e040253480420623437303035316632663839636132653933333463323331343839373166393102030d59a0040d00000004627466730000000132&amp;hy=SH&amp;storeid=264561cd400006d53206859440000006e03004fb353481c769b40b71a70829&amp;ef=3&amp;bizid=1022" externmd5="abef68b62f11ddd5449169041a96d385" width="512" height="512" tpurl="" tpauthkey="" attachedtext="" attachedtextcolor="" lensid="" emojiattr="" linkid="" desc=""></emoji></msg>'}, 'Status': 3, 'ImgStatus': 2, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1753155189, 'MsgSource': '<msgsource>\n\t<silence>0</silence>\n\t<membercount>62</membercount>\n\t<signature>N0_V1_0PEuticP|v1_FCFgzTdb</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'PushContent': '小爱在群聊中发了一个表情', 'NewMsgId': 6262324036613381526, 'MsgSeq': 871390637}
2025-07-22 11:32:57 | INFO | 收到表情消息: 消息ID:29702069 来自:48097389945@chatroom 发送人:xiaomaochong MD5:94580a4eaa8d8f4796dc5e404b1f647b 大小:2394796
2025-07-22 11:32:57 | DEBUG | [DoubaoImageRecognition] 缓存表情消息: 6262324036613381526
2025-07-22 11:33:15 | DEBUG | 收到消息: {'MsgId': 1590049936, 'FromUserName': {'string': '27852221909@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 1, 'Content': {'string': 'wxid_ugv5ryus4gz622:\n好嘟 下午咱们打'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1753155207, 'MsgSource': '<msgsource>\n\t<bizflag>0</bizflag>\n\t<pua>1</pua>\n\t<eggIncluded>1</eggIncluded>\n\t<silence>1</silence>\n\t<membercount>146</membercount>\n\t<signature>N0_V1_mKnupfid|v1_nkA9rzMU</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'NewMsgId': 7976043158565905400, 'MsgSeq': 871390638}
2025-07-22 11:33:15 | INFO | 收到文本消息: 消息ID:1590049936 来自:27852221909@chatroom 发送人:wxid_ugv5ryus4gz622 @:[] 内容:好嘟 下午咱们打
2025-07-22 11:33:15 | DEBUG | 处理消息内容: '好嘟 下午咱们打'
2025-07-22 11:33:15 | DEBUG | 消息内容 '好嘟 下午咱们打' 不匹配任何命令，忽略
2025-07-22 11:33:45 | DEBUG | 收到消息: {'MsgId': 2027340619, 'FromUserName': {'string': '27852221909@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 1, 'Content': {'string': 'wxid_ugv5ryus4gz622:\n昨天小红非拉我去她家做客 一下做客到3点多'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1753155237, 'MsgSource': '<msgsource>\n\t<bizflag>0</bizflag>\n\t<pua>1</pua>\n\t<eggIncluded>1</eggIncluded>\n\t<silence>1</silence>\n\t<membercount>146</membercount>\n\t<signature>N0_V1_APFuJEwI|v1_4uBynn92</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'NewMsgId': 3276896246555938293, 'MsgSeq': 871390639}
2025-07-22 11:33:45 | INFO | 收到文本消息: 消息ID:2027340619 来自:27852221909@chatroom 发送人:wxid_ugv5ryus4gz622 @:[] 内容:昨天小红非拉我去她家做客 一下做客到3点多
2025-07-22 11:33:45 | DEBUG | 处理消息内容: '昨天小红非拉我去她家做客 一下做客到3点多'
2025-07-22 11:33:45 | DEBUG | 消息内容 '昨天小红非拉我去她家做客 一下做客到3点多' 不匹配任何命令，忽略
2025-07-22 11:33:54 | DEBUG | 收到消息: {'MsgId': 1781273513, 'FromUserName': {'string': '27852221909@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 47, 'Content': {'string': 'wxid_ugv5ryus4gz622:\n<msg><emoji fromusername="wxid_ugv5ryus4gz622" tousername="27852221909@chatroom" type="3" idbuffer="media:0_0" md5="6ad68ca85777465a8df0f27da44715d9" len="11270" productid="" androidmd5="6ad68ca85777465a8df0f27da44715d9" androidlen="11270" s60v3md5="6ad68ca85777465a8df0f27da44715d9" s60v3len="11270" s60v5md5="6ad68ca85777465a8df0f27da44715d9" s60v5len="11270" cdnurl="http://vweixinf.tc.qq.com/110/20401/stodownload?m=6ad68ca85777465a8df0f27da44715d9&amp;filekey=3043020101042f302d02016e0402535a0420366164363863613835373737343635613864663066323764613434373135643902022c06040d00000004627466730000000131&amp;hy=SZ&amp;storeid=323032303131323731333232333330303033343539613034316139363139323837663566303930303030303036653031303034666231&amp;ef=1&amp;bizid=1022" designerid="" thumburl="" encrypturl="http://vweixinf.tc.qq.com/110/20402/stodownload?m=c1b5a1ddf68fed1a939ca5ff54076a51&amp;filekey=3043020101042f302d02016e0402535a0420633162356131646466363866656431613933396361356666353430373661353102022c10040d00000004627466730000000131&amp;hy=SZ&amp;storeid=323032303131323731333232333330303033656137353034316139363139323837663566303930303030303036653032303034666232&amp;ef=2&amp;bizid=1022" aeskey="c8f96a1356124695b360518cfb17fa8e" externurl="http://vweixinf.tc.qq.com/110/20403/stodownload?m=f8838ab53f5188755dfddff19250f42e&amp;filekey=3043020101042f302d02016e0402535a04206638383338616235336635313838373535646664646666313932353066343265020218a0040d00000004627466730000000131&amp;hy=SZ&amp;storeid=323032303131323731333232333330303034623030303034316139363139323837663566303930303030303036653033303034666233&amp;ef=3&amp;bizid=1022" externmd5="d96426206d9b8c61d910a2b1ceb71608" width="400" height="378" tpurl="" tpauthkey="" attachedtext="" attachedtextcolor="" lensid="" emojiattr="Cg/mkbjkuI3nnYDlpLTohJE=" linkid="" desc=""></emoji><gameext type="0" content="0"></gameext></msg>'}, 'Status': 3, 'ImgStatus': 2, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1753155246, 'MsgSource': '<msgsource>\n\t<silence>1</silence>\n\t<membercount>146</membercount>\n\t<signature>N0_V1_FmsoA8Kp|v1_xclnp3XE</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'NewMsgId': 3553354303968990527, 'MsgSeq': 871390640}
2025-07-22 11:33:54 | INFO | 收到表情消息: 消息ID:1781273513 来自:27852221909@chatroom 发送人:wxid_ugv5ryus4gz622 MD5:6ad68ca85777465a8df0f27da44715d9 大小:11270
2025-07-22 11:33:54 | DEBUG | [DoubaoImageRecognition] 缓存表情消息: 3553354303968990527
2025-07-22 11:34:11 | DEBUG | 收到消息: {'MsgId': 113510017, 'FromUserName': {'string': '48097389945@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 1, 'Content': {'string': 'xiaomaochong:\n洞房'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1753155263, 'MsgSource': '<msgsource>\n\t<pua>1</pua>\n\t<silence>0</silence>\n\t<membercount>62</membercount>\n\t<signature>N0_V1_+1qxYxqa|v1_IGNrQwQx</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'PushContent': '小爱 : 洞房', 'NewMsgId': 5671210705037903184, 'MsgSeq': 871390641}
2025-07-22 11:34:11 | INFO | 收到文本消息: 消息ID:113510017 来自:48097389945@chatroom 发送人:xiaomaochong @:[] 内容:洞房
2025-07-22 11:34:11 | DEBUG | 处理消息内容: '洞房'
2025-07-22 11:34:11 | DEBUG | 消息内容 '洞房' 不匹配任何命令，忽略
2025-07-22 11:34:13 | DEBUG | 收到消息: {'MsgId': 228868045, 'FromUserName': {'string': '48097389945@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 1, 'Content': {'string': 'wxid_8l9ymg1mafud12:\n「小爱」[爱心]「锦岚（开玩笑就退群版）」\n地点：小树林\n活动：啪啪\n结果：失败\n羞羞：卧槽！被人发现~\n恩爱值减少100\n\n下次:2025-07-22 11:44:24'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1753155264, 'MsgSource': '<msgsource>\n\t<silence>0</silence>\n\t<membercount>62</membercount>\n\t<signature>N0_V1_9q+hiN0U|v1_U0d5MsP1</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'PushContent': '不吃香菜\ue110 : 「小爱」[爱心]「锦岚（开玩笑就退群版）」\n地点：小树林\n活动：啪...', 'NewMsgId': 8050066967578893192, 'MsgSeq': 871390642}
2025-07-22 11:34:13 | INFO | 收到文本消息: 消息ID:228868045 来自:48097389945@chatroom 发送人:wxid_8l9ymg1mafud12 @:[] 内容:「小爱」[爱心]「锦岚（开玩笑就退群版）」
地点：小树林
活动：啪啪
结果：失败
羞羞：卧槽！被人发现~
恩爱值减少100

下次:2025-07-22 11:44:24
2025-07-22 11:34:13 | DEBUG | 处理消息内容: '「小爱」[爱心]「锦岚（开玩笑就退群版）」
地点：小树林
活动：啪啪
结果：失败
羞羞：卧槽！被人发现~
恩爱值减少100

下次:2025-07-22 11:44:24'
2025-07-22 11:34:13 | DEBUG | 消息内容 '「小爱」[爱心]「锦岚（开玩笑就退群版）」
地点：小树林
活动：啪啪
结果：失败
羞羞：卧槽！被人发现~
恩爱值减少100

下次:2025-07-22 11:44:24' 不匹配任何命令，忽略
2025-07-22 11:34:15 | DEBUG | 收到消息: {'MsgId': 1981002147, 'FromUserName': {'string': '48097389945@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 1, 'Content': {'string': 'wxid_q35rkzgkjvlv12:\n 💕 [小爱]👩\u200d❤\u200d👨[锦岚（开玩笑就退群版）]💕小爱你刚刚买的兰博基尼[砖圈]带上最爱你的锦岚（开玩笑就退群版），去旅游\n💓═☘︎═•洞💗房•═☘︎═💓\n👩\u200d❤\u200d👨关系：恩爱夫妻\n⛺地点：厕所\n😍活动：搞一圈\n[爱心]结果：依旧兼听\n💓═☘︎══•💗•══☘︎═💓\n🔮魅力:+45\n[烟花]恩爱:+2 \n🕒下次:2025-07-22 11:54:25'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1753155264, 'MsgSource': '<msgsource>\n\t<silence>0</silence>\n\t<membercount>62</membercount>\n\t<signature>N0_V1_npKuYA/y|v1_WBdWmjsj</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'PushContent': '摇月 :  \ue327 [小爱]\ue005\u200d\ue022\u200d\ue004[锦岚（开玩笑就退群版）]\ue327小爱你刚刚买...', 'NewMsgId': 2564720735186016219, 'MsgSeq': 871390643}
2025-07-22 11:34:15 | INFO | 收到文本消息: 消息ID:1981002147 来自:48097389945@chatroom 发送人:wxid_q35rkzgkjvlv12 @:[] 内容: 💕 [小爱]👩‍❤‍👨[锦岚（开玩笑就退群版）]💕小爱你刚刚买的兰博基尼[砖圈]带上最爱你的锦岚（开玩笑就退群版），去旅游
💓═☘︎═•洞💗房•═☘︎═💓
👩‍❤‍👨关系：恩爱夫妻
⛺地点：厕所
😍活动：搞一圈
[爱心]结果：依旧兼听
💓═☘︎══•💗•══☘︎═💓
🔮魅力:+45
[烟花]恩爱:+2 
🕒下次:2025-07-22 11:54:25
2025-07-22 11:34:15 | DEBUG | 处理消息内容: '💕 [小爱]👩‍❤‍👨[锦岚（开玩笑就退群版）]💕小爱你刚刚买的兰博基尼[砖圈]带上最爱你的锦岚（开玩笑就退群版），去旅游
💓═☘︎═•洞💗房•═☘︎═💓
👩‍❤‍👨关系：恩爱夫妻
⛺地点：厕所
😍活动：搞一圈
[爱心]结果：依旧兼听
💓═☘︎══•💗•══☘︎═💓
🔮魅力:+45
[烟花]恩爱:+2 
🕒下次:2025-07-22 11:54:25'
2025-07-22 11:34:15 | DEBUG | 消息内容 '💕 [小爱]👩‍❤‍👨[锦岚（开玩笑就退群版）]💕小爱你刚刚买的兰博基尼[砖圈]带上最爱你的锦岚（开玩笑就退群版），去旅游
💓═☘︎═•洞💗房•═☘︎═💓
👩‍❤‍👨关系：恩爱夫妻
⛺地点：厕所
😍活动：搞一圈
[爱心]结果：依旧兼听
💓═☘︎══•💗•══☘︎═💓
🔮魅力:+45
[烟花]恩爱:+2 
🕒下次:2025-07-22 11:54:25' 不匹配任何命令，忽略
2025-07-22 11:35:19 | DEBUG | 收到消息: {'MsgId': 2069327563, 'FromUserName': {'string': '27852221909@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 1, 'Content': {'string': 'wxid_cb2tqz5n94lp12:\n5-6章难打吗'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1753155330, 'MsgSource': '<msgsource>\n\t<bizflag>0</bizflag>\n\t<pua>1</pua>\n\t<eggIncluded>1</eggIncluded>\n\t<silence>1</silence>\n\t<membercount>146</membercount>\n\t<signature>N0_V1_rZ0Qo8j9|v1_srqRRMBK</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'NewMsgId': 4695824796963121076, 'MsgSeq': 871390644}
2025-07-22 11:35:19 | INFO | 收到文本消息: 消息ID:2069327563 来自:27852221909@chatroom 发送人:wxid_cb2tqz5n94lp12 @:[] 内容:5-6章难打吗
2025-07-22 11:35:19 | DEBUG | 处理消息内容: '5-6章难打吗'
2025-07-22 11:35:19 | DEBUG | 消息内容 '5-6章难打吗' 不匹配任何命令，忽略
2025-07-22 11:36:14 | DEBUG | 收到消息: {'MsgId': 677524513, 'FromUserName': {'string': '55878994168@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 1, 'Content': {'string': 'wxid_ubbh6q832tcs21:\n美图 画一个大美女'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1753155387, 'MsgSource': '<msgsource>\n\t<pua>1</pua>\n\t<eggIncluded>1</eggIncluded>\n\t<silence>0</silence>\n\t<membercount>3</membercount>\n\t<signature>N0_V1_td45UlRL|v1_cQ0BaIg9</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'PushContent': '郭 : 美图 画一个大美女', 'NewMsgId': 7030885525648509841, 'MsgSeq': 871390645}
2025-07-22 11:36:14 | INFO | 收到文本消息: 消息ID:677524513 来自:55878994168@chatroom 发送人:wxid_ubbh6q832tcs21 @:[] 内容:美图 画一个大美女
2025-07-22 11:36:15 | DEBUG | 处理消息内容: '美图 画一个大美女'
2025-07-22 11:36:15 | DEBUG | 消息内容 '美图 画一个大美女' 不匹配任何命令，忽略
2025-07-22 11:36:16 | INFO | 发送文字消息: 对方wxid:55878994168@chatroom at:['wxid_ubbh6q832tcs21'] 内容:@郭 ❌ 命令格式错误，正确格式: 🎨 美图RoboNeo插件使用说明

📝 使用方法：
• 引用图片消息并发送: 美图+提示词
• 例如: 美图换成泳衣
• 例如: 美图+换成古装

💡 提示词示例：
• 换成泳衣
• 换成古装
• 换成婚纱
• 换成职业装
• 换成运动装

🤖 AI交互功能：
• AI可能会询问更多细节（如风格、颜色等）
• 收到AI询问时，直接回答即可
• 例如：AI问"什么风格的泳衣？" 你回答"比基尼"

⏱️ 处理时间: 约30-180秒
🔄 支持多种风格转换

2025-07-22 11:36:17 | INFO | 发送文字消息: 对方wxid:55878994168@chatroom at:['wxid_ubbh6q832tcs21'] 内容:@郭 🎨 美图RoboNeo插件使用说明
📝 命令格式:
• 🎨 美图RoboNeo插件使用说明

📝 使用方法：
• 引用图片消息并发送: 美图+提示词
• 例如: 美图换成泳衣
• 例如: 美图+换成古装

💡 提示词示例：
• 换成泳衣
• 换成古装
• 换成婚纱
• 换成职业装
• 换成运动装

🤖 AI交互功能：
• AI可能会询问更多细节（如风格、颜色等）
• 收到AI询问时，直接回答即可
• 例如：AI问"什么风格的泳衣？" 你回答"比基尼"

⏱️ 处理时间: 约30-180秒
🔄 支持多种风格转换

• 引用图片并发送: 美图+提示词
💡 示例: 美图换成泳衣
⏱️ 处理时间: 约30-180秒
🤖 AI可能会询问更多细节，请直接回答
2025-07-22 11:36:56 | DEBUG | 收到消息: {'MsgId': 2133750845, 'FromUserName': {'string': '27852221909@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 49, 'Content': {'string': 'wxid_idzryo4rneok22:\n<?xml version="1.0"?>\n<msg>\n\t<appmsg appid="" sdkver="0">\n\t\t<title>那叫盛情邀请</title>\n\t\t<des />\n\t\t<username />\n\t\t<action>view</action>\n\t\t<type>57</type>\n\t\t<showtype>0</showtype>\n\t\t<content />\n\t\t<url />\n\t\t<lowurl />\n\t\t<forwardflag>0</forwardflag>\n\t\t<dataurl />\n\t\t<lowdataurl />\n\t\t<contentattr>0</contentattr>\n\t\t<streamvideo>\n\t\t\t<streamvideourl />\n\t\t\t<streamvideototaltime>0</streamvideototaltime>\n\t\t\t<streamvideotitle />\n\t\t\t<streamvideowording />\n\t\t\t<streamvideoweburl />\n\t\t\t<streamvideothumburl />\n\t\t\t<streamvideoaduxinfo />\n\t\t\t<streamvideopublishid />\n\t\t</streamvideo>\n\t\t<canvasPageItem>\n\t\t\t<canvasPageXml><![CDATA[]]></canvasPageXml>\n\t\t</canvasPageItem>\n\t\t<refermsg>\n\t\t\t<type>1</type>\n\t\t\t<svrid>3276896246555938293</svrid>\n\t\t\t<fromusr>27852221909@chatroom</fromusr>\n\t\t\t<chatusr>wxid_ugv5ryus4gz622</chatusr>\n\t\t\t<displayname>悦菟ིྀ</displayname>\n\t\t\t<msgsource>&lt;msgsource&gt;\n\t&lt;bizflag&gt;0&lt;/bizflag&gt;\n\t&lt;pua&gt;1&lt;/pua&gt;\n\t&lt;eggIncluded&gt;1&lt;/eggIncluded&gt;\n\t&lt;silence&gt;1&lt;/silence&gt;\n\t&lt;membercount&gt;146&lt;/membercount&gt;\n\t&lt;signature&gt;N0_V1_y+GH8Idx|v1_2YzkRgwv&lt;/signature&gt;\n\t&lt;tmp_node&gt;\n\t\t&lt;publisher-id&gt;&lt;/publisher-id&gt;\n\t&lt;/tmp_node&gt;\n&lt;/msgsource&gt;\n</msgsource>\n\t\t\t<content>\n昨天小红非拉我去她家做客 一下做客到3点多</content>\n\t\t\t<strid />\n\t\t\t<createtime>1753155237</createtime>\n\t\t</refermsg>\n\t\t<appattach>\n\t\t\t<totallen>0</totallen>\n\t\t\t<attachid />\n\t\t\t<cdnattachurl />\n\t\t\t<emoticonmd5></emoticonmd5>\n\t\t\t<aeskey></aeskey>\n\t\t\t<fileext />\n\t\t\t<islargefilemsg>0</islargefilemsg>\n\t\t</appattach>\n\t\t<extinfo />\n\t\t<androidsource>0</androidsource>\n\t\t<thumburl />\n\t\t<mediatagname />\n\t\t<messageaction><![CDATA[]]></messageaction>\n\t\t<messageext><![CDATA[]]></messageext>\n\t\t<emoticongift>\n\t\t\t<packageflag>0</packageflag>\n\t\t\t<packageid />\n\t\t</emoticongift>\n\t\t<emoticonshared>\n\t\t\t<packageflag>0</packageflag>\n\t\t\t<packageid />\n\t\t</emoticonshared>\n\t\t<designershared>\n\t\t\t<designeruin>0</designeruin>\n\t\t\t<designername>null</designername>\n\t\t\t<designerrediretcturl><![CDATA[null]]></designerrediretcturl>\n\t\t</designershared>\n\t\t<emotionpageshared>\n\t\t\t<tid>0</tid>\n\t\t\t<title>null</title>\n\t\t\t<desc>null</desc>\n\t\t\t<iconUrl><![CDATA[null]]></iconUrl>\n\t\t\t<secondUrl>null</secondUrl>\n\t\t\t<pageType>0</pageType>\n\t\t\t<setKey>null</setKey>\n\t\t</emotionpageshared>\n\t\t<webviewshared>\n\t\t\t<shareUrlOriginal />\n\t\t\t<shareUrlOpen />\n\t\t\t<jsAppId />\n\t\t\t<publisherId />\n\t\t\t<publisherReqId />\n\t\t</webviewshared>\n\t\t<template_id />\n\t\t<md5 />\n\t\t<websearch>\n\t\t\t<rec_category>0</rec_category>\n\t\t\t<channelId>0</channelId>\n\t\t</websearch>\n\t\t<weappinfo>\n\t\t\t<username />\n\t\t\t<appid />\n\t\t\t<appservicetype>0</appservicetype>\n\t\t\t<secflagforsinglepagemode>0</secflagforsinglepagemode>\n\t\t\t<videopageinfo>\n\t\t\t\t<thumbwidth>0</thumbwidth>\n\t\t\t\t<thumbheight>0</thumbheight>\n\t\t\t\t<fromopensdk>0</fromopensdk>\n\t\t\t</videopageinfo>\n\t\t</weappinfo>\n\t\t<statextstr />\n\t\t<musicShareItem>\n\t\t\t<musicDuration>0</musicDuration>\n\t\t</musicShareItem>\n\t\t<finderLiveProductShare>\n\t\t\t<finderLiveID><![CDATA[]]></finderLiveID>\n\t\t\t<finderUsername><![CDATA[]]></finderUsername>\n\t\t\t<finderObjectID><![CDATA[]]></finderObjectID>\n\t\t\t<finderNonceID><![CDATA[]]></finderNonceID>\n\t\t\t<liveStatus><![CDATA[]]></liveStatus>\n\t\t\t<appId><![CDATA[]]></appId>\n\t\t\t<pagePath><![CDATA[]]></pagePath>\n\t\t\t<productId><![CDATA[]]></productId>\n\t\t\t<coverUrl><![CDATA[]]></coverUrl>\n\t\t\t<productTitle><![CDATA[]]></productTitle>\n\t\t\t<marketPrice><![CDATA[0]]></marketPrice>\n\t\t\t<sellingPrice><![CDATA[0]]></sellingPrice>\n\t\t\t<platformHeadImg><![CDATA[]]></platformHeadImg>\n\t\t\t<platformName><![CDATA[]]></platformName>\n\t\t\t<shopWindowId><![CDATA[]]></shopWindowId>\n\t\t\t<flashSalePrice><![CDATA[0]]></flashSalePrice>\n\t\t\t<flashSaleEndTime><![CDATA[0]]></flashSaleEndTime>\n\t\t\t<ecSource><![CDATA[]]></ecSource>\n\t\t\t<sellingPriceWording><![CDATA[]]></sellingPriceWording>\n\t\t\t<platformIconURL><![CDATA[]]></platformIconURL>\n\t\t\t<firstProductTagURL><![CDATA[]]></firstProductTagURL>\n\t\t\t<firstProductTagAspectRatioString><![CDATA[0.0]]></firstProductTagAspectRatioString>\n\t\t\t<secondProductTagURL><![CDATA[]]></secondProductTagURL>\n\t\t\t<secondProductTagAspectRatioString><![CDATA[0.0]]></secondProductTagAspectRatioString>\n\t\t\t<firstGuaranteeWording><![CDATA[]]></firstGuaranteeWording>\n\t\t\t<secondGuaranteeWording><![CDATA[]]></secondGuaranteeWording>\n\t\t\t<thirdGuaranteeWording><![CDATA[]]></thirdGuaranteeWording>\n\t\t\t<isPriceBeginShow>false</isPriceBeginShow>\n\t\t\t<lastGMsgID><![CDATA[]]></lastGMsgID>\n\t\t\t<promoterKey><![CDATA[]]></promoterKey>\n\t\t\t<discountWording><![CDATA[]]></discountWording>\n\t\t\t<priceSuffixDescription><![CDATA[]]></priceSuffixDescription>\n\t\t\t<productCardKey><![CDATA[]]></productCardKey>\n\t\t\t<isWxShop><![CDATA[]]></isWxShop>\n\t\t\t<brandIconUrl><![CDATA[]]></brandIconUrl>\n\t\t\t<rIconUrl><![CDATA[]]></rIconUrl>\n\t\t\t<rIconUrlDarkMode><![CDATA[]]></rIconUrlDarkMode>\n\t\t\t<topShopIconUrl><![CDATA[]]></topShopIconUrl>\n\t\t\t<topShopIconUrlDarkMode><![CDATA[]]></topShopIconUrlDarkMode>\n\t\t\t<simplifyTopShopIconUrl><![CDATA[]]></simplifyTopShopIconUrl>\n\t\t\t<simplifyTopShopIconUrlDarkmode><![CDATA[]]></simplifyTopShopIconUrlDarkmode>\n\t\t\t<topShopIconWidth><![CDATA[0]]></topShopIconWidth>\n\t\t\t<topShopIconHeight><![CDATA[0]]></topShopIconHeight>\n\t\t\t<simplifyTopShopIconWidth><![CDATA[0]]></simplifyTopShopIconWidth>\n\t\t\t<simplifyTopShopIconHeight><![CDATA[0]]></simplifyTopShopIconHeight>\n\t\t\t<showBoxItemStringList />\n\t\t</finderLiveProductShare>\n\t\t<finderOrder>\n\t\t\t<appID><![CDATA[]]></appID>\n\t\t\t<orderID><![CDATA[]]></orderID>\n\t\t\t<path><![CDATA[]]></path>\n\t\t\t<priceWording><![CDATA[]]></priceWording>\n\t\t\t<stateWording><![CDATA[]]></stateWording>\n\t\t\t<productImageURL><![CDATA[]]></productImageURL>\n\t\t\t<products><![CDATA[]]></products>\n\t\t\t<productsCount><![CDATA[0]]></productsCount>\n\t\t\t<orderType><![CDATA[0]]></orderType>\n\t\t\t<newPriceWording><![CDATA[]]></newPriceWording>\n\t\t\t<newStateWording><![CDATA[]]></newStateWording>\n\t\t\t<useNewWording><![CDATA[0]]></useNewWording>\n\t\t</finderOrder>\n\t\t<finderShopWindowShare>\n\t\t\t<finderUsername><![CDATA[]]></finderUsername>\n\t\t\t<avatar><![CDATA[]]></avatar>\n\t\t\t<nickname><![CDATA[]]></nickname>\n\t\t\t<commodityInStockCount><![CDATA[]]></commodityInStockCount>\n\t\t\t<appId><![CDATA[]]></appId>\n\t\t\t<path><![CDATA[]]></path>\n\t\t\t<appUsername><![CDATA[]]></appUsername>\n\t\t\t<query><![CDATA[]]></query>\n\t\t\t<liteAppId><![CDATA[]]></liteAppId>\n\t\t\t<liteAppPath><![CDATA[]]></liteAppPath>\n\t\t\t<liteAppQuery><![CDATA[]]></liteAppQuery>\n\t\t\t<platformTagURL><![CDATA[]]></platformTagURL>\n\t\t\t<saleWording><![CDATA[]]></saleWording>\n\t\t\t<lastGMsgID><![CDATA[]]></lastGMsgID>\n\t\t\t<profileTypeWording><![CDATA[]]></profileTypeWording>\n\t\t\t<saleWordingExtra><![CDATA[]]></saleWordingExtra>\n\t\t\t<isWxShop><![CDATA[]]></isWxShop>\n\t\t\t<platformIconUrl><![CDATA[]]></platformIconUrl>\n\t\t\t<brandIconUrl><![CDATA[]]></brandIconUrl>\n\t\t\t<description><![CDATA[]]></description>\n\t\t\t<backgroundUrl><![CDATA[]]></backgroundUrl>\n\t\t\t<darkModePlatformIconUrl><![CDATA[]]></darkModePlatformIconUrl>\n\t\t\t<rIconUrl><![CDATA[]]></rIconUrl>\n\t\t\t<rIconUrlDarkMode><![CDATA[]]></rIconUrlDarkMode>\n\t\t\t<rWords><![CDATA[]]></rWords>\n\t\t\t<topShopIconUrl><![CDATA[]]></topShopIconUrl>\n\t\t\t<topShopIconUrlDarkMode><![CDATA[]]></topShopIconUrlDarkMode>\n\t\t\t<simplifyTopShopIconUrl><![CDATA[]]></simplifyTopShopIconUrl>\n\t\t\t<simplifyTopShopIconUrlDarkmode><![CDATA[]]></simplifyTopShopIconUrlDarkmode>\n\t\t\t<topShopIconWidth><![CDATA[0]]></topShopIconWidth>\n\t\t\t<topShopIconHeight><![CDATA[0]]></topShopIconHeight>\n\t\t\t<simplifyTopShopIconWidth><![CDATA[0]]></simplifyTopShopIconWidth>\n\t\t\t<simplifyTopShopIconHeight><![CDATA[0]]></simplifyTopShopIconHeight>\n\t\t\t<reputationInfo>\n\t\t\t\t<hasReputationInfo>0</hasReputationInfo>\n\t\t\t\t<reputationScore>0</reputationScore>\n\t\t\t\t<reputationWording />\n\t\t\t\t<reputationTextColor />\n\t\t\t\t<reputationLevelWording />\n\t\t\t\t<reputationBackgroundColor />\n\t\t\t</reputationInfo>\n\t\t\t<productImageURLList />\n\t\t</finderShopWindowShare>\n\t\t<findernamecard>\n\t\t\t<username />\n\t\t\t<avatar><![CDATA[]]></avatar>\n\t\t\t<nickname />\n\t\t\t<auth_job />\n\t\t\t<auth_icon>0</auth_icon>\n\t\t\t<auth_icon_url />\n\t\t\t<ecSource><![CDATA[]]></ecSource>\n\t\t\t<lastGMsgID><![CDATA[]]></lastGMsgID>\n\t\t</findernamecard>\n\t\t<finderGuarantee>\n\t\t\t<scene><![CDATA[0]]></scene>\n\t\t</finderGuarantee>\n\t\t<directshare>0</directshare>\n\t\t<gamecenter>\n\t\t\t<namecard>\n\t\t\t\t<iconUrl />\n\t\t\t\t<name />\n\t\t\t\t<desc />\n\t\t\t\t<tail />\n\t\t\t\t<jumpUrl />\n\t\t\t\t<liteappId />\n\t\t\t\t<liteappPath />\n\t\t\t\t<liteappQuery />\n\t\t\t\t<liteappMinVersion />\n\t\t\t</namecard>\n\t\t</gamecenter>\n\t\t<patMsg>\n\t\t\t<chatUser />\n\t\t\t<records>\n\t\t\t\t<recordNum>0</recordNum>\n\t\t\t</records>\n\t\t</patMsg>\n\t\t<secretmsg>\n\t\t\t<issecretmsg>0</issecretmsg>\n\t\t</secretmsg>\n\t\t<referfromscene>0</referfromscene>\n\t\t<gameshare>\n\t\t\t<liteappext>\n\t\t\t\t<liteappbizdata />\n\t\t\t\t<priority>0</priority>\n\t\t\t</liteappext>\n\t\t\t<appbrandext>\n\t\t\t\t<litegameinfo />\n\t\t\t\t<priority>-1</priority>\n\t\t\t</appbrandext>\n\t\t\t<gameshareid />\n\t\t\t<sharedata />\n\t\t\t<isvideo>0</isvideo>\n\t\t\t<duration>-1</duration>\n\t\t\t<isexposed>0</isexposed>\n\t\t\t<readtext />\n\t\t</gameshare>\n\t\t<tingChatRoomItem>\n\t\t\t<type>0</type>\n\t\t\t<categoryItem>null</categoryItem>\n\t\t\t<categoryId />\n\t\t</tingChatRoomItem>\n\t\t<mpsharetrace>\n\t\t\t<hasfinderelement>0</hasfinderelement>\n\t\t\t<lastgmsgid />\n\t\t</mpsharetrace>\n\t\t<wxgamecard>\n\t\t\t<framesetname />\n\t\t\t<mbcarddata />\n\t\t\t<minpkgversion />\n\t\t\t<clientextinfo />\n\t\t\t<mbcardheight>0</mbcardheight>\n\t\t\t<isoldversion>0</isoldversion>\n\t\t</wxgamecard>\n\t\t<ecskfcard>\n\t\t\t<framesetname />\n\t\t\t<mbcarddata />\n\t\t\t<minupdateunixtimestamp>0</minupdateunixtimestamp>\n\t\t\t<needheader>false</needheader>\n\t\t\t<summary />\n\t\t</ecskfcard>\n\t\t<liteapp>\n\t\t\t<id>null</id>\n\t\t\t<path />\n\t\t\t<query />\n\t\t\t<istransparent>0</istransparent>\n\t\t\t<hideicon>0</hideicon>\n\t\t\t<forbidforward>0</forbidforward>\n\t\t</liteapp>\n\t\t<opensdk_share_is_modified>0</opensdk_share_is_modified>\n\t</appmsg>\n\t<fromusername>wxid_idzryo4rneok22</fromusername>\n\t<scene>0</scene>\n\t<appinfo>\n\t\t<version>1</version>\n\t\t<appname />\n\t</appinfo>\n\t<commenturl />\n</msg>\n'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1753155428, 'MsgSource': '<msgsource>\n\t<alnode>\n\t\t<fr>4</fr>\n\t</alnode>\n\t<eggIncluded>1</eggIncluded>\n\t<sec_msg_node>\n\t\t<uuid>339dcb440932883de1d723015e3f6455_</uuid>\n\t\t<risk-file-flag />\n\t\t<risk-file-md5-list />\n\t</sec_msg_node>\n\t<silence>1</silence>\n\t<membercount>146</membercount>\n\t<signature>N0_V1_Gn6gwArv|v1_db5K7hG8</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'NewMsgId': 8884137378535390949, 'MsgSeq': 871390650}
2025-07-22 11:36:56 | DEBUG | 从群聊消息中提取发送者: wxid_idzryo4rneok22
2025-07-22 11:36:56 | DEBUG | 使用已解析的XML处理引用消息
2025-07-22 11:36:56 | INFO | 收到引用消息: 消息ID:2133750845 来自:27852221909@chatroom 发送人:wxid_idzryo4rneok22 内容:那叫盛情邀请 引用类型:1
2025-07-22 11:36:56 | INFO | [DouBaoImageToImage] 收到引用消息: 那叫盛情邀请
2025-07-22 11:36:56 | INFO | [TimerTask] 收到引用消息调试信息:
2025-07-22 11:36:56 | INFO |   - 消息内容: 那叫盛情邀请
2025-07-22 11:36:56 | INFO |   - 群组ID: 27852221909@chatroom
2025-07-22 11:36:56 | INFO |   - 发送人: wxid_idzryo4rneok22
2025-07-22 11:36:56 | INFO |   - 引用信息: {'MsgType': 1, 'Content': '\n昨天小红非拉我去她家做客 一下做客到3点多', 'Msgid': '3276896246555938293', 'NewMsgId': '3276896246555938293', 'ToWxid': 'wxid_4usgcju5ey9q29', 'FromWxid': '27852221909@chatroom', 'Nickname': '悦菟ིྀ', 'MsgSource': '<msgsource>\n\t<bizflag>0</bizflag>\n\t<pua>1</pua>\n\t<eggIncluded>1</eggIncluded>\n\t<silence>1</silence>\n\t<membercount>146</membercount>\n\t<signature>N0_V1_y+GH8Idx|v1_2YzkRgwv</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'Createtime': '1753155237', 'SenderWxid': 'wxid_idzryo4rneok22'}
2025-07-22 11:36:56 | INFO |   - 引用消息ID: 
2025-07-22 11:36:56 | INFO |   - 引用消息类型: 
2025-07-22 11:36:56 | INFO |   - 引用消息内容: 
昨天小红非拉我去她家做客 一下做客到3点多
2025-07-22 11:36:56 | INFO |   - 引用消息发送人: wxid_idzryo4rneok22
2025-07-22 11:37:05 | DEBUG | 收到消息: {'MsgId': 19254330, 'FromUserName': {'string': '27852221909@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 1, 'Content': {'string': 'wxid_idzryo4rneok22:\n什么叫非拉你去'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1753155437, 'MsgSource': '<msgsource>\n\t<pua>1</pua>\n\t<eggIncluded>1</eggIncluded>\n\t<silence>1</silence>\n\t<membercount>146</membercount>\n\t<signature>N0_V1_W6lXX9cZ|v1_c1t3LgXu</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'NewMsgId': 7386932331003492360, 'MsgSeq': 871390651}
2025-07-22 11:37:05 | INFO | 收到文本消息: 消息ID:19254330 来自:27852221909@chatroom 发送人:wxid_idzryo4rneok22 @:[] 内容:什么叫非拉你去
2025-07-22 11:37:05 | DEBUG | 处理消息内容: '什么叫非拉你去'
2025-07-22 11:37:05 | DEBUG | 消息内容 '什么叫非拉你去' 不匹配任何命令，忽略
2025-07-22 11:37:17 | DEBUG | 收到消息: {'MsgId': 1859161633, 'FromUserName': {'string': '27852221909@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 3, 'Content': {'string': 'wxid_x4s6k999g6qg22:\n<?xml version="1.0"?>\n<msg>\n\t<img aeskey="e667e22b06d2939d2cf88633785a5b28" encryver="1" cdnthumbaeskey="e667e22b06d2939d2cf88633785a5b28" cdnthumburl="3057020100044b30490201000204025e8c6802032f54690204619999db0204687f0778042435353334623734322d663564372d343335342d616231352d373663303161336632626664020405290a020201000405004c57c100" cdnthumblength="3705" cdnthumbheight="120" cdnthumbwidth="67" cdnmidheight="0" cdnmidwidth="0" cdnhdheight="0" cdnhdwidth="0" cdnmidimgurl="3057020100044b30490201000204025e8c6802032f54690204619999db0204687f0778042435353334623734322d663564372d343335342d616231352d373663303161336632626664020405290a020201000405004c57c100" length="75635" md5="14e6a6ca349bcec6683c3a3d4da4dd50" hevc_mid_size="75635">\n\t\t<secHashInfoBase64>eyJwaGFzaCI6ImQyMzIzMzEwMTIwMDAwMDAiLCJwZHFoYXNoIjoiY2ViMzY2NjMyYjZjMWIzYTRlY2I5Y2NhNmNlZDQ0ZWViYjYzYjM2Mjg4ZGMxODg4MThlNzBjYTEyNzFhYzczYiJ9</secHashInfoBase64>\n\t\t<live>\n\t\t\t<duration>0</duration>\n\t\t\t<size>0</size>\n\t\t\t<md5 />\n\t\t\t<fileid />\n\t\t\t<hdsize>0</hdsize>\n\t\t\t<hdmd5 />\n\t\t\t<hdfileid />\n\t\t\t<stillimagetimems>0</stillimagetimems>\n\t\t</live>\n\t</img>\n\t<platform_signature />\n\t<imgdatahash />\n\t<ImgSourceInfo>\n\t\t<ImgSourceUrl />\n\t\t<BizType>0</BizType>\n\t</ImgSourceInfo>\n</msg>\n'}, 'Status': 3, 'ImgStatus': 2, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1753155448, 'MsgSource': '<msgsource>\n\t<bizflag>0</bizflag>\n\t<mediaeditcontent />\n\t<sec_msg_node>\n\t\t<uuid>84db83d3bce1b51b4bee114ad2415ab9_</uuid>\n\t\t<risk-file-flag />\n\t\t<risk-file-md5-list />\n\t</sec_msg_node>\n\t<silence>1</silence>\n\t<membercount>146</membercount>\n\t<signature>N0_V1_ha1R0mOJ|v1_4KK5ziiU</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'NewMsgId': 2901305557148980791, 'MsgSeq': 871390652}
2025-07-22 11:37:17 | INFO | 收到图片消息: 消息ID:1859161633 来自:27852221909@chatroom 发送人:wxid_x4s6k999g6qg22 XML:<?xml version="1.0"?><msg><img aeskey="e667e22b06d2939d2cf88633785a5b28" encryver="1" cdnthumbaeskey="e667e22b06d2939d2cf88633785a5b28" cdnthumburl="3057020100044b30490201000204025e8c6802032f54690204619999db0204687f0778042435353334623734322d663564372d343335342d616231352d373663303161336632626664020405290a020201000405004c57c100" cdnthumblength="3705" cdnthumbheight="120" cdnthumbwidth="67" cdnmidheight="0" cdnmidwidth="0" cdnhdheight="0" cdnhdwidth="0" cdnmidimgurl="3057020100044b30490201000204025e8c6802032f54690204619999db0204687f0778042435353334623734322d663564372d343335342d616231352d373663303161336632626664020405290a020201000405004c57c100" length="75635" md5="14e6a6ca349bcec6683c3a3d4da4dd50" hevc_mid_size="75635"><secHashInfoBase64>eyJwaGFzaCI6ImQyMzIzMzEwMTIwMDAwMDAiLCJwZHFoYXNoIjoiY2ViMzY2NjMyYjZjMWIzYTRlY2I5Y2NhNmNlZDQ0ZWViYjYzYjM2Mjg4ZGMxODg4MThlNzBjYTEyNzFhYzczYiJ9</secHashInfoBase64><live><duration>0</duration><size>0</size><md5 /><fileid /><hdsize>0</hdsize><hdmd5 /><hdfileid /><stillimagetimems>0</stillimagetimems></live></img><platform_signature /><imgdatahash /><ImgSourceInfo><ImgSourceUrl /><BizType>0</BizType></ImgSourceInfo></msg>
2025-07-22 11:37:17 | INFO | [ImageEcho] 保存图片信息成功，当前群 27852221909@chatroom 已存储 5 张图片
2025-07-22 11:37:17 | INFO | [TimerTask] 缓存图片消息: 1859161633
2025-07-22 11:37:22 | DEBUG | 收到消息: {'MsgId': 1178501514, 'FromUserName': {'string': '27852221909@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 1, 'Content': {'string': 'wxid_x4s6k999g6qg22:\n服了这都过不了'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1753155454, 'MsgSource': '<msgsource>\n\t<bizflag>0</bizflag>\n\t<pua>1</pua>\n\t<eggIncluded>1</eggIncluded>\n\t<silence>1</silence>\n\t<membercount>146</membercount>\n\t<signature>N0_V1_fmHTZ0Ah|v1_EIXlKcuP</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'NewMsgId': 2829194429601437469, 'MsgSeq': 871390653}
2025-07-22 11:37:22 | INFO | 收到文本消息: 消息ID:1178501514 来自:27852221909@chatroom 发送人:wxid_x4s6k999g6qg22 @:[] 内容:服了这都过不了
2025-07-22 11:37:22 | DEBUG | 处理消息内容: '服了这都过不了'
2025-07-22 11:37:22 | DEBUG | 消息内容 '服了这都过不了' 不匹配任何命令，忽略
2025-07-22 11:37:27 | DEBUG | 收到消息: {'MsgId': 1466455688, 'FromUserName': {'string': '27852221909@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 1, 'Content': {'string': 'wxid_ugv5ryus4gz622:\n行行行 你用名词'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1753155459, 'MsgSource': '<msgsource>\n\t<bizflag>0</bizflag>\n\t<pua>1</pua>\n\t<eggIncluded>1</eggIncluded>\n\t<silence>1</silence>\n\t<membercount>146</membercount>\n\t<signature>N0_V1_xdlwmSMj|v1_SVya1NkB</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'NewMsgId': 6863291462587930228, 'MsgSeq': 871390654}
2025-07-22 11:37:27 | INFO | 收到文本消息: 消息ID:1466455688 来自:27852221909@chatroom 发送人:wxid_ugv5ryus4gz622 @:[] 内容:行行行 你用名词
2025-07-22 11:37:27 | DEBUG | 处理消息内容: '行行行 你用名词'
2025-07-22 11:37:27 | DEBUG | 消息内容 '行行行 你用名词' 不匹配任何命令，忽略
2025-07-22 11:37:33 | DEBUG | 收到消息: {'MsgId': 116568049, 'FromUserName': {'string': '27852221909@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 1, 'Content': {'string': 'wxid_ugv5ryus4gz622:\n我用土词'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1753155465, 'MsgSource': '<msgsource>\n\t<bizflag>0</bizflag>\n\t<pua>1</pua>\n\t<eggIncluded>1</eggIncluded>\n\t<silence>1</silence>\n\t<membercount>146</membercount>\n\t<signature>N0_V1_UriqNk7u|v1_77FI7kOg</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'NewMsgId': 6369409146193543146, 'MsgSeq': 871390655}
2025-07-22 11:37:33 | INFO | 收到文本消息: 消息ID:116568049 来自:27852221909@chatroom 发送人:wxid_ugv5ryus4gz622 @:[] 内容:我用土词
2025-07-22 11:37:33 | DEBUG | 处理消息内容: '我用土词'
2025-07-22 11:37:33 | DEBUG | 消息内容 '我用土词' 不匹配任何命令，忽略
2025-07-22 11:38:04 | DEBUG | 收到消息: {'MsgId': 1543384041, 'FromUserName': {'string': '27852221909@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 47, 'Content': {'string': 'wxid_idzryo4rneok22:\n<msg><emoji fromusername="wxid_idzryo4rneok22" tousername="27852221909@chatroom" type="2" idbuffer="media:0_0" md5="2ddd14ca239e1bd311c3c42fc6f6669f" len="9993" productid="" androidmd5="2ddd14ca239e1bd311c3c42fc6f6669f" androidlen="9993" s60v3md5="2ddd14ca239e1bd311c3c42fc6f6669f" s60v3len="9993" s60v5md5="2ddd14ca239e1bd311c3c42fc6f6669f" s60v5len="9993" cdnurl="http://vweixinf.tc.qq.com/110/20401/stodownload?m=2ddd14ca239e1bd311c3c42fc6f6669f&amp;filekey=3043020101042f302d02016e040253480420326464643134636132333965316264333131633363343266633666363636396602022709040d00000004627466730000000132&amp;hy=SH&amp;storeid=264e8cabd0005dcef5ee423ae0000006e01004fb153482858abc1e65da6b99&amp;ef=1&amp;bizid=1022" designerid="" thumburl="" encrypturl="http://vweixinf.tc.qq.com/110/20402/stodownload?m=ec1a38b58c0e37b225b8fca02e048e78&amp;filekey=3043020101042f302d02016e040253480420656331613338623538633065333762323235623866636130326530343865373802022710040d00000004627466730000000132&amp;hy=SH&amp;storeid=264e8cabd00066a025ee423ae0000006e02004fb253482858abc1e65da6ba2&amp;ef=2&amp;bizid=1022" aeskey="09bb2de2f71a4559a080e0f3eee2c164" externurl="http://vweixinf.tc.qq.com/110/20403/stodownload?m=6d591dcf8b22bbde0c87b1d936cf9463&amp;filekey=3043020101042f302d02016e0402534804203664353931646366386232326262646530633837623164393336636639343633020210a0040d00000004627466730000000132&amp;hy=SH&amp;storeid=264e8cabd0006f5bc5ee423ae0000006e03004fb353482858abc1e65da6ba9&amp;ef=3&amp;bizid=1022" externmd5="d750613d0bd80d7970374fabc9f0ffc2" width="300" height="300" tpurl="" tpauthkey="" attachedtext="" attachedtextcolor="" lensid="" emojiattr="" linkid="" desc=""></emoji></msg>'}, 'Status': 3, 'ImgStatus': 2, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1753155496, 'MsgSource': '<msgsource>\n\t<silence>1</silence>\n\t<membercount>146</membercount>\n\t<signature>N0_V1_QuwarVfg|v1_FAe9HeZ6</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'NewMsgId': 7955224975507596063, 'MsgSeq': 871390656}
2025-07-22 11:38:04 | INFO | 收到表情消息: 消息ID:1543384041 来自:27852221909@chatroom 发送人:wxid_idzryo4rneok22 MD5:2ddd14ca239e1bd311c3c42fc6f6669f 大小:9993
2025-07-22 11:38:04 | DEBUG | [DoubaoImageRecognition] 缓存表情消息: 7955224975507596063
2025-07-22 11:39:37 | DEBUG | 收到消息: {'MsgId': 1546394273, 'FromUserName': {'string': '47325400669@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 3, 'Content': {'string': 'wxid_p60yfpl5zg2m29:\n<?xml version="1.0"?>\n<msg>\n\t<img aeskey="6c7a62626774796767657668776a6374" encryver="0" cdnthumbaeskey="6c7a62626774796767657668776a6374" cdnthumburl="3057020100044b30490201000204c9f76c8702033d14ba02049b02ff9d0204687f0804042437663031613862392d313862352d346336382d386633662d3965353062626565363231650204052828010201000405004c543e00e8963673" cdnthumblength="4843" cdnthumbheight="99" cdnthumbwidth="100" cdnmidheight="0" cdnmidwidth="0" cdnhdheight="0" cdnhdwidth="0" cdnmidimgurl="3057020100044b30490201000204c9f76c8702033d14ba02049b02ff9d0204687f0804042437663031613862392d313862352d346336382d386633662d3965353062626565363231650204052828010201000405004c543e00e8963673" length="6466" cdnbigimgurl="3057020100044b30490201000204c9f76c8702033d14ba02049b02ff9d0204687f0804042437663031613862392d313862352d346336382d386633662d3965353062626565363231650204052828010201000405004c543e00e8963673" hdlength="6362" md5="486ae5fdd40bfe4b088bf60fd2aeab96">\n\t\t<secHashInfoBase64 />\n\t\t<live>\n\t\t\t<duration>0</duration>\n\t\t\t<size>0</size>\n\t\t\t<md5 />\n\t\t\t<fileid />\n\t\t\t<hdsize>0</hdsize>\n\t\t\t<hdmd5 />\n\t\t\t<hdfileid />\n\t\t\t<stillimagetimems>0</stillimagetimems>\n\t\t</live>\n\t</img>\n\t<platform_signature />\n\t<imgdatahash />\n\t<ImgSourceInfo>\n\t\t<ImgSourceUrl />\n\t\t<BizType>0</BizType>\n\t</ImgSourceInfo>\n</msg>\n'}, 'Status': 3, 'ImgStatus': 2, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1753155588, 'MsgSource': '<msgsource>\n\t<sec_msg_node>\n\t\t<uuid>80a74ab83383fd2d529547462af99f1e_</uuid>\n\t\t<risk-file-flag />\n\t\t<risk-file-md5-list />\n\t</sec_msg_node>\n\t<silence>0</silence>\n\t<membercount>219</membercount>\n\t<signature>N0_V1_1buHObXz|v1_1D0HVvwo</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'PushContent': '小小x在群聊中发了一张图片', 'NewMsgId': 5447065510640456143, 'MsgSeq': 871390657}
2025-07-22 11:39:37 | INFO | 收到图片消息: 消息ID:1546394273 来自:47325400669@chatroom 发送人:wxid_p60yfpl5zg2m29 XML:<?xml version="1.0"?><msg><img aeskey="6c7a62626774796767657668776a6374" encryver="0" cdnthumbaeskey="6c7a62626774796767657668776a6374" cdnthumburl="3057020100044b30490201000204c9f76c8702033d14ba02049b02ff9d0204687f0804042437663031613862392d313862352d346336382d386633662d3965353062626565363231650204052828010201000405004c543e00e8963673" cdnthumblength="4843" cdnthumbheight="99" cdnthumbwidth="100" cdnmidheight="0" cdnmidwidth="0" cdnhdheight="0" cdnhdwidth="0" cdnmidimgurl="3057020100044b30490201000204c9f76c8702033d14ba02049b02ff9d0204687f0804042437663031613862392d313862352d346336382d386633662d3965353062626565363231650204052828010201000405004c543e00e8963673" length="6466" cdnbigimgurl="3057020100044b30490201000204c9f76c8702033d14ba02049b02ff9d0204687f0804042437663031613862392d313862352d346336382d386633662d3965353062626565363231650204052828010201000405004c543e00e8963673" hdlength="6362" md5="486ae5fdd40bfe4b088bf60fd2aeab96"><secHashInfoBase64 /><live><duration>0</duration><size>0</size><md5 /><fileid /><hdsize>0</hdsize><hdmd5 /><hdfileid /><stillimagetimems>0</stillimagetimems></live></img><platform_signature /><imgdatahash /><ImgSourceInfo><ImgSourceUrl /><BizType>0</BizType></ImgSourceInfo></msg>
2025-07-22 11:39:37 | INFO | [ImageEcho] 保存图片信息成功，当前群 47325400669@chatroom 已存储 5 张图片
2025-07-22 11:39:37 | INFO | [TimerTask] 缓存图片消息: 1546394273
2025-07-22 11:39:57 | DEBUG | 收到消息: {'MsgId': 1913164573, 'FromUserName': {'string': '27852221909@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 1, 'Content': {'string': 'qianting1731076232:\n[脸红][脸红][脸红][脸红][脸红][脸红][脸红]'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1753155609, 'MsgSource': '<msgsource>\n\t<pua>1</pua>\n\t<eggIncluded>1</eggIncluded>\n\t<silence>1</silence>\n\t<membercount>146</membercount>\n\t<signature>N0_V1_IowwRmPO|v1_wT8LHjXe</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'NewMsgId': 3377416524369045009, 'MsgSeq': 871390658}
2025-07-22 11:39:57 | INFO | 收到表情消息: 消息ID:1913164573 来自:27852221909@chatroom 发送人:qianting1731076232 @:[] 内容:[脸红][脸红][脸红][脸红][脸红][脸红][脸红]
2025-07-22 11:39:57 | DEBUG | [DoubaoImageRecognition] 缓存表情消息: 3377416524369045009
2025-07-22 11:40:03 | DEBUG | 收到消息: {'MsgId': 390313584, 'FromUserName': {'string': '27852221909@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 1, 'Content': {'string': 'qianting1731076232:\n完犊子'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1753155615, 'MsgSource': '<msgsource>\n\t<pua>1</pua>\n\t<eggIncluded>1</eggIncluded>\n\t<silence>1</silence>\n\t<membercount>146</membercount>\n\t<signature>N0_V1_Ulxf/nBC|v1_sNpPanzr</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'NewMsgId': 6926112836241857567, 'MsgSeq': 871390659}
2025-07-22 11:40:03 | INFO | 收到文本消息: 消息ID:390313584 来自:27852221909@chatroom 发送人:qianting1731076232 @:[] 内容:完犊子
2025-07-22 11:40:03 | DEBUG | 处理消息内容: '完犊子'
2025-07-22 11:40:03 | DEBUG | 消息内容 '完犊子' 不匹配任何命令，忽略
2025-07-22 11:40:14 | DEBUG | 收到消息: {'MsgId': 633157875, 'FromUserName': {'string': '27852221909@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 1, 'Content': {'string': 'tianen532965049:\n6-6有割哭 '}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1753155626, 'MsgSource': '<msgsource>\n\t<sec_msg_node>\n\t\t<alnode>\n\t\t\t<fr>1</fr>\n\t\t</alnode>\n\t</sec_msg_node>\n\t<pua>1</pua>\n\t<silence>1</silence>\n\t<membercount>146</membercount>\n\t<signature>N0_V1_W1rAsI3s|v1_ySj5y/JA</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'NewMsgId': 6453729110686740992, 'MsgSeq': 871390660}
2025-07-22 11:40:14 | INFO | 收到文本消息: 消息ID:633157875 来自:27852221909@chatroom 发送人:tianen532965049 @:[] 内容:6-6有割哭 
2025-07-22 11:40:14 | DEBUG | 处理消息内容: '6-6有割哭'
2025-07-22 11:40:14 | DEBUG | 消息内容 '6-6有割哭' 不匹配任何命令，忽略
2025-07-22 11:40:16 | DEBUG | 收到消息: {'MsgId': 443232653, 'FromUserName': {'string': '27852221909@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 1, 'Content': {'string': 'qianting1731076232:\n不过了妈的'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1753155627, 'MsgSource': '<msgsource>\n\t<pua>1</pua>\n\t<eggIncluded>1</eggIncluded>\n\t<silence>1</silence>\n\t<membercount>146</membercount>\n\t<signature>N0_V1_wwc/Tjoz|v1_IBBFtseT</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'NewMsgId': 7841265127361533557, 'MsgSeq': 871390661}
2025-07-22 11:40:16 | INFO | 收到文本消息: 消息ID:443232653 来自:27852221909@chatroom 发送人:qianting1731076232 @:[] 内容:不过了妈的
2025-07-22 11:40:16 | DEBUG | 处理消息内容: '不过了妈的'
2025-07-22 11:40:16 | DEBUG | 消息内容 '不过了妈的' 不匹配任何命令，忽略
2025-07-22 11:40:18 | DEBUG | 收到消息: {'MsgId': 52998569, 'FromUserName': {'string': '27852221909@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 1, 'Content': {'string': 'tianen532965049:\n温馨提示'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1753155630, 'MsgSource': '<msgsource>\n\t<sec_msg_node>\n\t\t<alnode>\n\t\t\t<fr>1</fr>\n\t\t</alnode>\n\t</sec_msg_node>\n\t<pua>1</pua>\n\t<silence>1</silence>\n\t<membercount>146</membercount>\n\t<signature>N0_V1_hXa90awD|v1_Q/XbqmR0</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'NewMsgId': 4878788115155122340, 'MsgSeq': 871390662}
2025-07-22 11:40:18 | INFO | 收到文本消息: 消息ID:52998569 来自:27852221909@chatroom 发送人:tianen532965049 @:[] 内容:温馨提示
2025-07-22 11:40:18 | DEBUG | 处理消息内容: '温馨提示'
2025-07-22 11:40:18 | DEBUG | 消息内容 '温馨提示' 不匹配任何命令，忽略
2025-07-22 11:40:34 | DEBUG | 收到消息: {'MsgId': 1574624299, 'FromUserName': {'string': '27852221909@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 1, 'Content': {'string': 'qianting1731076232:\n破游戏'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1753155646, 'MsgSource': '<msgsource>\n\t<pua>1</pua>\n\t<eggIncluded>1</eggIncluded>\n\t<silence>1</silence>\n\t<membercount>146</membercount>\n\t<signature>N0_V1_U5jvjBL9|v1_HLjIfOHw</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'NewMsgId': 4515512125450773843, 'MsgSeq': 871390663}
2025-07-22 11:40:34 | INFO | 收到文本消息: 消息ID:1574624299 来自:27852221909@chatroom 发送人:qianting1731076232 @:[] 内容:破游戏
2025-07-22 11:40:34 | DEBUG | 处理消息内容: '破游戏'
2025-07-22 11:40:34 | DEBUG | 消息内容 '破游戏' 不匹配任何命令，忽略
2025-07-22 11:40:37 | DEBUG | 收到消息: {'MsgId': 936000557, 'FromUserName': {'string': '27852221909@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 1, 'Content': {'string': 'wxid_x4s6k999g6qg22:\n@ᬐ婷宝.\u2005等修复'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1753155649, 'MsgSource': '<msgsource>\n\t<atuserlist>qianting1731076232</atuserlist>\n\t<bizflag>0</bizflag>\n\t<pua>1</pua>\n\t<eggIncluded>1</eggIncluded>\n\t<silence>1</silence>\n\t<membercount>146</membercount>\n\t<signature>N0_V1_EAoa9eYs|v1_db5b/kET</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'NewMsgId': 8487706750023531729, 'MsgSeq': 871390664}
2025-07-22 11:40:37 | INFO | 收到文本消息: 消息ID:936000557 来自:27852221909@chatroom 发送人:wxid_x4s6k999g6qg22 @:['qianting1731076232'] 内容:@ᬐ婷宝. 等修复
2025-07-22 11:40:37 | DEBUG | 处理消息内容: '@ᬐ婷宝. 等修复'
2025-07-22 11:40:37 | DEBUG | 消息内容 '@ᬐ婷宝. 等修复' 不匹配任何命令，忽略
2025-07-22 11:40:41 | DEBUG | 收到消息: {'MsgId': 1678679786, 'FromUserName': {'string': '27852221909@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 1, 'Content': {'string': 'qianting1731076232:\n我在卸载它'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1753155653, 'MsgSource': '<msgsource>\n\t<pua>1</pua>\n\t<eggIncluded>1</eggIncluded>\n\t<silence>1</silence>\n\t<membercount>146</membercount>\n\t<signature>N0_V1_QtUuJZ+g|v1_GbTZmKkb</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'NewMsgId': 3610055194722931621, 'MsgSeq': 871390665}
2025-07-22 11:40:41 | INFO | 收到文本消息: 消息ID:1678679786 来自:27852221909@chatroom 发送人:qianting1731076232 @:[] 内容:我在卸载它
2025-07-22 11:40:41 | DEBUG | 处理消息内容: '我在卸载它'
2025-07-22 11:40:41 | DEBUG | 消息内容 '我在卸载它' 不匹配任何命令，忽略
2025-07-22 11:40:43 | DEBUG | 收到消息: {'MsgId': 1829874983, 'FromUserName': {'string': '27852221909@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 47, 'Content': {'string': 'wxid_ugv5ryus4gz622:\n<msg><emoji fromusername="wxid_ugv5ryus4gz622" tousername="27852221909@chatroom" type="3" idbuffer="media:0_0" md5="3a02e18aed72430b9875994f4c30edc0" len="21149" productid="" androidmd5="3a02e18aed72430b9875994f4c30edc0" androidlen="21149" s60v3md5="3a02e18aed72430b9875994f4c30edc0" s60v3len="21149" s60v5md5="3a02e18aed72430b9875994f4c30edc0" s60v5len="21149" cdnurl="http://vweixinf.tc.qq.com/110/20401/stodownload?m=3a02e18aed72430b9875994f4c30edc0&amp;filekey=3043020101042f302d02016e0402535a042033613032653138616564373234333062393837353939346634633330656463300202529d040d00000004627466730000000132&amp;hy=SZ&amp;storeid=266d56dbf0005a4da5d0f60e70000006e01004fb1535a1f6f1011568555813&amp;ef=1&amp;bizid=1022" designerid="" thumburl="" encrypturl="http://vweixinf.tc.qq.com/110/20402/stodownload?m=6962cf64637171229f3c1a9c18a0abcf&amp;filekey=3043020101042f302d02016e0402535a04203639363263663634363337313731323239663363316139633138613061626366020252a0040d00000004627466730000000132&amp;hy=SZ&amp;storeid=266d56dbf0005fa8d5d0f60e70000006e02004fb2535a1f6f1011568555826&amp;ef=2&amp;bizid=1022" aeskey="3f03e498b02f46fe8ce127c08f3f9dcc" externurl="http://vweixinf.tc.qq.com/110/20403/stodownload?m=bdb2ad2567a9ec7dc96d43e0e10feb4c&amp;filekey=3043020101042f302d02016e0402535a0420626462326164323536376139656337646339366434336530653130666562346302021fb0040d00000004627466730000000132&amp;hy=SZ&amp;storeid=266d56dbf0006456a5d0f60e70000006e03004fb3535a1f6f1011568555830&amp;ef=3&amp;bizid=1022" externmd5="acf67c4949538269d933ba9b8bb93035" width="542" height="542" tpurl="" tpauthkey="" attachedtext="" attachedtextcolor="" lensid="" emojiattr="" linkid="" desc=""></emoji><gameext type="0" content="0"></gameext></msg>'}, 'Status': 3, 'ImgStatus': 2, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1753155655, 'MsgSource': '<msgsource>\n\t<silence>1</silence>\n\t<membercount>146</membercount>\n\t<signature>N0_V1_WrqcS3mt|v1_hEtGeEoS</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'NewMsgId': 596894929271458240, 'MsgSeq': 871390666}
2025-07-22 11:40:43 | INFO | 收到表情消息: 消息ID:1829874983 来自:27852221909@chatroom 发送人:wxid_ugv5ryus4gz622 MD5:3a02e18aed72430b9875994f4c30edc0 大小:21149
2025-07-22 11:40:43 | DEBUG | [DoubaoImageRecognition] 缓存表情消息: 596894929271458240
2025-07-22 11:40:54 | DEBUG | 收到消息: {'MsgId': 1805755417, 'FromUserName': {'string': '27852221909@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 1, 'Content': {'string': 'wxid_x4s6k999g6qg22:\n4-6都过不了了'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1753155666, 'MsgSource': '<msgsource>\n\t<bizflag>0</bizflag>\n\t<pua>1</pua>\n\t<eggIncluded>1</eggIncluded>\n\t<silence>1</silence>\n\t<membercount>146</membercount>\n\t<signature>N0_V1_2ssyUfxQ|v1_3/NVNx99</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'NewMsgId': 3603688447625464695, 'MsgSeq': 871390667}
2025-07-22 11:40:54 | INFO | 收到文本消息: 消息ID:1805755417 来自:27852221909@chatroom 发送人:wxid_x4s6k999g6qg22 @:[] 内容:4-6都过不了了
2025-07-22 11:40:54 | DEBUG | 处理消息内容: '4-6都过不了了'
2025-07-22 11:40:54 | DEBUG | 消息内容 '4-6都过不了了' 不匹配任何命令，忽略
2025-07-22 11:42:49 | DEBUG | 收到消息: {'MsgId': 1224819894, 'FromUserName': {'string': '27852221909@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 49, 'Content': {'string': 'wxid_ubbh6q832tcs21:\n<?xml version="1.0"?>\n<msg>\n\t<appmsg appid="" sdkver="0">\n\t\t<title>666</title>\n\t\t<des />\n\t\t<username />\n\t\t<action>view</action>\n\t\t<type>57</type>\n\t\t<showtype>0</showtype>\n\t\t<content />\n\t\t<url />\n\t\t<lowurl />\n\t\t<forwardflag>0</forwardflag>\n\t\t<dataurl />\n\t\t<lowdataurl />\n\t\t<contentattr>0</contentattr>\n\t\t<streamvideo>\n\t\t\t<streamvideourl />\n\t\t\t<streamvideototaltime>0</streamvideototaltime>\n\t\t\t<streamvideotitle />\n\t\t\t<streamvideowording />\n\t\t\t<streamvideoweburl />\n\t\t\t<streamvideothumburl />\n\t\t\t<streamvideoaduxinfo />\n\t\t\t<streamvideopublishid />\n\t\t</streamvideo>\n\t\t<canvasPageItem>\n\t\t\t<canvasPageXml><![CDATA[]]></canvasPageXml>\n\t\t</canvasPageItem>\n\t\t<refermsg>\n\t\t\t<type>1</type>\n\t\t\t<svrid>3610055194722931621</svrid>\n\t\t\t<fromusr>27852221909@chatroom</fromusr>\n\t\t\t<chatusr>qianting1731076232</chatusr>\n\t\t\t<displayname>ᬐ婷宝.</displayname>\n\t\t\t<msgsource>&lt;msgsource&gt;\n\t&lt;pua&gt;1&lt;/pua&gt;\n\t&lt;eggIncluded&gt;1&lt;/eggIncluded&gt;\n\t&lt;silence&gt;1&lt;/silence&gt;\n\t&lt;membercount&gt;146&lt;/membercount&gt;\n\t&lt;signature&gt;N0_V1_RjL1UfHe|v1_v2uItW8d&lt;/signature&gt;\n\t&lt;tmp_node&gt;\n\t\t&lt;publisher-id&gt;&lt;/publisher-id&gt;\n\t&lt;/tmp_node&gt;\n&lt;/msgsource&gt;\n</msgsource>\n\t\t\t<content>\n我在卸载它</content>\n\t\t\t<strid />\n\t\t\t<createtime>1753155653</createtime>\n\t\t</refermsg>\n\t\t<appattach>\n\t\t\t<totallen>0</totallen>\n\t\t\t<attachid />\n\t\t\t<cdnattachurl />\n\t\t\t<emoticonmd5 />\n\t\t\t<aeskey />\n\t\t\t<fileext />\n\t\t\t<islargefilemsg>0</islargefilemsg>\n\t\t</appattach>\n\t\t<extinfo />\n\t\t<androidsource>0</androidsource>\n\t\t<thumburl />\n\t\t<mediatagname />\n\t\t<messageaction><![CDATA[]]></messageaction>\n\t\t<messageext><![CDATA[]]></messageext>\n\t\t<emoticongift>\n\t\t\t<packageflag>0</packageflag>\n\t\t\t<packageid />\n\t\t</emoticongift>\n\t\t<emoticonshared>\n\t\t\t<packageflag>0</packageflag>\n\t\t\t<packageid />\n\t\t</emoticonshared>\n\t\t<designershared>\n\t\t\t<designeruin>0</designeruin>\n\t\t\t<designername>null</designername>\n\t\t\t<designerrediretcturl><![CDATA[null]]></designerrediretcturl>\n\t\t</designershared>\n\t\t<emotionpageshared>\n\t\t\t<tid>0</tid>\n\t\t\t<title>null</title>\n\t\t\t<desc>null</desc>\n\t\t\t<iconUrl><![CDATA[null]]></iconUrl>\n\t\t\t<secondUrl>null</secondUrl>\n\t\t\t<pageType>0</pageType>\n\t\t\t<setKey>null</setKey>\n\t\t</emotionpageshared>\n\t\t<webviewshared>\n\t\t\t<shareUrlOriginal />\n\t\t\t<shareUrlOpen />\n\t\t\t<jsAppId />\n\t\t\t<publisherId />\n\t\t\t<publisherReqId />\n\t\t</webviewshared>\n\t\t<template_id />\n\t\t<md5 />\n\t\t<websearch>\n\t\t\t<rec_category>0</rec_category>\n\t\t\t<channelId>0</channelId>\n\t\t</websearch>\n\t\t<weappinfo>\n\t\t\t<username />\n\t\t\t<appid />\n\t\t\t<appservicetype>0</appservicetype>\n\t\t\t<secflagforsinglepagemode>0</secflagforsinglepagemode>\n\t\t\t<videopageinfo>\n\t\t\t\t<thumbwidth>0</thumbwidth>\n\t\t\t\t<thumbheight>0</thumbheight>\n\t\t\t\t<fromopensdk>0</fromopensdk>\n\t\t\t</videopageinfo>\n\t\t</weappinfo>\n\t\t<statextstr />\n\t\t<musicShareItem>\n\t\t\t<musicDuration>0</musicDuration>\n\t\t</musicShareItem>\n\t\t<finderLiveProductShare>\n\t\t\t<finderLiveID><![CDATA[]]></finderLiveID>\n\t\t\t<finderUsername><![CDATA[]]></finderUsername>\n\t\t\t<finderObjectID><![CDATA[]]></finderObjectID>\n\t\t\t<finderNonceID><![CDATA[]]></finderNonceID>\n\t\t\t<liveStatus><![CDATA[]]></liveStatus>\n\t\t\t<appId><![CDATA[]]></appId>\n\t\t\t<pagePath><![CDATA[]]></pagePath>\n\t\t\t<productId><![CDATA[]]></productId>\n\t\t\t<coverUrl><![CDATA[]]></coverUrl>\n\t\t\t<productTitle><![CDATA[]]></productTitle>\n\t\t\t<marketPrice><![CDATA[0]]></marketPrice>\n\t\t\t<sellingPrice><![CDATA[0]]></sellingPrice>\n\t\t\t<platformHeadImg><![CDATA[]]></platformHeadImg>\n\t\t\t<platformName><![CDATA[]]></platformName>\n\t\t\t<shopWindowId><![CDATA[]]></shopWindowId>\n\t\t\t<flashSalePrice><![CDATA[0]]></flashSalePrice>\n\t\t\t<flashSaleEndTime><![CDATA[0]]></flashSaleEndTime>\n\t\t\t<ecSource><![CDATA[]]></ecSource>\n\t\t\t<sellingPriceWording><![CDATA[]]></sellingPriceWording>\n\t\t\t<platformIconURL><![CDATA[]]></platformIconURL>\n\t\t\t<firstProductTagURL><![CDATA[]]></firstProductTagURL>\n\t\t\t<firstProductTagAspectRatioString><![CDATA[0.0]]></firstProductTagAspectRatioString>\n\t\t\t<secondProductTagURL><![CDATA[]]></secondProductTagURL>\n\t\t\t<secondProductTagAspectRatioString><![CDATA[0.0]]></secondProductTagAspectRatioString>\n\t\t\t<firstGuaranteeWording><![CDATA[]]></firstGuaranteeWording>\n\t\t\t<secondGuaranteeWording><![CDATA[]]></secondGuaranteeWording>\n\t\t\t<thirdGuaranteeWording><![CDATA[]]></thirdGuaranteeWording>\n\t\t\t<isPriceBeginShow>false</isPriceBeginShow>\n\t\t\t<lastGMsgID><![CDATA[]]></lastGMsgID>\n\t\t\t<promoterKey><![CDATA[]]></promoterKey>\n\t\t\t<discountWording><![CDATA[]]></discountWording>\n\t\t\t<priceSuffixDescription><![CDATA[]]></priceSuffixDescription>\n\t\t\t<productCardKey><![CDATA[]]></productCardKey>\n\t\t\t<isWxShop><![CDATA[]]></isWxShop>\n\t\t\t<brandIconUrl><![CDATA[]]></brandIconUrl>\n\t\t\t<showBoxItemStringList />\n\t\t</finderLiveProductShare>\n\t\t<finderOrder>\n\t\t\t<appID><![CDATA[]]></appID>\n\t\t\t<orderID><![CDATA[]]></orderID>\n\t\t\t<path><![CDATA[]]></path>\n\t\t\t<priceWording><![CDATA[]]></priceWording>\n\t\t\t<stateWording><![CDATA[]]></stateWording>\n\t\t\t<productImageURL><![CDATA[]]></productImageURL>\n\t\t\t<products><![CDATA[]]></products>\n\t\t\t<productsCount><![CDATA[0]]></productsCount>\n\t\t\t<orderType><![CDATA[0]]></orderType>\n\t\t\t<newPriceWording><![CDATA[]]></newPriceWording>\n\t\t\t<newStateWording><![CDATA[]]></newStateWording>\n\t\t\t<useNewWording><![CDATA[0]]></useNewWording>\n\t\t</finderOrder>\n\t\t<finderShopWindowShare>\n\t\t\t<finderUsername><![CDATA[]]></finderUsername>\n\t\t\t<avatar><![CDATA[]]></avatar>\n\t\t\t<nickname><![CDATA[]]></nickname>\n\t\t\t<commodityInStockCount><![CDATA[]]></commodityInStockCount>\n\t\t\t<appId><![CDATA[]]></appId>\n\t\t\t<path><![CDATA[]]></path>\n\t\t\t<appUsername><![CDATA[]]></appUsername>\n\t\t\t<query><![CDATA[]]></query>\n\t\t\t<liteAppId><![CDATA[]]></liteAppId>\n\t\t\t<liteAppPath><![CDATA[]]></liteAppPath>\n\t\t\t<liteAppQuery><![CDATA[]]></liteAppQuery>\n\t\t\t<platformTagURL><![CDATA[]]></platformTagURL>\n\t\t\t<saleWording><![CDATA[]]></saleWording>\n\t\t\t<lastGMsgID><![CDATA[]]></lastGMsgID>\n\t\t\t<profileTypeWording><![CDATA[]]></profileTypeWording>\n\t\t\t<saleWordingExtra><![CDATA[]]></saleWordingExtra>\n\t\t\t<isWxShop><![CDATA[]]></isWxShop>\n\t\t\t<platformIconUrl><![CDATA[]]></platformIconUrl>\n\t\t\t<brandIconUrl><![CDATA[]]></brandIconUrl>\n\t\t\t<description><![CDATA[]]></description>\n\t\t\t<backgroundUrl><![CDATA[]]></backgroundUrl>\n\t\t\t<darkModePlatformIconUrl><![CDATA[]]></darkModePlatformIconUrl>\n\t\t\t<reputationInfo>\n\t\t\t\t<hasReputationInfo>0</hasReputationInfo>\n\t\t\t\t<reputationScore>0</reputationScore>\n\t\t\t\t<reputationWording />\n\t\t\t\t<reputationTextColor />\n\t\t\t\t<reputationLevelWording />\n\t\t\t\t<reputationBackgroundColor />\n\t\t\t</reputationInfo>\n\t\t\t<productImageURLList />\n\t\t</finderShopWindowShare>\n\t\t<findernamecard>\n\t\t\t<username />\n\t\t\t<avatar><![CDATA[]]></avatar>\n\t\t\t<nickname />\n\t\t\t<auth_job />\n\t\t\t<auth_icon>0</auth_icon>\n\t\t\t<auth_icon_url />\n\t\t\t<ecSource><![CDATA[]]></ecSource>\n\t\t\t<lastGMsgID><![CDATA[]]></lastGMsgID>\n\t\t</findernamecard>\n\t\t<finderGuarantee>\n\t\t\t<scene><![CDATA[0]]></scene>\n\t\t</finderGuarantee>\n\t\t<directshare>0</directshare>\n\t\t<gamecenter>\n\t\t\t<namecard>\n\t\t\t\t<iconUrl />\n\t\t\t\t<name />\n\t\t\t\t<desc />\n\t\t\t\t<tail />\n\t\t\t\t<jumpUrl />\n\t\t\t</namecard>\n\t\t</gamecenter>\n\t\t<patMsg>\n\t\t\t<chatUser />\n\t\t\t<records>\n\t\t\t\t<recordNum>0</recordNum>\n\t\t\t</records>\n\t\t</patMsg>\n\t\t<secretmsg>\n\t\t\t<issecretmsg>0</issecretmsg>\n\t\t</secretmsg>\n\t\t<referfromscene>0</referfromscene>\n\t\t<gameshare>\n\t\t\t<liteappext>\n\t\t\t\t<liteappbizdata />\n\t\t\t\t<priority>0</priority>\n\t\t\t</liteappext>\n\t\t\t<appbrandext>\n\t\t\t\t<litegameinfo />\n\t\t\t\t<priority>-1</priority>\n\t\t\t</appbrandext>\n\t\t\t<gameshareid />\n\t\t\t<sharedata />\n\t\t\t<isvideo>0</isvideo>\n\t\t\t<duration>-1</duration>\n\t\t\t<isexposed>0</isexposed>\n\t\t\t<readtext />\n\t\t</gameshare>\n\t\t<mpsharetrace>\n\t\t\t<hasfinderelement>0</hasfinderelement>\n\t\t\t<lastgmsgid />\n\t\t</mpsharetrace>\n\t\t<wxgamecard>\n\t\t\t<framesetname />\n\t\t\t<mbcarddata />\n\t\t\t<minpkgversion />\n\t\t\t<clientextinfo />\n\t\t\t<mbcardheight>0</mbcardheight>\n\t\t\t<isoldversion>0</isoldversion>\n\t\t</wxgamecard>\n\t\t<liteapp>\n\t\t\t<id>null</id>\n\t\t\t<path />\n\t\t\t<query />\n\t\t\t<istransparent>0</istransparent>\n\t\t\t<hideicon>0</hideicon>\n\t\t</liteapp>\n\t\t<opensdk_share_is_modified>0</opensdk_share_is_modified>\n\t</appmsg>\n\t<fromusername>wxid_ubbh6q832tcs21</fromusername>\n\t<scene>0</scene>\n\t<appinfo>\n\t\t<version>1</version>\n\t\t<appname></appname>\n\t</appinfo>\n\t<commenturl></commenturl>\n</msg>\n'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1753155781, 'MsgSource': '<msgsource>\n\t<alnode>\n\t\t<fr>4</fr>\n\t</alnode>\n\t<eggIncluded>1</eggIncluded>\n\t<sec_msg_node>\n\t\t<uuid>e0d51f40187bc17e8021f2039541c763_</uuid>\n\t\t<risk-file-flag />\n\t\t<risk-file-md5-list />\n\t</sec_msg_node>\n\t<silence>1</silence>\n\t<membercount>146</membercount>\n\t<signature>N0_V1_+xzSFUmV|v1_1z5q2Ct/</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'NewMsgId': 5748682735617321151, 'MsgSeq': 871390668}
2025-07-22 11:42:49 | DEBUG | 从群聊消息中提取发送者: wxid_ubbh6q832tcs21
2025-07-22 11:42:49 | DEBUG | 使用已解析的XML处理引用消息
2025-07-22 11:42:49 | INFO | 收到引用消息: 消息ID:1224819894 来自:27852221909@chatroom 发送人:wxid_ubbh6q832tcs21 内容:666 引用类型:1
2025-07-22 11:42:49 | INFO | [DouBaoImageToImage] 收到引用消息: 666
2025-07-22 11:42:49 | INFO | [TimerTask] 收到引用消息调试信息:
2025-07-22 11:42:49 | INFO |   - 消息内容: 666
2025-07-22 11:42:49 | INFO |   - 群组ID: 27852221909@chatroom
2025-07-22 11:42:49 | INFO |   - 发送人: wxid_ubbh6q832tcs21
2025-07-22 11:42:49 | INFO |   - 引用信息: {'MsgType': 1, 'Content': '\n我在卸载它', 'Msgid': '3610055194722931621', 'NewMsgId': '3610055194722931621', 'ToWxid': 'wxid_4usgcju5ey9q29', 'FromWxid': '27852221909@chatroom', 'Nickname': 'ᬐ婷宝.', 'MsgSource': '<msgsource>\n\t<pua>1</pua>\n\t<eggIncluded>1</eggIncluded>\n\t<silence>1</silence>\n\t<membercount>146</membercount>\n\t<signature>N0_V1_RjL1UfHe|v1_v2uItW8d</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'Createtime': '1753155653', 'SenderWxid': 'wxid_ubbh6q832tcs21'}
2025-07-22 11:42:49 | INFO |   - 引用消息ID: 
2025-07-22 11:42:49 | INFO |   - 引用消息类型: 
2025-07-22 11:42:49 | INFO |   - 引用消息内容: 
我在卸载它
2025-07-22 11:42:49 | INFO |   - 引用消息发送人: wxid_ubbh6q832tcs21
2025-07-22 11:44:39 | DEBUG | 收到消息: {'MsgId': 1546744903, 'FromUserName': {'string': '27852221909@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 49, 'Content': {'string': 'wxid_xv01lkcmn48l22:\n<?xml version="1.0"?>\n<msg>\n\t<appmsg appid="" sdkver="0">\n\t\t<title>又过不了了？</title>\n\t\t<type>57</type>\n\t\t<appattach>\n\t\t\t<cdnthumbaeskey />\n\t\t\t<aeskey />\n\t\t</appattach>\n\t\t<refermsg>\n\t\t\t<type>1</type>\n\t\t\t<svrid>3603688447625464695</svrid>\n\t\t\t<fromusr>27852221909@chatroom</fromusr>\n\t\t\t<chatusr>wxid_x4s6k999g6qg22</chatusr>\n\t\t\t<displayname>栀栀</displayname>\n\t\t\t<content>4-6都过不了了</content>\n\t\t\t<msgsource>&lt;msgsource&gt;&lt;sequence_id&gt;859754297&lt;/sequence_id&gt;\n\t&lt;bizflag&gt;0&lt;/bizflag&gt;\n\t&lt;pua&gt;1&lt;/pua&gt;\n\t&lt;eggIncluded&gt;1&lt;/eggIncluded&gt;\n\t&lt;silence&gt;0&lt;/silence&gt;\n\t&lt;membercount&gt;146&lt;/membercount&gt;\n\t&lt;signature&gt;N0_V1_toy0NuOx|v1_rcDXcJvv&lt;/signature&gt;\n\t&lt;tmp_node&gt;\n\t\t&lt;publisher-id&gt;&lt;/publisher-id&gt;\n\t&lt;/tmp_node&gt;\n&lt;/msgsource&gt;\n</msgsource>\n\t\t\t<createtime>1753155666</createtime>\n\t\t</refermsg>\n\t</appmsg>\n\t<fromusername>wxid_xv01lkcmn48l22</fromusername>\n\t<scene>0</scene>\n\t<appinfo>\n\t\t<version>1</version>\n\t\t<appname></appname>\n\t</appinfo>\n\t<commenturl></commenturl>\n</msg>\n'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1753155891, 'MsgSource': '<msgsource>\n\t<bizflag>0</bizflag>\n\t<pua>1</pua>\n\t<eggIncluded>1</eggIncluded>\n\t<sec_msg_node>\n\t\t<uuid>7fc922b6a052afdc28a7e7eeef9ce0c1_</uuid>\n\t\t<risk-file-flag />\n\t\t<risk-file-md5-list />\n\t</sec_msg_node>\n\t<silence>1</silence>\n\t<membercount>146</membercount>\n\t<signature>N0_V1_Zzoc6Y2P|v1_JMtvQas7</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'NewMsgId': 8324178918698746076, 'MsgSeq': 871390669}
2025-07-22 11:44:39 | DEBUG | 从群聊消息中提取发送者: wxid_xv01lkcmn48l22
2025-07-22 11:44:39 | DEBUG | 使用已解析的XML处理引用消息
2025-07-22 11:44:39 | INFO | 收到引用消息: 消息ID:1546744903 来自:27852221909@chatroom 发送人:wxid_xv01lkcmn48l22 内容:又过不了了？ 引用类型:1
2025-07-22 11:44:39 | INFO | [DouBaoImageToImage] 收到引用消息: 又过不了了？
2025-07-22 11:44:39 | INFO | [TimerTask] 收到引用消息调试信息:
2025-07-22 11:44:39 | INFO |   - 消息内容: 又过不了了？
2025-07-22 11:44:39 | INFO |   - 群组ID: 27852221909@chatroom
2025-07-22 11:44:39 | INFO |   - 发送人: wxid_xv01lkcmn48l22
2025-07-22 11:44:39 | INFO |   - 引用信息: {'MsgType': 1, 'Content': '4-6都过不了了', 'Msgid': '3603688447625464695', 'NewMsgId': '3603688447625464695', 'ToWxid': 'wxid_4usgcju5ey9q29', 'FromWxid': '27852221909@chatroom', 'Nickname': '栀栀', 'MsgSource': '<msgsource><sequence_id>859754297</sequence_id>\n\t<bizflag>0</bizflag>\n\t<pua>1</pua>\n\t<eggIncluded>1</eggIncluded>\n\t<silence>0</silence>\n\t<membercount>146</membercount>\n\t<signature>N0_V1_toy0NuOx|v1_rcDXcJvv</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'Createtime': '1753155666', 'SenderWxid': 'wxid_xv01lkcmn48l22'}
2025-07-22 11:44:39 | INFO |   - 引用消息ID: 
2025-07-22 11:44:39 | INFO |   - 引用消息类型: 
2025-07-22 11:44:39 | INFO |   - 引用消息内容: 4-6都过不了了
2025-07-22 11:44:39 | INFO |   - 引用消息发送人: wxid_xv01lkcmn48l22
2025-07-22 11:46:20 | DEBUG | 收到消息: {'MsgId': 1181571826, 'FromUserName': {'string': '47325400669@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 3, 'Content': {'string': 'wxid_9uwska6u4yzm22:\n<?xml version="1.0"?>\n<msg>\n\t<img aeskey="5518ce378f32f39170905072e6852baf" encryver="1" cdnthumbaeskey="5518ce378f32f39170905072e6852baf" cdnthumburl="3057020100044b30490201000204b26fc56a02032e45530204bfad016f0204687f07c0042461653063383064652d306465342d343037382d623735622d653735373938353236323762020405250a020201000405004c550700" cdnthumblength="6667" cdnthumbheight="135" cdnthumbwidth="180" cdnmidheight="0" cdnmidwidth="0" cdnhdheight="0" cdnhdwidth="0" cdnmidimgurl="3057020100044b30490201000204b26fc56a02032e45530204bfad016f0204687f07c0042461653063383064652d306465342d343037382d623735622d653735373938353236323762020405250a020201000405004c550700" length="114041" md5="46e2c15af4cab25905358da954f54484" hevc_mid_size="114041">\n\t\t<secHashInfoBase64>eyJwaGFzaCI6IjcxZjU4MGIxMTAwMDAwMDAiLCJwZHFoYXNoIjoiNzdhZDJiYTFjOGQ2OTY0NDk3MmQyMTE5N2JkMmNlNGUyNzJkMzljNDRjZmU0MjE4OWFlM2JkNDM3NzBhNDMzZSJ9</secHashInfoBase64>\n\t\t<live>\n\t\t\t<duration>0</duration>\n\t\t\t<size>0</size>\n\t\t\t<md5 />\n\t\t\t<fileid />\n\t\t\t<hdsize>0</hdsize>\n\t\t\t<hdmd5 />\n\t\t\t<hdfileid />\n\t\t\t<stillimagetimems>0</stillimagetimems>\n\t\t</live>\n\t</img>\n\t<platform_signature />\n\t<imgdatahash />\n\t<ImgSourceInfo>\n\t\t<ImgSourceUrl />\n\t\t<BizType>0</BizType>\n\t</ImgSourceInfo>\n</msg>\n'}, 'Status': 3, 'ImgStatus': 2, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1753155991, 'MsgSource': '<msgsource>\n\t<bizflag>0</bizflag>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n\t<alnode>\n\t\t<fr>5</fr>\n\t</alnode>\n\t<sec_msg_node>\n\t\t<uuid>ae93ec3b8f755b4e4883d1f227ac39f5_</uuid>\n\t\t<risk-file-flag />\n\t\t<risk-file-md5-list />\n\t</sec_msg_node>\n\t<silence>0</silence>\n\t<membercount>219</membercount>\n\t<signature>N0_V1_eSC0Zn6E|v1_FuRWZ2Cb</signature>\n</msgsource>\n', 'PushContent': 'Garson在群聊中发了一张图片', 'NewMsgId': 4012327996310807055, 'MsgSeq': 871390670}
2025-07-22 11:46:20 | INFO | 收到图片消息: 消息ID:1181571826 来自:47325400669@chatroom 发送人:wxid_9uwska6u4yzm22 XML:<?xml version="1.0"?><msg><img aeskey="5518ce378f32f39170905072e6852baf" encryver="1" cdnthumbaeskey="5518ce378f32f39170905072e6852baf" cdnthumburl="3057020100044b30490201000204b26fc56a02032e45530204bfad016f0204687f07c0042461653063383064652d306465342d343037382d623735622d653735373938353236323762020405250a020201000405004c550700" cdnthumblength="6667" cdnthumbheight="135" cdnthumbwidth="180" cdnmidheight="0" cdnmidwidth="0" cdnhdheight="0" cdnhdwidth="0" cdnmidimgurl="3057020100044b30490201000204b26fc56a02032e45530204bfad016f0204687f07c0042461653063383064652d306465342d343037382d623735622d653735373938353236323762020405250a020201000405004c550700" length="114041" md5="46e2c15af4cab25905358da954f54484" hevc_mid_size="114041"><secHashInfoBase64>eyJwaGFzaCI6IjcxZjU4MGIxMTAwMDAwMDAiLCJwZHFoYXNoIjoiNzdhZDJiYTFjOGQ2OTY0NDk3MmQyMTE5N2JkMmNlNGUyNzJkMzljNDRjZmU0MjE4OWFlM2JkNDM3NzBhNDMzZSJ9</secHashInfoBase64><live><duration>0</duration><size>0</size><md5 /><fileid /><hdsize>0</hdsize><hdmd5 /><hdfileid /><stillimagetimems>0</stillimagetimems></live></img><platform_signature /><imgdatahash /><ImgSourceInfo><ImgSourceUrl /><BizType>0</BizType></ImgSourceInfo></msg>
2025-07-22 11:46:20 | INFO | [ImageEcho] 保存图片信息成功，当前群 47325400669@chatroom 已存储 5 张图片
2025-07-22 11:46:20 | INFO | [TimerTask] 缓存图片消息: 1181571826
2025-07-22 11:46:33 | DEBUG | 收到消息: {'MsgId': 1630817403, 'FromUserName': {'string': '47325400669@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 1, 'Content': {'string': 'wxid_ubbh6q832tcs21:\n[666]'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1753156005, 'MsgSource': '<msgsource>\n\t<pua>1</pua>\n\t<eggIncluded>1</eggIncluded>\n\t<silence>0</silence>\n\t<membercount>219</membercount>\n\t<signature>N0_V1_Pliyipam|v1_wkELRGwR</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'PushContent': '郭 : [666]', 'NewMsgId': 8430759071224958732, 'MsgSeq': 871390671}
2025-07-22 11:46:33 | INFO | 收到表情消息: 消息ID:1630817403 来自:47325400669@chatroom 发送人:wxid_ubbh6q832tcs21 @:[] 内容:[666]
2025-07-22 11:46:33 | DEBUG | [DoubaoImageRecognition] 缓存表情消息: 8430759071224958732
2025-07-22 11:47:03 | DEBUG | 收到消息: {'MsgId': 594208266, 'FromUserName': {'string': '47325400669@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 1, 'Content': {'string': 'wxid_ubbh6q832tcs21:\n就拍了一张吗'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1753156035, 'MsgSource': '<msgsource>\n\t<pua>1</pua>\n\t<eggIncluded>1</eggIncluded>\n\t<silence>0</silence>\n\t<membercount>219</membercount>\n\t<signature>N0_V1_LPhgYIJx|v1_iw5czTS+</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'PushContent': '郭 : 就拍了一张吗', 'NewMsgId': 9110223855398013815, 'MsgSeq': 871390672}
2025-07-22 11:47:03 | INFO | 收到文本消息: 消息ID:594208266 来自:47325400669@chatroom 发送人:wxid_ubbh6q832tcs21 @:[] 内容:就拍了一张吗
2025-07-22 11:47:03 | DEBUG | 处理消息内容: '就拍了一张吗'
2025-07-22 11:47:03 | DEBUG | 消息内容 '就拍了一张吗' 不匹配任何命令，忽略
2025-07-22 11:47:25 | INFO | [TempFileManager] 开始清理临时文件...
2025-07-22 11:47:25 | DEBUG | [TempFileManager] 清理文件: temp\meituai\meitu_0f9b7c9be096d2e90b88e15c1dc556a4.jpg
2025-07-22 11:47:25 | DEBUG | [TempFileManager] 清理文件: temp\meituai\meitu_eeb2f791b898b50f2552117c1b1ae07e.jpg
2025-07-22 11:47:25 | DEBUG | [TempFileManager] 清理文件: temp\roboneo\quoted_image_1753150166.jpg
2025-07-22 11:47:25 | DEBUG | [TempFileManager] 清理文件: temp\roboneo\quoted_image_1753150338.jpg
2025-07-22 11:47:25 | DEBUG | [TempFileManager] 清理文件: temp\roboneo\quoted_image_1753150883.jpg
2025-07-22 11:47:25 | DEBUG | [TempFileManager] 清理文件: temp\roboneo\quoted_image_1753151129.jpg
2025-07-22 11:47:25 | DEBUG | [TempFileManager] 清理文件: temp\roboneo\quoted_image_1753151331.jpg
2025-07-22 11:47:25 | DEBUG | [TempFileManager] 清理文件: temp\roboneo\quoted_image_1753151434.jpg
2025-07-22 11:47:25 | INFO | [TempFileManager] 清理完成: 删除 8 个文件，释放 1.37MB 空间
2025-07-22 11:47:39 | DEBUG | 收到消息: {'MsgId': 1620927639, 'FromUserName': {'string': '47325400669@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 1, 'Content': {'string': 'xiehuaping668:\n就拍了一张吗'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1753156071, 'MsgSource': '<msgsource>\n\t<alnode>\n\t\t<fr>1</fr>\n\t</alnode>\n\t<pua>1</pua>\n\t<silence>0</silence>\n\t<membercount>219</membercount>\n\t<signature>N0_V1_FIMZW2X4|v1_gOah/6dj</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'PushContent': '你已被群主移出群聊 ر ر : 就拍了一张吗', 'NewMsgId': 6180574185648867438, 'MsgSeq': 871390673}
2025-07-22 11:47:39 | INFO | 收到文本消息: 消息ID:1620927639 来自:47325400669@chatroom 发送人:xiehuaping668 @:[] 内容:就拍了一张吗
2025-07-22 11:47:39 | DEBUG | 处理消息内容: '就拍了一张吗'
2025-07-22 11:47:39 | DEBUG | 消息内容 '就拍了一张吗' 不匹配任何命令，忽略
2025-07-22 11:48:06 | DEBUG | 收到消息: {'MsgId': 684906586, 'FromUserName': {'string': '27852221909@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 1, 'Content': {'string': 'wxid_cb2tqz5n94lp12:\n割哭什么buff'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1753156098, 'MsgSource': '<msgsource>\n\t<bizflag>0</bizflag>\n\t<pua>1</pua>\n\t<eggIncluded>1</eggIncluded>\n\t<silence>1</silence>\n\t<membercount>146</membercount>\n\t<signature>N0_V1_VMmvI1tu|v1_oxcyrEz9</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'NewMsgId': 5016842813388819780, 'MsgSeq': 871390674}
2025-07-22 11:48:06 | INFO | 收到文本消息: 消息ID:684906586 来自:27852221909@chatroom 发送人:wxid_cb2tqz5n94lp12 @:[] 内容:割哭什么buff
2025-07-22 11:48:06 | DEBUG | 处理消息内容: '割哭什么buff'
2025-07-22 11:48:06 | DEBUG | 消息内容 '割哭什么buff' 不匹配任何命令，忽略
2025-07-22 11:48:09 | DEBUG | 收到消息: {'MsgId': 1099555444, 'FromUserName': {'string': '27852221909@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 47, 'Content': {'string': 'wxid_cb2tqz5n94lp12:\n<msg><emoji fromusername="wxid_cb2tqz5n94lp12" tousername="27852221909@chatroom" type="2" idbuffer="media:0_0" md5="e10febd770f610723c56d2d142df5beb" len="41452" productid="" androidmd5="e10febd770f610723c56d2d142df5beb" androidlen="41452" s60v3md5="e10febd770f610723c56d2d142df5beb" s60v3len="41452" s60v5md5="e10febd770f610723c56d2d142df5beb" s60v5len="41452" cdnurl="http://vweixinf.tc.qq.com/110/20401/stodownload?m=e10febd770f610723c56d2d142df5beb&amp;filekey=30440201010430302e02016e0402535a04206531306665626437373066363130373233633536643264313432646635626562020300a1ec040d00000004627466730000000131&amp;hy=SZ&amp;storeid=323032313036303530323134343430303031656438623338336231383432626533393566303930303030303036653031303034666231&amp;ef=1&amp;bizid=1022" designerid="" thumburl="" encrypturl="http://vweixinf.tc.qq.com/110/20402/stodownload?m=0677bafa543217afda6de469372f9ffb&amp;filekey=30440201010430302e02016e0402535a04203036373762616661353433323137616664613664653436393337326639666662020300a1f0040d00000004627466730000000131&amp;hy=SZ&amp;storeid=323032313036303530323134343430303032656363613338336231383432626533393566303930303030303036653032303034666232&amp;ef=2&amp;bizid=1022" aeskey="b32757fe7e214feba64bf309d2bbd618" externurl="http://vweixinf.tc.qq.com/110/20403/stodownload?m=e1ea5967c6cdb79f1eada2a6f4305718&amp;filekey=3043020101042f302d02016e0402535a04206531656135393637633663646237396631656164613261366634333035373138020243f0040d00000004627466730000000131&amp;hy=SZ&amp;storeid=323032313036303530323134343430303034386631633338336231383432626533393566303930303030303036653033303034666233&amp;ef=3&amp;bizid=1022" externmd5="dc8e60775d01e1b1da3db1c282b7213e" width="592" height="592" tpurl="" tpauthkey="" attachedtext="" attachedtextcolor="" lensid="" emojiattr="" linkid="" desc=""></emoji><gameext type="0" content="0"></gameext></msg>'}, 'Status': 3, 'ImgStatus': 2, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1753156101, 'MsgSource': '<msgsource>\n\t<silence>1</silence>\n\t<membercount>146</membercount>\n\t<signature>N0_V1_rEP7GjwM|v1_sYtRejQw</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'NewMsgId': 4231737441481745460, 'MsgSeq': 871390675}
2025-07-22 11:48:09 | INFO | 收到表情消息: 消息ID:1099555444 来自:27852221909@chatroom 发送人:wxid_cb2tqz5n94lp12 MD5:e10febd770f610723c56d2d142df5beb 大小:41452
2025-07-22 11:48:09 | DEBUG | [DoubaoImageRecognition] 缓存表情消息: 4231737441481745460
2025-07-22 11:48:42 | DEBUG | 收到消息: {'MsgId': 2042579182, 'FromUserName': {'string': '47325400669@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 1, 'Content': {'string': 'wxid_9uwska6u4yzm22:\n其他的要充值vip会员'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1753156134, 'MsgSource': '<msgsource>\n\t<bizflag>0</bizflag>\n\t<pua>1</pua>\n\t<eggIncluded>1</eggIncluded>\n\t<silence>0</silence>\n\t<membercount>219</membercount>\n\t<signature>N0_V1_gNhH1ZpF|v1_MGOJE8cm</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'PushContent': 'Garson : 其他的要充值vip会员', 'NewMsgId': 6851128656856224521, 'MsgSeq': 871390676}
2025-07-22 11:48:42 | INFO | 收到文本消息: 消息ID:2042579182 来自:47325400669@chatroom 发送人:wxid_9uwska6u4yzm22 @:[] 内容:其他的要充值vip会员
2025-07-22 11:48:42 | DEBUG | 处理消息内容: '其他的要充值vip会员'
2025-07-22 11:48:42 | DEBUG | 消息内容 '其他的要充值vip会员' 不匹配任何命令，忽略
2025-07-22 11:48:44 | DEBUG | 收到消息: {'MsgId': 1870889804, 'FromUserName': {'string': '47325400669@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 1, 'Content': {'string': 'wxid_ar7quydkgn7522:\n就拍了一张吗'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1753156136, 'MsgSource': '<msgsource>\n\t<bizflag>0</bizflag>\n\t<pua>1</pua>\n\t<eggIncluded>1</eggIncluded>\n\t<silence>0</silence>\n\t<membercount>219</membercount>\n\t<signature>N0_V1_oQ4Z3D+V|v1_Xi8mymIW</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'PushContent': 'ₔ安与²⁰²⁵ : 就拍了一张吗', 'NewMsgId': 8324467496467492048, 'MsgSeq': 871390677}
2025-07-22 11:48:44 | INFO | 收到文本消息: 消息ID:1870889804 来自:47325400669@chatroom 发送人:wxid_ar7quydkgn7522 @:[] 内容:就拍了一张吗
2025-07-22 11:48:44 | DEBUG | 处理消息内容: '就拍了一张吗'
2025-07-22 11:48:44 | DEBUG | 消息内容 '就拍了一张吗' 不匹配任何命令，忽略
2025-07-22 11:49:22 | DEBUG | 收到消息: {'MsgId': 1530791315, 'FromUserName': {'string': '47325400669@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 1, 'Content': {'string': 'wxid_ubbh6q832tcs21:\n别逼我求你'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1753156174, 'MsgSource': '<msgsource>\n\t<pua>1</pua>\n\t<eggIncluded>1</eggIncluded>\n\t<silence>0</silence>\n\t<membercount>219</membercount>\n\t<signature>N0_V1_iFc5BuBc|v1_WdaCA5bZ</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'PushContent': '郭 : 别逼我求你', 'NewMsgId': 2089587573878056885, 'MsgSeq': 871390678}
2025-07-22 11:49:22 | INFO | 收到文本消息: 消息ID:1530791315 来自:47325400669@chatroom 发送人:wxid_ubbh6q832tcs21 @:[] 内容:别逼我求你
2025-07-22 11:49:22 | DEBUG | 处理消息内容: '别逼我求你'
2025-07-22 11:49:22 | DEBUG | 消息内容 '别逼我求你' 不匹配任何命令，忽略
2025-07-22 11:49:45 | DEBUG | 收到消息: {'MsgId': 209014310, 'FromUserName': {'string': '47325400669@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 49, 'Content': {'string': 'wxid_9uwska6u4yzm22:\n<?xml version="1.0"?>\n<msg>\n\t<appmsg appid="" sdkver="0">\n\t\t<title />\n\t\t<type>8</type>\n\t\t<appattach>\n\t\t\t<totallen>3035761</totallen>\n\t\t\t<emoticonmd5>68ffc0b75f7ebddb6c43db727efeb4dc</emoticonmd5>\n\t\t\t<fileext>pic</fileext>\n\t\t\t<attachid>0:0:68ffc0b75f7ebddb6c43db727efeb4dc</attachid>\n\t\t\t<cdnthumbaeskey>6976646e6e677968696678746e6d766e</cdnthumbaeskey>\n\t\t\t<aeskey />\n\t\t\t<cdnthumburl>3057020100044b30490201000204fa6599a102033d11fe0204988350700204687e44c1042438346533653539652d613333322d343138382d623836352d3734333939393237313237620204052408030201000405004c57c2006c46739d</cdnthumburl>\n\t\t\t<cdnthumblength>31847</cdnthumblength>\n\t\t\t<cdnthumbwidth>350</cdnthumbwidth>\n\t\t\t<cdnthumbheight>213</cdnthumbheight>\n\t\t\t<cdnthumbmd5>c49a7507c2fe2f5c41c3c2f678e1f1c5</cdnthumbmd5>\n\t\t\t<emojiinfo>CiA2OGZmYzBiNzVmN2ViZGRiNmM0M2RiNzI3ZWZlYjRkYxKeAmh0dHA6Ly93eGFwcC50Yy5xcS5jb20vMjYyLzIwMzA0L3N0b2Rvd25sb2FkP209NjhmZmMwYjc1ZjdlYmRkYjZjNDNkYjcyN2VmZWI0ZGMmZmlsZWtleT0zMDM1MDIwMTAxMDQyMTMwMWYwMjAyMDEwNjA0MDI1MzQ4MDQxMDY4ZmZjMGI3NWY3ZWJkZGI2YzQzZGI3MjdlZmViNGRjMDIwMzJlNTI3MTA0MGQwMDAwMDAwNDYyNzQ2NjczMDAwMDAwMDEzMiZoeT1TSCZzdG9yZWlkPTI2MzExOGY0NjAwMGI0ZjhjMDAwMDAwMDAwMDAwMDEwNjAwMDA0ZjUwNTM0ODIwNTZmYjQwYjZhMjAwNTZlJmJpemlkPTEwMjMqngJodHRwOi8vd3hhcHAudGMucXEuY29tLzI2Mi8yMDMwNC9zdG9kb3dubG9hZD9tPTY4N2IyNjhkNzk0NjJlYmNjZDcwNTg3N2NkNWI4MDJiJmZpbGVrZXk9MzAzNTAyMDEwMTA0MjEzMDFmMDIwMjAxMDYwNDAyNTM0ODA0MTA2ODdiMjY4ZDc5NDYyZWJjY2Q3MDU4NzdjZDViODAyYjAyMDMyZTUyODAwNDBkMDAwMDAwMDQ2Mjc0NjY3MzAwMDAwMDAxMzImaHk9U0gmc3RvcmVpZD0yNjMxMThmNDcwMDA2NGRjZTAwMDAwMDAwMDAwMDAxMDYwMDAwNGY1MDUzNDgwNzlkMmEwMGI2YTJiYmNhNyZiaXppZD0xMDIzMiAxZjNjNDZlZjAyNzNhNTUyNmVmOGU5ZWRmMGEyOGY2NDoAggEA</emojiinfo>\n\t\t</appattach>\n\t\t<percent>99</percent>\n\t</appmsg>\n\t<fromusername>wxid_9uwska6u4yzm22</fromusername>\n\t<scene>0</scene>\n\t<appinfo>\n\t\t<version>1</version>\n\t\t<appname></appname>\n\t</appinfo>\n\t<commenturl></commenturl>\n</msg>\n'}, 'Status': 3, 'ImgStatus': 2, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1753156197, 'MsgSource': '<msgsource>\n\t<sec_msg_node>\n\t\t<uuid>161752d9ba5dd3a4ba6ccf58c1df58d7_</uuid>\n\t\t<risk-file-flag />\n\t\t<risk-file-md5-list />\n\t</sec_msg_node>\n\t<silence>0</silence>\n\t<membercount>219</membercount>\n\t<signature>N0_V1_MaCF228L|v1_rxHbou8j</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'PushContent': '你收到了一条消息', 'NewMsgId': 7240478097706662335, 'MsgSeq': 871390679}
2025-07-22 11:49:45 | DEBUG | 从群聊消息中提取发送者: wxid_9uwska6u4yzm22
2025-07-22 11:49:45 | DEBUG | XML消息完整内容:
<?xml version="1.0"?>
<msg>
	<appmsg appid="" sdkver="0">
		<title />
		<type>8</type>
		<appattach>
			<totallen>3035761</totallen>
			<emoticonmd5>68ffc0b75f7ebddb6c43db727efeb4dc</emoticonmd5>
			<fileext>pic</fileext>
			<attachid>0:0:68ffc0b75f7ebddb6c43db727efeb4dc</attachid>
			<cdnthumbaeskey>6976646e6e677968696678746e6d766e</cdnthumbaeskey>
			<aeskey />
			<cdnthumburl>3057020100044b30490201000204fa6599a102033d11fe0204988350700204687e44c1042438346533653539652d613333322d343138382d623836352d3734333939393237313237620204052408030201000405004c57c2006c46739d</cdnthumburl>
			<cdnthumblength>31847</cdnthumblength>
			<cdnthumbwidth>350</cdnthumbwidth>
			<cdnthumbheight>213</cdnthumbheight>
			<cdnthumbmd5>c49a7507c2fe2f5c41c3c2f678e1f1c5</cdnthumbmd5>
			<emojiinfo>CiA2OGZmYzBiNzVmN2ViZGRiNmM0M2RiNzI3ZWZlYjRkYxKeAmh0dHA6Ly93eGFwcC50Yy5xcS5jb20vMjYyLzIwMzA0L3N0b2Rvd25sb2FkP209NjhmZmMwYjc1ZjdlYmRkYjZjNDNkYjcyN2VmZWI0ZGMmZmlsZWtleT0zMDM1MDIwMTAxMDQyMTMwMWYwMjAyMDEwNjA0MDI1MzQ4MDQxMDY4ZmZjMGI3NWY3ZWJkZGI2YzQzZGI3MjdlZmViNGRjMDIwMzJlNTI3MTA0MGQwMDAwMDAwNDYyNzQ2NjczMDAwMDAwMDEzMiZoeT1TSCZzdG9yZWlkPTI2MzExOGY0NjAwMGI0ZjhjMDAwMDAwMDAwMDAwMDEwNjAwMDA0ZjUwNTM0ODIwNTZmYjQwYjZhMjAwNTZlJmJpemlkPTEwMjMqngJodHRwOi8vd3hhcHAudGMucXEuY29tLzI2Mi8yMDMwNC9zdG9kb3dubG9hZD9tPTY4N2IyNjhkNzk0NjJlYmNjZDcwNTg3N2NkNWI4MDJiJmZpbGVrZXk9MzAzNTAyMDEwMTA0MjEzMDFmMDIwMjAxMDYwNDAyNTM0ODA0MTA2ODdiMjY4ZDc5NDYyZWJjY2Q3MDU4NzdjZDViODAyYjAyMDMyZTUyODAwNDBkMDAwMDAwMDQ2Mjc0NjY3MzAwMDAwMDAxMzImaHk9U0gmc3RvcmVpZD0yNjMxMThmNDcwMDA2NGRjZTAwMDAwMDAwMDAwMDAxMDYwMDAwNGY1MDUzNDgwNzlkMmEwMGI2YTJiYmNhNyZiaXppZD0xMDIzMiAxZjNjNDZlZjAyNzNhNTUyNmVmOGU5ZWRmMGEyOGY2NDoAggEA</emojiinfo>
		</appattach>
		<percent>99</percent>
	</appmsg>
	<fromusername>wxid_9uwska6u4yzm22</fromusername>
	<scene>0</scene>
	<appinfo>
		<version>1</version>
		<appname></appname>
	</appinfo>
	<commenturl></commenturl>
</msg>

2025-07-22 11:49:45 | DEBUG | XML消息类型: 8
2025-07-22 11:49:45 | DEBUG | XML消息标题: None
2025-07-22 11:49:45 | DEBUG | 附件信息 totallen: 3035761
2025-07-22 11:49:45 | DEBUG | 附件信息 emoticonmd5: 68ffc0b75f7ebddb6c43db727efeb4dc
2025-07-22 11:49:45 | DEBUG | 附件信息 fileext: pic
2025-07-22 11:49:45 | DEBUG | 附件信息 attachid: 0:0:68ffc0b75f7ebddb6c43db727efeb4dc
2025-07-22 11:49:45 | DEBUG | 附件信息 cdnthumbaeskey: 6976646e6e677968696678746e6d766e
2025-07-22 11:49:45 | DEBUG | 附件信息 cdnthumburl: 3057020100044b30490201000204fa6599a102033d11fe0204988350700204687e44c1042438346533653539652d613333322d343138382d623836352d3734333939393237313237620204052408030201000405004c57c2006c46739d
2025-07-22 11:49:45 | DEBUG | 附件信息 cdnthumblength: 31847
2025-07-22 11:49:45 | DEBUG | 附件信息 cdnthumbwidth: 350
2025-07-22 11:49:45 | DEBUG | 附件信息 cdnthumbheight: 213
2025-07-22 11:49:45 | DEBUG | 附件信息 cdnthumbmd5: c49a7507c2fe2f5c41c3c2f678e1f1c5
2025-07-22 11:49:45 | DEBUG | 附件信息 emojiinfo: CiA2OGZmYzBiNzVmN2ViZGRiNmM0M2RiNzI3ZWZlYjRkYxKeAmh0dHA6Ly93eGFwcC50Yy5xcS5jb20vMjYyLzIwMzA0L3N0b2Rvd25sb2FkP209NjhmZmMwYjc1ZjdlYmRkYjZjNDNkYjcyN2VmZWI0ZGMmZmlsZWtleT0zMDM1MDIwMTAxMDQyMTMwMWYwMjAyMDEwNjA0MDI1MzQ4MDQxMDY4ZmZjMGI3NWY3ZWJkZGI2YzQzZGI3MjdlZmViNGRjMDIwMzJlNTI3MTA0MGQwMDAwMDAwNDYyNzQ2NjczMDAwMDAwMDEzMiZoeT1TSCZzdG9yZWlkPTI2MzExOGY0NjAwMGI0ZjhjMDAwMDAwMDAwMDAwMDEwNjAwMDA0ZjUwNTM0ODIwNTZmYjQwYjZhMjAwNTZlJmJpemlkPTEwMjMqngJodHRwOi8vd3hhcHAudGMucXEuY29tLzI2Mi8yMDMwNC9zdG9kb3dubG9hZD9tPTY4N2IyNjhkNzk0NjJlYmNjZDcwNTg3N2NkNWI4MDJiJmZpbGVrZXk9MzAzNTAyMDEwMTA0MjEzMDFmMDIwMjAxMDYwNDAyNTM0ODA0MTA2ODdiMjY4ZDc5NDYyZWJjY2Q3MDU4NzdjZDViODAyYjAyMDMyZTUyODAwNDBkMDAwMDAwMDQ2Mjc0NjY3MzAwMDAwMDAxMzImaHk9U0gmc3RvcmVpZD0yNjMxMThmNDcwMDA2NGRjZTAwMDAwMDAwMDAwMDAxMDYwMDAwNGY1MDUzNDgwNzlkMmEwMGI2YTJiYmNhNyZiaXppZD0xMDIzMiAxZjNjNDZlZjAyNzNhNTUyNmVmOGU5ZWRmMGEyOGY2NDoAggEA
2025-07-22 11:49:45 | INFO | 未知的XML消息类型: 8
2025-07-22 11:49:45 | INFO | 消息标题: None
2025-07-22 11:49:45 | INFO | 消息描述: N/A
2025-07-22 11:49:45 | INFO | 消息URL: N/A
2025-07-22 11:49:45 | INFO | 完整XML内容:
<?xml version="1.0"?>
<msg>
	<appmsg appid="" sdkver="0">
		<title />
		<type>8</type>
		<appattach>
			<totallen>3035761</totallen>
			<emoticonmd5>68ffc0b75f7ebddb6c43db727efeb4dc</emoticonmd5>
			<fileext>pic</fileext>
			<attachid>0:0:68ffc0b75f7ebddb6c43db727efeb4dc</attachid>
			<cdnthumbaeskey>6976646e6e677968696678746e6d766e</cdnthumbaeskey>
			<aeskey />
			<cdnthumburl>3057020100044b30490201000204fa6599a102033d11fe0204988350700204687e44c1042438346533653539652d613333322d343138382d623836352d3734333939393237313237620204052408030201000405004c57c2006c46739d</cdnthumburl>
			<cdnthumblength>31847</cdnthumblength>
			<cdnthumbwidth>350</cdnthumbwidth>
			<cdnthumbheight>213</cdnthumbheight>
			<cdnthumbmd5>c49a7507c2fe2f5c41c3c2f678e1f1c5</cdnthumbmd5>
			<emojiinfo>CiA2OGZmYzBiNzVmN2ViZGRiNmM0M2RiNzI3ZWZlYjRkYxKeAmh0dHA6Ly93eGFwcC50Yy5xcS5jb20vMjYyLzIwMzA0L3N0b2Rvd25sb2FkP209NjhmZmMwYjc1ZjdlYmRkYjZjNDNkYjcyN2VmZWI0ZGMmZmlsZWtleT0zMDM1MDIwMTAxMDQyMTMwMWYwMjAyMDEwNjA0MDI1MzQ4MDQxMDY4ZmZjMGI3NWY3ZWJkZGI2YzQzZGI3MjdlZmViNGRjMDIwMzJlNTI3MTA0MGQwMDAwMDAwNDYyNzQ2NjczMDAwMDAwMDEzMiZoeT1TSCZzdG9yZWlkPTI2MzExOGY0NjAwMGI0ZjhjMDAwMDAwMDAwMDAwMDEwNjAwMDA0ZjUwNTM0ODIwNTZmYjQwYjZhMjAwNTZlJmJpemlkPTEwMjMqngJodHRwOi8vd3hhcHAudGMucXEuY29tLzI2Mi8yMDMwNC9zdG9kb3dubG9hZD9tPTY4N2IyNjhkNzk0NjJlYmNjZDcwNTg3N2NkNWI4MDJiJmZpbGVrZXk9MzAzNTAyMDEwMTA0MjEzMDFmMDIwMjAxMDYwNDAyNTM0ODA0MTA2ODdiMjY4ZDc5NDYyZWJjY2Q3MDU4NzdjZDViODAyYjAyMDMyZTUyODAwNDBkMDAwMDAwMDQ2Mjc0NjY3MzAwMDAwMDAxMzImaHk9U0gmc3RvcmVpZD0yNjMxMThmNDcwMDA2NGRjZTAwMDAwMDAwMDAwMDAxMDYwMDAwNGY1MDUzNDgwNzlkMmEwMGI2YTJiYmNhNyZiaXppZD0xMDIzMiAxZjNjNDZlZjAyNzNhNTUyNmVmOGU5ZWRmMGEyOGY2NDoAggEA</emojiinfo>
		</appattach>
		<percent>99</percent>
	</appmsg>
	<fromusername>wxid_9uwska6u4yzm22</fromusername>
	<scene>0</scene>
	<appinfo>
		<version>1</version>
		<appname></appname>
	</appinfo>
	<commenturl></commenturl>
</msg>

