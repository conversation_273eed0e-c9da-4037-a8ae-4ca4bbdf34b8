[VivoAgents]
enable = true
command-format = """⚙️智能体指令：
@智能体名 问题
或
智能体名 问题

🤖支持的智能体：
- 孙悟空：西游记智能体
- 林黛玉：红楼梦智能体
- 卡斯特：恋爱军师智能体
"""

# 基础配置
tts-url = "http://www.yx520.ltd/API/wzzyy/zmp3.php"
tts-voice = "318"

# API配置
api-url = "https://agents.vivo.com.cn/gpts-v2-api/chat/v1/completions/web/stream/public"
app-id = "**********"

# 登录凭证
csrf-token = "631e52060fcb7b3da08350978e76d765.*************"
cookies = "vivo_account_cookie_iqoo_deviceid=wb_84d83ded-fcee-4a37-a0de-ae917b23bb3f; clientId=199; findPwdRandomNum=f9350811f485a826; question1=%E6%82%A8%E6%9C%80%E7%86%9F%E6%82%89%E7%9A%84%E7%AB%A5%E5%B9%B4%E5%A5%BD%E5%8F%8B%E7%9A%84%E5%90%8D%E5%AD%97%EF%BC%9F; question2=%E6%82%A8%E9%AB%98%E4%B8%AD%E7%8F%AD%E4%B8%BB%E4%BB%BB%E7%9A%84%E5%90%8D%E5%AD%97%EF%BC%9F; question3=%E5%90%AF%E8%92%99%E8%80%81%E5%B8%88%E7%9A%84%E5%90%8D%E5%AD%97%EF%BC%9F; findpwdType=8; account=MTM2OTY0MjY3MTM=; log_r_flow_no=PASSPORT20250218105707747; log_r_reuslt=HIGHRISK; pwd=aGdnR2ZpOUwybmRiMEJHTUdnOVc5aWFBaGQ5b1o4dnBiNzJVemQ3T3hkQVV6MXZjMllHSXpxNHdiZDJieEd5QysyQjJPSGxsaEJlN2UvcmFobXlzRkhZVWNMd1BmVThzejZjWHFOcWRWNnVnR0J0aTNaNmRMYkladHBQbE5WNzVmdzNhSnV6eVN2Uy96b0xYWHFBR2RFSlY0YmNvTS9qZnp2Mld0cytSWWVlTQ==; lr_wb_84d83ded-fcee-4a37-a0de-ae917b23bb3f=3f048382bd8fd2a98c13c9006b5f3354; openid=427d539267f04864; vivo_account_cookie_iqoo_checksum=631e52060fcb7b3da08350978e76d765.*************; vivo_account_cookie_iqoo_openid=427d539267f04864; vivo_account_cookie_iqoo_authtoken=**********************************************************; vivo_account_cookie_iqoo_vivotoken=631e52060fcb7b3da08350978e76d765.*************; vivo_account_cookie_iqoo_regioncode=CN"

# 登录凭证自动更新配置
username = ""  # vivo账号
password = ""  # vivo密码
auto-refresh = true  # 是否自动刷新

# 智能体配置
[VivoAgents.agents.wukong]
name = "孙悟空"
agent-id = ********
command = ["悟空", "wk", "孙悟空"]
description = "西游记智能体,性格活泼好动"
tts-voice = "4"  # 悟空音色

[VivoAgents.agents.daiyu]
name = "林黛玉"
agent-id = ********
command = ["黛玉", "林黛玉", "dy"]
description = "红楼梦中的林黛玉,多愁善感的女子"
tts-voice = "27"  # 林黛玉音色

[VivoAgents.agents.kaste]
name = "恋爱军师-卡斯特"
agent-id = ********
command = ["卡斯特", "恋爱军师", "ks"]
description = "恋爱军师智能体,擅长情感咨询"
tts-voice = "336"  # 卡斯特音色

# 限流配置
[VivoAgents.rate_limit]
tokens_per_second = 0.1  # 每10秒生成1个令牌
bucket_size = 3  # 令牌桶容量 