import os,json,time,random,hashlib,base64,asyncio,httpx
from pathlib import Path
try:import tomllib
except:import tomli as tomllib
from WechatAPI import <PERSON>chatAPIClient
from utils.decorators import on_text_message,on_quote_message,on_emoji_message
from utils.plugin_base import PluginBase
from utils.temp_file_manager import cleanup_file
class KeLingImageGenerator:
    def __init__(self, cookies):
        self.cookies = cookies
        self.api_base_url = "https://api-app-cn.klingai.com"
        self.upload_base_url = "https://upload.kuaishouzt.com"
        self.base_headers = {
            'Accept': 'application/json, text/plain, */*',
            'Time-Zone': 'Asia/Shanghai',
            'Accept-Language': 'zh',
            'User-Agent': 'Mozilla/5.0 (Linux; Android 10; V2002A Build/QP1A.190711.020) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/83.0.4103.106 Mobile Safari/537.36',
            'Origin': 'https://app.klingai.com',
            'X-Requested-With': 'mark.via',
            'Sec-Fetch-Site': 'same-site',
            'Sec-Fetch-Mode': 'cors',
            'Sec-Fetch-Dest': 'empty',
            'Referer': 'https://app.klingai.com/cn/image-to-image/single/new',
            'Accept-Encoding': 'gzip, deflate',
            'Accept-Language': 'zh-CN,zh;q=0.9,en-US;q=0.8,en;q=0.7'
        }

    async def get_upload_token(self, client: httpx.AsyncClient, filename):
        url = f"{self.api_base_url}/api/upload/issue/token"
        headers = self.base_headers.copy()
        headers['Cookie'] = self.cookies
        try:
            response = await client.get(url, params={'filename': filename}, headers=headers, timeout=30)
            if response.status_code == 200:
                result = response.json()
                return (result['data']['token'], result['data']['httpEndpoints']) if result.get('result') == 1 else (None, None)
            return None, None
        except:
            return None, None

    async def check_upload_status(self, client: httpx.AsyncClient, token):
        headers = {'Accept': 'application/json, text/plain, */*', 'User-Agent': 'Mozilla/5.0 (Linux; Android 10; V2002A Build/QP1A.190711.020) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/83.0.4103.106 Mobile Safari/537.36', 'Origin': 'https://app.klingai.com', 'X-Requested-With': 'mark.via', 'Sec-Fetch-Site': 'cross-site', 'Sec-Fetch-Mode': 'cors', 'Sec-Fetch-Dest': 'empty', 'Referer': 'https://app.klingai.com/cn/image-to-image/single/new', 'Accept-Encoding': 'gzip, deflate', 'Accept-Language': 'zh-CN,zh;q=0.9,en-US;q=0.8,en;q=0.7'}
        try:
            response = await client.get(f"{self.upload_base_url}/api/upload/resume", params={'upload_token': token}, headers=headers, timeout=30)
            return response.json() if response.status_code == 200 and response.json().get('result') == 1 else None
        except:
            return None

    async def upload_file_fragment(self, client: httpx.AsyncClient, token, file_content, fragment_id=0):
        file_size = len(file_content)
        headers = {'Accept': 'application/json, text/plain, */*', 'User-Agent': 'Mozilla/5.0 (Linux; Android 10; V2002A Build/QP1A.190711.020) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/83.0.4103.106 Mobile Safari/537.36', 'Content-Type': 'application/octet-stream', 'Content-Range': f'bytes 0-{file_size-1}/{file_size}', 'Origin': 'https://app.klingai.com', 'X-Requested-With': 'mark.via', 'Sec-Fetch-Site': 'cross-site', 'Sec-Fetch-Mode': 'cors', 'Sec-Fetch-Dest': 'empty', 'Referer': 'https://app.klingai.com/cn/image-to-image/single/new', 'Accept-Encoding': 'gzip, deflate', 'Accept-Language': 'zh-CN,zh;q=0.9,en-US;q=0.8,en;q=0.7'}
        try:
            response = await client.post(f"{self.upload_base_url}/api/upload/fragment", params={'upload_token': token, 'fragment_id': str(fragment_id)}, headers=headers, content=file_content, timeout=60)
            return response.json() if response.status_code == 200 and response.json().get('result') == 1 else None
        except:
            return None

    async def complete_upload(self, client: httpx.AsyncClient, token, fragment_count=1):
        headers = {'Accept': 'application/json, text/plain, */*', 'User-Agent': 'Mozilla/5.0 (Linux; Android 10; V2002A Build/QP1A.190711.020) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/83.0.4103.106 Mobile Safari/537.36', 'Origin': 'https://app.klingai.com', 'X-Requested-With': 'mark.via', 'Sec-Fetch-Site': 'cross-site', 'Sec-Fetch-Mode': 'cors', 'Sec-Fetch-Dest': 'empty', 'Referer': 'https://app.klingai.com/cn/image-to-image/single/new', 'Accept-Encoding': 'gzip, deflate', 'Accept-Language': 'zh-CN,zh;q=0.9,en-US;q=0.8,en;q=0.7'}
        try:
            response = await client.post(f"{self.upload_base_url}/api/upload/complete", params={'fragment_count': str(fragment_count), 'upload_token': token}, headers=headers, timeout=30)
            return response.json() if response.status_code == 200 and response.json().get('result') == 1 else None
        except:
            return None

    async def verify_upload_token(self, client: httpx.AsyncClient, token):
        headers = self.base_headers.copy()
        headers['Cookie'] = self.cookies
        try:
            response = await client.get(f"{self.api_base_url}/api/upload/verify/token", params={'token': token}, headers=headers, timeout=30)
            return response.json() if response.status_code == 200 and response.json().get('result') == 1 else None
        except:
            return None

    async def upload_image(self, client: httpx.AsyncClient, image_path):
        try:
            if not os.path.exists(image_path): return None
            with open(image_path, 'rb') as f: file_content = f.read()
            file_name = os.path.basename(image_path)
            token, _ = await self.get_upload_token(client, file_name)
            if not token: return None
            status_result = await self.check_upload_status(client, token)
            if not status_result: return None
            if not status_result.get('existed', False):
                if not await self.upload_file_fragment(client, token, file_content): return None
                if not await self.complete_upload(client, token, 1): return None
            verify_result = await self.verify_upload_token(client, token)
            return verify_result.get('data', {}).get('url', '') if verify_result else None
        except:
            return None

    async def recognize_face(self, client: httpx.AsyncClient, image_url):
        params = {'__NS_hxfalcon': 'HUDR_sFnX-FFuAW5VsfDNK0XOP6snthhLc_IxjxBz8_r61EvYFIc7AGaHwcmlb_Lw36QFxBn0Bj4EKN4Zam4e3VuXvsYqg9MYxFklc37VMFPhmNyIlhW9Lap2cj0wiLovVU8JAvEnehn95vqdLV8O603AWy6J_0JP6IsYwdw1pyS9mspS5JAUESzgixsf9lnypQtkWlIbuMUFfLnx1CZ3fMOirJgOJB47umbxOucEsawk2fnnf4QhZ7e9X4YOdXaoWDjvoha-P4sHvw-A--Q4FlgCCrJ4ZHqVp8VG1h6c0F0KRFNV7tidPMWDtbSr1rvILnyuza3Ua-A9VfHcET29MkFw3XaVXuT25sPENvhRkLMDFu_ZJPbBkSJQilvTsgB63yYIYd3LQ6sgvcY6eSA18dijdwT8nr-l0L3i8-TPts3nRLkkE680CHgxvlHiyHXnBoydqOIa4aPVtfcyPXYbrfitxJZZtwKf6RmY0cSXmO3QTE2Y8pchpuopaDiE6xip3bwTZpezVZyNeDdt4D7q1EbDOwdl_oR0Aa5coR1-YP-iYqtG5QAaJa4jAyrpdFnrn4SR0l3zL1Q4fGLdg7pRn_dedz9hFYtLxDcW6IV6ZiHCTkvZHam1daJP8VieRA..$HE_3629b1d4d60859e1ddea7ca2d6ebe2d7907c7d7d7d7cb9e5d1c73ca759986853ab15ea7ce62b43a7062b43957d', 'caver': '2'}
        request_data = {"type": "vcg_check_face", "inputs": [{"name": "input", "inputType": "URL", "url": image_url}]}
        headers = self.base_headers.copy()
        headers['Content-Type'] = 'application/json'
        headers['Cookie'] = self.cookies
        try:
            response = await client.post(f"{self.api_base_url}/api/task/recognize", params=params, headers=headers, content=json.dumps(request_data), timeout=60)
            return response.json() if response.status_code == 200 and response.json().get('result') == 1 else None
        except:
            return None

    async def submit_image_to_image_task(self, client: httpx.AsyncClient, image_url, face_detection_result, prompt="风格转换"):
        params = {'__NS_hxfalcon': 'HUDR_sFnX-FFuAW5VsfDNK0XOP6snthhLc_IxjxBz8_r61EvYFIc7AGaHwcmlb_Lw36QFxBn0Bj4EKN4Zam4e3VuXvsYqg9MYxFklc37VMFPhmNyIlhW9Lap2cj0wiLovVU8JAvEnehn95vqdLV8O603AWy6J_0JP6IsYwdw1pyS9mspS5JAUESzgixsf9lnypQtkWlIbuMUFfLnx1CZ3fMOirJgOJB47umbxOucEsawk2fnnf4QhZ7e9X4YOdXaoWDjvoha-P4sHvw-A--Q4FlgCCrJ4ZHqVp8VG1h6c0F0KRFNV7tidPMWDtbSr1rvILnyuza3Ua-A9VfHcET29MkFw3XaVXuT25sPENvhRkLMDFu_ZJPbBkSJQilvTsgB63yYIYd3LQ6sgvcY6eSA18dijdwT8nr-l0L3i8-TPts3nRLkkE680CHgxvlniyHXnBoSdqOIa4V_UtfcyPVcQrfitxG1ZtwKf6RmY0cSXmO3QURDBs5UjqKolKjqG5RenkrZSaNe1GZOAMzom7XfvmknOblhm-4NyG_cfox9wI-_qd-NFoQ5eOvE8FRrsQletkdiDxFrldE1kOXicjrFN18tGRWkiM_B_pCxLuMR6ZSvAQE_ZAKm1jqJP8VieRA..$HE_ccd34b2e2cf2a31b271086d8adeeee05358687878786b81f2b3d965cf266254053ef10861cd1b95dfcd1b96f87', 'caver': '2'}
        face_data = face_detection_result.get('data', {}) if face_detection_result else {}
        face_items = face_data.get('faceItems', [])
        media_info = face_data.get('mediaInfo', {})
        callback_payloads = [{"name": "face_count", "value": str(len(face_items))}]
        for i, face_item in enumerate(face_items):
            face_resources = [{"name": r.get('name'), "type": "IMAGE", "url": r.get('url', '')} for r in face_item.get('resources', []) if r.get('name') in ['face', 'face_feature']]
            face_arguments = [{"name": "face_bound", "type": "IMAGE", "value": arg.get('value', '')} for arg in face_item.get('arguments', []) if arg.get('name') == 'face_bound']
            callback_payloads.append({"name": f"face{i}", "resources": face_resources, "arguments": face_arguments})
        callback_payloads.extend([{"name": "referenceImageWidth", "value": str(media_info.get('width', 888))}, {"name": "referenceImageHeight", "value": str(media_info.get('height', 1920))}])
        request_data = {"type": "mmu_img2img_aiweb", "inputs": [{"inputType": "URL", "url": image_url, "name": "input"}], "arguments": [{"name": "biz", "value": "klingai"}, {"name": "prompt", "value": prompt}, {"name": "imageCount", "value": "1"}, {"name": "kolors_version", "value": "1.5"}, {"name": "style", "value": "默认"}, {"name": "fidelity", "value": "0.5"}, {"name": "humanFidelity", "value": "1"}, {"name": "aspect_ratio", "value": "9:16"}, {"name": "referenceType", "value": "mmu_img2img_aiweb_v15_portrait"}], "callbackPayloads": callback_payloads}
        headers = self.base_headers.copy()
        headers['Content-Type'] = 'application/json'
        headers['Cookie'] = self.cookies
        try:
            response = await client.post(f"{self.api_base_url}/api/task/submit", params=params, headers=headers, content=json.dumps(request_data), timeout=60)
            return response.json() if response.status_code == 200 and response.json().get('result') == 1 else None
        except:
            return None

    async def generate_image(self, client: httpx.AsyncClient, image_url, prompt="风格转换"):
        try:
            face_result = await self.recognize_face(client, image_url)
            img2img_result = await self.submit_image_to_image_task(client, image_url, face_result, prompt)
            if not img2img_result: return None, None
            task_id = img2img_result.get('data', {}).get('task', {}).get('id', '')
            if not task_id: return None, None
            unread_works_result = await self.poll_task_until_complete(client, task_id, 60, 10)
            if unread_works_result:
                cover_url = unread_works_result.get('data', {}).get('cover', {}).get('resource', '')
                return (cover_url, None) if cover_url and 'default_msg_cover' not in cover_url else (None, None)
            return None, None
        except:
            return None, None

    async def get_unread_works(self, client: httpx.AsyncClient):
        params = {'__NS_hxfalcon': 'HUDR_sFnX-FFuAW5VsfDNK0XOP6snthhLc_IxjxBz8_r61EvYFIc7AGaHwcmlb_Lw36QFxBn0Bj4EKN4Zam4e3VuXvsYqg9MYxFklc37VMFPhmNyIlhW9Lap2cj0wiLovVU8JAvEnehn95vqdLV8O603AWy6J_0JP6IsYwdw1pyS9mspS5JAUESzgixsf9lnypQtkWlIbuMUFfLnx1CZ3fMOirJgOJB47umbxOucEsawk2fnnf4QhZ7e9X4YOdXaoWDjvoha-P4sHvw-A--Q4FlgCCrJ4ZHqVp8VG1h6c0F0KRFNV7tidPMWDtbSr1rvILnyuza3Ua-A9VfHcET29MkFw3XaVXuT25sPENvhRkLMDFu_ZJPbBkSJQilvTsgB63yYIYd3LQ6sgvcY6eSA18dijdwT8nr-l0L3i8-TPts3nRLkkE680CHgxvlniyHXnBoSdqOIa4V_UtfcyPbwRrfitxHtZtwKf6S-Y0cSXmO3QTE2Y8pchpuopaDiE6xip3bwTZpezVZyNeDdt4D7q1EbDOwdl_oR0Aa5coR1-YP-iYqtG5QAaJa4jAyrpdFnrn4SR0l3zL1Q4fGLdg7pRn_dedz9hFYtLxDcW6IV6ZiHCTkvZHam1mKJP8VieRA..$HE_c3dc442123fdac14281f890249233edfa88988888889a1102432cf56f93ca43e5ce01f8913deb652f3deb66088', 'caver': '2'}
        headers = self.base_headers.copy()
        headers['Cookie'] = self.cookies
        try:
            response = await client.get(f"{self.api_base_url}/api/homepage/unread_works", params=params, headers=headers, timeout=30)
            return response.json() if response.status_code == 200 and response.json().get('result') == 1 else None
        except:
            return None

    async def poll_task_until_complete(self, client: httpx.AsyncClient, task_id, max_attempts=60, interval=10):
        for attempt in range(1, max_attempts + 1):
            unread_works = await self.get_unread_works(client)
            if not unread_works:
                await asyncio.sleep(interval)
                continue
            works_data = unread_works.get('data', {})
            cover = works_data.get('cover', {})
            unread_list = works_data.get('unreadWorks', [])
            our_task_found = any(str(work.get('taskId', '')) == str(task_id) and work.get('status', '') == "SUCCESS_UNREAD" for work in unread_list)
            if our_task_found:
                cover_url = cover.get('resource', '')
                if cover_url and 'default_msg_cover' not in cover_url and cover.get('width', 0) > 1 and cover.get('height', 0) > 1:
                    return unread_works
            if attempt < max_attempts: await asyncio.sleep(interval)
        return None

    async def process_image(self, image_path, prompt="风格转换"):
        try:
            async with httpx.AsyncClient(timeout=300) as client:
                image_url = await self.upload_image(client, image_path)
                return await self.generate_image(client, image_url, prompt) if image_url else (None, None)
        except:
            return None, None


class KeLingImageToImage(PluginBase):
    description = "可灵AI图生图功能，支持图像编辑、风格转换等"
    author = "XYBot"
    version = "1.0.0"
    plugin_name = "KeLingImageToImage"

    def __init__(self):
        super().__init__()
        self.temp_dir = Path("temp/keling_image_to_image")
        self.temp_dir.mkdir(parents=True, exist_ok=True)
        self._load_config()
        self.user_last_request = {}
        self.emoji_message_cache = {}
        self.quote_command = ["可灵"]

    def _load_config(self):
        try:
            config_path = f"plugins/{self.plugin_name}/config.toml"
            with open(config_path, "rb") as f: config = tomllib.load(f).get(self.plugin_name, {})
        except: config = {}
        self.enable = config.get("enable", True)
        self.command = config.get("command", ["可灵图生图"])
        self.command_format = config.get("command-format", "可灵图生图 [提示词] [图片路径]")
        self.quote_command = config.get("quote", {}).get("command", ["可灵"])
        self.quote_command_format = config.get("quote", {}).get("command-format", "引用图片并发送: 可灵 [提示词]")
        self.cookies = config.get("api", {}).get("api_key", "")
        self.cooldown = config.get("rate_limit", {}).get("cooldown", 15)
        self.natural_response = config.get("natural_response", True)
        self._init_natural_responses()

    def _init_natural_responses(self):
        self.confirm_msgs = ["好的", "收到", "明白了", "嗯", "知道了", "行", "OK"]
        self.error_msgs = ["这可把我难住了", "搞不定了", "这个有点难", "弄不了", "出问题了", "搞不出来", "这个不行"]
        self.rate_limit_msgs = ["你搁这刷bug呢", "慢点慢点", "别这么急", "一个一个来", "太快了太快了", "歇会儿", "别刷了"]

    def _check_rate_limit(self, user_wxid):
        current_time = time.time()
        if user_wxid in self.user_last_request and current_time - self.user_last_request[user_wxid] < self.cooldown: return True
        self.user_last_request[user_wxid] = current_time
        return False

    async def _simple_confirm(self, bot, wxid, custom_message=None):
        if custom_message: await bot.send_text_message(wxid, custom_message)
        elif self.natural_response: await bot.send_text_message(wxid, random.choice(self.confirm_msgs))

    @on_text_message
    async def handle_text(self, bot: WechatAPIClient, message: dict):
        """处理文本消息"""
        if not self.enable:
            return

        content = str(message["Content"]).strip()
        wxid = message["FromWxid"]
        user_wxid = message["SenderWxid"]

        # 检查是否是帮助命令
        if content in ["可灵图生图帮助", "可灵图生图说明", "可灵图生图指令"]:
            await self._send_usage_instructions(bot, wxid, user_wxid)
            return

        # 检查是否是插件命令
        command_parts = content.split(" ", 2)  # 分割成三部分：命令、提示词、图片路径

        # 标准图生图命令处理
        if command_parts[0] in self.command:
            # 标准图生图命令处理
            if len(command_parts) < 3:
                await bot.send_at_message(
                    wxid,
                    f"❌ 命令格式错误，正确格式: {self.command_format}",
                    [user_wxid]
                )
                # 发送使用说明
                await self._send_usage_instructions(bot, wxid, user_wxid)
                return

            # 检查限流
            if self._check_rate_limit(user_wxid):
                if self.natural_response:
                    rate_limit_msg = random.choice(self.rate_limit_msgs)
                    await bot.send_text_message(wxid, rate_limit_msg)
                else:
                    await bot.send_at_message(wxid, "⏳ 请求太频繁，请稍后再试", [user_wxid])
                return

            # 获取提示词和图片路径
            prompt = command_parts[1].strip()
            image_path = command_parts[2].strip()

            # 检查图片是否存在
            if not os.path.exists(image_path):
                await bot.send_at_message(
                    wxid,
                    f"❌ 图片不存在: {image_path}",
                    [user_wxid]
                )
                # 发送使用说明
                await self._send_usage_instructions(bot, wxid, user_wxid)
                return

            try:
                await self._test_upload_flow(bot, wxid, user_wxid, prompt, image_path)
            except Exception as e:
                await bot.send_at_message(wxid, f"❌ 处理失败: {str(e)}", [user_wxid])

    @on_quote_message
    async def handle_quote(self, bot: WechatAPIClient, message: dict):
        """处理引用消息"""
        if not self.enable:
            return

        content = str(message["Content"]).strip()
        wxid = message["FromWxid"]
        user_wxid = message["SenderWxid"]

        # 解析引用消息内容
        command_parts = content.split(" ", 1)
        if len(command_parts) < 1 or command_parts[0] not in self.quote_command:
            return

        # 获取提示词
        prompt = command_parts[1].strip() if len(command_parts) > 1 else "风格转换"

        if self._check_rate_limit(user_wxid):
            await bot.send_text_message(wxid, random.choice(self.rate_limit_msgs) if self.natural_response else "⏳ 请求太频繁，请稍后再试")
            return
        try:
            await self._handle_quoted_image(bot, message, prompt)
        except Exception as e:
            await bot.send_at_message(wxid, f"❌ 处理引用图片失败: {str(e)}", [user_wxid])

    @on_emoji_message
    async def handle_emoji(self, bot, message):
        if not self.enable: return
        try:
            msg_id = message.get('NewMsgId') or message.get('MsgId')
            if not msg_id: return
            emoji_info = {'EmojiUrl': message.get('EmojiUrl'), 'EmojiMD5': message.get('EmojiMD5'), 'EmojiLen': message.get('EmojiLen'), 'Content': message.get('Content', ''), 'CreateTime': message.get('CreateTime'), 'SenderWxid': message.get('SenderWxid')}
            content = message.get('Content', '')
            if 'aeskey=' in content:
                try:
                    aeskey_start = content.find('aeskey="') + 8
                    aeskey_end = content.find('"', aeskey_start)
                    if aeskey_start > 7 and aeskey_end > aeskey_start: emoji_info['aeskey'] = content[aeskey_start:aeskey_end]
                except: pass
            self.emoji_message_cache[str(msg_id)] = emoji_info
            if len(self.emoji_message_cache) > 1000:
                for key in list(self.emoji_message_cache.keys())[:100]: del self.emoji_message_cache[key]
        except: pass

    async def _test_upload_flow(self, bot, wxid, user_wxid, prompt, image_path):
        try:
            await self._simple_confirm(bot, wxid)
            if not self.cookies:
                await bot.send_at_message(wxid, "❌ 可灵AI Cookie未配置，请在配置文件中设置api_key", [user_wxid])
                return
            generator = KeLingImageGenerator(self.cookies)
            result_url, text_response = await generator.process_image(image_path, prompt)
            if result_url:
                await self._download_and_send_image(bot, wxid, result_url, user_wxid)
                if text_response and text_response.strip(): await bot.send_text_message(wxid, text_response.strip())
            else:
                error_msg = random.choice(self.error_msgs) if self.natural_response else (text_response or "图片生成失败")
                await bot.send_at_message(wxid, f"❌ {error_msg}", [user_wxid])
        except Exception as e:
            error_msg = random.choice(self.error_msgs) if self.natural_response else f"❌ 处理异常: {str(e)}"
            await (bot.send_text_message(wxid, error_msg) if self.natural_response else bot.send_at_message(wxid, error_msg, [user_wxid]))

    async def _handle_quoted_image(self, bot, message, prompt):
        wxid = message["FromWxid"]
        user_wxid = message["SenderWxid"]
        try:
            await self._simple_confirm(bot, wxid)
            if not self.cookies:
                await bot.send_at_message(wxid, "❌ 可灵AI Cookie未配置，请在配置文件中设置api_key", [user_wxid])
                return
            quote_info = message.get("Quote", {})
            quoted_msg_id = quote_info.get("Msgid") or quote_info.get("NewMsgId")
            quoted_sender = quote_info.get("SenderWxid") or quote_info.get("FromWxid")
            bot_wxid = getattr(bot, 'wxid', None)
            if not quoted_msg_id:
                await bot.send_at_message(wxid, "❌ 未找到引用的消息", [user_wxid])
                return
            quoted_msg_type = quote_info.get("MsgType")
            if quoted_msg_type not in [3, 47]:
                await bot.send_at_message(wxid, f"❌ 请引用图片或表情消息 (当前引用消息类型: {quoted_msg_type})", [user_wxid])
                return
            if quoted_sender == bot_wxid:
                await bot.send_at_message(wxid, "❌ 暂不支持处理机器人发送的图片\n请引用其他用户发送的图片", [user_wxid])
                return
            image_path = await self._download_quoted_image_from_quote_info(bot, quote_info)
            if not image_path:
                await bot.send_at_message(wxid, "❌ 下载引用图片失败", [user_wxid])
                return
            generator = KeLingImageGenerator(self.cookies)
            result_url, text_response = await generator.process_image(image_path, prompt)

            if result_url:
                await self._download_and_send_image(bot, wxid, result_url, user_wxid)
                if text_response and text_response.strip(): await bot.send_text_message(wxid, text_response.strip())
            else:
                error_msg = random.choice(self.error_msgs) if self.natural_response else (text_response or "图片生成失败")
                await bot.send_at_message(wxid, f"❌ {error_msg}", [user_wxid])
            if image_path: cleanup_file(image_path, delay_seconds=5)
        except Exception as e:
            error_msg = random.choice(self.error_msgs) if self.natural_response else f"❌ 处理异常: {str(e)}"
            await (bot.send_text_message(wxid, error_msg) if self.natural_response else bot.send_at_message(wxid, error_msg, [user_wxid]))

    async def _download_quoted_image_from_quote_info(self, bot, quote_info):
        try:
            quoted_msg_id = quote_info.get("Msgid") or quote_info.get("NewMsgId")
            quoted_msg_type = quote_info.get("MsgType")
            if quoted_msg_type == 47:
                return await self._download_emoji_image(self.emoji_message_cache[str(quoted_msg_id)]) if str(quoted_msg_id) in self.emoji_message_cache else None
            if quoted_msg_type == 3:
                quote_content = quote_info.get("Content", "")
                if not quote_content: return None
                import xml.etree.ElementTree as ET
                try:
                    root = ET.fromstring(quote_content)
                    img_node = root.find('.//img')
                    if img_node is None:
                        refermsg = root.find('.//refermsg')
                        if refermsg is not None and refermsg.find('content') is not None:
                            content_text = refermsg.find('content').text
                            if content_text:
                                content_text = content_text.replace('&lt;', '<').replace('&gt;', '>')
                                try:
                                    inner_root = ET.fromstring(content_text)
                                    img_node = inner_root.find('img')
                                except: pass
                    if img_node is None:
                        if "aeskey=" in quote_content and "cdnmidimgurl=" in quote_content:
                            aeskey = quote_content[quote_content.find('aeskey="') + 8:quote_content.find('"', quote_content.find('aeskey="') + 8)]
                            cdnmidimgurl = quote_content[quote_content.find('cdnmidimgurl="') + 14:quote_content.find('"', quote_content.find('cdnmidimgurl="') + 14)]
                        elif "cdnthumbaeskey=" in quote_content and "cdnthumburl=" in quote_content:
                            aeskey = quote_content[quote_content.find('cdnthumbaeskey="') + 16:quote_content.find('"', quote_content.find('cdnthumbaeskey="') + 16)]
                            cdnmidimgurl = quote_content[quote_content.find('cdnthumburl="') + 13:quote_content.find('"', quote_content.find('cdnthumburl="') + 13)]
                        else: return None
                    else:
                        aeskey = img_node.get('aeskey') or img_node.get('cdnthumbaeskey')
                        cdnmidimgurl = img_node.get('cdnmidimgurl') or img_node.get('cdnthumburl')
                    return await self._download_image_via_wechat_api(bot, aeskey, cdnmidimgurl) if aeskey and cdnmidimgurl else None
                except: return None
            return None
        except: return None

    async def _download_image_via_wechat_api(self, bot, aeskey, cdnmidimgurl):
        try:
            image_base64 = await bot.download_image(aeskey, cdnmidimgurl)
            if not image_base64: return None
            temp_file = self.temp_dir / f"quoted_image_{int(time.time())}.jpg"
            with open(temp_file, "wb") as f: f.write(base64.b64decode(image_base64))
            return str(temp_file)
        except: return None

    async def _download_emoji_image(self, emoji_info):
        try:
            emoji_url = emoji_info.get('EmojiUrl')
            if not emoji_url: return None
            temp_path = self.temp_dir / f"emoji_{int(time.time())}_{random.randint(1000, 9999)}.bin"
            async with httpx.AsyncClient(timeout=30) as client:
                response = await client.get(emoji_url)
                if response.status_code == 200:
                    with open(temp_path, 'wb') as f: f.write(response.content)
                    return self._process_emoji_to_image(str(temp_path))
            return None
        except: return None

    def _process_emoji_to_image(self, emoji_path):
        try:
            gif_path = emoji_path.replace('.bin', '.gif')
            if emoji_path != gif_path: os.rename(emoji_path, gif_path)
            return gif_path or emoji_path
        except: return None

    async def _download_image_from_url(self, url):
        try:
            temp_path = self.temp_dir / f"image_{int(time.time())}_{random.randint(1000, 9999)}.jpg"
            async with httpx.AsyncClient(timeout=30) as client:
                response = await client.get(url)
                if response.status_code == 200:
                    with open(temp_path, 'wb') as f: f.write(response.content)
                    return str(temp_path)
            return None
        except: return None

    async def _download_and_send_image(self, bot, wxid, image_url, user_wxid):
        try:
            temp_path = self.temp_dir / f"result_{int(time.time())}_{random.randint(1000, 9999)}.png"
            headers = {'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36', 'Accept': 'image/webp,image/apng,image/*,*/*;q=0.8', 'Accept-Language': 'zh-CN,zh;q=0.9,en;q=0.8', 'Accept-Encoding': 'gzip, deflate, br', 'Referer': 'https://klingai.kuaishou.com/', 'Connection': 'keep-alive'}
            async with httpx.AsyncClient(timeout=60.0) as client:
                response = await client.get(image_url, headers=headers)
                if response.status_code == 200:
                    image_data = response.content
                    if len(image_data) < 1024:
                        await bot.send_at_message(wxid, "❌ 下载的图片数据异常", [user_wxid])
                        return
                    result = await bot.send_image_message(wxid, image_data)
                    if not (result and result[2] != 0):
                        with open(temp_path, 'wb') as f: f.write(image_data)
                        file_result = await bot.send_file_message(wxid, str(temp_path))
                        if file_result and file_result[2] != 0:
                            cleanup_file(temp_path, delay_seconds=10)
                        else:
                            await bot.send_at_message(wxid, "❌ 图片发送失败", [user_wxid])
                else:
                    await bot.send_at_message(wxid, f"❌ 下载生成图片失败: HTTP {response.status_code}", [user_wxid])
        except Exception as e:
            await bot.send_at_message(wxid, f"❌ 发送图片失败: {str(e)}", [user_wxid])

    async def _send_usage_instructions(self, bot, wxid, user_wxid):
        usage_text = f"🎨 可灵AI图生图功能\n📝 基本用法：引用图片 + 可灵 [提示词]\n💡 提示词示例：风格转换、卡通风格、油画风格、水彩画风格、科幻风格\n⚙️ 配置要求：需要在配置文件中设置可灵AI的Cookie，请求间隔: {self.cooldown}秒"
        await bot.send_at_message(wxid, usage_text, [user_wxid])
