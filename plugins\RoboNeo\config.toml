[RoboNeo]
enable = true
command = ["美图"]
natural_response = true  # 启用自然化响应，避免机械化回复
default_prompt = "换成泳衣"  # 默认提示词
command-format = """
🎨 美图RoboNeo插件使用说明

📝 使用方法：
• 引用图片消息并发送: 美图+提示词
• 例如: 美图换成泳衣
• 例如: 美图+换成古装

💡 提示词示例：
• 换成泳衣
• 换成古装
• 换成婚纱
• 换成职业装
• 换成运动装

🤖 AI交互功能：
• AI可能会询问更多细节（如风格、颜色等）
• 收到AI询问时，直接回答即可
• 例如：AI问"什么风格的泳衣？" 你回答"比基尼"

⏱️ 处理时间: 约30-180秒
🔄 支持多种风格转换
"""

[RoboNeo.quote]
command = ["美图"]
command-format = "引用图片并发送: 美图+提示词"

[RoboNeo.api]
policy_url = "https://strategy.app.meitudata.com/upload/policy"
generation_url = "https://ai-engine-gateway-roboneo.meitu.com/roboneo/sync/request/stream"
history_url = "https://ai-engine-gateway-roboneo.meitu.com/roboneo/sync/request"
access_token = "_v2NjUyYzUxNGMjMTc2MDg0NzIzNCM4Mzg4ODY1IzExIzExODlhYTMxNGRmY2Q5NGU1ZGFjNDNmYjg3ZThiYmQwYmYjSFVBV0VJX0NMT1VEI0JKX0hXIzY4N2RiZTgy"
client_id = "1189857651"
uid = "1697403212"
gnum = "2886031057"

[RoboNeo.rate_limit]
cooldown = 60  # 60秒冷却时间，因为处理时间较长

# 超时设置
max_wait_time = 180  # 最大等待时间180秒
