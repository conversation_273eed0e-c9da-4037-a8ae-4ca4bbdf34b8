# RevokePlugin - 消息撤回插件

## 功能介绍

RevokePlugin 是一个用于撤回机器人发送消息的插件。它可以自动记录机器人发送的消息信息，并提供撤回功能。

## 主要特性

- 🔄 **自动拦截消息**：自动记录机器人发送的所有消息ID信息
- ⏰ **时间限制**：遵循微信2分钟撤回限制
- 📝 **消息列表**：查看可撤回的消息列表和剩余时间
- 🎯 **精确撤回**：支持撤回指定序号的消息
- 🛡️ **限流保护**：防止频繁操作

## 使用方法

### 基本命令

1. **撤回最后一条消息**
   ```
   撤回
   ```

2. **撤回指定消息**
   ```
   撤回 2    # 撤回倒数第2条消息
   撤回 3    # 撤回倒数第3条消息
   ```

3. **引用撤回（推荐）**
   ```
   回复某条消息 + 发送"撤回"
   ```
   这是最精确的撤回方式，直接撤回被引用的消息

4. **查看消息列表**
   ```
   消息列表
   ```

5. **查看使用说明**
   ```
   撤回（不带参数）
   ```

### 示例对话

**方式一：序号撤回**
```
用户: 消息列表
Bot: 📝 可撤回的消息列表：
     1. 14:30:25 - ✅ 剩余 95秒
     2. 14:29:10 - ✅ 剩余 20秒
     3. 14:28:45 - ❌ 已超时

     💡 使用 '撤回 数字' 来撤回对应消息

用户: 撤回 1
(消息被撤回，无提示)
```

**方式二：引用撤回（推荐）**
```
Bot: 这是一条需要撤回的消息
用户: [引用上面的消息] 撤回
(消息被撤回，无提示)
```

## 配置说明

### 基本配置
- `enable`: 是否启用插件
- `command`: 触发命令列表

### 限制配置
- `time_limit`: 撤回时间限制（秒），默认120秒
- `max_count`: 最多记录消息数量，默认10条
- `admin_only`: 是否仅管理员可用，默认false

### 限流配置
- `cooldown`: 操作冷却时间（秒），默认3秒

## 技术实现

### 消息拦截机制
插件在启用时立即包装 `WechatAPIClient` 的发送方法来拦截消息ID：
- `send_text_message`
- `send_image_message`
- `send_voice_message`
- `send_app_message`
- `send_at_message`

这确保了从插件加载开始，所有机器人发送的消息都会被记录。

### 消息存储
使用 `deque` 数据结构存储消息信息，自动限制存储数量：
```python
{
    'client_msg_id': int,
    'create_time': int,
    'new_msg_id': int,
    'timestamp': datetime
}
```

### 撤回API
调用微信API的 `revoke_message` 方法：
```python
await bot.revoke_message(wxid, client_msg_id, create_time, new_msg_id)
```

## 注意事项

1. **时间限制**：微信只允许撤回2分钟内的消息
2. **权限要求**：需要机器人有撤回消息的权限
3. **存储限制**：默认只记录最近10条消息
4. **自动清理**：超时的消息会自动标记但不会立即删除

## 错误处理

插件包含完善的错误处理机制：
- 网络异常处理
- 权限不足处理
- 消息不存在处理
- 超时消息处理
