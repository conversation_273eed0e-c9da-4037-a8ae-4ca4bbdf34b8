import asyncio
import tomllib
import time
from random import choice
from loguru import logger

from WechatAPI import WechatAPIClient
from database.XYBotDB import XYBotDB
from utils.decorators import *
from utils.plugin_base import PluginBase


class Leaderboard(PluginBase):
    description = "积分榜"
    author = "HenryXiaoYang"
    version = "1.0.1"  # 更新版本号

    def __init__(self):
        super().__init__()

        with open("plugins/Leaderboard/config.toml", "rb") as f:
            plugin_config = tomllib.load(f)

        config = plugin_config["Leaderboard"]

        self.enable = config["enable"]
        self.command = config["command"]
        self.max_count = config["max-count"]

        self.db = XYBotDB()

        # 添加消息去重相关属性
        self.processed_messages = {}  # 存储已处理的消息ID
        self.message_expiry = 60  # 消息ID过期时间（秒）
        self.last_cleanup = time.time()  # 上次清理时间
        self.cleanup_interval = 300  # 清理间隔（秒）

    def _cleanup_processed_messages(self):
        """清理过期的已处理消息ID"""
        now = time.time()

        # 如果距离上次清理未达到间隔时间，跳过
        if now - self.last_cleanup < self.cleanup_interval:
            return

        # 清理过期的消息ID
        expired_keys = []
        for msg_id, timestamp in self.processed_messages.items():
            if now - timestamp > self.message_expiry:
                expired_keys.append(msg_id)

        # 删除过期的消息ID
        for msg_id in expired_keys:
            self.processed_messages.pop(msg_id, None)

        # 如果有消息被清理，记录日志
        if expired_keys:
            pass

        # 更新上次清理时间
        self.last_cleanup = now

    @on_text_message
    async def handle_text(self, bot: WechatAPIClient, message: dict):
        if not self.enable:
            return

        content = str(message["Content"]).strip()
        command = content.split(" ")

        if command[0] not in self.command:
            return

        # 添加消息去重逻辑
        msg_id = message.get("MsgId")
        if not msg_id:
            logger.warning("[Leaderboard] 消息缺少MsgId，无法进行去重")
        else:
            # 检查消息是否已处理
            if msg_id in self.processed_messages:

                return

            # 记录已处理的消息
            self.processed_messages[msg_id] = time.time()

        # 清理过期的消息ID
        self._cleanup_processed_messages()

        if "群" in command[0]:
            chatroom_members = await bot.get_chatroom_member_list(message["FromWxid"])
            data = []
            for member in chatroom_members:
                wxid = member["UserName"]
                points = self.db.get_points(wxid)
                if points == 0:
                    continue
                data.append((member["NickName"], points))

            data.sort(key=lambda x: x[1], reverse=True)
            data = data[:self.max_count]

            out_message = "-----XYBot积分群排行榜-----"
            rank_emojis = ["👑", "🥈", "🥉"]
            for rank, (nickname, points) in enumerate(data, start=1):
                emoji = rank_emojis[rank - 1] if rank <= 3 else ""
                random_emoji = choice(
                    ["😄", "😃", "😁", "😆", "😊", "😍", "😋", "😎", "🤗", "😺", "🥳", "🤩", "🎉", "⭐", "🎊", "🎈", "🌟", "✨", "🎶",
                     "❤️", "😛"])
                out_message += f"\n{emoji}{'' if emoji else str(rank) + '.'} {nickname}   {points}分  {random_emoji}"

        else:
            data = self.db.get_leaderboard(self.max_count)

            wxids = [i[0] for i in data]
            nicknames = []

            async def get_nicknames_chunk(chunk_wxids):
                return await bot.get_nickname(chunk_wxids)

            # 将wxids分成每组20个
            chunks = [wxids[i:i + 20] for i in range(0, len(wxids), 20)]
            # 使用信号量限制并发数为2
            sem = asyncio.Semaphore(2)

            async def worker(chunk):
                async with sem:
                    return await get_nicknames_chunk(chunk)

            # 并发执行所有请求
            tasks = [worker(chunk) for chunk in chunks]
            results = await asyncio.gather(*tasks)

            # 将所有结果合并到nicknames列表中
            for result in results:
                nicknames.extend(result)

            out_message = "-----XYBot积分排行榜-----"
            rank_emojis = ["👑", "🥈", "🥉"]
            for rank, (i, nickname) in enumerate(zip(data, nicknames), start=1):
                wxid, points = i
                nickname = nickname or wxid
                emoji = rank_emojis[rank - 1] if rank <= 3 else ""
                random_emoji = choice(
                    ["😄", "😃", "😁", "😆", "😊", "😍", "😋", "😎", "🤗", "😺", "🥳", "🤩", "🎉", "⭐", "🎊", "🎈", "🌟", "✨", "🎶",
                     "❤️", "😛"])
                out_message += f"\n{emoji}{'' if emoji else str(rank) + '.'} {nickname}   {points}分  {random_emoji}"

        await bot.send_text_message(message["FromWxid"], out_message)
