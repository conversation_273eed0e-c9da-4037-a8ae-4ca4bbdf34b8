DEFAULT_STYLESHEET = {
    'light': {
        'width': 800,
        'background_color': '#F7F7F9',
        'text_color': '#2C3E50',
        'title_color': '#1A5FB4',
        'border_color': '#1A5FB4', 
        'border_width': 2,
        'border_radius': 25,
        'font_size': 28,
        'title_font_size': 42,
        'line_spacing': 1.6,
        'padding': 35,
        'shadow': True,
        'gradient': False,
        'title_spacing': 25,
        'enhance_emoji': True
    },
    'dark': {
        'width': 800,
        'background_color': '#1A1B26',
        'text_color': '#FFFFFF',
        'title_color': '#7AA2F7',
        'border_color': '#7AA2F7',
        'border_width': 2,
        'border_radius': 25,
        'font_size': 28,
        'title_font_size': 42,
        'line_spacing': 1.6,
        'padding': 35,
        'shadow': True,
        'gradient': True,
        'title_spacing': 25,
        'enhance_emoji': True
    }
}

# 添加新的样式表

TECH_STYLESHEETS = {
    'modern': {
        'width': 850,
        'background_color': '#0D1117',
        'text_color': '#E6EDF3',
        'title_color': '#58A6FF',
        'border_color': '#58A6FF',
        'border_width': 3,
        'border_radius': 40,
        'font_size': 34,
        'title_font_size': 52,
        'line_spacing': 1.8,
        'padding': 50,
        'shadow': True,
        'gradient': True,
        'tech_effect': True,
        'border_style': 'neon'
    },
    'matrix': {
        'width': 850,
        'background_color': '#000000',
        'text_color': '#00FF00',
        'title_color': '#00FF00',
        'border_color': '#00FF00',
        'border_width': 3,
        'border_radius': 35,
        'font_size': 32,
        'title_font_size': 48,
        'line_spacing': 1.6,
        'padding': 45,
        'shadow': True,
        'gradient': True,
        'tech_effect': True,
        'border_style': 'stripe',
        'border_accent_color': '#88FF88'
    },
    'hologram': {
        'width': 850,
        'background_color': '#102040',
        'text_color': '#88CCFF',
        'title_color': '#00FFFF',
        'border_color': '#00FFFF',
        'border_width': 3,
        'border_radius': 40,
        'font_size': 34,
        'title_font_size': 50,
        'line_spacing': 1.7,
        'padding': 45,
        'shadow': True,
        'gradient': True,
        'tech_effect': True,
        'border_style': 'glow',
        'border_accent_color': '#0088FF'
    },
    'cyberpunk': {
        'width': 850,
        'background_color': '#0F0F2D',
        'text_color': '#FFFFFF',
        'title_color': '#FF00FF',
        'border_color': '#FF00FF',
        'border_width': 4,
        'border_radius': 35,
        'font_size': 32,
        'title_font_size': 48,
        'line_spacing': 1.6,
        'padding': 45,
        'shadow': True,
        'gradient': True,
        'tech_effect': True,
        'border_style': 'gradient',
        'border_accent_color': '#00FFFF'
    }
}

# 添加精美边框样式表
FANCY_BORDER_STYLESHEETS = {
    'neon': {
        'width': 850,
        'is_dark': True,
        'background_color': '#1A1A2A',
        'text_color': '#FFFFFF',
        'title_color': '#4DC4FF',
        'border_color': '#4DC4FF',
        'border_width': 3,
        'border_radius': 40,
        'font_size': 34,
        'title_font_size': 48,
        'line_spacing': 1.8,
        'padding': 45,
        'shadow': True,
        'gradient': True,
        'border_style': 'neon'
    },
    'gradient': {
        'width': 850,
        'is_dark': True,
        'background_color': '#0D1117',
        'text_color': '#FFFFFF',
        'title_color': '#FF6B8B',
        'border_color': '#FF6B8B',
        'border_width': 3,
        'border_radius': 40,
        'font_size': 34,
        'title_font_size': 48,
        'line_spacing': 1.8,
        'padding': 45,
        'shadow': True,
        'gradient': True,
        'border_style': 'gradient',
        'border_accent_color': '#4DA6FF'
    },
    'glow': {
        'width': 850,
        'is_dark': True,
        'background_color': '#0A0A14',
        'text_color': '#F0F0F0',
        'title_color': '#FFD700',
        'border_color': '#FFD700',
        'border_width': 3,
        'border_radius': 40,
        'font_size': 34,
        'title_font_size': 48,
        'line_spacing': 1.8,
        'padding': 45,
        'shadow': False,
        'gradient': True,
        'border_style': 'glow',
        'border_accent_color': '#FFA500'
    },
    'double': {
        'width': 850,
        'is_dark': True,
        'background_color': '#131822',
        'text_color': '#FFFFFF',
        'title_color': '#64B5F6',
        'border_color': '#64B5F6',
        'border_width': 3,
        'border_radius': 40,
        'font_size': 34,
        'title_font_size': 48,
        'line_spacing': 1.8,
        'padding': 45,
        'shadow': True,
        'gradient': True,
        'border_style': 'double',
        'border_accent_color': '#81C784'
    },
    'stripe': {
        'width': 850,
        'is_dark': True,
        'background_color': '#0F1522',
        'text_color': '#FFFFFF',
        'title_color': '#BA68C8',
        'border_color': '#BA68C8',
        'border_width': 3,
        'border_radius': 40,
        'font_size': 34,
        'title_font_size': 48,
        'line_spacing': 1.8,
        'padding': 45,
        'shadow': True,
        'gradient': True,
        'border_style': 'stripe',
        'border_accent_color': '#4DD0E1'
    }
}

# 主题集合
THEMES = {
    **DEFAULT_STYLESHEET,
    **{f"tech_{k}": v for k, v in TECH_STYLESHEETS.items()},
    **{f"fancy_{k}": v for k, v in FANCY_BORDER_STYLESHEETS.items()}
}

def get_style(style_name=None):
    """获取指定名称的样式，如果不存在则返回默认样式"""
    if not style_name:
        return DEFAULT_STYLESHEET['light']
    
    if style_name in THEMES:
        return THEMES[style_name]
    
    # 处理特殊情况
    if style_name == 'tech':
        return TECH_STYLESHEETS['modern']
    if style_name == 'fancy':
        return FANCY_BORDER_STYLESHEETS['neon']
    
    # 回退到默认
    return DEFAULT_STYLESHEET['light']

def list_styles():
    """列出所有可用的样式名称"""
    return list(THEMES.keys()) 