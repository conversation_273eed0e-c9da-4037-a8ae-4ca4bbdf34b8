#!/usr/bin/env python3
"""
测试带缩略图的小程序XML格式
"""
import xml.etree.ElementTree as ET

def test_thumbnail_xml():
    """测试带缩略图的小程序XML"""
    xml_content = '''<appmsg appid="wxa708de63ee4a2353" sdkver="0">
<title>唱舞星愿站（唱舞全明星）</title>
<des>星愿站</des>
<action>view</action>
<type>33</type>
<showtype>0</showtype>
<content/>
<url>https://weixin.qq.com</url>
<lowurl>https://weixin.qq.com</lowurl>
<dataurl/>
<lowdataurl/>
<appattach>
    <totallen>0</totallen>
    <attachid/>
    <emoticonmd5/>
    <fileext/>
    <cdnthumburl>3057020100044b30490201000204a95c809d02032df98b02048e009324020468464c38042433636534386265392d386261652d346637622d383130362d3433346461343836373162610204051808030201000405004c57c300</cdnthumburl>
    <cdnthumbmd5>53a81a7ea69f1857dc306224ec7bf272</cdnthumbmd5>
    <cdnthumblength>142524</cdnthumblength>
    <cdnthumbheight>360</cdnthumbheight>
    <cdnthumbwidth>450</cdnthumbwidth>
    <cdnthumbaeskey>35b62ce09aa2bfd9cb3d3c18d417ff8c</cdnthumbaeskey>
    <aeskey>35b62ce09aa2bfd9cb3d3c18d417ff8c</aeskey>
    <encryver>1</encryver>
    <islargefilemsg>0</islargefilemsg>
</appattach>
<extinfo/>
<sourceusername>wxa708de63ee4a2353</sourceusername>
<sourcedisplayname>唱舞星愿站</sourcedisplayname>
<thumburl>http://mmbiz.qpic.cn/mmbiz_png/Y4yibCmxrbX6aCNIhj0BH1CXuE8chunB9AN5fnaRJUMRGRicxkh8crn7P1VCQuhV4GB3KCkE4e5ibmXVEibxaoIJuA/640?wx_fmt=png&amp;wxfrom=200</thumburl>
<md5>53a81a7ea69f1857dc306224ec7bf272</md5>
<weappinfo>
    <pagepath><![CDATA[pages/pointsStroe/wares/index.html?key=abz3BM9k&unionid=oA7D81SRhmAwPJuDQ0Pw39LWcWoY&corpid=wwccefc778261bf00f&app_id=wxa708de63ee4a2353&plate_id=91&userid=40610459]]></pagepath>
    <username>gh_25eb09d7bc53@app</username>
    <appid>wxa708de63ee4a2353</appid>
    <version>14</version>
    <type>2</type>
    <weappiconurl>http://mmbiz.qpic.cn/mmbiz_png/Y4yibCmxrbX6aCNIhj0BH1CXuE8chunB9AN5fnaRJUMRGRicxkh8crn7P1VCQuhV4GB3KCkE4e5ibmXVEibxaoIJuA/640?wx_fmt=png&amp;wxfrom=200</weappiconurl>
    <appservicetype>0</appservicetype>
</weappinfo>
</appmsg>'''

    try:
        # 尝试解析XML
        root = ET.fromstring(xml_content)
        print("✅ 带缩略图的XML格式正确！")
        
        # 提取关键信息
        title = root.find('title')
        msg_type = root.find('type')
        url = root.find('url')
        thumburl = root.find('thumburl')
        
        print(f"标题: {title.text if title is not None else 'N/A'}")
        print(f"消息类型: {msg_type.text if msg_type is not None else 'N/A'}")
        print(f"URL: {url.text if url is not None else 'N/A'}")
        print(f"缩略图URL: {thumburl.text if thumburl is not None else 'N/A'}")
        
        # 检查appattach中的缩略图信息
        appattach = root.find('appattach')
        if appattach is not None:
            cdnthumburl = appattach.find('cdnthumburl')
            cdnthumbmd5 = appattach.find('cdnthumbmd5')
            cdnthumblength = appattach.find('cdnthumblength')
            cdnthumbwidth = appattach.find('cdnthumbwidth')
            cdnthumbheight = appattach.find('cdnthumbheight')
            
            print(f"CDN缩略图URL: {cdnthumburl.text if cdnthumburl is not None else 'N/A'}")
            print(f"CDN缩略图MD5: {cdnthumbmd5.text if cdnthumbmd5 is not None else 'N/A'}")
            print(f"CDN缩略图大小: {cdnthumblength.text if cdnthumblength is not None else 'N/A'} bytes")
            print(f"CDN缩略图尺寸: {cdnthumbwidth.text if cdnthumbwidth is not None else 'N/A'}x{cdnthumbheight.text if cdnthumbheight is not None else 'N/A'}")
        
        # 检查weappinfo
        weappinfo = root.find('weappinfo')
        if weappinfo is not None:
            appid = weappinfo.find('appid')
            username = weappinfo.find('username')
            weappiconurl = weappinfo.find('weappiconurl')
            print(f"小程序AppID: {appid.text if appid is not None else 'N/A'}")
            print(f"小程序用户名: {username.text if username is not None else 'N/A'}")
            print(f"小程序图标URL: {weappiconurl.text if weappiconurl is not None else 'N/A'}")
        
        return True
        
    except ET.ParseError as e:
        print(f"❌ XML格式错误: {e}")
        return False
    except Exception as e:
        print(f"❌ 其他错误: {e}")
        return False

if __name__ == "__main__":
    print("=== 测试带缩略图的小程序XML格式 ===")
    test_thumbnail_xml()
