#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
美图AI - 对话生图改图工具
基于抓包分析实现的美图AI功能
支持对话、生图、改图等功能
"""

import requests
import json
import time
import uuid
import re
from typing import Optional, Dict, Any, Generator

class MeituAI:
    def __init__(self):
        """初始化美图AI客户端"""
        self.base_url = "https://ai-engine-gateway-roboneo.meitu.com"
        self.access_token = "_v2NjUyYzUxNGMjMTc2MDg0NzIzNCM4Mzg4ODY1IzExIzExODlhYTMxNGRmY2Q5NGU1ZGFjNDNmYjg3ZThiYmQwYmYjSFVBV0VJX0NMT1VEI0JKX0hXIzY4N2RiZTgy"
        self.room_id = str(uuid.uuid4())
        self.session = requests.Session()
        
        # 设置请求头
        self.headers = {
            "Access-Token": self.access_token,
            "Content-Type": "application/json; charset=utf-8",
            "Host": "ai-engine-gateway-roboneo.meitu.com",
            "Connection": "Keep-Alive",
            "Accept-Encoding": "gzip",
            "User-Agent": "okhttp/4.12.0",
            "traceIdHigh": "2886031057",
            "traceIdLow": str(int(time.time() * 1000000))
        }
        
        # 基础参数
        self.base_params = {
            "path_scene": "roboneo",
            "body": "",
            "features": "",
            "image_urls": [],
            "video_urls": [],
            "later_face": 0,
            "room_id": self.room_id,
            "id": "",
            "content": "",
            "type": "",
            "task_id": "",
            "req_mode": "",
            "time_zone": "Asia/Shanghai",
            "platform": "android",
            "client_id": "1189857651",
            "client_os": "10",
            "client_channel_id": "And_NeoHomeMobile",
            "lang": "zh-Hans",
            "area_code": "CN",
            "version": "1.3.0",
            "uid": "1697403212",
            "gid": "2886031057",
            "theme": 2,
            "app_scene": "roboneo",
            "token": "7E14E5FF92D54108A074A34D2083C93F"
        }
        
        print(f"🎨 美图AI已初始化，房间ID: {self.room_id}")

    def _send_message(self, message: str, image_urls: list = None) -> Generator[Dict[str, Any], None, None]:
        """发送消息到AI并获取流式响应"""
        if image_urls is None:
            image_urls = []
            
        # 构建请求参数
        params = self.base_params.copy()
        params.update({
            "message": message,
            "image_urls": image_urls,
            "trace_id": str(uuid.uuid4())
        })
        
        payload = {"parameter": params}
        
        try:
            # 发送POST请求
            response = self.session.post(
                f"{self.base_url}/roboneo/sync/request/stream",
                headers=self.headers,
                json=payload,
                stream=True,
                timeout=60
            )
            
            if response.status_code != 200:
                print(f"❌ 请求失败: {response.status_code}")
                return
                
            # 解析SSE流式响应
            for line in response.iter_lines(decode_unicode=True):
                if line and line.startswith('data: '):
                    try:
                        data_str = line[6:]  # 去掉 'data: ' 前缀
                        if data_str.strip():
                            data = json.loads(data_str)
                            yield data
                    except json.JSONDecodeError:
                        continue
                        
        except requests.exceptions.RequestException as e:
            print(f"❌ 网络请求错误: {e}")

    def chat(self, message: str) -> str:
        """普通对话功能"""
        print(f"💬 用户: {message}")
        
        full_response = ""
        current_text = ""
        
        for data in self._send_message(message):
            if data.get("type") == "text":
                content = data.get("content", "")
                if content:
                    current_text += content
                    print(content, end="", flush=True)
            elif data.get("type") == "status_change" and data.get("status") == "all_done":
                break
                
        if current_text:
            full_response = current_text
            print()  # 换行
            
        return full_response

    def generate_image(self, prompt: str) -> Optional[str]:
        """生成图片"""
        print(f"🎨 生成图片: {prompt}")
        
        image_url = None
        current_text = ""
        progress = 0
        
        for data in self._send_message(prompt):
            data_type = data.get("type")
            
            if data_type == "text":
                content = data.get("content", "")
                if content:
                    current_text += content
                    print(content, end="", flush=True)
                    
            elif data_type == "request":
                # AI询问更多细节
                question = data.get("question", "")
                if question:
                    print(f"\n🤖 AI询问: {question}")
                    user_input = input("请回答: ")
                    return self.generate_image(user_input)  # 递归处理用户回答
                    
            elif data_type == "design":
                print(f"\n🎨 开始设计，设计ID: {data.get('design_id')}")
                
            elif data_type == "render_percent":
                percent = data.get("percent", [0])[0]
                if percent > progress:
                    progress = percent
                    print(f"\r⏳ 生成进度: {progress}%", end="", flush=True)
                    
            elif data_type == "media":
                media_item = data.get("media_item", {})
                image_url = media_item.get("media_url")
                if image_url:
                    print(f"\n✅ 图片生成完成!")
                    print(f"🖼️  图片地址: {image_url}")
                    
            elif data_type == "status_change" and data.get("status") == "all_done":
                break
                
        if current_text and not image_url:
            print()
            
        return image_url

    def edit_image(self, image_url: str, edit_prompt: str) -> Optional[str]:
        """编辑图片"""
        print(f"✏️  编辑图片: {edit_prompt}")
        print(f"📷 原图: {image_url}")
        
        new_image_url = None
        current_text = ""
        progress = 0
        
        for data in self._send_message(edit_prompt, [image_url]):
            data_type = data.get("type")
            
            if data_type == "text":
                content = data.get("content", "")
                if content:
                    current_text += content
                    print(content, end="", flush=True)
                    
            elif data_type == "design":
                print(f"\n🎨 开始编辑，设计ID: {data.get('design_id')}")
                
            elif data_type == "render_percent":
                percent = data.get("percent", [0])[0]
                if percent > progress:
                    progress = percent
                    print(f"\r⏳ 编辑进度: {progress}%", end="", flush=True)
                    
            elif data_type == "media":
                media_item = data.get("media_item", {})
                new_image_url = media_item.get("media_url")
                if new_image_url:
                    print(f"\n✅ 图片编辑完成!")
                    print(f"🖼️  新图片地址: {new_image_url}")
                    
            elif data_type == "status_change" and data.get("status") == "all_done":
                break
                
        if current_text and not new_image_url:
            print()
            
        return new_image_url

    def interactive_mode(self):
        """交互模式"""
        print("🎨 欢迎使用美图AI!")
        print("💡 支持功能:")
        print("   - 普通对话")
        print("   - 生图: 画一个xxx / 生成xxx图片")
        print("   - 改图: 将图片改成xxx (需要先生成图片)")
        print("   - 退出: quit/exit")
        print("-" * 50)
        
        current_image = None
        
        while True:
            try:
                user_input = input("\n💬 请输入: ").strip()
                
                if user_input.lower() in ['quit', 'exit', '退出']:
                    print("👋 再见!")
                    break
                    
                if not user_input:
                    continue
                    
                # 判断是否为生图请求
                if any(keyword in user_input for keyword in ['画', '生成', '创作', '制作']):
                    image_url = self.generate_image(user_input)
                    if image_url:
                        current_image = image_url
                        
                # 判断是否为改图请求
                elif any(keyword in user_input for keyword in ['改', '修改', '调整', '换成']):
                    if current_image:
                        new_image = self.edit_image(current_image, user_input)
                        if new_image:
                            current_image = new_image
                    else:
                        print("❌ 请先生成一张图片再进行编辑")
                        
                # 普通对话
                else:
                    self.chat(user_input)
                    
            except KeyboardInterrupt:
                print("\n👋 再见!")
                break
            except Exception as e:
                print(f"❌ 发生错误: {e}")

def main():
    """主函数"""
    ai = MeituAI()
    ai.interactive_mode()

if __name__ == "__main__":
    main()
