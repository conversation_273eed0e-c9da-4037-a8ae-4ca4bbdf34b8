<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Strict//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-strict.dtd">
<html xmlns="http://www.w3.org/1999/xhtml" xml:lang="en" lang="en">
<head>
    <meta http-equiv="content-type" content="text/html; charset=utf-8"/>
    <title>MediaInfo(Lib) License</title>
</head>

<body>
<div>
    <h3 style="text-align: center">MediaInfo(Lib) License</h3>
    <p>
        Copyright (c) 2002-2024 <a href="mailto:<EMAIL>">MediaArea.net SARL</a>. All rights reserved.
    </p>
    <p>
        Redistribution and use in source and binary forms, with or without modification,
        are permitted provided that the following conditions are met:
    </p>
    <ul>
        <li>Redistributions of source code must retain the above copyright notice,
        this list of conditions and the following disclaimer.</li>
        <li>Redistributions in binary form must reproduce the above copyright notice,
        this list of conditions and the following disclaimer in the documentation and/or
        other materials provided with the distribution.</li>
    </ul>
    <p>
        THIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDERS AND CONTRIBUTORS &ldquo;AS IS&rdquo;
        AND ANY EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE IMPLIED WARRANTIES
        OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE ARE DISCLAIMED. IN NO EVENT SHALL THE
        COPYRIGHT HOLDER OR CONTRIBUTORS BE LIABLE FOR ANY DIRECT, INDIRECT, INCIDENTAL, SPECIAL,
        EXEMPLARY, OR CONSEQUENTIAL DAMAGES (INCLUDING, BUT NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE
        GOODS OR SERVICES; LOSS OF USE, DATA, OR PROFITS; OR BUSINESS INTERRUPTION) HOWEVER CAUSED
        AND ON ANY THEORY OF LIABILITY, WHETHER IN CONTRACT, STRICT LIABILITY, OR TORT (INCLUDING
        NEGLIGENCE OR OTHERWISE) ARISING IN ANY WAY OUT OF THE USE OF THIS SOFTWARE, EVEN IF ADVISED
        OF THE POSSIBILITY OF SUCH DAMAGE.
    </p>
</div>

<hr/>
<div>
<p>Alternate open source licenses:<br/>
You can relicense (including source headers change) MediaInfoLib
under Apache License 2.0 or later,
and/or GNU Lesser General Public License 2.1 or later,
and/or GNU General Public License 2.0 or later,
and/or Mozilla Public License 2.0 or later.</p>
</div>

<hr/>
<div>
<p>Alternate license for redistributions of the library in binary form:<br/>
Redistributions in binary form must reproduce the following sentence (including the link to the website) in the documentation and/or other materials provided with the distribution.<br/>
This product uses <a href="http://mediaarea.net/MediaInfo">MediaInfo</a> library, Copyright (c) 2002-2024 <a href="mailto:<EMAIL>">MediaArea.net SARL</a>.</p>
</div>

<hr/>
<div>
    <h3 style="text-align: center">Third party libraries</h3>
    <p>
        The software relies on third party libraries. Such libraries have their own license:
    </p>
        <ul>
            <li>C++ standard library: <a href="http://gcc.gnu.org/onlinedocs/libstdc++/manual/bk01pt01ch01s02.html">libstdc++</a>,
                <a href="http://llvm.org/docs/DeveloperPolicy.html#license">libc++</a>,
                <a href="http://msdn.microsoft.com/en-us/library/vstudio/ms235299.aspx">Visual C++ library</a> or any other C++ standard library.
            </li>
            <li>Base classes library: ZenLib, (c) MediaArea.net SARL, zlib license.</li>
            <li>GUI (optional): <a href="http://qt-project.org/products/licensing">Qt</a>.</li>
            <li>GUI (optional): <a href="http://www.wxwidgets.org/about/newlicen.htm">WxWidgets</a>.</li>
            <li>XML parser (optional): <a href="http://www.grinninglizard.com/tinyxml2docs/index.html">TinyXML-2</a>.</li>
            <li>gzip decompression routine (optional): <a href="http://www.gzip.org/zlib/zlib_license.html">zlib</a>.</li>
            <li>FTP, FTPS, SFTP, HTTP, HTTPS (optional): <a href="http://curl.haxx.se/docs/copyright.html">libcurl</a>.</li>
            <li>AES (optional): <a href="http://brgladman.org/oldsite/AES/">AES routines from Brian Gladman</a>.</li>
            <li>MMS (optional): <a href="http://sourceforge.net/projects/libmms/">libmms</a>.</li>
            <li>JNI (optional): <a href="https://github.com/ricardoquesada/android-ndk/blob/master/usr/include/jni.h">JNI interface from Android NDK</a>.</li>
        </ul>
</div>

<hr/>
<div>
    <h3 style="text-align: center">Contributors</h3>
    <ul>
        <li>Jérôme Martinez (main developper)</li>
        <li>Lionel Duchateau (odd formats support)</li>
        <li>XhmikosR from MPC-HC Team (tests)</li>
        <li>FlylinkDC++ team (tests, crash corrections)</li>
        <li>Max Pozdeev (former native Mac GUI developper)</li>
    </ul>
</div>

</body>
</html>
