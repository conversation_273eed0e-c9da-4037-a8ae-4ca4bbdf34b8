"""
临时文件管理插件
提供手动清理、状态查看等功能
"""

import os
import asyncio
from pathlib import Path
from typing import Dict, Any

from utils.plugin_base import PluginBase
from utils.temp_file_manager import temp_manager, get_temp_stats, manual_cleanup
from utils.decorators import on_text_message
from WechatAPI import WechatAPIClient
from loguru import logger


class TempFileManagerPlugin(PluginBase):
    description = "临时文件管理插件"
    author = "XYBot"
    version = "1.0.0"

    def __init__(self):
        super().__init__()
        self.plugin_name = "TempFileManager"

    @on_text_message
    async def handle_text_message(self, bot: WechatAPIClient, message: dict):
        """处理文本消息"""
        try:
            content = message["Content"].strip()
            wxid = message["FromWxid"]
            sender_wxid = message["SenderWxid"]

            # 检查是否是管理员
            if not self._is_admin(sender_wxid):
                return

            # 临时文件状态查看
            if content == "临时文件状态" or content == "temp状态":
                await self._show_temp_status(bot, wxid)
                return

            # 手动清理临时文件
            elif content == "清理临时文件" or content == "清理temp":
                await self._manual_cleanup(bot, wxid)
                return

            # 清理指定插件的临时文件
            elif content.startswith("清理临时文件 ") or content.startswith("清理temp "):
                plugin_name = content.split(" ", 1)[1].strip()
                await self._cleanup_plugin_temp(bot, wxid, plugin_name)
                return

            # 临时文件帮助
            elif content == "临时文件帮助" or content == "temp帮助":
                await self._show_help(bot, wxid)
                return

        except Exception as e:
            logger.error(f"[{self.plugin_name}] 处理消息时出错: {e}")
            await bot.send_text_message(wxid, f"处理请求时出错: {str(e)}")
    
    def _is_admin(self, sender_wxid: str) -> bool:
        """检查是否是管理员"""
        # 这里可以根据实际需求修改管理员判断逻辑
        # 暂时返回True，实际使用时应该添加管理员列表
        return True
    
    async def _show_temp_status(self, bot, wxid: str):
        """显示临时文件状态"""
        try:
            stats = get_temp_stats()
            
            if not stats:
                await bot.send_text_message(wxid, "📁 临时文件目录为空")
                return
            
            # 构建状态报告
            report_lines = ["📊 临时文件状态报告", "=" * 30]
            
            total_files = 0
            total_size = 0
            
            for plugin_name, plugin_stats in stats.items():
                file_count = plugin_stats["file_count"]
                size_mb = plugin_stats["total_size_mb"]
                oldest_hours = plugin_stats["oldest_file_age_hours"]
                
                total_files += file_count
                total_size += size_mb
                
                # 状态图标
                if size_mb > 100:
                    icon = "🔴"  # 大文件
                elif oldest_hours > 24:
                    icon = "🟡"  # 老文件
                elif file_count > 50:
                    icon = "🟠"  # 文件多
                else:
                    icon = "🟢"  # 正常
                
                report_lines.append(
                    f"{icon} {plugin_name}:\n"
                    f"   文件数: {file_count}\n"
                    f"   大小: {size_mb:.2f}MB\n"
                    f"   最老文件: {oldest_hours:.1f}小时前"
                )
            
            report_lines.extend([
                "=" * 30,
                f"📈 总计: {total_files} 个文件, {total_size:.2f}MB",
                "",
                "💡 使用 '清理临时文件' 进行清理",
                "💡 使用 '清理temp 插件名' 清理指定插件"
            ])
            
            await bot.send_text_message(wxid, "\n".join(report_lines))
            
        except Exception as e:
            logger.error(f"[{self.plugin_name}] 显示状态时出错: {e}")
            await bot.send_text_message(wxid, f"获取状态时出错: {str(e)}")
    
    async def _manual_cleanup(self, bot, wxid: str):
        """手动清理临时文件"""
        try:
            await bot.send_text_message(wxid, "🧹 开始清理临时文件...")

            # 执行清理
            results = manual_cleanup()

            if not results:
                await bot.send_text_message(wxid, "✅ 没有需要清理的文件")
                return
            
            # 构建清理报告
            report_lines = ["🧹 清理完成报告", "=" * 25]
            
            total_cleaned = 0
            total_size = 0
            total_failed = 0
            
            for plugin_name, result in results.items():
                cleaned = result["cleaned"]
                failed = result["failed"]
                size_mb = result["size_mb"]
                
                total_cleaned += cleaned
                total_failed += failed
                total_size += size_mb
                
                if cleaned > 0 or failed > 0:
                    status = "✅" if failed == 0 else "⚠️"
                    report_lines.append(
                        f"{status} {plugin_name}:\n"
                        f"   清理: {cleaned} 个文件\n"
                        f"   失败: {failed} 个文件\n"
                        f"   释放: {size_mb:.2f}MB"
                    )
            
            report_lines.extend([
                "=" * 25,
                f"📊 总计: 清理 {total_cleaned} 个文件",
                f"💾 释放空间: {total_size:.2f}MB",
                f"❌ 失败: {total_failed} 个文件" if total_failed > 0 else ""
            ])
            
            # 移除空行
            report_lines = [line for line in report_lines if line]
            
            await bot.send_text_message(wxid, "\n".join(report_lines))

        except Exception as e:
            logger.error(f"[{self.plugin_name}] 手动清理时出错: {e}")
            await bot.send_text_message(wxid, f"清理时出错: {str(e)}")
    
    async def _cleanup_plugin_temp(self, bot, wxid: str, plugin_name: str):
        """清理指定插件的临时文件"""
        try:
            plugin_dir = Path("temp") / plugin_name
            
            if not plugin_dir.exists():
                await bot.send_text_message(wxid, f"❌ 插件 '{plugin_name}' 的临时目录不存在")
                return

            await bot.send_text_message(wxid, f"🧹 开始清理插件 '{plugin_name}' 的临时文件...")
            
            # 清理指定目录
            result = temp_manager.cleanup_directory(plugin_dir)
            
            cleaned = result["cleaned"]
            failed = result["failed"]
            size_mb = result["size_mb"]
            
            if cleaned == 0 and failed == 0:
                await bot.send_text_message(wxid, f"✅ 插件 '{plugin_name}' 没有需要清理的文件")
            else:
                status = "✅" if failed == 0 else "⚠️"
                report = (
                    f"{status} 插件 '{plugin_name}' 清理完成:\n"
                    f"📁 清理文件: {cleaned} 个\n"
                    f"💾 释放空间: {size_mb:.2f}MB"
                )

                if failed > 0:
                    report += f"\n❌ 失败文件: {failed} 个"

                await bot.send_text_message(wxid, report)
            
        except Exception as e:
            logger.error(f"[{self.plugin_name}] 清理插件临时文件时出错: {e}")
            await bot.send_text_message(wxid, f"清理插件 '{plugin_name}' 时出错: {str(e)}")

    async def _show_help(self, bot, wxid: str):
        """显示帮助信息"""
        help_text = """
🛠️ 临时文件管理帮助

📋 可用命令:
• 临时文件状态 / temp状态
  查看所有临时文件的状态统计

• 清理临时文件 / 清理temp
  手动清理所有过期的临时文件

• 清理临时文件 [插件名] / 清理temp [插件名]
  清理指定插件的临时文件

• 临时文件帮助 / temp帮助
  显示此帮助信息

🔧 自动清理规则:
• 图片文件: 2小时后清理
• 视频文件: 30分钟后清理
• 音频文件: 1小时后清理
• 临时文件: 15分钟后清理
• 大文件(>50MB): 30分钟后清理
• 其他文件: 1小时后清理

⏰ 系统每30分钟自动清理一次
🔒 仅管理员可使用此功能
        """.strip()

        await bot.send_text_message(wxid, help_text)


# 插件实例
plugin_instance = TempFileManagerPlugin()
