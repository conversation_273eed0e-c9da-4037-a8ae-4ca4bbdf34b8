2025-07-25 12:45:13 | SUCCESS | 读取主设置成功
2025-07-25 12:45:13 | DEBUG | 最终使用的 Redis 主机地址: 127.0.0.1
2025-07-25 12:45:13 | INFO | 2025/07/25 12:45:13 GetRedisAddr: 127.0.0.1:6379
2025-07-25 12:45:13 | INFO | 2025/07/25 12:45:13 Redis连接成功 | 地址:127.0.0.1:6379 DB:0
2025-07-25 12:45:13 | INFO | 2025/07/25 12:45:13 Server start at :9000
2025-07-25 12:45:14 | SUCCESS | WechatAPI服务已启动
2025-07-25 12:45:14 | INFO | 登录账号信息: wxid: wxid_4usgcju5ey9q29  昵称: 瑶瑶  微信号: Today-KFC  手机号: None
2025-07-25 12:45:14 | INFO | 登录设备信息: device_name: <PERSON>'s Pad  device_id: 49d050a7e94d1ecc40790d74b1c4369f
2025-07-25 12:45:14 | SUCCESS | 登录成功
2025-07-25 12:45:14 | SUCCESS | 已开启自动心跳
2025-07-25 12:45:14 | INFO | 成功加载表情映射文件，共 539 条记录
2025-07-25 12:45:14 | SUCCESS | 数据库初始化成功
2025-07-25 12:45:14 | SUCCESS | 定时任务已启动
2025-07-25 12:45:14 | SUCCESS | 插件 AISignIn 已加载定时任务: {'plugins.AISignIn.main.AISignIn.auto_signin_task'}
2025-07-25 12:45:14 | INFO | 已加载管理员列表: ['wxid_ubbh6q832tcs21', 'admin-wxid']
2025-07-25 12:45:15 | INFO | 播客API初始化成功
2025-07-25 12:45:15 | INFO | 加载配置完成，转换后的数据: {'gh_7057516c9e71': {'name': '唱舞全明星', 'groups': ['27852221909@chatroom', '55878994168@chatroom', '51891329927@chatroom']}, 'gh_d4d21a0e4623': {'name': '两颗桃', 'groups': ['48097389945@chatroom']}, 'gh_410ff2fa6ec9': {'name': 'NGC660 Ai研究院', 'groups': ['47442567074@chatroom']}, 'gh_f78c53c507c0': {'name': '不正常吐槽君', 'groups': ['48097389945@chatroom']}, 'gh_ba3381c847b3': {'name': '唱舞星计划', 'groups': ['51891329927@chatroom']}, 'gh_1a102fb4cad9': {'name': '我在娱乐圈的唯一人脉', 'groups': ['48097389945@chatroom', '27852221909@chatroom']}}
2025-07-25 12:45:15 | INFO | ArticleForwarder插件初始化完成 - 监控配置: {'gh_7057516c9e71': {'name': '唱舞全明星', 'groups': ['27852221909@chatroom', '55878994168@chatroom', '51891329927@chatroom']}, 'gh_d4d21a0e4623': {'name': '两颗桃', 'groups': ['48097389945@chatroom']}, 'gh_410ff2fa6ec9': {'name': 'NGC660 Ai研究院', 'groups': ['47442567074@chatroom']}, 'gh_f78c53c507c0': {'name': '不正常吐槽君', 'groups': ['48097389945@chatroom']}, 'gh_ba3381c847b3': {'name': '唱舞星计划', 'groups': ['51891329927@chatroom']}, 'gh_1a102fb4cad9': {'name': '我在娱乐圈的唯一人脉', 'groups': ['48097389945@chatroom', '27852221909@chatroom']}}
2025-07-25 12:45:15 | DEBUG | [TempFileManager] 添加清理规则: default
2025-07-25 12:45:15 | DEBUG | [TempFileManager] 添加清理规则: images
2025-07-25 12:45:15 | DEBUG | [TempFileManager] 添加清理规则: videos
2025-07-25 12:45:15 | DEBUG | [TempFileManager] 添加清理规则: audio
2025-07-25 12:45:15 | DEBUG | [TempFileManager] 添加清理规则: temp
2025-07-25 12:45:15 | DEBUG | [TempFileManager] 添加清理规则: large_files
2025-07-25 12:45:15 | INFO | [TempFileManager] 临时文件清理任务已启动
2025-07-25 12:45:15 | INFO | [ChatSummary] 数据库初始化成功
2025-07-25 12:45:15 | SUCCESS | 插件 GoodMorning 已加载定时任务: {'plugins.GoodMorning.main.GoodMorning.daily_task'}
2025-07-25 12:45:16 | SUCCESS | 插件 News 已加载定时任务: {'plugins.News.main.News.noon_news', 'plugins.News.main.News.night_news'}
2025-07-25 12:45:16 | INFO | [PatReply] AMR文件已存在且是最新的: C:\XYBotV2\data\paiyipai\1_BFB5258B-2B13-4CF4-A40C-5BCCE74E5414_converted.amr
2025-07-25 12:45:16 | DEBUG | [PatReply] 从缓存加载base64: 18228 字符
2025-07-25 12:45:16 | SUCCESS | 插件 QuarkSignIn 已加载定时任务: {'plugins.QuarkSignIn.main.QuarkSignIn.auto_signin_task'}
2025-07-25 12:45:16 | INFO | [QuarkSignIn] 已设置自动签到时间: 08:00
2025-07-25 12:45:16 | INFO | 插件状态: 启用, 检查间隔: 3600秒, 最大重试次数: 3, 重试延迟: 5秒
2025-07-25 12:45:16 | INFO | 已加载管理员列表: ['wxid_ubbh6q832tcs21', 'admin-wxid']
2025-07-25 12:45:16 | INFO | 已加载 2 个改名监控群和 1 个退群监控群
2025-07-25 12:45:16 | INFO | [RenameReminder] 开始启用插件...
2025-07-25 12:45:16 | SUCCESS | 插件 RenameReminder 已加载定时任务: {'plugins.RenameReminder.main.RenameReminder.check_name_changes'}
2025-07-25 12:45:16 | INFO | [RenameReminder] 插件已启用，开始初始化...
2025-07-25 12:45:16 | INFO | [RenameReminder] 支持的命令: 开启改名提醒, 关闭改名提醒, 开启退群提醒, 关闭退群提醒, 查看提醒状态
2025-07-25 12:45:16 | INFO | 已设置检查间隔为 3600 秒
2025-07-25 12:45:16 | INFO | 已加载 2 个监控群，开始更新成员列表
2025-07-25 12:45:16 | DEBUG | 已更新群 51891329927@chatroom 的成员列表
2025-07-25 12:45:16 | DEBUG | 已更新群 27852221909@chatroom 的成员列表
2025-07-25 12:45:16 | SUCCESS | 插件 TimerTask 已加载定时任务: {'plugins.TimerTask.main.TimerTask.check_tasks'}
2025-07-25 12:45:17 | INFO | [VideoDemand] 加载了 128 个视频类别配置
2025-07-25 12:45:17 | INFO | [YaoyaoPlugin] 私聊黑名单用户: ['gh_a5cf07d474f1', 'gh_ba3381c847b3']
2025-07-25 12:45:17 | INFO | [YaoyaoPlugin] 已加载管理员列表: ['wxid_ubbh6q832tcs21', 'admin-wxid']
2025-07-25 12:45:17 | INFO | [yuanbao] 插件初始化完成
2025-07-25 12:45:17 | DEBUG | [yuanbao] 指令: ['元宝', 'yuanbao']
2025-07-25 12:45:17 | DEBUG | [yuanbao] 绘图模型: gpt_175B_0404
2025-07-25 12:45:17 | DEBUG | [yuanbao] 认证信息加载状态: hy_token=True, hy_user=True, agent_id=True, x_uskey=True
2025-07-25 12:45:17 | SUCCESS | 已加载插件: ['AdminPoint', 'AdminSignInReset', 'AdminWhitelist', 'AISignIn', 'AppMessageTester', 'ArticleForwarder', 'BaiduAgentsPlugin', 'BaiduDraw', 'BotStatus', 'ChatSummary', 'DanceSignInPlugin', 'DeepseekPlugin', 'Doubao', 'DoubaoDrawing', 'DoubaoImageRecognition', 'DouBaoImageToImage', 'DouBaoImageToVideo', 'DoubaoVideoSearch', 'DouHui', 'EmojiTestPlugin', 'FixedQuoteTest', 'GetContact', 'Gomoku', 'GoodMorning', 'HunyuanDraw', 'ImageEcho', 'ImageToImage', 'JiemengDraw', 'KeLingImageToImage', 'KlingAI', 'Leaderboard', 'LuckyDraw', 'ManagePlugin', 'MeituAI', 'Menu', 'MiniProgramTester', 'Music', 'News', 'PatReply', 'PointTrade', 'QuarkSignIn', 'QueryPoint', 'RandomMember', 'RandomPicture', 'RenameReminder', 'RevokePlugin', 'RoboNeo', 'SignIn', 'TempFileManagerPlugin', 'TencentLke', 'TimerTask', 'TongyiDraw', 'URLShortener', 'VideoDemand', 'VideoParserPlugin', 'VideoTest', 'VivoAgentsPlugin', 'VoiceTest', 'Warthunder', 'WeatherQuery', 'WelcomePlugin', 'Xunfei', 'YaoyaoPlugin', 'Yuanbao', '抽签', '造梦次元']
2025-07-25 12:45:17 | INFO | 处理堆积消息中
2025-07-25 12:45:17 | DEBUG | 接受到 1 条消息
2025-07-25 12:45:18 | DEBUG | 接受到 1 条消息
2025-07-25 12:45:20 | SUCCESS | 处理堆积消息完毕
2025-07-25 12:45:20 | SUCCESS | 开始处理消息
2025-07-25 12:45:49 | DEBUG | 收到消息: {'MsgId': 822820373, 'FromUserName': {'string': '47325400669@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 1, 'Content': {'string': 'scottuk:\n是的 八戒好色谁都知道了'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1753418771, 'MsgSource': '<msgsource>\n\t<bizflag>0</bizflag>\n\t<pua>1</pua>\n\t<eggIncluded>1</eggIncluded>\n\t<silence>0</silence>\n\t<membercount>221</membercount>\n\t<signature>N0_V1_u9AaCvRy|v1_RuCemNPB</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'PushContent': 's : 是的 八戒好色谁都知道了', 'NewMsgId': 6723906434478789244, 'MsgSeq': 871398740}
2025-07-25 12:45:49 | INFO | 收到文本消息: 消息ID:822820373 来自:47325400669@chatroom 发送人:scottuk @:[] 内容:是的 八戒好色谁都知道了
2025-07-25 12:45:49 | INFO | 成功加载表情映射文件，共 539 条记录
2025-07-25 12:45:49 | DEBUG | 处理消息内容: '是的 八戒好色谁都知道了'
2025-07-25 12:45:49 | DEBUG | 消息内容 '是的 八戒好色谁都知道了' 不匹配任何命令，忽略
2025-07-25 12:46:08 | DEBUG | 收到消息: {'MsgId': 597549660, 'FromUserName': {'string': '55878994168@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 1, 'Content': {'string': 'wxid_ubbh6q832tcs21:\n我的表情'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1753418790, 'MsgSource': '<msgsource>\n\t<pua>1</pua>\n\t<eggIncluded>1</eggIncluded>\n\t<silence>0</silence>\n\t<membercount>3</membercount>\n\t<signature>N0_V1_CGFhfCg4|v1_JA6tCu1T</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'PushContent': '郭 : 我的表情', 'NewMsgId': 535324903884074204, 'MsgSeq': 871398741}
2025-07-25 12:46:08 | INFO | 收到文本消息: 消息ID:597549660 来自:55878994168@chatroom 发送人:wxid_ubbh6q832tcs21 @:[] 内容:我的表情
2025-07-25 12:46:08 | ERROR | 发送表情列表聊天记录失败: RevokePlugin._wrap_bot_methods.<locals>.wrapped_send_app() missing 1 required positional argument: 'type'
2025-07-25 12:46:08 | ERROR | 生成表情列表聊天记录失败: RevokePlugin._wrap_bot_methods.<locals>.wrapped_send_app() missing 1 required positional argument: 'type'
2025-07-25 12:46:08 | INFO | 发送文字消息: 对方wxid:55878994168@chatroom at: 内容:我的表情列表
==============================
• CPDD
• ddcp
• 一肚子坏水
• 下班了
• 不听
• 不玩了
• 串门
• 乏了
• 亮不亮
• 亲
• 你疯了
• 偷看
• 冒泡
• 副本滴滴
• 又睡醒了
• 变猪
• 吃瓜
• 呆
• 哼
• 喷火
• 嗦
• 嘚瑟
• 守法
• 小爱扭
• 小郭挨打
• 就偷
• 帅变狗
• 弹琴
• 快看
• 扇你
• 打
• 打你
• 打可乐
• 打小爱
• 打小郭
• 打小郭屁屁
• 打阿姨
• 打阿寻
• 扭
• 找死
• 控制你
• 揍小爱
• 揍小郭
• 提肛
• 摆烂
• 摇
• 摇摆
• 摇起来
• 放肆
• 斜看
• 来财
• 欢迎新人
• 欺负我
• 毙了你
• 洗脑
• 清醒一点
• 炸你
• 电你
• 电你
• 疯了
• 看戏
• 看戏
• 看看腿
• 看笨蛋
• 睡觉
• 秀腹肌
• 笑死
• 继续
• 继续毙
• 群主
• 群最帅
• 舔我
• 舞
• 花落团长
• 蜘蛛
• 谢谢老板
• 走
• 跑
• 跟着舞
• 跳楼机
• 跳水
• 跳水
• 踹阿亮
2025-07-25 12:46:08 | DEBUG | 处理消息内容: '我的表情'
2025-07-25 12:46:08 | DEBUG | 消息内容 '我的表情' 不匹配任何命令，忽略
2025-07-25 12:46:09 | DEBUG | 收到消息: {'MsgId': 1148786102, 'FromUserName': {'string': '47325400669@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 1, 'Content': {'string': 'wxid_npsnrcqwwy6v22:\n八戒是不是真跟女生睡过觉'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1753418791, 'MsgSource': '<msgsource>\n\t<sec_msg_node>\n\t\t<alnode>\n\t\t\t<fr>1</fr>\n\t\t</alnode>\n\t</sec_msg_node>\n\t<pua>1</pua>\n\t<silence>0</silence>\n\t<membercount>221</membercount>\n\t<signature>N0_V1_GpOaKWh+|v1_FBqDLHp0</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'PushContent': '程序员🦌小鹿 : 八戒是不是真跟女生睡过觉', 'NewMsgId': 2971057337559723903, 'MsgSeq': 871398743}
2025-07-25 12:46:09 | INFO | 收到文本消息: 消息ID:1148786102 来自:47325400669@chatroom 发送人:wxid_npsnrcqwwy6v22 @:[] 内容:八戒是不是真跟女生睡过觉
2025-07-25 12:46:09 | DEBUG | 处理消息内容: '八戒是不是真跟女生睡过觉'
2025-07-25 12:46:09 | DEBUG | 消息内容 '八戒是不是真跟女生睡过觉' 不匹配任何命令，忽略
2025-07-25 12:46:11 | DEBUG | 收到消息: {'MsgId': 46665991, 'FromUserName': {'string': '47325400669@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 1, 'Content': {'string': 'heaventt:\n是的，哪里还有隐私'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1753418793, 'MsgSource': '<msgsource>\n\t<pua>1</pua>\n\t<eggIncluded>1</eggIncluded>\n\t<silence>0</silence>\n\t<membercount>221</membercount>\n\t<signature>N0_V1_L2cP3kKt|v1_jqGzedcP</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'PushContent': 'heaven : 是的，哪里还有隐私', 'NewMsgId': 1881273369909217157, 'MsgSeq': 871398745}
2025-07-25 12:46:11 | INFO | 收到文本消息: 消息ID:46665991 来自:47325400669@chatroom 发送人:heaventt @:[] 内容:是的，哪里还有隐私
2025-07-25 12:46:11 | DEBUG | 处理消息内容: '是的，哪里还有隐私'
2025-07-25 12:46:11 | DEBUG | 消息内容 '是的，哪里还有隐私' 不匹配任何命令，忽略
2025-07-25 12:46:12 | DEBUG | 收到消息: {'MsgId': 1144379654, 'FromUserName': {'string': '47325400669@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 1, 'Content': {'string': 'xiehuaping668:\n[旺柴][旺柴]'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1753418793, 'MsgSource': '<msgsource>\n\t<bizflag>0</bizflag>\n\t<pua>1</pua>\n\t<eggIncluded>1</eggIncluded>\n\t<silence>0</silence>\n\t<membercount>221</membercount>\n\t<signature>N0_V1_RPojtDjf|v1_r2ErQnln</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'PushContent': '你已被群主移出群聊 ر ر : [旺柴][旺柴]', 'NewMsgId': 1647850713259272410, 'MsgSeq': 871398746}
2025-07-25 12:46:12 | INFO | 收到表情消息: 消息ID:1144379654 来自:47325400669@chatroom 发送人:xiehuaping668 @:[] 内容:[旺柴][旺柴]
2025-07-25 12:46:12 | DEBUG | [DoubaoImageRecognition] 缓存表情消息: 1647850713259272410
2025-07-25 12:46:35 | DEBUG | 收到消息: {'MsgId': 1613028711, 'FromUserName': {'string': '47325400669@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 1, 'Content': {'string': 'xiehuaping668:\n自己部署一个qw3啊'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1753418818, 'MsgSource': '<msgsource>\n\t<bizflag>0</bizflag>\n\t<pua>1</pua>\n\t<eggIncluded>1</eggIncluded>\n\t<silence>0</silence>\n\t<membercount>221</membercount>\n\t<signature>N0_V1_7cOrl/rz|v1_g/o+K5lO</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'PushContent': '你已被群主移出群聊 ر ر : 自己部署一个qw3啊', 'NewMsgId': 4701240591967706933, 'MsgSeq': 871398747}
2025-07-25 12:46:35 | INFO | 收到文本消息: 消息ID:1613028711 来自:47325400669@chatroom 发送人:xiehuaping668 @:[] 内容:自己部署一个qw3啊
2025-07-25 12:46:35 | DEBUG | 处理消息内容: '自己部署一个qw3啊'
2025-07-25 12:46:35 | DEBUG | 消息内容 '自己部署一个qw3啊' 不匹配任何命令，忽略
2025-07-25 12:46:48 | DEBUG | 收到消息: {'MsgId': 1881141260, 'FromUserName': {'string': '47325400669@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 1, 'Content': {'string': 'heaventt:\n没硬件'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1753418830, 'MsgSource': '<msgsource>\n\t<pua>1</pua>\n\t<eggIncluded>1</eggIncluded>\n\t<silence>0</silence>\n\t<membercount>221</membercount>\n\t<signature>N0_V1_xqFjo2bC|v1_285z/CpK</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'PushContent': 'heaven : 没硬件', 'NewMsgId': 5553519502915761841, 'MsgSeq': 871398748}
2025-07-25 12:46:48 | INFO | 收到文本消息: 消息ID:1881141260 来自:47325400669@chatroom 发送人:heaventt @:[] 内容:没硬件
2025-07-25 12:46:48 | DEBUG | 处理消息内容: '没硬件'
2025-07-25 12:46:48 | DEBUG | 消息内容 '没硬件' 不匹配任何命令，忽略
2025-07-25 12:47:11 | DEBUG | 收到消息: {'MsgId': 941372140, 'FromUserName': {'string': '48097389945@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 1, 'Content': {'string': 'wxid_cwxauba42ki122:\n这个群我只有小爱，还有那个嫂嫂好友，嫂嫂跑了，只剩小爱了[捂脸]'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1753418854, 'MsgSource': '<msgsource>\n\t<pua>1</pua>\n\t<silence>0</silence>\n\t<membercount>60</membercount>\n\t<signature>N0_V1_IGlSsPfl|v1_d63APGcT</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'PushContent': 'ن初心ᯤ⁶ᴳ คิดถึงن : 这个群我只有小爱，还有那个嫂嫂好友，嫂嫂跑了，只剩小爱了[Facep...', 'NewMsgId': 5227775486278528733, 'MsgSeq': 871398749}
2025-07-25 12:47:11 | INFO | 收到文本消息: 消息ID:941372140 来自:48097389945@chatroom 发送人:wxid_cwxauba42ki122 @:[] 内容:这个群我只有小爱，还有那个嫂嫂好友，嫂嫂跑了，只剩小爱了[捂脸]
2025-07-25 12:47:12 | DEBUG | 处理消息内容: '这个群我只有小爱，还有那个嫂嫂好友，嫂嫂跑了，只剩小爱了[捂脸]'
2025-07-25 12:47:12 | DEBUG | 消息内容 '这个群我只有小爱，还有那个嫂嫂好友，嫂嫂跑了，只剩小爱了[捂脸]' 不匹配任何命令，忽略
2025-07-25 12:47:31 | DEBUG | 收到消息: {'MsgId': 259640906, 'FromUserName': {'string': '48097389945@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 1, 'Content': {'string': 'xiaomaochong:\n嫂嫂在呢'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1753418874, 'MsgSource': '<msgsource>\n\t<pua>1</pua>\n\t<silence>0</silence>\n\t<membercount>60</membercount>\n\t<signature>N0_V1_jGNcwBUx|v1_O7Za9iGj</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'PushContent': '小爱 : 嫂嫂在呢', 'NewMsgId': 265050997257942747, 'MsgSeq': 871398750}
2025-07-25 12:47:31 | INFO | 收到文本消息: 消息ID:259640906 来自:48097389945@chatroom 发送人:xiaomaochong @:[] 内容:嫂嫂在呢
2025-07-25 12:47:32 | DEBUG | 处理消息内容: '嫂嫂在呢'
2025-07-25 12:47:32 | DEBUG | 消息内容 '嫂嫂在呢' 不匹配任何命令，忽略
2025-07-25 12:47:33 | DEBUG | 收到消息: {'MsgId': 109241177, 'FromUserName': {'string': '47325400669@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 1, 'Content': {'string': 'heaventt:\n本地有部署一个心理健康大模型，这种比较隐私'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1753418875, 'MsgSource': '<msgsource>\n\t<pua>1</pua>\n\t<eggIncluded>1</eggIncluded>\n\t<silence>0</silence>\n\t<membercount>221</membercount>\n\t<signature>N0_V1_7QaA10uS|v1_VoW00BSR</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'PushContent': 'heaven : 本地有部署一个心理健康大模型，这种比较隐私', 'NewMsgId': 7863537042931807310, 'MsgSeq': 871398751}
2025-07-25 12:47:33 | INFO | 收到文本消息: 消息ID:109241177 来自:47325400669@chatroom 发送人:heaventt @:[] 内容:本地有部署一个心理健康大模型，这种比较隐私
2025-07-25 12:47:33 | DEBUG | 处理消息内容: '本地有部署一个心理健康大模型，这种比较隐私'
2025-07-25 12:47:33 | DEBUG | 消息内容 '本地有部署一个心理健康大模型，这种比较隐私' 不匹配任何命令，忽略
2025-07-25 12:47:47 | DEBUG | 收到消息: {'MsgId': 626522092, 'FromUserName': {'string': '47325400669@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 1, 'Content': {'string': 'wxid_62fiham2pn7521:\n要什么隐私，你不泄露大把地方把你信息卖出去'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1753418889, 'MsgSource': '<msgsource>\n\t<pua>1</pua>\n\t<eggIncluded>1</eggIncluded>\n\t<silence>0</silence>\n\t<membercount>221</membercount>\n\t<signature>N0_V1_mg9Pnt/j|v1_cuxliA6o</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'PushContent': '八戒。 : 要什么隐私，你不泄露大把地方把你信息卖出去', 'NewMsgId': 3762151072745288401, 'MsgSeq': 871398752}
2025-07-25 12:47:47 | INFO | 收到文本消息: 消息ID:626522092 来自:47325400669@chatroom 发送人:wxid_62fiham2pn7521 @:[] 内容:要什么隐私，你不泄露大把地方把你信息卖出去
2025-07-25 12:47:47 | DEBUG | 处理消息内容: '要什么隐私，你不泄露大把地方把你信息卖出去'
2025-07-25 12:47:47 | DEBUG | 消息内容 '要什么隐私，你不泄露大把地方把你信息卖出去' 不匹配任何命令，忽略
2025-07-25 12:47:49 | DEBUG | 收到消息: {'MsgId': 390978027, 'FromUserName': {'string': '47325400669@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 47, 'Content': {'string': 'wxid_62fiham2pn7521:\n<msg><emoji fromusername = "wxid_62fiham2pn7521" tousername = "47325400669@chatroom" type="2" idbuffer="media:0_0" md5="2b42c8682f7eeae8d1e43a7e07f083cb" len = "2364817" productid="" androidmd5="2b42c8682f7eeae8d1e43a7e07f083cb" androidlen="2364817" s60v3md5 = "2b42c8682f7eeae8d1e43a7e07f083cb" s60v3len="2364817" s60v5md5 = "2b42c8682f7eeae8d1e43a7e07f083cb" s60v5len="2364817" cdnurl = "http://vweixinf.tc.qq.com/110/20401/stodownload?m=2b42c8682f7eeae8d1e43a7e07f083cb&amp;filekey=30440201010430302e02016e04025348042032623432633836383266376565616538643165343361376530376630383363620203241591040d00000004627466730000000132&amp;hy=SH&amp;storeid=26821caf20003d6b84206bcae0000006e01004fb153482e92f1f1573ffdf27&amp;ef=1&amp;bizid=1022" designerid = "" thumburl = "" encrypturl = "http://vweixinf.tc.qq.com/110/20402/stodownload?m=8fb5a37135409a27df2dd330b07b1130&amp;filekey=30440201010430302e02016e040253480420386662356133373133353430396132376466326464333330623037623131333002032415a0040d00000004627466730000000132&amp;hy=SH&amp;storeid=26821caf20007011a4206bcae0000006e02004fb253482e92f1f1573ffdf4a&amp;ef=2&amp;bizid=1022" aeskey= "df28678d30a343e0aecdd77ff35df1dc" externurl = "http://vweixinf.tc.qq.com/110/20403/stodownload?m=58ae99bec620c8dab3fccb78e7a8578a&amp;filekey=30440201010430302e02016e04025348042035386165393962656336323063386461623366636362373865376138353738610203018c70040d00000004627466730000000132&amp;hy=SH&amp;storeid=26821caf2000a09544206bcae0000006e03004fb353482e92f1f1573ffdf65&amp;ef=3&amp;bizid=1022" externmd5 = "982bedfb64ba749f93005f599bfa0e39" width= "240" height= "240" tpurl= "" tpauthkey= "" attachedtext= "" attachedtextcolor= "" lensid= "" emojiattr= "" linkid= "" desc= "" ></emoji>  </msg>'}, 'Status': 3, 'ImgStatus': 2, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1753418891, 'MsgSource': '<msgsource>\n\t<silence>0</silence>\n\t<membercount>221</membercount>\n\t<signature>N0_V1_dW00uwkq|v1_tyjq0yJ4</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'PushContent': '八戒。在群聊中发了一个表情', 'NewMsgId': 2016936733293192317, 'MsgSeq': 871398753}
2025-07-25 12:47:49 | INFO | 收到表情消息: 消息ID:390978027 来自:47325400669@chatroom 发送人:wxid_62fiham2pn7521 MD5:2b42c8682f7eeae8d1e43a7e07f083cb 大小:2364817
2025-07-25 12:47:49 | DEBUG | [DoubaoImageRecognition] 缓存表情消息: 2016936733293192317
2025-07-25 12:47:49 | DEBUG | 收到消息: {'MsgId': 1615974559, 'FromUserName': {'string': '48097389945@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 47, 'Content': {'string': 'wxid_l9koi6kli78i22:\n<msg><emoji fromusername="wxid_l9koi6kli78i22" tousername="48097389945@chatroom" type="1" idbuffer="media:0_0" md5="ee7fd2a53d6ffd4bcfe283faff055745" len="25875" productid="" androidmd5="ee7fd2a53d6ffd4bcfe283faff055745" androidlen="25875" s60v3md5="ee7fd2a53d6ffd4bcfe283faff055745" s60v3len="25875" s60v5md5="ee7fd2a53d6ffd4bcfe283faff055745" s60v5len="25875" cdnurl="http://wxapp.tc.qq.com/262/20304/stodownload?m=ee7fd2a53d6ffd4bcfe283faff055745&amp;filekey=30340201010420301e02020106040253480410ee7fd2a53d6ffd4bcfe283faff05574502026513040d00000004627466730000000132&amp;hy=SH&amp;storeid=2632299fd000d1138000000000000010600004f50534800dc596097b5cc9a3&amp;bizid=1023" designerid="" thumburl="" encrypturl="http://wxapp.tc.qq.com/262/20304/stodownload?m=850b838482951e1b82139ddd20a8285c&amp;filekey=30340201010420301e02020106040253480410850b838482951e1b82139ddd20a8285c02026520040d00000004627466730000000132&amp;hy=SH&amp;storeid=2632299fe00004c4b000000000000010600004f50534829bc596097b57a60e&amp;bizid=1023" aeskey="d1662d85ca7c1f72bf5d7ec8f3360117" externurl="http://wxapp.tc.qq.com/262/20304/stodownload?m=40c52929cec1d7dfe865ab3fff1cc57f&amp;filekey=30340201010420301e0202010604025348041040c52929cec1d7dfe865ab3fff1cc57f02023800040d00000004627466730000000132&amp;hy=SH&amp;storeid=2632299fe0002b6a3000000000000010600004f505348079d2a00b7bfa5d7a&amp;bizid=1023" externmd5="9f19cf5fde7251af60f7c2d9ec779b4d" width="240" height="240" tpurl="" tpauthkey="" attachedtext="" attachedtextcolor="" lensid="" emojiattr="" linkid="" desc=""></emoji></msg>'}, 'Status': 3, 'ImgStatus': 2, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1753418891, 'MsgSource': '<msgsource>\n\t<silence>0</silence>\n\t<membercount>60</membercount>\n\t<signature>N0_V1_21McPufG|v1_RG9rUt2r</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'PushContent': '十五在群聊中发了一个表情', 'NewMsgId': 2507407880824769633, 'MsgSeq': 871398754}
2025-07-25 12:47:49 | INFO | 收到表情消息: 消息ID:1615974559 来自:48097389945@chatroom 发送人:wxid_l9koi6kli78i22 MD5:ee7fd2a53d6ffd4bcfe283faff055745 大小:25875
2025-07-25 12:47:49 | DEBUG | [DoubaoImageRecognition] 缓存表情消息: 2507407880824769633
2025-07-25 12:48:05 | DEBUG | 收到消息: {'MsgId': 1613069066, 'FromUserName': {'string': '48097389945@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 1, 'Content': {'string': 'wxid_l9koi6kli78i22:\n我只有37.2'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1753418908, 'MsgSource': '<msgsource>\n\t<pua>1</pua>\n\t<silence>0</silence>\n\t<membercount>60</membercount>\n\t<signature>N0_V1_LXOZ4RDp|v1_9m5yvlld</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'PushContent': '十五 : 我只有37.2', 'NewMsgId': 4504596842414098364, 'MsgSeq': 871398755}
2025-07-25 12:48:05 | INFO | 收到文本消息: 消息ID:1613069066 来自:48097389945@chatroom 发送人:wxid_l9koi6kli78i22 @:[] 内容:我只有37.2
2025-07-25 12:48:06 | DEBUG | 处理消息内容: '我只有37.2'
2025-07-25 12:48:06 | DEBUG | 消息内容 '我只有37.2' 不匹配任何命令，忽略
2025-07-25 12:48:11 | DEBUG | 收到消息: {'MsgId': 716675700, 'FromUserName': {'string': '48097389945@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 1, 'Content': {'string': 'wxid_ubbh6q832tcs21:\n@小爱\u2005踢'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1753418914, 'MsgSource': '<msgsource>\n\t<atuserlist><![CDATA[xiaomaochong]]></atuserlist>\n\t<pua>1</pua>\n\t<eggIncluded>1</eggIncluded>\n\t<silence>0</silence>\n\t<membercount>60</membercount>\n\t<signature>N0_V1_zTx4e/Yd|v1_8zfdLJ1l</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'PushContent': '郭 : @小爱\u2005踢', 'NewMsgId': 3681975590646387112, 'MsgSeq': 871398756}
2025-07-25 12:48:11 | INFO | 收到文本消息: 消息ID:716675700 来自:48097389945@chatroom 发送人:wxid_ubbh6q832tcs21 @:['xiaomaochong'] 内容:@小爱 踢
2025-07-25 12:48:12 | DEBUG | 处理消息内容: '@小爱 踢'
2025-07-25 12:48:12 | DEBUG | 消息内容 '@小爱 踢' 不匹配任何命令，忽略
2025-07-25 12:48:12 | DEBUG | 收到消息: {'MsgId': 331277113, 'FromUserName': {'string': '48097389945@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 1, 'Content': {'string': 'wxid_l9koi6kli78i22:\n他说这个群很精彩'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1753418915, 'MsgSource': '<msgsource>\n\t<pua>1</pua>\n\t<silence>0</silence>\n\t<membercount>60</membercount>\n\t<signature>N0_V1_Uu7p/NMQ|v1_JoTTI8s8</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'PushContent': '十五 : 他说这个群很精彩', 'NewMsgId': 4883671823291891029, 'MsgSeq': 871398757}
2025-07-25 12:48:12 | INFO | 收到文本消息: 消息ID:331277113 来自:48097389945@chatroom 发送人:wxid_l9koi6kli78i22 @:[] 内容:他说这个群很精彩
2025-07-25 12:48:12 | DEBUG | 处理消息内容: '他说这个群很精彩'
2025-07-25 12:48:12 | DEBUG | 消息内容 '他说这个群很精彩' 不匹配任何命令，忽略
2025-07-25 12:48:16 | DEBUG | 收到消息: {'MsgId': 1361779550, 'FromUserName': {'string': '48097389945@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 47, 'Content': {'string': 'wxid_ubbh6q832tcs21:\n<msg><emoji fromusername = "wxid_ubbh6q832tcs21" tousername = "48097389945@chatroom" type="2" idbuffer="media:0_0" md5="0c9fca899455e9210927c2f87e0d283e" len = "20874" productid="" androidmd5="0c9fca899455e9210927c2f87e0d283e" androidlen="20874" s60v3md5 = "0c9fca899455e9210927c2f87e0d283e" s60v3len="20874" s60v5md5 = "0c9fca899455e9210927c2f87e0d283e" s60v5len="20874" cdnurl = "http://wxapp.tc.qq.com/262/20304/stodownload?m=0c9fca899455e9210927c2f87e0d283e&amp;filekey=30340201010420301e020201060402534804100c9fca899455e9210927c2f87e0d283e0202518a040d00000004627466730000000131&amp;hy=SH&amp;storeid=32303232303831393035303533393030306266376530303030303030303030376464613030623030303030313036&amp;bizid=1023" designerid = "" thumburl = "" encrypturl = "http://wxapp.tc.qq.com/262/20304/stodownload?m=35d132eb43f9b3e0df1101aa8dddf83f&amp;filekey=30340201010420301e0202010604025348041035d132eb43f9b3e0df1101aa8dddf83f02025190040d00000004627466730000000132&amp;hy=SH&amp;storeid=262fea9a3000f177a000000000000010600004f50534813cd2a00b6564889d&amp;bizid=1023" aeskey= "120013e310bdf992e37a677d37c75c29" externurl = "http://wxapp.tc.qq.com/262/20304/stodownload?m=42e676067232ee653404e4faf06ebbd7&amp;filekey=30340201010420301e0202010604025348041042e676067232ee653404e4faf06ebbd702022f30040d00000004627466730000000131&amp;hy=SH&amp;storeid=32303232303831393035303534303030303133653835303030303030303037386433393630393030303030313036&amp;bizid=1023" externmd5 = "09539f8a9922650219dd38bb6af7e227" width= "350" height= "349" tpurl= "" tpauthkey= "" attachedtext= "" attachedtextcolor= "" lensid= "" emojiattr= "" linkid= "" desc= "" ></emoji>  </msg>'}, 'Status': 3, 'ImgStatus': 2, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1753418919, 'MsgSource': '<msgsource>\n\t<silence>0</silence>\n\t<membercount>60</membercount>\n\t<signature>N0_V1_PPawfN66|v1_RyStfqhV</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'PushContent': '郭在群聊中发了一个表情', 'NewMsgId': 96387716406456380, 'MsgSeq': 871398758}
2025-07-25 12:48:16 | INFO | 收到表情消息: 消息ID:1361779550 来自:48097389945@chatroom 发送人:wxid_ubbh6q832tcs21 MD5:0c9fca899455e9210927c2f87e0d283e 大小:20874
2025-07-25 12:48:17 | DEBUG | [DoubaoImageRecognition] 缓存表情消息: 96387716406456380
2025-07-25 12:48:23 | DEBUG | 收到消息: {'MsgId': 2087007358, 'FromUserName': {'string': '48097389945@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 1, 'Content': {'string': 'wxid_84mmq4cu7ita22:\n我删了好多人'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1753418925, 'MsgSource': '<msgsource>\n\t<sec_msg_node>\n\t\t<alnode>\n\t\t\t<fr>1</fr>\n\t\t</alnode>\n\t</sec_msg_node>\n\t<pua>1</pua>\n\t<silence>0</silence>\n\t<membercount>60</membercount>\n\t<signature>N0_V1_XWsFqawp|v1_oZI871Tq</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'PushContent': '喵小叶 : 我删了好多人', 'NewMsgId': 1854300680133743236, 'MsgSeq': 871398759}
2025-07-25 12:48:23 | INFO | 收到文本消息: 消息ID:2087007358 来自:48097389945@chatroom 发送人:wxid_84mmq4cu7ita22 @:[] 内容:我删了好多人
2025-07-25 12:48:23 | DEBUG | 处理消息内容: '我删了好多人'
2025-07-25 12:48:23 | DEBUG | 消息内容 '我删了好多人' 不匹配任何命令，忽略
2025-07-25 12:48:25 | DEBUG | 收到消息: {'MsgId': 436691974, 'FromUserName': {'string': '48097389945@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 10002, 'Content': {'string': 'wxid_ubbh6q832tcs21:\n<sysmsg type="revokemsg"><revokemsg><session>48097389945@chatroom</session><msgid>719531818</msgid><newmsgid>3681975590646387112</newmsgid><replacemsg><![CDATA["郭" 撤回了一条消息]]></replacemsg><announcement_id><![CDATA[]]></announcement_id></revokemsg></sysmsg>'}, 'Status': 4, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1753418924, 'MsgSource': '<msgsource>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'NewMsgId': 463421191434788971, 'MsgSeq': 871398760}
2025-07-25 12:48:25 | DEBUG | 系统消息类型: revokemsg
2025-07-25 12:48:25 | INFO | 未知的系统消息类型: {'MsgId': 436691974, 'ToWxid': 'wxid_4usgcju5ey9q29', 'MsgType': 10002, 'Content': '\n<sysmsg type="revokemsg"><revokemsg><session>48097389945@chatroom</session><msgid>719531818</msgid><newmsgid>3681975590646387112</newmsgid><replacemsg><![CDATA["郭" 撤回了一条消息]]></replacemsg><announcement_id><![CDATA[]]></announcement_id></revokemsg></sysmsg>', 'Status': 4, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1753418924, 'MsgSource': '<msgsource>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'NewMsgId': 463421191434788971, 'MsgSeq': 871398760, 'FromWxid': '48097389945@chatroom', 'IsGroup': True, 'SenderWxid': 'wxid_ubbh6q832tcs21'}
2025-07-25 12:48:31 | DEBUG | 收到消息: {'MsgId': 1767266183, 'FromUserName': {'string': '48097389945@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 34, 'Content': {'string': 'xiaomaochong:\n<msg><voicemsg endflag="1" cancelflag="0" forwardflag="0" voiceformat="4" voicelength="23000" length="66642" bufid="0" aeskey="70656a65797178746e62776b63796270" voiceurl="3052020100044b304902010002049363814102033d14ba0204dc39949d020468830cb6042465633962363936382d616266392d343632342d393131312d61363462376330636533656502040528000f02010004001dc74187" voicemd5="90c7545161c5101cda095f75a2535dc0" clientmsgid="494cd5716bb4ce7e67f76aaff6aa3e5648097389945@chatroom_48015_1753418932" fromusername="xiaomaochong" /></msg>'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1753418934, 'MsgSource': '<msgsource>\n\t<silence>0</silence>\n\t<membercount>60</membercount>\n\t<signature>N0_V1_kXppOm16|v1_D2yMcrxp</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'PushContent': '小爱在群聊中发了一段语音', 'NewMsgId': 1692479179568851830, 'MsgSeq': 871398761}
2025-07-25 12:48:31 | INFO | 收到语音消息: 消息ID:1767266183 来自:48097389945@chatroom 发送人:xiaomaochong XML:
<msg><voicemsg endflag="1" cancelflag="0" forwardflag="0" voiceformat="4" voicelength="23000" length="66642" bufid="0" aeskey="70656a65797178746e62776b63796270" voiceurl="3052020100044b304902010002049363814102033d14ba0204dc39949d020468830cb6042465633962363936382d616266392d343632342d393131312d61363462376330636533656502040528000f02010004001dc74187" voicemd5="90c7545161c5101cda095f75a2535dc0" clientmsgid="494cd5716bb4ce7e67f76aaff6aa3e5648097389945@chatroom_48015_1753418932" fromusername="xiaomaochong" /></msg>
2025-07-25 12:48:34 | DEBUG | 收到消息: {'MsgId': 1567802590, 'FromUserName': {'string': '47325400669@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 1, 'Content': {'string': 'wxid_9uwska6u4yzm22:\n群友集资两张5090给八戒搞个戒色版模型出来'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1753418936, 'MsgSource': '<msgsource>\n\t<bizflag>0</bizflag>\n\t<pua>1</pua>\n\t<eggIncluded>1</eggIncluded>\n\t<silence>0</silence>\n\t<membercount>221</membercount>\n\t<signature>N0_V1_ndxmIkCP|v1_nMdqfjL6</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'PushContent': 'Garson : 群友集资两张5090给八戒搞个戒色版模型出来', 'NewMsgId': 3149065218487791799, 'MsgSeq': 871398762}
2025-07-25 12:48:34 | INFO | 收到文本消息: 消息ID:1567802590 来自:47325400669@chatroom 发送人:wxid_9uwska6u4yzm22 @:[] 内容:群友集资两张5090给八戒搞个戒色版模型出来
2025-07-25 12:48:34 | DEBUG | 处理消息内容: '群友集资两张5090给八戒搞个戒色版模型出来'
2025-07-25 12:48:34 | DEBUG | 消息内容 '群友集资两张5090给八戒搞个戒色版模型出来' 不匹配任何命令，忽略
2025-07-25 12:48:35 | DEBUG | 收到消息: {'MsgId': 386508089, 'FromUserName': {'string': '48097389945@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 1, 'Content': {'string': 'wxid_l9koi6kli78i22:\n可以让我屌丝快乐'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1753418936, 'MsgSource': '<msgsource>\n\t<pua>1</pua>\n\t<silence>0</silence>\n\t<membercount>60</membercount>\n\t<signature>N0_V1_A0U/CmnL|v1_pjZU1mFo</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'PushContent': '十五 : 可以让我屌丝快乐', 'NewMsgId': 6839817398866357972, 'MsgSeq': 871398763}
2025-07-25 12:48:35 | INFO | 收到文本消息: 消息ID:386508089 来自:48097389945@chatroom 发送人:wxid_l9koi6kli78i22 @:[] 内容:可以让我屌丝快乐
2025-07-25 12:48:36 | DEBUG | 处理消息内容: '可以让我屌丝快乐'
2025-07-25 12:48:36 | DEBUG | 消息内容 '可以让我屌丝快乐' 不匹配任何命令，忽略
2025-07-25 12:48:39 | DEBUG | 收到消息: {'MsgId': 857505430, 'FromUserName': {'string': '48097389945@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 1, 'Content': {'string': 'wxid_84mmq4cu7ita22:\n在一个群聊了很多年，'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1753418941, 'MsgSource': '<msgsource>\n\t<sec_msg_node>\n\t\t<alnode>\n\t\t\t<fr>1</fr>\n\t\t</alnode>\n\t</sec_msg_node>\n\t<pua>1</pua>\n\t<silence>0</silence>\n\t<membercount>60</membercount>\n\t<signature>N0_V1_2Uj/aDuS|v1_wSojiO5/</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'PushContent': '喵小叶 : 在一个群聊了很多年，', 'NewMsgId': 3409831575447825181, 'MsgSeq': 871398764}
2025-07-25 12:48:39 | INFO | 收到文本消息: 消息ID:857505430 来自:48097389945@chatroom 发送人:wxid_84mmq4cu7ita22 @:[] 内容:在一个群聊了很多年，
2025-07-25 12:48:39 | DEBUG | 处理消息内容: '在一个群聊了很多年，'
2025-07-25 12:48:39 | DEBUG | 消息内容 '在一个群聊了很多年，' 不匹配任何命令，忽略
2025-07-25 12:48:41 | DEBUG | 收到消息: {'MsgId': 120288656, 'FromUserName': {'string': '48097389945@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 49, 'Content': {'string': 'wxid_cwxauba42ki122:\n<?xml version="1.0"?>\n<msg>\n\t<appmsg appid="" sdkver="0">\n\t\t<title>退了吧？咋没看见</title>\n\t\t<des />\n\t\t<username />\n\t\t<action>view</action>\n\t\t<type>57</type>\n\t\t<showtype>0</showtype>\n\t\t<content />\n\t\t<url />\n\t\t<lowurl />\n\t\t<forwardflag>0</forwardflag>\n\t\t<dataurl />\n\t\t<lowdataurl />\n\t\t<contentattr>0</contentattr>\n\t\t<streamvideo>\n\t\t\t<streamvideourl />\n\t\t\t<streamvideototaltime>0</streamvideototaltime>\n\t\t\t<streamvideotitle />\n\t\t\t<streamvideowording />\n\t\t\t<streamvideoweburl />\n\t\t\t<streamvideothumburl />\n\t\t\t<streamvideoaduxinfo />\n\t\t\t<streamvideopublishid />\n\t\t</streamvideo>\n\t\t<canvasPageItem>\n\t\t\t<canvasPageXml><![CDATA[]]></canvasPageXml>\n\t\t</canvasPageItem>\n\t\t<refermsg>\n\t\t\t<type>1</type>\n\t\t\t<svrid>265050997257942747</svrid>\n\t\t\t<fromusr>48097389945@chatroom</fromusr>\n\t\t\t<chatusr>xiaomaochong</chatusr>\n\t\t\t<displayname>小爱</displayname>\n\t\t\t<msgsource>&lt;msgsource&gt;\n\t&lt;pua&gt;1&lt;/pua&gt;\n\t&lt;silence&gt;1&lt;/silence&gt;\n\t&lt;membercount&gt;60&lt;/membercount&gt;\n\t&lt;signature&gt;N0_V1_ySm2YFol|v1_rEVAaYXm&lt;/signature&gt;\n\t&lt;tmp_node&gt;\n\t\t&lt;publisher-id&gt;&lt;/publisher-id&gt;\n\t&lt;/tmp_node&gt;\n&lt;/msgsource&gt;\n</msgsource>\n\t\t\t<content>\n嫂嫂在呢</content>\n\t\t\t<strid />\n\t\t\t<createtime>1753418874</createtime>\n\t\t</refermsg>\n\t\t<appattach>\n\t\t\t<totallen>0</totallen>\n\t\t\t<attachid />\n\t\t\t<cdnattachurl />\n\t\t\t<emoticonmd5></emoticonmd5>\n\t\t\t<aeskey></aeskey>\n\t\t\t<fileext />\n\t\t\t<islargefilemsg>0</islargefilemsg>\n\t\t</appattach>\n\t\t<extinfo />\n\t\t<androidsource>0</androidsource>\n\t\t<thumburl />\n\t\t<mediatagname />\n\t\t<messageaction><![CDATA[]]></messageaction>\n\t\t<messageext><![CDATA[]]></messageext>\n\t\t<emoticongift>\n\t\t\t<packageflag>0</packageflag>\n\t\t\t<packageid />\n\t\t</emoticongift>\n\t\t<emoticonshared>\n\t\t\t<packageflag>0</packageflag>\n\t\t\t<packageid />\n\t\t</emoticonshared>\n\t\t<designershared>\n\t\t\t<designeruin>0</designeruin>\n\t\t\t<designername>null</designername>\n\t\t\t<designerrediretcturl><![CDATA[null]]></designerrediretcturl>\n\t\t</designershared>\n\t\t<emotionpageshared>\n\t\t\t<tid>0</tid>\n\t\t\t<title>null</title>\n\t\t\t<desc>null</desc>\n\t\t\t<iconUrl><![CDATA[null]]></iconUrl>\n\t\t\t<secondUrl>null</secondUrl>\n\t\t\t<pageType>0</pageType>\n\t\t\t<setKey>null</setKey>\n\t\t</emotionpageshared>\n\t\t<webviewshared>\n\t\t\t<shareUrlOriginal />\n\t\t\t<shareUrlOpen />\n\t\t\t<jsAppId />\n\t\t\t<publisherId />\n\t\t\t<publisherReqId />\n\t\t</webviewshared>\n\t\t<template_id />\n\t\t<md5 />\n\t\t<websearch>\n\t\t\t<rec_category>0</rec_category>\n\t\t\t<channelId>0</channelId>\n\t\t</websearch>\n\t\t<weappinfo>\n\t\t\t<username />\n\t\t\t<appid />\n\t\t\t<appservicetype>0</appservicetype>\n\t\t\t<secflagforsinglepagemode>0</secflagforsinglepagemode>\n\t\t\t<videopageinfo>\n\t\t\t\t<thumbwidth>0</thumbwidth>\n\t\t\t\t<thumbheight>0</thumbheight>\n\t\t\t\t<fromopensdk>0</fromopensdk>\n\t\t\t</videopageinfo>\n\t\t</weappinfo>\n\t\t<statextstr />\n\t\t<musicShareItem>\n\t\t\t<musicDuration>0</musicDuration>\n\t\t</musicShareItem>\n\t\t<finderLiveProductShare>\n\t\t\t<finderLiveID><![CDATA[]]></finderLiveID>\n\t\t\t<finderUsername><![CDATA[]]></finderUsername>\n\t\t\t<finderObjectID><![CDATA[]]></finderObjectID>\n\t\t\t<finderNonceID><![CDATA[]]></finderNonceID>\n\t\t\t<liveStatus><![CDATA[]]></liveStatus>\n\t\t\t<appId><![CDATA[]]></appId>\n\t\t\t<pagePath><![CDATA[]]></pagePath>\n\t\t\t<productId><![CDATA[]]></productId>\n\t\t\t<coverUrl><![CDATA[]]></coverUrl>\n\t\t\t<productTitle><![CDATA[]]></productTitle>\n\t\t\t<marketPrice><![CDATA[0]]></marketPrice>\n\t\t\t<sellingPrice><![CDATA[0]]></sellingPrice>\n\t\t\t<platformHeadImg><![CDATA[]]></platformHeadImg>\n\t\t\t<platformName><![CDATA[]]></platformName>\n\t\t\t<shopWindowId><![CDATA[]]></shopWindowId>\n\t\t\t<flashSalePrice><![CDATA[0]]></flashSalePrice>\n\t\t\t<flashSaleEndTime><![CDATA[0]]></flashSaleEndTime>\n\t\t\t<ecSource><![CDATA[]]></ecSource>\n\t\t\t<sellingPriceWording><![CDATA[]]></sellingPriceWording>\n\t\t\t<platformIconURL><![CDATA[]]></platformIconURL>\n\t\t\t<firstProductTagURL><![CDATA[]]></firstProductTagURL>\n\t\t\t<firstProductTagAspectRatioString><![CDATA[0.0]]></firstProductTagAspectRatioString>\n\t\t\t<secondProductTagURL><![CDATA[]]></secondProductTagURL>\n\t\t\t<secondProductTagAspectRatioString><![CDATA[0.0]]></secondProductTagAspectRatioString>\n\t\t\t<firstGuaranteeWording><![CDATA[]]></firstGuaranteeWording>\n\t\t\t<secondGuaranteeWording><![CDATA[]]></secondGuaranteeWording>\n\t\t\t<thirdGuaranteeWording><![CDATA[]]></thirdGuaranteeWording>\n\t\t\t<isPriceBeginShow>false</isPriceBeginShow>\n\t\t\t<lastGMsgID><![CDATA[]]></lastGMsgID>\n\t\t\t<promoterKey><![CDATA[]]></promoterKey>\n\t\t\t<discountWording><![CDATA[]]></discountWording>\n\t\t\t<priceSuffixDescription><![CDATA[]]></priceSuffixDescription>\n\t\t\t<showBoxItemStringList />\n\t\t</finderLiveProductShare>\n\t\t<finderOrder>\n\t\t\t<appID><![CDATA[]]></appID>\n\t\t\t<orderID><![CDATA[]]></orderID>\n\t\t\t<path><![CDATA[]]></path>\n\t\t\t<priceWording><![CDATA[]]></priceWording>\n\t\t\t<stateWording><![CDATA[]]></stateWording>\n\t\t\t<productImageURL><![CDATA[]]></productImageURL>\n\t\t\t<products><![CDATA[]]></products>\n\t\t\t<productsCount><![CDATA[0]]></productsCount>\n\t\t</finderOrder>\n\t\t<finderShopWindowShare>\n\t\t\t<finderUsername><![CDATA[]]></finderUsername>\n\t\t\t<avatar><![CDATA[]]></avatar>\n\t\t\t<nickname><![CDATA[]]></nickname>\n\t\t\t<commodityInStockCount><![CDATA[]]></commodityInStockCount>\n\t\t\t<appId><![CDATA[]]></appId>\n\t\t\t<path><![CDATA[]]></path>\n\t\t\t<appUsername><![CDATA[]]></appUsername>\n\t\t\t<query><![CDATA[]]></query>\n\t\t\t<liteAppId><![CDATA[]]></liteAppId>\n\t\t\t<liteAppPath><![CDATA[]]></liteAppPath>\n\t\t\t<liteAppQuery><![CDATA[]]></liteAppQuery>\n\t\t\t<platformTagURL><![CDATA[]]></platformTagURL>\n\t\t\t<saleWording><![CDATA[]]></saleWording>\n\t\t\t<lastGMsgID><![CDATA[]]></lastGMsgID>\n\t\t\t<profileTypeWording><![CDATA[]]></profileTypeWording>\n\t\t\t<reputationInfo>\n\t\t\t\t<hasReputationInfo>0</hasReputationInfo>\n\t\t\t\t<reputationScore>0</reputationScore>\n\t\t\t\t<reputationWording />\n\t\t\t\t<reputationTextColor />\n\t\t\t\t<reputationLevelWording />\n\t\t\t\t<reputationBackgroundColor />\n\t\t\t</reputationInfo>\n\t\t\t<productImageURLList />\n\t\t</finderShopWindowShare>\n\t\t<findernamecard>\n\t\t\t<username />\n\t\t\t<avatar><![CDATA[]]></avatar>\n\t\t\t<nickname />\n\t\t\t<auth_job />\n\t\t\t<auth_icon>0</auth_icon>\n\t\t\t<auth_icon_url />\n\t\t\t<ecSource><![CDATA[]]></ecSource>\n\t\t\t<lastGMsgID><![CDATA[]]></lastGMsgID>\n\t\t</findernamecard>\n\t\t<finderGuarantee>\n\t\t\t<scene><![CDATA[0]]></scene>\n\t\t</finderGuarantee>\n\t\t<directshare>0</directshare>\n\t\t<gamecenter>\n\t\t\t<namecard>\n\t\t\t\t<iconUrl />\n\t\t\t\t<name />\n\t\t\t\t<desc />\n\t\t\t\t<tail />\n\t\t\t\t<jumpUrl />\n\t\t\t</namecard>\n\t\t</gamecenter>\n\t\t<patMsg>\n\t\t\t<chatUser />\n\t\t\t<records>\n\t\t\t\t<recordNum>0</recordNum>\n\t\t\t</records>\n\t\t</patMsg>\n\t\t<secretmsg>\n\t\t\t<issecretmsg>0</issecretmsg>\n\t\t</secretmsg>\n\t\t<referfromscene>0</referfromscene>\n\t\t<gameshare>\n\t\t\t<liteappext>\n\t\t\t\t<liteappbizdata />\n\t\t\t\t<priority>0</priority>\n\t\t\t</liteappext>\n\t\t\t<appbrandext>\n\t\t\t\t<litegameinfo />\n\t\t\t\t<priority>-1</priority>\n\t\t\t</appbrandext>\n\t\t\t<gameshareid />\n\t\t\t<sharedata />\n\t\t\t<isvideo>0</isvideo>\n\t\t\t<duration>-1</duration>\n\t\t\t<isexposed>0</isexposed>\n\t\t\t<readtext />\n\t\t</gameshare>\n\t\t<mpsharetrace>\n\t\t\t<hasfinderelement>0</hasfinderelement>\n\t\t\t<lastgmsgid />\n\t\t</mpsharetrace>\n\t\t<wxgamecard>\n\t\t\t<framesetname />\n\t\t\t<mbcarddata />\n\t\t\t<minpkgversion />\n\t\t\t<mbcardheight>0</mbcardheight>\n\t\t\t<isoldversion>0</isoldversion>\n\t\t</wxgamecard>\n\t\t<liteapp>\n\t\t\t<id>null</id>\n\t\t\t<path />\n\t\t\t<query />\n\t\t</liteapp>\n\t</appmsg>\n\t<fromusername>wxid_cwxauba42ki122</fromusername>\n\t<scene>0</scene>\n\t<appinfo>\n\t\t<version>1</version>\n\t\t<appname />\n\t</appinfo>\n\t<commenturl />\n</msg>\n'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1753418942, 'MsgSource': '<msgsource>\n\t<alnode>\n\t\t<fr>4</fr>\n\t</alnode>\n\t<sec_msg_node>\n\t\t<uuid>06832cb7bea2b122c976e0864ef2ec6e_</uuid>\n\t\t<risk-file-flag />\n\t\t<risk-file-md5-list />\n\t</sec_msg_node>\n\t<silence>0</silence>\n\t<membercount>60</membercount>\n\t<signature>N0_V1_f8buEf2q|v1_XhUOvjsT</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'PushContent': 'ن初心ᯤ⁶ᴳ คิดถึงن : 退了吧？咋没看见', 'NewMsgId': 9058105508062270562, 'MsgSeq': 871398765}
2025-07-25 12:48:41 | DEBUG | 从群聊消息中提取发送者: wxid_cwxauba42ki122
2025-07-25 12:48:41 | DEBUG | 使用已解析的XML处理引用消息
2025-07-25 12:48:41 | INFO | 收到引用消息: 消息ID:120288656 来自:48097389945@chatroom 发送人:wxid_cwxauba42ki122 内容:退了吧？咋没看见 引用类型:1
2025-07-25 12:48:41 | INFO | [DouBaoImageToImage] 收到引用消息: 退了吧？咋没看见
2025-07-25 12:48:41 | INFO | [TimerTask] 收到引用消息调试信息:
2025-07-25 12:48:41 | INFO |   - 消息内容: 退了吧？咋没看见
2025-07-25 12:48:41 | INFO |   - 群组ID: 48097389945@chatroom
2025-07-25 12:48:41 | INFO |   - 发送人: wxid_cwxauba42ki122
2025-07-25 12:48:41 | INFO |   - 引用信息: {'MsgType': 1, 'Content': '\n嫂嫂在呢', 'Msgid': '265050997257942747', 'NewMsgId': '265050997257942747', 'ToWxid': 'wxid_4usgcju5ey9q29', 'FromWxid': '48097389945@chatroom', 'Nickname': '小爱', 'MsgSource': '<msgsource>\n\t<pua>1</pua>\n\t<silence>1</silence>\n\t<membercount>60</membercount>\n\t<signature>N0_V1_ySm2YFol|v1_rEVAaYXm</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'Createtime': '1753418874', 'SenderWxid': 'wxid_cwxauba42ki122'}
2025-07-25 12:48:41 | INFO |   - 引用消息ID: 
2025-07-25 12:48:41 | INFO |   - 引用消息类型: 
2025-07-25 12:48:41 | INFO |   - 引用消息内容: 
嫂嫂在呢
2025-07-25 12:48:41 | INFO |   - 引用消息发送人: wxid_cwxauba42ki122
2025-07-25 12:48:48 | DEBUG | 收到消息: {'MsgId': 329231539, 'FromUserName': {'string': '48097389945@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 1, 'Content': {'string': 'wxid_84mmq4cu7ita22:\n玩的大家不欢而散了'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1753418950, 'MsgSource': '<msgsource>\n\t<sec_msg_node>\n\t\t<alnode>\n\t\t\t<fr>1</fr>\n\t\t</alnode>\n\t</sec_msg_node>\n\t<pua>1</pua>\n\t<silence>0</silence>\n\t<membercount>60</membercount>\n\t<signature>N0_V1_bEXFTWPd|v1_0BsNLOIT</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'PushContent': '喵小叶 : 玩的大家不欢而散了', 'NewMsgId': 5011750228121167021, 'MsgSeq': 871398766}
2025-07-25 12:48:48 | INFO | 收到文本消息: 消息ID:329231539 来自:48097389945@chatroom 发送人:wxid_84mmq4cu7ita22 @:[] 内容:玩的大家不欢而散了
2025-07-25 12:48:48 | DEBUG | 处理消息内容: '玩的大家不欢而散了'
2025-07-25 12:48:48 | DEBUG | 消息内容 '玩的大家不欢而散了' 不匹配任何命令，忽略
2025-07-25 12:48:55 | DEBUG | 收到消息: {'MsgId': 1767161086, 'FromUserName': {'string': '48097389945@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 1, 'Content': {'string': 'xiaomaochong:\n嫂子的小号在呢'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1753418958, 'MsgSource': '<msgsource>\n\t<pua>1</pua>\n\t<silence>0</silence>\n\t<membercount>60</membercount>\n\t<signature>N0_V1_Ch2RJyZe|v1_E+2yGOpc</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'PushContent': '小爱 : 嫂子的小号在呢', 'NewMsgId': 1012003243502546771, 'MsgSeq': 871398767}
2025-07-25 12:48:55 | INFO | 收到文本消息: 消息ID:1767161086 来自:48097389945@chatroom 发送人:xiaomaochong @:[] 内容:嫂子的小号在呢
2025-07-25 12:48:56 | DEBUG | 处理消息内容: '嫂子的小号在呢'
2025-07-25 12:48:56 | DEBUG | 消息内容 '嫂子的小号在呢' 不匹配任何命令，忽略
2025-07-25 12:49:01 | DEBUG | 收到消息: {'MsgId': 129700582, 'FromUserName': {'string': '48097389945@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 1, 'Content': {'string': 'wxid_cwxauba42ki122:\n[擦汗]'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1753418964, 'MsgSource': '<msgsource>\n\t<pua>1</pua>\n\t<silence>0</silence>\n\t<membercount>60</membercount>\n\t<signature>N0_V1_O8RlAJmo|v1_IZcW7huX</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'PushContent': 'ن初心ᯤ⁶ᴳ คิดถึงن : [擦汗]', 'NewMsgId': 9080306753223386500, 'MsgSeq': 871398768}
2025-07-25 12:49:01 | INFO | 收到表情消息: 消息ID:129700582 来自:48097389945@chatroom 发送人:wxid_cwxauba42ki122 @:[] 内容:[擦汗]
2025-07-25 12:49:02 | DEBUG | [DoubaoImageRecognition] 缓存表情消息: 9080306753223386500
2025-07-25 12:49:18 | DEBUG | 收到消息: {'MsgId': 527774871, 'FromUserName': {'string': '48097389945@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 1, 'Content': {'string': 'xiaomaochong:\n只是你不知道是谁[抠鼻]'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1753418981, 'MsgSource': '<msgsource>\n\t<pua>1</pua>\n\t<silence>0</silence>\n\t<membercount>60</membercount>\n\t<signature>N0_V1_hjqCbxX8|v1_+0CgorQw</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'PushContent': '小爱 : 只是你不知道是谁[抠鼻]', 'NewMsgId': 1052980008148842455, 'MsgSeq': 871398769}
2025-07-25 12:49:18 | INFO | 收到文本消息: 消息ID:527774871 来自:48097389945@chatroom 发送人:xiaomaochong @:[] 内容:只是你不知道是谁[抠鼻]
2025-07-25 12:49:19 | DEBUG | 处理消息内容: '只是你不知道是谁[抠鼻]'
2025-07-25 12:49:19 | DEBUG | 消息内容 '只是你不知道是谁[抠鼻]' 不匹配任何命令，忽略
2025-07-25 12:49:35 | DEBUG | 收到消息: {'MsgId': 1737206957, 'FromUserName': {'string': '48097389945@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 1, 'Content': {'string': 'wxid_cwxauba42ki122:\n看来嫂嫂是不喜欢我了，故意变了小号'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1753418997, 'MsgSource': '<msgsource>\n\t<pua>1</pua>\n\t<silence>0</silence>\n\t<membercount>60</membercount>\n\t<signature>N0_V1_opxYIDZS|v1_2QKswW7y</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'PushContent': 'ن初心ᯤ⁶ᴳ คิดถึงن : 看来嫂嫂是不喜欢我了，故意变了小号', 'NewMsgId': 4676593803864466626, 'MsgSeq': 871398770}
2025-07-25 12:49:35 | INFO | 收到文本消息: 消息ID:1737206957 来自:48097389945@chatroom 发送人:wxid_cwxauba42ki122 @:[] 内容:看来嫂嫂是不喜欢我了，故意变了小号
2025-07-25 12:49:35 | DEBUG | 处理消息内容: '看来嫂嫂是不喜欢我了，故意变了小号'
2025-07-25 12:49:35 | DEBUG | 消息内容 '看来嫂嫂是不喜欢我了，故意变了小号' 不匹配任何命令，忽略
2025-07-25 12:49:43 | DEBUG | 收到消息: {'MsgId': 187695862, 'FromUserName': {'string': '48097389945@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 49, 'Content': {'string': 'wxid_84mmq4cu7ita22:\n<?xml version="1.0"?>\n<msg>\n\t<appmsg appid="" sdkver="0">\n\t\t<title>我以前就是不给加，</title>\n\t\t<des />\n\t\t<action>view</action>\n\t\t<type>57</type>\n\t\t<showtype>0</showtype>\n\t\t<content />\n\t\t<url />\n\t\t<dataurl />\n\t\t<lowurl />\n\t\t<lowdataurl />\n\t\t<recorditem />\n\t\t<thumburl />\n\t\t<messageaction />\n\t\t<laninfo />\n\t\t<refermsg>\n\t\t\t<type>1</type>\n\t\t\t<svrid>7930735540245270298</svrid>\n\t\t\t<fromusr>48097389945@chatroom</fromusr>\n\t\t\t<chatusr>wxid_l9koi6kli78i22</chatusr>\n\t\t\t<createtime>1753418474</createtime>\n\t\t\t<msgsource>&lt;msgsource&gt;\n    &lt;pua&gt;1&lt;/pua&gt;\n    &lt;silence&gt;1&lt;/silence&gt;\n    &lt;membercount&gt;61&lt;/membercount&gt;\n    &lt;signature&gt;N0_V1_Ioa6DeQA|v1_crYe9Rle&lt;/signature&gt;\n    &lt;tmp_node&gt;\n        &lt;publisher-id /&gt;\n    &lt;/tmp_node&gt;\n    &lt;sec_msg_node&gt;\n        &lt;alnode&gt;\n            &lt;fr&gt;1&lt;/fr&gt;\n        &lt;/alnode&gt;\n    &lt;/sec_msg_node&gt;\n&lt;/msgsource&gt;\n</msgsource>\n\t\t\t<content>加好友就是🤡</content>\n\t\t\t<displayname>十五</displayname>\n\t\t</refermsg>\n\t\t<extinfo />\n\t\t<sourceusername />\n\t\t<sourcedisplayname />\n\t\t<commenturl />\n\t\t<appattach>\n\t\t\t<totallen>0</totallen>\n\t\t\t<attachid />\n\t\t\t<emoticonmd5 />\n\t\t\t<fileext />\n\t\t\t<aeskey />\n\t\t</appattach>\n\t\t<webviewshared>\n\t\t\t<publisherId />\n\t\t\t<publisherReqId>0</publisherReqId>\n\t\t</webviewshared>\n\t\t<weappinfo>\n\t\t\t<pagepath />\n\t\t\t<username />\n\t\t\t<appid />\n\t\t\t<appservicetype>0</appservicetype>\n\t\t</weappinfo>\n\t\t<websearch />\n\t</appmsg>\n\t<fromusername>wxid_84mmq4cu7ita22</fromusername>\n\t<scene>0</scene>\n\t<appinfo>\n\t\t<version>1</version>\n\t\t<appname></appname>\n\t</appinfo>\n\t<commenturl></commenturl>\n</msg>\n'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1753419005, 'MsgSource': '<msgsource>\n\t<pua>1</pua>\n\t<sec_msg_node>\n\t\t<uuid>f5b9e1c7449f35a27d6066ba9ceb8186_</uuid>\n\t\t<risk-file-flag />\n\t\t<risk-file-md5-list />\n\t</sec_msg_node>\n\t<silence>0</silence>\n\t<membercount>60</membercount>\n\t<signature>N0_V1_BMZ/LJpX|v1_xkFksjQ0</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'PushContent': '喵小叶 : 我以前就是不给加，', 'NewMsgId': 3283037910291342737, 'MsgSeq': 871398771}
2025-07-25 12:49:43 | DEBUG | 从群聊消息中提取发送者: wxid_84mmq4cu7ita22
2025-07-25 12:49:43 | DEBUG | 使用已解析的XML处理引用消息
2025-07-25 12:49:43 | INFO | 收到引用消息: 消息ID:187695862 来自:48097389945@chatroom 发送人:wxid_84mmq4cu7ita22 内容:我以前就是不给加， 引用类型:1
2025-07-25 12:49:43 | INFO | [DouBaoImageToImage] 收到引用消息: 我以前就是不给加，
2025-07-25 12:49:43 | INFO | [TimerTask] 收到引用消息调试信息:
2025-07-25 12:49:43 | INFO |   - 消息内容: 我以前就是不给加，
2025-07-25 12:49:43 | INFO |   - 群组ID: 48097389945@chatroom
2025-07-25 12:49:43 | INFO |   - 发送人: wxid_84mmq4cu7ita22
2025-07-25 12:49:43 | INFO |   - 引用信息: {'MsgType': 1, 'Content': '加好友就是🤡', 'Msgid': '7930735540245270298', 'NewMsgId': '7930735540245270298', 'ToWxid': 'wxid_4usgcju5ey9q29', 'FromWxid': '48097389945@chatroom', 'Nickname': '十五', 'MsgSource': '<msgsource>\n    <pua>1</pua>\n    <silence>1</silence>\n    <membercount>61</membercount>\n    <signature>N0_V1_Ioa6DeQA|v1_crYe9Rle</signature>\n    <tmp_node>\n        <publisher-id />\n    </tmp_node>\n    <sec_msg_node>\n        <alnode>\n            <fr>1</fr>\n        </alnode>\n    </sec_msg_node>\n</msgsource>\n', 'Createtime': '1753418474', 'SenderWxid': 'wxid_84mmq4cu7ita22'}
2025-07-25 12:49:43 | INFO |   - 引用消息ID: 
2025-07-25 12:49:43 | INFO |   - 引用消息类型: 
2025-07-25 12:49:43 | INFO |   - 引用消息内容: 加好友就是🤡
2025-07-25 12:49:43 | INFO |   - 引用消息发送人: wxid_84mmq4cu7ita22
2025-07-25 12:49:51 | DEBUG | 收到消息: {'MsgId': 1676321660, 'FromUserName': {'string': '48097389945@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 1, 'Content': {'string': 'wxid_84mmq4cu7ita22:\n然后，越禁止越多'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1753419013, 'MsgSource': '<msgsource>\n\t<sec_msg_node>\n\t\t<alnode>\n\t\t\t<fr>1</fr>\n\t\t</alnode>\n\t</sec_msg_node>\n\t<pua>1</pua>\n\t<silence>0</silence>\n\t<membercount>60</membercount>\n\t<signature>N0_V1_hPwJG9B2|v1_D6WMOpP2</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'PushContent': '喵小叶 : 然后，越禁止越多', 'NewMsgId': 8149412092626954274, 'MsgSeq': 871398772}
2025-07-25 12:49:51 | INFO | 收到文本消息: 消息ID:1676321660 来自:48097389945@chatroom 发送人:wxid_84mmq4cu7ita22 @:[] 内容:然后，越禁止越多
