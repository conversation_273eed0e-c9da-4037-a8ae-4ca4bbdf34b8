[WechatAPIServer]
port = 9000                # WechatAPI服务器端口，默认9000，如有冲突可修改
mode = "release"           # 运行模式：release(生产环境)，debug(调试模式)
redis-host = "127.0.0.1"   # Redis服务器地址，本地使用127.0.0.1
redis-port = 6379          # Redis端口，默认6379
redis-password = ""        # Redis密码，如果有设置密码则填写
redis-db = 0               # Redis数据库编号，默认0

# XYBot 核心设置
[XYBot]
version = "v1.0.0"                    # 版本号，请勿修改
ignore-protection = false             # 是否忽略风控保护机制，建议保持false
database-url = "sqlite:///xybot.db"   # SQLite数据库地址，一般无需修改
keyvalDB-url = "sqlite+aiosqlite:///database/keyval.db"  # 键值数据库地址

# 管理员设置
admins = ["wxid_ubbh6q832tcs21", "admin-wxid"]  # 管理员的wxid列表，可从消息日志中获取
disabled-plugins = ["ExamplePlugin"]   # 禁用的插件列表，不需要的插件名称填在这里
timezone = "Asia/Shanghai"             # 时区设置，中国用户使用 Asia/Shanghai

# 实验性功能，如果main_config.toml配置改动，或者plugins文件夹有改动，自动重启。可以在开发时使用，不建议在生产环境使用。
auto-restart = false                 # 仅建议在开发时启用，生产环境保持false

# 消息过滤设置
ignore-mode = "None"            # 消息处理模式：
# "None" - 处理所有消息
# "Whitelist" - 仅处理白名单消息
# "Blacklist" - 屏蔽黑名单消息

whitelist = [# 白名单列表
    "wxid_1", # 个人用户微信ID
    "wxid_2",
    "chatroom@111", # 群聊ID
    "chatroom@222"
]

blacklist = [# 黑名单列表
    "wxid_3", # 个人用户微信ID
    "wxid_4",
    "chatroom@333", # 群聊ID
    "chatroom@444"
]

# OpenAI格式API设置
[OpenAI]
api-key = "sk-xxxx"                    # OpenAI格式API密钥，必须填写有效的API Key
base-url = "https://api.openai.com/v1" # API接口地址
# 使用OpanAI官方API填写 https://api.openai.com/v1
# 使用其他API或者代理API需要修改为对应地址