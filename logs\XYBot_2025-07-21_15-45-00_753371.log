2025-07-21 15:45:02 | SUCCESS | 读取主设置成功
2025-07-21 15:45:02 | DEBUG | 最终使用的 Redis 主机地址: 127.0.0.1
2025-07-21 15:45:02 | INFO | 2025/07/21 15:45:02 GetRedisAddr: 127.0.0.1:6379
2025-07-21 15:45:02 | INFO | 2025/07/21 15:45:02 Redis连接成功 | 地址:127.0.0.1:6379 DB:0
2025-07-21 15:45:02 | INFO | 2025/07/21 15:45:02 Server start at :9000
2025-07-21 15:45:02 | SUCCESS | WechatAPI服务已启动
2025-07-21 15:45:03 | INFO | 登录账号信息: wxid: wxid_4usgcju5ey9q29  昵称: 瑶瑶  微信号: Today-KFC  手机号: None
2025-07-21 15:45:03 | INFO | 登录设备信息: device_name: <PERSON>'s Pad  device_id: 49d050a7e94d1ecc40790d74b1c4369f
2025-07-21 15:45:03 | SUCCESS | 登录成功
2025-07-21 15:45:03 | SUCCESS | 已开启自动心跳
2025-07-21 15:45:03 | INFO | 成功加载表情映射文件，共 519 条记录
2025-07-21 15:45:03 | SUCCESS | 数据库初始化成功
2025-07-21 15:45:03 | SUCCESS | 定时任务已启动
2025-07-21 15:45:03 | SUCCESS | 插件 AISignIn 已加载定时任务: {'plugins.AISignIn.main.AISignIn.auto_signin_task'}
2025-07-21 15:45:03 | INFO | 已加载管理员列表: ['wxid_ubbh6q832tcs21', 'admin-wxid']
2025-07-21 15:45:04 | INFO | 播客API初始化成功
2025-07-21 15:45:04 | INFO | 加载配置完成，转换后的数据: {'gh_7057516c9e71': {'name': '唱舞全明星', 'groups': ['27852221909@chatroom', '55878994168@chatroom', '51891329927@chatroom']}, 'gh_d4d21a0e4623': {'name': '两颗桃', 'groups': ['48097389945@chatroom']}, 'gh_410ff2fa6ec9': {'name': 'NGC660 Ai研究院', 'groups': ['47442567074@chatroom']}, 'gh_f78c53c507c0': {'name': '不正常吐槽君', 'groups': ['48097389945@chatroom']}, 'gh_ba3381c847b3': {'name': '唱舞星计划', 'groups': ['51891329927@chatroom']}, 'gh_1a102fb4cad9': {'name': '我在娱乐圈的唯一人脉', 'groups': ['48097389945@chatroom', '27852221909@chatroom']}}
2025-07-21 15:45:04 | INFO | ArticleForwarder插件初始化完成 - 监控配置: {'gh_7057516c9e71': {'name': '唱舞全明星', 'groups': ['27852221909@chatroom', '55878994168@chatroom', '51891329927@chatroom']}, 'gh_d4d21a0e4623': {'name': '两颗桃', 'groups': ['48097389945@chatroom']}, 'gh_410ff2fa6ec9': {'name': 'NGC660 Ai研究院', 'groups': ['47442567074@chatroom']}, 'gh_f78c53c507c0': {'name': '不正常吐槽君', 'groups': ['48097389945@chatroom']}, 'gh_ba3381c847b3': {'name': '唱舞星计划', 'groups': ['51891329927@chatroom']}, 'gh_1a102fb4cad9': {'name': '我在娱乐圈的唯一人脉', 'groups': ['48097389945@chatroom', '27852221909@chatroom']}}
2025-07-21 15:45:04 | DEBUG | [TempFileManager] 添加清理规则: default
2025-07-21 15:45:04 | DEBUG | [TempFileManager] 添加清理规则: images
2025-07-21 15:45:04 | DEBUG | [TempFileManager] 添加清理规则: videos
2025-07-21 15:45:04 | DEBUG | [TempFileManager] 添加清理规则: audio
2025-07-21 15:45:04 | DEBUG | [TempFileManager] 添加清理规则: temp
2025-07-21 15:45:04 | DEBUG | [TempFileManager] 添加清理规则: large_files
2025-07-21 15:45:04 | INFO | [TempFileManager] 临时文件清理任务已启动
2025-07-21 15:45:04 | INFO | [ChatSummary] 数据库初始化成功
2025-07-21 15:45:05 | INFO | 成功加载表情映射文件，共 519 条记录
2025-07-21 15:45:05 | SUCCESS | 插件 GoodMorning 已加载定时任务: {'plugins.GoodMorning.main.GoodMorning.daily_task'}
2025-07-21 15:45:05 | SUCCESS | 插件 News 已加载定时任务: {'plugins.News.main.News.noon_news', 'plugins.News.main.News.night_news'}
2025-07-21 15:45:05 | INFO | [PatReply] AMR文件已存在且是最新的: C:\XYBotV2\data\paiyipai\1_BFB5258B-2B13-4CF4-A40C-5BCCE74E5414_converted.amr
2025-07-21 15:45:05 | DEBUG | [PatReply] 从缓存加载base64: 18228 字符
2025-07-21 15:45:05 | SUCCESS | 插件 QuarkSignIn 已加载定时任务: {'plugins.QuarkSignIn.main.QuarkSignIn.auto_signin_task'}
2025-07-21 15:45:05 | INFO | [QuarkSignIn] 已设置自动签到时间: 08:00
2025-07-21 15:45:05 | INFO | 插件状态: 启用, 检查间隔: 3600秒, 最大重试次数: 3, 重试延迟: 5秒
2025-07-21 15:45:05 | INFO | 已加载管理员列表: ['wxid_ubbh6q832tcs21', 'admin-wxid']
2025-07-21 15:45:05 | INFO | 已加载 2 个改名监控群和 1 个退群监控群
2025-07-21 15:45:05 | INFO | [RenameReminder] 开始启用插件...
2025-07-21 15:45:05 | SUCCESS | 插件 RenameReminder 已加载定时任务: {'plugins.RenameReminder.main.RenameReminder.check_name_changes'}
2025-07-21 15:45:05 | INFO | [RenameReminder] 插件已启用，开始初始化...
2025-07-21 15:45:05 | INFO | [RenameReminder] 支持的命令: 开启改名提醒, 关闭改名提醒, 开启退群提醒, 关闭退群提醒, 查看提醒状态
2025-07-21 15:45:05 | INFO | 已设置检查间隔为 3600 秒
2025-07-21 15:45:05 | INFO | 已加载 2 个监控群，开始更新成员列表
2025-07-21 15:45:05 | DEBUG | 已更新群 51891329927@chatroom 的成员列表
2025-07-21 15:45:06 | DEBUG | 已更新群 27852221909@chatroom 的成员列表
2025-07-21 15:45:06 | SUCCESS | 插件 TimerTask 已加载定时任务: {'plugins.TimerTask.main.TimerTask.check_tasks'}
2025-07-21 15:45:06 | INFO | [VideoDemand] 加载了 128 个视频类别配置
2025-07-21 15:45:06 | INFO | [YaoyaoPlugin] 私聊黑名单用户: ['gh_a5cf07d474f1', 'gh_ba3381c847b3']
2025-07-21 15:45:06 | INFO | [YaoyaoPlugin] 已加载管理员列表: ['wxid_ubbh6q832tcs21', 'admin-wxid']
2025-07-21 15:45:06 | INFO | [yuanbao] 插件初始化完成
2025-07-21 15:45:06 | DEBUG | [yuanbao] 指令: ['元宝', 'yuanbao']
2025-07-21 15:45:06 | DEBUG | [yuanbao] 绘图模型: gpt_175B_0404
2025-07-21 15:45:06 | DEBUG | [yuanbao] 认证信息加载状态: hy_token=True, hy_user=True, agent_id=True, x_uskey=True
2025-07-21 15:45:06 | SUCCESS | 已加载插件: ['AdminPoint', 'AdminSignInReset', 'AdminWhitelist', 'AISignIn', 'AppMessageTester', 'ArticleForwarder', 'BaiduAgentsPlugin', 'BaiduDraw', 'BotStatus', 'ChatSummary', 'DanceSignInPlugin', 'DeepseekPlugin', 'Doubao', 'DoubaoDrawing', 'DoubaoImageRecognition', 'DouBaoImageToImage', 'DouBaoImageToVideo', 'DoubaoVideoSearch', 'DouHui', 'EmojiTestPlugin', 'FixedQuoteTest', 'GetContact', 'Gomoku', 'GoodMorning', 'HunyuanDraw', 'ImageEcho', 'ImageToImage', 'JiemengDraw', 'KeLingImageToImage', 'KlingAI', 'Leaderboard', 'LuckyDraw', 'ManagePlugin', 'Menu', 'MiniProgramTester', 'Music', 'News', 'PatReply', 'PointTrade', 'QuarkSignIn', 'QueryPoint', 'RandomMember', 'RandomPicture', 'RenameReminder', 'RevokePlugin', 'RoboNeo', 'SignIn', 'TempFileManagerPlugin', 'TencentLke', 'TimerTask', 'TongyiDraw', 'URLShortener', 'VideoDemand', 'VideoParserPlugin', 'VideoTest', 'VivoAgentsPlugin', 'VoiceTest', 'Warthunder', 'WeatherQuery', 'WelcomePlugin', 'Xunfei', 'YaoyaoPlugin', 'Yuanbao', '抽签', '造梦次元']
2025-07-21 15:45:06 | INFO | 处理堆积消息中
2025-07-21 15:45:07 | SUCCESS | 处理堆积消息完毕
2025-07-21 15:45:07 | SUCCESS | 开始处理消息
2025-07-21 15:45:17 | DEBUG | 收到消息: {'MsgId': 997969088, 'FromUserName': {'string': '48097389945@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 1, 'Content': {'string': 'wxid_84mmq4cu7ita22:\n小熊'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1753083926, 'MsgSource': '<msgsource>\n\t<sec_msg_node>\n\t\t<alnode>\n\t\t\t<fr>1</fr>\n\t\t</alnode>\n\t</sec_msg_node>\n\t<pua>1</pua>\n\t<silence>0</silence>\n\t<membercount>62</membercount>\n\t<signature>N0_V1_xcRS4AGg|v1_sJTDiiAs</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'PushContent': '喵小叶 : 小熊', 'NewMsgId': 8717323020297528720, 'MsgSeq': 871387670}
2025-07-21 15:45:17 | INFO | 收到文本消息: 消息ID:997969088 来自:48097389945@chatroom 发送人:wxid_84mmq4cu7ita22 @:[] 内容:小熊
2025-07-21 15:45:17 | DEBUG | 处理消息内容: '小熊'
2025-07-21 15:45:17 | DEBUG | 消息内容 '小熊' 不匹配任何命令，忽略
2025-07-21 15:45:25 | DEBUG | 收到消息: {'MsgId': 1199612817, 'FromUserName': {'string': '48097389945@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 1, 'Content': {'string': 'last--exile:\n#女生蓝色衬衣配什么颜色裤子好看'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1753083934, 'MsgSource': '<msgsource>\n\t<pua>1</pua>\n\t<alnode>\n\t\t<cf>5</cf>\n\t\t<inlenlist>14,12</inlenlist>\n\t</alnode>\n\t<silence>0</silence>\n\t<membercount>62</membercount>\n\t<signature>N0_V1_RvV+vWgY|v1_PAdOnDXf</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'PushContent': '亮 : #女生蓝色衬衣配什么颜色裤子好看', 'NewMsgId': 1649069227636178257, 'MsgSeq': 871387671}
2025-07-21 15:45:25 | INFO | 收到文本消息: 消息ID:1199612817 来自:48097389945@chatroom 发送人:last--exile @:[] 内容:#女生蓝色衬衣配什么颜色裤子好看
2025-07-21 15:45:25 | DEBUG | 处理消息内容: '#女生蓝色衬衣配什么颜色裤子好看'
2025-07-21 15:45:25 | DEBUG | 消息内容 '#女生蓝色衬衣配什么颜色裤子好看' 不匹配任何命令，忽略
2025-07-21 15:45:29 | DEBUG | 收到消息: {'MsgId': 1576765006, 'FromUserName': {'string': '48097389945@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 34, 'Content': {'string': 'xiaomaochong:\n<msg><voicemsg endflag="1" cancelflag="0" forwardflag="0" voiceformat="4" voicelength="1000" length="9587" bufid="0" aeskey="6d677a6a74617a71626f6f6470656c75" voiceurl="3052020100044b304902010002049363814102033d14ba02046437949d0204687df022042431663063306534612d343033632d346436632d626633332d30393461343331383465333502040528000f02010004001dc74187" voicemd5="a4f8ae7ef2ac2fed2f9c83f092d58251" clientmsgid="41386366366231333863396431366400381545072125d67e8f934d4100" fromusername="xiaomaochong" /></msg>'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1753083938, 'MsgSource': '<msgsource>\n\t<silence>0</silence>\n\t<membercount>62</membercount>\n\t<signature>N0_V1_bLHPUvOT|v1_AB4Xqf8R</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'PushContent': '小爱在群聊中发了一段语音', 'NewMsgId': 7012421899377048168, 'MsgSeq': 871387672}
2025-07-21 15:45:29 | INFO | 收到语音消息: 消息ID:1576765006 来自:48097389945@chatroom 发送人:xiaomaochong XML:
<msg><voicemsg endflag="1" cancelflag="0" forwardflag="0" voiceformat="4" voicelength="1000" length="9587" bufid="0" aeskey="6d677a6a74617a71626f6f6470656c75" voiceurl="3052020100044b304902010002049363814102033d14ba02046437949d0204687df022042431663063306534612d343033632d346436632d626633332d30393461343331383465333502040528000f02010004001dc74187" voicemd5="a4f8ae7ef2ac2fed2f9c83f092d58251" clientmsgid="41386366366231333863396431366400381545072125d67e8f934d4100" fromusername="xiaomaochong" /></msg>
2025-07-21 15:45:33 | DEBUG | 收到消息: {'MsgId': 884589057, 'FromUserName': {'string': '48097389945@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 1, 'Content': {'string': 'wxid_q35rkzgkjvlv12:\n女生蓝色衬衣可以搭配多种颜色的裤子，以下是几种常见的搭配建议：\n\n1. 白色裤子：蓝色和白色是经典搭配，清新、简洁，适合春夏季节。\n2. 黑色裤子：黑色与蓝色搭配显得稳重、干练，适合商务或正式场合。\n3. 灰色裤子：蓝色和灰色搭配在一起很高级，可以营造出一种简约而不失格调的造型。\n4. 牛仔裤：牛仔裤是百搭单品，蓝色衬衣搭配牛仔裤可以营造出休闲、随姓的感觉。\n5. 红色或粉色裤子：这些鲜艳的颜色可以为蓝色衬衣增添一些亮点和活力，使整体造型更加醒目。\n\n当然，具体的搭配还需要考虑个人的气质、喜好和场合等因素。总之，选择合适的颜色搭配可以让你的造型更加出彩。'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1753083942, 'MsgSource': '<msgsource>\n\t<silence>0</silence>\n\t<membercount>62</membercount>\n\t<signature>N0_V1_pFVtHOVB|v1_ZcNLfEsn</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'PushContent': '摇月 : 女生蓝色衬衣可以搭配多种颜色的裤子，以下是几种常见的搭配建议...', 'NewMsgId': 3367633379683128381, 'MsgSeq': 871387673}
2025-07-21 15:45:33 | INFO | 收到文本消息: 消息ID:884589057 来自:48097389945@chatroom 发送人:wxid_q35rkzgkjvlv12 @:[] 内容:女生蓝色衬衣可以搭配多种颜色的裤子，以下是几种常见的搭配建议：

1. 白色裤子：蓝色和白色是经典搭配，清新、简洁，适合春夏季节。
2. 黑色裤子：黑色与蓝色搭配显得稳重、干练，适合商务或正式场合。
3. 灰色裤子：蓝色和灰色搭配在一起很高级，可以营造出一种简约而不失格调的造型。
4. 牛仔裤：牛仔裤是百搭单品，蓝色衬衣搭配牛仔裤可以营造出休闲、随姓的感觉。
5. 红色或粉色裤子：这些鲜艳的颜色可以为蓝色衬衣增添一些亮点和活力，使整体造型更加醒目。

当然，具体的搭配还需要考虑个人的气质、喜好和场合等因素。总之，选择合适的颜色搭配可以让你的造型更加出彩。
2025-07-21 15:45:33 | DEBUG | 处理消息内容: '女生蓝色衬衣可以搭配多种颜色的裤子，以下是几种常见的搭配建议：

1. 白色裤子：蓝色和白色是经典搭配，清新、简洁，适合春夏季节。
2. 黑色裤子：黑色与蓝色搭配显得稳重、干练，适合商务或正式场合。
3. 灰色裤子：蓝色和灰色搭配在一起很高级，可以营造出一种简约而不失格调的造型。
4. 牛仔裤：牛仔裤是百搭单品，蓝色衬衣搭配牛仔裤可以营造出休闲、随姓的感觉。
5. 红色或粉色裤子：这些鲜艳的颜色可以为蓝色衬衣增添一些亮点和活力，使整体造型更加醒目。

当然，具体的搭配还需要考虑个人的气质、喜好和场合等因素。总之，选择合适的颜色搭配可以让你的造型更加出彩。'
2025-07-21 15:45:33 | DEBUG | 消息内容 '女生蓝色衬衣可以搭配多种颜色的裤子，以下是几种常见的搭配建议：

1. 白色裤子：蓝色和白色是经典搭配，清新、简洁，适合春夏季节。
2. 黑色裤子：黑色与蓝色搭配显得稳重、干练，适合商务或正式场合。
3. 灰色裤子：蓝色和灰色搭配在一起很高级，可以营造出一种简约而不失格调的造型。
4. 牛仔裤：牛仔裤是百搭单品，蓝色衬衣搭配牛仔裤可以营造出休闲、随姓的感觉。
5. 红色或粉色裤子：这些鲜艳的颜色可以为蓝色衬衣增添一些亮点和活力，使整体造型更加醒目。

当然，具体的搭配还需要考虑个人的气质、喜好和场合等因素。总之，选择合适的颜色搭配可以让你的造型更加出彩。' 不匹配任何命令，忽略
2025-07-21 15:45:38 | DEBUG | 收到消息: {'MsgId': 808533292, 'FromUserName': {'string': '48097389945@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 1, 'Content': {'string': 'zll953369865:\n@喵小叶\u2005你去欺负亮老板啊'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1753083947, 'MsgSource': '<msgsource>\n\t<atuserlist><![CDATA[,wxid_84mmq4cu7ita22]]></atuserlist>\n\t<pua>1</pua>\n\t<silence>0</silence>\n\t<membercount>62</membercount>\n\t<signature>N0_V1_rvzL/PIe|v1_1WPx+jJV</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'PushContent': '【微信红包】收到一个微信红包 : @喵小叶\u2005你去欺负亮老板啊', 'NewMsgId': 647699095125148035, 'MsgSeq': 871387674}
2025-07-21 15:45:38 | INFO | 收到文本消息: 消息ID:808533292 来自:48097389945@chatroom 发送人:zll953369865 @:['wxid_84mmq4cu7ita22'] 内容:@喵小叶 你去欺负亮老板啊
2025-07-21 15:45:38 | DEBUG | 处理消息内容: '@喵小叶 你去欺负亮老板啊'
2025-07-21 15:45:38 | DEBUG | 消息内容 '@喵小叶 你去欺负亮老板啊' 不匹配任何命令，忽略
2025-07-21 15:45:40 | DEBUG | 收到消息: {'MsgId': 312171921, 'FromUserName': {'string': '55878994168@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 49, 'Content': {'string': 'wxid_ubbh6q832tcs21:\n<?xml version="1.0"?>\n<msg>\n\t<appmsg appid="" sdkver="0">\n\t\t<title>美图 换成真人形象</title>\n\t\t<des />\n\t\t<username />\n\t\t<action>view</action>\n\t\t<type>57</type>\n\t\t<showtype>0</showtype>\n\t\t<content />\n\t\t<url />\n\t\t<lowurl />\n\t\t<forwardflag>0</forwardflag>\n\t\t<dataurl />\n\t\t<lowdataurl />\n\t\t<contentattr>0</contentattr>\n\t\t<streamvideo>\n\t\t\t<streamvideourl />\n\t\t\t<streamvideototaltime>0</streamvideototaltime>\n\t\t\t<streamvideotitle />\n\t\t\t<streamvideowording />\n\t\t\t<streamvideoweburl />\n\t\t\t<streamvideothumburl />\n\t\t\t<streamvideoaduxinfo />\n\t\t\t<streamvideopublishid />\n\t\t</streamvideo>\n\t\t<canvasPageItem>\n\t\t\t<canvasPageXml><![CDATA[]]></canvasPageXml>\n\t\t</canvasPageItem>\n\t\t<refermsg>\n\t\t\t<type>3</type>\n\t\t\t<svrid>5599849422073580249</svrid>\n\t\t\t<fromusr>55878994168@chatroom</fromusr>\n\t\t\t<chatusr>wxid_4usgcju5ey9q29</chatusr>\n\t\t\t<displayname>瑶瑶</displayname>\n\t\t\t<msgsource>&lt;msgsource&gt;\n\t&lt;sec_msg_node&gt;\n\t\t&lt;uuid&gt;9dfebe9dedce03c44b01630ce4eec64f_&lt;/uuid&gt;\n\t\t&lt;risk-file-flag /&gt;\n\t\t&lt;risk-file-md5-list /&gt;\n\t&lt;/sec_msg_node&gt;\n\t&lt;imgmsg_pd cdnmidimgurl_size="33318" cdnmidimgurl_pd_pri="50" cdnmidimgurl_pd="1" /&gt;\n\t&lt;silence&gt;1&lt;/silence&gt;\n\t&lt;membercount&gt;3&lt;/membercount&gt;\n\t&lt;NotAutoDownloadRange&gt;20:00-22:00;00:00-01:00&lt;/NotAutoDownloadRange&gt;\n\t&lt;signature&gt;N0_V1_c8kj2nMe|v1_nvJFSTl5&lt;/signature&gt;\n\t&lt;tmp_node&gt;\n\t\t&lt;publisher-id&gt;&lt;/publisher-id&gt;\n\t&lt;/tmp_node&gt;\n&lt;/msgsource&gt;\n</msgsource>\n\t\t\t<content>&lt;?xml version="1.0"?&gt;\n&lt;msg&gt;\n\t&lt;img aeskey="6c706a667764666c617067717163627a" encryver="0" cdnthumbaeskey="6c706a667764666c617067717163627a" cdnthumburl="3057020100044b30490201000204ec35623a02033d11fe0204f68350700204687de6eb042430303561333734322d663166622d343338352d613965612d6135666133643266363531380204052428010201000405004c537600cc39404d" cdnthumblength="3559" cdnthumbheight="100" cdnthumbwidth="100" cdnmidheight="0" cdnmidwidth="0" cdnhdheight="0" cdnhdwidth="0" cdnmidimgurl="3057020100044b30490201000204ec35623a02033d11fe0204f68350700204687de6eb042430303561333734322d663166622d343338352d613965612d6135666133643266363531380204052428010201000405004c537600cc39404d" length="33318" cdnbigimgurl="3057020100044b30490201000204ec35623a02033d11fe0204f68350700204687de6eb042430303561333734322d663166622d343338352d613965612d6135666133643266363531380204052428010201000405004c537600cc39404d" hdlength="911463" md5="4a3c60a49d88ed13849d05d1f3907608"&gt;\n\t\t&lt;secHashInfoBase64 /&gt;\n\t\t&lt;live&gt;\n\t\t\t&lt;duration&gt;0&lt;/duration&gt;\n\t\t\t&lt;size&gt;0&lt;/size&gt;\n\t\t\t&lt;md5 /&gt;\n\t\t\t&lt;fileid /&gt;\n\t\t\t&lt;hdsize&gt;0&lt;/hdsize&gt;\n\t\t\t&lt;hdmd5 /&gt;\n\t\t\t&lt;hdfileid /&gt;\n\t\t\t&lt;stillimagetimems&gt;0&lt;/stillimagetimems&gt;\n\t\t&lt;/live&gt;\n\t&lt;/img&gt;\n\t&lt;platform_signature /&gt;\n\t&lt;imgdatahash /&gt;\n\t&lt;ImgSourceInfo&gt;\n\t\t&lt;ImgSourceUrl /&gt;\n\t\t&lt;BizType&gt;0&lt;/BizType&gt;\n\t&lt;/ImgSourceInfo&gt;\n&lt;/msg&gt;\n</content>\n\t\t\t<strid />\n\t\t\t<createtime>1753081578</createtime>\n\t\t</refermsg>\n\t\t<appattach>\n\t\t\t<totallen>0</totallen>\n\t\t\t<attachid />\n\t\t\t<cdnattachurl />\n\t\t\t<emoticonmd5 />\n\t\t\t<aeskey />\n\t\t\t<fileext />\n\t\t\t<islargefilemsg>0</islargefilemsg>\n\t\t</appattach>\n\t\t<extinfo />\n\t\t<androidsource>0</androidsource>\n\t\t<thumburl />\n\t\t<mediatagname />\n\t\t<messageaction><![CDATA[]]></messageaction>\n\t\t<messageext><![CDATA[]]></messageext>\n\t\t<emoticongift>\n\t\t\t<packageflag>0</packageflag>\n\t\t\t<packageid />\n\t\t</emoticongift>\n\t\t<emoticonshared>\n\t\t\t<packageflag>0</packageflag>\n\t\t\t<packageid />\n\t\t</emoticonshared>\n\t\t<designershared>\n\t\t\t<designeruin>0</designeruin>\n\t\t\t<designername>null</designername>\n\t\t\t<designerrediretcturl><![CDATA[null]]></designerrediretcturl>\n\t\t</designershared>\n\t\t<emotionpageshared>\n\t\t\t<tid>0</tid>\n\t\t\t<title>null</title>\n\t\t\t<desc>null</desc>\n\t\t\t<iconUrl><![CDATA[null]]></iconUrl>\n\t\t\t<secondUrl>null</secondUrl>\n\t\t\t<pageType>0</pageType>\n\t\t\t<setKey>null</setKey>\n\t\t</emotionpageshared>\n\t\t<webviewshared>\n\t\t\t<shareUrlOriginal />\n\t\t\t<shareUrlOpen />\n\t\t\t<jsAppId />\n\t\t\t<publisherId />\n\t\t\t<publisherReqId />\n\t\t</webviewshared>\n\t\t<template_id />\n\t\t<md5 />\n\t\t<websearch>\n\t\t\t<rec_category>0</rec_category>\n\t\t\t<channelId>0</channelId>\n\t\t</websearch>\n\t\t<weappinfo>\n\t\t\t<username />\n\t\t\t<appid />\n\t\t\t<appservicetype>0</appservicetype>\n\t\t\t<secflagforsinglepagemode>0</secflagforsinglepagemode>\n\t\t\t<videopageinfo>\n\t\t\t\t<thumbwidth>0</thumbwidth>\n\t\t\t\t<thumbheight>0</thumbheight>\n\t\t\t\t<fromopensdk>0</fromopensdk>\n\t\t\t</videopageinfo>\n\t\t</weappinfo>\n\t\t<statextstr />\n\t\t<musicShareItem>\n\t\t\t<musicDuration>0</musicDuration>\n\t\t</musicShareItem>\n\t\t<finderLiveProductShare>\n\t\t\t<finderLiveID><![CDATA[]]></finderLiveID>\n\t\t\t<finderUsername><![CDATA[]]></finderUsername>\n\t\t\t<finderObjectID><![CDATA[]]></finderObjectID>\n\t\t\t<finderNonceID><![CDATA[]]></finderNonceID>\n\t\t\t<liveStatus><![CDATA[]]></liveStatus>\n\t\t\t<appId><![CDATA[]]></appId>\n\t\t\t<pagePath><![CDATA[]]></pagePath>\n\t\t\t<productId><![CDATA[]]></productId>\n\t\t\t<coverUrl><![CDATA[]]></coverUrl>\n\t\t\t<productTitle><![CDATA[]]></productTitle>\n\t\t\t<marketPrice><![CDATA[0]]></marketPrice>\n\t\t\t<sellingPrice><![CDATA[0]]></sellingPrice>\n\t\t\t<platformHeadImg><![CDATA[]]></platformHeadImg>\n\t\t\t<platformName><![CDATA[]]></platformName>\n\t\t\t<shopWindowId><![CDATA[]]></shopWindowId>\n\t\t\t<flashSalePrice><![CDATA[0]]></flashSalePrice>\n\t\t\t<flashSaleEndTime><![CDATA[0]]></flashSaleEndTime>\n\t\t\t<ecSource><![CDATA[]]></ecSource>\n\t\t\t<sellingPriceWording><![CDATA[]]></sellingPriceWording>\n\t\t\t<platformIconURL><![CDATA[]]></platformIconURL>\n\t\t\t<firstProductTagURL><![CDATA[]]></firstProductTagURL>\n\t\t\t<firstProductTagAspectRatioString><![CDATA[0.0]]></firstProductTagAspectRatioString>\n\t\t\t<secondProductTagURL><![CDATA[]]></secondProductTagURL>\n\t\t\t<secondProductTagAspectRatioString><![CDATA[0.0]]></secondProductTagAspectRatioString>\n\t\t\t<firstGuaranteeWording><![CDATA[]]></firstGuaranteeWording>\n\t\t\t<secondGuaranteeWording><![CDATA[]]></secondGuaranteeWording>\n\t\t\t<thirdGuaranteeWording><![CDATA[]]></thirdGuaranteeWording>\n\t\t\t<isPriceBeginShow>false</isPriceBeginShow>\n\t\t\t<lastGMsgID><![CDATA[]]></lastGMsgID>\n\t\t\t<promoterKey><![CDATA[]]></promoterKey>\n\t\t\t<discountWording><![CDATA[]]></discountWording>\n\t\t\t<priceSuffixDescription><![CDATA[]]></priceSuffixDescription>\n\t\t\t<productCardKey><![CDATA[]]></productCardKey>\n\t\t\t<isWxShop><![CDATA[]]></isWxShop>\n\t\t\t<brandIconUrl><![CDATA[]]></brandIconUrl>\n\t\t\t<showBoxItemStringList />\n\t\t</finderLiveProductShare>\n\t\t<finderOrder>\n\t\t\t<appID><![CDATA[]]></appID>\n\t\t\t<orderID><![CDATA[]]></orderID>\n\t\t\t<path><![CDATA[]]></path>\n\t\t\t<priceWording><![CDATA[]]></priceWording>\n\t\t\t<stateWording><![CDATA[]]></stateWording>\n\t\t\t<productImageURL><![CDATA[]]></productImageURL>\n\t\t\t<products><![CDATA[]]></products>\n\t\t\t<productsCount><![CDATA[0]]></productsCount>\n\t\t\t<orderType><![CDATA[0]]></orderType>\n\t\t\t<newPriceWording><![CDATA[]]></newPriceWording>\n\t\t\t<newStateWording><![CDATA[]]></newStateWording>\n\t\t\t<useNewWording><![CDATA[0]]></useNewWording>\n\t\t</finderOrder>\n\t\t<finderShopWindowShare>\n\t\t\t<finderUsername><![CDATA[]]></finderUsername>\n\t\t\t<avatar><![CDATA[]]></avatar>\n\t\t\t<nickname><![CDATA[]]></nickname>\n\t\t\t<commodityInStockCount><![CDATA[]]></commodityInStockCount>\n\t\t\t<appId><![CDATA[]]></appId>\n\t\t\t<path><![CDATA[]]></path>\n\t\t\t<appUsername><![CDATA[]]></appUsername>\n\t\t\t<query><![CDATA[]]></query>\n\t\t\t<liteAppId><![CDATA[]]></liteAppId>\n\t\t\t<liteAppPath><![CDATA[]]></liteAppPath>\n\t\t\t<liteAppQuery><![CDATA[]]></liteAppQuery>\n\t\t\t<platformTagURL><![CDATA[]]></platformTagURL>\n\t\t\t<saleWording><![CDATA[]]></saleWording>\n\t\t\t<lastGMsgID><![CDATA[]]></lastGMsgID>\n\t\t\t<profileTypeWording><![CDATA[]]></profileTypeWording>\n\t\t\t<saleWordingExtra><![CDATA[]]></saleWordingExtra>\n\t\t\t<isWxShop><![CDATA[]]></isWxShop>\n\t\t\t<platformIconUrl><![CDATA[]]></platformIconUrl>\n\t\t\t<brandIconUrl><![CDATA[]]></brandIconUrl>\n\t\t\t<description><![CDATA[]]></description>\n\t\t\t<backgroundUrl><![CDATA[]]></backgroundUrl>\n\t\t\t<darkModePlatformIconUrl><![CDATA[]]></darkModePlatformIconUrl>\n\t\t\t<reputationInfo>\n\t\t\t\t<hasReputationInfo>0</hasReputationInfo>\n\t\t\t\t<reputationScore>0</reputationScore>\n\t\t\t\t<reputationWording />\n\t\t\t\t<reputationTextColor />\n\t\t\t\t<reputationLevelWording />\n\t\t\t\t<reputationBackgroundColor />\n\t\t\t</reputationInfo>\n\t\t\t<productImageURLList />\n\t\t</finderShopWindowShare>\n\t\t<findernamecard>\n\t\t\t<username />\n\t\t\t<avatar><![CDATA[]]></avatar>\n\t\t\t<nickname />\n\t\t\t<auth_job />\n\t\t\t<auth_icon>0</auth_icon>\n\t\t\t<auth_icon_url />\n\t\t\t<ecSource><![CDATA[]]></ecSource>\n\t\t\t<lastGMsgID><![CDATA[]]></lastGMsgID>\n\t\t</findernamecard>\n\t\t<finderGuarantee>\n\t\t\t<scene><![CDATA[0]]></scene>\n\t\t</finderGuarantee>\n\t\t<directshare>0</directshare>\n\t\t<gamecenter>\n\t\t\t<namecard>\n\t\t\t\t<iconUrl />\n\t\t\t\t<name />\n\t\t\t\t<desc />\n\t\t\t\t<tail />\n\t\t\t\t<jumpUrl />\n\t\t\t</namecard>\n\t\t</gamecenter>\n\t\t<patMsg>\n\t\t\t<chatUser />\n\t\t\t<records>\n\t\t\t\t<recordNum>0</recordNum>\n\t\t\t</records>\n\t\t</patMsg>\n\t\t<secretmsg>\n\t\t\t<issecretmsg>0</issecretmsg>\n\t\t</secretmsg>\n\t\t<referfromscene>0</referfromscene>\n\t\t<gameshare>\n\t\t\t<liteappext>\n\t\t\t\t<liteappbizdata />\n\t\t\t\t<priority>0</priority>\n\t\t\t</liteappext>\n\t\t\t<appbrandext>\n\t\t\t\t<litegameinfo />\n\t\t\t\t<priority>-1</priority>\n\t\t\t</appbrandext>\n\t\t\t<gameshareid />\n\t\t\t<sharedata />\n\t\t\t<isvideo>0</isvideo>\n\t\t\t<duration>-1</duration>\n\t\t\t<isexposed>0</isexposed>\n\t\t\t<readtext />\n\t\t</gameshare>\n\t\t<mpsharetrace>\n\t\t\t<hasfinderelement>0</hasfinderelement>\n\t\t\t<lastgmsgid />\n\t\t</mpsharetrace>\n\t\t<wxgamecard>\n\t\t\t<framesetname />\n\t\t\t<mbcarddata />\n\t\t\t<minpkgversion />\n\t\t\t<clientextinfo />\n\t\t\t<mbcardheight>0</mbcardheight>\n\t\t\t<isoldversion>0</isoldversion>\n\t\t</wxgamecard>\n\t\t<liteapp>\n\t\t\t<id>null</id>\n\t\t\t<path />\n\t\t\t<query />\n\t\t\t<istransparent>0</istransparent>\n\t\t\t<hideicon>0</hideicon>\n\t\t</liteapp>\n\t\t<opensdk_share_is_modified>0</opensdk_share_is_modified>\n\t</appmsg>\n\t<fromusername>wxid_ubbh6q832tcs21</fromusername>\n\t<scene>0</scene>\n\t<appinfo>\n\t\t<version>1</version>\n\t\t<appname></appname>\n\t</appinfo>\n\t<commenturl></commenturl>\n</msg>\n'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1753083949, 'MsgSource': '<msgsource>\n\t<alnode>\n\t\t<fr>4</fr>\n\t</alnode>\n\t<eggIncluded>1</eggIncluded>\n\t<sec_msg_node>\n\t\t<uuid>9dfebe9dedce03c44b01630ce4eec64f_</uuid>\n\t\t<risk-file-flag />\n\t\t<risk-file-md5-list />\n\t</sec_msg_node>\n\t<silence>0</silence>\n\t<membercount>3</membercount>\n\t<signature>N0_V1_cTR/XN2y|v1_HntcTqwT</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'PushContent': '郭子 : 美图 换成真人形象', 'NewMsgId': 6938959835603993601, 'MsgSeq': 871387675}
2025-07-21 15:45:40 | DEBUG | 从群聊消息中提取发送者: wxid_ubbh6q832tcs21
2025-07-21 15:45:40 | DEBUG | 使用已解析的XML处理引用消息
2025-07-21 15:45:40 | INFO | 收到引用消息: 消息ID:312171921 来自:55878994168@chatroom 发送人:wxid_ubbh6q832tcs21 内容:美图 换成真人形象 引用类型:3
2025-07-21 15:45:41 | INFO | [DouBaoImageToImage] 收到引用消息: 美图 换成真人形象
2025-07-21 15:45:41 | INFO | 发送文字消息: 对方wxid:55878994168@chatroom at: 内容:嗯
2025-07-21 15:45:41 | INFO | [RoboNeo] 开始处理图像生成: 用户=wxid_ubbh6q832tcs21, 提示词=换成真人形象, 图片=temp\roboneo\quoted_image_1753083941.jpg
2025-07-21 15:45:43 | INFO | [RoboNeo] 图片上传成功: https://roboneo-private.meitudata.com/roboneo/687df03021836m5uovzsco9170.jpg?k=c90858751b4a5084254a464354059f95&t=687e0c50
2025-07-21 15:45:44 | INFO | [RoboNeo] 获得Task ID: B2C516C4D3714F438C8E1929121F5E92
2025-07-21 15:46:21 | INFO | [RoboNeo] 生成进度: 0%
2025-07-21 15:46:21 | INFO | [RoboNeo] 生成进度: 0%
2025-07-21 15:46:30 | INFO | [RoboNeo] 生成进度: 20%
2025-07-21 15:46:50 | INFO | [RoboNeo] 生成进度: 40%
2025-07-21 15:47:07 | INFO | [RoboNeo] 生成进度: 60%
2025-07-21 15:47:24 | INFO | [RoboNeo] 生成进度: 100%
2025-07-21 15:47:24 | INFO | [RoboNeo] 找到生成图片: https://roboneo-private.meitudata.com/roboneo/convert_save/3087ef0ca9194514abc1ebbd7d6940a2.png?k=d62664c273c6677af121967cf4dd8d16&t=687fb57f
2025-07-21 15:47:24 | INFO | [RoboNeo] AI回复内容:
2025-07-21 15:47:24 | INFO |   好的，我这就把图片换成真人形象，让我构思一下。
2025-07-21 15:47:24 | INFO |   正在分析图片中的人物形象和风格特征...
2025-07-21 15:47:24 | INFO |   正在将动漫风格的人物转换为真人形象...
2025-07-21 15:47:56 | INFO | 发送图片消息: 对方wxid:55878994168@chatroom 图片base64略
2025-07-21 15:47:56 | INFO | [RoboNeo] 图片发送成功
2025-07-21 15:47:56 | INFO | [TimerTask] 收到引用消息调试信息:
2025-07-21 15:47:56 | INFO |   - 消息内容: 美图 换成真人形象
2025-07-21 15:47:56 | INFO |   - 群组ID: 55878994168@chatroom
2025-07-21 15:47:56 | INFO |   - 发送人: wxid_ubbh6q832tcs21
2025-07-21 15:47:56 | INFO |   - 引用信息: {'MsgType': 3, 'Content': '<?xml version="1.0"?>\n<msg>\n\t<appmsg appid="" sdkver="0">\n\t\t<title>美图 换成真人形象</title>\n\t\t<des />\n\t\t<username />\n\t\t<action>view</action>\n\t\t<type>57</type>\n\t\t<showtype>0</showtype>\n\t\t<content />\n\t\t<url />\n\t\t<lowurl />\n\t\t<forwardflag>0</forwardflag>\n\t\t<dataurl />\n\t\t<lowdataurl />\n\t\t<contentattr>0</contentattr>\n\t\t<streamvideo>\n\t\t\t<streamvideourl />\n\t\t\t<streamvideototaltime>0</streamvideototaltime>\n\t\t\t<streamvideotitle />\n\t\t\t<streamvideowording />\n\t\t\t<streamvideoweburl />\n\t\t\t<streamvideothumburl />\n\t\t\t<streamvideoaduxinfo />\n\t\t\t<streamvideopublishid />\n\t\t</streamvideo>\n\t\t<canvasPageItem>\n\t\t\t<canvasPageXml><![CDATA[]]></canvasPageXml>\n\t\t</canvasPageItem>\n\t\t<refermsg>\n\t\t\t<type>3</type>\n\t\t\t<svrid>5599849422073580249</svrid>\n\t\t\t<fromusr>55878994168@chatroom</fromusr>\n\t\t\t<chatusr>wxid_4usgcju5ey9q29</chatusr>\n\t\t\t<displayname>瑶瑶</displayname>\n\t\t\t<msgsource>&lt;msgsource&gt;\n\t&lt;sec_msg_node&gt;\n\t\t&lt;uuid&gt;9dfebe9dedce03c44b01630ce4eec64f_&lt;/uuid&gt;\n\t\t&lt;risk-file-flag /&gt;\n\t\t&lt;risk-file-md5-list /&gt;\n\t&lt;/sec_msg_node&gt;\n\t&lt;imgmsg_pd cdnmidimgurl_size="33318" cdnmidimgurl_pd_pri="50" cdnmidimgurl_pd="1" /&gt;\n\t&lt;silence&gt;1&lt;/silence&gt;\n\t&lt;membercount&gt;3&lt;/membercount&gt;\n\t&lt;NotAutoDownloadRange&gt;20:00-22:00;00:00-01:00&lt;/NotAutoDownloadRange&gt;\n\t&lt;signature&gt;N0_V1_c8kj2nMe|v1_nvJFSTl5&lt;/signature&gt;\n\t&lt;tmp_node&gt;\n\t\t&lt;publisher-id&gt;&lt;/publisher-id&gt;\n\t&lt;/tmp_node&gt;\n&lt;/msgsource&gt;\n</msgsource>\n\t\t\t<content>&lt;?xml version="1.0"?&gt;\n&lt;msg&gt;\n\t&lt;img aeskey="6c706a667764666c617067717163627a" encryver="0" cdnthumbaeskey="6c706a667764666c617067717163627a" cdnthumburl="3057020100044b30490201000204ec35623a02033d11fe0204f68350700204687de6eb042430303561333734322d663166622d343338352d613965612d6135666133643266363531380204052428010201000405004c537600cc39404d" cdnthumblength="3559" cdnthumbheight="100" cdnthumbwidth="100" cdnmidheight="0" cdnmidwidth="0" cdnhdheight="0" cdnhdwidth="0" cdnmidimgurl="3057020100044b30490201000204ec35623a02033d11fe0204f68350700204687de6eb042430303561333734322d663166622d343338352d613965612d6135666133643266363531380204052428010201000405004c537600cc39404d" length="33318" cdnbigimgurl="3057020100044b30490201000204ec35623a02033d11fe0204f68350700204687de6eb042430303561333734322d663166622d343338352d613965612d6135666133643266363531380204052428010201000405004c537600cc39404d" hdlength="911463" md5="4a3c60a49d88ed13849d05d1f3907608"&gt;\n\t\t&lt;secHashInfoBase64 /&gt;\n\t\t&lt;live&gt;\n\t\t\t&lt;duration&gt;0&lt;/duration&gt;\n\t\t\t&lt;size&gt;0&lt;/size&gt;\n\t\t\t&lt;md5 /&gt;\n\t\t\t&lt;fileid /&gt;\n\t\t\t&lt;hdsize&gt;0&lt;/hdsize&gt;\n\t\t\t&lt;hdmd5 /&gt;\n\t\t\t&lt;hdfileid /&gt;\n\t\t\t&lt;stillimagetimems&gt;0&lt;/stillimagetimems&gt;\n\t\t&lt;/live&gt;\n\t&lt;/img&gt;\n\t&lt;platform_signature /&gt;\n\t&lt;imgdatahash /&gt;\n\t&lt;ImgSourceInfo&gt;\n\t\t&lt;ImgSourceUrl /&gt;\n\t\t&lt;BizType&gt;0&lt;/BizType&gt;\n\t&lt;/ImgSourceInfo&gt;\n&lt;/msg&gt;\n</content>\n\t\t\t<strid />\n\t\t\t<createtime>1753081578</createtime>\n\t\t</refermsg>\n\t\t<appattach>\n\t\t\t<totallen>0</totallen>\n\t\t\t<attachid />\n\t\t\t<cdnattachurl />\n\t\t\t<emoticonmd5 />\n\t\t\t<aeskey />\n\t\t\t<fileext />\n\t\t\t<islargefilemsg>0</islargefilemsg>\n\t\t</appattach>\n\t\t<extinfo />\n\t\t<androidsource>0</androidsource>\n\t\t<thumburl />\n\t\t<mediatagname />\n\t\t<messageaction><![CDATA[]]></messageaction>\n\t\t<messageext><![CDATA[]]></messageext>\n\t\t<emoticongift>\n\t\t\t<packageflag>0</packageflag>\n\t\t\t<packageid />\n\t\t</emoticongift>\n\t\t<emoticonshared>\n\t\t\t<packageflag>0</packageflag>\n\t\t\t<packageid />\n\t\t</emoticonshared>\n\t\t<designershared>\n\t\t\t<designeruin>0</designeruin>\n\t\t\t<designername>null</designername>\n\t\t\t<designerrediretcturl><![CDATA[null]]></designerrediretcturl>\n\t\t</designershared>\n\t\t<emotionpageshared>\n\t\t\t<tid>0</tid>\n\t\t\t<title>null</title>\n\t\t\t<desc>null</desc>\n\t\t\t<iconUrl><![CDATA[null]]></iconUrl>\n\t\t\t<secondUrl>null</secondUrl>\n\t\t\t<pageType>0</pageType>\n\t\t\t<setKey>null</setKey>\n\t\t</emotionpageshared>\n\t\t<webviewshared>\n\t\t\t<shareUrlOriginal />\n\t\t\t<shareUrlOpen />\n\t\t\t<jsAppId />\n\t\t\t<publisherId />\n\t\t\t<publisherReqId />\n\t\t</webviewshared>\n\t\t<template_id />\n\t\t<md5 />\n\t\t<websearch>\n\t\t\t<rec_category>0</rec_category>\n\t\t\t<channelId>0</channelId>\n\t\t</websearch>\n\t\t<weappinfo>\n\t\t\t<username />\n\t\t\t<appid />\n\t\t\t<appservicetype>0</appservicetype>\n\t\t\t<secflagforsinglepagemode>0</secflagforsinglepagemode>\n\t\t\t<videopageinfo>\n\t\t\t\t<thumbwidth>0</thumbwidth>\n\t\t\t\t<thumbheight>0</thumbheight>\n\t\t\t\t<fromopensdk>0</fromopensdk>\n\t\t\t</videopageinfo>\n\t\t</weappinfo>\n\t\t<statextstr />\n\t\t<musicShareItem>\n\t\t\t<musicDuration>0</musicDuration>\n\t\t</musicShareItem>\n\t\t<finderLiveProductShare>\n\t\t\t<finderLiveID><![CDATA[]]></finderLiveID>\n\t\t\t<finderUsername><![CDATA[]]></finderUsername>\n\t\t\t<finderObjectID><![CDATA[]]></finderObjectID>\n\t\t\t<finderNonceID><![CDATA[]]></finderNonceID>\n\t\t\t<liveStatus><![CDATA[]]></liveStatus>\n\t\t\t<appId><![CDATA[]]></appId>\n\t\t\t<pagePath><![CDATA[]]></pagePath>\n\t\t\t<productId><![CDATA[]]></productId>\n\t\t\t<coverUrl><![CDATA[]]></coverUrl>\n\t\t\t<productTitle><![CDATA[]]></productTitle>\n\t\t\t<marketPrice><![CDATA[0]]></marketPrice>\n\t\t\t<sellingPrice><![CDATA[0]]></sellingPrice>\n\t\t\t<platformHeadImg><![CDATA[]]></platformHeadImg>\n\t\t\t<platformName><![CDATA[]]></platformName>\n\t\t\t<shopWindowId><![CDATA[]]></shopWindowId>\n\t\t\t<flashSalePrice><![CDATA[0]]></flashSalePrice>\n\t\t\t<flashSaleEndTime><![CDATA[0]]></flashSaleEndTime>\n\t\t\t<ecSource><![CDATA[]]></ecSource>\n\t\t\t<sellingPriceWording><![CDATA[]]></sellingPriceWording>\n\t\t\t<platformIconURL><![CDATA[]]></platformIconURL>\n\t\t\t<firstProductTagURL><![CDATA[]]></firstProductTagURL>\n\t\t\t<firstProductTagAspectRatioString><![CDATA[0.0]]></firstProductTagAspectRatioString>\n\t\t\t<secondProductTagURL><![CDATA[]]></secondProductTagURL>\n\t\t\t<secondProductTagAspectRatioString><![CDATA[0.0]]></secondProductTagAspectRatioString>\n\t\t\t<firstGuaranteeWording><![CDATA[]]></firstGuaranteeWording>\n\t\t\t<secondGuaranteeWording><![CDATA[]]></secondGuaranteeWording>\n\t\t\t<thirdGuaranteeWording><![CDATA[]]></thirdGuaranteeWording>\n\t\t\t<isPriceBeginShow>false</isPriceBeginShow>\n\t\t\t<lastGMsgID><![CDATA[]]></lastGMsgID>\n\t\t\t<promoterKey><![CDATA[]]></promoterKey>\n\t\t\t<discountWording><![CDATA[]]></discountWording>\n\t\t\t<priceSuffixDescription><![CDATA[]]></priceSuffixDescription>\n\t\t\t<productCardKey><![CDATA[]]></productCardKey>\n\t\t\t<isWxShop><![CDATA[]]></isWxShop>\n\t\t\t<brandIconUrl><![CDATA[]]></brandIconUrl>\n\t\t\t<showBoxItemStringList />\n\t\t</finderLiveProductShare>\n\t\t<finderOrder>\n\t\t\t<appID><![CDATA[]]></appID>\n\t\t\t<orderID><![CDATA[]]></orderID>\n\t\t\t<path><![CDATA[]]></path>\n\t\t\t<priceWording><![CDATA[]]></priceWording>\n\t\t\t<stateWording><![CDATA[]]></stateWording>\n\t\t\t<productImageURL><![CDATA[]]></productImageURL>\n\t\t\t<products><![CDATA[]]></products>\n\t\t\t<productsCount><![CDATA[0]]></productsCount>\n\t\t\t<orderType><![CDATA[0]]></orderType>\n\t\t\t<newPriceWording><![CDATA[]]></newPriceWording>\n\t\t\t<newStateWording><![CDATA[]]></newStateWording>\n\t\t\t<useNewWording><![CDATA[0]]></useNewWording>\n\t\t</finderOrder>\n\t\t<finderShopWindowShare>\n\t\t\t<finderUsername><![CDATA[]]></finderUsername>\n\t\t\t<avatar><![CDATA[]]></avatar>\n\t\t\t<nickname><![CDATA[]]></nickname>\n\t\t\t<commodityInStockCount><![CDATA[]]></commodityInStockCount>\n\t\t\t<appId><![CDATA[]]></appId>\n\t\t\t<path><![CDATA[]]></path>\n\t\t\t<appUsername><![CDATA[]]></appUsername>\n\t\t\t<query><![CDATA[]]></query>\n\t\t\t<liteAppId><![CDATA[]]></liteAppId>\n\t\t\t<liteAppPath><![CDATA[]]></liteAppPath>\n\t\t\t<liteAppQuery><![CDATA[]]></liteAppQuery>\n\t\t\t<platformTagURL><![CDATA[]]></platformTagURL>\n\t\t\t<saleWording><![CDATA[]]></saleWording>\n\t\t\t<lastGMsgID><![CDATA[]]></lastGMsgID>\n\t\t\t<profileTypeWording><![CDATA[]]></profileTypeWording>\n\t\t\t<saleWordingExtra><![CDATA[]]></saleWordingExtra>\n\t\t\t<isWxShop><![CDATA[]]></isWxShop>\n\t\t\t<platformIconUrl><![CDATA[]]></platformIconUrl>\n\t\t\t<brandIconUrl><![CDATA[]]></brandIconUrl>\n\t\t\t<description><![CDATA[]]></description>\n\t\t\t<backgroundUrl><![CDATA[]]></backgroundUrl>\n\t\t\t<darkModePlatformIconUrl><![CDATA[]]></darkModePlatformIconUrl>\n\t\t\t<reputationInfo>\n\t\t\t\t<hasReputationInfo>0</hasReputationInfo>\n\t\t\t\t<reputationScore>0</reputationScore>\n\t\t\t\t<reputationWording />\n\t\t\t\t<reputationTextColor />\n\t\t\t\t<reputationLevelWording />\n\t\t\t\t<reputationBackgroundColor />\n\t\t\t</reputationInfo>\n\t\t\t<productImageURLList />\n\t\t</finderShopWindowShare>\n\t\t<findernamecard>\n\t\t\t<username />\n\t\t\t<avatar><![CDATA[]]></avatar>\n\t\t\t<nickname />\n\t\t\t<auth_job />\n\t\t\t<auth_icon>0</auth_icon>\n\t\t\t<auth_icon_url />\n\t\t\t<ecSource><![CDATA[]]></ecSource>\n\t\t\t<lastGMsgID><![CDATA[]]></lastGMsgID>\n\t\t</findernamecard>\n\t\t<finderGuarantee>\n\t\t\t<scene><![CDATA[0]]></scene>\n\t\t</finderGuarantee>\n\t\t<directshare>0</directshare>\n\t\t<gamecenter>\n\t\t\t<namecard>\n\t\t\t\t<iconUrl />\n\t\t\t\t<name />\n\t\t\t\t<desc />\n\t\t\t\t<tail />\n\t\t\t\t<jumpUrl />\n\t\t\t</namecard>\n\t\t</gamecenter>\n\t\t<patMsg>\n\t\t\t<chatUser />\n\t\t\t<records>\n\t\t\t\t<recordNum>0</recordNum>\n\t\t\t</records>\n\t\t</patMsg>\n\t\t<secretmsg>\n\t\t\t<issecretmsg>0</issecretmsg>\n\t\t</secretmsg>\n\t\t<referfromscene>0</referfromscene>\n\t\t<gameshare>\n\t\t\t<liteappext>\n\t\t\t\t<liteappbizdata />\n\t\t\t\t<priority>0</priority>\n\t\t\t</liteappext>\n\t\t\t<appbrandext>\n\t\t\t\t<litegameinfo />\n\t\t\t\t<priority>-1</priority>\n\t\t\t</appbrandext>\n\t\t\t<gameshareid />\n\t\t\t<sharedata />\n\t\t\t<isvideo>0</isvideo>\n\t\t\t<duration>-1</duration>\n\t\t\t<isexposed>0</isexposed>\n\t\t\t<readtext />\n\t\t</gameshare>\n\t\t<mpsharetrace>\n\t\t\t<hasfinderelement>0</hasfinderelement>\n\t\t\t<lastgmsgid />\n\t\t</mpsharetrace>\n\t\t<wxgamecard>\n\t\t\t<framesetname />\n\t\t\t<mbcarddata />\n\t\t\t<minpkgversion />\n\t\t\t<clientextinfo />\n\t\t\t<mbcardheight>0</mbcardheight>\n\t\t\t<isoldversion>0</isoldversion>\n\t\t</wxgamecard>\n\t\t<liteapp>\n\t\t\t<id>null</id>\n\t\t\t<path />\n\t\t\t<query />\n\t\t\t<istransparent>0</istransparent>\n\t\t\t<hideicon>0</hideicon>\n\t\t</liteapp>\n\t\t<opensdk_share_is_modified>0</opensdk_share_is_modified>\n\t</appmsg>\n\t<fromusername>wxid_ubbh6q832tcs21</fromusername>\n\t<scene>0</scene>\n\t<appinfo>\n\t\t<version>1</version>\n\t\t<appname></appname>\n\t</appinfo>\n\t<commenturl></commenturl>\n</msg>\n', 'Msgid': '5599849422073580249', 'NewMsgId': '5599849422073580249', 'ToWxid': 'wxid_4usgcju5ey9q29', 'FromWxid': '55878994168@chatroom', 'Nickname': '瑶瑶', 'MsgSource': '<msgsource>\n\t<sec_msg_node>\n\t\t<uuid>9dfebe9dedce03c44b01630ce4eec64f_</uuid>\n\t\t<risk-file-flag />\n\t\t<risk-file-md5-list />\n\t</sec_msg_node>\n\t<imgmsg_pd cdnmidimgurl_size="33318" cdnmidimgurl_pd_pri="50" cdnmidimgurl_pd="1" />\n\t<silence>1</silence>\n\t<membercount>3</membercount>\n\t<NotAutoDownloadRange>20:00-22:00;00:00-01:00</NotAutoDownloadRange>\n\t<signature>N0_V1_c8kj2nMe|v1_nvJFSTl5</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'Createtime': '1753081578', 'SenderWxid': 'wxid_ubbh6q832tcs21'}
2025-07-21 15:47:56 | INFO |   - 引用消息ID: 
2025-07-21 15:47:56 | INFO |   - 引用消息类型: 
2025-07-21 15:47:56 | INFO |   - 引用消息内容: <?xml version="1.0"?>
<msg>
	<appmsg appid="" sdkver="0">
		<title>美图 换成真人形象</title>
		<des />
		<username />
		<action>view</action>
		<type>57</type>
		<showtype>0</showtype>
		<content />
		<url />
		<lowurl />
		<forwardflag>0</forwardflag>
		<dataurl />
		<lowdataurl />
		<contentattr>0</contentattr>
		<streamvideo>
			<streamvideourl />
			<streamvideototaltime>0</streamvideototaltime>
			<streamvideotitle />
			<streamvideowording />
			<streamvideoweburl />
			<streamvideothumburl />
			<streamvideoaduxinfo />
			<streamvideopublishid />
		</streamvideo>
		<canvasPageItem>
			<canvasPageXml><![CDATA[]]></canvasPageXml>
		</canvasPageItem>
		<refermsg>
			<type>3</type>
			<svrid>5599849422073580249</svrid>
			<fromusr>55878994168@chatroom</fromusr>
			<chatusr>wxid_4usgcju5ey9q29</chatusr>
			<displayname>瑶瑶</displayname>
			<msgsource>&lt;msgsource&gt;
	&lt;sec_msg_node&gt;
		&lt;uuid&gt;9dfebe9dedce03c44b01630ce4eec64f_&lt;/uuid&gt;
		&lt;risk-file-flag /&gt;
		&lt;risk-file-md5-list /&gt;
	&lt;/sec_msg_node&gt;
	&lt;imgmsg_pd cdnmidimgurl_size="33318" cdnmidimgurl_pd_pri="50" cdnmidimgurl_pd="1" /&gt;
	&lt;silence&gt;1&lt;/silence&gt;
	&lt;membercount&gt;3&lt;/membercount&gt;
	&lt;NotAutoDownloadRange&gt;20:00-22:00;00:00-01:00&lt;/NotAutoDownloadRange&gt;
	&lt;signature&gt;N0_V1_c8kj2nMe|v1_nvJFSTl5&lt;/signature&gt;
	&lt;tmp_node&gt;
		&lt;publisher-id&gt;&lt;/publisher-id&gt;
	&lt;/tmp_node&gt;
&lt;/msgsource&gt;
</msgsource>
			<content>&lt;?xml version="1.0"?&gt;
&lt;msg&gt;
	&lt;img aeskey="6c706a667764666c617067717163627a" encryver="0" cdnthumbaeskey="6c706a667764666c617067717163627a" cdnthumburl="3057020100044b30490201000204ec35623a02033d11fe0204f68350700204687de6eb042430303561333734322d663166622d343338352d613965612d6135666133643266363531380204052428010201000405004c537600cc39404d" cdnthumblength="3559" cdnthumbheight="100" cdnthumbwidth="100" cdnmidheight="0" cdnmidwidth="0" cdnhdheight="0" cdnhdwidth="0" cdnmidimgurl="3057020100044b30490201000204ec35623a02033d11fe0204f68350700204687de6eb042430303561333734322d663166622d343338352d613965612d6135666133643266363531380204052428010201000405004c537600cc39404d" length="33318" cdnbigimgurl="3057020100044b30490201000204ec35623a02033d11fe0204f68350700204687de6eb042430303561333734322d663166622d343338352d613965612d6135666133643266363531380204052428010201000405004c537600cc39404d" hdlength="911463" md5="4a3c60a49d88ed13849d05d1f3907608"&gt;
		&lt;secHashInfoBase64 /&gt;
		&lt;live&gt;
			&lt;duration&gt;0&lt;/duration&gt;
			&lt;size&gt;0&lt;/size&gt;
			&lt;md5 /&gt;
			&lt;fileid /&gt;
			&lt;hdsize&gt;0&lt;/hdsize&gt;
			&lt;hdmd5 /&gt;
			&lt;hdfileid /&gt;
			&lt;stillimagetimems&gt;0&lt;/stillimagetimems&gt;
		&lt;/live&gt;
	&lt;/img&gt;
	&lt;platform_signature /&gt;
	&lt;imgdatahash /&gt;
	&lt;ImgSourceInfo&gt;
		&lt;ImgSourceUrl /&gt;
		&lt;BizType&gt;0&lt;/BizType&gt;
	&lt;/ImgSourceInfo&gt;
&lt;/msg&gt;
</content>
			<strid />
			<createtime>1753081578</createtime>
		</refermsg>
		<appattach>
			<totallen>0</totallen>
			<attachid />
			<cdnattachurl />
			<emoticonmd5 />
			<aeskey />
			<fileext />
			<islargefilemsg>0</islargefilemsg>
		</appattach>
		<extinfo />
		<androidsource>0</androidsource>
		<thumburl />
		<mediatagname />
		<messageaction><![CDATA[]]></messageaction>
		<messageext><![CDATA[]]></messageext>
		<emoticongift>
			<packageflag>0</packageflag>
			<packageid />
		</emoticongift>
		<emoticonshared>
			<packageflag>0</packageflag>
			<packageid />
		</emoticonshared>
		<designershared>
			<designeruin>0</designeruin>
			<designername>null</designername>
			<designerrediretcturl><![CDATA[null]]></designerrediretcturl>
		</designershared>
		<emotionpageshared>
			<tid>0</tid>
			<title>null</title>
			<desc>null</desc>
			<iconUrl><![CDATA[null]]></iconUrl>
			<secondUrl>null</secondUrl>
			<pageType>0</pageType>
			<setKey>null</setKey>
		</emotionpageshared>
		<webviewshared>
			<shareUrlOriginal />
			<shareUrlOpen />
			<jsAppId />
			<publisherId />
			<publisherReqId />
		</webviewshared>
		<template_id />
		<md5 />
		<websearch>
			<rec_category>0</rec_category>
			<channelId>0</channelId>
		</websearch>
		<weappinfo>
			<username />
			<appid />
			<appservicetype>0</appservicetype>
			<secflagforsinglepagemode>0</secflagforsinglepagemode>
			<videopageinfo>
				<thumbwidth>0</thumbwidth>
				<thumbheight>0</thumbheight>
				<fromopensdk>0</fromopensdk>
			</videopageinfo>
		</weappinfo>
		<statextstr />
		<musicShareItem>
			<musicDuration>0</musicDuration>
		</musicShareItem>
		<finderLiveProductShare>
			<finderLiveID><![CDATA[]]></finderLiveID>
			<finderUsername><![CDATA[]]></finderUsername>
			<finderObjectID><![CDATA[]]></finderObjectID>
			<finderNonceID><![CDATA[]]></finderNonceID>
			<liveStatus><![CDATA[]]></liveStatus>
			<appId><![CDATA[]]></appId>
			<pagePath><![CDATA[]]></pagePath>
			<productId><![CDATA[]]></productId>
			<coverUrl><![CDATA[]]></coverUrl>
			<productTitle><![CDATA[]]></productTitle>
			<marketPrice><![CDATA[0]]></marketPrice>
			<sellingPrice><![CDATA[0]]></sellingPrice>
			<platformHeadImg><![CDATA[]]></platformHeadImg>
			<platformName><![CDATA[]]></platformName>
			<shopWindowId><![CDATA[]]></shopWindowId>
			<flashSalePrice><![CDATA[0]]></flashSalePrice>
			<flashSaleEndTime><![CDATA[0]]></flashSaleEndTime>
			<ecSource><![CDATA[]]></ecSource>
			<sellingPriceWording><![CDATA[]]></sellingPriceWording>
			<platformIconURL><![CDATA[]]></platformIconURL>
			<firstProductTagURL><![CDATA[]]></firstProductTagURL>
			<firstProductTagAspectRatioString><![CDATA[0.0]]></firstProductTagAspectRatioString>
			<secondProductTagURL><![CDATA[]]></secondProductTagURL>
			<secondProductTagAspectRatioString><![CDATA[0.0]]></secondProductTagAspectRatioString>
			<firstGuaranteeWording><![CDATA[]]></firstGuaranteeWording>
			<secondGuaranteeWording><![CDATA[]]></secondGuaranteeWording>
			<thirdGuaranteeWording><![CDATA[]]></thirdGuaranteeWording>
			<isPriceBeginShow>false</isPriceBeginShow>
			<lastGMsgID><![CDATA[]]></lastGMsgID>
			<promoterKey><![CDATA[]]></promoterKey>
			<discountWording><![CDATA[]]></discountWording>
			<priceSuffixDescription><![CDATA[]]></priceSuffixDescription>
			<productCardKey><![CDATA[]]></productCardKey>
			<isWxShop><![CDATA[]]></isWxShop>
			<brandIconUrl><![CDATA[]]></brandIconUrl>
			<showBoxItemStringList />
		</finderLiveProductShare>
		<finderOrder>
			<appID><![CDATA[]]></appID>
			<orderID><![CDATA[]]></orderID>
			<path><![CDATA[]]></path>
			<priceWording><![CDATA[]]></priceWording>
			<stateWording><![CDATA[]]></stateWording>
			<productImageURL><![CDATA[]]></productImageURL>
			<products><![CDATA[]]></products>
			<productsCount><![CDATA[0]]></productsCount>
			<orderType><![CDATA[0]]></orderType>
			<newPriceWording><![CDATA[]]></newPriceWording>
			<newStateWording><![CDATA[]]></newStateWording>
			<useNewWording><![CDATA[0]]></useNewWording>
		</finderOrder>
		<finderShopWindowShare>
			<finderUsername><![CDATA[]]></finderUsername>
			<avatar><![CDATA[]]></avatar>
			<nickname><![CDATA[]]></nickname>
			<commodityInStockCount><![CDATA[]]></commodityInStockCount>
			<appId><![CDATA[]]></appId>
			<path><![CDATA[]]></path>
			<appUsername><![CDATA[]]></appUsername>
			<query><![CDATA[]]></query>
			<liteAppId><![CDATA[]]></liteAppId>
			<liteAppPath><![CDATA[]]></liteAppPath>
			<liteAppQuery><![CDATA[]]></liteAppQuery>
			<platformTagURL><![CDATA[]]></platformTagURL>
			<saleWording><![CDATA[]]></saleWording>
			<lastGMsgID><![CDATA[]]></lastGMsgID>
			<profileTypeWording><![CDATA[]]></profileTypeWording>
			<saleWordingExtra><![CDATA[]]></saleWordingExtra>
			<isWxShop><![CDATA[]]></isWxShop>
			<platformIconUrl><![CDATA[]]></platformIconUrl>
			<brandIconUrl><![CDATA[]]></brandIconUrl>
			<description><![CDATA[]]></description>
			<backgroundUrl><![CDATA[]]></backgroundUrl>
			<darkModePlatformIconUrl><![CDATA[]]></darkModePlatformIconUrl>
			<reputationInfo>
				<hasReputationInfo>0</hasReputationInfo>
				<reputationScore>0</reputationScore>
				<reputationWording />
				<reputationTextColor />
				<reputationLevelWording />
				<reputationBackgroundColor />
			</reputationInfo>
			<productImageURLList />
		</finderShopWindowShare>
		<findernamecard>
			<username />
			<avatar><![CDATA[]]></avatar>
			<nickname />
			<auth_job />
			<auth_icon>0</auth_icon>
			<auth_icon_url />
			<ecSource><![CDATA[]]></ecSource>
			<lastGMsgID><![CDATA[]]></lastGMsgID>
		</findernamecard>
		<finderGuarantee>
			<scene><![CDATA[0]]></scene>
		</finderGuarantee>
		<directshare>0</directshare>
		<gamecenter>
			<namecard>
				<iconUrl />
				<name />
				<desc />
				<tail />
				<jumpUrl />
			</namecard>
		</gamecenter>
		<patMsg>
			<chatUser />
			<records>
				<recordNum>0</recordNum>
			</records>
		</patMsg>
		<secretmsg>
			<issecretmsg>0</issecretmsg>
		</secretmsg>
		<referfromscene>0</referfromscene>
		<gameshare>
			<liteappext>
				<liteappbizdata />
				<priority>0</priority>
			</liteappext>
			<appbrandext>
				<litegameinfo />
				<priority>-1</priority>
			</appbrandext>
			<gameshareid />
			<sharedata />
			<isvideo>0</isvideo>
			<duration>-1</duration>
			<isexposed>0</isexposed>
			<readtext />
		</gameshare>
		<mpsharetrace>
			<hasfinderelement>0</hasfinderelement>
			<lastgmsgid />
		</mpsharetrace>
		<wxgamecard>
			<framesetname />
			<mbcarddata />
			<minpkgversion />
			<clientextinfo />
			<mbcardheight>0</mbcardheight>
			<isoldversion>0</isoldversion>
		</wxgamecard>
		<liteapp>
			<id>null</id>
			<path />
			<query />
			<istransparent>0</istransparent>
			<hideicon>0</hideicon>
		</liteapp>
		<opensdk_share_is_modified>0</opensdk_share_is_modified>
	</appmsg>
	<fromusername>wxid_ubbh6q832tcs21</fromusername>
	<scene>0</scene>
	<appinfo>
		<version>1</version>
		<appname></appname>
	</appinfo>
	<commenturl></commenturl>
</msg>

2025-07-21 15:47:56 | INFO |   - 引用消息发送人: wxid_ubbh6q832tcs21
2025-07-21 15:47:56 | DEBUG | 收到消息: {'MsgId': 976934952, 'FromUserName': {'string': '48097389945@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 3, 'Content': {'string': 'wxid_n5c0aekjceu621:\n<?xml version="1.0"?>\n<msg>\n\t<img aeskey="b68e4301cb93d599ea8a0d2488424e68" encryver="1" cdnthumbaeskey="b68e4301cb93d599ea8a0d2488424e68" cdnthumburl="3057020100044b304902010002045f9677f402032dd21502042090f2b60204687df02d042464346132336337372d623030622d343032392d393635322d313765343966613362656131020405250a020201000405004c4dfd00" cdnthumblength="4934" cdnthumbheight="120" cdnthumbwidth="90" cdnmidheight="0" cdnmidwidth="0" cdnhdheight="0" cdnhdwidth="0" cdnmidimgurl="3057020100044b304902010002045f9677f402032dd21502042090f2b60204687df02d042464346132336337372d623030622d343032392d393635322d313765343966613362656131020405250a020201000405004c4dfd00" length="27770" md5="7e3f3c554fc84e2c60c3a6fe8d8ea771" hevc_mid_size="27770" originsourcemd5="f1b2333b74ccc15726fd43c864b9c612">\n\t\t<secHashInfoBase64 />\n\t\t<live>\n\t\t\t<duration>0</duration>\n\t\t\t<size>0</size>\n\t\t\t<md5 />\n\t\t\t<fileid />\n\t\t\t<hdsize>0</hdsize>\n\t\t\t<hdmd5 />\n\t\t\t<hdfileid />\n\t\t\t<stillimagetimems>0</stillimagetimems>\n\t\t</live>\n\t</img>\n\t<platform_signature />\n\t<imgdatahash />\n\t<ImgSourceInfo>\n\t\t<ImgSourceUrl />\n\t\t<BizType>0</BizType>\n\t</ImgSourceInfo>\n</msg>\n'}, 'Status': 3, 'ImgStatus': 2, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1753083950, 'MsgSource': '<msgsource>\n\t<alnode>\n\t\t<fr>2</fr>\n\t</alnode>\n\t<mediaeditcontent>\n\t\t<emojilist>\n\t\t\t<emojiitem>\n\t\t\t\t<md5>6628079f0045611fd6d7400f0f4a78d4</md5>\n\t\t\t</emojiitem>\n\t\t</emojilist>\n\t</mediaeditcontent>\n\t<sec_msg_node>\n\t\t<uuid>99f5d1226c3ff1686d4062e583a45029_</uuid>\n\t\t<risk-file-flag />\n\t\t<risk-file-md5-list />\n\t</sec_msg_node>\n\t<silence>0</silence>\n\t<membercount>62</membercount>\n\t<signature>N0_V1_0farzo5N|v1_u1NBsWlv</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'PushContent': 'ㅤㅤㅤㅤㅤㅤㅤㅤㅤㅤㅤㅤㅤㅤㅤㅤ在群聊中发了一张图片', 'NewMsgId': 5964630131962690839, 'MsgSeq': 871387677}
2025-07-21 15:47:56 | INFO | 收到图片消息: 消息ID:976934952 来自:48097389945@chatroom 发送人:wxid_n5c0aekjceu621 XML:<?xml version="1.0"?><msg><img aeskey="b68e4301cb93d599ea8a0d2488424e68" encryver="1" cdnthumbaeskey="b68e4301cb93d599ea8a0d2488424e68" cdnthumburl="3057020100044b304902010002045f9677f402032dd21502042090f2b60204687df02d042464346132336337372d623030622d343032392d393635322d313765343966613362656131020405250a020201000405004c4dfd00" cdnthumblength="4934" cdnthumbheight="120" cdnthumbwidth="90" cdnmidheight="0" cdnmidwidth="0" cdnhdheight="0" cdnhdwidth="0" cdnmidimgurl="3057020100044b304902010002045f9677f402032dd21502042090f2b60204687df02d042464346132336337372d623030622d343032392d393635322d313765343966613362656131020405250a020201000405004c4dfd00" length="27770" md5="7e3f3c554fc84e2c60c3a6fe8d8ea771" hevc_mid_size="27770" originsourcemd5="f1b2333b74ccc15726fd43c864b9c612"><secHashInfoBase64 /><live><duration>0</duration><size>0</size><md5 /><fileid /><hdsize>0</hdsize><hdmd5 /><hdfileid /><stillimagetimems>0</stillimagetimems></live></img><platform_signature /><imgdatahash /><ImgSourceInfo><ImgSourceUrl /><BizType>0</BizType></ImgSourceInfo></msg>
2025-07-21 15:47:56 | INFO | [ImageEcho] 保存图片信息成功，当前群 48097389945@chatroom 已存储 5 张图片
2025-07-21 15:47:56 | INFO | [TimerTask] 缓存图片消息: 976934952
2025-07-21 15:47:57 | DEBUG | 收到消息: {'MsgId': 756465317, 'FromUserName': {'string': '48097389945@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 49, 'Content': {'string': 'wxid_84mmq4cu7ita22:\n<?xml version="1.0"?>\n<msg>\n\t<appmsg appid="" sdkver="0">\n\t\t<title>那个得小阿姨揍他</title>\n\t\t<type>57</type>\n\t\t<appattach>\n\t\t\t<cdnthumbaeskey />\n\t\t\t<aeskey />\n\t\t</appattach>\n\t\t<refermsg>\n\t\t\t<type>1</type>\n\t\t\t<svrid>647699095125148035</svrid>\n\t\t\t<fromusr>48097389945@chatroom</fromusr>\n\t\t\t<chatusr>zll953369865</chatusr>\n\t\t\t<displayname>【微信红包】收到一个微信红包</displayname>\n\t\t\t<content>@喵小叶\u2005你去欺负亮老板啊</content>\n\t\t\t<msgsource>&lt;msgsource&gt;&lt;sequence_id&gt;821944782&lt;/sequence_id&gt;\n\t&lt;atuserlist&gt;&lt;![CDATA[,wxid_84mmq4cu7ita22]]&gt;&lt;/atuserlist&gt;\n\t&lt;pua&gt;1&lt;/pua&gt;\n\t&lt;silence&gt;1&lt;/silence&gt;\n\t&lt;membercount&gt;62&lt;/membercount&gt;\n\t&lt;signature&gt;N0_V1_Ysqoo0yn|v1_Goa9dF2P&lt;/signature&gt;\n\t&lt;tmp_node&gt;\n\t\t&lt;publisher-id&gt;&lt;/publisher-id&gt;\n\t&lt;/tmp_node&gt;\n&lt;/msgsource&gt;\n</msgsource>\n\t\t\t<createtime>1753083947</createtime>\n\t\t</refermsg>\n\t</appmsg>\n\t<fromusername>wxid_84mmq4cu7ita22</fromusername>\n\t<scene>0</scene>\n\t<appinfo>\n\t\t<version>1</version>\n\t\t<appname></appname>\n\t</appinfo>\n\t<commenturl></commenturl>\n</msg>\n'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1753083965, 'MsgSource': '<msgsource>\n\t<bizflag>0</bizflag>\n\t<pua>1</pua>\n\t<eggIncluded>1</eggIncluded>\n\t<sec_msg_node>\n\t\t<uuid>203ee83de775bef379353aa3afa92957_</uuid>\n\t\t<risk-file-flag />\n\t\t<risk-file-md5-list />\n\t</sec_msg_node>\n\t<silence>0</silence>\n\t<membercount>62</membercount>\n\t<signature>N0_V1_jtr1qe1v|v1_BNueW8Ye</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'PushContent': '喵小叶 : 那个得小阿姨揍他', 'NewMsgId': 1074221891683462268, 'MsgSeq': 871387679}
2025-07-21 15:47:57 | DEBUG | 从群聊消息中提取发送者: wxid_84mmq4cu7ita22
2025-07-21 15:47:57 | DEBUG | 使用已解析的XML处理引用消息
2025-07-21 15:47:57 | INFO | 收到引用消息: 消息ID:756465317 来自:48097389945@chatroom 发送人:wxid_84mmq4cu7ita22 内容:那个得小阿姨揍他 引用类型:1
2025-07-21 15:47:57 | INFO | [DouBaoImageToImage] 收到引用消息: 那个得小阿姨揍他
2025-07-21 15:47:57 | INFO | [TimerTask] 收到引用消息调试信息:
2025-07-21 15:47:57 | INFO |   - 消息内容: 那个得小阿姨揍他
2025-07-21 15:47:57 | INFO |   - 群组ID: 48097389945@chatroom
2025-07-21 15:47:57 | INFO |   - 发送人: wxid_84mmq4cu7ita22
2025-07-21 15:47:57 | INFO |   - 引用信息: {'MsgType': 1, 'Content': '@喵小叶\u2005你去欺负亮老板啊', 'Msgid': '647699095125148035', 'NewMsgId': '647699095125148035', 'ToWxid': 'wxid_4usgcju5ey9q29', 'FromWxid': '48097389945@chatroom', 'Nickname': '【微信红包】收到一个微信红包', 'MsgSource': '<msgsource><sequence_id>821944782</sequence_id>\n\t<atuserlist><![CDATA[,wxid_84mmq4cu7ita22]]></atuserlist>\n\t<pua>1</pua>\n\t<silence>1</silence>\n\t<membercount>62</membercount>\n\t<signature>N0_V1_Ysqoo0yn|v1_Goa9dF2P</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'Createtime': '1753083947', 'SenderWxid': 'wxid_84mmq4cu7ita22'}
2025-07-21 15:47:57 | INFO |   - 引用消息ID: 
2025-07-21 15:47:57 | INFO |   - 引用消息类型: 
2025-07-21 15:47:57 | INFO |   - 引用消息内容: @喵小叶 你去欺负亮老板啊
2025-07-21 15:47:57 | INFO |   - 引用消息发送人: wxid_84mmq4cu7ita22
2025-07-21 15:47:57 | DEBUG | 收到消息: {'MsgId': 1903184661, 'FromUserName': {'string': '48097389945@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 1, 'Content': {'string': 'wxid_n5c0aekjceu621:\n我职场POLO衫'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1753083965, 'MsgSource': '<msgsource>\n\t<pua>1</pua>\n\t<silence>0</silence>\n\t<membercount>62</membercount>\n\t<signature>N0_V1_NRFsAsUQ|v1_x8Y9rt/F</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'PushContent': 'ㅤㅤㅤㅤㅤㅤㅤㅤㅤㅤㅤㅤㅤㅤㅤㅤ : 我职场POLO衫', 'NewMsgId': 5495862464953468765, 'MsgSeq': 871387680}
2025-07-21 15:47:57 | INFO | 收到文本消息: 消息ID:1903184661 来自:48097389945@chatroom 发送人:wxid_n5c0aekjceu621 @:[] 内容:我职场POLO衫
2025-07-21 15:47:58 | DEBUG | 处理消息内容: '我职场POLO衫'
2025-07-21 15:47:58 | DEBUG | 消息内容 '我职场POLO衫' 不匹配任何命令，忽略
2025-07-21 15:47:59 | DEBUG | 收到消息: {'MsgId': 72387224, 'FromUserName': {'string': '48097389945@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 1, 'Content': {'string': 'wxid_84mmq4cu7ita22:\n我掰扯不过'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1753083971, 'MsgSource': '<msgsource>\n\t<bizflag>0</bizflag>\n\t<pua>1</pua>\n\t<eggIncluded>1</eggIncluded>\n\t<silence>0</silence>\n\t<membercount>62</membercount>\n\t<signature>N0_V1_9C4oSHbc|v1_l7qlb6+4</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'PushContent': '喵小叶 : 我掰扯不过', 'NewMsgId': 600792207759873147, 'MsgSeq': 871387681}
2025-07-21 15:47:59 | INFO | 收到文本消息: 消息ID:72387224 来自:48097389945@chatroom 发送人:wxid_84mmq4cu7ita22 @:[] 内容:我掰扯不过
2025-07-21 15:48:00 | DEBUG | 处理消息内容: '我掰扯不过'
2025-07-21 15:48:00 | DEBUG | 消息内容 '我掰扯不过' 不匹配任何命令，忽略
2025-07-21 15:48:02 | DEBUG | 收到消息: {'MsgId': 2115140067, 'FromUserName': {'string': '48097389945@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 47, 'Content': {'string': 'wxid_n5c0aekjceu621:\n<msg><emoji fromusername = "wxid_n5c0aekjceu621" tousername = "48097389945@chatroom" type="2" idbuffer="media:0_0" md5="fc3463d39f1aebeffeb2593410512fde" len = "34802" productid="" androidmd5="fc3463d39f1aebeffeb2593410512fde" androidlen="34802" s60v3md5 = "fc3463d39f1aebeffeb2593410512fde" s60v3len="34802" s60v5md5 = "fc3463d39f1aebeffeb2593410512fde" s60v5len="34802" cdnurl = "http://wxapp.tc.qq.com/262/20304/stodownload?m=fc3463d39f1aebeffeb2593410512fde&amp;filekey=30350201010421301f02020106040253480410fc3463d39f1aebeffeb2593410512fde02030087f2040d00000004627466730000000132&amp;hy=SH&amp;storeid=2632403760009a2f9000000000000010600004f5053482e667b40b7dc8d77f&amp;bizid=1023" designerid = "" thumburl = "" encrypturl = "http://wxapp.tc.qq.com/262/20304/stodownload?m=6d270bb3f107b9b81f96b64444df4899&amp;filekey=30350201010421301f020201060402534804106d270bb3f107b9b81f96b64444df48990203008800040d00000004627466730000000132&amp;hy=SH&amp;storeid=263240376000d510d000000000000010600004f50534801c67b40b7d8554cf&amp;bizid=1023" aeskey= "e43b10ce9ed867575b758c155d555d2a" externurl = "" externmd5 = "" width= "82" height= "82" tpurl= "" tpauthkey= "" attachedtext= "" attachedtextcolor= "" lensid= "" emojiattr= "" linkid= "" desc= "" ></emoji>  </msg>'}, 'Status': 3, 'ImgStatus': 2, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1753083972, 'MsgSource': '<msgsource>\n\t<silence>0</silence>\n\t<membercount>62</membercount>\n\t<signature>N0_V1_6mGj8ndu|v1_Zl2uS+//</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'PushContent': 'ㅤㅤㅤㅤㅤㅤㅤㅤㅤㅤㅤㅤㅤㅤㅤㅤ在群聊中发了一个表情', 'NewMsgId': 6394819757083203046, 'MsgSeq': 871387682}
2025-07-21 15:48:02 | INFO | 收到表情消息: 消息ID:2115140067 来自:48097389945@chatroom 发送人:wxid_n5c0aekjceu621 MD5:fc3463d39f1aebeffeb2593410512fde 大小:34802
2025-07-21 15:48:02 | DEBUG | [DoubaoImageRecognition] 缓存表情消息: 6394819757083203046
2025-07-21 15:48:02 | DEBUG | 收到消息: {'MsgId': 1788245129, 'FromUserName': {'string': '48097389945@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 47, 'Content': {'string': 'last--exile:\n<msg><emoji fromusername="last--exile" tousername="48097389945@chatroom" type="2" idbuffer="media:0_0" md5="36c8c028bc7a1655f319d2e0e9c10d43" len="42642" productid="" androidmd5="36c8c028bc7a1655f319d2e0e9c10d43" androidlen="42642" s60v3md5="36c8c028bc7a1655f319d2e0e9c10d43" s60v3len="42642" s60v5md5="36c8c028bc7a1655f319d2e0e9c10d43" s60v5len="42642" cdnurl="http://vweixinf.tc.qq.com/110/20401/stodownload?m=36c8c028bc7a1655f319d2e0e9c10d43&amp;filekey=30440201010430302e02016e0402535a04203336633863303238626337613136353566333139643265306539633130643433020300a692040d00000004627466730000000131&amp;hy=SZ&amp;storeid=323032313039333032323431353030303034636337313765303062393138383136313838303930303030303036653031303034666231&amp;ef=1&amp;bizid=1022" designerid="" thumburl="" encrypturl="http://vweixinf.tc.qq.com/110/20402/stodownload?m=db9a0a0bc64bec9dc24adf9595cd58b7&amp;filekey=30440201010430302e02016e0402535a04206462396130613062633634626563396463323461646639353935636435386237020300a6a0040d00000004627466730000000131&amp;hy=SZ&amp;storeid=323032313039333032323431353030303035633862633765303062393138383136313838303930303030303036653032303034666232&amp;ef=2&amp;bizid=1022" aeskey="f73cd9d3039e46f5ac4d6d1daedf659f" externurl="http://vweixinf.tc.qq.com/110/20403/stodownload?m=a1494656c4963a81f04fe6200db2fd35&amp;filekey=3043020101042f302d02016e0402535a0420613134393436353663343936336138316630346665363230306462326664333502021400040d00000004627466730000000131&amp;hy=SZ&amp;storeid=323032313039333032323431353030303037396439303765303062393138383136313838303930303030303036653033303034666233&amp;ef=3&amp;bizid=1022" externmd5="056a2064518927f800c6eea91c05ed08" width="240" height="176" tpurl="" tpauthkey="" attachedtext="" attachedtextcolor="" lensid="" emojiattr="" linkid="" desc=""></emoji></msg>'}, 'Status': 3, 'ImgStatus': 2, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1753083973, 'MsgSource': '<msgsource>\n\t<silence>0</silence>\n\t<membercount>62</membercount>\n\t<signature>N0_V1_XwQuU43A|v1_TxTlKN6g</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'PushContent': '亮在群聊中发了一个表情', 'NewMsgId': 2427907594158326967, 'MsgSeq': 871387683}
2025-07-21 15:48:02 | INFO | 收到表情消息: 消息ID:1788245129 来自:48097389945@chatroom 发送人:last--exile MD5:36c8c028bc7a1655f319d2e0e9c10d43 大小:42642
2025-07-21 15:48:03 | DEBUG | [DoubaoImageRecognition] 缓存表情消息: 2427907594158326967
2025-07-21 15:48:03 | DEBUG | 收到消息: {'MsgId': 204281272, 'FromUserName': {'string': '48097389945@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 1, 'Content': {'string': 'wxid_84mmq4cu7ita22:\n你行你上'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1753083977, 'MsgSource': '<msgsource>\n\t<bizflag>0</bizflag>\n\t<pua>1</pua>\n\t<eggIncluded>1</eggIncluded>\n\t<silence>0</silence>\n\t<membercount>62</membercount>\n\t<signature>N0_V1_c6+D1xTy|v1_yCqnFB7h</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'PushContent': '喵小叶 : 你行你上', 'NewMsgId': 2437753850299609834, 'MsgSeq': 871387684}
2025-07-21 15:48:03 | INFO | 收到文本消息: 消息ID:204281272 来自:48097389945@chatroom 发送人:wxid_84mmq4cu7ita22 @:[] 内容:你行你上
2025-07-21 15:48:03 | DEBUG | 处理消息内容: '你行你上'
2025-07-21 15:48:03 | DEBUG | 消息内容 '你行你上' 不匹配任何命令，忽略
2025-07-21 15:48:05 | DEBUG | 收到消息: {'MsgId': 2105084272, 'FromUserName': {'string': '48097389945@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 1, 'Content': {'string': 'wxid_84mmq4cu7ita22:\n[呲牙]'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1753083991, 'MsgSource': '<msgsource>\n\t<bizflag>0</bizflag>\n\t<pua>1</pua>\n\t<eggIncluded>1</eggIncluded>\n\t<silence>0</silence>\n\t<membercount>62</membercount>\n\t<signature>N0_V1_N1GXVJ0f|v1_F4m+QkB2</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'PushContent': '喵小叶 : [呲牙]', 'NewMsgId': 4272733661866370285, 'MsgSeq': 871387685}
2025-07-21 15:48:05 | INFO | 收到表情消息: 消息ID:2105084272 来自:48097389945@chatroom 发送人:wxid_84mmq4cu7ita22 @:[] 内容:[呲牙]
2025-07-21 15:48:05 | DEBUG | [DoubaoImageRecognition] 缓存表情消息: 4272733661866370285
2025-07-21 15:48:06 | DEBUG | 收到消息: {'MsgId': 178253478, 'FromUserName': {'string': '48097389945@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 49, 'Content': {'string': 'wxid_84mmq4cu7ita22:\n<?xml version="1.0"?>\n<msg>\n\t<appmsg appid="" sdkver="0">\n\t\t<title>可以澳</title>\n\t\t<type>57</type>\n\t\t<appattach>\n\t\t\t<cdnthumbaeskey />\n\t\t\t<aeskey />\n\t\t</appattach>\n\t\t<refermsg>\n\t\t\t<type>1</type>\n\t\t\t<svrid>5495862464953468765</svrid>\n\t\t\t<fromusr>48097389945@chatroom</fromusr>\n\t\t\t<chatusr>wxid_n5c0aekjceu621</chatusr>\n\t\t\t<displayname>ㅤㅤㅤㅤㅤㅤㅤㅤㅤㅤㅤㅤㅤㅤㅤㅤ</displayname>\n\t\t\t<content>我职场POLO衫</content>\n\t\t\t<msgsource>&lt;msgsource&gt;&lt;sequence_id&gt;821944788&lt;/sequence_id&gt;\n\t&lt;pua&gt;1&lt;/pua&gt;\n\t&lt;silence&gt;1&lt;/silence&gt;\n\t&lt;membercount&gt;62&lt;/membercount&gt;\n\t&lt;signature&gt;N0_V1_AztBAAkU|v1_o7hOEchW&lt;/signature&gt;\n\t&lt;tmp_node&gt;\n\t\t&lt;publisher-id&gt;&lt;/publisher-id&gt;\n\t&lt;/tmp_node&gt;\n&lt;/msgsource&gt;\n</msgsource>\n\t\t\t<createtime>1753083965</createtime>\n\t\t</refermsg>\n\t</appmsg>\n\t<fromusername>wxid_84mmq4cu7ita22</fromusername>\n\t<scene>0</scene>\n\t<appinfo>\n\t\t<version>1</version>\n\t\t<appname></appname>\n\t</appinfo>\n\t<commenturl></commenturl>\n</msg>\n'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1753084009, 'MsgSource': '<msgsource>\n\t<bizflag>0</bizflag>\n\t<pua>1</pua>\n\t<eggIncluded>1</eggIncluded>\n\t<sec_msg_node>\n\t\t<uuid>28bff2ae5b9ad45e38e6b63d60c53fb5_</uuid>\n\t\t<risk-file-flag />\n\t\t<risk-file-md5-list />\n\t</sec_msg_node>\n\t<silence>0</silence>\n\t<membercount>62</membercount>\n\t<signature>N0_V1_mV+Nez2W|v1_fQ9D8XBK</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'PushContent': '喵小叶 : 可以澳', 'NewMsgId': 4459370675027837386, 'MsgSeq': 871387686}
2025-07-21 15:48:06 | DEBUG | 从群聊消息中提取发送者: wxid_84mmq4cu7ita22
2025-07-21 15:48:06 | DEBUG | 使用已解析的XML处理引用消息
2025-07-21 15:48:06 | INFO | 收到引用消息: 消息ID:178253478 来自:48097389945@chatroom 发送人:wxid_84mmq4cu7ita22 内容:可以澳 引用类型:1
2025-07-21 15:48:06 | INFO | [DouBaoImageToImage] 收到引用消息: 可以澳
2025-07-21 15:48:06 | INFO | [TimerTask] 收到引用消息调试信息:
2025-07-21 15:48:06 | INFO |   - 消息内容: 可以澳
2025-07-21 15:48:06 | INFO |   - 群组ID: 48097389945@chatroom
2025-07-21 15:48:06 | INFO |   - 发送人: wxid_84mmq4cu7ita22
2025-07-21 15:48:06 | INFO |   - 引用信息: {'MsgType': 1, 'Content': '我职场POLO衫', 'Msgid': '5495862464953468765', 'NewMsgId': '5495862464953468765', 'ToWxid': 'wxid_4usgcju5ey9q29', 'FromWxid': '48097389945@chatroom', 'Nickname': 'ㅤㅤㅤㅤㅤㅤㅤㅤㅤㅤㅤㅤㅤㅤㅤㅤ', 'MsgSource': '<msgsource><sequence_id>821944788</sequence_id>\n\t<pua>1</pua>\n\t<silence>1</silence>\n\t<membercount>62</membercount>\n\t<signature>N0_V1_AztBAAkU|v1_o7hOEchW</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'Createtime': '1753083965', 'SenderWxid': 'wxid_84mmq4cu7ita22'}
2025-07-21 15:48:06 | INFO |   - 引用消息ID: 
2025-07-21 15:48:06 | INFO |   - 引用消息类型: 
2025-07-21 15:48:06 | INFO |   - 引用消息内容: 我职场POLO衫
2025-07-21 15:48:06 | INFO |   - 引用消息发送人: wxid_84mmq4cu7ita22
2025-07-21 15:48:06 | DEBUG | 收到消息: {'MsgId': *********, 'FromUserName': {'string': '48097389945@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 3, 'Content': {'string': 'wxid_84mmq4cu7ita22:\n<?xml version="1.0"?>\n<msg>\n\t<img aeskey="fb452bd66e3d4bf14b2944c45bbed87e" encryver="1" cdnthumbaeskey="fb452bd66e3d4bf14b2944c45bbed87e" cdnthumburl="3057020100044b3049020100020443ec963f02032f559502046641f7df0204687df070042461633738303938322d646238342d346139352d386332382d623537656330313936343730020405250a020201000405004c51e500" cdnthumblength="5057" cdnthumbheight="120" cdnthumbwidth="67" cdnmidheight="0" cdnmidwidth="0" cdnhdheight="0" cdnhdwidth="0" cdnmidimgurl="3057020100044b3049020100020443ec963f02032f559502046641f7df0204687df070042461633738303938322d646238342d346139352d386332382d623537656330313936343730020405250a020201000405004c51e500" length="153771" md5="71c521514e01999a2d0bdb5d360fa7d7" hevc_mid_size="153771" originsourcemd5="301088fe4047f684e08f677c8643d56d">\n\t\t<secHashInfoBase64>eyJwaGFzaCI6Ijc2NTQxMDAxMTAwMDQwMDAiLCJwZHFoYXNoIjoiOTUzNWMwYjcyOTEyN2U2M2ZlOTBhNDQ3NThlYTAxZmM5MWQ0NWUwZGRiNDVmODNlZmQxYzgzZDY2OWU5MTYwNiJ9</secHashInfoBase64>\n\t\t<live>\n\t\t\t<duration>0</duration>\n\t\t\t<size>0</size>\n\t\t\t<md5 />\n\t\t\t<fileid />\n\t\t\t<hdsize>0</hdsize>\n\t\t\t<hdmd5 />\n\t\t\t<hdfileid />\n\t\t\t<stillimagetimems>0</stillimagetimems>\n\t\t</live>\n\t</img>\n\t<platform_signature />\n\t<imgdatahash />\n\t<ImgSourceInfo>\n\t\t<ImgSourceUrl />\n\t\t<BizType>0</BizType>\n\t</ImgSourceInfo>\n</msg>\n'}, 'Status': 3, 'ImgStatus': 2, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1753084016, 'MsgSource': '<msgsource>\n\t<bizflag>0</bizflag>\n\t<sec_msg_node>\n\t\t<uuid>55ff7d6f2cee6d7f6d5b86ceab666837_</uuid>\n\t\t<risk-file-flag />\n\t\t<risk-file-md5-list />\n\t</sec_msg_node>\n\t<silence>0</silence>\n\t<membercount>62</membercount>\n\t<signature>N0_V1_pZZ3cltq|v1_UNogPZyn</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'PushContent': '喵小叶在群聊中发了一张图片', 'NewMsgId': 3700420632542948772, 'MsgSeq': 871387687}
2025-07-21 15:48:06 | INFO | 收到图片消息: 消息ID:********* 来自:48097389945@chatroom 发送人:wxid_84mmq4cu7ita22 XML:<?xml version="1.0"?><msg><img aeskey="fb452bd66e3d4bf14b2944c45bbed87e" encryver="1" cdnthumbaeskey="fb452bd66e3d4bf14b2944c45bbed87e" cdnthumburl="3057020100044b3049020100020443ec963f02032f559502046641f7df0204687df070042461633738303938322d646238342d346139352d386332382d623537656330313936343730020405250a020201000405004c51e500" cdnthumblength="5057" cdnthumbheight="120" cdnthumbwidth="67" cdnmidheight="0" cdnmidwidth="0" cdnhdheight="0" cdnhdwidth="0" cdnmidimgurl="3057020100044b3049020100020443ec963f02032f559502046641f7df0204687df070042461633738303938322d646238342d346139352d386332382d623537656330313936343730020405250a020201000405004c51e500" length="153771" md5="71c521514e01999a2d0bdb5d360fa7d7" hevc_mid_size="153771" originsourcemd5="301088fe4047f684e08f677c8643d56d"><secHashInfoBase64>eyJwaGFzaCI6Ijc2NTQxMDAxMTAwMDQwMDAiLCJwZHFoYXNoIjoiOTUzNWMwYjcyOTEyN2U2M2ZlOTBhNDQ3NThlYTAxZmM5MWQ0NWUwZGRiNDVmODNlZmQxYzgzZDY2OWU5MTYwNiJ9</secHashInfoBase64><live><duration>0</duration><size>0</size><md5 /><fileid /><hdsize>0</hdsize><hdmd5 /><hdfileid /><stillimagetimems>0</stillimagetimems></live></img><platform_signature /><imgdatahash /><ImgSourceInfo><ImgSourceUrl /><BizType>0</BizType></ImgSourceInfo></msg>
2025-07-21 15:48:07 | INFO | [ImageEcho] 保存图片信息成功，当前群 48097389945@chatroom 已存储 5 张图片
2025-07-21 15:48:07 | INFO | [TimerTask] 缓存图片消息: *********
2025-07-21 15:48:07 | DEBUG | 收到消息: {'MsgId': 1649216811, 'FromUserName': {'string': '48097389945@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 49, 'Content': {'string': 'zll953369865:\n<?xml version="1.0"?>\n<msg>\n\t<appmsg appid="" sdkver="0">\n\t\t<title>那我欺负你啊</title>\n\t\t<des />\n\t\t<action>view</action>\n\t\t<type>57</type>\n\t\t<showtype>0</showtype>\n\t\t<content />\n\t\t<url />\n\t\t<dataurl />\n\t\t<lowurl />\n\t\t<lowdataurl />\n\t\t<recorditem />\n\t\t<thumburl />\n\t\t<messageaction />\n\t\t<laninfo />\n\t\t<refermsg>\n\t\t\t<type>1</type>\n\t\t\t<svrid>2437753850299609834</svrid>\n\t\t\t<fromusr>48097389945@chatroom</fromusr>\n\t\t\t<chatusr>wxid_84mmq4cu7ita22</chatusr>\n\t\t\t<createtime>1753083977</createtime>\n\t\t\t<msgsource>&lt;msgsource&gt;\n    &lt;bizflag&gt;0&lt;/bizflag&gt;\n    &lt;pua&gt;1&lt;/pua&gt;\n    &lt;eggIncluded&gt;1&lt;/eggIncluded&gt;\n    &lt;silence&gt;1&lt;/silence&gt;\n    &lt;membercount&gt;62&lt;/membercount&gt;\n    &lt;signature&gt;N0_V1_D8Lq9wwY|v1_tgUOKy60&lt;/signature&gt;\n    &lt;tmp_node&gt;\n        &lt;publisher-id /&gt;\n    &lt;/tmp_node&gt;\n    &lt;sec_msg_node&gt;\n        &lt;alnode&gt;\n            &lt;fr&gt;1&lt;/fr&gt;\n        &lt;/alnode&gt;\n    &lt;/sec_msg_node&gt;\n&lt;/msgsource&gt;\n</msgsource>\n\t\t\t<content>你行你上</content>\n\t\t\t<displayname>喵小叶</displayname>\n\t\t</refermsg>\n\t\t<extinfo />\n\t\t<sourceusername />\n\t\t<sourcedisplayname />\n\t\t<commenturl />\n\t\t<appattach>\n\t\t\t<totallen>0</totallen>\n\t\t\t<attachid />\n\t\t\t<emoticonmd5 />\n\t\t\t<fileext />\n\t\t\t<aeskey />\n\t\t</appattach>\n\t\t<webviewshared>\n\t\t\t<publisherId />\n\t\t\t<publisherReqId>0</publisherReqId>\n\t\t</webviewshared>\n\t\t<weappinfo>\n\t\t\t<pagepath />\n\t\t\t<username />\n\t\t\t<appid />\n\t\t\t<appservicetype>0</appservicetype>\n\t\t</weappinfo>\n\t\t<websearch />\n\t</appmsg>\n\t<fromusername>zll953369865</fromusername>\n\t<scene>0</scene>\n\t<appinfo>\n\t\t<version>1</version>\n\t\t<appname></appname>\n\t</appinfo>\n\t<commenturl></commenturl>\n</msg>\n'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1753084019, 'MsgSource': '<msgsource>\n\t<pua>1</pua>\n\t<sec_msg_node>\n\t\t<uuid>31690db5f5536f5a8683d0134bdf589f_</uuid>\n\t\t<risk-file-flag />\n\t\t<risk-file-md5-list />\n\t</sec_msg_node>\n\t<silence>0</silence>\n\t<membercount>62</membercount>\n\t<signature>N0_V1_BWUWv9HC|v1_XvL7hq/A</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'PushContent': '【微信红包】收到一个微信红包 : 那我欺负你啊', 'NewMsgId': 5703182674423398096, 'MsgSeq': 871387688}
2025-07-21 15:48:07 | DEBUG | 从群聊消息中提取发送者: zll953369865
2025-07-21 15:48:07 | DEBUG | 使用已解析的XML处理引用消息
2025-07-21 15:48:07 | INFO | 收到引用消息: 消息ID:1649216811 来自:48097389945@chatroom 发送人:zll953369865 内容:那我欺负你啊 引用类型:1
2025-07-21 15:48:08 | INFO | [DouBaoImageToImage] 收到引用消息: 那我欺负你啊
2025-07-21 15:48:08 | INFO | [TimerTask] 收到引用消息调试信息:
2025-07-21 15:48:08 | INFO |   - 消息内容: 那我欺负你啊
2025-07-21 15:48:08 | INFO |   - 群组ID: 48097389945@chatroom
2025-07-21 15:48:08 | INFO |   - 发送人: zll953369865
2025-07-21 15:48:08 | INFO |   - 引用信息: {'MsgType': 1, 'Content': '你行你上', 'Msgid': '2437753850299609834', 'NewMsgId': '2437753850299609834', 'ToWxid': 'wxid_4usgcju5ey9q29', 'FromWxid': '48097389945@chatroom', 'Nickname': '喵小叶', 'MsgSource': '<msgsource>\n    <bizflag>0</bizflag>\n    <pua>1</pua>\n    <eggIncluded>1</eggIncluded>\n    <silence>1</silence>\n    <membercount>62</membercount>\n    <signature>N0_V1_D8Lq9wwY|v1_tgUOKy60</signature>\n    <tmp_node>\n        <publisher-id />\n    </tmp_node>\n    <sec_msg_node>\n        <alnode>\n            <fr>1</fr>\n        </alnode>\n    </sec_msg_node>\n</msgsource>\n', 'Createtime': '1753083977', 'SenderWxid': 'zll953369865'}
2025-07-21 15:48:08 | INFO |   - 引用消息ID: 
2025-07-21 15:48:08 | INFO |   - 引用消息类型: 
2025-07-21 15:48:08 | INFO |   - 引用消息内容: 你行你上
2025-07-21 15:48:08 | INFO |   - 引用消息发送人: zll953369865
2025-07-21 15:48:08 | DEBUG | 收到消息: {'MsgId': 2011789196, 'FromUserName': {'string': '48097389945@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 1, 'Content': {'string': 'wxid_84mmq4cu7ita22:\n店里的款式'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1753084021, 'MsgSource': '<msgsource>\n\t<bizflag>0</bizflag>\n\t<pua>1</pua>\n\t<eggIncluded>1</eggIncluded>\n\t<silence>0</silence>\n\t<membercount>62</membercount>\n\t<signature>N0_V1_X4MYna8v|v1_lYoWeoCy</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'PushContent': '喵小叶 : 店里的款式', 'NewMsgId': 7011723367500541232, 'MsgSeq': 871387689}
2025-07-21 15:48:08 | INFO | 收到文本消息: 消息ID:2011789196 来自:48097389945@chatroom 发送人:wxid_84mmq4cu7ita22 @:[] 内容:店里的款式
2025-07-21 15:48:08 | DEBUG | 处理消息内容: '店里的款式'
2025-07-21 15:48:08 | DEBUG | 消息内容 '店里的款式' 不匹配任何命令，忽略
2025-07-21 15:48:10 | DEBUG | 收到消息: {'MsgId': 1847102630, 'FromUserName': {'string': '48097389945@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 47, 'Content': {'string': 'zll953369865:\n<msg><emoji fromusername = "zll953369865" tousername = "48097389945@chatroom" type="2" idbuffer="media:0_0" md5="b61853caaaf8ba89a7248247c78b3fda" len = "11085" productid="com.tencent.xin.emoticon.person.stiker_1474739821fdc543cf7af3f1a9" androidmd5="b61853caaaf8ba89a7248247c78b3fda" androidlen="11085" s60v3md5 = "b61853caaaf8ba89a7248247c78b3fda" s60v3len="11085" s60v5md5 = "b61853caaaf8ba89a7248247c78b3fda" s60v5len="11085" cdnurl = "http://wxapp.tc.qq.com/262/20304/stodownload?m=b61853caaaf8ba89a7248247c78b3fda&amp;filekey=30340201010420301e02020106040253480410b61853caaaf8ba89a7248247c78b3fda02022b4d040d00000004627466730000000132&amp;hy=SH&amp;storeid=268512dfc0006d806be60a50a0000010600004f50534819965b01e6baf6afe&amp;bizid=1023" designerid = "" thumburl = "http://mmbiz.qpic.cn/mmemoticon/ajNVdqHZLLDOlsGtttOfyCAyKd5FbxyhdLRAOicIAZRQMzmviabzj4ale5TWL9dKH5/0" encrypturl = "http://wxapp.tc.qq.com/262/20304/stodownload?m=4a2c12c6397e61f91dca4b790787eaa9&amp;filekey=30340201010420301e020201060402534804104a2c12c6397e61f91dca4b790787eaa902022b50040d00000004627466730000000132&amp;hy=SH&amp;storeid=2631ccc33000b1003000000000000010600004f5053481a8c596097074d305&amp;bizid=1023" aeskey= "ede6375121a22704cec543e9c6f1bf3b" externurl = "http://wxapp.tc.qq.com/262/20304/stodownload?m=438bbf14b88ec5f1d180483bdb39c9c2&amp;filekey=30340201010420301e020201060402535a0410438bbf14b88ec5f1d180483bdb39c9c202022360040d00000004627466730000000132&amp;hy=SZ&amp;storeid=2631ccc33000dbad2000000000000010600004f50535a12b2788096ad0bec5&amp;bizid=1023" externmd5 = "1c6b8092f35995e89b6a16d849adac43" width= "240" height= "240" tpurl= "" tpauthkey= "" attachedtext= "" attachedtextcolor= "" lensid= "" emojiattr= "Cgnlk4jlk4jlk4g=" linkid= "" desc= "Cg8KBXpoX2NuEgblk4jlk4gKDwoFemhfdHcSBuWTiOWTiAoPCgdkZWZhdWx0EgRIYWhh" ></emoji>  </msg>'}, 'Status': 3, 'ImgStatus': 2, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1753084022, 'MsgSource': '<msgsource>\n\t<silence>0</silence>\n\t<membercount>62</membercount>\n\t<signature>N0_V1_LjqIkQ1Z|v1_rt5UympT</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'PushContent': '【微信红包】收到一个微信红包在群聊中发了一个表情', 'NewMsgId': 580104593251546784, 'MsgSeq': 871387690}
2025-07-21 15:48:10 | INFO | 收到表情消息: 消息ID:1847102630 来自:48097389945@chatroom 发送人:zll953369865 MD5:b61853caaaf8ba89a7248247c78b3fda 大小:11085
2025-07-21 15:48:10 | DEBUG | [DoubaoImageRecognition] 缓存表情消息: 580104593251546784
2025-07-21 15:48:11 | DEBUG | 收到消息: {'MsgId': 1482944594, 'FromUserName': {'string': '48097389945@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 1, 'Content': {'string': 'wxid_84mmq4cu7ita22:\n@ㅤㅤㅤㅤㅤㅤㅤㅤㅤㅤㅤㅤㅤㅤㅤㅤ\u2005'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1753084025, 'MsgSource': '<msgsource>\n\t<atuserlist>wxid_n5c0aekjceu621</atuserlist>\n\t<bizflag>0</bizflag>\n\t<pua>1</pua>\n\t<alnode>\n\t\t<inlenlist>18</inlenlist>\n\t</alnode>\n\t<eggIncluded>1</eggIncluded>\n\t<silence>0</silence>\n\t<membercount>62</membercount>\n\t<signature>N0_V1_2p5fbmz4|v1_cL1zZJlZ</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'PushContent': '喵小叶 : @ㅤㅤㅤㅤㅤㅤㅤㅤㅤㅤㅤㅤㅤㅤㅤㅤ\u2005', 'NewMsgId': 7940412246312166752, 'MsgSeq': 871387691}
2025-07-21 15:48:11 | INFO | 收到文本消息: 消息ID:1482944594 来自:48097389945@chatroom 发送人:wxid_84mmq4cu7ita22 @:['wxid_n5c0aekjceu621'] 内容:@ㅤㅤㅤㅤㅤㅤㅤㅤㅤㅤㅤㅤㅤㅤㅤㅤ 
2025-07-21 15:48:11 | DEBUG | 处理消息内容: '@ㅤㅤㅤㅤㅤㅤㅤㅤㅤㅤㅤㅤㅤㅤㅤㅤ'
2025-07-21 15:48:11 | DEBUG | 消息内容 '@ㅤㅤㅤㅤㅤㅤㅤㅤㅤㅤㅤㅤㅤㅤㅤㅤ' 不匹配任何命令，忽略
2025-07-21 15:48:13 | DEBUG | 收到消息: {'MsgId': 1061914101, 'FromUserName': {'string': '48097389945@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 1, 'Content': {'string': 'wxid_84mmq4cu7ita22:\n是不是和我网上买的差不多'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1753084047, 'MsgSource': '<msgsource>\n\t<bizflag>0</bizflag>\n\t<pua>1</pua>\n\t<eggIncluded>1</eggIncluded>\n\t<silence>0</silence>\n\t<membercount>62</membercount>\n\t<signature>N0_V1_xJYbH1jK|v1_J1jI8apZ</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'PushContent': '喵小叶 : 是不是和我网上买的差不多', 'NewMsgId': 7873842856722247843, 'MsgSeq': 871387692}
2025-07-21 15:48:13 | INFO | 收到文本消息: 消息ID:1061914101 来自:48097389945@chatroom 发送人:wxid_84mmq4cu7ita22 @:[] 内容:是不是和我网上买的差不多
2025-07-21 15:48:13 | DEBUG | 处理消息内容: '是不是和我网上买的差不多'
2025-07-21 15:48:13 | DEBUG | 消息内容 '是不是和我网上买的差不多' 不匹配任何命令，忽略
2025-07-21 15:48:15 | DEBUG | 收到消息: {'MsgId': 55794486, 'FromUserName': {'string': '48097389945@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 1, 'Content': {'string': 'wxid_84mmq4cu7ita22:\n到了就知道了'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1753084070, 'MsgSource': '<msgsource>\n\t<bizflag>0</bizflag>\n\t<pua>1</pua>\n\t<eggIncluded>1</eggIncluded>\n\t<silence>0</silence>\n\t<membercount>62</membercount>\n\t<signature>N0_V1_uZ6qZQU8|v1_YW+yFlIX</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'PushContent': '喵小叶 : 到了就知道了', 'NewMsgId': 7327574700934377937, 'MsgSeq': 871387693}
2025-07-21 15:48:15 | INFO | 收到文本消息: 消息ID:55794486 来自:48097389945@chatroom 发送人:wxid_84mmq4cu7ita22 @:[] 内容:到了就知道了
2025-07-21 15:48:15 | DEBUG | 处理消息内容: '到了就知道了'
2025-07-21 15:48:15 | DEBUG | 消息内容 '到了就知道了' 不匹配任何命令，忽略
2025-07-21 15:48:17 | DEBUG | 收到消息: {'MsgId': 266643020, 'FromUserName': {'string': '48097389945@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 1, 'Content': {'string': 'last--exile:\n#一般女生穿什么衣服说明她比较老实'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1753084071, 'MsgSource': '<msgsource>\n\t<pua>1</pua>\n\t<silence>0</silence>\n\t<membercount>62</membercount>\n\t<signature>N0_V1_oVadw+vb|v1_1xfprITu</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'PushContent': '亮 : #一般女生穿什么衣服说明她比较老实', 'NewMsgId': 3816760940366920413, 'MsgSeq': 871387694}
2025-07-21 15:48:17 | INFO | 收到文本消息: 消息ID:266643020 来自:48097389945@chatroom 发送人:last--exile @:[] 内容:#一般女生穿什么衣服说明她比较老实
2025-07-21 15:48:17 | DEBUG | 处理消息内容: '#一般女生穿什么衣服说明她比较老实'
2025-07-21 15:48:17 | DEBUG | 消息内容 '#一般女生穿什么衣服说明她比较老实' 不匹配任何命令，忽略
2025-07-21 15:48:19 | DEBUG | 收到消息: {'MsgId': 1690960945, 'FromUserName': {'string': '48097389945@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 34, 'Content': {'string': 'xiaomaochong:\n<msg><voicemsg endflag="1" cancelflag="0" forwardflag="0" voiceformat="4" voicelength="1000" length="9901" bufid="0" aeskey="69746d62636e79686d676f727a747065" voiceurl="3052020100044b304902010002049363814102033d14ba0204be3f949d0204687df0aa042434613837643532612d626663332d343361382d386239342d65336237656562653733306302040528000f02010004001dc74187" voicemd5="fca701843fa8d210d682045194f46846" clientmsgid="41386366366231333863396431366400541547072125d67e8f946ac106" fromusername="xiaomaochong" /></msg>'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1753084074, 'MsgSource': '<msgsource>\n\t<silence>0</silence>\n\t<membercount>62</membercount>\n\t<signature>N0_V1_4JcUYeJH|v1_zAJ5cp9U</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'PushContent': '小爱在群聊中发了一段语音', 'NewMsgId': 2262866285745428452, 'MsgSeq': 871387695}
2025-07-21 15:48:19 | INFO | 收到语音消息: 消息ID:1690960945 来自:48097389945@chatroom 发送人:xiaomaochong XML:
<msg><voicemsg endflag="1" cancelflag="0" forwardflag="0" voiceformat="4" voicelength="1000" length="9901" bufid="0" aeskey="69746d62636e79686d676f727a747065" voiceurl="3052020100044b304902010002049363814102033d14ba0204be3f949d0204687df0aa042434613837643532612d626663332d343361382d386239342d65336237656562653733306302040528000f02010004001dc74187" voicemd5="fca701843fa8d210d682045194f46846" clientmsgid="41386366366231333863396431366400541547072125d67e8f946ac106" fromusername="xiaomaochong" /></msg>
2025-07-21 15:48:20 | DEBUG | 收到消息: {'MsgId': 637939624, 'FromUserName': {'string': '48097389945@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 1, 'Content': {'string': 'wxid_q35rkzgkjvlv12:\n女生是否老实并不能通过穿着来判断，因为一个人的姓格和品质与穿着并没有必然的联系。因此，不能简单地将女生的穿着作为评价她们是否老实的标准。\n\n不同的女生有不同的穿衣风格和喜好，而且她们的穿衣选择还受到场合、文化、个人喜好等多种因素的影响。因此，不能根据女生的衣着来推断她们的姓格或品质。\n\n一个人的老实程度应该通过与其姣往和相处来了解和判断，而不是简单地通过外表或穿着来下结论。'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1753084076, 'MsgSource': '<msgsource>\n\t<silence>0</silence>\n\t<membercount>62</membercount>\n\t<signature>N0_V1_NxGv71c3|v1_gk5bKojE</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'PushContent': '摇月 : 女生是否老实并不能通过穿着来判断，因为一个人的姓格和品质与穿...', 'NewMsgId': 8575600226677271840, 'MsgSeq': 871387696}
2025-07-21 15:48:20 | INFO | 收到文本消息: 消息ID:637939624 来自:48097389945@chatroom 发送人:wxid_q35rkzgkjvlv12 @:[] 内容:女生是否老实并不能通过穿着来判断，因为一个人的姓格和品质与穿着并没有必然的联系。因此，不能简单地将女生的穿着作为评价她们是否老实的标准。

不同的女生有不同的穿衣风格和喜好，而且她们的穿衣选择还受到场合、文化、个人喜好等多种因素的影响。因此，不能根据女生的衣着来推断她们的姓格或品质。

一个人的老实程度应该通过与其姣往和相处来了解和判断，而不是简单地通过外表或穿着来下结论。
2025-07-21 15:48:20 | DEBUG | 处理消息内容: '女生是否老实并不能通过穿着来判断，因为一个人的姓格和品质与穿着并没有必然的联系。因此，不能简单地将女生的穿着作为评价她们是否老实的标准。

不同的女生有不同的穿衣风格和喜好，而且她们的穿衣选择还受到场合、文化、个人喜好等多种因素的影响。因此，不能根据女生的衣着来推断她们的姓格或品质。

一个人的老实程度应该通过与其姣往和相处来了解和判断，而不是简单地通过外表或穿着来下结论。'
2025-07-21 15:48:20 | DEBUG | 消息内容 '女生是否老实并不能通过穿着来判断，因为一个人的姓格和品质与穿着并没有必然的联系。因此，不能简单地将女生的穿着作为评价她们是否老实的标准。

不同的女生有不同的穿衣风格和喜好，而且她们的穿衣选择还受到场合、文化、个人喜好等多种因素的影响。因此，不能根据女生的衣着来推断她们的姓格或品质。

一个人的老实程度应该通过与其姣往和相处来了解和判断，而不是简单地通过外表或穿着来下结论。' 不匹配任何命令，忽略
2025-07-21 15:48:22 | DEBUG | 收到消息: {'MsgId': 1409274682, 'FromUserName': {'string': '48097389945@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 49, 'Content': {'string': 'wxid_n5c0aekjceu621:\n<?xml version="1.0"?>\n<msg>\n\t<appmsg appid="" sdkver="0">\n\t\t<title>这个模特看不出来</title>\n\t\t<des />\n\t\t<username />\n\t\t<action>view</action>\n\t\t<type>57</type>\n\t\t<showtype>0</showtype>\n\t\t<content />\n\t\t<url />\n\t\t<lowurl />\n\t\t<forwardflag>0</forwardflag>\n\t\t<dataurl />\n\t\t<lowdataurl />\n\t\t<contentattr>0</contentattr>\n\t\t<streamvideo>\n\t\t\t<streamvideourl />\n\t\t\t<streamvideototaltime>0</streamvideototaltime>\n\t\t\t<streamvideotitle />\n\t\t\t<streamvideowording />\n\t\t\t<streamvideoweburl />\n\t\t\t<streamvideothumburl />\n\t\t\t<streamvideoaduxinfo />\n\t\t\t<streamvideopublishid />\n\t\t</streamvideo>\n\t\t<canvasPageItem>\n\t\t\t<canvasPageXml><![CDATA[]]></canvasPageXml>\n\t\t</canvasPageItem>\n\t\t<refermsg>\n\t\t\t<type>1</type>\n\t\t\t<svrid>7873842856722247843</svrid>\n\t\t\t<fromusr>48097389945@chatroom</fromusr>\n\t\t\t<chatusr>wxid_84mmq4cu7ita22</chatusr>\n\t\t\t<displayname>喵小叶</displayname>\n\t\t\t<msgsource>&lt;msgsource&gt;\n\t&lt;bizflag&gt;0&lt;/bizflag&gt;\n\t&lt;pua&gt;1&lt;/pua&gt;\n\t&lt;eggIncluded&gt;1&lt;/eggIncluded&gt;\n\t&lt;silence&gt;1&lt;/silence&gt;\n\t&lt;membercount&gt;62&lt;/membercount&gt;\n\t&lt;signature&gt;N0_V1_zInvo7Q9|v1_bqekFZzO&lt;/signature&gt;\n\t&lt;tmp_node&gt;\n\t\t&lt;publisher-id&gt;&lt;/publisher-id&gt;\n\t&lt;/tmp_node&gt;\n&lt;/msgsource&gt;\n</msgsource>\n\t\t\t<content>\n是不是和我网上买的差不多</content>\n\t\t\t<strid />\n\t\t\t<createtime>1753084047</createtime>\n\t\t</refermsg>\n\t\t<appattach>\n\t\t\t<totallen>0</totallen>\n\t\t\t<attachid />\n\t\t\t<cdnattachurl />\n\t\t\t<emoticonmd5 />\n\t\t\t<aeskey />\n\t\t\t<fileext />\n\t\t\t<islargefilemsg>0</islargefilemsg>\n\t\t</appattach>\n\t\t<extinfo />\n\t\t<androidsource>0</androidsource>\n\t\t<thumburl />\n\t\t<mediatagname />\n\t\t<messageaction><![CDATA[]]></messageaction>\n\t\t<messageext><![CDATA[]]></messageext>\n\t\t<emoticongift>\n\t\t\t<packageflag>0</packageflag>\n\t\t\t<packageid />\n\t\t</emoticongift>\n\t\t<emoticonshared>\n\t\t\t<packageflag>0</packageflag>\n\t\t\t<packageid />\n\t\t</emoticonshared>\n\t\t<designershared>\n\t\t\t<designeruin>0</designeruin>\n\t\t\t<designername>null</designername>\n\t\t\t<designerrediretcturl><![CDATA[null]]></designerrediretcturl>\n\t\t</designershared>\n\t\t<emotionpageshared>\n\t\t\t<tid>0</tid>\n\t\t\t<title>null</title>\n\t\t\t<desc>null</desc>\n\t\t\t<iconUrl><![CDATA[null]]></iconUrl>\n\t\t\t<secondUrl>null</secondUrl>\n\t\t\t<pageType>0</pageType>\n\t\t\t<setKey>null</setKey>\n\t\t</emotionpageshared>\n\t\t<webviewshared>\n\t\t\t<shareUrlOriginal />\n\t\t\t<shareUrlOpen />\n\t\t\t<jsAppId />\n\t\t\t<publisherId />\n\t\t\t<publisherReqId />\n\t\t</webviewshared>\n\t\t<template_id />\n\t\t<md5 />\n\t\t<websearch>\n\t\t\t<rec_category>0</rec_category>\n\t\t\t<channelId>0</channelId>\n\t\t</websearch>\n\t\t<weappinfo>\n\t\t\t<username />\n\t\t\t<appid />\n\t\t\t<appservicetype>0</appservicetype>\n\t\t\t<secflagforsinglepagemode>0</secflagforsinglepagemode>\n\t\t\t<videopageinfo>\n\t\t\t\t<thumbwidth>0</thumbwidth>\n\t\t\t\t<thumbheight>0</thumbheight>\n\t\t\t\t<fromopensdk>0</fromopensdk>\n\t\t\t</videopageinfo>\n\t\t</weappinfo>\n\t\t<statextstr />\n\t\t<musicShareItem>\n\t\t\t<musicDuration>0</musicDuration>\n\t\t</musicShareItem>\n\t\t<finderLiveProductShare>\n\t\t\t<finderLiveID><![CDATA[]]></finderLiveID>\n\t\t\t<finderUsername><![CDATA[]]></finderUsername>\n\t\t\t<finderObjectID><![CDATA[]]></finderObjectID>\n\t\t\t<finderNonceID><![CDATA[]]></finderNonceID>\n\t\t\t<liveStatus><![CDATA[]]></liveStatus>\n\t\t\t<appId><![CDATA[]]></appId>\n\t\t\t<pagePath><![CDATA[]]></pagePath>\n\t\t\t<productId><![CDATA[]]></productId>\n\t\t\t<coverUrl><![CDATA[]]></coverUrl>\n\t\t\t<productTitle><![CDATA[]]></productTitle>\n\t\t\t<marketPrice><![CDATA[0]]></marketPrice>\n\t\t\t<sellingPrice><![CDATA[0]]></sellingPrice>\n\t\t\t<platformHeadImg><![CDATA[]]></platformHeadImg>\n\t\t\t<platformName><![CDATA[]]></platformName>\n\t\t\t<shopWindowId><![CDATA[]]></shopWindowId>\n\t\t\t<flashSalePrice><![CDATA[0]]></flashSalePrice>\n\t\t\t<flashSaleEndTime><![CDATA[0]]></flashSaleEndTime>\n\t\t\t<ecSource><![CDATA[]]></ecSource>\n\t\t\t<sellingPriceWording><![CDATA[]]></sellingPriceWording>\n\t\t\t<platformIconURL><![CDATA[]]></platformIconURL>\n\t\t\t<firstProductTagURL><![CDATA[]]></firstProductTagURL>\n\t\t\t<firstProductTagAspectRatioString><![CDATA[0.0]]></firstProductTagAspectRatioString>\n\t\t\t<secondProductTagURL><![CDATA[]]></secondProductTagURL>\n\t\t\t<secondProductTagAspectRatioString><![CDATA[0.0]]></secondProductTagAspectRatioString>\n\t\t\t<firstGuaranteeWording><![CDATA[]]></firstGuaranteeWording>\n\t\t\t<secondGuaranteeWording><![CDATA[]]></secondGuaranteeWording>\n\t\t\t<thirdGuaranteeWording><![CDATA[]]></thirdGuaranteeWording>\n\t\t\t<isPriceBeginShow>false</isPriceBeginShow>\n\t\t\t<lastGMsgID><![CDATA[]]></lastGMsgID>\n\t\t\t<promoterKey><![CDATA[]]></promoterKey>\n\t\t\t<discountWording><![CDATA[]]></discountWording>\n\t\t\t<priceSuffixDescription><![CDATA[]]></priceSuffixDescription>\n\t\t\t<showBoxItemStringList />\n\t\t</finderLiveProductShare>\n\t\t<finderOrder>\n\t\t\t<appID><![CDATA[]]></appID>\n\t\t\t<orderID><![CDATA[]]></orderID>\n\t\t\t<path><![CDATA[]]></path>\n\t\t\t<priceWording><![CDATA[]]></priceWording>\n\t\t\t<stateWording><![CDATA[]]></stateWording>\n\t\t\t<productImageURL><![CDATA[]]></productImageURL>\n\t\t\t<products><![CDATA[]]></products>\n\t\t\t<productsCount><![CDATA[0]]></productsCount>\n\t\t</finderOrder>\n\t\t<finderShopWindowShare>\n\t\t\t<finderUsername><![CDATA[]]></finderUsername>\n\t\t\t<avatar><![CDATA[]]></avatar>\n\t\t\t<nickname><![CDATA[]]></nickname>\n\t\t\t<commodityInStockCount><![CDATA[]]></commodityInStockCount>\n\t\t\t<appId><![CDATA[]]></appId>\n\t\t\t<path><![CDATA[]]></path>\n\t\t\t<appUsername><![CDATA[]]></appUsername>\n\t\t\t<query><![CDATA[]]></query>\n\t\t\t<liteAppId><![CDATA[]]></liteAppId>\n\t\t\t<liteAppPath><![CDATA[]]></liteAppPath>\n\t\t\t<liteAppQuery><![CDATA[]]></liteAppQuery>\n\t\t\t<platformTagURL><![CDATA[]]></platformTagURL>\n\t\t\t<saleWording><![CDATA[]]></saleWording>\n\t\t\t<lastGMsgID><![CDATA[]]></lastGMsgID>\n\t\t\t<profileTypeWording><![CDATA[]]></profileTypeWording>\n\t\t\t<reputationInfo>\n\t\t\t\t<hasReputationInfo>0</hasReputationInfo>\n\t\t\t\t<reputationScore>0</reputationScore>\n\t\t\t\t<reputationWording />\n\t\t\t\t<reputationTextColor />\n\t\t\t\t<reputationLevelWording />\n\t\t\t\t<reputationBackgroundColor />\n\t\t\t</reputationInfo>\n\t\t\t<productImageURLList />\n\t\t</finderShopWindowShare>\n\t\t<findernamecard>\n\t\t\t<username />\n\t\t\t<avatar><![CDATA[]]></avatar>\n\t\t\t<nickname />\n\t\t\t<auth_job />\n\t\t\t<auth_icon>0</auth_icon>\n\t\t\t<auth_icon_url />\n\t\t\t<ecSource><![CDATA[]]></ecSource>\n\t\t\t<lastGMsgID><![CDATA[]]></lastGMsgID>\n\t\t</findernamecard>\n\t\t<finderGuarantee>\n\t\t\t<scene><![CDATA[0]]></scene>\n\t\t</finderGuarantee>\n\t\t<directshare>0</directshare>\n\t\t<gamecenter>\n\t\t\t<namecard>\n\t\t\t\t<iconUrl />\n\t\t\t\t<name />\n\t\t\t\t<desc />\n\t\t\t\t<tail />\n\t\t\t\t<jumpUrl />\n\t\t\t</namecard>\n\t\t</gamecenter>\n\t\t<patMsg>\n\t\t\t<chatUser />\n\t\t\t<records>\n\t\t\t\t<recordNum>0</recordNum>\n\t\t\t</records>\n\t\t</patMsg>\n\t\t<secretmsg>\n\t\t\t<issecretmsg>0</issecretmsg>\n\t\t</secretmsg>\n\t\t<referfromscene>0</referfromscene>\n\t\t<gameshare>\n\t\t\t<liteappext>\n\t\t\t\t<liteappbizdata />\n\t\t\t\t<priority>0</priority>\n\t\t\t</liteappext>\n\t\t\t<appbrandext>\n\t\t\t\t<litegameinfo />\n\t\t\t\t<priority>-1</priority>\n\t\t\t</appbrandext>\n\t\t\t<gameshareid />\n\t\t\t<sharedata />\n\t\t\t<isvideo>0</isvideo>\n\t\t\t<duration>-1</duration>\n\t\t\t<isexposed>0</isexposed>\n\t\t\t<readtext />\n\t\t</gameshare>\n\t\t<mpsharetrace>\n\t\t\t<hasfinderelement>0</hasfinderelement>\n\t\t\t<lastgmsgid />\n\t\t</mpsharetrace>\n\t\t<wxgamecard>\n\t\t\t<framesetname />\n\t\t\t<mbcarddata />\n\t\t\t<minpkgversion />\n\t\t\t<mbcardheight>0</mbcardheight>\n\t\t\t<isoldversion>0</isoldversion>\n\t\t</wxgamecard>\n\t\t<liteapp>\n\t\t\t<id>null</id>\n\t\t\t<path />\n\t\t\t<query />\n\t\t</liteapp>\n\t</appmsg>\n\t<fromusername>wxid_n5c0aekjceu621</fromusername>\n\t<scene>0</scene>\n\t<appinfo>\n\t\t<version>1</version>\n\t\t<appname></appname>\n\t</appinfo>\n\t<commenturl></commenturl>\n</msg>\n'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1753084099, 'MsgSource': '<msgsource>\n\t<alnode>\n\t\t<fr>4</fr>\n\t</alnode>\n\t<sec_msg_node>\n\t\t<uuid>e5b02c525a3469a23f252523ae7092b9_</uuid>\n\t\t<risk-file-flag />\n\t\t<risk-file-md5-list />\n\t</sec_msg_node>\n\t<silence>0</silence>\n\t<membercount>62</membercount>\n\t<signature>N0_V1_azL4aFha|v1_OvE2CQXz</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'PushContent': 'ㅤㅤㅤㅤㅤㅤㅤㅤㅤㅤㅤㅤㅤㅤㅤㅤ : 这个模特看不出来', 'NewMsgId': 219225483405013584, 'MsgSeq': 871387699}
2025-07-21 15:48:22 | DEBUG | 从群聊消息中提取发送者: wxid_n5c0aekjceu621
2025-07-21 15:48:22 | DEBUG | 使用已解析的XML处理引用消息
2025-07-21 15:48:22 | INFO | 收到引用消息: 消息ID:1409274682 来自:48097389945@chatroom 发送人:wxid_n5c0aekjceu621 内容:这个模特看不出来 引用类型:1
2025-07-21 15:48:22 | INFO | [DouBaoImageToImage] 收到引用消息: 这个模特看不出来
2025-07-21 15:48:22 | INFO | [TimerTask] 收到引用消息调试信息:
2025-07-21 15:48:22 | INFO |   - 消息内容: 这个模特看不出来
2025-07-21 15:48:22 | INFO |   - 群组ID: 48097389945@chatroom
2025-07-21 15:48:22 | INFO |   - 发送人: wxid_n5c0aekjceu621
2025-07-21 15:48:22 | INFO |   - 引用信息: {'MsgType': 1, 'Content': '\n是不是和我网上买的差不多', 'Msgid': '7873842856722247843', 'NewMsgId': '7873842856722247843', 'ToWxid': 'wxid_4usgcju5ey9q29', 'FromWxid': '48097389945@chatroom', 'Nickname': '喵小叶', 'MsgSource': '<msgsource>\n\t<bizflag>0</bizflag>\n\t<pua>1</pua>\n\t<eggIncluded>1</eggIncluded>\n\t<silence>1</silence>\n\t<membercount>62</membercount>\n\t<signature>N0_V1_zInvo7Q9|v1_bqekFZzO</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'Createtime': '1753084047', 'SenderWxid': 'wxid_n5c0aekjceu621'}
2025-07-21 15:48:22 | INFO |   - 引用消息ID: 
2025-07-21 15:48:22 | INFO |   - 引用消息类型: 
2025-07-21 15:48:22 | INFO |   - 引用消息内容: 
是不是和我网上买的差不多
2025-07-21 15:48:22 | INFO |   - 引用消息发送人: wxid_n5c0aekjceu621
2025-07-21 15:48:23 | DEBUG | 收到消息: {'MsgId': 181849582, 'FromUserName': {'string': '48097389945@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 1, 'Content': {'string': 'last--exile:\n#一般男生穿什么衣服说明他比较老实'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1753084100, 'MsgSource': '<msgsource>\n\t<pua>1</pua>\n\t<alnode>\n\t\t<cf>5</cf>\n\t\t<inlenlist>17</inlenlist>\n\t</alnode>\n\t<silence>0</silence>\n\t<membercount>62</membercount>\n\t<signature>N0_V1_fgduQCUG|v1_K7RxRyYu</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'PushContent': '亮 : #一般男生穿什么衣服说明他比较老实', 'NewMsgId': 7928743293654226881, 'MsgSeq': 871387700}
2025-07-21 15:48:23 | INFO | 收到文本消息: 消息ID:181849582 来自:48097389945@chatroom 发送人:last--exile @:[] 内容:#一般男生穿什么衣服说明他比较老实
2025-07-21 15:48:23 | DEBUG | 处理消息内容: '#一般男生穿什么衣服说明他比较老实'
2025-07-21 15:48:23 | DEBUG | 消息内容 '#一般男生穿什么衣服说明他比较老实' 不匹配任何命令，忽略
2025-07-21 15:48:25 | DEBUG | 收到消息: {'MsgId': 1051474014, 'FromUserName': {'string': '48097389945@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 34, 'Content': {'string': 'xiaomaochong:\n<msg><voicemsg endflag="1" cancelflag="0" forwardflag="0" voiceformat="4" voicelength="1000" length="10044" bufid="0" aeskey="6a6a6e697365726b6c61627579737a66" voiceurl="3052020100044b304902010002049363814102033d14ba0204e63c949d0204687df0c7042466653032333631322d356630302d343663342d616231362d35373463343938313532643202040528000f02010004001dc74187" voicemd5="4f3fa28a38b9b88713b489ec23a9126b" clientmsgid="41386366366231333863396431366400231548072125d67e8f9b8c2101" fromusername="xiaomaochong" /></msg>'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1753084103, 'MsgSource': '<msgsource>\n\t<silence>0</silence>\n\t<membercount>62</membercount>\n\t<signature>N0_V1_QkiE0m3l|v1_mIj4lM7M</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'PushContent': '小爱在群聊中发了一段语音', 'NewMsgId': 4760834353529720308, 'MsgSeq': 871387701}
2025-07-21 15:48:25 | INFO | 收到语音消息: 消息ID:1051474014 来自:48097389945@chatroom 发送人:xiaomaochong XML:
<msg><voicemsg endflag="1" cancelflag="0" forwardflag="0" voiceformat="4" voicelength="1000" length="10044" bufid="0" aeskey="6a6a6e697365726b6c61627579737a66" voiceurl="3052020100044b304902010002049363814102033d14ba0204e63c949d0204687df0c7042466653032333631322d356630302d343663342d616231362d35373463343938313532643202040528000f02010004001dc74187" voicemd5="4f3fa28a38b9b88713b489ec23a9126b" clientmsgid="41386366366231333863396431366400231548072125d67e8f9b8c2101" fromusername="xiaomaochong" /></msg>
2025-07-21 15:48:26 | DEBUG | 收到消息: {'MsgId': 2090612074, 'FromUserName': {'string': '48097389945@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 1, 'Content': {'string': 'wxid_q35rkzgkjvlv12:\n同样，男生的老实程度也不能通过穿着来判断。穿衣风格只是个人审美和喜好的体现，与个人的姓格和品质没有必然的联系。\n\n不同的男生有不同的穿衣风格和喜好，他们的穿衣选择可能受到职业、场合、文化、个人喜好等多种因素的影响。因此，不能根据男生的衣着来推断他们是否老实。\n\n要真正了解一个人的老实程度，需要通过与其姣往、相处以及观察其行为表现来得出结论。'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1753084106, 'MsgSource': '<msgsource>\n\t<silence>0</silence>\n\t<membercount>62</membercount>\n\t<signature>N0_V1_1Oguk20s|v1_yayXzmSp</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'PushContent': '摇月 : 同样，男生的老实程度也不能通过穿着来判断。穿衣风格只是个人审...', 'NewMsgId': 1835027235044896905, 'MsgSeq': 871387702}
2025-07-21 15:48:26 | INFO | 收到文本消息: 消息ID:2090612074 来自:48097389945@chatroom 发送人:wxid_q35rkzgkjvlv12 @:[] 内容:同样，男生的老实程度也不能通过穿着来判断。穿衣风格只是个人审美和喜好的体现，与个人的姓格和品质没有必然的联系。

不同的男生有不同的穿衣风格和喜好，他们的穿衣选择可能受到职业、场合、文化、个人喜好等多种因素的影响。因此，不能根据男生的衣着来推断他们是否老实。

要真正了解一个人的老实程度，需要通过与其姣往、相处以及观察其行为表现来得出结论。
2025-07-21 15:48:26 | DEBUG | 处理消息内容: '同样，男生的老实程度也不能通过穿着来判断。穿衣风格只是个人审美和喜好的体现，与个人的姓格和品质没有必然的联系。

不同的男生有不同的穿衣风格和喜好，他们的穿衣选择可能受到职业、场合、文化、个人喜好等多种因素的影响。因此，不能根据男生的衣着来推断他们是否老实。

要真正了解一个人的老实程度，需要通过与其姣往、相处以及观察其行为表现来得出结论。'
2025-07-21 15:48:26 | DEBUG | 消息内容 '同样，男生的老实程度也不能通过穿着来判断。穿衣风格只是个人审美和喜好的体现，与个人的姓格和品质没有必然的联系。

不同的男生有不同的穿衣风格和喜好，他们的穿衣选择可能受到职业、场合、文化、个人喜好等多种因素的影响。因此，不能根据男生的衣着来推断他们是否老实。

要真正了解一个人的老实程度，需要通过与其姣往、相处以及观察其行为表现来得出结论。' 不匹配任何命令，忽略
2025-07-21 15:48:39 | DEBUG | 收到消息: {'MsgId': 541544359, 'FromUserName': {'string': '47325400669@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 1, 'Content': {'string': 'q363059309:\n八豆鸭子的进化史'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1753084129, 'MsgSource': '<msgsource>\n\t<sec_msg_node>\n\t\t<alnode>\n\t\t\t<fr>1</fr>\n\t\t</alnode>\n\t</sec_msg_node>\n\t<pua>1</pua>\n\t<silence>0</silence>\n\t<membercount>219</membercount>\n\t<signature>N0_V1_XR5Z1NdO|v1_6AcUcaAk</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'PushContent': '\x7f\x7f \x7f\x7f \x7f\x7f \x7f\x7f \x7f\x7f \x7f\x7f \x7f\x7f \x7f\x7f \x7f\x7f \x7f\x7fLin : 八豆鸭子的进化史', 'NewMsgId': 2545169574147039137, 'MsgSeq': 871387703}
2025-07-21 15:48:39 | INFO | 收到文本消息: 消息ID:541544359 来自:47325400669@chatroom 发送人:q363059309 @:[] 内容:八豆鸭子的进化史
2025-07-21 15:48:39 | DEBUG | 处理消息内容: '八豆鸭子的进化史'
2025-07-21 15:48:39 | DEBUG | 消息内容 '八豆鸭子的进化史' 不匹配任何命令，忽略
2025-07-21 15:48:54 | DEBUG | 收到消息: {'MsgId': 1678991280, 'FromUserName': {'string': '48097389945@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 49, 'Content': {'string': 'wxid_84mmq4cu7ita22:\n<?xml version="1.0"?>\n<msg>\n\t<appmsg appid="" sdkver="0">\n\t\t<title>差点想说再拉个群</title>\n\t\t<des />\n\t\t<action>view</action>\n\t\t<type>57</type>\n\t\t<showtype>0</showtype>\n\t\t<content />\n\t\t<url />\n\t\t<dataurl />\n\t\t<lowurl />\n\t\t<lowdataurl />\n\t\t<recorditem />\n\t\t<thumburl />\n\t\t<messageaction />\n\t\t<laninfo />\n\t\t<refermsg>\n\t\t\t<type>49</type>\n\t\t\t<svrid>5703182674423398096</svrid>\n\t\t\t<fromusr>48097389945@chatroom</fromusr>\n\t\t\t<chatusr>zll953369865</chatusr>\n\t\t\t<createtime>1753084019</createtime>\n\t\t\t<msgsource>&lt;msgsource&gt;\n    &lt;pua&gt;1&lt;/pua&gt;\n    &lt;sec_msg_node&gt;\n        &lt;uuid&gt;31690db5f5536f5a8683d0134bdf589f_&lt;/uuid&gt;\n        &lt;risk-file-flag /&gt;\n        &lt;risk-file-md5-list /&gt;\n        &lt;alnode&gt;\n            &lt;fr&gt;1&lt;/fr&gt;\n        &lt;/alnode&gt;\n    &lt;/sec_msg_node&gt;\n    &lt;silence&gt;1&lt;/silence&gt;\n    &lt;membercount&gt;62&lt;/membercount&gt;\n    &lt;signature&gt;N0_V1_9bGyxdNa|v1_yt0BLlOM&lt;/signature&gt;\n    &lt;tmp_node&gt;\n        &lt;publisher-id /&gt;\n    &lt;/tmp_node&gt;\n&lt;/msgsource&gt;\n</msgsource>\n\t\t\t<content>&lt;?xml version="1.0"?&gt;\n&lt;msg&gt;\n\t&lt;appmsg appid="" sdkver="0"&gt;\n\t\t&lt;title&gt;那我欺负你啊&lt;/title&gt;\n\t\t&lt;des /&gt;\n\t\t&lt;action&gt;view&lt;/action&gt;\n\t\t&lt;type&gt;57&lt;/type&gt;\n\t\t&lt;showtype&gt;0&lt;/showtype&gt;\n\t\t&lt;content /&gt;\n\t\t&lt;url /&gt;\n\t\t&lt;dataurl /&gt;\n\t\t&lt;lowurl /&gt;\n\t\t&lt;lowdataurl /&gt;\n\t\t&lt;recorditem /&gt;\n\t\t&lt;thumburl /&gt;\n\t\t&lt;messageaction /&gt;\n\t\t&lt;laninfo /&gt;\n\t\t&lt;refermsg&gt;\n\t\t\t&lt;type&gt;1&lt;/type&gt;\n\t\t\t&lt;svrid&gt;2437753850299609834&lt;/svrid&gt;\n\t\t\t&lt;fromusr&gt;48097389945@chatroom&lt;/fromusr&gt;\n\t\t\t&lt;chatusr&gt;wxid_84mmq4cu7ita22&lt;/chatusr&gt;\n\t\t\t&lt;createtime&gt;1753083977&lt;/createtime&gt;\n\t\t\t&lt;msgsource&gt;&amp;lt;msgsource&amp;gt;\n    &amp;lt;bizflag&amp;gt;0&amp;lt;/bizflag&amp;gt;\n    &amp;lt;pua&amp;gt;1&amp;lt;/pua&amp;gt;\n    &amp;lt;eggIncluded&amp;gt;1&amp;lt;/eggIncluded&amp;gt;\n    &amp;lt;silence&amp;gt;1&amp;lt;/silence&amp;gt;\n    &amp;lt;membercount&amp;gt;62&amp;lt;/membercount&amp;gt;\n    &amp;lt;signature&amp;gt;N0_V1_D8Lq9wwY|v1_tgUOKy60&amp;lt;/signature&amp;gt;\n    &amp;lt;tmp_node&amp;gt;\n        &amp;lt;publisher-id /&amp;gt;\n    &amp;lt;/tmp_node&amp;gt;\n    &amp;lt;sec_msg_node&amp;gt;\n        &amp;lt;alnode&amp;gt;\n            &amp;lt;fr&amp;gt;1&amp;lt;/fr&amp;gt;\n        &amp;lt;/alnode&amp;gt;\n    &amp;lt;/sec_msg_node&amp;gt;\n&amp;lt;/msgsource&amp;gt;\n&lt;/msgsource&gt;\n\t\t\t&lt;content&gt;你行你上&lt;/content&gt;\n\t\t\t&lt;displayname&gt;喵小叶&lt;/displayname&gt;\n\t\t&lt;/refermsg&gt;\n\t\t&lt;extinfo /&gt;\n\t\t&lt;sourceusername /&gt;\n\t\t&lt;sourcedisplayname /&gt;\n\t\t&lt;commenturl /&gt;\n\t\t&lt;appattach&gt;\n\t\t\t&lt;totallen&gt;0&lt;/totallen&gt;\n\t\t\t&lt;attachid /&gt;\n\t\t\t&lt;emoticonmd5 /&gt;\n\t\t\t&lt;fileext /&gt;\n\t\t\t&lt;aeskey /&gt;\n\t\t&lt;/appattach&gt;\n\t\t&lt;webviewshared&gt;\n\t\t\t&lt;publisherId /&gt;\n\t\t\t&lt;publisherReqId&gt;0&lt;/publisherReqId&gt;\n\t\t&lt;/webviewshared&gt;\n\t\t&lt;weappinfo&gt;\n\t\t\t&lt;pagepath /&gt;\n\t\t\t&lt;username /&gt;\n\t\t\t&lt;appid /&gt;\n\t\t\t&lt;appservicetype&gt;0&lt;/appservicetype&gt;\n\t\t&lt;/weappinfo&gt;\n\t\t&lt;websearch /&gt;\n\t&lt;/appmsg&gt;\n\t&lt;fromusername&gt;zll953369865&lt;/fromusername&gt;\n\t&lt;scene&gt;0&lt;/scene&gt;\n\t&lt;appinfo&gt;\n\t\t&lt;version&gt;1&lt;/version&gt;\n\t\t&lt;appname&gt;&lt;/appname&gt;\n\t&lt;/appinfo&gt;\n\t&lt;commenturl&gt;&lt;/commenturl&gt;\n&lt;/msg&gt;\n</content>\n\t\t\t<displayname>【微信红包】收到一个微信红包</displayname>\n\t\t</refermsg>\n\t\t<extinfo />\n\t\t<sourceusername />\n\t\t<sourcedisplayname />\n\t\t<commenturl />\n\t\t<appattach>\n\t\t\t<totallen>0</totallen>\n\t\t\t<attachid />\n\t\t\t<emoticonmd5 />\n\t\t\t<fileext />\n\t\t\t<aeskey />\n\t\t</appattach>\n\t\t<webviewshared>\n\t\t\t<publisherId />\n\t\t\t<publisherReqId>0</publisherReqId>\n\t\t</webviewshared>\n\t\t<weappinfo>\n\t\t\t<pagepath />\n\t\t\t<username />\n\t\t\t<appid />\n\t\t\t<appservicetype>0</appservicetype>\n\t\t</weappinfo>\n\t\t<websearch />\n\t</appmsg>\n\t<fromusername>wxid_84mmq4cu7ita22</fromusername>\n\t<scene>0</scene>\n\t<appinfo>\n\t\t<version>1</version>\n\t\t<appname></appname>\n\t</appinfo>\n\t<commenturl></commenturl>\n</msg>\n'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1753084142, 'MsgSource': '<msgsource>\n\t<pua>1</pua>\n\t<sec_msg_node>\n\t\t<uuid>5a4f9045735fcede75f9e6e9ab2e7b17_</uuid>\n\t\t<risk-file-flag />\n\t\t<risk-file-md5-list />\n\t</sec_msg_node>\n\t<silence>0</silence>\n\t<membercount>62</membercount>\n\t<signature>N0_V1_dF1/4w0W|v1_5X0LEebx</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'PushContent': '喵小叶 : 差点想说再拉个群', 'NewMsgId': 5708825885223106036, 'MsgSeq': 871387704}
2025-07-21 15:48:54 | DEBUG | 从群聊消息中提取发送者: wxid_84mmq4cu7ita22
2025-07-21 15:48:54 | DEBUG | 使用已解析的XML处理引用消息
2025-07-21 15:48:54 | DEBUG | 引用消息内容已HTML解码
2025-07-21 15:48:54 | INFO | 收到引用消息: 消息ID:1678991280 来自:48097389945@chatroom 发送人:wxid_84mmq4cu7ita22 内容:差点想说再拉个群 引用类型:49
2025-07-21 15:48:54 | INFO | [DouBaoImageToImage] 收到引用消息: 差点想说再拉个群
2025-07-21 15:48:54 | INFO | [TimerTask] 收到引用消息调试信息:
2025-07-21 15:48:54 | INFO |   - 消息内容: 差点想说再拉个群
2025-07-21 15:48:54 | INFO |   - 群组ID: 48097389945@chatroom
2025-07-21 15:48:54 | INFO |   - 发送人: wxid_84mmq4cu7ita22
2025-07-21 15:48:54 | INFO |   - 引用信息: {'MsgType': 49, 'Content': '<?xml version="1.0"?>\n<msg>\n\t<appmsg appid="" sdkver="0">\n\t\t<title>那我欺负你啊</title>\n\t\t<des />\n\t\t<action>view</action>\n\t\t<type>57</type>\n\t\t<showtype>0</showtype>\n\t\t<content />\n\t\t<url />\n\t\t<dataurl />\n\t\t<lowurl />\n\t\t<lowdataurl />\n\t\t<recorditem />\n\t\t<thumburl />\n\t\t<messageaction />\n\t\t<laninfo />\n\t\t<refermsg>\n\t\t\t<type>1</type>\n\t\t\t<svrid>2437753850299609834</svrid>\n\t\t\t<fromusr>48097389945@chatroom</fromusr>\n\t\t\t<chatusr>wxid_84mmq4cu7ita22</chatusr>\n\t\t\t<createtime>1753083977</createtime>\n\t\t\t<msgsource><msgsource>\n    <bizflag>0</bizflag>\n    <pua>1</pua>\n    <eggIncluded>1</eggIncluded>\n    <silence>1</silence>\n    <membercount>62</membercount>\n    <signature>N0_V1_D8Lq9wwY|v1_tgUOKy60</signature>\n    <tmp_node>\n        <publisher-id />\n    </tmp_node>\n    <sec_msg_node>\n        <alnode>\n            <fr>1</fr>\n        </alnode>\n    </sec_msg_node>\n</msgsource>\n</msgsource>\n\t\t\t<content>你行你上</content>\n\t\t\t<displayname>喵小叶</displayname>\n\t\t</refermsg>\n\t\t<extinfo />\n\t\t<sourceusername />\n\t\t<sourcedisplayname />\n\t\t<commenturl />\n\t\t<appattach>\n\t\t\t<totallen>0</totallen>\n\t\t\t<attachid />\n\t\t\t<emoticonmd5 />\n\t\t\t<fileext />\n\t\t\t<aeskey />\n\t\t</appattach>\n\t\t<webviewshared>\n\t\t\t<publisherId />\n\t\t\t<publisherReqId>0</publisherReqId>\n\t\t</webviewshared>\n\t\t<weappinfo>\n\t\t\t<pagepath />\n\t\t\t<username />\n\t\t\t<appid />\n\t\t\t<appservicetype>0</appservicetype>\n\t\t</weappinfo>\n\t\t<websearch />\n\t</appmsg>\n\t<fromusername>zll953369865</fromusername>\n\t<scene>0</scene>\n\t<appinfo>\n\t\t<version>1</version>\n\t\t<appname></appname>\n\t</appinfo>\n\t<commenturl></commenturl>\n</msg>\n', 'Msgid': '5703182674423398096', 'NewMsgId': '5703182674423398096', 'ToWxid': 'wxid_4usgcju5ey9q29', 'FromWxid': '48097389945@chatroom', 'Nickname': '【微信红包】收到一个微信红包', 'MsgSource': '<msgsource>\n    <pua>1</pua>\n    <sec_msg_node>\n        <uuid>31690db5f5536f5a8683d0134bdf589f_</uuid>\n        <risk-file-flag />\n        <risk-file-md5-list />\n        <alnode>\n            <fr>1</fr>\n        </alnode>\n    </sec_msg_node>\n    <silence>1</silence>\n    <membercount>62</membercount>\n    <signature>N0_V1_9bGyxdNa|v1_yt0BLlOM</signature>\n    <tmp_node>\n        <publisher-id />\n    </tmp_node>\n</msgsource>\n', 'Createtime': '1753084019', 'SenderWxid': 'wxid_84mmq4cu7ita22'}
2025-07-21 15:48:54 | INFO |   - 引用消息ID: 
2025-07-21 15:48:54 | INFO |   - 引用消息类型: 
2025-07-21 15:48:54 | INFO |   - 引用消息内容: <?xml version="1.0"?>
<msg>
	<appmsg appid="" sdkver="0">
		<title>那我欺负你啊</title>
		<des />
		<action>view</action>
		<type>57</type>
		<showtype>0</showtype>
		<content />
		<url />
		<dataurl />
		<lowurl />
		<lowdataurl />
		<recorditem />
		<thumburl />
		<messageaction />
		<laninfo />
		<refermsg>
			<type>1</type>
			<svrid>2437753850299609834</svrid>
			<fromusr>48097389945@chatroom</fromusr>
			<chatusr>wxid_84mmq4cu7ita22</chatusr>
			<createtime>1753083977</createtime>
			<msgsource><msgsource>
    <bizflag>0</bizflag>
    <pua>1</pua>
    <eggIncluded>1</eggIncluded>
    <silence>1</silence>
    <membercount>62</membercount>
    <signature>N0_V1_D8Lq9wwY|v1_tgUOKy60</signature>
    <tmp_node>
        <publisher-id />
    </tmp_node>
    <sec_msg_node>
        <alnode>
            <fr>1</fr>
        </alnode>
    </sec_msg_node>
</msgsource>
</msgsource>
			<content>你行你上</content>
			<displayname>喵小叶</displayname>
		</refermsg>
		<extinfo />
		<sourceusername />
		<sourcedisplayname />
		<commenturl />
		<appattach>
			<totallen>0</totallen>
			<attachid />
			<emoticonmd5 />
			<fileext />
			<aeskey />
		</appattach>
		<webviewshared>
			<publisherId />
			<publisherReqId>0</publisherReqId>
		</webviewshared>
		<weappinfo>
			<pagepath />
			<username />
			<appid />
			<appservicetype>0</appservicetype>
		</weappinfo>
		<websearch />
	</appmsg>
	<fromusername>zll953369865</fromusername>
	<scene>0</scene>
	<appinfo>
		<version>1</version>
		<appname></appname>
	</appinfo>
	<commenturl></commenturl>
</msg>

2025-07-21 15:48:54 | INFO |   - 引用消息发送人: wxid_84mmq4cu7ita22
2025-07-21 15:49:04 | DEBUG | 收到消息: {'MsgId': 1219205476, 'FromUserName': {'string': '48097389945@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 1, 'Content': {'string': 'wxid_84mmq4cu7ita22:\n想想10群就是这么拉成11群的'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1753084153, 'MsgSource': '<msgsource>\n\t<sec_msg_node>\n\t\t<alnode>\n\t\t\t<fr>1</fr>\n\t\t</alnode>\n\t</sec_msg_node>\n\t<pua>1</pua>\n\t<silence>0</silence>\n\t<membercount>62</membercount>\n\t<signature>N0_V1_NZPFuQpm|v1_ggoViui3</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'PushContent': '喵小叶 : 想想10群就是这么拉成11群的', 'NewMsgId': 1230748475114759820, 'MsgSeq': 871387705}
2025-07-21 15:49:04 | INFO | 收到文本消息: 消息ID:1219205476 来自:48097389945@chatroom 发送人:wxid_84mmq4cu7ita22 @:[] 内容:想想10群就是这么拉成11群的
2025-07-21 15:49:04 | DEBUG | 处理消息内容: '想想10群就是这么拉成11群的'
2025-07-21 15:49:04 | DEBUG | 消息内容 '想想10群就是这么拉成11群的' 不匹配任何命令，忽略
2025-07-21 15:49:09 | DEBUG | 收到消息: {'MsgId': 538916894, 'FromUserName': {'string': '48097389945@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 47, 'Content': {'string': 'wxid_84mmq4cu7ita22:\n<msg><emoji fromusername = "wxid_84mmq4cu7ita22" tousername = "48097389945@chatroom" type="2" idbuffer="media:0_0" md5="36c13781c6ac20052f18f4fe7360b4f2" len = "22487" productid="" androidmd5="36c13781c6ac20052f18f4fe7360b4f2" androidlen="22487" s60v3md5 = "36c13781c6ac20052f18f4fe7360b4f2" s60v3len="22487" s60v5md5 = "36c13781c6ac20052f18f4fe7360b4f2" s60v5len="22487" cdnurl = "http://vweixinf.tc.qq.com/110/20401/stodownload?m=36c13781c6ac20052f18f4fe7360b4f2&amp;filekey=3043020101042f302d02016e0402535a04203336633133373831633661633230303532663138663466653733363062346632020257d7040d00000004627466730000000131&amp;hy=SZ&amp;storeid=323032313034303432323337353530303033363536356530366266333466363232393538303930303030303036653031303034666231&amp;ef=1&amp;bizid=1022" designerid = "" thumburl = "" encrypturl = "http://vweixinf.tc.qq.com/110/20402/stodownload?m=a37142253a792d312678c575e1d5107b&amp;filekey=3043020101042f302d02016e0402535a04206133373134323235336137393264333132363738633537356531643531303762020257e0040d00000004627466730000000131&amp;hy=SZ&amp;storeid=323032313034303432323337353530303034366266376530366266333466363232393538303930303030303036653032303034666232&amp;ef=2&amp;bizid=1022" aeskey= "2e437b81eea94f40ba53c7ff39f8b11c" externurl = "http://vweixinf.tc.qq.com/110/20403/stodownload?m=e2fbf51b910c856eecd42e8c95154ae8&amp;filekey=3043020101042f302d02016e0402535a04206532666266353162393130633835366565636434326538633935313534616538020207d0040d00000004627466730000000131&amp;hy=SZ&amp;storeid=323032313034303432323337353530303035303763396530366266333466363232393538303930303030303036653033303034666233&amp;ef=3&amp;bizid=1022" externmd5 = "5aff186828e0042c5b22e97ac50aedcd" width= "115" height= "115" tpurl= "" tpauthkey= "" attachedtext= "" attachedtextcolor= "" lensid= "" emojiattr= "" linkid= "" desc= "" ></emoji>  </msg>'}, 'Status': 3, 'ImgStatus': 2, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1753084159, 'MsgSource': '<sec_msg_node>\n\t<alnode>\n\t\t<fr>2</fr>\n\t</alnode>\n</sec_msg_node>\n<msgsource>\n\t<silence>0</silence>\n\t<membercount>62</membercount>\n\t<signature>N0_V1_EsnmXBe8|v1_KgHrFGN3</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'PushContent': '喵小叶在群聊中发了一个表情', 'NewMsgId': 1814938582842471876, 'MsgSeq': 871387706}
2025-07-21 15:49:09 | INFO | 收到表情消息: 消息ID:538916894 来自:48097389945@chatroom 发送人:wxid_84mmq4cu7ita22 MD5:36c13781c6ac20052f18f4fe7360b4f2 大小:22487
2025-07-21 15:49:10 | DEBUG | [DoubaoImageRecognition] 缓存表情消息: 1814938582842471876
2025-07-21 15:49:25 | DEBUG | 收到消息: {'MsgId': 1177013001, 'FromUserName': {'string': '48097389945@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 1, 'Content': {'string': 'wxid_84mmq4cu7ita22:\n10群现在咋样了'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1753084174, 'MsgSource': '<msgsource>\n\t<sec_msg_node>\n\t\t<alnode>\n\t\t\t<fr>1</fr>\n\t\t</alnode>\n\t</sec_msg_node>\n\t<pua>1</pua>\n\t<silence>0</silence>\n\t<membercount>62</membercount>\n\t<signature>N0_V1_r/KfWa98|v1_CBrmo9rl</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'PushContent': '喵小叶 : 10群现在咋样了', 'NewMsgId': 4501498518981982887, 'MsgSeq': 871387707}
2025-07-21 15:49:25 | INFO | 收到文本消息: 消息ID:1177013001 来自:48097389945@chatroom 发送人:wxid_84mmq4cu7ita22 @:[] 内容:10群现在咋样了
2025-07-21 15:49:26 | DEBUG | 处理消息内容: '10群现在咋样了'
2025-07-21 15:49:26 | DEBUG | 消息内容 '10群现在咋样了' 不匹配任何命令，忽略
2025-07-21 15:49:32 | DEBUG | 收到消息: {'MsgId': 2121596315, 'FromUserName': {'string': '48097389945@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 49, 'Content': {'string': 'zll953369865:\n<?xml version="1.0"?>\n<msg>\n\t<appmsg appid="" sdkver="0">\n\t\t<title>啥意思</title>\n\t\t<des />\n\t\t<action>view</action>\n\t\t<type>57</type>\n\t\t<showtype>0</showtype>\n\t\t<content />\n\t\t<url />\n\t\t<dataurl />\n\t\t<lowurl />\n\t\t<lowdataurl />\n\t\t<recorditem />\n\t\t<thumburl />\n\t\t<messageaction />\n\t\t<laninfo />\n\t\t<refermsg>\n\t\t\t<type>1</type>\n\t\t\t<svrid>1230748475114759820</svrid>\n\t\t\t<fromusr>48097389945@chatroom</fromusr>\n\t\t\t<chatusr>wxid_84mmq4cu7ita22</chatusr>\n\t\t\t<createtime>1753084153</createtime>\n\t\t\t<msgsource>&lt;msgsource&gt;\n    &lt;sec_msg_node&gt;\n        &lt;alnode&gt;\n            &lt;fr&gt;1&lt;/fr&gt;\n        &lt;/alnode&gt;\n    &lt;/sec_msg_node&gt;\n    &lt;pua&gt;1&lt;/pua&gt;\n    &lt;silence&gt;1&lt;/silence&gt;\n    &lt;membercount&gt;62&lt;/membercount&gt;\n    &lt;signature&gt;N0_V1_YlhHxS5f|v1_jdBkMLqA&lt;/signature&gt;\n    &lt;tmp_node&gt;\n        &lt;publisher-id /&gt;\n    &lt;/tmp_node&gt;\n&lt;/msgsource&gt;\n</msgsource>\n\t\t\t<content>想想10群就是这么拉成11群的</content>\n\t\t\t<displayname>喵小叶</displayname>\n\t\t</refermsg>\n\t\t<extinfo />\n\t\t<sourceusername />\n\t\t<sourcedisplayname />\n\t\t<commenturl />\n\t\t<appattach>\n\t\t\t<totallen>0</totallen>\n\t\t\t<attachid />\n\t\t\t<emoticonmd5 />\n\t\t\t<fileext />\n\t\t\t<aeskey />\n\t\t</appattach>\n\t\t<webviewshared>\n\t\t\t<publisherId />\n\t\t\t<publisherReqId>0</publisherReqId>\n\t\t</webviewshared>\n\t\t<weappinfo>\n\t\t\t<pagepath />\n\t\t\t<username />\n\t\t\t<appid />\n\t\t\t<appservicetype>0</appservicetype>\n\t\t</weappinfo>\n\t\t<websearch />\n\t</appmsg>\n\t<fromusername>zll953369865</fromusername>\n\t<scene>0</scene>\n\t<appinfo>\n\t\t<version>1</version>\n\t\t<appname></appname>\n\t</appinfo>\n\t<commenturl></commenturl>\n</msg>\n'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1753084181, 'MsgSource': '<msgsource>\n\t<pua>1</pua>\n\t<sec_msg_node>\n\t\t<uuid>9e1f42615265c35e7857208bd5c9bab5_</uuid>\n\t\t<risk-file-flag />\n\t\t<risk-file-md5-list />\n\t</sec_msg_node>\n\t<silence>0</silence>\n\t<membercount>62</membercount>\n\t<signature>N0_V1_NpAtbp3U|v1_3r0E9RXw</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'PushContent': '【微信红包】收到一个微信红包 : 啥意思', 'NewMsgId': 565827244431775081, 'MsgSeq': 871387708}
2025-07-21 15:49:32 | DEBUG | 从群聊消息中提取发送者: zll953369865
2025-07-21 15:49:32 | DEBUG | 使用已解析的XML处理引用消息
2025-07-21 15:49:32 | INFO | 收到引用消息: 消息ID:2121596315 来自:48097389945@chatroom 发送人:zll953369865 内容:啥意思 引用类型:1
2025-07-21 15:49:32 | INFO | [DouBaoImageToImage] 收到引用消息: 啥意思
2025-07-21 15:49:32 | INFO | [TimerTask] 收到引用消息调试信息:
2025-07-21 15:49:32 | INFO |   - 消息内容: 啥意思
2025-07-21 15:49:32 | INFO |   - 群组ID: 48097389945@chatroom
2025-07-21 15:49:32 | INFO |   - 发送人: zll953369865
2025-07-21 15:49:32 | INFO |   - 引用信息: {'MsgType': 1, 'Content': '想想10群就是这么拉成11群的', 'Msgid': '1230748475114759820', 'NewMsgId': '1230748475114759820', 'ToWxid': 'wxid_4usgcju5ey9q29', 'FromWxid': '48097389945@chatroom', 'Nickname': '喵小叶', 'MsgSource': '<msgsource>\n    <sec_msg_node>\n        <alnode>\n            <fr>1</fr>\n        </alnode>\n    </sec_msg_node>\n    <pua>1</pua>\n    <silence>1</silence>\n    <membercount>62</membercount>\n    <signature>N0_V1_YlhHxS5f|v1_jdBkMLqA</signature>\n    <tmp_node>\n        <publisher-id />\n    </tmp_node>\n</msgsource>\n', 'Createtime': '1753084153', 'SenderWxid': 'zll953369865'}
2025-07-21 15:49:32 | INFO |   - 引用消息ID: 
2025-07-21 15:49:32 | INFO |   - 引用消息类型: 
2025-07-21 15:49:32 | INFO |   - 引用消息内容: 想想10群就是这么拉成11群的
2025-07-21 15:49:32 | INFO |   - 引用消息发送人: zll953369865
2025-07-21 15:49:37 | DEBUG | 收到消息: {'MsgId': 1823380046, 'FromUserName': {'string': '27852221909@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 1, 'Content': {'string': 'wxid_5kipwrzramxr22:\n一首比一首催眠'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1753084186, 'MsgSource': '<msgsource>\n\t<bizflag>0</bizflag>\n\t<pua>1</pua>\n\t<eggIncluded>1</eggIncluded>\n\t<silence>1</silence>\n\t<membercount>146</membercount>\n\t<signature>N0_V1_alwIwOYh|v1_Su9pDCR7</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'NewMsgId': 7088673571900979973, 'MsgSeq': 871387709}
2025-07-21 15:49:37 | INFO | 收到文本消息: 消息ID:1823380046 来自:27852221909@chatroom 发送人:wxid_5kipwrzramxr22 @:[] 内容:一首比一首催眠
2025-07-21 15:49:37 | DEBUG | 处理消息内容: '一首比一首催眠'
2025-07-21 15:49:37 | DEBUG | 消息内容 '一首比一首催眠' 不匹配任何命令，忽略
2025-07-21 15:49:38 | DEBUG | 收到消息: {'MsgId': 28272576, 'FromUserName': {'string': '48097389945@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 1, 'Content': {'string': 'zll953369865:\n没明白'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1753084186, 'MsgSource': '<msgsource>\n\t<sec_msg_node>\n\t\t<alnode>\n\t\t\t<fr>1</fr>\n\t\t</alnode>\n\t</sec_msg_node>\n\t<pua>1</pua>\n\t<silence>0</silence>\n\t<membercount>62</membercount>\n\t<signature>N0_V1_Up3j9Stv|v1_VgBr6jB7</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'PushContent': '【微信红包】收到一个微信红包 : 没明白', 'NewMsgId': 8521602059628712797, 'MsgSeq': 871387710}
2025-07-21 15:49:38 | INFO | 收到文本消息: 消息ID:28272576 来自:48097389945@chatroom 发送人:zll953369865 @:[] 内容:没明白
2025-07-21 15:49:39 | DEBUG | 处理消息内容: '没明白'
2025-07-21 15:49:39 | DEBUG | 消息内容 '没明白' 不匹配任何命令，忽略
2025-07-21 15:49:41 | DEBUG | 收到消息: {'MsgId': 1816323339, 'FromUserName': {'string': '48097389945@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 1, 'Content': {'string': 'zll953369865:\n不能欺负你？'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1753084189, 'MsgSource': '<msgsource>\n\t<sec_msg_node>\n\t\t<alnode>\n\t\t\t<fr>1</fr>\n\t\t</alnode>\n\t</sec_msg_node>\n\t<pua>1</pua>\n\t<silence>0</silence>\n\t<membercount>62</membercount>\n\t<signature>N0_V1_zwtHPMfi|v1_MCpM2ziD</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'PushContent': '【微信红包】收到一个微信红包 : 不能欺负你？', 'NewMsgId': 3045073107825808084, 'MsgSeq': 871387711}
2025-07-21 15:49:41 | INFO | 收到文本消息: 消息ID:1816323339 来自:48097389945@chatroom 发送人:zll953369865 @:[] 内容:不能欺负你？
2025-07-21 15:49:41 | DEBUG | 处理消息内容: '不能欺负你？'
2025-07-21 15:49:41 | DEBUG | 消息内容 '不能欺负你？' 不匹配任何命令，忽略
2025-07-21 15:49:43 | DEBUG | 收到消息: {'MsgId': 2090854785, 'FromUserName': {'string': '27852221909@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 47, 'Content': {'string': 'wxid_5kipwrzramxr22:\n<msg><emoji fromusername="wxid_5kipwrzramxr22" tousername="27852221909@chatroom" type="2" idbuffer="media:0_0" md5="ebdee103ba92d3a093f5aed64746928d" len="204426" productid="" androidmd5="ebdee103ba92d3a093f5aed64746928d" androidlen="204426" s60v3md5="ebdee103ba92d3a093f5aed64746928d" s60v3len="204426" s60v5md5="ebdee103ba92d3a093f5aed64746928d" s60v5len="204426" cdnurl="http://wxapp.tc.qq.com/262/20304/stodownload?m=ebdee103ba92d3a093f5aed64746928d&amp;filekey=30350201010421301f02020106040253480410ebdee103ba92d3a093f5aed64746928d0203031e8a040d00000004627466730000000132&amp;hy=SH&amp;storeid=265fbc1f3000a8c8d1593f1020000010600004f505348023468e0b794f8b89&amp;bizid=1023" designerid="" thumburl="" encrypturl="http://wxapp.tc.qq.com/262/20304/stodownload?m=aaceb4efcb340832705174491203cac3&amp;filekey=30350201010421301f02020106040253480410aaceb4efcb340832705174491203cac30203031e90040d00000004627466730000000132&amp;hy=SH&amp;storeid=265fbc1f3000c4ee11593f1020000010600004f5053482f23a03156d517d26&amp;bizid=1023" aeskey="6b4b24b91b285186a8b1cc05a79d5393" externurl="http://wxapp.tc.qq.com/262/20304/stodownload?m=7f39059f601d2b20e9c70ee1ed24370e&amp;filekey=30350201010421301f020201060402534804107f39059f601d2b20e9c70ee1ed24370e020300a990040d00000004627466730000000132&amp;hy=SH&amp;storeid=265fbc26f000c0a8954422a1c0000010600004f50534809307bd1e7e8b10b0&amp;bizid=1023" externmd5="84f161515c9d4e851617b6f43e8ecb39" width="640" height="639" tpurl="" tpauthkey="" attachedtext="" attachedtextcolor="" lensid="" emojiattr="" linkid="" desc=""></emoji><gameext type="0" content="0"></gameext></msg>'}, 'Status': 3, 'ImgStatus': 2, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1753084192, 'MsgSource': '<msgsource>\n\t<silence>1</silence>\n\t<membercount>146</membercount>\n\t<signature>N0_V1_J3Zbrk7r|v1_bwdO1IdA</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'NewMsgId': 8316894450744433754, 'MsgSeq': 871387712}
2025-07-21 15:49:43 | INFO | 收到表情消息: 消息ID:2090854785 来自:27852221909@chatroom 发送人:wxid_5kipwrzramxr22 MD5:ebdee103ba92d3a093f5aed64746928d 大小:204426
2025-07-21 15:49:43 | DEBUG | [DoubaoImageRecognition] 缓存表情消息: 8316894450744433754
2025-07-21 15:49:50 | DEBUG | 收到消息: {'MsgId': 68905025, 'FromUserName': {'string': '47325400669@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 1, 'Content': {'string': 'wxid_fh84okl6f5wp22:\n鸭子的进化史是一段从恐龙后裔逐步适应水生环境、最终分化出野生种群并被人类驯化的漫长历程。其演化主线围绕生态适应（如湿地环境、滤食习性）和物种分化（从雁形目共同祖先到现代鸭类）展开，同时人类驯化进一步塑造了家鸭的特征。以下是分阶段的详细梳理：\n\n\n### 一、起源：从恐龙到早期鸟类（约1.5亿-6600万年前）  \n鸭子属于鸟类，而鸟类的直接祖先被认为是中生代的兽脚类恐龙（如迅猛龙）。这一阶段的关键演化事件为：  \n- 恐龙向鸟类的过渡：约1.5亿年前的始祖鸟（Archaeopteryx）是最早的鸟类化石之一，虽保留恐龙特征（牙齿、尾椎骨），但已具备羽毛和翅膀，证明鸟类由恐龙演化而来。  \n- 白垩纪灭绝事件：6600万年前小行星撞击导致非鸟恐龙灭绝，而鸟类因体型小、适应力强得以幸存，为后续辐射演化奠定基础。  \n\n\n### 二、雁形目的诞生：适应水生环境的早期分化（古新世-始新世，6600万-2300万年前）  \n恐龙灭绝后，鸟类进入“生态空位”期，雁形目（Anseriformes）的祖先开始出现并适应水生环境：  \n- 早期雁形目化石：始新世（约5000万年前）的黄昏鸟（Hesperornis）虽非鸭类直接祖先，但已具备潜水能力，说明雁形目早期对水生环境的适应；更接近鸭类的早期化石如Eonessa（始新世晚期），体型类似小型鸭，已有扁平的喙和适合游泳的骨骼结构。  \n- 关键特征的演化：  \n  - 蹼足：脚趾间的蹼逐渐形成，便于划水（自然选择中，蹼足更利于在湿地移动的个体存活）；  \n  - 滤食喙：喙部变宽、边缘出现锯齿状“栉板”，可过滤水中的水生植物、昆虫和小型甲壳类（替代了早期鸟类的尖锐喙，适应杂食性）。  \n\n\n### 三、鸭科的形成与分化（渐新世-中新世，3400万-530万年前）  \n随着全球湿地（湖泊、河流、沼泽）的扩张，雁形目分化出多个科，鸭科（Anatidae） 逐渐形成，并演化出多样化的鸭类：  \n- 生态位适应：不同鸭类开始适应不同的湿地环境，例如：  \n  - 河鸭属（Anas）：如绿头鸭，栖息于淡水河湖，喙扁平适合滤食水草和小型生物；  \n  - 潜鸭属（Aythya）：如红头潜鸭，擅长潜水捕食，身体更紧凑，脚的位置靠后（利于潜水划水）；  \n  - 栖鸭属（Cairina）：如疣鼻栖鸭，适应树栖环境，爪子更锋利，可在树上筑巢。  \n- 形态特征强化：翅膀短而宽（适合短距离快速飞行，逃避天敌）、骨骼中空（减轻体重）、尾脂腺发达（分泌油脂防水，适应长时间水中活动）。  \n\n\n### 四、接近现代的鸭类：上新世-更新世（530万-1.1万年前）  \n这一时期气候波动（冰期与间冰期交替）推动鸭类进一步扩散和适应：  \n- 分布扩展：冰期时，鸭类随冰川进退迁移，逐渐适应从热带到温带的广泛气候，形成全球性分布（除南极外均有分布）。  \n- 与人类的早期互动：更新世晚期，人类进入狩猎采集阶段，鸭类因肉质和蛋成为捕猎对象，部分野鸭（如绿头鸭）开始出现在人类聚居地附近，为后续驯化埋下伏笔。  \n\n\n### 五、人类驯化：家鸭的诞生（约8000-6000年前）  \n新石器时代，人类进入农业社会后，开始驯化野生动物，绿头鸭（Anas platyrhynchos） 成为家鸭的主要祖先（少数家鸭源自斑嘴鸭）：  \n- 驯化动力：人类选择温顺、不擅飞（便于管理）、产蛋多、生长快的个体繁殖，逐步改变其野生习性。  \n- 驯化后的变化：  \n  - 飞行能力退化：翅膀变短、胸肌减弱，体重增加（家鸭平均体重是野鸭的1.5-2倍）；  \n  - 繁殖习性改变：野鸭一年产卵10-15枚，家鸭可产卵100-300枚，且失去季节性繁殖限制；  \n  - 外形多样化：通过人工选择，出现白、黑、花等羽色，喙和体型也更丰富（如北京鸭的肥胖体型）。  \n\n\n### 六、现代鸭类：自然与人工选择的共同结果  \n如今，鸭类包括野生种群（如绿头鸭、斑嘴鸭、秋沙鸭等，约120种）和家养品种（如北京鸭、番鸭、高邮鸭等，数百个品种）：  \n- 野生鸭类仍保留迁徙、潜水、快速飞行等适应野外生存的特征；  \n- 家鸭则成为人类重要的肉、蛋来源，其演化完全受人工选择驱动，与野生祖先差异显著。  \n\n\n### 总结：鸭子进化的核心逻辑  \n鸭子的演化是“环境适应”与“选择压力”共同作用的结果：从恐龙后裔到适应水生的鸟类，再到被人类驯化的家禽，每一步变化都围绕“生存与繁衍”展开——湿地环境塑造了它们的形态和习性，而人类需求则进一步改写了部分种群的进化轨迹。'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1753084199, 'MsgSource': '<msgsource>\n\t<silence>0</silence>\n\t<membercount>219</membercount>\n\t<signature>N0_V1_FbgXcSp2|v1_7GWk2vAt</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'PushContent': '阿猪米德 : 鸭子的进化史是一段从恐龙后裔逐步适应水生环境、最终分化出野生...', 'NewMsgId': 4055015163966176977, 'MsgSeq': 871387713}
2025-07-21 15:49:50 | INFO | 收到文本消息: 消息ID:68905025 来自:47325400669@chatroom 发送人:wxid_fh84okl6f5wp22 @:[] 内容:鸭子的进化史是一段从恐龙后裔逐步适应水生环境、最终分化出野生种群并被人类驯化的漫长历程。其演化主线围绕生态适应（如湿地环境、滤食习性）和物种分化（从雁形目共同祖先到现代鸭类）展开，同时人类驯化进一步塑造了家鸭的特征。以下是分阶段的详细梳理：


### 一、起源：从恐龙到早期鸟类（约1.5亿-6600万年前）  
鸭子属于鸟类，而鸟类的直接祖先被认为是中生代的兽脚类恐龙（如迅猛龙）。这一阶段的关键演化事件为：  
- 恐龙向鸟类的过渡：约1.5亿年前的始祖鸟（Archaeopteryx）是最早的鸟类化石之一，虽保留恐龙特征（牙齿、尾椎骨），但已具备羽毛和翅膀，证明鸟类由恐龙演化而来。  
- 白垩纪灭绝事件：6600万年前小行星撞击导致非鸟恐龙灭绝，而鸟类因体型小、适应力强得以幸存，为后续辐射演化奠定基础。  


### 二、雁形目的诞生：适应水生环境的早期分化（古新世-始新世，6600万-2300万年前）  
恐龙灭绝后，鸟类进入“生态空位”期，雁形目（Anseriformes）的祖先开始出现并适应水生环境：  
- 早期雁形目化石：始新世（约5000万年前）的黄昏鸟（Hesperornis）虽非鸭类直接祖先，但已具备潜水能力，说明雁形目早期对水生环境的适应；更接近鸭类的早期化石如Eonessa（始新世晚期），体型类似小型鸭，已有扁平的喙和适合游泳的骨骼结构。  
- 关键特征的演化：  
  - 蹼足：脚趾间的蹼逐渐形成，便于划水（自然选择中，蹼足更利于在湿地移动的个体存活）；  
  - 滤食喙：喙部变宽、边缘出现锯齿状“栉板”，可过滤水中的水生植物、昆虫和小型甲壳类（替代了早期鸟类的尖锐喙，适应杂食性）。  


### 三、鸭科的形成与分化（渐新世-中新世，3400万-530万年前）  
随着全球湿地（湖泊、河流、沼泽）的扩张，雁形目分化出多个科，鸭科（Anatidae） 逐渐形成，并演化出多样化的鸭类：  
- 生态位适应：不同鸭类开始适应不同的湿地环境，例如：  
  - 河鸭属（Anas）：如绿头鸭，栖息于淡水河湖，喙扁平适合滤食水草和小型生物；  
  - 潜鸭属（Aythya）：如红头潜鸭，擅长潜水捕食，身体更紧凑，脚的位置靠后（利于潜水划水）；  
  - 栖鸭属（Cairina）：如疣鼻栖鸭，适应树栖环境，爪子更锋利，可在树上筑巢。  
- 形态特征强化：翅膀短而宽（适合短距离快速飞行，逃避天敌）、骨骼中空（减轻体重）、尾脂腺发达（分泌油脂防水，适应长时间水中活动）。  


### 四、接近现代的鸭类：上新世-更新世（530万-1.1万年前）  
这一时期气候波动（冰期与间冰期交替）推动鸭类进一步扩散和适应：  
- 分布扩展：冰期时，鸭类随冰川进退迁移，逐渐适应从热带到温带的广泛气候，形成全球性分布（除南极外均有分布）。  
- 与人类的早期互动：更新世晚期，人类进入狩猎采集阶段，鸭类因肉质和蛋成为捕猎对象，部分野鸭（如绿头鸭）开始出现在人类聚居地附近，为后续驯化埋下伏笔。  


### 五、人类驯化：家鸭的诞生（约8000-6000年前）  
新石器时代，人类进入农业社会后，开始驯化野生动物，绿头鸭（Anas platyrhynchos） 成为家鸭的主要祖先（少数家鸭源自斑嘴鸭）：  
- 驯化动力：人类选择温顺、不擅飞（便于管理）、产蛋多、生长快的个体繁殖，逐步改变其野生习性。  
- 驯化后的变化：  
  - 飞行能力退化：翅膀变短、胸肌减弱，体重增加（家鸭平均体重是野鸭的1.5-2倍）；  
  - 繁殖习性改变：野鸭一年产卵10-15枚，家鸭可产卵100-300枚，且失去季节性繁殖限制；  
  - 外形多样化：通过人工选择，出现白、黑、花等羽色，喙和体型也更丰富（如北京鸭的肥胖体型）。  


### 六、现代鸭类：自然与人工选择的共同结果  
如今，鸭类包括野生种群（如绿头鸭、斑嘴鸭、秋沙鸭等，约120种）和家养品种（如北京鸭、番鸭、高邮鸭等，数百个品种）：  
- 野生鸭类仍保留迁徙、潜水、快速飞行等适应野外生存的特征；  
- 家鸭则成为人类重要的肉、蛋来源，其演化完全受人工选择驱动，与野生祖先差异显著。  


### 总结：鸭子进化的核心逻辑  
鸭子的演化是“环境适应”与“选择压力”共同作用的结果：从恐龙后裔到适应水生的鸟类，再到被人类驯化的家禽，每一步变化都围绕“生存与繁衍”展开——湿地环境塑造了它们的形态和习性，而人类需求则进一步改写了部分种群的进化轨迹。
2025-07-21 15:49:50 | DEBUG | 处理消息内容: '鸭子的进化史是一段从恐龙后裔逐步适应水生环境、最终分化出野生种群并被人类驯化的漫长历程。其演化主线围绕生态适应（如湿地环境、滤食习性）和物种分化（从雁形目共同祖先到现代鸭类）展开，同时人类驯化进一步塑造了家鸭的特征。以下是分阶段的详细梳理：


### 一、起源：从恐龙到早期鸟类（约1.5亿-6600万年前）  
鸭子属于鸟类，而鸟类的直接祖先被认为是中生代的兽脚类恐龙（如迅猛龙）。这一阶段的关键演化事件为：  
- 恐龙向鸟类的过渡：约1.5亿年前的始祖鸟（Archaeopteryx）是最早的鸟类化石之一，虽保留恐龙特征（牙齿、尾椎骨），但已具备羽毛和翅膀，证明鸟类由恐龙演化而来。  
- 白垩纪灭绝事件：6600万年前小行星撞击导致非鸟恐龙灭绝，而鸟类因体型小、适应力强得以幸存，为后续辐射演化奠定基础。  


### 二、雁形目的诞生：适应水生环境的早期分化（古新世-始新世，6600万-2300万年前）  
恐龙灭绝后，鸟类进入“生态空位”期，雁形目（Anseriformes）的祖先开始出现并适应水生环境：  
- 早期雁形目化石：始新世（约5000万年前）的黄昏鸟（Hesperornis）虽非鸭类直接祖先，但已具备潜水能力，说明雁形目早期对水生环境的适应；更接近鸭类的早期化石如Eonessa（始新世晚期），体型类似小型鸭，已有扁平的喙和适合游泳的骨骼结构。  
- 关键特征的演化：  
  - 蹼足：脚趾间的蹼逐渐形成，便于划水（自然选择中，蹼足更利于在湿地移动的个体存活）；  
  - 滤食喙：喙部变宽、边缘出现锯齿状“栉板”，可过滤水中的水生植物、昆虫和小型甲壳类（替代了早期鸟类的尖锐喙，适应杂食性）。  


### 三、鸭科的形成与分化（渐新世-中新世，3400万-530万年前）  
随着全球湿地（湖泊、河流、沼泽）的扩张，雁形目分化出多个科，鸭科（Anatidae） 逐渐形成，并演化出多样化的鸭类：  
- 生态位适应：不同鸭类开始适应不同的湿地环境，例如：  
  - 河鸭属（Anas）：如绿头鸭，栖息于淡水河湖，喙扁平适合滤食水草和小型生物；  
  - 潜鸭属（Aythya）：如红头潜鸭，擅长潜水捕食，身体更紧凑，脚的位置靠后（利于潜水划水）；  
  - 栖鸭属（Cairina）：如疣鼻栖鸭，适应树栖环境，爪子更锋利，可在树上筑巢。  
- 形态特征强化：翅膀短而宽（适合短距离快速飞行，逃避天敌）、骨骼中空（减轻体重）、尾脂腺发达（分泌油脂防水，适应长时间水中活动）。  


### 四、接近现代的鸭类：上新世-更新世（530万-1.1万年前）  
这一时期气候波动（冰期与间冰期交替）推动鸭类进一步扩散和适应：  
- 分布扩展：冰期时，鸭类随冰川进退迁移，逐渐适应从热带到温带的广泛气候，形成全球性分布（除南极外均有分布）。  
- 与人类的早期互动：更新世晚期，人类进入狩猎采集阶段，鸭类因肉质和蛋成为捕猎对象，部分野鸭（如绿头鸭）开始出现在人类聚居地附近，为后续驯化埋下伏笔。  


### 五、人类驯化：家鸭的诞生（约8000-6000年前）  
新石器时代，人类进入农业社会后，开始驯化野生动物，绿头鸭（Anas platyrhynchos） 成为家鸭的主要祖先（少数家鸭源自斑嘴鸭）：  
- 驯化动力：人类选择温顺、不擅飞（便于管理）、产蛋多、生长快的个体繁殖，逐步改变其野生习性。  
- 驯化后的变化：  
  - 飞行能力退化：翅膀变短、胸肌减弱，体重增加（家鸭平均体重是野鸭的1.5-2倍）；  
  - 繁殖习性改变：野鸭一年产卵10-15枚，家鸭可产卵100-300枚，且失去季节性繁殖限制；  
  - 外形多样化：通过人工选择，出现白、黑、花等羽色，喙和体型也更丰富（如北京鸭的肥胖体型）。  


### 六、现代鸭类：自然与人工选择的共同结果  
如今，鸭类包括野生种群（如绿头鸭、斑嘴鸭、秋沙鸭等，约120种）和家养品种（如北京鸭、番鸭、高邮鸭等，数百个品种）：  
- 野生鸭类仍保留迁徙、潜水、快速飞行等适应野外生存的特征；  
- 家鸭则成为人类重要的肉、蛋来源，其演化完全受人工选择驱动，与野生祖先差异显著。  


### 总结：鸭子进化的核心逻辑  
鸭子的演化是“环境适应”与“选择压力”共同作用的结果：从恐龙后裔到适应水生的鸟类，再到被人类驯化的家禽，每一步变化都围绕“生存与繁衍”展开——湿地环境塑造了它们的形态和习性，而人类需求则进一步改写了部分种群的进化轨迹。'
2025-07-21 15:49:50 | DEBUG | 消息内容 '鸭子的进化史是一段从恐龙后裔逐步适应水生环境、最终分化出野生种群并被人类驯化的漫长历程。其演化主线围绕生态适应（如湿地环境、滤食习性）和物种分化（从雁形目共同祖先到现代鸭类）展开，同时人类驯化进一步塑造了家鸭的特征。以下是分阶段的详细梳理：


### 一、起源：从恐龙到早期鸟类（约1.5亿-6600万年前）  
鸭子属于鸟类，而鸟类的直接祖先被认为是中生代的兽脚类恐龙（如迅猛龙）。这一阶段的关键演化事件为：  
- 恐龙向鸟类的过渡：约1.5亿年前的始祖鸟（Archaeopteryx）是最早的鸟类化石之一，虽保留恐龙特征（牙齿、尾椎骨），但已具备羽毛和翅膀，证明鸟类由恐龙演化而来。  
- 白垩纪灭绝事件：6600万年前小行星撞击导致非鸟恐龙灭绝，而鸟类因体型小、适应力强得以幸存，为后续辐射演化奠定基础。  


### 二、雁形目的诞生：适应水生环境的早期分化（古新世-始新世，6600万-2300万年前）  
恐龙灭绝后，鸟类进入“生态空位”期，雁形目（Anseriformes）的祖先开始出现并适应水生环境：  
- 早期雁形目化石：始新世（约5000万年前）的黄昏鸟（Hesperornis）虽非鸭类直接祖先，但已具备潜水能力，说明雁形目早期对水生环境的适应；更接近鸭类的早期化石如Eonessa（始新世晚期），体型类似小型鸭，已有扁平的喙和适合游泳的骨骼结构。  
- 关键特征的演化：  
  - 蹼足：脚趾间的蹼逐渐形成，便于划水（自然选择中，蹼足更利于在湿地移动的个体存活）；  
  - 滤食喙：喙部变宽、边缘出现锯齿状“栉板”，可过滤水中的水生植物、昆虫和小型甲壳类（替代了早期鸟类的尖锐喙，适应杂食性）。  


### 三、鸭科的形成与分化（渐新世-中新世，3400万-530万年前）  
随着全球湿地（湖泊、河流、沼泽）的扩张，雁形目分化出多个科，鸭科（Anatidae） 逐渐形成，并演化出多样化的鸭类：  
- 生态位适应：不同鸭类开始适应不同的湿地环境，例如：  
  - 河鸭属（Anas）：如绿头鸭，栖息于淡水河湖，喙扁平适合滤食水草和小型生物；  
  - 潜鸭属（Aythya）：如红头潜鸭，擅长潜水捕食，身体更紧凑，脚的位置靠后（利于潜水划水）；  
  - 栖鸭属（Cairina）：如疣鼻栖鸭，适应树栖环境，爪子更锋利，可在树上筑巢。  
- 形态特征强化：翅膀短而宽（适合短距离快速飞行，逃避天敌）、骨骼中空（减轻体重）、尾脂腺发达（分泌油脂防水，适应长时间水中活动）。  


### 四、接近现代的鸭类：上新世-更新世（530万-1.1万年前）  
这一时期气候波动（冰期与间冰期交替）推动鸭类进一步扩散和适应：  
- 分布扩展：冰期时，鸭类随冰川进退迁移，逐渐适应从热带到温带的广泛气候，形成全球性分布（除南极外均有分布）。  
- 与人类的早期互动：更新世晚期，人类进入狩猎采集阶段，鸭类因肉质和蛋成为捕猎对象，部分野鸭（如绿头鸭）开始出现在人类聚居地附近，为后续驯化埋下伏笔。  


### 五、人类驯化：家鸭的诞生（约8000-6000年前）  
新石器时代，人类进入农业社会后，开始驯化野生动物，绿头鸭（Anas platyrhynchos） 成为家鸭的主要祖先（少数家鸭源自斑嘴鸭）：  
- 驯化动力：人类选择温顺、不擅飞（便于管理）、产蛋多、生长快的个体繁殖，逐步改变其野生习性。  
- 驯化后的变化：  
  - 飞行能力退化：翅膀变短、胸肌减弱，体重增加（家鸭平均体重是野鸭的1.5-2倍）；  
  - 繁殖习性改变：野鸭一年产卵10-15枚，家鸭可产卵100-300枚，且失去季节性繁殖限制；  
  - 外形多样化：通过人工选择，出现白、黑、花等羽色，喙和体型也更丰富（如北京鸭的肥胖体型）。  


### 六、现代鸭类：自然与人工选择的共同结果  
如今，鸭类包括野生种群（如绿头鸭、斑嘴鸭、秋沙鸭等，约120种）和家养品种（如北京鸭、番鸭、高邮鸭等，数百个品种）：  
- 野生鸭类仍保留迁徙、潜水、快速飞行等适应野外生存的特征；  
- 家鸭则成为人类重要的肉、蛋来源，其演化完全受人工选择驱动，与野生祖先差异显著。  


### 总结：鸭子进化的核心逻辑  
鸭子的演化是“环境适应”与“选择压力”共同作用的结果：从恐龙后裔到适应水生的鸟类，再到被人类驯化的家禽，每一步变化都围绕“生存与繁衍”展开——湿地环境塑造了它们的形态和习性，而人类需求则进一步改写了部分种群的进化轨迹。' 不匹配任何命令，忽略
2025-07-21 15:49:53 | DEBUG | 收到消息: {'MsgId': 1416306261, 'FromUserName': {'string': '47325400669@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 10002, 'Content': {'string': '47325400669@chatroom:\n<sysmsg type="secmsg">\n  <secmsg>\n    <session>47325400669@chatroom</session>\n    <newmsgid>4055015163966176977</newmsgid>\n    <sec_msg_node>\n      <sfn></sfn>\n      <fd></fd>\n      <show-h5></show-h5>\n      <clip-len></clip-len>\n      <share-tip-wording><![CDATA[]]></share-tip-wording>\n      <share-tip-url><![CDATA[]]></share-tip-url>\n      <fold-reduce><![CDATA[1]]></fold-reduce>\n      <block-range></block-range>\n      <media-to-emoji></media-to-emoji>\n      <bubble-type></bubble-type>\n      <preview-type></preview-type>\n      <url-click-type></url-click-type>\n      <sec-ctrl-flag></sec-ctrl-flag>\n      <fr-type></fr-type>\n      <risk-file-flag></risk-file-flag>\n      <risk-file-md5-list><![CDATA[]]></risk-file-md5-list>\n      <risk-warning-url><![CDATA[]]></risk-warning-url>\n      <unread-media-expired></unread-media-expired>\n    </sec_msg_node>\n  </secmsg>\n</sysmsg>'}, 'Status': 4, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1753084200, 'MsgSource': '<msgsource>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'NewMsgId': 3237540951737911366, 'MsgSeq': 871387714}
2025-07-21 15:49:53 | DEBUG | 系统消息类型: secmsg
2025-07-21 15:49:53 | INFO | 未知的系统消息类型: {'MsgId': 1416306261, 'ToWxid': 'wxid_4usgcju5ey9q29', 'MsgType': 10002, 'Content': '\n<sysmsg type="secmsg">\n  <secmsg>\n    <session>47325400669@chatroom</session>\n    <newmsgid>4055015163966176977</newmsgid>\n    <sec_msg_node>\n      <sfn></sfn>\n      <fd></fd>\n      <show-h5></show-h5>\n      <clip-len></clip-len>\n      <share-tip-wording><![CDATA[]]></share-tip-wording>\n      <share-tip-url><![CDATA[]]></share-tip-url>\n      <fold-reduce><![CDATA[1]]></fold-reduce>\n      <block-range></block-range>\n      <media-to-emoji></media-to-emoji>\n      <bubble-type></bubble-type>\n      <preview-type></preview-type>\n      <url-click-type></url-click-type>\n      <sec-ctrl-flag></sec-ctrl-flag>\n      <fr-type></fr-type>\n      <risk-file-flag></risk-file-flag>\n      <risk-file-md5-list><![CDATA[]]></risk-file-md5-list>\n      <risk-warning-url><![CDATA[]]></risk-warning-url>\n      <unread-media-expired></unread-media-expired>\n    </sec_msg_node>\n  </secmsg>\n</sysmsg>', 'Status': 4, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1753084200, 'MsgSource': '<msgsource>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'NewMsgId': 3237540951737911366, 'MsgSeq': 871387714, 'FromWxid': '47325400669@chatroom', 'IsGroup': True, 'SenderWxid': '47325400669@chatroom'}
2025-07-21 15:49:54 | DEBUG | 收到消息: {'MsgId': 1524804756, 'FromUserName': {'string': '27852221909@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 47, 'Content': {'string': 'wxid_x4s6k999g6qg22:\n<msg><emoji fromusername="wxid_x4s6k999g6qg22" tousername="27852221909@chatroom" type="2" idbuffer="media:0_0" md5="917f700a493e0e4bc30d7f7592bbcb5e" len="1269409" productid="" androidmd5="917f700a493e0e4bc30d7f7592bbcb5e" androidlen="1269409" s60v3md5="917f700a493e0e4bc30d7f7592bbcb5e" s60v3len="1269409" s60v5md5="917f700a493e0e4bc30d7f7592bbcb5e" s60v5len="1269409" cdnurl="http://vweixinf.tc.qq.com/110/20401/stodownload?m=917f700a493e0e4bc30d7f7592bbcb5e&amp;filekey=30440201010430302e02016e04025348042039313766373030613439336530653462633330643766373539326262636235650203135ea1040d00000004627466730000000132&amp;hy=SH&amp;storeid=267ebb69100092717a5d6dab50000006e01004fb1534820ce91b1500d04f2e&amp;ef=1&amp;bizid=1022" designerid="" thumburl="" encrypturl="http://vweixinf.tc.qq.com/110/20402/stodownload?m=da003382f14ab370fbb1a3356a964745&amp;filekey=30440201010430302e02016e04025348042064613030333338326631346162333730666262316133333536613936343734350203135eb0040d00000004627466730000000132&amp;hy=SH&amp;storeid=267ebb691000b2e9aa5d6dab50000006e02004fb2534820ce91b1500d04f5a&amp;ef=2&amp;bizid=1022" aeskey="e6218e27599f4c76a354927025d3ebdb" externurl="http://vweixinf.tc.qq.com/110/20403/stodownload?m=00a909f7ccf92ab327a136f29f787dac&amp;filekey=30440201010430302e02016e0402534804203030613930396637636366393261623332376131333666323966373837646163020301a110040d00000004627466730000000132&amp;hy=SH&amp;storeid=267ebb691000cb2efa5d6dab50000006e03004fb3534820ce91b1500d04f69&amp;ef=3&amp;bizid=1022" externmd5="d1e90465ee834f2adbe5be15cb819fec" width="240" height="235" tpurl="" tpauthkey="" attachedtext="" attachedtextcolor="" lensid="" emojiattr="" linkid="" desc=""></emoji><gameext type="0" content="0"></gameext></msg>'}, 'Status': 3, 'ImgStatus': 2, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1753084202, 'MsgSource': '<msgsource>\n\t<silence>1</silence>\n\t<membercount>146</membercount>\n\t<signature>N0_V1_o5QYXvoH|v1_wZLShpK1</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'NewMsgId': 5188794251335027931, 'MsgSeq': 871387715}
2025-07-21 15:49:54 | INFO | 收到表情消息: 消息ID:1524804756 来自:27852221909@chatroom 发送人:wxid_x4s6k999g6qg22 MD5:917f700a493e0e4bc30d7f7592bbcb5e 大小:1269409
2025-07-21 15:49:54 | DEBUG | [DoubaoImageRecognition] 缓存表情消息: 5188794251335027931
2025-07-21 15:50:00 | DEBUG | 收到消息: {'MsgId': 1315337652, 'FromUserName': {'string': '48097389945@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 1, 'Content': {'string': 'wxid_84mmq4cu7ita22:\n小爱现在遥遥领先群还有多少个'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1753084209, 'MsgSource': '<msgsource>\n\t<sec_msg_node>\n\t\t<alnode>\n\t\t\t<fr>1</fr>\n\t\t</alnode>\n\t</sec_msg_node>\n\t<pua>1</pua>\n\t<silence>0</silence>\n\t<membercount>62</membercount>\n\t<signature>N0_V1_c4BoFvN2|v1_767C2I0B</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'PushContent': '喵小叶 : 小爱现在遥遥领先群还有多少个', 'NewMsgId': 8426677354063496050, 'MsgSeq': 871387716}
2025-07-21 15:50:00 | INFO | 收到文本消息: 消息ID:1315337652 来自:48097389945@chatroom 发送人:wxid_84mmq4cu7ita22 @:[] 内容:小爱现在遥遥领先群还有多少个
2025-07-21 15:50:00 | DEBUG | 处理消息内容: '小爱现在遥遥领先群还有多少个'
2025-07-21 15:50:00 | DEBUG | 消息内容 '小爱现在遥遥领先群还有多少个' 不匹配任何命令，忽略
2025-07-21 15:50:10 | DEBUG | 收到消息: {'MsgId': 1682455429, 'FromUserName': {'string': '48097389945@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 1, 'Content': {'string': 'last--exile:\n#女生经常穿黑色休闲裤代表什么意思'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1753084219, 'MsgSource': '<msgsource>\n\t<pua>1</pua>\n\t<silence>0</silence>\n\t<membercount>62</membercount>\n\t<signature>N0_V1_LIt+orTt|v1_u4yp2cBX</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'PushContent': '亮 : #女生经常穿黑色休闲裤代表什么意思', 'NewMsgId': 914860073940179989, 'MsgSeq': 871387717}
2025-07-21 15:50:10 | INFO | 收到文本消息: 消息ID:1682455429 来自:48097389945@chatroom 发送人:last--exile @:[] 内容:#女生经常穿黑色休闲裤代表什么意思
2025-07-21 15:50:11 | DEBUG | 处理消息内容: '#女生经常穿黑色休闲裤代表什么意思'
2025-07-21 15:50:11 | DEBUG | 消息内容 '#女生经常穿黑色休闲裤代表什么意思' 不匹配任何命令，忽略
2025-07-21 15:50:13 | DEBUG | 收到消息: {'MsgId': 673476787, 'FromUserName': {'string': '48097389945@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 1, 'Content': {'string': 'wxid_84mmq4cu7ita22:\n我可以去别的群聊天玩，不跟你们玩了哼'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1753084220, 'MsgSource': '<msgsource>\n\t<sec_msg_node>\n\t\t<alnode>\n\t\t\t<fr>1</fr>\n\t\t</alnode>\n\t</sec_msg_node>\n\t<pua>1</pua>\n\t<silence>0</silence>\n\t<membercount>62</membercount>\n\t<signature>N0_V1_FEmATEQM|v1_7CzDKqLE</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'PushContent': '喵小叶 : 我可以去别的群聊天玩，不跟你们玩了哼', 'NewMsgId': 7227019802148067322, 'MsgSeq': 871387718}
2025-07-21 15:50:13 | INFO | 收到文本消息: 消息ID:673476787 来自:48097389945@chatroom 发送人:wxid_84mmq4cu7ita22 @:[] 内容:我可以去别的群聊天玩，不跟你们玩了哼
2025-07-21 15:50:13 | DEBUG | 处理消息内容: '我可以去别的群聊天玩，不跟你们玩了哼'
2025-07-21 15:50:13 | DEBUG | 消息内容 '我可以去别的群聊天玩，不跟你们玩了哼' 不匹配任何命令，忽略
2025-07-21 15:50:15 | DEBUG | 收到消息: {'MsgId': *********, 'FromUserName': {'string': '48097389945@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 34, 'Content': {'string': 'xiaomaochong:\n<msg><voicemsg endflag="1" cancelflag="0" forwardflag="0" voiceformat="4" voicelength="1000" length="10029" bufid="0" aeskey="766769656374627862646a75706a7865" voiceurl="3052020100044b304902010002049363814102033d14ba02042326949d0204687df13f042437636134366363362d626132632d343662612d613834302d33333030623934666230666202040528000f02010004001dc74187" voicemd5="d88ba5853780212ba2ec5ed49cf18ca5" clientmsgid="41386366366231333863396431366400221550072125d67e8f98ba0106" fromusername="xiaomaochong" /></msg>'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1753084223, 'MsgSource': '<msgsource>\n\t<silence>0</silence>\n\t<membercount>62</membercount>\n\t<signature>N0_V1_TAxSe/1L|v1_8WfdBHyn</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'PushContent': '小爱在群聊中发了一段语音', 'NewMsgId': 5849854243242819397, 'MsgSeq': *********}
2025-07-21 15:50:15 | INFO | 收到语音消息: 消息ID:********* 来自:48097389945@chatroom 发送人:xiaomaochong XML:
<msg><voicemsg endflag="1" cancelflag="0" forwardflag="0" voiceformat="4" voicelength="1000" length="10029" bufid="0" aeskey="766769656374627862646a75706a7865" voiceurl="3052020100044b304902010002049363814102033d14ba02042326949d0204687df13f042437636134366363362d626132632d343662612d613834302d33333030623934666230666202040528000f02010004001dc74187" voicemd5="d88ba5853780212ba2ec5ed49cf18ca5" clientmsgid="41386366366231333863396431366400221550072125d67e8f98ba0106" fromusername="xiaomaochong" /></msg>
2025-07-21 15:50:16 | DEBUG | 收到消息: {'MsgId': *********, 'FromUserName': {'string': '48097389945@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 1, 'Content': {'string': 'wxid_q35rkzgkjvlv12:\n女生经常穿黑色休闲裤并不代表什么特定的意义。服装的颜色和款式选择是个人喜好和风格的体现，每个人都有不同的偏好。\n\n黑色休闲裤是一种非常普遍且百搭的服饰，可以搭配多种上衣，适合不同场合穿着。因此，女生选择穿黑色休闲裤可能只是因为她喜欢这种颜色和款式的裤子，并没有其他特别的含义。\n\n需要注意的是，我们应该避免对别人的穿着进行过度的解读和猜测，尊重每个人的个人选择和隐私。'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1753084225, 'MsgSource': '<msgsource>\n\t<silence>0</silence>\n\t<membercount>62</membercount>\n\t<signature>N0_V1_Rott1oEq|v1_7AP+3AqZ</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'PushContent': '摇月 : 女生经常穿黑色休闲裤并不代表什么特定的意义。服装的颜色和款式...', 'NewMsgId': 5536502958367099932, 'MsgSeq': 871387720}
2025-07-21 15:50:16 | INFO | 收到文本消息: 消息ID:********* 来自:48097389945@chatroom 发送人:wxid_q35rkzgkjvlv12 @:[] 内容:女生经常穿黑色休闲裤并不代表什么特定的意义。服装的颜色和款式选择是个人喜好和风格的体现，每个人都有不同的偏好。

黑色休闲裤是一种非常普遍且百搭的服饰，可以搭配多种上衣，适合不同场合穿着。因此，女生选择穿黑色休闲裤可能只是因为她喜欢这种颜色和款式的裤子，并没有其他特别的含义。

需要注意的是，我们应该避免对别人的穿着进行过度的解读和猜测，尊重每个人的个人选择和隐私。
2025-07-21 15:50:16 | DEBUG | 处理消息内容: '女生经常穿黑色休闲裤并不代表什么特定的意义。服装的颜色和款式选择是个人喜好和风格的体现，每个人都有不同的偏好。

黑色休闲裤是一种非常普遍且百搭的服饰，可以搭配多种上衣，适合不同场合穿着。因此，女生选择穿黑色休闲裤可能只是因为她喜欢这种颜色和款式的裤子，并没有其他特别的含义。

需要注意的是，我们应该避免对别人的穿着进行过度的解读和猜测，尊重每个人的个人选择和隐私。'
2025-07-21 15:50:16 | DEBUG | 消息内容 '女生经常穿黑色休闲裤并不代表什么特定的意义。服装的颜色和款式选择是个人喜好和风格的体现，每个人都有不同的偏好。

黑色休闲裤是一种非常普遍且百搭的服饰，可以搭配多种上衣，适合不同场合穿着。因此，女生选择穿黑色休闲裤可能只是因为她喜欢这种颜色和款式的裤子，并没有其他特别的含义。

需要注意的是，我们应该避免对别人的穿着进行过度的解读和猜测，尊重每个人的个人选择和隐私。' 不匹配任何命令，忽略
2025-07-21 15:50:22 | DEBUG | 收到消息: {'MsgId': 1934002158, 'FromUserName': {'string': '27852221909@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 1, 'Content': {'string': 'wxid_x4s6k999g6qg22:\n有歌就是出不来'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1753084231, 'MsgSource': '<msgsource>\n\t<bizflag>0</bizflag>\n\t<pua>1</pua>\n\t<eggIncluded>1</eggIncluded>\n\t<silence>1</silence>\n\t<membercount>146</membercount>\n\t<signature>N0_V1_NmbYSGGC|v1_8a5glJgB</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'NewMsgId': 8254569680063966234, 'MsgSeq': 871387721}
2025-07-21 15:50:22 | INFO | 收到文本消息: 消息ID:1934002158 来自:27852221909@chatroom 发送人:wxid_x4s6k999g6qg22 @:[] 内容:有歌就是出不来
2025-07-21 15:50:22 | DEBUG | 处理消息内容: '有歌就是出不来'
2025-07-21 15:50:22 | DEBUG | 消息内容 '有歌就是出不来' 不匹配任何命令，忽略
2025-07-21 15:52:03 | DEBUG | 收到消息: {'MsgId': 1108099699, 'FromUserName': {'string': '48097389945@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 1, 'Content': {'string': 'wxid_84mmq4cu7ita22:\n尊重每个人的个人选择和隐私。'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1753084332, 'MsgSource': '<msgsource>\n\t<alnode>\n\t\t<cf>2</cf>\n\t</alnode>\n\t<pua>1</pua>\n\t<silence>0</silence>\n\t<membercount>62</membercount>\n\t<signature>N0_V1_FhsnJuyM|v1_8p+z4YVg</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'PushContent': '喵小叶 : 尊重每个人的个人选择和隐私。', 'NewMsgId': 4044864997852436887, 'MsgSeq': 871387722}
2025-07-21 15:52:03 | INFO | 收到文本消息: 消息ID:1108099699 来自:48097389945@chatroom 发送人:wxid_84mmq4cu7ita22 @:[] 内容:尊重每个人的个人选择和隐私。
2025-07-21 15:52:03 | DEBUG | 处理消息内容: '尊重每个人的个人选择和隐私。'
2025-07-21 15:52:03 | DEBUG | 消息内容 '尊重每个人的个人选择和隐私。' 不匹配任何命令，忽略
2025-07-21 15:52:09 | DEBUG | 收到消息: {'MsgId': 1472576493, 'FromUserName': {'string': '27852221909@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 1, 'Content': {'string': 'wxid_5kipwrzramxr22:\n@小5\u2005你偷懒'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1753084338, 'MsgSource': '<msgsource>\n\t<atuserlist>wxid_bmzp9achod6922</atuserlist>\n\t<bizflag>0</bizflag>\n\t<pua>1</pua>\n\t<eggIncluded>1</eggIncluded>\n\t<silence>1</silence>\n\t<membercount>146</membercount>\n\t<signature>N0_V1_wgwGh2YR|v1_/qV60Lik</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'NewMsgId': 8962614681046335490, 'MsgSeq': 871387723}
2025-07-21 15:52:09 | INFO | 收到文本消息: 消息ID:1472576493 来自:27852221909@chatroom 发送人:wxid_5kipwrzramxr22 @:['wxid_bmzp9achod6922'] 内容:@小5 你偷懒
2025-07-21 15:52:09 | DEBUG | 处理消息内容: '@小5 你偷懒'
2025-07-21 15:52:09 | DEBUG | 消息内容 '@小5 你偷懒' 不匹配任何命令，忽略
2025-07-21 15:52:51 | DEBUG | 收到消息: {'MsgId': 423414535, 'FromUserName': {'string': '48097389945@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 49, 'Content': {'string': 'wxid_84mmq4cu7ita22:\n<?xml version="1.0"?>\n<msg>\n\t<appmsg appid="" sdkver="0">\n\t\t<title>你要怎么欺负我</title>\n\t\t<des />\n\t\t<action>view</action>\n\t\t<type>57</type>\n\t\t<showtype>0</showtype>\n\t\t<content />\n\t\t<url />\n\t\t<dataurl />\n\t\t<lowurl />\n\t\t<lowdataurl />\n\t\t<recorditem />\n\t\t<thumburl />\n\t\t<messageaction />\n\t\t<laninfo />\n\t\t<refermsg>\n\t\t\t<type>1</type>\n\t\t\t<svrid>3045073107825808084</svrid>\n\t\t\t<fromusr>48097389945@chatroom</fromusr>\n\t\t\t<chatusr>zll953369865</chatusr>\n\t\t\t<createtime>1753084189</createtime>\n\t\t\t<msgsource>&lt;msgsource&gt;\n    &lt;sec_msg_node&gt;\n        &lt;alnode&gt;\n            &lt;fr&gt;1&lt;/fr&gt;\n        &lt;/alnode&gt;\n    &lt;/sec_msg_node&gt;\n    &lt;pua&gt;1&lt;/pua&gt;\n    &lt;silence&gt;1&lt;/silence&gt;\n    &lt;membercount&gt;62&lt;/membercount&gt;\n    &lt;signature&gt;N0_V1_g9okzfc6|v1_cbMuW3rA&lt;/signature&gt;\n    &lt;tmp_node&gt;\n        &lt;publisher-id /&gt;\n    &lt;/tmp_node&gt;\n&lt;/msgsource&gt;\n</msgsource>\n\t\t\t<content>不能欺负你？</content>\n\t\t\t<displayname>【微信红包】收到一个微信红包</displayname>\n\t\t</refermsg>\n\t\t<extinfo />\n\t\t<sourceusername />\n\t\t<sourcedisplayname />\n\t\t<commenturl />\n\t\t<appattach>\n\t\t\t<totallen>0</totallen>\n\t\t\t<attachid />\n\t\t\t<emoticonmd5 />\n\t\t\t<fileext />\n\t\t\t<aeskey />\n\t\t</appattach>\n\t\t<webviewshared>\n\t\t\t<publisherId />\n\t\t\t<publisherReqId>0</publisherReqId>\n\t\t</webviewshared>\n\t\t<weappinfo>\n\t\t\t<pagepath />\n\t\t\t<username />\n\t\t\t<appid />\n\t\t\t<appservicetype>0</appservicetype>\n\t\t</weappinfo>\n\t\t<websearch />\n\t</appmsg>\n\t<fromusername>wxid_84mmq4cu7ita22</fromusername>\n\t<scene>0</scene>\n\t<appinfo>\n\t\t<version>1</version>\n\t\t<appname></appname>\n\t</appinfo>\n\t<commenturl></commenturl>\n</msg>\n'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1753084380, 'MsgSource': '<msgsource>\n\t<pua>1</pua>\n\t<sec_msg_node>\n\t\t<uuid>178838eea9316086744b662ab08d8779_</uuid>\n\t\t<risk-file-flag />\n\t\t<risk-file-md5-list />\n\t</sec_msg_node>\n\t<silence>0</silence>\n\t<membercount>62</membercount>\n\t<signature>N0_V1_79XAPRst|v1_hBXHIcU4</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'PushContent': '喵小叶 : 你要怎么欺负我', 'NewMsgId': 8487121184500547717, 'MsgSeq': 871387724}
2025-07-21 15:52:51 | DEBUG | 从群聊消息中提取发送者: wxid_84mmq4cu7ita22
2025-07-21 15:52:51 | DEBUG | 使用已解析的XML处理引用消息
2025-07-21 15:52:51 | INFO | 收到引用消息: 消息ID:423414535 来自:48097389945@chatroom 发送人:wxid_84mmq4cu7ita22 内容:你要怎么欺负我 引用类型:1
2025-07-21 15:52:51 | INFO | [DouBaoImageToImage] 收到引用消息: 你要怎么欺负我
2025-07-21 15:52:51 | INFO | [TimerTask] 收到引用消息调试信息:
2025-07-21 15:52:51 | INFO |   - 消息内容: 你要怎么欺负我
2025-07-21 15:52:51 | INFO |   - 群组ID: 48097389945@chatroom
2025-07-21 15:52:51 | INFO |   - 发送人: wxid_84mmq4cu7ita22
2025-07-21 15:52:51 | INFO |   - 引用信息: {'MsgType': 1, 'Content': '不能欺负你？', 'Msgid': '3045073107825808084', 'NewMsgId': '3045073107825808084', 'ToWxid': 'wxid_4usgcju5ey9q29', 'FromWxid': '48097389945@chatroom', 'Nickname': '【微信红包】收到一个微信红包', 'MsgSource': '<msgsource>\n    <sec_msg_node>\n        <alnode>\n            <fr>1</fr>\n        </alnode>\n    </sec_msg_node>\n    <pua>1</pua>\n    <silence>1</silence>\n    <membercount>62</membercount>\n    <signature>N0_V1_g9okzfc6|v1_cbMuW3rA</signature>\n    <tmp_node>\n        <publisher-id />\n    </tmp_node>\n</msgsource>\n', 'Createtime': '1753084189', 'SenderWxid': 'wxid_84mmq4cu7ita22'}
2025-07-21 15:52:51 | INFO |   - 引用消息ID: 
2025-07-21 15:52:51 | INFO |   - 引用消息类型: 
2025-07-21 15:52:51 | INFO |   - 引用消息内容: 不能欺负你？
2025-07-21 15:52:51 | INFO |   - 引用消息发送人: wxid_84mmq4cu7ita22
2025-07-21 15:53:04 | DEBUG | 收到消息: {'MsgId': 782964142, 'FromUserName': {'string': '48097389945@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 1, 'Content': {'string': 'wxid_n5c0aekjceu621:\n我下班了喵喵'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1753084393, 'MsgSource': '<msgsource>\n\t<pua>1</pua>\n\t<silence>0</silence>\n\t<membercount>62</membercount>\n\t<signature>N0_V1_ASCzNlKb|v1_5SvrvYN3</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'PushContent': 'ㅤㅤㅤㅤㅤㅤㅤㅤㅤㅤㅤㅤㅤㅤㅤㅤ : 我下班了喵喵', 'NewMsgId': 1003555997173918788, 'MsgSeq': 871387725}
2025-07-21 15:53:04 | INFO | 收到文本消息: 消息ID:782964142 来自:48097389945@chatroom 发送人:wxid_n5c0aekjceu621 @:[] 内容:我下班了喵喵
2025-07-21 15:53:04 | DEBUG | 处理消息内容: '我下班了喵喵'
2025-07-21 15:53:04 | DEBUG | 消息内容 '我下班了喵喵' 不匹配任何命令，忽略
2025-07-21 15:53:09 | DEBUG | 收到消息: {'MsgId': 1001061226, 'FromUserName': {'string': '48097389945@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 1, 'Content': {'string': 'wxid_84mmq4cu7ita22:\n咋的去大蜀山打一架咩'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1753084398, 'MsgSource': '<msgsource>\n\t<sec_msg_node>\n\t\t<alnode>\n\t\t\t<fr>1</fr>\n\t\t</alnode>\n\t</sec_msg_node>\n\t<pua>1</pua>\n\t<silence>0</silence>\n\t<membercount>62</membercount>\n\t<signature>N0_V1_AP/srv/T|v1_U5C+rXop</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'PushContent': '喵小叶 : 咋的去大蜀山打一架咩', 'NewMsgId': 5768304518271977349, 'MsgSeq': 871387726}
2025-07-21 15:53:09 | INFO | 收到文本消息: 消息ID:1001061226 来自:48097389945@chatroom 发送人:wxid_84mmq4cu7ita22 @:[] 内容:咋的去大蜀山打一架咩
2025-07-21 15:53:09 | DEBUG | 处理消息内容: '咋的去大蜀山打一架咩'
2025-07-21 15:53:09 | DEBUG | 消息内容 '咋的去大蜀山打一架咩' 不匹配任何命令，忽略
2025-07-21 15:53:30 | DEBUG | 收到消息: {'MsgId': 1424559942, 'FromUserName': {'string': '48097389945@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 49, 'Content': {'string': 'wxid_84mmq4cu7ita22:\n<?xml version="1.0"?>\n<msg>\n\t<appmsg appid="" sdkver="0">\n\t\t<title>羡慕</title>\n\t\t<des />\n\t\t<action>view</action>\n\t\t<type>57</type>\n\t\t<showtype>0</showtype>\n\t\t<content />\n\t\t<url />\n\t\t<dataurl />\n\t\t<lowurl />\n\t\t<lowdataurl />\n\t\t<recorditem />\n\t\t<thumburl />\n\t\t<messageaction />\n\t\t<laninfo />\n\t\t<refermsg>\n\t\t\t<type>1</type>\n\t\t\t<svrid>1003555997173918788</svrid>\n\t\t\t<fromusr>48097389945@chatroom</fromusr>\n\t\t\t<chatusr>wxid_n5c0aekjceu621</chatusr>\n\t\t\t<createtime>1753084393</createtime>\n\t\t\t<msgsource>&lt;msgsource&gt;\n    &lt;pua&gt;1&lt;/pua&gt;\n    &lt;silence&gt;1&lt;/silence&gt;\n    &lt;membercount&gt;62&lt;/membercount&gt;\n    &lt;signature&gt;N0_V1_ke71rBS5|v1_LJ9ZBypK&lt;/signature&gt;\n    &lt;tmp_node&gt;\n        &lt;publisher-id /&gt;\n    &lt;/tmp_node&gt;\n    &lt;sec_msg_node&gt;\n        &lt;alnode&gt;\n            &lt;fr&gt;1&lt;/fr&gt;\n        &lt;/alnode&gt;\n    &lt;/sec_msg_node&gt;\n&lt;/msgsource&gt;\n</msgsource>\n\t\t\t<content>我下班了喵喵</content>\n\t\t\t<displayname>ㅤㅤㅤㅤㅤㅤㅤㅤㅤㅤㅤㅤㅤㅤㅤㅤ</displayname>\n\t\t</refermsg>\n\t\t<extinfo />\n\t\t<sourceusername />\n\t\t<sourcedisplayname />\n\t\t<commenturl />\n\t\t<appattach>\n\t\t\t<totallen>0</totallen>\n\t\t\t<attachid />\n\t\t\t<emoticonmd5 />\n\t\t\t<fileext />\n\t\t\t<aeskey />\n\t\t</appattach>\n\t\t<webviewshared>\n\t\t\t<publisherId />\n\t\t\t<publisherReqId>0</publisherReqId>\n\t\t</webviewshared>\n\t\t<weappinfo>\n\t\t\t<pagepath />\n\t\t\t<username />\n\t\t\t<appid />\n\t\t\t<appservicetype>0</appservicetype>\n\t\t</weappinfo>\n\t\t<websearch />\n\t</appmsg>\n\t<fromusername>wxid_84mmq4cu7ita22</fromusername>\n\t<scene>0</scene>\n\t<appinfo>\n\t\t<version>1</version>\n\t\t<appname></appname>\n\t</appinfo>\n\t<commenturl></commenturl>\n</msg>\n'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1753084418, 'MsgSource': '<msgsource>\n\t<pua>1</pua>\n\t<sec_msg_node>\n\t\t<uuid>44d6f9ceb316487e91278171ed6df669_</uuid>\n\t\t<risk-file-flag />\n\t\t<risk-file-md5-list />\n\t</sec_msg_node>\n\t<silence>0</silence>\n\t<membercount>62</membercount>\n\t<signature>N0_V1_X6JKbZPm|v1_Y9UUNWOa</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'PushContent': '喵小叶 : 羡慕', 'NewMsgId': 8693150869289173095, 'MsgSeq': 871387727}
2025-07-21 15:53:30 | DEBUG | 从群聊消息中提取发送者: wxid_84mmq4cu7ita22
2025-07-21 15:53:30 | DEBUG | 使用已解析的XML处理引用消息
2025-07-21 15:53:30 | INFO | 收到引用消息: 消息ID:1424559942 来自:48097389945@chatroom 发送人:wxid_84mmq4cu7ita22 内容:羡慕 引用类型:1
2025-07-21 15:53:30 | INFO | [DouBaoImageToImage] 收到引用消息: 羡慕
2025-07-21 15:53:30 | INFO | [TimerTask] 收到引用消息调试信息:
2025-07-21 15:53:30 | INFO |   - 消息内容: 羡慕
2025-07-21 15:53:30 | INFO |   - 群组ID: 48097389945@chatroom
2025-07-21 15:53:30 | INFO |   - 发送人: wxid_84mmq4cu7ita22
2025-07-21 15:53:30 | INFO |   - 引用信息: {'MsgType': 1, 'Content': '我下班了喵喵', 'Msgid': '1003555997173918788', 'NewMsgId': '1003555997173918788', 'ToWxid': 'wxid_4usgcju5ey9q29', 'FromWxid': '48097389945@chatroom', 'Nickname': 'ㅤㅤㅤㅤㅤㅤㅤㅤㅤㅤㅤㅤㅤㅤㅤㅤ', 'MsgSource': '<msgsource>\n    <pua>1</pua>\n    <silence>1</silence>\n    <membercount>62</membercount>\n    <signature>N0_V1_ke71rBS5|v1_LJ9ZBypK</signature>\n    <tmp_node>\n        <publisher-id />\n    </tmp_node>\n    <sec_msg_node>\n        <alnode>\n            <fr>1</fr>\n        </alnode>\n    </sec_msg_node>\n</msgsource>\n', 'Createtime': '1753084393', 'SenderWxid': 'wxid_84mmq4cu7ita22'}
2025-07-21 15:53:30 | INFO |   - 引用消息ID: 
2025-07-21 15:53:30 | INFO |   - 引用消息类型: 
2025-07-21 15:53:30 | INFO |   - 引用消息内容: 我下班了喵喵
2025-07-21 15:53:30 | INFO |   - 引用消息发送人: wxid_84mmq4cu7ita22
2025-07-21 15:53:31 | DEBUG | 收到消息: {'MsgId': 370300442, 'FromUserName': {'string': '48097389945@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 1, 'Content': {'string': 'wxid_84mmq4cu7ita22:\n我还早'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1753084421, 'MsgSource': '<msgsource>\n\t<sec_msg_node>\n\t\t<alnode>\n\t\t\t<fr>1</fr>\n\t\t</alnode>\n\t</sec_msg_node>\n\t<pua>1</pua>\n\t<silence>0</silence>\n\t<membercount>62</membercount>\n\t<signature>N0_V1_Kd7aBxh/|v1_Ilo4fYGq</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'PushContent': '喵小叶 : 我还早', 'NewMsgId': 802231718037307297, 'MsgSeq': 871387728}
2025-07-21 15:53:31 | INFO | 收到文本消息: 消息ID:370300442 来自:48097389945@chatroom 发送人:wxid_84mmq4cu7ita22 @:[] 内容:我还早
2025-07-21 15:53:32 | DEBUG | 处理消息内容: '我还早'
2025-07-21 15:53:32 | DEBUG | 消息内容 '我还早' 不匹配任何命令，忽略
2025-07-21 15:53:34 | DEBUG | 收到消息: {'MsgId': 2017730820, 'FromUserName': {'string': '48097389945@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 1, 'Content': {'string': 'wxid_84mmq4cu7ita22:\n等我下班'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1753084423, 'MsgSource': '<msgsource>\n\t<sec_msg_node>\n\t\t<alnode>\n\t\t\t<fr>1</fr>\n\t\t</alnode>\n\t</sec_msg_node>\n\t<pua>1</pua>\n\t<silence>0</silence>\n\t<membercount>62</membercount>\n\t<signature>N0_V1_BdQdexBW|v1_h+KZtbYd</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'PushContent': '喵小叶 : 等我下班', 'NewMsgId': 5834966384353464418, 'MsgSeq': 871387729}
2025-07-21 15:53:34 | INFO | 收到文本消息: 消息ID:2017730820 来自:48097389945@chatroom 发送人:wxid_84mmq4cu7ita22 @:[] 内容:等我下班
2025-07-21 15:53:34 | DEBUG | 处理消息内容: '等我下班'
2025-07-21 15:53:34 | DEBUG | 消息内容 '等我下班' 不匹配任何命令，忽略
2025-07-21 15:53:36 | DEBUG | 收到消息: {'MsgId': 462326746, 'FromUserName': {'string': '48097389945@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 1, 'Content': {'string': 'wxid_84mmq4cu7ita22:\n上线'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1753084425, 'MsgSource': '<msgsource>\n\t<sec_msg_node>\n\t\t<alnode>\n\t\t\t<fr>1</fr>\n\t\t</alnode>\n\t</sec_msg_node>\n\t<pua>1</pua>\n\t<silence>0</silence>\n\t<membercount>62</membercount>\n\t<signature>N0_V1_nSRmwINO|v1_Qrb72XxA</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'PushContent': '喵小叶 : 上线', 'NewMsgId': 3084089924305992106, 'MsgSeq': 871387730}
2025-07-21 15:53:36 | INFO | 收到文本消息: 消息ID:462326746 来自:48097389945@chatroom 发送人:wxid_84mmq4cu7ita22 @:[] 内容:上线
2025-07-21 15:53:37 | DEBUG | 处理消息内容: '上线'
2025-07-21 15:53:37 | DEBUG | 消息内容 '上线' 不匹配任何命令，忽略
2025-07-21 15:53:57 | DEBUG | 收到消息: {'MsgId': 700383867, 'FromUserName': {'string': '48097389945@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 1, 'Content': {'string': 'wxid_84mmq4cu7ita22:\n我下班直接打车回去躺着'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1753084446, 'MsgSource': '<msgsource>\n\t<sec_msg_node>\n\t\t<alnode>\n\t\t\t<fr>1</fr>\n\t\t</alnode>\n\t</sec_msg_node>\n\t<pua>1</pua>\n\t<silence>0</silence>\n\t<membercount>62</membercount>\n\t<signature>N0_V1_RYj6TzPZ|v1_2jTF0tWy</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'PushContent': '喵小叶 : 我下班直接打车回去躺着', 'NewMsgId': 4113756129456325193, 'MsgSeq': 871387731}
2025-07-21 15:53:57 | INFO | 收到文本消息: 消息ID:700383867 来自:48097389945@chatroom 发送人:wxid_84mmq4cu7ita22 @:[] 内容:我下班直接打车回去躺着
2025-07-21 15:53:57 | DEBUG | 处理消息内容: '我下班直接打车回去躺着'
2025-07-21 15:53:57 | DEBUG | 消息内容 '我下班直接打车回去躺着' 不匹配任何命令，忽略
2025-07-21 15:54:03 | DEBUG | 收到消息: {'MsgId': 885652484, 'FromUserName': {'string': '48097389945@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 47, 'Content': {'string': 'wxid_84mmq4cu7ita22:\n<msg><emoji fromusername = "wxid_84mmq4cu7ita22" tousername = "48097389945@chatroom" type="1" idbuffer="media:0_0" md5="5f2eefb2e365affda1cf919f4a184aea" len = "69236" productid="" androidmd5="5f2eefb2e365affda1cf919f4a184aea" androidlen="69236" s60v3md5 = "5f2eefb2e365affda1cf919f4a184aea" s60v3len="69236" s60v5md5 = "5f2eefb2e365affda1cf919f4a184aea" s60v5len="69236" cdnurl = "http://vweixinf.tc.qq.com/110/20401/stodownload?m=5f2eefb2e365affda1cf919f4a184aea&amp;filekey=30440201010430302e02016e04025348042035663265656662326533363561666664613163663931396634613138346165610203010e74040d00000004627466730000000132&amp;hy=SH&amp;storeid=26690065b000d385f0537eb9d0000006e01004fb1534824b3f0315699dcfff&amp;ef=1&amp;bizid=1022" designerid = "" thumburl = "" encrypturl = "http://vweixinf.tc.qq.com/110/20402/stodownload?m=ab1daa1e1babdc7e6e280f2cd4f3757a&amp;filekey=30440201010430302e02016e04025348042061623164616131653162616264633765366532383066326364346633373537610203010e80040d00000004627466730000000132&amp;hy=SH&amp;storeid=26690065b000deff30537eb9d0000006e02004fb2534824b3f0315699dd00a&amp;ef=2&amp;bizid=1022" aeskey= "a683ffd2b0a54839b4e22e38faffd53b" externurl = "" externmd5 = "" width= "540" height= "133" tpurl= "" tpauthkey= "" attachedtext= "" attachedtextcolor= "" lensid= "" emojiattr= "" linkid= "" desc= "" ></emoji>  </msg>'}, 'Status': 3, 'ImgStatus': 2, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1753084452, 'MsgSource': '<sec_msg_node>\n\t<alnode>\n\t\t<fr>2</fr>\n\t</alnode>\n</sec_msg_node>\n<msgsource>\n\t<silence>0</silence>\n\t<membercount>62</membercount>\n\t<signature>N0_V1_aPdGsLC8|v1_2vHG4DVH</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'PushContent': '喵小叶在群聊中发了一个表情', 'NewMsgId': 1026299721172141592, 'MsgSeq': 871387732}
2025-07-21 15:54:03 | INFO | 收到表情消息: 消息ID:885652484 来自:48097389945@chatroom 发送人:wxid_84mmq4cu7ita22 MD5:5f2eefb2e365affda1cf919f4a184aea 大小:69236
2025-07-21 15:54:03 | DEBUG | [DoubaoImageRecognition] 缓存表情消息: 1026299721172141592
2025-07-21 15:54:11 | DEBUG | 收到消息: {'MsgId': 321927161, 'FromUserName': {'string': '48097389945@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 1, 'Content': {'string': 'wxid_n5c0aekjceu621:\n不用这么着急的乖'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1753084460, 'MsgSource': '<msgsource>\n\t<pua>1</pua>\n\t<silence>0</silence>\n\t<membercount>62</membercount>\n\t<signature>N0_V1_hufahSn+|v1_TcuY3hAo</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'PushContent': 'ㅤㅤㅤㅤㅤㅤㅤㅤㅤㅤㅤㅤㅤㅤㅤㅤ : 不用这么着急的乖', 'NewMsgId': 9331984106847695, 'MsgSeq': 871387733}
2025-07-21 15:54:11 | INFO | 收到文本消息: 消息ID:321927161 来自:48097389945@chatroom 发送人:wxid_n5c0aekjceu621 @:[] 内容:不用这么着急的乖
2025-07-21 15:54:12 | DEBUG | 处理消息内容: '不用这么着急的乖'
2025-07-21 15:54:12 | DEBUG | 消息内容 '不用这么着急的乖' 不匹配任何命令，忽略
2025-07-21 15:54:14 | DEBUG | 收到消息: {'MsgId': 126058738, 'FromUserName': {'string': '48097389945@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 1, 'Content': {'string': 'wxid_84mmq4cu7ita22:\n还有一个小时这样'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1753084463, 'MsgSource': '<msgsource>\n\t<sec_msg_node>\n\t\t<alnode>\n\t\t\t<fr>1</fr>\n\t\t</alnode>\n\t</sec_msg_node>\n\t<pua>1</pua>\n\t<silence>0</silence>\n\t<membercount>62</membercount>\n\t<signature>N0_V1_GaA81uqx|v1_/xsjSSCu</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'PushContent': '喵小叶 : 还有一个小时这样', 'NewMsgId': 1070104949975625338, 'MsgSeq': 871387734}
2025-07-21 15:54:14 | INFO | 收到文本消息: 消息ID:126058738 来自:48097389945@chatroom 发送人:wxid_84mmq4cu7ita22 @:[] 内容:还有一个小时这样
2025-07-21 15:54:14 | DEBUG | 处理消息内容: '还有一个小时这样'
2025-07-21 15:54:14 | DEBUG | 消息内容 '还有一个小时这样' 不匹配任何命令，忽略
2025-07-21 15:54:19 | DEBUG | 收到消息: {'MsgId': 1130511582, 'FromUserName': {'string': '48097389945@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 1, 'Content': {'string': 'wxid_84mmq4cu7ita22:\n4点了'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1753084468, 'MsgSource': '<msgsource>\n\t<sec_msg_node>\n\t\t<alnode>\n\t\t\t<fr>1</fr>\n\t\t</alnode>\n\t</sec_msg_node>\n\t<pua>1</pua>\n\t<silence>0</silence>\n\t<membercount>62</membercount>\n\t<signature>N0_V1_RFvgPmub|v1_uMmstYuh</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'PushContent': '喵小叶 : 4点了', 'NewMsgId': 2070104440184934898, 'MsgSeq': 871387735}
2025-07-21 15:54:19 | INFO | 收到文本消息: 消息ID:1130511582 来自:48097389945@chatroom 发送人:wxid_84mmq4cu7ita22 @:[] 内容:4点了
2025-07-21 15:54:19 | DEBUG | 处理消息内容: '4点了'
2025-07-21 15:54:19 | DEBUG | 消息内容 '4点了' 不匹配任何命令，忽略
2025-07-21 15:54:21 | DEBUG | 收到消息: {'MsgId': 1640807296, 'FromUserName': {'string': '48097389945@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 47, 'Content': {'string': 'wxid_n5c0aekjceu621:\n<msg><emoji fromusername = "wxid_n5c0aekjceu621" tousername = "48097389945@chatroom" type="3" idbuffer="media:0_0" md5="5b494adbad020accc927324b8e562998" len = "100285" productid="com.tencent.xin.emoticon.person.stiker_1548236367a4a1bf34c73e2000" androidmd5="5b494adbad020accc927324b8e562998" androidlen="100285" s60v3md5 = "5b494adbad020accc927324b8e562998" s60v3len="100285" s60v5md5 = "5b494adbad020accc927324b8e562998" s60v5len="100285" cdnurl = "http://wxapp.tc.qq.com/275/20304/stodownload?m=5b494adbad020accc927324b8e562998&amp;filekey=30350201010421301f020201130402534804105b494adbad020accc927324b8e56299802030187bd040d00000004627466730000000132&amp;hy=SH&amp;storeid=264795ae900040b20000000000000011300004f5053481446eb40b6fb64dcb&amp;bizid=1023" designerid = "" thumburl = "http://wxapp.tc.qq.com/275/20304/stodownload?m=1145273c4930d3b0c793809fbc58d9f8&amp;filekey=30340201010420301e020201130402534804101145273c4930d3b0c793809fbc58d9f802024d4e040d00000004627466730000000132&amp;hy=SH&amp;storeid=264795ae90006193d000000000000011300004f50534804469b40b719667de&amp;bizid=1023" encrypturl = "http://wxapp.tc.qq.com/262/20304/stodownload?m=62a007c0ae8bbf90d620605bb671baba&amp;filekey=30350201010421301f0202010604025348041062a007c0ae8bbf90d620605bb671baba02030187c0040d00000004627466730000000132&amp;hy=SH&amp;storeid=2630f16fa000332f2000000000000010600004f5053482056fb40b63bf01ed&amp;bizid=1023" aeskey= "42de0e30c4660e990ced60af36584aac" externurl = "http://wxapp.tc.qq.com/262/20304/stodownload?m=dc0571c8deae94e2979be78017497d28&amp;filekey=30340201010420301e02020106040253480410dc0571c8deae94e2979be78017497d2802025d60040d00000004627466730000000132&amp;hy=SH&amp;storeid=2630f16fa0005ffb8000000000000010600004f50534800d67b40b63c0d274&amp;bizid=1023" externmd5 = "b087c9c9c2ed3def9760c766d1211031" width= "240" height= "240" tpurl= "" tpauthkey= "" attachedtext= "" attachedtextcolor= "" lensid= "" emojiattr= "" linkid= "" desc= "ChIKBXpoX2NuEgnmkbjmkbjlpLQKCQoFemhfdHcSAAoLCgdkZWZhdWx0EgA=" ></emoji>  </msg>'}, 'Status': 3, 'ImgStatus': 2, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1753084468, 'MsgSource': '<msgsource>\n\t<silence>0</silence>\n\t<membercount>62</membercount>\n\t<signature>N0_V1_LfeRDn02|v1_0yL+0E7F</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'PushContent': 'ㅤㅤㅤㅤㅤㅤㅤㅤㅤㅤㅤㅤㅤㅤㅤㅤ在群聊中发了一个表情', 'NewMsgId': 7584433851863217919, 'MsgSeq': 871387736}
2025-07-21 15:54:21 | INFO | 收到表情消息: 消息ID:1640807296 来自:48097389945@chatroom 发送人:wxid_n5c0aekjceu621 MD5:5b494adbad020accc927324b8e562998 大小:100285
2025-07-21 15:54:21 | DEBUG | [DoubaoImageRecognition] 缓存表情消息: 7584433851863217919
2025-07-21 15:54:22 | DEBUG | 收到消息: {'MsgId': 794704644, 'FromUserName': {'string': '48097389945@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 1, 'Content': {'string': 'wxid_84mmq4cu7ita22:\n5点就不远了'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1753084472, 'MsgSource': '<msgsource>\n\t<sec_msg_node>\n\t\t<alnode>\n\t\t\t<fr>1</fr>\n\t\t</alnode>\n\t</sec_msg_node>\n\t<pua>1</pua>\n\t<silence>0</silence>\n\t<membercount>62</membercount>\n\t<signature>N0_V1_eq7XTEn7|v1_cwMfc0JL</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'PushContent': '喵小叶 : 5点就不远了', 'NewMsgId': 3028056225782047960, 'MsgSeq': 871387737}
2025-07-21 15:54:22 | INFO | 收到文本消息: 消息ID:794704644 来自:48097389945@chatroom 发送人:wxid_84mmq4cu7ita22 @:[] 内容:5点就不远了
2025-07-21 15:54:23 | DEBUG | 处理消息内容: '5点就不远了'
2025-07-21 15:54:23 | DEBUG | 消息内容 '5点就不远了' 不匹配任何命令，忽略
2025-07-21 15:54:27 | DEBUG | 收到消息: {'MsgId': 182726718, 'FromUserName': {'string': '48097389945@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 1, 'Content': {'string': 'wxid_84mmq4cu7ita22:\n还有个快递要拿'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1753084476, 'MsgSource': '<msgsource>\n\t<sec_msg_node>\n\t\t<alnode>\n\t\t\t<fr>1</fr>\n\t\t</alnode>\n\t</sec_msg_node>\n\t<pua>1</pua>\n\t<silence>0</silence>\n\t<membercount>62</membercount>\n\t<signature>N0_V1_rXTkuQnh|v1_9vpX0L9Q</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'PushContent': '喵小叶 : 还有个快递要拿', 'NewMsgId': 5693406403153385897, 'MsgSeq': 871387738}
2025-07-21 15:54:27 | INFO | 收到文本消息: 消息ID:182726718 来自:48097389945@chatroom 发送人:wxid_84mmq4cu7ita22 @:[] 内容:还有个快递要拿
2025-07-21 15:54:27 | DEBUG | 处理消息内容: '还有个快递要拿'
2025-07-21 15:54:27 | DEBUG | 消息内容 '还有个快递要拿' 不匹配任何命令，忽略
2025-07-21 15:54:44 | DEBUG | 收到消息: {'MsgId': 1417987309, 'FromUserName': {'string': '48097389945@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 1, 'Content': {'string': 'wxid_n5c0aekjceu621:\n你5点下班嘛'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1753084493, 'MsgSource': '<msgsource>\n\t<pua>1</pua>\n\t<silence>0</silence>\n\t<membercount>62</membercount>\n\t<signature>N0_V1_SAhEgW1x|v1_wZCtFREH</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'PushContent': 'ㅤㅤㅤㅤㅤㅤㅤㅤㅤㅤㅤㅤㅤㅤㅤㅤ : 你5点下班嘛', 'NewMsgId': 8345600183408639625, 'MsgSeq': 871387739}
2025-07-21 15:54:44 | INFO | 收到文本消息: 消息ID:1417987309 来自:48097389945@chatroom 发送人:wxid_n5c0aekjceu621 @:[] 内容:你5点下班嘛
2025-07-21 15:54:45 | DEBUG | 处理消息内容: '你5点下班嘛'
2025-07-21 15:54:45 | DEBUG | 消息内容 '你5点下班嘛' 不匹配任何命令，忽略
2025-07-21 15:54:56 | DEBUG | 收到消息: {'MsgId': 581064593, 'FromUserName': {'string': '48097389945@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 49, 'Content': {'string': 'zll953369865:\n<?xml version="1.0"?>\n<msg>\n\t<appmsg appid="" sdkver="0">\n\t\t<title>可以啊</title>\n\t\t<des />\n\t\t<action>view</action>\n\t\t<type>57</type>\n\t\t<showtype>0</showtype>\n\t\t<content />\n\t\t<url />\n\t\t<dataurl />\n\t\t<lowurl />\n\t\t<lowdataurl />\n\t\t<recorditem />\n\t\t<thumburl />\n\t\t<messageaction />\n\t\t<laninfo />\n\t\t<refermsg>\n\t\t\t<type>1</type>\n\t\t\t<svrid>5768304518271977349</svrid>\n\t\t\t<fromusr>48097389945@chatroom</fromusr>\n\t\t\t<chatusr>wxid_84mmq4cu7ita22</chatusr>\n\t\t\t<createtime>1753084398</createtime>\n\t\t\t<msgsource>&lt;msgsource&gt;\n    &lt;sec_msg_node&gt;\n        &lt;alnode&gt;\n            &lt;fr&gt;1&lt;/fr&gt;\n        &lt;/alnode&gt;\n    &lt;/sec_msg_node&gt;\n    &lt;pua&gt;1&lt;/pua&gt;\n    &lt;silence&gt;1&lt;/silence&gt;\n    &lt;membercount&gt;62&lt;/membercount&gt;\n    &lt;signature&gt;N0_V1_8dw6gOp3|v1_pmtAI98A&lt;/signature&gt;\n    &lt;tmp_node&gt;\n        &lt;publisher-id /&gt;\n    &lt;/tmp_node&gt;\n&lt;/msgsource&gt;\n</msgsource>\n\t\t\t<content>咋的去大蜀山打一架咩</content>\n\t\t\t<displayname>喵小叶</displayname>\n\t\t</refermsg>\n\t\t<extinfo />\n\t\t<sourceusername />\n\t\t<sourcedisplayname />\n\t\t<commenturl />\n\t\t<appattach>\n\t\t\t<totallen>0</totallen>\n\t\t\t<attachid />\n\t\t\t<emoticonmd5 />\n\t\t\t<fileext />\n\t\t\t<aeskey />\n\t\t</appattach>\n\t\t<webviewshared>\n\t\t\t<publisherId />\n\t\t\t<publisherReqId>0</publisherReqId>\n\t\t</webviewshared>\n\t\t<weappinfo>\n\t\t\t<pagepath />\n\t\t\t<username />\n\t\t\t<appid />\n\t\t\t<appservicetype>0</appservicetype>\n\t\t</weappinfo>\n\t\t<websearch />\n\t</appmsg>\n\t<fromusername>zll953369865</fromusername>\n\t<scene>0</scene>\n\t<appinfo>\n\t\t<version>1</version>\n\t\t<appname></appname>\n\t</appinfo>\n\t<commenturl></commenturl>\n</msg>\n'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1753084505, 'MsgSource': '<msgsource>\n\t<pua>1</pua>\n\t<sec_msg_node>\n\t\t<uuid>492ad4d9296d76b6f41337b65f22f887_</uuid>\n\t\t<risk-file-flag />\n\t\t<risk-file-md5-list />\n\t</sec_msg_node>\n\t<silence>0</silence>\n\t<membercount>62</membercount>\n\t<signature>N0_V1_vJjY9fqz|v1_bF/2WRbO</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'PushContent': '【微信红包】收到一个微信红包 : 可以啊', 'NewMsgId': 1342229658284501224, 'MsgSeq': 871387740}
2025-07-21 15:54:56 | DEBUG | 从群聊消息中提取发送者: zll953369865
2025-07-21 15:54:56 | DEBUG | 使用已解析的XML处理引用消息
2025-07-21 15:54:56 | INFO | 收到引用消息: 消息ID:581064593 来自:48097389945@chatroom 发送人:zll953369865 内容:可以啊 引用类型:1
2025-07-21 15:54:56 | INFO | [DouBaoImageToImage] 收到引用消息: 可以啊
2025-07-21 15:54:56 | INFO | [TimerTask] 收到引用消息调试信息:
2025-07-21 15:54:56 | INFO |   - 消息内容: 可以啊
2025-07-21 15:54:56 | INFO |   - 群组ID: 48097389945@chatroom
2025-07-21 15:54:56 | INFO |   - 发送人: zll953369865
2025-07-21 15:54:56 | INFO |   - 引用信息: {'MsgType': 1, 'Content': '咋的去大蜀山打一架咩', 'Msgid': '5768304518271977349', 'NewMsgId': '5768304518271977349', 'ToWxid': 'wxid_4usgcju5ey9q29', 'FromWxid': '48097389945@chatroom', 'Nickname': '喵小叶', 'MsgSource': '<msgsource>\n    <sec_msg_node>\n        <alnode>\n            <fr>1</fr>\n        </alnode>\n    </sec_msg_node>\n    <pua>1</pua>\n    <silence>1</silence>\n    <membercount>62</membercount>\n    <signature>N0_V1_8dw6gOp3|v1_pmtAI98A</signature>\n    <tmp_node>\n        <publisher-id />\n    </tmp_node>\n</msgsource>\n', 'Createtime': '1753084398', 'SenderWxid': 'zll953369865'}
2025-07-21 15:54:56 | INFO |   - 引用消息ID: 
2025-07-21 15:54:56 | INFO |   - 引用消息类型: 
2025-07-21 15:54:56 | INFO |   - 引用消息内容: 咋的去大蜀山打一架咩
2025-07-21 15:54:56 | INFO |   - 引用消息发送人: zll953369865
2025-07-21 15:54:57 | DEBUG | 收到消息: {'MsgId': 565537627, 'FromUserName': {'string': '48097389945@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 3, 'Content': {'string': 'wxid_84mmq4cu7ita22:\n<?xml version="1.0"?>\n<msg>\n\t<img aeskey="440beaf5fc0eb520a9f256ceda51e309" encryver="1" cdnthumbaeskey="440beaf5fc0eb520a9f256ceda51e309" cdnthumburl="3057020100044b3049020100020443ec963f02032f559502046641f7df0204687df25a042463653463323231642d393137302d343331362d383235612d653930313531653036646232020405250a020201000405004c55cd00" cdnthumblength="3534" cdnthumbheight="120" cdnthumbwidth="55" cdnmidheight="0" cdnmidwidth="0" cdnhdheight="0" cdnhdwidth="0" cdnmidimgurl="3057020100044b3049020100020443ec963f02032f559502046641f7df0204687df25a042463653463323231642d393137302d343331362d383235612d653930313531653036646232020405250a020201000405004c55cd00" length="71941" md5="fc18bac21058a3be43eb86f27cd9a071" hevc_mid_size="71941" originsourcemd5="91b0d4823c16c50e90d42db46e297c9a">\n\t\t<secHashInfoBase64>eyJwaGFzaCI6IjMwMTAwMDEwMTAwMDEwMDAiLCJwZHFoYXNoIjoiZDRkMjk4YzdjNGY2OWFkYjExZDJjYjliMTk5YTExOTk5ZTlhZDMxOTMxZGI3YjNhNDIzMzMzMjZmYTJiMDEzMyJ9</secHashInfoBase64>\n\t\t<live>\n\t\t\t<duration>0</duration>\n\t\t\t<size>0</size>\n\t\t\t<md5 />\n\t\t\t<fileid />\n\t\t\t<hdsize>0</hdsize>\n\t\t\t<hdmd5 />\n\t\t\t<hdfileid />\n\t\t\t<stillimagetimems>0</stillimagetimems>\n\t\t</live>\n\t</img>\n\t<platform_signature />\n\t<imgdatahash />\n\t<ImgSourceInfo>\n\t\t<ImgSourceUrl />\n\t\t<BizType>0</BizType>\n\t</ImgSourceInfo>\n</msg>\n'}, 'Status': 3, 'ImgStatus': 2, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1753084506, 'MsgSource': '<msgsource>\n\t<bizflag>0</bizflag>\n\t<sec_msg_node>\n\t\t<uuid>280ebd3bad10c011f9227d2c89da733a_</uuid>\n\t\t<risk-file-flag />\n\t\t<risk-file-md5-list />\n\t</sec_msg_node>\n\t<silence>0</silence>\n\t<membercount>62</membercount>\n\t<signature>N0_V1_bNwOtKAs|v1_HqRWytLv</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'PushContent': '喵小叶在群聊中发了一张图片', 'NewMsgId': 4049266461325497863, 'MsgSeq': 871387741}
2025-07-21 15:54:57 | INFO | 收到图片消息: 消息ID:565537627 来自:48097389945@chatroom 发送人:wxid_84mmq4cu7ita22 XML:<?xml version="1.0"?><msg><img aeskey="440beaf5fc0eb520a9f256ceda51e309" encryver="1" cdnthumbaeskey="440beaf5fc0eb520a9f256ceda51e309" cdnthumburl="3057020100044b3049020100020443ec963f02032f559502046641f7df0204687df25a042463653463323231642d393137302d343331362d383235612d653930313531653036646232020405250a020201000405004c55cd00" cdnthumblength="3534" cdnthumbheight="120" cdnthumbwidth="55" cdnmidheight="0" cdnmidwidth="0" cdnhdheight="0" cdnhdwidth="0" cdnmidimgurl="3057020100044b3049020100020443ec963f02032f559502046641f7df0204687df25a042463653463323231642d393137302d343331362d383235612d653930313531653036646232020405250a020201000405004c55cd00" length="71941" md5="fc18bac21058a3be43eb86f27cd9a071" hevc_mid_size="71941" originsourcemd5="91b0d4823c16c50e90d42db46e297c9a"><secHashInfoBase64>eyJwaGFzaCI6IjMwMTAwMDEwMTAwMDEwMDAiLCJwZHFoYXNoIjoiZDRkMjk4YzdjNGY2OWFkYjExZDJjYjliMTk5YTExOTk5ZTlhZDMxOTMxZGI3YjNhNDIzMzMzMjZmYTJiMDEzMyJ9</secHashInfoBase64><live><duration>0</duration><size>0</size><md5 /><fileid /><hdsize>0</hdsize><hdmd5 /><hdfileid /><stillimagetimems>0</stillimagetimems></live></img><platform_signature /><imgdatahash /><ImgSourceInfo><ImgSourceUrl /><BizType>0</BizType></ImgSourceInfo></msg>
2025-07-21 15:54:57 | INFO | [ImageEcho] 保存图片信息成功，当前群 48097389945@chatroom 已存储 5 张图片
2025-07-21 15:54:57 | INFO | [TimerTask] 缓存图片消息: 565537627
2025-07-21 15:54:57 | DEBUG | 收到消息: {'MsgId': 773877993, 'FromUserName': {'string': '48097389945@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 1, 'Content': {'string': 'zll953369865:\n你来'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1753084506, 'MsgSource': '<msgsource>\n\t<sec_msg_node>\n\t\t<alnode>\n\t\t\t<fr>1</fr>\n\t\t</alnode>\n\t</sec_msg_node>\n\t<pua>1</pua>\n\t<silence>0</silence>\n\t<membercount>62</membercount>\n\t<signature>N0_V1_QkiLPCx8|v1_eARY3pkc</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'PushContent': '【微信红包】收到一个微信红包 : 你来', 'NewMsgId': 8964813577604825365, 'MsgSeq': 871387742}
2025-07-21 15:54:57 | INFO | 收到文本消息: 消息ID:773877993 来自:48097389945@chatroom 发送人:zll953369865 @:[] 内容:你来
2025-07-21 15:54:58 | DEBUG | 处理消息内容: '你来'
2025-07-21 15:54:58 | DEBUG | 消息内容 '你来' 不匹配任何命令，忽略
2025-07-21 15:55:03 | DEBUG | 收到消息: {'MsgId': 91970657, 'FromUserName': {'string': '48097389945@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 49, 'Content': {'string': 'wxid_84mmq4cu7ita22:\n<?xml version="1.0"?>\n<msg>\n\t<appmsg appid="" sdkver="0">\n\t\t<title>我不去</title>\n\t\t<des />\n\t\t<action>view</action>\n\t\t<type>57</type>\n\t\t<showtype>0</showtype>\n\t\t<content />\n\t\t<url />\n\t\t<dataurl />\n\t\t<lowurl />\n\t\t<lowdataurl />\n\t\t<recorditem />\n\t\t<thumburl />\n\t\t<messageaction />\n\t\t<laninfo />\n\t\t<refermsg>\n\t\t\t<type>1</type>\n\t\t\t<svrid>8964813577604825365</svrid>\n\t\t\t<fromusr>48097389945@chatroom</fromusr>\n\t\t\t<chatusr>zll953369865</chatusr>\n\t\t\t<createtime>1753084506</createtime>\n\t\t\t<msgsource>&lt;msgsource&gt;\n    &lt;sec_msg_node&gt;\n        &lt;alnode&gt;\n            &lt;fr&gt;1&lt;/fr&gt;\n        &lt;/alnode&gt;\n    &lt;/sec_msg_node&gt;\n    &lt;pua&gt;1&lt;/pua&gt;\n    &lt;silence&gt;1&lt;/silence&gt;\n    &lt;membercount&gt;62&lt;/membercount&gt;\n    &lt;signature&gt;N0_V1_wj0CfaC3|v1_NPn9cJ/n&lt;/signature&gt;\n    &lt;tmp_node&gt;\n        &lt;publisher-id /&gt;\n    &lt;/tmp_node&gt;\n&lt;/msgsource&gt;\n</msgsource>\n\t\t\t<content>你来</content>\n\t\t\t<displayname>【微信红包】收到一个微信红包</displayname>\n\t\t</refermsg>\n\t\t<extinfo />\n\t\t<sourceusername />\n\t\t<sourcedisplayname />\n\t\t<commenturl />\n\t\t<appattach>\n\t\t\t<totallen>0</totallen>\n\t\t\t<attachid />\n\t\t\t<emoticonmd5 />\n\t\t\t<fileext />\n\t\t\t<aeskey />\n\t\t</appattach>\n\t\t<webviewshared>\n\t\t\t<publisherId />\n\t\t\t<publisherReqId>0</publisherReqId>\n\t\t</webviewshared>\n\t\t<weappinfo>\n\t\t\t<pagepath />\n\t\t\t<username />\n\t\t\t<appid />\n\t\t\t<appservicetype>0</appservicetype>\n\t\t</weappinfo>\n\t\t<websearch />\n\t</appmsg>\n\t<fromusername>wxid_84mmq4cu7ita22</fromusername>\n\t<scene>0</scene>\n\t<appinfo>\n\t\t<version>1</version>\n\t\t<appname></appname>\n\t</appinfo>\n\t<commenturl></commenturl>\n</msg>\n'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1753084512, 'MsgSource': '<msgsource>\n\t<pua>1</pua>\n\t<sec_msg_node>\n\t\t<uuid>dcd42ebaab572d8cd9387565c01b14d3_</uuid>\n\t\t<risk-file-flag />\n\t\t<risk-file-md5-list />\n\t</sec_msg_node>\n\t<silence>0</silence>\n\t<membercount>62</membercount>\n\t<signature>N0_V1_b0/ddN4h|v1_WBP0leV5</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'PushContent': '喵小叶 : 我不去', 'NewMsgId': 2909785366153451011, 'MsgSeq': 871387743}
2025-07-21 15:55:03 | DEBUG | 从群聊消息中提取发送者: wxid_84mmq4cu7ita22
2025-07-21 15:55:03 | DEBUG | 使用已解析的XML处理引用消息
2025-07-21 15:55:03 | INFO | 收到引用消息: 消息ID:91970657 来自:48097389945@chatroom 发送人:wxid_84mmq4cu7ita22 内容:我不去 引用类型:1
2025-07-21 15:55:03 | INFO | [DouBaoImageToImage] 收到引用消息: 我不去
2025-07-21 15:55:03 | INFO | [TimerTask] 收到引用消息调试信息:
2025-07-21 15:55:03 | INFO |   - 消息内容: 我不去
2025-07-21 15:55:03 | INFO |   - 群组ID: 48097389945@chatroom
2025-07-21 15:55:03 | INFO |   - 发送人: wxid_84mmq4cu7ita22
2025-07-21 15:55:03 | INFO |   - 引用信息: {'MsgType': 1, 'Content': '你来', 'Msgid': '8964813577604825365', 'NewMsgId': '8964813577604825365', 'ToWxid': 'wxid_4usgcju5ey9q29', 'FromWxid': '48097389945@chatroom', 'Nickname': '【微信红包】收到一个微信红包', 'MsgSource': '<msgsource>\n    <sec_msg_node>\n        <alnode>\n            <fr>1</fr>\n        </alnode>\n    </sec_msg_node>\n    <pua>1</pua>\n    <silence>1</silence>\n    <membercount>62</membercount>\n    <signature>N0_V1_wj0CfaC3|v1_NPn9cJ/n</signature>\n    <tmp_node>\n        <publisher-id />\n    </tmp_node>\n</msgsource>\n', 'Createtime': '1753084506', 'SenderWxid': 'wxid_84mmq4cu7ita22'}
2025-07-21 15:55:03 | INFO |   - 引用消息ID: 
2025-07-21 15:55:03 | INFO |   - 引用消息类型: 
2025-07-21 15:55:03 | INFO |   - 引用消息内容: 你来
2025-07-21 15:55:03 | INFO |   - 引用消息发送人: wxid_84mmq4cu7ita22
2025-07-21 15:55:07 | DEBUG | 收到消息: {'MsgId': 805526357, 'FromUserName': {'string': '48097389945@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 1, 'Content': {'string': 'zll953369865:\n我把你打一顿'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1753084516, 'MsgSource': '<msgsource>\n\t<sec_msg_node>\n\t\t<alnode>\n\t\t\t<fr>1</fr>\n\t\t</alnode>\n\t</sec_msg_node>\n\t<pua>1</pua>\n\t<silence>0</silence>\n\t<membercount>62</membercount>\n\t<signature>N0_V1_er28IkOi|v1_3emgTnbc</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'PushContent': '【微信红包】收到一个微信红包 : 我把你打一顿', 'NewMsgId': 2203768149289201927, 'MsgSeq': 871387744}
2025-07-21 15:55:07 | INFO | 收到文本消息: 消息ID:805526357 来自:48097389945@chatroom 发送人:zll953369865 @:[] 内容:我把你打一顿
2025-07-21 15:55:08 | DEBUG | 处理消息内容: '我把你打一顿'
2025-07-21 15:55:08 | DEBUG | 消息内容 '我把你打一顿' 不匹配任何命令，忽略
2025-07-21 15:55:17 | DEBUG | 收到消息: {'MsgId': 447502569, 'FromUserName': {'string': '48097389945@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 1, 'Content': {'string': 'zll953369865:\n打赢了  你就不敢这么嚣张了'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1753084526, 'MsgSource': '<msgsource>\n\t<sec_msg_node>\n\t\t<alnode>\n\t\t\t<fr>1</fr>\n\t\t</alnode>\n\t</sec_msg_node>\n\t<pua>1</pua>\n\t<silence>0</silence>\n\t<membercount>62</membercount>\n\t<signature>N0_V1_S/cNSvJj|v1_5LDgBtAy</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'PushContent': '【微信红包】收到一个微信红包 : 打赢了  你就不敢这么嚣张了', 'NewMsgId': 1099567407580568794, 'MsgSeq': 871387745}
2025-07-21 15:55:17 | INFO | 收到文本消息: 消息ID:447502569 来自:48097389945@chatroom 发送人:zll953369865 @:[] 内容:打赢了  你就不敢这么嚣张了
2025-07-21 15:55:18 | DEBUG | 处理消息内容: '打赢了  你就不敢这么嚣张了'
2025-07-21 15:55:18 | DEBUG | 消息内容 '打赢了  你就不敢这么嚣张了' 不匹配任何命令，忽略
2025-07-21 15:55:20 | DEBUG | 收到消息: {'MsgId': 654313292, 'FromUserName': {'string': '48097389945@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 1, 'Content': {'string': 'zll953369865:\n哈哈'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1753084528, 'MsgSource': '<msgsource>\n\t<sec_msg_node>\n\t\t<alnode>\n\t\t\t<fr>1</fr>\n\t\t</alnode>\n\t</sec_msg_node>\n\t<pua>1</pua>\n\t<silence>0</silence>\n\t<membercount>62</membercount>\n\t<signature>N0_V1_lLtJStWe|v1_tb3V+i8F</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'PushContent': '【微信红包】收到一个微信红包 : 哈哈', 'NewMsgId': 7302582955802598603, 'MsgSeq': 871387746}
2025-07-21 15:55:20 | INFO | 收到文本消息: 消息ID:654313292 来自:48097389945@chatroom 发送人:zll953369865 @:[] 内容:哈哈
2025-07-21 15:55:20 | DEBUG | 处理消息内容: '哈哈'
2025-07-21 15:55:20 | DEBUG | 消息内容 '哈哈' 不匹配任何命令，忽略
2025-07-21 15:55:22 | DEBUG | 收到消息: {'MsgId': 1937802844, 'FromUserName': {'string': '48097389945@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 1, 'Content': {'string': 'wxid_84mmq4cu7ita22:\n线上的事，线下就算了'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1753084529, 'MsgSource': '<msgsource>\n\t<sec_msg_node>\n\t\t<alnode>\n\t\t\t<fr>1</fr>\n\t\t</alnode>\n\t</sec_msg_node>\n\t<pua>1</pua>\n\t<silence>0</silence>\n\t<membercount>62</membercount>\n\t<signature>N0_V1_J/Dvtrc+|v1_g1fOHaZ5</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'PushContent': '喵小叶 : 线上的事，线下就算了', 'NewMsgId': 8864228303575133570, 'MsgSeq': 871387747}
2025-07-21 15:55:22 | INFO | 收到文本消息: 消息ID:1937802844 来自:48097389945@chatroom 发送人:wxid_84mmq4cu7ita22 @:[] 内容:线上的事，线下就算了
2025-07-21 15:55:22 | DEBUG | 处理消息内容: '线上的事，线下就算了'
2025-07-21 15:55:22 | DEBUG | 消息内容 '线上的事，线下就算了' 不匹配任何命令，忽略
2025-07-21 15:55:29 | DEBUG | 收到消息: {'MsgId': 468013100, 'FromUserName': {'string': '48097389945@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 1, 'Content': {'string': 'zll953369865:\n原来你也怕啊'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1753084538, 'MsgSource': '<msgsource>\n\t<sec_msg_node>\n\t\t<alnode>\n\t\t\t<fr>1</fr>\n\t\t</alnode>\n\t</sec_msg_node>\n\t<pua>1</pua>\n\t<silence>0</silence>\n\t<membercount>62</membercount>\n\t<signature>N0_V1_XpLzw15t|v1_p9La2QHG</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'PushContent': '【微信红包】收到一个微信红包 : 原来你也怕啊', 'NewMsgId': 1493488734691464630, 'MsgSeq': 871387748}
2025-07-21 15:55:29 | INFO | 收到文本消息: 消息ID:468013100 来自:48097389945@chatroom 发送人:zll953369865 @:[] 内容:原来你也怕啊
2025-07-21 15:55:30 | DEBUG | 处理消息内容: '原来你也怕啊'
2025-07-21 15:55:30 | DEBUG | 消息内容 '原来你也怕啊' 不匹配任何命令，忽略
2025-07-21 15:55:32 | DEBUG | 收到消息: {'MsgId': 1399256266, 'FromUserName': {'string': '48097389945@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 47, 'Content': {'string': 'wxid_84mmq4cu7ita22:\n<msg><emoji fromusername = "wxid_84mmq4cu7ita22" tousername = "48097389945@chatroom" type="1" idbuffer="media:0_0" md5="5f2eefb2e365affda1cf919f4a184aea" len = "69236" productid="" androidmd5="5f2eefb2e365affda1cf919f4a184aea" androidlen="69236" s60v3md5 = "5f2eefb2e365affda1cf919f4a184aea" s60v3len="69236" s60v5md5 = "5f2eefb2e365affda1cf919f4a184aea" s60v5len="69236" cdnurl = "http://vweixinf.tc.qq.com/110/20401/stodownload?m=5f2eefb2e365affda1cf919f4a184aea&amp;filekey=30440201010430302e02016e04025348042035663265656662326533363561666664613163663931396634613138346165610203010e74040d00000004627466730000000132&amp;hy=SH&amp;storeid=26690065b000d385f0537eb9d0000006e01004fb1534824b3f0315699dcfff&amp;ef=1&amp;bizid=1022" designerid = "" thumburl = "" encrypturl = "http://vweixinf.tc.qq.com/110/20402/stodownload?m=ab1daa1e1babdc7e6e280f2cd4f3757a&amp;filekey=30440201010430302e02016e04025348042061623164616131653162616264633765366532383066326364346633373537610203010e80040d00000004627466730000000132&amp;hy=SH&amp;storeid=26690065b000deff30537eb9d0000006e02004fb2534824b3f0315699dd00a&amp;ef=2&amp;bizid=1022" aeskey= "a683ffd2b0a54839b4e22e38faffd53b" externurl = "" externmd5 = "" width= "540" height= "133" tpurl= "" tpauthkey= "" attachedtext= "" attachedtextcolor= "" lensid= "" emojiattr= "" linkid= "" desc= "" ></emoji>  </msg>'}, 'Status': 3, 'ImgStatus': 2, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1753084541, 'MsgSource': '<sec_msg_node>\n\t<alnode>\n\t\t<fr>2</fr>\n\t</alnode>\n</sec_msg_node>\n<msgsource>\n\t<silence>0</silence>\n\t<membercount>62</membercount>\n\t<signature>N0_V1_C6bFBdD4|v1_/jwQSp4k</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'PushContent': '喵小叶在群聊中发了一个表情', 'NewMsgId': 584110973135573349, 'MsgSeq': 871387749}
2025-07-21 15:55:32 | INFO | 收到表情消息: 消息ID:1399256266 来自:48097389945@chatroom 发送人:wxid_84mmq4cu7ita22 MD5:5f2eefb2e365affda1cf919f4a184aea 大小:69236
2025-07-21 15:55:32 | DEBUG | [DoubaoImageRecognition] 缓存表情消息: 584110973135573349
2025-07-21 15:55:42 | DEBUG | 收到消息: {'MsgId': 1461935526, 'FromUserName': {'string': '48097389945@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 1, 'Content': {'string': 'wxid_n5c0aekjceu621:\n晚上还要要开黑的兄弟没，限段位（王者0星⭐～50星⭐）'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1753084551, 'MsgSource': '<msgsource>\n\t<pua>1</pua>\n\t<silence>0</silence>\n\t<membercount>62</membercount>\n\t<signature>N0_V1_ixsx0G+n|v1_pjAYwW18</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'PushContent': 'ㅤㅤㅤㅤㅤㅤㅤㅤㅤㅤㅤㅤㅤㅤㅤㅤ : 晚上还要要开黑的兄弟没，限段位（王者0星\ue32f～50星\ue32f）', 'NewMsgId': 1771683862482836245, 'MsgSeq': 871387750}
2025-07-21 15:55:42 | INFO | 收到文本消息: 消息ID:1461935526 来自:48097389945@chatroom 发送人:wxid_n5c0aekjceu621 @:[] 内容:晚上还要要开黑的兄弟没，限段位（王者0星⭐～50星⭐）
2025-07-21 15:55:43 | DEBUG | 处理消息内容: '晚上还要要开黑的兄弟没，限段位（王者0星⭐～50星⭐）'
2025-07-21 15:55:43 | DEBUG | 消息内容 '晚上还要要开黑的兄弟没，限段位（王者0星⭐～50星⭐）' 不匹配任何命令，忽略
2025-07-21 15:57:14 | DEBUG | 收到消息: {'MsgId': 351675197, 'FromUserName': {'string': '47325400669@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 1, 'Content': {'string': 'wxid_62fiham2pn7521:\n八豆你这样说的依据论证是什么，有什么学术论文依据吗'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1753084643, 'MsgSource': '<msgsource>\n\t<pua>1</pua>\n\t<eggIncluded>1</eggIncluded>\n\t<silence>0</silence>\n\t<membercount>219</membercount>\n\t<signature>N0_V1_6A/4b1MN|v1_wI2EA6PM</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'PushContent': '八戒。 : 八豆你这样说的依据论证是什么，有什么学术论文依据吗', 'NewMsgId': 7732365681251893407, 'MsgSeq': 871387751}
2025-07-21 15:57:14 | INFO | 收到文本消息: 消息ID:351675197 来自:47325400669@chatroom 发送人:wxid_62fiham2pn7521 @:[] 内容:八豆你这样说的依据论证是什么，有什么学术论文依据吗
2025-07-21 15:57:14 | DEBUG | 处理消息内容: '八豆你这样说的依据论证是什么，有什么学术论文依据吗'
2025-07-21 15:57:14 | DEBUG | 消息内容 '八豆你这样说的依据论证是什么，有什么学术论文依据吗' 不匹配任何命令，忽略
2025-07-21 15:57:22 | DEBUG | 收到消息: {'MsgId': 1350799676, 'FromUserName': {'string': '27852221909@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 1, 'Content': {'string': 'wxid_idzryo4rneok22:\n副本'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1753084651, 'MsgSource': '<msgsource>\n\t<pua>1</pua>\n\t<eggIncluded>1</eggIncluded>\n\t<silence>1</silence>\n\t<membercount>146</membercount>\n\t<signature>N0_V1_EVN78VBb|v1_cmhTQXTg</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'NewMsgId': 8409585557286172850, 'MsgSeq': 871387752}
2025-07-21 15:57:22 | INFO | 收到文本消息: 消息ID:1350799676 来自:27852221909@chatroom 发送人:wxid_idzryo4rneok22 @:[] 内容:副本
2025-07-21 15:57:22 | DEBUG | 处理消息内容: '副本'
2025-07-21 15:57:22 | DEBUG | 消息内容 '副本' 不匹配任何命令，忽略
2025-07-21 15:57:28 | DEBUG | 收到消息: {'MsgId': 1506975199, 'FromUserName': {'string': '48097389945@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 1, 'Content': {'string': 'wxid_n5c0aekjceu621:\n小恐龙'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1753084658, 'MsgSource': '<msgsource>\n\t<pua>1</pua>\n\t<silence>0</silence>\n\t<membercount>62</membercount>\n\t<signature>N0_V1_yEmqM2Kg|v1_EuEbwibD</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'PushContent': 'ㅤㅤㅤㅤㅤㅤㅤㅤㅤㅤㅤㅤㅤㅤㅤㅤ : 小恐龙', 'NewMsgId': 5113572020449273837, 'MsgSeq': 871387753}
2025-07-21 15:57:28 | INFO | 收到文本消息: 消息ID:1506975199 来自:48097389945@chatroom 发送人:wxid_n5c0aekjceu621 @:[] 内容:小恐龙
2025-07-21 15:57:29 | DEBUG | 处理消息内容: '小恐龙'
2025-07-21 15:57:29 | DEBUG | 消息内容 '小恐龙' 不匹配任何命令，忽略
2025-07-21 15:57:31 | DEBUG | 收到消息: {'MsgId': 997473620, 'FromUserName': {'string': '27852221909@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 1, 'Content': {'string': 'wxid_idzryo4rneok22:\n缺一'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1753084658, 'MsgSource': '<msgsource>\n\t<pua>1</pua>\n\t<eggIncluded>1</eggIncluded>\n\t<silence>1</silence>\n\t<membercount>146</membercount>\n\t<signature>N0_V1_Z4ZjleZV|v1_M8sklU34</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'NewMsgId': 1327447973554284122, 'MsgSeq': 871387754}
2025-07-21 15:57:31 | INFO | 收到文本消息: 消息ID:997473620 来自:27852221909@chatroom 发送人:wxid_idzryo4rneok22 @:[] 内容:缺一
2025-07-21 15:57:31 | DEBUG | 处理消息内容: '缺一'
2025-07-21 15:57:31 | DEBUG | 消息内容 '缺一' 不匹配任何命令，忽略
2025-07-21 15:57:32 | DEBUG | 收到消息: {'MsgId': 559124134, 'FromUserName': {'string': '48097389945@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 47, 'Content': {'string': 'wxid_lneb7n23o4lg12:\n<msg><emoji fromusername = "wxid_lneb7n23o4lg12" tousername = "48097389945@chatroom" type="2" idbuffer="media:0_0" md5="c41b5607fb27d5ac121ee257649c581a" len = "53506" productid="" androidmd5="c41b5607fb27d5ac121ee257649c581a" androidlen="53506" s60v3md5 = "c41b5607fb27d5ac121ee257649c581a" s60v3len="53506" s60v5md5 = "c41b5607fb27d5ac121ee257649c581a" s60v5len="53506" cdnurl = "http://vweixinf.tc.qq.com/110/20401/stodownload?m=c41b5607fb27d5ac121ee257649c581a&amp;filekey=30440201010430302e02016e0402534804206334316235363037666232376435616331323165653235373634396335383161020300d102040d00000004627466730000000132&amp;hy=SH&amp;storeid=2687df2f4000dc1e77d2d13e40000006e01004fb153480fb468e0b6d4d6e23&amp;ef=1&amp;bizid=1022" designerid = "" thumburl = "" encrypturl = "http://vweixinf.tc.qq.com/110/20402/stodownload?m=7e1445a32adc83174f55cd60f22a9285&amp;filekey=30440201010430302e02016e0402534804203765313434356133326164633833313734663535636436306632326139323835020300d110040d00000004627466730000000132&amp;hy=SH&amp;storeid=2687df2f4000e746a7d2d13e40000006e02004fb253480fb468e0b6d4d6e33&amp;ef=2&amp;bizid=1022" aeskey= "fd439050bc9e45ad8d8db25f0e69f318" externurl = "http://vweixinf.tc.qq.com/110/20403/stodownload?m=e2dec73820afdb6b512fca62d1e8c72e&amp;filekey=3043020101042f302d02016e040253480420653264656337333832306166646236623531326663613632643165386337326502024250040d00000004627466730000000132&amp;hy=SH&amp;storeid=2687df2f500001e3c7d2d13e40000006e03004fb353480fb468e0b6d4d6e43&amp;ef=3&amp;bizid=1022" externmd5 = "6c404d4798d22483093a86bc5d1f1873" width= "142" height= "95" tpurl= "" tpauthkey= "" attachedtext= "" attachedtextcolor= "" lensid= "" emojiattr= "" linkid= "" desc= "" ></emoji>  </msg>'}, 'Status': 3, 'ImgStatus': 2, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1753084661, 'MsgSource': '<msgsource>\n\t<silence>0</silence>\n\t<membercount>62</membercount>\n\t<signature>N0_V1_vx+VEN82|v1_NaSwoplp</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'PushContent': 'ᡣ在群聊中发了一个表情', 'NewMsgId': 3994964794812508628, 'MsgSeq': 871387755}
2025-07-21 15:57:32 | INFO | 收到表情消息: 消息ID:559124134 来自:48097389945@chatroom 发送人:wxid_lneb7n23o4lg12 MD5:c41b5607fb27d5ac121ee257649c581a 大小:53506
2025-07-21 15:57:33 | DEBUG | [DoubaoImageRecognition] 缓存表情消息: 3994964794812508628
2025-07-21 15:57:38 | DEBUG | 收到消息: {'MsgId': 695891172, 'FromUserName': {'string': '47325400669@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 1, 'Content': {'string': 'wxid_fh84okl6f5wp22:\n鸭子进化史的相关论述有诸多学术研究作为依据，中国农业大学生物学院黄银花教授课题组的相关研究成果，为鸭子进化史提供了有力的论证，具体如下：\n- 鸭属物种的分化时间：黄银花教授课题组在《细胞报道》发表的《九种高质量鸭属动物基因组为鸭属演化驯化提供新见解》一文指出，通过构建鸭科系统发育树，发现鸭属物种大约在1800万年前从鸭亚科分化出来，并在约386万年前进一步分化，随后琶嘴鸭和花脸鸭大约在331万年前开始分化。\n- 家鸭的起源：该研究通过对绿头鸭、斑嘴鸭等七种野鸭和绍兴鸭进行全基因组测序和组装，构建了理想的鸭驯化模型，认为家鸭的祖先是绿头鸭，斑嘴鸭可能对祖先绿头鸭有直接贡献，也可能对家鸭血缘存在贡献。斑嘴鸭和绿头鸭大约在14.2万年前发生分离，家鸭祖先与绿头鸭分离于约1.9万年前，而北京鸭形成时间则是在887年前。\n- 转座子爆发对鸭子进化的影响：研究发现鸭属动物演化过程中发生了两次LTR逆转录转座子的爆发事件。最近一次爆发大约12.2万年前，发生在家鸭及其近缘物种，如北京鸭、绍兴鸭、绿头鸭和斑嘴鸭；另一次古老的爆发大约92.4万年前，主要发生在另外五种远缘物种。这些LTR逆转录转座子爆发事件为家鸭基因组产生了一些重要变异，包含与家鸭毛色相关的MITF基因、体型相关的IGF2BP1基因等相关的变异，使得家鸭及其祖先野鸭更具有可塑性，环境适应性更强。\n\n此外，还有一些其他研究也能为鸭子进化史提供依据。例如，通过对恐龙化石和鸟类基因的研究，《科学》杂志曾报道哈佛大学从恐龙化石中提取胶原蛋白，与鸡的蛋白序列高度相似，基因分析显示鸟类属于兽脚类恐龙的一支。这也间接证明了鸭子作为鸟类，是从恐龙进化而来的。'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1753084667, 'MsgSource': '<msgsource>\n\t<silence>0</silence>\n\t<membercount>219</membercount>\n\t<signature>N0_V1_5Z0a9/x4|v1_QUEESCcn</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'PushContent': '阿猪米德 : 鸭子进化史的相关论述有诸多学术研究作为依据，中国农业大学生物...', 'NewMsgId': 3502975332233991077, 'MsgSeq': 871387756}
2025-07-21 15:57:38 | INFO | 收到文本消息: 消息ID:695891172 来自:47325400669@chatroom 发送人:wxid_fh84okl6f5wp22 @:[] 内容:鸭子进化史的相关论述有诸多学术研究作为依据，中国农业大学生物学院黄银花教授课题组的相关研究成果，为鸭子进化史提供了有力的论证，具体如下：
- 鸭属物种的分化时间：黄银花教授课题组在《细胞报道》发表的《九种高质量鸭属动物基因组为鸭属演化驯化提供新见解》一文指出，通过构建鸭科系统发育树，发现鸭属物种大约在1800万年前从鸭亚科分化出来，并在约386万年前进一步分化，随后琶嘴鸭和花脸鸭大约在331万年前开始分化。
- 家鸭的起源：该研究通过对绿头鸭、斑嘴鸭等七种野鸭和绍兴鸭进行全基因组测序和组装，构建了理想的鸭驯化模型，认为家鸭的祖先是绿头鸭，斑嘴鸭可能对祖先绿头鸭有直接贡献，也可能对家鸭血缘存在贡献。斑嘴鸭和绿头鸭大约在14.2万年前发生分离，家鸭祖先与绿头鸭分离于约1.9万年前，而北京鸭形成时间则是在887年前。
- 转座子爆发对鸭子进化的影响：研究发现鸭属动物演化过程中发生了两次LTR逆转录转座子的爆发事件。最近一次爆发大约12.2万年前，发生在家鸭及其近缘物种，如北京鸭、绍兴鸭、绿头鸭和斑嘴鸭；另一次古老的爆发大约92.4万年前，主要发生在另外五种远缘物种。这些LTR逆转录转座子爆发事件为家鸭基因组产生了一些重要变异，包含与家鸭毛色相关的MITF基因、体型相关的IGF2BP1基因等相关的变异，使得家鸭及其祖先野鸭更具有可塑性，环境适应性更强。

此外，还有一些其他研究也能为鸭子进化史提供依据。例如，通过对恐龙化石和鸟类基因的研究，《科学》杂志曾报道哈佛大学从恐龙化石中提取胶原蛋白，与鸡的蛋白序列高度相似，基因分析显示鸟类属于兽脚类恐龙的一支。这也间接证明了鸭子作为鸟类，是从恐龙进化而来的。
2025-07-21 15:57:38 | DEBUG | 处理消息内容: '鸭子进化史的相关论述有诸多学术研究作为依据，中国农业大学生物学院黄银花教授课题组的相关研究成果，为鸭子进化史提供了有力的论证，具体如下：
- 鸭属物种的分化时间：黄银花教授课题组在《细胞报道》发表的《九种高质量鸭属动物基因组为鸭属演化驯化提供新见解》一文指出，通过构建鸭科系统发育树，发现鸭属物种大约在1800万年前从鸭亚科分化出来，并在约386万年前进一步分化，随后琶嘴鸭和花脸鸭大约在331万年前开始分化。
- 家鸭的起源：该研究通过对绿头鸭、斑嘴鸭等七种野鸭和绍兴鸭进行全基因组测序和组装，构建了理想的鸭驯化模型，认为家鸭的祖先是绿头鸭，斑嘴鸭可能对祖先绿头鸭有直接贡献，也可能对家鸭血缘存在贡献。斑嘴鸭和绿头鸭大约在14.2万年前发生分离，家鸭祖先与绿头鸭分离于约1.9万年前，而北京鸭形成时间则是在887年前。
- 转座子爆发对鸭子进化的影响：研究发现鸭属动物演化过程中发生了两次LTR逆转录转座子的爆发事件。最近一次爆发大约12.2万年前，发生在家鸭及其近缘物种，如北京鸭、绍兴鸭、绿头鸭和斑嘴鸭；另一次古老的爆发大约92.4万年前，主要发生在另外五种远缘物种。这些LTR逆转录转座子爆发事件为家鸭基因组产生了一些重要变异，包含与家鸭毛色相关的MITF基因、体型相关的IGF2BP1基因等相关的变异，使得家鸭及其祖先野鸭更具有可塑性，环境适应性更强。

此外，还有一些其他研究也能为鸭子进化史提供依据。例如，通过对恐龙化石和鸟类基因的研究，《科学》杂志曾报道哈佛大学从恐龙化石中提取胶原蛋白，与鸡的蛋白序列高度相似，基因分析显示鸟类属于兽脚类恐龙的一支。这也间接证明了鸭子作为鸟类，是从恐龙进化而来的。'
2025-07-21 15:57:38 | DEBUG | 消息内容 '鸭子进化史的相关论述有诸多学术研究作为依据，中国农业大学生物学院黄银花教授课题组的相关研究成果，为鸭子进化史提供了有力的论证，具体如下：
- 鸭属物种的分化时间：黄银花教授课题组在《细胞报道》发表的《九种高质量鸭属动物基因组为鸭属演化驯化提供新见解》一文指出，通过构建鸭科系统发育树，发现鸭属物种大约在1800万年前从鸭亚科分化出来，并在约386万年前进一步分化，随后琶嘴鸭和花脸鸭大约在331万年前开始分化。
- 家鸭的起源：该研究通过对绿头鸭、斑嘴鸭等七种野鸭和绍兴鸭进行全基因组测序和组装，构建了理想的鸭驯化模型，认为家鸭的祖先是绿头鸭，斑嘴鸭可能对祖先绿头鸭有直接贡献，也可能对家鸭血缘存在贡献。斑嘴鸭和绿头鸭大约在14.2万年前发生分离，家鸭祖先与绿头鸭分离于约1.9万年前，而北京鸭形成时间则是在887年前。
- 转座子爆发对鸭子进化的影响：研究发现鸭属动物演化过程中发生了两次LTR逆转录转座子的爆发事件。最近一次爆发大约12.2万年前，发生在家鸭及其近缘物种，如北京鸭、绍兴鸭、绿头鸭和斑嘴鸭；另一次古老的爆发大约92.4万年前，主要发生在另外五种远缘物种。这些LTR逆转录转座子爆发事件为家鸭基因组产生了一些重要变异，包含与家鸭毛色相关的MITF基因、体型相关的IGF2BP1基因等相关的变异，使得家鸭及其祖先野鸭更具有可塑性，环境适应性更强。

此外，还有一些其他研究也能为鸭子进化史提供依据。例如，通过对恐龙化石和鸟类基因的研究，《科学》杂志曾报道哈佛大学从恐龙化石中提取胶原蛋白，与鸡的蛋白序列高度相似，基因分析显示鸟类属于兽脚类恐龙的一支。这也间接证明了鸭子作为鸟类，是从恐龙进化而来的。' 不匹配任何命令，忽略
2025-07-21 15:58:42 | DEBUG | 收到消息: {'MsgId': 1468009025, 'FromUserName': {'string': '48097389945@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 49, 'Content': {'string': 'wxid_84mmq4cu7ita22:\n<?xml version="1.0"?>\n<msg>\n\t<appmsg appid="" sdkver="0">\n\t\t<title>不要总想真实我</title>\n\t\t<des />\n\t\t<action>view</action>\n\t\t<type>57</type>\n\t\t<showtype>0</showtype>\n\t\t<content />\n\t\t<url />\n\t\t<dataurl />\n\t\t<lowurl />\n\t\t<lowdataurl />\n\t\t<recorditem />\n\t\t<thumburl />\n\t\t<messageaction />\n\t\t<laninfo />\n\t\t<refermsg>\n\t\t\t<type>1</type>\n\t\t\t<svrid>1493488734691464630</svrid>\n\t\t\t<fromusr>48097389945@chatroom</fromusr>\n\t\t\t<chatusr>zll953369865</chatusr>\n\t\t\t<createtime>1753084538</createtime>\n\t\t\t<msgsource>&lt;msgsource&gt;\n    &lt;sec_msg_node&gt;\n        &lt;alnode&gt;\n            &lt;fr&gt;1&lt;/fr&gt;\n        &lt;/alnode&gt;\n    &lt;/sec_msg_node&gt;\n    &lt;pua&gt;1&lt;/pua&gt;\n    &lt;silence&gt;1&lt;/silence&gt;\n    &lt;membercount&gt;62&lt;/membercount&gt;\n    &lt;signature&gt;N0_V1_MYC3+hV4|v1_Qo4AtNVQ&lt;/signature&gt;\n    &lt;tmp_node&gt;\n        &lt;publisher-id /&gt;\n    &lt;/tmp_node&gt;\n&lt;/msgsource&gt;\n</msgsource>\n\t\t\t<content>原来你也怕啊</content>\n\t\t\t<displayname>【微信红包】收到一个微信红包</displayname>\n\t\t</refermsg>\n\t\t<extinfo />\n\t\t<sourceusername />\n\t\t<sourcedisplayname />\n\t\t<commenturl />\n\t\t<appattach>\n\t\t\t<totallen>0</totallen>\n\t\t\t<attachid />\n\t\t\t<emoticonmd5 />\n\t\t\t<fileext />\n\t\t\t<aeskey />\n\t\t</appattach>\n\t\t<webviewshared>\n\t\t\t<publisherId />\n\t\t\t<publisherReqId>0</publisherReqId>\n\t\t</webviewshared>\n\t\t<weappinfo>\n\t\t\t<pagepath />\n\t\t\t<username />\n\t\t\t<appid />\n\t\t\t<appservicetype>0</appservicetype>\n\t\t</weappinfo>\n\t\t<websearch />\n\t</appmsg>\n\t<fromusername>wxid_84mmq4cu7ita22</fromusername>\n\t<scene>0</scene>\n\t<appinfo>\n\t\t<version>1</version>\n\t\t<appname></appname>\n\t</appinfo>\n\t<commenturl></commenturl>\n</msg>\n'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1753084732, 'MsgSource': '<msgsource>\n\t<pua>1</pua>\n\t<sec_msg_node>\n\t\t<uuid>8b723422465368f28618c7bdddb454de_</uuid>\n\t\t<risk-file-flag />\n\t\t<risk-file-md5-list />\n\t</sec_msg_node>\n\t<silence>0</silence>\n\t<membercount>62</membercount>\n\t<signature>N0_V1_7mGLfOhh|v1_zm1mpVc2</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'PushContent': '喵小叶 : 不要总想真实我', 'NewMsgId': 4591617075172897131, 'MsgSeq': 871387757}
2025-07-21 15:58:42 | DEBUG | 从群聊消息中提取发送者: wxid_84mmq4cu7ita22
2025-07-21 15:58:42 | DEBUG | 使用已解析的XML处理引用消息
2025-07-21 15:58:42 | INFO | 收到引用消息: 消息ID:1468009025 来自:48097389945@chatroom 发送人:wxid_84mmq4cu7ita22 内容:不要总想真实我 引用类型:1
2025-07-21 15:58:43 | INFO | [DouBaoImageToImage] 收到引用消息: 不要总想真实我
2025-07-21 15:58:43 | INFO | [TimerTask] 收到引用消息调试信息:
2025-07-21 15:58:43 | INFO |   - 消息内容: 不要总想真实我
2025-07-21 15:58:43 | INFO |   - 群组ID: 48097389945@chatroom
2025-07-21 15:58:43 | INFO |   - 发送人: wxid_84mmq4cu7ita22
2025-07-21 15:58:43 | INFO |   - 引用信息: {'MsgType': 1, 'Content': '原来你也怕啊', 'Msgid': '1493488734691464630', 'NewMsgId': '1493488734691464630', 'ToWxid': 'wxid_4usgcju5ey9q29', 'FromWxid': '48097389945@chatroom', 'Nickname': '【微信红包】收到一个微信红包', 'MsgSource': '<msgsource>\n    <sec_msg_node>\n        <alnode>\n            <fr>1</fr>\n        </alnode>\n    </sec_msg_node>\n    <pua>1</pua>\n    <silence>1</silence>\n    <membercount>62</membercount>\n    <signature>N0_V1_MYC3+hV4|v1_Qo4AtNVQ</signature>\n    <tmp_node>\n        <publisher-id />\n    </tmp_node>\n</msgsource>\n', 'Createtime': '1753084538', 'SenderWxid': 'wxid_84mmq4cu7ita22'}
2025-07-21 15:58:43 | INFO |   - 引用消息ID: 
2025-07-21 15:58:43 | INFO |   - 引用消息类型: 
2025-07-21 15:58:43 | INFO |   - 引用消息内容: 原来你也怕啊
2025-07-21 15:58:43 | INFO |   - 引用消息发送人: wxid_84mmq4cu7ita22
2025-07-21 15:59:02 | DEBUG | 收到消息: {'MsgId': 452343512, 'FromUserName': {'string': '48097389945@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 1, 'Content': {'string': 'wxid_84mmq4cu7ita22:\n同在合肥，相煎何太急'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1753084751, 'MsgSource': '<msgsource>\n\t<sec_msg_node>\n\t\t<alnode>\n\t\t\t<fr>1</fr>\n\t\t</alnode>\n\t</sec_msg_node>\n\t<pua>1</pua>\n\t<silence>0</silence>\n\t<membercount>62</membercount>\n\t<signature>N0_V1_JKhx12qa|v1_EwC9huMW</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'PushContent': '喵小叶 : 同在合肥，相煎何太急', 'NewMsgId': 4579259620579944926, 'MsgSeq': 871387758}
2025-07-21 15:59:02 | INFO | 收到文本消息: 消息ID:452343512 来自:48097389945@chatroom 发送人:wxid_84mmq4cu7ita22 @:[] 内容:同在合肥，相煎何太急
2025-07-21 15:59:02 | DEBUG | 处理消息内容: '同在合肥，相煎何太急'
2025-07-21 15:59:02 | DEBUG | 消息内容 '同在合肥，相煎何太急' 不匹配任何命令，忽略
2025-07-21 15:59:10 | DEBUG | 收到消息: {'MsgId': 808894254, 'FromUserName': {'string': '48097389945@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 1, 'Content': {'string': 'wxid_n5c0aekjceu621:\n哈哈'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1753084759, 'MsgSource': '<msgsource>\n\t<pua>1</pua>\n\t<silence>0</silence>\n\t<membercount>62</membercount>\n\t<signature>N0_V1_HLZgZ7UT|v1_O4cU6BnU</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'PushContent': 'ㅤㅤㅤㅤㅤㅤㅤㅤㅤㅤㅤㅤㅤㅤㅤㅤ : 哈哈', 'NewMsgId': 8964632682494744817, 'MsgSeq': 871387759}
2025-07-21 15:59:10 | INFO | 收到文本消息: 消息ID:808894254 来自:48097389945@chatroom 发送人:wxid_n5c0aekjceu621 @:[] 内容:哈哈
2025-07-21 15:59:10 | DEBUG | 处理消息内容: '哈哈'
2025-07-21 15:59:10 | DEBUG | 消息内容 '哈哈' 不匹配任何命令，忽略
2025-07-21 15:59:12 | DEBUG | 收到消息: {'MsgId': 345588252, 'FromUserName': {'string': '48097389945@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 47, 'Content': {'string': 'last--exile:\n<msg><emoji fromusername="last--exile" tousername="48097389945@chatroom" type="2" idbuffer="media:0_0" md5="431c3a89449833af8d5c71ecbc740fad" len="20291" productid="" androidmd5="431c3a89449833af8d5c71ecbc740fad" androidlen="20291" s60v3md5="431c3a89449833af8d5c71ecbc740fad" s60v3len="20291" s60v5md5="431c3a89449833af8d5c71ecbc740fad" s60v5len="20291" cdnurl="http://wxapp.tc.qq.com/262/20304/stodownload?m=431c3a89449833af8d5c71ecbc740fad&amp;filekey=30340201010420301e020201060402535a0410431c3a89449833af8d5c71ecbc740fad02024f43040d00000004627466730000000131&amp;hy=SZ&amp;storeid=32303232303431303231353131383030303536383164333563383038666135383536383830393030303030313036&amp;bizid=1023" designerid="" thumburl="" encrypturl="http://wxapp.tc.qq.com/262/20304/stodownload?m=cdd0e0abd760d7e4282dcd71c4716a13&amp;filekey=30340201010420301e020201060402535a0410cdd0e0abd760d7e4282dcd71c4716a1302024f50040d00000004627466730000000131&amp;hy=SZ&amp;storeid=32303232303431303231353131383030303830393063333563383038666131623535383830393030303030313036&amp;bizid=1023" aeskey="15d1d60d40662f327f4924c32cc78ee6" externurl="http://wxapp.tc.qq.com/262/20304/stodownload?m=6a9378e89ce2afaccced23dbe7d38e00&amp;filekey=30340201010420301e020201060402535a04106a9378e89ce2afaccced23dbe7d38e0002021970040d00000004627466730000000131&amp;hy=SZ&amp;storeid=32303232303431303231353131383030306432333935333563383038666131623535383830393030303030313036&amp;bizid=1023" externmd5="060fc6e4163cbe944cd0b4c4971d9b8a" width="300" height="253" tpurl="" tpauthkey="" attachedtext="" attachedtextcolor="" lensid="" emojiattr="ChbohJHlrZDph4zmnInnlLvpnaLkuoYu" linkid="" desc=""></emoji></msg>'}, 'Status': 3, 'ImgStatus': 2, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1753084759, 'MsgSource': '<msgsource>\n\t<silence>0</silence>\n\t<membercount>62</membercount>\n\t<signature>N0_V1_j+H+263x|v1_uazzylLd</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'PushContent': '亮在群聊中发了一个表情', 'NewMsgId': 8529836931080186995, 'MsgSeq': 871387760}
2025-07-21 15:59:12 | INFO | 收到表情消息: 消息ID:345588252 来自:48097389945@chatroom 发送人:last--exile MD5:431c3a89449833af8d5c71ecbc740fad 大小:20291
2025-07-21 15:59:12 | DEBUG | [DoubaoImageRecognition] 缓存表情消息: 8529836931080186995
2025-07-21 15:59:19 | DEBUG | 收到消息: {'MsgId': 84789769, 'FromUserName': {'string': '48097389945@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 1, 'Content': {'string': 'wxid_84mmq4cu7ita22:\n老表'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1753084768, 'MsgSource': '<msgsource>\n\t<sec_msg_node>\n\t\t<alnode>\n\t\t\t<fr>1</fr>\n\t\t</alnode>\n\t</sec_msg_node>\n\t<pua>1</pua>\n\t<silence>0</silence>\n\t<membercount>62</membercount>\n\t<signature>N0_V1_edA6maoS|v1_6bIrPnWJ</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'PushContent': '喵小叶 : 老表', 'NewMsgId': 2293987485314216000, 'MsgSeq': 871387761}
2025-07-21 15:59:19 | INFO | 收到文本消息: 消息ID:84789769 来自:48097389945@chatroom 发送人:wxid_84mmq4cu7ita22 @:[] 内容:老表
2025-07-21 15:59:19 | DEBUG | 处理消息内容: '老表'
2025-07-21 15:59:19 | DEBUG | 消息内容 '老表' 不匹配任何命令，忽略
2025-07-21 15:59:27 | DEBUG | 收到消息: {'MsgId': 1193316275, 'FromUserName': {'string': '27852221909@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 1, 'Content': {'string': 'wxid_bmzp9achod6922:\n我忙了下'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1753084776, 'MsgSource': '<msgsource>\n\t<bizflag>0</bizflag>\n\t<pua>1</pua>\n\t<eggIncluded>1</eggIncluded>\n\t<silence>1</silence>\n\t<membercount>146</membercount>\n\t<signature>N0_V1_gC3mesmC|v1_+CG58YBL</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'NewMsgId': 4427001567706098557, 'MsgSeq': 871387762}
2025-07-21 15:59:27 | INFO | 收到文本消息: 消息ID:1193316275 来自:27852221909@chatroom 发送人:wxid_bmzp9achod6922 @:[] 内容:我忙了下
2025-07-21 15:59:27 | DEBUG | 处理消息内容: '我忙了下'
2025-07-21 15:59:27 | DEBUG | 消息内容 '我忙了下' 不匹配任何命令，忽略
2025-07-21 15:59:36 | DEBUG | 收到消息: {'MsgId': 2083768632, 'FromUserName': {'string': '48097389945@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 1, 'Content': {'string': 'wxid_n5c0aekjceu621:\n我都不着急该有的缘分总会相见的'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1753084785, 'MsgSource': '<msgsource>\n\t<pua>1</pua>\n\t<silence>0</silence>\n\t<membercount>62</membercount>\n\t<signature>N0_V1_+shAlH1M|v1_WGFDnzA2</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'PushContent': 'ㅤㅤㅤㅤㅤㅤㅤㅤㅤㅤㅤㅤㅤㅤㅤㅤ : 我都不着急该有的缘分总会相见的', 'NewMsgId': 2821719074437445584, 'MsgSeq': 871387763}
2025-07-21 15:59:36 | INFO | 收到文本消息: 消息ID:2083768632 来自:48097389945@chatroom 发送人:wxid_n5c0aekjceu621 @:[] 内容:我都不着急该有的缘分总会相见的
2025-07-21 15:59:36 | DEBUG | 处理消息内容: '我都不着急该有的缘分总会相见的'
2025-07-21 15:59:36 | DEBUG | 消息内容 '我都不着急该有的缘分总会相见的' 不匹配任何命令，忽略
2025-07-21 15:59:38 | DEBUG | 收到消息: {'MsgId': 1100008872, 'FromUserName': {'string': '47325400669@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 1, 'Content': {'string': 'wxid_62fiham2pn7521:\n八豆把你引用到的学术研究依据文献出处和作者和关键摘要，发布日期，以时间线整理给钱'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1753084787, 'MsgSource': '<msgsource>\n\t<pua>1</pua>\n\t<eggIncluded>1</eggIncluded>\n\t<silence>0</silence>\n\t<membercount>219</membercount>\n\t<signature>N0_V1_hPLk6xwp|v1_oihU/emr</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'PushContent': '八戒。 : 八豆把你引用到的学术研究依据文献出处和作者和关键摘要，发布日...', 'NewMsgId': 2985526573948407178, 'MsgSeq': 871387764}
2025-07-21 15:59:38 | INFO | 收到文本消息: 消息ID:1100008872 来自:47325400669@chatroom 发送人:wxid_62fiham2pn7521 @:[] 内容:八豆把你引用到的学术研究依据文献出处和作者和关键摘要，发布日期，以时间线整理给钱
2025-07-21 15:59:38 | DEBUG | 处理消息内容: '八豆把你引用到的学术研究依据文献出处和作者和关键摘要，发布日期，以时间线整理给钱'
2025-07-21 15:59:38 | DEBUG | 消息内容 '八豆把你引用到的学术研究依据文献出处和作者和关键摘要，发布日期，以时间线整理给钱' 不匹配任何命令，忽略
2025-07-21 16:00:00 | DEBUG | 收到消息: {'MsgId': 1477293630, 'FromUserName': {'string': '48097389945@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 49, 'Content': {'string': 'zll953369865:\n<?xml version="1.0"?>\n<msg>\n\t<appmsg appid="" sdkver="0">\n\t\t<title>哟 你开始讲大道理</title>\n\t\t<des />\n\t\t<action>view</action>\n\t\t<type>57</type>\n\t\t<showtype>0</showtype>\n\t\t<content />\n\t\t<url />\n\t\t<dataurl />\n\t\t<lowurl />\n\t\t<lowdataurl />\n\t\t<recorditem />\n\t\t<thumburl />\n\t\t<messageaction />\n\t\t<laninfo />\n\t\t<refermsg>\n\t\t\t<type>1</type>\n\t\t\t<svrid>4579259620579944926</svrid>\n\t\t\t<fromusr>48097389945@chatroom</fromusr>\n\t\t\t<chatusr>wxid_84mmq4cu7ita22</chatusr>\n\t\t\t<createtime>1753084751</createtime>\n\t\t\t<msgsource>&lt;msgsource&gt;\n    &lt;sec_msg_node&gt;\n        &lt;alnode&gt;\n            &lt;fr&gt;1&lt;/fr&gt;\n        &lt;/alnode&gt;\n    &lt;/sec_msg_node&gt;\n    &lt;pua&gt;1&lt;/pua&gt;\n    &lt;silence&gt;1&lt;/silence&gt;\n    &lt;membercount&gt;62&lt;/membercount&gt;\n    &lt;signature&gt;N0_V1_HcEkmNiH|v1_bB863Oo8&lt;/signature&gt;\n    &lt;tmp_node&gt;\n        &lt;publisher-id /&gt;\n    &lt;/tmp_node&gt;\n&lt;/msgsource&gt;\n</msgsource>\n\t\t\t<content>同在合肥，相煎何太急</content>\n\t\t\t<displayname>喵小叶</displayname>\n\t\t</refermsg>\n\t\t<extinfo />\n\t\t<sourceusername />\n\t\t<sourcedisplayname />\n\t\t<commenturl />\n\t\t<appattach>\n\t\t\t<totallen>0</totallen>\n\t\t\t<attachid />\n\t\t\t<emoticonmd5 />\n\t\t\t<fileext />\n\t\t\t<aeskey />\n\t\t</appattach>\n\t\t<webviewshared>\n\t\t\t<publisherId />\n\t\t\t<publisherReqId>0</publisherReqId>\n\t\t</webviewshared>\n\t\t<weappinfo>\n\t\t\t<pagepath />\n\t\t\t<username />\n\t\t\t<appid />\n\t\t\t<appservicetype>0</appservicetype>\n\t\t</weappinfo>\n\t\t<websearch />\n\t</appmsg>\n\t<fromusername>zll953369865</fromusername>\n\t<scene>0</scene>\n\t<appinfo>\n\t\t<version>1</version>\n\t\t<appname></appname>\n\t</appinfo>\n\t<commenturl></commenturl>\n</msg>\n'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1753084809, 'MsgSource': '<msgsource>\n\t<pua>1</pua>\n\t<sec_msg_node>\n\t\t<uuid>583d9b093950d1fb407c7b85aac1ebf1_</uuid>\n\t\t<risk-file-flag />\n\t\t<risk-file-md5-list />\n\t</sec_msg_node>\n\t<silence>0</silence>\n\t<membercount>62</membercount>\n\t<signature>N0_V1_m7OXryrJ|v1_fRe4BHeS</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'PushContent': '【微信红包】收到一个微信红包 : 哟 你开始讲大道理', 'NewMsgId': 8789386079415822922, 'MsgSeq': 871387765}
2025-07-21 16:00:00 | DEBUG | 从群聊消息中提取发送者: zll953369865
2025-07-21 16:00:00 | DEBUG | 使用已解析的XML处理引用消息
2025-07-21 16:00:00 | INFO | 收到引用消息: 消息ID:1477293630 来自:48097389945@chatroom 发送人:zll953369865 内容:哟 你开始讲大道理 引用类型:1
2025-07-21 16:00:00 | INFO | [DouBaoImageToImage] 收到引用消息: 哟 你开始讲大道理
2025-07-21 16:00:00 | INFO | [TimerTask] 收到引用消息调试信息:
2025-07-21 16:00:00 | INFO |   - 消息内容: 哟 你开始讲大道理
2025-07-21 16:00:00 | INFO |   - 群组ID: 48097389945@chatroom
2025-07-21 16:00:00 | INFO |   - 发送人: zll953369865
2025-07-21 16:00:00 | INFO |   - 引用信息: {'MsgType': 1, 'Content': '同在合肥，相煎何太急', 'Msgid': '4579259620579944926', 'NewMsgId': '4579259620579944926', 'ToWxid': 'wxid_4usgcju5ey9q29', 'FromWxid': '48097389945@chatroom', 'Nickname': '喵小叶', 'MsgSource': '<msgsource>\n    <sec_msg_node>\n        <alnode>\n            <fr>1</fr>\n        </alnode>\n    </sec_msg_node>\n    <pua>1</pua>\n    <silence>1</silence>\n    <membercount>62</membercount>\n    <signature>N0_V1_HcEkmNiH|v1_bB863Oo8</signature>\n    <tmp_node>\n        <publisher-id />\n    </tmp_node>\n</msgsource>\n', 'Createtime': '1753084751', 'SenderWxid': 'zll953369865'}
2025-07-21 16:00:00 | INFO |   - 引用消息ID: 
2025-07-21 16:00:00 | INFO |   - 引用消息类型: 
2025-07-21 16:00:00 | INFO |   - 引用消息内容: 同在合肥，相煎何太急
2025-07-21 16:00:00 | INFO |   - 引用消息发送人: zll953369865
