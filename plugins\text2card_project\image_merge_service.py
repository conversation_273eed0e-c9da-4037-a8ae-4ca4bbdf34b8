from pathlib import Path
import logging
import io
import os
import httpx
from typing import List, Union, Optional, Tuple, Set
from PIL import Image, ImageDraw
from plugins.text2card_project.utils.image_processor import ImageProcessor
from plugins.text2card_project.text_card_service import ImageCache, ImageError, NetworkError

class ImageMergeService:
    """图片合并服务类

    完全独立的实现，专注于图片合并功能
    """

    def __init__(self, cache_dir: str = None):
        """初始化图片合并服务

        Args:
            cache_dir: 可选的缓存目录路径
        """
        self._cache_dir = Path(cache_dir) if cache_dir else Path(__file__).parent / 'cache'
        self._logger = logging.getLogger('ImageMergeService')
        self._cache = ImageCache(self._cache_dir)
        self._processor = ImageProcessor()
        self._temp_files: Set[str] = set()  # 记录需要清理的临时文件

    async def merge_images(self,
                          images: List[Union[str, Image.Image, Path]],
                          output_path: Optional[str] = None,
                          direction: str = 'vertical',
                          spacing: int = 50,
                          enhance: bool = True,
                          radius: int = 30,
                          target_width: Optional[int] = None,
                          align: str = 'center',
                          beautify: bool = False,  # 是否启用美化效果
                          **kwargs) -> Union[str, Image.Image]:
        """合并多张图片

        Args:
            images: 图片列表(支持URL、PIL Image对象或本地路径)
            output_path: 可选的输出路径
            direction: 合并方向('horizontal'/'vertical'), 默认垂直
            spacing: 图片间距，默认50像素
            enhance: 是否增强图片
            radius: 圆角半径
            target_width: 目标宽度
            align: 对齐方式('top'/'center'/'bottom' 用于水平合并, 'left'/'center'/'right' 用于垂直合并)
            beautify: 是否启用美化效果(边框、阴影等)
            **kwargs: 其他配置参数

        Returns:
            合并后的图片或保存路径
        """
        try:
            # 处理输入图片
            processed_images = []
            failed_images = []

            for img in images:
                try:
                    if isinstance(img, (str, Path)):
                        # 下载或加载图片
                        loaded_img = await self._load_image(img)
                        if isinstance(img, str):
                            # 记录下载的临时文件
                            self._temp_files.add(str(img))
                    else:
                        loaded_img = img

                    # 调整大小(如果需要)
                    if target_width:
                        loaded_img = self._processor.resize_image(loaded_img, target_width=target_width)

                    # 增强效果(如果需要)
                    if enhance:
                        loaded_img = self._processor.enhance_image(loaded_img)

                    # 添加圆角(如果需要)
                    if radius > 0:
                        loaded_img = self._processor.add_corners(loaded_img, radius)

                    processed_images.append(loaded_img)

                except Exception as e:
                    self._logger.warning(f"处理图片失败: {str(e)}")
                    failed_images.append(img)
                    continue

            if not processed_images:
                raise ImageError("没有可用的图片进行合并")

            # 合并图片
            result = self._merge_images(processed_images,
                                      direction=direction,
                                      spacing=spacing,
                                      align=align,
                                      **kwargs)

            # 添加美化效果(如果需要)
            if beautify:
                result = self._beautify_image(result, **kwargs)

            # 保存或返回
            if output_path:
                result.save(output_path, quality=95, optimize=True)
                # 记录输出文件
                self._temp_files.add(str(output_path))
                return output_path
            return result

        except Exception as e:
            self._logger.error(f"合并图片失败: {str(e)}")
            raise ImageError(f"合并图片失败: {str(e)}")

    def cleanup_temp_files(self):
        """清理所有临时文件"""
        for file_path in self._temp_files:
            try:
                if os.path.exists(file_path):
                    os.remove(file_path)
                    self._logger.debug(f"已删除临时文件: {file_path}")
            except Exception as e:
                self._logger.warning(f"删除临时文件失败 {file_path}: {str(e)}")
        self._temp_files.clear()

    async def batch_process_images(self,
                                 image_urls: List[str],
                                 output_dir: Union[str, Path],
                                 **kwargs) -> Tuple[str, List[str], List[str]]:
        """批量处理图片

        Args:
            image_urls: 图片URL列表
            output_dir: 输出目录
            **kwargs: 传递给merge_images的参数

        Returns:
            Tuple[合并后的图片路径, 成功处理的图片路径列表, 处理失败的图片URL列表]
        """
        output_dir = Path(output_dir)
        output_dir.mkdir(parents=True, exist_ok=True)

        # 下载并处理所有图片
        processed_paths = []
        failed_urls = []

        for i, url in enumerate(image_urls):
            try:
                # 下载并保存单张图片
                img_path = output_dir / f"image_{i}.jpg"
                img = await self._load_image(url)
                img.save(img_path)
                processed_paths.append(str(img_path))
                self._temp_files.add(str(img_path))
            except Exception as e:
                self._logger.warning(f"处理图片失败 {url}: {str(e)}")
                failed_urls.append(url)

        if not processed_paths:
            raise ImageError("所有图片处理都失败了")

        # 合并成功下载的图片
        merged_path = output_dir / "merged_result.png"
        try:
            await self.merge_images(
                images=processed_paths,
                output_path=str(merged_path),
                **kwargs
            )
        except Exception as e:
            self._logger.error(f"合并图片失败: {str(e)}")
            raise

        return str(merged_path), processed_paths, failed_urls

    async def __aenter__(self):
        """异步上下文管理器入口"""
        return self

    async def __aexit__(self, exc_type, exc_val, exc_tb):
        """异步上下文管理器退出时清理临时文件"""
        self.cleanup_temp_files()

    async def _load_image(self, source: Union[str, Path]) -> Image.Image:
        """加载图片

        Args:
            source: 图片来源(URL或本地路径)

        Returns:
            PIL Image对象
        """
        try:
            if isinstance(source, Path) or not str(source).startswith(('http://', 'https://')):
                return Image.open(source)

            # 检查缓存
            cache_path = self._cache.get_cache_path(str(source))
            if cache_path:
                return Image.open(cache_path)

            # 下载图片
            async with httpx.AsyncClient() as client:
                response = await client.get(str(source), timeout=10.0)
                if response.status_code != 200:
                    raise NetworkError(f"下载图片失败: {response.status_code}")
                content = response.content
                image = Image.open(io.BytesIO(content))

            # 保存到缓存
            self._cache.save_to_cache(str(source), image)
            return image

        except httpx.RequestError as e:
            raise NetworkError(f"网络请求失败: {str(e)}")
        except Exception as e:
            raise ImageError(f"加载图片失败: {str(e)}")

    def _merge_images(self,
                     images: List[Image.Image],
                     direction: str = 'horizontal',
                     spacing: int = 50,
                     align: str = 'center',
                     background_color: str = 'white',
                     border_width: int = 2,
                     padding: int = 40) -> Image.Image:
        """合并图片

        Args:
            images: 处理后的图片列表
            direction: 合并方向
            spacing: 间距
            align: 对齐方式
            background_color: 背景颜色
            border_width: 边框宽度
            padding: 内边距

        Returns:
            合并后的图片
        """
        if not images:
            raise ValueError("图片列表不能为空")

        # 计算总尺寸(含padding)
        if direction == 'vertical':
            total_width = max(img.size[0] for img in images) + padding * 2
            total_height = (sum(img.size[1] for img in images) +
                          spacing * (len(images) - 1) + padding * 2)
        else:
            total_width = (sum(img.size[0] for img in images) +
                         spacing * (len(images) - 1) + padding * 2)
            total_height = max(img.size[1] for img in images) + padding * 2

        # 创建基础图片(带背景色)
        result = Image.new('RGBA', (total_width, total_height), background_color)

        # 添加边框
        if border_width > 0:
            draw = ImageDraw.Draw(result)
            draw.rectangle(
                [(border_width//2, border_width//2),
                 (total_width-border_width//2-1, total_height-border_width//2-1)],
                outline='#EEEEEE',
                width=border_width
            )

        # 粘贴图片
        x, y = padding, padding
        for img in images:
            if direction == 'vertical':
                # 水平对齐
                if align == 'left':
                    x = padding
                elif align == 'right':
                    x = total_width - img.size[0] - padding
                else:  # center
                    x = (total_width - img.size[0]) // 2

                result.paste(img, (x, y), img if img.mode == 'RGBA' else None)
                y += img.size[1] + spacing
            else:
                # 垂直对齐
                if align == 'top':
                    y = padding
                elif align == 'bottom':
                    y = total_height - img.size[1] - padding
                else:  # center
                    y = (total_height - img.size[1]) // 2

                result.paste(img, (x, y), img if img.mode == 'RGBA' else None)
                x += img.size[0] + spacing

        return result

    def _beautify_image(self,
                       image: Image.Image,
                       background_color: str = 'white',
                       border_radius: int = 30,
                       border_color: str = '#EEEEEE',
                       border_width: int = 2,
                       padding: int = 40,
                       shadow: bool = True,
                       gradient: bool = True) -> Image.Image:
        """为图片添加美化效果

        Args:
            image: 原始图片
            background_color: 背景颜色
            border_radius: 边框圆角半径
            border_color: 边框颜色
            border_width: 边框宽度
            padding: 内边距
            shadow: 是否添加阴影
            gradient: 是否添加渐变背景

        Returns:
            美化后的图片
        """
        # 计算新尺寸
        width = image.size[0] + padding * 2
        height = image.size[1] + padding * 2

        # 创建基础图片
        result = Image.new('RGBA', (width, height), (0, 0, 0, 0))

        # 添加渐变背景
        if gradient:
            gradient_overlay = self._create_gradient_background(width, height, background_color)
            result = Image.alpha_composite(result, gradient_overlay)
        else:
            # 使用纯色背景
            background = Image.new('RGBA', (width, height), background_color)
            result = Image.alpha_composite(result, background)

        # 添加边框
        if border_width > 0:
            border = self._create_border(width, height, border_radius, border_color, border_width)
            result = Image.alpha_composite(result, border)

        # 添加阴影
        if shadow:
            shadow_layer = self._create_shadow(width, height, border_radius)
            result = Image.alpha_composite(shadow_layer, result)

        # 粘贴原图
        result.paste(image, (padding, padding), image if image.mode == 'RGBA' else None)

        return result