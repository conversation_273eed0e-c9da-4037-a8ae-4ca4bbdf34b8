[TimerTask]
enable = true
command = ["定时", "提醒我", "闹钟"]
command-format = """
定时任务使用说明：

📝 文本任务：
- 定时 10分钟后 开会提醒
- 提醒我 明天8点 起床
- 定时 每天18点 下班了

🖼️ 图片任务：
1. 先发送一张图片
2. 引用该图片并发送：定时 10分钟后
3. 系统将在指定时间发送该图片

⏰ 支持的时间格式：
- 相对时间：10分钟后、2小时后、30秒后
- 绝对时间：明天8点、18点、下午6点
- 重复任务：每天18点、每天18:30、每小时、每30分钟
- 每周任务：每周一18点、每周五19:30、每周日10:00

📋 管理命令：
- 群任务 - 查看本群所有任务
- 删除群任务1 或 删除群任务 1 - 删除第1个任务
- 修改群任务1每周五18:00 或 修改群任务 1 每周五 18:00 - 修改时间
- 改时间1明天8点 或 改时间 1 明天8点 - 快速修改
- 改成每周五18:00钻石 或 改成 每周五 18:00 钻石 - 关键词修改
- 任务1提醒我 - 设置个人艾特提醒（任务执行时会单独艾特您）
- 清空群任务 - 删除本群所有任务

💡 支持紧凑格式（无空格）和标准格式（有空格）
💡 兼容旧命令：我的任务、删除任务、修改任务等
💡 📢数字表示有多少人设置了该任务的提醒
"""
natural_response = false  # 关闭自然化，直接回复结果

[TimerTask.rate_limit]
cooldown = 2
