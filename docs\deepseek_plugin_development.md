# Deepseek插件开发经验总结

## 1. 项目背景

Deepseek插件是一个基于Deepseek API的聊天机器人插件，用于在微信中提供AI对话服务。本文档记录了开发过程中的经验和解决方案，以供后续类似项目参考。

## 2. 抓包分析经验

### 2.1 关键请求信息

在开发初期，我们通过抓包分析获取了以下关键信息：

- API地址：`https://chatgpt.vivo.com.cn/chatgpt-api/chat/public/completions`
- 请求方式：POST
- 关键请求头：
  - X-AI-GATEWAY-TIMESTAMP
  - X-uid
  - X-AI-GATEWAY-NONCE
  - X-AI-GATEWAY-SIGNATURE
  - X-AI-GATEWAY-APP-ID
  - uucToken
  - uuid

### 2.2 抓包技巧

1. 使用Fiddler/Charles等工具抓取HTTPS请求
2. 关注请求头中的认证信息和时间戳
3. 分析请求体格式和必要参数
4. 注意观察动态变化的参数（如timestamp、nonce等）

### 2.3 遇到的问题

1. **签名生成问题**
   - 问题：初期尝试自行生成签名，但无法复现正确的签名算法
   - 解决方案：直接使用抓包获取的完整请求头，避免签名生成的复杂性

2. **请求参数有效期**
   - 问题：部分参数（如timestamp）有时效性
   - 解决方案：定期更新这些参数，或使用固定的测试参数

## 3. 会话管理

### 3.1 会话ID处理

1. **初始方案**：动态生成会话ID
   ```python
   self._session_id = str(uuid.uuid4())
   ```

2. **遇到的问题**：
   - 每次生成新的会话ID导致上下文丢失
   - 服务端返回"sessionId对应会话未创建"错误

3. **优化方案**：
   ```python
   # 使用固定的会话ID
   self._session_id = "9765b271-b132-4007-ab6e-676383924bdf"
   ```

### 3.2 经验总结

- 优先使用固定的会话ID，除非API明确要求动态生成
- 保存会话状态，便于维护对话上下文
- 考虑会话超时和清理机制

## 4. 流式响应处理

### 4.1 SSE响应格式

服务器返回的是Server-Sent Events (SSE)格式的流式响应：
```
event: thinking
data: {"type":"thinking","message":"思考中..."}

event: text
data: {"type":"text","message":"实际响应内容"}
```

### 4.2 实现方案

1. 使用aiohttp处理流式响应
2. 解析SSE格式数据
3. 区分不同类型的事件（thinking/text）
4. 只保留最后一条完整消息

```python
async def _process_stream(self, response):
    buffer = []
    current_event = None
    
    async for line in response.content:
        if line.startswith('event:'):
            current_event = line[6:].strip()
        elif line.startswith('data:'):
            data = line[5:].strip()
            if data == "[DONE]":
                break
            try:
                json_data = json.loads(data)
                if json_data.get("type") in ["thinking", "text"]:
                    message = json_data.get("message", "")
                    if message:
                        buffer.append(message)
            except json.JSONDecodeError:
                continue
                
    return buffer[-1] if buffer else ""
```

### 4.3 注意事项

- 处理SSE格式数据时需要正确解析事件类型
- 考虑网络异常情况的处理
- 合理使用缓冲区，避免内存占用过大
- 正确处理结束标记（[DONE]）

## 5. 图片生成优化

### 5.1 初始实现

最初的实现方案是在获取到API响应后再生成图片：

```python
# 1. 获取API响应
text = await self._process_stream(response)

# 2. 生成图片
image_path = await self._generate_card(text)

# 3. 发送图片
await bot.send_image_message(message["FromWxid"], image_path)
```

### 5.2 优化方案

为了提高响应速度，实现了并行处理：

1. 在发送API请求前就开始下载头部图片
2. 使用asyncio.create_task创建异步任务
3. 等待API响应的同时完成图片下载
4. 最后合成完整的图片卡片

```python
# 1. 创建下载头图任务
header_image_task = asyncio.create_task(self._download_header_image())

# 2. 发送API请求
text = await self._process_stream(response)

# 3. 等待头图下载完成
header_image_path = await header_image_task

# 4. 生成图片卡片
image_path = await self._generate_card(text, header_image_path)
```

### 5.3 性能提升

- 减少用户等待时间
- 提高资源利用效率
- 优化用户体验

## 6. 错误处理

### 6.1 主要错误类型

1. 网络请求错误
2. API响应错误
3. 会话管理错误
4. 图片生成错误

### 6.2 错误处理策略

```python
try:
    # API请求处理
except aiohttp.ClientError as e:
    logger.error(f"网络请求错误: {e}")
    await bot.send_text_message(
        message["FromWxid"],
        "❌网络请求失败，请稍后重试"
    )
except json.JSONDecodeError as e:
    logger.error(f"JSON解析错误: {e}")
except Exception as e:
    logger.error(f"处理失败: {str(e)}")
    logger.error(f"错误详情: {traceback.format_exc()}")
finally:
    # 清理临时文件
    await self._cleanup_temp_files()
```

### 6.3 最佳实践

1. 使用具体的异常类型而不是笼统的Exception
2. 详细的日志记录，包括错误堆栈
3. 友好的用户错误提示
4. 资源清理确保在finally块中执行

## 7. 配置管理

### 7.1 配置文件结构

```toml
[DeepseekPlugin]
enable = true
command = ["deepseek", "ds"]

# API配置
api-url = "https://chatgpt.vivo.com.cn/chatgpt-api/chat/public/completions"
timestamp = "1739632923"
uid = "2d3ae4195eb9de657bdb48a383551d1c"
nonce = "48dd5211"
signature = "Yxt53KQgp2cLpjU9wR/92kLTe5JnawRSxaYay9QvpyQ="
app-id = "4483725646"
```

### 7.2 配置管理建议

1. 将敏感信息放在配置文件中
2. 使用类型安全的配置解析
3. 提供默认值和配置验证
4. 支持配置热更新

## 8. 开发建议

### 8.1 通用建议

1. 先通过抓包分析API接口特点
2. 使用固定测试数据快速验证
3. 逐步完善功能，分步骤调试
4. 注重代码复用和模块化

### 8.2 调试技巧

1. 使用详细的日志记录
2. 保存关键请求响应样本
3. 使用测试账号和测试群
4. 模拟各种异常情况

### 8.3 性能优化

1. 使用异步编程提高并发
2. 实现请求缓存机制
3. 优化资源使用
4. 监控关键指标

## 9. 总结

开发Deepseek插件的过程中，我们积累了宝贵的经验：

1. 抓包分析是关键的第一步
2. 会话管理需要特别注意
3. 流式响应处理要考虑完备
4. 图片生成可以优化性能
5. 错误处理要周到细致
6. 配置管理要灵活安全

这些经验对于开发类似的API集成插件都有重要的参考价值。通过合理的规划和实现，可以避免很多常见的问题，提高开发效率。 