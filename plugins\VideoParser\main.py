import json,re,traceback,tomllib,httpx,time,asyncio,os,urllib.parse,base64,subprocess,math,random
from loguru import logger
import xml.etree.ElementTree as ET
from PIL import Image
from pathlib import Path

# 添加一个使用 ffmpeg 创建视频的函数
async def create_slideshow(image_paths, output_path, fps=24, duration=3, music_url=None):
    try:
        os.makedirs("temp", exist_ok=True); os.makedirs("temp/video", exist_ok=True); os.makedirs("temp/resized", exist_ok=True)
        music_file = None
        if music_url:
            try:
                music_file = "temp/background_music.mp3"
                async with httpx.AsyncClient() as client:
                    response = await client.get(music_url, timeout=30)
                    if response.status_code == 200: open(music_file, "wb").write(response.content)
                    else: music_file = None
            except: music_file = None
        has_music = music_file is not None and os.path.exists(music_file)
        if len(image_paths) == 1:
            cmd = ["ffmpeg", "-y", "-loop", "1", "-i", image_paths[0], "-c:v", "libx264", "-pix_fmt", "yuv420p", "-t", str(duration), "-r", str(fps), "-preset", "medium", "-tune", "stillimage", "-crf", "18"]
            if has_music: cmd.extend(["-i", music_file, "-c:a", "aac", "-b:a", "128k", "-shortest"])
            cmd.append(output_path)
            try: subprocess.run(cmd, check=True); return True
            except: return False
        image_dimensions = []
        for img_path in image_paths:
            try: image_dimensions.append(Image.open(img_path).size)
            except: pass
        if not image_dimensions: return False
        avg_aspect_ratio = sum(w/h for w, h in image_dimensions) / len(image_dimensions)
        target_width, target_height = (1280, 720) if avg_aspect_ratio > 1.0 else (720, 1280)
        resized_images = []
        for i, img_path in enumerate(image_paths):
            try:
                Image.open(img_path).close()
                resized_path = f"temp/resized/img_{i:03d}.jpg"
                resize_cmd = ["ffmpeg", "-y", "-i", img_path, "-vf", f"scale={target_width}:{target_height}:force_original_aspect_ratio=decrease,pad={target_width}:{target_height}:(ow-iw)/2:(oh-ih)/2:black", "-q:v", "1", "-pix_fmt", "yuv420p", resized_path]
                subprocess.run(resize_cmd, check=True); resized_images.append(resized_path)
            except: continue
        if not resized_images: return False

        filter_script = "temp/video/filter_complex.txt"
        transitions = [{"name": "circleclose", "duration": 1.2}, {"name": "circleopen", "duration": 1.2}, {"name": "diagbl", "duration": 1.0}, {"name": "diagbr", "duration": 1.0}, {"name": "diagtl", "duration": 1.0}, {"name": "diagtr", "duration": 1.0}, {"name": "hlslice", "duration": 1.2}, {"name": "hrslice", "duration": 1.2}, {"name": "vuslice", "duration": 1.2}, {"name": "vdslice", "duration": 1.2}, {"name": "radial", "duration": 1.5}, {"name": "smoothleft", "duration": 1.2}, {"name": "smoothright", "duration": 1.2}, {"name": "smoothup", "duration": 1.2}, {"name": "smoothdown", "duration": 1.2}, {"name": "pixelize", "duration": 1.3}, {"name": "zoomin", "duration": 1.5}, {"name": "fadeblack", "duration": 1.0}, {"name": "fadewhite", "duration": 1.0}, {"name": "distance", "duration": 1.5}, {"name": "wiperight", "duration": 1.0}, {"name": "wipeleft", "duration": 1.0}]
        image_durations = [round(random.uniform(2.0, 5.0), 1) for _ in range(len(image_paths))]
        filter_content = ""
        for i in range(len(resized_images)):
            filter_content += f"[{i}:v] scale={target_width}:{target_height}, setpts=PTS-STARTPTS [v{i}];\n"
        filter_content += f"[v0] trim=duration={image_durations[0]},setpts=PTS-STARTPTS [firstv];\n"
        random.shuffle(transitions)
        for i in range(1, len(resized_images)):
            transition = transitions[i % len(transitions)]["name"]
            transition_duration = min(random.uniform(1.0, 1.8), image_durations[i] / 2)
            filter_content += f"[v{i}] trim=duration={image_durations[i]},setpts=PTS-STARTPTS [v{i}out];\n[v{i-1}out][v{i}out] xfade=transition={transition}:duration={transition_duration}:offset={image_durations[i-1]-transition_duration} [v{i}transitioned];\n"

        if len(resized_images) == 1: filter_content += "[firstv] concat=n=1:v=1:a=0 [outv]"
        elif len(resized_images) == 2: filter_content += "[firstv][v1transitioned] concat=n=2:v=1:a=0 [outv]"
        else: filter_content += f"[firstv][v1transitioned]{''.join(f'[v{i}transitioned]' for i in range(2, len(resized_images)))} concat=n={len(resized_images)}:v=1:a=0 [outv]"
        with open(filter_script, "w", encoding="utf-8") as f: f.write(filter_content)

        temp_video = "temp/video/temp_video.mp4"
        input_args = []
        for i, img in enumerate(resized_images):
            img_duration = image_durations[i] if i < len(image_durations) else duration
            input_args.extend(["-loop", "1", "-t", str(img_duration), "-i", img])
        video_cmd = ["ffmpeg", "-y", *input_args, "-filter_complex_script", filter_script, "-map", "[outv]", "-c:v", "libx264", "-preset", "medium", "-crf", "22", "-r", str(fps), "-pix_fmt", "yuv420p", temp_video]

        try: subprocess.run(video_cmd, check=True)
        except:
            concat_file = "temp/video/concat.txt"
            with open(concat_file, "w", encoding="utf-8") as f:
                for i, img_path in enumerate(resized_images):
                    img_duration = image_durations[i] if i < len(image_durations) else duration
                    f.write(f"file '{os.path.abspath(img_path)}'\nduration {img_duration}\n")
                f.write(f"file '{os.path.abspath(resized_images[-1])}'\n")
            video_cmd = ["ffmpeg", "-y", "-f", "concat", "-safe", "0", "-i", concat_file, "-c:v", "libx264", "-preset", "medium", "-crf", "22", "-r", str(fps), "-pix_fmt", "yuv420p", "-vf", f"fps=24,format=yuv420p,unsharp=5:5:1.0:5:5:0.0,eq=saturation=1.2,fade=in:0:25,fade=out:{(len(resized_images) * duration * fps) - 25}:25", temp_video]
            try: subprocess.run(video_cmd, check=True)
            except: return False

        if has_music:
            video_duration = float(subprocess.check_output(["ffprobe", "-v", "error", "-show_entries", "format=duration", "-of", "default=noprint_wrappers=1:nokey=1", temp_video]).decode().strip())
            music_duration = float(subprocess.check_output(["ffprobe", "-v", "error", "-show_entries", "format=duration", "-of", "default=noprint_wrappers=1:nokey=1", music_file]).decode().strip())
            final_cmd = ["ffmpeg", "-y", "-i", temp_video] + (["-stream_loop", str(math.ceil(video_duration / music_duration) - 1)] if music_duration < video_duration else []) + ["-i", music_file, "-c:v", "copy", "-c:a", "aac", "-b:a", "128k", "-shortest", output_path]
            subprocess.run(final_cmd, check=True)
        else: __import__('shutil').copy(temp_video, output_path)
        for file_path in [filter_script, temp_video]:
            if os.path.exists(file_path): os.remove(file_path)
        return True

    except:
        return False

from WechatAPI import WechatAPIClient
from utils.decorators import on_text_message, on_quote_message
from utils.plugin_base import PluginBase
from utils.temp_file_manager import cleanup_file
from plugins.URLShortener import URLShortenerService
from plugins.Doubao import Doubao
from plugins.text2card_project import TextCardService

class VideoParserPlugin(PluginBase):
    """视频解析插件"""

    description = "视频解析插件 - 支持多平台视频解析"
    author = "Claude"
    version = "1.1.0"

    def __init__(self):
        super().__init__()

        # 初始化短链接服务
        self.url_shortener = URLShortenerService()

        # 初始化豆包服务(用于总结功能)
        self.doubao = Doubao()

        # 初始化TextCardService
        self.text_card = TextCardService()

        # 初始化临时目录
        self.temp_dir = Path("temp/image")
        self.temp_dir.mkdir(parents=True, exist_ok=True)

        # 语音临时目录
        self.voice_dir = Path("temp/voice")
        self.voice_dir.mkdir(parents=True, exist_ok=True)

        # 添加视频临时目录
        self.video_dir = Path("temp/video")
        self.video_dir.mkdir(parents=True, exist_ok=True)
        # 初始化配置
        self._init_config()

    def _init_config(self):
        try: config = tomllib.load(open(os.path.join(os.path.dirname(__file__), "config.toml"), "rb")).get("VideoParserPlugin", {})
        except: config = {}
        self.enable,self.command,self.cache_time,self.max_retries = config.get("enable",True),config.get("command",["解析"]),config.get("cache-time",3600),config.get("max-retries",3)
        self.voice_api,self.voice_pattern = config.get("voice-api","http://www.yx520.ltd/API/wzzyy/zmp3.php"),re.compile(r'^转(\d+)$')
        self.video_number_api,self.video_number_secret = config.get("video-number-api","https://api.dudunas.top/api/vxdown"),config.get("video-number-secret","bf91ad9748b3be2cdd48cca599d5446d")
        self.local_api,self.dragon_api,self.tuji_api = config.get("local-api","http://192.168.100.1:8080/video/share/url/parse?url="),config.get("dragon-api","https://www.hhlqilongzhu.cn/api/sp_jx/sp.php?url="),config.get("tuji-api","https://api.317ak.com/API/tuji/api.php?url=")
        self.summary_api,self.summary_qq,self.summary_key = config.get("summary-api","http://www.yx520.ltd/API/doubao/db.php"),config.get("summary-qq","741628350"),config.get("summary-key","wVV5tJhF041I6kj2")
        self.backup_apis,self.player_prefix,self.backup_players = config.get("backup-apis",[]),config.get("player-prefix","http://109a.cn/bfq/api.php?url="),config.get("backup-players",[])
        self.command_handlers = {"总结一下":self._handle_summary,"解析":self._handle_video,"取图集":self._handle_image_set,"图转视频":self._handle_image_to_video,"视频号":self._handle_video_number,"转图片":self._handle_text_to_image,"转语音":self._handle_text_to_voice}
        self.local_pattern = r'https?://(?:v\.kuaishou\.com/[a-zA-Z0-9]+/?|\w+\.kuaishou\.com/[a-zA-Z0-9]+/?|www\.douyin\.com/(?:note|video)/\d+/?|v\.douyin\.com/[a-zA-Z0-9\-]+/?|h5\.pipix\.com/s/[a-zA-Z0-9]+/?|haokan\.hao123\.com/v\?vid=\d+(?:&[^&]+)*|www\.meipai\.com/video/\d+(?:/\d+)?(?:\?[^?]+)*|xhslink\.com(?:/[a-zA-Z0-9]+)+/?|www\.xiaohongshu\.com/discovery/item/[a-zA-Z0-9]+(?:\?[^?]+)*|h5\.pipigx\.com/pp/(?:post|review)/\d+(?:\?[^?]+)*|www\.pearvideo\.com/(?:detail_)?\d+(?:\?[^?]+)*|(?:video|weibo)\.weibo\.(?:com|cn)/(?:show|tv)/[^\s]+|m\.oasis\.weibo\.cn/v1/h5/share(?:\?[^?]+)*|(?:kg|node)\.qq\.com/(?:\w+/)*[a-zA-Z0-9]+(?:[/?][^?]+)*|v\.ixigua\.com/[a-zA-Z0-9]+/?|share\.xiaochuankeji\.cn/[^\s]+|kuai\.360\.cn/[^\s]+|h5\.weishi\.qq\.com/[^\s]+)'
        self.dragon_pattern = r'https?://(?:(?:www\.)?bilibili\.com/video/(?:BV[\w]+|av\d+)/?|b23\.tv/[a-zA-Z0-9]+/?|www\.kuaishou\.com/short-video/[a-zA-Z0-9]+/?|m\.acfun\.cn/v/\?ac=\d+(?:&[^&]+)*|v\.6\.cn/(?:minivideo|show)/\d+/?|www\.iesdouyin\.com/(?:share/video|xg/video)/\d+/?|v\.qq\.com/[^\s]+|v\.youku\.com/[^\s]+|www\.iqiyi\.com/[^\s]+)'
        self.url_cache,self.api_status = {},{"local":{"fails":0,"last_fail":0},"dragon":{"fails":0,"last_fail":0}}

    def _extract_url(self, content: str) -> tuple[str, str]:
        """提取URL并判断类型"""
        if not content:
            return None, None

        # 检查缓存
        cache_key = content.strip()
        if cache_key in self.url_cache and time.time() - self.url_cache[cache_key]["time"] < self.cache_time:
            return self.url_cache[cache_key]["url"], self.url_cache[cache_key]["type"]
        elif cache_key in self.url_cache:
            del self.url_cache[cache_key]

        # 处理XML消息中的URL
        if content.startswith("<msg>") and "<url>" in content and "</url>" in content:
            try:
                url_match = re.search(r'<url>(.*?)</url>', content)
                if url_match and "xiaohongshu.com" in (url := url_match.group(1).replace("&amp;", "&")):
                    self.url_cache[cache_key] = {"url": url, "type": "local", "time": time.time()}
                    return url, "local"
            except:
                pass
        # URL匹配
        for pattern, url_type in [(self.local_pattern, "local"), (self.dragon_pattern, "dragon")]:
            if match := re.search(pattern, content):
                url = match.group(0)
                self.url_cache[cache_key] = {"url": url, "type": url_type, "time": time.time()}
                return url, url_type
        return None, None

    async def _parse_video_number(self, object_id: str, object_nonce_id: str) -> dict:
        """解析视频号视频"""
        for _ in range(self.max_retries):
            try:
                result = await self._do_parse_video_number(object_id, object_nonce_id)
                if result: return result
            except: pass
            await asyncio.sleep(1)
        return None

    async def _do_parse_video_number(self, object_id: str, object_nonce_id: str) -> dict:
        """实际的视频号解析函数"""
        try:
            params = {
                "objectId": object_id,
                "objectNonceId": object_nonce_id,
                "AppSecret": self.video_number_secret
            }

            async with httpx.AsyncClient() as client:
                response = await client.get(self.video_number_api, params=params, timeout=30)
                if response.status_code == 200:
                    result = response.json()
                    if result.get("code") == 200:
                        data = result.get("data", {})

                        # 获取原始视频URL
                        original_url = data.get("url", "")

                        # 对视频URL进行URL编码（percent-encoding）
                        encoded_url = ""
                        if original_url:
                            try:
                                # 使用urllib.parse.quote进行URL编码
                                encoded_url = urllib.parse.quote(original_url, safe='')
                            except:
                                encoded_url = original_url

                        return {"title": "视频号内容", "description": data.get("desc", "暂无描述"), "video_url": original_url, "encoded_url": encoded_url, "cover_url": data.get("img", ""), "source": "video_number"}
                    return None

        except:
            return None

    async def _parse_local_video(self, url: str) -> dict:
        """本地解析接口"""
        try:
            async with httpx.AsyncClient() as client:
                response = await client.get(f"{self.tuji_api}{url}", timeout=30)
                if response.status_code == 200:
                    result = response.json()
                    if result.get("code") == 200 and result.get("data", {}).get("images"):
                        data = result.get("data", {})
                        return {"title": data.get("title", "未知图集"), "description": data.get("author", "未知作者"), "images": data.get("images", []), "type": "tuji"}
        except: pass
        for _ in range(self.max_retries):
            try:
                result = await self._do_parse_local_video(url)
                if result: return result
            except: pass
            await asyncio.sleep(1)
        return None

    async def _do_parse_local_video(self, url: str) -> dict:
        """实际的本地解析函数"""
        try:
            async with httpx.AsyncClient() as client:
                response = await client.get(f"{self.local_api}{url}", timeout=30)
                if response.status_code == 200:
                    result = response.json()
                    if result.get("code") == 200:
                        data = result.get("data", {})
                        return {"title": data.get("title", "未知视频"), "description": data.get("description", "暂无描述"), "video_url": data.get("video_url", ""), "cover_url": data.get("cover_url", ""), "source": "local"}
                    return None
        except:
            self.api_status["local"]["fails"] += 1
            self.api_status["local"]["last_fail"] = time.time()
            return None

    async def _parse_dragon_video(self, url: str) -> dict:
        """龙珠解析接口"""
        for _ in range(self.max_retries):
            try:
                result = await self._do_parse_dragon_video(url)
                if result: return result
            except: pass
            await asyncio.sleep(1)
        return None

    async def _do_parse_dragon_video(self, url: str) -> dict:
        """实际的龙珠解析函数"""
        try:
            async with httpx.AsyncClient() as client:
                response = await client.get(f"{self.dragon_api}{url}", timeout=30)
                if response.status_code == 200:
                    result = response.json()
                    if result.get("code") == 200:
                        data = result.get("data", {})
                        return {"title": data.get("title", "未知视频"), "description": data.get("description", "暂无描述"), "video_url": data.get("url", ""), "cover_url": data.get("cover", ""), "source": "dragon"}
                    return None
        except:
            self.api_status["dragon"]["fails"] += 1
            self.api_status["dragon"]["last_fail"] = time.time()
            return None

    def _get_player_url(self, video_url: str, source: str) -> str:
        """获取播放器URL，如果主播放器失败则尝试备用播放器

        Args:
            video_url: 视频URL，可能是原始URL或已经URL编码后的URL
            source: 视频来源，如"video_number"、"local"、"dragon"等

        Returns:
            str: 最终的播放URL
        """
        # 本地和龙珠解析的视频直接返回原始链接
        if source in ["local", "dragon"]:
            return video_url
        # 视频号解析的视频需要加播放器前缀
        try:
            encoded_url = video_url if '%' in video_url else urllib.parse.quote(video_url, safe='')
            return f"{self.player_prefix}{encoded_url}"
        except:
            for player in self.backup_players:
                try:
                    encoded_url = video_url if '%' in video_url else urllib.parse.quote(video_url, safe='')
                    return f"{player}{encoded_url}"
                except:
                    pass
            return video_url

    async def _send_video_card(self, bot: WechatAPIClient, wxid: str, video_info: dict):
        """发送视频卡片消息"""
        # 处理图集类型
        if video_info.get("type") == "tuji" and video_info.get("images"):
            try:
                # 创建临时目录
                temp_dir = Path("temp/tuji")
                temp_dir.mkdir(parents=True, exist_ok=True)
                timestamp = int(time.time())

                # 临时文件路径列表
                temp_files = []
                downloaded_images = []
                merged_image_path = f"temp/tuji/merged_{timestamp}.jpg"
                temp_files.append(merged_image_path)

                # 下载所有图片
                async with httpx.AsyncClient() as client:
                    for i, img_url in enumerate(video_info["images"]):
                        try:
                            response = await client.get(img_url, timeout=60)
                            if response.status_code == 200:
                                # 保存临时图片
                                img_path = f"temp/tuji/img_{timestamp}_{i}.jpg"
                                temp_files.append(img_path)
                                with open(img_path, "wb") as f:
                                    f.write(response.content)

                                # 打开图片并添加到列表
                                img = Image.open(img_path)
                                downloaded_images.append(img)
                        except:
                            continue

                if not downloaded_images:
                    await bot.send_text_message(wxid, "❌ 图片下载失败")
                    return

                # 合并图片
                total_height = sum(img.height for img in downloaded_images)
                max_width = max(img.width for img in downloaded_images)
                merged_image = Image.new('RGB', (max_width, total_height))

                y_offset = 0
                for img in downloaded_images:
                    merged_image.paste(img, (0, y_offset))
                    y_offset += img.height

                # 保存合并后的图片
                merged_image.save(merged_image_path)

                # 使用二进制方式发送图片
                with open(merged_image_path, "rb") as f:
                    image_data = f.read()
                    await bot.send_image_message(wxid, image_data)

                # 设置延迟清理图片文件 - 使用统一管理器
                for file_path in temp_files:
                    cleanup_file(file_path, delay_seconds=60)
                return

            except:
                await bot.send_text_message(wxid, "❌ 图集处理失败")
                return

        # 处理视频类型(原有逻辑)
        if not video_info.get("video_url"):
            await bot.send_text_message(wxid, "❌ 未获取到视频地址")
            return

        # 获取播放地址 - 优先使用编码后的URL（如果存在）
        video_url = video_info.get("video_url", "")
        encoded_url = video_info.get("encoded_url", "")

        # 根据来源和是否有编码URL决定使用哪个URL
        play_url = self._get_player_url(encoded_url if video_info.get("source") == "video_number" and encoded_url else video_url, video_info.get("source", ""))

        try:
            await bot.send_link_message(
                wxid,
                url=play_url,
                title=video_info["title"],
                description=video_info["description"],
                thumb_url=video_info["cover_url"]
            )
        except:
            source_text = {"video_number": "视频号", "local": "本地解析", "dragon": "龙珠解析"}.get(video_info.get("source"), "未知来源")

            try:
                # 在文本消息中只包含播放链接，避免过长
                await bot.send_text_message(
                    wxid,
                    f"✅ 解析成功 (来源: {source_text})\n\n"
                    f"🎬 {video_info['title']}\n"
                    f"📝 {video_info['description']}\n\n"
                    f"🎮 播放链接: {play_url}"
                )
            except Exception:
                # 尝试发送更简短的消息
                await bot.send_text_message(
                    wxid,
                    f"✅ 解析成功，但链接过长无法发送\n请复制链接到浏览器打开"
                )

    def _extract_url_from_xml(self, xml_content: str) -> str:
        """从XML中提取URL

        Args:
            xml_content: XML格式的消息内容

        Returns:
            str: 提取到的URL,提取失败返回None
        """
        try:
            print(f"[DEBUG VideoParser] 开始从XML提取微信链接...")
            print(f"[DEBUG VideoParser] XML内容长度: {len(xml_content)} 字符")

            # 首先尝试匹配短格式URL
            short_url_pattern = r'https?://mp\.weixin\.qq\.com/s/[a-zA-Z0-9_-]+'
            short_urls = re.findall(short_url_pattern, xml_content)
            if short_urls:
                print(f"[DEBUG VideoParser] 找到短格式URL: {short_urls[0]}")
                return short_urls[0]

            print(f"[DEBUG VideoParser] 未找到短格式URL，尝试解析XML...")
            root = ET.fromstring(xml_content)

            # 提取所有可能的URL
            urls = []

            # 从webviewshared提取
            webview = root.find(".//webviewshared")
            if webview is not None:
                # 优先使用shareUrlOpen
                url_node = webview.find("shareUrlOpen")
                if url_node is not None and url_node.text and "mp.weixin.qq.com" in url_node.text:
                    urls.append(url_node.text.strip())

                # 备选shareUrlOriginal
                url_node = webview.find("shareUrlOriginal")
                if url_node is not None and url_node.text and "mp.weixin.qq.com" in url_node.text:
                    urls.append(url_node.text.strip())

            # 从url标签提取
            url_elem = root.find(".//url")
            if url_elem is not None and url_elem.text and "mp.weixin.qq.com" in url_elem.text:
                urls.append(url_elem.text.strip())

            # 处理提取到的URL
            print(f"[DEBUG VideoParser] 从XML节点提取到 {len(urls)} 个候选URL")
            for i, url in enumerate(urls):
                if url:
                    print(f"[DEBUG VideoParser] 候选URL {i+1}: {url[:100]}...")
                    if "mp.weixin.qq.com/s" in url:
                        print(f"[DEBUG VideoParser] 选择URL: {url}")
                        return url
            print(f"[DEBUG VideoParser] 未找到有效的微信文章URL")
            return None
        except Exception as e:
            print(f"[DEBUG VideoParser] XML解析失败: {str(e)}")
            return None

    async def _get_summary(self, url: str) -> dict:
        """调用总结API获取内容总结"""
        try:
            params = {
                "qq": self.summary_qq,
                "key": self.summary_key,
                "msg": f"总结一下: {url}"
            }

            async with httpx.AsyncClient() as client:
                response = await client.get(self.summary_api, params=params, timeout=60)
                if response.status_code == 200:
                    result = response.text
                    try:
                        json_result = json.loads(result)
                        return {
                            "code": json_result.get("code"),
                            "text": json_result.get("text", "").strip(),
                            "voice_url": json_result.get("voice_url")
                        }
                    except:
                        return None
                return None
        except:
            return None

    async def _handle_summary(self, bot: WechatAPIClient, message: dict, quote: dict) -> None:
        """处理总结命令"""
        quote_content = quote.get("Content", "")
        if not quote_content:
            await bot.send_text_message(message["FromWxid"], "❌ 未找到需要总结的内容")
            return

        # 提取URL
        url = None
        if quote.get("MsgType") == 49:  # XML消息
            try:
                url = self._extract_url_from_xml(quote_content)
            except:
                await bot.send_text_message(message["FromWxid"], "❌ 提取链接失败")
                return

        if not url:
            await bot.send_text_message(message["FromWxid"], "❌ 未找到可以总结的链接")
            return

        print(f"[DEBUG VideoParser] 提取到的原始URL: {url}")
        print(f"[DEBUG VideoParser] 原始URL长度: {len(url)} 字符")

        # 使用短链接服务处理URL
        try:
            print(f"[DEBUG VideoParser] 开始短链接处理...")
            short_url = await self.url_shortener.shorten_url(url)
            if not short_url:
                print(f"[DEBUG VideoParser] 短链接处理失败")
                await bot.send_text_message(message["FromWxid"], "❌ 链接处理失败，请稍后重试")
                return
            print(f"[DEBUG VideoParser] 短链接处理完成: {short_url}")
            print(f"[DEBUG VideoParser] 短链接长度: {len(short_url)} 字符")
            url = short_url
        except Exception as e:
            print(f"[DEBUG VideoParser] 短链接处理异常: {str(e)}")
            await bot.send_text_message(message["FromWxid"], "❌ 链接处理失败，请稍后重试")
            return

        # 构造总结请求
        try:
            summary_query = f"帮我总结一下这篇文章的内容: {url}"
            print(f"[DEBUG VideoParser] 构造的总结查询: {summary_query}")
            print(f"[DEBUG VideoParser] 即将调用豆包API...")
            # 调用豆包API进行总结
            summary_result = await self.doubao.call_doubao_api(bot, message, summary_query)

            if not summary_result:
                await bot.send_text_message(message["FromWxid"], "❌ 总结失败，请稍后重试")
                return
            if not isinstance(summary_result, dict):
                await bot.send_text_message(message["FromWxid"], "❌ 总结失败，返回格式异常")
                return

            # 处理文本类型的响应
            if summary_result.get("type") == "text":
                text = summary_result.get("text", "").strip()

                if not text:
                    await bot.send_text_message(message["FromWxid"], "❌ 总结内容为空，请稍后重试")
                    return

                # 等待完整响应
                no_change_count = 0
                for _ in range(15):
                    await asyncio.sleep(2)
                    new_result = await self.doubao.call_doubao_api(bot, message, summary_query)
                    if new_result and isinstance(new_result, dict) and new_result.get("type") == "text":
                        new_text = new_result.get("text", "").strip()
                        if new_text and len(new_text) > len(text):
                            text = new_text
                            no_change_count = 0
                        else:
                            no_change_count += 1
                    if no_change_count >= 3 and ("。" in text[-5:] or "!" in text[-5:] or "！" in text[-5:]):
                        break

                # 使用TextCardService生成图片
                temp_dir = Path("temp")
                temp_dir.mkdir(parents=True, exist_ok=True)
                timestamp = int(time.time())
                header_image_path = str(temp_dir / f"header_{timestamp}.png")
                content_image_path = str(temp_dir / f"content_{timestamp}.png")
                final_image_path = str(temp_dir / f"summary_{timestamp}.png")

                try:
                    # 1. 先下载头部图片
                    header_image_url = "https://api.317ak.com/API/tp/dmdntp.php"
                    header_downloaded = False

                    try:
                        # 使用TextCardService的_download_image方法下载头部图片
                        header_image = await self.text_card._download_image(header_image_url)
                        if header_image:
                            # 处理头部图片
                            processed_header = self.text_card._process_header_image(
                                header_image,
                                width=1000,  # 设置宽度
                                enhance=True  # 启用图片增强
                            )
                            processed_header.save(header_image_path)
                            header_downloaded = True
                    except:
                        pass

                    # 2. 生成文本内容图片
                    content_success = await self.text_card.generate_card(
                        text=text,
                        output_path=content_image_path,
                        width=1000,  # 设置宽度
                        gradient=True,  # 启用渐变效果
                        theme='light',  # 使用亮色主题
                        title="文章内容总结"  # 添加标题
                    )

                    if not content_success or not os.path.exists(content_image_path):
                        raise Exception("生成内容图片失败")

                    # 3. 合并图片(如果头部图片下载成功)
                    if header_downloaded and os.path.exists(header_image_path):
                        # 创建ImageMergeService实例
                        from plugins.text2card_project.image_merge_service import ImageMergeService
                        merger = ImageMergeService()

                        # 合并图片
                        await merger.merge_images(
                            images=[header_image_path, content_image_path],
                            output_path=final_image_path,
                            direction='vertical',
                            spacing=20,
                            enhance=False,  # 图片已经增强过
                            radius=30,
                            beautify=True
                        )

                    else:
                        final_image_path = content_image_path

                    # 4. 发送图片
                    if os.path.exists(final_image_path):
                        # 读取图片数据并发送
                        with open(final_image_path, "rb") as f:
                            image_data = f.read()
                            await bot.send_image_message(message["FromWxid"], image_data)
                    else:
                        await bot.send_text_message(message["FromWxid"], f"📝 内容总结:\n\n{text}")

                    # 5. 设置延迟清理图片文件 - 使用统一管理器
                    for path in [header_image_path, content_image_path, final_image_path]:
                        if path:
                            cleanup_file(path, delay_seconds=10)

                except:
                    await bot.send_text_message(message["FromWxid"], f"📝 内容总结:\n\n{text}")

                    # 清理文件 - 使用统一管理器
                    for path in [header_image_path, content_image_path, final_image_path]:
                        if path:
                            cleanup_file(path)

            else:
                await bot.send_text_message(message["FromWxid"], "❌ 总结失败，响应格式异常")
        except asyncio.TimeoutError:
            await bot.send_text_message(message["FromWxid"], "❌ 总结处理超时，请稍后重试")
        except:
            await bot.send_text_message(message["FromWxid"], "❌ 总结服务异常，请稍后重试")

    async def _handle_video(self, bot: WechatAPIClient, message: dict, quote: dict) -> None:
        """处理视频解析命令"""
        quote_content = quote.get("Content", "")
        if not quote_content:
            original_content = message.get("Content", "")
            if isinstance(original_content, dict):
                original_content = original_content.get("string", "")
            quote_content = original_content
        url, parse_type = self._extract_url(quote_content)

        if not url:
            platforms = [
                "抖音", "快手", "B站", "视频号", "微博视频",
                "小红书", "皮皮虾", "微视", "全民K歌", "腾讯视频",
                "优酷", "爱奇艺", "西瓜视频", "最右", "快影"
            ]
            await bot.send_text_message(
                message["FromWxid"],
                f"❌ 未检测到支持的视频链接\n\n"
                f"🎯 支持的平台:\n{', '.join(platforms)}"
            )
            return
        if parse_type == "local":
            video_info = await self._parse_local_video(url)
        else:
            video_info = await self._parse_dragon_video(url)

        if video_info:
            await self._send_video_card(bot, message["FromWxid"], video_info)
        else:
            await bot.send_text_message(
                message["FromWxid"],
                "❌ 视频解析失败，请稍后重试\n"
                "💡 可能的原因:\n"
                "1. 视频已失效或被删除\n"
                "2. 当前平台暂不支持解析\n"
                "3. 解析服务暂时不可用"
            )

    async def _handle_image_set(self, bot: WechatAPIClient, message: dict, quote: dict) -> None:
        """处理图集解析命令"""
        quote_content = quote.get("Content", "")
        if not quote_content:
            await bot.send_text_message(message["FromWxid"], "❌ 未找到需要解析的内容")
            return

        # 提取URL
        url = None

        # 处理XML消息
        if quote.get("MsgType") == 49:
            try:
                url = await self._extract_url_from_xml(quote_content)
            except Exception as e:
                logger.error(f"[VideoParser] 提取URL失败: {e}")
                await bot.send_text_message(message["FromWxid"], "❌ 提取链接失败")
                return
        # 处理普通文本消息
        else:
            url, _ = self._extract_url(quote_content)

        if not url:
            await bot.send_text_message(message["FromWxid"], "❌ 未找到可以解析的链接")
            return

        logger.debug(f"[VideoParser] 开始解析图集,URL: {url}")

        # 修改为使用本地解析API
        try:
            async with httpx.AsyncClient() as client:
                response = await client.get(f"{self.local_api}{url}", timeout=30)
                if response.status_code == 200:
                    result = response.json()
                    logger.debug(f"[VideoParser] 图集API返回: {result}")
                    if result.get("code") == 200:
                        data = result.get("data", {})
                        if data.get("images") or data.get("image_urls"):
                            images = data.get("images", []) or data.get("image_urls", [])
                            if images:
                                # 创建临时目录
                                temp_dir = Path("temp/tuji_video")
                                temp_dir.mkdir(parents=True, exist_ok=True)
                                timestamp = int(time.time())

                                # 临时文件路径列表
                                temp_files = []
                                downloaded_images = []
                                video_path = f"temp/video/slideshow_{timestamp}.mp4"
                                temp_files.append(video_path)

                                # 下载所有图片
                                async with httpx.AsyncClient() as client:
                                    for i, img_url in enumerate(images):
                                        try:
                                            response = await client.get(img_url, timeout=60)
                                            if response.status_code == 200:
                                                img_path = f"temp/tuji_video/img_{timestamp}_{i}.jpg"
                                                temp_files.append(img_path)
                                                with open(img_path, "wb") as f:
                                                    f.write(response.content)
                                                downloaded_images.append(img_path)
                                        except Exception as e:
                                            logger.error(f"[VideoParser] 下载图片失败: {e}")
                                            continue

                                if not downloaded_images:
                                    await bot.send_text_message(message["FromWxid"], "❌ 图片下载失败")
                                    return

                                # 获取音乐
                                music_info = await self._get_random_music()
                                music_url = music_info.get("url") if music_info else None

                                # 创建视频
                                success = await create_slideshow(downloaded_images, video_path, music_url=music_url)
                                if not success:
                                    await bot.send_text_message(message["FromWxid"], "❌ 视频创建失败")
                                    return

                                # 发送视频
                                with open(video_path, "rb") as f:
                                    video_data = f.read()

                                    # 添加以下代码获取封面图片
                                    cover_data = None
                                    if downloaded_images:
                                        try:
                                            with open(downloaded_images[0], "rb") as cover_file:
                                                cover_data = cover_file.read()
                                                cover_base64 = base64.b64encode(cover_data).decode()
                                        except Exception as e:
                                            logger.error(f"[VideoParser] 读取封面图片失败: {e}")
                                            cover_base64 = None

                                    # 修改发送视频的调用，添加封面参数
                                    await bot.send_video_message(
                                        message["FromWxid"],
                                        video_data,
                                        cover_base64  # 使用第一张图片作为封面
                                    )
                                    await bot.send_text_message(message["FromWxid"], f"✅ 图集视频生成成功 ({len(images)}张图片)")

                                # 设置延迟清理文件 - 使用统一管理器
                                for file_path in temp_files:
                                    cleanup_file(file_path, delay_seconds=300)  # 5分钟后清理
                                return

                    logger.error(f"[VideoParser] 图集API返回异常或无图集数据: {result}")

            await bot.send_text_message(message["FromWxid"], "❌ 图集解析失败，请稍后重试")
        except Exception as e:
            logger.error(f"[VideoParser] 图集解析失败: {e}")
            logger.error(f"错误详情: {traceback.format_exc()}")
            await bot.send_text_message(message["FromWxid"], "❌ 图集解析服务异常，请稍后重试")

    async def _handle_video_number(self, bot: WechatAPIClient, message: dict, quote: dict) -> None:
        """处理视频号解析命令"""
        try:
            xml_content = quote.get("Content", "")
            root = ET.fromstring(xml_content)
            finder_feed = root.find(".//finderFeed")
            if finder_feed is not None:
                object_id = finder_feed.find("objectId")
                object_nonce_id = finder_feed.find("objectNonceId")

                if object_id is not None and object_nonce_id is not None:
                    object_id = object_id.text
                    object_nonce_id = object_nonce_id.text
                    logger.debug(f"[VideoParser] 从XML解析到视频号信息: objectId={object_id} objectNonceId={object_nonce_id}")

                    # 发送处理中的提示
                    await bot.send_text_message(message["FromWxid"], "🔄 正在解析视频号内容，请稍候...")

                    video_info = await self._parse_video_number(object_id, object_nonce_id)
                    if video_info:
                        await self._send_video_card(bot, message["FromWxid"], video_info)
                        return
                    else:
                        logger.error("[VideoParser] 视频号解析失败")
                else:
                    logger.error("[VideoParser] 未找到视频号必要信息")
            else:
                logger.error("[VideoParser] 未找到finderFeed节点")

        except Exception as e:
            logger.error(f"[VideoParser] 解析视频号消息失败: {e}")
            logger.error(f"错误详情: {traceback.format_exc()}")
        await bot.send_text_message(message["FromWxid"], "❌ 视频号解析失败，请稍后重试")

    async def _handle_text_to_image(self, bot: WechatAPIClient, message: dict, quote: dict) -> None:
        """处理文本转图片命令"""
        quote_content = quote.get("Content", "")
        if not quote_content:
            await bot.send_text_message(message["FromWxid"], "❌ 未找到需要转换的文本内容")
            return

        # 如果引用的内容是字典类型，尝试获取其中的文本内容
        if isinstance(quote_content, dict):
            quote_content = quote_content.get("string", "")

        if not quote_content.strip():
            await bot.send_text_message(message["FromWxid"], "❌ 文本内容为空")
            return

        logger.debug(f"[VideoParser] 开始处理文本转图片: {quote_content[:100]}...")  # 只记录前100个字符

        # 生成临时文件路径(使用毫秒级时间戳避免冲突)
        timestamp = int(time.time() * 1000)
        temp_dir = Path("temp")
        temp_dir.mkdir(parents=True, exist_ok=True)
        final_image_path = str(temp_dir / f"{timestamp}.png")

        logger.debug(f"[VideoParser] 开始生成文本卡片图片")

        try:
            # 创建TextCardService的实例
            text_card_service = TextCardService()

            # 在实例上调用generate_card_with_header方法，并传递header_image_url参数
            success = await text_card_service.generate_card_with_header(
                text=quote_content,  # 注意这里是text参数，不是quote_content
                header_image_url="https://api.317ak.com/API/tp/dmdntp.php",
                output_path=final_image_path,
                enhance_header=True,  # 使用增强选项
                width=800  # 保留宽度参数
            )

            if not success:
                # 如果增强模式失败，尝试不使用增强模式
                logger.warning("[VideoParser] 增强模式生成失败，尝试普通模式")
                success = await text_card_service.generate_card_with_header(
                    text=quote_content,  # 同样使用text参数
                    header_image_url="https://api.317ak.com/API/tp/dmdntp.php",
                    output_path=final_image_path,
                    enhance_header=False,
                    width=800
                )

                if not success:
                    # 如果仍然失败，尝试生成简单的文本卡片
                    logger.warning("[VideoParser] 带头部图片生成失败，尝试生成简单文本卡片")
                    success = await text_card_service.generate_card(
                        text=quote_content,  # 使用text参数
                        output_path=final_image_path,
                        width=800
                    )

            if not success:
                await bot.send_text_message(message["FromWxid"], "❌ 生成图片失败，请稍后重试")
                logger.error("[VideoParser] 文本转图片失败")
                return

            # 检查图片文件是否存在
            if not os.path.exists(final_image_path):
                await bot.send_text_message(message["FromWxid"], "❌ 图片文件生成失败")
                logger.error(f"[VideoParser] 生成的图片文件不存在: {final_image_path}")
                return

            # 发送图片
            try:
                logger.debug(f"[VideoParser] 开始发送图片: {final_image_path}")
                # 读取图片数据并发送
                with open(final_image_path, "rb") as f:
                    image_data = f.read()
                    image_base64 = base64.b64encode(image_data).decode()
                    await bot.send_image_message(message["FromWxid"], image_base64)
                logger.info("[VideoParser] 图片发送成功")
            except Exception as e:
                logger.error(f"[VideoParser] 发送图片失败: {e}")
                await bot.send_text_message(message["FromWxid"], f"❌ 发送图片失败: {str(e)}")

            # 设置延迟清理图片文件 - 使用统一管理器
            cleanup_file(final_image_path, delay_seconds=10)

        except Exception as e:
            logger.error(f"[VideoParser] 图片处理失败: {e}")
            logger.error(f"[VideoParser] 错误详情: {traceback.format_exc()}")
            await bot.send_text_message(message["FromWxid"], f"❌ 图片处理失败: {str(e)}")

            # 清理文件 - 使用统一管理器
            cleanup_file(final_image_path)

    async def _handle_text_to_voice(self, bot: WechatAPIClient, message: dict, quote: dict, voice_id: str) -> None:
        """处理文本转语音命令"""
        quote_content = quote.get("Content", "")
        if not quote_content:
            await bot.send_text_message(message["FromWxid"], "❌ 未找到需要转换的文本内容")
            return

        # 如果引用的内容是字典类型，尝试获取其中的文本内容
        if isinstance(quote_content, dict):
            quote_content = quote_content.get("string", "")

        if not quote_content.strip():
            await bot.send_text_message(message["FromWxid"], "❌ 文本内容为空")
            return

        # 添加文本长度限制
        text = quote_content.strip()
        if len(text) > 300:  # 限制300字
            await bot.send_text_message(
                message["FromWxid"],
                "❌ 文本内容过长\n"
                "💡 提示：请将文本控制在300字以内"
            )
            return

        logger.debug(f"[VideoParser] 开始处理文本转语音: 文本={text[:100]}..., 语音ID={voice_id}")

        try:
            # 调用API转换语音
            async with httpx.AsyncClient() as client:
                params = {
                    "text": text,
                    "voice": voice_id
                }

                response = await client.get(self.voice_api, params=params, timeout=30)
                if response.status_code == 200:
                    # 直接获取语音数据并发送
                    voice_data = response.content
                    await bot.send_voice_message(
                        message["FromWxid"],
                        voice_data,  # 直接发送二进制数据
                        'mp3'
                    )
                else:
                    raise Exception(f"API返回状态码: {response.status_code}")

        except Exception as e:
            logger.error(f"[VideoParser] 文本转语音失败: {e}")
            logger.error(f"错误详情: {traceback.format_exc()}")
            await bot.send_text_message(
                message["FromWxid"],
                "❌ 语音转换失败，请稍后重试\n"
                "💡 提示：请确保文本内容在300字以内"
            )

    @on_quote_message
    async def handle_quote(self, bot: WechatAPIClient, message: dict):
        """处理引用消息"""
        if not self.enable:
            return

        content = message.get("Content", "").strip()

        if not content:
            return

        # 检查是否是语音转换命令
        voice_match = self.voice_pattern.match(content)
        if voice_match:
            voice_id = voice_match.group(1)
            try:
                voice_id_int = int(voice_id)
                if not (1 <= voice_id_int <= 539):
                    await bot.send_text_message(
                        message["FromWxid"],
                        "❌ 语音编号无效\n支持的语音编号范围: 1-539"
                    )
                    return
                await self._handle_text_to_voice(bot, message, message.get("Quote", {}), voice_id)
                return
            except ValueError:
                # 如果转换失败，继续处理其他命令
                pass

        # 检查是否是支持的命令
        if content not in self.command_handlers and content not in self.command:
            return

        quote = message.get("Quote", {})
        logger.debug(f"[VideoParser] 处理引用消息命令[{content}]: {quote}")

        # 查找对应的命令处理器
        handler = self.command_handlers.get(content)
        if handler:
            await handler(bot, message, quote)
            return

        # 如果是解析命令,则处理视频解析
        if content in self.command:
            await self._handle_video(bot, message, quote)

    @on_text_message
    async def handle_text(self, bot: WechatAPIClient, message: dict):
        """处理文本消息"""
        if not self.enable:
            return

        content = message.get("Content", "").strip()

        # 检查是否是语音转换命令（转X+文本）
        voice_match = re.match(r'^转(\d+)(.+)$', content)
        if voice_match:
            voice_id = voice_match.group(1)
            text = voice_match.group(2).strip()

            try:
                voice_id_int = int(voice_id)
                if not (1 <= voice_id_int <= 539):
                    await bot.send_text_message(
                        message["FromWxid"],
                        "❌ 语音编号无效\n支持的语音编号范围: 1-539"
                    )
                    return

                # 创建一个模拟的引用消息数据结构
                mock_quote = {
                    "Content": text,
                    "MsgType": 1
                }

                # 调用语音转换处理
                await self._handle_text_to_voice(bot, message, mock_quote, voice_id)

            except ValueError:
                await bot.send_text_message(
                    message["FromWxid"],
                    "❌ 语音编号无效\n支持的语音编号范围: 1-539"
                )
                return

        return

    async def test_download_image(self, bot: WechatAPIClient):
        """测试图片下载功能

        Args:
            bot: WechatAPIClient实例
        """
        try:
            # 图片信息
            aeskey = "6f2c7b12e71af348d2068312c03bba04"
            cdnmidimgurl = "3057020100044b304902010002047fe06faa02032f53a3020412752d70020467b3b55f042434353435356562302d376536342d343939322d383364322d346136386262383031386531020401290a020201000405004c56fb00"

            # 下载图片
            image_data = await bot.download_image(aeskey, cdnmidimgurl)
            if not image_data:
                logger.error("[VideoParser] 图片下载失败")
                return

            # 确保临时目录存在
            self.temp_dir.mkdir(parents=True, exist_ok=True)

            # 保存图片
            image_path = str(self.temp_dir / "test.jpg")
            image_bytes = base64.b64decode(image_data)
            with open(image_path, "wb") as f:
                f.write(image_bytes)

            logger.info(f"[VideoParser] 图片已保存到: {image_path}")

        except Exception as e:
            logger.error(f"[VideoParser] 测试图片下载失败: {e}")
            logger.error(f"错误详情: {traceback.format_exc()}")

    async def _save_image(self, bot: WechatAPIClient, aeskey: str, cdnmidimgurl: str, msgid: str):
        """下载并保存图片

        Args:
            bot: WechatAPIClient 实例
            aeskey: 图片AES密钥
            cdnmidimgurl: 图片CDN URL
            msgid: 消息ID

        Returns:
            str: 保存的图片路径,如果失败返回None
        """
        try:
            # 下载图片
            image_data = await bot.download_image(aeskey, cdnmidimgurl)
            if not image_data:
                return None

            # 确保临时目录存在
            self.temp_dir.mkdir(parents=True, exist_ok=True)

            # 保存图片
            image_path = str(self.temp_dir / f"{msgid}.jpg")
            image_bytes = base64.b64decode(image_data)
            with open(image_path, "wb") as f:
                f.write(image_bytes)

            return image_path

        except:
            return None

    async def _handle_image_to_video(self, bot: WechatAPIClient, message: dict, quote: dict) -> None:
        """处理图集转视频命令"""
        quote_content = quote.get("Content", "")
        if not quote_content:
            await bot.send_text_message(message["FromWxid"], "❌ 未找到需要解析的内容")
            return

        # 提取URL
        url = None

        # 处理XML消息
        if quote.get("MsgType") == 49:
            try:
                url = self._extract_url_from_xml(quote_content)
            except:
                await bot.send_text_message(message["FromWxid"], "❌ 提取链接失败")
                return
        # 处理普通文本消息
        else:
            url, _ = self._extract_url(quote_content)

        if not url:
            await bot.send_text_message(message["FromWxid"], "❌ 未找到可以解析的链接")
            return

        await bot.send_text_message(message["FromWxid"], "🔄 正在处理图集转视频请求，请稍候...")

        # 使用图集解析API
        try:
            async with httpx.AsyncClient() as client:
                response = await client.get(f"{self.tuji_api}{url}", timeout=30)
                if response.status_code == 200:
                    result = response.json()
                    if result.get("code") == 200:
                        data = result.get("data", {})
                        # 检查是否包含图集数据
                        if data.get("images") or data.get("image_urls"):
                            images = data.get("images", []) or data.get("image_urls", [])
                            if images:
                                # 创建临时目录
                                temp_dir = Path("temp/tuji_video")
                                temp_dir.mkdir(parents=True, exist_ok=True)
                                timestamp = int(time.time())

                                # 临时文件路径列表
                                temp_files = []
                                downloaded_images = []
                                video_path = f"temp/video/slideshow_{timestamp}.mp4"
                                temp_files.append(video_path)

                                # 下载所有图片
                                async with httpx.AsyncClient() as client:
                                    for i, img_url in enumerate(images):
                                        try:
                                            response = await client.get(img_url, timeout=60)
                                            if response.status_code == 200:
                                                img_path = f"temp/tuji_video/img_{timestamp}_{i}.jpg"
                                                temp_files.append(img_path)
                                                with open(img_path, "wb") as f:
                                                    f.write(response.content)
                                                downloaded_images.append(img_path)
                                        except:
                                            continue

                                if not downloaded_images:
                                    await bot.send_text_message(message["FromWxid"], "❌ 图片下载失败")
                                    return

                                music_info = await self._get_random_music()
                                music_url = music_info.get("url") if music_info else None
                                success = await create_slideshow(downloaded_images, video_path, music_url=music_url)
                                if not success:
                                    await bot.send_text_message(message["FromWxid"], "❌ 视频创建失败")
                                    return
                                with open(video_path, "rb") as f:
                                    video_data = f.read()
                                    cover_base64 = None
                                    if downloaded_images:
                                        try: cover_base64 = base64.b64encode(open(downloaded_images[0], "rb").read()).decode()
                                        except: cover_base64 = None
                                    await bot.send_video_message(message["FromWxid"], video_data, cover_base64)
                                    await bot.send_text_message(message["FromWxid"], f"✅ 图集视频生成成功 ({len(images)}张图片)")
                                for file_path in temp_files: cleanup_file(file_path, delay_seconds=300)
                                return

            await bot.send_text_message(message["FromWxid"], "❌ 图集解析失败，请稍后重试")
        except httpx.ClientError:
            await bot.send_text_message(message["FromWxid"], "❌ 图集解析服务网络异常，请稍后重试")
        except:
            await bot.send_text_message(message["FromWxid"], "❌ 图集解析服务异常，请稍后重试")

    async def _get_random_music(self):
        try:
            async with httpx.AsyncClient() as client:
                response = await client.get("https://www.hhlqilongzhu.cn/api/wangyi_hot_review.php", timeout=10)
                if response.status_code == 200:
                    data = response.json()
                    if data.get("code") == 200 and data.get("url"):
                        return {"title": data.get("song", "未知歌曲"), "singer": data.get("singer", "未知歌手"), "url": data.get("url"), "cover": data.get("img"), "link": data.get("link")}
            return None
        except: return None