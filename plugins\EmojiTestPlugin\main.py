import asyncio
from loguru import logger
from utils.plugin_base import PluginBase
from utils.decorators import on_quote_message, on_text_message
from WechatAPI import WechatAPIClient
import xml.etree.ElementTree as ET
import json
from pathlib import Path
from datetime import datetime
import html
import os
import time
import hashlib
import random
import aiofiles
import aiofiles.os

class EmojiTestPlugin(PluginBase):
    """表情包复读与命名插件"""
    
    description = "用于复读表情包，支持给表情包命名并通过名字调用"
    author = "XYBot"
    version = "1.1.0"
    
    def __init__(self):
        super().__init__()
        self.enable = True
        self.command = ["复读", "表情"]  # 添加"表情"命令
        # 表情映射文件路径
        self.emoji_map_file = Path("data/emoji_map/emoji_map.json")
        # 初始化表情映射为空字典，稍后异步加载
        self.emoji_size_map = {}

        # 异步初始化标志
        self._initialized = False
        
    async def load_emoji_map(self) -> dict:
        """异步加载表情映射文件"""
        try:
            if self.emoji_map_file.exists():
                async with aiofiles.open(self.emoji_map_file, 'r', encoding='utf-8') as f:
                    content = await f.read()
                    data = json.loads(content)
                logger.info(f"成功加载表情映射文件，共 {len(data)} 条记录")
                return data
            return {}
        except Exception as e:
            logger.error(f"加载表情映射文件失败: {e}")
            return {}
        
    async def save_emoji_map(self):
        """异步保存表情映射到文件"""
        try:
            # 使用 asyncio.to_thread 来异步执行目录创建
            await asyncio.to_thread(self.emoji_map_file.parent.mkdir, parents=True, exist_ok=True)

            # 异步写入文件
            content = json.dumps(self.emoji_size_map, indent=2, ensure_ascii=False)
            async with aiofiles.open(self.emoji_map_file, 'w', encoding='utf-8') as f:
                await f.write(content)
            logger.info(f"成功保存表情映射文件，共 {len(self.emoji_size_map)} 条记录")
        except Exception as e:
            logger.error(f"保存表情映射文件失败: {e}")
            
    async def ensure_initialized(self):
        """确保插件已异步初始化"""
        if not self._initialized:
            self.emoji_size_map = await self.load_emoji_map()
            self._initialized = True

    async def clean_emoji_map(self):
        """异步清理表情映射"""
        now = datetime.now()
        cleaned = 0
        for md5, info in list(self.emoji_size_map.items()):
            # 如果表情有名字，则不清理
            if info.get('name'):
                continue

            # 检查最后使用时间
            last_used = info.get('last_used')
            if not last_used:
                del self.emoji_size_map[md5]
                cleaned += 1
                continue

            try:
                last_used_time = datetime.fromisoformat(last_used)
                # 超过7天未使用的表情（未命名）将被清理
                if (now - last_used_time).days > 7:
                    del self.emoji_size_map[md5]
                    cleaned += 1
            except Exception as e:
                logger.error(f"解析最后使用时间失败: {e}")

        if cleaned > 0:
            await self.save_emoji_map()
            logger.info(f"清理了 {cleaned} 个未命名的表情记录")

    @on_text_message
    async def handle_message(self, bot: WechatAPIClient, message: dict):
        """处理普通消息"""
        if not self.enable:
            return

        # 确保插件已初始化
        await self.ensure_initialized()

        content = message.get("Content", {})
        if isinstance(content, dict):
            content = content.get("string", "")
            
        if not content:
            return
            
        content = content.strip()
        
        # 处理查看我的表情命令
        if content == "我的表情":
            await self._handle_list_emojis(bot, message)
            return
            
        # 处理删除命名表情的命令
        if content.startswith("删除表情 "):
            emoji_name = content[5:].strip()
            await self._handle_emoji_delete(bot, message, emoji_name)
            return
            
        # 检查消息内容
        words = content.split()
        if not words:
            return
            
        times = 1
        emoji_name = content
        
        # 检查是否以数字结尾（用于指定次数）
        if words[-1].isdigit():
            times = min(int(words[-1]), 5)
            emoji_name = ' '.join(words[:-1]).strip()  # 前面部分作为表情名
        
        # 检查是否完全匹配已命名的表情
        emoji_name_lower = emoji_name.lower()
        for md5, info in self.emoji_size_map.items():
            # 检查 names 数组
            if 'names' in info:
                names = info['names']
                for name in names:
                    if (name == emoji_name or  # 完全匹配
                        (name.isascii() and name.lower() == emoji_name_lower)):  # 英文不区分大小写
                        await self._handle_emoji_by_name(bot, message, name, times)
                        return
            # 兼容旧数据的 name 字段
            elif 'name' in info:
                stored_name = info['name']
                if stored_name and (
                    stored_name == emoji_name or  # 完全匹配
                    (stored_name.isascii() and stored_name.lower() == emoji_name_lower)  # 英文不区分大小写
                ):
                    await self._handle_emoji_by_name(bot, message, stored_name, times)
                    return
        
        # 如果没有匹配到，直接返回，不提示
        return

    @on_quote_message
    async def handle_quote(self, bot: WechatAPIClient, message: dict):
        """处理引用消息"""
        if not self.enable:
            return

        # 确保插件已初始化
        await self.ensure_initialized()

        # 获取消息内容
        content = message.get("Content", {})
        if isinstance(content, dict):
            content = content.get("string", "")
            
        if not content:
            return
            
        content = content.strip()  # 去除前后空格
        
        # 处理表情命名命令 - 使用完全匹配
        if content.startswith("命名 "):
            emoji_name = content[3:].strip()  # 提取"命名 "后面的内容
            if emoji_name:  # 确保名字不为空
                await self._handle_emoji_naming(bot, message, emoji_name)
            return
            
        # 检查消息内容
        words = content.split()
        if not words:
            return
            
        times = 1
        emoji_name = content
        
        # 检查是否以数字结尾（用于指定次数）
        if words[-1].isdigit():
            times = min(int(words[-1]), 5)
            emoji_name = ' '.join(words[:-1]).strip()  # 前面部分作为表情名
            
        # 检查是否存在这个表情名
        emoji_exists = False
        for _, info in self.emoji_size_map.items():
            # 检查 names 数组
            if 'names' in info:
                names = info['names']
                for name in names:
                    if name == emoji_name or (name.isascii() and name.lower() == emoji_name.lower()):
                        emoji_exists = True
                        break
            # 兼容旧数据的 name 字段
            elif 'name' in info and info['name'] == emoji_name:
                emoji_exists = True
                break
                
        # 只有当表情名存在时才尝试发送
        if emoji_exists:
            await self._handle_emoji_by_name(bot, message, emoji_name, times)
            return
            
        # 如果不是表情名相关的命令，则按原有的复读逻辑处理
        if emoji_name == "复读":
            # 原有的复读逻辑（引用表情直接复读）
            # 获取复读次数
            times = 1
            if len(words) > 0 and words[-1].isdigit() and words[-1] != "复读":
                times = min(int(words[-1]), 5)  # 最多发送5次
            
            # 优先使用 Quote 中的信息
            quoted_msg = message.get("Quote", {})
            if quoted_msg and quoted_msg.get("MsgType") == 47:  # 表情消息
                emoji_md5 = None
                
                # 1. 直接从 Quote 中获取
                emoji_md5 = quoted_msg.get("EmojiMd5")
                
                # 2. 如果没有，尝试从 content 中解析 XML
                if not emoji_md5 and quoted_msg.get("Content"):
                    try:
                        content_text = quoted_msg.get("Content")
                        # 先处理 HTML 实体编码
                        if "&lt;" in content_text or "&gt;" in content_text:
                            content_text = html.unescape(content_text)
                        
                        # 检查是否是 wxid:timestamp:0:md5::0 格式
                        if ":" in content_text:
                            parts = content_text.strip().split(":")
                            if len(parts) >= 4:
                                potential_md5 = parts[3]
                                if len(potential_md5) == 32 and all(c in '0123456789abcdef' for c in potential_md5.lower()):
                                    emoji_md5 = potential_md5
                        
                        # 如果上面的格式不匹配，尝试解析 XML 格式
                        if not emoji_md5 and "<emoji" in content_text:
                            msg_start = content_text.find("<msg>")
                            msg_end = content_text.find("</msg>")
                            if msg_start >= 0 and msg_end >= 0:
                                msg_content = content_text[msg_start:msg_end + 6]
                                root = ET.fromstring(msg_content)
                                emoji_node = root.find(".//emoji")
                                if emoji_node is not None:
                                    emoji_md5 = emoji_node.get("md5")
                                    emoji_len = emoji_node.get("len")
                                    if emoji_md5 and emoji_len:
                                        try:
                                            emoji_size = int(emoji_len)
                                            self.emoji_size_map[emoji_md5] = {
                                                'size': emoji_size,
                                                'last_used': datetime.now().isoformat()
                                            }
                                            # 异步保存，但不等待完成以避免阻塞
                                            asyncio.create_task(self.save_emoji_map())
                                        except ValueError:
                                            logger.error(f"表情大小转换失败: {emoji_len}")
                    except Exception as e:
                        logger.error(f"解析引用消息内容失败: {e}")
                
                if emoji_md5:
                    # 获取目标ID
                    room_id = message.get("FromWxid") if "@chatroom" in message.get("FromWxid", "") else None
                    from_id = message.get("SenderWxid")
                    target_id = room_id or from_id
                    
                    if target_id:
                        await self._send_emoji(bot, target_id, emoji_md5, times)

    async def _handle_emoji_naming(self, bot: WechatAPIClient, message: dict, emoji_name: str):
        """处理表情命名"""
        quoted_msg = message.get("Quote", {})
        if not quoted_msg or quoted_msg.get("MsgType") != 47:
            await bot.send_text_message(message.get("FromWxid"), "请引用一个表情包进行命名")
            return
            
        emoji_md5 = await self._get_emoji_md5(quoted_msg)
        if not emoji_md5:
            await bot.send_text_message(message.get("FromWxid"), "无法获取表情信息")
            return
            
        # 获取表情大小信息
        emoji_size = None
        try:
            content_text = quoted_msg.get("Content", "")
            if "<emoji" in content_text:
                root = ET.fromstring(content_text)
                emoji_node = root.find(".//emoji")
                if emoji_node is not None:
                    emoji_len = emoji_node.get("len")
                    if emoji_len:
                        emoji_size = int(emoji_len)
        except Exception as e:
            logger.error(f"解析表情大小失败: {e}")
            
        # 更新表情映射，确保包含 size 字段
        if emoji_md5 not in self.emoji_size_map:
            self.emoji_size_map[emoji_md5] = {}
            
        info = self.emoji_size_map[emoji_md5]
        # 将现有的单个名字转换为名字列表
        if 'name' in info:
            info['names'] = [info.pop('name')]
        elif 'names' not in info:
            info['names'] = []
            
        # 检查名字是否已存在
        if emoji_name not in info['names']:
            info['names'].append(emoji_name)
            
        info.update({
            'size': emoji_size or 9992069,
            'last_used': datetime.now().isoformat()
        })

        await self.save_emoji_map()
        await bot.send_text_message(
            message.get("FromWxid"),
            f"表情已添加触发词：{emoji_name}"
        )

    async def _handle_emoji_by_name(self, bot: WechatAPIClient, message: dict, emoji_name: str, times: int = 1):
        """通过名字发送表情"""
        # 对于英文名称，转换为小写进行比较
        emoji_name_lower = emoji_name.lower()
        
        for md5, info in self.emoji_size_map.items():
            # 检查 names 数组
            if 'names' in info:
                for name in info['names']:
                    if (name == emoji_name or  # 完全匹配
                        (name.isascii() and name.lower() == emoji_name_lower)):  # 英文不区分大小写
                        room_id = message.get("FromWxid") if "@chatroom" in message.get("FromWxid", "") else None
                        from_id = message.get("SenderWxid")
                        target_id = room_id or from_id
                        
                        if target_id:
                            await self._send_emoji(bot, target_id, md5, times)
                        return
            # 兼容旧数据的 name 字段
            elif 'name' in info:
                stored_name = info['name']
                if stored_name and (
                    stored_name == emoji_name or  # 完全匹配
                    (stored_name.isascii() and stored_name.lower() == emoji_name_lower)  # 英文不区分大小写
                ):
                    room_id = message.get("FromWxid") if "@chatroom" in message.get("FromWxid", "") else None
                    from_id = message.get("SenderWxid")
                    target_id = room_id or from_id
                    
                    if target_id:
                        await self._send_emoji(bot, target_id, md5, times)
                    return

    async def _get_emoji_md5(self, quoted_msg: dict) -> str:
        """从引用消息中获取表情MD5"""
        emoji_md5 = quoted_msg.get("EmojiMd5")
        
        if not emoji_md5 and quoted_msg.get("Content"):
            try:
                content_text = quoted_msg.get("Content")
                # 处理 HTML 实体编码
                if "&lt;" in content_text or "&gt;" in content_text:
                    content_text = html.unescape(content_text)
                
                # 检查 wxid:timestamp:0:md5::0 格式
                if ":" in content_text:
                    parts = content_text.strip().split(":")
                    if len(parts) >= 4:
                        potential_md5 = parts[3]
                        if len(potential_md5) == 32 and all(c in '0123456789abcdef' for c in potential_md5.lower()):
                            emoji_md5 = potential_md5
                
                # 解析 XML 格式
                if not emoji_md5 and "<emoji" in content_text:
                    msg_start = content_text.find("<msg>")
                    msg_end = content_text.find("</msg>")
                    if msg_start >= 0 and msg_end >= 0:
                        msg_content = content_text[msg_start:msg_end + 6]
                        root = ET.fromstring(msg_content)
                        emoji_node = root.find(".//emoji")
                        if emoji_node is not None:
                            emoji_md5 = emoji_node.get("md5")
                            emoji_len = emoji_node.get("len")
                            if emoji_md5 and emoji_len:
                                try:
                                    emoji_size = int(emoji_len)
                                    self.emoji_size_map[emoji_md5] = {
                                        'size': emoji_size,
                                        'last_used': datetime.now().isoformat()
                                    }
                                    # 异步保存，但不等待完成以避免阻塞
                                    asyncio.create_task(self.save_emoji_map())
                                except ValueError:
                                    logger.error(f"表情大小转换失败: {emoji_len}")
            except Exception as e:
                logger.error(f"解析引用消息内容失败: {e}")
                
        return emoji_md5

    async def _send_emoji(self, bot: WechatAPIClient, target_id: str, emoji_md5: str, times: int = 1):
        """发送表情包"""
        emoji_info = self.emoji_size_map.get(emoji_md5, {})
        emoji_size = emoji_info.get('size', 9992069)  # 使用 get 方法，提供默认值
        
        for i in range(times):
            try:
                await bot.send_emoji_message(target_id, emoji_md5, emoji_size)
                if i < times - 1:
                    await asyncio.sleep(1)  # 间隔1秒发送
            except Exception as e:
                logger.error(f"表情发送失败: {e}")
                break

    async def _handle_emoji_delete(self, bot: WechatAPIClient, message: dict, emoji_name: str):
        """处理删除表情命名"""
        deleted = False
        # 对于英文名称，转换为小写进行比较
        emoji_name_lower = emoji_name.lower()
        
        for md5, info in list(self.emoji_size_map.items()):
            if 'names' in info:
                names = info['names']
                # 查找要删除的名字
                for i, name in enumerate(names):
                    if (name == emoji_name or 
                        (name.isascii() and name.lower() == emoji_name_lower)):
                        names.pop(i)
                        deleted = True
                        break
                if deleted:
                    if not names:  # 如果没有其他名字了，删除names字段
                        info.pop('names')
                    break
            elif 'name' in info:  # 兼容旧数据
                name = info['name']
                if (name == emoji_name or 
                    (name.isascii() and name.lower() == emoji_name_lower)):
                    info.pop('name')
                    deleted = True
                    break
                    
        if deleted:
            await self.save_emoji_map()
            await bot.send_text_message(
                message.get("FromWxid"),
                f"已删除表情触发词：{emoji_name}"
            )
        else:
            await bot.send_text_message(
                message.get("FromWxid"),
                f"未找到触发词：{emoji_name}"
            )

    async def _handle_list_emojis(self, bot: WechatAPIClient, message: dict):
        """处理查看已命名表情列表"""
        # 收集所有已命名的表情
        emoji_dict = {}  # 使用字典存储表情及其触发词
        for emoji_md5, info in self.emoji_size_map.items():
            if 'names' in info:
                names = info['names']
                if names:
                    emoji_dict[emoji_md5] = names
            elif 'name' in info:  # 兼容旧数据
                name = info['name']
                if name:
                    emoji_dict[emoji_md5] = [name]

        if not emoji_dict:
            await bot.send_text_message(
                message.get("FromWxid"),
                "还没有命名过任何表情"
            )
            return

        # 生成聊天记录格式的表情列表
        try:
            # 按触发词排序
            sorted_items = sorted(emoji_dict.items(), key=lambda x: x[1][0].lower())

            # 构建聊天记录数据
            chat_records = []
            chat_records.append({
                'username': '系统',
                'message': '我的表情列表',
                'index': 0
            })

            # 将所有表情合并到一条消息中
            emoji_lines = []
            for emoji_md5, names in sorted_items:
                # 对于每个表情，显示其所有触发词
                trigger_words = " | ".join(names)
                emoji_lines.append(f"• {trigger_words}")

            # 合并所有表情到一条消息
            all_emojis_message = "\n".join(emoji_lines)
            chat_records.append({
                'username': '表情包',
                'message': all_emojis_message,
                'index': 1
            })

            # 生成聊天记录XML
            record_xml = await self._generate_emoji_record_xml(chat_records)

            # 生成描述
            description = self._generate_emoji_description(chat_records)

            # 发送聊天记录消息
            await self._send_emoji_chat_record(bot, message.get("FromWxid"), record_xml, description)

        except Exception as e:
            logger.error(f"生成表情列表聊天记录失败: {e}")
            # 如果生成聊天记录失败，退回到文本模式
            text_lines = ["我的表情列表"]
            text_lines.append("=" * 30)  # 分隔线

            # 按触发词排序
            sorted_items = sorted(emoji_dict.items(), key=lambda x: x[1][0].lower())

            for emoji_md5, names in sorted_items:
                # 对于每个表情，显示其所有触发词
                trigger_words = " | ".join(names)
                text_lines.append(f"• {trigger_words}")

            text = "\n".join(text_lines)
            await bot.send_text_message(
                message.get("FromWxid"),
                text
            )

    def _generate_emoji_description(self, chat_records: list) -> str:
        """生成表情列表聊天记录描述"""
        desc_lines = []
        for record in chat_records:
            desc_lines.append(f"{record['username']}: {record['message']}")
        return '\n'.join(desc_lines)

    async def _generate_emoji_record_xml(self, chat_records: list) -> str:
        """生成表情列表聊天记录的XML内容"""
        # 生成描述（HTML编码）
        desc_parts = []
        for record in chat_records:
            desc_parts.append(f"{record['username']}:&#x20;{record['message']}")
        desc = '&#x0A;'.join(desc_parts)

        # 为每个用户设置头像
        user_avatars = {
            '系统': 'https://mmbiz.qpic.cn/mmbiz_png/4whpV1VZl2iccsvYbHvnphkyGtnvjfUS6uBGv4jmG4gh2v7RqgrscwUBmXVrEXqZib5bLdwdJEJcyDjamicZSibOdQ/0?wx_fmt=png',
            '表情包': 'https://mmbiz.qpic.cn/mmbiz_png/4whpV1VZl2iccsvYbHvnphkyGtnvjfUS6uBGv4jmG4gh2v7RqgrscwUBmXVrEXqZib5bLdwdJEJcyDjamicZSibOdQ/0?wx_fmt=png'
        }

        # 生成数据项
        data_items = []
        current_time = int(time.time())

        for record in chat_records:
            # 生成随机ID
            data_id = hashlib.md5(f"{record['username']}{record['message']}{record['index']}".encode()).hexdigest()
            source_id = str(random.randint(1000000000000000000, 9999999999999999999))

            # 使用该用户的头像
            user_avatar = user_avatars.get(record['username'], user_avatars['表情包'])

            # 为每个用户生成64位长度的hashusername
            user_hash = hashlib.sha256(f"{record['username']}_unique_user_identifier".encode()).hexdigest()

            # 简单的时间设置
            time_offset = record['index'] * 60  # 每条消息间隔1分钟
            message_time = current_time + time_offset

            data_item = f'''<dataitem datatype="1" dataid="{data_id}" datasourceid="{source_id}">
<datadesc>{record['message']}</datadesc>
<sourcename>{record['username']}</sourcename>
<sourceheadurl>{user_avatar}</sourceheadurl>
<sourcetime>2025-06-29&#x20;12:{record['index']:02d}:00</sourcetime>
<srcMsgCreateTime>{message_time}</srcMsgCreateTime>
<fromnewmsgid>{source_id}</fromnewmsgid>
<dataitemsource><hashusername>{user_hash}</hashusername></dataitemsource>
</dataitem>'''
            data_items.append(data_item)

        # 组装完整的recordinfo XML
        record_xml = f'''<recordinfo>
<title>我的表情列表</title>
<desc>{desc}</desc>
<datalist count="{len(chat_records)}">
{''.join(data_items)}
</datalist>
<favcreatetime>{int(time.time() * 1000)}</favcreatetime>
</recordinfo>'''

        return record_xml

    async def _send_emoji_chat_record(self, bot: WechatAPIClient, wxid: str, record_xml: str, description: str):
        """发送表情列表聊天记录消息"""
        try:
            # 聊天记录消息XML格式 (Type 19)
            xml = f'''<appmsg appid="" sdkver="0">
<title>我的表情列表</title>
<des>{description}</des>
<action>view</action>
<type>19</type>
<showtype>0</showtype>
<content/>
<url>https://support.weixin.qq.com/cgi-bin/mmsupport-bin/readtemplate?t=page/favorite_record__w_unsupport&amp;from=singlemessage&amp;isappinstalled=0</url>
<lowurl/>
<dataurl/>
<lowdataurl/>
<recorditem><![CDATA[{record_xml}]]></recorditem>
<appattach>
    <totallen>0</totallen>
    <attachid/>
    <emoticonmd5/>
    <fileext/>
    <aeskey/>
</appattach>
<extinfo/>
<sourceusername/>
<sourcedisplayname/>
<thumburl/>
<md5/>
</appmsg>
<fromusername>{bot.wxid}</fromusername>
<scene>0</scene>
<appinfo>
    <version>1</version>
    <appname></appname>
</appinfo>
<commenturl/>'''

            # 发送聊天记录消息
            client_msg_id, _, new_msg_id = await bot.send_app_message(
                wxid,
                xml,
                19  # 聊天记录消息类型
            )
            logger.info(f"成功发送表情列表聊天记录到: {wxid}, ClientMsgId: {client_msg_id}, NewMsgId: {new_msg_id}")

        except Exception as e:
            logger.error(f"发送表情列表聊天记录失败: {e}")
            raise
