[ImageToImage]
enable = true
command = ["测试上传", "图生图测试", "upload"]
command-format = """
🧪 图生图测试功能

📝 使用方法：
• 测试上传 - 使用默认提示词测试
• 测试上传 换成古装 - 使用自定义提示词

📁 测试图片位置: C:\\DBE25C6475AF6852691B040206E94167.jpg

⚙️ 测试步骤：
1. 获取上传Token
2. 申请上传地址
3. 上传图片文件
4. 确认上传
5. 提交AI任务
6. 等待处理结果

每一步都会显示详细状态信息，方便调试。
"""

[ImageToImage.quote]
command = ["即梦"]
command-format = "引用图片并发送: 即梦+提示词"

[ImageToImage.api]
base_url = "https://dreamina-app-lq.jianying.com"
upload_url = "https://imagex.bytedanceapi.com"

[ImageToImage.rate_limit]
cooldown = 10

# 自然化响应设置
natural_response = true  # 启用自然化响应，避免机械化回复
