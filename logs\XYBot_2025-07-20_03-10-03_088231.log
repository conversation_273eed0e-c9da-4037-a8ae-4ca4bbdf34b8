2025-07-20 03:10:04 | SUCCESS | 读取主设置成功
2025-07-20 03:10:04 | DEBUG | 最终使用的 Redis 主机地址: 127.0.0.1
2025-07-20 03:10:04 | SUCCESS | WechatAPI服务已启动
2025-07-20 03:10:04 | INFO | 2025/07/20 03:10:04 GetRedisAddr: 127.0.0.1:6379
2025-07-20 03:10:04 | INFO | 2025/07/20 03:10:04 Redis连接成功 | 地址:127.0.0.1:6379 DB:0
2025-07-20 03:10:04 | INFO | 2025/07/20 03:10:04 Server start at :9000
2025-07-20 03:10:04 | INFO | 2025/07/20 03:10:04 Failed to start server: listen tcp :9000: bind: Only one usage of each socket address (protocol/network address/port) is normally permitted.
2025-07-20 03:10:04 | INFO | 登录账号信息: wxid: wxid_4usgcju5ey9q29  昵称: 瑶瑶  微信号: Today-KFC  手机号: None
2025-07-20 03:10:04 | INFO | 登录设备信息: device_name: <PERSON>'s Pad  device_id: 49d050a7e94d1ecc40790d74b1c4369f
2025-07-20 03:10:04 | SUCCESS | 登录成功
2025-07-20 03:10:04 | INFO | 自动心跳已在运行中
2025-07-20 03:10:04 | INFO | 成功加载表情映射文件，共 515 条记录
2025-07-20 03:10:04 | SUCCESS | 数据库初始化成功
2025-07-20 03:10:04 | SUCCESS | 定时任务已启动
2025-07-20 03:10:04 | ERROR | 加载插件模块 AdminPoint 时发生错误: Traceback (most recent call last):
  File "C:\XYBotV2\utils\plugin_manager.py", line 127, in load_plugins_from_directory
    module = importlib.import_module(f"plugins.{item}.main")
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Program Files\Python311\Lib\importlib\__init__.py", line 126, in import_module
    return _bootstrap._gcd_import(name[level:], package, level)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "<frozen importlib._bootstrap>", line 1204, in _gcd_import
  File "<frozen importlib._bootstrap>", line 1176, in _find_and_load
  File "<frozen importlib._bootstrap>", line 1147, in _find_and_load_unlocked
  File "<frozen importlib._bootstrap>", line 690, in _load_unlocked
  File "<frozen importlib._bootstrap_external>", line 940, in exec_module
  File "<frozen importlib._bootstrap>", line 241, in _call_with_frames_removed
  File "C:\XYBotV2\plugins\AdminPoint\main.py", line 4, in <module>
    from database.XYBotDB import XYBotDB
ModuleNotFoundError: No module named 'database.XYBotDB'

2025-07-20 03:10:04 | ERROR | 加载插件模块 AdminSigninReset 时发生错误: Traceback (most recent call last):
  File "C:\XYBotV2\utils\plugin_manager.py", line 127, in load_plugins_from_directory
    module = importlib.import_module(f"plugins.{item}.main")
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Program Files\Python311\Lib\importlib\__init__.py", line 126, in import_module
    return _bootstrap._gcd_import(name[level:], package, level)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "<frozen importlib._bootstrap>", line 1204, in _gcd_import
  File "<frozen importlib._bootstrap>", line 1176, in _find_and_load
  File "<frozen importlib._bootstrap>", line 1147, in _find_and_load_unlocked
  File "<frozen importlib._bootstrap>", line 690, in _load_unlocked
  File "<frozen importlib._bootstrap_external>", line 940, in exec_module
  File "<frozen importlib._bootstrap>", line 241, in _call_with_frames_removed
  File "C:\XYBotV2\plugins\AdminSigninReset\main.py", line 4, in <module>
    from database.XYBotDB import XYBotDB
ModuleNotFoundError: No module named 'database.XYBotDB'

2025-07-20 03:10:04 | ERROR | 加载插件模块 AdminWhitelist 时发生错误: Traceback (most recent call last):
  File "C:\XYBotV2\utils\plugin_manager.py", line 127, in load_plugins_from_directory
    module = importlib.import_module(f"plugins.{item}.main")
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Program Files\Python311\Lib\importlib\__init__.py", line 126, in import_module
    return _bootstrap._gcd_import(name[level:], package, level)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "<frozen importlib._bootstrap>", line 1204, in _gcd_import
  File "<frozen importlib._bootstrap>", line 1176, in _find_and_load
  File "<frozen importlib._bootstrap>", line 1147, in _find_and_load_unlocked
  File "<frozen importlib._bootstrap>", line 690, in _load_unlocked
  File "<frozen importlib._bootstrap_external>", line 940, in exec_module
  File "<frozen importlib._bootstrap>", line 241, in _call_with_frames_removed
  File "C:\XYBotV2\plugins\AdminWhitelist\main.py", line 4, in <module>
    from database.XYBotDB import XYBotDB
ModuleNotFoundError: No module named 'database.XYBotDB'

2025-07-20 03:10:04 | SUCCESS | 插件 AISignIn 已加载定时任务: {'plugins.AISignIn.main.AISignIn.auto_signin_task'}
2025-07-20 03:10:05 | INFO | 已加载管理员列表: ['wxid_ubbh6q832tcs21', 'admin-wxid']
2025-07-20 03:10:05 | INFO | 播客API初始化成功
2025-07-20 03:10:05 | INFO | 加载配置完成，转换后的数据: {'gh_7057516c9e71': {'name': '唱舞全明星', 'groups': ['27852221909@chatroom', '55878994168@chatroom', '51891329927@chatroom']}, 'gh_d4d21a0e4623': {'name': '两颗桃', 'groups': ['48097389945@chatroom']}, 'gh_410ff2fa6ec9': {'name': 'NGC660 Ai研究院', 'groups': ['47442567074@chatroom']}, 'gh_f78c53c507c0': {'name': '不正常吐槽君', 'groups': ['48097389945@chatroom']}, 'gh_ba3381c847b3': {'name': '唱舞星计划', 'groups': ['51891329927@chatroom']}, 'gh_1a102fb4cad9': {'name': '我在娱乐圈的唯一人脉', 'groups': ['48097389945@chatroom', '27852221909@chatroom']}}
2025-07-20 03:10:05 | INFO | ArticleForwarder插件初始化完成 - 监控配置: {'gh_7057516c9e71': {'name': '唱舞全明星', 'groups': ['27852221909@chatroom', '55878994168@chatroom', '51891329927@chatroom']}, 'gh_d4d21a0e4623': {'name': '两颗桃', 'groups': ['48097389945@chatroom']}, 'gh_410ff2fa6ec9': {'name': 'NGC660 Ai研究院', 'groups': ['47442567074@chatroom']}, 'gh_f78c53c507c0': {'name': '不正常吐槽君', 'groups': ['48097389945@chatroom']}, 'gh_ba3381c847b3': {'name': '唱舞星计划', 'groups': ['51891329927@chatroom']}, 'gh_1a102fb4cad9': {'name': '我在娱乐圈的唯一人脉', 'groups': ['48097389945@chatroom', '27852221909@chatroom']}}
2025-07-20 03:10:05 | ERROR | 加载插件模块 BaiduAgents 时发生错误: Traceback (most recent call last):
  File "C:\XYBotV2\utils\plugin_manager.py", line 127, in load_plugins_from_directory
    module = importlib.import_module(f"plugins.{item}.main")
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Program Files\Python311\Lib\importlib\__init__.py", line 126, in import_module
    return _bootstrap._gcd_import(name[level:], package, level)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "<frozen importlib._bootstrap>", line 1204, in _gcd_import
  File "<frozen importlib._bootstrap>", line 1176, in _find_and_load
  File "<frozen importlib._bootstrap>", line 1147, in _find_and_load_unlocked
  File "<frozen importlib._bootstrap>", line 690, in _load_unlocked
  File "<frozen importlib._bootstrap_external>", line 940, in exec_module
  File "<frozen importlib._bootstrap>", line 241, in _call_with_frames_removed
  File "C:\XYBotV2\plugins\BaiduAgents\main.py", line 24, in <module>
    from utils.temp_file_manager import cleanup_file
ModuleNotFoundError: No module named 'utils.temp_file_manager'

2025-07-20 03:10:05 | ERROR | 加载插件模块 BotStatus 时发生错误: Traceback (most recent call last):
  File "C:\XYBotV2\utils\plugin_manager.py", line 127, in load_plugins_from_directory
    module = importlib.import_module(f"plugins.{item}.main")
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Program Files\Python311\Lib\importlib\__init__.py", line 126, in import_module
    return _bootstrap._gcd_import(name[level:], package, level)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "<frozen importlib._bootstrap>", line 1204, in _gcd_import
  File "<frozen importlib._bootstrap>", line 1176, in _find_and_load
  File "<frozen importlib._bootstrap>", line 1147, in _find_and_load_unlocked
  File "<frozen importlib._bootstrap>", line 690, in _load_unlocked
  File "<frozen importlib._bootstrap_external>", line 940, in exec_module
  File "<frozen importlib._bootstrap>", line 241, in _call_with_frames_removed
  File "C:\XYBotV2\plugins\BotStatus\main.py", line 7, in <module>
    from database.keyvalDB import KeyvalDB
ModuleNotFoundError: No module named 'database.keyvalDB'

2025-07-20 03:10:05 | ERROR | 加载插件模块 ChatSummary 时发生错误: Traceback (most recent call last):
  File "C:\XYBotV2\utils\plugin_manager.py", line 127, in load_plugins_from_directory
    module = importlib.import_module(f"plugins.{item}.main")
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Program Files\Python311\Lib\importlib\__init__.py", line 126, in import_module
    return _bootstrap._gcd_import(name[level:], package, level)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "<frozen importlib._bootstrap>", line 1204, in _gcd_import
  File "<frozen importlib._bootstrap>", line 1176, in _find_and_load
  File "<frozen importlib._bootstrap>", line 1147, in _find_and_load_unlocked
  File "<frozen importlib._bootstrap>", line 690, in _load_unlocked
  File "<frozen importlib._bootstrap_external>", line 940, in exec_module
  File "<frozen importlib._bootstrap>", line 241, in _call_with_frames_removed
  File "C:\XYBotV2\plugins\ChatSummary\main.py", line 18, in <module>
    from utils.decorators import (
ImportError: cannot import name 'on_xml_message' from 'utils.decorators' (C:\XYBotV2\utils\decorators.py)

2025-07-20 03:10:05 | ERROR | 加载插件模块 Deepseek 时发生错误: Traceback (most recent call last):
  File "C:\XYBotV2\utils\plugin_manager.py", line 127, in load_plugins_from_directory
    module = importlib.import_module(f"plugins.{item}.main")
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Program Files\Python311\Lib\importlib\__init__.py", line 126, in import_module
    return _bootstrap._gcd_import(name[level:], package, level)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "<frozen importlib._bootstrap>", line 1204, in _gcd_import
  File "<frozen importlib._bootstrap>", line 1176, in _find_and_load
  File "<frozen importlib._bootstrap>", line 1147, in _find_and_load_unlocked
  File "<frozen importlib._bootstrap>", line 690, in _load_unlocked
  File "<frozen importlib._bootstrap_external>", line 940, in exec_module
  File "<frozen importlib._bootstrap>", line 241, in _call_with_frames_removed
  File "C:\XYBotV2\plugins\Deepseek\main.py", line 22, in <module>
    from utils.temp_file_manager import cleanup_file
ModuleNotFoundError: No module named 'utils.temp_file_manager'

2025-07-20 03:10:05 | ERROR | 加载插件模块 DoubaoImageRecognition 时发生错误: Traceback (most recent call last):
  File "C:\XYBotV2\utils\plugin_manager.py", line 127, in load_plugins_from_directory
    module = importlib.import_module(f"plugins.{item}.main")
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Program Files\Python311\Lib\importlib\__init__.py", line 126, in import_module
    return _bootstrap._gcd_import(name[level:], package, level)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "<frozen importlib._bootstrap>", line 1204, in _gcd_import
  File "<frozen importlib._bootstrap>", line 1176, in _find_and_load
  File "<frozen importlib._bootstrap>", line 1147, in _find_and_load_unlocked
  File "<frozen importlib._bootstrap>", line 690, in _load_unlocked
  File "<frozen importlib._bootstrap_external>", line 940, in exec_module
  File "<frozen importlib._bootstrap>", line 241, in _call_with_frames_removed
  File "C:\XYBotV2\plugins\DoubaoImageRecognition\main.py", line 28, in <module>
    from utils.temp_file_manager import create_temp_file, cleanup_file, mark_file_active, mark_file_inactive
ModuleNotFoundError: No module named 'utils.temp_file_manager'

2025-07-20 03:10:05 | ERROR | 加载插件模块 DouBaoImageToImage 时发生错误: Traceback (most recent call last):
  File "C:\XYBotV2\utils\plugin_manager.py", line 127, in load_plugins_from_directory
    module = importlib.import_module(f"plugins.{item}.main")
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Program Files\Python311\Lib\importlib\__init__.py", line 126, in import_module
    return _bootstrap._gcd_import(name[level:], package, level)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "<frozen importlib._bootstrap>", line 1204, in _gcd_import
  File "<frozen importlib._bootstrap>", line 1176, in _find_and_load
  File "<frozen importlib._bootstrap>", line 1147, in _find_and_load_unlocked
  File "<frozen importlib._bootstrap>", line 690, in _load_unlocked
  File "<frozen importlib._bootstrap_external>", line 940, in exec_module
  File "<frozen importlib._bootstrap>", line 241, in _call_with_frames_removed
  File "C:\XYBotV2\plugins\DouBaoImageToImage\main.py", line 12, in <module>
    from utils.temp_file_manager import create_temp_file, cleanup_file, mark_file_active, mark_file_inactive
ModuleNotFoundError: No module named 'utils.temp_file_manager'

2025-07-20 03:10:06 | ERROR | 加载插件模块 DouBaoImageToVideo 时发生错误: Traceback (most recent call last):
  File "C:\XYBotV2\utils\plugin_manager.py", line 127, in load_plugins_from_directory
    module = importlib.import_module(f"plugins.{item}.main")
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Program Files\Python311\Lib\importlib\__init__.py", line 126, in import_module
    return _bootstrap._gcd_import(name[level:], package, level)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "<frozen importlib._bootstrap>", line 1204, in _gcd_import
  File "<frozen importlib._bootstrap>", line 1176, in _find_and_load
  File "<frozen importlib._bootstrap>", line 1126, in _find_and_load_unlocked
  File "<frozen importlib._bootstrap>", line 241, in _call_with_frames_removed
  File "<frozen importlib._bootstrap>", line 1204, in _gcd_import
  File "<frozen importlib._bootstrap>", line 1176, in _find_and_load
  File "<frozen importlib._bootstrap>", line 1147, in _find_and_load_unlocked
  File "<frozen importlib._bootstrap>", line 690, in _load_unlocked
  File "<frozen importlib._bootstrap_external>", line 940, in exec_module
  File "<frozen importlib._bootstrap>", line 241, in _call_with_frames_removed
  File "C:\XYBotV2\plugins\DouBaoImageToVideo\__init__.py", line 1, in <module>
    from .main import DouBaoImageToVideo
  File "C:\XYBotV2\plugins\DouBaoImageToVideo\main.py", line 28, in <module>
    from utils.temp_file_manager import cleanup_file
ModuleNotFoundError: No module named 'utils.temp_file_manager'

2025-07-20 03:10:06 | ERROR | 加载插件模块 DouHui 时发生错误: Traceback (most recent call last):
  File "C:\XYBotV2\utils\plugin_manager.py", line 127, in load_plugins_from_directory
    module = importlib.import_module(f"plugins.{item}.main")
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Program Files\Python311\Lib\importlib\__init__.py", line 126, in import_module
    return _bootstrap._gcd_import(name[level:], package, level)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "<frozen importlib._bootstrap>", line 1204, in _gcd_import
  File "<frozen importlib._bootstrap>", line 1176, in _find_and_load
  File "<frozen importlib._bootstrap>", line 1147, in _find_and_load_unlocked
  File "<frozen importlib._bootstrap>", line 690, in _load_unlocked
  File "<frozen importlib._bootstrap_external>", line 940, in exec_module
  File "<frozen importlib._bootstrap>", line 241, in _call_with_frames_removed
  File "C:\XYBotV2\plugins\DouHui\main.py", line 21, in <module>
    from utils.temp_file_manager import cleanup_file
ModuleNotFoundError: No module named 'utils.temp_file_manager'

2025-07-20 03:10:06 | INFO | 成功加载表情映射文件，共 515 条记录
2025-07-20 03:10:06 | ERROR | 加载插件模块 Gomoku 时发生错误: Traceback (most recent call last):
  File "C:\XYBotV2\utils\plugin_manager.py", line 127, in load_plugins_from_directory
    module = importlib.import_module(f"plugins.{item}.main")
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Program Files\Python311\Lib\importlib\__init__.py", line 126, in import_module
    return _bootstrap._gcd_import(name[level:], package, level)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "<frozen importlib._bootstrap>", line 1204, in _gcd_import
  File "<frozen importlib._bootstrap>", line 1176, in _find_and_load
  File "<frozen importlib._bootstrap>", line 1147, in _find_and_load_unlocked
  File "<frozen importlib._bootstrap>", line 690, in _load_unlocked
  File "<frozen importlib._bootstrap_external>", line 940, in exec_module
  File "<frozen importlib._bootstrap>", line 241, in _call_with_frames_removed
  File "C:\XYBotV2\plugins\Gomoku\main.py", line 9, in <module>
    from database.XYBotDB import XYBotDB
ModuleNotFoundError: No module named 'database.XYBotDB'

2025-07-20 03:10:06 | SUCCESS | 插件 GoodMorning 已加载定时任务: {'plugins.GoodMorning.main.GoodMorning.daily_task'}
2025-07-20 03:10:06 | ERROR | 加载插件模块 JiemengDraw 时发生错误: Traceback (most recent call last):
  File "C:\XYBotV2\utils\plugin_manager.py", line 127, in load_plugins_from_directory
    module = importlib.import_module(f"plugins.{item}.main")
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Program Files\Python311\Lib\importlib\__init__.py", line 126, in import_module
    return _bootstrap._gcd_import(name[level:], package, level)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "<frozen importlib._bootstrap>", line 1204, in _gcd_import
  File "<frozen importlib._bootstrap>", line 1176, in _find_and_load
  File "<frozen importlib._bootstrap>", line 1147, in _find_and_load_unlocked
  File "<frozen importlib._bootstrap>", line 690, in _load_unlocked
  File "<frozen importlib._bootstrap_external>", line 940, in exec_module
  File "<frozen importlib._bootstrap>", line 241, in _call_with_frames_removed
  File "C:\XYBotV2\plugins\JiemengDraw\main.py", line 9, in <module>
    from utils.temp_file_manager import cleanup_file
ModuleNotFoundError: No module named 'utils.temp_file_manager'

2025-07-20 03:10:06 | ERROR | 加载插件模块 KeLingImageToImage 时发生错误: Traceback (most recent call last):
  File "C:\XYBotV2\utils\plugin_manager.py", line 127, in load_plugins_from_directory
    module = importlib.import_module(f"plugins.{item}.main")
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Program Files\Python311\Lib\importlib\__init__.py", line 126, in import_module
    return _bootstrap._gcd_import(name[level:], package, level)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "<frozen importlib._bootstrap>", line 1204, in _gcd_import
  File "<frozen importlib._bootstrap>", line 1176, in _find_and_load
  File "<frozen importlib._bootstrap>", line 1147, in _find_and_load_unlocked
  File "<frozen importlib._bootstrap>", line 690, in _load_unlocked
  File "<frozen importlib._bootstrap_external>", line 940, in exec_module
  File "<frozen importlib._bootstrap>", line 241, in _call_with_frames_removed
  File "C:\XYBotV2\plugins\KeLingImageToImage\main.py", line 8, in <module>
    from utils.temp_file_manager import cleanup_file
ModuleNotFoundError: No module named 'utils.temp_file_manager'

2025-07-20 03:10:06 | ERROR | 加载插件模块 Leaderboard 时发生错误: Traceback (most recent call last):
  File "C:\XYBotV2\utils\plugin_manager.py", line 127, in load_plugins_from_directory
    module = importlib.import_module(f"plugins.{item}.main")
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Program Files\Python311\Lib\importlib\__init__.py", line 126, in import_module
    return _bootstrap._gcd_import(name[level:], package, level)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "<frozen importlib._bootstrap>", line 1204, in _gcd_import
  File "<frozen importlib._bootstrap>", line 1176, in _find_and_load
  File "<frozen importlib._bootstrap>", line 1147, in _find_and_load_unlocked
  File "<frozen importlib._bootstrap>", line 690, in _load_unlocked
  File "<frozen importlib._bootstrap_external>", line 940, in exec_module
  File "<frozen importlib._bootstrap>", line 241, in _call_with_frames_removed
  File "C:\XYBotV2\plugins\Leaderboard\main.py", line 8, in <module>
    from database.XYBotDB import XYBotDB
ModuleNotFoundError: No module named 'database.XYBotDB'

2025-07-20 03:10:06 | ERROR | 加载插件模块 LuckyDraw 时发生错误: Traceback (most recent call last):
  File "C:\XYBotV2\utils\plugin_manager.py", line 127, in load_plugins_from_directory
    module = importlib.import_module(f"plugins.{item}.main")
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Program Files\Python311\Lib\importlib\__init__.py", line 126, in import_module
    return _bootstrap._gcd_import(name[level:], package, level)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "<frozen importlib._bootstrap>", line 1204, in _gcd_import
  File "<frozen importlib._bootstrap>", line 1176, in _find_and_load
  File "<frozen importlib._bootstrap>", line 1147, in _find_and_load_unlocked
  File "<frozen importlib._bootstrap>", line 690, in _load_unlocked
  File "<frozen importlib._bootstrap_external>", line 940, in exec_module
  File "<frozen importlib._bootstrap>", line 241, in _call_with_frames_removed
  File "C:\XYBotV2\plugins\LuckyDraw\main.py", line 7, in <module>
    from database.XYBotDB import XYBotDB
ModuleNotFoundError: No module named 'database.XYBotDB'

2025-07-20 03:10:06 | ERROR | 加载插件模块 ManagePlugin 时发生错误: Traceback (most recent call last):
  File "C:\XYBotV2\utils\plugin_manager.py", line 127, in load_plugins_from_directory
    module = importlib.import_module(f"plugins.{item}.main")
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Program Files\Python311\Lib\importlib\__init__.py", line 126, in import_module
    return _bootstrap._gcd_import(name[level:], package, level)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "<frozen importlib._bootstrap>", line 1204, in _gcd_import
  File "<frozen importlib._bootstrap>", line 1176, in _find_and_load
  File "<frozen importlib._bootstrap>", line 1147, in _find_and_load_unlocked
  File "<frozen importlib._bootstrap>", line 690, in _load_unlocked
  File "<frozen importlib._bootstrap_external>", line 940, in exec_module
  File "<frozen importlib._bootstrap>", line 241, in _call_with_frames_removed
  File "C:\XYBotV2\plugins\ManagePlugin\main.py", line 6, in <module>
    from database.XYBotDB import XYBotDB
ModuleNotFoundError: No module named 'database.XYBotDB'

2025-07-20 03:10:06 | SUCCESS | 插件 News 已加载定时任务: {'plugins.News.main.News.noon_news', 'plugins.News.main.News.night_news'}
2025-07-20 03:10:06 | INFO | [PatReply] AMR文件已存在且是最新的: C:\XYBotV2\data\paiyipai\1_BFB5258B-2B13-4CF4-A40C-5BCCE74E5414_converted.amr
2025-07-20 03:10:06 | DEBUG | [PatReply] 从缓存加载base64: 18228 字符
2025-07-20 03:10:06 | ERROR | 加载插件模块 PointTrade 时发生错误: Traceback (most recent call last):
  File "C:\XYBotV2\utils\plugin_manager.py", line 127, in load_plugins_from_directory
    module = importlib.import_module(f"plugins.{item}.main")
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Program Files\Python311\Lib\importlib\__init__.py", line 126, in import_module
    return _bootstrap._gcd_import(name[level:], package, level)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "<frozen importlib._bootstrap>", line 1204, in _gcd_import
  File "<frozen importlib._bootstrap>", line 1176, in _find_and_load
  File "<frozen importlib._bootstrap>", line 1147, in _find_and_load_unlocked
  File "<frozen importlib._bootstrap>", line 690, in _load_unlocked
  File "<frozen importlib._bootstrap_external>", line 940, in exec_module
  File "<frozen importlib._bootstrap>", line 241, in _call_with_frames_removed
  File "C:\XYBotV2\plugins\PointTrade\main.py", line 5, in <module>
    from database.XYBotDB import XYBotDB
ModuleNotFoundError: No module named 'database.XYBotDB'

2025-07-20 03:10:06 | SUCCESS | 插件 QuarkSignIn 已加载定时任务: {'plugins.QuarkSignIn.main.QuarkSignIn.auto_signin_task'}
2025-07-20 03:10:06 | INFO | [QuarkSignIn] 已设置自动签到时间: 08:00
2025-07-20 03:10:06 | ERROR | 加载插件模块 QueryPoint 时发生错误: Traceback (most recent call last):
  File "C:\XYBotV2\utils\plugin_manager.py", line 127, in load_plugins_from_directory
    module = importlib.import_module(f"plugins.{item}.main")
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Program Files\Python311\Lib\importlib\__init__.py", line 126, in import_module
    return _bootstrap._gcd_import(name[level:], package, level)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "<frozen importlib._bootstrap>", line 1204, in _gcd_import
  File "<frozen importlib._bootstrap>", line 1176, in _find_and_load
  File "<frozen importlib._bootstrap>", line 1147, in _find_and_load_unlocked
  File "<frozen importlib._bootstrap>", line 690, in _load_unlocked
  File "<frozen importlib._bootstrap_external>", line 940, in exec_module
  File "<frozen importlib._bootstrap>", line 241, in _call_with_frames_removed
  File "C:\XYBotV2\plugins\QueryPoint\main.py", line 4, in <module>
    from database.XYBotDB import XYBotDB
ModuleNotFoundError: No module named 'database.XYBotDB'

2025-07-20 03:10:06 | INFO | 插件状态: 启用, 检查间隔: 3600秒, 最大重试次数: 3, 重试延迟: 5秒
2025-07-20 03:10:06 | INFO | 已加载管理员列表: ['wxid_ubbh6q832tcs21', 'admin-wxid']
2025-07-20 03:10:06 | INFO | 已加载 2 个改名监控群和 1 个退群监控群
2025-07-20 03:10:06 | INFO | [RenameReminder] 开始启用插件...
2025-07-20 03:10:06 | SUCCESS | 插件 RenameReminder 已加载定时任务: {'plugins.RenameReminder.main.RenameReminder.check_name_changes'}
2025-07-20 03:10:06 | INFO | [RenameReminder] 插件已启用，开始初始化...
2025-07-20 03:10:06 | INFO | [RenameReminder] 支持的命令: 开启改名提醒, 关闭改名提醒, 开启退群提醒, 关闭退群提醒, 查看提醒状态
2025-07-20 03:10:06 | INFO | 已设置检查间隔为 3600 秒
2025-07-20 03:10:06 | INFO | 已加载 2 个监控群，开始更新成员列表
2025-07-20 03:10:07 | DEBUG | 已更新群 27852221909@chatroom 的成员列表
2025-07-20 03:10:08 | DEBUG | 已更新群 51891329927@chatroom 的成员列表
2025-07-20 03:10:08 | ERROR | 加载插件模块 SignIn 时发生错误: Traceback (most recent call last):
  File "C:\XYBotV2\utils\plugin_manager.py", line 127, in load_plugins_from_directory
    module = importlib.import_module(f"plugins.{item}.main")
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Program Files\Python311\Lib\importlib\__init__.py", line 126, in import_module
    return _bootstrap._gcd_import(name[level:], package, level)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "<frozen importlib._bootstrap>", line 1204, in _gcd_import
  File "<frozen importlib._bootstrap>", line 1176, in _find_and_load
  File "<frozen importlib._bootstrap>", line 1147, in _find_and_load_unlocked
  File "<frozen importlib._bootstrap>", line 690, in _load_unlocked
  File "<frozen importlib._bootstrap_external>", line 940, in exec_module
  File "<frozen importlib._bootstrap>", line 241, in _call_with_frames_removed
  File "C:\XYBotV2\plugins\SignIn\main.py", line 8, in <module>
    from database.XYBotDB import XYBotDB
ModuleNotFoundError: No module named 'database.XYBotDB'

2025-07-20 03:10:08 | ERROR | 加载插件模块 TempFileManager 时发生错误: Traceback (most recent call last):
  File "C:\XYBotV2\utils\plugin_manager.py", line 127, in load_plugins_from_directory
    module = importlib.import_module(f"plugins.{item}.main")
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Program Files\Python311\Lib\importlib\__init__.py", line 126, in import_module
    return _bootstrap._gcd_import(name[level:], package, level)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "<frozen importlib._bootstrap>", line 1204, in _gcd_import
  File "<frozen importlib._bootstrap>", line 1176, in _find_and_load
  File "<frozen importlib._bootstrap>", line 1147, in _find_and_load_unlocked
  File "<frozen importlib._bootstrap>", line 690, in _load_unlocked
  File "<frozen importlib._bootstrap_external>", line 940, in exec_module
  File "<frozen importlib._bootstrap>", line 241, in _call_with_frames_removed
  File "C:\XYBotV2\plugins\TempFileManager\main.py", line 12, in <module>
    from utils.temp_file_manager import temp_manager, get_temp_stats, manual_cleanup
ModuleNotFoundError: No module named 'utils.temp_file_manager'

2025-07-20 03:10:08 | ERROR | 加载插件模块 TimerTask 时发生错误: Traceback (most recent call last):
  File "C:\XYBotV2\utils\plugin_manager.py", line 127, in load_plugins_from_directory
    module = importlib.import_module(f"plugins.{item}.main")
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Program Files\Python311\Lib\importlib\__init__.py", line 126, in import_module
    return _bootstrap._gcd_import(name[level:], package, level)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "<frozen importlib._bootstrap>", line 1204, in _gcd_import
  File "<frozen importlib._bootstrap>", line 1176, in _find_and_load
  File "<frozen importlib._bootstrap>", line 1147, in _find_and_load_unlocked
  File "<frozen importlib._bootstrap>", line 690, in _load_unlocked
  File "<frozen importlib._bootstrap_external>", line 940, in exec_module
  File "<frozen importlib._bootstrap>", line 241, in _call_with_frames_removed
  File "C:\XYBotV2\plugins\TimerTask\main.py", line 13, in <module>
    from database.keyvalDB import KeyvalDB
ModuleNotFoundError: No module named 'database.keyvalDB'

2025-07-20 03:10:08 | INFO | [VideoDemand] 加载了 128 个视频类别配置
2025-07-20 03:10:08 | ERROR | 加载插件模块 VideoParser 时发生错误: Traceback (most recent call last):
  File "C:\XYBotV2\utils\plugin_manager.py", line 127, in load_plugins_from_directory
    module = importlib.import_module(f"plugins.{item}.main")
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Program Files\Python311\Lib\importlib\__init__.py", line 126, in import_module
    return _bootstrap._gcd_import(name[level:], package, level)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "<frozen importlib._bootstrap>", line 1204, in _gcd_import
  File "<frozen importlib._bootstrap>", line 1176, in _find_and_load
  File "<frozen importlib._bootstrap>", line 1147, in _find_and_load_unlocked
  File "<frozen importlib._bootstrap>", line 690, in _load_unlocked
  File "<frozen importlib._bootstrap_external>", line 940, in exec_module
  File "<frozen importlib._bootstrap>", line 241, in _call_with_frames_removed
  File "C:\XYBotV2\plugins\VideoParser\main.py", line 97, in <module>
    from utils.temp_file_manager import cleanup_file
ModuleNotFoundError: No module named 'utils.temp_file_manager'

2025-07-20 03:10:08 | ERROR | 加载插件模块 VoiceTest 时发生错误: Traceback (most recent call last):
  File "C:\XYBotV2\utils\plugin_manager.py", line 127, in load_plugins_from_directory
    module = importlib.import_module(f"plugins.{item}.main")
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Program Files\Python311\Lib\importlib\__init__.py", line 126, in import_module
    return _bootstrap._gcd_import(name[level:], package, level)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "<frozen importlib._bootstrap>", line 1204, in _gcd_import
  File "<frozen importlib._bootstrap>", line 1176, in _find_and_load
  File "<frozen importlib._bootstrap>", line 1147, in _find_and_load_unlocked
  File "<frozen importlib._bootstrap>", line 690, in _load_unlocked
  File "<frozen importlib._bootstrap_external>", line 940, in exec_module
  File "<frozen importlib._bootstrap>", line 241, in _call_with_frames_removed
  File "C:\XYBotV2\plugins\VoiceTest\main.py", line 17, in <module>
    from utils.temp_file_manager import create_temp_file, cleanup_file, mark_file_active, mark_file_inactive
ModuleNotFoundError: No module named 'utils.temp_file_manager'

2025-07-20 03:10:09 | ERROR | 加载插件模块 YaoyaoPlugin 时发生错误: Traceback (most recent call last):
  File "C:\XYBotV2\utils\plugin_manager.py", line 127, in load_plugins_from_directory
    module = importlib.import_module(f"plugins.{item}.main")
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Program Files\Python311\Lib\importlib\__init__.py", line 126, in import_module
    return _bootstrap._gcd_import(name[level:], package, level)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "<frozen importlib._bootstrap>", line 1204, in _gcd_import
  File "<frozen importlib._bootstrap>", line 1176, in _find_and_load
  File "<frozen importlib._bootstrap>", line 1147, in _find_and_load_unlocked
  File "<frozen importlib._bootstrap>", line 690, in _load_unlocked
  File "<frozen importlib._bootstrap_external>", line 940, in exec_module
  File "<frozen importlib._bootstrap>", line 241, in _call_with_frames_removed
  File "C:\XYBotV2\plugins\YaoyaoPlugin\main.py", line 24, in <module>
    from utils.temp_file_manager import cleanup_file
ModuleNotFoundError: No module named 'utils.temp_file_manager'

2025-07-20 03:10:09 | INFO | [yuanbao] 插件初始化完成
2025-07-20 03:10:09 | DEBUG | [yuanbao] 指令: ['元宝', 'yuanbao']
2025-07-20 03:10:09 | DEBUG | [yuanbao] 绘图模型: gpt_175B_0404
2025-07-20 03:10:09 | DEBUG | [yuanbao] 认证信息加载状态: hy_token=True, hy_user=True, agent_id=True, x_uskey=True
2025-07-20 03:10:09 | SUCCESS | 已加载插件: ['AISignIn', 'AppMessageTester', 'ArticleForwarder', 'BaiduDraw', 'DanceSignInPlugin', 'Doubao', 'DoubaoDrawing', 'DoubaoVideoSearch', 'EmojiTestPlugin', 'FixedQuoteTest', 'GetContact', 'GoodMorning', 'HunyuanDraw', 'ImageEcho', 'ImageToImage', 'KlingAI', 'Menu', 'MiniProgramTester', 'Music', 'News', 'PatReply', 'QuarkSignIn', 'RandomMember', 'RandomPicture', 'RenameReminder', 'RevokePlugin', 'TencentLke', 'TongyiDraw', 'URLShortener', 'VideoDemand', 'VideoTest', 'VivoAgentsPlugin', 'Warthunder', 'WeatherQuery', 'WelcomePlugin', 'Xunfei', 'Yuanbao', '抽签', '造梦次元']
2025-07-20 03:10:09 | INFO | 处理堆积消息中
2025-07-20 03:10:09 | SUCCESS | 处理堆积消息完毕
2025-07-20 03:10:09 | SUCCESS | 开始处理消息
2025-07-20 03:10:46 | INFO | 收到终止信号，正在优雅关闭...
2025-07-20 03:10:46 | INFO | 正在取消主任务...
2025-07-20 03:10:46 | INFO | 消息处理队列被取消
