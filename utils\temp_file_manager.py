"""
统一的临时文件管理器
提供自动清理、配置化管理等功能
"""

import os
import time
import asyncio
import threading
from pathlib import Path
from typing import Dict, List, Optional, Union
from dataclasses import dataclass
from concurrent.futures import ThreadPoolExecutor
from loguru import logger


@dataclass
class CleanupRule:
    """清理规则配置"""
    max_age_hours: float = 1.0  # 文件最大保留时间（小时）
    file_patterns: List[str] = None  # 文件匹配模式，如 ["*.jpg", "*.png", "*.tmp"]
    exclude_patterns: List[str] = None  # 排除模式
    min_size_mb: float = 0  # 最小文件大小（MB），小于此大小的文件不清理
    max_size_mb: float = float('inf')  # 最大文件大小（MB），大于此大小的文件优先清理
    
    def __post_init__(self):
        if self.file_patterns is None:
            self.file_patterns = ["*"]  # 默认匹配所有文件
        if self.exclude_patterns is None:
            self.exclude_patterns = []


class TempFileManager:
    """统一临时文件管理器"""
    
    _instance = None
    _lock = threading.Lock()
    
    def __new__(cls):
        if cls._instance is None:
            with cls._lock:
                if cls._instance is None:
                    cls._instance = super().__new__(cls)
        return cls._instance
    
    def __init__(self):
        if hasattr(self, '_initialized'):
            return
        
        self._initialized = True
        self.base_temp_dir = Path("temp")
        self.cleanup_rules: Dict[str, CleanupRule] = {}
        self.active_files: set = set()  # 正在使用的文件
        self.active_files_lock = threading.Lock()
        
        # 清理任务配置
        self.cleanup_interval = 1800  # 30分钟清理一次
        self.last_cleanup = 0
        self.cleanup_task = None
        self.executor = ThreadPoolExecutor(max_workers=2, thread_name_prefix="TempCleanup")
        
        # 默认清理规则
        self._setup_default_rules()
        
        # 启动清理任务
        self.start_cleanup_task()
    
    def _setup_default_rules(self):
        """设置默认清理规则"""
        # 通用规则：1小时清理所有文件
        self.add_cleanup_rule("default", CleanupRule(
            max_age_hours=1.0,
            file_patterns=["*"]
        ))
        
        # 图片文件：2小时清理
        self.add_cleanup_rule("images", CleanupRule(
            max_age_hours=2.0,
            file_patterns=["*.jpg", "*.jpeg", "*.png", "*.gif", "*.webp", "*.bmp"]
        ))
        
        # 视频文件：30分钟清理（通常较大）
        self.add_cleanup_rule("videos", CleanupRule(
            max_age_hours=0.5,
            file_patterns=["*.mp4", "*.avi", "*.mov", "*.mkv", "*.flv"]
        ))
        
        # 音频文件：1小时清理
        self.add_cleanup_rule("audio", CleanupRule(
            max_age_hours=1.0,
            file_patterns=["*.mp3", "*.wav", "*.amr", "*.silk", "*.m4a"]
        ))
        
        # 临时文件：15分钟清理
        self.add_cleanup_rule("temp", CleanupRule(
            max_age_hours=0.25,
            file_patterns=["*.tmp", "*.temp", "*.cache"]
        ))
        
        # 大文件：优先清理超过50MB的文件
        self.add_cleanup_rule("large_files", CleanupRule(
            max_age_hours=0.5,
            file_patterns=["*"],
            min_size_mb=50.0
        ))
    
    def add_cleanup_rule(self, name: str, rule: CleanupRule):
        """添加清理规则"""
        self.cleanup_rules[name] = rule
        logger.debug(f"[TempFileManager] 添加清理规则: {name}")
    
    def remove_cleanup_rule(self, name: str):
        """移除清理规则"""
        if name in self.cleanup_rules:
            del self.cleanup_rules[name]
            logger.debug(f"[TempFileManager] 移除清理规则: {name}")
    
    def mark_file_active(self, file_path: Union[str, Path]):
        """标记文件为活跃状态（正在使用）"""
        with self.active_files_lock:
            self.active_files.add(str(file_path))
    
    def mark_file_inactive(self, file_path: Union[str, Path]):
        """标记文件为非活跃状态"""
        with self.active_files_lock:
            self.active_files.discard(str(file_path))
    
    def create_temp_file(self, plugin_name: str, suffix: str = "", prefix: str = "temp_") -> Path:
        """创建临时文件"""
        plugin_dir = self.base_temp_dir / plugin_name
        plugin_dir.mkdir(parents=True, exist_ok=True)
        
        import tempfile
        fd, temp_path = tempfile.mkstemp(
            suffix=suffix,
            prefix=prefix,
            dir=str(plugin_dir)
        )
        os.close(fd)  # 关闭文件描述符
        
        temp_path = Path(temp_path)
        self.mark_file_active(temp_path)
        logger.debug(f"[TempFileManager] 创建临时文件: {temp_path}")
        return temp_path
    
    def cleanup_file(self, file_path: Union[str, Path], delay_seconds: int = 0):
        """清理单个文件"""
        file_path = Path(file_path)
        
        def _cleanup():
            if delay_seconds > 0:
                time.sleep(delay_seconds)
            
            try:
                self.mark_file_inactive(file_path)
                if file_path.exists():
                    file_path.unlink()
                    logger.debug(f"[TempFileManager] 已清理文件: {file_path}")
            except Exception as e:
                logger.warning(f"[TempFileManager] 清理文件失败: {file_path}, 错误: {e}")
        
        if delay_seconds > 0:
            # 异步延迟清理
            threading.Thread(target=_cleanup, daemon=True).start()
        else:
            # 立即清理
            _cleanup()
    
    def _should_cleanup_file(self, file_path: Path, rule: CleanupRule) -> bool:
        """判断文件是否应该被清理"""
        try:
            # 检查文件是否活跃
            with self.active_files_lock:
                if str(file_path) in self.active_files:
                    return False
            
            # 检查文件模式匹配
            if not any(file_path.match(pattern) for pattern in rule.file_patterns):
                return False
            
            # 检查排除模式
            if any(file_path.match(pattern) for pattern in rule.exclude_patterns):
                return False
            
            # 检查文件年龄
            file_stat = file_path.stat()
            file_age_hours = (time.time() - file_stat.st_mtime) / 3600
            if file_age_hours < rule.max_age_hours:
                return False
            
            # 检查文件大小
            file_size_mb = file_stat.st_size / (1024 * 1024)
            if file_size_mb < rule.min_size_mb or file_size_mb > rule.max_size_mb:
                return False
            
            return True
            
        except Exception as e:
            logger.warning(f"[TempFileManager] 检查文件失败: {file_path}, 错误: {e}")
            return False
    
    def cleanup_directory(self, directory: Union[str, Path], rule_name: str = "default") -> Dict[str, int]:
        """清理指定目录"""
        directory = Path(directory)
        if not directory.exists():
            return {"cleaned": 0, "failed": 0, "size_mb": 0}
        
        rule = self.cleanup_rules.get(rule_name, self.cleanup_rules["default"])
        
        cleaned = 0
        failed = 0
        total_size = 0
        
        try:
            for file_path in directory.rglob("*"):
                if file_path.is_file() and self._should_cleanup_file(file_path, rule):
                    try:
                        file_size = file_path.stat().st_size
                        file_path.unlink()
                        cleaned += 1
                        total_size += file_size
                        logger.debug(f"[TempFileManager] 清理文件: {file_path}")
                    except Exception as e:
                        failed += 1
                        logger.warning(f"[TempFileManager] 清理文件失败: {file_path}, 错误: {e}")
        
        except Exception as e:
            logger.error(f"[TempFileManager] 清理目录失败: {directory}, 错误: {e}")
            failed += 1
        
        return {
            "cleaned": cleaned,
            "failed": failed,
            "size_mb": total_size / (1024 * 1024)
        }
    
    def cleanup_all_temp_files(self) -> Dict[str, Dict[str, int]]:
        """清理所有临时文件"""
        current_time = time.time()
        
        # 检查清理间隔
        if current_time - self.last_cleanup < self.cleanup_interval:
            return {}
        
        logger.info("[TempFileManager] 开始清理临时文件...")
        
        results = {}
        total_cleaned = 0
        total_size = 0
        
        try:
            # 遍历temp目录下的所有子目录
            if self.base_temp_dir.exists():
                for subdir in self.base_temp_dir.iterdir():
                    if subdir.is_dir():
                        # 根据目录内容选择合适的清理规则
                        rule_name = self._select_cleanup_rule(subdir)
                        result = self.cleanup_directory(subdir, rule_name)
                        
                        if result["cleaned"] > 0 or result["failed"] > 0:
                            results[subdir.name] = result
                            total_cleaned += result["cleaned"]
                            total_size += result["size_mb"]
            
            self.last_cleanup = current_time
            
            if total_cleaned > 0:
                logger.info(f"[TempFileManager] 清理完成: 删除 {total_cleaned} 个文件，释放 {total_size:.2f}MB 空间")
            
        except Exception as e:
            logger.error(f"[TempFileManager] 清理临时文件时出错: {e}")
        
        return results

    def force_cleanup_all_temp_files(self) -> Dict[str, Dict[str, int]]:
        """强制清理所有临时文件，不受间隔限制，删除所有文件"""
        logger.info("[TempFileManager] 手动强制清理临时文件...")

        results = {}
        total_cleaned = 0
        total_size = 0

        try:
            # 遍历temp目录下的所有子目录
            if self.base_temp_dir.exists():
                for subdir in self.base_temp_dir.iterdir():
                    if subdir.is_dir():
                        # 手动清理时删除所有文件，不使用规则限制
                        result = self._force_cleanup_directory(subdir)

                        if result["cleaned"] > 0 or result["failed"] > 0:
                            results[subdir.name] = result
                            total_cleaned += result["cleaned"]
                            total_size += result["size_mb"]

            # 更新最后清理时间
            self.last_cleanup = time.time()

            if total_cleaned > 0:
                logger.info(f"[TempFileManager] 手动清理完成: 删除 {total_cleaned} 个文件，释放 {total_size:.2f}MB 空间")
            else:
                logger.info("[TempFileManager] 手动清理完成: 没有需要清理的文件")

        except Exception as e:
            logger.error(f"[TempFileManager] 手动清理临时文件时出错: {e}")

        return results

    def _force_cleanup_directory(self, directory: Path) -> Dict[str, int]:
        """强制清理指定目录的所有文件，不受任何规则限制"""
        directory = Path(directory)
        if not directory.exists():
            return {"cleaned": 0, "failed": 0, "size_mb": 0}

        cleaned = 0
        failed = 0
        total_size = 0

        try:
            for file_path in directory.rglob("*"):
                if file_path.is_file():
                    try:
                        # 检查文件是否活跃（正在使用）
                        with self.active_files_lock:
                            if str(file_path) in self.active_files:
                                continue  # 跳过正在使用的文件

                        file_size = file_path.stat().st_size
                        file_path.unlink()
                        cleaned += 1
                        total_size += file_size
                        logger.debug(f"[TempFileManager] 强制清理文件: {file_path}")
                    except Exception as e:
                        failed += 1
                        logger.warning(f"[TempFileManager] 强制清理文件失败: {file_path}, 错误: {e}")

        except Exception as e:
            logger.error(f"[TempFileManager] 强制清理目录失败: {directory}, 错误: {e}")
            failed += 1

        return {
            "cleaned": cleaned,
            "failed": failed,
            "size_mb": total_size / (1024 * 1024)
        }

    def _select_cleanup_rule(self, directory: Path) -> str:
        """根据目录内容选择合适的清理规则"""
        dir_name = directory.name.lower()
        
        # 根据目录名选择规则
        if any(keyword in dir_name for keyword in ["image", "img", "pic"]):
            return "images"
        elif any(keyword in dir_name for keyword in ["video", "vid", "movie"]):
            return "videos"
        elif any(keyword in dir_name for keyword in ["voice", "audio", "sound"]):
            return "audio"
        elif any(keyword in dir_name for keyword in ["temp", "tmp", "cache"]):
            return "temp"
        else:
            return "default"
    
    def start_cleanup_task(self):
        """启动清理任务"""
        if self.cleanup_task is not None:
            return
        
        def cleanup_worker():
            while True:
                try:
                    time.sleep(self.cleanup_interval)
                    self.cleanup_all_temp_files()
                except Exception as e:
                    logger.error(f"[TempFileManager] 清理任务出错: {e}")
        
        self.cleanup_task = threading.Thread(target=cleanup_worker, daemon=True)
        self.cleanup_task.start()
        logger.info("[TempFileManager] 临时文件清理任务已启动")
    
    def stop_cleanup_task(self):
        """停止清理任务"""
        # 由于使用daemon线程，程序退出时会自动停止
        pass
    
    def get_temp_dir_stats(self) -> Dict[str, Dict[str, Union[int, float]]]:
        """获取临时目录统计信息"""
        stats = {}
        
        try:
            if not self.base_temp_dir.exists():
                return stats
            
            for subdir in self.base_temp_dir.iterdir():
                if subdir.is_dir():
                    file_count = 0
                    total_size = 0
                    oldest_file = None
                    newest_file = None
                    
                    for file_path in subdir.rglob("*"):
                        if file_path.is_file():
                            file_count += 1
                            file_stat = file_path.stat()
                            total_size += file_stat.st_size
                            
                            if oldest_file is None or file_stat.st_mtime < oldest_file:
                                oldest_file = file_stat.st_mtime
                            if newest_file is None or file_stat.st_mtime > newest_file:
                                newest_file = file_stat.st_mtime
                    
                    stats[subdir.name] = {
                        "file_count": file_count,
                        "total_size_mb": total_size / (1024 * 1024),
                        "oldest_file_age_hours": (time.time() - oldest_file) / 3600 if oldest_file else 0,
                        "newest_file_age_hours": (time.time() - newest_file) / 3600 if newest_file else 0
                    }
        
        except Exception as e:
            logger.error(f"[TempFileManager] 获取统计信息失败: {e}")
        
        return stats


# 全局实例
temp_manager = TempFileManager()


# 便捷函数
def create_temp_file(plugin_name: str, suffix: str = "", prefix: str = "temp_") -> Path:
    """创建临时文件"""
    return temp_manager.create_temp_file(plugin_name, suffix, prefix)


def cleanup_file(file_path: Union[str, Path], delay_seconds: int = 0):
    """清理文件"""
    temp_manager.cleanup_file(file_path, delay_seconds)


def mark_file_active(file_path: Union[str, Path]):
    """标记文件为活跃状态"""
    temp_manager.mark_file_active(file_path)


def mark_file_inactive(file_path: Union[str, Path]):
    """标记文件为非活跃状态"""
    temp_manager.mark_file_inactive(file_path)


def get_temp_stats() -> Dict[str, Dict[str, Union[int, float]]]:
    """获取临时文件统计信息"""
    return temp_manager.get_temp_dir_stats()


def manual_cleanup() -> Dict[str, Dict[str, int]]:
    """手动触发清理，强制执行不受间隔限制"""
    return temp_manager.force_cleanup_all_temp_files(manual=True)
