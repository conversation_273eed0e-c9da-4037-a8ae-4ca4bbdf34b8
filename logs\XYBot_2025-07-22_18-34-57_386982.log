2025-07-22 18:34:58 | SUCCESS | 读取主设置成功
2025-07-22 18:34:58 | DEBUG | 最终使用的 Redis 主机地址: 127.0.0.1
2025-07-22 18:34:58 | INFO | 2025/07/22 18:34:58 GetRedisAddr: 127.0.0.1:6379
2025-07-22 18:34:58 | INFO | 2025/07/22 18:34:58 Redis连接成功 | 地址:127.0.0.1:6379 DB:0
2025-07-22 18:34:58 | INFO | 2025/07/22 18:34:58 Server start at :9000
2025-07-22 18:34:59 | SUCCESS | WechatAPI服务已启动
2025-07-22 18:34:59 | INFO | 登录账号信息: wxid: wxid_4usgcju5ey9q29  昵称: 瑶瑶  微信号: Today-KFC  手机号: None
2025-07-22 18:34:59 | INFO | 登录设备信息: device_name: <PERSON>'s Pad  device_id: 49d050a7e94d1ecc40790d74b1c4369f
2025-07-22 18:34:59 | SUCCESS | 登录成功
2025-07-22 18:34:59 | SUCCESS | 已开启自动心跳
2025-07-22 18:34:59 | INFO | 成功加载表情映射文件，共 521 条记录
2025-07-22 18:34:59 | SUCCESS | 数据库初始化成功
2025-07-22 18:34:59 | SUCCESS | 定时任务已启动
2025-07-22 18:34:59 | SUCCESS | 插件 AISignIn 已加载定时任务: {'plugins.AISignIn.main.AISignIn.auto_signin_task'}
2025-07-22 18:34:59 | INFO | 已加载管理员列表: ['wxid_ubbh6q832tcs21', 'admin-wxid']
2025-07-22 18:35:00 | INFO | 播客API初始化成功
2025-07-22 18:35:00 | INFO | 加载配置完成，转换后的数据: {'gh_7057516c9e71': {'name': '唱舞全明星', 'groups': ['27852221909@chatroom', '55878994168@chatroom', '51891329927@chatroom']}, 'gh_d4d21a0e4623': {'name': '两颗桃', 'groups': ['48097389945@chatroom']}, 'gh_410ff2fa6ec9': {'name': 'NGC660 Ai研究院', 'groups': ['47442567074@chatroom']}, 'gh_f78c53c507c0': {'name': '不正常吐槽君', 'groups': ['48097389945@chatroom']}, 'gh_ba3381c847b3': {'name': '唱舞星计划', 'groups': ['51891329927@chatroom']}, 'gh_1a102fb4cad9': {'name': '我在娱乐圈的唯一人脉', 'groups': ['48097389945@chatroom', '27852221909@chatroom']}}
2025-07-22 18:35:00 | INFO | ArticleForwarder插件初始化完成 - 监控配置: {'gh_7057516c9e71': {'name': '唱舞全明星', 'groups': ['27852221909@chatroom', '55878994168@chatroom', '51891329927@chatroom']}, 'gh_d4d21a0e4623': {'name': '两颗桃', 'groups': ['48097389945@chatroom']}, 'gh_410ff2fa6ec9': {'name': 'NGC660 Ai研究院', 'groups': ['47442567074@chatroom']}, 'gh_f78c53c507c0': {'name': '不正常吐槽君', 'groups': ['48097389945@chatroom']}, 'gh_ba3381c847b3': {'name': '唱舞星计划', 'groups': ['51891329927@chatroom']}, 'gh_1a102fb4cad9': {'name': '我在娱乐圈的唯一人脉', 'groups': ['48097389945@chatroom', '27852221909@chatroom']}}
2025-07-22 18:35:00 | DEBUG | [TempFileManager] 添加清理规则: default
2025-07-22 18:35:00 | DEBUG | [TempFileManager] 添加清理规则: images
2025-07-22 18:35:00 | DEBUG | [TempFileManager] 添加清理规则: videos
2025-07-22 18:35:00 | DEBUG | [TempFileManager] 添加清理规则: audio
2025-07-22 18:35:00 | DEBUG | [TempFileManager] 添加清理规则: temp
2025-07-22 18:35:00 | DEBUG | [TempFileManager] 添加清理规则: large_files
2025-07-22 18:35:00 | INFO | [TempFileManager] 临时文件清理任务已启动
2025-07-22 18:35:00 | INFO | [ChatSummary] 数据库初始化成功
2025-07-22 18:35:00 | INFO | 成功加载表情映射文件，共 521 条记录
2025-07-22 18:35:00 | SUCCESS | 插件 GoodMorning 已加载定时任务: {'plugins.GoodMorning.main.GoodMorning.daily_task'}
2025-07-22 18:35:01 | SUCCESS | 插件 News 已加载定时任务: {'plugins.News.main.News.night_news', 'plugins.News.main.News.noon_news'}
2025-07-22 18:35:01 | INFO | [PatReply] AMR文件已存在且是最新的: C:\XYBotV2\data\paiyipai\1_BFB5258B-2B13-4CF4-A40C-5BCCE74E5414_converted.amr
2025-07-22 18:35:01 | DEBUG | [PatReply] 从缓存加载base64: 18228 字符
2025-07-22 18:35:01 | SUCCESS | 插件 QuarkSignIn 已加载定时任务: {'plugins.QuarkSignIn.main.QuarkSignIn.auto_signin_task'}
2025-07-22 18:35:01 | INFO | [QuarkSignIn] 已设置自动签到时间: 08:00
2025-07-22 18:35:01 | INFO | 插件状态: 启用, 检查间隔: 3600秒, 最大重试次数: 3, 重试延迟: 5秒
2025-07-22 18:35:01 | INFO | 已加载管理员列表: ['wxid_ubbh6q832tcs21', 'admin-wxid']
2025-07-22 18:35:01 | INFO | 已加载 2 个改名监控群和 1 个退群监控群
2025-07-22 18:35:01 | INFO | [RenameReminder] 开始启用插件...
2025-07-22 18:35:01 | SUCCESS | 插件 RenameReminder 已加载定时任务: {'plugins.RenameReminder.main.RenameReminder.check_name_changes'}
2025-07-22 18:35:01 | INFO | [RenameReminder] 插件已启用，开始初始化...
2025-07-22 18:35:01 | INFO | [RenameReminder] 支持的命令: 开启改名提醒, 关闭改名提醒, 开启退群提醒, 关闭退群提醒, 查看提醒状态
2025-07-22 18:35:01 | INFO | 已设置检查间隔为 3600 秒
2025-07-22 18:35:01 | INFO | 已加载 2 个监控群，开始更新成员列表
2025-07-22 18:35:01 | DEBUG | 已更新群 51891329927@chatroom 的成员列表
2025-07-22 18:35:01 | DEBUG | 已更新群 27852221909@chatroom 的成员列表
2025-07-22 18:35:02 | SUCCESS | 插件 TimerTask 已加载定时任务: {'plugins.TimerTask.main.TimerTask.check_tasks'}
2025-07-22 18:35:02 | INFO | [VideoDemand] 加载了 128 个视频类别配置
2025-07-22 18:35:02 | INFO | [YaoyaoPlugin] 私聊黑名单用户: ['gh_a5cf07d474f1', 'gh_ba3381c847b3']
2025-07-22 18:35:02 | INFO | [YaoyaoPlugin] 已加载管理员列表: ['wxid_ubbh6q832tcs21', 'admin-wxid']
2025-07-22 18:35:02 | INFO | [yuanbao] 插件初始化完成
2025-07-22 18:35:02 | DEBUG | [yuanbao] 指令: ['元宝', 'yuanbao']
2025-07-22 18:35:02 | DEBUG | [yuanbao] 绘图模型: gpt_175B_0404
2025-07-22 18:35:02 | DEBUG | [yuanbao] 认证信息加载状态: hy_token=True, hy_user=True, agent_id=True, x_uskey=True
2025-07-22 18:35:02 | SUCCESS | 已加载插件: ['AdminPoint', 'AdminSignInReset', 'AdminWhitelist', 'AISignIn', 'AppMessageTester', 'ArticleForwarder', 'BaiduAgentsPlugin', 'BaiduDraw', 'BotStatus', 'ChatSummary', 'DanceSignInPlugin', 'DeepseekPlugin', 'Doubao', 'DoubaoDrawing', 'DoubaoImageRecognition', 'DouBaoImageToImage', 'DouBaoImageToVideo', 'DoubaoVideoSearch', 'DouHui', 'EmojiTestPlugin', 'FixedQuoteTest', 'GetContact', 'Gomoku', 'GoodMorning', 'HunyuanDraw', 'ImageEcho', 'ImageToImage', 'JiemengDraw', 'KeLingImageToImage', 'KlingAI', 'Leaderboard', 'LuckyDraw', 'ManagePlugin', 'MeituAI', 'Menu', 'MiniProgramTester', 'Music', 'News', 'PatReply', 'PointTrade', 'QuarkSignIn', 'QueryPoint', 'RandomMember', 'RandomPicture', 'RenameReminder', 'RevokePlugin', 'RoboNeo', 'SignIn', 'TempFileManagerPlugin', 'TencentLke', 'TimerTask', 'TongyiDraw', 'URLShortener', 'VideoDemand', 'VideoParserPlugin', 'VideoTest', 'VivoAgentsPlugin', 'VoiceTest', 'Warthunder', 'WeatherQuery', 'WelcomePlugin', 'Xunfei', 'YaoyaoPlugin', 'Yuanbao', '抽签', '造梦次元']
2025-07-22 18:35:02 | INFO | 处理堆积消息中
2025-07-22 18:35:02 | DEBUG | 接受到 1 条消息
2025-07-22 18:35:04 | SUCCESS | 处理堆积消息完毕
2025-07-22 18:35:04 | SUCCESS | 开始处理消息
2025-07-22 18:35:51 | DEBUG | 收到消息: {'MsgId': 1547082588, 'FromUserName': {'string': '47442567074@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 1, 'Content': {'string': 'wxid_ubbh6q832tcs21:\n搜歌 小爱'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1753180564, 'MsgSource': '<msgsource>\n\t<pua>1</pua>\n\t<eggIncluded>1</eggIncluded>\n\t<silence>0</silence>\n\t<membercount>494</membercount>\n\t<signature>N0_V1_oDU7U+da|v1_+aT6jvEe</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'PushContent': '郭 : 搜歌 小爱', 'NewMsgId': 6667416433394876892, 'MsgSeq': 871392651}
2025-07-22 18:35:51 | INFO | 收到文本消息: 消息ID:1547082588 来自:47442567074@chatroom 发送人:wxid_ubbh6q832tcs21 @:[] 内容:搜歌 小爱
2025-07-22 18:35:53 | DEBUG | [Music] 成功获取发送者 wxid_ubbh6q832tcs21 的头像: https://wx.qlogo.cn/mmhead/ver_1/8wGAWmpuprTQGmWf3R4kiayO7ibj5rNx2s2JMZEHgRwicoeMTgXgNNibmNjHYPYD7R1ASNnDFza4TIvrF52zFEQve4R8HQcUzjPl5XNUe3DjhVHkJHPDiag6hvibFdic2wM3O2G/132
2025-07-22 18:35:53 | INFO | 发送app消息: 对方wxid:47442567074@chatroom 类型:19 xml:<appmsg appid="" sdkver="0"><title>🎵 小爱 - 搜索结果</title><des>🎵 音乐助手: 为您找到 "小爱" 的相关歌曲：🎶 搜索结果: 1、小爱 -- 李佳思2、大城小爱 -- 王力宏3、大城小爱 (Live) -- 梓渝4、小爱 -- 大柯5、小爱 -- 吴雨霏6、小爱 -- 贯诗钦7、小城小爱 -- Uu (刘梦妤)8、大城小爱 -- SomeLove💡 使用提示: 发送 "听1" 到 "听8" 选择对应歌曲</des><action>view</action><type>19</type><showtype>0</showtype><content/><url>https://support.weixin.qq.com/cgi-bin/mmsupport-bin/readtemplate?t=page/favorite_record__w_unsupport&amp;from=singlemessage&amp;isappinstalled=0</url><lowurl/><dataurl/><lowdataurl/><recorditem><![CDATA[<recordinfo><title>🎵 音乐搜索结果</title><desc>🎵 音乐助手:&#x20;为您找到 "小爱" 的相关歌曲：&#x0A;🎶 搜索结果:&#x20;1、小爱 -- 李佳思2、大城小爱 -- 王力宏3、大城小爱 (Live) -- 梓渝4、小爱 -- 大柯5、小爱 -- 吴雨霏6、小爱 -- 贯诗钦7、小城小爱 -- Uu (刘梦妤)8、大城小爱 -- SomeLove&#x0A;💡 使用提示:&#x20;发送 "听1" 到 "听8" 选择对应歌曲</desc><datalist count="3"><dataitem datatype="1" dataid="3e36dc30ca1b9934d1cb8a5c4a8104ce" datasourceid="6046006305934201455"><datadesc>为您找到 "小爱" 的相关歌曲：</datadesc><sourcename>🎵 音乐助手</sourcename><sourceheadurl>https://wx.qlogo.cn/mmhead/ver_1/8wGAWmpuprTQGmWf3R4kiayO7ibj5rNx2s2JMZEHgRwicoeMTgXgNNibmNjHYPYD7R1ASNnDFza4TIvrF52zFEQve4R8HQcUzjPl5XNUe3DjhVHkJHPDiag6hvibFdic2wM3O2G/132</sourceheadurl><sourcetime>2025-07-21&#x20;12:00:00</sourcetime><srcMsgCreateTime>1753180553</srcMsgCreateTime><fromnewmsgid>6046006305934201455</fromnewmsgid><dataitemsource><hashusername>47117a242116be9ab5e02f7331cd04041140f0caf32a9bedff8b44eb7e0c1cb7</hashusername></dataitemsource></dataitem><dataitem datatype="1" dataid="18c033c927ae5527ff3e4860c96aa345" datasourceid="4005550426972340798"><datadesc>1、小爱 -- 李佳思2、大城小爱 -- 王力宏3、大城小爱 (Live) -- 梓渝4、小爱 -- 大柯5、小爱 -- 吴雨霏6、小爱 -- 贯诗钦7、小城小爱 -- Uu (刘梦妤)8、大城小爱 -- SomeLove</datadesc><sourcename>🎶 搜索结果</sourcename><sourceheadurl>https://wx.qlogo.cn/mmhead/ver_1/PCUwVKYvh0icQrEmB5CiaoyoPaAY9X93CtB0OeA6QmOUml3ljscUgvL3rVRwD2iacibn6oDaaA7xzmibrmt9ibcaNR27Sl8sawD9ZUDgAenibnicpoecdGEaRubkMkImzibwjoddx/96</sourceheadurl><sourcetime>2025-07-21&#x20;12:01:00</sourcetime><srcMsgCreateTime>1753180583</srcMsgCreateTime><fromnewmsgid>4005550426972340798</fromnewmsgid><dataitemsource><hashusername>e6b73ff27565cff370109f8fcda0e65fdab070d2463b159472ee7e7f54f7d313</hashusername></dataitemsource></dataitem><dataitem datatype="1" dataid="60a9d586e3f24b8c1418bbb8db6db29b" datasourceid="5218936483281257360"><datadesc>发送 "听1" 到 "听8" 选择对应歌曲</datadesc><sourcename>💡 使用提示</sourcename><sourceheadurl>https://wx.qlogo.cn/mmhead/ver_1/rTPg4GeXEic6oQSZ2jcAI6ic6JEhFtYT40Eu3rUIjZpTKTATSgCEiaMJlUuFibkfxN4IpDCv0qxCA5WgowqT0BGRTQAVeDTn1PTGj1LUYbl2bRuJiaCb1ukH4e1TPIibyUiaX6hFKgria9aX7NM5SJFHDq3pBA/132</sourceheadurl><sourcetime>2025-07-21&#x20;12:02:00</sourcetime><srcMsgCreateTime>**********</srcMsgCreateTime><fromnewmsgid>5218936483281257360</fromnewmsgid><dataitemsource><hashusername>8f57acad5c854c4004fd00435c305ac427418e4ac2f7816798d3db2db6eff1eb</hashusername></dataitemsource></dataitem></datalist><favcreatetime>1753180553258</favcreatetime></recordinfo>]]></recorditem><appattach>    <totallen>0</totallen>    <attachid/>    <emoticonmd5/>    <fileext/>    <aeskey/></appattach><extinfo/><sourceusername/><sourcedisplayname/><thumburl/><md5/></appmsg><fromusername>wxid_4usgcju5ey9q29</fromusername><scene>0</scene><appinfo>    <version>1</version>    <appname></appname></appinfo><commenturl/>
2025-07-22 18:35:53 | DEBUG | 处理消息内容: '搜歌 小爱'
2025-07-22 18:35:53 | DEBUG | 消息内容 '搜歌 小爱' 不匹配任何命令，忽略
2025-07-22 18:36:32 | DEBUG | 收到消息: {'MsgId': 1281631042, 'FromUserName': {'string': '48097389945@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 47, 'Content': {'string': 'wxid_jegyk4i3v7zg22:\n<msg><emoji fromusername="wxid_jegyk4i3v7zg22" tousername="48097389945@chatroom" type="2" idbuffer="media:0_0" md5="fc6f96a2c164bfafa3035dc86c50ee0a" len="15085" productid="" androidmd5="fc6f96a2c164bfafa3035dc86c50ee0a" androidlen="15085" s60v3md5="fc6f96a2c164bfafa3035dc86c50ee0a" s60v3len="15085" s60v5md5="fc6f96a2c164bfafa3035dc86c50ee0a" s60v5len="15085" cdnurl="http://wxapp.tc.qq.com/262/20304/stodownload?m=fc6f96a2c164bfafa3035dc86c50ee0a&amp;filekey=30340201010420301e020201060402535a0410fc6f96a2c164bfafa3035dc86c50ee0a02023aed040d00000004627466730000000131&amp;hy=SZ&amp;storeid=32303231303632353132313230383030306139646333336333306630306333363133356630393030303030313036&amp;bizid=1023" designerid="" thumburl="" encrypturl="http://wxapp.tc.qq.com/262/20304/stodownload?m=21fd37523a3bd1fa10c4851b77392e5c&amp;filekey=30340201010420301e020201060402535a041021fd37523a3bd1fa10c4851b77392e5c02023af0040d00000004627466730000000131&amp;hy=SZ&amp;storeid=32303231303632353132313230383030306430383838336333306630306362633338356630393030303030313036&amp;bizid=1023" aeskey="65c3e647eac3b5f32e31222f18340fc1" externurl="http://wxapp.tc.qq.com/262/20304/stodownload?m=b99f3505aa55860aadc2a49cf81acf2b&amp;filekey=30340201010420301e020201060402535a0410b99f3505aa55860aadc2a49cf81acf2b02021930040d00000004627466730000000131&amp;hy=SZ&amp;storeid=32303231303632353132313230383030306564623230336333306630306363653539356630393030303030313036&amp;bizid=1023" externmd5="41fec80a408cc4ab8ba3683f5dda7774" width="39" height="53" tpurl="" tpauthkey="" attachedtext="" attachedtextcolor="" lensid="" emojiattr="" linkid="" desc=""></emoji><gameext type="0" content="0"></gameext><extcommoninfo></extcommoninfo></msg>'}, 'Status': 3, 'ImgStatus': 2, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1753180605, 'MsgSource': '<msgsource>\n\t<silence>0</silence>\n\t<membercount>62</membercount>\n\t<signature>N0_V1_d8b5lKzm|v1_jh3raCdc</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'PushContent': '阿尼亚与她在群聊中发了一个表情', 'NewMsgId': 1145590358102602979, 'MsgSeq': 871392654}
2025-07-22 18:36:32 | INFO | 收到表情消息: 消息ID:1281631042 来自:48097389945@chatroom 发送人:wxid_jegyk4i3v7zg22 MD5:fc6f96a2c164bfafa3035dc86c50ee0a 大小:15085
2025-07-22 18:36:33 | DEBUG | [DoubaoImageRecognition] 缓存表情消息: 1145590358102602979
2025-07-22 18:36:47 | DEBUG | 收到消息: {'MsgId': 1881440706, 'FromUserName': {'string': '47442567074@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 1, 'Content': {'string': 'wxid_p5j3x2tgfq9n12:\n听8'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1753180620, 'MsgSource': '<msgsource>\n\t<pua>1</pua>\n\t<eggIncluded>1</eggIncluded>\n\t<silence>0</silence>\n\t<membercount>494</membercount>\n\t<signature>N0_V1_iF1fDTOj|v1_Z97YoZUv</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'PushContent': '大极霸 : 听8', 'NewMsgId': 3300192022687417971, 'MsgSeq': 871392655}
2025-07-22 18:36:47 | INFO | 收到文本消息: 消息ID:1881440706 来自:47442567074@chatroom 发送人:wxid_p5j3x2tgfq9n12 @:[] 内容:听8
2025-07-22 18:36:47 | DEBUG | [Music] 处理选择音乐命令，用户: wxid_p5j3x2tgfq9n12, 选择序号: 8
2025-07-22 18:36:47 | DEBUG | [Music] 用户 wxid_p5j3x2tgfq9n12 没有搜索记录
2025-07-22 18:36:48 | INFO | 发送文字消息: 对方wxid:47442567074@chatroom at:['wxid_p5j3x2tgfq9n12'] 内容:@『k』 ❌请先搜索歌曲！
2025-07-22 18:36:48 | DEBUG | 处理消息内容: '听8'
2025-07-22 18:36:48 | DEBUG | 消息内容 '听8' 不匹配任何命令，忽略
2025-07-22 18:36:52 | DEBUG | 收到消息: {'MsgId': **********, 'FromUserName': {'string': '47442567074@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 1, 'Content': {'string': 'xiaomaochong:\n搜歌 小爱'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1753180625, 'MsgSource': '<msgsource>\n\t<pua>1</pua>\n\t<silence>0</silence>\n\t<membercount>494</membercount>\n\t<signature>N0_V1_V9l//9UF|v1_jBJAqH0P</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'PushContent': '小爱 : 搜歌 小爱', 'NewMsgId': 5562113464462536045, 'MsgSeq': 871392658}
2025-07-22 18:36:52 | INFO | 收到文本消息: 消息ID:********** 来自:47442567074@chatroom 发送人:xiaomaochong @:[] 内容:搜歌 小爱
2025-07-22 18:36:53 | DEBUG | [Music] 成功获取发送者 xiaomaochong 的头像: https://wx.qlogo.cn/mmhead/ver_1/A0q8X42libPrLtZNHs0pa3icRpsf0QvrcoSNVdylZBUJhPzdKkpcsxUp25ql6XtFiaMcEle2XxmReZUZfqkT6VEicOE4icNNr2w9wxEBxLtGFA9UGnqjcvjBu6WfCicnARSzAu/132
2025-07-22 18:36:53 | INFO | 发送app消息: 对方wxid:47442567074@chatroom 类型:19 xml:<appmsg appid="" sdkver="0"><title>🎵 小爱 - 搜索结果</title><des>🎵 音乐助手: 为您找到 "小爱" 的相关歌曲：🎶 搜索结果: 1、小爱 -- 李佳思2、大城小爱 -- 王力宏3、大城小爱 (Live) -- 梓渝4、小爱 -- 大柯5、小爱 -- 吴雨霏6、小爱 -- 贯诗钦7、小城小爱 -- Uu (刘梦妤)8、大城小爱 -- SomeLove💡 使用提示: 发送 "听1" 到 "听8" 选择对应歌曲</des><action>view</action><type>19</type><showtype>0</showtype><content/><url>https://support.weixin.qq.com/cgi-bin/mmsupport-bin/readtemplate?t=page/favorite_record__w_unsupport&amp;from=singlemessage&amp;isappinstalled=0</url><lowurl/><dataurl/><lowdataurl/><recorditem><![CDATA[<recordinfo><title>🎵 音乐搜索结果</title><desc>🎵 音乐助手:&#x20;为您找到 "小爱" 的相关歌曲：&#x0A;🎶 搜索结果:&#x20;1、小爱 -- 李佳思2、大城小爱 -- 王力宏3、大城小爱 (Live) -- 梓渝4、小爱 -- 大柯5、小爱 -- 吴雨霏6、小爱 -- 贯诗钦7、小城小爱 -- Uu (刘梦妤)8、大城小爱 -- SomeLove&#x0A;💡 使用提示:&#x20;发送 "听1" 到 "听8" 选择对应歌曲</desc><datalist count="3"><dataitem datatype="1" dataid="3e36dc30ca1b9934d1cb8a5c4a8104ce" datasourceid="8984642510053410891"><datadesc>为您找到 "小爱" 的相关歌曲：</datadesc><sourcename>🎵 音乐助手</sourcename><sourceheadurl>https://wx.qlogo.cn/mmhead/ver_1/A0q8X42libPrLtZNHs0pa3icRpsf0QvrcoSNVdylZBUJhPzdKkpcsxUp25ql6XtFiaMcEle2XxmReZUZfqkT6VEicOE4icNNr2w9wxEBxLtGFA9UGnqjcvjBu6WfCicnARSzAu/132</sourceheadurl><sourcetime>2025-07-21&#x20;12:00:00</sourcetime><srcMsgCreateTime>**********</srcMsgCreateTime><fromnewmsgid>8984642510053410891</fromnewmsgid><dataitemsource><hashusername>47117a242116be9ab5e02f7331cd04041140f0caf32a9bedff8b44eb7e0c1cb7</hashusername></dataitemsource></dataitem><dataitem datatype="1" dataid="18c033c927ae5527ff3e4860c96aa345" datasourceid="7512198816760245181"><datadesc>1、小爱 -- 李佳思2、大城小爱 -- 王力宏3、大城小爱 (Live) -- 梓渝4、小爱 -- 大柯5、小爱 -- 吴雨霏6、小爱 -- 贯诗钦7、小城小爱 -- Uu (刘梦妤)8、大城小爱 -- SomeLove</datadesc><sourcename>🎶 搜索结果</sourcename><sourceheadurl>https://wx.qlogo.cn/mmhead/ver_1/PCUwVKYvh0icQrEmB5CiaoyoPaAY9X93CtB0OeA6QmOUml3ljscUgvL3rVRwD2iacibn6oDaaA7xzmibrmt9ibcaNR27Sl8sawD9ZUDgAenibnicpoecdGEaRubkMkImzibwjoddx/96</sourceheadurl><sourcetime>2025-07-21&#x20;12:01:00</sourcetime><srcMsgCreateTime>1753180643</srcMsgCreateTime><fromnewmsgid>7512198816760245181</fromnewmsgid><dataitemsource><hashusername>e6b73ff27565cff370109f8fcda0e65fdab070d2463b159472ee7e7f54f7d313</hashusername></dataitemsource></dataitem><dataitem datatype="1" dataid="60a9d586e3f24b8c1418bbb8db6db29b" datasourceid="5687335250417458665"><datadesc>发送 "听1" 到 "听8" 选择对应歌曲</datadesc><sourcename>💡 使用提示</sourcename><sourceheadurl>https://wx.qlogo.cn/mmhead/ver_1/rTPg4GeXEic6oQSZ2jcAI6ic6JEhFtYT40Eu3rUIjZpTKTATSgCEiaMJlUuFibkfxN4IpDCv0qxCA5WgowqT0BGRTQAVeDTn1PTGj1LUYbl2bRuJiaCb1ukH4e1TPIibyUiaX6hFKgria9aX7NM5SJFHDq3pBA/132</sourceheadurl><sourcetime>2025-07-21&#x20;12:02:00</sourcetime><srcMsgCreateTime>1753180673</srcMsgCreateTime><fromnewmsgid>5687335250417458665</fromnewmsgid><dataitemsource><hashusername>8f57acad5c854c4004fd00435c305ac427418e4ac2f7816798d3db2db6eff1eb</hashusername></dataitemsource></dataitem></datalist><favcreatetime>**********465</favcreatetime></recordinfo>]]></recorditem><appattach>    <totallen>0</totallen>    <attachid/>    <emoticonmd5/>    <fileext/>    <aeskey/></appattach><extinfo/><sourceusername/><sourcedisplayname/><thumburl/><md5/></appmsg><fromusername>wxid_4usgcju5ey9q29</fromusername><scene>0</scene><appinfo>    <version>1</version>    <appname></appname></appinfo><commenturl/>
2025-07-22 18:36:53 | DEBUG | 处理消息内容: '搜歌 小爱'
2025-07-22 18:36:53 | DEBUG | 消息内容 '搜歌 小爱' 不匹配任何命令，忽略
2025-07-22 18:37:07 | DEBUG | 收到消息: {'MsgId': 627049483, 'FromUserName': {'string': '47442567074@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 1, 'Content': {'string': 'xiaomaochong:\n6[破涕为笑]'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1753180640, 'MsgSource': '<msgsource>\n\t<pua>1</pua>\n\t<silence>0</silence>\n\t<membercount>494</membercount>\n\t<signature>N0_V1_3ddzKnrJ|v1_/95wQ3Wk</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'PushContent': '小爱 : 6[破涕为笑]', 'NewMsgId': 3476910057148133410, 'MsgSeq': 871392661}
2025-07-22 18:37:07 | INFO | 收到文本消息: 消息ID:627049483 来自:47442567074@chatroom 发送人:xiaomaochong @:[] 内容:6[破涕为笑]
2025-07-22 18:37:07 | DEBUG | 处理消息内容: '6[破涕为笑]'
2025-07-22 18:37:07 | DEBUG | 消息内容 '6[破涕为笑]' 不匹配任何命令，忽略
2025-07-22 18:37:16 | DEBUG | 收到消息: {'MsgId': 317570992, 'FromUserName': {'string': '47442567074@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 1, 'Content': {'string': 'xiaomaochong:\n听1'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1753180649, 'MsgSource': '<msgsource>\n\t<pua>1</pua>\n\t<silence>0</silence>\n\t<membercount>494</membercount>\n\t<signature>N0_V1_NDSsL8L3|v1_1G/mpmLo</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'PushContent': '小爱 : 听1', 'NewMsgId': 6819389359621059345, 'MsgSeq': 871392662}
2025-07-22 18:37:16 | INFO | 收到文本消息: 消息ID:317570992 来自:47442567074@chatroom 发送人:xiaomaochong @:[] 内容:听1
2025-07-22 18:37:16 | DEBUG | [Music] 处理选择音乐命令，用户: xiaomaochong, 选择序号: 1
2025-07-22 18:37:16 | DEBUG | [Music] 用户搜索数据: {'result': '小爱', 'timestamp': **********.0755217, 'group_id': '47442567074@chatroom'}
2025-07-22 18:37:16 | DEBUG | [Music] 开始获取歌曲，关键词: 小爱, 序号: 1
2025-07-22 18:37:18 | DEBUG | [Music] API请求成功，状态码: 200
2025-07-22 18:37:18 | DEBUG | [Music] API原始响应: {
    "code": 200,
    "msg": "若音频链接为空或只能播放一半请稍后再用",
    "title": "小爱",
    "singer": "李佳思",
    "cover": "http://imge.kugou.com/stdmusic/400/20250417/20250417203222425923.jpg",
    "link": "https://www.kugou.com/song/#hash=0A29AD14A47D15EA946D69503FA598CC",
    "music_url": "https://er-sycdn.kuwo.cn/f146b385432a7f7d9278b762673159ff/687f69ec/resource/30106/trackmedia/F000000RxNgF3xdxE1.flac?src=无损音质 FLAC_23.42 MB?src=flac_23.42 MB?from=longzhu_api",
    "lyrics": "﻿[id:$00000000]\r\n[ar:李佳思]\r\n[ti:小爱]\r\n[by:]\r\n[hash:0a29ad14a47d15ea946d69503fa598cc]\r\n[al:]\r\n[sign:]\r\n[qq:]\r\n[total:213364]\r\n[offset:0]\r\n[00:00.26]李佳思 - 小爱\r\n[00:01.22]作词：贯诗钦\r\n[00:02.08]作曲：贯诗钦\r\n[00:02.94]编曲：王柏鸿\r\n[00:16.70]Hey could u hear 我的声音\r\n[00:20.74]会不会在夏季带来甜蜜\r\n[00:24.94]I just want to know\r\n[00:27.07]I just want to see\r\n[00:29.19]I just want you be with me\r\n[00:33.36]Hey could u bring me more happy\r\n[00:37.61]我要 a bit of love 可不可以\r\n[00:41.86]Oh my sweetheart\r\n[00:43.27]Oh my darling boy\r\n[00:45.55]Oh 请叫我 baby 请不要离去\r\n[00:50.10]我希望 u and me 一起\r\n[00:54.38]看 sea 看海鸥看风景\r\n[00:58.63]任时间 gone away\r\n[01:00.75]我们 still 一起\r\n[01:03.08]You worth everything for me\r\n[01:07.02]你希望 u and me 一起\r\n[01:11.32]背包踏访圣托里尼\r\n[01:15.41]我们拍部时光电影\r\n[01:19.89]Just two of us in this movie\r\n[01:40.95]Hey could u hear 我的声音\r\n[01:44.95]会不会在夏季带来甜蜜\r\n[01:49.20]I just want to know\r\n[01:51.18]I just want to see\r\n[01:53.41]I just want you be with me\r\n[01:57.62]Hey could u bring me more happy\r\n[02:01.87]我要 a bit of love 可不可以\r\n[02:06.07]Oh my sweetheart\r\n[02:07.64]Oh my darling boy\r\n[02:09.71]Oh 请叫我 baby 请不要离去\r\n[02:14.32]我希望 u and me 一起\r\n[02:18.67]看 sea 看海鸥看风景\r\n[02:22.86]任时间 gone away\r\n[02:24.99]我们 still 一起\r\n[02:27.32]You worth everything for me\r\n[02:31.26]你希望 u and me 一起\r\n[02:35.51]背包踏访圣托里尼\r\n[02:39.71]我们拍部时光电影\r\n[02:44.09]Just two of us in this movie\r\n[02:48.09]我希望 u and me 一起\r\n[02:52.34]看 sea 看海鸥看风景\r\n[02:56.49]任时间 gone away\r\n[02:58.61]我们 still 一起\r\n[03:00.99]You worth everything for me\r\n[03:05.22]你希望 u and me 一起\r\n[03:09.16]背包踏访圣托里尼\r\n[03:13.36]我们拍部时光电影\r\n[03:17.89]Just two of us in this movie\r\n"
}
2025-07-22 18:37:18 | DEBUG | [Music] API响应JSON解析成功: {'code': 200, 'msg': '若音频链接为空或只能播放一半请稍后再用', 'title': '小爱', 'singer': '李佳思', 'cover': 'http://imge.kugou.com/stdmusic/400/20250417/20250417203222425923.jpg', 'link': 'https://www.kugou.com/song/#hash=0A29AD14A47D15EA946D69503FA598CC', 'music_url': 'https://er-sycdn.kuwo.cn/f146b385432a7f7d9278b762673159ff/687f69ec/resource/30106/trackmedia/F000000RxNgF3xdxE1.flac?src=无损音质 FLAC_23.42 MB?src=flac_23.42 MB?from=longzhu_api', 'lyrics': '\ufeff[id:$00000000]\r\n[ar:李佳思]\r\n[ti:小爱]\r\n[by:]\r\n[hash:0a29ad14a47d15ea946d69503fa598cc]\r\n[al:]\r\n[sign:]\r\n[qq:]\r\n[total:213364]\r\n[offset:0]\r\n[00:00.26]李佳思 - 小爱\r\n[00:01.22]作词：贯诗钦\r\n[00:02.08]作曲：贯诗钦\r\n[00:02.94]编曲：王柏鸿\r\n[00:16.70]Hey could u hear 我的声音\r\n[00:20.74]会不会在夏季带来甜蜜\r\n[00:24.94]I just want to know\r\n[00:27.07]I just want to see\r\n[00:29.19]I just want you be with me\r\n[00:33.36]Hey could u bring me more happy\r\n[00:37.61]我要 a bit of love 可不可以\r\n[00:41.86]Oh my sweetheart\r\n[00:43.27]Oh my darling boy\r\n[00:45.55]Oh 请叫我 baby 请不要离去\r\n[00:50.10]我希望 u and me 一起\r\n[00:54.38]看 sea 看海鸥看风景\r\n[00:58.63]任时间 gone away\r\n[01:00.75]我们 still 一起\r\n[01:03.08]You worth everything for me\r\n[01:07.02]你希望 u and me 一起\r\n[01:11.32]背包踏访圣托里尼\r\n[01:15.41]我们拍部时光电影\r\n[01:19.89]Just two of us in this movie\r\n[01:40.95]Hey could u hear 我的声音\r\n[01:44.95]会不会在夏季带来甜蜜\r\n[01:49.20]I just want to know\r\n[01:51.18]I just want to see\r\n[01:53.41]I just want you be with me\r\n[01:57.62]Hey could u bring me more happy\r\n[02:01.87]我要 a bit of love 可不可以\r\n[02:06.07]Oh my sweetheart\r\n[02:07.64]Oh my darling boy\r\n[02:09.71]Oh 请叫我 baby 请不要离去\r\n[02:14.32]我希望 u and me 一起\r\n[02:18.67]看 sea 看海鸥看风景\r\n[02:22.86]任时间 gone away\r\n[02:24.99]我们 still 一起\r\n[02:27.32]You worth everything for me\r\n[02:31.26]你希望 u and me 一起\r\n[02:35.51]背包踏访圣托里尼\r\n[02:39.71]我们拍部时光电影\r\n[02:44.09]Just two of us in this movie\r\n[02:48.09]我希望 u and me 一起\r\n[02:52.34]看 sea 看海鸥看风景\r\n[02:56.49]任时间 gone away\r\n[02:58.61]我们 still 一起\r\n[03:00.99]You worth everything for me\r\n[03:05.22]你希望 u and me 一起\r\n[03:09.16]背包踏访圣托里尼\r\n[03:13.36]我们拍部时光电影\r\n[03:17.89]Just two of us in this movie\r\n'}
2025-07-22 18:37:18 | DEBUG | [Music] 成功解析歌曲信息: 标题=小爱, 歌手=李佳思
2025-07-22 18:37:18 | DEBUG | [Music] 开始生成音乐XML
2025-07-22 18:37:18 | DEBUG | [Music] 音乐XML生成成功，准备发送
2025-07-22 18:37:19 | INFO | 发送app消息: 对方wxid:47442567074@chatroom 类型:3 xml:<appmsg appid="wx485a97c844086dc9" sdkver="0"><title>小爱</title><des>李佳思</des><action>view</action><type>3</type><showtype>0</showtype><content/><url>https://weixin.qq.com</url><dataurl>https://er-sycdn.kuwo.cn/f146b385432a7f7d9278b762673159ff/687f69ec/resource/30106/trackmedia/F000000RxNgF3xdxE1.flac?src=无损音质 FLAC_23.42 MB?src=flac_23.42 MB?from=longzhu_api</dataurl><lowurl>https://weixin.qq.com</lowurl><lowdataurl>https://er-sycdn.kuwo.cn/f146b385432a7f7d9278b762673159ff/687f69ec/resource/30106/trackmedia/F000000RxNgF3xdxE1.flac?src=无损音质 FLAC_23.42 MB?src=flac_23.42 MB?from=longzhu_api</lowdataurl><recorditem/><thumburl>http://imge.kugou.com/stdmusic/400/20250417/20250417203222425923.jpg</thumburl><messageaction/><laninfo/><extinfo/><sourceusername/><sourcedisplayname/><songlyric>﻿[id:$00000000]
[ar:李佳思]
[ti:小爱]
[by:]
[hash:0a29ad14a47d15ea946d69503fa598cc]
[al:]
[sign:]
[qq:]
[total:213364]
[offset:0]
[00:00.26]李佳思 - 小爱
[00:01.22]作词：贯诗钦
[00:02.08]作曲：贯诗钦
[00:02.94]编曲：王柏鸿
[00:16.70]Hey could u hear 我的声音
[00:20.74]会不会在夏季带来甜蜜
[00:24.94]I just want to know
[00:27.07]I just want to see
[00:29.19]I just want you be with me
[00:33.36]Hey could u bring me more happy
[00:37.61]我要 a bit of love 可不可以
[00:41.86]Oh my sweetheart
[00:43.27]Oh my darling boy
[00:45.55]Oh 请叫我 baby 请不要离去
[00:50.10]我希望 u and me 一起
[00:54.38]看 sea 看海鸥看风景
[00:58.63]任时间 gone away
[01:00.75]我们 still 一起
[01:03.08]You worth everything for me
[01:07.02]你希望 u and me 一起
[01:11.32]背包踏访圣托里尼
[01:15.41]我们拍部时光电影
[01:19.89]Just two of us in this movie
[01:40.95]Hey could u hear 我的声音
[01:44.95]会不会在夏季带来甜蜜
[01:49.20]I just want to know
[01:51.18]I just want to see
[01:53.41]I just want you be with me
[01:57.62]Hey could u bring me more happy
[02:01.87]我要 a bit of love 可不可以
[02:06.07]Oh my sweetheart
[02:07.64]Oh my darling boy
[02:09.71]Oh 请叫我 baby 请不要离去
[02:14.32]我希望 u and me 一起
[02:18.67]看 sea 看海鸥看风景
[02:22.86]任时间 gone away
[02:24.99]我们 still 一起
[02:27.32]You worth everything for me
[02:31.26]你希望 u and me 一起
[02:35.51]背包踏访圣托里尼
[02:39.71]我们拍部时光电影
[02:44.09]Just two of us in this movie
[02:48.09]我希望 u and me 一起
[02:52.34]看 sea 看海鸥看风景
[02:56.49]任时间 gone away
[02:58.61]我们 still 一起
[03:00.99]You worth everything for me
[03:05.22]你希望 u and me 一起
[03:09.16]背包踏访圣托里尼
[03:13.36]我们拍部时光电影
[03:17.89]Just two of us in this movie
</songlyric><commenturl/><appattach><totallen>0</totallen><attachid/><emoticonmd5/><fileext/><aeskey/></appattach><webviewshared><publisherId/><publisherReqId>0</publisherReqId></webviewshared><weappinfo><pagepath/><username/><appid/><appservicetype>0</appservicetype></weappinfo><websearch/><songalbumurl>http://imge.kugou.com/stdmusic/400/20250417/20250417203222425923.jpg</songalbumurl></appmsg><fromusername>wxid_4usgcju5ey9q29</fromusername><scene>0</scene><appinfo><version>1</version><appname></appname></appinfo><commenturl/>
2025-07-22 18:37:19 | DEBUG | [Music] 音乐消息发送完成
2025-07-22 18:37:19 | DEBUG | 处理消息内容: '听1'
2025-07-22 18:37:19 | DEBUG | 消息内容 '听1' 不匹配任何命令，忽略
2025-07-22 18:37:38 | DEBUG | 收到消息: {'MsgId': 1648988533, 'FromUserName': {'string': '47442567074@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 1, 'Content': {'string': 'xiaomaochong:\n必须你参与下是吧'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1753180671, 'MsgSource': '<msgsource>\n\t<pua>1</pua>\n\t<silence>0</silence>\n\t<membercount>494</membercount>\n\t<signature>N0_V1_SESGMSwj|v1_2SMR7FNn</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'PushContent': '小爱 : 必须你参与下是吧', 'NewMsgId': 5008427879096415665, 'MsgSeq': 871392665}
2025-07-22 18:37:38 | INFO | 收到文本消息: 消息ID:1648988533 来自:47442567074@chatroom 发送人:xiaomaochong @:[] 内容:必须你参与下是吧
2025-07-22 18:37:38 | DEBUG | 处理消息内容: '必须你参与下是吧'
2025-07-22 18:37:38 | DEBUG | 消息内容 '必须你参与下是吧' 不匹配任何命令，忽略
2025-07-22 18:37:50 | DEBUG | 收到消息: {'MsgId': 1189594905, 'FromUserName': {'string': '47442567074@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 1, 'Content': {'string': 'wxid_ubbh6q832tcs21:\n[旺柴]'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1753180683, 'MsgSource': '<msgsource>\n\t<pua>1</pua>\n\t<eggIncluded>1</eggIncluded>\n\t<silence>0</silence>\n\t<membercount>494</membercount>\n\t<signature>N0_V1_QyuS/iFh|v1_RDzQbhqp</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'PushContent': '郭 : [旺柴]', 'NewMsgId': 7697810741870688131, 'MsgSeq': 871392666}
2025-07-22 18:37:50 | INFO | 收到表情消息: 消息ID:1189594905 来自:47442567074@chatroom 发送人:wxid_ubbh6q832tcs21 @:[] 内容:[旺柴]
2025-07-22 18:37:50 | DEBUG | [DoubaoImageRecognition] 缓存表情消息: 7697810741870688131
2025-07-22 18:38:13 | DEBUG | 收到消息: {'MsgId': 222167052, 'FromUserName': {'string': '47442567074@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 49, 'Content': {'string': 'xiaomaochong:\n<?xml version="1.0"?>\n<msg>\n\t<appmsg appid="" sdkver="0">\n\t\t<title>卡片小爱</title>\n\t\t<des />\n\t\t<username />\n\t\t<action>view</action>\n\t\t<type>57</type>\n\t\t<showtype>0</showtype>\n\t\t<content />\n\t\t<url />\n\t\t<lowurl />\n\t\t<forwardflag>0</forwardflag>\n\t\t<dataurl />\n\t\t<lowdataurl />\n\t\t<contentattr>0</contentattr>\n\t\t<streamvideo>\n\t\t\t<streamvideourl />\n\t\t\t<streamvideototaltime>0</streamvideototaltime>\n\t\t\t<streamvideotitle />\n\t\t\t<streamvideowording />\n\t\t\t<streamvideoweburl />\n\t\t\t<streamvideothumburl />\n\t\t\t<streamvideoaduxinfo />\n\t\t\t<streamvideopublishid />\n\t\t</streamvideo>\n\t\t<canvasPageItem>\n\t\t\t<canvasPageXml><![CDATA[]]></canvasPageXml>\n\t\t</canvasPageItem>\n\t\t<refermsg>\n\t\t\t<type>49</type>\n\t\t\t<svrid>8464529937443046887</svrid>\n\t\t\t<fromusr>47442567074@chatroom</fromusr>\n\t\t\t<chatusr>wxid_4usgcju5ey9q29</chatusr>\n\t\t\t<displayname>瑶瑶</displayname>\n\t\t\t<msgsource>&lt;msgsource&gt;\n\t&lt;bizflag&gt;0&lt;/bizflag&gt;\n\t&lt;sec_msg_node&gt;\n\t\t&lt;uuid&gt;16f7ad10530a751a1b03e6665befabb9_&lt;/uuid&gt;\n\t\t&lt;risk-file-flag /&gt;\n\t\t&lt;risk-file-md5-list /&gt;\n\t&lt;/sec_msg_node&gt;\n\t&lt;silence&gt;1&lt;/silence&gt;\n\t&lt;membercount&gt;494&lt;/membercount&gt;\n\t&lt;signature&gt;N0_V1_x2gfTOP+|v1_wEIbgXpL&lt;/signature&gt;\n\t&lt;tmp_node&gt;\n\t\t&lt;publisher-id&gt;&lt;/publisher-id&gt;\n\t&lt;/tmp_node&gt;\n&lt;/msgsource&gt;\n</msgsource>\n\t\t\t<content>&lt;?xml version="1.0"?&gt;\n&lt;msg&gt;\n\t&lt;appmsg appid="wx485a97c844086dc9" sdkver="0"&gt;\n\t\t&lt;title&gt;小爱&lt;/title&gt;\n\t\t&lt;des&gt;李佳思&lt;/des&gt;\n\t\t&lt;action&gt;view&lt;/action&gt;\n\t\t&lt;type&gt;3&lt;/type&gt;\n\t\t&lt;showtype&gt;0&lt;/showtype&gt;\n\t\t&lt;content /&gt;\n\t\t&lt;url&gt;https://weixin.qq.com&lt;/url&gt;\n\t\t&lt;dataurl&gt;https://er-sycdn.kuwo.cn/f146b385432a7f7d9278b762673159ff/687f69ec/resource/30106/trackmedia/F000000RxNgF3xdxE1.flac?src=无损音质 FLAC_23.42 MB?src=flac_23.42 MB?from=longzhu_api&lt;/dataurl&gt;\n\t\t&lt;lowurl&gt;https://weixin.qq.com&lt;/lowurl&gt;\n\t\t&lt;lowdataurl&gt;https://er-sycdn.kuwo.cn/f146b385432a7f7d9278b762673159ff/687f69ec/resource/30106/trackmedia/F000000RxNgF3xdxE1.flac?src=无损音质 FLAC_23.42 MB?src=flac_23.42 MB?from=longzhu_api&lt;/lowdataurl&gt;\n\t\t&lt;recorditem /&gt;\n\t\t&lt;thumburl&gt;http://imge.kugou.com/stdmusic/400/20250417/20250417203222425923.jpg&lt;/thumburl&gt;\n\t\t&lt;messageaction /&gt;\n\t\t&lt;laninfo /&gt;\n\t\t&lt;extinfo /&gt;\n\t\t&lt;sourceusername /&gt;\n\t\t&lt;sourcedisplayname /&gt;\n\t\t&lt;songlyric&gt;\ufeff[id:$00000000]\n[ar:李佳思]\n[ti:小爱]\n[by:]\n[hash:0a29ad14a47d15ea946d69503fa598cc]\n[al:]\n[sign:]\n[qq:]\n[total:213364]\n[offset:0]\n[00:00.26]李佳思 - 小爱\n[00:01.22]作词：贯诗钦\n[00:02.08]作曲：贯诗钦\n[00:02.94]编曲：王柏鸿\n[00:16.70]Hey could u hear 我的声音\n[00:20.74]会不会在夏季带来甜蜜\n[00:24.94]I just want to know\n[00:27.07]I just want to see\n[00:29.19]I just want you be with me\n[00:33.36]Hey could u bring me more happy\n[00:37.61]我要 a bit of love 可不可以\n[00:41.86]Oh my sweetheart\n[00:43.27]Oh my darling boy\n[00:45.55]Oh 请叫我 baby 请不要离去\n[00:50.10]我希望 u and me 一起\n[00:54.38]看 sea 看海鸥看风景\n[00:58.63]任时间 gone away\n[01:00.75]我们 still 一起\n[01:03.08]You worth everything for me\n[01:07.02]你希望 u and me 一起\n[01:11.32]背包踏访圣托里尼\n[01:15.41]我们拍部时光电影\n[01:19.89]Just two of us in this movie\n[01:40.95]Hey could u hear 我的声音\n[01:44.95]会不会在夏季带来甜蜜\n[01:49.20]I just want to know\n[01:51.18]I just want to see\n[01:53.41]I just want you be with me\n[01:57.62]Hey could u bring me more happy\n[02:01.87]我要 a bit of love 可不可以\n[02:06.07]Oh my sweetheart\n[02:07.64]Oh my darling boy\n[02:09.71]Oh 请叫我 baby 请不要离去\n[02:14.32]我希望 u and me 一起\n[02:18.67]看 sea 看海鸥看风景\n[02:22.86]任时间 gone away\n[02:24.99]我们 still 一起\n[02:27.32]You worth everything for me\n[02:31.26]你希望 u and me 一起\n[02:35.51]背包踏访圣托里尼\n[02:39.71]我们拍部时光电影\n[02:44.09]Just two of us in this movie\n[02:48.09]我希望 u and me 一起\n[02:52.34]看 sea 看海鸥看风景\n[02:56.49]任时间 gone away\n[02:58.61]我们 still 一起\n[03:00.99]You worth everything for me\n[03:05.22]你希望 u and me 一起\n[03:09.16]背包踏访圣托里尼\n[03:13.36]我们拍部时光电影\n[03:17.89]Just two of us in this movie\n&lt;/songlyric&gt;\n\t\t&lt;commenturl /&gt;\n\t\t&lt;appattach&gt;\n\t\t\t&lt;totallen&gt;0&lt;/totallen&gt;\n\t\t\t&lt;attachid /&gt;\n\t\t\t&lt;emoticonmd5&gt;&lt;/emoticonmd5&gt;\n\t\t\t&lt;fileext /&gt;\n\t\t\t&lt;aeskey&gt;&lt;/aeskey&gt;\n\t\t&lt;/appattach&gt;\n\t\t&lt;webviewshared&gt;\n\t\t\t&lt;publisherId /&gt;\n\t\t\t&lt;publisherReqId&gt;0&lt;/publisherReqId&gt;\n\t\t&lt;/webviewshared&gt;\n\t\t&lt;weappinfo&gt;\n\t\t\t&lt;pagepath /&gt;\n\t\t\t&lt;username /&gt;\n\t\t\t&lt;appid /&gt;\n\t\t\t&lt;appservicetype&gt;0&lt;/appservicetype&gt;\n\t\t&lt;/weappinfo&gt;\n\t\t&lt;websearch /&gt;\n\t\t&lt;songalbumurl&gt;http://imge.kugou.com/stdmusic/400/20250417/20250417203222425923.jpg&lt;/songalbumurl&gt;\n\t&lt;/appmsg&gt;\n\t&lt;fromusername&gt;wxid_4usgcju5ey9q29&lt;/fromusername&gt;\n\t&lt;scene&gt;0&lt;/scene&gt;\n\t&lt;appinfo&gt;\n\t\t&lt;version&gt;1&lt;/version&gt;\n\t\t&lt;appname /&gt;\n\t&lt;/appinfo&gt;\n\t&lt;commenturl /&gt;\n&lt;/msg&gt;\n</content>\n\t\t\t<strid />\n\t\t\t<createtime>1753180652</createtime>\n\t\t</refermsg>\n\t\t<appattach>\n\t\t\t<totallen>0</totallen>\n\t\t\t<attachid />\n\t\t\t<cdnattachurl />\n\t\t\t<emoticonmd5></emoticonmd5>\n\t\t\t<aeskey></aeskey>\n\t\t\t<fileext />\n\t\t\t<islargefilemsg>0</islargefilemsg>\n\t\t</appattach>\n\t\t<extinfo />\n\t\t<androidsource>0</androidsource>\n\t\t<thumburl />\n\t\t<mediatagname />\n\t\t<messageaction><![CDATA[]]></messageaction>\n\t\t<messageext><![CDATA[]]></messageext>\n\t\t<emoticongift>\n\t\t\t<packageflag>0</packageflag>\n\t\t\t<packageid />\n\t\t</emoticongift>\n\t\t<emoticonshared>\n\t\t\t<packageflag>0</packageflag>\n\t\t\t<packageid />\n\t\t</emoticonshared>\n\t\t<designershared>\n\t\t\t<designeruin>0</designeruin>\n\t\t\t<designername>null</designername>\n\t\t\t<designerrediretcturl><![CDATA[null]]></designerrediretcturl>\n\t\t</designershared>\n\t\t<emotionpageshared>\n\t\t\t<tid>0</tid>\n\t\t\t<title>null</title>\n\t\t\t<desc>null</desc>\n\t\t\t<iconUrl><![CDATA[null]]></iconUrl>\n\t\t\t<secondUrl>null</secondUrl>\n\t\t\t<pageType>0</pageType>\n\t\t\t<setKey>null</setKey>\n\t\t</emotionpageshared>\n\t\t<webviewshared>\n\t\t\t<shareUrlOriginal />\n\t\t\t<shareUrlOpen />\n\t\t\t<jsAppId />\n\t\t\t<publisherId />\n\t\t\t<publisherReqId />\n\t\t</webviewshared>\n\t\t<template_id />\n\t\t<md5 />\n\t\t<websearch>\n\t\t\t<rec_category>0</rec_category>\n\t\t\t<channelId>0</channelId>\n\t\t</websearch>\n\t\t<weappinfo>\n\t\t\t<username />\n\t\t\t<appid />\n\t\t\t<appservicetype>0</appservicetype>\n\t\t\t<secflagforsinglepagemode>0</secflagforsinglepagemode>\n\t\t\t<videopageinfo>\n\t\t\t\t<thumbwidth>0</thumbwidth>\n\t\t\t\t<thumbheight>0</thumbheight>\n\t\t\t\t<fromopensdk>0</fromopensdk>\n\t\t\t</videopageinfo>\n\t\t</weappinfo>\n\t\t<statextstr />\n\t\t<musicShareItem>\n\t\t\t<musicDuration>0</musicDuration>\n\t\t</musicShareItem>\n\t\t<finderLiveProductShare>\n\t\t\t<finderLiveID><![CDATA[]]></finderLiveID>\n\t\t\t<finderUsername><![CDATA[]]></finderUsername>\n\t\t\t<finderObjectID><![CDATA[]]></finderObjectID>\n\t\t\t<finderNonceID><![CDATA[]]></finderNonceID>\n\t\t\t<liveStatus><![CDATA[]]></liveStatus>\n\t\t\t<appId><![CDATA[]]></appId>\n\t\t\t<pagePath><![CDATA[]]></pagePath>\n\t\t\t<productId><![CDATA[]]></productId>\n\t\t\t<coverUrl><![CDATA[]]></coverUrl>\n\t\t\t<productTitle><![CDATA[]]></productTitle>\n\t\t\t<marketPrice><![CDATA[0]]></marketPrice>\n\t\t\t<sellingPrice><![CDATA[0]]></sellingPrice>\n\t\t\t<platformHeadImg><![CDATA[]]></platformHeadImg>\n\t\t\t<platformName><![CDATA[]]></platformName>\n\t\t\t<shopWindowId><![CDATA[]]></shopWindowId>\n\t\t\t<flashSalePrice><![CDATA[0]]></flashSalePrice>\n\t\t\t<flashSaleEndTime><![CDATA[0]]></flashSaleEndTime>\n\t\t\t<ecSource><![CDATA[]]></ecSource>\n\t\t\t<sellingPriceWording><![CDATA[]]></sellingPriceWording>\n\t\t\t<platformIconURL><![CDATA[]]></platformIconURL>\n\t\t\t<firstProductTagURL><![CDATA[]]></firstProductTagURL>\n\t\t\t<firstProductTagAspectRatioString><![CDATA[0.0]]></firstProductTagAspectRatioString>\n\t\t\t<secondProductTagURL><![CDATA[]]></secondProductTagURL>\n\t\t\t<secondProductTagAspectRatioString><![CDATA[0.0]]></secondProductTagAspectRatioString>\n\t\t\t<firstGuaranteeWording><![CDATA[]]></firstGuaranteeWording>\n\t\t\t<secondGuaranteeWording><![CDATA[]]></secondGuaranteeWording>\n\t\t\t<thirdGuaranteeWording><![CDATA[]]></thirdGuaranteeWording>\n\t\t\t<isPriceBeginShow>false</isPriceBeginShow>\n\t\t\t<lastGMsgID><![CDATA[]]></lastGMsgID>\n\t\t\t<promoterKey><![CDATA[]]></promoterKey>\n\t\t\t<discountWording><![CDATA[]]></discountWording>\n\t\t\t<priceSuffixDescription><![CDATA[]]></priceSuffixDescription>\n\t\t\t<showBoxItemStringList />\n\t\t</finderLiveProductShare>\n\t\t<finderOrder>\n\t\t\t<appID><![CDATA[]]></appID>\n\t\t\t<orderID><![CDATA[]]></orderID>\n\t\t\t<path><![CDATA[]]></path>\n\t\t\t<priceWording><![CDATA[]]></priceWording>\n\t\t\t<stateWording><![CDATA[]]></stateWording>\n\t\t\t<productImageURL><![CDATA[]]></productImageURL>\n\t\t\t<products><![CDATA[]]></products>\n\t\t\t<productsCount><![CDATA[0]]></productsCount>\n\t\t</finderOrder>\n\t\t<finderShopWindowShare>\n\t\t\t<finderUsername><![CDATA[]]></finderUsername>\n\t\t\t<avatar><![CDATA[]]></avatar>\n\t\t\t<nickname><![CDATA[]]></nickname>\n\t\t\t<commodityInStockCount><![CDATA[]]></commodityInStockCount>\n\t\t\t<appId><![CDATA[]]></appId>\n\t\t\t<path><![CDATA[]]></path>\n\t\t\t<appUsername><![CDATA[]]></appUsername>\n\t\t\t<query><![CDATA[]]></query>\n\t\t\t<liteAppId><![CDATA[]]></liteAppId>\n\t\t\t<liteAppPath><![CDATA[]]></liteAppPath>\n\t\t\t<liteAppQuery><![CDATA[]]></liteAppQuery>\n\t\t\t<platformTagURL><![CDATA[]]></platformTagURL>\n\t\t\t<saleWording><![CDATA[]]></saleWording>\n\t\t\t<lastGMsgID><![CDATA[]]></lastGMsgID>\n\t\t\t<profileTypeWording><![CDATA[]]></profileTypeWording>\n\t\t\t<reputationInfo>\n\t\t\t\t<hasReputationInfo>0</hasReputationInfo>\n\t\t\t\t<reputationScore>0</reputationScore>\n\t\t\t\t<reputationWording />\n\t\t\t\t<reputationTextColor />\n\t\t\t\t<reputationLevelWording />\n\t\t\t\t<reputationBackgroundColor />\n\t\t\t</reputationInfo>\n\t\t\t<productImageURLList />\n\t\t</finderShopWindowShare>\n\t\t<findernamecard>\n\t\t\t<username />\n\t\t\t<avatar><![CDATA[]]></avatar>\n\t\t\t<nickname />\n\t\t\t<auth_job />\n\t\t\t<auth_icon>0</auth_icon>\n\t\t\t<auth_icon_url />\n\t\t\t<ecSource><![CDATA[]]></ecSource>\n\t\t\t<lastGMsgID><![CDATA[]]></lastGMsgID>\n\t\t</findernamecard>\n\t\t<finderGuarantee>\n\t\t\t<scene><![CDATA[0]]></scene>\n\t\t</finderGuarantee>\n\t\t<directshare>0</directshare>\n\t\t<gamecenter>\n\t\t\t<namecard>\n\t\t\t\t<iconUrl />\n\t\t\t\t<name />\n\t\t\t\t<desc />\n\t\t\t\t<tail />\n\t\t\t\t<jumpUrl />\n\t\t\t</namecard>\n\t\t</gamecenter>\n\t\t<patMsg>\n\t\t\t<chatUser />\n\t\t\t<records>\n\t\t\t\t<recordNum>0</recordNum>\n\t\t\t</records>\n\t\t</patMsg>\n\t\t<secretmsg>\n\t\t\t<issecretmsg>0</issecretmsg>\n\t\t</secretmsg>\n\t\t<referfromscene>0</referfromscene>\n\t\t<gameshare>\n\t\t\t<liteappext>\n\t\t\t\t<liteappbizdata />\n\t\t\t\t<priority>0</priority>\n\t\t\t</liteappext>\n\t\t\t<appbrandext>\n\t\t\t\t<litegameinfo />\n\t\t\t\t<priority>-1</priority>\n\t\t\t</appbrandext>\n\t\t\t<gameshareid />\n\t\t\t<sharedata />\n\t\t\t<isvideo>0</isvideo>\n\t\t\t<duration>-1</duration>\n\t\t\t<isexposed>0</isexposed>\n\t\t\t<readtext />\n\t\t</gameshare>\n\t\t<mpsharetrace>\n\t\t\t<hasfinderelement>0</hasfinderelement>\n\t\t\t<lastgmsgid />\n\t\t</mpsharetrace>\n\t\t<wxgamecard>\n\t\t\t<framesetname />\n\t\t\t<mbcarddata />\n\t\t\t<minpkgversion />\n\t\t\t<mbcardheight>0</mbcardheight>\n\t\t\t<isoldversion>0</isoldversion>\n\t\t</wxgamecard>\n\t\t<liteapp>\n\t\t\t<id>null</id>\n\t\t\t<path />\n\t\t\t<query />\n\t\t</liteapp>\n\t</appmsg>\n\t<fromusername>xiaomaochong</fromusername>\n\t<scene>0</scene>\n\t<appinfo>\n\t\t<version>1</version>\n\t\t<appname />\n\t</appinfo>\n\t<commenturl />\n</msg>\n'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1753180706, 'MsgSource': '<msgsource>\n\t<alnode>\n\t\t<fr>4</fr>\n\t</alnode>\n\t<sec_msg_node>\n\t\t<uuid>16f7ad10530a751a1b03e6665befabb9_</uuid>\n\t\t<risk-file-flag />\n\t\t<risk-file-md5-list />\n\t</sec_msg_node>\n\t<silence>0</silence>\n\t<membercount>494</membercount>\n\t<signature>N0_V1_3GoXnXRA|v1_I2j4ceqn</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'PushContent': '小爱 : 卡片小爱', 'NewMsgId': 8807494503295469507, 'MsgSeq': 871392667}
2025-07-22 18:38:13 | DEBUG | 从群聊消息中提取发送者: xiaomaochong
2025-07-22 18:38:13 | DEBUG | 使用已解析的XML处理引用消息
2025-07-22 18:38:13 | INFO | 收到引用消息: 消息ID:222167052 来自:47442567074@chatroom 发送人:xiaomaochong 内容:卡片小爱 引用类型:49
2025-07-22 18:38:13 | INFO | [DouBaoImageToImage] 收到引用消息: 卡片小爱
2025-07-22 18:38:13 | INFO | [TimerTask] 收到引用消息调试信息:
2025-07-22 18:38:13 | INFO |   - 消息内容: 卡片小爱
2025-07-22 18:38:13 | INFO |   - 群组ID: 47442567074@chatroom
2025-07-22 18:38:13 | INFO |   - 发送人: xiaomaochong
2025-07-22 18:38:13 | INFO |   - 引用信息: {'MsgType': 49, 'Content': '<?xml version="1.0"?>\n<msg>\n\t<appmsg appid="wx485a97c844086dc9" sdkver="0">\n\t\t<title>小爱</title>\n\t\t<des>李佳思</des>\n\t\t<action>view</action>\n\t\t<type>3</type>\n\t\t<showtype>0</showtype>\n\t\t<content />\n\t\t<url>https://weixin.qq.com</url>\n\t\t<dataurl>https://er-sycdn.kuwo.cn/f146b385432a7f7d9278b762673159ff/687f69ec/resource/30106/trackmedia/F000000RxNgF3xdxE1.flac?src=无损音质 FLAC_23.42 MB?src=flac_23.42 MB?from=longzhu_api</dataurl>\n\t\t<lowurl>https://weixin.qq.com</lowurl>\n\t\t<lowdataurl>https://er-sycdn.kuwo.cn/f146b385432a7f7d9278b762673159ff/687f69ec/resource/30106/trackmedia/F000000RxNgF3xdxE1.flac?src=无损音质 FLAC_23.42 MB?src=flac_23.42 MB?from=longzhu_api</lowdataurl>\n\t\t<recorditem />\n\t\t<thumburl>http://imge.kugou.com/stdmusic/400/20250417/20250417203222425923.jpg</thumburl>\n\t\t<messageaction />\n\t\t<laninfo />\n\t\t<extinfo />\n\t\t<sourceusername />\n\t\t<sourcedisplayname />\n\t\t<songlyric>\ufeff[id:$00000000]\n[ar:李佳思]\n[ti:小爱]\n[by:]\n[hash:0a29ad14a47d15ea946d69503fa598cc]\n[al:]\n[sign:]\n[qq:]\n[total:213364]\n[offset:0]\n[00:00.26]李佳思 - 小爱\n[00:01.22]作词：贯诗钦\n[00:02.08]作曲：贯诗钦\n[00:02.94]编曲：王柏鸿\n[00:16.70]Hey could u hear 我的声音\n[00:20.74]会不会在夏季带来甜蜜\n[00:24.94]I just want to know\n[00:27.07]I just want to see\n[00:29.19]I just want you be with me\n[00:33.36]Hey could u bring me more happy\n[00:37.61]我要 a bit of love 可不可以\n[00:41.86]Oh my sweetheart\n[00:43.27]Oh my darling boy\n[00:45.55]Oh 请叫我 baby 请不要离去\n[00:50.10]我希望 u and me 一起\n[00:54.38]看 sea 看海鸥看风景\n[00:58.63]任时间 gone away\n[01:00.75]我们 still 一起\n[01:03.08]You worth everything for me\n[01:07.02]你希望 u and me 一起\n[01:11.32]背包踏访圣托里尼\n[01:15.41]我们拍部时光电影\n[01:19.89]Just two of us in this movie\n[01:40.95]Hey could u hear 我的声音\n[01:44.95]会不会在夏季带来甜蜜\n[01:49.20]I just want to know\n[01:51.18]I just want to see\n[01:53.41]I just want you be with me\n[01:57.62]Hey could u bring me more happy\n[02:01.87]我要 a bit of love 可不可以\n[02:06.07]Oh my sweetheart\n[02:07.64]Oh my darling boy\n[02:09.71]Oh 请叫我 baby 请不要离去\n[02:14.32]我希望 u and me 一起\n[02:18.67]看 sea 看海鸥看风景\n[02:22.86]任时间 gone away\n[02:24.99]我们 still 一起\n[02:27.32]You worth everything for me\n[02:31.26]你希望 u and me 一起\n[02:35.51]背包踏访圣托里尼\n[02:39.71]我们拍部时光电影\n[02:44.09]Just two of us in this movie\n[02:48.09]我希望 u and me 一起\n[02:52.34]看 sea 看海鸥看风景\n[02:56.49]任时间 gone away\n[02:58.61]我们 still 一起\n[03:00.99]You worth everything for me\n[03:05.22]你希望 u and me 一起\n[03:09.16]背包踏访圣托里尼\n[03:13.36]我们拍部时光电影\n[03:17.89]Just two of us in this movie\n</songlyric>\n\t\t<commenturl />\n\t\t<appattach>\n\t\t\t<totallen>0</totallen>\n\t\t\t<attachid />\n\t\t\t<emoticonmd5></emoticonmd5>\n\t\t\t<fileext />\n\t\t\t<aeskey></aeskey>\n\t\t</appattach>\n\t\t<webviewshared>\n\t\t\t<publisherId />\n\t\t\t<publisherReqId>0</publisherReqId>\n\t\t</webviewshared>\n\t\t<weappinfo>\n\t\t\t<pagepath />\n\t\t\t<username />\n\t\t\t<appid />\n\t\t\t<appservicetype>0</appservicetype>\n\t\t</weappinfo>\n\t\t<websearch />\n\t\t<songalbumurl>http://imge.kugou.com/stdmusic/400/20250417/20250417203222425923.jpg</songalbumurl>\n\t</appmsg>\n\t<fromusername>wxid_4usgcju5ey9q29</fromusername>\n\t<scene>0</scene>\n\t<appinfo>\n\t\t<version>1</version>\n\t\t<appname />\n\t</appinfo>\n\t<commenturl />\n</msg>\n', 'Msgid': '8464529937443046887', 'NewMsgId': '8464529937443046887', 'ToWxid': 'wxid_4usgcju5ey9q29', 'FromWxid': '47442567074@chatroom', 'Nickname': '瑶瑶', 'MsgSource': '<msgsource>\n\t<bizflag>0</bizflag>\n\t<sec_msg_node>\n\t\t<uuid>16f7ad10530a751a1b03e6665befabb9_</uuid>\n\t\t<risk-file-flag />\n\t\t<risk-file-md5-list />\n\t</sec_msg_node>\n\t<silence>1</silence>\n\t<membercount>494</membercount>\n\t<signature>N0_V1_x2gfTOP+|v1_wEIbgXpL</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'Createtime': '1753180652', 'SenderWxid': 'xiaomaochong'}
2025-07-22 18:38:13 | INFO |   - 引用消息ID: 
2025-07-22 18:38:13 | INFO |   - 引用消息类型: 
2025-07-22 18:38:13 | INFO |   - 引用消息内容: <?xml version="1.0"?>
<msg>
	<appmsg appid="wx485a97c844086dc9" sdkver="0">
		<title>小爱</title>
		<des>李佳思</des>
		<action>view</action>
		<type>3</type>
		<showtype>0</showtype>
		<content />
		<url>https://weixin.qq.com</url>
		<dataurl>https://er-sycdn.kuwo.cn/f146b385432a7f7d9278b762673159ff/687f69ec/resource/30106/trackmedia/F000000RxNgF3xdxE1.flac?src=无损音质 FLAC_23.42 MB?src=flac_23.42 MB?from=longzhu_api</dataurl>
		<lowurl>https://weixin.qq.com</lowurl>
		<lowdataurl>https://er-sycdn.kuwo.cn/f146b385432a7f7d9278b762673159ff/687f69ec/resource/30106/trackmedia/F000000RxNgF3xdxE1.flac?src=无损音质 FLAC_23.42 MB?src=flac_23.42 MB?from=longzhu_api</lowdataurl>
		<recorditem />
		<thumburl>http://imge.kugou.com/stdmusic/400/20250417/20250417203222425923.jpg</thumburl>
		<messageaction />
		<laninfo />
		<extinfo />
		<sourceusername />
		<sourcedisplayname />
		<songlyric>﻿[id:$00000000]
[ar:李佳思]
[ti:小爱]
[by:]
[hash:0a29ad14a47d15ea946d69503fa598cc]
[al:]
[sign:]
[qq:]
[total:213364]
[offset:0]
[00:00.26]李佳思 - 小爱
[00:01.22]作词：贯诗钦
[00:02.08]作曲：贯诗钦
[00:02.94]编曲：王柏鸿
[00:16.70]Hey could u hear 我的声音
[00:20.74]会不会在夏季带来甜蜜
[00:24.94]I just want to know
[00:27.07]I just want to see
[00:29.19]I just want you be with me
[00:33.36]Hey could u bring me more happy
[00:37.61]我要 a bit of love 可不可以
[00:41.86]Oh my sweetheart
[00:43.27]Oh my darling boy
[00:45.55]Oh 请叫我 baby 请不要离去
[00:50.10]我希望 u and me 一起
[00:54.38]看 sea 看海鸥看风景
[00:58.63]任时间 gone away
[01:00.75]我们 still 一起
[01:03.08]You worth everything for me
[01:07.02]你希望 u and me 一起
[01:11.32]背包踏访圣托里尼
[01:15.41]我们拍部时光电影
[01:19.89]Just two of us in this movie
[01:40.95]Hey could u hear 我的声音
[01:44.95]会不会在夏季带来甜蜜
[01:49.20]I just want to know
[01:51.18]I just want to see
[01:53.41]I just want you be with me
[01:57.62]Hey could u bring me more happy
[02:01.87]我要 a bit of love 可不可以
[02:06.07]Oh my sweetheart
[02:07.64]Oh my darling boy
[02:09.71]Oh 请叫我 baby 请不要离去
[02:14.32]我希望 u and me 一起
[02:18.67]看 sea 看海鸥看风景
[02:22.86]任时间 gone away
[02:24.99]我们 still 一起
[02:27.32]You worth everything for me
[02:31.26]你希望 u and me 一起
[02:35.51]背包踏访圣托里尼
[02:39.71]我们拍部时光电影
[02:44.09]Just two of us in this movie
[02:48.09]我希望 u and me 一起
[02:52.34]看 sea 看海鸥看风景
[02:56.49]任时间 gone away
[02:58.61]我们 still 一起
[03:00.99]You worth everything for me
[03:05.22]你希望 u and me 一起
[03:09.16]背包踏访圣托里尼
[03:13.36]我们拍部时光电影
[03:17.89]Just two of us in this movie
</songlyric>
		<commenturl />
		<appattach>
			<totallen>0</totallen>
			<attachid />
			<emoticonmd5></emoticonmd5>
			<fileext />
			<aeskey></aeskey>
		</appattach>
		<webviewshared>
			<publisherId />
			<publisherReqId>0</publisherReqId>
		</webviewshared>
		<weappinfo>
			<pagepath />
			<username />
			<appid />
			<appservicetype>0</appservicetype>
		</weappinfo>
		<websearch />
		<songalbumurl>http://imge.kugou.com/stdmusic/400/20250417/20250417203222425923.jpg</songalbumurl>
	</appmsg>
	<fromusername>wxid_4usgcju5ey9q29</fromusername>
	<scene>0</scene>
	<appinfo>
		<version>1</version>
		<appname />
	</appinfo>
	<commenturl />
</msg>

2025-07-22 18:38:13 | INFO |   - 引用消息发送人: xiaomaochong
2025-07-22 18:38:13 | DEBUG | 收到消息: {'MsgId': 2105702965, 'FromUserName': {'string': '47442567074@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 49, 'Content': {'string': 'xiaomaochong:\n<?xml version="1.0"?>\n<msg>\n\t<appmsg appid="wx485a97c844086dc9" sdkver="0">\n\t\t<title>小爱</title>\n\t\t<des>小爱</des>\n\t\t<username />\n\t\t<action>view</action>\n\t\t<type>3</type>\n\t\t<showtype>0</showtype>\n\t\t<content />\n\t\t<url />\n\t\t<lowurl />\n\t\t<forwardflag>0</forwardflag>\n\t\t<dataurl>https://er-sycdn.kuwo.cn/f146b385432a7f7d9278b762673159ff/687f69ec/resource/30106/trackmedia/F000000RxNgF3xdxE1.flac?src=无损音质 FLAC_23.42 MB?src=flac_23.42 MB?from=longzhu_api</dataurl>\n\t\t<lowdataurl />\n\t\t<contentattr>0</contentattr>\n\t\t<streamvideo>\n\t\t\t<streamvideourl />\n\t\t\t<streamvideototaltime>0</streamvideototaltime>\n\t\t\t<streamvideotitle />\n\t\t\t<streamvideowording />\n\t\t\t<streamvideoweburl />\n\t\t\t<streamvideothumburl />\n\t\t\t<streamvideoaduxinfo />\n\t\t\t<streamvideopublishid />\n\t\t</streamvideo>\n\t\t<canvasPageItem>\n\t\t\t<canvasPageXml><![CDATA[]]></canvasPageXml>\n\t\t</canvasPageItem>\n\t\t<appattach>\n\t\t\t<totallen>0</totallen>\n\t\t\t<attachid />\n\t\t\t<cdnattachurl />\n\t\t\t<emoticonmd5></emoticonmd5>\n\t\t\t<aeskey></aeskey>\n\t\t\t<fileext />\n\t\t\t<islargefilemsg>0</islargefilemsg>\n\t\t</appattach>\n\t\t<extinfo />\n\t\t<androidsource>2</androidsource>\n\t\t<thumburl />\n\t\t<mediatagname />\n\t\t<messageaction><![CDATA[]]></messageaction>\n\t\t<messageext><![CDATA[]]></messageext>\n\t\t<emoticongift>\n\t\t\t<packageflag>0</packageflag>\n\t\t\t<packageid />\n\t\t</emoticongift>\n\t\t<emoticonshared>\n\t\t\t<packageflag>0</packageflag>\n\t\t\t<packageid />\n\t\t</emoticonshared>\n\t\t<designershared>\n\t\t\t<designeruin>0</designeruin>\n\t\t\t<designername>null</designername>\n\t\t\t<designerrediretcturl><![CDATA[null]]></designerrediretcturl>\n\t\t</designershared>\n\t\t<emotionpageshared>\n\t\t\t<tid>0</tid>\n\t\t\t<title>null</title>\n\t\t\t<desc>null</desc>\n\t\t\t<iconUrl><![CDATA[null]]></iconUrl>\n\t\t\t<secondUrl>null</secondUrl>\n\t\t\t<pageType>0</pageType>\n\t\t\t<setKey>null</setKey>\n\t\t</emotionpageshared>\n\t\t<webviewshared>\n\t\t\t<shareUrlOriginal />\n\t\t\t<shareUrlOpen />\n\t\t\t<jsAppId />\n\t\t\t<publisherId />\n\t\t\t<publisherReqId />\n\t\t</webviewshared>\n\t\t<template_id />\n\t\t<md5 />\n\t\t<websearch>\n\t\t\t<rec_category>0</rec_category>\n\t\t\t<channelId>0</channelId>\n\t\t</websearch>\n\t\t<weappinfo>\n\t\t\t<username />\n\t\t\t<appid />\n\t\t\t<appservicetype>0</appservicetype>\n\t\t\t<secflagforsinglepagemode>0</secflagforsinglepagemode>\n\t\t\t<videopageinfo>\n\t\t\t\t<thumbwidth>0</thumbwidth>\n\t\t\t\t<thumbheight>0</thumbheight>\n\t\t\t\t<fromopensdk>0</fromopensdk>\n\t\t\t</videopageinfo>\n\t\t</weappinfo>\n\t\t<statextstr>GhQKEnd4NDg1YTk3Yzg0NDA4NmRjOQ==</statextstr>\n\t\t<songalbumurl>https://wx.qlogo.cn/mmhead/ver_1/6iaSQLKjLK6YMpTGwdaFEJHr0OKpQPK5HR2zUmibE6f8W4tsSjkT8RbjHCBOnoIdKv28tarjHhJic6qRhiczIdOzbEUTMQebeT1XouSEMct9ofiaha74cyibDl8vxtUztxWIicB/0</songalbumurl>\n\t\t<songlyric>[99:99.99]小爱机器人提醒您，该歌曲没有歌词</songlyric>\n\t\t<musicShareItem>\n\t\t\t<musicDuration>0</musicDuration>\n\t\t</musicShareItem>\n\t\t<finderLiveProductShare>\n\t\t\t<finderLiveID><![CDATA[]]></finderLiveID>\n\t\t\t<finderUsername><![CDATA[]]></finderUsername>\n\t\t\t<finderObjectID><![CDATA[]]></finderObjectID>\n\t\t\t<finderNonceID><![CDATA[]]></finderNonceID>\n\t\t\t<liveStatus><![CDATA[]]></liveStatus>\n\t\t\t<appId><![CDATA[]]></appId>\n\t\t\t<pagePath><![CDATA[]]></pagePath>\n\t\t\t<productId><![CDATA[]]></productId>\n\t\t\t<coverUrl><![CDATA[]]></coverUrl>\n\t\t\t<productTitle><![CDATA[]]></productTitle>\n\t\t\t<marketPrice><![CDATA[0]]></marketPrice>\n\t\t\t<sellingPrice><![CDATA[0]]></sellingPrice>\n\t\t\t<platformHeadImg><![CDATA[]]></platformHeadImg>\n\t\t\t<platformName><![CDATA[]]></platformName>\n\t\t\t<shopWindowId><![CDATA[]]></shopWindowId>\n\t\t\t<flashSalePrice><![CDATA[0]]></flashSalePrice>\n\t\t\t<flashSaleEndTime><![CDATA[0]]></flashSaleEndTime>\n\t\t\t<ecSource><![CDATA[]]></ecSource>\n\t\t\t<sellingPriceWording><![CDATA[]]></sellingPriceWording>\n\t\t\t<platformIconURL><![CDATA[]]></platformIconURL>\n\t\t\t<firstProductTagURL><![CDATA[]]></firstProductTagURL>\n\t\t\t<firstProductTagAspectRatioString><![CDATA[0.0]]></firstProductTagAspectRatioString>\n\t\t\t<secondProductTagURL><![CDATA[]]></secondProductTagURL>\n\t\t\t<secondProductTagAspectRatioString><![CDATA[0.0]]></secondProductTagAspectRatioString>\n\t\t\t<firstGuaranteeWording><![CDATA[]]></firstGuaranteeWording>\n\t\t\t<secondGuaranteeWording><![CDATA[]]></secondGuaranteeWording>\n\t\t\t<thirdGuaranteeWording><![CDATA[]]></thirdGuaranteeWording>\n\t\t\t<isPriceBeginShow>false</isPriceBeginShow>\n\t\t\t<lastGMsgID><![CDATA[]]></lastGMsgID>\n\t\t\t<promoterKey><![CDATA[]]></promoterKey>\n\t\t\t<discountWording><![CDATA[]]></discountWording>\n\t\t\t<priceSuffixDescription><![CDATA[]]></priceSuffixDescription>\n\t\t\t<showBoxItemStringList />\n\t\t</finderLiveProductShare>\n\t\t<finderOrder>\n\t\t\t<appID><![CDATA[]]></appID>\n\t\t\t<orderID><![CDATA[]]></orderID>\n\t\t\t<path><![CDATA[]]></path>\n\t\t\t<priceWording><![CDATA[]]></priceWording>\n\t\t\t<stateWording><![CDATA[]]></stateWording>\n\t\t\t<productImageURL><![CDATA[]]></productImageURL>\n\t\t\t<products><![CDATA[]]></products>\n\t\t\t<productsCount><![CDATA[0]]></productsCount>\n\t\t</finderOrder>\n\t\t<finderShopWindowShare>\n\t\t\t<finderUsername><![CDATA[]]></finderUsername>\n\t\t\t<avatar><![CDATA[]]></avatar>\n\t\t\t<nickname><![CDATA[]]></nickname>\n\t\t\t<commodityInStockCount><![CDATA[]]></commodityInStockCount>\n\t\t\t<appId><![CDATA[]]></appId>\n\t\t\t<path><![CDATA[]]></path>\n\t\t\t<appUsername><![CDATA[]]></appUsername>\n\t\t\t<query><![CDATA[]]></query>\n\t\t\t<liteAppId><![CDATA[]]></liteAppId>\n\t\t\t<liteAppPath><![CDATA[]]></liteAppPath>\n\t\t\t<liteAppQuery><![CDATA[]]></liteAppQuery>\n\t\t\t<platformTagURL><![CDATA[]]></platformTagURL>\n\t\t\t<saleWording><![CDATA[]]></saleWording>\n\t\t\t<lastGMsgID><![CDATA[]]></lastGMsgID>\n\t\t\t<profileTypeWording><![CDATA[]]></profileTypeWording>\n\t\t\t<reputationInfo>\n\t\t\t\t<hasReputationInfo>0</hasReputationInfo>\n\t\t\t\t<reputationScore>0</reputationScore>\n\t\t\t\t<reputationWording />\n\t\t\t\t<reputationTextColor />\n\t\t\t\t<reputationLevelWording />\n\t\t\t\t<reputationBackgroundColor />\n\t\t\t</reputationInfo>\n\t\t\t<productImageURLList />\n\t\t</finderShopWindowShare>\n\t\t<findernamecard>\n\t\t\t<username />\n\t\t\t<avatar><![CDATA[]]></avatar>\n\t\t\t<nickname />\n\t\t\t<auth_job />\n\t\t\t<auth_icon>0</auth_icon>\n\t\t\t<auth_icon_url />\n\t\t\t<ecSource><![CDATA[]]></ecSource>\n\t\t\t<lastGMsgID><![CDATA[]]></lastGMsgID>\n\t\t</findernamecard>\n\t\t<finderGuarantee>\n\t\t\t<scene><![CDATA[0]]></scene>\n\t\t</finderGuarantee>\n\t\t<directshare>0</directshare>\n\t\t<gamecenter>\n\t\t\t<namecard>\n\t\t\t\t<iconUrl />\n\t\t\t\t<name />\n\t\t\t\t<desc />\n\t\t\t\t<tail />\n\t\t\t\t<jumpUrl />\n\t\t\t</namecard>\n\t\t</gamecenter>\n\t\t<patMsg>\n\t\t\t<chatUser />\n\t\t\t<records>\n\t\t\t\t<recordNum>0</recordNum>\n\t\t\t</records>\n\t\t</patMsg>\n\t\t<secretmsg>\n\t\t\t<issecretmsg>0</issecretmsg>\n\t\t</secretmsg>\n\t\t<referfromscene>0</referfromscene>\n\t\t<gameshare>\n\t\t\t<liteappext>\n\t\t\t\t<liteappbizdata />\n\t\t\t\t<priority>0</priority>\n\t\t\t</liteappext>\n\t\t\t<appbrandext>\n\t\t\t\t<litegameinfo />\n\t\t\t\t<priority>-1</priority>\n\t\t\t</appbrandext>\n\t\t\t<gameshareid />\n\t\t\t<sharedata />\n\t\t\t<isvideo>0</isvideo>\n\t\t\t<duration>-1</duration>\n\t\t\t<isexposed>0</isexposed>\n\t\t\t<readtext />\n\t\t</gameshare>\n\t\t<mpsharetrace>\n\t\t\t<hasfinderelement>0</hasfinderelement>\n\t\t\t<lastgmsgid />\n\t\t</mpsharetrace>\n\t\t<wxgamecard>\n\t\t\t<framesetname />\n\t\t\t<mbcarddata />\n\t\t\t<minpkgversion />\n\t\t\t<mbcardheight>0</mbcardheight>\n\t\t\t<isoldversion>0</isoldversion>\n\t\t</wxgamecard>\n\t\t<liteapp>\n\t\t\t<id>null</id>\n\t\t\t<path />\n\t\t\t<query />\n\t\t</liteapp>\n\t</appmsg>\n\t<fromusername>xiaomaochong</fromusername>\n\t<scene>0</scene>\n\t<appinfo>\n\t\t<version>29</version>\n\t\t<appname>摇一摇搜歌</appname>\n\t</appinfo>\n\t<commenturl />\n</msg>\n'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1753180706, 'MsgSource': '<msgsource>\n\t<sec_msg_node>\n\t\t<uuid>8235f053484a7acb7668b04ecec133d2_</uuid>\n\t\t<risk-file-flag />\n\t\t<risk-file-md5-list />\n\t</sec_msg_node>\n\t<silence>0</silence>\n\t<membercount>494</membercount>\n\t<signature>N0_V1_zCuC0lTm|v1_sTvo8deJ</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'PushContent': '你收到了一条消息', 'NewMsgId': 3706219329806236412, 'MsgSeq': 871392668}
2025-07-22 18:38:13 | DEBUG | 从群聊消息中提取发送者: xiaomaochong
2025-07-22 18:38:13 | DEBUG | XML消息完整内容:
<?xml version="1.0"?>
<msg>
	<appmsg appid="wx485a97c844086dc9" sdkver="0">
		<title>小爱</title>
		<des>小爱</des>
		<username />
		<action>view</action>
		<type>3</type>
		<showtype>0</showtype>
		<content />
		<url />
		<lowurl />
		<forwardflag>0</forwardflag>
		<dataurl>https://er-sycdn.kuwo.cn/f146b385432a7f7d9278b762673159ff/687f69ec/resource/30106/trackmedia/F000000RxNgF3xdxE1.flac?src=无损音质 FLAC_23.42 MB?src=flac_23.42 MB?from=longzhu_api</dataurl>
		<lowdataurl />
		<contentattr>0</contentattr>
		<streamvideo>
			<streamvideourl />
			<streamvideototaltime>0</streamvideototaltime>
			<streamvideotitle />
			<streamvideowording />
			<streamvideoweburl />
			<streamvideothumburl />
			<streamvideoaduxinfo />
			<streamvideopublishid />
		</streamvideo>
		<canvasPageItem>
			<canvasPageXml><![CDATA[]]></canvasPageXml>
		</canvasPageItem>
		<appattach>
			<totallen>0</totallen>
			<attachid />
			<cdnattachurl />
			<emoticonmd5></emoticonmd5>
			<aeskey></aeskey>
			<fileext />
			<islargefilemsg>0</islargefilemsg>
		</appattach>
		<extinfo />
		<androidsource>2</androidsource>
		<thumburl />
		<mediatagname />
		<messageaction><![CDATA[]]></messageaction>
		<messageext><![CDATA[]]></messageext>
		<emoticongift>
			<packageflag>0</packageflag>
			<packageid />
		</emoticongift>
		<emoticonshared>
			<packageflag>0</packageflag>
			<packageid />
		</emoticonshared>
		<designershared>
			<designeruin>0</designeruin>
			<designername>null</designername>
			<designerrediretcturl><![CDATA[null]]></designerrediretcturl>
		</designershared>
		<emotionpageshared>
			<tid>0</tid>
			<title>null</title>
			<desc>null</desc>
			<iconUrl><![CDATA[null]]></iconUrl>
			<secondUrl>null</secondUrl>
			<pageType>0</pageType>
			<setKey>null</setKey>
		</emotionpageshared>
		<webviewshared>
			<shareUrlOriginal />
			<shareUrlOpen />
			<jsAppId />
			<publisherId />
			<publisherReqId />
		</webviewshared>
		<template_id />
		<md5 />
		<websearch>
			<rec_category>0</rec_category>
			<channelId>0</channelId>
		</websearch>
		<weappinfo>
			<username />
			<appid />
			<appservicetype>0</appservicetype>
			<secflagforsinglepagemode>0</secflagforsinglepagemode>
			<videopageinfo>
				<thumbwidth>0</thumbwidth>
				<thumbheight>0</thumbheight>
				<fromopensdk>0</fromopensdk>
			</videopageinfo>
		</weappinfo>
		<statextstr>GhQKEnd4NDg1YTk3Yzg0NDA4NmRjOQ==</statextstr>
		<songalbumurl>https://wx.qlogo.cn/mmhead/ver_1/6iaSQLKjLK6YMpTGwdaFEJHr0OKpQPK5HR2zUmibE6f8W4tsSjkT8RbjHCBOnoIdKv28tarjHhJic6qRhiczIdOzbEUTMQebeT1XouSEMct9ofiaha74cyibDl8vxtUztxWIicB/0</songalbumurl>
		<songlyric>[99:99.99]小爱机器人提醒您，该歌曲没有歌词</songlyric>
		<musicShareItem>
			<musicDuration>0</musicDuration>
		</musicShareItem>
		<finderLiveProductShare>
			<finderLiveID><![CDATA[]]></finderLiveID>
			<finderUsername><![CDATA[]]></finderUsername>
			<finderObjectID><![CDATA[]]></finderObjectID>
			<finderNonceID><![CDATA[]]></finderNonceID>
			<liveStatus><![CDATA[]]></liveStatus>
			<appId><![CDATA[]]></appId>
			<pagePath><![CDATA[]]></pagePath>
			<productId><![CDATA[]]></productId>
			<coverUrl><![CDATA[]]></coverUrl>
			<productTitle><![CDATA[]]></productTitle>
			<marketPrice><![CDATA[0]]></marketPrice>
			<sellingPrice><![CDATA[0]]></sellingPrice>
			<platformHeadImg><![CDATA[]]></platformHeadImg>
			<platformName><![CDATA[]]></platformName>
			<shopWindowId><![CDATA[]]></shopWindowId>
			<flashSalePrice><![CDATA[0]]></flashSalePrice>
			<flashSaleEndTime><![CDATA[0]]></flashSaleEndTime>
			<ecSource><![CDATA[]]></ecSource>
			<sellingPriceWording><![CDATA[]]></sellingPriceWording>
			<platformIconURL><![CDATA[]]></platformIconURL>
			<firstProductTagURL><![CDATA[]]></firstProductTagURL>
			<firstProductTagAspectRatioString><![CDATA[0.0]]></firstProductTagAspectRatioString>
			<secondProductTagURL><![CDATA[]]></secondProductTagURL>
			<secondProductTagAspectRatioString><![CDATA[0.0]]></secondProductTagAspectRatioString>
			<firstGuaranteeWording><![CDATA[]]></firstGuaranteeWording>
			<secondGuaranteeWording><![CDATA[]]></secondGuaranteeWording>
			<thirdGuaranteeWording><![CDATA[]]></thirdGuaranteeWording>
			<isPriceBeginShow>false</isPriceBeginShow>
			<lastGMsgID><![CDATA[]]></lastGMsgID>
			<promoterKey><![CDATA[]]></promoterKey>
			<discountWording><![CDATA[]]></discountWording>
			<priceSuffixDescription><![CDATA[]]></priceSuffixDescription>
			<showBoxItemStringList />
		</finderLiveProductShare>
		<finderOrder>
			<appID><![CDATA[]]></appID>
			<orderID><![CDATA[]]></orderID>
			<path><![CDATA[]]></path>
			<priceWording><![CDATA[]]></priceWording>
			<stateWording><![CDATA[]]></stateWording>
			<productImageURL><![CDATA[]]></productImageURL>
			<products><![CDATA[]]></products>
			<productsCount><![CDATA[0]]></productsCount>
		</finderOrder>
		<finderShopWindowShare>
			<finderUsername><![CDATA[]]></finderUsername>
			<avatar><![CDATA[]]></avatar>
			<nickname><![CDATA[]]></nickname>
			<commodityInStockCount><![CDATA[]]></commodityInStockCount>
			<appId><![CDATA[]]></appId>
			<path><![CDATA[]]></path>
			<appUsername><![CDATA[]]></appUsername>
			<query><![CDATA[]]></query>
			<liteAppId><![CDATA[]]></liteAppId>
			<liteAppPath><![CDATA[]]></liteAppPath>
			<liteAppQuery><![CDATA[]]></liteAppQuery>
			<platformTagURL><![CDATA[]]></platformTagURL>
			<saleWording><![CDATA[]]></saleWording>
			<lastGMsgID><![CDATA[]]></lastGMsgID>
			<profileTypeWording><![CDATA[]]></profileTypeWording>
			<reputationInfo>
				<hasReputationInfo>0</hasReputationInfo>
				<reputationScore>0</reputationScore>
				<reputationWording />
				<reputationTextColor />
				<reputationLevelWording />
				<reputationBackgroundColor />
			</reputationInfo>
			<productImageURLList />
		</finderShopWindowShare>
		<findernamecard>
			<username />
			<avatar><![CDATA[]]></avatar>
			<nickname />
			<auth_job />
			<auth_icon>0</auth_icon>
			<auth_icon_url />
			<ecSource><![CDATA[]]></ecSource>
			<lastGMsgID><![CDATA[]]></lastGMsgID>
		</findernamecard>
		<finderGuarantee>
			<scene><![CDATA[0]]></scene>
		</finderGuarantee>
		<directshare>0</directshare>
		<gamecenter>
			<namecard>
				<iconUrl />
				<name />
				<desc />
				<tail />
				<jumpUrl />
			</namecard>
		</gamecenter>
		<patMsg>
			<chatUser />
			<records>
				<recordNum>0</recordNum>
			</records>
		</patMsg>
		<secretmsg>
			<issecretmsg>0</issecretmsg>
		</secretmsg>
		<referfromscene>0</referfromscene>
		<gameshare>
			<liteappext>
				<liteappbizdata />
				<priority>0</priority>
			</liteappext>
			<appbrandext>
				<litegameinfo />
				<priority>-1</priority>
			</appbrandext>
			<gameshareid />
			<sharedata />
			<isvideo>0</isvideo>
			<duration>-1</duration>
			<isexposed>0</isexposed>
			<readtext />
		</gameshare>
		<mpsharetrace>
			<hasfinderelement>0</hasfinderelement>
			<lastgmsgid />
		</mpsharetrace>
		<wxgamecard>
			<framesetname />
			<mbcarddata />
			<minpkgversion />
			<mbcardheight>0</mbcardheight>
			<isoldversion>0</isoldversion>
		</wxgamecard>
		<liteapp>
			<id>null</id>
			<path />
			<query />
		</liteapp>
	</appmsg>
	<fromusername>xiaomaochong</fromusername>
	<scene>0</scene>
	<appinfo>
		<version>29</version>
		<appname>摇一摇搜歌</appname>
	</appinfo>
	<commenturl />
</msg>

2025-07-22 18:38:13 | DEBUG | XML消息类型: 3
2025-07-22 18:38:13 | DEBUG | XML消息标题: 小爱
2025-07-22 18:38:13 | DEBUG | XML消息描述: 小爱
2025-07-22 18:38:13 | DEBUG | 附件信息 totallen: 0
2025-07-22 18:38:13 | DEBUG | 附件信息 islargefilemsg: 0
2025-07-22 18:38:13 | INFO | 收到红包消息: 标题:小爱 描述:小爱 来自:47442567074@chatroom 发送人:wxid_4usgcju5ey9q29
2025-07-22 18:38:49 | DEBUG | 收到消息: {'MsgId': 1214863745, 'FromUserName': {'string': '47442567074@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 1, 'Content': {'string': 'wxid_ubbh6q832tcs21:\n为啥还有我的头像'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1753180742, 'MsgSource': '<msgsource>\n\t<pua>1</pua>\n\t<eggIncluded>1</eggIncluded>\n\t<silence>0</silence>\n\t<membercount>494</membercount>\n\t<signature>N0_V1_BpvYHPXp|v1_E43QfL3c</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'PushContent': '郭 : 为啥还有我的头像', 'NewMsgId': 1111981829074885657, 'MsgSeq': 871392669}
2025-07-22 18:38:49 | INFO | 收到文本消息: 消息ID:1214863745 来自:47442567074@chatroom 发送人:wxid_ubbh6q832tcs21 @:[] 内容:为啥还有我的头像
2025-07-22 18:38:49 | DEBUG | 处理消息内容: '为啥还有我的头像'
2025-07-22 18:38:49 | DEBUG | 消息内容 '为啥还有我的头像' 不匹配任何命令，忽略
2025-07-22 18:39:08 | DEBUG | 收到消息: {'MsgId': 1500879598, 'FromUserName': {'string': '47442567074@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 1, 'Content': {'string': 'xiaomaochong:\n你自己设置的啊'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1753180761, 'MsgSource': '<msgsource>\n\t<pua>1</pua>\n\t<silence>0</silence>\n\t<membercount>494</membercount>\n\t<signature>N0_V1_c+aovN2r|v1_oETaE7gx</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'PushContent': '小爱 : 你自己设置的啊', 'NewMsgId': 4619720599001628535, 'MsgSeq': 871392670}
2025-07-22 18:39:08 | INFO | 收到文本消息: 消息ID:1500879598 来自:47442567074@chatroom 发送人:xiaomaochong @:[] 内容:你自己设置的啊
2025-07-22 18:39:08 | DEBUG | 处理消息内容: '你自己设置的啊'
2025-07-22 18:39:08 | DEBUG | 消息内容 '你自己设置的啊' 不匹配任何命令，忽略
2025-07-22 18:39:52 | DEBUG | 收到消息: {'MsgId': 385934724, 'FromUserName': {'string': '48097389945@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 49, 'Content': {'string': 'wxid_wlnzvr8ivgd422:\n<?xml version="1.0"?>\n<msg>\n\t<appmsg appid="" sdkver="0">\n\t\t<title>我才不要，老板太难伺候</title>\n\t\t<des />\n\t\t<username />\n\t\t<action>view</action>\n\t\t<type>57</type>\n\t\t<showtype>0</showtype>\n\t\t<content />\n\t\t<url />\n\t\t<lowurl />\n\t\t<forwardflag>0</forwardflag>\n\t\t<dataurl />\n\t\t<lowdataurl />\n\t\t<contentattr>0</contentattr>\n\t\t<streamvideo>\n\t\t\t<streamvideourl />\n\t\t\t<streamvideototaltime>0</streamvideototaltime>\n\t\t\t<streamvideotitle />\n\t\t\t<streamvideowording />\n\t\t\t<streamvideoweburl />\n\t\t\t<streamvideothumburl />\n\t\t\t<streamvideoaduxinfo />\n\t\t\t<streamvideopublishid />\n\t\t</streamvideo>\n\t\t<canvasPageItem>\n\t\t\t<canvasPageXml><![CDATA[]]></canvasPageXml>\n\t\t</canvasPageItem>\n\t\t<refermsg>\n\t\t\t<type>1</type>\n\t\t\t<svrid>7794469446990082250</svrid>\n\t\t\t<fromusr>48097389945@chatroom</fromusr>\n\t\t\t<chatusr>xiaomaochong</chatusr>\n\t\t\t<displayname>小爱</displayname>\n\t\t\t<msgsource>&lt;msgsource&gt;\n\t&lt;pua&gt;1&lt;/pua&gt;\n\t&lt;silence&gt;1&lt;/silence&gt;\n\t&lt;membercount&gt;62&lt;/membercount&gt;\n\t&lt;signature&gt;N0_V1_9oXLg/Of|v1_HniAlsxI&lt;/signature&gt;\n\t&lt;tmp_node&gt;\n\t\t&lt;publisher-id&gt;&lt;/publisher-id&gt;\n\t&lt;/tmp_node&gt;\n&lt;/msgsource&gt;\n</msgsource>\n\t\t\t<content>\n你要当行政了吗</content>\n\t\t\t<strid />\n\t\t\t<createtime>1753180352</createtime>\n\t\t</refermsg>\n\t\t<appattach>\n\t\t\t<totallen>0</totallen>\n\t\t\t<attachid />\n\t\t\t<cdnattachurl />\n\t\t\t<emoticonmd5 />\n\t\t\t<aeskey />\n\t\t\t<fileext />\n\t\t\t<islargefilemsg>0</islargefilemsg>\n\t\t</appattach>\n\t\t<extinfo />\n\t\t<androidsource>0</androidsource>\n\t\t<thumburl />\n\t\t<mediatagname />\n\t\t<messageaction><![CDATA[]]></messageaction>\n\t\t<messageext><![CDATA[]]></messageext>\n\t\t<emoticongift>\n\t\t\t<packageflag>0</packageflag>\n\t\t\t<packageid />\n\t\t</emoticongift>\n\t\t<emoticonshared>\n\t\t\t<packageflag>0</packageflag>\n\t\t\t<packageid />\n\t\t</emoticonshared>\n\t\t<designershared>\n\t\t\t<designeruin>0</designeruin>\n\t\t\t<designername>null</designername>\n\t\t\t<designerrediretcturl><![CDATA[null]]></designerrediretcturl>\n\t\t</designershared>\n\t\t<emotionpageshared>\n\t\t\t<tid>0</tid>\n\t\t\t<title>null</title>\n\t\t\t<desc>null</desc>\n\t\t\t<iconUrl><![CDATA[null]]></iconUrl>\n\t\t\t<secondUrl>null</secondUrl>\n\t\t\t<pageType>0</pageType>\n\t\t\t<setKey>null</setKey>\n\t\t</emotionpageshared>\n\t\t<webviewshared>\n\t\t\t<shareUrlOriginal />\n\t\t\t<shareUrlOpen />\n\t\t\t<jsAppId />\n\t\t\t<publisherId />\n\t\t\t<publisherReqId />\n\t\t</webviewshared>\n\t\t<template_id />\n\t\t<md5 />\n\t\t<websearch>\n\t\t\t<rec_category>0</rec_category>\n\t\t\t<channelId>0</channelId>\n\t\t</websearch>\n\t\t<weappinfo>\n\t\t\t<username />\n\t\t\t<appid />\n\t\t\t<appservicetype>0</appservicetype>\n\t\t\t<secflagforsinglepagemode>0</secflagforsinglepagemode>\n\t\t\t<videopageinfo>\n\t\t\t\t<thumbwidth>0</thumbwidth>\n\t\t\t\t<thumbheight>0</thumbheight>\n\t\t\t\t<fromopensdk>0</fromopensdk>\n\t\t\t</videopageinfo>\n\t\t</weappinfo>\n\t\t<statextstr />\n\t\t<musicShareItem>\n\t\t\t<musicDuration>0</musicDuration>\n\t\t</musicShareItem>\n\t\t<finderLiveProductShare>\n\t\t\t<finderLiveID><![CDATA[]]></finderLiveID>\n\t\t\t<finderUsername><![CDATA[]]></finderUsername>\n\t\t\t<finderObjectID><![CDATA[]]></finderObjectID>\n\t\t\t<finderNonceID><![CDATA[]]></finderNonceID>\n\t\t\t<liveStatus><![CDATA[]]></liveStatus>\n\t\t\t<appId><![CDATA[]]></appId>\n\t\t\t<pagePath><![CDATA[]]></pagePath>\n\t\t\t<productId><![CDATA[]]></productId>\n\t\t\t<coverUrl><![CDATA[]]></coverUrl>\n\t\t\t<productTitle><![CDATA[]]></productTitle>\n\t\t\t<marketPrice><![CDATA[0]]></marketPrice>\n\t\t\t<sellingPrice><![CDATA[0]]></sellingPrice>\n\t\t\t<platformHeadImg><![CDATA[]]></platformHeadImg>\n\t\t\t<platformName><![CDATA[]]></platformName>\n\t\t\t<shopWindowId><![CDATA[]]></shopWindowId>\n\t\t\t<flashSalePrice><![CDATA[0]]></flashSalePrice>\n\t\t\t<flashSaleEndTime><![CDATA[0]]></flashSaleEndTime>\n\t\t\t<ecSource><![CDATA[]]></ecSource>\n\t\t\t<sellingPriceWording><![CDATA[]]></sellingPriceWording>\n\t\t\t<platformIconURL><![CDATA[]]></platformIconURL>\n\t\t\t<firstProductTagURL><![CDATA[]]></firstProductTagURL>\n\t\t\t<firstProductTagAspectRatioString><![CDATA[0.0]]></firstProductTagAspectRatioString>\n\t\t\t<secondProductTagURL><![CDATA[]]></secondProductTagURL>\n\t\t\t<secondProductTagAspectRatioString><![CDATA[0.0]]></secondProductTagAspectRatioString>\n\t\t\t<firstGuaranteeWording><![CDATA[]]></firstGuaranteeWording>\n\t\t\t<secondGuaranteeWording><![CDATA[]]></secondGuaranteeWording>\n\t\t\t<thirdGuaranteeWording><![CDATA[]]></thirdGuaranteeWording>\n\t\t\t<isPriceBeginShow>false</isPriceBeginShow>\n\t\t\t<lastGMsgID><![CDATA[]]></lastGMsgID>\n\t\t\t<promoterKey><![CDATA[]]></promoterKey>\n\t\t\t<discountWording><![CDATA[]]></discountWording>\n\t\t\t<priceSuffixDescription><![CDATA[]]></priceSuffixDescription>\n\t\t\t<productCardKey><![CDATA[]]></productCardKey>\n\t\t\t<isWxShop><![CDATA[]]></isWxShop>\n\t\t\t<brandIconUrl><![CDATA[]]></brandIconUrl>\n\t\t\t<rIconUrl><![CDATA[]]></rIconUrl>\n\t\t\t<rIconUrlDarkMode><![CDATA[]]></rIconUrlDarkMode>\n\t\t\t<topShopIconUrl><![CDATA[]]></topShopIconUrl>\n\t\t\t<topShopIconUrlDarkMode><![CDATA[]]></topShopIconUrlDarkMode>\n\t\t\t<simplifyTopShopIconUrl><![CDATA[]]></simplifyTopShopIconUrl>\n\t\t\t<simplifyTopShopIconUrlDarkmode><![CDATA[]]></simplifyTopShopIconUrlDarkmode>\n\t\t\t<topShopIconWidth><![CDATA[0]]></topShopIconWidth>\n\t\t\t<topShopIconHeight><![CDATA[0]]></topShopIconHeight>\n\t\t\t<simplifyTopShopIconWidth><![CDATA[0]]></simplifyTopShopIconWidth>\n\t\t\t<simplifyTopShopIconHeight><![CDATA[0]]></simplifyTopShopIconHeight>\n\t\t\t<showBoxItemStringList />\n\t\t</finderLiveProductShare>\n\t\t<finderOrder>\n\t\t\t<appID><![CDATA[]]></appID>\n\t\t\t<orderID><![CDATA[]]></orderID>\n\t\t\t<path><![CDATA[]]></path>\n\t\t\t<priceWording><![CDATA[]]></priceWording>\n\t\t\t<stateWording><![CDATA[]]></stateWording>\n\t\t\t<productImageURL><![CDATA[]]></productImageURL>\n\t\t\t<products><![CDATA[]]></products>\n\t\t\t<productsCount><![CDATA[0]]></productsCount>\n\t\t\t<orderType><![CDATA[0]]></orderType>\n\t\t\t<newPriceWording><![CDATA[]]></newPriceWording>\n\t\t\t<newStateWording><![CDATA[]]></newStateWording>\n\t\t\t<useNewWording><![CDATA[0]]></useNewWording>\n\t\t</finderOrder>\n\t\t<finderShopWindowShare>\n\t\t\t<finderUsername><![CDATA[]]></finderUsername>\n\t\t\t<avatar><![CDATA[]]></avatar>\n\t\t\t<nickname><![CDATA[]]></nickname>\n\t\t\t<commodityInStockCount><![CDATA[]]></commodityInStockCount>\n\t\t\t<appId><![CDATA[]]></appId>\n\t\t\t<path><![CDATA[]]></path>\n\t\t\t<appUsername><![CDATA[]]></appUsername>\n\t\t\t<query><![CDATA[]]></query>\n\t\t\t<liteAppId><![CDATA[]]></liteAppId>\n\t\t\t<liteAppPath><![CDATA[]]></liteAppPath>\n\t\t\t<liteAppQuery><![CDATA[]]></liteAppQuery>\n\t\t\t<platformTagURL><![CDATA[]]></platformTagURL>\n\t\t\t<saleWording><![CDATA[]]></saleWording>\n\t\t\t<lastGMsgID><![CDATA[]]></lastGMsgID>\n\t\t\t<profileTypeWording><![CDATA[]]></profileTypeWording>\n\t\t\t<saleWordingExtra><![CDATA[]]></saleWordingExtra>\n\t\t\t<isWxShop><![CDATA[]]></isWxShop>\n\t\t\t<platformIconUrl><![CDATA[]]></platformIconUrl>\n\t\t\t<brandIconUrl><![CDATA[]]></brandIconUrl>\n\t\t\t<description><![CDATA[]]></description>\n\t\t\t<backgroundUrl><![CDATA[]]></backgroundUrl>\n\t\t\t<darkModePlatformIconUrl><![CDATA[]]></darkModePlatformIconUrl>\n\t\t\t<rIconUrl><![CDATA[]]></rIconUrl>\n\t\t\t<rIconUrlDarkMode><![CDATA[]]></rIconUrlDarkMode>\n\t\t\t<rWords><![CDATA[]]></rWords>\n\t\t\t<topShopIconUrl><![CDATA[]]></topShopIconUrl>\n\t\t\t<topShopIconUrlDarkMode><![CDATA[]]></topShopIconUrlDarkMode>\n\t\t\t<simplifyTopShopIconUrl><![CDATA[]]></simplifyTopShopIconUrl>\n\t\t\t<simplifyTopShopIconUrlDarkmode><![CDATA[]]></simplifyTopShopIconUrlDarkmode>\n\t\t\t<topShopIconWidth><![CDATA[0]]></topShopIconWidth>\n\t\t\t<topShopIconHeight><![CDATA[0]]></topShopIconHeight>\n\t\t\t<simplifyTopShopIconWidth><![CDATA[0]]></simplifyTopShopIconWidth>\n\t\t\t<simplifyTopShopIconHeight><![CDATA[0]]></simplifyTopShopIconHeight>\n\t\t\t<reputationInfo>\n\t\t\t\t<hasReputationInfo>0</hasReputationInfo>\n\t\t\t\t<reputationScore>0</reputationScore>\n\t\t\t\t<reputationWording />\n\t\t\t\t<reputationTextColor />\n\t\t\t\t<reputationLevelWording />\n\t\t\t\t<reputationBackgroundColor />\n\t\t\t</reputationInfo>\n\t\t\t<productImageURLList />\n\t\t</finderShopWindowShare>\n\t\t<findernamecard>\n\t\t\t<username />\n\t\t\t<avatar><![CDATA[]]></avatar>\n\t\t\t<nickname />\n\t\t\t<auth_job />\n\t\t\t<auth_icon>0</auth_icon>\n\t\t\t<auth_icon_url />\n\t\t\t<ecSource><![CDATA[]]></ecSource>\n\t\t\t<lastGMsgID><![CDATA[]]></lastGMsgID>\n\t\t</findernamecard>\n\t\t<finderGuarantee>\n\t\t\t<scene><![CDATA[0]]></scene>\n\t\t</finderGuarantee>\n\t\t<directshare>0</directshare>\n\t\t<gamecenter>\n\t\t\t<namecard>\n\t\t\t\t<iconUrl />\n\t\t\t\t<name />\n\t\t\t\t<desc />\n\t\t\t\t<tail />\n\t\t\t\t<jumpUrl />\n\t\t\t\t<liteappId />\n\t\t\t\t<liteappPath />\n\t\t\t\t<liteappQuery />\n\t\t\t\t<liteappMinVersion />\n\t\t\t</namecard>\n\t\t</gamecenter>\n\t\t<patMsg>\n\t\t\t<chatUser />\n\t\t\t<records>\n\t\t\t\t<recordNum>0</recordNum>\n\t\t\t</records>\n\t\t</patMsg>\n\t\t<secretmsg>\n\t\t\t<issecretmsg>0</issecretmsg>\n\t\t</secretmsg>\n\t\t<referfromscene>0</referfromscene>\n\t\t<gameshare>\n\t\t\t<liteappext>\n\t\t\t\t<liteappbizdata />\n\t\t\t\t<priority>0</priority>\n\t\t\t</liteappext>\n\t\t\t<appbrandext>\n\t\t\t\t<litegameinfo />\n\t\t\t\t<priority>-1</priority>\n\t\t\t</appbrandext>\n\t\t\t<gameshareid />\n\t\t\t<sharedata />\n\t\t\t<isvideo>0</isvideo>\n\t\t\t<duration>-1</duration>\n\t\t\t<isexposed>0</isexposed>\n\t\t\t<readtext />\n\t\t</gameshare>\n\t\t<tingChatRoomItem>\n\t\t\t<type>0</type>\n\t\t\t<categoryItem>null</categoryItem>\n\t\t\t<categoryId />\n\t\t</tingChatRoomItem>\n\t\t<mpsharetrace>\n\t\t\t<hasfinderelement>0</hasfinderelement>\n\t\t\t<lastgmsgid />\n\t\t</mpsharetrace>\n\t\t<wxgamecard>\n\t\t\t<framesetname />\n\t\t\t<mbcarddata />\n\t\t\t<minpkgversion />\n\t\t\t<clientextinfo />\n\t\t\t<mbcardheight>0</mbcardheight>\n\t\t\t<isoldversion>0</isoldversion>\n\t\t</wxgamecard>\n\t\t<ecskfcard>\n\t\t\t<framesetname />\n\t\t\t<mbcarddata />\n\t\t\t<minupdateunixtimestamp>0</minupdateunixtimestamp>\n\t\t\t<needheader>false</needheader>\n\t\t\t<summary />\n\t\t</ecskfcard>\n\t\t<liteapp>\n\t\t\t<id>null</id>\n\t\t\t<path />\n\t\t\t<query />\n\t\t\t<istransparent>0</istransparent>\n\t\t\t<hideicon>0</hideicon>\n\t\t\t<forbidforward>0</forbidforward>\n\t\t</liteapp>\n\t\t<opensdk_share_is_modified>0</opensdk_share_is_modified>\n\t</appmsg>\n\t<fromusername>wxid_wlnzvr8ivgd422</fromusername>\n\t<scene>0</scene>\n\t<appinfo>\n\t\t<version>1</version>\n\t\t<appname></appname>\n\t</appinfo>\n\t<commenturl></commenturl>\n</msg>\n'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1753180805, 'MsgSource': '<msgsource>\n\t<alnode>\n\t\t<fr>4</fr>\n\t</alnode>\n\t<eggIncluded>1</eggIncluded>\n\t<sec_msg_node>\n\t\t<uuid>1ebd709aded1fd4a1e5d71b5b689de6b_</uuid>\n\t\t<risk-file-flag />\n\t\t<risk-file-md5-list />\n\t</sec_msg_node>\n\t<silence>0</silence>\n\t<membercount>62</membercount>\n\t<signature>N0_V1_hVnABFl5|v1_V9atsTe9</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'PushContent': '锦岚（开玩笑就退群版） : 我才不要，老板太难伺候', 'NewMsgId': 3572492784903331373, 'MsgSeq': 871392671}
2025-07-22 18:39:52 | DEBUG | 从群聊消息中提取发送者: wxid_wlnzvr8ivgd422
2025-07-22 18:39:52 | DEBUG | 使用已解析的XML处理引用消息
2025-07-22 18:39:52 | INFO | 收到引用消息: 消息ID:385934724 来自:48097389945@chatroom 发送人:wxid_wlnzvr8ivgd422 内容:我才不要，老板太难伺候 引用类型:1
2025-07-22 18:39:53 | INFO | [DouBaoImageToImage] 收到引用消息: 我才不要，老板太难伺候
2025-07-22 18:39:53 | INFO | [TimerTask] 收到引用消息调试信息:
2025-07-22 18:39:53 | INFO |   - 消息内容: 我才不要，老板太难伺候
2025-07-22 18:39:53 | INFO |   - 群组ID: 48097389945@chatroom
2025-07-22 18:39:53 | INFO |   - 发送人: wxid_wlnzvr8ivgd422
2025-07-22 18:39:53 | INFO |   - 引用信息: {'MsgType': 1, 'Content': '\n你要当行政了吗', 'Msgid': '7794469446990082250', 'NewMsgId': '7794469446990082250', 'ToWxid': 'wxid_4usgcju5ey9q29', 'FromWxid': '48097389945@chatroom', 'Nickname': '小爱', 'MsgSource': '<msgsource>\n\t<pua>1</pua>\n\t<silence>1</silence>\n\t<membercount>62</membercount>\n\t<signature>N0_V1_9oXLg/Of|v1_HniAlsxI</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'Createtime': '1753180352', 'SenderWxid': 'wxid_wlnzvr8ivgd422'}
2025-07-22 18:39:53 | INFO |   - 引用消息ID: 
2025-07-22 18:39:53 | INFO |   - 引用消息类型: 
2025-07-22 18:39:53 | INFO |   - 引用消息内容: 
你要当行政了吗
2025-07-22 18:39:53 | INFO |   - 引用消息发送人: wxid_wlnzvr8ivgd422
2025-07-22 18:40:03 | DEBUG | 收到消息: {'MsgId': 1202053795, 'FromUserName': {'string': '48097389945@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 1, 'Content': {'string': 'wxid_wlnzvr8ivgd422:\n我这个岗，和她接触不到'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1753180816, 'MsgSource': '<msgsource>\n\t<pua>1</pua>\n\t<eggIncluded>1</eggIncluded>\n\t<silence>0</silence>\n\t<membercount>62</membercount>\n\t<signature>N0_V1_C88p/dJ8|v1_Bwim3vzT</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'PushContent': '锦岚（开玩笑就退群版） : 我这个岗，和她接触不到', 'NewMsgId': 2451323153645403475, 'MsgSeq': 871392672}
2025-07-22 18:40:03 | INFO | 收到文本消息: 消息ID:1202053795 来自:48097389945@chatroom 发送人:wxid_wlnzvr8ivgd422 @:[] 内容:我这个岗，和她接触不到
2025-07-22 18:40:04 | DEBUG | 处理消息内容: '我这个岗，和她接触不到'
2025-07-22 18:40:04 | DEBUG | 消息内容 '我这个岗，和她接触不到' 不匹配任何命令，忽略
2025-07-22 18:40:08 | DEBUG | 收到消息: {'MsgId': 1574639174, 'FromUserName': {'string': '48097389945@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 1, 'Content': {'string': 'wxid_wlnzvr8ivgd422:\n才能干这么久'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1753180821, 'MsgSource': '<msgsource>\n\t<pua>1</pua>\n\t<eggIncluded>1</eggIncluded>\n\t<silence>0</silence>\n\t<membercount>62</membercount>\n\t<signature>N0_V1_Tg62RTDz|v1_7+2EJZvD</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'PushContent': '锦岚（开玩笑就退群版） : 才能干这么久', 'NewMsgId': 6476862325089469248, 'MsgSeq': 871392673}
2025-07-22 18:40:08 | INFO | 收到文本消息: 消息ID:1574639174 来自:48097389945@chatroom 发送人:wxid_wlnzvr8ivgd422 @:[] 内容:才能干这么久
2025-07-22 18:40:09 | DEBUG | 处理消息内容: '才能干这么久'
2025-07-22 18:40:09 | DEBUG | 消息内容 '才能干这么久' 不匹配任何命令，忽略
2025-07-22 18:40:12 | DEBUG | 收到消息: {'MsgId': 1050888550, 'FromUserName': {'string': '48097389945@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 49, 'Content': {'string': 'wxid_hqdtktnqvw8e21:\n<?xml version="1.0"?>\n<msg>\n\t<appmsg appid="" sdkver="0">\n\t\t<title>同款自行车[Doge]</title>\n\t\t<type>57</type>\n\t\t<appattach>\n\t\t\t<cdnthumbaeskey />\n\t\t\t<aeskey />\n\t\t</appattach>\n\t\t<refermsg>\n\t\t\t<type>3</type>\n\t\t\t<svrid>2891487413926356363</svrid>\n\t\t\t<fromusr>48097389945@chatroom</fromusr>\n\t\t\t<chatusr>wxid_wlnzvr8ivgd422</chatusr>\n\t\t\t<displayname>锦岚（开玩笑就退群版）</displayname>\n\t\t\t<content>&lt;?xml version="1.0"?&gt;\n&lt;msg&gt;\n\t&lt;img aeskey="d1f2d8dfbf1ad1c97acc9fc248ee807b" encryver="1" cdnthumbaeskey="d1f2d8dfbf1ad1c97acc9fc248ee807b" cdnthumburl="3057020100044b3049020100020468cde53a02032f501e020457845ad30204687f687e042431636664643237622d326463322d343336612d626238362d343362663735623337353761020405250a020201000405004c4dfe00" cdnthumblength="5273" cdnthumbheight="432" cdnthumbwidth="244" cdnmidheight="0" cdnmidwidth="0" cdnhdheight="0" cdnhdwidth="0" cdnmidimgurl="3057020100044b3049020100020468cde53a02032f501e020457845ad30204687f687e042431636664643237622d326463322d343336612d626238362d343362663735623337353761020405250a020201000405004c4dfe00" length="905676" md5="c3a463d01c258c05bba20e457adc5047" hevc_mid_size="64292" originsourcemd5="c26adc49c67b78f8c4afc228d5069546"&gt;\n\t\t&lt;secHashInfoBase64&gt;eyJwaGFzaCI6IjMxNzEwMTgwMTIxMjgyODAiLCJwZHFIYXNoIjoiZGM1OWRjNzY5Yzc1OTc2NWE2\nNzZhMzcyMGI1MjZhMWU2ODFlNGE4ZGNhODU2YzI3ZWU2MDhhZDlhZTQ4YWUyMyJ9\n&lt;/secHashInfoBase64&gt;\n\t\t&lt;live&gt;\n\t\t\t&lt;duration&gt;0&lt;/duration&gt;\n\t\t\t&lt;size&gt;0&lt;/size&gt;\n\t\t\t&lt;md5 /&gt;\n\t\t\t&lt;fileid /&gt;\n\t\t\t&lt;hdsize&gt;0&lt;/hdsize&gt;\n\t\t\t&lt;hdmd5 /&gt;\n\t\t\t&lt;hdfileid /&gt;\n\t\t\t&lt;stillimagetimems&gt;0&lt;/stillimagetimems&gt;\n\t\t&lt;/live&gt;\n\t&lt;/img&gt;\n\t&lt;platform_signature /&gt;\n\t&lt;imgdatahash /&gt;\n\t&lt;ImgSourceInfo&gt;\n\t\t&lt;ImgSourceUrl /&gt;\n\t\t&lt;BizType&gt;0&lt;/BizType&gt;\n\t&lt;/ImgSourceInfo&gt;\n&lt;/msg&gt;\n</content>\n\t\t\t<msgsource>&lt;msgsource&gt;&lt;svr_create_time&gt;1753180286&lt;/svr_create_time&gt;&lt;sequence_id&gt;835250110&lt;/sequence_id&gt;\n\t&lt;sec_msg_node&gt;\n\t\t&lt;uuid&gt;4abebec16d49fe4d5f4842a0b48c06d7_&lt;/uuid&gt;\n\t\t&lt;risk-file-flag /&gt;\n\t\t&lt;risk-file-md5-list /&gt;\n\t&lt;/sec_msg_node&gt;\n\t&lt;imgmsg_pd cdnmidimgurl_size="64292" /&gt;\n\t&lt;silence&gt;1&lt;/silence&gt;\n\t&lt;membercount&gt;62&lt;/membercount&gt;\n\t&lt;NotAutoDownloadRange&gt;20:00-22:00;00:00-01:00&lt;/NotAutoDownloadRange&gt;\n\t&lt;signature&gt;N0_V1_01n/YgWV|v1_ANdmyjDA&lt;/signature&gt;\n\t&lt;tmp_node&gt;\n\t\t&lt;publisher-id&gt;&lt;/publisher-id&gt;\n\t&lt;/tmp_node&gt;\n&lt;/msgsource&gt;\n</msgsource>\n\t\t\t<createtime>1753180286</createtime>\n\t\t</refermsg>\n\t</appmsg>\n\t<fromusername>wxid_hqdtktnqvw8e21</fromusername>\n\t<scene>0</scene>\n\t<appinfo>\n\t\t<version>1</version>\n\t\t<appname></appname>\n\t</appinfo>\n\t<commenturl></commenturl>\n</msg>\n'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1753180824, 'MsgSource': '<msgsource>\n\t<bizflag>0</bizflag>\n\t<pua>1</pua>\n\t<sec_msg_node>\n\t\t<uuid>575f8943f0356befc7aa76da4d844fdd_</uuid>\n\t\t<risk-file-flag />\n\t\t<risk-file-md5-list />\n\t</sec_msg_node>\n\t<silence>0</silence>\n\t<membercount>62</membercount>\n\t<signature>N0_V1_7aAj4ONQ|v1_kPfzT5gG</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'PushContent': 'Ritz : 同款自行车[旺柴]', 'NewMsgId': 4178126636718273630, 'MsgSeq': 871392674}
2025-07-22 18:40:12 | DEBUG | 从群聊消息中提取发送者: wxid_hqdtktnqvw8e21
2025-07-22 18:40:12 | DEBUG | 使用已解析的XML处理引用消息
2025-07-22 18:40:12 | INFO | 收到引用消息: 消息ID:1050888550 来自:48097389945@chatroom 发送人:wxid_hqdtktnqvw8e21 内容:同款自行车[Doge] 引用类型:3
2025-07-22 18:40:12 | INFO | [DouBaoImageToImage] 收到引用消息: 同款自行车[Doge]
2025-07-22 18:40:12 | INFO | [TimerTask] 收到引用消息调试信息:
2025-07-22 18:40:12 | INFO |   - 消息内容: 同款自行车[Doge]
2025-07-22 18:40:12 | INFO |   - 群组ID: 48097389945@chatroom
2025-07-22 18:40:12 | INFO |   - 发送人: wxid_hqdtktnqvw8e21
2025-07-22 18:40:12 | INFO |   - 引用信息: {'MsgType': 3, 'Content': '<?xml version="1.0"?>\n<msg>\n\t<appmsg appid="" sdkver="0">\n\t\t<title>同款自行车[Doge]</title>\n\t\t<type>57</type>\n\t\t<appattach>\n\t\t\t<cdnthumbaeskey />\n\t\t\t<aeskey />\n\t\t</appattach>\n\t\t<refermsg>\n\t\t\t<type>3</type>\n\t\t\t<svrid>2891487413926356363</svrid>\n\t\t\t<fromusr>48097389945@chatroom</fromusr>\n\t\t\t<chatusr>wxid_wlnzvr8ivgd422</chatusr>\n\t\t\t<displayname>锦岚（开玩笑就退群版）</displayname>\n\t\t\t<content>&lt;?xml version="1.0"?&gt;\n&lt;msg&gt;\n\t&lt;img aeskey="d1f2d8dfbf1ad1c97acc9fc248ee807b" encryver="1" cdnthumbaeskey="d1f2d8dfbf1ad1c97acc9fc248ee807b" cdnthumburl="3057020100044b3049020100020468cde53a02032f501e020457845ad30204687f687e042431636664643237622d326463322d343336612d626238362d343362663735623337353761020405250a020201000405004c4dfe00" cdnthumblength="5273" cdnthumbheight="432" cdnthumbwidth="244" cdnmidheight="0" cdnmidwidth="0" cdnhdheight="0" cdnhdwidth="0" cdnmidimgurl="3057020100044b3049020100020468cde53a02032f501e020457845ad30204687f687e042431636664643237622d326463322d343336612d626238362d343362663735623337353761020405250a020201000405004c4dfe00" length="905676" md5="c3a463d01c258c05bba20e457adc5047" hevc_mid_size="64292" originsourcemd5="c26adc49c67b78f8c4afc228d5069546"&gt;\n\t\t&lt;secHashInfoBase64&gt;eyJwaGFzaCI6IjMxNzEwMTgwMTIxMjgyODAiLCJwZHFIYXNoIjoiZGM1OWRjNzY5Yzc1OTc2NWE2\nNzZhMzcyMGI1MjZhMWU2ODFlNGE4ZGNhODU2YzI3ZWU2MDhhZDlhZTQ4YWUyMyJ9\n&lt;/secHashInfoBase64&gt;\n\t\t&lt;live&gt;\n\t\t\t&lt;duration&gt;0&lt;/duration&gt;\n\t\t\t&lt;size&gt;0&lt;/size&gt;\n\t\t\t&lt;md5 /&gt;\n\t\t\t&lt;fileid /&gt;\n\t\t\t&lt;hdsize&gt;0&lt;/hdsize&gt;\n\t\t\t&lt;hdmd5 /&gt;\n\t\t\t&lt;hdfileid /&gt;\n\t\t\t&lt;stillimagetimems&gt;0&lt;/stillimagetimems&gt;\n\t\t&lt;/live&gt;\n\t&lt;/img&gt;\n\t&lt;platform_signature /&gt;\n\t&lt;imgdatahash /&gt;\n\t&lt;ImgSourceInfo&gt;\n\t\t&lt;ImgSourceUrl /&gt;\n\t\t&lt;BizType&gt;0&lt;/BizType&gt;\n\t&lt;/ImgSourceInfo&gt;\n&lt;/msg&gt;\n</content>\n\t\t\t<msgsource>&lt;msgsource&gt;&lt;svr_create_time&gt;1753180286&lt;/svr_create_time&gt;&lt;sequence_id&gt;835250110&lt;/sequence_id&gt;\n\t&lt;sec_msg_node&gt;\n\t\t&lt;uuid&gt;4abebec16d49fe4d5f4842a0b48c06d7_&lt;/uuid&gt;\n\t\t&lt;risk-file-flag /&gt;\n\t\t&lt;risk-file-md5-list /&gt;\n\t&lt;/sec_msg_node&gt;\n\t&lt;imgmsg_pd cdnmidimgurl_size="64292" /&gt;\n\t&lt;silence&gt;1&lt;/silence&gt;\n\t&lt;membercount&gt;62&lt;/membercount&gt;\n\t&lt;NotAutoDownloadRange&gt;20:00-22:00;00:00-01:00&lt;/NotAutoDownloadRange&gt;\n\t&lt;signature&gt;N0_V1_01n/YgWV|v1_ANdmyjDA&lt;/signature&gt;\n\t&lt;tmp_node&gt;\n\t\t&lt;publisher-id&gt;&lt;/publisher-id&gt;\n\t&lt;/tmp_node&gt;\n&lt;/msgsource&gt;\n</msgsource>\n\t\t\t<createtime>1753180286</createtime>\n\t\t</refermsg>\n\t</appmsg>\n\t<fromusername>wxid_hqdtktnqvw8e21</fromusername>\n\t<scene>0</scene>\n\t<appinfo>\n\t\t<version>1</version>\n\t\t<appname></appname>\n\t</appinfo>\n\t<commenturl></commenturl>\n</msg>\n', 'Msgid': '2891487413926356363', 'NewMsgId': '2891487413926356363', 'ToWxid': 'wxid_4usgcju5ey9q29', 'FromWxid': '48097389945@chatroom', 'Nickname': '锦岚（开玩笑就退群版）', 'MsgSource': '<msgsource><svr_create_time>1753180286</svr_create_time><sequence_id>835250110</sequence_id>\n\t<sec_msg_node>\n\t\t<uuid>4abebec16d49fe4d5f4842a0b48c06d7_</uuid>\n\t\t<risk-file-flag />\n\t\t<risk-file-md5-list />\n\t</sec_msg_node>\n\t<imgmsg_pd cdnmidimgurl_size="64292" />\n\t<silence>1</silence>\n\t<membercount>62</membercount>\n\t<NotAutoDownloadRange>20:00-22:00;00:00-01:00</NotAutoDownloadRange>\n\t<signature>N0_V1_01n/YgWV|v1_ANdmyjDA</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'Createtime': '1753180286', 'SenderWxid': 'wxid_hqdtktnqvw8e21'}
2025-07-22 18:40:12 | INFO |   - 引用消息ID: 
2025-07-22 18:40:12 | INFO |   - 引用消息类型: 
2025-07-22 18:40:12 | INFO |   - 引用消息内容: <?xml version="1.0"?>
<msg>
	<appmsg appid="" sdkver="0">
		<title>同款自行车[Doge]</title>
		<type>57</type>
		<appattach>
			<cdnthumbaeskey />
			<aeskey />
		</appattach>
		<refermsg>
			<type>3</type>
			<svrid>2891487413926356363</svrid>
			<fromusr>48097389945@chatroom</fromusr>
			<chatusr>wxid_wlnzvr8ivgd422</chatusr>
			<displayname>锦岚（开玩笑就退群版）</displayname>
			<content>&lt;?xml version="1.0"?&gt;
&lt;msg&gt;
	&lt;img aeskey="d1f2d8dfbf1ad1c97acc9fc248ee807b" encryver="1" cdnthumbaeskey="d1f2d8dfbf1ad1c97acc9fc248ee807b" cdnthumburl="3057020100044b3049020100020468cde53a02032f501e020457845ad30204687f687e042431636664643237622d326463322d343336612d626238362d343362663735623337353761020405250a020201000405004c4dfe00" cdnthumblength="5273" cdnthumbheight="432" cdnthumbwidth="244" cdnmidheight="0" cdnmidwidth="0" cdnhdheight="0" cdnhdwidth="0" cdnmidimgurl="3057020100044b3049020100020468cde53a02032f501e020457845ad30204687f687e042431636664643237622d326463322d343336612d626238362d343362663735623337353761020405250a020201000405004c4dfe00" length="905676" md5="c3a463d01c258c05bba20e457adc5047" hevc_mid_size="64292" originsourcemd5="c26adc49c67b78f8c4afc228d5069546"&gt;
		&lt;secHashInfoBase64&gt;eyJwaGFzaCI6IjMxNzEwMTgwMTIxMjgyODAiLCJwZHFIYXNoIjoiZGM1OWRjNzY5Yzc1OTc2NWE2
NzZhMzcyMGI1MjZhMWU2ODFlNGE4ZGNhODU2YzI3ZWU2MDhhZDlhZTQ4YWUyMyJ9
&lt;/secHashInfoBase64&gt;
		&lt;live&gt;
			&lt;duration&gt;0&lt;/duration&gt;
			&lt;size&gt;0&lt;/size&gt;
			&lt;md5 /&gt;
			&lt;fileid /&gt;
			&lt;hdsize&gt;0&lt;/hdsize&gt;
			&lt;hdmd5 /&gt;
			&lt;hdfileid /&gt;
			&lt;stillimagetimems&gt;0&lt;/stillimagetimems&gt;
		&lt;/live&gt;
	&lt;/img&gt;
	&lt;platform_signature /&gt;
	&lt;imgdatahash /&gt;
	&lt;ImgSourceInfo&gt;
		&lt;ImgSourceUrl /&gt;
		&lt;BizType&gt;0&lt;/BizType&gt;
	&lt;/ImgSourceInfo&gt;
&lt;/msg&gt;
</content>
			<msgsource>&lt;msgsource&gt;&lt;svr_create_time&gt;1753180286&lt;/svr_create_time&gt;&lt;sequence_id&gt;835250110&lt;/sequence_id&gt;
	&lt;sec_msg_node&gt;
		&lt;uuid&gt;4abebec16d49fe4d5f4842a0b48c06d7_&lt;/uuid&gt;
		&lt;risk-file-flag /&gt;
		&lt;risk-file-md5-list /&gt;
	&lt;/sec_msg_node&gt;
	&lt;imgmsg_pd cdnmidimgurl_size="64292" /&gt;
	&lt;silence&gt;1&lt;/silence&gt;
	&lt;membercount&gt;62&lt;/membercount&gt;
	&lt;NotAutoDownloadRange&gt;20:00-22:00;00:00-01:00&lt;/NotAutoDownloadRange&gt;
	&lt;signature&gt;N0_V1_01n/YgWV|v1_ANdmyjDA&lt;/signature&gt;
	&lt;tmp_node&gt;
		&lt;publisher-id&gt;&lt;/publisher-id&gt;
	&lt;/tmp_node&gt;
&lt;/msgsource&gt;
</msgsource>
			<createtime>1753180286</createtime>
		</refermsg>
	</appmsg>
	<fromusername>wxid_hqdtktnqvw8e21</fromusername>
	<scene>0</scene>
	<appinfo>
		<version>1</version>
		<appname></appname>
	</appinfo>
	<commenturl></commenturl>
</msg>

2025-07-22 18:40:12 | INFO |   - 引用消息发送人: wxid_hqdtktnqvw8e21
2025-07-22 18:40:17 | DEBUG | 收到消息: {'MsgId': 1684145018, 'FromUserName': {'string': '48097389945@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 1, 'Content': {'string': 'wxid_wlnzvr8ivgd422:\n我干行政坚持不到两个月'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1753180830, 'MsgSource': '<msgsource>\n\t<pua>1</pua>\n\t<eggIncluded>1</eggIncluded>\n\t<silence>0</silence>\n\t<membercount>62</membercount>\n\t<signature>N0_V1_bBygEifg|v1_JEDbIKEA</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'PushContent': '锦岚（开玩笑就退群版） : 我干行政坚持不到两个月', 'NewMsgId': 7936815023432926821, 'MsgSeq': 871392675}
2025-07-22 18:40:17 | INFO | 收到文本消息: 消息ID:1684145018 来自:48097389945@chatroom 发送人:wxid_wlnzvr8ivgd422 @:[] 内容:我干行政坚持不到两个月
2025-07-22 18:40:17 | DEBUG | 处理消息内容: '我干行政坚持不到两个月'
2025-07-22 18:40:17 | DEBUG | 消息内容 '我干行政坚持不到两个月' 不匹配任何命令，忽略
2025-07-22 18:40:19 | DEBUG | 收到消息: {'MsgId': 588002264, 'FromUserName': {'string': '48097389945@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 1, 'Content': {'string': 'wxid_hqdtktnqvw8e21:\n啥岗位'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1753180830, 'MsgSource': '<msgsource>\n\t<bizflag>0</bizflag>\n\t<pua>1</pua>\n\t<silence>0</silence>\n\t<membercount>62</membercount>\n\t<signature>N0_V1_sJy3ERnt|v1_Y0FL5TO1</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'PushContent': 'Ritz : 啥岗位', 'NewMsgId': 2250640367744083669, 'MsgSeq': 871392676}
2025-07-22 18:40:19 | INFO | 收到文本消息: 消息ID:588002264 来自:48097389945@chatroom 发送人:wxid_hqdtktnqvw8e21 @:[] 内容:啥岗位
2025-07-22 18:40:19 | DEBUG | 处理消息内容: '啥岗位'
2025-07-22 18:40:19 | DEBUG | 消息内容 '啥岗位' 不匹配任何命令，忽略
2025-07-22 18:40:23 | DEBUG | 收到消息: {'MsgId': 697399912, 'FromUserName': {'string': '48097389945@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 1, 'Content': {'string': 'wxid_wlnzvr8ivgd422:\n就受不了了'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1753180836, 'MsgSource': '<msgsource>\n\t<pua>1</pua>\n\t<eggIncluded>1</eggIncluded>\n\t<silence>0</silence>\n\t<membercount>62</membercount>\n\t<signature>N0_V1_XY9tIw2J|v1_Nqdu2whI</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'PushContent': '锦岚（开玩笑就退群版） : 就受不了了', 'NewMsgId': 8075805792447535321, 'MsgSeq': 871392677}
2025-07-22 18:40:23 | INFO | 收到文本消息: 消息ID:697399912 来自:48097389945@chatroom 发送人:wxid_wlnzvr8ivgd422 @:[] 内容:就受不了了
2025-07-22 18:40:23 | DEBUG | 处理消息内容: '就受不了了'
2025-07-22 18:40:23 | DEBUG | 消息内容 '就受不了了' 不匹配任何命令，忽略
2025-07-22 18:40:26 | DEBUG | 收到消息: {'MsgId': 1770105094, 'FromUserName': {'string': '48097389945@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 1, 'Content': {'string': 'wxid_wlnzvr8ivgd422:\n她那脾气'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1753180839, 'MsgSource': '<msgsource>\n\t<pua>1</pua>\n\t<eggIncluded>1</eggIncluded>\n\t<silence>0</silence>\n\t<membercount>62</membercount>\n\t<signature>N0_V1_RHhcfQZL|v1_VfL4Pg5w</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'PushContent': '锦岚（开玩笑就退群版） : 她那脾气', 'NewMsgId': 8264288638098766532, 'MsgSeq': 871392678}
2025-07-22 18:40:26 | INFO | 收到文本消息: 消息ID:1770105094 来自:48097389945@chatroom 发送人:wxid_wlnzvr8ivgd422 @:[] 内容:她那脾气
2025-07-22 18:40:26 | DEBUG | 处理消息内容: '她那脾气'
2025-07-22 18:40:26 | DEBUG | 消息内容 '她那脾气' 不匹配任何命令，忽略
2025-07-22 18:40:36 | DEBUG | 收到消息: {'MsgId': 1474179639, 'FromUserName': {'string': '48097389945@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 49, 'Content': {'string': 'wxid_wlnzvr8ivgd422:\n<?xml version="1.0"?>\n<msg>\n\t<appmsg appid="" sdkver="0">\n\t\t<title>看看同款</title>\n\t\t<des />\n\t\t<username />\n\t\t<action>view</action>\n\t\t<type>57</type>\n\t\t<showtype>0</showtype>\n\t\t<content />\n\t\t<url />\n\t\t<lowurl />\n\t\t<forwardflag>0</forwardflag>\n\t\t<dataurl />\n\t\t<lowdataurl />\n\t\t<contentattr>0</contentattr>\n\t\t<streamvideo>\n\t\t\t<streamvideourl />\n\t\t\t<streamvideototaltime>0</streamvideototaltime>\n\t\t\t<streamvideotitle />\n\t\t\t<streamvideowording />\n\t\t\t<streamvideoweburl />\n\t\t\t<streamvideothumburl />\n\t\t\t<streamvideoaduxinfo />\n\t\t\t<streamvideopublishid />\n\t\t</streamvideo>\n\t\t<canvasPageItem>\n\t\t\t<canvasPageXml><![CDATA[]]></canvasPageXml>\n\t\t</canvasPageItem>\n\t\t<refermsg>\n\t\t\t<type>49</type>\n\t\t\t<svrid>4178126636718273630</svrid>\n\t\t\t<fromusr>48097389945@chatroom</fromusr>\n\t\t\t<chatusr>wxid_hqdtktnqvw8e21</chatusr>\n\t\t\t<displayname>Ritz</displayname>\n\t\t\t<msgsource>&lt;msgsource&gt;\n\t&lt;bizflag&gt;0&lt;/bizflag&gt;\n\t&lt;pua&gt;1&lt;/pua&gt;\n\t&lt;sec_msg_node&gt;\n\t\t&lt;uuid&gt;575f8943f0356befc7aa76da4d844fdd_&lt;/uuid&gt;\n\t\t&lt;risk-file-flag /&gt;\n\t\t&lt;risk-file-md5-list /&gt;\n\t&lt;/sec_msg_node&gt;\n\t&lt;silence&gt;1&lt;/silence&gt;\n\t&lt;membercount&gt;62&lt;/membercount&gt;\n\t&lt;signature&gt;N0_V1_U3QSTzmV|v1_NFYZ/3RY&lt;/signature&gt;\n\t&lt;tmp_node&gt;\n\t\t&lt;publisher-id&gt;&lt;/publisher-id&gt;\n\t&lt;/tmp_node&gt;\n&lt;/msgsource&gt;\n</msgsource>\n\t\t\t<content>&lt;?xml version="1.0"?&gt;\n&lt;msg&gt;\n\t&lt;appmsg appid="" sdkver="0"&gt;\n\t\t&lt;title&gt;同款自行车[Doge]&lt;/title&gt;\n\t\t&lt;type&gt;57&lt;/type&gt;\n\t\t&lt;appattach&gt;\n\t\t\t&lt;cdnthumbaeskey /&gt;\n\t\t\t&lt;aeskey /&gt;\n\t\t&lt;/appattach&gt;\n\t\t&lt;refermsg&gt;&lt;/refermsg&gt;\n\t&lt;/appmsg&gt;\n\t&lt;fromusername&gt;wxid_hqdtktnqvw8e21&lt;/fromusername&gt;\n\t&lt;scene&gt;0&lt;/scene&gt;\n\t&lt;appinfo&gt;\n\t\t&lt;version&gt;1&lt;/version&gt;\n\t\t&lt;appname&gt;&lt;/appname&gt;\n\t&lt;/appinfo&gt;\n\t&lt;commenturl&gt;&lt;/commenturl&gt;\n&lt;/msg&gt;\n</content>\n\t\t\t<strid />\n\t\t\t<createtime>1753180824</createtime>\n\t\t</refermsg>\n\t\t<appattach>\n\t\t\t<totallen>0</totallen>\n\t\t\t<attachid />\n\t\t\t<cdnattachurl />\n\t\t\t<emoticonmd5 />\n\t\t\t<aeskey />\n\t\t\t<fileext />\n\t\t\t<islargefilemsg>0</islargefilemsg>\n\t\t</appattach>\n\t\t<extinfo />\n\t\t<androidsource>0</androidsource>\n\t\t<thumburl />\n\t\t<mediatagname />\n\t\t<messageaction><![CDATA[]]></messageaction>\n\t\t<messageext><![CDATA[]]></messageext>\n\t\t<emoticongift>\n\t\t\t<packageflag>0</packageflag>\n\t\t\t<packageid />\n\t\t</emoticongift>\n\t\t<emoticonshared>\n\t\t\t<packageflag>0</packageflag>\n\t\t\t<packageid />\n\t\t</emoticonshared>\n\t\t<designershared>\n\t\t\t<designeruin>0</designeruin>\n\t\t\t<designername>null</designername>\n\t\t\t<designerrediretcturl><![CDATA[null]]></designerrediretcturl>\n\t\t</designershared>\n\t\t<emotionpageshared>\n\t\t\t<tid>0</tid>\n\t\t\t<title>null</title>\n\t\t\t<desc>null</desc>\n\t\t\t<iconUrl><![CDATA[null]]></iconUrl>\n\t\t\t<secondUrl>null</secondUrl>\n\t\t\t<pageType>0</pageType>\n\t\t\t<setKey>null</setKey>\n\t\t</emotionpageshared>\n\t\t<webviewshared>\n\t\t\t<shareUrlOriginal />\n\t\t\t<shareUrlOpen />\n\t\t\t<jsAppId />\n\t\t\t<publisherId />\n\t\t\t<publisherReqId />\n\t\t</webviewshared>\n\t\t<template_id />\n\t\t<md5 />\n\t\t<websearch>\n\t\t\t<rec_category>0</rec_category>\n\t\t\t<channelId>0</channelId>\n\t\t</websearch>\n\t\t<weappinfo>\n\t\t\t<username />\n\t\t\t<appid />\n\t\t\t<appservicetype>0</appservicetype>\n\t\t\t<secflagforsinglepagemode>0</secflagforsinglepagemode>\n\t\t\t<videopageinfo>\n\t\t\t\t<thumbwidth>0</thumbwidth>\n\t\t\t\t<thumbheight>0</thumbheight>\n\t\t\t\t<fromopensdk>0</fromopensdk>\n\t\t\t</videopageinfo>\n\t\t</weappinfo>\n\t\t<statextstr />\n\t\t<musicShareItem>\n\t\t\t<musicDuration>0</musicDuration>\n\t\t</musicShareItem>\n\t\t<finderLiveProductShare>\n\t\t\t<finderLiveID><![CDATA[]]></finderLiveID>\n\t\t\t<finderUsername><![CDATA[]]></finderUsername>\n\t\t\t<finderObjectID><![CDATA[]]></finderObjectID>\n\t\t\t<finderNonceID><![CDATA[]]></finderNonceID>\n\t\t\t<liveStatus><![CDATA[]]></liveStatus>\n\t\t\t<appId><![CDATA[]]></appId>\n\t\t\t<pagePath><![CDATA[]]></pagePath>\n\t\t\t<productId><![CDATA[]]></productId>\n\t\t\t<coverUrl><![CDATA[]]></coverUrl>\n\t\t\t<productTitle><![CDATA[]]></productTitle>\n\t\t\t<marketPrice><![CDATA[0]]></marketPrice>\n\t\t\t<sellingPrice><![CDATA[0]]></sellingPrice>\n\t\t\t<platformHeadImg><![CDATA[]]></platformHeadImg>\n\t\t\t<platformName><![CDATA[]]></platformName>\n\t\t\t<shopWindowId><![CDATA[]]></shopWindowId>\n\t\t\t<flashSalePrice><![CDATA[0]]></flashSalePrice>\n\t\t\t<flashSaleEndTime><![CDATA[0]]></flashSaleEndTime>\n\t\t\t<ecSource><![CDATA[]]></ecSource>\n\t\t\t<sellingPriceWording><![CDATA[]]></sellingPriceWording>\n\t\t\t<platformIconURL><![CDATA[]]></platformIconURL>\n\t\t\t<firstProductTagURL><![CDATA[]]></firstProductTagURL>\n\t\t\t<firstProductTagAspectRatioString><![CDATA[0.0]]></firstProductTagAspectRatioString>\n\t\t\t<secondProductTagURL><![CDATA[]]></secondProductTagURL>\n\t\t\t<secondProductTagAspectRatioString><![CDATA[0.0]]></secondProductTagAspectRatioString>\n\t\t\t<firstGuaranteeWording><![CDATA[]]></firstGuaranteeWording>\n\t\t\t<secondGuaranteeWording><![CDATA[]]></secondGuaranteeWording>\n\t\t\t<thirdGuaranteeWording><![CDATA[]]></thirdGuaranteeWording>\n\t\t\t<isPriceBeginShow>false</isPriceBeginShow>\n\t\t\t<lastGMsgID><![CDATA[]]></lastGMsgID>\n\t\t\t<promoterKey><![CDATA[]]></promoterKey>\n\t\t\t<discountWording><![CDATA[]]></discountWording>\n\t\t\t<priceSuffixDescription><![CDATA[]]></priceSuffixDescription>\n\t\t\t<productCardKey><![CDATA[]]></productCardKey>\n\t\t\t<isWxShop><![CDATA[]]></isWxShop>\n\t\t\t<brandIconUrl><![CDATA[]]></brandIconUrl>\n\t\t\t<rIconUrl><![CDATA[]]></rIconUrl>\n\t\t\t<rIconUrlDarkMode><![CDATA[]]></rIconUrlDarkMode>\n\t\t\t<topShopIconUrl><![CDATA[]]></topShopIconUrl>\n\t\t\t<topShopIconUrlDarkMode><![CDATA[]]></topShopIconUrlDarkMode>\n\t\t\t<simplifyTopShopIconUrl><![CDATA[]]></simplifyTopShopIconUrl>\n\t\t\t<simplifyTopShopIconUrlDarkmode><![CDATA[]]></simplifyTopShopIconUrlDarkmode>\n\t\t\t<topShopIconWidth><![CDATA[0]]></topShopIconWidth>\n\t\t\t<topShopIconHeight><![CDATA[0]]></topShopIconHeight>\n\t\t\t<simplifyTopShopIconWidth><![CDATA[0]]></simplifyTopShopIconWidth>\n\t\t\t<simplifyTopShopIconHeight><![CDATA[0]]></simplifyTopShopIconHeight>\n\t\t\t<showBoxItemStringList />\n\t\t</finderLiveProductShare>\n\t\t<finderOrder>\n\t\t\t<appID><![CDATA[]]></appID>\n\t\t\t<orderID><![CDATA[]]></orderID>\n\t\t\t<path><![CDATA[]]></path>\n\t\t\t<priceWording><![CDATA[]]></priceWording>\n\t\t\t<stateWording><![CDATA[]]></stateWording>\n\t\t\t<productImageURL><![CDATA[]]></productImageURL>\n\t\t\t<products><![CDATA[]]></products>\n\t\t\t<productsCount><![CDATA[0]]></productsCount>\n\t\t\t<orderType><![CDATA[0]]></orderType>\n\t\t\t<newPriceWording><![CDATA[]]></newPriceWording>\n\t\t\t<newStateWording><![CDATA[]]></newStateWording>\n\t\t\t<useNewWording><![CDATA[0]]></useNewWording>\n\t\t</finderOrder>\n\t\t<finderShopWindowShare>\n\t\t\t<finderUsername><![CDATA[]]></finderUsername>\n\t\t\t<avatar><![CDATA[]]></avatar>\n\t\t\t<nickname><![CDATA[]]></nickname>\n\t\t\t<commodityInStockCount><![CDATA[]]></commodityInStockCount>\n\t\t\t<appId><![CDATA[]]></appId>\n\t\t\t<path><![CDATA[]]></path>\n\t\t\t<appUsername><![CDATA[]]></appUsername>\n\t\t\t<query><![CDATA[]]></query>\n\t\t\t<liteAppId><![CDATA[]]></liteAppId>\n\t\t\t<liteAppPath><![CDATA[]]></liteAppPath>\n\t\t\t<liteAppQuery><![CDATA[]]></liteAppQuery>\n\t\t\t<platformTagURL><![CDATA[]]></platformTagURL>\n\t\t\t<saleWording><![CDATA[]]></saleWording>\n\t\t\t<lastGMsgID><![CDATA[]]></lastGMsgID>\n\t\t\t<profileTypeWording><![CDATA[]]></profileTypeWording>\n\t\t\t<saleWordingExtra><![CDATA[]]></saleWordingExtra>\n\t\t\t<isWxShop><![CDATA[]]></isWxShop>\n\t\t\t<platformIconUrl><![CDATA[]]></platformIconUrl>\n\t\t\t<brandIconUrl><![CDATA[]]></brandIconUrl>\n\t\t\t<description><![CDATA[]]></description>\n\t\t\t<backgroundUrl><![CDATA[]]></backgroundUrl>\n\t\t\t<darkModePlatformIconUrl><![CDATA[]]></darkModePlatformIconUrl>\n\t\t\t<rIconUrl><![CDATA[]]></rIconUrl>\n\t\t\t<rIconUrlDarkMode><![CDATA[]]></rIconUrlDarkMode>\n\t\t\t<rWords><![CDATA[]]></rWords>\n\t\t\t<topShopIconUrl><![CDATA[]]></topShopIconUrl>\n\t\t\t<topShopIconUrlDarkMode><![CDATA[]]></topShopIconUrlDarkMode>\n\t\t\t<simplifyTopShopIconUrl><![CDATA[]]></simplifyTopShopIconUrl>\n\t\t\t<simplifyTopShopIconUrlDarkmode><![CDATA[]]></simplifyTopShopIconUrlDarkmode>\n\t\t\t<topShopIconWidth><![CDATA[0]]></topShopIconWidth>\n\t\t\t<topShopIconHeight><![CDATA[0]]></topShopIconHeight>\n\t\t\t<simplifyTopShopIconWidth><![CDATA[0]]></simplifyTopShopIconWidth>\n\t\t\t<simplifyTopShopIconHeight><![CDATA[0]]></simplifyTopShopIconHeight>\n\t\t\t<reputationInfo>\n\t\t\t\t<hasReputationInfo>0</hasReputationInfo>\n\t\t\t\t<reputationScore>0</reputationScore>\n\t\t\t\t<reputationWording />\n\t\t\t\t<reputationTextColor />\n\t\t\t\t<reputationLevelWording />\n\t\t\t\t<reputationBackgroundColor />\n\t\t\t</reputationInfo>\n\t\t\t<productImageURLList />\n\t\t</finderShopWindowShare>\n\t\t<findernamecard>\n\t\t\t<username />\n\t\t\t<avatar><![CDATA[]]></avatar>\n\t\t\t<nickname />\n\t\t\t<auth_job />\n\t\t\t<auth_icon>0</auth_icon>\n\t\t\t<auth_icon_url />\n\t\t\t<ecSource><![CDATA[]]></ecSource>\n\t\t\t<lastGMsgID><![CDATA[]]></lastGMsgID>\n\t\t</findernamecard>\n\t\t<finderGuarantee>\n\t\t\t<scene><![CDATA[0]]></scene>\n\t\t</finderGuarantee>\n\t\t<directshare>0</directshare>\n\t\t<gamecenter>\n\t\t\t<namecard>\n\t\t\t\t<iconUrl />\n\t\t\t\t<name />\n\t\t\t\t<desc />\n\t\t\t\t<tail />\n\t\t\t\t<jumpUrl />\n\t\t\t\t<liteappId />\n\t\t\t\t<liteappPath />\n\t\t\t\t<liteappQuery />\n\t\t\t\t<liteappMinVersion />\n\t\t\t</namecard>\n\t\t</gamecenter>\n\t\t<patMsg>\n\t\t\t<chatUser />\n\t\t\t<records>\n\t\t\t\t<recordNum>0</recordNum>\n\t\t\t</records>\n\t\t</patMsg>\n\t\t<secretmsg>\n\t\t\t<issecretmsg>0</issecretmsg>\n\t\t</secretmsg>\n\t\t<referfromscene>0</referfromscene>\n\t\t<gameshare>\n\t\t\t<liteappext>\n\t\t\t\t<liteappbizdata />\n\t\t\t\t<priority>0</priority>\n\t\t\t</liteappext>\n\t\t\t<appbrandext>\n\t\t\t\t<litegameinfo />\n\t\t\t\t<priority>-1</priority>\n\t\t\t</appbrandext>\n\t\t\t<gameshareid />\n\t\t\t<sharedata />\n\t\t\t<isvideo>0</isvideo>\n\t\t\t<duration>-1</duration>\n\t\t\t<isexposed>0</isexposed>\n\t\t\t<readtext />\n\t\t</gameshare>\n\t\t<tingChatRoomItem>\n\t\t\t<type>0</type>\n\t\t\t<categoryItem>null</categoryItem>\n\t\t\t<categoryId />\n\t\t</tingChatRoomItem>\n\t\t<mpsharetrace>\n\t\t\t<hasfinderelement>0</hasfinderelement>\n\t\t\t<lastgmsgid />\n\t\t</mpsharetrace>\n\t\t<wxgamecard>\n\t\t\t<framesetname />\n\t\t\t<mbcarddata />\n\t\t\t<minpkgversion />\n\t\t\t<clientextinfo />\n\t\t\t<mbcardheight>0</mbcardheight>\n\t\t\t<isoldversion>0</isoldversion>\n\t\t</wxgamecard>\n\t\t<ecskfcard>\n\t\t\t<framesetname />\n\t\t\t<mbcarddata />\n\t\t\t<minupdateunixtimestamp>0</minupdateunixtimestamp>\n\t\t\t<needheader>false</needheader>\n\t\t\t<summary />\n\t\t</ecskfcard>\n\t\t<liteapp>\n\t\t\t<id>null</id>\n\t\t\t<path />\n\t\t\t<query />\n\t\t\t<istransparent>0</istransparent>\n\t\t\t<hideicon>0</hideicon>\n\t\t\t<forbidforward>0</forbidforward>\n\t\t</liteapp>\n\t\t<opensdk_share_is_modified>0</opensdk_share_is_modified>\n\t</appmsg>\n\t<fromusername>wxid_wlnzvr8ivgd422</fromusername>\n\t<scene>0</scene>\n\t<appinfo>\n\t\t<version>1</version>\n\t\t<appname></appname>\n\t</appinfo>\n\t<commenturl></commenturl>\n</msg>\n'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1753180849, 'MsgSource': '<msgsource>\n\t<alnode>\n\t\t<fr>4</fr>\n\t</alnode>\n\t<eggIncluded>1</eggIncluded>\n\t<sec_msg_node>\n\t\t<uuid>575f8943f0356befc7aa76da4d844fdd_</uuid>\n\t\t<risk-file-flag />\n\t\t<risk-file-md5-list />\n\t</sec_msg_node>\n\t<silence>0</silence>\n\t<membercount>62</membercount>\n\t<signature>N0_V1_RFGVuiFQ|v1_BcDgEjkG</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'PushContent': '锦岚（开玩笑就退群版） : 看看同款', 'NewMsgId': 7940574495556795, 'MsgSeq': 871392679}
2025-07-22 18:40:36 | DEBUG | 从群聊消息中提取发送者: wxid_wlnzvr8ivgd422
2025-07-22 18:40:36 | DEBUG | 使用已解析的XML处理引用消息
2025-07-22 18:40:36 | INFO | 收到引用消息: 消息ID:1474179639 来自:48097389945@chatroom 发送人:wxid_wlnzvr8ivgd422 内容:看看同款 引用类型:49
2025-07-22 18:40:36 | INFO | [DouBaoImageToImage] 收到引用消息: 看看同款
2025-07-22 18:40:36 | INFO | [TimerTask] 收到引用消息调试信息:
2025-07-22 18:40:36 | INFO |   - 消息内容: 看看同款
2025-07-22 18:40:36 | INFO |   - 群组ID: 48097389945@chatroom
2025-07-22 18:40:36 | INFO |   - 发送人: wxid_wlnzvr8ivgd422
2025-07-22 18:40:36 | INFO |   - 引用信息: {'MsgType': 49, 'Content': '<?xml version="1.0"?>\n<msg>\n\t<appmsg appid="" sdkver="0">\n\t\t<title>同款自行车[Doge]</title>\n\t\t<type>57</type>\n\t\t<appattach>\n\t\t\t<cdnthumbaeskey />\n\t\t\t<aeskey />\n\t\t</appattach>\n\t\t<refermsg></refermsg>\n\t</appmsg>\n\t<fromusername>wxid_hqdtktnqvw8e21</fromusername>\n\t<scene>0</scene>\n\t<appinfo>\n\t\t<version>1</version>\n\t\t<appname></appname>\n\t</appinfo>\n\t<commenturl></commenturl>\n</msg>\n', 'Msgid': '4178126636718273630', 'NewMsgId': '4178126636718273630', 'ToWxid': 'wxid_4usgcju5ey9q29', 'FromWxid': '48097389945@chatroom', 'Nickname': 'Ritz', 'MsgSource': '<msgsource>\n\t<bizflag>0</bizflag>\n\t<pua>1</pua>\n\t<sec_msg_node>\n\t\t<uuid>575f8943f0356befc7aa76da4d844fdd_</uuid>\n\t\t<risk-file-flag />\n\t\t<risk-file-md5-list />\n\t</sec_msg_node>\n\t<silence>1</silence>\n\t<membercount>62</membercount>\n\t<signature>N0_V1_U3QSTzmV|v1_NFYZ/3RY</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'Createtime': '1753180824', 'SenderWxid': 'wxid_wlnzvr8ivgd422'}
2025-07-22 18:40:36 | INFO |   - 引用消息ID: 
2025-07-22 18:40:36 | INFO |   - 引用消息类型: 
2025-07-22 18:40:36 | INFO |   - 引用消息内容: <?xml version="1.0"?>
<msg>
	<appmsg appid="" sdkver="0">
		<title>同款自行车[Doge]</title>
		<type>57</type>
		<appattach>
			<cdnthumbaeskey />
			<aeskey />
		</appattach>
		<refermsg></refermsg>
	</appmsg>
	<fromusername>wxid_hqdtktnqvw8e21</fromusername>
	<scene>0</scene>
	<appinfo>
		<version>1</version>
		<appname></appname>
	</appinfo>
	<commenturl></commenturl>
</msg>

2025-07-22 18:40:36 | INFO |   - 引用消息发送人: wxid_wlnzvr8ivgd422
2025-07-22 18:41:01 | DEBUG | 收到消息: {'MsgId': 156979019, 'FromUserName': {'string': '48097389945@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 49, 'Content': {'string': 'wxid_wlnzvr8ivgd422:\n<?xml version="1.0"?>\n<msg>\n\t<appmsg appid="" sdkver="0">\n\t\t<title>助理，哈哈哈哈哈</title>\n\t\t<des />\n\t\t<username />\n\t\t<action>view</action>\n\t\t<type>57</type>\n\t\t<showtype>0</showtype>\n\t\t<content />\n\t\t<url />\n\t\t<lowurl />\n\t\t<forwardflag>0</forwardflag>\n\t\t<dataurl />\n\t\t<lowdataurl />\n\t\t<contentattr>0</contentattr>\n\t\t<streamvideo>\n\t\t\t<streamvideourl />\n\t\t\t<streamvideototaltime>0</streamvideototaltime>\n\t\t\t<streamvideotitle />\n\t\t\t<streamvideowording />\n\t\t\t<streamvideoweburl />\n\t\t\t<streamvideothumburl />\n\t\t\t<streamvideoaduxinfo />\n\t\t\t<streamvideopublishid />\n\t\t</streamvideo>\n\t\t<canvasPageItem>\n\t\t\t<canvasPageXml><![CDATA[]]></canvasPageXml>\n\t\t</canvasPageItem>\n\t\t<refermsg>\n\t\t\t<type>1</type>\n\t\t\t<svrid>2250640367744083669</svrid>\n\t\t\t<fromusr>48097389945@chatroom</fromusr>\n\t\t\t<chatusr>wxid_hqdtktnqvw8e21</chatusr>\n\t\t\t<displayname>Ritz</displayname>\n\t\t\t<msgsource>&lt;msgsource&gt;\n\t&lt;bizflag&gt;0&lt;/bizflag&gt;\n\t&lt;pua&gt;1&lt;/pua&gt;\n\t&lt;silence&gt;1&lt;/silence&gt;\n\t&lt;membercount&gt;62&lt;/membercount&gt;\n\t&lt;signature&gt;N0_V1_4YhbvBGS|v1_rbsO9FUU&lt;/signature&gt;\n\t&lt;tmp_node&gt;\n\t\t&lt;publisher-id&gt;&lt;/publisher-id&gt;\n\t&lt;/tmp_node&gt;\n&lt;/msgsource&gt;\n</msgsource>\n\t\t\t<content>\n啥岗位</content>\n\t\t\t<strid />\n\t\t\t<createtime>1753180830</createtime>\n\t\t</refermsg>\n\t\t<appattach>\n\t\t\t<totallen>0</totallen>\n\t\t\t<attachid />\n\t\t\t<cdnattachurl />\n\t\t\t<emoticonmd5 />\n\t\t\t<aeskey />\n\t\t\t<fileext />\n\t\t\t<islargefilemsg>0</islargefilemsg>\n\t\t</appattach>\n\t\t<extinfo />\n\t\t<androidsource>0</androidsource>\n\t\t<thumburl />\n\t\t<mediatagname />\n\t\t<messageaction><![CDATA[]]></messageaction>\n\t\t<messageext><![CDATA[]]></messageext>\n\t\t<emoticongift>\n\t\t\t<packageflag>0</packageflag>\n\t\t\t<packageid />\n\t\t</emoticongift>\n\t\t<emoticonshared>\n\t\t\t<packageflag>0</packageflag>\n\t\t\t<packageid />\n\t\t</emoticonshared>\n\t\t<designershared>\n\t\t\t<designeruin>0</designeruin>\n\t\t\t<designername>null</designername>\n\t\t\t<designerrediretcturl><![CDATA[null]]></designerrediretcturl>\n\t\t</designershared>\n\t\t<emotionpageshared>\n\t\t\t<tid>0</tid>\n\t\t\t<title>null</title>\n\t\t\t<desc>null</desc>\n\t\t\t<iconUrl><![CDATA[null]]></iconUrl>\n\t\t\t<secondUrl>null</secondUrl>\n\t\t\t<pageType>0</pageType>\n\t\t\t<setKey>null</setKey>\n\t\t</emotionpageshared>\n\t\t<webviewshared>\n\t\t\t<shareUrlOriginal />\n\t\t\t<shareUrlOpen />\n\t\t\t<jsAppId />\n\t\t\t<publisherId />\n\t\t\t<publisherReqId />\n\t\t</webviewshared>\n\t\t<template_id />\n\t\t<md5 />\n\t\t<websearch>\n\t\t\t<rec_category>0</rec_category>\n\t\t\t<channelId>0</channelId>\n\t\t</websearch>\n\t\t<weappinfo>\n\t\t\t<username />\n\t\t\t<appid />\n\t\t\t<appservicetype>0</appservicetype>\n\t\t\t<secflagforsinglepagemode>0</secflagforsinglepagemode>\n\t\t\t<videopageinfo>\n\t\t\t\t<thumbwidth>0</thumbwidth>\n\t\t\t\t<thumbheight>0</thumbheight>\n\t\t\t\t<fromopensdk>0</fromopensdk>\n\t\t\t</videopageinfo>\n\t\t</weappinfo>\n\t\t<statextstr />\n\t\t<musicShareItem>\n\t\t\t<musicDuration>0</musicDuration>\n\t\t</musicShareItem>\n\t\t<finderLiveProductShare>\n\t\t\t<finderLiveID><![CDATA[]]></finderLiveID>\n\t\t\t<finderUsername><![CDATA[]]></finderUsername>\n\t\t\t<finderObjectID><![CDATA[]]></finderObjectID>\n\t\t\t<finderNonceID><![CDATA[]]></finderNonceID>\n\t\t\t<liveStatus><![CDATA[]]></liveStatus>\n\t\t\t<appId><![CDATA[]]></appId>\n\t\t\t<pagePath><![CDATA[]]></pagePath>\n\t\t\t<productId><![CDATA[]]></productId>\n\t\t\t<coverUrl><![CDATA[]]></coverUrl>\n\t\t\t<productTitle><![CDATA[]]></productTitle>\n\t\t\t<marketPrice><![CDATA[0]]></marketPrice>\n\t\t\t<sellingPrice><![CDATA[0]]></sellingPrice>\n\t\t\t<platformHeadImg><![CDATA[]]></platformHeadImg>\n\t\t\t<platformName><![CDATA[]]></platformName>\n\t\t\t<shopWindowId><![CDATA[]]></shopWindowId>\n\t\t\t<flashSalePrice><![CDATA[0]]></flashSalePrice>\n\t\t\t<flashSaleEndTime><![CDATA[0]]></flashSaleEndTime>\n\t\t\t<ecSource><![CDATA[]]></ecSource>\n\t\t\t<sellingPriceWording><![CDATA[]]></sellingPriceWording>\n\t\t\t<platformIconURL><![CDATA[]]></platformIconURL>\n\t\t\t<firstProductTagURL><![CDATA[]]></firstProductTagURL>\n\t\t\t<firstProductTagAspectRatioString><![CDATA[0.0]]></firstProductTagAspectRatioString>\n\t\t\t<secondProductTagURL><![CDATA[]]></secondProductTagURL>\n\t\t\t<secondProductTagAspectRatioString><![CDATA[0.0]]></secondProductTagAspectRatioString>\n\t\t\t<firstGuaranteeWording><![CDATA[]]></firstGuaranteeWording>\n\t\t\t<secondGuaranteeWording><![CDATA[]]></secondGuaranteeWording>\n\t\t\t<thirdGuaranteeWording><![CDATA[]]></thirdGuaranteeWording>\n\t\t\t<isPriceBeginShow>false</isPriceBeginShow>\n\t\t\t<lastGMsgID><![CDATA[]]></lastGMsgID>\n\t\t\t<promoterKey><![CDATA[]]></promoterKey>\n\t\t\t<discountWording><![CDATA[]]></discountWording>\n\t\t\t<priceSuffixDescription><![CDATA[]]></priceSuffixDescription>\n\t\t\t<productCardKey><![CDATA[]]></productCardKey>\n\t\t\t<isWxShop><![CDATA[]]></isWxShop>\n\t\t\t<brandIconUrl><![CDATA[]]></brandIconUrl>\n\t\t\t<rIconUrl><![CDATA[]]></rIconUrl>\n\t\t\t<rIconUrlDarkMode><![CDATA[]]></rIconUrlDarkMode>\n\t\t\t<topShopIconUrl><![CDATA[]]></topShopIconUrl>\n\t\t\t<topShopIconUrlDarkMode><![CDATA[]]></topShopIconUrlDarkMode>\n\t\t\t<simplifyTopShopIconUrl><![CDATA[]]></simplifyTopShopIconUrl>\n\t\t\t<simplifyTopShopIconUrlDarkmode><![CDATA[]]></simplifyTopShopIconUrlDarkmode>\n\t\t\t<topShopIconWidth><![CDATA[0]]></topShopIconWidth>\n\t\t\t<topShopIconHeight><![CDATA[0]]></topShopIconHeight>\n\t\t\t<simplifyTopShopIconWidth><![CDATA[0]]></simplifyTopShopIconWidth>\n\t\t\t<simplifyTopShopIconHeight><![CDATA[0]]></simplifyTopShopIconHeight>\n\t\t\t<showBoxItemStringList />\n\t\t</finderLiveProductShare>\n\t\t<finderOrder>\n\t\t\t<appID><![CDATA[]]></appID>\n\t\t\t<orderID><![CDATA[]]></orderID>\n\t\t\t<path><![CDATA[]]></path>\n\t\t\t<priceWording><![CDATA[]]></priceWording>\n\t\t\t<stateWording><![CDATA[]]></stateWording>\n\t\t\t<productImageURL><![CDATA[]]></productImageURL>\n\t\t\t<products><![CDATA[]]></products>\n\t\t\t<productsCount><![CDATA[0]]></productsCount>\n\t\t\t<orderType><![CDATA[0]]></orderType>\n\t\t\t<newPriceWording><![CDATA[]]></newPriceWording>\n\t\t\t<newStateWording><![CDATA[]]></newStateWording>\n\t\t\t<useNewWording><![CDATA[0]]></useNewWording>\n\t\t</finderOrder>\n\t\t<finderShopWindowShare>\n\t\t\t<finderUsername><![CDATA[]]></finderUsername>\n\t\t\t<avatar><![CDATA[]]></avatar>\n\t\t\t<nickname><![CDATA[]]></nickname>\n\t\t\t<commodityInStockCount><![CDATA[]]></commodityInStockCount>\n\t\t\t<appId><![CDATA[]]></appId>\n\t\t\t<path><![CDATA[]]></path>\n\t\t\t<appUsername><![CDATA[]]></appUsername>\n\t\t\t<query><![CDATA[]]></query>\n\t\t\t<liteAppId><![CDATA[]]></liteAppId>\n\t\t\t<liteAppPath><![CDATA[]]></liteAppPath>\n\t\t\t<liteAppQuery><![CDATA[]]></liteAppQuery>\n\t\t\t<platformTagURL><![CDATA[]]></platformTagURL>\n\t\t\t<saleWording><![CDATA[]]></saleWording>\n\t\t\t<lastGMsgID><![CDATA[]]></lastGMsgID>\n\t\t\t<profileTypeWording><![CDATA[]]></profileTypeWording>\n\t\t\t<saleWordingExtra><![CDATA[]]></saleWordingExtra>\n\t\t\t<isWxShop><![CDATA[]]></isWxShop>\n\t\t\t<platformIconUrl><![CDATA[]]></platformIconUrl>\n\t\t\t<brandIconUrl><![CDATA[]]></brandIconUrl>\n\t\t\t<description><![CDATA[]]></description>\n\t\t\t<backgroundUrl><![CDATA[]]></backgroundUrl>\n\t\t\t<darkModePlatformIconUrl><![CDATA[]]></darkModePlatformIconUrl>\n\t\t\t<rIconUrl><![CDATA[]]></rIconUrl>\n\t\t\t<rIconUrlDarkMode><![CDATA[]]></rIconUrlDarkMode>\n\t\t\t<rWords><![CDATA[]]></rWords>\n\t\t\t<topShopIconUrl><![CDATA[]]></topShopIconUrl>\n\t\t\t<topShopIconUrlDarkMode><![CDATA[]]></topShopIconUrlDarkMode>\n\t\t\t<simplifyTopShopIconUrl><![CDATA[]]></simplifyTopShopIconUrl>\n\t\t\t<simplifyTopShopIconUrlDarkmode><![CDATA[]]></simplifyTopShopIconUrlDarkmode>\n\t\t\t<topShopIconWidth><![CDATA[0]]></topShopIconWidth>\n\t\t\t<topShopIconHeight><![CDATA[0]]></topShopIconHeight>\n\t\t\t<simplifyTopShopIconWidth><![CDATA[0]]></simplifyTopShopIconWidth>\n\t\t\t<simplifyTopShopIconHeight><![CDATA[0]]></simplifyTopShopIconHeight>\n\t\t\t<reputationInfo>\n\t\t\t\t<hasReputationInfo>0</hasReputationInfo>\n\t\t\t\t<reputationScore>0</reputationScore>\n\t\t\t\t<reputationWording />\n\t\t\t\t<reputationTextColor />\n\t\t\t\t<reputationLevelWording />\n\t\t\t\t<reputationBackgroundColor />\n\t\t\t</reputationInfo>\n\t\t\t<productImageURLList />\n\t\t</finderShopWindowShare>\n\t\t<findernamecard>\n\t\t\t<username />\n\t\t\t<avatar><![CDATA[]]></avatar>\n\t\t\t<nickname />\n\t\t\t<auth_job />\n\t\t\t<auth_icon>0</auth_icon>\n\t\t\t<auth_icon_url />\n\t\t\t<ecSource><![CDATA[]]></ecSource>\n\t\t\t<lastGMsgID><![CDATA[]]></lastGMsgID>\n\t\t</findernamecard>\n\t\t<finderGuarantee>\n\t\t\t<scene><![CDATA[0]]></scene>\n\t\t</finderGuarantee>\n\t\t<directshare>0</directshare>\n\t\t<gamecenter>\n\t\t\t<namecard>\n\t\t\t\t<iconUrl />\n\t\t\t\t<name />\n\t\t\t\t<desc />\n\t\t\t\t<tail />\n\t\t\t\t<jumpUrl />\n\t\t\t\t<liteappId />\n\t\t\t\t<liteappPath />\n\t\t\t\t<liteappQuery />\n\t\t\t\t<liteappMinVersion />\n\t\t\t</namecard>\n\t\t</gamecenter>\n\t\t<patMsg>\n\t\t\t<chatUser />\n\t\t\t<records>\n\t\t\t\t<recordNum>0</recordNum>\n\t\t\t</records>\n\t\t</patMsg>\n\t\t<secretmsg>\n\t\t\t<issecretmsg>0</issecretmsg>\n\t\t</secretmsg>\n\t\t<referfromscene>0</referfromscene>\n\t\t<gameshare>\n\t\t\t<liteappext>\n\t\t\t\t<liteappbizdata />\n\t\t\t\t<priority>0</priority>\n\t\t\t</liteappext>\n\t\t\t<appbrandext>\n\t\t\t\t<litegameinfo />\n\t\t\t\t<priority>-1</priority>\n\t\t\t</appbrandext>\n\t\t\t<gameshareid />\n\t\t\t<sharedata />\n\t\t\t<isvideo>0</isvideo>\n\t\t\t<duration>-1</duration>\n\t\t\t<isexposed>0</isexposed>\n\t\t\t<readtext />\n\t\t</gameshare>\n\t\t<tingChatRoomItem>\n\t\t\t<type>0</type>\n\t\t\t<categoryItem>null</categoryItem>\n\t\t\t<categoryId />\n\t\t</tingChatRoomItem>\n\t\t<mpsharetrace>\n\t\t\t<hasfinderelement>0</hasfinderelement>\n\t\t\t<lastgmsgid />\n\t\t</mpsharetrace>\n\t\t<wxgamecard>\n\t\t\t<framesetname />\n\t\t\t<mbcarddata />\n\t\t\t<minpkgversion />\n\t\t\t<clientextinfo />\n\t\t\t<mbcardheight>0</mbcardheight>\n\t\t\t<isoldversion>0</isoldversion>\n\t\t</wxgamecard>\n\t\t<ecskfcard>\n\t\t\t<framesetname />\n\t\t\t<mbcarddata />\n\t\t\t<minupdateunixtimestamp>0</minupdateunixtimestamp>\n\t\t\t<needheader>false</needheader>\n\t\t\t<summary />\n\t\t</ecskfcard>\n\t\t<liteapp>\n\t\t\t<id>null</id>\n\t\t\t<path />\n\t\t\t<query />\n\t\t\t<istransparent>0</istransparent>\n\t\t\t<hideicon>0</hideicon>\n\t\t\t<forbidforward>0</forbidforward>\n\t\t</liteapp>\n\t\t<opensdk_share_is_modified>0</opensdk_share_is_modified>\n\t</appmsg>\n\t<fromusername>wxid_wlnzvr8ivgd422</fromusername>\n\t<scene>0</scene>\n\t<appinfo>\n\t\t<version>1</version>\n\t\t<appname></appname>\n\t</appinfo>\n\t<commenturl></commenturl>\n</msg>\n'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1753180874, 'MsgSource': '<msgsource>\n\t<alnode>\n\t\t<fr>4</fr>\n\t</alnode>\n\t<eggIncluded>1</eggIncluded>\n\t<sec_msg_node>\n\t\t<uuid>9d6d4ec748c82f76d9a650d0ae357da5_</uuid>\n\t\t<risk-file-flag />\n\t\t<risk-file-md5-list />\n\t</sec_msg_node>\n\t<silence>0</silence>\n\t<membercount>62</membercount>\n\t<signature>N0_V1_P4Cpicmh|v1_b0MdAuh1</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'PushContent': '锦岚（开玩笑就退群版） : 助理，哈哈哈哈哈', 'NewMsgId': 620514828144952729, 'MsgSeq': 871392680}
2025-07-22 18:41:01 | DEBUG | 从群聊消息中提取发送者: wxid_wlnzvr8ivgd422
2025-07-22 18:41:01 | DEBUG | 使用已解析的XML处理引用消息
2025-07-22 18:41:01 | INFO | 收到引用消息: 消息ID:156979019 来自:48097389945@chatroom 发送人:wxid_wlnzvr8ivgd422 内容:助理，哈哈哈哈哈 引用类型:1
2025-07-22 18:41:02 | INFO | [DouBaoImageToImage] 收到引用消息: 助理，哈哈哈哈哈
2025-07-22 18:41:02 | INFO | [TimerTask] 收到引用消息调试信息:
2025-07-22 18:41:02 | INFO |   - 消息内容: 助理，哈哈哈哈哈
2025-07-22 18:41:02 | INFO |   - 群组ID: 48097389945@chatroom
2025-07-22 18:41:02 | INFO |   - 发送人: wxid_wlnzvr8ivgd422
2025-07-22 18:41:02 | INFO |   - 引用信息: {'MsgType': 1, 'Content': '\n啥岗位', 'Msgid': '2250640367744083669', 'NewMsgId': '2250640367744083669', 'ToWxid': 'wxid_4usgcju5ey9q29', 'FromWxid': '48097389945@chatroom', 'Nickname': 'Ritz', 'MsgSource': '<msgsource>\n\t<bizflag>0</bizflag>\n\t<pua>1</pua>\n\t<silence>1</silence>\n\t<membercount>62</membercount>\n\t<signature>N0_V1_4YhbvBGS|v1_rbsO9FUU</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'Createtime': '1753180830', 'SenderWxid': 'wxid_wlnzvr8ivgd422'}
2025-07-22 18:41:02 | INFO |   - 引用消息ID: 
2025-07-22 18:41:02 | INFO |   - 引用消息类型: 
2025-07-22 18:41:02 | INFO |   - 引用消息内容: 
啥岗位
2025-07-22 18:41:02 | INFO |   - 引用消息发送人: wxid_wlnzvr8ivgd422
2025-07-22 18:41:06 | DEBUG | 收到消息: {'MsgId': 864392775, 'FromUserName': {'string': '47442567074@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 1, 'Content': {'string': 'wxid_i73nrnun919k12:\n搜歌 小郭'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1753180879, 'MsgSource': '<msgsource>\n\t<bizflag>0</bizflag>\n\t<pua>1</pua>\n\t<eggIncluded>1</eggIncluded>\n\t<silence>0</silence>\n\t<membercount>494</membercount>\n\t<signature>N0_V1_urowbPnS|v1_BgiTW2YN</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'PushContent': '   : 搜歌 小郭', 'NewMsgId': 4801133075939879406, 'MsgSeq': 871392681}
2025-07-22 18:41:06 | INFO | 收到文本消息: 消息ID:864392775 来自:47442567074@chatroom 发送人:wxid_i73nrnun919k12 @:[] 内容:搜歌 小郭
2025-07-22 18:41:07 | DEBUG | [Music] 成功获取发送者 wxid_i73nrnun919k12 的头像: https://wx.qlogo.cn/mmhead/ver_1/scnn8NbIhXDGiaibiagdVVV4JYiaeLg7eibibM9oUO5boxKKo7Uericotuib6ppaDuWT62Xj2O52g0dHond73tjQ1HO0mUcVneRoccOPeMoZUnxsHyuY1cXeniaUicDEticebDpXMQF/132
2025-07-22 18:41:08 | INFO | 发送app消息: 对方wxid:47442567074@chatroom 类型:19 xml:<appmsg appid="" sdkver="0"><title>🎵 小郭 - 搜索结果</title><des>🎵 音乐助手: 为您找到 "小郭" 的相关歌曲：🎶 搜索结果: 1、矛盾综合体 -- 花朝2、你突然 (Demo) -- 郭彦彤、小李同学。3、三年之约 -- 郭富祥4、小丑的真爱舞台 -- 郭富祥5、裁剪WeChat2 (Remix) -- 郭亚文6、一程山路 -- 小郭同学7、心酸 -- 尚雨晨8、号我 -- 郭彦彤、小李同学。💡 使用提示: 发送 "听1" 到 "听8" 选择对应歌曲</des><action>view</action><type>19</type><showtype>0</showtype><content/><url>https://support.weixin.qq.com/cgi-bin/mmsupport-bin/readtemplate?t=page/favorite_record__w_unsupport&amp;from=singlemessage&amp;isappinstalled=0</url><lowurl/><dataurl/><lowdataurl/><recorditem><![CDATA[<recordinfo><title>🎵 音乐搜索结果</title><desc>🎵 音乐助手:&#x20;为您找到 "小郭" 的相关歌曲：&#x0A;🎶 搜索结果:&#x20;1、矛盾综合体 -- 花朝2、你突然 (Demo) -- 郭彦彤、小李同学。3、三年之约 -- 郭富祥4、小丑的真爱舞台 -- 郭富祥5、裁剪WeChat2 (Remix) -- 郭亚文6、一程山路 -- 小郭同学7、心酸 -- 尚雨晨8、号我 -- 郭彦彤、小李同学。&#x0A;💡 使用提示:&#x20;发送 "听1" 到 "听8" 选择对应歌曲</desc><datalist count="3"><dataitem datatype="1" dataid="43f5983d2618302a03a06f2e622dcc62" datasourceid="3766002209877783445"><datadesc>为您找到 "小郭" 的相关歌曲：</datadesc><sourcename>🎵 音乐助手</sourcename><sourceheadurl>https://wx.qlogo.cn/mmhead/ver_1/scnn8NbIhXDGiaibiagdVVV4JYiaeLg7eibibM9oUO5boxKKo7Uericotuib6ppaDuWT62Xj2O52g0dHond73tjQ1HO0mUcVneRoccOPeMoZUnxsHyuY1cXeniaUicDEticebDpXMQF/132</sourceheadurl><sourcetime>2025-07-21&#x20;12:00:00</sourcetime><srcMsgCreateTime>1753180867</srcMsgCreateTime><fromnewmsgid>3766002209877783445</fromnewmsgid><dataitemsource><hashusername>47117a242116be9ab5e02f7331cd04041140f0caf32a9bedff8b44eb7e0c1cb7</hashusername></dataitemsource></dataitem><dataitem datatype="1" dataid="0cc8fbbe6ec1081bdfb4ab6093295f20" datasourceid="7524758389558689367"><datadesc>1、矛盾综合体 -- 花朝2、你突然 (Demo) -- 郭彦彤、小李同学。3、三年之约 -- 郭富祥4、小丑的真爱舞台 -- 郭富祥5、裁剪WeChat2 (Remix) -- 郭亚文6、一程山路 -- 小郭同学7、心酸 -- 尚雨晨8、号我 -- 郭彦彤、小李同学。</datadesc><sourcename>🎶 搜索结果</sourcename><sourceheadurl>https://wx.qlogo.cn/mmhead/ver_1/PCUwVKYvh0icQrEmB5CiaoyoPaAY9X93CtB0OeA6QmOUml3ljscUgvL3rVRwD2iacibn6oDaaA7xzmibrmt9ibcaNR27Sl8sawD9ZUDgAenibnicpoecdGEaRubkMkImzibwjoddx/96</sourceheadurl><sourcetime>2025-07-21&#x20;12:01:00</sourcetime><srcMsgCreateTime>1753180897</srcMsgCreateTime><fromnewmsgid>7524758389558689367</fromnewmsgid><dataitemsource><hashusername>e6b73ff27565cff370109f8fcda0e65fdab070d2463b159472ee7e7f54f7d313</hashusername></dataitemsource></dataitem><dataitem datatype="1" dataid="60a9d586e3f24b8c1418bbb8db6db29b" datasourceid="8709781235937329553"><datadesc>发送 "听1" 到 "听8" 选择对应歌曲</datadesc><sourcename>💡 使用提示</sourcename><sourceheadurl>https://wx.qlogo.cn/mmhead/ver_1/rTPg4GeXEic6oQSZ2jcAI6ic6JEhFtYT40Eu3rUIjZpTKTATSgCEiaMJlUuFibkfxN4IpDCv0qxCA5WgowqT0BGRTQAVeDTn1PTGj1LUYbl2bRuJiaCb1ukH4e1TPIibyUiaX6hFKgria9aX7NM5SJFHDq3pBA/132</sourceheadurl><sourcetime>2025-07-21&#x20;12:02:00</sourcetime><srcMsgCreateTime>1753180927</srcMsgCreateTime><fromnewmsgid>8709781235937329553</fromnewmsgid><dataitemsource><hashusername>8f57acad5c854c4004fd00435c305ac427418e4ac2f7816798d3db2db6eff1eb</hashusername></dataitemsource></dataitem></datalist><favcreatetime>1753180867921</favcreatetime></recordinfo>]]></recorditem><appattach>    <totallen>0</totallen>    <attachid/>    <emoticonmd5/>    <fileext/>    <aeskey/></appattach><extinfo/><sourceusername/><sourcedisplayname/><thumburl/><md5/></appmsg><fromusername>wxid_4usgcju5ey9q29</fromusername><scene>0</scene><appinfo>    <version>1</version>    <appname></appname></appinfo><commenturl/>
2025-07-22 18:41:08 | DEBUG | 处理消息内容: '搜歌 小郭'
2025-07-22 18:41:08 | DEBUG | 消息内容 '搜歌 小郭' 不匹配任何命令，忽略
2025-07-22 18:41:30 | DEBUG | 收到消息: {'MsgId': **********, 'FromUserName': {'string': 'weixin'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 10002, 'Content': {'string': '<sysmsg type="BizRecommendCard">\n    <BizRecommendCard>\n        <CardName><![CDATA[Notify]]></CardName>\n        <CardBuffer><![CDATA[CAMaNHsiYml6dWluIjoyMjI0NDk3NzgxLCJtc2dpZCI6MjY1MjMyMzc4NCwiaXRlbWlkeCI6NX0iH25ld3NmbGFzaF8xMjY2MTAxMDA3XzE3NTMxODAzMDMo59X9wwY=]]></CardBuffer>\n    </BizRecommendCard>\n</sysmsg>'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': **********, 'MsgSource': '<msgsource>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'NewMsgId': 7156376729140205926, 'MsgSeq': *********}
2025-07-22 18:41:30 | DEBUG | 系统消息类型: BizRecommendCard
2025-07-22 18:41:30 | INFO | 未知的系统消息类型: {'MsgId': **********, 'ToWxid': 'wxid_4usgcju5ey9q29', 'MsgType': 10002, 'Content': '<sysmsg type="BizRecommendCard">\n    <BizRecommendCard>\n        <CardName><![CDATA[Notify]]></CardName>\n        <CardBuffer><![CDATA[CAMaNHsiYml6dWluIjoyMjI0NDk3NzgxLCJtc2dpZCI6MjY1MjMyMzc4NCwiaXRlbWlkeCI6NX0iH25ld3NmbGFzaF8xMjY2MTAxMDA3XzE3NTMxODAzMDMo59X9wwY=]]></CardBuffer>\n    </BizRecommendCard>\n</sysmsg>', 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': **********, 'MsgSource': '<msgsource>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'NewMsgId': 7156376729140205926, 'MsgSeq': *********, 'FromWxid': 'weixin', 'SenderWxid': 'weixin', 'IsGroup': False}
2025-07-22 18:42:17 | DEBUG | 收到消息: {'MsgId': **********, 'FromUserName': {'string': '48097389945@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 1, 'Content': {'string': 'wxid_wlnzvr8ivgd422:\n点歌有钱就花'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': **********, 'MsgSource': '<msgsource>\n\t<pua>1</pua>\n\t<eggIncluded>1</eggIncluded>\n\t<silence>0</silence>\n\t<membercount>62</membercount>\n\t<signature>N0_V1_yqV67oef|v1_FrVXeq9R</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'PushContent': '锦岚（开玩笑就退群版） : 点歌有钱就花', 'NewMsgId': 3723029544833775993, 'MsgSeq': 871392685}
2025-07-22 18:42:17 | INFO | 收到文本消息: 消息ID:********** 来自:48097389945@chatroom 发送人:wxid_wlnzvr8ivgd422 @:[] 内容:点歌有钱就花
2025-07-22 18:42:17 | DEBUG | 处理消息内容: '点歌有钱就花'
2025-07-22 18:42:17 | DEBUG | 消息内容 '点歌有钱就花' 不匹配任何命令，忽略
2025-07-22 18:42:19 | DEBUG | 收到消息: {'MsgId': 682513846, 'FromUserName': {'string': '48097389945@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 49, 'Content': {'string': 'xiaomaochong:\n<msg>\n\t<appmsg appid="wx0a022393a4dd43b2" sdkver="0">\n\t\t<title>有钱就花</title>\n\t\t<des>媛小小</des>\n\t\t<type>3</type>\n\t\t<dataurl>https://er-sycdn.kuwo.cn/0095b74e2e437187e903215fe89a5d78/687f6b17/resource/30106/trackmedia/F000000IOozq3dC6ar.flac</dataurl>\n\t\t<songalbumurl>http://img1.kwcdn.kuwo.cn/star/albumcover/320/s4s86/21/866658516.jpg</songalbumurl>\n\t\t<appattach>\n\t\t\t<cdnthumbaeskey />\n\t\t\t<aeskey />\n\t\t</appattach>\n\t</appmsg>\n\t<fromusername>xiaomaochong</fromusername>\n\t<scene>0</scene>\n\t<appinfo>\n\t\t<version>5</version>\n\t\t<appname>小爱同学</appname>\n\t</appinfo>\n\t<commenturl />\n</msg>\n'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1753180951, 'MsgSource': '<msgsource>\n\t<sec_msg_node>\n\t\t<uuid>e3990e466b33acb2f5949cba14dfb96f_</uuid>\n\t\t<risk-file-flag />\n\t\t<risk-file-md5-list />\n\t</sec_msg_node>\n\t<silence>0</silence>\n\t<membercount>62</membercount>\n\t<signature>N0_V1_Je/6er4K|v1_ebpDCbej</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'PushContent': '你收到了一条消息', 'NewMsgId': 4122033763086395805, 'MsgSeq': 871392686}
2025-07-22 18:42:19 | DEBUG | 从群聊消息中提取发送者: xiaomaochong
2025-07-22 18:42:19 | DEBUG | XML消息完整内容:
<msg>
	<appmsg appid="wx0a022393a4dd43b2" sdkver="0">
		<title>有钱就花</title>
		<des>媛小小</des>
		<type>3</type>
		<dataurl>https://er-sycdn.kuwo.cn/0095b74e2e437187e903215fe89a5d78/687f6b17/resource/30106/trackmedia/F000000IOozq3dC6ar.flac</dataurl>
		<songalbumurl>http://img1.kwcdn.kuwo.cn/star/albumcover/320/s4s86/21/866658516.jpg</songalbumurl>
		<appattach>
			<cdnthumbaeskey />
			<aeskey />
		</appattach>
	</appmsg>
	<fromusername>xiaomaochong</fromusername>
	<scene>0</scene>
	<appinfo>
		<version>5</version>
		<appname>小爱同学</appname>
	</appinfo>
	<commenturl />
</msg>

2025-07-22 18:42:19 | DEBUG | XML消息类型: 3
2025-07-22 18:42:19 | DEBUG | XML消息标题: 有钱就花
2025-07-22 18:42:19 | DEBUG | XML消息描述: 媛小小
2025-07-22 18:42:19 | INFO | 收到红包消息: 标题:有钱就花 描述:媛小小 来自:48097389945@chatroom 发送人:wxid_4usgcju5ey9q29
2025-07-22 18:42:20 | DEBUG | 收到消息: {'MsgId': 2092547709, 'FromUserName': {'string': '48097389945@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 49, 'Content': {'string': 'wxid_q35rkzgkjvlv12:\n<?xml version="1.0"?>\n<msg>\n\t<appmsg appid="wx233b7b61425c908e" sdkver="0">\n\t\t<title>有钱就花  媛小小</title>\n\t\t<des>锦岚（开玩笑就退群版）</des>\n\t\t<action>view</action>\n\t\t<type>3</type>\n\t\t<showtype>0</showtype>\n\t\t<soundtype>0</soundtype>\n\t\t<mediatagname />\n\t\t<messageext />\n\t\t<messageaction />\n\t\t<content />\n\t\t<contentattr>0</contentattr>\n\t\t<url>http://c.y.qq.com/v8/playsong.html?songmid=003RAIPi49Q0lo</url>\n\t\t<lowurl>http://c.y.qq.com/v8/playsong.html?songmid=003RAIPi49Q0lo</lowurl>\n\t\t<dataurl>http://wx.music.tc.qq.com/C400003RAIPi49Q0lo.m4a?guid=600026289&amp;vkey=7A342F23B609112838FC57D75589A18739AF945D0432DA3ADB1850A397219608F4F5219E79B0851645A1F6E891EB46654CFE2E4F02FE4041__v2b94c504&amp;uin=244513432&amp;fromtag=120032</dataurl>\n\t\t<lowdataurl>http://wx.music.tc.qq.com/C400003RAIPi49Q0lo.m4a?guid=600026289&amp;vkey=7A342F23B609112838FC57D75589A18739AF945D0432DA3ADB1850A397219608F4F5219E79B0851645A1F6E891EB46654CFE2E4F02FE4041__v2b94c504&amp;uin=244513432&amp;fromtag=120032</lowdataurl>\n\t\t<songalbumurl>http://wx.qlogo.cn/mmhead/ver_1/jdyGplDoicKn2ia1XL0VXvibjrdJvI3bra8LYibUzYvuvsgIo1YBwHUJLkjnjOvTicDwPSlUjiaMZIS5G76ltuFsWWU2woNhBICjCDxzU5NDuLp7mHZNmvib6m4n8ib6rIiaerhRmFvoUW3PPRepO8yeoBC2GKQ/0</songalbumurl>\n\t\t<songlyric>[ti:]\n[ar:]\n[al:]\n[by:天琴实验室AI生成v1.0]\n[offset:0]\n[00:00.00]有钱就花-媛小小\n[00:01.15]词曲：康诚\n[00:02.31]编曲：王亚东\n[00:03.46]混音：王亚东\n[00:04.62]吉他：LeoD \n[00:05.77]和声：扁桃体不发言\n[00:06.93]制作人：康诚\n[00:08.08]OP：星汉马文化\n[00:09.24]生活就像一面镜子你笑它也笑\n[00:13.50]命运就像天来注定管它到不到\n[00:17.70]愁是一天乐是一天首先要吃饱\n[00:21.90]快活一晚忙是一晚一定要睡好\n[00:26.07]人生就像马拉松赛谁都要上道\n[00:30.21]我就乐意慢慢的走谁也管不着\n[00:34.41]人生就像愤怒小鸟失败猪会笑\n[00:38.55]我像一颗无名小草没山让我靠\n[00:42.48]咱有钱就花 反正下个月还发\n[00:46.89]存着不花人要走了是不是白搭\n[00:50.58]人要及时行乐还要活在当下\n[00:54.75]哎呀妈呀听的我现在就想出发\n[00:59.16]咱有钱就花 反正下个月还发\n[01:03.60]留着不花人还走了是不是白瞎\n[01:07.29]人生这一幅画可要尽情的耍\n[01:11.43]哎呀妈呀听的我好想去趟拉萨\n[01:32.82]人生就像马拉松赛谁都要上道\n[01:36.99]我就乐意慢慢的走谁也管不着\n[01:41.19]人生就像愤怒小鸟失败猪会笑\n[01:45.36]我像一颗无名小草没山让我靠\n[01:49.29]咱有钱就花 反正下个月还发\n[01:53.67]存着不花人要走了是不是白搭\n[01:57.39]人要及时行乐还要活在当下\n[02:01.53]哎呀妈呀听的我现在就想出发\n[02:05.94]咱有钱就花 反正下个月还发\n[02:10.38]留着不花人还走了是不是白瞎\n[02:14.07]人生这一幅画可要尽情的耍\n[02:18.24]哎呀妈呀听的我好想去趟拉萨\n[02:22.65]咱有钱就花 反正下个月还发\n[02:27.09]存着不花人要走了是不是白搭\n[02:30.78]人要及时行乐还要活在当下\n[02:34.89]哎呀妈呀听的我现在就想出发\n[02:39.36]咱有钱就花 反正下个月还发\n[02:43.77]留着不花人还走了是不是白瞎\n[02:47.46]人生这一幅画可要尽情的耍\n[02:51.63]哎呀妈呀听的我好想去趟拉萨</songlyric>\n\t\t<appattach>\n\t\t\t<totallen>0</totallen>\n\t\t\t<attachid />\n\t\t\t<emoticonmd5></emoticonmd5>\n\t\t\t<fileext />\n\t\t\t<cdnthumbaeskey />\n\t\t\t<aeskey></aeskey>\n\t\t</appattach>\n\t\t<extinfo />\n\t\t<sourceusername />\n\t\t<sourcedisplayname />\n\t\t<thumburl>http://wx.qlogo.cn/mmhead/ver_1/jdyGplDoicKn2ia1XL0VXvibjrdJvI3bra8LYibUzYvuvsgIo1YBwHUJLkjnjOvTicDwPSlUjiaMZIS5G76ltuFsWWU2woNhBICjCDxzU5NDuLp7mHZNmvib6m4n8ib6rIiaerhRmFvoUW3PPRepO8yeoBC2GKQ/0</thumburl>\n\t\t<md5 />\n\t\t<statextstr />\n\t</appmsg>\n\t<fromusername>wxid_q35rkzgkjvlv12</fromusername>\n\t<scene>0</scene>\n\t<appinfo>\n\t\t<version>1</version>\n\t\t<appname />\n\t</appinfo>\n\t<commenturl />\n</msg>\n'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1753180951, 'MsgSource': '<msgsource>\n\t<sec_msg_node>\n\t\t<uuid>54db59e2657b1342990abdfe3ced6ee3_</uuid>\n\t\t<risk-file-flag />\n\t\t<risk-file-md5-list />\n\t</sec_msg_node>\n\t<silence>0</silence>\n\t<membercount>62</membercount>\n\t<signature>N0_V1_WIscgNC3|v1_TjdUAkgS</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'PushContent': '你收到了一条消息', 'NewMsgId': 1585618080249449733, 'MsgSeq': 871392687}
2025-07-22 18:42:20 | DEBUG | 从群聊消息中提取发送者: wxid_q35rkzgkjvlv12
2025-07-22 18:42:20 | DEBUG | XML消息完整内容:
<?xml version="1.0"?>
<msg>
	<appmsg appid="wx233b7b61425c908e" sdkver="0">
		<title>有钱就花  媛小小</title>
		<des>锦岚（开玩笑就退群版）</des>
		<action>view</action>
		<type>3</type>
		<showtype>0</showtype>
		<soundtype>0</soundtype>
		<mediatagname />
		<messageext />
		<messageaction />
		<content />
		<contentattr>0</contentattr>
		<url>http://c.y.qq.com/v8/playsong.html?songmid=003RAIPi49Q0lo</url>
		<lowurl>http://c.y.qq.com/v8/playsong.html?songmid=003RAIPi49Q0lo</lowurl>
		<dataurl>http://wx.music.tc.qq.com/C400003RAIPi49Q0lo.m4a?guid=600026289&amp;vkey=7A342F23B609112838FC57D75589A18739AF945D0432DA3ADB1850A397219608F4F5219E79B0851645A1F6E891EB46654CFE2E4F02FE4041__v2b94c504&amp;uin=244513432&amp;fromtag=120032</dataurl>
		<lowdataurl>http://wx.music.tc.qq.com/C400003RAIPi49Q0lo.m4a?guid=600026289&amp;vkey=7A342F23B609112838FC57D75589A18739AF945D0432DA3ADB1850A397219608F4F5219E79B0851645A1F6E891EB46654CFE2E4F02FE4041__v2b94c504&amp;uin=244513432&amp;fromtag=120032</lowdataurl>
		<songalbumurl>http://wx.qlogo.cn/mmhead/ver_1/jdyGplDoicKn2ia1XL0VXvibjrdJvI3bra8LYibUzYvuvsgIo1YBwHUJLkjnjOvTicDwPSlUjiaMZIS5G76ltuFsWWU2woNhBICjCDxzU5NDuLp7mHZNmvib6m4n8ib6rIiaerhRmFvoUW3PPRepO8yeoBC2GKQ/0</songalbumurl>
		<songlyric>[ti:]
[ar:]
[al:]
[by:天琴实验室AI生成v1.0]
[offset:0]
[00:00.00]有钱就花-媛小小
[00:01.15]词曲：康诚
[00:02.31]编曲：王亚东
[00:03.46]混音：王亚东
[00:04.62]吉他：LeoD 
[00:05.77]和声：扁桃体不发言
[00:06.93]制作人：康诚
[00:08.08]OP：星汉马文化
[00:09.24]生活就像一面镜子你笑它也笑
[00:13.50]命运就像天来注定管它到不到
[00:17.70]愁是一天乐是一天首先要吃饱
[00:21.90]快活一晚忙是一晚一定要睡好
[00:26.07]人生就像马拉松赛谁都要上道
[00:30.21]我就乐意慢慢的走谁也管不着
[00:34.41]人生就像愤怒小鸟失败猪会笑
[00:38.55]我像一颗无名小草没山让我靠
[00:42.48]咱有钱就花 反正下个月还发
[00:46.89]存着不花人要走了是不是白搭
[00:50.58]人要及时行乐还要活在当下
[00:54.75]哎呀妈呀听的我现在就想出发
[00:59.16]咱有钱就花 反正下个月还发
[01:03.60]留着不花人还走了是不是白瞎
[01:07.29]人生这一幅画可要尽情的耍
[01:11.43]哎呀妈呀听的我好想去趟拉萨
[01:32.82]人生就像马拉松赛谁都要上道
[01:36.99]我就乐意慢慢的走谁也管不着
[01:41.19]人生就像愤怒小鸟失败猪会笑
[01:45.36]我像一颗无名小草没山让我靠
[01:49.29]咱有钱就花 反正下个月还发
[01:53.67]存着不花人要走了是不是白搭
[01:57.39]人要及时行乐还要活在当下
[02:01.53]哎呀妈呀听的我现在就想出发
[02:05.94]咱有钱就花 反正下个月还发
[02:10.38]留着不花人还走了是不是白瞎
[02:14.07]人生这一幅画可要尽情的耍
[02:18.24]哎呀妈呀听的我好想去趟拉萨
[02:22.65]咱有钱就花 反正下个月还发
[02:27.09]存着不花人要走了是不是白搭
[02:30.78]人要及时行乐还要活在当下
[02:34.89]哎呀妈呀听的我现在就想出发
[02:39.36]咱有钱就花 反正下个月还发
[02:43.77]留着不花人还走了是不是白瞎
[02:47.46]人生这一幅画可要尽情的耍
[02:51.63]哎呀妈呀听的我好想去趟拉萨</songlyric>
		<appattach>
			<totallen>0</totallen>
			<attachid />
			<emoticonmd5></emoticonmd5>
			<fileext />
			<cdnthumbaeskey />
			<aeskey></aeskey>
		</appattach>
		<extinfo />
		<sourceusername />
		<sourcedisplayname />
		<thumburl>http://wx.qlogo.cn/mmhead/ver_1/jdyGplDoicKn2ia1XL0VXvibjrdJvI3bra8LYibUzYvuvsgIo1YBwHUJLkjnjOvTicDwPSlUjiaMZIS5G76ltuFsWWU2woNhBICjCDxzU5NDuLp7mHZNmvib6m4n8ib6rIiaerhRmFvoUW3PPRepO8yeoBC2GKQ/0</thumburl>
		<md5 />
		<statextstr />
	</appmsg>
	<fromusername>wxid_q35rkzgkjvlv12</fromusername>
	<scene>0</scene>
	<appinfo>
		<version>1</version>
		<appname />
	</appinfo>
	<commenturl />
</msg>

2025-07-22 18:42:20 | DEBUG | XML消息类型: 3
2025-07-22 18:42:20 | DEBUG | XML消息标题: 有钱就花  媛小小
2025-07-22 18:42:20 | DEBUG | XML消息描述: 锦岚（开玩笑就退群版）
2025-07-22 18:42:20 | DEBUG | 附件信息 totallen: 0
2025-07-22 18:42:20 | DEBUG | XML消息URL: http://c.y.qq.com/v8/playsong.html?songmid=003RAIPi49Q0lo
2025-07-22 18:42:20 | DEBUG | XML消息缩略图URL: http://wx.qlogo.cn/mmhead/ver_1/jdyGplDoicKn2ia1XL0VXvibjrdJvI3bra8LYibUzYvuvsgIo1YBwHUJLkjnjOvTicDwPSlUjiaMZIS5G76ltuFsWWU2woNhBICjCDxzU5NDuLp7mHZNmvib6m4n8ib6rIiaerhRmFvoUW3PPRepO8yeoBC2GKQ/0
2025-07-22 18:42:20 | INFO | 收到红包消息: 标题:有钱就花  媛小小 描述:锦岚（开玩笑就退群版） 来自:48097389945@chatroom 发送人:wxid_4usgcju5ey9q29
2025-07-22 18:42:20 | DEBUG | 收到消息: {'MsgId': 739535837, 'FromUserName': {'string': '48097389945@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 49, 'Content': {'string': 'wxid_bqljhmmoqlqm12:\n<msg>\n\t<appmsg appid="" sdkver="0">\n\t\t<title>有钱就花</title>\n\t\t<des>媛小小</des>\n\t\t<type>3</type>\n\t\t<dataurl>https://er-sycdn.kuwo.cn/0095b74e2e437187e903215fe89a5d78/687f6b17/resource/30106/trackmedia/F000000IOozq3dC6ar.flac</dataurl>\n\t\t<songalbumurl>http://img1.kwcdn.kuwo.cn/star/albumcover/320/s4s86/21/866658516.jpg</songalbumurl>\n\t\t<appattach>\n\t\t\t<cdnthumbaeskey />\n\t\t\t<aeskey />\n\t\t</appattach>\n\t</appmsg>\n\t<fromusername>wxid_bqljhmmoqlqm12</fromusername>\n\t<scene>0</scene>\n\t<appinfo>\n\t\t<version>1</version>\n\t\t<appname />\n\t</appinfo>\n\t<commenturl />\n</msg>\n'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1753180951, 'MsgSource': '<msgsource>\n\t<sec_msg_node>\n\t\t<uuid>e4e3b9a75b1fd572e77e07070d2abaf8_</uuid>\n\t\t<risk-file-flag />\n\t\t<risk-file-md5-list />\n\t</sec_msg_node>\n\t<silence>0</silence>\n\t<membercount>62</membercount>\n\t<signature>N0_V1_3eKzZlmq|v1_4TslImPk</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'PushContent': '你收到了一条消息', 'NewMsgId': 1600669409490404604, 'MsgSeq': 871392688}
2025-07-22 18:42:20 | DEBUG | 从群聊消息中提取发送者: wxid_bqljhmmoqlqm12
2025-07-22 18:42:20 | DEBUG | XML消息完整内容:
<msg>
	<appmsg appid="" sdkver="0">
		<title>有钱就花</title>
		<des>媛小小</des>
		<type>3</type>
		<dataurl>https://er-sycdn.kuwo.cn/0095b74e2e437187e903215fe89a5d78/687f6b17/resource/30106/trackmedia/F000000IOozq3dC6ar.flac</dataurl>
		<songalbumurl>http://img1.kwcdn.kuwo.cn/star/albumcover/320/s4s86/21/866658516.jpg</songalbumurl>
		<appattach>
			<cdnthumbaeskey />
			<aeskey />
		</appattach>
	</appmsg>
	<fromusername>wxid_bqljhmmoqlqm12</fromusername>
	<scene>0</scene>
	<appinfo>
		<version>1</version>
		<appname />
	</appinfo>
	<commenturl />
</msg>

2025-07-22 18:42:20 | DEBUG | XML消息类型: 3
2025-07-22 18:42:20 | DEBUG | XML消息标题: 有钱就花
2025-07-22 18:42:20 | DEBUG | XML消息描述: 媛小小
2025-07-22 18:42:20 | INFO | 收到红包消息: 标题:有钱就花 描述:媛小小 来自:48097389945@chatroom 发送人:wxid_4usgcju5ey9q29
2025-07-22 18:42:21 | DEBUG | 收到消息: {'MsgId': 1972773387, 'FromUserName': {'string': '48097389945@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 49, 'Content': {'string': 'xiaomaochong:\n<msg>\n\t<appmsg appid="wx0a022393a4dd43b2" sdkver="0">\n\t\t<title>有钱就花</title>\n\t\t<des>媛小小</des>\n\t\t<type>3</type>\n\t\t<url>https://er-sycdn.kuwo.cn/7163dfc1b59eb7cf63de87528636f954/687f6b17/resource/30106/trackmedia/M800000IOozq3dC6ar.mp3?from=bodian</url>\n\t\t<lowurl>https://er-sycdn.kuwo.cn/7163dfc1b59eb7cf63de87528636f954/687f6b17/resource/30106/trackmedia/M800000IOozq3dC6ar.mp3?from=bodian</lowurl>\n\t\t<dataurl>https://er-sycdn.kuwo.cn/7163dfc1b59eb7cf63de87528636f954/687f6b17/resource/30106/trackmedia/M800000IOozq3dC6ar.mp3?from=bodian</dataurl>\n\t\t<lowdataurl>https://er-sycdn.kuwo.cn/7163dfc1b59eb7cf63de87528636f954/687f6b17/resource/30106/trackmedia/M800000IOozq3dC6ar.mp3?from=bodian</lowdataurl>\n\t\t<songalbumurl>https://img3.kuwo.cn/star/starheads/700/s4s59/13/1073424014.jpg</songalbumurl>\n\t\t<songlyric>[00:00:00]来源于黄白助手点歌音乐</songlyric>\n\t\t<appattach>\n\t\t\t<cdnthumbaeskey />\n\t\t\t<aeskey />\n\t\t</appattach>\n\t</appmsg>\n\t<fromusername>xiaomaochong</fromusername>\n\t<scene>0</scene>\n\t<appinfo>\n\t\t<version>5</version>\n\t\t<appname>小爱同学</appname>\n\t</appinfo>\n\t<commenturl />\n</msg>\n'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1753180952, 'MsgSource': '<msgsource>\n\t<sec_msg_node>\n\t\t<uuid>f9745ab42a1b3cdc95f965f0600f2720_</uuid>\n\t\t<risk-file-flag />\n\t\t<risk-file-md5-list />\n\t</sec_msg_node>\n\t<silence>0</silence>\n\t<membercount>62</membercount>\n\t<signature>N0_V1_vj5qvZAy|v1_bXTlQdnl</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'PushContent': '你收到了一条消息', 'NewMsgId': 9164505100363659732, 'MsgSeq': 871392689}
2025-07-22 18:42:21 | DEBUG | 从群聊消息中提取发送者: xiaomaochong
2025-07-22 18:42:21 | DEBUG | XML消息完整内容:
<msg>
	<appmsg appid="wx0a022393a4dd43b2" sdkver="0">
		<title>有钱就花</title>
		<des>媛小小</des>
		<type>3</type>
		<url>https://er-sycdn.kuwo.cn/7163dfc1b59eb7cf63de87528636f954/687f6b17/resource/30106/trackmedia/M800000IOozq3dC6ar.mp3?from=bodian</url>
		<lowurl>https://er-sycdn.kuwo.cn/7163dfc1b59eb7cf63de87528636f954/687f6b17/resource/30106/trackmedia/M800000IOozq3dC6ar.mp3?from=bodian</lowurl>
		<dataurl>https://er-sycdn.kuwo.cn/7163dfc1b59eb7cf63de87528636f954/687f6b17/resource/30106/trackmedia/M800000IOozq3dC6ar.mp3?from=bodian</dataurl>
		<lowdataurl>https://er-sycdn.kuwo.cn/7163dfc1b59eb7cf63de87528636f954/687f6b17/resource/30106/trackmedia/M800000IOozq3dC6ar.mp3?from=bodian</lowdataurl>
		<songalbumurl>https://img3.kuwo.cn/star/starheads/700/s4s59/13/1073424014.jpg</songalbumurl>
		<songlyric>[00:00:00]来源于黄白助手点歌音乐</songlyric>
		<appattach>
			<cdnthumbaeskey />
			<aeskey />
		</appattach>
	</appmsg>
	<fromusername>xiaomaochong</fromusername>
	<scene>0</scene>
	<appinfo>
		<version>5</version>
		<appname>小爱同学</appname>
	</appinfo>
	<commenturl />
</msg>

2025-07-22 18:42:21 | DEBUG | XML消息类型: 3
2025-07-22 18:42:21 | DEBUG | XML消息标题: 有钱就花
2025-07-22 18:42:21 | DEBUG | XML消息描述: 媛小小
2025-07-22 18:42:21 | DEBUG | XML消息URL: https://er-sycdn.kuwo.cn/7163dfc1b59eb7cf63de87528636f954/687f6b17/resource/30106/trackmedia/M800000IOozq3dC6ar.mp3?from=bodian
2025-07-22 18:42:21 | INFO | 收到红包消息: 标题:有钱就花 描述:媛小小 来自:48097389945@chatroom 发送人:wxid_4usgcju5ey9q29
2025-07-22 18:42:22 | DEBUG | 收到消息: {'MsgId': 1825206639, 'FromUserName': {'string': '48097389945@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 49, 'Content': {'string': 'wxid_nlmjroes6ot322:\n<msg>\n\t<appmsg appid="wx0a022393a4dd43b2" sdkver="0">\n\t\t<title>有钱就花</title>\n\t\t<des>媛小小</des>\n\t\t<type>3</type>\n\t\t<url>https://er-sycdn.kuwo.cn/7163dfc1b59eb7cf63de87528636f954/687f6b17/resource/30106/trackmedia/M800000IOozq3dC6ar.mp3?from=bodian</url>\n\t\t<lowurl>https://er-sycdn.kuwo.cn/7163dfc1b59eb7cf63de87528636f954/687f6b17/resource/30106/trackmedia/M800000IOozq3dC6ar.mp3?from=bodian</lowurl>\n\t\t<dataurl>https://er-sycdn.kuwo.cn/7163dfc1b59eb7cf63de87528636f954/687f6b17/resource/30106/trackmedia/M800000IOozq3dC6ar.mp3?from=bodian</dataurl>\n\t\t<lowdataurl>https://er-sycdn.kuwo.cn/7163dfc1b59eb7cf63de87528636f954/687f6b17/resource/30106/trackmedia/M800000IOozq3dC6ar.mp3?from=bodian</lowdataurl>\n\t\t<songalbumurl>https://img3.kuwo.cn/star/starheads/700/s4s59/13/1073424014.jpg</songalbumurl>\n\t\t<songlyric>[00:00:00]来源于黄白助手点歌音乐</songlyric>\n\t\t<appattach>\n\t\t\t<cdnthumbaeskey />\n\t\t\t<aeskey />\n\t\t</appattach>\n\t</appmsg>\n\t<fromusername>wxid_nlmjroes6ot322</fromusername>\n\t<scene>0</scene>\n\t<appinfo>\n\t\t<version>5</version>\n\t\t<appname>小爱同学</appname>\n\t</appinfo>\n\t<commenturl />\n</msg>\n'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1753180952, 'MsgSource': '<msgsource>\n\t<sec_msg_node>\n\t\t<uuid>75f666123e607f3a2c687c4b806d980e_</uuid>\n\t\t<risk-file-flag />\n\t\t<risk-file-md5-list />\n\t</sec_msg_node>\n\t<silence>0</silence>\n\t<membercount>62</membercount>\n\t<signature>N0_V1_BKzAcyZg|v1_FkguE+w0</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'PushContent': '你收到了一条消息', 'NewMsgId': 2127651085537077401, 'MsgSeq': 871392690}
2025-07-22 18:42:22 | DEBUG | 从群聊消息中提取发送者: wxid_nlmjroes6ot322
2025-07-22 18:42:22 | DEBUG | XML消息完整内容:
<msg>
	<appmsg appid="wx0a022393a4dd43b2" sdkver="0">
		<title>有钱就花</title>
		<des>媛小小</des>
		<type>3</type>
		<url>https://er-sycdn.kuwo.cn/7163dfc1b59eb7cf63de87528636f954/687f6b17/resource/30106/trackmedia/M800000IOozq3dC6ar.mp3?from=bodian</url>
		<lowurl>https://er-sycdn.kuwo.cn/7163dfc1b59eb7cf63de87528636f954/687f6b17/resource/30106/trackmedia/M800000IOozq3dC6ar.mp3?from=bodian</lowurl>
		<dataurl>https://er-sycdn.kuwo.cn/7163dfc1b59eb7cf63de87528636f954/687f6b17/resource/30106/trackmedia/M800000IOozq3dC6ar.mp3?from=bodian</dataurl>
		<lowdataurl>https://er-sycdn.kuwo.cn/7163dfc1b59eb7cf63de87528636f954/687f6b17/resource/30106/trackmedia/M800000IOozq3dC6ar.mp3?from=bodian</lowdataurl>
		<songalbumurl>https://img3.kuwo.cn/star/starheads/700/s4s59/13/1073424014.jpg</songalbumurl>
		<songlyric>[00:00:00]来源于黄白助手点歌音乐</songlyric>
		<appattach>
			<cdnthumbaeskey />
			<aeskey />
		</appattach>
	</appmsg>
	<fromusername>wxid_nlmjroes6ot322</fromusername>
	<scene>0</scene>
	<appinfo>
		<version>5</version>
		<appname>小爱同学</appname>
	</appinfo>
	<commenturl />
</msg>

2025-07-22 18:42:22 | DEBUG | XML消息类型: 3
2025-07-22 18:42:22 | DEBUG | XML消息标题: 有钱就花
2025-07-22 18:42:22 | DEBUG | XML消息描述: 媛小小
2025-07-22 18:42:22 | DEBUG | XML消息URL: https://er-sycdn.kuwo.cn/7163dfc1b59eb7cf63de87528636f954/687f6b17/resource/30106/trackmedia/M800000IOozq3dC6ar.mp3?from=bodian
2025-07-22 18:42:22 | INFO | 收到红包消息: 标题:有钱就花 描述:媛小小 来自:48097389945@chatroom 发送人:wxid_4usgcju5ey9q29
2025-07-22 18:42:22 | DEBUG | 收到消息: {'MsgId': 295237208, 'FromUserName': {'string': '48097389945@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 49, 'Content': {'string': 'wxid_8l9ymg1mafud12:\n<?xml version="1.0"?>\n<msg>\n\t<appmsg appid="wx25a5ad4ed63c2176" sdkver="0">\n\t\t<title>有钱就花</title>\n\t\t<des>媛小小</des>\n\t\t<action>view</action>\n\t\t<type>3</type>\n\t\t<showtype>0</showtype>\n\t\t<soundtype>0</soundtype>\n\t\t<mediatagname />\n\t\t<messageext />\n\t\t<messageaction />\n\t\t<content />\n\t\t<contentattr>0</contentattr>\n\t\t<url>https://wx.music.tc.qq.com/C400003RAIPi49Q0lo.m4a?guid=834160952&amp;vkey=78C1EE5ECFFF34E275B1E73433395FDF4C8EAE21E62D84C9627158D3FA21E70EF803EBEAAC677CAA97E1FA7965F54A24449CA48B63C385E8__v2b9ab2c0&amp;uin=704900150&amp;fromtag=120032</url>\n\t\t<lowurl>https://wx.music.tc.qq.com/C400003RAIPi49Q0lo.m4a?guid=834160952&amp;vkey=78C1EE5ECFFF34E275B1E73433395FDF4C8EAE21E62D84C9627158D3FA21E70EF803EBEAAC677CAA97E1FA7965F54A24449CA48B63C385E8__v2b9ab2c0&amp;uin=704900150&amp;fromtag=120032</lowurl>\n\t\t<dataurl>https://wx.music.tc.qq.com/C400003RAIPi49Q0lo.m4a?guid=834160952&amp;vkey=78C1EE5ECFFF34E275B1E73433395FDF4C8EAE21E62D84C9627158D3FA21E70EF803EBEAAC677CAA97E1FA7965F54A24449CA48B63C385E8__v2b9ab2c0&amp;uin=704900150&amp;fromtag=120032</dataurl>\n\t\t<lowdataurl>https://wx.music.tc.qq.com/C400003RAIPi49Q0lo.m4a?guid=834160952&amp;vkey=78C1EE5ECFFF34E275B1E73433395FDF4C8EAE21E62D84C9627158D3FA21E70EF803EBEAAC677CAA97E1FA7965F54A24449CA48B63C385E8__v2b9ab2c0&amp;uin=704900150&amp;fromtag=120032</lowdataurl>\n\t\t<appattach>\n\t\t\t<totallen>0</totallen>\n\t\t\t<attachid />\n\t\t\t<emoticonmd5 />\n\t\t\t<fileext />\n\t\t\t<cdnthumbaeskey />\n\t\t\t<aeskey />\n\t\t</appattach>\n\t\t<extinfo />\n\t\t<sourceusername />\n\t\t<sourcedisplayname />\n\t\t<thumburl>https://y.qq.com/music/photo_new/T002R300x300M000002dNHxU0UU6th.jpg?max_age=2592000</thumburl>\n\t\t<md5 />\n\t\t<statextstr />\n\t</appmsg>\n\t<fromusername>wxid_8l9ymg1mafud12</fromusername>\n\t<scene>0</scene>\n\t<appinfo>\n\t\t<version>1</version>\n\t\t<appname></appname>\n\t</appinfo>\n\t<commenturl></commenturl>\n</msg>\n'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1753180952, 'MsgSource': '<msgsource>\n\t<sec_msg_node>\n\t\t<uuid>31ade47a39ef65fe185282a01cba55a9_</uuid>\n\t\t<risk-file-flag />\n\t\t<risk-file-md5-list />\n\t</sec_msg_node>\n\t<silence>0</silence>\n\t<membercount>62</membercount>\n\t<signature>N0_V1_SrkeH4Xc|v1_yI1bPhEL</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'PushContent': '你收到了一条消息', 'NewMsgId': 3017697461718727730, 'MsgSeq': 871392691}
2025-07-22 18:42:22 | DEBUG | 从群聊消息中提取发送者: wxid_8l9ymg1mafud12
2025-07-22 18:42:22 | DEBUG | XML消息完整内容:
<?xml version="1.0"?>
<msg>
	<appmsg appid="wx25a5ad4ed63c2176" sdkver="0">
		<title>有钱就花</title>
		<des>媛小小</des>
		<action>view</action>
		<type>3</type>
		<showtype>0</showtype>
		<soundtype>0</soundtype>
		<mediatagname />
		<messageext />
		<messageaction />
		<content />
		<contentattr>0</contentattr>
		<url>https://wx.music.tc.qq.com/C400003RAIPi49Q0lo.m4a?guid=834160952&amp;vkey=78C1EE5ECFFF34E275B1E73433395FDF4C8EAE21E62D84C9627158D3FA21E70EF803EBEAAC677CAA97E1FA7965F54A24449CA48B63C385E8__v2b9ab2c0&amp;uin=704900150&amp;fromtag=120032</url>
		<lowurl>https://wx.music.tc.qq.com/C400003RAIPi49Q0lo.m4a?guid=834160952&amp;vkey=78C1EE5ECFFF34E275B1E73433395FDF4C8EAE21E62D84C9627158D3FA21E70EF803EBEAAC677CAA97E1FA7965F54A24449CA48B63C385E8__v2b9ab2c0&amp;uin=704900150&amp;fromtag=120032</lowurl>
		<dataurl>https://wx.music.tc.qq.com/C400003RAIPi49Q0lo.m4a?guid=834160952&amp;vkey=78C1EE5ECFFF34E275B1E73433395FDF4C8EAE21E62D84C9627158D3FA21E70EF803EBEAAC677CAA97E1FA7965F54A24449CA48B63C385E8__v2b9ab2c0&amp;uin=704900150&amp;fromtag=120032</dataurl>
		<lowdataurl>https://wx.music.tc.qq.com/C400003RAIPi49Q0lo.m4a?guid=834160952&amp;vkey=78C1EE5ECFFF34E275B1E73433395FDF4C8EAE21E62D84C9627158D3FA21E70EF803EBEAAC677CAA97E1FA7965F54A24449CA48B63C385E8__v2b9ab2c0&amp;uin=704900150&amp;fromtag=120032</lowdataurl>
		<appattach>
			<totallen>0</totallen>
			<attachid />
			<emoticonmd5 />
			<fileext />
			<cdnthumbaeskey />
			<aeskey />
		</appattach>
		<extinfo />
		<sourceusername />
		<sourcedisplayname />
		<thumburl>https://y.qq.com/music/photo_new/T002R300x300M000002dNHxU0UU6th.jpg?max_age=2592000</thumburl>
		<md5 />
		<statextstr />
	</appmsg>
	<fromusername>wxid_8l9ymg1mafud12</fromusername>
	<scene>0</scene>
	<appinfo>
		<version>1</version>
		<appname></appname>
	</appinfo>
	<commenturl></commenturl>
</msg>

2025-07-22 18:42:22 | DEBUG | XML消息类型: 3
2025-07-22 18:42:22 | DEBUG | XML消息标题: 有钱就花
2025-07-22 18:42:22 | DEBUG | XML消息描述: 媛小小
2025-07-22 18:42:22 | DEBUG | 附件信息 totallen: 0
2025-07-22 18:42:22 | DEBUG | XML消息URL: https://wx.music.tc.qq.com/C400003RAIPi49Q0lo.m4a?guid=834160952&vkey=78C1EE5ECFFF34E275B1E73433395FDF4C8EAE21E62D84C9627158D3FA21E70EF803EBEAAC677CAA97E1FA7965F54A24449CA48B63C385E8__v2b9ab2c0&uin=704900150&fromtag=120032
2025-07-22 18:42:22 | DEBUG | XML消息缩略图URL: https://y.qq.com/music/photo_new/T002R300x300M000002dNHxU0UU6th.jpg?max_age=2592000
2025-07-22 18:42:22 | INFO | 收到红包消息: 标题:有钱就花 描述:媛小小 来自:48097389945@chatroom 发送人:wxid_4usgcju5ey9q29
2025-07-22 18:42:23 | DEBUG | 收到消息: {'MsgId': 1617198160, 'FromUserName': {'string': '48097389945@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 49, 'Content': {'string': 'wxid_z2oepgiz8qcs22:\n<msg>\n\t<appmsg appid="" sdkver="0">\n\t\t<title>有钱就花</title>\n\t\t<des>媛小小</des>\n\t\t<type>3</type>\n\t\t<dataurl>https://er-sycdn.kuwo.cn/cdb87d997eea26f2325cbed5cb76a012/687f6b18/resource/30106/trackmedia/F000000IOozq3dC6ar.flac?src=无损音质 FLAC_21.01 MB?src=flac_21.01 MB?from=longzhu_api</dataurl>\n\t\t<songalbumurl>http://imge.kugou.com/stdmusic/400/20250520/20250520174139458104.jpg</songalbumurl>\n\t\t<appattach>\n\t\t\t<cdnthumbaeskey />\n\t\t\t<aeskey />\n\t\t</appattach>\n\t</appmsg>\n\t<fromusername>wxid_z2oepgiz8qcs22</fromusername>\n\t<scene>0</scene>\n\t<appinfo>\n\t\t<version>1</version>\n\t\t<appname />\n\t</appinfo>\n\t<commenturl />\n</msg>\n'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1753180952, 'MsgSource': '<msgsource>\n\t<sec_msg_node>\n\t\t<uuid>3d8f768151ac81564af405b908e9c77e_</uuid>\n\t\t<risk-file-flag />\n\t\t<risk-file-md5-list />\n\t</sec_msg_node>\n\t<silence>0</silence>\n\t<membercount>62</membercount>\n\t<signature>N0_V1_U27u7WyA|v1_niT5l8a4</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'PushContent': '你收到了一条消息', 'NewMsgId': 3087494752839268184, 'MsgSeq': 871392692}
2025-07-22 18:42:23 | DEBUG | 从群聊消息中提取发送者: wxid_z2oepgiz8qcs22
2025-07-22 18:42:23 | DEBUG | XML消息完整内容:
<msg>
	<appmsg appid="" sdkver="0">
		<title>有钱就花</title>
		<des>媛小小</des>
		<type>3</type>
		<dataurl>https://er-sycdn.kuwo.cn/cdb87d997eea26f2325cbed5cb76a012/687f6b18/resource/30106/trackmedia/F000000IOozq3dC6ar.flac?src=无损音质 FLAC_21.01 MB?src=flac_21.01 MB?from=longzhu_api</dataurl>
		<songalbumurl>http://imge.kugou.com/stdmusic/400/20250520/20250520174139458104.jpg</songalbumurl>
		<appattach>
			<cdnthumbaeskey />
			<aeskey />
		</appattach>
	</appmsg>
	<fromusername>wxid_z2oepgiz8qcs22</fromusername>
	<scene>0</scene>
	<appinfo>
		<version>1</version>
		<appname />
	</appinfo>
	<commenturl />
</msg>

2025-07-22 18:42:23 | DEBUG | XML消息类型: 3
2025-07-22 18:42:23 | DEBUG | XML消息标题: 有钱就花
2025-07-22 18:42:23 | DEBUG | XML消息描述: 媛小小
2025-07-22 18:42:23 | INFO | 收到红包消息: 标题:有钱就花 描述:媛小小 来自:48097389945@chatroom 发送人:wxid_4usgcju5ey9q29
2025-07-22 18:42:24 | DEBUG | 收到消息: {'MsgId': 964082837, 'FromUserName': {'string': '48097389945@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 49, 'Content': {'string': 'wxid_lneb7n23o4lg12:\n<?xml version="1.0"?>\n<msg>\n\t<appmsg appid="wx485a97c844086dc9" sdkver="0">\n\t\t<title>有钱随便花-DJ完整版-江哥</title>\n\t\t<des>无妄-抖音</des>\n\t\t<username />\n\t\t<action>view</action>\n\t\t<type>3</type>\n\t\t<showtype>0</showtype>\n\t\t<content />\n\t\t<url />\n\t\t<lowurl />\n\t\t<forwardflag>0</forwardflag>\n\t\t<dataurl>https://v3-ktv.douyinvod.com/b79cf6623b2f6201623acd997118e10d/6880bd64/video/tos/cn/tos-cn-ve-2774/okIQtxWWpAAGMwsaIxUB7fhAcxzHKKQB2Nwtxo/</dataurl>\n\t\t<lowdataurl />\n\t\t<contentattr>0</contentattr>\n\t\t<streamvideo>\n\t\t\t<streamvideourl />\n\t\t\t<streamvideototaltime>0</streamvideototaltime>\n\t\t\t<streamvideotitle />\n\t\t\t<streamvideowording />\n\t\t\t<streamvideoweburl />\n\t\t\t<streamvideothumburl />\n\t\t\t<streamvideoaduxinfo />\n\t\t\t<streamvideopublishid />\n\t\t</streamvideo>\n\t\t<canvasPageItem>\n\t\t\t<canvasPageXml><![CDATA[]]></canvasPageXml>\n\t\t</canvasPageItem>\n\t\t<appattach>\n\t\t\t<totallen>0</totallen>\n\t\t\t<attachid />\n\t\t\t<cdnattachurl />\n\t\t\t<emoticonmd5 />\n\t\t\t<aeskey />\n\t\t\t<fileext />\n\t\t\t<islargefilemsg>0</islargefilemsg>\n\t\t</appattach>\n\t\t<extinfo />\n\t\t<androidsource>2</androidsource>\n\t\t<thumburl />\n\t\t<mediatagname />\n\t\t<messageaction><![CDATA[]]></messageaction>\n\t\t<messageext><![CDATA[]]></messageext>\n\t\t<emoticongift>\n\t\t\t<packageflag>0</packageflag>\n\t\t\t<packageid />\n\t\t</emoticongift>\n\t\t<emoticonshared>\n\t\t\t<packageflag>0</packageflag>\n\t\t\t<packageid />\n\t\t</emoticonshared>\n\t\t<designershared>\n\t\t\t<designeruin>0</designeruin>\n\t\t\t<designername>null</designername>\n\t\t\t<designerrediretcturl><![CDATA[null]]></designerrediretcturl>\n\t\t</designershared>\n\t\t<emotionpageshared>\n\t\t\t<tid>0</tid>\n\t\t\t<title>null</title>\n\t\t\t<desc>null</desc>\n\t\t\t<iconUrl><![CDATA[null]]></iconUrl>\n\t\t\t<secondUrl>null</secondUrl>\n\t\t\t<pageType>0</pageType>\n\t\t\t<setKey>null</setKey>\n\t\t</emotionpageshared>\n\t\t<webviewshared>\n\t\t\t<shareUrlOriginal />\n\t\t\t<shareUrlOpen />\n\t\t\t<jsAppId />\n\t\t\t<publisherId />\n\t\t\t<publisherReqId />\n\t\t</webviewshared>\n\t\t<template_id />\n\t\t<md5 />\n\t\t<websearch>\n\t\t\t<rec_category>0</rec_category>\n\t\t\t<channelId>0</channelId>\n\t\t</websearch>\n\t\t<weappinfo>\n\t\t\t<username />\n\t\t\t<appid />\n\t\t\t<appservicetype>0</appservicetype>\n\t\t\t<secflagforsinglepagemode>0</secflagforsinglepagemode>\n\t\t\t<videopageinfo>\n\t\t\t\t<thumbwidth>0</thumbwidth>\n\t\t\t\t<thumbheight>0</thumbheight>\n\t\t\t\t<fromopensdk>0</fromopensdk>\n\t\t\t</videopageinfo>\n\t\t</weappinfo>\n\t\t<statextstr>GhQKEnd4NDg1YTk3Yzg0NDA4NmRjOQ==</statextstr>\n\t\t<songalbumurl>https://wx.qlogo.cn/mmhead/ver_1/iaZCKGjtAic5icOnI1ibaY7DCzx0SVR3ic6NXwcAHpFTZIzDcvzMaeNlEYqezUL04icbD6ZylLwcGMYIzQy6kjY4AdicRyb39fASpxswdSeDrs5tomibd2sADOgpbRIF6jic2v1ibFafdgicYm69maZehIgNhg8Tw/0</songalbumurl>\n\t\t<songlyric>[00:22.31]这些年一个人\n[00:24.43]在外飘呀飘\n[00:26.33]三十了好几呀\n[00:28.76]还没有成家\n[00:31.03]赚的钱还不够\n[00:33.44]一个人花\n[00:35.16]不知何时才能把财发\n[00:39.46]亲爱的那个她\n[00:41.59]生个胖娃娃\n[00:43.71]上有老下有小\n[00:45.93]更不容易呀\n[00:48.17]我也要努力的\n[00:50.14]去挣钱啊\n[00:52.44]这个社会压力山大\n[00:56.43]等我有了钱有钱随便花\n[01:00.83]想买啥来就买啥\n[01:05.18]抽烟我只抽大中华\n[01:09.43]开车就要开宝马\n[01:14.05]等我有了钱有钱随便花\n[01:17.94]想吃啥来就吃啥\n[01:22.24]人生就是这么回事啊\n[01:26.63]活就活得潇潇洒洒\n[01:48.18]亲爱的那个她\n[01:50.15]生个胖娃娃\n[01:52.27]上有老下有小\n[01:54.50]更不容易呀\n[01:56.62]我也要努力的\n[01:58.74]去挣钱啊\n[02:00.87]这个社会压力山大\n[02:05.17]等我有了钱有钱随便花\n[02:09.42]想买啥来就买啥\n[02:13.67]抽烟我只抽大中华\n[02:18.03]开车就要开宝马\n[02:22.33]等我有了钱有钱随便花\n[02:26.54]想吃啥来就吃啥\n[02:30.89]人生就是这么回事啊\n[02:35.09]活就活得潇潇洒洒\n[02:39.59]等我有了钱有钱随便花\n[02:43.64]想买啥来就买啥\n[02:47.99]抽烟我只抽大中华\n[02:52.25]开车就要开宝马\n[02:56.56]等我有了钱有钱随便花\n[03:00.82]想吃啥来就吃啥\n[03:05.17]人生就是这么回事啊\n[03:09.42]活就活得潇潇洒洒\r\n</songlyric>\n\t\t<musicShareItem>\n\t\t\t<musicDuration>0</musicDuration>\n\t\t</musicShareItem>\n\t\t<finderLiveProductShare>\n\t\t\t<finderLiveID><![CDATA[]]></finderLiveID>\n\t\t\t<finderUsername><![CDATA[]]></finderUsername>\n\t\t\t<finderObjectID><![CDATA[]]></finderObjectID>\n\t\t\t<finderNonceID><![CDATA[]]></finderNonceID>\n\t\t\t<liveStatus><![CDATA[]]></liveStatus>\n\t\t\t<appId><![CDATA[]]></appId>\n\t\t\t<pagePath><![CDATA[]]></pagePath>\n\t\t\t<productId><![CDATA[]]></productId>\n\t\t\t<coverUrl><![CDATA[]]></coverUrl>\n\t\t\t<productTitle><![CDATA[]]></productTitle>\n\t\t\t<marketPrice><![CDATA[0]]></marketPrice>\n\t\t\t<sellingPrice><![CDATA[0]]></sellingPrice>\n\t\t\t<platformHeadImg><![CDATA[]]></platformHeadImg>\n\t\t\t<platformName><![CDATA[]]></platformName>\n\t\t\t<shopWindowId><![CDATA[]]></shopWindowId>\n\t\t\t<flashSalePrice><![CDATA[0]]></flashSalePrice>\n\t\t\t<flashSaleEndTime><![CDATA[0]]></flashSaleEndTime>\n\t\t\t<ecSource><![CDATA[]]></ecSource>\n\t\t\t<sellingPriceWording><![CDATA[]]></sellingPriceWording>\n\t\t\t<platformIconURL><![CDATA[]]></platformIconURL>\n\t\t\t<firstProductTagURL><![CDATA[]]></firstProductTagURL>\n\t\t\t<firstProductTagAspectRatioString><![CDATA[0.0]]></firstProductTagAspectRatioString>\n\t\t\t<secondProductTagURL><![CDATA[]]></secondProductTagURL>\n\t\t\t<secondProductTagAspectRatioString><![CDATA[0.0]]></secondProductTagAspectRatioString>\n\t\t\t<firstGuaranteeWording><![CDATA[]]></firstGuaranteeWording>\n\t\t\t<secondGuaranteeWording><![CDATA[]]></secondGuaranteeWording>\n\t\t\t<thirdGuaranteeWording><![CDATA[]]></thirdGuaranteeWording>\n\t\t\t<isPriceBeginShow>false</isPriceBeginShow>\n\t\t\t<lastGMsgID><![CDATA[]]></lastGMsgID>\n\t\t\t<promoterKey><![CDATA[]]></promoterKey>\n\t\t\t<discountWording><![CDATA[]]></discountWording>\n\t\t\t<priceSuffixDescription><![CDATA[]]></priceSuffixDescription>\n\t\t\t<showBoxItemStringList />\n\t\t</finderLiveProductShare>\n\t\t<finderOrder>\n\t\t\t<appID><![CDATA[]]></appID>\n\t\t\t<orderID><![CDATA[]]></orderID>\n\t\t\t<path><![CDATA[]]></path>\n\t\t\t<priceWording><![CDATA[]]></priceWording>\n\t\t\t<stateWording><![CDATA[]]></stateWording>\n\t\t\t<productImageURL><![CDATA[]]></productImageURL>\n\t\t\t<products><![CDATA[]]></products>\n\t\t\t<productsCount><![CDATA[0]]></productsCount>\n\t\t</finderOrder>\n\t\t<finderShopWindowShare>\n\t\t\t<finderUsername><![CDATA[]]></finderUsername>\n\t\t\t<avatar><![CDATA[]]></avatar>\n\t\t\t<nickname><![CDATA[]]></nickname>\n\t\t\t<commodityInStockCount><![CDATA[]]></commodityInStockCount>\n\t\t\t<appId><![CDATA[]]></appId>\n\t\t\t<path><![CDATA[]]></path>\n\t\t\t<appUsername><![CDATA[]]></appUsername>\n\t\t\t<query><![CDATA[]]></query>\n\t\t\t<liteAppId><![CDATA[]]></liteAppId>\n\t\t\t<liteAppPath><![CDATA[]]></liteAppPath>\n\t\t\t<liteAppQuery><![CDATA[]]></liteAppQuery>\n\t\t\t<platformTagURL><![CDATA[]]></platformTagURL>\n\t\t\t<saleWording><![CDATA[]]></saleWording>\n\t\t\t<lastGMsgID><![CDATA[]]></lastGMsgID>\n\t\t\t<profileTypeWording><![CDATA[]]></profileTypeWording>\n\t\t\t<reputationInfo>\n\t\t\t\t<hasReputationInfo>0</hasReputationInfo>\n\t\t\t\t<reputationScore>0</reputationScore>\n\t\t\t\t<reputationWording />\n\t\t\t\t<reputationTextColor />\n\t\t\t\t<reputationLevelWording />\n\t\t\t\t<reputationBackgroundColor />\n\t\t\t</reputationInfo>\n\t\t\t<productImageURLList />\n\t\t</finderShopWindowShare>\n\t\t<findernamecard>\n\t\t\t<username />\n\t\t\t<avatar><![CDATA[]]></avatar>\n\t\t\t<nickname />\n\t\t\t<auth_job />\n\t\t\t<auth_icon>0</auth_icon>\n\t\t\t<auth_icon_url />\n\t\t\t<ecSource><![CDATA[]]></ecSource>\n\t\t\t<lastGMsgID><![CDATA[]]></lastGMsgID>\n\t\t</findernamecard>\n\t\t<finderGuarantee>\n\t\t\t<scene><![CDATA[0]]></scene>\n\t\t</finderGuarantee>\n\t\t<directshare>0</directshare>\n\t\t<gamecenter>\n\t\t\t<namecard>\n\t\t\t\t<iconUrl />\n\t\t\t\t<name />\n\t\t\t\t<desc />\n\t\t\t\t<tail />\n\t\t\t\t<jumpUrl />\n\t\t\t</namecard>\n\t\t</gamecenter>\n\t\t<patMsg>\n\t\t\t<chatUser />\n\t\t\t<records>\n\t\t\t\t<recordNum>0</recordNum>\n\t\t\t</records>\n\t\t</patMsg>\n\t\t<secretmsg>\n\t\t\t<issecretmsg>0</issecretmsg>\n\t\t</secretmsg>\n\t\t<referfromscene>0</referfromscene>\n\t\t<gameshare>\n\t\t\t<liteappext>\n\t\t\t\t<liteappbizdata />\n\t\t\t\t<priority>0</priority>\n\t\t\t</liteappext>\n\t\t\t<appbrandext>\n\t\t\t\t<litegameinfo />\n\t\t\t\t<priority>-1</priority>\n\t\t\t</appbrandext>\n\t\t\t<gameshareid />\n\t\t\t<sharedata />\n\t\t\t<isvideo>0</isvideo>\n\t\t\t<duration>-1</duration>\n\t\t\t<isexposed>0</isexposed>\n\t\t\t<readtext />\n\t\t</gameshare>\n\t\t<mpsharetrace>\n\t\t\t<hasfinderelement>0</hasfinderelement>\n\t\t\t<lastgmsgid />\n\t\t</mpsharetrace>\n\t\t<wxgamecard>\n\t\t\t<framesetname />\n\t\t\t<mbcarddata />\n\t\t\t<minpkgversion />\n\t\t\t<mbcardheight>0</mbcardheight>\n\t\t\t<isoldversion>0</isoldversion>\n\t\t</wxgamecard>\n\t\t<liteapp>\n\t\t\t<id>null</id>\n\t\t\t<path />\n\t\t\t<query />\n\t\t</liteapp>\n\t</appmsg>\n\t<fromusername>wxid_lneb7n23o4lg12</fromusername>\n\t<scene>0</scene>\n\t<appinfo>\n\t\t<version>29</version>\n\t\t<appname>摇一摇搜歌</appname>\n\t</appinfo>\n\t<commenturl></commenturl>\n</msg>\n'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1753180952, 'MsgSource': '<msgsource>\n\t<sec_msg_node>\n\t\t<uuid>4a2553e8620178aead10b86dcf869ea8_</uuid>\n\t\t<risk-file-flag />\n\t\t<risk-file-md5-list />\n\t</sec_msg_node>\n\t<silence>0</silence>\n\t<membercount>62</membercount>\n\t<signature>N0_V1_ZGJXARhb|v1_aAqMODuA</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'PushContent': '你收到了一条消息', 'NewMsgId': 8032605474147224080, 'MsgSeq': 871392693}
2025-07-22 18:42:24 | DEBUG | 从群聊消息中提取发送者: wxid_lneb7n23o4lg12
2025-07-22 18:42:24 | DEBUG | XML消息完整内容:
<?xml version="1.0"?>
<msg>
	<appmsg appid="wx485a97c844086dc9" sdkver="0">
		<title>有钱随便花-DJ完整版-江哥</title>
		<des>无妄-抖音</des>
		<username />
		<action>view</action>
		<type>3</type>
		<showtype>0</showtype>
		<content />
		<url />
		<lowurl />
		<forwardflag>0</forwardflag>
		<dataurl>https://v3-ktv.douyinvod.com/b79cf6623b2f6201623acd997118e10d/6880bd64/video/tos/cn/tos-cn-ve-2774/okIQtxWWpAAGMwsaIxUB7fhAcxzHKKQB2Nwtxo/</dataurl>
		<lowdataurl />
		<contentattr>0</contentattr>
		<streamvideo>
			<streamvideourl />
			<streamvideototaltime>0</streamvideototaltime>
			<streamvideotitle />
			<streamvideowording />
			<streamvideoweburl />
			<streamvideothumburl />
			<streamvideoaduxinfo />
			<streamvideopublishid />
		</streamvideo>
		<canvasPageItem>
			<canvasPageXml><![CDATA[]]></canvasPageXml>
		</canvasPageItem>
		<appattach>
			<totallen>0</totallen>
			<attachid />
			<cdnattachurl />
			<emoticonmd5 />
			<aeskey />
			<fileext />
			<islargefilemsg>0</islargefilemsg>
		</appattach>
		<extinfo />
		<androidsource>2</androidsource>
		<thumburl />
		<mediatagname />
		<messageaction><![CDATA[]]></messageaction>
		<messageext><![CDATA[]]></messageext>
		<emoticongift>
			<packageflag>0</packageflag>
			<packageid />
		</emoticongift>
		<emoticonshared>
			<packageflag>0</packageflag>
			<packageid />
		</emoticonshared>
		<designershared>
			<designeruin>0</designeruin>
			<designername>null</designername>
			<designerrediretcturl><![CDATA[null]]></designerrediretcturl>
		</designershared>
		<emotionpageshared>
			<tid>0</tid>
			<title>null</title>
			<desc>null</desc>
			<iconUrl><![CDATA[null]]></iconUrl>
			<secondUrl>null</secondUrl>
			<pageType>0</pageType>
			<setKey>null</setKey>
		</emotionpageshared>
		<webviewshared>
			<shareUrlOriginal />
			<shareUrlOpen />
			<jsAppId />
			<publisherId />
			<publisherReqId />
		</webviewshared>
		<template_id />
		<md5 />
		<websearch>
			<rec_category>0</rec_category>
			<channelId>0</channelId>
		</websearch>
		<weappinfo>
			<username />
			<appid />
			<appservicetype>0</appservicetype>
			<secflagforsinglepagemode>0</secflagforsinglepagemode>
			<videopageinfo>
				<thumbwidth>0</thumbwidth>
				<thumbheight>0</thumbheight>
				<fromopensdk>0</fromopensdk>
			</videopageinfo>
		</weappinfo>
		<statextstr>GhQKEnd4NDg1YTk3Yzg0NDA4NmRjOQ==</statextstr>
		<songalbumurl>https://wx.qlogo.cn/mmhead/ver_1/iaZCKGjtAic5icOnI1ibaY7DCzx0SVR3ic6NXwcAHpFTZIzDcvzMaeNlEYqezUL04icbD6ZylLwcGMYIzQy6kjY4AdicRyb39fASpxswdSeDrs5tomibd2sADOgpbRIF6jic2v1ibFafdgicYm69maZehIgNhg8Tw/0</songalbumurl>
		<songlyric>[00:22.31]这些年一个人
[00:24.43]在外飘呀飘
[00:26.33]三十了好几呀
[00:28.76]还没有成家
[00:31.03]赚的钱还不够
[00:33.44]一个人花
[00:35.16]不知何时才能把财发
[00:39.46]亲爱的那个她
[00:41.59]生个胖娃娃
[00:43.71]上有老下有小
[00:45.93]更不容易呀
[00:48.17]我也要努力的
[00:50.14]去挣钱啊
[00:52.44]这个社会压力山大
[00:56.43]等我有了钱有钱随便花
[01:00.83]想买啥来就买啥
[01:05.18]抽烟我只抽大中华
[01:09.43]开车就要开宝马
[01:14.05]等我有了钱有钱随便花
[01:17.94]想吃啥来就吃啥
[01:22.24]人生就是这么回事啊
[01:26.63]活就活得潇潇洒洒
[01:48.18]亲爱的那个她
[01:50.15]生个胖娃娃
[01:52.27]上有老下有小
[01:54.50]更不容易呀
[01:56.62]我也要努力的
[01:58.74]去挣钱啊
[02:00.87]这个社会压力山大
[02:05.17]等我有了钱有钱随便花
[02:09.42]想买啥来就买啥
[02:13.67]抽烟我只抽大中华
[02:18.03]开车就要开宝马
[02:22.33]等我有了钱有钱随便花
[02:26.54]想吃啥来就吃啥
[02:30.89]人生就是这么回事啊
[02:35.09]活就活得潇潇洒洒
[02:39.59]等我有了钱有钱随便花
[02:43.64]想买啥来就买啥
[02:47.99]抽烟我只抽大中华
[02:52.25]开车就要开宝马
[02:56.56]等我有了钱有钱随便花
[03:00.82]想吃啥来就吃啥
[03:05.17]人生就是这么回事啊
[03:09.42]活就活得潇潇洒洒

</songlyric>
		<musicShareItem>
			<musicDuration>0</musicDuration>
		</musicShareItem>
		<finderLiveProductShare>
			<finderLiveID><![CDATA[]]></finderLiveID>
			<finderUsername><![CDATA[]]></finderUsername>
			<finderObjectID><![CDATA[]]></finderObjectID>
			<finderNonceID><![CDATA[]]></finderNonceID>
			<liveStatus><![CDATA[]]></liveStatus>
			<appId><![CDATA[]]></appId>
			<pagePath><![CDATA[]]></pagePath>
			<productId><![CDATA[]]></productId>
			<coverUrl><![CDATA[]]></coverUrl>
			<productTitle><![CDATA[]]></productTitle>
			<marketPrice><![CDATA[0]]></marketPrice>
			<sellingPrice><![CDATA[0]]></sellingPrice>
			<platformHeadImg><![CDATA[]]></platformHeadImg>
			<platformName><![CDATA[]]></platformName>
			<shopWindowId><![CDATA[]]></shopWindowId>
			<flashSalePrice><![CDATA[0]]></flashSalePrice>
			<flashSaleEndTime><![CDATA[0]]></flashSaleEndTime>
			<ecSource><![CDATA[]]></ecSource>
			<sellingPriceWording><![CDATA[]]></sellingPriceWording>
			<platformIconURL><![CDATA[]]></platformIconURL>
			<firstProductTagURL><![CDATA[]]></firstProductTagURL>
			<firstProductTagAspectRatioString><![CDATA[0.0]]></firstProductTagAspectRatioString>
			<secondProductTagURL><![CDATA[]]></secondProductTagURL>
			<secondProductTagAspectRatioString><![CDATA[0.0]]></secondProductTagAspectRatioString>
			<firstGuaranteeWording><![CDATA[]]></firstGuaranteeWording>
			<secondGuaranteeWording><![CDATA[]]></secondGuaranteeWording>
			<thirdGuaranteeWording><![CDATA[]]></thirdGuaranteeWording>
			<isPriceBeginShow>false</isPriceBeginShow>
			<lastGMsgID><![CDATA[]]></lastGMsgID>
			<promoterKey><![CDATA[]]></promoterKey>
			<discountWording><![CDATA[]]></discountWording>
			<priceSuffixDescription><![CDATA[]]></priceSuffixDescription>
			<showBoxItemStringList />
		</finderLiveProductShare>
		<finderOrder>
			<appID><![CDATA[]]></appID>
			<orderID><![CDATA[]]></orderID>
			<path><![CDATA[]]></path>
			<priceWording><![CDATA[]]></priceWording>
			<stateWording><![CDATA[]]></stateWording>
			<productImageURL><![CDATA[]]></productImageURL>
			<products><![CDATA[]]></products>
			<productsCount><![CDATA[0]]></productsCount>
		</finderOrder>
		<finderShopWindowShare>
			<finderUsername><![CDATA[]]></finderUsername>
			<avatar><![CDATA[]]></avatar>
			<nickname><![CDATA[]]></nickname>
			<commodityInStockCount><![CDATA[]]></commodityInStockCount>
			<appId><![CDATA[]]></appId>
			<path><![CDATA[]]></path>
			<appUsername><![CDATA[]]></appUsername>
			<query><![CDATA[]]></query>
			<liteAppId><![CDATA[]]></liteAppId>
			<liteAppPath><![CDATA[]]></liteAppPath>
			<liteAppQuery><![CDATA[]]></liteAppQuery>
			<platformTagURL><![CDATA[]]></platformTagURL>
			<saleWording><![CDATA[]]></saleWording>
			<lastGMsgID><![CDATA[]]></lastGMsgID>
			<profileTypeWording><![CDATA[]]></profileTypeWording>
			<reputationInfo>
				<hasReputationInfo>0</hasReputationInfo>
				<reputationScore>0</reputationScore>
				<reputationWording />
				<reputationTextColor />
				<reputationLevelWording />
				<reputationBackgroundColor />
			</reputationInfo>
			<productImageURLList />
		</finderShopWindowShare>
		<findernamecard>
			<username />
			<avatar><![CDATA[]]></avatar>
			<nickname />
			<auth_job />
			<auth_icon>0</auth_icon>
			<auth_icon_url />
			<ecSource><![CDATA[]]></ecSource>
			<lastGMsgID><![CDATA[]]></lastGMsgID>
		</findernamecard>
		<finderGuarantee>
			<scene><![CDATA[0]]></scene>
		</finderGuarantee>
		<directshare>0</directshare>
		<gamecenter>
			<namecard>
				<iconUrl />
				<name />
				<desc />
				<tail />
				<jumpUrl />
			</namecard>
		</gamecenter>
		<patMsg>
			<chatUser />
			<records>
				<recordNum>0</recordNum>
			</records>
		</patMsg>
		<secretmsg>
			<issecretmsg>0</issecretmsg>
		</secretmsg>
		<referfromscene>0</referfromscene>
		<gameshare>
			<liteappext>
				<liteappbizdata />
				<priority>0</priority>
			</liteappext>
			<appbrandext>
				<litegameinfo />
				<priority>-1</priority>
			</appbrandext>
			<gameshareid />
			<sharedata />
			<isvideo>0</isvideo>
			<duration>-1</duration>
			<isexposed>0</isexposed>
			<readtext />
		</gameshare>
		<mpsharetrace>
			<hasfinderelement>0</hasfinderelement>
			<lastgmsgid />
		</mpsharetrace>
		<wxgamecard>
			<framesetname />
			<mbcarddata />
			<minpkgversion />
			<mbcardheight>0</mbcardheight>
			<isoldversion>0</isoldversion>
		</wxgamecard>
		<liteapp>
			<id>null</id>
			<path />
			<query />
		</liteapp>
	</appmsg>
	<fromusername>wxid_lneb7n23o4lg12</fromusername>
	<scene>0</scene>
	<appinfo>
		<version>29</version>
		<appname>摇一摇搜歌</appname>
	</appinfo>
	<commenturl></commenturl>
</msg>

2025-07-22 18:42:24 | DEBUG | XML消息类型: 3
2025-07-22 18:42:24 | DEBUG | XML消息标题: 有钱随便花-DJ完整版-江哥
2025-07-22 18:42:24 | DEBUG | XML消息描述: 无妄-抖音
2025-07-22 18:42:24 | DEBUG | 附件信息 totallen: 0
2025-07-22 18:42:24 | DEBUG | 附件信息 islargefilemsg: 0
2025-07-22 18:42:24 | INFO | 收到红包消息: 标题:有钱随便花-DJ完整版-江哥 描述:无妄-抖音 来自:48097389945@chatroom 发送人:wxid_4usgcju5ey9q29
2025-07-22 18:42:24 | DEBUG | 收到消息: {'MsgId': 935670059, 'FromUserName': {'string': '48097389945@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 49, 'Content': {'string': 'wxid_lneb7n23o4lg12:\n<?xml version="1.0"?>\n<msg>\n\t<appmsg appid="wx485a97c844086dc9" sdkver="0">\n\t\t<title>有钱就花</title>\n\t\t<des>锦岚（开玩笑就退群版）</des>\n\t\t<username />\n\t\t<action>view</action>\n\t\t<type>3</type>\n\t\t<showtype>0</showtype>\n\t\t<content />\n\t\t<url />\n\t\t<lowurl />\n\t\t<forwardflag>0</forwardflag>\n\t\t<dataurl>https://v3-luna.douyinvod.com/3b1888e501de4916c93d9e131c7a297f/6880cb1a/video/tos/cn/tos-cn-ve-2774/oABIDMC8gCuGAUfVZFf8goDB6Baa4EtC7x14Qq/</dataurl>\n\t\t<lowdataurl />\n\t\t<contentattr>0</contentattr>\n\t\t<streamvideo>\n\t\t\t<streamvideourl />\n\t\t\t<streamvideototaltime>0</streamvideototaltime>\n\t\t\t<streamvideotitle />\n\t\t\t<streamvideowording />\n\t\t\t<streamvideoweburl />\n\t\t\t<streamvideothumburl />\n\t\t\t<streamvideoaduxinfo />\n\t\t\t<streamvideopublishid />\n\t\t</streamvideo>\n\t\t<canvasPageItem>\n\t\t\t<canvasPageXml><![CDATA[]]></canvasPageXml>\n\t\t</canvasPageItem>\n\t\t<appattach>\n\t\t\t<totallen>0</totallen>\n\t\t\t<attachid />\n\t\t\t<cdnattachurl />\n\t\t\t<emoticonmd5 />\n\t\t\t<aeskey />\n\t\t\t<fileext />\n\t\t\t<islargefilemsg>0</islargefilemsg>\n\t\t</appattach>\n\t\t<extinfo />\n\t\t<androidsource>2</androidsource>\n\t\t<thumburl />\n\t\t<mediatagname />\n\t\t<messageaction><![CDATA[]]></messageaction>\n\t\t<messageext><![CDATA[]]></messageext>\n\t\t<emoticongift>\n\t\t\t<packageflag>0</packageflag>\n\t\t\t<packageid />\n\t\t</emoticongift>\n\t\t<emoticonshared>\n\t\t\t<packageflag>0</packageflag>\n\t\t\t<packageid />\n\t\t</emoticonshared>\n\t\t<designershared>\n\t\t\t<designeruin>0</designeruin>\n\t\t\t<designername>null</designername>\n\t\t\t<designerrediretcturl><![CDATA[null]]></designerrediretcturl>\n\t\t</designershared>\n\t\t<emotionpageshared>\n\t\t\t<tid>0</tid>\n\t\t\t<title>null</title>\n\t\t\t<desc>null</desc>\n\t\t\t<iconUrl><![CDATA[null]]></iconUrl>\n\t\t\t<secondUrl>null</secondUrl>\n\t\t\t<pageType>0</pageType>\n\t\t\t<setKey>null</setKey>\n\t\t</emotionpageshared>\n\t\t<webviewshared>\n\t\t\t<shareUrlOriginal />\n\t\t\t<shareUrlOpen />\n\t\t\t<jsAppId />\n\t\t\t<publisherId />\n\t\t\t<publisherReqId />\n\t\t</webviewshared>\n\t\t<template_id />\n\t\t<md5 />\n\t\t<websearch>\n\t\t\t<rec_category>0</rec_category>\n\t\t\t<channelId>0</channelId>\n\t\t</websearch>\n\t\t<weappinfo>\n\t\t\t<username />\n\t\t\t<appid />\n\t\t\t<appservicetype>0</appservicetype>\n\t\t\t<secflagforsinglepagemode>0</secflagforsinglepagemode>\n\t\t\t<videopageinfo>\n\t\t\t\t<thumbwidth>0</thumbwidth>\n\t\t\t\t<thumbheight>0</thumbheight>\n\t\t\t\t<fromopensdk>0</fromopensdk>\n\t\t\t</videopageinfo>\n\t\t</weappinfo>\n\t\t<statextstr>GhQKEnd4NDg1YTk3Yzg0NDA4NmRjOQ==</statextstr>\n\t\t<songalbumurl>https://wx.qlogo.cn/mmhead/ver_1/iaZCKGjtAic5icOnI1ibaY7DCzx0SVR3ic6NXwcAHpFTZIzDcvzMaeNlEYqezUL04icbD6ZylLwcGMYIzQy6kjY4AdicRyb39fASpxswdSeDrs5tomibd2sADOgpbRIF6jic2v1ibFafdgicYm69maZehIgNhg8Tw/0</songalbumurl>\n\t\t<songlyric>[99:99.99]小爱提醒您该歌曲暂无歌词！</songlyric>\n\t\t<musicShareItem>\n\t\t\t<musicDuration>0</musicDuration>\n\t\t</musicShareItem>\n\t\t<finderLiveProductShare>\n\t\t\t<finderLiveID><![CDATA[]]></finderLiveID>\n\t\t\t<finderUsername><![CDATA[]]></finderUsername>\n\t\t\t<finderObjectID><![CDATA[]]></finderObjectID>\n\t\t\t<finderNonceID><![CDATA[]]></finderNonceID>\n\t\t\t<liveStatus><![CDATA[]]></liveStatus>\n\t\t\t<appId><![CDATA[]]></appId>\n\t\t\t<pagePath><![CDATA[]]></pagePath>\n\t\t\t<productId><![CDATA[]]></productId>\n\t\t\t<coverUrl><![CDATA[]]></coverUrl>\n\t\t\t<productTitle><![CDATA[]]></productTitle>\n\t\t\t<marketPrice><![CDATA[0]]></marketPrice>\n\t\t\t<sellingPrice><![CDATA[0]]></sellingPrice>\n\t\t\t<platformHeadImg><![CDATA[]]></platformHeadImg>\n\t\t\t<platformName><![CDATA[]]></platformName>\n\t\t\t<shopWindowId><![CDATA[]]></shopWindowId>\n\t\t\t<flashSalePrice><![CDATA[0]]></flashSalePrice>\n\t\t\t<flashSaleEndTime><![CDATA[0]]></flashSaleEndTime>\n\t\t\t<ecSource><![CDATA[]]></ecSource>\n\t\t\t<sellingPriceWording><![CDATA[]]></sellingPriceWording>\n\t\t\t<platformIconURL><![CDATA[]]></platformIconURL>\n\t\t\t<firstProductTagURL><![CDATA[]]></firstProductTagURL>\n\t\t\t<firstProductTagAspectRatioString><![CDATA[0.0]]></firstProductTagAspectRatioString>\n\t\t\t<secondProductTagURL><![CDATA[]]></secondProductTagURL>\n\t\t\t<secondProductTagAspectRatioString><![CDATA[0.0]]></secondProductTagAspectRatioString>\n\t\t\t<firstGuaranteeWording><![CDATA[]]></firstGuaranteeWording>\n\t\t\t<secondGuaranteeWording><![CDATA[]]></secondGuaranteeWording>\n\t\t\t<thirdGuaranteeWording><![CDATA[]]></thirdGuaranteeWording>\n\t\t\t<isPriceBeginShow>false</isPriceBeginShow>\n\t\t\t<lastGMsgID><![CDATA[]]></lastGMsgID>\n\t\t\t<promoterKey><![CDATA[]]></promoterKey>\n\t\t\t<discountWording><![CDATA[]]></discountWording>\n\t\t\t<priceSuffixDescription><![CDATA[]]></priceSuffixDescription>\n\t\t\t<showBoxItemStringList />\n\t\t</finderLiveProductShare>\n\t\t<finderOrder>\n\t\t\t<appID><![CDATA[]]></appID>\n\t\t\t<orderID><![CDATA[]]></orderID>\n\t\t\t<path><![CDATA[]]></path>\n\t\t\t<priceWording><![CDATA[]]></priceWording>\n\t\t\t<stateWording><![CDATA[]]></stateWording>\n\t\t\t<productImageURL><![CDATA[]]></productImageURL>\n\t\t\t<products><![CDATA[]]></products>\n\t\t\t<productsCount><![CDATA[0]]></productsCount>\n\t\t</finderOrder>\n\t\t<finderShopWindowShare>\n\t\t\t<finderUsername><![CDATA[]]></finderUsername>\n\t\t\t<avatar><![CDATA[]]></avatar>\n\t\t\t<nickname><![CDATA[]]></nickname>\n\t\t\t<commodityInStockCount><![CDATA[]]></commodityInStockCount>\n\t\t\t<appId><![CDATA[]]></appId>\n\t\t\t<path><![CDATA[]]></path>\n\t\t\t<appUsername><![CDATA[]]></appUsername>\n\t\t\t<query><![CDATA[]]></query>\n\t\t\t<liteAppId><![CDATA[]]></liteAppId>\n\t\t\t<liteAppPath><![CDATA[]]></liteAppPath>\n\t\t\t<liteAppQuery><![CDATA[]]></liteAppQuery>\n\t\t\t<platformTagURL><![CDATA[]]></platformTagURL>\n\t\t\t<saleWording><![CDATA[]]></saleWording>\n\t\t\t<lastGMsgID><![CDATA[]]></lastGMsgID>\n\t\t\t<profileTypeWording><![CDATA[]]></profileTypeWording>\n\t\t\t<reputationInfo>\n\t\t\t\t<hasReputationInfo>0</hasReputationInfo>\n\t\t\t\t<reputationScore>0</reputationScore>\n\t\t\t\t<reputationWording />\n\t\t\t\t<reputationTextColor />\n\t\t\t\t<reputationLevelWording />\n\t\t\t\t<reputationBackgroundColor />\n\t\t\t</reputationInfo>\n\t\t\t<productImageURLList />\n\t\t</finderShopWindowShare>\n\t\t<findernamecard>\n\t\t\t<username />\n\t\t\t<avatar><![CDATA[]]></avatar>\n\t\t\t<nickname />\n\t\t\t<auth_job />\n\t\t\t<auth_icon>0</auth_icon>\n\t\t\t<auth_icon_url />\n\t\t\t<ecSource><![CDATA[]]></ecSource>\n\t\t\t<lastGMsgID><![CDATA[]]></lastGMsgID>\n\t\t</findernamecard>\n\t\t<finderGuarantee>\n\t\t\t<scene><![CDATA[0]]></scene>\n\t\t</finderGuarantee>\n\t\t<directshare>0</directshare>\n\t\t<gamecenter>\n\t\t\t<namecard>\n\t\t\t\t<iconUrl />\n\t\t\t\t<name />\n\t\t\t\t<desc />\n\t\t\t\t<tail />\n\t\t\t\t<jumpUrl />\n\t\t\t</namecard>\n\t\t</gamecenter>\n\t\t<patMsg>\n\t\t\t<chatUser />\n\t\t\t<records>\n\t\t\t\t<recordNum>0</recordNum>\n\t\t\t</records>\n\t\t</patMsg>\n\t\t<secretmsg>\n\t\t\t<issecretmsg>0</issecretmsg>\n\t\t</secretmsg>\n\t\t<referfromscene>0</referfromscene>\n\t\t<gameshare>\n\t\t\t<liteappext>\n\t\t\t\t<liteappbizdata />\n\t\t\t\t<priority>0</priority>\n\t\t\t</liteappext>\n\t\t\t<appbrandext>\n\t\t\t\t<litegameinfo />\n\t\t\t\t<priority>-1</priority>\n\t\t\t</appbrandext>\n\t\t\t<gameshareid />\n\t\t\t<sharedata />\n\t\t\t<isvideo>0</isvideo>\n\t\t\t<duration>-1</duration>\n\t\t\t<isexposed>0</isexposed>\n\t\t\t<readtext />\n\t\t</gameshare>\n\t\t<mpsharetrace>\n\t\t\t<hasfinderelement>0</hasfinderelement>\n\t\t\t<lastgmsgid />\n\t\t</mpsharetrace>\n\t\t<wxgamecard>\n\t\t\t<framesetname />\n\t\t\t<mbcarddata />\n\t\t\t<minpkgversion />\n\t\t\t<mbcardheight>0</mbcardheight>\n\t\t\t<isoldversion>0</isoldversion>\n\t\t</wxgamecard>\n\t\t<liteapp>\n\t\t\t<id>null</id>\n\t\t\t<path />\n\t\t\t<query />\n\t\t</liteapp>\n\t</appmsg>\n\t<fromusername>wxid_lneb7n23o4lg12</fromusername>\n\t<scene>0</scene>\n\t<appinfo>\n\t\t<version>29</version>\n\t\t<appname>摇一摇搜歌</appname>\n\t</appinfo>\n\t<commenturl></commenturl>\n</msg>\n'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1753180952, 'MsgSource': '<msgsource>\n\t<sec_msg_node>\n\t\t<uuid>b73344beb91c7e30c3333b3dff5d161d_</uuid>\n\t\t<risk-file-flag />\n\t\t<risk-file-md5-list />\n\t</sec_msg_node>\n\t<silence>0</silence>\n\t<membercount>62</membercount>\n\t<signature>N0_V1_2RPGJ/Df|v1_IGDo9oyS</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'PushContent': '你收到了一条消息', 'NewMsgId': 3684022082457352698, 'MsgSeq': 871392694}
2025-07-22 18:42:24 | DEBUG | 从群聊消息中提取发送者: wxid_lneb7n23o4lg12
2025-07-22 18:42:24 | DEBUG | XML消息完整内容:
<?xml version="1.0"?>
<msg>
	<appmsg appid="wx485a97c844086dc9" sdkver="0">
		<title>有钱就花</title>
		<des>锦岚（开玩笑就退群版）</des>
		<username />
		<action>view</action>
		<type>3</type>
		<showtype>0</showtype>
		<content />
		<url />
		<lowurl />
		<forwardflag>0</forwardflag>
		<dataurl>https://v3-luna.douyinvod.com/3b1888e501de4916c93d9e131c7a297f/6880cb1a/video/tos/cn/tos-cn-ve-2774/oABIDMC8gCuGAUfVZFf8goDB6Baa4EtC7x14Qq/</dataurl>
		<lowdataurl />
		<contentattr>0</contentattr>
		<streamvideo>
			<streamvideourl />
			<streamvideototaltime>0</streamvideototaltime>
			<streamvideotitle />
			<streamvideowording />
			<streamvideoweburl />
			<streamvideothumburl />
			<streamvideoaduxinfo />
			<streamvideopublishid />
		</streamvideo>
		<canvasPageItem>
			<canvasPageXml><![CDATA[]]></canvasPageXml>
		</canvasPageItem>
		<appattach>
			<totallen>0</totallen>
			<attachid />
			<cdnattachurl />
			<emoticonmd5 />
			<aeskey />
			<fileext />
			<islargefilemsg>0</islargefilemsg>
		</appattach>
		<extinfo />
		<androidsource>2</androidsource>
		<thumburl />
		<mediatagname />
		<messageaction><![CDATA[]]></messageaction>
		<messageext><![CDATA[]]></messageext>
		<emoticongift>
			<packageflag>0</packageflag>
			<packageid />
		</emoticongift>
		<emoticonshared>
			<packageflag>0</packageflag>
			<packageid />
		</emoticonshared>
		<designershared>
			<designeruin>0</designeruin>
			<designername>null</designername>
			<designerrediretcturl><![CDATA[null]]></designerrediretcturl>
		</designershared>
		<emotionpageshared>
			<tid>0</tid>
			<title>null</title>
			<desc>null</desc>
			<iconUrl><![CDATA[null]]></iconUrl>
			<secondUrl>null</secondUrl>
			<pageType>0</pageType>
			<setKey>null</setKey>
		</emotionpageshared>
		<webviewshared>
			<shareUrlOriginal />
			<shareUrlOpen />
			<jsAppId />
			<publisherId />
			<publisherReqId />
		</webviewshared>
		<template_id />
		<md5 />
		<websearch>
			<rec_category>0</rec_category>
			<channelId>0</channelId>
		</websearch>
		<weappinfo>
			<username />
			<appid />
			<appservicetype>0</appservicetype>
			<secflagforsinglepagemode>0</secflagforsinglepagemode>
			<videopageinfo>
				<thumbwidth>0</thumbwidth>
				<thumbheight>0</thumbheight>
				<fromopensdk>0</fromopensdk>
			</videopageinfo>
		</weappinfo>
		<statextstr>GhQKEnd4NDg1YTk3Yzg0NDA4NmRjOQ==</statextstr>
		<songalbumurl>https://wx.qlogo.cn/mmhead/ver_1/iaZCKGjtAic5icOnI1ibaY7DCzx0SVR3ic6NXwcAHpFTZIzDcvzMaeNlEYqezUL04icbD6ZylLwcGMYIzQy6kjY4AdicRyb39fASpxswdSeDrs5tomibd2sADOgpbRIF6jic2v1ibFafdgicYm69maZehIgNhg8Tw/0</songalbumurl>
		<songlyric>[99:99.99]小爱提醒您该歌曲暂无歌词！</songlyric>
		<musicShareItem>
			<musicDuration>0</musicDuration>
		</musicShareItem>
		<finderLiveProductShare>
			<finderLiveID><![CDATA[]]></finderLiveID>
			<finderUsername><![CDATA[]]></finderUsername>
			<finderObjectID><![CDATA[]]></finderObjectID>
			<finderNonceID><![CDATA[]]></finderNonceID>
			<liveStatus><![CDATA[]]></liveStatus>
			<appId><![CDATA[]]></appId>
			<pagePath><![CDATA[]]></pagePath>
			<productId><![CDATA[]]></productId>
			<coverUrl><![CDATA[]]></coverUrl>
			<productTitle><![CDATA[]]></productTitle>
			<marketPrice><![CDATA[0]]></marketPrice>
			<sellingPrice><![CDATA[0]]></sellingPrice>
			<platformHeadImg><![CDATA[]]></platformHeadImg>
			<platformName><![CDATA[]]></platformName>
			<shopWindowId><![CDATA[]]></shopWindowId>
			<flashSalePrice><![CDATA[0]]></flashSalePrice>
			<flashSaleEndTime><![CDATA[0]]></flashSaleEndTime>
			<ecSource><![CDATA[]]></ecSource>
			<sellingPriceWording><![CDATA[]]></sellingPriceWording>
			<platformIconURL><![CDATA[]]></platformIconURL>
			<firstProductTagURL><![CDATA[]]></firstProductTagURL>
			<firstProductTagAspectRatioString><![CDATA[0.0]]></firstProductTagAspectRatioString>
			<secondProductTagURL><![CDATA[]]></secondProductTagURL>
			<secondProductTagAspectRatioString><![CDATA[0.0]]></secondProductTagAspectRatioString>
			<firstGuaranteeWording><![CDATA[]]></firstGuaranteeWording>
			<secondGuaranteeWording><![CDATA[]]></secondGuaranteeWording>
			<thirdGuaranteeWording><![CDATA[]]></thirdGuaranteeWording>
			<isPriceBeginShow>false</isPriceBeginShow>
			<lastGMsgID><![CDATA[]]></lastGMsgID>
			<promoterKey><![CDATA[]]></promoterKey>
			<discountWording><![CDATA[]]></discountWording>
			<priceSuffixDescription><![CDATA[]]></priceSuffixDescription>
			<showBoxItemStringList />
		</finderLiveProductShare>
		<finderOrder>
			<appID><![CDATA[]]></appID>
			<orderID><![CDATA[]]></orderID>
			<path><![CDATA[]]></path>
			<priceWording><![CDATA[]]></priceWording>
			<stateWording><![CDATA[]]></stateWording>
			<productImageURL><![CDATA[]]></productImageURL>
			<products><![CDATA[]]></products>
			<productsCount><![CDATA[0]]></productsCount>
		</finderOrder>
		<finderShopWindowShare>
			<finderUsername><![CDATA[]]></finderUsername>
			<avatar><![CDATA[]]></avatar>
			<nickname><![CDATA[]]></nickname>
			<commodityInStockCount><![CDATA[]]></commodityInStockCount>
			<appId><![CDATA[]]></appId>
			<path><![CDATA[]]></path>
			<appUsername><![CDATA[]]></appUsername>
			<query><![CDATA[]]></query>
			<liteAppId><![CDATA[]]></liteAppId>
			<liteAppPath><![CDATA[]]></liteAppPath>
			<liteAppQuery><![CDATA[]]></liteAppQuery>
			<platformTagURL><![CDATA[]]></platformTagURL>
			<saleWording><![CDATA[]]></saleWording>
			<lastGMsgID><![CDATA[]]></lastGMsgID>
			<profileTypeWording><![CDATA[]]></profileTypeWording>
			<reputationInfo>
				<hasReputationInfo>0</hasReputationInfo>
				<reputationScore>0</reputationScore>
				<reputationWording />
				<reputationTextColor />
				<reputationLevelWording />
				<reputationBackgroundColor />
			</reputationInfo>
			<productImageURLList />
		</finderShopWindowShare>
		<findernamecard>
			<username />
			<avatar><![CDATA[]]></avatar>
			<nickname />
			<auth_job />
			<auth_icon>0</auth_icon>
			<auth_icon_url />
			<ecSource><![CDATA[]]></ecSource>
			<lastGMsgID><![CDATA[]]></lastGMsgID>
		</findernamecard>
		<finderGuarantee>
			<scene><![CDATA[0]]></scene>
		</finderGuarantee>
		<directshare>0</directshare>
		<gamecenter>
			<namecard>
				<iconUrl />
				<name />
				<desc />
				<tail />
				<jumpUrl />
			</namecard>
		</gamecenter>
		<patMsg>
			<chatUser />
			<records>
				<recordNum>0</recordNum>
			</records>
		</patMsg>
		<secretmsg>
			<issecretmsg>0</issecretmsg>
		</secretmsg>
		<referfromscene>0</referfromscene>
		<gameshare>
			<liteappext>
				<liteappbizdata />
				<priority>0</priority>
			</liteappext>
			<appbrandext>
				<litegameinfo />
				<priority>-1</priority>
			</appbrandext>
			<gameshareid />
			<sharedata />
			<isvideo>0</isvideo>
			<duration>-1</duration>
			<isexposed>0</isexposed>
			<readtext />
		</gameshare>
		<mpsharetrace>
			<hasfinderelement>0</hasfinderelement>
			<lastgmsgid />
		</mpsharetrace>
		<wxgamecard>
			<framesetname />
			<mbcarddata />
			<minpkgversion />
			<mbcardheight>0</mbcardheight>
			<isoldversion>0</isoldversion>
		</wxgamecard>
		<liteapp>
			<id>null</id>
			<path />
			<query />
		</liteapp>
	</appmsg>
	<fromusername>wxid_lneb7n23o4lg12</fromusername>
	<scene>0</scene>
	<appinfo>
		<version>29</version>
		<appname>摇一摇搜歌</appname>
	</appinfo>
	<commenturl></commenturl>
</msg>

2025-07-22 18:42:24 | DEBUG | XML消息类型: 3
2025-07-22 18:42:24 | DEBUG | XML消息标题: 有钱就花
2025-07-22 18:42:24 | DEBUG | XML消息描述: 锦岚（开玩笑就退群版）
2025-07-22 18:42:24 | DEBUG | 附件信息 totallen: 0
2025-07-22 18:42:24 | DEBUG | 附件信息 islargefilemsg: 0
2025-07-22 18:42:24 | INFO | 收到红包消息: 标题:有钱就花 描述:锦岚（开玩笑就退群版） 来自:48097389945@chatroom 发送人:wxid_4usgcju5ey9q29
2025-07-22 18:42:25 | DEBUG | 收到消息: {'MsgId': 2114511485, 'FromUserName': {'string': '48097389945@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 49, 'Content': {'string': 'wxid_lneb7n23o4lg12:\n<?xml version="1.0"?>\n<msg>\n\t<appmsg appid="wx485a97c844086dc9" sdkver="0">\n\t\t<title>有钱就花-媛小小</title>\n\t\t<des>无妄-酷狗</des>\n\t\t<username />\n\t\t<action>view</action>\n\t\t<type>3</type>\n\t\t<showtype>0</showtype>\n\t\t<content />\n\t\t<url />\n\t\t<lowurl />\n\t\t<forwardflag>0</forwardflag>\n\t\t<dataurl>https://er-sycdn.kuwo.cn/2ad617165eca9de757a6c7b34fc75553/687f6b19/resource/30106/trackmedia/M800000IOozq3dC6ar.mp3?from=longzhu_api?from=longzhu_api</dataurl>\n\t\t<lowdataurl />\n\t\t<contentattr>0</contentattr>\n\t\t<streamvideo>\n\t\t\t<streamvideourl />\n\t\t\t<streamvideototaltime>0</streamvideototaltime>\n\t\t\t<streamvideotitle />\n\t\t\t<streamvideowording />\n\t\t\t<streamvideoweburl />\n\t\t\t<streamvideothumburl />\n\t\t\t<streamvideoaduxinfo />\n\t\t\t<streamvideopublishid />\n\t\t</streamvideo>\n\t\t<canvasPageItem>\n\t\t\t<canvasPageXml><![CDATA[]]></canvasPageXml>\n\t\t</canvasPageItem>\n\t\t<appattach>\n\t\t\t<totallen>0</totallen>\n\t\t\t<attachid />\n\t\t\t<cdnattachurl />\n\t\t\t<emoticonmd5 />\n\t\t\t<aeskey />\n\t\t\t<fileext />\n\t\t\t<islargefilemsg>0</islargefilemsg>\n\t\t</appattach>\n\t\t<extinfo />\n\t\t<androidsource>2</androidsource>\n\t\t<thumburl />\n\t\t<mediatagname />\n\t\t<messageaction><![CDATA[]]></messageaction>\n\t\t<messageext><![CDATA[]]></messageext>\n\t\t<emoticongift>\n\t\t\t<packageflag>0</packageflag>\n\t\t\t<packageid />\n\t\t</emoticongift>\n\t\t<emoticonshared>\n\t\t\t<packageflag>0</packageflag>\n\t\t\t<packageid />\n\t\t</emoticonshared>\n\t\t<designershared>\n\t\t\t<designeruin>0</designeruin>\n\t\t\t<designername>null</designername>\n\t\t\t<designerrediretcturl><![CDATA[null]]></designerrediretcturl>\n\t\t</designershared>\n\t\t<emotionpageshared>\n\t\t\t<tid>0</tid>\n\t\t\t<title>null</title>\n\t\t\t<desc>null</desc>\n\t\t\t<iconUrl><![CDATA[null]]></iconUrl>\n\t\t\t<secondUrl>null</secondUrl>\n\t\t\t<pageType>0</pageType>\n\t\t\t<setKey>null</setKey>\n\t\t</emotionpageshared>\n\t\t<webviewshared>\n\t\t\t<shareUrlOriginal />\n\t\t\t<shareUrlOpen />\n\t\t\t<jsAppId />\n\t\t\t<publisherId />\n\t\t\t<publisherReqId />\n\t\t</webviewshared>\n\t\t<template_id />\n\t\t<md5 />\n\t\t<websearch>\n\t\t\t<rec_category>0</rec_category>\n\t\t\t<channelId>0</channelId>\n\t\t</websearch>\n\t\t<weappinfo>\n\t\t\t<username />\n\t\t\t<appid />\n\t\t\t<appservicetype>0</appservicetype>\n\t\t\t<secflagforsinglepagemode>0</secflagforsinglepagemode>\n\t\t\t<videopageinfo>\n\t\t\t\t<thumbwidth>0</thumbwidth>\n\t\t\t\t<thumbheight>0</thumbheight>\n\t\t\t\t<fromopensdk>0</fromopensdk>\n\t\t\t</videopageinfo>\n\t\t</weappinfo>\n\t\t<statextstr>GhQKEnd4NDg1YTk3Yzg0NDA4NmRjOQ==</statextstr>\n\t\t<songalbumurl>http://imge.kugou.com/stdmusic/400/20250520/20250520174139458104.jpg</songalbumurl>\n\t\t<songlyric>[ti:]\r\n[ar:]\r\n[al:]\r\n[by:天琴实验室AI生成v1.0]\r\n[offset:0]\r\n[00:00.00]有钱就花-媛小小\r\n[00:01.15]词曲：康诚\r\n[00:02.31]编曲：王亚东\r\n[00:03.46]混音：王亚东\r\n[00:04.62]吉他：LeoD\r\n[00:05.77]和声：扁桃体不发言\r\n[00:06.93]制作人：康诚\r\n[00:08.08]OP：星汉马文化\r\n[00:09.24]生活就像一面镜子你笑它也笑\r\n[00:13.50]命运就像天来注定管它到不到\r\n[00:17.70]愁是一天乐是一天首先要吃饱\r\n[00:21.90]快活一晚忙是一晚一定要睡好\r\n[00:26.07]人生就像马拉松赛谁都要上道\r\n[00:30.21]我就乐意慢慢的走谁也管不着\r\n[00:34.41]人生就像愤怒小鸟失败猪会笑\r\n[00:38.55]我像一颗无名小草没山让我靠\r\n[00:42.48]咱有钱就花 反正下个月还发\r\n[00:46.89]存着不花人要走了是不是白搭\r\n[00:50.58]人要及时行乐还要活在当下\r\n[00:54.75]哎呀妈呀听的我现在就想出发\r\n[00:59.16]咱有钱就花 反正下个月还发\r\n[01:03.60]留着不花人还走了是不是白瞎\r\n[01:07.29]人生这一幅画可要尽情的耍\r\n[01:11.43]哎呀妈呀听的我好想去趟拉萨\r\n[01:32.82]人生就像马拉松赛谁都要上道\r\n[01:36.99]我就乐意慢慢的走谁也管不着\r\n[01:41.19]人生就像愤怒小鸟失败猪会笑\r\n[01:45.36]我像一颗无名小草没山让我靠\r\n[01:49.29]咱有钱就花 反正下个月还发\r\n[01:53.67]存着不花人要走了是不是白搭\r\n[01:57.39]人要及时行乐还要活在当下\r\n[02:01.53]哎呀妈呀听的我现在就想出发\r\n[02:05.94]咱有钱就花 反正下个月还发\r\n[02:10.38]留着不花人还走了是不是白瞎\r\n[02:14.07]人生这一幅画可要尽情的耍\r\n[02:18.24]哎呀妈呀听的我好想去趟拉萨\r\n[02:22.65]咱有钱就花 反正下个月还发\r\n[02:27.09]存着不花人要走了是不是白搭\r\n[02:30.78]人要及时行乐还要活在当下\r\n[02:34.89]哎呀妈呀听的我现在就想出发\r\n[02:39.36]咱有钱就花 反正下个月还发\r\n[02:43.77]留着不花人还走了是不是白瞎\r\n[02:47.46]人生这一幅画可要尽情的耍\r\n[02:51.63]哎呀妈呀听的我好想去趟拉萨\r\n</songlyric>\n\t\t<musicShareItem>\n\t\t\t<musicDuration>0</musicDuration>\n\t\t</musicShareItem>\n\t\t<finderLiveProductShare>\n\t\t\t<finderLiveID><![CDATA[]]></finderLiveID>\n\t\t\t<finderUsername><![CDATA[]]></finderUsername>\n\t\t\t<finderObjectID><![CDATA[]]></finderObjectID>\n\t\t\t<finderNonceID><![CDATA[]]></finderNonceID>\n\t\t\t<liveStatus><![CDATA[]]></liveStatus>\n\t\t\t<appId><![CDATA[]]></appId>\n\t\t\t<pagePath><![CDATA[]]></pagePath>\n\t\t\t<productId><![CDATA[]]></productId>\n\t\t\t<coverUrl><![CDATA[]]></coverUrl>\n\t\t\t<productTitle><![CDATA[]]></productTitle>\n\t\t\t<marketPrice><![CDATA[0]]></marketPrice>\n\t\t\t<sellingPrice><![CDATA[0]]></sellingPrice>\n\t\t\t<platformHeadImg><![CDATA[]]></platformHeadImg>\n\t\t\t<platformName><![CDATA[]]></platformName>\n\t\t\t<shopWindowId><![CDATA[]]></shopWindowId>\n\t\t\t<flashSalePrice><![CDATA[0]]></flashSalePrice>\n\t\t\t<flashSaleEndTime><![CDATA[0]]></flashSaleEndTime>\n\t\t\t<ecSource><![CDATA[]]></ecSource>\n\t\t\t<sellingPriceWording><![CDATA[]]></sellingPriceWording>\n\t\t\t<platformIconURL><![CDATA[]]></platformIconURL>\n\t\t\t<firstProductTagURL><![CDATA[]]></firstProductTagURL>\n\t\t\t<firstProductTagAspectRatioString><![CDATA[0.0]]></firstProductTagAspectRatioString>\n\t\t\t<secondProductTagURL><![CDATA[]]></secondProductTagURL>\n\t\t\t<secondProductTagAspectRatioString><![CDATA[0.0]]></secondProductTagAspectRatioString>\n\t\t\t<firstGuaranteeWording><![CDATA[]]></firstGuaranteeWording>\n\t\t\t<secondGuaranteeWording><![CDATA[]]></secondGuaranteeWording>\n\t\t\t<thirdGuaranteeWording><![CDATA[]]></thirdGuaranteeWording>\n\t\t\t<isPriceBeginShow>false</isPriceBeginShow>\n\t\t\t<lastGMsgID><![CDATA[]]></lastGMsgID>\n\t\t\t<promoterKey><![CDATA[]]></promoterKey>\n\t\t\t<discountWording><![CDATA[]]></discountWording>\n\t\t\t<priceSuffixDescription><![CDATA[]]></priceSuffixDescription>\n\t\t\t<showBoxItemStringList />\n\t\t</finderLiveProductShare>\n\t\t<finderOrder>\n\t\t\t<appID><![CDATA[]]></appID>\n\t\t\t<orderID><![CDATA[]]></orderID>\n\t\t\t<path><![CDATA[]]></path>\n\t\t\t<priceWording><![CDATA[]]></priceWording>\n\t\t\t<stateWording><![CDATA[]]></stateWording>\n\t\t\t<productImageURL><![CDATA[]]></productImageURL>\n\t\t\t<products><![CDATA[]]></products>\n\t\t\t<productsCount><![CDATA[0]]></productsCount>\n\t\t</finderOrder>\n\t\t<finderShopWindowShare>\n\t\t\t<finderUsername><![CDATA[]]></finderUsername>\n\t\t\t<avatar><![CDATA[]]></avatar>\n\t\t\t<nickname><![CDATA[]]></nickname>\n\t\t\t<commodityInStockCount><![CDATA[]]></commodityInStockCount>\n\t\t\t<appId><![CDATA[]]></appId>\n\t\t\t<path><![CDATA[]]></path>\n\t\t\t<appUsername><![CDATA[]]></appUsername>\n\t\t\t<query><![CDATA[]]></query>\n\t\t\t<liteAppId><![CDATA[]]></liteAppId>\n\t\t\t<liteAppPath><![CDATA[]]></liteAppPath>\n\t\t\t<liteAppQuery><![CDATA[]]></liteAppQuery>\n\t\t\t<platformTagURL><![CDATA[]]></platformTagURL>\n\t\t\t<saleWording><![CDATA[]]></saleWording>\n\t\t\t<lastGMsgID><![CDATA[]]></lastGMsgID>\n\t\t\t<profileTypeWording><![CDATA[]]></profileTypeWording>\n\t\t\t<reputationInfo>\n\t\t\t\t<hasReputationInfo>0</hasReputationInfo>\n\t\t\t\t<reputationScore>0</reputationScore>\n\t\t\t\t<reputationWording />\n\t\t\t\t<reputationTextColor />\n\t\t\t\t<reputationLevelWording />\n\t\t\t\t<reputationBackgroundColor />\n\t\t\t</reputationInfo>\n\t\t\t<productImageURLList />\n\t\t</finderShopWindowShare>\n\t\t<findernamecard>\n\t\t\t<username />\n\t\t\t<avatar><![CDATA[]]></avatar>\n\t\t\t<nickname />\n\t\t\t<auth_job />\n\t\t\t<auth_icon>0</auth_icon>\n\t\t\t<auth_icon_url />\n\t\t\t<ecSource><![CDATA[]]></ecSource>\n\t\t\t<lastGMsgID><![CDATA[]]></lastGMsgID>\n\t\t</findernamecard>\n\t\t<finderGuarantee>\n\t\t\t<scene><![CDATA[0]]></scene>\n\t\t</finderGuarantee>\n\t\t<directshare>0</directshare>\n\t\t<gamecenter>\n\t\t\t<namecard>\n\t\t\t\t<iconUrl />\n\t\t\t\t<name />\n\t\t\t\t<desc />\n\t\t\t\t<tail />\n\t\t\t\t<jumpUrl />\n\t\t\t</namecard>\n\t\t</gamecenter>\n\t\t<patMsg>\n\t\t\t<chatUser />\n\t\t\t<records>\n\t\t\t\t<recordNum>0</recordNum>\n\t\t\t</records>\n\t\t</patMsg>\n\t\t<secretmsg>\n\t\t\t<issecretmsg>0</issecretmsg>\n\t\t</secretmsg>\n\t\t<referfromscene>0</referfromscene>\n\t\t<gameshare>\n\t\t\t<liteappext>\n\t\t\t\t<liteappbizdata />\n\t\t\t\t<priority>0</priority>\n\t\t\t</liteappext>\n\t\t\t<appbrandext>\n\t\t\t\t<litegameinfo />\n\t\t\t\t<priority>-1</priority>\n\t\t\t</appbrandext>\n\t\t\t<gameshareid />\n\t\t\t<sharedata />\n\t\t\t<isvideo>0</isvideo>\n\t\t\t<duration>-1</duration>\n\t\t\t<isexposed>0</isexposed>\n\t\t\t<readtext />\n\t\t</gameshare>\n\t\t<mpsharetrace>\n\t\t\t<hasfinderelement>0</hasfinderelement>\n\t\t\t<lastgmsgid />\n\t\t</mpsharetrace>\n\t\t<wxgamecard>\n\t\t\t<framesetname />\n\t\t\t<mbcarddata />\n\t\t\t<minpkgversion />\n\t\t\t<mbcardheight>0</mbcardheight>\n\t\t\t<isoldversion>0</isoldversion>\n\t\t</wxgamecard>\n\t\t<liteapp>\n\t\t\t<id>null</id>\n\t\t\t<path />\n\t\t\t<query />\n\t\t</liteapp>\n\t</appmsg>\n\t<fromusername>wxid_lneb7n23o4lg12</fromusername>\n\t<scene>0</scene>\n\t<appinfo>\n\t\t<version>29</version>\n\t\t<appname>摇一摇搜歌</appname>\n\t</appinfo>\n\t<commenturl></commenturl>\n</msg>\n'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1753180953, 'MsgSource': '<msgsource>\n\t<sec_msg_node>\n\t\t<uuid>5c168adc2ff0a77f6ac95e9afbd02e26_</uuid>\n\t\t<risk-file-flag />\n\t\t<risk-file-md5-list />\n\t</sec_msg_node>\n\t<silence>0</silence>\n\t<membercount>62</membercount>\n\t<signature>N0_V1_CnPYMUfy|v1_/4w8BIyV</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'PushContent': '你收到了一条消息', 'NewMsgId': 6901690665260629209, 'MsgSeq': 871392695}
2025-07-22 18:42:25 | DEBUG | 从群聊消息中提取发送者: wxid_lneb7n23o4lg12
2025-07-22 18:42:25 | DEBUG | XML消息完整内容:
<?xml version="1.0"?>
<msg>
	<appmsg appid="wx485a97c844086dc9" sdkver="0">
		<title>有钱就花-媛小小</title>
		<des>无妄-酷狗</des>
		<username />
		<action>view</action>
		<type>3</type>
		<showtype>0</showtype>
		<content />
		<url />
		<lowurl />
		<forwardflag>0</forwardflag>
		<dataurl>https://er-sycdn.kuwo.cn/2ad617165eca9de757a6c7b34fc75553/687f6b19/resource/30106/trackmedia/M800000IOozq3dC6ar.mp3?from=longzhu_api?from=longzhu_api</dataurl>
		<lowdataurl />
		<contentattr>0</contentattr>
		<streamvideo>
			<streamvideourl />
			<streamvideototaltime>0</streamvideototaltime>
			<streamvideotitle />
			<streamvideowording />
			<streamvideoweburl />
			<streamvideothumburl />
			<streamvideoaduxinfo />
			<streamvideopublishid />
		</streamvideo>
		<canvasPageItem>
			<canvasPageXml><![CDATA[]]></canvasPageXml>
		</canvasPageItem>
		<appattach>
			<totallen>0</totallen>
			<attachid />
			<cdnattachurl />
			<emoticonmd5 />
			<aeskey />
			<fileext />
			<islargefilemsg>0</islargefilemsg>
		</appattach>
		<extinfo />
		<androidsource>2</androidsource>
		<thumburl />
		<mediatagname />
		<messageaction><![CDATA[]]></messageaction>
		<messageext><![CDATA[]]></messageext>
		<emoticongift>
			<packageflag>0</packageflag>
			<packageid />
		</emoticongift>
		<emoticonshared>
			<packageflag>0</packageflag>
			<packageid />
		</emoticonshared>
		<designershared>
			<designeruin>0</designeruin>
			<designername>null</designername>
			<designerrediretcturl><![CDATA[null]]></designerrediretcturl>
		</designershared>
		<emotionpageshared>
			<tid>0</tid>
			<title>null</title>
			<desc>null</desc>
			<iconUrl><![CDATA[null]]></iconUrl>
			<secondUrl>null</secondUrl>
			<pageType>0</pageType>
			<setKey>null</setKey>
		</emotionpageshared>
		<webviewshared>
			<shareUrlOriginal />
			<shareUrlOpen />
			<jsAppId />
			<publisherId />
			<publisherReqId />
		</webviewshared>
		<template_id />
		<md5 />
		<websearch>
			<rec_category>0</rec_category>
			<channelId>0</channelId>
		</websearch>
		<weappinfo>
			<username />
			<appid />
			<appservicetype>0</appservicetype>
			<secflagforsinglepagemode>0</secflagforsinglepagemode>
			<videopageinfo>
				<thumbwidth>0</thumbwidth>
				<thumbheight>0</thumbheight>
				<fromopensdk>0</fromopensdk>
			</videopageinfo>
		</weappinfo>
		<statextstr>GhQKEnd4NDg1YTk3Yzg0NDA4NmRjOQ==</statextstr>
		<songalbumurl>http://imge.kugou.com/stdmusic/400/20250520/20250520174139458104.jpg</songalbumurl>
		<songlyric>[ti:]

[ar:]

[al:]

[by:天琴实验室AI生成v1.0]

[offset:0]

[00:00.00]有钱就花-媛小小

[00:01.15]词曲：康诚

[00:02.31]编曲：王亚东

[00:03.46]混音：王亚东

[00:04.62]吉他：LeoD

[00:05.77]和声：扁桃体不发言

[00:06.93]制作人：康诚

[00:08.08]OP：星汉马文化

[00:09.24]生活就像一面镜子你笑它也笑

[00:13.50]命运就像天来注定管它到不到

[00:17.70]愁是一天乐是一天首先要吃饱

[00:21.90]快活一晚忙是一晚一定要睡好

[00:26.07]人生就像马拉松赛谁都要上道

[00:30.21]我就乐意慢慢的走谁也管不着

[00:34.41]人生就像愤怒小鸟失败猪会笑

[00:38.55]我像一颗无名小草没山让我靠

[00:42.48]咱有钱就花 反正下个月还发

[00:46.89]存着不花人要走了是不是白搭

[00:50.58]人要及时行乐还要活在当下

[00:54.75]哎呀妈呀听的我现在就想出发

[00:59.16]咱有钱就花 反正下个月还发

[01:03.60]留着不花人还走了是不是白瞎

[01:07.29]人生这一幅画可要尽情的耍

[01:11.43]哎呀妈呀听的我好想去趟拉萨

[01:32.82]人生就像马拉松赛谁都要上道

[01:36.99]我就乐意慢慢的走谁也管不着

[01:41.19]人生就像愤怒小鸟失败猪会笑

[01:45.36]我像一颗无名小草没山让我靠

[01:49.29]咱有钱就花 反正下个月还发

[01:53.67]存着不花人要走了是不是白搭

[01:57.39]人要及时行乐还要活在当下

[02:01.53]哎呀妈呀听的我现在就想出发

[02:05.94]咱有钱就花 反正下个月还发

[02:10.38]留着不花人还走了是不是白瞎

[02:14.07]人生这一幅画可要尽情的耍

[02:18.24]哎呀妈呀听的我好想去趟拉萨

[02:22.65]咱有钱就花 反正下个月还发

[02:27.09]存着不花人要走了是不是白搭

[02:30.78]人要及时行乐还要活在当下

[02:34.89]哎呀妈呀听的我现在就想出发

[02:39.36]咱有钱就花 反正下个月还发

[02:43.77]留着不花人还走了是不是白瞎

[02:47.46]人生这一幅画可要尽情的耍

[02:51.63]哎呀妈呀听的我好想去趟拉萨

</songlyric>
		<musicShareItem>
			<musicDuration>0</musicDuration>
		</musicShareItem>
		<finderLiveProductShare>
			<finderLiveID><![CDATA[]]></finderLiveID>
			<finderUsername><![CDATA[]]></finderUsername>
			<finderObjectID><![CDATA[]]></finderObjectID>
			<finderNonceID><![CDATA[]]></finderNonceID>
			<liveStatus><![CDATA[]]></liveStatus>
			<appId><![CDATA[]]></appId>
			<pagePath><![CDATA[]]></pagePath>
			<productId><![CDATA[]]></productId>
			<coverUrl><![CDATA[]]></coverUrl>
			<productTitle><![CDATA[]]></productTitle>
			<marketPrice><![CDATA[0]]></marketPrice>
			<sellingPrice><![CDATA[0]]></sellingPrice>
			<platformHeadImg><![CDATA[]]></platformHeadImg>
			<platformName><![CDATA[]]></platformName>
			<shopWindowId><![CDATA[]]></shopWindowId>
			<flashSalePrice><![CDATA[0]]></flashSalePrice>
			<flashSaleEndTime><![CDATA[0]]></flashSaleEndTime>
			<ecSource><![CDATA[]]></ecSource>
			<sellingPriceWording><![CDATA[]]></sellingPriceWording>
			<platformIconURL><![CDATA[]]></platformIconURL>
			<firstProductTagURL><![CDATA[]]></firstProductTagURL>
			<firstProductTagAspectRatioString><![CDATA[0.0]]></firstProductTagAspectRatioString>
			<secondProductTagURL><![CDATA[]]></secondProductTagURL>
			<secondProductTagAspectRatioString><![CDATA[0.0]]></secondProductTagAspectRatioString>
			<firstGuaranteeWording><![CDATA[]]></firstGuaranteeWording>
			<secondGuaranteeWording><![CDATA[]]></secondGuaranteeWording>
			<thirdGuaranteeWording><![CDATA[]]></thirdGuaranteeWording>
			<isPriceBeginShow>false</isPriceBeginShow>
			<lastGMsgID><![CDATA[]]></lastGMsgID>
			<promoterKey><![CDATA[]]></promoterKey>
			<discountWording><![CDATA[]]></discountWording>
			<priceSuffixDescription><![CDATA[]]></priceSuffixDescription>
			<showBoxItemStringList />
		</finderLiveProductShare>
		<finderOrder>
			<appID><![CDATA[]]></appID>
			<orderID><![CDATA[]]></orderID>
			<path><![CDATA[]]></path>
			<priceWording><![CDATA[]]></priceWording>
			<stateWording><![CDATA[]]></stateWording>
			<productImageURL><![CDATA[]]></productImageURL>
			<products><![CDATA[]]></products>
			<productsCount><![CDATA[0]]></productsCount>
		</finderOrder>
		<finderShopWindowShare>
			<finderUsername><![CDATA[]]></finderUsername>
			<avatar><![CDATA[]]></avatar>
			<nickname><![CDATA[]]></nickname>
			<commodityInStockCount><![CDATA[]]></commodityInStockCount>
			<appId><![CDATA[]]></appId>
			<path><![CDATA[]]></path>
			<appUsername><![CDATA[]]></appUsername>
			<query><![CDATA[]]></query>
			<liteAppId><![CDATA[]]></liteAppId>
			<liteAppPath><![CDATA[]]></liteAppPath>
			<liteAppQuery><![CDATA[]]></liteAppQuery>
			<platformTagURL><![CDATA[]]></platformTagURL>
			<saleWording><![CDATA[]]></saleWording>
			<lastGMsgID><![CDATA[]]></lastGMsgID>
			<profileTypeWording><![CDATA[]]></profileTypeWording>
			<reputationInfo>
				<hasReputationInfo>0</hasReputationInfo>
				<reputationScore>0</reputationScore>
				<reputationWording />
				<reputationTextColor />
				<reputationLevelWording />
				<reputationBackgroundColor />
			</reputationInfo>
			<productImageURLList />
		</finderShopWindowShare>
		<findernamecard>
			<username />
			<avatar><![CDATA[]]></avatar>
			<nickname />
			<auth_job />
			<auth_icon>0</auth_icon>
			<auth_icon_url />
			<ecSource><![CDATA[]]></ecSource>
			<lastGMsgID><![CDATA[]]></lastGMsgID>
		</findernamecard>
		<finderGuarantee>
			<scene><![CDATA[0]]></scene>
		</finderGuarantee>
		<directshare>0</directshare>
		<gamecenter>
			<namecard>
				<iconUrl />
				<name />
				<desc />
				<tail />
				<jumpUrl />
			</namecard>
		</gamecenter>
		<patMsg>
			<chatUser />
			<records>
				<recordNum>0</recordNum>
			</records>
		</patMsg>
		<secretmsg>
			<issecretmsg>0</issecretmsg>
		</secretmsg>
		<referfromscene>0</referfromscene>
		<gameshare>
			<liteappext>
				<liteappbizdata />
				<priority>0</priority>
			</liteappext>
			<appbrandext>
				<litegameinfo />
				<priority>-1</priority>
			</appbrandext>
			<gameshareid />
			<sharedata />
			<isvideo>0</isvideo>
			<duration>-1</duration>
			<isexposed>0</isexposed>
			<readtext />
		</gameshare>
		<mpsharetrace>
			<hasfinderelement>0</hasfinderelement>
			<lastgmsgid />
		</mpsharetrace>
		<wxgamecard>
			<framesetname />
			<mbcarddata />
			<minpkgversion />
			<mbcardheight>0</mbcardheight>
			<isoldversion>0</isoldversion>
		</wxgamecard>
		<liteapp>
			<id>null</id>
			<path />
			<query />
		</liteapp>
	</appmsg>
	<fromusername>wxid_lneb7n23o4lg12</fromusername>
	<scene>0</scene>
	<appinfo>
		<version>29</version>
		<appname>摇一摇搜歌</appname>
	</appinfo>
	<commenturl></commenturl>
</msg>

2025-07-22 18:42:25 | DEBUG | XML消息类型: 3
2025-07-22 18:42:25 | DEBUG | XML消息标题: 有钱就花-媛小小
2025-07-22 18:42:25 | DEBUG | XML消息描述: 无妄-酷狗
2025-07-22 18:42:25 | DEBUG | 附件信息 totallen: 0
2025-07-22 18:42:25 | DEBUG | 附件信息 islargefilemsg: 0
2025-07-22 18:42:25 | INFO | 收到红包消息: 标题:有钱就花-媛小小 描述:无妄-酷狗 来自:48097389945@chatroom 发送人:wxid_4usgcju5ey9q29
2025-07-22 18:42:26 | DEBUG | 收到消息: {'MsgId': 1585984771, 'FromUserName': {'string': '48097389945@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 49, 'Content': {'string': 'wxid_lneb7n23o4lg12:\n<?xml version="1.0"?>\n<msg>\n\t<appmsg appid="wx485a97c844086dc9" sdkver="0">\n\t\t<title>有钱就花-啊莱的如花世界</title>\n\t\t<des>无妄-汽水</des>\n\t\t<username />\n\t\t<action>view</action>\n\t\t<type>3</type>\n\t\t<showtype>0</showtype>\n\t\t<content />\n\t\t<url />\n\t\t<lowurl />\n\t\t<forwardflag>0</forwardflag>\n\t\t<dataurl>https://v3-luna.douyinvod.com/3b1888e501de4916c93d9e131c7a297f/6880cb1a/video/tos/cn/tos-cn-ve-2774/oABIDMC8gCuGAUfVZFf8goDB6Baa4EtC7x14Qq/</dataurl>\n\t\t<lowdataurl />\n\t\t<contentattr>0</contentattr>\n\t\t<streamvideo>\n\t\t\t<streamvideourl />\n\t\t\t<streamvideototaltime>0</streamvideototaltime>\n\t\t\t<streamvideotitle />\n\t\t\t<streamvideowording />\n\t\t\t<streamvideoweburl />\n\t\t\t<streamvideothumburl />\n\t\t\t<streamvideoaduxinfo />\n\t\t\t<streamvideopublishid />\n\t\t</streamvideo>\n\t\t<canvasPageItem>\n\t\t\t<canvasPageXml><![CDATA[]]></canvasPageXml>\n\t\t</canvasPageItem>\n\t\t<appattach>\n\t\t\t<totallen>0</totallen>\n\t\t\t<attachid />\n\t\t\t<cdnattachurl />\n\t\t\t<emoticonmd5 />\n\t\t\t<aeskey />\n\t\t\t<fileext />\n\t\t\t<islargefilemsg>0</islargefilemsg>\n\t\t</appattach>\n\t\t<extinfo />\n\t\t<androidsource>2</androidsource>\n\t\t<thumburl />\n\t\t<mediatagname />\n\t\t<messageaction><![CDATA[]]></messageaction>\n\t\t<messageext><![CDATA[]]></messageext>\n\t\t<emoticongift>\n\t\t\t<packageflag>0</packageflag>\n\t\t\t<packageid />\n\t\t</emoticongift>\n\t\t<emoticonshared>\n\t\t\t<packageflag>0</packageflag>\n\t\t\t<packageid />\n\t\t</emoticonshared>\n\t\t<designershared>\n\t\t\t<designeruin>0</designeruin>\n\t\t\t<designername>null</designername>\n\t\t\t<designerrediretcturl><![CDATA[null]]></designerrediretcturl>\n\t\t</designershared>\n\t\t<emotionpageshared>\n\t\t\t<tid>0</tid>\n\t\t\t<title>null</title>\n\t\t\t<desc>null</desc>\n\t\t\t<iconUrl><![CDATA[null]]></iconUrl>\n\t\t\t<secondUrl>null</secondUrl>\n\t\t\t<pageType>0</pageType>\n\t\t\t<setKey>null</setKey>\n\t\t</emotionpageshared>\n\t\t<webviewshared>\n\t\t\t<shareUrlOriginal />\n\t\t\t<shareUrlOpen />\n\t\t\t<jsAppId />\n\t\t\t<publisherId />\n\t\t\t<publisherReqId />\n\t\t</webviewshared>\n\t\t<template_id />\n\t\t<md5 />\n\t\t<websearch>\n\t\t\t<rec_category>0</rec_category>\n\t\t\t<channelId>0</channelId>\n\t\t</websearch>\n\t\t<weappinfo>\n\t\t\t<username />\n\t\t\t<appid />\n\t\t\t<appservicetype>0</appservicetype>\n\t\t\t<secflagforsinglepagemode>0</secflagforsinglepagemode>\n\t\t\t<videopageinfo>\n\t\t\t\t<thumbwidth>0</thumbwidth>\n\t\t\t\t<thumbheight>0</thumbheight>\n\t\t\t\t<fromopensdk>0</fromopensdk>\n\t\t\t</videopageinfo>\n\t\t</weappinfo>\n\t\t<statextstr>GhQKEnd4NDg1YTk3Yzg0NDA4NmRjOQ==</statextstr>\n\t\t<songalbumurl>https://wx.qlogo.cn/mmhead/ver_1/iaZCKGjtAic5icOnI1ibaY7DCzx0SVR3ic6NXwcAHpFTZIzDcvzMaeNlEYqezUL04icbD6ZylLwcGMYIzQy6kjY4AdicRyb39fASpxswdSeDrs5tomibd2sADOgpbRIF6jic2v1ibFafdgicYm69maZehIgNhg8Tw/0</songalbumurl>\n\t\t<songlyric>[00:00.00]\n</songlyric>\n\t\t<musicShareItem>\n\t\t\t<musicDuration>0</musicDuration>\n\t\t</musicShareItem>\n\t\t<finderLiveProductShare>\n\t\t\t<finderLiveID><![CDATA[]]></finderLiveID>\n\t\t\t<finderUsername><![CDATA[]]></finderUsername>\n\t\t\t<finderObjectID><![CDATA[]]></finderObjectID>\n\t\t\t<finderNonceID><![CDATA[]]></finderNonceID>\n\t\t\t<liveStatus><![CDATA[]]></liveStatus>\n\t\t\t<appId><![CDATA[]]></appId>\n\t\t\t<pagePath><![CDATA[]]></pagePath>\n\t\t\t<productId><![CDATA[]]></productId>\n\t\t\t<coverUrl><![CDATA[]]></coverUrl>\n\t\t\t<productTitle><![CDATA[]]></productTitle>\n\t\t\t<marketPrice><![CDATA[0]]></marketPrice>\n\t\t\t<sellingPrice><![CDATA[0]]></sellingPrice>\n\t\t\t<platformHeadImg><![CDATA[]]></platformHeadImg>\n\t\t\t<platformName><![CDATA[]]></platformName>\n\t\t\t<shopWindowId><![CDATA[]]></shopWindowId>\n\t\t\t<flashSalePrice><![CDATA[0]]></flashSalePrice>\n\t\t\t<flashSaleEndTime><![CDATA[0]]></flashSaleEndTime>\n\t\t\t<ecSource><![CDATA[]]></ecSource>\n\t\t\t<sellingPriceWording><![CDATA[]]></sellingPriceWording>\n\t\t\t<platformIconURL><![CDATA[]]></platformIconURL>\n\t\t\t<firstProductTagURL><![CDATA[]]></firstProductTagURL>\n\t\t\t<firstProductTagAspectRatioString><![CDATA[0.0]]></firstProductTagAspectRatioString>\n\t\t\t<secondProductTagURL><![CDATA[]]></secondProductTagURL>\n\t\t\t<secondProductTagAspectRatioString><![CDATA[0.0]]></secondProductTagAspectRatioString>\n\t\t\t<firstGuaranteeWording><![CDATA[]]></firstGuaranteeWording>\n\t\t\t<secondGuaranteeWording><![CDATA[]]></secondGuaranteeWording>\n\t\t\t<thirdGuaranteeWording><![CDATA[]]></thirdGuaranteeWording>\n\t\t\t<isPriceBeginShow>false</isPriceBeginShow>\n\t\t\t<lastGMsgID><![CDATA[]]></lastGMsgID>\n\t\t\t<promoterKey><![CDATA[]]></promoterKey>\n\t\t\t<discountWording><![CDATA[]]></discountWording>\n\t\t\t<priceSuffixDescription><![CDATA[]]></priceSuffixDescription>\n\t\t\t<showBoxItemStringList />\n\t\t</finderLiveProductShare>\n\t\t<finderOrder>\n\t\t\t<appID><![CDATA[]]></appID>\n\t\t\t<orderID><![CDATA[]]></orderID>\n\t\t\t<path><![CDATA[]]></path>\n\t\t\t<priceWording><![CDATA[]]></priceWording>\n\t\t\t<stateWording><![CDATA[]]></stateWording>\n\t\t\t<productImageURL><![CDATA[]]></productImageURL>\n\t\t\t<products><![CDATA[]]></products>\n\t\t\t<productsCount><![CDATA[0]]></productsCount>\n\t\t</finderOrder>\n\t\t<finderShopWindowShare>\n\t\t\t<finderUsername><![CDATA[]]></finderUsername>\n\t\t\t<avatar><![CDATA[]]></avatar>\n\t\t\t<nickname><![CDATA[]]></nickname>\n\t\t\t<commodityInStockCount><![CDATA[]]></commodityInStockCount>\n\t\t\t<appId><![CDATA[]]></appId>\n\t\t\t<path><![CDATA[]]></path>\n\t\t\t<appUsername><![CDATA[]]></appUsername>\n\t\t\t<query><![CDATA[]]></query>\n\t\t\t<liteAppId><![CDATA[]]></liteAppId>\n\t\t\t<liteAppPath><![CDATA[]]></liteAppPath>\n\t\t\t<liteAppQuery><![CDATA[]]></liteAppQuery>\n\t\t\t<platformTagURL><![CDATA[]]></platformTagURL>\n\t\t\t<saleWording><![CDATA[]]></saleWording>\n\t\t\t<lastGMsgID><![CDATA[]]></lastGMsgID>\n\t\t\t<profileTypeWording><![CDATA[]]></profileTypeWording>\n\t\t\t<reputationInfo>\n\t\t\t\t<hasReputationInfo>0</hasReputationInfo>\n\t\t\t\t<reputationScore>0</reputationScore>\n\t\t\t\t<reputationWording />\n\t\t\t\t<reputationTextColor />\n\t\t\t\t<reputationLevelWording />\n\t\t\t\t<reputationBackgroundColor />\n\t\t\t</reputationInfo>\n\t\t\t<productImageURLList />\n\t\t</finderShopWindowShare>\n\t\t<findernamecard>\n\t\t\t<username />\n\t\t\t<avatar><![CDATA[]]></avatar>\n\t\t\t<nickname />\n\t\t\t<auth_job />\n\t\t\t<auth_icon>0</auth_icon>\n\t\t\t<auth_icon_url />\n\t\t\t<ecSource><![CDATA[]]></ecSource>\n\t\t\t<lastGMsgID><![CDATA[]]></lastGMsgID>\n\t\t</findernamecard>\n\t\t<finderGuarantee>\n\t\t\t<scene><![CDATA[0]]></scene>\n\t\t</finderGuarantee>\n\t\t<directshare>0</directshare>\n\t\t<gamecenter>\n\t\t\t<namecard>\n\t\t\t\t<iconUrl />\n\t\t\t\t<name />\n\t\t\t\t<desc />\n\t\t\t\t<tail />\n\t\t\t\t<jumpUrl />\n\t\t\t</namecard>\n\t\t</gamecenter>\n\t\t<patMsg>\n\t\t\t<chatUser />\n\t\t\t<records>\n\t\t\t\t<recordNum>0</recordNum>\n\t\t\t</records>\n\t\t</patMsg>\n\t\t<secretmsg>\n\t\t\t<issecretmsg>0</issecretmsg>\n\t\t</secretmsg>\n\t\t<referfromscene>0</referfromscene>\n\t\t<gameshare>\n\t\t\t<liteappext>\n\t\t\t\t<liteappbizdata />\n\t\t\t\t<priority>0</priority>\n\t\t\t</liteappext>\n\t\t\t<appbrandext>\n\t\t\t\t<litegameinfo />\n\t\t\t\t<priority>-1</priority>\n\t\t\t</appbrandext>\n\t\t\t<gameshareid />\n\t\t\t<sharedata />\n\t\t\t<isvideo>0</isvideo>\n\t\t\t<duration>-1</duration>\n\t\t\t<isexposed>0</isexposed>\n\t\t\t<readtext />\n\t\t</gameshare>\n\t\t<mpsharetrace>\n\t\t\t<hasfinderelement>0</hasfinderelement>\n\t\t\t<lastgmsgid />\n\t\t</mpsharetrace>\n\t\t<wxgamecard>\n\t\t\t<framesetname />\n\t\t\t<mbcarddata />\n\t\t\t<minpkgversion />\n\t\t\t<mbcardheight>0</mbcardheight>\n\t\t\t<isoldversion>0</isoldversion>\n\t\t</wxgamecard>\n\t\t<liteapp>\n\t\t\t<id>null</id>\n\t\t\t<path />\n\t\t\t<query />\n\t\t</liteapp>\n\t</appmsg>\n\t<fromusername>wxid_lneb7n23o4lg12</fromusername>\n\t<scene>0</scene>\n\t<appinfo>\n\t\t<version>29</version>\n\t\t<appname>摇一摇搜歌</appname>\n\t</appinfo>\n\t<commenturl></commenturl>\n</msg>\n'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1753180954, 'MsgSource': '<msgsource>\n\t<sec_msg_node>\n\t\t<uuid>14a6e145568e0975c19f93894016e729_</uuid>\n\t\t<risk-file-flag />\n\t\t<risk-file-md5-list />\n\t</sec_msg_node>\n\t<silence>0</silence>\n\t<membercount>62</membercount>\n\t<signature>N0_V1_ttPZdyio|v1_QdbIl2pa</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'PushContent': '你收到了一条消息', 'NewMsgId': 6138835001578430678, 'MsgSeq': 871392696}
2025-07-22 18:42:26 | DEBUG | 从群聊消息中提取发送者: wxid_lneb7n23o4lg12
2025-07-22 18:42:26 | DEBUG | XML消息完整内容:
<?xml version="1.0"?>
<msg>
	<appmsg appid="wx485a97c844086dc9" sdkver="0">
		<title>有钱就花-啊莱的如花世界</title>
		<des>无妄-汽水</des>
		<username />
		<action>view</action>
		<type>3</type>
		<showtype>0</showtype>
		<content />
		<url />
		<lowurl />
		<forwardflag>0</forwardflag>
		<dataurl>https://v3-luna.douyinvod.com/3b1888e501de4916c93d9e131c7a297f/6880cb1a/video/tos/cn/tos-cn-ve-2774/oABIDMC8gCuGAUfVZFf8goDB6Baa4EtC7x14Qq/</dataurl>
		<lowdataurl />
		<contentattr>0</contentattr>
		<streamvideo>
			<streamvideourl />
			<streamvideototaltime>0</streamvideototaltime>
			<streamvideotitle />
			<streamvideowording />
			<streamvideoweburl />
			<streamvideothumburl />
			<streamvideoaduxinfo />
			<streamvideopublishid />
		</streamvideo>
		<canvasPageItem>
			<canvasPageXml><![CDATA[]]></canvasPageXml>
		</canvasPageItem>
		<appattach>
			<totallen>0</totallen>
			<attachid />
			<cdnattachurl />
			<emoticonmd5 />
			<aeskey />
			<fileext />
			<islargefilemsg>0</islargefilemsg>
		</appattach>
		<extinfo />
		<androidsource>2</androidsource>
		<thumburl />
		<mediatagname />
		<messageaction><![CDATA[]]></messageaction>
		<messageext><![CDATA[]]></messageext>
		<emoticongift>
			<packageflag>0</packageflag>
			<packageid />
		</emoticongift>
		<emoticonshared>
			<packageflag>0</packageflag>
			<packageid />
		</emoticonshared>
		<designershared>
			<designeruin>0</designeruin>
			<designername>null</designername>
			<designerrediretcturl><![CDATA[null]]></designerrediretcturl>
		</designershared>
		<emotionpageshared>
			<tid>0</tid>
			<title>null</title>
			<desc>null</desc>
			<iconUrl><![CDATA[null]]></iconUrl>
			<secondUrl>null</secondUrl>
			<pageType>0</pageType>
			<setKey>null</setKey>
		</emotionpageshared>
		<webviewshared>
			<shareUrlOriginal />
			<shareUrlOpen />
			<jsAppId />
			<publisherId />
			<publisherReqId />
		</webviewshared>
		<template_id />
		<md5 />
		<websearch>
			<rec_category>0</rec_category>
			<channelId>0</channelId>
		</websearch>
		<weappinfo>
			<username />
			<appid />
			<appservicetype>0</appservicetype>
			<secflagforsinglepagemode>0</secflagforsinglepagemode>
			<videopageinfo>
				<thumbwidth>0</thumbwidth>
				<thumbheight>0</thumbheight>
				<fromopensdk>0</fromopensdk>
			</videopageinfo>
		</weappinfo>
		<statextstr>GhQKEnd4NDg1YTk3Yzg0NDA4NmRjOQ==</statextstr>
		<songalbumurl>https://wx.qlogo.cn/mmhead/ver_1/iaZCKGjtAic5icOnI1ibaY7DCzx0SVR3ic6NXwcAHpFTZIzDcvzMaeNlEYqezUL04icbD6ZylLwcGMYIzQy6kjY4AdicRyb39fASpxswdSeDrs5tomibd2sADOgpbRIF6jic2v1ibFafdgicYm69maZehIgNhg8Tw/0</songalbumurl>
		<songlyric>[00:00.00]
</songlyric>
		<musicShareItem>
			<musicDuration>0</musicDuration>
		</musicShareItem>
		<finderLiveProductShare>
			<finderLiveID><![CDATA[]]></finderLiveID>
			<finderUsername><![CDATA[]]></finderUsername>
			<finderObjectID><![CDATA[]]></finderObjectID>
			<finderNonceID><![CDATA[]]></finderNonceID>
			<liveStatus><![CDATA[]]></liveStatus>
			<appId><![CDATA[]]></appId>
			<pagePath><![CDATA[]]></pagePath>
			<productId><![CDATA[]]></productId>
			<coverUrl><![CDATA[]]></coverUrl>
			<productTitle><![CDATA[]]></productTitle>
			<marketPrice><![CDATA[0]]></marketPrice>
			<sellingPrice><![CDATA[0]]></sellingPrice>
			<platformHeadImg><![CDATA[]]></platformHeadImg>
			<platformName><![CDATA[]]></platformName>
			<shopWindowId><![CDATA[]]></shopWindowId>
			<flashSalePrice><![CDATA[0]]></flashSalePrice>
			<flashSaleEndTime><![CDATA[0]]></flashSaleEndTime>
			<ecSource><![CDATA[]]></ecSource>
			<sellingPriceWording><![CDATA[]]></sellingPriceWording>
			<platformIconURL><![CDATA[]]></platformIconURL>
			<firstProductTagURL><![CDATA[]]></firstProductTagURL>
			<firstProductTagAspectRatioString><![CDATA[0.0]]></firstProductTagAspectRatioString>
			<secondProductTagURL><![CDATA[]]></secondProductTagURL>
			<secondProductTagAspectRatioString><![CDATA[0.0]]></secondProductTagAspectRatioString>
			<firstGuaranteeWording><![CDATA[]]></firstGuaranteeWording>
			<secondGuaranteeWording><![CDATA[]]></secondGuaranteeWording>
			<thirdGuaranteeWording><![CDATA[]]></thirdGuaranteeWording>
			<isPriceBeginShow>false</isPriceBeginShow>
			<lastGMsgID><![CDATA[]]></lastGMsgID>
			<promoterKey><![CDATA[]]></promoterKey>
			<discountWording><![CDATA[]]></discountWording>
			<priceSuffixDescription><![CDATA[]]></priceSuffixDescription>
			<showBoxItemStringList />
		</finderLiveProductShare>
		<finderOrder>
			<appID><![CDATA[]]></appID>
			<orderID><![CDATA[]]></orderID>
			<path><![CDATA[]]></path>
			<priceWording><![CDATA[]]></priceWording>
			<stateWording><![CDATA[]]></stateWording>
			<productImageURL><![CDATA[]]></productImageURL>
			<products><![CDATA[]]></products>
			<productsCount><![CDATA[0]]></productsCount>
		</finderOrder>
		<finderShopWindowShare>
			<finderUsername><![CDATA[]]></finderUsername>
			<avatar><![CDATA[]]></avatar>
			<nickname><![CDATA[]]></nickname>
			<commodityInStockCount><![CDATA[]]></commodityInStockCount>
			<appId><![CDATA[]]></appId>
			<path><![CDATA[]]></path>
			<appUsername><![CDATA[]]></appUsername>
			<query><![CDATA[]]></query>
			<liteAppId><![CDATA[]]></liteAppId>
			<liteAppPath><![CDATA[]]></liteAppPath>
			<liteAppQuery><![CDATA[]]></liteAppQuery>
			<platformTagURL><![CDATA[]]></platformTagURL>
			<saleWording><![CDATA[]]></saleWording>
			<lastGMsgID><![CDATA[]]></lastGMsgID>
			<profileTypeWording><![CDATA[]]></profileTypeWording>
			<reputationInfo>
				<hasReputationInfo>0</hasReputationInfo>
				<reputationScore>0</reputationScore>
				<reputationWording />
				<reputationTextColor />
				<reputationLevelWording />
				<reputationBackgroundColor />
			</reputationInfo>
			<productImageURLList />
		</finderShopWindowShare>
		<findernamecard>
			<username />
			<avatar><![CDATA[]]></avatar>
			<nickname />
			<auth_job />
			<auth_icon>0</auth_icon>
			<auth_icon_url />
			<ecSource><![CDATA[]]></ecSource>
			<lastGMsgID><![CDATA[]]></lastGMsgID>
		</findernamecard>
		<finderGuarantee>
			<scene><![CDATA[0]]></scene>
		</finderGuarantee>
		<directshare>0</directshare>
		<gamecenter>
			<namecard>
				<iconUrl />
				<name />
				<desc />
				<tail />
				<jumpUrl />
			</namecard>
		</gamecenter>
		<patMsg>
			<chatUser />
			<records>
				<recordNum>0</recordNum>
			</records>
		</patMsg>
		<secretmsg>
			<issecretmsg>0</issecretmsg>
		</secretmsg>
		<referfromscene>0</referfromscene>
		<gameshare>
			<liteappext>
				<liteappbizdata />
				<priority>0</priority>
			</liteappext>
			<appbrandext>
				<litegameinfo />
				<priority>-1</priority>
			</appbrandext>
			<gameshareid />
			<sharedata />
			<isvideo>0</isvideo>
			<duration>-1</duration>
			<isexposed>0</isexposed>
			<readtext />
		</gameshare>
		<mpsharetrace>
			<hasfinderelement>0</hasfinderelement>
			<lastgmsgid />
		</mpsharetrace>
		<wxgamecard>
			<framesetname />
			<mbcarddata />
			<minpkgversion />
			<mbcardheight>0</mbcardheight>
			<isoldversion>0</isoldversion>
		</wxgamecard>
		<liteapp>
			<id>null</id>
			<path />
			<query />
		</liteapp>
	</appmsg>
	<fromusername>wxid_lneb7n23o4lg12</fromusername>
	<scene>0</scene>
	<appinfo>
		<version>29</version>
		<appname>摇一摇搜歌</appname>
	</appinfo>
	<commenturl></commenturl>
</msg>

2025-07-22 18:42:26 | DEBUG | XML消息类型: 3
2025-07-22 18:42:26 | DEBUG | XML消息标题: 有钱就花-啊莱的如花世界
2025-07-22 18:42:26 | DEBUG | XML消息描述: 无妄-汽水
2025-07-22 18:42:26 | DEBUG | 附件信息 totallen: 0
2025-07-22 18:42:26 | DEBUG | 附件信息 islargefilemsg: 0
2025-07-22 18:42:26 | INFO | 收到红包消息: 标题:有钱就花-啊莱的如花世界 描述:无妄-汽水 来自:48097389945@chatroom 发送人:wxid_4usgcju5ey9q29
2025-07-22 18:42:26 | DEBUG | 收到消息: {'MsgId': 956836782, 'FromUserName': {'string': '48097389945@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 49, 'Content': {'string': 'wxid_srknxij3jka022:\n<?xml version="1.0"?>\n<msg>\n\t<appmsg appid="wx485a97c844086dc9" sdkver="0">\n\t\t<title>大户人家</title>\n\t\t<des>白宇正 - 无妄</des>\n\t\t<username />\n\t\t<action>view</action>\n\t\t<type>3</type>\n\t\t<showtype>0</showtype>\n\t\t<content />\n\t\t<url />\n\t\t<lowurl />\n\t\t<forwardflag>0</forwardflag>\n\t\t<dataurl>http://43.140.245.243:1080/ppp.php?u=wylb&amp;ikey=34625775414b5246334f6547705779324d42766533754f334f65624f67724d614f54346d59766f322f7230746b3966386e5639657146387062584d306a613666693169522f657171787a53745a6c4f7a6548666167702f3777484e74316c42497a57457643366f496a58593d&amp;d=5149716c48744d39306532575947356b747a7650446c3533304159745570692b43333364564a37454f595131364c52365864764e7756684464646e64593473626635596c4175706a6c30366f6771314c5350707338413d3d&amp;type=302</dataurl>\n\t\t<lowdataurl />\n\t\t<contentattr>0</contentattr>\n\t\t<streamvideo>\n\t\t\t<streamvideourl />\n\t\t\t<streamvideototaltime>0</streamvideototaltime>\n\t\t\t<streamvideotitle />\n\t\t\t<streamvideowording />\n\t\t\t<streamvideoweburl />\n\t\t\t<streamvideothumburl />\n\t\t\t<streamvideoaduxinfo />\n\t\t\t<streamvideopublishid />\n\t\t</streamvideo>\n\t\t<canvasPageItem>\n\t\t\t<canvasPageXml><![CDATA[]]></canvasPageXml>\n\t\t</canvasPageItem>\n\t\t<appattach>\n\t\t\t<totallen>0</totallen>\n\t\t\t<attachid />\n\t\t\t<cdnattachurl />\n\t\t\t<emoticonmd5 />\n\t\t\t<aeskey />\n\t\t\t<fileext />\n\t\t\t<islargefilemsg>0</islargefilemsg>\n\t\t</appattach>\n\t\t<extinfo />\n\t\t<androidsource>2</androidsource>\n\t\t<thumburl />\n\t\t<mediatagname />\n\t\t<messageaction><![CDATA[]]></messageaction>\n\t\t<messageext><![CDATA[]]></messageext>\n\t\t<emoticongift>\n\t\t\t<packageflag>0</packageflag>\n\t\t\t<packageid />\n\t\t</emoticongift>\n\t\t<emoticonshared>\n\t\t\t<packageflag>0</packageflag>\n\t\t\t<packageid />\n\t\t</emoticonshared>\n\t\t<designershared>\n\t\t\t<designeruin>0</designeruin>\n\t\t\t<designername>null</designername>\n\t\t\t<designerrediretcturl><![CDATA[null]]></designerrediretcturl>\n\t\t</designershared>\n\t\t<emotionpageshared>\n\t\t\t<tid>0</tid>\n\t\t\t<title>null</title>\n\t\t\t<desc>null</desc>\n\t\t\t<iconUrl><![CDATA[null]]></iconUrl>\n\t\t\t<secondUrl>null</secondUrl>\n\t\t\t<pageType>0</pageType>\n\t\t\t<setKey>null</setKey>\n\t\t</emotionpageshared>\n\t\t<webviewshared>\n\t\t\t<shareUrlOriginal />\n\t\t\t<shareUrlOpen />\n\t\t\t<jsAppId />\n\t\t\t<publisherId />\n\t\t\t<publisherReqId />\n\t\t</webviewshared>\n\t\t<template_id />\n\t\t<md5 />\n\t\t<websearch>\n\t\t\t<rec_category>0</rec_category>\n\t\t\t<channelId>0</channelId>\n\t\t</websearch>\n\t\t<weappinfo>\n\t\t\t<username />\n\t\t\t<appid />\n\t\t\t<appservicetype>0</appservicetype>\n\t\t\t<secflagforsinglepagemode>0</secflagforsinglepagemode>\n\t\t\t<videopageinfo>\n\t\t\t\t<thumbwidth>0</thumbwidth>\n\t\t\t\t<thumbheight>0</thumbheight>\n\t\t\t\t<fromopensdk>0</fromopensdk>\n\t\t\t</videopageinfo>\n\t\t</weappinfo>\n\t\t<statextstr>GhQKEnd4NDg1YTk3Yzg0NDA4NmRjOQ==</statextstr>\n\t\t<songalbumurl>https://wx.qlogo.cn/mmhead/ver_1/M3Cxb3tmBiapwLmdrkibreU3xvaQ7SRERdiajnmeefwrTX1VeTbayBrd22QCUr5CgawADGR1LjZcwoy3Oia2H1BUudLS9v6dpRVhssJlNVZbt2dTq8uicgcOlezLf3oEX6U3pxKhEmNoYCuZHC0qRwsm2BQ/0</songalbumurl>\n\t\t<songlyric>[00:00.00] 作词 : 白宇正\n[00:01.00] 作曲 : 白宇正\n[00:02.00] 编曲 : 史嵩昊\n[00:03.00] 制作人 : 金华\n[00:12.06]\n[00:26.03]脚上的刺\n[00:27.26]没有人能看得到\n[00:28.75]要不涂上\n[00:29.80]那进口红水药\n[00:32.41]就算没有依靠\n[00:33.29]会跌倒\n[00:34.23]也无法\n[00:35.22]阻止我奔跑\n[00:37.27]有人惯养又娇生\n[00:39.03]从小\n[00:40.03]可我更生又自立\n[00:42.39]到老\n[00:43.27]我的押韵\n[00:44.31]为何这么屌\n[00:47.17]远远看着放在路边\n[00:49.34]违停区域的那超跑\n[00:50.59]我更喜欢去坐公交\n[00:52.39]他们穿着华丽衣服\n[00:54.37]打量我说我太俗套\n[00:56.07]鄙视我穿的厚棉袄\n[00:58.05]他们左边穿金右边戴银\n[01:00.36]挎着过万的包\n[01:01.79]冷的发抖还在嘲笑\n[01:03.85]我说兄弟出门前\n[01:05.60]能不能\n[01:06.40]看一看天气预报\n[01:27.72]脚上的刺\n[01:28.90]没有人能看得到\n[01:30.25]要不涂上\n[01:31.55]那进口红水药\n[01:32.98]就算没有依靠\n[01:34.34]会跌倒\n[01:35.70]wu wo～\n[01:37.04]wu wo～\n[01:38.74]有人惯养又娇生从小\n[01:41.40]旁人苛责听不到\n[01:44.47]wu wo～\n[01:45.72]wu wo～\n[01:48.54]远远看着放在路边\n[01:50.59]违停区域的那超跑\n[01:51.89]我更喜欢去坐公交\n[01:53.86]他们穿着华丽衣服\n[01:55.84]打量我说我太俗套\n[01:57.39]鄙视我穿的厚棉袄\n[01:59.36]他们左边穿金右边戴银\n[02:01.64]挎着过万的包\n[02:03.06]冷的发抖还在嘲笑\n[02:05.06]我说兄弟出门前\n[02:06.84]能不能看一看天气预报\n[02:11.80]bridge\n[02:12.79]大户人家\n[02:14.71]有钱就花\n[02:16.07]忘记了吗\n[02:20.39]路边停车费快交一下\n[02:23.04]看着放在路边\n[02:24.40]违停区域的那超跑\n[02:25.52]我更喜欢去坐公交\n[02:27.32]他们穿着华丽衣服\n[02:29.16]打量我说我太俗套\n[02:31.09]鄙视我穿的厚棉袄\n[02:32.88]他们左边穿金右边戴银\n[02:35.17]挎着过万的包\n[02:36.64]冷的发抖还在嘲笑\n[02:38.68]我说兄弟你出门前\n[02:40.96]看一看天气预报\n[02:56.68] 和声 : 白宇正\n[02:57.18] 吉他 : 乔浚丞\n[02:57.68] 录音师 : 刘彦宏\n[02:58.18] 混音师 : 刘彦宏\n[02:58.68] 音乐监制 : 韩冰\n[02:59.18] 发行人 : 潘昊铖\n[02:59.68] 制作公司 : 即刻半音\n[03:00.18] OP : 北京华数文化传媒有限公司\n[03:00.68] SP : 北京华数文化传媒有限公司\n[03:01.18] （未经著作权人许可 不得翻唱 翻录或使用）\n</songlyric>\n\t\t<musicShareItem>\n\t\t\t<musicDuration>0</musicDuration>\n\t\t</musicShareItem>\n\t\t<finderLiveProductShare>\n\t\t\t<finderLiveID><![CDATA[]]></finderLiveID>\n\t\t\t<finderUsername><![CDATA[]]></finderUsername>\n\t\t\t<finderObjectID><![CDATA[]]></finderObjectID>\n\t\t\t<finderNonceID><![CDATA[]]></finderNonceID>\n\t\t\t<liveStatus><![CDATA[]]></liveStatus>\n\t\t\t<appId><![CDATA[]]></appId>\n\t\t\t<pagePath><![CDATA[]]></pagePath>\n\t\t\t<productId><![CDATA[]]></productId>\n\t\t\t<coverUrl><![CDATA[]]></coverUrl>\n\t\t\t<productTitle><![CDATA[]]></productTitle>\n\t\t\t<marketPrice><![CDATA[0]]></marketPrice>\n\t\t\t<sellingPrice><![CDATA[0]]></sellingPrice>\n\t\t\t<platformHeadImg><![CDATA[]]></platformHeadImg>\n\t\t\t<platformName><![CDATA[]]></platformName>\n\t\t\t<shopWindowId><![CDATA[]]></shopWindowId>\n\t\t\t<flashSalePrice><![CDATA[0]]></flashSalePrice>\n\t\t\t<flashSaleEndTime><![CDATA[0]]></flashSaleEndTime>\n\t\t\t<ecSource><![CDATA[]]></ecSource>\n\t\t\t<sellingPriceWording><![CDATA[]]></sellingPriceWording>\n\t\t\t<platformIconURL><![CDATA[]]></platformIconURL>\n\t\t\t<firstProductTagURL><![CDATA[]]></firstProductTagURL>\n\t\t\t<firstProductTagAspectRatioString><![CDATA[0.0]]></firstProductTagAspectRatioString>\n\t\t\t<secondProductTagURL><![CDATA[]]></secondProductTagURL>\n\t\t\t<secondProductTagAspectRatioString><![CDATA[0.0]]></secondProductTagAspectRatioString>\n\t\t\t<firstGuaranteeWording><![CDATA[]]></firstGuaranteeWording>\n\t\t\t<secondGuaranteeWording><![CDATA[]]></secondGuaranteeWording>\n\t\t\t<thirdGuaranteeWording><![CDATA[]]></thirdGuaranteeWording>\n\t\t\t<isPriceBeginShow>false</isPriceBeginShow>\n\t\t\t<lastGMsgID><![CDATA[]]></lastGMsgID>\n\t\t\t<promoterKey><![CDATA[]]></promoterKey>\n\t\t\t<discountWording><![CDATA[]]></discountWording>\n\t\t\t<priceSuffixDescription><![CDATA[]]></priceSuffixDescription>\n\t\t\t<productCardKey><![CDATA[]]></productCardKey>\n\t\t\t<isWxShop><![CDATA[]]></isWxShop>\n\t\t\t<brandIconUrl><![CDATA[]]></brandIconUrl>\n\t\t\t<rIconUrl><![CDATA[]]></rIconUrl>\n\t\t\t<rIconUrlDarkMode><![CDATA[]]></rIconUrlDarkMode>\n\t\t\t<topShopIconUrl><![CDATA[]]></topShopIconUrl>\n\t\t\t<topShopIconUrlDarkMode><![CDATA[]]></topShopIconUrlDarkMode>\n\t\t\t<simplifyTopShopIconUrl><![CDATA[]]></simplifyTopShopIconUrl>\n\t\t\t<simplifyTopShopIconUrlDarkmode><![CDATA[]]></simplifyTopShopIconUrlDarkmode>\n\t\t\t<topShopIconWidth><![CDATA[0]]></topShopIconWidth>\n\t\t\t<topShopIconHeight><![CDATA[0]]></topShopIconHeight>\n\t\t\t<simplifyTopShopIconWidth><![CDATA[0]]></simplifyTopShopIconWidth>\n\t\t\t<simplifyTopShopIconHeight><![CDATA[0]]></simplifyTopShopIconHeight>\n\t\t\t<showBoxItemStringList />\n\t\t</finderLiveProductShare>\n\t\t<finderOrder>\n\t\t\t<appID><![CDATA[]]></appID>\n\t\t\t<orderID><![CDATA[]]></orderID>\n\t\t\t<path><![CDATA[]]></path>\n\t\t\t<priceWording><![CDATA[]]></priceWording>\n\t\t\t<stateWording><![CDATA[]]></stateWording>\n\t\t\t<productImageURL><![CDATA[]]></productImageURL>\n\t\t\t<products><![CDATA[]]></products>\n\t\t\t<productsCount><![CDATA[0]]></productsCount>\n\t\t\t<orderType><![CDATA[0]]></orderType>\n\t\t\t<newPriceWording><![CDATA[]]></newPriceWording>\n\t\t\t<newStateWording><![CDATA[]]></newStateWording>\n\t\t\t<useNewWording><![CDATA[0]]></useNewWording>\n\t\t</finderOrder>\n\t\t<finderShopWindowShare>\n\t\t\t<finderUsername><![CDATA[]]></finderUsername>\n\t\t\t<avatar><![CDATA[]]></avatar>\n\t\t\t<nickname><![CDATA[]]></nickname>\n\t\t\t<commodityInStockCount><![CDATA[]]></commodityInStockCount>\n\t\t\t<appId><![CDATA[]]></appId>\n\t\t\t<path><![CDATA[]]></path>\n\t\t\t<appUsername><![CDATA[]]></appUsername>\n\t\t\t<query><![CDATA[]]></query>\n\t\t\t<liteAppId><![CDATA[]]></liteAppId>\n\t\t\t<liteAppPath><![CDATA[]]></liteAppPath>\n\t\t\t<liteAppQuery><![CDATA[]]></liteAppQuery>\n\t\t\t<platformTagURL><![CDATA[]]></platformTagURL>\n\t\t\t<saleWording><![CDATA[]]></saleWording>\n\t\t\t<lastGMsgID><![CDATA[]]></lastGMsgID>\n\t\t\t<profileTypeWording><![CDATA[]]></profileTypeWording>\n\t\t\t<saleWordingExtra><![CDATA[]]></saleWordingExtra>\n\t\t\t<isWxShop><![CDATA[]]></isWxShop>\n\t\t\t<platformIconUrl><![CDATA[]]></platformIconUrl>\n\t\t\t<brandIconUrl><![CDATA[]]></brandIconUrl>\n\t\t\t<description><![CDATA[]]></description>\n\t\t\t<backgroundUrl><![CDATA[]]></backgroundUrl>\n\t\t\t<darkModePlatformIconUrl><![CDATA[]]></darkModePlatformIconUrl>\n\t\t\t<rIconUrl><![CDATA[]]></rIconUrl>\n\t\t\t<rIconUrlDarkMode><![CDATA[]]></rIconUrlDarkMode>\n\t\t\t<rWords><![CDATA[]]></rWords>\n\t\t\t<topShopIconUrl><![CDATA[]]></topShopIconUrl>\n\t\t\t<topShopIconUrlDarkMode><![CDATA[]]></topShopIconUrlDarkMode>\n\t\t\t<simplifyTopShopIconUrl><![CDATA[]]></simplifyTopShopIconUrl>\n\t\t\t<simplifyTopShopIconUrlDarkmode><![CDATA[]]></simplifyTopShopIconUrlDarkmode>\n\t\t\t<topShopIconWidth><![CDATA[0]]></topShopIconWidth>\n\t\t\t<topShopIconHeight><![CDATA[0]]></topShopIconHeight>\n\t\t\t<simplifyTopShopIconWidth><![CDATA[0]]></simplifyTopShopIconWidth>\n\t\t\t<simplifyTopShopIconHeight><![CDATA[0]]></simplifyTopShopIconHeight>\n\t\t\t<reputationInfo>\n\t\t\t\t<hasReputationInfo>0</hasReputationInfo>\n\t\t\t\t<reputationScore>0</reputationScore>\n\t\t\t\t<reputationWording />\n\t\t\t\t<reputationTextColor />\n\t\t\t\t<reputationLevelWording />\n\t\t\t\t<reputationBackgroundColor />\n\t\t\t</reputationInfo>\n\t\t\t<productImageURLList />\n\t\t</finderShopWindowShare>\n\t\t<findernamecard>\n\t\t\t<username />\n\t\t\t<avatar><![CDATA[]]></avatar>\n\t\t\t<nickname />\n\t\t\t<auth_job />\n\t\t\t<auth_icon>0</auth_icon>\n\t\t\t<auth_icon_url />\n\t\t\t<ecSource><![CDATA[]]></ecSource>\n\t\t\t<lastGMsgID><![CDATA[]]></lastGMsgID>\n\t\t</findernamecard>\n\t\t<finderGuarantee>\n\t\t\t<scene><![CDATA[0]]></scene>\n\t\t</finderGuarantee>\n\t\t<directshare>0</directshare>\n\t\t<gamecenter>\n\t\t\t<namecard>\n\t\t\t\t<iconUrl />\n\t\t\t\t<name />\n\t\t\t\t<desc />\n\t\t\t\t<tail />\n\t\t\t\t<jumpUrl />\n\t\t\t\t<liteappId />\n\t\t\t\t<liteappPath />\n\t\t\t\t<liteappQuery />\n\t\t\t\t<liteappMinVersion />\n\t\t\t</namecard>\n\t\t</gamecenter>\n\t\t<patMsg>\n\t\t\t<chatUser />\n\t\t\t<records>\n\t\t\t\t<recordNum>0</recordNum>\n\t\t\t</records>\n\t\t</patMsg>\n\t\t<secretmsg>\n\t\t\t<issecretmsg>0</issecretmsg>\n\t\t</secretmsg>\n\t\t<referfromscene>0</referfromscene>\n\t\t<gameshare>\n\t\t\t<liteappext>\n\t\t\t\t<liteappbizdata />\n\t\t\t\t<priority>0</priority>\n\t\t\t</liteappext>\n\t\t\t<appbrandext>\n\t\t\t\t<litegameinfo />\n\t\t\t\t<priority>-1</priority>\n\t\t\t</appbrandext>\n\t\t\t<gameshareid />\n\t\t\t<sharedata />\n\t\t\t<isvideo>0</isvideo>\n\t\t\t<duration>-1</duration>\n\t\t\t<isexposed>0</isexposed>\n\t\t\t<readtext />\n\t\t</gameshare>\n\t\t<tingChatRoomItem>\n\t\t\t<type>0</type>\n\t\t\t<categoryItem>null</categoryItem>\n\t\t\t<categoryId />\n\t\t</tingChatRoomItem>\n\t\t<mpsharetrace>\n\t\t\t<hasfinderelement>0</hasfinderelement>\n\t\t\t<lastgmsgid />\n\t\t</mpsharetrace>\n\t\t<wxgamecard>\n\t\t\t<framesetname />\n\t\t\t<mbcarddata />\n\t\t\t<minpkgversion />\n\t\t\t<clientextinfo />\n\t\t\t<mbcardheight>0</mbcardheight>\n\t\t\t<isoldversion>0</isoldversion>\n\t\t</wxgamecard>\n\t\t<ecskfcard>\n\t\t\t<framesetname />\n\t\t\t<mbcarddata />\n\t\t\t<minupdateunixtimestamp>0</minupdateunixtimestamp>\n\t\t\t<needheader>false</needheader>\n\t\t\t<summary />\n\t\t</ecskfcard>\n\t\t<liteapp>\n\t\t\t<id>null</id>\n\t\t\t<path />\n\t\t\t<query />\n\t\t\t<istransparent>0</istransparent>\n\t\t\t<hideicon>0</hideicon>\n\t\t\t<forbidforward>0</forbidforward>\n\t\t</liteapp>\n\t\t<opensdk_share_is_modified>0</opensdk_share_is_modified>\n\t</appmsg>\n\t<fromusername>wxid_srknxij3jka022</fromusername>\n\t<scene>0</scene>\n\t<appinfo>\n\t\t<version>29</version>\n\t\t<appname>摇一摇搜歌</appname>\n\t</appinfo>\n\t<commenturl></commenturl>\n</msg>\n'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1753180958, 'MsgSource': '<msgsource>\n\t<sec_msg_node>\n\t\t<uuid>aafaf0f3748fc1aabd456ffe7df77626_</uuid>\n\t\t<risk-file-flag />\n\t\t<risk-file-md5-list />\n\t</sec_msg_node>\n\t<silence>0</silence>\n\t<membercount>62</membercount>\n\t<signature>N0_V1_0JB7vQWf|v1_eWxxFKry</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'PushContent': '你收到了一条消息', 'NewMsgId': 6163516280028816547, 'MsgSeq': 871392697}
2025-07-22 18:42:26 | DEBUG | 从群聊消息中提取发送者: wxid_srknxij3jka022
2025-07-22 18:42:26 | DEBUG | XML消息完整内容:
<?xml version="1.0"?>
<msg>
	<appmsg appid="wx485a97c844086dc9" sdkver="0">
		<title>大户人家</title>
		<des>白宇正 - 无妄</des>
		<username />
		<action>view</action>
		<type>3</type>
		<showtype>0</showtype>
		<content />
		<url />
		<lowurl />
		<forwardflag>0</forwardflag>
		<dataurl>http://43.140.245.243:1080/ppp.php?u=wylb&amp;ikey=34625775414b5246334f6547705779324d42766533754f334f65624f67724d614f54346d59766f322f7230746b3966386e5639657146387062584d306a613666693169522f657171787a53745a6c4f7a6548666167702f3777484e74316c42497a57457643366f496a58593d&amp;d=5149716c48744d39306532575947356b747a7650446c3533304159745570692b43333364564a37454f595131364c52365864764e7756684464646e64593473626635596c4175706a6c30366f6771314c5350707338413d3d&amp;type=302</dataurl>
		<lowdataurl />
		<contentattr>0</contentattr>
		<streamvideo>
			<streamvideourl />
			<streamvideototaltime>0</streamvideototaltime>
			<streamvideotitle />
			<streamvideowording />
			<streamvideoweburl />
			<streamvideothumburl />
			<streamvideoaduxinfo />
			<streamvideopublishid />
		</streamvideo>
		<canvasPageItem>
			<canvasPageXml><![CDATA[]]></canvasPageXml>
		</canvasPageItem>
		<appattach>
			<totallen>0</totallen>
			<attachid />
			<cdnattachurl />
			<emoticonmd5 />
			<aeskey />
			<fileext />
			<islargefilemsg>0</islargefilemsg>
		</appattach>
		<extinfo />
		<androidsource>2</androidsource>
		<thumburl />
		<mediatagname />
		<messageaction><![CDATA[]]></messageaction>
		<messageext><![CDATA[]]></messageext>
		<emoticongift>
			<packageflag>0</packageflag>
			<packageid />
		</emoticongift>
		<emoticonshared>
			<packageflag>0</packageflag>
			<packageid />
		</emoticonshared>
		<designershared>
			<designeruin>0</designeruin>
			<designername>null</designername>
			<designerrediretcturl><![CDATA[null]]></designerrediretcturl>
		</designershared>
		<emotionpageshared>
			<tid>0</tid>
			<title>null</title>
			<desc>null</desc>
			<iconUrl><![CDATA[null]]></iconUrl>
			<secondUrl>null</secondUrl>
			<pageType>0</pageType>
			<setKey>null</setKey>
		</emotionpageshared>
		<webviewshared>
			<shareUrlOriginal />
			<shareUrlOpen />
			<jsAppId />
			<publisherId />
			<publisherReqId />
		</webviewshared>
		<template_id />
		<md5 />
		<websearch>
			<rec_category>0</rec_category>
			<channelId>0</channelId>
		</websearch>
		<weappinfo>
			<username />
			<appid />
			<appservicetype>0</appservicetype>
			<secflagforsinglepagemode>0</secflagforsinglepagemode>
			<videopageinfo>
				<thumbwidth>0</thumbwidth>
				<thumbheight>0</thumbheight>
				<fromopensdk>0</fromopensdk>
			</videopageinfo>
		</weappinfo>
		<statextstr>GhQKEnd4NDg1YTk3Yzg0NDA4NmRjOQ==</statextstr>
		<songalbumurl>https://wx.qlogo.cn/mmhead/ver_1/M3Cxb3tmBiapwLmdrkibreU3xvaQ7SRERdiajnmeefwrTX1VeTbayBrd22QCUr5CgawADGR1LjZcwoy3Oia2H1BUudLS9v6dpRVhssJlNVZbt2dTq8uicgcOlezLf3oEX6U3pxKhEmNoYCuZHC0qRwsm2BQ/0</songalbumurl>
		<songlyric>[00:00.00] 作词 : 白宇正
[00:01.00] 作曲 : 白宇正
[00:02.00] 编曲 : 史嵩昊
[00:03.00] 制作人 : 金华
[00:12.06]
[00:26.03]脚上的刺
[00:27.26]没有人能看得到
[00:28.75]要不涂上
[00:29.80]那进口红水药
[00:32.41]就算没有依靠
[00:33.29]会跌倒
[00:34.23]也无法
[00:35.22]阻止我奔跑
[00:37.27]有人惯养又娇生
[00:39.03]从小
[00:40.03]可我更生又自立
[00:42.39]到老
[00:43.27]我的押韵
[00:44.31]为何这么屌
[00:47.17]远远看着放在路边
[00:49.34]违停区域的那超跑
[00:50.59]我更喜欢去坐公交
[00:52.39]他们穿着华丽衣服
[00:54.37]打量我说我太俗套
[00:56.07]鄙视我穿的厚棉袄
[00:58.05]他们左边穿金右边戴银
[01:00.36]挎着过万的包
[01:01.79]冷的发抖还在嘲笑
[01:03.85]我说兄弟出门前
[01:05.60]能不能
[01:06.40]看一看天气预报
[01:27.72]脚上的刺
[01:28.90]没有人能看得到
[01:30.25]要不涂上
[01:31.55]那进口红水药
[01:32.98]就算没有依靠
[01:34.34]会跌倒
[01:35.70]wu wo～
[01:37.04]wu wo～
[01:38.74]有人惯养又娇生从小
[01:41.40]旁人苛责听不到
[01:44.47]wu wo～
[01:45.72]wu wo～
[01:48.54]远远看着放在路边
[01:50.59]违停区域的那超跑
[01:51.89]我更喜欢去坐公交
[01:53.86]他们穿着华丽衣服
[01:55.84]打量我说我太俗套
[01:57.39]鄙视我穿的厚棉袄
[01:59.36]他们左边穿金右边戴银
[02:01.64]挎着过万的包
[02:03.06]冷的发抖还在嘲笑
[02:05.06]我说兄弟出门前
[02:06.84]能不能看一看天气预报
[02:11.80]bridge
[02:12.79]大户人家
[02:14.71]有钱就花
[02:16.07]忘记了吗
[02:20.39]路边停车费快交一下
[02:23.04]看着放在路边
[02:24.40]违停区域的那超跑
[02:25.52]我更喜欢去坐公交
[02:27.32]他们穿着华丽衣服
[02:29.16]打量我说我太俗套
[02:31.09]鄙视我穿的厚棉袄
[02:32.88]他们左边穿金右边戴银
[02:35.17]挎着过万的包
[02:36.64]冷的发抖还在嘲笑
[02:38.68]我说兄弟你出门前
[02:40.96]看一看天气预报
[02:56.68] 和声 : 白宇正
[02:57.18] 吉他 : 乔浚丞
[02:57.68] 录音师 : 刘彦宏
[02:58.18] 混音师 : 刘彦宏
[02:58.68] 音乐监制 : 韩冰
[02:59.18] 发行人 : 潘昊铖
[02:59.68] 制作公司 : 即刻半音
[03:00.18] OP : 北京华数文化传媒有限公司
[03:00.68] SP : 北京华数文化传媒有限公司
[03:01.18] （未经著作权人许可 不得翻唱 翻录或使用）
</songlyric>
		<musicShareItem>
			<musicDuration>0</musicDuration>
		</musicShareItem>
		<finderLiveProductShare>
			<finderLiveID><![CDATA[]]></finderLiveID>
			<finderUsername><![CDATA[]]></finderUsername>
			<finderObjectID><![CDATA[]]></finderObjectID>
			<finderNonceID><![CDATA[]]></finderNonceID>
			<liveStatus><![CDATA[]]></liveStatus>
			<appId><![CDATA[]]></appId>
			<pagePath><![CDATA[]]></pagePath>
			<productId><![CDATA[]]></productId>
			<coverUrl><![CDATA[]]></coverUrl>
			<productTitle><![CDATA[]]></productTitle>
			<marketPrice><![CDATA[0]]></marketPrice>
			<sellingPrice><![CDATA[0]]></sellingPrice>
			<platformHeadImg><![CDATA[]]></platformHeadImg>
			<platformName><![CDATA[]]></platformName>
			<shopWindowId><![CDATA[]]></shopWindowId>
			<flashSalePrice><![CDATA[0]]></flashSalePrice>
			<flashSaleEndTime><![CDATA[0]]></flashSaleEndTime>
			<ecSource><![CDATA[]]></ecSource>
			<sellingPriceWording><![CDATA[]]></sellingPriceWording>
			<platformIconURL><![CDATA[]]></platformIconURL>
			<firstProductTagURL><![CDATA[]]></firstProductTagURL>
			<firstProductTagAspectRatioString><![CDATA[0.0]]></firstProductTagAspectRatioString>
			<secondProductTagURL><![CDATA[]]></secondProductTagURL>
			<secondProductTagAspectRatioString><![CDATA[0.0]]></secondProductTagAspectRatioString>
			<firstGuaranteeWording><![CDATA[]]></firstGuaranteeWording>
			<secondGuaranteeWording><![CDATA[]]></secondGuaranteeWording>
			<thirdGuaranteeWording><![CDATA[]]></thirdGuaranteeWording>
			<isPriceBeginShow>false</isPriceBeginShow>
			<lastGMsgID><![CDATA[]]></lastGMsgID>
			<promoterKey><![CDATA[]]></promoterKey>
			<discountWording><![CDATA[]]></discountWording>
			<priceSuffixDescription><![CDATA[]]></priceSuffixDescription>
			<productCardKey><![CDATA[]]></productCardKey>
			<isWxShop><![CDATA[]]></isWxShop>
			<brandIconUrl><![CDATA[]]></brandIconUrl>
			<rIconUrl><![CDATA[]]></rIconUrl>
			<rIconUrlDarkMode><![CDATA[]]></rIconUrlDarkMode>
			<topShopIconUrl><![CDATA[]]></topShopIconUrl>
			<topShopIconUrlDarkMode><![CDATA[]]></topShopIconUrlDarkMode>
			<simplifyTopShopIconUrl><![CDATA[]]></simplifyTopShopIconUrl>
			<simplifyTopShopIconUrlDarkmode><![CDATA[]]></simplifyTopShopIconUrlDarkmode>
			<topShopIconWidth><![CDATA[0]]></topShopIconWidth>
			<topShopIconHeight><![CDATA[0]]></topShopIconHeight>
			<simplifyTopShopIconWidth><![CDATA[0]]></simplifyTopShopIconWidth>
			<simplifyTopShopIconHeight><![CDATA[0]]></simplifyTopShopIconHeight>
			<showBoxItemStringList />
		</finderLiveProductShare>
		<finderOrder>
			<appID><![CDATA[]]></appID>
			<orderID><![CDATA[]]></orderID>
			<path><![CDATA[]]></path>
			<priceWording><![CDATA[]]></priceWording>
			<stateWording><![CDATA[]]></stateWording>
			<productImageURL><![CDATA[]]></productImageURL>
			<products><![CDATA[]]></products>
			<productsCount><![CDATA[0]]></productsCount>
			<orderType><![CDATA[0]]></orderType>
			<newPriceWording><![CDATA[]]></newPriceWording>
			<newStateWording><![CDATA[]]></newStateWording>
			<useNewWording><![CDATA[0]]></useNewWording>
		</finderOrder>
		<finderShopWindowShare>
			<finderUsername><![CDATA[]]></finderUsername>
			<avatar><![CDATA[]]></avatar>
			<nickname><![CDATA[]]></nickname>
			<commodityInStockCount><![CDATA[]]></commodityInStockCount>
			<appId><![CDATA[]]></appId>
			<path><![CDATA[]]></path>
			<appUsername><![CDATA[]]></appUsername>
			<query><![CDATA[]]></query>
			<liteAppId><![CDATA[]]></liteAppId>
			<liteAppPath><![CDATA[]]></liteAppPath>
			<liteAppQuery><![CDATA[]]></liteAppQuery>
			<platformTagURL><![CDATA[]]></platformTagURL>
			<saleWording><![CDATA[]]></saleWording>
			<lastGMsgID><![CDATA[]]></lastGMsgID>
			<profileTypeWording><![CDATA[]]></profileTypeWording>
			<saleWordingExtra><![CDATA[]]></saleWordingExtra>
			<isWxShop><![CDATA[]]></isWxShop>
			<platformIconUrl><![CDATA[]]></platformIconUrl>
			<brandIconUrl><![CDATA[]]></brandIconUrl>
			<description><![CDATA[]]></description>
			<backgroundUrl><![CDATA[]]></backgroundUrl>
			<darkModePlatformIconUrl><![CDATA[]]></darkModePlatformIconUrl>
			<rIconUrl><![CDATA[]]></rIconUrl>
			<rIconUrlDarkMode><![CDATA[]]></rIconUrlDarkMode>
			<rWords><![CDATA[]]></rWords>
			<topShopIconUrl><![CDATA[]]></topShopIconUrl>
			<topShopIconUrlDarkMode><![CDATA[]]></topShopIconUrlDarkMode>
			<simplifyTopShopIconUrl><![CDATA[]]></simplifyTopShopIconUrl>
			<simplifyTopShopIconUrlDarkmode><![CDATA[]]></simplifyTopShopIconUrlDarkmode>
			<topShopIconWidth><![CDATA[0]]></topShopIconWidth>
			<topShopIconHeight><![CDATA[0]]></topShopIconHeight>
			<simplifyTopShopIconWidth><![CDATA[0]]></simplifyTopShopIconWidth>
			<simplifyTopShopIconHeight><![CDATA[0]]></simplifyTopShopIconHeight>
			<reputationInfo>
				<hasReputationInfo>0</hasReputationInfo>
				<reputationScore>0</reputationScore>
				<reputationWording />
				<reputationTextColor />
				<reputationLevelWording />
				<reputationBackgroundColor />
			</reputationInfo>
			<productImageURLList />
		</finderShopWindowShare>
		<findernamecard>
			<username />
			<avatar><![CDATA[]]></avatar>
			<nickname />
			<auth_job />
			<auth_icon>0</auth_icon>
			<auth_icon_url />
			<ecSource><![CDATA[]]></ecSource>
			<lastGMsgID><![CDATA[]]></lastGMsgID>
		</findernamecard>
		<finderGuarantee>
			<scene><![CDATA[0]]></scene>
		</finderGuarantee>
		<directshare>0</directshare>
		<gamecenter>
			<namecard>
				<iconUrl />
				<name />
				<desc />
				<tail />
				<jumpUrl />
				<liteappId />
				<liteappPath />
				<liteappQuery />
				<liteappMinVersion />
			</namecard>
		</gamecenter>
		<patMsg>
			<chatUser />
			<records>
				<recordNum>0</recordNum>
			</records>
		</patMsg>
		<secretmsg>
			<issecretmsg>0</issecretmsg>
		</secretmsg>
		<referfromscene>0</referfromscene>
		<gameshare>
			<liteappext>
				<liteappbizdata />
				<priority>0</priority>
			</liteappext>
			<appbrandext>
				<litegameinfo />
				<priority>-1</priority>
			</appbrandext>
			<gameshareid />
			<sharedata />
			<isvideo>0</isvideo>
			<duration>-1</duration>
			<isexposed>0</isexposed>
			<readtext />
		</gameshare>
		<tingChatRoomItem>
			<type>0</type>
			<categoryItem>null</categoryItem>
			<categoryId />
		</tingChatRoomItem>
		<mpsharetrace>
			<hasfinderelement>0</hasfinderelement>
			<lastgmsgid />
		</mpsharetrace>
		<wxgamecard>
			<framesetname />
			<mbcarddata />
			<minpkgversion />
			<clientextinfo />
			<mbcardheight>0</mbcardheight>
			<isoldversion>0</isoldversion>
		</wxgamecard>
		<ecskfcard>
			<framesetname />
			<mbcarddata />
			<minupdateunixtimestamp>0</minupdateunixtimestamp>
			<needheader>false</needheader>
			<summary />
		</ecskfcard>
		<liteapp>
			<id>null</id>
			<path />
			<query />
			<istransparent>0</istransparent>
			<hideicon>0</hideicon>
			<forbidforward>0</forbidforward>
		</liteapp>
		<opensdk_share_is_modified>0</opensdk_share_is_modified>
	</appmsg>
	<fromusername>wxid_srknxij3jka022</fromusername>
	<scene>0</scene>
	<appinfo>
		<version>29</version>
		<appname>摇一摇搜歌</appname>
	</appinfo>
	<commenturl></commenturl>
</msg>

2025-07-22 18:42:26 | DEBUG | XML消息类型: 3
2025-07-22 18:42:26 | DEBUG | XML消息标题: 大户人家
2025-07-22 18:42:26 | DEBUG | XML消息描述: 白宇正 - 无妄
2025-07-22 18:42:26 | DEBUG | 附件信息 totallen: 0
2025-07-22 18:42:26 | DEBUG | 附件信息 islargefilemsg: 0
2025-07-22 18:42:26 | INFO | 收到红包消息: 标题:大户人家 描述:白宇正 - 无妄 来自:48097389945@chatroom 发送人:wxid_4usgcju5ey9q29
2025-07-22 18:42:35 | DEBUG | 收到消息: {'MsgId': 184112554, 'FromUserName': {'string': '48097389945@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 1, 'Content': {'string': 'wxid_wlnzvr8ivgd422:\n洞房'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1753180968, 'MsgSource': '<msgsource>\n\t<pua>1</pua>\n\t<eggIncluded>1</eggIncluded>\n\t<silence>0</silence>\n\t<membercount>62</membercount>\n\t<signature>N0_V1_O0Hq8pWt|v1_9peXUFoI</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'PushContent': '锦岚（开玩笑就退群版） : 洞房', 'NewMsgId': 2500753060156924206, 'MsgSeq': 871392698}
2025-07-22 18:42:35 | INFO | 收到文本消息: 消息ID:184112554 来自:48097389945@chatroom 发送人:wxid_wlnzvr8ivgd422 @:[] 内容:洞房
2025-07-22 18:42:36 | DEBUG | 处理消息内容: '洞房'
2025-07-22 18:42:36 | DEBUG | 消息内容 '洞房' 不匹配任何命令，忽略
2025-07-22 18:42:38 | DEBUG | 收到消息: {'MsgId': 1442096212, 'FromUserName': {'string': '48097389945@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 1, 'Content': {'string': 'wxid_q35rkzgkjvlv12:\n💕 [锦岚（开玩笑就退群版）]👩\u200d❤\u200d👨[小爱]💕\n💓═☘︎═•洞💗房•═☘︎═💓\n👩\u200d❤\u200d👨关系：情侣\n⛺地点：阳台\n😍姿势：隔山打牛\n😮\u200d💨结果：失败\n❤\u200d🔥状态：断裂\n💓═☘︎══•💗•══☘︎═💓\n🔮魅力:-11\n[玫瑰]恩爱:-8 \n🕒下次:2025-07-22 19:02:50'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1753180970, 'MsgSource': '<msgsource>\n\t<silence>0</silence>\n\t<membercount>62</membercount>\n\t<signature>N0_V1_Sml4O6d/|v1_ugk9qr3c</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'PushContent': '摇月 : \ue327 [锦岚（开玩笑就退群版）]\ue005\u200d\ue022\u200d\ue004[小爱]\ue327\n\ue327═☘︎═•...', 'NewMsgId': 8571158202117528714, 'MsgSeq': 871392699}
2025-07-22 18:42:38 | INFO | 收到文本消息: 消息ID:1442096212 来自:48097389945@chatroom 发送人:wxid_q35rkzgkjvlv12 @:[] 内容:💕 [锦岚（开玩笑就退群版）]👩‍❤‍👨[小爱]💕
💓═☘︎═•洞💗房•═☘︎═💓
👩‍❤‍👨关系：情侣
⛺地点：阳台
😍姿势：隔山打牛
😮‍💨结果：失败
❤‍🔥状态：断裂
💓═☘︎══•💗•══☘︎═💓
🔮魅力:-11
[玫瑰]恩爱:-8 
🕒下次:2025-07-22 19:02:50
2025-07-22 18:42:38 | DEBUG | 处理消息内容: '💕 [锦岚（开玩笑就退群版）]👩‍❤‍👨[小爱]💕
💓═☘︎═•洞💗房•═☘︎═💓
👩‍❤‍👨关系：情侣
⛺地点：阳台
😍姿势：隔山打牛
😮‍💨结果：失败
❤‍🔥状态：断裂
💓═☘︎══•💗•══☘︎═💓
🔮魅力:-11
[玫瑰]恩爱:-8 
🕒下次:2025-07-22 19:02:50'
2025-07-22 18:42:38 | DEBUG | 消息内容 '💕 [锦岚（开玩笑就退群版）]👩‍❤‍👨[小爱]💕
💓═☘︎═•洞💗房•═☘︎═💓
👩‍❤‍👨关系：情侣
⛺地点：阳台
😍姿势：隔山打牛
😮‍💨结果：失败
❤‍🔥状态：断裂
💓═☘︎══•💗•══☘︎═💓
🔮魅力:-11
[玫瑰]恩爱:-8 
🕒下次:2025-07-22 19:02:50' 不匹配任何命令，忽略
2025-07-22 18:42:40 | DEBUG | 收到消息: {'MsgId': 464589708, 'FromUserName': {'string': '48097389945@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 1, 'Content': {'string': 'wxid_8l9ymg1mafud12:\n「锦岚（开玩笑就退群版）」[爱心]「小爱」\n地点：操场\n活动：双修\n结果：失败\n羞羞：到不了底\n恩爱值减少200\n\n下次:2025-07-22 18:52:49'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1753180970, 'MsgSource': '<msgsource>\n\t<silence>0</silence>\n\t<membercount>62</membercount>\n\t<signature>N0_V1_NJ9vqZ50|v1_Imm0uxK9</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'PushContent': '不吃香菜\ue110 : 「锦岚（开玩笑就退群版）」[爱心]「小爱」\n地点：操场\n活动：双修...', 'NewMsgId': 8037200271889082181, 'MsgSeq': 871392700}
2025-07-22 18:42:40 | INFO | 收到文本消息: 消息ID:464589708 来自:48097389945@chatroom 发送人:wxid_8l9ymg1mafud12 @:[] 内容:「锦岚（开玩笑就退群版）」[爱心]「小爱」
地点：操场
活动：双修
结果：失败
羞羞：到不了底
恩爱值减少200

下次:2025-07-22 18:52:49
2025-07-22 18:42:40 | DEBUG | 处理消息内容: '「锦岚（开玩笑就退群版）」[爱心]「小爱」
地点：操场
活动：双修
结果：失败
羞羞：到不了底
恩爱值减少200

下次:2025-07-22 18:52:49'
2025-07-22 18:42:40 | DEBUG | 消息内容 '「锦岚（开玩笑就退群版）」[爱心]「小爱」
地点：操场
活动：双修
结果：失败
羞羞：到不了底
恩爱值减少200

下次:2025-07-22 18:52:49' 不匹配任何命令，忽略
2025-07-22 18:43:03 | DEBUG | 收到消息: {'MsgId': 867020614, 'FromUserName': {'string': '48097389945@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 1, 'Content': {'string': 'wxid_wlnzvr8ivgd422:\n连续两次失败'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1753180996, 'MsgSource': '<msgsource>\n\t<pua>1</pua>\n\t<eggIncluded>1</eggIncluded>\n\t<silence>0</silence>\n\t<membercount>62</membercount>\n\t<signature>N0_V1_z62igxqz|v1_y8ErriTO</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'PushContent': '锦岚（开玩笑就退群版） : 连续两次失败', 'NewMsgId': 6006651677249323917, 'MsgSeq': 871392701}
2025-07-22 18:43:03 | INFO | 收到文本消息: 消息ID:867020614 来自:48097389945@chatroom 发送人:wxid_wlnzvr8ivgd422 @:[] 内容:连续两次失败
2025-07-22 18:43:03 | DEBUG | 处理消息内容: '连续两次失败'
2025-07-22 18:43:03 | DEBUG | 消息内容 '连续两次失败' 不匹配任何命令，忽略
2025-07-22 18:43:58 | DEBUG | 收到消息: {'MsgId': 1141913913, 'FromUserName': {'string': '48097389945@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 47, 'Content': {'string': 'last--exile:\n<msg><emoji fromusername="last--exile" tousername="48097389945@chatroom" type="2" idbuffer="media:0_0" md5="d4c61a2788f6985a8eb78b81b98accb0" len="3807103" productid="" androidmd5="d4c61a2788f6985a8eb78b81b98accb0" androidlen="3807103" s60v3md5="d4c61a2788f6985a8eb78b81b98accb0" s60v3len="3807103" s60v5md5="d4c61a2788f6985a8eb78b81b98accb0" s60v5len="3807103" cdnurl="http://vweixinf.tc.qq.com/110/20402/stodownload?m=d4c61a2788f6985a8eb78b81b98accb0&amp;filekey=30440201010430302e02016e0402535a0420643463363161323738386636393835613865623738623831623938616363623002033a177f040d00000004627466730000000132&amp;hy=SZ&amp;storeid=2645e0597000e43126d8f64bc0000006e01004fb2535a2eaa3970b64866fe0&amp;ef=1&amp;bizid=1022" designerid="" thumburl="" encrypturl="http://vweixinf.tc.qq.com/110/20402/stodownload?m=81cc5fec7eac32cb70f16ff3a79a5a7e&amp;filekey=30440201010430302e02016e0402535a0420383163633566656337656163333263623730663136666633613739613561376502033a1780040d00000004627466730000000132&amp;hy=SZ&amp;storeid=2645e0598000340d16d8f64bc0000006e02004fb2535a2eaa3970b64867037&amp;ef=2&amp;bizid=1022" aeskey="d25d2cd1e7a24da9bb8e5f5adc2cc7ab" externurl="http://vweixinf.tc.qq.com/110/20403/stodownload?m=65bb71f6e6ea475c75cee3f1e1ad5d12&amp;filekey=30440201010430302e02016e0402535a04203635626237316636653665613437356337356365653366316531616435643132020303a980040d00000004627466730000000132&amp;hy=SZ&amp;storeid=2645e059800072ca86d8f64bc0000006e03004fb3535a2eaa3970b64867093&amp;ef=3&amp;bizid=1022" externmd5="5795c4f8ad918b139dc95a61c1c1d6a4" width="198" height="300" tpurl="" tpauthkey="" attachedtext="" attachedtextcolor="" lensid="" emojiattr="" linkid="" desc=""></emoji></msg>'}, 'Status': 3, 'ImgStatus': 2, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1753181051, 'MsgSource': '<msgsource>\n\t<silence>0</silence>\n\t<membercount>62</membercount>\n\t<signature>N0_V1_9FDE8hUz|v1_kgMGQGth</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'PushContent': '亮在群聊中发了一个表情', 'NewMsgId': 6897052585556521485, 'MsgSeq': 871392702}
2025-07-22 18:43:58 | INFO | 收到表情消息: 消息ID:1141913913 来自:48097389945@chatroom 发送人:last--exile MD5:d4c61a2788f6985a8eb78b81b98accb0 大小:3807103
2025-07-22 18:43:58 | DEBUG | [DoubaoImageRecognition] 缓存表情消息: 6897052585556521485
2025-07-22 18:44:00 | DEBUG | 收到消息: {'MsgId': 1344690213, 'FromUserName': {'string': '48097389945@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 1, 'Content': {'string': 'last--exile:\n下班'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1753181054, 'MsgSource': '<msgsource>\n\t<pua>1</pua>\n\t<silence>0</silence>\n\t<membercount>62</membercount>\n\t<signature>N0_V1_IVqT4NGI|v1_eMjNGgwh</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'PushContent': '亮 : 下班', 'NewMsgId': 7658845997066531722, 'MsgSeq': 871392703}
2025-07-22 18:44:00 | INFO | 收到文本消息: 消息ID:1344690213 来自:48097389945@chatroom 发送人:last--exile @:[] 内容:下班
2025-07-22 18:44:01 | DEBUG | 处理消息内容: '下班'
2025-07-22 18:44:01 | DEBUG | 消息内容 '下班' 不匹配任何命令，忽略
2025-07-22 18:46:39 | DEBUG | 收到消息: {'MsgId': 1257397141, 'FromUserName': {'string': '48097389945@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 49, 'Content': {'string': 'wxid_hqdtktnqvw8e21:\n<?xml version="1.0"?>\n<msg>\n\t<appmsg appid="" sdkver="0">\n\t\t<title>牛</title>\n\t\t<type>57</type>\n\t\t<appattach>\n\t\t\t<cdnthumbaeskey />\n\t\t\t<aeskey />\n\t\t</appattach>\n\t\t<refermsg>\n\t\t\t<type>1</type>\n\t\t\t<svrid>2151539757507869952</svrid>\n\t\t\t<fromusr>48097389945@chatroom</fromusr>\n\t\t\t<chatusr>wxid_jegyk4i3v7zg22</chatusr>\n\t\t\t<displayname>阿尼亚与她</displayname>\n\t\t\t<content>处理闲鱼订单</content>\n\t\t\t<msgsource>&lt;msgsource&gt;&lt;sequence_id&gt;835250139&lt;/sequence_id&gt;\n\t&lt;bizflag&gt;0&lt;/bizflag&gt;\n\t&lt;pua&gt;1&lt;/pua&gt;\n\t&lt;eggIncluded&gt;1&lt;/eggIncluded&gt;\n\t&lt;silence&gt;1&lt;/silence&gt;\n\t&lt;membercount&gt;62&lt;/membercount&gt;\n\t&lt;signature&gt;N0_V1_A+AJTafU|v1_4P61zCgQ&lt;/signature&gt;\n\t&lt;tmp_node&gt;\n\t\t&lt;publisher-id&gt;&lt;/publisher-id&gt;\n\t&lt;/tmp_node&gt;\n&lt;/msgsource&gt;\n</msgsource>\n\t\t\t<createtime>1753180403</createtime>\n\t\t</refermsg>\n\t</appmsg>\n\t<fromusername>wxid_hqdtktnqvw8e21</fromusername>\n\t<scene>0</scene>\n\t<appinfo>\n\t\t<version>1</version>\n\t\t<appname></appname>\n\t</appinfo>\n\t<commenturl></commenturl>\n</msg>\n'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1753181212, 'MsgSource': '<msgsource>\n\t<bizflag>0</bizflag>\n\t<pua>1</pua>\n\t<sec_msg_node>\n\t\t<uuid>8e964fb96a6a83321d5abc72988a292f_</uuid>\n\t\t<risk-file-flag />\n\t\t<risk-file-md5-list />\n\t</sec_msg_node>\n\t<silence>0</silence>\n\t<membercount>62</membercount>\n\t<signature>N0_V1_jTE3xCHv|v1_+fFww68X</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'PushContent': 'Ritz : 牛', 'NewMsgId': 4540444366111480966, 'MsgSeq': 871392704}
2025-07-22 18:46:39 | DEBUG | 从群聊消息中提取发送者: wxid_hqdtktnqvw8e21
2025-07-22 18:46:39 | DEBUG | 使用已解析的XML处理引用消息
2025-07-22 18:46:39 | INFO | 收到引用消息: 消息ID:1257397141 来自:48097389945@chatroom 发送人:wxid_hqdtktnqvw8e21 内容:牛 引用类型:1
2025-07-22 18:46:39 | INFO | [DouBaoImageToImage] 收到引用消息: 牛
2025-07-22 18:46:39 | INFO | [TimerTask] 收到引用消息调试信息:
2025-07-22 18:46:39 | INFO |   - 消息内容: 牛
2025-07-22 18:46:39 | INFO |   - 群组ID: 48097389945@chatroom
2025-07-22 18:46:39 | INFO |   - 发送人: wxid_hqdtktnqvw8e21
2025-07-22 18:46:39 | INFO |   - 引用信息: {'MsgType': 1, 'Content': '处理闲鱼订单', 'Msgid': '2151539757507869952', 'NewMsgId': '2151539757507869952', 'ToWxid': 'wxid_4usgcju5ey9q29', 'FromWxid': '48097389945@chatroom', 'Nickname': '阿尼亚与她', 'MsgSource': '<msgsource><sequence_id>835250139</sequence_id>\n\t<bizflag>0</bizflag>\n\t<pua>1</pua>\n\t<eggIncluded>1</eggIncluded>\n\t<silence>1</silence>\n\t<membercount>62</membercount>\n\t<signature>N0_V1_A+AJTafU|v1_4P61zCgQ</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'Createtime': '1753180403', 'SenderWxid': 'wxid_hqdtktnqvw8e21'}
2025-07-22 18:46:39 | INFO |   - 引用消息ID: 
2025-07-22 18:46:39 | INFO |   - 引用消息类型: 
2025-07-22 18:46:39 | INFO |   - 引用消息内容: 处理闲鱼订单
2025-07-22 18:46:39 | INFO |   - 引用消息发送人: wxid_hqdtktnqvw8e21
2025-07-22 18:47:00 | DEBUG | 收到消息: {'MsgId': 237447471, 'FromUserName': {'string': '48097389945@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 49, 'Content': {'string': 'wxid_hqdtktnqvw8e21:\n<?xml version="1.0"?>\n<msg>\n\t<appmsg appid="" sdkver="0">\n\t\t<title>还看不了</title>\n\t\t<type>57</type>\n\t\t<appattach>\n\t\t\t<cdnthumbaeskey />\n\t\t\t<aeskey />\n\t\t</appattach>\n\t\t<refermsg>\n\t\t\t<type>49</type>\n\t\t\t<svrid>7940574495556795</svrid>\n\t\t\t<fromusr>48097389945@chatroom</fromusr>\n\t\t\t<chatusr>wxid_wlnzvr8ivgd422</chatusr>\n\t\t\t<displayname>锦岚（开玩笑就退群版）</displayname>\n\t\t\t<content>&lt;msg&gt;&lt;appmsg appid=""  sdkver="0"&gt;&lt;title&gt;看看同款&lt;/title&gt;&lt;type&gt;57&lt;/type&gt;&lt;action&gt;view&lt;/action&gt;&lt;appattach&gt;&lt;cdnthumbaeskey&gt;&lt;/cdnthumbaeskey&gt;&lt;aeskey&gt;&lt;/aeskey&gt;&lt;/appattach&gt;&lt;webviewshared&gt;&lt;jsAppId&gt;&lt;![CDATA[]]&gt;&lt;/jsAppId&gt;&lt;/webviewshared&gt;&lt;mpsharetrace&gt;&lt;hasfinderelement&gt;0&lt;/hasfinderelement&gt;&lt;/mpsharetrace&gt;&lt;secretmsg&gt;&lt;isscrectmsg&gt;0&lt;/isscrectmsg&gt;&lt;/secretmsg&gt;&lt;/appmsg&gt;&lt;fromusername&gt;wxid_wlnzvr8ivgd422&lt;/fromusername&gt;&lt;appinfo&gt;&lt;version&gt;1&lt;/version&gt;&lt;appname&gt;&lt;/appname&gt;&lt;isforceupdate&gt;1&lt;/isforceupdate&gt;&lt;/appinfo&gt;&lt;/msg&gt;</content>\n\t\t\t<msgsource>&lt;msgsource&gt;&lt;sequence_id&gt;835250213&lt;/sequence_id&gt;\n\t&lt;alnode&gt;\n\t\t&lt;fr&gt;4&lt;/fr&gt;\n\t&lt;/alnode&gt;\n\t&lt;eggIncluded&gt;1&lt;/eggIncluded&gt;\n\t&lt;sec_msg_node&gt;\n\t\t&lt;uuid&gt;575f8943f0356befc7aa76da4d844fdd_&lt;/uuid&gt;\n\t\t&lt;risk-file-flag /&gt;\n\t\t&lt;risk-file-md5-list /&gt;\n\t&lt;/sec_msg_node&gt;\n\t&lt;silence&gt;1&lt;/silence&gt;\n\t&lt;membercount&gt;62&lt;/membercount&gt;\n\t&lt;signature&gt;N0_V1_vc56h+PE|v1_atIRloox&lt;/signature&gt;\n\t&lt;tmp_node&gt;\n\t\t&lt;publisher-id&gt;&lt;/publisher-id&gt;\n\t&lt;/tmp_node&gt;\n&lt;/msgsource&gt;\n</msgsource>\n\t\t\t<createtime>1753180849</createtime>\n\t\t</refermsg>\n\t</appmsg>\n\t<fromusername>wxid_hqdtktnqvw8e21</fromusername>\n\t<scene>0</scene>\n\t<appinfo>\n\t\t<version>1</version>\n\t\t<appname></appname>\n\t</appinfo>\n\t<commenturl></commenturl>\n</msg>\n'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1753181233, 'MsgSource': '<msgsource>\n\t<bizflag>0</bizflag>\n\t<pua>1</pua>\n\t<sec_msg_node>\n\t\t<uuid>d6b9dfc6a0a2a99300a13dcbbb0a1e0d_</uuid>\n\t\t<risk-file-flag />\n\t\t<risk-file-md5-list />\n\t</sec_msg_node>\n\t<silence>0</silence>\n\t<membercount>62</membercount>\n\t<signature>N0_V1_mJOrHaT9|v1_oO2v6yJo</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'PushContent': 'Ritz : 还看不了', 'NewMsgId': 8421707060987329016, 'MsgSeq': 871392705}
2025-07-22 18:47:00 | DEBUG | 从群聊消息中提取发送者: wxid_hqdtktnqvw8e21
2025-07-22 18:47:00 | DEBUG | 使用已解析的XML处理引用消息
2025-07-22 18:47:00 | INFO | 收到引用消息: 消息ID:237447471 来自:48097389945@chatroom 发送人:wxid_hqdtktnqvw8e21 内容:还看不了 引用类型:49
2025-07-22 18:47:00 | INFO | [DouBaoImageToImage] 收到引用消息: 还看不了
2025-07-22 18:47:00 | INFO | [TimerTask] 收到引用消息调试信息:
2025-07-22 18:47:00 | INFO |   - 消息内容: 还看不了
2025-07-22 18:47:00 | INFO |   - 群组ID: 48097389945@chatroom
2025-07-22 18:47:00 | INFO |   - 发送人: wxid_hqdtktnqvw8e21
2025-07-22 18:47:00 | INFO |   - 引用信息: {'MsgType': 49, 'Content': '<msg><appmsg appid=""  sdkver="0"><title>看看同款</title><type>57</type><action>view</action><appattach><cdnthumbaeskey></cdnthumbaeskey><aeskey></aeskey></appattach><webviewshared><jsAppId><![CDATA[]]></jsAppId></webviewshared><mpsharetrace><hasfinderelement>0</hasfinderelement></mpsharetrace><secretmsg><isscrectmsg>0</isscrectmsg></secretmsg></appmsg><fromusername>wxid_wlnzvr8ivgd422</fromusername><appinfo><version>1</version><appname></appname><isforceupdate>1</isforceupdate></appinfo></msg>', 'Msgid': '7940574495556795', 'NewMsgId': '7940574495556795', 'ToWxid': 'wxid_4usgcju5ey9q29', 'FromWxid': '48097389945@chatroom', 'Nickname': '锦岚（开玩笑就退群版）', 'MsgSource': '<msgsource><sequence_id>835250213</sequence_id>\n\t<alnode>\n\t\t<fr>4</fr>\n\t</alnode>\n\t<eggIncluded>1</eggIncluded>\n\t<sec_msg_node>\n\t\t<uuid>575f8943f0356befc7aa76da4d844fdd_</uuid>\n\t\t<risk-file-flag />\n\t\t<risk-file-md5-list />\n\t</sec_msg_node>\n\t<silence>1</silence>\n\t<membercount>62</membercount>\n\t<signature>N0_V1_vc56h+PE|v1_atIRloox</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'Createtime': '1753180849', 'SenderWxid': 'wxid_hqdtktnqvw8e21'}
2025-07-22 18:47:00 | INFO |   - 引用消息ID: 
2025-07-22 18:47:00 | INFO |   - 引用消息类型: 
2025-07-22 18:47:00 | INFO |   - 引用消息内容: <msg><appmsg appid=""  sdkver="0"><title>看看同款</title><type>57</type><action>view</action><appattach><cdnthumbaeskey></cdnthumbaeskey><aeskey></aeskey></appattach><webviewshared><jsAppId><![CDATA[]]></jsAppId></webviewshared><mpsharetrace><hasfinderelement>0</hasfinderelement></mpsharetrace><secretmsg><isscrectmsg>0</isscrectmsg></secretmsg></appmsg><fromusername>wxid_wlnzvr8ivgd422</fromusername><appinfo><version>1</version><appname></appname><isforceupdate>1</isforceupdate></appinfo></msg>
2025-07-22 18:47:00 | INFO |   - 引用消息发送人: wxid_hqdtktnqvw8e21
2025-07-22 18:47:06 | DEBUG | 收到消息: {'MsgId': 2008538506, 'FromUserName': {'string': '48097389945@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 1, 'Content': {'string': 'wxid_hqdtktnqvw8e21:\n还没下班'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1753181239, 'MsgSource': '<msgsource>\n\t<bizflag>0</bizflag>\n\t<pua>1</pua>\n\t<silence>0</silence>\n\t<membercount>62</membercount>\n\t<signature>N0_V1_03Ik8s/u|v1_sXBgAmgL</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'PushContent': 'Ritz : 还没下班', 'NewMsgId': 5544964129008274541, 'MsgSeq': 871392706}
2025-07-22 18:47:06 | INFO | 收到文本消息: 消息ID:2008538506 来自:48097389945@chatroom 发送人:wxid_hqdtktnqvw8e21 @:[] 内容:还没下班
2025-07-22 18:47:07 | DEBUG | 处理消息内容: '还没下班'
2025-07-22 18:47:07 | DEBUG | 消息内容 '还没下班' 不匹配任何命令，忽略
2025-07-22 18:47:10 | DEBUG | 收到消息: {'MsgId': 606496271, 'FromUserName': {'string': '48097389945@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 47, 'Content': {'string': 'wxid_hqdtktnqvw8e21:\n<msg><emoji fromusername = "wxid_hqdtktnqvw8e21" tousername = "48097389945@chatroom" type="2" idbuffer="media:0_0" md5="c2bf1402c3f92fb36964bf4f2c6331d3" len = "32149" productid="" androidmd5="c2bf1402c3f92fb36964bf4f2c6331d3" androidlen="32149" s60v3md5 = "c2bf1402c3f92fb36964bf4f2c6331d3" s60v3len="32149" s60v5md5 = "c2bf1402c3f92fb36964bf4f2c6331d3" s60v5len="32149" cdnurl = "http://wxapp.tc.qq.com/262/20304/stodownload?m=c2bf1402c3f92fb36964bf4f2c6331d3&amp;filekey=30340201010420301e02020106040253480410c2bf1402c3f92fb36964bf4f2c6331d302027d95040d00000004627466730000000132&amp;hy=SH&amp;storeid=2631e1956000762fa000000000000010600004f50534806fd2a00b746e5a28&amp;bizid=1023" designerid = "" thumburl = "" encrypturl = "http://wxapp.tc.qq.com/262/20304/stodownload?m=1b1a00805509b86362cb8d094173f957&amp;filekey=30340201010420301e020201060402534804101b1a00805509b86362cb8d094173f95702027da0040d00000004627466730000000132&amp;hy=SH&amp;storeid=2631e1956000b18f0000000000000010600004f5053481ec67b40b7481bba5&amp;bizid=1023" aeskey= "b1232b76222fc078c66ebef435945167" externurl = "http://wxapp.tc.qq.com/262/20304/stodownload?m=0cc2d3312bd2660f5ae8295cce682a0c&amp;filekey=30340201010420301e020201060402534804100cc2d3312bd2660f5ae8295cce682a0c02024710040d00000004627466730000000132&amp;hy=SH&amp;storeid=2631e1956000eb04f000000000000010600004f5053481c067b40b7462312a&amp;bizid=1023" externmd5 = "e74fbd56f609acc31317b7c69201d50e" width= "440" height= "458" tpurl= "" tpauthkey= "" attachedtext= "" attachedtextcolor= "" lensid= "" emojiattr= "CgbmlZHlkb0=" linkid= "" desc= "" ></emoji> <gameext type="0" content="0" ></gameext> </msg>'}, 'Status': 3, 'ImgStatus': 2, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1753181243, 'MsgSource': '<msgsource>\n\t<silence>0</silence>\n\t<membercount>62</membercount>\n\t<signature>N0_V1_RJC8/LY/|v1_1FM/jog8</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'PushContent': 'Ritz在群聊中发了一个表情', 'NewMsgId': 6331775537828326493, 'MsgSeq': 871392707}
2025-07-22 18:47:10 | INFO | 收到表情消息: 消息ID:606496271 来自:48097389945@chatroom 发送人:wxid_hqdtktnqvw8e21 MD5:c2bf1402c3f92fb36964bf4f2c6331d3 大小:32149
2025-07-22 18:47:11 | DEBUG | [DoubaoImageRecognition] 缓存表情消息: 6331775537828326493
2025-07-22 18:47:13 | DEBUG | 收到消息: {'MsgId': 1166801014, 'FromUserName': {'string': '48097389945@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 1, 'Content': {'string': 'xiaomaochong:\n洞房'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1753181246, 'MsgSource': '<msgsource>\n\t<pua>1</pua>\n\t<silence>0</silence>\n\t<membercount>62</membercount>\n\t<signature>N0_V1_8Rwplh/g|v1_XlfdV4z6</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'PushContent': '小爱 : 洞房', 'NewMsgId': 4696107666827030856, 'MsgSeq': 871392708}
2025-07-22 18:47:13 | INFO | 收到文本消息: 消息ID:1166801014 来自:48097389945@chatroom 发送人:xiaomaochong @:[] 内容:洞房
2025-07-22 18:47:13 | DEBUG | 处理消息内容: '洞房'
2025-07-22 18:47:13 | DEBUG | 消息内容 '洞房' 不匹配任何命令，忽略
2025-07-22 18:47:15 | DEBUG | 收到消息: {'MsgId': 1180992214, 'FromUserName': {'string': '48097389945@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 1, 'Content': {'string': 'wxid_q35rkzgkjvlv12:\n💕 [小爱]👩\u200d❤\u200d👨[锦岚（开玩笑就退群版）]💕\n💓═☘︎═•洞💗房•═☘︎═💓\n👩\u200d❤\u200d👨关系：情侣\n⛺地点：阳台\n😍姿势：隔山打牛\n😮\u200d💨结果：失败\n❤\u200d🔥状态：断裂\n💓═☘︎══•💗•══☘︎═💓\n🔮魅力:-39\n[玫瑰]恩爱:-2 \n🕒下次:2025-07-22 19:07:28'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1753181247, 'MsgSource': '<msgsource>\n\t<silence>0</silence>\n\t<membercount>62</membercount>\n\t<signature>N0_V1_WC+4gUtp|v1_D/LZ8o5Z</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'PushContent': '摇月 : \ue327 [小爱]\ue005\u200d\ue022\u200d\ue004[锦岚（开玩笑就退群版）]\ue327\n\ue327═☘︎═•...', 'NewMsgId': 1317463401713064254, 'MsgSeq': 871392709}
2025-07-22 18:47:15 | INFO | 收到文本消息: 消息ID:1180992214 来自:48097389945@chatroom 发送人:wxid_q35rkzgkjvlv12 @:[] 内容:💕 [小爱]👩‍❤‍👨[锦岚（开玩笑就退群版）]💕
💓═☘︎═•洞💗房•═☘︎═💓
👩‍❤‍👨关系：情侣
⛺地点：阳台
😍姿势：隔山打牛
😮‍💨结果：失败
❤‍🔥状态：断裂
💓═☘︎══•💗•══☘︎═💓
🔮魅力:-39
[玫瑰]恩爱:-2 
🕒下次:2025-07-22 19:07:28
2025-07-22 18:47:15 | DEBUG | 处理消息内容: '💕 [小爱]👩‍❤‍👨[锦岚（开玩笑就退群版）]💕
💓═☘︎═•洞💗房•═☘︎═💓
👩‍❤‍👨关系：情侣
⛺地点：阳台
😍姿势：隔山打牛
😮‍💨结果：失败
❤‍🔥状态：断裂
💓═☘︎══•💗•══☘︎═💓
🔮魅力:-39
[玫瑰]恩爱:-2 
🕒下次:2025-07-22 19:07:28'
2025-07-22 18:47:15 | DEBUG | 消息内容 '💕 [小爱]👩‍❤‍👨[锦岚（开玩笑就退群版）]💕
💓═☘︎═•洞💗房•═☘︎═💓
👩‍❤‍👨关系：情侣
⛺地点：阳台
😍姿势：隔山打牛
😮‍💨结果：失败
❤‍🔥状态：断裂
💓═☘︎══•💗•══☘︎═💓
🔮魅力:-39
[玫瑰]恩爱:-2 
🕒下次:2025-07-22 19:07:28' 不匹配任何命令，忽略
2025-07-22 18:47:17 | DEBUG | 收到消息: {'MsgId': 1955825155, 'FromUserName': {'string': '48097389945@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 1, 'Content': {'string': 'wxid_8l9ymg1mafud12:\n「小爱」[爱心]「锦岚（开玩笑就退群版）」\n地点：小汽车\n活动：双修\n结果：失败\n羞羞：声音太大引起交警注意\n恩爱值减少500\n\n下次:2025-07-22 18:57:27'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1753181247, 'MsgSource': '<msgsource>\n\t<silence>0</silence>\n\t<membercount>62</membercount>\n\t<signature>N0_V1_aKMzYlWg|v1_e+5HXb0o</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'PushContent': '不吃香菜\ue110 : 「小爱」[爱心]「锦岚（开玩笑就退群版）」\n地点：小汽车\n活动：双...', 'NewMsgId': 7407200613723050743, 'MsgSeq': 871392710}
2025-07-22 18:47:17 | INFO | 收到文本消息: 消息ID:1955825155 来自:48097389945@chatroom 发送人:wxid_8l9ymg1mafud12 @:[] 内容:「小爱」[爱心]「锦岚（开玩笑就退群版）」
地点：小汽车
活动：双修
结果：失败
羞羞：声音太大引起交警注意
恩爱值减少500

下次:2025-07-22 18:57:27
2025-07-22 18:47:18 | DEBUG | 处理消息内容: '「小爱」[爱心]「锦岚（开玩笑就退群版）」
地点：小汽车
活动：双修
结果：失败
羞羞：声音太大引起交警注意
恩爱值减少500

下次:2025-07-22 18:57:27'
2025-07-22 18:47:18 | DEBUG | 消息内容 '「小爱」[爱心]「锦岚（开玩笑就退群版）」
地点：小汽车
活动：双修
结果：失败
羞羞：声音太大引起交警注意
恩爱值减少500

下次:2025-07-22 18:57:27' 不匹配任何命令，忽略
2025-07-22 18:47:29 | DEBUG | 收到消息: {'MsgId': 985944833, 'FromUserName': {'string': '48097389945@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 1, 'Content': {'string': 'xiaomaochong:\n这会不行'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1753181262, 'MsgSource': '<msgsource>\n\t<pua>1</pua>\n\t<silence>0</silence>\n\t<membercount>62</membercount>\n\t<signature>N0_V1_AToJyLxq|v1_NrF1knd1</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'PushContent': '小爱 : 这会不行', 'NewMsgId': 3194654621982671091, 'MsgSeq': 871392711}
2025-07-22 18:47:29 | INFO | 收到文本消息: 消息ID:985944833 来自:48097389945@chatroom 发送人:xiaomaochong @:[] 内容:这会不行
2025-07-22 18:47:30 | DEBUG | 处理消息内容: '这会不行'
2025-07-22 18:47:30 | DEBUG | 消息内容 '这会不行' 不匹配任何命令，忽略
2025-07-22 18:47:42 | DEBUG | 收到消息: {'MsgId': 1768272848, 'FromUserName': {'string': '48097389945@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 1, 'Content': {'string': 'xiaomaochong:\n洞房老失败'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1753181275, 'MsgSource': '<msgsource>\n\t<pua>1</pua>\n\t<silence>0</silence>\n\t<membercount>62</membercount>\n\t<signature>N0_V1_guYG8YP/|v1_QjscujjH</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'PushContent': '小爱 : 洞房老失败', 'NewMsgId': 3973547890890462832, 'MsgSeq': 871392712}
2025-07-22 18:47:42 | INFO | 收到文本消息: 消息ID:1768272848 来自:48097389945@chatroom 发送人:xiaomaochong @:[] 内容:洞房老失败
2025-07-22 18:47:42 | DEBUG | 处理消息内容: '洞房老失败'
2025-07-22 18:47:42 | DEBUG | 消息内容 '洞房老失败' 不匹配任何命令，忽略
