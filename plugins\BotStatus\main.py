import re
import time
import tomllib
import random

from WechatAPI import WechatAPIClient
from database.keyvalDB import KeyvalDB
from utils.decorators import *
from utils.plugin_base import PluginBase


class BotStatus(PluginBase):
    description = "机器人状态"
    author = "HenryXiaoYang"
    version = "1.0.0"

    def __init__(self):
        super().__init__()
        self.db_key = "botstatus_start_time"  # 用于存储启动时间的键名
        self.start_time = None  # 初始化为None，稍后异步设置

        with open("plugins/BotStatus/config.toml", "rb") as f:
            plugin_config = tomllib.load(f)

        with open("main_config.toml", "rb") as f:
            main_config = tomllib.load(f)

        config = plugin_config["BotStatus"]
        main_config = main_config["XYBot"]

        self.enable = config["enable"]
        self.command = config["command"]
        self.reset_command = config.get("reset-command", ["重置状态", "重置"])
        self.natural_response = config.get("natural_response", True)
        self.version = main_config["version"]
        self.status_message = config["status-message"]

        # 初始化自然化回复词库
        self._init_natural_responses()

    def _init_natural_responses(self):
        """初始化自然化回复词库"""
        self.status_responses = [
            "我还活着呢", "在线中", "正常运行", "一切正常",
            "状态良好", "运行中", "没问题", "好着呢"
        ]

        self.uptime_prefixes = [
            "已经跑了", "运行了", "在线", "已经",
            "持续运行", "不间断运行了", "连续在线"
        ]

        self.reset_confirmations = [
            "好的，重新开始计时", "收到，已重置", "重置完成",
            "OK，重新计时", "已重置", "重新开始"
        ]

    async def async_init(self):
        """初始化插件，设置启动时间"""
        # 获取KeyvalDB实例
        keyval_db = KeyvalDB()

        # 尝试从数据库获取启动时间
        stored_time = await keyval_db.get(self.db_key)

        if stored_time is None:
            # 如果数据库中没有记录，则创建新记录
            self.start_time = time.time()
            await keyval_db.set(self.db_key, str(self.start_time))
        else:
            # 如果数据库中有记录，则使用该记录
            self.start_time = float(stored_time)

    async def _reset_status(self):
        """重置状态，重新开始计时"""
        keyval_db = KeyvalDB()
        self.start_time = time.time()
        await keyval_db.set(self.db_key, str(self.start_time))

    def _format_uptime(self):
        """格式化运行时间为易读的字符串"""
        # 确保start_time已初始化
        if self.start_time is None:
            return "正在计算中..."

        uptime = int(time.time() - self.start_time)
        days = uptime // (24 * 3600)
        hours = (uptime % (24 * 3600)) // 3600
        minutes = (uptime % 3600) // 60
        seconds = uptime % 60

        parts = []
        if days > 0:
            parts.append(f"{days}天")
        if hours > 0:
            parts.append(f"{hours}小时")
        if minutes > 0:
            parts.append(f"{minutes}分")
        if seconds > 0 or not parts:  # 如果没有其他时间单位，显示秒数
            parts.append(f"{seconds}秒")

        time_str = "".join(parts)

        if self.natural_response:
            prefix = random.choice(self.uptime_prefixes)
            return f"{prefix}{time_str}"
        else:
            return time_str

    @on_text_message
    async def handle_text(self, bot: WechatAPIClient, message: dict):
        if not self.enable:
            return

        content = str(message["Content"]).strip()
        command = content.split(" ")

        if not len(command):
            return

        # 检查是否是重置命令
        if command[0] in self.reset_command:
            await self._reset_status()
            if self.natural_response:
                confirmation = random.choice(self.reset_confirmations)
                await bot.send_text_message(message.get("FromWxid"), confirmation)
            else:
                await bot.send_text_message(message.get("FromWxid"), "状态已重置，重新开始计时")
            return

        # 检查是否是状态查询命令
        if command[0] not in self.command:
            return

        if self.natural_response:
            # 自然化回复
            status_msg = random.choice(self.status_responses)
            uptime_msg = self._format_uptime()
            out_message = f"{status_msg}，{uptime_msg}"
        else:
            # 传统回复
            out_message = (f"{self.status_message}\n"
                           f"当前版本: {self.version}\n"
                           f"运行时长: {self._format_uptime()}")

        await bot.send_text_message(message.get("FromWxid"), out_message)

    @on_at_message
    async def handle_at(self, bot: WechatAPIClient, message: dict):
        if not self.enable:
            return

        content = str(message["Content"]).strip()
        command = re.split(r'[\s\u2005]+', content)

        if len(command) < 2:
            return

        # 检查是否是重置命令
        if command[1] in self.reset_command:
            await self._reset_status()
            if self.natural_response:
                confirmation = random.choice(self.reset_confirmations)
                await bot.send_text_message(message.get("FromWxid"), confirmation)
            else:
                await bot.send_text_message(message.get("FromWxid"), "状态已重置，重新开始计时")
            return

        # 检查是否是状态查询命令
        if command[1] not in self.command:
            return

        if self.natural_response:
            # 自然化回复
            status_msg = random.choice(self.status_responses)
            uptime_msg = self._format_uptime()
            out_message = f"{status_msg}，{uptime_msg}"
        else:
            # 传统回复
            out_message = (f"{self.status_message}\n"
                           f"当前版本: {self.version}\n"
                           f"运行时长: {self._format_uptime()}")

        await bot.send_text_message(message.get("FromWxid"), out_message)
