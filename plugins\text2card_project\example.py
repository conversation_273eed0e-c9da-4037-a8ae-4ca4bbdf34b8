from text2card import CardGenerator
import os
import requests
from PIL import Image, ImageDraw

def create_sample_header():
    """创建示例头部图片"""
    width = 1000
    height = 300
    image = Image.new('RGB', (width, height), (210, 236, 255))
    draw = ImageDraw.Draw(image)
    
    # 添加渐变背景
    for y in range(height):
        r = int(210 + (255 - 210) * y / height)
        g = int(236 + (255 - 236) * y / height)
        b = 255
        draw.line([(0, y), (width, y)], fill=(r, g, b))
    
    # 保存图片
    image.save("header.jpg", quality=95)
    return "header.jpg"

def main():
    # 使用绝对路径
    fonts_dir = r"C:\NGCBot-master\text2card_project\fonts"
    
    # 验证字体文件是否存在
    required_fonts = ["msyh.ttc", "msyhbd.ttc"]
    for font in required_fonts:
        font_path = os.path.join(fonts_dir, font)
        if not os.path.exists(font_path):
            print(f"错误：找不到字体文件 {font_path}")
            return
        
    # 初始化生成器
    generator = CardGenerator(fonts_dir=fonts_dir)

    # 群聊摘要文本
    text = """**💥2025.01.09聊天内容摘要💥**

🌟**有趣互动**
- 🎈A奸笑奸笑 发起肯德基疯狂星期四的梗，引发一系列搞笑回应（全天）
  - 🤑希望获得50元支持，引发群体调侃
  - 🎭灰机 以搞笑方式回应"演的怎么了？"引发共鸣
  - 🎥A奸笑奸笑 再次以深情方式请求支持，群体活跃度高
- 🚀灰机 以高启强角色代入，增加逃亡与肯德基疯狂星期四的幽默元素

🔍**技术探讨**
- 💬讨论Java编程中遇到的错误，提及"java.lang.NoMoneyException"引发笑点（时间不详）
- 🤖介绍AI画图、对话、IP溯源等功能的机器人菜单（全天）

📊**数据统计与信息分享**
- 📢#FreeBuf早报 暂无文章，提及NGC660安全实验室整理分享（08:30:12）
- 🌐爱心 NGCBot菜单 展示积分功能与操作指南（全天）

🌈**其他亮点**
- 🐤多名群友以不同角色和情境参与肯德基疯狂星期四的梗，增加群聊趣味性
- 🎮提及游戏与云电脑性能讨论，引发技术相关问题的探讨

#聊天摘要 #20250109 #有趣话题
- 总消息数：195条
- 活跃时段：12小时
- 活跃成员：多位成员参与互动
- 类型分布：包含文字、表情包、话题讨论等多种形式"""

    try:
        # 生成卡片
        print("开始生成卡片...")
        card = generator.generate_card(
            text,
            width=1000,
            is_dark=False,
            title_font_size=36,      # 增大标题字体
            text_font_size=24,       # 增大正文字体
            line_spacing=1.2,        # 减小行间距
            padding=30,              # 调整内边距
            background_color=(210, 236, 255),  # 设置浅蓝色背景
            gradient=True,           # 启用渐变背景
            text_color=(0, 0, 0),    # 设置黑色字体
            title_color=(0, 0, 0)    # 标题也设置为黑色
        )
        output_path = "chat_summary.png"
        card.save(output_path)
        print(f"卡片已保存到: {output_path}")
        
    except Exception as e:
        print(f"生成卡片时出错: {e}")

if __name__ == "__main__":
    main()