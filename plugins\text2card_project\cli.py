import argparse
import sys
from datetime import datetime
from plugins.text2card_project.text_card_service import TextCardService
from plugins.text2card_project.configs.stylesheets import get_style, list_styles

def main():
    parser = argparse.ArgumentParser(description="将文本转换为精美卡片图像")
    parser.add_argument("text", nargs="?", help="要转换的文本内容，如果不提供则从标准输入读取")
    parser.add_argument("-o", "--output", help="输出文件路径，默认为当前目录")
    parser.add_argument("-w", "--width", type=int, help="图片宽度，默认800")
    parser.add_argument("-d", "--dark", action="store_true", help="使用深色主题")
    parser.add_argument("-s", "--style", choices=["light", "dark", "tech", "fancy"] + list_styles(), 
                        help="使用预定义样式，可选light/dark/tech/fancy或其他样式")
    parser.add_argument("-i", "--image", help="标题图片路径")
    parser.add_argument("-g", "--gradient", action="store_true", help="使用渐变背景")
    parser.add_argument("-t", "--tech", action="store_true", help="添加科技风格效果")
    parser.add_argument("--tech-style", choices=["modern", "matrix", "hologram", "cyberpunk"], 
                        default="modern", help="科技风格类型")
    
    # 新增命令行参数
    parser.add_argument("--border-style", choices=["simple", "gradient", "double", "neon", "stripe", "glow"], 
                        help="边框样式")
    parser.add_argument("--border-color", help="边框颜色，十六进制格式，如 #FF5733")
    parser.add_argument("--border-accent", help="边框辅助颜色，用于渐变、双层等效果")
    parser.add_argument("--fancy", action="store_true", help="使用精美边框效果")
    parser.add_argument("--fancy-style", choices=["neon", "gradient", "double", "glow", "stripe"], 
                        default="neon", help="精美边框风格类型")
    parser.add_argument("--theme-color", help="主题颜色，将自动调整标题和边框颜色")
    
    args = parser.parse_args()
    
    # 处理文本输入
    text = args.text
    if not text:
        # 从stdin读取
        text = "".join(sys.stdin.readlines())
    
    if not text:
        parser.print_help()
        sys.exit(1)
    
    # 处理输出路径
    output_path = args.output
    if not output_path:
        # 生成默认输出路径
        timestamp = datetime.now().strftime("%Y%m%d%H%M%S")
        output_path = f"text_card_{timestamp}.png"
    
    # 设置卡片服务
    service = TextCardService()
    
    # 处理样式选择
    params = {}
    
    # 处理预定义样式
    if args.style:
        if args.style == "light":
            pass  # 默认就是light
        elif args.style == "dark":
            params["is_dark"] = True
        elif args.style == "tech":
            # 使用generate_tech_card代替
            tech_style = args.tech_style or "modern"
            output_file = service.generate_tech_card(text, output_path, tech_style)
            print(f"科技风格卡片已生成: {output_file}")
            return
        elif args.style == "fancy":
            # 使用generate_fancy_border_card代替
            fancy_style = args.fancy_style or "neon"
            theme_color = args.theme_color
            output_file = service.generate_fancy_border_card(text, output_path, fancy_style, theme_color)
            print(f"精美边框卡片已生成: {output_file}")
            return
        else:
            # 使用指定的预定义样式
            style_dict = get_style(args.style)
            params.update(style_dict)
    
    # 处理单独的样式参数，这些会覆盖预定义样式
    if args.width:
        params["width"] = args.width
    
    if args.dark:
        params["is_dark"] = True
    
    if args.image:
        params["title_image"] = args.image
    
    if args.gradient:
        params["gradient"] = True
    
    if args.tech:
        # 使用generate_tech_card代替
        tech_style = args.tech_style or "modern"
        output_file = service.generate_tech_card(text, output_path, tech_style)
        print(f"科技风格卡片已生成: {output_file}")
        return
    
    # 处理新增的边框样式参数
    if args.border_style:
        params["border_style"] = args.border_style
    
    if args.border_color:
        params["border_color"] = args.border_color
        
    if args.border_accent:
        params["border_accent_color"] = args.border_accent
    
    if args.fancy:
        # 使用generate_fancy_border_card代替
        fancy_style = args.fancy_style or "neon"
        theme_color = args.theme_color
        output_file = service.generate_fancy_border_card(text, output_path, fancy_style, theme_color)
        print(f"精美边框卡片已生成: {output_file}")
        return
    
    if args.theme_color:
        # 如果设置了主题颜色，自动设置标题和边框颜色
        params["title_color"] = args.theme_color
        params["border_color"] = args.theme_color
    
    # 生成卡片
    output_file = service.generate_card(text, output_path, **params)
    print(f"卡片已生成: {output_file}") 