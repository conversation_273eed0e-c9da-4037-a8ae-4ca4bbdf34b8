import os
import asyncio
import time
import random
import subprocess
import glob
import platform
from pathlib import Path
try:
    import tomllib
except ModuleNotFoundError:
    import tomli as tomllib
from loguru import logger
from WechatAPI import WechatAPIClient
from utils.decorators import on_text_message, schedule
from utils.plugin_base import PluginBase


class AISignIn(PluginBase):
    description = "AI网站自动签到插件 - 自动访问各类AI网站保持账号活跃状态"
    author = "XYBot"
    version = "2.0.0"
    plugin_name = "AISignIn"

    def __init__(self):
        super().__init__()
        self.temp_dir = Path("plugins/AISignIn/temp")
        self.temp_dir.mkdir(parents=True, exist_ok=True)
        self._load_config()
        self.user_last_request = {}
        self._init_natural_responses()
        self._is_signing_in = False  # 防止重复执行的锁

    def _load_config(self):
        try:
            with open(f"plugins/{self.plugin_name}/config.toml", "rb") as f:
                config = tomllib.load(f).get(self.plugin_name, {})
        except:
            config = {}

        self.enable = config.get("enable", True)
        self.command = config.get("command", ["AI签到", "签到"])
        self.command_format = config.get("command-format", "AI签到 - AI网站自动签到")
        self.cooldown = config.get("rate_limit", {}).get("cooldown", 30)
        self.natural_response = config.get("natural_response", True)
        
        # Chrome设置
        chrome_settings = config.get("chrome_settings", {})
        # 支持多个URL配置，向后兼容单个URL
        if "urls" in chrome_settings:
            self.urls = chrome_settings.get("urls", [
                "https://www.doubao.com/chat/4498442627796994",
                "https://jimeng.jianying.com/ai-tool/home"
            ])
        else:
            # 向后兼容旧配置
            self.urls = [chrome_settings.get("url", "https://www.doubao.com/chat/4498442627796994")]

        self.duration = chrome_settings.get("duration", 60)
        self.window_size = chrome_settings.get("window_size", "1920,1080")
        self.interval_between_sites = chrome_settings.get("interval_between_sites", 5)

        # 定时签到设置
        auto_signin_settings = config.get("auto_signin", {})
        self.auto_signin_enable = auto_signin_settings.get("enable", True)
        self.auto_signin_hour = auto_signin_settings.get("hour", 0)
        self.auto_signin_minute = auto_signin_settings.get("minute", 1)
        self.auto_signin_notify = auto_signin_settings.get("notify", False)
        self.auto_signin_notify_groups = auto_signin_settings.get("notify_groups", [])

    def _init_natural_responses(self):
        """初始化自然化回复词库"""
        self.confirm_msgs = [
            "好的", "收到", "明白了", "嗯", "知道了", "行", "OK", "开始签到"
        ]

        self.error_msgs = [
            "这可把我难住了", "搞不定了", "这个有点难", "弄不了",
            "出问题了", "搞不出来", "这个不行"
        ]

        self.rate_limit_msgs = [
            "你搁这刷bug呢", "慢点慢点", "别这么急", "一个一个来",
            "太快了太快了", "歇会儿", "别刷了", "刚签到过，等等再说"
        ]

    async def on_enable(self, bot=None):
        """插件启用时调用，动态设置定时任务时间"""
        await super().on_enable(bot)

        if self.auto_signin_enable:
            try:
                # 获取定时任务的ID
                job_id = f"{self.__class__.__module__}.{self.__class__.__name__}.auto_signin_task"

                # 使用APScheduler的reschedule_job方法修改任务时间
                from utils.decorators import scheduler
                scheduler.reschedule_job(
                    job_id=job_id,
                    trigger='cron',
                    hour=self.auto_signin_hour,
                    minute=self.auto_signin_minute
                )
            except Exception as e:
                logger.error(f"[{self.plugin_name}] 设置定时任务时间失败: {e}")

    async def _simple_confirm(self, bot, wxid):
        """简单确认回复，不艾特人"""
        if self.natural_response:
            confirm_msg = random.choice(self.confirm_msgs)
            await bot.send_text_message(wxid, confirm_msg)

    def _find_chrome(self):
        """查找Chrome浏览器"""
        if platform.system() == "Windows":
            # Windows常见路径
            paths = [
                r"C:\Program Files\Google\Chrome\Application\chrome.exe",
                r"C:\Program Files (x86)\Google\Chrome\Application\chrome.exe",
                os.path.expanduser(r"~\AppData\Local\Google\Chrome\Application\chrome.exe")
            ]
            
            # 检查用户目录下的Chrome
            try:
                user_paths = glob.glob(r"C:\Users\<USER>\AppData\Local\Google\Chrome\Application\chrome.exe")
                paths.extend(user_paths)
            except:
                pass
            
            for path in paths:
                if os.path.isfile(path):
                    return path
            
            # 尝试注册表查找
            try:
                import winreg
                key = winreg.OpenKey(winreg.HKEY_LOCAL_MACHINE, 
                                   r"SOFTWARE\Microsoft\Windows\CurrentVersion\App Paths\chrome.exe")
                chrome_path = winreg.QueryValue(key, "")
                winreg.CloseKey(key)
                if os.path.isfile(chrome_path):
                    return chrome_path
            except:
                pass
        else:
            # Linux/Mac路径
            paths = [
                "/usr/bin/google-chrome",
                "/usr/bin/chromium-browser",
                "/Applications/Google Chrome.app/Contents/MacOS/Google Chrome",
                "/opt/google/chrome/chrome"
            ]
            
            for path in paths:
                if os.path.isfile(path):
                    return path
        
        return None

    def _get_chrome_user_data_dir(self):
        """获取Chrome默认用户数据目录"""
        if platform.system() == "Windows":
            possible_dirs = [
                os.path.expanduser(r"~\AppData\Local\Google\Chrome\User Data"),
                os.path.expanduser(r"~\AppData\Roaming\Google\Chrome\User Data"),
                r"C:\Users\<USER>\AppData\Local\Google\Chrome\User Data".format(os.getenv('USERNAME', '')),
            ]
        else:
            # Linux/Mac
            possible_dirs = [
                os.path.expanduser("~/.config/google-chrome"),
                os.path.expanduser("~/Library/Application Support/Google/Chrome"),
                os.path.expanduser("~/.config/chromium")
            ]
        
        for user_data_dir in possible_dirs:
            if os.path.exists(user_data_dir):
                # 检查是否有Default配置文件
                default_profile = os.path.join(user_data_dir, "Default")
                if os.path.exists(default_profile):
                    return user_data_dir
        
        logger.warning(f"[{self.plugin_name}] 未找到Chrome用户数据目录")
        return None

    async def _run_chrome_signin(self):
        """运行Chrome签到，依次访问多个AI网站"""
        try:
            # 查找Chrome
            chrome_path = self._find_chrome()
            if not chrome_path:
                raise Exception("未找到Chrome浏览器，请确保已安装Google Chrome")

            # 获取默认用户数据目录
            user_data_dir = self._get_chrome_user_data_dir()

            success_count = 0
            total_sites = len(self.urls)

            for i, url in enumerate(self.urls, 1):
                try:

                    # Chrome启动参数
                    args = [
                        chrome_path,
                        "--no-first-run",
                        "--no-default-browser-check",
                        f"--window-size={self.window_size}",
                        url
                    ]

                    # 配置用户数据目录
                    if user_data_dir:
                        args.append(f"--user-data-dir={user_data_dir}")
                    else:
                        logger.warning(f"[{self.plugin_name}] 未找到默认配置文件，可能需要重新登录")

                    # 启动Chrome
                    if platform.system() == "Windows":
                        process = subprocess.Popen(
                            args,
                            creationflags=subprocess.CREATE_NEW_PROCESS_GROUP,
                            stdout=subprocess.DEVNULL,
                            stderr=subprocess.DEVNULL
                        )
                    else:
                        process = subprocess.Popen(
                            args,
                            stdout=subprocess.DEVNULL,
                            stderr=subprocess.DEVNULL
                        )

                    # 等待指定时间
                    await asyncio.sleep(self.duration)

                    # 关闭Chrome
                    await self._close_chrome_process(process)

                    success_count += 1

                    # 如果不是最后一个网站，等待间隔时间
                    if i < total_sites:
                        await asyncio.sleep(self.interval_between_sites)

                except Exception as e:
                    logger.error(f"[{self.plugin_name}] 访问第 {i} 个网站失败: {e}")
                    # 尝试关闭可能残留的进程
                    try:
                        if 'process' in locals():
                            await self._close_chrome_process(process)
                    except:
                        pass

            return success_count > 0  # 只要有一个网站访问成功就算成功

        except Exception as e:
            logger.error(f"[{self.plugin_name}] Chrome签到失败: {e}")
            return False

    async def _close_chrome_process(self, process):
        """关闭Chrome进程"""
        try:
            # 尝试优雅关闭
            process.terminate()
            await asyncio.sleep(5)  # 等待5秒

            if process.poll() is None:
                # 强制关闭
                process.kill()
        except:
            # 使用系统命令强制关闭所有Chrome进程
            try:
                if platform.system() == "Windows":
                    subprocess.run(['taskkill', '/F', '/IM', 'chrome.exe'],
                                 capture_output=True, check=False)
                else:
                    subprocess.run(['pkill', '-f', 'chrome'],
                                 capture_output=True, check=False)
            except:
                logger.warning(f"[{self.plugin_name}] Chrome关闭可能失败，请手动检查")

    async def _send_signin_notification(self, bot: WechatAPIClient, is_auto=False):
        """发送签到通知"""
        if not self.auto_signin_notify or not self.auto_signin_notify_groups:
            return

        try:
            signin_type = "定时自动签到" if is_auto else "手动签到"
            site_count = len(self.urls)
            notify_msg = f"🎯 AI网站{signin_type}已完成\n📊 访问了 {site_count} 个网站：\n• 豆包AI对话\n• 剪映智能创作"

            for group_id in self.auto_signin_notify_groups:
                try:
                    await bot.send_text_message(group_id, notify_msg)
                except Exception as e:
                    logger.error(f"[{self.plugin_name}] 向群 {group_id} 发送通知失败: {e}")
        except Exception as e:
            logger.error(f"[{self.plugin_name}] 发送签到通知失败: {e}")

    @schedule('cron', hour=0, minute=1)  # 默认每天0点01分执行，将在on_enable中动态修改
    async def auto_signin_task(self, bot: WechatAPIClient):
        """定时自动签到任务"""
        if not self.enable or not self.auto_signin_enable:
            return

        # 防止重复执行
        if self._is_signing_in:
            return

        try:
            self._is_signing_in = True

            # 执行签到操作
            success = await self._run_chrome_signin()

            if success:
                # 发送通知（如果启用）
                await self._send_signin_notification(bot, is_auto=True)
            else:
                logger.error(f"[{self.plugin_name}] 定时自动签到失败")

        except Exception as e:
            logger.error(f"[{self.plugin_name}] 定时自动签到异常: {e}")
        finally:
            self._is_signing_in = False

    async def _manual_signin_task(self, bot: WechatAPIClient, wxid: str):
        """手动签到任务"""
        try:
            self._is_signing_in = True

            # 执行签到操作
            success = await self._run_chrome_signin()

            if success:
                # 发送通知（如果启用）
                await self._send_signin_notification(bot, is_auto=False)
            else:
                logger.error(f"[{self.plugin_name}] 手动签到失败")

        except Exception as e:
            logger.error(f"[{self.plugin_name}] 手动签到异常: {e}")
        finally:
            self._is_signing_in = False

    @on_text_message
    async def handle_text(self, bot: WechatAPIClient, message: dict):
        if not self.enable:
            return

        content = str(message["Content"]).strip()
        wxid = message["FromWxid"]
        user_wxid = message["SenderWxid"]

        command = content.split(" ", 1)
        if command[0] not in self.command:
            return

        # 如果只是发送命令，显示使用说明
        if len(command) == 1 and content in self.command:
            # 限流检查
            wait_time = self._check_user_limit(wxid, user_wxid)
            if wait_time > 0:
                if self.natural_response:
                    rate_limit_msg = random.choice(self.rate_limit_msgs)
                    await bot.send_text_message(wxid, rate_limit_msg)
                else:
                    await bot.send_at_message(wxid, f"请等待 {wait_time:.1f} 秒", [user_wxid])
                return

            try:
                # 检查是否正在签到
                if self._is_signing_in:
                    if self.natural_response:
                        busy_msg = "正在签到中，请稍等"
                        await bot.send_text_message(wxid, busy_msg)
                    else:
                        await bot.send_at_message(wxid, "签到任务正在进行中，请稍等", [user_wxid])
                    return

                # 简单确认
                await self._simple_confirm(bot, wxid)

                # 异步执行签到操作，不等待结果
                asyncio.create_task(self._manual_signin_task(bot, wxid))

            except Exception as e:
                logger.error(f"[{self.plugin_name}] 异常: {e}")
                if self.natural_response:
                    error_msg = random.choice(self.error_msgs)
                    await bot.send_text_message(wxid, error_msg)
                else:
                    await bot.send_at_message(wxid, "处理失败", [user_wxid])

    def _check_user_limit(self, wxid: str, user_wxid: str) -> float:
        user_key = f"{wxid}_{user_wxid}"
        last_request = self.user_last_request.get(user_key, 0)
        current_time = time.time()
        elapsed = current_time - last_request
        self.user_last_request[user_key] = current_time

        if elapsed < self.cooldown:
            return self.cooldown - elapsed

        return 0
