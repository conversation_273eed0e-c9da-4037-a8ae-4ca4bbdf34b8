2025-07-29 00:01:00 | DEBUG | 收到消息: {'MsgId': 294232316, 'FromUserName': {'string': '***********@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 1, 'Content': {'string': 'wxid_ikxxrwasicud11:\n掐的真准'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1753718467, 'MsgSource': '<msgsource>\n\t<sec_msg_node>\n\t\t<alnode>\n\t\t\t<fr>1</fr>\n\t\t</alnode>\n\t</sec_msg_node>\n\t<pua>1</pua>\n\t<silence>1</silence>\n\t<membercount>144</membercount>\n\t<signature>N0_V1_NX3a+H4H|v1_QWlk1j1D</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'NewMsgId': 3573632587759504405, 'MsgSeq': 871407187}
2025-07-29 00:01:00 | INFO | 收到文本消息: 消息ID:294232316 来自:***********@chatroom 发送人:wxid_ikxxrwasicud11 @:[] 内容:掐的真准
2025-07-29 00:01:00 | DEBUG | 处理消息内容: '掐的真准'
2025-07-29 00:01:00 | DEBUG | 消息内容 '掐的真准' 不匹配任何命令，忽略
2025-07-29 00:01:10 | DEBUG | 收到消息: {'MsgId': 536624012, 'FromUserName': {'string': '***********@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 1, 'Content': {'string': 'wxid_ugv5ryus4gz622:\n打了12个号'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': **********, 'MsgSource': '<msgsource>\n\t<bizflag>0</bizflag>\n\t<pua>1</pua>\n\t<eggIncluded>1</eggIncluded>\n\t<silence>1</silence>\n\t<membercount>144</membercount>\n\t<signature>N0_V1_V7h/nhsp|v1_veMNZ+we</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'NewMsgId': 5280666386436737288, 'MsgSeq': 871407188}
2025-07-29 00:01:10 | INFO | 收到文本消息: 消息ID:536624012 来自:***********@chatroom 发送人:wxid_ugv5ryus4gz622 @:[] 内容:打了12个号
2025-07-29 00:01:10 | DEBUG | 处理消息内容: '打了12个号'
2025-07-29 00:01:10 | DEBUG | 消息内容 '打了12个号' 不匹配任何命令，忽略
2025-07-29 00:01:13 | DEBUG | 收到消息: {'MsgId': 911111313, 'FromUserName': {'string': '***********@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 47, 'Content': {'string': 'wxid_snv13qf05qjx11:\n<msg><emoji fromusername = "wxid_snv13qf05qjx11" tousername = "***********@chatroom" type="2" idbuffer="media:0_0" md5="c52a41cbdb62a7a19387486ca32f4337" len = "679860" productid="" androidmd5="c52a41cbdb62a7a19387486ca32f4337" androidlen="679860" s60v3md5 = "c52a41cbdb62a7a19387486ca32f4337" s60v3len="679860" s60v5md5 = "c52a41cbdb62a7a19387486ca32f4337" s60v5len="679860" cdnurl = "http://vweixinf.tc.qq.com/110/20401/stodownload?m=c52a41cbdb62a7a19387486ca32f4337&amp;filekey=30440201010430302e02016e0402535a0420633532613431636264623632613761313933383734383663613332663433333702030a5fb4040d00000004627466730000000132&amp;hy=SZ&amp;storeid=263637a3a0008c4682691c3a50000006e01004fb1535a0749f910b6ba12b11&amp;ef=1&amp;bizid=1022" designerid = "" thumburl = "" encrypturl = "http://vweixinf.tc.qq.com/110/20402/stodownload?m=89c6ba67659e8e5f7098a46b5aa99f6e&amp;filekey=30440201010430302e02016e0402535a0420383963366261363736353965386535663730393861343662356161393966366502030a5fc0040d00000004627466730000000132&amp;hy=SZ&amp;storeid=263637a3a000a10bd2691c3a50000006e02004fb2535a0749f910b6ba12b26&amp;ef=2&amp;bizid=1022" aeskey= "afa24110b4ed42f8ab3b0b97d334dc9e" externurl = "http://vweixinf.tc.qq.com/110/20403/stodownload?m=d64fbca00740d064b99cc54f23a8664e&amp;filekey=30440201010430302e02016e0402535a04206436346662636130303734306430363462393963633534663233613836363465020300a930040d00000004627466730000000132&amp;hy=SZ&amp;storeid=263637a3a000b897d2691c3a50000006e03004fb3535a0749f910b6ba12b43&amp;ef=3&amp;bizid=1022" externmd5 = "6600823ebd91020e3ee3af1ca5cd4de8" width= "312" height= "312" tpurl= "" tpauthkey= "" attachedtext= "" attachedtextcolor= "" lensid= "" emojiattr= "" linkid= "" desc= "" ></emoji> <gameext type="0" content="0" ></gameext> </msg>'}, 'Status': 3, 'ImgStatus': 2, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1753718478, 'MsgSource': '<msgsource>\n\t<silence>1</silence>\n\t<membercount>144</membercount>\n\t<signature>N0_V1_zHkpOlbU|v1_YYpKASjf</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'NewMsgId': 9214812115341583231, 'MsgSeq': 871407189}
2025-07-29 00:01:13 | INFO | 收到表情消息: 消息ID:911111313 来自:***********@chatroom 发送人:wxid_snv13qf05qjx11 MD5:c52a41cbdb62a7a19387486ca32f4337 大小:679860
2025-07-29 00:01:13 | DEBUG | [DoubaoImageRecognition] 缓存表情消息: 9214812115341583231
2025-07-29 00:01:21 | DEBUG | 收到消息: {'MsgId': 1549511037, 'FromUserName': {'string': '***********@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 1, 'Content': {'string': 'wxid_ugv5ryus4gz622:\n72局'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1753718488, 'MsgSource': '<msgsource>\n\t<bizflag>0</bizflag>\n\t<pua>1</pua>\n\t<eggIncluded>1</eggIncluded>\n\t<silence>1</silence>\n\t<membercount>144</membercount>\n\t<signature>N0_V1_toTxiyfQ|v1_Rf77XK8/</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'NewMsgId': 1282258979997206016, 'MsgSeq': 871407190}
2025-07-29 00:01:21 | INFO | 收到文本消息: 消息ID:1549511037 来自:***********@chatroom 发送人:wxid_ugv5ryus4gz622 @:[] 内容:72局
2025-07-29 00:01:21 | DEBUG | 处理消息内容: '72局'
2025-07-29 00:01:21 | DEBUG | 消息内容 '72局' 不匹配任何命令，忽略
2025-07-29 00:01:36 | DEBUG | 收到消息: {'MsgId': 202510858, 'FromUserName': {'string': '***********@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 47, 'Content': {'string': 'wxid_idzryo4rneok22:\n<msg><emoji fromusername="wxid_idzryo4rneok22" tousername="***********@chatroom" type="2" idbuffer="media:0_0" md5="06a38377ba1ab30149fda754ed2d442c" len="122302" productid="" androidmd5="06a38377ba1ab30149fda754ed2d442c" androidlen="122302" s60v3md5="06a38377ba1ab30149fda754ed2d442c" s60v3len="122302" s60v5md5="06a38377ba1ab30149fda754ed2d442c" s60v5len="122302" cdnurl="http://wxapp.tc.qq.com/262/20304/stodownload?m=06a38377ba1ab30149fda754ed2d442c&amp;filekey=30350201010421301f0202010604025348041006a38377ba1ab30149fda754ed2d442c020301ddbe040d00000004627466730000000131&amp;hy=SH&amp;storeid=32303232303831363230313632383030303234346265303030303030303065303564393930623030303030313036&amp;bizid=1023" designerid="" thumburl="" encrypturl="http://wxapp.tc.qq.com/262/20304/stodownload?m=c8cbbf3234befe308d960e1bfe2e5091&amp;filekey=30350201010421301f02020106040253480410c8cbbf3234befe308d960e1bfe2e5091020301ddc0040d00000004627466730000000131&amp;hy=SH&amp;storeid=32303232303831363230313632383030303731663531303030303030303037636432393630393030303030313036&amp;bizid=1023" aeskey="dd6a1ff656fc1cc201175153d299b0f8" externurl="http://wxapp.tc.qq.com/262/20304/stodownload?m=c4f63a7c1dae069a4714b7e324b9447c&amp;filekey=30340201010420301e02020106040253480410c4f63a7c1dae069a4714b7e324b9447c02027fb0040d00000004627466730000000131&amp;hy=SH&amp;storeid=32303232303831363230313632383030306161386135303030303030303065396464613030623030303030313036&amp;bizid=1023" externmd5="d2e1a4d2fb61369efe921c4a25967ad4" width="200" height="146" tpurl="" tpauthkey="" attachedtext="" attachedtextcolor="" lensid="" emojiattr="" linkid="" desc=""></emoji></msg>'}, 'Status': 3, 'ImgStatus': 2, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1753718503, 'MsgSource': '<msgsource>\n\t<silence>1</silence>\n\t<membercount>144</membercount>\n\t<signature>N0_V1_PRK9uKS/|v1_h28zMO9m</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'NewMsgId': 2364469209657545768, 'MsgSeq': 871407191}
2025-07-29 00:01:36 | INFO | 收到表情消息: 消息ID:202510858 来自:***********@chatroom 发送人:wxid_idzryo4rneok22 MD5:06a38377ba1ab30149fda754ed2d442c 大小:122302
2025-07-29 00:01:36 | DEBUG | [DoubaoImageRecognition] 缓存表情消息: 2364469209657545768
2025-07-29 00:01:48 | DEBUG | 收到消息: {'MsgId': 874458508, 'FromUserName': {'string': '***********@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 1, 'Content': {'string': 'wxid_idzryo4rneok22:\n牛逼'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1753718515, 'MsgSource': '<msgsource>\n\t<pua>1</pua>\n\t<eggIncluded>1</eggIncluded>\n\t<silence>1</silence>\n\t<membercount>144</membercount>\n\t<signature>N0_V1_pLPuu9Ca|v1_K9uAw6+8</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'NewMsgId': 8669545204036310486, 'MsgSeq': 871407192}
2025-07-29 00:01:48 | INFO | 收到文本消息: 消息ID:874458508 来自:***********@chatroom 发送人:wxid_idzryo4rneok22 @:[] 内容:牛逼
2025-07-29 00:01:48 | DEBUG | 处理消息内容: '牛逼'
2025-07-29 00:01:48 | DEBUG | 消息内容 '牛逼' 不匹配任何命令，忽略
2025-07-29 00:02:03 | DEBUG | 收到消息: {'MsgId': 649416959, 'FromUserName': {'string': '***********@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 3, 'Content': {'string': 'wxid_ugv5ryus4gz622:\n<?xml version="1.0"?>\n<msg>\n\t<img aeskey="9d295c17eba3ec6237e3b16e51262bd9" encryver="1" cdnthumbaeskey="9d295c17eba3ec6237e3b16e51262bd9" cdnthumburl="3057020100044b30490201000204524af8fe02032f51ae0204d2b15f7c020468879f01042438636130636661622d653166352d346130382d386236652d396232353433653537336134020405292a010201000405004c537600" cdnthumblength="2918" cdnthumbheight="88" cdnthumbwidth="120" cdnmidheight="0" cdnmidwidth="0" cdnhdheight="0" cdnhdwidth="0" cdnmidimgurl="3057020100044b30490201000204524af8fe02032f51ae0204d2b15f7c020468879f01042438636130636661622d653166352d346130382d386236652d396232353433653537336134020405292a010201000405004c537600" length="25511" cdnbigimgurl="3057020100044b30490201000204524af8fe02032f51ae0204d2b15f7c020468879f01042438636130636661622d653166352d346130382d386236652d396232353433653537336134020405292a010201000405004c537600" hdlength="92177" md5="f7e3853b734b38773fdb93b50dcf7131" hevc_mid_size="25511">\n\t\t<secHashInfoBase64>eyJwaGFzaCI6ImQzYjEzMTIwMjAwMDIwMDAiLCJwZHFoYXNoIjoiNzkwYTEyYjViYjYzYmZlMWZmNDI3ZjI0YmFlOTVkMTYxNmQ2MDA2ZjBhNmJhZDk2Zjc5MDVkOTQwODI5MDA2YiJ9</secHashInfoBase64>\n\t\t<live>\n\t\t\t<duration>0</duration>\n\t\t\t<size>0</size>\n\t\t\t<md5 />\n\t\t\t<fileid />\n\t\t\t<hdsize>0</hdsize>\n\t\t\t<hdmd5 />\n\t\t\t<hdfileid />\n\t\t\t<stillimagetimems>0</stillimagetimems>\n\t\t</live>\n\t</img>\n\t<platform_signature />\n\t<imgdatahash />\n\t<ImgSourceInfo>\n\t\t<ImgSourceUrl />\n\t\t<BizType>0</BizType>\n\t</ImgSourceInfo>\n</msg>\n'}, 'Status': 3, 'ImgStatus': 2, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1753718530, 'MsgSource': '<msgsource>\n\t<bizflag>0</bizflag>\n\t<mediaeditcontent />\n\t<sec_msg_node>\n\t\t<uuid>01eefcd86502ac152be0acad5e8393ad_</uuid>\n\t\t<risk-file-flag />\n\t\t<risk-file-md5-list />\n\t</sec_msg_node>\n\t<silence>1</silence>\n\t<membercount>144</membercount>\n\t<signature>N0_V1_8uMQ744l|v1_Ax0IvZVy</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'NewMsgId': 2467043224644360791, 'MsgSeq': 871407193}
2025-07-29 00:02:03 | INFO | 收到图片消息: 消息ID:649416959 来自:***********@chatroom 发送人:wxid_ugv5ryus4gz622 XML:<?xml version="1.0"?><msg><img aeskey="9d295c17eba3ec6237e3b16e51262bd9" encryver="1" cdnthumbaeskey="9d295c17eba3ec6237e3b16e51262bd9" cdnthumburl="3057020100044b30490201000204524af8fe02032f51ae0204d2b15f7c020468879f01042438636130636661622d653166352d346130382d386236652d396232353433653537336134020405292a010201000405004c537600" cdnthumblength="2918" cdnthumbheight="88" cdnthumbwidth="120" cdnmidheight="0" cdnmidwidth="0" cdnhdheight="0" cdnhdwidth="0" cdnmidimgurl="3057020100044b30490201000204524af8fe02032f51ae0204d2b15f7c020468879f01042438636130636661622d653166352d346130382d386236652d396232353433653537336134020405292a010201000405004c537600" length="25511" cdnbigimgurl="3057020100044b30490201000204524af8fe02032f51ae0204d2b15f7c020468879f01042438636130636661622d653166352d346130382d386236652d396232353433653537336134020405292a010201000405004c537600" hdlength="92177" md5="f7e3853b734b38773fdb93b50dcf7131" hevc_mid_size="25511"><secHashInfoBase64>eyJwaGFzaCI6ImQzYjEzMTIwMjAwMDIwMDAiLCJwZHFoYXNoIjoiNzkwYTEyYjViYjYzYmZlMWZmNDI3ZjI0YmFlOTVkMTYxNmQ2MDA2ZjBhNmJhZDk2Zjc5MDVkOTQwODI5MDA2YiJ9</secHashInfoBase64><live><duration>0</duration><size>0</size><md5 /><fileid /><hdsize>0</hdsize><hdmd5 /><hdfileid /><stillimagetimems>0</stillimagetimems></live></img><platform_signature /><imgdatahash /><ImgSourceInfo><ImgSourceUrl /><BizType>0</BizType></ImgSourceInfo></msg>
2025-07-29 00:02:03 | INFO | [ImageEcho] 保存图片信息成功，当前群 ***********@chatroom 已存储 5 张图片
2025-07-29 00:02:03 | INFO | [TimerTask] 缓存图片消息: 649416959
2025-07-29 00:02:13 | DEBUG | 收到消息: {'MsgId': 199146873, 'FromUserName': {'string': '***********@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 1, 'Content': {'string': 'wxid_ugv5ryus4gz622:\n预言家1号和2号'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1753718540, 'MsgSource': '<msgsource>\n\t<bizflag>0</bizflag>\n\t<pua>1</pua>\n\t<eggIncluded>1</eggIncluded>\n\t<silence>1</silence>\n\t<membercount>144</membercount>\n\t<signature>N0_V1_BUB1f3mR|v1_Cy4yrzlr</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'NewMsgId': 6646295159434835232, 'MsgSeq': 871407194}
2025-07-29 00:02:13 | INFO | 收到文本消息: 消息ID:199146873 来自:***********@chatroom 发送人:wxid_ugv5ryus4gz622 @:[] 内容:预言家1号和2号
2025-07-29 00:02:13 | DEBUG | 处理消息内容: '预言家1号和2号'
2025-07-29 00:02:13 | DEBUG | 消息内容 '预言家1号和2号' 不匹配任何命令，忽略
2025-07-29 00:02:17 | DEBUG | 收到消息: {'MsgId': 742486825, 'FromUserName': {'string': '***********@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 1, 'Content': {'string': 'wxid_ikxxrwasicud11:\n你们太持久'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1753718544, 'MsgSource': '<msgsource>\n\t<sec_msg_node>\n\t\t<alnode>\n\t\t\t<fr>1</fr>\n\t\t</alnode>\n\t</sec_msg_node>\n\t<pua>1</pua>\n\t<silence>1</silence>\n\t<membercount>144</membercount>\n\t<signature>N0_V1_bPP30hIk|v1_u8Ih6cQH</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'NewMsgId': 4592359537147619535, 'MsgSeq': 871407195}
2025-07-29 00:02:17 | INFO | 收到文本消息: 消息ID:742486825 来自:***********@chatroom 发送人:wxid_ikxxrwasicud11 @:[] 内容:你们太持久
2025-07-29 00:02:17 | DEBUG | 处理消息内容: '你们太持久'
2025-07-29 00:02:17 | DEBUG | 消息内容 '你们太持久' 不匹配任何命令，忽略
2025-07-29 00:02:23 | DEBUG | 收到消息: {'MsgId': 336708820, 'FromUserName': {'string': '***********@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 47, 'Content': {'string': 'wxid_idzryo4rneok22:\n<msg><emoji fromusername="wxid_idzryo4rneok22" tousername="***********@chatroom" type="2" idbuffer="media:0_0" md5="06a38377ba1ab30149fda754ed2d442c" len="122302" productid="" androidmd5="06a38377ba1ab30149fda754ed2d442c" androidlen="122302" s60v3md5="06a38377ba1ab30149fda754ed2d442c" s60v3len="122302" s60v5md5="06a38377ba1ab30149fda754ed2d442c" s60v5len="122302" cdnurl="http://wxapp.tc.qq.com/262/20304/stodownload?m=06a38377ba1ab30149fda754ed2d442c&amp;filekey=30350201010421301f0202010604025348041006a38377ba1ab30149fda754ed2d442c020301ddbe040d00000004627466730000000131&amp;hy=SH&amp;storeid=32303232303831363230313632383030303234346265303030303030303065303564393930623030303030313036&amp;bizid=1023" designerid="" thumburl="" encrypturl="http://wxapp.tc.qq.com/262/20304/stodownload?m=c8cbbf3234befe308d960e1bfe2e5091&amp;filekey=30350201010421301f02020106040253480410c8cbbf3234befe308d960e1bfe2e5091020301ddc0040d00000004627466730000000131&amp;hy=SH&amp;storeid=32303232303831363230313632383030303731663531303030303030303037636432393630393030303030313036&amp;bizid=1023" aeskey="dd6a1ff656fc1cc201175153d299b0f8" externurl="http://wxapp.tc.qq.com/262/20304/stodownload?m=c4f63a7c1dae069a4714b7e324b9447c&amp;filekey=30340201010420301e02020106040253480410c4f63a7c1dae069a4714b7e324b9447c02027fb0040d00000004627466730000000131&amp;hy=SH&amp;storeid=32303232303831363230313632383030306161386135303030303030303065396464613030623030303030313036&amp;bizid=1023" externmd5="d2e1a4d2fb61369efe921c4a25967ad4" width="200" height="146" tpurl="" tpauthkey="" attachedtext="" attachedtextcolor="" lensid="" emojiattr="" linkid="" desc=""></emoji></msg>'}, 'Status': 3, 'ImgStatus': 2, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1753718549, 'MsgSource': '<msgsource>\n\t<silence>1</silence>\n\t<membercount>144</membercount>\n\t<signature>N0_V1_mTlZ/TIW|v1_jfEqbuU9</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'NewMsgId': 4393586307072291262, 'MsgSeq': 871407196}
2025-07-29 00:02:23 | INFO | 收到表情消息: 消息ID:336708820 来自:***********@chatroom 发送人:wxid_idzryo4rneok22 MD5:06a38377ba1ab30149fda754ed2d442c 大小:122302
2025-07-29 00:02:23 | DEBUG | [DoubaoImageRecognition] 缓存表情消息: 4393586307072291262
2025-07-29 00:02:43 | DEBUG | 收到消息: {'MsgId': 73814844, 'FromUserName': {'string': '***********@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 10002, 'Content': {'string': '***********@chatroom:\n<sysmsg type="pat">\n<pat>\n  <fromusername>wxid_idzryo4rneok22</fromusername>\n  <chatusername>***********@chatroom</chatusername>\n  <pattedusername>wxid_ugv5ryus4gz622</pattedusername>\n  <patsuffix><![CDATA[说：公主万福金安[烟花]]]></patsuffix>\n  <patsuffixversion>2</patsuffixversion>\n\n\n\n\n\n\n  <template><![CDATA["${wxid_idzryo4rneok22}" 拍了拍 "${wxid_ugv5ryus4gz622}" 说：公主万福金安[烟花]]]></template>\n\n\n\n\n\n\n\n</pat>\n</sysmsg>'}, 'Status': 4, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1753718565, 'MsgSource': '<msgsource>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'NewMsgId': 2074650927363238317, 'MsgSeq': 871407197}
2025-07-29 00:02:43 | DEBUG | 系统消息类型: pat
2025-07-29 00:02:43 | INFO | 收到拍一拍消息: 消息ID:73814844 来自:***********@chatroom 发送人:***********@chatroom 拍者:wxid_idzryo4rneok22 被拍:wxid_ugv5ryus4gz622 后缀:说：公主万福金安[烟花]
2025-07-29 00:02:43 | DEBUG | [PatReply] 被拍者 wxid_ugv5ryus4gz622 不是目标用户 wxid_4usgcju5ey9q29，跳过
2025-07-29 00:02:44 | DEBUG | 收到消息: {'MsgId': 97567917, 'FromUserName': {'string': '***********@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 3, 'Content': {'string': 'wxid_snv13qf05qjx11:\n<?xml version="1.0"?>\n<msg>\n\t<img aeskey="ff1938dc6d9c7b7912261750b0ad74e5" encryver="1" cdnthumbaeskey="ff1938dc6d9c7b7912261750b0ad74e5" cdnthumburl="3057020100044b3049020100020428925d0502032f55fa02041f861c65020468879f2b042435303363366231362d626534342d343261652d383063382d6662643765333930303363380204052838010201000405004c4d3600" cdnthumblength="4314" cdnthumbheight="132" cdnthumbwidth="180" cdnmidheight="0" cdnmidwidth="0" cdnhdheight="0" cdnhdwidth="0" cdnmidimgurl="3057020100044b3049020100020428925d0502032f55fa02041f861c65020468879f2b042435303363366231362d626534342d343261652d383063382d6662643765333930303363380204052838010201000405004c4d3600" length="18389" cdnbigimgurl="3057020100044b3049020100020428925d0502032f55fa02041f861c65020468879f2b042435303363366231362d626534342d343261652d383063382d6662643765333930303363380204052838010201000405004c4d3600" hdlength="18389" md5="129e027436873d2c977fffd5142d801e">\n\t\t<secHashInfoBase64 />\n\t\t<live>\n\t\t\t<duration>0</duration>\n\t\t\t<size>0</size>\n\t\t\t<md5 />\n\t\t\t<fileid />\n\t\t\t<hdsize>0</hdsize>\n\t\t\t<hdmd5 />\n\t\t\t<hdfileid />\n\t\t\t<stillimagetimems>0</stillimagetimems>\n\t\t</live>\n\t</img>\n\t<platform_signature />\n\t<imgdatahash />\n\t<ImgSourceInfo>\n\t\t<ImgSourceUrl />\n\t\t<BizType>0</BizType>\n\t</ImgSourceInfo>\n</msg>\n'}, 'Status': 3, 'ImgStatus': 2, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1753718571, 'MsgSource': '<msgsource>\n\t<img_file_name>88f66870-3d64-4294-8885-ff06ebdc2f59.png</img_file_name>\n\t<alnode>\n\t\t<fr>1</fr>\n\t\t<cf>3</cf>\n\t</alnode>\n\t<sec_msg_node>\n\t\t<uuid>ee091777b529302b6d8688505d1bafbd_</uuid>\n\t\t<risk-file-flag />\n\t\t<risk-file-md5-list />\n\t</sec_msg_node>\n\t<silence>1</silence>\n\t<membercount>144</membercount>\n\t<signature>N0_V1_nX9E0qgd|v1_P/Q393MZ</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'NewMsgId': 344229340190436390, 'MsgSeq': 871407198}
2025-07-29 00:02:44 | INFO | 收到图片消息: 消息ID:97567917 来自:***********@chatroom 发送人:wxid_snv13qf05qjx11 XML:<?xml version="1.0"?><msg><img aeskey="ff1938dc6d9c7b7912261750b0ad74e5" encryver="1" cdnthumbaeskey="ff1938dc6d9c7b7912261750b0ad74e5" cdnthumburl="3057020100044b3049020100020428925d0502032f55fa02041f861c65020468879f2b042435303363366231362d626534342d343261652d383063382d6662643765333930303363380204052838010201000405004c4d3600" cdnthumblength="4314" cdnthumbheight="132" cdnthumbwidth="180" cdnmidheight="0" cdnmidwidth="0" cdnhdheight="0" cdnhdwidth="0" cdnmidimgurl="3057020100044b3049020100020428925d0502032f55fa02041f861c65020468879f2b042435303363366231362d626534342d343261652d383063382d6662643765333930303363380204052838010201000405004c4d3600" length="18389" cdnbigimgurl="3057020100044b3049020100020428925d0502032f55fa02041f861c65020468879f2b042435303363366231362d626534342d343261652d383063382d6662643765333930303363380204052838010201000405004c4d3600" hdlength="18389" md5="129e027436873d2c977fffd5142d801e"><secHashInfoBase64 /><live><duration>0</duration><size>0</size><md5 /><fileid /><hdsize>0</hdsize><hdmd5 /><hdfileid /><stillimagetimems>0</stillimagetimems></live></img><platform_signature /><imgdatahash /><ImgSourceInfo><ImgSourceUrl /><BizType>0</BizType></ImgSourceInfo></msg>
2025-07-29 00:02:44 | INFO | [ImageEcho] 保存图片信息成功，当前群 ***********@chatroom 已存储 5 张图片
2025-07-29 00:02:44 | INFO | [TimerTask] 缓存图片消息: 97567917
2025-07-29 00:02:52 | DEBUG | 收到消息: {'MsgId': 1987072170, 'FromUserName': {'string': '***********@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 1, 'Content': {'string': 'wxid_snv13qf05qjx11:\n6'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1753718579, 'MsgSource': '<msgsource>\n\t<alnode>\n\t\t<fr>1</fr>\n\t</alnode>\n\t<silence>1</silence>\n\t<membercount>144</membercount>\n\t<signature>N0_V1_FJuyY18D|v1_0JJ2aUwf</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'NewMsgId': 4366435341269320987, 'MsgSeq': 871407199}
2025-07-29 00:02:52 | INFO | 收到文本消息: 消息ID:1987072170 来自:***********@chatroom 发送人:wxid_snv13qf05qjx11 @:[] 内容:6
2025-07-29 00:02:52 | DEBUG | 处理消息内容: '6'
2025-07-29 00:02:52 | DEBUG | 消息内容 '6' 不匹配任何命令，忽略
2025-07-29 00:02:55 | DEBUG | 收到消息: {'MsgId': 189204503, 'FromUserName': {'string': '***********@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 1, 'Content': {'string': 'wxid_ugv5ryus4gz622:\n哈哈哈哈哈哈哈哈哈哈'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1753718580, 'MsgSource': '<msgsource>\n\t<bizflag>0</bizflag>\n\t<pua>1</pua>\n\t<alnode>\n\t\t<inlenlist>10</inlenlist>\n\t</alnode>\n\t<eggIncluded>1</eggIncluded>\n\t<silence>1</silence>\n\t<membercount>144</membercount>\n\t<signature>N0_V1_AVeJGTO7|v1_GSEIuZXQ</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'NewMsgId': 884832841784117334, 'MsgSeq': 871407200}
2025-07-29 00:02:55 | INFO | 收到文本消息: 消息ID:189204503 来自:***********@chatroom 发送人:wxid_ugv5ryus4gz622 @:[] 内容:哈哈哈哈哈哈哈哈哈哈
2025-07-29 00:02:55 | DEBUG | 处理消息内容: '哈哈哈哈哈哈哈哈哈哈'
2025-07-29 00:02:55 | DEBUG | 消息内容 '哈哈哈哈哈哈哈哈哈哈' 不匹配任何命令，忽略
2025-07-29 00:02:58 | DEBUG | 收到消息: {'MsgId': 1152080204, 'FromUserName': {'string': '***********@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 1, 'Content': {'string': 'wxid_snv13qf05qjx11:\n再下佩服'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1753718584, 'MsgSource': '<msgsource>\n\t<alnode>\n\t\t<fr>1</fr>\n\t</alnode>\n\t<silence>1</silence>\n\t<membercount>144</membercount>\n\t<signature>N0_V1_drjBeu6x|v1_/cealWeb</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'NewMsgId': 7629276059174666883, 'MsgSeq': 871407201}
2025-07-29 00:02:58 | INFO | 收到文本消息: 消息ID:1152080204 来自:***********@chatroom 发送人:wxid_snv13qf05qjx11 @:[] 内容:再下佩服
2025-07-29 00:02:58 | DEBUG | 处理消息内容: '再下佩服'
2025-07-29 00:02:58 | DEBUG | 消息内容 '再下佩服' 不匹配任何命令，忽略
2025-07-29 00:03:01 | DEBUG | 收到消息: {'MsgId': 88732120, 'FromUserName': {'string': '***********@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 1, 'Content': {'string': 'wxid_ugv5ryus4gz622:\n还真就打了12个'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1753718587, 'MsgSource': '<msgsource>\n\t<bizflag>0</bizflag>\n\t<pua>1</pua>\n\t<eggIncluded>1</eggIncluded>\n\t<silence>1</silence>\n\t<membercount>144</membercount>\n\t<signature>N0_V1_a6AqaE0c|v1_zg3q2oP1</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'NewMsgId': 7699780424691010024, 'MsgSeq': 871407202}
2025-07-29 00:03:01 | INFO | 收到文本消息: 消息ID:88732120 来自:***********@chatroom 发送人:wxid_ugv5ryus4gz622 @:[] 内容:还真就打了12个
2025-07-29 00:03:01 | DEBUG | 处理消息内容: '还真就打了12个'
2025-07-29 00:03:01 | DEBUG | 消息内容 '还真就打了12个' 不匹配任何命令，忽略
2025-07-29 00:03:05 | DEBUG | 收到消息: {'MsgId': 738890384, 'FromUserName': {'string': '***********@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 1, 'Content': {'string': 'wxid_ikxxrwasicud11:\n神了'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1753718592, 'MsgSource': '<msgsource>\n\t<sec_msg_node>\n\t\t<alnode>\n\t\t\t<fr>1</fr>\n\t\t</alnode>\n\t</sec_msg_node>\n\t<pua>1</pua>\n\t<silence>1</silence>\n\t<membercount>144</membercount>\n\t<signature>N0_V1_FQC7lF7C|v1_f7Ru0Dxy</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'NewMsgId': 7291929090746052083, 'MsgSeq': 871407203}
2025-07-29 00:03:05 | INFO | 收到文本消息: 消息ID:738890384 来自:***********@chatroom 发送人:wxid_ikxxrwasicud11 @:[] 内容:神了
2025-07-29 00:03:05 | DEBUG | 处理消息内容: '神了'
2025-07-29 00:03:05 | DEBUG | 消息内容 '神了' 不匹配任何命令，忽略
2025-07-29 00:03:09 | DEBUG | 收到消息: {'MsgId': 416056276, 'FromUserName': {'string': '***********@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 47, 'Content': {'string': 'wxid_idzryo4rneok22:\n<msg><emoji fromusername="wxid_idzryo4rneok22" tousername="***********@chatroom" type="1" idbuffer="media:0_0" md5="094a2d521571fcbc74a7653a557458ff" len="61453" productid="" androidmd5="094a2d521571fcbc74a7653a557458ff" androidlen="61453" s60v3md5="094a2d521571fcbc74a7653a557458ff" s60v3len="61453" s60v5md5="094a2d521571fcbc74a7653a557458ff" s60v5len="61453" cdnurl="http://vweixinf.tc.qq.com/110/20401/stodownload?m=094a2d521571fcbc74a7653a557458ff&amp;filekey=30440201010430302e02016e0402534804203039346132643532313537316663626337346137363533613535373435386666020300f00d040d00000004627466730000000132&amp;hy=SH&amp;storeid=2679b812000015924613e5ea80000006e01004fb153481f73c1f15698a87c5&amp;ef=1&amp;bizid=1022" designerid="" thumburl="" encrypturl="http://vweixinf.tc.qq.com/110/20402/stodownload?m=f3623ce16fae40b63a2924db62f49bc4&amp;filekey=30440201010430302e02016e0402534804206633363233636531366661653430623633613239323464623632663439626334020300f010040d00000004627466730000000132&amp;hy=SH&amp;storeid=2679b81200001e6e0613e5ea80000006e02004fb253481f73c1f15698a87cc&amp;ef=2&amp;bizid=1022" aeskey="e2f2377a3eae45828809d20032f31bd2" externurl="http://vweixinf.tc.qq.com/110/20403/stodownload?m=61f47c5b4ce8b83d6a3513e3caf239a8&amp;filekey=3043020101042f302d02016e040253480420363166343763356234636538623833643661333531336533636166323339613802020bf0040d00000004627466730000000132&amp;hy=SH&amp;storeid=2679b81200002a285613e5ea80000006e03004fb353481f73c1f15698a87d3&amp;ef=3&amp;bizid=1022" externmd5="b32758dc01733c50cc308c1fec5534e3" width="297" height="300" tpurl="" tpauthkey="" attachedtext="" attachedtextcolor="" lensid="" emojiattr="" linkid="" desc=""></emoji></msg>'}, 'Status': 3, 'ImgStatus': 2, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1753718596, 'MsgSource': '<msgsource>\n\t<silence>1</silence>\n\t<membercount>144</membercount>\n\t<signature>N0_V1_tCeOSYIw|v1_om3lbn89</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'NewMsgId': 852054744776341193, 'MsgSeq': 871407204}
2025-07-29 00:03:09 | INFO | 收到表情消息: 消息ID:416056276 来自:***********@chatroom 发送人:wxid_idzryo4rneok22 MD5:094a2d521571fcbc74a7653a557458ff 大小:61453
2025-07-29 00:03:09 | DEBUG | [DoubaoImageRecognition] 缓存表情消息: 852054744776341193
2025-07-29 00:03:19 | DEBUG | 收到消息: {'MsgId': 1052013494, 'FromUserName': {'string': '***********@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 10002, 'Content': {'string': '***********@chatroom:\n<sysmsg type="pat">\n<pat>\n  <fromusername>wxid_idzryo4rneok22</fromusername>\n  <chatusername>***********@chatroom</chatusername>\n  <pattedusername>wxid_snv13qf05qjx11</pattedusername>\n  <patsuffix><![CDATA[ㅤO.o? 嘎哈！]]></patsuffix>\n  <patsuffixversion>2</patsuffixversion>\n\n\n\n\n\n\n  <template><![CDATA["${wxid_idzryo4rneok22}" 拍了拍 "${wxid_snv13qf05qjx11}" ㅤO.o? 嘎哈！]]></template>\n\n\n\n\n\n\n\n</pat>\n</sysmsg>'}, 'Status': 4, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1753718604, 'MsgSource': '<msgsource>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'NewMsgId': 6276160457485243250, 'MsgSeq': 871407205}
2025-07-29 00:03:19 | DEBUG | 系统消息类型: pat
2025-07-29 00:03:19 | INFO | 收到拍一拍消息: 消息ID:1052013494 来自:***********@chatroom 发送人:***********@chatroom 拍者:wxid_idzryo4rneok22 被拍:wxid_snv13qf05qjx11 后缀:ㅤO.o? 嘎哈！
2025-07-29 00:03:19 | DEBUG | [PatReply] 被拍者 wxid_snv13qf05qjx11 不是目标用户 wxid_4usgcju5ey9q29，跳过
2025-07-29 00:03:20 | DEBUG | 收到消息: {'MsgId': 849322235, 'FromUserName': {'string': '***********@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 47, 'Content': {'string': 'wxid_xv01lkcmn48l22:\n<msg><emoji fromusername = "wxid_xv01lkcmn48l22" tousername = "***********@chatroom" type="3" idbuffer="media:0_0" md5="84469de5406f26fda9eb26090d83e996" len = "8274" productid="" androidmd5="84469de5406f26fda9eb26090d83e996" androidlen="8274" s60v3md5 = "84469de5406f26fda9eb26090d83e996" s60v3len="8274" s60v5md5 = "84469de5406f26fda9eb26090d83e996" s60v5len="8274" cdnurl = "http://vweixinf.tc.qq.com/110/20401/stodownload?m=84469de5406f26fda9eb26090d83e996&amp;filekey=3043020101042f302d02016e040253480420383434363964653534303666323666646139656232363039306438336539393602022052040d00000004627466730000000132&amp;hy=SH&amp;storeid=265e938b000093873a41f0da90000006e01004fb15348031478e0b6f80571e&amp;ef=1&amp;bizid=1022" designerid = "" thumburl = "" encrypturl = "http://vweixinf.tc.qq.com/110/20402/stodownload?m=09f23759f07bb21b5a6b39e4e4028f15&amp;filekey=3043020101042f302d02016e040253480420303966323337353966303762623231623561366233396534653430323866313502022060040d00000004627466730000000132&amp;hy=SH&amp;storeid=265e938b00009f58ea41f0da90000006e02004fb25348031478e0b6f805725&amp;ef=2&amp;bizid=1022" aeskey= "6eea04bf90de4941952dfc1769956c4e" externurl = "http://vweixinf.tc.qq.com/110/20403/stodownload?m=d9dca087a88e9fb0ba2ad1d9663cbbbd&amp;filekey=3043020101042f302d02016e040253480420643964636130383761383865396662306261326164316439363633636262626402020f50040d00000004627466730000000132&amp;hy=SH&amp;storeid=265e938b0000a8101a41f0da90000006e03004fb35348031478e0b6f805736&amp;ef=3&amp;bizid=1022" externmd5 = "27382e1a10ab7b5fd6610cb6df42ebe5" width= "300" height= "288" tpurl= "" tpauthkey= "" attachedtext= "" attachedtextcolor= "" lensid= "" emojiattr= "" linkid= "" desc= "" ></emoji> <gameext type="0" content="0" ></gameext> </msg>'}, 'Status': 3, 'ImgStatus': 2, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1753718607, 'MsgSource': '<msgsource>\n\t<silence>1</silence>\n\t<membercount>144</membercount>\n\t<signature>N0_V1_DYAuq9rc|v1_I7W18ZkB</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'NewMsgId': 581130450221819321, 'MsgSeq': 871407206}
2025-07-29 00:03:20 | INFO | 收到表情消息: 消息ID:849322235 来自:***********@chatroom 发送人:wxid_xv01lkcmn48l22 MD5:84469de5406f26fda9eb26090d83e996 大小:8274
2025-07-29 00:03:20 | DEBUG | [DoubaoImageRecognition] 缓存表情消息: 581130450221819321
2025-07-29 00:03:22 | DEBUG | 收到消息: {'MsgId': 1470130287, 'FromUserName': {'string': '***********@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 1, 'Content': {'string': 'wxid_ugv5ryus4gz622:\n你俩腰疼吗'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1753718609, 'MsgSource': '<msgsource>\n\t<bizflag>0</bizflag>\n\t<pua>1</pua>\n\t<eggIncluded>1</eggIncluded>\n\t<silence>1</silence>\n\t<membercount>144</membercount>\n\t<signature>N0_V1_cGCtCS1Q|v1_aiZQAefz</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'NewMsgId': 6479847781716147167, 'MsgSeq': 871407207}
2025-07-29 00:03:22 | INFO | 收到文本消息: 消息ID:1470130287 来自:***********@chatroom 发送人:wxid_ugv5ryus4gz622 @:[] 内容:你俩腰疼吗
2025-07-29 00:03:22 | DEBUG | 处理消息内容: '你俩腰疼吗'
2025-07-29 00:03:22 | DEBUG | 消息内容 '你俩腰疼吗' 不匹配任何命令，忽略
2025-07-29 00:03:25 | DEBUG | 收到消息: {'MsgId': 1051381517, 'FromUserName': {'string': '***********@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 1, 'Content': {'string': 'wxid_ugv5ryus4gz622:\n我腰疼'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1753718612, 'MsgSource': '<msgsource>\n\t<bizflag>0</bizflag>\n\t<pua>1</pua>\n\t<eggIncluded>1</eggIncluded>\n\t<silence>1</silence>\n\t<membercount>144</membercount>\n\t<signature>N0_V1_SiU+I8Dx|v1_OnuHT7DQ</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'NewMsgId': 6905600317307562664, 'MsgSeq': 871407208}
2025-07-29 00:03:25 | INFO | 收到文本消息: 消息ID:1051381517 来自:***********@chatroom 发送人:wxid_ugv5ryus4gz622 @:[] 内容:我腰疼
2025-07-29 00:03:25 | DEBUG | 处理消息内容: '我腰疼'
2025-07-29 00:03:25 | DEBUG | 消息内容 '我腰疼' 不匹配任何命令，忽略
2025-07-29 00:03:35 | DEBUG | 收到消息: {'MsgId': 1284065798, 'FromUserName': {'string': '***********@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 1, 'Content': {'string': 'wxid_idzryo4rneok22:\n今儿腰不疼'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1753718622, 'MsgSource': '<msgsource>\n\t<pua>1</pua>\n\t<eggIncluded>1</eggIncluded>\n\t<silence>1</silence>\n\t<membercount>144</membercount>\n\t<signature>N0_V1_NMVeVO2j|v1_Kzerhm7q</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'NewMsgId': 2152786957290428035, 'MsgSeq': 871407209}
2025-07-29 00:03:35 | INFO | 收到文本消息: 消息ID:1284065798 来自:***********@chatroom 发送人:wxid_idzryo4rneok22 @:[] 内容:今儿腰不疼
2025-07-29 00:03:35 | DEBUG | 处理消息内容: '今儿腰不疼'
2025-07-29 00:03:35 | DEBUG | 消息内容 '今儿腰不疼' 不匹配任何命令，忽略
2025-07-29 00:03:38 | DEBUG | 收到消息: {'MsgId': 949442882, 'FromUserName': {'string': '***********@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 1, 'Content': {'string': 'wxid_idzryo4rneok22:\n背疼'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1753718625, 'MsgSource': '<msgsource>\n\t<pua>1</pua>\n\t<eggIncluded>1</eggIncluded>\n\t<silence>1</silence>\n\t<membercount>144</membercount>\n\t<signature>N0_V1_9SHGxEc5|v1_LzMeDd6x</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'NewMsgId': 2089292507958368284, 'MsgSeq': 871407210}
2025-07-29 00:03:38 | INFO | 收到文本消息: 消息ID:949442882 来自:***********@chatroom 发送人:wxid_idzryo4rneok22 @:[] 内容:背疼
2025-07-29 00:03:38 | DEBUG | 处理消息内容: '背疼'
2025-07-29 00:03:38 | DEBUG | 消息内容 '背疼' 不匹配任何命令，忽略
2025-07-29 00:03:46 | DEBUG | 收到消息: {'MsgId': 1250810701, 'FromUserName': {'string': '***********@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 1, 'Content': {'string': 'wxid_idzryo4rneok22:\n想去做个大保健'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1753718632, 'MsgSource': '<msgsource>\n\t<pua>1</pua>\n\t<eggIncluded>1</eggIncluded>\n\t<silence>1</silence>\n\t<membercount>144</membercount>\n\t<signature>N0_V1_gdxvBPv5|v1_RhSEHI96</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'NewMsgId': 7008354998539612822, 'MsgSeq': 871407211}
2025-07-29 00:03:46 | INFO | 收到文本消息: 消息ID:1250810701 来自:***********@chatroom 发送人:wxid_idzryo4rneok22 @:[] 内容:想去做个大保健
2025-07-29 00:03:46 | DEBUG | 处理消息内容: '想去做个大保健'
2025-07-29 00:03:46 | DEBUG | 消息内容 '想去做个大保健' 不匹配任何命令，忽略
2025-07-29 00:03:48 | DEBUG | 收到消息: {'MsgId': 1823119421, 'FromUserName': {'string': '***********@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 49, 'Content': {'string': 'wxid_snv13qf05qjx11:\n<?xml version="1.0"?>\n<msg>\n\t<appmsg appid="" sdkver="0">\n\t\t<title>+1</title>\n\t\t<des />\n\t\t<action />\n\t\t<type>57</type>\n\t\t<showtype>0</showtype>\n\t\t<soundtype>0</soundtype>\n\t\t<mediatagname />\n\t\t<messageext />\n\t\t<messageaction />\n\t\t<content />\n\t\t<contentattr>0</contentattr>\n\t\t<url />\n\t\t<lowurl />\n\t\t<dataurl />\n\t\t<lowdataurl />\n\t\t<songalbumurl />\n\t\t<songlyric />\n\t\t<template_id />\n\t\t<appattach>\n\t\t\t<totallen>0</totallen>\n\t\t\t<attachid />\n\t\t\t<emoticonmd5 />\n\t\t\t<fileext />\n\t\t\t<aeskey />\n\t\t</appattach>\n\t\t<extinfo />\n\t\t<sourceusername />\n\t\t<sourcedisplayname />\n\t\t<thumburl />\n\t\t<md5 />\n\t\t<statextstr />\n\t\t<refermsg>\n\t\t\t<type>1</type>\n\t\t\t<svrid>2089292507958368284</svrid>\n\t\t\t<fromusr>***********@chatroom</fromusr>\n\t\t\t<chatusr>wxid_idzryo4rneok22</chatusr>\n\t\t\t<displayname>红十</displayname>\n\t\t\t<msgsource>&lt;msgsource&gt;\n\t&lt;pua&gt;1&lt;/pua&gt;\n\t&lt;eggIncluded&gt;1&lt;/eggIncluded&gt;\n\t&lt;silence&gt;1&lt;/silence&gt;\n\t&lt;membercount&gt;144&lt;/membercount&gt;\n\t&lt;signature&gt;N0_V1_5yRWb9oJ|v1_8rEUtrC5&lt;/signature&gt;\n\t&lt;tmp_node&gt;\n\t\t&lt;publisher-id&gt;&lt;/publisher-id&gt;\n\t&lt;/tmp_node&gt;\n&lt;/msgsource&gt;\n</msgsource>\n\t\t\t<createtime>1753718625</createtime>\n\t\t\t<content>背疼</content>\n\t\t</refermsg>\n\t</appmsg>\n\t<fromusername>wxid_snv13qf05qjx11</fromusername>\n\t<scene>0</scene>\n\t<appinfo>\n\t\t<version>1</version>\n\t\t<appname></appname>\n\t</appinfo>\n\t<commenturl></commenturl>\n</msg>\n'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1753718633, 'MsgSource': '<msgsource>\n\t<alnode>\n\t\t<fr>1</fr>\n\t</alnode>\n\t<sec_msg_node>\n\t\t<uuid>8592b654439e3e58373f9f29f6e32adf_</uuid>\n\t\t<risk-file-flag />\n\t\t<risk-file-md5-list />\n\t</sec_msg_node>\n\t<silence>1</silence>\n\t<membercount>144</membercount>\n\t<signature>N0_V1_vFSyRUTS|v1_O+ZWTove</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'NewMsgId': 2877018885907031623, 'MsgSeq': 871407212}
2025-07-29 00:03:48 | DEBUG | 从群聊消息中提取发送者: wxid_snv13qf05qjx11
2025-07-29 00:03:48 | DEBUG | 使用已解析的XML处理引用消息
2025-07-29 00:03:48 | INFO | 收到引用消息: 消息ID:1823119421 来自:***********@chatroom 发送人:wxid_snv13qf05qjx11 内容:+1 引用类型:1
2025-07-29 00:03:48 | INFO | [DouBaoImageToImage] 收到引用消息: +1
2025-07-29 00:03:48 | INFO | [TimerTask] 收到引用消息调试信息:
2025-07-29 00:03:48 | INFO |   - 消息内容: +1
2025-07-29 00:03:48 | INFO |   - 群组ID: ***********@chatroom
2025-07-29 00:03:48 | INFO |   - 发送人: wxid_snv13qf05qjx11
2025-07-29 00:03:48 | INFO |   - 引用信息: {'MsgType': 1, 'Content': '背疼', 'Msgid': '2089292507958368284', 'NewMsgId': '2089292507958368284', 'ToWxid': 'wxid_4usgcju5ey9q29', 'FromWxid': '***********@chatroom', 'Nickname': '红十', 'MsgSource': '<msgsource>\n\t<pua>1</pua>\n\t<eggIncluded>1</eggIncluded>\n\t<silence>1</silence>\n\t<membercount>144</membercount>\n\t<signature>N0_V1_5yRWb9oJ|v1_8rEUtrC5</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'Createtime': '1753718625', 'SenderWxid': 'wxid_snv13qf05qjx11'}
2025-07-29 00:03:48 | INFO |   - 引用消息ID: 
2025-07-29 00:03:48 | INFO |   - 引用消息类型: 
2025-07-29 00:03:48 | INFO |   - 引用消息内容: 背疼
2025-07-29 00:03:48 | INFO |   - 引用消息发送人: wxid_snv13qf05qjx11
2025-07-29 00:03:49 | DEBUG | 收到消息: {'MsgId': 1705444949, 'FromUserName': {'string': '***********@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 49, 'Content': {'string': 'wxid_ikxxrwasicud11:\n<msg>\n\t<appmsg appid="" sdkver="">\n\t\t<des><![CDATA[我给你发了一个红包，赶紧去拆!]]></des>\n\t\t<url><![CDATA[https://wxapp.tenpay.com/mmpayhb/wxhb_personalreceive?showwxpaytitle=1&msgtype=1&channelid=1&sendid=1000039901202507297358086210003&ver=6&sign=24f5ca0894002734bc5d318cfc3b29c46bdbeb7e814b48e82a84c289864c0fe069d61d676c22411745afad763738e1f32a7ea5e822b0b99339b4e5f6b37291806da5a569101e90b2cdab6559c74d6adb]]></url>\n\t\t<lowurl><![CDATA[]]></lowurl>\n\t\t<type><![CDATA[2001]]></type>\n\t\t<title><![CDATA[微信红包]]></title>\n\t\t<thumburl><![CDATA[https://wx.gtimg.com/hongbao/1800/hb.png]]></thumburl>\n\t\t<wcpayinfo>\n\t\t\t<templateid><![CDATA[7a2a165d31da7fce6dd77e05c300028a]]></templateid>\n\t\t\t<url><![CDATA[https://wxapp.tenpay.com/mmpayhb/wxhb_personalreceive?showwxpaytitle=1&msgtype=1&channelid=1&sendid=1000039901202507297358086210003&ver=6&sign=24f5ca0894002734bc5d318cfc3b29c46bdbeb7e814b48e82a84c289864c0fe069d61d676c22411745afad763738e1f32a7ea5e822b0b99339b4e5f6b37291806da5a569101e90b2cdab6559c74d6adb]]></url>\n\t\t\t<iconurl><![CDATA[https://wx.gtimg.com/hongbao/1800/hb.png]]></iconurl>\n\t\t\t<receivertitle><![CDATA[🍋水]]></receivertitle>\n\t\t\t<sendertitle><![CDATA[🍋水]]></sendertitle>\n\t\t\t<scenetext><![CDATA[微信红包]]></scenetext>\n\t\t\t<senderdes><![CDATA[查看红包]]></senderdes>\n\t\t\t<receiverdes><![CDATA[领取红包]]></receiverdes>\n\t\t\t<nativeurl><![CDATA[wxpay://c2cbizmessagehandler/hongbao/receivehongbao?msgtype=1&channelid=1&sendid=1000039901202507297358086210003&sendusername=wxid_ikxxrwasicud11&ver=6&sign=24f5ca0894002734bc5d318cfc3b29c46bdbeb7e814b48e82a84c289864c0fe069d61d676c22411745afad763738e1f32a7ea5e822b0b99339b4e5f6b37291806da5a569101e90b2cdab6559c74d6adb&total_num=10]]></nativeurl>\n\t\t\t<sceneid><![CDATA[1002]]></sceneid>\n\t\t\t<innertype><![CDATA[0]]></innertype>\n\t\t\t<paymsgid><![CDATA[1000039901202507297358086210003]]></paymsgid>\n\t\t\t<scenetext>微信红包</scenetext>\n\t\t\t<locallogoicon><![CDATA[c2c_hongbao_icon_cn]]></locallogoicon>\n\t\t\t<invalidtime><![CDATA[1753805036]]></invalidtime>\n\t\t\t<broaden />\n\t\t</wcpayinfo>\n\t</appmsg>\n\t<fromusername><![CDATA[wxid_ikxxrwasicud11]]></fromusername>\n</msg>\n'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1753718636, 'MsgSource': '<msgsource>\n\t<pushkey />\n\t<ModifyMsgAction />\n\t<silence>1</silence>\n\t<membercount>144</membercount>\n\t<signature>N0_V1_PiOWtaiZ|v1_8eleNkxD</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'NewMsgId': 4477277450896143212, 'MsgSeq': 871407213}
2025-07-29 00:03:49 | DEBUG | 从群聊消息中提取发送者: wxid_ikxxrwasicud11
2025-07-29 00:03:49 | DEBUG | XML消息完整内容:
<msg>
	<appmsg appid="" sdkver="">
		<des><![CDATA[我给你发了一个红包，赶紧去拆!]]></des>
		<url><![CDATA[https://wxapp.tenpay.com/mmpayhb/wxhb_personalreceive?showwxpaytitle=1&msgtype=1&channelid=1&sendid=1000039901202507297358086210003&ver=6&sign=24f5ca0894002734bc5d318cfc3b29c46bdbeb7e814b48e82a84c289864c0fe069d61d676c22411745afad763738e1f32a7ea5e822b0b99339b4e5f6b37291806da5a569101e90b2cdab6559c74d6adb]]></url>
		<lowurl><![CDATA[]]></lowurl>
		<type><![CDATA[2001]]></type>
		<title><![CDATA[微信红包]]></title>
		<thumburl><![CDATA[https://wx.gtimg.com/hongbao/1800/hb.png]]></thumburl>
		<wcpayinfo>
			<templateid><![CDATA[7a2a165d31da7fce6dd77e05c300028a]]></templateid>
			<url><![CDATA[https://wxapp.tenpay.com/mmpayhb/wxhb_personalreceive?showwxpaytitle=1&msgtype=1&channelid=1&sendid=1000039901202507297358086210003&ver=6&sign=24f5ca0894002734bc5d318cfc3b29c46bdbeb7e814b48e82a84c289864c0fe069d61d676c22411745afad763738e1f32a7ea5e822b0b99339b4e5f6b37291806da5a569101e90b2cdab6559c74d6adb]]></url>
			<iconurl><![CDATA[https://wx.gtimg.com/hongbao/1800/hb.png]]></iconurl>
			<receivertitle><![CDATA[🍋水]]></receivertitle>
			<sendertitle><![CDATA[🍋水]]></sendertitle>
			<scenetext><![CDATA[微信红包]]></scenetext>
			<senderdes><![CDATA[查看红包]]></senderdes>
			<receiverdes><![CDATA[领取红包]]></receiverdes>
			<nativeurl><![CDATA[wxpay://c2cbizmessagehandler/hongbao/receivehongbao?msgtype=1&channelid=1&sendid=1000039901202507297358086210003&sendusername=wxid_ikxxrwasicud11&ver=6&sign=24f5ca0894002734bc5d318cfc3b29c46bdbeb7e814b48e82a84c289864c0fe069d61d676c22411745afad763738e1f32a7ea5e822b0b99339b4e5f6b37291806da5a569101e90b2cdab6559c74d6adb&total_num=10]]></nativeurl>
			<sceneid><![CDATA[1002]]></sceneid>
			<innertype><![CDATA[0]]></innertype>
			<paymsgid><![CDATA[1000039901202507297358086210003]]></paymsgid>
			<scenetext>微信红包</scenetext>
			<locallogoicon><![CDATA[c2c_hongbao_icon_cn]]></locallogoicon>
			<invalidtime><![CDATA[1753805036]]></invalidtime>
			<broaden />
		</wcpayinfo>
	</appmsg>
	<fromusername><![CDATA[wxid_ikxxrwasicud11]]></fromusername>
</msg>

2025-07-29 00:03:49 | DEBUG | XML消息类型: 2001
2025-07-29 00:03:49 | DEBUG | XML消息标题: 微信红包
2025-07-29 00:03:49 | DEBUG | XML消息描述: 我给你发了一个红包，赶紧去拆!
2025-07-29 00:03:49 | DEBUG | XML消息URL: https://wxapp.tenpay.com/mmpayhb/wxhb_personalreceive?showwxpaytitle=1&msgtype=1&channelid=1&sendid=1000039901202507297358086210003&ver=6&sign=24f5ca0894002734bc5d318cfc3b29c46bdbeb7e814b48e82a84c289864c0fe069d61d676c22411745afad763738e1f32a7ea5e822b0b99339b4e5f6b37291806da5a569101e90b2cdab6559c74d6adb
2025-07-29 00:03:49 | DEBUG | XML消息缩略图URL: https://wx.gtimg.com/hongbao/1800/hb.png
2025-07-29 00:03:49 | INFO | 未知的XML消息类型: 2001
2025-07-29 00:03:49 | INFO | 消息标题: 微信红包
2025-07-29 00:03:49 | INFO | 消息描述: 我给你发了一个红包，赶紧去拆!
2025-07-29 00:03:49 | INFO | 消息URL: https://wxapp.tenpay.com/mmpayhb/wxhb_personalreceive?showwxpaytitle=1&msgtype=1&channelid=1&sendid=1000039901202507297358086210003&ver=6&sign=24f5ca0894002734bc5d318cfc3b29c46bdbeb7e814b48e82a84c289864c0fe069d61d676c22411745afad763738e1f32a7ea5e822b0b99339b4e5f6b37291806da5a569101e90b2cdab6559c74d6adb
2025-07-29 00:03:49 | INFO | 完整XML内容:
<msg>
	<appmsg appid="" sdkver="">
		<des><![CDATA[我给你发了一个红包，赶紧去拆!]]></des>
		<url><![CDATA[https://wxapp.tenpay.com/mmpayhb/wxhb_personalreceive?showwxpaytitle=1&msgtype=1&channelid=1&sendid=1000039901202507297358086210003&ver=6&sign=24f5ca0894002734bc5d318cfc3b29c46bdbeb7e814b48e82a84c289864c0fe069d61d676c22411745afad763738e1f32a7ea5e822b0b99339b4e5f6b37291806da5a569101e90b2cdab6559c74d6adb]]></url>
		<lowurl><![CDATA[]]></lowurl>
		<type><![CDATA[2001]]></type>
		<title><![CDATA[微信红包]]></title>
		<thumburl><![CDATA[https://wx.gtimg.com/hongbao/1800/hb.png]]></thumburl>
		<wcpayinfo>
			<templateid><![CDATA[7a2a165d31da7fce6dd77e05c300028a]]></templateid>
			<url><![CDATA[https://wxapp.tenpay.com/mmpayhb/wxhb_personalreceive?showwxpaytitle=1&msgtype=1&channelid=1&sendid=1000039901202507297358086210003&ver=6&sign=24f5ca0894002734bc5d318cfc3b29c46bdbeb7e814b48e82a84c289864c0fe069d61d676c22411745afad763738e1f32a7ea5e822b0b99339b4e5f6b37291806da5a569101e90b2cdab6559c74d6adb]]></url>
			<iconurl><![CDATA[https://wx.gtimg.com/hongbao/1800/hb.png]]></iconurl>
			<receivertitle><![CDATA[🍋水]]></receivertitle>
			<sendertitle><![CDATA[🍋水]]></sendertitle>
			<scenetext><![CDATA[微信红包]]></scenetext>
			<senderdes><![CDATA[查看红包]]></senderdes>
			<receiverdes><![CDATA[领取红包]]></receiverdes>
			<nativeurl><![CDATA[wxpay://c2cbizmessagehandler/hongbao/receivehongbao?msgtype=1&channelid=1&sendid=1000039901202507297358086210003&sendusername=wxid_ikxxrwasicud11&ver=6&sign=24f5ca0894002734bc5d318cfc3b29c46bdbeb7e814b48e82a84c289864c0fe069d61d676c22411745afad763738e1f32a7ea5e822b0b99339b4e5f6b37291806da5a569101e90b2cdab6559c74d6adb&total_num=10]]></nativeurl>
			<sceneid><![CDATA[1002]]></sceneid>
			<innertype><![CDATA[0]]></innertype>
			<paymsgid><![CDATA[1000039901202507297358086210003]]></paymsgid>
			<scenetext>微信红包</scenetext>
			<locallogoicon><![CDATA[c2c_hongbao_icon_cn]]></locallogoicon>
			<invalidtime><![CDATA[1753805036]]></invalidtime>
			<broaden />
		</wcpayinfo>
	</appmsg>
	<fromusername><![CDATA[wxid_ikxxrwasicud11]]></fromusername>
</msg>

2025-07-29 00:04:08 | DEBUG | 收到消息: {'MsgId': 519347078, 'FromUserName': {'string': '***********@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 10002, 'Content': {'string': '***********@chatroom:\n<sysmsg type="pat">\n<pat>\n  <fromusername>wxid_ikxxrwasicud11</fromusername>\n  <chatusername>***********@chatroom</chatusername>\n  <pattedusername>wxid_idzryo4rneok22</pattedusername>\n  <patsuffix><![CDATA[表示已读]]></patsuffix>\n  <patsuffixversion>2</patsuffixversion>\n\n\n\n\n\n\n  <template><![CDATA["${wxid_ikxxrwasicud11}" 拍了拍 "${wxid_idzryo4rneok22}" 表示已读]]></template>\n\n\n\n\n\n\n\n</pat>\n</sysmsg>'}, 'Status': 4, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1753718652, 'MsgSource': '<msgsource>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'NewMsgId': 6125104800600275793, 'MsgSeq': 871407214}
2025-07-29 00:04:08 | DEBUG | 系统消息类型: pat
2025-07-29 00:04:08 | INFO | 收到拍一拍消息: 消息ID:519347078 来自:***********@chatroom 发送人:***********@chatroom 拍者:wxid_ikxxrwasicud11 被拍:wxid_idzryo4rneok22 后缀:表示已读
2025-07-29 00:04:08 | DEBUG | [PatReply] 被拍者 wxid_idzryo4rneok22 不是目标用户 wxid_4usgcju5ey9q29，跳过
2025-07-29 00:04:11 | DEBUG | 收到消息: {'MsgId': 1316310011, 'FromUserName': {'string': '***********@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 47, 'Content': {'string': 'wxid_av3l5ovnw2o422:\n<msg><emoji fromusername="wxid_av3l5ovnw2o422" tousername="***********@chatroom" type="2" idbuffer="media:0_0" md5="81cea63763eb5e939971befb36779e99" len="93169" productid="" androidmd5="81cea63763eb5e939971befb36779e99" androidlen="93169" s60v3md5="81cea63763eb5e939971befb36779e99" s60v3len="93169" s60v5md5="81cea63763eb5e939971befb36779e99" s60v5len="93169" cdnurl="http://vweixinf.tc.qq.com/110/20401/stodownload?m=81cea63763eb5e939971befb36779e99&amp;filekey=30440201010430302e02016e04025348042038316365613633373633656235653933393937316265666233363737396539390203016bf1040d00000004627466730000000132&amp;hy=SH&amp;storeid=263d61aaf0006ae373114089b0000006e01004fb1534812269b40b6eb4f221&amp;ef=1&amp;bizid=1022" designerid="" thumburl="" encrypturl="http://vweixinf.tc.qq.com/110/20402/stodownload?m=36db6c75221f73e5102fa74e2413b5b0&amp;filekey=30440201010430302e02016e04025348042033366462366337353232316637336535313032666137346532343133623562300203016c00040d00000004627466730000000132&amp;hy=SH&amp;storeid=263d61aaf000726383114089b0000006e02004fb2534812269b40b6eb4f22e&amp;ef=2&amp;bizid=1022" aeskey="0a51385326d04e709cf187379a747623" externurl="http://vweixinf.tc.qq.com/110/20403/stodownload?m=108f7f1b69b3259742518d1af77d15da&amp;filekey=3043020101042f302d02016e040253480420313038663766316236396233323539373432353138643161663737643135646102022b50040d00000004627466730000000132&amp;hy=SH&amp;storeid=263d61aaf000790753114089b0000006e03004fb3534812269b40b6eb4f23e&amp;ef=3&amp;bizid=1022" externmd5="3c676d07f9799172e2482dea7ece99e5" width="640" height="559" tpurl="" tpauthkey="" attachedtext="" attachedtextcolor="" lensid="" emojiattr="" linkid="" desc=""></emoji></msg>'}, 'Status': 3, 'ImgStatus': 2, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1753718657, 'MsgSource': '<msgsource>\n\t<silence>1</silence>\n\t<membercount>144</membercount>\n\t<signature>N0_V1_a/+Pixkb|v1_3ixVgMMD</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'NewMsgId': 3355907926339080478, 'MsgSeq': 871407215}
2025-07-29 00:04:11 | INFO | 收到表情消息: 消息ID:1316310011 来自:***********@chatroom 发送人:wxid_av3l5ovnw2o422 MD5:81cea63763eb5e939971befb36779e99 大小:93169
2025-07-29 00:04:11 | DEBUG | [DoubaoImageRecognition] 缓存表情消息: 3355907926339080478
2025-07-29 00:04:15 | DEBUG | 收到消息: {'MsgId': 145083306, 'FromUserName': {'string': '***********@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 1, 'Content': {'string': 'wxid_snv13qf05qjx11:\n谢谢李将军'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1753718662, 'MsgSource': '<msgsource>\n\t<bizflag>0</bizflag>\n\t<pua>1</pua>\n\t<eggIncluded>1</eggIncluded>\n\t<silence>1</silence>\n\t<membercount>144</membercount>\n\t<signature>N0_V1_18IakTMf|v1_6H39mK9f</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'NewMsgId': 6620513697575601795, 'MsgSeq': 871407216}
2025-07-29 00:04:15 | INFO | 收到文本消息: 消息ID:145083306 来自:***********@chatroom 发送人:wxid_snv13qf05qjx11 @:[] 内容:谢谢李将军
2025-07-29 00:04:15 | DEBUG | 处理消息内容: '谢谢李将军'
2025-07-29 00:04:15 | DEBUG | 消息内容 '谢谢李将军' 不匹配任何命令，忽略
2025-07-29 00:04:20 | DEBUG | 收到消息: {'MsgId': 1949381598, 'FromUserName': {'string': '***********@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 1, 'Content': {'string': 'wxid_ugv5ryus4gz622:\n啊 累死累活挣3毛'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1753718666, 'MsgSource': '<msgsource>\n\t<bizflag>0</bizflag>\n\t<pua>1</pua>\n\t<eggIncluded>1</eggIncluded>\n\t<silence>1</silence>\n\t<membercount>144</membercount>\n\t<signature>N0_V1_KlnG/Q0c|v1_OLjq5HCV</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'NewMsgId': 4748437776024136453, 'MsgSeq': 871407217}
2025-07-29 00:04:20 | INFO | 收到文本消息: 消息ID:1949381598 来自:***********@chatroom 发送人:wxid_ugv5ryus4gz622 @:[] 内容:啊 累死累活挣3毛
2025-07-29 00:04:20 | DEBUG | 处理消息内容: '啊 累死累活挣3毛'
2025-07-29 00:04:20 | DEBUG | 消息内容 '啊 累死累活挣3毛' 不匹配任何命令，忽略
2025-07-29 00:04:23 | DEBUG | 收到消息: {'MsgId': 842223885, 'FromUserName': {'string': '***********@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 47, 'Content': {'string': 'wxid_ugv5ryus4gz622:\n<msg><emoji fromusername="wxid_ugv5ryus4gz622" tousername="***********@chatroom" type="2" idbuffer="media:0_0" md5="774d749d0f815fce1326b0d458af4f26" len="13444" productid="" androidmd5="774d749d0f815fce1326b0d458af4f26" androidlen="13444" s60v3md5="774d749d0f815fce1326b0d458af4f26" s60v3len="13444" s60v5md5="774d749d0f815fce1326b0d458af4f26" s60v5len="13444" cdnurl="http://wxapp.tc.qq.com/262/20304/stodownload?m=774d749d0f815fce1326b0d458af4f26&amp;filekey=30340201010420301e020201060402535a0410774d749d0f815fce1326b0d458af4f2602023484040d00000004627466730000000132&amp;hy=SZ&amp;storeid=263f46e0e000192aab16ccaf40000010600004f50535a0760c950b6bc83175&amp;bizid=1023" designerid="" thumburl="" encrypturl="http://wxapp.tc.qq.com/262/20304/stodownload?m=b8ad9dc04a4fd80c0c1d63fae48b2d60&amp;filekey=30340201010420301e020201060402535a0410b8ad9dc04a4fd80c0c1d63fae48b2d6002023490040d00000004627466730000000132&amp;hy=SZ&amp;storeid=263f46e0e000281eab16ccaf40000010600004f50535a2c1ccb50b6951fcd5&amp;bizid=1023" aeskey="357704d24b8a8a7f01e8c1a719322912" externurl="http://wxapp.tc.qq.com/262/20304/stodownload?m=bfefc762781630b67ea32039ea00fdd6&amp;filekey=30340201010420301e020201060402535a0410bfefc762781630b67ea32039ea00fdd602022b50040d00000004627466730000000132&amp;hy=SZ&amp;storeid=263f4708b00047a0678b51e920000010600004f50535a1de97910b6c0f2cd4&amp;bizid=1023" externmd5="ec2bf2b1020fffea234fce32f8683788" width="75" height="75" tpurl="" tpauthkey="" attachedtext="" attachedtextcolor="" lensid="" emojiattr="" linkid="" desc=""></emoji><gameext type="0" content="0"></gameext></msg>'}, 'Status': 3, 'ImgStatus': 2, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1753718669, 'MsgSource': '<msgsource>\n\t<silence>1</silence>\n\t<membercount>144</membercount>\n\t<signature>N0_V1_amKPpW8E|v1_ytce3OTQ</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'NewMsgId': 818303245576357668, 'MsgSeq': 871407218}
2025-07-29 00:04:23 | INFO | 收到表情消息: 消息ID:842223885 来自:***********@chatroom 发送人:wxid_ugv5ryus4gz622 MD5:774d749d0f815fce1326b0d458af4f26 大小:13444
2025-07-29 00:04:23 | DEBUG | [DoubaoImageRecognition] 缓存表情消息: 818303245576357668
2025-07-29 00:04:23 | DEBUG | 收到消息: {'MsgId': 1712182543, 'FromUserName': {'string': '***********@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 1, 'Content': {'string': 'tianen532965049:\n谢老'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1753718670, 'MsgSource': '<msgsource>\n\t<bizflag>0</bizflag>\n\t<pua>1</pua>\n\t<eggIncluded>1</eggIncluded>\n\t<silence>1</silence>\n\t<membercount>144</membercount>\n\t<signature>N0_V1_49HO8lMA|v1_eOFi29yx</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'NewMsgId': 3994220405659653845, 'MsgSeq': 871407219}
2025-07-29 00:04:23 | INFO | 收到文本消息: 消息ID:1712182543 来自:***********@chatroom 发送人:tianen532965049 @:[] 内容:谢老
2025-07-29 00:04:23 | DEBUG | 处理消息内容: '谢老'
2025-07-29 00:04:23 | DEBUG | 消息内容 '谢老' 不匹配任何命令，忽略
2025-07-29 00:04:45 | DEBUG | 收到消息: {'MsgId': 82329212, 'FromUserName': {'string': '***********@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 1, 'Content': {'string': 'wxid_2ztoui7te69r22:\n谢谢老板'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1753718692, 'MsgSource': '<msgsource>\n\t<pua>1</pua>\n\t<eggIncluded>1</eggIncluded>\n\t<silence>1</silence>\n\t<membercount>144</membercount>\n\t<signature>N0_V1_/UKBf6Ti|v1_3xohfb52</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'NewMsgId': 7230698831454743379, 'MsgSeq': 871407220}
2025-07-29 00:04:45 | INFO | 收到文本消息: 消息ID:82329212 来自:***********@chatroom 发送人:wxid_2ztoui7te69r22 @:[] 内容:谢谢老板
2025-07-29 00:04:46 | INFO | 发送表情消息: 对方wxid:***********@chatroom md5:540280e804b91bf641fac9b8a9cf1325 总长度:9992069
2025-07-29 00:04:46 | DEBUG | 处理消息内容: '谢谢老板'
2025-07-29 00:04:46 | DEBUG | 消息内容 '谢谢老板' 不匹配任何命令，忽略
2025-07-29 00:05:09 | DEBUG | 收到消息: {'MsgId': 200488197, 'FromUserName': {'string': '***********@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 1, 'Content': {'string': 'wxid_snv13qf05qjx11:\n我用个劵够了'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1753718716, 'MsgSource': '<msgsource>\n\t<alnode>\n\t\t<fr>1</fr>\n\t</alnode>\n\t<silence>1</silence>\n\t<membercount>144</membercount>\n\t<signature>N0_V1_56s7OqCf|v1_Uh0QDISR</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'NewMsgId': 8205848610192390675, 'MsgSeq': 871407223}
2025-07-29 00:05:09 | INFO | 收到文本消息: 消息ID:200488197 来自:***********@chatroom 发送人:wxid_snv13qf05qjx11 @:[] 内容:我用个劵够了
2025-07-29 00:05:09 | DEBUG | 处理消息内容: '我用个劵够了'
2025-07-29 00:05:09 | DEBUG | 消息内容 '我用个劵够了' 不匹配任何命令，忽略
2025-07-29 00:05:14 | DEBUG | 收到消息: {'MsgId': 372739652, 'FromUserName': {'string': '***********@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 47, 'Content': {'string': 'wxid_snv13qf05qjx11:\n<msg><emoji fromusername = "wxid_snv13qf05qjx11" tousername = "***********@chatroom" type="2" idbuffer="media:0_0" md5="4eae0559b347467511b5003c6955d0f4" len = "56927" productid="" androidmd5="4eae0559b347467511b5003c6955d0f4" androidlen="56927" s60v3md5 = "4eae0559b347467511b5003c6955d0f4" s60v3len="56927" s60v5md5 = "4eae0559b347467511b5003c6955d0f4" s60v5len="56927" cdnurl = "http://vweixinf.tc.qq.com/110/20401/stodownload?m=4eae0559b347467511b5003c6955d0f4&amp;filekey=30440201010430302e02016e0402535a04203465616530353539623334373436373531316235303033633639353564306634020300de5f040d00000004627466730000000132&amp;hy=SZ&amp;storeid=267c2b96300047d071da2754b0000006e01004fb1535a0b369bc1e698bb06f&amp;ef=1&amp;bizid=1022" designerid = "" thumburl = "" encrypturl = "http://vweixinf.tc.qq.com/110/20402/stodownload?m=21d76db6fde3ed65d72502ee7a53f72d&amp;filekey=30440201010430302e02016e0402535a04203231643736646236666465336564363564373235303265653761353366373264020300de60040d00000004627466730000000132&amp;hy=SZ&amp;storeid=267c2b963000568d51da2754b0000006e02004fb2535a0b369bc1e698bb080&amp;ef=2&amp;bizid=1022" aeskey= "2fe4e539fb7240939742d64ee0df04c0" externurl = "http://vweixinf.tc.qq.com/110/20403/stodownload?m=a0674103f37f366fb959a7ae53a1fe08&amp;filekey=30440201010430302e02016e0402535a042061303637343130336633376633363666623935396137616535336131666530380203009f80040d00000004627466730000000132&amp;hy=SZ&amp;storeid=267c2b963000634501da2754b0000006e03004fb3535a0b369bc1e698bb08d&amp;ef=3&amp;bizid=1022" externmd5 = "e7837d01414917bb9b88bbfea5d39d32" width= "240" height= "240" tpurl= "" tpauthkey= "" attachedtext= "" attachedtextcolor= "" lensid= "" emojiattr= "" linkid= "" desc= "" ></emoji> <gameext type="0" content="0" ></gameext> </msg>'}, 'Status': 3, 'ImgStatus': 2, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1753718721, 'MsgSource': '<msgsource>\n\t<silence>1</silence>\n\t<membercount>144</membercount>\n\t<signature>N0_V1_WYOAX46L|v1_OZKwbh37</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'NewMsgId': 4169221299529024302, 'MsgSeq': 871407224}
2025-07-29 00:05:14 | INFO | 收到表情消息: 消息ID:372739652 来自:***********@chatroom 发送人:wxid_snv13qf05qjx11 MD5:4eae0559b347467511b5003c6955d0f4 大小:56927
2025-07-29 00:05:14 | DEBUG | [DoubaoImageRecognition] 缓存表情消息: 4169221299529024302
2025-07-29 00:05:29 | DEBUG | 收到消息: {'MsgId': 222508534, 'FromUserName': {'string': '***********@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 1, 'Content': {'string': 'wxid_ugv5ryus4gz622:\n我用个劵的话'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1753718735, 'MsgSource': '<msgsource>\n\t<bizflag>0</bizflag>\n\t<pua>1</pua>\n\t<eggIncluded>1</eggIncluded>\n\t<silence>1</silence>\n\t<membercount>144</membercount>\n\t<signature>N0_V1_X3LMR4Ji|v1_x1I4ZhJn</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'NewMsgId': 6017524214429002233, 'MsgSeq': 871407225}
2025-07-29 00:05:29 | INFO | 收到文本消息: 消息ID:222508534 来自:***********@chatroom 发送人:wxid_ugv5ryus4gz622 @:[] 内容:我用个劵的话
2025-07-29 00:05:29 | DEBUG | 处理消息内容: '我用个劵的话'
2025-07-29 00:05:29 | DEBUG | 消息内容 '我用个劵的话' 不匹配任何命令，忽略
2025-07-29 00:05:43 | DEBUG | 收到消息: {'MsgId': 1715955580, 'FromUserName': {'string': '48097389945@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 1, 'Content': {'string': 'chichengqianwanli:\n洞房'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1753718750, 'MsgSource': '<msgsource>\n\t<pua>1</pua>\n\t<eggIncluded>1</eggIncluded>\n\t<silence>0</silence>\n\t<membercount>73</membercount>\n\t<signature>N0_V1_xHzyWfER|v1_6VZdlGHB</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'PushContent': '- : 洞房', 'NewMsgId': 4829012296335246170, 'MsgSeq': 871407226}
2025-07-29 00:05:43 | INFO | 收到文本消息: 消息ID:1715955580 来自:48097389945@chatroom 发送人:chichengqianwanli @:[] 内容:洞房
2025-07-29 00:05:44 | DEBUG | 处理消息内容: '洞房'
2025-07-29 00:05:44 | DEBUG | 消息内容 '洞房' 不匹配任何命令，忽略
2025-07-29 00:05:47 | DEBUG | 收到消息: {'MsgId': 1104634212, 'FromUserName': {'string': '48097389945@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 1, 'Content': {'string': 'wxid_q35rkzgkjvlv12:\n@- 魅力只有0明显不足，无法完成双修，赶紧去互送礼物增加魅力'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1753718751, 'MsgSource': '<msgsource>\n\t<silence>0</silence>\n\t<membercount>73</membercount>\n\t<signature>N0_V1_yZ6iGYwB|v1_EJJnnwuy</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'PushContent': '摇月 : @- 魅力只有0明显不足，无法完成双修，赶紧去互送礼物增加魅力', 'NewMsgId': 1888901046754313779, 'MsgSeq': 871407227}
2025-07-29 00:05:47 | INFO | 收到文本消息: 消息ID:1104634212 来自:48097389945@chatroom 发送人:wxid_q35rkzgkjvlv12 @:[] 内容:@- 魅力只有0明显不足，无法完成双修，赶紧去互送礼物增加魅力
2025-07-29 00:05:47 | DEBUG | 处理消息内容: '@- 魅力只有0明显不足，无法完成双修，赶紧去互送礼物增加魅力'
2025-07-29 00:05:47 | DEBUG | 消息内容 '@- 魅力只有0明显不足，无法完成双修，赶紧去互送礼物增加魅力' 不匹配任何命令，忽略
2025-07-29 00:05:50 | DEBUG | 收到消息: {'MsgId': 291516405, 'FromUserName': {'string': '***********@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 1, 'Content': {'string': 'wxid_ugv5ryus4gz622:\n够买片柠檬'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1753718751, 'MsgSource': '<msgsource>\n\t<bizflag>0</bizflag>\n\t<pua>1</pua>\n\t<eggIncluded>1</eggIncluded>\n\t<silence>1</silence>\n\t<membercount>144</membercount>\n\t<signature>N0_V1_JXYwjutp|v1_THigJg+b</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'NewMsgId': 8825654567638659608, 'MsgSeq': 871407228}
2025-07-29 00:05:50 | INFO | 收到文本消息: 消息ID:291516405 来自:***********@chatroom 发送人:wxid_ugv5ryus4gz622 @:[] 内容:够买片柠檬
2025-07-29 00:05:50 | DEBUG | 处理消息内容: '够买片柠檬'
2025-07-29 00:05:50 | DEBUG | 消息内容 '够买片柠檬' 不匹配任何命令，忽略
2025-07-29 00:05:53 | DEBUG | 收到消息: {'MsgId': 1233726145, 'FromUserName': {'string': '48097389945@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 1, 'Content': {'string': 'wxid_8l9ymg1mafud12:\n@-\u2005\r\n你没有配偶呢'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1753718751, 'MsgSource': '<msgsource>\n\t<atuserlist>chichengqianwanli</atuserlist>\n\t<silence>0</silence>\n\t<membercount>73</membercount>\n\t<signature>N0_V1_7VmXWu4l|v1_uJ6VAXRM</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'PushContent': '不吃香菜\ue110 : @-\u2005\r\n你没有配偶呢', 'NewMsgId': 1943319059668440218, 'MsgSeq': 871407229}
2025-07-29 00:05:53 | INFO | 收到文本消息: 消息ID:1233726145 来自:48097389945@chatroom 发送人:wxid_8l9ymg1mafud12 @:['chichengqianwanli'] 内容:@- 

你没有配偶呢
2025-07-29 00:05:54 | DEBUG | 处理消息内容: '@- 

你没有配偶呢'
2025-07-29 00:05:54 | DEBUG | 消息内容 '@- 

你没有配偶呢' 不匹配任何命令，忽略
2025-07-29 00:05:57 | DEBUG | 收到消息: {'MsgId': 1476607050, 'FromUserName': {'string': '***********@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 1, 'Content': {'string': 'wxid_ugv5ryus4gz622:\n也行'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1753718752, 'MsgSource': '<msgsource>\n\t<bizflag>0</bizflag>\n\t<pua>1</pua>\n\t<eggIncluded>1</eggIncluded>\n\t<silence>1</silence>\n\t<membercount>144</membercount>\n\t<signature>N0_V1_6ZhPKMFw|v1_rmgcqboL</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'NewMsgId': 6014990293980256081, 'MsgSeq': 871407230}
2025-07-29 00:05:57 | INFO | 收到文本消息: 消息ID:1476607050 来自:***********@chatroom 发送人:wxid_ugv5ryus4gz622 @:[] 内容:也行
2025-07-29 00:05:57 | DEBUG | 处理消息内容: '也行'
2025-07-29 00:05:57 | DEBUG | 消息内容 '也行' 不匹配任何命令，忽略
2025-07-29 00:06:00 | DEBUG | 收到消息: {'MsgId': 1064228330, 'FromUserName': {'string': '***********@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 1, 'Content': {'string': 'wxid_idzryo4rneok22:\n感谢烙铁的'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1753718762, 'MsgSource': '<msgsource>\n\t<pua>1</pua>\n\t<eggIncluded>1</eggIncluded>\n\t<silence>1</silence>\n\t<membercount>144</membercount>\n\t<signature>N0_V1_Guetn+KA|v1_OIRcCkyv</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'NewMsgId': 899813246890282201, 'MsgSeq': 871407231}
2025-07-29 00:06:00 | INFO | 收到文本消息: 消息ID:1064228330 来自:***********@chatroom 发送人:wxid_idzryo4rneok22 @:[] 内容:感谢烙铁的
2025-07-29 00:06:00 | DEBUG | 处理消息内容: '感谢烙铁的'
2025-07-29 00:06:00 | DEBUG | 消息内容 '感谢烙铁的' 不匹配任何命令，忽略
2025-07-29 00:06:02 | DEBUG | 收到消息: {'MsgId': 2039277403, 'FromUserName': {'string': '***********@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 47, 'Content': {'string': 'wxid_ohq9p1qosjzq22:\n<msg><emoji fromusername="wxid_ohq9p1qosjzq22" tousername="***********@chatroom" type="2" idbuffer="media:0_0" md5="d0c4b6383e9894fdcc6306896155d6be" len="32030" productid="com.tencent.xin.emoticon.person.stiker_1684231067c6322758fd4de078" androidmd5="d0c4b6383e9894fdcc6306896155d6be" androidlen="32030" s60v3md5="d0c4b6383e9894fdcc6306896155d6be" s60v3len="32030" s60v5md5="d0c4b6383e9894fdcc6306896155d6be" s60v5len="32030" cdnurl="http://wxapp.tc.qq.com/262/20304/stodownload?m=d0c4b6383e9894fdcc6306896155d6be&amp;filekey=30340201010420301e020201060402535a0410d0c4b6383e9894fdcc6306896155d6be02027d1e040d00000004627466730000000132&amp;hy=SZ&amp;storeid=264ecaeac000f1f847ab726dd0000010600004f50535a27266bc1e7ea32228&amp;bizid=1023" designerid="" thumburl="http://mmbiz.qpic.cn/mmemoticon/Q3auHgzwzM4pQElh3x6XD9QJibCFan1MB4Riaib5H2bz6h01XE6NLMFClUTjgSYchEl/0" encrypturl="http://wxapp.tc.qq.com/262/20304/stodownload?m=5ddbc774620c684539af1e27be92d6ae&amp;filekey=30340201010420301e020201060402535a04105ddbc774620c684539af1e27be92d6ae02027d20040d00000004627466730000000132&amp;hy=SZ&amp;storeid=264ecaead0000df817ab726dd0000010600004f50535a06e918e0b6fd2ec64&amp;bizid=1023" aeskey="5c53434c85d804fec087f78cd4e2e1ed" externurl="http://wxapp.tc.qq.com/262/20304/stodownload?m=17d64994a2f95f235890febe980b0769&amp;filekey=30340201010420301e020201060402535a041017d64994a2f95f235890febe980b076902021f80040d00000004627466730000000132&amp;hy=SZ&amp;storeid=264ecaead0001cfcc7ab726dd0000010600004f50535a2fb6ebc1e6660f19b&amp;bizid=1023" externmd5="5e0b57c8fad352ccc5e7f3e5ef56b656" width="240" height="240" tpurl="" tpauthkey="" attachedtext="" attachedtextcolor="" lensid="" emojiattr="" linkid="" desc="ChcKB2RlZmF1bHQSDOiwouiwouiAgeadvw=="></emoji></msg>'}, 'Status': 3, 'ImgStatus': 2, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1753718762, 'MsgSource': '<msgsource>\n\t<silence>1</silence>\n\t<membercount>144</membercount>\n\t<signature>N0_V1_2tapWXBG|v1_pRzbWXgC</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'NewMsgId': 4673681775440929925, 'MsgSeq': 871407232}
2025-07-29 00:06:02 | INFO | 收到表情消息: 消息ID:2039277403 来自:***********@chatroom 发送人:wxid_ohq9p1qosjzq22 MD5:d0c4b6383e9894fdcc6306896155d6be 大小:32030
2025-07-29 00:06:02 | DEBUG | [DoubaoImageRecognition] 缓存表情消息: 4673681775440929925
2025-07-29 00:06:03 | DEBUG | 收到消息: {'MsgId': 1162014119, 'FromUserName': {'string': '48097389945@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 1, 'Content': {'string': 'chichengqianwanli:\n[擦汗]'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1753718763, 'MsgSource': '<msgsource>\n\t<pua>1</pua>\n\t<eggIncluded>1</eggIncluded>\n\t<silence>0</silence>\n\t<membercount>73</membercount>\n\t<signature>N0_V1_JuHUHsN7|v1_eh6RndDt</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'PushContent': '- : [擦汗]', 'NewMsgId': 1298160670256843726, 'MsgSeq': 871407233}
2025-07-29 00:06:03 | INFO | 收到表情消息: 消息ID:1162014119 来自:48097389945@chatroom 发送人:chichengqianwanli @:[] 内容:[擦汗]
2025-07-29 00:06:03 | DEBUG | [DoubaoImageRecognition] 缓存表情消息: 1298160670256843726
2025-07-29 00:06:04 | DEBUG | 收到消息: {'MsgId': 104802475, 'FromUserName': {'string': '***********@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 1, 'Content': {'string': 'wxid_idzryo4rneok22:\n水'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1753718764, 'MsgSource': '<msgsource>\n\t<pua>1</pua>\n\t<eggIncluded>1</eggIncluded>\n\t<silence>1</silence>\n\t<membercount>144</membercount>\n\t<signature>N0_V1_URP6dmds|v1_67qpaPZn</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'NewMsgId': 3223542385712377040, 'MsgSeq': 871407234}
2025-07-29 00:06:04 | INFO | 收到文本消息: 消息ID:104802475 来自:***********@chatroom 发送人:wxid_idzryo4rneok22 @:[] 内容:水
2025-07-29 00:06:04 | DEBUG | 处理消息内容: '水'
2025-07-29 00:06:04 | DEBUG | 消息内容 '水' 不匹配任何命令，忽略
2025-07-29 00:06:06 | DEBUG | 收到消息: {'MsgId': 546502059, 'FromUserName': {'string': '***********@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 1, 'Content': {'string': 'wxid_ikxxrwasicud11:\n看看别人，看看你'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1753718765, 'MsgSource': '<msgsource>\n\t<sec_msg_node>\n\t\t<alnode>\n\t\t\t<fr>1</fr>\n\t\t</alnode>\n\t</sec_msg_node>\n\t<pua>1</pua>\n\t<silence>1</silence>\n\t<membercount>144</membercount>\n\t<signature>N0_V1_fixsA5gk|v1_2dvpfdu3</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'NewMsgId': 6719103921539222519, 'MsgSeq': 871407235}
2025-07-29 00:06:06 | INFO | 收到文本消息: 消息ID:546502059 来自:***********@chatroom 发送人:wxid_ikxxrwasicud11 @:[] 内容:看看别人，看看你
2025-07-29 00:06:06 | DEBUG | 处理消息内容: '看看别人，看看你'
2025-07-29 00:06:06 | DEBUG | 消息内容 '看看别人，看看你' 不匹配任何命令，忽略
2025-07-29 00:06:09 | DEBUG | 收到消息: {'MsgId': 1283958268, 'FromUserName': {'string': '***********@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 1, 'Content': {'string': 'wxid_snv13qf05qjx11:\n那不能'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1753718765, 'MsgSource': '<msgsource>\n\t<alnode>\n\t\t<fr>1</fr>\n\t</alnode>\n\t<silence>1</silence>\n\t<membercount>144</membercount>\n\t<signature>N0_V1_FnK977ST|v1_D9b6i9dt</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'NewMsgId': 5493390341927015999, 'MsgSeq': 871407236}
2025-07-29 00:06:09 | INFO | 收到文本消息: 消息ID:1283958268 来自:***********@chatroom 发送人:wxid_snv13qf05qjx11 @:[] 内容:那不能
2025-07-29 00:06:09 | DEBUG | 处理消息内容: '那不能'
2025-07-29 00:06:09 | DEBUG | 消息内容 '那不能' 不匹配任何命令，忽略
2025-07-29 00:06:12 | DEBUG | 收到消息: {'MsgId': 1063383823, 'FromUserName': {'string': '***********@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 1, 'Content': {'string': 'wxid_snv13qf05qjx11:\n你能买块冰'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1753718777, 'MsgSource': '<msgsource>\n\t<alnode>\n\t\t<fr>1</fr>\n\t</alnode>\n\t<silence>1</silence>\n\t<membercount>144</membercount>\n\t<signature>N0_V1_flHfN22/|v1_Cu7ESimM</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'NewMsgId': 4161690052967373121, 'MsgSeq': 871407237}
2025-07-29 00:06:12 | INFO | 收到文本消息: 消息ID:1063383823 来自:***********@chatroom 发送人:wxid_snv13qf05qjx11 @:[] 内容:你能买块冰
2025-07-29 00:06:12 | DEBUG | 处理消息内容: '你能买块冰'
2025-07-29 00:06:12 | DEBUG | 消息内容 '你能买块冰' 不匹配任何命令，忽略
2025-07-29 00:06:17 | DEBUG | 收到消息: {'MsgId': 1065788482, 'FromUserName': {'string': '***********@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 47, 'Content': {'string': 'wxid_ugv5ryus4gz622:\n<msg><emoji fromusername="wxid_ugv5ryus4gz622" tousername="***********@chatroom" type="2" idbuffer="media:0_0" md5="c03582e709ab8f2fd1f675fdd572e8ac" len="24712" productid="" androidmd5="c03582e709ab8f2fd1f675fdd572e8ac" androidlen="24712" s60v3md5="c03582e709ab8f2fd1f675fdd572e8ac" s60v3len="24712" s60v5md5="c03582e709ab8f2fd1f675fdd572e8ac" s60v5len="24712" cdnurl="http://vweixinf.tc.qq.com/110/20401/stodownload?m=c03582e709ab8f2fd1f675fdd572e8ac&amp;filekey=3043020101042f302d02016e040253480420633033353832653730396162386632666431663637356664643537326538616302026088040d00000004627466730000000132&amp;hy=SH&amp;storeid=2668f5a9c000d856a6ba618260000006e01004fb153481ac60b01e6c2622d4&amp;ef=1&amp;bizid=1022" designerid="" thumburl="" encrypturl="http://vweixinf.tc.qq.com/110/20402/stodownload?m=1730fa83bb082bb7885a6dff2ee44059&amp;filekey=3043020101042f302d02016e040253480420313733306661383362623038326262373838356136646666326565343430353902026090040d00000004627466730000000132&amp;hy=SH&amp;storeid=2668f5a9c000e051d6ba618260000006e02004fb253481ac60b01e6c2622eb&amp;ef=2&amp;bizid=1022" aeskey="afe5ceaf8cc04a84a6bba1164ce9c109" externurl="http://vweixinf.tc.qq.com/110/20403/stodownload?m=d51e1e839b5502fbc263ce4fac0c31e5&amp;filekey=3043020101042f302d02016e0402534804206435316531653833396235353032666263323633636534666163306333316535020211a0040d00000004627466730000000132&amp;hy=SH&amp;storeid=2668f5a9c000e9cac6ba618260000006e03004fb353481ac60b01e6c2622f2&amp;ef=3&amp;bizid=1022" externmd5="545b61c415cae948d1b984620ba9a6ce" width="240" height="240" tpurl="" tpauthkey="" attachedtext="" attachedtextcolor="" lensid="" emojiattr="" linkid="" desc=""></emoji><gameext type="0" content="0"></gameext></msg>'}, 'Status': 3, 'ImgStatus': 2, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1753718783, 'MsgSource': '<msgsource>\n\t<silence>1</silence>\n\t<membercount>144</membercount>\n\t<signature>N0_V1_lxFVd+S3|v1_VQBBM/KL</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'NewMsgId': 5264640963045277920, 'MsgSeq': 871407238}
2025-07-29 00:06:17 | INFO | 收到表情消息: 消息ID:1065788482 来自:***********@chatroom 发送人:wxid_ugv5ryus4gz622 MD5:c03582e709ab8f2fd1f675fdd572e8ac 大小:24712
2025-07-29 00:06:17 | DEBUG | [DoubaoImageRecognition] 缓存表情消息: 5264640963045277920
2025-07-29 00:06:26 | DEBUG | 收到消息: {'MsgId': 1589121821, 'FromUserName': {'string': '***********@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 47, 'Content': {'string': 'wxid_ikxxrwasicud11:\n<msg><emoji fromusername = "wxid_ikxxrwasicud11" tousername = "***********@chatroom" type="1" idbuffer="media:0_0" md5="f5e16e61c7ecb72feb5bd60745d18be0" len = "6592" productid="" androidmd5="f5e16e61c7ecb72feb5bd60745d18be0" androidlen="6592" s60v3md5 = "f5e16e61c7ecb72feb5bd60745d18be0" s60v3len="6592" s60v5md5 = "f5e16e61c7ecb72feb5bd60745d18be0" s60v5len="6592" cdnurl = "http://vweixinf.tc.qq.com/110/20401/stodownload?m=f5e16e61c7ecb72feb5bd60745d18be0&amp;filekey=3043020101042f302d02016e0402535a04206635653136653631633765636237326665623562643630373435643138626530020219c0040d00000004627466730000000132&amp;hy=SZ&amp;storeid=267d7a23a000110651912ee240000006e01004fb1535a18371541570b3e6aa&amp;ef=1&amp;bizid=1022" designerid = "" thumburl = "" encrypturl = "http://vweixinf.tc.qq.com/110/20402/stodownload?m=7f4fa4ba8e77fc249e06c3cbcd012bd1&amp;filekey=3043020101042f302d02016e0402535a04203766346661346261386537376663323439653036633363626364303132626431020219d0040d00000004627466730000000132&amp;hy=SZ&amp;storeid=267d7a23a0001a2f61912ee240000006e02004fb2535a18371541570b3e6b3&amp;ef=2&amp;bizid=1022" aeskey= "b3ac3ccf17344ffcace68f74ff8a730e" externurl = "http://vweixinf.tc.qq.com/110/20403/stodownload?m=217ee14044bc4ac3ba9dbc62a7709e6d&amp;filekey=3043020101042f302d02016e0402535a0420323137656531343034346263346163336261396462633632613737303965366402020310040d00000004627466730000000132&amp;hy=SZ&amp;storeid=267d7a23a00023e6d1912ee240000006e03004fb3535a18371541570b3e6ba&amp;ef=3&amp;bizid=1022" externmd5 = "66445af48bf096cb507d15f7b123a36b" width= "90" height= "90" tpurl= "" tpauthkey= "" attachedtext= "" attachedtextcolor= "" lensid= "" emojiattr= "" linkid= "" desc= "" ></emoji>  </msg>'}, 'Status': 3, 'ImgStatus': 2, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1753718793, 'MsgSource': '<sec_msg_node>\n\t<alnode>\n\t\t<fr>2</fr>\n\t</alnode>\n</sec_msg_node>\n<msgsource>\n\t<silence>1</silence>\n\t<membercount>144</membercount>\n\t<signature>N0_V1_oTP6Z81m|v1_jygrSoGO</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'NewMsgId': 4059990486503589130, 'MsgSeq': 871407239}
2025-07-29 00:06:26 | INFO | 收到表情消息: 消息ID:1589121821 来自:***********@chatroom 发送人:wxid_ikxxrwasicud11 MD5:f5e16e61c7ecb72feb5bd60745d18be0 大小:6592
2025-07-29 00:06:26 | DEBUG | [DoubaoImageRecognition] 缓存表情消息: 4059990486503589130
2025-07-29 00:06:36 | DEBUG | 收到消息: {'MsgId': 1906716774, 'FromUserName': {'string': '***********@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 1, 'Content': {'string': 'wxid_ugv5ryus4gz622:\n我用个劵能买块冰碴'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1753718803, 'MsgSource': '<msgsource>\n\t<bizflag>0</bizflag>\n\t<pua>1</pua>\n\t<eggIncluded>1</eggIncluded>\n\t<silence>1</silence>\n\t<membercount>144</membercount>\n\t<signature>N0_V1_Jabubi0o|v1_Yudv5qia</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'NewMsgId': 7139466701041046644, 'MsgSeq': 871407240}
2025-07-29 00:06:36 | INFO | 收到文本消息: 消息ID:1906716774 来自:***********@chatroom 发送人:wxid_ugv5ryus4gz622 @:[] 内容:我用个劵能买块冰碴
2025-07-29 00:06:36 | DEBUG | 处理消息内容: '我用个劵能买块冰碴'
2025-07-29 00:06:36 | DEBUG | 消息内容 '我用个劵能买块冰碴' 不匹配任何命令，忽略
2025-07-29 00:07:02 | DEBUG | 收到消息: {'MsgId': 1220441715, 'FromUserName': {'string': '***********@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 1, 'Content': {'string': 'tianen532965049:\n我能买个盖子'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1753718829, 'MsgSource': '<msgsource>\n\t<bizflag>0</bizflag>\n\t<pua>1</pua>\n\t<eggIncluded>1</eggIncluded>\n\t<silence>1</silence>\n\t<membercount>144</membercount>\n\t<signature>N0_V1_qC16SlBU|v1_PGW4QRVS</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'NewMsgId': 6714825678843528821, 'MsgSeq': 871407241}
2025-07-29 00:07:02 | INFO | 收到文本消息: 消息ID:1220441715 来自:***********@chatroom 发送人:tianen532965049 @:[] 内容:我能买个盖子
2025-07-29 00:07:02 | DEBUG | 处理消息内容: '我能买个盖子'
2025-07-29 00:07:02 | DEBUG | 消息内容 '我能买个盖子' 不匹配任何命令，忽略
2025-07-29 00:07:05 | DEBUG | 收到消息: {'MsgId': 651555499, 'FromUserName': {'string': '***********@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 1, 'Content': {'string': 'wxid_snv13qf05qjx11:\n我去躺板板了'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1753718830, 'MsgSource': '<msgsource>\n\t<alnode>\n\t\t<fr>1</fr>\n\t</alnode>\n\t<silence>1</silence>\n\t<membercount>144</membercount>\n\t<signature>N0_V1_K9gka20W|v1_/g8chsZs</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'NewMsgId': 8981783838799921522, 'MsgSeq': 871407242}
2025-07-29 00:07:05 | INFO | 收到文本消息: 消息ID:651555499 来自:***********@chatroom 发送人:wxid_snv13qf05qjx11 @:[] 内容:我去躺板板了
2025-07-29 00:07:05 | DEBUG | 处理消息内容: '我去躺板板了'
2025-07-29 00:07:05 | DEBUG | 消息内容 '我去躺板板了' 不匹配任何命令，忽略
2025-07-29 00:07:34 | DEBUG | 收到消息: {'MsgId': 1879988996, 'FromUserName': {'string': '***********@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 1, 'Content': {'string': 'wxid_ugv5ryus4gz622:\n@夕未语\u2005咱俩凑一杯'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1753718861, 'MsgSource': '<msgsource>\n\t<atuserlist>wxid_snv13qf05qjx11</atuserlist>\n\t<bizflag>0</bizflag>\n\t<pua>1</pua>\n\t<eggIncluded>1</eggIncluded>\n\t<silence>1</silence>\n\t<membercount>144</membercount>\n\t<signature>N0_V1_SmHpoRVb|v1_wuTa/ZoQ</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'NewMsgId': 5496927200384611548, 'MsgSeq': 871407243}
2025-07-29 00:07:34 | INFO | 收到文本消息: 消息ID:1879988996 来自:***********@chatroom 发送人:wxid_ugv5ryus4gz622 @:['wxid_snv13qf05qjx11'] 内容:@夕未语 咱俩凑一杯
2025-07-29 00:07:34 | DEBUG | 处理消息内容: '@夕未语 咱俩凑一杯'
2025-07-29 00:07:34 | DEBUG | 消息内容 '@夕未语 咱俩凑一杯' 不匹配任何命令，忽略
2025-07-29 00:07:37 | DEBUG | 收到消息: {'MsgId': 345298881, 'FromUserName': {'string': '***********@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 1, 'Content': {'string': 'wxid_ugv5ryus4gz622:\n我给你出冰'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1753718864, 'MsgSource': '<msgsource>\n\t<bizflag>0</bizflag>\n\t<pua>1</pua>\n\t<eggIncluded>1</eggIncluded>\n\t<silence>1</silence>\n\t<membercount>144</membercount>\n\t<signature>N0_V1_zhF1k00r|v1_4wmRhO/L</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'NewMsgId': 1768080146711541285, 'MsgSeq': 871407244}
2025-07-29 00:07:37 | INFO | 收到文本消息: 消息ID:345298881 来自:***********@chatroom 发送人:wxid_ugv5ryus4gz622 @:[] 内容:我给你出冰
2025-07-29 00:07:38 | DEBUG | 处理消息内容: '我给你出冰'
2025-07-29 00:07:38 | DEBUG | 消息内容 '我给你出冰' 不匹配任何命令，忽略
2025-07-29 00:07:47 | DEBUG | 收到消息: {'MsgId': 1294047259, 'FromUserName': {'string': '***********@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 1, 'Content': {'string': 'wxid_snv13qf05qjx11:\n不得'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1753718874, 'MsgSource': '<msgsource>\n\t<bizflag>0</bizflag>\n\t<pua>1</pua>\n\t<eggIncluded>1</eggIncluded>\n\t<silence>1</silence>\n\t<membercount>144</membercount>\n\t<signature>N0_V1_qDnNNKEa|v1_I3gJkoDW</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'NewMsgId': 2243356677858424545, 'MsgSeq': 871407245}
2025-07-29 00:07:47 | INFO | 收到文本消息: 消息ID:1294047259 来自:***********@chatroom 发送人:wxid_snv13qf05qjx11 @:[] 内容:不得
2025-07-29 00:07:47 | DEBUG | 处理消息内容: '不得'
2025-07-29 00:07:47 | DEBUG | 消息内容 '不得' 不匹配任何命令，忽略
2025-07-29 00:08:06 | DEBUG | 收到消息: {'MsgId': 1627370362, 'FromUserName': {'string': '***********@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 1, 'Content': {'string': 'wxid_snv13qf05qjx11:\n我喝常温的也行'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1753718893, 'MsgSource': '<msgsource>\n\t<bizflag>0</bizflag>\n\t<pua>1</pua>\n\t<eggIncluded>1</eggIncluded>\n\t<silence>1</silence>\n\t<membercount>144</membercount>\n\t<signature>N0_V1_gjJeq+7f|v1_wClfhrvY</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'NewMsgId': 149838690391455081, 'MsgSeq': 871407246}
2025-07-29 00:08:06 | INFO | 收到文本消息: 消息ID:1627370362 来自:***********@chatroom 发送人:wxid_snv13qf05qjx11 @:[] 内容:我喝常温的也行
2025-07-29 00:08:06 | DEBUG | 处理消息内容: '我喝常温的也行'
2025-07-29 00:08:06 | DEBUG | 消息内容 '我喝常温的也行' 不匹配任何命令，忽略
2025-07-29 00:08:17 | DEBUG | 收到消息: {'MsgId': 448283361, 'FromUserName': {'string': '***********@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 1, 'Content': {'string': 'wxid_snv13qf05qjx11:\n哈哈哈哈哈哈哈'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1753718904, 'MsgSource': '<msgsource>\n\t<bizflag>0</bizflag>\n\t<pua>1</pua>\n\t<alnode>\n\t\t<inlenlist>7</inlenlist>\n\t</alnode>\n\t<eggIncluded>1</eggIncluded>\n\t<silence>1</silence>\n\t<membercount>144</membercount>\n\t<signature>N0_V1_Ru2GfLtl|v1_6gKgPcJv</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'NewMsgId': 4008460484363342110, 'MsgSeq': 871407247}
2025-07-29 00:08:17 | INFO | 收到文本消息: 消息ID:448283361 来自:***********@chatroom 发送人:wxid_snv13qf05qjx11 @:[] 内容:哈哈哈哈哈哈哈
2025-07-29 00:08:17 | DEBUG | 处理消息内容: '哈哈哈哈哈哈哈'
2025-07-29 00:08:17 | DEBUG | 消息内容 '哈哈哈哈哈哈哈' 不匹配任何命令，忽略
2025-07-29 00:08:20 | DEBUG | 收到消息: {'MsgId': 1210462396, 'FromUserName': {'string': '***********@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 1, 'Content': {'string': 'wxid_ugv5ryus4gz622:\n你咋这样式儿的呢你'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1753718906, 'MsgSource': '<msgsource>\n\t<bizflag>0</bizflag>\n\t<pua>1</pua>\n\t<alnode>\n\t\t<inlenlist>7</inlenlist>\n\t</alnode>\n\t<eggIncluded>1</eggIncluded>\n\t<silence>1</silence>\n\t<membercount>144</membercount>\n\t<signature>N0_V1_6iFtNon7|v1_b64qmi1h</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'NewMsgId': 9203813488712677390, 'MsgSeq': 871407248}
2025-07-29 00:08:20 | INFO | 收到文本消息: 消息ID:1210462396 来自:***********@chatroom 发送人:wxid_ugv5ryus4gz622 @:[] 内容:你咋这样式儿的呢你
2025-07-29 00:08:20 | DEBUG | 处理消息内容: '你咋这样式儿的呢你'
2025-07-29 00:08:20 | DEBUG | 消息内容 '你咋这样式儿的呢你' 不匹配任何命令，忽略
2025-07-29 00:08:42 | DEBUG | 收到消息: {'MsgId': 464245991, 'FromUserName': {'string': '***********@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 47, 'Content': {'string': 'wxid_snv13qf05qjx11:\n<msg><emoji fromusername = "wxid_snv13qf05qjx11" tousername = "***********@chatroom" type="2" idbuffer="media:0_0" md5="61eba683bf4b4699134a3922958f01ed" len = "57264" productid="" androidmd5="61eba683bf4b4699134a3922958f01ed" androidlen="57264" s60v3md5 = "61eba683bf4b4699134a3922958f01ed" s60v3len="57264" s60v5md5 = "61eba683bf4b4699134a3922958f01ed" s60v5len="57264" cdnurl = "http://vweixinf.tc.qq.com/110/20401/stodownload?m=61eba683bf4b4699134a3922958f01ed&amp;filekey=30440201010430302e02016e0402534804203631656261363833626634623436393931333461333932323935386630316564020300dfb0040d00000004627466730000000132&amp;hy=SH&amp;storeid=267506d6b000199aa5c241f440000006e01004fb153480e089bc1e71911d62&amp;ef=1&amp;bizid=1022" designerid = "" thumburl = "" encrypturl = "http://vweixinf.tc.qq.com/110/20402/stodownload?m=9e48b794d8a5b82aece7a159686992b0&amp;filekey=30440201010430302e02016e0402534804203965343862373934643861356238326165636537613135393638363939326230020300dfc0040d00000004627466730000000132&amp;hy=SH&amp;storeid=267506d6b000240935c241f440000006e02004fb253480e089bc1e71911d6f&amp;ef=2&amp;bizid=1022" aeskey= "a979c560f84741c3bd4a07158dd7e33f" externurl = "" externmd5 = "" width= "240" height= "240" tpurl= "" tpauthkey= "" attachedtext= "" attachedtextcolor= "" lensid= "" emojiattr= "" linkid= "" desc= "" ></emoji> <gameext type="0" content="0" ></gameext> </msg>'}, 'Status': 3, 'ImgStatus': 2, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1753718929, 'MsgSource': '<msgsource>\n\t<silence>1</silence>\n\t<membercount>144</membercount>\n\t<signature>N0_V1_n9/ACe1i|v1_jVmJtR7w</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'NewMsgId': 8237955923520908727, 'MsgSeq': 871407249}
2025-07-29 00:08:42 | INFO | 收到表情消息: 消息ID:464245991 来自:***********@chatroom 发送人:wxid_snv13qf05qjx11 MD5:61eba683bf4b4699134a3922958f01ed 大小:57264
2025-07-29 00:08:42 | DEBUG | [DoubaoImageRecognition] 缓存表情消息: 8237955923520908727
2025-07-29 00:10:13 | DEBUG | 收到消息: {'MsgId': 305548406, 'FromUserName': {'string': '48097389945@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 1, 'Content': {'string': 'wxid_laurnst5xn0q22:\n@-\u2005求婚'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1753719020, 'MsgSource': '<msgsource>\n\t<atuserlist><![CDATA[,chichengqianwanli]]></atuserlist>\n\t<silence>0</silence>\n\t<membercount>73</membercount>\n\t<signature>N0_V1_34PH+oy5|v1_khSyO/k2</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'PushContent': 'متسول暗 : @-\u2005求婚', 'NewMsgId': 4144051591595299008, 'MsgSeq': 871407250}
2025-07-29 00:10:13 | INFO | 收到文本消息: 消息ID:305548406 来自:48097389945@chatroom 发送人:wxid_laurnst5xn0q22 @:['chichengqianwanli'] 内容:@- 求婚
2025-07-29 00:10:13 | DEBUG | 处理消息内容: '@- 求婚'
2025-07-29 00:10:13 | DEBUG | 消息内容 '@- 求婚' 不匹配任何命令，忽略
2025-07-29 00:10:17 | DEBUG | 收到消息: {'MsgId': 1834195927, 'FromUserName': {'string': '48097389945@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 1, 'Content': {'string': 'wxid_q35rkzgkjvlv12:\n@متسول暗 魅力太低了，无法求婚，需要魅力达到1000才可以求婚'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1753719021, 'MsgSource': '<msgsource>\n\t<silence>0</silence>\n\t<membercount>73</membercount>\n\t<signature>N0_V1_kuPmQEsU|v1_XirGPbkp</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'PushContent': '摇月 : @متسول暗 魅力太低了，无法求婚，需要魅力达到1000才可以求婚', 'NewMsgId': 3110241065970521698, 'MsgSeq': 871407251}
2025-07-29 00:10:17 | INFO | 收到文本消息: 消息ID:1834195927 来自:48097389945@chatroom 发送人:wxid_q35rkzgkjvlv12 @:[] 内容:@متسول暗 魅力太低了，无法求婚，需要魅力达到1000才可以求婚
2025-07-29 00:10:17 | DEBUG | 处理消息内容: '@متسول暗 魅力太低了，无法求婚，需要魅力达到1000才可以求婚'
2025-07-29 00:10:17 | DEBUG | 消息内容 '@متسول暗 魅力太低了，无法求婚，需要魅力达到1000才可以求婚' 不匹配任何命令，忽略
2025-07-29 00:10:20 | DEBUG | 收到消息: {'MsgId': 1128836250, 'FromUserName': {'string': '48097389945@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 1, 'Content': {'string': 'wxid_8l9ymg1mafud12:\n====公告====\n「متسول暗」向「-」求婚了！让我们祝TA成功！么么哒！\n24小时内求婚未得到反馈自动失效哦！'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1753719021, 'MsgSource': '<msgsource>\n\t<silence>0</silence>\n\t<membercount>73</membercount>\n\t<signature>N0_V1_ot7KG3qw|v1_4pX5v8z7</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'PushContent': '不吃香菜\ue110 : ====公告====\n「متسول暗」向「-」求婚了！让我们祝TA成功！么么哒...', 'NewMsgId': 785069444392125108, 'MsgSeq': 871407252}
2025-07-29 00:10:20 | INFO | 收到文本消息: 消息ID:1128836250 来自:48097389945@chatroom 发送人:wxid_8l9ymg1mafud12 @:[] 内容:====公告====
「متسول暗」向「-」求婚了！让我们祝TA成功！么么哒！
24小时内求婚未得到反馈自动失效哦！
2025-07-29 00:10:20 | DEBUG | 处理消息内容: '====公告====
「متسول暗」向「-」求婚了！让我们祝TA成功！么么哒！
24小时内求婚未得到反馈自动失效哦！'
2025-07-29 00:10:20 | DEBUG | 消息内容 '====公告====
「متسول暗」向「-」求婚了！让我们祝TA成功！么么哒！
24小时内求婚未得到反馈自动失效哦！' 不匹配任何命令，忽略
2025-07-29 00:10:23 | DEBUG | 收到消息: {'MsgId': 145941702, 'FromUserName': {'string': '48097389945@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 1, 'Content': {'string': 'wxid_8l9ymg1mafud12:\n@-\u2005\r\n有人向你求婚了哦！你可以发送【答应求婚】或者 【拒绝求婚】'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1753719022, 'MsgSource': '<msgsource>\n\t<atuserlist>chichengqianwanli</atuserlist>\n\t<silence>0</silence>\n\t<membercount>73</membercount>\n\t<signature>N0_V1_BiXY+0Oi|v1_c5gaoT5D</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'PushContent': '不吃香菜\ue110 : @-\u2005\r\n有人向你求婚了哦！你可以发送【答应求婚】或者 【拒绝求婚...', 'NewMsgId': 843746688731721986, 'MsgSeq': 871407253}
2025-07-29 00:10:23 | INFO | 收到文本消息: 消息ID:145941702 来自:48097389945@chatroom 发送人:wxid_8l9ymg1mafud12 @:['chichengqianwanli'] 内容:@- 

有人向你求婚了哦！你可以发送【答应求婚】或者 【拒绝求婚】
2025-07-29 00:10:24 | DEBUG | 处理消息内容: '@- 

有人向你求婚了哦！你可以发送【答应求婚】或者 【拒绝求婚】'
2025-07-29 00:10:24 | DEBUG | 消息内容 '@- 

有人向你求婚了哦！你可以发送【答应求婚】或者 【拒绝求婚】' 不匹配任何命令，忽略
2025-07-29 00:10:26 | DEBUG | 收到消息: {'MsgId': 1300774082, 'FromUserName': {'string': '48097389945@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 34, 'Content': {'string': 'wxid_8l9ymg1mafud12:\n<msg><voicemsg endflag="1" cancelflag="0" forwardflag="0" voiceformat="4" voicelength="24314" length="77807" bufid="0" aeskey="6a69676c72696c6a747970666c706d62" voiceurl="3052020100044b3049020100020499f11ce202033d11fe020466f765b402046887a0f0042466396237393439352d323563372d346636342d623161322d32323337356439643665386102040524000f0201000400b67695f5" voicemd5="41dab9c5e415242421e43a4ef2af087a" clientmsgid="49670b6b5b2812a9d0b604c57261570448097389945@chatroom23_1753719023" fromusername="wxid_8l9ymg1mafud12" /></msg>'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1753719024, 'MsgSource': '<msgsource>\n\t<silence>0</silence>\n\t<membercount>73</membercount>\n\t<signature>N0_V1_PKJ/ng6U|v1_yWDydw9w</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'PushContent': '不吃香菜\ue110在群聊中发了一段语音', 'NewMsgId': 8729448967789060475, 'MsgSeq': 871407254}
2025-07-29 00:10:26 | INFO | 收到语音消息: 消息ID:1300774082 来自:48097389945@chatroom 发送人:wxid_8l9ymg1mafud12 XML:
<msg><voicemsg endflag="1" cancelflag="0" forwardflag="0" voiceformat="4" voicelength="24314" length="77807" bufid="0" aeskey="6a69676c72696c6a747970666c706d62" voiceurl="3052020100044b3049020100020499f11ce202033d11fe020466f765b402046887a0f0042466396237393439352d323563372d346636342d623161322d32323337356439643665386102040524000f0201000400b67695f5" voicemd5="41dab9c5e415242421e43a4ef2af087a" clientmsgid="49670b6b5b2812a9d0b604c57261570448097389945@chatroom23_1753719023" fromusername="wxid_8l9ymg1mafud12" /></msg>
2025-07-29 00:10:37 | DEBUG | 收到消息: {'MsgId': 57662877, 'FromUserName': {'string': '48097389945@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 43, 'Content': {'string': 'wxid_laurnst5xn0q22:\n<?xml version="1.0"?>\n<msg>\n\t<videomsg aeskey="80308a532f168159ab925d28a501f81a" cdnvideourl="3057020100044b30490201000204c7128a1c02032f57ed0204e388e67202046887a103042466616330623338362d363063362d343237382d623434642d3761393333633636393737610204052808040201000405004c57c100" cdnthumbaeskey="80308a532f168159ab925d28a501f81a" cdnthumburl="3057020100044b30490201000204c7128a1c02032f57ed0204e388e67202046887a103042466616330623338362d363063362d343237382d623434642d3761393333633636393737610204052808040201000405004c57c100" length="3023445" playlength="14" cdnthumblength="12086" cdnthumbwidth="0" cdnthumbheight="0" fromusername="wxid_laurnst5xn0q22" md5="1cd83b5a696c76e3efb0d2e9b2c97920" newmd5="53961ba581748ce45343b0905293aef7" isplaceholder="0" rawmd5="b1f26f9967a8f5d5e31ce99aec605f0b" rawlength="3006669" cdnrawvideourl="3057020100044b3049020100020495529cdb02033d12000204cee8c37602046887a0b6042436383038393430652d373932612d346230312d393830342d6236646339333337303338640204051408040201000405004c4d3500" cdnrawvideoaeskey="ede7aab793d0aed274bddd12c19494ed" overwritenewmsgid="0" originsourcemd5="b1f26f9967a8f5d5e31ce99aec605f0b" isad="0" />\n</msg>\n'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1753719044, 'MsgSource': '<msgsource>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n\t<sec_msg_node>\n\t\t<uuid>9275197dabcb6a9f9b8f03b667a6c047_</uuid>\n\t\t<risk-file-flag />\n\t\t<risk-file-md5-list />\n\t</sec_msg_node>\n\t<silence>0</silence>\n\t<membercount>73</membercount>\n\t<signature>N0_V1_inMniig8|v1_0N99rfiI</signature>\n</msgsource>\n', 'PushContent': 'متسول暗在群聊中发了一段视频', 'NewMsgId': 7997982310281055417, 'MsgSeq': 871407255}
2025-07-29 00:10:37 | INFO | 收到视频消息: 消息ID:57662877 来自:48097389945@chatroom 发送人:wxid_laurnst5xn0q22 XML:
<?xml version="1.0"?>
<msg>
	<videomsg aeskey="80308a532f168159ab925d28a501f81a" cdnvideourl="3057020100044b30490201000204c7128a1c02032f57ed0204e388e67202046887a103042466616330623338362d363063362d343237382d623434642d3761393333633636393737610204052808040201000405004c57c100" cdnthumbaeskey="80308a532f168159ab925d28a501f81a" cdnthumburl="3057020100044b30490201000204c7128a1c02032f57ed0204e388e67202046887a103042466616330623338362d363063362d343237382d623434642d3761393333633636393737610204052808040201000405004c57c100" length="3023445" playlength="14" cdnthumblength="12086" cdnthumbwidth="0" cdnthumbheight="0" fromusername="wxid_laurnst5xn0q22" md5="1cd83b5a696c76e3efb0d2e9b2c97920" newmd5="53961ba581748ce45343b0905293aef7" isplaceholder="0" rawmd5="b1f26f9967a8f5d5e31ce99aec605f0b" rawlength="3006669" cdnrawvideourl="3057020100044b3049020100020495529cdb02033d12000204cee8c37602046887a0b6042436383038393430652d373932612d346230312d393830342d6236646339333337303338640204051408040201000405004c4d3500" cdnrawvideoaeskey="ede7aab793d0aed274bddd12c19494ed" overwritenewmsgid="0" originsourcemd5="b1f26f9967a8f5d5e31ce99aec605f0b" isad="0" />
</msg>

2025-07-29 00:10:45 | DEBUG | 收到消息: {'MsgId': 939546449, 'FromUserName': {'string': '***********@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 1, 'Content': {'string': 'wxid_ugv5ryus4gz622:\n早点睡觉 明天咱们继续'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1753719052, 'MsgSource': '<msgsource>\n\t<bizflag>0</bizflag>\n\t<pua>1</pua>\n\t<eggIncluded>1</eggIncluded>\n\t<silence>1</silence>\n\t<membercount>144</membercount>\n\t<signature>N0_V1_Dp3QU1u9|v1_qXQgwnkl</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'NewMsgId': 5561223714806508498, 'MsgSeq': 871407256}
2025-07-29 00:10:45 | INFO | 收到文本消息: 消息ID:939546449 来自:***********@chatroom 发送人:wxid_ugv5ryus4gz622 @:[] 内容:早点睡觉 明天咱们继续
2025-07-29 00:10:45 | DEBUG | 处理消息内容: '早点睡觉 明天咱们继续'
2025-07-29 00:10:45 | DEBUG | 消息内容 '早点睡觉 明天咱们继续' 不匹配任何命令，忽略
2025-07-29 00:11:18 | DEBUG | 收到消息: {'MsgId': 457538443, 'FromUserName': {'string': '***********@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 1, 'Content': {'string': 'wxid_idzryo4rneok22:\n明天下午我可以'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1753719085, 'MsgSource': '<msgsource>\n\t<pua>1</pua>\n\t<eggIncluded>1</eggIncluded>\n\t<silence>1</silence>\n\t<membercount>144</membercount>\n\t<signature>N0_V1_MGU1OVDo|v1_FkW7ths3</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'NewMsgId': 5285203649476694106, 'MsgSeq': 871407257}
2025-07-29 00:11:18 | INFO | 收到文本消息: 消息ID:457538443 来自:***********@chatroom 发送人:wxid_idzryo4rneok22 @:[] 内容:明天下午我可以
2025-07-29 00:11:18 | DEBUG | 处理消息内容: '明天下午我可以'
2025-07-29 00:11:18 | DEBUG | 消息内容 '明天下午我可以' 不匹配任何命令，忽略
2025-07-29 00:11:25 | DEBUG | 收到消息: {'MsgId': 1702781768, 'FromUserName': {'string': '***********@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 1, 'Content': {'string': 'wxid_idzryo4rneok22:\n上午要带娃'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1753719091, 'MsgSource': '<msgsource>\n\t<pua>1</pua>\n\t<eggIncluded>1</eggIncluded>\n\t<silence>1</silence>\n\t<membercount>144</membercount>\n\t<signature>N0_V1_a6T8FwqW|v1_u1ea83lF</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'NewMsgId': 1692038486075303709, 'MsgSeq': 871407258}
2025-07-29 00:11:25 | INFO | 收到文本消息: 消息ID:1702781768 来自:***********@chatroom 发送人:wxid_idzryo4rneok22 @:[] 内容:上午要带娃
2025-07-29 00:11:25 | DEBUG | 处理消息内容: '上午要带娃'
2025-07-29 00:11:25 | DEBUG | 消息内容 '上午要带娃' 不匹配任何命令，忽略
2025-07-29 00:11:52 | DEBUG | 收到消息: {'MsgId': 615736664, 'FromUserName': {'string': '***********@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 1, 'Content': {'string': 'wxid_ikxxrwasicud11:\n唱舞签到'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1753719119, 'MsgSource': '<msgsource>\n\t<sec_msg_node>\n\t\t<alnode>\n\t\t\t<fr>1</fr>\n\t\t</alnode>\n\t</sec_msg_node>\n\t<pua>1</pua>\n\t<silence>1</silence>\n\t<membercount>144</membercount>\n\t<signature>N0_V1_0ei+J1PG|v1_oylZ7R3b</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'NewMsgId': 2289402711782857241, 'MsgSeq': 871407259}
2025-07-29 00:11:52 | INFO | 收到文本消息: 消息ID:615736664 来自:***********@chatroom 发送人:wxid_ikxxrwasicud11 @:[] 内容:唱舞签到
2025-07-29 00:11:53 | INFO | 发送链接消息: 对方wxid:***********@chatroom 链接:https://open.weixin.qq.com/connect/oauth2/authorize?appid=wx6de432d4ad7e151c&redirect_uri=http%3A%2F%2Freserve.fhsj.xipu.com%2Fapi%2Fsign%2Findex&response_type=code&scope=snsapi_base&state=STATE#wechat_redirect 标题:唱舞全明星 描述:点击进入签到页面，领取专属福利 缩略图链接:https://mmbiz.qpic.cn/mmbiz_jpg/RicuJvxibRtUBlv9G75UTulanktDF1OxFO7Wyhzs1WS609tq1j9icfNhLkM6zB3lwM5ZlbgQia1ibIcxuj35WAm465w/640?wxtype=jpeg&wxfrom=0
2025-07-29 00:11:53 | DEBUG | 处理消息内容: '唱舞签到'
2025-07-29 00:11:53 | DEBUG | 消息内容 '唱舞签到' 不匹配任何命令，忽略
2025-07-29 00:12:21 | DEBUG | 收到消息: {'MsgId': 90613892, 'FromUserName': {'string': '***********@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 49, 'Content': {'string': 'wxid_ikxxrwasicud11:\n<?xml version="1.0"?>\n<msg>\n\t<appmsg appid="wxa708de63ee4a2353" sdkver="">\n\t\t<title>派对邀请函-签到解锁6周年专属称号</title>\n\t\t<des />\n\t\t<action>view</action>\n\t\t<type>33</type>\n\t\t<showtype>0</showtype>\n\t\t<content />\n\t\t<url>https://mp.weixin.qq.com/mp/waerrpage?appid=wxa708de63ee4a2353&amp;amp;type=upgrade&amp;amp;upgradetype=3#wechat_redirect</url>\n\t\t<dataurl />\n\t\t<lowurl />\n\t\t<lowdataurl />\n\t\t<recorditem />\n\t\t<thumburl />\n\t\t<messageaction />\n\t\t<laninfo />\n\t\t<md5>e131aed86dbf5d42c275812f57ff50cb</md5>\n\t\t<extinfo />\n\t\t<sourceusername />\n\t\t<sourcedisplayname>唱舞星愿站</sourcedisplayname>\n\t\t<commenturl />\n\t\t<appattach>\n\t\t\t<totallen>0</totallen>\n\t\t\t<attachid />\n\t\t\t<emoticonmd5 />\n\t\t\t<fileext>jpg</fileext>\n\t\t\t<filekey>b9b331a5bef6fe88a569a63572f38d16</filekey>\n\t\t\t<cdnthumburl>3057020100044b30490201000204062b4e3002032f5aa902041dab697102046887a16c042437633337393637352d323164372d346338362d626132362d3665393031623562636538300204052808030201000405004c54a100</cdnthumburl>\n\t\t\t<aeskey>cf57d753f9e382fb938af0cbc8986f2d</aeskey>\n\t\t\t<cdnthumbaeskey>cf57d753f9e382fb938af0cbc8986f2d</cdnthumbaeskey>\n\t\t\t<cdnthumbmd5>e131aed86dbf5d42c275812f57ff50cb</cdnthumbmd5>\n\t\t\t<encryver>1</encryver>\n\t\t\t<cdnthumblength>296019</cdnthumblength>\n\t\t\t<cdnthumbheight>100</cdnthumbheight>\n\t\t\t<cdnthumbwidth>100</cdnthumbwidth>\n\t\t</appattach>\n\t\t<webviewshared>\n\t\t\t<publisherId />\n\t\t\t<publisherReqId>0</publisherReqId>\n\t\t</webviewshared>\n\t\t<weappinfo>\n\t\t\t<pagepath>pages/Activity/signIn/index.html?key=jSqgSKXV&amp;inviteId=oA7D81SRhmAwPJuDQ0Pw39LWcWoY&amp;taskId=</pagepath>\n\t\t\t<username>gh_25eb09d7bc53@app</username>\n\t\t\t<appid>wxa708de63ee4a2353</appid>\n\t\t\t<type>2</type>\n\t\t\t<weappiconurl>http://mmbiz.qpic.cn/mmbiz_png/Y4yibCmxrbX6aCNIhj0BH1CXuE8chunB9AN5fnaRJUMRGRicxkh8crn7P1VCQuhV4GB3KCkE4e5ibmXVEibxaoIJuA/640?wx_fmt=png&amp;wxfrom=200</weappiconurl>\n\t\t\t<appservicetype>0</appservicetype>\n\t\t\t<shareId>2_wxa708de63ee4a2353_1004561842_1753719148_1</shareId>\n\t\t</weappinfo>\n\t\t<websearch />\n\t</appmsg>\n\t<fromusername>wxid_ikxxrwasicud11</fromusername>\n\t<scene>0</scene>\n\t<appinfo>\n\t\t<version>2</version>\n\t\t<appname>唱舞星愿站</appname>\n\t</appinfo>\n\t<commenturl></commenturl>\n</msg>\n'}, 'Status': 3, 'ImgStatus': 2, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1753719148, 'MsgSource': '<msgsource>\n\t<sec_msg_node>\n\t\t<uuid>113155a2ed938ca7c60c7a4c0fed81fb_</uuid>\n\t\t<risk-file-flag />\n\t\t<risk-file-md5-list />\n\t</sec_msg_node>\n\t<silence>1</silence>\n\t<membercount>144</membercount>\n\t<signature>N0_V1_pZT2+V89|v1_TKO8kFeR</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'NewMsgId': 7404354160334087078, 'MsgSeq': *********}
2025-07-29 00:12:21 | DEBUG | 从群聊消息中提取发送者: wxid_ikxxrwasicud11
2025-07-29 00:12:21 | DEBUG | XML消息完整内容:
<?xml version="1.0"?>
<msg>
	<appmsg appid="wxa708de63ee4a2353" sdkver="">
		<title>派对邀请函-签到解锁6周年专属称号</title>
		<des />
		<action>view</action>
		<type>33</type>
		<showtype>0</showtype>
		<content />
		<url>https://mp.weixin.qq.com/mp/waerrpage?appid=wxa708de63ee4a2353&amp;amp;type=upgrade&amp;amp;upgradetype=3#wechat_redirect</url>
		<dataurl />
		<lowurl />
		<lowdataurl />
		<recorditem />
		<thumburl />
		<messageaction />
		<laninfo />
		<md5>e131aed86dbf5d42c275812f57ff50cb</md5>
		<extinfo />
		<sourceusername />
		<sourcedisplayname>唱舞星愿站</sourcedisplayname>
		<commenturl />
		<appattach>
			<totallen>0</totallen>
			<attachid />
			<emoticonmd5 />
			<fileext>jpg</fileext>
			<filekey>b9b331a5bef6fe88a569a63572f38d16</filekey>
			<cdnthumburl>3057020100044b30490201000204062b4e3002032f5aa902041dab697102046887a16c042437633337393637352d323164372d346338362d626132362d3665393031623562636538300204052808030201000405004c54a100</cdnthumburl>
			<aeskey>cf57d753f9e382fb938af0cbc8986f2d</aeskey>
			<cdnthumbaeskey>cf57d753f9e382fb938af0cbc8986f2d</cdnthumbaeskey>
			<cdnthumbmd5>e131aed86dbf5d42c275812f57ff50cb</cdnthumbmd5>
			<encryver>1</encryver>
			<cdnthumblength>296019</cdnthumblength>
			<cdnthumbheight>100</cdnthumbheight>
			<cdnthumbwidth>100</cdnthumbwidth>
		</appattach>
		<webviewshared>
			<publisherId />
			<publisherReqId>0</publisherReqId>
		</webviewshared>
		<weappinfo>
			<pagepath>pages/Activity/signIn/index.html?key=jSqgSKXV&amp;inviteId=oA7D81SRhmAwPJuDQ0Pw39LWcWoY&amp;taskId=</pagepath>
			<username>gh_25eb09d7bc53@app</username>
			<appid>wxa708de63ee4a2353</appid>
			<type>2</type>
			<weappiconurl>http://mmbiz.qpic.cn/mmbiz_png/Y4yibCmxrbX6aCNIhj0BH1CXuE8chunB9AN5fnaRJUMRGRicxkh8crn7P1VCQuhV4GB3KCkE4e5ibmXVEibxaoIJuA/640?wx_fmt=png&amp;wxfrom=200</weappiconurl>
			<appservicetype>0</appservicetype>
			<shareId>2_wxa708de63ee4a2353_1004561842_1753719148_1</shareId>
		</weappinfo>
		<websearch />
	</appmsg>
	<fromusername>wxid_ikxxrwasicud11</fromusername>
	<scene>0</scene>
	<appinfo>
		<version>2</version>
		<appname>唱舞星愿站</appname>
	</appinfo>
	<commenturl></commenturl>
</msg>

2025-07-29 00:12:21 | DEBUG | XML消息类型: 33
2025-07-29 00:12:21 | DEBUG | XML消息标题: 派对邀请函-签到解锁6周年专属称号
2025-07-29 00:12:21 | DEBUG | XML消息描述: None
2025-07-29 00:12:21 | DEBUG | 附件信息 totallen: 0
2025-07-29 00:12:21 | DEBUG | 附件信息 fileext: jpg
2025-07-29 00:12:21 | DEBUG | 附件信息 filekey: b9b331a5bef6fe88a569a63572f38d16
2025-07-29 00:12:21 | DEBUG | 附件信息 cdnthumburl: 3057020100044b30490201000204062b4e3002032f5aa902041dab697102046887a16c042437633337393637352d323164372d346338362d626132362d3665393031623562636538300204052808030201000405004c54a100
2025-07-29 00:12:21 | DEBUG | 附件信息 aeskey: cf57d753f9e382fb938af0cbc8986f2d
2025-07-29 00:12:21 | DEBUG | 附件信息 cdnthumbaeskey: cf57d753f9e382fb938af0cbc8986f2d
2025-07-29 00:12:21 | DEBUG | 附件信息 cdnthumbmd5: e131aed86dbf5d42c275812f57ff50cb
2025-07-29 00:12:21 | DEBUG | 附件信息 encryver: 1
2025-07-29 00:12:21 | DEBUG | 附件信息 cdnthumblength: 296019
2025-07-29 00:12:21 | DEBUG | 附件信息 cdnthumbheight: 100
2025-07-29 00:12:21 | DEBUG | 附件信息 cdnthumbwidth: 100
2025-07-29 00:12:21 | DEBUG | XML消息URL: https://mp.weixin.qq.com/mp/waerrpage?appid=wxa708de63ee4a2353&amp;type=upgrade&amp;upgradetype=3#wechat_redirect
2025-07-29 00:12:21 | INFO | 未知的XML消息类型: 33
2025-07-29 00:12:21 | INFO | 消息标题: 派对邀请函-签到解锁6周年专属称号
2025-07-29 00:12:21 | INFO | 消息描述: None
2025-07-29 00:12:21 | INFO | 消息URL: https://mp.weixin.qq.com/mp/waerrpage?appid=wxa708de63ee4a2353&amp;type=upgrade&amp;upgradetype=3#wechat_redirect
2025-07-29 00:12:21 | INFO | 完整XML内容:
<?xml version="1.0"?>
<msg>
	<appmsg appid="wxa708de63ee4a2353" sdkver="">
		<title>派对邀请函-签到解锁6周年专属称号</title>
		<des />
		<action>view</action>
		<type>33</type>
		<showtype>0</showtype>
		<content />
		<url>https://mp.weixin.qq.com/mp/waerrpage?appid=wxa708de63ee4a2353&amp;amp;type=upgrade&amp;amp;upgradetype=3#wechat_redirect</url>
		<dataurl />
		<lowurl />
		<lowdataurl />
		<recorditem />
		<thumburl />
		<messageaction />
		<laninfo />
		<md5>e131aed86dbf5d42c275812f57ff50cb</md5>
		<extinfo />
		<sourceusername />
		<sourcedisplayname>唱舞星愿站</sourcedisplayname>
		<commenturl />
		<appattach>
			<totallen>0</totallen>
			<attachid />
			<emoticonmd5 />
			<fileext>jpg</fileext>
			<filekey>b9b331a5bef6fe88a569a63572f38d16</filekey>
			<cdnthumburl>3057020100044b30490201000204062b4e3002032f5aa902041dab697102046887a16c042437633337393637352d323164372d346338362d626132362d3665393031623562636538300204052808030201000405004c54a100</cdnthumburl>
			<aeskey>cf57d753f9e382fb938af0cbc8986f2d</aeskey>
			<cdnthumbaeskey>cf57d753f9e382fb938af0cbc8986f2d</cdnthumbaeskey>
			<cdnthumbmd5>e131aed86dbf5d42c275812f57ff50cb</cdnthumbmd5>
			<encryver>1</encryver>
			<cdnthumblength>296019</cdnthumblength>
			<cdnthumbheight>100</cdnthumbheight>
			<cdnthumbwidth>100</cdnthumbwidth>
		</appattach>
		<webviewshared>
			<publisherId />
			<publisherReqId>0</publisherReqId>
		</webviewshared>
		<weappinfo>
			<pagepath>pages/Activity/signIn/index.html?key=jSqgSKXV&amp;inviteId=oA7D81SRhmAwPJuDQ0Pw39LWcWoY&amp;taskId=</pagepath>
			<username>gh_25eb09d7bc53@app</username>
			<appid>wxa708de63ee4a2353</appid>
			<type>2</type>
			<weappiconurl>http://mmbiz.qpic.cn/mmbiz_png/Y4yibCmxrbX6aCNIhj0BH1CXuE8chunB9AN5fnaRJUMRGRicxkh8crn7P1VCQuhV4GB3KCkE4e5ibmXVEibxaoIJuA/640?wx_fmt=png&amp;wxfrom=200</weappiconurl>
			<appservicetype>0</appservicetype>
			<shareId>2_wxa708de63ee4a2353_1004561842_1753719148_1</shareId>
		</weappinfo>
		<websearch />
	</appmsg>
	<fromusername>wxid_ikxxrwasicud11</fromusername>
	<scene>0</scene>
	<appinfo>
		<version>2</version>
		<appname>唱舞星愿站</appname>
	</appinfo>
	<commenturl></commenturl>
</msg>

2025-07-29 00:13:03 | DEBUG | 收到消息: {'MsgId': 1109982614, 'FromUserName': {'string': '***********@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 1, 'Content': {'string': 'wxid_snv13qf05qjx11:\n置顶不有么'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1753719190, 'MsgSource': '<msgsource>\n\t<bizflag>0</bizflag>\n\t<pua>1</pua>\n\t<eggIncluded>1</eggIncluded>\n\t<silence>1</silence>\n\t<membercount>144</membercount>\n\t<signature>N0_V1_d8zJBCym|v1_Bjm19LN1</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'NewMsgId': 7688208365017767737, 'MsgSeq': 871407263}
2025-07-29 00:13:03 | INFO | 收到文本消息: 消息ID:1109982614 来自:***********@chatroom 发送人:wxid_snv13qf05qjx11 @:[] 内容:置顶不有么
2025-07-29 00:13:03 | DEBUG | 处理消息内容: '置顶不有么'
2025-07-29 00:13:03 | DEBUG | 消息内容 '置顶不有么' 不匹配任何命令，忽略
2025-07-29 00:13:58 | DEBUG | 收到消息: {'MsgId': 232999874, 'FromUserName': {'string': '***********@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 1, 'Content': {'string': 'wxid_snv13qf05qjx11:\n那显眼包加啥的'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1753719245, 'MsgSource': '<msgsource>\n\t<bizflag>0</bizflag>\n\t<pua>1</pua>\n\t<eggIncluded>1</eggIncluded>\n\t<silence>1</silence>\n\t<membercount>144</membercount>\n\t<signature>N0_V1_cK42fN2q|v1_vGdmnwGD</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'NewMsgId': 7894261284076634551, 'MsgSeq': 871407264}
2025-07-29 00:13:58 | INFO | 收到文本消息: 消息ID:232999874 来自:***********@chatroom 发送人:wxid_snv13qf05qjx11 @:[] 内容:那显眼包加啥的
2025-07-29 00:13:58 | DEBUG | 处理消息内容: '那显眼包加啥的'
2025-07-29 00:13:58 | DEBUG | 消息内容 '那显眼包加啥的' 不匹配任何命令，忽略
2025-07-29 00:14:08 | DEBUG | 收到消息: {'MsgId': 765207156, 'FromUserName': {'string': '***********@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 1, 'Content': {'string': 'wxid_xv01lkcmn48l22:\n我v比较low'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1753719254, 'MsgSource': '<msgsource>\n\t<bizflag>0</bizflag>\n\t<pua>1</pua>\n\t<eggIncluded>1</eggIncluded>\n\t<silence>1</silence>\n\t<membercount>144</membercount>\n\t<signature>N0_V1_CdJXFFxx|v1_uu+oi+0S</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'NewMsgId': 6638856302029620615, 'MsgSeq': 871407265}
2025-07-29 00:14:08 | INFO | 收到文本消息: 消息ID:765207156 来自:***********@chatroom 发送人:wxid_xv01lkcmn48l22 @:[] 内容:我v比较low
2025-07-29 00:14:08 | DEBUG | 处理消息内容: '我v比较low'
2025-07-29 00:14:08 | DEBUG | 消息内容 '我v比较low' 不匹配任何命令，忽略
2025-07-29 00:14:13 | DEBUG | 收到消息: {'MsgId': 1003104037, 'FromUserName': {'string': '***********@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 1, 'Content': {'string': 'wxid_xv01lkcmn48l22:\n点一下就没了'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1753719260, 'MsgSource': '<msgsource>\n\t<bizflag>0</bizflag>\n\t<pua>1</pua>\n\t<eggIncluded>1</eggIncluded>\n\t<silence>1</silence>\n\t<membercount>144</membercount>\n\t<signature>N0_V1_4EfgyT6n|v1_L6dLZi5o</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'NewMsgId': 1786267742000638842, 'MsgSeq': 871407266}
2025-07-29 00:14:13 | INFO | 收到文本消息: 消息ID:1003104037 来自:***********@chatroom 发送人:wxid_xv01lkcmn48l22 @:[] 内容:点一下就没了
2025-07-29 00:14:13 | DEBUG | 处理消息内容: '点一下就没了'
2025-07-29 00:14:13 | DEBUG | 消息内容 '点一下就没了' 不匹配任何命令，忽略
2025-07-29 00:14:20 | DEBUG | 收到消息: {'MsgId': 1254408644, 'FromUserName': {'string': '***********@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 1, 'Content': {'string': 'wxid_snv13qf05qjx11:\n哈哈哈哈'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1753719267, 'MsgSource': '<msgsource>\n\t<bizflag>0</bizflag>\n\t<pua>1</pua>\n\t<eggIncluded>1</eggIncluded>\n\t<silence>1</silence>\n\t<membercount>144</membercount>\n\t<signature>N0_V1_RbTKKmFt|v1_yM/c6VN5</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'NewMsgId': 2093597660800195385, 'MsgSeq': 871407267}
2025-07-29 00:14:20 | INFO | 收到文本消息: 消息ID:1254408644 来自:***********@chatroom 发送人:wxid_snv13qf05qjx11 @:[] 内容:哈哈哈哈
2025-07-29 00:14:20 | DEBUG | 处理消息内容: '哈哈哈哈'
2025-07-29 00:14:20 | DEBUG | 消息内容 '哈哈哈哈' 不匹配任何命令，忽略
2025-07-29 00:16:29 | DEBUG | 收到消息: {'MsgId': 1003574944, 'FromUserName': {'string': '***********@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 1, 'Content': {'string': 'wxid_ikxxrwasicud11:\n挂机亲密+3'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1753719396, 'MsgSource': '<msgsource>\n\t<sec_msg_node>\n\t\t<alnode>\n\t\t\t<fr>1</fr>\n\t\t</alnode>\n\t</sec_msg_node>\n\t<pua>1</pua>\n\t<silence>1</silence>\n\t<membercount>144</membercount>\n\t<signature>N0_V1_r3QuIYP5|v1_i1I+OhCV</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'NewMsgId': 1274368948350781335, 'MsgSeq': 871407268}
2025-07-29 00:16:29 | INFO | 收到文本消息: 消息ID:1003574944 来自:***********@chatroom 发送人:wxid_ikxxrwasicud11 @:[] 内容:挂机亲密+3
2025-07-29 00:16:29 | DEBUG | 处理消息内容: '挂机亲密+3'
2025-07-29 00:16:29 | DEBUG | 消息内容 '挂机亲密+3' 不匹配任何命令，忽略
2025-07-29 00:17:24 | DEBUG | 收到消息: {'MsgId': 1631982789, 'FromUserName': {'string': '***********@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 1, 'Content': {'string': 'wxid_snv13qf05qjx11:\n那太棒了'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1753719451, 'MsgSource': '<msgsource>\n\t<bizflag>0</bizflag>\n\t<pua>1</pua>\n\t<eggIncluded>1</eggIncluded>\n\t<silence>1</silence>\n\t<membercount>144</membercount>\n\t<signature>N0_V1_eyL/19OQ|v1_Al6a/9J1</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'NewMsgId': 287936920553390736, 'MsgSeq': 871407269}
2025-07-29 00:17:24 | INFO | 收到文本消息: 消息ID:1631982789 来自:***********@chatroom 发送人:wxid_snv13qf05qjx11 @:[] 内容:那太棒了
2025-07-29 00:17:24 | DEBUG | 处理消息内容: '那太棒了'
2025-07-29 00:17:24 | DEBUG | 消息内容 '那太棒了' 不匹配任何命令，忽略
2025-07-29 00:18:13 | DEBUG | 收到消息: {'MsgId': 1517119776, 'FromUserName': {'string': '***********@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 1, 'Content': {'string': 'wxid_snv13qf05qjx11:\n能换号签么'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1753719500, 'MsgSource': '<msgsource>\n\t<bizflag>0</bizflag>\n\t<pua>1</pua>\n\t<eggIncluded>1</eggIncluded>\n\t<silence>1</silence>\n\t<membercount>144</membercount>\n\t<signature>N0_V1_2ZqTcuSG|v1_tkR8e3S0</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'NewMsgId': 1638486330076354624, 'MsgSeq': 871407270}
2025-07-29 00:18:13 | INFO | 收到文本消息: 消息ID:1517119776 来自:***********@chatroom 发送人:wxid_snv13qf05qjx11 @:[] 内容:能换号签么
2025-07-29 00:18:13 | DEBUG | 处理消息内容: '能换号签么'
2025-07-29 00:18:13 | DEBUG | 消息内容 '能换号签么' 不匹配任何命令，忽略
2025-07-29 00:18:46 | DEBUG | 收到消息: {'MsgId': 365503416, 'FromUserName': {'string': '***********@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 1, 'Content': {'string': 'wxid_ikxxrwasicud11:\n可以的'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1753719533, 'MsgSource': '<msgsource>\n\t<sec_msg_node>\n\t\t<alnode>\n\t\t\t<fr>1</fr>\n\t\t</alnode>\n\t</sec_msg_node>\n\t<pua>1</pua>\n\t<silence>1</silence>\n\t<membercount>144</membercount>\n\t<signature>N0_V1_rOCzF6m4|v1_pIuTPXXE</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'NewMsgId': 4354936348702843704, 'MsgSeq': 871407271}
2025-07-29 00:18:46 | INFO | 收到文本消息: 消息ID:365503416 来自:***********@chatroom 发送人:wxid_ikxxrwasicud11 @:[] 内容:可以的
2025-07-29 00:18:46 | DEBUG | 处理消息内容: '可以的'
2025-07-29 00:18:46 | DEBUG | 消息内容 '可以的' 不匹配任何命令，忽略
2025-07-29 00:19:57 | DEBUG | 收到消息: {'MsgId': 564101139, 'FromUserName': {'string': '***********@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 3, 'Content': {'string': 'wxid_ikxxrwasicud11:\n<?xml version="1.0"?>\n<msg>\n\t<img aeskey="d890242d3cfd25a1d5677b5b2c5ab8d4" encryver="1" cdnthumbaeskey="d890242d3cfd25a1d5677b5b2c5ab8d4" cdnthumburl="3057020100044b30490201000204062b4e3002034c57c10204254fe17c02046887a333042462303732326435662d343664322d346562382d386334312d3766623434633637623337350204052818020201000405004c57c100" cdnthumblength="2068" cdnthumbheight="34" cdnthumbwidth="155" cdnmidheight="0" cdnmidwidth="0" cdnhdheight="0" cdnhdwidth="0" cdnmidimgurl="3057020100044b30490201000204062b4e3002034c57c10204254fe17c02046887a333042462303732326435662d343664322d346562382d386334312d3766623434633637623337350204052818020201000405004c57c100" length="21898" md5="24c13297f712d03a8d962764370be144">\n\t\t<secHashInfoBase64 />\n\t\t<live>\n\t\t\t<duration>0</duration>\n\t\t\t<size>0</size>\n\t\t\t<md5 />\n\t\t\t<fileid />\n\t\t\t<hdsize>0</hdsize>\n\t\t\t<hdmd5 />\n\t\t\t<hdfileid />\n\t\t\t<stillimagetimems>0</stillimagetimems>\n\t\t</live>\n\t</img>\n\t<platform_signature />\n\t<imgdatahash />\n\t<ImgSourceInfo>\n\t\t<ImgSourceUrl />\n\t\t<BizType>0</BizType>\n\t</ImgSourceInfo>\n</msg>\n'}, 'Status': 3, 'ImgStatus': 2, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1753719604, 'MsgSource': '<msgsource>\n\t<sec_msg_node>\n\t\t<uuid>daf0834a7a681ca61423b292d7a6b278_</uuid>\n\t\t<risk-file-flag />\n\t\t<risk-file-md5-list />\n\t</sec_msg_node>\n\t<silence>1</silence>\n\t<membercount>144</membercount>\n\t<signature>N0_V1_Pg5xQFnB|v1_ts3hUOGw</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'NewMsgId': 8425314702383838565, 'MsgSeq': 871407272}
2025-07-29 00:19:57 | INFO | 收到图片消息: 消息ID:564101139 来自:***********@chatroom 发送人:wxid_ikxxrwasicud11 XML:<?xml version="1.0"?><msg><img aeskey="d890242d3cfd25a1d5677b5b2c5ab8d4" encryver="1" cdnthumbaeskey="d890242d3cfd25a1d5677b5b2c5ab8d4" cdnthumburl="3057020100044b30490201000204062b4e3002034c57c10204254fe17c02046887a333042462303732326435662d343664322d346562382d386334312d3766623434633637623337350204052818020201000405004c57c100" cdnthumblength="2068" cdnthumbheight="34" cdnthumbwidth="155" cdnmidheight="0" cdnmidwidth="0" cdnhdheight="0" cdnhdwidth="0" cdnmidimgurl="3057020100044b30490201000204062b4e3002034c57c10204254fe17c02046887a333042462303732326435662d343664322d346562382d386334312d3766623434633637623337350204052818020201000405004c57c100" length="21898" md5="24c13297f712d03a8d962764370be144"><secHashInfoBase64 /><live><duration>0</duration><size>0</size><md5 /><fileid /><hdsize>0</hdsize><hdmd5 /><hdfileid /><stillimagetimems>0</stillimagetimems></live></img><platform_signature /><imgdatahash /><ImgSourceInfo><ImgSourceUrl /><BizType>0</BizType></ImgSourceInfo></msg>
2025-07-29 00:19:57 | INFO | [ImageEcho] 保存图片信息成功，当前群 ***********@chatroom 已存储 5 张图片
2025-07-29 00:19:57 | INFO | [TimerTask] 缓存图片消息: 564101139
2025-07-29 00:20:06 | DEBUG | 收到消息: {'MsgId': 1387845268, 'FromUserName': {'string': '***********@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 1, 'Content': {'string': 'wxid_ikxxrwasicud11:\n这个还不错'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1753719613, 'MsgSource': '<msgsource>\n\t<atuserlist><![CDATA[]]></atuserlist>\n\t<silence>1</silence>\n\t<membercount>144</membercount>\n\t<signature>N0_V1_lvKyZk64|v1_Jmg8+mZ9</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'NewMsgId': 9046352295603360156, 'MsgSeq': 871407273}
2025-07-29 00:20:06 | INFO | 收到文本消息: 消息ID:1387845268 来自:***********@chatroom 发送人:wxid_ikxxrwasicud11 @:[] 内容:这个还不错
2025-07-29 00:20:06 | DEBUG | 处理消息内容: '这个还不错'
2025-07-29 00:20:06 | DEBUG | 消息内容 '这个还不错' 不匹配任何命令，忽略
2025-07-29 00:21:17 | DEBUG | 收到消息: {'MsgId': 479177683, 'FromUserName': {'string': '***********@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 1, 'Content': {'string': 'wxid_ctp9qffuf14b21:\n@゛花落ོ.°\u20052号房换下模式'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1753719684, 'MsgSource': '<msgsource>\n\t<atuserlist><![CDATA[wxid_ikxxrwasicud11]]></atuserlist>\n\t<pua>1</pua>\n\t<eggIncluded>1</eggIncluded>\n\t<silence>1</silence>\n\t<membercount>144</membercount>\n\t<signature>N0_V1_H8OnFIHy|v1_Y+UiYKD1</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'NewMsgId': 4870344301266132981, 'MsgSeq': 871407274}
2025-07-29 00:21:17 | INFO | 收到文本消息: 消息ID:479177683 来自:***********@chatroom 发送人:wxid_ctp9qffuf14b21 @:['wxid_ikxxrwasicud11'] 内容:@゛花落ོ.° 2号房换下模式
2025-07-29 00:21:17 | DEBUG | 处理消息内容: '@゛花落ོ.° 2号房换下模式'
2025-07-29 00:21:17 | DEBUG | 消息内容 '@゛花落ོ.° 2号房换下模式' 不匹配任何命令，忽略
2025-07-29 00:21:51 | DEBUG | 收到消息: {'MsgId': 1379829193, 'FromUserName': {'string': '***********@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 1, 'Content': {'string': 'wxid_ikxxrwasicud11:\n好'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1753719718, 'MsgSource': '<msgsource>\n\t<sec_msg_node>\n\t\t<alnode>\n\t\t\t<fr>1</fr>\n\t\t</alnode>\n\t</sec_msg_node>\n\t<pua>1</pua>\n\t<silence>1</silence>\n\t<membercount>144</membercount>\n\t<signature>N0_V1_yXW01Brq|v1_Rof4+Ct4</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'NewMsgId': 8488629833633631187, 'MsgSeq': 871407275}
2025-07-29 00:21:51 | INFO | 收到文本消息: 消息ID:1379829193 来自:***********@chatroom 发送人:wxid_ikxxrwasicud11 @:[] 内容:好
2025-07-29 00:21:52 | INFO | 发送表情消息: 对方wxid:***********@chatroom md5:ec659f94db17b05434544a0c377ddfc0 总长度:9992069
2025-07-29 00:21:52 | DEBUG | 处理消息内容: '好'
2025-07-29 00:21:52 | DEBUG | 消息内容 '好' 不匹配任何命令，忽略
2025-07-29 00:22:12 | DEBUG | 收到消息: {'MsgId': 926662818, 'FromUserName': {'string': '***********@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 49, 'Content': {'string': 'wxid_snv13qf05qjx11:\n<?xml version="1.0"?>\n<msg>\n\t<appmsg appid="" sdkver="0">\n\t\t<title>？</title>\n\t\t<type>57</type>\n\t\t<appattach>\n\t\t\t<cdnthumbaeskey />\n\t\t\t<aeskey />\n\t\t</appattach>\n\t\t<refermsg>\n\t\t\t<type>47</type>\n\t\t\t<svrid>6451520547157940328</svrid>\n\t\t\t<fromusr>***********@chatroom</fromusr>\n\t\t\t<chatusr>wxid_4usgcju5ey9q29</chatusr>\n\t\t\t<displayname>瑶瑶</displayname>\n\t\t\t<content>&lt;msg&gt;&lt;emoji fromusername = "wxid_4usgcju5ey9q29" tousername = "***********@chatroom" type="2" idbuffer="media:0_0" md5="ec659f94db17b05434544a0c377ddfc0" len = "394561" productid="" androidmd5="ec659f94db17b05434544a0c377ddfc0" androidlen="394561" s60v3md5 = "ec659f94db17b05434544a0c377ddfc0" s60v3len="394561" s60v5md5 = "ec659f94db17b05434544a0c377ddfc0" s60v5len="394561" cdnurl = "http://vweixinf.tc.qq.com/110/20401/stodownload?m=ec659f94db17b05434544a0c377ddfc0&amp;amp;filekey=30440201010430302e02016e0402535a042065633635396639346462313762303534333435343461306333373764646663300203060541040d00000004627466730000000132&amp;amp;hy=SZ&amp;amp;storeid=26877224b0003337bf5dee8850000006e01004fb1535a29cfe011568865606&amp;amp;ef=1&amp;amp;bizid=1022" designerid = "" thumburl = "" encrypturl = "http://vweixinf.tc.qq.com/110/20402/stodownload?m=38d4d15c316659b4e890b4e3c7ac3c7c&amp;amp;filekey=30440201010430302e02016e0402535a042033386434643135633331363635396234653839306234653363376163336337630203060550040d00000004627466730000000132&amp;amp;hy=SZ&amp;amp;storeid=26877224b000417bdf5dee8850000006e02004fb2535a29cfe01156886561e&amp;amp;ef=2&amp;amp;bizid=1022" aeskey= "695df9c553cb498d96d7ab1f98a32e42" externurl = "http://vweixinf.tc.qq.com/110/20403/stodownload?m=7cbc2c2ad453fa44f6919d2c2cee191c&amp;amp;filekey=30440201010430302e02016e0402535a042037636263326332616434353366613434663639313964326332636565313931630203009130040d00000004627466730000000132&amp;amp;hy=SZ&amp;amp;storeid=26877224b0005369ef5dee8850000006e03004fb3535a29cfe011568865632&amp;amp;ef=3&amp;amp;bizid=1022" externmd5 = "018e81e27adf973e3d91f2286238160a" width= "450" height= "450" tpurl= "" tpauthkey= "" attachedtext= "" attachedtextcolor= "" lensid= "" emojiattr= "" linkid= "" desc= "" &gt;&lt;/emoji&gt; &lt;gameext type="0" content="0" &gt;&lt;/gameext&gt; &lt;/msg&gt;</content>\n\t\t\t<msgsource>&lt;msgsource&gt;&lt;sequence_id&gt;766645867&lt;/sequence_id&gt;\n\t&lt;silence&gt;1&lt;/silence&gt;\n\t&lt;membercount&gt;144&lt;/membercount&gt;\n\t&lt;signature&gt;N0_V1_3iHZ1mCn|v1_wXruJ4or&lt;/signature&gt;\n\t&lt;tmp_node&gt;\n\t\t&lt;publisher-id&gt;&lt;/publisher-id&gt;\n\t&lt;/tmp_node&gt;\n&lt;/msgsource&gt;\n</msgsource>\n\t\t\t<createtime>1753719719</createtime>\n\t\t</refermsg>\n\t</appmsg>\n\t<fromusername>wxid_snv13qf05qjx11</fromusername>\n\t<scene>0</scene>\n\t<appinfo>\n\t\t<version>1</version>\n\t\t<appname></appname>\n\t</appinfo>\n\t<commenturl></commenturl>\n</msg>\n'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1753719739, 'MsgSource': '<msgsource>\n\t<bizflag>0</bizflag>\n\t<pua>1</pua>\n\t<eggIncluded>1</eggIncluded>\n\t<sec_msg_node>\n\t\t<uuid>5bba0b12cb50fbd5da6e75d10bad81cf_</uuid>\n\t\t<risk-file-flag />\n\t\t<risk-file-md5-list />\n\t</sec_msg_node>\n\t<silence>1</silence>\n\t<membercount>144</membercount>\n\t<signature>N0_V1_Kgbx2thB|v1_OTMuXd+i</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'NewMsgId': 908971670363472854, 'MsgSeq': 871407278}
2025-07-29 00:22:12 | DEBUG | 从群聊消息中提取发送者: wxid_snv13qf05qjx11
2025-07-29 00:22:12 | DEBUG | 使用已解析的XML处理引用消息
2025-07-29 00:22:12 | INFO | 收到引用消息: 消息ID:926662818 来自:***********@chatroom 发送人:wxid_snv13qf05qjx11 内容:？ 引用类型:47
2025-07-29 00:22:12 | INFO | [DouBaoImageToImage] 收到引用消息: ？
2025-07-29 00:22:12 | INFO | [TimerTask] 收到引用消息调试信息:
2025-07-29 00:22:12 | INFO |   - 消息内容: ？
2025-07-29 00:22:12 | INFO |   - 群组ID: ***********@chatroom
2025-07-29 00:22:12 | INFO |   - 发送人: wxid_snv13qf05qjx11
2025-07-29 00:22:12 | INFO |   - 引用信息: {'MsgType': 47, 'Content': '<msg><emoji fromusername = "wxid_4usgcju5ey9q29" tousername = "***********@chatroom" type="2" idbuffer="media:0_0" md5="ec659f94db17b05434544a0c377ddfc0" len = "394561" productid="" androidmd5="ec659f94db17b05434544a0c377ddfc0" androidlen="394561" s60v3md5 = "ec659f94db17b05434544a0c377ddfc0" s60v3len="394561" s60v5md5 = "ec659f94db17b05434544a0c377ddfc0" s60v5len="394561" cdnurl = "http://vweixinf.tc.qq.com/110/20401/stodownload?m=ec659f94db17b05434544a0c377ddfc0&amp;filekey=30440201010430302e02016e0402535a042065633635396639346462313762303534333435343461306333373764646663300203060541040d00000004627466730000000132&amp;hy=SZ&amp;storeid=26877224b0003337bf5dee8850000006e01004fb1535a29cfe011568865606&amp;ef=1&amp;bizid=1022" designerid = "" thumburl = "" encrypturl = "http://vweixinf.tc.qq.com/110/20402/stodownload?m=38d4d15c316659b4e890b4e3c7ac3c7c&amp;filekey=30440201010430302e02016e0402535a042033386434643135633331363635396234653839306234653363376163336337630203060550040d00000004627466730000000132&amp;hy=SZ&amp;storeid=26877224b000417bdf5dee8850000006e02004fb2535a29cfe01156886561e&amp;ef=2&amp;bizid=1022" aeskey= "695df9c553cb498d96d7ab1f98a32e42" externurl = "http://vweixinf.tc.qq.com/110/20403/stodownload?m=7cbc2c2ad453fa44f6919d2c2cee191c&amp;filekey=30440201010430302e02016e0402535a042037636263326332616434353366613434663639313964326332636565313931630203009130040d00000004627466730000000132&amp;hy=SZ&amp;storeid=26877224b0005369ef5dee8850000006e03004fb3535a29cfe011568865632&amp;ef=3&amp;bizid=1022" externmd5 = "018e81e27adf973e3d91f2286238160a" width= "450" height= "450" tpurl= "" tpauthkey= "" attachedtext= "" attachedtextcolor= "" lensid= "" emojiattr= "" linkid= "" desc= "" ></emoji> <gameext type="0" content="0" ></gameext> </msg>', 'Msgid': '6451520547157940328', 'NewMsgId': '6451520547157940328', 'ToWxid': 'wxid_4usgcju5ey9q29', 'FromWxid': '***********@chatroom', 'Nickname': '瑶瑶', 'MsgSource': '<msgsource><sequence_id>766645867</sequence_id>\n\t<silence>1</silence>\n\t<membercount>144</membercount>\n\t<signature>N0_V1_3iHZ1mCn|v1_wXruJ4or</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'Createtime': '1753719719', 'SenderWxid': 'wxid_snv13qf05qjx11'}
2025-07-29 00:22:12 | INFO |   - 引用消息ID: 
2025-07-29 00:22:12 | INFO |   - 引用消息类型: 
2025-07-29 00:22:12 | INFO |   - 引用消息内容: <msg><emoji fromusername = "wxid_4usgcju5ey9q29" tousername = "***********@chatroom" type="2" idbuffer="media:0_0" md5="ec659f94db17b05434544a0c377ddfc0" len = "394561" productid="" androidmd5="ec659f94db17b05434544a0c377ddfc0" androidlen="394561" s60v3md5 = "ec659f94db17b05434544a0c377ddfc0" s60v3len="394561" s60v5md5 = "ec659f94db17b05434544a0c377ddfc0" s60v5len="394561" cdnurl = "http://vweixinf.tc.qq.com/110/20401/stodownload?m=ec659f94db17b05434544a0c377ddfc0&amp;filekey=30440201010430302e02016e0402535a042065633635396639346462313762303534333435343461306333373764646663300203060541040d00000004627466730000000132&amp;hy=SZ&amp;storeid=26877224b0003337bf5dee8850000006e01004fb1535a29cfe011568865606&amp;ef=1&amp;bizid=1022" designerid = "" thumburl = "" encrypturl = "http://vweixinf.tc.qq.com/110/20402/stodownload?m=38d4d15c316659b4e890b4e3c7ac3c7c&amp;filekey=30440201010430302e02016e0402535a042033386434643135633331363635396234653839306234653363376163336337630203060550040d00000004627466730000000132&amp;hy=SZ&amp;storeid=26877224b000417bdf5dee8850000006e02004fb2535a29cfe01156886561e&amp;ef=2&amp;bizid=1022" aeskey= "695df9c553cb498d96d7ab1f98a32e42" externurl = "http://vweixinf.tc.qq.com/110/20403/stodownload?m=7cbc2c2ad453fa44f6919d2c2cee191c&amp;filekey=30440201010430302e02016e0402535a042037636263326332616434353366613434663639313964326332636565313931630203009130040d00000004627466730000000132&amp;hy=SZ&amp;storeid=26877224b0005369ef5dee8850000006e03004fb3535a29cfe011568865632&amp;ef=3&amp;bizid=1022" externmd5 = "018e81e27adf973e3d91f2286238160a" width= "450" height= "450" tpurl= "" tpauthkey= "" attachedtext= "" attachedtextcolor= "" lensid= "" emojiattr= "" linkid= "" desc= "" ></emoji> <gameext type="0" content="0" ></gameext> </msg>
2025-07-29 00:22:12 | INFO |   - 引用消息发送人: wxid_snv13qf05qjx11
2025-07-29 00:22:23 | DEBUG | 收到消息: {'MsgId': 1432685310, 'FromUserName': {'string': '***********@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 49, 'Content': {'string': 'wxid_ctp9qffuf14b21:\n<?xml version="1.0"?>\n<msg>\n\t<appmsg appid="" sdkver="0">\n\t\t<title>不配床上练臂力？</title>\n\t\t<des />\n\t\t<username />\n\t\t<action>view</action>\n\t\t<type>57</type>\n\t\t<showtype>0</showtype>\n\t\t<content />\n\t\t<url />\n\t\t<lowurl />\n\t\t<forwardflag>0</forwardflag>\n\t\t<dataurl />\n\t\t<lowdataurl />\n\t\t<contentattr>0</contentattr>\n\t\t<streamvideo>\n\t\t\t<streamvideourl />\n\t\t\t<streamvideototaltime>0</streamvideototaltime>\n\t\t\t<streamvideotitle />\n\t\t\t<streamvideowording />\n\t\t\t<streamvideoweburl />\n\t\t\t<streamvideothumburl />\n\t\t\t<streamvideoaduxinfo />\n\t\t\t<streamvideopublishid />\n\t\t</streamvideo>\n\t\t<canvasPageItem>\n\t\t\t<canvasPageXml><![CDATA[]]></canvasPageXml>\n\t\t</canvasPageItem>\n\t\t<refermsg>\n\t\t\t<type>47</type>\n\t\t\t<svrid>6451520547157940328</svrid>\n\t\t\t<fromusr>***********@chatroom</fromusr>\n\t\t\t<chatusr>wxid_4usgcju5ey9q29</chatusr>\n\t\t\t<displayname>瑶瑶</displayname>\n\t\t\t<msgsource>&lt;msgsource&gt;\n\t&lt;silence&gt;1&lt;/silence&gt;\n\t&lt;membercount&gt;144&lt;/membercount&gt;\n\t&lt;signature&gt;N0_V1_xNtnP43w|v1_M3mRBCve&lt;/signature&gt;\n\t&lt;tmp_node&gt;\n\t\t&lt;publisher-id&gt;&lt;/publisher-id&gt;\n\t&lt;/tmp_node&gt;\n&lt;/msgsource&gt;\n</msgsource>\n\t\t\t<content>&lt;msg&gt;&lt;emoji fromusername = "wxid_4usgcju5ey9q29" tousername = "***********@chatroom" type="2" idbuffer="media*#*0_0" md5="ec659f94db17b05434544a0c377ddfc0" len = "394561" productid="" androidmd5="ec659f94db17b05434544a0c377ddfc0" androidlen="394561" s60v3md5 = "ec659f94db17b05434544a0c377ddfc0" s60v3len="394561" s60v5md5 = "ec659f94db17b05434544a0c377ddfc0" s60v5len="394561" cdnurl = "http*#*//vweixinf.tc.qq.com/110/20401/stodownload?m=ec659f94db17b05434544a0c377ddfc0&amp;amp;filekey=30440201010430302e02016e0402535a042065633635396639346462313762303534333435343461306333373764646663300203060541040d00000004627466730000000132&amp;amp;hy=SZ&amp;amp;storeid=26877224b0003337bf5dee8850000006e01004fb1535a29cfe011568865606&amp;amp;ef=1&amp;amp;bizid=1022" designerid = "" thumburl = "" encrypturl = "http*#*//vweixinf.tc.qq.com/110/20402/stodownload?m=38d4d15c316659b4e890b4e3c7ac3c7c&amp;amp;filekey=30440201010430302e02016e0402535a042033386434643135633331363635396234653839306234653363376163336337630203060550040d00000004627466730000000132&amp;amp;hy=SZ&amp;amp;storeid=26877224b000417bdf5dee8850000006e02004fb2535a29cfe01156886561e&amp;amp;ef=2&amp;amp;bizid=1022" aeskey= "695df9c553cb498d96d7ab1f98a32e42" externurl = "http*#*//vweixinf.tc.qq.com/110/20403/stodownload?m=7cbc2c2ad453fa44f6919d2c2cee191c&amp;amp;filekey=30440201010430302e02016e0402535a042037636263326332616434353366613434663639313964326332636565313931630203009130040d00000004627466730000000132&amp;amp;hy=SZ&amp;amp;storeid=26877224b0005369ef5dee8850000006e03004fb3535a29cfe011568865632&amp;amp;ef=3&amp;amp;bizid=1022" externmd5 = "018e81e27adf973e3d91f2286238160a" width= "450" height= "450" tpurl= "" tpauthkey= "" attachedtext= "" attachedtextcolor= "" lensid= "" emojiattr= "" linkid= "" desc= "" &gt;&lt;/emoji&gt; &lt;gameext type="0" content="0" &gt;&lt;/gameext&gt; &lt;/msg&gt;:0\n</content>\n\t\t\t<strid />\n\t\t\t<createtime>1753719719</createtime>\n\t\t</refermsg>\n\t\t<appattach>\n\t\t\t<totallen>0</totallen>\n\t\t\t<attachid />\n\t\t\t<cdnattachurl />\n\t\t\t<emoticonmd5 />\n\t\t\t<aeskey />\n\t\t\t<fileext />\n\t\t\t<islargefilemsg>0</islargefilemsg>\n\t\t</appattach>\n\t\t<extinfo />\n\t\t<androidsource>0</androidsource>\n\t\t<thumburl />\n\t\t<mediatagname />\n\t\t<messageaction><![CDATA[]]></messageaction>\n\t\t<messageext><![CDATA[]]></messageext>\n\t\t<emoticongift>\n\t\t\t<packageflag>0</packageflag>\n\t\t\t<packageid />\n\t\t</emoticongift>\n\t\t<emoticonshared>\n\t\t\t<packageflag>0</packageflag>\n\t\t\t<packageid />\n\t\t</emoticonshared>\n\t\t<designershared>\n\t\t\t<designeruin>0</designeruin>\n\t\t\t<designername>null</designername>\n\t\t\t<designerrediretcturl><![CDATA[null]]></designerrediretcturl>\n\t\t</designershared>\n\t\t<emotionpageshared>\n\t\t\t<tid>0</tid>\n\t\t\t<title>null</title>\n\t\t\t<desc>null</desc>\n\t\t\t<iconUrl><![CDATA[null]]></iconUrl>\n\t\t\t<secondUrl>null</secondUrl>\n\t\t\t<pageType>0</pageType>\n\t\t\t<setKey>null</setKey>\n\t\t</emotionpageshared>\n\t\t<webviewshared>\n\t\t\t<shareUrlOriginal />\n\t\t\t<shareUrlOpen />\n\t\t\t<jsAppId />\n\t\t\t<publisherId />\n\t\t\t<publisherReqId />\n\t\t</webviewshared>\n\t\t<template_id />\n\t\t<md5 />\n\t\t<websearch>\n\t\t\t<rec_category>0</rec_category>\n\t\t\t<channelId>0</channelId>\n\t\t</websearch>\n\t\t<weappinfo>\n\t\t\t<username />\n\t\t\t<appid />\n\t\t\t<appservicetype>0</appservicetype>\n\t\t\t<secflagforsinglepagemode>0</secflagforsinglepagemode>\n\t\t\t<videopageinfo>\n\t\t\t\t<thumbwidth>0</thumbwidth>\n\t\t\t\t<thumbheight>0</thumbheight>\n\t\t\t\t<fromopensdk>0</fromopensdk>\n\t\t\t</videopageinfo>\n\t\t</weappinfo>\n\t\t<statextstr />\n\t\t<musicShareItem>\n\t\t\t<musicDuration>0</musicDuration>\n\t\t</musicShareItem>\n\t\t<finderLiveProductShare>\n\t\t\t<finderLiveID><![CDATA[]]></finderLiveID>\n\t\t\t<finderUsername><![CDATA[]]></finderUsername>\n\t\t\t<finderObjectID><![CDATA[]]></finderObjectID>\n\t\t\t<finderNonceID><![CDATA[]]></finderNonceID>\n\t\t\t<liveStatus><![CDATA[]]></liveStatus>\n\t\t\t<appId><![CDATA[]]></appId>\n\t\t\t<pagePath><![CDATA[]]></pagePath>\n\t\t\t<productId><![CDATA[]]></productId>\n\t\t\t<coverUrl><![CDATA[]]></coverUrl>\n\t\t\t<productTitle><![CDATA[]]></productTitle>\n\t\t\t<marketPrice><![CDATA[0]]></marketPrice>\n\t\t\t<sellingPrice><![CDATA[0]]></sellingPrice>\n\t\t\t<platformHeadImg><![CDATA[]]></platformHeadImg>\n\t\t\t<platformName><![CDATA[]]></platformName>\n\t\t\t<shopWindowId><![CDATA[]]></shopWindowId>\n\t\t\t<flashSalePrice><![CDATA[0]]></flashSalePrice>\n\t\t\t<flashSaleEndTime><![CDATA[0]]></flashSaleEndTime>\n\t\t\t<ecSource><![CDATA[]]></ecSource>\n\t\t\t<sellingPriceWording><![CDATA[]]></sellingPriceWording>\n\t\t\t<platformIconURL><![CDATA[]]></platformIconURL>\n\t\t\t<firstProductTagURL><![CDATA[]]></firstProductTagURL>\n\t\t\t<firstProductTagAspectRatioString><![CDATA[0.0]]></firstProductTagAspectRatioString>\n\t\t\t<secondProductTagURL><![CDATA[]]></secondProductTagURL>\n\t\t\t<secondProductTagAspectRatioString><![CDATA[0.0]]></secondProductTagAspectRatioString>\n\t\t\t<firstGuaranteeWording><![CDATA[]]></firstGuaranteeWording>\n\t\t\t<secondGuaranteeWording><![CDATA[]]></secondGuaranteeWording>\n\t\t\t<thirdGuaranteeWording><![CDATA[]]></thirdGuaranteeWording>\n\t\t\t<isPriceBeginShow>false</isPriceBeginShow>\n\t\t\t<lastGMsgID><![CDATA[]]></lastGMsgID>\n\t\t\t<promoterKey><![CDATA[]]></promoterKey>\n\t\t\t<discountWording><![CDATA[]]></discountWording>\n\t\t\t<priceSuffixDescription><![CDATA[]]></priceSuffixDescription>\n\t\t\t<productCardKey><![CDATA[]]></productCardKey>\n\t\t\t<isWxShop><![CDATA[]]></isWxShop>\n\t\t\t<brandIconUrl><![CDATA[]]></brandIconUrl>\n\t\t\t<rIconUrl><![CDATA[]]></rIconUrl>\n\t\t\t<rIconUrlDarkMode><![CDATA[]]></rIconUrlDarkMode>\n\t\t\t<topShopIconUrl><![CDATA[]]></topShopIconUrl>\n\t\t\t<topShopIconUrlDarkMode><![CDATA[]]></topShopIconUrlDarkMode>\n\t\t\t<simplifyTopShopIconUrl><![CDATA[]]></simplifyTopShopIconUrl>\n\t\t\t<simplifyTopShopIconUrlDarkmode><![CDATA[]]></simplifyTopShopIconUrlDarkmode>\n\t\t\t<topShopIconWidth><![CDATA[0]]></topShopIconWidth>\n\t\t\t<topShopIconHeight><![CDATA[0]]></topShopIconHeight>\n\t\t\t<simplifyTopShopIconWidth><![CDATA[0]]></simplifyTopShopIconWidth>\n\t\t\t<simplifyTopShopIconHeight><![CDATA[0]]></simplifyTopShopIconHeight>\n\t\t\t<showBoxItemStringList />\n\t\t</finderLiveProductShare>\n\t\t<finderOrder>\n\t\t\t<appID><![CDATA[]]></appID>\n\t\t\t<orderID><![CDATA[]]></orderID>\n\t\t\t<path><![CDATA[]]></path>\n\t\t\t<priceWording><![CDATA[]]></priceWording>\n\t\t\t<stateWording><![CDATA[]]></stateWording>\n\t\t\t<productImageURL><![CDATA[]]></productImageURL>\n\t\t\t<products><![CDATA[]]></products>\n\t\t\t<productsCount><![CDATA[0]]></productsCount>\n\t\t\t<orderType><![CDATA[0]]></orderType>\n\t\t\t<newPriceWording><![CDATA[]]></newPriceWording>\n\t\t\t<newStateWording><![CDATA[]]></newStateWording>\n\t\t\t<useNewWording><![CDATA[0]]></useNewWording>\n\t\t</finderOrder>\n\t\t<finderShopWindowShare>\n\t\t\t<finderUsername><![CDATA[]]></finderUsername>\n\t\t\t<avatar><![CDATA[]]></avatar>\n\t\t\t<nickname><![CDATA[]]></nickname>\n\t\t\t<commodityInStockCount><![CDATA[]]></commodityInStockCount>\n\t\t\t<appId><![CDATA[]]></appId>\n\t\t\t<path><![CDATA[]]></path>\n\t\t\t<appUsername><![CDATA[]]></appUsername>\n\t\t\t<query><![CDATA[]]></query>\n\t\t\t<liteAppId><![CDATA[]]></liteAppId>\n\t\t\t<liteAppPath><![CDATA[]]></liteAppPath>\n\t\t\t<liteAppQuery><![CDATA[]]></liteAppQuery>\n\t\t\t<platformTagURL><![CDATA[]]></platformTagURL>\n\t\t\t<saleWording><![CDATA[]]></saleWording>\n\t\t\t<lastGMsgID><![CDATA[]]></lastGMsgID>\n\t\t\t<profileTypeWording><![CDATA[]]></profileTypeWording>\n\t\t\t<saleWordingExtra><![CDATA[]]></saleWordingExtra>\n\t\t\t<isWxShop><![CDATA[]]></isWxShop>\n\t\t\t<platformIconUrl><![CDATA[]]></platformIconUrl>\n\t\t\t<brandIconUrl><![CDATA[]]></brandIconUrl>\n\t\t\t<description><![CDATA[]]></description>\n\t\t\t<backgroundUrl><![CDATA[]]></backgroundUrl>\n\t\t\t<darkModePlatformIconUrl><![CDATA[]]></darkModePlatformIconUrl>\n\t\t\t<rIconUrl><![CDATA[]]></rIconUrl>\n\t\t\t<rIconUrlDarkMode><![CDATA[]]></rIconUrlDarkMode>\n\t\t\t<rWords><![CDATA[]]></rWords>\n\t\t\t<topShopIconUrl><![CDATA[]]></topShopIconUrl>\n\t\t\t<topShopIconUrlDarkMode><![CDATA[]]></topShopIconUrlDarkMode>\n\t\t\t<simplifyTopShopIconUrl><![CDATA[]]></simplifyTopShopIconUrl>\n\t\t\t<simplifyTopShopIconUrlDarkmode><![CDATA[]]></simplifyTopShopIconUrlDarkmode>\n\t\t\t<topShopIconWidth><![CDATA[0]]></topShopIconWidth>\n\t\t\t<topShopIconHeight><![CDATA[0]]></topShopIconHeight>\n\t\t\t<simplifyTopShopIconWidth><![CDATA[0]]></simplifyTopShopIconWidth>\n\t\t\t<simplifyTopShopIconHeight><![CDATA[0]]></simplifyTopShopIconHeight>\n\t\t\t<reputationInfo>\n\t\t\t\t<hasReputationInfo>0</hasReputationInfo>\n\t\t\t\t<reputationScore>0</reputationScore>\n\t\t\t\t<reputationWording />\n\t\t\t\t<reputationTextColor />\n\t\t\t\t<reputationLevelWording />\n\t\t\t\t<reputationBackgroundColor />\n\t\t\t</reputationInfo>\n\t\t\t<productImageURLList />\n\t\t</finderShopWindowShare>\n\t\t<findernamecard>\n\t\t\t<username />\n\t\t\t<avatar><![CDATA[]]></avatar>\n\t\t\t<nickname />\n\t\t\t<auth_job />\n\t\t\t<auth_icon>0</auth_icon>\n\t\t\t<auth_icon_url />\n\t\t\t<ecSource><![CDATA[]]></ecSource>\n\t\t\t<lastGMsgID><![CDATA[]]></lastGMsgID>\n\t\t</findernamecard>\n\t\t<finderGuarantee>\n\t\t\t<scene><![CDATA[0]]></scene>\n\t\t</finderGuarantee>\n\t\t<directshare>0</directshare>\n\t\t<gamecenter>\n\t\t\t<namecard>\n\t\t\t\t<iconUrl />\n\t\t\t\t<name />\n\t\t\t\t<desc />\n\t\t\t\t<tail />\n\t\t\t\t<jumpUrl />\n\t\t\t\t<liteappId />\n\t\t\t\t<liteappPath />\n\t\t\t\t<liteappQuery />\n\t\t\t\t<liteappMinVersion />\n\t\t\t</namecard>\n\t\t</gamecenter>\n\t\t<patMsg>\n\t\t\t<chatUser />\n\t\t\t<records>\n\t\t\t\t<recordNum>0</recordNum>\n\t\t\t</records>\n\t\t</patMsg>\n\t\t<secretmsg>\n\t\t\t<issecretmsg>0</issecretmsg>\n\t\t</secretmsg>\n\t\t<referfromscene>0</referfromscene>\n\t\t<gameshare>\n\t\t\t<liteappext>\n\t\t\t\t<liteappbizdata />\n\t\t\t\t<priority>0</priority>\n\t\t\t</liteappext>\n\t\t\t<appbrandext>\n\t\t\t\t<litegameinfo />\n\t\t\t\t<priority>-1</priority>\n\t\t\t</appbrandext>\n\t\t\t<gameshareid />\n\t\t\t<sharedata />\n\t\t\t<isvideo>0</isvideo>\n\t\t\t<duration>-1</duration>\n\t\t\t<isexposed>0</isexposed>\n\t\t\t<readtext />\n\t\t</gameshare>\n\t\t<tingChatRoomItem>\n\t\t\t<type>0</type>\n\t\t\t<categoryItem>null</categoryItem>\n\t\t\t<categoryId />\n\t\t</tingChatRoomItem>\n\t\t<mpsharetrace>\n\t\t\t<hasfinderelement>0</hasfinderelement>\n\t\t\t<lastgmsgid />\n\t\t</mpsharetrace>\n\t\t<wxgamecard>\n\t\t\t<framesetname />\n\t\t\t<mbcarddata />\n\t\t\t<minpkgversion />\n\t\t\t<clientextinfo />\n\t\t\t<mbcardheight>0</mbcardheight>\n\t\t\t<isoldversion>0</isoldversion>\n\t\t</wxgamecard>\n\t\t<ecskfcard>\n\t\t\t<framesetname />\n\t\t\t<mbcarddata />\n\t\t\t<minupdateunixtimestamp>0</minupdateunixtimestamp>\n\t\t\t<needheader>false</needheader>\n\t\t\t<summary />\n\t\t</ecskfcard>\n\t\t<liteapp>\n\t\t\t<id>null</id>\n\t\t\t<path />\n\t\t\t<query />\n\t\t\t<istransparent>0</istransparent>\n\t\t\t<hideicon>0</hideicon>\n\t\t\t<forbidforward>0</forbidforward>\n\t\t</liteapp>\n\t\t<opensdk_share_is_modified>0</opensdk_share_is_modified>\n\t</appmsg>\n\t<fromusername>wxid_ctp9qffuf14b21</fromusername>\n\t<scene>0</scene>\n\t<appinfo>\n\t\t<version>1</version>\n\t\t<appname></appname>\n\t</appinfo>\n\t<commenturl></commenturl>\n</msg>\n'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1753719750, 'MsgSource': '<msgsource>\n\t<alnode>\n\t\t<fr>4</fr>\n\t</alnode>\n\t<eggIncluded>1</eggIncluded>\n\t<sec_msg_node>\n\t\t<uuid>282c8e5d48f3a63534c8d5622983cd46_</uuid>\n\t\t<risk-file-flag />\n\t\t<risk-file-md5-list />\n\t</sec_msg_node>\n\t<silence>1</silence>\n\t<membercount>144</membercount>\n\t<signature>N0_V1_g5V86LCx|v1_qBhouRXL</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'NewMsgId': 2682335281627084649, 'MsgSeq': 871407279}
2025-07-29 00:22:23 | DEBUG | 从群聊消息中提取发送者: wxid_ctp9qffuf14b21
2025-07-29 00:22:23 | DEBUG | 使用已解析的XML处理引用消息
2025-07-29 00:22:23 | INFO | 收到引用消息: 消息ID:1432685310 来自:***********@chatroom 发送人:wxid_ctp9qffuf14b21 内容:不配床上练臂力？ 引用类型:47
2025-07-29 00:22:23 | INFO | [DouBaoImageToImage] 收到引用消息: 不配床上练臂力？
2025-07-29 00:22:23 | INFO | [TimerTask] 收到引用消息调试信息:
2025-07-29 00:22:23 | INFO |   - 消息内容: 不配床上练臂力？
2025-07-29 00:22:23 | INFO |   - 群组ID: ***********@chatroom
2025-07-29 00:22:23 | INFO |   - 发送人: wxid_ctp9qffuf14b21
2025-07-29 00:22:23 | INFO |   - 引用信息: {'MsgType': 47, 'Content': '<msg><emoji fromusername = "wxid_4usgcju5ey9q29" tousername = "***********@chatroom" type="2" idbuffer="media*#*0_0" md5="ec659f94db17b05434544a0c377ddfc0" len = "394561" productid="" androidmd5="ec659f94db17b05434544a0c377ddfc0" androidlen="394561" s60v3md5 = "ec659f94db17b05434544a0c377ddfc0" s60v3len="394561" s60v5md5 = "ec659f94db17b05434544a0c377ddfc0" s60v5len="394561" cdnurl = "http*#*//vweixinf.tc.qq.com/110/20401/stodownload?m=ec659f94db17b05434544a0c377ddfc0&amp;filekey=30440201010430302e02016e0402535a042065633635396639346462313762303534333435343461306333373764646663300203060541040d00000004627466730000000132&amp;hy=SZ&amp;storeid=26877224b0003337bf5dee8850000006e01004fb1535a29cfe011568865606&amp;ef=1&amp;bizid=1022" designerid = "" thumburl = "" encrypturl = "http*#*//vweixinf.tc.qq.com/110/20402/stodownload?m=38d4d15c316659b4e890b4e3c7ac3c7c&amp;filekey=30440201010430302e02016e0402535a042033386434643135633331363635396234653839306234653363376163336337630203060550040d00000004627466730000000132&amp;hy=SZ&amp;storeid=26877224b000417bdf5dee8850000006e02004fb2535a29cfe01156886561e&amp;ef=2&amp;bizid=1022" aeskey= "695df9c553cb498d96d7ab1f98a32e42" externurl = "http*#*//vweixinf.tc.qq.com/110/20403/stodownload?m=7cbc2c2ad453fa44f6919d2c2cee191c&amp;filekey=30440201010430302e02016e0402535a042037636263326332616434353366613434663639313964326332636565313931630203009130040d00000004627466730000000132&amp;hy=SZ&amp;storeid=26877224b0005369ef5dee8850000006e03004fb3535a29cfe011568865632&amp;ef=3&amp;bizid=1022" externmd5 = "018e81e27adf973e3d91f2286238160a" width= "450" height= "450" tpurl= "" tpauthkey= "" attachedtext= "" attachedtextcolor= "" lensid= "" emojiattr= "" linkid= "" desc= "" ></emoji> <gameext type="0" content="0" ></gameext> </msg>:0\n', 'Msgid': '6451520547157940328', 'NewMsgId': '6451520547157940328', 'ToWxid': 'wxid_4usgcju5ey9q29', 'FromWxid': '***********@chatroom', 'Nickname': '瑶瑶', 'MsgSource': '<msgsource>\n\t<silence>1</silence>\n\t<membercount>144</membercount>\n\t<signature>N0_V1_xNtnP43w|v1_M3mRBCve</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'Createtime': '1753719719', 'SenderWxid': 'wxid_ctp9qffuf14b21'}
2025-07-29 00:22:23 | INFO |   - 引用消息ID: 
2025-07-29 00:22:23 | INFO |   - 引用消息类型: 
2025-07-29 00:22:23 | INFO |   - 引用消息内容: <msg><emoji fromusername = "wxid_4usgcju5ey9q29" tousername = "***********@chatroom" type="2" idbuffer="media*#*0_0" md5="ec659f94db17b05434544a0c377ddfc0" len = "394561" productid="" androidmd5="ec659f94db17b05434544a0c377ddfc0" androidlen="394561" s60v3md5 = "ec659f94db17b05434544a0c377ddfc0" s60v3len="394561" s60v5md5 = "ec659f94db17b05434544a0c377ddfc0" s60v5len="394561" cdnurl = "http*#*//vweixinf.tc.qq.com/110/20401/stodownload?m=ec659f94db17b05434544a0c377ddfc0&amp;filekey=30440201010430302e02016e0402535a042065633635396639346462313762303534333435343461306333373764646663300203060541040d00000004627466730000000132&amp;hy=SZ&amp;storeid=26877224b0003337bf5dee8850000006e01004fb1535a29cfe011568865606&amp;ef=1&amp;bizid=1022" designerid = "" thumburl = "" encrypturl = "http*#*//vweixinf.tc.qq.com/110/20402/stodownload?m=38d4d15c316659b4e890b4e3c7ac3c7c&amp;filekey=30440201010430302e02016e0402535a042033386434643135633331363635396234653839306234653363376163336337630203060550040d00000004627466730000000132&amp;hy=SZ&amp;storeid=26877224b000417bdf5dee8850000006e02004fb2535a29cfe01156886561e&amp;ef=2&amp;bizid=1022" aeskey= "695df9c553cb498d96d7ab1f98a32e42" externurl = "http*#*//vweixinf.tc.qq.com/110/20403/stodownload?m=7cbc2c2ad453fa44f6919d2c2cee191c&amp;filekey=30440201010430302e02016e0402535a042037636263326332616434353366613434663639313964326332636565313931630203009130040d00000004627466730000000132&amp;hy=SZ&amp;storeid=26877224b0005369ef5dee8850000006e03004fb3535a29cfe011568865632&amp;ef=3&amp;bizid=1022" externmd5 = "018e81e27adf973e3d91f2286238160a" width= "450" height= "450" tpurl= "" tpauthkey= "" attachedtext= "" attachedtextcolor= "" lensid= "" emojiattr= "" linkid= "" desc= "" ></emoji> <gameext type="0" content="0" ></gameext> </msg>:0

2025-07-29 00:22:23 | INFO |   - 引用消息发送人: wxid_ctp9qffuf14b21
2025-07-29 00:23:51 | DEBUG | 收到消息: {'MsgId': 1427104398, 'FromUserName': {'string': '***********@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 1, 'Content': {'string': 'wxid_idzryo4rneok22:\n这'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1753719838, 'MsgSource': '<msgsource>\n\t<pua>1</pua>\n\t<eggIncluded>1</eggIncluded>\n\t<silence>1</silence>\n\t<membercount>144</membercount>\n\t<signature>N0_V1_k8DMyuI8|v1_ICXDCbLU</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'NewMsgId': 4551260383193276776, 'MsgSeq': 871407280}
2025-07-29 00:23:51 | INFO | 收到文本消息: 消息ID:1427104398 来自:***********@chatroom 发送人:wxid_idzryo4rneok22 @:[] 内容:这
2025-07-29 00:23:51 | DEBUG | 处理消息内容: '这'
2025-07-29 00:23:51 | DEBUG | 消息内容 '这' 不匹配任何命令，忽略
2025-07-29 00:23:56 | DEBUG | 收到消息: {'MsgId': 1848559486, 'FromUserName': {'string': '***********@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 1, 'Content': {'string': 'wxid_idzryo4rneok22:\n嗯…'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1753719843, 'MsgSource': '<msgsource>\n\t<pua>1</pua>\n\t<eggIncluded>1</eggIncluded>\n\t<silence>1</silence>\n\t<membercount>144</membercount>\n\t<signature>N0_V1_vFWcXqAt|v1_SZ0pBUUR</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'NewMsgId': 4782386205946136599, 'MsgSeq': 871407281}
2025-07-29 00:23:56 | INFO | 收到文本消息: 消息ID:1848559486 来自:***********@chatroom 发送人:wxid_idzryo4rneok22 @:[] 内容:嗯…
2025-07-29 00:23:56 | DEBUG | 处理消息内容: '嗯…'
2025-07-29 00:23:56 | DEBUG | 消息内容 '嗯…' 不匹配任何命令，忽略
2025-07-29 00:24:02 | DEBUG | 收到消息: {'MsgId': 738541993, 'FromUserName': {'string': '***********@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 1, 'Content': {'string': 'wxid_idzryo4rneok22:\n啧'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1753719849, 'MsgSource': '<msgsource>\n\t<pua>1</pua>\n\t<eggIncluded>1</eggIncluded>\n\t<silence>1</silence>\n\t<membercount>144</membercount>\n\t<signature>N0_V1_dcyq0nYL|v1_YXBjD43G</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'NewMsgId': 3115248671312720159, 'MsgSeq': 871407282}
2025-07-29 00:24:02 | INFO | 收到文本消息: 消息ID:738541993 来自:***********@chatroom 发送人:wxid_idzryo4rneok22 @:[] 内容:啧
2025-07-29 00:24:02 | DEBUG | 处理消息内容: '啧'
2025-07-29 00:24:02 | DEBUG | 消息内容 '啧' 不匹配任何命令，忽略
2025-07-29 00:24:06 | INFO | [TempFileManager] 开始清理临时文件...
2025-07-29 00:24:07 | DEBUG | 开始检查群成员变化，当前监控 2 个群
2025-07-29 00:24:08 | DEBUG | 群成员变化检查完成
2025-07-29 00:24:09 | DEBUG | 收到消息: {'MsgId': 568120353, 'FromUserName': {'string': '***********@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 1, 'Content': {'string': 'wxid_idzryo4rneok22:\n咦'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1753719856, 'MsgSource': '<msgsource>\n\t<pua>1</pua>\n\t<eggIncluded>1</eggIncluded>\n\t<silence>1</silence>\n\t<membercount>144</membercount>\n\t<signature>N0_V1_XvfONqNx|v1_Oaexevm2</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'NewMsgId': 6855574114480558066, 'MsgSeq': 871407283}
2025-07-29 00:24:09 | INFO | 收到文本消息: 消息ID:568120353 来自:***********@chatroom 发送人:wxid_idzryo4rneok22 @:[] 内容:咦
2025-07-29 00:24:09 | DEBUG | 处理消息内容: '咦'
2025-07-29 00:24:09 | DEBUG | 消息内容 '咦' 不匹配任何命令，忽略
2025-07-29 00:25:18 | DEBUG | 收到消息: {'MsgId': 1984229875, 'FromUserName': {'string': '***********@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 1, 'Content': {'string': 'wxid_ikxxrwasicud11:\n这运动非得床上做吗'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1753719925, 'MsgSource': '<msgsource>\n\t<atuserlist><![CDATA[]]></atuserlist>\n\t<silence>1</silence>\n\t<membercount>144</membercount>\n\t<signature>N0_V1_3SimyDvt|v1_fbFa2r6g</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'NewMsgId': 8220245725267781250, 'MsgSeq': 871407284}
2025-07-29 00:25:18 | INFO | 收到文本消息: 消息ID:1984229875 来自:***********@chatroom 发送人:wxid_ikxxrwasicud11 @:[] 内容:这运动非得床上做吗
2025-07-29 00:25:18 | DEBUG | 处理消息内容: '这运动非得床上做吗'
2025-07-29 00:25:18 | DEBUG | 消息内容 '这运动非得床上做吗' 不匹配任何命令，忽略
2025-07-29 00:26:09 | DEBUG | 收到消息: {'MsgId': 1073444079, 'FromUserName': {'string': '***********@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 1, 'Content': {'string': 'wxid_idzryo4rneok22:\n咱不道'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1753719976, 'MsgSource': '<msgsource>\n\t<pua>1</pua>\n\t<eggIncluded>1</eggIncluded>\n\t<silence>1</silence>\n\t<membercount>144</membercount>\n\t<signature>N0_V1_Q+y+fLZl|v1_vm6VUnyS</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'NewMsgId': 3829125819277153938, 'MsgSeq': 871407285}
2025-07-29 00:26:09 | INFO | 收到文本消息: 消息ID:1073444079 来自:***********@chatroom 发送人:wxid_idzryo4rneok22 @:[] 内容:咱不道
2025-07-29 00:26:09 | DEBUG | 处理消息内容: '咱不道'
2025-07-29 00:26:09 | DEBUG | 消息内容 '咱不道' 不匹配任何命令，忽略
2025-07-29 00:26:22 | DEBUG | 收到消息: {'MsgId': 89427541, 'FromUserName': {'string': '***********@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 1, 'Content': {'string': 'tianen532965049:\n好'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1753719989, 'MsgSource': '<msgsource>\n\t<bizflag>0</bizflag>\n\t<pua>1</pua>\n\t<eggIncluded>1</eggIncluded>\n\t<silence>1</silence>\n\t<membercount>144</membercount>\n\t<signature>N0_V1_Pb/YkXmW|v1_ao44IA6s</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'NewMsgId': 1892689057994959200, 'MsgSeq': 871407286}
2025-07-29 00:26:22 | INFO | 收到文本消息: 消息ID:89427541 来自:***********@chatroom 发送人:tianen532965049 @:[] 内容:好
2025-07-29 00:26:22 | INFO | 发送表情消息: 对方wxid:***********@chatroom md5:ec659f94db17b05434544a0c377ddfc0 总长度:9992069
2025-07-29 00:26:22 | DEBUG | 处理消息内容: '好'
2025-07-29 00:26:22 | DEBUG | 消息内容 '好' 不匹配任何命令，忽略
2025-07-29 00:26:32 | DEBUG | 收到消息: {'MsgId': 1029493788, 'FromUserName': {'string': '***********@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 1, 'Content': {'string': 'tianen532965049:\n地上也中'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1753719999, 'MsgSource': '<msgsource>\n\t<bizflag>0</bizflag>\n\t<pua>1</pua>\n\t<eggIncluded>1</eggIncluded>\n\t<silence>1</silence>\n\t<membercount>144</membercount>\n\t<signature>N0_V1_SymwmpjU|v1_2Bs9B4tp</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'NewMsgId': 6179519518835999361, 'MsgSeq': 871407289}
2025-07-29 00:26:32 | INFO | 收到文本消息: 消息ID:1029493788 来自:***********@chatroom 发送人:tianen532965049 @:[] 内容:地上也中
2025-07-29 00:26:32 | DEBUG | 处理消息内容: '地上也中'
2025-07-29 00:26:32 | DEBUG | 消息内容 '地上也中' 不匹配任何命令，忽略
2025-07-29 00:26:48 | DEBUG | 收到消息: {'MsgId': 907903105, 'FromUserName': {'string': '***********@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 47, 'Content': {'string': 'wxid_ikxxrwasicud11:\n<msg><emoji fromusername = "wxid_ikxxrwasicud11" tousername = "***********@chatroom" type="2" idbuffer="media:0_0" md5="67ce524ee00e3d286259e630c63d20d5" len = "875086" productid="" androidmd5="67ce524ee00e3d286259e630c63d20d5" androidlen="875086" s60v3md5 = "67ce524ee00e3d286259e630c63d20d5" s60v3len="875086" s60v5md5 = "67ce524ee00e3d286259e630c63d20d5" s60v5len="875086" cdnurl = "http://vweixinf.tc.qq.com/110/20401/stodownload?m=67ce524ee00e3d286259e630c63d20d5&amp;filekey=30440201010430302e02016e0402535a0420363763653532346565303065336432383632353965363330633633643230643502030d5a4e040d00000004627466730000000132&amp;hy=SZ&amp;storeid=2625d4524000045afde6b3c8e0000006e01004fb1535a1630c950b6b1a7cb5&amp;ef=1&amp;bizid=1022" designerid = "" thumburl = "" encrypturl = "http://vweixinf.tc.qq.com/110/20402/stodownload?m=2c0ad5f0c6159ff45705ff067c3ebf99&amp;filekey=30440201010430302e02016e0402535a0420326330616435663063363135396666343537303566663036376333656266393902030d5a50040d00000004627466730000000132&amp;hy=SZ&amp;storeid=2625d4524000246c7de6b3c8e0000006e02004fb2535a1630c950b6b1a7ce3&amp;ef=2&amp;bizid=1022" aeskey= "41efa13e9ae440819ab3f8f34f88232c" externurl = "http://vweixinf.tc.qq.com/110/20403/stodownload?m=b6af85b353a2296238f5164633ea24a9&amp;filekey=30440201010430302e02016e0402535a042062366166383562333533613232393632333866353136343633336561323461390203008800040d00000004627466730000000132&amp;hy=SZ&amp;storeid=2625d4524000445dfde6b3c8e0000006e03004fb3535a1630c950b6b1a7d15&amp;ef=3&amp;bizid=1022" externmd5 = "ecb61ecc98c7c91dee8d312229e2b5fd" width= "320" height= "320" tpurl= "" tpauthkey= "" attachedtext= "" attachedtextcolor= "" lensid= "" emojiattr= "" linkid= "" desc= "" ></emoji>  </msg>'}, 'Status': 3, 'ImgStatus': 2, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1753720015, 'MsgSource': '<msgsource>\n\t<silence>1</silence>\n\t<membercount>144</membercount>\n\t<signature>N0_V1_DKb0P4/L|v1_NowGBTxM</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'NewMsgId': 1128571216586056185, 'MsgSeq': 871407290}
2025-07-29 00:26:48 | INFO | 收到表情消息: 消息ID:907903105 来自:***********@chatroom 发送人:wxid_ikxxrwasicud11 MD5:67ce524ee00e3d286259e630c63d20d5 大小:875086
2025-07-29 00:26:48 | DEBUG | [DoubaoImageRecognition] 缓存表情消息: 1128571216586056185
2025-07-29 00:26:53 | DEBUG | 收到消息: {'MsgId': 2116799948, 'FromUserName': {'string': '***********@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 1, 'Content': {'string': 'wxid_idzryo4rneok22:\n其实哪都中'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1753720020, 'MsgSource': '<msgsource>\n\t<pua>1</pua>\n\t<eggIncluded>1</eggIncluded>\n\t<silence>1</silence>\n\t<membercount>144</membercount>\n\t<signature>N0_V1_pAOtzfVv|v1_AnJAZlU5</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'NewMsgId': 4549907017792359245, 'MsgSeq': 871407291}
2025-07-29 00:26:53 | INFO | 收到文本消息: 消息ID:2116799948 来自:***********@chatroom 发送人:wxid_idzryo4rneok22 @:[] 内容:其实哪都中
2025-07-29 00:26:53 | DEBUG | 处理消息内容: '其实哪都中'
2025-07-29 00:26:53 | DEBUG | 消息内容 '其实哪都中' 不匹配任何命令，忽略
2025-07-29 00:27:01 | DEBUG | 收到消息: {'MsgId': 884685015, 'FromUserName': {'string': '***********@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 1, 'Content': {'string': 'wxid_idzryo4rneok22:\n不道'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1753720028, 'MsgSource': '<msgsource>\n\t<pua>1</pua>\n\t<eggIncluded>1</eggIncluded>\n\t<silence>1</silence>\n\t<membercount>144</membercount>\n\t<signature>N0_V1_Ll4PqcAp|v1_BDHsMuT5</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'NewMsgId': 746319757016727465, 'MsgSeq': 871407292}
2025-07-29 00:27:01 | INFO | 收到文本消息: 消息ID:884685015 来自:***********@chatroom 发送人:wxid_idzryo4rneok22 @:[] 内容:不道
2025-07-29 00:27:01 | DEBUG | 处理消息内容: '不道'
2025-07-29 00:27:01 | DEBUG | 消息内容 '不道' 不匹配任何命令，忽略
2025-07-29 00:27:04 | DEBUG | 收到消息: {'MsgId': 673131836, 'FromUserName': {'string': '***********@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 1, 'Content': {'string': 'tianen532965049:\n该睡觉了🛌'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1753720029, 'MsgSource': '<msgsource>\n\t<bizflag>0</bizflag>\n\t<pua>1</pua>\n\t<eggIncluded>1</eggIncluded>\n\t<silence>1</silence>\n\t<membercount>144</membercount>\n\t<signature>N0_V1_Valh52Zl|v1_dipazi0R</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'NewMsgId': 6696174344253478839, 'MsgSeq': 871407293}
2025-07-29 00:27:04 | INFO | 收到文本消息: 消息ID:673131836 来自:***********@chatroom 发送人:tianen532965049 @:[] 内容:该睡觉了🛌
2025-07-29 00:27:04 | DEBUG | 处理消息内容: '该睡觉了🛌'
2025-07-29 00:27:04 | DEBUG | 消息内容 '该睡觉了🛌' 不匹配任何命令，忽略
2025-07-29 00:27:07 | DEBUG | 收到消息: {'MsgId': 1511192765, 'FromUserName': {'string': '***********@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 1, 'Content': {'string': 'wxid_ikxxrwasicud11:\n失陪'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1753720034, 'MsgSource': '<msgsource>\n\t<atuserlist><![CDATA[]]></atuserlist>\n\t<silence>1</silence>\n\t<membercount>144</membercount>\n\t<signature>N0_V1_98ZvZAnx|v1_TOT//oRy</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'NewMsgId': 1409173430183210645, 'MsgSeq': 871407294}
2025-07-29 00:27:07 | INFO | 收到文本消息: 消息ID:1511192765 来自:***********@chatroom 发送人:wxid_ikxxrwasicud11 @:[] 内容:失陪
2025-07-29 00:27:07 | DEBUG | 处理消息内容: '失陪'
2025-07-29 00:27:07 | DEBUG | 消息内容 '失陪' 不匹配任何命令，忽略
2025-07-29 00:27:15 | DEBUG | 收到消息: {'MsgId': 1796056543, 'FromUserName': {'string': '***********@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 47, 'Content': {'string': 'wxid_ikxxrwasicud11:\n<msg><emoji fromusername = "wxid_ikxxrwasicud11" tousername = "***********@chatroom" type="2" idbuffer="media:0_0" md5="a2ad4e675d0f0f24c2d2ae34e9ba66ba" len = "196206" productid="" androidmd5="a2ad4e675d0f0f24c2d2ae34e9ba66ba" androidlen="196206" s60v3md5 = "a2ad4e675d0f0f24c2d2ae34e9ba66ba" s60v3len="196206" s60v5md5 = "a2ad4e675d0f0f24c2d2ae34e9ba66ba" s60v5len="196206" cdnurl = "http://vweixinf.tc.qq.com/110/20401/stodownload?m=a2ad4e675d0f0f24c2d2ae34e9ba66ba&amp;filekey=30440201010430302e02016e0402534804206132616434653637356430663066323463326432616533346539626136366261020302fe6e040d00000004627466730000000132&amp;hy=SH&amp;storeid=26747dc1c000d2f7786f158000000006e01004fb153481aa3f03156dde48e5&amp;ef=1&amp;bizid=1022" designerid = "" thumburl = "" encrypturl = "http://vweixinf.tc.qq.com/110/20402/stodownload?m=0b4f8a0eb1fbe1246dc92c481da5b83c&amp;filekey=30440201010430302e02016e0402534804203062346638613065623166626531323436646339326334383164613562383363020302fe70040d00000004627466730000000132&amp;hy=SH&amp;storeid=26747dc1c000e026286f158000000006e02004fb253481aa3f03156dde48f0&amp;ef=2&amp;bizid=1022" aeskey= "9d3213785e844652b7e2f07853ed3379" externurl = "http://vweixinf.tc.qq.com/110/20403/stodownload?m=832b915071ce51eb80ffd425c074c70e&amp;filekey=3043020101042f302d02016e040253480420383332623931353037316365353165623830666664343235633037346337306502025640040d00000004627466730000000132&amp;hy=SH&amp;storeid=26747dc1c000ec6c186f158000000006e03004fb353481aa3f03156dde490c&amp;ef=3&amp;bizid=1022" externmd5 = "425fa088601e05c3dc935c6697360564" width= "240" height= "240" tpurl= "" tpauthkey= "" attachedtext= "" attachedtextcolor= "" lensid= "" emojiattr= "" linkid= "" desc= "" ></emoji>  </msg>'}, 'Status': 3, 'ImgStatus': 2, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1753720042, 'MsgSource': '<msgsource>\n\t<silence>1</silence>\n\t<membercount>144</membercount>\n\t<signature>N0_V1_FJgRrKTh|v1_N1S9Zkj5</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'NewMsgId': 5596415081449083295, 'MsgSeq': 871407295}
2025-07-29 00:27:15 | INFO | 收到表情消息: 消息ID:1796056543 来自:***********@chatroom 发送人:wxid_ikxxrwasicud11 MD5:a2ad4e675d0f0f24c2d2ae34e9ba66ba 大小:196206
2025-07-29 00:27:15 | DEBUG | [DoubaoImageRecognition] 缓存表情消息: 5596415081449083295
2025-07-29 00:27:16 | DEBUG | 收到消息: {'MsgId': 254156229, 'FromUserName': {'string': '***********@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 47, 'Content': {'string': 'wxid_1ul5r40nibpn12:\n<msg><emoji fromusername="wxid_1ul5r40nibpn12" tousername="***********@chatroom" type="2" idbuffer="media:0_0" md5="ece0550e29c1a576c5952e5c272bd363" len="14509" productid="" androidmd5="ece0550e29c1a576c5952e5c272bd363" androidlen="14509" s60v3md5="ece0550e29c1a576c5952e5c272bd363" s60v3len="14509" s60v5md5="ece0550e29c1a576c5952e5c272bd363" s60v5len="14509" cdnurl="http://vweixinf.tc.qq.com/110/20401/stodownload?m=ece0550e29c1a576c5952e5c272bd363&amp;filekey=3043020101042f302d02016e0402535a04206563653035353065323963316135373663353935326535633237326264333633020238ad040d00000004627466730000000132&amp;hy=SZ&amp;storeid=2685ba7c40002207ac8e1688c0000006e01004fb1535a2b7f9011568a40d90&amp;ef=1&amp;bizid=1022" designerid="" thumburl="" encrypturl="http://vweixinf.tc.qq.com/110/20402/stodownload?m=52b3256031e760418007c8b8a847e644&amp;filekey=3043020101042f302d02016e0402535a04203532623332353630333165373630343138303037633862386138343765363434020238b0040d00000004627466730000000132&amp;hy=SZ&amp;storeid=2685ba7c40002e38ac8e1688c0000006e02004fb2535a2b7f9011568a40da3&amp;ef=2&amp;bizid=1022" aeskey="c20a686ca1c3425982f17a2d3bf89c59" externurl="http://vweixinf.tc.qq.com/110/20403/stodownload?m=ad8dd11f31fe03b7582289edfaf5037b&amp;filekey=3043020101042f302d02016e0402535a04206164386464313166333166653033623735383232383965646661663530333762020217e0040d00000004627466730000000132&amp;hy=SZ&amp;storeid=2685ba7c4000340e0c8e1688c0000006e03004fb3535a2b7f9011568a40db1&amp;ef=3&amp;bizid=1022" externmd5="db573e786bad99398f4eb26203969b1a" width="406" height="406" tpurl="" tpauthkey="" attachedtext="" attachedtextcolor="" lensid="" emojiattr="" linkid="" desc=""></emoji><gameext type="0" content="0"></gameext></msg>'}, 'Status': 3, 'ImgStatus': 2, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1753720043, 'MsgSource': '<msgsource>\n\t<silence>1</silence>\n\t<membercount>144</membercount>\n\t<signature>N0_V1_kzUHW5hu|v1_ohRVdXjm</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'NewMsgId': 4575355188902226569, 'MsgSeq': 871407296}
2025-07-29 00:27:16 | INFO | 收到表情消息: 消息ID:254156229 来自:***********@chatroom 发送人:wxid_1ul5r40nibpn12 MD5:ece0550e29c1a576c5952e5c272bd363 大小:14509
2025-07-29 00:27:16 | DEBUG | [DoubaoImageRecognition] 缓存表情消息: 4575355188902226569
2025-07-29 00:27:16 | DEBUG | 收到消息: {'MsgId': 2003688739, 'FromUserName': {'string': '***********@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 1, 'Content': {'string': 'wxid_idzryo4rneok22:\n肘了'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1753720043, 'MsgSource': '<msgsource>\n\t<pua>1</pua>\n\t<eggIncluded>1</eggIncluded>\n\t<silence>1</silence>\n\t<membercount>144</membercount>\n\t<signature>N0_V1_AMxGQBXh|v1_cJpudYlB</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'NewMsgId': 3424120477033018471, 'MsgSeq': 871407297}
2025-07-29 00:27:16 | INFO | 收到文本消息: 消息ID:2003688739 来自:***********@chatroom 发送人:wxid_idzryo4rneok22 @:[] 内容:肘了
2025-07-29 00:27:16 | DEBUG | 处理消息内容: '肘了'
2025-07-29 00:27:16 | DEBUG | 消息内容 '肘了' 不匹配任何命令，忽略
2025-07-29 00:27:19 | DEBUG | 收到消息: {'MsgId': 167225191, 'FromUserName': {'string': '***********@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 1, 'Content': {'string': 'tianen532965049:\n失陪'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1753720046, 'MsgSource': '<msgsource>\n\t<bizflag>0</bizflag>\n\t<pua>1</pua>\n\t<eggIncluded>1</eggIncluded>\n\t<silence>1</silence>\n\t<membercount>144</membercount>\n\t<signature>N0_V1_wVBCgK7l|v1_yK3LmYgT</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'NewMsgId': 1626013860039388029, 'MsgSeq': 871407298}
2025-07-29 00:27:19 | INFO | 收到文本消息: 消息ID:167225191 来自:***********@chatroom 发送人:tianen532965049 @:[] 内容:失陪
2025-07-29 00:27:19 | DEBUG | 处理消息内容: '失陪'
2025-07-29 00:27:19 | DEBUG | 消息内容 '失陪' 不匹配任何命令，忽略
2025-07-29 00:27:37 | DEBUG | 收到消息: {'MsgId': 590940941, 'FromUserName': {'string': '***********@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 49, 'Content': {'string': 'wxid_snv13qf05qjx11:\n<?xml version="1.0"?>\n<msg>\n\t<appmsg appid="" sdkver="0">\n\t\t<title>女鹅</title>\n\t\t<type>57</type>\n\t\t<appattach>\n\t\t\t<cdnthumbaeskey />\n\t\t\t<aeskey />\n\t\t</appattach>\n\t\t<refermsg>\n\t\t\t<type>47</type>\n\t\t\t<svrid>4575355188902226569</svrid>\n\t\t\t<fromusr>***********@chatroom</fromusr>\n\t\t\t<chatusr>wxid_1ul5r40nibpn12</chatusr>\n\t\t\t<displayname>昭禾以纯</displayname>\n\t\t\t<content>&lt;msg&gt;&lt;emoji fromusername="wxid_1ul5r40nibpn12" tousername="***********@chatroom" type="2" idbuffer="media:0_0" md5="ece0550e29c1a576c5952e5c272bd363" len="14509" productid="" androidmd5="ece0550e29c1a576c5952e5c272bd363" androidlen="14509" s60v3md5="ece0550e29c1a576c5952e5c272bd363" s60v3len="14509" s60v5md5="ece0550e29c1a576c5952e5c272bd363" s60v5len="14509" cdnurl="http://vweixinf.tc.qq.com/110/20401/stodownload?m=ece0550e29c1a576c5952e5c272bd363&amp;amp;filekey=3043020101042f302d02016e0402535a04206563653035353065323963316135373663353935326535633237326264333633020238ad040d00000004627466730000000132&amp;amp;hy=SZ&amp;amp;storeid=2685ba7c40002207ac8e1688c0000006e01004fb1535a2b7f9011568a40d90&amp;amp;ef=1&amp;amp;bizid=1022" designerid="" thumburl="" encrypturl="http://vweixinf.tc.qq.com/110/20402/stodownload?m=52b3256031e760418007c8b8a847e644&amp;amp;filekey=3043020101042f302d02016e0402535a04203532623332353630333165373630343138303037633862386138343765363434020238b0040d00000004627466730000000132&amp;amp;hy=SZ&amp;amp;storeid=2685ba7c40002e38ac8e1688c0000006e02004fb2535a2b7f9011568a40da3&amp;amp;ef=2&amp;amp;bizid=1022" aeskey="c20a686ca1c3425982f17a2d3bf89c59" externurl="http://vweixinf.tc.qq.com/110/20403/stodownload?m=ad8dd11f31fe03b7582289edfaf5037b&amp;amp;filekey=3043020101042f302d02016e0402535a04206164386464313166333166653033623735383232383965646661663530333762020217e0040d00000004627466730000000132&amp;amp;hy=SZ&amp;amp;storeid=2685ba7c4000340e0c8e1688c0000006e03004fb3535a2b7f9011568a40db1&amp;amp;ef=3&amp;amp;bizid=1022" externmd5="db573e786bad99398f4eb26203969b1a" width="406" height="406" tpurl="" tpauthkey="" attachedtext="" attachedtextcolor="" lensid="" emojiattr="" linkid="" desc=""&gt;&lt;/emoji&gt;&lt;gameext type="0" content="0"&gt;&lt;/gameext&gt;&lt;/msg&gt;</content>\n\t\t\t<msgsource>&lt;msgsource&gt;&lt;sequence_id&gt;766645916&lt;/sequence_id&gt;\n\t&lt;silence&gt;1&lt;/silence&gt;\n\t&lt;membercount&gt;144&lt;/membercount&gt;\n\t&lt;signature&gt;N0_V1_auE8n1y0|v1_wjYD9Z3E&lt;/signature&gt;\n\t&lt;tmp_node&gt;\n\t\t&lt;publisher-id&gt;&lt;/publisher-id&gt;\n\t&lt;/tmp_node&gt;\n&lt;/msgsource&gt;\n</msgsource>\n\t\t\t<createtime>1753720043</createtime>\n\t\t</refermsg>\n\t</appmsg>\n\t<fromusername>wxid_snv13qf05qjx11</fromusername>\n\t<scene>0</scene>\n\t<appinfo>\n\t\t<version>1</version>\n\t\t<appname></appname>\n\t</appinfo>\n\t<commenturl></commenturl>\n</msg>\n'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1753720064, 'MsgSource': '<msgsource>\n\t<bizflag>0</bizflag>\n\t<pua>1</pua>\n\t<eggIncluded>1</eggIncluded>\n\t<sec_msg_node>\n\t\t<uuid>6526ee3d7962917953f4ae9dd595af9d_</uuid>\n\t\t<risk-file-flag />\n\t\t<risk-file-md5-list />\n\t</sec_msg_node>\n\t<silence>1</silence>\n\t<membercount>144</membercount>\n\t<signature>N0_V1_nTKYEXzJ|v1_2j9I1f4t</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'NewMsgId': 1287969809595047285, 'MsgSeq': 871407299}
2025-07-29 00:27:37 | DEBUG | 从群聊消息中提取发送者: wxid_snv13qf05qjx11
2025-07-29 00:27:37 | DEBUG | 使用已解析的XML处理引用消息
2025-07-29 00:27:37 | INFO | 收到引用消息: 消息ID:590940941 来自:***********@chatroom 发送人:wxid_snv13qf05qjx11 内容:女鹅 引用类型:47
2025-07-29 00:27:37 | INFO | [DouBaoImageToImage] 收到引用消息: 女鹅
2025-07-29 00:27:37 | INFO | [TimerTask] 收到引用消息调试信息:
2025-07-29 00:27:37 | INFO |   - 消息内容: 女鹅
2025-07-29 00:27:37 | INFO |   - 群组ID: ***********@chatroom
2025-07-29 00:27:37 | INFO |   - 发送人: wxid_snv13qf05qjx11
2025-07-29 00:27:37 | INFO |   - 引用信息: {'MsgType': 47, 'Content': '<msg><emoji fromusername="wxid_1ul5r40nibpn12" tousername="***********@chatroom" type="2" idbuffer="media:0_0" md5="ece0550e29c1a576c5952e5c272bd363" len="14509" productid="" androidmd5="ece0550e29c1a576c5952e5c272bd363" androidlen="14509" s60v3md5="ece0550e29c1a576c5952e5c272bd363" s60v3len="14509" s60v5md5="ece0550e29c1a576c5952e5c272bd363" s60v5len="14509" cdnurl="http://vweixinf.tc.qq.com/110/20401/stodownload?m=ece0550e29c1a576c5952e5c272bd363&amp;filekey=3043020101042f302d02016e0402535a04206563653035353065323963316135373663353935326535633237326264333633020238ad040d00000004627466730000000132&amp;hy=SZ&amp;storeid=2685ba7c40002207ac8e1688c0000006e01004fb1535a2b7f9011568a40d90&amp;ef=1&amp;bizid=1022" designerid="" thumburl="" encrypturl="http://vweixinf.tc.qq.com/110/20402/stodownload?m=52b3256031e760418007c8b8a847e644&amp;filekey=3043020101042f302d02016e0402535a04203532623332353630333165373630343138303037633862386138343765363434020238b0040d00000004627466730000000132&amp;hy=SZ&amp;storeid=2685ba7c40002e38ac8e1688c0000006e02004fb2535a2b7f9011568a40da3&amp;ef=2&amp;bizid=1022" aeskey="c20a686ca1c3425982f17a2d3bf89c59" externurl="http://vweixinf.tc.qq.com/110/20403/stodownload?m=ad8dd11f31fe03b7582289edfaf5037b&amp;filekey=3043020101042f302d02016e0402535a04206164386464313166333166653033623735383232383965646661663530333762020217e0040d00000004627466730000000132&amp;hy=SZ&amp;storeid=2685ba7c4000340e0c8e1688c0000006e03004fb3535a2b7f9011568a40db1&amp;ef=3&amp;bizid=1022" externmd5="db573e786bad99398f4eb26203969b1a" width="406" height="406" tpurl="" tpauthkey="" attachedtext="" attachedtextcolor="" lensid="" emojiattr="" linkid="" desc=""></emoji><gameext type="0" content="0"></gameext></msg>', 'Msgid': '4575355188902226569', 'NewMsgId': '4575355188902226569', 'ToWxid': 'wxid_4usgcju5ey9q29', 'FromWxid': '***********@chatroom', 'Nickname': '昭禾以纯', 'MsgSource': '<msgsource><sequence_id>766645916</sequence_id>\n\t<silence>1</silence>\n\t<membercount>144</membercount>\n\t<signature>N0_V1_auE8n1y0|v1_wjYD9Z3E</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'Createtime': '1753720043', 'SenderWxid': 'wxid_snv13qf05qjx11'}
2025-07-29 00:27:37 | INFO |   - 引用消息ID: 
2025-07-29 00:27:37 | INFO |   - 引用消息类型: 
2025-07-29 00:27:37 | INFO |   - 引用消息内容: <msg><emoji fromusername="wxid_1ul5r40nibpn12" tousername="***********@chatroom" type="2" idbuffer="media:0_0" md5="ece0550e29c1a576c5952e5c272bd363" len="14509" productid="" androidmd5="ece0550e29c1a576c5952e5c272bd363" androidlen="14509" s60v3md5="ece0550e29c1a576c5952e5c272bd363" s60v3len="14509" s60v5md5="ece0550e29c1a576c5952e5c272bd363" s60v5len="14509" cdnurl="http://vweixinf.tc.qq.com/110/20401/stodownload?m=ece0550e29c1a576c5952e5c272bd363&amp;filekey=3043020101042f302d02016e0402535a04206563653035353065323963316135373663353935326535633237326264333633020238ad040d00000004627466730000000132&amp;hy=SZ&amp;storeid=2685ba7c40002207ac8e1688c0000006e01004fb1535a2b7f9011568a40d90&amp;ef=1&amp;bizid=1022" designerid="" thumburl="" encrypturl="http://vweixinf.tc.qq.com/110/20402/stodownload?m=52b3256031e760418007c8b8a847e644&amp;filekey=3043020101042f302d02016e0402535a04203532623332353630333165373630343138303037633862386138343765363434020238b0040d00000004627466730000000132&amp;hy=SZ&amp;storeid=2685ba7c40002e38ac8e1688c0000006e02004fb2535a2b7f9011568a40da3&amp;ef=2&amp;bizid=1022" aeskey="c20a686ca1c3425982f17a2d3bf89c59" externurl="http://vweixinf.tc.qq.com/110/20403/stodownload?m=ad8dd11f31fe03b7582289edfaf5037b&amp;filekey=3043020101042f302d02016e0402535a04206164386464313166333166653033623735383232383965646661663530333762020217e0040d00000004627466730000000132&amp;hy=SZ&amp;storeid=2685ba7c4000340e0c8e1688c0000006e03004fb3535a2b7f9011568a40db1&amp;ef=3&amp;bizid=1022" externmd5="db573e786bad99398f4eb26203969b1a" width="406" height="406" tpurl="" tpauthkey="" attachedtext="" attachedtextcolor="" lensid="" emojiattr="" linkid="" desc=""></emoji><gameext type="0" content="0"></gameext></msg>
2025-07-29 00:27:37 | INFO |   - 引用消息发送人: wxid_snv13qf05qjx11
2025-07-29 00:28:27 | DEBUG | 收到消息: {'MsgId': 1974270391, 'FromUserName': {'string': '***********@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 1, 'Content': {'string': 'wxid_1ul5r40nibpn12:\n再见'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1753720114, 'MsgSource': '<msgsource>\n\t<bizflag>0</bizflag>\n\t<pua>1</pua>\n\t<eggIncluded>1</eggIncluded>\n\t<silence>1</silence>\n\t<membercount>144</membercount>\n\t<signature>N0_V1_KuGHJKrT|v1_IdgZ/jZI</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'NewMsgId': 6420196423369394523, 'MsgSeq': 871407300}
2025-07-29 00:28:27 | INFO | 收到文本消息: 消息ID:1974270391 来自:***********@chatroom 发送人:wxid_1ul5r40nibpn12 @:[] 内容:再见
2025-07-29 00:28:27 | DEBUG | 处理消息内容: '再见'
2025-07-29 00:28:27 | DEBUG | 消息内容 '再见' 不匹配任何命令，忽略
2025-07-29 00:28:43 | DEBUG | 收到消息: {'MsgId': 1252142522, 'FromUserName': {'string': '***********@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 1, 'Content': {'string': 'wxid_idzryo4rneok22:\n你不睡觉 你是不是不想干了'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1753720130, 'MsgSource': '<msgsource>\n\t<pua>1</pua>\n\t<eggIncluded>1</eggIncluded>\n\t<silence>1</silence>\n\t<membercount>144</membercount>\n\t<signature>N0_V1_wNJikVhQ|v1_1dQsURck</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'NewMsgId': 6076209811109777941, 'MsgSeq': 871407301}
2025-07-29 00:28:43 | INFO | 收到文本消息: 消息ID:1252142522 来自:***********@chatroom 发送人:wxid_idzryo4rneok22 @:[] 内容:你不睡觉 你是不是不想干了
2025-07-29 00:28:43 | DEBUG | 处理消息内容: '你不睡觉 你是不是不想干了'
2025-07-29 00:28:43 | DEBUG | 消息内容 '你不睡觉 你是不是不想干了' 不匹配任何命令，忽略
2025-07-29 00:28:46 | DEBUG | 收到消息: {'MsgId': 128613407, 'FromUserName': {'string': '***********@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 1, 'Content': {'string': 'wxid_xv01lkcmn48l22:\n笑死我了'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1753720130, 'MsgSource': '<msgsource>\n\t<bizflag>0</bizflag>\n\t<pua>1</pua>\n\t<eggIncluded>1</eggIncluded>\n\t<silence>1</silence>\n\t<membercount>144</membercount>\n\t<signature>N0_V1_PN0gVJFl|v1_8MU4JCQR</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'NewMsgId': 3991126988815891772, 'MsgSeq': 871407302}
2025-07-29 00:28:46 | INFO | 收到文本消息: 消息ID:128613407 来自:***********@chatroom 发送人:wxid_xv01lkcmn48l22 @:[] 内容:笑死我了
2025-07-29 00:28:46 | DEBUG | 处理消息内容: '笑死我了'
2025-07-29 00:28:46 | DEBUG | 消息内容 '笑死我了' 不匹配任何命令，忽略
2025-07-29 00:29:10 | DEBUG | 收到消息: {'MsgId': 1192469913, 'FromUserName': {'string': '***********@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 1, 'Content': {'string': 'wxid_snv13qf05qjx11:\n来 你笑一个'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1753720157, 'MsgSource': '<msgsource>\n\t<bizflag>0</bizflag>\n\t<pua>1</pua>\n\t<eggIncluded>1</eggIncluded>\n\t<silence>1</silence>\n\t<membercount>144</membercount>\n\t<signature>N0_V1_WH3Ddk1N|v1_fCBekhzX</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'NewMsgId': 5201132965057690534, 'MsgSeq': 871407303}
2025-07-29 00:29:10 | INFO | 收到文本消息: 消息ID:1192469913 来自:***********@chatroom 发送人:wxid_snv13qf05qjx11 @:[] 内容:来 你笑一个
2025-07-29 00:29:10 | DEBUG | 处理消息内容: '来 你笑一个'
2025-07-29 00:29:10 | DEBUG | 消息内容 '来 你笑一个' 不匹配任何命令，忽略
2025-07-29 00:29:12 | DEBUG | 收到消息: {'MsgId': 1234473656, 'FromUserName': {'string': '***********@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 1, 'Content': {'string': 'wxid_1ul5r40nibpn12:\n姐没课了'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1753720158, 'MsgSource': '<msgsource>\n\t<bizflag>0</bizflag>\n\t<pua>1</pua>\n\t<eggIncluded>1</eggIncluded>\n\t<silence>1</silence>\n\t<membercount>144</membercount>\n\t<signature>N0_V1_lRrc+Lqs|v1_fGZNK3P9</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'NewMsgId': 689499701612340732, 'MsgSeq': 871407304}
2025-07-29 00:29:12 | INFO | 收到文本消息: 消息ID:1234473656 来自:***********@chatroom 发送人:wxid_1ul5r40nibpn12 @:[] 内容:姐没课了
2025-07-29 00:29:12 | DEBUG | 处理消息内容: '姐没课了'
2025-07-29 00:29:12 | DEBUG | 消息内容 '姐没课了' 不匹配任何命令，忽略
2025-07-29 00:29:15 | DEBUG | 收到消息: {'MsgId': 1827738880, 'FromUserName': {'string': '***********@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 1, 'Content': {'string': 'wxid_1ul5r40nibpn12:\n明天放假了'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1753720162, 'MsgSource': '<msgsource>\n\t<bizflag>0</bizflag>\n\t<pua>1</pua>\n\t<eggIncluded>1</eggIncluded>\n\t<silence>1</silence>\n\t<membercount>144</membercount>\n\t<signature>N0_V1_szeA5ZlC|v1_CtI6abHL</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'NewMsgId': 6068679186342344408, 'MsgSeq': 871407305}
2025-07-29 00:29:15 | INFO | 收到文本消息: 消息ID:1827738880 来自:***********@chatroom 发送人:wxid_1ul5r40nibpn12 @:[] 内容:明天放假了
2025-07-29 00:29:15 | DEBUG | 处理消息内容: '明天放假了'
2025-07-29 00:29:15 | DEBUG | 消息内容 '明天放假了' 不匹配任何命令，忽略
2025-07-29 00:29:18 | DEBUG | 收到消息: {'MsgId': 2040314164, 'FromUserName': {'string': '***********@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 1, 'Content': {'string': 'tianen532965049:\n笑发财了'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1753720162, 'MsgSource': '<msgsource>\n\t<bizflag>0</bizflag>\n\t<pua>1</pua>\n\t<eggIncluded>1</eggIncluded>\n\t<silence>1</silence>\n\t<membercount>144</membercount>\n\t<signature>N0_V1_TNsY1Uuk|v1_Foc3fkti</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'NewMsgId': 1667772326428716064, 'MsgSeq': 871407306}
2025-07-29 00:29:18 | INFO | 收到文本消息: 消息ID:2040314164 来自:***********@chatroom 发送人:tianen532965049 @:[] 内容:笑发财了
2025-07-29 00:29:18 | DEBUG | 处理消息内容: '笑发财了'
2025-07-29 00:29:18 | DEBUG | 消息内容 '笑发财了' 不匹配任何命令，忽略
2025-07-29 00:29:22 | DEBUG | 收到消息: {'MsgId': 1395898503, 'FromUserName': {'string': '***********@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 47, 'Content': {'string': 'wxid_1ul5r40nibpn12:\n<msg><emoji fromusername="wxid_1ul5r40nibpn12" tousername="***********@chatroom" type="2" idbuffer="media:0_0" md5="170f8e7f7f315f0b6fd79b9d10280900" len="6620" productid="" androidmd5="170f8e7f7f315f0b6fd79b9d10280900" androidlen="6620" s60v3md5="170f8e7f7f315f0b6fd79b9d10280900" s60v3len="6620" s60v5md5="170f8e7f7f315f0b6fd79b9d10280900" s60v5len="6620" cdnurl="http://vweixinf.tc.qq.com/110/20401/stodownload?m=170f8e7f7f315f0b6fd79b9d10280900&amp;filekey=3043020101042f302d02016e0402534804203137306638653766376633313566306236666437396239643130323830393030020219dc040d00000004627466730000000132&amp;hy=SH&amp;storeid=265a1122b000c6d0122c6a5ed0000006e01004fb1534823307bd1e67d02dfe&amp;ef=1&amp;bizid=1022" designerid="" thumburl="" encrypturl="http://vweixinf.tc.qq.com/110/20402/stodownload?m=95d2594b8b4d444c0b1811d85a9b1a80&amp;filekey=3043020101042f302d02016e0402534804203935643235393462386234643434346330623138313164383561396231613830020219e0040d00000004627466730000000132&amp;hy=SH&amp;storeid=265a1122b000d3a8822c6a5ed0000006e02004fb2534823307bd1e67d02e0e&amp;ef=2&amp;bizid=1022" aeskey="15b72192fbee4457b050b85e970de652" externurl="http://vweixinf.tc.qq.com/110/20403/stodownload?m=471683cbe5b516e45c097666841ffce8&amp;filekey=3043020101042f302d02016e040253480420343731363833636265356235313665343563303937363636383431666663653802020b70040d00000004627466730000000132&amp;hy=SH&amp;storeid=265a1122b000de06922c6a5ed0000006e03004fb3534823307bd1e67d02e1d&amp;ef=3&amp;bizid=1022" externmd5="3c0339fdc75f21038a6dfefc5332de4b" width="216" height="239" tpurl="" tpauthkey="" attachedtext="" attachedtextcolor="" lensid="" emojiattr="" linkid="" desc=""></emoji><gameext type="0" content="0"></gameext></msg>'}, 'Status': 3, 'ImgStatus': 2, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1753720165, 'MsgSource': '<msgsource>\n\t<silence>1</silence>\n\t<membercount>144</membercount>\n\t<signature>N0_V1_ylFB4IGE|v1_DNk84luB</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'NewMsgId': 4971971487037172099, 'MsgSeq': 871407307}
2025-07-29 00:29:22 | INFO | 收到表情消息: 消息ID:1395898503 来自:***********@chatroom 发送人:wxid_1ul5r40nibpn12 MD5:170f8e7f7f315f0b6fd79b9d10280900 大小:6620
2025-07-29 00:29:22 | DEBUG | [DoubaoImageRecognition] 缓存表情消息: 4971971487037172099
2025-07-29 00:29:22 | DEBUG | 收到消息: {'MsgId': 966165599, 'FromUserName': {'string': '***********@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 10002, 'Content': {'string': '***********@chatroom:\n<sysmsg type="pat">\n<pat>\n  <fromusername>wxid_snv13qf05qjx11</fromusername>\n  <chatusername>***********@chatroom</chatusername>\n  <pattedusername>wxid_xv01lkcmn48l22</pattedusername>\n  <patsuffix><![CDATA[]]></patsuffix>\n  <patsuffixversion>2</patsuffixversion>\n\n\n\n\n  <template><![CDATA["${wxid_snv13qf05qjx11}" 拍了拍 "${wxid_xv01lkcmn48l22}"]]></template>\n\n\n\n\n</pat>\n</sysmsg>'}, 'Status': 4, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1753720165, 'MsgSource': '<msgsource>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'NewMsgId': 5814784886984425537, 'MsgSeq': 871407308}
2025-07-29 00:29:22 | DEBUG | 系统消息类型: pat
2025-07-29 00:29:22 | INFO | 收到拍一拍消息: 消息ID:966165599 来自:***********@chatroom 发送人:***********@chatroom 拍者:wxid_snv13qf05qjx11 被拍:wxid_xv01lkcmn48l22 后缀:None
2025-07-29 00:29:22 | DEBUG | [PatReply] 被拍者 wxid_xv01lkcmn48l22 不是目标用户 wxid_4usgcju5ey9q29，跳过
2025-07-29 00:29:27 | DEBUG | 收到消息: {'MsgId': 1023686513, 'FromUserName': {'string': '***********@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 1, 'Content': {'string': 'wxid_1ul5r40nibpn12:\n别逼我了 我真没招了'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1753720174, 'MsgSource': '<msgsource>\n\t<bizflag>0</bizflag>\n\t<pua>1</pua>\n\t<eggIncluded>1</eggIncluded>\n\t<silence>1</silence>\n\t<membercount>144</membercount>\n\t<signature>N0_V1_5p+ROeaa|v1_0C7ozwYm</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'NewMsgId': 5309154129732953485, 'MsgSeq': 871407309}
2025-07-29 00:29:27 | INFO | 收到文本消息: 消息ID:1023686513 来自:***********@chatroom 发送人:wxid_1ul5r40nibpn12 @:[] 内容:别逼我了 我真没招了
2025-07-29 00:29:27 | DEBUG | 处理消息内容: '别逼我了 我真没招了'
2025-07-29 00:29:27 | DEBUG | 消息内容 '别逼我了 我真没招了' 不匹配任何命令，忽略
2025-07-29 00:29:32 | DEBUG | 收到消息: {'MsgId': 1037769368, 'FromUserName': {'string': '***********@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 1, 'Content': {'string': 'wxid_idzryo4rneok22:\n你还有假呐'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1753720179, 'MsgSource': '<msgsource>\n\t<pua>1</pua>\n\t<eggIncluded>1</eggIncluded>\n\t<silence>1</silence>\n\t<membercount>144</membercount>\n\t<signature>N0_V1_rRmh/I0P|v1_bkuTCfQD</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'NewMsgId': 5233790729637120106, 'MsgSeq': 871407310}
2025-07-29 00:29:32 | INFO | 收到文本消息: 消息ID:1037769368 来自:***********@chatroom 发送人:wxid_idzryo4rneok22 @:[] 内容:你还有假呐
2025-07-29 00:29:32 | DEBUG | 处理消息内容: '你还有假呐'
2025-07-29 00:29:32 | DEBUG | 消息内容 '你还有假呐' 不匹配任何命令，忽略
2025-07-29 00:29:35 | DEBUG | 收到消息: {'MsgId': 811409886, 'FromUserName': {'string': '***********@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 1, 'Content': {'string': 'tianen532965049:\n放假了排位自己打吧'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1753720179, 'MsgSource': '<msgsource>\n\t<bizflag>0</bizflag>\n\t<pua>1</pua>\n\t<eggIncluded>1</eggIncluded>\n\t<silence>1</silence>\n\t<membercount>144</membercount>\n\t<signature>N0_V1_A5zdTNW4|v1_yKMM8MFD</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'NewMsgId': 7565890989653129321, 'MsgSeq': 871407311}
2025-07-29 00:29:35 | INFO | 收到文本消息: 消息ID:811409886 来自:***********@chatroom 发送人:tianen532965049 @:[] 内容:放假了排位自己打吧
2025-07-29 00:29:35 | DEBUG | 处理消息内容: '放假了排位自己打吧'
2025-07-29 00:29:35 | DEBUG | 消息内容 '放假了排位自己打吧' 不匹配任何命令，忽略
2025-07-29 00:29:39 | DEBUG | 收到消息: {'MsgId': 1288202240, 'FromUserName': {'string': '***********@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 1, 'Content': {'string': 'wxid_xv01lkcmn48l22:\n我不爱笑'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1753720186, 'MsgSource': '<msgsource>\n\t<bizflag>0</bizflag>\n\t<pua>1</pua>\n\t<eggIncluded>1</eggIncluded>\n\t<silence>1</silence>\n\t<membercount>144</membercount>\n\t<signature>N0_V1_wuvLyrLR|v1_bOiWWs9n</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'NewMsgId': 7445284939859706005, 'MsgSeq': 871407312}
2025-07-29 00:29:39 | INFO | 收到文本消息: 消息ID:1288202240 来自:***********@chatroom 发送人:wxid_xv01lkcmn48l22 @:[] 内容:我不爱笑
2025-07-29 00:29:39 | DEBUG | 处理消息内容: '我不爱笑'
2025-07-29 00:29:39 | DEBUG | 消息内容 '我不爱笑' 不匹配任何命令，忽略
2025-07-29 00:29:42 | DEBUG | 收到消息: {'MsgId': 371473770, 'FromUserName': {'string': '***********@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 1, 'Content': {'string': 'wxid_1ul5r40nibpn12:\n我又上班了'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1753720188, 'MsgSource': '<msgsource>\n\t<bizflag>0</bizflag>\n\t<pua>1</pua>\n\t<eggIncluded>1</eggIncluded>\n\t<silence>1</silence>\n\t<membercount>144</membercount>\n\t<signature>N0_V1_T5tQwgPv|v1_G+pnAWIB</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'NewMsgId': 8139814517936261990, 'MsgSeq': 871407313}
2025-07-29 00:29:42 | INFO | 收到文本消息: 消息ID:371473770 来自:***********@chatroom 发送人:wxid_1ul5r40nibpn12 @:[] 内容:我又上班了
2025-07-29 00:29:42 | DEBUG | 处理消息内容: '我又上班了'
2025-07-29 00:29:42 | DEBUG | 消息内容 '我又上班了' 不匹配任何命令，忽略
2025-07-29 00:29:49 | DEBUG | 收到消息: {'MsgId': 1889280352, 'FromUserName': {'string': '***********@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 47, 'Content': {'string': 'wxid_xv01lkcmn48l22:\n<msg><emoji fromusername = "wxid_xv01lkcmn48l22" tousername = "***********@chatroom" type="2" idbuffer="media:0_0" md5="1c22a9ff2cfe567044cd58696c2e579d" len = "32098" productid="" androidmd5="1c22a9ff2cfe567044cd58696c2e579d" androidlen="32098" s60v3md5 = "1c22a9ff2cfe567044cd58696c2e579d" s60v3len="32098" s60v5md5 = "1c22a9ff2cfe567044cd58696c2e579d" s60v5len="32098" cdnurl = "http://wxapp.tc.qq.com/262/20304/stodownload?m=1c22a9ff2cfe567044cd58696c2e579d&amp;filekey=30340201010420301e020201060402534804101c22a9ff2cfe567044cd58696c2e579d02027d62040d00000004627466730000000132&amp;hy=SH&amp;storeid=266c85625000357dd2933d49d0000010600004f50534826132031569db05da&amp;bizid=1023" designerid = "" thumburl = "" encrypturl = "http://wxapp.tc.qq.com/262/20304/stodownload?m=bcc7a677c335b574a4b5879a9dee9083&amp;filekey=30340201010420301e02020106040253480410bcc7a677c335b574a4b5879a9dee908302027d70040d00000004627466730000000132&amp;hy=SH&amp;storeid=266c8562500041b872933d49d0000010600004f5053482fb478e0b75bffc13&amp;bizid=1023" aeskey= "be6040d3f564d3f05fd104de094a13f4" externurl = "http://wxapp.tc.qq.com/262/20304/stodownload?m=b8185fe276362e520c54288f460ecbe6&amp;filekey=30340201010420301e02020106040253480410b8185fe276362e520c54288f460ecbe6020236c0040d00000004627466730000000132&amp;hy=SH&amp;storeid=266c8572e000980262933d49d0000010600004f50534826f39031577405d52&amp;bizid=1023" externmd5 = "d877a40ef50eb801484dff6a5fd8b710" width= "150" height= "151" tpurl= "" tpauthkey= "" attachedtext= "" attachedtextcolor= "" lensid= "" emojiattr= "" linkid= "" desc= "" ></emoji> <gameext type="0" content="0" ></gameext> </msg>'}, 'Status': 3, 'ImgStatus': 2, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1753720196, 'MsgSource': '<msgsource>\n\t<silence>1</silence>\n\t<membercount>144</membercount>\n\t<signature>N0_V1_3ui8ERYj|v1_7DZeKDls</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'NewMsgId': 1847966790596031638, 'MsgSeq': 871407314}
2025-07-29 00:29:49 | INFO | 收到表情消息: 消息ID:1889280352 来自:***********@chatroom 发送人:wxid_xv01lkcmn48l22 MD5:1c22a9ff2cfe567044cd58696c2e579d 大小:32098
2025-07-29 00:29:49 | DEBUG | [DoubaoImageRecognition] 缓存表情消息: 1847966790596031638
2025-07-29 00:29:49 | DEBUG | 收到消息: {'MsgId': 2078502718, 'FromUserName': {'string': '***********@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 1, 'Content': {'string': 'wxid_1ul5r40nibpn12:\n我起来上班了'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1753720196, 'MsgSource': '<msgsource>\n\t<bizflag>0</bizflag>\n\t<pua>1</pua>\n\t<eggIncluded>1</eggIncluded>\n\t<silence>1</silence>\n\t<membercount>144</membercount>\n\t<signature>N0_V1_4uauru8G|v1_aUVWC0CU</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'NewMsgId': 7765767728238457789, 'MsgSeq': 871407315}
2025-07-29 00:29:49 | INFO | 收到文本消息: 消息ID:2078502718 来自:***********@chatroom 发送人:wxid_1ul5r40nibpn12 @:[] 内容:我起来上班了
2025-07-29 00:29:49 | DEBUG | 处理消息内容: '我起来上班了'
2025-07-29 00:29:49 | DEBUG | 消息内容 '我起来上班了' 不匹配任何命令，忽略
2025-07-29 00:29:53 | DEBUG | 收到消息: {'MsgId': 189748874, 'FromUserName': {'string': '***********@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 47, 'Content': {'string': 'tianen532965049:\n<msg><emoji fromusername = "tianen532965049" tousername = "***********@chatroom" type="2" idbuffer="media:0_0" md5="7a62cdb74d27fa268d10cf6cc7dfcbfc" len = "136172" productid="" androidmd5="7a62cdb74d27fa268d10cf6cc7dfcbfc" androidlen="136172" s60v3md5 = "7a62cdb74d27fa268d10cf6cc7dfcbfc" s60v3len="136172" s60v5md5 = "7a62cdb74d27fa268d10cf6cc7dfcbfc" s60v5len="136172" cdnurl = "http://vweixinf.tc.qq.com/110/20401/stodownload?m=7a62cdb74d27fa268d10cf6cc7dfcbfc&amp;filekey=30440201010430302e02016e040253480420376136326364623734643237666132363864313063663663633764666362666302030213ec040d00000004627466730000000132&amp;hy=SH&amp;storeid=264bcffe40000f01c53cf07e80000006e01004fb153482916ab40b6f0ff45e&amp;ef=1&amp;bizid=1022" designerid = "" thumburl = "" encrypturl = "http://vweixinf.tc.qq.com/110/20402/stodownload?m=d9ff6e7ad1ed285ce5ac65028d2be315&amp;filekey=30440201010430302e02016e040253480420643966663665376164316564323835636535616336353032386432626533313502030213f0040d00000004627466730000000132&amp;hy=SH&amp;storeid=264bcffe40001dee853cf07e80000006e02004fb253482916ab40b6f0ff471&amp;ef=2&amp;bizid=1022" aeskey= "89b06b9e0b004bbd913e166c1ec2d3ab" externurl = "http://vweixinf.tc.qq.com/110/20403/stodownload?m=30db96aa847472ce8d6b0f4e36279b8d&amp;filekey=3043020101042f302d02016e040253480420333064623936616138343734373263653864366230663465333632373962386402023220040d00000004627466730000000132&amp;hy=SH&amp;storeid=264bcffe40002d04553cf07e80000006e03004fb353482916ab40b6f0ff48a&amp;ef=3&amp;bizid=1022" externmd5 = "8f0c8a8d09eeca9115e4785a6a3804c3" width= "300" height= "300" tpurl= "" tpauthkey= "" attachedtext= "" attachedtextcolor= "" lensid= "" emojiattr= "" linkid= "" desc= "" ></emoji> <gameext type="0" content="0" ></gameext> </msg>'}, 'Status': 3, 'ImgStatus': 2, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1753720198, 'MsgSource': '<msgsource>\n\t<silence>1</silence>\n\t<membercount>144</membercount>\n\t<signature>N0_V1_u8x9r72U|v1_TjVzXTAw</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'NewMsgId': 5085130336516553152, 'MsgSeq': 871407316}
2025-07-29 00:29:53 | INFO | 收到表情消息: 消息ID:189748874 来自:***********@chatroom 发送人:tianen532965049 MD5:7a62cdb74d27fa268d10cf6cc7dfcbfc 大小:136172
2025-07-29 00:29:53 | DEBUG | [DoubaoImageRecognition] 缓存表情消息: 5085130336516553152
2025-07-29 00:29:55 | DEBUG | 收到消息: {'MsgId': 1460265748, 'FromUserName': {'string': '***********@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 1, 'Content': {'string': 'wxid_snv13qf05qjx11:\n她一号的班'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1753720202, 'MsgSource': '<msgsource>\n\t<bizflag>0</bizflag>\n\t<pua>1</pua>\n\t<eggIncluded>1</eggIncluded>\n\t<silence>1</silence>\n\t<membercount>144</membercount>\n\t<signature>N0_V1_ssGTIyqg|v1_S5JxvJEo</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'NewMsgId': 8399335951175582781, 'MsgSeq': 871407317}
2025-07-29 00:29:55 | INFO | 收到文本消息: 消息ID:1460265748 来自:***********@chatroom 发送人:wxid_snv13qf05qjx11 @:[] 内容:她一号的班
2025-07-29 00:29:55 | DEBUG | 处理消息内容: '她一号的班'
2025-07-29 00:29:55 | DEBUG | 消息内容 '她一号的班' 不匹配任何命令，忽略
2025-07-29 00:30:01 | DEBUG | 收到消息: {'MsgId': 955607241, 'FromUserName': {'string': '***********@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 47, 'Content': {'string': 'wxid_1ul5r40nibpn12:\n<msg><emoji fromusername="wxid_1ul5r40nibpn12" tousername="***********@chatroom" type="3" idbuffer="media:0_0" md5="1bd262f31203d54d8eafcf9badd0c0e3" len="49587" productid="" androidmd5="1bd262f31203d54d8eafcf9badd0c0e3" androidlen="49587" s60v3md5="1bd262f31203d54d8eafcf9badd0c0e3" s60v3len="49587" s60v5md5="1bd262f31203d54d8eafcf9badd0c0e3" s60v5len="49587" cdnurl="http://vweixinf.tc.qq.com/110/20401/stodownload?m=1bd262f31203d54d8eafcf9badd0c0e3&amp;filekey=30440201010430302e02016e0402535a04203162643236326633313230336435346438656166636639626164643063306533020300c1b3040d00000004627466730000000132&amp;hy=SZ&amp;storeid=266be877b00049552aaee718a0000006e01004fb1535a25e6ebc1e78a8eda7&amp;ef=1&amp;bizid=1022" designerid="" thumburl="" encrypturl="http://vweixinf.tc.qq.com/110/20402/stodownload?m=e5385de9a4a80563e868db8215f46ac4&amp;filekey=30440201010430302e02016e0402535a04206535333835646539613461383035363365383638646238323135663436616334020300c1c0040d00000004627466730000000132&amp;hy=SZ&amp;storeid=266be877b000539b0aaee718a0000006e02004fb2535a25e6ebc1e78a8edb5&amp;ef=2&amp;bizid=1022" aeskey="2efeeaa198ba417986896d4bce7c7993" externurl="http://vweixinf.tc.qq.com/110/20403/stodownload?m=6d8012be4dac3d30b9aaee5566727843&amp;filekey=3043020101042f302d02016e0402535a04203664383031326265346461633364333062396161656535353636373237383433020255c0040d00000004627466730000000132&amp;hy=SZ&amp;storeid=266be877b0005dca7aaee718a0000006e03004fb3535a25e6ebc1e78a8edbe&amp;ef=3&amp;bizid=1022" externmd5="5323f0378c4c1663622020bd62d1dda6" width="690" height="700" tpurl="" tpauthkey="" attachedtext="" attachedtextcolor="" lensid="" emojiattr="" linkid="" desc=""></emoji><gameext type="0" content="0"></gameext></msg>'}, 'Status': 3, 'ImgStatus': 2, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1753720207, 'MsgSource': '<msgsource>\n\t<silence>1</silence>\n\t<membercount>144</membercount>\n\t<signature>N0_V1_YgCv9KQC|v1_/XrT/Mq4</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'NewMsgId': 9207543309963728148, 'MsgSeq': 871407318}
2025-07-29 00:30:01 | INFO | 收到表情消息: 消息ID:955607241 来自:***********@chatroom 发送人:wxid_1ul5r40nibpn12 MD5:1bd262f31203d54d8eafcf9badd0c0e3 大小:49587
2025-07-29 00:30:01 | DEBUG | [DoubaoImageRecognition] 缓存表情消息: 9207543309963728148
2025-07-29 00:30:06 | DEBUG | 收到消息: {'MsgId': 1562720596, 'FromUserName': {'string': '***********@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 1, 'Content': {'string': 'wxid_xv01lkcmn48l22:\n一轮接一轮啊'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1753720213, 'MsgSource': '<msgsource>\n\t<bizflag>0</bizflag>\n\t<pua>1</pua>\n\t<eggIncluded>1</eggIncluded>\n\t<silence>1</silence>\n\t<membercount>144</membercount>\n\t<signature>N0_V1_lXXtNATu|v1_DhqSiFFw</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'NewMsgId': 3120542044155834142, 'MsgSeq': 871407319}
2025-07-29 00:30:06 | INFO | 收到文本消息: 消息ID:1562720596 来自:***********@chatroom 发送人:wxid_xv01lkcmn48l22 @:[] 内容:一轮接一轮啊
2025-07-29 00:30:06 | DEBUG | 处理消息内容: '一轮接一轮啊'
2025-07-29 00:30:06 | DEBUG | 消息内容 '一轮接一轮啊' 不匹配任何命令，忽略
2025-07-29 00:30:12 | DEBUG | 收到消息: {'MsgId': 1174133860, 'FromUserName': {'string': '***********@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 1, 'Content': {'string': 'wxid_1ul5r40nibpn12:\n啊的'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1753720219, 'MsgSource': '<msgsource>\n\t<bizflag>0</bizflag>\n\t<pua>1</pua>\n\t<eggIncluded>1</eggIncluded>\n\t<silence>1</silence>\n\t<membercount>144</membercount>\n\t<signature>N0_V1_OvsxyMF7|v1_3y26DnCq</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'NewMsgId': 8014208150574501024, 'MsgSeq': 871407320}
2025-07-29 00:30:12 | INFO | 收到文本消息: 消息ID:1174133860 来自:***********@chatroom 发送人:wxid_1ul5r40nibpn12 @:[] 内容:啊的
2025-07-29 00:30:12 | DEBUG | 处理消息内容: '啊的'
2025-07-29 00:30:12 | DEBUG | 消息内容 '啊的' 不匹配任何命令，忽略
2025-07-29 00:30:22 | DEBUG | 收到消息: {'MsgId': 2024973768, 'FromUserName': {'string': '***********@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 49, 'Content': {'string': 'tianen532965049:\n<?xml version="1.0"?>\n<msg>\n\t<appmsg appid="" sdkver="0">\n\t\t<title>这样吗</title>\n\t\t<type>57</type>\n\t\t<appattach>\n\t\t\t<cdnthumbaeskey />\n\t\t\t<aeskey />\n\t\t</appattach>\n\t\t<refermsg>\n\t\t\t<type>1</type>\n\t\t\t<svrid>8399335951175582781</svrid>\n\t\t\t<fromusr>***********@chatroom</fromusr>\n\t\t\t<chatusr>wxid_snv13qf05qjx11</chatusr>\n\t\t\t<displayname>夕未语</displayname>\n\t\t\t<content>她一号的班</content>\n\t\t\t<msgsource>&lt;msgsource&gt;&lt;sequence_id&gt;911393836&lt;/sequence_id&gt;\n\t&lt;bizflag&gt;0&lt;/bizflag&gt;\n\t&lt;pua&gt;1&lt;/pua&gt;\n\t&lt;eggIncluded&gt;1&lt;/eggIncluded&gt;\n\t&lt;silence&gt;1&lt;/silence&gt;\n\t&lt;membercount&gt;144&lt;/membercount&gt;\n\t&lt;signature&gt;N0_V1_cMzXnKpB|v1_Lpj4SjZY&lt;/signature&gt;\n\t&lt;tmp_node&gt;\n\t\t&lt;publisher-id&gt;&lt;/publisher-id&gt;\n\t&lt;/tmp_node&gt;\n&lt;/msgsource&gt;\n</msgsource>\n\t\t\t<createtime>1753720202</createtime>\n\t\t</refermsg>\n\t</appmsg>\n\t<fromusername>tianen532965049</fromusername>\n\t<scene>0</scene>\n\t<appinfo>\n\t\t<version>1</version>\n\t\t<appname></appname>\n\t</appinfo>\n\t<commenturl></commenturl>\n</msg>\n'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1753720229, 'MsgSource': '<msgsource>\n\t<bizflag>0</bizflag>\n\t<pua>1</pua>\n\t<eggIncluded>1</eggIncluded>\n\t<sec_msg_node>\n\t\t<uuid>5b42cf81d252191fd1a5be2e056e001f_</uuid>\n\t\t<risk-file-flag />\n\t\t<risk-file-md5-list />\n\t</sec_msg_node>\n\t<silence>1</silence>\n\t<membercount>144</membercount>\n\t<signature>N0_V1_D3m+g+99|v1_fJn2iBCH</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'NewMsgId': 2757923593140110540, 'MsgSeq': 871407321}
2025-07-29 00:30:22 | DEBUG | 从群聊消息中提取发送者: tianen532965049
2025-07-29 00:30:22 | DEBUG | 使用已解析的XML处理引用消息
2025-07-29 00:30:22 | INFO | 收到引用消息: 消息ID:2024973768 来自:***********@chatroom 发送人:tianen532965049 内容:这样吗 引用类型:1
2025-07-29 00:30:22 | INFO | [DouBaoImageToImage] 收到引用消息: 这样吗
2025-07-29 00:30:22 | INFO | [TimerTask] 收到引用消息调试信息:
2025-07-29 00:30:22 | INFO |   - 消息内容: 这样吗
2025-07-29 00:30:22 | INFO |   - 群组ID: ***********@chatroom
2025-07-29 00:30:22 | INFO |   - 发送人: tianen532965049
2025-07-29 00:30:22 | INFO |   - 引用信息: {'MsgType': 1, 'Content': '她一号的班', 'Msgid': '8399335951175582781', 'NewMsgId': '8399335951175582781', 'ToWxid': 'wxid_4usgcju5ey9q29', 'FromWxid': '***********@chatroom', 'Nickname': '夕未语', 'MsgSource': '<msgsource><sequence_id>911393836</sequence_id>\n\t<bizflag>0</bizflag>\n\t<pua>1</pua>\n\t<eggIncluded>1</eggIncluded>\n\t<silence>1</silence>\n\t<membercount>144</membercount>\n\t<signature>N0_V1_cMzXnKpB|v1_Lpj4SjZY</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'Createtime': '1753720202', 'SenderWxid': 'tianen532965049'}
2025-07-29 00:30:22 | INFO |   - 引用消息ID: 
2025-07-29 00:30:22 | INFO |   - 引用消息类型: 
2025-07-29 00:30:22 | INFO |   - 引用消息内容: 她一号的班
2025-07-29 00:30:22 | INFO |   - 引用消息发送人: tianen532965049
2025-07-29 00:30:26 | DEBUG | 收到消息: {'MsgId': 742711698, 'FromUserName': {'string': '***********@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 1, 'Content': {'string': 'wxid_1ul5r40nibpn12:\n@饿飞\u2005 不要怕 我可以先帮你分担一个号'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1753720233, 'MsgSource': '<msgsource>\n\t<atuserlist>tianen532965049</atuserlist>\n\t<bizflag>0</bizflag>\n\t<pua>1</pua>\n\t<eggIncluded>1</eggIncluded>\n\t<silence>1</silence>\n\t<membercount>144</membercount>\n\t<signature>N0_V1_+z57+qku|v1_As/iuNCX</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'NewMsgId': 8998072175898908661, 'MsgSeq': 871407322}
2025-07-29 00:30:26 | INFO | 收到文本消息: 消息ID:742711698 来自:***********@chatroom 发送人:wxid_1ul5r40nibpn12 @:['tianen532965049'] 内容:@饿飞  不要怕 我可以先帮你分担一个号
2025-07-29 00:30:26 | DEBUG | 处理消息内容: '@饿飞  不要怕 我可以先帮你分担一个号'
2025-07-29 00:30:26 | DEBUG | 消息内容 '@饿飞  不要怕 我可以先帮你分担一个号' 不匹配任何命令，忽略
2025-07-29 00:30:31 | DEBUG | 收到消息: {'MsgId': 211728029, 'FromUserName': {'string': '***********@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 47, 'Content': {'string': 'wxid_1ul5r40nibpn12:\n<msg><emoji fromusername="wxid_1ul5r40nibpn12" tousername="***********@chatroom" type="2" idbuffer="media:0_0" md5="26f020497503aecb1ddc5fd0511444e8" len="46732" productid="" androidmd5="26f020497503aecb1ddc5fd0511444e8" androidlen="46732" s60v3md5="26f020497503aecb1ddc5fd0511444e8" s60v3len="46732" s60v5md5="26f020497503aecb1ddc5fd0511444e8" s60v5len="46732" cdnurl="http://vweixinf.tc.qq.com/110/20401/stodownload?m=26f020497503aecb1ddc5fd0511444e8&amp;filekey=30440201010430302e02016e0402534804203236663032303439373530336165636231646463356664303531313434346538020300b68c040d00000004627466730000000132&amp;hy=SH&amp;storeid=265b529250002366acdd05b000000006e01004fb153480ec65b01e69268cfe&amp;ef=1&amp;bizid=1022" designerid="" thumburl="" encrypturl="http://vweixinf.tc.qq.com/110/20402/stodownload?m=c13e07b1d2bf0782d8ce82e29b8673de&amp;filekey=30440201010430302e02016e0402534804206331336530376231643262663037383264386365383265323962383637336465020300b690040d00000004627466730000000132&amp;hy=SH&amp;storeid=265b529250002e855cdd05b000000006e02004fb253480ec65b01e69268d0d&amp;ef=2&amp;bizid=1022" aeskey="7b0354106c094084b91f067a5a589ecc" externurl="http://vweixinf.tc.qq.com/110/20403/stodownload?m=b9a44a7f3abff7e70db9e52c5516b752&amp;filekey=3043020101042f302d02016e040253480420623961343461376633616266663765373064623965353263353531366237353202023b00040d00000004627466730000000132&amp;hy=SH&amp;storeid=265b5292500038548cdd05b000000006e03004fb353480ec65b01e69268d14&amp;ef=3&amp;bizid=1022" externmd5="0403dff945cd5f4cb7f96f126d9dbcd7" width="986" height="986" tpurl="" tpauthkey="" attachedtext="" attachedtextcolor="" lensid="" emojiattr="" linkid="" desc=""></emoji><gameext type="0" content="0"></gameext></msg>'}, 'Status': 3, 'ImgStatus': 2, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1753720238, 'MsgSource': '<msgsource>\n\t<silence>1</silence>\n\t<membercount>144</membercount>\n\t<signature>N0_V1_3GWfyAhG|v1_nq5Uitp7</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'NewMsgId': 9137271126693340045, 'MsgSeq': 871407323}
2025-07-29 00:30:31 | INFO | 收到表情消息: 消息ID:211728029 来自:***********@chatroom 发送人:wxid_1ul5r40nibpn12 MD5:26f020497503aecb1ddc5fd0511444e8 大小:46732
2025-07-29 00:30:31 | DEBUG | [DoubaoImageRecognition] 缓存表情消息: 9137271126693340045
2025-07-29 00:30:43 | DEBUG | 收到消息: {'MsgId': 1592368050, 'FromUserName': {'string': '***********@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 49, 'Content': {'string': 'wxid_snv13qf05qjx11:\n<?xml version="1.0"?>\n<msg>\n\t<appmsg appid="" sdkver="0">\n\t\t<title>是的 哈哈哈 好巧哦</title>\n\t\t<type>57</type>\n\t\t<appattach>\n\t\t\t<cdnthumbaeskey />\n\t\t\t<aeskey />\n\t\t</appattach>\n\t\t<refermsg>\n\t\t\t<type>49</type>\n\t\t\t<svrid>2757923593140110540</svrid>\n\t\t\t<fromusr>***********@chatroom</fromusr>\n\t\t\t<chatusr>tianen532965049</chatusr>\n\t\t\t<displayname>饿飞</displayname>\n\t\t\t<content>&lt;msg&gt;&lt;appmsg appid=""  sdkver="0"&gt;&lt;title&gt;这样吗&lt;/title&gt;&lt;type&gt;57&lt;/type&gt;&lt;appattach&gt;&lt;cdnthumbaeskey&gt;&lt;/cdnthumbaeskey&gt;&lt;aeskey&gt;&lt;/aeskey&gt;&lt;/appattach&gt;&lt;/appmsg&gt;&lt;fromusername&gt;tianen532965049&lt;/fromusername&gt;&lt;appinfo&gt;&lt;version&gt;1&lt;/version&gt;&lt;appname&gt;&lt;/appname&gt;&lt;isforceupdate&gt;1&lt;/isforceupdate&gt;&lt;/appinfo&gt;&lt;/msg&gt;</content>\n\t\t\t<msgsource>&lt;msgsource&gt;&lt;sequence_id&gt;766645972&lt;/sequence_id&gt;\n\t&lt;bizflag&gt;0&lt;/bizflag&gt;\n\t&lt;pua&gt;1&lt;/pua&gt;\n\t&lt;eggIncluded&gt;1&lt;/eggIncluded&gt;\n\t&lt;sec_msg_node&gt;\n\t\t&lt;uuid&gt;5b42cf81d252191fd1a5be2e056e001f_&lt;/uuid&gt;\n\t\t&lt;risk-file-flag /&gt;\n\t\t&lt;risk-file-md5-list /&gt;\n\t&lt;/sec_msg_node&gt;\n\t&lt;silence&gt;1&lt;/silence&gt;\n\t&lt;membercount&gt;144&lt;/membercount&gt;\n\t&lt;signature&gt;N0_V1_DE6M/Ajw|v1_kr6O+9Os&lt;/signature&gt;\n\t&lt;tmp_node&gt;\n\t\t&lt;publisher-id&gt;&lt;/publisher-id&gt;\n\t&lt;/tmp_node&gt;\n&lt;/msgsource&gt;\n</msgsource>\n\t\t\t<createtime>1753720229</createtime>\n\t\t</refermsg>\n\t</appmsg>\n\t<fromusername>wxid_snv13qf05qjx11</fromusername>\n\t<scene>0</scene>\n\t<appinfo>\n\t\t<version>1</version>\n\t\t<appname></appname>\n\t</appinfo>\n\t<commenturl></commenturl>\n</msg>\n'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1753720250, 'MsgSource': '<msgsource>\n\t<bizflag>0</bizflag>\n\t<pua>1</pua>\n\t<eggIncluded>1</eggIncluded>\n\t<sec_msg_node>\n\t\t<uuid>9a8911d9a4b4b7ce77ab325f81490fe3_</uuid>\n\t\t<risk-file-flag />\n\t\t<risk-file-md5-list />\n\t</sec_msg_node>\n\t<silence>1</silence>\n\t<membercount>144</membercount>\n\t<signature>N0_V1_Nh/Zsc0z|v1_cfmj5OSw</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'NewMsgId': 3857562130011703557, 'MsgSeq': 871407324}
2025-07-29 00:30:43 | DEBUG | 从群聊消息中提取发送者: wxid_snv13qf05qjx11
2025-07-29 00:30:43 | DEBUG | 使用已解析的XML处理引用消息
2025-07-29 00:30:43 | INFO | 收到引用消息: 消息ID:1592368050 来自:***********@chatroom 发送人:wxid_snv13qf05qjx11 内容:是的 哈哈哈 好巧哦 引用类型:49
2025-07-29 00:30:43 | INFO | [DouBaoImageToImage] 收到引用消息: 是的 哈哈哈 好巧哦
2025-07-29 00:30:43 | INFO | [TimerTask] 收到引用消息调试信息:
2025-07-29 00:30:43 | INFO |   - 消息内容: 是的 哈哈哈 好巧哦
2025-07-29 00:30:43 | INFO |   - 群组ID: ***********@chatroom
2025-07-29 00:30:43 | INFO |   - 发送人: wxid_snv13qf05qjx11
2025-07-29 00:30:43 | INFO |   - 引用信息: {'MsgType': 49, 'Content': '<msg><appmsg appid=""  sdkver="0"><title>这样吗</title><type>57</type><appattach><cdnthumbaeskey></cdnthumbaeskey><aeskey></aeskey></appattach></appmsg><fromusername>tianen532965049</fromusername><appinfo><version>1</version><appname></appname><isforceupdate>1</isforceupdate></appinfo></msg>', 'Msgid': '2757923593140110540', 'NewMsgId': '2757923593140110540', 'ToWxid': 'wxid_4usgcju5ey9q29', 'FromWxid': '***********@chatroom', 'Nickname': '饿飞', 'MsgSource': '<msgsource><sequence_id>766645972</sequence_id>\n\t<bizflag>0</bizflag>\n\t<pua>1</pua>\n\t<eggIncluded>1</eggIncluded>\n\t<sec_msg_node>\n\t\t<uuid>5b42cf81d252191fd1a5be2e056e001f_</uuid>\n\t\t<risk-file-flag />\n\t\t<risk-file-md5-list />\n\t</sec_msg_node>\n\t<silence>1</silence>\n\t<membercount>144</membercount>\n\t<signature>N0_V1_DE6M/Ajw|v1_kr6O+9Os</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'Createtime': '1753720229', 'SenderWxid': 'wxid_snv13qf05qjx11'}
2025-07-29 00:30:43 | INFO |   - 引用消息ID: 
2025-07-29 00:30:43 | INFO |   - 引用消息类型: 
2025-07-29 00:30:43 | INFO |   - 引用消息内容: <msg><appmsg appid=""  sdkver="0"><title>这样吗</title><type>57</type><appattach><cdnthumbaeskey></cdnthumbaeskey><aeskey></aeskey></appattach></appmsg><fromusername>tianen532965049</fromusername><appinfo><version>1</version><appname></appname><isforceupdate>1</isforceupdate></appinfo></msg>
2025-07-29 00:30:43 | INFO |   - 引用消息发送人: wxid_snv13qf05qjx11
2025-07-29 00:30:51 | DEBUG | 收到消息: {'MsgId': 1351775555, 'FromUserName': {'string': '***********@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 1, 'Content': {'string': 'tianen532965049:\n下个月我估计不得行'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1753720258, 'MsgSource': '<msgsource>\n\t<bizflag>0</bizflag>\n\t<pua>1</pua>\n\t<eggIncluded>1</eggIncluded>\n\t<silence>1</silence>\n\t<membercount>144</membercount>\n\t<signature>N0_V1_zsbqvmwv|v1_JeSC4j+3</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'NewMsgId': 8696085630412994889, 'MsgSeq': 871407325}
2025-07-29 00:30:51 | INFO | 收到文本消息: 消息ID:1351775555 来自:***********@chatroom 发送人:tianen532965049 @:[] 内容:下个月我估计不得行
2025-07-29 00:30:51 | DEBUG | 处理消息内容: '下个月我估计不得行'
2025-07-29 00:30:51 | DEBUG | 消息内容 '下个月我估计不得行' 不匹配任何命令，忽略
2025-07-29 00:31:03 | DEBUG | 收到消息: {'MsgId': 85691391, 'FromUserName': {'string': '***********@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 1, 'Content': {'string': 'wxid_1ul5r40nibpn12:\n没事 我十号上班 应该打的完'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1753720270, 'MsgSource': '<msgsource>\n\t<bizflag>0</bizflag>\n\t<pua>1</pua>\n\t<eggIncluded>1</eggIncluded>\n\t<silence>1</silence>\n\t<membercount>144</membercount>\n\t<signature>N0_V1_2V6HMeHG|v1_V5v8sk/d</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'NewMsgId': 7982217087339186512, 'MsgSeq': 871407326}
2025-07-29 00:31:03 | INFO | 收到文本消息: 消息ID:85691391 来自:***********@chatroom 发送人:wxid_1ul5r40nibpn12 @:[] 内容:没事 我十号上班 应该打的完
2025-07-29 00:31:03 | DEBUG | 处理消息内容: '没事 我十号上班 应该打的完'
2025-07-29 00:31:03 | DEBUG | 消息内容 '没事 我十号上班 应该打的完' 不匹配任何命令，忽略
2025-07-29 00:31:07 | DEBUG | 收到消息: {'MsgId': 1162538666, 'FromUserName': {'string': '***********@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 1, 'Content': {'string': 'wxid_1ul5r40nibpn12:\n吧'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1753720274, 'MsgSource': '<msgsource>\n\t<bizflag>0</bizflag>\n\t<pua>1</pua>\n\t<eggIncluded>1</eggIncluded>\n\t<silence>1</silence>\n\t<membercount>144</membercount>\n\t<signature>N0_V1_BfXvBWyy|v1_DRvSvSif</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'NewMsgId': 2757468289278478323, 'MsgSeq': 871407327}
2025-07-29 00:31:07 | INFO | 收到文本消息: 消息ID:1162538666 来自:***********@chatroom 发送人:wxid_1ul5r40nibpn12 @:[] 内容:吧
2025-07-29 00:31:07 | DEBUG | 处理消息内容: '吧'
2025-07-29 00:31:07 | DEBUG | 消息内容 '吧' 不匹配任何命令，忽略
2025-07-29 00:31:11 | DEBUG | 收到消息: {'MsgId': 857963265, 'FromUserName': {'string': '***********@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 47, 'Content': {'string': 'wxid_1ul5r40nibpn12:\n<msg><emoji fromusername="wxid_1ul5r40nibpn12" tousername="***********@chatroom" type="2" idbuffer="media:0_0" md5="2dd5ce1060d1524d74f7b38ac8568371" len="212274" productid="" androidmd5="2dd5ce1060d1524d74f7b38ac8568371" androidlen="212274" s60v3md5="2dd5ce1060d1524d74f7b38ac8568371" s60v3len="212274" s60v5md5="2dd5ce1060d1524d74f7b38ac8568371" s60v5len="212274" cdnurl="http://vweixinf.tc.qq.com/110/20401/stodownload?m=2dd5ce1060d1524d74f7b38ac8568371&amp;filekey=30440201010430302e02016e04025348042032646435636531303630643135323464373466376233386163383536383337310203033d32040d00000004627466730000000132&amp;hy=SH&amp;storeid=2680afd68000491d73f79cf860000006e01004fb153482f435b00b71af4553&amp;ef=1&amp;bizid=1022" designerid="" thumburl="" encrypturl="http://vweixinf.tc.qq.com/110/20402/stodownload?m=a12a3d5f84a140867e3ee220cf0929bf&amp;filekey=30440201010430302e02016e04025348042061313261336435663834613134303836376533656532323063663039323962660203033d40040d00000004627466730000000132&amp;hy=SH&amp;storeid=2680afd68000576853f79cf860000006e02004fb253482f435b00b71af4568&amp;ef=2&amp;bizid=1022" aeskey="45930c115c424b9d8110b2892033ca93" externurl="http://vweixinf.tc.qq.com/110/20403/stodownload?m=0cbeca7c73b9339f4d9363392c675c18&amp;filekey=30440201010430302e02016e0402534804203063626563613763373362393333396634643933363333393263363735633138020300b260040d00000004627466730000000132&amp;hy=SH&amp;storeid=2680afd680006607e3f79cf860000006e03004fb353482f435b00b71af4579&amp;ef=3&amp;bizid=1022" externmd5="904cc423d71642a9912005d010ab56b5" width="240" height="240" tpurl="" tpauthkey="" attachedtext="" attachedtextcolor="" lensid="" emojiattr="" linkid="" desc=""></emoji><gameext type="0" content="0"></gameext></msg>'}, 'Status': 3, 'ImgStatus': 2, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1753720278, 'MsgSource': '<msgsource>\n\t<silence>1</silence>\n\t<membercount>144</membercount>\n\t<signature>N0_V1_iCVn6+Rs|v1_VFOhINpd</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'NewMsgId': 1340338952153123027, 'MsgSeq': 871407328}
2025-07-29 00:31:11 | INFO | 收到表情消息: 消息ID:857963265 来自:***********@chatroom 发送人:wxid_1ul5r40nibpn12 MD5:2dd5ce1060d1524d74f7b38ac8568371 大小:212274
2025-07-29 00:31:11 | DEBUG | [DoubaoImageRecognition] 缓存表情消息: 1340338952153123027
2025-07-29 00:31:16 | DEBUG | 收到消息: {'MsgId': 1174611202, 'FromUserName': {'string': '***********@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 1, 'Content': {'string': 'tianen532965049:\n能'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1753720283, 'MsgSource': '<msgsource>\n\t<bizflag>0</bizflag>\n\t<pua>1</pua>\n\t<eggIncluded>1</eggIncluded>\n\t<silence>1</silence>\n\t<membercount>144</membercount>\n\t<signature>N0_V1_Max6l7zX|v1_xK1JRRaD</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'NewMsgId': 5385386035363115288, 'MsgSeq': 871407329}
2025-07-29 00:31:16 | INFO | 收到文本消息: 消息ID:1174611202 来自:***********@chatroom 发送人:tianen532965049 @:[] 内容:能
2025-07-29 00:31:16 | DEBUG | 处理消息内容: '能'
2025-07-29 00:31:16 | DEBUG | 消息内容 '能' 不匹配任何命令，忽略
2025-07-29 00:31:22 | DEBUG | 收到消息: {'MsgId': 1098527503, 'FromUserName': {'string': '***********@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 47, 'Content': {'string': 'tianen532965049:\n<msg><emoji fromusername = "tianen532965049" tousername = "***********@chatroom" type="2" idbuffer="media:0_0" md5="ecc0d179f5d326bad1a3d1a5cf86644b" len = "15440" productid="" androidmd5="ecc0d179f5d326bad1a3d1a5cf86644b" androidlen="15440" s60v3md5 = "ecc0d179f5d326bad1a3d1a5cf86644b" s60v3len="15440" s60v5md5 = "ecc0d179f5d326bad1a3d1a5cf86644b" s60v5len="15440" cdnurl = "http://vweixinf.tc.qq.com/110/20401/stodownload?m=ecc0d179f5d326bad1a3d1a5cf86644b&amp;filekey=3043020101042f302d02016e040253480420656363306431373966356433323662616431613364316135636638363634346202023c50040d00000004627466730000000132&amp;hy=SH&amp;storeid=265ac747300083c042da9bb9c0000006e01004fb1534819a85bc1e65c45556&amp;ef=1&amp;bizid=1022" designerid = "" thumburl = "" encrypturl = "http://vweixinf.tc.qq.com/110/20402/stodownload?m=7b17ad6af68d2f7c998680524e0014e7&amp;filekey=3043020101042f302d02016e040253480420376231376164366166363864326637633939383638303532346530303134653702023c60040d00000004627466730000000132&amp;hy=SH&amp;storeid=265ac74730008dc152da9bb9c0000006e02004fb2534819a85bc1e65c45562&amp;ef=2&amp;bizid=1022" aeskey= "44e03730c0994446a5baedf9145c8041" externurl = "http://vweixinf.tc.qq.com/110/20403/stodownload?m=2e3e1c91884c4c05ecceb059cf9583a0&amp;filekey=3043020101042f302d02016e040253480420326533653163393138383463346330356563636562303539636639353833613002021470040d00000004627466730000000132&amp;hy=SH&amp;storeid=265ac747300094d3a2da9bb9c0000006e03004fb3534819a85bc1e65c4556e&amp;ef=3&amp;bizid=1022" externmd5 = "f641c65c8277db2c57bd25adb4914e79" width= "639" height= "600" tpurl= "" tpauthkey= "" attachedtext= "" attachedtextcolor= "" lensid= "" emojiattr= "" linkid= "" desc= "" ></emoji> <gameext type="0" content="0" ></gameext> </msg>'}, 'Status': 3, 'ImgStatus': 2, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1753720289, 'MsgSource': '<msgsource>\n\t<silence>1</silence>\n\t<membercount>144</membercount>\n\t<signature>N0_V1_fdzt7W13|v1_324j5i5w</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'NewMsgId': 629841721309992661, 'MsgSeq': 871407330}
2025-07-29 00:31:22 | INFO | 收到表情消息: 消息ID:1098527503 来自:***********@chatroom 发送人:tianen532965049 MD5:ecc0d179f5d326bad1a3d1a5cf86644b 大小:15440
2025-07-29 00:31:22 | DEBUG | [DoubaoImageRecognition] 缓存表情消息: 629841721309992661
2025-07-29 00:31:23 | DEBUG | 收到消息: {'MsgId': 1348889696, 'FromUserName': {'string': '***********@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 1, 'Content': {'string': 'wxid_snv13qf05qjx11:\n你看你 你咋那么实诚'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1753720290, 'MsgSource': '<msgsource>\n\t<bizflag>0</bizflag>\n\t<pua>1</pua>\n\t<eggIncluded>1</eggIncluded>\n\t<silence>1</silence>\n\t<membercount>144</membercount>\n\t<signature>N0_V1_XMze04Mx|v1_orX3ELZn</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'NewMsgId': 3376025381077937610, 'MsgSeq': 871407331}
2025-07-29 00:31:23 | INFO | 收到文本消息: 消息ID:1348889696 来自:***********@chatroom 发送人:wxid_snv13qf05qjx11 @:[] 内容:你看你 你咋那么实诚
2025-07-29 00:31:23 | DEBUG | 处理消息内容: '你看你 你咋那么实诚'
2025-07-29 00:31:23 | DEBUG | 消息内容 '你看你 你咋那么实诚' 不匹配任何命令，忽略
2025-07-29 00:31:32 | DEBUG | 收到消息: {'MsgId': 2110228822, 'FromUserName': {'string': '***********@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 1, 'Content': {'string': 'wxid_snv13qf05qjx11:\n我都说1号了'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1753720299, 'MsgSource': '<msgsource>\n\t<bizflag>0</bizflag>\n\t<pua>1</pua>\n\t<eggIncluded>1</eggIncluded>\n\t<silence>1</silence>\n\t<membercount>144</membercount>\n\t<signature>N0_V1_ES1WaGgu|v1_qYdH4iwQ</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'NewMsgId': 790143826135052994, 'MsgSeq': 871407332}
2025-07-29 00:31:32 | INFO | 收到文本消息: 消息ID:2110228822 来自:***********@chatroom 发送人:wxid_snv13qf05qjx11 @:[] 内容:我都说1号了
2025-07-29 00:31:32 | DEBUG | 处理消息内容: '我都说1号了'
2025-07-29 00:31:32 | DEBUG | 消息内容 '我都说1号了' 不匹配任何命令，忽略
2025-07-29 00:31:35 | DEBUG | 收到消息: {'MsgId': 1006301681, 'FromUserName': {'string': '***********@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 47, 'Content': {'string': 'wxid_snv13qf05qjx11:\n<msg><emoji fromusername = "wxid_snv13qf05qjx11" tousername = "***********@chatroom" type="2" idbuffer="media:0_0" md5="b229889fbf6ea09518a7e25e323b7e3a" len = "54855" productid="" androidmd5="b229889fbf6ea09518a7e25e323b7e3a" androidlen="54855" s60v3md5 = "b229889fbf6ea09518a7e25e323b7e3a" s60v3len="54855" s60v5md5 = "b229889fbf6ea09518a7e25e323b7e3a" s60v5len="54855" cdnurl = "http://vweixinf.tc.qq.com/110/20401/stodownload?m=b229889fbf6ea09518a7e25e323b7e3a&amp;filekey=30440201010430302e02016e0402534804206232323938383966626636656130393531386137653235653332336237653361020300d647040d00000004627466730000000132&amp;hy=SH&amp;storeid=267d6560c0006d5d6f23060d10000006e01004fb153480e561b01e7a4f4430&amp;ef=1&amp;bizid=1022" designerid = "" thumburl = "" encrypturl = "http://vweixinf.tc.qq.com/110/20402/stodownload?m=58bfaf972fe112ce89f4f460c085e19d&amp;filekey=30440201010430302e02016e0402534804203538626661663937326665313132636538396634663436306330383565313964020300d650040d00000004627466730000000132&amp;hy=SH&amp;storeid=267d6560c00077b26f23060d10000006e02004fb253480e561b01e7a4f443f&amp;ef=2&amp;bizid=1022" aeskey= "dc1f9fb5a439428eb27d38c3f8207c27" externurl = "http://vweixinf.tc.qq.com/110/20403/stodownload?m=eb737f97e1a2b25983d79f9a30e83bbd&amp;filekey=30440201010430302e02016e04025348042065623733376639376531613262323539383364373966396133306538336262640203008820040d00000004627466730000000132&amp;hy=SH&amp;storeid=267d6560c00083f5cf23060d10000006e03004fb353480e561b01e7a4f444a&amp;ef=3&amp;bizid=1022" externmd5 = "1bc96202d4d233948fd3b7fd7f9dc384" width= "240" height= "240" tpurl= "" tpauthkey= "" attachedtext= "" attachedtextcolor= "" lensid= "" emojiattr= "" linkid= "" desc= "" ></emoji> <gameext type="0" content="0" ></gameext> </msg>'}, 'Status': 3, 'ImgStatus': 2, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1753720302, 'MsgSource': '<msgsource>\n\t<silence>1</silence>\n\t<membercount>144</membercount>\n\t<signature>N0_V1_MfujoJaf|v1_FDZGQnxD</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'NewMsgId': 8826561550103341740, 'MsgSeq': 871407333}
2025-07-29 00:31:35 | INFO | 收到表情消息: 消息ID:1006301681 来自:***********@chatroom 发送人:wxid_snv13qf05qjx11 MD5:b229889fbf6ea09518a7e25e323b7e3a 大小:54855
2025-07-29 00:31:35 | DEBUG | [DoubaoImageRecognition] 缓存表情消息: 8826561550103341740
2025-07-29 00:31:41 | DEBUG | 收到消息: {'MsgId': 1174437566, 'FromUserName': {'string': '***********@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 1, 'Content': {'string': 'wxid_xv01lkcmn48l22:\n哈哈哈哈'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1753720308, 'MsgSource': '<msgsource>\n\t<bizflag>0</bizflag>\n\t<pua>1</pua>\n\t<eggIncluded>1</eggIncluded>\n\t<silence>1</silence>\n\t<membercount>144</membercount>\n\t<signature>N0_V1_gH0Ziy0A|v1_WGBOpuKD</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'NewMsgId': 1919202901323338420, 'MsgSeq': 871407334}
2025-07-29 00:31:41 | INFO | 收到文本消息: 消息ID:1174437566 来自:***********@chatroom 发送人:wxid_xv01lkcmn48l22 @:[] 内容:哈哈哈哈
2025-07-29 00:31:41 | DEBUG | 处理消息内容: '哈哈哈哈'
2025-07-29 00:31:41 | DEBUG | 消息内容 '哈哈哈哈' 不匹配任何命令，忽略
2025-07-29 00:31:44 | DEBUG | 收到消息: {'MsgId': 816137801, 'FromUserName': {'string': '***********@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 1, 'Content': {'string': 'wxid_1ul5r40nibpn12:\n哈哈哈 人都说不太行了'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1753720308, 'MsgSource': '<msgsource>\n\t<bizflag>0</bizflag>\n\t<pua>1</pua>\n\t<eggIncluded>1</eggIncluded>\n\t<silence>1</silence>\n\t<membercount>144</membercount>\n\t<signature>N0_V1_Y5viX3Sa|v1_xg8JDhit</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'NewMsgId': 8367618114502698152, 'MsgSeq': 871407335}
2025-07-29 00:31:44 | INFO | 收到文本消息: 消息ID:816137801 来自:***********@chatroom 发送人:wxid_1ul5r40nibpn12 @:[] 内容:哈哈哈 人都说不太行了
2025-07-29 00:31:44 | DEBUG | 处理消息内容: '哈哈哈 人都说不太行了'
2025-07-29 00:31:44 | DEBUG | 消息内容 '哈哈哈 人都说不太行了' 不匹配任何命令，忽略
2025-07-29 00:31:48 | DEBUG | 收到消息: {'MsgId': 1427027594, 'FromUserName': {'string': '***********@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 1, 'Content': {'string': 'wxid_1ul5r40nibpn12:\n没事 到时候打不完'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1753720314, 'MsgSource': '<msgsource>\n\t<bizflag>0</bizflag>\n\t<pua>1</pua>\n\t<eggIncluded>1</eggIncluded>\n\t<silence>1</silence>\n\t<membercount>144</membercount>\n\t<signature>N0_V1_WveC5bdU|v1_es6wSu2r</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'NewMsgId': 6032375762092974045, 'MsgSeq': 871407336}
2025-07-29 00:31:48 | INFO | 收到文本消息: 消息ID:1427027594 来自:***********@chatroom 发送人:wxid_1ul5r40nibpn12 @:[] 内容:没事 到时候打不完
2025-07-29 00:31:48 | DEBUG | 处理消息内容: '没事 到时候打不完'
2025-07-29 00:31:48 | DEBUG | 消息内容 '没事 到时候打不完' 不匹配任何命令，忽略
2025-07-29 00:31:50 | DEBUG | 收到消息: {'MsgId': 2104770812, 'FromUserName': {'string': '***********@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 1, 'Content': {'string': 'wxid_1ul5r40nibpn12:\n不行也得行'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1753720316, 'MsgSource': '<msgsource>\n\t<bizflag>0</bizflag>\n\t<pua>1</pua>\n\t<eggIncluded>1</eggIncluded>\n\t<silence>1</silence>\n\t<membercount>144</membercount>\n\t<signature>N0_V1_Awqq+iO6|v1_WJY6Bs8D</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'NewMsgId': 7054832350612719305, 'MsgSeq': 871407337}
2025-07-29 00:31:50 | INFO | 收到文本消息: 消息ID:2104770812 来自:***********@chatroom 发送人:wxid_1ul5r40nibpn12 @:[] 内容:不行也得行
2025-07-29 00:31:50 | DEBUG | 处理消息内容: '不行也得行'
2025-07-29 00:31:50 | DEBUG | 消息内容 '不行也得行' 不匹配任何命令，忽略
2025-07-29 00:31:58 | DEBUG | 收到消息: {'MsgId': 1328639817, 'FromUserName': {'string': '***********@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 47, 'Content': {'string': 'wxid_1ul5r40nibpn12:\n<msg><emoji fromusername="wxid_1ul5r40nibpn12" tousername="***********@chatroom" type="2" idbuffer="media:0_0" md5="4eb4d66ce0da84bef3d12155eac1cfe1" len="411692" productid="" androidmd5="4eb4d66ce0da84bef3d12155eac1cfe1" androidlen="411692" s60v3md5="4eb4d66ce0da84bef3d12155eac1cfe1" s60v3len="411692" s60v5md5="4eb4d66ce0da84bef3d12155eac1cfe1" s60v5len="411692" cdnurl="http://vweixinf.tc.qq.com/110/20401/stodownload?m=4eb4d66ce0da84bef3d12155eac1cfe1&amp;filekey=30440201010430302e02016e0402535a04203465623464363663653064613834626566336431323135356561633163666531020306482c040d00000004627466730000000132&amp;hy=SZ&amp;storeid=26857da9f0006f2b443c0f4ed0000006e01004fb1535a072f1011568d0b7da&amp;ef=1&amp;bizid=1022" designerid="" thumburl="" encrypturl="http://vweixinf.tc.qq.com/110/20402/stodownload?m=0a016a78045df66855029afe7b50284d&amp;filekey=30440201010430302e02016e0402535a042030613031366137383034356466363638353530323961666537623530323834640203064830040d00000004627466730000000132&amp;hy=SZ&amp;storeid=26857da9f00080f6e43c0f4ed0000006e02004fb2535a072f1011568d0b7f6&amp;ef=2&amp;bizid=1022" aeskey="fd6001fbb08749af99c810d29c7f5b20" externurl="http://vweixinf.tc.qq.com/110/20403/stodownload?m=326d708ae92e24bf6567e92d37b5f025&amp;filekey=30440201010430302e02016e0402535a042033323664373038616539326532346266363536376539326433376235663032350203010100040d00000004627466730000000132&amp;hy=SZ&amp;storeid=26857da9f0008ddad43c0f4ed0000006e03004fb3535a072f1011568d0b807&amp;ef=3&amp;bizid=1022" externmd5="c2ed532ead7d2e8ab1a0fccb36579ad0" width="360" height="360" tpurl="" tpauthkey="" attachedtext="" attachedtextcolor="" lensid="" emojiattr="" linkid="" desc=""></emoji><gameext type="0" content="0"></gameext></msg>'}, 'Status': 3, 'ImgStatus': 2, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1753720324, 'MsgSource': '<msgsource>\n\t<silence>1</silence>\n\t<membercount>144</membercount>\n\t<signature>N0_V1_hYUrEOnH|v1_9zdY8B8U</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'NewMsgId': 7441106769030381999, 'MsgSeq': 871407338}
2025-07-29 00:31:58 | INFO | 收到表情消息: 消息ID:1328639817 来自:***********@chatroom 发送人:wxid_1ul5r40nibpn12 MD5:4eb4d66ce0da84bef3d12155eac1cfe1 大小:411692
2025-07-29 00:31:58 | DEBUG | [DoubaoImageRecognition] 缓存表情消息: 7441106769030381999
2025-07-29 00:31:58 | DEBUG | 收到消息: {'MsgId': 1169875770, 'FromUserName': {'string': '***********@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 1, 'Content': {'string': 'tianen532965049:\n太可怕了'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1753720325, 'MsgSource': '<msgsource>\n\t<bizflag>0</bizflag>\n\t<pua>1</pua>\n\t<eggIncluded>1</eggIncluded>\n\t<silence>1</silence>\n\t<membercount>144</membercount>\n\t<signature>N0_V1_ZJpmSqXf|v1_70Inq9DE</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'NewMsgId': 5553536887334828421, 'MsgSeq': 871407339}
2025-07-29 00:31:58 | INFO | 收到文本消息: 消息ID:1169875770 来自:***********@chatroom 发送人:tianen532965049 @:[] 内容:太可怕了
2025-07-29 00:31:58 | DEBUG | 处理消息内容: '太可怕了'
2025-07-29 00:31:58 | DEBUG | 消息内容 '太可怕了' 不匹配任何命令，忽略
2025-07-29 00:32:13 | DEBUG | 收到消息: {'MsgId': 421924275, 'FromUserName': {'string': '***********@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 1, 'Content': {'string': 'wxid_xv01lkcmn48l22:\n给夕打'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1753720340, 'MsgSource': '<msgsource>\n\t<bizflag>0</bizflag>\n\t<pua>1</pua>\n\t<eggIncluded>1</eggIncluded>\n\t<silence>1</silence>\n\t<membercount>144</membercount>\n\t<signature>N0_V1_1D9yvCBY|v1_9X6pAuqK</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'NewMsgId': 8222911879711711651, 'MsgSeq': 871407340}
2025-07-29 00:32:13 | INFO | 收到文本消息: 消息ID:421924275 来自:***********@chatroom 发送人:wxid_xv01lkcmn48l22 @:[] 内容:给夕打
2025-07-29 00:32:13 | DEBUG | 处理消息内容: '给夕打'
2025-07-29 00:32:13 | DEBUG | 消息内容 '给夕打' 不匹配任何命令，忽略
2025-07-29 00:32:16 | DEBUG | 收到消息: {'MsgId': 444461585, 'FromUserName': {'string': '***********@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 47, 'Content': {'string': 'tianen532965049:\n<msg><emoji fromusername = "tianen532965049" tousername = "***********@chatroom" type="3" idbuffer="media:0_0" md5="e1cc6165a5b75af27781afa5e6e35f91" len = "5685" productid="" androidmd5="e1cc6165a5b75af27781afa5e6e35f91" androidlen="5685" s60v3md5 = "e1cc6165a5b75af27781afa5e6e35f91" s60v3len="5685" s60v5md5 = "e1cc6165a5b75af27781afa5e6e35f91" s60v5len="5685" cdnurl = "http://vweixinf.tc.qq.com/110/20401/stodownload?m=e1cc6165a5b75af27781afa5e6e35f91&amp;filekey=3043020101042f302d02016e0402535a0420653163633631363561356237356166323737383161666135653665333566393102021635040d00000004627466730000000132&amp;hy=SZ&amp;storeid=26399bc2c0009f9a0cd17bb3c0000006e01004fb1535a0129e910b6912bff1&amp;ef=1&amp;bizid=1022" designerid = "" thumburl = "" encrypturl = "http://vweixinf.tc.qq.com/110/20402/stodownload?m=b15131cf8e3aed02973ad5fbea9c1efd&amp;filekey=3043020101042f302d02016e0402535a0420623135313331636638653361656430323937336164356662656139633165666402021640040d00000004627466730000000132&amp;hy=SZ&amp;storeid=26399bc2c000a53eecd17bb3c0000006e02004fb2535a0129e910b6912c00b&amp;ef=2&amp;bizid=1022" aeskey= "fe4cf1eb56ce42808869ba8beebf010e" externurl = "http://vweixinf.tc.qq.com/110/20403/stodownload?m=e2a2eb87e7074e0979600b0b3583f71d&amp;filekey=3043020101042f302d02016e0402535a0420653261326562383765373037346530393739363030623062333538336637316402020bc0040d00000004627466730000000132&amp;hy=SZ&amp;storeid=26399bc2c000ab9eacd17bb3c0000006e03004fb3535a0129e910b6912c02e&amp;ef=3&amp;bizid=1022" externmd5 = "e486227233ea3edf69464b520fd19f88" width= "236" height= "240" tpurl= "" tpauthkey= "" attachedtext= "" attachedtextcolor= "" lensid= "" emojiattr= "" linkid= "" desc= "" ></emoji> <gameext type="0" content="0" ></gameext> </msg>'}, 'Status': 3, 'ImgStatus': 2, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1753720343, 'MsgSource': '<msgsource>\n\t<silence>1</silence>\n\t<membercount>144</membercount>\n\t<signature>N0_V1_gJEnUEFJ|v1_Fzl7Uf2j</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'NewMsgId': 4078508472504216430, 'MsgSeq': 871407341}
2025-07-29 00:32:16 | INFO | 收到表情消息: 消息ID:444461585 来自:***********@chatroom 发送人:tianen532965049 MD5:e1cc6165a5b75af27781afa5e6e35f91 大小:5685
2025-07-29 00:32:16 | DEBUG | [DoubaoImageRecognition] 缓存表情消息: 4078508472504216430
2025-07-29 00:32:20 | DEBUG | 收到消息: {'MsgId': 635367583, 'FromUserName': {'string': '***********@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 1, 'Content': {'string': 'wxid_xv01lkcmn48l22:\n然后就能带我'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1753720347, 'MsgSource': '<msgsource>\n\t<bizflag>0</bizflag>\n\t<pua>1</pua>\n\t<eggIncluded>1</eggIncluded>\n\t<silence>1</silence>\n\t<membercount>144</membercount>\n\t<signature>N0_V1_fSN1Or4G|v1_OP8WQWVe</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'NewMsgId': 1463781228334987617, 'MsgSeq': 871407342}
2025-07-29 00:32:20 | INFO | 收到文本消息: 消息ID:635367583 来自:***********@chatroom 发送人:wxid_xv01lkcmn48l22 @:[] 内容:然后就能带我
2025-07-29 00:32:20 | DEBUG | 处理消息内容: '然后就能带我'
2025-07-29 00:32:20 | DEBUG | 消息内容 '然后就能带我' 不匹配任何命令，忽略
2025-07-29 00:32:23 | DEBUG | 收到消息: {'MsgId': 1959287840, 'FromUserName': {'string': '***********@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 1, 'Content': {'string': 'wxid_snv13qf05qjx11:\n我上班去了'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1753720349, 'MsgSource': '<msgsource>\n\t<bizflag>0</bizflag>\n\t<pua>1</pua>\n\t<eggIncluded>1</eggIncluded>\n\t<silence>1</silence>\n\t<membercount>144</membercount>\n\t<signature>N0_V1_rViHFFgR|v1_P2GcznJ7</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'NewMsgId': 2651061017912205635, 'MsgSeq': 871407343}
2025-07-29 00:32:23 | INFO | 收到文本消息: 消息ID:1959287840 来自:***********@chatroom 发送人:wxid_snv13qf05qjx11 @:[] 内容:我上班去了
2025-07-29 00:32:23 | DEBUG | 处理消息内容: '我上班去了'
2025-07-29 00:32:23 | DEBUG | 消息内容 '我上班去了' 不匹配任何命令，忽略
2025-07-29 00:32:26 | DEBUG | 收到消息: {'MsgId': 188628516, 'FromUserName': {'string': '***********@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 47, 'Content': {'string': 'wxid_xv01lkcmn48l22:\n<msg><emoji fromusername = "wxid_xv01lkcmn48l22" tousername = "***********@chatroom" type="2" idbuffer="media:0_0" md5="d333dd0f22bab637a48d7a60b7358de9" len = "21459" productid="" androidmd5="d333dd0f22bab637a48d7a60b7358de9" androidlen="21459" s60v3md5 = "d333dd0f22bab637a48d7a60b7358de9" s60v3len="21459" s60v5md5 = "d333dd0f22bab637a48d7a60b7358de9" s60v5len="21459" cdnurl = "http://vweixinf.tc.qq.com/110/20401/stodownload?m=d333dd0f22bab637a48d7a60b7358de9&amp;filekey=3043020101042f302d02016e0402534804206433333364643066323262616236333761343864376136306237333538646539020253d3040d00000004627466730000000132&amp;hy=SH&amp;storeid=2677ab6f80008c99e09ee013a0000006e01004fb153480933f1f156910aa23&amp;ef=1&amp;bizid=1022" designerid = "" thumburl = "" encrypturl = "http://vweixinf.tc.qq.com/110/20402/stodownload?m=936b11eda524ecc2252bb3cccf8cdb5e&amp;filekey=3043020101042f302d02016e0402534804203933366231316564613532346563633232353262623363636366386364623565020253e0040d00000004627466730000000132&amp;hy=SH&amp;storeid=2677ab6f80009adf909ee013a0000006e02004fb253480933f1f156910aa2a&amp;ef=2&amp;bizid=1022" aeskey= "a8361101c49b4b70be5bdfeb0b8b208a" externurl = "http://vweixinf.tc.qq.com/110/20403/stodownload?m=c184630344d09eadf21c0b63962d9dbc&amp;filekey=3043020101042f302d02016e0402534804206331383436333033343464303965616466323163306236333936326439646263020240e0040d00000004627466730000000132&amp;hy=SH&amp;storeid=2677ab6f8000a73a509ee013a0000006e03004fb353480933f1f156910aa37&amp;ef=3&amp;bizid=1022" externmd5 = "a700c80ae0ae91b785b23bffd647e6c1" width= "240" height= "240" tpurl= "" tpauthkey= "" attachedtext= "" attachedtextcolor= "" lensid= "" emojiattr= "" linkid= "" desc= "" ></emoji> <gameext type="0" content="0" ></gameext> </msg>'}, 'Status': 3, 'ImgStatus': 2, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1753720350, 'MsgSource': '<msgsource>\n\t<silence>1</silence>\n\t<membercount>144</membercount>\n\t<signature>N0_V1_lE85uSdo|v1_N/xpF/U9</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'NewMsgId': 6029445608054309193, 'MsgSeq': 871407344}
2025-07-29 00:32:26 | INFO | 收到表情消息: 消息ID:188628516 来自:***********@chatroom 发送人:wxid_xv01lkcmn48l22 MD5:d333dd0f22bab637a48d7a60b7358de9 大小:21459
2025-07-29 00:32:26 | DEBUG | [DoubaoImageRecognition] 缓存表情消息: 6029445608054309193
2025-07-29 00:32:26 | DEBUG | 收到消息: {'MsgId': 1237268408, 'FromUserName': {'string': '***********@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 1, 'Content': {'string': 'wxid_ikxxrwasicud11:\n再不打排位要退游了'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1753720352, 'MsgSource': '<msgsource>\n\t<atuserlist><![CDATA[]]></atuserlist>\n\t<silence>1</silence>\n\t<membercount>144</membercount>\n\t<signature>N0_V1_S9rG7bFo|v1_dGnVMnY3</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'NewMsgId': 5567212945754134287, 'MsgSeq': 871407345}
2025-07-29 00:32:26 | INFO | 收到文本消息: 消息ID:1237268408 来自:***********@chatroom 发送人:wxid_ikxxrwasicud11 @:[] 内容:再不打排位要退游了
2025-07-29 00:32:26 | DEBUG | 处理消息内容: '再不打排位要退游了'
2025-07-29 00:32:26 | DEBUG | 消息内容 '再不打排位要退游了' 不匹配任何命令，忽略
2025-07-29 00:32:29 | DEBUG | 收到消息: {'MsgId': 1845983845, 'FromUserName': {'string': '***********@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 47, 'Content': {'string': 'wxid_xv01lkcmn48l22:\n<msg><emoji fromusername = "wxid_xv01lkcmn48l22" tousername = "***********@chatroom" type="2" idbuffer="media:0_0" md5="c1df77026271070dd8062ef09f2bb554" len = "127369" productid="" androidmd5="c1df77026271070dd8062ef09f2bb554" androidlen="127369" s60v3md5 = "c1df77026271070dd8062ef09f2bb554" s60v3len="127369" s60v5md5 = "c1df77026271070dd8062ef09f2bb554" s60v5len="127369" cdnurl = "http://wxapp.tc.qq.com/262/20304/stodownload?m=c1df77026271070dd8062ef09f2bb554&amp;filekey=30350201010421301f020201060402535a0410c1df77026271070dd8062ef09f2bb554020301f189040d00000004627466730000000132&amp;hy=SZ&amp;storeid=267fc88dd0009b6ac0bbb844a0000010600004f50535a00bd915156bb96a41&amp;bizid=1023" designerid = "" thumburl = "" encrypturl = "http://wxapp.tc.qq.com/262/20304/stodownload?m=408ae3dc443fc622c643feafdd4c60de&amp;filekey=30350201010421301f020201060402535a0410408ae3dc443fc622c643feafdd4c60de020301f190040d00000004627466730000000132&amp;hy=SZ&amp;storeid=267fc88dd000b221c0bbb844a0000010600004f50535a0ea4888096d1544cb&amp;bizid=1023" aeskey= "8cd5b72b0b9832d3a1fe0fd1ebed74b0" externurl = "http://wxapp.tc.qq.com/262/20304/stodownload?m=321fe070eaf988a1865b3ad252c441d4&amp;filekey=30350201010421301f020201060402535a0410321fe070eaf988a1865b3ad252c441d4020300fca0040d00000004627466730000000132&amp;hy=SZ&amp;storeid=267fc8a890006bcb04a7d8c540000010600004f50535a065fa01156c583900&amp;bizid=1023" externmd5 = "2f1d4515669d8cac0eff0008f432d4b2" width= "480" height= "480" tpurl= "" tpauthkey= "" attachedtext= "" attachedtextcolor= "" lensid= "" emojiattr= "" linkid= "" desc= "" ></emoji> <gameext type="0" content="0" ></gameext> </msg>'}, 'Status': 3, 'ImgStatus': 2, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1753720355, 'MsgSource': '<msgsource>\n\t<silence>1</silence>\n\t<membercount>144</membercount>\n\t<signature>N0_V1_IsZlY7kw|v1_swq1wxDp</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'NewMsgId': 1189691313551495041, 'MsgSeq': 871407346}
2025-07-29 00:32:29 | INFO | 收到表情消息: 消息ID:1845983845 来自:***********@chatroom 发送人:wxid_xv01lkcmn48l22 MD5:c1df77026271070dd8062ef09f2bb554 大小:127369
2025-07-29 00:32:29 | DEBUG | [DoubaoImageRecognition] 缓存表情消息: 1189691313551495041
2025-07-29 00:32:29 | DEBUG | 收到消息: {'MsgId': 608038, 'FromUserName': {'string': '***********@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 1, 'Content': {'string': 'wxid_ikxxrwasicud11:\n让她自己打'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1753720356, 'MsgSource': '<msgsource>\n\t<atuserlist><![CDATA[]]></atuserlist>\n\t<silence>1</silence>\n\t<membercount>144</membercount>\n\t<signature>N0_V1_m7Ayd2GG|v1_SnpYYaJy</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'NewMsgId': 4053396503051352757, 'MsgSeq': 871407347}
2025-07-29 00:32:29 | INFO | 收到文本消息: 消息ID:608038 来自:***********@chatroom 发送人:wxid_ikxxrwasicud11 @:[] 内容:让她自己打
2025-07-29 00:32:29 | DEBUG | 处理消息内容: '让她自己打'
2025-07-29 00:32:29 | DEBUG | 消息内容 '让她自己打' 不匹配任何命令，忽略
2025-07-29 00:32:40 | DEBUG | 收到消息: {'MsgId': 1513075504, 'FromUserName': {'string': '***********@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 1, 'Content': {'string': 'wxid_1ul5r40nibpn12:\n你上个屁 你八百年没上班了'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1753720367, 'MsgSource': '<msgsource>\n\t<bizflag>0</bizflag>\n\t<pua>1</pua>\n\t<eggIncluded>1</eggIncluded>\n\t<silence>1</silence>\n\t<membercount>144</membercount>\n\t<signature>N0_V1_qkSov+Wt|v1_+mp15A+u</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'NewMsgId': 8627777125615691738, 'MsgSeq': 871407348}
2025-07-29 00:32:40 | INFO | 收到文本消息: 消息ID:1513075504 来自:***********@chatroom 发送人:wxid_1ul5r40nibpn12 @:[] 内容:你上个屁 你八百年没上班了
2025-07-29 00:32:40 | DEBUG | 处理消息内容: '你上个屁 你八百年没上班了'
2025-07-29 00:32:40 | DEBUG | 消息内容 '你上个屁 你八百年没上班了' 不匹配任何命令，忽略
2025-07-29 00:32:43 | DEBUG | 收到消息: {'MsgId': 1153596525, 'FromUserName': {'string': '***********@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 1, 'Content': {'string': 'tianen532965049:\n那就杜总自己打'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1753720370, 'MsgSource': '<msgsource>\n\t<bizflag>0</bizflag>\n\t<pua>1</pua>\n\t<eggIncluded>1</eggIncluded>\n\t<silence>1</silence>\n\t<membercount>144</membercount>\n\t<signature>N0_V1_wFvHg1Uv|v1_NQCpRSg1</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'NewMsgId': 7803079925874866568, 'MsgSeq': 871407349}
2025-07-29 00:32:43 | INFO | 收到文本消息: 消息ID:1153596525 来自:***********@chatroom 发送人:tianen532965049 @:[] 内容:那就杜总自己打
2025-07-29 00:32:43 | DEBUG | 处理消息内容: '那就杜总自己打'
2025-07-29 00:32:43 | DEBUG | 消息内容 '那就杜总自己打' 不匹配任何命令，忽略
2025-07-29 00:32:46 | DEBUG | 收到消息: {'MsgId': 1278810116, 'FromUserName': {'string': '***********@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 47, 'Content': {'string': 'wxid_1ul5r40nibpn12:\n<msg><emoji fromusername="wxid_1ul5r40nibpn12" tousername="***********@chatroom" type="3" idbuffer="media:0_0" md5="5731933f599c03fb923b3f177bca6476" len="7045" productid="" androidmd5="5731933f599c03fb923b3f177bca6476" androidlen="7045" s60v3md5="5731933f599c03fb923b3f177bca6476" s60v3len="7045" s60v5md5="5731933f599c03fb923b3f177bca6476" s60v5len="7045" cdnurl="http://vweixinf.tc.qq.com/110/20401/stodownload?m=5731933f599c03fb923b3f177bca6476&amp;filekey=3043020101042f302d02016e040253480420353733313933336635393963303366623932336233663137376263613634373602021b85040d00000004627466730000000132&amp;hy=SH&amp;storeid=265f4083b00095dc27a85db7a0000006e01004fb153482667c0d15732b7b0e&amp;ef=1&amp;bizid=1022" designerid="" thumburl="" encrypturl="http://vweixinf.tc.qq.com/110/20402/stodownload?m=9cd8ab31c321326ce8d5b04220c9ceeb&amp;filekey=3043020101042f302d02016e040253480420396364386162333163333231333236636538643562303432323063396365656202021b90040d00000004627466730000000132&amp;hy=SH&amp;storeid=265f4083b0009ec8f7a85db7a0000006e02004fb253482667c0d15732b7b1f&amp;ef=2&amp;bizid=1022" aeskey="7ff442ba2bef4a8198d661ab9f60e2ad" externurl="http://vweixinf.tc.qq.com/110/20403/stodownload?m=95b9b8e0cd9b7fc7f5b21a5c7c710523&amp;filekey=3043020101042f302d02016e040253480420393562396238653063643962376663376635623231613563376337313035323302020e00040d00000004627466730000000132&amp;hy=SH&amp;storeid=265f4083b000ad8137a85db7a0000006e03004fb353482667c0d15732b7b3e&amp;ef=3&amp;bizid=1022" externmd5="754fa8c43d173e2521d724b8be7255af" width="300" height="252" tpurl="" tpauthkey="" attachedtext="" attachedtextcolor="" lensid="" emojiattr="" linkid="" desc=""></emoji><gameext type="0" content="0"></gameext></msg>'}, 'Status': 3, 'ImgStatus': 2, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1753720371, 'MsgSource': '<msgsource>\n\t<silence>1</silence>\n\t<membercount>144</membercount>\n\t<signature>N0_V1_/snF/gbm|v1_KkBzfIxK</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'NewMsgId': 4450029382624979853, 'MsgSeq': 871407350}
2025-07-29 00:32:46 | INFO | 收到表情消息: 消息ID:1278810116 来自:***********@chatroom 发送人:wxid_1ul5r40nibpn12 MD5:5731933f599c03fb923b3f177bca6476 大小:7045
2025-07-29 00:32:46 | DEBUG | [DoubaoImageRecognition] 缓存表情消息: 4450029382624979853
2025-07-29 00:32:50 | DEBUG | 收到消息: {'MsgId': 585122384, 'FromUserName': {'string': '***********@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 47, 'Content': {'string': 'tianen532965049:\n<msg><emoji fromusername = "tianen532965049" tousername = "***********@chatroom" type="2" idbuffer="media:0_0" md5="d0757b4d1331dfe37a5a02c63230ca5c" len = "318811" productid="" androidmd5="d0757b4d1331dfe37a5a02c63230ca5c" androidlen="318811" s60v3md5 = "d0757b4d1331dfe37a5a02c63230ca5c" s60v3len="318811" s60v5md5 = "d0757b4d1331dfe37a5a02c63230ca5c" s60v5len="318811" cdnurl = "http://vweixinf.tc.qq.com/110/20401/stodownload?m=d0757b4d1331dfe37a5a02c63230ca5c&amp;filekey=30440201010430302e02016e0402535a04206430373537623464313333316466653337613561303263363332333063613563020304dd5b040d00000004627466730000000132&amp;hy=SZ&amp;storeid=2680f48790008c1cfd9a3469e0000006e01004fb1535a1cca70b1568397ec2&amp;ef=1&amp;bizid=1022" designerid = "" thumburl = "" encrypturl = "http://vweixinf.tc.qq.com/110/20402/stodownload?m=bfc5fc86857e90136af22840d8db4f29&amp;filekey=30440201010430302e02016e0402535a04206266633566633836383537653930313336616632323834306438646234663239020304dd60040d00000004627466730000000132&amp;hy=SZ&amp;storeid=2680f4879000a91cdd9a3469e0000006e02004fb2535a1cca70b1568397ecd&amp;ef=2&amp;bizid=1022" aeskey= "1a96d33f44e349f18a54707c78f2b993" externurl = "http://vweixinf.tc.qq.com/110/20403/stodownload?m=af52f1b5ceabe43ae78aa5243e353d11&amp;filekey=30440201010430302e02016e0402535a04206166353266316235636561626534336165373861613532343365333533643131020300a480040d00000004627466730000000132&amp;hy=SZ&amp;storeid=2680f4879000bcdf9d9a3469e0000006e03004fb3535a1cca70b1568397ed4&amp;ef=3&amp;bizid=1022" externmd5 = "cb13031bbeb811bd5b9153970d74e212" width= "240" height= "240" tpurl= "" tpauthkey= "" attachedtext= "" attachedtextcolor= "" lensid= "" emojiattr= "" linkid= "" desc= "" ></emoji> <gameext type="0" content="0" ></gameext> </msg>'}, 'Status': 3, 'ImgStatus': 2, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1753720377, 'MsgSource': '<msgsource>\n\t<silence>1</silence>\n\t<membercount>144</membercount>\n\t<signature>N0_V1_wAwQfhsc|v1_WkMzJsVa</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'NewMsgId': 5342123824883556956, 'MsgSeq': 871407351}
2025-07-29 00:32:50 | INFO | 收到表情消息: 消息ID:585122384 来自:***********@chatroom 发送人:tianen532965049 MD5:d0757b4d1331dfe37a5a02c63230ca5c 大小:318811
2025-07-29 00:32:50 | DEBUG | [DoubaoImageRecognition] 缓存表情消息: 5342123824883556956
2025-07-29 00:32:52 | DEBUG | 收到消息: {'MsgId': 290323150, 'FromUserName': {'string': '***********@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 1, 'Content': {'string': 'wxid_snv13qf05qjx11:\n刚找的'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1753720380, 'MsgSource': '<msgsource>\n\t<bizflag>0</bizflag>\n\t<pua>1</pua>\n\t<eggIncluded>1</eggIncluded>\n\t<silence>1</silence>\n\t<membercount>144</membercount>\n\t<signature>N0_V1_FqLO/1QP|v1_iOqMUGMm</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'NewMsgId': 3947799878391385783, 'MsgSeq': 871407352}
2025-07-29 00:32:52 | INFO | 收到文本消息: 消息ID:290323150 来自:***********@chatroom 发送人:wxid_snv13qf05qjx11 @:[] 内容:刚找的
2025-07-29 00:32:52 | DEBUG | 处理消息内容: '刚找的'
2025-07-29 00:32:52 | DEBUG | 消息内容 '刚找的' 不匹配任何命令，忽略
2025-07-29 00:33:04 | DEBUG | 收到消息: {'MsgId': 1838951885, 'FromUserName': {'string': '***********@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 1, 'Content': {'string': 'wxid_1ul5r40nibpn12:\nlc说谁呢'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1753720391, 'MsgSource': '<msgsource>\n\t<bizflag>0</bizflag>\n\t<pua>1</pua>\n\t<eggIncluded>1</eggIncluded>\n\t<silence>1</silence>\n\t<membercount>144</membercount>\n\t<signature>N0_V1_mcSihwJC|v1_x4sV2xTR</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'NewMsgId': 8957924750011449717, 'MsgSeq': 871407353}
2025-07-29 00:33:04 | INFO | 收到文本消息: 消息ID:1838951885 来自:***********@chatroom 发送人:wxid_1ul5r40nibpn12 @:[] 内容:lc说谁呢
2025-07-29 00:33:04 | DEBUG | 处理消息内容: 'lc说谁呢'
2025-07-29 00:33:04 | DEBUG | 消息内容 'lc说谁呢' 不匹配任何命令，忽略
2025-07-29 00:33:06 | DEBUG | 收到消息: {'MsgId': 2001925311, 'FromUserName': {'string': '***********@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 1, 'Content': {'string': 'wxid_snv13qf05qjx11:\n突然就有了'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1753720391, 'MsgSource': '<msgsource>\n\t<bizflag>0</bizflag>\n\t<pua>1</pua>\n\t<eggIncluded>1</eggIncluded>\n\t<silence>1</silence>\n\t<membercount>144</membercount>\n\t<signature>N0_V1_fUbeTs+d|v1_cnKt81gL</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'NewMsgId': 8034951710679600798, 'MsgSeq': 871407354}
2025-07-29 00:33:06 | INFO | 收到文本消息: 消息ID:2001925311 来自:***********@chatroom 发送人:wxid_snv13qf05qjx11 @:[] 内容:突然就有了
2025-07-29 00:33:06 | DEBUG | 处理消息内容: '突然就有了'
2025-07-29 00:33:06 | DEBUG | 消息内容 '突然就有了' 不匹配任何命令，忽略
2025-07-29 00:33:21 | DEBUG | 收到消息: {'MsgId': 2079667151, 'FromUserName': {'string': '***********@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 1, 'Content': {'string': 'wxid_ikxxrwasicud11:\n说1号上班的[让我看看]'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1753720408, 'MsgSource': '<msgsource>\n\t<atuserlist><![CDATA[]]></atuserlist>\n\t<silence>1</silence>\n\t<membercount>144</membercount>\n\t<signature>N0_V1_DNrFE6eC|v1_XVVC73N5</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'NewMsgId': 4985996392953511172, 'MsgSeq': 871407355}
2025-07-29 00:33:21 | INFO | 收到文本消息: 消息ID:2079667151 来自:***********@chatroom 发送人:wxid_ikxxrwasicud11 @:[] 内容:说1号上班的[让我看看]
2025-07-29 00:33:21 | DEBUG | 处理消息内容: '说1号上班的[让我看看]'
2025-07-29 00:33:21 | DEBUG | 消息内容 '说1号上班的[让我看看]' 不匹配任何命令，忽略
2025-07-29 00:33:26 | DEBUG | 收到消息: {'MsgId': 882741617, 'FromUserName': {'string': '***********@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 1, 'Content': {'string': 'wxid_snv13qf05qjx11:\n说我呢'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1753720413, 'MsgSource': '<msgsource>\n\t<bizflag>0</bizflag>\n\t<pua>1</pua>\n\t<eggIncluded>1</eggIncluded>\n\t<silence>1</silence>\n\t<membercount>144</membercount>\n\t<signature>N0_V1_I07xlep0|v1_iND2AuOP</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'NewMsgId': 5186747478366242389, 'MsgSeq': 871407356}
2025-07-29 00:33:26 | INFO | 收到文本消息: 消息ID:882741617 来自:***********@chatroom 发送人:wxid_snv13qf05qjx11 @:[] 内容:说我呢
2025-07-29 00:33:26 | DEBUG | 处理消息内容: '说我呢'
2025-07-29 00:33:26 | DEBUG | 消息内容 '说我呢' 不匹配任何命令，忽略
2025-07-29 00:33:40 | DEBUG | 收到消息: {'MsgId': 1164070013, 'FromUserName': {'string': '***********@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 1, 'Content': {'string': 'wxid_snv13qf05qjx11:\n我打就我打'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1753720427, 'MsgSource': '<msgsource>\n\t<bizflag>0</bizflag>\n\t<pua>1</pua>\n\t<eggIncluded>1</eggIncluded>\n\t<silence>1</silence>\n\t<membercount>144</membercount>\n\t<signature>N0_V1_9yClLK6v|v1_FGiuRy7L</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'NewMsgId': 4247515140628211167, 'MsgSeq': 871407357}
2025-07-29 00:33:40 | INFO | 收到文本消息: 消息ID:1164070013 来自:***********@chatroom 发送人:wxid_snv13qf05qjx11 @:[] 内容:我打就我打
2025-07-29 00:33:40 | DEBUG | 处理消息内容: '我打就我打'
2025-07-29 00:33:40 | DEBUG | 消息内容 '我打就我打' 不匹配任何命令，忽略
2025-07-29 00:33:55 | DEBUG | 收到消息: {'MsgId': 237784914, 'FromUserName': {'string': '***********@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 1, 'Content': {'string': 'wxid_snv13qf05qjx11:\n你的你自己打'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1753720442, 'MsgSource': '<msgsource>\n\t<bizflag>0</bizflag>\n\t<pua>1</pua>\n\t<eggIncluded>1</eggIncluded>\n\t<silence>1</silence>\n\t<membercount>144</membercount>\n\t<signature>N0_V1_1Ab2g0Qi|v1_yJC2Hp67</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'NewMsgId': 5615998178666440275, 'MsgSeq': 871407358}
2025-07-29 00:33:55 | INFO | 收到文本消息: 消息ID:237784914 来自:***********@chatroom 发送人:wxid_snv13qf05qjx11 @:[] 内容:你的你自己打
2025-07-29 00:33:55 | DEBUG | 处理消息内容: '你的你自己打'
2025-07-29 00:33:55 | DEBUG | 消息内容 '你的你自己打' 不匹配任何命令，忽略
2025-07-29 00:33:58 | DEBUG | 收到消息: {'MsgId': 1309465237, 'FromUserName': {'string': '***********@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 1, 'Content': {'string': 'wxid_1ul5r40nibpn12:\n哈哈哈哈哈'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1753720442, 'MsgSource': '<msgsource>\n\t<bizflag>0</bizflag>\n\t<pua>1</pua>\n\t<eggIncluded>1</eggIncluded>\n\t<silence>1</silence>\n\t<membercount>144</membercount>\n\t<signature>N0_V1_9zMcuApf|v1_cu/sut7/</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'NewMsgId': 3575459462184421864, 'MsgSeq': 871407359}
2025-07-29 00:33:58 | INFO | 收到文本消息: 消息ID:1309465237 来自:***********@chatroom 发送人:wxid_1ul5r40nibpn12 @:[] 内容:哈哈哈哈哈
2025-07-29 00:33:58 | DEBUG | 处理消息内容: '哈哈哈哈哈'
2025-07-29 00:33:58 | DEBUG | 消息内容 '哈哈哈哈哈' 不匹配任何命令，忽略
2025-07-29 00:34:05 | DEBUG | 收到消息: {'MsgId': 250933, 'FromUserName': {'string': '***********@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 10002, 'Content': {'string': '***********@chatroom:\n<sysmsg type="pat">\n<pat>\n  <fromusername>wxid_snv13qf05qjx11</fromusername>\n  <chatusername>***********@chatroom</chatusername>\n  <pattedusername>wxid_ikxxrwasicud11</pattedusername>\n  <patsuffix><![CDATA[]]></patsuffix>\n  <patsuffixversion>2</patsuffixversion>\n\n\n\n\n  <template><![CDATA["${wxid_snv13qf05qjx11}" 拍了拍 "${wxid_ikxxrwasicud11}"]]></template>\n\n\n\n\n</pat>\n</sysmsg>'}, 'Status': 4, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1753720448, 'MsgSource': '<msgsource>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'NewMsgId': 1636338747588124646, 'MsgSeq': 871407360}
2025-07-29 00:34:05 | DEBUG | 系统消息类型: pat
2025-07-29 00:34:05 | INFO | 收到拍一拍消息: 消息ID:250933 来自:***********@chatroom 发送人:***********@chatroom 拍者:wxid_snv13qf05qjx11 被拍:wxid_ikxxrwasicud11 后缀:None
2025-07-29 00:34:05 | DEBUG | [PatReply] 被拍者 wxid_ikxxrwasicud11 不是目标用户 wxid_4usgcju5ey9q29，跳过
2025-07-29 00:34:06 | DEBUG | 收到消息: {'MsgId': 741027885, 'FromUserName': {'string': '***********@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 47, 'Content': {'string': 'wxid_ikxxrwasicud11:\n<msg><emoji fromusername = "wxid_ikxxrwasicud11" tousername = "***********@chatroom" type="2" idbuffer="media:0_0" md5="67ce524ee00e3d286259e630c63d20d5" len = "875086" productid="" androidmd5="67ce524ee00e3d286259e630c63d20d5" androidlen="875086" s60v3md5 = "67ce524ee00e3d286259e630c63d20d5" s60v3len="875086" s60v5md5 = "67ce524ee00e3d286259e630c63d20d5" s60v5len="875086" cdnurl = "http://vweixinf.tc.qq.com/110/20401/stodownload?m=67ce524ee00e3d286259e630c63d20d5&amp;filekey=30440201010430302e02016e0402535a0420363763653532346565303065336432383632353965363330633633643230643502030d5a4e040d00000004627466730000000132&amp;hy=SZ&amp;storeid=2625d4524000045afde6b3c8e0000006e01004fb1535a1630c950b6b1a7cb5&amp;ef=1&amp;bizid=1022" designerid = "" thumburl = "" encrypturl = "http://vweixinf.tc.qq.com/110/20402/stodownload?m=2c0ad5f0c6159ff45705ff067c3ebf99&amp;filekey=30440201010430302e02016e0402535a0420326330616435663063363135396666343537303566663036376333656266393902030d5a50040d00000004627466730000000132&amp;hy=SZ&amp;storeid=2625d4524000246c7de6b3c8e0000006e02004fb2535a1630c950b6b1a7ce3&amp;ef=2&amp;bizid=1022" aeskey= "41efa13e9ae440819ab3f8f34f88232c" externurl = "http://vweixinf.tc.qq.com/110/20403/stodownload?m=b6af85b353a2296238f5164633ea24a9&amp;filekey=30440201010430302e02016e0402535a042062366166383562333533613232393632333866353136343633336561323461390203008800040d00000004627466730000000132&amp;hy=SZ&amp;storeid=2625d4524000445dfde6b3c8e0000006e03004fb3535a1630c950b6b1a7d15&amp;ef=3&amp;bizid=1022" externmd5 = "ecb61ecc98c7c91dee8d312229e2b5fd" width= "320" height= "320" tpurl= "" tpauthkey= "" attachedtext= "" attachedtextcolor= "" lensid= "" emojiattr= "" linkid= "" desc= "" ></emoji>  </msg>'}, 'Status': 3, 'ImgStatus': 2, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1753720453, 'MsgSource': '<msgsource>\n\t<silence>1</silence>\n\t<membercount>144</membercount>\n\t<signature>N0_V1_2TByhBi1|v1_1GuJvvB/</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'NewMsgId': 1596101139897730075, 'MsgSeq': 871407361}
2025-07-29 00:34:06 | INFO | 收到表情消息: 消息ID:741027885 来自:***********@chatroom 发送人:wxid_ikxxrwasicud11 MD5:67ce524ee00e3d286259e630c63d20d5 大小:875086
2025-07-29 00:34:06 | DEBUG | [DoubaoImageRecognition] 缓存表情消息: 1596101139897730075
2025-07-29 00:34:34 | DEBUG | 收到消息: {'MsgId': 398214529, 'FromUserName': {'string': '***********@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 1, 'Content': {'string': 'wxid_ikxxrwasicud11:\n我还以为把妹的'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1753720481, 'MsgSource': '<msgsource>\n\t<atuserlist><![CDATA[]]></atuserlist>\n\t<silence>1</silence>\n\t<membercount>144</membercount>\n\t<signature>N0_V1_1vHDHzb6|v1_sZnGxwSh</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'NewMsgId': 8568813374392657352, 'MsgSeq': 871407362}
2025-07-29 00:34:34 | INFO | 收到文本消息: 消息ID:398214529 来自:***********@chatroom 发送人:wxid_ikxxrwasicud11 @:[] 内容:我还以为把妹的
2025-07-29 00:34:34 | DEBUG | 处理消息内容: '我还以为把妹的'
2025-07-29 00:34:34 | DEBUG | 消息内容 '我还以为把妹的' 不匹配任何命令，忽略
2025-07-29 00:35:18 | DEBUG | 收到消息: {'MsgId': 2087777891, 'FromUserName': {'string': '***********@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 1, 'Content': {'string': 'wxid_snv13qf05qjx11:\n退游了'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1753720525, 'MsgSource': '<msgsource>\n\t<bizflag>0</bizflag>\n\t<pua>1</pua>\n\t<eggIncluded>1</eggIncluded>\n\t<silence>1</silence>\n\t<membercount>144</membercount>\n\t<signature>N0_V1_wXvPHpaz|v1_IcdnDH7i</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'NewMsgId': 4811853852932559826, 'MsgSeq': 871407363}
2025-07-29 00:35:18 | INFO | 收到文本消息: 消息ID:2087777891 来自:***********@chatroom 发送人:wxid_snv13qf05qjx11 @:[] 内容:退游了
2025-07-29 00:35:18 | DEBUG | 处理消息内容: '退游了'
2025-07-29 00:35:18 | DEBUG | 消息内容 '退游了' 不匹配任何命令，忽略
2025-07-29 00:35:26 | ERROR | 处理消息时发生错误: 
Traceback (most recent call last):
  File "C:\XYBotV2\venv\Lib\site-packages\httpx\_transports\default.py", line 101, in map_httpcore_exceptions
    yield
  File "C:\XYBotV2\venv\Lib\site-packages\httpx\_transports\default.py", line 394, in handle_async_request
    resp = await self._pool.handle_async_request(req)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\XYBotV2\venv\Lib\site-packages\httpcore\_async\connection_pool.py", line 256, in handle_async_request
    raise exc from None
  File "C:\XYBotV2\venv\Lib\site-packages\httpcore\_async\connection_pool.py", line 236, in handle_async_request
    response = await connection.handle_async_request(
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\XYBotV2\venv\Lib\site-packages\httpcore\_async\http_proxy.py", line 316, in handle_async_request
    stream = await stream.start_tls(**kwargs)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\XYBotV2\venv\Lib\site-packages\httpcore\_async\http11.py", line 376, in start_tls
    return await self._stream.start_tls(ssl_context, server_hostname, timeout)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\XYBotV2\venv\Lib\site-packages\httpcore\_backends\anyio.py", line 67, in start_tls
    with map_exceptions(exc_map):
  File "C:\Program Files\Python311\Lib\contextlib.py", line 158, in __exit__
    self.gen.throw(typ, value, traceback)
  File "C:\XYBotV2\venv\Lib\site-packages\httpcore\_exceptions.py", line 14, in map_exceptions
    raise to_exc(exc) from exc
httpcore.ConnectError

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "C:\XYBotV2\bot_core.py", line 72, in handle_message
    await xybot.process_message(msg)
  File "C:\XYBotV2\utils\xybot.py", line 97, in process_message
    await self.process_text_message(message)
  File "C:\XYBotV2\utils\xybot.py", line 274, in process_text_message
    await EventManager.emit("text_message", self.bot, message)
  File "C:\XYBotV2\utils\event_manager.py", line 35, in emit
    result = await handler(*handler_args, **new_kwargs)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\XYBotV2\plugins\TencentLke\main.py", line 50, in handle_text
    await self.TencentLke(bot, message, message["Content"])
  File "C:\XYBotV2\plugins\TencentLke\main.py", line 63, in TencentLke
    async with client.stream("POST", url=url, headers=headers, content=payload, timeout=10) as resp:
  File "C:\Program Files\Python311\Lib\contextlib.py", line 210, in __aenter__
    return await anext(self.gen)
           ^^^^^^^^^^^^^^^^^^^^^
  File "C:\XYBotV2\venv\Lib\site-packages\httpx\_client.py", line 1583, in stream
    response = await self.send(
               ^^^^^^^^^^^^^^^^
  File "C:\XYBotV2\venv\Lib\site-packages\httpx\_client.py", line 1629, in send
    response = await self._send_handling_auth(
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\XYBotV2\venv\Lib\site-packages\httpx\_client.py", line 1657, in _send_handling_auth
    response = await self._send_handling_redirects(
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\XYBotV2\venv\Lib\site-packages\httpx\_client.py", line 1694, in _send_handling_redirects
    response = await self._send_single_request(request)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\XYBotV2\venv\Lib\site-packages\httpx\_client.py", line 1730, in _send_single_request
    response = await transport.handle_async_request(request)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\XYBotV2\venv\Lib\site-packages\httpx\_transports\default.py", line 393, in handle_async_request
    with map_httpcore_exceptions():
  File "C:\Program Files\Python311\Lib\contextlib.py", line 158, in __exit__
    self.gen.throw(typ, value, traceback)
  File "C:\XYBotV2\venv\Lib\site-packages\httpx\_transports\default.py", line 118, in map_httpcore_exceptions
    raise mapped_exc(message) from exc
httpx.ConnectError

2025-07-29 00:35:27 | DEBUG | 收到消息: {'MsgId': 1019810983, 'FromUserName': {'string': '***********@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 1, 'Content': {'string': 'wxid_snv13qf05qjx11:\n再见'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1753720527, 'MsgSource': '<msgsource>\n\t<bizflag>0</bizflag>\n\t<pua>1</pua>\n\t<eggIncluded>1</eggIncluded>\n\t<silence>1</silence>\n\t<membercount>144</membercount>\n\t<signature>N0_V1_p6SwQUdT|v1_Y936DoMg</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'NewMsgId': 6565464938475810024, 'MsgSeq': 871407364}
2025-07-29 00:35:27 | INFO | 收到文本消息: 消息ID:1019810983 来自:***********@chatroom 发送人:wxid_snv13qf05qjx11 @:[] 内容:再见
2025-07-29 00:35:27 | DEBUG | 处理消息内容: '再见'
2025-07-29 00:35:27 | DEBUG | 消息内容 '再见' 不匹配任何命令，忽略
2025-07-29 00:35:31 | DEBUG | 收到消息: {'MsgId': 604659704, 'FromUserName': {'string': '***********@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 1, 'Content': {'string': 'wxid_ikxxrwasicud11:\n恶匪打'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1753720532, 'MsgSource': '<msgsource>\n\t<atuserlist><![CDATA[]]></atuserlist>\n\t<silence>1</silence>\n\t<membercount>144</membercount>\n\t<signature>N0_V1_Rv7Ez1QL|v1_n4pMF2bJ</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'NewMsgId': 4707770948271153794, 'MsgSeq': 871407365}
2025-07-29 00:35:31 | INFO | 收到文本消息: 消息ID:604659704 来自:***********@chatroom 发送人:wxid_ikxxrwasicud11 @:[] 内容:恶匪打
2025-07-29 00:35:31 | DEBUG | 处理消息内容: '恶匪打'
2025-07-29 00:35:31 | DEBUG | 消息内容 '恶匪打' 不匹配任何命令，忽略
2025-07-29 00:35:38 | DEBUG | 收到消息: {'MsgId': 1996637305, 'FromUserName': {'string': '***********@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 47, 'Content': {'string': 'wxid_xv01lkcmn48l22:\n<msg><emoji fromusername = "wxid_xv01lkcmn48l22" tousername = "***********@chatroom" type="3" idbuffer="media:0_0" md5="75116dd68112f4c992133753fcf0e4ff" len = "12976" productid="" androidmd5="75116dd68112f4c992133753fcf0e4ff" androidlen="12976" s60v3md5 = "75116dd68112f4c992133753fcf0e4ff" s60v3len="12976" s60v5md5 = "75116dd68112f4c992133753fcf0e4ff" s60v5len="12976" cdnurl = "http://vweixinf.tc.qq.com/110/20401/stodownload?m=75116dd68112f4c992133753fcf0e4ff&amp;filekey=3043020101042f302d02016e0402534804203735313136646436383131326634633939323133333735336663663065346666020232b0040d00000004627466730000000132&amp;hy=SH&amp;storeid=26490607800043b1a86f9866e0000006e01004fb153482386db40b0ac3af8c&amp;ef=1&amp;bizid=1022" designerid = "" thumburl = "" encrypturl = "http://vweixinf.tc.qq.com/110/20402/stodownload?m=14ac07dc2b85744e1ee4f91af544bd25&amp;filekey=3043020101042f302d02016e0402534804203134616330376463326238353734346531656534663931616635343462643235020232c0040d00000004627466730000000132&amp;hy=SH&amp;storeid=2649060780004db9286f9866e0000006e02004fb253482386db40b0ac3afa0&amp;ef=2&amp;bizid=1022" aeskey= "e63cd94eb7f6468cb0fc2db1ce45d1ff" externurl = "http://vweixinf.tc.qq.com/110/20403/stodownload?m=7cb9c8a5e412ea4736c05b3a17e818bd&amp;filekey=3043020101042f302d02016e040253480420376362396338613565343132656134373336633035623361313765383138626402022090040d00000004627466730000000132&amp;hy=SH&amp;storeid=264906078000544f986f9866e0000006e03004fb353482386db40b0ac3afba&amp;ef=3&amp;bizid=1022" externmd5 = "3e84bdd5c334b728eb47abdb0162ac7b" width= "240" height= "240" tpurl= "" tpauthkey= "" attachedtext= "" attachedtextcolor= "" lensid= "" emojiattr= "" linkid= "" desc= "" ></emoji> <gameext type="0" content="0" ></gameext> </msg>'}, 'Status': 3, 'ImgStatus': 2, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1753720537, 'MsgSource': '<msgsource>\n\t<silence>1</silence>\n\t<membercount>144</membercount>\n\t<signature>N0_V1_ZhahgjYD|v1_i68I32lC</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'NewMsgId': 3928917850037235520, 'MsgSeq': 871407366}
2025-07-29 00:35:38 | INFO | 收到表情消息: 消息ID:1996637305 来自:***********@chatroom 发送人:wxid_xv01lkcmn48l22 MD5:75116dd68112f4c992133753fcf0e4ff 大小:12976
2025-07-29 00:35:38 | DEBUG | [DoubaoImageRecognition] 缓存表情消息: 3928917850037235520
2025-07-29 00:35:40 | DEBUG | 收到消息: {'MsgId': 1545466863, 'FromUserName': {'string': '***********@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 1, 'Content': {'string': 'wxid_1ul5r40nibpn12:\n饿飞打'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1753720547, 'MsgSource': '<msgsource>\n\t<bizflag>0</bizflag>\n\t<pua>1</pua>\n\t<eggIncluded>1</eggIncluded>\n\t<silence>1</silence>\n\t<membercount>144</membercount>\n\t<signature>N0_V1_Z3mIxMSr|v1_PGc8MsVG</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'NewMsgId': 9117967835859193436, 'MsgSeq': 871407367}
2025-07-29 00:35:40 | INFO | 收到文本消息: 消息ID:1545466863 来自:***********@chatroom 发送人:wxid_1ul5r40nibpn12 @:[] 内容:饿飞打
2025-07-29 00:35:40 | DEBUG | 处理消息内容: '饿飞打'
2025-07-29 00:35:40 | DEBUG | 消息内容 '饿飞打' 不匹配任何命令，忽略
2025-07-29 00:35:54 | DEBUG | 收到消息: {'MsgId': 1060456242, 'FromUserName': {'string': '***********@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 1, 'Content': {'string': 'wxid_snv13qf05qjx11:\n好的 不退了'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1753720551, 'MsgSource': '<msgsource>\n\t<bizflag>0</bizflag>\n\t<pua>1</pua>\n\t<eggIncluded>1</eggIncluded>\n\t<silence>1</silence>\n\t<membercount>144</membercount>\n\t<signature>N0_V1_I+/XiQ5T|v1_5AsI7ZyA</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'NewMsgId': 7881939860224644400, 'MsgSeq': 871407368}
2025-07-29 00:35:54 | INFO | 收到文本消息: 消息ID:1060456242 来自:***********@chatroom 发送人:wxid_snv13qf05qjx11 @:[] 内容:好的 不退了
2025-07-29 00:35:54 | DEBUG | 处理消息内容: '好的 不退了'
2025-07-29 00:35:54 | DEBUG | 消息内容 '好的 不退了' 不匹配任何命令，忽略
2025-07-29 00:35:57 | DEBUG | 收到消息: {'MsgId': 391935862, 'FromUserName': {'string': '***********@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 1, 'Content': {'string': 'wxid_1ul5r40nibpn12:\n再让我休两年'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1753720560, 'MsgSource': '<msgsource>\n\t<bizflag>0</bizflag>\n\t<pua>1</pua>\n\t<eggIncluded>1</eggIncluded>\n\t<silence>1</silence>\n\t<membercount>144</membercount>\n\t<signature>N0_V1_olDqG+X+|v1_+F4mELFK</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'NewMsgId': 5768794063541720623, 'MsgSeq': 871407369}
2025-07-29 00:35:57 | INFO | 收到文本消息: 消息ID:391935862 来自:***********@chatroom 发送人:wxid_1ul5r40nibpn12 @:[] 内容:再让我休两年
2025-07-29 00:35:57 | DEBUG | 处理消息内容: '再让我休两年'
2025-07-29 00:35:57 | DEBUG | 消息内容 '再让我休两年' 不匹配任何命令，忽略
2025-07-29 00:36:07 | DEBUG | 收到消息: {'MsgId': 1166315337, 'FromUserName': {'string': '***********@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 47, 'Content': {'string': 'wxid_xv01lkcmn48l22:\n<msg><emoji fromusername = "wxid_xv01lkcmn48l22" tousername = "***********@chatroom" type="2" idbuffer="media:0_0" md5="7a29d5da102e3ad4609a3b480ed723ac" len = "196120" productid="" androidmd5="7a29d5da102e3ad4609a3b480ed723ac" androidlen="196120" s60v3md5 = "7a29d5da102e3ad4609a3b480ed723ac" s60v3len="196120" s60v5md5 = "7a29d5da102e3ad4609a3b480ed723ac" s60v5len="196120" cdnurl = "http://vweixinf.tc.qq.com/110/20401/stodownload?m=7a29d5da102e3ad4609a3b480ed723ac&amp;filekey=30440201010430302e02016e0402535a04203761323964356461313032653361643436303961336234383065643732336163020302fe18040d00000004627466730000000132&amp;hy=SZ&amp;storeid=267a57ad4000a869cb5fcb9e30000006e01004fb1535a237f201156a45d905&amp;ef=1&amp;bizid=1022" designerid = "" thumburl = "" encrypturl = "http://vweixinf.tc.qq.com/110/20402/stodownload?m=078816f9c7c4225e9266efa04977742a&amp;filekey=30440201010430302e02016e0402535a04203037383831366639633763343232356539323636656661303439373737343261020302fe20040d00000004627466730000000132&amp;hy=SZ&amp;storeid=267a57ad4000bbaafb5fcb9e30000006e02004fb2535a237f201156a45d90e&amp;ef=2&amp;bizid=1022" aeskey= "5d7aee598f9a4f8aafdf3d62af314f88" externurl = "http://vweixinf.tc.qq.com/110/20403/stodownload?m=93a456dc7508205d83934b34ae56057e&amp;filekey=30440201010430302e02016e0402535a04203933613435366463373530383230356438333933346233346165353630353765020300d640040d00000004627466730000000132&amp;hy=SZ&amp;storeid=267a57ad4000cff25b5fcb9e30000006e03004fb3535a237f201156a45d915&amp;ef=3&amp;bizid=1022" externmd5 = "967be247941be0c115ba583b4395d88a" width= "240" height= "240" tpurl= "" tpauthkey= "" attachedtext= "" attachedtextcolor= "" lensid= "" emojiattr= "" linkid= "" desc= "" ></emoji> <gameext type="0" content="0" ></gameext> </msg>'}, 'Status': 3, 'ImgStatus': 2, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1753720574, 'MsgSource': '<msgsource>\n\t<silence>1</silence>\n\t<membercount>144</membercount>\n\t<signature>N0_V1_HATSUUk6|v1_CfbD3Fyy</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'NewMsgId': 6278430959635042192, 'MsgSeq': 871407370}
2025-07-29 00:36:07 | INFO | 收到表情消息: 消息ID:1166315337 来自:***********@chatroom 发送人:wxid_xv01lkcmn48l22 MD5:7a29d5da102e3ad4609a3b480ed723ac 大小:196120
2025-07-29 00:36:07 | DEBUG | [DoubaoImageRecognition] 缓存表情消息: 6278430959635042192
2025-07-29 00:36:10 | DEBUG | 收到消息: {'MsgId': 769358004, 'FromUserName': {'string': '***********@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 1, 'Content': {'string': 'wxid_1ul5r40nibpn12:\n我可以和排位say goodbye了'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1753720577, 'MsgSource': '<msgsource>\n\t<bizflag>0</bizflag>\n\t<pua>1</pua>\n\t<alnode>\n\t\t<inlenlist>7</inlenlist>\n\t</alnode>\n\t<eggIncluded>1</eggIncluded>\n\t<silence>1</silence>\n\t<membercount>144</membercount>\n\t<signature>N0_V1_XP+V+sni|v1_Zg/OEmh9</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'NewMsgId': 5308113542250917442, 'MsgSeq': 871407371}
2025-07-29 00:36:10 | INFO | 收到文本消息: 消息ID:769358004 来自:***********@chatroom 发送人:wxid_1ul5r40nibpn12 @:[] 内容:我可以和排位say goodbye了
2025-07-29 00:36:10 | DEBUG | 处理消息内容: '我可以和排位say goodbye了'
2025-07-29 00:36:10 | DEBUG | 消息内容 '我可以和排位say goodbye了' 不匹配任何命令，忽略
2025-07-29 00:36:19 | DEBUG | 收到消息: {'MsgId': 759340040, 'FromUserName': {'string': '***********@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 1, 'Content': {'string': 'wxid_snv13qf05qjx11:\n你休两年 饿飞退了'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1753720586, 'MsgSource': '<msgsource>\n\t<bizflag>0</bizflag>\n\t<pua>1</pua>\n\t<eggIncluded>1</eggIncluded>\n\t<silence>1</silence>\n\t<membercount>144</membercount>\n\t<signature>N0_V1_VP2J4dB6|v1_YCizS80z</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'NewMsgId': 7433093799380099165, 'MsgSeq': 871407372}
2025-07-29 00:36:19 | INFO | 收到文本消息: 消息ID:759340040 来自:***********@chatroom 发送人:wxid_snv13qf05qjx11 @:[] 内容:你休两年 饿飞退了
2025-07-29 00:36:19 | DEBUG | 处理消息内容: '你休两年 饿飞退了'
2025-07-29 00:36:19 | DEBUG | 消息内容 '你休两年 饿飞退了' 不匹配任何命令，忽略
2025-07-29 00:36:23 | DEBUG | 收到消息: {'MsgId': 1503734877, 'FromUserName': {'string': '***********@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 47, 'Content': {'string': 'wxid_1ul5r40nibpn12:\n<msg><emoji fromusername="wxid_1ul5r40nibpn12" tousername="***********@chatroom" type="2" idbuffer="media:0_0" md5="29e3e8a2b5d732135f29fb8016d62de3" len="3129575" productid="" androidmd5="29e3e8a2b5d732135f29fb8016d62de3" androidlen="3129575" s60v3md5="29e3e8a2b5d732135f29fb8016d62de3" s60v3len="3129575" s60v5md5="29e3e8a2b5d732135f29fb8016d62de3" s60v5len="3129575" cdnurl="http://vweixinf.tc.qq.com/110/20401/stodownload?m=29e3e8a2b5d732135f29fb8016d62de3&amp;filekey=30440201010430302e02016e0402535a0420323965336538613262356437333231333566323966623830313664363264653302032fc0e7040d00000004627466730000000132&amp;hy=SZ&amp;storeid=2681d69860006429c06330e040000006e01004fb1535a21ccc151573e7e3e3&amp;ef=1&amp;bizid=1022" designerid="" thumburl="" encrypturl="http://vweixinf.tc.qq.com/110/20402/stodownload?m=0ee5e119c99078c0848c863d2c50331a&amp;filekey=30440201010430302e02016e0402535a0420306565356531313963393930373863303834386338363364326335303333316102032fc0f0040d00000004627466730000000132&amp;hy=SZ&amp;storeid=2681d698600096b3b06330e040000006e02004fb2535a21ccc151573e7e413&amp;ef=2&amp;bizid=1022" aeskey="e940693209b64343a45111d9bb5db615" externurl="http://vweixinf.tc.qq.com/110/20403/stodownload?m=806ba09dfbfd18400acf68b67afdf995&amp;filekey=30440201010430302e02016e0402535a042038303662613039646662666431383430306163663638623637616664663939350203023aa0040d00000004627466730000000132&amp;hy=SZ&amp;storeid=2681d6986000cd53806330e040000006e03004fb3535a21ccc151573e7e44d&amp;ef=3&amp;bizid=1022" externmd5="6f219a988daef43b9c18b118dc410abf" width="512" height="512" tpurl="" tpauthkey="" attachedtext="" attachedtextcolor="" lensid="" emojiattr="" linkid="" desc=""></emoji><gameext type="0" content="0"></gameext></msg>'}, 'Status': 3, 'ImgStatus': 2, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1753720589, 'MsgSource': '<msgsource>\n\t<silence>1</silence>\n\t<membercount>144</membercount>\n\t<signature>N0_V1_VaTVvTyO|v1_yx/IhGwV</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'NewMsgId': 4774037707571692493, 'MsgSeq': 871407373}
2025-07-29 00:36:23 | INFO | 收到表情消息: 消息ID:1503734877 来自:***********@chatroom 发送人:wxid_1ul5r40nibpn12 MD5:29e3e8a2b5d732135f29fb8016d62de3 大小:3129575
2025-07-29 00:36:23 | DEBUG | [DoubaoImageRecognition] 缓存表情消息: 4774037707571692493
2025-07-29 00:36:47 | DEBUG | 收到消息: {'MsgId': 67808751, 'FromUserName': {'string': '***********@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 1, 'Content': {'string': 'wxid_1ul5r40nibpn12:\n没事的 我自己打吧 我可以完成的'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1753720613, 'MsgSource': '<msgsource>\n\t<bizflag>0</bizflag>\n\t<pua>1</pua>\n\t<eggIncluded>1</eggIncluded>\n\t<silence>1</silence>\n\t<membercount>144</membercount>\n\t<signature>N0_V1_iQ9lW302|v1_HNkxN/Y8</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'NewMsgId': 669289572323175152, 'MsgSeq': 871407374}
2025-07-29 00:36:47 | INFO | 收到文本消息: 消息ID:67808751 来自:***********@chatroom 发送人:wxid_1ul5r40nibpn12 @:[] 内容:没事的 我自己打吧 我可以完成的
2025-07-29 00:36:47 | DEBUG | 处理消息内容: '没事的 我自己打吧 我可以完成的'
2025-07-29 00:36:47 | DEBUG | 消息内容 '没事的 我自己打吧 我可以完成的' 不匹配任何命令，忽略
2025-07-29 00:36:55 | DEBUG | 收到消息: {'MsgId': 1945906106, 'FromUserName': {'string': '***********@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 1, 'Content': {'string': 'wxid_xv01lkcmn48l22:\n没事的 我自己打吧 我可以完成的'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1753720622, 'MsgSource': '<msgsource>\n\t<bizflag>0</bizflag>\n\t<pua>1</pua>\n\t<alnode>\n\t\t<cf>2</cf>\n\t\t<inlenlist>16</inlenlist>\n\t</alnode>\n\t<eggIncluded>1</eggIncluded>\n\t<silence>1</silence>\n\t<membercount>144</membercount>\n\t<signature>N0_V1_hUya2Stm|v1_l08qnA3U</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'NewMsgId': 4187843681068035591, 'MsgSeq': 871407375}
2025-07-29 00:36:55 | INFO | 收到文本消息: 消息ID:1945906106 来自:***********@chatroom 发送人:wxid_xv01lkcmn48l22 @:[] 内容:没事的 我自己打吧 我可以完成的
2025-07-29 00:36:55 | DEBUG | 处理消息内容: '没事的 我自己打吧 我可以完成的'
2025-07-29 00:36:55 | DEBUG | 消息内容 '没事的 我自己打吧 我可以完成的' 不匹配任何命令，忽略
2025-07-29 00:37:11 | DEBUG | 收到消息: {'MsgId': 422651895, 'FromUserName': {'string': '***********@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 1, 'Content': {'string': 'wxid_snv13qf05qjx11:\n我女鹅真坚强'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1753720638, 'MsgSource': '<msgsource>\n\t<bizflag>0</bizflag>\n\t<pua>1</pua>\n\t<eggIncluded>1</eggIncluded>\n\t<silence>1</silence>\n\t<membercount>144</membercount>\n\t<signature>N0_V1_4isy2XF+|v1_SmXDJDda</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'NewMsgId': 5890694596243176999, 'MsgSeq': 871407376}
2025-07-29 00:37:11 | INFO | 收到文本消息: 消息ID:422651895 来自:***********@chatroom 发送人:wxid_snv13qf05qjx11 @:[] 内容:我女鹅真坚强
2025-07-29 00:37:11 | DEBUG | 处理消息内容: '我女鹅真坚强'
2025-07-29 00:37:11 | DEBUG | 消息内容 '我女鹅真坚强' 不匹配任何命令，忽略
2025-07-29 00:37:14 | DEBUG | 收到消息: {'MsgId': 1124877312, 'FromUserName': {'string': '***********@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 1, 'Content': {'string': 'wxid_1ul5r40nibpn12:\n打不完再让饿飞打也不迟'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1753720641, 'MsgSource': '<msgsource>\n\t<bizflag>0</bizflag>\n\t<pua>1</pua>\n\t<eggIncluded>1</eggIncluded>\n\t<silence>1</silence>\n\t<membercount>144</membercount>\n\t<signature>N0_V1_F0DATzli|v1_DSYSeIsD</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'NewMsgId': 3038739489354973418, 'MsgSeq': 871407377}
2025-07-29 00:37:14 | INFO | 收到文本消息: 消息ID:1124877312 来自:***********@chatroom 发送人:wxid_1ul5r40nibpn12 @:[] 内容:打不完再让饿飞打也不迟
2025-07-29 00:37:14 | DEBUG | 处理消息内容: '打不完再让饿飞打也不迟'
2025-07-29 00:37:14 | DEBUG | 消息内容 '打不完再让饿飞打也不迟' 不匹配任何命令，忽略
2025-07-29 00:37:17 | DEBUG | 收到消息: {'MsgId': 671423423, 'FromUserName': {'string': '***********@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 47, 'Content': {'string': 'wxid_snv13qf05qjx11:\n<msg><emoji fromusername = "wxid_snv13qf05qjx11" tousername = "***********@chatroom" type="2" idbuffer="media:0_0" md5="e265e60f4de76729d693417bf6c78a64" len = "1628486" productid="" androidmd5="e265e60f4de76729d693417bf6c78a64" androidlen="1628486" s60v3md5 = "e265e60f4de76729d693417bf6c78a64" s60v3len="1628486" s60v5md5 = "e265e60f4de76729d693417bf6c78a64" s60v5len="1628486" cdnurl = "http://vweixinf.tc.qq.com/110/20401/stodownload?m=e265e60f4de76729d693417bf6c78a64&amp;filekey=30440201010430302e02016e0402535a04206532363565363066346465373637323964363933343137626636633738613634020318d946040d00000004627466730000000132&amp;hy=SZ&amp;storeid=26691e3ed000cca9cc71958320000006e01004fb1535a2ccfe01156d2e74f5&amp;ef=1&amp;bizid=1022" designerid = "" thumburl = "" encrypturl = "http://vweixinf.tc.qq.com/110/20402/stodownload?m=4189f529ce794e0fc4c95f83cd67e18e&amp;filekey=30440201010430302e02016e0402535a04203431383966353239636537393465306663346339356638336364363765313865020318d950040d00000004627466730000000132&amp;hy=SZ&amp;storeid=26691e3ed000eb039c71958320000006e02004fb2535a2ccfe01156d2e7535&amp;ef=2&amp;bizid=1022" aeskey= "c5e7df76b6a2400487a0ed7c63164d5e" externurl = "http://vweixinf.tc.qq.com/110/20403/stodownload?m=96d74c3356029467dc93887841302875&amp;filekey=30440201010430302e02016e0402535a04203936643734633333353630323934363764633933383837383431333032383735020302b210040d00000004627466730000000132&amp;hy=SZ&amp;storeid=26691e3ee00012f8ac71958320000006e03004fb3535a2ccfe01156d2e7566&amp;ef=3&amp;bizid=1022" externmd5 = "d39412978c7df946dba99b2f7ab6709d" width= "240" height= "240" tpurl= "" tpauthkey= "" attachedtext= "" attachedtextcolor= "" lensid= "" emojiattr= "" linkid= "" desc= "" ></emoji> <gameext type="0" content="0" ></gameext> </msg>'}, 'Status': 3, 'ImgStatus': 2, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1753720642, 'MsgSource': '<msgsource>\n\t<silence>1</silence>\n\t<membercount>144</membercount>\n\t<signature>N0_V1_beuF/q6n|v1_x5zDw3Pw</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'NewMsgId': 8479392737814408404, 'MsgSeq': 871407378}
2025-07-29 00:37:17 | INFO | 收到表情消息: 消息ID:671423423 来自:***********@chatroom 发送人:wxid_snv13qf05qjx11 MD5:e265e60f4de76729d693417bf6c78a64 大小:1628486
2025-07-29 00:37:17 | DEBUG | [DoubaoImageRecognition] 缓存表情消息: 8479392737814408404
2025-07-29 00:37:20 | DEBUG | 收到消息: {'MsgId': 289651622, 'FromUserName': {'string': '***********@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 47, 'Content': {'string': 'wxid_1ul5r40nibpn12:\n<msg><emoji fromusername="wxid_1ul5r40nibpn12" tousername="***********@chatroom" type="2" idbuffer="media:0_0" md5="bc0989b4f1687caa295b06a9560642f0" len="238617" productid="" androidmd5="bc0989b4f1687caa295b06a9560642f0" androidlen="238617" s60v3md5="bc0989b4f1687caa295b06a9560642f0" s60v3len="238617" s60v5md5="bc0989b4f1687caa295b06a9560642f0" s60v5len="238617" cdnurl="http://vweixinf.tc.qq.com/110/20401/stodownload?m=bc0989b4f1687caa295b06a9560642f0&amp;filekey=30440201010430302e02016e0402534804206263303938396234663136383763616132393562303661393536303634326630020303a419040d00000004627466730000000132&amp;hy=SH&amp;storeid=267c55f43000eac0d740189770000006e01004fb1534824882bc1e6b46eabc&amp;ef=1&amp;bizid=1022" designerid="" thumburl="" encrypturl="http://vweixinf.tc.qq.com/110/20402/stodownload?m=361421189d58dce58b0175b82b67474d&amp;filekey=30440201010430302e02016e0402534804203336313432313138396435386463653538623031373562383262363734373464020303a420040d00000004627466730000000132&amp;hy=SH&amp;storeid=267c55f4400007075740189770000006e02004fb2534824882bc1e6b46eac8&amp;ef=2&amp;bizid=1022" aeskey="f8640774f7e1424bb952dcaae5bdd375" externurl="http://vweixinf.tc.qq.com/110/20403/stodownload?m=e9fbfeeddd70bab92d1043cdf8e50e45&amp;filekey=3043020101042f302d02016e040253480420653966626665656464643730626162393264313034336364663865353065343502021490040d00000004627466730000000132&amp;hy=SH&amp;storeid=267c55f440001fdac740189770000006e03004fb3534824882bc1e6b46eadd&amp;ef=3&amp;bizid=1022" externmd5="49258240564f649f3a21db33ed2ecb7c" width="504" height="495" tpurl="" tpauthkey="" attachedtext="" attachedtextcolor="" lensid="" emojiattr="" linkid="" desc=""></emoji><gameext type="0" content="0"></gameext></msg>'}, 'Status': 3, 'ImgStatus': 2, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1753720646, 'MsgSource': '<msgsource>\n\t<silence>1</silence>\n\t<membercount>144</membercount>\n\t<signature>N0_V1_a6BsKYO9|v1_tSa/x8ic</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'NewMsgId': 718109174842335120, 'MsgSeq': 871407379}
2025-07-29 00:37:20 | INFO | 收到表情消息: 消息ID:289651622 来自:***********@chatroom 发送人:wxid_1ul5r40nibpn12 MD5:bc0989b4f1687caa295b06a9560642f0 大小:238617
2025-07-29 00:37:20 | DEBUG | [DoubaoImageRecognition] 缓存表情消息: 718109174842335120
2025-07-29 00:37:28 | DEBUG | 收到消息: {'MsgId': 1284062353, 'FromUserName': {'string': '***********@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 1, 'Content': {'string': 'wxid_ikxxrwasicud11:\n陈坚强'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1753720655, 'MsgSource': '<msgsource>\n\t<atuserlist><![CDATA[]]></atuserlist>\n\t<silence>1</silence>\n\t<membercount>144</membercount>\n\t<signature>N0_V1_shEJeAZr|v1_74w9vCht</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'NewMsgId': 6312937271626855842, 'MsgSeq': 871407380}
2025-07-29 00:37:28 | INFO | 收到文本消息: 消息ID:1284062353 来自:***********@chatroom 发送人:wxid_ikxxrwasicud11 @:[] 内容:陈坚强
2025-07-29 00:37:28 | DEBUG | 处理消息内容: '陈坚强'
2025-07-29 00:37:28 | DEBUG | 消息内容 '陈坚强' 不匹配任何命令，忽略
2025-07-29 00:37:41 | DEBUG | 收到消息: {'MsgId': 517301385, 'FromUserName': {'string': '***********@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 1, 'Content': {'string': 'wxid_ikxxrwasicud11:\n好样der'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1753720669, 'MsgSource': '<msgsource>\n\t<atuserlist><![CDATA[]]></atuserlist>\n\t<silence>1</silence>\n\t<membercount>144</membercount>\n\t<signature>N0_V1_gc0XshZv|v1_rLkYW9Sf</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'NewMsgId': 6025822990575118600, 'MsgSeq': 871407381}
2025-07-29 00:37:41 | INFO | 收到文本消息: 消息ID:517301385 来自:***********@chatroom 发送人:wxid_ikxxrwasicud11 @:[] 内容:好样der
2025-07-29 00:37:41 | DEBUG | 处理消息内容: '好样der'
2025-07-29 00:37:41 | DEBUG | 消息内容 '好样der' 不匹配任何命令，忽略
2025-07-29 00:38:03 | DEBUG | 收到消息: {'MsgId': 1627096601, 'FromUserName': {'string': '***********@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 1, 'Content': {'string': 'wxid_snv13qf05qjx11:\n明天安排12个号的副本'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1753720690, 'MsgSource': '<msgsource>\n\t<bizflag>0</bizflag>\n\t<pua>1</pua>\n\t<eggIncluded>1</eggIncluded>\n\t<silence>1</silence>\n\t<membercount>144</membercount>\n\t<signature>N0_V1_rng0A4Xp|v1_5zMm+e49</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'NewMsgId': 4765896375774393823, 'MsgSeq': 871407382}
2025-07-29 00:38:03 | INFO | 收到文本消息: 消息ID:1627096601 来自:***********@chatroom 发送人:wxid_snv13qf05qjx11 @:[] 内容:明天安排12个号的副本
2025-07-29 00:38:03 | DEBUG | 处理消息内容: '明天安排12个号的副本'
2025-07-29 00:38:03 | DEBUG | 消息内容 '明天安排12个号的副本' 不匹配任何命令，忽略
2025-07-29 00:38:05 | DEBUG | 收到消息: {'MsgId': 1157645056, 'FromUserName': {'string': '***********@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 1, 'Content': {'string': 'wxid_1ul5r40nibpn12:\n李强坚'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1753720691, 'MsgSource': '<msgsource>\n\t<bizflag>0</bizflag>\n\t<pua>1</pua>\n\t<eggIncluded>1</eggIncluded>\n\t<silence>1</silence>\n\t<membercount>144</membercount>\n\t<signature>N0_V1_/HGkTMka|v1_tnCf7fLs</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'NewMsgId': 6806378987884959825, 'MsgSeq': 871407383}
2025-07-29 00:38:05 | INFO | 收到文本消息: 消息ID:1157645056 来自:***********@chatroom 发送人:wxid_1ul5r40nibpn12 @:[] 内容:李强坚
2025-07-29 00:38:05 | DEBUG | 处理消息内容: '李强坚'
2025-07-29 00:38:05 | DEBUG | 消息内容 '李强坚' 不匹配任何命令，忽略
2025-07-29 00:38:14 | DEBUG | 收到消息: {'MsgId': 1755821245, 'FromUserName': {'string': '***********@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 1, 'Content': {'string': 'wxid_1ul5r40nibpn12:\n明天不行 明天还在上班'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1753720701, 'MsgSource': '<msgsource>\n\t<bizflag>0</bizflag>\n\t<pua>1</pua>\n\t<eggIncluded>1</eggIncluded>\n\t<silence>1</silence>\n\t<membercount>144</membercount>\n\t<signature>N0_V1_O0pLf+oq|v1_30HJd1wm</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'NewMsgId': 7851531446876044020, 'MsgSeq': 871407384}
2025-07-29 00:38:14 | INFO | 收到文本消息: 消息ID:1755821245 来自:***********@chatroom 发送人:wxid_1ul5r40nibpn12 @:[] 内容:明天不行 明天还在上班
2025-07-29 00:38:14 | DEBUG | 处理消息内容: '明天不行 明天还在上班'
2025-07-29 00:38:14 | DEBUG | 消息内容 '明天不行 明天还在上班' 不匹配任何命令，忽略
2025-07-29 00:38:19 | DEBUG | 收到消息: {'MsgId': 1904288841, 'FromUserName': {'string': '***********@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 47, 'Content': {'string': 'wxid_1ul5r40nibpn12:\n<msg><emoji fromusername="wxid_1ul5r40nibpn12" tousername="***********@chatroom" type="2" idbuffer="media:0_0" md5="2428ae0abe86e906f64e06de754b7105" len="22705" productid="" androidmd5="2428ae0abe86e906f64e06de754b7105" androidlen="22705" s60v3md5="2428ae0abe86e906f64e06de754b7105" s60v3len="22705" s60v5md5="2428ae0abe86e906f64e06de754b7105" s60v5len="22705" cdnurl="http://wxapp.tc.qq.com/262/20304/stodownload?m=2428ae0abe86e906f64e06de754b7105&amp;filekey=30340201010420301e020201060402535a04102428ae0abe86e906f64e06de754b7105020258b1040d00000004627466730000000132&amp;hy=SZ&amp;storeid=268245a7b00094e84196b81b10000010600004f50535a0dfe915157fa59581&amp;bizid=1023" designerid="" thumburl="" encrypturl="http://wxapp.tc.qq.com/262/20304/stodownload?m=b107073d7e174978a68b9773759da332&amp;filekey=30340201010420301e020201060402535a0410b107073d7e174978a68b9773759da332020258c0040d00000004627466730000000132&amp;hy=SZ&amp;storeid=268245a7b000a9e2e196b81b10000010600004f50535a0e6ea15157eab0a7f&amp;bizid=1023" aeskey="18336fa7c6d01b06ddfd1dd10bf2fd86" externurl="http://wxapp.tc.qq.com/262/20304/stodownload?m=7ccf02607fc067f777d3da30b7625d65&amp;filekey=30340201010420301e020201060402535a04107ccf02607fc067f777d3da30b7625d6502022450040d00000004627466730000000132&amp;hy=SZ&amp;storeid=268245ae2000ef29d003deafd0000010600004f50535a24af2151578883391&amp;bizid=1023" externmd5="d5e5dbebe93270ff118e8dfe767cc9ea" width="568" height="568" tpurl="" tpauthkey="" attachedtext="" attachedtextcolor="" lensid="" emojiattr="" linkid="" desc=""></emoji><gameext type="0" content="0"></gameext></msg>'}, 'Status': 3, 'ImgStatus': 2, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1753720706, 'MsgSource': '<msgsource>\n\t<silence>1</silence>\n\t<membercount>144</membercount>\n\t<signature>N0_V1_IL1yd4x+|v1_CKKDxLhp</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'NewMsgId': 2182509087043665103, 'MsgSeq': 871407385}
2025-07-29 00:38:19 | INFO | 收到表情消息: 消息ID:1904288841 来自:***********@chatroom 发送人:wxid_1ul5r40nibpn12 MD5:2428ae0abe86e906f64e06de754b7105 大小:22705
2025-07-29 00:38:19 | DEBUG | [DoubaoImageRecognition] 缓存表情消息: 2182509087043665103
2025-07-29 00:38:25 | DEBUG | 收到消息: {'MsgId': 1736982987, 'FromUserName': {'string': '***********@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 47, 'Content': {'string': 'wxid_snv13qf05qjx11:\n<msg><emoji fromusername = "wxid_snv13qf05qjx11" tousername = "***********@chatroom" type="2" idbuffer="media:0_0" md5="668fe34e516a8a578a9741f6a14a0285" len = "48769" productid="" androidmd5="668fe34e516a8a578a9741f6a14a0285" androidlen="48769" s60v3md5 = "668fe34e516a8a578a9741f6a14a0285" s60v3len="48769" s60v5md5 = "668fe34e516a8a578a9741f6a14a0285" s60v5len="48769" cdnurl = "http://vweixinf.tc.qq.com/110/20401/stodownload?m=668fe34e516a8a578a9741f6a14a0285&amp;filekey=30440201010430302e02016e0402535a04203636386665333465353136613861353738613937343166366131346130323835020300be81040d00000004627466730000000132&amp;hy=SZ&amp;storeid=267e1294f000bc57b75cba2810000006e01004fb1535a2876bbc1e6cd05831&amp;ef=1&amp;bizid=1022" designerid = "" thumburl = "" encrypturl = "http://vweixinf.tc.qq.com/110/20402/stodownload?m=f2d35691cba6057792e8fe2aeea6ab65&amp;filekey=30440201010430302e02016e0402535a04206632643335363931636261363035373739326538666532616565613661623635020300be90040d00000004627466730000000132&amp;hy=SZ&amp;storeid=267e1294f000c690775cba2810000006e02004fb2535a2876bbc1e6cd0583e&amp;ef=2&amp;bizid=1022" aeskey= "31c2a6a926a443689232d30113efd4e0" externurl = "http://vweixinf.tc.qq.com/110/20403/stodownload?m=ed59ca96809729ccb6bd3a7c7f2ed977&amp;filekey=3043020101042f302d02016e0402535a0420656435396361393638303937323963636236626433613763376632656439373702024830040d00000004627466730000000132&amp;hy=SZ&amp;storeid=267e1294f000d00ff75cba2810000006e03004fb3535a2876bbc1e6cd05849&amp;ef=3&amp;bizid=1022" externmd5 = "a35c06f6e53d31fd515b287712d55e0c" width= "960" height= "959" tpurl= "" tpauthkey= "" attachedtext= "" attachedtextcolor= "" lensid= "" emojiattr= "" linkid= "" desc= "" ></emoji> <gameext type="0" content="0" ></gameext> </msg>'}, 'Status': 3, 'ImgStatus': 2, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1753720712, 'MsgSource': '<msgsource>\n\t<silence>1</silence>\n\t<membercount>144</membercount>\n\t<signature>N0_V1_S/e8TO+m|v1_xLJieFvO</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'NewMsgId': 2076988003846814572, 'MsgSeq': 871407386}
2025-07-29 00:38:25 | INFO | 收到表情消息: 消息ID:1736982987 来自:***********@chatroom 发送人:wxid_snv13qf05qjx11 MD5:668fe34e516a8a578a9741f6a14a0285 大小:48769
2025-07-29 00:38:25 | DEBUG | [DoubaoImageRecognition] 缓存表情消息: 2076988003846814572
2025-07-29 00:38:27 | DEBUG | 收到消息: {'MsgId': 1333193516, 'FromUserName': {'string': '***********@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 1, 'Content': {'string': 'wxid_ikxxrwasicud11:\n啥时候休息'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1753720714, 'MsgSource': '<msgsource>\n\t<atuserlist><![CDATA[]]></atuserlist>\n\t<silence>1</silence>\n\t<membercount>144</membercount>\n\t<signature>N0_V1_dEGqc5XY|v1_0jCyBrYC</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'NewMsgId': 2180484757854794567, 'MsgSeq': 871407387}
2025-07-29 00:38:27 | INFO | 收到文本消息: 消息ID:1333193516 来自:***********@chatroom 发送人:wxid_ikxxrwasicud11 @:[] 内容:啥时候休息
2025-07-29 00:38:27 | DEBUG | 处理消息内容: '啥时候休息'
2025-07-29 00:38:27 | DEBUG | 消息内容 '啥时候休息' 不匹配任何命令，忽略
2025-07-29 00:38:41 | DEBUG | 收到消息: {'MsgId': 1606219862, 'FromUserName': {'string': '***********@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 1, 'Content': {'string': 'wxid_ikxxrwasicud11:\n我记录一下'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1753720728, 'MsgSource': '<msgsource>\n\t<atuserlist><![CDATA[]]></atuserlist>\n\t<silence>1</silence>\n\t<membercount>144</membercount>\n\t<signature>N0_V1_Yx7BCRG4|v1_SUeL1Ln6</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'NewMsgId': 8779994451397461235, 'MsgSeq': 871407388}
2025-07-29 00:38:41 | INFO | 收到文本消息: 消息ID:1606219862 来自:***********@chatroom 发送人:wxid_ikxxrwasicud11 @:[] 内容:我记录一下
2025-07-29 00:38:41 | DEBUG | 处理消息内容: '我记录一下'
2025-07-29 00:38:41 | DEBUG | 消息内容 '我记录一下' 不匹配任何命令，忽略
2025-07-29 00:39:02 | DEBUG | 收到消息: {'MsgId': 1291464115, 'FromUserName': {'string': '***********@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 1, 'Content': {'string': 'wxid_1ul5r40nibpn12:\n30'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1753720749, 'MsgSource': '<msgsource>\n\t<bizflag>0</bizflag>\n\t<pua>1</pua>\n\t<eggIncluded>1</eggIncluded>\n\t<silence>1</silence>\n\t<membercount>144</membercount>\n\t<signature>N0_V1_kC6DnDIs|v1_73GBQ83V</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'NewMsgId': 3494674580873960077, 'MsgSeq': 871407389}
2025-07-29 00:39:02 | INFO | 收到文本消息: 消息ID:1291464115 来自:***********@chatroom 发送人:wxid_1ul5r40nibpn12 @:[] 内容:30
2025-07-29 00:39:02 | DEBUG | 处理消息内容: '30'
2025-07-29 00:39:02 | DEBUG | 消息内容 '30' 不匹配任何命令，忽略
2025-07-29 00:39:24 | DEBUG | 收到消息: {'MsgId': 1695727098, 'FromUserName': {'string': '***********@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 1, 'Content': {'string': 'wxid_snv13qf05qjx11:\n记录啥呀 安排的满满的'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1753720771, 'MsgSource': '<msgsource>\n\t<bizflag>0</bizflag>\n\t<pua>1</pua>\n\t<eggIncluded>1</eggIncluded>\n\t<silence>1</silence>\n\t<membercount>144</membercount>\n\t<signature>N0_V1_TJrJug+e|v1_86cZ4M3V</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'NewMsgId': 1035182687244722603, 'MsgSeq': 871407390}
2025-07-29 00:39:24 | INFO | 收到文本消息: 消息ID:1695727098 来自:***********@chatroom 发送人:wxid_snv13qf05qjx11 @:[] 内容:记录啥呀 安排的满满的
2025-07-29 00:39:24 | DEBUG | 处理消息内容: '记录啥呀 安排的满满的'
2025-07-29 00:39:24 | DEBUG | 消息内容 '记录啥呀 安排的满满的' 不匹配任何命令，忽略
2025-07-29 00:39:36 | DEBUG | 收到消息: {'MsgId': 422576610, 'FromUserName': {'string': '***********@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 47, 'Content': {'string': 'wxid_ikxxrwasicud11:\n<msg><emoji fromusername = "wxid_ikxxrwasicud11" tousername = "***********@chatroom" type="3" idbuffer="media:0_0" md5="42a64d8e6822468f1a931253d41ff286" len = "7018" productid="" androidmd5="42a64d8e6822468f1a931253d41ff286" androidlen="7018" s60v3md5 = "42a64d8e6822468f1a931253d41ff286" s60v3len="7018" s60v5md5 = "42a64d8e6822468f1a931253d41ff286" s60v5len="7018" cdnurl = "http://wxapp.tc.qq.com/262/20304/stodownload?m=42a64d8e6822468f1a931253d41ff286&amp;filekey=30340201010420301e0202010604025348041042a64d8e6822468f1a931253d41ff28602021b6a040d00000004627466730000000132&amp;hy=SH&amp;storeid=2630cb0450000a3f1000000000000010600004f5053482d364b40b787f8237&amp;bizid=1023" designerid = "" thumburl = "" encrypturl = "http://wxapp.tc.qq.com/262/20304/stodownload?m=0e081ec87523226238278c2b4dd9ed7a&amp;filekey=30340201010420301e020201060402535a04100e081ec87523226238278c2b4dd9ed7a02021b70040d00000004627466730000000132&amp;hy=SZ&amp;storeid=2630cb0450003e52f000000000000010600004f50535a1222788096750aed5&amp;bizid=1023" aeskey= "65fe51e67dc6b09b081baf27defd0855" externurl = "http://wxapp.tc.qq.com/262/20304/stodownload?m=ac34ec48d8168cebdbdedc7e8939cd47&amp;filekey=30340201010420301e02020106040253480410ac34ec48d8168cebdbdedc7e8939cd4702020f90040d00000004627466730000000132&amp;hy=SH&amp;storeid=2630cb04500062c09000000000000010600004f50534809165b40b78a49d67&amp;bizid=1023" externmd5 = "a0151a977df18c33a863394cf3e56f67" width= "240" height= "240" tpurl= "" tpauthkey= "" attachedtext= "" attachedtextcolor= "" lensid= "" emojiattr= "" linkid= "" desc= "" ></emoji>  </msg>'}, 'Status': 3, 'ImgStatus': 2, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1753720783, 'MsgSource': '<msgsource>\n\t<silence>1</silence>\n\t<membercount>144</membercount>\n\t<signature>N0_V1_GVO+eTGe|v1_53qeDxty</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'NewMsgId': 7389954652803800971, 'MsgSeq': 871407391}
2025-07-29 00:39:36 | INFO | 收到表情消息: 消息ID:422576610 来自:***********@chatroom 发送人:wxid_ikxxrwasicud11 MD5:42a64d8e6822468f1a931253d41ff286 大小:7018
2025-07-29 00:39:36 | DEBUG | [DoubaoImageRecognition] 缓存表情消息: 7389954652803800971
2025-07-29 00:39:37 | DEBUG | 收到消息: {'MsgId': 2070209154, 'FromUserName': {'string': '***********@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 1, 'Content': {'string': 'wxid_1ul5r40nibpn12:\n[快哭了]'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1753720784, 'MsgSource': '<msgsource>\n\t<bizflag>0</bizflag>\n\t<pua>1</pua>\n\t<eggIncluded>1</eggIncluded>\n\t<silence>1</silence>\n\t<membercount>144</membercount>\n\t<signature>N0_V1_aYgh7PgV|v1_l5e44t2s</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'NewMsgId': 902399806005249554, 'MsgSeq': 871407392}
2025-07-29 00:39:37 | INFO | 收到表情消息: 消息ID:2070209154 来自:***********@chatroom 发送人:wxid_1ul5r40nibpn12 @:[] 内容:[快哭了]
2025-07-29 00:39:37 | DEBUG | [DoubaoImageRecognition] 缓存表情消息: 902399806005249554
2025-07-29 00:41:24 | DEBUG | 收到消息: {'MsgId': 1814010068, 'FromUserName': {'string': '***********@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 1, 'Content': {'string': 'wxid_idzryo4rneok22:\n我问一下'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1753720891, 'MsgSource': '<msgsource>\n\t<pua>1</pua>\n\t<eggIncluded>1</eggIncluded>\n\t<silence>1</silence>\n\t<membercount>144</membercount>\n\t<signature>N0_V1_XJAFpZcx|v1_M1mD5dB9</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'NewMsgId': 6319115280711945606, 'MsgSeq': 871407393}
2025-07-29 00:41:24 | INFO | 收到文本消息: 消息ID:1814010068 来自:***********@chatroom 发送人:wxid_idzryo4rneok22 @:[] 内容:我问一下
2025-07-29 00:41:24 | DEBUG | 处理消息内容: '我问一下'
2025-07-29 00:41:24 | DEBUG | 消息内容 '我问一下' 不匹配任何命令，忽略
2025-07-29 00:41:28 | DEBUG | 收到消息: {'MsgId': 996505372, 'FromUserName': {'string': '***********@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 1, 'Content': {'string': 'wxid_idzryo4rneok22:\n谁给我打'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1753720895, 'MsgSource': '<msgsource>\n\t<pua>1</pua>\n\t<eggIncluded>1</eggIncluded>\n\t<silence>1</silence>\n\t<membercount>144</membercount>\n\t<signature>N0_V1_PCDJGZHR|v1_xD0ByIVO</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'NewMsgId': 791408411205416613, 'MsgSeq': 871407394}
2025-07-29 00:41:28 | INFO | 收到文本消息: 消息ID:996505372 来自:***********@chatroom 发送人:wxid_idzryo4rneok22 @:[] 内容:谁给我打
2025-07-29 00:41:28 | DEBUG | 处理消息内容: '谁给我打'
2025-07-29 00:41:28 | DEBUG | 消息内容 '谁给我打' 不匹配任何命令，忽略
2025-07-29 00:41:36 | DEBUG | 收到消息: {'MsgId': 540564710, 'FromUserName': {'string': '***********@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 1, 'Content': {'string': 'wxid_1ul5r40nibpn12:\n我啊'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1753720903, 'MsgSource': '<msgsource>\n\t<bizflag>0</bizflag>\n\t<pua>1</pua>\n\t<eggIncluded>1</eggIncluded>\n\t<silence>1</silence>\n\t<membercount>144</membercount>\n\t<signature>N0_V1_EXSvK0vm|v1_vUV1Gq/j</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'NewMsgId': 94949923697317407, 'MsgSeq': 871407395}
2025-07-29 00:41:36 | INFO | 收到文本消息: 消息ID:540564710 来自:***********@chatroom 发送人:wxid_1ul5r40nibpn12 @:[] 内容:我啊
2025-07-29 00:41:36 | DEBUG | 处理消息内容: '我啊'
2025-07-29 00:41:36 | DEBUG | 消息内容 '我啊' 不匹配任何命令，忽略
2025-07-29 00:41:40 | DEBUG | 收到消息: {'MsgId': 224677043, 'FromUserName': {'string': '***********@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 47, 'Content': {'string': 'wxid_idzryo4rneok22:\n<msg><emoji fromusername="wxid_idzryo4rneok22" tousername="***********@chatroom" type="1" idbuffer="media:0_0" md5="04cd78169ad229ef1b6fd009c841e0a3" len="30574" productid="" androidmd5="04cd78169ad229ef1b6fd009c841e0a3" androidlen="30574" s60v3md5="04cd78169ad229ef1b6fd009c841e0a3" s60v3len="30574" s60v5md5="04cd78169ad229ef1b6fd009c841e0a3" s60v5len="30574" cdnurl="http://vweixinf.tc.qq.com/110/20401/stodownload?m=04cd78169ad229ef1b6fd009c841e0a3&amp;filekey=3043020101042f302d02016e0402535a042030346364373831363961643232396566316236666430303963383431653061330202776e040d00000004627466730000000132&amp;hy=SZ&amp;storeid=266309d6f000cb76bd92d5ebb0000006e01004fb1535a0376ebc1e72d75a09&amp;ef=1&amp;bizid=1022" designerid="" thumburl="" encrypturl="http://vweixinf.tc.qq.com/110/20402/stodownload?m=63d03514b426b3dae3a30b361eca27e0&amp;filekey=3043020101042f302d02016e0402535a0420363364303335313462343236623364616533613330623336316563613237653002027770040d00000004627466730000000132&amp;hy=SZ&amp;storeid=266309d6f000d1083d92d5ebb0000006e02004fb2535a0376ebc1e72d75a16&amp;ef=2&amp;bizid=1022" aeskey="3db6eb2d1a8c4f89be1dbb7eedf9807d" externurl="http://vweixinf.tc.qq.com/110/20403/stodownload?m=6dc53f166ebbd901525bb13e9e0bf44e&amp;filekey=3043020101042f302d02016e0402535a0420366463353366313636656262643930313532356262313365396530626634346502020e00040d00000004627466730000000132&amp;hy=SZ&amp;storeid=266309d6f000d70a3d92d5ebb0000006e03004fb3535a0376ebc1e72d75a2a&amp;ef=3&amp;bizid=1022" externmd5="af832ab02eb841f386a9c9be425e1ce3" width="217" height="239" tpurl="" tpauthkey="" attachedtext="" attachedtextcolor="" lensid="" emojiattr="" linkid="" desc=""></emoji></msg>'}, 'Status': 3, 'ImgStatus': 2, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1753720907, 'MsgSource': '<msgsource>\n\t<silence>1</silence>\n\t<membercount>144</membercount>\n\t<signature>N0_V1_BRbWSn5M|v1_SAx0PhU5</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'NewMsgId': 2311893735245972647, 'MsgSeq': 871407396}
2025-07-29 00:41:40 | INFO | 收到表情消息: 消息ID:224677043 来自:***********@chatroom 发送人:wxid_idzryo4rneok22 MD5:04cd78169ad229ef1b6fd009c841e0a3 大小:30574
2025-07-29 00:41:40 | DEBUG | [DoubaoImageRecognition] 缓存表情消息: 2311893735245972647
2025-07-29 00:41:45 | DEBUG | 收到消息: {'MsgId': 947942554, 'FromUserName': {'string': '***********@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 47, 'Content': {'string': 'wxid_1ul5r40nibpn12:\n<msg><emoji fromusername="wxid_1ul5r40nibpn12" tousername="***********@chatroom" type="2" idbuffer="media:0_0" md5="ee4bd7c0401f63b42f91d7e5a8b12af7" len="756419" productid="" androidmd5="ee4bd7c0401f63b42f91d7e5a8b12af7" androidlen="756419" s60v3md5="ee4bd7c0401f63b42f91d7e5a8b12af7" s60v3len="756419" s60v5md5="ee4bd7c0401f63b42f91d7e5a8b12af7" s60v5len="756419" cdnurl="http://vweixinf.tc.qq.com/110/20401/stodownload?m=ee4bd7c0401f63b42f91d7e5a8b12af7&amp;filekey=30440201010430302e02016e040253480420656534626437633034303166363362343266393164376535613862313261663702030b8ac3040d00000004627466730000000132&amp;hy=SH&amp;storeid=262fa1a0d00096b535270a6060000006e01004fb1534821735b00b6df80d50&amp;ef=1&amp;bizid=1022" designerid="" thumburl="" encrypturl="http://vweixinf.tc.qq.com/110/20402/stodownload?m=26b9961707dc1a96a8ec569c8d197ccc&amp;filekey=30440201010430302e02016e040253480420323662393936313730376463316139366138656335363963386431393763636302030b8ad0040d00000004627466730000000132&amp;hy=SH&amp;storeid=262fa1a0d000b4f7c5270a6060000006e02004fb2534821735b00b6df80d84&amp;ef=2&amp;bizid=1022" aeskey="90e12cfe1301429e84aff3cdc34efe79" externurl="http://vweixinf.tc.qq.com/110/20403/stodownload?m=be8713ff073d49ef747453fe0e130161&amp;filekey=30440201010430302e02016e04025348042062653837313366663037336434396566373437343533666530653133303136310203009140040d00000004627466730000000132&amp;hy=SH&amp;storeid=262fa1a0d000d3ca95270a6060000006e03004fb3534821735b00b6df80dc9&amp;ef=3&amp;bizid=1022" externmd5="b79e86ab699e8f408052a2b2c49faef8" width="317" height="317" tpurl="" tpauthkey="" attachedtext="" attachedtextcolor="" lensid="" emojiattr="" linkid="" desc=""></emoji><gameext type="0" content="0"></gameext></msg>'}, 'Status': 3, 'ImgStatus': 2, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1753720912, 'MsgSource': '<msgsource>\n\t<silence>1</silence>\n\t<membercount>144</membercount>\n\t<signature>N0_V1_DvOM1eGo|v1_uX+PRFa6</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'NewMsgId': 3774269623138611776, 'MsgSeq': 871407397}
2025-07-29 00:41:45 | INFO | 收到表情消息: 消息ID:947942554 来自:***********@chatroom 发送人:wxid_1ul5r40nibpn12 MD5:ee4bd7c0401f63b42f91d7e5a8b12af7 大小:756419
2025-07-29 00:41:45 | DEBUG | [DoubaoImageRecognition] 缓存表情消息: 3774269623138611776
2025-07-29 00:41:51 | DEBUG | 收到消息: {'MsgId': 2132081216, 'FromUserName': {'string': '***********@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 1, 'Content': {'string': 'wxid_1ul5r40nibpn12:\n问废话呢'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1753720918, 'MsgSource': '<msgsource>\n\t<bizflag>0</bizflag>\n\t<pua>1</pua>\n\t<eggIncluded>1</eggIncluded>\n\t<silence>1</silence>\n\t<membercount>144</membercount>\n\t<signature>N0_V1_6KCyspK0|v1_4HJsJH6X</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'NewMsgId': 6523647842974426792, 'MsgSeq': 871407398}
2025-07-29 00:41:51 | INFO | 收到文本消息: 消息ID:2132081216 来自:***********@chatroom 发送人:wxid_1ul5r40nibpn12 @:[] 内容:问废话呢
2025-07-29 00:41:51 | DEBUG | 处理消息内容: '问废话呢'
2025-07-29 00:41:51 | DEBUG | 消息内容 '问废话呢' 不匹配任何命令，忽略
2025-07-29 00:42:28 | DEBUG | 收到消息: {'MsgId': 284191872, 'FromUserName': {'string': '***********@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 47, 'Content': {'string': 'wxid_idzryo4rneok22:\n<msg><emoji fromusername="wxid_idzryo4rneok22" tousername="***********@chatroom" type="3" idbuffer="media:0_0" md5="41ae5c0ccfa7743b9c1dfc8093b381f9" len="14368" productid="" androidmd5="41ae5c0ccfa7743b9c1dfc8093b381f9" androidlen="14368" s60v3md5="41ae5c0ccfa7743b9c1dfc8093b381f9" s60v3len="14368" s60v5md5="41ae5c0ccfa7743b9c1dfc8093b381f9" s60v5len="14368" cdnurl="http://vweixinf.tc.qq.com/110/20401/stodownload?m=41ae5c0ccfa7743b9c1dfc8093b381f9&amp;filekey=3043020101042f302d02016e040253480420343161653563306363666137373433623963316466633830393362333831663902023820040d00000004627466730000000132&amp;hy=SH&amp;storeid=265d46913000ee98a2bea69190000006e01004fb153481b83903156690c36c&amp;ef=1&amp;bizid=1022" designerid="" thumburl="" encrypturl="http://vweixinf.tc.qq.com/110/20402/stodownload?m=6f567e8ec3e8b257f412d5d0de270a7b&amp;filekey=3043020101042f302d02016e040253480420366635363765386563336538623235376634313264356430646532373061376202023830040d00000004627466730000000132&amp;hy=SH&amp;storeid=265d46914000035562bea69190000006e02004fb253481b83903156690c379&amp;ef=2&amp;bizid=1022" aeskey="be5ba7566b8c491788055a3292fb9810" externurl="http://vweixinf.tc.qq.com/110/20403/stodownload?m=48a224ef3150d35a70e1bd701f2f3065&amp;filekey=3043020101042f302d02016e040253480420343861323234656633313530643335613730653162643730316632663330363502022220040d00000004627466730000000132&amp;hy=SH&amp;storeid=265d469140000e2c82bea69190000006e03004fb353481b83903156690c387&amp;ef=3&amp;bizid=1022" externmd5="e72c4037ca67854ee7577d1eac6e1914" width="391" height="391" tpurl="" tpauthkey="" attachedtext="" attachedtextcolor="" lensid="" emojiattr="" linkid="" desc=""></emoji></msg>'}, 'Status': 3, 'ImgStatus': 2, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1753720955, 'MsgSource': '<msgsource>\n\t<silence>1</silence>\n\t<membercount>144</membercount>\n\t<signature>N0_V1_2UW5e0KO|v1_irp/N5JE</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'NewMsgId': 18522278844345788, 'MsgSeq': 871407399}
2025-07-29 00:42:28 | INFO | 收到表情消息: 消息ID:284191872 来自:***********@chatroom 发送人:wxid_idzryo4rneok22 MD5:41ae5c0ccfa7743b9c1dfc8093b381f9 大小:14368
2025-07-29 00:42:28 | DEBUG | [DoubaoImageRecognition] 缓存表情消息: 18522278844345788
2025-07-29 00:42:58 | DEBUG | 收到消息: {'MsgId': 1254047231, 'FromUserName': {'string': '***********@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 1, 'Content': {'string': 'wxid_idzryo4rneok22:\n反正你们一人打三把 就上去了'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1753720985, 'MsgSource': '<msgsource>\n\t<pua>1</pua>\n\t<eggIncluded>1</eggIncluded>\n\t<silence>1</silence>\n\t<membercount>144</membercount>\n\t<signature>N0_V1_J+kewOBv|v1_tiy0hSim</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'NewMsgId': 461817965210413537, 'MsgSeq': 871407400}
2025-07-29 00:42:58 | INFO | 收到文本消息: 消息ID:1254047231 来自:***********@chatroom 发送人:wxid_idzryo4rneok22 @:[] 内容:反正你们一人打三把 就上去了
2025-07-29 00:42:58 | DEBUG | 处理消息内容: '反正你们一人打三把 就上去了'
2025-07-29 00:42:58 | DEBUG | 消息内容 '反正你们一人打三把 就上去了' 不匹配任何命令，忽略
2025-07-29 00:43:05 | ERROR | 处理消息时发生错误: 
Traceback (most recent call last):
  File "C:\XYBotV2\venv\Lib\site-packages\httpx\_transports\default.py", line 101, in map_httpcore_exceptions
    yield
  File "C:\XYBotV2\venv\Lib\site-packages\httpx\_transports\default.py", line 394, in handle_async_request
    resp = await self._pool.handle_async_request(req)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\XYBotV2\venv\Lib\site-packages\httpcore\_async\connection_pool.py", line 256, in handle_async_request
    raise exc from None
  File "C:\XYBotV2\venv\Lib\site-packages\httpcore\_async\connection_pool.py", line 236, in handle_async_request
    response = await connection.handle_async_request(
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\XYBotV2\venv\Lib\site-packages\httpcore\_async\http_proxy.py", line 316, in handle_async_request
    stream = await stream.start_tls(**kwargs)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\XYBotV2\venv\Lib\site-packages\httpcore\_async\http11.py", line 376, in start_tls
    return await self._stream.start_tls(ssl_context, server_hostname, timeout)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\XYBotV2\venv\Lib\site-packages\httpcore\_backends\anyio.py", line 67, in start_tls
    with map_exceptions(exc_map):
  File "C:\Program Files\Python311\Lib\contextlib.py", line 158, in __exit__
    self.gen.throw(typ, value, traceback)
  File "C:\XYBotV2\venv\Lib\site-packages\httpcore\_exceptions.py", line 14, in map_exceptions
    raise to_exc(exc) from exc
httpcore.ConnectError

The above exception was the direct cause of the following exception:

Traceback (most recent call last):
  File "C:\XYBotV2\bot_core.py", line 72, in handle_message
    await xybot.process_message(msg)
  File "C:\XYBotV2\utils\xybot.py", line 97, in process_message
    await self.process_text_message(message)
  File "C:\XYBotV2\utils\xybot.py", line 274, in process_text_message
    await EventManager.emit("text_message", self.bot, message)
  File "C:\XYBotV2\utils\event_manager.py", line 35, in emit
    result = await handler(*handler_args, **new_kwargs)
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\XYBotV2\plugins\TencentLke\main.py", line 50, in handle_text
    await self.TencentLke(bot, message, message["Content"])
  File "C:\XYBotV2\plugins\TencentLke\main.py", line 63, in TencentLke
    async with client.stream("POST", url=url, headers=headers, content=payload, timeout=10) as resp:
  File "C:\Program Files\Python311\Lib\contextlib.py", line 210, in __aenter__
    return await anext(self.gen)
           ^^^^^^^^^^^^^^^^^^^^^
  File "C:\XYBotV2\venv\Lib\site-packages\httpx\_client.py", line 1583, in stream
    response = await self.send(
               ^^^^^^^^^^^^^^^^
  File "C:\XYBotV2\venv\Lib\site-packages\httpx\_client.py", line 1629, in send
    response = await self._send_handling_auth(
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\XYBotV2\venv\Lib\site-packages\httpx\_client.py", line 1657, in _send_handling_auth
    response = await self._send_handling_redirects(
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\XYBotV2\venv\Lib\site-packages\httpx\_client.py", line 1694, in _send_handling_redirects
    response = await self._send_single_request(request)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\XYBotV2\venv\Lib\site-packages\httpx\_client.py", line 1730, in _send_single_request
    response = await transport.handle_async_request(request)
               ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\XYBotV2\venv\Lib\site-packages\httpx\_transports\default.py", line 393, in handle_async_request
    with map_httpcore_exceptions():
  File "C:\Program Files\Python311\Lib\contextlib.py", line 158, in __exit__
    self.gen.throw(typ, value, traceback)
  File "C:\XYBotV2\venv\Lib\site-packages\httpx\_transports\default.py", line 118, in map_httpcore_exceptions
    raise mapped_exc(message) from exc
httpx.ConnectError

2025-07-29 00:43:06 | DEBUG | 收到消息: {'MsgId': 189708568, 'FromUserName': {'string': '***********@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 1, 'Content': {'string': 'wxid_idzryo4rneok22:\n红十咋的都行'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1753720993, 'MsgSource': '<msgsource>\n\t<pua>1</pua>\n\t<eggIncluded>1</eggIncluded>\n\t<silence>1</silence>\n\t<membercount>144</membercount>\n\t<signature>N0_V1_sYbkHZEF|v1_/R0oxCYS</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'NewMsgId': 2971078764182433705, 'MsgSeq': 871407401}
2025-07-29 00:43:06 | INFO | 收到文本消息: 消息ID:189708568 来自:***********@chatroom 发送人:wxid_idzryo4rneok22 @:[] 内容:红十咋的都行
2025-07-29 00:43:06 | DEBUG | 处理消息内容: '红十咋的都行'
2025-07-29 00:43:06 | DEBUG | 消息内容 '红十咋的都行' 不匹配任何命令，忽略
2025-07-29 00:43:13 | DEBUG | 收到消息: {'MsgId': 1523957191, 'FromUserName': {'string': '***********@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 47, 'Content': {'string': 'wxid_idzryo4rneok22:\n<msg><emoji fromusername="wxid_idzryo4rneok22" tousername="***********@chatroom" type="3" idbuffer="media:0_0" md5="41ae5c0ccfa7743b9c1dfc8093b381f9" len="14368" productid="" androidmd5="41ae5c0ccfa7743b9c1dfc8093b381f9" androidlen="14368" s60v3md5="41ae5c0ccfa7743b9c1dfc8093b381f9" s60v3len="14368" s60v5md5="41ae5c0ccfa7743b9c1dfc8093b381f9" s60v5len="14368" cdnurl="http://vweixinf.tc.qq.com/110/20401/stodownload?m=41ae5c0ccfa7743b9c1dfc8093b381f9&amp;filekey=3043020101042f302d02016e040253480420343161653563306363666137373433623963316466633830393362333831663902023820040d00000004627466730000000132&amp;hy=SH&amp;storeid=265d46913000ee98a2bea69190000006e01004fb153481b83903156690c36c&amp;ef=1&amp;bizid=1022" designerid="" thumburl="" encrypturl="http://vweixinf.tc.qq.com/110/20402/stodownload?m=6f567e8ec3e8b257f412d5d0de270a7b&amp;filekey=3043020101042f302d02016e040253480420366635363765386563336538623235376634313264356430646532373061376202023830040d00000004627466730000000132&amp;hy=SH&amp;storeid=265d46914000035562bea69190000006e02004fb253481b83903156690c379&amp;ef=2&amp;bizid=1022" aeskey="be5ba7566b8c491788055a3292fb9810" externurl="http://vweixinf.tc.qq.com/110/20403/stodownload?m=48a224ef3150d35a70e1bd701f2f3065&amp;filekey=3043020101042f302d02016e040253480420343861323234656633313530643335613730653162643730316632663330363502022220040d00000004627466730000000132&amp;hy=SH&amp;storeid=265d469140000e2c82bea69190000006e03004fb353481b83903156690c387&amp;ef=3&amp;bizid=1022" externmd5="e72c4037ca67854ee7577d1eac6e1914" width="391" height="391" tpurl="" tpauthkey="" attachedtext="" attachedtextcolor="" lensid="" emojiattr="" linkid="" desc=""></emoji></msg>'}, 'Status': 3, 'ImgStatus': 2, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1753720995, 'MsgSource': '<msgsource>\n\t<silence>1</silence>\n\t<membercount>144</membercount>\n\t<signature>N0_V1_PbLbgoMZ|v1_HpektZUt</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'NewMsgId': 5334775740722003080, 'MsgSeq': 871407402}
2025-07-29 00:43:13 | INFO | 收到表情消息: 消息ID:1523957191 来自:***********@chatroom 发送人:wxid_idzryo4rneok22 MD5:41ae5c0ccfa7743b9c1dfc8093b381f9 大小:14368
2025-07-29 00:43:13 | DEBUG | [DoubaoImageRecognition] 缓存表情消息: 5334775740722003080
2025-07-29 00:44:13 | DEBUG | 收到消息: {'MsgId': 693243536, 'FromUserName': {'string': '***********@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 1, 'Content': {'string': 'wxid_1ul5r40nibpn12:\n那不是我实在没功夫打了 下个月先打你的'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1753721060, 'MsgSource': '<msgsource>\n\t<bizflag>0</bizflag>\n\t<pua>1</pua>\n\t<eggIncluded>1</eggIncluded>\n\t<silence>1</silence>\n\t<membercount>144</membercount>\n\t<signature>N0_V1_KKNwWv25|v1_a+gaPSSx</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'NewMsgId': 3443887881686146817, 'MsgSeq': 871407403}
2025-07-29 00:44:13 | INFO | 收到文本消息: 消息ID:693243536 来自:***********@chatroom 发送人:wxid_1ul5r40nibpn12 @:[] 内容:那不是我实在没功夫打了 下个月先打你的
2025-07-29 00:44:13 | DEBUG | 处理消息内容: '那不是我实在没功夫打了 下个月先打你的'
2025-07-29 00:44:13 | DEBUG | 消息内容 '那不是我实在没功夫打了 下个月先打你的' 不匹配任何命令，忽略
2025-07-29 00:44:26 | DEBUG | 收到消息: {'MsgId': 640338192, 'FromUserName': {'string': '***********@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 47, 'Content': {'string': 'wxid_1ul5r40nibpn12:\n<msg><emoji fromusername="wxid_1ul5r40nibpn12" tousername="***********@chatroom" type="2" idbuffer="media:0_0" md5="f483a8084ee171f77649f073a72d029f" len="28237" productid="" androidmd5="f483a8084ee171f77649f073a72d029f" androidlen="28237" s60v3md5="f483a8084ee171f77649f073a72d029f" s60v3len="28237" s60v5md5="f483a8084ee171f77649f073a72d029f" s60v5len="28237" cdnurl="http://vweixinf.tc.qq.com/110/20401/stodownload?m=f483a8084ee171f77649f073a72d029f&amp;filekey=3043020101042f302d02016e040253480420663438336138303834656531373166373736343966303733613732643032396602026e4d040d00000004627466730000000132&amp;hy=SH&amp;storeid=267c8de99000866bfc9ef6def0000006e01004fb153480588bbc1e6c764150&amp;ef=1&amp;bizid=1022" designerid="" thumburl="" encrypturl="http://vweixinf.tc.qq.com/110/20402/stodownload?m=fdda6f52204b3a78382dcb39b49cf7bb&amp;filekey=3043020101042f302d02016e040253480420666464613666353232303462336137383338326463623339623439636637626202026e50040d00000004627466730000000132&amp;hy=SH&amp;storeid=267c8de990009162ac9ef6def0000006e02004fb253480588bbc1e6c764157&amp;ef=2&amp;bizid=1022" aeskey="f325e91bd8cc4c5b965b1686d21e59d4" externurl="http://vweixinf.tc.qq.com/110/20403/stodownload?m=11aa9bedac970b13e052dde7e5df2de0&amp;filekey=3043020101042f302d02016e040253480420313161613962656461633937306231336530353264646537653564663264653002023f10040d00000004627466730000000132&amp;hy=SH&amp;storeid=267c8de990009c1b7c9ef6def0000006e03004fb353480588bbc1e6c76415e&amp;ef=3&amp;bizid=1022" externmd5="11c4c762f644f158a8f077dca01a795a" width="240" height="240" tpurl="" tpauthkey="" attachedtext="" attachedtextcolor="" lensid="" emojiattr="" linkid="" desc=""></emoji><gameext type="0" content="0"></gameext></msg>'}, 'Status': 3, 'ImgStatus': 2, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1753721073, 'MsgSource': '<msgsource>\n\t<silence>1</silence>\n\t<membercount>144</membercount>\n\t<signature>N0_V1_JufNiXLf|v1_GbPskXLT</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'NewMsgId': 1951552986367673860, 'MsgSeq': 871407404}
2025-07-29 00:44:26 | INFO | 收到表情消息: 消息ID:640338192 来自:***********@chatroom 发送人:wxid_1ul5r40nibpn12 MD5:f483a8084ee171f77649f073a72d029f 大小:28237
2025-07-29 00:44:26 | DEBUG | [DoubaoImageRecognition] 缓存表情消息: 1951552986367673860
2025-07-29 00:45:17 | DEBUG | 收到消息: {'MsgId': 1084680689, 'FromUserName': {'string': '***********@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 47, 'Content': {'string': 'wxid_idzryo4rneok22:\n<msg><emoji fromusername="wxid_idzryo4rneok22" tousername="***********@chatroom" type="1" idbuffer="media:0_0" md5="8b09544f00efb6768fc0e81b7222cce3" len="33251" productid="" androidmd5="8b09544f00efb6768fc0e81b7222cce3" androidlen="33251" s60v3md5="8b09544f00efb6768fc0e81b7222cce3" s60v3len="33251" s60v5md5="8b09544f00efb6768fc0e81b7222cce3" s60v5len="33251" cdnurl="http://vweixinf.tc.qq.com/110/20401/stodownload?m=8b09544f00efb6768fc0e81b7222cce3&amp;filekey=30440201010430302e02016e040253480420386230393534346630306566623637363866633065383162373232326363653302030081e3040d00000004627466730000000132&amp;hy=SH&amp;storeid=2668f64fa000722579e3e93360000006e01004fb153482b68ebc1e677c403d&amp;ef=1&amp;bizid=1022" designerid="" thumburl="" encrypturl="http://vweixinf.tc.qq.com/110/20402/stodownload?m=059bc87a4c7884a09a4d91e70e74df14&amp;filekey=30440201010430302e02016e040253480420303539626338376134633738383461303961346439316537306537346466313402030081f0040d00000004627466730000000132&amp;hy=SH&amp;storeid=2668f64fa0007a8299e3e93360000006e02004fb253482b68ebc1e677c4056&amp;ef=2&amp;bizid=1022" aeskey="f1d0add28199432cb442732cd70ffdbf" externurl="http://vweixinf.tc.qq.com/110/20403/stodownload?m=9f22a2206af6455fe9ed9667c5cec339&amp;filekey=3043020101042f302d02016e040253480420396632326132323036616636343535666539656439363637633563656333333902024320040d00000004627466730000000132&amp;hy=SH&amp;storeid=2668f64fa0008337d9e3e93360000006e03004fb353482b68ebc1e677c406b&amp;ef=3&amp;bizid=1022" externmd5="69c2752378d4a46f04d36df034da85cc" width="660" height="568" tpurl="" tpauthkey="" attachedtext="" attachedtextcolor="" lensid="" emojiattr="" linkid="" desc=""></emoji></msg>'}, 'Status': 3, 'ImgStatus': 2, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1753721124, 'MsgSource': '<msgsource>\n\t<silence>1</silence>\n\t<membercount>144</membercount>\n\t<signature>N0_V1_9AE2xuyQ|v1_X+9y21xh</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'NewMsgId': 820735949927792712, 'MsgSeq': 871407405}
2025-07-29 00:45:17 | INFO | 收到表情消息: 消息ID:1084680689 来自:***********@chatroom 发送人:wxid_idzryo4rneok22 MD5:8b09544f00efb6768fc0e81b7222cce3 大小:33251
2025-07-29 00:45:17 | DEBUG | [DoubaoImageRecognition] 缓存表情消息: 820735949927792712
2025-07-29 00:46:23 | DEBUG | 收到消息: {'MsgId': 1196815942, 'FromUserName': {'string': '***********@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 1, 'Content': {'string': 'wxid_1ul5r40nibpn12:\n睡觉了阿壮'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1753721190, 'MsgSource': '<msgsource>\n\t<bizflag>0</bizflag>\n\t<pua>1</pua>\n\t<eggIncluded>1</eggIncluded>\n\t<silence>1</silence>\n\t<membercount>144</membercount>\n\t<signature>N0_V1_go3M1mmV|v1_zsR07noB</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'NewMsgId': 2632286217756885743, 'MsgSeq': 871407406}
2025-07-29 00:46:23 | INFO | 收到文本消息: 消息ID:1196815942 来自:***********@chatroom 发送人:wxid_1ul5r40nibpn12 @:[] 内容:睡觉了阿壮
2025-07-29 00:46:23 | DEBUG | 处理消息内容: '睡觉了阿壮'
2025-07-29 00:46:23 | DEBUG | 消息内容 '睡觉了阿壮' 不匹配任何命令，忽略
2025-07-29 00:47:08 | DEBUG | 收到消息: {'MsgId': **********, 'FromUserName': {'string': '***********@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 1, 'Content': {'string': 'wxid_idzryo4rneok22:\n321 睡'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': **********, 'MsgSource': '<msgsource>\n\t<pua>1</pua>\n\t<eggIncluded>1</eggIncluded>\n\t<silence>1</silence>\n\t<membercount>144</membercount>\n\t<signature>N0_V1_DcyAXLpR|v1_tOrBnPI7</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'NewMsgId': 9057038388927581582, 'MsgSeq': 871407407}
2025-07-29 00:47:08 | INFO | 收到文本消息: 消息ID:********** 来自:***********@chatroom 发送人:wxid_idzryo4rneok22 @:[] 内容:321 睡
2025-07-29 00:47:08 | DEBUG | 处理消息内容: '321 睡'
2025-07-29 00:47:08 | DEBUG | 消息内容 '321 睡' 不匹配任何命令，忽略
2025-07-29 00:54:06 | INFO | [TempFileManager] 开始清理临时文件...
2025-07-29 01:23:37 | DEBUG | 收到消息: {'MsgId': **********, 'FromUserName': {'string': '***********@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 1, 'Content': {'string': 'wxid_ubbh6q832tcs21:\n打'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': **********, 'MsgSource': '<msgsource>\n\t<pua>1</pua>\n\t<eggIncluded>1</eggIncluded>\n\t<silence>1</silence>\n\t<membercount>144</membercount>\n\t<signature>N0_V1_/tJ25Woc|v1_7RkizzOt</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'NewMsgId': 518368826970079749, 'MsgSeq': 871407408}
2025-07-29 01:23:37 | INFO | 收到文本消息: 消息ID:********** 来自:***********@chatroom 发送人:wxid_ubbh6q832tcs21 @:[] 内容:打
2025-07-29 01:23:38 | INFO | 发送表情消息: 对方wxid:***********@chatroom md5:fe3cd0728ea249c42967eca1a923ca19 总长度:9992069
2025-07-29 01:23:38 | DEBUG | 处理消息内容: '打'
2025-07-29 01:23:38 | DEBUG | 消息内容 '打' 不匹配任何命令，忽略
2025-07-29 01:24:06 | INFO | [TempFileManager] 开始清理临时文件...
2025-07-29 01:24:07 | DEBUG | 开始检查群成员变化，当前监控 2 个群
2025-07-29 01:24:08 | DEBUG | 群成员变化检查完成
2025-07-29 01:43:38 | DEBUG | 收到消息: {'MsgId': 1414659217, 'FromUserName': {'string': '48097389945@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 47, 'Content': {'string': 'wxid_ubbh6q832tcs21:\n<msg><emoji fromusername = "wxid_ubbh6q832tcs21" tousername = "48097389945@chatroom" type="2" idbuffer="media:0_0" md5="3c5d9173b23ecc025e1ec8c858142b57" len = "1005541" productid="" androidmd5="3c5d9173b23ecc025e1ec8c858142b57" androidlen="1005541" s60v3md5 = "3c5d9173b23ecc025e1ec8c858142b57" s60v3len="1005541" s60v5md5 = "3c5d9173b23ecc025e1ec8c858142b57" s60v5len="1005541" cdnurl = "http://vweixinf.tc.qq.com/110/20401/stodownload?m=3c5d9173b23ecc025e1ec8c858142b57&amp;filekey=30440201010430302e02016e040253480420336335643931373362323365636330323565316563386338353831343262353702030f57e5040d00000004627466730000000132&amp;hy=SH&amp;storeid=2683a773b00015f7c687175f00000006e01004fb153480fd65b01e02592370&amp;ef=1&amp;bizid=1022" designerid = "" thumburl = "" encrypturl = "http://vweixinf.tc.qq.com/110/20402/stodownload?m=4bab97cabbe1706f0a603389b3529eeb&amp;filekey=30440201010430302e02016e040253480420346261623937636162626531373036663061363033333839623335323965656202030f57f0040d00000004627466730000000132&amp;hy=SH&amp;storeid=2683a773b0003cd1f687175f00000006e02004fb253480fd65b01e02592390&amp;ef=2&amp;bizid=1022" aeskey= "0d82b033625c4bf18bc5c338aeb7333e" externurl = "http://vweixinf.tc.qq.com/110/20403/stodownload?m=0064acb5c83b81eef262bb4c27a223c8&amp;filekey=30440201010430302e02016e04025348042030303634616362356338336238316565663236326262346332376132323363380203020430040d00000004627466730000000132&amp;hy=SH&amp;storeid=2683a773b0005d930687175f00000006e03004fb353480fd65b01e025923ad&amp;ef=3&amp;bizid=1022" externmd5 = "ef10f433c759c4681e3dcaecf0b0a32d" width= "640" height= "640" tpurl= "" tpauthkey= "" attachedtext= "" attachedtextcolor= "" lensid= "" emojiattr= "" linkid= "" desc= "" ></emoji>  </msg>'}, 'Status': 3, 'ImgStatus': 2, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1753724625, 'MsgSource': '<msgsource>\n\t<silence>0</silence>\n\t<membercount>73</membercount>\n\t<signature>N0_V1_YWhN4uv/|v1_nXLlTs4b</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'PushContent': '郭在群聊中发了一个表情', 'NewMsgId': 4079786936398271108, 'MsgSeq': 871407411}
2025-07-29 01:43:38 | INFO | 收到表情消息: 消息ID:1414659217 来自:48097389945@chatroom 发送人:wxid_ubbh6q832tcs21 MD5:3c5d9173b23ecc025e1ec8c858142b57 大小:1005541
2025-07-29 01:43:38 | DEBUG | [DoubaoImageRecognition] 缓存表情消息: 4079786936398271108
2025-07-29 01:54:06 | INFO | [TempFileManager] 开始清理临时文件...
2025-07-29 02:24:06 | INFO | [TempFileManager] 开始清理临时文件...
2025-07-29 02:24:07 | DEBUG | 开始检查群成员变化，当前监控 2 个群
2025-07-29 02:24:08 | DEBUG | 群成员变化检查完成
2025-07-29 02:54:06 | INFO | [TempFileManager] 开始清理临时文件...
2025-07-29 03:24:06 | INFO | [TempFileManager] 开始清理临时文件...
2025-07-29 03:24:07 | DEBUG | 开始检查群成员变化，当前监控 2 个群
2025-07-29 03:24:08 | DEBUG | 群成员变化检查完成
2025-07-29 03:24:44 | DEBUG | 收到消息: {'MsgId': 922701095, 'FromUserName': {'string': '***********@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 1, 'Content': {'string': 'qianting1731076232:\n 签到'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1753730691, 'MsgSource': '<msgsource>\n\t<pua>1</pua>\n\t<eggIncluded>1</eggIncluded>\n\t<silence>1</silence>\n\t<membercount>144</membercount>\n\t<signature>N0_V1_Dol+G8TM|v1_M4u45lfc</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'NewMsgId': 8660491657932607309, 'MsgSeq': 871407412}
2025-07-29 03:24:44 | INFO | 收到文本消息: 消息ID:922701095 来自:***********@chatroom 发送人:qianting1731076232 @:[] 内容: 签到
2025-07-29 03:24:44 | DEBUG | 处理消息内容: '签到'
2025-07-29 03:24:44 | DEBUG | 消息内容 '签到' 不匹配任何命令，忽略
2025-07-29 03:24:44 | INFO | 数据库: 用户qianting1731076232登录时间设置为2025-07-29 00:00:00+08:00
2025-07-29 03:24:44 | INFO | 数据库: 用户qianting1731076232连续签到天数设置为6
2025-07-29 03:24:44 | INFO | 数据库: 用户qianting1731076232积分增加19
2025-07-29 03:24:45 | INFO | 发送文字消息: 对方wxid:***********@chatroom at:['qianting1731076232'] 内容:@奈斯༩༧ 
-----XYBot-----
签到成功！你领到了 18 个积分！✅
你是今天第 1 个签到的！🎉
你连续签到了 6 天！ 再奖励 1 积分！[爱心]
2025-07-29 03:24:45 | DEBUG | 收到消息: {'MsgId': 410312357, 'FromUserName': {'string': 'wxid_4usgcju5ey9q29'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 10002, 'Content': {'string': '<sysmsg type="ClientCheckConsistency"><ClientCheckConsistency><clientcheck><fullpathfilename>WeChat</fullpathfilename><fileoffset>0</fileoffset><checkbuffersize>9999999</checkbuffersize><seq>536870912</seq></clientcheck></ClientCheckConsistency></sysmsg>'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1753730693, 'MsgSource': '<msgsource>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'NewMsgId': 8942005499484232202, 'MsgSeq': 871407415}
2025-07-29 03:24:45 | DEBUG | 系统消息类型: ClientCheckConsistency
2025-07-29 03:24:45 | INFO | 未知的系统消息类型: {'MsgId': 410312357, 'ToWxid': 'wxid_4usgcju5ey9q29', 'MsgType': 10002, 'Content': '<sysmsg type="ClientCheckConsistency"><ClientCheckConsistency><clientcheck><fullpathfilename>WeChat</fullpathfilename><fileoffset>0</fileoffset><checkbuffersize>9999999</checkbuffersize><seq>536870912</seq></clientcheck></ClientCheckConsistency></sysmsg>', 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1753730693, 'MsgSource': '<msgsource>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'NewMsgId': 8942005499484232202, 'MsgSeq': 871407415, 'FromWxid': 'wxid_4usgcju5ey9q29', 'SenderWxid': 'wxid_4usgcju5ey9q29', 'IsGroup': False}
2025-07-29 03:54:06 | INFO | [TempFileManager] 开始清理临时文件...
2025-07-29 04:24:06 | INFO | [TempFileManager] 开始清理临时文件...
2025-07-29 04:24:07 | DEBUG | 开始检查群成员变化，当前监控 2 个群
2025-07-29 04:24:08 | DEBUG | 群成员变化检查完成
2025-07-29 04:36:52 | DEBUG | 收到消息: {'MsgId': 722573815, 'FromUserName': {'string': '48097389945@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 1, 'Content': {'string': 'wxid_wlnzvr8ivgd422:\n还没睡着'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1753735020, 'MsgSource': '<msgsource>\n\t<pua>1</pua>\n\t<eggIncluded>1</eggIncluded>\n\t<silence>0</silence>\n\t<membercount>73</membercount>\n\t<signature>N0_V1_PZ7QIhrY|v1_ChymsI0k</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'PushContent': '锦岚 : 还没睡着', 'NewMsgId': 2750821525456604814, 'MsgSeq': 871407416}
2025-07-29 04:36:52 | INFO | 收到文本消息: 消息ID:722573815 来自:48097389945@chatroom 发送人:wxid_wlnzvr8ivgd422 @:[] 内容:还没睡着
2025-07-29 04:36:53 | DEBUG | 处理消息内容: '还没睡着'
2025-07-29 04:36:53 | DEBUG | 消息内容 '还没睡着' 不匹配任何命令，忽略
2025-07-29 04:36:56 | DEBUG | 收到消息: {'MsgId': 1195089578, 'FromUserName': {'string': '48097389945@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 1, 'Content': {'string': 'wxid_wlnzvr8ivgd422:\n我真服了'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1753735023, 'MsgSource': '<msgsource>\n\t<pua>1</pua>\n\t<eggIncluded>1</eggIncluded>\n\t<silence>0</silence>\n\t<membercount>73</membercount>\n\t<signature>N0_V1_zw8vsPsp|v1_v+TbOw0W</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'PushContent': '锦岚 : 我真服了', 'NewMsgId': 4495394242267806302, 'MsgSeq': 871407417}
2025-07-29 04:36:56 | INFO | 收到文本消息: 消息ID:1195089578 来自:48097389945@chatroom 发送人:wxid_wlnzvr8ivgd422 @:[] 内容:我真服了
2025-07-29 04:36:57 | DEBUG | 处理消息内容: '我真服了'
2025-07-29 04:36:57 | DEBUG | 消息内容 '我真服了' 不匹配任何命令，忽略
2025-07-29 04:37:08 | DEBUG | 收到消息: {'MsgId': 1936859131, 'FromUserName': {'string': '48097389945@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 1, 'Content': {'string': 'wxid_wlnzvr8ivgd422:\n看小说越看越激动'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1753735036, 'MsgSource': '<msgsource>\n\t<pua>1</pua>\n\t<eggIncluded>1</eggIncluded>\n\t<silence>0</silence>\n\t<membercount>73</membercount>\n\t<signature>N0_V1_7WbvHdUx|v1_IOgLEGfg</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'PushContent': '锦岚 : 看小说越看越激动', 'NewMsgId': 6054765138773501989, 'MsgSeq': 871407418}
2025-07-29 04:37:08 | INFO | 收到文本消息: 消息ID:1936859131 来自:48097389945@chatroom 发送人:wxid_wlnzvr8ivgd422 @:[] 内容:看小说越看越激动
2025-07-29 04:37:09 | DEBUG | 处理消息内容: '看小说越看越激动'
2025-07-29 04:37:09 | DEBUG | 消息内容 '看小说越看越激动' 不匹配任何命令，忽略
2025-07-29 04:51:45 | DEBUG | 收到消息: {'MsgId': 1831133952, 'FromUserName': {'string': 'weixin'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 9999, 'Content': {'string': '<newcount>3</newcount><version>900</version>'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1753735913, 'MsgSource': '<msgsource>\n\t<signature>v1_8bI8ZG72</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'NewMsgId': 4654002541328034085, 'MsgSeq': 871407419}
2025-07-29 04:51:45 | INFO | 未知的消息类型: {'MsgId': 1831133952, 'ToWxid': 'wxid_4usgcju5ey9q29', 'MsgType': 9999, 'Content': {'string': '<newcount>3</newcount><version>900</version>'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1753735913, 'MsgSource': '<msgsource>\n\t<signature>v1_8bI8ZG72</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'NewMsgId': 4654002541328034085, 'MsgSeq': 871407419, 'FromWxid': 'weixin'}
2025-07-29 04:51:50 | DEBUG | 收到消息: {'MsgId': 713159212, 'FromUserName': {'string': 'wxid_4usgcju5ey9q29'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 51, 'Content': {'string': '<msg>\n<op id=\'11\'>\n<name>HandOffMaster</name>\n<arg><handofflist opcode="4" seq="163" devicevirtualid="863718534fa662b1b041fc5d1d50e54f" networkstatus="4g" availablecount="1000">\n        \n        </handofflist></arg>\n</op>\n</msg>'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1753735918, 'MsgSource': '<msgsource>\n\t<signature>v1_umF+EwH7</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'NewMsgId': 91394207153228360, 'MsgSeq': 871407420}
2025-07-29 04:52:21 | DEBUG | 收到消息: {'MsgId': 951216429, 'FromUserName': {'string': 'wxid_4usgcju5ey9q29'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 51, 'Content': {'string': '<msg>\n<op id=\'11\'>\n<name>HandOffMaster</name>\n<arg><handofflist opcode="4" seq="164" devicevirtualid="863718534fa662b1b041fc5d1d50e54f" networkstatus="4g" availablecount="1000">\n        \n        </handofflist></arg>\n</op>\n</msg>'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1753735949, 'MsgSource': '<msgsource>\n\t<signature>v1_Kn+VX1mG</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'NewMsgId': 6267963859182659259, 'MsgSeq': 871407421}
2025-07-29 04:54:06 | INFO | [TempFileManager] 开始清理临时文件...
2025-07-29 05:24:06 | INFO | [TempFileManager] 开始清理临时文件...
2025-07-29 05:24:07 | DEBUG | 开始检查群成员变化，当前监控 2 个群
2025-07-29 05:24:08 | DEBUG | 群成员变化检查完成
2025-07-29 05:26:03 | DEBUG | 收到消息: {'MsgId': 1483999770, 'FromUserName': {'string': '48097389945@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 47, 'Content': {'string': 'xiaomaochong:\n<msg><emoji fromusername="xiaomaochong" tousername="48097389945@chatroom" type="2" idbuffer="media:0_0" md5="02dd3138120243a6021c80a2fb8fda90" len="214989" productid="" androidmd5="02dd3138120243a6021c80a2fb8fda90" androidlen="214989" s60v3md5="02dd3138120243a6021c80a2fb8fda90" s60v3len="214989" s60v5md5="02dd3138120243a6021c80a2fb8fda90" s60v5len="214989" cdnurl="http://vweixinf.tc.qq.com/110/20401/stodownload?m=02dd3138120243a6021c80a2fb8fda90&amp;filekey=30440201010430302e02016e040253480420303264643331333831323032343361363032316338306132666238666461393002030347cd040d00000004627466730000000132&amp;hy=SH&amp;storeid=262b0b0ab000d708d5fae26b80000006e01004fb153480586cb40b6bca7476&amp;ef=1&amp;bizid=1022" designerid="" thumburl="" encrypturl="http://vweixinf.tc.qq.com/110/20402/stodownload?m=10d742c6202e6facb60f805e470619f5&amp;filekey=30440201010430302e02016e040253480420313064373432633632303265366661636236306638303565343730363139663502030347d0040d00000004627466730000000132&amp;hy=SH&amp;storeid=262b0b0ab000e2e205fae26b80000006e02004fb253480586cb40b6bca7480&amp;ef=2&amp;bizid=1022" aeskey="45b8a38a1ea842da9133e151a00c622f" externurl="" externmd5="" width="625" height="306" tpurl="" tpauthkey="" attachedtext="" attachedtextcolor="" lensid="" emojiattr="" linkid="" desc=""></emoji></msg>'}, 'Status': 3, 'ImgStatus': 2, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1753737970, 'MsgSource': '<msgsource>\n\t<silence>0</silence>\n\t<membercount>73</membercount>\n\t<signature>N0_V1_CTlLotva|v1_gwRp7sm5</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'PushContent': '小爱在群聊中发了一个表情', 'NewMsgId': 7735309536865493833, 'MsgSeq': 871407422}
2025-07-29 05:26:03 | INFO | 收到表情消息: 消息ID:1483999770 来自:48097389945@chatroom 发送人:xiaomaochong MD5:02dd3138120243a6021c80a2fb8fda90 大小:214989
2025-07-29 05:26:04 | DEBUG | [DoubaoImageRecognition] 缓存表情消息: 7735309536865493833
2025-07-29 05:34:22 | DEBUG | 收到消息: {'MsgId': 273972324, 'FromUserName': {'string': '48097389945@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 3, 'Content': {'string': 'xiaomaochong:\n<?xml version="1.0"?>\n<msg>\n\t<img aeskey="51e0d3c8806a206ba4f225608eab924c" encryver="1" cdnthumbaeskey="51e0d3c8806a206ba4f225608eab924c" cdnthumburl="3057020100044b30490201000204dfe4d3ff02032f5c9d0204cda93db7020468879138042438633434306636392d663433382d343664332d383061312d343064666438636631623930020405250a020201000405004c4dfd00" cdnthumblength="3042" cdnthumbheight="120" cdnthumbwidth="55" cdnmidheight="0" cdnmidwidth="0" cdnhdheight="0" cdnhdwidth="0" cdnmidimgurl="3057020100044b30490201000204dfe4d3ff02032f5c9d0204cda93db7020468879138042438633434306636392d663433382d343664332d383061312d343064666438636631623930020405250a020201000405004c4dfd00" length="86092" md5="bafd47b2d2e19e47b5d87e1750dceabe" originsourcemd5="a6cdc217c4a57075cc814c652fa67699">\n\t\t<secHashInfoBase64 />\n\t\t<live>\n\t\t\t<duration>0</duration>\n\t\t\t<size>0</size>\n\t\t\t<md5 />\n\t\t\t<fileid />\n\t\t\t<hdsize>0</hdsize>\n\t\t\t<hdmd5 />\n\t\t\t<hdfileid />\n\t\t\t<stillimagetimems>0</stillimagetimems>\n\t\t</live>\n\t</img>\n\t<platform_signature />\n\t<imgdatahash />\n\t<ImgSourceInfo>\n\t\t<ImgSourceUrl />\n\t\t<BizType>0</BizType>\n\t</ImgSourceInfo>\n</msg>\n'}, 'Status': 3, 'ImgStatus': 2, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1753738470, 'MsgSource': '<msgsource>\n\t<alnode>\n\t\t<fr>5</fr>\n\t</alnode>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n\t<sec_msg_node>\n\t\t<uuid>606a22ade495614571912e2786fa441c_</uuid>\n\t\t<risk-file-flag />\n\t\t<risk-file-md5-list />\n\t</sec_msg_node>\n\t<silence>0</silence>\n\t<membercount>73</membercount>\n\t<signature>N0_V1_0pK/qsHE|v1_8/ADdrp3</signature>\n</msgsource>\n', 'PushContent': '小爱在群聊中发了一张图片', 'NewMsgId': 3008358514722425534, 'MsgSeq': 871407423}
2025-07-29 05:34:22 | INFO | 收到图片消息: 消息ID:273972324 来自:48097389945@chatroom 发送人:xiaomaochong XML:<?xml version="1.0"?><msg><img aeskey="51e0d3c8806a206ba4f225608eab924c" encryver="1" cdnthumbaeskey="51e0d3c8806a206ba4f225608eab924c" cdnthumburl="3057020100044b30490201000204dfe4d3ff02032f5c9d0204cda93db7020468879138042438633434306636392d663433382d343664332d383061312d343064666438636631623930020405250a020201000405004c4dfd00" cdnthumblength="3042" cdnthumbheight="120" cdnthumbwidth="55" cdnmidheight="0" cdnmidwidth="0" cdnhdheight="0" cdnhdwidth="0" cdnmidimgurl="3057020100044b30490201000204dfe4d3ff02032f5c9d0204cda93db7020468879138042438633434306636392d663433382d343664332d383061312d343064666438636631623930020405250a020201000405004c4dfd00" length="86092" md5="bafd47b2d2e19e47b5d87e1750dceabe" originsourcemd5="a6cdc217c4a57075cc814c652fa67699"><secHashInfoBase64 /><live><duration>0</duration><size>0</size><md5 /><fileid /><hdsize>0</hdsize><hdmd5 /><hdfileid /><stillimagetimems>0</stillimagetimems></live></img><platform_signature /><imgdatahash /><ImgSourceInfo><ImgSourceUrl /><BizType>0</BizType></ImgSourceInfo></msg>
2025-07-29 05:34:23 | INFO | [ImageEcho] 保存图片信息成功，当前群 48097389945@chatroom 已存储 5 张图片
2025-07-29 05:34:23 | INFO | [TimerTask] 缓存图片消息: 273972324
2025-07-29 05:34:32 | DEBUG | 收到消息: {'MsgId': 2011102757, 'FromUserName': {'string': '48097389945@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 3, 'Content': {'string': 'xiaomaochong:\n<?xml version="1.0"?>\n<msg>\n\t<img aeskey="197eb2ebd4693ec2466b373303662bd0" encryver="1" cdnthumbaeskey="197eb2ebd4693ec2466b373303662bd0" cdnthumburl="3057020100044b30490201000204dfe4d3ff02032f5c9d0204cda93db702046887913a042435363161636133382d373165652d343563652d396562622d373966393365366564333839020405250a020201000405004c4e6100" cdnthumblength="3129" cdnthumbheight="120" cdnthumbwidth="55" cdnmidheight="0" cdnmidwidth="0" cdnhdheight="0" cdnhdwidth="0" cdnmidimgurl="3057020100044b30490201000204dfe4d3ff02032f5c9d0204cda93db702046887913a042435363161636133382d373165652d343563652d396562622d373966393365366564333839020405250a020201000405004c4e6100" length="109346" md5="28091d5918c4d35625b36f4e6250188f" originsourcemd5="7ace2fb3c4075b77996006adead06947">\n\t\t<secHashInfoBase64 />\n\t\t<live>\n\t\t\t<duration>0</duration>\n\t\t\t<size>0</size>\n\t\t\t<md5 />\n\t\t\t<fileid />\n\t\t\t<hdsize>0</hdsize>\n\t\t\t<hdmd5 />\n\t\t\t<hdfileid />\n\t\t\t<stillimagetimems>0</stillimagetimems>\n\t\t</live>\n\t</img>\n\t<platform_signature />\n\t<imgdatahash />\n\t<ImgSourceInfo>\n\t\t<ImgSourceUrl />\n\t\t<BizType>0</BizType>\n\t</ImgSourceInfo>\n</msg>\n'}, 'Status': 3, 'ImgStatus': 2, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1753738480, 'MsgSource': '<msgsource>\n\t<alnode>\n\t\t<fr>5</fr>\n\t</alnode>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n\t<sec_msg_node>\n\t\t<uuid>b001fc7e2231a5a9a499b7e11731062e_</uuid>\n\t\t<risk-file-flag />\n\t\t<risk-file-md5-list />\n\t</sec_msg_node>\n\t<silence>0</silence>\n\t<membercount>73</membercount>\n\t<signature>N0_V1_A55uoJ2c|v1_VDLpfAw/</signature>\n</msgsource>\n', 'PushContent': '小爱在群聊中发了一张图片', 'NewMsgId': 2025294584523346577, 'MsgSeq': 871407424}
2025-07-29 05:34:32 | INFO | 收到图片消息: 消息ID:2011102757 来自:48097389945@chatroom 发送人:xiaomaochong XML:<?xml version="1.0"?><msg><img aeskey="197eb2ebd4693ec2466b373303662bd0" encryver="1" cdnthumbaeskey="197eb2ebd4693ec2466b373303662bd0" cdnthumburl="3057020100044b30490201000204dfe4d3ff02032f5c9d0204cda93db702046887913a042435363161636133382d373165652d343563652d396562622d373966393365366564333839020405250a020201000405004c4e6100" cdnthumblength="3129" cdnthumbheight="120" cdnthumbwidth="55" cdnmidheight="0" cdnmidwidth="0" cdnhdheight="0" cdnhdwidth="0" cdnmidimgurl="3057020100044b30490201000204dfe4d3ff02032f5c9d0204cda93db702046887913a042435363161636133382d373165652d343563652d396562622d373966393365366564333839020405250a020201000405004c4e6100" length="109346" md5="28091d5918c4d35625b36f4e6250188f" originsourcemd5="7ace2fb3c4075b77996006adead06947"><secHashInfoBase64 /><live><duration>0</duration><size>0</size><md5 /><fileid /><hdsize>0</hdsize><hdmd5 /><hdfileid /><stillimagetimems>0</stillimagetimems></live></img><platform_signature /><imgdatahash /><ImgSourceInfo><ImgSourceUrl /><BizType>0</BizType></ImgSourceInfo></msg>
2025-07-29 05:34:33 | INFO | [ImageEcho] 保存图片信息成功，当前群 48097389945@chatroom 已存储 5 张图片
2025-07-29 05:34:33 | INFO | [TimerTask] 缓存图片消息: 2011102757
2025-07-29 05:34:47 | DEBUG | 收到消息: {'MsgId': 750975797, 'FromUserName': {'string': '48097389945@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 3, 'Content': {'string': 'xiaomaochong:\n<?xml version="1.0"?>\n<msg>\n\t<img aeskey="87cba43c86d017b4581d68d7290ec7f6" encryver="1" cdnthumbaeskey="87cba43c86d017b4581d68d7290ec7f6" cdnthumburl="3057020100044b30490201000204dfe4d3ff02032f5c9d0204cda93db702046887913b042437386463656661622d653830342d343063342d396562372d666264666537393336626530020405290a020201000405004c54a100" cdnthumblength="3181" cdnthumbheight="120" cdnthumbwidth="55" cdnmidheight="0" cdnmidwidth="0" cdnhdheight="0" cdnhdwidth="0" cdnmidimgurl="3057020100044b30490201000204dfe4d3ff02032f5c9d0204cda93db702046887913b042437386463656661622d653830342d343063342d396562372d666264666537393336626530020405290a020201000405004c54a100" length="113027" md5="beacdc104ea547517e4219014c915f2b" originsourcemd5="621a8556f95f3a7e6f123cf61b377bdd">\n\t\t<secHashInfoBase64 />\n\t\t<live>\n\t\t\t<duration>0</duration>\n\t\t\t<size>0</size>\n\t\t\t<md5 />\n\t\t\t<fileid />\n\t\t\t<hdsize>0</hdsize>\n\t\t\t<hdmd5 />\n\t\t\t<hdfileid />\n\t\t\t<stillimagetimems>0</stillimagetimems>\n\t\t</live>\n\t</img>\n\t<platform_signature />\n\t<imgdatahash />\n\t<ImgSourceInfo>\n\t\t<ImgSourceUrl />\n\t\t<BizType>0</BizType>\n\t</ImgSourceInfo>\n</msg>\n'}, 'Status': 3, 'ImgStatus': 2, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1753738495, 'MsgSource': '<msgsource>\n\t<alnode>\n\t\t<fr>5</fr>\n\t</alnode>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n\t<sec_msg_node>\n\t\t<uuid>db18eee27e701c2c0a4f8c9eb142b96d_</uuid>\n\t\t<risk-file-flag />\n\t\t<risk-file-md5-list />\n\t</sec_msg_node>\n\t<silence>0</silence>\n\t<membercount>73</membercount>\n\t<signature>N0_V1_HH+YrcaO|v1_h1jullIN</signature>\n</msgsource>\n', 'PushContent': '小爱在群聊中发了一张图片', 'NewMsgId': 1875394123983844491, 'MsgSeq': 871407425}
2025-07-29 05:34:47 | INFO | 收到图片消息: 消息ID:750975797 来自:48097389945@chatroom 发送人:xiaomaochong XML:<?xml version="1.0"?><msg><img aeskey="87cba43c86d017b4581d68d7290ec7f6" encryver="1" cdnthumbaeskey="87cba43c86d017b4581d68d7290ec7f6" cdnthumburl="3057020100044b30490201000204dfe4d3ff02032f5c9d0204cda93db702046887913b042437386463656661622d653830342d343063342d396562372d666264666537393336626530020405290a020201000405004c54a100" cdnthumblength="3181" cdnthumbheight="120" cdnthumbwidth="55" cdnmidheight="0" cdnmidwidth="0" cdnhdheight="0" cdnhdwidth="0" cdnmidimgurl="3057020100044b30490201000204dfe4d3ff02032f5c9d0204cda93db702046887913b042437386463656661622d653830342d343063342d396562372d666264666537393336626530020405290a020201000405004c54a100" length="113027" md5="beacdc104ea547517e4219014c915f2b" originsourcemd5="621a8556f95f3a7e6f123cf61b377bdd"><secHashInfoBase64 /><live><duration>0</duration><size>0</size><md5 /><fileid /><hdsize>0</hdsize><hdmd5 /><hdfileid /><stillimagetimems>0</stillimagetimems></live></img><platform_signature /><imgdatahash /><ImgSourceInfo><ImgSourceUrl /><BizType>0</BizType></ImgSourceInfo></msg>
2025-07-29 05:34:48 | INFO | [ImageEcho] 保存图片信息成功，当前群 48097389945@chatroom 已存储 5 张图片
2025-07-29 05:34:48 | INFO | [TimerTask] 缓存图片消息: 750975797
2025-07-29 05:54:06 | INFO | [TempFileManager] 开始清理临时文件...
2025-07-29 06:24:06 | INFO | [TempFileManager] 开始清理临时文件...
2025-07-29 06:24:07 | DEBUG | 开始检查群成员变化，当前监控 2 个群
2025-07-29 06:24:08 | DEBUG | 群成员变化检查完成
2025-07-29 06:34:36 | DEBUG | 收到消息: {'MsgId': 1753741860, 'FromUserName': {'string': 'newsapp'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 10002, 'Content': {'string': '<?xml version="1.0"?>\n<sysmsg type="functionmsg">\n\t<functionmsg>\n\t\t<cgi>/cgi-bin/micromsg-bin/addtxnewsmsg</cgi>\n\t\t<cmdid>825</cmdid>\n\t\t<businessid>50001</businessid>\n\t\t<functionmsgid>2025072900</functionmsgid>\n\t\t<op>0</op>\n\t\t<version>1753741803</version>\n\t\t<retryinterval>150</retryinterval>\n\t\t<reportid>63162</reportid>\n\t\t<successkey>0</successkey>\n\t\t<failkey>1</failkey>\n\t\t<finalfailkey>2</finalfailkey>\n\t\t<custombuff>CAAQ\nAzjY7p/EBkDr85/EBkik9J/EBlAA</custombuff>\n\t\t<retrycount>3</retrycount>\n\t</functionmsg>\n</sysmsg>\n'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1753742082, 'NewMsgId': 1753741860, 'MsgSeq': 0}
2025-07-29 06:34:36 | DEBUG | 系统消息类型: functionmsg
2025-07-29 06:34:36 | INFO | 未知的系统消息类型: {'MsgId': 1753741860, 'ToWxid': 'wxid_4usgcju5ey9q29', 'MsgType': 10002, 'Content': '<?xml version="1.0"?>\n<sysmsg type="functionmsg">\n\t<functionmsg>\n\t\t<cgi>/cgi-bin/micromsg-bin/addtxnewsmsg</cgi>\n\t\t<cmdid>825</cmdid>\n\t\t<businessid>50001</businessid>\n\t\t<functionmsgid>2025072900</functionmsgid>\n\t\t<op>0</op>\n\t\t<version>1753741803</version>\n\t\t<retryinterval>150</retryinterval>\n\t\t<reportid>63162</reportid>\n\t\t<successkey>0</successkey>\n\t\t<failkey>1</failkey>\n\t\t<finalfailkey>2</finalfailkey>\n\t\t<custombuff>CAAQ\nAzjY7p/EBkDr85/EBkik9J/EBlAA</custombuff>\n\t\t<retrycount>3</retrycount>\n\t</functionmsg>\n</sysmsg>\n', 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1753742082, 'NewMsgId': 1753741860, 'MsgSeq': 0, 'FromWxid': 'newsapp', 'SenderWxid': 'newsapp', 'IsGroup': False}
2025-07-29 06:54:07 | INFO | [TempFileManager] 开始清理临时文件...
2025-07-29 07:12:48 | DEBUG | 收到消息: {'MsgId': 762227140, 'FromUserName': {'string': '***********@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 1, 'Content': {'string': 'wxid_besewpsontwy29:\n签到'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1753744375, 'MsgSource': '<msgsource>\n\t<pua>1</pua>\n\t<eggIncluded>1</eggIncluded>\n\t<silence>1</silence>\n\t<membercount>144</membercount>\n\t<signature>N0_V1_aUfKSDa1|v1_3BWDxE8a</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'NewMsgId': 1728518680839469989, 'MsgSeq': 871407426}
2025-07-29 07:12:48 | INFO | 收到文本消息: 消息ID:762227140 来自:***********@chatroom 发送人:wxid_besewpsontwy29 @:[] 内容:签到
2025-07-29 07:12:48 | DEBUG | 处理消息内容: '签到'
2025-07-29 07:12:48 | DEBUG | 消息内容 '签到' 不匹配任何命令，忽略
2025-07-29 07:12:48 | INFO | 数据库: 用户wxid_besewpsontwy29登录时间设置为2025-07-29 00:00:00+08:00
2025-07-29 07:12:48 | INFO | 数据库: 用户wxid_besewpsontwy29连续签到天数设置为8
2025-07-29 07:12:48 | INFO | 数据库: 用户wxid_besewpsontwy29积分增加20
2025-07-29 07:12:49 | INFO | 发送文字消息: 对方wxid:***********@chatroom at:['wxid_besewpsontwy29'] 内容:@穆穆 
-----XYBot-----
签到成功！你领到了 19 个积分！✅
你是今天第 2 个签到的！🎉
你连续签到了 8 天！ 再奖励 1 积分！[爱心]
2025-07-29 07:24:07 | INFO | [TempFileManager] 开始清理临时文件...
2025-07-29 07:24:07 | DEBUG | 开始检查群成员变化，当前监控 2 个群
2025-07-29 07:24:08 | DEBUG | 群成员变化检查完成
2025-07-29 07:29:39 | DEBUG | 收到消息: {'MsgId': 740910487, 'FromUserName': {'string': '48097389945@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 1, 'Content': {'string': 'wxid_c90iqp1oemkj22:\n复读1'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1753745386, 'MsgSource': '<msgsource>\n\t<pua>1</pua>\n\t<eggIncluded>1</eggIncluded>\n\t<silence>0</silence>\n\t<membercount>73</membercount>\n\t<signature>N0_V1_s0zE8x4N|v1_++7aB0GF</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'PushContent': '情绪 : 复读1', 'NewMsgId': 1204289242118038377, 'MsgSeq': 871407429}
2025-07-29 07:29:39 | INFO | 收到文本消息: 消息ID:740910487 来自:48097389945@chatroom 发送人:wxid_c90iqp1oemkj22 @:[] 内容:复读1
2025-07-29 07:29:39 | DEBUG | 处理消息内容: '复读1'
2025-07-29 07:29:39 | DEBUG | 消息内容 '复读1' 不匹配任何命令，忽略
2025-07-29 07:40:48 | DEBUG | 收到消息: {'MsgId': 1353936275, 'FromUserName': {'string': '48097389945@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 47, 'Content': {'string': 'wxid_wlnzvr8ivgd422:\n<msg><emoji fromusername = "wxid_wlnzvr8ivgd422" tousername = "48097389945@chatroom" type="2" idbuffer="media:0_0" md5="5a1e188c79cc0c66e11cf127f264b68f" len = "510217" productid="" androidmd5="5a1e188c79cc0c66e11cf127f264b68f" androidlen="510217" s60v3md5 = "5a1e188c79cc0c66e11cf127f264b68f" s60v3len="510217" s60v5md5 = "5a1e188c79cc0c66e11cf127f264b68f" s60v5len="510217" cdnurl = "http://vweixinf.tc.qq.com/110/20401/stodownload?m=5a1e188c79cc0c66e11cf127f264b68f&amp;filekey=30440201010430302e02016e0402535a04203561316531383863373963633063363665313163663132376632363462363866020307c909040d00000004627466730000000132&amp;hy=SZ&amp;storeid=2680e22f40008a17cf6edcae50000006e01004fb1535a25afb01156b9c929a&amp;ef=1&amp;bizid=1022" designerid = "" thumburl = "" encrypturl = "http://vweixinf.tc.qq.com/110/20402/stodownload?m=3b6ed8c7819c05d151e239d2bc7b1025&amp;filekey=30440201010430302e02016e0402535a04203362366564386337383139633035643135316532333964326263376231303235020307c910040d00000004627466730000000132&amp;hy=SZ&amp;storeid=2680e22f4000a2535f6edcae50000006e02004fb2535a25afb01156b9c92b7&amp;ef=2&amp;bizid=1022" aeskey= "fc476faa13854c62bdf0b73c371a14c7" externurl = "http://vweixinf.tc.qq.com/110/20403/stodownload?m=22ed37e237bbfa12607da86c3efa6d01&amp;filekey=30440201010430302e02016e0402535a042032326564333765323337626266613132363037646138366333656661366430310203018c70040d00000004627466730000000132&amp;hy=SZ&amp;storeid=2680e22f4000af173f6edcae50000006e03004fb3535a25afb01156b9c92d7&amp;ef=3&amp;bizid=1022" externmd5 = "2176d5dd73a805cc2bc16d9796c670f1" width= "400" height= "400" tpurl= "" tpauthkey= "" attachedtext= "" attachedtextcolor= "" lensid= "" emojiattr= "" linkid= "" desc= "" ></emoji>  </msg>'}, 'Status': 3, 'ImgStatus': 2, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1753746056, 'MsgSource': '<msgsource>\n\t<silence>0</silence>\n\t<membercount>73</membercount>\n\t<signature>N0_V1_GE3mUhY6|v1_v6q823ur</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'PushContent': '锦岚在群聊中发了一个表情', 'NewMsgId': 6796405379896376763, 'MsgSeq': 871407430}
2025-07-29 07:40:48 | INFO | 收到表情消息: 消息ID:1353936275 来自:48097389945@chatroom 发送人:wxid_wlnzvr8ivgd422 MD5:5a1e188c79cc0c66e11cf127f264b68f 大小:510217
2025-07-29 07:40:49 | DEBUG | [DoubaoImageRecognition] 缓存表情消息: 6796405379896376763
2025-07-29 07:42:21 | DEBUG | 收到消息: {'MsgId': 1739505751, 'FromUserName': {'string': '48097389945@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 47, 'Content': {'string': 'last--exile:\n<msg><emoji fromusername="last--exile" tousername="48097389945@chatroom" type="2" idbuffer="media:0_0" md5="e61e743283c2198d1f434f2562aeebab" len="22608" productid="" androidmd5="e61e743283c2198d1f434f2562aeebab" androidlen="22608" s60v3md5="e61e743283c2198d1f434f2562aeebab" s60v3len="22608" s60v5md5="e61e743283c2198d1f434f2562aeebab" s60v5len="22608" cdnurl="http://vweixinf.tc.qq.com/110/20401/stodownload?m=e61e743283c2198d1f434f2562aeebab&amp;filekey=3043020101042f302d02016e0402535a0420653631653734333238336332313938643166343334663235363261656562616202025850040d00000004627466730000000132&amp;hy=SZ&amp;storeid=2626bd94f000006b2537480a90000006e01004fb1535a29dd6870b644197b8&amp;ef=1&amp;bizid=1022" designerid="" thumburl="" encrypturl="http://vweixinf.tc.qq.com/110/20402/stodownload?m=d431f24838d7167df08a711520af0079&amp;filekey=3043020101042f302d02016e0402535a0420643433316632343833386437313637646630386137313135323061663030373902025860040d00000004627466730000000132&amp;hy=SZ&amp;storeid=2626bd94f0000aa17537480a90000006e02004fb2535a29dd6870b644197c4&amp;ef=2&amp;bizid=1022" aeskey="00f007e39e3c4f11aa9b12eaa198e1ce" externurl="http://vweixinf.tc.qq.com/110/20403/stodownload?m=08fcb3d1e28145787ec7061c4bd73476&amp;filekey=3043020101042f302d02016e0402535a04203038666362336431653238313435373837656337303631633462643733343736020234e0040d00000004627466730000000132&amp;hy=SZ&amp;storeid=2626bd94f00013941537480a90000006e03004fb3535a29dd6870b644197d3&amp;ef=3&amp;bizid=1022" externmd5="3db589dcbbc43051084c7f698f19b62d" width="440" height="432" tpurl="" tpauthkey="" attachedtext="" attachedtextcolor="" lensid="" emojiattr="" linkid="" desc=""></emoji></msg>'}, 'Status': 3, 'ImgStatus': 2, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1753746148, 'MsgSource': '<msgsource>\n\t<silence>0</silence>\n\t<membercount>73</membercount>\n\t<signature>N0_V1_4lPM65GH|v1_Z+LJtqIn</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'PushContent': '亮在群聊中发了一个表情', 'NewMsgId': 321013602854144782, 'MsgSeq': 871407431}
2025-07-29 07:42:21 | INFO | 收到表情消息: 消息ID:1739505751 来自:48097389945@chatroom 发送人:last--exile MD5:e61e743283c2198d1f434f2562aeebab 大小:22608
2025-07-29 07:42:21 | DEBUG | [DoubaoImageRecognition] 缓存表情消息: 321013602854144782
2025-07-29 07:45:09 | DEBUG | 收到消息: {'MsgId': 1214565900, 'FromUserName': {'string': '48097389945@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 47, 'Content': {'string': 'wxid_l9koi6kli78i22:\n<msg><emoji fromusername="wxid_l9koi6kli78i22" tousername="48097389945@chatroom" type="1" idbuffer="media:0_0" md5="ab88010b06a5b13544b37c0766e290d7" len="53399" productid="" androidmd5="ab88010b06a5b13544b37c0766e290d7" androidlen="53399" s60v3md5="ab88010b06a5b13544b37c0766e290d7" s60v3len="53399" s60v5md5="ab88010b06a5b13544b37c0766e290d7" s60v5len="53399" cdnurl="http://vweixinf.tc.qq.com/110/20401/stodownload?m=ab88010b06a5b13544b37c0766e290d7&amp;filekey=30440201010430302e02016e0402534804206162383830313062303661356231333534346233376330373636653239306437020300d097040d00000004627466730000000132&amp;hy=SH&amp;storeid=26855122e000dc9d0ccc4eb3f0000006e01004fb153481b1371f156ed83a78&amp;ef=1&amp;bizid=1022" designerid="" thumburl="" encrypturl="http://vweixinf.tc.qq.com/110/20402/stodownload?m=2efc12d2f51f0a70fee51fb455040f94&amp;filekey=30440201010430302e02016e0402534804203265666331326432663531663061373066656535316662343535303430663934020300d0a0040d00000004627466730000000132&amp;hy=SH&amp;storeid=26855122e000eba7eccc4eb3f0000006e02004fb253481b1371f156ed83a83&amp;ef=2&amp;bizid=1022" aeskey="2788ad9b3ca7411fa1af093eb331d0ca" externurl="" externmd5="" width="240" height="178" tpurl="" tpauthkey="" attachedtext="" attachedtextcolor="" lensid="" emojiattr="" linkid="" desc=""></emoji></msg>'}, 'Status': 3, 'ImgStatus': 2, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1753746317, 'MsgSource': '<msgsource>\n\t<silence>0</silence>\n\t<membercount>73</membercount>\n\t<signature>N0_V1_OTzD5wKI|v1_SLT35qr4</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'PushContent': '十五在群聊中发了一个表情', 'NewMsgId': 7987122858466611738, 'MsgSeq': 871407432}
2025-07-29 07:45:09 | INFO | 收到表情消息: 消息ID:1214565900 来自:48097389945@chatroom 发送人:wxid_l9koi6kli78i22 MD5:ab88010b06a5b13544b37c0766e290d7 大小:53399
2025-07-29 07:45:10 | DEBUG | [DoubaoImageRecognition] 缓存表情消息: 7987122858466611738
2025-07-29 07:45:13 | DEBUG | 收到消息: {'MsgId': 1001177226, 'FromUserName': {'string': '48097389945@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 47, 'Content': {'string': 'wxid_l9koi6kli78i22:\n<msg><emoji fromusername="wxid_l9koi6kli78i22" tousername="48097389945@chatroom" type="2" idbuffer="media:0_0" md5="4d3de6f737fb939ed8a2d2a7c30c0d8c" len="4976199" productid="" androidmd5="4d3de6f737fb939ed8a2d2a7c30c0d8c" androidlen="4976199" s60v3md5="4d3de6f737fb939ed8a2d2a7c30c0d8c" s60v3len="4976199" s60v5md5="4d3de6f737fb939ed8a2d2a7c30c0d8c" s60v5len="4976199" cdnurl="http://vweixinf.tc.qq.com/110/20402/stodownload?m=4d3de6f737fb939ed8a2d2a7c30c0d8c&amp;filekey=30440201010430302e02016e0402535a0420346433646536663733376662393339656438613264326137633330633064386302034bee47040d00000004627466730000000132&amp;hy=SZ&amp;storeid=2675c573c000d76a824776ed30000006e01004fb2535a2bf6cbc1e69530cc2&amp;ef=1&amp;bizid=1022" designerid="" thumburl="" encrypturl="http://vweixinf.tc.qq.com/110/20402/stodownload?m=c6977239d9bf67c3cc3b4d28c2b141ec&amp;filekey=30440201010430302e02016e0402535a0420633639373732333964396266363763336363336234643238633262313431656302034bee50040d00000004627466730000000132&amp;hy=SZ&amp;storeid=2675c573d0002d82b24776ed30000006e02004fb2535a2bf6cbc1e69530ced&amp;ef=2&amp;bizid=1022" aeskey="e65af798c84749068c12efbf5e8ba789" externurl="http://vweixinf.tc.qq.com/110/20403/stodownload?m=b97734ddf7e8f41627f24bb9cd329138&amp;filekey=30440201010430302e02016e0402535a042062393737333464646637653866343136323766323462623963643332393133380203042c90040d00000004627466730000000132&amp;hy=SZ&amp;storeid=2675c573d0007b23f24776ed30000006e03004fb3535a2bf6cbc1e69530d0e&amp;ef=3&amp;bizid=1022" externmd5="e3d5c59563e6e04d866a634cda2b4b27" width="299" height="300" tpurl="" tpauthkey="" attachedtext="" attachedtextcolor="" lensid="" emojiattr="" linkid="" desc=""></emoji></msg>'}, 'Status': 3, 'ImgStatus': 2, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1753746321, 'MsgSource': '<msgsource>\n\t<silence>0</silence>\n\t<membercount>73</membercount>\n\t<signature>N0_V1_lukqEVHb|v1_MTMdBWPH</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'PushContent': '十五在群聊中发了一个表情', 'NewMsgId': 8437262419346132092, 'MsgSeq': 871407433}
2025-07-29 07:45:13 | INFO | 收到表情消息: 消息ID:1001177226 来自:48097389945@chatroom 发送人:wxid_l9koi6kli78i22 MD5:4d3de6f737fb939ed8a2d2a7c30c0d8c 大小:4976199
2025-07-29 07:45:14 | DEBUG | [DoubaoImageRecognition] 缓存表情消息: 8437262419346132092
2025-07-29 07:50:29 | DEBUG | 收到消息: {'MsgId': 66653799, 'FromUserName': {'string': 'wxid_4usgcju5ey9q29'}, 'ToWxid': {'string': '***********@chatroom'}, 'MsgType': 51, 'Content': {'string': '<msg>\n<op id=\'2\'>\n<username>***********@chatroom</username>\n<name>lastMessage</name>\n<arg>{"messageSvrId":"7935122228824923077","MsgCreateTime":"1753744377000"}</arg>\n</op>\n</msg>'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1753746638, 'MsgSource': '<msgsource>\n\t<signature>v1_Hi6gTfXa</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'NewMsgId': 6772974587271727791, 'MsgSeq': 871407434}
2025-07-29 07:50:31 | DEBUG | 收到消息: {'MsgId': 649123687, 'FromUserName': {'string': 'wxid_4usgcju5ey9q29'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 51, 'Content': {'string': '<msg>\n<op id=\'11\'>\n<name>HandOffMaster</name>\n<arg><handofflist opcode="4" seq="165" devicevirtualid="863718534fa662b1b041fc5d1d50e54f" networkstatus="4g" availablecount="1000">\n        \n        </handofflist></arg>\n</op>\n</msg>'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1753746640, 'MsgSource': '<msgsource>\n\t<signature>v1_ihk2UF52</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'NewMsgId': 5157050757600054994, 'MsgSeq': 871407435}
