import os, time, traceback, base64
from collections import deque
from datetime import datetime
from typing import Union
try:
    import tomllib
except ModuleNotFoundError:
    import tomli as tomllib
from loguru import logger
from pathlib import Path
from WechatAPI import WechatAPIClient
from utils.decorators import on_quote_message
from utils.plugin_base import PluginBase

class RevokePlugin(PluginBase):
    description = "消息撤回插件 - 可以撤回机器人发送的消息"
    author = "XYBot"
    version = "1.0.0"
    plugin_name = "RevokePlugin"

    # 类变量：存储插件实例
    _instance = None

    def __init__(self):
        super().__init__()
        self.temp_dir = Path("plugins/RevokePlugin/temp")
        self.temp_dir.mkdir(parents=True, exist_ok=True)
        self._load_config()
        self.user_last_request = {}

        # 消息存储：{wxid: deque([{msg_info, timestamp}, ...])}
        self.sent_messages = {}

        # 消息ID映射：{new_msg_id: {client_msg_id, create_time, new_msg_id, timestamp}}
        self.message_id_map = {}

        # 用于标记是否已经包装了bot方法
        self._bot_wrapped = False
        self._bot_instance = None

        # 设置类实例
        RevokePlugin._instance = self

    @classmethod
    def notify_message_sent(cls, wxid: str, client_msg_id, create_time: int, new_msg_id):
        """类方法：供其他插件调用，通知消息发送成功"""
        if cls._instance and cls._instance.enable:
            cls._instance.store_sent_message(wxid, client_msg_id, create_time, new_msg_id)

    async def on_enable(self, bot=None):
        """插件启用时调用，立即包装bot方法"""
        await super().on_enable(bot)
        if bot:
            self._bot_instance = bot
            self._wrap_bot_methods(bot)

    def _load_config(self):
        try:
            with open(f"plugins/{self.plugin_name}/config.toml", "rb") as f:
                config = tomllib.load(f).get(self.plugin_name, {})
        except:
            config = {}

        self.enable = config.get("enable", True)
        self.command = config.get("command", ["撤回", "撤销", "消息列表"])
        self.command_format = config.get("command-format", "使用方法：\n1. 撤回 [数字] - 撤回最近的第几条消息（默认撤回最后一条）\n2. 引用消息 + 撤回 - 撤回被引用的消息\n3. 消息列表 - 查看可撤回的消息")
        self.cooldown = config.get("rate_limit", {}).get("cooldown", 3)

        # 撤回限制配置
        limits = config.get("limits", {})
        self.time_limit = limits.get("time_limit", 120)  # 2分钟
        self.max_count = limits.get("max_count", 10)
        self.admin_only = limits.get("admin_only", False)

    def _wrap_bot_methods(self, bot: WechatAPIClient):
        """包装bot的发送方法以拦截消息ID"""
        if self._bot_wrapped and self._bot_instance is bot:
            return

        # 保存原始方法
        original_send_text = bot.send_text_message
        original_send_image = bot.send_image_message
        original_send_voice = bot.send_voice_message
        original_send_app = bot.send_app_message
        original_send_at = bot.send_at_message
        original_send_cdn_img = bot.send_cdn_img_msg
        original_send_link = bot.send_link_message
        original_send_video = bot.send_video_message

        async def wrapped_send_text(wxid: str, content: str, at: Union[list, str] = "") -> tuple[int, int, int]:
            result = await original_send_text(wxid, content, at)
            if result and len(result) == 3:
                self.store_sent_message(wxid, result[0], result[1], result[2])
            return result

        async def wrapped_send_image(wxid: str, image) -> tuple[int, int, int]:
            result = await original_send_image(wxid, image)
            if result and len(result) == 3:
                # 存储消息信息并缓存图片
                local_image_path = self._cache_sent_image(image, result[2])
                self.store_sent_message(wxid, result[0], result[1], result[2], local_image_path)
            return result

        async def wrapped_send_voice(wxid: str, voice, format: str = "amr") -> tuple[int, int, int]:
            result = await original_send_voice(wxid, voice, format)
            if result and len(result) == 3:
                self.store_sent_message(wxid, result[0], result[1], result[2])
            return result

        async def wrapped_send_app(wxid: str, xml: str, type: int) -> tuple[int, int, int]:
            result = await original_send_app(wxid, xml, type)
            if result and len(result) == 3:
                self.store_sent_message(wxid, result[0], result[1], result[2])
            return result

        async def wrapped_send_at(wxid: str, content: str, at: list[str]) -> tuple[int, int, int]:
            result = await original_send_at(wxid, content, at)
            if result and len(result) == 3:
                self.store_sent_message(wxid, result[0], result[1], result[2])
            return result

        async def wrapped_send_cdn_img(wxid: str, xml: str) -> tuple[str, int, int]:
            result = await original_send_cdn_img(wxid, xml)
            if result and len(result) == 3:
                self.store_sent_message(wxid, result[0], result[1], result[2])
            return result

        async def wrapped_send_link(wxid: str, url: str, title: str = "", description: str = "", thumb_url: str = "") -> tuple[str, int, int]:
            result = await original_send_link(wxid, url, title, description, thumb_url)
            if result and len(result) == 3:
                self.store_sent_message(wxid, result[0], result[1], result[2])
            return result

        async def wrapped_send_video(wxid: str, video, image=None) -> tuple[int, int]:
            result = await original_send_video(wxid, video, image)
            if result and len(result) == 2:
                # 视频消息返回 (ClientMsgid, NewMsgId)，没有CreateTime
                # 使用当前时间作为CreateTime
                import time
                create_time = int(time.time())
                self.store_sent_message(wxid, result[0], create_time, result[1])
            return result

        # 替换方法
        bot.send_text_message = wrapped_send_text
        bot.send_image_message = wrapped_send_image
        bot.send_voice_message = wrapped_send_voice
        bot.send_app_message = wrapped_send_app
        bot.send_at_message = wrapped_send_at
        bot.send_cdn_img_msg = wrapped_send_cdn_img
        bot.send_link_message = wrapped_send_link
        bot.send_video_message = wrapped_send_video

        self._bot_wrapped = True
        self._bot_instance = bot

    def _cache_sent_image(self, image, new_msg_id) -> str:
        """缓存发送的图片，返回本地路径"""
        try:
            # 创建图片缓存目录
            cache_dir = self.temp_dir / "sent_images"
            cache_dir.mkdir(parents=True, exist_ok=True)

            # 生成缓存文件路径
            cache_file = cache_dir / f"{new_msg_id}.jpg"

            # 获取图片数据
            image_data = None
            if hasattr(image, 'read'):  # 如果是文件对象
                current_pos = image.tell() if hasattr(image, 'tell') else 0
                image.seek(0) if hasattr(image, 'seek') else None
                image_data = image.read()
                image.seek(current_pos) if hasattr(image, 'seek') else None
            elif isinstance(image, (bytes, bytearray)):  # 如果是字节数据
                image_data = bytes(image)
            elif isinstance(image, str):
                # 处理字符串类型：可能是文件路径或base64数据
                if os.path.exists(image):  # 如果是文件路径
                    with open(image, 'rb') as f:
                        image_data = f.read()
                else:  # 尝试作为base64数据处理
                    try:
                        # 检查是否包含base64前缀，如果有则去除
                        if ',' in image:
                            image = image.split(',', 1)[1]
                        image_data = base64.b64decode(image)
                    except Exception as base64_error:
                        logger.debug(f"[{self.plugin_name}] base64解码失败，可能不是base64数据: {str(base64_error)}")
                        # 如果base64解码失败，可能是其他格式的字符串，不记录警告
                        return None

            if image_data:
                # 保存图片到缓存目录
                with open(cache_file, 'wb') as f:
                    f.write(image_data)
                return str(cache_file)
            else:
                # 只有在确实无法处理时才记录警告，避免对正常情况产生噪音
                logger.debug(f"[{self.plugin_name}] 无法获取图片数据进行缓存，image类型: {type(image)}")
                return None

        except Exception as e:
            logger.error(f"[{self.plugin_name}] 缓存图片失败: {str(e)}")
            return None

    def store_sent_message(self, wxid: str, client_msg_id, create_time: int, new_msg_id, local_image_path: str = None):
        """存储发送的消息信息"""
        if wxid not in self.sent_messages:
            self.sent_messages[wxid] = deque(maxlen=self.max_count)

        # client_msg_id可以是字符串（图片消息）或整数（文本消息），保持原始类型
        # 不需要强制转换为整数

        # 处理不同类型的new_msg_id，确保是整数用于映射查找
        if isinstance(new_msg_id, str):
            try:
                new_msg_id = int(new_msg_id)
            except (ValueError, TypeError):
                logger.warning(f"[{self.plugin_name}] 无法转换new_msg_id为整数: {new_msg_id}")
                return

        msg_info = {
            'client_msg_id': client_msg_id,
            'create_time': create_time,
            'new_msg_id': new_msg_id,
            'timestamp': datetime.now()
        }

        # 如果有本地图片路径，添加到消息信息中
        if local_image_path:
            msg_info['local_image_path'] = local_image_path

        self.sent_messages[wxid].append(msg_info)

        # 同时存储到消息ID映射中，方便通过new_msg_id查找
        self.message_id_map[new_msg_id] = msg_info

        # 清理过期的消息ID映射
        self._cleanup_expired_messages()

    def _cleanup_expired_messages(self):
        """清理过期的消息ID映射"""
        current_time = datetime.now()
        expired_ids = []

        for msg_id, msg_info in self.message_id_map.items():
            time_diff = (current_time - msg_info['timestamp']).total_seconds()
            if time_diff > self.time_limit:
                expired_ids.append(msg_id)

        for msg_id in expired_ids:
            del self.message_id_map[msg_id]

    def get_message_by_id(self, new_msg_id: str) -> dict:
        """通过new_msg_id获取消息信息"""
        try:
            # new_msg_id可能是字符串，需要转换为整数
            msg_id = int(new_msg_id)
            msg_info = self.message_id_map.get(msg_id)

            if not msg_info:
                return None

            # 检查时间限制
            time_diff = (datetime.now() - msg_info['timestamp']).total_seconds()
            if time_diff > self.time_limit:
                return None

            return msg_info
        except (ValueError, TypeError):
            return None

    def _check_user_limit(self, wxid: str, user_wxid: str) -> float:
        user_key = f"{wxid}_{user_wxid}"
        last_request = self.user_last_request.get(user_key, 0)
        current_time = time.time()
        elapsed = current_time - last_request
        self.user_last_request[user_key] = current_time

        if elapsed < self.cooldown:
            return self.cooldown - elapsed

        return 0

    @on_quote_message
    async def handle_quote(self, bot: WechatAPIClient, message: dict):
        """处理引用消息（回复消息）"""
        if not self.enable:
            return

        # 确保bot方法已被包装
        self._wrap_bot_methods(bot)

        content = str(message["Content"]).strip()
        wxid = message["FromWxid"]
        user_wxid = message["SenderWxid"]

        # 检查是否是撤回命令
        if content not in self.command:
            return

        # 获取被引用的消息信息
        quote_info = message.get("Quote", {})
        if not quote_info:
            await bot.send_text_message(wxid, "无法获取被引用的消息信息")
            return

        quoted_msg_id = quote_info.get("NewMsgId") or quote_info.get("Msgid")
        if not quoted_msg_id:
            await bot.send_text_message(wxid, "无法获取被引用消息的ID")
            return

        # 限流检查
        wait_time = self._check_user_limit(wxid, user_wxid)
        if wait_time > 0:
            await bot.send_at_message(wxid, f"请等待 {wait_time:.1f} 秒", [user_wxid])
            return

        try:
            # 通过消息ID查找要撤回的消息，如果找不到则等待重试
            msg_info = self.get_message_by_id(quoted_msg_id)
            if not msg_info:
                import asyncio
                await asyncio.sleep(2)  # 等待2秒，让消息监听器有时间处理

                # 重新查找
                msg_info = self.get_message_by_id(quoted_msg_id)
                if not msg_info:
                    await bot.send_text_message(wxid, f"该消息无法撤回或已超时\n消息ID: {quoted_msg_id}")
                    return

            # 对于非文本消息（client_msg_id是字符串），尝试转换为整数
            client_msg_id = msg_info['client_msg_id']
            if isinstance(client_msg_id, str):
                # 支持多种消息ID格式：
                # 1. 图片消息: "wxid_xxx_timestamp"
                # 2. 应用消息: "群ID@chatroom_timestamp" 或 "wxid_xxx_timestamp"
                # 3. 其他消息: 尝试提取最后的数字部分
                try:
                    # 提取最后一个下划线后的数字部分
                    if '_' in client_msg_id:
                        parts = client_msg_id.split('_')
                        timestamp_part = parts[-1]
                        client_msg_id = int(timestamp_part)
                    else:
                        # 如果没有下划线，尝试直接转换为整数
                        client_msg_id = int(client_msg_id)
                except (ValueError, IndexError) as e:
                    logger.warning(f"[{self.plugin_name}] 消息ClientMsgId转换失败: {e}")
                    await bot.send_text_message(wxid, "消息撤回失败：ID转换错误")
                    return

            success = await bot.revoke_message(
                wxid,
                client_msg_id,
                msg_info['create_time'],
                msg_info['new_msg_id']
            )

            if success:
                # 从存储中移除已撤回的消息
                if wxid in self.sent_messages:
                    messages = list(self.sent_messages[wxid])
                    # 找到并移除对应的消息
                    for i, stored_msg in enumerate(messages):
                        if stored_msg['new_msg_id'] == msg_info['new_msg_id']:
                            messages.pop(i)
                            break
                    self.sent_messages[wxid] = deque(messages, maxlen=self.max_count)

                # 从消息ID映射中移除
                if msg_info['new_msg_id'] in self.message_id_map:
                    del self.message_id_map[msg_info['new_msg_id']]

                # 撤回成功时不发送提示消息
            else:
                await bot.send_text_message(wxid, "撤回失败")

        except Exception as e:
            logger.error(f"[{self.plugin_name}] 引用撤回异常: {e}")
            logger.error(f"[{self.plugin_name}] 异常详情: {traceback.format_exc()}")
            try:
                await bot.send_text_message(wxid, "处理失败")
            except Exception as send_err:
                logger.error(f"[{self.plugin_name}] 发送错误消息失败: {send_err}")