import json
import re
import tomllib
import tomli_w
import traceback
import asyncio
import os
import time
import uuid
import hmac
import hashlib
import base64
from typing import Optional, Dict, Any
from datetime import datetime, timedelta
from collections import defaultdict
from pathlib import Path

import httpx
from loguru import logger

from WechatAPI import WechatAPIClient
from utils.decorators import *
from utils.plugin_base import PluginBase
from utils.temp_file_manager import cleanup_file
from plugins.text2card_project.text_card_service import TextCardService, TextCardError

class YaoyaoPlugin(PluginBase):
    """瑶瑶群管插件"""
    
    description = "瑶瑶 - 智能群聊管家"
    author = "Claude"
    version = "1.0.0"
    plugin_name = "YaoyaoPlugin"

    def __init__(self):
        super().__init__()
        
        # 初始化临时目录
        self._temp_dir = Path("temp/yaoyao")
        self._temp_dir.mkdir(parents=True, exist_ok=True)
        
        # 频率限制相关
        self.ask_records = defaultdict(list)
        self.warned_users = set()
        self.ask_limit = 5  # 10分钟内最多允许提问5次
        self.ask_window = 600  # 10分钟的时间窗口
        
        # 读取配置
        config = {}
        try:
            config_path = f"plugins/{self.plugin_name}/config.toml"
            if not os.path.exists(config_path):
                logger.warning(f"[{self.plugin_name}] 配置文件不存在，将使用默认配置")
            else:
                with open(config_path, "rb") as f:
                    plugin_config = tomllib.load(f)
                config = plugin_config.get(self.plugin_name, {})
        except Exception as e:
            logger.error(f"[{self.plugin_name}] 配置文件读取失败: {str(e)}")
            config = {} 
        
        # 基本配置
        self.enable = config.get("enable", True)
        self.command = config.get("command", ["瑶瑶", "yy"])
        self.command_format = config.get("command-format", "")
        
        # TTS配置
        self.tts_url = config.get("tts-url", "http://www.yx520.ltd/API/wzzyy/zmp3.php")
        self.tts_voice = config.get("tts-voice", "318")
        
        # API配置
        self.api_url = config.get("api-url", "https://agents.vivo.com.cn/gpts-v2-api/chat/v1/completions/web/stream/public")
        self.app_id = config.get("app-id", "4483725646")
        self.app_name = config.get("app-name", "瑶妹")
        self.agent_id = config.get("agent-id", 77382026)
        self.uid = config.get("uid", "d41d8cd98f00b204e9800998ecf8427e")
        
        # 登录凭证
        self.csrf_token = config.get("csrf-token", "")
        self.cookies = config.get("cookies", "")
        
        # 登录凭证自动更新配置
        self.username = config.get("username", "")
        self.password = config.get("password", "")
        self.auto_refresh = config.get("auto-refresh", True)
        self.last_refresh_time = datetime.now()
        self.refresh_interval = timedelta(hours=12)
        
        # 限流配置
        rate_limit_config = config.get("rate_limit", {})
        self.tokens_per_second = rate_limit_config.get("tokens_per_second", 0.1)
        self.bucket_size = rate_limit_config.get("bucket_size", 3)
        self.tokens = self.bucket_size
        self.last_token_time = time.time()

        # 私聊黑名单配置
        self.private_blacklist = config.get("private-blacklist", [])
        logger.info(f"[{self.plugin_name}] 私聊黑名单用户: {self.private_blacklist}")

        # 加载管理员配置
        self.admins = []
        try:
            with open("main_config.toml", "rb") as f:
                main_config = tomllib.load(f)
                self.admins = main_config.get("XYBot", {}).get("admins", [])
                logger.info(f"[{self.plugin_name}] 已加载管理员列表: {self.admins}")
        except Exception as e:
            logger.error(f"[{self.plugin_name}] 加载管理员配置失败: {e}")
            self.admins = []

        # 初始化TextCardService
        self.text_card = TextCardService()
        
        # 会话管理
        self._sessions = {}
        self._chat_sessions = {}
        self.session_timeout = timedelta(minutes=5)
        
        # 启动会话清理任务
        asyncio.create_task(self._clean_expired_sessions()) 

    async def _clean_expired_sessions(self):
        """定期清理过期会话"""
        while True:
            try:
                current_time = datetime.now()
                expired_sessions = []
                
                for wxid, session in self._chat_sessions.items():
                    if current_time - session["last_active"] > self.session_timeout:
                        expired_sessions.append(wxid)
                        
                if expired_sessions:
                    for wxid in expired_sessions:
                        session = self._chat_sessions.pop(wxid)
                        try:
                            await session["bot"].send_text_message(
                                wxid,
                                "看起来你已经很久没说话啦～我先去休息了哦,需要我的时候随时叫我「瑶瑶」就好啦! 😴"
                            )
                        except:
                            pass

                        
            except Exception as e:
                logger.error(f"[{self.plugin_name}] 清理过期会话出错: {e}")
                
            await asyncio.sleep(60)

    def _update_session_activity(self, wxid: str):
        """更新会话活跃时间"""
        if wxid in self._chat_sessions:
            self._chat_sessions[wxid]["last_active"] = datetime.now()

    def _is_admin(self, wxid: str) -> bool:
        """检查用户是否是管理员"""
        return wxid in self.admins

    def _save_config(self):
        """保存配置到文件"""
        try:
            config_path = f"plugins/{self.plugin_name}/config.toml"
            with open(config_path, "rb") as f:
                config = tomllib.load(f)

            # 更新私聊黑名单
            config[self.plugin_name]["private-blacklist"] = self.private_blacklist

            with open(config_path, "w", encoding="utf-8") as f:
                tomli_w.dump(config, f)

            logger.info(f"[{self.plugin_name}] 配置已保存")
        except Exception as e:
            logger.error(f"[{self.plugin_name}] 保存配置失败: {e}")

    async def _handle_admin_commands(self, bot: WechatAPIClient, wxid: str, content: str) -> bool:
        """处理管理员命令"""
        try:
            # 添加用户到私聊黑名单
            if content.startswith("瑶瑶黑名单添加 "):
                target_wxid = content[8:].strip()
                if target_wxid:
                    if target_wxid not in self.private_blacklist:
                        self.private_blacklist.append(target_wxid)
                        self._save_config()
                        await bot.send_text_message(
                            wxid,
                            f"✅ 已将用户 {target_wxid} 添加到私聊黑名单"
                        )
                    else:
                        await bot.send_text_message(
                            wxid,
                            f"⚠️ 用户 {target_wxid} 已在私聊黑名单中"
                        )
                else:
                    await bot.send_text_message(
                        wxid,
                        "❌ 请提供有效的用户wxid\n用法: 瑶瑶黑名单添加 wxid_xxx"
                    )
                return True

            # 从私聊黑名单移除用户
            elif content.startswith("瑶瑶黑名单移除 "):
                target_wxid = content[8:].strip()
                if target_wxid:
                    if target_wxid in self.private_blacklist:
                        self.private_blacklist.remove(target_wxid)
                        self._save_config()
                        await bot.send_text_message(
                            wxid,
                            f"✅ 已将用户 {target_wxid} 从私聊黑名单移除"
                        )
                    else:
                        await bot.send_text_message(
                            wxid,
                            f"⚠️ 用户 {target_wxid} 不在私聊黑名单中"
                        )
                else:
                    await bot.send_text_message(
                        wxid,
                        "❌ 请提供有效的用户wxid\n用法: 瑶瑶黑名单移除 wxid_xxx"
                    )
                return True

            # 查看私聊黑名单
            elif content == "瑶瑶黑名单查看":
                if self.private_blacklist:
                    blacklist_text = "\n".join([f"• {wxid}" for wxid in self.private_blacklist])
                    await bot.send_text_message(
                        wxid,
                        f"📋 当前私聊黑名单用户:\n{blacklist_text}"
                    )
                else:
                    await bot.send_text_message(
                        wxid,
                        "📋 私聊黑名单为空"
                    )
                return True

            # 显示管理员帮助
            elif content == "瑶瑶管理帮助":
                help_text = """🔧 瑶瑶管理员命令:

📋 黑名单管理:
• 瑶瑶黑名单添加 wxid_xxx - 添加用户到私聊黑名单
• 瑶瑶黑名单移除 wxid_xxx - 从私聊黑名单移除用户
• 瑶瑶黑名单查看 - 查看当前黑名单

❓ 帮助:
• 瑶瑶管理帮助 - 显示此帮助信息

注意: 黑名单中的用户无法在私聊中触发瑶瑶回复"""
                await bot.send_text_message(wxid, help_text)
                return True

        except Exception as e:
            logger.error(f"[{self.plugin_name}] 处理管理员命令失败: {e}")
            await bot.send_text_message(wxid, f"❌ 处理命令时出错: {str(e)}")
            return True

        return False

    async def _process_stream(self, response: httpx.Response) -> str:
        """处理SSE流式响应"""
        buffer = []
        current_event = None
        try:
            async for line in response.aiter_lines():
                if line:
                    line = line.strip()
                    
                    # 检查登录过期
                    try:
                        error_data = json.loads(line)
                        if error_data.get("code") == 20002:
                            raise ValueError("登录信息已过期，请联系管理员更新登录信息")
                    except json.JSONDecodeError:
                        pass
                    
                    if line.startswith('event:'):
                        current_event = line[6:].strip()
                        continue
                        
                    if line.startswith('data:'):
                        data = line[5:].strip()
                        
                        if data == "[DONE]":
                            break
                            
                        if current_event == 'error':
                            logger.error(f"[{self.plugin_name}] 服务器返回错误: {data}")
                            raise ValueError(f"服务器返回错误: {data}")
                            
                        try:
                            json_data = json.loads(data)
                            message = json_data.get("message", "")
                            if message and message not in buffer and not json_data.get("end", False):
                                buffer.append(message)
                        except json.JSONDecodeError as e:
                            logger.warning(f"[{self.plugin_name}] JSON解析失败: {e}, 原始数据: {data}")
                            continue
                            
        except Exception as e:
            logger.error(f"[{self.plugin_name}] 处理响应流时出错: {e}")
            raise
            
        result = buffer[-1] if buffer else ""
        return result 

    async def _generate_card(self, text: str) -> Optional[str]:
        """生成图片卡片"""
        temp_path = f"{self._temp_dir}/card_{int(time.time())}.png"
        default_header = "data/default picture/yaoyao.jpg"
        
        try:
            if len(text) <= 200:
                await self.text_card.generate_card(
                    text=text,
                    title_image=default_header,
                    output_path=temp_path,
                    width=600,
                    is_dark=False,
                    font_size=18,
                    line_spacing=1.5
                )
            else:
                await self.text_card.generate_card(
                    text=text,
                    title_image=default_header,
                    output_path=temp_path,
                    width=720,
                    is_dark=False,
                    font_size=16,
                    line_spacing=1.2
                )
            
            # 延迟清理临时文件 - 使用统一管理器
            cleanup_file(temp_path, delay_seconds=60)
            
            return temp_path
            
        except Exception as e:
            logger.error(f"[{self.plugin_name}] 生成图片失败: {e}")
            cleanup_file(temp_path)
            return None

    async def _text_to_speech(self, text: str) -> Optional[bytes]:
        """将文本转换为语音"""
        try:
            params = {
                "text": text,
                "voice": self.tts_voice
            }
            
            async with httpx.AsyncClient() as client:
                response = await client.get(self.tts_url, params=params, timeout=30.0)
                if response.status_code == 200:
                    audio_data = response.content
                    if len(audio_data) < 100:
                        logger.warning(f"[{self.plugin_name}] TTS返回的数据太小，可能无效")
                        return None
                    return audio_data
                else:
                    logger.warning(f"[{self.plugin_name}] 语音生成失败: {response.status_code}")
                    return None
        except Exception as e:
            logger.error(f"[{self.plugin_name}] 生成语音失败: {e}")
            return None 

    async def _process_short_text(self, bot: WechatAPIClient, wxid: str, text: str) -> None:
        """处理短文本消息"""
        try:
            voice_data = await self._text_to_speech(text)
            if voice_data:
                await bot.send_voice_message(wxid, voice_data, 'mp3')
                return
        except Exception as e:
            logger.error(f"[{self.plugin_name}] 发送语音失败: {e}")
        
        try:
            image_path = await self._generate_card(text)
            if image_path and os.path.exists(image_path):
                with open(image_path, 'rb') as f:
                    image_data = f.read()
                await bot.send_image_message(wxid, image_data)
                return
        except Exception as e:
            logger.error(f"[{self.plugin_name}] 生成图片失败: {e}")
        finally:
            if image_path:
                cleanup_file(image_path)
        
        await bot.send_text_message(wxid, text)

    async def _process_long_text(self, bot: WechatAPIClient, wxid: str, text: str) -> None:
        """处理长文本消息"""
        try:
            image_path = await self._generate_card(text)
            if image_path and os.path.exists(image_path):
                with open(image_path, 'rb') as f:
                    image_data = f.read()
                await bot.send_image_message(wxid, image_data)
                return
        except Exception as e:
            logger.error(f"[{self.plugin_name}] 生成图片失败: {e}")
        finally:
            if image_path:
                cleanup_file(image_path)
        
        segments = [text[i:i+200] for i in range(0, len(text), 200)]
        for i, segment in enumerate(segments[:5]):
            await bot.send_text_message(wxid, segment)
            if i == 4 and len(segments) > 5:
                await bot.send_text_message(wxid, "...(内容过长，已省略剩余部分)")

    async def _refresh_login(self) -> bool:
        """刷新登录凭证"""
        try:
            async with httpx.AsyncClient() as client:
                response = await client.get("https://id.vivo.com.cn/")
                if response.status_code != 200:
                    return False
                
                cookies = response.cookies
                
                login_data = {
                    "username": self.username,
                    "password": self.password,
                    "client_id": "1",
                    "redirect_uri": "https://agents.vivo.com.cn/",
                }
                
                response = await client.post(
                    "https://id.vivo.com.cn/api/auth/login",
                    json=login_data,
                    cookies=cookies
                )
                
                if response.status_code != 200:
                    return False
                
                login_result = response.json()
                if not login_result.get("success"):
                    logger.error(f"[{self.plugin_name}] 登录失败: {login_result.get('message')}")
                    return False
                
                csrf_token = login_result.get("data", {}).get("csrf_token")
                if not csrf_token:
                    return False
                
            self.csrf_token = csrf_token
            self.cookies = "; ".join([f"{k}={v}" for k, v in response.cookies.items()])
            
            with open(f"plugins/{self.plugin_name}/config.toml", "rb") as f:
                config = tomllib.load(f)
            
            config[self.plugin_name]["csrf-token"] = self.csrf_token
            config[self.plugin_name]["cookies"] = self.cookies
            
            with open(f"plugins/{self.plugin_name}/config.toml", "w", encoding="utf-8") as f:
                tomli_w.dump(config, f)
            

            self.last_refresh_time = datetime.now()
            return True
            
        except Exception as e:
            logger.error(f"[{self.plugin_name}] 刷新登录凭证失败: {e}")
            return False 

    async def _check_and_refresh_login(self) -> bool:
        """检查并刷新登录状态"""
        if not self.auto_refresh:
            return True
            
        if datetime.now() - self.last_refresh_time < self.refresh_interval:
            return True
            
        if not self.username or not self.password:
            logger.warning(f"[{self.plugin_name}] 未配置账号密码，无法自动刷新登录凭证")
            return True
            
        return await self._refresh_login()

    async def _process_message(self, bot: WechatAPIClient, wxid: str, content: str) -> None:
        """处理消息"""
        try:
            if not await self._check_and_refresh_login():
                await bot.send_text_message(
                    wxid,
                    "抱歉，我现在遇到了一些问题，请稍后再试~ 🔄"
                )
                return
                
            session_id = self._sessions.get(wxid)
            if not session_id:
                session_id = str(uuid.uuid4())
                self._sessions[wxid] = session_id
            
            request_data = {
                "sessionId": session_id,
                "requestId": str(uuid.uuid4()),
                "appName": self.app_name,
                "appId": self.agent_id,
                "prompt": content,
                "fileInfoList": []
            }
            
            headers = self._generate_headers()
            
            async with httpx.AsyncClient() as client:
                async with client.stream(
                    "POST",
                    self.api_url,
                    headers=headers,
                    json=request_data,
                    timeout=60.0
                ) as response:
                    if response.status_code != 200:
                        error_text = await response.aread()
                        logger.error(f"[{self.plugin_name}] API请求失败: 状态码={response.status_code}, 响应内容={error_text}")
                        raise httpx.HTTPError(f"API请求失败: {response.status_code}")
                    
                    text = await self._process_stream(response)
                    if not text:
                        raise ValueError("抱歉，我暂时回答不了，等会再试试")
                    

                    
                    if len(text) <= 200:
                        await self._process_short_text(bot, wxid, text)
                    else:
                        await self._process_long_text(bot, wxid, text)
                    
        except ValueError as e:
            if "登录信息已过期" in str(e):
                logger.error(f"[{self.plugin_name}] 登录已过期")
                if wxid in self._chat_sessions:
                    self._chat_sessions.pop(wxid)
                await bot.send_text_message(
                    wxid,
                    "抱歉，我需要休息一下下，等会再来找我哦~ 🔄"
                )
            else:
                logger.error(f"[{self.plugin_name}] 处理失败: {str(e)}")
                await bot.send_text_message(wxid, f"❌{str(e)}")
        except httpx.HTTPError as e:
            logger.error(f"[{self.plugin_name}] 网络请求错误: {e}")
            await bot.send_text_message(wxid, "网络有点问题，等会再试试哦~ 🌐")
        except Exception as e:
            logger.error(f"[{self.plugin_name}] 处理失败: {str(e)}")
            logger.error(f"[{self.plugin_name}] 错误详情: {traceback.format_exc()}")
            await bot.send_text_message(wxid, "抱歉，我遇到了一些问题，等会再试试~ 🔧")

    def _generate_headers(self) -> dict:
        """生成请求头"""
        timestamp = str(int(time.time()))
        nonce = base64.b64encode(os.urandom(6)).decode('utf-8')[:8]
        trace_id = str(uuid.uuid4())
        
        headers = {
            "X-AI-GATEWAY-TIMESTAMP": timestamp,
            "X-CSRF-TOKEN": self.csrf_token,
            "X-AI-GATEWAY-SIGNED-HEADERS": "x-ai-gateway-app-id;x-ai-gateway-timestamp;x-ai-gateway-nonce",
            "X-AI-GATEWAY-NONCE": nonce,
            "Content-Type": "application/json",
            "traceId": trace_id,
            "X-AI-GATEWAY-APP-ID": self.app_id,
            "X-uid": self.uid,
            "platform": "web",
            "provider": "chatgpt",
            "Accept": "*/*",
            "Origin": "https://agents.vivo.com.cn",
            "X-Requested-With": "mark.via",
            "Sec-Fetch-Site": "same-origin",
            "Sec-Fetch-Mode": "cors", 
            "Sec-Fetch-Dest": "empty",
            "Referer": f"https://agents.vivo.com.cn/agent/{self.agent_id}/dialog?vcodePage=1&categoryId=-1&belong=AiAppSquare",
            "Accept-Encoding": "gzip, deflate",
            "Accept-Language": "zh-CN,zh;q=0.9,en-US;q=0.8,en;q=0.7",
            "Cookie": self.cookies
        }
        
        signed_content = f"x-ai-gateway-app-id={headers['X-AI-GATEWAY-APP-ID']}&x-ai-gateway-timestamp={timestamp}&x-ai-gateway-nonce={nonce}"
        signature = base64.b64encode(
            hmac.new(
                self.csrf_token.encode('utf-8'),
                signed_content.encode('utf-8'),
                hashlib.sha256
            ).digest()
        ).decode('utf-8')
        
        headers["X-AI-GATEWAY-SIGNATURE"] = signature
        
        return headers

    @on_text_message
    async def handle_text(self, bot: WechatAPIClient, message: dict):
        """处理文本消息"""
        if not self.enable:
            return

        wxid = message["FromWxid"]
        sender_wxid = message["SenderWxid"]
        content = message["Content"].strip()

        # 忽略机器人自己发送的消息（防止自己触发自己）
        if sender_wxid == bot.wxid:
            logger.debug(f"[{self.plugin_name}] 忽略机器人自己发送的消息")
            return

        # 处理私聊黑名单检查（优先级最高）
        if not "@chatroom" in wxid:
            # 对于公众号消息，检查FromWxid（公众号ID）
            # 对于普通私聊，检查SenderWxid（发送者ID）
            check_wxid = wxid if wxid.startswith('gh_') else sender_wxid
            if check_wxid in self.private_blacklist:
                logger.debug(f"[{self.plugin_name}] 用户/公众号 {check_wxid} 在私聊黑名单中，忽略消息")
                return

        # 处理管理员命令
        if self._is_admin(sender_wxid):
            if await self._handle_admin_commands(bot, wxid, content):
                return

        if wxid in self._chat_sessions:
            if content.lower() in ["瑶瑶再见", "瑶瑶拜拜", "瑶瑶退下"]:
                self._chat_sessions.pop(wxid)
                await bot.send_text_message(
                    wxid,
                    "好的，瑶瑶去休息啦～下次见！👋"
                )
                return

            if not await self._check_frequency(bot, wxid):
                return

            self._update_session_activity(wxid)
            await self._process_message(bot, wxid, content)
            return

        # 处理私聊消息
        if not "@chatroom" in wxid:
            if not await self._check_frequency(bot, wxid):
                return
            await self._process_message(bot, wxid, content)
            return
            
        if content in self.command:
            session_data = {"bot": bot, "last_active": datetime.now()}
            self._chat_sessions[wxid] = session_data
            await bot.send_text_message(wxid, self.command_format)
            return
            
        if not content.startswith(tuple(self.command)):
            return

        if not await self._check_frequency(bot, wxid):
            return
            
        question = re.sub(f"^({'|'.join(self.command)})", "", content).strip()
        if not question:
            await bot.send_text_message(
                wxid,
                "有什么我可以帮你的吗? 😊"
            )
            return
            

        await self._process_message(bot, wxid, question)

    async def _check_frequency(self, bot: WechatAPIClient, wxid: str) -> bool:
        """检查用户提问频率并处理提醒逻辑"""
        current_time = time.time()
        current_records = self.ask_records[wxid]
        current_records = [t for t in current_records if current_time - t <= self.ask_window]
        self.ask_records[wxid] = current_records
        current_records.append(current_time)

        if len(current_records) > self.ask_limit and wxid not in self.warned_users:
            self.warned_users.add(wxid)
            await bot.send_text_message(
                wxid,
                "⚠️您提问太频繁啦~建议休息一下再来哦\n"
                f"（10分钟内最多提问{self.ask_limit}次）"
            )
            return False

        if len(current_records) > self.ask_limit:
            return False

        if len(current_records) == 1:
            self.warned_users.discard(wxid)

        return True

    async def on_disable(self):
        """插件禁用时清理资源"""
        await super().on_disable()
        
        for wxid, session in self._chat_sessions.items():
            try:
                await session["bot"].send_text_message(
                    wxid,
                    "抱歉呀,我需要去休息一下下,很快就会回来的! 🔄"
                )
            except:
                pass
                
        self._chat_sessions.clear()
        
        # 临时文件清理现在由统一的TempFileManager处理