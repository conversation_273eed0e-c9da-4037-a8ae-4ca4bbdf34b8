<?xml version="1.0" encoding="UTF-8"?>
<project version="4">
  <component name="AugmentWebviewStateStore">
    <option name="stateMap">
      <map>
        <entry key="CHAT_STATE" value="{&quot;currentConversationId&quot;:&quot;e9d08e26-5325-4a73-9711-d0a33865194d&quot;,&quot;conversations&quot;:{&quot;e9d08e26-5325-4a73-9711-d0a33865194d&quot;:{&quot;id&quot;:&quot;e9d08e26-5325-4a73-9711-d0a33865194d&quot;,&quot;createdAtIso&quot;:&quot;2025-07-29T03:21:57.450Z&quot;,&quot;lastInteractedAtIso&quot;:&quot;2025-07-29T03:26:54.351Z&quot;,&quot;chatHistory&quot;:[{&quot;status&quot;:&quot;success&quot;,&quot;request_id&quot;:&quot;88ab0cba-634d-495b-a181-d69bed066419&quot;,&quot;request_message&quot;:&quot;具体介绍这个插件&quot;,&quot;rich_text_json_repr&quot;:{&quot;type&quot;:&quot;doc&quot;,&quot;content&quot;:[{&quot;type&quot;:&quot;paragraph&quot;,&quot;content&quot;:[{&quot;type&quot;:&quot;text&quot;,&quot;text&quot;:&quot;具体介绍这个插件&quot;}]}]},&quot;mentioned_items&quot;:[],&quot;structured_output_nodes&quot;:[{&quot;id&quot;:0,&quot;type&quot;:0,&quot;content&quot;:&quot;&quot;},{&quot;id&quot;:1,&quot;type&quot;:2,&quot;content&quot;:&quot;\n\n---\n\n*<NAME_EMAIL> has been suspended. To continue, [purchase a subscription](https://app.augmentcode.com/account).*&quot;}],&quot;seen_state&quot;:&quot;seen&quot;,&quot;structured_request_nodes&quot;:[{&quot;id&quot;:0,&quot;type&quot;:0,&quot;text_node&quot;:{&quot;content&quot;:&quot;具体介绍这个插件&quot;}},{&quot;id&quot;:1,&quot;type&quot;:4,&quot;ide_state_node&quot;:{&quot;workspaceFolders&quot;:[{&quot;repositoryRoot&quot;:&quot;C:/XYBotV2&quot;,&quot;folderRoot&quot;:&quot;C:/XYBotV2&quot;}],&quot;workspaceFoldersUnchanged&quot;:false}}],&quot;timestamp&quot;:&quot;2025-07-29T03:26:54.375Z&quot;,&quot;response_text&quot;:&quot;\n\n---\n\n*<NAME_EMAIL> has been suspended. To continue, [purchase a subscription](https://app.augmentcode.com/account).*&quot;,&quot;workspace_file_chunks&quot;:[]}],&quot;feedbackStates&quot;:{&quot;temp-fe-a4419a88-cd6d-4ccd-8490-f710241bb7a8&quot;:{&quot;selectedRating&quot;:0,&quot;feedbackNote&quot;:&quot;&quot;}},&quot;toolUseStates&quot;:{},&quot;draftExchange&quot;:{&quot;request_message&quot;:&quot;&quot;,&quot;rich_text_json_repr&quot;:{&quot;type&quot;:&quot;doc&quot;,&quot;content&quot;:[{&quot;type&quot;:&quot;paragraph&quot;}]},&quot;status&quot;:&quot;draft&quot;},&quot;requestIds&quot;:[],&quot;isPinned&quot;:false,&quot;isShareable&quot;:true,&quot;extraData&quot;:{&quot;hasDirtyEdits&quot;:false,&quot;isAgentConversation&quot;:true,&quot;baselineTimestamp&quot;:0},&quot;personaType&quot;:0,&quot;rootTaskUuid&quot;:&quot;0036ba00-2aff-4090-9eaa-5e31fc2c5d30&quot;}},&quot;agentExecutionMode&quot;:&quot;manual&quot;,&quot;isPanelCollapsed&quot;:true,&quot;displayedAnnouncements&quot;:[]}" />
      </map>
    </option>
  </component>
</project>