2025-07-20 02:42:48 | SUCCESS | 读取主设置成功
2025-07-20 02:42:48 | DEBUG | 最终使用的 Redis 主机地址: 127.0.0.1
2025-07-20 02:42:49 | INFO | 2025/07/20 02:42:49 GetRedisAddr: 127.0.0.1:6379
2025-07-20 02:42:49 | INFO | 2025/07/20 02:42:49 Redis连接成功 | 地址:127.0.0.1:6379 DB:0
2025-07-20 02:42:49 | INFO | 2025/07/20 02:42:49 Server start at :9000
2025-07-20 02:42:49 | SUCCESS | WechatAPI服务已启动
2025-07-20 02:42:50 | SUCCESS | 获取到登录uuid: YfWxw5PQlueqteZ129RR
2025-07-20 02:42:50 | INFO | 等待登录中，过期倒计时：239
2025-07-20 02:42:57 | INFO | 登录账号信息: wxid: wxid_4usgcju5ey9q29  昵称: 瑶瑶  微信号: Today-KFC  手机号: None
2025-07-20 02:42:57 | INFO | 登录设备信息: device_name: <PERSON>'s Pad  device_id: 49d050a7e94d1ecc40790d74b1c4369f
2025-07-20 02:42:57 | SUCCESS | 登录成功
2025-07-20 02:42:57 | SUCCESS | 已开启自动心跳
2025-07-20 02:42:57 | INFO | 成功加载表情映射文件，共 441 条记录
2025-07-20 02:42:57 | SUCCESS | 数据库初始化成功
2025-07-20 02:42:57 | SUCCESS | 定时任务已启动
2025-07-20 02:42:57 | ERROR | 加载插件模块 admin_point 时发生错误: Traceback (most recent call last):
  File "C:\XYBotV2\utils\plugin_manager.py", line 102, in load_plugins_from_directory
    module = importlib.import_module(f"plugins.{module_name}")
             ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "C:\Program Files\Python311\Lib\importlib\__init__.py", line 126, in import_module
    return _bootstrap._gcd_import(name[level:], package, level)
           ^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^^
  File "<frozen importlib._bootstrap>", line 1204, in _gcd_import
  File "<frozen importlib._bootstrap>", line 1176, in _find_and_load
  File "<frozen importlib._bootstrap>", line 1126, in _find_and_load_unlocked
  File "<frozen importlib._bootstrap>", line 241, in _call_with_frames_removed
  File "<frozen importlib._bootstrap>", line 1204, in _gcd_import
  File "<frozen importlib._bootstrap>", line 1176, in _find_and_load
  File "<frozen importlib._bootstrap>", line 1147, in _find_and_load_unlocked
  File "<frozen importlib._bootstrap>", line 690, in _load_unlocked
  File "<frozen importlib._bootstrap_external>", line 940, in exec_module
  File "<frozen importlib._bootstrap>", line 241, in _call_with_frames_removed
  File "C:\XYBotV2\plugins\__init__.py", line 10, in <module>
    from plugins.guess_number import GuessNumber
ModuleNotFoundError: No module named 'plugins.guess_number'

2025-07-20 02:42:57 | SUCCESS | 已加载插件: False
2025-07-20 02:42:57 | INFO | 处理堆积消息中
2025-07-20 02:43:01 | DEBUG | 接受到 21 条消息
2025-07-20 02:43:03 | DEBUG | 接受到 1 条消息
2025-07-20 02:43:04 | SUCCESS | 处理堆积消息完毕
2025-07-20 02:43:04 | SUCCESS | 开始处理消息
2025-07-20 02:43:13 | DEBUG | 收到消息: {'MsgId': 602105410, 'FromUserName': {'string': 'weixin'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 10002, 'Content': {'string': '\n\t\t\t\t<sysmsg type="ClientCheckGetExtInfo">\n\t\t\t\t\t<ClientCheckGetExtInfo>\n\t\t\t\t\t\t<ReportContext>539033600</ReportContext>\n\t\t\t\t\t\t<Basic>0</Basic>\n                        <Cellular>1</Cellular>\n\t\t\t\t\t</ClientCheckGetExtInfo>\n\t\t\t\t</sysmsg>\n\t\t\t'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1752950597, 'MsgSource': '<msgsource>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'NewMsgId': 5271308396538316233, 'MsgSeq': 871384006}
2025-07-20 02:43:13 | DEBUG | 系统消息类型: ClientCheckGetExtInfo
2025-07-20 02:45:55 | WARNING | 获取新消息失败 [WinError 64] 指定的网络名不再可用。
2025-07-20 02:46:34 | DEBUG | 收到消息: {'MsgId': 965570456, 'FromUserName': {'string': '55878994168@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 1, 'Content': {'string': 'wxid_ubbh6q832tcs21:\n签到'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1752950797, 'MsgSource': '<msgsource>\n\t<pua>1</pua>\n\t<eggIncluded>1</eggIncluded>\n\t<silence>0</silence>\n\t<membercount>3</membercount>\n\t<signature>N0_V1_hcgIsYfF|v1_VzBoOWXi</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'PushContent': '郭子 : 签到', 'NewMsgId': 8644429162469480258, 'MsgSeq': 871384007}
2025-07-20 02:46:34 | INFO | 收到文本消息: 消息ID:965570456 来自:55878994168@chatroom 发送人:wxid_ubbh6q832tcs21 @:[] 内容:签到
2025-07-20 02:46:43 | DEBUG | 收到消息: {'MsgId': 1429719595, 'FromUserName': {'string': '55878994168@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 1, 'Content': {'string': 'wxid_ubbh6q832tcs21:\n状态'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1752950807, 'MsgSource': '<msgsource>\n\t<pua>1</pua>\n\t<eggIncluded>1</eggIncluded>\n\t<silence>0</silence>\n\t<membercount>3</membercount>\n\t<signature>N0_V1_foGqK+Dd|v1_1hvZe0SN</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'PushContent': '郭子 : 状态', 'NewMsgId': 2522202220053363066, 'MsgSeq': 871384008}
2025-07-20 02:46:43 | INFO | 收到文本消息: 消息ID:1429719595 来自:55878994168@chatroom 发送人:wxid_ubbh6q832tcs21 @:[] 内容:状态
2025-07-20 02:46:51 | DEBUG | 收到消息: {'MsgId': 1103743214, 'FromUserName': {'string': '55878994168@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 1, 'Content': {'string': 'wxid_ubbh6q832tcs21:\n123'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1752950815, 'MsgSource': '<msgsource>\n\t<pua>1</pua>\n\t<eggIncluded>1</eggIncluded>\n\t<silence>0</silence>\n\t<membercount>3</membercount>\n\t<signature>N0_V1_ujSsuBRJ|v1_EM1zp3S2</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'PushContent': '郭子 : 123', 'NewMsgId': 4243306880962015065, 'MsgSeq': 871384009}
2025-07-20 02:46:51 | INFO | 收到文本消息: 消息ID:1103743214 来自:55878994168@chatroom 发送人:wxid_ubbh6q832tcs21 @:[] 内容:123
2025-07-20 02:46:56 | DEBUG | 收到消息: {'MsgId': 870512535, 'FromUserName': {'string': '55878994168@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 1, 'Content': {'string': 'wxid_ubbh6q832tcs21:\n豆包'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1752950819, 'MsgSource': '<msgsource>\n\t<pua>1</pua>\n\t<eggIncluded>1</eggIncluded>\n\t<silence>0</silence>\n\t<membercount>3</membercount>\n\t<signature>N0_V1_fsEue2a1|v1_EoJfJUm6</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'PushContent': '郭子 : 豆包', 'NewMsgId': 7398117767460664142, 'MsgSeq': 871384010}
2025-07-20 02:46:56 | INFO | 收到文本消息: 消息ID:870512535 来自:55878994168@chatroom 发送人:wxid_ubbh6q832tcs21 @:[] 内容:豆包
2025-07-20 02:47:07 | DEBUG | 收到消息: {'MsgId': 1253629681, 'FromUserName': {'string': '55878994168@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 1, 'Content': {'string': 'wxid_ubbh6q832tcs21:\n菜单'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1752950830, 'MsgSource': '<msgsource>\n\t<pua>1</pua>\n\t<eggIncluded>1</eggIncluded>\n\t<silence>0</silence>\n\t<membercount>3</membercount>\n\t<signature>N0_V1_DaLjW5G2|v1_uMcriWPl</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'PushContent': '郭子 : 菜单', 'NewMsgId': 7189172128025595046, 'MsgSeq': 871384011}
2025-07-20 02:47:07 | INFO | 收到文本消息: 消息ID:1253629681 来自:55878994168@chatroom 发送人:wxid_ubbh6q832tcs21 @:[] 内容:菜单
2025-07-20 02:47:27 | WARNING | 获取新消息失败 [WinError 64] 指定的网络名不再可用。
2025-07-20 02:49:26 | WARNING | 获取新消息失败 [WinError 64] 指定的网络名不再可用。
