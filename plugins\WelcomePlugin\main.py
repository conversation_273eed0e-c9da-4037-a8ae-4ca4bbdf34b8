import xml.etree.ElementTree as ET
from typing import List, Dict, Any, Optional
import random
import tomllib
import json
import asyncio
from loguru import logger
from urllib.parse import quote
import time
import httpx
import os
import hashlib
import re
from pathlib import Path

from utils.plugin_base import PluginBase
from utils.decorators import on_system_message, on_text_message
from WechatAPI import WechatAPIClient


class WelcomePlugin(PluginBase):
    """欢迎新成员插件"""
    
    description = "自动欢迎新加入群聊的成员，支持群聊独立配置和管理"
    author = "XYBot"
    version = "1.2.0"
    plugin_name = "WelcomePlugin"
    
    def __init__(self):
        super().__init__()
        
        # 初始化配置
        self.enable = True
        self.default_welcome_message = "@{nickname} 欢迎加入我们！"
        self.tts_url = "http://www.yx520.ltd/API/wzzyy/zmp3.php"
        self.tts_voice = "318"
        self.groups = {}
        
        # 添加用户昵称缓存，格式为 {group_id: {wxid: nickname}}
        self.user_nicknames = {}
        
        # 读取全局管理员列表
        self.global_admins = []
        try:
            # 尝试从各种可能的位置读取main_config.toml
            config_paths = [
                "main_config.toml",                       # 相对路径
                "../main_config.toml",                    # 上一级目录
                "C:/XYBotV2/main_config.toml",           # 绝对路径
                os.path.abspath("main_config.toml"),      # 绝对路径转换
                os.path.abspath("../main_config.toml")    # 上级目录的绝对路径
            ]
            
            # 遍历所有可能的路径
            for path in config_paths:
                if os.path.exists(path):
                    with open(path, "rb") as f:
                        main_config = tomllib.load(f)
                        if "admins" in main_config:
                            self.global_admins = main_config["admins"]
                            break
            
            # 如果还是没有找到管理员，直接硬编码['wxid_ubbh6q832tcs21', 'admin-wxid']
            if not self.global_admins:
                self.global_admins = ['wxid_ubbh6q832tcs21', 'admin-wxid']

                
        except Exception as e:
            logger.error(f"[WelcomePlugin] 读取全局管理员配置失败: {e}")
            # 出错时使用硬编码的管理员列表作为备用
            self.global_admins = ['wxid_ubbh6q832tcs21', 'admin-wxid']

        
        # 加载配置
        self._load_config()
        
        # 添加配置锁，防止并发读写
        self._config_lock = asyncio.Lock() 

    def _load_config(self) -> bool:
        """从文件加载配置"""
        try:
            old_config_path = "plugins/welcome_messages.json"
            config_path = "plugins/WelcomePlugin/welcome_messages.json"

            # 检查旧路径文件是否存在，如果存在则移动到新路径
            if os.path.exists(old_config_path) and not os.path.exists(config_path):
                os.makedirs(os.path.dirname(config_path), exist_ok=True)  # 确保目标目录存在
                import shutil
                shutil.move(old_config_path, config_path)
            
            if not os.path.exists(config_path):
                logger.warning("[WelcomePlugin] 欢迎词配置文件不存在，将创建新文件")
                self._save_config()
                return True
                
            # 读取文件内容前先确保文件存在且可读
            try:
                with open(config_path, "r", encoding="utf-8") as f:
                    config = json.load(f)
                    
                # 加载插件配置
                plugin_config = config.get("plugin_config", {})
                self.enable = plugin_config.get("enable", True)
                self.default_welcome_message = plugin_config.get("default_welcome_message", "@{nickname} 欢迎加入我们！")
                self.tts_url = plugin_config.get("tts_url", "http://www.yx520.ltd/API/wzzyy/zmp3.php")
                self.tts_voice = plugin_config.get("tts_voice", "318")
                
                # 加载群组配置
                self.groups = {}  # 完全清空当前群组配置
                self.groups = config.get("groups", {})
                
                return True
                
            except json.JSONDecodeError as e:
                logger.error(f"[WelcomePlugin] 配置文件格式错误: {e}")
                return False
                
        except Exception as e:
            logger.error(f"[WelcomePlugin] 加载欢迎插件配置失败: {e}")
            return False
            
    def _save_config(self) -> bool:
        """保存配置到文件"""
        try:
            config = {
                "plugin_config": {
                    "enable": self.enable,
                    "default_welcome_message": self.default_welcome_message,
                    "tts_url": self.tts_url,
                    "tts_voice": self.tts_voice
                },
                "groups": self.groups
            }
            

            
            config_path = "plugins/WelcomePlugin/welcome_messages.json"
            with open(config_path, "w", encoding="utf-8") as f:
                json.dump(config, f, ensure_ascii=False, indent=4)

            return True
            
        except Exception as e:
            logger.error(f"[WelcomePlugin] 保存欢迎插件配置失败: {e}")
            return False
            
    async def reload_config(self) -> bool:
        """重新加载配置(热重载)"""
        async with self._config_lock:
            try:
                # 保存当前配置的副本
                old_config = {
                    "enable": self.enable,
                    "default_welcome_message": self.default_welcome_message,
                    "tts_url": self.tts_url,
                    "tts_voice": self.tts_voice,
                    "groups": self.groups.copy()
                }
                
                # 保存当前管理员列表
                current_admins = self.global_admins.copy()
                
                # 尝试加载新配置
                if not self._load_config():
                    # 如果加载失败，恢复旧配置
                    self.enable = old_config["enable"]
                    self.default_welcome_message = old_config["default_welcome_message"]
                    self.tts_url = old_config["tts_url"]
                    self.tts_voice = old_config["tts_voice"]
                    self.groups = old_config["groups"]
                    return False
                
                # 确保不会丢失管理员列表
                if not self.global_admins:
                    self.global_admins = current_admins

                return True
                
            except Exception as e:
                logger.error(f"[WelcomePlugin] 重载配置失败: {e}")
                return False 

    def _is_group_admin(self, group_id: str, wxid: str) -> bool:
        """检查用户是否是指定群的管理员或全局管理员"""
        # 检查是否是全局管理员
        if wxid in self.global_admins:
            return True
        # 检查是否是群管理员
        if group_id in self.groups:
            if wxid in self.groups[group_id]["admins"]:
                return True

        return False
        
    def _get_group_config(self, group_id: str) -> dict:
        """获取群聊配置，如果没有则返回默认配置"""
        if group_id in self.groups:
            return self.groups[group_id]
        return {
            "admins": [],
            "welcome_message": self.default_welcome_message,
            "enable": self.enable
        }
            
    def _parse_member_info(self, root: ET.Element, link_name: str = "names") -> List[Dict[str, str]]:
        """解析新成员信息"""
        new_members = []
        try:
            names_link = root.find(f".//link[@name='{link_name}']")
            if names_link is not None:
                memberlist = names_link.find("memberlist")
                if memberlist is not None:
                    for member in memberlist.findall("member"):
                        username = member.find("username").text
                        nickname = member.find("nickname").text
                        new_members.append({
                            "username": username,
                            "nickname": nickname
                        })
        except Exception as e:
            logger.error(f"解析新成员信息失败: {e}")
        return new_members
        
    def _clean_content(self, content: str) -> str:
        """清理消息内容，去除群ID前缀"""
        if ":\n<sysmsg" in content:
            return content.split(":\n", 1)[1].strip()
        return content.strip()
        
    def _clean_tts_text(self, text: str) -> str:
        """清理文本用于TTS转换
        
        保留主要文本内容，移除特殊字符，使文本更适合语音播报
        """
        # 移除@{nickname}标记，保留昵称
        text = text.replace("@{nickname}", "")
        
        # 移除链接
        text = re.sub(r'http[s]?://(?:[a-zA-Z]|[0-9]|[$-_@.&+]|[!*\\(\\),]|(?:%[0-9a-fA-F][0-9a-fA-F]))+', '', text)
        
        # 移除特定的特殊字符，保留基本标点
        special_chars = ["@", "#", "$", "%", "&", "*", "\\", "/", "【", "】", "（", "）", "(", ")", ":", "：", "❤", "🤝", "w", "+"]
        for char in special_chars:
            text = text.replace(char, "")
        
        # 将全角字母转换为半角字母
        full_width_map = {
            'Ａ': 'A', 'Ｂ': 'B', 'Ｃ': 'C', 'Ｄ': 'D', 'Ｅ': 'E', 'Ｆ': 'F', 'Ｇ': 'G', 'Ｈ': 'H', 'Ｉ': 'I', 'Ｊ': 'J',
            'Ｋ': 'K', 'Ｌ': 'L', 'Ｍ': 'M', 'Ｎ': 'N', 'Ｏ': 'O', 'Ｐ': 'P', 'Ｑ': 'Q', 'Ｒ': 'R', 'Ｓ': 'S', 'Ｔ': 'T',
            'Ｕ': 'U', 'Ｖ': 'V', 'Ｗ': 'W', 'Ｘ': 'X', 'Ｙ': 'Y', 'Ｚ': 'Z',
            'ａ': 'a', 'ｂ': 'b', 'ｃ': 'c', 'ｄ': 'd', 'ｅ': 'e', 'ｆ': 'f', 'ｇ': 'g', 'ｈ': 'h', 'ｉ': 'i', 'ｊ': 'j',
            'ｋ': 'k', 'ｌ': 'l', 'ｍ': 'm', 'ｎ': 'n', 'ｏ': 'o', 'ｐ': 'p', 'ｑ': 'q', 'ｒ': 'r', 'ｓ': 's', 'ｔ': 't',
            'ｕ': 'u', 'ｖ': 'v', 'ｗ': 'w', 'ｘ': 'x', 'ｙ': 'y', 'ｚ': 'z',
            '０': '0', '１': '1', '２': '2', '３': '3', '４': '4', '５': '5', '６': '6', '７': '7', '８': '8', '９': '9'
        }
        for full, half in full_width_map.items():
            text = text.replace(full, half)
            
        # 移除剩余的表情符号和特殊Unicode字符
        text = re.sub(r'[^\u4e00-\u9fa5a-zA-Z0-9，。！？,.!?]', '', text)
            
        # 将多个换行和空格替换为单个空格
        text = re.sub(r'\s+', ' ', text)
        
        # 移除开头和结尾的空白字符
        text = text.strip()
        

        
        return text
        
    async def _text_to_speech(self, text: str) -> Optional[bytes]:
        """将文本转换为语音"""
        try:
            # 如果文本为空，直接返回None
            if not text:
                logger.warning("[WelcomePlugin] TTS文本为空，跳过TTS转换")
                return None
                
            # 限制文本长度，防止URL过长
            if len(text) > 100:
                text = text[:100]
                logger.warning(f"[WelcomePlugin] TTS文本过长，已截断至100字符: {text}")
            
            # URL编码文本
            encoded_text = quote(text)
            
            # 构建TTS URL
            tts_url = f"{self.tts_url}?text={encoded_text}&voice={self.tts_voice}"

            # 使用httpx发送请求
            async with httpx.AsyncClient(timeout=10.0) as client:
                response = await client.get(tts_url)
                if response.status_code == 200:
                    voice_data = response.content
                    data_size = len(voice_data)

                    if data_size < 100:  # 简单检查数据大小
                        logger.warning(f"[WelcomePlugin] TTS返回的数据太小，可能无效: {response.content[:50]}")
                        return None
                    return voice_data
                else:
                    logger.error(f"[WelcomePlugin] TTS请求失败: {response.status_code}")
                    return None
        except Exception as e:
            logger.error(f"[WelcomePlugin] TTS转换失败: {e}")
            return None

    async def _send_voice_welcome(self, bot: WechatAPIClient, group_id: str, welcome_msg: str, nickname: str = ""):
        """发送欢迎消息（应用消息和表情包）"""
        try:
            # 发送应用消息
            try:
                # 构建XML消息
                title = "欢迎加入！"
                # 使用传入的nickname作为singer，如果没有则使用默认值
                singer = nickname if nickname else "😃"
                url = "https://weixin.qq.com"
                music_url = "https://sf3-cdn-tos.douyinstatic.com/obj/ies-music/7477140345122294565.mp3"
                cover_url = "http://shp.qpic.cn/collector/3211055935/d3e833ba-bddb-459b-97f9-edc7b3551af8/0"
                
                # 使用音乐插件的XML格式
                xml = f"""<appmsg appid="wx485a97c844086dc9" sdkver="0"><title>{title}</title><des>{singer}</des><action>view</action><type>3</type><showtype>0</showtype><content/><url>{url}</url><dataurl>{music_url}</dataurl><lowurl>{url}</lowurl><lowdataurl>{music_url}</lowdataurl><recorditem/><thumburl>{cover_url}</thumburl><messageaction/><laninfo/><extinfo/><sourceusername/><sourcedisplayname/><songlyric></songlyric><commenturl/><appattach><totallen>0</totallen><attachid/><emoticonmd5/><fileext/><aeskey/></appattach><webviewshared><publisherId/><publisherReqId>0</publisherReqId></webviewshared><weappinfo><pagepath/><username/><appid/><appservicetype>0</appservicetype></weappinfo><websearch/><songalbumurl>{cover_url}</songalbumurl></appmsg><fromusername>{bot.wxid}</fromusername><scene>0</scene><appinfo><version>1</version><appname></appname></appinfo><commenturl/>"""
                
                # 发送应用消息
                _, _, new_msg_id = await bot.send_app_message(
                    group_id,
                    xml,
                    3  # 使用类型3，这是音乐消息类型
                )

                # 如果第一种格式失败，尝试第二种格式
                if new_msg_id == 0:
                    # 尝试使用随机音乐的XML格式
                    random_music_xml = f"""<appmsg appid="wx79f2c4418704b4f8" sdkver="0"><title>{title}</title><des>{singer}</des><action>view</action><type>3</type><showtype>0</showtype><content/><url>{url}</url><dataurl>{music_url}</dataurl><lowurl>{url}</lowurl><lowdataurl>{music_url}</lowdataurl><recorditem/><thumburl>{cover_url}</thumburl><messageaction/><laninfo/><extinfo/><sourceusername/><sourcedisplayname/><songlyric></songlyric><commenturl/><appattach><totallen>0</totallen><attachid/><emoticonmd5/><fileext/><aeskey/></appattach><webviewshared><publisherId/><publisherReqId>0</publisherReqId></webviewshared><weappinfo><pagepath/><username/><appid/><appservicetype>0</appservicetype></weappinfo><websearch/><songalbumurl>{cover_url}</songalbumurl></appmsg><fromusername>{bot.wxid}</fromusername><scene>0</scene><appinfo><version>1</version><appname/></appinfo><commenturl/>"""

                    await bot.send_app_message(
                        group_id,
                        random_music_xml,
                        3  # 使用类型3，这是音乐消息类型
                    )
            except Exception as e:
                logger.error(f"[WelcomePlugin] 发送应用消息失败: {e}")
            
            # 发送表情包 - 直接使用已知的MD5和大小
            try:
                # 使用已知的表情包MD5和大小
                emoji_md5 = "28a4ea054d4192d888067ebd51b442d0"
                emoji_size = 40461

                # 发送3次表情包
                for i in range(3):
                    await bot.send_emoji_message(group_id, emoji_md5, emoji_size)
                    if i < 2:  # 最后一次不需要延迟
                        await asyncio.sleep(1)
            except Exception as e:
                logger.error(f"[WelcomePlugin] 发送表情包失败: {e}")
            
        except Exception as e:
            logger.error(f"[WelcomePlugin] 发送欢迎消息失败: {e}")
        
    def _extract_at_username(self, content: str) -> str:
        """从消息内容中提取@的用户名"""
        if not content.startswith("@"):
            return ""
            
        # 提取@后的用户名（直到空格或结束）
        parts = content.split(" ", 1)
        if parts:
            return parts[0][1:]  # 去掉@符号
        return ""
        
    def _get_wxid_string(self, wxid_field) -> str:
        """安全地从wxid字段获取字符串值，处理不同的数据结构"""
        if isinstance(wxid_field, dict) and "string" in wxid_field:
            return wxid_field["string"]
        elif isinstance(wxid_field, str):
            return wxid_field
        return ""

    def _update_user_nickname(self, group_id: str, wxid: str, nickname: str) -> None:
        """更新用户昵称缓存"""
        if not group_id or not wxid or not nickname:
            return
            
        # 初始化群组的昵称字典（如果不存在）
        if group_id not in self.user_nicknames:
            self.user_nicknames[group_id] = {}
            
        # 保存昵称
        self.user_nicknames[group_id][wxid] = nickname

    def _get_user_display_name(self, wxid: str, group_id: str = "") -> str:
        """获取用户的显示名称，优先使用群昵称，没有群昵称则使用wxid"""
        try:
            # 如果wxid为空，直接返回"未知用户"
            if not wxid:
                return "未知用户"
                
            # 特殊情况：群ID为空
            if not group_id:
                # 如果是全局管理员，添加特殊标记
                if wxid in self.global_admins:
                    return f"{wxid[:8]}...(全局管理)"
                return f"{wxid[:8]}..." if len(wxid) > 10 else wxid
            
            # 从缓存中查找用户昵称
            if group_id in self.user_nicknames and wxid in self.user_nicknames[group_id]:
                nickname = self.user_nicknames[group_id][wxid]
                # 为全局管理员添加标记
                if wxid in self.global_admins:
                    return f"{nickname}(全局管理)"
                return nickname
            
            # 可以根据微信ID特征进行一些优化显示
            if wxid == "wxid_ubbh6q832tcs21":
                return "郭(全局管理)" if wxid in self.global_admins else "郭"
                
            if wxid == "wxid_4usgcju5ey9q29":
                return "瑶瑶(机器人)"
                
            if wxid == "wxid_rwfb9vuy93jn22":
                return "退游了各位"
                
            # 对于全局管理员，添加标记
            if wxid in self.global_admins:
                # 获取wxid的缩略显示
                short_id = wxid[:8] + "..." if len(wxid) > 10 else wxid
                return f"{short_id}(全局管理)"
            
            # 简化wxid显示
            if len(wxid) > 10:
                return f"{wxid[:8]}..."
            return wxid
        except Exception as e:
            logger.error(f"[WelcomePlugin] 获取用户显示名称失败: {e}")
            return wxid

    def _format_admin_list(self, admin_list, group_id):
        """将管理员列表格式化为美观的竖排显示"""
        if not admin_list:
            return "⚠️ 当前群无管理员"
            
        # 将wxid转换为显示名称
        admin_display_names = [self._get_user_display_name(admin, group_id) for admin in admin_list]
        
        # 格式化为带序号和表情的列表
        formatted_list = []
        for i, name in enumerate(admin_display_names):
            # 根据是否为全局管理员使用不同的图标
            if any(admin in self.global_admins for admin in admin_list if self._get_user_display_name(admin, group_id) == name):
                icon = "👑" # 全局管理员
            else:
                icon = "🔰" # 普通群管理员
                
            formatted_list.append(f"{i+1}. {icon} {name}")
            
        # 合并为多行文本
        return "\n".join(formatted_list)

    def _verify_saved_config(self, group_id: str, expected_admins: list) -> bool:
        """验证配置是否成功保存到文件并重新加载"""
        try:
            # 尝试重新读取配置文件来验证
            with open("plugins/WelcomePlugin/welcome_messages.json", "r", encoding="utf-8") as f:
                saved_config = json.load(f)
                
            # 检查群组和管理员是否存在于保存的配置中
            if 'groups' in saved_config and group_id in saved_config['groups']:
                saved_admins = saved_config['groups'][group_id].get('admins', [])

                # 检查所有期望的管理员是否都正确保存
                all_saved = all(admin in saved_admins for admin in expected_admins)
                
                if not all_saved:
                    logger.error(f"[WelcomePlugin] 配置验证失败：期望的管理员 {expected_admins} 与文件中的 {saved_admins} 不匹配")
                return all_saved
            else:
                logger.error(f"[WelcomePlugin] 配置验证失败：文件中没有找到群组 {group_id}")
                return False
                
        except Exception as e:
            logger.error(f"[WelcomePlugin] 验证配置失败: {e}")
            return False 

    @on_text_message
    async def handle_text(self, bot: WechatAPIClient, message: dict):
        """处理文本消息"""
        if not message.get("Content"):
            return
            
        content = message["Content"].strip()
        sender = message.get("SenderWxid", "")
        group_id = message.get("FromWxid", "")
        
        # 获取机器人自己的wxid
        self_wxid = self._get_wxid_string(message.get("ToWxid", ""))
        

        
        # 如果消息包含群名称前缀（格式：群名称:\n内容），去除前缀
        if ":\n" in content:
            sender_nickname, content = content.split(":\n", 1)
            content = content.strip()
            # 保存发送者昵称
            self._update_user_nickname(group_id, sender, sender_nickname)
        
        # 尝试提取@用户的昵称和wxid对应关系
        if content.startswith("@"):
            # 从消息中提取@的用户昵称
            at_nickname = ""
            # 确保我们使用导入的re模块
            import re  # 在方法内部重新导入re模块以确保可用
            match = re.match(r'^@([^\s\u2000-\u200F\u2028-\u202F\u205F\u3000]+)', content)
            if match:
                at_nickname = match.group(1)
        
        # 获取@的用户列表
        at_users = []
        if "AtUserList" in message:
            at_users = message["AtUserList"]
        else:
            # 尝试从消息源解析@的用户
            msg_source = message.get("MsgSource", "")
            if msg_source:
                try:
                    root = ET.fromstring(msg_source)
                    # 直接获取atuserlist节点内容
                    atuserlist = root.find("atuserlist")
                    if atuserlist is not None and atuserlist.text:
                        # 分割CDATA中的内容（可能有多个用户）
                        user_ids = atuserlist.text.split(',')
                        for user_id in user_ids:
                            user_id = user_id.strip()
                            if user_id and user_id not in at_users:
                                at_users.append(user_id)
                    else:
                        # 兼容旧方式：查找at节点
                        at_nodes = root.findall(".//at")
                        for at_node in at_nodes:
                            user_id = at_node.get("username")
                            if user_id and user_id not in at_users:
                                at_users.append(user_id)
                except Exception as e:
                    logger.error(f"[WelcomePlugin] 解析@用户失败: {e}")
        
        # 保存@用户的昵称
        if at_users and content.startswith("@") and 'at_nickname' in locals() and at_nickname:
            for wxid in at_users:
                self._update_user_nickname(group_id, wxid, at_nickname)
        
        # 检查特殊情况：如果@的是机器人自己，则可能是在询问命令，提供帮助
        if at_users and len(at_users) == 1 and at_users[0] == self_wxid:
            # 如果只@了机器人但没有明确命令，提供帮助
            if not content or content.strip() == "":
                help_msg = "👋 您好！您可以通过以下命令与我互动：\n\n"
                help_msg += "📋 管理员命令：\n"
                help_msg += "🔸 @某人 添加群管理\n"
                help_msg += "🔸 @某人 移除群管理\n"
                help_msg += "🔸 查看群管理\n"
                help_msg += "🔸 开启欢迎\n"
                help_msg += "🔸 关闭欢迎\n"
                help_msg += "🔸 设置欢迎词 内容\n"
                help_msg += "🔸 查看欢迎词\n"
                help_msg += "🔸 重置欢迎词\n"
                help_msg += "🔸 重载欢迎配置"
                await bot.send_text_message(group_id, help_msg)
                return
                
        # 检查是否是全局管理员(用于特权操作)
        is_global_admin = sender in self.global_admins
        
        # 尝试解析消息文本
        # 微信中，当用户@某人并添加内容时，实际消息格式为"@用户名 实际内容"
        # 提取实际命令内容
        actual_content = content
        if content.startswith("@"):
            # 支持所有类型的空白字符（包括Unicode特殊空格）
            # 使用正则表达式查找第一个空白字符后的内容
            import re  # 再次确保re模块可用
            # 使用更强大的表达式匹配所有Unicode空白字符
            match = re.search(r'^@[^\s\u2000-\u200F\u2028-\u202F\u205F\u3000]+[\s\u2000-\u200F\u2028-\u202F\u205F\u3000]+(.*)', content, re.UNICODE)
            if match:
                actual_content = match.group(1).strip()
            else:
                # 兼容原有方式作为备选
                parts = content.split(" ", 1)
                if len(parts) > 1:
                    actual_content = parts[1].strip()
                else:
                    actual_content = ""
        
        # 检查是否需要处理这条消息 - 只有明确的命令才处理
        is_command_message = False
        command_keywords = ["添加群管理", "设置群管理", "移除群管理", "查看群管理", 
                           "开启欢迎", "关闭欢迎", "设置欢迎词", "查看欢迎词", 
                           "重置欢迎词", "重载欢迎配置"]
        
        # 检查原始内容或实际内容是否包含命令关键词
        for keyword in command_keywords:
            if keyword in content or keyword in actual_content:
                is_command_message = True
                break
                
        # 特殊情况：@机器人本身视为命令
        if at_users and len(at_users) == 1 and at_users[0] == self_wxid:
            is_command_message = True
            
        # 如果不是命令消息，直接返回
        if not is_command_message:
            return
            
        # 下面是命令处理逻辑 - 只有明确的命令才会继续处理
                
        # 检查是否是@的命令但没有具体指令 - 只当实际是命令时才提示
        if at_users and len(actual_content.strip()) == 0 and is_global_admin:
            # 检查是否有其他命令关键词
            has_cmd_keyword = False
            for keyword in ["添加", "设置", "移除"]:
                if keyword in content:
                    has_cmd_keyword = True
                    break
                    
            if has_cmd_keyword:
                help_msg = "💡 @用户后需要输入命令\n\n📋 可用@命令：\n"
                help_msg += "🔸 @某人 添加群管理\n"
                help_msg += "🔸 @某人 移除群管理"
                await bot.send_text_message(group_id, help_msg)
                return
            else:
                # 如果没有命令关键词，可能只是普通@，不处理
                return 

        # 检查是否是群管理员或全局管理员
        is_admin = self._is_group_admin(group_id, sender)
        if not is_admin:
            # 如果不是管理员但使用了管理命令，提供提示
            admin_commands = ["添加群管理", "设置群管理", "移除群管理", "查看群管理", 
                             "开启欢迎", "关闭欢迎", "设置欢迎词", "查看欢迎词", 
                             "重置欢迎词", "重载欢迎配置"]
            
            for cmd in admin_commands:
                if content == cmd or content.startswith(cmd + " ") or actual_content == cmd or actual_content.startswith(cmd + " "):
                    await bot.send_text_message(group_id, "⚠️ 权限不足\n\n只有管理员才能使用此命令")
                    return
            return
            
        # 获取群配置
        group_config = self._get_group_config(group_id)
        
        # 如果群不在配置中，自动添加
        if group_id not in self.groups:
            self.groups[group_id] = {
                "admins": [],
                "welcome_message": self.default_welcome_message,
                "enable": self.enable
            }
            # 保存配置以持久化新群设置
            self._save_config()
        
        # 全局管理员专属命令：设置群管理员 - 必须明确包含"添加群管理"或"设置群管理"
        if is_global_admin and (actual_content == "添加群管理" or actual_content == "设置群管理" or
                              content.endswith("添加群管理") or content.endswith("设置群管理")):
            # 如果没有@任何用户
            if not at_users:
                await bot.send_text_message(group_id, "⚠️ 请@要添加的管理员！\n\n💡 正确格式：@用户 添加群管理")
                return
            
            # 如果@的是机器人自己
            if len(at_users) == 1 and at_users[0] == self_wxid:
                await bot.send_text_message(group_id, "ℹ️ 不需要添加机器人为管理员\n\n💡 请@其他用户添加为管理员")
                return
                
            # 确保群在配置中
            if group_id not in self.groups:
                self.groups[group_id] = {
                    "admins": [],
                    "welcome_message": self.default_welcome_message,
                    "enable": self.enable
                }
                
            # 添加被@的用户为群管理员
            added_users = []
            for user in at_users:
                if user == self_wxid:
                    continue  # 跳过机器人自己

                if user not in self.groups[group_id]["admins"]:
                    self.groups[group_id]["admins"].append(user)
                    added_users.append(user)
            
            if not added_users:
                await bot.send_text_message(group_id, "ℹ️ 被@的用户已经是管理员了")
                return
                
            # 保存配置并确认结果
            save_result = self._save_config()

            # 额外验证配置是否正确保存
            verify_result = False
            if save_result:
                verify_result = self._verify_saved_config(group_id, self.groups[group_id]["admins"])

                # 如果验证失败，尝试再次保存
                if not verify_result:
                    save_result = self._save_config()
                    verify_result = self._verify_saved_config(group_id, self.groups[group_id]["admins"])

            if save_result and verify_result:
                # 格式化管理员列表为竖排显示
                admin_list = self._format_admin_list(self.groups[group_id]["admins"], group_id)
                reply_msg = f"✅ 添加成功！\n\n👥 当前群管理员：\n{admin_list}"
                await bot.send_text_message(group_id, reply_msg)
            else:
                error_msg = "❌ 设置失败，请检查日志。已尝试多次保存但未成功。"
                await bot.send_text_message(group_id, error_msg)

            return
            
        # 全局管理员专属命令：移除群管理员 - 必须明确包含"移除群管理"
        elif is_global_admin and (actual_content == "移除群管理" or content.endswith("移除群管理")):
            if not at_users:
                await bot.send_text_message(group_id, "⚠️ 请@要移除的管理员！\n\n💡 正确格式：@用户 移除群管理")
                return

            # 从群管理员中移除被@的用户
            removed_users = []
            for user in at_users:
                if user == self_wxid:
                    continue

                if user in self.groups[group_id]["admins"]:
                    self.groups[group_id]["admins"].remove(user)
                    removed_users.append(user)
            
            if not removed_users:
                await bot.send_text_message(group_id, "ℹ️ 被@的用户本来就不是管理员")
                return
                
            # 保存配置并确认结果
            save_result = self._save_config()

            # 额外验证配置是否正确保存
            verify_result = False
            if save_result:
                verify_result = self._verify_saved_config(group_id, self.groups[group_id]["admins"])

                # 如果验证失败，尝试再次保存
                if not verify_result:
                    save_result = self._save_config()
                    verify_result = self._verify_saved_config(group_id, self.groups[group_id]["admins"])

            if save_result and verify_result:
                if self.groups[group_id]["admins"]:
                    # 格式化管理员列表为竖排显示
                    admin_list = self._format_admin_list(self.groups[group_id]["admins"], group_id)
                    reply_msg = f"✅ 移除成功！\n\n👥 当前群管理员：\n{admin_list}"
                else:
                    reply_msg = "✅ 移除成功！\n\n⚠️ 当前群无管理员"

                await bot.send_text_message(group_id, reply_msg)
            else:
                error_msg = "❌ 操作失败，请检查日志。已尝试多次保存但未成功。"
                await bot.send_text_message(group_id, error_msg)
            return
            
        # 全局管理员专属命令：查看群管理员 - 必须是精确命令
        elif is_global_admin and actual_content == "查看群管理":
            # 强制从文件重新加载配置，确保显示最新状态
            self._load_config()

            # 直接从文件中读取最新的配置进行验证
            try:
                with open("plugins/WelcomePlugin/welcome_messages.json", "r", encoding="utf-8") as f:
                    saved_config = json.load(f)

                if 'groups' in saved_config and group_id in saved_config['groups']:
                    saved_admins = saved_config['groups'][group_id].get('admins', [])

                    # 如果文件中的管理员列表与内存中的不同，更新内存中的列表
                    if saved_admins != self.groups.get(group_id, {}).get('admins', []):
                        if group_id in self.groups:
                            self.groups[group_id]["admins"] = saved_admins

            except Exception as e:
                logger.error(f"[WelcomePlugin] 读取文件中的最新配置失败: {e}")

            if group_id in self.groups and self.groups[group_id]["admins"]:
                # 格式化管理员列表为竖排显示
                admin_list = self._format_admin_list(self.groups[group_id]["admins"], group_id)
                await bot.send_text_message(group_id, f"👥 当前群管理员：\n{admin_list}")
            else:
                await bot.send_text_message(group_id, "⚠️ 当前群无欢迎管理员\n💡 仅全局管理员可管理")
            return
                
        # 以下是普通群管理员也可以使用的命令 - 必须是精确命令
        elif actual_content == "开启欢迎":
            self.groups[group_id]["enable"] = True
            if self._save_config():
                await bot.send_text_message(group_id, "✅ 开启成功！\n\n🎉 本群欢迎功能已开启")
            else:
                await bot.send_text_message(group_id, "❌ 开启失败，请检查日志")
                
        elif actual_content == "关闭欢迎":
            self.groups[group_id]["enable"] = False
            if self._save_config():
                await bot.send_text_message(group_id, "✅ 关闭成功！\n\n🔇 本群欢迎功能已关闭")
            else:
                await bot.send_text_message(group_id, "❌ 关闭失败，请检查日志")
                
        elif actual_content.startswith("设置欢迎词 "):
            new_message = actual_content[6:].strip()
            if not new_message:
                await bot.send_text_message(group_id, "❌ 欢迎词不能为空！\n\n💡 请输入有效的欢迎词")
                return
                
            # 保存用户输入的原始消息用于显示
            original_message = new_message
            
            # 自动在欢迎词前面添加@标记
            if not new_message.startswith("@{nickname}"):
                new_message = f"@{{nickname}} {new_message}"

            self.groups[group_id]["welcome_message"] = new_message
            if self._save_config():
                await bot.send_text_message(group_id, f"✅ 设置成功！\n\n🔹 当前欢迎词：\n「{original_message}」")
            else:
                await bot.send_text_message(group_id, "❌ 设置失败，请检查日志")
                
        elif actual_content == "查看欢迎词":
            welcome_msg = group_config["welcome_message"]
            # 清理显示的欢迎词，去掉@{nickname}标记
            display_msg = welcome_msg.replace("@{nickname}", "").strip()
            await bot.send_text_message(group_id, f"📝 当前欢迎词：\n「{display_msg}」")
            
        elif actual_content == "重置欢迎词":
            self.groups[group_id]["welcome_message"] = self.default_welcome_message
            if self._save_config():
                # 清理显示的欢迎词，去掉@{nickname}标记
                display_msg = self.default_welcome_message.replace("@{nickname}", "").strip()
                await bot.send_text_message(group_id, f"✅ 重置成功！\n\n🔹 当前欢迎词：\n「{display_msg}」")
            else:
                await bot.send_text_message(group_id, "❌ 重置失败，请检查日志")
                
        elif actual_content == "重载欢迎配置":
            if await self.reload_config():
                await bot.send_text_message(group_id, "✅ 重载成功！\n\n🔄 欢迎配置已刷新")
            else:
                await bot.send_text_message(group_id, "❌ 重载失败，请检查日志")
        
        # 处理未知命令，只有在明确是命令消息的情况下提供提示
        else:
            # 避免触发的全局命令前缀
            global_cmd_prefixes = ["重载所有", "加载所有", "启动所有", "停止所有", "更新所有", "启用所有", "禁用所有"]
            if any(actual_content.startswith(prefix) for prefix in global_cmd_prefixes):
                return
            
            # 只有以下格式是正确的完整命令:
            valid_cmds = ["添加群管理", "设置群管理", "移除群管理", "查看群管理", 
                         "开启欢迎", "关闭欢迎", "查看欢迎词", "重置欢迎词", "重载欢迎配置"]
             
            # 对于需要附加参数的命令:
            param_cmds = ["设置欢迎词"]
             
            # 检查是否是不完整的命令
            for cmd in param_cmds:
                if actual_content == cmd:  # 如果只输入了命令前缀但没有参数
                    await bot.send_text_message(group_id, f"⚠️ 命令不完整\n\n💡 正确格式：{cmd} 内容")
                    return
             
            # 对于其他可能是命令的消息提供帮助，但只在确定是命令消息时才提示
            # 更精准地判断是否为当前插件的命令
            is_plugin_cmd = False
             
            # 1. 检查是否是完整的有效命令
            if actual_content in valid_cmds:
                is_plugin_cmd = True
                 
            # 2. 检查是否是@命令，且包含关键词
            if content.startswith("@") and ("添加群管理" in content or "设置群管理" in content or "移除群管理" in content):
                is_plugin_cmd = True
                 
            # 3. 检查@某人后紧跟命令关键词
            if at_users and any(cmd in content for cmd in ["添加群管理", "设置群管理", "移除群管理"]):
                is_plugin_cmd = True
                 
            if is_plugin_cmd:
                help_msg = "⚠️ 未知命令或格式错误\n\n📋 可用命令：\n"
                help_msg += "🔸 @某人 添加群管理\n"
                help_msg += "🔸 @某人 移除群管理\n"
                help_msg += "🔸 查看群管理\n"
                help_msg += "🔸 开启欢迎\n"
                help_msg += "🔸 关闭欢迎\n"
                help_msg += "🔸 设置欢迎词 内容\n"
                help_msg += "🔸 查看欢迎词\n"
                help_msg += "🔸 重置欢迎词\n"
                help_msg += "🔸 重载欢迎配置"
                await bot.send_text_message(group_id, help_msg)
                return

    @on_system_message
    async def handle_system(self, bot: WechatAPIClient, message: dict):
        """处理系统消息"""
        group_id = message.get("FromWxid", "")
        if not group_id:
            return
            
        # 获取群配置
        group_config = self._get_group_config(group_id)
        if not group_config["enable"]:
            return
            
        content = message.get("Content", "")
        if isinstance(content, dict):
            content = content.get("string", "")
            
        try:
            content = self._clean_content(content)
            
            root = ET.fromstring(content)
            if root.tag != "sysmsg":
                return
                
            msg_type = root.attrib.get("type")
            
            if msg_type == "sysmsgtemplate":
                sysmsgtemplate = root.find("sysmsgtemplate")
                if sysmsgtemplate is not None:
                    template = sysmsgtemplate.find("content_template")
                    if template is not None:
                        template_type = template.attrib.get("type")
                        
                        if template_type in ["tmpl_type_profile", "tmpl_type_profilewithrevoke"]:
                            template_text = template.find("template").text
                            
                            new_members = []
                            if '"$names$"加入了群聊' in template_text:
                                new_members = self._parse_member_info(root, "names")
                            elif '"$username$"邀请"$names$"加入了群聊' in template_text:
                                new_members = self._parse_member_info(root, "names")
                            elif '你邀请"$names$"加入了群聊' in template_text:
                                new_members = self._parse_member_info(root, "names")
                            elif '"$adder$"通过扫描"$from$"分享的二维码加入群聊' in template_text:
                                new_members = self._parse_member_info(root, "adder")
                            elif '"$adder$"通过"$from$"的邀请二维码加入群聊' in template_text:
                                new_members = self._parse_member_info(root, "adder")
                            else:
                                return
                                
                            if not new_members:
                                return
                            
                            for member in new_members:
                                welcome_msg = group_config["welcome_message"].format(
                                    nickname=member["nickname"]
                                )
                                await bot.send_text_message(group_id, welcome_msg, [member["username"]])
                                await self._send_voice_welcome(bot, group_id, welcome_msg, member["nickname"])
                            
        except ET.ParseError as e:
            logger.error(f"解析系统消息XML失败: {e}")
        except Exception as e:
            logger.error(f"处理欢迎消息时发生错误: {e}")