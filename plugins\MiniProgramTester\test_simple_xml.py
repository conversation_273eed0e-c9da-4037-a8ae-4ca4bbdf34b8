#!/usr/bin/env python3
"""
测试简化的小程序XML格式
"""
import xml.etree.ElementTree as ET

def test_simple_xml():
    """测试简化的小程序XML"""
    xml_content = '''<appmsg appid="wxa708de63ee4a2353" sdkver="0">
<title>唱舞星愿站（唱舞全明星）</title>
<des>星愿站</des>
<action>view</action>
<type>33</type>
<showtype>0</showtype>
<content/>
<url/>
<lowurl/>
<dataurl/>
<lowdataurl/>
<appattach>
    <totallen>0</totallen>
    <attachid/>
    <emoticonmd5/>
    <fileext/>
    <aeskey/>
</appattach>
<extinfo/>
<sourceusername>wxa708de63ee4a2353</sourceusername>
<sourcedisplayname>唱舞星愿站</sourcedisplayname>
<thumburl/>
<md5/>
<weappinfo>
    <pagepath><![CDATA[pages/pointsStroe/wares/index.html?key=abz3BM9k&unionid=oA7D81SRhmAwPJuDQ0Pw39LWcWoY&corpid=wwccefc778261bf00f&app_id=wxa708de63ee4a2353&plate_id=91&userid=40610459]]></pagepath>
    <username>gh_25eb09d7bc53@app</username>
    <appid>wxa708de63ee4a2353</appid>
    <version>14</version>
    <type>2</type>
    <weappiconurl><![CDATA[http://mmbiz.qpic.cn/mmbiz_png/Y4yibCmxrbX6aCNIhj0BH1CXuE8chunB9AN5fnaRJUMRGRicxkh8crn7P1VCQuhV4GB3KCkE4e5ibmXVEibxaoIJuA/640?wx_fmt=png&wxfrom=200]]></weappiconurl>
    <appservicetype>0</appservicetype>
</weappinfo>
</appmsg>'''

    try:
        # 尝试解析XML
        root = ET.fromstring(xml_content)
        print("✅ 简化XML格式正确！")
        
        # 提取关键信息
        title = root.find('title')
        msg_type = root.find('type')
        weappinfo = root.find('weappinfo')
        
        print(f"标题: {title.text if title is not None else 'N/A'}")
        print(f"消息类型: {msg_type.text if msg_type is not None else 'N/A'}")
        
        if weappinfo is not None:
            appid = weappinfo.find('appid')
            username = weappinfo.find('username')
            print(f"小程序AppID: {appid.text if appid is not None else 'N/A'}")
            print(f"小程序用户名: {username.text if username is not None else 'N/A'}")
        
        return True
        
    except ET.ParseError as e:
        print(f"❌ XML格式错误: {e}")
        return False
    except Exception as e:
        print(f"❌ 其他错误: {e}")
        return False

def test_ultra_simple_xml():
    """测试超简化的小程序XML"""
    xml_content = '''<appmsg appid="wxa708de63ee4a2353" sdkver="0">
<title>唱舞星愿站</title>
<des>星愿站</des>
<type>33</type>
<weappinfo>
    <username>gh_25eb09d7bc53@app</username>
    <appid>wxa708de63ee4a2353</appid>
</weappinfo>
</appmsg>'''

    try:
        # 尝试解析XML
        root = ET.fromstring(xml_content)
        print("✅ 超简化XML格式正确！")
        
        # 提取关键信息
        title = root.find('title')
        msg_type = root.find('type')
        weappinfo = root.find('weappinfo')
        
        print(f"标题: {title.text if title is not None else 'N/A'}")
        print(f"消息类型: {msg_type.text if msg_type is not None else 'N/A'}")
        
        if weappinfo is not None:
            appid = weappinfo.find('appid')
            username = weappinfo.find('username')
            print(f"小程序AppID: {appid.text if appid is not None else 'N/A'}")
            print(f"小程序用户名: {username.text if username is not None else 'N/A'}")
        
        return True
        
    except ET.ParseError as e:
        print(f"❌ XML格式错误: {e}")
        return False
    except Exception as e:
        print(f"❌ 其他错误: {e}")
        return False

if __name__ == "__main__":
    print("=== 测试简化XML格式 ===")
    test_simple_xml()
    
    print("\n=== 测试超简化XML格式 ===")
    test_ultra_simple_xml()
