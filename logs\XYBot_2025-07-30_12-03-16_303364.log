2025-07-30 12:03:17 | SUCCESS | 读取主设置成功
2025-07-30 12:03:17 | DEBUG | 最终使用的 Redis 主机地址: 127.0.0.1
2025-07-30 12:03:17 | INFO | 2025/07/30 12:03:17 GetRedisAddr: 127.0.0.1:6379
2025-07-30 12:03:17 | INFO | 2025/07/30 12:03:17 Redis连接成功 | 地址:127.0.0.1:6379 DB:0
2025-07-30 12:03:17 | INFO | 2025/07/30 12:03:17 Server start at :9000
2025-07-30 12:03:18 | SUCCESS | WechatAPI服务已启动
2025-07-30 12:03:18 | INFO | 登录账号信息: wxid: wxid_4usgcju5ey9q29  昵称: 瑶瑶  微信号: Today-KFC  手机号: None
2025-07-30 12:03:18 | INFO | 登录设备信息: device_name: <PERSON>'s Pad  device_id: 49d050a7e94d1ecc40790d74b1c4369f
2025-07-30 12:03:18 | SUCCESS | 登录成功
2025-07-30 12:03:18 | SUCCESS | 已开启自动心跳
2025-07-30 12:03:18 | INFO | 成功加载表情映射文件，共 546 条记录
2025-07-30 12:03:18 | SUCCESS | 数据库初始化成功
2025-07-30 12:03:18 | SUCCESS | 定时任务已启动
2025-07-30 12:03:18 | SUCCESS | 插件 AISignIn 已加载定时任务: {'plugins.AISignIn.main.AISignIn.auto_signin_task'}
2025-07-30 12:03:19 | INFO | 已加载管理员列表: ['wxid_ubbh6q832tcs21', 'admin-wxid']
2025-07-30 12:03:19 | INFO | 播客API初始化成功
2025-07-30 12:03:19 | INFO | 加载配置完成，转换后的数据: {'gh_7057516c9e71': {'name': '唱舞全明星', 'groups': ['27852221909@chatroom', '51891329927@chatroom']}, 'gh_d4d21a0e4623': {'name': '两颗桃', 'groups': ['***********@chatroom']}, 'gh_410ff2fa6ec9': {'name': 'NGC660 Ai研究院', 'groups': ['47442567074@chatroom']}, 'gh_f78c53c507c0': {'name': '不正常吐槽君', 'groups': ['***********@chatroom']}, 'gh_ba3381c847b3': {'name': '唱舞星计划', 'groups': ['51891329927@chatroom']}, 'gh_1a102fb4cad9': {'name': '我在娱乐圈的唯一人脉', 'groups': ['***********@chatroom', '27852221909@chatroom']}}
2025-07-30 12:03:19 | INFO | ArticleForwarder插件初始化完成 - 监控配置: {'gh_7057516c9e71': {'name': '唱舞全明星', 'groups': ['27852221909@chatroom', '51891329927@chatroom']}, 'gh_d4d21a0e4623': {'name': '两颗桃', 'groups': ['***********@chatroom']}, 'gh_410ff2fa6ec9': {'name': 'NGC660 Ai研究院', 'groups': ['47442567074@chatroom']}, 'gh_f78c53c507c0': {'name': '不正常吐槽君', 'groups': ['***********@chatroom']}, 'gh_ba3381c847b3': {'name': '唱舞星计划', 'groups': ['51891329927@chatroom']}, 'gh_1a102fb4cad9': {'name': '我在娱乐圈的唯一人脉', 'groups': ['***********@chatroom', '27852221909@chatroom']}}
2025-07-30 12:03:19 | DEBUG | [TempFileManager] 添加清理规则: default
2025-07-30 12:03:19 | DEBUG | [TempFileManager] 添加清理规则: images
2025-07-30 12:03:19 | DEBUG | [TempFileManager] 添加清理规则: videos
2025-07-30 12:03:19 | DEBUG | [TempFileManager] 添加清理规则: audio
2025-07-30 12:03:19 | DEBUG | [TempFileManager] 添加清理规则: temp
2025-07-30 12:03:19 | DEBUG | [TempFileManager] 添加清理规则: large_files
2025-07-30 12:03:19 | INFO | [TempFileManager] 临时文件清理任务已启动
2025-07-30 12:03:20 | INFO | [ChatSummary] 数据库初始化成功
2025-07-30 12:03:20 | SUCCESS | 插件 GoodMorning 已加载定时任务: {'plugins.GoodMorning.main.GoodMorning.daily_task'}
2025-07-30 12:03:20 | SUCCESS | 插件 News 已加载定时任务: {'plugins.News.main.News.night_news', 'plugins.News.main.News.noon_news'}
2025-07-30 12:03:20 | INFO | [PatReply] AMR文件已存在且是最新的: C:\XYBotV2\data\paiyipai\1_BFB5258B-2B13-4CF4-A40C-5BCCE74E5414_converted.amr
2025-07-30 12:03:20 | DEBUG | [PatReply] 从缓存加载base64: 18228 字符
2025-07-30 12:03:20 | SUCCESS | 插件 QuarkSignIn 已加载定时任务: {'plugins.QuarkSignIn.main.QuarkSignIn.auto_signin_task'}
2025-07-30 12:03:20 | INFO | [QuarkSignIn] 已设置自动签到时间: 08:00
2025-07-30 12:03:20 | INFO | 插件状态: 启用, 检查间隔: 3600秒, 最大重试次数: 3, 重试延迟: 5秒
2025-07-30 12:03:20 | INFO | 已加载管理员列表: ['wxid_ubbh6q832tcs21', 'admin-wxid']
2025-07-30 12:03:20 | INFO | 已加载 2 个改名监控群和 1 个退群监控群
2025-07-30 12:03:20 | INFO | [RenameReminder] 开始启用插件...
2025-07-30 12:03:20 | SUCCESS | 插件 RenameReminder 已加载定时任务: {'plugins.RenameReminder.main.RenameReminder.check_name_changes'}
2025-07-30 12:03:20 | INFO | [RenameReminder] 插件已启用，开始初始化...
2025-07-30 12:03:20 | INFO | [RenameReminder] 支持的命令: 开启改名提醒, 关闭改名提醒, 开启退群提醒, 关闭退群提醒, 查看提醒状态
2025-07-30 12:03:20 | INFO | 已设置检查间隔为 3600 秒
2025-07-30 12:03:20 | INFO | 已加载 2 个监控群，开始更新成员列表
2025-07-30 12:03:20 | DEBUG | 已更新群 27852221909@chatroom 的成员列表
2025-07-30 12:03:21 | DEBUG | 已更新群 51891329927@chatroom 的成员列表
2025-07-30 12:03:21 | SUCCESS | 插件 TimerTask 已加载定时任务: {'plugins.TimerTask.main.TimerTask.check_tasks'}
2025-07-30 12:03:21 | INFO | [VideoDemand] 加载了 128 个视频类别配置
2025-07-30 12:03:22 | INFO | [YaoyaoPlugin] 私聊黑名单用户: ['gh_a5cf07d474f1', 'gh_ba3381c847b3']
2025-07-30 12:03:22 | INFO | [YaoyaoPlugin] 已加载管理员列表: ['wxid_ubbh6q832tcs21', 'admin-wxid']
2025-07-30 12:03:22 | INFO | [yuanbao] 插件初始化完成
2025-07-30 12:03:22 | DEBUG | [yuanbao] 指令: ['元宝', 'yuanbao']
2025-07-30 12:03:22 | DEBUG | [yuanbao] 绘图模型: gpt_175B_0404
2025-07-30 12:03:22 | DEBUG | [yuanbao] 认证信息加载状态: hy_token=True, hy_user=True, agent_id=True, x_uskey=True
2025-07-30 12:03:22 | SUCCESS | 已加载插件: ['AdminPoint', 'AdminSignInReset', 'AdminWhitelist', 'AISignIn', 'AppMessageTester', 'ArticleForwarder', 'BaiduAgentsPlugin', 'BaiduDraw', 'BotStatus', 'ChatSummary', 'DanceSignInPlugin', 'DeepseekPlugin', 'Doubao', 'DoubaoDrawing', 'DoubaoImageRecognition', 'DouBaoImageToImage', 'DouBaoImageToVideo', 'DoubaoVideoSearch', 'DouHui', 'EmojiTestPlugin', 'FixedQuoteTest', 'GetContact', 'Gomoku', 'GoodMorning', 'HunyuanDraw', 'ImageEcho', 'ImageToImage', 'JiemengDraw', 'KeLingImageToImage', 'KlingAI', 'Leaderboard', 'LuckyDraw', 'ManagePlugin', 'MeituAI', 'Menu', 'MiniProgramTester', 'Music', 'News', 'PatReply', 'PointTrade', 'QuarkSignIn', 'QueryPoint', 'RandomMember', 'RandomPicture', 'RenameReminder', 'RevokePlugin', 'RoboNeo', 'SignIn', 'TempFileManagerPlugin', 'TencentLke', 'TimerTask', 'TongyiDraw', 'URLShortener', 'VideoDemand', 'VideoParserPlugin', 'VideoTest', 'VivoAgentsPlugin', 'VoiceTest', 'Warthunder', 'WeatherQuery', 'WelcomePlugin', 'Xunfei', 'YaoyaoPlugin', 'Yuanbao', '抽签', '造梦次元']
2025-07-30 12:03:22 | INFO | 处理堆积消息中
2025-07-30 12:03:22 | DEBUG | 接受到 6 条消息
2025-07-30 12:03:23 | SUCCESS | 处理堆积消息完毕
2025-07-30 12:03:23 | SUCCESS | 开始处理消息
2025-07-30 12:03:29 | DEBUG | 收到消息: {'MsgId': 465574603, 'FromUserName': {'string': '***********@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 1, 'Content': {'string': 'wxid_jegyk4i3v7zg22:\ngpt翻译中英文，太垃圾了'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1753848221, 'MsgSource': '<msgsource>\n\t<bizflag>0</bizflag>\n\t<pua>1</pua>\n\t<eggIncluded>1</eggIncluded>\n\t<silence>0</silence>\n\t<membercount>73</membercount>\n\t<signature>N0_V1_cyPCJ2Te|v1_ZYYb1DMq</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'PushContent': '阿尼亚与她 : gpt翻译中英文，太垃圾了', 'NewMsgId': 7029639333892769637, 'MsgSeq': 871411539}
2025-07-30 12:03:29 | INFO | 收到文本消息: 消息ID:465574603 来自:***********@chatroom 发送人:wxid_jegyk4i3v7zg22 @:[] 内容:gpt翻译中英文，太垃圾了
2025-07-30 12:03:30 | INFO | 成功加载表情映射文件，共 546 条记录
2025-07-30 12:03:30 | DEBUG | 处理消息内容: 'gpt翻译中英文，太垃圾了'
2025-07-30 12:03:30 | DEBUG | 消息内容 'gpt翻译中英文，太垃圾了' 不匹配任何命令，忽略
2025-07-30 12:04:38 | DEBUG | 收到消息: {'MsgId': 1753848060, 'FromUserName': {'string': 'newsapp'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 10002, 'Content': {'string': '<?xml version="1.0"?>\n<sysmsg type="functionmsg">\n\t<functionmsg>\n\t\t<cgi>/cgi-bin/micromsg-bin/addtxnewsmsg</cgi>\n\t\t<cmdid>825</cmdid>\n\t\t<businessid>50001</businessid>\n\t\t<functionmsgid>2025073001</functionmsgid>\n\t\t<op>0</op>\n\t\t<version>1753848006</version>\n\t\t<retryinterval>150</retryinterval>\n\t\t<reportid>63162</reportid>\n\t\t<successkey>0</successkey>\n\t\t<failkey>1</failkey>\n\t\t<finalfailkey>2</finalfailkey>\n\t\t<custombuff>CAAQ\nAzjZoKbEBkDGsabEBkj8sabEBlAA</custombuff>\n\t\t<retrycount>3</retrycount>\n\t</functionmsg>\n</sysmsg>\n'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1753848282, 'NewMsgId': 1753848060, 'MsgSeq': 0}
2025-07-30 12:04:38 | DEBUG | 系统消息类型: functionmsg
2025-07-30 12:04:38 | INFO | 未知的系统消息类型: {'MsgId': 1753848060, 'ToWxid': 'wxid_4usgcju5ey9q29', 'MsgType': 10002, 'Content': '<?xml version="1.0"?>\n<sysmsg type="functionmsg">\n\t<functionmsg>\n\t\t<cgi>/cgi-bin/micromsg-bin/addtxnewsmsg</cgi>\n\t\t<cmdid>825</cmdid>\n\t\t<businessid>50001</businessid>\n\t\t<functionmsgid>2025073001</functionmsgid>\n\t\t<op>0</op>\n\t\t<version>1753848006</version>\n\t\t<retryinterval>150</retryinterval>\n\t\t<reportid>63162</reportid>\n\t\t<successkey>0</successkey>\n\t\t<failkey>1</failkey>\n\t\t<finalfailkey>2</finalfailkey>\n\t\t<custombuff>CAAQ\nAzjZoKbEBkDGsabEBkj8sabEBlAA</custombuff>\n\t\t<retrycount>3</retrycount>\n\t</functionmsg>\n</sysmsg>\n', 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1753848282, 'NewMsgId': 1753848060, 'MsgSeq': 0, 'FromWxid': 'newsapp', 'SenderWxid': 'newsapp', 'IsGroup': False}
2025-07-30 12:08:47 | DEBUG | 收到消息: {'MsgId': 1065235849, 'FromUserName': {'string': '***********@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 1, 'Content': {'string': 'wxid_jegyk4i3v7zg22:\n画asian woman laying on a bed in red on t shirt, in the style of karol bak, uniformly staged images, shilin huang, 32k uhd, dark amber and gray --ar 71:93 --stylize 750 --v 6'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1753848539, 'MsgSource': '<msgsource>\n\t<alnode>\n\t\t<cf>2</cf>\n\t</alnode>\n\t<pua>1</pua>\n\t<silence>0</silence>\n\t<membercount>73</membercount>\n\t<signature>N0_V1_ADeJywa8|v1_P2g8H9lQ</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'PushContent': '阿尼亚与她 : 画asian woman laying on a bed in red on t shirt, in the style of karol bak, uniformly sta...', 'NewMsgId': 4907771037596516018, 'MsgSeq': 871411540}
2025-07-30 12:08:47 | INFO | 收到文本消息: 消息ID:1065235849 来自:***********@chatroom 发送人:wxid_jegyk4i3v7zg22 @:[] 内容:画asian woman laying on a bed in red on t shirt, in the style of karol bak, uniformly staged images, shilin huang, 32k uhd, dark amber and gray --ar 71:93 --stylize 750 --v 6
2025-07-30 12:08:47 | DEBUG | 处理消息内容: '画asian woman laying on a bed in red on t shirt, in the style of karol bak, uniformly staged images, shilin huang, 32k uhd, dark amber and gray --ar 71:93 --stylize 750 --v 6'
2025-07-30 12:08:47 | DEBUG | 消息内容 '画asian woman laying on a bed in red on t shirt, in the style of karol bak, uniformly staged images, shilin huang, 32k uhd, dark amber and gray --ar 71:93 --stylize 750 --v 6' 不匹配任何命令，忽略
2025-07-30 12:10:11 | DEBUG | 收到消息: {'MsgId': 1279951962, 'FromUserName': {'string': '***********@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 1, 'Content': {'string': 'wxid_7o4g44suf20t22:\n笑死'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1753848623, 'MsgSource': '<msgsource>\n\t<pua>1</pua>\n\t<eggIncluded>1</eggIncluded>\n\t<silence>0</silence>\n\t<membercount>73</membercount>\n\t<signature>N0_V1_u+K8Z66i|v1_W6xc43Jw</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'PushContent': '与其，不惹 : 笑死', 'NewMsgId': 7180617531975068508, 'MsgSeq': 871411541}
2025-07-30 12:10:11 | INFO | 收到文本消息: 消息ID:1279951962 来自:***********@chatroom 发送人:wxid_7o4g44suf20t22 @:[] 内容:笑死
2025-07-30 12:10:12 | INFO | 发送表情消息: 对方wxid:***********@chatroom md5:d6db8b359af85e7e180ee752832f5e77 总长度:9992069
2025-07-30 12:10:12 | DEBUG | 处理消息内容: '笑死'
2025-07-30 12:10:12 | DEBUG | 消息内容 '笑死' 不匹配任何命令，忽略
2025-07-30 12:10:31 | DEBUG | 收到消息: {'MsgId': 1584540268, 'FromUserName': {'string': '***********@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 47, 'Content': {'string': 'wxid_laurnst5xn0q22:\n<msg><emoji fromusername = "wxid_laurnst5xn0q22" tousername = "***********@chatroom" type="2" idbuffer="media:0_0" md5="7324eb23fabedf7856770d0f436db6b5" len = "2227181" productid="" androidmd5="7324eb23fabedf7856770d0f436db6b5" androidlen="2227181" s60v3md5 = "7324eb23fabedf7856770d0f436db6b5" s60v3len="2227181" s60v5md5 = "7324eb23fabedf7856770d0f436db6b5" s60v5len="2227181" cdnurl = "http://vweixinf.tc.qq.com/110/20401/stodownload?m=7324eb23fabedf7856770d0f436db6b5&amp;filekey=30440201010430302e02016e0402535a04203733323465623233666162656466373835363737306430663433366462366235020321fbed040d00000004627466730000000132&amp;hy=SZ&amp;storeid=265a5eaf80000e7d7523571c30000006e01004fb1535a073b21815679edb03&amp;ef=1&amp;bizid=1022" designerid = "" thumburl = "" encrypturl = "http://vweixinf.tc.qq.com/110/20402/stodownload?m=0eb443a8ecdedad110db19108afe4895&amp;filekey=30440201010430302e02016e0402535a04203065623434336138656364656461643131306462313931303861666534383935020321fbf0040d00000004627466730000000132&amp;hy=SZ&amp;storeid=265a5eaf800036c21523571c30000006e02004fb2535a073b21815679edb12&amp;ef=2&amp;bizid=1022" aeskey= "ae9ea4a2f09848e78a14bcd99649ed87" externurl = "http://vweixinf.tc.qq.com/110/20403/stodownload?m=ce6d1d584027d46312ef1bbe28d3931a&amp;filekey=30440201010430302e02016e0402535a042063653664316435383430323764343633313265663162626532386433393331610203024780040d00000004627466730000000132&amp;hy=SZ&amp;storeid=265a5eaf8000607c9523571c30000006e03004fb3535a073b21815679edb29&amp;ef=3&amp;bizid=1022" externmd5 = "d9ec293b45b3bf2d48ce4c46c320cb87" width= "240" height= "240" tpurl= "" tpauthkey= "" attachedtext= "" attachedtextcolor= "" lensid= "" emojiattr= "" linkid= "" desc= "" ></emoji>  </msg>'}, 'Status': 3, 'ImgStatus': 2, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1753848643, 'MsgSource': '<msgsource>\n\t<silence>0</silence>\n\t<membercount>73</membercount>\n\t<signature>N0_V1_8rhk+wMv|v1_JgkCr+CF</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'PushContent': 'متسول暗在群聊中发了一个表情', 'NewMsgId': 9198554871602485882, 'MsgSeq': 871411544}
2025-07-30 12:10:31 | INFO | 收到表情消息: 消息ID:1584540268 来自:***********@chatroom 发送人:wxid_laurnst5xn0q22 MD5:7324eb23fabedf7856770d0f436db6b5 大小:2227181
2025-07-30 12:10:31 | DEBUG | [DoubaoImageRecognition] 缓存表情消息: 9198554871602485882
2025-07-30 12:10:41 | DEBUG | 收到消息: {'MsgId': 968623915, 'FromUserName': {'string': '***********@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 1, 'Content': {'string': 'wxid_jegyk4i3v7zg22:\nhttps://v.douyin.com/LKeiICLTYHM'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1753848653, 'MsgSource': '<msgsource>\n\t<alnode>\n\t\t<cf>3</cf>\n\t</alnode>\n\t<pua>1</pua>\n\t<silence>0</silence>\n\t<membercount>73</membercount>\n\t<signature>N0_V1_fXzehIfJ|v1_oQT+n8+d</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'PushContent': '阿尼亚与她 : https://v.douyin.com/LKeiICLTYHM', 'NewMsgId': 1210380910468253867, 'MsgSeq': 871411545}
2025-07-30 12:10:41 | INFO | 收到文本消息: 消息ID:968623915 来自:***********@chatroom 发送人:wxid_jegyk4i3v7zg22 @:[] 内容:https://v.douyin.com/LKeiICLTYHM
2025-07-30 12:10:41 | DEBUG | 处理消息内容: 'https://v.douyin.com/LKeiICLTYHM'
2025-07-30 12:10:41 | DEBUG | 消息内容 'https://v.douyin.com/LKeiICLTYHM' 不匹配任何命令，忽略
2025-07-30 12:10:56 | DEBUG | 收到消息: {'MsgId': 1807188869, 'FromUserName': {'string': '***********@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 1, 'Content': {'string': 'wxid_srknxij3jka022:\nMJ绘画任务完成\n提示词：asian woman laying on a bed in red on t shirt, in the style of karol bak, uniformly staged images, shilin huang, 32k uhd, dark amber and gray --ar 71:93 --stylize 750 --v 6\n耗时：126.45秒'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1753848668, 'MsgSource': '<msgsource>\n\t<pua>1</pua>\n\t<silence>0</silence>\n\t<membercount>73</membercount>\n\t<signature>N0_V1_xidsHTW+|v1_mikWoGgv</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'PushContent': '她 : MJ绘画任务完成\n提示词：asian woman laying on a bed in red on t shirt, in the sty...', 'NewMsgId': 4545531679991936619, 'MsgSeq': 871411546}
2025-07-30 12:10:56 | INFO | 收到文本消息: 消息ID:1807188869 来自:***********@chatroom 发送人:wxid_srknxij3jka022 @:[] 内容:MJ绘画任务完成
提示词：asian woman laying on a bed in red on t shirt, in the style of karol bak, uniformly staged images, shilin huang, 32k uhd, dark amber and gray --ar 71:93 --stylize 750 --v 6
耗时：126.45秒
2025-07-30 12:10:56 | DEBUG | 处理消息内容: 'MJ绘画任务完成
提示词：asian woman laying on a bed in red on t shirt, in the style of karol bak, uniformly staged images, shilin huang, 32k uhd, dark amber and gray --ar 71:93 --stylize 750 --v 6
耗时：126.45秒'
2025-07-30 12:10:56 | DEBUG | 消息内容 'MJ绘画任务完成
提示词：asian woman laying on a bed in red on t shirt, in the style of karol bak, uniformly staged images, shilin huang, 32k uhd, dark amber and gray --ar 71:93 --stylize 750 --v 6
耗时：126.45秒' 不匹配任何命令，忽略
2025-07-30 12:11:07 | DEBUG | 收到消息: {'MsgId': 1162177348, 'FromUserName': {'string': '***********@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 3, 'Content': {'string': 'wxid_srknxij3jka022:\n<?xml version="1.0"?>\n<msg>\n\t<img aeskey="ed92758d54782ea571b46278f39c18a2" encryver="1" cdnthumbaeskey="ed92758d54782ea571b46278f39c18a2" cdnthumburl="3057020100044b304902010002049c5da34302032e6c63020455ab016f020468899b64042461323939383935302d306262662d346261312d386561312d396561393338303736653530020405252a010201000405004c4d9b00" cdnthumblength="5767" cdnthumbheight="120" cdnthumbwidth="92" cdnmidheight="0" cdnmidwidth="0" cdnhdheight="0" cdnhdwidth="0" cdnmidimgurl="3057020100044b304902010002049c5da34302032e6c63020455ab016f020468899b64042461323939383935302d306262662d346261312d386561312d396561393338303736653530020405252a010201000405004c4d9b00" length="37291" cdnbigimgurl="3057020100044b304902010002049c5da34302032e6c63020455ab016f020468899b64042461323939383935302d306262662d346261312d386561312d396561393338303736653530020405252a010201000405004c4d9b00" hdlength="8071301" md5="33347b3058a52cbdb54bce2246f291c1" hevc_mid_size="37291" originsourcemd5="33347b3058a52cbdb54bce2246f291c1">\n\t\t<secHashInfoBase64 />\n\t\t<live>\n\t\t\t<duration>0</duration>\n\t\t\t<size>0</size>\n\t\t\t<md5 />\n\t\t\t<fileid />\n\t\t\t<hdsize>0</hdsize>\n\t\t\t<hdmd5 />\n\t\t\t<hdfileid />\n\t\t\t<stillimagetimems>0</stillimagetimems>\n\t\t</live>\n\t</img>\n\t<appinfo>\n\t\t<appid>wxb0eef1f67b7a2949</appid>\n\t\t<version>4</version>\n\t\t<appname>国家反诈中心</appname>\n\t\t<isforceupdate>0</isforceupdate>\n\t\t<messageaction />\n\t\t<messageext />\n\t\t<mediatagname />\n\t</appinfo>\n\t<platform_signature />\n\t<imgdatahash />\n\t<ImgSourceInfo>\n\t\t<ImgSourceUrl />\n\t\t<BizType>0</BizType>\n\t</ImgSourceInfo>\n</msg>\n'}, 'Status': 3, 'ImgStatus': 2, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1753848679, 'MsgSource': '<msgsource>\n\t<alnode>\n\t\t<fr>2</fr>\n\t</alnode>\n\t<sec_msg_node>\n\t\t<uuid>11647773b1ea55860d1043f592d32b32_</uuid>\n\t\t<risk-file-flag />\n\t\t<risk-file-md5-list />\n\t</sec_msg_node>\n\t<silence>0</silence>\n\t<membercount>73</membercount>\n\t<signature>N0_V1_6iRhYGqm|v1_w0co4ZLD</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'PushContent': '她在群聊中发了一张图片', 'NewMsgId': 4357759627939714994, 'MsgSeq': 871411547}
2025-07-30 12:11:07 | INFO | 收到图片消息: 消息ID:1162177348 来自:***********@chatroom 发送人:wxid_srknxij3jka022 XML:<?xml version="1.0"?><msg><img aeskey="ed92758d54782ea571b46278f39c18a2" encryver="1" cdnthumbaeskey="ed92758d54782ea571b46278f39c18a2" cdnthumburl="3057020100044b304902010002049c5da34302032e6c63020455ab016f020468899b64042461323939383935302d306262662d346261312d386561312d396561393338303736653530020405252a010201000405004c4d9b00" cdnthumblength="5767" cdnthumbheight="120" cdnthumbwidth="92" cdnmidheight="0" cdnmidwidth="0" cdnhdheight="0" cdnhdwidth="0" cdnmidimgurl="3057020100044b304902010002049c5da34302032e6c63020455ab016f020468899b64042461323939383935302d306262662d346261312d386561312d396561393338303736653530020405252a010201000405004c4d9b00" length="37291" cdnbigimgurl="3057020100044b304902010002049c5da34302032e6c63020455ab016f020468899b64042461323939383935302d306262662d346261312d386561312d396561393338303736653530020405252a010201000405004c4d9b00" hdlength="8071301" md5="33347b3058a52cbdb54bce2246f291c1" hevc_mid_size="37291" originsourcemd5="33347b3058a52cbdb54bce2246f291c1"><secHashInfoBase64 /><live><duration>0</duration><size>0</size><md5 /><fileid /><hdsize>0</hdsize><hdmd5 /><hdfileid /><stillimagetimems>0</stillimagetimems></live></img><appinfo><appid>wxb0eef1f67b7a2949</appid><version>4</version><appname>国家反诈中心</appname><isforceupdate>0</isforceupdate><messageaction /><messageext /><mediatagname /></appinfo><platform_signature /><imgdatahash /><ImgSourceInfo><ImgSourceUrl /><BizType>0</BizType></ImgSourceInfo></msg>
2025-07-30 12:11:07 | INFO | [ImageEcho] 保存图片信息成功，当前群 ***********@chatroom 已存储 5 张图片
2025-07-30 12:11:07 | INFO | [TimerTask] 缓存图片消息: 1162177348
2025-07-30 12:11:07 | DEBUG | 收到消息: {'MsgId': 762515339, 'FromUserName': {'string': '***********@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 3, 'Content': {'string': 'wxid_srknxij3jka022:\n<?xml version="1.0"?>\n<msg>\n\t<img aeskey="c861c2b56672441758555580b272cf7d" encryver="1" cdnthumbaeskey="c861c2b56672441758555580b272cf7d" cdnthumburl="3057020100044b304902010002049c5da34302032e6c63020455ab016f020468899b67042437373433373134312d333036322d346534632d383362342d366536323463663136636336020405292a010201000405004c537700" cdnthumblength="5184" cdnthumbheight="120" cdnthumbwidth="92" cdnmidheight="0" cdnmidwidth="0" cdnhdheight="0" cdnhdwidth="0" cdnmidimgurl="3057020100044b304902010002049c5da34302032e6c63020455ab016f020468899b67042437373433373134312d333036322d346534632d383362342d366536323463663136636336020405292a010201000405004c537700" length="24991" cdnbigimgurl="3057020100044b304902010002049c5da34302032e6c63020455ab016f020468899b67042437373433373134312d333036322d346534632d383362342d366536323463663136636336020405292a010201000405004c537700" hdlength="414288" md5="65c99cc1a75fa59597994817523951fe" hevc_mid_size="24991" originsourcemd5="65c99cc1a75fa59597994817523951fe">\n\t\t<secHashInfoBase64 />\n\t\t<live>\n\t\t\t<duration>0</duration>\n\t\t\t<size>0</size>\n\t\t\t<md5 />\n\t\t\t<fileid />\n\t\t\t<hdsize>0</hdsize>\n\t\t\t<hdmd5 />\n\t\t\t<hdfileid />\n\t\t\t<stillimagetimems>0</stillimagetimems>\n\t\t</live>\n\t</img>\n\t<appinfo>\n\t\t<appid>wxe3ad19e142df87b3</appid>\n\t\t<version>5</version>\n\t\t<appname>麻豆约拍</appname>\n\t\t<isforceupdate>0</isforceupdate>\n\t\t<messageaction />\n\t\t<messageext />\n\t\t<mediatagname />\n\t</appinfo>\n\t<platform_signature />\n\t<imgdatahash />\n\t<ImgSourceInfo>\n\t\t<ImgSourceUrl />\n\t\t<BizType>0</BizType>\n\t</ImgSourceInfo>\n</msg>\n'}, 'Status': 3, 'ImgStatus': 2, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1753848679, 'MsgSource': '<msgsource>\n\t<alnode>\n\t\t<fr>2</fr>\n\t</alnode>\n\t<sec_msg_node>\n\t\t<uuid>c314b5b40c3b20d1115c2f39cf4ef299_</uuid>\n\t\t<risk-file-flag />\n\t\t<risk-file-md5-list />\n\t</sec_msg_node>\n\t<silence>0</silence>\n\t<membercount>73</membercount>\n\t<signature>N0_V1_a4cMowhB|v1_ldDdibWP</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'PushContent': '她在群聊中发了一张图片', 'NewMsgId': 22859552143168783, 'MsgSeq': 871411548}
2025-07-30 12:11:07 | INFO | 收到图片消息: 消息ID:762515339 来自:***********@chatroom 发送人:wxid_srknxij3jka022 XML:<?xml version="1.0"?><msg><img aeskey="c861c2b56672441758555580b272cf7d" encryver="1" cdnthumbaeskey="c861c2b56672441758555580b272cf7d" cdnthumburl="3057020100044b304902010002049c5da34302032e6c63020455ab016f020468899b67042437373433373134312d333036322d346534632d383362342d366536323463663136636336020405292a010201000405004c537700" cdnthumblength="5184" cdnthumbheight="120" cdnthumbwidth="92" cdnmidheight="0" cdnmidwidth="0" cdnhdheight="0" cdnhdwidth="0" cdnmidimgurl="3057020100044b304902010002049c5da34302032e6c63020455ab016f020468899b67042437373433373134312d333036322d346534632d383362342d366536323463663136636336020405292a010201000405004c537700" length="24991" cdnbigimgurl="3057020100044b304902010002049c5da34302032e6c63020455ab016f020468899b67042437373433373134312d333036322d346534632d383362342d366536323463663136636336020405292a010201000405004c537700" hdlength="414288" md5="65c99cc1a75fa59597994817523951fe" hevc_mid_size="24991" originsourcemd5="65c99cc1a75fa59597994817523951fe"><secHashInfoBase64 /><live><duration>0</duration><size>0</size><md5 /><fileid /><hdsize>0</hdsize><hdmd5 /><hdfileid /><stillimagetimems>0</stillimagetimems></live></img><appinfo><appid>wxe3ad19e142df87b3</appid><version>5</version><appname>麻豆约拍</appname><isforceupdate>0</isforceupdate><messageaction /><messageext /><mediatagname /></appinfo><platform_signature /><imgdatahash /><ImgSourceInfo><ImgSourceUrl /><BizType>0</BizType></ImgSourceInfo></msg>
2025-07-30 12:11:08 | INFO | [ImageEcho] 保存图片信息成功，当前群 ***********@chatroom 已存储 5 张图片
2025-07-30 12:11:08 | INFO | [TimerTask] 缓存图片消息: 762515339
2025-07-30 12:11:08 | DEBUG | 收到消息: {'MsgId': 574809993, 'FromUserName': {'string': '***********@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 3, 'Content': {'string': 'wxid_srknxij3jka022:\n<?xml version="1.0"?>\n<msg>\n\t<img aeskey="5288389d2002d5731f61515646e6dc54" encryver="1" cdnthumbaeskey="5288389d2002d5731f61515646e6dc54" cdnthumburl="3057020100044b304902010002049c5da34302032e6c63020455ab016f020468899b67042462393264333634352d653933382d343633322d626334322d363931643631363737633838020405252a010201000405004c550700" cdnthumblength="5294" cdnthumbheight="120" cdnthumbwidth="92" cdnmidheight="0" cdnmidwidth="0" cdnhdheight="0" cdnhdwidth="0" cdnmidimgurl="3057020100044b304902010002049c5da34302032e6c63020455ab016f020468899b67042462393264333634352d653933382d343633322d626334322d363931643631363737633838020405252a010201000405004c550700" length="30120" cdnbigimgurl="3057020100044b304902010002049c5da34302032e6c63020455ab016f020468899b67042462393264333634352d653933382d343633322d626334322d363931643631363737633838020405252a010201000405004c550700" hdlength="433903" md5="fc0cae15f6af8a429ab9d55ecf811f2c" hevc_mid_size="30120" originsourcemd5="fc0cae15f6af8a429ab9d55ecf811f2c">\n\t\t<secHashInfoBase64 />\n\t\t<live>\n\t\t\t<duration>0</duration>\n\t\t\t<size>0</size>\n\t\t\t<md5 />\n\t\t\t<fileid />\n\t\t\t<hdsize>0</hdsize>\n\t\t\t<hdmd5 />\n\t\t\t<hdfileid />\n\t\t\t<stillimagetimems>0</stillimagetimems>\n\t\t</live>\n\t</img>\n\t<appinfo>\n\t\t<appid>wxb0eef1f67b7a2949</appid>\n\t\t<version>4</version>\n\t\t<appname>国家反诈中心</appname>\n\t\t<isforceupdate>0</isforceupdate>\n\t\t<messageaction />\n\t\t<messageext />\n\t\t<mediatagname />\n\t</appinfo>\n\t<platform_signature />\n\t<imgdatahash />\n\t<ImgSourceInfo>\n\t\t<ImgSourceUrl />\n\t\t<BizType>0</BizType>\n\t</ImgSourceInfo>\n</msg>\n'}, 'Status': 3, 'ImgStatus': 2, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1753848680, 'MsgSource': '<msgsource>\n\t<alnode>\n\t\t<fr>2</fr>\n\t</alnode>\n\t<sec_msg_node>\n\t\t<uuid>bdc2a00a0a7a3e2a794df215a0e252ce_</uuid>\n\t\t<risk-file-flag />\n\t\t<risk-file-md5-list />\n\t</sec_msg_node>\n\t<silence>0</silence>\n\t<membercount>73</membercount>\n\t<signature>N0_V1_Ryg5o2rW|v1_S8/frqLW</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'PushContent': '她在群聊中发了一张图片', 'NewMsgId': 2170256115409206800, 'MsgSeq': 871411549}
2025-07-30 12:11:08 | INFO | 收到图片消息: 消息ID:574809993 来自:***********@chatroom 发送人:wxid_srknxij3jka022 XML:<?xml version="1.0"?><msg><img aeskey="5288389d2002d5731f61515646e6dc54" encryver="1" cdnthumbaeskey="5288389d2002d5731f61515646e6dc54" cdnthumburl="3057020100044b304902010002049c5da34302032e6c63020455ab016f020468899b67042462393264333634352d653933382d343633322d626334322d363931643631363737633838020405252a010201000405004c550700" cdnthumblength="5294" cdnthumbheight="120" cdnthumbwidth="92" cdnmidheight="0" cdnmidwidth="0" cdnhdheight="0" cdnhdwidth="0" cdnmidimgurl="3057020100044b304902010002049c5da34302032e6c63020455ab016f020468899b67042462393264333634352d653933382d343633322d626334322d363931643631363737633838020405252a010201000405004c550700" length="30120" cdnbigimgurl="3057020100044b304902010002049c5da34302032e6c63020455ab016f020468899b67042462393264333634352d653933382d343633322d626334322d363931643631363737633838020405252a010201000405004c550700" hdlength="433903" md5="fc0cae15f6af8a429ab9d55ecf811f2c" hevc_mid_size="30120" originsourcemd5="fc0cae15f6af8a429ab9d55ecf811f2c"><secHashInfoBase64 /><live><duration>0</duration><size>0</size><md5 /><fileid /><hdsize>0</hdsize><hdmd5 /><hdfileid /><stillimagetimems>0</stillimagetimems></live></img><appinfo><appid>wxb0eef1f67b7a2949</appid><version>4</version><appname>国家反诈中心</appname><isforceupdate>0</isforceupdate><messageaction /><messageext /><mediatagname /></appinfo><platform_signature /><imgdatahash /><ImgSourceInfo><ImgSourceUrl /><BizType>0</BizType></ImgSourceInfo></msg>
2025-07-30 12:11:09 | INFO | [ImageEcho] 保存图片信息成功，当前群 ***********@chatroom 已存储 5 张图片
2025-07-30 12:11:09 | INFO | [TimerTask] 缓存图片消息: 574809993
2025-07-30 12:11:09 | DEBUG | 收到消息: {'MsgId': 778374663, 'FromUserName': {'string': '***********@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 3, 'Content': {'string': 'wxid_srknxij3jka022:\n<?xml version="1.0"?>\n<msg>\n\t<img aeskey="dfb51dc6073dcee3f39631ab5718047c" encryver="1" cdnthumbaeskey="dfb51dc6073dcee3f39631ab5718047c" cdnthumburl="3057020100044b304902010002049c5da34302032e6c63020455ab016f020468899b69042464333234633638312d383737382d343037312d386565622d646164376264626133646138020405252a010201000405004c4e6300" cdnthumblength="5566" cdnthumbheight="120" cdnthumbwidth="92" cdnmidheight="0" cdnmidwidth="0" cdnhdheight="0" cdnhdwidth="0" cdnmidimgurl="3057020100044b304902010002049c5da34302032e6c63020455ab016f020468899b69042464333234633638312d383737382d343037312d386565622d646164376264626133646138020405252a010201000405004c4e6300" length="31620" cdnbigimgurl="3057020100044b304902010002049c5da34302032e6c63020455ab016f020468899b69042464333234633638312d383737382d343037312d386565622d646164376264626133646138020405252a010201000405004c4e6300" hdlength="456816" md5="ce33de957b73e12a215d5dcaaca77941" hevc_mid_size="31620" originsourcemd5="ce33de957b73e12a215d5dcaaca77941">\n\t\t<secHashInfoBase64 />\n\t\t<live>\n\t\t\t<duration>0</duration>\n\t\t\t<size>0</size>\n\t\t\t<md5 />\n\t\t\t<fileid />\n\t\t\t<hdsize>0</hdsize>\n\t\t\t<hdmd5 />\n\t\t\t<hdfileid />\n\t\t\t<stillimagetimems>0</stillimagetimems>\n\t\t</live>\n\t</img>\n\t<appinfo>\n\t\t<appid>wxe3ad19e142df87b3</appid>\n\t\t<version>5</version>\n\t\t<appname>麻豆约拍</appname>\n\t\t<isforceupdate>0</isforceupdate>\n\t\t<messageaction />\n\t\t<messageext />\n\t\t<mediatagname />\n\t</appinfo>\n\t<platform_signature />\n\t<imgdatahash />\n\t<ImgSourceInfo>\n\t\t<ImgSourceUrl />\n\t\t<BizType>0</BizType>\n\t</ImgSourceInfo>\n</msg>\n'}, 'Status': 3, 'ImgStatus': 2, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1753848681, 'MsgSource': '<msgsource>\n\t<alnode>\n\t\t<fr>2</fr>\n\t</alnode>\n\t<sec_msg_node>\n\t\t<uuid>dedf0db0968eed53abcd2ccc6d37d3ce_</uuid>\n\t\t<risk-file-flag />\n\t\t<risk-file-md5-list />\n\t</sec_msg_node>\n\t<silence>0</silence>\n\t<membercount>73</membercount>\n\t<signature>N0_V1_F3S/kmoQ|v1_k3obwjBi</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'PushContent': '她在群聊中发了一张图片', 'NewMsgId': 3229273099318948471, 'MsgSeq': 871411550}
2025-07-30 12:11:09 | INFO | 收到图片消息: 消息ID:778374663 来自:***********@chatroom 发送人:wxid_srknxij3jka022 XML:<?xml version="1.0"?><msg><img aeskey="dfb51dc6073dcee3f39631ab5718047c" encryver="1" cdnthumbaeskey="dfb51dc6073dcee3f39631ab5718047c" cdnthumburl="3057020100044b304902010002049c5da34302032e6c63020455ab016f020468899b69042464333234633638312d383737382d343037312d386565622d646164376264626133646138020405252a010201000405004c4e6300" cdnthumblength="5566" cdnthumbheight="120" cdnthumbwidth="92" cdnmidheight="0" cdnmidwidth="0" cdnhdheight="0" cdnhdwidth="0" cdnmidimgurl="3057020100044b304902010002049c5da34302032e6c63020455ab016f020468899b69042464333234633638312d383737382d343037312d386565622d646164376264626133646138020405252a010201000405004c4e6300" length="31620" cdnbigimgurl="3057020100044b304902010002049c5da34302032e6c63020455ab016f020468899b69042464333234633638312d383737382d343037312d386565622d646164376264626133646138020405252a010201000405004c4e6300" hdlength="456816" md5="ce33de957b73e12a215d5dcaaca77941" hevc_mid_size="31620" originsourcemd5="ce33de957b73e12a215d5dcaaca77941"><secHashInfoBase64 /><live><duration>0</duration><size>0</size><md5 /><fileid /><hdsize>0</hdsize><hdmd5 /><hdfileid /><stillimagetimems>0</stillimagetimems></live></img><appinfo><appid>wxe3ad19e142df87b3</appid><version>5</version><appname>麻豆约拍</appname><isforceupdate>0</isforceupdate><messageaction /><messageext /><mediatagname /></appinfo><platform_signature /><imgdatahash /><ImgSourceInfo><ImgSourceUrl /><BizType>0</BizType></ImgSourceInfo></msg>
2025-07-30 12:11:10 | INFO | [ImageEcho] 保存图片信息成功，当前群 ***********@chatroom 已存储 5 张图片
2025-07-30 12:11:10 | INFO | [TimerTask] 缓存图片消息: 778374663
2025-07-30 12:11:10 | DEBUG | 收到消息: {'MsgId': 942514623, 'FromUserName': {'string': '***********@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 3, 'Content': {'string': 'wxid_srknxij3jka022:\n<?xml version="1.0"?>\n<msg>\n\t<img aeskey="12b0da34bbddeee5c177785bd1706bea" encryver="1" cdnthumbaeskey="12b0da34bbddeee5c177785bd1706bea" cdnthumburl="3057020100044b304902010002049c5da34302032e6c63020455ab016f020468899b6a042465306463323164382d623231392d346230392d613763392d663566306633643332366535020405292a010201000405004c505700" cdnthumblength="5750" cdnthumbheight="120" cdnthumbwidth="92" cdnmidheight="0" cdnmidwidth="0" cdnhdheight="0" cdnhdwidth="0" cdnmidimgurl="3057020100044b304902010002049c5da34302032e6c63020455ab016f020468899b6a042465306463323164382d623231392d346230392d613763392d663566306633643332366535020405292a010201000405004c505700" length="34615" cdnbigimgurl="3057020100044b304902010002049c5da34302032e6c63020455ab016f020468899b6a042465306463323164382d623231392d346230392d613763392d663566306633643332366535020405292a010201000405004c505700" hdlength="410701" md5="97855b25ce6f2a949c27f5fd66a88987" hevc_mid_size="34615" originsourcemd5="97855b25ce6f2a949c27f5fd66a88987">\n\t\t<secHashInfoBase64 />\n\t\t<live>\n\t\t\t<duration>0</duration>\n\t\t\t<size>0</size>\n\t\t\t<md5 />\n\t\t\t<fileid />\n\t\t\t<hdsize>0</hdsize>\n\t\t\t<hdmd5 />\n\t\t\t<hdfileid />\n\t\t\t<stillimagetimems>0</stillimagetimems>\n\t\t</live>\n\t</img>\n\t<appinfo>\n\t\t<appid>wx92398516de814096</appid>\n\t\t<version>7</version>\n\t\t<appname>梅赛德斯一奔驰</appname>\n\t\t<isforceupdate>0</isforceupdate>\n\t\t<messageaction />\n\t\t<messageext />\n\t\t<mediatagname />\n\t</appinfo>\n\t<platform_signature />\n\t<imgdatahash />\n\t<ImgSourceInfo>\n\t\t<ImgSourceUrl />\n\t\t<BizType>0</BizType>\n\t</ImgSourceInfo>\n</msg>\n'}, 'Status': 3, 'ImgStatus': 2, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1753848682, 'MsgSource': '<msgsource>\n\t<alnode>\n\t\t<fr>2</fr>\n\t</alnode>\n\t<sec_msg_node>\n\t\t<uuid>6271251943b379faa1157a633440d160_</uuid>\n\t\t<risk-file-flag />\n\t\t<risk-file-md5-list />\n\t</sec_msg_node>\n\t<silence>0</silence>\n\t<membercount>73</membercount>\n\t<signature>N0_V1_tLkOuo18|v1_0bZcFda0</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'PushContent': '她在群聊中发了一张图片', 'NewMsgId': 1010830426572857507, 'MsgSeq': 871411551}
2025-07-30 12:11:10 | INFO | 收到图片消息: 消息ID:942514623 来自:***********@chatroom 发送人:wxid_srknxij3jka022 XML:<?xml version="1.0"?><msg><img aeskey="12b0da34bbddeee5c177785bd1706bea" encryver="1" cdnthumbaeskey="12b0da34bbddeee5c177785bd1706bea" cdnthumburl="3057020100044b304902010002049c5da34302032e6c63020455ab016f020468899b6a042465306463323164382d623231392d346230392d613763392d663566306633643332366535020405292a010201000405004c505700" cdnthumblength="5750" cdnthumbheight="120" cdnthumbwidth="92" cdnmidheight="0" cdnmidwidth="0" cdnhdheight="0" cdnhdwidth="0" cdnmidimgurl="3057020100044b304902010002049c5da34302032e6c63020455ab016f020468899b6a042465306463323164382d623231392d346230392d613763392d663566306633643332366535020405292a010201000405004c505700" length="34615" cdnbigimgurl="3057020100044b304902010002049c5da34302032e6c63020455ab016f020468899b6a042465306463323164382d623231392d346230392d613763392d663566306633643332366535020405292a010201000405004c505700" hdlength="410701" md5="97855b25ce6f2a949c27f5fd66a88987" hevc_mid_size="34615" originsourcemd5="97855b25ce6f2a949c27f5fd66a88987"><secHashInfoBase64 /><live><duration>0</duration><size>0</size><md5 /><fileid /><hdsize>0</hdsize><hdmd5 /><hdfileid /><stillimagetimems>0</stillimagetimems></live></img><appinfo><appid>wx92398516de814096</appid><version>7</version><appname>梅赛德斯一奔驰</appname><isforceupdate>0</isforceupdate><messageaction /><messageext /><mediatagname /></appinfo><platform_signature /><imgdatahash /><ImgSourceInfo><ImgSourceUrl /><BizType>0</BizType></ImgSourceInfo></msg>
2025-07-30 12:11:11 | INFO | [ImageEcho] 保存图片信息成功，当前群 ***********@chatroom 已存储 5 张图片
2025-07-30 12:11:11 | INFO | [TimerTask] 缓存图片消息: 942514623
2025-07-30 12:11:13 | DEBUG | 收到消息: {'MsgId': 1280826177, 'FromUserName': {'string': '***********@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 1, 'Content': {'string': 'wxid_srknxij3jka022:\n老飞宇《挑战两个小时吃7000万》一：真正的搜打撤，不到10分钟撤离800万！\n#老飞宇66 #三角洲行动 #三角洲猛攻 #航天基地没有懦夫 #老飞宇挑战两个小时吃7000万'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1753848685, 'MsgSource': '<msgsource>\n\t<pua>1</pua>\n\t<silence>0</silence>\n\t<membercount>73</membercount>\n\t<signature>N0_V1_i87OoyM8|v1_Yfwgy5yl</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'PushContent': '她 : 老飞宇《挑战两个小时吃7000万》一：真正的搜打撤，不到10分钟撤离...', 'NewMsgId': 1691512259585655241, 'MsgSeq': 871411552}
2025-07-30 12:11:13 | INFO | 收到文本消息: 消息ID:1280826177 来自:***********@chatroom 发送人:wxid_srknxij3jka022 @:[] 内容:老飞宇《挑战两个小时吃7000万》一：真正的搜打撤，不到10分钟撤离800万！
#老飞宇66 #三角洲行动 #三角洲猛攻 #航天基地没有懦夫 #老飞宇挑战两个小时吃7000万
2025-07-30 12:11:13 | DEBUG | 处理消息内容: '老飞宇《挑战两个小时吃7000万》一：真正的搜打撤，不到10分钟撤离800万！
#老飞宇66 #三角洲行动 #三角洲猛攻 #航天基地没有懦夫 #老飞宇挑战两个小时吃7000万'
2025-07-30 12:11:13 | DEBUG | 消息内容 '老飞宇《挑战两个小时吃7000万》一：真正的搜打撤，不到10分钟撤离800万！
#老飞宇66 #三角洲行动 #三角洲猛攻 #航天基地没有懦夫 #老飞宇挑战两个小时吃7000万' 不匹配任何命令，忽略
2025-07-30 12:11:15 | DEBUG | 收到消息: {'MsgId': 476993049, 'FromUserName': {'string': '***********@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 3, 'Content': {'string': 'wxid_srknxij3jka022:\n<?xml version="1.0"?>\n<msg>\n\t<img aeskey="ad4ae23cf08d68a41277595d07bef5cf" encryver="1" cdnthumbaeskey="ad4ae23cf08d68a41277595d07bef5cf" cdnthumburl="3057020100044b304902010002049c5da34302032e6c63020455ab016f020468899b6e042464613932666164382d323561322d343364322d623161622d633763666330343433323964020405292a010201000405004c537700" cdnthumblength="5881" cdnthumbheight="120" cdnthumbwidth="90" cdnmidheight="0" cdnmidwidth="0" cdnhdheight="0" cdnhdwidth="0" cdnmidimgurl="3057020100044b304902010002049c5da34302032e6c63020455ab016f020468899b6e042464613932666164382d323561322d343364322d623161622d633763666330343433323964020405292a010201000405004c537700" length="15224" cdnbigimgurl="3057020100044b304902010002049c5da34302032e6c63020455ab016f020468899b6e042464613932666164382d323561322d343364322d623161622d633763666330343433323964020405292a010201000405004c537700" hdlength="58618" md5="5890e3ce46e9c70fa56a33fbe449e841" hevc_mid_size="15224" originsourcemd5="d5a44bad3c570ce9eff01df388efc69c">\n\t\t<secHashInfoBase64 />\n\t\t<live>\n\t\t\t<duration>0</duration>\n\t\t\t<size>0</size>\n\t\t\t<md5 />\n\t\t\t<fileid />\n\t\t\t<hdsize>0</hdsize>\n\t\t\t<hdmd5 />\n\t\t\t<hdfileid />\n\t\t\t<stillimagetimems>0</stillimagetimems>\n\t\t</live>\n\t</img>\n\t<appinfo>\n\t\t<appid>wx0e054ce4ae213018</appid>\n\t\t<version>20</version>\n\t\t<appname>国务院客户端</appname>\n\t\t<isforceupdate>0</isforceupdate>\n\t\t<messageaction />\n\t\t<messageext />\n\t\t<mediatagname />\n\t</appinfo>\n\t<platform_signature />\n\t<imgdatahash />\n\t<ImgSourceInfo>\n\t\t<ImgSourceUrl />\n\t\t<BizType>0</BizType>\n\t</ImgSourceInfo>\n</msg>\n'}, 'Status': 3, 'ImgStatus': 2, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1753848687, 'MsgSource': '<msgsource>\n\t<alnode>\n\t\t<fr>2</fr>\n\t</alnode>\n\t<sec_msg_node>\n\t\t<uuid>6f2d2ea1237337920557186ed808b50a_</uuid>\n\t\t<risk-file-flag />\n\t\t<risk-file-md5-list />\n\t</sec_msg_node>\n\t<silence>0</silence>\n\t<membercount>73</membercount>\n\t<signature>N0_V1_GTUwzteZ|v1_BbylmZWI</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'PushContent': '她在群聊中发了一张图片', 'NewMsgId': 7055321856790887672, 'MsgSeq': 871411553}
2025-07-30 12:11:15 | INFO | 收到图片消息: 消息ID:476993049 来自:***********@chatroom 发送人:wxid_srknxij3jka022 XML:<?xml version="1.0"?><msg><img aeskey="ad4ae23cf08d68a41277595d07bef5cf" encryver="1" cdnthumbaeskey="ad4ae23cf08d68a41277595d07bef5cf" cdnthumburl="3057020100044b304902010002049c5da34302032e6c63020455ab016f020468899b6e042464613932666164382d323561322d343364322d623161622d633763666330343433323964020405292a010201000405004c537700" cdnthumblength="5881" cdnthumbheight="120" cdnthumbwidth="90" cdnmidheight="0" cdnmidwidth="0" cdnhdheight="0" cdnhdwidth="0" cdnmidimgurl="3057020100044b304902010002049c5da34302032e6c63020455ab016f020468899b6e042464613932666164382d323561322d343364322d623161622d633763666330343433323964020405292a010201000405004c537700" length="15224" cdnbigimgurl="3057020100044b304902010002049c5da34302032e6c63020455ab016f020468899b6e042464613932666164382d323561322d343364322d623161622d633763666330343433323964020405292a010201000405004c537700" hdlength="58618" md5="5890e3ce46e9c70fa56a33fbe449e841" hevc_mid_size="15224" originsourcemd5="d5a44bad3c570ce9eff01df388efc69c"><secHashInfoBase64 /><live><duration>0</duration><size>0</size><md5 /><fileid /><hdsize>0</hdsize><hdmd5 /><hdfileid /><stillimagetimems>0</stillimagetimems></live></img><appinfo><appid>wx0e054ce4ae213018</appid><version>20</version><appname>国务院客户端</appname><isforceupdate>0</isforceupdate><messageaction /><messageext /><mediatagname /></appinfo><platform_signature /><imgdatahash /><ImgSourceInfo><ImgSourceUrl /><BizType>0</BizType></ImgSourceInfo></msg>
2025-07-30 12:11:16 | INFO | [ImageEcho] 保存图片信息成功，当前群 ***********@chatroom 已存储 5 张图片
2025-07-30 12:11:16 | INFO | [TimerTask] 缓存图片消息: 476993049
2025-07-30 12:11:17 | DEBUG | 收到消息: {'MsgId': 1632739009, 'FromUserName': {'string': '***********@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 1, 'Content': {'string': 'wxid_jegyk4i3v7zg22:\n这才对嘛'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1753848689, 'MsgSource': '<msgsource>\n\t<bizflag>0</bizflag>\n\t<pua>1</pua>\n\t<eggIncluded>1</eggIncluded>\n\t<silence>0</silence>\n\t<membercount>73</membercount>\n\t<signature>N0_V1_7eMSlsIG|v1_CTjcdN6K</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'PushContent': '阿尼亚与她 : 这才对嘛', 'NewMsgId': 1267008616433469109, 'MsgSeq': 871411554}
2025-07-30 12:11:17 | INFO | 收到文本消息: 消息ID:1632739009 来自:***********@chatroom 发送人:wxid_jegyk4i3v7zg22 @:[] 内容:这才对嘛
2025-07-30 12:11:17 | DEBUG | 处理消息内容: '这才对嘛'
2025-07-30 12:11:17 | DEBUG | 消息内容 '这才对嘛' 不匹配任何命令，忽略
2025-07-30 12:11:22 | DEBUG | 收到消息: {'MsgId': 1815696873, 'FromUserName': {'string': '***********@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 1, 'Content': {'string': 'wxid_jegyk4i3v7zg22:\nperfect'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1753848694, 'MsgSource': '<msgsource>\n\t<bizflag>0</bizflag>\n\t<pua>1</pua>\n\t<alnode>\n\t\t<inlenlist>7</inlenlist>\n\t</alnode>\n\t<eggIncluded>1</eggIncluded>\n\t<silence>0</silence>\n\t<membercount>73</membercount>\n\t<signature>N0_V1_DPX+KUM1|v1_0U534tIS</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'PushContent': '阿尼亚与她 : perfect', 'NewMsgId': 1852945215385540427, 'MsgSeq': 871411555}
2025-07-30 12:11:22 | INFO | 收到文本消息: 消息ID:1815696873 来自:***********@chatroom 发送人:wxid_jegyk4i3v7zg22 @:[] 内容:perfect
2025-07-30 12:11:22 | DEBUG | 处理消息内容: 'perfect'
2025-07-30 12:11:22 | DEBUG | 消息内容 'perfect' 不匹配任何命令，忽略
2025-07-30 12:11:29 | DEBUG | 收到消息: {'MsgId': 1762390701, 'FromUserName': {'string': '***********@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 3, 'Content': {'string': 'xiaomaochong:\n<?xml version="1.0"?>\n<msg>\n\t<img aeskey="98c4843b3a239664983709caa60ca699" encryver="1" cdnthumbaeskey="98c4843b3a239664983709caa60ca699" cdnthumburl="3057020100044b304902010002049363814102032f51490204f3328e71020468899b7d042437326266656439612d643732392d343966312d626633612d616232393639626564353464020405292a010201000405004c4d3500" cdnthumblength="5881" cdnthumbheight="120" cdnthumbwidth="90" cdnmidheight="0" cdnmidwidth="0" cdnhdheight="0" cdnhdwidth="0" cdnmidimgurl="3057020100044b304902010002049363814102032f51490204f3328e71020468899b7d042437326266656439612d643732392d343966312d626633612d616232393639626564353464020405292a010201000405004c4d3500" length="15224" cdnbigimgurl="3057020100044b304902010002049363814102032f51490204f3328e71020468899b7d042437326266656439612d643732392d343966312d626633612d616232393639626564353464020405292a010201000405004c4d3500" hdlength="58618" md5="5890e3ce46e9c70fa56a33fbe449e841" hevc_mid_size="15224" originsourcemd5="d5a44bad3c570ce9eff01df388efc69c">\n\t\t<secHashInfoBase64 />\n\t\t<live>\n\t\t\t<duration>0</duration>\n\t\t\t<size>0</size>\n\t\t\t<md5 />\n\t\t\t<fileid />\n\t\t\t<hdsize>0</hdsize>\n\t\t\t<hdmd5 />\n\t\t\t<hdfileid />\n\t\t\t<stillimagetimems>0</stillimagetimems>\n\t\t</live>\n\t</img>\n\t<appinfo>\n\t\t<appid>wx0e054ce4ae213018</appid>\n\t\t<version>20</version>\n\t\t<appname>国务院客户端</appname>\n\t\t<isforceupdate>0</isforceupdate>\n\t\t<messageaction />\n\t\t<messageext />\n\t\t<mediatagname />\n\t</appinfo>\n\t<platform_signature />\n\t<imgdatahash />\n\t<ImgSourceInfo>\n\t\t<ImgSourceUrl />\n\t\t<BizType>0</BizType>\n\t</ImgSourceInfo>\n</msg>\n'}, 'Status': 3, 'ImgStatus': 2, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1753848701, 'MsgSource': '<msgsource>\n\t<alnode>\n\t\t<fr>2</fr>\n\t</alnode>\n\t<sec_msg_node>\n\t\t<uuid>e11bdff973ea1368d793b8c89351a54f_</uuid>\n\t\t<risk-file-flag />\n\t\t<risk-file-md5-list />\n\t</sec_msg_node>\n\t<silence>0</silence>\n\t<membercount>73</membercount>\n\t<signature>N0_V1_QfkDZn0F|v1_dqHnZvPf</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'PushContent': '小爱在群聊中发了一张图片', 'NewMsgId': 7622387556613119821, 'MsgSeq': 871411556}
2025-07-30 12:11:29 | INFO | 收到图片消息: 消息ID:1762390701 来自:***********@chatroom 发送人:xiaomaochong XML:<?xml version="1.0"?><msg><img aeskey="98c4843b3a239664983709caa60ca699" encryver="1" cdnthumbaeskey="98c4843b3a239664983709caa60ca699" cdnthumburl="3057020100044b304902010002049363814102032f51490204f3328e71020468899b7d042437326266656439612d643732392d343966312d626633612d616232393639626564353464020405292a010201000405004c4d3500" cdnthumblength="5881" cdnthumbheight="120" cdnthumbwidth="90" cdnmidheight="0" cdnmidwidth="0" cdnhdheight="0" cdnhdwidth="0" cdnmidimgurl="3057020100044b304902010002049363814102032f51490204f3328e71020468899b7d042437326266656439612d643732392d343966312d626633612d616232393639626564353464020405292a010201000405004c4d3500" length="15224" cdnbigimgurl="3057020100044b304902010002049363814102032f51490204f3328e71020468899b7d042437326266656439612d643732392d343966312d626633612d616232393639626564353464020405292a010201000405004c4d3500" hdlength="58618" md5="5890e3ce46e9c70fa56a33fbe449e841" hevc_mid_size="15224" originsourcemd5="d5a44bad3c570ce9eff01df388efc69c"><secHashInfoBase64 /><live><duration>0</duration><size>0</size><md5 /><fileid /><hdsize>0</hdsize><hdmd5 /><hdfileid /><stillimagetimems>0</stillimagetimems></live></img><appinfo><appid>wx0e054ce4ae213018</appid><version>20</version><appname>国务院客户端</appname><isforceupdate>0</isforceupdate><messageaction /><messageext /><mediatagname /></appinfo><platform_signature /><imgdatahash /><ImgSourceInfo><ImgSourceUrl /><BizType>0</BizType></ImgSourceInfo></msg>
2025-07-30 12:11:30 | INFO | [ImageEcho] 保存图片信息成功，当前群 ***********@chatroom 已存储 5 张图片
2025-07-30 12:11:30 | INFO | [TimerTask] 缓存图片消息: 1762390701
2025-07-30 12:11:41 | DEBUG | 收到消息: {'MsgId': 614253662, 'FromUserName': {'string': '***********@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 47, 'Content': {'string': 'wxid_jegyk4i3v7zg22:\n<msg><emoji fromusername="wxid_jegyk4i3v7zg22" tousername="***********@chatroom" type="2" idbuffer="media:0_0" md5="6bf139b37c4fc9a1ae052bedf4bdc8db" len="1496415" productid="" androidmd5="6bf139b37c4fc9a1ae052bedf4bdc8db" androidlen="1496415" s60v3md5="6bf139b37c4fc9a1ae052bedf4bdc8db" s60v3len="1496415" s60v5md5="6bf139b37c4fc9a1ae052bedf4bdc8db" s60v5len="1496415" cdnurl="http://vweixinf.tc.qq.com/110/20401/stodownload?m=6bf139b37c4fc9a1ae052bedf4bdc8db&amp;filekey=30440201010430302e02016e0402535a04203662663133396233376334666339613161653035326265646634626463386462020316d55f040d00000004627466730000000132&amp;hy=SZ&amp;storeid=26826d4cc0000e2358df7c9dd0000006e01004fb1535a138f101157f23f6f7&amp;ef=1&amp;bizid=1022" designerid="" thumburl="" encrypturl="http://vweixinf.tc.qq.com/110/20402/stodownload?m=ce92703d21cab4950a12d1be3697156a&amp;filekey=30440201010430302e02016e0402535a04206365393237303364323163616234393530613132643162653336393731353661020316d560040d00000004627466730000000132&amp;hy=SZ&amp;storeid=26826d4cc000295c38df7c9dd0000006e02004fb2535a138f101157f23f714&amp;ef=2&amp;bizid=1022" aeskey="1b40d97d58cd43bea2309b7b815674cb" externurl="http://vweixinf.tc.qq.com/110/20403/stodownload?m=29edd20435bc8e1eb6e8e9764a2ce68b&amp;filekey=30440201010430302e02016e0402535a04203239656464323034333562633865316562366538653937363461326365363862020302b980040d00000004627466730000000132&amp;hy=SZ&amp;storeid=26826d4cc0004a3e58df7c9dd0000006e03004fb3535a138f101157f23f739&amp;ef=3&amp;bizid=1022" externmd5="7535fb135c9c83cc15722363b629f712" width="640" height="640" tpurl="" tpauthkey="" attachedtext="" attachedtextcolor="" lensid="" emojiattr="" linkid="" desc=""></emoji><gameext type="0" content="0"></gameext><extcommoninfo></extcommoninfo></msg>'}, 'Status': 3, 'ImgStatus': 2, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1753848714, 'MsgSource': '<msgsource>\n\t<silence>0</silence>\n\t<membercount>73</membercount>\n\t<signature>N0_V1_Iw/hJZtp|v1_GuTaBpmK</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'PushContent': '阿尼亚与她在群聊中发了一个表情', 'NewMsgId': 5542306970041868465, 'MsgSeq': 871411557}
2025-07-30 12:11:41 | INFO | 收到表情消息: 消息ID:614253662 来自:***********@chatroom 发送人:wxid_jegyk4i3v7zg22 MD5:6bf139b37c4fc9a1ae052bedf4bdc8db 大小:1496415
2025-07-30 12:11:42 | DEBUG | [DoubaoImageRecognition] 缓存表情消息: 5542306970041868465
2025-07-30 12:11:43 | DEBUG | 收到消息: {'MsgId': 641514126, 'FromUserName': {'string': '***********@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 47, 'Content': {'string': 'wxid_jegyk4i3v7zg22:\n<msg><emoji fromusername="wxid_jegyk4i3v7zg22" tousername="***********@chatroom" type="2" idbuffer="media:0_0" md5="d59b91247a40ccf864db95dafb17ce14" len="3563447" productid="" androidmd5="d59b91247a40ccf864db95dafb17ce14" androidlen="3563447" s60v3md5="d59b91247a40ccf864db95dafb17ce14" s60v3len="3563447" s60v5md5="d59b91247a40ccf864db95dafb17ce14" s60v5len="3563447" cdnurl="http://vweixinf.tc.qq.com/110/20402/stodownload?m=d59b91247a40ccf864db95dafb17ce14&amp;filekey=30440201010430302e02016e0402535a042064353962393132343761343063636638363464623935646166623137636531340203365fb7040d00000004627466730000000132&amp;hy=SZ&amp;storeid=2680294de000a132bcdcbadb00000006e01004fb2535a24469bc1e763314c6&amp;ef=1&amp;bizid=1022" designerid="" thumburl="" encrypturl="http://vweixinf.tc.qq.com/110/20402/stodownload?m=4420e6a7ee3c80b23f76a37fd9ee7182&amp;filekey=30440201010430302e02016e0402535a042034343230653661376565336338306232336637366133376664396565373138320203365fc0040d00000004627466730000000132&amp;hy=SZ&amp;storeid=2680294de000dcfe4cdcbadb00000006e02004fb2535a24469bc1e763314e1&amp;ef=2&amp;bizid=1022" aeskey="77fca316fc9647ceb56c2591ef7989c5" externurl="http://vweixinf.tc.qq.com/110/20403/stodownload?m=a97d5283b88cd0380387252d3b26ae75&amp;filekey=30440201010430302e02016e0402535a04206139376435323833623838636430333830333837323532643362323661653735020302ff70040d00000004627466730000000132&amp;hy=SZ&amp;storeid=2680294df000241cfcdcbadb00000006e03004fb3535a24469bc1e76331503&amp;ef=3&amp;bizid=1022" externmd5="6b601c880138b6501626e3323ee1a3b3" width="299" height="300" tpurl="" tpauthkey="" attachedtext="" attachedtextcolor="" lensid="" emojiattr="" linkid="" desc=""></emoji><gameext type="0" content="0"></gameext><extcommoninfo></extcommoninfo></msg>'}, 'Status': 3, 'ImgStatus': 2, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1753848715, 'MsgSource': '<msgsource>\n\t<silence>0</silence>\n\t<membercount>73</membercount>\n\t<signature>N0_V1_SyKOyBRw|v1_xmQvFj9D</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'PushContent': '阿尼亚与她在群聊中发了一个表情', 'NewMsgId': 3035749297793599667, 'MsgSeq': 871411558}
2025-07-30 12:11:43 | INFO | 收到表情消息: 消息ID:641514126 来自:***********@chatroom 发送人:wxid_jegyk4i3v7zg22 MD5:d59b91247a40ccf864db95dafb17ce14 大小:3563447
2025-07-30 12:11:44 | DEBUG | [DoubaoImageRecognition] 缓存表情消息: 3035749297793599667
2025-07-30 12:12:39 | DEBUG | 收到消息: {'MsgId': 363195345, 'FromUserName': {'string': '***********@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 49, 'Content': {'string': 'wxid_7o4g44suf20t22:\n<?xml version="1.0"?>\n<msg>\n\t<appmsg appid="" sdkver="0">\n\t\t<title>@阿尼亚与她\u2005这是嘛玩意儿</title>\n\t\t<des />\n\t\t<username />\n\t\t<action>view</action>\n\t\t<type>57</type>\n\t\t<showtype>0</showtype>\n\t\t<content />\n\t\t<url />\n\t\t<lowurl />\n\t\t<forwardflag>0</forwardflag>\n\t\t<dataurl />\n\t\t<lowdataurl />\n\t\t<contentattr>0</contentattr>\n\t\t<streamvideo>\n\t\t\t<streamvideourl />\n\t\t\t<streamvideototaltime>0</streamvideototaltime>\n\t\t\t<streamvideotitle />\n\t\t\t<streamvideowording />\n\t\t\t<streamvideoweburl />\n\t\t\t<streamvideothumburl />\n\t\t\t<streamvideoaduxinfo />\n\t\t\t<streamvideopublishid />\n\t\t</streamvideo>\n\t\t<canvasPageItem>\n\t\t\t<canvasPageXml><![CDATA[]]></canvasPageXml>\n\t\t</canvasPageItem>\n\t\t<refermsg>\n\t\t\t<type>3</type>\n\t\t\t<svrid>7055321856790887672</svrid>\n\t\t\t<fromusr>***********@chatroom</fromusr>\n\t\t\t<chatusr>wxid_srknxij3jka022</chatusr>\n\t\t\t<displayname>她</displayname>\n\t\t\t<msgsource>&lt;msgsource&gt;\n\t&lt;alnode&gt;\n\t\t&lt;fr&gt;2&lt;/fr&gt;\n\t&lt;/alnode&gt;\n\t&lt;sec_msg_node&gt;\n\t\t&lt;uuid&gt;6f2d2ea1237337920557186ed808b50a_&lt;/uuid&gt;\n\t\t&lt;risk-file-flag /&gt;\n\t\t&lt;risk-file-md5-list /&gt;\n\t&lt;/sec_msg_node&gt;\n\t&lt;silence&gt;0&lt;/silence&gt;\n\t&lt;membercount&gt;73&lt;/membercount&gt;\n\t&lt;NotAutoDownloadRange&gt;20:00-22:00;00:00-01:00&lt;/NotAutoDownloadRange&gt;\n\t&lt;signature&gt;N0_V1_HnF9Bn5g|v1_SXMrpsLU&lt;/signature&gt;\n\t&lt;tmp_node&gt;\n\t\t&lt;publisher-id&gt;&lt;/publisher-id&gt;\n\t&lt;/tmp_node&gt;\n&lt;/msgsource&gt;\n</msgsource>\n\t\t\t<content>&lt;?xml version="1.0"?&gt;\n&lt;msg&gt;\n\t&lt;img aeskey="ad4ae23cf08d68a41277595d07bef5cf" encryver="1" cdnthumbaeskey="ad4ae23cf08d68a41277595d07bef5cf" cdnthumburl="3057020100044b304902010002049c5da34302032e6c63020455ab016f020468899b6e042464613932666164382d323561322d343364322d623161622d633763666330343433323964020405292a010201000405004c537700" cdnthumblength="5881" cdnthumbheight="120" cdnthumbwidth="90" cdnmidheight="0" cdnmidwidth="0" cdnhdheight="0" cdnhdwidth="0" cdnmidimgurl="3057020100044b304902010002049c5da34302032e6c63020455ab016f020468899b6e042464613932666164382d323561322d343364322d623161622d633763666330343433323964020405292a010201000405004c537700" length="15224" cdnbigimgurl="3057020100044b304902010002049c5da34302032e6c63020455ab016f020468899b6e042464613932666164382d323561322d343364322d623161622d633763666330343433323964020405292a010201000405004c537700" hdlength="58618" md5="5890e3ce46e9c70fa56a33fbe449e841" hevc_mid_size="15224" originsourcemd5="d5a44bad3c570ce9eff01df388efc69c"&gt;\n\t\t&lt;secHashInfoBase64 /&gt;\n\t\t&lt;live&gt;\n\t\t\t&lt;duration&gt;0&lt;/duration&gt;\n\t\t\t&lt;size&gt;0&lt;/size&gt;\n\t\t\t&lt;md5 /&gt;\n\t\t\t&lt;fileid /&gt;\n\t\t\t&lt;hdsize&gt;0&lt;/hdsize&gt;\n\t\t\t&lt;hdmd5 /&gt;\n\t\t\t&lt;hdfileid /&gt;\n\t\t\t&lt;stillimagetimems&gt;0&lt;/stillimagetimems&gt;\n\t\t&lt;/live&gt;\n\t&lt;/img&gt;\n\t&lt;appinfo&gt;\n\t\t&lt;appid&gt;wx0e054ce4ae213018&lt;/appid&gt;\n\t\t&lt;version&gt;20&lt;/version&gt;\n\t\t&lt;appname&gt;国务院客户端&lt;/appname&gt;\n\t\t&lt;isforceupdate&gt;0&lt;/isforceupdate&gt;\n\t\t&lt;messageaction /&gt;\n\t\t&lt;messageext /&gt;\n\t\t&lt;mediatagname /&gt;\n\t&lt;/appinfo&gt;\n\t&lt;platform_signature /&gt;\n\t&lt;imgdatahash /&gt;\n\t&lt;ImgSourceInfo&gt;\n\t\t&lt;ImgSourceUrl /&gt;\n\t\t&lt;BizType&gt;0&lt;/BizType&gt;\n\t&lt;/ImgSourceInfo&gt;\n&lt;/msg&gt;\n</content>\n\t\t\t<strid />\n\t\t\t<createtime>1753848687</createtime>\n\t\t</refermsg>\n\t\t<appattach>\n\t\t\t<totallen>0</totallen>\n\t\t\t<attachid />\n\t\t\t<cdnattachurl />\n\t\t\t<emoticonmd5></emoticonmd5>\n\t\t\t<aeskey></aeskey>\n\t\t\t<fileext />\n\t\t\t<islargefilemsg>0</islargefilemsg>\n\t\t</appattach>\n\t\t<extinfo />\n\t\t<androidsource>0</androidsource>\n\t\t<thumburl />\n\t\t<mediatagname />\n\t\t<messageaction><![CDATA[]]></messageaction>\n\t\t<messageext><![CDATA[]]></messageext>\n\t\t<emoticongift>\n\t\t\t<packageflag>0</packageflag>\n\t\t\t<packageid />\n\t\t</emoticongift>\n\t\t<emoticonshared>\n\t\t\t<packageflag>0</packageflag>\n\t\t\t<packageid />\n\t\t</emoticonshared>\n\t\t<designershared>\n\t\t\t<designeruin>0</designeruin>\n\t\t\t<designername>null</designername>\n\t\t\t<designerrediretcturl><![CDATA[null]]></designerrediretcturl>\n\t\t</designershared>\n\t\t<emotionpageshared>\n\t\t\t<tid>0</tid>\n\t\t\t<title>null</title>\n\t\t\t<desc>null</desc>\n\t\t\t<iconUrl><![CDATA[null]]></iconUrl>\n\t\t\t<secondUrl>null</secondUrl>\n\t\t\t<pageType>0</pageType>\n\t\t\t<setKey>null</setKey>\n\t\t</emotionpageshared>\n\t\t<webviewshared>\n\t\t\t<shareUrlOriginal />\n\t\t\t<shareUrlOpen />\n\t\t\t<jsAppId />\n\t\t\t<publisherId />\n\t\t\t<publisherReqId />\n\t\t</webviewshared>\n\t\t<template_id />\n\t\t<md5 />\n\t\t<websearch>\n\t\t\t<rec_category>0</rec_category>\n\t\t\t<channelId>0</channelId>\n\t\t</websearch>\n\t\t<weappinfo>\n\t\t\t<username />\n\t\t\t<appid />\n\t\t\t<appservicetype>0</appservicetype>\n\t\t\t<secflagforsinglepagemode>0</secflagforsinglepagemode>\n\t\t\t<videopageinfo>\n\t\t\t\t<thumbwidth>0</thumbwidth>\n\t\t\t\t<thumbheight>0</thumbheight>\n\t\t\t\t<fromopensdk>0</fromopensdk>\n\t\t\t</videopageinfo>\n\t\t</weappinfo>\n\t\t<statextstr />\n\t\t<musicShareItem>\n\t\t\t<musicDuration>0</musicDuration>\n\t\t</musicShareItem>\n\t\t<finderLiveProductShare>\n\t\t\t<finderLiveID><![CDATA[]]></finderLiveID>\n\t\t\t<finderUsername><![CDATA[]]></finderUsername>\n\t\t\t<finderObjectID><![CDATA[]]></finderObjectID>\n\t\t\t<finderNonceID><![CDATA[]]></finderNonceID>\n\t\t\t<liveStatus><![CDATA[]]></liveStatus>\n\t\t\t<appId><![CDATA[]]></appId>\n\t\t\t<pagePath><![CDATA[]]></pagePath>\n\t\t\t<productId><![CDATA[]]></productId>\n\t\t\t<coverUrl><![CDATA[]]></coverUrl>\n\t\t\t<productTitle><![CDATA[]]></productTitle>\n\t\t\t<marketPrice><![CDATA[0]]></marketPrice>\n\t\t\t<sellingPrice><![CDATA[0]]></sellingPrice>\n\t\t\t<platformHeadImg><![CDATA[]]></platformHeadImg>\n\t\t\t<platformName><![CDATA[]]></platformName>\n\t\t\t<shopWindowId><![CDATA[]]></shopWindowId>\n\t\t\t<flashSalePrice><![CDATA[0]]></flashSalePrice>\n\t\t\t<flashSaleEndTime><![CDATA[0]]></flashSaleEndTime>\n\t\t\t<ecSource><![CDATA[]]></ecSource>\n\t\t\t<sellingPriceWording><![CDATA[]]></sellingPriceWording>\n\t\t\t<platformIconURL><![CDATA[]]></platformIconURL>\n\t\t\t<firstProductTagURL><![CDATA[]]></firstProductTagURL>\n\t\t\t<firstProductTagAspectRatioString><![CDATA[0.0]]></firstProductTagAspectRatioString>\n\t\t\t<secondProductTagURL><![CDATA[]]></secondProductTagURL>\n\t\t\t<secondProductTagAspectRatioString><![CDATA[0.0]]></secondProductTagAspectRatioString>\n\t\t\t<firstGuaranteeWording><![CDATA[]]></firstGuaranteeWording>\n\t\t\t<secondGuaranteeWording><![CDATA[]]></secondGuaranteeWording>\n\t\t\t<thirdGuaranteeWording><![CDATA[]]></thirdGuaranteeWording>\n\t\t\t<isPriceBeginShow>false</isPriceBeginShow>\n\t\t\t<lastGMsgID><![CDATA[]]></lastGMsgID>\n\t\t\t<promoterKey><![CDATA[]]></promoterKey>\n\t\t\t<discountWording><![CDATA[]]></discountWording>\n\t\t\t<priceSuffixDescription><![CDATA[]]></priceSuffixDescription>\n\t\t\t<productCardKey><![CDATA[]]></productCardKey>\n\t\t\t<isWxShop><![CDATA[]]></isWxShop>\n\t\t\t<brandIconUrl><![CDATA[]]></brandIconUrl>\n\t\t\t<rIconUrl><![CDATA[]]></rIconUrl>\n\t\t\t<rIconUrlDarkMode><![CDATA[]]></rIconUrlDarkMode>\n\t\t\t<topShopIconUrl><![CDATA[]]></topShopIconUrl>\n\t\t\t<topShopIconUrlDarkMode><![CDATA[]]></topShopIconUrlDarkMode>\n\t\t\t<simplifyTopShopIconUrl><![CDATA[]]></simplifyTopShopIconUrl>\n\t\t\t<simplifyTopShopIconUrlDarkmode><![CDATA[]]></simplifyTopShopIconUrlDarkmode>\n\t\t\t<topShopIconWidth><![CDATA[0]]></topShopIconWidth>\n\t\t\t<topShopIconHeight><![CDATA[0]]></topShopIconHeight>\n\t\t\t<simplifyTopShopIconWidth><![CDATA[0]]></simplifyTopShopIconWidth>\n\t\t\t<simplifyTopShopIconHeight><![CDATA[0]]></simplifyTopShopIconHeight>\n\t\t\t<showBoxItemStringList />\n\t\t</finderLiveProductShare>\n\t\t<finderOrder>\n\t\t\t<appID><![CDATA[]]></appID>\n\t\t\t<orderID><![CDATA[]]></orderID>\n\t\t\t<path><![CDATA[]]></path>\n\t\t\t<priceWording><![CDATA[]]></priceWording>\n\t\t\t<stateWording><![CDATA[]]></stateWording>\n\t\t\t<productImageURL><![CDATA[]]></productImageURL>\n\t\t\t<products><![CDATA[]]></products>\n\t\t\t<productsCount><![CDATA[0]]></productsCount>\n\t\t\t<orderType><![CDATA[0]]></orderType>\n\t\t\t<newPriceWording><![CDATA[]]></newPriceWording>\n\t\t\t<newStateWording><![CDATA[]]></newStateWording>\n\t\t\t<useNewWording><![CDATA[0]]></useNewWording>\n\t\t</finderOrder>\n\t\t<finderShopWindowShare>\n\t\t\t<finderUsername><![CDATA[]]></finderUsername>\n\t\t\t<avatar><![CDATA[]]></avatar>\n\t\t\t<nickname><![CDATA[]]></nickname>\n\t\t\t<commodityInStockCount><![CDATA[]]></commodityInStockCount>\n\t\t\t<appId><![CDATA[]]></appId>\n\t\t\t<path><![CDATA[]]></path>\n\t\t\t<appUsername><![CDATA[]]></appUsername>\n\t\t\t<query><![CDATA[]]></query>\n\t\t\t<liteAppId><![CDATA[]]></liteAppId>\n\t\t\t<liteAppPath><![CDATA[]]></liteAppPath>\n\t\t\t<liteAppQuery><![CDATA[]]></liteAppQuery>\n\t\t\t<platformTagURL><![CDATA[]]></platformTagURL>\n\t\t\t<saleWording><![CDATA[]]></saleWording>\n\t\t\t<lastGMsgID><![CDATA[]]></lastGMsgID>\n\t\t\t<profileTypeWording><![CDATA[]]></profileTypeWording>\n\t\t\t<saleWordingExtra><![CDATA[]]></saleWordingExtra>\n\t\t\t<isWxShop><![CDATA[]]></isWxShop>\n\t\t\t<platformIconUrl><![CDATA[]]></platformIconUrl>\n\t\t\t<brandIconUrl><![CDATA[]]></brandIconUrl>\n\t\t\t<description><![CDATA[]]></description>\n\t\t\t<backgroundUrl><![CDATA[]]></backgroundUrl>\n\t\t\t<darkModePlatformIconUrl><![CDATA[]]></darkModePlatformIconUrl>\n\t\t\t<rIconUrl><![CDATA[]]></rIconUrl>\n\t\t\t<rIconUrlDarkMode><![CDATA[]]></rIconUrlDarkMode>\n\t\t\t<rWords><![CDATA[]]></rWords>\n\t\t\t<topShopIconUrl><![CDATA[]]></topShopIconUrl>\n\t\t\t<topShopIconUrlDarkMode><![CDATA[]]></topShopIconUrlDarkMode>\n\t\t\t<simplifyTopShopIconUrl><![CDATA[]]></simplifyTopShopIconUrl>\n\t\t\t<simplifyTopShopIconUrlDarkmode><![CDATA[]]></simplifyTopShopIconUrlDarkmode>\n\t\t\t<topShopIconWidth><![CDATA[0]]></topShopIconWidth>\n\t\t\t<topShopIconHeight><![CDATA[0]]></topShopIconHeight>\n\t\t\t<simplifyTopShopIconWidth><![CDATA[0]]></simplifyTopShopIconWidth>\n\t\t\t<simplifyTopShopIconHeight><![CDATA[0]]></simplifyTopShopIconHeight>\n\t\t\t<reputationInfo>\n\t\t\t\t<hasReputationInfo>0</hasReputationInfo>\n\t\t\t\t<reputationScore>0</reputationScore>\n\t\t\t\t<reputationWording />\n\t\t\t\t<reputationTextColor />\n\t\t\t\t<reputationLevelWording />\n\t\t\t\t<reputationBackgroundColor />\n\t\t\t</reputationInfo>\n\t\t\t<productImageURLList />\n\t\t</finderShopWindowShare>\n\t\t<findernamecard>\n\t\t\t<username />\n\t\t\t<avatar><![CDATA[]]></avatar>\n\t\t\t<nickname />\n\t\t\t<auth_job />\n\t\t\t<auth_icon>0</auth_icon>\n\t\t\t<auth_icon_url />\n\t\t\t<ecSource><![CDATA[]]></ecSource>\n\t\t\t<lastGMsgID><![CDATA[]]></lastGMsgID>\n\t\t</findernamecard>\n\t\t<finderGuarantee>\n\t\t\t<scene><![CDATA[0]]></scene>\n\t\t</finderGuarantee>\n\t\t<directshare>0</directshare>\n\t\t<gamecenter>\n\t\t\t<namecard>\n\t\t\t\t<iconUrl />\n\t\t\t\t<name />\n\t\t\t\t<desc />\n\t\t\t\t<tail />\n\t\t\t\t<jumpUrl />\n\t\t\t\t<liteappId />\n\t\t\t\t<liteappPath />\n\t\t\t\t<liteappQuery />\n\t\t\t\t<liteappMinVersion />\n\t\t\t</namecard>\n\t\t</gamecenter>\n\t\t<patMsg>\n\t\t\t<chatUser />\n\t\t\t<records>\n\t\t\t\t<recordNum>0</recordNum>\n\t\t\t</records>\n\t\t</patMsg>\n\t\t<secretmsg>\n\t\t\t<issecretmsg>0</issecretmsg>\n\t\t</secretmsg>\n\t\t<referfromscene>0</referfromscene>\n\t\t<gameshare>\n\t\t\t<liteappext>\n\t\t\t\t<liteappbizdata />\n\t\t\t\t<priority>0</priority>\n\t\t\t</liteappext>\n\t\t\t<appbrandext>\n\t\t\t\t<litegameinfo />\n\t\t\t\t<priority>-1</priority>\n\t\t\t</appbrandext>\n\t\t\t<gameshareid />\n\t\t\t<sharedata />\n\t\t\t<isvideo>0</isvideo>\n\t\t\t<duration>-1</duration>\n\t\t\t<isexposed>0</isexposed>\n\t\t\t<readtext />\n\t\t</gameshare>\n\t\t<tingChatRoomItem>\n\t\t\t<type>0</type>\n\t\t\t<categoryItem>null</categoryItem>\n\t\t\t<categoryId />\n\t\t</tingChatRoomItem>\n\t\t<mpsharetrace>\n\t\t\t<hasfinderelement>0</hasfinderelement>\n\t\t\t<lastgmsgid />\n\t\t</mpsharetrace>\n\t\t<wxgamecard>\n\t\t\t<framesetname />\n\t\t\t<mbcarddata />\n\t\t\t<minpkgversion />\n\t\t\t<clientextinfo />\n\t\t\t<mbcardheight>0</mbcardheight>\n\t\t\t<isoldversion>0</isoldversion>\n\t\t</wxgamecard>\n\t\t<ecskfcard>\n\t\t\t<framesetname />\n\t\t\t<mbcarddata />\n\t\t\t<minupdateunixtimestamp>0</minupdateunixtimestamp>\n\t\t\t<needheader>false</needheader>\n\t\t\t<summary />\n\t\t</ecskfcard>\n\t\t<liteapp>\n\t\t\t<id>null</id>\n\t\t\t<path />\n\t\t\t<query />\n\t\t\t<istransparent>0</istransparent>\n\t\t\t<hideicon>0</hideicon>\n\t\t\t<forbidforward>0</forbidforward>\n\t\t</liteapp>\n\t\t<opensdk_share_is_modified>0</opensdk_share_is_modified>\n\t</appmsg>\n\t<fromusername>wxid_7o4g44suf20t22</fromusername>\n\t<scene>0</scene>\n\t<appinfo>\n\t\t<version>1</version>\n\t\t<appname />\n\t</appinfo>\n\t<commenturl />\n</msg>\n'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1753848771, 'MsgSource': '<msgsource>\n\t<alnode>\n\t\t<fr>4</fr>\n\t</alnode>\n\t<atuserlist>wxid_jegyk4i3v7zg22</atuserlist>\n\t<eggIncluded>1</eggIncluded>\n\t<sec_msg_node>\n\t\t<uuid>6f2d2ea1237337920557186ed808b50a_</uuid>\n\t\t<risk-file-flag />\n\t\t<risk-file-md5-list />\n\t</sec_msg_node>\n\t<silence>0</silence>\n\t<membercount>73</membercount>\n\t<signature>N0_V1_5vi4Xp7l|v1_oN0A7+xy</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'PushContent': '与其，不惹 : @阿尼亚与她\u2005这是嘛玩意儿', 'NewMsgId': 6419040516485543401, 'MsgSeq': 871411559}
2025-07-30 12:12:39 | DEBUG | 从群聊消息中提取发送者: wxid_7o4g44suf20t22
2025-07-30 12:12:39 | DEBUG | 使用已解析的XML处理引用消息
2025-07-30 12:12:39 | INFO | 收到引用消息: 消息ID:363195345 来自:***********@chatroom 发送人:wxid_7o4g44suf20t22 内容:@阿尼亚与她 这是嘛玩意儿 引用类型:3
2025-07-30 12:12:39 | INFO | [DouBaoImageToImage] 收到引用消息: @阿尼亚与她 这是嘛玩意儿
2025-07-30 12:12:39 | INFO | [TimerTask] 收到引用消息调试信息:
2025-07-30 12:12:39 | INFO |   - 消息内容: @阿尼亚与她 这是嘛玩意儿
2025-07-30 12:12:39 | INFO |   - 群组ID: ***********@chatroom
2025-07-30 12:12:39 | INFO |   - 发送人: wxid_7o4g44suf20t22
2025-07-30 12:12:39 | INFO |   - 引用信息: {'MsgType': 3, 'Content': '<?xml version="1.0"?>\n<msg>\n\t<appmsg appid="" sdkver="0">\n\t\t<title>@阿尼亚与她\u2005这是嘛玩意儿</title>\n\t\t<des />\n\t\t<username />\n\t\t<action>view</action>\n\t\t<type>57</type>\n\t\t<showtype>0</showtype>\n\t\t<content />\n\t\t<url />\n\t\t<lowurl />\n\t\t<forwardflag>0</forwardflag>\n\t\t<dataurl />\n\t\t<lowdataurl />\n\t\t<contentattr>0</contentattr>\n\t\t<streamvideo>\n\t\t\t<streamvideourl />\n\t\t\t<streamvideototaltime>0</streamvideototaltime>\n\t\t\t<streamvideotitle />\n\t\t\t<streamvideowording />\n\t\t\t<streamvideoweburl />\n\t\t\t<streamvideothumburl />\n\t\t\t<streamvideoaduxinfo />\n\t\t\t<streamvideopublishid />\n\t\t</streamvideo>\n\t\t<canvasPageItem>\n\t\t\t<canvasPageXml><![CDATA[]]></canvasPageXml>\n\t\t</canvasPageItem>\n\t\t<refermsg>\n\t\t\t<type>3</type>\n\t\t\t<svrid>7055321856790887672</svrid>\n\t\t\t<fromusr>***********@chatroom</fromusr>\n\t\t\t<chatusr>wxid_srknxij3jka022</chatusr>\n\t\t\t<displayname>她</displayname>\n\t\t\t<msgsource>&lt;msgsource&gt;\n\t&lt;alnode&gt;\n\t\t&lt;fr&gt;2&lt;/fr&gt;\n\t&lt;/alnode&gt;\n\t&lt;sec_msg_node&gt;\n\t\t&lt;uuid&gt;6f2d2ea1237337920557186ed808b50a_&lt;/uuid&gt;\n\t\t&lt;risk-file-flag /&gt;\n\t\t&lt;risk-file-md5-list /&gt;\n\t&lt;/sec_msg_node&gt;\n\t&lt;silence&gt;0&lt;/silence&gt;\n\t&lt;membercount&gt;73&lt;/membercount&gt;\n\t&lt;NotAutoDownloadRange&gt;20:00-22:00;00:00-01:00&lt;/NotAutoDownloadRange&gt;\n\t&lt;signature&gt;N0_V1_HnF9Bn5g|v1_SXMrpsLU&lt;/signature&gt;\n\t&lt;tmp_node&gt;\n\t\t&lt;publisher-id&gt;&lt;/publisher-id&gt;\n\t&lt;/tmp_node&gt;\n&lt;/msgsource&gt;\n</msgsource>\n\t\t\t<content>&lt;?xml version="1.0"?&gt;\n&lt;msg&gt;\n\t&lt;img aeskey="ad4ae23cf08d68a41277595d07bef5cf" encryver="1" cdnthumbaeskey="ad4ae23cf08d68a41277595d07bef5cf" cdnthumburl="3057020100044b304902010002049c5da34302032e6c63020455ab016f020468899b6e042464613932666164382d323561322d343364322d623161622d633763666330343433323964020405292a010201000405004c537700" cdnthumblength="5881" cdnthumbheight="120" cdnthumbwidth="90" cdnmidheight="0" cdnmidwidth="0" cdnhdheight="0" cdnhdwidth="0" cdnmidimgurl="3057020100044b304902010002049c5da34302032e6c63020455ab016f020468899b6e042464613932666164382d323561322d343364322d623161622d633763666330343433323964020405292a010201000405004c537700" length="15224" cdnbigimgurl="3057020100044b304902010002049c5da34302032e6c63020455ab016f020468899b6e042464613932666164382d323561322d343364322d623161622d633763666330343433323964020405292a010201000405004c537700" hdlength="58618" md5="5890e3ce46e9c70fa56a33fbe449e841" hevc_mid_size="15224" originsourcemd5="d5a44bad3c570ce9eff01df388efc69c"&gt;\n\t\t&lt;secHashInfoBase64 /&gt;\n\t\t&lt;live&gt;\n\t\t\t&lt;duration&gt;0&lt;/duration&gt;\n\t\t\t&lt;size&gt;0&lt;/size&gt;\n\t\t\t&lt;md5 /&gt;\n\t\t\t&lt;fileid /&gt;\n\t\t\t&lt;hdsize&gt;0&lt;/hdsize&gt;\n\t\t\t&lt;hdmd5 /&gt;\n\t\t\t&lt;hdfileid /&gt;\n\t\t\t&lt;stillimagetimems&gt;0&lt;/stillimagetimems&gt;\n\t\t&lt;/live&gt;\n\t&lt;/img&gt;\n\t&lt;appinfo&gt;\n\t\t&lt;appid&gt;wx0e054ce4ae213018&lt;/appid&gt;\n\t\t&lt;version&gt;20&lt;/version&gt;\n\t\t&lt;appname&gt;国务院客户端&lt;/appname&gt;\n\t\t&lt;isforceupdate&gt;0&lt;/isforceupdate&gt;\n\t\t&lt;messageaction /&gt;\n\t\t&lt;messageext /&gt;\n\t\t&lt;mediatagname /&gt;\n\t&lt;/appinfo&gt;\n\t&lt;platform_signature /&gt;\n\t&lt;imgdatahash /&gt;\n\t&lt;ImgSourceInfo&gt;\n\t\t&lt;ImgSourceUrl /&gt;\n\t\t&lt;BizType&gt;0&lt;/BizType&gt;\n\t&lt;/ImgSourceInfo&gt;\n&lt;/msg&gt;\n</content>\n\t\t\t<strid />\n\t\t\t<createtime>1753848687</createtime>\n\t\t</refermsg>\n\t\t<appattach>\n\t\t\t<totallen>0</totallen>\n\t\t\t<attachid />\n\t\t\t<cdnattachurl />\n\t\t\t<emoticonmd5></emoticonmd5>\n\t\t\t<aeskey></aeskey>\n\t\t\t<fileext />\n\t\t\t<islargefilemsg>0</islargefilemsg>\n\t\t</appattach>\n\t\t<extinfo />\n\t\t<androidsource>0</androidsource>\n\t\t<thumburl />\n\t\t<mediatagname />\n\t\t<messageaction><![CDATA[]]></messageaction>\n\t\t<messageext><![CDATA[]]></messageext>\n\t\t<emoticongift>\n\t\t\t<packageflag>0</packageflag>\n\t\t\t<packageid />\n\t\t</emoticongift>\n\t\t<emoticonshared>\n\t\t\t<packageflag>0</packageflag>\n\t\t\t<packageid />\n\t\t</emoticonshared>\n\t\t<designershared>\n\t\t\t<designeruin>0</designeruin>\n\t\t\t<designername>null</designername>\n\t\t\t<designerrediretcturl><![CDATA[null]]></designerrediretcturl>\n\t\t</designershared>\n\t\t<emotionpageshared>\n\t\t\t<tid>0</tid>\n\t\t\t<title>null</title>\n\t\t\t<desc>null</desc>\n\t\t\t<iconUrl><![CDATA[null]]></iconUrl>\n\t\t\t<secondUrl>null</secondUrl>\n\t\t\t<pageType>0</pageType>\n\t\t\t<setKey>null</setKey>\n\t\t</emotionpageshared>\n\t\t<webviewshared>\n\t\t\t<shareUrlOriginal />\n\t\t\t<shareUrlOpen />\n\t\t\t<jsAppId />\n\t\t\t<publisherId />\n\t\t\t<publisherReqId />\n\t\t</webviewshared>\n\t\t<template_id />\n\t\t<md5 />\n\t\t<websearch>\n\t\t\t<rec_category>0</rec_category>\n\t\t\t<channelId>0</channelId>\n\t\t</websearch>\n\t\t<weappinfo>\n\t\t\t<username />\n\t\t\t<appid />\n\t\t\t<appservicetype>0</appservicetype>\n\t\t\t<secflagforsinglepagemode>0</secflagforsinglepagemode>\n\t\t\t<videopageinfo>\n\t\t\t\t<thumbwidth>0</thumbwidth>\n\t\t\t\t<thumbheight>0</thumbheight>\n\t\t\t\t<fromopensdk>0</fromopensdk>\n\t\t\t</videopageinfo>\n\t\t</weappinfo>\n\t\t<statextstr />\n\t\t<musicShareItem>\n\t\t\t<musicDuration>0</musicDuration>\n\t\t</musicShareItem>\n\t\t<finderLiveProductShare>\n\t\t\t<finderLiveID><![CDATA[]]></finderLiveID>\n\t\t\t<finderUsername><![CDATA[]]></finderUsername>\n\t\t\t<finderObjectID><![CDATA[]]></finderObjectID>\n\t\t\t<finderNonceID><![CDATA[]]></finderNonceID>\n\t\t\t<liveStatus><![CDATA[]]></liveStatus>\n\t\t\t<appId><![CDATA[]]></appId>\n\t\t\t<pagePath><![CDATA[]]></pagePath>\n\t\t\t<productId><![CDATA[]]></productId>\n\t\t\t<coverUrl><![CDATA[]]></coverUrl>\n\t\t\t<productTitle><![CDATA[]]></productTitle>\n\t\t\t<marketPrice><![CDATA[0]]></marketPrice>\n\t\t\t<sellingPrice><![CDATA[0]]></sellingPrice>\n\t\t\t<platformHeadImg><![CDATA[]]></platformHeadImg>\n\t\t\t<platformName><![CDATA[]]></platformName>\n\t\t\t<shopWindowId><![CDATA[]]></shopWindowId>\n\t\t\t<flashSalePrice><![CDATA[0]]></flashSalePrice>\n\t\t\t<flashSaleEndTime><![CDATA[0]]></flashSaleEndTime>\n\t\t\t<ecSource><![CDATA[]]></ecSource>\n\t\t\t<sellingPriceWording><![CDATA[]]></sellingPriceWording>\n\t\t\t<platformIconURL><![CDATA[]]></platformIconURL>\n\t\t\t<firstProductTagURL><![CDATA[]]></firstProductTagURL>\n\t\t\t<firstProductTagAspectRatioString><![CDATA[0.0]]></firstProductTagAspectRatioString>\n\t\t\t<secondProductTagURL><![CDATA[]]></secondProductTagURL>\n\t\t\t<secondProductTagAspectRatioString><![CDATA[0.0]]></secondProductTagAspectRatioString>\n\t\t\t<firstGuaranteeWording><![CDATA[]]></firstGuaranteeWording>\n\t\t\t<secondGuaranteeWording><![CDATA[]]></secondGuaranteeWording>\n\t\t\t<thirdGuaranteeWording><![CDATA[]]></thirdGuaranteeWording>\n\t\t\t<isPriceBeginShow>false</isPriceBeginShow>\n\t\t\t<lastGMsgID><![CDATA[]]></lastGMsgID>\n\t\t\t<promoterKey><![CDATA[]]></promoterKey>\n\t\t\t<discountWording><![CDATA[]]></discountWording>\n\t\t\t<priceSuffixDescription><![CDATA[]]></priceSuffixDescription>\n\t\t\t<productCardKey><![CDATA[]]></productCardKey>\n\t\t\t<isWxShop><![CDATA[]]></isWxShop>\n\t\t\t<brandIconUrl><![CDATA[]]></brandIconUrl>\n\t\t\t<rIconUrl><![CDATA[]]></rIconUrl>\n\t\t\t<rIconUrlDarkMode><![CDATA[]]></rIconUrlDarkMode>\n\t\t\t<topShopIconUrl><![CDATA[]]></topShopIconUrl>\n\t\t\t<topShopIconUrlDarkMode><![CDATA[]]></topShopIconUrlDarkMode>\n\t\t\t<simplifyTopShopIconUrl><![CDATA[]]></simplifyTopShopIconUrl>\n\t\t\t<simplifyTopShopIconUrlDarkmode><![CDATA[]]></simplifyTopShopIconUrlDarkmode>\n\t\t\t<topShopIconWidth><![CDATA[0]]></topShopIconWidth>\n\t\t\t<topShopIconHeight><![CDATA[0]]></topShopIconHeight>\n\t\t\t<simplifyTopShopIconWidth><![CDATA[0]]></simplifyTopShopIconWidth>\n\t\t\t<simplifyTopShopIconHeight><![CDATA[0]]></simplifyTopShopIconHeight>\n\t\t\t<showBoxItemStringList />\n\t\t</finderLiveProductShare>\n\t\t<finderOrder>\n\t\t\t<appID><![CDATA[]]></appID>\n\t\t\t<orderID><![CDATA[]]></orderID>\n\t\t\t<path><![CDATA[]]></path>\n\t\t\t<priceWording><![CDATA[]]></priceWording>\n\t\t\t<stateWording><![CDATA[]]></stateWording>\n\t\t\t<productImageURL><![CDATA[]]></productImageURL>\n\t\t\t<products><![CDATA[]]></products>\n\t\t\t<productsCount><![CDATA[0]]></productsCount>\n\t\t\t<orderType><![CDATA[0]]></orderType>\n\t\t\t<newPriceWording><![CDATA[]]></newPriceWording>\n\t\t\t<newStateWording><![CDATA[]]></newStateWording>\n\t\t\t<useNewWording><![CDATA[0]]></useNewWording>\n\t\t</finderOrder>\n\t\t<finderShopWindowShare>\n\t\t\t<finderUsername><![CDATA[]]></finderUsername>\n\t\t\t<avatar><![CDATA[]]></avatar>\n\t\t\t<nickname><![CDATA[]]></nickname>\n\t\t\t<commodityInStockCount><![CDATA[]]></commodityInStockCount>\n\t\t\t<appId><![CDATA[]]></appId>\n\t\t\t<path><![CDATA[]]></path>\n\t\t\t<appUsername><![CDATA[]]></appUsername>\n\t\t\t<query><![CDATA[]]></query>\n\t\t\t<liteAppId><![CDATA[]]></liteAppId>\n\t\t\t<liteAppPath><![CDATA[]]></liteAppPath>\n\t\t\t<liteAppQuery><![CDATA[]]></liteAppQuery>\n\t\t\t<platformTagURL><![CDATA[]]></platformTagURL>\n\t\t\t<saleWording><![CDATA[]]></saleWording>\n\t\t\t<lastGMsgID><![CDATA[]]></lastGMsgID>\n\t\t\t<profileTypeWording><![CDATA[]]></profileTypeWording>\n\t\t\t<saleWordingExtra><![CDATA[]]></saleWordingExtra>\n\t\t\t<isWxShop><![CDATA[]]></isWxShop>\n\t\t\t<platformIconUrl><![CDATA[]]></platformIconUrl>\n\t\t\t<brandIconUrl><![CDATA[]]></brandIconUrl>\n\t\t\t<description><![CDATA[]]></description>\n\t\t\t<backgroundUrl><![CDATA[]]></backgroundUrl>\n\t\t\t<darkModePlatformIconUrl><![CDATA[]]></darkModePlatformIconUrl>\n\t\t\t<rIconUrl><![CDATA[]]></rIconUrl>\n\t\t\t<rIconUrlDarkMode><![CDATA[]]></rIconUrlDarkMode>\n\t\t\t<rWords><![CDATA[]]></rWords>\n\t\t\t<topShopIconUrl><![CDATA[]]></topShopIconUrl>\n\t\t\t<topShopIconUrlDarkMode><![CDATA[]]></topShopIconUrlDarkMode>\n\t\t\t<simplifyTopShopIconUrl><![CDATA[]]></simplifyTopShopIconUrl>\n\t\t\t<simplifyTopShopIconUrlDarkmode><![CDATA[]]></simplifyTopShopIconUrlDarkmode>\n\t\t\t<topShopIconWidth><![CDATA[0]]></topShopIconWidth>\n\t\t\t<topShopIconHeight><![CDATA[0]]></topShopIconHeight>\n\t\t\t<simplifyTopShopIconWidth><![CDATA[0]]></simplifyTopShopIconWidth>\n\t\t\t<simplifyTopShopIconHeight><![CDATA[0]]></simplifyTopShopIconHeight>\n\t\t\t<reputationInfo>\n\t\t\t\t<hasReputationInfo>0</hasReputationInfo>\n\t\t\t\t<reputationScore>0</reputationScore>\n\t\t\t\t<reputationWording />\n\t\t\t\t<reputationTextColor />\n\t\t\t\t<reputationLevelWording />\n\t\t\t\t<reputationBackgroundColor />\n\t\t\t</reputationInfo>\n\t\t\t<productImageURLList />\n\t\t</finderShopWindowShare>\n\t\t<findernamecard>\n\t\t\t<username />\n\t\t\t<avatar><![CDATA[]]></avatar>\n\t\t\t<nickname />\n\t\t\t<auth_job />\n\t\t\t<auth_icon>0</auth_icon>\n\t\t\t<auth_icon_url />\n\t\t\t<ecSource><![CDATA[]]></ecSource>\n\t\t\t<lastGMsgID><![CDATA[]]></lastGMsgID>\n\t\t</findernamecard>\n\t\t<finderGuarantee>\n\t\t\t<scene><![CDATA[0]]></scene>\n\t\t</finderGuarantee>\n\t\t<directshare>0</directshare>\n\t\t<gamecenter>\n\t\t\t<namecard>\n\t\t\t\t<iconUrl />\n\t\t\t\t<name />\n\t\t\t\t<desc />\n\t\t\t\t<tail />\n\t\t\t\t<jumpUrl />\n\t\t\t\t<liteappId />\n\t\t\t\t<liteappPath />\n\t\t\t\t<liteappQuery />\n\t\t\t\t<liteappMinVersion />\n\t\t\t</namecard>\n\t\t</gamecenter>\n\t\t<patMsg>\n\t\t\t<chatUser />\n\t\t\t<records>\n\t\t\t\t<recordNum>0</recordNum>\n\t\t\t</records>\n\t\t</patMsg>\n\t\t<secretmsg>\n\t\t\t<issecretmsg>0</issecretmsg>\n\t\t</secretmsg>\n\t\t<referfromscene>0</referfromscene>\n\t\t<gameshare>\n\t\t\t<liteappext>\n\t\t\t\t<liteappbizdata />\n\t\t\t\t<priority>0</priority>\n\t\t\t</liteappext>\n\t\t\t<appbrandext>\n\t\t\t\t<litegameinfo />\n\t\t\t\t<priority>-1</priority>\n\t\t\t</appbrandext>\n\t\t\t<gameshareid />\n\t\t\t<sharedata />\n\t\t\t<isvideo>0</isvideo>\n\t\t\t<duration>-1</duration>\n\t\t\t<isexposed>0</isexposed>\n\t\t\t<readtext />\n\t\t</gameshare>\n\t\t<tingChatRoomItem>\n\t\t\t<type>0</type>\n\t\t\t<categoryItem>null</categoryItem>\n\t\t\t<categoryId />\n\t\t</tingChatRoomItem>\n\t\t<mpsharetrace>\n\t\t\t<hasfinderelement>0</hasfinderelement>\n\t\t\t<lastgmsgid />\n\t\t</mpsharetrace>\n\t\t<wxgamecard>\n\t\t\t<framesetname />\n\t\t\t<mbcarddata />\n\t\t\t<minpkgversion />\n\t\t\t<clientextinfo />\n\t\t\t<mbcardheight>0</mbcardheight>\n\t\t\t<isoldversion>0</isoldversion>\n\t\t</wxgamecard>\n\t\t<ecskfcard>\n\t\t\t<framesetname />\n\t\t\t<mbcarddata />\n\t\t\t<minupdateunixtimestamp>0</minupdateunixtimestamp>\n\t\t\t<needheader>false</needheader>\n\t\t\t<summary />\n\t\t</ecskfcard>\n\t\t<liteapp>\n\t\t\t<id>null</id>\n\t\t\t<path />\n\t\t\t<query />\n\t\t\t<istransparent>0</istransparent>\n\t\t\t<hideicon>0</hideicon>\n\t\t\t<forbidforward>0</forbidforward>\n\t\t</liteapp>\n\t\t<opensdk_share_is_modified>0</opensdk_share_is_modified>\n\t</appmsg>\n\t<fromusername>wxid_7o4g44suf20t22</fromusername>\n\t<scene>0</scene>\n\t<appinfo>\n\t\t<version>1</version>\n\t\t<appname />\n\t</appinfo>\n\t<commenturl />\n</msg>\n', 'Msgid': '7055321856790887672', 'NewMsgId': '7055321856790887672', 'ToWxid': 'wxid_4usgcju5ey9q29', 'FromWxid': '***********@chatroom', 'Nickname': '她', 'MsgSource': '<msgsource>\n\t<alnode>\n\t\t<fr>2</fr>\n\t</alnode>\n\t<sec_msg_node>\n\t\t<uuid>6f2d2ea1237337920557186ed808b50a_</uuid>\n\t\t<risk-file-flag />\n\t\t<risk-file-md5-list />\n\t</sec_msg_node>\n\t<silence>0</silence>\n\t<membercount>73</membercount>\n\t<NotAutoDownloadRange>20:00-22:00;00:00-01:00</NotAutoDownloadRange>\n\t<signature>N0_V1_HnF9Bn5g|v1_SXMrpsLU</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'Createtime': '1753848687', 'SenderWxid': 'wxid_7o4g44suf20t22'}
2025-07-30 12:12:39 | INFO |   - 引用消息ID: 
2025-07-30 12:12:39 | INFO |   - 引用消息类型: 
2025-07-30 12:12:39 | INFO |   - 引用消息内容: <?xml version="1.0"?>
<msg>
	<appmsg appid="" sdkver="0">
		<title>@阿尼亚与她 这是嘛玩意儿</title>
		<des />
		<username />
		<action>view</action>
		<type>57</type>
		<showtype>0</showtype>
		<content />
		<url />
		<lowurl />
		<forwardflag>0</forwardflag>
		<dataurl />
		<lowdataurl />
		<contentattr>0</contentattr>
		<streamvideo>
			<streamvideourl />
			<streamvideototaltime>0</streamvideototaltime>
			<streamvideotitle />
			<streamvideowording />
			<streamvideoweburl />
			<streamvideothumburl />
			<streamvideoaduxinfo />
			<streamvideopublishid />
		</streamvideo>
		<canvasPageItem>
			<canvasPageXml><![CDATA[]]></canvasPageXml>
		</canvasPageItem>
		<refermsg>
			<type>3</type>
			<svrid>7055321856790887672</svrid>
			<fromusr>***********@chatroom</fromusr>
			<chatusr>wxid_srknxij3jka022</chatusr>
			<displayname>她</displayname>
			<msgsource>&lt;msgsource&gt;
	&lt;alnode&gt;
		&lt;fr&gt;2&lt;/fr&gt;
	&lt;/alnode&gt;
	&lt;sec_msg_node&gt;
		&lt;uuid&gt;6f2d2ea1237337920557186ed808b50a_&lt;/uuid&gt;
		&lt;risk-file-flag /&gt;
		&lt;risk-file-md5-list /&gt;
	&lt;/sec_msg_node&gt;
	&lt;silence&gt;0&lt;/silence&gt;
	&lt;membercount&gt;73&lt;/membercount&gt;
	&lt;NotAutoDownloadRange&gt;20:00-22:00;00:00-01:00&lt;/NotAutoDownloadRange&gt;
	&lt;signature&gt;N0_V1_HnF9Bn5g|v1_SXMrpsLU&lt;/signature&gt;
	&lt;tmp_node&gt;
		&lt;publisher-id&gt;&lt;/publisher-id&gt;
	&lt;/tmp_node&gt;
&lt;/msgsource&gt;
</msgsource>
			<content>&lt;?xml version="1.0"?&gt;
&lt;msg&gt;
	&lt;img aeskey="ad4ae23cf08d68a41277595d07bef5cf" encryver="1" cdnthumbaeskey="ad4ae23cf08d68a41277595d07bef5cf" cdnthumburl="3057020100044b304902010002049c5da34302032e6c63020455ab016f020468899b6e042464613932666164382d323561322d343364322d623161622d633763666330343433323964020405292a010201000405004c537700" cdnthumblength="5881" cdnthumbheight="120" cdnthumbwidth="90" cdnmidheight="0" cdnmidwidth="0" cdnhdheight="0" cdnhdwidth="0" cdnmidimgurl="3057020100044b304902010002049c5da34302032e6c63020455ab016f020468899b6e042464613932666164382d323561322d343364322d623161622d633763666330343433323964020405292a010201000405004c537700" length="15224" cdnbigimgurl="3057020100044b304902010002049c5da34302032e6c63020455ab016f020468899b6e042464613932666164382d323561322d343364322d623161622d633763666330343433323964020405292a010201000405004c537700" hdlength="58618" md5="5890e3ce46e9c70fa56a33fbe449e841" hevc_mid_size="15224" originsourcemd5="d5a44bad3c570ce9eff01df388efc69c"&gt;
		&lt;secHashInfoBase64 /&gt;
		&lt;live&gt;
			&lt;duration&gt;0&lt;/duration&gt;
			&lt;size&gt;0&lt;/size&gt;
			&lt;md5 /&gt;
			&lt;fileid /&gt;
			&lt;hdsize&gt;0&lt;/hdsize&gt;
			&lt;hdmd5 /&gt;
			&lt;hdfileid /&gt;
			&lt;stillimagetimems&gt;0&lt;/stillimagetimems&gt;
		&lt;/live&gt;
	&lt;/img&gt;
	&lt;appinfo&gt;
		&lt;appid&gt;wx0e054ce4ae213018&lt;/appid&gt;
		&lt;version&gt;20&lt;/version&gt;
		&lt;appname&gt;国务院客户端&lt;/appname&gt;
		&lt;isforceupdate&gt;0&lt;/isforceupdate&gt;
		&lt;messageaction /&gt;
		&lt;messageext /&gt;
		&lt;mediatagname /&gt;
	&lt;/appinfo&gt;
	&lt;platform_signature /&gt;
	&lt;imgdatahash /&gt;
	&lt;ImgSourceInfo&gt;
		&lt;ImgSourceUrl /&gt;
		&lt;BizType&gt;0&lt;/BizType&gt;
	&lt;/ImgSourceInfo&gt;
&lt;/msg&gt;
</content>
			<strid />
			<createtime>1753848687</createtime>
		</refermsg>
		<appattach>
			<totallen>0</totallen>
			<attachid />
			<cdnattachurl />
			<emoticonmd5></emoticonmd5>
			<aeskey></aeskey>
			<fileext />
			<islargefilemsg>0</islargefilemsg>
		</appattach>
		<extinfo />
		<androidsource>0</androidsource>
		<thumburl />
		<mediatagname />
		<messageaction><![CDATA[]]></messageaction>
		<messageext><![CDATA[]]></messageext>
		<emoticongift>
			<packageflag>0</packageflag>
			<packageid />
		</emoticongift>
		<emoticonshared>
			<packageflag>0</packageflag>
			<packageid />
		</emoticonshared>
		<designershared>
			<designeruin>0</designeruin>
			<designername>null</designername>
			<designerrediretcturl><![CDATA[null]]></designerrediretcturl>
		</designershared>
		<emotionpageshared>
			<tid>0</tid>
			<title>null</title>
			<desc>null</desc>
			<iconUrl><![CDATA[null]]></iconUrl>
			<secondUrl>null</secondUrl>
			<pageType>0</pageType>
			<setKey>null</setKey>
		</emotionpageshared>
		<webviewshared>
			<shareUrlOriginal />
			<shareUrlOpen />
			<jsAppId />
			<publisherId />
			<publisherReqId />
		</webviewshared>
		<template_id />
		<md5 />
		<websearch>
			<rec_category>0</rec_category>
			<channelId>0</channelId>
		</websearch>
		<weappinfo>
			<username />
			<appid />
			<appservicetype>0</appservicetype>
			<secflagforsinglepagemode>0</secflagforsinglepagemode>
			<videopageinfo>
				<thumbwidth>0</thumbwidth>
				<thumbheight>0</thumbheight>
				<fromopensdk>0</fromopensdk>
			</videopageinfo>
		</weappinfo>
		<statextstr />
		<musicShareItem>
			<musicDuration>0</musicDuration>
		</musicShareItem>
		<finderLiveProductShare>
			<finderLiveID><![CDATA[]]></finderLiveID>
			<finderUsername><![CDATA[]]></finderUsername>
			<finderObjectID><![CDATA[]]></finderObjectID>
			<finderNonceID><![CDATA[]]></finderNonceID>
			<liveStatus><![CDATA[]]></liveStatus>
			<appId><![CDATA[]]></appId>
			<pagePath><![CDATA[]]></pagePath>
			<productId><![CDATA[]]></productId>
			<coverUrl><![CDATA[]]></coverUrl>
			<productTitle><![CDATA[]]></productTitle>
			<marketPrice><![CDATA[0]]></marketPrice>
			<sellingPrice><![CDATA[0]]></sellingPrice>
			<platformHeadImg><![CDATA[]]></platformHeadImg>
			<platformName><![CDATA[]]></platformName>
			<shopWindowId><![CDATA[]]></shopWindowId>
			<flashSalePrice><![CDATA[0]]></flashSalePrice>
			<flashSaleEndTime><![CDATA[0]]></flashSaleEndTime>
			<ecSource><![CDATA[]]></ecSource>
			<sellingPriceWording><![CDATA[]]></sellingPriceWording>
			<platformIconURL><![CDATA[]]></platformIconURL>
			<firstProductTagURL><![CDATA[]]></firstProductTagURL>
			<firstProductTagAspectRatioString><![CDATA[0.0]]></firstProductTagAspectRatioString>
			<secondProductTagURL><![CDATA[]]></secondProductTagURL>
			<secondProductTagAspectRatioString><![CDATA[0.0]]></secondProductTagAspectRatioString>
			<firstGuaranteeWording><![CDATA[]]></firstGuaranteeWording>
			<secondGuaranteeWording><![CDATA[]]></secondGuaranteeWording>
			<thirdGuaranteeWording><![CDATA[]]></thirdGuaranteeWording>
			<isPriceBeginShow>false</isPriceBeginShow>
			<lastGMsgID><![CDATA[]]></lastGMsgID>
			<promoterKey><![CDATA[]]></promoterKey>
			<discountWording><![CDATA[]]></discountWording>
			<priceSuffixDescription><![CDATA[]]></priceSuffixDescription>
			<productCardKey><![CDATA[]]></productCardKey>
			<isWxShop><![CDATA[]]></isWxShop>
			<brandIconUrl><![CDATA[]]></brandIconUrl>
			<rIconUrl><![CDATA[]]></rIconUrl>
			<rIconUrlDarkMode><![CDATA[]]></rIconUrlDarkMode>
			<topShopIconUrl><![CDATA[]]></topShopIconUrl>
			<topShopIconUrlDarkMode><![CDATA[]]></topShopIconUrlDarkMode>
			<simplifyTopShopIconUrl><![CDATA[]]></simplifyTopShopIconUrl>
			<simplifyTopShopIconUrlDarkmode><![CDATA[]]></simplifyTopShopIconUrlDarkmode>
			<topShopIconWidth><![CDATA[0]]></topShopIconWidth>
			<topShopIconHeight><![CDATA[0]]></topShopIconHeight>
			<simplifyTopShopIconWidth><![CDATA[0]]></simplifyTopShopIconWidth>
			<simplifyTopShopIconHeight><![CDATA[0]]></simplifyTopShopIconHeight>
			<showBoxItemStringList />
		</finderLiveProductShare>
		<finderOrder>
			<appID><![CDATA[]]></appID>
			<orderID><![CDATA[]]></orderID>
			<path><![CDATA[]]></path>
			<priceWording><![CDATA[]]></priceWording>
			<stateWording><![CDATA[]]></stateWording>
			<productImageURL><![CDATA[]]></productImageURL>
			<products><![CDATA[]]></products>
			<productsCount><![CDATA[0]]></productsCount>
			<orderType><![CDATA[0]]></orderType>
			<newPriceWording><![CDATA[]]></newPriceWording>
			<newStateWording><![CDATA[]]></newStateWording>
			<useNewWording><![CDATA[0]]></useNewWording>
		</finderOrder>
		<finderShopWindowShare>
			<finderUsername><![CDATA[]]></finderUsername>
			<avatar><![CDATA[]]></avatar>
			<nickname><![CDATA[]]></nickname>
			<commodityInStockCount><![CDATA[]]></commodityInStockCount>
			<appId><![CDATA[]]></appId>
			<path><![CDATA[]]></path>
			<appUsername><![CDATA[]]></appUsername>
			<query><![CDATA[]]></query>
			<liteAppId><![CDATA[]]></liteAppId>
			<liteAppPath><![CDATA[]]></liteAppPath>
			<liteAppQuery><![CDATA[]]></liteAppQuery>
			<platformTagURL><![CDATA[]]></platformTagURL>
			<saleWording><![CDATA[]]></saleWording>
			<lastGMsgID><![CDATA[]]></lastGMsgID>
			<profileTypeWording><![CDATA[]]></profileTypeWording>
			<saleWordingExtra><![CDATA[]]></saleWordingExtra>
			<isWxShop><![CDATA[]]></isWxShop>
			<platformIconUrl><![CDATA[]]></platformIconUrl>
			<brandIconUrl><![CDATA[]]></brandIconUrl>
			<description><![CDATA[]]></description>
			<backgroundUrl><![CDATA[]]></backgroundUrl>
			<darkModePlatformIconUrl><![CDATA[]]></darkModePlatformIconUrl>
			<rIconUrl><![CDATA[]]></rIconUrl>
			<rIconUrlDarkMode><![CDATA[]]></rIconUrlDarkMode>
			<rWords><![CDATA[]]></rWords>
			<topShopIconUrl><![CDATA[]]></topShopIconUrl>
			<topShopIconUrlDarkMode><![CDATA[]]></topShopIconUrlDarkMode>
			<simplifyTopShopIconUrl><![CDATA[]]></simplifyTopShopIconUrl>
			<simplifyTopShopIconUrlDarkmode><![CDATA[]]></simplifyTopShopIconUrlDarkmode>
			<topShopIconWidth><![CDATA[0]]></topShopIconWidth>
			<topShopIconHeight><![CDATA[0]]></topShopIconHeight>
			<simplifyTopShopIconWidth><![CDATA[0]]></simplifyTopShopIconWidth>
			<simplifyTopShopIconHeight><![CDATA[0]]></simplifyTopShopIconHeight>
			<reputationInfo>
				<hasReputationInfo>0</hasReputationInfo>
				<reputationScore>0</reputationScore>
				<reputationWording />
				<reputationTextColor />
				<reputationLevelWording />
				<reputationBackgroundColor />
			</reputationInfo>
			<productImageURLList />
		</finderShopWindowShare>
		<findernamecard>
			<username />
			<avatar><![CDATA[]]></avatar>
			<nickname />
			<auth_job />
			<auth_icon>0</auth_icon>
			<auth_icon_url />
			<ecSource><![CDATA[]]></ecSource>
			<lastGMsgID><![CDATA[]]></lastGMsgID>
		</findernamecard>
		<finderGuarantee>
			<scene><![CDATA[0]]></scene>
		</finderGuarantee>
		<directshare>0</directshare>
		<gamecenter>
			<namecard>
				<iconUrl />
				<name />
				<desc />
				<tail />
				<jumpUrl />
				<liteappId />
				<liteappPath />
				<liteappQuery />
				<liteappMinVersion />
			</namecard>
		</gamecenter>
		<patMsg>
			<chatUser />
			<records>
				<recordNum>0</recordNum>
			</records>
		</patMsg>
		<secretmsg>
			<issecretmsg>0</issecretmsg>
		</secretmsg>
		<referfromscene>0</referfromscene>
		<gameshare>
			<liteappext>
				<liteappbizdata />
				<priority>0</priority>
			</liteappext>
			<appbrandext>
				<litegameinfo />
				<priority>-1</priority>
			</appbrandext>
			<gameshareid />
			<sharedata />
			<isvideo>0</isvideo>
			<duration>-1</duration>
			<isexposed>0</isexposed>
			<readtext />
		</gameshare>
		<tingChatRoomItem>
			<type>0</type>
			<categoryItem>null</categoryItem>
			<categoryId />
		</tingChatRoomItem>
		<mpsharetrace>
			<hasfinderelement>0</hasfinderelement>
			<lastgmsgid />
		</mpsharetrace>
		<wxgamecard>
			<framesetname />
			<mbcarddata />
			<minpkgversion />
			<clientextinfo />
			<mbcardheight>0</mbcardheight>
			<isoldversion>0</isoldversion>
		</wxgamecard>
		<ecskfcard>
			<framesetname />
			<mbcarddata />
			<minupdateunixtimestamp>0</minupdateunixtimestamp>
			<needheader>false</needheader>
			<summary />
		</ecskfcard>
		<liteapp>
			<id>null</id>
			<path />
			<query />
			<istransparent>0</istransparent>
			<hideicon>0</hideicon>
			<forbidforward>0</forbidforward>
		</liteapp>
		<opensdk_share_is_modified>0</opensdk_share_is_modified>
	</appmsg>
	<fromusername>wxid_7o4g44suf20t22</fromusername>
	<scene>0</scene>
	<appinfo>
		<version>1</version>
		<appname />
	</appinfo>
	<commenturl />
</msg>

2025-07-30 12:12:39 | INFO |   - 引用消息发送人: wxid_7o4g44suf20t22
2025-07-30 12:12:40 | DEBUG | 收到消息: {'MsgId': 1693025407, 'FromUserName': {'string': '***********@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 49, 'Content': {'string': 'wxid_laurnst5xn0q22:\n<?xml version="1.0"?>\n<msg>\n\t<appmsg appid="" sdkver="0">\n\t\t<title>这个好真啊</title>\n\t\t<des />\n\t\t<username />\n\t\t<action>view</action>\n\t\t<type>57</type>\n\t\t<showtype>0</showtype>\n\t\t<content />\n\t\t<url />\n\t\t<lowurl />\n\t\t<forwardflag>0</forwardflag>\n\t\t<dataurl />\n\t\t<lowdataurl />\n\t\t<contentattr>0</contentattr>\n\t\t<streamvideo>\n\t\t\t<streamvideourl />\n\t\t\t<streamvideototaltime>0</streamvideototaltime>\n\t\t\t<streamvideotitle />\n\t\t\t<streamvideowording />\n\t\t\t<streamvideoweburl />\n\t\t\t<streamvideothumburl />\n\t\t\t<streamvideoaduxinfo />\n\t\t\t<streamvideopublishid />\n\t\t</streamvideo>\n\t\t<canvasPageItem>\n\t\t\t<canvasPageXml><![CDATA[]]></canvasPageXml>\n\t\t</canvasPageItem>\n\t\t<refermsg>\n\t\t\t<type>3</type>\n\t\t\t<svrid>22859552143168783</svrid>\n\t\t\t<fromusr>***********@chatroom</fromusr>\n\t\t\t<chatusr>wxid_srknxij3jka022</chatusr>\n\t\t\t<displayname>她</displayname>\n\t\t\t<msgsource>&lt;msgsource&gt;\n\t&lt;alnode&gt;\n\t\t&lt;fr&gt;2&lt;/fr&gt;\n\t&lt;/alnode&gt;\n\t&lt;sec_msg_node&gt;\n\t\t&lt;uuid&gt;c314b5b40c3b20d1115c2f39cf4ef299_&lt;/uuid&gt;\n\t\t&lt;risk-file-flag /&gt;\n\t\t&lt;risk-file-md5-list /&gt;\n\t&lt;/sec_msg_node&gt;\n\t&lt;imgmsg_pd cdnmidimgurl_size="24991" cdnmidimgurl_pd_pri="50" cdnmidimgurl_pd="1" /&gt;\n\t&lt;silence&gt;1&lt;/silence&gt;\n\t&lt;membercount&gt;73&lt;/membercount&gt;\n\t&lt;NotAutoDownloadRange&gt;20:00-22:00;00:00-01:00&lt;/NotAutoDownloadRange&gt;\n\t&lt;signature&gt;N0_V1_GMkscd8S|v1_urG02yXR&lt;/signature&gt;\n\t&lt;tmp_node&gt;\n\t\t&lt;publisher-id&gt;&lt;/publisher-id&gt;\n\t&lt;/tmp_node&gt;\n&lt;/msgsource&gt;\n</msgsource>\n\t\t\t<content>&lt;?xml version="1.0"?&gt;\n&lt;msg&gt;\n\t&lt;img aeskey="c861c2b56672441758555580b272cf7d" encryver="1" cdnthumbaeskey="c861c2b56672441758555580b272cf7d" cdnthumburl="3057020100044b304902010002049c5da34302032e6c63020455ab016f020468899b67042437373433373134312d333036322d346534632d383362342d366536323463663136636336020405292a010201000405004c537700" cdnthumblength="5184" cdnthumbheight="120" cdnthumbwidth="92" cdnmidheight="0" cdnmidwidth="0" cdnhdheight="0" cdnhdwidth="0" cdnmidimgurl="3057020100044b304902010002049c5da34302032e6c63020455ab016f020468899b67042437373433373134312d333036322d346534632d383362342d366536323463663136636336020405292a010201000405004c537700" length="24991" cdnbigimgurl="3057020100044b304902010002049c5da34302032e6c63020455ab016f020468899b67042437373433373134312d333036322d346534632d383362342d366536323463663136636336020405292a010201000405004c537700" hdlength="414288" md5="65c99cc1a75fa59597994817523951fe" hevc_mid_size="24991" originsourcemd5="65c99cc1a75fa59597994817523951fe"&gt;\n\t\t&lt;secHashInfoBase64 /&gt;\n\t\t&lt;live&gt;\n\t\t\t&lt;duration&gt;0&lt;/duration&gt;\n\t\t\t&lt;size&gt;0&lt;/size&gt;\n\t\t\t&lt;md5 /&gt;\n\t\t\t&lt;fileid /&gt;\n\t\t\t&lt;hdsize&gt;0&lt;/hdsize&gt;\n\t\t\t&lt;hdmd5 /&gt;\n\t\t\t&lt;hdfileid /&gt;\n\t\t\t&lt;stillimagetimems&gt;0&lt;/stillimagetimems&gt;\n\t\t&lt;/live&gt;\n\t&lt;/img&gt;\n\t&lt;appinfo&gt;\n\t\t&lt;appid&gt;wxe3ad19e142df87b3&lt;/appid&gt;\n\t\t&lt;version&gt;5&lt;/version&gt;\n\t\t&lt;appname&gt;麻豆约拍&lt;/appname&gt;\n\t\t&lt;isforceupdate&gt;0&lt;/isforceupdate&gt;\n\t\t&lt;messageaction /&gt;\n\t\t&lt;messageext /&gt;\n\t\t&lt;mediatagname /&gt;\n\t&lt;/appinfo&gt;\n\t&lt;platform_signature /&gt;\n\t&lt;imgdatahash /&gt;\n\t&lt;ImgSourceInfo&gt;\n\t\t&lt;ImgSourceUrl /&gt;\n\t\t&lt;BizType&gt;0&lt;/BizType&gt;\n\t&lt;/ImgSourceInfo&gt;\n&lt;/msg&gt;\n</content>\n\t\t\t<strid />\n\t\t\t<createtime>1753848679</createtime>\n\t\t</refermsg>\n\t\t<appattach>\n\t\t\t<totallen>0</totallen>\n\t\t\t<attachid />\n\t\t\t<cdnattachurl />\n\t\t\t<emoticonmd5 />\n\t\t\t<aeskey />\n\t\t\t<fileext />\n\t\t\t<islargefilemsg>0</islargefilemsg>\n\t\t</appattach>\n\t\t<extinfo />\n\t\t<androidsource>0</androidsource>\n\t\t<thumburl />\n\t\t<mediatagname />\n\t\t<messageaction><![CDATA[]]></messageaction>\n\t\t<messageext><![CDATA[]]></messageext>\n\t\t<emoticongift>\n\t\t\t<packageflag>0</packageflag>\n\t\t\t<packageid />\n\t\t</emoticongift>\n\t\t<emoticonshared>\n\t\t\t<packageflag>0</packageflag>\n\t\t\t<packageid />\n\t\t</emoticonshared>\n\t\t<designershared>\n\t\t\t<designeruin>0</designeruin>\n\t\t\t<designername>null</designername>\n\t\t\t<designerrediretcturl><![CDATA[null]]></designerrediretcturl>\n\t\t</designershared>\n\t\t<emotionpageshared>\n\t\t\t<tid>0</tid>\n\t\t\t<title>null</title>\n\t\t\t<desc>null</desc>\n\t\t\t<iconUrl><![CDATA[null]]></iconUrl>\n\t\t\t<secondUrl>null</secondUrl>\n\t\t\t<pageType>0</pageType>\n\t\t\t<setKey>null</setKey>\n\t\t</emotionpageshared>\n\t\t<webviewshared>\n\t\t\t<shareUrlOriginal />\n\t\t\t<shareUrlOpen />\n\t\t\t<jsAppId />\n\t\t\t<publisherId />\n\t\t\t<publisherReqId />\n\t\t</webviewshared>\n\t\t<template_id />\n\t\t<md5 />\n\t\t<websearch>\n\t\t\t<rec_category>0</rec_category>\n\t\t\t<channelId>0</channelId>\n\t\t</websearch>\n\t\t<weappinfo>\n\t\t\t<username />\n\t\t\t<appid />\n\t\t\t<appservicetype>0</appservicetype>\n\t\t\t<secflagforsinglepagemode>0</secflagforsinglepagemode>\n\t\t\t<videopageinfo>\n\t\t\t\t<thumbwidth>0</thumbwidth>\n\t\t\t\t<thumbheight>0</thumbheight>\n\t\t\t\t<fromopensdk>0</fromopensdk>\n\t\t\t</videopageinfo>\n\t\t</weappinfo>\n\t\t<statextstr />\n\t\t<musicShareItem>\n\t\t\t<musicDuration>0</musicDuration>\n\t\t</musicShareItem>\n\t\t<finderLiveProductShare>\n\t\t\t<finderLiveID><![CDATA[]]></finderLiveID>\n\t\t\t<finderUsername><![CDATA[]]></finderUsername>\n\t\t\t<finderObjectID><![CDATA[]]></finderObjectID>\n\t\t\t<finderNonceID><![CDATA[]]></finderNonceID>\n\t\t\t<liveStatus><![CDATA[]]></liveStatus>\n\t\t\t<appId><![CDATA[]]></appId>\n\t\t\t<pagePath><![CDATA[]]></pagePath>\n\t\t\t<productId><![CDATA[]]></productId>\n\t\t\t<coverUrl><![CDATA[]]></coverUrl>\n\t\t\t<productTitle><![CDATA[]]></productTitle>\n\t\t\t<marketPrice><![CDATA[0]]></marketPrice>\n\t\t\t<sellingPrice><![CDATA[0]]></sellingPrice>\n\t\t\t<platformHeadImg><![CDATA[]]></platformHeadImg>\n\t\t\t<platformName><![CDATA[]]></platformName>\n\t\t\t<shopWindowId><![CDATA[]]></shopWindowId>\n\t\t\t<flashSalePrice><![CDATA[0]]></flashSalePrice>\n\t\t\t<flashSaleEndTime><![CDATA[0]]></flashSaleEndTime>\n\t\t\t<ecSource><![CDATA[]]></ecSource>\n\t\t\t<sellingPriceWording><![CDATA[]]></sellingPriceWording>\n\t\t\t<platformIconURL><![CDATA[]]></platformIconURL>\n\t\t\t<firstProductTagURL><![CDATA[]]></firstProductTagURL>\n\t\t\t<firstProductTagAspectRatioString><![CDATA[0.0]]></firstProductTagAspectRatioString>\n\t\t\t<secondProductTagURL><![CDATA[]]></secondProductTagURL>\n\t\t\t<secondProductTagAspectRatioString><![CDATA[0.0]]></secondProductTagAspectRatioString>\n\t\t\t<firstGuaranteeWording><![CDATA[]]></firstGuaranteeWording>\n\t\t\t<secondGuaranteeWording><![CDATA[]]></secondGuaranteeWording>\n\t\t\t<thirdGuaranteeWording><![CDATA[]]></thirdGuaranteeWording>\n\t\t\t<isPriceBeginShow>false</isPriceBeginShow>\n\t\t\t<lastGMsgID><![CDATA[]]></lastGMsgID>\n\t\t\t<promoterKey><![CDATA[]]></promoterKey>\n\t\t\t<discountWording><![CDATA[]]></discountWording>\n\t\t\t<priceSuffixDescription><![CDATA[]]></priceSuffixDescription>\n\t\t\t<productCardKey><![CDATA[]]></productCardKey>\n\t\t\t<isWxShop><![CDATA[]]></isWxShop>\n\t\t\t<brandIconUrl><![CDATA[]]></brandIconUrl>\n\t\t\t<rIconUrl><![CDATA[]]></rIconUrl>\n\t\t\t<rIconUrlDarkMode><![CDATA[]]></rIconUrlDarkMode>\n\t\t\t<topShopIconUrl><![CDATA[]]></topShopIconUrl>\n\t\t\t<topShopIconUrlDarkMode><![CDATA[]]></topShopIconUrlDarkMode>\n\t\t\t<simplifyTopShopIconUrl><![CDATA[]]></simplifyTopShopIconUrl>\n\t\t\t<simplifyTopShopIconUrlDarkmode><![CDATA[]]></simplifyTopShopIconUrlDarkmode>\n\t\t\t<topShopIconWidth><![CDATA[0]]></topShopIconWidth>\n\t\t\t<topShopIconHeight><![CDATA[0]]></topShopIconHeight>\n\t\t\t<simplifyTopShopIconWidth><![CDATA[0]]></simplifyTopShopIconWidth>\n\t\t\t<simplifyTopShopIconHeight><![CDATA[0]]></simplifyTopShopIconHeight>\n\t\t\t<showBoxItemStringList />\n\t\t</finderLiveProductShare>\n\t\t<finderOrder>\n\t\t\t<appID><![CDATA[]]></appID>\n\t\t\t<orderID><![CDATA[]]></orderID>\n\t\t\t<path><![CDATA[]]></path>\n\t\t\t<priceWording><![CDATA[]]></priceWording>\n\t\t\t<stateWording><![CDATA[]]></stateWording>\n\t\t\t<productImageURL><![CDATA[]]></productImageURL>\n\t\t\t<products><![CDATA[]]></products>\n\t\t\t<productsCount><![CDATA[0]]></productsCount>\n\t\t\t<orderType><![CDATA[0]]></orderType>\n\t\t\t<newPriceWording><![CDATA[]]></newPriceWording>\n\t\t\t<newStateWording><![CDATA[]]></newStateWording>\n\t\t\t<useNewWording><![CDATA[0]]></useNewWording>\n\t\t</finderOrder>\n\t\t<finderShopWindowShare>\n\t\t\t<finderUsername><![CDATA[]]></finderUsername>\n\t\t\t<avatar><![CDATA[]]></avatar>\n\t\t\t<nickname><![CDATA[]]></nickname>\n\t\t\t<commodityInStockCount><![CDATA[]]></commodityInStockCount>\n\t\t\t<appId><![CDATA[]]></appId>\n\t\t\t<path><![CDATA[]]></path>\n\t\t\t<appUsername><![CDATA[]]></appUsername>\n\t\t\t<query><![CDATA[]]></query>\n\t\t\t<liteAppId><![CDATA[]]></liteAppId>\n\t\t\t<liteAppPath><![CDATA[]]></liteAppPath>\n\t\t\t<liteAppQuery><![CDATA[]]></liteAppQuery>\n\t\t\t<platformTagURL><![CDATA[]]></platformTagURL>\n\t\t\t<saleWording><![CDATA[]]></saleWording>\n\t\t\t<lastGMsgID><![CDATA[]]></lastGMsgID>\n\t\t\t<profileTypeWording><![CDATA[]]></profileTypeWording>\n\t\t\t<saleWordingExtra><![CDATA[]]></saleWordingExtra>\n\t\t\t<isWxShop><![CDATA[]]></isWxShop>\n\t\t\t<platformIconUrl><![CDATA[]]></platformIconUrl>\n\t\t\t<brandIconUrl><![CDATA[]]></brandIconUrl>\n\t\t\t<description><![CDATA[]]></description>\n\t\t\t<backgroundUrl><![CDATA[]]></backgroundUrl>\n\t\t\t<darkModePlatformIconUrl><![CDATA[]]></darkModePlatformIconUrl>\n\t\t\t<rIconUrl><![CDATA[]]></rIconUrl>\n\t\t\t<rIconUrlDarkMode><![CDATA[]]></rIconUrlDarkMode>\n\t\t\t<rWords><![CDATA[]]></rWords>\n\t\t\t<topShopIconUrl><![CDATA[]]></topShopIconUrl>\n\t\t\t<topShopIconUrlDarkMode><![CDATA[]]></topShopIconUrlDarkMode>\n\t\t\t<simplifyTopShopIconUrl><![CDATA[]]></simplifyTopShopIconUrl>\n\t\t\t<simplifyTopShopIconUrlDarkmode><![CDATA[]]></simplifyTopShopIconUrlDarkmode>\n\t\t\t<topShopIconWidth><![CDATA[0]]></topShopIconWidth>\n\t\t\t<topShopIconHeight><![CDATA[0]]></topShopIconHeight>\n\t\t\t<simplifyTopShopIconWidth><![CDATA[0]]></simplifyTopShopIconWidth>\n\t\t\t<simplifyTopShopIconHeight><![CDATA[0]]></simplifyTopShopIconHeight>\n\t\t\t<reputationInfo>\n\t\t\t\t<hasReputationInfo>0</hasReputationInfo>\n\t\t\t\t<reputationScore>0</reputationScore>\n\t\t\t\t<reputationWording />\n\t\t\t\t<reputationTextColor />\n\t\t\t\t<reputationLevelWording />\n\t\t\t\t<reputationBackgroundColor />\n\t\t\t</reputationInfo>\n\t\t\t<productImageURLList />\n\t\t</finderShopWindowShare>\n\t\t<findernamecard>\n\t\t\t<username />\n\t\t\t<avatar><![CDATA[]]></avatar>\n\t\t\t<nickname />\n\t\t\t<auth_job />\n\t\t\t<auth_icon>0</auth_icon>\n\t\t\t<auth_icon_url />\n\t\t\t<ecSource><![CDATA[]]></ecSource>\n\t\t\t<lastGMsgID><![CDATA[]]></lastGMsgID>\n\t\t</findernamecard>\n\t\t<finderGuarantee>\n\t\t\t<scene><![CDATA[0]]></scene>\n\t\t</finderGuarantee>\n\t\t<directshare>0</directshare>\n\t\t<gamecenter>\n\t\t\t<namecard>\n\t\t\t\t<iconUrl />\n\t\t\t\t<name />\n\t\t\t\t<desc />\n\t\t\t\t<tail />\n\t\t\t\t<jumpUrl />\n\t\t\t\t<liteappId />\n\t\t\t\t<liteappPath />\n\t\t\t\t<liteappQuery />\n\t\t\t\t<liteappMinVersion />\n\t\t\t</namecard>\n\t\t</gamecenter>\n\t\t<patMsg>\n\t\t\t<chatUser />\n\t\t\t<records>\n\t\t\t\t<recordNum>0</recordNum>\n\t\t\t</records>\n\t\t</patMsg>\n\t\t<secretmsg>\n\t\t\t<issecretmsg>0</issecretmsg>\n\t\t</secretmsg>\n\t\t<referfromscene>0</referfromscene>\n\t\t<gameshare>\n\t\t\t<liteappext>\n\t\t\t\t<liteappbizdata />\n\t\t\t\t<priority>0</priority>\n\t\t\t</liteappext>\n\t\t\t<appbrandext>\n\t\t\t\t<litegameinfo />\n\t\t\t\t<priority>-1</priority>\n\t\t\t</appbrandext>\n\t\t\t<gameshareid />\n\t\t\t<sharedata />\n\t\t\t<isvideo>0</isvideo>\n\t\t\t<duration>-1</duration>\n\t\t\t<isexposed>0</isexposed>\n\t\t\t<readtext />\n\t\t</gameshare>\n\t\t<tingChatRoomItem>\n\t\t\t<type>0</type>\n\t\t\t<categoryItem>null</categoryItem>\n\t\t\t<categoryId />\n\t\t\t<listenItem>null</listenItem>\n\t\t</tingChatRoomItem>\n\t\t<mpsharetrace>\n\t\t\t<hasfinderelement>0</hasfinderelement>\n\t\t\t<lastgmsgid />\n\t\t</mpsharetrace>\n\t\t<wxgamecard>\n\t\t\t<framesetname />\n\t\t\t<mbcarddata />\n\t\t\t<minpkgversion />\n\t\t\t<clientextinfo />\n\t\t\t<mbcardheight>0</mbcardheight>\n\t\t\t<isoldversion>0</isoldversion>\n\t\t</wxgamecard>\n\t\t<ecskfcard>\n\t\t\t<framesetname />\n\t\t\t<mbcarddata />\n\t\t\t<minupdateunixtimestamp>0</minupdateunixtimestamp>\n\t\t\t<needheader>false</needheader>\n\t\t\t<summary />\n\t\t</ecskfcard>\n\t\t<liteapp>\n\t\t\t<id>null</id>\n\t\t\t<path />\n\t\t\t<query />\n\t\t\t<istransparent>0</istransparent>\n\t\t\t<hideicon>0</hideicon>\n\t\t\t<forbidforward>0</forbidforward>\n\t\t</liteapp>\n\t\t<opensdk_share_is_modified>0</opensdk_share_is_modified>\n\t</appmsg>\n\t<fromusername>wxid_laurnst5xn0q22</fromusername>\n\t<scene>0</scene>\n\t<appinfo>\n\t\t<version>1</version>\n\t\t<appname></appname>\n\t</appinfo>\n\t<commenturl></commenturl>\n</msg>\n'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1753848772, 'MsgSource': '<msgsource>\n\t<alnode>\n\t\t<fr>4</fr>\n\t</alnode>\n\t<eggIncluded>1</eggIncluded>\n\t<sec_msg_node>\n\t\t<uuid>c314b5b40c3b20d1115c2f39cf4ef299_</uuid>\n\t\t<risk-file-flag />\n\t\t<risk-file-md5-list />\n\t</sec_msg_node>\n\t<silence>0</silence>\n\t<membercount>73</membercount>\n\t<signature>N0_V1_J9GjbAlo|v1_L87x7wSh</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'PushContent': 'متسول暗 : 这个好真啊', 'NewMsgId': 8582723612119003088, 'MsgSeq': 871411560}
2025-07-30 12:12:40 | DEBUG | 从群聊消息中提取发送者: wxid_laurnst5xn0q22
2025-07-30 12:12:40 | DEBUG | 使用已解析的XML处理引用消息
2025-07-30 12:12:40 | INFO | 收到引用消息: 消息ID:1693025407 来自:***********@chatroom 发送人:wxid_laurnst5xn0q22 内容:这个好真啊 引用类型:3
2025-07-30 12:12:41 | INFO | [DouBaoImageToImage] 收到引用消息: 这个好真啊
2025-07-30 12:12:41 | INFO | [TimerTask] 收到引用消息调试信息:
2025-07-30 12:12:41 | INFO |   - 消息内容: 这个好真啊
2025-07-30 12:12:41 | INFO |   - 群组ID: ***********@chatroom
2025-07-30 12:12:41 | INFO |   - 发送人: wxid_laurnst5xn0q22
2025-07-30 12:12:41 | INFO |   - 引用信息: {'MsgType': 3, 'Content': '<?xml version="1.0"?>\n<msg>\n\t<appmsg appid="" sdkver="0">\n\t\t<title>这个好真啊</title>\n\t\t<des />\n\t\t<username />\n\t\t<action>view</action>\n\t\t<type>57</type>\n\t\t<showtype>0</showtype>\n\t\t<content />\n\t\t<url />\n\t\t<lowurl />\n\t\t<forwardflag>0</forwardflag>\n\t\t<dataurl />\n\t\t<lowdataurl />\n\t\t<contentattr>0</contentattr>\n\t\t<streamvideo>\n\t\t\t<streamvideourl />\n\t\t\t<streamvideototaltime>0</streamvideototaltime>\n\t\t\t<streamvideotitle />\n\t\t\t<streamvideowording />\n\t\t\t<streamvideoweburl />\n\t\t\t<streamvideothumburl />\n\t\t\t<streamvideoaduxinfo />\n\t\t\t<streamvideopublishid />\n\t\t</streamvideo>\n\t\t<canvasPageItem>\n\t\t\t<canvasPageXml><![CDATA[]]></canvasPageXml>\n\t\t</canvasPageItem>\n\t\t<refermsg>\n\t\t\t<type>3</type>\n\t\t\t<svrid>22859552143168783</svrid>\n\t\t\t<fromusr>***********@chatroom</fromusr>\n\t\t\t<chatusr>wxid_srknxij3jka022</chatusr>\n\t\t\t<displayname>她</displayname>\n\t\t\t<msgsource>&lt;msgsource&gt;\n\t&lt;alnode&gt;\n\t\t&lt;fr&gt;2&lt;/fr&gt;\n\t&lt;/alnode&gt;\n\t&lt;sec_msg_node&gt;\n\t\t&lt;uuid&gt;c314b5b40c3b20d1115c2f39cf4ef299_&lt;/uuid&gt;\n\t\t&lt;risk-file-flag /&gt;\n\t\t&lt;risk-file-md5-list /&gt;\n\t&lt;/sec_msg_node&gt;\n\t&lt;imgmsg_pd cdnmidimgurl_size="24991" cdnmidimgurl_pd_pri="50" cdnmidimgurl_pd="1" /&gt;\n\t&lt;silence&gt;1&lt;/silence&gt;\n\t&lt;membercount&gt;73&lt;/membercount&gt;\n\t&lt;NotAutoDownloadRange&gt;20:00-22:00;00:00-01:00&lt;/NotAutoDownloadRange&gt;\n\t&lt;signature&gt;N0_V1_GMkscd8S|v1_urG02yXR&lt;/signature&gt;\n\t&lt;tmp_node&gt;\n\t\t&lt;publisher-id&gt;&lt;/publisher-id&gt;\n\t&lt;/tmp_node&gt;\n&lt;/msgsource&gt;\n</msgsource>\n\t\t\t<content>&lt;?xml version="1.0"?&gt;\n&lt;msg&gt;\n\t&lt;img aeskey="c861c2b56672441758555580b272cf7d" encryver="1" cdnthumbaeskey="c861c2b56672441758555580b272cf7d" cdnthumburl="3057020100044b304902010002049c5da34302032e6c63020455ab016f020468899b67042437373433373134312d333036322d346534632d383362342d366536323463663136636336020405292a010201000405004c537700" cdnthumblength="5184" cdnthumbheight="120" cdnthumbwidth="92" cdnmidheight="0" cdnmidwidth="0" cdnhdheight="0" cdnhdwidth="0" cdnmidimgurl="3057020100044b304902010002049c5da34302032e6c63020455ab016f020468899b67042437373433373134312d333036322d346534632d383362342d366536323463663136636336020405292a010201000405004c537700" length="24991" cdnbigimgurl="3057020100044b304902010002049c5da34302032e6c63020455ab016f020468899b67042437373433373134312d333036322d346534632d383362342d366536323463663136636336020405292a010201000405004c537700" hdlength="414288" md5="65c99cc1a75fa59597994817523951fe" hevc_mid_size="24991" originsourcemd5="65c99cc1a75fa59597994817523951fe"&gt;\n\t\t&lt;secHashInfoBase64 /&gt;\n\t\t&lt;live&gt;\n\t\t\t&lt;duration&gt;0&lt;/duration&gt;\n\t\t\t&lt;size&gt;0&lt;/size&gt;\n\t\t\t&lt;md5 /&gt;\n\t\t\t&lt;fileid /&gt;\n\t\t\t&lt;hdsize&gt;0&lt;/hdsize&gt;\n\t\t\t&lt;hdmd5 /&gt;\n\t\t\t&lt;hdfileid /&gt;\n\t\t\t&lt;stillimagetimems&gt;0&lt;/stillimagetimems&gt;\n\t\t&lt;/live&gt;\n\t&lt;/img&gt;\n\t&lt;appinfo&gt;\n\t\t&lt;appid&gt;wxe3ad19e142df87b3&lt;/appid&gt;\n\t\t&lt;version&gt;5&lt;/version&gt;\n\t\t&lt;appname&gt;麻豆约拍&lt;/appname&gt;\n\t\t&lt;isforceupdate&gt;0&lt;/isforceupdate&gt;\n\t\t&lt;messageaction /&gt;\n\t\t&lt;messageext /&gt;\n\t\t&lt;mediatagname /&gt;\n\t&lt;/appinfo&gt;\n\t&lt;platform_signature /&gt;\n\t&lt;imgdatahash /&gt;\n\t&lt;ImgSourceInfo&gt;\n\t\t&lt;ImgSourceUrl /&gt;\n\t\t&lt;BizType&gt;0&lt;/BizType&gt;\n\t&lt;/ImgSourceInfo&gt;\n&lt;/msg&gt;\n</content>\n\t\t\t<strid />\n\t\t\t<createtime>1753848679</createtime>\n\t\t</refermsg>\n\t\t<appattach>\n\t\t\t<totallen>0</totallen>\n\t\t\t<attachid />\n\t\t\t<cdnattachurl />\n\t\t\t<emoticonmd5 />\n\t\t\t<aeskey />\n\t\t\t<fileext />\n\t\t\t<islargefilemsg>0</islargefilemsg>\n\t\t</appattach>\n\t\t<extinfo />\n\t\t<androidsource>0</androidsource>\n\t\t<thumburl />\n\t\t<mediatagname />\n\t\t<messageaction><![CDATA[]]></messageaction>\n\t\t<messageext><![CDATA[]]></messageext>\n\t\t<emoticongift>\n\t\t\t<packageflag>0</packageflag>\n\t\t\t<packageid />\n\t\t</emoticongift>\n\t\t<emoticonshared>\n\t\t\t<packageflag>0</packageflag>\n\t\t\t<packageid />\n\t\t</emoticonshared>\n\t\t<designershared>\n\t\t\t<designeruin>0</designeruin>\n\t\t\t<designername>null</designername>\n\t\t\t<designerrediretcturl><![CDATA[null]]></designerrediretcturl>\n\t\t</designershared>\n\t\t<emotionpageshared>\n\t\t\t<tid>0</tid>\n\t\t\t<title>null</title>\n\t\t\t<desc>null</desc>\n\t\t\t<iconUrl><![CDATA[null]]></iconUrl>\n\t\t\t<secondUrl>null</secondUrl>\n\t\t\t<pageType>0</pageType>\n\t\t\t<setKey>null</setKey>\n\t\t</emotionpageshared>\n\t\t<webviewshared>\n\t\t\t<shareUrlOriginal />\n\t\t\t<shareUrlOpen />\n\t\t\t<jsAppId />\n\t\t\t<publisherId />\n\t\t\t<publisherReqId />\n\t\t</webviewshared>\n\t\t<template_id />\n\t\t<md5 />\n\t\t<websearch>\n\t\t\t<rec_category>0</rec_category>\n\t\t\t<channelId>0</channelId>\n\t\t</websearch>\n\t\t<weappinfo>\n\t\t\t<username />\n\t\t\t<appid />\n\t\t\t<appservicetype>0</appservicetype>\n\t\t\t<secflagforsinglepagemode>0</secflagforsinglepagemode>\n\t\t\t<videopageinfo>\n\t\t\t\t<thumbwidth>0</thumbwidth>\n\t\t\t\t<thumbheight>0</thumbheight>\n\t\t\t\t<fromopensdk>0</fromopensdk>\n\t\t\t</videopageinfo>\n\t\t</weappinfo>\n\t\t<statextstr />\n\t\t<musicShareItem>\n\t\t\t<musicDuration>0</musicDuration>\n\t\t</musicShareItem>\n\t\t<finderLiveProductShare>\n\t\t\t<finderLiveID><![CDATA[]]></finderLiveID>\n\t\t\t<finderUsername><![CDATA[]]></finderUsername>\n\t\t\t<finderObjectID><![CDATA[]]></finderObjectID>\n\t\t\t<finderNonceID><![CDATA[]]></finderNonceID>\n\t\t\t<liveStatus><![CDATA[]]></liveStatus>\n\t\t\t<appId><![CDATA[]]></appId>\n\t\t\t<pagePath><![CDATA[]]></pagePath>\n\t\t\t<productId><![CDATA[]]></productId>\n\t\t\t<coverUrl><![CDATA[]]></coverUrl>\n\t\t\t<productTitle><![CDATA[]]></productTitle>\n\t\t\t<marketPrice><![CDATA[0]]></marketPrice>\n\t\t\t<sellingPrice><![CDATA[0]]></sellingPrice>\n\t\t\t<platformHeadImg><![CDATA[]]></platformHeadImg>\n\t\t\t<platformName><![CDATA[]]></platformName>\n\t\t\t<shopWindowId><![CDATA[]]></shopWindowId>\n\t\t\t<flashSalePrice><![CDATA[0]]></flashSalePrice>\n\t\t\t<flashSaleEndTime><![CDATA[0]]></flashSaleEndTime>\n\t\t\t<ecSource><![CDATA[]]></ecSource>\n\t\t\t<sellingPriceWording><![CDATA[]]></sellingPriceWording>\n\t\t\t<platformIconURL><![CDATA[]]></platformIconURL>\n\t\t\t<firstProductTagURL><![CDATA[]]></firstProductTagURL>\n\t\t\t<firstProductTagAspectRatioString><![CDATA[0.0]]></firstProductTagAspectRatioString>\n\t\t\t<secondProductTagURL><![CDATA[]]></secondProductTagURL>\n\t\t\t<secondProductTagAspectRatioString><![CDATA[0.0]]></secondProductTagAspectRatioString>\n\t\t\t<firstGuaranteeWording><![CDATA[]]></firstGuaranteeWording>\n\t\t\t<secondGuaranteeWording><![CDATA[]]></secondGuaranteeWording>\n\t\t\t<thirdGuaranteeWording><![CDATA[]]></thirdGuaranteeWording>\n\t\t\t<isPriceBeginShow>false</isPriceBeginShow>\n\t\t\t<lastGMsgID><![CDATA[]]></lastGMsgID>\n\t\t\t<promoterKey><![CDATA[]]></promoterKey>\n\t\t\t<discountWording><![CDATA[]]></discountWording>\n\t\t\t<priceSuffixDescription><![CDATA[]]></priceSuffixDescription>\n\t\t\t<productCardKey><![CDATA[]]></productCardKey>\n\t\t\t<isWxShop><![CDATA[]]></isWxShop>\n\t\t\t<brandIconUrl><![CDATA[]]></brandIconUrl>\n\t\t\t<rIconUrl><![CDATA[]]></rIconUrl>\n\t\t\t<rIconUrlDarkMode><![CDATA[]]></rIconUrlDarkMode>\n\t\t\t<topShopIconUrl><![CDATA[]]></topShopIconUrl>\n\t\t\t<topShopIconUrlDarkMode><![CDATA[]]></topShopIconUrlDarkMode>\n\t\t\t<simplifyTopShopIconUrl><![CDATA[]]></simplifyTopShopIconUrl>\n\t\t\t<simplifyTopShopIconUrlDarkmode><![CDATA[]]></simplifyTopShopIconUrlDarkmode>\n\t\t\t<topShopIconWidth><![CDATA[0]]></topShopIconWidth>\n\t\t\t<topShopIconHeight><![CDATA[0]]></topShopIconHeight>\n\t\t\t<simplifyTopShopIconWidth><![CDATA[0]]></simplifyTopShopIconWidth>\n\t\t\t<simplifyTopShopIconHeight><![CDATA[0]]></simplifyTopShopIconHeight>\n\t\t\t<showBoxItemStringList />\n\t\t</finderLiveProductShare>\n\t\t<finderOrder>\n\t\t\t<appID><![CDATA[]]></appID>\n\t\t\t<orderID><![CDATA[]]></orderID>\n\t\t\t<path><![CDATA[]]></path>\n\t\t\t<priceWording><![CDATA[]]></priceWording>\n\t\t\t<stateWording><![CDATA[]]></stateWording>\n\t\t\t<productImageURL><![CDATA[]]></productImageURL>\n\t\t\t<products><![CDATA[]]></products>\n\t\t\t<productsCount><![CDATA[0]]></productsCount>\n\t\t\t<orderType><![CDATA[0]]></orderType>\n\t\t\t<newPriceWording><![CDATA[]]></newPriceWording>\n\t\t\t<newStateWording><![CDATA[]]></newStateWording>\n\t\t\t<useNewWording><![CDATA[0]]></useNewWording>\n\t\t</finderOrder>\n\t\t<finderShopWindowShare>\n\t\t\t<finderUsername><![CDATA[]]></finderUsername>\n\t\t\t<avatar><![CDATA[]]></avatar>\n\t\t\t<nickname><![CDATA[]]></nickname>\n\t\t\t<commodityInStockCount><![CDATA[]]></commodityInStockCount>\n\t\t\t<appId><![CDATA[]]></appId>\n\t\t\t<path><![CDATA[]]></path>\n\t\t\t<appUsername><![CDATA[]]></appUsername>\n\t\t\t<query><![CDATA[]]></query>\n\t\t\t<liteAppId><![CDATA[]]></liteAppId>\n\t\t\t<liteAppPath><![CDATA[]]></liteAppPath>\n\t\t\t<liteAppQuery><![CDATA[]]></liteAppQuery>\n\t\t\t<platformTagURL><![CDATA[]]></platformTagURL>\n\t\t\t<saleWording><![CDATA[]]></saleWording>\n\t\t\t<lastGMsgID><![CDATA[]]></lastGMsgID>\n\t\t\t<profileTypeWording><![CDATA[]]></profileTypeWording>\n\t\t\t<saleWordingExtra><![CDATA[]]></saleWordingExtra>\n\t\t\t<isWxShop><![CDATA[]]></isWxShop>\n\t\t\t<platformIconUrl><![CDATA[]]></platformIconUrl>\n\t\t\t<brandIconUrl><![CDATA[]]></brandIconUrl>\n\t\t\t<description><![CDATA[]]></description>\n\t\t\t<backgroundUrl><![CDATA[]]></backgroundUrl>\n\t\t\t<darkModePlatformIconUrl><![CDATA[]]></darkModePlatformIconUrl>\n\t\t\t<rIconUrl><![CDATA[]]></rIconUrl>\n\t\t\t<rIconUrlDarkMode><![CDATA[]]></rIconUrlDarkMode>\n\t\t\t<rWords><![CDATA[]]></rWords>\n\t\t\t<topShopIconUrl><![CDATA[]]></topShopIconUrl>\n\t\t\t<topShopIconUrlDarkMode><![CDATA[]]></topShopIconUrlDarkMode>\n\t\t\t<simplifyTopShopIconUrl><![CDATA[]]></simplifyTopShopIconUrl>\n\t\t\t<simplifyTopShopIconUrlDarkmode><![CDATA[]]></simplifyTopShopIconUrlDarkmode>\n\t\t\t<topShopIconWidth><![CDATA[0]]></topShopIconWidth>\n\t\t\t<topShopIconHeight><![CDATA[0]]></topShopIconHeight>\n\t\t\t<simplifyTopShopIconWidth><![CDATA[0]]></simplifyTopShopIconWidth>\n\t\t\t<simplifyTopShopIconHeight><![CDATA[0]]></simplifyTopShopIconHeight>\n\t\t\t<reputationInfo>\n\t\t\t\t<hasReputationInfo>0</hasReputationInfo>\n\t\t\t\t<reputationScore>0</reputationScore>\n\t\t\t\t<reputationWording />\n\t\t\t\t<reputationTextColor />\n\t\t\t\t<reputationLevelWording />\n\t\t\t\t<reputationBackgroundColor />\n\t\t\t</reputationInfo>\n\t\t\t<productImageURLList />\n\t\t</finderShopWindowShare>\n\t\t<findernamecard>\n\t\t\t<username />\n\t\t\t<avatar><![CDATA[]]></avatar>\n\t\t\t<nickname />\n\t\t\t<auth_job />\n\t\t\t<auth_icon>0</auth_icon>\n\t\t\t<auth_icon_url />\n\t\t\t<ecSource><![CDATA[]]></ecSource>\n\t\t\t<lastGMsgID><![CDATA[]]></lastGMsgID>\n\t\t</findernamecard>\n\t\t<finderGuarantee>\n\t\t\t<scene><![CDATA[0]]></scene>\n\t\t</finderGuarantee>\n\t\t<directshare>0</directshare>\n\t\t<gamecenter>\n\t\t\t<namecard>\n\t\t\t\t<iconUrl />\n\t\t\t\t<name />\n\t\t\t\t<desc />\n\t\t\t\t<tail />\n\t\t\t\t<jumpUrl />\n\t\t\t\t<liteappId />\n\t\t\t\t<liteappPath />\n\t\t\t\t<liteappQuery />\n\t\t\t\t<liteappMinVersion />\n\t\t\t</namecard>\n\t\t</gamecenter>\n\t\t<patMsg>\n\t\t\t<chatUser />\n\t\t\t<records>\n\t\t\t\t<recordNum>0</recordNum>\n\t\t\t</records>\n\t\t</patMsg>\n\t\t<secretmsg>\n\t\t\t<issecretmsg>0</issecretmsg>\n\t\t</secretmsg>\n\t\t<referfromscene>0</referfromscene>\n\t\t<gameshare>\n\t\t\t<liteappext>\n\t\t\t\t<liteappbizdata />\n\t\t\t\t<priority>0</priority>\n\t\t\t</liteappext>\n\t\t\t<appbrandext>\n\t\t\t\t<litegameinfo />\n\t\t\t\t<priority>-1</priority>\n\t\t\t</appbrandext>\n\t\t\t<gameshareid />\n\t\t\t<sharedata />\n\t\t\t<isvideo>0</isvideo>\n\t\t\t<duration>-1</duration>\n\t\t\t<isexposed>0</isexposed>\n\t\t\t<readtext />\n\t\t</gameshare>\n\t\t<tingChatRoomItem>\n\t\t\t<type>0</type>\n\t\t\t<categoryItem>null</categoryItem>\n\t\t\t<categoryId />\n\t\t\t<listenItem>null</listenItem>\n\t\t</tingChatRoomItem>\n\t\t<mpsharetrace>\n\t\t\t<hasfinderelement>0</hasfinderelement>\n\t\t\t<lastgmsgid />\n\t\t</mpsharetrace>\n\t\t<wxgamecard>\n\t\t\t<framesetname />\n\t\t\t<mbcarddata />\n\t\t\t<minpkgversion />\n\t\t\t<clientextinfo />\n\t\t\t<mbcardheight>0</mbcardheight>\n\t\t\t<isoldversion>0</isoldversion>\n\t\t</wxgamecard>\n\t\t<ecskfcard>\n\t\t\t<framesetname />\n\t\t\t<mbcarddata />\n\t\t\t<minupdateunixtimestamp>0</minupdateunixtimestamp>\n\t\t\t<needheader>false</needheader>\n\t\t\t<summary />\n\t\t</ecskfcard>\n\t\t<liteapp>\n\t\t\t<id>null</id>\n\t\t\t<path />\n\t\t\t<query />\n\t\t\t<istransparent>0</istransparent>\n\t\t\t<hideicon>0</hideicon>\n\t\t\t<forbidforward>0</forbidforward>\n\t\t</liteapp>\n\t\t<opensdk_share_is_modified>0</opensdk_share_is_modified>\n\t</appmsg>\n\t<fromusername>wxid_laurnst5xn0q22</fromusername>\n\t<scene>0</scene>\n\t<appinfo>\n\t\t<version>1</version>\n\t\t<appname></appname>\n\t</appinfo>\n\t<commenturl></commenturl>\n</msg>\n', 'Msgid': '22859552143168783', 'NewMsgId': '22859552143168783', 'ToWxid': 'wxid_4usgcju5ey9q29', 'FromWxid': '***********@chatroom', 'Nickname': '她', 'MsgSource': '<msgsource>\n\t<alnode>\n\t\t<fr>2</fr>\n\t</alnode>\n\t<sec_msg_node>\n\t\t<uuid>c314b5b40c3b20d1115c2f39cf4ef299_</uuid>\n\t\t<risk-file-flag />\n\t\t<risk-file-md5-list />\n\t</sec_msg_node>\n\t<imgmsg_pd cdnmidimgurl_size="24991" cdnmidimgurl_pd_pri="50" cdnmidimgurl_pd="1" />\n\t<silence>1</silence>\n\t<membercount>73</membercount>\n\t<NotAutoDownloadRange>20:00-22:00;00:00-01:00</NotAutoDownloadRange>\n\t<signature>N0_V1_GMkscd8S|v1_urG02yXR</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'Createtime': '1753848679', 'SenderWxid': 'wxid_laurnst5xn0q22'}
2025-07-30 12:12:41 | INFO |   - 引用消息ID: 
2025-07-30 12:12:41 | INFO |   - 引用消息类型: 
2025-07-30 12:12:41 | INFO |   - 引用消息内容: <?xml version="1.0"?>
<msg>
	<appmsg appid="" sdkver="0">
		<title>这个好真啊</title>
		<des />
		<username />
		<action>view</action>
		<type>57</type>
		<showtype>0</showtype>
		<content />
		<url />
		<lowurl />
		<forwardflag>0</forwardflag>
		<dataurl />
		<lowdataurl />
		<contentattr>0</contentattr>
		<streamvideo>
			<streamvideourl />
			<streamvideototaltime>0</streamvideototaltime>
			<streamvideotitle />
			<streamvideowording />
			<streamvideoweburl />
			<streamvideothumburl />
			<streamvideoaduxinfo />
			<streamvideopublishid />
		</streamvideo>
		<canvasPageItem>
			<canvasPageXml><![CDATA[]]></canvasPageXml>
		</canvasPageItem>
		<refermsg>
			<type>3</type>
			<svrid>22859552143168783</svrid>
			<fromusr>***********@chatroom</fromusr>
			<chatusr>wxid_srknxij3jka022</chatusr>
			<displayname>她</displayname>
			<msgsource>&lt;msgsource&gt;
	&lt;alnode&gt;
		&lt;fr&gt;2&lt;/fr&gt;
	&lt;/alnode&gt;
	&lt;sec_msg_node&gt;
		&lt;uuid&gt;c314b5b40c3b20d1115c2f39cf4ef299_&lt;/uuid&gt;
		&lt;risk-file-flag /&gt;
		&lt;risk-file-md5-list /&gt;
	&lt;/sec_msg_node&gt;
	&lt;imgmsg_pd cdnmidimgurl_size="24991" cdnmidimgurl_pd_pri="50" cdnmidimgurl_pd="1" /&gt;
	&lt;silence&gt;1&lt;/silence&gt;
	&lt;membercount&gt;73&lt;/membercount&gt;
	&lt;NotAutoDownloadRange&gt;20:00-22:00;00:00-01:00&lt;/NotAutoDownloadRange&gt;
	&lt;signature&gt;N0_V1_GMkscd8S|v1_urG02yXR&lt;/signature&gt;
	&lt;tmp_node&gt;
		&lt;publisher-id&gt;&lt;/publisher-id&gt;
	&lt;/tmp_node&gt;
&lt;/msgsource&gt;
</msgsource>
			<content>&lt;?xml version="1.0"?&gt;
&lt;msg&gt;
	&lt;img aeskey="c861c2b56672441758555580b272cf7d" encryver="1" cdnthumbaeskey="c861c2b56672441758555580b272cf7d" cdnthumburl="3057020100044b304902010002049c5da34302032e6c63020455ab016f020468899b67042437373433373134312d333036322d346534632d383362342d366536323463663136636336020405292a010201000405004c537700" cdnthumblength="5184" cdnthumbheight="120" cdnthumbwidth="92" cdnmidheight="0" cdnmidwidth="0" cdnhdheight="0" cdnhdwidth="0" cdnmidimgurl="3057020100044b304902010002049c5da34302032e6c63020455ab016f020468899b67042437373433373134312d333036322d346534632d383362342d366536323463663136636336020405292a010201000405004c537700" length="24991" cdnbigimgurl="3057020100044b304902010002049c5da34302032e6c63020455ab016f020468899b67042437373433373134312d333036322d346534632d383362342d366536323463663136636336020405292a010201000405004c537700" hdlength="414288" md5="65c99cc1a75fa59597994817523951fe" hevc_mid_size="24991" originsourcemd5="65c99cc1a75fa59597994817523951fe"&gt;
		&lt;secHashInfoBase64 /&gt;
		&lt;live&gt;
			&lt;duration&gt;0&lt;/duration&gt;
			&lt;size&gt;0&lt;/size&gt;
			&lt;md5 /&gt;
			&lt;fileid /&gt;
			&lt;hdsize&gt;0&lt;/hdsize&gt;
			&lt;hdmd5 /&gt;
			&lt;hdfileid /&gt;
			&lt;stillimagetimems&gt;0&lt;/stillimagetimems&gt;
		&lt;/live&gt;
	&lt;/img&gt;
	&lt;appinfo&gt;
		&lt;appid&gt;wxe3ad19e142df87b3&lt;/appid&gt;
		&lt;version&gt;5&lt;/version&gt;
		&lt;appname&gt;麻豆约拍&lt;/appname&gt;
		&lt;isforceupdate&gt;0&lt;/isforceupdate&gt;
		&lt;messageaction /&gt;
		&lt;messageext /&gt;
		&lt;mediatagname /&gt;
	&lt;/appinfo&gt;
	&lt;platform_signature /&gt;
	&lt;imgdatahash /&gt;
	&lt;ImgSourceInfo&gt;
		&lt;ImgSourceUrl /&gt;
		&lt;BizType&gt;0&lt;/BizType&gt;
	&lt;/ImgSourceInfo&gt;
&lt;/msg&gt;
</content>
			<strid />
			<createtime>1753848679</createtime>
		</refermsg>
		<appattach>
			<totallen>0</totallen>
			<attachid />
			<cdnattachurl />
			<emoticonmd5 />
			<aeskey />
			<fileext />
			<islargefilemsg>0</islargefilemsg>
		</appattach>
		<extinfo />
		<androidsource>0</androidsource>
		<thumburl />
		<mediatagname />
		<messageaction><![CDATA[]]></messageaction>
		<messageext><![CDATA[]]></messageext>
		<emoticongift>
			<packageflag>0</packageflag>
			<packageid />
		</emoticongift>
		<emoticonshared>
			<packageflag>0</packageflag>
			<packageid />
		</emoticonshared>
		<designershared>
			<designeruin>0</designeruin>
			<designername>null</designername>
			<designerrediretcturl><![CDATA[null]]></designerrediretcturl>
		</designershared>
		<emotionpageshared>
			<tid>0</tid>
			<title>null</title>
			<desc>null</desc>
			<iconUrl><![CDATA[null]]></iconUrl>
			<secondUrl>null</secondUrl>
			<pageType>0</pageType>
			<setKey>null</setKey>
		</emotionpageshared>
		<webviewshared>
			<shareUrlOriginal />
			<shareUrlOpen />
			<jsAppId />
			<publisherId />
			<publisherReqId />
		</webviewshared>
		<template_id />
		<md5 />
		<websearch>
			<rec_category>0</rec_category>
			<channelId>0</channelId>
		</websearch>
		<weappinfo>
			<username />
			<appid />
			<appservicetype>0</appservicetype>
			<secflagforsinglepagemode>0</secflagforsinglepagemode>
			<videopageinfo>
				<thumbwidth>0</thumbwidth>
				<thumbheight>0</thumbheight>
				<fromopensdk>0</fromopensdk>
			</videopageinfo>
		</weappinfo>
		<statextstr />
		<musicShareItem>
			<musicDuration>0</musicDuration>
		</musicShareItem>
		<finderLiveProductShare>
			<finderLiveID><![CDATA[]]></finderLiveID>
			<finderUsername><![CDATA[]]></finderUsername>
			<finderObjectID><![CDATA[]]></finderObjectID>
			<finderNonceID><![CDATA[]]></finderNonceID>
			<liveStatus><![CDATA[]]></liveStatus>
			<appId><![CDATA[]]></appId>
			<pagePath><![CDATA[]]></pagePath>
			<productId><![CDATA[]]></productId>
			<coverUrl><![CDATA[]]></coverUrl>
			<productTitle><![CDATA[]]></productTitle>
			<marketPrice><![CDATA[0]]></marketPrice>
			<sellingPrice><![CDATA[0]]></sellingPrice>
			<platformHeadImg><![CDATA[]]></platformHeadImg>
			<platformName><![CDATA[]]></platformName>
			<shopWindowId><![CDATA[]]></shopWindowId>
			<flashSalePrice><![CDATA[0]]></flashSalePrice>
			<flashSaleEndTime><![CDATA[0]]></flashSaleEndTime>
			<ecSource><![CDATA[]]></ecSource>
			<sellingPriceWording><![CDATA[]]></sellingPriceWording>
			<platformIconURL><![CDATA[]]></platformIconURL>
			<firstProductTagURL><![CDATA[]]></firstProductTagURL>
			<firstProductTagAspectRatioString><![CDATA[0.0]]></firstProductTagAspectRatioString>
			<secondProductTagURL><![CDATA[]]></secondProductTagURL>
			<secondProductTagAspectRatioString><![CDATA[0.0]]></secondProductTagAspectRatioString>
			<firstGuaranteeWording><![CDATA[]]></firstGuaranteeWording>
			<secondGuaranteeWording><![CDATA[]]></secondGuaranteeWording>
			<thirdGuaranteeWording><![CDATA[]]></thirdGuaranteeWording>
			<isPriceBeginShow>false</isPriceBeginShow>
			<lastGMsgID><![CDATA[]]></lastGMsgID>
			<promoterKey><![CDATA[]]></promoterKey>
			<discountWording><![CDATA[]]></discountWording>
			<priceSuffixDescription><![CDATA[]]></priceSuffixDescription>
			<productCardKey><![CDATA[]]></productCardKey>
			<isWxShop><![CDATA[]]></isWxShop>
			<brandIconUrl><![CDATA[]]></brandIconUrl>
			<rIconUrl><![CDATA[]]></rIconUrl>
			<rIconUrlDarkMode><![CDATA[]]></rIconUrlDarkMode>
			<topShopIconUrl><![CDATA[]]></topShopIconUrl>
			<topShopIconUrlDarkMode><![CDATA[]]></topShopIconUrlDarkMode>
			<simplifyTopShopIconUrl><![CDATA[]]></simplifyTopShopIconUrl>
			<simplifyTopShopIconUrlDarkmode><![CDATA[]]></simplifyTopShopIconUrlDarkmode>
			<topShopIconWidth><![CDATA[0]]></topShopIconWidth>
			<topShopIconHeight><![CDATA[0]]></topShopIconHeight>
			<simplifyTopShopIconWidth><![CDATA[0]]></simplifyTopShopIconWidth>
			<simplifyTopShopIconHeight><![CDATA[0]]></simplifyTopShopIconHeight>
			<showBoxItemStringList />
		</finderLiveProductShare>
		<finderOrder>
			<appID><![CDATA[]]></appID>
			<orderID><![CDATA[]]></orderID>
			<path><![CDATA[]]></path>
			<priceWording><![CDATA[]]></priceWording>
			<stateWording><![CDATA[]]></stateWording>
			<productImageURL><![CDATA[]]></productImageURL>
			<products><![CDATA[]]></products>
			<productsCount><![CDATA[0]]></productsCount>
			<orderType><![CDATA[0]]></orderType>
			<newPriceWording><![CDATA[]]></newPriceWording>
			<newStateWording><![CDATA[]]></newStateWording>
			<useNewWording><![CDATA[0]]></useNewWording>
		</finderOrder>
		<finderShopWindowShare>
			<finderUsername><![CDATA[]]></finderUsername>
			<avatar><![CDATA[]]></avatar>
			<nickname><![CDATA[]]></nickname>
			<commodityInStockCount><![CDATA[]]></commodityInStockCount>
			<appId><![CDATA[]]></appId>
			<path><![CDATA[]]></path>
			<appUsername><![CDATA[]]></appUsername>
			<query><![CDATA[]]></query>
			<liteAppId><![CDATA[]]></liteAppId>
			<liteAppPath><![CDATA[]]></liteAppPath>
			<liteAppQuery><![CDATA[]]></liteAppQuery>
			<platformTagURL><![CDATA[]]></platformTagURL>
			<saleWording><![CDATA[]]></saleWording>
			<lastGMsgID><![CDATA[]]></lastGMsgID>
			<profileTypeWording><![CDATA[]]></profileTypeWording>
			<saleWordingExtra><![CDATA[]]></saleWordingExtra>
			<isWxShop><![CDATA[]]></isWxShop>
			<platformIconUrl><![CDATA[]]></platformIconUrl>
			<brandIconUrl><![CDATA[]]></brandIconUrl>
			<description><![CDATA[]]></description>
			<backgroundUrl><![CDATA[]]></backgroundUrl>
			<darkModePlatformIconUrl><![CDATA[]]></darkModePlatformIconUrl>
			<rIconUrl><![CDATA[]]></rIconUrl>
			<rIconUrlDarkMode><![CDATA[]]></rIconUrlDarkMode>
			<rWords><![CDATA[]]></rWords>
			<topShopIconUrl><![CDATA[]]></topShopIconUrl>
			<topShopIconUrlDarkMode><![CDATA[]]></topShopIconUrlDarkMode>
			<simplifyTopShopIconUrl><![CDATA[]]></simplifyTopShopIconUrl>
			<simplifyTopShopIconUrlDarkmode><![CDATA[]]></simplifyTopShopIconUrlDarkmode>
			<topShopIconWidth><![CDATA[0]]></topShopIconWidth>
			<topShopIconHeight><![CDATA[0]]></topShopIconHeight>
			<simplifyTopShopIconWidth><![CDATA[0]]></simplifyTopShopIconWidth>
			<simplifyTopShopIconHeight><![CDATA[0]]></simplifyTopShopIconHeight>
			<reputationInfo>
				<hasReputationInfo>0</hasReputationInfo>
				<reputationScore>0</reputationScore>
				<reputationWording />
				<reputationTextColor />
				<reputationLevelWording />
				<reputationBackgroundColor />
			</reputationInfo>
			<productImageURLList />
		</finderShopWindowShare>
		<findernamecard>
			<username />
			<avatar><![CDATA[]]></avatar>
			<nickname />
			<auth_job />
			<auth_icon>0</auth_icon>
			<auth_icon_url />
			<ecSource><![CDATA[]]></ecSource>
			<lastGMsgID><![CDATA[]]></lastGMsgID>
		</findernamecard>
		<finderGuarantee>
			<scene><![CDATA[0]]></scene>
		</finderGuarantee>
		<directshare>0</directshare>
		<gamecenter>
			<namecard>
				<iconUrl />
				<name />
				<desc />
				<tail />
				<jumpUrl />
				<liteappId />
				<liteappPath />
				<liteappQuery />
				<liteappMinVersion />
			</namecard>
		</gamecenter>
		<patMsg>
			<chatUser />
			<records>
				<recordNum>0</recordNum>
			</records>
		</patMsg>
		<secretmsg>
			<issecretmsg>0</issecretmsg>
		</secretmsg>
		<referfromscene>0</referfromscene>
		<gameshare>
			<liteappext>
				<liteappbizdata />
				<priority>0</priority>
			</liteappext>
			<appbrandext>
				<litegameinfo />
				<priority>-1</priority>
			</appbrandext>
			<gameshareid />
			<sharedata />
			<isvideo>0</isvideo>
			<duration>-1</duration>
			<isexposed>0</isexposed>
			<readtext />
		</gameshare>
		<tingChatRoomItem>
			<type>0</type>
			<categoryItem>null</categoryItem>
			<categoryId />
			<listenItem>null</listenItem>
		</tingChatRoomItem>
		<mpsharetrace>
			<hasfinderelement>0</hasfinderelement>
			<lastgmsgid />
		</mpsharetrace>
		<wxgamecard>
			<framesetname />
			<mbcarddata />
			<minpkgversion />
			<clientextinfo />
			<mbcardheight>0</mbcardheight>
			<isoldversion>0</isoldversion>
		</wxgamecard>
		<ecskfcard>
			<framesetname />
			<mbcarddata />
			<minupdateunixtimestamp>0</minupdateunixtimestamp>
			<needheader>false</needheader>
			<summary />
		</ecskfcard>
		<liteapp>
			<id>null</id>
			<path />
			<query />
			<istransparent>0</istransparent>
			<hideicon>0</hideicon>
			<forbidforward>0</forbidforward>
		</liteapp>
		<opensdk_share_is_modified>0</opensdk_share_is_modified>
	</appmsg>
	<fromusername>wxid_laurnst5xn0q22</fromusername>
	<scene>0</scene>
	<appinfo>
		<version>1</version>
		<appname></appname>
	</appinfo>
	<commenturl></commenturl>
</msg>

2025-07-30 12:12:41 | INFO |   - 引用消息发送人: wxid_laurnst5xn0q22
2025-07-30 12:13:15 | DEBUG | 收到消息: {'MsgId': 894292748, 'FromUserName': {'string': '***********@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 43, 'Content': {'string': 'xiaomaochong:\n<?xml version="1.0"?>\n<msg>\n\t<videomsg aeskey="7c45f4ad3f46af9ac2ee1e9571ca0c54" cdnvideourl="3057020100044b304902010002049363814102032f77f502048e5b90db020468899bc2042435616531363665622d643333322d343137312d393537382d3135656134613335306335350204052408040201000405004c57c100" cdnthumbaeskey="7c45f4ad3f46af9ac2ee1e9571ca0c54" cdnthumburl="3057020100044b304902010002049363814102032f77f502048e5b90db020468899bc2042435616531363665622d643333322d343137312d393537382d3135656134613335306335350204052408040201000405004c57c100" length="86685624" playlength="29" cdnthumblength="8083" cdnthumbwidth="720" cdnthumbheight="405" fromusername="xiaomaochong" md5="b6e88983bf7afe1f622e27761c87f6f5" newmd5="1c8b10a2924bbf32fc280fe2661d345c" isplaceholder="0" rawmd5="b4ce26241a4becd9ad727e157453d1ac" rawlength="186257086" cdnrawvideourl="3056020100044a304802010002035a663f02032f77f50204985b90db020468899bce042436616135346439352d316466382d343833622d386366652d3135646463376466623165320204059400040201000405004c50b900" cdnrawvideoaeskey="672d04ab307678833189d939fa95468d" overwritenewmsgid="0" originsourcemd5="e5f7b7a09244355de7e888bd9a53236f" isad="0" />\n</msg>\n'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1753848807, 'MsgSource': '<msgsource>\n\t<sec_msg_node>\n\t\t<uuid>707d3335ee8d1f10ca212a5e118b4cba_</uuid>\n\t\t<risk-file-flag />\n\t\t<risk-file-md5-list />\n\t</sec_msg_node>\n\t<silence>0</silence>\n\t<membercount>73</membercount>\n\t<signature>N0_V1_skZ4+7rd|v1_ttk734z6</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'PushContent': '小爱在群聊中发了一段视频', 'NewMsgId': 5798914027523795341, 'MsgSeq': 871411561}
2025-07-30 12:13:15 | INFO | 收到视频消息: 消息ID:894292748 来自:***********@chatroom 发送人:xiaomaochong XML:
<?xml version="1.0"?>
<msg>
	<videomsg aeskey="7c45f4ad3f46af9ac2ee1e9571ca0c54" cdnvideourl="3057020100044b304902010002049363814102032f77f502048e5b90db020468899bc2042435616531363665622d643333322d343137312d393537382d3135656134613335306335350204052408040201000405004c57c100" cdnthumbaeskey="7c45f4ad3f46af9ac2ee1e9571ca0c54" cdnthumburl="3057020100044b304902010002049363814102032f77f502048e5b90db020468899bc2042435616531363665622d643333322d343137312d393537382d3135656134613335306335350204052408040201000405004c57c100" length="86685624" playlength="29" cdnthumblength="8083" cdnthumbwidth="720" cdnthumbheight="405" fromusername="xiaomaochong" md5="b6e88983bf7afe1f622e27761c87f6f5" newmd5="1c8b10a2924bbf32fc280fe2661d345c" isplaceholder="0" rawmd5="b4ce26241a4becd9ad727e157453d1ac" rawlength="186257086" cdnrawvideourl="3056020100044a304802010002035a663f02032f77f50204985b90db020468899bce042436616135346439352d316466382d343833622d386366652d3135646463376466623165320204059400040201000405004c50b900" cdnrawvideoaeskey="672d04ab307678833189d939fa95468d" overwritenewmsgid="0" originsourcemd5="e5f7b7a09244355de7e888bd9a53236f" isad="0" />
</msg>

2025-07-30 12:15:21 | DEBUG | 收到消息: {'MsgId': 1945388443, 'FromUserName': {'string': '***********@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 43, 'Content': {'string': 'wxid_srknxij3jka022:\n<?xml version="1.0"?>\n<msg>\n\t<videomsg aeskey="9aa590fda57a3a29033549431012418a" cdnvideourl="3057020100044b304902010002049c5da34302034c55070204d183ad27020468899c4f042463393635643364372d663766352d343530652d613966372d3134373261363662366333370204052408040201000405004c550700" cdnthumbaeskey="9aa590fda57a3a29033549431012418a" cdnthumburl="3057020100044b304902010002049c5da34302034c55070204d183ad27020468899c4f042463393635643364372d663766352d343530652d613966372d3134373261363662366333370204052408040201000405004c550700" length="124580905" playlength="642" cdnthumblength="8107" cdnthumbwidth="1920" cdnthumbheight="1080" fromusername="wxid_srknxij3jka022" md5="aa7cd4f326ae8669d35d1c32aace1d95" newmd5="bcc2004b2c24f2385a3d6ccfd4b15d23" isplaceholder="0" rawmd5="a0094529aab6b823379c7734f6049e8c" rawlength="20528229" cdnrawvideourl="3057020100044b30490201000204f33b8a7102032e6c63020433ab016f020468899c61042436306666626338652d353839642d343833362d386665332d3062396162316665326433310204059800040201000405004c505700" cdnrawvideoaeskey="3d0231e5bfa6b85a2ebc1e596293a9f5" overwritenewmsgid="0" originsourcemd5="15a2e7f6a508ec56119f168c85159b2a" isad="0" />\n</msg>\n'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1753848933, 'MsgSource': '<msgsource>\n\t<sec_msg_node>\n\t\t<uuid>9594546ccbddd6e2826a5d44edfba7ef_</uuid>\n\t\t<risk-file-flag />\n\t\t<risk-file-md5-list />\n\t</sec_msg_node>\n\t<silence>0</silence>\n\t<membercount>73</membercount>\n\t<signature>N0_V1_hXBoB4kQ|v1_6R0o+Yd7</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'PushContent': '她在群聊中发了一段视频', 'NewMsgId': 6175052756569132904, 'MsgSeq': 871411562}
2025-07-30 12:15:21 | INFO | 收到视频消息: 消息ID:1945388443 来自:***********@chatroom 发送人:wxid_srknxij3jka022 XML:
<?xml version="1.0"?>
<msg>
	<videomsg aeskey="9aa590fda57a3a29033549431012418a" cdnvideourl="3057020100044b304902010002049c5da34302034c55070204d183ad27020468899c4f042463393635643364372d663766352d343530652d613966372d3134373261363662366333370204052408040201000405004c550700" cdnthumbaeskey="9aa590fda57a3a29033549431012418a" cdnthumburl="3057020100044b304902010002049c5da34302034c55070204d183ad27020468899c4f042463393635643364372d663766352d343530652d613966372d3134373261363662366333370204052408040201000405004c550700" length="124580905" playlength="642" cdnthumblength="8107" cdnthumbwidth="1920" cdnthumbheight="1080" fromusername="wxid_srknxij3jka022" md5="aa7cd4f326ae8669d35d1c32aace1d95" newmd5="bcc2004b2c24f2385a3d6ccfd4b15d23" isplaceholder="0" rawmd5="a0094529aab6b823379c7734f6049e8c" rawlength="20528229" cdnrawvideourl="3057020100044b30490201000204f33b8a7102032e6c63020433ab016f020468899c61042436306666626338652d353839642d343833362d386665332d3062396162316665326433310204059800040201000405004c505700" cdnrawvideoaeskey="3d0231e5bfa6b85a2ebc1e596293a9f5" overwritenewmsgid="0" originsourcemd5="15a2e7f6a508ec56119f168c85159b2a" isad="0" />
</msg>

2025-07-30 12:18:01 | DEBUG | 收到消息: {'MsgId': 1493110373, 'FromUserName': {'string': '***********@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 49, 'Content': {'string': 'wxid_jegyk4i3v7zg22:\n<?xml version="1.0"?>\n<msg>\n\t<appmsg appid="" sdkver="0">\n\t\t<title>那必须的</title>\n\t\t<type>57</type>\n\t\t<appattach>\n\t\t\t<cdnthumbaeskey />\n\t\t\t<aeskey></aeskey>\n\t\t</appattach>\n\t\t<refermsg>\n\t\t\t<type>49</type>\n\t\t\t<svrid>8582723612119003088</svrid>\n\t\t\t<fromusr>***********@chatroom</fromusr>\n\t\t\t<chatusr>wxid_laurnst5xn0q22</chatusr>\n\t\t\t<displayname>متسول暗</displayname>\n\t\t\t<content>&lt;msg&gt;&lt;appmsg appid=""  sdkver="0"&gt;&lt;title&gt;这个好真啊&lt;/title&gt;&lt;type&gt;57&lt;/type&gt;&lt;action&gt;view&lt;/action&gt;&lt;appattach&gt;&lt;cdnthumbaeskey&gt;&lt;/cdnthumbaeskey&gt;&lt;aeskey&gt;&lt;/aeskey&gt;&lt;/appattach&gt;&lt;webviewshared&gt;&lt;jsAppId&gt;&lt;![CDATA[]]&gt;&lt;/jsAppId&gt;&lt;/webviewshared&gt;&lt;mpsharetrace&gt;&lt;hasfinderelement&gt;0&lt;/hasfinderelement&gt;&lt;/mpsharetrace&gt;&lt;secretmsg&gt;&lt;isscrectmsg&gt;0&lt;/isscrectmsg&gt;&lt;/secretmsg&gt;&lt;/appmsg&gt;&lt;fromusername&gt;wxid_laurnst5xn0q22&lt;/fromusername&gt;&lt;appinfo&gt;&lt;version&gt;1&lt;/version&gt;&lt;appname&gt;&lt;/appname&gt;&lt;isforceupdate&gt;1&lt;/isforceupdate&gt;&lt;/appinfo&gt;&lt;extcommoninfo&gt;&lt;/extcommoninfo&gt;&lt;/msg&gt;</content>\n\t\t\t<msgsource>&lt;msgsource&gt;&lt;sequence_id&gt;836242872&lt;/sequence_id&gt;\n\t&lt;alnode&gt;\n\t\t&lt;fr&gt;4&lt;/fr&gt;\n\t&lt;/alnode&gt;\n\t&lt;eggIncluded&gt;1&lt;/eggIncluded&gt;\n\t&lt;sec_msg_node&gt;\n\t\t&lt;uuid&gt;c314b5b40c3b20d1115c2f39cf4ef299_&lt;/uuid&gt;\n\t\t&lt;risk-file-flag /&gt;\n\t\t&lt;risk-file-md5-list /&gt;\n\t&lt;/sec_msg_node&gt;\n\t&lt;silence&gt;1&lt;/silence&gt;\n\t&lt;membercount&gt;73&lt;/membercount&gt;\n\t&lt;signature&gt;N0_V1_uUB9osWI|v1_h3sAGVpC&lt;/signature&gt;\n\t&lt;tmp_node&gt;\n\t\t&lt;publisher-id&gt;&lt;/publisher-id&gt;\n\t&lt;/tmp_node&gt;\n&lt;/msgsource&gt;\n</msgsource>\n\t\t\t<createtime>1753848772</createtime>\n\t\t</refermsg>\n\t</appmsg>\n\t<fromusername>wxid_jegyk4i3v7zg22</fromusername>\n\t<scene>0</scene>\n\t<appinfo>\n\t\t<version>1</version>\n\t\t<appname />\n\t</appinfo>\n\t<commenturl />\n</msg>\n'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1753849093, 'MsgSource': '<msgsource>\n\t<bizflag>0</bizflag>\n\t<pua>1</pua>\n\t<eggIncluded>1</eggIncluded>\n\t<sec_msg_node>\n\t\t<uuid>b1c4444c7ec08987ffcec84f2a4a962d_</uuid>\n\t\t<risk-file-flag />\n\t\t<risk-file-md5-list />\n\t</sec_msg_node>\n\t<silence>0</silence>\n\t<membercount>73</membercount>\n\t<signature>N0_V1_ueTzBQ3p|v1_n4G69hMk</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'PushContent': '阿尼亚与她 : 那必须的', 'NewMsgId': 7144052906794907322, 'MsgSeq': 871411563}
2025-07-30 12:18:01 | DEBUG | 从群聊消息中提取发送者: wxid_jegyk4i3v7zg22
2025-07-30 12:18:01 | DEBUG | 使用已解析的XML处理引用消息
2025-07-30 12:18:01 | INFO | 收到引用消息: 消息ID:1493110373 来自:***********@chatroom 发送人:wxid_jegyk4i3v7zg22 内容:那必须的 引用类型:49
2025-07-30 12:18:02 | INFO | [DouBaoImageToImage] 收到引用消息: 那必须的
2025-07-30 12:18:02 | INFO | [TimerTask] 收到引用消息调试信息:
2025-07-30 12:18:02 | INFO |   - 消息内容: 那必须的
2025-07-30 12:18:02 | INFO |   - 群组ID: ***********@chatroom
2025-07-30 12:18:02 | INFO |   - 发送人: wxid_jegyk4i3v7zg22
2025-07-30 12:18:02 | INFO |   - 引用信息: {'MsgType': 49, 'Content': '<msg><appmsg appid=""  sdkver="0"><title>这个好真啊</title><type>57</type><action>view</action><appattach><cdnthumbaeskey></cdnthumbaeskey><aeskey></aeskey></appattach><webviewshared><jsAppId><![CDATA[]]></jsAppId></webviewshared><mpsharetrace><hasfinderelement>0</hasfinderelement></mpsharetrace><secretmsg><isscrectmsg>0</isscrectmsg></secretmsg></appmsg><fromusername>wxid_laurnst5xn0q22</fromusername><appinfo><version>1</version><appname></appname><isforceupdate>1</isforceupdate></appinfo><extcommoninfo></extcommoninfo></msg>', 'Msgid': '8582723612119003088', 'NewMsgId': '8582723612119003088', 'ToWxid': 'wxid_4usgcju5ey9q29', 'FromWxid': '***********@chatroom', 'Nickname': 'متسول暗', 'MsgSource': '<msgsource><sequence_id>836242872</sequence_id>\n\t<alnode>\n\t\t<fr>4</fr>\n\t</alnode>\n\t<eggIncluded>1</eggIncluded>\n\t<sec_msg_node>\n\t\t<uuid>c314b5b40c3b20d1115c2f39cf4ef299_</uuid>\n\t\t<risk-file-flag />\n\t\t<risk-file-md5-list />\n\t</sec_msg_node>\n\t<silence>1</silence>\n\t<membercount>73</membercount>\n\t<signature>N0_V1_uUB9osWI|v1_h3sAGVpC</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'Createtime': '1753848772', 'SenderWxid': 'wxid_jegyk4i3v7zg22'}
2025-07-30 12:18:02 | INFO |   - 引用消息ID: 
2025-07-30 12:18:02 | INFO |   - 引用消息类型: 
2025-07-30 12:18:02 | INFO |   - 引用消息内容: <msg><appmsg appid=""  sdkver="0"><title>这个好真啊</title><type>57</type><action>view</action><appattach><cdnthumbaeskey></cdnthumbaeskey><aeskey></aeskey></appattach><webviewshared><jsAppId><![CDATA[]]></jsAppId></webviewshared><mpsharetrace><hasfinderelement>0</hasfinderelement></mpsharetrace><secretmsg><isscrectmsg>0</isscrectmsg></secretmsg></appmsg><fromusername>wxid_laurnst5xn0q22</fromusername><appinfo><version>1</version><appname></appname><isforceupdate>1</isforceupdate></appinfo><extcommoninfo></extcommoninfo></msg>
2025-07-30 12:18:02 | INFO |   - 引用消息发送人: wxid_jegyk4i3v7zg22
2025-07-30 12:18:07 | DEBUG | 收到消息: {'MsgId': 31131204, 'FromUserName': {'string': '***********@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 1, 'Content': {'string': 'wxid_jegyk4i3v7zg22:\n有一个提示词我忘记了'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1753849100, 'MsgSource': '<msgsource>\n\t<bizflag>0</bizflag>\n\t<pua>1</pua>\n\t<eggIncluded>1</eggIncluded>\n\t<silence>0</silence>\n\t<membercount>73</membercount>\n\t<signature>N0_V1_CDTz1Muv|v1_tW2Fmp4o</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'PushContent': '阿尼亚与她 : 有一个提示词我忘记了', 'NewMsgId': 2466002255518060232, 'MsgSeq': 871411564}
2025-07-30 12:18:07 | INFO | 收到文本消息: 消息ID:31131204 来自:***********@chatroom 发送人:wxid_jegyk4i3v7zg22 @:[] 内容:有一个提示词我忘记了
2025-07-30 12:18:08 | DEBUG | 处理消息内容: '有一个提示词我忘记了'
2025-07-30 12:18:08 | DEBUG | 消息内容 '有一个提示词我忘记了' 不匹配任何命令，忽略
2025-07-30 12:18:13 | DEBUG | 收到消息: {'MsgId': 1670619276, 'FromUserName': {'string': '***********@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 1, 'Content': {'string': 'wxid_jegyk4i3v7zg22:\n花的超级无敌好看'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1753849106, 'MsgSource': '<msgsource>\n\t<bizflag>0</bizflag>\n\t<pua>1</pua>\n\t<eggIncluded>1</eggIncluded>\n\t<silence>0</silence>\n\t<membercount>73</membercount>\n\t<signature>N0_V1_KUUpykl2|v1_ThrC+KRa</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'PushContent': '阿尼亚与她 : 花的超级无敌好看', 'NewMsgId': 2082428683678364070, 'MsgSeq': 871411565}
2025-07-30 12:18:13 | INFO | 收到文本消息: 消息ID:1670619276 来自:***********@chatroom 发送人:wxid_jegyk4i3v7zg22 @:[] 内容:花的超级无敌好看
2025-07-30 12:18:14 | DEBUG | 处理消息内容: '花的超级无敌好看'
2025-07-30 12:18:14 | DEBUG | 消息内容 '花的超级无敌好看' 不匹配任何命令，忽略
2025-07-30 12:18:19 | DEBUG | 收到消息: {'MsgId': 2091238400, 'FromUserName': {'string': '***********@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 1, 'Content': {'string': 'wxid_jegyk4i3v7zg22:\n画'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1753849112, 'MsgSource': '<msgsource>\n\t<bizflag>0</bizflag>\n\t<pua>1</pua>\n\t<eggIncluded>1</eggIncluded>\n\t<silence>0</silence>\n\t<membercount>73</membercount>\n\t<signature>N0_V1_JtJFna+j|v1_KvcJiAP+</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'PushContent': '阿尼亚与她 : 画', 'NewMsgId': 5607777672079232124, 'MsgSeq': 871411566}
2025-07-30 12:18:19 | INFO | 收到文本消息: 消息ID:2091238400 来自:***********@chatroom 发送人:wxid_jegyk4i3v7zg22 @:[] 内容:画
2025-07-30 12:18:20 | DEBUG | 处理消息内容: '画'
2025-07-30 12:18:20 | DEBUG | 消息内容 '画' 不匹配任何命令，忽略
2025-07-30 12:18:32 | DEBUG | 收到消息: {'MsgId': 407328407, 'FromUserName': {'string': '***********@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 1, 'Content': {'string': 'wxid_jegyk4i3v7zg22:\n让ai给我生成完整的提示词'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1753849124, 'MsgSource': '<msgsource>\n\t<bizflag>0</bizflag>\n\t<pua>1</pua>\n\t<eggIncluded>1</eggIncluded>\n\t<silence>0</silence>\n\t<membercount>73</membercount>\n\t<signature>N0_V1_TzGikb7b|v1_1hk/d3PJ</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'PushContent': '阿尼亚与她 : 让ai给我生成完整的提示词', 'NewMsgId': 2075983832809255982, 'MsgSeq': 871411567}
2025-07-30 12:18:32 | INFO | 收到文本消息: 消息ID:407328407 来自:***********@chatroom 发送人:wxid_jegyk4i3v7zg22 @:[] 内容:让ai给我生成完整的提示词
2025-07-30 12:18:32 | DEBUG | 处理消息内容: '让ai给我生成完整的提示词'
2025-07-30 12:18:32 | DEBUG | 消息内容 '让ai给我生成完整的提示词' 不匹配任何命令，忽略
2025-07-30 12:19:30 | DEBUG | 收到消息: {'MsgId': 2125358381, 'FromUserName': {'string': '***********@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 1, 'Content': {'string': 'xiaomaochong:\n画一次多少钱，一会欠费了[抠鼻][皱眉]'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1753849182, 'MsgSource': '<msgsource>\n\t<pua>1</pua>\n\t<silence>0</silence>\n\t<membercount>73</membercount>\n\t<signature>N0_V1_JeWxDtjZ|v1_mAGToFZp</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'PushContent': '小爱 : 画一次多少钱，一会欠费了[抠鼻][皱眉]', 'NewMsgId': 630953519351360670, 'MsgSeq': 871411568}
2025-07-30 12:19:30 | INFO | 收到文本消息: 消息ID:2125358381 来自:***********@chatroom 发送人:xiaomaochong @:[] 内容:画一次多少钱，一会欠费了[抠鼻][皱眉]
2025-07-30 12:19:30 | DEBUG | 处理消息内容: '画一次多少钱，一会欠费了[抠鼻][皱眉]'
2025-07-30 12:19:30 | DEBUG | 消息内容 '画一次多少钱，一会欠费了[抠鼻][皱眉]' 不匹配任何命令，忽略
2025-07-30 12:19:41 | DEBUG | 收到消息: {'MsgId': 1462088416, 'FromUserName': {'string': '***********@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 49, 'Content': {'string': 'wxid_e3o8s2nf9u2o22:\n<?xml version="1.0"?>\n<msg>\n\t<appmsg appid="" sdkver="0">\n\t\t<title>当前版本不支持展示该内容，请升级至最新版本。</title>\n\t\t<des />\n\t\t<username />\n\t\t<action>view</action>\n\t\t<type>51</type>\n\t\t<showtype>0</showtype>\n\t\t<content />\n\t\t<url>https://support.weixin.qq.com/security/readtemplate?t=w_security_center_website/upgrade</url>\n\t\t<lowurl />\n\t\t<forwardflag>0</forwardflag>\n\t\t<dataurl />\n\t\t<lowdataurl />\n\t\t<contentattr>0</contentattr>\n\t\t<streamvideo>\n\t\t\t<streamvideourl />\n\t\t\t<streamvideototaltime>0</streamvideototaltime>\n\t\t\t<streamvideotitle />\n\t\t\t<streamvideowording />\n\t\t\t<streamvideoweburl />\n\t\t\t<streamvideothumburl />\n\t\t\t<streamvideoaduxinfo />\n\t\t\t<streamvideopublishid />\n\t\t</streamvideo>\n\t\t<canvasPageItem>\n\t\t\t<canvasPageXml><![CDATA[]]></canvasPageXml>\n\t\t</canvasPageItem>\n\t\t<appattach>\n\t\t\t<totallen>0</totallen>\n\t\t\t<attachid />\n\t\t\t<cdnattachurl />\n\t\t\t<emoticonmd5></emoticonmd5>\n\t\t\t<aeskey></aeskey>\n\t\t\t<fileext />\n\t\t\t<islargefilemsg>0</islargefilemsg>\n\t\t</appattach>\n\t\t<extinfo />\n\t\t<androidsource>0</androidsource>\n\t\t<thumburl />\n\t\t<mediatagname />\n\t\t<messageaction><![CDATA[]]></messageaction>\n\t\t<messageext><![CDATA[]]></messageext>\n\t\t<emoticongift>\n\t\t\t<packageflag>0</packageflag>\n\t\t\t<packageid />\n\t\t</emoticongift>\n\t\t<emoticonshared>\n\t\t\t<packageflag>0</packageflag>\n\t\t\t<packageid />\n\t\t</emoticonshared>\n\t\t<designershared>\n\t\t\t<designeruin>0</designeruin>\n\t\t\t<designername>null</designername>\n\t\t\t<designerrediretcturl><![CDATA[null]]></designerrediretcturl>\n\t\t</designershared>\n\t\t<emotionpageshared>\n\t\t\t<tid>0</tid>\n\t\t\t<title>null</title>\n\t\t\t<desc>null</desc>\n\t\t\t<iconUrl><![CDATA[null]]></iconUrl>\n\t\t\t<secondUrl>null</secondUrl>\n\t\t\t<pageType>0</pageType>\n\t\t\t<setKey>null</setKey>\n\t\t</emotionpageshared>\n\t\t<webviewshared>\n\t\t\t<shareUrlOriginal />\n\t\t\t<shareUrlOpen />\n\t\t\t<jsAppId />\n\t\t\t<publisherId />\n\t\t\t<publisherReqId />\n\t\t</webviewshared>\n\t\t<template_id />\n\t\t<md5 />\n\t\t<websearch>\n\t\t\t<rec_category>0</rec_category>\n\t\t\t<channelId>0</channelId>\n\t\t</websearch>\n\t\t<weappinfo>\n\t\t\t<username />\n\t\t\t<appid />\n\t\t\t<appservicetype>0</appservicetype>\n\t\t\t<secflagforsinglepagemode>0</secflagforsinglepagemode>\n\t\t\t<videopageinfo>\n\t\t\t\t<thumbwidth>0</thumbwidth>\n\t\t\t\t<thumbheight>0</thumbheight>\n\t\t\t\t<fromopensdk>0</fromopensdk>\n\t\t\t</videopageinfo>\n\t\t</weappinfo>\n\t\t<statextstr />\n\t\t<musicShareItem>\n\t\t\t<musicDuration>0</musicDuration>\n\t\t</musicShareItem>\n\t\t<finderFeed>\n\t\t\t<objectId>14711244296557234705</objectId>\n\t\t\t<objectNonceId>15052248329069960540_4_20_13_1_1753848986694891_f5f03dae-6cfb-11f0-9a25-0578289e0d04</objectNonceId>\n\t\t\t<feedType>4</feedType>\n\t\t\t<nickname>绝世蘑菇owo</nickname>\n\t\t\t<username>v2_060000231003b20faec8cae28f10c6d0ce05e430b0775dbe16e30432f03cb941cbc374d4e51d@finder</username>\n\t\t\t<avatar><![CDATA[https://wx.qlogo.cn/finderhead/ver_1/4HpWYdmYGeyBMp1DcokxEZicIGykrx9QNLDgEmNgPQbnhPhicHC22PNMeqR0CmdD9KFyoOsdwfyhm9ZCpk9nMD7p47icliazlBoOKKlHbic5qk3gwIJTRJ62XQ9ZzyYWicPRKk/0]]></avatar>\n\t\t\t<desc>这期是我定制的大卡抱小卡#鸣潮 #鸣潮cos #cosplay #cos #卡提希娅 #芙露德莉斯 #卡提希娅cos</desc>\n\t\t\t<mediaCount>1</mediaCount>\n\t\t\t<localId>0</localId>\n\t\t\t<authIconType>1</authIconType>\n\t\t\t<authIconUrl><![CDATA[https://dldir1v6.qq.com/weixin/checkresupdate/auth_icon_level3_2e2f94615c1e4651a25a7e0446f63135.png]]></authIconUrl>\n\t\t\t<mediaList>\n\t\t\t\t<media>\n\t\t\t\t\t<mediaType>4</mediaType>\n\t\t\t\t\t<url><![CDATA[http://wxapp.tc.qq.com/251/20302/stodownload?encfilekey=Cvvj5Ix3eewK0tHtibORqcsqchXNh0Gf3sJcaYqC2rQDj1s6ria5x8rR40Z8Fia534nIbQbmibp0tI8efibkfAYaNxIQEr7GaohAL35h9KGy1jTHicQvjT2za6kZbco962nEPn&bizid=1023&dotrans=0&hy=SH&idx=1&m=&uzid=7a1ac&token=cztXnd9GyrFcZibINAPxDLkxHCmunBtcCEjxuKkNvGS1Q2LDNkuZLDWiaT17iarJK2qiavibWDia532NKZeicxicibXiaibd7icrwU2b8268USTV0Bd1ibI3IctGDoqbmkIwuibic5UrQEicchia4tLRRAVLeibyGpdbHLMTibsEkj86W0q7IDPIjTTSMU&basedata=CAESBnhXVDEyNhoGeFdUMTExGgZ4V1QxMjYaBnhXVDExMhoGeFdUMTI3GgZ4V1QxMTMaBnhXVDEyOCIMCgoKBnhXVDExMhABKgcIvR4QABgC&sign=1dtuUe4tNOCLa0cRdvj4Ud5L_M2l2rUwygDk9K2SXZaFBmiknDMASGUcHTnyNJH-wyh6TaOfce9Jt0ZUa1qFFQ&ctsc=20&extg=108b900&svrbypass=AAuL%2FQsFAAABAAAAAAB1ulkPbE32atZ4nZyJaBAAAADnaHZTnGbFfAj9RgZXfw6VKJ2UBC0PwnHXTAmIv%2BjE8nmIEpZ9bmAd6hrwXLJpMoOlQAcJYs8YaQI%3D&svrnonce=1753848989]]></url>\n\t\t\t\t\t<thumbUrl><![CDATA[http://wxapp.tc.qq.com/251/20304/stodownload?encfilekey=rjD5jyTuFrIpZ2ibE8T7Ym3K77SEULgkiazkSEdLLdJj6uib44Qy0MP6IzLMNGygicJKAVxBB4p3x6EoQZic3sVibQ7xPzSSO7jlCESbLBUFXkRM6wqScpg6aJ9g&hy=SZ&idx=1&m=&scene=2&uzid=1&picformat=200&wxampicformat=503&token=6xykWLEnztKIzBicPuvgFxq2fFv0jetq7kDQNTiaw40ibBBpqibC8SgB1LCXszZhEWgqVODl6cedjctpZ2hIQEhmE6VQicJ1S6yYu3MYc8xRJJTKtO1Yv1kDMz69le5vb73OhTLsV8ibyj7TmkLSaEYVZJOFWDP5HWWHIB&ctsc=2-20]]></thumbUrl>\n\t\t\t\t\t<coverUrl><![CDATA[http://wxapp.tc.qq.com/251/20304/stodownload?encfilekey=rjD5jyTuFrIpZ2ibE8T7Ym3K77SEULgkiazkSEdLLdJj6uib44Qy0MP6IzLMNGygicJKAVxBB4p3x6EoQZic3sVibQ7xPzSSO7jlCESbLBUFXkRM6wqScpg6aJ9g&hy=SZ&idx=1&m=&scene=2&uzid=1&picformat=200&wxampicformat=503&token=6xykWLEnztKIzBicPuvgFxq2fFv0jetq7kDQNTiaw40ibBBpqibC8SgB1LCXszZhEWgqVODl6cedjctpZ2hIQEhmE6VQicJ1S6yYu3MYc8xRJJTKtO1Yv1kDMz69le5vb73OhTLsV8ibyj7TmkLSaEYVZJOFWDP5HWWHIB&ctsc=2-20]]></coverUrl>\n\t\t\t\t\t<fullCoverUrl><![CDATA[]]></fullCoverUrl>\n\t\t\t\t\t<fullClipInset><![CDATA[]]></fullClipInset>\n\t\t\t\t\t<width>1080.0</width>\n\t\t\t\t\t<height>1920.0</height>\n\t\t\t\t\t<videoPlayDuration>8</videoPlayDuration>\n\t\t\t\t</media>\n\t\t\t</mediaList>\n\t\t\t<megaVideo>\n\t\t\t\t<objectId />\n\t\t\t\t<objectNonceId />\n\t\t\t</megaVideo>\n\t\t\t<bizUsername />\n\t\t\t<bizNickname />\n\t\t\t<bizAvatar><![CDATA[]]></bizAvatar>\n\t\t\t<bizUsernameV2 />\n\t\t\t<bizAuthIconType>0</bizAuthIconType>\n\t\t\t<bizAuthIconUrl><![CDATA[]]></bizAuthIconUrl>\n\t\t\t<coverEffectType>0</coverEffectType>\n\t\t\t<coverEffectText><![CDATA[]]></coverEffectText>\n\t\t\t<finderForwardSource><![CDATA[]]></finderForwardSource>\n\t\t\t<contactJumpInfoStr><![CDATA[]]></contactJumpInfoStr>\n\t\t\t<ecSource><![CDATA[]]></ecSource>\n\t\t\t<lastGMsgID><![CDATA[]]></lastGMsgID>\n\t\t\t<sourceCommentScene>20</sourceCommentScene>\n\t\t\t<finderShareExtInfo><![CDATA[{"hasInput":false,"tabContextId":"4-1753848622649","contextId":"1-1-20-f0b29b7ffc8f40599541fdb3984c18b2","shareSrcScene":4}]]></finderShareExtInfo>\n\t\t</finderFeed>\n\t\t<finderLiveProductShare>\n\t\t\t<finderLiveID><![CDATA[]]></finderLiveID>\n\t\t\t<finderUsername><![CDATA[]]></finderUsername>\n\t\t\t<finderObjectID><![CDATA[]]></finderObjectID>\n\t\t\t<finderNonceID><![CDATA[]]></finderNonceID>\n\t\t\t<liveStatus><![CDATA[]]></liveStatus>\n\t\t\t<appId><![CDATA[]]></appId>\n\t\t\t<pagePath><![CDATA[]]></pagePath>\n\t\t\t<productId><![CDATA[]]></productId>\n\t\t\t<coverUrl><![CDATA[]]></coverUrl>\n\t\t\t<productTitle><![CDATA[]]></productTitle>\n\t\t\t<marketPrice><![CDATA[0]]></marketPrice>\n\t\t\t<sellingPrice><![CDATA[0]]></sellingPrice>\n\t\t\t<platformHeadImg><![CDATA[]]></platformHeadImg>\n\t\t\t<platformName><![CDATA[]]></platformName>\n\t\t\t<shopWindowId><![CDATA[]]></shopWindowId>\n\t\t\t<flashSalePrice><![CDATA[0]]></flashSalePrice>\n\t\t\t<flashSaleEndTime><![CDATA[0]]></flashSaleEndTime>\n\t\t\t<ecSource><![CDATA[]]></ecSource>\n\t\t\t<sellingPriceWording><![CDATA[]]></sellingPriceWording>\n\t\t\t<platformIconURL><![CDATA[]]></platformIconURL>\n\t\t\t<firstProductTagURL><![CDATA[]]></firstProductTagURL>\n\t\t\t<firstProductTagAspectRatioString><![CDATA[0.0]]></firstProductTagAspectRatioString>\n\t\t\t<secondProductTagURL><![CDATA[]]></secondProductTagURL>\n\t\t\t<secondProductTagAspectRatioString><![CDATA[0.0]]></secondProductTagAspectRatioString>\n\t\t\t<firstGuaranteeWording><![CDATA[]]></firstGuaranteeWording>\n\t\t\t<secondGuaranteeWording><![CDATA[]]></secondGuaranteeWording>\n\t\t\t<thirdGuaranteeWording><![CDATA[]]></thirdGuaranteeWording>\n\t\t\t<isPriceBeginShow>false</isPriceBeginShow>\n\t\t\t<lastGMsgID><![CDATA[]]></lastGMsgID>\n\t\t\t<promoterKey><![CDATA[]]></promoterKey>\n\t\t\t<discountWording><![CDATA[]]></discountWording>\n\t\t\t<priceSuffixDescription><![CDATA[]]></priceSuffixDescription>\n\t\t\t<productCardKey><![CDATA[]]></productCardKey>\n\t\t\t<isWxShop><![CDATA[]]></isWxShop>\n\t\t\t<brandIconUrl><![CDATA[]]></brandIconUrl>\n\t\t\t<showBoxItemStringList />\n\t\t</finderLiveProductShare>\n\t\t<finderOrder>\n\t\t\t<appID><![CDATA[]]></appID>\n\t\t\t<orderID><![CDATA[]]></orderID>\n\t\t\t<path><![CDATA[]]></path>\n\t\t\t<priceWording><![CDATA[]]></priceWording>\n\t\t\t<stateWording><![CDATA[]]></stateWording>\n\t\t\t<productImageURL><![CDATA[]]></productImageURL>\n\t\t\t<products><![CDATA[]]></products>\n\t\t\t<productsCount><![CDATA[0]]></productsCount>\n\t\t\t<orderType><![CDATA[0]]></orderType>\n\t\t\t<newPriceWording><![CDATA[]]></newPriceWording>\n\t\t\t<newStateWording><![CDATA[]]></newStateWording>\n\t\t\t<useNewWording><![CDATA[0]]></useNewWording>\n\t\t</finderOrder>\n\t\t<finderShopWindowShare>\n\t\t\t<finderUsername><![CDATA[]]></finderUsername>\n\t\t\t<avatar><![CDATA[]]></avatar>\n\t\t\t<nickname><![CDATA[]]></nickname>\n\t\t\t<commodityInStockCount><![CDATA[]]></commodityInStockCount>\n\t\t\t<appId><![CDATA[]]></appId>\n\t\t\t<path><![CDATA[]]></path>\n\t\t\t<appUsername><![CDATA[]]></appUsername>\n\t\t\t<query><![CDATA[]]></query>\n\t\t\t<liteAppId><![CDATA[]]></liteAppId>\n\t\t\t<liteAppPath><![CDATA[]]></liteAppPath>\n\t\t\t<liteAppQuery><![CDATA[]]></liteAppQuery>\n\t\t\t<platformTagURL><![CDATA[]]></platformTagURL>\n\t\t\t<saleWording><![CDATA[]]></saleWording>\n\t\t\t<lastGMsgID><![CDATA[]]></lastGMsgID>\n\t\t\t<profileTypeWording><![CDATA[]]></profileTypeWording>\n\t\t\t<saleWordingExtra><![CDATA[]]></saleWordingExtra>\n\t\t\t<isWxShop><![CDATA[]]></isWxShop>\n\t\t\t<platformIconUrl><![CDATA[]]></platformIconUrl>\n\t\t\t<brandIconUrl><![CDATA[]]></brandIconUrl>\n\t\t\t<description><![CDATA[]]></description>\n\t\t\t<backgroundUrl><![CDATA[]]></backgroundUrl>\n\t\t\t<darkModePlatformIconUrl><![CDATA[]]></darkModePlatformIconUrl>\n\t\t\t<reputationInfo>\n\t\t\t\t<hasReputationInfo>0</hasReputationInfo>\n\t\t\t\t<reputationScore>0</reputationScore>\n\t\t\t\t<reputationWording />\n\t\t\t\t<reputationTextColor />\n\t\t\t\t<reputationLevelWording />\n\t\t\t\t<reputationBackgroundColor />\n\t\t\t</reputationInfo>\n\t\t\t<productImageURLList />\n\t\t</finderShopWindowShare>\n\t\t<findernamecard>\n\t\t\t<username />\n\t\t\t<avatar><![CDATA[]]></avatar>\n\t\t\t<nickname />\n\t\t\t<auth_job />\n\t\t\t<auth_icon>0</auth_icon>\n\t\t\t<auth_icon_url />\n\t\t\t<ecSource><![CDATA[]]></ecSource>\n\t\t\t<lastGMsgID><![CDATA[]]></lastGMsgID>\n\t\t</findernamecard>\n\t\t<finderGuarantee>\n\t\t\t<scene><![CDATA[0]]></scene>\n\t\t</finderGuarantee>\n\t\t<directshare>0</directshare>\n\t\t<gamecenter>\n\t\t\t<namecard>\n\t\t\t\t<iconUrl />\n\t\t\t\t<name />\n\t\t\t\t<desc />\n\t\t\t\t<tail />\n\t\t\t\t<jumpUrl />\n\t\t\t</namecard>\n\t\t</gamecenter>\n\t\t<patMsg>\n\t\t\t<chatUser />\n\t\t\t<records>\n\t\t\t\t<recordNum>0</recordNum>\n\t\t\t</records>\n\t\t</patMsg>\n\t\t<secretmsg>\n\t\t\t<issecretmsg>0</issecretmsg>\n\t\t</secretmsg>\n\t\t<referfromscene>0</referfromscene>\n\t\t<gameshare>\n\t\t\t<liteappext>\n\t\t\t\t<liteappbizdata />\n\t\t\t\t<priority>0</priority>\n\t\t\t</liteappext>\n\t\t\t<appbrandext>\n\t\t\t\t<litegameinfo />\n\t\t\t\t<priority>-1</priority>\n\t\t\t</appbrandext>\n\t\t\t<gameshareid />\n\t\t\t<sharedata />\n\t\t\t<isvideo>0</isvideo>\n\t\t\t<duration>-1</duration>\n\t\t\t<isexposed>0</isexposed>\n\t\t\t<readtext />\n\t\t</gameshare>\n\t\t<mpsharetrace>\n\t\t\t<hasfinderelement>0</hasfinderelement>\n\t\t\t<lastgmsgid />\n\t\t</mpsharetrace>\n\t\t<wxgamecard>\n\t\t\t<framesetname />\n\t\t\t<mbcarddata />\n\t\t\t<minpkgversion />\n\t\t\t<clientextinfo />\n\t\t\t<mbcardheight>0</mbcardheight>\n\t\t\t<isoldversion>0</isoldversion>\n\t\t</wxgamecard>\n\t\t<liteapp>\n\t\t\t<id>null</id>\n\t\t\t<path />\n\t\t\t<query />\n\t\t\t<istransparent>0</istransparent>\n\t\t\t<hideicon>0</hideicon>\n\t\t</liteapp>\n\t\t<opensdk_share_is_modified>0</opensdk_share_is_modified>\n\t</appmsg>\n\t<fromusername>wxid_e3o8s2nf9u2o22</fromusername>\n\t<scene>0</scene>\n\t<appinfo>\n\t\t<version>1</version>\n\t\t<appname />\n\t</appinfo>\n\t<commenturl />\n</msg>\n'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1753849193, 'MsgSource': '<msgsource>\n\t<alnode>\n\t\t<fr>2</fr>\n\t</alnode>\n\t<sec_msg_node>\n\t\t<uuid>f61458404edcb783db51ce16d99e69b7_</uuid>\n\t\t<risk-file-flag />\n\t\t<risk-file-md5-list />\n\t</sec_msg_node>\n\t<silence>0</silence>\n\t<membercount>73</membercount>\n\t<signature>N0_V1_E+XYHoXB|v1_kYX1pLQe</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'PushContent': '你收到了一条消息', 'NewMsgId': 9009403332576211524, 'MsgSeq': 871411569}
2025-07-30 12:19:41 | DEBUG | 从群聊消息中提取发送者: wxid_e3o8s2nf9u2o22
2025-07-30 12:19:41 | DEBUG | XML消息完整内容:
<?xml version="1.0"?>
<msg>
	<appmsg appid="" sdkver="0">
		<title>当前版本不支持展示该内容，请升级至最新版本。</title>
		<des />
		<username />
		<action>view</action>
		<type>51</type>
		<showtype>0</showtype>
		<content />
		<url>https://support.weixin.qq.com/security/readtemplate?t=w_security_center_website/upgrade</url>
		<lowurl />
		<forwardflag>0</forwardflag>
		<dataurl />
		<lowdataurl />
		<contentattr>0</contentattr>
		<streamvideo>
			<streamvideourl />
			<streamvideototaltime>0</streamvideototaltime>
			<streamvideotitle />
			<streamvideowording />
			<streamvideoweburl />
			<streamvideothumburl />
			<streamvideoaduxinfo />
			<streamvideopublishid />
		</streamvideo>
		<canvasPageItem>
			<canvasPageXml><![CDATA[]]></canvasPageXml>
		</canvasPageItem>
		<appattach>
			<totallen>0</totallen>
			<attachid />
			<cdnattachurl />
			<emoticonmd5></emoticonmd5>
			<aeskey></aeskey>
			<fileext />
			<islargefilemsg>0</islargefilemsg>
		</appattach>
		<extinfo />
		<androidsource>0</androidsource>
		<thumburl />
		<mediatagname />
		<messageaction><![CDATA[]]></messageaction>
		<messageext><![CDATA[]]></messageext>
		<emoticongift>
			<packageflag>0</packageflag>
			<packageid />
		</emoticongift>
		<emoticonshared>
			<packageflag>0</packageflag>
			<packageid />
		</emoticonshared>
		<designershared>
			<designeruin>0</designeruin>
			<designername>null</designername>
			<designerrediretcturl><![CDATA[null]]></designerrediretcturl>
		</designershared>
		<emotionpageshared>
			<tid>0</tid>
			<title>null</title>
			<desc>null</desc>
			<iconUrl><![CDATA[null]]></iconUrl>
			<secondUrl>null</secondUrl>
			<pageType>0</pageType>
			<setKey>null</setKey>
		</emotionpageshared>
		<webviewshared>
			<shareUrlOriginal />
			<shareUrlOpen />
			<jsAppId />
			<publisherId />
			<publisherReqId />
		</webviewshared>
		<template_id />
		<md5 />
		<websearch>
			<rec_category>0</rec_category>
			<channelId>0</channelId>
		</websearch>
		<weappinfo>
			<username />
			<appid />
			<appservicetype>0</appservicetype>
			<secflagforsinglepagemode>0</secflagforsinglepagemode>
			<videopageinfo>
				<thumbwidth>0</thumbwidth>
				<thumbheight>0</thumbheight>
				<fromopensdk>0</fromopensdk>
			</videopageinfo>
		</weappinfo>
		<statextstr />
		<musicShareItem>
			<musicDuration>0</musicDuration>
		</musicShareItem>
		<finderFeed>
			<objectId>14711244296557234705</objectId>
			<objectNonceId>15052248329069960540_4_20_13_1_1753848986694891_f5f03dae-6cfb-11f0-9a25-0578289e0d04</objectNonceId>
			<feedType>4</feedType>
			<nickname>绝世蘑菇owo</nickname>
			<username>v2_060000231003b20faec8cae28f10c6d0ce05e430b0775dbe16e30432f03cb941cbc374d4e51d@finder</username>
			<avatar><![CDATA[https://wx.qlogo.cn/finderhead/ver_1/4HpWYdmYGeyBMp1DcokxEZicIGykrx9QNLDgEmNgPQbnhPhicHC22PNMeqR0CmdD9KFyoOsdwfyhm9ZCpk9nMD7p47icliazlBoOKKlHbic5qk3gwIJTRJ62XQ9ZzyYWicPRKk/0]]></avatar>
			<desc>这期是我定制的大卡抱小卡#鸣潮 #鸣潮cos #cosplay #cos #卡提希娅 #芙露德莉斯 #卡提希娅cos</desc>
			<mediaCount>1</mediaCount>
			<localId>0</localId>
			<authIconType>1</authIconType>
			<authIconUrl><![CDATA[https://dldir1v6.qq.com/weixin/checkresupdate/auth_icon_level3_2e2f94615c1e4651a25a7e0446f63135.png]]></authIconUrl>
			<mediaList>
				<media>
					<mediaType>4</mediaType>
					<url><![CDATA[http://wxapp.tc.qq.com/251/20302/stodownload?encfilekey=Cvvj5Ix3eewK0tHtibORqcsqchXNh0Gf3sJcaYqC2rQDj1s6ria5x8rR40Z8Fia534nIbQbmibp0tI8efibkfAYaNxIQEr7GaohAL35h9KGy1jTHicQvjT2za6kZbco962nEPn&bizid=1023&dotrans=0&hy=SH&idx=1&m=&uzid=7a1ac&token=cztXnd9GyrFcZibINAPxDLkxHCmunBtcCEjxuKkNvGS1Q2LDNkuZLDWiaT17iarJK2qiavibWDia532NKZeicxicibXiaibd7icrwU2b8268USTV0Bd1ibI3IctGDoqbmkIwuibic5UrQEicchia4tLRRAVLeibyGpdbHLMTibsEkj86W0q7IDPIjTTSMU&basedata=CAESBnhXVDEyNhoGeFdUMTExGgZ4V1QxMjYaBnhXVDExMhoGeFdUMTI3GgZ4V1QxMTMaBnhXVDEyOCIMCgoKBnhXVDExMhABKgcIvR4QABgC&sign=1dtuUe4tNOCLa0cRdvj4Ud5L_M2l2rUwygDk9K2SXZaFBmiknDMASGUcHTnyNJH-wyh6TaOfce9Jt0ZUa1qFFQ&ctsc=20&extg=108b900&svrbypass=AAuL%2FQsFAAABAAAAAAB1ulkPbE32atZ4nZyJaBAAAADnaHZTnGbFfAj9RgZXfw6VKJ2UBC0PwnHXTAmIv%2BjE8nmIEpZ9bmAd6hrwXLJpMoOlQAcJYs8YaQI%3D&svrnonce=1753848989]]></url>
					<thumbUrl><![CDATA[http://wxapp.tc.qq.com/251/20304/stodownload?encfilekey=rjD5jyTuFrIpZ2ibE8T7Ym3K77SEULgkiazkSEdLLdJj6uib44Qy0MP6IzLMNGygicJKAVxBB4p3x6EoQZic3sVibQ7xPzSSO7jlCESbLBUFXkRM6wqScpg6aJ9g&hy=SZ&idx=1&m=&scene=2&uzid=1&picformat=200&wxampicformat=503&token=6xykWLEnztKIzBicPuvgFxq2fFv0jetq7kDQNTiaw40ibBBpqibC8SgB1LCXszZhEWgqVODl6cedjctpZ2hIQEhmE6VQicJ1S6yYu3MYc8xRJJTKtO1Yv1kDMz69le5vb73OhTLsV8ibyj7TmkLSaEYVZJOFWDP5HWWHIB&ctsc=2-20]]></thumbUrl>
					<coverUrl><![CDATA[http://wxapp.tc.qq.com/251/20304/stodownload?encfilekey=rjD5jyTuFrIpZ2ibE8T7Ym3K77SEULgkiazkSEdLLdJj6uib44Qy0MP6IzLMNGygicJKAVxBB4p3x6EoQZic3sVibQ7xPzSSO7jlCESbLBUFXkRM6wqScpg6aJ9g&hy=SZ&idx=1&m=&scene=2&uzid=1&picformat=200&wxampicformat=503&token=6xykWLEnztKIzBicPuvgFxq2fFv0jetq7kDQNTiaw40ibBBpqibC8SgB1LCXszZhEWgqVODl6cedjctpZ2hIQEhmE6VQicJ1S6yYu3MYc8xRJJTKtO1Yv1kDMz69le5vb73OhTLsV8ibyj7TmkLSaEYVZJOFWDP5HWWHIB&ctsc=2-20]]></coverUrl>
					<fullCoverUrl><![CDATA[]]></fullCoverUrl>
					<fullClipInset><![CDATA[]]></fullClipInset>
					<width>1080.0</width>
					<height>1920.0</height>
					<videoPlayDuration>8</videoPlayDuration>
				</media>
			</mediaList>
			<megaVideo>
				<objectId />
				<objectNonceId />
			</megaVideo>
			<bizUsername />
			<bizNickname />
			<bizAvatar><![CDATA[]]></bizAvatar>
			<bizUsernameV2 />
			<bizAuthIconType>0</bizAuthIconType>
			<bizAuthIconUrl><![CDATA[]]></bizAuthIconUrl>
			<coverEffectType>0</coverEffectType>
			<coverEffectText><![CDATA[]]></coverEffectText>
			<finderForwardSource><![CDATA[]]></finderForwardSource>
			<contactJumpInfoStr><![CDATA[]]></contactJumpInfoStr>
			<ecSource><![CDATA[]]></ecSource>
			<lastGMsgID><![CDATA[]]></lastGMsgID>
			<sourceCommentScene>20</sourceCommentScene>
			<finderShareExtInfo><![CDATA[{"hasInput":false,"tabContextId":"4-1753848622649","contextId":"1-1-20-f0b29b7ffc8f40599541fdb3984c18b2","shareSrcScene":4}]]></finderShareExtInfo>
		</finderFeed>
		<finderLiveProductShare>
			<finderLiveID><![CDATA[]]></finderLiveID>
			<finderUsername><![CDATA[]]></finderUsername>
			<finderObjectID><![CDATA[]]></finderObjectID>
			<finderNonceID><![CDATA[]]></finderNonceID>
			<liveStatus><![CDATA[]]></liveStatus>
			<appId><![CDATA[]]></appId>
			<pagePath><![CDATA[]]></pagePath>
			<productId><![CDATA[]]></productId>
			<coverUrl><![CDATA[]]></coverUrl>
			<productTitle><![CDATA[]]></productTitle>
			<marketPrice><![CDATA[0]]></marketPrice>
			<sellingPrice><![CDATA[0]]></sellingPrice>
			<platformHeadImg><![CDATA[]]></platformHeadImg>
			<platformName><![CDATA[]]></platformName>
			<shopWindowId><![CDATA[]]></shopWindowId>
			<flashSalePrice><![CDATA[0]]></flashSalePrice>
			<flashSaleEndTime><![CDATA[0]]></flashSaleEndTime>
			<ecSource><![CDATA[]]></ecSource>
			<sellingPriceWording><![CDATA[]]></sellingPriceWording>
			<platformIconURL><![CDATA[]]></platformIconURL>
			<firstProductTagURL><![CDATA[]]></firstProductTagURL>
			<firstProductTagAspectRatioString><![CDATA[0.0]]></firstProductTagAspectRatioString>
			<secondProductTagURL><![CDATA[]]></secondProductTagURL>
			<secondProductTagAspectRatioString><![CDATA[0.0]]></secondProductTagAspectRatioString>
			<firstGuaranteeWording><![CDATA[]]></firstGuaranteeWording>
			<secondGuaranteeWording><![CDATA[]]></secondGuaranteeWording>
			<thirdGuaranteeWording><![CDATA[]]></thirdGuaranteeWording>
			<isPriceBeginShow>false</isPriceBeginShow>
			<lastGMsgID><![CDATA[]]></lastGMsgID>
			<promoterKey><![CDATA[]]></promoterKey>
			<discountWording><![CDATA[]]></discountWording>
			<priceSuffixDescription><![CDATA[]]></priceSuffixDescription>
			<productCardKey><![CDATA[]]></productCardKey>
			<isWxShop><![CDATA[]]></isWxShop>
			<brandIconUrl><![CDATA[]]></brandIconUrl>
			<showBoxItemStringList />
		</finderLiveProductShare>
		<finderOrder>
			<appID><![CDATA[]]></appID>
			<orderID><![CDATA[]]></orderID>
			<path><![CDATA[]]></path>
			<priceWording><![CDATA[]]></priceWording>
			<stateWording><![CDATA[]]></stateWording>
			<productImageURL><![CDATA[]]></productImageURL>
			<products><![CDATA[]]></products>
			<productsCount><![CDATA[0]]></productsCount>
			<orderType><![CDATA[0]]></orderType>
			<newPriceWording><![CDATA[]]></newPriceWording>
			<newStateWording><![CDATA[]]></newStateWording>
			<useNewWording><![CDATA[0]]></useNewWording>
		</finderOrder>
		<finderShopWindowShare>
			<finderUsername><![CDATA[]]></finderUsername>
			<avatar><![CDATA[]]></avatar>
			<nickname><![CDATA[]]></nickname>
			<commodityInStockCount><![CDATA[]]></commodityInStockCount>
			<appId><![CDATA[]]></appId>
			<path><![CDATA[]]></path>
			<appUsername><![CDATA[]]></appUsername>
			<query><![CDATA[]]></query>
			<liteAppId><![CDATA[]]></liteAppId>
			<liteAppPath><![CDATA[]]></liteAppPath>
			<liteAppQuery><![CDATA[]]></liteAppQuery>
			<platformTagURL><![CDATA[]]></platformTagURL>
			<saleWording><![CDATA[]]></saleWording>
			<lastGMsgID><![CDATA[]]></lastGMsgID>
			<profileTypeWording><![CDATA[]]></profileTypeWording>
			<saleWordingExtra><![CDATA[]]></saleWordingExtra>
			<isWxShop><![CDATA[]]></isWxShop>
			<platformIconUrl><![CDATA[]]></platformIconUrl>
			<brandIconUrl><![CDATA[]]></brandIconUrl>
			<description><![CDATA[]]></description>
			<backgroundUrl><![CDATA[]]></backgroundUrl>
			<darkModePlatformIconUrl><![CDATA[]]></darkModePlatformIconUrl>
			<reputationInfo>
				<hasReputationInfo>0</hasReputationInfo>
				<reputationScore>0</reputationScore>
				<reputationWording />
				<reputationTextColor />
				<reputationLevelWording />
				<reputationBackgroundColor />
			</reputationInfo>
			<productImageURLList />
		</finderShopWindowShare>
		<findernamecard>
			<username />
			<avatar><![CDATA[]]></avatar>
			<nickname />
			<auth_job />
			<auth_icon>0</auth_icon>
			<auth_icon_url />
			<ecSource><![CDATA[]]></ecSource>
			<lastGMsgID><![CDATA[]]></lastGMsgID>
		</findernamecard>
		<finderGuarantee>
			<scene><![CDATA[0]]></scene>
		</finderGuarantee>
		<directshare>0</directshare>
		<gamecenter>
			<namecard>
				<iconUrl />
				<name />
				<desc />
				<tail />
				<jumpUrl />
			</namecard>
		</gamecenter>
		<patMsg>
			<chatUser />
			<records>
				<recordNum>0</recordNum>
			</records>
		</patMsg>
		<secretmsg>
			<issecretmsg>0</issecretmsg>
		</secretmsg>
		<referfromscene>0</referfromscene>
		<gameshare>
			<liteappext>
				<liteappbizdata />
				<priority>0</priority>
			</liteappext>
			<appbrandext>
				<litegameinfo />
				<priority>-1</priority>
			</appbrandext>
			<gameshareid />
			<sharedata />
			<isvideo>0</isvideo>
			<duration>-1</duration>
			<isexposed>0</isexposed>
			<readtext />
		</gameshare>
		<mpsharetrace>
			<hasfinderelement>0</hasfinderelement>
			<lastgmsgid />
		</mpsharetrace>
		<wxgamecard>
			<framesetname />
			<mbcarddata />
			<minpkgversion />
			<clientextinfo />
			<mbcardheight>0</mbcardheight>
			<isoldversion>0</isoldversion>
		</wxgamecard>
		<liteapp>
			<id>null</id>
			<path />
			<query />
			<istransparent>0</istransparent>
			<hideicon>0</hideicon>
		</liteapp>
		<opensdk_share_is_modified>0</opensdk_share_is_modified>
	</appmsg>
	<fromusername>wxid_e3o8s2nf9u2o22</fromusername>
	<scene>0</scene>
	<appinfo>
		<version>1</version>
		<appname />
	</appinfo>
	<commenturl />
</msg>

2025-07-30 12:19:41 | DEBUG | XML消息类型: 51
2025-07-30 12:19:41 | DEBUG | XML消息标题: 当前版本不支持展示该内容，请升级至最新版本。
2025-07-30 12:19:41 | DEBUG | XML消息描述: None
2025-07-30 12:19:41 | DEBUG | 附件信息 totallen: 0
2025-07-30 12:19:41 | DEBUG | 附件信息 islargefilemsg: 0
2025-07-30 12:19:41 | DEBUG | XML消息URL: https://support.weixin.qq.com/security/readtemplate?t=w_security_center_website/upgrade
2025-07-30 12:19:41 | INFO | 未知的XML消息类型: 51
2025-07-30 12:19:41 | INFO | 消息标题: 当前版本不支持展示该内容，请升级至最新版本。
2025-07-30 12:19:41 | INFO | 消息描述: None
2025-07-30 12:19:41 | INFO | 消息URL: https://support.weixin.qq.com/security/readtemplate?t=w_security_center_website/upgrade
2025-07-30 12:19:41 | INFO | 完整XML内容:
<?xml version="1.0"?>
<msg>
	<appmsg appid="" sdkver="0">
		<title>当前版本不支持展示该内容，请升级至最新版本。</title>
		<des />
		<username />
		<action>view</action>
		<type>51</type>
		<showtype>0</showtype>
		<content />
		<url>https://support.weixin.qq.com/security/readtemplate?t=w_security_center_website/upgrade</url>
		<lowurl />
		<forwardflag>0</forwardflag>
		<dataurl />
		<lowdataurl />
		<contentattr>0</contentattr>
		<streamvideo>
			<streamvideourl />
			<streamvideototaltime>0</streamvideototaltime>
			<streamvideotitle />
			<streamvideowording />
			<streamvideoweburl />
			<streamvideothumburl />
			<streamvideoaduxinfo />
			<streamvideopublishid />
		</streamvideo>
		<canvasPageItem>
			<canvasPageXml><![CDATA[]]></canvasPageXml>
		</canvasPageItem>
		<appattach>
			<totallen>0</totallen>
			<attachid />
			<cdnattachurl />
			<emoticonmd5></emoticonmd5>
			<aeskey></aeskey>
			<fileext />
			<islargefilemsg>0</islargefilemsg>
		</appattach>
		<extinfo />
		<androidsource>0</androidsource>
		<thumburl />
		<mediatagname />
		<messageaction><![CDATA[]]></messageaction>
		<messageext><![CDATA[]]></messageext>
		<emoticongift>
			<packageflag>0</packageflag>
			<packageid />
		</emoticongift>
		<emoticonshared>
			<packageflag>0</packageflag>
			<packageid />
		</emoticonshared>
		<designershared>
			<designeruin>0</designeruin>
			<designername>null</designername>
			<designerrediretcturl><![CDATA[null]]></designerrediretcturl>
		</designershared>
		<emotionpageshared>
			<tid>0</tid>
			<title>null</title>
			<desc>null</desc>
			<iconUrl><![CDATA[null]]></iconUrl>
			<secondUrl>null</secondUrl>
			<pageType>0</pageType>
			<setKey>null</setKey>
		</emotionpageshared>
		<webviewshared>
			<shareUrlOriginal />
			<shareUrlOpen />
			<jsAppId />
			<publisherId />
			<publisherReqId />
		</webviewshared>
		<template_id />
		<md5 />
		<websearch>
			<rec_category>0</rec_category>
			<channelId>0</channelId>
		</websearch>
		<weappinfo>
			<username />
			<appid />
			<appservicetype>0</appservicetype>
			<secflagforsinglepagemode>0</secflagforsinglepagemode>
			<videopageinfo>
				<thumbwidth>0</thumbwidth>
				<thumbheight>0</thumbheight>
				<fromopensdk>0</fromopensdk>
			</videopageinfo>
		</weappinfo>
		<statextstr />
		<musicShareItem>
			<musicDuration>0</musicDuration>
		</musicShareItem>
		<finderFeed>
			<objectId>14711244296557234705</objectId>
			<objectNonceId>15052248329069960540_4_20_13_1_1753848986694891_f5f03dae-6cfb-11f0-9a25-0578289e0d04</objectNonceId>
			<feedType>4</feedType>
			<nickname>绝世蘑菇owo</nickname>
			<username>v2_060000231003b20faec8cae28f10c6d0ce05e430b0775dbe16e30432f03cb941cbc374d4e51d@finder</username>
			<avatar><![CDATA[https://wx.qlogo.cn/finderhead/ver_1/4HpWYdmYGeyBMp1DcokxEZicIGykrx9QNLDgEmNgPQbnhPhicHC22PNMeqR0CmdD9KFyoOsdwfyhm9ZCpk9nMD7p47icliazlBoOKKlHbic5qk3gwIJTRJ62XQ9ZzyYWicPRKk/0]]></avatar>
			<desc>这期是我定制的大卡抱小卡#鸣潮 #鸣潮cos #cosplay #cos #卡提希娅 #芙露德莉斯 #卡提希娅cos</desc>
			<mediaCount>1</mediaCount>
			<localId>0</localId>
			<authIconType>1</authIconType>
			<authIconUrl><![CDATA[https://dldir1v6.qq.com/weixin/checkresupdate/auth_icon_level3_2e2f94615c1e4651a25a7e0446f63135.png]]></authIconUrl>
			<mediaList>
				<media>
					<mediaType>4</mediaType>
					<url><![CDATA[http://wxapp.tc.qq.com/251/20302/stodownload?encfilekey=Cvvj5Ix3eewK0tHtibORqcsqchXNh0Gf3sJcaYqC2rQDj1s6ria5x8rR40Z8Fia534nIbQbmibp0tI8efibkfAYaNxIQEr7GaohAL35h9KGy1jTHicQvjT2za6kZbco962nEPn&bizid=1023&dotrans=0&hy=SH&idx=1&m=&uzid=7a1ac&token=cztXnd9GyrFcZibINAPxDLkxHCmunBtcCEjxuKkNvGS1Q2LDNkuZLDWiaT17iarJK2qiavibWDia532NKZeicxicibXiaibd7icrwU2b8268USTV0Bd1ibI3IctGDoqbmkIwuibic5UrQEicchia4tLRRAVLeibyGpdbHLMTibsEkj86W0q7IDPIjTTSMU&basedata=CAESBnhXVDEyNhoGeFdUMTExGgZ4V1QxMjYaBnhXVDExMhoGeFdUMTI3GgZ4V1QxMTMaBnhXVDEyOCIMCgoKBnhXVDExMhABKgcIvR4QABgC&sign=1dtuUe4tNOCLa0cRdvj4Ud5L_M2l2rUwygDk9K2SXZaFBmiknDMASGUcHTnyNJH-wyh6TaOfce9Jt0ZUa1qFFQ&ctsc=20&extg=108b900&svrbypass=AAuL%2FQsFAAABAAAAAAB1ulkPbE32atZ4nZyJaBAAAADnaHZTnGbFfAj9RgZXfw6VKJ2UBC0PwnHXTAmIv%2BjE8nmIEpZ9bmAd6hrwXLJpMoOlQAcJYs8YaQI%3D&svrnonce=1753848989]]></url>
					<thumbUrl><![CDATA[http://wxapp.tc.qq.com/251/20304/stodownload?encfilekey=rjD5jyTuFrIpZ2ibE8T7Ym3K77SEULgkiazkSEdLLdJj6uib44Qy0MP6IzLMNGygicJKAVxBB4p3x6EoQZic3sVibQ7xPzSSO7jlCESbLBUFXkRM6wqScpg6aJ9g&hy=SZ&idx=1&m=&scene=2&uzid=1&picformat=200&wxampicformat=503&token=6xykWLEnztKIzBicPuvgFxq2fFv0jetq7kDQNTiaw40ibBBpqibC8SgB1LCXszZhEWgqVODl6cedjctpZ2hIQEhmE6VQicJ1S6yYu3MYc8xRJJTKtO1Yv1kDMz69le5vb73OhTLsV8ibyj7TmkLSaEYVZJOFWDP5HWWHIB&ctsc=2-20]]></thumbUrl>
					<coverUrl><![CDATA[http://wxapp.tc.qq.com/251/20304/stodownload?encfilekey=rjD5jyTuFrIpZ2ibE8T7Ym3K77SEULgkiazkSEdLLdJj6uib44Qy0MP6IzLMNGygicJKAVxBB4p3x6EoQZic3sVibQ7xPzSSO7jlCESbLBUFXkRM6wqScpg6aJ9g&hy=SZ&idx=1&m=&scene=2&uzid=1&picformat=200&wxampicformat=503&token=6xykWLEnztKIzBicPuvgFxq2fFv0jetq7kDQNTiaw40ibBBpqibC8SgB1LCXszZhEWgqVODl6cedjctpZ2hIQEhmE6VQicJ1S6yYu3MYc8xRJJTKtO1Yv1kDMz69le5vb73OhTLsV8ibyj7TmkLSaEYVZJOFWDP5HWWHIB&ctsc=2-20]]></coverUrl>
					<fullCoverUrl><![CDATA[]]></fullCoverUrl>
					<fullClipInset><![CDATA[]]></fullClipInset>
					<width>1080.0</width>
					<height>1920.0</height>
					<videoPlayDuration>8</videoPlayDuration>
				</media>
			</mediaList>
			<megaVideo>
				<objectId />
				<objectNonceId />
			</megaVideo>
			<bizUsername />
			<bizNickname />
			<bizAvatar><![CDATA[]]></bizAvatar>
			<bizUsernameV2 />
			<bizAuthIconType>0</bizAuthIconType>
			<bizAuthIconUrl><![CDATA[]]></bizAuthIconUrl>
			<coverEffectType>0</coverEffectType>
			<coverEffectText><![CDATA[]]></coverEffectText>
			<finderForwardSource><![CDATA[]]></finderForwardSource>
			<contactJumpInfoStr><![CDATA[]]></contactJumpInfoStr>
			<ecSource><![CDATA[]]></ecSource>
			<lastGMsgID><![CDATA[]]></lastGMsgID>
			<sourceCommentScene>20</sourceCommentScene>
			<finderShareExtInfo><![CDATA[{"hasInput":false,"tabContextId":"4-1753848622649","contextId":"1-1-20-f0b29b7ffc8f40599541fdb3984c18b2","shareSrcScene":4}]]></finderShareExtInfo>
		</finderFeed>
		<finderLiveProductShare>
			<finderLiveID><![CDATA[]]></finderLiveID>
			<finderUsername><![CDATA[]]></finderUsername>
			<finderObjectID><![CDATA[]]></finderObjectID>
			<finderNonceID><![CDATA[]]></finderNonceID>
			<liveStatus><![CDATA[]]></liveStatus>
			<appId><![CDATA[]]></appId>
			<pagePath><![CDATA[]]></pagePath>
			<productId><![CDATA[]]></productId>
			<coverUrl><![CDATA[]]></coverUrl>
			<productTitle><![CDATA[]]></productTitle>
			<marketPrice><![CDATA[0]]></marketPrice>
			<sellingPrice><![CDATA[0]]></sellingPrice>
			<platformHeadImg><![CDATA[]]></platformHeadImg>
			<platformName><![CDATA[]]></platformName>
			<shopWindowId><![CDATA[]]></shopWindowId>
			<flashSalePrice><![CDATA[0]]></flashSalePrice>
			<flashSaleEndTime><![CDATA[0]]></flashSaleEndTime>
			<ecSource><![CDATA[]]></ecSource>
			<sellingPriceWording><![CDATA[]]></sellingPriceWording>
			<platformIconURL><![CDATA[]]></platformIconURL>
			<firstProductTagURL><![CDATA[]]></firstProductTagURL>
			<firstProductTagAspectRatioString><![CDATA[0.0]]></firstProductTagAspectRatioString>
			<secondProductTagURL><![CDATA[]]></secondProductTagURL>
			<secondProductTagAspectRatioString><![CDATA[0.0]]></secondProductTagAspectRatioString>
			<firstGuaranteeWording><![CDATA[]]></firstGuaranteeWording>
			<secondGuaranteeWording><![CDATA[]]></secondGuaranteeWording>
			<thirdGuaranteeWording><![CDATA[]]></thirdGuaranteeWording>
			<isPriceBeginShow>false</isPriceBeginShow>
			<lastGMsgID><![CDATA[]]></lastGMsgID>
			<promoterKey><![CDATA[]]></promoterKey>
			<discountWording><![CDATA[]]></discountWording>
			<priceSuffixDescription><![CDATA[]]></priceSuffixDescription>
			<productCardKey><![CDATA[]]></productCardKey>
			<isWxShop><![CDATA[]]></isWxShop>
			<brandIconUrl><![CDATA[]]></brandIconUrl>
			<showBoxItemStringList />
		</finderLiveProductShare>
		<finderOrder>
			<appID><![CDATA[]]></appID>
			<orderID><![CDATA[]]></orderID>
			<path><![CDATA[]]></path>
			<priceWording><![CDATA[]]></priceWording>
			<stateWording><![CDATA[]]></stateWording>
			<productImageURL><![CDATA[]]></productImageURL>
			<products><![CDATA[]]></products>
			<productsCount><![CDATA[0]]></productsCount>
			<orderType><![CDATA[0]]></orderType>
			<newPriceWording><![CDATA[]]></newPriceWording>
			<newStateWording><![CDATA[]]></newStateWording>
			<useNewWording><![CDATA[0]]></useNewWording>
		</finderOrder>
		<finderShopWindowShare>
			<finderUsername><![CDATA[]]></finderUsername>
			<avatar><![CDATA[]]></avatar>
			<nickname><![CDATA[]]></nickname>
			<commodityInStockCount><![CDATA[]]></commodityInStockCount>
			<appId><![CDATA[]]></appId>
			<path><![CDATA[]]></path>
			<appUsername><![CDATA[]]></appUsername>
			<query><![CDATA[]]></query>
			<liteAppId><![CDATA[]]></liteAppId>
			<liteAppPath><![CDATA[]]></liteAppPath>
			<liteAppQuery><![CDATA[]]></liteAppQuery>
			<platformTagURL><![CDATA[]]></platformTagURL>
			<saleWording><![CDATA[]]></saleWording>
			<lastGMsgID><![CDATA[]]></lastGMsgID>
			<profileTypeWording><![CDATA[]]></profileTypeWording>
			<saleWordingExtra><![CDATA[]]></saleWordingExtra>
			<isWxShop><![CDATA[]]></isWxShop>
			<platformIconUrl><![CDATA[]]></platformIconUrl>
			<brandIconUrl><![CDATA[]]></brandIconUrl>
			<description><![CDATA[]]></description>
			<backgroundUrl><![CDATA[]]></backgroundUrl>
			<darkModePlatformIconUrl><![CDATA[]]></darkModePlatformIconUrl>
			<reputationInfo>
				<hasReputationInfo>0</hasReputationInfo>
				<reputationScore>0</reputationScore>
				<reputationWording />
				<reputationTextColor />
				<reputationLevelWording />
				<reputationBackgroundColor />
			</reputationInfo>
			<productImageURLList />
		</finderShopWindowShare>
		<findernamecard>
			<username />
			<avatar><![CDATA[]]></avatar>
			<nickname />
			<auth_job />
			<auth_icon>0</auth_icon>
			<auth_icon_url />
			<ecSource><![CDATA[]]></ecSource>
			<lastGMsgID><![CDATA[]]></lastGMsgID>
		</findernamecard>
		<finderGuarantee>
			<scene><![CDATA[0]]></scene>
		</finderGuarantee>
		<directshare>0</directshare>
		<gamecenter>
			<namecard>
				<iconUrl />
				<name />
				<desc />
				<tail />
				<jumpUrl />
			</namecard>
		</gamecenter>
		<patMsg>
			<chatUser />
			<records>
				<recordNum>0</recordNum>
			</records>
		</patMsg>
		<secretmsg>
			<issecretmsg>0</issecretmsg>
		</secretmsg>
		<referfromscene>0</referfromscene>
		<gameshare>
			<liteappext>
				<liteappbizdata />
				<priority>0</priority>
			</liteappext>
			<appbrandext>
				<litegameinfo />
				<priority>-1</priority>
			</appbrandext>
			<gameshareid />
			<sharedata />
			<isvideo>0</isvideo>
			<duration>-1</duration>
			<isexposed>0</isexposed>
			<readtext />
		</gameshare>
		<mpsharetrace>
			<hasfinderelement>0</hasfinderelement>
			<lastgmsgid />
		</mpsharetrace>
		<wxgamecard>
			<framesetname />
			<mbcarddata />
			<minpkgversion />
			<clientextinfo />
			<mbcardheight>0</mbcardheight>
			<isoldversion>0</isoldversion>
		</wxgamecard>
		<liteapp>
			<id>null</id>
			<path />
			<query />
			<istransparent>0</istransparent>
			<hideicon>0</hideicon>
		</liteapp>
		<opensdk_share_is_modified>0</opensdk_share_is_modified>
	</appmsg>
	<fromusername>wxid_e3o8s2nf9u2o22</fromusername>
	<scene>0</scene>
	<appinfo>
		<version>1</version>
		<appname />
	</appinfo>
	<commenturl />
</msg>

2025-07-30 12:20:02 | DEBUG | 收到消息: {'MsgId': 15535585, 'FromUserName': {'string': '***********@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 1, 'Content': {'string': 'wxid_srknxij3jka022:\nMJ绘画任务完成\n提示词：翻译错误：PARAM_FROM_TO_OR_Q_EMPTY\n耗时：101.1秒'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1753849214, 'MsgSource': '<msgsource>\n\t<pua>1</pua>\n\t<silence>0</silence>\n\t<membercount>73</membercount>\n\t<signature>N0_V1_8ElLur91|v1_ZgU7hWud</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'PushContent': '她 : MJ绘画任务完成\n提示词：翻译错误：PARAM_FROM_TO_OR_Q_EMPTY\n耗时：101.1秒', 'NewMsgId': 3659634385477510146, 'MsgSeq': 871411570}
2025-07-30 12:20:02 | INFO | 收到文本消息: 消息ID:15535585 来自:***********@chatroom 发送人:wxid_srknxij3jka022 @:[] 内容:MJ绘画任务完成
提示词：翻译错误：PARAM_FROM_TO_OR_Q_EMPTY
耗时：101.1秒
2025-07-30 12:20:02 | DEBUG | 处理消息内容: 'MJ绘画任务完成
提示词：翻译错误：PARAM_FROM_TO_OR_Q_EMPTY
耗时：101.1秒'
2025-07-30 12:20:02 | DEBUG | 消息内容 'MJ绘画任务完成
提示词：翻译错误：PARAM_FROM_TO_OR_Q_EMPTY
耗时：101.1秒' 不匹配任何命令，忽略
2025-07-30 12:20:08 | DEBUG | 收到消息: {'MsgId': 184621340, 'FromUserName': {'string': '***********@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 43, 'Content': {'string': 'xiaomaochong:\n<?xml version="1.0"?>\n<msg>\n\t<videomsg aeskey="a5ded3b5f63e36517187f148c3d3996e" cdnvideourl="3057020100044b304902010002049363814102032f51490204f3328e71020468899d84042438616666313339372d366135392d346134632d623065312d6565383537626239326332630204052808040201000405004c57c100" cdnthumbaeskey="a5ded3b5f63e36517187f148c3d3996e" cdnthumburl="3057020100044b304902010002049363814102032f51490204f3328e71020468899d84042438616666313339372d366135392d346134632d623065312d6565383537626239326332630204052808040201000405004c57c100" length="1912946" playlength="8" cdnthumblength="8405" cdnthumbwidth="405" cdnthumbheight="720" fromusername="xiaomaochong" md5="cfb9296ff07d71d12ed4af0c3f82f9ab" newmd5="6101ca6c00d2805781cfa6e4343cb3c3" isplaceholder="0" rawmd5="2e8b976537ade641ab8dbddcd628dc2f" rawlength="10680675" cdnrawvideourl="3056020100044a304802010002035a663f02032f514902041d328e71020468899d84042461376432626265352d356261362d343837362d623332612d6632333463353838316232380204059400040201000405004c4e6100" cdnrawvideoaeskey="2085aad7876a3c1d59976730137f235c" overwritenewmsgid="0" originsourcemd5="e8656262448d6e77d967daaad1a69644" isad="0" />\n</msg>\n'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1753849220, 'MsgSource': '<msgsource>\n\t<sec_msg_node>\n\t\t<uuid>2d510022cb19c4bf383dbb60939cc48f_</uuid>\n\t\t<risk-file-flag />\n\t\t<risk-file-md5-list />\n\t</sec_msg_node>\n\t<silence>0</silence>\n\t<membercount>73</membercount>\n\t<signature>N0_V1_28Tw3vdF|v1_LzVHkCXi</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'PushContent': '小爱在群聊中发了一段视频', 'NewMsgId': 5986711232305205703, 'MsgSeq': 871411571}
2025-07-30 12:20:08 | INFO | 收到视频消息: 消息ID:184621340 来自:***********@chatroom 发送人:xiaomaochong XML:
<?xml version="1.0"?>
<msg>
	<videomsg aeskey="a5ded3b5f63e36517187f148c3d3996e" cdnvideourl="3057020100044b304902010002049363814102032f51490204f3328e71020468899d84042438616666313339372d366135392d346134632d623065312d6565383537626239326332630204052808040201000405004c57c100" cdnthumbaeskey="a5ded3b5f63e36517187f148c3d3996e" cdnthumburl="3057020100044b304902010002049363814102032f51490204f3328e71020468899d84042438616666313339372d366135392d346134632d623065312d6565383537626239326332630204052808040201000405004c57c100" length="1912946" playlength="8" cdnthumblength="8405" cdnthumbwidth="405" cdnthumbheight="720" fromusername="xiaomaochong" md5="cfb9296ff07d71d12ed4af0c3f82f9ab" newmd5="6101ca6c00d2805781cfa6e4343cb3c3" isplaceholder="0" rawmd5="2e8b976537ade641ab8dbddcd628dc2f" rawlength="10680675" cdnrawvideourl="3056020100044a304802010002035a663f02032f514902041d328e71020468899d84042461376432626265352d356261362d343837362d623332612d6632333463353838316232380204059400040201000405004c4e6100" cdnrawvideoaeskey="2085aad7876a3c1d59976730137f235c" overwritenewmsgid="0" originsourcemd5="e8656262448d6e77d967daaad1a69644" isad="0" />
</msg>

2025-07-30 12:20:09 | DEBUG | 收到消息: {'MsgId': 405123498, 'FromUserName': {'string': '***********@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 3, 'Content': {'string': 'wxid_srknxij3jka022:\n<?xml version="1.0"?>\n<msg>\n\t<img aeskey="827dcdc0c704dbc676fab13c1ec73cc1" encryver="1" cdnthumbaeskey="827dcdc0c704dbc676fab13c1ec73cc1" cdnthumburl="3057020100044b304902010002049c5da34302032e6c63020436ab016f020468899d83042430396166343133392d646634612d343335382d386233322d326561396133303531663334020405252a010201000405004c53db00" cdnthumblength="5994" cdnthumbheight="120" cdnthumbwidth="120" cdnmidheight="0" cdnmidwidth="0" cdnhdheight="0" cdnhdwidth="0" cdnmidimgurl="3057020100044b304902010002049c5da34302032e6c63020436ab016f020468899d83042430396166343133392d646634612d343335382d386233322d326561396133303531663334020405252a010201000405004c53db00" length="45382" cdnbigimgurl="3057020100044b304902010002049c5da34302032e6c63020436ab016f020468899d83042430396166343133392d646634612d343335382d386233322d326561396133303531663334020405252a010201000405004c53db00" hdlength="6809595" md5="4de6ad4922201226e36af941516e74e1" hevc_mid_size="45382" originsourcemd5="4de6ad4922201226e36af941516e74e1">\n\t\t<secHashInfoBase64 />\n\t\t<live>\n\t\t\t<duration>0</duration>\n\t\t\t<size>0</size>\n\t\t\t<md5 />\n\t\t\t<fileid />\n\t\t\t<hdsize>0</hdsize>\n\t\t\t<hdmd5 />\n\t\t\t<hdfileid />\n\t\t\t<stillimagetimems>0</stillimagetimems>\n\t\t</live>\n\t</img>\n\t<appinfo>\n\t\t<appid>wxb0eef1f67b7a2949</appid>\n\t\t<version>4</version>\n\t\t<appname>国家反诈中心</appname>\n\t\t<isforceupdate>0</isforceupdate>\n\t\t<messageaction />\n\t\t<messageext />\n\t\t<mediatagname />\n\t</appinfo>\n\t<platform_signature />\n\t<imgdatahash />\n\t<ImgSourceInfo>\n\t\t<ImgSourceUrl />\n\t\t<BizType>0</BizType>\n\t</ImgSourceInfo>\n</msg>\n'}, 'Status': 3, 'ImgStatus': 2, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1753849221, 'MsgSource': '<msgsource>\n\t<alnode>\n\t\t<fr>2</fr>\n\t</alnode>\n\t<sec_msg_node>\n\t\t<uuid>d2cac002911f7ac3fd13174f060ff910_</uuid>\n\t\t<risk-file-flag />\n\t\t<risk-file-md5-list />\n\t</sec_msg_node>\n\t<silence>0</silence>\n\t<membercount>73</membercount>\n\t<signature>N0_V1_jpZSWS6P|v1_mpSswsri</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'PushContent': '她在群聊中发了一张图片', 'NewMsgId': 5957480528214397255, 'MsgSeq': 871411572}
2025-07-30 12:20:09 | INFO | 收到图片消息: 消息ID:405123498 来自:***********@chatroom 发送人:wxid_srknxij3jka022 XML:<?xml version="1.0"?><msg><img aeskey="827dcdc0c704dbc676fab13c1ec73cc1" encryver="1" cdnthumbaeskey="827dcdc0c704dbc676fab13c1ec73cc1" cdnthumburl="3057020100044b304902010002049c5da34302032e6c63020436ab016f020468899d83042430396166343133392d646634612d343335382d386233322d326561396133303531663334020405252a010201000405004c53db00" cdnthumblength="5994" cdnthumbheight="120" cdnthumbwidth="120" cdnmidheight="0" cdnmidwidth="0" cdnhdheight="0" cdnhdwidth="0" cdnmidimgurl="3057020100044b304902010002049c5da34302032e6c63020436ab016f020468899d83042430396166343133392d646634612d343335382d386233322d326561396133303531663334020405252a010201000405004c53db00" length="45382" cdnbigimgurl="3057020100044b304902010002049c5da34302032e6c63020436ab016f020468899d83042430396166343133392d646634612d343335382d386233322d326561396133303531663334020405252a010201000405004c53db00" hdlength="6809595" md5="4de6ad4922201226e36af941516e74e1" hevc_mid_size="45382" originsourcemd5="4de6ad4922201226e36af941516e74e1"><secHashInfoBase64 /><live><duration>0</duration><size>0</size><md5 /><fileid /><hdsize>0</hdsize><hdmd5 /><hdfileid /><stillimagetimems>0</stillimagetimems></live></img><appinfo><appid>wxb0eef1f67b7a2949</appid><version>4</version><appname>国家反诈中心</appname><isforceupdate>0</isforceupdate><messageaction /><messageext /><mediatagname /></appinfo><platform_signature /><imgdatahash /><ImgSourceInfo><ImgSourceUrl /><BizType>0</BizType></ImgSourceInfo></msg>
2025-07-30 12:20:10 | INFO | [ImageEcho] 保存图片信息成功，当前群 ***********@chatroom 已存储 5 张图片
2025-07-30 12:20:10 | INFO | [TimerTask] 缓存图片消息: 405123498
2025-07-30 12:20:10 | DEBUG | 收到消息: {'MsgId': 1012631718, 'FromUserName': {'string': '***********@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 3, 'Content': {'string': 'wxid_srknxij3jka022:\n<?xml version="1.0"?>\n<msg>\n\t<img aeskey="2887ec04a2d999de76d97228cadb99e7" encryver="1" cdnthumbaeskey="2887ec04a2d999de76d97228cadb99e7" cdnthumburl="3057020100044b304902010002049c5da34302032e6c63020436ab016f020468899d85042461613663663639382d363736652d343230612d383463332d303039636639373164323065020405252a010201000405004c543f00" cdnthumblength="4006" cdnthumbheight="120" cdnthumbwidth="120" cdnmidheight="0" cdnmidwidth="0" cdnhdheight="0" cdnhdwidth="0" cdnmidimgurl="3057020100044b304902010002049c5da34302032e6c63020436ab016f020468899d85042461613663663639382d363736652d343230612d383463332d303039636639373164323065020405252a010201000405004c543f00" length="39756" cdnbigimgurl="3057020100044b304902010002049c5da34302032e6c63020436ab016f020468899d85042461613663663639382d363736652d343230612d383463332d303039636639373164323065020405252a010201000405004c543f00" hdlength="376991" md5="1b27f5cbacbc68e60c7cbf94d19a8c01" hevc_mid_size="39756" originsourcemd5="1b27f5cbacbc68e60c7cbf94d19a8c01">\n\t\t<secHashInfoBase64 />\n\t\t<live>\n\t\t\t<duration>0</duration>\n\t\t\t<size>0</size>\n\t\t\t<md5 />\n\t\t\t<fileid />\n\t\t\t<hdsize>0</hdsize>\n\t\t\t<hdmd5 />\n\t\t\t<hdfileid />\n\t\t\t<stillimagetimems>0</stillimagetimems>\n\t\t</live>\n\t</img>\n\t<appinfo>\n\t\t<appid>wx0a022393a4dd43b2</appid>\n\t\t<version>5</version>\n\t\t<appname>小爱同学</appname>\n\t\t<isforceupdate>0</isforceupdate>\n\t\t<messageaction />\n\t\t<messageext />\n\t\t<mediatagname />\n\t</appinfo>\n\t<platform_signature />\n\t<imgdatahash />\n\t<ImgSourceInfo>\n\t\t<ImgSourceUrl />\n\t\t<BizType>0</BizType>\n\t</ImgSourceInfo>\n</msg>\n'}, 'Status': 3, 'ImgStatus': 2, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1753849222, 'MsgSource': '<msgsource>\n\t<alnode>\n\t\t<fr>2</fr>\n\t</alnode>\n\t<sec_msg_node>\n\t\t<uuid>ca96f1066e1997257e5172782fa0d2bc_</uuid>\n\t\t<risk-file-flag />\n\t\t<risk-file-md5-list />\n\t</sec_msg_node>\n\t<silence>0</silence>\n\t<membercount>73</membercount>\n\t<signature>N0_V1_huj/nDJF|v1_SWYTzAST</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'PushContent': '她在群聊中发了一张图片', 'NewMsgId': 2090735671554129219, 'MsgSeq': 871411573}
2025-07-30 12:20:10 | INFO | 收到图片消息: 消息ID:1012631718 来自:***********@chatroom 发送人:wxid_srknxij3jka022 XML:<?xml version="1.0"?><msg><img aeskey="2887ec04a2d999de76d97228cadb99e7" encryver="1" cdnthumbaeskey="2887ec04a2d999de76d97228cadb99e7" cdnthumburl="3057020100044b304902010002049c5da34302032e6c63020436ab016f020468899d85042461613663663639382d363736652d343230612d383463332d303039636639373164323065020405252a010201000405004c543f00" cdnthumblength="4006" cdnthumbheight="120" cdnthumbwidth="120" cdnmidheight="0" cdnmidwidth="0" cdnhdheight="0" cdnhdwidth="0" cdnmidimgurl="3057020100044b304902010002049c5da34302032e6c63020436ab016f020468899d85042461613663663639382d363736652d343230612d383463332d303039636639373164323065020405252a010201000405004c543f00" length="39756" cdnbigimgurl="3057020100044b304902010002049c5da34302032e6c63020436ab016f020468899d85042461613663663639382d363736652d343230612d383463332d303039636639373164323065020405252a010201000405004c543f00" hdlength="376991" md5="1b27f5cbacbc68e60c7cbf94d19a8c01" hevc_mid_size="39756" originsourcemd5="1b27f5cbacbc68e60c7cbf94d19a8c01"><secHashInfoBase64 /><live><duration>0</duration><size>0</size><md5 /><fileid /><hdsize>0</hdsize><hdmd5 /><hdfileid /><stillimagetimems>0</stillimagetimems></live></img><appinfo><appid>wx0a022393a4dd43b2</appid><version>5</version><appname>小爱同学</appname><isforceupdate>0</isforceupdate><messageaction /><messageext /><mediatagname /></appinfo><platform_signature /><imgdatahash /><ImgSourceInfo><ImgSourceUrl /><BizType>0</BizType></ImgSourceInfo></msg>
2025-07-30 12:20:11 | INFO | [ImageEcho] 保存图片信息成功，当前群 ***********@chatroom 已存储 5 张图片
2025-07-30 12:20:11 | INFO | [TimerTask] 缓存图片消息: 1012631718
2025-07-30 12:20:11 | DEBUG | 收到消息: {'MsgId': 1647422869, 'FromUserName': {'string': '***********@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 3, 'Content': {'string': 'wxid_srknxij3jka022:\n<?xml version="1.0"?>\n<msg>\n\t<img aeskey="a7ccdff2a1e373fbc24bd69a2b0ee90e" encryver="1" cdnthumbaeskey="a7ccdff2a1e373fbc24bd69a2b0ee90e" cdnthumburl="3057020100044b304902010002049c5da34302032e6c63020436ab016f020468899d86042465323261633137662d366339612d343136622d386664392d363461646332613831643935020405252a010201000405004c53db00" cdnthumblength="6884" cdnthumbheight="120" cdnthumbwidth="120" cdnmidheight="0" cdnmidwidth="0" cdnhdheight="0" cdnhdwidth="0" cdnmidimgurl="3057020100044b304902010002049c5da34302032e6c63020436ab016f020468899d86042465323261633137662d366339612d343136622d386664392d363461646332613831643935020405252a010201000405004c53db00" length="85426" cdnbigimgurl="3057020100044b304902010002049c5da34302032e6c63020436ab016f020468899d86042465323261633137662d366339612d343136622d386664392d363461646332613831643935020405252a010201000405004c53db00" hdlength="622629" md5="10c475eadee48b48c838ed5c8c10b92d" hevc_mid_size="85426" originsourcemd5="10c475eadee48b48c838ed5c8c10b92d">\n\t\t<secHashInfoBase64 />\n\t\t<live>\n\t\t\t<duration>0</duration>\n\t\t\t<size>0</size>\n\t\t\t<md5 />\n\t\t\t<fileid />\n\t\t\t<hdsize>0</hdsize>\n\t\t\t<hdmd5 />\n\t\t\t<hdfileid />\n\t\t\t<stillimagetimems>0</stillimagetimems>\n\t\t</live>\n\t</img>\n\t<appinfo>\n\t\t<appid>wx5aa333606550dfd5</appid>\n\t\t<version>54</version>\n\t\t<appname>QQ音乐</appname>\n\t\t<isforceupdate>0</isforceupdate>\n\t\t<messageaction />\n\t\t<messageext />\n\t\t<mediatagname />\n\t</appinfo>\n\t<platform_signature />\n\t<imgdatahash />\n\t<ImgSourceInfo>\n\t\t<ImgSourceUrl />\n\t\t<BizType>0</BizType>\n\t</ImgSourceInfo>\n</msg>\n'}, 'Status': 3, 'ImgStatus': 2, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1753849222, 'MsgSource': '<msgsource>\n\t<alnode>\n\t\t<fr>2</fr>\n\t</alnode>\n\t<sec_msg_node>\n\t\t<uuid>6065021b6ac0021149d1e2802aba3397_</uuid>\n\t\t<risk-file-flag />\n\t\t<risk-file-md5-list />\n\t</sec_msg_node>\n\t<silence>0</silence>\n\t<membercount>73</membercount>\n\t<signature>N0_V1_wtf7NeZo|v1_41zpZS+1</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'PushContent': '她在群聊中发了一张图片', 'NewMsgId': 207054778162512178, 'MsgSeq': 871411574}
2025-07-30 12:20:11 | INFO | 收到图片消息: 消息ID:1647422869 来自:***********@chatroom 发送人:wxid_srknxij3jka022 XML:<?xml version="1.0"?><msg><img aeskey="a7ccdff2a1e373fbc24bd69a2b0ee90e" encryver="1" cdnthumbaeskey="a7ccdff2a1e373fbc24bd69a2b0ee90e" cdnthumburl="3057020100044b304902010002049c5da34302032e6c63020436ab016f020468899d86042465323261633137662d366339612d343136622d386664392d363461646332613831643935020405252a010201000405004c53db00" cdnthumblength="6884" cdnthumbheight="120" cdnthumbwidth="120" cdnmidheight="0" cdnmidwidth="0" cdnhdheight="0" cdnhdwidth="0" cdnmidimgurl="3057020100044b304902010002049c5da34302032e6c63020436ab016f020468899d86042465323261633137662d366339612d343136622d386664392d363461646332613831643935020405252a010201000405004c53db00" length="85426" cdnbigimgurl="3057020100044b304902010002049c5da34302032e6c63020436ab016f020468899d86042465323261633137662d366339612d343136622d386664392d363461646332613831643935020405252a010201000405004c53db00" hdlength="622629" md5="10c475eadee48b48c838ed5c8c10b92d" hevc_mid_size="85426" originsourcemd5="10c475eadee48b48c838ed5c8c10b92d"><secHashInfoBase64 /><live><duration>0</duration><size>0</size><md5 /><fileid /><hdsize>0</hdsize><hdmd5 /><hdfileid /><stillimagetimems>0</stillimagetimems></live></img><appinfo><appid>wx5aa333606550dfd5</appid><version>54</version><appname>QQ音乐</appname><isforceupdate>0</isforceupdate><messageaction /><messageext /><mediatagname /></appinfo><platform_signature /><imgdatahash /><ImgSourceInfo><ImgSourceUrl /><BizType>0</BizType></ImgSourceInfo></msg>
2025-07-30 12:20:12 | INFO | [ImageEcho] 保存图片信息成功，当前群 ***********@chatroom 已存储 5 张图片
2025-07-30 12:20:12 | INFO | [TimerTask] 缓存图片消息: 1647422869
2025-07-30 12:20:12 | DEBUG | 收到消息: {'MsgId': 1103083052, 'FromUserName': {'string': '***********@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 3, 'Content': {'string': 'wxid_srknxij3jka022:\n<?xml version="1.0"?>\n<msg>\n\t<img aeskey="83cd2e3b10b88ddf990dd77a580f7eaa" encryver="1" cdnthumbaeskey="83cd2e3b10b88ddf990dd77a580f7eaa" cdnthumburl="3057020100044b304902010002049c5da34302032e6c63020436ab016f020468899d87042432643931333430372d383138362d346462362d616166632d633532346239356162633937020405252a010201000405004c543f00" cdnthumblength="3719" cdnthumbheight="120" cdnthumbwidth="120" cdnmidheight="0" cdnmidwidth="0" cdnhdheight="0" cdnhdwidth="0" cdnmidimgurl="3057020100044b304902010002049c5da34302032e6c63020436ab016f020468899d87042432643931333430372d383138362d346462362d616166632d633532346239356162633937020405252a010201000405004c543f00" length="21541" cdnbigimgurl="3057020100044b304902010002049c5da34302032e6c63020436ab016f020468899d87042432643931333430372d383138362d346462362d616166632d633532346239356162633937020405252a010201000405004c543f00" hdlength="243391" md5="03f1353c41729036615b84824ca5666a" hevc_mid_size="21541" originsourcemd5="03f1353c41729036615b84824ca5666a">\n\t\t<secHashInfoBase64 />\n\t\t<live>\n\t\t\t<duration>0</duration>\n\t\t\t<size>0</size>\n\t\t\t<md5 />\n\t\t\t<fileid />\n\t\t\t<hdsize>0</hdsize>\n\t\t\t<hdmd5 />\n\t\t\t<hdfileid />\n\t\t\t<stillimagetimems>0</stillimagetimems>\n\t\t</live>\n\t</img>\n\t<appinfo>\n\t\t<appid>wx92398516de814096</appid>\n\t\t<version>7</version>\n\t\t<appname>梅赛德斯一奔驰</appname>\n\t\t<isforceupdate>0</isforceupdate>\n\t\t<messageaction />\n\t\t<messageext />\n\t\t<mediatagname />\n\t</appinfo>\n\t<platform_signature />\n\t<imgdatahash />\n\t<ImgSourceInfo>\n\t\t<ImgSourceUrl />\n\t\t<BizType>0</BizType>\n\t</ImgSourceInfo>\n</msg>\n'}, 'Status': 3, 'ImgStatus': 2, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1753849223, 'MsgSource': '<msgsource>\n\t<alnode>\n\t\t<fr>2</fr>\n\t</alnode>\n\t<sec_msg_node>\n\t\t<uuid>3852eeb6201e4b69c6870208eeda5d00_</uuid>\n\t\t<risk-file-flag />\n\t\t<risk-file-md5-list />\n\t</sec_msg_node>\n\t<silence>0</silence>\n\t<membercount>73</membercount>\n\t<signature>N0_V1_bGeBD++p|v1_kJpQ9K2B</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'PushContent': '她在群聊中发了一张图片', 'NewMsgId': 6250468599522341183, 'MsgSeq': 871411575}
2025-07-30 12:20:12 | INFO | 收到图片消息: 消息ID:1103083052 来自:***********@chatroom 发送人:wxid_srknxij3jka022 XML:<?xml version="1.0"?><msg><img aeskey="83cd2e3b10b88ddf990dd77a580f7eaa" encryver="1" cdnthumbaeskey="83cd2e3b10b88ddf990dd77a580f7eaa" cdnthumburl="3057020100044b304902010002049c5da34302032e6c63020436ab016f020468899d87042432643931333430372d383138362d346462362d616166632d633532346239356162633937020405252a010201000405004c543f00" cdnthumblength="3719" cdnthumbheight="120" cdnthumbwidth="120" cdnmidheight="0" cdnmidwidth="0" cdnhdheight="0" cdnhdwidth="0" cdnmidimgurl="3057020100044b304902010002049c5da34302032e6c63020436ab016f020468899d87042432643931333430372d383138362d346462362d616166632d633532346239356162633937020405252a010201000405004c543f00" length="21541" cdnbigimgurl="3057020100044b304902010002049c5da34302032e6c63020436ab016f020468899d87042432643931333430372d383138362d346462362d616166632d633532346239356162633937020405252a010201000405004c543f00" hdlength="243391" md5="03f1353c41729036615b84824ca5666a" hevc_mid_size="21541" originsourcemd5="03f1353c41729036615b84824ca5666a"><secHashInfoBase64 /><live><duration>0</duration><size>0</size><md5 /><fileid /><hdsize>0</hdsize><hdmd5 /><hdfileid /><stillimagetimems>0</stillimagetimems></live></img><appinfo><appid>wx92398516de814096</appid><version>7</version><appname>梅赛德斯一奔驰</appname><isforceupdate>0</isforceupdate><messageaction /><messageext /><mediatagname /></appinfo><platform_signature /><imgdatahash /><ImgSourceInfo><ImgSourceUrl /><BizType>0</BizType></ImgSourceInfo></msg>
2025-07-30 12:20:12 | INFO | [ImageEcho] 保存图片信息成功，当前群 ***********@chatroom 已存储 5 张图片
2025-07-30 12:20:12 | INFO | [TimerTask] 缓存图片消息: 1103083052
2025-07-30 12:20:13 | DEBUG | 收到消息: {'MsgId': 1730360223, 'FromUserName': {'string': '***********@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 3, 'Content': {'string': 'wxid_srknxij3jka022:\n<?xml version="1.0"?>\n<msg>\n\t<img aeskey="2ab5fcd0878434786e1f389b58f95ab0" encryver="1" cdnthumbaeskey="2ab5fcd0878434786e1f389b58f95ab0" cdnthumburl="3057020100044b304902010002049c5da34302032e6c63020436ab016f020468899d88042463353962343465302d646338652d343261372d616433302d333739386234366436653733020405252a010201000405004c4d9b00" cdnthumblength="4547" cdnthumbheight="120" cdnthumbwidth="120" cdnmidheight="0" cdnmidwidth="0" cdnhdheight="0" cdnhdwidth="0" cdnmidimgurl="3057020100044b304902010002049c5da34302032e6c63020436ab016f020468899d88042463353962343465302d646338652d343261372d616433302d333739386234366436653733020405252a010201000405004c4d9b00" length="38316" cdnbigimgurl="3057020100044b304902010002049c5da34302032e6c63020436ab016f020468899d88042463353962343465302d646338652d343261372d616433302d333739386234366436653733020405252a010201000405004c4d9b00" hdlength="392198" md5="2b5e03cf1de4d7a74c6c005599fc538e" hevc_mid_size="38316" originsourcemd5="2b5e03cf1de4d7a74c6c005599fc538e">\n\t\t<secHashInfoBase64 />\n\t\t<live>\n\t\t\t<duration>0</duration>\n\t\t\t<size>0</size>\n\t\t\t<md5 />\n\t\t\t<fileid />\n\t\t\t<hdsize>0</hdsize>\n\t\t\t<hdmd5 />\n\t\t\t<hdfileid />\n\t\t\t<stillimagetimems>0</stillimagetimems>\n\t\t</live>\n\t</img>\n\t<appinfo>\n\t\t<appid>wxe3ad19e142df87b3</appid>\n\t\t<version>5</version>\n\t\t<appname>麻豆约拍</appname>\n\t\t<isforceupdate>0</isforceupdate>\n\t\t<messageaction />\n\t\t<messageext />\n\t\t<mediatagname />\n\t</appinfo>\n\t<platform_signature />\n\t<imgdatahash />\n\t<ImgSourceInfo>\n\t\t<ImgSourceUrl />\n\t\t<BizType>0</BizType>\n\t</ImgSourceInfo>\n</msg>\n'}, 'Status': 3, 'ImgStatus': 2, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1753849224, 'MsgSource': '<msgsource>\n\t<alnode>\n\t\t<fr>2</fr>\n\t</alnode>\n\t<sec_msg_node>\n\t\t<uuid>6630efb86e36dbfa9e41afe375140766_</uuid>\n\t\t<risk-file-flag />\n\t\t<risk-file-md5-list />\n\t</sec_msg_node>\n\t<silence>0</silence>\n\t<membercount>73</membercount>\n\t<signature>N0_V1_lpEHhdfr|v1_FDTbost4</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'PushContent': '她在群聊中发了一张图片', 'NewMsgId': 6029478494639962083, 'MsgSeq': 871411576}
2025-07-30 12:20:13 | INFO | 收到图片消息: 消息ID:1730360223 来自:***********@chatroom 发送人:wxid_srknxij3jka022 XML:<?xml version="1.0"?><msg><img aeskey="2ab5fcd0878434786e1f389b58f95ab0" encryver="1" cdnthumbaeskey="2ab5fcd0878434786e1f389b58f95ab0" cdnthumburl="3057020100044b304902010002049c5da34302032e6c63020436ab016f020468899d88042463353962343465302d646338652d343261372d616433302d333739386234366436653733020405252a010201000405004c4d9b00" cdnthumblength="4547" cdnthumbheight="120" cdnthumbwidth="120" cdnmidheight="0" cdnmidwidth="0" cdnhdheight="0" cdnhdwidth="0" cdnmidimgurl="3057020100044b304902010002049c5da34302032e6c63020436ab016f020468899d88042463353962343465302d646338652d343261372d616433302d333739386234366436653733020405252a010201000405004c4d9b00" length="38316" cdnbigimgurl="3057020100044b304902010002049c5da34302032e6c63020436ab016f020468899d88042463353962343465302d646338652d343261372d616433302d333739386234366436653733020405252a010201000405004c4d9b00" hdlength="392198" md5="2b5e03cf1de4d7a74c6c005599fc538e" hevc_mid_size="38316" originsourcemd5="2b5e03cf1de4d7a74c6c005599fc538e"><secHashInfoBase64 /><live><duration>0</duration><size>0</size><md5 /><fileid /><hdsize>0</hdsize><hdmd5 /><hdfileid /><stillimagetimems>0</stillimagetimems></live></img><appinfo><appid>wxe3ad19e142df87b3</appid><version>5</version><appname>麻豆约拍</appname><isforceupdate>0</isforceupdate><messageaction /><messageext /><mediatagname /></appinfo><platform_signature /><imgdatahash /><ImgSourceInfo><ImgSourceUrl /><BizType>0</BizType></ImgSourceInfo></msg>
2025-07-30 12:20:13 | INFO | [ImageEcho] 保存图片信息成功，当前群 ***********@chatroom 已存储 5 张图片
2025-07-30 12:20:13 | INFO | [TimerTask] 缓存图片消息: 1730360223
2025-07-30 12:20:30 | DEBUG | 收到消息: {'MsgId': 1946838777, 'FromUserName': {'string': '27852221909@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 3, 'Content': {'string': 'wxid_2530z9t0joek22:\n<?xml version="1.0"?>\n<msg>\n\t<img aeskey="d630dc69042b29c0fde4967fb1fd2fc2" encryver="1" cdnthumbaeskey="d630dc69042b29c0fde4967fb1fd2fc2" cdnthumburl="3057020100044b30490201000204f060aea202032f802902049af73db7020468899d9a042464356633633865352d326265332d343939662d386366352d343964303738343739373965020405250a020201000405004c50b900" cdnthumblength="4478" cdnthumbheight="196" cdnthumbwidth="432" cdnmidheight="0" cdnmidwidth="0" cdnhdheight="0" cdnhdwidth="0" cdnmidimgurl="3057020100044b30490201000204f060aea202032f802902049af73db7020468899d9a042464356633633865352d326265332d343939662d386366352d343964303738343739373965020405250a020201000405004c50b900" length="819056" md5="12916c2a690c2b19de3a093e4f061be0" hevc_mid_size="70250" originsourcemd5="dbbc8e40d4f30d5498b27b76399d3d3c">\n\t\t<secHashInfoBase64>eyJwaGFzaCI6ImJjYjBhNDAwMDIwMDAwMDAiLCJwZHFIYXNoIjoiZWFjMTJjNTNkZjI4NDU3ZTUx\nYWUxYzczZDBlNjYwOTMyNjhmOWU4OTM4YzVkN2E1MmQ1MTEzOGNlM2RkYTBiZCJ9\n</secHashInfoBase64>\n\t\t<live>\n\t\t\t<duration>0</duration>\n\t\t\t<size>0</size>\n\t\t\t<md5 />\n\t\t\t<fileid />\n\t\t\t<hdsize>0</hdsize>\n\t\t\t<hdmd5 />\n\t\t\t<hdfileid />\n\t\t\t<stillimagetimems>0</stillimagetimems>\n\t\t</live>\n\t</img>\n\t<platform_signature />\n\t<imgdatahash />\n\t<ImgSourceInfo>\n\t\t<ImgSourceUrl />\n\t\t<BizType>0</BizType>\n\t</ImgSourceInfo>\n</msg>\n'}, 'Status': 3, 'ImgStatus': 2, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1753849242, 'MsgSource': '<msgsource>\n\t<sec_msg_node>\n\t\t<uuid>17ae2d4bc0afb26a9efffc95069127e6_</uuid>\n\t\t<risk-file-flag />\n\t\t<risk-file-md5-list />\n\t</sec_msg_node>\n\t<silence>1</silence>\n\t<membercount>145</membercount>\n\t<signature>N0_V1_wComg5hM|v1_0cSmlMRe</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'NewMsgId': 1045288270616198691, 'MsgSeq': 871411577}
2025-07-30 12:20:30 | INFO | 收到图片消息: 消息ID:1946838777 来自:27852221909@chatroom 发送人:wxid_2530z9t0joek22 XML:<?xml version="1.0"?><msg><img aeskey="d630dc69042b29c0fde4967fb1fd2fc2" encryver="1" cdnthumbaeskey="d630dc69042b29c0fde4967fb1fd2fc2" cdnthumburl="3057020100044b30490201000204f060aea202032f802902049af73db7020468899d9a042464356633633865352d326265332d343939662d386366352d343964303738343739373965020405250a020201000405004c50b900" cdnthumblength="4478" cdnthumbheight="196" cdnthumbwidth="432" cdnmidheight="0" cdnmidwidth="0" cdnhdheight="0" cdnhdwidth="0" cdnmidimgurl="3057020100044b30490201000204f060aea202032f802902049af73db7020468899d9a042464356633633865352d326265332d343939662d386366352d343964303738343739373965020405250a020201000405004c50b900" length="819056" md5="12916c2a690c2b19de3a093e4f061be0" hevc_mid_size="70250" originsourcemd5="dbbc8e40d4f30d5498b27b76399d3d3c"><secHashInfoBase64>eyJwaGFzaCI6ImJjYjBhNDAwMDIwMDAwMDAiLCJwZHFIYXNoIjoiZWFjMTJjNTNkZjI4NDU3ZTUxYWUxYzczZDBlNjYwOTMyNjhmOWU4OTM4YzVkN2E1MmQ1MTEzOGNlM2RkYTBiZCJ9</secHashInfoBase64><live><duration>0</duration><size>0</size><md5 /><fileid /><hdsize>0</hdsize><hdmd5 /><hdfileid /><stillimagetimems>0</stillimagetimems></live></img><platform_signature /><imgdatahash /><ImgSourceInfo><ImgSourceUrl /><BizType>0</BizType></ImgSourceInfo></msg>
2025-07-30 12:20:30 | INFO | [ImageEcho] 保存图片信息成功，当前群 27852221909@chatroom 已存储 5 张图片
2025-07-30 12:20:30 | INFO | [TimerTask] 缓存图片消息: 1946838777
2025-07-30 12:20:38 | DEBUG | 收到消息: {'MsgId': 1646380789, 'FromUserName': {'string': '27852221909@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 1, 'Content': {'string': 'wxid_2530z9t0joek22:\n这套衣服有没有图'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1753849250, 'MsgSource': '<msgsource>\n\t<pua>1</pua>\n\t<eggIncluded>1</eggIncluded>\n\t<silence>1</silence>\n\t<membercount>145</membercount>\n\t<signature>N0_V1_8hw3+3Ay|v1_YZAFPHfs</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'NewMsgId': 4026585705566691394, 'MsgSeq': 871411578}
2025-07-30 12:20:38 | INFO | 收到文本消息: 消息ID:1646380789 来自:27852221909@chatroom 发送人:wxid_2530z9t0joek22 @:[] 内容:这套衣服有没有图
2025-07-30 12:20:38 | DEBUG | 处理消息内容: '这套衣服有没有图'
2025-07-30 12:20:38 | DEBUG | 消息内容 '这套衣服有没有图' 不匹配任何命令，忽略
2025-07-30 12:20:40 | DEBUG | 收到消息: {'MsgId': 47774871, 'FromUserName': {'string': '***********@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 1, 'Content': {'string': 'xiaomaochong:\n画一个绝世美女'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1753849250, 'MsgSource': '<msgsource>\n\t<pua>1</pua>\n\t<silence>0</silence>\n\t<membercount>73</membercount>\n\t<signature>N0_V1_8LxsZEfP|v1_pYI782ws</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'PushContent': '小爱 : 画一个绝世美女', 'NewMsgId': 4433520028362027122, 'MsgSeq': 871411579}
2025-07-30 12:20:40 | INFO | 收到文本消息: 消息ID:47774871 来自:***********@chatroom 发送人:xiaomaochong @:[] 内容:画一个绝世美女
2025-07-30 12:20:40 | DEBUG | 处理消息内容: '画一个绝世美女'
2025-07-30 12:20:40 | DEBUG | 消息内容 '画一个绝世美女' 不匹配任何命令，忽略
2025-07-30 12:20:45 | DEBUG | 收到消息: {'MsgId': 1620154398, 'FromUserName': {'string': '27852221909@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 1, 'Content': {'string': 'wxid_2530z9t0joek22:\n有人弄出来吗'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1753849257, 'MsgSource': '<msgsource>\n\t<pua>1</pua>\n\t<eggIncluded>1</eggIncluded>\n\t<silence>1</silence>\n\t<membercount>145</membercount>\n\t<signature>N0_V1_vG2gxzU8|v1_brSfyVqh</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'NewMsgId': 8347234748776329735, 'MsgSeq': 871411580}
2025-07-30 12:20:45 | INFO | 收到文本消息: 消息ID:1620154398 来自:27852221909@chatroom 发送人:wxid_2530z9t0joek22 @:[] 内容:有人弄出来吗
2025-07-30 12:20:45 | DEBUG | 处理消息内容: '有人弄出来吗'
2025-07-30 12:20:45 | DEBUG | 消息内容 '有人弄出来吗' 不匹配任何命令，忽略
2025-07-30 12:21:41 | DEBUG | 收到消息: {'MsgId': 1329532660, 'FromUserName': {'string': '27852221909@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 49, 'Content': {'string': 'wxid_bmzp9achod6922:\n<?xml version="1.0"?>\n<msg>\n\t<appmsg appid="" sdkver="0">\n\t\t<title>没</title>\n\t\t<type>57</type>\n\t\t<appattach>\n\t\t\t<cdnthumbaeskey />\n\t\t\t<aeskey></aeskey>\n\t\t</appattach>\n\t\t<refermsg>\n\t\t\t<type>1</type>\n\t\t\t<svrid>4026585705566691394</svrid>\n\t\t\t<fromusr>27852221909@chatroom</fromusr>\n\t\t\t<chatusr>wxid_2530z9t0joek22</chatusr>\n\t\t\t<displayname>慕ؓ悦ؓ˒</displayname>\n\t\t\t<content>这套衣服有没有图</content>\n\t\t\t<msgsource>&lt;msgsource&gt;&lt;sequence_id&gt;849828423&lt;/sequence_id&gt;\n\t&lt;pua&gt;1&lt;/pua&gt;\n\t&lt;eggIncluded&gt;1&lt;/eggIncluded&gt;\n\t&lt;silence&gt;1&lt;/silence&gt;\n\t&lt;membercount&gt;145&lt;/membercount&gt;\n\t&lt;signature&gt;N0_V1_qVEyHOR9|v1_32AnwvM+&lt;/signature&gt;\n\t&lt;tmp_node&gt;\n\t\t&lt;publisher-id&gt;&lt;/publisher-id&gt;\n\t&lt;/tmp_node&gt;\n&lt;/msgsource&gt;\n</msgsource>\n\t\t\t<createtime>1753849250</createtime>\n\t\t</refermsg>\n\t</appmsg>\n\t<fromusername>wxid_bmzp9achod6922</fromusername>\n\t<scene>0</scene>\n\t<appinfo>\n\t\t<version>1</version>\n\t\t<appname />\n\t</appinfo>\n\t<commenturl />\n</msg>\n'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1753849312, 'MsgSource': '<msgsource>\n\t<bizflag>0</bizflag>\n\t<pua>1</pua>\n\t<eggIncluded>1</eggIncluded>\n\t<sec_msg_node>\n\t\t<uuid>6f33bab2c552f9de2ebf237897d89c0d_</uuid>\n\t\t<risk-file-flag />\n\t\t<risk-file-md5-list />\n\t</sec_msg_node>\n\t<silence>1</silence>\n\t<membercount>145</membercount>\n\t<signature>N0_V1_mlYuv5LY|v1_FjToohrE</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'NewMsgId': 4165350617175089202, 'MsgSeq': 871411581}
2025-07-30 12:21:41 | DEBUG | 从群聊消息中提取发送者: wxid_bmzp9achod6922
2025-07-30 12:21:41 | DEBUG | 使用已解析的XML处理引用消息
2025-07-30 12:21:41 | INFO | 收到引用消息: 消息ID:1329532660 来自:27852221909@chatroom 发送人:wxid_bmzp9achod6922 内容:没 引用类型:1
2025-07-30 12:21:41 | INFO | [DouBaoImageToImage] 收到引用消息: 没
2025-07-30 12:21:41 | INFO | [TimerTask] 收到引用消息调试信息:
2025-07-30 12:21:41 | INFO |   - 消息内容: 没
2025-07-30 12:21:41 | INFO |   - 群组ID: 27852221909@chatroom
2025-07-30 12:21:41 | INFO |   - 发送人: wxid_bmzp9achod6922
2025-07-30 12:21:41 | INFO |   - 引用信息: {'MsgType': 1, 'Content': '这套衣服有没有图', 'Msgid': '4026585705566691394', 'NewMsgId': '4026585705566691394', 'ToWxid': 'wxid_4usgcju5ey9q29', 'FromWxid': '27852221909@chatroom', 'Nickname': '慕ؓ悦ؓ˒', 'MsgSource': '<msgsource><sequence_id>849828423</sequence_id>\n\t<pua>1</pua>\n\t<eggIncluded>1</eggIncluded>\n\t<silence>1</silence>\n\t<membercount>145</membercount>\n\t<signature>N0_V1_qVEyHOR9|v1_32AnwvM+</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'Createtime': '1753849250', 'SenderWxid': 'wxid_bmzp9achod6922'}
2025-07-30 12:21:41 | INFO |   - 引用消息ID: 
2025-07-30 12:21:41 | INFO |   - 引用消息类型: 
2025-07-30 12:21:41 | INFO |   - 引用消息内容: 这套衣服有没有图
2025-07-30 12:21:41 | INFO |   - 引用消息发送人: wxid_bmzp9achod6922
2025-07-30 12:21:55 | DEBUG | 收到消息: {'MsgId': 728489572, 'FromUserName': {'string': '***********@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 1, 'Content': {'string': 'wxid_srknxij3jka022:\nMJ绘画任务完成\n提示词：How much does it cost at once? I owe later. [Picking my nose] [frowning]\n耗时：102.77秒'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1753849327, 'MsgSource': '<msgsource>\n\t<pua>1</pua>\n\t<silence>0</silence>\n\t<membercount>73</membercount>\n\t<signature>N0_V1_3Hw9nfpx|v1_dZVzKyrM</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'PushContent': '她 : MJ绘画任务完成\n提示词：How much does it cost at once? I owe later. [Picking my n...', 'NewMsgId': 3825378374442870592, 'MsgSeq': 871411582}
2025-07-30 12:21:55 | INFO | 收到文本消息: 消息ID:728489572 来自:***********@chatroom 发送人:wxid_srknxij3jka022 @:[] 内容:MJ绘画任务完成
提示词：How much does it cost at once? I owe later. [Picking my nose] [frowning]
耗时：102.77秒
2025-07-30 12:21:55 | DEBUG | 处理消息内容: 'MJ绘画任务完成
提示词：How much does it cost at once? I owe later. [Picking my nose] [frowning]
耗时：102.77秒'
2025-07-30 12:21:55 | DEBUG | 消息内容 'MJ绘画任务完成
提示词：How much does it cost at once? I owe later. [Picking my nose] [frowning]
耗时：102.77秒' 不匹配任何命令，忽略
2025-07-30 12:22:07 | DEBUG | 收到消息: {'MsgId': 237258780, 'FromUserName': {'string': '27852221909@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 1, 'Content': {'string': 'wxid_2530z9t0joek22:\n没图鉴吗'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1753849339, 'MsgSource': '<msgsource>\n\t<pua>1</pua>\n\t<eggIncluded>1</eggIncluded>\n\t<silence>1</silence>\n\t<membercount>145</membercount>\n\t<signature>N0_V1_tyX6CRYd|v1_xmXsD6vZ</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'NewMsgId': 3186382379852868209, 'MsgSeq': 871411583}
2025-07-30 12:22:07 | INFO | 收到文本消息: 消息ID:237258780 来自:27852221909@chatroom 发送人:wxid_2530z9t0joek22 @:[] 内容:没图鉴吗
2025-07-30 12:22:07 | DEBUG | 处理消息内容: '没图鉴吗'
2025-07-30 12:22:07 | DEBUG | 消息内容 '没图鉴吗' 不匹配任何命令，忽略
2025-07-30 12:22:09 | DEBUG | 收到消息: {'MsgId': 732174463, 'FromUserName': {'string': '***********@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 3, 'Content': {'string': 'wxid_srknxij3jka022:\n<?xml version="1.0"?>\n<msg>\n\t<img aeskey="bceb2d58570807f7eb94d7d6c4053962" encryver="1" cdnthumbaeskey="bceb2d58570807f7eb94d7d6c4053962" cdnthumburl="3057020100044b304902010002049c5da34302032e6c63020436ab016f020468899df9042435343161313863362d643464622d343163362d613634342d373935353064373666383532020405252a010201000405004c511f00" cdnthumblength="7727" cdnthumbheight="120" cdnthumbwidth="120" cdnmidheight="0" cdnmidwidth="0" cdnhdheight="0" cdnhdwidth="0" cdnmidimgurl="3057020100044b304902010002049c5da34302032e6c63020436ab016f020468899df9042435343161313863362d643464622d343163362d613634342d373935353064373666383532020405252a010201000405004c511f00" length="60402" cdnbigimgurl="3057020100044b304902010002049c5da34302032e6c63020436ab016f020468899df9042435343161313863362d643464622d343163362d613634342d373935353064373666383532020405252a010201000405004c511f00" hdlength="6527347" md5="b32750878b659b7bd8ed7e9aebaaae38" hevc_mid_size="60402" originsourcemd5="b32750878b659b7bd8ed7e9aebaaae38">\n\t\t<secHashInfoBase64 />\n\t\t<live>\n\t\t\t<duration>0</duration>\n\t\t\t<size>0</size>\n\t\t\t<md5 />\n\t\t\t<fileid />\n\t\t\t<hdsize>0</hdsize>\n\t\t\t<hdmd5 />\n\t\t\t<hdfileid />\n\t\t\t<stillimagetimems>0</stillimagetimems>\n\t\t</live>\n\t</img>\n\t<appinfo>\n\t\t<appid>wx92398516de814096</appid>\n\t\t<version>7</version>\n\t\t<appname>梅赛德斯一奔驰</appname>\n\t\t<isforceupdate>0</isforceupdate>\n\t\t<messageaction />\n\t\t<messageext />\n\t\t<mediatagname />\n\t</appinfo>\n\t<platform_signature />\n\t<imgdatahash />\n\t<ImgSourceInfo>\n\t\t<ImgSourceUrl />\n\t\t<BizType>0</BizType>\n\t</ImgSourceInfo>\n</msg>\n'}, 'Status': 3, 'ImgStatus': 2, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1753849340, 'MsgSource': '<msgsource>\n\t<alnode>\n\t\t<fr>2</fr>\n\t</alnode>\n\t<sec_msg_node>\n\t\t<uuid>5a526adea4d9fad65e563f2109fd08ce_</uuid>\n\t\t<risk-file-flag />\n\t\t<risk-file-md5-list />\n\t</sec_msg_node>\n\t<silence>0</silence>\n\t<membercount>73</membercount>\n\t<signature>N0_V1_oRN0X7D5|v1_sXfc83pc</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'PushContent': '她在群聊中发了一张图片', 'NewMsgId': 7650111020873331073, 'MsgSeq': 871411584}
2025-07-30 12:22:09 | INFO | 收到图片消息: 消息ID:732174463 来自:***********@chatroom 发送人:wxid_srknxij3jka022 XML:<?xml version="1.0"?><msg><img aeskey="bceb2d58570807f7eb94d7d6c4053962" encryver="1" cdnthumbaeskey="bceb2d58570807f7eb94d7d6c4053962" cdnthumburl="3057020100044b304902010002049c5da34302032e6c63020436ab016f020468899df9042435343161313863362d643464622d343163362d613634342d373935353064373666383532020405252a010201000405004c511f00" cdnthumblength="7727" cdnthumbheight="120" cdnthumbwidth="120" cdnmidheight="0" cdnmidwidth="0" cdnhdheight="0" cdnhdwidth="0" cdnmidimgurl="3057020100044b304902010002049c5da34302032e6c63020436ab016f020468899df9042435343161313863362d643464622d343163362d613634342d373935353064373666383532020405252a010201000405004c511f00" length="60402" cdnbigimgurl="3057020100044b304902010002049c5da34302032e6c63020436ab016f020468899df9042435343161313863362d643464622d343163362d613634342d373935353064373666383532020405252a010201000405004c511f00" hdlength="6527347" md5="b32750878b659b7bd8ed7e9aebaaae38" hevc_mid_size="60402" originsourcemd5="b32750878b659b7bd8ed7e9aebaaae38"><secHashInfoBase64 /><live><duration>0</duration><size>0</size><md5 /><fileid /><hdsize>0</hdsize><hdmd5 /><hdfileid /><stillimagetimems>0</stillimagetimems></live></img><appinfo><appid>wx92398516de814096</appid><version>7</version><appname>梅赛德斯一奔驰</appname><isforceupdate>0</isforceupdate><messageaction /><messageext /><mediatagname /></appinfo><platform_signature /><imgdatahash /><ImgSourceInfo><ImgSourceUrl /><BizType>0</BizType></ImgSourceInfo></msg>
2025-07-30 12:22:09 | INFO | [ImageEcho] 保存图片信息成功，当前群 ***********@chatroom 已存储 5 张图片
2025-07-30 12:22:09 | INFO | [TimerTask] 缓存图片消息: 732174463
2025-07-30 12:22:10 | DEBUG | 收到消息: {'MsgId': 1292411848, 'FromUserName': {'string': '***********@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 3, 'Content': {'string': 'wxid_srknxij3jka022:\n<?xml version="1.0"?>\n<msg>\n\t<img aeskey="eec4faa075c26b9ce3814d9d69b13850" encryver="1" cdnthumbaeskey="eec4faa075c26b9ce3814d9d69b13850" cdnthumburl="3057020100044b304902010002049c5da34302032e6c63020436ab016f020468899dfc042465626230633664662d623930662d346430302d383031332d633362636161323038616631020405292a010201000405004c505700" cdnthumblength="5783" cdnthumbheight="120" cdnthumbwidth="120" cdnmidheight="0" cdnmidwidth="0" cdnhdheight="0" cdnhdwidth="0" cdnmidimgurl="3057020100044b304902010002049c5da34302032e6c63020436ab016f020468899dfc042465626230633664662d623930662d346430302d383031332d633362636161323038616631020405292a010201000405004c505700" length="44879" cdnbigimgurl="3057020100044b304902010002049c5da34302032e6c63020436ab016f020468899dfc042465626230633664662d623930662d346430302d383031332d633362636161323038616631020405292a010201000405004c505700" hdlength="374158" md5="56d42781bca3df48c2c7e13eb9aa2b18" hevc_mid_size="44879" originsourcemd5="56d42781bca3df48c2c7e13eb9aa2b18">\n\t\t<secHashInfoBase64 />\n\t\t<live>\n\t\t\t<duration>0</duration>\n\t\t\t<size>0</size>\n\t\t\t<md5 />\n\t\t\t<fileid />\n\t\t\t<hdsize>0</hdsize>\n\t\t\t<hdmd5 />\n\t\t\t<hdfileid />\n\t\t\t<stillimagetimems>0</stillimagetimems>\n\t\t</live>\n\t</img>\n\t<appinfo>\n\t\t<appid>wx92398516de814096</appid>\n\t\t<version>7</version>\n\t\t<appname>梅赛德斯一奔驰</appname>\n\t\t<isforceupdate>0</isforceupdate>\n\t\t<messageaction />\n\t\t<messageext />\n\t\t<mediatagname />\n\t</appinfo>\n\t<platform_signature />\n\t<imgdatahash />\n\t<ImgSourceInfo>\n\t\t<ImgSourceUrl />\n\t\t<BizType>0</BizType>\n\t</ImgSourceInfo>\n</msg>\n'}, 'Status': 3, 'ImgStatus': 2, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1753849340, 'MsgSource': '<msgsource>\n\t<alnode>\n\t\t<fr>2</fr>\n\t</alnode>\n\t<sec_msg_node>\n\t\t<uuid>88798c775561247e34e18fcdccd4dda2_</uuid>\n\t\t<risk-file-flag />\n\t\t<risk-file-md5-list />\n\t</sec_msg_node>\n\t<silence>0</silence>\n\t<membercount>73</membercount>\n\t<signature>N0_V1_GOFO+011|v1_LRBXeq8T</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'PushContent': '她在群聊中发了一张图片', 'NewMsgId': 4119173045695683144, 'MsgSeq': 871411585}
2025-07-30 12:22:10 | INFO | 收到图片消息: 消息ID:1292411848 来自:***********@chatroom 发送人:wxid_srknxij3jka022 XML:<?xml version="1.0"?><msg><img aeskey="eec4faa075c26b9ce3814d9d69b13850" encryver="1" cdnthumbaeskey="eec4faa075c26b9ce3814d9d69b13850" cdnthumburl="3057020100044b304902010002049c5da34302032e6c63020436ab016f020468899dfc042465626230633664662d623930662d346430302d383031332d633362636161323038616631020405292a010201000405004c505700" cdnthumblength="5783" cdnthumbheight="120" cdnthumbwidth="120" cdnmidheight="0" cdnmidwidth="0" cdnhdheight="0" cdnhdwidth="0" cdnmidimgurl="3057020100044b304902010002049c5da34302032e6c63020436ab016f020468899dfc042465626230633664662d623930662d346430302d383031332d633362636161323038616631020405292a010201000405004c505700" length="44879" cdnbigimgurl="3057020100044b304902010002049c5da34302032e6c63020436ab016f020468899dfc042465626230633664662d623930662d346430302d383031332d633362636161323038616631020405292a010201000405004c505700" hdlength="374158" md5="56d42781bca3df48c2c7e13eb9aa2b18" hevc_mid_size="44879" originsourcemd5="56d42781bca3df48c2c7e13eb9aa2b18"><secHashInfoBase64 /><live><duration>0</duration><size>0</size><md5 /><fileid /><hdsize>0</hdsize><hdmd5 /><hdfileid /><stillimagetimems>0</stillimagetimems></live></img><appinfo><appid>wx92398516de814096</appid><version>7</version><appname>梅赛德斯一奔驰</appname><isforceupdate>0</isforceupdate><messageaction /><messageext /><mediatagname /></appinfo><platform_signature /><imgdatahash /><ImgSourceInfo><ImgSourceUrl /><BizType>0</BizType></ImgSourceInfo></msg>
2025-07-30 12:22:10 | INFO | [ImageEcho] 保存图片信息成功，当前群 ***********@chatroom 已存储 5 张图片
2025-07-30 12:22:10 | INFO | [TimerTask] 缓存图片消息: 1292411848
2025-07-30 12:22:10 | DEBUG | 收到消息: {'MsgId': 70559140, 'FromUserName': {'string': '***********@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 3, 'Content': {'string': 'wxid_srknxij3jka022:\n<?xml version="1.0"?>\n<msg>\n\t<img aeskey="3fbd2169096e16be2094dc53a2a3dbdb" encryver="1" cdnthumbaeskey="3fbd2169096e16be2094dc53a2a3dbdb" cdnthumburl="3057020100044b304902010002049c5da34302032e6c63020436ab016f020468899dfc042439313031336230622d663561642d346230622d393332622d643532363131323738373238020405292a010201000405004c57c300" cdnthumblength="5761" cdnthumbheight="120" cdnthumbwidth="120" cdnmidheight="0" cdnmidwidth="0" cdnhdheight="0" cdnhdwidth="0" cdnmidimgurl="3057020100044b304902010002049c5da34302032e6c63020436ab016f020468899dfc042439313031336230622d663561642d346230622d393332622d643532363131323738373238020405292a010201000405004c57c300" length="49165" cdnbigimgurl="3057020100044b304902010002049c5da34302032e6c63020436ab016f020468899dfc042439313031336230622d663561642d346230622d393332622d643532363131323738373238020405292a010201000405004c57c300" hdlength="361852" md5="b7f8e56003fef85a8fa0a7b6b382c2ed" hevc_mid_size="49165" originsourcemd5="b7f8e56003fef85a8fa0a7b6b382c2ed">\n\t\t<secHashInfoBase64 />\n\t\t<live>\n\t\t\t<duration>0</duration>\n\t\t\t<size>0</size>\n\t\t\t<md5 />\n\t\t\t<fileid />\n\t\t\t<hdsize>0</hdsize>\n\t\t\t<hdmd5 />\n\t\t\t<hdfileid />\n\t\t\t<stillimagetimems>0</stillimagetimems>\n\t\t</live>\n\t</img>\n\t<appinfo>\n\t\t<appid>wx92398516de814096</appid>\n\t\t<version>7</version>\n\t\t<appname>梅赛德斯一奔驰</appname>\n\t\t<isforceupdate>0</isforceupdate>\n\t\t<messageaction />\n\t\t<messageext />\n\t\t<mediatagname />\n\t</appinfo>\n\t<platform_signature />\n\t<imgdatahash />\n\t<ImgSourceInfo>\n\t\t<ImgSourceUrl />\n\t\t<BizType>0</BizType>\n\t</ImgSourceInfo>\n</msg>\n'}, 'Status': 3, 'ImgStatus': 2, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1753849340, 'MsgSource': '<msgsource>\n\t<alnode>\n\t\t<fr>2</fr>\n\t</alnode>\n\t<sec_msg_node>\n\t\t<uuid>d3f3c708f55fc654c69d6876325d2e63_</uuid>\n\t\t<risk-file-flag />\n\t\t<risk-file-md5-list />\n\t</sec_msg_node>\n\t<silence>0</silence>\n\t<membercount>73</membercount>\n\t<signature>N0_V1_LkIAO6B2|v1_9K6Bya6+</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'PushContent': '她在群聊中发了一张图片', 'NewMsgId': 158731338449393035, 'MsgSeq': 871411586}
2025-07-30 12:22:10 | INFO | 收到图片消息: 消息ID:70559140 来自:***********@chatroom 发送人:wxid_srknxij3jka022 XML:<?xml version="1.0"?><msg><img aeskey="3fbd2169096e16be2094dc53a2a3dbdb" encryver="1" cdnthumbaeskey="3fbd2169096e16be2094dc53a2a3dbdb" cdnthumburl="3057020100044b304902010002049c5da34302032e6c63020436ab016f020468899dfc042439313031336230622d663561642d346230622d393332622d643532363131323738373238020405292a010201000405004c57c300" cdnthumblength="5761" cdnthumbheight="120" cdnthumbwidth="120" cdnmidheight="0" cdnmidwidth="0" cdnhdheight="0" cdnhdwidth="0" cdnmidimgurl="3057020100044b304902010002049c5da34302032e6c63020436ab016f020468899dfc042439313031336230622d663561642d346230622d393332622d643532363131323738373238020405292a010201000405004c57c300" length="49165" cdnbigimgurl="3057020100044b304902010002049c5da34302032e6c63020436ab016f020468899dfc042439313031336230622d663561642d346230622d393332622d643532363131323738373238020405292a010201000405004c57c300" hdlength="361852" md5="b7f8e56003fef85a8fa0a7b6b382c2ed" hevc_mid_size="49165" originsourcemd5="b7f8e56003fef85a8fa0a7b6b382c2ed"><secHashInfoBase64 /><live><duration>0</duration><size>0</size><md5 /><fileid /><hdsize>0</hdsize><hdmd5 /><hdfileid /><stillimagetimems>0</stillimagetimems></live></img><appinfo><appid>wx92398516de814096</appid><version>7</version><appname>梅赛德斯一奔驰</appname><isforceupdate>0</isforceupdate><messageaction /><messageext /><mediatagname /></appinfo><platform_signature /><imgdatahash /><ImgSourceInfo><ImgSourceUrl /><BizType>0</BizType></ImgSourceInfo></msg>
2025-07-30 12:22:11 | INFO | [ImageEcho] 保存图片信息成功，当前群 ***********@chatroom 已存储 5 张图片
2025-07-30 12:22:11 | INFO | [TimerTask] 缓存图片消息: 70559140
2025-07-30 12:22:11 | DEBUG | 收到消息: {'MsgId': 330306033, 'FromUserName': {'string': '***********@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 3, 'Content': {'string': 'wxid_srknxij3jka022:\n<?xml version="1.0"?>\n<msg>\n\t<img aeskey="44d4793f706c9722452644b709491c64" encryver="1" cdnthumbaeskey="44d4793f706c9722452644b709491c64" cdnthumburl="3057020100044b304902010002049c5da34302032e6c63020436ab016f020468899dfc042431323565383431352d316535392d346663332d396435652d613431303536646163663365020405292a010201000405004c537700" cdnthumblength="5018" cdnthumbheight="120" cdnthumbwidth="120" cdnmidheight="0" cdnmidwidth="0" cdnhdheight="0" cdnhdwidth="0" cdnmidimgurl="3057020100044b304902010002049c5da34302032e6c63020436ab016f020468899dfc042431323565383431352d316535392d346663332d396435652d613431303536646163663365020405292a010201000405004c537700" length="33067" cdnbigimgurl="3057020100044b304902010002049c5da34302032e6c63020436ab016f020468899dfc042431323565383431352d316535392d346663332d396435652d613431303536646163663365020405292a010201000405004c537700" hdlength="358129" md5="ee7b2c1210ff2322ff74783a2022560e" hevc_mid_size="33067" originsourcemd5="ee7b2c1210ff2322ff74783a2022560e">\n\t\t<secHashInfoBase64 />\n\t\t<live>\n\t\t\t<duration>0</duration>\n\t\t\t<size>0</size>\n\t\t\t<md5 />\n\t\t\t<fileid />\n\t\t\t<hdsize>0</hdsize>\n\t\t\t<hdmd5 />\n\t\t\t<hdfileid />\n\t\t\t<stillimagetimems>0</stillimagetimems>\n\t\t</live>\n\t</img>\n\t<appinfo>\n\t\t<appid>wx92398516de814096</appid>\n\t\t<version>7</version>\n\t\t<appname>梅赛德斯一奔驰</appname>\n\t\t<isforceupdate>0</isforceupdate>\n\t\t<messageaction />\n\t\t<messageext />\n\t\t<mediatagname />\n\t</appinfo>\n\t<platform_signature />\n\t<imgdatahash />\n\t<ImgSourceInfo>\n\t\t<ImgSourceUrl />\n\t\t<BizType>0</BizType>\n\t</ImgSourceInfo>\n</msg>\n'}, 'Status': 3, 'ImgStatus': 2, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1753849341, 'MsgSource': '<msgsource>\n\t<alnode>\n\t\t<fr>2</fr>\n\t</alnode>\n\t<sec_msg_node>\n\t\t<uuid>a35a2a496ef943451ee4c880729d7c3a_</uuid>\n\t\t<risk-file-flag />\n\t\t<risk-file-md5-list />\n\t</sec_msg_node>\n\t<silence>0</silence>\n\t<membercount>73</membercount>\n\t<signature>N0_V1_SvPtlQly|v1_H+QJEQHJ</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'PushContent': '她在群聊中发了一张图片', 'NewMsgId': 2319609237368992007, 'MsgSeq': 871411587}
2025-07-30 12:22:11 | INFO | 收到图片消息: 消息ID:330306033 来自:***********@chatroom 发送人:wxid_srknxij3jka022 XML:<?xml version="1.0"?><msg><img aeskey="44d4793f706c9722452644b709491c64" encryver="1" cdnthumbaeskey="44d4793f706c9722452644b709491c64" cdnthumburl="3057020100044b304902010002049c5da34302032e6c63020436ab016f020468899dfc042431323565383431352d316535392d346663332d396435652d613431303536646163663365020405292a010201000405004c537700" cdnthumblength="5018" cdnthumbheight="120" cdnthumbwidth="120" cdnmidheight="0" cdnmidwidth="0" cdnhdheight="0" cdnhdwidth="0" cdnmidimgurl="3057020100044b304902010002049c5da34302032e6c63020436ab016f020468899dfc042431323565383431352d316535392d346663332d396435652d613431303536646163663365020405292a010201000405004c537700" length="33067" cdnbigimgurl="3057020100044b304902010002049c5da34302032e6c63020436ab016f020468899dfc042431323565383431352d316535392d346663332d396435652d613431303536646163663365020405292a010201000405004c537700" hdlength="358129" md5="ee7b2c1210ff2322ff74783a2022560e" hevc_mid_size="33067" originsourcemd5="ee7b2c1210ff2322ff74783a2022560e"><secHashInfoBase64 /><live><duration>0</duration><size>0</size><md5 /><fileid /><hdsize>0</hdsize><hdmd5 /><hdfileid /><stillimagetimems>0</stillimagetimems></live></img><appinfo><appid>wx92398516de814096</appid><version>7</version><appname>梅赛德斯一奔驰</appname><isforceupdate>0</isforceupdate><messageaction /><messageext /><mediatagname /></appinfo><platform_signature /><imgdatahash /><ImgSourceInfo><ImgSourceUrl /><BizType>0</BizType></ImgSourceInfo></msg>
2025-07-30 12:22:12 | INFO | [ImageEcho] 保存图片信息成功，当前群 ***********@chatroom 已存储 5 张图片
2025-07-30 12:22:12 | INFO | [TimerTask] 缓存图片消息: 330306033
2025-07-30 12:22:12 | DEBUG | 收到消息: {'MsgId': 64651195, 'FromUserName': {'string': '***********@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 3, 'Content': {'string': 'wxid_srknxij3jka022:\n<?xml version="1.0"?>\n<msg>\n\t<img aeskey="9f971dba4f7bf66977afc0aba0fb11c1" encryver="1" cdnthumbaeskey="9f971dba4f7bf66977afc0aba0fb11c1" cdnthumburl="3057020100044b304902010002049c5da34302032e6c63020436ab016f020468899dfd042461366165616338332d393732612d346536612d626337652d343132656331316163366531020405252a010201000405004c511f00" cdnthumblength="7233" cdnthumbheight="120" cdnthumbwidth="120" cdnmidheight="0" cdnmidwidth="0" cdnhdheight="0" cdnhdwidth="0" cdnmidimgurl="3057020100044b304902010002049c5da34302032e6c63020436ab016f020468899dfd042461366165616338332d393732612d346536612d626337652d343132656331316163366531020405252a010201000405004c511f00" length="85461" cdnbigimgurl="3057020100044b304902010002049c5da34302032e6c63020436ab016f020468899dfd042461366165616338332d393732612d346536612d626337652d343132656331316163366531020405252a010201000405004c511f00" hdlength="533655" md5="490e1e2460c25fdac65e0e550e4d9b86" hevc_mid_size="85461" originsourcemd5="490e1e2460c25fdac65e0e550e4d9b86">\n\t\t<secHashInfoBase64 />\n\t\t<live>\n\t\t\t<duration>0</duration>\n\t\t\t<size>0</size>\n\t\t\t<md5 />\n\t\t\t<fileid />\n\t\t\t<hdsize>0</hdsize>\n\t\t\t<hdmd5 />\n\t\t\t<hdfileid />\n\t\t\t<stillimagetimems>0</stillimagetimems>\n\t\t</live>\n\t</img>\n\t<appinfo>\n\t\t<appid>wx0a022393a4dd43b2</appid>\n\t\t<version>5</version>\n\t\t<appname>小爱同学</appname>\n\t\t<isforceupdate>0</isforceupdate>\n\t\t<messageaction />\n\t\t<messageext />\n\t\t<mediatagname />\n\t</appinfo>\n\t<platform_signature />\n\t<imgdatahash />\n\t<ImgSourceInfo>\n\t\t<ImgSourceUrl />\n\t\t<BizType>0</BizType>\n\t</ImgSourceInfo>\n</msg>\n'}, 'Status': 3, 'ImgStatus': 2, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1753849341, 'MsgSource': '<msgsource>\n\t<alnode>\n\t\t<fr>2</fr>\n\t</alnode>\n\t<sec_msg_node>\n\t\t<uuid>3c2c46e7d9d70dd00f77e9050a3d3407_</uuid>\n\t\t<risk-file-flag />\n\t\t<risk-file-md5-list />\n\t</sec_msg_node>\n\t<silence>0</silence>\n\t<membercount>73</membercount>\n\t<signature>N0_V1_VjZVu+8V|v1_GQeFgMxl</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'PushContent': '她在群聊中发了一张图片', 'NewMsgId': 6464434103382516200, 'MsgSeq': 871411588}
2025-07-30 12:22:12 | INFO | 收到图片消息: 消息ID:64651195 来自:***********@chatroom 发送人:wxid_srknxij3jka022 XML:<?xml version="1.0"?><msg><img aeskey="9f971dba4f7bf66977afc0aba0fb11c1" encryver="1" cdnthumbaeskey="9f971dba4f7bf66977afc0aba0fb11c1" cdnthumburl="3057020100044b304902010002049c5da34302032e6c63020436ab016f020468899dfd042461366165616338332d393732612d346536612d626337652d343132656331316163366531020405252a010201000405004c511f00" cdnthumblength="7233" cdnthumbheight="120" cdnthumbwidth="120" cdnmidheight="0" cdnmidwidth="0" cdnhdheight="0" cdnhdwidth="0" cdnmidimgurl="3057020100044b304902010002049c5da34302032e6c63020436ab016f020468899dfd042461366165616338332d393732612d346536612d626337652d343132656331316163366531020405252a010201000405004c511f00" length="85461" cdnbigimgurl="3057020100044b304902010002049c5da34302032e6c63020436ab016f020468899dfd042461366165616338332d393732612d346536612d626337652d343132656331316163366531020405252a010201000405004c511f00" hdlength="533655" md5="490e1e2460c25fdac65e0e550e4d9b86" hevc_mid_size="85461" originsourcemd5="490e1e2460c25fdac65e0e550e4d9b86"><secHashInfoBase64 /><live><duration>0</duration><size>0</size><md5 /><fileid /><hdsize>0</hdsize><hdmd5 /><hdfileid /><stillimagetimems>0</stillimagetimems></live></img><appinfo><appid>wx0a022393a4dd43b2</appid><version>5</version><appname>小爱同学</appname><isforceupdate>0</isforceupdate><messageaction /><messageext /><mediatagname /></appinfo><platform_signature /><imgdatahash /><ImgSourceInfo><ImgSourceUrl /><BizType>0</BizType></ImgSourceInfo></msg>
2025-07-30 12:22:13 | INFO | [ImageEcho] 保存图片信息成功，当前群 ***********@chatroom 已存储 5 张图片
2025-07-30 12:22:13 | INFO | [TimerTask] 缓存图片消息: 64651195
2025-07-30 12:23:46 | DEBUG | 收到消息: {'MsgId': 757806009, 'FromUserName': {'string': '***********@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 1, 'Content': {'string': 'wxid_srknxij3jka022:\nMJ绘画任务完成\n提示词：A peerless beauty\n耗时：95.71秒'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1753849438, 'MsgSource': '<msgsource>\n\t<pua>1</pua>\n\t<silence>0</silence>\n\t<membercount>73</membercount>\n\t<signature>N0_V1_Wk+XbKgT|v1_dyRMLmj0</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'PushContent': '她 : MJ绘画任务完成\n提示词：A peerless beauty\n耗时：95.71秒', 'NewMsgId': 2574630285248906277, 'MsgSeq': 871411589}
2025-07-30 12:23:46 | INFO | 收到文本消息: 消息ID:757806009 来自:***********@chatroom 发送人:wxid_srknxij3jka022 @:[] 内容:MJ绘画任务完成
提示词：A peerless beauty
耗时：95.71秒
2025-07-30 12:23:46 | DEBUG | 处理消息内容: 'MJ绘画任务完成
提示词：A peerless beauty
耗时：95.71秒'
2025-07-30 12:23:46 | DEBUG | 消息内容 'MJ绘画任务完成
提示词：A peerless beauty
耗时：95.71秒' 不匹配任何命令，忽略
2025-07-30 12:23:48 | DEBUG | 收到消息: {'MsgId': 1754959160, 'FromUserName': {'string': '27852221909@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 47, 'Content': {'string': 'wxid_bmzp9achod6922:\n<msg><emoji fromusername="wxid_bmzp9achod6922" tousername="27852221909@chatroom" type="2" idbuffer="media:0_0" md5="72876848580c9f6380092f05cb4abdb6" len="60106" productid="" androidmd5="72876848580c9f6380092f05cb4abdb6" androidlen="60106" s60v3md5="72876848580c9f6380092f05cb4abdb6" s60v3len="60106" s60v5md5="72876848580c9f6380092f05cb4abdb6" s60v5len="60106" cdnurl="http://vweixinf.tc.qq.com/110/20401/stodownload?m=72876848580c9f6380092f05cb4abdb6&amp;filekey=30440201010430302e02016e0402535a04203732383736383438353830633966363338303039326630356362346162646236020300eaca040d00000004627466730000000132&amp;hy=SZ&amp;storeid=26751691f000914ff3c2d9d6c0000006e01004fb1535a02768bc1e72ebc75b&amp;ef=1&amp;bizid=1022" designerid="" thumburl="" encrypturl="http://vweixinf.tc.qq.com/110/20402/stodownload?m=40bed0dfd2a322fc110604369cb75f92&amp;filekey=30440201010430302e02016e0402535a04203430626564306466643261333232666331313036303433363963623735663932020300ead0040d00000004627466730000000132&amp;hy=SZ&amp;storeid=26751691f0009f10f3c2d9d6c0000006e02004fb2535a02768bc1e72ebc766&amp;ef=2&amp;bizid=1022" aeskey="d050ef1c2fc74c0b85bc70fc855a3063" externurl="http://vweixinf.tc.qq.com/110/20403/stodownload?m=df8649faa3c767fdaf412ac0a8903e92&amp;filekey=3043020101042f302d02016e0402535a0420646638363439666161336337363766646166343132616330613839303365393202020b10040d00000004627466730000000132&amp;hy=SZ&amp;storeid=26751691f000ad1663c2d9d6c0000006e03004fb3535a02768bc1e72ebc771&amp;ef=3&amp;bizid=1022" externmd5="921a584c6c587b0dcc16504455f5d18d" width="300" height="300" tpurl="" tpauthkey="" attachedtext="" attachedtextcolor="" lensid="" emojiattr="" linkid="" desc=""></emoji><gameext type="0" content="0"></gameext></msg>'}, 'Status': 3, 'ImgStatus': 2, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1753849438, 'MsgSource': '<msgsource>\n\t<silence>1</silence>\n\t<membercount>145</membercount>\n\t<signature>N0_V1_fTKOuLPo|v1_UyAiVElg</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'NewMsgId': 6648334189680688484, 'MsgSeq': 871411590}
2025-07-30 12:23:48 | INFO | 收到表情消息: 消息ID:1754959160 来自:27852221909@chatroom 发送人:wxid_bmzp9achod6922 MD5:72876848580c9f6380092f05cb4abdb6 大小:60106
2025-07-30 12:23:48 | DEBUG | [DoubaoImageRecognition] 缓存表情消息: 6648334189680688484
2025-07-30 12:23:49 | DEBUG | 收到消息: {'MsgId': 18593096, 'FromUserName': {'string': '27852221909@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 1, 'Content': {'string': 'wxid_bmzp9achod6922:\n有'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1753849440, 'MsgSource': '<msgsource>\n\t<bizflag>0</bizflag>\n\t<pua>1</pua>\n\t<eggIncluded>1</eggIncluded>\n\t<silence>1</silence>\n\t<membercount>145</membercount>\n\t<signature>N0_V1_GleudvHv|v1_zNTiHPsI</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'NewMsgId': 9137006721738666539, 'MsgSeq': 871411591}
2025-07-30 12:23:49 | INFO | 收到文本消息: 消息ID:18593096 来自:27852221909@chatroom 发送人:wxid_bmzp9achod6922 @:[] 内容:有
2025-07-30 12:23:49 | DEBUG | 处理消息内容: '有'
2025-07-30 12:23:49 | DEBUG | 消息内容 '有' 不匹配任何命令，忽略
2025-07-30 12:23:51 | DEBUG | 收到消息: {'MsgId': 1919769799, 'FromUserName': {'string': '27852221909@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 1, 'Content': {'string': 'wxid_bmzp9achod6922:\n你快去弄'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1753849443, 'MsgSource': '<msgsource>\n\t<bizflag>0</bizflag>\n\t<pua>1</pua>\n\t<eggIncluded>1</eggIncluded>\n\t<silence>1</silence>\n\t<membercount>145</membercount>\n\t<signature>N0_V1_0+/XVzxN|v1_jFYP4zmD</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'NewMsgId': 8542727155353974647, 'MsgSeq': 871411592}
2025-07-30 12:23:51 | INFO | 收到文本消息: 消息ID:1919769799 来自:27852221909@chatroom 发送人:wxid_bmzp9achod6922 @:[] 内容:你快去弄
2025-07-30 12:23:51 | DEBUG | 处理消息内容: '你快去弄'
2025-07-30 12:23:51 | DEBUG | 消息内容 '你快去弄' 不匹配任何命令，忽略
2025-07-30 12:23:56 | DEBUG | 收到消息: {'MsgId': 1476571024, 'FromUserName': {'string': '***********@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 3, 'Content': {'string': 'wxid_srknxij3jka022:\n<?xml version="1.0"?>\n<msg>\n\t<img aeskey="8c41525a2313d145128b4e74c1a8fb08" encryver="1" cdnthumbaeskey="8c41525a2313d145128b4e74c1a8fb08" cdnthumburl="3057020100044b304902010002049c5da34302032e6c63020436ab016f020468899e67042462656233306630622d666439302d346161342d616539322d643035636639663036666339020405252a010201000405004c4dff00" cdnthumblength="7264" cdnthumbheight="120" cdnthumbwidth="120" cdnmidheight="0" cdnmidwidth="0" cdnhdheight="0" cdnhdwidth="0" cdnmidimgurl="3057020100044b304902010002049c5da34302032e6c63020436ab016f020468899e67042462656233306630622d666439302d346161342d616539322d643035636639663036666339020405252a010201000405004c4dff00" length="49692" cdnbigimgurl="3057020100044b304902010002049c5da34302032e6c63020436ab016f020468899e67042462656233306630622d666439302d346161342d616539322d643035636639663036666339020405252a010201000405004c4dff00" hdlength="7011525" md5="877ff0850c4748868a3c56366ad40e02" hevc_mid_size="49692" originsourcemd5="877ff0850c4748868a3c56366ad40e02">\n\t\t<secHashInfoBase64 />\n\t\t<live>\n\t\t\t<duration>0</duration>\n\t\t\t<size>0</size>\n\t\t\t<md5 />\n\t\t\t<fileid />\n\t\t\t<hdsize>0</hdsize>\n\t\t\t<hdmd5 />\n\t\t\t<hdfileid />\n\t\t\t<stillimagetimems>0</stillimagetimems>\n\t\t</live>\n\t</img>\n\t<appinfo>\n\t\t<appid>wx5aa333606550dfd5</appid>\n\t\t<version>54</version>\n\t\t<appname>QQ音乐</appname>\n\t\t<isforceupdate>0</isforceupdate>\n\t\t<messageaction />\n\t\t<messageext />\n\t\t<mediatagname />\n\t</appinfo>\n\t<platform_signature />\n\t<imgdatahash />\n\t<ImgSourceInfo>\n\t\t<ImgSourceUrl />\n\t\t<BizType>0</BizType>\n\t</ImgSourceInfo>\n</msg>\n'}, 'Status': 3, 'ImgStatus': 2, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1753849448, 'MsgSource': '<msgsource>\n\t<alnode>\n\t\t<fr>2</fr>\n\t</alnode>\n\t<sec_msg_node>\n\t\t<uuid>d7c5ff2d76213e5a1bf4cadd64d3892f_</uuid>\n\t\t<risk-file-flag />\n\t\t<risk-file-md5-list />\n\t</sec_msg_node>\n\t<silence>0</silence>\n\t<membercount>73</membercount>\n\t<signature>N0_V1_Jt/RyLrv|v1_gzBdYIkY</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'PushContent': '她在群聊中发了一张图片', 'NewMsgId': 8356724915549958759, 'MsgSeq': 871411593}
2025-07-30 12:23:56 | INFO | 收到图片消息: 消息ID:1476571024 来自:***********@chatroom 发送人:wxid_srknxij3jka022 XML:<?xml version="1.0"?><msg><img aeskey="8c41525a2313d145128b4e74c1a8fb08" encryver="1" cdnthumbaeskey="8c41525a2313d145128b4e74c1a8fb08" cdnthumburl="3057020100044b304902010002049c5da34302032e6c63020436ab016f020468899e67042462656233306630622d666439302d346161342d616539322d643035636639663036666339020405252a010201000405004c4dff00" cdnthumblength="7264" cdnthumbheight="120" cdnthumbwidth="120" cdnmidheight="0" cdnmidwidth="0" cdnhdheight="0" cdnhdwidth="0" cdnmidimgurl="3057020100044b304902010002049c5da34302032e6c63020436ab016f020468899e67042462656233306630622d666439302d346161342d616539322d643035636639663036666339020405252a010201000405004c4dff00" length="49692" cdnbigimgurl="3057020100044b304902010002049c5da34302032e6c63020436ab016f020468899e67042462656233306630622d666439302d346161342d616539322d643035636639663036666339020405252a010201000405004c4dff00" hdlength="7011525" md5="877ff0850c4748868a3c56366ad40e02" hevc_mid_size="49692" originsourcemd5="877ff0850c4748868a3c56366ad40e02"><secHashInfoBase64 /><live><duration>0</duration><size>0</size><md5 /><fileid /><hdsize>0</hdsize><hdmd5 /><hdfileid /><stillimagetimems>0</stillimagetimems></live></img><appinfo><appid>wx5aa333606550dfd5</appid><version>54</version><appname>QQ音乐</appname><isforceupdate>0</isforceupdate><messageaction /><messageext /><mediatagname /></appinfo><platform_signature /><imgdatahash /><ImgSourceInfo><ImgSourceUrl /><BizType>0</BizType></ImgSourceInfo></msg>
2025-07-30 12:23:57 | INFO | [ImageEcho] 保存图片信息成功，当前群 ***********@chatroom 已存储 5 张图片
2025-07-30 12:23:57 | INFO | [TimerTask] 缓存图片消息: 1476571024
2025-07-30 12:23:57 | DEBUG | 收到消息: {'MsgId': 1970035557, 'FromUserName': {'string': '27852221909@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 1, 'Content': {'string': 'wxid_bmzp9achod6922:\n别人有的你凭啥没'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1753849448, 'MsgSource': '<msgsource>\n\t<bizflag>0</bizflag>\n\t<pua>1</pua>\n\t<eggIncluded>1</eggIncluded>\n\t<silence>1</silence>\n\t<membercount>145</membercount>\n\t<signature>N0_V1_WZtWxJkV|v1_PuMnIdSM</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'NewMsgId': 1327909855642762198, 'MsgSeq': 871411594}
2025-07-30 12:23:57 | INFO | 收到文本消息: 消息ID:1970035557 来自:27852221909@chatroom 发送人:wxid_bmzp9achod6922 @:[] 内容:别人有的你凭啥没
2025-07-30 12:23:57 | DEBUG | 处理消息内容: '别人有的你凭啥没'
2025-07-30 12:23:57 | DEBUG | 消息内容 '别人有的你凭啥没' 不匹配任何命令，忽略
2025-07-30 12:23:59 | DEBUG | 收到消息: {'MsgId': 1272824660, 'FromUserName': {'string': '***********@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 3, 'Content': {'string': 'wxid_srknxij3jka022:\n<?xml version="1.0"?>\n<msg>\n\t<img aeskey="975755c6da4a1e2e960072b84beb9e96" encryver="1" cdnthumbaeskey="975755c6da4a1e2e960072b84beb9e96" cdnthumburl="3057020100044b304902010002049c5da34302032e6c63020436ab016f020468899e68042436336664383336312d666264632d343434642d613035332d653863363564356462333864020405292a010201000405004c57c300" cdnthumblength="4305" cdnthumbheight="120" cdnthumbwidth="120" cdnmidheight="0" cdnmidwidth="0" cdnhdheight="0" cdnhdwidth="0" cdnmidimgurl="3057020100044b304902010002049c5da34302032e6c63020436ab016f020468899e68042436336664383336312d666264632d343434642d613035332d653863363564356462333864020405292a010201000405004c57c300" length="25031" cdnbigimgurl="3057020100044b304902010002049c5da34302032e6c63020436ab016f020468899e68042436336664383336312d666264632d343434642d613035332d653863363564356462333864020405292a010201000405004c57c300" hdlength="291309" md5="6c9e49e3edfa292011021fb119dff7e0" hevc_mid_size="25031" originsourcemd5="6c9e49e3edfa292011021fb119dff7e0">\n\t\t<secHashInfoBase64 />\n\t\t<live>\n\t\t\t<duration>0</duration>\n\t\t\t<size>0</size>\n\t\t\t<md5 />\n\t\t\t<fileid />\n\t\t\t<hdsize>0</hdsize>\n\t\t\t<hdmd5 />\n\t\t\t<hdfileid />\n\t\t\t<stillimagetimems>0</stillimagetimems>\n\t\t</live>\n\t</img>\n\t<appinfo>\n\t\t<appid>wxca25444c1e5649c5</appid>\n\t\t<version>4</version>\n\t\t<appname>iPhome Xi Max工程机</appname>\n\t\t<isforceupdate>0</isforceupdate>\n\t\t<messageaction />\n\t\t<messageext />\n\t\t<mediatagname />\n\t</appinfo>\n\t<platform_signature />\n\t<imgdatahash />\n\t<ImgSourceInfo>\n\t\t<ImgSourceUrl />\n\t\t<BizType>0</BizType>\n\t</ImgSourceInfo>\n</msg>\n'}, 'Status': 3, 'ImgStatus': 2, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1753849449, 'MsgSource': '<msgsource>\n\t<alnode>\n\t\t<fr>2</fr>\n\t</alnode>\n\t<sec_msg_node>\n\t\t<uuid>84378a14b996648d6a2aac948c29459b_</uuid>\n\t\t<risk-file-flag />\n\t\t<risk-file-md5-list />\n\t</sec_msg_node>\n\t<silence>0</silence>\n\t<membercount>73</membercount>\n\t<signature>N0_V1_FOG0HLk9|v1_UvMWKTw3</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'PushContent': '她在群聊中发了一张图片', 'NewMsgId': 4656452851944732206, 'MsgSeq': 871411595}
2025-07-30 12:23:59 | INFO | 收到图片消息: 消息ID:1272824660 来自:***********@chatroom 发送人:wxid_srknxij3jka022 XML:<?xml version="1.0"?><msg><img aeskey="975755c6da4a1e2e960072b84beb9e96" encryver="1" cdnthumbaeskey="975755c6da4a1e2e960072b84beb9e96" cdnthumburl="3057020100044b304902010002049c5da34302032e6c63020436ab016f020468899e68042436336664383336312d666264632d343434642d613035332d653863363564356462333864020405292a010201000405004c57c300" cdnthumblength="4305" cdnthumbheight="120" cdnthumbwidth="120" cdnmidheight="0" cdnmidwidth="0" cdnhdheight="0" cdnhdwidth="0" cdnmidimgurl="3057020100044b304902010002049c5da34302032e6c63020436ab016f020468899e68042436336664383336312d666264632d343434642d613035332d653863363564356462333864020405292a010201000405004c57c300" length="25031" cdnbigimgurl="3057020100044b304902010002049c5da34302032e6c63020436ab016f020468899e68042436336664383336312d666264632d343434642d613035332d653863363564356462333864020405292a010201000405004c57c300" hdlength="291309" md5="6c9e49e3edfa292011021fb119dff7e0" hevc_mid_size="25031" originsourcemd5="6c9e49e3edfa292011021fb119dff7e0"><secHashInfoBase64 /><live><duration>0</duration><size>0</size><md5 /><fileid /><hdsize>0</hdsize><hdmd5 /><hdfileid /><stillimagetimems>0</stillimagetimems></live></img><appinfo><appid>wxca25444c1e5649c5</appid><version>4</version><appname>iPhome Xi Max工程机</appname><isforceupdate>0</isforceupdate><messageaction /><messageext /><mediatagname /></appinfo><platform_signature /><imgdatahash /><ImgSourceInfo><ImgSourceUrl /><BizType>0</BizType></ImgSourceInfo></msg>
2025-07-30 12:24:00 | INFO | [ImageEcho] 保存图片信息成功，当前群 ***********@chatroom 已存储 5 张图片
2025-07-30 12:24:00 | INFO | [TimerTask] 缓存图片消息: 1272824660
2025-07-30 12:24:00 | DEBUG | 收到消息: {'MsgId': 1751095060, 'FromUserName': {'string': '***********@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 3, 'Content': {'string': 'wxid_srknxij3jka022:\n<?xml version="1.0"?>\n<msg>\n\t<img aeskey="cc2f0e3978d0d86075d9f1d252da7df0" encryver="1" cdnthumbaeskey="cc2f0e3978d0d86075d9f1d252da7df0" cdnthumburl="3057020100044b304902010002049c5da34302032e6c63020436ab016f020468899e69042461396234656136642d396438302d346231622d393361662d363663326331313439666532020405252a010201000405004c4d9b00" cdnthumblength="6123" cdnthumbheight="120" cdnthumbwidth="120" cdnmidheight="0" cdnmidwidth="0" cdnhdheight="0" cdnhdwidth="0" cdnmidimgurl="3057020100044b304902010002049c5da34302032e6c63020436ab016f020468899e69042461396234656136642d396438302d346231622d393361662d363663326331313439666532020405252a010201000405004c4d9b00" length="61299" cdnbigimgurl="3057020100044b304902010002049c5da34302032e6c63020436ab016f020468899e69042461396234656136642d396438302d346231622d393361662d363663326331313439666532020405252a010201000405004c4d9b00" hdlength="491880" md5="fcf4043cee44a25876b85d30fc8ecc97" hevc_mid_size="61299" originsourcemd5="fcf4043cee44a25876b85d30fc8ecc97">\n\t\t<secHashInfoBase64 />\n\t\t<live>\n\t\t\t<duration>0</duration>\n\t\t\t<size>0</size>\n\t\t\t<md5 />\n\t\t\t<fileid />\n\t\t\t<hdsize>0</hdsize>\n\t\t\t<hdmd5 />\n\t\t\t<hdfileid />\n\t\t\t<stillimagetimems>0</stillimagetimems>\n\t\t</live>\n\t</img>\n\t<appinfo>\n\t\t<appid>wxb0eef1f67b7a2949</appid>\n\t\t<version>4</version>\n\t\t<appname>国家反诈中心</appname>\n\t\t<isforceupdate>0</isforceupdate>\n\t\t<messageaction />\n\t\t<messageext />\n\t\t<mediatagname />\n\t</appinfo>\n\t<platform_signature />\n\t<imgdatahash />\n\t<ImgSourceInfo>\n\t\t<ImgSourceUrl />\n\t\t<BizType>0</BizType>\n\t</ImgSourceInfo>\n</msg>\n'}, 'Status': 3, 'ImgStatus': 2, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1753849449, 'MsgSource': '<msgsource>\n\t<alnode>\n\t\t<fr>2</fr>\n\t</alnode>\n\t<sec_msg_node>\n\t\t<uuid>bf50cd684c602e65e64e2c7c0bf942b8_</uuid>\n\t\t<risk-file-flag />\n\t\t<risk-file-md5-list />\n\t</sec_msg_node>\n\t<silence>0</silence>\n\t<membercount>73</membercount>\n\t<signature>N0_V1_8ry5EkZp|v1_LsF9VW9b</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'PushContent': '她在群聊中发了一张图片', 'NewMsgId': 5441209600897010328, 'MsgSeq': 871411596}
2025-07-30 12:24:00 | INFO | 收到图片消息: 消息ID:1751095060 来自:***********@chatroom 发送人:wxid_srknxij3jka022 XML:<?xml version="1.0"?><msg><img aeskey="cc2f0e3978d0d86075d9f1d252da7df0" encryver="1" cdnthumbaeskey="cc2f0e3978d0d86075d9f1d252da7df0" cdnthumburl="3057020100044b304902010002049c5da34302032e6c63020436ab016f020468899e69042461396234656136642d396438302d346231622d393361662d363663326331313439666532020405252a010201000405004c4d9b00" cdnthumblength="6123" cdnthumbheight="120" cdnthumbwidth="120" cdnmidheight="0" cdnmidwidth="0" cdnhdheight="0" cdnhdwidth="0" cdnmidimgurl="3057020100044b304902010002049c5da34302032e6c63020436ab016f020468899e69042461396234656136642d396438302d346231622d393361662d363663326331313439666532020405252a010201000405004c4d9b00" length="61299" cdnbigimgurl="3057020100044b304902010002049c5da34302032e6c63020436ab016f020468899e69042461396234656136642d396438302d346231622d393361662d363663326331313439666532020405252a010201000405004c4d9b00" hdlength="491880" md5="fcf4043cee44a25876b85d30fc8ecc97" hevc_mid_size="61299" originsourcemd5="fcf4043cee44a25876b85d30fc8ecc97"><secHashInfoBase64 /><live><duration>0</duration><size>0</size><md5 /><fileid /><hdsize>0</hdsize><hdmd5 /><hdfileid /><stillimagetimems>0</stillimagetimems></live></img><appinfo><appid>wxb0eef1f67b7a2949</appid><version>4</version><appname>国家反诈中心</appname><isforceupdate>0</isforceupdate><messageaction /><messageext /><mediatagname /></appinfo><platform_signature /><imgdatahash /><ImgSourceInfo><ImgSourceUrl /><BizType>0</BizType></ImgSourceInfo></msg>
2025-07-30 12:24:00 | INFO | [ImageEcho] 保存图片信息成功，当前群 ***********@chatroom 已存储 5 张图片
2025-07-30 12:24:00 | INFO | [TimerTask] 缓存图片消息: 1751095060
2025-07-30 12:24:01 | DEBUG | 收到消息: {'MsgId': 22743193, 'FromUserName': {'string': '***********@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 3, 'Content': {'string': 'wxid_srknxij3jka022:\n<?xml version="1.0"?>\n<msg>\n\t<img aeskey="b071f35f838962b49524a5ab0529a977" encryver="1" cdnthumbaeskey="b071f35f838962b49524a5ab0529a977" cdnthumburl="3057020100044b304902010002049c5da34302032e6c63020436ab016f020468899e6a042436326664336366632d363532632d346536632d613537372d356666363530313730376665020405292a010201000405004c54a300" cdnthumblength="5138" cdnthumbheight="120" cdnthumbwidth="120" cdnmidheight="0" cdnmidwidth="0" cdnhdheight="0" cdnhdwidth="0" cdnmidimgurl="3057020100044b304902010002049c5da34302032e6c63020436ab016f020468899e6a042436326664336366632d363532632d346536632d613537372d356666363530313730376665020405292a010201000405004c54a300" length="49204" cdnbigimgurl="3057020100044b304902010002049c5da34302032e6c63020436ab016f020468899e6a042436326664336366632d363532632d346536632d613537372d356666363530313730376665020405292a010201000405004c54a300" hdlength="444315" md5="ed2450a7d1fe89e4a80c45ccc5e9e57a" hevc_mid_size="49204" originsourcemd5="ed2450a7d1fe89e4a80c45ccc5e9e57a">\n\t\t<secHashInfoBase64 />\n\t\t<live>\n\t\t\t<duration>0</duration>\n\t\t\t<size>0</size>\n\t\t\t<md5 />\n\t\t\t<fileid />\n\t\t\t<hdsize>0</hdsize>\n\t\t\t<hdmd5 />\n\t\t\t<hdfileid />\n\t\t\t<stillimagetimems>0</stillimagetimems>\n\t\t</live>\n\t</img>\n\t<appinfo>\n\t\t<appid>wxf0a80d0ac2e82aa7</appid>\n\t\t<version>65</version>\n\t\t<appname>QQ分享</appname>\n\t\t<isforceupdate>0</isforceupdate>\n\t\t<messageaction />\n\t\t<messageext />\n\t\t<mediatagname />\n\t</appinfo>\n\t<platform_signature />\n\t<imgdatahash />\n\t<ImgSourceInfo>\n\t\t<ImgSourceUrl />\n\t\t<BizType>0</BizType>\n\t</ImgSourceInfo>\n</msg>\n'}, 'Status': 3, 'ImgStatus': 2, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1753849450, 'MsgSource': '<msgsource>\n\t<alnode>\n\t\t<fr>2</fr>\n\t</alnode>\n\t<sec_msg_node>\n\t\t<uuid>9be10b7018352faee921d74c31e98e5d_</uuid>\n\t\t<risk-file-flag />\n\t\t<risk-file-md5-list />\n\t</sec_msg_node>\n\t<silence>0</silence>\n\t<membercount>73</membercount>\n\t<signature>N0_V1_H1/YrGDV|v1_IlpUQrvv</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'PushContent': '她在群聊中发了一张图片', 'NewMsgId': 1568725932952637412, 'MsgSeq': 871411597}
2025-07-30 12:24:01 | INFO | 收到图片消息: 消息ID:22743193 来自:***********@chatroom 发送人:wxid_srknxij3jka022 XML:<?xml version="1.0"?><msg><img aeskey="b071f35f838962b49524a5ab0529a977" encryver="1" cdnthumbaeskey="b071f35f838962b49524a5ab0529a977" cdnthumburl="3057020100044b304902010002049c5da34302032e6c63020436ab016f020468899e6a042436326664336366632d363532632d346536632d613537372d356666363530313730376665020405292a010201000405004c54a300" cdnthumblength="5138" cdnthumbheight="120" cdnthumbwidth="120" cdnmidheight="0" cdnmidwidth="0" cdnhdheight="0" cdnhdwidth="0" cdnmidimgurl="3057020100044b304902010002049c5da34302032e6c63020436ab016f020468899e6a042436326664336366632d363532632d346536632d613537372d356666363530313730376665020405292a010201000405004c54a300" length="49204" cdnbigimgurl="3057020100044b304902010002049c5da34302032e6c63020436ab016f020468899e6a042436326664336366632d363532632d346536632d613537372d356666363530313730376665020405292a010201000405004c54a300" hdlength="444315" md5="ed2450a7d1fe89e4a80c45ccc5e9e57a" hevc_mid_size="49204" originsourcemd5="ed2450a7d1fe89e4a80c45ccc5e9e57a"><secHashInfoBase64 /><live><duration>0</duration><size>0</size><md5 /><fileid /><hdsize>0</hdsize><hdmd5 /><hdfileid /><stillimagetimems>0</stillimagetimems></live></img><appinfo><appid>wxf0a80d0ac2e82aa7</appid><version>65</version><appname>QQ分享</appname><isforceupdate>0</isforceupdate><messageaction /><messageext /><mediatagname /></appinfo><platform_signature /><imgdatahash /><ImgSourceInfo><ImgSourceUrl /><BizType>0</BizType></ImgSourceInfo></msg>
2025-07-30 12:24:01 | INFO | [ImageEcho] 保存图片信息成功，当前群 ***********@chatroom 已存储 5 张图片
2025-07-30 12:24:01 | INFO | [TimerTask] 缓存图片消息: 22743193
2025-07-30 12:24:01 | DEBUG | 收到消息: {'MsgId': 1043387111, 'FromUserName': {'string': '***********@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 3, 'Content': {'string': 'wxid_srknxij3jka022:\n<?xml version="1.0"?>\n<msg>\n\t<img aeskey="e0bb50517cf7d0cf746a0e010b9873c8" encryver="1" cdnthumbaeskey="e0bb50517cf7d0cf746a0e010b9873c8" cdnthumburl="3057020100044b304902010002049c5da34302032e6c63020436ab016f020468899e6b042464633661623864612d613132342d343661352d393933342d396535336666356133383736020405292a010201000405004c57c300" cdnthumblength="6307" cdnthumbheight="120" cdnthumbwidth="120" cdnmidheight="0" cdnmidwidth="0" cdnhdheight="0" cdnhdwidth="0" cdnmidimgurl="3057020100044b304902010002049c5da34302032e6c63020436ab016f020468899e6b042464633661623864612d613132342d343661352d393933342d396535336666356133383736020405292a010201000405004c57c300" length="48681" cdnbigimgurl="3057020100044b304902010002049c5da34302032e6c63020436ab016f020468899e6b042464633661623864612d613132342d343661352d393933342d396535336666356133383736020405292a010201000405004c57c300" hdlength="439623" md5="733a6bf0d93fb30b5d0805f939eb811b" hevc_mid_size="48681" originsourcemd5="733a6bf0d93fb30b5d0805f939eb811b">\n\t\t<secHashInfoBase64 />\n\t\t<live>\n\t\t\t<duration>0</duration>\n\t\t\t<size>0</size>\n\t\t\t<md5 />\n\t\t\t<fileid />\n\t\t\t<hdsize>0</hdsize>\n\t\t\t<hdmd5 />\n\t\t\t<hdfileid />\n\t\t\t<stillimagetimems>0</stillimagetimems>\n\t\t</live>\n\t</img>\n\t<appinfo>\n\t\t<appid>wx0a022393a4dd43b2</appid>\n\t\t<version>5</version>\n\t\t<appname>小爱同学</appname>\n\t\t<isforceupdate>0</isforceupdate>\n\t\t<messageaction />\n\t\t<messageext />\n\t\t<mediatagname />\n\t</appinfo>\n\t<platform_signature />\n\t<imgdatahash />\n\t<ImgSourceInfo>\n\t\t<ImgSourceUrl />\n\t\t<BizType>0</BizType>\n\t</ImgSourceInfo>\n</msg>\n'}, 'Status': 3, 'ImgStatus': 2, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1753849451, 'MsgSource': '<msgsource>\n\t<alnode>\n\t\t<fr>2</fr>\n\t</alnode>\n\t<sec_msg_node>\n\t\t<uuid>382083b5c3e79b63a39b9fdf2a19feb2_</uuid>\n\t\t<risk-file-flag />\n\t\t<risk-file-md5-list />\n\t</sec_msg_node>\n\t<silence>0</silence>\n\t<membercount>73</membercount>\n\t<signature>N0_V1_wMRQUxgT|v1_ZYrbIuoJ</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'PushContent': '她在群聊中发了一张图片', 'NewMsgId': 7761349957457918999, 'MsgSeq': 871411598}
2025-07-30 12:24:01 | INFO | 收到图片消息: 消息ID:1043387111 来自:***********@chatroom 发送人:wxid_srknxij3jka022 XML:<?xml version="1.0"?><msg><img aeskey="e0bb50517cf7d0cf746a0e010b9873c8" encryver="1" cdnthumbaeskey="e0bb50517cf7d0cf746a0e010b9873c8" cdnthumburl="3057020100044b304902010002049c5da34302032e6c63020436ab016f020468899e6b042464633661623864612d613132342d343661352d393933342d396535336666356133383736020405292a010201000405004c57c300" cdnthumblength="6307" cdnthumbheight="120" cdnthumbwidth="120" cdnmidheight="0" cdnmidwidth="0" cdnhdheight="0" cdnhdwidth="0" cdnmidimgurl="3057020100044b304902010002049c5da34302032e6c63020436ab016f020468899e6b042464633661623864612d613132342d343661352d393933342d396535336666356133383736020405292a010201000405004c57c300" length="48681" cdnbigimgurl="3057020100044b304902010002049c5da34302032e6c63020436ab016f020468899e6b042464633661623864612d613132342d343661352d393933342d396535336666356133383736020405292a010201000405004c57c300" hdlength="439623" md5="733a6bf0d93fb30b5d0805f939eb811b" hevc_mid_size="48681" originsourcemd5="733a6bf0d93fb30b5d0805f939eb811b"><secHashInfoBase64 /><live><duration>0</duration><size>0</size><md5 /><fileid /><hdsize>0</hdsize><hdmd5 /><hdfileid /><stillimagetimems>0</stillimagetimems></live></img><appinfo><appid>wx0a022393a4dd43b2</appid><version>5</version><appname>小爱同学</appname><isforceupdate>0</isforceupdate><messageaction /><messageext /><mediatagname /></appinfo><platform_signature /><imgdatahash /><ImgSourceInfo><ImgSourceUrl /><BizType>0</BizType></ImgSourceInfo></msg>
2025-07-30 12:24:02 | INFO | [ImageEcho] 保存图片信息成功，当前群 ***********@chatroom 已存储 5 张图片
2025-07-30 12:24:02 | INFO | [TimerTask] 缓存图片消息: 1043387111
2025-07-30 12:24:30 | DEBUG | 收到消息: {'MsgId': 2028209138, 'FromUserName': {'string': '27852221909@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 1, 'Content': {'string': 'wxid_bmzp9achod6922:\n还送一个漂亮的大坐骑'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1753849482, 'MsgSource': '<msgsource>\n\t<bizflag>0</bizflag>\n\t<pua>1</pua>\n\t<eggIncluded>1</eggIncluded>\n\t<silence>1</silence>\n\t<membercount>145</membercount>\n\t<signature>N0_V1_f96pdeVv|v1_7TgyAlid</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'NewMsgId': 4766534891062932403, 'MsgSeq': 871411599}
2025-07-30 12:24:30 | INFO | 收到文本消息: 消息ID:2028209138 来自:27852221909@chatroom 发送人:wxid_bmzp9achod6922 @:[] 内容:还送一个漂亮的大坐骑
2025-07-30 12:24:30 | DEBUG | 处理消息内容: '还送一个漂亮的大坐骑'
2025-07-30 12:24:30 | DEBUG | 消息内容 '还送一个漂亮的大坐骑' 不匹配任何命令，忽略
2025-07-30 12:24:33 | DEBUG | 收到消息: {'MsgId': 2061526688, 'FromUserName': {'string': '27852221909@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 3, 'Content': {'string': 'wxid_2530z9t0joek22:\n<?xml version="1.0"?>\n<msg>\n\t<img aeskey="da938e99149a2431c99409ad8c98da08" encryver="1" cdnthumbaeskey="da938e99149a2431c99409ad8c98da08" cdnthumburl="3057020100044b30490201000204f060aea202032f802902049af73db7020468899e8d042464613031306339392d663235332d346332642d626361632d353033353764626631633139020405290a020201000405004c57c100" cdnthumblength="4799" cdnthumbheight="196" cdnthumbwidth="432" cdnmidheight="0" cdnmidwidth="0" cdnhdheight="0" cdnhdwidth="0" cdnmidimgurl="3057020100044b30490201000204f060aea202032f802902049af73db7020468899e8d042464613031306339392d663235332d346332642d626361632d353033353764626631633139020405290a020201000405004c57c100" length="916383" md5="c37b71e31f3928cde431fd6bbc132300" hevc_mid_size="83646" originsourcemd5="e6322fcec6420bcf34b7e0d4ad3138fa">\n\t\t<secHashInfoBase64>eyJwaGFzaCI6ImI0ZjA0NDMwMDQwMDAwMDAiLCJwZHFIYXNoIjoiNjdiMDcwZDhjY2Q4YzVmNjQz\nNWExNzcyMmY5OTM3NjIyZjM2YTY4ZDg5YWQyNjMxZTEzNTg5YjliOGU1MTM1NiJ9\n</secHashInfoBase64>\n\t\t<live>\n\t\t\t<duration>0</duration>\n\t\t\t<size>0</size>\n\t\t\t<md5 />\n\t\t\t<fileid />\n\t\t\t<hdsize>0</hdsize>\n\t\t\t<hdmd5 />\n\t\t\t<hdfileid />\n\t\t\t<stillimagetimems>0</stillimagetimems>\n\t\t</live>\n\t</img>\n\t<platform_signature />\n\t<imgdatahash />\n\t<ImgSourceInfo>\n\t\t<ImgSourceUrl />\n\t\t<BizType>0</BizType>\n\t</ImgSourceInfo>\n</msg>\n'}, 'Status': 3, 'ImgStatus': 2, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1753849485, 'MsgSource': '<msgsource>\n\t<sec_msg_node>\n\t\t<uuid>cf35c816fd7e7347bcd68e25e842abc0_</uuid>\n\t\t<risk-file-flag />\n\t\t<risk-file-md5-list />\n\t</sec_msg_node>\n\t<silence>1</silence>\n\t<membercount>145</membercount>\n\t<signature>N0_V1_DdvU9Tr6|v1_FXQ0ODBc</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'NewMsgId': 7272009725206363595, 'MsgSeq': 871411600}
2025-07-30 12:24:33 | INFO | 收到图片消息: 消息ID:2061526688 来自:27852221909@chatroom 发送人:wxid_2530z9t0joek22 XML:<?xml version="1.0"?><msg><img aeskey="da938e99149a2431c99409ad8c98da08" encryver="1" cdnthumbaeskey="da938e99149a2431c99409ad8c98da08" cdnthumburl="3057020100044b30490201000204f060aea202032f802902049af73db7020468899e8d042464613031306339392d663235332d346332642d626361632d353033353764626631633139020405290a020201000405004c57c100" cdnthumblength="4799" cdnthumbheight="196" cdnthumbwidth="432" cdnmidheight="0" cdnmidwidth="0" cdnhdheight="0" cdnhdwidth="0" cdnmidimgurl="3057020100044b30490201000204f060aea202032f802902049af73db7020468899e8d042464613031306339392d663235332d346332642d626361632d353033353764626631633139020405290a020201000405004c57c100" length="916383" md5="c37b71e31f3928cde431fd6bbc132300" hevc_mid_size="83646" originsourcemd5="e6322fcec6420bcf34b7e0d4ad3138fa"><secHashInfoBase64>eyJwaGFzaCI6ImI0ZjA0NDMwMDQwMDAwMDAiLCJwZHFIYXNoIjoiNjdiMDcwZDhjY2Q4YzVmNjQzNWExNzcyMmY5OTM3NjIyZjM2YTY4ZDg5YWQyNjMxZTEzNTg5YjliOGU1MTM1NiJ9</secHashInfoBase64><live><duration>0</duration><size>0</size><md5 /><fileid /><hdsize>0</hdsize><hdmd5 /><hdfileid /><stillimagetimems>0</stillimagetimems></live></img><platform_signature /><imgdatahash /><ImgSourceInfo><ImgSourceUrl /><BizType>0</BizType></ImgSourceInfo></msg>
2025-07-30 12:24:33 | INFO | [ImageEcho] 保存图片信息成功，当前群 27852221909@chatroom 已存储 5 张图片
2025-07-30 12:24:33 | INFO | [TimerTask] 缓存图片消息: 2061526688
2025-07-30 12:24:34 | DEBUG | 收到消息: {'MsgId': 1413848734, 'FromUserName': {'string': '27852221909@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 1, 'Content': {'string': 'wxid_bmzp9achod6922:\n还有几百钻石'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1753849486, 'MsgSource': '<msgsource>\n\t<bizflag>0</bizflag>\n\t<pua>1</pua>\n\t<eggIncluded>1</eggIncluded>\n\t<silence>1</silence>\n\t<membercount>145</membercount>\n\t<signature>N0_V1_DKlqL7Xu|v1_C/O0rEnQ</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'NewMsgId': 3277451992500231320, 'MsgSeq': 871411601}
2025-07-30 12:24:34 | INFO | 收到文本消息: 消息ID:1413848734 来自:27852221909@chatroom 发送人:wxid_bmzp9achod6922 @:[] 内容:还有几百钻石
2025-07-30 12:24:34 | DEBUG | 处理消息内容: '还有几百钻石'
2025-07-30 12:24:34 | DEBUG | 消息内容 '还有几百钻石' 不匹配任何命令，忽略
2025-07-30 12:24:38 | DEBUG | 收到消息: {'MsgId': 221607167, 'FromUserName': {'string': '27852221909@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 1, 'Content': {'string': 'wxid_2530z9t0joek22:\n这动作可真是'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1753849490, 'MsgSource': '<msgsource>\n\t<pua>1</pua>\n\t<eggIncluded>1</eggIncluded>\n\t<silence>1</silence>\n\t<membercount>145</membercount>\n\t<signature>N0_V1_98mRcU3f|v1_sHg6AK0n</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'NewMsgId': 1022399475647152919, 'MsgSeq': 871411602}
2025-07-30 12:24:38 | INFO | 收到文本消息: 消息ID:221607167 来自:27852221909@chatroom 发送人:wxid_2530z9t0joek22 @:[] 内容:这动作可真是
2025-07-30 12:24:38 | DEBUG | 处理消息内容: '这动作可真是'
2025-07-30 12:24:38 | DEBUG | 消息内容 '这动作可真是' 不匹配任何命令，忽略
2025-07-30 12:24:41 | DEBUG | 收到消息: {'MsgId': 359860346, 'FromUserName': {'string': '27852221909@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 1, 'Content': {'string': 'wxid_bmzp9achod6922:\n还有好多道具'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1753849493, 'MsgSource': '<msgsource>\n\t<bizflag>0</bizflag>\n\t<pua>1</pua>\n\t<eggIncluded>1</eggIncluded>\n\t<silence>1</silence>\n\t<membercount>145</membercount>\n\t<signature>N0_V1_VBH8TXTb|v1_QZkQ9t5d</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'NewMsgId': 5518508912173223335, 'MsgSeq': 871411603}
2025-07-30 12:24:41 | INFO | 收到文本消息: 消息ID:359860346 来自:27852221909@chatroom 发送人:wxid_bmzp9achod6922 @:[] 内容:还有好多道具
2025-07-30 12:24:41 | DEBUG | 处理消息内容: '还有好多道具'
2025-07-30 12:24:41 | DEBUG | 消息内容 '还有好多道具' 不匹配任何命令，忽略
2025-07-30 12:24:43 | DEBUG | 收到消息: {'MsgId': 963838934, 'FromUserName': {'string': '***********@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 49, 'Content': {'string': 'wxid_jegyk4i3v7zg22:\n<?xml version="1.0"?>\n<msg>\n\t<appmsg appid="" sdkver="0">\n\t\t<title>一毛五</title>\n\t\t<type>57</type>\n\t\t<appattach>\n\t\t\t<cdnthumbaeskey />\n\t\t\t<aeskey></aeskey>\n\t\t</appattach>\n\t\t<refermsg>\n\t\t\t<type>1</type>\n\t\t\t<svrid>630953519351360670</svrid>\n\t\t\t<fromusr>***********@chatroom</fromusr>\n\t\t\t<chatusr>xiaomaochong</chatusr>\n\t\t\t<displayname>小爱</displayname>\n\t\t\t<content>画一次多少钱，一会欠费了[抠鼻][皱眉]</content>\n\t\t\t<msgsource>&lt;msgsource&gt;&lt;sequence_id&gt;836242892&lt;/sequence_id&gt;\n\t&lt;pua&gt;1&lt;/pua&gt;\n\t&lt;silence&gt;1&lt;/silence&gt;\n\t&lt;membercount&gt;73&lt;/membercount&gt;\n\t&lt;signature&gt;N0_V1_h6HTrmWd|v1_Jb7CoItO&lt;/signature&gt;\n\t&lt;tmp_node&gt;\n\t\t&lt;publisher-id&gt;&lt;/publisher-id&gt;\n\t&lt;/tmp_node&gt;\n&lt;/msgsource&gt;\n</msgsource>\n\t\t\t<createtime>1753849182</createtime>\n\t\t</refermsg>\n\t</appmsg>\n\t<fromusername>wxid_jegyk4i3v7zg22</fromusername>\n\t<scene>0</scene>\n\t<appinfo>\n\t\t<version>1</version>\n\t\t<appname />\n\t</appinfo>\n\t<commenturl />\n</msg>\n'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1753849495, 'MsgSource': '<msgsource>\n\t<bizflag>0</bizflag>\n\t<pua>1</pua>\n\t<eggIncluded>1</eggIncluded>\n\t<sec_msg_node>\n\t\t<uuid>0911c682cc0f674bc85bb73050f932a1_</uuid>\n\t\t<risk-file-flag />\n\t\t<risk-file-md5-list />\n\t</sec_msg_node>\n\t<silence>0</silence>\n\t<membercount>73</membercount>\n\t<signature>N0_V1_yuUIeMz2|v1_q0DJqjrc</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'PushContent': '阿尼亚与她 : 一毛五', 'NewMsgId': 2185639790013103346, 'MsgSeq': 871411604}
2025-07-30 12:24:43 | DEBUG | 从群聊消息中提取发送者: wxid_jegyk4i3v7zg22
2025-07-30 12:24:43 | DEBUG | 使用已解析的XML处理引用消息
2025-07-30 12:24:43 | INFO | 收到引用消息: 消息ID:963838934 来自:***********@chatroom 发送人:wxid_jegyk4i3v7zg22 内容:一毛五 引用类型:1
2025-07-30 12:24:44 | INFO | [DouBaoImageToImage] 收到引用消息: 一毛五
2025-07-30 12:24:44 | INFO | [TimerTask] 收到引用消息调试信息:
2025-07-30 12:24:44 | INFO |   - 消息内容: 一毛五
2025-07-30 12:24:44 | INFO |   - 群组ID: ***********@chatroom
2025-07-30 12:24:44 | INFO |   - 发送人: wxid_jegyk4i3v7zg22
2025-07-30 12:24:44 | INFO |   - 引用信息: {'MsgType': 1, 'Content': '画一次多少钱，一会欠费了[抠鼻][皱眉]', 'Msgid': '630953519351360670', 'NewMsgId': '630953519351360670', 'ToWxid': 'wxid_4usgcju5ey9q29', 'FromWxid': '***********@chatroom', 'Nickname': '小爱', 'MsgSource': '<msgsource><sequence_id>836242892</sequence_id>\n\t<pua>1</pua>\n\t<silence>1</silence>\n\t<membercount>73</membercount>\n\t<signature>N0_V1_h6HTrmWd|v1_Jb7CoItO</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'Createtime': '1753849182', 'SenderWxid': 'wxid_jegyk4i3v7zg22'}
2025-07-30 12:24:44 | INFO |   - 引用消息ID: 
2025-07-30 12:24:44 | INFO |   - 引用消息类型: 
2025-07-30 12:24:44 | INFO |   - 引用消息内容: 画一次多少钱，一会欠费了[抠鼻][皱眉]
2025-07-30 12:24:44 | INFO |   - 引用消息发送人: wxid_jegyk4i3v7zg22
2025-07-30 12:24:47 | DEBUG | 收到消息: {'MsgId': 1136235312, 'FromUserName': {'string': '***********@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 47, 'Content': {'string': 'wxid_jegyk4i3v7zg22:\n<msg><emoji fromusername="wxid_jegyk4i3v7zg22" tousername="***********@chatroom" type="2" idbuffer="media:0_0" md5="d59b91247a40ccf864db95dafb17ce14" len="3563447" productid="" androidmd5="d59b91247a40ccf864db95dafb17ce14" androidlen="3563447" s60v3md5="d59b91247a40ccf864db95dafb17ce14" s60v3len="3563447" s60v5md5="d59b91247a40ccf864db95dafb17ce14" s60v5len="3563447" cdnurl="http://vweixinf.tc.qq.com/110/20402/stodownload?m=d59b91247a40ccf864db95dafb17ce14&amp;filekey=30440201010430302e02016e0402535a042064353962393132343761343063636638363464623935646166623137636531340203365fb7040d00000004627466730000000132&amp;hy=SZ&amp;storeid=2680294de000a132bcdcbadb00000006e01004fb2535a24469bc1e763314c6&amp;ef=1&amp;bizid=1022" designerid="" thumburl="" encrypturl="http://vweixinf.tc.qq.com/110/20402/stodownload?m=4420e6a7ee3c80b23f76a37fd9ee7182&amp;filekey=30440201010430302e02016e0402535a042034343230653661376565336338306232336637366133376664396565373138320203365fc0040d00000004627466730000000132&amp;hy=SZ&amp;storeid=2680294de000dcfe4cdcbadb00000006e02004fb2535a24469bc1e763314e1&amp;ef=2&amp;bizid=1022" aeskey="77fca316fc9647ceb56c2591ef7989c5" externurl="http://vweixinf.tc.qq.com/110/20403/stodownload?m=a97d5283b88cd0380387252d3b26ae75&amp;filekey=30440201010430302e02016e0402535a04206139376435323833623838636430333830333837323532643362323661653735020302ff70040d00000004627466730000000132&amp;hy=SZ&amp;storeid=2680294df000241cfcdcbadb00000006e03004fb3535a24469bc1e76331503&amp;ef=3&amp;bizid=1022" externmd5="6b601c880138b6501626e3323ee1a3b3" width="299" height="300" tpurl="" tpauthkey="" attachedtext="" attachedtextcolor="" lensid="" emojiattr="" linkid="" desc=""></emoji><gameext type="0" content="0"></gameext><extcommoninfo></extcommoninfo></msg>'}, 'Status': 3, 'ImgStatus': 2, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1753849499, 'MsgSource': '<msgsource>\n\t<silence>0</silence>\n\t<membercount>73</membercount>\n\t<signature>N0_V1_YHVVDcMi|v1_1lTpcAuD</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'PushContent': '阿尼亚与她在群聊中发了一个表情', 'NewMsgId': 3553518537685612904, 'MsgSeq': 871411605}
2025-07-30 12:24:47 | INFO | 收到表情消息: 消息ID:1136235312 来自:***********@chatroom 发送人:wxid_jegyk4i3v7zg22 MD5:d59b91247a40ccf864db95dafb17ce14 大小:3563447
2025-07-30 12:24:47 | DEBUG | [DoubaoImageRecognition] 缓存表情消息: 3553518537685612904
2025-07-30 12:24:50 | DEBUG | 收到消息: {'MsgId': 1022451641, 'FromUserName': {'string': '27852221909@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 1, 'Content': {'string': 'wxid_bmzp9achod6922:\n衣服还好看'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1753849502, 'MsgSource': '<msgsource>\n\t<bizflag>0</bizflag>\n\t<pua>1</pua>\n\t<eggIncluded>1</eggIncluded>\n\t<silence>1</silence>\n\t<membercount>145</membercount>\n\t<signature>N0_V1_B/SG2Z/J|v1_Hh7PHigl</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'NewMsgId': 6288336373397836717, 'MsgSeq': 871411606}
2025-07-30 12:24:50 | INFO | 收到文本消息: 消息ID:1022451641 来自:27852221909@chatroom 发送人:wxid_bmzp9achod6922 @:[] 内容:衣服还好看
2025-07-30 12:24:50 | DEBUG | 处理消息内容: '衣服还好看'
2025-07-30 12:24:50 | DEBUG | 消息内容 '衣服还好看' 不匹配任何命令，忽略
2025-07-30 12:25:44 | DEBUG | 收到消息: {'MsgId': 1940190847, 'FromUserName': {'string': '***********@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 1, 'Content': {'string': 'wxid_jegyk4i3v7zg22:\n比短信还贵'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1753849557, 'MsgSource': '<msgsource>\n\t<bizflag>0</bizflag>\n\t<pua>1</pua>\n\t<eggIncluded>1</eggIncluded>\n\t<silence>0</silence>\n\t<membercount>73</membercount>\n\t<signature>N0_V1_9LxZZw2q|v1_6xQGzadk</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'PushContent': '阿尼亚与她 : 比短信还贵', 'NewMsgId': 8031084368170541315, 'MsgSeq': 871411607}
2025-07-30 12:25:44 | INFO | 收到文本消息: 消息ID:1940190847 来自:***********@chatroom 发送人:wxid_jegyk4i3v7zg22 @:[] 内容:比短信还贵
2025-07-30 12:25:45 | DEBUG | 处理消息内容: '比短信还贵'
2025-07-30 12:25:45 | DEBUG | 消息内容 '比短信还贵' 不匹配任何命令，忽略
2025-07-30 12:25:49 | DEBUG | 收到消息: {'MsgId': 268629046, 'FromUserName': {'string': '***********@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 47, 'Content': {'string': 'wxid_jegyk4i3v7zg22:\n<msg><emoji fromusername="wxid_jegyk4i3v7zg22" tousername="***********@chatroom" type="2" idbuffer="media:0_0" md5="659fe688b2617a2a0a897355f445d249" len="3439809" productid="" androidmd5="659fe688b2617a2a0a897355f445d249" androidlen="3439809" s60v3md5="659fe688b2617a2a0a897355f445d249" s60v3len="3439809" s60v5md5="659fe688b2617a2a0a897355f445d249" s60v5len="3439809" cdnurl="http://vweixinf.tc.qq.com/110/20401/stodownload?m=659fe688b2617a2a0a897355f445d249&amp;filekey=30440201010430302e02016e04025348042036353966653638386232363137613261306138393733353566343435643234390203347cc1040d00000004627466730000000132&amp;hy=SH&amp;storeid=268425179000a9ba98992fb0c0000006e01004fb153481f9320315693c62a8&amp;ef=1&amp;bizid=1022" designerid="" thumburl="" encrypturl="http://vweixinf.tc.qq.com/110/20402/stodownload?m=cabfbb9548bf28e17343540a2e758e5a&amp;filekey=30440201010430302e02016e04025348042063616266626239353438626632386531373334333534306132653735386535610203347cd0040d00000004627466730000000132&amp;hy=SH&amp;storeid=268425179000e8c5a8992fb0c0000006e02004fb253481f9320315693c6305&amp;ef=2&amp;bizid=1022" aeskey="65bb2cdd579944cd95044118968691f7" externurl="http://vweixinf.tc.qq.com/110/20403/stodownload?m=571525991d985a538e815cb436322676&amp;filekey=30440201010430302e02016e04025348042035373135323539393164393835613533386538313563623433363332323637360203015810040d00000004627466730000000132&amp;hy=SH&amp;storeid=26842517a00033ba78992fb0c0000006e03004fb353481f9320315693c6345&amp;ef=3&amp;bizid=1022" externmd5="cdcd4d764bcd051b40ece292f4040f85" width="640" height="640" tpurl="" tpauthkey="" attachedtext="" attachedtextcolor="" lensid="" emojiattr="" linkid="" desc=""></emoji><gameext type="0" content="0"></gameext><extcommoninfo></extcommoninfo></msg>'}, 'Status': 3, 'ImgStatus': 2, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1753849561, 'MsgSource': '<msgsource>\n\t<silence>0</silence>\n\t<membercount>73</membercount>\n\t<signature>N0_V1_tQ09a2ef|v1_Ew69WEqh</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'PushContent': '阿尼亚与她在群聊中发了一个表情', 'NewMsgId': 4004777365009806460, 'MsgSeq': 871411608}
2025-07-30 12:25:49 | INFO | 收到表情消息: 消息ID:268629046 来自:***********@chatroom 发送人:wxid_jegyk4i3v7zg22 MD5:659fe688b2617a2a0a897355f445d249 大小:3439809
2025-07-30 12:25:49 | DEBUG | [DoubaoImageRecognition] 缓存表情消息: 4004777365009806460
2025-07-30 12:27:57 | DEBUG | 收到消息: {'MsgId': 1127953603, 'FromUserName': {'string': '27852221909@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 47, 'Content': {'string': 'wxid_2530z9t0joek22:\n<msg><emoji fromusername = "wxid_2530z9t0joek22" tousername = "27852221909@chatroom" type="2" idbuffer="media:0_0" md5="228d134484fcf4cc1074a1492c72691b" len = "1497794" productid="" androidmd5="228d134484fcf4cc1074a1492c72691b" androidlen="1497794" s60v3md5 = "228d134484fcf4cc1074a1492c72691b" s60v3len="1497794" s60v5md5 = "228d134484fcf4cc1074a1492c72691b" s60v5len="1497794" cdnurl = "http://vweixinf.tc.qq.com/110/20401/stodownload?m=228d134484fcf4cc1074a1492c72691b&amp;filekey=30440201010430302e02016e0402534804203232386431333434383466636634636331303734613134393263373236393162020316dac2040d00000004627466730000000132&amp;hy=SH&amp;storeid=2680eff2d000c87f90a0d63c80000006e01004fb1534819f3b031568bc3116&amp;ef=1&amp;bizid=1022" designerid = "" thumburl = "" encrypturl = "http://vweixinf.tc.qq.com/110/20402/stodownload?m=75fc423fbd91e4190dc13192b07b04de&amp;filekey=30440201010430302e02016e0402534804203735666334323366626439316534313930646331333139326230376230346465020316dad0040d00000004627466730000000132&amp;hy=SH&amp;storeid=2680eff2d000ed2540a0d63c80000006e02004fb2534819f3b031568bc313f&amp;ef=2&amp;bizid=1022" aeskey= "970a1f92107742bfa6a00004214eb722" externurl = "http://vweixinf.tc.qq.com/110/20403/stodownload?m=29c945f9dbdc1c7ea317b80533b22e40&amp;filekey=30440201010430302e02016e04025348042032396339343566396462646331633765613331376238303533336232326534300203030700040d00000004627466730000000132&amp;hy=SH&amp;storeid=2680eff2e0001b70c0a0d63c80000006e03004fb3534819f3b031568bc315d&amp;ef=3&amp;bizid=1022" externmd5 = "c1e81be394c0cc15b6190a87475031fb" width= "225" height= "240" tpurl= "" tpauthkey= "" attachedtext= "" attachedtextcolor= "" lensid= "" emojiattr= "" linkid= "" desc= "" ></emoji>  </msg>'}, 'Status': 3, 'ImgStatus': 2, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1753849690, 'MsgSource': '<msgsource>\n\t<silence>1</silence>\n\t<membercount>145</membercount>\n\t<signature>N0_V1_1S1QKX70|v1_Aw2KVWvr</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'NewMsgId': 6596900939947581329, 'MsgSeq': 871411609}
2025-07-30 12:27:57 | INFO | 收到表情消息: 消息ID:1127953603 来自:27852221909@chatroom 发送人:wxid_2530z9t0joek22 MD5:228d134484fcf4cc1074a1492c72691b 大小:1497794
2025-07-30 12:27:57 | DEBUG | [DoubaoImageRecognition] 缓存表情消息: 6596900939947581329
2025-07-30 12:33:03 | DEBUG | 收到消息: {'MsgId': 2023899047, 'FromUserName': {'string': '27852221909@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 49, 'Content': {'string': 'wxid_ikxxrwasicud11:\n<?xml version="1.0"?>\n<msg>\n\t<appmsg appid="" sdkver="0">\n\t\t<title>没图鉴</title>\n\t\t<des />\n\t\t<action />\n\t\t<type>57</type>\n\t\t<showtype>0</showtype>\n\t\t<soundtype>0</soundtype>\n\t\t<mediatagname />\n\t\t<messageext />\n\t\t<messageaction />\n\t\t<content />\n\t\t<contentattr>0</contentattr>\n\t\t<url />\n\t\t<lowurl />\n\t\t<dataurl />\n\t\t<lowdataurl />\n\t\t<songalbumurl />\n\t\t<songlyric />\n\t\t<template_id />\n\t\t<appattach>\n\t\t\t<totallen>0</totallen>\n\t\t\t<attachid />\n\t\t\t<emoticonmd5 />\n\t\t\t<fileext />\n\t\t\t<aeskey />\n\t\t</appattach>\n\t\t<extinfo />\n\t\t<sourceusername />\n\t\t<sourcedisplayname />\n\t\t<thumburl />\n\t\t<md5 />\n\t\t<statextstr />\n\t\t<refermsg>\n\t\t\t<type>3</type>\n\t\t\t<svrid>7272009725206363595</svrid>\n\t\t\t<fromusr>27852221909@chatroom</fromusr>\n\t\t\t<chatusr>wxid_2530z9t0joek22</chatusr>\n\t\t\t<displayname>慕ؓ悦ؓ˒</displayname>\n\t\t\t<msgsource>&lt;msgsource&gt;\n\t&lt;sec_msg_node&gt;\n\t\t&lt;uuid&gt;cf35c816fd7e7347bcd68e25e842abc0_&lt;/uuid&gt;\n\t\t&lt;risk-file-flag /&gt;\n\t\t&lt;risk-file-md5-list /&gt;\n\t&lt;/sec_msg_node&gt;\n\t&lt;imgmsg_pd cdnmidimgurl_size="83646" cdnmidimgurl_pd_pri="30" cdnmidimgurl_pd="0" /&gt;\n\t&lt;silence&gt;1&lt;/silence&gt;\n\t&lt;membercount&gt;145&lt;/membercount&gt;\n\t&lt;signature&gt;N0_V1_S8NSjAqh|v1_Mug/0esO&lt;/signature&gt;\n\t&lt;tmp_node&gt;\n\t\t&lt;publisher-id&gt;&lt;/publisher-id&gt;\n\t&lt;/tmp_node&gt;\n&lt;/msgsource&gt;\n</msgsource>\n\t\t\t<createtime>1753849485</createtime>\n\t\t\t<content>wxid_2530z9t0joek22:\n&lt;?xml version="1.0"?&gt;\n&lt;msg&gt;\n\t&lt;img aeskey="da938e99149a2431c99409ad8c98da08" encryver="1" cdnthumbaeskey="da938e99149a2431c99409ad8c98da08" cdnthumburl="3057020100044b30490201000204f060aea202032f802902049af73db7020468899e8d042464613031306339392d663235332d346332642d626361632d353033353764626631633139020405290a020201000405004c57c100" cdnthumblength="4799" cdnthumbheight="196" cdnthumbwidth="432" cdnmidheight="0" cdnmidwidth="0" cdnhdheight="0" cdnhdwidth="0" cdnmidimgurl="3057020100044b30490201000204f060aea202032f802902049af73db7020468899e8d042464613031306339392d663235332d346332642d626361632d353033353764626631633139020405290a020201000405004c57c100" length="916383" md5="c37b71e31f3928cde431fd6bbc132300" hevc_mid_size="83646" originsourcemd5="e6322fcec6420bcf34b7e0d4ad3138fa"&gt;\n\t\t&lt;secHashInfoBase64&gt;eyJwaGFzaCI6ImI0ZjA0NDMwMDQwMDAwMDAiLCJwZHFIYXNoIjoiNjdiMDcwZDhjY2Q4YzVmNjQz\nNWExNzcyMmY5OTM3NjIyZjM2YTY4ZDg5YWQyNjMxZTEzNTg5YjliOGU1MTM1NiJ9\n&lt;/secHashInfoBase64&gt;\n\t\t&lt;live&gt;\n\t\t\t&lt;duration&gt;0&lt;/duration&gt;\n\t\t\t&lt;size&gt;0&lt;/size&gt;\n\t\t\t&lt;md5 /&gt;\n\t\t\t&lt;fileid /&gt;\n\t\t\t&lt;hdsize&gt;0&lt;/hdsize&gt;\n\t\t\t&lt;hdmd5 /&gt;\n\t\t\t&lt;hdfileid /&gt;\n\t\t\t&lt;stillimagetimems&gt;0&lt;/stillimagetimems&gt;\n\t\t&lt;/live&gt;\n\t&lt;/img&gt;\n\t&lt;platform_signature /&gt;\n\t&lt;imgdatahash /&gt;\n\t&lt;ImgSourceInfo&gt;\n\t\t&lt;ImgSourceUrl /&gt;\n\t\t&lt;BizType&gt;0&lt;/BizType&gt;\n\t&lt;/ImgSourceInfo&gt;\n&lt;/msg&gt;\n</content>\n\t\t</refermsg>\n\t</appmsg>\n\t<fromusername>wxid_ikxxrwasicud11</fromusername>\n\t<scene>0</scene>\n\t<appinfo>\n\t\t<version>1</version>\n\t\t<appname></appname>\n\t</appinfo>\n\t<commenturl></commenturl>\n</msg>\n'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1753849995, 'MsgSource': '<msgsource>\n\t<alnode>\n\t\t<fr>1</fr>\n\t</alnode>\n\t<sec_msg_node>\n\t\t<uuid>f08c81438b2e01552c33b00d9c1af519_</uuid>\n\t\t<risk-file-flag />\n\t\t<risk-file-md5-list />\n\t</sec_msg_node>\n\t<silence>1</silence>\n\t<membercount>145</membercount>\n\t<signature>N0_V1_oImjSEOD|v1_msBv/N4e</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'NewMsgId': 8346484550914759911, 'MsgSeq': 871411610}
2025-07-30 12:33:03 | DEBUG | 从群聊消息中提取发送者: wxid_ikxxrwasicud11
2025-07-30 12:33:03 | DEBUG | 使用已解析的XML处理引用消息
2025-07-30 12:33:03 | INFO | 收到引用消息: 消息ID:2023899047 来自:27852221909@chatroom 发送人:wxid_ikxxrwasicud11 内容:没图鉴 引用类型:3
2025-07-30 12:33:03 | INFO | [DouBaoImageToImage] 收到引用消息: 没图鉴
2025-07-30 12:33:03 | INFO | [TimerTask] 收到引用消息调试信息:
2025-07-30 12:33:03 | INFO |   - 消息内容: 没图鉴
2025-07-30 12:33:03 | INFO |   - 群组ID: 27852221909@chatroom
2025-07-30 12:33:03 | INFO |   - 发送人: wxid_ikxxrwasicud11
2025-07-30 12:33:03 | INFO |   - 引用信息: {'MsgType': 3, 'Content': '<?xml version="1.0"?>\n<msg>\n\t<appmsg appid="" sdkver="0">\n\t\t<title>没图鉴</title>\n\t\t<des />\n\t\t<action />\n\t\t<type>57</type>\n\t\t<showtype>0</showtype>\n\t\t<soundtype>0</soundtype>\n\t\t<mediatagname />\n\t\t<messageext />\n\t\t<messageaction />\n\t\t<content />\n\t\t<contentattr>0</contentattr>\n\t\t<url />\n\t\t<lowurl />\n\t\t<dataurl />\n\t\t<lowdataurl />\n\t\t<songalbumurl />\n\t\t<songlyric />\n\t\t<template_id />\n\t\t<appattach>\n\t\t\t<totallen>0</totallen>\n\t\t\t<attachid />\n\t\t\t<emoticonmd5 />\n\t\t\t<fileext />\n\t\t\t<aeskey />\n\t\t</appattach>\n\t\t<extinfo />\n\t\t<sourceusername />\n\t\t<sourcedisplayname />\n\t\t<thumburl />\n\t\t<md5 />\n\t\t<statextstr />\n\t\t<refermsg>\n\t\t\t<type>3</type>\n\t\t\t<svrid>7272009725206363595</svrid>\n\t\t\t<fromusr>27852221909@chatroom</fromusr>\n\t\t\t<chatusr>wxid_2530z9t0joek22</chatusr>\n\t\t\t<displayname>慕ؓ悦ؓ˒</displayname>\n\t\t\t<msgsource>&lt;msgsource&gt;\n\t&lt;sec_msg_node&gt;\n\t\t&lt;uuid&gt;cf35c816fd7e7347bcd68e25e842abc0_&lt;/uuid&gt;\n\t\t&lt;risk-file-flag /&gt;\n\t\t&lt;risk-file-md5-list /&gt;\n\t&lt;/sec_msg_node&gt;\n\t&lt;imgmsg_pd cdnmidimgurl_size="83646" cdnmidimgurl_pd_pri="30" cdnmidimgurl_pd="0" /&gt;\n\t&lt;silence&gt;1&lt;/silence&gt;\n\t&lt;membercount&gt;145&lt;/membercount&gt;\n\t&lt;signature&gt;N0_V1_S8NSjAqh|v1_Mug/0esO&lt;/signature&gt;\n\t&lt;tmp_node&gt;\n\t\t&lt;publisher-id&gt;&lt;/publisher-id&gt;\n\t&lt;/tmp_node&gt;\n&lt;/msgsource&gt;\n</msgsource>\n\t\t\t<createtime>1753849485</createtime>\n\t\t\t<content>wxid_2530z9t0joek22:\n&lt;?xml version="1.0"?&gt;\n&lt;msg&gt;\n\t&lt;img aeskey="da938e99149a2431c99409ad8c98da08" encryver="1" cdnthumbaeskey="da938e99149a2431c99409ad8c98da08" cdnthumburl="3057020100044b30490201000204f060aea202032f802902049af73db7020468899e8d042464613031306339392d663235332d346332642d626361632d353033353764626631633139020405290a020201000405004c57c100" cdnthumblength="4799" cdnthumbheight="196" cdnthumbwidth="432" cdnmidheight="0" cdnmidwidth="0" cdnhdheight="0" cdnhdwidth="0" cdnmidimgurl="3057020100044b30490201000204f060aea202032f802902049af73db7020468899e8d042464613031306339392d663235332d346332642d626361632d353033353764626631633139020405290a020201000405004c57c100" length="916383" md5="c37b71e31f3928cde431fd6bbc132300" hevc_mid_size="83646" originsourcemd5="e6322fcec6420bcf34b7e0d4ad3138fa"&gt;\n\t\t&lt;secHashInfoBase64&gt;eyJwaGFzaCI6ImI0ZjA0NDMwMDQwMDAwMDAiLCJwZHFIYXNoIjoiNjdiMDcwZDhjY2Q4YzVmNjQz\nNWExNzcyMmY5OTM3NjIyZjM2YTY4ZDg5YWQyNjMxZTEzNTg5YjliOGU1MTM1NiJ9\n&lt;/secHashInfoBase64&gt;\n\t\t&lt;live&gt;\n\t\t\t&lt;duration&gt;0&lt;/duration&gt;\n\t\t\t&lt;size&gt;0&lt;/size&gt;\n\t\t\t&lt;md5 /&gt;\n\t\t\t&lt;fileid /&gt;\n\t\t\t&lt;hdsize&gt;0&lt;/hdsize&gt;\n\t\t\t&lt;hdmd5 /&gt;\n\t\t\t&lt;hdfileid /&gt;\n\t\t\t&lt;stillimagetimems&gt;0&lt;/stillimagetimems&gt;\n\t\t&lt;/live&gt;\n\t&lt;/img&gt;\n\t&lt;platform_signature /&gt;\n\t&lt;imgdatahash /&gt;\n\t&lt;ImgSourceInfo&gt;\n\t\t&lt;ImgSourceUrl /&gt;\n\t\t&lt;BizType&gt;0&lt;/BizType&gt;\n\t&lt;/ImgSourceInfo&gt;\n&lt;/msg&gt;\n</content>\n\t\t</refermsg>\n\t</appmsg>\n\t<fromusername>wxid_ikxxrwasicud11</fromusername>\n\t<scene>0</scene>\n\t<appinfo>\n\t\t<version>1</version>\n\t\t<appname></appname>\n\t</appinfo>\n\t<commenturl></commenturl>\n</msg>\n', 'Msgid': '7272009725206363595', 'NewMsgId': '7272009725206363595', 'ToWxid': 'wxid_4usgcju5ey9q29', 'FromWxid': '27852221909@chatroom', 'Nickname': '慕ؓ悦ؓ˒', 'MsgSource': '<msgsource>\n\t<sec_msg_node>\n\t\t<uuid>cf35c816fd7e7347bcd68e25e842abc0_</uuid>\n\t\t<risk-file-flag />\n\t\t<risk-file-md5-list />\n\t</sec_msg_node>\n\t<imgmsg_pd cdnmidimgurl_size="83646" cdnmidimgurl_pd_pri="30" cdnmidimgurl_pd="0" />\n\t<silence>1</silence>\n\t<membercount>145</membercount>\n\t<signature>N0_V1_S8NSjAqh|v1_Mug/0esO</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'Createtime': '1753849485', 'SenderWxid': 'wxid_ikxxrwasicud11'}
2025-07-30 12:33:03 | INFO |   - 引用消息ID: 
2025-07-30 12:33:03 | INFO |   - 引用消息类型: 
2025-07-30 12:33:03 | INFO |   - 引用消息内容: <?xml version="1.0"?>
<msg>
	<appmsg appid="" sdkver="0">
		<title>没图鉴</title>
		<des />
		<action />
		<type>57</type>
		<showtype>0</showtype>
		<soundtype>0</soundtype>
		<mediatagname />
		<messageext />
		<messageaction />
		<content />
		<contentattr>0</contentattr>
		<url />
		<lowurl />
		<dataurl />
		<lowdataurl />
		<songalbumurl />
		<songlyric />
		<template_id />
		<appattach>
			<totallen>0</totallen>
			<attachid />
			<emoticonmd5 />
			<fileext />
			<aeskey />
		</appattach>
		<extinfo />
		<sourceusername />
		<sourcedisplayname />
		<thumburl />
		<md5 />
		<statextstr />
		<refermsg>
			<type>3</type>
			<svrid>7272009725206363595</svrid>
			<fromusr>27852221909@chatroom</fromusr>
			<chatusr>wxid_2530z9t0joek22</chatusr>
			<displayname>慕ؓ悦ؓ˒</displayname>
			<msgsource>&lt;msgsource&gt;
	&lt;sec_msg_node&gt;
		&lt;uuid&gt;cf35c816fd7e7347bcd68e25e842abc0_&lt;/uuid&gt;
		&lt;risk-file-flag /&gt;
		&lt;risk-file-md5-list /&gt;
	&lt;/sec_msg_node&gt;
	&lt;imgmsg_pd cdnmidimgurl_size="83646" cdnmidimgurl_pd_pri="30" cdnmidimgurl_pd="0" /&gt;
	&lt;silence&gt;1&lt;/silence&gt;
	&lt;membercount&gt;145&lt;/membercount&gt;
	&lt;signature&gt;N0_V1_S8NSjAqh|v1_Mug/0esO&lt;/signature&gt;
	&lt;tmp_node&gt;
		&lt;publisher-id&gt;&lt;/publisher-id&gt;
	&lt;/tmp_node&gt;
&lt;/msgsource&gt;
</msgsource>
			<createtime>1753849485</createtime>
			<content>wxid_2530z9t0joek22:
&lt;?xml version="1.0"?&gt;
&lt;msg&gt;
	&lt;img aeskey="da938e99149a2431c99409ad8c98da08" encryver="1" cdnthumbaeskey="da938e99149a2431c99409ad8c98da08" cdnthumburl="3057020100044b30490201000204f060aea202032f802902049af73db7020468899e8d042464613031306339392d663235332d346332642d626361632d353033353764626631633139020405290a020201000405004c57c100" cdnthumblength="4799" cdnthumbheight="196" cdnthumbwidth="432" cdnmidheight="0" cdnmidwidth="0" cdnhdheight="0" cdnhdwidth="0" cdnmidimgurl="3057020100044b30490201000204f060aea202032f802902049af73db7020468899e8d042464613031306339392d663235332d346332642d626361632d353033353764626631633139020405290a020201000405004c57c100" length="916383" md5="c37b71e31f3928cde431fd6bbc132300" hevc_mid_size="83646" originsourcemd5="e6322fcec6420bcf34b7e0d4ad3138fa"&gt;
		&lt;secHashInfoBase64&gt;eyJwaGFzaCI6ImI0ZjA0NDMwMDQwMDAwMDAiLCJwZHFIYXNoIjoiNjdiMDcwZDhjY2Q4YzVmNjQz
NWExNzcyMmY5OTM3NjIyZjM2YTY4ZDg5YWQyNjMxZTEzNTg5YjliOGU1MTM1NiJ9
&lt;/secHashInfoBase64&gt;
		&lt;live&gt;
			&lt;duration&gt;0&lt;/duration&gt;
			&lt;size&gt;0&lt;/size&gt;
			&lt;md5 /&gt;
			&lt;fileid /&gt;
			&lt;hdsize&gt;0&lt;/hdsize&gt;
			&lt;hdmd5 /&gt;
			&lt;hdfileid /&gt;
			&lt;stillimagetimems&gt;0&lt;/stillimagetimems&gt;
		&lt;/live&gt;
	&lt;/img&gt;
	&lt;platform_signature /&gt;
	&lt;imgdatahash /&gt;
	&lt;ImgSourceInfo&gt;
		&lt;ImgSourceUrl /&gt;
		&lt;BizType&gt;0&lt;/BizType&gt;
	&lt;/ImgSourceInfo&gt;
&lt;/msg&gt;
</content>
		</refermsg>
	</appmsg>
	<fromusername>wxid_ikxxrwasicud11</fromusername>
	<scene>0</scene>
	<appinfo>
		<version>1</version>
		<appname></appname>
	</appinfo>
	<commenturl></commenturl>
</msg>

2025-07-30 12:33:03 | INFO |   - 引用消息发送人: wxid_ikxxrwasicud11
2025-07-30 12:33:19 | INFO | [TempFileManager] 开始清理临时文件...
2025-07-30 12:34:05 | DEBUG | 收到消息: {'MsgId': 1346242027, 'FromUserName': {'string': '27852221909@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 1, 'Content': {'string': 'wxid_sf4ikl8sczaj21:\n@゛花落ོ.°\u2005团大，一局排位的奖励哪里领的啊'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1753850058, 'MsgSource': '<msgsource>\n\t<atuserlist>wxid_ikxxrwasicud11</atuserlist>\n\t<bizflag>0</bizflag>\n\t<pua>1</pua>\n\t<alnode>\n\t\t<inlenlist>8</inlenlist>\n\t</alnode>\n\t<eggIncluded>1</eggIncluded>\n\t<silence>1</silence>\n\t<membercount>145</membercount>\n\t<signature>N0_V1_GOlbGhHf|v1_uJwD6KTB</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'NewMsgId': 6004856275625405186, 'MsgSeq': 871411611}
2025-07-30 12:34:05 | INFO | 收到文本消息: 消息ID:1346242027 来自:27852221909@chatroom 发送人:wxid_sf4ikl8sczaj21 @:['wxid_ikxxrwasicud11'] 内容:@゛花落ོ.° 团大，一局排位的奖励哪里领的啊
2025-07-30 12:34:05 | DEBUG | 处理消息内容: '@゛花落ོ.° 团大，一局排位的奖励哪里领的啊'
2025-07-30 12:34:05 | DEBUG | 消息内容 '@゛花落ོ.° 团大，一局排位的奖励哪里领的啊' 不匹配任何命令，忽略
2025-07-30 12:34:33 | DEBUG | 收到消息: {'MsgId': 1834375599, 'FromUserName': {'string': '27852221909@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 49, 'Content': {'string': 'wxid_ikxxrwasicud11:\n<?xml version="1.0"?>\n<msg>\n\t<appmsg appid="" sdkver="0">\n\t\t<title>1</title>\n\t\t<des />\n\t\t<action />\n\t\t<type>57</type>\n\t\t<showtype>0</showtype>\n\t\t<soundtype>0</soundtype>\n\t\t<mediatagname />\n\t\t<messageext />\n\t\t<messageaction />\n\t\t<content />\n\t\t<contentattr>0</contentattr>\n\t\t<url />\n\t\t<lowurl />\n\t\t<dataurl />\n\t\t<lowdataurl />\n\t\t<songalbumurl />\n\t\t<songlyric />\n\t\t<template_id />\n\t\t<appattach>\n\t\t\t<totallen>0</totallen>\n\t\t\t<attachid />\n\t\t\t<emoticonmd5 />\n\t\t\t<fileext />\n\t\t\t<aeskey />\n\t\t</appattach>\n\t\t<extinfo />\n\t\t<sourceusername />\n\t\t<sourcedisplayname />\n\t\t<thumburl />\n\t\t<md5 />\n\t\t<statextstr />\n\t\t<refermsg>\n\t\t\t<type>49</type>\n\t\t\t<svrid>*******************</svrid>\n\t\t\t<fromusr>27852221909@chatroom</fromusr>\n\t\t\t<chatusr>wxid_ctp9qffuf14b21</chatusr>\n\t\t\t<displayname>Z⁰</displayname>\n\t\t\t<msgsource>&lt;msgsource&gt;\n    &lt;alnode&gt;\n        &lt;fr&gt;4&lt;/fr&gt;\n    &lt;/alnode&gt;\n    &lt;tmp_node&gt;\n        &lt;publisher-id /&gt;\n    &lt;/tmp_node&gt;\n    &lt;sec_msg_node&gt;\n        &lt;uuid&gt;18a5c10a576c7cbf68e59b2364696a91_&lt;/uuid&gt;\n        &lt;risk-file-flag /&gt;\n        &lt;risk-file-md5-list /&gt;\n        &lt;alnode&gt;\n            &lt;fr&gt;1&lt;/fr&gt;\n        &lt;/alnode&gt;\n    &lt;/sec_msg_node&gt;\n    &lt;silence&gt;1&lt;/silence&gt;\n    &lt;membercount&gt;145&lt;/membercount&gt;\n    &lt;signature&gt;N0_V1_cdN3zDJJ|v1_Xc8vJdst&lt;/signature&gt;\n&lt;/msgsource&gt;\n</msgsource>\n\t\t\t<createtime>1753532331</createtime>\n\t\t\t<content>wxid_ctp9qffuf14b21:\n&lt;?xml version="1.0"?&gt;\n&lt;msg&gt;\n\t&lt;appmsg appid="" sdkver="0"&gt;\n\t\t&lt;title&gt;派对永不停Beat-1000钻石等你领取！&lt;/title&gt;\n\t\t&lt;des /&gt;\n\t\t&lt;username /&gt;\n\t\t&lt;action&gt;view&lt;/action&gt;\n\t\t&lt;type&gt;33&lt;/type&gt;\n\t\t&lt;showtype&gt;0&lt;/showtype&gt;\n\t\t&lt;content /&gt;\n\t\t&lt;url&gt;https://mp.weixin.qq.com/mp/waerrpage?appid=wxa708de63ee4a2353&amp;amp;type=upgrade&amp;amp;upgradetype=3#wechat_redirect&lt;/url&gt;\n\t\t&lt;lowurl /&gt;\n\t\t&lt;forwardflag&gt;0&lt;/forwardflag&gt;\n\t\t&lt;dataurl /&gt;\n\t\t&lt;lowdataurl /&gt;\n\t\t&lt;contentattr&gt;0&lt;/contentattr&gt;\n\t\t&lt;streamvideo&gt;\n\t\t\t&lt;streamvideourl /&gt;\n\t\t\t&lt;streamvideototaltime&gt;0&lt;/streamvideototaltime&gt;\n\t\t\t&lt;streamvideotitle /&gt;\n\t\t\t&lt;streamvideowording /&gt;\n\t\t\t&lt;streamvideoweburl /&gt;\n\t\t\t&lt;streamvideothumburl /&gt;\n\t\t\t&lt;streamvideoaduxinfo /&gt;\n\t\t\t&lt;streamvideopublishid /&gt;\n\t\t&lt;/streamvideo&gt;\n\t\t&lt;canvasPageItem&gt;\n\t\t\t&lt;canvasPageXml&gt;&lt;![CDATA[]]&gt;&lt;/canvasPageXml&gt;\n\t\t&lt;/canvasPageItem&gt;\n\t\t&lt;appattach&gt;\n\t\t\t&lt;attachid /&gt;\n\t\t\t&lt;cdnthumburl&gt;3057020100044b304902010002047d46407602032f59e102040ec7587d02046884c7aa042462663935616431332d383031372d346635362d396666632d3739306131306232616435310204051408030201000405004c4e6100&lt;/cdnthumburl&gt;\n\t\t\t&lt;cdnthumbmd5&gt;4ad6c0fff6a62ab56feba5cde2706304&lt;/cdnthumbmd5&gt;\n\t\t\t&lt;cdnthumblength&gt;75094&lt;/cdnthumblength&gt;\n\t\t\t&lt;cdnthumbheight&gt;576&lt;/cdnthumbheight&gt;\n\t\t\t&lt;cdnthumbwidth&gt;720&lt;/cdnthumbwidth&gt;\n\t\t\t&lt;cdnthumbaeskey&gt;df4cde14ffe3af3d73bd04a36193d515&lt;/cdnthumbaeskey&gt;\n\t\t\t&lt;aeskey&gt;df4cde14ffe3af3d73bd04a36193d515&lt;/aeskey&gt;\n\t\t\t&lt;encryver&gt;1&lt;/encryver&gt;\n\t\t\t&lt;fileext /&gt;\n\t\t\t&lt;islargefilemsg&gt;0&lt;/islargefilemsg&gt;\n\t\t&lt;/appattach&gt;\n\t\t&lt;extinfo /&gt;\n\t\t&lt;androidsource&gt;3&lt;/androidsource&gt;\n\t\t&lt;sourceusername&gt;gh_25eb09d7bc53@app&lt;/sourceusername&gt;\n\t\t&lt;sourcedisplayname&gt;唱舞星愿站&lt;/sourcedisplayname&gt;\n\t\t&lt;commenturl /&gt;\n\t\t&lt;thumburl /&gt;\n\t\t&lt;mediatagname /&gt;\n\t\t&lt;messageaction&gt;&lt;![CDATA[]]&gt;&lt;/messageaction&gt;\n\t\t&lt;messageext&gt;&lt;![CDATA[]]&gt;&lt;/messageext&gt;\n\t\t&lt;emoticongift&gt;\n\t\t\t&lt;packageflag&gt;0&lt;/packageflag&gt;\n\t\t\t&lt;packageid /&gt;\n\t\t&lt;/emoticongift&gt;\n\t\t&lt;emoticonshared&gt;\n\t\t\t&lt;packageflag&gt;0&lt;/packageflag&gt;\n\t\t\t&lt;packageid /&gt;\n\t\t&lt;/emoticonshared&gt;\n\t\t&lt;designershared&gt;\n\t\t\t&lt;designeruin&gt;0&lt;/designeruin&gt;\n\t\t\t&lt;designername&gt;null&lt;/designername&gt;\n\t\t\t&lt;designerrediretcturl&gt;&lt;![CDATA[null]]&gt;&lt;/designerrediretcturl&gt;\n\t\t&lt;/designershared&gt;\n\t\t&lt;emotionpageshared&gt;\n\t\t\t&lt;tid&gt;0&lt;/tid&gt;\n\t\t\t&lt;title&gt;null&lt;/title&gt;\n\t\t\t&lt;desc&gt;null&lt;/desc&gt;\n\t\t\t&lt;iconUrl&gt;&lt;![CDATA[null]]&gt;&lt;/iconUrl&gt;\n\t\t\t&lt;secondUrl&gt;null&lt;/secondUrl&gt;\n\t\t\t&lt;pageType&gt;0&lt;/pageType&gt;\n\t\t\t&lt;setKey&gt;null&lt;/setKey&gt;\n\t\t&lt;/emotionpageshared&gt;\n\t\t&lt;webviewshared&gt;\n\t\t\t&lt;shareUrlOriginal /&gt;\n\t\t\t&lt;shareUrlOpen /&gt;\n\t\t\t&lt;jsAppId /&gt;\n\t\t\t&lt;publisherId&gt;wxapp_wxa708de63ee4a2353pages/Activity/teamUp/index.html?key=uK1I2mwn&amp;amp;share_unionid=oA7D81elKyhcQQ8srAn4tlz-eSJU&amp;amp;task_id=&amp;amp;team_id=24069&lt;/publisherId&gt;\n\t\t\t&lt;publisherReqId /&gt;\n\t\t&lt;/webviewshared&gt;\n\t\t&lt;template_id /&gt;\n\t\t&lt;md5&gt;4ad6c0fff6a62ab56feba5cde2706304&lt;/md5&gt;\n\t\t&lt;finderGuarantee&gt;\n\t\t\t&lt;scene&gt;&lt;![CDATA[0]]&gt;&lt;/scene&gt;\n\t\t&lt;/finderGuarantee&gt;\n\t\t&lt;websearch&gt;\n\t\t\t&lt;rec_category&gt;0&lt;/rec_category&gt;\n\t\t\t&lt;channelId&gt;0&lt;/channelId&gt;\n\t\t&lt;/websearch&gt;\n\t\t&lt;weappinfo&gt;\n\t\t\t&lt;pagepath&gt;&lt;![CDATA[pages/Activity/teamUp/index.html?key=uK1I2mwn&amp;share_unionid=oA7D81elKyhcQQ8srAn4tlz-eSJU&amp;task_id=&amp;team_id=24069]]&gt;&lt;/pagepath&gt;\n\t\t\t&lt;username&gt;gh_25eb09d7bc53@app&lt;/username&gt;\n\t\t\t&lt;appid&gt;wxa708de63ee4a2353&lt;/appid&gt;\n\t\t\t&lt;version&gt;16&lt;/version&gt;\n\t\t\t&lt;type&gt;2&lt;/type&gt;\n\t\t\t&lt;weappiconurl&gt;&lt;![CDATA[http://wx.qlogo.cn/mmhead/FMajU52WvbEE6SVVwqdPicO6GjWVVIhhLiaZK2NvCJgt3epAkLg7A3nkRTia23B9lSt0pXQJz0viaR4/96]]&gt;&lt;/weappiconurl&gt;\n\t\t\t&lt;shareId&gt;&lt;![CDATA[1_wxa708de63ee4a2353_f17385d137a5603e10bd7aa0a53ee459_1753451238_2]]&gt;&lt;/shareId&gt;\n\t\t\t&lt;appservicetype&gt;0&lt;/appservicetype&gt;\n\t\t\t&lt;secflagforsinglepagemode&gt;0&lt;/secflagforsinglepagemode&gt;\n\t\t\t&lt;videopageinfo&gt;\n\t\t\t\t&lt;thumbwidth&gt;720&lt;/thumbwidth&gt;\n\t\t\t\t&lt;thumbheight&gt;576&lt;/thumbheight&gt;\n\t\t\t\t&lt;fromopensdk&gt;0&lt;/fromopensdk&gt;\n\t\t\t&lt;/videopageinfo&gt;\n\t\t&lt;/weappinfo&gt;\n\t\t&lt;statextstr /&gt;\n\t\t&lt;musicShareItem&gt;\n\t\t\t&lt;musicDuration&gt;0&lt;/musicDuration&gt;\n\t\t&lt;/musicShareItem&gt;\n\t\t&lt;finderLiveProductShare&gt;\n\t\t\t&lt;finderLiveID&gt;&lt;![CDATA[]]&gt;&lt;/finderLiveID&gt;\n\t\t\t&lt;finderUsername&gt;&lt;![CDATA[]]&gt;&lt;/finderUsername&gt;\n\t\t\t&lt;finderObjectID&gt;&lt;![CDATA[]]&gt;&lt;/finderObjectID&gt;\n\t\t\t&lt;finderNonceID&gt;&lt;![CDATA[]]&gt;&lt;/finderNonceID&gt;\n\t\t\t&lt;liveStatus&gt;&lt;![CDATA[]]&gt;&lt;/liveStatus&gt;\n\t\t\t&lt;appId&gt;&lt;![CDATA[]]&gt;&lt;/appId&gt;\n\t\t\t&lt;pagePath&gt;&lt;![CDATA[]]&gt;&lt;/pagePath&gt;\n\t\t\t&lt;productId&gt;&lt;![CDATA[]]&gt;&lt;/productId&gt;\n\t\t\t&lt;coverUrl&gt;&lt;![CDATA[]]&gt;&lt;/coverUrl&gt;\n\t\t\t&lt;productTitle&gt;&lt;![CDATA[]]&gt;&lt;/productTitle&gt;\n\t\t\t&lt;marketPrice&gt;&lt;![CDATA[0]]&gt;&lt;/marketPrice&gt;\n\t\t\t&lt;sellingPrice&gt;&lt;![CDATA[0]]&gt;&lt;/sellingPrice&gt;\n\t\t\t&lt;platformHeadImg&gt;&lt;![CDATA[]]&gt;&lt;/platformHeadImg&gt;\n\t\t\t&lt;platformName&gt;&lt;![CDATA[]]&gt;&lt;/platformName&gt;\n\t\t\t&lt;shopWindowId&gt;&lt;![CDATA[]]&gt;&lt;/shopWindowId&gt;\n\t\t\t&lt;flashSalePrice&gt;&lt;![CDATA[0]]&gt;&lt;/flashSalePrice&gt;\n\t\t\t&lt;flashSaleEndTime&gt;&lt;![CDATA[0]]&gt;&lt;/flashSaleEndTime&gt;\n\t\t\t&lt;ecSource&gt;&lt;![CDATA[]]&gt;&lt;/ecSource&gt;\n\t\t\t&lt;sellingPriceWording&gt;&lt;![CDATA[]]&gt;&lt;/sellingPriceWording&gt;\n\t\t\t&lt;platformIconURL&gt;&lt;![CDATA[]]&gt;&lt;/platformIconURL&gt;\n\t\t\t&lt;firstProductTagURL&gt;&lt;![CDATA[]]&gt;&lt;/firstProductTagURL&gt;\n\t\t\t&lt;firstProductTagAspectRatioString&gt;&lt;![CDATA[0.0]]&gt;&lt;/firstProductTagAspectRatioString&gt;\n\t\t\t&lt;secondProductTagURL&gt;&lt;![CDATA[]]&gt;&lt;/secondProductTagURL&gt;\n\t\t\t&lt;secondProductTagAspectRatioString&gt;&lt;![CDATA[0.0]]&gt;&lt;/secondProductTagAspectRatioString&gt;\n\t\t\t&lt;firstGuaranteeWording&gt;&lt;![CDATA[]]&gt;&lt;/firstGuaranteeWording&gt;\n\t\t\t&lt;secondGuaranteeWording&gt;&lt;![CDATA[]]&gt;&lt;/secondGuaranteeWording&gt;\n\t\t\t&lt;thirdGuaranteeWording&gt;&lt;![CDATA[]]&gt;&lt;/thirdGuaranteeWording&gt;\n\t\t\t&lt;isPriceBeginShow&gt;false&lt;/isPriceBeginShow&gt;\n\t\t\t&lt;lastGMsgID&gt;&lt;![CDATA[]]&gt;&lt;/lastGMsgID&gt;\n\t\t\t&lt;promoterKey&gt;&lt;![CDATA[]]&gt;&lt;/promoterKey&gt;\n\t\t\t&lt;discountWording&gt;&lt;![CDATA[]]&gt;&lt;/discountWording&gt;\n\t\t\t&lt;priceSuffixDescription&gt;&lt;![CDATA[]]&gt;&lt;/priceSuffixDescription&gt;\n\t\t\t&lt;productCardKey&gt;&lt;![CDATA[]]&gt;&lt;/productCardKey&gt;\n\t\t\t&lt;isWxShop&gt;&lt;![CDATA[]]&gt;&lt;/isWxShop&gt;\n\t\t\t&lt;brandIconUrl&gt;&lt;![CDATA[]]&gt;&lt;/brandIconUrl&gt;\n\t\t\t&lt;rIconUrl&gt;&lt;![CDATA[]]&gt;&lt;/rIconUrl&gt;\n\t\t\t&lt;rIconUrlDarkMode&gt;&lt;![CDATA[]]&gt;&lt;/rIconUrlDarkMode&gt;\n\t\t\t&lt;topShopIconUrl&gt;&lt;![CDATA[]]&gt;&lt;/topShopIconUrl&gt;\n\t\t\t&lt;topShopIconUrlDarkMode&gt;&lt;![CDATA[]]&gt;&lt;/topShopIconUrlDarkMode&gt;\n\t\t\t&lt;simplifyTopShopIconUrl&gt;&lt;![CDATA[]]&gt;&lt;/simplifyTopShopIconUrl&gt;\n\t\t\t&lt;simplifyTopShopIconUrlDarkmode&gt;&lt;![CDATA[]]&gt;&lt;/simplifyTopShopIconUrlDarkmode&gt;\n\t\t\t&lt;topShopIconWidth&gt;&lt;![CDATA[0]]&gt;&lt;/topShopIconWidth&gt;\n\t\t\t&lt;topShopIconHeight&gt;&lt;![CDATA[0]]&gt;&lt;/topShopIconHeight&gt;\n\t\t\t&lt;simplifyTopShopIconWidth&gt;&lt;![CDATA[0]]&gt;&lt;/simplifyTopShopIconWidth&gt;\n\t\t\t&lt;simplifyTopShopIconHeight&gt;&lt;![CDATA[0]]&gt;&lt;/simplifyTopShopIconHeight&gt;\n\t\t\t&lt;showBoxItemStringList /&gt;\n\t\t&lt;/finderLiveProductShare&gt;\n\t\t&lt;finderOrder&gt;\n\t\t\t&lt;appID&gt;&lt;![CDATA[]]&gt;&lt;/appID&gt;\n\t\t\t&lt;orderID&gt;&lt;![CDATA[]]&gt;&lt;/orderID&gt;\n\t\t\t&lt;path&gt;&lt;![CDATA[]]&gt;&lt;/path&gt;\n\t\t\t&lt;priceWording&gt;&lt;![CDATA[]]&gt;&lt;/priceWording&gt;\n\t\t\t&lt;stateWording&gt;&lt;![CDATA[]]&gt;&lt;/stateWording&gt;\n\t\t\t&lt;productImageURL&gt;&lt;![CDATA[]]&gt;&lt;/productImageURL&gt;\n\t\t\t&lt;products&gt;&lt;![CDATA[]]&gt;&lt;/products&gt;\n\t\t\t&lt;productsCount&gt;&lt;![CDATA[0]]&gt;&lt;/productsCount&gt;\n\t\t\t&lt;orderType&gt;&lt;![CDATA[0]]&gt;&lt;/orderType&gt;\n\t\t\t&lt;newPriceWording&gt;&lt;![CDATA[]]&gt;&lt;/newPriceWording&gt;\n\t\t\t&lt;newStateWording&gt;&lt;![CDATA[]]&gt;&lt;/newStateWording&gt;\n\t\t\t&lt;useNewWording&gt;&lt;![CDATA[0]]&gt;&lt;/useNewWording&gt;\n\t\t&lt;/finderOrder&gt;\n\t\t&lt;finderShopWindowShare&gt;\n\t\t\t&lt;finderUsername&gt;&lt;![CDATA[]]&gt;&lt;/finderUsername&gt;\n\t\t\t&lt;avatar&gt;&lt;![CDATA[]]&gt;&lt;/avatar&gt;\n\t\t\t&lt;nickname&gt;&lt;![CDATA[]]&gt;&lt;/nickname&gt;\n\t\t\t&lt;commodityInStockCount&gt;&lt;![CDATA[]]&gt;&lt;/commodityInStockCount&gt;\n\t\t\t&lt;appId&gt;&lt;![CDATA[]]&gt;&lt;/appId&gt;\n\t\t\t&lt;path&gt;&lt;![CDATA[]]&gt;&lt;/path&gt;\n\t\t\t&lt;appUsername&gt;&lt;![CDATA[]]&gt;&lt;/appUsername&gt;\n\t\t\t&lt;query&gt;&lt;![CDATA[]]&gt;&lt;/query&gt;\n\t\t\t&lt;liteAppId&gt;&lt;![CDATA[]]&gt;&lt;/liteAppId&gt;\n\t\t\t&lt;liteAppPath&gt;&lt;![CDATA[]]&gt;&lt;/liteAppPath&gt;\n\t\t\t&lt;liteAppQuery&gt;&lt;![CDATA[]]&gt;&lt;/liteAppQuery&gt;\n\t\t\t&lt;platformTagURL&gt;&lt;![CDATA[]]&gt;&lt;/platformTagURL&gt;\n\t\t\t&lt;saleWording&gt;&lt;![CDATA[]]&gt;&lt;/saleWording&gt;\n\t\t\t&lt;lastGMsgID&gt;&lt;![CDATA[]]&gt;&lt;/lastGMsgID&gt;\n\t\t\t&lt;profileTypeWording&gt;&lt;![CDATA[]]&gt;&lt;/profileTypeWording&gt;\n\t\t\t&lt;saleWordingExtra&gt;&lt;![CDATA[]]&gt;&lt;/saleWordingExtra&gt;\n\t\t\t&lt;isWxShop&gt;&lt;![CDATA[]]&gt;&lt;/isWxShop&gt;\n\t\t\t&lt;platformIconUrl&gt;&lt;![CDATA[]]&gt;&lt;/platformIconUrl&gt;\n\t\t\t&lt;brandIconUrl&gt;&lt;![CDATA[]]&gt;&lt;/brandIconUrl&gt;\n\t\t\t&lt;description&gt;&lt;![CDATA[]]&gt;&lt;/description&gt;\n\t\t\t&lt;backgroundUrl&gt;&lt;![CDATA[]]&gt;&lt;/backgroundUrl&gt;\n\t\t\t&lt;darkModePlatformIconUrl&gt;&lt;![CDATA[]]&gt;&lt;/darkModePlatformIconUrl&gt;\n\t\t\t&lt;rIconUrl&gt;&lt;![CDATA[]]&gt;&lt;/rIconUrl&gt;\n\t\t\t&lt;rIconUrlDarkMode&gt;&lt;![CDATA[]]&gt;&lt;/rIconUrlDarkMode&gt;\n\t\t\t&lt;rWords&gt;&lt;![CDATA[]]&gt;&lt;/rWords&gt;\n\t\t\t&lt;topShopIconUrl&gt;&lt;![CDATA[]]&gt;&lt;/topShopIconUrl&gt;\n\t\t\t&lt;topShopIconUrlDarkMode&gt;&lt;![CDATA[]]&gt;&lt;/topShopIconUrlDarkMode&gt;\n\t\t\t&lt;simplifyTopShopIconUrl&gt;&lt;![CDATA[]]&gt;&lt;/simplifyTopShopIconUrl&gt;\n\t\t\t&lt;simplifyTopShopIconUrlDarkmode&gt;&lt;![CDATA[]]&gt;&lt;/simplifyTopShopIconUrlDarkmode&gt;\n\t\t\t&lt;topShopIconWidth&gt;&lt;![CDATA[0]]&gt;&lt;/topShopIconWidth&gt;\n\t\t\t&lt;topShopIconHeight&gt;&lt;![CDATA[0]]&gt;&lt;/topShopIconHeight&gt;\n\t\t\t&lt;simplifyTopShopIconWidth&gt;&lt;![CDATA[0]]&gt;&lt;/simplifyTopShopIconWidth&gt;\n\t\t\t&lt;simplifyTopShopIconHeight&gt;&lt;![CDATA[0]]&gt;&lt;/simplifyTopShopIconHeight&gt;\n\t\t\t&lt;reputationInfo&gt;\n\t\t\t\t&lt;hasReputationInfo&gt;0&lt;/hasReputationInfo&gt;\n\t\t\t\t&lt;reputationScore&gt;0&lt;/reputationScore&gt;\n\t\t\t\t&lt;reputationWording /&gt;\n\t\t\t\t&lt;reputationTextColor /&gt;\n\t\t\t\t&lt;reputationLevelWording /&gt;\n\t\t\t\t&lt;reputationBackgroundColor /&gt;\n\t\t\t&lt;/reputationInfo&gt;\n\t\t\t&lt;productImageURLList /&gt;\n\t\t&lt;/finderShopWindowShare&gt;\n\t\t&lt;findernamecard&gt;\n\t\t\t&lt;username /&gt;\n\t\t\t&lt;avatar&gt;&lt;![CDATA[]]&gt;&lt;/avatar&gt;\n\t\t\t&lt;nickname /&gt;\n\t\t\t&lt;auth_job /&gt;\n\t\t\t&lt;auth_icon&gt;0&lt;/auth_icon&gt;\n\t\t\t&lt;auth_icon_url /&gt;\n\t\t\t&lt;ecSource&gt;&lt;![CDATA[]]&gt;&lt;/ecSource&gt;\n\t\t\t&lt;lastGMsgID&gt;&lt;![CDATA[]]&gt;&lt;/lastGMsgID&gt;\n\t\t&lt;/findernamecard&gt;\n\t\t&lt;finderGuarantee&gt;\n\t\t\t&lt;scene&gt;&lt;![CDATA[0]]&gt;&lt;/scene&gt;\n\t\t&lt;/finderGuarantee&gt;\n\t\t&lt;directshare&gt;0&lt;/directshare&gt;\n\t\t&lt;gamecenter&gt;\n\t\t\t&lt;namecard&gt;\n\t\t\t\t&lt;iconUrl /&gt;\n\t\t\t\t&lt;name /&gt;\n\t\t\t\t&lt;desc /&gt;\n\t\t\t\t&lt;tail /&gt;\n\t\t\t\t&lt;jumpUrl /&gt;\n\t\t\t\t&lt;liteappId /&gt;\n\t\t\t\t&lt;liteappPath /&gt;\n\t\t\t\t&lt;liteappQuery /&gt;\n\t\t\t\t&lt;liteappMinVersion /&gt;\n\t\t\t&lt;/namecard&gt;\n\t\t&lt;/gamecenter&gt;\n\t\t&lt;patMsg&gt;\n\t\t\t&lt;chatUser /&gt;\n\t\t\t&lt;records&gt;\n\t\t\t\t&lt;recordNum&gt;0&lt;/recordNum&gt;\n\t\t\t&lt;/records&gt;\n\t\t&lt;/patMsg&gt;\n\t\t&lt;secretmsg&gt;\n\t\t\t&lt;issecretmsg&gt;0&lt;/issecretmsg&gt;\n\t\t&lt;/secretmsg&gt;\n\t\t&lt;referfromscene&gt;0&lt;/referfromscene&gt;\n\t\t&lt;gameshare&gt;\n\t\t\t&lt;liteappext&gt;\n\t\t\t\t&lt;liteappbizdata /&gt;\n\t\t\t\t&lt;priority&gt;0&lt;/priority&gt;\n\t\t\t&lt;/liteappext&gt;\n\t\t\t&lt;appbrandext&gt;\n\t\t\t\t&lt;litegameinfo /&gt;\n\t\t\t\t&lt;priority&gt;-1&lt;/priority&gt;\n\t\t\t&lt;/appbrandext&gt;\n\t\t\t&lt;gameshareid /&gt;\n\t\t\t&lt;sharedata /&gt;\n\t\t\t&lt;isvideo&gt;0&lt;/isvideo&gt;\n\t\t\t&lt;duration&gt;-1&lt;/duration&gt;\n\t\t\t&lt;isexposed&gt;0&lt;/isexposed&gt;\n\t\t\t&lt;readtext /&gt;\n\t\t&lt;/gameshare&gt;\n\t\t&lt;tingChatRoomItem&gt;\n\t\t\t&lt;type&gt;0&lt;/type&gt;\n\t\t\t&lt;categoryItem&gt;null&lt;/categoryItem&gt;\n\t\t\t&lt;categoryId /&gt;\n\t\t&lt;/tingChatRoomItem&gt;\n\t\t&lt;mpsharetrace&gt;\n\t\t\t&lt;hasfinderelement&gt;0&lt;/hasfinderelement&gt;\n\t\t\t&lt;lastgmsgid /&gt;\n\t\t&lt;/mpsharetrace&gt;\n\t\t&lt;wxgamecard&gt;\n\t\t\t&lt;framesetname /&gt;\n\t\t\t&lt;mbcarddata /&gt;\n\t\t\t&lt;minpkgversion /&gt;\n\t\t\t&lt;clientextinfo /&gt;\n\t\t\t&lt;mbcardheight&gt;0&lt;/mbcardheight&gt;\n\t\t\t&lt;isoldversion&gt;0&lt;/isoldversion&gt;\n\t\t&lt;/wxgamecard&gt;\n\t\t&lt;ecskfcard&gt;\n\t\t\t&lt;framesetname /&gt;\n\t\t\t&lt;mbcarddata /&gt;\n\t\t\t&lt;minupdateunixtimestamp&gt;0&lt;/minupdateunixtimestamp&gt;\n\t\t\t&lt;needheader&gt;false&lt;/needheader&gt;\n\t\t\t&lt;summary /&gt;\n\t\t&lt;/ecskfcard&gt;\n\t\t&lt;liteapp&gt;\n\t\t\t&lt;id&gt;null&lt;/id&gt;\n\t\t\t&lt;path /&gt;\n\t\t\t&lt;query /&gt;\n\t\t\t&lt;istransparent&gt;0&lt;/istransparent&gt;\n\t\t\t&lt;hideicon&gt;0&lt;/hideicon&gt;\n\t\t\t&lt;forbidforward&gt;0&lt;/forbidforward&gt;\n\t\t&lt;/liteapp&gt;\n\t\t&lt;opensdk_share_is_modified&gt;0&lt;/opensdk_share_is_modified&gt;\n\t&lt;/appmsg&gt;\n\t&lt;fromusername&gt;wxid_ctp9qffuf14b21&lt;/fromusername&gt;\n\t&lt;scene&gt;0&lt;/scene&gt;\n\t&lt;appinfo&gt;\n\t\t&lt;version&gt;1&lt;/version&gt;\n\t\t&lt;appname&gt;&lt;/appname&gt;\n\t&lt;/appinfo&gt;\n\t&lt;commenturl&gt;&lt;/commenturl&gt;\n&lt;/msg&gt;\n</content>\n\t\t</refermsg>\n\t</appmsg>\n\t<fromusername>wxid_ikxxrwasicud11</fromusername>\n\t<scene>0</scene>\n\t<appinfo>\n\t\t<version>1</version>\n\t\t<appname></appname>\n\t</appinfo>\n\t<commenturl></commenturl>\n</msg>\n'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1753850084, 'MsgSource': '<msgsource>\n\t<alnode>\n\t\t<fr>1</fr>\n\t</alnode>\n\t<sec_msg_node>\n\t\t<uuid>de604bae57bc173addd2fc0269087e7f_</uuid>\n\t\t<risk-file-flag />\n\t\t<risk-file-md5-list />\n\t</sec_msg_node>\n\t<silence>1</silence>\n\t<membercount>145</membercount>\n\t<signature>N0_V1_39uaCqST|v1_8LEBF3Jg</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'NewMsgId': 2043524934423189409, 'MsgSeq': 871411612}
2025-07-30 12:34:33 | DEBUG | 从群聊消息中提取发送者: wxid_ikxxrwasicud11
2025-07-30 12:34:33 | DEBUG | 使用已解析的XML处理引用消息
2025-07-30 12:34:33 | INFO | 收到引用消息: 消息ID:1834375599 来自:27852221909@chatroom 发送人:wxid_ikxxrwasicud11 内容:1 引用类型:49
2025-07-30 12:34:33 | INFO | [DouBaoImageToImage] 收到引用消息: 1
2025-07-30 12:34:33 | INFO | [TimerTask] 收到引用消息调试信息:
2025-07-30 12:34:33 | INFO |   - 消息内容: 1
2025-07-30 12:34:33 | INFO |   - 群组ID: 27852221909@chatroom
2025-07-30 12:34:33 | INFO |   - 发送人: wxid_ikxxrwasicud11
2025-07-30 12:34:33 | INFO |   - 引用信息: {'MsgType': 49, 'Content': 'wxid_ctp9qffuf14b21:\n<?xml version="1.0"?>\n<msg>\n\t<appmsg appid="" sdkver="0">\n\t\t<title>派对永不停Beat-1000钻石等你领取！</title>\n\t\t<des />\n\t\t<username />\n\t\t<action>view</action>\n\t\t<type>33</type>\n\t\t<showtype>0</showtype>\n\t\t<content />\n\t\t<url>https://mp.weixin.qq.com/mp/waerrpage?appid=wxa708de63ee4a2353&amp;type=upgrade&amp;upgradetype=3#wechat_redirect</url>\n\t\t<lowurl />\n\t\t<forwardflag>0</forwardflag>\n\t\t<dataurl />\n\t\t<lowdataurl />\n\t\t<contentattr>0</contentattr>\n\t\t<streamvideo>\n\t\t\t<streamvideourl />\n\t\t\t<streamvideototaltime>0</streamvideototaltime>\n\t\t\t<streamvideotitle />\n\t\t\t<streamvideowording />\n\t\t\t<streamvideoweburl />\n\t\t\t<streamvideothumburl />\n\t\t\t<streamvideoaduxinfo />\n\t\t\t<streamvideopublishid />\n\t\t</streamvideo>\n\t\t<canvasPageItem>\n\t\t\t<canvasPageXml><![CDATA[]]></canvasPageXml>\n\t\t</canvasPageItem>\n\t\t<appattach>\n\t\t\t<attachid />\n\t\t\t<cdnthumburl>3057020100044b304902010002047d46407602032f59e102040ec7587d02046884c7aa042462663935616431332d383031372d346635362d396666632d3739306131306232616435310204051408030201000405004c4e6100</cdnthumburl>\n\t\t\t<cdnthumbmd5>4ad6c0fff6a62ab56feba5cde2706304</cdnthumbmd5>\n\t\t\t<cdnthumblength>75094</cdnthumblength>\n\t\t\t<cdnthumbheight>576</cdnthumbheight>\n\t\t\t<cdnthumbwidth>720</cdnthumbwidth>\n\t\t\t<cdnthumbaeskey>df4cde14ffe3af3d73bd04a36193d515</cdnthumbaeskey>\n\t\t\t<aeskey>df4cde14ffe3af3d73bd04a36193d515</aeskey>\n\t\t\t<encryver>1</encryver>\n\t\t\t<fileext />\n\t\t\t<islargefilemsg>0</islargefilemsg>\n\t\t</appattach>\n\t\t<extinfo />\n\t\t<androidsource>3</androidsource>\n\t\t<sourceusername>gh_25eb09d7bc53@app</sourceusername>\n\t\t<sourcedisplayname>唱舞星愿站</sourcedisplayname>\n\t\t<commenturl />\n\t\t<thumburl />\n\t\t<mediatagname />\n\t\t<messageaction><![CDATA[]]></messageaction>\n\t\t<messageext><![CDATA[]]></messageext>\n\t\t<emoticongift>\n\t\t\t<packageflag>0</packageflag>\n\t\t\t<packageid />\n\t\t</emoticongift>\n\t\t<emoticonshared>\n\t\t\t<packageflag>0</packageflag>\n\t\t\t<packageid />\n\t\t</emoticonshared>\n\t\t<designershared>\n\t\t\t<designeruin>0</designeruin>\n\t\t\t<designername>null</designername>\n\t\t\t<designerrediretcturl><![CDATA[null]]></designerrediretcturl>\n\t\t</designershared>\n\t\t<emotionpageshared>\n\t\t\t<tid>0</tid>\n\t\t\t<title>null</title>\n\t\t\t<desc>null</desc>\n\t\t\t<iconUrl><![CDATA[null]]></iconUrl>\n\t\t\t<secondUrl>null</secondUrl>\n\t\t\t<pageType>0</pageType>\n\t\t\t<setKey>null</setKey>\n\t\t</emotionpageshared>\n\t\t<webviewshared>\n\t\t\t<shareUrlOriginal />\n\t\t\t<shareUrlOpen />\n\t\t\t<jsAppId />\n\t\t\t<publisherId>wxapp_wxa708de63ee4a2353pages/Activity/teamUp/index.html?key=uK1I2mwn&amp;share_unionid=oA7D81elKyhcQQ8srAn4tlz-eSJU&amp;task_id=&amp;team_id=24069</publisherId>\n\t\t\t<publisherReqId />\n\t\t</webviewshared>\n\t\t<template_id />\n\t\t<md5>4ad6c0fff6a62ab56feba5cde2706304</md5>\n\t\t<finderGuarantee>\n\t\t\t<scene><![CDATA[0]]></scene>\n\t\t</finderGuarantee>\n\t\t<websearch>\n\t\t\t<rec_category>0</rec_category>\n\t\t\t<channelId>0</channelId>\n\t\t</websearch>\n\t\t<weappinfo>\n\t\t\t<pagepath><![CDATA[pages/Activity/teamUp/index.html?key=uK1I2mwn&share_unionid=oA7D81elKyhcQQ8srAn4tlz-eSJU&task_id=&team_id=24069]]></pagepath>\n\t\t\t<username>gh_25eb09d7bc53@app</username>\n\t\t\t<appid>wxa708de63ee4a2353</appid>\n\t\t\t<version>16</version>\n\t\t\t<type>2</type>\n\t\t\t<weappiconurl><![CDATA[http://wx.qlogo.cn/mmhead/FMajU52WvbEE6SVVwqdPicO6GjWVVIhhLiaZK2NvCJgt3epAkLg7A3nkRTia23B9lSt0pXQJz0viaR4/96]]></weappiconurl>\n\t\t\t<shareId><![CDATA[1_wxa708de63ee4a2353_f17385d137a5603e10bd7aa0a53ee459_1753451238_2]]></shareId>\n\t\t\t<appservicetype>0</appservicetype>\n\t\t\t<secflagforsinglepagemode>0</secflagforsinglepagemode>\n\t\t\t<videopageinfo>\n\t\t\t\t<thumbwidth>720</thumbwidth>\n\t\t\t\t<thumbheight>576</thumbheight>\n\t\t\t\t<fromopensdk>0</fromopensdk>\n\t\t\t</videopageinfo>\n\t\t</weappinfo>\n\t\t<statextstr />\n\t\t<musicShareItem>\n\t\t\t<musicDuration>0</musicDuration>\n\t\t</musicShareItem>\n\t\t<finderLiveProductShare>\n\t\t\t<finderLiveID><![CDATA[]]></finderLiveID>\n\t\t\t<finderUsername><![CDATA[]]></finderUsername>\n\t\t\t<finderObjectID><![CDATA[]]></finderObjectID>\n\t\t\t<finderNonceID><![CDATA[]]></finderNonceID>\n\t\t\t<liveStatus><![CDATA[]]></liveStatus>\n\t\t\t<appId><![CDATA[]]></appId>\n\t\t\t<pagePath><![CDATA[]]></pagePath>\n\t\t\t<productId><![CDATA[]]></productId>\n\t\t\t<coverUrl><![CDATA[]]></coverUrl>\n\t\t\t<productTitle><![CDATA[]]></productTitle>\n\t\t\t<marketPrice><![CDATA[0]]></marketPrice>\n\t\t\t<sellingPrice><![CDATA[0]]></sellingPrice>\n\t\t\t<platformHeadImg><![CDATA[]]></platformHeadImg>\n\t\t\t<platformName><![CDATA[]]></platformName>\n\t\t\t<shopWindowId><![CDATA[]]></shopWindowId>\n\t\t\t<flashSalePrice><![CDATA[0]]></flashSalePrice>\n\t\t\t<flashSaleEndTime><![CDATA[0]]></flashSaleEndTime>\n\t\t\t<ecSource><![CDATA[]]></ecSource>\n\t\t\t<sellingPriceWording><![CDATA[]]></sellingPriceWording>\n\t\t\t<platformIconURL><![CDATA[]]></platformIconURL>\n\t\t\t<firstProductTagURL><![CDATA[]]></firstProductTagURL>\n\t\t\t<firstProductTagAspectRatioString><![CDATA[0.0]]></firstProductTagAspectRatioString>\n\t\t\t<secondProductTagURL><![CDATA[]]></secondProductTagURL>\n\t\t\t<secondProductTagAspectRatioString><![CDATA[0.0]]></secondProductTagAspectRatioString>\n\t\t\t<firstGuaranteeWording><![CDATA[]]></firstGuaranteeWording>\n\t\t\t<secondGuaranteeWording><![CDATA[]]></secondGuaranteeWording>\n\t\t\t<thirdGuaranteeWording><![CDATA[]]></thirdGuaranteeWording>\n\t\t\t<isPriceBeginShow>false</isPriceBeginShow>\n\t\t\t<lastGMsgID><![CDATA[]]></lastGMsgID>\n\t\t\t<promoterKey><![CDATA[]]></promoterKey>\n\t\t\t<discountWording><![CDATA[]]></discountWording>\n\t\t\t<priceSuffixDescription><![CDATA[]]></priceSuffixDescription>\n\t\t\t<productCardKey><![CDATA[]]></productCardKey>\n\t\t\t<isWxShop><![CDATA[]]></isWxShop>\n\t\t\t<brandIconUrl><![CDATA[]]></brandIconUrl>\n\t\t\t<rIconUrl><![CDATA[]]></rIconUrl>\n\t\t\t<rIconUrlDarkMode><![CDATA[]]></rIconUrlDarkMode>\n\t\t\t<topShopIconUrl><![CDATA[]]></topShopIconUrl>\n\t\t\t<topShopIconUrlDarkMode><![CDATA[]]></topShopIconUrlDarkMode>\n\t\t\t<simplifyTopShopIconUrl><![CDATA[]]></simplifyTopShopIconUrl>\n\t\t\t<simplifyTopShopIconUrlDarkmode><![CDATA[]]></simplifyTopShopIconUrlDarkmode>\n\t\t\t<topShopIconWidth><![CDATA[0]]></topShopIconWidth>\n\t\t\t<topShopIconHeight><![CDATA[0]]></topShopIconHeight>\n\t\t\t<simplifyTopShopIconWidth><![CDATA[0]]></simplifyTopShopIconWidth>\n\t\t\t<simplifyTopShopIconHeight><![CDATA[0]]></simplifyTopShopIconHeight>\n\t\t\t<showBoxItemStringList />\n\t\t</finderLiveProductShare>\n\t\t<finderOrder>\n\t\t\t<appID><![CDATA[]]></appID>\n\t\t\t<orderID><![CDATA[]]></orderID>\n\t\t\t<path><![CDATA[]]></path>\n\t\t\t<priceWording><![CDATA[]]></priceWording>\n\t\t\t<stateWording><![CDATA[]]></stateWording>\n\t\t\t<productImageURL><![CDATA[]]></productImageURL>\n\t\t\t<products><![CDATA[]]></products>\n\t\t\t<productsCount><![CDATA[0]]></productsCount>\n\t\t\t<orderType><![CDATA[0]]></orderType>\n\t\t\t<newPriceWording><![CDATA[]]></newPriceWording>\n\t\t\t<newStateWording><![CDATA[]]></newStateWording>\n\t\t\t<useNewWording><![CDATA[0]]></useNewWording>\n\t\t</finderOrder>\n\t\t<finderShopWindowShare>\n\t\t\t<finderUsername><![CDATA[]]></finderUsername>\n\t\t\t<avatar><![CDATA[]]></avatar>\n\t\t\t<nickname><![CDATA[]]></nickname>\n\t\t\t<commodityInStockCount><![CDATA[]]></commodityInStockCount>\n\t\t\t<appId><![CDATA[]]></appId>\n\t\t\t<path><![CDATA[]]></path>\n\t\t\t<appUsername><![CDATA[]]></appUsername>\n\t\t\t<query><![CDATA[]]></query>\n\t\t\t<liteAppId><![CDATA[]]></liteAppId>\n\t\t\t<liteAppPath><![CDATA[]]></liteAppPath>\n\t\t\t<liteAppQuery><![CDATA[]]></liteAppQuery>\n\t\t\t<platformTagURL><![CDATA[]]></platformTagURL>\n\t\t\t<saleWording><![CDATA[]]></saleWording>\n\t\t\t<lastGMsgID><![CDATA[]]></lastGMsgID>\n\t\t\t<profileTypeWording><![CDATA[]]></profileTypeWording>\n\t\t\t<saleWordingExtra><![CDATA[]]></saleWordingExtra>\n\t\t\t<isWxShop><![CDATA[]]></isWxShop>\n\t\t\t<platformIconUrl><![CDATA[]]></platformIconUrl>\n\t\t\t<brandIconUrl><![CDATA[]]></brandIconUrl>\n\t\t\t<description><![CDATA[]]></description>\n\t\t\t<backgroundUrl><![CDATA[]]></backgroundUrl>\n\t\t\t<darkModePlatformIconUrl><![CDATA[]]></darkModePlatformIconUrl>\n\t\t\t<rIconUrl><![CDATA[]]></rIconUrl>\n\t\t\t<rIconUrlDarkMode><![CDATA[]]></rIconUrlDarkMode>\n\t\t\t<rWords><![CDATA[]]></rWords>\n\t\t\t<topShopIconUrl><![CDATA[]]></topShopIconUrl>\n\t\t\t<topShopIconUrlDarkMode><![CDATA[]]></topShopIconUrlDarkMode>\n\t\t\t<simplifyTopShopIconUrl><![CDATA[]]></simplifyTopShopIconUrl>\n\t\t\t<simplifyTopShopIconUrlDarkmode><![CDATA[]]></simplifyTopShopIconUrlDarkmode>\n\t\t\t<topShopIconWidth><![CDATA[0]]></topShopIconWidth>\n\t\t\t<topShopIconHeight><![CDATA[0]]></topShopIconHeight>\n\t\t\t<simplifyTopShopIconWidth><![CDATA[0]]></simplifyTopShopIconWidth>\n\t\t\t<simplifyTopShopIconHeight><![CDATA[0]]></simplifyTopShopIconHeight>\n\t\t\t<reputationInfo>\n\t\t\t\t<hasReputationInfo>0</hasReputationInfo>\n\t\t\t\t<reputationScore>0</reputationScore>\n\t\t\t\t<reputationWording />\n\t\t\t\t<reputationTextColor />\n\t\t\t\t<reputationLevelWording />\n\t\t\t\t<reputationBackgroundColor />\n\t\t\t</reputationInfo>\n\t\t\t<productImageURLList />\n\t\t</finderShopWindowShare>\n\t\t<findernamecard>\n\t\t\t<username />\n\t\t\t<avatar><![CDATA[]]></avatar>\n\t\t\t<nickname />\n\t\t\t<auth_job />\n\t\t\t<auth_icon>0</auth_icon>\n\t\t\t<auth_icon_url />\n\t\t\t<ecSource><![CDATA[]]></ecSource>\n\t\t\t<lastGMsgID><![CDATA[]]></lastGMsgID>\n\t\t</findernamecard>\n\t\t<finderGuarantee>\n\t\t\t<scene><![CDATA[0]]></scene>\n\t\t</finderGuarantee>\n\t\t<directshare>0</directshare>\n\t\t<gamecenter>\n\t\t\t<namecard>\n\t\t\t\t<iconUrl />\n\t\t\t\t<name />\n\t\t\t\t<desc />\n\t\t\t\t<tail />\n\t\t\t\t<jumpUrl />\n\t\t\t\t<liteappId />\n\t\t\t\t<liteappPath />\n\t\t\t\t<liteappQuery />\n\t\t\t\t<liteappMinVersion />\n\t\t\t</namecard>\n\t\t</gamecenter>\n\t\t<patMsg>\n\t\t\t<chatUser />\n\t\t\t<records>\n\t\t\t\t<recordNum>0</recordNum>\n\t\t\t</records>\n\t\t</patMsg>\n\t\t<secretmsg>\n\t\t\t<issecretmsg>0</issecretmsg>\n\t\t</secretmsg>\n\t\t<referfromscene>0</referfromscene>\n\t\t<gameshare>\n\t\t\t<liteappext>\n\t\t\t\t<liteappbizdata />\n\t\t\t\t<priority>0</priority>\n\t\t\t</liteappext>\n\t\t\t<appbrandext>\n\t\t\t\t<litegameinfo />\n\t\t\t\t<priority>-1</priority>\n\t\t\t</appbrandext>\n\t\t\t<gameshareid />\n\t\t\t<sharedata />\n\t\t\t<isvideo>0</isvideo>\n\t\t\t<duration>-1</duration>\n\t\t\t<isexposed>0</isexposed>\n\t\t\t<readtext />\n\t\t</gameshare>\n\t\t<tingChatRoomItem>\n\t\t\t<type>0</type>\n\t\t\t<categoryItem>null</categoryItem>\n\t\t\t<categoryId />\n\t\t</tingChatRoomItem>\n\t\t<mpsharetrace>\n\t\t\t<hasfinderelement>0</hasfinderelement>\n\t\t\t<lastgmsgid />\n\t\t</mpsharetrace>\n\t\t<wxgamecard>\n\t\t\t<framesetname />\n\t\t\t<mbcarddata />\n\t\t\t<minpkgversion />\n\t\t\t<clientextinfo />\n\t\t\t<mbcardheight>0</mbcardheight>\n\t\t\t<isoldversion>0</isoldversion>\n\t\t</wxgamecard>\n\t\t<ecskfcard>\n\t\t\t<framesetname />\n\t\t\t<mbcarddata />\n\t\t\t<minupdateunixtimestamp>0</minupdateunixtimestamp>\n\t\t\t<needheader>false</needheader>\n\t\t\t<summary />\n\t\t</ecskfcard>\n\t\t<liteapp>\n\t\t\t<id>null</id>\n\t\t\t<path />\n\t\t\t<query />\n\t\t\t<istransparent>0</istransparent>\n\t\t\t<hideicon>0</hideicon>\n\t\t\t<forbidforward>0</forbidforward>\n\t\t</liteapp>\n\t\t<opensdk_share_is_modified>0</opensdk_share_is_modified>\n\t</appmsg>\n\t<fromusername>wxid_ctp9qffuf14b21</fromusername>\n\t<scene>0</scene>\n\t<appinfo>\n\t\t<version>1</version>\n\t\t<appname></appname>\n\t</appinfo>\n\t<commenturl></commenturl>\n</msg>\n', 'Msgid': '*******************', 'NewMsgId': '*******************', 'ToWxid': 'wxid_4usgcju5ey9q29', 'FromWxid': '27852221909@chatroom', 'Nickname': 'Z⁰', 'MsgSource': '<msgsource>\n    <alnode>\n        <fr>4</fr>\n    </alnode>\n    <tmp_node>\n        <publisher-id />\n    </tmp_node>\n    <sec_msg_node>\n        <uuid>18a5c10a576c7cbf68e59b2364696a91_</uuid>\n        <risk-file-flag />\n        <risk-file-md5-list />\n        <alnode>\n            <fr>1</fr>\n        </alnode>\n    </sec_msg_node>\n    <silence>1</silence>\n    <membercount>145</membercount>\n    <signature>N0_V1_cdN3zDJJ|v1_Xc8vJdst</signature>\n</msgsource>\n', 'Createtime': '1753532331', 'SenderWxid': 'wxid_ikxxrwasicud11'}
2025-07-30 12:34:33 | INFO |   - 引用消息ID: 
2025-07-30 12:34:33 | INFO |   - 引用消息类型: 
2025-07-30 12:34:33 | INFO |   - 引用消息内容: wxid_ctp9qffuf14b21:
<?xml version="1.0"?>
<msg>
	<appmsg appid="" sdkver="0">
		<title>派对永不停Beat-1000钻石等你领取！</title>
		<des />
		<username />
		<action>view</action>
		<type>33</type>
		<showtype>0</showtype>
		<content />
		<url>https://mp.weixin.qq.com/mp/waerrpage?appid=wxa708de63ee4a2353&amp;type=upgrade&amp;upgradetype=3#wechat_redirect</url>
		<lowurl />
		<forwardflag>0</forwardflag>
		<dataurl />
		<lowdataurl />
		<contentattr>0</contentattr>
		<streamvideo>
			<streamvideourl />
			<streamvideototaltime>0</streamvideototaltime>
			<streamvideotitle />
			<streamvideowording />
			<streamvideoweburl />
			<streamvideothumburl />
			<streamvideoaduxinfo />
			<streamvideopublishid />
		</streamvideo>
		<canvasPageItem>
			<canvasPageXml><![CDATA[]]></canvasPageXml>
		</canvasPageItem>
		<appattach>
			<attachid />
			<cdnthumburl>3057020100044b304902010002047d46407602032f59e102040ec7587d02046884c7aa042462663935616431332d383031372d346635362d396666632d3739306131306232616435310204051408030201000405004c4e6100</cdnthumburl>
			<cdnthumbmd5>4ad6c0fff6a62ab56feba5cde2706304</cdnthumbmd5>
			<cdnthumblength>75094</cdnthumblength>
			<cdnthumbheight>576</cdnthumbheight>
			<cdnthumbwidth>720</cdnthumbwidth>
			<cdnthumbaeskey>df4cde14ffe3af3d73bd04a36193d515</cdnthumbaeskey>
			<aeskey>df4cde14ffe3af3d73bd04a36193d515</aeskey>
			<encryver>1</encryver>
			<fileext />
			<islargefilemsg>0</islargefilemsg>
		</appattach>
		<extinfo />
		<androidsource>3</androidsource>
		<sourceusername>gh_25eb09d7bc53@app</sourceusername>
		<sourcedisplayname>唱舞星愿站</sourcedisplayname>
		<commenturl />
		<thumburl />
		<mediatagname />
		<messageaction><![CDATA[]]></messageaction>
		<messageext><![CDATA[]]></messageext>
		<emoticongift>
			<packageflag>0</packageflag>
			<packageid />
		</emoticongift>
		<emoticonshared>
			<packageflag>0</packageflag>
			<packageid />
		</emoticonshared>
		<designershared>
			<designeruin>0</designeruin>
			<designername>null</designername>
			<designerrediretcturl><![CDATA[null]]></designerrediretcturl>
		</designershared>
		<emotionpageshared>
			<tid>0</tid>
			<title>null</title>
			<desc>null</desc>
			<iconUrl><![CDATA[null]]></iconUrl>
			<secondUrl>null</secondUrl>
			<pageType>0</pageType>
			<setKey>null</setKey>
		</emotionpageshared>
		<webviewshared>
			<shareUrlOriginal />
			<shareUrlOpen />
			<jsAppId />
			<publisherId>wxapp_wxa708de63ee4a2353pages/Activity/teamUp/index.html?key=uK1I2mwn&amp;share_unionid=oA7D81elKyhcQQ8srAn4tlz-eSJU&amp;task_id=&amp;team_id=24069</publisherId>
			<publisherReqId />
		</webviewshared>
		<template_id />
		<md5>4ad6c0fff6a62ab56feba5cde2706304</md5>
		<finderGuarantee>
			<scene><![CDATA[0]]></scene>
		</finderGuarantee>
		<websearch>
			<rec_category>0</rec_category>
			<channelId>0</channelId>
		</websearch>
		<weappinfo>
			<pagepath><![CDATA[pages/Activity/teamUp/index.html?key=uK1I2mwn&share_unionid=oA7D81elKyhcQQ8srAn4tlz-eSJU&task_id=&team_id=24069]]></pagepath>
			<username>gh_25eb09d7bc53@app</username>
			<appid>wxa708de63ee4a2353</appid>
			<version>16</version>
			<type>2</type>
			<weappiconurl><![CDATA[http://wx.qlogo.cn/mmhead/FMajU52WvbEE6SVVwqdPicO6GjWVVIhhLiaZK2NvCJgt3epAkLg7A3nkRTia23B9lSt0pXQJz0viaR4/96]]></weappiconurl>
			<shareId><![CDATA[1_wxa708de63ee4a2353_f17385d137a5603e10bd7aa0a53ee459_1753451238_2]]></shareId>
			<appservicetype>0</appservicetype>
			<secflagforsinglepagemode>0</secflagforsinglepagemode>
			<videopageinfo>
				<thumbwidth>720</thumbwidth>
				<thumbheight>576</thumbheight>
				<fromopensdk>0</fromopensdk>
			</videopageinfo>
		</weappinfo>
		<statextstr />
		<musicShareItem>
			<musicDuration>0</musicDuration>
		</musicShareItem>
		<finderLiveProductShare>
			<finderLiveID><![CDATA[]]></finderLiveID>
			<finderUsername><![CDATA[]]></finderUsername>
			<finderObjectID><![CDATA[]]></finderObjectID>
			<finderNonceID><![CDATA[]]></finderNonceID>
			<liveStatus><![CDATA[]]></liveStatus>
			<appId><![CDATA[]]></appId>
			<pagePath><![CDATA[]]></pagePath>
			<productId><![CDATA[]]></productId>
			<coverUrl><![CDATA[]]></coverUrl>
			<productTitle><![CDATA[]]></productTitle>
			<marketPrice><![CDATA[0]]></marketPrice>
			<sellingPrice><![CDATA[0]]></sellingPrice>
			<platformHeadImg><![CDATA[]]></platformHeadImg>
			<platformName><![CDATA[]]></platformName>
			<shopWindowId><![CDATA[]]></shopWindowId>
			<flashSalePrice><![CDATA[0]]></flashSalePrice>
			<flashSaleEndTime><![CDATA[0]]></flashSaleEndTime>
			<ecSource><![CDATA[]]></ecSource>
			<sellingPriceWording><![CDATA[]]></sellingPriceWording>
			<platformIconURL><![CDATA[]]></platformIconURL>
			<firstProductTagURL><![CDATA[]]></firstProductTagURL>
			<firstProductTagAspectRatioString><![CDATA[0.0]]></firstProductTagAspectRatioString>
			<secondProductTagURL><![CDATA[]]></secondProductTagURL>
			<secondProductTagAspectRatioString><![CDATA[0.0]]></secondProductTagAspectRatioString>
			<firstGuaranteeWording><![CDATA[]]></firstGuaranteeWording>
			<secondGuaranteeWording><![CDATA[]]></secondGuaranteeWording>
			<thirdGuaranteeWording><![CDATA[]]></thirdGuaranteeWording>
			<isPriceBeginShow>false</isPriceBeginShow>
			<lastGMsgID><![CDATA[]]></lastGMsgID>
			<promoterKey><![CDATA[]]></promoterKey>
			<discountWording><![CDATA[]]></discountWording>
			<priceSuffixDescription><![CDATA[]]></priceSuffixDescription>
			<productCardKey><![CDATA[]]></productCardKey>
			<isWxShop><![CDATA[]]></isWxShop>
			<brandIconUrl><![CDATA[]]></brandIconUrl>
			<rIconUrl><![CDATA[]]></rIconUrl>
			<rIconUrlDarkMode><![CDATA[]]></rIconUrlDarkMode>
			<topShopIconUrl><![CDATA[]]></topShopIconUrl>
			<topShopIconUrlDarkMode><![CDATA[]]></topShopIconUrlDarkMode>
			<simplifyTopShopIconUrl><![CDATA[]]></simplifyTopShopIconUrl>
			<simplifyTopShopIconUrlDarkmode><![CDATA[]]></simplifyTopShopIconUrlDarkmode>
			<topShopIconWidth><![CDATA[0]]></topShopIconWidth>
			<topShopIconHeight><![CDATA[0]]></topShopIconHeight>
			<simplifyTopShopIconWidth><![CDATA[0]]></simplifyTopShopIconWidth>
			<simplifyTopShopIconHeight><![CDATA[0]]></simplifyTopShopIconHeight>
			<showBoxItemStringList />
		</finderLiveProductShare>
		<finderOrder>
			<appID><![CDATA[]]></appID>
			<orderID><![CDATA[]]></orderID>
			<path><![CDATA[]]></path>
			<priceWording><![CDATA[]]></priceWording>
			<stateWording><![CDATA[]]></stateWording>
			<productImageURL><![CDATA[]]></productImageURL>
			<products><![CDATA[]]></products>
			<productsCount><![CDATA[0]]></productsCount>
			<orderType><![CDATA[0]]></orderType>
			<newPriceWording><![CDATA[]]></newPriceWording>
			<newStateWording><![CDATA[]]></newStateWording>
			<useNewWording><![CDATA[0]]></useNewWording>
		</finderOrder>
		<finderShopWindowShare>
			<finderUsername><![CDATA[]]></finderUsername>
			<avatar><![CDATA[]]></avatar>
			<nickname><![CDATA[]]></nickname>
			<commodityInStockCount><![CDATA[]]></commodityInStockCount>
			<appId><![CDATA[]]></appId>
			<path><![CDATA[]]></path>
			<appUsername><![CDATA[]]></appUsername>
			<query><![CDATA[]]></query>
			<liteAppId><![CDATA[]]></liteAppId>
			<liteAppPath><![CDATA[]]></liteAppPath>
			<liteAppQuery><![CDATA[]]></liteAppQuery>
			<platformTagURL><![CDATA[]]></platformTagURL>
			<saleWording><![CDATA[]]></saleWording>
			<lastGMsgID><![CDATA[]]></lastGMsgID>
			<profileTypeWording><![CDATA[]]></profileTypeWording>
			<saleWordingExtra><![CDATA[]]></saleWordingExtra>
			<isWxShop><![CDATA[]]></isWxShop>
			<platformIconUrl><![CDATA[]]></platformIconUrl>
			<brandIconUrl><![CDATA[]]></brandIconUrl>
			<description><![CDATA[]]></description>
			<backgroundUrl><![CDATA[]]></backgroundUrl>
			<darkModePlatformIconUrl><![CDATA[]]></darkModePlatformIconUrl>
			<rIconUrl><![CDATA[]]></rIconUrl>
			<rIconUrlDarkMode><![CDATA[]]></rIconUrlDarkMode>
			<rWords><![CDATA[]]></rWords>
			<topShopIconUrl><![CDATA[]]></topShopIconUrl>
			<topShopIconUrlDarkMode><![CDATA[]]></topShopIconUrlDarkMode>
			<simplifyTopShopIconUrl><![CDATA[]]></simplifyTopShopIconUrl>
			<simplifyTopShopIconUrlDarkmode><![CDATA[]]></simplifyTopShopIconUrlDarkmode>
			<topShopIconWidth><![CDATA[0]]></topShopIconWidth>
			<topShopIconHeight><![CDATA[0]]></topShopIconHeight>
			<simplifyTopShopIconWidth><![CDATA[0]]></simplifyTopShopIconWidth>
			<simplifyTopShopIconHeight><![CDATA[0]]></simplifyTopShopIconHeight>
			<reputationInfo>
				<hasReputationInfo>0</hasReputationInfo>
				<reputationScore>0</reputationScore>
				<reputationWording />
				<reputationTextColor />
				<reputationLevelWording />
				<reputationBackgroundColor />
			</reputationInfo>
			<productImageURLList />
		</finderShopWindowShare>
		<findernamecard>
			<username />
			<avatar><![CDATA[]]></avatar>
			<nickname />
			<auth_job />
			<auth_icon>0</auth_icon>
			<auth_icon_url />
			<ecSource><![CDATA[]]></ecSource>
			<lastGMsgID><![CDATA[]]></lastGMsgID>
		</findernamecard>
		<finderGuarantee>
			<scene><![CDATA[0]]></scene>
		</finderGuarantee>
		<directshare>0</directshare>
		<gamecenter>
			<namecard>
				<iconUrl />
				<name />
				<desc />
				<tail />
				<jumpUrl />
				<liteappId />
				<liteappPath />
				<liteappQuery />
				<liteappMinVersion />
			</namecard>
		</gamecenter>
		<patMsg>
			<chatUser />
			<records>
				<recordNum>0</recordNum>
			</records>
		</patMsg>
		<secretmsg>
			<issecretmsg>0</issecretmsg>
		</secretmsg>
		<referfromscene>0</referfromscene>
		<gameshare>
			<liteappext>
				<liteappbizdata />
				<priority>0</priority>
			</liteappext>
			<appbrandext>
				<litegameinfo />
				<priority>-1</priority>
			</appbrandext>
			<gameshareid />
			<sharedata />
			<isvideo>0</isvideo>
			<duration>-1</duration>
			<isexposed>0</isexposed>
			<readtext />
		</gameshare>
		<tingChatRoomItem>
			<type>0</type>
			<categoryItem>null</categoryItem>
			<categoryId />
		</tingChatRoomItem>
		<mpsharetrace>
			<hasfinderelement>0</hasfinderelement>
			<lastgmsgid />
		</mpsharetrace>
		<wxgamecard>
			<framesetname />
			<mbcarddata />
			<minpkgversion />
			<clientextinfo />
			<mbcardheight>0</mbcardheight>
			<isoldversion>0</isoldversion>
		</wxgamecard>
		<ecskfcard>
			<framesetname />
			<mbcarddata />
			<minupdateunixtimestamp>0</minupdateunixtimestamp>
			<needheader>false</needheader>
			<summary />
		</ecskfcard>
		<liteapp>
			<id>null</id>
			<path />
			<query />
			<istransparent>0</istransparent>
			<hideicon>0</hideicon>
			<forbidforward>0</forbidforward>
		</liteapp>
		<opensdk_share_is_modified>0</opensdk_share_is_modified>
	</appmsg>
	<fromusername>wxid_ctp9qffuf14b21</fromusername>
	<scene>0</scene>
	<appinfo>
		<version>1</version>
		<appname></appname>
	</appinfo>
	<commenturl></commenturl>
</msg>

2025-07-30 12:34:33 | INFO |   - 引用消息发送人: wxid_ikxxrwasicud11
2025-07-30 12:35:00 | DEBUG | 收到消息: {'MsgId': 746435453, 'FromUserName': {'string': '27852221909@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 49, 'Content': {'string': 'wxid_ikxxrwasicud11:\n<?xml version="1.0"?>\n<msg>\n\t<appmsg appid="" sdkver="0">\n\t\t<title>狂欢能量站-拍立得等你拿！</title>\n\t\t<des>唱舞星愿站</des>\n\t\t<action />\n\t\t<type>33</type>\n\t\t<showtype>0</showtype>\n\t\t<soundtype>0</soundtype>\n\t\t<mediatagname />\n\t\t<messageext />\n\t\t<messageaction />\n\t\t<content />\n\t\t<contentattr>0</contentattr>\n\t\t<url />\n\t\t<lowurl />\n\t\t<dataurl />\n\t\t<lowdataurl />\n\t\t<songalbumurl />\n\t\t<songlyric />\n\t\t<template_id />\n\t\t<appattach>\n\t\t\t<totallen>0</totallen>\n\t\t\t<attachid />\n\t\t\t<emoticonmd5 />\n\t\t\t<fileext />\n\t\t\t<cdnthumburl>3057020100044b30490201000204062b4e3002032f5aa902049f99697102046889a100042463626337396538332d656139662d343366352d393833362d3734656632373535316636390204052408030201000405004c543d00</cdnthumburl>\n\t\t\t<cdnthumbmd5>6c33da0b3f908983303d41bdc9008c2c</cdnthumbmd5>\n\t\t\t<cdnthumblength>337997</cdnthumblength>\n\t\t\t<cdnthumbwidth>1080</cdnthumbwidth>\n\t\t\t<cdnthumbheight>4056</cdnthumbheight>\n\t\t\t<cdnthumbaeskey>a3289b849e5e51baa7e8af48cf029315</cdnthumbaeskey>\n\t\t\t<aeskey>a3289b849e5e51baa7e8af48cf029315</aeskey>\n\t\t\t<encryver>0</encryver>\n\t\t</appattach>\n\t\t<extinfo />\n\t\t<sourceusername>gh_25eb09d7bc53@app</sourceusername>\n\t\t<sourcedisplayname>唱舞星愿站</sourcedisplayname>\n\t\t<thumburl />\n\t\t<md5 />\n\t\t<statextstr />\n\t\t<weappinfo>\n\t\t\t<username><![CDATA[gh_25eb09d7bc53@app]]></username>\n\t\t\t<appid><![CDATA[]]></appid>\n\t\t\t<type>2</type>\n\t\t\t<version>16</version>\n\t\t\t<weappiconurl><![CDATA[http://mmbiz.qpic.cn/mmbiz_png/Y4yibCmxrbX6aCNIhj0BH1CXuE8chunB9AN5fnaRJUMRGRicxkh8crn7P1VCQuhV4GB3KCkE4e5ibmXVEibxaoIJuA/640?wx_fmt=png&wxfrom=200]]></weappiconurl>\n\t\t\t<pagepath><![CDATA[pages/Activity/lotteryDraw/index.html?key=ZR1Fj6id&inviteId=oA7D81SRhmAwPJuDQ0Pw39LWcWoY&taskId=]]></pagepath>\n\t\t\t<pkginfo>\n\t\t\t\t<type>0</type>\n\t\t\t\t<md5><![CDATA[]]></md5>\n\t\t\t</pkginfo>\n\t\t\t<wadynamicpageinfo>\n\t\t\t\t<shouldUseDynamicPage>0</shouldUseDynamicPage>\n\t\t\t\t<cacheKey><![CDATA[]]></cacheKey>\n\t\t\t</wadynamicpageinfo>\n\t\t\t<appservicetype>0</appservicetype>\n\t\t</weappinfo>\n\t</appmsg>\n\t<fromusername>wxid_ikxxrwasicud11</fromusername>\n\t<scene>0</scene>\n\t<appinfo>\n\t\t<version>1</version>\n\t\t<appname></appname>\n\t</appinfo>\n\t<commenturl></commenturl>\n</msg>\n'}, 'Status': 3, 'ImgStatus': 2, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1753850112, 'MsgSource': '<msgsource>\n\t<alnode>\n\t\t<fr>5</fr>\n\t</alnode>\n\t<sec_msg_node>\n\t\t<uuid>48843abb949d72180edd5829eb4a5d1c_</uuid>\n\t\t<risk-file-flag />\n\t\t<risk-file-md5-list />\n\t</sec_msg_node>\n\t<silence>1</silence>\n\t<membercount>145</membercount>\n\t<signature>N0_V1_cFkPV0EI|v1_FwzZ4DY8</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'NewMsgId': 577012614842926964, 'MsgSeq': 871411613}
2025-07-30 12:35:00 | DEBUG | 从群聊消息中提取发送者: wxid_ikxxrwasicud11
2025-07-30 12:35:00 | DEBUG | XML消息完整内容:
<?xml version="1.0"?>
<msg>
	<appmsg appid="" sdkver="0">
		<title>狂欢能量站-拍立得等你拿！</title>
		<des>唱舞星愿站</des>
		<action />
		<type>33</type>
		<showtype>0</showtype>
		<soundtype>0</soundtype>
		<mediatagname />
		<messageext />
		<messageaction />
		<content />
		<contentattr>0</contentattr>
		<url />
		<lowurl />
		<dataurl />
		<lowdataurl />
		<songalbumurl />
		<songlyric />
		<template_id />
		<appattach>
			<totallen>0</totallen>
			<attachid />
			<emoticonmd5 />
			<fileext />
			<cdnthumburl>3057020100044b30490201000204062b4e3002032f5aa902049f99697102046889a100042463626337396538332d656139662d343366352d393833362d3734656632373535316636390204052408030201000405004c543d00</cdnthumburl>
			<cdnthumbmd5>6c33da0b3f908983303d41bdc9008c2c</cdnthumbmd5>
			<cdnthumblength>337997</cdnthumblength>
			<cdnthumbwidth>1080</cdnthumbwidth>
			<cdnthumbheight>4056</cdnthumbheight>
			<cdnthumbaeskey>a3289b849e5e51baa7e8af48cf029315</cdnthumbaeskey>
			<aeskey>a3289b849e5e51baa7e8af48cf029315</aeskey>
			<encryver>0</encryver>
		</appattach>
		<extinfo />
		<sourceusername>gh_25eb09d7bc53@app</sourceusername>
		<sourcedisplayname>唱舞星愿站</sourcedisplayname>
		<thumburl />
		<md5 />
		<statextstr />
		<weappinfo>
			<username><![CDATA[gh_25eb09d7bc53@app]]></username>
			<appid><![CDATA[]]></appid>
			<type>2</type>
			<version>16</version>
			<weappiconurl><![CDATA[http://mmbiz.qpic.cn/mmbiz_png/Y4yibCmxrbX6aCNIhj0BH1CXuE8chunB9AN5fnaRJUMRGRicxkh8crn7P1VCQuhV4GB3KCkE4e5ibmXVEibxaoIJuA/640?wx_fmt=png&wxfrom=200]]></weappiconurl>
			<pagepath><![CDATA[pages/Activity/lotteryDraw/index.html?key=ZR1Fj6id&inviteId=oA7D81SRhmAwPJuDQ0Pw39LWcWoY&taskId=]]></pagepath>
			<pkginfo>
				<type>0</type>
				<md5><![CDATA[]]></md5>
			</pkginfo>
			<wadynamicpageinfo>
				<shouldUseDynamicPage>0</shouldUseDynamicPage>
				<cacheKey><![CDATA[]]></cacheKey>
			</wadynamicpageinfo>
			<appservicetype>0</appservicetype>
		</weappinfo>
	</appmsg>
	<fromusername>wxid_ikxxrwasicud11</fromusername>
	<scene>0</scene>
	<appinfo>
		<version>1</version>
		<appname></appname>
	</appinfo>
	<commenturl></commenturl>
</msg>

2025-07-30 12:35:00 | DEBUG | XML消息类型: 33
2025-07-30 12:35:00 | DEBUG | XML消息标题: 狂欢能量站-拍立得等你拿！
2025-07-30 12:35:00 | DEBUG | XML消息描述: 唱舞星愿站
2025-07-30 12:35:00 | DEBUG | 附件信息 totallen: 0
2025-07-30 12:35:00 | DEBUG | 附件信息 cdnthumburl: 3057020100044b30490201000204062b4e3002032f5aa902049f99697102046889a100042463626337396538332d656139662d343366352d393833362d3734656632373535316636390204052408030201000405004c543d00
2025-07-30 12:35:00 | DEBUG | 附件信息 cdnthumbmd5: 6c33da0b3f908983303d41bdc9008c2c
2025-07-30 12:35:00 | DEBUG | 附件信息 cdnthumblength: 337997
2025-07-30 12:35:00 | DEBUG | 附件信息 cdnthumbwidth: 1080
2025-07-30 12:35:00 | DEBUG | 附件信息 cdnthumbheight: 4056
2025-07-30 12:35:00 | DEBUG | 附件信息 cdnthumbaeskey: a3289b849e5e51baa7e8af48cf029315
2025-07-30 12:35:00 | DEBUG | 附件信息 aeskey: a3289b849e5e51baa7e8af48cf029315
2025-07-30 12:35:00 | DEBUG | 附件信息 encryver: 0
2025-07-30 12:35:00 | INFO | 未知的XML消息类型: 33
2025-07-30 12:35:00 | INFO | 消息标题: 狂欢能量站-拍立得等你拿！
2025-07-30 12:35:00 | INFO | 消息描述: 唱舞星愿站
2025-07-30 12:35:00 | INFO | 消息URL: N/A
2025-07-30 12:35:00 | INFO | 完整XML内容:
<?xml version="1.0"?>
<msg>
	<appmsg appid="" sdkver="0">
		<title>狂欢能量站-拍立得等你拿！</title>
		<des>唱舞星愿站</des>
		<action />
		<type>33</type>
		<showtype>0</showtype>
		<soundtype>0</soundtype>
		<mediatagname />
		<messageext />
		<messageaction />
		<content />
		<contentattr>0</contentattr>
		<url />
		<lowurl />
		<dataurl />
		<lowdataurl />
		<songalbumurl />
		<songlyric />
		<template_id />
		<appattach>
			<totallen>0</totallen>
			<attachid />
			<emoticonmd5 />
			<fileext />
			<cdnthumburl>3057020100044b30490201000204062b4e3002032f5aa902049f99697102046889a100042463626337396538332d656139662d343366352d393833362d3734656632373535316636390204052408030201000405004c543d00</cdnthumburl>
			<cdnthumbmd5>6c33da0b3f908983303d41bdc9008c2c</cdnthumbmd5>
			<cdnthumblength>337997</cdnthumblength>
			<cdnthumbwidth>1080</cdnthumbwidth>
			<cdnthumbheight>4056</cdnthumbheight>
			<cdnthumbaeskey>a3289b849e5e51baa7e8af48cf029315</cdnthumbaeskey>
			<aeskey>a3289b849e5e51baa7e8af48cf029315</aeskey>
			<encryver>0</encryver>
		</appattach>
		<extinfo />
		<sourceusername>gh_25eb09d7bc53@app</sourceusername>
		<sourcedisplayname>唱舞星愿站</sourcedisplayname>
		<thumburl />
		<md5 />
		<statextstr />
		<weappinfo>
			<username><![CDATA[gh_25eb09d7bc53@app]]></username>
			<appid><![CDATA[]]></appid>
			<type>2</type>
			<version>16</version>
			<weappiconurl><![CDATA[http://mmbiz.qpic.cn/mmbiz_png/Y4yibCmxrbX6aCNIhj0BH1CXuE8chunB9AN5fnaRJUMRGRicxkh8crn7P1VCQuhV4GB3KCkE4e5ibmXVEibxaoIJuA/640?wx_fmt=png&wxfrom=200]]></weappiconurl>
			<pagepath><![CDATA[pages/Activity/lotteryDraw/index.html?key=ZR1Fj6id&inviteId=oA7D81SRhmAwPJuDQ0Pw39LWcWoY&taskId=]]></pagepath>
			<pkginfo>
				<type>0</type>
				<md5><![CDATA[]]></md5>
			</pkginfo>
			<wadynamicpageinfo>
				<shouldUseDynamicPage>0</shouldUseDynamicPage>
				<cacheKey><![CDATA[]]></cacheKey>
			</wadynamicpageinfo>
			<appservicetype>0</appservicetype>
		</weappinfo>
	</appmsg>
	<fromusername>wxid_ikxxrwasicud11</fromusername>
	<scene>0</scene>
	<appinfo>
		<version>1</version>
		<appname></appname>
	</appinfo>
	<commenturl></commenturl>
</msg>

2025-07-30 12:35:09 | DEBUG | 收到消息: {'MsgId': 1865756093, 'FromUserName': {'string': '27852221909@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 1, 'Content': {'string': 'wxid_ikxxrwasicud11:\n目前只能点这个'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1753850121, 'MsgSource': '<msgsource>\n\t<alnode>\n\t\t<fr>1</fr>\n\t</alnode>\n\t<silence>1</silence>\n\t<membercount>145</membercount>\n\t<signature>N0_V1_elWMBNjb|v1_ArZqRURe</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'NewMsgId': 7801322009237729410, 'MsgSeq': 871411614}
2025-07-30 12:35:09 | INFO | 收到文本消息: 消息ID:1865756093 来自:27852221909@chatroom 发送人:wxid_ikxxrwasicud11 @:[] 内容:目前只能点这个
2025-07-30 12:35:09 | DEBUG | 处理消息内容: '目前只能点这个'
2025-07-30 12:35:09 | DEBUG | 消息内容 '目前只能点这个' 不匹配任何命令，忽略
2025-07-30 12:35:42 | DEBUG | 收到消息: {'MsgId': 910094458, 'FromUserName': {'string': '27852221909@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 1, 'Content': {'string': 'wxid_sf4ikl8sczaj21:\n没次数'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1753850154, 'MsgSource': '<msgsource>\n\t<bizflag>0</bizflag>\n\t<pua>1</pua>\n\t<eggIncluded>1</eggIncluded>\n\t<silence>1</silence>\n\t<membercount>145</membercount>\n\t<signature>N0_V1_jCUG7N4g|v1_B/2St1wr</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'NewMsgId': 3159825738164343803, 'MsgSeq': 871411615}
2025-07-30 12:35:42 | INFO | 收到文本消息: 消息ID:910094458 来自:27852221909@chatroom 发送人:wxid_sf4ikl8sczaj21 @:[] 内容:没次数
2025-07-30 12:35:42 | DEBUG | 处理消息内容: '没次数'
2025-07-30 12:35:42 | DEBUG | 消息内容 '没次数' 不匹配任何命令，忽略
2025-07-30 12:35:45 | DEBUG | 收到消息: {'MsgId': 1676187970, 'FromUserName': {'string': '27852221909@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 47, 'Content': {'string': 'wxid_sf4ikl8sczaj21:\n<msg><emoji fromusername = "wxid_sf4ikl8sczaj21" tousername = "27852221909@chatroom" type="2" idbuffer="media:0_0" md5="9b6939e10dc83ee8e3532d8f3fd41f56" len = "3190658" productid="" androidmd5="9b6939e10dc83ee8e3532d8f3fd41f56" androidlen="3190658" s60v3md5 = "9b6939e10dc83ee8e3532d8f3fd41f56" s60v3len="3190658" s60v5md5 = "9b6939e10dc83ee8e3532d8f3fd41f56" s60v5len="3190658" cdnurl = "http://vweixinf.tc.qq.com/110/20401/stodownload?m=9b6939e10dc83ee8e3532d8f3fd41f56&amp;filekey=30440201010430302e02016e0402534804203962363933396531306463383365653865333533326438663366643431663536020330af82040d00000004627466730000000132&amp;hy=SH&amp;storeid=267a99a5300074baa15ba23260000006e01004fb15348229411b156b6554ee&amp;ef=1&amp;bizid=1022" designerid = "" thumburl = "" encrypturl = "http://vweixinf.tc.qq.com/110/20402/stodownload?m=4535787746daaf231ee33cca29315efb&amp;filekey=30440201010430302e02016e0402534804203435333537383737343664616166323331656533336363613239333135656662020330af90040d00000004627466730000000132&amp;hy=SH&amp;storeid=267a99a53000bd98015ba23260000006e02004fb25348229411b156b6554ff&amp;ef=2&amp;bizid=1022" aeskey= "458f4b0f44944843b8a88527541c8879" externurl = "http://vweixinf.tc.qq.com/110/20403/stodownload?m=84160db55ead707587c6fc645bcb9c54&amp;filekey=30440201010430302e02016e0402534804203834313630646235356561643730373538376336666336343562636239633534020301a140040d00000004627466730000000132&amp;hy=SH&amp;storeid=267a99a5400003ac015ba23260000006e03004fb35348229411b156b655514&amp;ef=3&amp;bizid=1022" externmd5 = "08f42e4ca3a91ba4fbce571bb2b963f3" width= "240" height= "240" tpurl= "" tpauthkey= "" attachedtext= "" attachedtextcolor= "" lensid= "" emojiattr= "" linkid= "" desc= "" ></emoji> <gameext type="0" content="0" ></gameext> </msg>'}, 'Status': 3, 'ImgStatus': 2, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1753850157, 'MsgSource': '<msgsource>\n\t<silence>1</silence>\n\t<membercount>145</membercount>\n\t<signature>N0_V1_u+B9NanI|v1_rDH/m0k8</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'NewMsgId': 5707844707390947224, 'MsgSeq': 871411616}
2025-07-30 12:35:45 | INFO | 收到表情消息: 消息ID:1676187970 来自:27852221909@chatroom 发送人:wxid_sf4ikl8sczaj21 MD5:9b6939e10dc83ee8e3532d8f3fd41f56 大小:3190658
2025-07-30 12:35:45 | DEBUG | [DoubaoImageRecognition] 缓存表情消息: 5707844707390947224
2025-07-30 12:36:23 | DEBUG | 收到消息: {'MsgId': 224494066, 'FromUserName': {'string': '27852221909@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 1, 'Content': {'string': 'wxid_95kdb9mikm3o22:\n8月1才开始呢'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1753850195, 'MsgSource': '<msgsource>\n\t<pua>1</pua>\n\t<eggIncluded>1</eggIncluded>\n\t<silence>1</silence>\n\t<membercount>145</membercount>\n\t<signature>N0_V1_35jGcq3j|v1_sjj8Zs0f</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'NewMsgId': 739771913387197871, 'MsgSeq': 871411617}
2025-07-30 12:36:23 | INFO | 收到文本消息: 消息ID:224494066 来自:27852221909@chatroom 发送人:wxid_95kdb9mikm3o22 @:[] 内容:8月1才开始呢
2025-07-30 12:36:23 | DEBUG | 处理消息内容: '8月1才开始呢'
2025-07-30 12:36:23 | DEBUG | 消息内容 '8月1才开始呢' 不匹配任何命令，忽略
2025-07-30 12:37:00 | DEBUG | 收到消息: {'MsgId': 1282733182, 'FromUserName': {'string': '27852221909@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 3, 'Content': {'string': 'wxid_ikxxrwasicud11:\n<?xml version="1.0"?>\n<msg>\n\t<img aeskey="97ce5330ff1d58e9d9d4fc8ac0fd05c9" encryver="1" cdnthumbaeskey="97ce5330ff1d58e9d9d4fc8ac0fd05c9" cdnthumburl="3057020100044b30490201000204062b4e3002032f5aa902049f99697102046889a178042464656161376239332d656266642d343630332d393035332d3862386661366531313131320204052438010201000405004c53d900" cdnthumblength="1773" cdnthumbheight="102" cdnthumbwidth="180" cdnmidheight="0" cdnmidwidth="0" cdnhdheight="0" cdnhdwidth="0" cdnmidimgurl="3057020100044b30490201000204062b4e3002032f5aa902049f99697102046889a178042464656161376239332d656266642d343630332d393035332d3862386661366531313131320204052438010201000405004c53d900" length="15534" cdnbigimgurl="3057020100044b30490201000204062b4e3002032f5aa902049f99697102046889a178042464656161376239332d656266642d343630332d393035332d3862386661366531313131320204052438010201000405004c53d900" hdlength="15534" md5="96a01bdc4fccf2e8570e8c3d8c60ad68">\n\t\t<secHashInfoBase64 />\n\t\t<live>\n\t\t\t<duration>0</duration>\n\t\t\t<size>0</size>\n\t\t\t<md5 />\n\t\t\t<fileid />\n\t\t\t<hdsize>0</hdsize>\n\t\t\t<hdmd5 />\n\t\t\t<hdfileid />\n\t\t\t<stillimagetimems>0</stillimagetimems>\n\t\t</live>\n\t</img>\n\t<platform_signature />\n\t<imgdatahash />\n\t<ImgSourceInfo>\n\t\t<ImgSourceUrl />\n\t\t<BizType>0</BizType>\n\t</ImgSourceInfo>\n</msg>\n'}, 'Status': 3, 'ImgStatus': 2, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1753850232, 'MsgSource': '<msgsource>\n\t<img_file_name>5181fee9-f077-4f45-8c6d-bb98b79d5591.png</img_file_name>\n\t<alnode>\n\t\t<fr>1</fr>\n\t\t<cf>3</cf>\n\t</alnode>\n\t<sec_msg_node>\n\t\t<uuid>6e1f8bd4220242ba0f0a4d2ea273e5b5_</uuid>\n\t\t<risk-file-flag />\n\t\t<risk-file-md5-list />\n\t</sec_msg_node>\n\t<silence>1</silence>\n\t<membercount>145</membercount>\n\t<signature>N0_V1_RcJDeovV|v1_gswsSiEM</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'NewMsgId': 5827453429828788043, 'MsgSeq': 871411618}
2025-07-30 12:37:00 | INFO | 收到图片消息: 消息ID:1282733182 来自:27852221909@chatroom 发送人:wxid_ikxxrwasicud11 XML:<?xml version="1.0"?><msg><img aeskey="97ce5330ff1d58e9d9d4fc8ac0fd05c9" encryver="1" cdnthumbaeskey="97ce5330ff1d58e9d9d4fc8ac0fd05c9" cdnthumburl="3057020100044b30490201000204062b4e3002032f5aa902049f99697102046889a178042464656161376239332d656266642d343630332d393035332d3862386661366531313131320204052438010201000405004c53d900" cdnthumblength="1773" cdnthumbheight="102" cdnthumbwidth="180" cdnmidheight="0" cdnmidwidth="0" cdnhdheight="0" cdnhdwidth="0" cdnmidimgurl="3057020100044b30490201000204062b4e3002032f5aa902049f99697102046889a178042464656161376239332d656266642d343630332d393035332d3862386661366531313131320204052438010201000405004c53d900" length="15534" cdnbigimgurl="3057020100044b30490201000204062b4e3002032f5aa902049f99697102046889a178042464656161376239332d656266642d343630332d393035332d3862386661366531313131320204052438010201000405004c53d900" hdlength="15534" md5="96a01bdc4fccf2e8570e8c3d8c60ad68"><secHashInfoBase64 /><live><duration>0</duration><size>0</size><md5 /><fileid /><hdsize>0</hdsize><hdmd5 /><hdfileid /><stillimagetimems>0</stillimagetimems></live></img><platform_signature /><imgdatahash /><ImgSourceInfo><ImgSourceUrl /><BizType>0</BizType></ImgSourceInfo></msg>
2025-07-30 12:37:00 | INFO | [ImageEcho] 保存图片信息成功，当前群 27852221909@chatroom 已存储 5 张图片
2025-07-30 12:37:00 | INFO | [TimerTask] 缓存图片消息: 1282733182
2025-07-30 12:37:09 | DEBUG | 收到消息: {'MsgId': 1915893823, 'FromUserName': {'string': '27852221909@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 1, 'Content': {'string': 'wxid_ikxxrwasicud11:\n哦莫 断签'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1753850241, 'MsgSource': '<msgsource>\n\t<alnode>\n\t\t<fr>1</fr>\n\t</alnode>\n\t<silence>1</silence>\n\t<membercount>145</membercount>\n\t<signature>N0_V1_2ePmT5bc|v1_rjk0DTnL</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'NewMsgId': 200223063382922527, 'MsgSeq': 871411619}
2025-07-30 12:37:09 | INFO | 收到文本消息: 消息ID:1915893823 来自:27852221909@chatroom 发送人:wxid_ikxxrwasicud11 @:[] 内容:哦莫 断签
2025-07-30 12:37:09 | DEBUG | 处理消息内容: '哦莫 断签'
2025-07-30 12:37:09 | DEBUG | 消息内容 '哦莫 断签' 不匹配任何命令，忽略
2025-07-30 12:37:19 | DEBUG | 收到消息: {'MsgId': 2135395288, 'FromUserName': {'string': '27852221909@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 49, 'Content': {'string': 'wxid_ikxxrwasicud11:\n<?xml version="1.0"?>\n<msg>\n\t<appmsg appid="" sdkver="0">\n\t\t<title>派对邀请函-签到解锁6周年专属称号</title>\n\t\t<des>唱舞星愿站</des>\n\t\t<action />\n\t\t<type>33</type>\n\t\t<showtype>0</showtype>\n\t\t<soundtype>0</soundtype>\n\t\t<mediatagname />\n\t\t<messageext />\n\t\t<messageaction />\n\t\t<content />\n\t\t<contentattr>0</contentattr>\n\t\t<url>https://mp.weixin.qq.com/mp/waerrpage?appid=wxa708de63ee4a2353&amp;type=upgrade&amp;upgradetype=3#wechat_redirect</url>\n\t\t<lowurl />\n\t\t<dataurl />\n\t\t<lowdataurl />\n\t\t<songalbumurl />\n\t\t<songlyric />\n\t\t<template_id />\n\t\t<appattach>\n\t\t\t<totallen>0</totallen>\n\t\t\t<attachid />\n\t\t\t<emoticonmd5 />\n\t\t\t<fileext />\n\t\t\t<cdnthumburl>3057020100044b30490201000204062b4e3002032f5aa902049f99697102046889a18b042433383262363761342d613237392d343136352d393836632d3730333037323664643431360204052808030201000405004c4d3500</cdnthumburl>\n\t\t\t<cdnthumbmd5>b103f9a58ba6aa2b9dd2cc65522eef2e</cdnthumbmd5>\n\t\t\t<cdnthumblength>158369</cdnthumblength>\n\t\t\t<cdnthumbwidth>720</cdnthumbwidth>\n\t\t\t<cdnthumbheight>576</cdnthumbheight>\n\t\t\t<cdnthumbaeskey>1340fc50c81aa3386ab312bff26ea5d3</cdnthumbaeskey>\n\t\t\t<aeskey>1340fc50c81aa3386ab312bff26ea5d3</aeskey>\n\t\t\t<encryver>0</encryver>\n\t\t</appattach>\n\t\t<extinfo />\n\t\t<sourceusername>gh_25eb09d7bc53@app</sourceusername>\n\t\t<sourcedisplayname>唱舞星愿站</sourcedisplayname>\n\t\t<thumburl />\n\t\t<md5>b103f9a58ba6aa2b9dd2cc65522eef2e</md5>\n\t\t<statextstr />\n\t\t<weappinfo>\n\t\t\t<username><![CDATA[gh_25eb09d7bc53@app]]></username>\n\t\t\t<appid><![CDATA[wxa708de63ee4a2353]]></appid>\n\t\t\t<type>2</type>\n\t\t\t<version>16</version>\n\t\t\t<weappiconurl><![CDATA[http://mmbiz.qpic.cn/mmbiz_png/Y4yibCmxrbX6aCNIhj0BH1CXuE8chunB9AN5fnaRJUMRGRicxkh8crn7P1VCQuhV4GB3KCkE4e5ibmXVEibxaoIJuA/640?wx_fmt=png&wxfrom=200]]></weappiconurl>\n\t\t\t<pagepath><![CDATA[pages/Activity/signIn/index.html?key=jSqgSKXV&inviteId=oA7D81ZZCWDzx-UmiFhXMVnngr4M&taskId=]]></pagepath>\n\t\t\t<shareId><![CDATA[0_wxa708de63ee4a2353_3c2a58020487dba191654db6f34404ec_1753532350_0]]></shareId>\n\t\t\t<appservicetype>0</appservicetype>\n\t\t</weappinfo>\n\t</appmsg>\n\t<fromusername>wxid_ikxxrwasicud11</fromusername>\n\t<scene>0</scene>\n\t<appinfo>\n\t\t<version>1</version>\n\t\t<appname></appname>\n\t</appinfo>\n\t<commenturl></commenturl>\n</msg>\n'}, 'Status': 3, 'ImgStatus': 2, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1753850251, 'MsgSource': '<msgsource>\n\t<alnode>\n\t\t<fr>4</fr>\n\t</alnode>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n\t<sec_msg_node>\n\t\t<uuid>a584aae0f79bade59009b5c2a08d68fb_</uuid>\n\t\t<risk-file-flag />\n\t\t<risk-file-md5-list />\n\t</sec_msg_node>\n\t<silence>1</silence>\n\t<membercount>145</membercount>\n\t<signature>N0_V1_XVhcwwlh|v1_VwZAzFPy</signature>\n</msgsource>\n', 'NewMsgId': 4717117390751744414, 'MsgSeq': 871411620}
2025-07-30 12:37:19 | DEBUG | 从群聊消息中提取发送者: wxid_ikxxrwasicud11
2025-07-30 12:37:19 | DEBUG | XML消息完整内容:
<?xml version="1.0"?>
<msg>
	<appmsg appid="" sdkver="0">
		<title>派对邀请函-签到解锁6周年专属称号</title>
		<des>唱舞星愿站</des>
		<action />
		<type>33</type>
		<showtype>0</showtype>
		<soundtype>0</soundtype>
		<mediatagname />
		<messageext />
		<messageaction />
		<content />
		<contentattr>0</contentattr>
		<url>https://mp.weixin.qq.com/mp/waerrpage?appid=wxa708de63ee4a2353&amp;type=upgrade&amp;upgradetype=3#wechat_redirect</url>
		<lowurl />
		<dataurl />
		<lowdataurl />
		<songalbumurl />
		<songlyric />
		<template_id />
		<appattach>
			<totallen>0</totallen>
			<attachid />
			<emoticonmd5 />
			<fileext />
			<cdnthumburl>3057020100044b30490201000204062b4e3002032f5aa902049f99697102046889a18b042433383262363761342d613237392d343136352d393836632d3730333037323664643431360204052808030201000405004c4d3500</cdnthumburl>
			<cdnthumbmd5>b103f9a58ba6aa2b9dd2cc65522eef2e</cdnthumbmd5>
			<cdnthumblength>158369</cdnthumblength>
			<cdnthumbwidth>720</cdnthumbwidth>
			<cdnthumbheight>576</cdnthumbheight>
			<cdnthumbaeskey>1340fc50c81aa3386ab312bff26ea5d3</cdnthumbaeskey>
			<aeskey>1340fc50c81aa3386ab312bff26ea5d3</aeskey>
			<encryver>0</encryver>
		</appattach>
		<extinfo />
		<sourceusername>gh_25eb09d7bc53@app</sourceusername>
		<sourcedisplayname>唱舞星愿站</sourcedisplayname>
		<thumburl />
		<md5>b103f9a58ba6aa2b9dd2cc65522eef2e</md5>
		<statextstr />
		<weappinfo>
			<username><![CDATA[gh_25eb09d7bc53@app]]></username>
			<appid><![CDATA[wxa708de63ee4a2353]]></appid>
			<type>2</type>
			<version>16</version>
			<weappiconurl><![CDATA[http://mmbiz.qpic.cn/mmbiz_png/Y4yibCmxrbX6aCNIhj0BH1CXuE8chunB9AN5fnaRJUMRGRicxkh8crn7P1VCQuhV4GB3KCkE4e5ibmXVEibxaoIJuA/640?wx_fmt=png&wxfrom=200]]></weappiconurl>
			<pagepath><![CDATA[pages/Activity/signIn/index.html?key=jSqgSKXV&inviteId=oA7D81ZZCWDzx-UmiFhXMVnngr4M&taskId=]]></pagepath>
			<shareId><![CDATA[0_wxa708de63ee4a2353_3c2a58020487dba191654db6f34404ec_1753532350_0]]></shareId>
			<appservicetype>0</appservicetype>
		</weappinfo>
	</appmsg>
	<fromusername>wxid_ikxxrwasicud11</fromusername>
	<scene>0</scene>
	<appinfo>
		<version>1</version>
		<appname></appname>
	</appinfo>
	<commenturl></commenturl>
</msg>

2025-07-30 12:37:19 | DEBUG | XML消息类型: 33
2025-07-30 12:37:19 | DEBUG | XML消息标题: 派对邀请函-签到解锁6周年专属称号
2025-07-30 12:37:19 | DEBUG | XML消息描述: 唱舞星愿站
2025-07-30 12:37:19 | DEBUG | 附件信息 totallen: 0
2025-07-30 12:37:19 | DEBUG | 附件信息 cdnthumburl: 3057020100044b30490201000204062b4e3002032f5aa902049f99697102046889a18b042433383262363761342d613237392d343136352d393836632d3730333037323664643431360204052808030201000405004c4d3500
2025-07-30 12:37:19 | DEBUG | 附件信息 cdnthumbmd5: b103f9a58ba6aa2b9dd2cc65522eef2e
2025-07-30 12:37:19 | DEBUG | 附件信息 cdnthumblength: 158369
2025-07-30 12:37:19 | DEBUG | 附件信息 cdnthumbwidth: 720
2025-07-30 12:37:19 | DEBUG | 附件信息 cdnthumbheight: 576
2025-07-30 12:37:19 | DEBUG | 附件信息 cdnthumbaeskey: 1340fc50c81aa3386ab312bff26ea5d3
2025-07-30 12:37:19 | DEBUG | 附件信息 aeskey: 1340fc50c81aa3386ab312bff26ea5d3
2025-07-30 12:37:19 | DEBUG | 附件信息 encryver: 0
2025-07-30 12:37:19 | DEBUG | XML消息URL: https://mp.weixin.qq.com/mp/waerrpage?appid=wxa708de63ee4a2353&type=upgrade&upgradetype=3#wechat_redirect
2025-07-30 12:37:19 | INFO | 未知的XML消息类型: 33
2025-07-30 12:37:19 | INFO | 消息标题: 派对邀请函-签到解锁6周年专属称号
2025-07-30 12:37:19 | INFO | 消息描述: 唱舞星愿站
2025-07-30 12:37:19 | INFO | 消息URL: https://mp.weixin.qq.com/mp/waerrpage?appid=wxa708de63ee4a2353&type=upgrade&upgradetype=3#wechat_redirect
2025-07-30 12:37:19 | INFO | 完整XML内容:
<?xml version="1.0"?>
<msg>
	<appmsg appid="" sdkver="0">
		<title>派对邀请函-签到解锁6周年专属称号</title>
		<des>唱舞星愿站</des>
		<action />
		<type>33</type>
		<showtype>0</showtype>
		<soundtype>0</soundtype>
		<mediatagname />
		<messageext />
		<messageaction />
		<content />
		<contentattr>0</contentattr>
		<url>https://mp.weixin.qq.com/mp/waerrpage?appid=wxa708de63ee4a2353&amp;type=upgrade&amp;upgradetype=3#wechat_redirect</url>
		<lowurl />
		<dataurl />
		<lowdataurl />
		<songalbumurl />
		<songlyric />
		<template_id />
		<appattach>
			<totallen>0</totallen>
			<attachid />
			<emoticonmd5 />
			<fileext />
			<cdnthumburl>3057020100044b30490201000204062b4e3002032f5aa902049f99697102046889a18b042433383262363761342d613237392d343136352d393836632d3730333037323664643431360204052808030201000405004c4d3500</cdnthumburl>
			<cdnthumbmd5>b103f9a58ba6aa2b9dd2cc65522eef2e</cdnthumbmd5>
			<cdnthumblength>158369</cdnthumblength>
			<cdnthumbwidth>720</cdnthumbwidth>
			<cdnthumbheight>576</cdnthumbheight>
			<cdnthumbaeskey>1340fc50c81aa3386ab312bff26ea5d3</cdnthumbaeskey>
			<aeskey>1340fc50c81aa3386ab312bff26ea5d3</aeskey>
			<encryver>0</encryver>
		</appattach>
		<extinfo />
		<sourceusername>gh_25eb09d7bc53@app</sourceusername>
		<sourcedisplayname>唱舞星愿站</sourcedisplayname>
		<thumburl />
		<md5>b103f9a58ba6aa2b9dd2cc65522eef2e</md5>
		<statextstr />
		<weappinfo>
			<username><![CDATA[gh_25eb09d7bc53@app]]></username>
			<appid><![CDATA[wxa708de63ee4a2353]]></appid>
			<type>2</type>
			<version>16</version>
			<weappiconurl><![CDATA[http://mmbiz.qpic.cn/mmbiz_png/Y4yibCmxrbX6aCNIhj0BH1CXuE8chunB9AN5fnaRJUMRGRicxkh8crn7P1VCQuhV4GB3KCkE4e5ibmXVEibxaoIJuA/640?wx_fmt=png&wxfrom=200]]></weappiconurl>
			<pagepath><![CDATA[pages/Activity/signIn/index.html?key=jSqgSKXV&inviteId=oA7D81ZZCWDzx-UmiFhXMVnngr4M&taskId=]]></pagepath>
			<shareId><![CDATA[0_wxa708de63ee4a2353_3c2a58020487dba191654db6f34404ec_1753532350_0]]></shareId>
			<appservicetype>0</appservicetype>
		</weappinfo>
	</appmsg>
	<fromusername>wxid_ikxxrwasicud11</fromusername>
	<scene>0</scene>
	<appinfo>
		<version>1</version>
		<appname></appname>
	</appinfo>
	<commenturl></commenturl>
</msg>

2025-07-30 12:37:27 | DEBUG | 收到消息: {'MsgId': *********, 'FromUserName': {'string': '27852221909@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 1, 'Content': {'string': 'wxid_ikxxrwasicud11:\n这得连续签到7天'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1753850259, 'MsgSource': '<msgsource>\n\t<alnode>\n\t\t<fr>1</fr>\n\t</alnode>\n\t<silence>1</silence>\n\t<membercount>145</membercount>\n\t<signature>N0_V1_QktZA8hw|v1_dojFOnsE</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'NewMsgId': 7400287282265722530, 'MsgSeq': 871411621}
2025-07-30 12:37:27 | INFO | 收到文本消息: 消息ID:********* 来自:27852221909@chatroom 发送人:wxid_ikxxrwasicud11 @:[] 内容:这得连续签到7天
2025-07-30 12:37:27 | DEBUG | 处理消息内容: '这得连续签到7天'
2025-07-30 12:37:27 | DEBUG | 消息内容 '这得连续签到7天' 不匹配任何命令，忽略
2025-07-30 12:38:49 | DEBUG | 收到消息: {'MsgId': 609733609, 'FromUserName': {'string': '27852221909@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 1, 'Content': {'string': 'wxid_sf4ikl8sczaj21:\n好叭'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1753850341, 'MsgSource': '<msgsource>\n\t<bizflag>0</bizflag>\n\t<pua>1</pua>\n\t<eggIncluded>1</eggIncluded>\n\t<silence>1</silence>\n\t<membercount>145</membercount>\n\t<signature>N0_V1_xkuLWUOA|v1_MwgBEZdz</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'NewMsgId': 6296820853261540181, 'MsgSeq': 871411622}
2025-07-30 12:38:49 | INFO | 收到文本消息: 消息ID:609733609 来自:27852221909@chatroom 发送人:wxid_sf4ikl8sczaj21 @:[] 内容:好叭
2025-07-30 12:38:49 | DEBUG | 处理消息内容: '好叭'
2025-07-30 12:38:49 | DEBUG | 消息内容 '好叭' 不匹配任何命令，忽略
2025-07-30 12:38:52 | DEBUG | 收到消息: {'MsgId': 1574477084, 'FromUserName': {'string': '***********@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 1, 'Content': {'string': 'xiaomaochong:\n太贵了'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1753850344, 'MsgSource': '<msgsource>\n\t<pua>1</pua>\n\t<silence>0</silence>\n\t<membercount>73</membercount>\n\t<signature>N0_V1_EfpVjUHP|v1_zpa5tUOa</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'PushContent': '小爱 : 太贵了', 'NewMsgId': 8192869212188803017, 'MsgSeq': 871411623}
2025-07-30 12:38:52 | INFO | 收到文本消息: 消息ID:1574477084 来自:***********@chatroom 发送人:xiaomaochong @:[] 内容:太贵了
2025-07-30 12:38:52 | DEBUG | 处理消息内容: '太贵了'
2025-07-30 12:38:52 | DEBUG | 消息内容 '太贵了' 不匹配任何命令，忽略
2025-07-30 12:39:09 | DEBUG | 收到消息: {'MsgId': 958040852, 'FromUserName': {'string': '***********@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 1, 'Content': {'string': 'xiaomaochong:\n又不能画大尺度的'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1753850361, 'MsgSource': '<msgsource>\n\t<pua>1</pua>\n\t<silence>0</silence>\n\t<membercount>73</membercount>\n\t<signature>N0_V1_CZhmj9xB|v1_kcnblpCk</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'PushContent': '小爱 : 又不能画大尺度的', 'NewMsgId': 1150708027838297549, 'MsgSeq': 871411624}
2025-07-30 12:39:09 | INFO | 收到文本消息: 消息ID:958040852 来自:***********@chatroom 发送人:xiaomaochong @:[] 内容:又不能画大尺度的
2025-07-30 12:39:09 | DEBUG | 处理消息内容: '又不能画大尺度的'
2025-07-30 12:39:09 | DEBUG | 消息内容 '又不能画大尺度的' 不匹配任何命令，忽略
2025-07-30 12:39:48 | DEBUG | 收到消息: {'MsgId': 1794164967, 'FromUserName': {'string': '***********@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 1, 'Content': {'string': 'xiaomaochong:\n画一个36d大长腿中国美女，海滩照'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1753850400, 'MsgSource': '<msgsource>\n\t<pua>1</pua>\n\t<silence>0</silence>\n\t<membercount>73</membercount>\n\t<signature>N0_V1_04a+x+Q/|v1_UXpoF66G</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'PushContent': '小爱 : 画一个36d大长腿中国美女，海滩照', 'NewMsgId': 4599387946452216762, 'MsgSeq': 871411625}
2025-07-30 12:39:48 | INFO | 收到文本消息: 消息ID:1794164967 来自:***********@chatroom 发送人:xiaomaochong @:[] 内容:画一个36d大长腿中国美女，海滩照
2025-07-30 12:39:48 | DEBUG | 处理消息内容: '画一个36d大长腿中国美女，海滩照'
2025-07-30 12:39:48 | DEBUG | 消息内容 '画一个36d大长腿中国美女，海滩照' 不匹配任何命令，忽略
2025-07-30 12:41:21 | DEBUG | 收到消息: {'MsgId': 80628851, 'FromUserName': {'string': '***********@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 1, 'Content': {'string': 'wxid_srknxij3jka022:\nMJ绘画任务完成\n提示词：A 36d long legged Chinese beauty, beach photo\n耗时：92.03秒'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1753850494, 'MsgSource': '<msgsource>\n\t<pua>1</pua>\n\t<silence>0</silence>\n\t<membercount>73</membercount>\n\t<signature>N0_V1_Ho5JFCdz|v1_gGlinkyf</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'PushContent': '她 : MJ绘画任务完成\n提示词：A 36d long legged Chinese beauty, beach photo\n耗时：92...', 'NewMsgId': 2585101585024777406, 'MsgSeq': 871411626}
2025-07-30 12:41:21 | INFO | 收到文本消息: 消息ID:80628851 来自:***********@chatroom 发送人:wxid_srknxij3jka022 @:[] 内容:MJ绘画任务完成
提示词：A 36d long legged Chinese beauty, beach photo
耗时：92.03秒
2025-07-30 12:41:22 | DEBUG | 处理消息内容: 'MJ绘画任务完成
提示词：A 36d long legged Chinese beauty, beach photo
耗时：92.03秒'
2025-07-30 12:41:22 | DEBUG | 消息内容 'MJ绘画任务完成
提示词：A 36d long legged Chinese beauty, beach photo
耗时：92.03秒' 不匹配任何命令，忽略
2025-07-30 12:41:26 | DEBUG | 收到消息: {'MsgId': 291909579, 'FromUserName': {'string': '***********@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 3, 'Content': {'string': 'wxid_srknxij3jka022:\n<?xml version="1.0"?>\n<msg>\n\t<img aeskey="a67e586f04f4186f4b40071564a97e4c" encryver="1" cdnthumbaeskey="a67e586f04f4186f4b40071564a97e4c" cdnthumburl="3057020100044b304902010002049c5da34302032e6c6302046cab016f02046889a281042437623036626331352d393536362d346564622d383930322d343534313430323365303066020405252a010201000405004c543f00" cdnthumblength="5485" cdnthumbheight="120" cdnthumbwidth="120" cdnmidheight="0" cdnmidwidth="0" cdnhdheight="0" cdnhdwidth="0" cdnmidimgurl="3057020100044b304902010002049c5da34302032e6c6302046cab016f02046889a281042437623036626331352d393536362d346564622d383930322d343534313430323365303066020405252a010201000405004c543f00" length="28178" cdnbigimgurl="3057020100044b304902010002049c5da34302032e6c6302046cab016f02046889a281042437623036626331352d393536362d346564622d383930322d343534313430323365303066020405252a010201000405004c543f00" hdlength="4483652" md5="6df26712fb1b57ad3bfee3561a0da53e" hevc_mid_size="28178" originsourcemd5="6df26712fb1b57ad3bfee3561a0da53e">\n\t\t<secHashInfoBase64 />\n\t\t<live>\n\t\t\t<duration>0</duration>\n\t\t\t<size>0</size>\n\t\t\t<md5 />\n\t\t\t<fileid />\n\t\t\t<hdsize>0</hdsize>\n\t\t\t<hdmd5 />\n\t\t\t<hdfileid />\n\t\t\t<stillimagetimems>0</stillimagetimems>\n\t\t</live>\n\t</img>\n\t<appinfo>\n\t\t<appid>wxb0eef1f67b7a2949</appid>\n\t\t<version>4</version>\n\t\t<appname>国家反诈中心</appname>\n\t\t<isforceupdate>0</isforceupdate>\n\t\t<messageaction />\n\t\t<messageext />\n\t\t<mediatagname />\n\t</appinfo>\n\t<platform_signature />\n\t<imgdatahash />\n\t<ImgSourceInfo>\n\t\t<ImgSourceUrl />\n\t\t<BizType>0</BizType>\n\t</ImgSourceInfo>\n</msg>\n'}, 'Status': 3, 'ImgStatus': 2, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1753850498, 'MsgSource': '<msgsource>\n\t<alnode>\n\t\t<fr>2</fr>\n\t</alnode>\n\t<sec_msg_node>\n\t\t<uuid>2a7d0e454b8315f8e8489a8713946adf_</uuid>\n\t\t<risk-file-flag />\n\t\t<risk-file-md5-list />\n\t</sec_msg_node>\n\t<silence>0</silence>\n\t<membercount>73</membercount>\n\t<signature>N0_V1_y8Lcu88f|v1_oONnfkKK</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'PushContent': '她在群聊中发了一张图片', 'NewMsgId': 3491690861587024733, 'MsgSeq': 871411627}
2025-07-30 12:41:26 | INFO | 收到图片消息: 消息ID:291909579 来自:***********@chatroom 发送人:wxid_srknxij3jka022 XML:<?xml version="1.0"?><msg><img aeskey="a67e586f04f4186f4b40071564a97e4c" encryver="1" cdnthumbaeskey="a67e586f04f4186f4b40071564a97e4c" cdnthumburl="3057020100044b304902010002049c5da34302032e6c6302046cab016f02046889a281042437623036626331352d393536362d346564622d383930322d343534313430323365303066020405252a010201000405004c543f00" cdnthumblength="5485" cdnthumbheight="120" cdnthumbwidth="120" cdnmidheight="0" cdnmidwidth="0" cdnhdheight="0" cdnhdwidth="0" cdnmidimgurl="3057020100044b304902010002049c5da34302032e6c6302046cab016f02046889a281042437623036626331352d393536362d346564622d383930322d343534313430323365303066020405252a010201000405004c543f00" length="28178" cdnbigimgurl="3057020100044b304902010002049c5da34302032e6c6302046cab016f02046889a281042437623036626331352d393536362d346564622d383930322d343534313430323365303066020405252a010201000405004c543f00" hdlength="4483652" md5="6df26712fb1b57ad3bfee3561a0da53e" hevc_mid_size="28178" originsourcemd5="6df26712fb1b57ad3bfee3561a0da53e"><secHashInfoBase64 /><live><duration>0</duration><size>0</size><md5 /><fileid /><hdsize>0</hdsize><hdmd5 /><hdfileid /><stillimagetimems>0</stillimagetimems></live></img><appinfo><appid>wxb0eef1f67b7a2949</appid><version>4</version><appname>国家反诈中心</appname><isforceupdate>0</isforceupdate><messageaction /><messageext /><mediatagname /></appinfo><platform_signature /><imgdatahash /><ImgSourceInfo><ImgSourceUrl /><BizType>0</BizType></ImgSourceInfo></msg>
2025-07-30 12:41:27 | INFO | [ImageEcho] 保存图片信息成功，当前群 ***********@chatroom 已存储 5 张图片
2025-07-30 12:41:27 | INFO | [TimerTask] 缓存图片消息: 291909579
2025-07-30 12:41:27 | DEBUG | 收到消息: {'MsgId': 838656172, 'FromUserName': {'string': '***********@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 3, 'Content': {'string': 'wxid_srknxij3jka022:\n<?xml version="1.0"?>\n<msg>\n\t<img aeskey="c61fae0f391a0d9da92ee4de9a08ae17" encryver="1" cdnthumbaeskey="c61fae0f391a0d9da92ee4de9a08ae17" cdnthumburl="3057020100044b304902010002049c5da34302032e6c6302046cab016f02046889a282042437363364343562612d316438352d343235302d386463662d656438346666653562353331020405292a010201000405004c4d3700" cdnthumblength="5503" cdnthumbheight="120" cdnthumbwidth="120" cdnmidheight="0" cdnmidwidth="0" cdnhdheight="0" cdnhdwidth="0" cdnmidimgurl="3057020100044b304902010002049c5da34302032e6c6302046cab016f02046889a282042437363364343562612d316438352d343235302d386463662d656438346666653562353331020405292a010201000405004c4d3700" length="42672" cdnbigimgurl="3057020100044b304902010002049c5da34302032e6c6302046cab016f02046889a282042437363364343562612d316438352d343235302d386463662d656438346666653562353331020405292a010201000405004c4d3700" hdlength="340473" md5="9c4c989aa79e702f23da21312e53e8b3" hevc_mid_size="42672" originsourcemd5="9c4c989aa79e702f23da21312e53e8b3">\n\t\t<secHashInfoBase64 />\n\t\t<live>\n\t\t\t<duration>0</duration>\n\t\t\t<size>0</size>\n\t\t\t<md5 />\n\t\t\t<fileid />\n\t\t\t<hdsize>0</hdsize>\n\t\t\t<hdmd5 />\n\t\t\t<hdfileid />\n\t\t\t<stillimagetimems>0</stillimagetimems>\n\t\t</live>\n\t</img>\n\t<appinfo>\n\t\t<appid>wx5aa333606550dfd5</appid>\n\t\t<version>54</version>\n\t\t<appname>QQ音乐</appname>\n\t\t<isforceupdate>0</isforceupdate>\n\t\t<messageaction />\n\t\t<messageext />\n\t\t<mediatagname />\n\t</appinfo>\n\t<platform_signature />\n\t<imgdatahash />\n\t<ImgSourceInfo>\n\t\t<ImgSourceUrl />\n\t\t<BizType>0</BizType>\n\t</ImgSourceInfo>\n</msg>\n'}, 'Status': 3, 'ImgStatus': 2, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1753850499, 'MsgSource': '<msgsource>\n\t<alnode>\n\t\t<fr>2</fr>\n\t</alnode>\n\t<sec_msg_node>\n\t\t<uuid>7fa96d442cbdc35097cf26e2367d5e58_</uuid>\n\t\t<risk-file-flag />\n\t\t<risk-file-md5-list />\n\t</sec_msg_node>\n\t<silence>0</silence>\n\t<membercount>73</membercount>\n\t<signature>N0_V1_anyv8Vuc|v1_OjyZ75+e</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'PushContent': '她在群聊中发了一张图片', 'NewMsgId': 67742072697160951, 'MsgSeq': 871411628}
2025-07-30 12:41:27 | INFO | 收到图片消息: 消息ID:838656172 来自:***********@chatroom 发送人:wxid_srknxij3jka022 XML:<?xml version="1.0"?><msg><img aeskey="c61fae0f391a0d9da92ee4de9a08ae17" encryver="1" cdnthumbaeskey="c61fae0f391a0d9da92ee4de9a08ae17" cdnthumburl="3057020100044b304902010002049c5da34302032e6c6302046cab016f02046889a282042437363364343562612d316438352d343235302d386463662d656438346666653562353331020405292a010201000405004c4d3700" cdnthumblength="5503" cdnthumbheight="120" cdnthumbwidth="120" cdnmidheight="0" cdnmidwidth="0" cdnhdheight="0" cdnhdwidth="0" cdnmidimgurl="3057020100044b304902010002049c5da34302032e6c6302046cab016f02046889a282042437363364343562612d316438352d343235302d386463662d656438346666653562353331020405292a010201000405004c4d3700" length="42672" cdnbigimgurl="3057020100044b304902010002049c5da34302032e6c6302046cab016f02046889a282042437363364343562612d316438352d343235302d386463662d656438346666653562353331020405292a010201000405004c4d3700" hdlength="340473" md5="9c4c989aa79e702f23da21312e53e8b3" hevc_mid_size="42672" originsourcemd5="9c4c989aa79e702f23da21312e53e8b3"><secHashInfoBase64 /><live><duration>0</duration><size>0</size><md5 /><fileid /><hdsize>0</hdsize><hdmd5 /><hdfileid /><stillimagetimems>0</stillimagetimems></live></img><appinfo><appid>wx5aa333606550dfd5</appid><version>54</version><appname>QQ音乐</appname><isforceupdate>0</isforceupdate><messageaction /><messageext /><mediatagname /></appinfo><platform_signature /><imgdatahash /><ImgSourceInfo><ImgSourceUrl /><BizType>0</BizType></ImgSourceInfo></msg>
2025-07-30 12:41:28 | INFO | [ImageEcho] 保存图片信息成功，当前群 ***********@chatroom 已存储 5 张图片
2025-07-30 12:41:28 | INFO | [TimerTask] 缓存图片消息: 838656172
2025-07-30 12:41:28 | DEBUG | 收到消息: {'MsgId': 276765717, 'FromUserName': {'string': '***********@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 3, 'Content': {'string': 'wxid_srknxij3jka022:\n<?xml version="1.0"?>\n<msg>\n\t<img aeskey="8492946be66c5615b8a862ed0173f39e" encryver="1" cdnthumbaeskey="8492946be66c5615b8a862ed0173f39e" cdnthumburl="3057020100044b304902010002049c5da34302032e6c6302046cab016f02046889a283042463346365633839372d666333632d346433312d383239632d303830613262383732376530020405292a010201000405004c57c300" cdnthumblength="3532" cdnthumbheight="120" cdnthumbwidth="120" cdnmidheight="0" cdnmidwidth="0" cdnhdheight="0" cdnhdwidth="0" cdnmidimgurl="3057020100044b304902010002049c5da34302032e6c6302046cab016f02046889a283042463346365633839372d666333632d346433312d383239632d303830613262383732376530020405292a010201000405004c57c300" length="22204" cdnbigimgurl="3057020100044b304902010002049c5da34302032e6c6302046cab016f02046889a283042463346365633839372d666333632d346433312d383239632d303830613262383732376530020405292a010201000405004c57c300" hdlength="207046" md5="84fcbc023d77778433e9c6e9289a5a1c" hevc_mid_size="22204" originsourcemd5="84fcbc023d77778433e9c6e9289a5a1c">\n\t\t<secHashInfoBase64 />\n\t\t<live>\n\t\t\t<duration>0</duration>\n\t\t\t<size>0</size>\n\t\t\t<md5 />\n\t\t\t<fileid />\n\t\t\t<hdsize>0</hdsize>\n\t\t\t<hdmd5 />\n\t\t\t<hdfileid />\n\t\t\t<stillimagetimems>0</stillimagetimems>\n\t\t</live>\n\t</img>\n\t<appinfo>\n\t\t<appid>wx0a022393a4dd43b2</appid>\n\t\t<version>5</version>\n\t\t<appname>小爱同学</appname>\n\t\t<isforceupdate>0</isforceupdate>\n\t\t<messageaction />\n\t\t<messageext />\n\t\t<mediatagname />\n\t</appinfo>\n\t<platform_signature />\n\t<imgdatahash />\n\t<ImgSourceInfo>\n\t\t<ImgSourceUrl />\n\t\t<BizType>0</BizType>\n\t</ImgSourceInfo>\n</msg>\n'}, 'Status': 3, 'ImgStatus': 2, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1753850499, 'MsgSource': '<msgsource>\n\t<alnode>\n\t\t<fr>2</fr>\n\t</alnode>\n\t<sec_msg_node>\n\t\t<uuid>4d21eb2200776438d76da0679a7a91c3_</uuid>\n\t\t<risk-file-flag />\n\t\t<risk-file-md5-list />\n\t</sec_msg_node>\n\t<silence>0</silence>\n\t<membercount>73</membercount>\n\t<signature>N0_V1_f868dHj6|v1_qqPQDLuh</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'PushContent': '她在群聊中发了一张图片', 'NewMsgId': 8904336110012397557, 'MsgSeq': 871411629}
2025-07-30 12:41:28 | INFO | 收到图片消息: 消息ID:276765717 来自:***********@chatroom 发送人:wxid_srknxij3jka022 XML:<?xml version="1.0"?><msg><img aeskey="8492946be66c5615b8a862ed0173f39e" encryver="1" cdnthumbaeskey="8492946be66c5615b8a862ed0173f39e" cdnthumburl="3057020100044b304902010002049c5da34302032e6c6302046cab016f02046889a283042463346365633839372d666333632d346433312d383239632d303830613262383732376530020405292a010201000405004c57c300" cdnthumblength="3532" cdnthumbheight="120" cdnthumbwidth="120" cdnmidheight="0" cdnmidwidth="0" cdnhdheight="0" cdnhdwidth="0" cdnmidimgurl="3057020100044b304902010002049c5da34302032e6c6302046cab016f02046889a283042463346365633839372d666333632d346433312d383239632d303830613262383732376530020405292a010201000405004c57c300" length="22204" cdnbigimgurl="3057020100044b304902010002049c5da34302032e6c6302046cab016f02046889a283042463346365633839372d666333632d346433312d383239632d303830613262383732376530020405292a010201000405004c57c300" hdlength="207046" md5="84fcbc023d77778433e9c6e9289a5a1c" hevc_mid_size="22204" originsourcemd5="84fcbc023d77778433e9c6e9289a5a1c"><secHashInfoBase64 /><live><duration>0</duration><size>0</size><md5 /><fileid /><hdsize>0</hdsize><hdmd5 /><hdfileid /><stillimagetimems>0</stillimagetimems></live></img><appinfo><appid>wx0a022393a4dd43b2</appid><version>5</version><appname>小爱同学</appname><isforceupdate>0</isforceupdate><messageaction /><messageext /><mediatagname /></appinfo><platform_signature /><imgdatahash /><ImgSourceInfo><ImgSourceUrl /><BizType>0</BizType></ImgSourceInfo></msg>
2025-07-30 12:41:28 | INFO | [ImageEcho] 保存图片信息成功，当前群 ***********@chatroom 已存储 5 张图片
2025-07-30 12:41:28 | INFO | [TimerTask] 缓存图片消息: 276765717
2025-07-30 12:41:29 | DEBUG | 收到消息: {'MsgId': 573034058, 'FromUserName': {'string': '***********@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 3, 'Content': {'string': 'wxid_srknxij3jka022:\n<?xml version="1.0"?>\n<msg>\n\t<img aeskey="9ee7d17f845eb18bd2db4baec213781c" encryver="1" cdnthumbaeskey="9ee7d17f845eb18bd2db4baec213781c" cdnthumburl="3057020100044b304902010002049c5da34302032e6c6302046cab016f02046889a284042431323765323139652d306432612d346232392d616633322d353337373038366132653663020405252a010201000405004c511f00" cdnthumblength="2825" cdnthumbheight="120" cdnthumbwidth="120" cdnmidheight="0" cdnmidwidth="0" cdnhdheight="0" cdnhdwidth="0" cdnmidimgurl="3057020100044b304902010002049c5da34302032e6c6302046cab016f02046889a284042431323765323139652d306432612d346232392d616633322d353337373038366132653663020405252a010201000405004c511f00" length="10934" cdnbigimgurl="3057020100044b304902010002049c5da34302032e6c6302046cab016f02046889a284042431323765323139652d306432612d346232392d616633322d353337373038366132653663020405252a010201000405004c511f00" hdlength="146600" md5="f4e1c98141536d214ac2e88e639ddb2c" hevc_mid_size="10934" originsourcemd5="f4e1c98141536d214ac2e88e639ddb2c">\n\t\t<secHashInfoBase64 />\n\t\t<live>\n\t\t\t<duration>0</duration>\n\t\t\t<size>0</size>\n\t\t\t<md5 />\n\t\t\t<fileid />\n\t\t\t<hdsize>0</hdsize>\n\t\t\t<hdmd5 />\n\t\t\t<hdfileid />\n\t\t\t<stillimagetimems>0</stillimagetimems>\n\t\t</live>\n\t</img>\n\t<appinfo>\n\t\t<appid>wx0e054ce4ae213018</appid>\n\t\t<version>20</version>\n\t\t<appname>国务院客户端</appname>\n\t\t<isforceupdate>0</isforceupdate>\n\t\t<messageaction />\n\t\t<messageext />\n\t\t<mediatagname />\n\t</appinfo>\n\t<platform_signature />\n\t<imgdatahash />\n\t<ImgSourceInfo>\n\t\t<ImgSourceUrl />\n\t\t<BizType>0</BizType>\n\t</ImgSourceInfo>\n</msg>\n'}, 'Status': 3, 'ImgStatus': 2, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1753850500, 'MsgSource': '<msgsource>\n\t<alnode>\n\t\t<fr>2</fr>\n\t</alnode>\n\t<sec_msg_node>\n\t\t<uuid>136e05063d4efc6bbc1eec96cf160f59_</uuid>\n\t\t<risk-file-flag />\n\t\t<risk-file-md5-list />\n\t</sec_msg_node>\n\t<silence>0</silence>\n\t<membercount>73</membercount>\n\t<signature>N0_V1_d0gghpQs|v1_luiQaW9N</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'PushContent': '她在群聊中发了一张图片', 'NewMsgId': 772189785084817315, 'MsgSeq': 871411630}
2025-07-30 12:41:29 | INFO | 收到图片消息: 消息ID:573034058 来自:***********@chatroom 发送人:wxid_srknxij3jka022 XML:<?xml version="1.0"?><msg><img aeskey="9ee7d17f845eb18bd2db4baec213781c" encryver="1" cdnthumbaeskey="9ee7d17f845eb18bd2db4baec213781c" cdnthumburl="3057020100044b304902010002049c5da34302032e6c6302046cab016f02046889a284042431323765323139652d306432612d346232392d616633322d353337373038366132653663020405252a010201000405004c511f00" cdnthumblength="2825" cdnthumbheight="120" cdnthumbwidth="120" cdnmidheight="0" cdnmidwidth="0" cdnhdheight="0" cdnhdwidth="0" cdnmidimgurl="3057020100044b304902010002049c5da34302032e6c6302046cab016f02046889a284042431323765323139652d306432612d346232392d616633322d353337373038366132653663020405252a010201000405004c511f00" length="10934" cdnbigimgurl="3057020100044b304902010002049c5da34302032e6c6302046cab016f02046889a284042431323765323139652d306432612d346232392d616633322d353337373038366132653663020405252a010201000405004c511f00" hdlength="146600" md5="f4e1c98141536d214ac2e88e639ddb2c" hevc_mid_size="10934" originsourcemd5="f4e1c98141536d214ac2e88e639ddb2c"><secHashInfoBase64 /><live><duration>0</duration><size>0</size><md5 /><fileid /><hdsize>0</hdsize><hdmd5 /><hdfileid /><stillimagetimems>0</stillimagetimems></live></img><appinfo><appid>wx0e054ce4ae213018</appid><version>20</version><appname>国务院客户端</appname><isforceupdate>0</isforceupdate><messageaction /><messageext /><mediatagname /></appinfo><platform_signature /><imgdatahash /><ImgSourceInfo><ImgSourceUrl /><BizType>0</BizType></ImgSourceInfo></msg>
2025-07-30 12:41:29 | INFO | [ImageEcho] 保存图片信息成功，当前群 ***********@chatroom 已存储 5 张图片
2025-07-30 12:41:29 | INFO | [TimerTask] 缓存图片消息: 573034058
2025-07-30 12:41:30 | DEBUG | 收到消息: {'MsgId': 916224234, 'FromUserName': {'string': '***********@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 3, 'Content': {'string': 'wxid_srknxij3jka022:\n<?xml version="1.0"?>\n<msg>\n\t<img aeskey="a4d504691ffba4bfbc73bc05be701f81" encryver="1" cdnthumbaeskey="a4d504691ffba4bfbc73bc05be701f81" cdnthumburl="3057020100044b304902010002049c5da34302032e6c6302046cab016f02046889a284042435353663303132322d373533362d343965642d623532352d343662333962366337376139020405252a010201000405004c543f00" cdnthumblength="4156" cdnthumbheight="120" cdnthumbwidth="120" cdnmidheight="0" cdnmidwidth="0" cdnhdheight="0" cdnhdwidth="0" cdnmidimgurl="3057020100044b304902010002049c5da34302032e6c6302046cab016f02046889a284042435353663303132322d373533362d343965642d623532352d343662333962366337376139020405252a010201000405004c543f00" length="13694" cdnbigimgurl="3057020100044b304902010002049c5da34302032e6c6302046cab016f02046889a284042435353663303132322d373533362d343965642d623532352d343662333962366337376139020405252a010201000405004c543f00" hdlength="146569" md5="a3bcc8528514b1276ec25f253c8a6164" hevc_mid_size="13694" originsourcemd5="a3bcc8528514b1276ec25f253c8a6164">\n\t\t<secHashInfoBase64 />\n\t\t<live>\n\t\t\t<duration>0</duration>\n\t\t\t<size>0</size>\n\t\t\t<md5 />\n\t\t\t<fileid />\n\t\t\t<hdsize>0</hdsize>\n\t\t\t<hdmd5 />\n\t\t\t<hdfileid />\n\t\t\t<stillimagetimems>0</stillimagetimems>\n\t\t</live>\n\t</img>\n\t<appinfo>\n\t\t<appid>wxe3ad19e142df87b3</appid>\n\t\t<version>5</version>\n\t\t<appname>麻豆约拍</appname>\n\t\t<isforceupdate>0</isforceupdate>\n\t\t<messageaction />\n\t\t<messageext />\n\t\t<mediatagname />\n\t</appinfo>\n\t<platform_signature />\n\t<imgdatahash />\n\t<ImgSourceInfo>\n\t\t<ImgSourceUrl />\n\t\t<BizType>0</BizType>\n\t</ImgSourceInfo>\n</msg>\n'}, 'Status': 3, 'ImgStatus': 2, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1753850500, 'MsgSource': '<msgsource>\n\t<alnode>\n\t\t<fr>2</fr>\n\t</alnode>\n\t<sec_msg_node>\n\t\t<uuid>c00ec5185888eceba9ce2306c0bcfe42_</uuid>\n\t\t<risk-file-flag />\n\t\t<risk-file-md5-list />\n\t</sec_msg_node>\n\t<silence>0</silence>\n\t<membercount>73</membercount>\n\t<signature>N0_V1_gJFAfsEj|v1_ppIp1k1R</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'PushContent': '她在群聊中发了一张图片', 'NewMsgId': 4799465393038431414, 'MsgSeq': 871411631}
2025-07-30 12:41:30 | INFO | 收到图片消息: 消息ID:916224234 来自:***********@chatroom 发送人:wxid_srknxij3jka022 XML:<?xml version="1.0"?><msg><img aeskey="a4d504691ffba4bfbc73bc05be701f81" encryver="1" cdnthumbaeskey="a4d504691ffba4bfbc73bc05be701f81" cdnthumburl="3057020100044b304902010002049c5da34302032e6c6302046cab016f02046889a284042435353663303132322d373533362d343965642d623532352d343662333962366337376139020405252a010201000405004c543f00" cdnthumblength="4156" cdnthumbheight="120" cdnthumbwidth="120" cdnmidheight="0" cdnmidwidth="0" cdnhdheight="0" cdnhdwidth="0" cdnmidimgurl="3057020100044b304902010002049c5da34302032e6c6302046cab016f02046889a284042435353663303132322d373533362d343965642d623532352d343662333962366337376139020405252a010201000405004c543f00" length="13694" cdnbigimgurl="3057020100044b304902010002049c5da34302032e6c6302046cab016f02046889a284042435353663303132322d373533362d343965642d623532352d343662333962366337376139020405252a010201000405004c543f00" hdlength="146569" md5="a3bcc8528514b1276ec25f253c8a6164" hevc_mid_size="13694" originsourcemd5="a3bcc8528514b1276ec25f253c8a6164"><secHashInfoBase64 /><live><duration>0</duration><size>0</size><md5 /><fileid /><hdsize>0</hdsize><hdmd5 /><hdfileid /><stillimagetimems>0</stillimagetimems></live></img><appinfo><appid>wxe3ad19e142df87b3</appid><version>5</version><appname>麻豆约拍</appname><isforceupdate>0</isforceupdate><messageaction /><messageext /><mediatagname /></appinfo><platform_signature /><imgdatahash /><ImgSourceInfo><ImgSourceUrl /><BizType>0</BizType></ImgSourceInfo></msg>
2025-07-30 12:41:30 | INFO | [ImageEcho] 保存图片信息成功，当前群 ***********@chatroom 已存储 5 张图片
2025-07-30 12:41:30 | INFO | [TimerTask] 缓存图片消息: 916224234
2025-07-30 12:42:46 | DEBUG | 收到消息: {'MsgId': 372915694, 'FromUserName': {'string': '***********@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 1, 'Content': {'string': 'xiaomaochong:\n比豆包强一点点'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1753850578, 'MsgSource': '<msgsource>\n\t<pua>1</pua>\n\t<silence>0</silence>\n\t<membercount>73</membercount>\n\t<signature>N0_V1_PnYAqqzE|v1_RjogkVck</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'PushContent': '小爱 : 比豆包强一点点', 'NewMsgId': 6842430639140746128, 'MsgSeq': 871411632}
2025-07-30 12:42:46 | INFO | 收到文本消息: 消息ID:372915694 来自:***********@chatroom 发送人:xiaomaochong @:[] 内容:比豆包强一点点
2025-07-30 12:42:46 | DEBUG | 处理消息内容: '比豆包强一点点'
2025-07-30 12:42:46 | DEBUG | 消息内容 '比豆包强一点点' 不匹配任何命令，忽略
2025-07-30 12:42:56 | DEBUG | 收到消息: {'MsgId': 2090337211, 'FromUserName': {'string': '***********@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 43, 'Content': {'string': 'zuoledd:\n<?xml version="1.0"?>\n<msg>\n\t<videomsg aeskey="034ad8ee6a0450f4ad22a55f241feab1" cdnvideourl="3057020100044b3049020100020445d9af4902032e1d7b02042f619624020468899230042432663366643834342d346163352d343037632d393763612d3261333735306462306330370204052808040201000405004c57c300" cdnthumbaeskey="034ad8ee6a0450f4ad22a55f241feab1" cdnthumburl="3057020100044b3049020100020445d9af4902032e1d7b02042f619624020468899230042432663366643834342d346163352d343037632d393763612d3261333735306462306330370204052808040201000405004c57c300" length="8365528" playlength="52" cdnthumblength="19967" cdnthumbwidth="360" cdnthumbheight="640" fromusername="zuoledd" md5="e7b52c69ff23b3f9cf533432fe942fa7" newmd5="5aa0be9c4b299b92147afee3700a0f1d" isplaceholder="0" rawmd5="" rawlength="0" cdnrawvideourl="" cdnrawvideoaeskey="" overwritenewmsgid="0" originsourcemd5="" isad="0" />\n</msg>\n'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1753850588, 'MsgSource': '<msgsource>\n\t<bizflag>0</bizflag>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n\t<alnode>\n\t\t<fr>5</fr>\n\t</alnode>\n\t<weappsourceUsername>(null),zuoledd</weappsourceUsername>\n\t<sec_msg_node>\n\t\t<uuid>693768915863f5e26e5f04b4f61d622c_</uuid>\n\t\t<risk-file-flag />\n\t\t<risk-file-md5-list />\n\t</sec_msg_node>\n\t<silence>0</silence>\n\t<membercount>73</membercount>\n\t<signature>N0_V1_3e0hUwfR|v1_MxZMhmPH</signature>\n</msgsource>\n', 'PushContent': '作乐多端在群聊中发了一段视频', 'NewMsgId': 2087746212289460459, 'MsgSeq': 871411633}
2025-07-30 12:42:56 | INFO | 收到视频消息: 消息ID:2090337211 来自:***********@chatroom 发送人:zuoledd XML:
<?xml version="1.0"?>
<msg>
	<videomsg aeskey="034ad8ee6a0450f4ad22a55f241feab1" cdnvideourl="3057020100044b3049020100020445d9af4902032e1d7b02042f619624020468899230042432663366643834342d346163352d343037632d393763612d3261333735306462306330370204052808040201000405004c57c300" cdnthumbaeskey="034ad8ee6a0450f4ad22a55f241feab1" cdnthumburl="3057020100044b3049020100020445d9af4902032e1d7b02042f619624020468899230042432663366643834342d346163352d343037632d393763612d3261333735306462306330370204052808040201000405004c57c300" length="8365528" playlength="52" cdnthumblength="19967" cdnthumbwidth="360" cdnthumbheight="640" fromusername="zuoledd" md5="e7b52c69ff23b3f9cf533432fe942fa7" newmd5="5aa0be9c4b299b92147afee3700a0f1d" isplaceholder="0" rawmd5="" rawlength="0" cdnrawvideourl="" cdnrawvideoaeskey="" overwritenewmsgid="0" originsourcemd5="" isad="0" />
</msg>

2025-07-30 12:43:05 | DEBUG | 收到消息: {'MsgId': 464862503, 'FromUserName': {'string': '***********@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 3, 'Content': {'string': 'zuoledd:\n<?xml version="1.0"?>\n<msg>\n\t<img aeskey="d9cd355825fcbab2c9dd01bfd6383da8" encryver="1" cdnthumbaeskey="d9cd355825fcbab2c9dd01bfd6383da8" cdnthumburl="3057020100044b30490201000204d546cabb02032f50e702045a9cdc780204688984df042461626631633738352d396134362d343161612d396666662d333736336363643565333130020405250a020201000405004c4d9b00" cdnthumblength="6235" cdnthumbheight="180" cdnthumbwidth="180" cdnmidheight="0" cdnmidwidth="0" cdnhdheight="0" cdnhdwidth="0" cdnmidimgurl="3057020100044b30490201000204d546cabb02032f50e702045a9cdc780204688984df042461626631633738352d396134362d343161612d396666662d333736336363643565333130020405250a020201000405004c4d9b00" length="43848" md5="dc45498cd27eff37e2f2712bde3df928" hevc_mid_size="43848">\n\t\t<secHashInfoBase64>eyJwaGFzaCI6ImY4MmMxMTA5MDAwMDAwMDAiLCJwZHFoYXNoIjoiYmZmOTg0NDBmY2VmOTFlMDEyNGQ1OGM4Nzk5OTRiMjM0YmI5NjkzODBmMzcwY2YwMzFiMzc5ZTdhNmM0MjU5NyJ9</secHashInfoBase64>\n\t\t<live>\n\t\t\t<duration>0</duration>\n\t\t\t<size>0</size>\n\t\t\t<md5 />\n\t\t\t<fileid />\n\t\t\t<hdsize>0</hdsize>\n\t\t\t<hdmd5 />\n\t\t\t<hdfileid />\n\t\t\t<stillimagetimems>0</stillimagetimems>\n\t\t</live>\n\t</img>\n\t<platform_signature />\n\t<imgdatahash />\n\t<ImgSourceInfo>\n\t\t<ImgSourceUrl />\n\t\t<BizType>0</BizType>\n\t</ImgSourceInfo>\n</msg>\n'}, 'Status': 3, 'ImgStatus': 2, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1753850597, 'MsgSource': '<msgsource>\n\t<bizflag>0</bizflag>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n\t<alnode>\n\t\t<fr>5</fr>\n\t</alnode>\n\t<sec_msg_node>\n\t\t<uuid>cd8a198499ede6d9fd094d6faf674412_</uuid>\n\t\t<risk-file-flag />\n\t\t<risk-file-md5-list />\n\t</sec_msg_node>\n\t<silence>0</silence>\n\t<membercount>73</membercount>\n\t<signature>N0_V1_C0SL+5+S|v1_L6E7OSiG</signature>\n</msgsource>\n', 'PushContent': '作乐多端在群聊中发了一张图片', 'NewMsgId': 8009299119428846198, 'MsgSeq': 871411634}
2025-07-30 12:43:05 | INFO | 收到图片消息: 消息ID:464862503 来自:***********@chatroom 发送人:zuoledd XML:<?xml version="1.0"?><msg><img aeskey="d9cd355825fcbab2c9dd01bfd6383da8" encryver="1" cdnthumbaeskey="d9cd355825fcbab2c9dd01bfd6383da8" cdnthumburl="3057020100044b30490201000204d546cabb02032f50e702045a9cdc780204688984df042461626631633738352d396134362d343161612d396666662d333736336363643565333130020405250a020201000405004c4d9b00" cdnthumblength="6235" cdnthumbheight="180" cdnthumbwidth="180" cdnmidheight="0" cdnmidwidth="0" cdnhdheight="0" cdnhdwidth="0" cdnmidimgurl="3057020100044b30490201000204d546cabb02032f50e702045a9cdc780204688984df042461626631633738352d396134362d343161612d396666662d333736336363643565333130020405250a020201000405004c4d9b00" length="43848" md5="dc45498cd27eff37e2f2712bde3df928" hevc_mid_size="43848"><secHashInfoBase64>eyJwaGFzaCI6ImY4MmMxMTA5MDAwMDAwMDAiLCJwZHFoYXNoIjoiYmZmOTg0NDBmY2VmOTFlMDEyNGQ1OGM4Nzk5OTRiMjM0YmI5NjkzODBmMzcwY2YwMzFiMzc5ZTdhNmM0MjU5NyJ9</secHashInfoBase64><live><duration>0</duration><size>0</size><md5 /><fileid /><hdsize>0</hdsize><hdmd5 /><hdfileid /><stillimagetimems>0</stillimagetimems></live></img><platform_signature /><imgdatahash /><ImgSourceInfo><ImgSourceUrl /><BizType>0</BizType></ImgSourceInfo></msg>
2025-07-30 12:43:06 | INFO | [ImageEcho] 保存图片信息成功，当前群 ***********@chatroom 已存储 5 张图片
2025-07-30 12:43:06 | INFO | [TimerTask] 缓存图片消息: 464862503
2025-07-30 12:43:11 | DEBUG | 收到消息: {'MsgId': *********, 'FromUserName': {'string': '***********@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 3, 'Content': {'string': 'zuoledd:\n<?xml version="1.0"?>\n<msg>\n\t<img aeskey="86c5aadabe9e9108dca6f4ab1c4786bd" encryver="1" cdnthumbaeskey="86c5aadabe9e9108dca6f4ab1c4786bd" cdnthumburl="3057020100044b304902010002048ff1f77502032f54cf0204a3c6ccb7020468898faa042461626131326530382d373765382d343238622d623634312d363166323263636331636634020401250a020201000405004c53db00" cdnthumblength="2758" cdnthumbheight="180" cdnthumbwidth="96" cdnmidheight="0" cdnmidwidth="0" cdnhdheight="0" cdnhdwidth="0" cdnmidimgurl="3057020100044b304902010002048ff1f77502032f54cf0204a3c6ccb7020468898faa042461626131326530382d373765382d343238622d623634312d363166323263636331636634020401250a020201000405004c53db00" length="41419" md5="7a7c1d1751490156704ad1ec7e90a4b4" hevc_mid_size="41419">\n\t\t<secHashInfoBase64>eyJwaGFzaCI6IjUwMTAxMDUwNDA4MDAxMDAiLCJwZHFoYXNoIjoiZTg5OTU2MjdhNWY0N2E2OGM3OWY4ZGIxMWE2ZTYwZDllMzI0MGUxYmY4NzA3MmU2OTkxYmNjOTgzNjYxMzM2NiJ9</secHashInfoBase64>\n\t\t<live>\n\t\t\t<duration>0</duration>\n\t\t\t<size>0</size>\n\t\t\t<md5 />\n\t\t\t<fileid />\n\t\t\t<hdsize>0</hdsize>\n\t\t\t<hdmd5 />\n\t\t\t<hdfileid />\n\t\t\t<stillimagetimems>0</stillimagetimems>\n\t\t</live>\n\t</img>\n\t<platform_signature />\n\t<imgdatahash />\n\t<ImgSourceInfo>\n\t\t<ImgSourceUrl />\n\t\t<BizType>0</BizType>\n\t</ImgSourceInfo>\n</msg>\n'}, 'Status': 3, 'ImgStatus': 2, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1753850603, 'MsgSource': '<msgsource>\n\t<bizflag>0</bizflag>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n\t<alnode>\n\t\t<fr>5</fr>\n\t</alnode>\n\t<sec_msg_node>\n\t\t<uuid>6d799bf3d7c8ecf091699fe88350c9ff_</uuid>\n\t\t<risk-file-flag />\n\t\t<risk-file-md5-list />\n\t</sec_msg_node>\n\t<silence>0</silence>\n\t<membercount>73</membercount>\n\t<signature>N0_V1_goN4Js7D|v1_gsUjhdLc</signature>\n</msgsource>\n', 'PushContent': '作乐多端在群聊中发了一张图片', 'NewMsgId': 1652621974800387033, 'MsgSeq': 871411635}
2025-07-30 12:43:11 | INFO | 收到图片消息: 消息ID:********* 来自:***********@chatroom 发送人:zuoledd XML:<?xml version="1.0"?><msg><img aeskey="86c5aadabe9e9108dca6f4ab1c4786bd" encryver="1" cdnthumbaeskey="86c5aadabe9e9108dca6f4ab1c4786bd" cdnthumburl="3057020100044b304902010002048ff1f77502032f54cf0204a3c6ccb7020468898faa042461626131326530382d373765382d343238622d623634312d363166323263636331636634020401250a020201000405004c53db00" cdnthumblength="2758" cdnthumbheight="180" cdnthumbwidth="96" cdnmidheight="0" cdnmidwidth="0" cdnhdheight="0" cdnhdwidth="0" cdnmidimgurl="3057020100044b304902010002048ff1f77502032f54cf0204a3c6ccb7020468898faa042461626131326530382d373765382d343238622d623634312d363166323263636331636634020401250a020201000405004c53db00" length="41419" md5="7a7c1d1751490156704ad1ec7e90a4b4" hevc_mid_size="41419"><secHashInfoBase64>eyJwaGFzaCI6IjUwMTAxMDUwNDA4MDAxMDAiLCJwZHFoYXNoIjoiZTg5OTU2MjdhNWY0N2E2OGM3OWY4ZGIxMWE2ZTYwZDllMzI0MGUxYmY4NzA3MmU2OTkxYmNjOTgzNjYxMzM2NiJ9</secHashInfoBase64><live><duration>0</duration><size>0</size><md5 /><fileid /><hdsize>0</hdsize><hdmd5 /><hdfileid /><stillimagetimems>0</stillimagetimems></live></img><platform_signature /><imgdatahash /><ImgSourceInfo><ImgSourceUrl /><BizType>0</BizType></ImgSourceInfo></msg>
2025-07-30 12:43:12 | INFO | [ImageEcho] 保存图片信息成功，当前群 ***********@chatroom 已存储 5 张图片
2025-07-30 12:43:12 | INFO | [TimerTask] 缓存图片消息: *********
2025-07-30 12:43:56 | DEBUG | 收到消息: {'MsgId': 1595782166, 'FromUserName': {'string': '***********@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 3, 'Content': {'string': 'zuoledd:\n<?xml version="1.0"?>\n<msg>\n\t<img aeskey="b0cdcfed9f7f62d25fd8b88e66ce0a69" encryver="1" cdnthumbaeskey="b0cdcfed9f7f62d25fd8b88e66ce0a69" cdnthumburl="3057020100044b304902010002045422df2d02032f540502042ea7b4de020468887f4c042461313264353832322d383939352d346164382d383265632d653665376337666538383230020405250a020201000405004c4e6100" cdnthumblength="3105" cdnthumbheight="180" cdnthumbwidth="72" cdnmidheight="0" cdnmidwidth="0" cdnhdheight="0" cdnhdwidth="0" cdnmidimgurl="3057020100044b304902010002045422df2d02032f540502042ea7b4de020468887f4c042461313264353832322d383939352d346164382d383265632d653665376337666538383230020405250a020201000405004c4e6100" length="137212" md5="0f5c093e18c6d19a3600be105dfe4c4e" hevc_mid_size="137212">\n\t\t<secHashInfoBase64>eyJwaGFzaCI6IjMwMDAwMDAwMDAwMDAwMDAiLCJwZHFoYXNoIjoiZGJkMGZmMjAwOGZjMjBhZjNhY2YwMWM5YmZkMGIzZGIyMGUxYjhlNDQwMzU1Zjc3NDExN2ZmZTAwMGVmMDQ4ZiJ9</secHashInfoBase64>\n\t\t<live>\n\t\t\t<duration>0</duration>\n\t\t\t<size>0</size>\n\t\t\t<md5 />\n\t\t\t<fileid />\n\t\t\t<hdsize>0</hdsize>\n\t\t\t<hdmd5 />\n\t\t\t<hdfileid />\n\t\t\t<stillimagetimems>0</stillimagetimems>\n\t\t</live>\n\t</img>\n\t<platform_signature />\n\t<imgdatahash />\n\t<ImgSourceInfo>\n\t\t<ImgSourceUrl />\n\t\t<BizType>0</BizType>\n\t</ImgSourceInfo>\n</msg>\n'}, 'Status': 3, 'ImgStatus': 2, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1753850648, 'MsgSource': '<msgsource>\n\t<bizflag>0</bizflag>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n\t<alnode>\n\t\t<fr>5</fr>\n\t</alnode>\n\t<sec_msg_node>\n\t\t<uuid>26de359799eabd9dceb165f26c7a18c5_</uuid>\n\t\t<risk-file-flag />\n\t\t<risk-file-md5-list />\n\t</sec_msg_node>\n\t<silence>0</silence>\n\t<membercount>73</membercount>\n\t<signature>N0_V1_xp4rrtbf|v1_GZnd9pte</signature>\n</msgsource>\n', 'PushContent': '作乐多端在群聊中发了一张图片', 'NewMsgId': 7386547733914332985, 'MsgSeq': 871411636}
2025-07-30 12:43:56 | INFO | 收到图片消息: 消息ID:1595782166 来自:***********@chatroom 发送人:zuoledd XML:<?xml version="1.0"?><msg><img aeskey="b0cdcfed9f7f62d25fd8b88e66ce0a69" encryver="1" cdnthumbaeskey="b0cdcfed9f7f62d25fd8b88e66ce0a69" cdnthumburl="3057020100044b304902010002045422df2d02032f540502042ea7b4de020468887f4c042461313264353832322d383939352d346164382d383265632d653665376337666538383230020405250a020201000405004c4e6100" cdnthumblength="3105" cdnthumbheight="180" cdnthumbwidth="72" cdnmidheight="0" cdnmidwidth="0" cdnhdheight="0" cdnhdwidth="0" cdnmidimgurl="3057020100044b304902010002045422df2d02032f540502042ea7b4de020468887f4c042461313264353832322d383939352d346164382d383265632d653665376337666538383230020405250a020201000405004c4e6100" length="137212" md5="0f5c093e18c6d19a3600be105dfe4c4e" hevc_mid_size="137212"><secHashInfoBase64>eyJwaGFzaCI6IjMwMDAwMDAwMDAwMDAwMDAiLCJwZHFoYXNoIjoiZGJkMGZmMjAwOGZjMjBhZjNhY2YwMWM5YmZkMGIzZGIyMGUxYjhlNDQwMzU1Zjc3NDExN2ZmZTAwMGVmMDQ4ZiJ9</secHashInfoBase64><live><duration>0</duration><size>0</size><md5 /><fileid /><hdsize>0</hdsize><hdmd5 /><hdfileid /><stillimagetimems>0</stillimagetimems></live></img><platform_signature /><imgdatahash /><ImgSourceInfo><ImgSourceUrl /><BizType>0</BizType></ImgSourceInfo></msg>
2025-07-30 12:43:57 | INFO | [ImageEcho] 保存图片信息成功，当前群 ***********@chatroom 已存储 5 张图片
2025-07-30 12:43:57 | INFO | [TimerTask] 缓存图片消息: 1595782166
2025-07-30 12:45:19 | DEBUG | 收到消息: {'MsgId': 313351615, 'FromUserName': {'string': '***********@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 1, 'Content': {'string': 'last--exile:\n呦西呦西'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1753850731, 'MsgSource': '<msgsource>\n\t<pua>1</pua>\n\t<silence>0</silence>\n\t<membercount>73</membercount>\n\t<signature>N0_V1_2S0vs8Rq|v1_lyrY6rnO</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'PushContent': '亮 : 呦西呦西', 'NewMsgId': 2020518005866139691, 'MsgSeq': 871411637}
2025-07-30 12:45:19 | INFO | 收到文本消息: 消息ID:313351615 来自:***********@chatroom 发送人:last--exile @:[] 内容:呦西呦西
2025-07-30 12:45:20 | DEBUG | 处理消息内容: '呦西呦西'
2025-07-30 12:45:20 | DEBUG | 消息内容 '呦西呦西' 不匹配任何命令，忽略
2025-07-30 12:47:32 | DEBUG | 收到消息: {'MsgId': 548504309, 'FromUserName': {'string': '51595225532@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 1, 'Content': {'string': 'wxid_zmtl4dx10j6j29:\n@Aurora\u2005么么哒'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1753850864, 'MsgSource': '<msgsource>\n\t<atuserlist><![CDATA[wxid_m1lh16d8hfl221]]></atuserlist>\n\t<pua>1</pua>\n\t<eggSeed>2100379573</eggSeed>\n\t<eggIncluded>2</eggIncluded>\n\t<silence>0</silence>\n\t<membercount>10</membercount>\n\t<signature>N0_V1_iSnEy3/S|v1_zVT5sRGd</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'PushContent': '18 : @Aurora\u2005么么哒', 'NewMsgId': 8590993074934723345, 'MsgSeq': 871411638}
2025-07-30 12:47:32 | INFO | 收到文本消息: 消息ID:548504309 来自:51595225532@chatroom 发送人:wxid_zmtl4dx10j6j29 @:['wxid_m1lh16d8hfl221'] 内容:@Aurora 么么哒
2025-07-30 12:47:32 | DEBUG | 处理消息内容: '@Aurora 么么哒'
2025-07-30 12:47:32 | DEBUG | 消息内容 '@Aurora 么么哒' 不匹配任何命令，忽略
2025-07-30 12:52:50 | DEBUG | 收到消息: {'MsgId': 2030817312, 'FromUserName': {'string': '***********@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 1, 'Content': {'string': 'wxid_jegyk4i3v7zg22:\n0.02 复制打开抖音，看看【宝贝曾曾的作品】零帧起手。# 宝贝曾曾  https://v.douyin.com/CYCjPb2SKfY/ <EMAIL> 03/11 UYZ:/ '}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1753851182, 'MsgSource': '<msgsource>\n\t<bizflag>0</bizflag>\n\t<pua>1</pua>\n\t<alnode>\n\t\t<inlenlist>89</inlenlist>\n\t</alnode>\n\t<eggIncluded>1</eggIncluded>\n\t<silence>0</silence>\n\t<membercount>73</membercount>\n\t<signature>N0_V1_LiVacEg+|v1_jv5K5K1b</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'PushContent': '阿尼亚与她 : 0.02 复制打开抖音，看看【宝贝曾曾的作品】零帧起手。# 宝贝曾曾  ...', 'NewMsgId': 4942072981175204797, 'MsgSeq': 871411639}
2025-07-30 12:52:50 | INFO | 收到文本消息: 消息ID:2030817312 来自:***********@chatroom 发送人:wxid_jegyk4i3v7zg22 @:[] 内容:0.02 复制打开抖音，看看【宝贝曾曾的作品】零帧起手。# 宝贝曾曾  https://v.douyin.com/CYCjPb2SKfY/ <EMAIL> 03/11 UYZ:/ 
2025-07-30 12:52:50 | DEBUG | 处理消息内容: '0.02 复制打开抖音，看看【宝贝曾曾的作品】零帧起手。# 宝贝曾曾  https://v.douyin.com/CYCjPb2SKfY/ <EMAIL> 03/11 UYZ:/'
2025-07-30 12:52:50 | DEBUG | 消息内容 '0.02 复制打开抖音，看看【宝贝曾曾的作品】零帧起手。# 宝贝曾曾  https://v.douyin.com/CYCjPb2SKfY/ <EMAIL> 03/11 UYZ:/' 不匹配任何命令，忽略
2025-07-30 12:52:52 | DEBUG | 收到消息: {'MsgId': 678732491, 'FromUserName': {'string': '***********@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 10002, 'Content': {'string': '***********@chatroom:\n<sysmsg type="secmsg">\n  <secmsg>\n    <session>***********@chatroom</session>\n    <newmsgid>4942072981175204797</newmsgid>\n    <sec_msg_node>\n      <sfn>0</sfn>\n      <fd></fd>\n      <show-h5></show-h5>\n      <clip-len>0</clip-len>\n      <share-tip-wording><![CDATA[]]></share-tip-wording>\n      <share-tip-url><![CDATA[]]></share-tip-url>\n      <fold-reduce><![CDATA[1]]></fold-reduce>\n      <block-range>0</block-range>\n      <media-to-emoji>0</media-to-emoji>\n      <bubble-type>0</bubble-type>\n      <preview-type>0</preview-type>\n      <url-click-type>0</url-click-type>\n      <sec-ctrl-flag>0</sec-ctrl-flag>\n      <fr-type></fr-type>\n      <risk-file-flag>0</risk-file-flag>\n      <risk-file-md5-list><![CDATA[]]></risk-file-md5-list>\n      <risk-warning-url><![CDATA[]]></risk-warning-url>\n      <unread-media-expired></unread-media-expired>\n    </sec_msg_node>\n  </secmsg>\n</sysmsg>'}, 'Status': 4, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1753851183, 'MsgSource': '<msgsource>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'NewMsgId': 2635402042706749283, 'MsgSeq': 871411640}
2025-07-30 12:52:52 | DEBUG | 系统消息类型: secmsg
2025-07-30 12:52:52 | INFO | 未知的系统消息类型: {'MsgId': 678732491, 'ToWxid': 'wxid_4usgcju5ey9q29', 'MsgType': 10002, 'Content': '\n<sysmsg type="secmsg">\n  <secmsg>\n    <session>***********@chatroom</session>\n    <newmsgid>4942072981175204797</newmsgid>\n    <sec_msg_node>\n      <sfn>0</sfn>\n      <fd></fd>\n      <show-h5></show-h5>\n      <clip-len>0</clip-len>\n      <share-tip-wording><![CDATA[]]></share-tip-wording>\n      <share-tip-url><![CDATA[]]></share-tip-url>\n      <fold-reduce><![CDATA[1]]></fold-reduce>\n      <block-range>0</block-range>\n      <media-to-emoji>0</media-to-emoji>\n      <bubble-type>0</bubble-type>\n      <preview-type>0</preview-type>\n      <url-click-type>0</url-click-type>\n      <sec-ctrl-flag>0</sec-ctrl-flag>\n      <fr-type></fr-type>\n      <risk-file-flag>0</risk-file-flag>\n      <risk-file-md5-list><![CDATA[]]></risk-file-md5-list>\n      <risk-warning-url><![CDATA[]]></risk-warning-url>\n      <unread-media-expired></unread-media-expired>\n    </sec_msg_node>\n  </secmsg>\n</sysmsg>', 'Status': 4, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1753851183, 'MsgSource': '<msgsource>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'NewMsgId': 2635402042706749283, 'MsgSeq': 871411640, 'FromWxid': '***********@chatroom', 'IsGroup': True, 'SenderWxid': '***********@chatroom'}
2025-07-30 12:54:28 | DEBUG | 收到消息: {'MsgId': 638953633, 'FromUserName': {'string': '***********@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 47, 'Content': {'string': 'wxid_jegyk4i3v7zg22:\n<msg><emoji fromusername="wxid_jegyk4i3v7zg22" tousername="***********@chatroom" type="2" idbuffer="media:0_0" md5="aaf37f2f70b3b978f794210146d67fd9" len="4452666" productid="" androidmd5="aaf37f2f70b3b978f794210146d67fd9" androidlen="4452666" s60v3md5="aaf37f2f70b3b978f794210146d67fd9" s60v3len="4452666" s60v5md5="aaf37f2f70b3b978f794210146d67fd9" s60v5len="4452666" cdnurl="http://vweixinf.tc.qq.com/110/20401/stodownload?m=aaf37f2f70b3b978f794210146d67fd9&amp;filekey=30440201010430302e02016e0402535a04206161663337663266373062336239373866373934323130313436643637666439020343f13a040d00000004627466730000000132&amp;hy=SZ&amp;storeid=26852ae960004a6158d26d88d0000006e01004fb1535a299f001156e433b4a&amp;ef=1&amp;bizid=1022" designerid="" thumburl="" encrypturl="http://vweixinf.tc.qq.com/110/20402/stodownload?m=e8d11e273d8f200aad2a64db0a8288c4&amp;filekey=30440201010430302e02016e0402535a04206538643131653237336438663230306161643261363464623061383238386334020343f140040d00000004627466730000000132&amp;hy=SZ&amp;storeid=26852ae960008de2d8d26d88d0000006e02004fb2535a299f001156e433b8d&amp;ef=2&amp;bizid=1022" aeskey="46708aa7ef30488492a30fd1364fe43f" externurl="http://vweixinf.tc.qq.com/110/20403/stodownload?m=f6fba296b119e9442a5bc13dd5cdafec&amp;filekey=30440201010430302e02016e0402535a042066366662613239366231313965393434326135626331336464356364616665630203032d70040d00000004627466730000000132&amp;hy=SZ&amp;storeid=26852ae96000d02058d26d88d0000006e03004fb3535a299f001156e433bc7&amp;ef=3&amp;bizid=1022" externmd5="723d4d17ad102650bc190aab0413a83e" width="334" height="454" tpurl="" tpauthkey="" attachedtext="" attachedtextcolor="" lensid="" emojiattr="" linkid="" desc=""></emoji><gameext type="0" content="0"></gameext><extcommoninfo></extcommoninfo></msg>'}, 'Status': 3, 'ImgStatus': 2, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1753851281, 'MsgSource': '<msgsource>\n\t<silence>0</silence>\n\t<membercount>73</membercount>\n\t<signature>N0_V1_Nrgo7wUk|v1_fSanG9KX</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'PushContent': '阿尼亚与她在群聊中发了一个表情', 'NewMsgId': 6835487863382512724, 'MsgSeq': 871411641}
2025-07-30 12:54:28 | INFO | 收到表情消息: 消息ID:638953633 来自:***********@chatroom 发送人:wxid_jegyk4i3v7zg22 MD5:aaf37f2f70b3b978f794210146d67fd9 大小:4452666
2025-07-30 12:54:29 | DEBUG | [DoubaoImageRecognition] 缓存表情消息: 6835487863382512724
2025-07-30 13:00:49 | DEBUG | 收到消息: {'MsgId': 1612374853, 'FromUserName': {'string': '***********@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 49, 'Content': {'string': 'xiaomaochong:\n<?xml version="1.0"?>\n<msg>\n\t<appmsg>\n\t\t<title>Lehrling的聊天记录</title>\n\t\t<des>Lehrling: [视频]\nLehrling: [图片]\nLehrling: 我们泸州一个朋友的女朋友去云南卖一次3000\nLehrling: 被男朋友逮到了   拍了这个视频</des>\n\t\t<action>view</action>\n\t\t<type>19</type>\n\t\t<url>https://support.weixin.qq.com/cgi-bin/mmsupport-bin/readtemplate?t=page/favorite_record__w_unsupport&amp;from=singlemessage&amp;isappinstalled=0</url>\n\t\t<recorditem><![CDATA[<recordinfo><title>Lehrling的聊天记录</title><desc>Lehrling:&#x20;[视频]&#x0A;Lehrling:&#x20;[图片]&#x0A;Lehrling:&#x20;我们泸州一个朋友的女朋友去云南卖一次3000&#x0A;Lehrling:&#x20;被男朋友逮到了&#x20;&#x20;&#x20;拍了这个视频</desc><datalist count="5"><dataitem datatype="4" dataid="35e7f95b2e33466b7bff978d27c9f643"><cdnthumburl>3057020100044b3049020100020452f33d3b02032f54690204739999db0204688843b9042466616139666466302d623633332d343139652d383034352d6437356333663062343966640204059420010201000405004c543d00</cdnthumburl><cdnthumbkey>05e4f14d399a860bebe650947f03e443</cdnthumbkey><thumbfullmd5>82e95bb0e3514ff4a96ce6339b3ec8de</thumbfullmd5><thumbsize>10619</thumbsize><cdndataurl>3057020100044b304902010002044281421902032f54690204699999db0204688749cd042439366165326138662d326531372d346438612d616366302d6435336465366437356465330204051808040201000405004c54a100</cdndataurl><cdndatakey>20a1aa2b275c54c145bbe5a2316d3642</cdndatakey><fullmd5>e0317ddf822738fd6d52fd8fb1371766</fullmd5><datafmt>mp4</datafmt><duration>48</duration><head256md5>cc07bb583be7dd2919c17f715992e028</head256md5><sourcename>Lehrling</sourcename><sourceheadurl>https://wx.qlogo.cn/mmhead/ver_1/cFoOxcJzLTO3iaxxveSHuplKib0RLgog1FuqUxx9xQGY3r8bElgj6k9Bh2aZhicEPQ70fna2ibRNcNSzkneiaSgSmsJ2icu7KaLsPMpwJugxz1wtwaN5J92pXGNC9LKaAiauZlz/132</sourceheadurl><sourcetime>2025-07-29&#x20;11:40:47</sourcetime><srcMsgCreateTime>1753760447</srcMsgCreateTime><messageuuid>22584707e02346303c83efda6276167d_</messageuuid><fromnewmsgid>5544425851274583525</fromnewmsgid></dataitem><dataitem datatype="2" dataid="427adc38c9a706dfb8cdb46de69c5469"><cdnthumburl>3057020100044b3049020100020452f33d3b02032f54690204739999db0204688843b9042466326566323464392d346437322d343232372d386263382d3462656464633631633831620204059420010201000405004c50b900</cdnthumburl><cdnthumbkey>4ba8710422d211498a43d73c6176a745</cdnthumbkey><thumbfullmd5>67e07d9bcab6eeda761ff1845f32237f</thumbfullmd5><thumbsize>10771</thumbsize><cdndataurl>3057020100044b304902010002043a85361c02032f559502042242f7df0204688985d5042461613264623230322d353965612d343330332d626535302d3665376439356539653137370204059820010201000405004c518100</cdndataurl><cdndatakey>c3f88c26be058684374ea5fc0446af6d</cdndatakey><fullmd5>afcb319e4fb7423abb9b7e94454e6a3d</fullmd5><datasize>245193</datasize><datafmt>jpg</datafmt><head256md5>5a81ecbab985980f66cd592d07d30c77</head256md5><sourcename>Lehrling</sourcename><sourceheadurl>https://wx.qlogo.cn/mmhead/ver_1/cFoOxcJzLTO3iaxxveSHuplKib0RLgog1FuqUxx9xQGY3r8bElgj6k9Bh2aZhicEPQ70fna2ibRNcNSzkneiaSgSmsJ2icu7KaLsPMpwJugxz1wtwaN5J92pXGNC9LKaAiauZlz/132</sourceheadurl><sourcetime>2025-07-29&#x20;11:40:56</sourcetime><srcMsgCreateTime>1753760456</srcMsgCreateTime><messageuuid>ca3bf20485677462fec0fb21d51b75dd_</messageuuid><fromnewmsgid>8332122678751704185</fromnewmsgid></dataitem><dataitem datatype="1" dataid="25c5eb95feda96f5c802892947895bc3"><datadesc>我们泸州一个朋友的女朋友去云南卖一次3000</datadesc><sourcename>Lehrling</sourcename><sourceheadurl>https://wx.qlogo.cn/mmhead/ver_1/cFoOxcJzLTO3iaxxveSHuplKib0RLgog1FuqUxx9xQGY3r8bElgj6k9Bh2aZhicEPQ70fna2ibRNcNSzkneiaSgSmsJ2icu7KaLsPMpwJugxz1wtwaN5J92pXGNC9LKaAiauZlz/132</sourceheadurl><sourcetime>2025-07-29&#x20;11:41:15</sourcetime><srcMsgCreateTime>1753760475</srcMsgCreateTime><fromnewmsgid>8078266616818792686</fromnewmsgid></dataitem><dataitem datatype="1" dataid="0e56b85fa4e0268b289a3d84432671a7"><datadesc>被男朋友逮到了&#x20;&#x20;&#x20;拍了这个视频</datadesc><sourcename>Lehrling</sourcename><sourceheadurl>https://wx.qlogo.cn/mmhead/ver_1/cFoOxcJzLTO3iaxxveSHuplKib0RLgog1FuqUxx9xQGY3r8bElgj6k9Bh2aZhicEPQ70fna2ibRNcNSzkneiaSgSmsJ2icu7KaLsPMpwJugxz1wtwaN5J92pXGNC9LKaAiauZlz/132</sourceheadurl><sourcetime>2025-07-29&#x20;11:41:22</sourcetime><srcMsgCreateTime>1753760482</srcMsgCreateTime><fromnewmsgid>5409750279689916524</fromnewmsgid></dataitem><dataitem datatype="1" dataid="ae986b4f2dbf071630f9bee78fb30f21"><datadesc>发给所有人</datadesc><sourcename>Lehrling</sourcename><sourceheadurl>https://wx.qlogo.cn/mmhead/ver_1/cFoOxcJzLTO3iaxxveSHuplKib0RLgog1FuqUxx9xQGY3r8bElgj6k9Bh2aZhicEPQ70fna2ibRNcNSzkneiaSgSmsJ2icu7KaLsPMpwJugxz1wtwaN5J92pXGNC9LKaAiauZlz/132</sourceheadurl><sourcetime>2025-07-29&#x20;11:41:38</sourcetime><srcMsgCreateTime>1753760498</srcMsgCreateTime><fromnewmsgid>4489571173535569019</fromnewmsgid></dataitem></datalist><favcreatetime>1753851661854</favcreatetime></recordinfo>]]></recorditem>\n\t\t<appattach />\n\t</appmsg>\n\t<fromusername>xiaomaochong</fromusername>\n\t<scene>0</scene>\n\t<appinfo>\n\t\t<version>1</version>\n\t\t<appname />\n\t</appinfo>\n\t<commenturl />\n</msg>\n'}, 'Status': 3, 'ImgStatus': 2, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1753851662, 'MsgSource': '<msgsource>\n\t<alnode>\n\t\t<fr>4</fr>\n\t</alnode>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n\t<passthrough>\n\t\t<forward_depth>1</forward_depth>\n\t</passthrough>\n\t<sec_msg_node>\n\t\t<uuid>4f0bd764bda723e9e1db65ae66a55207_</uuid>\n\t\t<risk-file-flag />\n\t\t<risk-file-md5-list />\n\t</sec_msg_node>\n\t<silence>0</silence>\n\t<membercount>73</membercount>\n\t<signature>N0_V1_SONytdq6|v1_6oKDTNSI</signature>\n</msgsource>\n', 'PushContent': '小爱 : [聊天记录]', 'NewMsgId': 4416322340738789564, 'MsgSeq': 871411642}
2025-07-30 13:00:49 | DEBUG | 从群聊消息中提取发送者: xiaomaochong
2025-07-30 13:00:49 | DEBUG | XML消息完整内容:
<?xml version="1.0"?>
<msg>
	<appmsg>
		<title>Lehrling的聊天记录</title>
		<des>Lehrling: [视频]
Lehrling: [图片]
Lehrling: 我们泸州一个朋友的女朋友去云南卖一次3000
Lehrling: 被男朋友逮到了   拍了这个视频</des>
		<action>view</action>
		<type>19</type>
		<url>https://support.weixin.qq.com/cgi-bin/mmsupport-bin/readtemplate?t=page/favorite_record__w_unsupport&amp;from=singlemessage&amp;isappinstalled=0</url>
		<recorditem><![CDATA[<recordinfo><title>Lehrling的聊天记录</title><desc>Lehrling:&#x20;[视频]&#x0A;Lehrling:&#x20;[图片]&#x0A;Lehrling:&#x20;我们泸州一个朋友的女朋友去云南卖一次3000&#x0A;Lehrling:&#x20;被男朋友逮到了&#x20;&#x20;&#x20;拍了这个视频</desc><datalist count="5"><dataitem datatype="4" dataid="35e7f95b2e33466b7bff978d27c9f643"><cdnthumburl>3057020100044b3049020100020452f33d3b02032f54690204739999db0204688843b9042466616139666466302d623633332d343139652d383034352d6437356333663062343966640204059420010201000405004c543d00</cdnthumburl><cdnthumbkey>05e4f14d399a860bebe650947f03e443</cdnthumbkey><thumbfullmd5>82e95bb0e3514ff4a96ce6339b3ec8de</thumbfullmd5><thumbsize>10619</thumbsize><cdndataurl>3057020100044b304902010002044281421902032f54690204699999db0204688749cd042439366165326138662d326531372d346438612d616366302d6435336465366437356465330204051808040201000405004c54a100</cdndataurl><cdndatakey>20a1aa2b275c54c145bbe5a2316d3642</cdndatakey><fullmd5>e0317ddf822738fd6d52fd8fb1371766</fullmd5><datafmt>mp4</datafmt><duration>48</duration><head256md5>cc07bb583be7dd2919c17f715992e028</head256md5><sourcename>Lehrling</sourcename><sourceheadurl>https://wx.qlogo.cn/mmhead/ver_1/cFoOxcJzLTO3iaxxveSHuplKib0RLgog1FuqUxx9xQGY3r8bElgj6k9Bh2aZhicEPQ70fna2ibRNcNSzkneiaSgSmsJ2icu7KaLsPMpwJugxz1wtwaN5J92pXGNC9LKaAiauZlz/132</sourceheadurl><sourcetime>2025-07-29&#x20;11:40:47</sourcetime><srcMsgCreateTime>1753760447</srcMsgCreateTime><messageuuid>22584707e02346303c83efda6276167d_</messageuuid><fromnewmsgid>5544425851274583525</fromnewmsgid></dataitem><dataitem datatype="2" dataid="427adc38c9a706dfb8cdb46de69c5469"><cdnthumburl>3057020100044b3049020100020452f33d3b02032f54690204739999db0204688843b9042466326566323464392d346437322d343232372d386263382d3462656464633631633831620204059420010201000405004c50b900</cdnthumburl><cdnthumbkey>4ba8710422d211498a43d73c6176a745</cdnthumbkey><thumbfullmd5>67e07d9bcab6eeda761ff1845f32237f</thumbfullmd5><thumbsize>10771</thumbsize><cdndataurl>3057020100044b304902010002043a85361c02032f559502042242f7df0204688985d5042461613264623230322d353965612d343330332d626535302d3665376439356539653137370204059820010201000405004c518100</cdndataurl><cdndatakey>c3f88c26be058684374ea5fc0446af6d</cdndatakey><fullmd5>afcb319e4fb7423abb9b7e94454e6a3d</fullmd5><datasize>245193</datasize><datafmt>jpg</datafmt><head256md5>5a81ecbab985980f66cd592d07d30c77</head256md5><sourcename>Lehrling</sourcename><sourceheadurl>https://wx.qlogo.cn/mmhead/ver_1/cFoOxcJzLTO3iaxxveSHuplKib0RLgog1FuqUxx9xQGY3r8bElgj6k9Bh2aZhicEPQ70fna2ibRNcNSzkneiaSgSmsJ2icu7KaLsPMpwJugxz1wtwaN5J92pXGNC9LKaAiauZlz/132</sourceheadurl><sourcetime>2025-07-29&#x20;11:40:56</sourcetime><srcMsgCreateTime>1753760456</srcMsgCreateTime><messageuuid>ca3bf20485677462fec0fb21d51b75dd_</messageuuid><fromnewmsgid>8332122678751704185</fromnewmsgid></dataitem><dataitem datatype="1" dataid="25c5eb95feda96f5c802892947895bc3"><datadesc>我们泸州一个朋友的女朋友去云南卖一次3000</datadesc><sourcename>Lehrling</sourcename><sourceheadurl>https://wx.qlogo.cn/mmhead/ver_1/cFoOxcJzLTO3iaxxveSHuplKib0RLgog1FuqUxx9xQGY3r8bElgj6k9Bh2aZhicEPQ70fna2ibRNcNSzkneiaSgSmsJ2icu7KaLsPMpwJugxz1wtwaN5J92pXGNC9LKaAiauZlz/132</sourceheadurl><sourcetime>2025-07-29&#x20;11:41:15</sourcetime><srcMsgCreateTime>1753760475</srcMsgCreateTime><fromnewmsgid>8078266616818792686</fromnewmsgid></dataitem><dataitem datatype="1" dataid="0e56b85fa4e0268b289a3d84432671a7"><datadesc>被男朋友逮到了&#x20;&#x20;&#x20;拍了这个视频</datadesc><sourcename>Lehrling</sourcename><sourceheadurl>https://wx.qlogo.cn/mmhead/ver_1/cFoOxcJzLTO3iaxxveSHuplKib0RLgog1FuqUxx9xQGY3r8bElgj6k9Bh2aZhicEPQ70fna2ibRNcNSzkneiaSgSmsJ2icu7KaLsPMpwJugxz1wtwaN5J92pXGNC9LKaAiauZlz/132</sourceheadurl><sourcetime>2025-07-29&#x20;11:41:22</sourcetime><srcMsgCreateTime>1753760482</srcMsgCreateTime><fromnewmsgid>5409750279689916524</fromnewmsgid></dataitem><dataitem datatype="1" dataid="ae986b4f2dbf071630f9bee78fb30f21"><datadesc>发给所有人</datadesc><sourcename>Lehrling</sourcename><sourceheadurl>https://wx.qlogo.cn/mmhead/ver_1/cFoOxcJzLTO3iaxxveSHuplKib0RLgog1FuqUxx9xQGY3r8bElgj6k9Bh2aZhicEPQ70fna2ibRNcNSzkneiaSgSmsJ2icu7KaLsPMpwJugxz1wtwaN5J92pXGNC9LKaAiauZlz/132</sourceheadurl><sourcetime>2025-07-29&#x20;11:41:38</sourcetime><srcMsgCreateTime>1753760498</srcMsgCreateTime><fromnewmsgid>4489571173535569019</fromnewmsgid></dataitem></datalist><favcreatetime>1753851661854</favcreatetime></recordinfo>]]></recorditem>
		<appattach />
	</appmsg>
	<fromusername>xiaomaochong</fromusername>
	<scene>0</scene>
	<appinfo>
		<version>1</version>
		<appname />
	</appinfo>
	<commenturl />
</msg>

2025-07-30 13:00:49 | DEBUG | XML消息类型: 19
2025-07-30 13:00:49 | DEBUG | XML消息标题: Lehrling的聊天记录
2025-07-30 13:00:49 | DEBUG | XML消息描述: Lehrling: [视频]
Lehrling: [图片]
Lehrling: 我们泸州一个朋友的女朋友去云南卖一次3000
Lehrling: 被男朋友逮到了   拍了这个视频
2025-07-30 13:00:49 | DEBUG | XML消息URL: https://support.weixin.qq.com/cgi-bin/mmsupport-bin/readtemplate?t=page/favorite_record__w_unsupport&from=singlemessage&isappinstalled=0
2025-07-30 13:00:49 | INFO | 未知的XML消息类型: 19
2025-07-30 13:00:49 | INFO | 消息标题: Lehrling的聊天记录
2025-07-30 13:00:49 | INFO | 消息描述: Lehrling: [视频]
Lehrling: [图片]
Lehrling: 我们泸州一个朋友的女朋友去云南卖一次3000
Lehrling: 被男朋友逮到了   拍了这个视频
2025-07-30 13:00:49 | INFO | 消息URL: https://support.weixin.qq.com/cgi-bin/mmsupport-bin/readtemplate?t=page/favorite_record__w_unsupport&from=singlemessage&isappinstalled=0
2025-07-30 13:00:49 | INFO | 完整XML内容:
<?xml version="1.0"?>
<msg>
	<appmsg>
		<title>Lehrling的聊天记录</title>
		<des>Lehrling: [视频]
Lehrling: [图片]
Lehrling: 我们泸州一个朋友的女朋友去云南卖一次3000
Lehrling: 被男朋友逮到了   拍了这个视频</des>
		<action>view</action>
		<type>19</type>
		<url>https://support.weixin.qq.com/cgi-bin/mmsupport-bin/readtemplate?t=page/favorite_record__w_unsupport&amp;from=singlemessage&amp;isappinstalled=0</url>
		<recorditem><![CDATA[<recordinfo><title>Lehrling的聊天记录</title><desc>Lehrling:&#x20;[视频]&#x0A;Lehrling:&#x20;[图片]&#x0A;Lehrling:&#x20;我们泸州一个朋友的女朋友去云南卖一次3000&#x0A;Lehrling:&#x20;被男朋友逮到了&#x20;&#x20;&#x20;拍了这个视频</desc><datalist count="5"><dataitem datatype="4" dataid="35e7f95b2e33466b7bff978d27c9f643"><cdnthumburl>3057020100044b3049020100020452f33d3b02032f54690204739999db0204688843b9042466616139666466302d623633332d343139652d383034352d6437356333663062343966640204059420010201000405004c543d00</cdnthumburl><cdnthumbkey>05e4f14d399a860bebe650947f03e443</cdnthumbkey><thumbfullmd5>82e95bb0e3514ff4a96ce6339b3ec8de</thumbfullmd5><thumbsize>10619</thumbsize><cdndataurl>3057020100044b304902010002044281421902032f54690204699999db0204688749cd042439366165326138662d326531372d346438612d616366302d6435336465366437356465330204051808040201000405004c54a100</cdndataurl><cdndatakey>20a1aa2b275c54c145bbe5a2316d3642</cdndatakey><fullmd5>e0317ddf822738fd6d52fd8fb1371766</fullmd5><datafmt>mp4</datafmt><duration>48</duration><head256md5>cc07bb583be7dd2919c17f715992e028</head256md5><sourcename>Lehrling</sourcename><sourceheadurl>https://wx.qlogo.cn/mmhead/ver_1/cFoOxcJzLTO3iaxxveSHuplKib0RLgog1FuqUxx9xQGY3r8bElgj6k9Bh2aZhicEPQ70fna2ibRNcNSzkneiaSgSmsJ2icu7KaLsPMpwJugxz1wtwaN5J92pXGNC9LKaAiauZlz/132</sourceheadurl><sourcetime>2025-07-29&#x20;11:40:47</sourcetime><srcMsgCreateTime>1753760447</srcMsgCreateTime><messageuuid>22584707e02346303c83efda6276167d_</messageuuid><fromnewmsgid>5544425851274583525</fromnewmsgid></dataitem><dataitem datatype="2" dataid="427adc38c9a706dfb8cdb46de69c5469"><cdnthumburl>3057020100044b3049020100020452f33d3b02032f54690204739999db0204688843b9042466326566323464392d346437322d343232372d386263382d3462656464633631633831620204059420010201000405004c50b900</cdnthumburl><cdnthumbkey>4ba8710422d211498a43d73c6176a745</cdnthumbkey><thumbfullmd5>67e07d9bcab6eeda761ff1845f32237f</thumbfullmd5><thumbsize>10771</thumbsize><cdndataurl>3057020100044b304902010002043a85361c02032f559502042242f7df0204688985d5042461613264623230322d353965612d343330332d626535302d3665376439356539653137370204059820010201000405004c518100</cdndataurl><cdndatakey>c3f88c26be058684374ea5fc0446af6d</cdndatakey><fullmd5>afcb319e4fb7423abb9b7e94454e6a3d</fullmd5><datasize>245193</datasize><datafmt>jpg</datafmt><head256md5>5a81ecbab985980f66cd592d07d30c77</head256md5><sourcename>Lehrling</sourcename><sourceheadurl>https://wx.qlogo.cn/mmhead/ver_1/cFoOxcJzLTO3iaxxveSHuplKib0RLgog1FuqUxx9xQGY3r8bElgj6k9Bh2aZhicEPQ70fna2ibRNcNSzkneiaSgSmsJ2icu7KaLsPMpwJugxz1wtwaN5J92pXGNC9LKaAiauZlz/132</sourceheadurl><sourcetime>2025-07-29&#x20;11:40:56</sourcetime><srcMsgCreateTime>1753760456</srcMsgCreateTime><messageuuid>ca3bf20485677462fec0fb21d51b75dd_</messageuuid><fromnewmsgid>8332122678751704185</fromnewmsgid></dataitem><dataitem datatype="1" dataid="25c5eb95feda96f5c802892947895bc3"><datadesc>我们泸州一个朋友的女朋友去云南卖一次3000</datadesc><sourcename>Lehrling</sourcename><sourceheadurl>https://wx.qlogo.cn/mmhead/ver_1/cFoOxcJzLTO3iaxxveSHuplKib0RLgog1FuqUxx9xQGY3r8bElgj6k9Bh2aZhicEPQ70fna2ibRNcNSzkneiaSgSmsJ2icu7KaLsPMpwJugxz1wtwaN5J92pXGNC9LKaAiauZlz/132</sourceheadurl><sourcetime>2025-07-29&#x20;11:41:15</sourcetime><srcMsgCreateTime>1753760475</srcMsgCreateTime><fromnewmsgid>8078266616818792686</fromnewmsgid></dataitem><dataitem datatype="1" dataid="0e56b85fa4e0268b289a3d84432671a7"><datadesc>被男朋友逮到了&#x20;&#x20;&#x20;拍了这个视频</datadesc><sourcename>Lehrling</sourcename><sourceheadurl>https://wx.qlogo.cn/mmhead/ver_1/cFoOxcJzLTO3iaxxveSHuplKib0RLgog1FuqUxx9xQGY3r8bElgj6k9Bh2aZhicEPQ70fna2ibRNcNSzkneiaSgSmsJ2icu7KaLsPMpwJugxz1wtwaN5J92pXGNC9LKaAiauZlz/132</sourceheadurl><sourcetime>2025-07-29&#x20;11:41:22</sourcetime><srcMsgCreateTime>1753760482</srcMsgCreateTime><fromnewmsgid>5409750279689916524</fromnewmsgid></dataitem><dataitem datatype="1" dataid="ae986b4f2dbf071630f9bee78fb30f21"><datadesc>发给所有人</datadesc><sourcename>Lehrling</sourcename><sourceheadurl>https://wx.qlogo.cn/mmhead/ver_1/cFoOxcJzLTO3iaxxveSHuplKib0RLgog1FuqUxx9xQGY3r8bElgj6k9Bh2aZhicEPQ70fna2ibRNcNSzkneiaSgSmsJ2icu7KaLsPMpwJugxz1wtwaN5J92pXGNC9LKaAiauZlz/132</sourceheadurl><sourcetime>2025-07-29&#x20;11:41:38</sourcetime><srcMsgCreateTime>1753760498</srcMsgCreateTime><fromnewmsgid>4489571173535569019</fromnewmsgid></dataitem></datalist><favcreatetime>1753851661854</favcreatetime></recordinfo>]]></recorditem>
		<appattach />
	</appmsg>
	<fromusername>xiaomaochong</fromusername>
	<scene>0</scene>
	<appinfo>
		<version>1</version>
		<appname />
	</appinfo>
	<commenturl />
</msg>

2025-07-30 13:03:19 | INFO | [TempFileManager] 开始清理临时文件...
2025-07-30 13:03:20 | DEBUG | 开始检查群成员变化，当前监控 2 个群
2025-07-30 13:03:21 | DEBUG | 群成员变化检查完成
2025-07-30 13:03:23 | DEBUG | 收到消息: {'MsgId': 1480380327, 'FromUserName': {'string': '***********@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 49, 'Content': {'string': 'xiaomaochong:\n<?xml version="1.0"?>\n<msg>\n\t<appmsg appid="" sdkver="0">\n\t\t<title>解析</title>\n\t\t<des />\n\t\t<username />\n\t\t<action>view</action>\n\t\t<type>57</type>\n\t\t<showtype>0</showtype>\n\t\t<content />\n\t\t<url />\n\t\t<lowurl />\n\t\t<forwardflag>0</forwardflag>\n\t\t<dataurl />\n\t\t<lowdataurl />\n\t\t<contentattr>0</contentattr>\n\t\t<streamvideo>\n\t\t\t<streamvideourl />\n\t\t\t<streamvideototaltime>0</streamvideototaltime>\n\t\t\t<streamvideotitle />\n\t\t\t<streamvideowording />\n\t\t\t<streamvideoweburl />\n\t\t\t<streamvideothumburl />\n\t\t\t<streamvideoaduxinfo />\n\t\t\t<streamvideopublishid />\n\t\t</streamvideo>\n\t\t<canvasPageItem>\n\t\t\t<canvasPageXml><![CDATA[]]></canvasPageXml>\n\t\t</canvasPageItem>\n\t\t<refermsg>\n\t\t\t<type>1</type>\n\t\t\t<svrid>4942072981175204797</svrid>\n\t\t\t<fromusr>***********@chatroom</fromusr>\n\t\t\t<chatusr>wxid_jegyk4i3v7zg22</chatusr>\n\t\t\t<displayname>阿尼亚与她</displayname>\n\t\t\t<msgsource>&lt;msgsource&gt;\n\t&lt;bizflag&gt;0&lt;/bizflag&gt;\n\t&lt;pua&gt;1&lt;/pua&gt;\n\t&lt;alnode&gt;\n\t\t&lt;inlenlist&gt;89&lt;/inlenlist&gt;\n\t&lt;/alnode&gt;\n\t&lt;eggIncluded&gt;1&lt;/eggIncluded&gt;\n\t&lt;silence&gt;1&lt;/silence&gt;\n\t&lt;membercount&gt;73&lt;/membercount&gt;\n\t&lt;signature&gt;N0_V1_a1BZOahv|v1_skMyKyPq&lt;/signature&gt;\n\t&lt;tmp_node&gt;\n\t\t&lt;publisher-id&gt;&lt;/publisher-id&gt;\n\t&lt;/tmp_node&gt;\n&lt;sec_msg_node&gt;&lt;fold-reduce&gt;1&lt;/fold-reduce&gt;&lt;/sec_msg_node&gt;&lt;/msgsource&gt;\n</msgsource>\n\t\t\t<content>\n0.02 复制打开抖音，看看【宝贝曾曾的作品】零帧起手。# 宝贝曾曾  https://v.douyin.com/CYCjPb2SKfY/ <EMAIL> 03/11 UYZ:/ </content>\n\t\t\t<strid />\n\t\t\t<createtime>1753851182</createtime>\n\t\t</refermsg>\n\t\t<appattach>\n\t\t\t<totallen>0</totallen>\n\t\t\t<attachid />\n\t\t\t<cdnattachurl />\n\t\t\t<emoticonmd5></emoticonmd5>\n\t\t\t<aeskey></aeskey>\n\t\t\t<fileext />\n\t\t\t<islargefilemsg>0</islargefilemsg>\n\t\t</appattach>\n\t\t<extinfo />\n\t\t<androidsource>0</androidsource>\n\t\t<thumburl />\n\t\t<mediatagname />\n\t\t<messageaction><![CDATA[]]></messageaction>\n\t\t<messageext><![CDATA[]]></messageext>\n\t\t<emoticongift>\n\t\t\t<packageflag>0</packageflag>\n\t\t\t<packageid />\n\t\t</emoticongift>\n\t\t<emoticonshared>\n\t\t\t<packageflag>0</packageflag>\n\t\t\t<packageid />\n\t\t</emoticonshared>\n\t\t<designershared>\n\t\t\t<designeruin>0</designeruin>\n\t\t\t<designername>null</designername>\n\t\t\t<designerrediretcturl><![CDATA[null]]></designerrediretcturl>\n\t\t</designershared>\n\t\t<emotionpageshared>\n\t\t\t<tid>0</tid>\n\t\t\t<title>null</title>\n\t\t\t<desc>null</desc>\n\t\t\t<iconUrl><![CDATA[null]]></iconUrl>\n\t\t\t<secondUrl>null</secondUrl>\n\t\t\t<pageType>0</pageType>\n\t\t\t<setKey>null</setKey>\n\t\t</emotionpageshared>\n\t\t<webviewshared>\n\t\t\t<shareUrlOriginal />\n\t\t\t<shareUrlOpen />\n\t\t\t<jsAppId />\n\t\t\t<publisherId />\n\t\t\t<publisherReqId />\n\t\t</webviewshared>\n\t\t<template_id />\n\t\t<md5 />\n\t\t<websearch>\n\t\t\t<rec_category>0</rec_category>\n\t\t\t<channelId>0</channelId>\n\t\t</websearch>\n\t\t<weappinfo>\n\t\t\t<username />\n\t\t\t<appid />\n\t\t\t<appservicetype>0</appservicetype>\n\t\t\t<secflagforsinglepagemode>0</secflagforsinglepagemode>\n\t\t\t<videopageinfo>\n\t\t\t\t<thumbwidth>0</thumbwidth>\n\t\t\t\t<thumbheight>0</thumbheight>\n\t\t\t\t<fromopensdk>0</fromopensdk>\n\t\t\t</videopageinfo>\n\t\t</weappinfo>\n\t\t<statextstr />\n\t\t<musicShareItem>\n\t\t\t<musicDuration>0</musicDuration>\n\t\t</musicShareItem>\n\t\t<finderLiveProductShare>\n\t\t\t<finderLiveID><![CDATA[]]></finderLiveID>\n\t\t\t<finderUsername><![CDATA[]]></finderUsername>\n\t\t\t<finderObjectID><![CDATA[]]></finderObjectID>\n\t\t\t<finderNonceID><![CDATA[]]></finderNonceID>\n\t\t\t<liveStatus><![CDATA[]]></liveStatus>\n\t\t\t<appId><![CDATA[]]></appId>\n\t\t\t<pagePath><![CDATA[]]></pagePath>\n\t\t\t<productId><![CDATA[]]></productId>\n\t\t\t<coverUrl><![CDATA[]]></coverUrl>\n\t\t\t<productTitle><![CDATA[]]></productTitle>\n\t\t\t<marketPrice><![CDATA[0]]></marketPrice>\n\t\t\t<sellingPrice><![CDATA[0]]></sellingPrice>\n\t\t\t<platformHeadImg><![CDATA[]]></platformHeadImg>\n\t\t\t<platformName><![CDATA[]]></platformName>\n\t\t\t<shopWindowId><![CDATA[]]></shopWindowId>\n\t\t\t<flashSalePrice><![CDATA[0]]></flashSalePrice>\n\t\t\t<flashSaleEndTime><![CDATA[0]]></flashSaleEndTime>\n\t\t\t<ecSource><![CDATA[]]></ecSource>\n\t\t\t<sellingPriceWording><![CDATA[]]></sellingPriceWording>\n\t\t\t<platformIconURL><![CDATA[]]></platformIconURL>\n\t\t\t<firstProductTagURL><![CDATA[]]></firstProductTagURL>\n\t\t\t<firstProductTagAspectRatioString><![CDATA[0.0]]></firstProductTagAspectRatioString>\n\t\t\t<secondProductTagURL><![CDATA[]]></secondProductTagURL>\n\t\t\t<secondProductTagAspectRatioString><![CDATA[0.0]]></secondProductTagAspectRatioString>\n\t\t\t<firstGuaranteeWording><![CDATA[]]></firstGuaranteeWording>\n\t\t\t<secondGuaranteeWording><![CDATA[]]></secondGuaranteeWording>\n\t\t\t<thirdGuaranteeWording><![CDATA[]]></thirdGuaranteeWording>\n\t\t\t<isPriceBeginShow>false</isPriceBeginShow>\n\t\t\t<lastGMsgID><![CDATA[]]></lastGMsgID>\n\t\t\t<promoterKey><![CDATA[]]></promoterKey>\n\t\t\t<discountWording><![CDATA[]]></discountWording>\n\t\t\t<priceSuffixDescription><![CDATA[]]></priceSuffixDescription>\n\t\t\t<showBoxItemStringList />\n\t\t</finderLiveProductShare>\n\t\t<finderOrder>\n\t\t\t<appID><![CDATA[]]></appID>\n\t\t\t<orderID><![CDATA[]]></orderID>\n\t\t\t<path><![CDATA[]]></path>\n\t\t\t<priceWording><![CDATA[]]></priceWording>\n\t\t\t<stateWording><![CDATA[]]></stateWording>\n\t\t\t<productImageURL><![CDATA[]]></productImageURL>\n\t\t\t<products><![CDATA[]]></products>\n\t\t\t<productsCount><![CDATA[0]]></productsCount>\n\t\t</finderOrder>\n\t\t<finderShopWindowShare>\n\t\t\t<finderUsername><![CDATA[]]></finderUsername>\n\t\t\t<avatar><![CDATA[]]></avatar>\n\t\t\t<nickname><![CDATA[]]></nickname>\n\t\t\t<commodityInStockCount><![CDATA[]]></commodityInStockCount>\n\t\t\t<appId><![CDATA[]]></appId>\n\t\t\t<path><![CDATA[]]></path>\n\t\t\t<appUsername><![CDATA[]]></appUsername>\n\t\t\t<query><![CDATA[]]></query>\n\t\t\t<liteAppId><![CDATA[]]></liteAppId>\n\t\t\t<liteAppPath><![CDATA[]]></liteAppPath>\n\t\t\t<liteAppQuery><![CDATA[]]></liteAppQuery>\n\t\t\t<platformTagURL><![CDATA[]]></platformTagURL>\n\t\t\t<saleWording><![CDATA[]]></saleWording>\n\t\t\t<lastGMsgID><![CDATA[]]></lastGMsgID>\n\t\t\t<profileTypeWording><![CDATA[]]></profileTypeWording>\n\t\t\t<reputationInfo>\n\t\t\t\t<hasReputationInfo>0</hasReputationInfo>\n\t\t\t\t<reputationScore>0</reputationScore>\n\t\t\t\t<reputationWording />\n\t\t\t\t<reputationTextColor />\n\t\t\t\t<reputationLevelWording />\n\t\t\t\t<reputationBackgroundColor />\n\t\t\t</reputationInfo>\n\t\t\t<productImageURLList />\n\t\t</finderShopWindowShare>\n\t\t<findernamecard>\n\t\t\t<username />\n\t\t\t<avatar><![CDATA[]]></avatar>\n\t\t\t<nickname />\n\t\t\t<auth_job />\n\t\t\t<auth_icon>0</auth_icon>\n\t\t\t<auth_icon_url />\n\t\t\t<ecSource><![CDATA[]]></ecSource>\n\t\t\t<lastGMsgID><![CDATA[]]></lastGMsgID>\n\t\t</findernamecard>\n\t\t<finderGuarantee>\n\t\t\t<scene><![CDATA[0]]></scene>\n\t\t</finderGuarantee>\n\t\t<directshare>0</directshare>\n\t\t<gamecenter>\n\t\t\t<namecard>\n\t\t\t\t<iconUrl />\n\t\t\t\t<name />\n\t\t\t\t<desc />\n\t\t\t\t<tail />\n\t\t\t\t<jumpUrl />\n\t\t\t</namecard>\n\t\t</gamecenter>\n\t\t<patMsg>\n\t\t\t<chatUser />\n\t\t\t<records>\n\t\t\t\t<recordNum>0</recordNum>\n\t\t\t</records>\n\t\t</patMsg>\n\t\t<secretmsg>\n\t\t\t<issecretmsg>0</issecretmsg>\n\t\t</secretmsg>\n\t\t<referfromscene>0</referfromscene>\n\t\t<gameshare>\n\t\t\t<liteappext>\n\t\t\t\t<liteappbizdata />\n\t\t\t\t<priority>0</priority>\n\t\t\t</liteappext>\n\t\t\t<appbrandext>\n\t\t\t\t<litegameinfo />\n\t\t\t\t<priority>-1</priority>\n\t\t\t</appbrandext>\n\t\t\t<gameshareid />\n\t\t\t<sharedata />\n\t\t\t<isvideo>0</isvideo>\n\t\t\t<duration>-1</duration>\n\t\t\t<isexposed>0</isexposed>\n\t\t\t<readtext />\n\t\t</gameshare>\n\t\t<mpsharetrace>\n\t\t\t<hasfinderelement>0</hasfinderelement>\n\t\t\t<lastgmsgid />\n\t\t</mpsharetrace>\n\t\t<wxgamecard>\n\t\t\t<framesetname />\n\t\t\t<mbcarddata />\n\t\t\t<minpkgversion />\n\t\t\t<mbcardheight>0</mbcardheight>\n\t\t\t<isoldversion>0</isoldversion>\n\t\t</wxgamecard>\n\t\t<liteapp>\n\t\t\t<id>null</id>\n\t\t\t<path />\n\t\t\t<query />\n\t\t</liteapp>\n\t</appmsg>\n\t<fromusername>xiaomaochong</fromusername>\n\t<scene>0</scene>\n\t<appinfo>\n\t\t<version>1</version>\n\t\t<appname />\n\t</appinfo>\n\t<commenturl />\n</msg>\n'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1753851815, 'MsgSource': '<msgsource>\n\t<alnode>\n\t\t<fr>4</fr>\n\t</alnode>\n\t<sec_msg_node>\n\t\t<uuid>2fdc197475fe423c25a5c92b6223ae96_</uuid>\n\t\t<risk-file-flag />\n\t\t<risk-file-md5-list />\n\t</sec_msg_node>\n\t<silence>0</silence>\n\t<membercount>73</membercount>\n\t<signature>N0_V1_y1pMKrlv|v1_1QlANh0I</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'PushContent': '小爱 : 解析', 'NewMsgId': 309379935926128659, 'MsgSeq': 871411643}
2025-07-30 13:03:23 | DEBUG | 从群聊消息中提取发送者: xiaomaochong
2025-07-30 13:03:23 | DEBUG | 使用已解析的XML处理引用消息
2025-07-30 13:03:23 | INFO | 收到引用消息: 消息ID:1480380327 来自:***********@chatroom 发送人:xiaomaochong 内容:解析 引用类型:1
2025-07-30 13:03:24 | INFO | [DouBaoImageToImage] 收到引用消息: 解析
2025-07-30 13:03:24 | INFO | [TimerTask] 收到引用消息调试信息:
2025-07-30 13:03:24 | INFO |   - 消息内容: 解析
2025-07-30 13:03:24 | INFO |   - 群组ID: ***********@chatroom
2025-07-30 13:03:24 | INFO |   - 发送人: xiaomaochong
2025-07-30 13:03:24 | INFO |   - 引用信息: {'MsgType': 1, 'Content': '\n0.02 复制打开抖音，看看【宝贝曾曾的作品】零帧起手。# 宝贝曾曾  https://v.douyin.com/CYCjPb2SKfY/ <EMAIL> 03/11 UYZ:/ ', 'Msgid': '4942072981175204797', 'NewMsgId': '4942072981175204797', 'ToWxid': 'wxid_4usgcju5ey9q29', 'FromWxid': '***********@chatroom', 'Nickname': '阿尼亚与她', 'MsgSource': '<msgsource>\n\t<bizflag>0</bizflag>\n\t<pua>1</pua>\n\t<alnode>\n\t\t<inlenlist>89</inlenlist>\n\t</alnode>\n\t<eggIncluded>1</eggIncluded>\n\t<silence>1</silence>\n\t<membercount>73</membercount>\n\t<signature>N0_V1_a1BZOahv|v1_skMyKyPq</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n<sec_msg_node><fold-reduce>1</fold-reduce></sec_msg_node></msgsource>\n', 'Createtime': '1753851182', 'SenderWxid': 'xiaomaochong'}
2025-07-30 13:03:24 | INFO |   - 引用消息ID: 
2025-07-30 13:03:24 | INFO |   - 引用消息类型: 
2025-07-30 13:03:24 | INFO |   - 引用消息内容: 
0.02 复制打开抖音，看看【宝贝曾曾的作品】零帧起手。# 宝贝曾曾  https://v.douyin.com/CYCjPb2SKfY/ <EMAIL> 03/11 UYZ:/ 
2025-07-30 13:03:24 | INFO |   - 引用消息发送人: xiaomaochong
2025-07-30 13:03:24 | DEBUG | [VideoParser] 处理引用消息命令[解析]: {'MsgType': 1, 'Content': '\n0.02 复制打开抖音，看看【宝贝曾曾的作品】零帧起手。# 宝贝曾曾  https://v.douyin.com/CYCjPb2SKfY/ <EMAIL> 03/11 UYZ:/ ', 'Msgid': '4942072981175204797', 'NewMsgId': '4942072981175204797', 'ToWxid': 'wxid_4usgcju5ey9q29', 'FromWxid': '***********@chatroom', 'Nickname': '阿尼亚与她', 'MsgSource': '<msgsource>\n\t<bizflag>0</bizflag>\n\t<pua>1</pua>\n\t<alnode>\n\t\t<inlenlist>89</inlenlist>\n\t</alnode>\n\t<eggIncluded>1</eggIncluded>\n\t<silence>1</silence>\n\t<membercount>73</membercount>\n\t<signature>N0_V1_a1BZOahv|v1_skMyKyPq</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n<sec_msg_node><fold-reduce>1</fold-reduce></sec_msg_node></msgsource>\n', 'Createtime': '1753851182', 'SenderWxid': 'xiaomaochong'}
2025-07-30 13:03:39 | INFO | 发送链接消息: 对方wxid:***********@chatroom 链接:https://v6-cold.douyinvod.com/beffa8505fb35f92ae486df2fe92dbe9/6889b5d7/video/tos/cn/tos-cn-ve-15c000-ce/o0757JLZA7yIiSrQUx8FEZweQCgfueJ7NgDFBa/?a=1128&ch=0&cr=0&dr=0&cd=0%7C0%7C0%7C0&cv=1&br=311&bt=311&cs=0&ds=3&ft=rVQ6egwwZRR2sCmo0PDS6kFgAX1tGHhygu9eF.nAvJr12nzXT&mime_type=video_mp4&qs=0&rc=aGRkNTUzZDo7ZGQ5OTM3M0BpM2ttbnc5cmxrNDMzbGkzNEBfYDUyXl9eXi0xNDNgYGJfYSMyZC5qMmRzYHFhLS1kLWJzcw%3D%3D&btag=c0010e00090000&cquery=100y&dy_q=**********&feature_id=fea919893f650a8c49286568590446ef&l=202507301303522C73015015D9BE17F272 标题:零帧起手。#宝贝曾曾 描述:暂无描述 缩略图链接:https://p9-sign.douyinpic.com/tos-cn-i-0813c000-ce/owqyP63VWBThaAA1rWA7vzEAiAHiaIFpnMsBJ~tplv-dy-resize-walign-adapt-aq:540:q75.webp?lk3s=138a59ce&x-expires=**********&x-signature=Oiwl0gYoNyGxSwHfEp6sfenwRWs%3D&from=327834062&s=PackSourceEnum_DOUYIN_REFLOW&se=false&sc=cover&biz_tag=aweme_video&l=202507301303516DDC52E42019890F35D3
2025-07-30 13:03:40 | DEBUG | 收到消息: {'MsgId': 213979380, 'FromUserName': {'string': '***********@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 3, 'Content': {'string': 'xiaomaochong:\n<?xml version="1.0"?>\n<msg>\n\t<img aeskey="9b7cd15714a422066c681d25f2785d7e" encryver="1" cdnthumbaeskey="9b7cd15714a422066c681d25f2785d7e" cdnthumburl="3057020100044b304902010002049363814102032f514902041431227502046889a7a8042463633239623264392d366266302d343530382d626362392d353531303832663865323239020405252a010201000405004c4d9900" cdnthumblength="2960" cdnthumbheight="120" cdnthumbwidth="90" cdnmidheight="0" cdnmidwidth="0" cdnhdheight="0" cdnhdwidth="0" cdnmidimgurl="3057020100044b304902010002049363814102032f514902041431227502046889a7a8042463633239623264392d366266302d343530382d626362392d353531303832663865323239020405252a010201000405004c4d9900" length="8841" cdnbigimgurl="3057020100044b304902010002049363814102032f514902041431227502046889a7a8042463633239623264392d366266302d343530382d626362392d353531303832663865323239020405252a010201000405004c4d9900" hdlength="44894" md5="0a1caa11b1a94291c27814069f28dfea" hevc_mid_size="8841" originsourcemd5="3d863bf91998a8b6f2893e427023de26">\n\t\t<secHashInfoBase64 />\n\t\t<live>\n\t\t\t<duration>0</duration>\n\t\t\t<size>0</size>\n\t\t\t<md5 />\n\t\t\t<fileid />\n\t\t\t<hdsize>0</hdsize>\n\t\t\t<hdmd5 />\n\t\t\t<hdfileid />\n\t\t\t<stillimagetimems>0</stillimagetimems>\n\t\t</live>\n\t</img>\n\t<appinfo>\n\t\t<appid>wx0a022393a4dd43b2</appid>\n\t\t<version>5</version>\n\t\t<appname>小爱同学</appname>\n\t\t<isforceupdate>0</isforceupdate>\n\t\t<messageaction />\n\t\t<messageext />\n\t\t<mediatagname />\n\t</appinfo>\n\t<platform_signature />\n\t<imgdatahash />\n\t<ImgSourceInfo>\n\t\t<ImgSourceUrl />\n\t\t<BizType>0</BizType>\n\t</ImgSourceInfo>\n</msg>\n'}, 'Status': 3, 'ImgStatus': 2, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1753851816, 'MsgSource': '<msgsource>\n\t<alnode>\n\t\t<fr>2</fr>\n\t</alnode>\n\t<sec_msg_node>\n\t\t<uuid>284ddb56cf212dd9edc606806f1964dd_</uuid>\n\t\t<risk-file-flag />\n\t\t<risk-file-md5-list />\n\t</sec_msg_node>\n\t<silence>0</silence>\n\t<membercount>73</membercount>\n\t<signature>N0_V1_J14r6LvR|v1_0f8RqZqF</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'PushContent': '小爱在群聊中发了一张图片', 'NewMsgId': 8633667680259182136, 'MsgSeq': 871411644}
2025-07-30 13:03:40 | INFO | 收到图片消息: 消息ID:213979380 来自:***********@chatroom 发送人:xiaomaochong XML:<?xml version="1.0"?><msg><img aeskey="9b7cd15714a422066c681d25f2785d7e" encryver="1" cdnthumbaeskey="9b7cd15714a422066c681d25f2785d7e" cdnthumburl="3057020100044b304902010002049363814102032f514902041431227502046889a7a8042463633239623264392d366266302d343530382d626362392d353531303832663865323239020405252a010201000405004c4d9900" cdnthumblength="2960" cdnthumbheight="120" cdnthumbwidth="90" cdnmidheight="0" cdnmidwidth="0" cdnhdheight="0" cdnhdwidth="0" cdnmidimgurl="3057020100044b304902010002049363814102032f514902041431227502046889a7a8042463633239623264392d366266302d343530382d626362392d353531303832663865323239020405252a010201000405004c4d9900" length="8841" cdnbigimgurl="3057020100044b304902010002049363814102032f514902041431227502046889a7a8042463633239623264392d366266302d343530382d626362392d353531303832663865323239020405252a010201000405004c4d9900" hdlength="44894" md5="0a1caa11b1a94291c27814069f28dfea" hevc_mid_size="8841" originsourcemd5="3d863bf91998a8b6f2893e427023de26"><secHashInfoBase64 /><live><duration>0</duration><size>0</size><md5 /><fileid /><hdsize>0</hdsize><hdmd5 /><hdfileid /><stillimagetimems>0</stillimagetimems></live></img><appinfo><appid>wx0a022393a4dd43b2</appid><version>5</version><appname>小爱同学</appname><isforceupdate>0</isforceupdate><messageaction /><messageext /><mediatagname /></appinfo><platform_signature /><imgdatahash /><ImgSourceInfo><ImgSourceUrl /><BizType>0</BizType></ImgSourceInfo></msg>
2025-07-30 13:03:40 | INFO | [ImageEcho] 保存图片信息成功，当前群 ***********@chatroom 已存储 5 张图片
2025-07-30 13:03:40 | INFO | [TimerTask] 缓存图片消息: 213979380
2025-07-30 13:03:41 | DEBUG | 收到消息: {'MsgId': 996538106, 'FromUserName': {'string': '***********@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 43, 'Content': {'string': 'xiaomaochong:\n<?xml version="1.0"?>\n<msg>\n\t<videomsg aeskey="d37fe376721697e7b07324740ce97acd" cdnvideourl="3057020100044b304902010002049363814102032f514902041431227502046889a7ac042434376533343161382d613838352d346466382d626262662d3437346630366663313137640204052808040201000405004c54a100" cdnthumbaeskey="d37fe376721697e7b07324740ce97acd" cdnthumburl="3057020100044b304902010002049363814102032f514902041431227502046889a7ac042434376533343161382d613838352d346466382d626262662d3437346630366663313137640204052808040201000405004c54a100" length="803505" playlength="16" cdnthumblength="12196" cdnthumbwidth="405" cdnthumbheight="720" fromusername="xiaomaochong" md5="5543bb5441ef71d7027f21472581224d" newmd5="7e5a5cadb3814427be0507f318a45d82" isplaceholder="0" rawmd5="0e2899f19518be743c4e7ba1ae174a1f" rawlength="803454" cdnrawvideourl="3056020100044a304802010002035a663f02032f51490204ac31227502046889a7ac042463346532353361382d363964332d343530302d616364362d3762373838323432343862340204059400040201000405004c4d9900" cdnrawvideoaeskey="d3be0e4073cbc6b6954e9d962dec5224" overwritenewmsgid="0" originsourcemd5="5543bb5441ef71d7027f21472581224d" isad="0" />\n</msg>\n'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1753851821, 'MsgSource': '<msgsource>\n\t<videopreloadlen>269462</videopreloadlen>\n\t<sec_msg_node>\n\t\t<uuid>c629eaccd36d681533796cbc6d81e9a2_</uuid>\n\t\t<risk-file-flag />\n\t\t<risk-file-md5-list />\n\t</sec_msg_node>\n\t<silence>0</silence>\n\t<membercount>73</membercount>\n\t<signature>N0_V1_Dwyae7fF|v1_zx385Vw/</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'PushContent': '小爱在群聊中发了一段视频', 'NewMsgId': 6945809177216520887, 'MsgSeq': 871411645}
2025-07-30 13:03:41 | INFO | 收到视频消息: 消息ID:996538106 来自:***********@chatroom 发送人:xiaomaochong XML:
<?xml version="1.0"?>
<msg>
	<videomsg aeskey="d37fe376721697e7b07324740ce97acd" cdnvideourl="3057020100044b304902010002049363814102032f514902041431227502046889a7ac042434376533343161382d613838352d346466382d626262662d3437346630366663313137640204052808040201000405004c54a100" cdnthumbaeskey="d37fe376721697e7b07324740ce97acd" cdnthumburl="3057020100044b304902010002049363814102032f514902041431227502046889a7ac042434376533343161382d613838352d346466382d626262662d3437346630366663313137640204052808040201000405004c54a100" length="803505" playlength="16" cdnthumblength="12196" cdnthumbwidth="405" cdnthumbheight="720" fromusername="xiaomaochong" md5="5543bb5441ef71d7027f21472581224d" newmd5="7e5a5cadb3814427be0507f318a45d82" isplaceholder="0" rawmd5="0e2899f19518be743c4e7ba1ae174a1f" rawlength="803454" cdnrawvideourl="3056020100044a304802010002035a663f02032f51490204ac31227502046889a7ac042463346532353361382d363964332d343530302d616364362d3762373838323432343862340204059400040201000405004c4d9900" cdnrawvideoaeskey="d3be0e4073cbc6b6954e9d962dec5224" overwritenewmsgid="0" originsourcemd5="5543bb5441ef71d7027f21472581224d" isad="0" />
</msg>

2025-07-30 13:04:40 | DEBUG | 收到消息: {'MsgId': 1359590924, 'FromUserName': {'string': '47442567074@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 49, 'Content': {'string': 'xiaomaochong:\n<?xml version="1.0"?>\n<msg>\n\t<appmsg appid="wx485a97c844086dc9" sdkver="0">\n\t\t<title>跑路了</title>\n\t\t<des>KichikuRasen</des>\n\t\t<username />\n\t\t<action>view</action>\n\t\t<type>3</type>\n\t\t<showtype>0</showtype>\n\t\t<content />\n\t\t<url />\n\t\t<lowurl />\n\t\t<forwardflag>0</forwardflag>\n\t\t<dataurl>https://v3-luna.douyinvod.com/29b1c96a304c5023420696f6695b3a01/688b0850/video/tos/cn/tos-cn-ve-2774/o0A8Bk8I9ilGEWMQI0iB49fDMTsTQnu5AhBrCs/</dataurl>\n\t\t<lowdataurl />\n\t\t<contentattr>0</contentattr>\n\t\t<streamvideo>\n\t\t\t<streamvideourl />\n\t\t\t<streamvideototaltime>0</streamvideototaltime>\n\t\t\t<streamvideotitle />\n\t\t\t<streamvideowording />\n\t\t\t<streamvideoweburl />\n\t\t\t<streamvideothumburl />\n\t\t\t<streamvideoaduxinfo />\n\t\t\t<streamvideopublishid />\n\t\t</streamvideo>\n\t\t<canvasPageItem>\n\t\t\t<canvasPageXml><![CDATA[]]></canvasPageXml>\n\t\t</canvasPageItem>\n\t\t<appattach>\n\t\t\t<totallen>0</totallen>\n\t\t\t<attachid />\n\t\t\t<cdnattachurl />\n\t\t\t<emoticonmd5></emoticonmd5>\n\t\t\t<aeskey></aeskey>\n\t\t\t<fileext />\n\t\t\t<islargefilemsg>0</islargefilemsg>\n\t\t</appattach>\n\t\t<extinfo />\n\t\t<androidsource>2</androidsource>\n\t\t<thumburl />\n\t\t<mediatagname />\n\t\t<messageaction><![CDATA[]]></messageaction>\n\t\t<messageext><![CDATA[]]></messageext>\n\t\t<emoticongift>\n\t\t\t<packageflag>0</packageflag>\n\t\t\t<packageid />\n\t\t</emoticongift>\n\t\t<emoticonshared>\n\t\t\t<packageflag>0</packageflag>\n\t\t\t<packageid />\n\t\t</emoticonshared>\n\t\t<designershared>\n\t\t\t<designeruin>0</designeruin>\n\t\t\t<designername>null</designername>\n\t\t\t<designerrediretcturl><![CDATA[null]]></designerrediretcturl>\n\t\t</designershared>\n\t\t<emotionpageshared>\n\t\t\t<tid>0</tid>\n\t\t\t<title>null</title>\n\t\t\t<desc>null</desc>\n\t\t\t<iconUrl><![CDATA[null]]></iconUrl>\n\t\t\t<secondUrl>null</secondUrl>\n\t\t\t<pageType>0</pageType>\n\t\t\t<setKey>null</setKey>\n\t\t</emotionpageshared>\n\t\t<webviewshared>\n\t\t\t<shareUrlOriginal />\n\t\t\t<shareUrlOpen />\n\t\t\t<jsAppId />\n\t\t\t<publisherId />\n\t\t\t<publisherReqId />\n\t\t</webviewshared>\n\t\t<template_id />\n\t\t<md5 />\n\t\t<websearch>\n\t\t\t<rec_category>0</rec_category>\n\t\t\t<channelId>0</channelId>\n\t\t</websearch>\n\t\t<weappinfo>\n\t\t\t<username />\n\t\t\t<appid />\n\t\t\t<appservicetype>0</appservicetype>\n\t\t\t<secflagforsinglepagemode>0</secflagforsinglepagemode>\n\t\t\t<videopageinfo>\n\t\t\t\t<thumbwidth>0</thumbwidth>\n\t\t\t\t<thumbheight>0</thumbheight>\n\t\t\t\t<fromopensdk>0</fromopensdk>\n\t\t\t</videopageinfo>\n\t\t</weappinfo>\n\t\t<statextstr>GhQKEnd4NDg1YTk3Yzg0NDA4NmRjOQ==</statextstr>\n\t\t<songalbumurl>https://wx.qlogo.cn/mmhead/ver_1/ZriaH48s42WfSndwztxrnzk2dIW713hMcwGhrjDsNcfeQvChLDXFVWhsCdQEnLG1dpEtdchUatBpyibHlk3LtjTZO2VmTpK6ibQcTKuwT2e1aY/0</songalbumurl>\n\t\t<songlyric>[00:19.39]无人可知窗寒梦时\n[00:22.63]再忆起别离事\n[00:25.51]不尽心事\n[00:29.15]两行旧词几多相似\n[00:32.43]如同今宵昨日念之\n[00:38.91]清风上南枝\n[00:42.23]梦中仍相思\n[00:45.07]等秋高看山势\n[00:47.71]再叹故知\n[00:50.79]三两笔着墨迟迟不为记事\n[00:55.23]随手便成诗\n[00:57.67]满腹心思此时寻你于句字\n[01:03.79]灯影下呢喃你名字\n[01:07.47]或许是我太偏执\n[01:11.51]万花开遍不及你归时\n[01:44.15]无人可知窗寒梦时\n[01:47.43]再忆起别离事\n[01:50.31]不尽心事\n[01:53.95]两行旧词几多相似\n[01:57.23]如同今宵昨日念之\n[02:03.71]清风上南枝\n[02:06.99]梦中仍相思\n[02:09.87]等秋高看山势\n[02:12.47]再叹故知\n[02:15.59]三两笔着墨迟迟不为记事\n[02:20.03]随手便成诗\n[02:22.47]满腹心思此时寻你于句字\n[02:28.59]灯影下呢喃你名字\n[02:32.27]或许是我太偏执\n[02:36.31]万花开遍不及你归时\n[02:41.63]三两笔着墨迟迟不为记事\n[02:46.07]随手便成诗\n[02:48.55]满腹心思此时寻你于句字\n[02:54.67]灯影下呢喃你名字\n[02:58.39]或许是我太偏执\n[03:02.43]万花开遍不及你归时\n</songlyric>\n\t\t<musicShareItem>\n\t\t\t<musicDuration>0</musicDuration>\n\t\t</musicShareItem>\n\t\t<finderLiveProductShare>\n\t\t\t<finderLiveID><![CDATA[]]></finderLiveID>\n\t\t\t<finderUsername><![CDATA[]]></finderUsername>\n\t\t\t<finderObjectID><![CDATA[]]></finderObjectID>\n\t\t\t<finderNonceID><![CDATA[]]></finderNonceID>\n\t\t\t<liveStatus><![CDATA[]]></liveStatus>\n\t\t\t<appId><![CDATA[]]></appId>\n\t\t\t<pagePath><![CDATA[]]></pagePath>\n\t\t\t<productId><![CDATA[]]></productId>\n\t\t\t<coverUrl><![CDATA[]]></coverUrl>\n\t\t\t<productTitle><![CDATA[]]></productTitle>\n\t\t\t<marketPrice><![CDATA[0]]></marketPrice>\n\t\t\t<sellingPrice><![CDATA[0]]></sellingPrice>\n\t\t\t<platformHeadImg><![CDATA[]]></platformHeadImg>\n\t\t\t<platformName><![CDATA[]]></platformName>\n\t\t\t<shopWindowId><![CDATA[]]></shopWindowId>\n\t\t\t<flashSalePrice><![CDATA[0]]></flashSalePrice>\n\t\t\t<flashSaleEndTime><![CDATA[0]]></flashSaleEndTime>\n\t\t\t<ecSource><![CDATA[]]></ecSource>\n\t\t\t<sellingPriceWording><![CDATA[]]></sellingPriceWording>\n\t\t\t<platformIconURL><![CDATA[]]></platformIconURL>\n\t\t\t<firstProductTagURL><![CDATA[]]></firstProductTagURL>\n\t\t\t<firstProductTagAspectRatioString><![CDATA[0.0]]></firstProductTagAspectRatioString>\n\t\t\t<secondProductTagURL><![CDATA[]]></secondProductTagURL>\n\t\t\t<secondProductTagAspectRatioString><![CDATA[0.0]]></secondProductTagAspectRatioString>\n\t\t\t<firstGuaranteeWording><![CDATA[]]></firstGuaranteeWording>\n\t\t\t<secondGuaranteeWording><![CDATA[]]></secondGuaranteeWording>\n\t\t\t<thirdGuaranteeWording><![CDATA[]]></thirdGuaranteeWording>\n\t\t\t<isPriceBeginShow>false</isPriceBeginShow>\n\t\t\t<lastGMsgID><![CDATA[]]></lastGMsgID>\n\t\t\t<promoterKey><![CDATA[]]></promoterKey>\n\t\t\t<discountWording><![CDATA[]]></discountWording>\n\t\t\t<priceSuffixDescription><![CDATA[]]></priceSuffixDescription>\n\t\t\t<showBoxItemStringList />\n\t\t</finderLiveProductShare>\n\t\t<finderOrder>\n\t\t\t<appID><![CDATA[]]></appID>\n\t\t\t<orderID><![CDATA[]]></orderID>\n\t\t\t<path><![CDATA[]]></path>\n\t\t\t<priceWording><![CDATA[]]></priceWording>\n\t\t\t<stateWording><![CDATA[]]></stateWording>\n\t\t\t<productImageURL><![CDATA[]]></productImageURL>\n\t\t\t<products><![CDATA[]]></products>\n\t\t\t<productsCount><![CDATA[0]]></productsCount>\n\t\t</finderOrder>\n\t\t<finderShopWindowShare>\n\t\t\t<finderUsername><![CDATA[]]></finderUsername>\n\t\t\t<avatar><![CDATA[]]></avatar>\n\t\t\t<nickname><![CDATA[]]></nickname>\n\t\t\t<commodityInStockCount><![CDATA[]]></commodityInStockCount>\n\t\t\t<appId><![CDATA[]]></appId>\n\t\t\t<path><![CDATA[]]></path>\n\t\t\t<appUsername><![CDATA[]]></appUsername>\n\t\t\t<query><![CDATA[]]></query>\n\t\t\t<liteAppId><![CDATA[]]></liteAppId>\n\t\t\t<liteAppPath><![CDATA[]]></liteAppPath>\n\t\t\t<liteAppQuery><![CDATA[]]></liteAppQuery>\n\t\t\t<platformTagURL><![CDATA[]]></platformTagURL>\n\t\t\t<saleWording><![CDATA[]]></saleWording>\n\t\t\t<lastGMsgID><![CDATA[]]></lastGMsgID>\n\t\t\t<profileTypeWording><![CDATA[]]></profileTypeWording>\n\t\t\t<reputationInfo>\n\t\t\t\t<hasReputationInfo>0</hasReputationInfo>\n\t\t\t\t<reputationScore>0</reputationScore>\n\t\t\t\t<reputationWording />\n\t\t\t\t<reputationTextColor />\n\t\t\t\t<reputationLevelWording />\n\t\t\t\t<reputationBackgroundColor />\n\t\t\t</reputationInfo>\n\t\t\t<productImageURLList />\n\t\t</finderShopWindowShare>\n\t\t<findernamecard>\n\t\t\t<username />\n\t\t\t<avatar><![CDATA[]]></avatar>\n\t\t\t<nickname />\n\t\t\t<auth_job />\n\t\t\t<auth_icon>0</auth_icon>\n\t\t\t<auth_icon_url />\n\t\t\t<ecSource><![CDATA[]]></ecSource>\n\t\t\t<lastGMsgID><![CDATA[]]></lastGMsgID>\n\t\t</findernamecard>\n\t\t<finderGuarantee>\n\t\t\t<scene><![CDATA[0]]></scene>\n\t\t</finderGuarantee>\n\t\t<directshare>0</directshare>\n\t\t<gamecenter>\n\t\t\t<namecard>\n\t\t\t\t<iconUrl />\n\t\t\t\t<name />\n\t\t\t\t<desc />\n\t\t\t\t<tail />\n\t\t\t\t<jumpUrl />\n\t\t\t</namecard>\n\t\t</gamecenter>\n\t\t<patMsg>\n\t\t\t<chatUser />\n\t\t\t<records>\n\t\t\t\t<recordNum>0</recordNum>\n\t\t\t</records>\n\t\t</patMsg>\n\t\t<secretmsg>\n\t\t\t<issecretmsg>0</issecretmsg>\n\t\t</secretmsg>\n\t\t<referfromscene>0</referfromscene>\n\t\t<gameshare>\n\t\t\t<liteappext>\n\t\t\t\t<liteappbizdata />\n\t\t\t\t<priority>0</priority>\n\t\t\t</liteappext>\n\t\t\t<appbrandext>\n\t\t\t\t<litegameinfo />\n\t\t\t\t<priority>-1</priority>\n\t\t\t</appbrandext>\n\t\t\t<gameshareid />\n\t\t\t<sharedata />\n\t\t\t<isvideo>0</isvideo>\n\t\t\t<duration>-1</duration>\n\t\t\t<isexposed>0</isexposed>\n\t\t\t<readtext />\n\t\t</gameshare>\n\t\t<mpsharetrace>\n\t\t\t<hasfinderelement>0</hasfinderelement>\n\t\t\t<lastgmsgid />\n\t\t</mpsharetrace>\n\t\t<wxgamecard>\n\t\t\t<framesetname />\n\t\t\t<mbcarddata />\n\t\t\t<minpkgversion />\n\t\t\t<mbcardheight>0</mbcardheight>\n\t\t\t<isoldversion>0</isoldversion>\n\t\t</wxgamecard>\n\t\t<liteapp>\n\t\t\t<id>null</id>\n\t\t\t<path />\n\t\t\t<query />\n\t\t</liteapp>\n\t</appmsg>\n\t<fromusername>xiaomaochong</fromusername>\n\t<scene>0</scene>\n\t<appinfo>\n\t\t<version>29</version>\n\t\t<appname>摇一摇搜歌</appname>\n\t</appinfo>\n\t<commenturl />\n</msg>\n'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1753851891, 'MsgSource': '<msgsource>\n\t<sec_msg_node>\n\t\t<uuid>c48b4f1e38a5029d823be97818bc7620_</uuid>\n\t\t<risk-file-flag />\n\t\t<risk-file-md5-list />\n\t</sec_msg_node>\n\t<silence>0</silence>\n\t<membercount>493</membercount>\n\t<signature>N0_V1_zAHC3TNH|v1_Rft3QTYl</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'PushContent': '你收到了一条消息', 'NewMsgId': 2930251926868316383, 'MsgSeq': 871411648}
2025-07-30 13:04:40 | DEBUG | 从群聊消息中提取发送者: xiaomaochong
2025-07-30 13:04:40 | DEBUG | XML消息完整内容:
<?xml version="1.0"?>
<msg>
	<appmsg appid="wx485a97c844086dc9" sdkver="0">
		<title>跑路了</title>
		<des>KichikuRasen</des>
		<username />
		<action>view</action>
		<type>3</type>
		<showtype>0</showtype>
		<content />
		<url />
		<lowurl />
		<forwardflag>0</forwardflag>
		<dataurl>https://v3-luna.douyinvod.com/29b1c96a304c5023420696f6695b3a01/688b0850/video/tos/cn/tos-cn-ve-2774/o0A8Bk8I9ilGEWMQI0iB49fDMTsTQnu5AhBrCs/</dataurl>
		<lowdataurl />
		<contentattr>0</contentattr>
		<streamvideo>
			<streamvideourl />
			<streamvideototaltime>0</streamvideototaltime>
			<streamvideotitle />
			<streamvideowording />
			<streamvideoweburl />
			<streamvideothumburl />
			<streamvideoaduxinfo />
			<streamvideopublishid />
		</streamvideo>
		<canvasPageItem>
			<canvasPageXml><![CDATA[]]></canvasPageXml>
		</canvasPageItem>
		<appattach>
			<totallen>0</totallen>
			<attachid />
			<cdnattachurl />
			<emoticonmd5></emoticonmd5>
			<aeskey></aeskey>
			<fileext />
			<islargefilemsg>0</islargefilemsg>
		</appattach>
		<extinfo />
		<androidsource>2</androidsource>
		<thumburl />
		<mediatagname />
		<messageaction><![CDATA[]]></messageaction>
		<messageext><![CDATA[]]></messageext>
		<emoticongift>
			<packageflag>0</packageflag>
			<packageid />
		</emoticongift>
		<emoticonshared>
			<packageflag>0</packageflag>
			<packageid />
		</emoticonshared>
		<designershared>
			<designeruin>0</designeruin>
			<designername>null</designername>
			<designerrediretcturl><![CDATA[null]]></designerrediretcturl>
		</designershared>
		<emotionpageshared>
			<tid>0</tid>
			<title>null</title>
			<desc>null</desc>
			<iconUrl><![CDATA[null]]></iconUrl>
			<secondUrl>null</secondUrl>
			<pageType>0</pageType>
			<setKey>null</setKey>
		</emotionpageshared>
		<webviewshared>
			<shareUrlOriginal />
			<shareUrlOpen />
			<jsAppId />
			<publisherId />
			<publisherReqId />
		</webviewshared>
		<template_id />
		<md5 />
		<websearch>
			<rec_category>0</rec_category>
			<channelId>0</channelId>
		</websearch>
		<weappinfo>
			<username />
			<appid />
			<appservicetype>0</appservicetype>
			<secflagforsinglepagemode>0</secflagforsinglepagemode>
			<videopageinfo>
				<thumbwidth>0</thumbwidth>
				<thumbheight>0</thumbheight>
				<fromopensdk>0</fromopensdk>
			</videopageinfo>
		</weappinfo>
		<statextstr>GhQKEnd4NDg1YTk3Yzg0NDA4NmRjOQ==</statextstr>
		<songalbumurl>https://wx.qlogo.cn/mmhead/ver_1/ZriaH48s42WfSndwztxrnzk2dIW713hMcwGhrjDsNcfeQvChLDXFVWhsCdQEnLG1dpEtdchUatBpyibHlk3LtjTZO2VmTpK6ibQcTKuwT2e1aY/0</songalbumurl>
		<songlyric>[00:19.39]无人可知窗寒梦时
[00:22.63]再忆起别离事
[00:25.51]不尽心事
[00:29.15]两行旧词几多相似
[00:32.43]如同今宵昨日念之
[00:38.91]清风上南枝
[00:42.23]梦中仍相思
[00:45.07]等秋高看山势
[00:47.71]再叹故知
[00:50.79]三两笔着墨迟迟不为记事
[00:55.23]随手便成诗
[00:57.67]满腹心思此时寻你于句字
[01:03.79]灯影下呢喃你名字
[01:07.47]或许是我太偏执
[01:11.51]万花开遍不及你归时
[01:44.15]无人可知窗寒梦时
[01:47.43]再忆起别离事
[01:50.31]不尽心事
[01:53.95]两行旧词几多相似
[01:57.23]如同今宵昨日念之
[02:03.71]清风上南枝
[02:06.99]梦中仍相思
[02:09.87]等秋高看山势
[02:12.47]再叹故知
[02:15.59]三两笔着墨迟迟不为记事
[02:20.03]随手便成诗
[02:22.47]满腹心思此时寻你于句字
[02:28.59]灯影下呢喃你名字
[02:32.27]或许是我太偏执
[02:36.31]万花开遍不及你归时
[02:41.63]三两笔着墨迟迟不为记事
[02:46.07]随手便成诗
[02:48.55]满腹心思此时寻你于句字
[02:54.67]灯影下呢喃你名字
[02:58.39]或许是我太偏执
[03:02.43]万花开遍不及你归时
</songlyric>
		<musicShareItem>
			<musicDuration>0</musicDuration>
		</musicShareItem>
		<finderLiveProductShare>
			<finderLiveID><![CDATA[]]></finderLiveID>
			<finderUsername><![CDATA[]]></finderUsername>
			<finderObjectID><![CDATA[]]></finderObjectID>
			<finderNonceID><![CDATA[]]></finderNonceID>
			<liveStatus><![CDATA[]]></liveStatus>
			<appId><![CDATA[]]></appId>
			<pagePath><![CDATA[]]></pagePath>
			<productId><![CDATA[]]></productId>
			<coverUrl><![CDATA[]]></coverUrl>
			<productTitle><![CDATA[]]></productTitle>
			<marketPrice><![CDATA[0]]></marketPrice>
			<sellingPrice><![CDATA[0]]></sellingPrice>
			<platformHeadImg><![CDATA[]]></platformHeadImg>
			<platformName><![CDATA[]]></platformName>
			<shopWindowId><![CDATA[]]></shopWindowId>
			<flashSalePrice><![CDATA[0]]></flashSalePrice>
			<flashSaleEndTime><![CDATA[0]]></flashSaleEndTime>
			<ecSource><![CDATA[]]></ecSource>
			<sellingPriceWording><![CDATA[]]></sellingPriceWording>
			<platformIconURL><![CDATA[]]></platformIconURL>
			<firstProductTagURL><![CDATA[]]></firstProductTagURL>
			<firstProductTagAspectRatioString><![CDATA[0.0]]></firstProductTagAspectRatioString>
			<secondProductTagURL><![CDATA[]]></secondProductTagURL>
			<secondProductTagAspectRatioString><![CDATA[0.0]]></secondProductTagAspectRatioString>
			<firstGuaranteeWording><![CDATA[]]></firstGuaranteeWording>
			<secondGuaranteeWording><![CDATA[]]></secondGuaranteeWording>
			<thirdGuaranteeWording><![CDATA[]]></thirdGuaranteeWording>
			<isPriceBeginShow>false</isPriceBeginShow>
			<lastGMsgID><![CDATA[]]></lastGMsgID>
			<promoterKey><![CDATA[]]></promoterKey>
			<discountWording><![CDATA[]]></discountWording>
			<priceSuffixDescription><![CDATA[]]></priceSuffixDescription>
			<showBoxItemStringList />
		</finderLiveProductShare>
		<finderOrder>
			<appID><![CDATA[]]></appID>
			<orderID><![CDATA[]]></orderID>
			<path><![CDATA[]]></path>
			<priceWording><![CDATA[]]></priceWording>
			<stateWording><![CDATA[]]></stateWording>
			<productImageURL><![CDATA[]]></productImageURL>
			<products><![CDATA[]]></products>
			<productsCount><![CDATA[0]]></productsCount>
		</finderOrder>
		<finderShopWindowShare>
			<finderUsername><![CDATA[]]></finderUsername>
			<avatar><![CDATA[]]></avatar>
			<nickname><![CDATA[]]></nickname>
			<commodityInStockCount><![CDATA[]]></commodityInStockCount>
			<appId><![CDATA[]]></appId>
			<path><![CDATA[]]></path>
			<appUsername><![CDATA[]]></appUsername>
			<query><![CDATA[]]></query>
			<liteAppId><![CDATA[]]></liteAppId>
			<liteAppPath><![CDATA[]]></liteAppPath>
			<liteAppQuery><![CDATA[]]></liteAppQuery>
			<platformTagURL><![CDATA[]]></platformTagURL>
			<saleWording><![CDATA[]]></saleWording>
			<lastGMsgID><![CDATA[]]></lastGMsgID>
			<profileTypeWording><![CDATA[]]></profileTypeWording>
			<reputationInfo>
				<hasReputationInfo>0</hasReputationInfo>
				<reputationScore>0</reputationScore>
				<reputationWording />
				<reputationTextColor />
				<reputationLevelWording />
				<reputationBackgroundColor />
			</reputationInfo>
			<productImageURLList />
		</finderShopWindowShare>
		<findernamecard>
			<username />
			<avatar><![CDATA[]]></avatar>
			<nickname />
			<auth_job />
			<auth_icon>0</auth_icon>
			<auth_icon_url />
			<ecSource><![CDATA[]]></ecSource>
			<lastGMsgID><![CDATA[]]></lastGMsgID>
		</findernamecard>
		<finderGuarantee>
			<scene><![CDATA[0]]></scene>
		</finderGuarantee>
		<directshare>0</directshare>
		<gamecenter>
			<namecard>
				<iconUrl />
				<name />
				<desc />
				<tail />
				<jumpUrl />
			</namecard>
		</gamecenter>
		<patMsg>
			<chatUser />
			<records>
				<recordNum>0</recordNum>
			</records>
		</patMsg>
		<secretmsg>
			<issecretmsg>0</issecretmsg>
		</secretmsg>
		<referfromscene>0</referfromscene>
		<gameshare>
			<liteappext>
				<liteappbizdata />
				<priority>0</priority>
			</liteappext>
			<appbrandext>
				<litegameinfo />
				<priority>-1</priority>
			</appbrandext>
			<gameshareid />
			<sharedata />
			<isvideo>0</isvideo>
			<duration>-1</duration>
			<isexposed>0</isexposed>
			<readtext />
		</gameshare>
		<mpsharetrace>
			<hasfinderelement>0</hasfinderelement>
			<lastgmsgid />
		</mpsharetrace>
		<wxgamecard>
			<framesetname />
			<mbcarddata />
			<minpkgversion />
			<mbcardheight>0</mbcardheight>
			<isoldversion>0</isoldversion>
		</wxgamecard>
		<liteapp>
			<id>null</id>
			<path />
			<query />
		</liteapp>
	</appmsg>
	<fromusername>xiaomaochong</fromusername>
	<scene>0</scene>
	<appinfo>
		<version>29</version>
		<appname>摇一摇搜歌</appname>
	</appinfo>
	<commenturl />
</msg>

2025-07-30 13:04:40 | DEBUG | XML消息类型: 3
2025-07-30 13:04:40 | DEBUG | XML消息标题: 跑路了
2025-07-30 13:04:40 | DEBUG | XML消息描述: KichikuRasen
2025-07-30 13:04:40 | DEBUG | 附件信息 totallen: 0
2025-07-30 13:04:40 | DEBUG | 附件信息 islargefilemsg: 0
2025-07-30 13:04:40 | INFO | 收到红包消息: 标题:跑路了 描述:KichikuRasen 来自:47442567074@chatroom 发送人:wxid_4usgcju5ey9q29
2025-07-30 13:11:03 | DEBUG | 收到消息: {'MsgId': 1623408233, 'FromUserName': {'string': '***********@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 47, 'Content': {'string': 'wxid_jegyk4i3v7zg22:\n<msg><emoji fromusername="wxid_jegyk4i3v7zg22" tousername="***********@chatroom" type="1" idbuffer="media:0_0" md5="0e02b61e0c21568f9c7a92d095fba591" len="10580" productid="" androidmd5="0e02b61e0c21568f9c7a92d095fba591" androidlen="10580" s60v3md5="0e02b61e0c21568f9c7a92d095fba591" s60v3len="10580" s60v5md5="0e02b61e0c21568f9c7a92d095fba591" s60v5len="10580" cdnurl="http://vweixinf.tc.qq.com/110/20401/stodownload?m=0e02b61e0c21568f9c7a92d095fba591&amp;filekey=3043020101042f302d02016e040253480420306530326236316530633231353638663963376139326430393566626135393102022954040d00000004627466730000000132&amp;hy=SH&amp;storeid=26889738e00014b4df33b8a710000006e01004fb153482ec7d0d156a42feae&amp;ef=1&amp;bizid=1022" designerid="" thumburl="" encrypturl="http://vweixinf.tc.qq.com/110/20402/stodownload?m=f60fa2a0894ee2693c372a70b5240bf3&amp;filekey=3043020101042f302d02016e040253480420663630666132613038393465653236393363333732613730623532343062663302022960040d00000004627466730000000132&amp;hy=SH&amp;storeid=26889738e00022525f33b8a710000006e02004fb253482ec7d0d156a42febb&amp;ef=2&amp;bizid=1022" aeskey="b258a930ec4e48e89dbbfcaacbcea699" externurl="http://vweixinf.tc.qq.com/110/20403/stodownload?m=0787b475bdf4de2fd6be805a3715599d&amp;filekey=3043020101042f302d02016e040253480420303738376234373562646634646532666436626538303561333731353539396402021860040d00000004627466730000000132&amp;hy=SH&amp;storeid=26889738e0002d670f33b8a710000006e03004fb353482ec7d0d156a42fec6&amp;ef=3&amp;bizid=1022" externmd5="8c1c04c29e42a11f19513b2a767dfa16" width="470" height="180" tpurl="" tpauthkey="" attachedtext="" attachedtextcolor="" lensid="" emojiattr="" linkid="" desc=""></emoji><gameext type="0" content="0"></gameext><extcommoninfo></extcommoninfo></msg>'}, 'Status': 3, 'ImgStatus': 2, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1753852275, 'MsgSource': '<msgsource>\n\t<silence>0</silence>\n\t<membercount>73</membercount>\n\t<signature>N0_V1_TTujFzkC|v1_Bnou/uAF</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'PushContent': '阿尼亚与她在群聊中发了一个表情', 'NewMsgId': 4236427867230517911, 'MsgSeq': 871411649}
2025-07-30 13:11:03 | INFO | 收到表情消息: 消息ID:1623408233 来自:***********@chatroom 发送人:wxid_jegyk4i3v7zg22 MD5:0e02b61e0c21568f9c7a92d095fba591 大小:10580
2025-07-30 13:11:03 | DEBUG | [DoubaoImageRecognition] 缓存表情消息: 4236427867230517911
2025-07-30 13:11:50 | DEBUG | 收到消息: {'MsgId': 1814987472, 'FromUserName': {'string': '27852221909@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 1, 'Content': {'string': 'wxid_2530z9t0joek22:\n@Ko9\u2005打一把排位'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1753852322, 'MsgSource': '<msgsource>\n\t<atuserlist><![CDATA[wxid_t9fgbkqco9ny22]]></atuserlist>\n\t<pua>1</pua>\n\t<eggIncluded>1</eggIncluded>\n\t<silence>1</silence>\n\t<membercount>145</membercount>\n\t<signature>N0_V1_upkRpoAM|v1_qpkevGf2</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'NewMsgId': 2521457459097979316, 'MsgSeq': 871411650}
2025-07-30 13:11:50 | INFO | 收到文本消息: 消息ID:1814987472 来自:27852221909@chatroom 发送人:wxid_2530z9t0joek22 @:['wxid_t9fgbkqco9ny22'] 内容:@Ko9 打一把排位
2025-07-30 13:11:50 | DEBUG | 处理消息内容: '@Ko9 打一把排位'
2025-07-30 13:11:50 | DEBUG | 消息内容 '@Ko9 打一把排位' 不匹配任何命令，忽略
