import os
import traceback
import xml.etree.ElementTree as ET
from functools import wraps
from typing import Callable, Union, Dict, Any

from loguru import logger
from apscheduler.schedulers.asyncio import AsyncIOScheduler
from apscheduler.triggers.cron import CronTrigger
from apscheduler.triggers.interval import IntervalTrigger

from utils.event_manager import EventManager

scheduler = AsyncIOScheduler()


def schedule(
        trigger: Union[str, CronTrigger, IntervalTrigger],
        **trigger_args
) -> Callable:
    """
    定时任务装饰器

    例子:

    - @schedule('interval', seconds=30)
    - @schedule('cron', hour=8, minute=30, second=30)
    - @schedule('date', run_date='2024-01-01 00:00:00')
    """

    def decorator(func: Callable):
        job_id = f"{func.__module__}.{func.__qualname__}"

        @wraps(func)
        async def wrapper(self, *args, **kwargs):
            return await func(self, *args, **kwargs)

        setattr(wrapper, '_is_scheduled', True)
        setattr(wrapper, '_schedule_trigger', trigger)
        setattr(wrapper, '_schedule_args', trigger_args)
        setattr(wrapper, '_job_id', job_id)

        return wrapper

    return decorator


def add_job_safe(scheduler: AsyncIOScheduler, job_id: str, func: Callable, bot,
                 trigger: Union[str, CronTrigger, IntervalTrigger], **trigger_args):
    """添加函数到定时任务中，如果存在则先删除现有的任务"""
    try:
        scheduler.remove_job(job_id)
    except:
        pass
    # Pass bot as first argument to the scheduled function
    scheduler.add_job(func, trigger, args=[bot], id=job_id, **trigger_args)


def remove_job_safe(scheduler: AsyncIOScheduler, job_id: str):
    """从定时任务中移除任务"""
    try:
        scheduler.remove_job(job_id)
    except:
        pass


def on_text_message(func: Callable):
    @wraps(func)
    def wrapper(self, *args, **kwargs):
        return func(self, *args, **kwargs)

    setattr(wrapper, '_event_type', 'text_message')
    return wrapper


def on_image_message(func: Callable):
    @wraps(func)
    def wrapper(self, *args, **kwargs):
        return func(self, *args, **kwargs)

    setattr(wrapper, '_event_type', 'image_message')
    return wrapper


def on_voice_message(func: Callable):
    @wraps(func)
    def wrapper(self, *args, **kwargs):
        return func(self, *args, **kwargs)

    setattr(wrapper, '_event_type', 'voice_message')
    return wrapper


def on_emoji_message(func: Callable):
    @wraps(func)
    def wrapper(self, *args, **kwargs):
        return func(self, *args, **kwargs)

    setattr(wrapper, '_event_type', 'emoji_message')
    return wrapper


def on_file_message(func: Callable):
    @wraps(func)
    def wrapper(self, *args, **kwargs):
        return func(self, *args, **kwargs)

    setattr(wrapper, '_event_type', 'file_message')
    return wrapper


def on_quote_message(func: Callable):
    @wraps(func)
    def wrapper(self, *args, **kwargs):
        return func(self, *args, **kwargs)

    setattr(wrapper, '_event_type', 'quote_message')
    return wrapper


def on_video_message(func: Callable):
    @wraps(func)
    def wrapper(self, *args, **kwargs):
        return func(self, *args, **kwargs)

    setattr(wrapper, '_event_type', 'video_message')
    return wrapper


def on_pat_message(func: Callable):
    @wraps(func)
    def wrapper(self, *args, **kwargs):
        return func(self, *args, **kwargs)

    setattr(wrapper, '_event_type', 'pat_message')
    return wrapper


def on_at_message(func: Callable):
    @wraps(func)
    def wrapper(self, *args, **kwargs):
        return func(self, *args, **kwargs)

    setattr(wrapper, '_event_type', 'at_message')
    return wrapper


def on_system_message(func: Callable):
    @wraps(func)
    def wrapper(self, *args, **kwargs):
        return func(self, *args, **kwargs)

    setattr(wrapper, '_event_type', 'system_message')
    return wrapper


def on_article_message(func: Callable):
    @wraps(func)
    def wrapper(self, *args, **kwargs):
        return func(self, *args, **kwargs)

    setattr(wrapper, '_event_type', 'article_message')
    return wrapper


# 新增加的文章消息装饰器，可以指定文章来源类型
def on_article_message_filter(source_type: str = None):
    """
    增强版公众号文章装饰器，可以按来源类型过滤

    Args:
        source_type: 可选值: 'direct'(直接来自公众号), 'group'(来自群聊), None(所有来源)
    """
    def decorator(func: Callable):
        @wraps(func)
        async def wrapper(self, bot, message, *args, **kwargs):
            # 检查消息来源类型
            if source_type == 'direct' and not message.get('IsDirectFromMP', False):
                return
            if source_type == 'group' and not message.get('IsGroup', False):
                return
            return await func(self, bot, message, *args, **kwargs)

        setattr(wrapper, '_event_type', 'article_message')
        return wrapper
    return decorator


# 新增加的引用消息装饰器，可以指定引用消息类型和来源
def on_quote_message_filter(quote_type: int = None, source_type: str = None):
    """
    增强版引用消息装饰器，可以按引用消息类型和来源类型过滤

    Args:
        quote_type: 引用消息的类型 (如 1=文本, 3=图片, 等)，None表示所有类型
        source_type: 可选值: 'group'(来自群聊), 'private'(来自私聊), None(所有来源)
    """
    def decorator(func: Callable):
        @wraps(func)
        async def wrapper(self, bot, message, *args, **kwargs):
            # 检查消息来源类型
            if source_type == 'group' and not message.get('IsGroup', False):
                return
            if source_type == 'private' and message.get('IsGroup', False):
                return

            # 检查引用消息类型
            quote = message.get('Quote', {})
            if quote_type is not None and quote.get('MsgType') != quote_type:
                return

            return await func(self, bot, message, *args, **kwargs)

        setattr(wrapper, '_event_type', 'quote_message')
        return wrapper
    return decorator


def on_xml_message(func: Callable):
    """
    XML消息装饰器
    用于处理各类XML格式的消息
    """
    @wraps(func)
    def wrapper(self, *args, **kwargs):
        return func(self, *args, **kwargs)

    setattr(wrapper, '_event_type', 'xml_message')
    return wrapper


def on_xml_message_filter(xml_type: int = None, source_type: str = None):
    """
    增强版XML消息装饰器，可以按XML类型和来源类型过滤

    Args:
        xml_type: XML消息的类型值，None表示处理所有类型的XML消息
        source_type: 可选值: 'group'(来自群聊), 'private'(来自私聊), None(所有来源)
    """
    def decorator(func: Callable):
        @wraps(func)
        async def wrapper(self, bot, message, *args, **kwargs):
            # 检查消息来源类型
            if source_type == 'group' and not message.get('IsGroup', False):
                return
            if source_type == 'private' and message.get('IsGroup', False):
                return

            # 检查XML消息类型
            msg_xml_type = message.get('XmlType')
            if xml_type is not None and msg_xml_type != xml_type:
                return

            return await func(self, bot, message, *args, **kwargs)

        setattr(wrapper, '_event_type', 'xml_message')
        return wrapper
    return decorator


async def process_xml_message(self, message: Dict[str, Any]):
    """处理xml消息"""
    # 获取消息内容
    content = message.get("Content")
    if isinstance(content, dict):
        content = content.get("string", "")

    # 处理群聊消息中的wxid前缀
    sender_wxid = None
    if message["FromWxid"].endswith("@chatroom"):  # 群聊消息
        message["IsGroup"] = True
        # 查找第一个换行符，wxid前缀通常是这样的格式: wxid_xxx:\n
        split_index = content.find(":\n")
        if split_index > 0:
            sender_wxid = content[:split_index]
            # 保留前缀后的实际内容
            content = content[split_index+2:]  # +2 是为了跳过 ":\n"
            message["SenderWxid"] = sender_wxid
        else:  # 可能是自己发的消息
            message["SenderWxid"] = self.wxid
    else:
        message["SenderWxid"] = message["FromWxid"]
        if message["FromWxid"] == self.wxid:  # 自己发的消息
            message["FromWxid"] = message["ToWxid"]
        message["IsGroup"] = False

    # 更新处理后的内容
    message["Content"] = content

    # 智能查找XML开始标签位置
    xml_start = -1
    for xml_marker in ["<?xml", "<msg", "<xml"]:
        xml_start = content.find(xml_marker)
        if xml_start >= 0:
            if xml_start > 0:
                logger.debug(f"找到XML开始标签，截取内容从位置 {xml_start}")
                content = content[xml_start:]
            break

    if xml_start < 0:  # 仍然找不到有效的XML开始标签
        logger.error(f"无法找到有效的XML开始标签，消息内容前50个字符: {content[:50]}")
        return

    # 再次更新处理后的内容
    message["Content"] = content

    # 将XML内容写入临时文件，然后记录文件路径
    try:
        # 创建临时目录（如果不存在）
        os.makedirs("temp/xml", exist_ok=True)

        # 生成唯一的文件名
        xml_file = f"temp/xml/xml_msg_{message.get('MsgId')}.xml"

        # 写入XML内容到文件
        with open(xml_file, "w", encoding="utf-8") as f:
            f.write(content)

        # 记录文件路径
        logger.debug(f"XML消息完整内容已保存到文件: {xml_file}")
    except Exception as e:
        logger.error(f"保存XML内容到文件失败: {e}")
        # 如果保存失败，直接记录XML内容（但可能会被截断）
        logger.debug("XML消息完整内容:\n{}", content)

    # 解析XML并提取类型
    message_type = None
    parsed_xml = None
    appmsg = None

    try:
        parsed_xml = ET.fromstring(content)
        message["ParsedXML"] = parsed_xml  # 保存解析结果供后续使用

        # 提取所有可能的重要信息
        appmsg = parsed_xml.find("appmsg")
        if appmsg is not None:
            # 提取基本信息
            type_node = appmsg.find("type")
            title_node = appmsg.find("title")
            des_node = appmsg.find("des")

            if type_node is not None:
                message_type = int(type_node.text)
                message["XmlType"] = message_type
                logger.debug(f"XML消息类型: {message_type}")

            if title_node is not None:
                message["Title"] = title_node.text
                logger.debug(f"XML消息标题: {title_node.text}")

            if des_node is not None:
                message["Description"] = des_node.text
                logger.debug(f"XML消息描述: {des_node.text}")

            # 提取更多详细信息
            appattach = appmsg.find("appattach")
            if appattach is not None:
                for child in appattach:
                    if child.text and child.text.strip():
                        message[f"Attach_{child.tag}"] = child.text
                        logger.debug(f"附件信息 {child.tag}: {child.text}")

            # 提取URL信息
            url = appmsg.find("url")
            if url is not None and url.text:
                message["Url"] = url.text
                logger.debug(f"XML消息URL: {url.text}")

            # 提取缩略图信息
            thumburl = appmsg.find("thumburl")
            if thumburl is not None and thumburl.text:
                message["ThumbUrl"] = thumburl.text
                logger.debug(f"XML消息缩略图URL: {thumburl.text}")

        else:
            # 如果没有appmsg节点，尝试其他常见节点
            for key_node in ["title", "des", "url", "type"]:
                node = parsed_xml.find(key_node)
                if node is not None and node.text:
                    message[key_node.capitalize()] = node.text
                    logger.debug(f"XML消息{key_node}: {node.text}")

    except ET.ParseError as e:
        logger.error(f"解析XML失败: {e}")
        logger.debug("XML内容:\n{}", content)
        return
    except Exception as e:
        logger.error(f"处理XML消息异常: {e}")
        logger.error(f"错误详情: {traceback.format_exc()}")
        return

    # 根据消息类型路由到不同处理函数
    if message_type == 57:  # 引用消息
        await self.process_quote_message(message)
    elif message_type == 6:  # 文件消息
        await self.process_file_message(message)
    elif message_type == 74:  # 文件消息，但还在上传，不用管
        pass
    elif message_type == 5:  # 公众号文章
        await self.process_article_message(message)
    elif message_type == 3:  # 红包消息
        # 提取红包信息
        try:
            title = message.get("Title", "")
            des = message.get("Description", "")
            message["RedPacketTitle"] = title
            message["RedPacketDesc"] = des

            # 记录红包消息信息
            logger.info(f"收到红包消息: 标题:{title} 描述:{des} 来自:{message['FromWxid']} 发送人:{message['SenderWxid']}")

            # 触发红包消息事件
            if self.ignore_check(message["FromWxid"], message["SenderWxid"]):
                await EventManager.emit("xml_message", self.bot, message)
        except Exception as e:
            logger.error(f"处理红包消息失败: {e}")
    else:
        # 对于未知类型的消息，记录所有可用信息
        logger.info(f"未知的XML消息类型: {message_type}")
        logger.info(f"消息标题: {message.get('Title', 'N/A')}")
        logger.info(f"消息描述: {message.get('Description', 'N/A')}")
        logger.info(f"消息URL: {message.get('Url', 'N/A')}")
        logger.info("完整XML内容:\n{}", content)

        # 仍然触发事件，让插件有机会处理
        if self.ignore_check(message["FromWxid"], message["SenderWxid"]):
            await EventManager.emit("xml_message", self.bot, message)
