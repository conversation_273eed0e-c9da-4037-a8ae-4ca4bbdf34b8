﻿# XYBot 启动脚本
# 功能：检查端口占用、清理僵尸进程、启动XYBot

param(
    [switch]$Monitor,  # 监控模式：持续监控端口
    [switch]$Clean,    # 清理模式：只清理端口不启动
    [int]$Port = 9000  # 指定端口，默认9000
)

# 检查管理员权限
function Test-Administrator {
    $currentUser = [Security.Principal.WindowsIdentity]::GetCurrent()
    $principal = New-Object Security.Principal.WindowsPrincipal($currentUser)
    return $principal.IsInRole([Security.Principal.WindowsBuiltInRole]::Administrator)
}

# 检查并清理端口占用
function Clear-PortUsage {
    param([int]$PortNumber)

    Write-Host "检查端口 $PortNumber 占用情况..." -ForegroundColor Yellow

    try {
        $connections = Get-NetTCPConnection -LocalPort $PortNumber -ErrorAction Stop

        if ($connections) {
            foreach ($connection in $connections) {
                try {
                    $process = Get-Process -Id $connection.OwningProcess -ErrorAction Stop
                    Write-Host "发现占用端口 $PortNumber 的进程: $($process.ProcessName) (PID: $($process.Id))" -ForegroundColor Red
                    Write-Host "正在结束进程..." -ForegroundColor Yellow

                    Stop-Process -Id $process.Id -Force
                    Write-Host "进程已结束，端口 $PortNumber 已释放" -ForegroundColor Green
                    Start-Sleep -Seconds 2  # 等待进程完全结束
                } catch {
                    Write-Host "无法获取进程信息或结束进程: $($_.Exception.Message)" -ForegroundColor Red
                }
            }
        } else {
            Write-Host "端口 $PortNumber 未被占用" -ForegroundColor Green
        }
        return $true
    } catch {
        Write-Host "端口 $PortNumber 未被占用" -ForegroundColor Green
        return $true
    }
}

# 启动XYBot
function Start-XYBot {
    Write-Host "启动 XYBot..." -ForegroundColor Green

    # 检查虚拟环境
    if (Test-Path ".\venv\Scripts\Activate.ps1") {
        Write-Host "激活虚拟环境..." -ForegroundColor Yellow
        & ".\venv\Scripts\Activate.ps1"
    }

    # 启动主程序
    try {
        python main.py
    } catch {
        Write-Host "启动失败: $($_.Exception.Message)" -ForegroundColor Red
        Write-Host "请确保已安装所有依赖: pip install -r requirements.txt" -ForegroundColor Yellow
    }
}

# 监控模式
function Start-MonitorMode {
    Write-Host "进入监控模式，每30秒检查一次端口占用情况..." -ForegroundColor Cyan
    Write-Host "按 Ctrl+C 退出监控" -ForegroundColor Yellow

    try {
        while ($true) {
            Clear-PortUsage -PortNumber $Port
            Write-Host "等待30秒后重新检查..." -ForegroundColor Gray
            Start-Sleep -Seconds 30
        }
    } catch [System.Management.Automation.PipelineStoppedException] {
        Write-Host "`n监控已停止" -ForegroundColor Yellow
    }
}

# 主逻辑
Write-Host "=== XYBot 启动助手 ===" -ForegroundColor Cyan

# 检查管理员权限
if (-not (Test-Administrator)) {
    Write-Warning "需要管理员权限来管理进程和端口！"
    Write-Host "请右键点击PowerShell，选择'以管理员身份运行'" -ForegroundColor Yellow
    Read-Host "按回车键退出"
    exit 1
}

# 根据参数执行不同操作
if ($Monitor) {
    Start-MonitorMode
} elseif ($Clean) {
    Clear-PortUsage -PortNumber $Port
    Write-Host "清理完成" -ForegroundColor Green
} else {
    # 默认模式：清理端口并启动XYBot
    if (Clear-PortUsage -PortNumber $Port) {
        Write-Host "端口检查完成，准备启动XYBot..." -ForegroundColor Green
        Start-Sleep -Seconds 2
        Start-XYBot
    } else {
        Write-Host "端口清理失败，无法启动XYBot" -ForegroundColor Red
        Read-Host "按回车键退出"
    }
}