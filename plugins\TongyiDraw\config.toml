[TongyiDraw]
enable = true
command = ["通义"]
command-format = "通义 <提示词> - 使用通义千问生成图片"
natural_response = true  # 启用自然化响应

[TongyiDraw.rate_limit]
cooldown = 10  # 冷却时间（秒）

[TongyiDraw.api]
# 通义千问API配置
base_url = "https://api.tongyi.com"
endpoint = "/dialog/conversation"
timeout = 60  # 请求超时时间（秒）

[TongyiDraw.headers]
# 请求头配置
accept = "text/event-stream"
x_platform = "h5"
user_agent = "Mozilla/5.0 (Linux; Android 10; V2002A Build/QP1A.190711.020) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/83.0.4103.106 Mobile Safari/537.36"
content_type = "application/json"
origin = "https://www.tongyi.com"
x_requested_with = "mark.via"
sec_fetch_site = "same-site"
sec_fetch_mode = "cors"
sec_fetch_dest = "empty"
referer = "https://www.tongyi.com/qianwen"
accept_encoding = "gzip, deflate"
accept_language = "zh-CN,zh;q=0.9,en-US;q=0.8,en;q=0.7"
# 从抓包信息中获取的认证信息
cookie = "UM_distinctid=19701e4760552-061b8d564ffdf5-26031f51-448e0-19701e47606fd; _samesite_flag_=true; cna=y/CxIJkuvGIBASQJjWCwD531; xlly_s=1; XSRF-TOKEN=009c50f2-4a73-4860-a4cc-18376d4f9029; tongyi_sso_ticket=L_6XJMKTIUvHeUf*trtMUHEnF6AFvL_vsXhJ0hTZbKZWDyJOTQSigErbTQrrQudl0; isg=BPPzg2xPn8On8lP_NzFJybLtifUdKIfq2n3xLaWQS5JLpBtGLPkwOHQyW5LvBN_i; tfstk=chsCB7wPP6gWj5kaG3wZfA_Bp6ONaznWIYOVdJ7_eR7uZ0fB6smm0IMIED0XJhv1."
x_xsrf_token = "c13fc967-b674-4eae-b65a-f4ccef7663ec"
