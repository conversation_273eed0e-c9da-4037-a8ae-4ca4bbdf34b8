import os, json, tomllib, re, time, random, uuid, httpx, asyncio, aiofiles
from loguru import logger
from urllib.parse import quote
from PIL import Image
from pathlib import Path
from typing import Optional, Dict, Any, List, Union

async def get_httpx_client():
    return httpx.AsyncClient(timeout=httpx.Timeout(connect=10.0, read=20.0, write=20.0, pool=5.0), verify=False, follow_redirects=True, limits=httpx.Limits(max_keepalive_connections=20, max_connections=10))

class HttpResponse:
    def __init__(self, status_code, body, headers=None):
        self.code = self.status_code = status_code
        self.body = body
        self.headers = headers or {}
        self._text = None
    
    def json(self): 
        return {} if not self.body else json.loads(self.body if isinstance(self.body, str) else self.body.decode('utf-8'))
    
    @property
    def text(self):
        if self._text is None: 
            self._text = self.body if isinstance(self.body, str) else self.body.decode('utf-8', errors='ignore')
        return self._text
    
    async def aread(self): 
        return self.body

try:
    from WechatAPI import WechatAPIClient
    from utils.decorators import on_text_message
    from utils.plugin_base import PluginBase
except ImportError:
    # 测试环境下的模拟类
    class WechatAPIClient:
        pass

    def on_text_message(func):
        return func

    class PluginBase:
        def __init__(self):
            pass

class DoubaoDrawing(PluginBase):
    description = "豆包AI画图插件"
    author = "XYBot"
    version = "1.0.0"
    plugin_name = "DoubaoDrawing"

    def __init__(self):
        super().__init__()
        self.temp_dir = Path("temp/doubao_drawing")
        self.temp_dir.mkdir(parents=True, exist_ok=True)
        self.max_age = 3600
        self.cleanup_interval = 1800
        self.last_cleanup = 0

        config = {}
        try:
            config_path = "plugins/DoubaoDrawing/config.toml"
            if os.path.exists(config_path):
                with open(config_path, "rb") as f:
                    config = tomllib.load(f).get("DoubaoDrawing", {})
        except: 
            pass

        self.enable = config.get("enable", True)
        self.command = config.get("command", ["豆画"])
        self.command_format = "🎨豆包AI画图插件使用说明：\n• 豆画<描述> - 高清原图（默认）\n• 豆画<描述> 缩略图 - 普通画图\n\n示例：豆画一只可爱的小猫"
        self.use_high_resolution = config.get("use_high_resolution", False)

        self.api_config = config.get("API", {})
        self.api_base_url = "https://www.doubao.com/samantha/chat/completion"
        self.cookies = self.api_config.get("cookies", "")
        self.device_id = "7468716989062841895"
        self.tea_uuid = "7468716986638386703"
        self.web_id = "7468716986638386703"

        self._client = None
        self._client_lock = asyncio.Lock()

        self.rate_limit = config.get("rate_limit", {"tokens_per_second": 0.5, "bucket_size": 5})
        self._token_bucket = self.rate_limit["bucket_size"]
        self._last_token_time = time.time()
        self._token_lock = asyncio.Lock()

        self._user_limits = {}
        self.min_request_interval = 2.0
        self._active_files = set()
        self._active_files_lock = asyncio.Lock()

        self.natural_response = config.get("natural_response", True)

        self._init_natural_responses()

    def _init_natural_responses(self):
        self.confirm_msgs = ["好的", "收到", "明白了", "嗯", "知道了", "行", "OK"]
        self.error_msgs = ["这可把我难住了", "搞不定了", "这个有点难", "弄不了", "出问题了", "搞不出来", "这个不行"]
        self.rate_limit_msgs = ["你搁这刷bug呢", "慢点慢点", "别这么急", "一个一个来", "太快了太快了", "歇会儿", "别刷了"]

    async def _simple_confirm(self, bot, wxid):
        if self.natural_response: 
            await bot.send_text_message(wxid, random.choice(self.confirm_msgs))



    def _select_image_url(self, image_data: dict, mode: str) -> str:
        if mode == "original":
            for key in ["image_raw", "image_ori", "image_thumb_ori"]:
                if key in image_data: return image_data[key]["url"]
        else:
            for key in ["image_thumb_ori", "image_thumb"]:
                if key in image_data: return image_data[key]["url"]
        for key in ["image_raw", "image_ori", "image_thumb_ori", "image_thumb"]:
            if key in image_data and "url" in image_data[key]: return image_data[key]["url"]
        return ""

    def _determine_image_mode(self, query: str) -> str:
        """根据查询内容确定图片模式"""
        if "原图" in query or "高清" in query or "高分辨率" in query:
            return "original"
        return "thumbnail"

    async def get_session(self):
        async with self._client_lock:
            if self._client is None:
                self._client = await get_httpx_client()
            return self._client

    def _generate_headers(self) -> dict:
        flow_trace = f"04-{uuid.uuid4().hex[:16]}-{uuid.uuid4().hex[:16]}-01"
        return {
            "Accept": "*/*", 
            "Accept-Encoding": "gzip, deflate", 
            "Accept-Language": "zh-CN,zh;q=0.9", 
            "Connection": "keep-alive", 
            "Content-Type": "application/json", 
            "Cookie": self.cookies, 
            "Host": "www.doubao.com", 
            "Origin": "https://www.doubao.com", 
            "Referer": "https://www.doubao.com/chat/", 
            "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36",
            "x-flow-trace": flow_trace, 
            "Agw-Js-Conv": "str", 
            "X-Requested-With": "mark.via", 
            "last-event-id": "undefined", 
            "Sec-Fetch-Site": "same-origin", 
            "Sec-Fetch-Mode": "cors", 
            "Sec-Fetch-Dest": "empty"
        }

    def is_drawing_request(self, query):
        """简化的画图请求识别逻辑 - 只响应"豆画+提示词"格式"""
        # 检查是否是"豆画"开头
        if query.startswith("豆画"):
            # 确保后面有实际内容（不只是空格）
            content = query[2:].strip()  # 移除"豆画"前缀
            if content:  # 有实际提示词内容
                return True

        return False

    async def call_doubao_api_for_image(self, bot: WechatAPIClient, message: dict, query: str) -> Optional[Dict[str, Any]]:
        """调用豆包API生成图片"""
        _ = bot, message
        max_retries, base_delay, max_delay = 3, 3, 10
        last_error, conversation_id = None, None

        prompt = query
        if "风格" not in query and "style" not in query.lower():
            prompt = f"图片风格为「写实风格」，{query}"
        if "比例" not in prompt and "ratio" not in prompt.lower():
            prompt = f"{prompt}，比例「1:1」"

        for retry in range(max_retries):
            try:
                params = {
                    "aid": "497858",
                    "device_id": self.device_id,
                    "device_platform": "web",
                    "language": "zh",
                    "pc_version": "2.16.1",
                    "pkg_type": "release_version",
                    "real_aid": "497858",
                    "region": "CN",
                    "samantha_web": "1",
                    "sys_region": "CN",
                    "tea_uuid": self.tea_uuid,
                    "use-olympus-account": "1",
                    "version_code": "20800",
                    "web_id": self.web_id
                }

                conversation_id = f"38{int(time.time() * 10000)}8"
                section_id = f"{conversation_id[:10]}4{conversation_id[10:]}"
                message_id = f"ba0d6520-{uuid.uuid4().hex[:4]}-11f0-a188-0d4d{uuid.uuid4().hex[:8]}"
                local_conversation_id = f"local_{uuid.uuid4().hex[:16]}"

                request_data = {
                    "messages": [{
                        "content": json.dumps({"text": prompt}),
                        "content_type": 2009,
                        "attachments": []
                    }],
                    "completion_option": {
                        "is_regen": False,
                        "with_suggest": False,
                        "need_create_conversation": True,
                        "launch_stage": 1,
                        "is_replace": False,
                        "is_delete": False,
                        "message_from": 0,
                        "use_auto_cot": False,
                        "event_id": "0"
                    },
                    "section_id": section_id,
                    "conversation_id": conversation_id,
                    "local_conversation_id": local_conversation_id,
                    "local_message_id": message_id
                }

                url = f"{self.api_base_url}?" + "&".join([f"{k}={quote(str(v))}" for k,v in params.items()])
                client = await self.get_session()
                headers = self._generate_headers()
                headers['Content-Type'] = 'application/json; charset=utf-8'

                try:
                    response = await client.post(url, json=request_data, headers=headers, timeout=httpx.Timeout(connect=10.0, read=300.0, write=60.0, pool=10.0))
                    if response.status_code != 200:
                        raise ValueError(f"API请求失败: {response.status_code}")

                    http_response = HttpResponse(response.status_code, response.content, dict(response.headers))
                    result = await self._process_stream(http_response)

                    if result:
                        if conversation_id:
                            asyncio.create_task(self._delete_conversation(conversation_id))
                        return result

                    if retry < max_retries - 1:
                        await asyncio.sleep(min(base_delay * (2 ** retry) + random.uniform(0, 2), max_delay))
                        continue

                    raise ValueError("未获取到有效图片生成响应")
                except httpx.RequestError:
                    raise ValueError("HTTP请求失败")

            except ValueError as e:
                last_error = e
                if retry < max_retries - 1:
                    await asyncio.sleep(min(base_delay * (2 ** retry) + random.uniform(0, 2), max_delay))
                    continue
                else:
                    if conversation_id:
                        asyncio.create_task(self._delete_conversation(conversation_id))
                    raise
            except Exception as e:
                last_error = e
                if retry < max_retries - 1:
                    await asyncio.sleep(min(base_delay * (2 ** retry) + random.uniform(0, 2), max_delay))
                    continue
                else:
                    if conversation_id:
                        asyncio.create_task(self._delete_conversation(conversation_id))
                    raise

        return {"type": "text", "text": f"图片生成失败，请稍后重试。错误信息: {last_error}"}

    async def _delete_conversation(self, conversation_id: str) -> bool:
        """删除对话"""
        try:
            params = {
                "version_code": "20800",
                "language": "zh",
                "device_platform": "web",
                "aid": "497858",
                "real_aid": "497858",
                "pc_version": "2.16.1",
                "pkg_type": "release_version",
                "device_id": self.device_id,
                "web_id": self.web_id,
                "tea_uuid": self.tea_uuid,
                "use-olympus-account": "1",
                "region": "CN",
                "sys_region": "CN",
                "samantha_web": "1"
            }
            url = f"https://www.doubao.com/samantha/thread/delete?" + "&".join([f"{k}={quote(str(v))}" for k,v in params.items()])
            headers = {
                "Accept": "application/json, text/plain, */*",
                "Content-Type": "application/json",
                "Origin": "https://www.doubao.com",
                "Referer": "https://www.doubao.com/chat/",
                "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36",
                "X-Requested-With": "mark.via",
                "Connection": "keep-alive",
                "Cookie": self.cookies
            }
            data = {"conversation_id": conversation_id}
            client = await self.get_session()

            try:
                response = await client.post(url, json=data, headers=headers, timeout=httpx.Timeout(connect=5.0, read=10.0, write=10.0, pool=5.0))
                if response.status_code == 200:
                    text = response.text
                    if not text or text.isspace(): return True
                    try:
                        resp_data = response.json()
                        return resp_data.get("code") == 0
                    except: return True
                return False
            except: return False
        except: return False

    async def _process_stream(self, response: HttpResponse) -> Optional[Dict[str, Any]]:
        """处理流式响应"""
        result_text, result_data, images_data = "", {"type": "text", "text": ""}, []
        is_image_request = False

        try:
            timeout, start_time, is_completed, last_update_time, max_idle_time = 300, time.time(), False, time.time(), 10
            buffer = ""
            chunk = response.body.encode('utf-8') if isinstance(response.body, str) else response.body

            try:
                try:
                    buffer = chunk.decode('utf-8', errors='ignore')
                except: return None

                while "\n\n" in buffer:
                    parts = buffer.split("\n\n", 1)
                    event, buffer = parts[0], parts[1]
                    if not event.strip(): continue

                    data_line = None
                    for line in event.split("\n"):
                        if line.startswith("data:"):
                            data_line = line[5:].strip()
                            break
                    if not data_line: continue

                    try:
                        event_data = json.loads(data_line)
                        if not isinstance(event_data, dict) or "event_type" not in event_data: continue
                        event_type = event_data["event_type"]

                        if event_type == 2003:
                            is_completed = True
                            if "tts_content" in event_data:
                                full_text = event_data["tts_content"]
                                if full_text and len(full_text) > len(result_text):
                                    result_text = full_text
                                    result_data["text"] = result_text
                            if images_data: return {"type": "image", "images": images_data}
                            result_data["text"] = result_text
                            return result_data

                        # 处理图片生成事件
                        if event_type == 2001 and "event_data" in event_data:
                            try:
                                inner_data = json.loads(event_data["event_data"])
                                if "message" not in inner_data: continue

                                message = inner_data["message"]
                                is_finish = inner_data.get("is_finish", False)

                                if "content_type" not in message or "content" not in message: continue

                                content_type = message["content_type"]
                                try:
                                    content = json.loads(message["content"])
                                except json.JSONDecodeError: continue

                                # 处理图片内容
                                if content_type == 2010 or content_type == 2074:
                                    if "data" in content:
                                        images_data = []
                                        for img_item in content["data"]:
                                            images_data.append(img_item)

                                        if images_data:
                                            result_data = {"type": "image", "images": images_data}
                                            if is_finish: return result_data

                                    elif "creations" in content:
                                        is_image_request = True
                                        for creation in content["creations"]:
                                            if creation.get("type") == 1 and "image" in creation:
                                                img_info = creation["image"]
                                                if img_info.get("status") == 2:
                                                    images_data.append(img_info)

                                        if images_data:
                                            result_data = {"type": "image", "images": images_data}
                                            if is_finish or inner_data.get("reset", False):
                                                return result_data

                                # 更新最后活动时间
                                last_update_time = time.time()

                            except Exception as e: continue

                    except json.JSONDecodeError as e: continue
                    except Exception as e: continue

                    # 检查超时
                    if time.time() - start_time > timeout:
                        if images_data: return {"type": "image", "images": images_data}
                        elif result_text: return {"type": "text", "text": result_text}
                        break

                    # 检查空闲时间
                    if time.time() - last_update_time > max_idle_time:
                        if images_data: return {"type": "image", "images": images_data}
                        elif result_text: return {"type": "text", "text": result_text}

            except Exception as e: pass

            # 返回已收集的数据
            if images_data: return {"type": "image", "images": images_data}
            if result_text: return {"type": "text", "text": result_text}
            if is_image_request and not images_data: return {"type": "text", "text": "图片生成失败，请稍后重试"}
            if is_completed: return {"type": "text", "text": ""}
            return {"type": "text", "text": ""}

        except Exception as e:
            if images_data: return {"type": "image", "images": images_data}
            elif result_text: return {"type": "text", "text": result_text}
            return None

    async def _save_image(self, image_data: Union[bytes, Image.Image], prefix: str = "temp") -> Optional[str]:
        """保存图片到临时目录"""
        try:
            # 确保临时目录存在
            self.temp_dir.mkdir(parents=True, exist_ok=True)

            # 生成保存路径
            image_path = str(self.temp_dir / f"{prefix}_{int(time.time())}.jpg")

            # 标记文件为活跃状态
            await self._mark_file_active(image_path)

            # 根据数据类型选择保存方式
            if isinstance(image_data, Image.Image):
                # 如果是PIL.Image对象，确保是RGB模式
                if image_data.mode == 'RGBA':
                    # 创建白色背景
                    background = Image.new('RGB', image_data.size, 'white')
                    # 将RGBA图片覆盖到白色背景上
                    background.paste(image_data, mask=image_data.split()[3])
                    image_data = background
                elif image_data.mode != 'RGB':
                    image_data = image_data.convert('RGB')
                # 保存图片
                image_data.save(image_path, format='JPEG', quality=95)
            else:
                # 如果是二进制数据
                async with aiofiles.open(image_path, "wb") as f:
                    await f.write(image_data)

            return image_path

        except Exception as e:
            return None

    async def _mark_file_active(self, filepath: str):
        """标记文件为活跃状态"""
        async with self._active_files_lock:
            self._active_files.add(filepath)

    async def _mark_file_inactive(self, filepath: str):
        """标记文件为非活跃状态"""
        async with self._active_files_lock:
            self._active_files.discard(filepath)

    async def _delayed_delete_files(self, file_paths: List[str], delay_seconds: int):
        """延迟删除文件"""
        try:
            await asyncio.sleep(delay_seconds)
            for path in file_paths:
                try:
                    if path and os.path.exists(path):
                        await self._mark_file_inactive(path)
                        os.remove(path)
                except: pass
        except: pass

    async def _update_tokens(self):
        """更新令牌桶"""
        current_time = time.time()
        elapsed = current_time - self._last_token_time
        new_tokens = elapsed * self.rate_limit["tokens_per_second"]
        self._token_bucket = min(self.rate_limit["bucket_size"], self._token_bucket + new_tokens)
        self._last_token_time = current_time

    async def _acquire_token(self) -> bool:
        """获取令牌"""
        async with self._token_lock:
            await self._update_tokens()
            if self._token_bucket >= 1:
                self._token_bucket -= 1
                return True
            return False

    def _check_user_limit(self, wxid: str, user_wxid: str) -> float:
        """检查用户请求限制"""
        current_time = time.time()
        if wxid not in self._user_limits:
            self._user_limits[wxid] = {}
        last_request = self._user_limits[wxid].get(user_wxid, 0)
        wait_time = max(0, self.min_request_interval - (current_time - last_request))
        if wait_time == 0:
            self._user_limits[wxid][user_wxid] = current_time
        return wait_time

    async def handle_image_result(self, bot: WechatAPIClient, message: dict, result: Dict[str, Any], query: str = "", quality_mode: str = None):
        """处理图片结果"""
        try:
            if result["type"] == "image":
                if not result.get("images"):
                    msg = random.choice(self.error_msgs) if self.natural_response else "❌ 获取图片失败"
                    await (bot.send_text_message(message["FromWxid"], msg) if self.natural_response else bot.send_at_message(message["FromWxid"], msg, [message["SenderWxid"]]))
                    return

                temp_paths, success_count, failed_count = [], 0, 0
                image_mode = quality_mode if quality_mode else self._determine_image_mode(query)

                for image in result["images"]:
                    selected_url = self._select_image_url(image, image_mode)
                    if not selected_url:
                        failed_count += 1
                        continue

                    temp_path = None
                    try:
                        client = await self.get_session()
                        headers = {'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'}

                        try:
                            response = await client.get(selected_url, headers=headers, timeout=httpx.Timeout(connect=10.0, read=60.0, write=30.0, pool=10.0))
                            if response.status_code != 200 or len(response.content) < 1000:
                                failed_count += 1
                                continue
                            image_data = response.content
                        except:
                            failed_count += 1
                            continue

                        temp_path = await self._save_image(image_data, f"image_{int(time.time())}")
                        if not temp_path or not os.path.exists(temp_path):
                            failed_count += 1
                            continue
                        temp_paths.append(temp_path)

                        try:
                            if not os.path.exists(temp_path) or os.path.getsize(temp_path) < 1024:
                                raise ValueError("图片文件无效")

                            async with aiofiles.open(temp_path, 'rb') as f:
                                image_data = await f.read()

                            if len(image_data) < 1024:
                                raise ValueError("图片数据过小")

                            try:
                                await bot.send_image_message(message["FromWxid"], image_data)
                                success_count += 1
                                await self._mark_file_inactive(temp_path)
                                await asyncio.sleep(1)
                            except httpx.ReadTimeout:
                                await asyncio.sleep(3)
                                try:
                                    await bot.send_image_message(message["FromWxid"], image_data)
                                    success_count += 1
                                    await self._mark_file_inactive(temp_path)
                                except:
                                    failed_count += 1
                            except:
                                failed_count += 1
                        except:
                            failed_count += 1
                            if temp_path:
                                await self._mark_file_inactive(temp_path)
                                if os.path.exists(temp_path):
                                    os.remove(temp_path)
                    except:
                        failed_count += 1
                        if temp_path:
                            await self._mark_file_inactive(temp_path)
                            if os.path.exists(temp_path):
                                os.remove(temp_path)

                if success_count == 0:
                    await bot.send_at_message(message["FromWxid"], "图片生成不了，等会再试试", [message["SenderWxid"]])
                if temp_paths:
                    asyncio.create_task(self._delayed_delete_files(temp_paths, 60))

            elif result["type"] == "text":
                text = result.get("text", "")
                if not text:
                    await bot.send_at_message(message["FromWxid"], "画图失败，请重新描述一下要画的内容", [message["SenderWxid"]])
                    return
                await bot.send_at_message(message["FromWxid"], text, [message["SenderWxid"]])

        except Exception as e:
            logger.error(f"[{self.plugin_name}] 处理图片结果异常: {str(e)}")
            if self.natural_response:
                error_msg = random.choice(self.error_msgs)
                await bot.send_text_message(message["FromWxid"], error_msg)
            else:
                await bot.send_at_message(message["FromWxid"], "处理不了，等会再试试", [message["SenderWxid"]])

    @on_text_message
    async def handle_text(self, bot: WechatAPIClient, message: dict):
        """处理文本消息"""
        if not self.enable:
            return

        content = str(message["Content"]).strip()
        wxid = message["FromWxid"]
        user_wxid = message["SenderWxid"]

        # 简化逻辑：检查是否是画图请求
        if not self.is_drawing_request(content):
            return

        logger.info(f"[{self.plugin_name}] 检测到画图请求: {content}")

        # 提取画图内容 - 处理"豆画"开头的请求
        query = content[2:].strip()  # 移除"豆画"前缀
        image_quality_mode = "original"  # 默认原图模式

        # 检查是否要求缩略图
        if query.endswith(" 缩略图") or query.endswith("缩略图"):
            image_quality_mode = "thumbnail"
            query = query.replace(" 缩略图", "").replace("缩略图", "").strip()

        if not query:
            await bot.send_at_message(wxid, "请告诉我要画什么内容", [user_wxid])
            return

        logger.info(f"[{self.plugin_name}] 开始处理画图请求: '{query}', 质量模式: {image_quality_mode}")

        try:
            # 检查用户请求限制
            wait_time = self._check_user_limit(wxid, user_wxid)
            if wait_time > 0:
                if self.natural_response:
                    rate_limit_msg = random.choice(self.rate_limit_msgs)
                    await bot.send_text_message(wxid, rate_limit_msg)
                else:
                    await bot.send_at_message(wxid, f"慢点慢点，等 {wait_time:.1f} 秒再来", [user_wxid])
                return

            # 检查令牌桶
            if not await self._acquire_token():
                if self.natural_response:
                    rate_limit_msg = random.choice(self.rate_limit_msgs)
                    await bot.send_text_message(wxid, rate_limit_msg)
                else:
                    await bot.send_at_message(wxid, "忙不过来了，歇会儿", [user_wxid])
                return

            # 确认收到请求
            await self._simple_confirm(bot, wxid)

            # 调用画图API
            result = await self.call_doubao_api_for_image(bot, message, query)

            if not result:
                if self.natural_response:
                    error_msg = random.choice(self.error_msgs)
                    await bot.send_text_message(wxid, error_msg)
                else:
                    await bot.send_at_message(wxid, "画图失败，等会再试试", [user_wxid])
                return

            # 处理结果
            await self.handle_image_result(bot, message, result, query, image_quality_mode)

        except Exception as e:
            logger.error(f"[{self.plugin_name}] 处理画图请求异常: {str(e)}")
            if self.natural_response:
                error_msg = random.choice(self.error_msgs)
                await bot.send_text_message(wxid, error_msg)
            else:
                await bot.send_at_message(wxid, "出问题了，等会再试试", [user_wxid])
