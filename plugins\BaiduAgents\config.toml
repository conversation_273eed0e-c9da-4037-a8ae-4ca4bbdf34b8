[BaiduAgents]
enable = true
natural_response = true  # 启用自然化响应，避免机械化回复
command-format = """⚙️百度智能体指令：
@智能体名 问题
或
智能体名 问题

🤖支持的智能体：
- 唐僧：西游记唐僧智能体
"""

# 基础配置
tts-url = "http://www.yx520.ltd/API/wzzyy/zmp3.php"
tts-voice = "502"

# API配置
api-url = "https://agent-proxy-ws.baidu.com/agent/call/conversation"
access-token = "WHFXUemJEeG5ENGI2TW93UHpMMXcyVjQxMnpEbTI5QzJfMTc0MDYxMzQzOVe8xMDA"
cookies = "BIDUPSID=978CBC8268268A8AAF63FDF3AEBE0786; PSTM=1739228496; BAIDUID=978CBC8268268A8AB93DDE580FE5EE68:FG=1"

# 智能体配置
[BaiduAgents.agents.tangseng]
name = "唐僧"
app-key = "XqWRbDxnD4b6MowPzL1w2V412zDm29C2"
command = ["唐僧", "玄奘", "三藏"]
description = "西游记唐僧智能体,慈悲为怀的取经人"
tts-voice = "502"  # 唐僧音色