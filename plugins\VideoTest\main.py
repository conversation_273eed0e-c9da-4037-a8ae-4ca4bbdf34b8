from utils.plugin_base import PluginBase
from utils.decorators import on_text_message
from loguru import logger
from WechatAPI import WechatAPIClient

class VideoTest(PluginBase):
    description = "测试发送视频号"
    author = "XYBot开发者"
    version = "1.0.0"
    plugin_name = "VideoTest"

    def __init__(self):
        super().__init__()
        self.enable = True
        self.command = ["测试视频号"]
        
    @on_text_message
    async def handle_text(self, bot: WechatAPIClient, message: dict):
        if not self.enable:
            return
            
        content = str(message["Content"]).strip()
        if content not in self.command:
            return
            
        wxid = message["FromWxid"]
        
        # 视频号XML内容
        xml_content = '''<?xml version="1.0"?>
<msg>
        <appmsg appid="" sdkver="0">
                <title>当前版本不支持展示该内容，请升级至最新版本。</title>
                <des />
                <username />
                <action>view</action>
                <type>51</type>
                <showtype>0</showtype>
                <content />
                <url>https://support.weixin.qq.com/security/readtemplate?t=w_security_center_website/upgrade</url>
                <lowurl />
                <forwardflag>0</forwardflag>
                <dataurl />
                <lowdataurl />
                <contentattr>0</contentattr>
                <streamvideo>
                        <streamvideourl />
                        <streamvideototaltime>0</streamvideototaltime>
                        <streamvideotitle />
                        <streamvideowording />
                        <streamvideoweburl />
                        <streamvideothumburl />
                        <streamvideoaduxinfo />
                        <streamvideopublishid />
                </streamvideo>
                <canvasPageItem>
                        <canvasPageXml><![CDATA[]]></canvasPageXml>
                </canvasPageItem>
                <appattach>
                        <totallen>0</totallen>
                        <attachid />
                        <cdnattachurl />
                        <emoticonmd5 />
                        <aeskey />
                        <fileext />
                        <islargefilemsg>0</islargefilemsg>
                </appattach>
                <extinfo />
                <androidsource>0</androidsource>
                <thumburl />
                <mediatagname />
                <messageaction><![CDATA[]]></messageaction>
                <messageext><![CDATA[]]></messageext>
                <emoticongift>
                        <packageflag>0</packageflag>
                        <packageid />
                </emoticongift>
                <emoticonshared>
                        <packageflag>0</packageflag>
                        <packageid />
                </emoticonshared>
                <designershared>
                        <designeruin>0</designeruin>
                        <designername>null</designername>
                        <designerrediretcturl><![CDATA[null]]></designerrediretcturl>
                </designershared>
                <emotionpageshared>
                        <tid>0</tid>
                        <title>null</title>
                        <desc>null</desc>
                        <iconUrl><![CDATA[null]]></iconUrl>
                        <secondUrl>null</secondUrl>
                        <pageType>0</pageType>
                        <setKey>null</setKey>
                </emotionpageshared>
                <webviewshared>
                        <shareUrlOriginal />
                        <shareUrlOpen />
                        <jsAppId />
                        <publisherId />
                        <publisherReqId />
                </webviewshared>
                <template_id />
                <md5 />
                <websearch>
                        <rec_category>0</rec_category>
                        <channelId>0</channelId>
                </websearch>
                <weappinfo>
                        <username />
                        <appid />
                        <appservicetype>0</appservicetype>
                        <secflagforsinglepagemode>0</secflagforsinglepagemode>
                        <videopageinfo>
                                <thumbwidth>0</thumbwidth>
                                <thumbheight>0</thumbheight>
                                <fromopensdk>0</fromopensdk>
                        </videopageinfo>
                </weappinfo>
                <statextstr />
                <musicShareItem>
                        <musicDuration>0</musicDuration>
                </musicShareItem>
                <finderFeed>
                        <objectId>14600161583828306357</objectId>
                        <objectNonceId>10659430287452057561_4_20_13_1_1747302531528236</objectNonceId>
                        <feedType>4</feedType>
                        <nickname>是你静哥</nickname>
                        <username>v2_060000231003b20faec8c4ea801cc1d6c901e83cb077695d72abba7aec2a3bac89b918e453c1@finder</username>
                        <avatar><![CDATA[https://wx.qlogo.cn/finderhead/ver_1/Qrk9cC0SFTianXVmjMNAXFwicmj1mwPMqp3mKflDbts07OLQuVuAAatPu2Pr3H2AxMLSmhnHj3ia19UNPS3YpNVibpUH4cExW5kdHdr1JVOyw2gVPJIsQ6ib0zPSlibb8Ag1FAIrSRmOhWwzNPZ5nAMluhIQ/0]]></avatar>
                        <desc>我能给你的感觉是绝版</desc>
                        <mediaCount>1</mediaCount>
                        <localId>0</localId>
                        <authIconType>0</authIconType>
                        <authIconUrl><![CDATA[]]></authIconUrl>
                        <mediaList>
                                <media>
                                        <mediaType>4</mediaType>
                                        <url><![CDATA[http://wxapp.tc.qq.com/251/20302/stodownload?encfilekey=Cvvj5Ix3eewK0tHtibORqcsqchXNh0Gf3sJcaYqC2rQBIUKe9ZeeTibuWXcK9Uy41hLAO7UrmsbRkP4GsUda9fAiaiaf0nZ3AJ19juk4f8ZY9IOGia1iao4eSvYNxx3yzuTw0j&hy=SH&idx=1&m=&uzid=2&token=6xykWLEnztKXZzysgGVkic87MYzCk3Kglq8HzypnqwZguuhR0zsAX0NcDQVWL7MiclX3nqsft3AupolHicbo7c1QHTrDjey9ufd1XdXbicIRbjNC4d52v6sgtkl2R80LVibC8pIv0Dc9EDrk4Y4TPINOaxex1XxeRBibQyMkK6ykQNkDY&basedata=CAESBnhXVDE1NhoGeFdUMTExGgZ4V1QxMDEaBnhXVDExMhoGeFdUMTEzGgZ4V1QxNTYaBnhXVDE1NxoGeFdUMTU4IhgKCgoGeFdUMTEyEAEKCgoGeFdUMTU3EAEqBwi9HhAAGAI&sign=LTc4J8cl7YsxbPQaC5kha8DeeHSKYxh0z9-Z7Jj5Pwsrc5430Mp-GJh3X7OLsGNoH2EWAGaFX_r3tztlCWqwHA&ctsc=20&extg=108b900&ftype=602&svrbypass=AAuL%2FQsFAAABAAAAAACckbXMpDfsbHgGhrglaBAAAADnaHZTnGbFfAj9RgZXfw6VMmDzITzNeJUwzkrxpwEr4GQnoeRvc851R5L69WpfygGjrB8az%2BHDOhk%3D&svrnonce=1747302534]]></url>
                                        <thumbUrl><![CDATA[http://wxapp.tc.qq.com/251/20304/stodownload?encfilekey=oibeqyX228riaCwo9STVsGLPj9UYCicgttv8WCyzz7VcR9XBhic0o8iaPAK1mMGVfLGcmXM0VRGSP90X80AbicFjDpmgqqibsJoszazXZbIahG4GZS0nbs816A40SichQHBI6ZicibGnKOuUOIdtI&hy=SH&idx=1&m=c1aadf2d98a951199ee1b5b9d351cd99&uzid=2&picformat=200&token=6xykWLEnztKIzBicPuvgFxiaRKz2c4Sx3jW0ER9h20OKCkTQrYELgrFKsviakWj1odDvN7r7daNgZgZ1CeiaRSiboV5STttzOzuDGSU4bY6UVsS958Vf1UBq4zqP7ItDAjuGvtAR2GvvQvmN9ia4yZ6T8tt4GtXSR8uSUk&ctsc=2-20]]></thumbUrl>
                                        <coverUrl><![CDATA[http://wxapp.tc.qq.com/251/20304/stodownload?encfilekey=oibeqyX228riaCwo9STVsGLPj9UYCicgttv8WCyzz7VcR9XBhic0o8iaPAK1mMGVfLGcmXM0VRGSP90X80AbicFjDpmgqqibsJoszazXZbIahG4GZS0nbs816A40SichQHBI6ZicibGnKOuUOIdtI&hy=SH&idx=1&m=c1aadf2d98a951199ee1b5b9d351cd99&uzid=2&picformat=200&token=6xykWLEnztKIzBicPuvgFxiaRKz2c4Sx3jW0ER9h20OKCkTQrYELgrFKsviakWj1odDvN7r7daNgZgZ1CeiaRSiboV5STttzOzuDGSU4bY6UVsS958Vf1UBq4zqP7ItDAjuGvtAR2GvvQvmN9ia4yZ6T8tt4GtXSR8uSUk&ctsc=2-20]]></coverUrl>
                                        <fullCoverUrl><![CDATA[]]></fullCoverUrl>
                                        <fullClipInset><![CDATA[]]></fullClipInset>
                                        <width>1080.0</width>
                                        <height>1920.0</height>
                                        <videoPlayDuration>8</videoPlayDuration>
                                </media>
                        </mediaList>
                        <megaVideo>
                                <objectId />
                                <objectNonceId />
                        </megaVideo>
                        <bizUsername />
                        <bizNickname />
                        <bizAvatar><![CDATA[]]></bizAvatar>
                        <bizUsernameV2 />
                        <bizAuthIconType>0</bizAuthIconType>
                        <bizAuthIconUrl><![CDATA[]]></bizAuthIconUrl>
                        <coverEffectType>0</coverEffectType>
                        <coverEffectText><![CDATA[]]></coverEffectText>
                        <finderForwardSource><![CDATA[]]></finderForwardSource>
                        <contactJumpInfoStr><![CDATA[]]></contactJumpInfoStr>
                        <ecSource><![CDATA[]]></ecSource>
                        <lastGMsgID><![CDATA[]]></lastGMsgID>
                        <sourceCommentScene>20</sourceCommentScene>
                        <finderShareExtInfo><![CDATA[{"hasInput":false,"tabContextId":"4-1747302501467","contextId":"1-1-20-25cde2d7872a4a148ab54dc82f7685ff","shareSrcScene":4}]]></finderShareExtInfo>
                </finderFeed>
                <finderLiveProductShare>
                        <finderLiveID><![CDATA[]]></finderLiveID>
                        <finderUsername><![CDATA[]]></finderUsername>
                        <finderObjectID><![CDATA[]]></finderObjectID>
                        <finderNonceID><![CDATA[]]></finderNonceID>
                        <liveStatus><![CDATA[]]></liveStatus>
                        <appId><![CDATA[]]></appId>
                        <pagePath><![CDATA[]]></pagePath>
                        <productId><![CDATA[]]></productId>
                        <coverUrl><![CDATA[]]></coverUrl>
                        <productTitle><![CDATA[]]></productTitle>
                        <marketPrice><![CDATA[0]]></marketPrice>
                        <sellingPrice><![CDATA[0]]></sellingPrice>
                        <platformHeadImg><![CDATA[]]></platformHeadImg>
                        <platformName><![CDATA[]]></platformName>
                        <shopWindowId><![CDATA[]]></shopWindowId>
                        <flashSalePrice><![CDATA[0]]></flashSalePrice>
                        <flashSaleEndTime><![CDATA[0]]></flashSaleEndTime>
                        <ecSource><![CDATA[]]></ecSource>
                        <sellingPriceWording><![CDATA[]]></sellingPriceWording>
                        <platformIconURL><![CDATA[]]></platformIconURL>
                        <firstProductTagURL><![CDATA[]]></firstProductTagURL>
                        <firstProductTagAspectRatioString><![CDATA[0.0]]></firstProductTagAspectRatioString>
                        <secondProductTagURL><![CDATA[]]></secondProductTagURL>
                        <secondProductTagAspectRatioString><![CDATA[0.0]]></secondProductTagAspectRatioString>
                        <firstGuaranteeWording><![CDATA[]]></firstGuaranteeWording>
                        <secondGuaranteeWording><![CDATA[]]></secondGuaranteeWording>
                        <thirdGuaranteeWording><![CDATA[]]></thirdGuaranteeWording>
                        <isPriceBeginShow>false</isPriceBeginShow>
                        <lastGMsgID><![CDATA[]]></lastGMsgID>
                        <promoterKey><![CDATA[]]></promoterKey>
                        <discountWording><![CDATA[]]></discountWording>
                        <priceSuffixDescription><![CDATA[]]></priceSuffixDescription>
                        <productCardKey><![CDATA[]]></productCardKey>
                        <isWxShop><![CDATA[]]></isWxShop>
                        <brandIconUrl><![CDATA[]]></brandIconUrl>
                        <showBoxItemStringList />
                </finderLiveProductShare>
                <finderOrder>
                        <appID><![CDATA[]]></appID>
                        <orderID><![CDATA[]]></orderID>
                        <path><![CDATA[]]></path>
                        <priceWording><![CDATA[]]></priceWording>
                        <stateWording><![CDATA[]]></stateWording>
                        <productImageURL><![CDATA[]]></productImageURL>
                        <products><![CDATA[]]></products>
                        <productsCount><![CDATA[0]]></productsCount>
                        <orderType><![CDATA[0]]></orderType>
                        <newPriceWording><![CDATA[]]></newPriceWording>
                        <newStateWording><![CDATA[]]></newStateWording>
                        <useNewWording><![CDATA[0]]></useNewWording>
                </finderOrder>
                <finderShopWindowShare>
                        <finderUsername><![CDATA[]]></finderUsername>
                        <avatar><![CDATA[]]></avatar>
                        <nickname><![CDATA[]]></nickname>
                        <commodityInStockCount><![CDATA[]]></commodityInStockCount>
                        <appId><![CDATA[]]></appId>
                        <path><![CDATA[]]></path>
                        <appUsername><![CDATA[]]></appUsername>
                        <query><![CDATA[]]></query>
                        <liteAppId><![CDATA[]]></liteAppId>
                        <liteAppPath><![CDATA[]]></liteAppPath>
                        <liteAppQuery><![CDATA[]]></liteAppQuery>
                        <platformTagURL><![CDATA[]]></platformTagURL>
                        <saleWording><![CDATA[]]></saleWording>
                        <lastGMsgID><![CDATA[]]></lastGMsgID>
                        <profileTypeWording><![CDATA[]]></profileTypeWording>
                        <saleWordingExtra><![CDATA[]]></saleWordingExtra>
                        <isWxShop><![CDATA[]]></isWxShop>
                        <platformIconUrl><![CDATA[]]></platformIconUrl>
                        <brandIconUrl><![CDATA[]]></brandIconUrl>
                        <description><![CDATA[]]></description>
                        <backgroundUrl><![CDATA[]]></backgroundUrl>
                        <darkModePlatformIconUrl><![CDATA[]]></darkModePlatformIconUrl>
                        <reputationInfo>
                                <hasReputationInfo>0</hasReputationInfo>
                                <reputationScore>0</reputationScore>
                                <reputationWording />
                                <reputationTextColor />
                                <reputationLevelWording />
                                <reputationBackgroundColor />
                        </reputationInfo>
                        <productImageURLList />
                </finderShopWindowShare>
                <findernamecard>
                        <username />
                        <avatar><![CDATA[]]></avatar>
                        <nickname />
                        <auth_job />
                        <auth_icon>0</auth_icon>
                        <auth_icon_url />
                        <ecSource><![CDATA[]]></ecSource>
                        <lastGMsgID><![CDATA[]]></lastGMsgID>
                </findernamecard>
                <finderGuarantee>
                        <scene><![CDATA[0]]></scene>
                </finderGuarantee>
                <directshare>0</directshare>
                <gamecenter>
                        <namecard>
                                <iconUrl />
                                <name />
                                <desc />
                                <tail />
                                <jumpUrl />
                        </namecard>
                </gamecenter>
                <patMsg>
                        <chatUser />
                        <records>
                                <recordNum>0</recordNum>
                        </records>
                </patMsg>
                <secretmsg>
                        <issecretmsg>0</issecretmsg>
                </secretmsg>
                <referfromscene>0</referfromscene>
                <gameshare>
                        <liteappext>
                                <liteappbizdata />
                                <priority>0</priority>
                        </liteappext>
                        <appbrandext>
                                <litegameinfo />
                                <priority>-1</priority>
                        </appbrandext>
                        <gameshareid />
                        <sharedata />
                        <isvideo>0</isvideo>
                        <duration>-1</duration>
                        <isexposed>0</isexposed>
                        <readtext />
                </gameshare>
                <mpsharetrace>
                        <hasfinderelement>0</hasfinderelement>
                        <lastgmsgid />
                </mpsharetrace>
                <wxgamecard>
                        <framesetname />
                        <mbcarddata />
                        <minpkgversion />
                        <clientextinfo />
                        <mbcardheight>0</mbcardheight>
                        <isoldversion>0</isoldversion>
                </wxgamecard>
                <liteapp>
                        <id>null</id>
                        <path />
                        <query />
                        <istransparent>0</istransparent>
                        <hideicon>0</hideicon>
                </liteapp>
                <opensdk_share_is_modified>0</opensdk_share_is_modified>
        </appmsg>
        <fromusername>wxid_ubbh6q832tcs21</fromusername>
        <scene>0</scene>
        <appinfo>
                <version>1</version>
                <appname></appname>
        </appinfo>
        <commenturl></commenturl>
</msg>'''
        
        try:
            # 发送视频号内容
            await bot.send_text_message(wxid, "正在测试发送视频号内容...")
            
            # 使用send_app_message发送视频号内容
            client_msg_id, create_time, new_msg_id = await bot.send_app_message(
                wxid,
                xml_content,
                51  # 视频号消息类型为51
            )
            
            # 发送结果
            result = f"视频号内容发送结果:\nMsgID: {new_msg_id}\n时间戳: {create_time}"
            await bot.send_text_message(wxid, result)
            
        except Exception as e:
            logger.error(f"[VideoTest] 发送视频号内容失败: {str(e)}")
            await bot.send_text_message(wxid, f"发送失败: {str(e)}")
