import json, httpx, time, traceback, random, uuid, re
try:
    import tomllib
except ModuleNotFoundError:
    import tomli as tomllib
from loguru import logger
from pathlib import Path
from WechatAPI import WechatAPIClient
from utils.decorators import on_text_message
from utils.plugin_base import PluginBase

class TongyiDraw(PluginBase):
    description = "通义千问绘图插件"
    author = "XYBot"
    version = "1.0.0"

    def __init__(self):
        super().__init__()
        self.plugin_name = "TongyiDraw"
        self.temp_dir = Path("plugins/TongyiDraw/temp")
        self.temp_dir.mkdir(parents=True, exist_ok=True)
        self._load_config()
        self.user_last_request = {}
        self._init_natural_responses()

    def _load_config(self):
        try:
            with open(f"plugins/{self.plugin_name}/config.toml", "rb") as f:
                config = tomllib.load(f).get(self.plugin_name, {})
        except:
            config = {}

        self.enable = config.get("enable", True)
        self.command = config.get("command", ["通义"])
        self.command_format = config.get("command-format", "通义 <提示词> - 使用通义千问生成图片")
        self.cooldown = config.get("rate_limit", {}).get("cooldown", 10)
        self.natural_response = config.get("natural_response", True)
        
        # API配置
        api_config = config.get("api", {})
        self.base_url = api_config.get("base_url", "https://api.tongyi.com")
        self.endpoint = api_config.get("endpoint", "/dialog/conversation")
        self.timeout = api_config.get("timeout", 60)
        
        # 请求头配置
        headers_config = config.get("headers", {})
        self.headers = {
            "accept": headers_config.get("accept", "text/event-stream"),
            "X-Platform": headers_config.get("x_platform", "h5"),
            "User-Agent": headers_config.get("user_agent", "Mozilla/5.0 (Linux; Android 10; V2002A Build/QP1A.190711.020) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/83.0.4103.106 Mobile Safari/537.36"),
            "Content-Type": headers_config.get("content_type", "application/json"),
            "Origin": headers_config.get("origin", "https://www.tongyi.com"),
            "X-Requested-With": headers_config.get("x_requested_with", "mark.via"),
            "Sec-Fetch-Site": headers_config.get("sec_fetch_site", "same-site"),
            "Sec-Fetch-Mode": headers_config.get("sec_fetch_mode", "cors"),
            "Sec-Fetch-Dest": headers_config.get("sec_fetch_dest", "empty"),
            "Referer": headers_config.get("referer", "https://www.tongyi.com/qianwen"),
            "Accept-Encoding": headers_config.get("accept_encoding", "gzip, deflate"),
            "Accept-Language": headers_config.get("accept_language", "zh-CN,zh;q=0.9,en-US;q=0.8,en;q=0.7")
        }

        # 添加认证信息（如果配置了的话）
        self.cookie = headers_config.get("cookie", "")
        self.x_xsrf_token = headers_config.get("x_xsrf_token", "")

        if self.cookie:
            self.headers["Cookie"] = self.cookie
        if self.x_xsrf_token:
            self.headers["X-XSRF-TOKEN"] = self.x_xsrf_token

    def _init_natural_responses(self):
        """初始化自然化回复词库"""
        self.confirm_msgs = [
            "好的", "收到", "明白了", "嗯", "知道了", "行", "OK"
        ]

        self.error_msgs = [
            "这可把我难住了", "搞不定了", "这个有点难", "弄不了",
            "出问题了", "搞不出来", "这个不行"
        ]

        self.rate_limit_msgs = [
            "你搁这刷bug呢", "慢点慢点", "别这么急", "一个一个来",
            "太快了太快了", "歇会儿", "别刷了"
        ]

    async def _simple_confirm(self, bot, wxid):
        """简单确认回复，不艾特人"""
        if self.natural_response:
            confirm_msg = random.choice(self.confirm_msgs)
            await bot.send_text_message(wxid, confirm_msg)

    @on_text_message
    async def handle_text(self, bot: WechatAPIClient, message: dict):
        if not self.enable:
            return

        content = str(message["Content"]).strip()
        wxid = message["FromWxid"]
        user_wxid = message["SenderWxid"]

        command = content.split(" ", 1)
        if command[0] not in self.command:
            return

        if len(command) == 1:
            await bot.send_at_message(wxid, self.command_format, [user_wxid])
            return

        # 限流检查
        wait_time = self._check_user_limit(wxid, user_wxid)
        if wait_time > 0:
            if self.natural_response:
                rate_limit_msg = random.choice(self.rate_limit_msgs)
                await bot.send_text_message(wxid, rate_limit_msg)
            else:
                await bot.send_at_message(wxid, f"请等待 {wait_time:.1f} 秒", [user_wxid])
            return

        try:
            # 处理绘图请求
            prompt = command[1].strip()
            result = await self._generate_image(prompt, bot, wxid)

            if not result:
                if self.natural_response:
                    error_msg = random.choice(self.error_msgs)
                    await bot.send_text_message(wxid, error_msg)
                else:
                    await bot.send_at_message(wxid, "图片生成失败", [user_wxid])

        except Exception as e:
            logger.error(f"[{self.plugin_name}] 异常: {e}")
            logger.error(traceback.format_exc())
            if self.natural_response:
                error_msg = random.choice(self.error_msgs)
                await bot.send_text_message(wxid, error_msg)
            else:
                await bot.send_at_message(wxid, "处理失败", [user_wxid])

    async def _generate_image(self, prompt: str, bot, wxid: str) -> bool:
        """调用通义千问API生成图片"""
        try:
            # 生成请求ID
            request_id = str(uuid.uuid4()).replace("-", "")

            # 构建请求数据
            data = {
                "requestId": request_id,
                "sessionId": "",
                "contents": [
                    {
                        "role": "user",
                        "contentType": "text",
                        "content": prompt
                    }
                ],
                "action": "next",
                "userAction": "chat",
                "sessionType": "text_chat",
                "mode": "chat",
                "parentMsgId": "",
                "model": ""
            }

            url = f"{self.base_url}{self.endpoint}"

            async with httpx.AsyncClient(timeout=self.timeout) as client:
                async with client.stream("POST", url, json=data, headers=self.headers) as response:
                    if response.status_code != 200:
                        logger.error(f"[{self.plugin_name}] API请求失败: {response.status_code}")
                        return False

                    last_text_content = ""
                    image_url = None

                    # 处理SSE流式响应
                    async for line in response.aiter_lines():
                        if line.startswith("data: "):
                            data_content = line[6:]  # 去掉 "data: " 前缀

                            if data_content == "[DONE]":
                                break

                            try:
                                json_data = json.loads(data_content)

                                # 检查是否包含内容
                                if "contents" in json_data:
                                    for content in json_data["contents"]:
                                        content_type = content.get("contentType", "")

                                        # 处理文本内容
                                        if content_type == "text":
                                            current_text = content.get("content", "")
                                            # 只有当文本内容发生变化且状态为finished时才发送
                                            if (current_text != last_text_content and
                                                content.get("status") == "finished" and
                                                current_text.strip()):
                                                await bot.send_text_message(wxid, current_text)
                                                last_text_content = current_text

                                        # 处理图片内容
                                        elif content_type == "text2image":
                                            text_content = content.get("content", "")
                                            extracted_url = self._extract_image_url(text_content)
                                            if extracted_url:
                                                image_url = extracted_url


                            except json.JSONDecodeError:
                                continue

                    # 如果有图片URL，下载并发送图片
                    if image_url:
                        await self._download_and_send_image(bot, wxid, image_url)
                        return True

        except Exception as e:
            logger.error(f"[{self.plugin_name}] 生成图片异常: {e}")
            logger.error(traceback.format_exc())

        return False

    def _extract_image_url(self, text: str) -> str:
        """从文本中提取图片URL"""
        # 使用正则表达式提取图片URL
        # 根据抓包信息，图片URL格式为: ![](https://wanx.alicdn.com/...)
        pattern = r'!\[.*?\]\((https://wanx\.alicdn\.com/[^)]+)\)'
        match = re.search(pattern, text)
        if match:
            return match.group(1)
        return None

    async def _download_and_send_image(self, bot: WechatAPIClient, wxid: str, image_url: str):
        """下载图片并发送"""
        try:
            # 处理图片URL，移除水印和格式转换参数，获取原始PNG格式
            clean_url = self._clean_image_url(image_url)

            async with httpx.AsyncClient(timeout=30) as client:
                # 首先尝试清理后的URL
                response = await client.get(clean_url)

                # 如果清理后的URL失败，尝试原始URL
                if response.status_code != 200:
                    logger.warning(f"[{self.plugin_name}] 清理后URL失败，尝试原始URL")
                    response = await client.get(image_url)

                if response.status_code == 200:
                    image_data = response.content

                    # 检查图片格式，如果是webp则转换为jpg
                    if self._is_webp_format(image_data):

                        converted_data = await self._convert_webp_to_jpg(image_data)
                        if converted_data:
                            image_data = converted_data
                        else:
                            logger.warning(f"[{self.plugin_name}] webp转换失败，尝试直接发送")

                    await bot.send_image_message(wxid, image_data)

                else:
                    logger.error(f"[{self.plugin_name}] 下载图片失败: {response.status_code}")

        except Exception as e:
            logger.error(f"[{self.plugin_name}] 下载发送图片异常: {e}")

    def _clean_image_url(self, url: str) -> str:
        """清理图片URL，移除水印和格式转换参数"""
        # 原始URL示例: https://wanx.alicdn.com/wanx/1839978429182535/text_to_image_lite_v2/081c0be8523044faacf75ff5da97cf1a_0.png?x-oss-process=image/watermark,image_aW1nL3dhdGVyMjAyNDExMjkwLnBuZz94LW9zcy1wcm9jZXNzPWltYWdlL3Jlc2l6ZSxtX2ZpeGVkLHdfMzAzLGhfNTI=,t_80,g_se,x_10,y_10/format,webp

        # 方法1: 直接移除所有OSS处理参数，获取原始PNG
        if '?' in url:
            clean_url = url.split('?')[0]
        else:
            clean_url = url

        # 方法2: 如果还是不行，尝试构造一个更简单的URL
        # 保留基础路径，但移除格式转换
        if '/format,webp' in clean_url:
            clean_url = clean_url.replace('/format,webp', '')

        return clean_url

    def _is_webp_format(self, image_data: bytes) -> bool:
        """检查图片是否为webp格式"""
        return image_data.startswith(b'RIFF') and b'WEBP' in image_data[:12]

    async def _convert_webp_to_jpg(self, webp_data: bytes) -> bytes:
        """将webp格式转换为jpg格式"""
        try:
            from PIL import Image
            import io

            # 使用PIL转换格式
            webp_image = Image.open(io.BytesIO(webp_data))

            # 如果是RGBA模式，转换为RGB（jpg不支持透明度）
            if webp_image.mode in ('RGBA', 'LA'):
                # 创建白色背景
                background = Image.new('RGB', webp_image.size, (255, 255, 255))
                if webp_image.mode == 'RGBA':
                    background.paste(webp_image, mask=webp_image.split()[-1])
                else:
                    background.paste(webp_image)
                webp_image = background
            elif webp_image.mode != 'RGB':
                webp_image = webp_image.convert('RGB')

            # 保存为jpg格式
            output = io.BytesIO()
            webp_image.save(output, format='JPEG', quality=90)
            jpg_data = output.getvalue()


            return jpg_data

        except ImportError:
            logger.warning(f"[{self.plugin_name}] PIL库未安装，无法转换图片格式")
            return None
        except Exception as e:
            logger.error(f"[{self.plugin_name}] 图片格式转换失败: {e}")
            return None

    def _check_user_limit(self, wxid: str, user_wxid: str) -> float:
        user_key = f"{wxid}_{user_wxid}"
        last_request = self.user_last_request.get(user_key, 0)
        current_time = time.time()
        elapsed = current_time - last_request
        self.user_last_request[user_key] = current_time

        if elapsed < self.cooldown:
            return self.cooldown - elapsed

        return 0
