2025-07-27 00:01:29 | DEBUG | 收到消息: {'MsgId': 1697664897, 'FromUserName': {'string': '47325400669@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 1, 'Content': {'string': 'xun900112:\n授权码'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1753545695, 'MsgSource': '<msgsource>\n\t<alnode>\n\t\t<fr>1</fr>\n\t</alnode>\n\t<silence>0</silence>\n\t<membercount>221</membercount>\n\t<signature>N0_V1_zHMKzr0F|v1_GQIHuCQy</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'PushContent': '金永勋 : 授权码', 'NewMsgId': 4201756349792504007, 'MsgSeq': 871402611}
2025-07-27 00:01:29 | INFO | 收到文本消息: 消息ID:1697664897 来自:47325400669@chatroom 发送人:xun900112 @:[] 内容:授权码
2025-07-27 00:01:29 | DEBUG | 处理消息内容: '授权码'
2025-07-27 00:01:29 | DEBUG | 消息内容 '授权码' 不匹配任何命令，忽略
2025-07-27 00:01:33 | DEBUG | 收到消息: {'MsgId': 184270866, 'FromUserName': {'string': '47325400669@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 1, 'Content': {'string': 'wxid_l5im9jaxhr4412:\n授权码：0464b-fb70b-fc7f0-2b7a3-e6823\n状态：active\n有效期：2026-07-27T00:01:37.564773'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1753545698, 'MsgSource': '<msgsource>\n\t<silence>0</silence>\n\t<membercount>221</membercount>\n\t<signature>N0_V1_vSbW0FWR|v1_ELaGjolL</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'PushContent': '小球子 : 授权码：0464b-fb70b-fc7f0-2b7a3-e6823\n状态：active\n有效期：2026-07-27T00:01:37....', 'NewMsgId': 5827935195322568238, 'MsgSeq': 871402612}
2025-07-27 00:01:33 | INFO | 收到文本消息: 消息ID:184270866 来自:47325400669@chatroom 发送人:wxid_l5im9jaxhr4412 @:[] 内容:授权码：0464b-fb70b-fc7f0-2b7a3-e6823
状态：active
有效期：2026-07-27T00:01:37.564773
2025-07-27 00:01:33 | DEBUG | 处理消息内容: '授权码：0464b-fb70b-fc7f0-2b7a3-e6823
状态：active
有效期：2026-07-27T00:01:37.564773'
2025-07-27 00:01:33 | DEBUG | 消息内容 '授权码：0464b-fb70b-fc7f0-2b7a3-e6823
状态：active
有效期：2026-07-27T00:01:37.564773' 不匹配任何命令，忽略
2025-07-27 00:03:46 | DEBUG | 收到消息: {'MsgId': 986032440, 'FromUserName': {'string': '27852221909@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 1, 'Content': {'string': 'wxid_ikxxrwasicud11:\n唱舞签到'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1753545831, 'MsgSource': '<msgsource>\n\t<sec_msg_node>\n\t\t<alnode>\n\t\t\t<fr>1</fr>\n\t\t</alnode>\n\t</sec_msg_node>\n\t<pua>1</pua>\n\t<silence>1</silence>\n\t<membercount>145</membercount>\n\t<signature>N0_V1_auFkQaD4|v1_S6zNHGjB</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'NewMsgId': 3587477661585494188, 'MsgSeq': 871402613}
2025-07-27 00:03:46 | INFO | 收到文本消息: 消息ID:986032440 来自:27852221909@chatroom 发送人:wxid_ikxxrwasicud11 @:[] 内容:唱舞签到
2025-07-27 00:03:46 | INFO | 发送链接消息: 对方wxid:27852221909@chatroom 链接:https://open.weixin.qq.com/connect/oauth2/authorize?appid=wx6de432d4ad7e151c&redirect_uri=http%3A%2F%2Freserve.fhsj.xipu.com%2Fapi%2Fsign%2Findex&response_type=code&scope=snsapi_base&state=STATE#wechat_redirect 标题:唱舞全明星 描述:点击进入签到页面，领取专属福利 缩略图链接:https://mmbiz.qpic.cn/mmbiz_jpg/RicuJvxibRtUBlv9G75UTulanktDF1OxFO7Wyhzs1WS609tq1j9icfNhLkM6zB3lwM5ZlbgQia1ibIcxuj35WAm465w/640?wxtype=jpeg&wxfrom=0
2025-07-27 00:03:46 | DEBUG | 处理消息内容: '唱舞签到'
2025-07-27 00:03:46 | DEBUG | 消息内容 '唱舞签到' 不匹配任何命令，忽略
2025-07-27 00:06:17 | DEBUG | 收到消息: {'MsgId': 1559901361, 'FromUserName': {'string': '47325400669@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 1, 'Content': {'string': 'xun900112:\n授权码'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1753545982, 'MsgSource': '<msgsource>\n\t<alnode>\n\t\t<fr>1</fr>\n\t</alnode>\n\t<silence>0</silence>\n\t<membercount>221</membercount>\n\t<signature>N0_V1_YfB5PfcA|v1_TliTMQlM</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'PushContent': '金永勋 : 授权码', 'NewMsgId': 3377005365639069532, 'MsgSeq': 871402617}
2025-07-27 00:06:17 | INFO | 收到文本消息: 消息ID:1559901361 来自:47325400669@chatroom 发送人:xun900112 @:[] 内容:授权码
2025-07-27 00:06:17 | DEBUG | 处理消息内容: '授权码'
2025-07-27 00:06:17 | DEBUG | 消息内容 '授权码' 不匹配任何命令，忽略
2025-07-27 00:06:19 | DEBUG | 收到消息: {'MsgId': 1544173543, 'FromUserName': {'string': '47325400669@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 1, 'Content': {'string': 'wxid_l5im9jaxhr4412:\n授权码：b5fa2-01796-94e2f-50f04-fc6ce\n状态：active\n有效期：2026-07-27T00:06:23.788339'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1753545984, 'MsgSource': '<msgsource>\n\t<silence>0</silence>\n\t<membercount>221</membercount>\n\t<signature>N0_V1_Xm83+XnQ|v1_PoC8syHR</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'PushContent': '小球子 : 授权码：b5fa2-01796-94e2f-50f04-fc6ce\n状态：active\n有效期：2026-07-27T00:06:23....', 'NewMsgId': 5626450042119852717, 'MsgSeq': 871402618}
2025-07-27 00:06:19 | INFO | 收到文本消息: 消息ID:1544173543 来自:47325400669@chatroom 发送人:wxid_l5im9jaxhr4412 @:[] 内容:授权码：b5fa2-01796-94e2f-50f04-fc6ce
状态：active
有效期：2026-07-27T00:06:23.788339
2025-07-27 00:06:19 | DEBUG | 处理消息内容: '授权码：b5fa2-01796-94e2f-50f04-fc6ce
状态：active
有效期：2026-07-27T00:06:23.788339'
2025-07-27 00:06:19 | DEBUG | 消息内容 '授权码：b5fa2-01796-94e2f-50f04-fc6ce
状态：active
有效期：2026-07-27T00:06:23.788339' 不匹配任何命令，忽略
2025-07-27 00:06:52 | DEBUG | 收到消息: {'MsgId': 2072062849, 'FromUserName': {'string': '***********@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 3, 'Content': {'string': 'wxid_e3o8s2nf9u2o22:\n<?xml version="1.0"?>\n<msg>\n\t<img aeskey="41a3d2854646e80e6bcc08051a586fea" encryver="1" cdnthumbaeskey="41a3d2854646e80e6bcc08051a586fea" cdnthumburl="3057020100044b304902010002041c3acaf602032f50e60204284e8b7b02046884fd21042430626263303437342d656337662d343263302d626631372d356366643734643761663739020405290a020201000405004c4cd200" cdnthumblength="3231" cdnthumbheight="336" cdnthumbwidth="504" cdnmidheight="0" cdnmidwidth="0" cdnhdheight="0" cdnhdwidth="0" cdnmidimgurl="3057020100044b304902010002041c3acaf602032f50e60204284e8b7b02046884fd21042430626263303437342d656337662d343263302d626631372d356366643734643761663739020405290a020201000405004c4cd200" length="352908" md5="95cb3fc366019b3b42a67648f4503054" hevc_mid_size="38594">\n\t\t<secHashInfoBase64>eyJwaGFzaCI6IjU0YTRkMDgwMDAwMDAwMDAiLCJwZHFIYXNoIjoiYjBiM2NkMmI0ODRjMzY3NGE3\nYTMyM2IzZDlkMTk4ZDFmY2YyNmM2NTg2NjFjNjkzYzMxYmNkMzk2ZTI0MWI4ZSJ9\n</secHashInfoBase64>\n\t\t<live>\n\t\t\t<duration>0</duration>\n\t\t\t<size>0</size>\n\t\t\t<md5 />\n\t\t\t<fileid />\n\t\t\t<hdsize>0</hdsize>\n\t\t\t<hdmd5 />\n\t\t\t<hdfileid />\n\t\t\t<stillimagetimems>0</stillimagetimems>\n\t\t</live>\n\t</img>\n\t<platform_signature />\n\t<imgdatahash />\n\t<ImgSourceInfo>\n\t\t<ImgSourceUrl />\n\t\t<BizType>0</BizType>\n\t</ImgSourceInfo>\n</msg>\n'}, 'Status': 3, 'ImgStatus': 2, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1753546017, 'MsgSource': '<msgsource>\n\t<alnode>\n\t\t<fr>4</fr>\n\t</alnode>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n\t<sec_msg_node>\n\t\t<uuid>8f36ffebb406638501284dbaf49dee5d_</uuid>\n\t\t<risk-file-flag />\n\t\t<risk-file-md5-list />\n\t</sec_msg_node>\n\t<silence>0</silence>\n\t<membercount>73</membercount>\n\t<signature>N0_V1_nxot3PaC|v1_9UzzG1z6</signature>\n</msgsource>\n', 'PushContent': '星空- 梦在群聊中发了一张图片', 'NewMsgId': 1538239138151520393, 'MsgSeq': 871402619}
2025-07-27 00:06:52 | INFO | 收到图片消息: 消息ID:2072062849 来自:***********@chatroom 发送人:wxid_e3o8s2nf9u2o22 XML:<?xml version="1.0"?><msg><img aeskey="41a3d2854646e80e6bcc08051a586fea" encryver="1" cdnthumbaeskey="41a3d2854646e80e6bcc08051a586fea" cdnthumburl="3057020100044b304902010002041c3acaf602032f50e60204284e8b7b02046884fd21042430626263303437342d656337662d343263302d626631372d356366643734643761663739020405290a020201000405004c4cd200" cdnthumblength="3231" cdnthumbheight="336" cdnthumbwidth="504" cdnmidheight="0" cdnmidwidth="0" cdnhdheight="0" cdnhdwidth="0" cdnmidimgurl="3057020100044b304902010002041c3acaf602032f50e60204284e8b7b02046884fd21042430626263303437342d656337662d343263302d626631372d356366643734643761663739020405290a020201000405004c4cd200" length="352908" md5="95cb3fc366019b3b42a67648f4503054" hevc_mid_size="38594"><secHashInfoBase64>eyJwaGFzaCI6IjU0YTRkMDgwMDAwMDAwMDAiLCJwZHFIYXNoIjoiYjBiM2NkMmI0ODRjMzY3NGE3YTMyM2IzZDlkMTk4ZDFmY2YyNmM2NTg2NjFjNjkzYzMxYmNkMzk2ZTI0MWI4ZSJ9</secHashInfoBase64><live><duration>0</duration><size>0</size><md5 /><fileid /><hdsize>0</hdsize><hdmd5 /><hdfileid /><stillimagetimems>0</stillimagetimems></live></img><platform_signature /><imgdatahash /><ImgSourceInfo><ImgSourceUrl /><BizType>0</BizType></ImgSourceInfo></msg>
2025-07-27 00:06:53 | INFO | [ImageEcho] 保存图片信息成功，当前群 ***********@chatroom 已存储 5 张图片
2025-07-27 00:06:53 | INFO | [TimerTask] 缓存图片消息: 2072062849
2025-07-27 00:07:14 | DEBUG | 收到消息: {'MsgId': 327562911, 'FromUserName': {'string': '***********@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 1, 'Content': {'string': 'wxid_n5c0aekjceu621:\n呦西黑丝包臀[色]'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1753546040, 'MsgSource': '<msgsource>\n\t<pua>1</pua>\n\t<silence>0</silence>\n\t<membercount>73</membercount>\n\t<signature>N0_V1_TgFp5H8A|v1_bMtTzAJN</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'PushContent': 'ㅤㅤㅤㅤㅤㅤㅤㅤㅤㅤㅤㅤㅤㅤㅤㅤ : 呦西黑丝包臀[色]', 'NewMsgId': 7763500433060545562, 'MsgSeq': 871402620}
2025-07-27 00:07:14 | INFO | 收到文本消息: 消息ID:327562911 来自:***********@chatroom 发送人:wxid_n5c0aekjceu621 @:[] 内容:呦西黑丝包臀[色]
2025-07-27 00:07:15 | DEBUG | 处理消息内容: '呦西黑丝包臀[色]'
2025-07-27 00:07:15 | DEBUG | 消息内容 '呦西黑丝包臀[色]' 不匹配任何命令，忽略
2025-07-27 00:08:43 | DEBUG | 收到消息: {'MsgId': 1399833804, 'FromUserName': {'string': '***********@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 49, 'Content': {'string': 'wxid_ubbh6q832tcs21:\n<?xml version="1.0"?>\n<msg>\n\t<appmsg appid="" sdkver="0">\n\t\t<title>识图 请输出适用于可灵ai的提示词</title>\n\t\t<des />\n\t\t<username />\n\t\t<action>view</action>\n\t\t<type>57</type>\n\t\t<showtype>0</showtype>\n\t\t<content />\n\t\t<url />\n\t\t<lowurl />\n\t\t<forwardflag>0</forwardflag>\n\t\t<dataurl />\n\t\t<lowdataurl />\n\t\t<contentattr>0</contentattr>\n\t\t<streamvideo>\n\t\t\t<streamvideourl />\n\t\t\t<streamvideototaltime>0</streamvideototaltime>\n\t\t\t<streamvideotitle />\n\t\t\t<streamvideowording />\n\t\t\t<streamvideoweburl />\n\t\t\t<streamvideothumburl />\n\t\t\t<streamvideoaduxinfo />\n\t\t\t<streamvideopublishid />\n\t\t</streamvideo>\n\t\t<canvasPageItem>\n\t\t\t<canvasPageXml><![CDATA[]]></canvasPageXml>\n\t\t</canvasPageItem>\n\t\t<refermsg>\n\t\t\t<type>3</type>\n\t\t\t<svrid>1538239138151520393</svrid>\n\t\t\t<fromusr>***********@chatroom</fromusr>\n\t\t\t<chatusr>wxid_e3o8s2nf9u2o22</chatusr>\n\t\t\t<displayname>星空- 梦</displayname>\n\t\t\t<msgsource>&lt;msgsource&gt;\n\t&lt;alnode&gt;\n\t\t&lt;fr&gt;4&lt;/fr&gt;\n\t&lt;/alnode&gt;\n\t&lt;tmp_node&gt;\n\t\t&lt;publisher-id&gt;&lt;/publisher-id&gt;\n\t&lt;/tmp_node&gt;\n\t&lt;sec_msg_node&gt;\n\t\t&lt;uuid&gt;8f36ffebb406638501284dbaf49dee5d_&lt;/uuid&gt;\n\t\t&lt;risk-file-flag /&gt;\n\t\t&lt;risk-file-md5-list /&gt;\n\t&lt;/sec_msg_node&gt;\n\t&lt;imgmsg_pd cdnmidimgurl_size="38594" cdnmidimgurl_pd_pri="50" cdnmidimgurl_pd="1" /&gt;\n\t&lt;silence&gt;1&lt;/silence&gt;\n\t&lt;membercount&gt;73&lt;/membercount&gt;\n\t&lt;NotAutoDownloadRange&gt;20:00-22:00;00:00-01:00&lt;/NotAutoDownloadRange&gt;\n\t&lt;signature&gt;N0_V1_nLzEdMW7|v1_2Dp12/Do&lt;/signature&gt;\n&lt;/msgsource&gt;\n</msgsource>\n\t\t\t<content>&lt;?xml version="1.0"?&gt;\n&lt;msg&gt;\n\t&lt;img aeskey="41a3d2854646e80e6bcc08051a586fea" encryver="1" cdnthumbaeskey="41a3d2854646e80e6bcc08051a586fea" cdnthumburl="3057020100044b304902010002041c3acaf602032f50e60204284e8b7b02046884fd21042430626263303437342d656337662d343263302d626631372d356366643734643761663739020405290a020201000405004c4cd200" cdnthumblength="3231" cdnthumbheight="336" cdnthumbwidth="504" cdnmidheight="0" cdnmidwidth="0" cdnhdheight="0" cdnhdwidth="0" cdnmidimgurl="3057020100044b304902010002041c3acaf602032f50e60204284e8b7b02046884fd21042430626263303437342d656337662d343263302d626631372d356366643734643761663739020405290a020201000405004c4cd200" length="352908" md5="95cb3fc366019b3b42a67648f4503054" hevc_mid_size="38594"&gt;\n\t\t&lt;secHashInfoBase64&gt;eyJwaGFzaCI6IjU0YTRkMDgwMDAwMDAwMDAiLCJwZHFIYXNoIjoiYjBiM2NkMmI0ODRjMzY3NGE3\nYTMyM2IzZDlkMTk4ZDFmY2YyNmM2NTg2NjFjNjkzYzMxYmNkMzk2ZTI0MWI4ZSJ9\n&lt;/secHashInfoBase64&gt;\n\t\t&lt;live&gt;\n\t\t\t&lt;duration&gt;0&lt;/duration&gt;\n\t\t\t&lt;size&gt;0&lt;/size&gt;\n\t\t\t&lt;md5 /&gt;\n\t\t\t&lt;fileid /&gt;\n\t\t\t&lt;hdsize&gt;0&lt;/hdsize&gt;\n\t\t\t&lt;hdmd5 /&gt;\n\t\t\t&lt;hdfileid /&gt;\n\t\t\t&lt;stillimagetimems&gt;0&lt;/stillimagetimems&gt;\n\t\t&lt;/live&gt;\n\t&lt;/img&gt;\n\t&lt;platform_signature /&gt;\n\t&lt;imgdatahash /&gt;\n\t&lt;ImgSourceInfo&gt;\n\t\t&lt;ImgSourceUrl /&gt;\n\t\t&lt;BizType&gt;0&lt;/BizType&gt;\n\t&lt;/ImgSourceInfo&gt;\n&lt;/msg&gt;\n</content>\n\t\t\t<strid />\n\t\t\t<createtime>1753546017</createtime>\n\t\t</refermsg>\n\t\t<appattach>\n\t\t\t<totallen>0</totallen>\n\t\t\t<attachid />\n\t\t\t<cdnattachurl />\n\t\t\t<emoticonmd5 />\n\t\t\t<aeskey />\n\t\t\t<fileext />\n\t\t\t<islargefilemsg>0</islargefilemsg>\n\t\t</appattach>\n\t\t<extinfo />\n\t\t<androidsource>0</androidsource>\n\t\t<thumburl />\n\t\t<mediatagname />\n\t\t<messageaction><![CDATA[]]></messageaction>\n\t\t<messageext><![CDATA[]]></messageext>\n\t\t<emoticongift>\n\t\t\t<packageflag>0</packageflag>\n\t\t\t<packageid />\n\t\t</emoticongift>\n\t\t<emoticonshared>\n\t\t\t<packageflag>0</packageflag>\n\t\t\t<packageid />\n\t\t</emoticonshared>\n\t\t<designershared>\n\t\t\t<designeruin>0</designeruin>\n\t\t\t<designername>null</designername>\n\t\t\t<designerrediretcturl><![CDATA[null]]></designerrediretcturl>\n\t\t</designershared>\n\t\t<emotionpageshared>\n\t\t\t<tid>0</tid>\n\t\t\t<title>null</title>\n\t\t\t<desc>null</desc>\n\t\t\t<iconUrl><![CDATA[null]]></iconUrl>\n\t\t\t<secondUrl>null</secondUrl>\n\t\t\t<pageType>0</pageType>\n\t\t\t<setKey>null</setKey>\n\t\t</emotionpageshared>\n\t\t<webviewshared>\n\t\t\t<shareUrlOriginal />\n\t\t\t<shareUrlOpen />\n\t\t\t<jsAppId />\n\t\t\t<publisherId />\n\t\t\t<publisherReqId />\n\t\t</webviewshared>\n\t\t<template_id />\n\t\t<md5 />\n\t\t<websearch>\n\t\t\t<rec_category>0</rec_category>\n\t\t\t<channelId>0</channelId>\n\t\t</websearch>\n\t\t<weappinfo>\n\t\t\t<username />\n\t\t\t<appid />\n\t\t\t<appservicetype>0</appservicetype>\n\t\t\t<secflagforsinglepagemode>0</secflagforsinglepagemode>\n\t\t\t<videopageinfo>\n\t\t\t\t<thumbwidth>0</thumbwidth>\n\t\t\t\t<thumbheight>0</thumbheight>\n\t\t\t\t<fromopensdk>0</fromopensdk>\n\t\t\t</videopageinfo>\n\t\t</weappinfo>\n\t\t<statextstr />\n\t\t<musicShareItem>\n\t\t\t<musicDuration>0</musicDuration>\n\t\t</musicShareItem>\n\t\t<finderLiveProductShare>\n\t\t\t<finderLiveID><![CDATA[]]></finderLiveID>\n\t\t\t<finderUsername><![CDATA[]]></finderUsername>\n\t\t\t<finderObjectID><![CDATA[]]></finderObjectID>\n\t\t\t<finderNonceID><![CDATA[]]></finderNonceID>\n\t\t\t<liveStatus><![CDATA[]]></liveStatus>\n\t\t\t<appId><![CDATA[]]></appId>\n\t\t\t<pagePath><![CDATA[]]></pagePath>\n\t\t\t<productId><![CDATA[]]></productId>\n\t\t\t<coverUrl><![CDATA[]]></coverUrl>\n\t\t\t<productTitle><![CDATA[]]></productTitle>\n\t\t\t<marketPrice><![CDATA[0]]></marketPrice>\n\t\t\t<sellingPrice><![CDATA[0]]></sellingPrice>\n\t\t\t<platformHeadImg><![CDATA[]]></platformHeadImg>\n\t\t\t<platformName><![CDATA[]]></platformName>\n\t\t\t<shopWindowId><![CDATA[]]></shopWindowId>\n\t\t\t<flashSalePrice><![CDATA[0]]></flashSalePrice>\n\t\t\t<flashSaleEndTime><![CDATA[0]]></flashSaleEndTime>\n\t\t\t<ecSource><![CDATA[]]></ecSource>\n\t\t\t<sellingPriceWording><![CDATA[]]></sellingPriceWording>\n\t\t\t<platformIconURL><![CDATA[]]></platformIconURL>\n\t\t\t<firstProductTagURL><![CDATA[]]></firstProductTagURL>\n\t\t\t<firstProductTagAspectRatioString><![CDATA[0.0]]></firstProductTagAspectRatioString>\n\t\t\t<secondProductTagURL><![CDATA[]]></secondProductTagURL>\n\t\t\t<secondProductTagAspectRatioString><![CDATA[0.0]]></secondProductTagAspectRatioString>\n\t\t\t<firstGuaranteeWording><![CDATA[]]></firstGuaranteeWording>\n\t\t\t<secondGuaranteeWording><![CDATA[]]></secondGuaranteeWording>\n\t\t\t<thirdGuaranteeWording><![CDATA[]]></thirdGuaranteeWording>\n\t\t\t<isPriceBeginShow>false</isPriceBeginShow>\n\t\t\t<lastGMsgID><![CDATA[]]></lastGMsgID>\n\t\t\t<promoterKey><![CDATA[]]></promoterKey>\n\t\t\t<discountWording><![CDATA[]]></discountWording>\n\t\t\t<priceSuffixDescription><![CDATA[]]></priceSuffixDescription>\n\t\t\t<productCardKey><![CDATA[]]></productCardKey>\n\t\t\t<isWxShop><![CDATA[]]></isWxShop>\n\t\t\t<brandIconUrl><![CDATA[]]></brandIconUrl>\n\t\t\t<showBoxItemStringList />\n\t\t</finderLiveProductShare>\n\t\t<finderOrder>\n\t\t\t<appID><![CDATA[]]></appID>\n\t\t\t<orderID><![CDATA[]]></orderID>\n\t\t\t<path><![CDATA[]]></path>\n\t\t\t<priceWording><![CDATA[]]></priceWording>\n\t\t\t<stateWording><![CDATA[]]></stateWording>\n\t\t\t<productImageURL><![CDATA[]]></productImageURL>\n\t\t\t<products><![CDATA[]]></products>\n\t\t\t<productsCount><![CDATA[0]]></productsCount>\n\t\t\t<orderType><![CDATA[0]]></orderType>\n\t\t\t<newPriceWording><![CDATA[]]></newPriceWording>\n\t\t\t<newStateWording><![CDATA[]]></newStateWording>\n\t\t\t<useNewWording><![CDATA[0]]></useNewWording>\n\t\t</finderOrder>\n\t\t<finderShopWindowShare>\n\t\t\t<finderUsername><![CDATA[]]></finderUsername>\n\t\t\t<avatar><![CDATA[]]></avatar>\n\t\t\t<nickname><![CDATA[]]></nickname>\n\t\t\t<commodityInStockCount><![CDATA[]]></commodityInStockCount>\n\t\t\t<appId><![CDATA[]]></appId>\n\t\t\t<path><![CDATA[]]></path>\n\t\t\t<appUsername><![CDATA[]]></appUsername>\n\t\t\t<query><![CDATA[]]></query>\n\t\t\t<liteAppId><![CDATA[]]></liteAppId>\n\t\t\t<liteAppPath><![CDATA[]]></liteAppPath>\n\t\t\t<liteAppQuery><![CDATA[]]></liteAppQuery>\n\t\t\t<platformTagURL><![CDATA[]]></platformTagURL>\n\t\t\t<saleWording><![CDATA[]]></saleWording>\n\t\t\t<lastGMsgID><![CDATA[]]></lastGMsgID>\n\t\t\t<profileTypeWording><![CDATA[]]></profileTypeWording>\n\t\t\t<saleWordingExtra><![CDATA[]]></saleWordingExtra>\n\t\t\t<isWxShop><![CDATA[]]></isWxShop>\n\t\t\t<platformIconUrl><![CDATA[]]></platformIconUrl>\n\t\t\t<brandIconUrl><![CDATA[]]></brandIconUrl>\n\t\t\t<description><![CDATA[]]></description>\n\t\t\t<backgroundUrl><![CDATA[]]></backgroundUrl>\n\t\t\t<darkModePlatformIconUrl><![CDATA[]]></darkModePlatformIconUrl>\n\t\t\t<reputationInfo>\n\t\t\t\t<hasReputationInfo>0</hasReputationInfo>\n\t\t\t\t<reputationScore>0</reputationScore>\n\t\t\t\t<reputationWording />\n\t\t\t\t<reputationTextColor />\n\t\t\t\t<reputationLevelWording />\n\t\t\t\t<reputationBackgroundColor />\n\t\t\t</reputationInfo>\n\t\t\t<productImageURLList />\n\t\t</finderShopWindowShare>\n\t\t<findernamecard>\n\t\t\t<username />\n\t\t\t<avatar><![CDATA[]]></avatar>\n\t\t\t<nickname />\n\t\t\t<auth_job />\n\t\t\t<auth_icon>0</auth_icon>\n\t\t\t<auth_icon_url />\n\t\t\t<ecSource><![CDATA[]]></ecSource>\n\t\t\t<lastGMsgID><![CDATA[]]></lastGMsgID>\n\t\t</findernamecard>\n\t\t<finderGuarantee>\n\t\t\t<scene><![CDATA[0]]></scene>\n\t\t</finderGuarantee>\n\t\t<directshare>0</directshare>\n\t\t<gamecenter>\n\t\t\t<namecard>\n\t\t\t\t<iconUrl />\n\t\t\t\t<name />\n\t\t\t\t<desc />\n\t\t\t\t<tail />\n\t\t\t\t<jumpUrl />\n\t\t\t</namecard>\n\t\t</gamecenter>\n\t\t<patMsg>\n\t\t\t<chatUser />\n\t\t\t<records>\n\t\t\t\t<recordNum>0</recordNum>\n\t\t\t</records>\n\t\t</patMsg>\n\t\t<secretmsg>\n\t\t\t<issecretmsg>0</issecretmsg>\n\t\t</secretmsg>\n\t\t<referfromscene>0</referfromscene>\n\t\t<gameshare>\n\t\t\t<liteappext>\n\t\t\t\t<liteappbizdata />\n\t\t\t\t<priority>0</priority>\n\t\t\t</liteappext>\n\t\t\t<appbrandext>\n\t\t\t\t<litegameinfo />\n\t\t\t\t<priority>-1</priority>\n\t\t\t</appbrandext>\n\t\t\t<gameshareid />\n\t\t\t<sharedata />\n\t\t\t<isvideo>0</isvideo>\n\t\t\t<duration>-1</duration>\n\t\t\t<isexposed>0</isexposed>\n\t\t\t<readtext />\n\t\t</gameshare>\n\t\t<mpsharetrace>\n\t\t\t<hasfinderelement>0</hasfinderelement>\n\t\t\t<lastgmsgid />\n\t\t</mpsharetrace>\n\t\t<wxgamecard>\n\t\t\t<framesetname />\n\t\t\t<mbcarddata />\n\t\t\t<minpkgversion />\n\t\t\t<clientextinfo />\n\t\t\t<mbcardheight>0</mbcardheight>\n\t\t\t<isoldversion>0</isoldversion>\n\t\t</wxgamecard>\n\t\t<liteapp>\n\t\t\t<id>null</id>\n\t\t\t<path />\n\t\t\t<query />\n\t\t\t<istransparent>0</istransparent>\n\t\t\t<hideicon>0</hideicon>\n\t\t</liteapp>\n\t\t<opensdk_share_is_modified>0</opensdk_share_is_modified>\n\t</appmsg>\n\t<fromusername>wxid_ubbh6q832tcs21</fromusername>\n\t<scene>0</scene>\n\t<appinfo>\n\t\t<version>1</version>\n\t\t<appname></appname>\n\t</appinfo>\n\t<commenturl></commenturl>\n</msg>\n'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1753546129, 'MsgSource': '<msgsource>\n\t<alnode>\n\t\t<fr>4</fr>\n\t</alnode>\n\t<eggIncluded>1</eggIncluded>\n\t<sec_msg_node>\n\t\t<uuid>8f36ffebb406638501284dbaf49dee5d_</uuid>\n\t\t<risk-file-flag />\n\t\t<risk-file-md5-list />\n\t</sec_msg_node>\n\t<silence>0</silence>\n\t<membercount>73</membercount>\n\t<signature>N0_V1_CJ5aXFD/|v1_qq+setJA</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'PushContent': '郭 : 识图 请输出适用于可灵ai的提示词', 'NewMsgId': 8451564921456059749, 'MsgSeq': 871402621}
2025-07-27 00:08:43 | DEBUG | 从群聊消息中提取发送者: wxid_ubbh6q832tcs21
2025-07-27 00:08:43 | DEBUG | 使用已解析的XML处理引用消息
2025-07-27 00:08:43 | INFO | 收到引用消息: 消息ID:1399833804 来自:***********@chatroom 发送人:wxid_ubbh6q832tcs21 内容:识图 请输出适用于可灵ai的提示词 引用类型:3
2025-07-27 00:08:44 | INFO | [DoubaoImageRecognition] 引用图片识图 - 提示词: '请输出适用于可灵ai的提示词'
2025-07-27 00:08:45 | INFO | [DoubaoImageRecognition] 开始豆包AI识图流程，用户: wxid_ubbh6q832tcs21, 提示词: 请输出适用于可灵ai的提示词
2025-07-27 00:08:45 | INFO | [DoubaoImageRecognition] 步骤1: 验证图片文件...
2025-07-27 00:08:46 | INFO | 发送文字消息: 对方wxid:***********@chatroom at: 内容:好的
2025-07-27 00:08:46 | INFO | [DoubaoImageRecognition] 步骤2: 调用豆包AI识图接口...
2025-07-27 00:08:46 | DEBUG | 上传认证请求状态码: 200
2025-07-27 00:08:46 | DEBUG | 上传认证响应头: {'server': 'volc-dcdn', 'content-type': 'application/json; charset=utf-8', 'transfer-encoding': 'chunked', 'connection': 'keep-alive', 'date': 'Sat, 26 Jul 2025 16:08:52 GMT', 'vary': 'Accept-Encoding', 'x-tt-logid': '20250727000852D9DBA3947E368ED629F5', 'tt_stable': '1', 'x-tt-agw-login': '1', 'server-timing': 'inner; dur=54, cdn-cache;desc=MISS, origin;dur=82, edge;dur=0, tt_agw; dur=50', 'x-tt-trace-host': '01c16e474b736ff7c4482469d1983178d02ee3a80ecfa95420c80e9064125dfafae101b99c623d797f5d830a3f8b6835c464c183cbd8456df1fab42c3a6357d68397d815576646a6ff9b782ef1dd103b9e85f47c20e668e73d442a94c7c57bbf349c6bca12cb6e57b906bc404f9399cc27', 'x-tt-trace-tag': 'id=5', 'x-tt-trace-id': '00-477e8e23030102bec4b77ea237c80000-477e8e23030102be-01', 'x-tt-timestamp': '1753546132.688', 'content-encoding': 'gzip', 'via': 'n157-114-230.cqmpct.Creative', 'x-request-ip': '**************', 'x-dsa-trace-id': '1753546132fdf9004fae9b2b7a1d7e2ba78d25f844', 'x-dsa-origin-status': '200'}
2025-07-27 00:08:46 | DEBUG | 上传认证响应内容: {"code":0,"msg":"","data":{"service_id":"a9rns2rl98","upload_path_prefix":"rc/pc/bot-chat","upload_host":"imagex.bytedanceapi.com","upload_auth_token":{"access_key":"AKTPNjFlMDAwNGRmNmMwNDk4NWJkMDFhMjQxNDQ2MjM0NjM","secret_key":"zazvEVVH15CJqpe9+YZy8WCUXS9xLH/nD26AgdM7tP7ZjnpvfOASVts9wDnd2mp4","session_token":"STS2eyJMVEFjY2Vzc0tleUlkIjoiQUtMVFlUZGhPR0ptWVRNNFl6ZG1OR1JoWVRoaE0yWTJPVFl5TW1SbU0yRmhNREEiLCJBY2Nlc3NLZXlJZCI6IkFLVFBOakZsTURBd05HUm1ObU13TkRrNE5XSmtNREZoTWpReE5EUTJNak0wTmpNIiwiU2lnbmVk
2025-07-27 00:08:52 | INFO | [DoubaoImageRecognition] 豆包AI识图成功
2025-07-27 00:08:52 | INFO | [DoubaoImageRecognition] 豆包AI识图完成
2025-07-27 00:08:53 | INFO | 发送文字消息: 对方wxid:***********@chatroom at:['wxid_ubbh6q832tcs21'] 内容:@郭 以下是适合在可灵AI生成类似风格的提示词，突出人物形象、穿搭、姿势氛围：

**中文提示词**：
一位年轻女性，棕色长发带齐刘海，穿浅米色长袖上衣（袖口有长飘带设计）、黑色短款吊带裙，搭配黑色丝袜与尖头高跟鞋，胸前挂蓝色挂绳。以跪趴姿势，单手扶头、单手撑地，背景是简洁明亮的白色纯色场景，整体风格时尚、性感且有青春感 。

**英文提示词（可灵 AI 若支持英文输入适配性更好）**：
A young woman with long brown hair and bangs, wearing a light beige long - sleeved top (with long streamers at the cuffs), a short black suspender skirt, paired with black stockings and pointed high - heels, and a blue lanyard around her chest. In a kneeling and prone posture, one hand on the head, one hand on the ground, with a simple and bright white pure color background. The overall style is fashionable, sexy and youthful.
2025-07-27 00:08:53 | INFO | [DouBaoImageToImage] 收到引用消息: 识图 请输出适用于可灵ai的提示词
2025-07-27 00:08:53 | INFO | [TimerTask] 收到引用消息调试信息:
2025-07-27 00:08:53 | INFO |   - 消息内容: 识图 请输出适用于可灵ai的提示词
2025-07-27 00:08:53 | INFO |   - 群组ID: ***********@chatroom
2025-07-27 00:08:53 | INFO |   - 发送人: wxid_ubbh6q832tcs21
2025-07-27 00:08:53 | INFO |   - 引用信息: {'MsgType': 3, 'Content': '<?xml version="1.0"?>\n<msg>\n\t<appmsg appid="" sdkver="0">\n\t\t<title>识图 请输出适用于可灵ai的提示词</title>\n\t\t<des />\n\t\t<username />\n\t\t<action>view</action>\n\t\t<type>57</type>\n\t\t<showtype>0</showtype>\n\t\t<content />\n\t\t<url />\n\t\t<lowurl />\n\t\t<forwardflag>0</forwardflag>\n\t\t<dataurl />\n\t\t<lowdataurl />\n\t\t<contentattr>0</contentattr>\n\t\t<streamvideo>\n\t\t\t<streamvideourl />\n\t\t\t<streamvideototaltime>0</streamvideototaltime>\n\t\t\t<streamvideotitle />\n\t\t\t<streamvideowording />\n\t\t\t<streamvideoweburl />\n\t\t\t<streamvideothumburl />\n\t\t\t<streamvideoaduxinfo />\n\t\t\t<streamvideopublishid />\n\t\t</streamvideo>\n\t\t<canvasPageItem>\n\t\t\t<canvasPageXml><![CDATA[]]></canvasPageXml>\n\t\t</canvasPageItem>\n\t\t<refermsg>\n\t\t\t<type>3</type>\n\t\t\t<svrid>1538239138151520393</svrid>\n\t\t\t<fromusr>***********@chatroom</fromusr>\n\t\t\t<chatusr>wxid_e3o8s2nf9u2o22</chatusr>\n\t\t\t<displayname>星空- 梦</displayname>\n\t\t\t<msgsource>&lt;msgsource&gt;\n\t&lt;alnode&gt;\n\t\t&lt;fr&gt;4&lt;/fr&gt;\n\t&lt;/alnode&gt;\n\t&lt;tmp_node&gt;\n\t\t&lt;publisher-id&gt;&lt;/publisher-id&gt;\n\t&lt;/tmp_node&gt;\n\t&lt;sec_msg_node&gt;\n\t\t&lt;uuid&gt;8f36ffebb406638501284dbaf49dee5d_&lt;/uuid&gt;\n\t\t&lt;risk-file-flag /&gt;\n\t\t&lt;risk-file-md5-list /&gt;\n\t&lt;/sec_msg_node&gt;\n\t&lt;imgmsg_pd cdnmidimgurl_size="38594" cdnmidimgurl_pd_pri="50" cdnmidimgurl_pd="1" /&gt;\n\t&lt;silence&gt;1&lt;/silence&gt;\n\t&lt;membercount&gt;73&lt;/membercount&gt;\n\t&lt;NotAutoDownloadRange&gt;20:00-22:00;00:00-01:00&lt;/NotAutoDownloadRange&gt;\n\t&lt;signature&gt;N0_V1_nLzEdMW7|v1_2Dp12/Do&lt;/signature&gt;\n&lt;/msgsource&gt;\n</msgsource>\n\t\t\t<content>&lt;?xml version="1.0"?&gt;\n&lt;msg&gt;\n\t&lt;img aeskey="41a3d2854646e80e6bcc08051a586fea" encryver="1" cdnthumbaeskey="41a3d2854646e80e6bcc08051a586fea" cdnthumburl="3057020100044b304902010002041c3acaf602032f50e60204284e8b7b02046884fd21042430626263303437342d656337662d343263302d626631372d356366643734643761663739020405290a020201000405004c4cd200" cdnthumblength="3231" cdnthumbheight="336" cdnthumbwidth="504" cdnmidheight="0" cdnmidwidth="0" cdnhdheight="0" cdnhdwidth="0" cdnmidimgurl="3057020100044b304902010002041c3acaf602032f50e60204284e8b7b02046884fd21042430626263303437342d656337662d343263302d626631372d356366643734643761663739020405290a020201000405004c4cd200" length="352908" md5="95cb3fc366019b3b42a67648f4503054" hevc_mid_size="38594"&gt;\n\t\t&lt;secHashInfoBase64&gt;eyJwaGFzaCI6IjU0YTRkMDgwMDAwMDAwMDAiLCJwZHFIYXNoIjoiYjBiM2NkMmI0ODRjMzY3NGE3\nYTMyM2IzZDlkMTk4ZDFmY2YyNmM2NTg2NjFjNjkzYzMxYmNkMzk2ZTI0MWI4ZSJ9\n&lt;/secHashInfoBase64&gt;\n\t\t&lt;live&gt;\n\t\t\t&lt;duration&gt;0&lt;/duration&gt;\n\t\t\t&lt;size&gt;0&lt;/size&gt;\n\t\t\t&lt;md5 /&gt;\n\t\t\t&lt;fileid /&gt;\n\t\t\t&lt;hdsize&gt;0&lt;/hdsize&gt;\n\t\t\t&lt;hdmd5 /&gt;\n\t\t\t&lt;hdfileid /&gt;\n\t\t\t&lt;stillimagetimems&gt;0&lt;/stillimagetimems&gt;\n\t\t&lt;/live&gt;\n\t&lt;/img&gt;\n\t&lt;platform_signature /&gt;\n\t&lt;imgdatahash /&gt;\n\t&lt;ImgSourceInfo&gt;\n\t\t&lt;ImgSourceUrl /&gt;\n\t\t&lt;BizType&gt;0&lt;/BizType&gt;\n\t&lt;/ImgSourceInfo&gt;\n&lt;/msg&gt;\n</content>\n\t\t\t<strid />\n\t\t\t<createtime>1753546017</createtime>\n\t\t</refermsg>\n\t\t<appattach>\n\t\t\t<totallen>0</totallen>\n\t\t\t<attachid />\n\t\t\t<cdnattachurl />\n\t\t\t<emoticonmd5 />\n\t\t\t<aeskey />\n\t\t\t<fileext />\n\t\t\t<islargefilemsg>0</islargefilemsg>\n\t\t</appattach>\n\t\t<extinfo />\n\t\t<androidsource>0</androidsource>\n\t\t<thumburl />\n\t\t<mediatagname />\n\t\t<messageaction><![CDATA[]]></messageaction>\n\t\t<messageext><![CDATA[]]></messageext>\n\t\t<emoticongift>\n\t\t\t<packageflag>0</packageflag>\n\t\t\t<packageid />\n\t\t</emoticongift>\n\t\t<emoticonshared>\n\t\t\t<packageflag>0</packageflag>\n\t\t\t<packageid />\n\t\t</emoticonshared>\n\t\t<designershared>\n\t\t\t<designeruin>0</designeruin>\n\t\t\t<designername>null</designername>\n\t\t\t<designerrediretcturl><![CDATA[null]]></designerrediretcturl>\n\t\t</designershared>\n\t\t<emotionpageshared>\n\t\t\t<tid>0</tid>\n\t\t\t<title>null</title>\n\t\t\t<desc>null</desc>\n\t\t\t<iconUrl><![CDATA[null]]></iconUrl>\n\t\t\t<secondUrl>null</secondUrl>\n\t\t\t<pageType>0</pageType>\n\t\t\t<setKey>null</setKey>\n\t\t</emotionpageshared>\n\t\t<webviewshared>\n\t\t\t<shareUrlOriginal />\n\t\t\t<shareUrlOpen />\n\t\t\t<jsAppId />\n\t\t\t<publisherId />\n\t\t\t<publisherReqId />\n\t\t</webviewshared>\n\t\t<template_id />\n\t\t<md5 />\n\t\t<websearch>\n\t\t\t<rec_category>0</rec_category>\n\t\t\t<channelId>0</channelId>\n\t\t</websearch>\n\t\t<weappinfo>\n\t\t\t<username />\n\t\t\t<appid />\n\t\t\t<appservicetype>0</appservicetype>\n\t\t\t<secflagforsinglepagemode>0</secflagforsinglepagemode>\n\t\t\t<videopageinfo>\n\t\t\t\t<thumbwidth>0</thumbwidth>\n\t\t\t\t<thumbheight>0</thumbheight>\n\t\t\t\t<fromopensdk>0</fromopensdk>\n\t\t\t</videopageinfo>\n\t\t</weappinfo>\n\t\t<statextstr />\n\t\t<musicShareItem>\n\t\t\t<musicDuration>0</musicDuration>\n\t\t</musicShareItem>\n\t\t<finderLiveProductShare>\n\t\t\t<finderLiveID><![CDATA[]]></finderLiveID>\n\t\t\t<finderUsername><![CDATA[]]></finderUsername>\n\t\t\t<finderObjectID><![CDATA[]]></finderObjectID>\n\t\t\t<finderNonceID><![CDATA[]]></finderNonceID>\n\t\t\t<liveStatus><![CDATA[]]></liveStatus>\n\t\t\t<appId><![CDATA[]]></appId>\n\t\t\t<pagePath><![CDATA[]]></pagePath>\n\t\t\t<productId><![CDATA[]]></productId>\n\t\t\t<coverUrl><![CDATA[]]></coverUrl>\n\t\t\t<productTitle><![CDATA[]]></productTitle>\n\t\t\t<marketPrice><![CDATA[0]]></marketPrice>\n\t\t\t<sellingPrice><![CDATA[0]]></sellingPrice>\n\t\t\t<platformHeadImg><![CDATA[]]></platformHeadImg>\n\t\t\t<platformName><![CDATA[]]></platformName>\n\t\t\t<shopWindowId><![CDATA[]]></shopWindowId>\n\t\t\t<flashSalePrice><![CDATA[0]]></flashSalePrice>\n\t\t\t<flashSaleEndTime><![CDATA[0]]></flashSaleEndTime>\n\t\t\t<ecSource><![CDATA[]]></ecSource>\n\t\t\t<sellingPriceWording><![CDATA[]]></sellingPriceWording>\n\t\t\t<platformIconURL><![CDATA[]]></platformIconURL>\n\t\t\t<firstProductTagURL><![CDATA[]]></firstProductTagURL>\n\t\t\t<firstProductTagAspectRatioString><![CDATA[0.0]]></firstProductTagAspectRatioString>\n\t\t\t<secondProductTagURL><![CDATA[]]></secondProductTagURL>\n\t\t\t<secondProductTagAspectRatioString><![CDATA[0.0]]></secondProductTagAspectRatioString>\n\t\t\t<firstGuaranteeWording><![CDATA[]]></firstGuaranteeWording>\n\t\t\t<secondGuaranteeWording><![CDATA[]]></secondGuaranteeWording>\n\t\t\t<thirdGuaranteeWording><![CDATA[]]></thirdGuaranteeWording>\n\t\t\t<isPriceBeginShow>false</isPriceBeginShow>\n\t\t\t<lastGMsgID><![CDATA[]]></lastGMsgID>\n\t\t\t<promoterKey><![CDATA[]]></promoterKey>\n\t\t\t<discountWording><![CDATA[]]></discountWording>\n\t\t\t<priceSuffixDescription><![CDATA[]]></priceSuffixDescription>\n\t\t\t<productCardKey><![CDATA[]]></productCardKey>\n\t\t\t<isWxShop><![CDATA[]]></isWxShop>\n\t\t\t<brandIconUrl><![CDATA[]]></brandIconUrl>\n\t\t\t<showBoxItemStringList />\n\t\t</finderLiveProductShare>\n\t\t<finderOrder>\n\t\t\t<appID><![CDATA[]]></appID>\n\t\t\t<orderID><![CDATA[]]></orderID>\n\t\t\t<path><![CDATA[]]></path>\n\t\t\t<priceWording><![CDATA[]]></priceWording>\n\t\t\t<stateWording><![CDATA[]]></stateWording>\n\t\t\t<productImageURL><![CDATA[]]></productImageURL>\n\t\t\t<products><![CDATA[]]></products>\n\t\t\t<productsCount><![CDATA[0]]></productsCount>\n\t\t\t<orderType><![CDATA[0]]></orderType>\n\t\t\t<newPriceWording><![CDATA[]]></newPriceWording>\n\t\t\t<newStateWording><![CDATA[]]></newStateWording>\n\t\t\t<useNewWording><![CDATA[0]]></useNewWording>\n\t\t</finderOrder>\n\t\t<finderShopWindowShare>\n\t\t\t<finderUsername><![CDATA[]]></finderUsername>\n\t\t\t<avatar><![CDATA[]]></avatar>\n\t\t\t<nickname><![CDATA[]]></nickname>\n\t\t\t<commodityInStockCount><![CDATA[]]></commodityInStockCount>\n\t\t\t<appId><![CDATA[]]></appId>\n\t\t\t<path><![CDATA[]]></path>\n\t\t\t<appUsername><![CDATA[]]></appUsername>\n\t\t\t<query><![CDATA[]]></query>\n\t\t\t<liteAppId><![CDATA[]]></liteAppId>\n\t\t\t<liteAppPath><![CDATA[]]></liteAppPath>\n\t\t\t<liteAppQuery><![CDATA[]]></liteAppQuery>\n\t\t\t<platformTagURL><![CDATA[]]></platformTagURL>\n\t\t\t<saleWording><![CDATA[]]></saleWording>\n\t\t\t<lastGMsgID><![CDATA[]]></lastGMsgID>\n\t\t\t<profileTypeWording><![CDATA[]]></profileTypeWording>\n\t\t\t<saleWordingExtra><![CDATA[]]></saleWordingExtra>\n\t\t\t<isWxShop><![CDATA[]]></isWxShop>\n\t\t\t<platformIconUrl><![CDATA[]]></platformIconUrl>\n\t\t\t<brandIconUrl><![CDATA[]]></brandIconUrl>\n\t\t\t<description><![CDATA[]]></description>\n\t\t\t<backgroundUrl><![CDATA[]]></backgroundUrl>\n\t\t\t<darkModePlatformIconUrl><![CDATA[]]></darkModePlatformIconUrl>\n\t\t\t<reputationInfo>\n\t\t\t\t<hasReputationInfo>0</hasReputationInfo>\n\t\t\t\t<reputationScore>0</reputationScore>\n\t\t\t\t<reputationWording />\n\t\t\t\t<reputationTextColor />\n\t\t\t\t<reputationLevelWording />\n\t\t\t\t<reputationBackgroundColor />\n\t\t\t</reputationInfo>\n\t\t\t<productImageURLList />\n\t\t</finderShopWindowShare>\n\t\t<findernamecard>\n\t\t\t<username />\n\t\t\t<avatar><![CDATA[]]></avatar>\n\t\t\t<nickname />\n\t\t\t<auth_job />\n\t\t\t<auth_icon>0</auth_icon>\n\t\t\t<auth_icon_url />\n\t\t\t<ecSource><![CDATA[]]></ecSource>\n\t\t\t<lastGMsgID><![CDATA[]]></lastGMsgID>\n\t\t</findernamecard>\n\t\t<finderGuarantee>\n\t\t\t<scene><![CDATA[0]]></scene>\n\t\t</finderGuarantee>\n\t\t<directshare>0</directshare>\n\t\t<gamecenter>\n\t\t\t<namecard>\n\t\t\t\t<iconUrl />\n\t\t\t\t<name />\n\t\t\t\t<desc />\n\t\t\t\t<tail />\n\t\t\t\t<jumpUrl />\n\t\t\t</namecard>\n\t\t</gamecenter>\n\t\t<patMsg>\n\t\t\t<chatUser />\n\t\t\t<records>\n\t\t\t\t<recordNum>0</recordNum>\n\t\t\t</records>\n\t\t</patMsg>\n\t\t<secretmsg>\n\t\t\t<issecretmsg>0</issecretmsg>\n\t\t</secretmsg>\n\t\t<referfromscene>0</referfromscene>\n\t\t<gameshare>\n\t\t\t<liteappext>\n\t\t\t\t<liteappbizdata />\n\t\t\t\t<priority>0</priority>\n\t\t\t</liteappext>\n\t\t\t<appbrandext>\n\t\t\t\t<litegameinfo />\n\t\t\t\t<priority>-1</priority>\n\t\t\t</appbrandext>\n\t\t\t<gameshareid />\n\t\t\t<sharedata />\n\t\t\t<isvideo>0</isvideo>\n\t\t\t<duration>-1</duration>\n\t\t\t<isexposed>0</isexposed>\n\t\t\t<readtext />\n\t\t</gameshare>\n\t\t<mpsharetrace>\n\t\t\t<hasfinderelement>0</hasfinderelement>\n\t\t\t<lastgmsgid />\n\t\t</mpsharetrace>\n\t\t<wxgamecard>\n\t\t\t<framesetname />\n\t\t\t<mbcarddata />\n\t\t\t<minpkgversion />\n\t\t\t<clientextinfo />\n\t\t\t<mbcardheight>0</mbcardheight>\n\t\t\t<isoldversion>0</isoldversion>\n\t\t</wxgamecard>\n\t\t<liteapp>\n\t\t\t<id>null</id>\n\t\t\t<path />\n\t\t\t<query />\n\t\t\t<istransparent>0</istransparent>\n\t\t\t<hideicon>0</hideicon>\n\t\t</liteapp>\n\t\t<opensdk_share_is_modified>0</opensdk_share_is_modified>\n\t</appmsg>\n\t<fromusername>wxid_ubbh6q832tcs21</fromusername>\n\t<scene>0</scene>\n\t<appinfo>\n\t\t<version>1</version>\n\t\t<appname></appname>\n\t</appinfo>\n\t<commenturl></commenturl>\n</msg>\n', 'Msgid': '1538239138151520393', 'NewMsgId': '1538239138151520393', 'ToWxid': 'wxid_4usgcju5ey9q29', 'FromWxid': '***********@chatroom', 'Nickname': '星空- 梦', 'MsgSource': '<msgsource>\n\t<alnode>\n\t\t<fr>4</fr>\n\t</alnode>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n\t<sec_msg_node>\n\t\t<uuid>8f36ffebb406638501284dbaf49dee5d_</uuid>\n\t\t<risk-file-flag />\n\t\t<risk-file-md5-list />\n\t</sec_msg_node>\n\t<imgmsg_pd cdnmidimgurl_size="38594" cdnmidimgurl_pd_pri="50" cdnmidimgurl_pd="1" />\n\t<silence>1</silence>\n\t<membercount>73</membercount>\n\t<NotAutoDownloadRange>20:00-22:00;00:00-01:00</NotAutoDownloadRange>\n\t<signature>N0_V1_nLzEdMW7|v1_2Dp12/Do</signature>\n</msgsource>\n', 'Createtime': '1753546017', 'SenderWxid': 'wxid_ubbh6q832tcs21'}
2025-07-27 00:08:53 | INFO |   - 引用消息ID: 
2025-07-27 00:08:53 | INFO |   - 引用消息类型: 
2025-07-27 00:08:53 | INFO |   - 引用消息内容: <?xml version="1.0"?>
<msg>
	<appmsg appid="" sdkver="0">
		<title>识图 请输出适用于可灵ai的提示词</title>
		<des />
		<username />
		<action>view</action>
		<type>57</type>
		<showtype>0</showtype>
		<content />
		<url />
		<lowurl />
		<forwardflag>0</forwardflag>
		<dataurl />
		<lowdataurl />
		<contentattr>0</contentattr>
		<streamvideo>
			<streamvideourl />
			<streamvideototaltime>0</streamvideototaltime>
			<streamvideotitle />
			<streamvideowording />
			<streamvideoweburl />
			<streamvideothumburl />
			<streamvideoaduxinfo />
			<streamvideopublishid />
		</streamvideo>
		<canvasPageItem>
			<canvasPageXml><![CDATA[]]></canvasPageXml>
		</canvasPageItem>
		<refermsg>
			<type>3</type>
			<svrid>1538239138151520393</svrid>
			<fromusr>***********@chatroom</fromusr>
			<chatusr>wxid_e3o8s2nf9u2o22</chatusr>
			<displayname>星空- 梦</displayname>
			<msgsource>&lt;msgsource&gt;
	&lt;alnode&gt;
		&lt;fr&gt;4&lt;/fr&gt;
	&lt;/alnode&gt;
	&lt;tmp_node&gt;
		&lt;publisher-id&gt;&lt;/publisher-id&gt;
	&lt;/tmp_node&gt;
	&lt;sec_msg_node&gt;
		&lt;uuid&gt;8f36ffebb406638501284dbaf49dee5d_&lt;/uuid&gt;
		&lt;risk-file-flag /&gt;
		&lt;risk-file-md5-list /&gt;
	&lt;/sec_msg_node&gt;
	&lt;imgmsg_pd cdnmidimgurl_size="38594" cdnmidimgurl_pd_pri="50" cdnmidimgurl_pd="1" /&gt;
	&lt;silence&gt;1&lt;/silence&gt;
	&lt;membercount&gt;73&lt;/membercount&gt;
	&lt;NotAutoDownloadRange&gt;20:00-22:00;00:00-01:00&lt;/NotAutoDownloadRange&gt;
	&lt;signature&gt;N0_V1_nLzEdMW7|v1_2Dp12/Do&lt;/signature&gt;
&lt;/msgsource&gt;
</msgsource>
			<content>&lt;?xml version="1.0"?&gt;
&lt;msg&gt;
	&lt;img aeskey="41a3d2854646e80e6bcc08051a586fea" encryver="1" cdnthumbaeskey="41a3d2854646e80e6bcc08051a586fea" cdnthumburl="3057020100044b304902010002041c3acaf602032f50e60204284e8b7b02046884fd21042430626263303437342d656337662d343263302d626631372d356366643734643761663739020405290a020201000405004c4cd200" cdnthumblength="3231" cdnthumbheight="336" cdnthumbwidth="504" cdnmidheight="0" cdnmidwidth="0" cdnhdheight="0" cdnhdwidth="0" cdnmidimgurl="3057020100044b304902010002041c3acaf602032f50e60204284e8b7b02046884fd21042430626263303437342d656337662d343263302d626631372d356366643734643761663739020405290a020201000405004c4cd200" length="352908" md5="95cb3fc366019b3b42a67648f4503054" hevc_mid_size="38594"&gt;
		&lt;secHashInfoBase64&gt;eyJwaGFzaCI6IjU0YTRkMDgwMDAwMDAwMDAiLCJwZHFIYXNoIjoiYjBiM2NkMmI0ODRjMzY3NGE3
YTMyM2IzZDlkMTk4ZDFmY2YyNmM2NTg2NjFjNjkzYzMxYmNkMzk2ZTI0MWI4ZSJ9
&lt;/secHashInfoBase64&gt;
		&lt;live&gt;
			&lt;duration&gt;0&lt;/duration&gt;
			&lt;size&gt;0&lt;/size&gt;
			&lt;md5 /&gt;
			&lt;fileid /&gt;
			&lt;hdsize&gt;0&lt;/hdsize&gt;
			&lt;hdmd5 /&gt;
			&lt;hdfileid /&gt;
			&lt;stillimagetimems&gt;0&lt;/stillimagetimems&gt;
		&lt;/live&gt;
	&lt;/img&gt;
	&lt;platform_signature /&gt;
	&lt;imgdatahash /&gt;
	&lt;ImgSourceInfo&gt;
		&lt;ImgSourceUrl /&gt;
		&lt;BizType&gt;0&lt;/BizType&gt;
	&lt;/ImgSourceInfo&gt;
&lt;/msg&gt;
</content>
			<strid />
			<createtime>1753546017</createtime>
		</refermsg>
		<appattach>
			<totallen>0</totallen>
			<attachid />
			<cdnattachurl />
			<emoticonmd5 />
			<aeskey />
			<fileext />
			<islargefilemsg>0</islargefilemsg>
		</appattach>
		<extinfo />
		<androidsource>0</androidsource>
		<thumburl />
		<mediatagname />
		<messageaction><![CDATA[]]></messageaction>
		<messageext><![CDATA[]]></messageext>
		<emoticongift>
			<packageflag>0</packageflag>
			<packageid />
		</emoticongift>
		<emoticonshared>
			<packageflag>0</packageflag>
			<packageid />
		</emoticonshared>
		<designershared>
			<designeruin>0</designeruin>
			<designername>null</designername>
			<designerrediretcturl><![CDATA[null]]></designerrediretcturl>
		</designershared>
		<emotionpageshared>
			<tid>0</tid>
			<title>null</title>
			<desc>null</desc>
			<iconUrl><![CDATA[null]]></iconUrl>
			<secondUrl>null</secondUrl>
			<pageType>0</pageType>
			<setKey>null</setKey>
		</emotionpageshared>
		<webviewshared>
			<shareUrlOriginal />
			<shareUrlOpen />
			<jsAppId />
			<publisherId />
			<publisherReqId />
		</webviewshared>
		<template_id />
		<md5 />
		<websearch>
			<rec_category>0</rec_category>
			<channelId>0</channelId>
		</websearch>
		<weappinfo>
			<username />
			<appid />
			<appservicetype>0</appservicetype>
			<secflagforsinglepagemode>0</secflagforsinglepagemode>
			<videopageinfo>
				<thumbwidth>0</thumbwidth>
				<thumbheight>0</thumbheight>
				<fromopensdk>0</fromopensdk>
			</videopageinfo>
		</weappinfo>
		<statextstr />
		<musicShareItem>
			<musicDuration>0</musicDuration>
		</musicShareItem>
		<finderLiveProductShare>
			<finderLiveID><![CDATA[]]></finderLiveID>
			<finderUsername><![CDATA[]]></finderUsername>
			<finderObjectID><![CDATA[]]></finderObjectID>
			<finderNonceID><![CDATA[]]></finderNonceID>
			<liveStatus><![CDATA[]]></liveStatus>
			<appId><![CDATA[]]></appId>
			<pagePath><![CDATA[]]></pagePath>
			<productId><![CDATA[]]></productId>
			<coverUrl><![CDATA[]]></coverUrl>
			<productTitle><![CDATA[]]></productTitle>
			<marketPrice><![CDATA[0]]></marketPrice>
			<sellingPrice><![CDATA[0]]></sellingPrice>
			<platformHeadImg><![CDATA[]]></platformHeadImg>
			<platformName><![CDATA[]]></platformName>
			<shopWindowId><![CDATA[]]></shopWindowId>
			<flashSalePrice><![CDATA[0]]></flashSalePrice>
			<flashSaleEndTime><![CDATA[0]]></flashSaleEndTime>
			<ecSource><![CDATA[]]></ecSource>
			<sellingPriceWording><![CDATA[]]></sellingPriceWording>
			<platformIconURL><![CDATA[]]></platformIconURL>
			<firstProductTagURL><![CDATA[]]></firstProductTagURL>
			<firstProductTagAspectRatioString><![CDATA[0.0]]></firstProductTagAspectRatioString>
			<secondProductTagURL><![CDATA[]]></secondProductTagURL>
			<secondProductTagAspectRatioString><![CDATA[0.0]]></secondProductTagAspectRatioString>
			<firstGuaranteeWording><![CDATA[]]></firstGuaranteeWording>
			<secondGuaranteeWording><![CDATA[]]></secondGuaranteeWording>
			<thirdGuaranteeWording><![CDATA[]]></thirdGuaranteeWording>
			<isPriceBeginShow>false</isPriceBeginShow>
			<lastGMsgID><![CDATA[]]></lastGMsgID>
			<promoterKey><![CDATA[]]></promoterKey>
			<discountWording><![CDATA[]]></discountWording>
			<priceSuffixDescription><![CDATA[]]></priceSuffixDescription>
			<productCardKey><![CDATA[]]></productCardKey>
			<isWxShop><![CDATA[]]></isWxShop>
			<brandIconUrl><![CDATA[]]></brandIconUrl>
			<showBoxItemStringList />
		</finderLiveProductShare>
		<finderOrder>
			<appID><![CDATA[]]></appID>
			<orderID><![CDATA[]]></orderID>
			<path><![CDATA[]]></path>
			<priceWording><![CDATA[]]></priceWording>
			<stateWording><![CDATA[]]></stateWording>
			<productImageURL><![CDATA[]]></productImageURL>
			<products><![CDATA[]]></products>
			<productsCount><![CDATA[0]]></productsCount>
			<orderType><![CDATA[0]]></orderType>
			<newPriceWording><![CDATA[]]></newPriceWording>
			<newStateWording><![CDATA[]]></newStateWording>
			<useNewWording><![CDATA[0]]></useNewWording>
		</finderOrder>
		<finderShopWindowShare>
			<finderUsername><![CDATA[]]></finderUsername>
			<avatar><![CDATA[]]></avatar>
			<nickname><![CDATA[]]></nickname>
			<commodityInStockCount><![CDATA[]]></commodityInStockCount>
			<appId><![CDATA[]]></appId>
			<path><![CDATA[]]></path>
			<appUsername><![CDATA[]]></appUsername>
			<query><![CDATA[]]></query>
			<liteAppId><![CDATA[]]></liteAppId>
			<liteAppPath><![CDATA[]]></liteAppPath>
			<liteAppQuery><![CDATA[]]></liteAppQuery>
			<platformTagURL><![CDATA[]]></platformTagURL>
			<saleWording><![CDATA[]]></saleWording>
			<lastGMsgID><![CDATA[]]></lastGMsgID>
			<profileTypeWording><![CDATA[]]></profileTypeWording>
			<saleWordingExtra><![CDATA[]]></saleWordingExtra>
			<isWxShop><![CDATA[]]></isWxShop>
			<platformIconUrl><![CDATA[]]></platformIconUrl>
			<brandIconUrl><![CDATA[]]></brandIconUrl>
			<description><![CDATA[]]></description>
			<backgroundUrl><![CDATA[]]></backgroundUrl>
			<darkModePlatformIconUrl><![CDATA[]]></darkModePlatformIconUrl>
			<reputationInfo>
				<hasReputationInfo>0</hasReputationInfo>
				<reputationScore>0</reputationScore>
				<reputationWording />
				<reputationTextColor />
				<reputationLevelWording />
				<reputationBackgroundColor />
			</reputationInfo>
			<productImageURLList />
		</finderShopWindowShare>
		<findernamecard>
			<username />
			<avatar><![CDATA[]]></avatar>
			<nickname />
			<auth_job />
			<auth_icon>0</auth_icon>
			<auth_icon_url />
			<ecSource><![CDATA[]]></ecSource>
			<lastGMsgID><![CDATA[]]></lastGMsgID>
		</findernamecard>
		<finderGuarantee>
			<scene><![CDATA[0]]></scene>
		</finderGuarantee>
		<directshare>0</directshare>
		<gamecenter>
			<namecard>
				<iconUrl />
				<name />
				<desc />
				<tail />
				<jumpUrl />
			</namecard>
		</gamecenter>
		<patMsg>
			<chatUser />
			<records>
				<recordNum>0</recordNum>
			</records>
		</patMsg>
		<secretmsg>
			<issecretmsg>0</issecretmsg>
		</secretmsg>
		<referfromscene>0</referfromscene>
		<gameshare>
			<liteappext>
				<liteappbizdata />
				<priority>0</priority>
			</liteappext>
			<appbrandext>
				<litegameinfo />
				<priority>-1</priority>
			</appbrandext>
			<gameshareid />
			<sharedata />
			<isvideo>0</isvideo>
			<duration>-1</duration>
			<isexposed>0</isexposed>
			<readtext />
		</gameshare>
		<mpsharetrace>
			<hasfinderelement>0</hasfinderelement>
			<lastgmsgid />
		</mpsharetrace>
		<wxgamecard>
			<framesetname />
			<mbcarddata />
			<minpkgversion />
			<clientextinfo />
			<mbcardheight>0</mbcardheight>
			<isoldversion>0</isoldversion>
		</wxgamecard>
		<liteapp>
			<id>null</id>
			<path />
			<query />
			<istransparent>0</istransparent>
			<hideicon>0</hideicon>
		</liteapp>
		<opensdk_share_is_modified>0</opensdk_share_is_modified>
	</appmsg>
	<fromusername>wxid_ubbh6q832tcs21</fromusername>
	<scene>0</scene>
	<appinfo>
		<version>1</version>
		<appname></appname>
	</appinfo>
	<commenturl></commenturl>
</msg>

2025-07-27 00:08:53 | INFO |   - 引用消息发送人: wxid_ubbh6q832tcs21
2025-07-27 00:08:57 | DEBUG | [TempFileManager] 已清理文件: temp\doubao_image_recognition\quoted_image_1753546125.jpg
2025-07-27 00:09:34 | DEBUG | 收到消息: {'MsgId': 1104907871, 'FromUserName': {'string': '***********@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 1, 'Content': {'string': 'wxid_ubbh6q832tcs21:\n可灵 生成一张写真风格画面，展现年轻女性，棕色长发配空气刘海，面容甜美、眼神明亮。身着白色长袖紧身衣，领口有深色系带，外搭黑色短吊带裙，挂蓝色挂绳，穿黑色丝袜与尖头高跟鞋 。以单膝跪地姿势呈现，一手扶头、一手撑地，微微前倾跪趴，背景纯色简洁，光线柔和明亮，营造清新时尚感  。你可根据实际需求，调整细节描述，比如发型、服饰颜色等，让生成效果更贴合预期 。'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1753546179, 'MsgSource': '<msgsource>\n\t<pua>1</pua>\n\t<alnode>\n\t\t<cf>5</cf>\n\t\t<inlenlist>176</inlenlist>\n\t</alnode>\n\t<eggIncluded>1</eggIncluded>\n\t<silence>0</silence>\n\t<membercount>73</membercount>\n\t<signature>N0_V1_Ry/Buf3H|v1_On2PZN0d</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'PushContent': '郭 : 可灵 生成一张写真风格画面，展现年轻女性，棕色长发配空气刘海...', 'NewMsgId': 4829950830873721600, 'MsgSeq': 871402627}
2025-07-27 00:09:34 | INFO | 收到文本消息: 消息ID:1104907871 来自:***********@chatroom 发送人:wxid_ubbh6q832tcs21 @:[] 内容:可灵 生成一张写真风格画面，展现年轻女性，棕色长发配空气刘海，面容甜美、眼神明亮。身着白色长袖紧身衣，领口有深色系带，外搭黑色短吊带裙，挂蓝色挂绳，穿黑色丝袜与尖头高跟鞋 。以单膝跪地姿势呈现，一手扶头、一手撑地，微微前倾跪趴，背景纯色简洁，光线柔和明亮，营造清新时尚感  。你可根据实际需求，调整细节描述，比如发型、服饰颜色等，让生成效果更贴合预期 。
2025-07-27 00:09:35 | INFO | 发送文字消息: 对方wxid:***********@chatroom at: 内容:收到
2025-07-27 00:09:37 | INFO | 发送文字消息: 对方wxid:***********@chatroom at: 内容:这个有点难
2025-07-27 00:09:37 | DEBUG | 处理消息内容: '可灵 生成一张写真风格画面，展现年轻女性，棕色长发配空气刘海，面容甜美、眼神明亮。身着白色长袖紧身衣，领口有深色系带，外搭黑色短吊带裙，挂蓝色挂绳，穿黑色丝袜与尖头高跟鞋 。以单膝跪地姿势呈现，一手扶头、一手撑地，微微前倾跪趴，背景纯色简洁，光线柔和明亮，营造清新时尚感  。你可根据实际需求，调整细节描述，比如发型、服饰颜色等，让生成效果更贴合预期 。'
2025-07-27 00:09:37 | DEBUG | 消息内容 '可灵 生成一张写真风格画面，展现年轻女性，棕色长发配空气刘海，面容甜美、眼神明亮。身着白色长袖紧身衣，领口有深色系带，外搭黑色短吊带裙，挂蓝色挂绳，穿黑色丝袜与尖头高跟鞋 。以单膝跪地姿势呈现，一手扶头、一手撑地，微微前倾跪趴，背景纯色简洁，光线柔和明亮，营造清新时尚感  。你可根据实际需求，调整细节描述，比如发型、服饰颜色等，让生成效果更贴合预期 。' 不匹配任何命令，忽略
2025-07-27 00:09:57 | DEBUG | 收到消息: {'MsgId': 540567407, 'FromUserName': {'string': '***********@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 1, 'Content': {'string': 'wxid_ubbh6q832tcs21:\n豆包 生成一张写真风格画面，展现年轻女性，棕色长发配空气刘海，面容甜美、眼神明亮。身着白色长袖紧身衣，领口有深色系带，外搭黑色短吊带裙，挂蓝色挂绳，穿黑色丝袜与尖头高跟鞋 。以单膝跪地姿势呈现，一手扶头、一手撑地，微微前倾跪趴，背景纯色简洁，光线柔和明亮，营造清新时尚感  。你可根据实际需求，调整细节描述，比如发型、服饰颜色等，让生成效果更贴合预期 。'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1753546202, 'MsgSource': '<msgsource>\n\t<pua>1</pua>\n\t<alnode>\n\t\t<cf>5</cf>\n\t\t<inlenlist>177</inlenlist>\n\t</alnode>\n\t<eggIncluded>1</eggIncluded>\n\t<silence>0</silence>\n\t<membercount>73</membercount>\n\t<signature>N0_V1_PC41Zlsr|v1_6OcYDRLd</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'PushContent': '郭 : 豆包 生成一张写真风格画面，展现年轻女性，棕色长发配空气刘海...', 'NewMsgId': 8816440123511304755, 'MsgSeq': 871402633}
2025-07-27 00:09:57 | INFO | 收到文本消息: 消息ID:540567407 来自:***********@chatroom 发送人:wxid_ubbh6q832tcs21 @:[] 内容:豆包 生成一张写真风格画面，展现年轻女性，棕色长发配空气刘海，面容甜美、眼神明亮。身着白色长袖紧身衣，领口有深色系带，外搭黑色短吊带裙，挂蓝色挂绳，穿黑色丝袜与尖头高跟鞋 。以单膝跪地姿势呈现，一手扶头、一手撑地，微微前倾跪趴，背景纯色简洁，光线柔和明亮，营造清新时尚感  。你可根据实际需求，调整细节描述，比如发型、服饰颜色等，让生成效果更贴合预期 。
2025-07-27 00:09:58 | DEBUG | 处理消息内容: '豆包 生成一张写真风格画面，展现年轻女性，棕色长发配空气刘海，面容甜美、眼神明亮。身着白色长袖紧身衣，领口有深色系带，外搭黑色短吊带裙，挂蓝色挂绳，穿黑色丝袜与尖头高跟鞋 。以单膝跪地姿势呈现，一手扶头、一手撑地，微微前倾跪趴，背景纯色简洁，光线柔和明亮，营造清新时尚感  。你可根据实际需求，调整细节描述，比如发型、服饰颜色等，让生成效果更贴合预期 。'
2025-07-27 00:09:58 | DEBUG | 消息内容 '豆包 生成一张写真风格画面，展现年轻女性，棕色长发配空气刘海，面容甜美、眼神明亮。身着白色长袖紧身衣，领口有深色系带，外搭黑色短吊带裙，挂蓝色挂绳，穿黑色丝袜与尖头高跟鞋 。以单膝跪地姿势呈现，一手扶头、一手撑地，微微前倾跪趴，背景纯色简洁，光线柔和明亮，营造清新时尚感  。你可根据实际需求，调整细节描述，比如发型、服饰颜色等，让生成效果更贴合预期 。' 不匹配任何命令，忽略
2025-07-27 00:11:21 | DEBUG | 收到消息: {'MsgId': 575989736, 'FromUserName': {'string': '***********@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 1, 'Content': {'string': 'wxid_ubbh6q832tcs21:\n豆包 画一张写真风格画面，展现年轻女性，棕色长发配空气刘海，面容甜美、眼神明亮。身着白色长袖紧身衣，领口有深色系带，外搭黑色短吊带裙，挂蓝色挂绳，穿黑色丝袜与尖头高跟鞋 。以单膝跪地姿势呈现，一手扶头、一手撑地，微微前倾跪趴，背景纯色简洁，光线柔和明亮，营造清新时尚感  。你可根据实际需求，调整细节描述，比如发型、服饰颜色等，让生成效果更贴合预期 。'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1753546286, 'MsgSource': '<msgsource>\n\t<pua>1</pua>\n\t<alnode>\n\t\t<cf>5</cf>\n\t\t<inlenlist>177</inlenlist>\n\t</alnode>\n\t<eggIncluded>1</eggIncluded>\n\t<silence>0</silence>\n\t<membercount>73</membercount>\n\t<signature>N0_V1_9EF8JDws|v1_csyiUMcT</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'PushContent': '郭 : 豆包 画一张写真风格画面，展现年轻女性，棕色长发配空气刘海，...', 'NewMsgId': 8409357934603896451, 'MsgSeq': 871402634}
2025-07-27 00:11:21 | INFO | 收到文本消息: 消息ID:575989736 来自:***********@chatroom 发送人:wxid_ubbh6q832tcs21 @:[] 内容:豆包 画一张写真风格画面，展现年轻女性，棕色长发配空气刘海，面容甜美、眼神明亮。身着白色长袖紧身衣，领口有深色系带，外搭黑色短吊带裙，挂蓝色挂绳，穿黑色丝袜与尖头高跟鞋 。以单膝跪地姿势呈现，一手扶头、一手撑地，微微前倾跪趴，背景纯色简洁，光线柔和明亮，营造清新时尚感  。你可根据实际需求，调整细节描述，比如发型、服饰颜色等，让生成效果更贴合预期 。
2025-07-27 00:11:22 | DEBUG | 处理消息内容: '豆包 画一张写真风格画面，展现年轻女性，棕色长发配空气刘海，面容甜美、眼神明亮。身着白色长袖紧身衣，领口有深色系带，外搭黑色短吊带裙，挂蓝色挂绳，穿黑色丝袜与尖头高跟鞋 。以单膝跪地姿势呈现，一手扶头、一手撑地，微微前倾跪趴，背景纯色简洁，光线柔和明亮，营造清新时尚感  。你可根据实际需求，调整细节描述，比如发型、服饰颜色等，让生成效果更贴合预期 。'
2025-07-27 00:11:22 | DEBUG | 消息内容 '豆包 画一张写真风格画面，展现年轻女性，棕色长发配空气刘海，面容甜美、眼神明亮。身着白色长袖紧身衣，领口有深色系带，外搭黑色短吊带裙，挂蓝色挂绳，穿黑色丝袜与尖头高跟鞋 。以单膝跪地姿势呈现，一手扶头、一手撑地，微微前倾跪趴，背景纯色简洁，光线柔和明亮，营造清新时尚感  。你可根据实际需求，调整细节描述，比如发型、服饰颜色等，让生成效果更贴合预期 。' 不匹配任何命令，忽略
2025-07-27 00:12:03 | DEBUG | 收到消息: {'MsgId': 1346186560, 'FromUserName': {'string': '47442567074@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 1, 'Content': {'string': 'wxid_v0rl6sh68r9u22:\n@瑶瑶\u2005画个美女'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1753546328, 'MsgSource': '<msgsource>\n\t<atuserlist><![CDATA[wxid_4usgcju5ey9q29]]></atuserlist>\n\t<pua>1</pua>\n\t<eggIncluded>1</eggIncluded>\n\t<silence>0</silence>\n\t<membercount>494</membercount>\n\t<signature>N0_V1_tTchb+n6|v1_8tAobkvl</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'PushContent': '自强不息在群聊中@了你', 'NewMsgId': 3101055470375536124, 'MsgSeq': 871402635}
2025-07-27 00:12:03 | INFO | 收到被@消息: 消息ID:1346186560 来自:47442567074@chatroom 发送人:wxid_v0rl6sh68r9u22 @:['wxid_4usgcju5ey9q29'] 内容:@瑶瑶 画个美女
2025-07-27 00:12:16 | DEBUG | 收到消息: {'MsgId': 1829851275, 'FromUserName': {'string': '***********@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 1, 'Content': {'string': 'wxid_ubbh6q832tcs21:\n噶了呀'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1753546341, 'MsgSource': '<msgsource>\n\t<pua>1</pua>\n\t<eggIncluded>1</eggIncluded>\n\t<silence>0</silence>\n\t<membercount>73</membercount>\n\t<signature>N0_V1_kh2kez27|v1_nS0fg7sA</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'PushContent': '郭 : 噶了呀', 'NewMsgId': 2522156159220958765, 'MsgSeq': 871402636}
2025-07-27 00:12:16 | INFO | 收到文本消息: 消息ID:1829851275 来自:***********@chatroom 发送人:wxid_ubbh6q832tcs21 @:[] 内容:噶了呀
2025-07-27 00:12:16 | DEBUG | 处理消息内容: '噶了呀'
2025-07-27 00:12:16 | DEBUG | 消息内容 '噶了呀' 不匹配任何命令，忽略
2025-07-27 00:12:28 | DEBUG | 收到消息: {'MsgId': 1148466054, 'FromUserName': {'string': '47442567074@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 1, 'Content': {'string': 'wxid_iemewc0wrqyk22:\n✅ 拦截到 [自强不息] 撤回的消息\n消息内容: @瑶瑶\u2005画个美女'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1753546353, 'MsgSource': '<msgsource>\n\t<silence>0</silence>\n\t<membercount>494</membercount>\n\t<signature>N0_V1_n58Yq3lX|v1_Gl3XWHjt</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'PushContent': 'NGCBot : ✅ 拦截到 [自强不息] 撤回的消息\n消息内容: @瑶瑶\u2005画个美女', 'NewMsgId': 6625905464928504545, 'MsgSeq': 871402637}
2025-07-27 00:12:28 | INFO | 收到文本消息: 消息ID:1148466054 来自:47442567074@chatroom 发送人:wxid_iemewc0wrqyk22 @:[] 内容:✅ 拦截到 [自强不息] 撤回的消息
消息内容: @瑶瑶 画个美女
2025-07-27 00:12:28 | DEBUG | 处理消息内容: '✅ 拦截到 [自强不息] 撤回的消息
消息内容: @瑶瑶 画个美女'
2025-07-27 00:12:28 | DEBUG | 消息内容 '✅ 拦截到 [自强不息] 撤回的消息
消息内容: @瑶瑶 画个美女' 不匹配任何命令，忽略
2025-07-27 00:12:35 | DEBUG | 收到消息: {'MsgId': 1168389157, 'FromUserName': {'string': '47442567074@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 10002, 'Content': {'string': 'wxid_v0rl6sh68r9u22:\n<sysmsg type="revokemsg"><revokemsg><session>47442567074@chatroom</session><msgid>630805169</msgid><newmsgid>3101055470375536124</newmsgid><replacemsg><![CDATA["自强不息" 撤回了一条消息]]></replacemsg><announcement_id><![CDATA[]]></announcement_id></revokemsg></sysmsg>'}, 'Status': 4, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1753546349, 'MsgSource': '<msgsource>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'NewMsgId': 1006354660952673604, 'MsgSeq': 871402638}
2025-07-27 00:12:35 | DEBUG | 系统消息类型: revokemsg
2025-07-27 00:12:35 | INFO | 未知的系统消息类型: {'MsgId': 1168389157, 'ToWxid': 'wxid_4usgcju5ey9q29', 'MsgType': 10002, 'Content': '\n<sysmsg type="revokemsg"><revokemsg><session>47442567074@chatroom</session><msgid>630805169</msgid><newmsgid>3101055470375536124</newmsgid><replacemsg><![CDATA["自强不息" 撤回了一条消息]]></replacemsg><announcement_id><![CDATA[]]></announcement_id></revokemsg></sysmsg>', 'Status': 4, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1753546349, 'MsgSource': '<msgsource>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'NewMsgId': 1006354660952673604, 'MsgSeq': 871402638, 'FromWxid': '47442567074@chatroom', 'IsGroup': True, 'SenderWxid': 'wxid_v0rl6sh68r9u22'}
2025-07-27 00:14:32 | INFO | [TempFileManager] 开始清理临时文件...
2025-07-27 00:14:33 | DEBUG | 开始检查群成员变化，当前监控 2 个群
2025-07-27 00:14:34 | DEBUG | 群成员变化检查完成
2025-07-27 00:44:32 | INFO | [TempFileManager] 开始清理临时文件...
2025-07-27 01:14:32 | INFO | [TempFileManager] 开始清理临时文件...
2025-07-27 01:14:33 | DEBUG | 开始检查群成员变化，当前监控 2 个群
2025-07-27 01:14:34 | DEBUG | 群成员变化检查完成
2025-07-27 01:44:32 | INFO | [TempFileManager] 开始清理临时文件...
2025-07-27 02:14:32 | INFO | [TempFileManager] 开始清理临时文件...
2025-07-27 02:14:33 | DEBUG | 开始检查群成员变化，当前监控 2 个群
2025-07-27 02:14:34 | DEBUG | 群成员变化检查完成
2025-07-27 02:44:32 | INFO | [TempFileManager] 开始清理临时文件...
2025-07-27 03:14:32 | INFO | [TempFileManager] 开始清理临时文件...
2025-07-27 03:14:33 | DEBUG | 开始检查群成员变化，当前监控 2 个群
2025-07-27 03:14:34 | DEBUG | 群成员变化检查完成
2025-07-27 03:44:32 | INFO | [TempFileManager] 开始清理临时文件...
2025-07-27 04:14:32 | INFO | [TempFileManager] 开始清理临时文件...
2025-07-27 04:14:33 | DEBUG | 开始检查群成员变化，当前监控 2 个群
2025-07-27 04:14:34 | DEBUG | 群成员变化检查完成
2025-07-27 04:24:00 | DEBUG | 收到消息: {'MsgId': 1611946146, 'FromUserName': {'string': '***********@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 1, 'Content': {'string': 'wxid_wlnzvr8ivgd422:\n睡醒了'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1753561446, 'MsgSource': '<msgsource>\n\t<pua>1</pua>\n\t<eggIncluded>1</eggIncluded>\n\t<silence>0</silence>\n\t<membercount>73</membercount>\n\t<signature>N0_V1_pnvEMjIV|v1_OBL0IDss</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'PushContent': '锦岚 : 睡醒了', 'NewMsgId': 5731147160656354541, 'MsgSeq': 871402640}
2025-07-27 04:24:00 | INFO | 收到文本消息: 消息ID:1611946146 来自:***********@chatroom 发送人:wxid_wlnzvr8ivgd422 @:[] 内容:睡醒了
2025-07-27 04:24:01 | DEBUG | 处理消息内容: '睡醒了'
2025-07-27 04:24:01 | DEBUG | 消息内容 '睡醒了' 不匹配任何命令，忽略
2025-07-27 04:24:06 | DEBUG | 收到消息: {'MsgId': 18420118, 'FromUserName': {'string': '***********@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 1, 'Content': {'string': 'wxid_wlnzvr8ivgd422:\n再睡'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1753561452, 'MsgSource': '<msgsource>\n\t<pua>1</pua>\n\t<eggIncluded>1</eggIncluded>\n\t<silence>0</silence>\n\t<membercount>73</membercount>\n\t<signature>N0_V1_WhM5K8ng|v1_kPlWBOC8</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'PushContent': '锦岚 : 再睡', 'NewMsgId': 3818772755417429046, 'MsgSeq': 871402641}
2025-07-27 04:24:06 | INFO | 收到文本消息: 消息ID:18420118 来自:***********@chatroom 发送人:wxid_wlnzvr8ivgd422 @:[] 内容:再睡
2025-07-27 04:24:06 | DEBUG | 处理消息内容: '再睡'
2025-07-27 04:24:06 | DEBUG | 消息内容 '再睡' 不匹配任何命令，忽略
2025-07-27 04:24:13 | DEBUG | 收到消息: {'MsgId': 1358234430, 'FromUserName': {'string': '***********@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 1, 'Content': {'string': 'wxid_wlnzvr8ivgd422:\n晚安'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1753561459, 'MsgSource': '<msgsource>\n\t<pua>1</pua>\n\t<eggIncluded>1</eggIncluded>\n\t<silence>0</silence>\n\t<membercount>73</membercount>\n\t<signature>N0_V1_LBQRpnLD|v1_S190y8kx</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'PushContent': '锦岚 : 晚安', 'NewMsgId': 4935823375369363271, 'MsgSeq': 871402642}
2025-07-27 04:24:13 | INFO | 收到文本消息: 消息ID:1358234430 来自:***********@chatroom 发送人:wxid_wlnzvr8ivgd422 @:[] 内容:晚安
2025-07-27 04:24:13 | DEBUG | 处理消息内容: '晚安'
2025-07-27 04:24:13 | DEBUG | 消息内容 '晚安' 不匹配任何命令，忽略
2025-07-27 04:44:32 | INFO | [TempFileManager] 开始清理临时文件...
2025-07-27 05:14:32 | INFO | [TempFileManager] 开始清理临时文件...
2025-07-27 05:14:33 | DEBUG | 开始检查群成员变化，当前监控 2 个群
2025-07-27 05:14:34 | DEBUG | 群成员变化检查完成
2025-07-27 05:44:32 | INFO | [TempFileManager] 开始清理临时文件...
2025-07-27 06:14:32 | INFO | [TempFileManager] 开始清理临时文件...
2025-07-27 06:14:33 | DEBUG | 开始检查群成员变化，当前监控 2 个群
2025-07-27 06:14:34 | DEBUG | 群成员变化检查完成
2025-07-27 06:34:40 | DEBUG | 收到消息: {'MsgId': 1753569060, 'FromUserName': {'string': 'newsapp'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 10002, 'Content': {'string': '<?xml version="1.0"?>\n<sysmsg type="functionmsg">\n\t<functionmsg>\n\t\t<cgi>/cgi-bin/micromsg-bin/addtxnewsmsg</cgi>\n\t\t<cmdid>825</cmdid>\n\t\t<businessid>50001</businessid>\n\t\t<functionmsgid>2025072700</functionmsgid>\n\t\t<op>0</op>\n\t\t<version>1753569004</version>\n\t\t<retryinterval>150</retryinterval>\n\t\t<reportid>63162</reportid>\n\t\t<successkey>0</successkey>\n\t\t<failkey>1</failkey>\n\t\t<finalfailkey>2</finalfailkey>\n\t\t<custombuff>CAAQ\nAzjtrZXEBkDsrZXEBkikrpXEBlAA</custombuff>\n\t\t<retrycount>3</retrycount>\n\t</functionmsg>\n</sysmsg>\n'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1753569283, 'NewMsgId': 1753569060, 'MsgSeq': 0}
2025-07-27 06:34:40 | DEBUG | 系统消息类型: functionmsg
2025-07-27 06:34:40 | INFO | 未知的系统消息类型: {'MsgId': 1753569060, 'ToWxid': 'wxid_4usgcju5ey9q29', 'MsgType': 10002, 'Content': '<?xml version="1.0"?>\n<sysmsg type="functionmsg">\n\t<functionmsg>\n\t\t<cgi>/cgi-bin/micromsg-bin/addtxnewsmsg</cgi>\n\t\t<cmdid>825</cmdid>\n\t\t<businessid>50001</businessid>\n\t\t<functionmsgid>2025072700</functionmsgid>\n\t\t<op>0</op>\n\t\t<version>1753569004</version>\n\t\t<retryinterval>150</retryinterval>\n\t\t<reportid>63162</reportid>\n\t\t<successkey>0</successkey>\n\t\t<failkey>1</failkey>\n\t\t<finalfailkey>2</finalfailkey>\n\t\t<custombuff>CAAQ\nAzjtrZXEBkDsrZXEBkikrpXEBlAA</custombuff>\n\t\t<retrycount>3</retrycount>\n\t</functionmsg>\n</sysmsg>\n', 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1753569283, 'NewMsgId': 1753569060, 'MsgSeq': 0, 'FromWxid': 'newsapp', 'SenderWxid': 'newsapp', 'IsGroup': False}
2025-07-27 06:37:31 | DEBUG | 收到消息: {'MsgId': 2029656382, 'FromUserName': {'string': '***********@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 1, 'Content': {'string': 'zuoledd:\n@锦岚\u2005早'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1753569457, 'MsgSource': '<msgsource>\n\t<atuserlist>wxid_wlnzvr8ivgd422</atuserlist>\n\t<bizflag>0</bizflag>\n\t<pua>1</pua>\n\t<eggIncluded>1</eggIncluded>\n\t<silence>0</silence>\n\t<membercount>73</membercount>\n\t<signature>N0_V1_xcHPrB8Z|v1_JLVUiXnG</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'PushContent': '作乐多端 : @锦岚\u2005早', 'NewMsgId': 4667516000684997900, 'MsgSeq': 871402643}
2025-07-27 06:37:31 | INFO | 收到文本消息: 消息ID:2029656382 来自:***********@chatroom 发送人:zuoledd @:['wxid_wlnzvr8ivgd422'] 内容:@锦岚 早
2025-07-27 06:37:31 | DEBUG | 处理消息内容: '@锦岚 早'
2025-07-27 06:37:31 | DEBUG | 消息内容 '@锦岚 早' 不匹配任何命令，忽略
2025-07-27 06:38:07 | DEBUG | 收到消息: {'MsgId': 1147904320, 'FromUserName': {'string': '***********@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 3, 'Content': {'string': 'zll953369865:\n<?xml version="1.0"?>\n<msg>\n\t<img aeskey="47620b46edcd43f5f3aa3bfa96cb898e" encryver="1" cdnthumbaeskey="47620b46edcd43f5f3aa3bfa96cb898e" cdnthumburl="3057020100044b30490201000204d801834002032f559502047041f7df0204688557dc042435356637643261332d663166302d343535372d623531352d336364636265633562626462020405152a010201000405004c511d00" cdnthumblength="2677" cdnthumbheight="468" cdnthumbwidth="472" cdnmidheight="0" cdnmidwidth="0" cdnhdheight="0" cdnhdwidth="0" cdnmidimgurl="3057020100044b30490201000204d801834002032f559502047041f7df0204688557dc042435356637643261332d663166302d343535372d623531352d336364636265633562626462020405152a010201000405004c511d00" length="53268" cdnbigimgurl="3057020100044b30490201000204d801834002032f559502047041f7df0204688557dc042435356637643261332d663166302d343535372d623531352d336364636265633562626462020405152a010201000405004c511d00" hdlength="118756" md5="4168f35b74fa5c857817f9681a6d822a" hevc_mid_size="16551" originsourcemd5="f6baf274bd68a00739bf30bea981bcbc">\n\t\t<secHashInfoBase64>eyJwaGFzaCI6IjEwMDAwMDAwMDAwMDAwMDAiLCJwZHFIYXNoIjoiNzk5OGU2NWNhNmQ4MWI3NGVl\nNzQ2ZGI2OWU3ODk2NmM1OTY2YTc5OWE3OTk1ODQ5NjE4YmUxYTU5YTAzODIwNyJ9\n</secHashInfoBase64>\n\t\t<live>\n\t\t\t<duration>0</duration>\n\t\t\t<size>0</size>\n\t\t\t<md5 />\n\t\t\t<fileid />\n\t\t\t<hdsize>0</hdsize>\n\t\t\t<hdmd5 />\n\t\t\t<hdfileid />\n\t\t\t<stillimagetimems>0</stillimagetimems>\n\t\t</live>\n\t</img>\n\t<platform_signature />\n\t<imgdatahash />\n\t<ImgSourceInfo>\n\t\t<ImgSourceUrl />\n\t\t<BizType>0</BizType>\n\t</ImgSourceInfo>\n</msg>\n'}, 'Status': 3, 'ImgStatus': 2, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1753569493, 'MsgSource': '<msgsource>\n\t<sec_msg_node>\n\t\t<uuid>412cd030b1647fe8f82dff1c81e0627b_</uuid>\n\t\t<risk-file-flag />\n\t\t<risk-file-md5-list />\n\t</sec_msg_node>\n\t<silence>0</silence>\n\t<membercount>73</membercount>\n\t<signature>N0_V1_dg0ciIOy|v1_zYPKsBfs</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'PushContent': '【微信红包】收到一个微信红包在群聊中发了一张图片', 'NewMsgId': 3819505901696246460, 'MsgSeq': 871402644}
2025-07-27 06:38:07 | INFO | 收到图片消息: 消息ID:1147904320 来自:***********@chatroom 发送人:zll953369865 XML:<?xml version="1.0"?><msg><img aeskey="47620b46edcd43f5f3aa3bfa96cb898e" encryver="1" cdnthumbaeskey="47620b46edcd43f5f3aa3bfa96cb898e" cdnthumburl="3057020100044b30490201000204d801834002032f559502047041f7df0204688557dc042435356637643261332d663166302d343535372d623531352d336364636265633562626462020405152a010201000405004c511d00" cdnthumblength="2677" cdnthumbheight="468" cdnthumbwidth="472" cdnmidheight="0" cdnmidwidth="0" cdnhdheight="0" cdnhdwidth="0" cdnmidimgurl="3057020100044b30490201000204d801834002032f559502047041f7df0204688557dc042435356637643261332d663166302d343535372d623531352d336364636265633562626462020405152a010201000405004c511d00" length="53268" cdnbigimgurl="3057020100044b30490201000204d801834002032f559502047041f7df0204688557dc042435356637643261332d663166302d343535372d623531352d336364636265633562626462020405152a010201000405004c511d00" hdlength="118756" md5="4168f35b74fa5c857817f9681a6d822a" hevc_mid_size="16551" originsourcemd5="f6baf274bd68a00739bf30bea981bcbc"><secHashInfoBase64>eyJwaGFzaCI6IjEwMDAwMDAwMDAwMDAwMDAiLCJwZHFIYXNoIjoiNzk5OGU2NWNhNmQ4MWI3NGVlNzQ2ZGI2OWU3ODk2NmM1OTY2YTc5OWE3OTk1ODQ5NjE4YmUxYTU5YTAzODIwNyJ9</secHashInfoBase64><live><duration>0</duration><size>0</size><md5 /><fileid /><hdsize>0</hdsize><hdmd5 /><hdfileid /><stillimagetimems>0</stillimagetimems></live></img><platform_signature /><imgdatahash /><ImgSourceInfo><ImgSourceUrl /><BizType>0</BizType></ImgSourceInfo></msg>
2025-07-27 06:38:07 | INFO | [ImageEcho] 保存图片信息成功，当前群 ***********@chatroom 已存储 5 张图片
2025-07-27 06:38:07 | INFO | [TimerTask] 缓存图片消息: 1147904320
2025-07-27 06:38:23 | DEBUG | 收到消息: {'MsgId': 64695319, 'FromUserName': {'string': '***********@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 1, 'Content': {'string': 'zll953369865:\n减肥成功'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1753569509, 'MsgSource': '<msgsource>\n\t<pua>1</pua>\n\t<eggIncluded>1</eggIncluded>\n\t<silence>0</silence>\n\t<membercount>73</membercount>\n\t<signature>N0_V1_esg08MVg|v1_f5siIEJG</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'PushContent': '【微信红包】收到一个微信红包 : 减肥成功', 'NewMsgId': 5480852583860669371, 'MsgSeq': 871402645}
2025-07-27 06:38:23 | INFO | 收到文本消息: 消息ID:64695319 来自:***********@chatroom 发送人:zll953369865 @:[] 内容:减肥成功
2025-07-27 06:38:23 | DEBUG | 处理消息内容: '减肥成功'
2025-07-27 06:38:23 | DEBUG | 消息内容 '减肥成功' 不匹配任何命令，忽略
2025-07-27 06:38:27 | DEBUG | 收到消息: {'MsgId': 1234963134, 'FromUserName': {'string': '***********@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 1, 'Content': {'string': 'zll953369865:\n低于120'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1753569513, 'MsgSource': '<msgsource>\n\t<pua>1</pua>\n\t<eggIncluded>1</eggIncluded>\n\t<silence>0</silence>\n\t<membercount>73</membercount>\n\t<signature>N0_V1_0PYiAMI9|v1_XI/PLoEc</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'PushContent': '【微信红包】收到一个微信红包 : 低于120', 'NewMsgId': 7912846506717206399, 'MsgSeq': 871402646}
2025-07-27 06:38:27 | INFO | 收到文本消息: 消息ID:1234963134 来自:***********@chatroom 发送人:zll953369865 @:[] 内容:低于120
2025-07-27 06:38:28 | DEBUG | 处理消息内容: '低于120'
2025-07-27 06:38:28 | DEBUG | 消息内容 '低于120' 不匹配任何命令，忽略
2025-07-27 06:39:00 | DEBUG | 收到消息: {'MsgId': 966185814, 'FromUserName': {'string': '***********@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 1, 'Content': {'string': 'wxid_7o4g44suf20t22:\n喝两口水上去了'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1753569546, 'MsgSource': '<msgsource>\n\t<pua>1</pua>\n\t<eggIncluded>1</eggIncluded>\n\t<silence>0</silence>\n\t<membercount>73</membercount>\n\t<signature>N0_V1_okq+RqJV|v1_FXnoe7GN</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'PushContent': '与其，不惹 : 喝两口水上去了', 'NewMsgId': 1064864143803490795, 'MsgSeq': 871402647}
2025-07-27 06:39:00 | INFO | 收到文本消息: 消息ID:966185814 来自:***********@chatroom 发送人:wxid_7o4g44suf20t22 @:[] 内容:喝两口水上去了
2025-07-27 06:39:00 | DEBUG | 处理消息内容: '喝两口水上去了'
2025-07-27 06:39:00 | DEBUG | 消息内容 '喝两口水上去了' 不匹配任何命令，忽略
2025-07-27 06:42:58 | DEBUG | 收到消息: {'MsgId': 2017548838, 'FromUserName': {'string': '***********@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 49, 'Content': {'string': 'wxid_wlnzvr8ivgd422:\n<?xml version="1.0"?>\n<msg>\n\t<appmsg appid="" sdkver="0">\n\t\t<title>早</title>\n\t\t<des />\n\t\t<username />\n\t\t<action>view</action>\n\t\t<type>57</type>\n\t\t<showtype>0</showtype>\n\t\t<content />\n\t\t<url />\n\t\t<lowurl />\n\t\t<forwardflag>0</forwardflag>\n\t\t<dataurl />\n\t\t<lowdataurl />\n\t\t<contentattr>0</contentattr>\n\t\t<streamvideo>\n\t\t\t<streamvideourl />\n\t\t\t<streamvideototaltime>0</streamvideototaltime>\n\t\t\t<streamvideotitle />\n\t\t\t<streamvideowording />\n\t\t\t<streamvideoweburl />\n\t\t\t<streamvideothumburl />\n\t\t\t<streamvideoaduxinfo />\n\t\t\t<streamvideopublishid />\n\t\t</streamvideo>\n\t\t<canvasPageItem>\n\t\t\t<canvasPageXml><![CDATA[]]></canvasPageXml>\n\t\t</canvasPageItem>\n\t\t<refermsg>\n\t\t\t<type>1</type>\n\t\t\t<svrid>4667516000684997900</svrid>\n\t\t\t<fromusr>***********@chatroom</fromusr>\n\t\t\t<chatusr>zuoledd</chatusr>\n\t\t\t<displayname>作乐多端</displayname>\n\t\t\t<msgsource>&lt;msgsource&gt;\n\t&lt;atuserlist&gt;wxid_wlnzvr8ivgd422&lt;/atuserlist&gt;\n\t&lt;bizflag&gt;0&lt;/bizflag&gt;\n\t&lt;pua&gt;1&lt;/pua&gt;\n\t&lt;eggIncluded&gt;1&lt;/eggIncluded&gt;\n\t&lt;silence&gt;1&lt;/silence&gt;\n\t&lt;membercount&gt;73&lt;/membercount&gt;\n\t&lt;signature&gt;N0_V1_wcT0XnFN|v1_62owxEqC&lt;/signature&gt;\n\t&lt;tmp_node&gt;\n\t\t&lt;publisher-id&gt;&lt;/publisher-id&gt;\n\t&lt;/tmp_node&gt;\n&lt;/msgsource&gt;\n</msgsource>\n\t\t\t<content>\n@锦岚\u2005早</content>\n\t\t\t<strid />\n\t\t\t<createtime>1753569457</createtime>\n\t\t</refermsg>\n\t\t<appattach>\n\t\t\t<totallen>0</totallen>\n\t\t\t<attachid />\n\t\t\t<cdnattachurl />\n\t\t\t<emoticonmd5 />\n\t\t\t<aeskey />\n\t\t\t<fileext />\n\t\t\t<islargefilemsg>0</islargefilemsg>\n\t\t</appattach>\n\t\t<extinfo />\n\t\t<androidsource>0</androidsource>\n\t\t<thumburl />\n\t\t<mediatagname />\n\t\t<messageaction><![CDATA[]]></messageaction>\n\t\t<messageext><![CDATA[]]></messageext>\n\t\t<emoticongift>\n\t\t\t<packageflag>0</packageflag>\n\t\t\t<packageid />\n\t\t</emoticongift>\n\t\t<emoticonshared>\n\t\t\t<packageflag>0</packageflag>\n\t\t\t<packageid />\n\t\t</emoticonshared>\n\t\t<designershared>\n\t\t\t<designeruin>0</designeruin>\n\t\t\t<designername>null</designername>\n\t\t\t<designerrediretcturl><![CDATA[null]]></designerrediretcturl>\n\t\t</designershared>\n\t\t<emotionpageshared>\n\t\t\t<tid>0</tid>\n\t\t\t<title>null</title>\n\t\t\t<desc>null</desc>\n\t\t\t<iconUrl><![CDATA[null]]></iconUrl>\n\t\t\t<secondUrl>null</secondUrl>\n\t\t\t<pageType>0</pageType>\n\t\t\t<setKey>null</setKey>\n\t\t</emotionpageshared>\n\t\t<webviewshared>\n\t\t\t<shareUrlOriginal />\n\t\t\t<shareUrlOpen />\n\t\t\t<jsAppId />\n\t\t\t<publisherId />\n\t\t\t<publisherReqId />\n\t\t</webviewshared>\n\t\t<template_id />\n\t\t<md5 />\n\t\t<websearch>\n\t\t\t<rec_category>0</rec_category>\n\t\t\t<channelId>0</channelId>\n\t\t</websearch>\n\t\t<weappinfo>\n\t\t\t<username />\n\t\t\t<appid />\n\t\t\t<appservicetype>0</appservicetype>\n\t\t\t<secflagforsinglepagemode>0</secflagforsinglepagemode>\n\t\t\t<videopageinfo>\n\t\t\t\t<thumbwidth>0</thumbwidth>\n\t\t\t\t<thumbheight>0</thumbheight>\n\t\t\t\t<fromopensdk>0</fromopensdk>\n\t\t\t</videopageinfo>\n\t\t</weappinfo>\n\t\t<statextstr />\n\t\t<musicShareItem>\n\t\t\t<musicDuration>0</musicDuration>\n\t\t</musicShareItem>\n\t\t<finderLiveProductShare>\n\t\t\t<finderLiveID><![CDATA[]]></finderLiveID>\n\t\t\t<finderUsername><![CDATA[]]></finderUsername>\n\t\t\t<finderObjectID><![CDATA[]]></finderObjectID>\n\t\t\t<finderNonceID><![CDATA[]]></finderNonceID>\n\t\t\t<liveStatus><![CDATA[]]></liveStatus>\n\t\t\t<appId><![CDATA[]]></appId>\n\t\t\t<pagePath><![CDATA[]]></pagePath>\n\t\t\t<productId><![CDATA[]]></productId>\n\t\t\t<coverUrl><![CDATA[]]></coverUrl>\n\t\t\t<productTitle><![CDATA[]]></productTitle>\n\t\t\t<marketPrice><![CDATA[0]]></marketPrice>\n\t\t\t<sellingPrice><![CDATA[0]]></sellingPrice>\n\t\t\t<platformHeadImg><![CDATA[]]></platformHeadImg>\n\t\t\t<platformName><![CDATA[]]></platformName>\n\t\t\t<shopWindowId><![CDATA[]]></shopWindowId>\n\t\t\t<flashSalePrice><![CDATA[0]]></flashSalePrice>\n\t\t\t<flashSaleEndTime><![CDATA[0]]></flashSaleEndTime>\n\t\t\t<ecSource><![CDATA[]]></ecSource>\n\t\t\t<sellingPriceWording><![CDATA[]]></sellingPriceWording>\n\t\t\t<platformIconURL><![CDATA[]]></platformIconURL>\n\t\t\t<firstProductTagURL><![CDATA[]]></firstProductTagURL>\n\t\t\t<firstProductTagAspectRatioString><![CDATA[0.0]]></firstProductTagAspectRatioString>\n\t\t\t<secondProductTagURL><![CDATA[]]></secondProductTagURL>\n\t\t\t<secondProductTagAspectRatioString><![CDATA[0.0]]></secondProductTagAspectRatioString>\n\t\t\t<firstGuaranteeWording><![CDATA[]]></firstGuaranteeWording>\n\t\t\t<secondGuaranteeWording><![CDATA[]]></secondGuaranteeWording>\n\t\t\t<thirdGuaranteeWording><![CDATA[]]></thirdGuaranteeWording>\n\t\t\t<isPriceBeginShow>false</isPriceBeginShow>\n\t\t\t<lastGMsgID><![CDATA[]]></lastGMsgID>\n\t\t\t<promoterKey><![CDATA[]]></promoterKey>\n\t\t\t<discountWording><![CDATA[]]></discountWording>\n\t\t\t<priceSuffixDescription><![CDATA[]]></priceSuffixDescription>\n\t\t\t<productCardKey><![CDATA[]]></productCardKey>\n\t\t\t<isWxShop><![CDATA[]]></isWxShop>\n\t\t\t<brandIconUrl><![CDATA[]]></brandIconUrl>\n\t\t\t<rIconUrl><![CDATA[]]></rIconUrl>\n\t\t\t<rIconUrlDarkMode><![CDATA[]]></rIconUrlDarkMode>\n\t\t\t<topShopIconUrl><![CDATA[]]></topShopIconUrl>\n\t\t\t<topShopIconUrlDarkMode><![CDATA[]]></topShopIconUrlDarkMode>\n\t\t\t<simplifyTopShopIconUrl><![CDATA[]]></simplifyTopShopIconUrl>\n\t\t\t<simplifyTopShopIconUrlDarkmode><![CDATA[]]></simplifyTopShopIconUrlDarkmode>\n\t\t\t<topShopIconWidth><![CDATA[0]]></topShopIconWidth>\n\t\t\t<topShopIconHeight><![CDATA[0]]></topShopIconHeight>\n\t\t\t<simplifyTopShopIconWidth><![CDATA[0]]></simplifyTopShopIconWidth>\n\t\t\t<simplifyTopShopIconHeight><![CDATA[0]]></simplifyTopShopIconHeight>\n\t\t\t<showBoxItemStringList />\n\t\t</finderLiveProductShare>\n\t\t<finderOrder>\n\t\t\t<appID><![CDATA[]]></appID>\n\t\t\t<orderID><![CDATA[]]></orderID>\n\t\t\t<path><![CDATA[]]></path>\n\t\t\t<priceWording><![CDATA[]]></priceWording>\n\t\t\t<stateWording><![CDATA[]]></stateWording>\n\t\t\t<productImageURL><![CDATA[]]></productImageURL>\n\t\t\t<products><![CDATA[]]></products>\n\t\t\t<productsCount><![CDATA[0]]></productsCount>\n\t\t\t<orderType><![CDATA[0]]></orderType>\n\t\t\t<newPriceWording><![CDATA[]]></newPriceWording>\n\t\t\t<newStateWording><![CDATA[]]></newStateWording>\n\t\t\t<useNewWording><![CDATA[0]]></useNewWording>\n\t\t</finderOrder>\n\t\t<finderShopWindowShare>\n\t\t\t<finderUsername><![CDATA[]]></finderUsername>\n\t\t\t<avatar><![CDATA[]]></avatar>\n\t\t\t<nickname><![CDATA[]]></nickname>\n\t\t\t<commodityInStockCount><![CDATA[]]></commodityInStockCount>\n\t\t\t<appId><![CDATA[]]></appId>\n\t\t\t<path><![CDATA[]]></path>\n\t\t\t<appUsername><![CDATA[]]></appUsername>\n\t\t\t<query><![CDATA[]]></query>\n\t\t\t<liteAppId><![CDATA[]]></liteAppId>\n\t\t\t<liteAppPath><![CDATA[]]></liteAppPath>\n\t\t\t<liteAppQuery><![CDATA[]]></liteAppQuery>\n\t\t\t<platformTagURL><![CDATA[]]></platformTagURL>\n\t\t\t<saleWording><![CDATA[]]></saleWording>\n\t\t\t<lastGMsgID><![CDATA[]]></lastGMsgID>\n\t\t\t<profileTypeWording><![CDATA[]]></profileTypeWording>\n\t\t\t<saleWordingExtra><![CDATA[]]></saleWordingExtra>\n\t\t\t<isWxShop><![CDATA[]]></isWxShop>\n\t\t\t<platformIconUrl><![CDATA[]]></platformIconUrl>\n\t\t\t<brandIconUrl><![CDATA[]]></brandIconUrl>\n\t\t\t<description><![CDATA[]]></description>\n\t\t\t<backgroundUrl><![CDATA[]]></backgroundUrl>\n\t\t\t<darkModePlatformIconUrl><![CDATA[]]></darkModePlatformIconUrl>\n\t\t\t<rIconUrl><![CDATA[]]></rIconUrl>\n\t\t\t<rIconUrlDarkMode><![CDATA[]]></rIconUrlDarkMode>\n\t\t\t<rWords><![CDATA[]]></rWords>\n\t\t\t<topShopIconUrl><![CDATA[]]></topShopIconUrl>\n\t\t\t<topShopIconUrlDarkMode><![CDATA[]]></topShopIconUrlDarkMode>\n\t\t\t<simplifyTopShopIconUrl><![CDATA[]]></simplifyTopShopIconUrl>\n\t\t\t<simplifyTopShopIconUrlDarkmode><![CDATA[]]></simplifyTopShopIconUrlDarkmode>\n\t\t\t<topShopIconWidth><![CDATA[0]]></topShopIconWidth>\n\t\t\t<topShopIconHeight><![CDATA[0]]></topShopIconHeight>\n\t\t\t<simplifyTopShopIconWidth><![CDATA[0]]></simplifyTopShopIconWidth>\n\t\t\t<simplifyTopShopIconHeight><![CDATA[0]]></simplifyTopShopIconHeight>\n\t\t\t<reputationInfo>\n\t\t\t\t<hasReputationInfo>0</hasReputationInfo>\n\t\t\t\t<reputationScore>0</reputationScore>\n\t\t\t\t<reputationWording />\n\t\t\t\t<reputationTextColor />\n\t\t\t\t<reputationLevelWording />\n\t\t\t\t<reputationBackgroundColor />\n\t\t\t</reputationInfo>\n\t\t\t<productImageURLList />\n\t\t</finderShopWindowShare>\n\t\t<findernamecard>\n\t\t\t<username />\n\t\t\t<avatar><![CDATA[]]></avatar>\n\t\t\t<nickname />\n\t\t\t<auth_job />\n\t\t\t<auth_icon>0</auth_icon>\n\t\t\t<auth_icon_url />\n\t\t\t<ecSource><![CDATA[]]></ecSource>\n\t\t\t<lastGMsgID><![CDATA[]]></lastGMsgID>\n\t\t</findernamecard>\n\t\t<finderGuarantee>\n\t\t\t<scene><![CDATA[0]]></scene>\n\t\t</finderGuarantee>\n\t\t<directshare>0</directshare>\n\t\t<gamecenter>\n\t\t\t<namecard>\n\t\t\t\t<iconUrl />\n\t\t\t\t<name />\n\t\t\t\t<desc />\n\t\t\t\t<tail />\n\t\t\t\t<jumpUrl />\n\t\t\t\t<liteappId />\n\t\t\t\t<liteappPath />\n\t\t\t\t<liteappQuery />\n\t\t\t\t<liteappMinVersion />\n\t\t\t</namecard>\n\t\t</gamecenter>\n\t\t<patMsg>\n\t\t\t<chatUser />\n\t\t\t<records>\n\t\t\t\t<recordNum>0</recordNum>\n\t\t\t</records>\n\t\t</patMsg>\n\t\t<secretmsg>\n\t\t\t<issecretmsg>0</issecretmsg>\n\t\t</secretmsg>\n\t\t<referfromscene>0</referfromscene>\n\t\t<gameshare>\n\t\t\t<liteappext>\n\t\t\t\t<liteappbizdata />\n\t\t\t\t<priority>0</priority>\n\t\t\t</liteappext>\n\t\t\t<appbrandext>\n\t\t\t\t<litegameinfo />\n\t\t\t\t<priority>-1</priority>\n\t\t\t</appbrandext>\n\t\t\t<gameshareid />\n\t\t\t<sharedata />\n\t\t\t<isvideo>0</isvideo>\n\t\t\t<duration>-1</duration>\n\t\t\t<isexposed>0</isexposed>\n\t\t\t<readtext />\n\t\t</gameshare>\n\t\t<tingChatRoomItem>\n\t\t\t<type>0</type>\n\t\t\t<categoryItem>null</categoryItem>\n\t\t\t<categoryId />\n\t\t</tingChatRoomItem>\n\t\t<mpsharetrace>\n\t\t\t<hasfinderelement>0</hasfinderelement>\n\t\t\t<lastgmsgid />\n\t\t</mpsharetrace>\n\t\t<wxgamecard>\n\t\t\t<framesetname />\n\t\t\t<mbcarddata />\n\t\t\t<minpkgversion />\n\t\t\t<clientextinfo />\n\t\t\t<mbcardheight>0</mbcardheight>\n\t\t\t<isoldversion>0</isoldversion>\n\t\t</wxgamecard>\n\t\t<ecskfcard>\n\t\t\t<framesetname />\n\t\t\t<mbcarddata />\n\t\t\t<minupdateunixtimestamp>0</minupdateunixtimestamp>\n\t\t\t<needheader>false</needheader>\n\t\t\t<summary />\n\t\t</ecskfcard>\n\t\t<liteapp>\n\t\t\t<id>null</id>\n\t\t\t<path />\n\t\t\t<query />\n\t\t\t<istransparent>0</istransparent>\n\t\t\t<hideicon>0</hideicon>\n\t\t\t<forbidforward>0</forbidforward>\n\t\t</liteapp>\n\t\t<opensdk_share_is_modified>0</opensdk_share_is_modified>\n\t</appmsg>\n\t<fromusername>wxid_wlnzvr8ivgd422</fromusername>\n\t<scene>0</scene>\n\t<appinfo>\n\t\t<version>1</version>\n\t\t<appname></appname>\n\t</appinfo>\n\t<commenturl></commenturl>\n</msg>\n'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1753569784, 'MsgSource': '<msgsource>\n\t<alnode>\n\t\t<fr>4</fr>\n\t</alnode>\n\t<eggIncluded>1</eggIncluded>\n\t<sec_msg_node>\n\t\t<uuid>2c33a07f888fb5342e724310a22ea2fe_</uuid>\n\t\t<risk-file-flag />\n\t\t<risk-file-md5-list />\n\t</sec_msg_node>\n\t<silence>0</silence>\n\t<membercount>73</membercount>\n\t<signature>N0_V1_1Rwz76Hm|v1_Hq2Etc1U</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'PushContent': '锦岚 : 早', 'NewMsgId': 2405960820342322799, 'MsgSeq': 871402648}
2025-07-27 06:42:58 | DEBUG | 从群聊消息中提取发送者: wxid_wlnzvr8ivgd422
2025-07-27 06:42:58 | DEBUG | 使用已解析的XML处理引用消息
2025-07-27 06:42:58 | INFO | 收到引用消息: 消息ID:2017548838 来自:***********@chatroom 发送人:wxid_wlnzvr8ivgd422 内容:早 引用类型:1
2025-07-27 06:42:58 | INFO | [DouBaoImageToImage] 收到引用消息: 早
2025-07-27 06:42:58 | INFO | [TimerTask] 收到引用消息调试信息:
2025-07-27 06:42:58 | INFO |   - 消息内容: 早
2025-07-27 06:42:58 | INFO |   - 群组ID: ***********@chatroom
2025-07-27 06:42:58 | INFO |   - 发送人: wxid_wlnzvr8ivgd422
2025-07-27 06:42:58 | INFO |   - 引用信息: {'MsgType': 1, 'Content': '\n@锦岚\u2005早', 'Msgid': '4667516000684997900', 'NewMsgId': '4667516000684997900', 'ToWxid': 'wxid_4usgcju5ey9q29', 'FromWxid': '***********@chatroom', 'Nickname': '作乐多端', 'MsgSource': '<msgsource>\n\t<atuserlist>wxid_wlnzvr8ivgd422</atuserlist>\n\t<bizflag>0</bizflag>\n\t<pua>1</pua>\n\t<eggIncluded>1</eggIncluded>\n\t<silence>1</silence>\n\t<membercount>73</membercount>\n\t<signature>N0_V1_wcT0XnFN|v1_62owxEqC</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'Createtime': '1753569457', 'SenderWxid': 'wxid_wlnzvr8ivgd422'}
2025-07-27 06:42:58 | INFO |   - 引用消息ID: 
2025-07-27 06:42:58 | INFO |   - 引用消息类型: 
2025-07-27 06:42:58 | INFO |   - 引用消息内容: 
@锦岚 早
2025-07-27 06:42:58 | INFO |   - 引用消息发送人: wxid_wlnzvr8ivgd422
2025-07-27 06:43:01 | DEBUG | 收到消息: {'MsgId': 1522787976, 'FromUserName': {'string': '***********@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 1, 'Content': {'string': 'wxid_wlnzvr8ivgd422:\n又醒了'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1753569788, 'MsgSource': '<msgsource>\n\t<pua>1</pua>\n\t<eggIncluded>1</eggIncluded>\n\t<silence>0</silence>\n\t<membercount>73</membercount>\n\t<signature>N0_V1_wI6gGkww|v1_xKWcrVvQ</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'PushContent': '锦岚 : 又醒了', 'NewMsgId': 8103476643490938849, 'MsgSeq': 871402649}
2025-07-27 06:43:01 | INFO | 收到文本消息: 消息ID:1522787976 来自:***********@chatroom 发送人:wxid_wlnzvr8ivgd422 @:[] 内容:又醒了
2025-07-27 06:43:02 | DEBUG | 处理消息内容: '又醒了'
2025-07-27 06:43:02 | DEBUG | 消息内容 '又醒了' 不匹配任何命令，忽略
2025-07-27 06:43:06 | DEBUG | 收到消息: {'MsgId': 477197988, 'FromUserName': {'string': '***********@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 1, 'Content': {'string': 'wxid_wlnzvr8ivgd422:\n再睡'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1753569792, 'MsgSource': '<msgsource>\n\t<pua>1</pua>\n\t<eggIncluded>1</eggIncluded>\n\t<silence>0</silence>\n\t<membercount>73</membercount>\n\t<signature>N0_V1_cg6SPSwK|v1_oDXbwQ4t</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'PushContent': '锦岚 : 再睡', 'NewMsgId': 1140856301965811294, 'MsgSeq': 871402650}
2025-07-27 06:43:06 | INFO | 收到文本消息: 消息ID:477197988 来自:***********@chatroom 发送人:wxid_wlnzvr8ivgd422 @:[] 内容:再睡
2025-07-27 06:43:06 | DEBUG | 处理消息内容: '再睡'
2025-07-27 06:43:06 | DEBUG | 消息内容 '再睡' 不匹配任何命令，忽略
2025-07-27 06:44:32 | INFO | [TempFileManager] 开始清理临时文件...
2025-07-27 06:45:10 | DEBUG | 收到消息: {'MsgId': 1624141554, 'FromUserName': {'string': '***********@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 1, 'Content': {'string': 'wxid_wlnzvr8ivgd422:\n洞房'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1753569917, 'MsgSource': '<msgsource>\n\t<pua>1</pua>\n\t<eggIncluded>1</eggIncluded>\n\t<silence>0</silence>\n\t<membercount>73</membercount>\n\t<signature>N0_V1_vXYEWOpy|v1_06pg4QWd</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'PushContent': '锦岚 : 洞房', 'NewMsgId': 7292927611854441109, 'MsgSeq': 871402651}
2025-07-27 06:45:10 | INFO | 收到文本消息: 消息ID:1624141554 来自:***********@chatroom 发送人:wxid_wlnzvr8ivgd422 @:[] 内容:洞房
2025-07-27 06:45:11 | DEBUG | 处理消息内容: '洞房'
2025-07-27 06:45:11 | DEBUG | 消息内容 '洞房' 不匹配任何命令，忽略
2025-07-27 06:45:13 | DEBUG | 收到消息: {'MsgId': 1548948188, 'FromUserName': {'string': '***********@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 1, 'Content': {'string': 'wxid_q35rkzgkjvlv12:\n💕 [锦岚]👩\u200d❤\u200d👨[小爱]💕\n💓═☘︎═•洞💗房•═☘︎═💓\n👩\u200d❤\u200d👨关系：情侣\n⛺地点：阳台\n😍姿势：隔山打牛\n😮\u200d💨结果：失败\n❤\u200d🔥状态：断裂\n💓═☘︎══•💗•══☘︎═💓\n🔮魅力:-13\n[玫瑰]恩爱:-4 \n🕒下次:2025-07-27 07:05:19'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1753569918, 'MsgSource': '<msgsource>\n\t<silence>0</silence>\n\t<membercount>73</membercount>\n\t<signature>N0_V1_m3diLUkI|v1_tWJ8Uw7m</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'PushContent': '摇月 : \ue327 [锦岚]\ue005\u200d\ue022\u200d\ue004[小爱]\ue327\n\ue327═☘︎═•洞\ue328房•═☘︎═...', 'NewMsgId': 2060354803693125513, 'MsgSeq': 871402652}
2025-07-27 06:45:13 | INFO | 收到文本消息: 消息ID:1548948188 来自:***********@chatroom 发送人:wxid_q35rkzgkjvlv12 @:[] 内容:💕 [锦岚]👩‍❤‍👨[小爱]💕
💓═☘︎═•洞💗房•═☘︎═💓
👩‍❤‍👨关系：情侣
⛺地点：阳台
😍姿势：隔山打牛
😮‍💨结果：失败
❤‍🔥状态：断裂
💓═☘︎══•💗•══☘︎═💓
🔮魅力:-13
[玫瑰]恩爱:-4 
🕒下次:2025-07-27 07:05:19
2025-07-27 06:45:14 | DEBUG | 处理消息内容: '💕 [锦岚]👩‍❤‍👨[小爱]💕
💓═☘︎═•洞💗房•═☘︎═💓
👩‍❤‍👨关系：情侣
⛺地点：阳台
😍姿势：隔山打牛
😮‍💨结果：失败
❤‍🔥状态：断裂
💓═☘︎══•💗•══☘︎═💓
🔮魅力:-13
[玫瑰]恩爱:-4 
🕒下次:2025-07-27 07:05:19'
2025-07-27 06:45:14 | DEBUG | 消息内容 '💕 [锦岚]👩‍❤‍👨[小爱]💕
💓═☘︎═•洞💗房•═☘︎═💓
👩‍❤‍👨关系：情侣
⛺地点：阳台
😍姿势：隔山打牛
😮‍💨结果：失败
❤‍🔥状态：断裂
💓═☘︎══•💗•══☘︎═💓
🔮魅力:-13
[玫瑰]恩爱:-4 
🕒下次:2025-07-27 07:05:19' 不匹配任何命令，忽略
2025-07-27 06:45:15 | DEBUG | 收到消息: {'MsgId': 2084407616, 'FromUserName': {'string': '***********@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 1, 'Content': {'string': 'wxid_8l9ymg1mafud12:\n「锦岚」[爱心]「小爱」\n地点：电影院\n活动：啪啪\n结果：成功\n羞羞：简直就像吃了炫迈，根本停不下来~\n恩爱值增加200\n\n下次:2025-07-27 06:55:17'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1753569918, 'MsgSource': '<msgsource>\n\t<silence>0</silence>\n\t<membercount>73</membercount>\n\t<signature>N0_V1_RCAI/ktG|v1_fbvMzoJn</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'PushContent': '不吃香菜\ue110 : 「锦岚」[爱心]「小爱」\n地点：电影院\n活动：啪啪\n结果：成功\n羞羞...', 'NewMsgId': 634369808889019892, 'MsgSeq': 871402653}
2025-07-27 06:45:15 | INFO | 收到文本消息: 消息ID:2084407616 来自:***********@chatroom 发送人:wxid_8l9ymg1mafud12 @:[] 内容:「锦岚」[爱心]「小爱」
地点：电影院
活动：啪啪
结果：成功
羞羞：简直就像吃了炫迈，根本停不下来~
恩爱值增加200

下次:2025-07-27 06:55:17
2025-07-27 06:45:16 | DEBUG | 处理消息内容: '「锦岚」[爱心]「小爱」
地点：电影院
活动：啪啪
结果：成功
羞羞：简直就像吃了炫迈，根本停不下来~
恩爱值增加200

下次:2025-07-27 06:55:17'
2025-07-27 06:45:16 | DEBUG | 消息内容 '「锦岚」[爱心]「小爱」
地点：电影院
活动：啪啪
结果：成功
羞羞：简直就像吃了炫迈，根本停不下来~
恩爱值增加200

下次:2025-07-27 06:55:17' 不匹配任何命令，忽略
2025-07-27 06:46:48 | DEBUG | 收到消息: {'MsgId': 290242489, 'FromUserName': {'string': '27852221909@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 49, 'Content': {'string': 'wxid_2530z9t0joek22:\n<?xml version="1.0"?>\n<msg>\n\t<appmsg appid="" sdkver="0">\n\t\t<title>派对永不停Beat-1000钻石等你领取！</title>\n\t\t<des />\n\t\t<username />\n\t\t<action>view</action>\n\t\t<type>33</type>\n\t\t<showtype>0</showtype>\n\t\t<content />\n\t\t<url>https://mp.weixin.qq.com/mp/waerrpage?appid=wxa708de63ee4a2353&amp;type=upgrade&amp;upgradetype=3#wechat_redirect</url>\n\t\t<lowurl />\n\t\t<forwardflag>0</forwardflag>\n\t\t<dataurl />\n\t\t<lowdataurl />\n\t\t<contentattr>0</contentattr>\n\t\t<streamvideo>\n\t\t\t<streamvideourl />\n\t\t\t<streamvideototaltime>0</streamvideototaltime>\n\t\t\t<streamvideotitle />\n\t\t\t<streamvideowording />\n\t\t\t<streamvideoweburl />\n\t\t\t<streamvideothumburl />\n\t\t\t<streamvideoaduxinfo />\n\t\t\t<streamvideopublishid />\n\t\t</streamvideo>\n\t\t<canvasPageItem>\n\t\t\t<canvasPageXml><![CDATA[]]></canvasPageXml>\n\t\t</canvasPageItem>\n\t\t<appattach>\n\t\t\t<attachid />\n\t\t\t<cdnthumburl>3057020100044b30490201000204ef2f8dd002032f53a10204928f8cb6020468855aa8042462346135366632392d316138632d343536352d626231652d6531376665306132386235320204011808030201000405004c57c100</cdnthumburl>\n\t\t\t<cdnthumbmd5>e9e9b54db642fe6ca8c55ac56a90eee4</cdnthumbmd5>\n\t\t\t<cdnthumblength>74789</cdnthumblength>\n\t\t\t<cdnthumbheight>576</cdnthumbheight>\n\t\t\t<cdnthumbwidth>720</cdnthumbwidth>\n\t\t\t<cdnthumbaeskey>7ab98605fd8a3645ecd912ac5f1ccc79</cdnthumbaeskey>\n\t\t\t<aeskey>7ab98605fd8a3645ecd912ac5f1ccc79</aeskey>\n\t\t\t<encryver>1</encryver>\n\t\t\t<fileext />\n\t\t\t<islargefilemsg>0</islargefilemsg>\n\t\t</appattach>\n\t\t<extinfo />\n\t\t<androidsource>0</androidsource>\n\t\t<sourceusername>gh_25eb09d7bc53@app</sourceusername>\n\t\t<sourcedisplayname>唱舞星愿站</sourcedisplayname>\n\t\t<commenturl />\n\t\t<thumburl />\n\t\t<mediatagname />\n\t\t<messageaction><![CDATA[]]></messageaction>\n\t\t<messageext><![CDATA[]]></messageext>\n\t\t<emoticongift>\n\t\t\t<packageflag>0</packageflag>\n\t\t\t<packageid />\n\t\t</emoticongift>\n\t\t<emoticonshared>\n\t\t\t<packageflag>0</packageflag>\n\t\t\t<packageid />\n\t\t</emoticonshared>\n\t\t<designershared>\n\t\t\t<designeruin>0</designeruin>\n\t\t\t<designername>null</designername>\n\t\t\t<designerrediretcturl><![CDATA[null]]></designerrediretcturl>\n\t\t</designershared>\n\t\t<emotionpageshared>\n\t\t\t<tid>0</tid>\n\t\t\t<title>null</title>\n\t\t\t<desc>null</desc>\n\t\t\t<iconUrl><![CDATA[null]]></iconUrl>\n\t\t\t<secondUrl>null</secondUrl>\n\t\t\t<pageType>0</pageType>\n\t\t\t<setKey>null</setKey>\n\t\t</emotionpageshared>\n\t\t<webviewshared>\n\t\t\t<shareUrlOriginal />\n\t\t\t<shareUrlOpen />\n\t\t\t<jsAppId />\n\t\t\t<publisherId>wxapp_wxa708de63ee4a2353pages/Activity/teamUp/index.html?key=uK1I2mwn&amp;share_unionid=oA7D81ZgczVzy9lpDUGlnt5LkVxc&amp;task_id=&amp;team_id=26414</publisherId>\n\t\t\t<publisherReqId />\n\t\t</webviewshared>\n\t\t<template_id />\n\t\t<md5>e9e9b54db642fe6ca8c55ac56a90eee4</md5>\n\t\t<websearch>\n\t\t\t<rec_category>0</rec_category>\n\t\t\t<channelId>0</channelId>\n\t\t</websearch>\n\t\t<weappinfo>\n\t\t\t<pagepath><![CDATA[pages/Activity/teamUp/index.html?key=uK1I2mwn&share_unionid=oA7D81ZgczVzy9lpDUGlnt5LkVxc&task_id=&team_id=26414]]></pagepath>\n\t\t\t<username>gh_25eb09d7bc53@app</username>\n\t\t\t<appid>wxa708de63ee4a2353</appid>\n\t\t\t<version>16</version>\n\t\t\t<type>2</type>\n\t\t\t<weappiconurl><![CDATA[http://wx.qlogo.cn/mmhead/FMajU52WvbEE6SVVwqdPicO6GjWVVIhhLiaZK2NvCJgt3epAkLg7A3nkRTia23B9lSt0pXQJz0viaR4/96]]></weappiconurl>\n\t\t\t<shareId><![CDATA[1_wxa708de63ee4a2353_1dfbea7e30807954cb6f6af5833cba63_1753570013_2]]></shareId>\n\t\t\t<appservicetype>0</appservicetype>\n\t\t\t<secflagforsinglepagemode>0</secflagforsinglepagemode>\n\t\t\t<videopageinfo>\n\t\t\t\t<thumbwidth>720</thumbwidth>\n\t\t\t\t<thumbheight>576</thumbheight>\n\t\t\t\t<fromopensdk>0</fromopensdk>\n\t\t\t</videopageinfo>\n\t\t</weappinfo>\n\t\t<statextstr />\n\t\t<musicShareItem>\n\t\t\t<musicDuration>0</musicDuration>\n\t\t</musicShareItem>\n\t\t<finderLiveProductShare>\n\t\t\t<finderLiveID><![CDATA[]]></finderLiveID>\n\t\t\t<finderUsername><![CDATA[]]></finderUsername>\n\t\t\t<finderObjectID><![CDATA[]]></finderObjectID>\n\t\t\t<finderNonceID><![CDATA[]]></finderNonceID>\n\t\t\t<liveStatus><![CDATA[]]></liveStatus>\n\t\t\t<appId><![CDATA[]]></appId>\n\t\t\t<pagePath><![CDATA[]]></pagePath>\n\t\t\t<productId><![CDATA[]]></productId>\n\t\t\t<coverUrl><![CDATA[]]></coverUrl>\n\t\t\t<productTitle><![CDATA[]]></productTitle>\n\t\t\t<marketPrice><![CDATA[0]]></marketPrice>\n\t\t\t<sellingPrice><![CDATA[0]]></sellingPrice>\n\t\t\t<platformHeadImg><![CDATA[]]></platformHeadImg>\n\t\t\t<platformName><![CDATA[]]></platformName>\n\t\t\t<shopWindowId><![CDATA[]]></shopWindowId>\n\t\t\t<flashSalePrice><![CDATA[0]]></flashSalePrice>\n\t\t\t<flashSaleEndTime><![CDATA[0]]></flashSaleEndTime>\n\t\t\t<ecSource><![CDATA[]]></ecSource>\n\t\t\t<sellingPriceWording><![CDATA[]]></sellingPriceWording>\n\t\t\t<platformIconURL><![CDATA[]]></platformIconURL>\n\t\t\t<firstProductTagURL><![CDATA[]]></firstProductTagURL>\n\t\t\t<firstProductTagAspectRatioString><![CDATA[0.0]]></firstProductTagAspectRatioString>\n\t\t\t<secondProductTagURL><![CDATA[]]></secondProductTagURL>\n\t\t\t<secondProductTagAspectRatioString><![CDATA[0.0]]></secondProductTagAspectRatioString>\n\t\t\t<firstGuaranteeWording><![CDATA[]]></firstGuaranteeWording>\n\t\t\t<secondGuaranteeWording><![CDATA[]]></secondGuaranteeWording>\n\t\t\t<thirdGuaranteeWording><![CDATA[]]></thirdGuaranteeWording>\n\t\t\t<isPriceBeginShow>false</isPriceBeginShow>\n\t\t\t<lastGMsgID><![CDATA[]]></lastGMsgID>\n\t\t\t<promoterKey><![CDATA[]]></promoterKey>\n\t\t\t<discountWording><![CDATA[]]></discountWording>\n\t\t\t<priceSuffixDescription><![CDATA[]]></priceSuffixDescription>\n\t\t\t<productCardKey><![CDATA[]]></productCardKey>\n\t\t\t<isWxShop><![CDATA[]]></isWxShop>\n\t\t\t<brandIconUrl><![CDATA[]]></brandIconUrl>\n\t\t\t<rIconUrl><![CDATA[]]></rIconUrl>\n\t\t\t<rIconUrlDarkMode><![CDATA[]]></rIconUrlDarkMode>\n\t\t\t<topShopIconUrl><![CDATA[]]></topShopIconUrl>\n\t\t\t<topShopIconUrlDarkMode><![CDATA[]]></topShopIconUrlDarkMode>\n\t\t\t<simplifyTopShopIconUrl><![CDATA[]]></simplifyTopShopIconUrl>\n\t\t\t<simplifyTopShopIconUrlDarkmode><![CDATA[]]></simplifyTopShopIconUrlDarkmode>\n\t\t\t<topShopIconWidth><![CDATA[0]]></topShopIconWidth>\n\t\t\t<topShopIconHeight><![CDATA[0]]></topShopIconHeight>\n\t\t\t<simplifyTopShopIconWidth><![CDATA[0]]></simplifyTopShopIconWidth>\n\t\t\t<simplifyTopShopIconHeight><![CDATA[0]]></simplifyTopShopIconHeight>\n\t\t\t<showBoxItemStringList />\n\t\t</finderLiveProductShare>\n\t\t<finderOrder>\n\t\t\t<appID><![CDATA[]]></appID>\n\t\t\t<orderID><![CDATA[]]></orderID>\n\t\t\t<path><![CDATA[]]></path>\n\t\t\t<priceWording><![CDATA[]]></priceWording>\n\t\t\t<stateWording><![CDATA[]]></stateWording>\n\t\t\t<productImageURL><![CDATA[]]></productImageURL>\n\t\t\t<products><![CDATA[]]></products>\n\t\t\t<productsCount><![CDATA[0]]></productsCount>\n\t\t\t<orderType><![CDATA[0]]></orderType>\n\t\t\t<newPriceWording><![CDATA[]]></newPriceWording>\n\t\t\t<newStateWording><![CDATA[]]></newStateWording>\n\t\t\t<useNewWording><![CDATA[0]]></useNewWording>\n\t\t</finderOrder>\n\t\t<finderShopWindowShare>\n\t\t\t<finderUsername><![CDATA[]]></finderUsername>\n\t\t\t<avatar><![CDATA[]]></avatar>\n\t\t\t<nickname><![CDATA[]]></nickname>\n\t\t\t<commodityInStockCount><![CDATA[]]></commodityInStockCount>\n\t\t\t<appId><![CDATA[]]></appId>\n\t\t\t<path><![CDATA[]]></path>\n\t\t\t<appUsername><![CDATA[]]></appUsername>\n\t\t\t<query><![CDATA[]]></query>\n\t\t\t<liteAppId><![CDATA[]]></liteAppId>\n\t\t\t<liteAppPath><![CDATA[]]></liteAppPath>\n\t\t\t<liteAppQuery><![CDATA[]]></liteAppQuery>\n\t\t\t<platformTagURL><![CDATA[]]></platformTagURL>\n\t\t\t<saleWording><![CDATA[]]></saleWording>\n\t\t\t<lastGMsgID><![CDATA[]]></lastGMsgID>\n\t\t\t<profileTypeWording><![CDATA[]]></profileTypeWording>\n\t\t\t<saleWordingExtra><![CDATA[]]></saleWordingExtra>\n\t\t\t<isWxShop><![CDATA[]]></isWxShop>\n\t\t\t<platformIconUrl><![CDATA[]]></platformIconUrl>\n\t\t\t<brandIconUrl><![CDATA[]]></brandIconUrl>\n\t\t\t<description><![CDATA[]]></description>\n\t\t\t<backgroundUrl><![CDATA[]]></backgroundUrl>\n\t\t\t<darkModePlatformIconUrl><![CDATA[]]></darkModePlatformIconUrl>\n\t\t\t<rIconUrl><![CDATA[]]></rIconUrl>\n\t\t\t<rIconUrlDarkMode><![CDATA[]]></rIconUrlDarkMode>\n\t\t\t<rWords><![CDATA[]]></rWords>\n\t\t\t<topShopIconUrl><![CDATA[]]></topShopIconUrl>\n\t\t\t<topShopIconUrlDarkMode><![CDATA[]]></topShopIconUrlDarkMode>\n\t\t\t<simplifyTopShopIconUrl><![CDATA[]]></simplifyTopShopIconUrl>\n\t\t\t<simplifyTopShopIconUrlDarkmode><![CDATA[]]></simplifyTopShopIconUrlDarkmode>\n\t\t\t<topShopIconWidth><![CDATA[0]]></topShopIconWidth>\n\t\t\t<topShopIconHeight><![CDATA[0]]></topShopIconHeight>\n\t\t\t<simplifyTopShopIconWidth><![CDATA[0]]></simplifyTopShopIconWidth>\n\t\t\t<simplifyTopShopIconHeight><![CDATA[0]]></simplifyTopShopIconHeight>\n\t\t\t<reputationInfo>\n\t\t\t\t<hasReputationInfo>0</hasReputationInfo>\n\t\t\t\t<reputationScore>0</reputationScore>\n\t\t\t\t<reputationWording />\n\t\t\t\t<reputationTextColor />\n\t\t\t\t<reputationLevelWording />\n\t\t\t\t<reputationBackgroundColor />\n\t\t\t</reputationInfo>\n\t\t\t<productImageURLList />\n\t\t</finderShopWindowShare>\n\t\t<findernamecard>\n\t\t\t<username />\n\t\t\t<avatar><![CDATA[]]></avatar>\n\t\t\t<nickname />\n\t\t\t<auth_job />\n\t\t\t<auth_icon>0</auth_icon>\n\t\t\t<auth_icon_url />\n\t\t\t<ecSource><![CDATA[]]></ecSource>\n\t\t\t<lastGMsgID><![CDATA[]]></lastGMsgID>\n\t\t</findernamecard>\n\t\t<finderGuarantee>\n\t\t\t<scene><![CDATA[0]]></scene>\n\t\t</finderGuarantee>\n\t\t<directshare>0</directshare>\n\t\t<gamecenter>\n\t\t\t<namecard>\n\t\t\t\t<iconUrl />\n\t\t\t\t<name />\n\t\t\t\t<desc />\n\t\t\t\t<tail />\n\t\t\t\t<jumpUrl />\n\t\t\t\t<liteappId />\n\t\t\t\t<liteappPath />\n\t\t\t\t<liteappQuery />\n\t\t\t\t<liteappMinVersion />\n\t\t\t</namecard>\n\t\t</gamecenter>\n\t\t<patMsg>\n\t\t\t<chatUser />\n\t\t\t<records>\n\t\t\t\t<recordNum>0</recordNum>\n\t\t\t</records>\n\t\t</patMsg>\n\t\t<secretmsg>\n\t\t\t<issecretmsg>0</issecretmsg>\n\t\t</secretmsg>\n\t\t<referfromscene>0</referfromscene>\n\t\t<gameshare>\n\t\t\t<liteappext>\n\t\t\t\t<liteappbizdata />\n\t\t\t\t<priority>0</priority>\n\t\t\t</liteappext>\n\t\t\t<appbrandext>\n\t\t\t\t<litegameinfo />\n\t\t\t\t<priority>-1</priority>\n\t\t\t</appbrandext>\n\t\t\t<gameshareid />\n\t\t\t<sharedata />\n\t\t\t<isvideo>0</isvideo>\n\t\t\t<duration>-1</duration>\n\t\t\t<isexposed>0</isexposed>\n\t\t\t<readtext />\n\t\t</gameshare>\n\t\t<tingChatRoomItem>\n\t\t\t<type>0</type>\n\t\t\t<categoryItem>null</categoryItem>\n\t\t\t<categoryId />\n\t\t</tingChatRoomItem>\n\t\t<mpsharetrace>\n\t\t\t<hasfinderelement>0</hasfinderelement>\n\t\t\t<lastgmsgid />\n\t\t</mpsharetrace>\n\t\t<wxgamecard>\n\t\t\t<framesetname />\n\t\t\t<mbcarddata />\n\t\t\t<minpkgversion />\n\t\t\t<clientextinfo />\n\t\t\t<mbcardheight>0</mbcardheight>\n\t\t\t<isoldversion>0</isoldversion>\n\t\t</wxgamecard>\n\t\t<ecskfcard>\n\t\t\t<framesetname />\n\t\t\t<mbcarddata />\n\t\t\t<minupdateunixtimestamp>0</minupdateunixtimestamp>\n\t\t\t<needheader>false</needheader>\n\t\t\t<summary />\n\t\t</ecskfcard>\n\t\t<liteapp>\n\t\t\t<id>null</id>\n\t\t\t<path />\n\t\t\t<query />\n\t\t\t<istransparent>0</istransparent>\n\t\t\t<hideicon>0</hideicon>\n\t\t\t<forbidforward>0</forbidforward>\n\t\t</liteapp>\n\t\t<opensdk_share_is_modified>0</opensdk_share_is_modified>\n\t</appmsg>\n\t<fromusername>wxid_2530z9t0joek22</fromusername>\n\t<scene>0</scene>\n\t<appinfo>\n\t\t<version>1</version>\n\t\t<appname></appname>\n\t</appinfo>\n\t<commenturl></commenturl>\n</msg>\n'}, 'Status': 3, 'ImgStatus': 2, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1753570014, 'MsgSource': '<msgsource>\n\t<alnode>\n\t\t<fr>4</fr>\n\t</alnode>\n\t<sec_msg_node>\n\t\t<uuid>bd02cf5792933839e019ae7d7d75c9b5_</uuid>\n\t\t<risk-file-flag />\n\t\t<risk-file-md5-list />\n\t</sec_msg_node>\n\t<silence>1</silence>\n\t<membercount>145</membercount>\n\t<signature>N0_V1_P+HkWBs2|v1_xW+v17sz</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'NewMsgId': 1085392205394661108, 'MsgSeq': 871402654}
2025-07-27 06:46:48 | DEBUG | 从群聊消息中提取发送者: wxid_2530z9t0joek22
2025-07-27 06:46:48 | DEBUG | XML消息完整内容:
<?xml version="1.0"?>
<msg>
	<appmsg appid="" sdkver="0">
		<title>派对永不停Beat-1000钻石等你领取！</title>
		<des />
		<username />
		<action>view</action>
		<type>33</type>
		<showtype>0</showtype>
		<content />
		<url>https://mp.weixin.qq.com/mp/waerrpage?appid=wxa708de63ee4a2353&amp;type=upgrade&amp;upgradetype=3#wechat_redirect</url>
		<lowurl />
		<forwardflag>0</forwardflag>
		<dataurl />
		<lowdataurl />
		<contentattr>0</contentattr>
		<streamvideo>
			<streamvideourl />
			<streamvideototaltime>0</streamvideototaltime>
			<streamvideotitle />
			<streamvideowording />
			<streamvideoweburl />
			<streamvideothumburl />
			<streamvideoaduxinfo />
			<streamvideopublishid />
		</streamvideo>
		<canvasPageItem>
			<canvasPageXml><![CDATA[]]></canvasPageXml>
		</canvasPageItem>
		<appattach>
			<attachid />
			<cdnthumburl>3057020100044b30490201000204ef2f8dd002032f53a10204928f8cb6020468855aa8042462346135366632392d316138632d343536352d626231652d6531376665306132386235320204011808030201000405004c57c100</cdnthumburl>
			<cdnthumbmd5>e9e9b54db642fe6ca8c55ac56a90eee4</cdnthumbmd5>
			<cdnthumblength>74789</cdnthumblength>
			<cdnthumbheight>576</cdnthumbheight>
			<cdnthumbwidth>720</cdnthumbwidth>
			<cdnthumbaeskey>7ab98605fd8a3645ecd912ac5f1ccc79</cdnthumbaeskey>
			<aeskey>7ab98605fd8a3645ecd912ac5f1ccc79</aeskey>
			<encryver>1</encryver>
			<fileext />
			<islargefilemsg>0</islargefilemsg>
		</appattach>
		<extinfo />
		<androidsource>0</androidsource>
		<sourceusername>gh_25eb09d7bc53@app</sourceusername>
		<sourcedisplayname>唱舞星愿站</sourcedisplayname>
		<commenturl />
		<thumburl />
		<mediatagname />
		<messageaction><![CDATA[]]></messageaction>
		<messageext><![CDATA[]]></messageext>
		<emoticongift>
			<packageflag>0</packageflag>
			<packageid />
		</emoticongift>
		<emoticonshared>
			<packageflag>0</packageflag>
			<packageid />
		</emoticonshared>
		<designershared>
			<designeruin>0</designeruin>
			<designername>null</designername>
			<designerrediretcturl><![CDATA[null]]></designerrediretcturl>
		</designershared>
		<emotionpageshared>
			<tid>0</tid>
			<title>null</title>
			<desc>null</desc>
			<iconUrl><![CDATA[null]]></iconUrl>
			<secondUrl>null</secondUrl>
			<pageType>0</pageType>
			<setKey>null</setKey>
		</emotionpageshared>
		<webviewshared>
			<shareUrlOriginal />
			<shareUrlOpen />
			<jsAppId />
			<publisherId>wxapp_wxa708de63ee4a2353pages/Activity/teamUp/index.html?key=uK1I2mwn&amp;share_unionid=oA7D81ZgczVzy9lpDUGlnt5LkVxc&amp;task_id=&amp;team_id=26414</publisherId>
			<publisherReqId />
		</webviewshared>
		<template_id />
		<md5>e9e9b54db642fe6ca8c55ac56a90eee4</md5>
		<websearch>
			<rec_category>0</rec_category>
			<channelId>0</channelId>
		</websearch>
		<weappinfo>
			<pagepath><![CDATA[pages/Activity/teamUp/index.html?key=uK1I2mwn&share_unionid=oA7D81ZgczVzy9lpDUGlnt5LkVxc&task_id=&team_id=26414]]></pagepath>
			<username>gh_25eb09d7bc53@app</username>
			<appid>wxa708de63ee4a2353</appid>
			<version>16</version>
			<type>2</type>
			<weappiconurl><![CDATA[http://wx.qlogo.cn/mmhead/FMajU52WvbEE6SVVwqdPicO6GjWVVIhhLiaZK2NvCJgt3epAkLg7A3nkRTia23B9lSt0pXQJz0viaR4/96]]></weappiconurl>
			<shareId><![CDATA[1_wxa708de63ee4a2353_1dfbea7e30807954cb6f6af5833cba63_1753570013_2]]></shareId>
			<appservicetype>0</appservicetype>
			<secflagforsinglepagemode>0</secflagforsinglepagemode>
			<videopageinfo>
				<thumbwidth>720</thumbwidth>
				<thumbheight>576</thumbheight>
				<fromopensdk>0</fromopensdk>
			</videopageinfo>
		</weappinfo>
		<statextstr />
		<musicShareItem>
			<musicDuration>0</musicDuration>
		</musicShareItem>
		<finderLiveProductShare>
			<finderLiveID><![CDATA[]]></finderLiveID>
			<finderUsername><![CDATA[]]></finderUsername>
			<finderObjectID><![CDATA[]]></finderObjectID>
			<finderNonceID><![CDATA[]]></finderNonceID>
			<liveStatus><![CDATA[]]></liveStatus>
			<appId><![CDATA[]]></appId>
			<pagePath><![CDATA[]]></pagePath>
			<productId><![CDATA[]]></productId>
			<coverUrl><![CDATA[]]></coverUrl>
			<productTitle><![CDATA[]]></productTitle>
			<marketPrice><![CDATA[0]]></marketPrice>
			<sellingPrice><![CDATA[0]]></sellingPrice>
			<platformHeadImg><![CDATA[]]></platformHeadImg>
			<platformName><![CDATA[]]></platformName>
			<shopWindowId><![CDATA[]]></shopWindowId>
			<flashSalePrice><![CDATA[0]]></flashSalePrice>
			<flashSaleEndTime><![CDATA[0]]></flashSaleEndTime>
			<ecSource><![CDATA[]]></ecSource>
			<sellingPriceWording><![CDATA[]]></sellingPriceWording>
			<platformIconURL><![CDATA[]]></platformIconURL>
			<firstProductTagURL><![CDATA[]]></firstProductTagURL>
			<firstProductTagAspectRatioString><![CDATA[0.0]]></firstProductTagAspectRatioString>
			<secondProductTagURL><![CDATA[]]></secondProductTagURL>
			<secondProductTagAspectRatioString><![CDATA[0.0]]></secondProductTagAspectRatioString>
			<firstGuaranteeWording><![CDATA[]]></firstGuaranteeWording>
			<secondGuaranteeWording><![CDATA[]]></secondGuaranteeWording>
			<thirdGuaranteeWording><![CDATA[]]></thirdGuaranteeWording>
			<isPriceBeginShow>false</isPriceBeginShow>
			<lastGMsgID><![CDATA[]]></lastGMsgID>
			<promoterKey><![CDATA[]]></promoterKey>
			<discountWording><![CDATA[]]></discountWording>
			<priceSuffixDescription><![CDATA[]]></priceSuffixDescription>
			<productCardKey><![CDATA[]]></productCardKey>
			<isWxShop><![CDATA[]]></isWxShop>
			<brandIconUrl><![CDATA[]]></brandIconUrl>
			<rIconUrl><![CDATA[]]></rIconUrl>
			<rIconUrlDarkMode><![CDATA[]]></rIconUrlDarkMode>
			<topShopIconUrl><![CDATA[]]></topShopIconUrl>
			<topShopIconUrlDarkMode><![CDATA[]]></topShopIconUrlDarkMode>
			<simplifyTopShopIconUrl><![CDATA[]]></simplifyTopShopIconUrl>
			<simplifyTopShopIconUrlDarkmode><![CDATA[]]></simplifyTopShopIconUrlDarkmode>
			<topShopIconWidth><![CDATA[0]]></topShopIconWidth>
			<topShopIconHeight><![CDATA[0]]></topShopIconHeight>
			<simplifyTopShopIconWidth><![CDATA[0]]></simplifyTopShopIconWidth>
			<simplifyTopShopIconHeight><![CDATA[0]]></simplifyTopShopIconHeight>
			<showBoxItemStringList />
		</finderLiveProductShare>
		<finderOrder>
			<appID><![CDATA[]]></appID>
			<orderID><![CDATA[]]></orderID>
			<path><![CDATA[]]></path>
			<priceWording><![CDATA[]]></priceWording>
			<stateWording><![CDATA[]]></stateWording>
			<productImageURL><![CDATA[]]></productImageURL>
			<products><![CDATA[]]></products>
			<productsCount><![CDATA[0]]></productsCount>
			<orderType><![CDATA[0]]></orderType>
			<newPriceWording><![CDATA[]]></newPriceWording>
			<newStateWording><![CDATA[]]></newStateWording>
			<useNewWording><![CDATA[0]]></useNewWording>
		</finderOrder>
		<finderShopWindowShare>
			<finderUsername><![CDATA[]]></finderUsername>
			<avatar><![CDATA[]]></avatar>
			<nickname><![CDATA[]]></nickname>
			<commodityInStockCount><![CDATA[]]></commodityInStockCount>
			<appId><![CDATA[]]></appId>
			<path><![CDATA[]]></path>
			<appUsername><![CDATA[]]></appUsername>
			<query><![CDATA[]]></query>
			<liteAppId><![CDATA[]]></liteAppId>
			<liteAppPath><![CDATA[]]></liteAppPath>
			<liteAppQuery><![CDATA[]]></liteAppQuery>
			<platformTagURL><![CDATA[]]></platformTagURL>
			<saleWording><![CDATA[]]></saleWording>
			<lastGMsgID><![CDATA[]]></lastGMsgID>
			<profileTypeWording><![CDATA[]]></profileTypeWording>
			<saleWordingExtra><![CDATA[]]></saleWordingExtra>
			<isWxShop><![CDATA[]]></isWxShop>
			<platformIconUrl><![CDATA[]]></platformIconUrl>
			<brandIconUrl><![CDATA[]]></brandIconUrl>
			<description><![CDATA[]]></description>
			<backgroundUrl><![CDATA[]]></backgroundUrl>
			<darkModePlatformIconUrl><![CDATA[]]></darkModePlatformIconUrl>
			<rIconUrl><![CDATA[]]></rIconUrl>
			<rIconUrlDarkMode><![CDATA[]]></rIconUrlDarkMode>
			<rWords><![CDATA[]]></rWords>
			<topShopIconUrl><![CDATA[]]></topShopIconUrl>
			<topShopIconUrlDarkMode><![CDATA[]]></topShopIconUrlDarkMode>
			<simplifyTopShopIconUrl><![CDATA[]]></simplifyTopShopIconUrl>
			<simplifyTopShopIconUrlDarkmode><![CDATA[]]></simplifyTopShopIconUrlDarkmode>
			<topShopIconWidth><![CDATA[0]]></topShopIconWidth>
			<topShopIconHeight><![CDATA[0]]></topShopIconHeight>
			<simplifyTopShopIconWidth><![CDATA[0]]></simplifyTopShopIconWidth>
			<simplifyTopShopIconHeight><![CDATA[0]]></simplifyTopShopIconHeight>
			<reputationInfo>
				<hasReputationInfo>0</hasReputationInfo>
				<reputationScore>0</reputationScore>
				<reputationWording />
				<reputationTextColor />
				<reputationLevelWording />
				<reputationBackgroundColor />
			</reputationInfo>
			<productImageURLList />
		</finderShopWindowShare>
		<findernamecard>
			<username />
			<avatar><![CDATA[]]></avatar>
			<nickname />
			<auth_job />
			<auth_icon>0</auth_icon>
			<auth_icon_url />
			<ecSource><![CDATA[]]></ecSource>
			<lastGMsgID><![CDATA[]]></lastGMsgID>
		</findernamecard>
		<finderGuarantee>
			<scene><![CDATA[0]]></scene>
		</finderGuarantee>
		<directshare>0</directshare>
		<gamecenter>
			<namecard>
				<iconUrl />
				<name />
				<desc />
				<tail />
				<jumpUrl />
				<liteappId />
				<liteappPath />
				<liteappQuery />
				<liteappMinVersion />
			</namecard>
		</gamecenter>
		<patMsg>
			<chatUser />
			<records>
				<recordNum>0</recordNum>
			</records>
		</patMsg>
		<secretmsg>
			<issecretmsg>0</issecretmsg>
		</secretmsg>
		<referfromscene>0</referfromscene>
		<gameshare>
			<liteappext>
				<liteappbizdata />
				<priority>0</priority>
			</liteappext>
			<appbrandext>
				<litegameinfo />
				<priority>-1</priority>
			</appbrandext>
			<gameshareid />
			<sharedata />
			<isvideo>0</isvideo>
			<duration>-1</duration>
			<isexposed>0</isexposed>
			<readtext />
		</gameshare>
		<tingChatRoomItem>
			<type>0</type>
			<categoryItem>null</categoryItem>
			<categoryId />
		</tingChatRoomItem>
		<mpsharetrace>
			<hasfinderelement>0</hasfinderelement>
			<lastgmsgid />
		</mpsharetrace>
		<wxgamecard>
			<framesetname />
			<mbcarddata />
			<minpkgversion />
			<clientextinfo />
			<mbcardheight>0</mbcardheight>
			<isoldversion>0</isoldversion>
		</wxgamecard>
		<ecskfcard>
			<framesetname />
			<mbcarddata />
			<minupdateunixtimestamp>0</minupdateunixtimestamp>
			<needheader>false</needheader>
			<summary />
		</ecskfcard>
		<liteapp>
			<id>null</id>
			<path />
			<query />
			<istransparent>0</istransparent>
			<hideicon>0</hideicon>
			<forbidforward>0</forbidforward>
		</liteapp>
		<opensdk_share_is_modified>0</opensdk_share_is_modified>
	</appmsg>
	<fromusername>wxid_2530z9t0joek22</fromusername>
	<scene>0</scene>
	<appinfo>
		<version>1</version>
		<appname></appname>
	</appinfo>
	<commenturl></commenturl>
</msg>

2025-07-27 06:46:48 | DEBUG | XML消息类型: 33
2025-07-27 06:46:48 | DEBUG | XML消息标题: 派对永不停Beat-1000钻石等你领取！
2025-07-27 06:46:48 | DEBUG | XML消息描述: None
2025-07-27 06:46:48 | DEBUG | 附件信息 cdnthumburl: 3057020100044b30490201000204ef2f8dd002032f53a10204928f8cb6020468855aa8042462346135366632392d316138632d343536352d626231652d6531376665306132386235320204011808030201000405004c57c100
2025-07-27 06:46:48 | DEBUG | 附件信息 cdnthumbmd5: e9e9b54db642fe6ca8c55ac56a90eee4
2025-07-27 06:46:48 | DEBUG | 附件信息 cdnthumblength: 74789
2025-07-27 06:46:48 | DEBUG | 附件信息 cdnthumbheight: 576
2025-07-27 06:46:48 | DEBUG | 附件信息 cdnthumbwidth: 720
2025-07-27 06:46:48 | DEBUG | 附件信息 cdnthumbaeskey: 7ab98605fd8a3645ecd912ac5f1ccc79
2025-07-27 06:46:48 | DEBUG | 附件信息 aeskey: 7ab98605fd8a3645ecd912ac5f1ccc79
2025-07-27 06:46:48 | DEBUG | 附件信息 encryver: 1
2025-07-27 06:46:48 | DEBUG | 附件信息 islargefilemsg: 0
2025-07-27 06:46:48 | DEBUG | XML消息URL: https://mp.weixin.qq.com/mp/waerrpage?appid=wxa708de63ee4a2353&type=upgrade&upgradetype=3#wechat_redirect
2025-07-27 06:46:48 | INFO | 未知的XML消息类型: 33
2025-07-27 06:46:48 | INFO | 消息标题: 派对永不停Beat-1000钻石等你领取！
2025-07-27 06:46:48 | INFO | 消息描述: None
2025-07-27 06:46:48 | INFO | 消息URL: https://mp.weixin.qq.com/mp/waerrpage?appid=wxa708de63ee4a2353&type=upgrade&upgradetype=3#wechat_redirect
2025-07-27 06:46:48 | INFO | 完整XML内容:
<?xml version="1.0"?>
<msg>
	<appmsg appid="" sdkver="0">
		<title>派对永不停Beat-1000钻石等你领取！</title>
		<des />
		<username />
		<action>view</action>
		<type>33</type>
		<showtype>0</showtype>
		<content />
		<url>https://mp.weixin.qq.com/mp/waerrpage?appid=wxa708de63ee4a2353&amp;type=upgrade&amp;upgradetype=3#wechat_redirect</url>
		<lowurl />
		<forwardflag>0</forwardflag>
		<dataurl />
		<lowdataurl />
		<contentattr>0</contentattr>
		<streamvideo>
			<streamvideourl />
			<streamvideototaltime>0</streamvideototaltime>
			<streamvideotitle />
			<streamvideowording />
			<streamvideoweburl />
			<streamvideothumburl />
			<streamvideoaduxinfo />
			<streamvideopublishid />
		</streamvideo>
		<canvasPageItem>
			<canvasPageXml><![CDATA[]]></canvasPageXml>
		</canvasPageItem>
		<appattach>
			<attachid />
			<cdnthumburl>3057020100044b30490201000204ef2f8dd002032f53a10204928f8cb6020468855aa8042462346135366632392d316138632d343536352d626231652d6531376665306132386235320204011808030201000405004c57c100</cdnthumburl>
			<cdnthumbmd5>e9e9b54db642fe6ca8c55ac56a90eee4</cdnthumbmd5>
			<cdnthumblength>74789</cdnthumblength>
			<cdnthumbheight>576</cdnthumbheight>
			<cdnthumbwidth>720</cdnthumbwidth>
			<cdnthumbaeskey>7ab98605fd8a3645ecd912ac5f1ccc79</cdnthumbaeskey>
			<aeskey>7ab98605fd8a3645ecd912ac5f1ccc79</aeskey>
			<encryver>1</encryver>
			<fileext />
			<islargefilemsg>0</islargefilemsg>
		</appattach>
		<extinfo />
		<androidsource>0</androidsource>
		<sourceusername>gh_25eb09d7bc53@app</sourceusername>
		<sourcedisplayname>唱舞星愿站</sourcedisplayname>
		<commenturl />
		<thumburl />
		<mediatagname />
		<messageaction><![CDATA[]]></messageaction>
		<messageext><![CDATA[]]></messageext>
		<emoticongift>
			<packageflag>0</packageflag>
			<packageid />
		</emoticongift>
		<emoticonshared>
			<packageflag>0</packageflag>
			<packageid />
		</emoticonshared>
		<designershared>
			<designeruin>0</designeruin>
			<designername>null</designername>
			<designerrediretcturl><![CDATA[null]]></designerrediretcturl>
		</designershared>
		<emotionpageshared>
			<tid>0</tid>
			<title>null</title>
			<desc>null</desc>
			<iconUrl><![CDATA[null]]></iconUrl>
			<secondUrl>null</secondUrl>
			<pageType>0</pageType>
			<setKey>null</setKey>
		</emotionpageshared>
		<webviewshared>
			<shareUrlOriginal />
			<shareUrlOpen />
			<jsAppId />
			<publisherId>wxapp_wxa708de63ee4a2353pages/Activity/teamUp/index.html?key=uK1I2mwn&amp;share_unionid=oA7D81ZgczVzy9lpDUGlnt5LkVxc&amp;task_id=&amp;team_id=26414</publisherId>
			<publisherReqId />
		</webviewshared>
		<template_id />
		<md5>e9e9b54db642fe6ca8c55ac56a90eee4</md5>
		<websearch>
			<rec_category>0</rec_category>
			<channelId>0</channelId>
		</websearch>
		<weappinfo>
			<pagepath><![CDATA[pages/Activity/teamUp/index.html?key=uK1I2mwn&share_unionid=oA7D81ZgczVzy9lpDUGlnt5LkVxc&task_id=&team_id=26414]]></pagepath>
			<username>gh_25eb09d7bc53@app</username>
			<appid>wxa708de63ee4a2353</appid>
			<version>16</version>
			<type>2</type>
			<weappiconurl><![CDATA[http://wx.qlogo.cn/mmhead/FMajU52WvbEE6SVVwqdPicO6GjWVVIhhLiaZK2NvCJgt3epAkLg7A3nkRTia23B9lSt0pXQJz0viaR4/96]]></weappiconurl>
			<shareId><![CDATA[1_wxa708de63ee4a2353_1dfbea7e30807954cb6f6af5833cba63_1753570013_2]]></shareId>
			<appservicetype>0</appservicetype>
			<secflagforsinglepagemode>0</secflagforsinglepagemode>
			<videopageinfo>
				<thumbwidth>720</thumbwidth>
				<thumbheight>576</thumbheight>
				<fromopensdk>0</fromopensdk>
			</videopageinfo>
		</weappinfo>
		<statextstr />
		<musicShareItem>
			<musicDuration>0</musicDuration>
		</musicShareItem>
		<finderLiveProductShare>
			<finderLiveID><![CDATA[]]></finderLiveID>
			<finderUsername><![CDATA[]]></finderUsername>
			<finderObjectID><![CDATA[]]></finderObjectID>
			<finderNonceID><![CDATA[]]></finderNonceID>
			<liveStatus><![CDATA[]]></liveStatus>
			<appId><![CDATA[]]></appId>
			<pagePath><![CDATA[]]></pagePath>
			<productId><![CDATA[]]></productId>
			<coverUrl><![CDATA[]]></coverUrl>
			<productTitle><![CDATA[]]></productTitle>
			<marketPrice><![CDATA[0]]></marketPrice>
			<sellingPrice><![CDATA[0]]></sellingPrice>
			<platformHeadImg><![CDATA[]]></platformHeadImg>
			<platformName><![CDATA[]]></platformName>
			<shopWindowId><![CDATA[]]></shopWindowId>
			<flashSalePrice><![CDATA[0]]></flashSalePrice>
			<flashSaleEndTime><![CDATA[0]]></flashSaleEndTime>
			<ecSource><![CDATA[]]></ecSource>
			<sellingPriceWording><![CDATA[]]></sellingPriceWording>
			<platformIconURL><![CDATA[]]></platformIconURL>
			<firstProductTagURL><![CDATA[]]></firstProductTagURL>
			<firstProductTagAspectRatioString><![CDATA[0.0]]></firstProductTagAspectRatioString>
			<secondProductTagURL><![CDATA[]]></secondProductTagURL>
			<secondProductTagAspectRatioString><![CDATA[0.0]]></secondProductTagAspectRatioString>
			<firstGuaranteeWording><![CDATA[]]></firstGuaranteeWording>
			<secondGuaranteeWording><![CDATA[]]></secondGuaranteeWording>
			<thirdGuaranteeWording><![CDATA[]]></thirdGuaranteeWording>
			<isPriceBeginShow>false</isPriceBeginShow>
			<lastGMsgID><![CDATA[]]></lastGMsgID>
			<promoterKey><![CDATA[]]></promoterKey>
			<discountWording><![CDATA[]]></discountWording>
			<priceSuffixDescription><![CDATA[]]></priceSuffixDescription>
			<productCardKey><![CDATA[]]></productCardKey>
			<isWxShop><![CDATA[]]></isWxShop>
			<brandIconUrl><![CDATA[]]></brandIconUrl>
			<rIconUrl><![CDATA[]]></rIconUrl>
			<rIconUrlDarkMode><![CDATA[]]></rIconUrlDarkMode>
			<topShopIconUrl><![CDATA[]]></topShopIconUrl>
			<topShopIconUrlDarkMode><![CDATA[]]></topShopIconUrlDarkMode>
			<simplifyTopShopIconUrl><![CDATA[]]></simplifyTopShopIconUrl>
			<simplifyTopShopIconUrlDarkmode><![CDATA[]]></simplifyTopShopIconUrlDarkmode>
			<topShopIconWidth><![CDATA[0]]></topShopIconWidth>
			<topShopIconHeight><![CDATA[0]]></topShopIconHeight>
			<simplifyTopShopIconWidth><![CDATA[0]]></simplifyTopShopIconWidth>
			<simplifyTopShopIconHeight><![CDATA[0]]></simplifyTopShopIconHeight>
			<showBoxItemStringList />
		</finderLiveProductShare>
		<finderOrder>
			<appID><![CDATA[]]></appID>
			<orderID><![CDATA[]]></orderID>
			<path><![CDATA[]]></path>
			<priceWording><![CDATA[]]></priceWording>
			<stateWording><![CDATA[]]></stateWording>
			<productImageURL><![CDATA[]]></productImageURL>
			<products><![CDATA[]]></products>
			<productsCount><![CDATA[0]]></productsCount>
			<orderType><![CDATA[0]]></orderType>
			<newPriceWording><![CDATA[]]></newPriceWording>
			<newStateWording><![CDATA[]]></newStateWording>
			<useNewWording><![CDATA[0]]></useNewWording>
		</finderOrder>
		<finderShopWindowShare>
			<finderUsername><![CDATA[]]></finderUsername>
			<avatar><![CDATA[]]></avatar>
			<nickname><![CDATA[]]></nickname>
			<commodityInStockCount><![CDATA[]]></commodityInStockCount>
			<appId><![CDATA[]]></appId>
			<path><![CDATA[]]></path>
			<appUsername><![CDATA[]]></appUsername>
			<query><![CDATA[]]></query>
			<liteAppId><![CDATA[]]></liteAppId>
			<liteAppPath><![CDATA[]]></liteAppPath>
			<liteAppQuery><![CDATA[]]></liteAppQuery>
			<platformTagURL><![CDATA[]]></platformTagURL>
			<saleWording><![CDATA[]]></saleWording>
			<lastGMsgID><![CDATA[]]></lastGMsgID>
			<profileTypeWording><![CDATA[]]></profileTypeWording>
			<saleWordingExtra><![CDATA[]]></saleWordingExtra>
			<isWxShop><![CDATA[]]></isWxShop>
			<platformIconUrl><![CDATA[]]></platformIconUrl>
			<brandIconUrl><![CDATA[]]></brandIconUrl>
			<description><![CDATA[]]></description>
			<backgroundUrl><![CDATA[]]></backgroundUrl>
			<darkModePlatformIconUrl><![CDATA[]]></darkModePlatformIconUrl>
			<rIconUrl><![CDATA[]]></rIconUrl>
			<rIconUrlDarkMode><![CDATA[]]></rIconUrlDarkMode>
			<rWords><![CDATA[]]></rWords>
			<topShopIconUrl><![CDATA[]]></topShopIconUrl>
			<topShopIconUrlDarkMode><![CDATA[]]></topShopIconUrlDarkMode>
			<simplifyTopShopIconUrl><![CDATA[]]></simplifyTopShopIconUrl>
			<simplifyTopShopIconUrlDarkmode><![CDATA[]]></simplifyTopShopIconUrlDarkmode>
			<topShopIconWidth><![CDATA[0]]></topShopIconWidth>
			<topShopIconHeight><![CDATA[0]]></topShopIconHeight>
			<simplifyTopShopIconWidth><![CDATA[0]]></simplifyTopShopIconWidth>
			<simplifyTopShopIconHeight><![CDATA[0]]></simplifyTopShopIconHeight>
			<reputationInfo>
				<hasReputationInfo>0</hasReputationInfo>
				<reputationScore>0</reputationScore>
				<reputationWording />
				<reputationTextColor />
				<reputationLevelWording />
				<reputationBackgroundColor />
			</reputationInfo>
			<productImageURLList />
		</finderShopWindowShare>
		<findernamecard>
			<username />
			<avatar><![CDATA[]]></avatar>
			<nickname />
			<auth_job />
			<auth_icon>0</auth_icon>
			<auth_icon_url />
			<ecSource><![CDATA[]]></ecSource>
			<lastGMsgID><![CDATA[]]></lastGMsgID>
		</findernamecard>
		<finderGuarantee>
			<scene><![CDATA[0]]></scene>
		</finderGuarantee>
		<directshare>0</directshare>
		<gamecenter>
			<namecard>
				<iconUrl />
				<name />
				<desc />
				<tail />
				<jumpUrl />
				<liteappId />
				<liteappPath />
				<liteappQuery />
				<liteappMinVersion />
			</namecard>
		</gamecenter>
		<patMsg>
			<chatUser />
			<records>
				<recordNum>0</recordNum>
			</records>
		</patMsg>
		<secretmsg>
			<issecretmsg>0</issecretmsg>
		</secretmsg>
		<referfromscene>0</referfromscene>
		<gameshare>
			<liteappext>
				<liteappbizdata />
				<priority>0</priority>
			</liteappext>
			<appbrandext>
				<litegameinfo />
				<priority>-1</priority>
			</appbrandext>
			<gameshareid />
			<sharedata />
			<isvideo>0</isvideo>
			<duration>-1</duration>
			<isexposed>0</isexposed>
			<readtext />
		</gameshare>
		<tingChatRoomItem>
			<type>0</type>
			<categoryItem>null</categoryItem>
			<categoryId />
		</tingChatRoomItem>
		<mpsharetrace>
			<hasfinderelement>0</hasfinderelement>
			<lastgmsgid />
		</mpsharetrace>
		<wxgamecard>
			<framesetname />
			<mbcarddata />
			<minpkgversion />
			<clientextinfo />
			<mbcardheight>0</mbcardheight>
			<isoldversion>0</isoldversion>
		</wxgamecard>
		<ecskfcard>
			<framesetname />
			<mbcarddata />
			<minupdateunixtimestamp>0</minupdateunixtimestamp>
			<needheader>false</needheader>
			<summary />
		</ecskfcard>
		<liteapp>
			<id>null</id>
			<path />
			<query />
			<istransparent>0</istransparent>
			<hideicon>0</hideicon>
			<forbidforward>0</forbidforward>
		</liteapp>
		<opensdk_share_is_modified>0</opensdk_share_is_modified>
	</appmsg>
	<fromusername>wxid_2530z9t0joek22</fromusername>
	<scene>0</scene>
	<appinfo>
		<version>1</version>
		<appname></appname>
	</appinfo>
	<commenturl></commenturl>
</msg>

2025-07-27 06:51:18 | DEBUG | 收到消息: {'MsgId': 1066026932, 'FromUserName': {'string': '***********@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 47, 'Content': {'string': 'last--exile:\n<msg><emoji fromusername="last--exile" tousername="***********@chatroom" type="2" idbuffer="media:0_0" md5="01cc10c434ef8167d024fa7143dbea85" len="451379" productid="" androidmd5="01cc10c434ef8167d024fa7143dbea85" androidlen="451379" s60v3md5="01cc10c434ef8167d024fa7143dbea85" s60v3len="451379" s60v5md5="01cc10c434ef8167d024fa7143dbea85" s60v5len="451379" cdnurl="http://vweixinf.tc.qq.com/110/20401/stodownload?m=01cc10c434ef8167d024fa7143dbea85&amp;filekey=30440201010430302e02016e0402535a04203031636331306334333465663831363764303234666137313433646265613835020306e333040d00000004627466730000000131&amp;hy=SZ&amp;storeid=323032313036303130353139313530303030373564373836373465663763643935373538303930303030303036653031303034666231&amp;ef=1&amp;bizid=1022" designerid="" thumburl="" encrypturl="http://vweixinf.tc.qq.com/110/20402/stodownload?m=e5aeaa158548e31d615f849164865849&amp;filekey=30440201010430302e02016e0402535a04206535616561613135383534386533316436313566383439313634383635383439020306e340040d00000004627466730000000131&amp;hy=SZ&amp;storeid=323032313036303130353139313530303031393933393836373465663763643935373538303930303030303036653032303034666232&amp;ef=2&amp;bizid=1022" aeskey="5f8b0be8f50b402f958ae70e4ddbdf8f" externurl="http://vweixinf.tc.qq.com/110/20403/stodownload?m=691060e7d2154fd3dc58e6d2dd6096b3&amp;filekey=3043020101042f302d02016e0402535a04203639313036306537643231353466643364633538653664326464363039366233020268e0040d00000004627466730000000131&amp;hy=SZ&amp;storeid=323032313036303130353139313530303033333563363836373465663763643935373538303930303030303036653033303034666233&amp;ef=3&amp;bizid=1022" externmd5="7c0f469041c0f52788a21938451e8b25" width="141" height="141" tpurl="" tpauthkey="" attachedtext="" attachedtextcolor="" lensid="" emojiattr="" linkid="" desc=""></emoji></msg>'}, 'Status': 3, 'ImgStatus': 2, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1753570285, 'MsgSource': '<msgsource>\n\t<silence>0</silence>\n\t<membercount>73</membercount>\n\t<signature>N0_V1_U7+bYmeS|v1_hMwb52sD</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'PushContent': '亮在群聊中发了一个表情', 'NewMsgId': 6212810033215097293, 'MsgSeq': 871402655}
2025-07-27 06:51:18 | INFO | 收到表情消息: 消息ID:1066026932 来自:***********@chatroom 发送人:last--exile MD5:01cc10c434ef8167d024fa7143dbea85 大小:451379
2025-07-27 06:51:19 | DEBUG | [DoubaoImageRecognition] 缓存表情消息: 6212810033215097293
2025-07-27 06:51:28 | DEBUG | 收到消息: {'MsgId': 1874911250, 'FromUserName': {'string': '***********@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 10002, 'Content': {'string': '***********@chatroom:\n<sysmsg type="pat">\n<pat>\n  <fromusername>last--exile</fromusername>\n  <chatusername>***********@chatroom</chatusername>\n  <pattedusername>wxid_wlnzvr8ivgd422</pattedusername>\n  <patsuffix><![CDATA[]]></patsuffix>\n  <patsuffixversion>2</patsuffixversion>\n\n\n\n\n  <template><![CDATA["${last--exile}" 拍了拍 "${wxid_wlnzvr8ivgd422}"]]></template>\n\n\n\n\n</pat>\n</sysmsg>'}, 'Status': 4, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1753570293, 'MsgSource': '<msgsource>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'NewMsgId': 8617618834400721868, 'MsgSeq': 871402656}
2025-07-27 06:51:28 | DEBUG | 系统消息类型: pat
2025-07-27 06:51:28 | INFO | 收到拍一拍消息: 消息ID:1874911250 来自:***********@chatroom 发送人:***********@chatroom 拍者:last--exile 被拍:wxid_wlnzvr8ivgd422 后缀:None
2025-07-27 06:51:29 | DEBUG | [PatReply] 被拍者 wxid_wlnzvr8ivgd422 不是目标用户 wxid_4usgcju5ey9q29，跳过
2025-07-27 06:58:19 | DEBUG | 收到消息: {'MsgId': 949039656, 'FromUserName': {'string': '***********@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 49, 'Content': {'string': 'wxid_e3o8s2nf9u2o22:\n<?xml version="1.0"?>\n<msg>\n\t<appmsg appid="" sdkver="0">\n\t\t<title>Coser  ||《新世纪福音战士》-绫波丽</title>\n\t\t<des>只要微笑就好了</des>\n\t\t<username />\n\t\t<action>view</action>\n\t\t<type>5</type>\n\t\t<showtype>0</showtype>\n\t\t<content />\n\t\t<url>https://mp.weixin.qq.com/s?__biz=MzkwMTM4ODkxMw==&amp;mid=2247507121&amp;idx=2&amp;sn=8cb3a9f9feb7a4c8f2d74fec03506d40&amp;chksm=c1d72cf89674c2c332277774e6b090715acd4ad90d6ed1b32de8ff9f130a24196dde63f44174&amp;mpshare=1&amp;scene=1&amp;srcid=0727ipAIF2ylC4so3CmV6AGY&amp;sharer_shareinfo=4085f1578b048fdeffb7ba596ecc6ff3&amp;sharer_shareinfo_first=4085f1578b048fdeffb7ba596ecc6ff3#rd</url>\n\t\t<lowurl />\n\t\t<forwardflag>0</forwardflag>\n\t\t<dataurl />\n\t\t<lowdataurl />\n\t\t<contentattr>0</contentattr>\n\t\t<streamvideo>\n\t\t\t<streamvideourl />\n\t\t\t<streamvideototaltime>0</streamvideototaltime>\n\t\t\t<streamvideotitle />\n\t\t\t<streamvideowording />\n\t\t\t<streamvideoweburl />\n\t\t\t<streamvideothumburl />\n\t\t\t<streamvideoaduxinfo />\n\t\t\t<streamvideopublishid />\n\t\t</streamvideo>\n\t\t<canvasPageItem>\n\t\t\t<canvasPageXml><![CDATA[]]></canvasPageXml>\n\t\t</canvasPageItem>\n\t\t<appattach>\n\t\t\t<attachid />\n\t\t\t<cdnthumburl>3057020100044b3049020100020435b9d70b02032f50e60204464e8b7b020468855d91042439376661396461382d353462622d343933372d623466362d3030383939626466366135660204051408030201000405004c4d9a00</cdnthumburl>\n\t\t\t<cdnthumbmd5>ea07ad17fe42d588805ad9fa7f97b8d5</cdnthumbmd5>\n\t\t\t<cdnthumblength>25765</cdnthumblength>\n\t\t\t<cdnthumbheight>120</cdnthumbheight>\n\t\t\t<cdnthumbwidth>120</cdnthumbwidth>\n\t\t\t<cdnthumbaeskey>659259f38395ba1c2f746b16a3eaf1a2</cdnthumbaeskey>\n\t\t\t<aeskey>659259f38395ba1c2f746b16a3eaf1a2</aeskey>\n\t\t\t<encryver>1</encryver>\n\t\t\t<fileext />\n\t\t\t<islargefilemsg>0</islargefilemsg>\n\t\t</appattach>\n\t\t<extinfo />\n\t\t<androidsource>2</androidsource>\n\t\t<sourceusername>gh_ddc1e6b200e1</sourceusername>\n\t\t<sourcedisplayname>异次元研讨社</sourcedisplayname>\n\t\t<commenturl />\n\t\t<thumburl />\n\t\t<mediatagname />\n\t\t<messageaction><![CDATA[]]></messageaction>\n\t\t<messageext><![CDATA[]]></messageext>\n\t\t<emoticongift>\n\t\t\t<packageflag>0</packageflag>\n\t\t\t<packageid />\n\t\t</emoticongift>\n\t\t<emoticonshared>\n\t\t\t<packageflag>0</packageflag>\n\t\t\t<packageid />\n\t\t</emoticonshared>\n\t\t<designershared>\n\t\t\t<designeruin>0</designeruin>\n\t\t\t<designername>null</designername>\n\t\t\t<designerrediretcturl><![CDATA[null]]></designerrediretcturl>\n\t\t</designershared>\n\t\t<emotionpageshared>\n\t\t\t<tid>0</tid>\n\t\t\t<title>null</title>\n\t\t\t<desc>null</desc>\n\t\t\t<iconUrl><![CDATA[null]]></iconUrl>\n\t\t\t<secondUrl>null</secondUrl>\n\t\t\t<pageType>0</pageType>\n\t\t\t<setKey>null</setKey>\n\t\t</emotionpageshared>\n\t\t<webviewshared>\n\t\t\t<shareUrlOriginal>http://mp.weixin.qq.com/s?__biz=MzkwMTM4ODkxMw==&amp;mid=2247507121&amp;idx=2&amp;sn=8cb3a9f9feb7a4c8f2d74fec03506d40&amp;chksm=c1c0c06ab120aa81d7e3719efabdfe23a1aedd6aa22aa8a68ac96d2900e9146f8da95eb4d4b0&amp;xtrack=1&amp;scene=90&amp;subscene=93&amp;sessionid=1753569921&amp;flutter_pos=1&amp;clicktime=1753570695&amp;enterid=1753570695&amp;finder_biz_enter_id=4&amp;ranksessionid=1753569644&amp;jumppath=20020_1753570681362%2CWebViewStubProxyUI_1753570682436%2C20020_1753570682576%2C50094_1753570693536&amp;jumppathdepth=4&amp;ascene=56&amp;fasttmpl_type=0&amp;fasttmpl_fullversion=7835612-zh_CN-zip&amp;fasttmpl_flag=0&amp;realreporttime=1753570695591#rd</shareUrlOriginal>\n\t\t\t<shareUrlOpen>https://mp.weixin.qq.com/s?__biz=MzkwMTM4ODkxMw==&amp;mid=2247507121&amp;idx=2&amp;sn=8cb3a9f9feb7a4c8f2d74fec03506d40&amp;chksm=c1c0c06ab120aa81d7e3719efabdfe23a1aedd6aa22aa8a68ac96d2900e9146f8da95eb4d4b0&amp;xtrack=1&amp;scene=90&amp;subscene=93&amp;sessionid=1753569921&amp;flutter_pos=1&amp;clicktime=1753570695&amp;enterid=1753570695&amp;finder_biz_enter_id=4&amp;ranksessionid=1753569644&amp;jumppath=20020_1753570681362%2CWebViewStubProxyUI_1753570682436%2C20020_1753570682576%2C50094_1753570693536&amp;jumppathdepth=4&amp;ascene=56&amp;fasttmpl_type=0&amp;fasttmpl_fullversion=7835612-zh_CN-zip&amp;fasttmpl_flag=0&amp;realreporttime=1753570695591&amp;devicetype=android-35&amp;version=28003956&amp;nettype=3gnet&amp;lang=zh_CN&amp;session_us=gh_ddc1e6b200e1&amp;countrycode=CN&amp;exportkey=n_ChQIAhIQDgW%2FK9IF%2B9zwa0FCdLSKoxLgAQIE97dBBAEAAAAAAPprFHGUhrQAAAAOpnltbLcz9gKNyK89dVj0i3n9n0NEQt2yscfmMPck6Q%2BLdFvVi6S7WwrjcbPG2ajSfsKXCEBqJ9jbExF54KTX8UPzhfLhhxUdZOJBufxikHvRhcaFZ2hFYnF0qiGB8gfgfLGuvATgRyOobqW3XULQm1DDdCxIRpWlyMrXE7A1mZfKP3nm3Gn3kqkgiI8qbgHup9xw%2Bm1qUAfCIJdnbVPwBsTgAEboZ2Z%2BrtCKww5Neff1Ttd2VsioBROYkdubxeBnjntGJaQrq0Gl&amp;pass_ticket=b%2BsmhyStqTLwZUidz9iaeu4ODrprccG1ZaYd4BC3kqivwaa%2BpIi0xCrrNvm%2FUSqx&amp;wx_header=3</shareUrlOpen>\n\t\t\t<jsAppId />\n\t\t\t<publisherId>msg_561777</publisherId>\n\t\t\t<publisherReqId>1224026429</publisherReqId>\n\t\t</webviewshared>\n\t\t<template_id />\n\t\t<md5>ea07ad17fe42d588805ad9fa7f97b8d5</md5>\n\t\t<websearch>\n\t\t\t<rec_category>0</rec_category>\n\t\t\t<channelId>0</channelId>\n\t\t</websearch>\n\t\t<weappinfo>\n\t\t\t<username />\n\t\t\t<appid />\n\t\t\t<appservicetype>0</appservicetype>\n\t\t\t<secflagforsinglepagemode>0</secflagforsinglepagemode>\n\t\t\t<videopageinfo>\n\t\t\t\t<thumbwidth>120</thumbwidth>\n\t\t\t\t<thumbheight>120</thumbheight>\n\t\t\t\t<fromopensdk>0</fromopensdk>\n\t\t\t</videopageinfo>\n\t\t</weappinfo>\n\t\t<statextstr />\n\t\t<mmreadershare>\n\t\t\t<itemshowtype>0</itemshowtype>\n\t\t\t<ispaysubscribe>0</ispaysubscribe>\n\t\t</mmreadershare>\n\t\t<musicShareItem>\n\t\t\t<musicDuration>0</musicDuration>\n\t\t</musicShareItem>\n\t\t<finderLiveProductShare>\n\t\t\t<finderLiveID><![CDATA[]]></finderLiveID>\n\t\t\t<finderUsername><![CDATA[]]></finderUsername>\n\t\t\t<finderObjectID><![CDATA[]]></finderObjectID>\n\t\t\t<finderNonceID><![CDATA[]]></finderNonceID>\n\t\t\t<liveStatus><![CDATA[]]></liveStatus>\n\t\t\t<appId><![CDATA[]]></appId>\n\t\t\t<pagePath><![CDATA[]]></pagePath>\n\t\t\t<productId><![CDATA[]]></productId>\n\t\t\t<coverUrl><![CDATA[]]></coverUrl>\n\t\t\t<productTitle><![CDATA[]]></productTitle>\n\t\t\t<marketPrice><![CDATA[0]]></marketPrice>\n\t\t\t<sellingPrice><![CDATA[0]]></sellingPrice>\n\t\t\t<platformHeadImg><![CDATA[]]></platformHeadImg>\n\t\t\t<platformName><![CDATA[]]></platformName>\n\t\t\t<shopWindowId><![CDATA[]]></shopWindowId>\n\t\t\t<flashSalePrice><![CDATA[0]]></flashSalePrice>\n\t\t\t<flashSaleEndTime><![CDATA[0]]></flashSaleEndTime>\n\t\t\t<ecSource><![CDATA[]]></ecSource>\n\t\t\t<sellingPriceWording><![CDATA[]]></sellingPriceWording>\n\t\t\t<platformIconURL><![CDATA[]]></platformIconURL>\n\t\t\t<firstProductTagURL><![CDATA[]]></firstProductTagURL>\n\t\t\t<firstProductTagAspectRatioString><![CDATA[0.0]]></firstProductTagAspectRatioString>\n\t\t\t<secondProductTagURL><![CDATA[]]></secondProductTagURL>\n\t\t\t<secondProductTagAspectRatioString><![CDATA[0.0]]></secondProductTagAspectRatioString>\n\t\t\t<firstGuaranteeWording><![CDATA[]]></firstGuaranteeWording>\n\t\t\t<secondGuaranteeWording><![CDATA[]]></secondGuaranteeWording>\n\t\t\t<thirdGuaranteeWording><![CDATA[]]></thirdGuaranteeWording>\n\t\t\t<isPriceBeginShow>false</isPriceBeginShow>\n\t\t\t<lastGMsgID><![CDATA[]]></lastGMsgID>\n\t\t\t<promoterKey><![CDATA[]]></promoterKey>\n\t\t\t<discountWording><![CDATA[]]></discountWording>\n\t\t\t<priceSuffixDescription><![CDATA[]]></priceSuffixDescription>\n\t\t\t<productCardKey><![CDATA[]]></productCardKey>\n\t\t\t<isWxShop><![CDATA[]]></isWxShop>\n\t\t\t<brandIconUrl><![CDATA[]]></brandIconUrl>\n\t\t\t<showBoxItemStringList />\n\t\t</finderLiveProductShare>\n\t\t<finderOrder>\n\t\t\t<appID><![CDATA[]]></appID>\n\t\t\t<orderID><![CDATA[]]></orderID>\n\t\t\t<path><![CDATA[]]></path>\n\t\t\t<priceWording><![CDATA[]]></priceWording>\n\t\t\t<stateWording><![CDATA[]]></stateWording>\n\t\t\t<productImageURL><![CDATA[]]></productImageURL>\n\t\t\t<products><![CDATA[]]></products>\n\t\t\t<productsCount><![CDATA[0]]></productsCount>\n\t\t\t<orderType><![CDATA[0]]></orderType>\n\t\t\t<newPriceWording><![CDATA[]]></newPriceWording>\n\t\t\t<newStateWording><![CDATA[]]></newStateWording>\n\t\t\t<useNewWording><![CDATA[0]]></useNewWording>\n\t\t</finderOrder>\n\t\t<finderShopWindowShare>\n\t\t\t<finderUsername><![CDATA[]]></finderUsername>\n\t\t\t<avatar><![CDATA[]]></avatar>\n\t\t\t<nickname><![CDATA[]]></nickname>\n\t\t\t<commodityInStockCount><![CDATA[]]></commodityInStockCount>\n\t\t\t<appId><![CDATA[]]></appId>\n\t\t\t<path><![CDATA[]]></path>\n\t\t\t<appUsername><![CDATA[]]></appUsername>\n\t\t\t<query><![CDATA[]]></query>\n\t\t\t<liteAppId><![CDATA[]]></liteAppId>\n\t\t\t<liteAppPath><![CDATA[]]></liteAppPath>\n\t\t\t<liteAppQuery><![CDATA[]]></liteAppQuery>\n\t\t\t<platformTagURL><![CDATA[]]></platformTagURL>\n\t\t\t<saleWording><![CDATA[]]></saleWording>\n\t\t\t<lastGMsgID><![CDATA[]]></lastGMsgID>\n\t\t\t<profileTypeWording><![CDATA[]]></profileTypeWording>\n\t\t\t<saleWordingExtra><![CDATA[]]></saleWordingExtra>\n\t\t\t<isWxShop><![CDATA[]]></isWxShop>\n\t\t\t<platformIconUrl><![CDATA[]]></platformIconUrl>\n\t\t\t<brandIconUrl><![CDATA[]]></brandIconUrl>\n\t\t\t<description><![CDATA[]]></description>\n\t\t\t<backgroundUrl><![CDATA[]]></backgroundUrl>\n\t\t\t<darkModePlatformIconUrl><![CDATA[]]></darkModePlatformIconUrl>\n\t\t\t<reputationInfo>\n\t\t\t\t<hasReputationInfo>0</hasReputationInfo>\n\t\t\t\t<reputationScore>0</reputationScore>\n\t\t\t\t<reputationWording />\n\t\t\t\t<reputationTextColor />\n\t\t\t\t<reputationLevelWording />\n\t\t\t\t<reputationBackgroundColor />\n\t\t\t</reputationInfo>\n\t\t\t<productImageURLList />\n\t\t</finderShopWindowShare>\n\t\t<findernamecard>\n\t\t\t<username />\n\t\t\t<avatar><![CDATA[]]></avatar>\n\t\t\t<nickname />\n\t\t\t<auth_job />\n\t\t\t<auth_icon>0</auth_icon>\n\t\t\t<auth_icon_url />\n\t\t\t<ecSource><![CDATA[]]></ecSource>\n\t\t\t<lastGMsgID><![CDATA[]]></lastGMsgID>\n\t\t</findernamecard>\n\t\t<finderGuarantee>\n\t\t\t<scene><![CDATA[0]]></scene>\n\t\t</finderGuarantee>\n\t\t<directshare>0</directshare>\n\t\t<gamecenter>\n\t\t\t<namecard>\n\t\t\t\t<iconUrl />\n\t\t\t\t<name />\n\t\t\t\t<desc />\n\t\t\t\t<tail />\n\t\t\t\t<jumpUrl />\n\t\t\t</namecard>\n\t\t</gamecenter>\n\t\t<patMsg>\n\t\t\t<chatUser />\n\t\t\t<records>\n\t\t\t\t<recordNum>0</recordNum>\n\t\t\t</records>\n\t\t</patMsg>\n\t\t<secretmsg>\n\t\t\t<issecretmsg>0</issecretmsg>\n\t\t</secretmsg>\n\t\t<referfromscene>0</referfromscene>\n\t\t<gameshare>\n\t\t\t<liteappext>\n\t\t\t\t<liteappbizdata />\n\t\t\t\t<priority>0</priority>\n\t\t\t</liteappext>\n\t\t\t<appbrandext>\n\t\t\t\t<litegameinfo />\n\t\t\t\t<priority>-1</priority>\n\t\t\t</appbrandext>\n\t\t\t<gameshareid />\n\t\t\t<sharedata />\n\t\t\t<isvideo>0</isvideo>\n\t\t\t<duration>-1</duration>\n\t\t\t<isexposed>0</isexposed>\n\t\t\t<readtext />\n\t\t</gameshare>\n\t\t<mpsharetrace>\n\t\t\t<hasfinderelement>0</hasfinderelement>\n\t\t\t<lastgmsgid />\n\t\t</mpsharetrace>\n\t\t<wxgamecard>\n\t\t\t<framesetname />\n\t\t\t<mbcarddata />\n\t\t\t<minpkgversion />\n\t\t\t<clientextinfo />\n\t\t\t<mbcardheight>0</mbcardheight>\n\t\t\t<isoldversion>0</isoldversion>\n\t\t</wxgamecard>\n\t\t<liteapp>\n\t\t\t<id>null</id>\n\t\t\t<path />\n\t\t\t<query />\n\t\t\t<istransparent>0</istransparent>\n\t\t\t<hideicon>0</hideicon>\n\t\t</liteapp>\n\t\t<opensdk_share_is_modified>0</opensdk_share_is_modified>\n\t</appmsg>\n\t<fromusername>wxid_e3o8s2nf9u2o22</fromusername>\n\t<scene>0</scene>\n\t<appinfo>\n\t\t<version>1</version>\n\t\t<appname />\n\t</appinfo>\n\t<commenturl />\n</msg>\n'}, 'Status': 3, 'ImgStatus': 2, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1753570705, 'MsgSource': '<msgsource>\n\t<sec_msg_node>\n\t\t<uuid>d001ada1aa94f6f7590e52af7e4eba6f_</uuid>\n\t\t<risk-file-flag />\n\t\t<risk-file-md5-list />\n\t</sec_msg_node>\n\t<silence>0</silence>\n\t<membercount>73</membercount>\n\t<signature>N0_V1_b9FrpjVR|v1_DWFRUMo/</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'PushContent': '星空- 梦 : [链接]Coser  ||《新世纪福音战士》-绫波丽', 'NewMsgId': 1109071046554681085, 'MsgSeq': 871402657}
2025-07-27 06:58:19 | DEBUG | 从群聊消息中提取发送者: wxid_e3o8s2nf9u2o22
2025-07-27 06:58:19 | INFO | 收到公众号文章消息: 消息ID:949039656 来自:***********@chatroom
2025-07-27 06:58:19 | ERROR | 解析XML失败: mismatched tag: line 1, column 384
2025-07-27 06:58:19 | DEBUG | [ArticleForwarder] 检测到XML开头被截断，尝试修复
2025-07-27 06:58:19 | DEBUG | [ArticleForwarder] 已修复XML开头截断问题
2025-07-27 06:58:19 | DEBUG | 尝试使用修复后的XML重新解析
2025-07-27 06:58:19 | DEBUG | 从sourcedisplayname提取到公众号: 异次元研讨社
2025-07-27 06:58:19 | DEBUG | 公众号「异次元研讨社」不在监控列表中，跳过处理
2025-07-27 07:02:57 | DEBUG | 收到消息: {'MsgId': 604902840, 'FromUserName': {'string': '***********@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 49, 'Content': {'string': 'wxid_n5c0aekjceu621:\n<?xml version="1.0"?>\n<msg>\n\t<appmsg appid="" sdkver="0">\n\t\t<title>你怎么又萎了</title>\n\t\t<des />\n\t\t<username />\n\t\t<action>view</action>\n\t\t<type>57</type>\n\t\t<showtype>0</showtype>\n\t\t<content />\n\t\t<url />\n\t\t<lowurl />\n\t\t<forwardflag>0</forwardflag>\n\t\t<dataurl />\n\t\t<lowdataurl />\n\t\t<contentattr>0</contentattr>\n\t\t<streamvideo>\n\t\t\t<streamvideourl />\n\t\t\t<streamvideototaltime>0</streamvideototaltime>\n\t\t\t<streamvideotitle />\n\t\t\t<streamvideowording />\n\t\t\t<streamvideoweburl />\n\t\t\t<streamvideothumburl />\n\t\t\t<streamvideoaduxinfo />\n\t\t\t<streamvideopublishid />\n\t\t</streamvideo>\n\t\t<canvasPageItem>\n\t\t\t<canvasPageXml><![CDATA[]]></canvasPageXml>\n\t\t</canvasPageItem>\n\t\t<refermsg>\n\t\t\t<type>47</type>\n\t\t\t<svrid>6212810033215097293</svrid>\n\t\t\t<fromusr>***********@chatroom</fromusr>\n\t\t\t<chatusr>last--exile</chatusr>\n\t\t\t<displayname>亮</displayname>\n\t\t\t<msgsource>&lt;msgsource&gt;\n\t&lt;silence&gt;1&lt;/silence&gt;\n\t&lt;membercount&gt;73&lt;/membercount&gt;\n\t&lt;signature&gt;N0_V1_1xRBREHi|v1_q51iucVo&lt;/signature&gt;\n\t&lt;tmp_node&gt;\n\t\t&lt;publisher-id&gt;&lt;/publisher-id&gt;\n\t&lt;/tmp_node&gt;\n&lt;/msgsource&gt;\n</msgsource>\n\t\t\t<content>&lt;msg&gt;&lt;emoji fromusername="last--exile" tousername="***********@chatroom" type="2" idbuffer="media*#*0_0" md5="01cc10c434ef8167d024fa7143dbea85" len="451379" productid="" androidmd5="01cc10c434ef8167d024fa7143dbea85" androidlen="451379" s60v3md5="01cc10c434ef8167d024fa7143dbea85" s60v3len="451379" s60v5md5="01cc10c434ef8167d024fa7143dbea85" s60v5len="451379" cdnurl="http*#*//vweixinf.tc.qq.com/110/20401/stodownload?m=01cc10c434ef8167d024fa7143dbea85&amp;amp;filekey=30440201010430302e02016e0402535a04203031636331306334333465663831363764303234666137313433646265613835020306e333040d00000004627466730000000131&amp;amp;hy=SZ&amp;amp;storeid=323032313036303130353139313530303030373564373836373465663763643935373538303930303030303036653031303034666231&amp;amp;ef=1&amp;amp;bizid=1022" designerid="" thumburl="" encrypturl="http*#*//vweixinf.tc.qq.com/110/20402/stodownload?m=e5aeaa158548e31d615f849164865849&amp;amp;filekey=30440201010430302e02016e0402535a04206535616561613135383534386533316436313566383439313634383635383439020306e340040d00000004627466730000000131&amp;amp;hy=SZ&amp;amp;storeid=323032313036303130353139313530303031393933393836373465663763643935373538303930303030303036653032303034666232&amp;amp;ef=2&amp;amp;bizid=1022" aeskey="5f8b0be8f50b402f958ae70e4ddbdf8f" externurl="http*#*//vweixinf.tc.qq.com/110/20403/stodownload?m=691060e7d2154fd3dc58e6d2dd6096b3&amp;amp;filekey=3043020101042f302d02016e0402535a04203639313036306537643231353466643364633538653664326464363039366233020268e0040d00000004627466730000000131&amp;amp;hy=SZ&amp;amp;storeid=323032313036303130353139313530303033333563363836373465663763643935373538303930303030303036653033303034666233&amp;amp;ef=3&amp;amp;bizid=1022" externmd5="7c0f469041c0f52788a21938451e8b25" width="141" height="141" tpurl="" tpauthkey="" attachedtext="" attachedtextcolor="" lensid="" emojiattr="" linkid="" desc=""&gt;&lt;/emoji&gt;&lt;/msg&gt;:0\n</content>\n\t\t\t<strid />\n\t\t\t<createtime>1753570285</createtime>\n\t\t</refermsg>\n\t\t<appattach>\n\t\t\t<totallen>0</totallen>\n\t\t\t<attachid />\n\t\t\t<cdnattachurl />\n\t\t\t<emoticonmd5 />\n\t\t\t<aeskey />\n\t\t\t<fileext />\n\t\t\t<islargefilemsg>0</islargefilemsg>\n\t\t</appattach>\n\t\t<extinfo />\n\t\t<androidsource>0</androidsource>\n\t\t<thumburl />\n\t\t<mediatagname />\n\t\t<messageaction><![CDATA[]]></messageaction>\n\t\t<messageext><![CDATA[]]></messageext>\n\t\t<emoticongift>\n\t\t\t<packageflag>0</packageflag>\n\t\t\t<packageid />\n\t\t</emoticongift>\n\t\t<emoticonshared>\n\t\t\t<packageflag>0</packageflag>\n\t\t\t<packageid />\n\t\t</emoticonshared>\n\t\t<designershared>\n\t\t\t<designeruin>0</designeruin>\n\t\t\t<designername>null</designername>\n\t\t\t<designerrediretcturl><![CDATA[null]]></designerrediretcturl>\n\t\t</designershared>\n\t\t<emotionpageshared>\n\t\t\t<tid>0</tid>\n\t\t\t<title>null</title>\n\t\t\t<desc>null</desc>\n\t\t\t<iconUrl><![CDATA[null]]></iconUrl>\n\t\t\t<secondUrl>null</secondUrl>\n\t\t\t<pageType>0</pageType>\n\t\t\t<setKey>null</setKey>\n\t\t</emotionpageshared>\n\t\t<webviewshared>\n\t\t\t<shareUrlOriginal />\n\t\t\t<shareUrlOpen />\n\t\t\t<jsAppId />\n\t\t\t<publisherId />\n\t\t\t<publisherReqId />\n\t\t</webviewshared>\n\t\t<template_id />\n\t\t<md5 />\n\t\t<websearch>\n\t\t\t<rec_category>0</rec_category>\n\t\t\t<channelId>0</channelId>\n\t\t</websearch>\n\t\t<weappinfo>\n\t\t\t<username />\n\t\t\t<appid />\n\t\t\t<appservicetype>0</appservicetype>\n\t\t\t<secflagforsinglepagemode>0</secflagforsinglepagemode>\n\t\t\t<videopageinfo>\n\t\t\t\t<thumbwidth>0</thumbwidth>\n\t\t\t\t<thumbheight>0</thumbheight>\n\t\t\t\t<fromopensdk>0</fromopensdk>\n\t\t\t</videopageinfo>\n\t\t</weappinfo>\n\t\t<statextstr />\n\t\t<musicShareItem>\n\t\t\t<musicDuration>0</musicDuration>\n\t\t</musicShareItem>\n\t\t<finderLiveProductShare>\n\t\t\t<finderLiveID><![CDATA[]]></finderLiveID>\n\t\t\t<finderUsername><![CDATA[]]></finderUsername>\n\t\t\t<finderObjectID><![CDATA[]]></finderObjectID>\n\t\t\t<finderNonceID><![CDATA[]]></finderNonceID>\n\t\t\t<liveStatus><![CDATA[]]></liveStatus>\n\t\t\t<appId><![CDATA[]]></appId>\n\t\t\t<pagePath><![CDATA[]]></pagePath>\n\t\t\t<productId><![CDATA[]]></productId>\n\t\t\t<coverUrl><![CDATA[]]></coverUrl>\n\t\t\t<productTitle><![CDATA[]]></productTitle>\n\t\t\t<marketPrice><![CDATA[0]]></marketPrice>\n\t\t\t<sellingPrice><![CDATA[0]]></sellingPrice>\n\t\t\t<platformHeadImg><![CDATA[]]></platformHeadImg>\n\t\t\t<platformName><![CDATA[]]></platformName>\n\t\t\t<shopWindowId><![CDATA[]]></shopWindowId>\n\t\t\t<flashSalePrice><![CDATA[0]]></flashSalePrice>\n\t\t\t<flashSaleEndTime><![CDATA[0]]></flashSaleEndTime>\n\t\t\t<ecSource><![CDATA[]]></ecSource>\n\t\t\t<sellingPriceWording><![CDATA[]]></sellingPriceWording>\n\t\t\t<platformIconURL><![CDATA[]]></platformIconURL>\n\t\t\t<firstProductTagURL><![CDATA[]]></firstProductTagURL>\n\t\t\t<firstProductTagAspectRatioString><![CDATA[0.0]]></firstProductTagAspectRatioString>\n\t\t\t<secondProductTagURL><![CDATA[]]></secondProductTagURL>\n\t\t\t<secondProductTagAspectRatioString><![CDATA[0.0]]></secondProductTagAspectRatioString>\n\t\t\t<firstGuaranteeWording><![CDATA[]]></firstGuaranteeWording>\n\t\t\t<secondGuaranteeWording><![CDATA[]]></secondGuaranteeWording>\n\t\t\t<thirdGuaranteeWording><![CDATA[]]></thirdGuaranteeWording>\n\t\t\t<isPriceBeginShow>false</isPriceBeginShow>\n\t\t\t<lastGMsgID><![CDATA[]]></lastGMsgID>\n\t\t\t<promoterKey><![CDATA[]]></promoterKey>\n\t\t\t<discountWording><![CDATA[]]></discountWording>\n\t\t\t<priceSuffixDescription><![CDATA[]]></priceSuffixDescription>\n\t\t\t<showBoxItemStringList />\n\t\t</finderLiveProductShare>\n\t\t<finderOrder>\n\t\t\t<appID><![CDATA[]]></appID>\n\t\t\t<orderID><![CDATA[]]></orderID>\n\t\t\t<path><![CDATA[]]></path>\n\t\t\t<priceWording><![CDATA[]]></priceWording>\n\t\t\t<stateWording><![CDATA[]]></stateWording>\n\t\t\t<productImageURL><![CDATA[]]></productImageURL>\n\t\t\t<products><![CDATA[]]></products>\n\t\t\t<productsCount><![CDATA[0]]></productsCount>\n\t\t</finderOrder>\n\t\t<finderShopWindowShare>\n\t\t\t<finderUsername><![CDATA[]]></finderUsername>\n\t\t\t<avatar><![CDATA[]]></avatar>\n\t\t\t<nickname><![CDATA[]]></nickname>\n\t\t\t<commodityInStockCount><![CDATA[]]></commodityInStockCount>\n\t\t\t<appId><![CDATA[]]></appId>\n\t\t\t<path><![CDATA[]]></path>\n\t\t\t<appUsername><![CDATA[]]></appUsername>\n\t\t\t<query><![CDATA[]]></query>\n\t\t\t<liteAppId><![CDATA[]]></liteAppId>\n\t\t\t<liteAppPath><![CDATA[]]></liteAppPath>\n\t\t\t<liteAppQuery><![CDATA[]]></liteAppQuery>\n\t\t\t<platformTagURL><![CDATA[]]></platformTagURL>\n\t\t\t<saleWording><![CDATA[]]></saleWording>\n\t\t\t<lastGMsgID><![CDATA[]]></lastGMsgID>\n\t\t\t<profileTypeWording><![CDATA[]]></profileTypeWording>\n\t\t\t<reputationInfo>\n\t\t\t\t<hasReputationInfo>0</hasReputationInfo>\n\t\t\t\t<reputationScore>0</reputationScore>\n\t\t\t\t<reputationWording />\n\t\t\t\t<reputationTextColor />\n\t\t\t\t<reputationLevelWording />\n\t\t\t\t<reputationBackgroundColor />\n\t\t\t</reputationInfo>\n\t\t\t<productImageURLList />\n\t\t</finderShopWindowShare>\n\t\t<findernamecard>\n\t\t\t<username />\n\t\t\t<avatar><![CDATA[]]></avatar>\n\t\t\t<nickname />\n\t\t\t<auth_job />\n\t\t\t<auth_icon>0</auth_icon>\n\t\t\t<auth_icon_url />\n\t\t\t<ecSource><![CDATA[]]></ecSource>\n\t\t\t<lastGMsgID><![CDATA[]]></lastGMsgID>\n\t\t</findernamecard>\n\t\t<finderGuarantee>\n\t\t\t<scene><![CDATA[0]]></scene>\n\t\t</finderGuarantee>\n\t\t<directshare>0</directshare>\n\t\t<gamecenter>\n\t\t\t<namecard>\n\t\t\t\t<iconUrl />\n\t\t\t\t<name />\n\t\t\t\t<desc />\n\t\t\t\t<tail />\n\t\t\t\t<jumpUrl />\n\t\t\t</namecard>\n\t\t</gamecenter>\n\t\t<patMsg>\n\t\t\t<chatUser />\n\t\t\t<records>\n\t\t\t\t<recordNum>0</recordNum>\n\t\t\t</records>\n\t\t</patMsg>\n\t\t<secretmsg>\n\t\t\t<issecretmsg>0</issecretmsg>\n\t\t</secretmsg>\n\t\t<referfromscene>0</referfromscene>\n\t\t<gameshare>\n\t\t\t<liteappext>\n\t\t\t\t<liteappbizdata />\n\t\t\t\t<priority>0</priority>\n\t\t\t</liteappext>\n\t\t\t<appbrandext>\n\t\t\t\t<litegameinfo />\n\t\t\t\t<priority>-1</priority>\n\t\t\t</appbrandext>\n\t\t\t<gameshareid />\n\t\t\t<sharedata />\n\t\t\t<isvideo>0</isvideo>\n\t\t\t<duration>-1</duration>\n\t\t\t<isexposed>0</isexposed>\n\t\t\t<readtext />\n\t\t</gameshare>\n\t\t<mpsharetrace>\n\t\t\t<hasfinderelement>0</hasfinderelement>\n\t\t\t<lastgmsgid />\n\t\t</mpsharetrace>\n\t\t<wxgamecard>\n\t\t\t<framesetname />\n\t\t\t<mbcarddata />\n\t\t\t<minpkgversion />\n\t\t\t<mbcardheight>0</mbcardheight>\n\t\t\t<isoldversion>0</isoldversion>\n\t\t</wxgamecard>\n\t\t<liteapp>\n\t\t\t<id>null</id>\n\t\t\t<path />\n\t\t\t<query />\n\t\t</liteapp>\n\t</appmsg>\n\t<fromusername>wxid_n5c0aekjceu621</fromusername>\n\t<scene>0</scene>\n\t<appinfo>\n\t\t<version>1</version>\n\t\t<appname></appname>\n\t</appinfo>\n\t<commenturl></commenturl>\n</msg>\n'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1753570984, 'MsgSource': '<msgsource>\n\t<alnode>\n\t\t<fr>4</fr>\n\t</alnode>\n\t<sec_msg_node>\n\t\t<uuid>fdc437e63cf2313df2480458e55b688c_</uuid>\n\t\t<risk-file-flag />\n\t\t<risk-file-md5-list />\n\t</sec_msg_node>\n\t<silence>0</silence>\n\t<membercount>73</membercount>\n\t<signature>N0_V1_bXVgtEgE|v1_l0R8rzMo</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'PushContent': 'ㅤㅤㅤㅤㅤㅤㅤㅤㅤㅤㅤㅤㅤㅤㅤㅤ : 你怎么又萎了', 'NewMsgId': 8203676299194273926, 'MsgSeq': 871402658}
2025-07-27 07:02:57 | DEBUG | 从群聊消息中提取发送者: wxid_n5c0aekjceu621
2025-07-27 07:02:57 | DEBUG | 使用已解析的XML处理引用消息
2025-07-27 07:02:57 | INFO | 收到引用消息: 消息ID:604902840 来自:***********@chatroom 发送人:wxid_n5c0aekjceu621 内容:你怎么又萎了 引用类型:47
2025-07-27 07:02:58 | INFO | [DouBaoImageToImage] 收到引用消息: 你怎么又萎了
2025-07-27 07:02:58 | INFO | [TimerTask] 收到引用消息调试信息:
2025-07-27 07:02:58 | INFO |   - 消息内容: 你怎么又萎了
2025-07-27 07:02:58 | INFO |   - 群组ID: ***********@chatroom
2025-07-27 07:02:58 | INFO |   - 发送人: wxid_n5c0aekjceu621
2025-07-27 07:02:58 | INFO |   - 引用信息: {'MsgType': 47, 'Content': '<msg><emoji fromusername="last--exile" tousername="***********@chatroom" type="2" idbuffer="media*#*0_0" md5="01cc10c434ef8167d024fa7143dbea85" len="451379" productid="" androidmd5="01cc10c434ef8167d024fa7143dbea85" androidlen="451379" s60v3md5="01cc10c434ef8167d024fa7143dbea85" s60v3len="451379" s60v5md5="01cc10c434ef8167d024fa7143dbea85" s60v5len="451379" cdnurl="http*#*//vweixinf.tc.qq.com/110/20401/stodownload?m=01cc10c434ef8167d024fa7143dbea85&amp;filekey=30440201010430302e02016e0402535a04203031636331306334333465663831363764303234666137313433646265613835020306e333040d00000004627466730000000131&amp;hy=SZ&amp;storeid=323032313036303130353139313530303030373564373836373465663763643935373538303930303030303036653031303034666231&amp;ef=1&amp;bizid=1022" designerid="" thumburl="" encrypturl="http*#*//vweixinf.tc.qq.com/110/20402/stodownload?m=e5aeaa158548e31d615f849164865849&amp;filekey=30440201010430302e02016e0402535a04206535616561613135383534386533316436313566383439313634383635383439020306e340040d00000004627466730000000131&amp;hy=SZ&amp;storeid=323032313036303130353139313530303031393933393836373465663763643935373538303930303030303036653032303034666232&amp;ef=2&amp;bizid=1022" aeskey="5f8b0be8f50b402f958ae70e4ddbdf8f" externurl="http*#*//vweixinf.tc.qq.com/110/20403/stodownload?m=691060e7d2154fd3dc58e6d2dd6096b3&amp;filekey=3043020101042f302d02016e0402535a04203639313036306537643231353466643364633538653664326464363039366233020268e0040d00000004627466730000000131&amp;hy=SZ&amp;storeid=323032313036303130353139313530303033333563363836373465663763643935373538303930303030303036653033303034666233&amp;ef=3&amp;bizid=1022" externmd5="7c0f469041c0f52788a21938451e8b25" width="141" height="141" tpurl="" tpauthkey="" attachedtext="" attachedtextcolor="" lensid="" emojiattr="" linkid="" desc=""></emoji></msg>:0\n', 'Msgid': '6212810033215097293', 'NewMsgId': '6212810033215097293', 'ToWxid': 'wxid_4usgcju5ey9q29', 'FromWxid': '***********@chatroom', 'Nickname': '亮', 'MsgSource': '<msgsource>\n\t<silence>1</silence>\n\t<membercount>73</membercount>\n\t<signature>N0_V1_1xRBREHi|v1_q51iucVo</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'Createtime': '1753570285', 'SenderWxid': 'wxid_n5c0aekjceu621'}
2025-07-27 07:02:58 | INFO |   - 引用消息ID: 
2025-07-27 07:02:58 | INFO |   - 引用消息类型: 
2025-07-27 07:02:58 | INFO |   - 引用消息内容: <msg><emoji fromusername="last--exile" tousername="***********@chatroom" type="2" idbuffer="media*#*0_0" md5="01cc10c434ef8167d024fa7143dbea85" len="451379" productid="" androidmd5="01cc10c434ef8167d024fa7143dbea85" androidlen="451379" s60v3md5="01cc10c434ef8167d024fa7143dbea85" s60v3len="451379" s60v5md5="01cc10c434ef8167d024fa7143dbea85" s60v5len="451379" cdnurl="http*#*//vweixinf.tc.qq.com/110/20401/stodownload?m=01cc10c434ef8167d024fa7143dbea85&amp;filekey=30440201010430302e02016e0402535a04203031636331306334333465663831363764303234666137313433646265613835020306e333040d00000004627466730000000131&amp;hy=SZ&amp;storeid=323032313036303130353139313530303030373564373836373465663763643935373538303930303030303036653031303034666231&amp;ef=1&amp;bizid=1022" designerid="" thumburl="" encrypturl="http*#*//vweixinf.tc.qq.com/110/20402/stodownload?m=e5aeaa158548e31d615f849164865849&amp;filekey=30440201010430302e02016e0402535a04206535616561613135383534386533316436313566383439313634383635383439020306e340040d00000004627466730000000131&amp;hy=SZ&amp;storeid=323032313036303130353139313530303031393933393836373465663763643935373538303930303030303036653032303034666232&amp;ef=2&amp;bizid=1022" aeskey="5f8b0be8f50b402f958ae70e4ddbdf8f" externurl="http*#*//vweixinf.tc.qq.com/110/20403/stodownload?m=691060e7d2154fd3dc58e6d2dd6096b3&amp;filekey=3043020101042f302d02016e0402535a04203639313036306537643231353466643364633538653664326464363039366233020268e0040d00000004627466730000000131&amp;hy=SZ&amp;storeid=323032313036303130353139313530303033333563363836373465663763643935373538303930303030303036653033303034666233&amp;ef=3&amp;bizid=1022" externmd5="7c0f469041c0f52788a21938451e8b25" width="141" height="141" tpurl="" tpauthkey="" attachedtext="" attachedtextcolor="" lensid="" emojiattr="" linkid="" desc=""></emoji></msg>:0

2025-07-27 07:02:58 | INFO |   - 引用消息发送人: wxid_n5c0aekjceu621
2025-07-27 07:03:18 | DEBUG | 收到消息: {'MsgId': 1618980357, 'FromUserName': {'string': '***********@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 1, 'Content': {'string': 'wxid_n5c0aekjceu621:\n可怜的阿亮'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1753571005, 'MsgSource': '<msgsource>\n\t<pua>1</pua>\n\t<silence>0</silence>\n\t<membercount>73</membercount>\n\t<signature>N0_V1_DXVoE9tg|v1_QSghM0sQ</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'PushContent': 'ㅤㅤㅤㅤㅤㅤㅤㅤㅤㅤㅤㅤㅤㅤㅤㅤ : 可怜的阿亮', 'NewMsgId': 5554887583658813633, 'MsgSeq': 871402659}
2025-07-27 07:03:18 | INFO | 收到文本消息: 消息ID:1618980357 来自:***********@chatroom 发送人:wxid_n5c0aekjceu621 @:[] 内容:可怜的阿亮
2025-07-27 07:03:19 | DEBUG | 处理消息内容: '可怜的阿亮'
2025-07-27 07:03:19 | DEBUG | 消息内容 '可怜的阿亮' 不匹配任何命令，忽略
2025-07-27 07:05:03 | DEBUG | 收到消息: {'MsgId': 344906094, 'FromUserName': {'string': '***********@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 47, 'Content': {'string': 'wxid_n5c0aekjceu621:\n<msg><emoji fromusername = "wxid_n5c0aekjceu621" tousername = "***********@chatroom" type="1" idbuffer="media:0_0" md5="1baa03994495da83e62bd968a9d621a0" len = "1451016" productid="" androidmd5="1baa03994495da83e62bd968a9d621a0" androidlen="1451016" s60v3md5 = "1baa03994495da83e62bd968a9d621a0" s60v3len="1451016" s60v5md5 = "1baa03994495da83e62bd968a9d621a0" s60v5len="1451016" cdnurl = "http://wxapp.tc.qq.com/262/20304/stodownload?m=1baa03994495da83e62bd968a9d621a0&amp;filekey=30350201010421301f020201060402534804101baa03994495da83e62bd968a9d621a00203162408040d00000004627466730000000132&amp;hy=SH&amp;storeid=2630642220003dcc7000000000000010600004f5053480bc64b40b66f43d08&amp;bizid=1023" designerid = "" thumburl = "" encrypturl = "http://wxapp.tc.qq.com/262/20304/stodownload?m=002f6228f52a40b35331904cde611bec&amp;filekey=30350201010421301f02020106040253480410002f6228f52a40b35331904cde611bec0203162410040d00000004627466730000000132&amp;hy=SH&amp;storeid=263064222000d009a000000000000010600004f5053481bc64b40b66faa6ab&amp;bizid=1023" aeskey= "3b4839abef99aedfa99bd461b18f67c2" externurl = "http://wxapp.tc.qq.com/262/20304/stodownload?m=30697f7fd5a483991ac9b4d5e800753c&amp;filekey=30350201010421301f0202010604025348041030697f7fd5a483991ac9b4d5e800753c0203017070040d00000004627466730000000132&amp;hy=SH&amp;storeid=263064223000309eb000000000000010600004f5053482d36fb40b66f71d79&amp;bizid=1023" externmd5 = "bd4b616ea36ccd509575b987304d3b97" width= "240" height= "240" tpurl= "" tpauthkey= "" attachedtext= "" attachedtextcolor= "" lensid= "" emojiattr= "" linkid= "" desc= "" ></emoji>  </msg>'}, 'Status': 3, 'ImgStatus': 2, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1753571110, 'MsgSource': '<msgsource>\n\t<silence>0</silence>\n\t<membercount>73</membercount>\n\t<signature>N0_V1_WHN4bH7D|v1_OkDqwRsl</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'PushContent': 'ㅤㅤㅤㅤㅤㅤㅤㅤㅤㅤㅤㅤㅤㅤㅤㅤ在群聊中发了一个表情', 'NewMsgId': 2633286591981933111, 'MsgSeq': 871402660}
2025-07-27 07:05:03 | INFO | 收到表情消息: 消息ID:344906094 来自:***********@chatroom 发送人:wxid_n5c0aekjceu621 MD5:1baa03994495da83e62bd968a9d621a0 大小:1451016
2025-07-27 07:05:04 | DEBUG | [DoubaoImageRecognition] 缓存表情消息: 2633286591981933111
2025-07-27 07:05:16 | DEBUG | 收到消息: {'MsgId': 234092157, 'FromUserName': {'string': '***********@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 1, 'Content': {'string': 'wxid_n5c0aekjceu621:\n我的硬邦邦[偷笑]'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1753571123, 'MsgSource': '<msgsource>\n\t<pua>1</pua>\n\t<silence>0</silence>\n\t<membercount>73</membercount>\n\t<signature>N0_V1_cjztRa8c|v1_xI2vKRrV</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'PushContent': 'ㅤㅤㅤㅤㅤㅤㅤㅤㅤㅤㅤㅤㅤㅤㅤㅤ : 我的硬邦邦[偷笑]', 'NewMsgId': 4699988240862426397, 'MsgSeq': 871402661}
2025-07-27 07:05:16 | INFO | 收到文本消息: 消息ID:234092157 来自:***********@chatroom 发送人:wxid_n5c0aekjceu621 @:[] 内容:我的硬邦邦[偷笑]
2025-07-27 07:05:17 | DEBUG | 处理消息内容: '我的硬邦邦[偷笑]'
2025-07-27 07:05:17 | DEBUG | 消息内容 '我的硬邦邦[偷笑]' 不匹配任何命令，忽略
2025-07-27 07:08:15 | DEBUG | 收到消息: {'MsgId': 1675517569, 'FromUserName': {'string': '***********@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 47, 'Content': {'string': 'last--exile:\n<msg><emoji fromusername="last--exile" tousername="***********@chatroom" type="2" idbuffer="media:0_0" md5="4b27b38ec1621c19c39123470d654855" len="51810" productid="" androidmd5="4b27b38ec1621c19c39123470d654855" androidlen="51810" s60v3md5="4b27b38ec1621c19c39123470d654855" s60v3len="51810" s60v5md5="4b27b38ec1621c19c39123470d654855" s60v5len="51810" cdnurl="http://wxapp.tc.qq.com/262/20304/stodownload?m=4b27b38ec1621c19c39123470d654855&amp;filekey=30350201010421301f020201060402535a04104b27b38ec1621c19c39123470d654855020300ca62040d00000004627466730000000131&amp;hy=SZ&amp;storeid=32303232303430373130323630353030303337633065396164343932613866343630383830393030303030313036&amp;bizid=1023" designerid="" thumburl="" encrypturl="http://wxapp.tc.qq.com/262/20304/stodownload?m=120e4bb6ba754a3c29fdefb59197048e&amp;filekey=30350201010421301f020201060402535a0410120e4bb6ba754a3c29fdefb59197048e020300ca70040d00000004627466730000000131&amp;hy=SZ&amp;storeid=32303232303430373130323630353030303830623563396164343932613866343630383830393030303030313036&amp;bizid=1023" aeskey="464a7a0bfdff973e237d168e6e965c27" externurl="" externmd5="" width="200" height="183" tpurl="" tpauthkey="" attachedtext="" attachedtextcolor="" lensid="" emojiattr="" linkid="" desc=""></emoji></msg>'}, 'Status': 3, 'ImgStatus': 2, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1753571301, 'MsgSource': '<msgsource>\n\t<silence>0</silence>\n\t<membercount>73</membercount>\n\t<signature>N0_V1_aIxq3+Yn|v1_FlisQ1E/</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'PushContent': '亮在群聊中发了一个表情', 'NewMsgId': 2167411905408629254, 'MsgSeq': 871402662}
2025-07-27 07:08:15 | INFO | 收到表情消息: 消息ID:1675517569 来自:***********@chatroom 发送人:last--exile MD5:4b27b38ec1621c19c39123470d654855 大小:51810
2025-07-27 07:08:16 | DEBUG | [DoubaoImageRecognition] 缓存表情消息: 2167411905408629254
2025-07-27 07:10:32 | DEBUG | 收到消息: {'MsgId': 1369336789, 'FromUserName': {'string': '***********@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 1, 'Content': {'string': 'wxid_l9koi6kli78i22:\n[坏笑][坏笑]'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1753571439, 'MsgSource': '<msgsource>\n\t<pua>1</pua>\n\t<silence>0</silence>\n\t<membercount>73</membercount>\n\t<signature>N0_V1_0IFwIt+M|v1_9Yp0OjV8</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'PushContent': '十五 : [坏笑][坏笑]', 'NewMsgId': 5763480912387991154, 'MsgSeq': 871402663}
2025-07-27 07:10:32 | INFO | 收到表情消息: 消息ID:1369336789 来自:***********@chatroom 发送人:wxid_l9koi6kli78i22 @:[] 内容:[坏笑][坏笑]
2025-07-27 07:10:33 | DEBUG | [DoubaoImageRecognition] 缓存表情消息: 5763480912387991154
2025-07-27 07:14:28 | DEBUG | 收到消息: {'MsgId': 1055527486, 'FromUserName': {'string': '***********@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 49, 'Content': {'string': 'last--exile:\n<?xml version="1.0"?>\n<msg>\n\t<appmsg appid="" sdkver="0">\n\t\t<title>千万粉丝网红道歉，热搜第一！演唱会延期</title>\n\t\t<des />\n\t\t<username />\n\t\t<action>view</action>\n\t\t<type>5</type>\n\t\t<showtype>0</showtype>\n\t\t<content />\n\t\t<url>https://mp.weixin.qq.com/s?__biz=MTk1MjIwODAwMQ==&amp;mid=2651814933&amp;idx=1&amp;sn=92255cd11a29980a52b9a04aa7c687e5&amp;chksm=46fdc353219796186e7a2e7726393f61bc5840d5f126c862e36ae270acee6042f120684594b4&amp;mpshare=1&amp;scene=1&amp;srcid=0727IQQPQaExAq227WZCjmNg&amp;sharer_shareinfo=1e7064b03d5d2bd068512f88a352576c&amp;sharer_shareinfo_first=1e7064b03d5d2bd068512f88a352576c#rd</url>\n\t\t<lowurl />\n\t\t<forwardflag>0</forwardflag>\n\t\t<dataurl />\n\t\t<lowdataurl />\n\t\t<contentattr>0</contentattr>\n\t\t<streamvideo>\n\t\t\t<streamvideourl />\n\t\t\t<streamvideototaltime>0</streamvideototaltime>\n\t\t\t<streamvideotitle />\n\t\t\t<streamvideowording />\n\t\t\t<streamvideoweburl />\n\t\t\t<streamvideothumburl />\n\t\t\t<streamvideoaduxinfo />\n\t\t\t<streamvideopublishid />\n\t\t</streamvideo>\n\t\t<canvasPageItem>\n\t\t\t<canvasPageXml><![CDATA[]]></canvasPageXml>\n\t\t</canvasPageItem>\n\t\t<appattach>\n\t\t\t<attachid />\n\t\t\t<cdnthumburl>3057020100044b30490201000204035f493102032f56c20204aeaaa27402046885615a042430626562336364332d383730342d346534662d623632332d3135653337383939636262320204051808030201000405004c54a200</cdnthumburl>\n\t\t\t<cdnthumbmd5>28f6f0fceba3813696eae80f35ef929d</cdnthumbmd5>\n\t\t\t<cdnthumblength>23317</cdnthumblength>\n\t\t\t<cdnthumbheight>120</cdnthumbheight>\n\t\t\t<cdnthumbwidth>120</cdnthumbwidth>\n\t\t\t<cdnthumbaeskey>0c30e4e36e68b02d4dc5836f9d830fdd</cdnthumbaeskey>\n\t\t\t<aeskey>0c30e4e36e68b02d4dc5836f9d830fdd</aeskey>\n\t\t\t<encryver>1</encryver>\n\t\t\t<fileext />\n\t\t\t<islargefilemsg>0</islargefilemsg>\n\t\t</appattach>\n\t\t<extinfo />\n\t\t<androidsource>2</androidsource>\n\t\t<sourceusername>wxid_4783107810411</sourceusername>\n\t\t<sourcedisplayname>南方都市报</sourcedisplayname>\n\t\t<commenturl />\n\t\t<thumburl />\n\t\t<mediatagname />\n\t\t<messageaction><![CDATA[]]></messageaction>\n\t\t<messageext><![CDATA[]]></messageext>\n\t\t<emoticongift>\n\t\t\t<packageflag>0</packageflag>\n\t\t\t<packageid />\n\t\t</emoticongift>\n\t\t<emoticonshared>\n\t\t\t<packageflag>0</packageflag>\n\t\t\t<packageid />\n\t\t</emoticonshared>\n\t\t<designershared>\n\t\t\t<designeruin>0</designeruin>\n\t\t\t<designername>null</designername>\n\t\t\t<designerrediretcturl><![CDATA[null]]></designerrediretcturl>\n\t\t</designershared>\n\t\t<emotionpageshared>\n\t\t\t<tid>0</tid>\n\t\t\t<title>null</title>\n\t\t\t<desc>null</desc>\n\t\t\t<iconUrl><![CDATA[null]]></iconUrl>\n\t\t\t<secondUrl>null</secondUrl>\n\t\t\t<pageType>0</pageType>\n\t\t\t<setKey>null</setKey>\n\t\t</emotionpageshared>\n\t\t<webviewshared>\n\t\t\t<shareUrlOriginal>http://mp.weixin.qq.com/s?__biz=MTk1MjIwODAwMQ==&amp;mid=2651814933&amp;idx=1&amp;sn=92255cd11a29980a52b9a04aa7c687e5&amp;chksm=4680030a99f4ff38b2c82ba627ccdf21b31714341e02d444b1b52163548e2c7d5d923a1b39d0&amp;xtrack=1&amp;scene=90&amp;subscene=93&amp;sessionid=1753571649&amp;flutter_pos=2&amp;clicktime=1753571656&amp;enterid=1753571656&amp;finder_biz_enter_id=4&amp;ranksessionid=1753570986&amp;ascene=56&amp;fasttmpl_type=0&amp;fasttmpl_fullversion=7835612-zh_CN-zip&amp;fasttmpl_flag=0&amp;realreporttime=1753571657011#rd</shareUrlOriginal>\n\t\t\t<shareUrlOpen>https://mp.weixin.qq.com/s?__biz=MTk1MjIwODAwMQ==&amp;mid=2651814933&amp;idx=1&amp;sn=92255cd11a29980a52b9a04aa7c687e5&amp;chksm=4680030a99f4ff38b2c82ba627ccdf21b31714341e02d444b1b52163548e2c7d5d923a1b39d0&amp;xtrack=1&amp;scene=90&amp;subscene=93&amp;sessionid=1753571649&amp;flutter_pos=2&amp;clicktime=1753571656&amp;enterid=1753571656&amp;finder_biz_enter_id=4&amp;ranksessionid=1753570986&amp;ascene=56&amp;fasttmpl_type=0&amp;fasttmpl_fullversion=7835612-zh_CN-zip&amp;fasttmpl_flag=0&amp;realreporttime=1753571657011&amp;devicetype=android-34&amp;version=28003133&amp;nettype=WIFI&amp;lang=zh_CN&amp;countrycode=CN&amp;exportkey=n_ChQIAhIQf1Gl2u857oMmUSwlYt3R1RLrAQIE97dBBAEAAAAAAOzLLgbRiV4AAAAOpnltbLcz9gKNyK89dVj0SpEjS3JxDA2HtMvC%2Bj%2FNhd46%2FJnK%2B56I3t8WUzf1Y%2FV5uF3VC4YDeSskEUIc8CEskVC1JsBzwEjaoxueF6Ht86bpWBHyftEC706jJnvi1Jep2XL2xeWe9EUEIfNC9n7WFEPaDJfic8bn0fp0jUYB09nMo%2BJ7EdjmgXSR2ktzXPYW7tdELuguspRbkhDqNo8mXG6sP2wMCgJsVDN8D7%2BH%2FaQ1OmVMTAOhJOwcRHrWRR9nU%2FSG8sewy4QMSJumNqdxyGLBfAo%3D&amp;pass_ticket=FvNleIi%2FIicPXQHyzihKi4NeUdr7esk96EkPVCAV3SM9RprShLgvxHutGyenpMMm&amp;wx_header=3</shareUrlOpen>\n\t\t\t<jsAppId />\n\t\t\t<publisherId>msg_10447016</publisherId>\n\t\t\t<publisherReqId>1224994285</publisherReqId>\n\t\t</webviewshared>\n\t\t<template_id />\n\t\t<md5>28f6f0fceba3813696eae80f35ef929d</md5>\n\t\t<websearch>\n\t\t\t<rec_category>0</rec_category>\n\t\t\t<channelId>0</channelId>\n\t\t</websearch>\n\t\t<weappinfo>\n\t\t\t<username />\n\t\t\t<appid />\n\t\t\t<appservicetype>0</appservicetype>\n\t\t\t<secflagforsinglepagemode>0</secflagforsinglepagemode>\n\t\t\t<videopageinfo>\n\t\t\t\t<thumbwidth>120</thumbwidth>\n\t\t\t\t<thumbheight>120</thumbheight>\n\t\t\t\t<fromopensdk>0</fromopensdk>\n\t\t\t</videopageinfo>\n\t\t</weappinfo>\n\t\t<statextstr />\n\t\t<mmreadershare>\n\t\t\t<itemshowtype>0</itemshowtype>\n\t\t\t<ispaysubscribe>0</ispaysubscribe>\n\t\t</mmreadershare>\n\t\t<musicShareItem>\n\t\t\t<musicDuration>0</musicDuration>\n\t\t</musicShareItem>\n\t\t<finderLiveProductShare>\n\t\t\t<finderLiveID><![CDATA[]]></finderLiveID>\n\t\t\t<finderUsername><![CDATA[]]></finderUsername>\n\t\t\t<finderObjectID><![CDATA[]]></finderObjectID>\n\t\t\t<finderNonceID><![CDATA[]]></finderNonceID>\n\t\t\t<liveStatus><![CDATA[]]></liveStatus>\n\t\t\t<appId><![CDATA[]]></appId>\n\t\t\t<pagePath><![CDATA[]]></pagePath>\n\t\t\t<productId><![CDATA[]]></productId>\n\t\t\t<coverUrl><![CDATA[]]></coverUrl>\n\t\t\t<productTitle><![CDATA[]]></productTitle>\n\t\t\t<marketPrice><![CDATA[0]]></marketPrice>\n\t\t\t<sellingPrice><![CDATA[0]]></sellingPrice>\n\t\t\t<platformHeadImg><![CDATA[]]></platformHeadImg>\n\t\t\t<platformName><![CDATA[]]></platformName>\n\t\t\t<shopWindowId><![CDATA[]]></shopWindowId>\n\t\t\t<flashSalePrice><![CDATA[0]]></flashSalePrice>\n\t\t\t<flashSaleEndTime><![CDATA[0]]></flashSaleEndTime>\n\t\t\t<ecSource><![CDATA[]]></ecSource>\n\t\t\t<sellingPriceWording><![CDATA[]]></sellingPriceWording>\n\t\t\t<platformIconURL><![CDATA[]]></platformIconURL>\n\t\t\t<firstProductTagURL><![CDATA[]]></firstProductTagURL>\n\t\t\t<firstProductTagAspectRatioString><![CDATA[0.0]]></firstProductTagAspectRatioString>\n\t\t\t<secondProductTagURL><![CDATA[]]></secondProductTagURL>\n\t\t\t<secondProductTagAspectRatioString><![CDATA[0.0]]></secondProductTagAspectRatioString>\n\t\t\t<firstGuaranteeWording><![CDATA[]]></firstGuaranteeWording>\n\t\t\t<secondGuaranteeWording><![CDATA[]]></secondGuaranteeWording>\n\t\t\t<thirdGuaranteeWording><![CDATA[]]></thirdGuaranteeWording>\n\t\t\t<isPriceBeginShow>false</isPriceBeginShow>\n\t\t\t<lastGMsgID><![CDATA[]]></lastGMsgID>\n\t\t\t<promoterKey><![CDATA[]]></promoterKey>\n\t\t\t<discountWording><![CDATA[]]></discountWording>\n\t\t\t<priceSuffixDescription><![CDATA[]]></priceSuffixDescription>\n\t\t\t<showBoxItemStringList />\n\t\t</finderLiveProductShare>\n\t\t<finderOrder>\n\t\t\t<appID><![CDATA[]]></appID>\n\t\t\t<orderID><![CDATA[]]></orderID>\n\t\t\t<path><![CDATA[]]></path>\n\t\t\t<priceWording><![CDATA[]]></priceWording>\n\t\t\t<stateWording><![CDATA[]]></stateWording>\n\t\t\t<productImageURL><![CDATA[]]></productImageURL>\n\t\t\t<products><![CDATA[]]></products>\n\t\t\t<productsCount><![CDATA[0]]></productsCount>\n\t\t</finderOrder>\n\t\t<finderShopWindowShare>\n\t\t\t<finderUsername><![CDATA[]]></finderUsername>\n\t\t\t<avatar><![CDATA[]]></avatar>\n\t\t\t<nickname><![CDATA[]]></nickname>\n\t\t\t<commodityInStockCount><![CDATA[]]></commodityInStockCount>\n\t\t\t<appId><![CDATA[]]></appId>\n\t\t\t<path><![CDATA[]]></path>\n\t\t\t<appUsername><![CDATA[]]></appUsername>\n\t\t\t<query><![CDATA[]]></query>\n\t\t\t<liteAppId><![CDATA[]]></liteAppId>\n\t\t\t<liteAppPath><![CDATA[]]></liteAppPath>\n\t\t\t<liteAppQuery><![CDATA[]]></liteAppQuery>\n\t\t\t<platformTagURL><![CDATA[]]></platformTagURL>\n\t\t\t<saleWording><![CDATA[]]></saleWording>\n\t\t\t<lastGMsgID><![CDATA[]]></lastGMsgID>\n\t\t\t<profileTypeWording><![CDATA[]]></profileTypeWording>\n\t\t\t<reputationInfo>\n\t\t\t\t<hasReputationInfo>0</hasReputationInfo>\n\t\t\t\t<reputationScore>0</reputationScore>\n\t\t\t\t<reputationWording />\n\t\t\t\t<reputationTextColor />\n\t\t\t\t<reputationLevelWording />\n\t\t\t\t<reputationBackgroundColor />\n\t\t\t</reputationInfo>\n\t\t\t<productImageURLList />\n\t\t</finderShopWindowShare>\n\t\t<findernamecard>\n\t\t\t<username />\n\t\t\t<avatar><![CDATA[]]></avatar>\n\t\t\t<nickname />\n\t\t\t<auth_job />\n\t\t\t<auth_icon>0</auth_icon>\n\t\t\t<auth_icon_url />\n\t\t\t<ecSource><![CDATA[]]></ecSource>\n\t\t\t<lastGMsgID><![CDATA[]]></lastGMsgID>\n\t\t</findernamecard>\n\t\t<finderGuarantee>\n\t\t\t<scene><![CDATA[0]]></scene>\n\t\t</finderGuarantee>\n\t\t<directshare>0</directshare>\n\t\t<gamecenter>\n\t\t\t<namecard>\n\t\t\t\t<iconUrl />\n\t\t\t\t<name />\n\t\t\t\t<desc />\n\t\t\t\t<tail />\n\t\t\t\t<jumpUrl />\n\t\t\t</namecard>\n\t\t</gamecenter>\n\t\t<patMsg>\n\t\t\t<chatUser />\n\t\t\t<records>\n\t\t\t\t<recordNum>0</recordNum>\n\t\t\t</records>\n\t\t</patMsg>\n\t\t<secretmsg>\n\t\t\t<issecretmsg>0</issecretmsg>\n\t\t</secretmsg>\n\t\t<referfromscene>0</referfromscene>\n\t\t<gameshare>\n\t\t\t<liteappext>\n\t\t\t\t<liteappbizdata />\n\t\t\t\t<priority>0</priority>\n\t\t\t</liteappext>\n\t\t\t<appbrandext>\n\t\t\t\t<litegameinfo />\n\t\t\t\t<priority>-1</priority>\n\t\t\t</appbrandext>\n\t\t\t<gameshareid />\n\t\t\t<sharedata />\n\t\t\t<isvideo>0</isvideo>\n\t\t\t<duration>-1</duration>\n\t\t\t<isexposed>0</isexposed>\n\t\t\t<readtext />\n\t\t</gameshare>\n\t\t<mpsharetrace>\n\t\t\t<hasfinderelement>1</hasfinderelement>\n\t\t\t<lastgmsgid />\n\t\t</mpsharetrace>\n\t\t<wxgamecard>\n\t\t\t<framesetname />\n\t\t\t<mbcarddata />\n\t\t\t<minpkgversion />\n\t\t\t<mbcardheight>0</mbcardheight>\n\t\t\t<isoldversion>0</isoldversion>\n\t\t</wxgamecard>\n\t\t<liteapp>\n\t\t\t<id>null</id>\n\t\t\t<path />\n\t\t\t<query />\n\t\t</liteapp>\n\t</appmsg>\n\t<fromusername>last--exile</fromusername>\n\t<scene>0</scene>\n\t<appinfo>\n\t\t<version>1</version>\n\t\t<appname />\n\t</appinfo>\n\t<commenturl />\n</msg>\n'}, 'Status': 3, 'ImgStatus': 2, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1753571674, 'MsgSource': '<msgsource>\n\t<sec_msg_node>\n\t\t<uuid>1fe2611edb6582f0943d7bbbf45fd2d5_</uuid>\n\t\t<risk-file-flag />\n\t\t<risk-file-md5-list />\n\t</sec_msg_node>\n\t<silence>0</silence>\n\t<membercount>73</membercount>\n\t<signature>N0_V1_MSUwqLTw|v1_el2vGw7R</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'PushContent': '亮 : [链接]千万粉丝网红道歉，热搜第一！演唱会延期', 'NewMsgId': 3464418481215720485, 'MsgSeq': 871402664}
2025-07-27 07:14:28 | DEBUG | 从群聊消息中提取发送者: last--exile
2025-07-27 07:14:28 | INFO | 收到公众号文章消息: 消息ID:1055527486 来自:***********@chatroom
2025-07-27 07:14:28 | ERROR | 解析XML失败: mismatched tag: line 1, column 384
2025-07-27 07:14:28 | DEBUG | [ArticleForwarder] 检测到XML开头被截断，尝试修复
2025-07-27 07:14:28 | DEBUG | [ArticleForwarder] 已修复XML开头截断问题
2025-07-27 07:14:28 | DEBUG | 尝试使用修复后的XML重新解析
2025-07-27 07:14:28 | DEBUG | 从sourcedisplayname提取到公众号: 南方都市报
2025-07-27 07:14:28 | DEBUG | 公众号「南方都市报」不在监控列表中，跳过处理
2025-07-27 07:14:32 | INFO | [TempFileManager] 开始清理临时文件...
2025-07-27 07:14:33 | DEBUG | 开始检查群成员变化，当前监控 2 个群
2025-07-27 07:14:34 | DEBUG | 群成员变化检查完成
2025-07-27 07:44:32 | INFO | [TempFileManager] 开始清理临时文件...
2025-07-27 07:59:12 | DEBUG | 收到消息: {'MsgId': 1607469168, 'FromUserName': {'string': 'wxid_4usgcju5ey9q29'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 51, 'Content': {'string': '<msg>\n<op id=\'11\'>\n<name>HandOffMaster</name>\n<arg><handofflist opcode="4" seq="140" devicevirtualid="9e2cfabb98fc867b47142075475cf08b" networkstatus="4g" availablecount="1000">\n        \n        </handofflist></arg>\n</op>\n</msg>'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1753574359, 'MsgSource': '<msgsource>\n\t<signature>v1_biHNvKKO</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'NewMsgId': 5596551275928317907, 'MsgSeq': 871402665}
2025-07-27 07:59:29 | DEBUG | 收到消息: {'MsgId': 689483140, 'FromUserName': {'string': 'wxid_7d30li5vpc4722'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 10000, 'Content': {'string': '你已添加了易点，现在可以开始聊天了。'}, 'Status': 4, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1753574376, 'MsgSource': '<msgsource>\n\t<signature>v1_iZ4e+LdA</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'NewMsgId': 7076426265996922031, 'MsgSeq': 871402666}
2025-07-27 07:59:29 | INFO | 未知的消息类型: {'MsgId': 689483140, 'ToWxid': 'wxid_4usgcju5ey9q29', 'MsgType': 10000, 'Content': {'string': '你已添加了易点，现在可以开始聊天了。'}, 'Status': 4, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1753574376, 'MsgSource': '<msgsource>\n\t<signature>v1_iZ4e+LdA</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'NewMsgId': 7076426265996922031, 'MsgSeq': 871402666, 'FromWxid': 'wxid_7d30li5vpc4722'}
