﻿import json
import re
import tomllib
import tomli_w
import traceback
import asyncio
import os
import time
import uuid
import hmac
import hashlib
import base64
from typing import Optional, Dict, Any, List, AsyncGenerator
from datetime import datetime, timedelta
from urllib.parse import quote

import httpx
import aiofiles
from loguru import logger
from pathlib import Path

from WechatAPI import WechatAPIClient
from utils.decorators import *
from utils.plugin_base import PluginBase
from plugins.text2card_project.text_card_service import TextCardService, TextCardError

class VivoAgentsPlugin(PluginBase):
    """vivo智能体插件"""
    
    description = "vivo智能体 - 多智能体聊天系统"
    author = "Claude"
    version = "1.0.0"
    plugin_name = "VivoAgents"

    def __init__(self):
        super().__init__()
        
        # 读取配置
        with open("plugins/VivoAgents/config.toml", "rb") as f:
            plugin_config = tomllib.load(f)
        config = plugin_config.get("VivoAgents", {})
        
        # 基本配置
        self.enable = config.get("enable", True)
        self.command_format = config.get("command-format", "")
        
        # TTS配置
        self.tts_url = config.get("tts-url", "http://www.yx520.ltd/API/wzzyy/zmp3.php")
        self.tts_voice = config.get("tts-voice", "318")
        
        # API配置
        self.api_url = config.get("api-url", "")
        self.app_id = config.get("app-id", "")
        self.uid = config.get("uid", "")
        
        # 登录凭证
        self.csrf_token = config.get("csrf-token", "")
        self.cookies = config.get("cookies", "")
        
        # 登录凭证自动更新配置
        self.username = config.get("username", "")
        self.password = config.get("password", "")
        self.auto_refresh = config.get("auto-refresh", True)
        self.last_refresh_time = datetime.now()
        self.refresh_interval = timedelta(hours=12)
        
        # 智能体配置
        self.agents = {}
        agents_config = config.get("agents", {})

        
        # 默认头像配置
        self.default_headers = {
            "悟空": "C:\\XYBotV2\\data\\default picture\\wukong.png",
            "黛玉": "C:\\XYBotV2\\data\\default picture\\daiyu.jpg",
            "卡斯特": "C:\\XYBotV2\\data\\default picture\\kasite.JPG"
        }
        
        for agent_id, agent_config in agents_config.items():
            try:
                agent_data = {
                    "name": agent_config.get("name", ""),
                    "agent_id": agent_config.get("agent-id", ""),
                    "command": agent_config.get("command", []),
                    "description": agent_config.get("description", ""),
                    "tts-voice": str(agent_config.get("tts-voice", self.tts_voice))
                }
                
                # 检查必要字段
                if not agent_data["name"] or not agent_data["agent_id"] or not agent_data["command"]:
                    logger.error(f"[{self.plugin_name}] 智能体 {agent_id} 缺少必要配置")
                    continue
                    
                self.agents[agent_id] = agent_data
            except Exception as e:
                logger.error(f"[{self.plugin_name}] 加载智能体 {agent_id} 失败: {str(e)}")
                logger.error(traceback.format_exc())
        

        
        # 初始化TextCardService
        self.text_card = TextCardService()
        
        # 会话管理
        self._sessions = {}  # 存储会话ID，格式：{f"{group_wxid}_{sender_wxid}": session_id}
        
        # 会话模式管理 - 使用组合键
        self._chat_sessions = {}  # {f"{group_wxid}_{sender_wxid}": {"agent": agent, "last_active": datetime}}
        self.session_timeout = timedelta(minutes=5)
        
        # 启动会话清理任务
        asyncio.create_task(self._clean_expired_sessions())
        
        # 临时存储
        self._temp_dir = "temp/VivoAgents"
        os.makedirs(self._temp_dir, exist_ok=True)
        
        # 限流配置
        rate_limit_config = config.get("rate_limit", {})
        self.tokens_per_second = rate_limit_config.get("tokens_per_second", 0.1)
        self.bucket_size = rate_limit_config.get("bucket_size", 3)
        self.tokens = self.bucket_size
        self.last_token_time = time.time()
        
        # 用户限流字典
        self.user_last_request = {}
        
        # 异步加载配置任务
        asyncio.create_task(self._async_load_config())

    async def _async_load_config(self):
        """异步加载配置"""
        try:
            async with aiofiles.open("plugins/VivoAgents/config.toml", "r", encoding="utf-8") as f:
                content = await f.read()
                plugin_config = tomllib.loads(content)
            config = plugin_config.get("VivoAgents", {})
            
            # 更新配置
            self.enable = config.get("enable", True)
            self.command_format = config.get("command-format", "")
            self.tts_url = config.get("tts-url", "http://www.yx520.ltd/API/wzzyy/zmp3.php")
            self.tts_voice = config.get("tts-voice", "318")
            self.api_url = config.get("api-url", "")
            self.app_id = config.get("app-id", "")
            self.uid = config.get("uid", "")
            self.csrf_token = config.get("csrf-token", "")
            self.cookies = config.get("cookies", "")
            self.username = config.get("username", "")
            self.password = config.get("password", "")
            self.auto_refresh = config.get("auto-refresh", True)
            

        except Exception as e:
            logger.error(f"[{self.plugin_name}] 异步加载配置失败: {str(e)}")
            logger.error(traceback.format_exc())

    def _get_session_key(self, group_wxid: str, sender_wxid: str) -> str:
        """生成会话键"""
        return f"{group_wxid}_{sender_wxid}"

    async def _clean_expired_sessions(self):
        """定期清理过期会话"""
        while True:
            try:
                current_time = datetime.now()
                expired_sessions = []
                
                for session_key, session in self._chat_sessions.items():
                    if current_time - session["last_active"] > self.session_timeout:
                        expired_sessions.append(session_key)
                        
                if expired_sessions:
                    # 创建一个新的WechatAPIClient实例，使用默认配置
                    bot = WechatAPIClient(ip="127.0.0.1", port=8680)
                    for session_key in expired_sessions:
                        session = self._chat_sessions.pop(session_key)
                        group_wxid = session_key.split("_")[0]
                        try:
                            await bot.send_text_message(
                                group_wxid,
                                f"看起来你已经很久没说话啦～我先去休息了哦,需要我的时候随时叫我就好啦! 😴"
                            )
                        except:
                            pass

                        
            except Exception as e:
                logger.error(f"[{self.plugin_name}] 清理过期会话出错: {e}")
                
            await asyncio.sleep(60)

    def _update_session_activity(self, group_wxid: str, sender_wxid: str):
        """更新会话活跃时间"""
        session_key = self._get_session_key(group_wxid, sender_wxid)
        if session_key in self._chat_sessions:
            self._chat_sessions[session_key]["last_active"] = datetime.now()

    async def _process_stream(self, response: httpx.Response, agent: Dict[str, Any]) -> str:
        """处理SSE流式响应"""
        buffer = []
        current_event = None
        try:
            # 获取Stream迭代器
            async for line in response.aiter_lines():
                if line:
                    line = line.strip()
                    
                    # 检查登录过期
                    try:
                        error_data = json.loads(line)
                        if error_data.get("code") == 20002:
                            raise ValueError("登录信息已过期，请联系管理员更新登录信息")
                    except json.JSONDecodeError:
                        pass
                    
                    if line.startswith('event:'):
                        current_event = line[6:].strip()
                        continue
                        
                    if line.startswith('data:'):
                        data = line[5:].strip()
                        
                        if data == "[DONE]":
                            break
                            
                        if current_event == 'error':
                            logger.error(f"[{self.plugin_name}] 服务器返回错误: {data}")
                            raise ValueError(f"服务器返回错误: {data}")
                            
                        try:
                            json_data = json.loads(data)
                            message = json_data.get("message", "")
                            if message and message not in buffer and not json_data.get("end", False):
                                buffer.append(message)
                        except json.JSONDecodeError as e:
                            logger.warning(f"[{self.plugin_name}] JSON解析失败: {e}, 原始数据: {data}")
                            continue
                            
            # 先获取最后一条消息
            result = buffer[-1] if buffer else ""
            
            # 处理所有以"哈哈"开头的消息
            if result.startswith("哈哈"):
                # 删除开头的"哈哈"和后面的逗号
                result = result[2:].lstrip("，").lstrip(",")
            
            return result
            
        except Exception as e:
            logger.error(f"[{self.plugin_name}] 处理响应流时出错: {e}")
            raise

    async def _generate_card(self, text: str, agent: Dict[str, Any]) -> Optional[str]:
        """生成图片卡片"""
        temp_path = None
        try:
            temp_path = f"{self._temp_dir}/card_{int(time.time())}.png"
            
            # 获取智能体对应的默认头像
            agent_name = agent.get("name", "")
            header_image = self.default_headers.get(agent_name)
            
            if not header_image or not os.path.exists(header_image):
                logger.warning(f"[{self.plugin_name}] 智能体 {agent_name} 的默认头像不存在")
                # 直接发送纯文本
                return None
            
            # 使用本地默认头像

            if len(text) <= 200:
                await self.text_card.generate_card(
                    text=text,
                    title_image=header_image,
                    output_path=temp_path,
                    width=600,
                    is_dark=False,
                    font_size=18,
                    line_spacing=1.5
                )
            else:
                await self.text_card.generate_card(
                    text=text,
                    title_image=header_image,
                    output_path=temp_path,
                    width=720,
                    is_dark=False,
                    font_size=16,
                    line_spacing=1.2
                )
            
            return temp_path
            
        except Exception as e:
            logger.error(f"[{self.plugin_name}] 生成图片失败: {e}")
            if temp_path and os.path.exists(temp_path):
                os.remove(temp_path)
            return None

    async def _text_to_speech(self, text: str, agent: Dict[str, Any]) -> Optional[bytes]:
        """将文本转换为语音
        
        Args:
            text: 要转换的文本
            agent: 智能体配置,包含 tts-voice 参数
            
        Returns:
            Optional[bytes]: 语音二进制数据,失败返回 None
        """
        try:
            voice = str(agent.get("tts-voice", self.tts_voice))
            
            encoded_text = quote(text)
            url = f"{self.tts_url}?text={encoded_text}&voice={voice}"
            
            async with httpx.AsyncClient() as client:
                response = await client.get(url, timeout=30)
                if response.status_code == 200:
                    return response.content
                else:
                    logger.warning(f"[{self.plugin_name}] 语音生成失败: {response.status_code}")
                    return None
        except Exception as e:
            logger.error(f"[{self.plugin_name}] 生成语音失败: {e}")
            return None

    async def _process_short_text(self, bot: WechatAPIClient, group_wxid: str, sender_wxid: str, text: str, agent: Dict[str, Any]) -> None:
        """处理短文本消息"""
        # 尝试转换为语音
        image_path = None
        try:
            voice_data = await self._text_to_speech(text, agent)
            if voice_data:
                # 直接发送语音数据
                await bot.send_voice_message(group_wxid, voice_data, 'mp3')
                return
        except Exception as e:
            logger.error(f"[{self.plugin_name}] 发送语音失败: {e}")

        try:
            image_path = await self._generate_card(text, agent)
            if image_path:
                # 直接使用文件路径发送
                await bot.send_image_message(group_wxid, image_path)
                return
        except Exception as e:
            logger.error(f"[{self.plugin_name}] 生成图片失败: {e}")
        finally:
            if image_path and os.path.exists(image_path):
                os.remove(image_path)

        await bot.send_text_message(group_wxid, text)

    async def _process_long_text(self, bot: WechatAPIClient, group_wxid: str, sender_wxid: str, text: str, agent: Dict[str, Any]) -> None:
        """处理长文本消息"""
        # 尝试转换为语音
        image_path = None
        try:
            voice_data = await self._text_to_speech(text, agent)
            if voice_data:
                await bot.send_voice_message(group_wxid, voice_data, 'mp3')
                return
        except Exception as e:
            logger.error(f"[{self.plugin_name}] 发送语音失败: {e}")

        try:
            image_path = await self._generate_card(text, agent)
            if image_path:
                await bot.send_image_message(group_wxid, image_path)
                return
        except Exception as e:
            logger.error(f"[{self.plugin_name}] 生成图片失败: {e}")
        finally:
            if image_path and os.path.exists(image_path):
                os.remove(image_path)

        await bot.send_text_message(group_wxid, text)

    async def _refresh_login(self) -> bool:
        """刷新登录凭证"""
        try:
            async with httpx.AsyncClient() as client:
                response = await client.get("https://id.vivo.com.cn/")
                if response.status_code != 200:
                    return False
                
                cookies = response.cookies
                
                login_data = {
                    "username": self.username,
                    "password": self.password,
                    "client_id": "1",
                    "redirect_uri": "https://agents.vivo.com.cn/",
                }
                
                response = await client.post(
                    "https://id.vivo.com.cn/api/auth/login",
                    json=login_data,
                    cookies=cookies
                )
                if response.status_code != 200:
                    return False
                
                login_result = response.json()
                if not login_result.get("success"):
                    logger.error(f"[{self.plugin_name}] 登录失败: {login_result.get('message')}")
                    return False
                
                new_cookies = response.cookies
                csrf_token = login_result.get("data", {}).get("csrf_token")
                
                if not csrf_token:
                    return False
                
            self.csrf_token = csrf_token
            self.cookies = "; ".join([f"{k}={v}" for k, v in new_cookies.items()])
            
            with open("plugins/VivoAgents/config.toml", "rb") as f:
                config = tomllib.load(f)
            
            config["VivoAgents"]["csrf-token"] = self.csrf_token
            config["VivoAgents"]["cookies"] = self.cookies
            
            with open("plugins/VivoAgents/config.toml", "w", encoding="utf-8") as f:
                tomli_w.dump(config, f)
            

            self.last_refresh_time = datetime.now()
            return True
            
        except Exception as e:
            logger.error(f"[{self.plugin_name}] 刷新登录凭证失败: {e}")
            return False

    async def _check_and_refresh_login(self) -> bool:
        """检查并刷新登录状态"""
        if not self.auto_refresh:
            return True
            
        if datetime.now() - self.last_refresh_time < self.refresh_interval:
            return True
            
        if not self.username or not self.password:
            logger.warning(f"[{self.plugin_name}] 未配置账号密码，无法自动刷新登录凭证")
            return True
            
        return await self._refresh_login()

    def _is_exit_command(self, content: str) -> bool:
        """检查是否是退出命令"""
        exit_commands = ["退出", "结束", "再见", "拜拜", "exit", "quit", "bye", "结束会话", "退出会话"]
        return content.lower() in exit_commands

    def _get_agent_by_command(self, command: str) -> Optional[Dict[str, Any]]:
        """根据命令获取智能体配置"""
        for agent_id, agent in self.agents.items():
            if command in agent["command"]:
                return agent
        return None

    async def _handle_session_exit(self, bot: WechatAPIClient, group_wxid: str, sender_wxid: str, current_agent: Dict[str, Any]) -> bool:
        """处理会话退出"""
        agent_name = current_agent["name"]
        self._chat_sessions.pop(self._get_session_key(group_wxid, sender_wxid))
        
        # 构建其他智能体列表
        other_agents = []
        for agent in self.agents.values():
            if agent["name"] != agent_name:
                other_agents.append(f"- {agent['name']}: {agent['command'][0]}")
        
        # 如果有其他智能体,显示切换选项
        if other_agents:
            await bot.send_text_message(
                group_wxid,
                f"好的,{agent_name}先去休息啦～\n\n" + \
                "如果想找其他智能体聊天:\n" + \
                "\n".join(other_agents) + \
                "\n\n需要的时候随时叫我哦! 👋"
            )
        else:
            await bot.send_text_message(
                group_wxid,
                f"好的,{agent_name}先去休息啦～需要的时候随时叫我哦! 👋"
            )
        return True

    async def _handle_agent_switch(self, bot: WechatAPIClient, group_wxid: str, sender_wxid: str, current_agent: Dict[str, Any], new_agent: Dict[str, Any]) -> bool:
        """处理智能体切换"""
        old_name = current_agent["name"]
        new_name = new_agent["name"]
        
        # 如果切换到相同的智能体,提示已经在对话中
        if old_name == new_name:
            await bot.send_text_message(
                group_wxid,
                f"我们已经在聊天啦～有什么想问{new_name}的吗? 😊"
            )
            return True
            
        # 更新会话信息
        self._chat_sessions[self._get_session_key(group_wxid, sender_wxid)] = {
            "agent": new_agent,
            "last_active": datetime.now()
        }
        
        # 发送切换提示
        await bot.send_text_message(
            group_wxid,
            f"{old_name}先去休息啦～\n" + \
            f"你好,我是{new_name}! {new_agent['description']}\n" + \
            "让我们开始愉快的对话吧~ 😊"
        )
        return True

    async def _process_message(self, bot: WechatAPIClient, group_wxid: str, sender_wxid: str, content: str, agent: Dict[str, Any]) -> None:
        """处理消息"""
        try:
            if not await self._check_and_refresh_login():
                await bot.send_text_message(
                    group_wxid,
                    "抱歉，我现在遇到了一些问题，请稍后再试~ 🔄"
                )
                return
                
            # 确保 session ID 是字符串类型
            session_key = self._get_session_key(group_wxid, sender_wxid)
            if session_key not in self._sessions:
                self._sessions[session_key] = str(uuid.uuid4())
            session_id = str(self._sessions[session_key])
            
            request_data = {
                "sessionId": session_id,
                "requestId": str(uuid.uuid4()),
                "appName": str(agent["name"]),
                "appId": str(agent["agent_id"]),
                "prompt": str(content),
                "fileInfoList": []
            }
            
            headers = self._generate_headers()
            
            async with httpx.AsyncClient() as client:
                response = await client.post(
                    self.api_url,
                    headers=headers,
                    json=request_data,
                    timeout=60.0
                )
                if response.status_code != 200:
                    error_text = response.text
                    logger.error(f"[{self.plugin_name}] API请求失败: 状态码={response.status_code}, 响应内容={error_text}")
                    raise httpx.HTTPError(f"API请求失败: {response.status_code}")
                
                text = await self._process_stream(response, agent)
                if not text:
                    raise ValueError("抱歉，我暂时无法回答，请稍后再试")
                
                if len(text) <= 200:
                    await self._process_short_text(bot, group_wxid, sender_wxid, text, agent)
                else:
                    await self._process_long_text(bot, group_wxid, sender_wxid, text, agent)
                    
        except ValueError as e:
            if "登录信息已过期" in str(e):
                logger.error(f"[{self.plugin_name}] 登录已过期")
                if session_key in self._chat_sessions:
                    self._chat_sessions.pop(session_key)
                await bot.send_text_message(
                    group_wxid,
                    "抱歉，我需要休息一下下，等会再来找我哦~ 🔄"
                )
            else:
                logger.error(f"[{self.plugin_name}] 处理失败: {str(e)}")
                await bot.send_text_message(group_wxid, f"❌{str(e)}")
        except httpx.HTTPError as e:
            logger.error(f"[{self.plugin_name}] 网络请求错误: {e}")
            await bot.send_text_message(group_wxid, "网络有点问题，等会再试试哦~ 🌐")
        except Exception as e:
            logger.error(f"[{self.plugin_name}] 处理失败: {str(e)}")
            logger.error(f"[{self.plugin_name}] 错误详情: {traceback.format_exc()}")
            await bot.send_text_message(group_wxid, "抱歉，我遇到了一些问题，等会再试试~ 🔧")

    def _generate_headers(self) -> dict:
        """生成请求头"""
        timestamp = str(int(time.time()))
        nonce = base64.b64encode(os.urandom(6)).decode('utf-8')[:8]
        trace_id = str(uuid.uuid4())
        
        headers = {
            "X-AI-GATEWAY-TIMESTAMP": timestamp,
            "X-CSRF-TOKEN": self.csrf_token,
            "X-AI-GATEWAY-SIGNED-HEADERS": "x-ai-gateway-app-id;x-ai-gateway-timestamp;x-ai-gateway-nonce",
            "X-AI-GATEWAY-NONCE": nonce,
            "Content-Type": "application/json",
            "traceId": trace_id,
            "X-AI-GATEWAY-APP-ID": self.app_id,
            "X-uid": self.uid,
            "platform": "web",
            "provider": "chatgpt",
            "Accept": "*/*",
            "Origin": "https://agents.vivo.com.cn",
            "X-Requested-With": "mark.via",
            "Sec-Fetch-Site": "same-origin",
            "Sec-Fetch-Mode": "cors",
            "Sec-Fetch-Dest": "empty",
            "Referer": "https://agents.vivo.com.cn/",
            "Accept-Encoding": "gzip, deflate",
            "Accept-Language": "zh-CN,zh;q=0.9,en-US;q=0.8,en;q=0.7",
            "Cookie": self.cookies
        }
        
        signed_content = f"x-ai-gateway-app-id={headers['X-AI-GATEWAY-APP-ID']}&x-ai-gateway-timestamp={timestamp}&x-ai-gateway-nonce={nonce}"
        signature = base64.b64encode(
            hmac.new(
                self.csrf_token.encode('utf-8'),
                signed_content.encode('utf-8'),
                hashlib.sha256
            ).digest()
        ).decode('utf-8')
        
        headers["X-AI-GATEWAY-SIGNATURE"] = signature
        
        return headers

    @on_text_message
    async def handle_text(self, bot: WechatAPIClient, message: dict):
        if not self.enable:
            return  # 不返回True，允许其他插件继续处理
        
        group_wxid = message["FromWxid"]  # 群ID
        sender_wxid = message["SenderWxid"]  # 发送者ID
        content = message["Content"].strip()
        session_key = self._get_session_key(group_wxid, sender_wxid)
        
        # 检查是否在会话模式中
        if session_key in self._chat_sessions:
            session = self._chat_sessions[session_key]
            current_agent = session["agent"]
            
            # 检查是否要退出会话
            if self._is_exit_command(content):
                return await self._handle_session_exit(bot, group_wxid, sender_wxid, current_agent)
                
            # 检查是否要切换智能体
            new_agent = self._get_agent_by_command(content)
            if new_agent:
                return await self._handle_agent_switch(bot, group_wxid, sender_wxid, current_agent, new_agent)
            
            # 更新会话活跃时间并处理消息
            self._update_session_activity(group_wxid, sender_wxid)
            await self._process_message(bot, group_wxid, sender_wxid, content, current_agent)
            return True
            
        # 检查是否是智能体命令
        new_agent = self._get_agent_by_command(content)
        if new_agent:
            self._chat_sessions[session_key] = {
                "agent": new_agent,
                "last_active": datetime.now()
            }
            await bot.send_text_message(
                group_wxid,
                f"你好～👋 我是{new_agent['name']}，{new_agent['description']}。\n\n" + \
                "💡 温馨提示:\n" + \
                "- 直接说话就可以和我聊天哦\n" + \
                "- 输入'再见'可以结束对话\n" + \
                "- 直接输入其他智能体的名字可以切换对话\n" + \
                f"- 当前可用智能体: {', '.join([agent['name'] for agent in self.agents.values() if agent['name'] != new_agent['name']])}"
            )
            return True
            
        # 检查命令前缀
        for agent in self.agents.values():
            for cmd in agent["command"]:
                if content.startswith(cmd):
                    question = content[len(cmd):].strip()
                    if not question:
                        await bot.send_text_message(
                            group_wxid,
                            f"你好,我是{agent['name']}。\n" + \
                            f"{agent['description']}\n\n" + \
                            "🤖 使用方式:\n" + \
                            f"1. 直接发送「{cmd}」进入会话模式\n" + \
                            f"2. 发送「{cmd} 问题」快速提问"
                        )
                        return True
                    

                    await self._process_message(bot, group_wxid, sender_wxid, question, agent)
                    return True
        
        return  # 不匹配时直接return，不返回任何值，允许其他插件处理

    async def on_disable(self):
        """插件禁用时清理资源"""
        await super().on_disable()
        
        # 通知所有会话用户
        bot = WechatAPIClient(ip="127.0.0.1", port=8680)
        for session_key, session in self._chat_sessions.items():
            group_wxid = session_key.split("_")[0]
            try:
                await bot.send_text_message(
                    group_wxid,
                    "抱歉呀,我需要去休息一下下,很快就会回来的! 🔄"
                )
            except:
                pass
                
        self._chat_sessions.clear()
        
        # 临时文件清理现在由统一的TempFileManager处理