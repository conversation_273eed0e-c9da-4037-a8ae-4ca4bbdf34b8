[AppMessageTester]
enable = true
command = ["红包来了", "唱舞星愿站", "游戏分享", "链接测试", "视频号分享", "聊天记录", "接龙"]

# 默认聊天记录内容（使用不同用户名，这样每条消息都有头像）
default_chat_content = "小明:今天天气真不错啊！|小红:是啊，我们去公园走走吧|小李:我也想去"
command-format = """
发送应用消息测试

用法：
- 红包来了 (发送红包音乐消息)
- 唱舞星愿站 (发送小程序消息)
- 游戏分享 (发送游戏分享消息)
- 链接测试 (发送简单链接消息)
- 视频号分享 (发送视频号分享消息)
- 聊天记录 (发送默认聊天记录消息)
- 聊天记录 自定义内容 (发送自定义聊天记录)
- 接龙 (发送接龙消息)

聊天记录自定义格式：
用户名1:消息内容1|用户名2:消息内容2|用户名3:消息内容3

示例：
聊天记录 小明:今天天气真好|小红:是啊，适合出去玩|小李:我们一起去公园吧
"""

# 限流配置
[AppMessageTester.rate_limit]
tokens_per_second = 0.1  # 每10秒生成1个令牌
bucket_size = 3  # 令牌桶容量 