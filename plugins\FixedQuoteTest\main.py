import os, json, httpx, asyncio, time, traceback, random
try:
    import tomllib
except ModuleNotFoundError:
    import tomli as tomllib
from loguru import logger
from pathlib import Path
from WechatAPI import WechatAPIClient
from utils.decorators import on_text_message
from utils.plugin_base import PluginBase

class FixedQuoteTest(PluginBase):
    description = "测试固定消息ID引用功能 - 使用固定的消息ID作为装饰性引用尾巴"
    author = "XYBot"
    version = "1.0.0"
    plugin_name = "FixedQuoteTest"

    def __init__(self):
        super().__init__()
        self.temp_dir = Path("plugins/FixedQuoteTest/temp")
        self.temp_dir.mkdir(parents=True, exist_ok=True)
        self._load_config()
        self.user_last_request = {}
        self._init_natural_responses()

    def _load_config(self):
        try:
            with open(f"plugins/{self.plugin_name}/config.toml", "rb") as f:
                config = tomllib.load(f).get(self.plugin_name, {})
        except:
            config = {}

        self.enable = config.get("enable", True)
        self.command = config.get("command", ["测试引用", "批量测试", "引用配置"])
        self.command_format = config.get("command-format", "使用方法：测试引用 [内容] | 批量测试 | 引用配置")
        self.cooldown = config.get("rate_limit", {}).get("cooldown", 3)
        self.natural_response = config.get("natural_response", True)

        # 固定引用配置
        settings = config.get("settings", {})
        self.fixed_msg_id = settings.get("fixed_msg_id", "1714825364954204565")
        self.sender_wxid = settings.get("sender_wxid", "wxid_ubbh6q832tcs21")
        self.sender_nickname = settings.get("sender_nickname", "❤️")
        self.quote_content = settings.get("quote_content", "今天星期四")



    def _init_natural_responses(self):
        """初始化自然化回复词库"""
        self.confirm_msgs = [
            "好的", "收到", "明白了", "嗯", "知道了", "行", "OK"
        ]

        self.error_msgs = [
            "这可把我难住了", "搞不定了", "这个有点难", "弄不了",
            "出问题了", "搞不出来", "这个不行"
        ]

        self.rate_limit_msgs = [
            "你搁这刷bug呢", "慢点慢点", "别这么急", "一个一个来",
            "太快了太快了", "歇会儿", "别刷了"
        ]

    async def _simple_confirm(self, bot, wxid):
        """简单确认回复，不艾特人"""
        if self.natural_response:
            confirm_msg = random.choice(self.confirm_msgs)
            await bot.send_text_message(wxid, confirm_msg)

    @on_text_message
    async def handle_text(self, bot: WechatAPIClient, message: dict):
        if not self.enable:
            return

        content = str(message["Content"]).strip()
        wxid = message["FromWxid"]
        user_wxid = message["SenderWxid"]

        # 检查是否是支持的命令
        command_found = False
        for cmd in self.command:
            if content.startswith(cmd):
                command_found = True
                break

        if not command_found:
            return

        # 限流检查
        wait_time = self._check_user_limit(wxid, user_wxid)
        if wait_time > 0:
            if self.natural_response:
                rate_limit_msg = random.choice(self.rate_limit_msgs)
                await bot.send_text_message(wxid, rate_limit_msg)
            else:
                await bot.send_at_message(wxid, f"请等待 {wait_time:.1f} 秒", [user_wxid])
            return

        try:
            # 简单确认
            await self._simple_confirm(bot, wxid)

            # 处理不同命令
            if content.startswith("测试引用"):
                reply_content = content.replace("测试引用", "").strip()
                if not reply_content:
                    reply_content = "这是一条测试消息，带有固定的装饰性引用尾巴！"

                await self.send_fixed_quote_message(bot, wxid, reply_content)

            elif content == "批量测试":
                test_messages = [
                    "第一条测试消息 🎉",
                    "第二条测试消息 ✨",
                    "第三条测试消息 🌟",
                    "第四条测试消息 💫",
                    "第五条测试消息 🎨"
                ]

                for i, msg in enumerate(test_messages, 1):
                    await self.send_fixed_quote_message(bot, wxid, msg)

                    # 间隔1秒避免发送过快
                    await asyncio.sleep(1)

            elif content == "引用配置":
                config_info = f"""当前固定引用配置：
📝 消息ID: {self.fixed_msg_id}
👤 发送者: {self.sender_nickname} ({self.sender_wxid})
💬 引用内容: {self.quote_content}

使用方法：
• 测试引用 [内容] - 发送带引用的消息
• 批量测试 - 发送5条测试消息
• 引用配置 - 查看当前配置"""

                await bot.send_text_message(wxid, config_info)

        except Exception as e:
            logger.error(f"[{self.plugin_name}] 异常: {e}")
            if self.natural_response:
                error_msg = random.choice(self.error_msgs)
                await bot.send_text_message(wxid, error_msg)
            else:
                await bot.send_at_message(wxid, "处理失败", [user_wxid])

    async def send_fixed_quote_message(self, bot: WechatAPIClient, wxid: str, reply_content: str):
        """发送带固定引用的消息"""
        try:
            # 构建引用消息的XML
            app_xml = f'''<appmsg appid="" sdkver="0">
                <title>{reply_content}</title>
                <des></des>
                <action></action>
                <type>57</type>
                <showtype>0</showtype>
                <soundtype>0</soundtype>
                <mediatagname></mediatagname>
                <messageext></messageext>
                <messageaction></messageaction>
                <content></content>
                <contentattr>0</contentattr>
                <url></url>
                <lowurl></lowurl>
                <dataurl></dataurl>
                <lowdataurl></lowdataurl>
                <songalbumurl></songalbumurl>
                <songlyric></songlyric>
                <appattach>
                    <totallen>0</totallen>
                    <attachid></attachid>
                    <emoticonmd5></emoticonmd5>
                    <fileext></fileext>
                    <cdnthumbaeskey></cdnthumbaeskey>
                    <aeskey></aeskey>
                </appattach>
                <extinfo></extinfo>
                <sourceusername></sourceusername>
                <sourcedisplayname></sourcedisplayname>
                <thumburl></thumburl>
                <md5></md5>
                <statextstr></statextstr>
                <directshare>0</directshare>
                <refermsg>
                    <type>1</type>
                    <svrid>{self.fixed_msg_id}</svrid>
                    <fromusr>{self.sender_wxid}</fromusr>
                    <chatusr>{wxid}</chatusr>
                    <displayname>{self.sender_nickname}</displayname>
                    <content>{self.quote_content}</content>
                    <msgsource></msgsource>
                </refermsg>
            </appmsg>'''

            # 发送引用消息
            await bot.send_app_message(wxid, app_xml, 57)


        except Exception as e:
            logger.error(f"[{self.plugin_name}] 发送固定引用消息失败: {e}")
            # 如果引用失败，发送普通消息作为备选
            await bot.send_text_message(wxid, f"⚠️ 引用发送失败，普通消息：{reply_content}")

    def _check_user_limit(self, wxid: str, user_wxid: str) -> float:
        user_key = f"{wxid}_{user_wxid}"
        last_request = self.user_last_request.get(user_key, 0)
        current_time = time.time()
        elapsed = current_time - last_request
        self.user_last_request[user_key] = current_time

        if elapsed < self.cooldown:
            return self.cooldown - elapsed

        return 0
