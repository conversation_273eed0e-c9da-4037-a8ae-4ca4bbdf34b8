2025-07-29 19:12:33 | SUCCESS | 读取主设置成功
2025-07-29 19:12:33 | DEBUG | 最终使用的 Redis 主机地址: 127.0.0.1
2025-07-29 19:12:33 | INFO | 2025/07/29 19:12:33 GetRedisAddr: 127.0.0.1:6379
2025-07-29 19:12:33 | INFO | 2025/07/29 19:12:33 Redis连接成功 | 地址:127.0.0.1:6379 DB:0
2025-07-29 19:12:33 | INFO | 2025/07/29 19:12:33 Server start at :9000
2025-07-29 19:12:34 | SUCCESS | WechatAPI服务已启动
2025-07-29 19:12:34 | INFO | 登录账号信息: wxid: wxid_4usgcju5ey9q29  昵称: 瑶瑶  微信号: Today-KFC  手机号: None
2025-07-29 19:12:34 | INFO | 登录设备信息: device_name: <PERSON>'s Pad  device_id: 49d050a7e94d1ecc40790d74b1c4369f
2025-07-29 19:12:34 | SUCCESS | 登录成功
2025-07-29 19:12:34 | SUCCESS | 已开启自动心跳
2025-07-29 19:12:34 | INFO | 成功加载表情映射文件，共 545 条记录
2025-07-29 19:12:34 | SUCCESS | 数据库初始化成功
2025-07-29 19:12:34 | SUCCESS | 定时任务已启动
2025-07-29 19:12:34 | SUCCESS | 插件 AISignIn 已加载定时任务: {'plugins.AISignIn.main.AISignIn.auto_signin_task'}
2025-07-29 19:12:35 | INFO | 已加载管理员列表: ['wxid_ubbh6q832tcs21', 'admin-wxid']
2025-07-29 19:12:36 | INFO | 播客API初始化成功
2025-07-29 19:12:36 | INFO | 加载配置完成，转换后的数据: {'gh_7057516c9e71': {'name': '唱舞全明星', 'groups': ['27852221909@chatroom', '51891329927@chatroom']}, 'gh_d4d21a0e4623': {'name': '两颗桃', 'groups': ['48097389945@chatroom']}, 'gh_410ff2fa6ec9': {'name': 'NGC660 Ai研究院', 'groups': ['47442567074@chatroom']}, 'gh_f78c53c507c0': {'name': '不正常吐槽君', 'groups': ['48097389945@chatroom']}, 'gh_ba3381c847b3': {'name': '唱舞星计划', 'groups': ['51891329927@chatroom']}, 'gh_1a102fb4cad9': {'name': '我在娱乐圈的唯一人脉', 'groups': ['48097389945@chatroom', '27852221909@chatroom']}}
2025-07-29 19:12:36 | INFO | ArticleForwarder插件初始化完成 - 监控配置: {'gh_7057516c9e71': {'name': '唱舞全明星', 'groups': ['27852221909@chatroom', '51891329927@chatroom']}, 'gh_d4d21a0e4623': {'name': '两颗桃', 'groups': ['48097389945@chatroom']}, 'gh_410ff2fa6ec9': {'name': 'NGC660 Ai研究院', 'groups': ['47442567074@chatroom']}, 'gh_f78c53c507c0': {'name': '不正常吐槽君', 'groups': ['48097389945@chatroom']}, 'gh_ba3381c847b3': {'name': '唱舞星计划', 'groups': ['51891329927@chatroom']}, 'gh_1a102fb4cad9': {'name': '我在娱乐圈的唯一人脉', 'groups': ['48097389945@chatroom', '27852221909@chatroom']}}
2025-07-29 19:12:36 | DEBUG | [TempFileManager] 添加清理规则: default
2025-07-29 19:12:36 | DEBUG | [TempFileManager] 添加清理规则: images
2025-07-29 19:12:36 | DEBUG | [TempFileManager] 添加清理规则: videos
2025-07-29 19:12:36 | DEBUG | [TempFileManager] 添加清理规则: audio
2025-07-29 19:12:36 | DEBUG | [TempFileManager] 添加清理规则: temp
2025-07-29 19:12:36 | DEBUG | [TempFileManager] 添加清理规则: large_files
2025-07-29 19:12:36 | INFO | [TempFileManager] 临时文件清理任务已启动
2025-07-29 19:12:37 | INFO | [ChatSummary] 数据库初始化成功
2025-07-29 19:12:37 | SUCCESS | 插件 GoodMorning 已加载定时任务: {'plugins.GoodMorning.main.GoodMorning.daily_task'}
2025-07-29 19:12:37 | SUCCESS | 插件 News 已加载定时任务: {'plugins.News.main.News.night_news', 'plugins.News.main.News.noon_news'}
2025-07-29 19:12:37 | INFO | [PatReply] AMR文件已存在且是最新的: C:\XYBotV2\data\paiyipai\1_BFB5258B-2B13-4CF4-A40C-5BCCE74E5414_converted.amr
2025-07-29 19:12:37 | DEBUG | [PatReply] 从缓存加载base64: 18228 字符
2025-07-29 19:12:37 | SUCCESS | 插件 QuarkSignIn 已加载定时任务: {'plugins.QuarkSignIn.main.QuarkSignIn.auto_signin_task'}
2025-07-29 19:12:37 | INFO | [QuarkSignIn] 已设置自动签到时间: 08:00
2025-07-29 19:12:37 | INFO | 插件状态: 启用, 检查间隔: 3600秒, 最大重试次数: 3, 重试延迟: 5秒
2025-07-29 19:12:37 | INFO | 已加载管理员列表: ['wxid_ubbh6q832tcs21', 'admin-wxid']
2025-07-29 19:12:37 | INFO | 已加载 2 个改名监控群和 1 个退群监控群
2025-07-29 19:12:37 | INFO | [RenameReminder] 开始启用插件...
2025-07-29 19:12:37 | SUCCESS | 插件 RenameReminder 已加载定时任务: {'plugins.RenameReminder.main.RenameReminder.check_name_changes'}
2025-07-29 19:12:37 | INFO | [RenameReminder] 插件已启用，开始初始化...
2025-07-29 19:12:37 | INFO | [RenameReminder] 支持的命令: 开启改名提醒, 关闭改名提醒, 开启退群提醒, 关闭退群提醒, 查看提醒状态
2025-07-29 19:12:37 | INFO | 已设置检查间隔为 3600 秒
2025-07-29 19:12:37 | INFO | 已加载 2 个监控群，开始更新成员列表
2025-07-29 19:12:38 | DEBUG | 已更新群 27852221909@chatroom 的成员列表
2025-07-29 19:12:38 | DEBUG | 已更新群 51891329927@chatroom 的成员列表
2025-07-29 19:12:44 | SUCCESS | 插件 TimerTask 已加载定时任务: {'plugins.TimerTask.main.TimerTask.check_tasks'}
2025-07-29 19:12:44 | INFO | [VideoDemand] 加载了 128 个视频类别配置
2025-07-29 19:12:45 | INFO | [YaoyaoPlugin] 私聊黑名单用户: ['gh_a5cf07d474f1', 'gh_ba3381c847b3']
2025-07-29 19:12:45 | INFO | [YaoyaoPlugin] 已加载管理员列表: ['wxid_ubbh6q832tcs21', 'admin-wxid']
2025-07-29 19:12:45 | INFO | [yuanbao] 插件初始化完成
2025-07-29 19:12:45 | DEBUG | [yuanbao] 指令: ['元宝', 'yuanbao']
2025-07-29 19:12:45 | DEBUG | [yuanbao] 绘图模型: gpt_175B_0404
2025-07-29 19:12:45 | DEBUG | [yuanbao] 认证信息加载状态: hy_token=True, hy_user=True, agent_id=True, x_uskey=True
2025-07-29 19:12:45 | SUCCESS | 已加载插件: ['AdminPoint', 'AdminSignInReset', 'AdminWhitelist', 'AISignIn', 'AppMessageTester', 'ArticleForwarder', 'BaiduAgentsPlugin', 'BaiduDraw', 'BotStatus', 'ChatSummary', 'DanceSignInPlugin', 'DeepseekPlugin', 'Doubao', 'DoubaoDrawing', 'DoubaoImageRecognition', 'DouBaoImageToImage', 'DouBaoImageToVideo', 'DoubaoVideoSearch', 'DouHui', 'EmojiTestPlugin', 'FixedQuoteTest', 'GetContact', 'Gomoku', 'GoodMorning', 'HunyuanDraw', 'ImageEcho', 'ImageToImage', 'JiemengDraw', 'KeLingImageToImage', 'KlingAI', 'Leaderboard', 'LuckyDraw', 'ManagePlugin', 'MeituAI', 'Menu', 'MiniProgramTester', 'Music', 'News', 'PatReply', 'PointTrade', 'QuarkSignIn', 'QueryPoint', 'RandomMember', 'RandomPicture', 'RenameReminder', 'RevokePlugin', 'RoboNeo', 'SignIn', 'TempFileManagerPlugin', 'TencentLke', 'TimerTask', 'TongyiDraw', 'URLShortener', 'VideoDemand', 'VideoParserPlugin', 'VideoTest', 'VivoAgentsPlugin', 'VoiceTest', 'Warthunder', 'WeatherQuery', 'WelcomePlugin', 'Xunfei', 'YaoyaoPlugin', 'Yuanbao', '抽签', '造梦次元']
2025-07-29 19:12:45 | INFO | 处理堆积消息中
2025-07-29 19:12:46 | SUCCESS | 处理堆积消息完毕
2025-07-29 19:12:46 | SUCCESS | 开始处理消息
2025-07-29 19:12:54 | DEBUG | 收到消息: {'MsgId': 1968291257, 'FromUserName': {'string': '27852221909@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 1, 'Content': {'string': 'wxid_ubbh6q832tcs21:\n啊'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1753787584, 'MsgSource': '<msgsource>\n\t<pua>1</pua>\n\t<eggIncluded>1</eggIncluded>\n\t<silence>1</silence>\n\t<membercount>145</membercount>\n\t<signature>N0_V1_Vz8fea9Y|v1_KBTptO2k</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'NewMsgId': 2575379215562473831, 'MsgSeq': 871409218}
2025-07-29 19:12:54 | INFO | 收到文本消息: 消息ID:1968291257 来自:27852221909@chatroom 发送人:wxid_ubbh6q832tcs21 @:[] 内容:啊
2025-07-29 19:12:54 | INFO | 成功加载表情映射文件，共 545 条记录
2025-07-29 19:12:54 | INFO | 发送表情消息: 对方wxid:27852221909@chatroom md5:5a1e188c79cc0c66e11cf127f264b68f 总长度:9992069
2025-07-29 19:12:54 | DEBUG | 处理消息内容: '啊'
2025-07-29 19:12:54 | DEBUG | 消息内容 '啊' 不匹配任何命令，忽略
2025-07-29 19:13:07 | DEBUG | 收到消息: {'MsgId': 1925961223, 'FromUserName': {'string': '27852221909@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 1, 'Content': {'string': 'wxid_idzryo4rneok22:\n啊'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1753787596, 'MsgSource': '<msgsource>\n\t<pua>1</pua>\n\t<eggIncluded>1</eggIncluded>\n\t<silence>1</silence>\n\t<membercount>145</membercount>\n\t<signature>N0_V1_Z14yWJVF|v1_tDbgiuMg</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'NewMsgId': 4152333676793002340, 'MsgSeq': 871409221}
2025-07-29 19:13:07 | INFO | 收到文本消息: 消息ID:1925961223 来自:27852221909@chatroom 发送人:wxid_idzryo4rneok22 @:[] 内容:啊
2025-07-29 19:13:09 | INFO | 发送表情消息: 对方wxid:27852221909@chatroom md5:5a1e188c79cc0c66e11cf127f264b68f 总长度:9992069
2025-07-29 19:13:09 | DEBUG | 处理消息内容: '啊'
2025-07-29 19:13:09 | DEBUG | 消息内容 '啊' 不匹配任何命令，忽略
2025-07-29 19:13:13 | DEBUG | 收到消息: {'MsgId': 1186012621, 'FromUserName': {'string': 'fmessage'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 37, 'Content': {'string': '<msg fromusername="wxid_x95sfijdz8xy22" encryptusername="v3_020b3826fd030100000000003638212ec27443000000501ea9a3dba12f95f6b60a0536a1adb6988c87adc5a1f2bb2db56f4eff24730b4a042e0181c996cd52ad645f8e08b4586faf708d623328058c775c2c417d1feefae6a364f55a82e588c8bfd3ea@stranger" fromnickname="H" content="我是群聊“唱舞·R团·休闲娱乐”的胡咧咧" fullpy="H" shortpy="H" imagestatus="3" scene="14" country="CN" province="Hubei" city="Wuhan" sign="后来我才想明白，太阳都不能让所有人喜欢，你说它温暖我说它刺眼" percard="1" sex="2" alias="hlxy2130" weibo="" albumflag="0" albumstyle="0" albumbgimgid="" snsflag="1" snsbgimgid="http://shmmsns.qpic.cn/mmsns/LmjbuWKBXFic1ZCsXFe5kXXG8Vicfib0JrLCv3cClb34ON41xBGMNCc4PCxL0aEib4kFCUlibqwDdxbo/0" snsbgobjectid="13999942787929223271" mhash="0efef22f4f80fce705aa4094479abebf" mfullhash="0efef22f4f80fce705aa4094479abebf" bigheadimgurl="http://wx.qlogo.cn/mmhead/ver_1/icfbBtWiblCy5ic5kwKz7usBxQy3avzibVxjwHNwQMSvJkvNVCknyhXibZ9huicPdaPpiafQzgzOOX5FDcEPPQElDXia6bIBdia0iczxBiaoZFBICHFNokWibrrJ6CvXQrQvcHkMEox4/0" smallheadimgurl="http://wx.qlogo.cn/mmhead/ver_1/icfbBtWiblCy5ic5kwKz7usBxQy3avzibVxjwHNwQMSvJkvNVCknyhXibZ9huicPdaPpiafQzgzOOX5FDcEPPQElDXia6bIBdia0iczxBiaoZFBICHFNokWibrrJ6CvXQrQvcHkMEox4/96" ticket="v4_000b708f0b040000010000000000399d67aada76f970844bcdac88681000000050ded0b020927e3c97896a09d47e6e9e8c34c15404a310b38db2eedeaff9fa4e512859277a92011abeb7a02062ba338a9ccb864a27a27eed3caf3e3d943bae95ae3ba2b17e1c312e2a9d8c7f9a4cdbbad4f9dd580258847397ce92fa7658b64652f0edda8556ce156dfff26001e00bcdb030cead8de93d83d2@stranger" opcode="2" googlecontact="" qrticket="" chatroomusername="27852221909@chatroom" sourceusername="" sourcenickname="" sharecardusername="" sharecardnickname="" cardversion="" extflag="0"><brandlist count="0" ver="870836741"></brandlist></msg>'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1753787597, 'MsgSource': '<msgsource>\n\t<signature>v1_V/zZI8Y2</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'NewMsgId': 319999828835528025, 'MsgSeq': 871409222}
2025-07-29 19:13:14 | DEBUG | 收到消息: {'MsgId': 2140004166, 'FromUserName': {'string': '27852221909@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 3, 'Content': {'string': 'wxid_2530z9t0joek22:\n<?xml version="1.0"?>\n<msg>\n\t<img aeskey="e61f1c8586fd53f77c35cfba8377ac60" encryver="1" cdnthumbaeskey="e61f1c8586fd53f77c35cfba8377ac60" cdnthumburl="3057020100044b30490201000204f060aea202032f59e102046bc7587d02046888ac50042465663432333761332d386636322d343965362d393432302d313365376664613737356638020405150a020201000405004c55cd00" cdnthumblength="4604" cdnthumbheight="432" cdnthumbwidth="196" cdnmidheight="0" cdnmidwidth="0" cdnhdheight="0" cdnhdwidth="0" cdnmidimgurl="3057020100044b30490201000204f060aea202032f59e102046bc7587d02046888ac50042465663432333761332d386636322d343965362d393432302d313365376664613737356638020405150a020201000405004c55cd00" length="867899" md5="56d16ccb9bfca32017cc87bc5c911103" hevc_mid_size="94443" originsourcemd5="82aa035acdc9c03b0358462f07e69e4f">\n\t\t<secHashInfoBase64>eyJwaGFzaCI6IjUwMTAxMDEwMDAxMDAwMDAiLCJwZHFIYXNoIjoiMWZlOGY4ZGUwNzg0NjMxM2Q3\nMzNlOGE3MTFkNGU4MWY0NzJiZDczMjE0MjVlOGY0MTU1NGY3YWJkNjFhMDU1NCJ9\n</secHashInfoBase64>\n\t\t<live>\n\t\t\t<duration>0</duration>\n\t\t\t<size>0</size>\n\t\t\t<md5 />\n\t\t\t<fileid />\n\t\t\t<hdsize>0</hdsize>\n\t\t\t<hdmd5 />\n\t\t\t<hdfileid />\n\t\t\t<stillimagetimems>0</stillimagetimems>\n\t\t</live>\n\t</img>\n\t<platform_signature />\n\t<imgdatahash />\n\t<ImgSourceInfo>\n\t\t<ImgSourceUrl />\n\t\t<BizType>0</BizType>\n\t</ImgSourceInfo>\n</msg>\n'}, 'Status': 3, 'ImgStatus': 2, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1753787600, 'MsgSource': '<msgsource>\n\t<sec_msg_node>\n\t\t<uuid>a97704e0175c09fa6d4cbbbf507acaee_</uuid>\n\t\t<risk-file-flag />\n\t\t<risk-file-md5-list />\n\t</sec_msg_node>\n\t<silence>1</silence>\n\t<membercount>145</membercount>\n\t<signature>N0_V1_TYL78++q|v1_AuAuh4bP</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'NewMsgId': 5791093611329239826, 'MsgSeq': 871409225}
2025-07-29 19:13:14 | INFO | 收到图片消息: 消息ID:2140004166 来自:27852221909@chatroom 发送人:wxid_2530z9t0joek22 XML:<?xml version="1.0"?><msg><img aeskey="e61f1c8586fd53f77c35cfba8377ac60" encryver="1" cdnthumbaeskey="e61f1c8586fd53f77c35cfba8377ac60" cdnthumburl="3057020100044b30490201000204f060aea202032f59e102046bc7587d02046888ac50042465663432333761332d386636322d343965362d393432302d313365376664613737356638020405150a020201000405004c55cd00" cdnthumblength="4604" cdnthumbheight="432" cdnthumbwidth="196" cdnmidheight="0" cdnmidwidth="0" cdnhdheight="0" cdnhdwidth="0" cdnmidimgurl="3057020100044b30490201000204f060aea202032f59e102046bc7587d02046888ac50042465663432333761332d386636322d343965362d393432302d313365376664613737356638020405150a020201000405004c55cd00" length="867899" md5="56d16ccb9bfca32017cc87bc5c911103" hevc_mid_size="94443" originsourcemd5="82aa035acdc9c03b0358462f07e69e4f"><secHashInfoBase64>eyJwaGFzaCI6IjUwMTAxMDEwMDAxMDAwMDAiLCJwZHFIYXNoIjoiMWZlOGY4ZGUwNzg0NjMxM2Q3MzNlOGE3MTFkNGU4MWY0NzJiZDczMjE0MjVlOGY0MTU1NGY3YWJkNjFhMDU1NCJ9</secHashInfoBase64><live><duration>0</duration><size>0</size><md5 /><fileid /><hdsize>0</hdsize><hdmd5 /><hdfileid /><stillimagetimems>0</stillimagetimems></live></img><platform_signature /><imgdatahash /><ImgSourceInfo><ImgSourceUrl /><BizType>0</BizType></ImgSourceInfo></msg>
2025-07-29 19:13:14 | INFO | [ImageEcho] 保存图片信息成功，当前群 27852221909@chatroom 已存储 5 张图片
2025-07-29 19:13:14 | INFO | [TimerTask] 缓存图片消息: 2140004166
2025-07-29 19:13:14 | DEBUG | 收到消息: {'MsgId': 2001404937, 'FromUserName': {'string': '27852221909@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 1, 'Content': {'string': 'wxid_2530z9t0joek22:\n差一个人啊'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1753787604, 'MsgSource': '<msgsource>\n\t<pua>1</pua>\n\t<eggIncluded>1</eggIncluded>\n\t<silence>1</silence>\n\t<membercount>145</membercount>\n\t<signature>N0_V1_lsEZGnzb|v1_uMsRM2a7</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'NewMsgId': 7030227075835249566, 'MsgSeq': 871409226}
2025-07-29 19:13:14 | INFO | 收到文本消息: 消息ID:2001404937 来自:27852221909@chatroom 发送人:wxid_2530z9t0joek22 @:[] 内容:差一个人啊
2025-07-29 19:13:14 | DEBUG | 处理消息内容: '差一个人啊'
2025-07-29 19:13:14 | DEBUG | 消息内容 '差一个人啊' 不匹配任何命令，忽略
2025-07-29 19:13:47 | DEBUG | 收到消息: {'MsgId': 1261610812, 'FromUserName': {'string': '27852221909@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 1, 'Content': {'string': 'wxid_ugv5ryus4gz622:\n啊'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1753787637, 'MsgSource': '<msgsource>\n\t<bizflag>0</bizflag>\n\t<pua>1</pua>\n\t<eggIncluded>1</eggIncluded>\n\t<silence>1</silence>\n\t<membercount>145</membercount>\n\t<signature>N0_V1_OU3BMOQJ|v1_J3+GaYUt</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'NewMsgId': 591371492658053652, 'MsgSeq': 871409227}
2025-07-29 19:13:47 | INFO | 收到文本消息: 消息ID:1261610812 来自:27852221909@chatroom 发送人:wxid_ugv5ryus4gz622 @:[] 内容:啊
2025-07-29 19:13:48 | INFO | 发送表情消息: 对方wxid:27852221909@chatroom md5:5a1e188c79cc0c66e11cf127f264b68f 总长度:9992069
2025-07-29 19:13:48 | DEBUG | 处理消息内容: '啊'
2025-07-29 19:13:48 | DEBUG | 消息内容 '啊' 不匹配任何命令，忽略
2025-07-29 19:13:51 | DEBUG | 收到消息: {'MsgId': 1849827424, 'FromUserName': {'string': '48097389945@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 3, 'Content': {'string': 'zuoledd:\n<?xml version="1.0"?>\n<msg>\n\t<img aeskey="a5bf5ea76ad128d977d25b1fb17806cf" encryver="1" cdnthumbaeskey="a5bf5ea76ad128d977d25b1fb17806cf" cdnthumburl="3057020100044b304902010002048a330c5e02032df7fa020445ebd476020468887c7a042465326338356131302d303761362d343639612d383564302d663965623866373763656138020405250a020201000405004c51e600" cdnthumblength="6030" cdnthumbheight="180" cdnthumbwidth="110" cdnmidheight="0" cdnmidwidth="0" cdnhdheight="0" cdnhdwidth="0" cdnmidimgurl="3057020100044b304902010002048a330c5e02032df7fa020445ebd476020468887c7a042465326338356131302d303761362d343639612d383564302d663965623866373763656138020405250a020201000405004c51e600" length="98793" md5="a05f9e7675502903a6fd5c8efdc923e9" hevc_mid_size="98793" originsourcemd5="58a8e6037fad2f890254c09ba227c573">\n\t\t<secHashInfoBase64>eyJwaGFzaCI6IjMwMDAwMDAwMDAwMDAwMDAiLCJwZHFoYXNoIjoiNGY1MDViODdiZmUwOGI1MDk3NTQxN2NiY2VlYjk0NjgzY2MxNjhkMTg3MDExZmI4OGIxNzM3NTYzMWQ0ZDZjZSJ9</secHashInfoBase64>\n\t\t<live>\n\t\t\t<duration>0</duration>\n\t\t\t<size>0</size>\n\t\t\t<md5 />\n\t\t\t<fileid />\n\t\t\t<hdsize>0</hdsize>\n\t\t\t<hdmd5 />\n\t\t\t<hdfileid />\n\t\t\t<stillimagetimems>0</stillimagetimems>\n\t\t</live>\n\t</img>\n\t<platform_signature />\n\t<imgdatahash />\n\t<ImgSourceInfo>\n\t\t<ImgSourceUrl />\n\t\t<BizType>0</BizType>\n\t</ImgSourceInfo>\n</msg>\n'}, 'Status': 3, 'ImgStatus': 2, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1753787639, 'MsgSource': '<msgsource>\n\t<bizflag>0</bizflag>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n\t<alnode>\n\t\t<fr>5</fr>\n\t</alnode>\n\t<sec_msg_node>\n\t\t<uuid>6dd3c7dc747603940a20d8fe16e8c09e_</uuid>\n\t\t<risk-file-flag />\n\t\t<risk-file-md5-list />\n\t</sec_msg_node>\n\t<silence>0</silence>\n\t<membercount>73</membercount>\n\t<signature>N0_V1_tfCo/yFe|v1_acEJIJXj</signature>\n</msgsource>\n', 'PushContent': '作乐多端在群聊中发了一张图片', 'NewMsgId': 5657323889925892371, 'MsgSeq': 871409230}
2025-07-29 19:13:51 | INFO | 收到图片消息: 消息ID:1849827424 来自:48097389945@chatroom 发送人:zuoledd XML:<?xml version="1.0"?><msg><img aeskey="a5bf5ea76ad128d977d25b1fb17806cf" encryver="1" cdnthumbaeskey="a5bf5ea76ad128d977d25b1fb17806cf" cdnthumburl="3057020100044b304902010002048a330c5e02032df7fa020445ebd476020468887c7a042465326338356131302d303761362d343639612d383564302d663965623866373763656138020405250a020201000405004c51e600" cdnthumblength="6030" cdnthumbheight="180" cdnthumbwidth="110" cdnmidheight="0" cdnmidwidth="0" cdnhdheight="0" cdnhdwidth="0" cdnmidimgurl="3057020100044b304902010002048a330c5e02032df7fa020445ebd476020468887c7a042465326338356131302d303761362d343639612d383564302d663965623866373763656138020405250a020201000405004c51e600" length="98793" md5="a05f9e7675502903a6fd5c8efdc923e9" hevc_mid_size="98793" originsourcemd5="58a8e6037fad2f890254c09ba227c573"><secHashInfoBase64>eyJwaGFzaCI6IjMwMDAwMDAwMDAwMDAwMDAiLCJwZHFoYXNoIjoiNGY1MDViODdiZmUwOGI1MDk3NTQxN2NiY2VlYjk0NjgzY2MxNjhkMTg3MDExZmI4OGIxNzM3NTYzMWQ0ZDZjZSJ9</secHashInfoBase64><live><duration>0</duration><size>0</size><md5 /><fileid /><hdsize>0</hdsize><hdmd5 /><hdfileid /><stillimagetimems>0</stillimagetimems></live></img><platform_signature /><imgdatahash /><ImgSourceInfo><ImgSourceUrl /><BizType>0</BizType></ImgSourceInfo></msg>
2025-07-29 19:13:51 | INFO | [ImageEcho] 保存图片信息成功，当前群 48097389945@chatroom 已存储 5 张图片
2025-07-29 19:13:51 | INFO | [TimerTask] 缓存图片消息: 1849827424
2025-07-29 19:14:11 | DEBUG | 收到消息: {'MsgId': 2047612564, 'FromUserName': {'string': '27852221909@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 49, 'Content': {'string': 'wxid_snv13qf05qjx11:\n<?xml version="1.0"?>\n<msg>\n\t<appmsg appid="" sdkver="0">\n\t\t<title>现在人满了也做不了 等等看啥情况</title>\n\t\t<des />\n\t\t<action />\n\t\t<type>57</type>\n\t\t<showtype>0</showtype>\n\t\t<soundtype>0</soundtype>\n\t\t<mediatagname />\n\t\t<messageext />\n\t\t<messageaction />\n\t\t<content />\n\t\t<contentattr>0</contentattr>\n\t\t<url />\n\t\t<lowurl />\n\t\t<dataurl />\n\t\t<lowdataurl />\n\t\t<songalbumurl />\n\t\t<songlyric />\n\t\t<template_id />\n\t\t<appattach>\n\t\t\t<totallen>0</totallen>\n\t\t\t<attachid />\n\t\t\t<emoticonmd5 />\n\t\t\t<fileext />\n\t\t\t<aeskey />\n\t\t</appattach>\n\t\t<extinfo />\n\t\t<sourceusername />\n\t\t<sourcedisplayname />\n\t\t<thumburl />\n\t\t<md5 />\n\t\t<statextstr />\n\t\t<refermsg>\n\t\t\t<type>1</type>\n\t\t\t<svrid>7030227075835249566</svrid>\n\t\t\t<fromusr>27852221909@chatroom</fromusr>\n\t\t\t<chatusr>wxid_2530z9t0joek22</chatusr>\n\t\t\t<displayname>慕ؓ悦ؓ˒</displayname>\n\t\t\t<msgsource>&lt;msgsource&gt;\n\t&lt;pua&gt;1&lt;/pua&gt;\n\t&lt;eggIncluded&gt;1&lt;/eggIncluded&gt;\n\t&lt;silence&gt;1&lt;/silence&gt;\n\t&lt;membercount&gt;145&lt;/membercount&gt;\n\t&lt;signature&gt;N0_V1_3mNoQnqq|v1_RieLnnLJ&lt;/signature&gt;\n\t&lt;tmp_node&gt;\n\t\t&lt;publisher-id&gt;&lt;/publisher-id&gt;\n\t&lt;/tmp_node&gt;\n&lt;/msgsource&gt;\n</msgsource>\n\t\t\t<createtime>1753787604</createtime>\n\t\t\t<content>差一个人啊</content>\n\t\t</refermsg>\n\t</appmsg>\n\t<fromusername>wxid_snv13qf05qjx11</fromusername>\n\t<scene>0</scene>\n\t<appinfo>\n\t\t<version>1</version>\n\t\t<appname></appname>\n\t</appinfo>\n\t<commenturl></commenturl>\n</msg>\n'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1753787661, 'MsgSource': '<msgsource>\n\t<alnode>\n\t\t<fr>1</fr>\n\t</alnode>\n\t<sec_msg_node>\n\t\t<uuid>0a23ed2d4007ad81c6f0166c9621cf55_</uuid>\n\t\t<risk-file-flag />\n\t\t<risk-file-md5-list />\n\t</sec_msg_node>\n\t<silence>1</silence>\n\t<membercount>145</membercount>\n\t<signature>N0_V1_fwZM8G+2|v1_9fRPkcxp</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'NewMsgId': 7033769943130969492, 'MsgSeq': 871409231}
2025-07-29 19:14:11 | DEBUG | 从群聊消息中提取发送者: wxid_snv13qf05qjx11
2025-07-29 19:14:11 | DEBUG | 使用已解析的XML处理引用消息
2025-07-29 19:14:11 | INFO | 收到引用消息: 消息ID:2047612564 来自:27852221909@chatroom 发送人:wxid_snv13qf05qjx11 内容:现在人满了也做不了 等等看啥情况 引用类型:1
2025-07-29 19:14:11 | INFO | [DouBaoImageToImage] 收到引用消息: 现在人满了也做不了 等等看啥情况
2025-07-29 19:14:11 | INFO | [TimerTask] 收到引用消息调试信息:
2025-07-29 19:14:11 | INFO |   - 消息内容: 现在人满了也做不了 等等看啥情况
2025-07-29 19:14:11 | INFO |   - 群组ID: 27852221909@chatroom
2025-07-29 19:14:11 | INFO |   - 发送人: wxid_snv13qf05qjx11
2025-07-29 19:14:11 | INFO |   - 引用信息: {'MsgType': 1, 'Content': '差一个人啊', 'Msgid': '7030227075835249566', 'NewMsgId': '7030227075835249566', 'ToWxid': 'wxid_4usgcju5ey9q29', 'FromWxid': '27852221909@chatroom', 'Nickname': '慕ؓ悦ؓ˒', 'MsgSource': '<msgsource>\n\t<pua>1</pua>\n\t<eggIncluded>1</eggIncluded>\n\t<silence>1</silence>\n\t<membercount>145</membercount>\n\t<signature>N0_V1_3mNoQnqq|v1_RieLnnLJ</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'Createtime': '1753787604', 'SenderWxid': 'wxid_snv13qf05qjx11'}
2025-07-29 19:14:11 | INFO |   - 引用消息ID: 
2025-07-29 19:14:11 | INFO |   - 引用消息类型: 
2025-07-29 19:14:11 | INFO |   - 引用消息内容: 差一个人啊
2025-07-29 19:14:11 | INFO |   - 引用消息发送人: wxid_snv13qf05qjx11
2025-07-29 19:14:51 | DEBUG | 收到消息: {'MsgId': 134553983, 'FromUserName': {'string': '48097389945@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 3, 'Content': {'string': 'wxid_w0rik7wwsvcy22:\n<?xml version="1.0"?>\n<msg>\n\t<img aeskey="bbaaee49eb503e7ff8e6ad98c4bc6b3a" encryver="1" cdnthumbaeskey="bbaaee49eb503e7ff8e6ad98c4bc6b3a" cdnthumburl="3057020100044b3049020100020473d808fb02032f53a3020448752d7002046888ac8a042439613766366238362d376333342d346432372d383933652d666262633736393037636538020405290a020201000405004c4cd300" cdnthumblength="7138" cdnthumbheight="180" cdnthumbwidth="136" cdnmidheight="0" cdnmidwidth="0" cdnhdheight="0" cdnhdwidth="0" cdnmidimgurl="3057020100044b3049020100020473d808fb02032f53a3020448752d7002046888ac8a042439613766366238362d376333342d346432372d383933652d666262633736393037636538020405290a020201000405004c4cd300" length="92633" md5="e6768d13f064ccd2cbbbd950b06de0dc" hevc_mid_size="92633" originsourcemd5="35ecf90ad0ef854eede0ff95257993c4">\n\t\t<secHashInfoBase64>eyJwaGFzaCI6Ijc0MjViMDE1MTAwMDAwMDAiLCJwZHFoYXNoIjoiY2ZjNjU0ZTYxMDkyMzQ2MDc5MjZjNjY2MzNiMmVjOGZjYjYzOGU4YjUyM2IzMWVjYzU2MzJkOWJiNjc4Y2IyNSJ9</secHashInfoBase64>\n\t\t<live>\n\t\t\t<duration>0</duration>\n\t\t\t<size>0</size>\n\t\t\t<md5 />\n\t\t\t<fileid />\n\t\t\t<hdsize>0</hdsize>\n\t\t\t<hdmd5 />\n\t\t\t<hdfileid />\n\t\t\t<stillimagetimems>0</stillimagetimems>\n\t\t</live>\n\t</img>\n\t<platform_signature />\n\t<imgdatahash />\n\t<ImgSourceInfo>\n\t\t<ImgSourceUrl />\n\t\t<BizType>0</BizType>\n\t</ImgSourceInfo>\n</msg>\n'}, 'Status': 3, 'ImgStatus': 2, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1753787700, 'MsgSource': '<msgsource>\n\t<bizflag>0</bizflag>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n\t<alnode>\n\t\t<fr>4</fr>\n\t</alnode>\n\t<sec_msg_node>\n\t\t<uuid>20c8c5f06aee83b199de1181d5c45240_</uuid>\n\t\t<risk-file-flag />\n\t\t<risk-file-md5-list />\n\t</sec_msg_node>\n\t<silence>0</silence>\n\t<membercount>73</membercount>\n\t<signature>N0_V1_OmZMCLpJ|v1_CDkWUBdt</signature>\n</msgsource>\n', 'PushContent': '一罐小可乐在群聊中发了一张图片', 'NewMsgId': 503301028514496684, 'MsgSeq': 871409232}
2025-07-29 19:14:51 | INFO | 收到图片消息: 消息ID:134553983 来自:48097389945@chatroom 发送人:wxid_w0rik7wwsvcy22 XML:<?xml version="1.0"?><msg><img aeskey="bbaaee49eb503e7ff8e6ad98c4bc6b3a" encryver="1" cdnthumbaeskey="bbaaee49eb503e7ff8e6ad98c4bc6b3a" cdnthumburl="3057020100044b3049020100020473d808fb02032f53a3020448752d7002046888ac8a042439613766366238362d376333342d346432372d383933652d666262633736393037636538020405290a020201000405004c4cd300" cdnthumblength="7138" cdnthumbheight="180" cdnthumbwidth="136" cdnmidheight="0" cdnmidwidth="0" cdnhdheight="0" cdnhdwidth="0" cdnmidimgurl="3057020100044b3049020100020473d808fb02032f53a3020448752d7002046888ac8a042439613766366238362d376333342d346432372d383933652d666262633736393037636538020405290a020201000405004c4cd300" length="92633" md5="e6768d13f064ccd2cbbbd950b06de0dc" hevc_mid_size="92633" originsourcemd5="35ecf90ad0ef854eede0ff95257993c4"><secHashInfoBase64>eyJwaGFzaCI6Ijc0MjViMDE1MTAwMDAwMDAiLCJwZHFoYXNoIjoiY2ZjNjU0ZTYxMDkyMzQ2MDc5MjZjNjY2MzNiMmVjOGZjYjYzOGU4YjUyM2IzMWVjYzU2MzJkOWJiNjc4Y2IyNSJ9</secHashInfoBase64><live><duration>0</duration><size>0</size><md5 /><fileid /><hdsize>0</hdsize><hdmd5 /><hdfileid /><stillimagetimems>0</stillimagetimems></live></img><platform_signature /><imgdatahash /><ImgSourceInfo><ImgSourceUrl /><BizType>0</BizType></ImgSourceInfo></msg>
2025-07-29 19:14:52 | INFO | [ImageEcho] 保存图片信息成功，当前群 48097389945@chatroom 已存储 5 张图片
2025-07-29 19:14:52 | INFO | [TimerTask] 缓存图片消息: 134553983
2025-07-29 19:15:09 | DEBUG | 收到消息: {'MsgId': 1570739157, 'FromUserName': {'string': '48097389945@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 1, 'Content': {'string': 'wxid_wlnzvr8ivgd422:\n武大图书馆是啥'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1753787719, 'MsgSource': '<msgsource>\n\t<pua>1</pua>\n\t<eggIncluded>1</eggIncluded>\n\t<silence>0</silence>\n\t<membercount>73</membercount>\n\t<signature>N0_V1_4bRIbrQY|v1_MOt5JXNV</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'PushContent': '锦岚 : 武大图书馆是啥', 'NewMsgId': 1447805752468222475, 'MsgSeq': 871409233}
2025-07-29 19:15:09 | INFO | 收到文本消息: 消息ID:1570739157 来自:48097389945@chatroom 发送人:wxid_wlnzvr8ivgd422 @:[] 内容:武大图书馆是啥
2025-07-29 19:15:10 | DEBUG | 处理消息内容: '武大图书馆是啥'
2025-07-29 19:15:10 | DEBUG | 消息内容 '武大图书馆是啥' 不匹配任何命令，忽略
2025-07-29 19:15:17 | DEBUG | 收到消息: {'MsgId': 1498998445, 'FromUserName': {'string': '48097389945@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 49, 'Content': {'string': 'wxid_w0rik7wwsvcy22:\n<?xml version="1.0"?>\n<msg>\n\t<appmsg appid="" sdkver="0">\n\t\t<title>豆包 给图片美腿加上黑丝</title>\n\t\t<type>57</type>\n\t\t<appattach>\n\t\t\t<cdnthumbaeskey />\n\t\t\t<aeskey />\n\t\t</appattach>\n\t\t<refermsg>\n\t\t\t<type>3</type>\n\t\t\t<svrid>503301028514496684</svrid>\n\t\t\t<fromusr>wxid_w0rik7wwsvcy22</fromusr>\n\t\t\t<chatusr />\n\t\t\t<displayname>一罐小可乐</displayname>\n\t\t\t<content>&lt;msg&gt;&lt;img hdlength="0" length="92633" aeskey="bbaaee49eb503e7ff8e6ad98c4bc6b3a" encryver="1" md5="e6768d13f064ccd2cbbbd950b06de0dc" originsourcemd5="35ecf90ad0ef854eede0ff95257993c4" filekey="48097389945@chatroom_160514_1753787700" uploadcontinuecount="0" imgsourceurl="" hevc_mid_size="92633" cdnbigimgurl="" cdnmidimgurl="3057020100044b3049020100020473d808fb02032f53a3020448752d7002046888ac8a042439613766366238362d376333342d346432372d383933652d666262633736393037636538020405290a020201000405004c4cd300" cdnthumburl="3057020100044b3049020100020473d808fb02032f53a3020448752d7002046888ac8a042439613766366238362d376333342d346432372d383933652d666262633736393037636538020405290a020201000405004c4cd300" cdnthumblength="7138" cdnthumbwidth="136" cdnthumbheight="180" cdnthumbaeskey="bbaaee49eb503e7ff8e6ad98c4bc6b3a"&gt;&lt;secHashInfoBase64&gt;eyJwaGFzaCI6Ijc0MjViMDE1MTAwMDAwMDAiLCJwZHFoYXNoIjoiY2ZjNjU0ZTYxMDkyMzQ2MDc5MjZjNjY2MzNiMmVjOGZjYjYzOGU4YjUyM2IzMWVjYzU2MzJkOWJiNjc4Y2IyNSJ9&lt;/secHashInfoBase64&gt;&lt;/img&gt;&lt;appinfo&gt;&lt;appid&gt;&lt;/appid&gt;&lt;appname&gt;&lt;/appname&gt;&lt;version&gt;0&lt;/version&gt;&lt;isforceupdate&gt;1&lt;/isforceupdate&gt;&lt;mediatagname&gt;&lt;/mediatagname&gt;&lt;messageext&gt;&lt;/messageext&gt;&lt;messageaction&gt;&lt;/messageaction&gt;&lt;/appinfo&gt;&lt;/msg&gt;</content>\n\t\t\t<msgsource>&lt;msgsource&gt;&lt;svr_create_time&gt;1753787701&lt;/svr_create_time&gt;&lt;bizflag&gt;0&lt;/bizflag&gt;&lt;sec_msg_node&gt;&lt;uuid&gt;&lt;![CDATA[20c8c5f06aee83b199de1181d5c45240_]]&gt;&lt;/uuid&gt;&lt;/sec_msg_node&gt;&lt;tmp_node&gt;&lt;publisher-id&gt;&lt;![CDATA[msg_3497823020907248142|wxid_90s9h2i2gcb622|53435110965@chatroom]]&gt;&lt;/publisher-id&gt;&lt;/tmp_node&gt;&lt;alnode&gt;&lt;fr&gt;4&lt;/fr&gt;&lt;/alnode&gt;&lt;/msgsource&gt;</msgsource>\n\t\t\t<createtime>1753787701</createtime>\n\t\t</refermsg>\n\t</appmsg>\n\t<fromusername>wxid_w0rik7wwsvcy22</fromusername>\n\t<scene>0</scene>\n\t<appinfo>\n\t\t<version>1</version>\n\t\t<appname></appname>\n\t</appinfo>\n\t<commenturl></commenturl>\n</msg>\n'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1753787727, 'MsgSource': '<msgsource>\n\t<bizflag>0</bizflag>\n\t<pua>1</pua>\n\t<eggIncluded>1</eggIncluded>\n\t<sec_msg_node>\n\t\t<uuid>020515735622a4a4ba6cd81a0bd7d174_</uuid>\n\t\t<risk-file-flag />\n\t\t<risk-file-md5-list />\n\t</sec_msg_node>\n\t<silence>0</silence>\n\t<membercount>73</membercount>\n\t<signature>N0_V1_szynxN1f|v1_+/9UisZo</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'PushContent': '一罐小可乐 : 豆包 给图片美腿加上黑丝', 'NewMsgId': 6997920699862695186, 'MsgSeq': 871409234}
2025-07-29 19:15:17 | DEBUG | 从群聊消息中提取发送者: wxid_w0rik7wwsvcy22
2025-07-29 19:15:17 | DEBUG | 使用已解析的XML处理引用消息
2025-07-29 19:15:17 | INFO | 收到引用消息: 消息ID:1498998445 来自:48097389945@chatroom 发送人:wxid_w0rik7wwsvcy22 内容:豆包 给图片美腿加上黑丝 引用类型:3
2025-07-29 19:15:18 | INFO | [DouBaoImageToImage] 收到引用消息: 豆包 给图片美腿加上黑丝
2025-07-29 19:15:18 | INFO | [DouBaoImageToImage] 引用图片图生图 - 提示词: '给图片美腿加上黑丝，比例「2:3」', 比例: 832x1248, 风格: None
2025-07-29 19:15:18 | INFO | [DouBaoImageToImage] 被引用消息类型: 3
2025-07-29 19:15:19 | INFO | [DouBaoImageToImage] 成功下载引用的图片并保存到: temp\doubao_image_to_image\quoted_image_1753787719.jpg
2025-07-29 19:15:19 | INFO | [DouBaoImageToImage] 开始豆包AI处理流程(无通知)，用户: wxid_w0rik7wwsvcy22, 提示词: 给图片美腿加上黑丝，比例「2:3」, 比例: 832x1248
2025-07-29 19:15:19 | INFO | [DouBaoImageToImage] 步骤1: 验证图片文件...
2025-07-29 19:15:19 | INFO | [DouBaoImageToImage] 图片验证成功，大小: 542.3KB
2025-07-29 19:15:19 | INFO | [DouBaoImageToImage] 步骤2: 调用豆包AI接口...
2025-07-29 19:15:19 | INFO | [DouBaoImageToImage] 开始豆包AI图生图处理，提示词: 给图片美腿加上黑丝，比例「2:3」
2025-07-29 19:15:19 | INFO | [DouBaoImageToImage] 使用比例: 2:3 (832x1248)
2025-07-29 19:15:23 | ERROR | [DouBaoImageToImage] 豆包AI处理失败
2025-07-29 19:15:23 | WARNING | [DouBaoImageToImage] 豆包AI处理失败
2025-07-29 19:15:24 | INFO | 发送文字消息: 对方wxid:48097389945@chatroom at:['wxid_w0rik7wwsvcy22'] 内容:@一罐小可乐 豆包AI处理失败，等会再试试吧
2025-07-29 19:15:24 | INFO | [TimerTask] 收到引用消息调试信息:
2025-07-29 19:15:24 | INFO |   - 消息内容: 豆包 给图片美腿加上黑丝
2025-07-29 19:15:24 | INFO |   - 群组ID: 48097389945@chatroom
2025-07-29 19:15:24 | INFO |   - 发送人: wxid_w0rik7wwsvcy22
2025-07-29 19:15:24 | INFO |   - 引用信息: {'MsgType': 3, 'Content': '<?xml version="1.0"?>\n<msg>\n\t<appmsg appid="" sdkver="0">\n\t\t<title>豆包 给图片美腿加上黑丝</title>\n\t\t<type>57</type>\n\t\t<appattach>\n\t\t\t<cdnthumbaeskey />\n\t\t\t<aeskey />\n\t\t</appattach>\n\t\t<refermsg>\n\t\t\t<type>3</type>\n\t\t\t<svrid>503301028514496684</svrid>\n\t\t\t<fromusr>wxid_w0rik7wwsvcy22</fromusr>\n\t\t\t<chatusr />\n\t\t\t<displayname>一罐小可乐</displayname>\n\t\t\t<content>&lt;msg&gt;&lt;img hdlength="0" length="92633" aeskey="bbaaee49eb503e7ff8e6ad98c4bc6b3a" encryver="1" md5="e6768d13f064ccd2cbbbd950b06de0dc" originsourcemd5="35ecf90ad0ef854eede0ff95257993c4" filekey="48097389945@chatroom_160514_1753787700" uploadcontinuecount="0" imgsourceurl="" hevc_mid_size="92633" cdnbigimgurl="" cdnmidimgurl="3057020100044b3049020100020473d808fb02032f53a3020448752d7002046888ac8a042439613766366238362d376333342d346432372d383933652d666262633736393037636538020405290a020201000405004c4cd300" cdnthumburl="3057020100044b3049020100020473d808fb02032f53a3020448752d7002046888ac8a042439613766366238362d376333342d346432372d383933652d666262633736393037636538020405290a020201000405004c4cd300" cdnthumblength="7138" cdnthumbwidth="136" cdnthumbheight="180" cdnthumbaeskey="bbaaee49eb503e7ff8e6ad98c4bc6b3a"&gt;&lt;secHashInfoBase64&gt;eyJwaGFzaCI6Ijc0MjViMDE1MTAwMDAwMDAiLCJwZHFoYXNoIjoiY2ZjNjU0ZTYxMDkyMzQ2MDc5MjZjNjY2MzNiMmVjOGZjYjYzOGU4YjUyM2IzMWVjYzU2MzJkOWJiNjc4Y2IyNSJ9&lt;/secHashInfoBase64&gt;&lt;/img&gt;&lt;appinfo&gt;&lt;appid&gt;&lt;/appid&gt;&lt;appname&gt;&lt;/appname&gt;&lt;version&gt;0&lt;/version&gt;&lt;isforceupdate&gt;1&lt;/isforceupdate&gt;&lt;mediatagname&gt;&lt;/mediatagname&gt;&lt;messageext&gt;&lt;/messageext&gt;&lt;messageaction&gt;&lt;/messageaction&gt;&lt;/appinfo&gt;&lt;/msg&gt;</content>\n\t\t\t<msgsource>&lt;msgsource&gt;&lt;svr_create_time&gt;1753787701&lt;/svr_create_time&gt;&lt;bizflag&gt;0&lt;/bizflag&gt;&lt;sec_msg_node&gt;&lt;uuid&gt;&lt;![CDATA[20c8c5f06aee83b199de1181d5c45240_]]&gt;&lt;/uuid&gt;&lt;/sec_msg_node&gt;&lt;tmp_node&gt;&lt;publisher-id&gt;&lt;![CDATA[msg_3497823020907248142|wxid_90s9h2i2gcb622|53435110965@chatroom]]&gt;&lt;/publisher-id&gt;&lt;/tmp_node&gt;&lt;alnode&gt;&lt;fr&gt;4&lt;/fr&gt;&lt;/alnode&gt;&lt;/msgsource&gt;</msgsource>\n\t\t\t<createtime>1753787701</createtime>\n\t\t</refermsg>\n\t</appmsg>\n\t<fromusername>wxid_w0rik7wwsvcy22</fromusername>\n\t<scene>0</scene>\n\t<appinfo>\n\t\t<version>1</version>\n\t\t<appname></appname>\n\t</appinfo>\n\t<commenturl></commenturl>\n</msg>\n', 'Msgid': '503301028514496684', 'NewMsgId': '503301028514496684', 'ToWxid': 'wxid_4usgcju5ey9q29', 'FromWxid': '48097389945@chatroom', 'Nickname': '一罐小可乐', 'MsgSource': '<msgsource><svr_create_time>1753787701</svr_create_time><bizflag>0</bizflag><sec_msg_node><uuid><![CDATA[20c8c5f06aee83b199de1181d5c45240_]]></uuid></sec_msg_node><tmp_node><publisher-id><![CDATA[msg_3497823020907248142|wxid_90s9h2i2gcb622|53435110965@chatroom]]></publisher-id></tmp_node><alnode><fr>4</fr></alnode></msgsource>', 'Createtime': '1753787701', 'SenderWxid': 'wxid_w0rik7wwsvcy22'}
2025-07-29 19:15:24 | INFO |   - 引用消息ID: 
2025-07-29 19:15:24 | INFO |   - 引用消息类型: 
2025-07-29 19:15:24 | INFO |   - 引用消息内容: <?xml version="1.0"?>
<msg>
	<appmsg appid="" sdkver="0">
		<title>豆包 给图片美腿加上黑丝</title>
		<type>57</type>
		<appattach>
			<cdnthumbaeskey />
			<aeskey />
		</appattach>
		<refermsg>
			<type>3</type>
			<svrid>503301028514496684</svrid>
			<fromusr>wxid_w0rik7wwsvcy22</fromusr>
			<chatusr />
			<displayname>一罐小可乐</displayname>
			<content>&lt;msg&gt;&lt;img hdlength="0" length="92633" aeskey="bbaaee49eb503e7ff8e6ad98c4bc6b3a" encryver="1" md5="e6768d13f064ccd2cbbbd950b06de0dc" originsourcemd5="35ecf90ad0ef854eede0ff95257993c4" filekey="48097389945@chatroom_160514_1753787700" uploadcontinuecount="0" imgsourceurl="" hevc_mid_size="92633" cdnbigimgurl="" cdnmidimgurl="3057020100044b3049020100020473d808fb02032f53a3020448752d7002046888ac8a042439613766366238362d376333342d346432372d383933652d666262633736393037636538020405290a020201000405004c4cd300" cdnthumburl="3057020100044b3049020100020473d808fb02032f53a3020448752d7002046888ac8a042439613766366238362d376333342d346432372d383933652d666262633736393037636538020405290a020201000405004c4cd300" cdnthumblength="7138" cdnthumbwidth="136" cdnthumbheight="180" cdnthumbaeskey="bbaaee49eb503e7ff8e6ad98c4bc6b3a"&gt;&lt;secHashInfoBase64&gt;eyJwaGFzaCI6Ijc0MjViMDE1MTAwMDAwMDAiLCJwZHFoYXNoIjoiY2ZjNjU0ZTYxMDkyMzQ2MDc5MjZjNjY2MzNiMmVjOGZjYjYzOGU4YjUyM2IzMWVjYzU2MzJkOWJiNjc4Y2IyNSJ9&lt;/secHashInfoBase64&gt;&lt;/img&gt;&lt;appinfo&gt;&lt;appid&gt;&lt;/appid&gt;&lt;appname&gt;&lt;/appname&gt;&lt;version&gt;0&lt;/version&gt;&lt;isforceupdate&gt;1&lt;/isforceupdate&gt;&lt;mediatagname&gt;&lt;/mediatagname&gt;&lt;messageext&gt;&lt;/messageext&gt;&lt;messageaction&gt;&lt;/messageaction&gt;&lt;/appinfo&gt;&lt;/msg&gt;</content>
			<msgsource>&lt;msgsource&gt;&lt;svr_create_time&gt;1753787701&lt;/svr_create_time&gt;&lt;bizflag&gt;0&lt;/bizflag&gt;&lt;sec_msg_node&gt;&lt;uuid&gt;&lt;![CDATA[20c8c5f06aee83b199de1181d5c45240_]]&gt;&lt;/uuid&gt;&lt;/sec_msg_node&gt;&lt;tmp_node&gt;&lt;publisher-id&gt;&lt;![CDATA[msg_3497823020907248142|wxid_90s9h2i2gcb622|53435110965@chatroom]]&gt;&lt;/publisher-id&gt;&lt;/tmp_node&gt;&lt;alnode&gt;&lt;fr&gt;4&lt;/fr&gt;&lt;/alnode&gt;&lt;/msgsource&gt;</msgsource>
			<createtime>1753787701</createtime>
		</refermsg>
	</appmsg>
	<fromusername>wxid_w0rik7wwsvcy22</fromusername>
	<scene>0</scene>
	<appinfo>
		<version>1</version>
		<appname></appname>
	</appinfo>
	<commenturl></commenturl>
</msg>

2025-07-29 19:15:24 | INFO |   - 引用消息发送人: wxid_w0rik7wwsvcy22
2025-07-29 19:15:25 | DEBUG | 收到消息: {'MsgId': 1899407708, 'FromUserName': {'string': '47325400669@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 1, 'Content': {'string': 'wxid_62fiham2pn7521:\n就剩老猪了吗'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1753787735, 'MsgSource': '<msgsource>\n\t<pua>1</pua>\n\t<eggIncluded>1</eggIncluded>\n\t<silence>0</silence>\n\t<membercount>222</membercount>\n\t<signature>N0_V1_7wAV7SKn|v1_aq0KgmAK</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'PushContent': '八戒。 : 就剩老猪了吗', 'NewMsgId': 3780079339908724739, 'MsgSeq': 871409237}
2025-07-29 19:15:25 | INFO | 收到文本消息: 消息ID:1899407708 来自:47325400669@chatroom 发送人:wxid_62fiham2pn7521 @:[] 内容:就剩老猪了吗
2025-07-29 19:15:25 | DEBUG | 处理消息内容: '就剩老猪了吗'
2025-07-29 19:15:25 | DEBUG | 消息内容 '就剩老猪了吗' 不匹配任何命令，忽略
2025-07-29 19:15:28 | DEBUG | 收到消息: {'MsgId': 26848786, 'FromUserName': {'string': '47325400669@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 47, 'Content': {'string': 'wxid_62fiham2pn7521:\n<msg><emoji fromusername = "wxid_62fiham2pn7521" tousername = "47325400669@chatroom" type="2" idbuffer="media:0_0" md5="2b42c8682f7eeae8d1e43a7e07f083cb" len = "2364817" productid="" androidmd5="2b42c8682f7eeae8d1e43a7e07f083cb" androidlen="2364817" s60v3md5 = "2b42c8682f7eeae8d1e43a7e07f083cb" s60v3len="2364817" s60v5md5 = "2b42c8682f7eeae8d1e43a7e07f083cb" s60v5len="2364817" cdnurl = "http://vweixinf.tc.qq.com/110/20401/stodownload?m=2b42c8682f7eeae8d1e43a7e07f083cb&amp;filekey=30440201010430302e02016e04025348042032623432633836383266376565616538643165343361376530376630383363620203241591040d00000004627466730000000132&amp;hy=SH&amp;storeid=26821caf20003d6b84206bcae0000006e01004fb153482e92f1f1573ffdf27&amp;ef=1&amp;bizid=1022" designerid = "" thumburl = "" encrypturl = "http://vweixinf.tc.qq.com/110/20402/stodownload?m=8fb5a37135409a27df2dd330b07b1130&amp;filekey=30440201010430302e02016e040253480420386662356133373133353430396132376466326464333330623037623131333002032415a0040d00000004627466730000000132&amp;hy=SH&amp;storeid=26821caf20007011a4206bcae0000006e02004fb253482e92f1f1573ffdf4a&amp;ef=2&amp;bizid=1022" aeskey= "df28678d30a343e0aecdd77ff35df1dc" externurl = "http://vweixinf.tc.qq.com/110/20403/stodownload?m=58ae99bec620c8dab3fccb78e7a8578a&amp;filekey=30440201010430302e02016e04025348042035386165393962656336323063386461623366636362373865376138353738610203018c70040d00000004627466730000000132&amp;hy=SH&amp;storeid=26821caf2000a09544206bcae0000006e03004fb353482e92f1f1573ffdf65&amp;ef=3&amp;bizid=1022" externmd5 = "982bedfb64ba749f93005f599bfa0e39" width= "240" height= "240" tpurl= "" tpauthkey= "" attachedtext= "" attachedtextcolor= "" lensid= "" emojiattr= "" linkid= "" desc= "" ></emoji>  </msg>'}, 'Status': 3, 'ImgStatus': 2, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1753787735, 'MsgSource': '<msgsource>\n\t<silence>0</silence>\n\t<membercount>222</membercount>\n\t<signature>N0_V1_WGiDxwVL|v1_dAIvyayv</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'PushContent': '八戒。在群聊中发了一个表情', 'NewMsgId': 1671372432035086192, 'MsgSeq': 871409238}
2025-07-29 19:15:28 | INFO | 收到表情消息: 消息ID:26848786 来自:47325400669@chatroom 发送人:wxid_62fiham2pn7521 MD5:2b42c8682f7eeae8d1e43a7e07f083cb 大小:2364817
2025-07-29 19:15:28 | DEBUG | [DoubaoImageRecognition] 缓存表情消息: 1671372432035086192
2025-07-29 19:15:28 | DEBUG | 收到消息: {'MsgId': 1577256596, 'FromUserName': {'string': '47325400669@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 1, 'Content': {'string': 'wxid_62fiham2pn7521:\n重载所有插件'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1753787735, 'MsgSource': '<msgsource>\n\t<pua>1</pua>\n\t<eggIncluded>1</eggIncluded>\n\t<silence>0</silence>\n\t<membercount>222</membercount>\n\t<signature>N0_V1_7XZud2pg|v1_opq+aItN</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'PushContent': '八戒。 : 重载所有插件', 'NewMsgId': 7896554354253533669, 'MsgSeq': 871409239}
2025-07-29 19:15:28 | INFO | 收到文本消息: 消息ID:1577256596 来自:47325400669@chatroom 发送人:wxid_62fiham2pn7521 @:[] 内容:重载所有插件
2025-07-29 19:15:29 | INFO | 发送文字消息: 对方wxid:47325400669@chatroom at: 内容:你没有权限使用此命令
2025-07-29 19:15:29 | DEBUG | 处理消息内容: '重载所有插件'
2025-07-29 19:15:29 | DEBUG | 消息内容 '重载所有插件' 不匹配任何命令，忽略
2025-07-29 19:15:29 | DEBUG | 收到消息: {'MsgId': 1158039698, 'FromUserName': {'string': '47325400669@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 1, 'Content': {'string': 'heaventt:\n你没有权限使用此命令'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1753787736, 'MsgSource': '<msgsource>\n\t<silence>0</silence>\n\t<membercount>222</membercount>\n\t<signature>N0_V1_uZYexBsO|v1_f6qS4jbY</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'PushContent': 'heaven : 你没有权限使用此命令', 'NewMsgId': 1640935512310452962, 'MsgSeq': 871409240}
2025-07-29 19:15:29 | INFO | 收到文本消息: 消息ID:1158039698 来自:47325400669@chatroom 发送人:heaventt @:[] 内容:你没有权限使用此命令
2025-07-29 19:15:29 | DEBUG | 处理消息内容: '你没有权限使用此命令'
2025-07-29 19:15:29 | DEBUG | 消息内容 '你没有权限使用此命令' 不匹配任何命令，忽略
2025-07-29 19:15:32 | DEBUG | 收到消息: {'MsgId': 1576991929, 'FromUserName': {'string': '47325400669@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 1, 'Content': {'string': 'wxid_n3arbyz7klbc12:\n你没有权限使用此命令'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1753787736, 'MsgSource': '<msgsource>\n\t<silence>0</silence>\n\t<membercount>222</membercount>\n\t<signature>N0_V1_sSyZ5Upk|v1_RrYMB5P0</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'PushContent': 'Ghost : 你没有权限使用此命令', 'NewMsgId': 8936408686984524703, 'MsgSeq': 871409241}
2025-07-29 19:15:32 | INFO | 收到文本消息: 消息ID:1576991929 来自:47325400669@chatroom 发送人:wxid_n3arbyz7klbc12 @:[] 内容:你没有权限使用此命令
2025-07-29 19:15:32 | DEBUG | 处理消息内容: '你没有权限使用此命令'
2025-07-29 19:15:32 | DEBUG | 消息内容 '你没有权限使用此命令' 不匹配任何命令，忽略
2025-07-29 19:15:33 | DEBUG | [TempFileManager] 已清理文件: temp\doubao_image_to_image\quoted_image_1753787719.jpg
2025-07-29 19:15:35 | DEBUG | 收到消息: {'MsgId': 1116217994, 'FromUserName': {'string': '47325400669@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 1, 'Content': {'string': 'wxid_l5im9jaxhr4412:\n你没有权限使用此命令'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1753787736, 'MsgSource': '<msgsource>\n\t<silence>0</silence>\n\t<membercount>222</membercount>\n\t<signature>N0_V1_iFPbe/4c|v1_806GqId/</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'PushContent': '小球子 : 你没有权限使用此命令', 'NewMsgId': 1874457227108138329, 'MsgSeq': 871409242}
2025-07-29 19:15:35 | INFO | 收到文本消息: 消息ID:1116217994 来自:47325400669@chatroom 发送人:wxid_l5im9jaxhr4412 @:[] 内容:你没有权限使用此命令
2025-07-29 19:15:35 | DEBUG | 处理消息内容: '你没有权限使用此命令'
2025-07-29 19:15:35 | DEBUG | 消息内容 '你没有权限使用此命令' 不匹配任何命令，忽略
2025-07-29 19:15:38 | DEBUG | 收到消息: {'MsgId': 1632549947, 'FromUserName': {'string': '47325400669@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 1, 'Content': {'string': 'wxid_fh84okl6f5wp22:\n你没有权限使用此命令'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1753787736, 'MsgSource': '<msgsource>\n\t<silence>0</silence>\n\t<membercount>222</membercount>\n\t<signature>N0_V1_7RRNtqA5|v1_+7wRlqkT</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'PushContent': '阿猪米德 : 你没有权限使用此命令', 'NewMsgId': 5029875990441379520, 'MsgSeq': 871409243}
2025-07-29 19:15:38 | INFO | 收到文本消息: 消息ID:1632549947 来自:47325400669@chatroom 发送人:wxid_fh84okl6f5wp22 @:[] 内容:你没有权限使用此命令
2025-07-29 19:15:38 | DEBUG | 处理消息内容: '你没有权限使用此命令'
2025-07-29 19:15:38 | DEBUG | 消息内容 '你没有权限使用此命令' 不匹配任何命令，忽略
2025-07-29 19:15:41 | DEBUG | 收到消息: {'MsgId': 1951796948, 'FromUserName': {'string': '48097389945@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 49, 'Content': {'string': 'wxid_laurnst5xn0q22:\n<?xml version="1.0"?>\n<msg>\n\t<appmsg appid="" sdkver="0">\n\t\t<title>性骚扰事件</title>\n\t\t<des />\n\t\t<username />\n\t\t<action>view</action>\n\t\t<type>57</type>\n\t\t<showtype>0</showtype>\n\t\t<content />\n\t\t<url />\n\t\t<lowurl />\n\t\t<forwardflag>0</forwardflag>\n\t\t<dataurl />\n\t\t<lowdataurl />\n\t\t<contentattr>0</contentattr>\n\t\t<streamvideo>\n\t\t\t<streamvideourl />\n\t\t\t<streamvideototaltime>0</streamvideototaltime>\n\t\t\t<streamvideotitle />\n\t\t\t<streamvideowording />\n\t\t\t<streamvideoweburl />\n\t\t\t<streamvideothumburl />\n\t\t\t<streamvideoaduxinfo />\n\t\t\t<streamvideopublishid />\n\t\t</streamvideo>\n\t\t<canvasPageItem>\n\t\t\t<canvasPageXml><![CDATA[]]></canvasPageXml>\n\t\t</canvasPageItem>\n\t\t<refermsg>\n\t\t\t<type>1</type>\n\t\t\t<svrid>1447805752468222475</svrid>\n\t\t\t<fromusr>48097389945@chatroom</fromusr>\n\t\t\t<chatusr>wxid_wlnzvr8ivgd422</chatusr>\n\t\t\t<displayname>锦岚</displayname>\n\t\t\t<msgsource>&lt;msgsource&gt;\n\t&lt;pua&gt;1&lt;/pua&gt;\n\t&lt;eggIncluded&gt;1&lt;/eggIncluded&gt;\n\t&lt;silence&gt;1&lt;/silence&gt;\n\t&lt;membercount&gt;73&lt;/membercount&gt;\n\t&lt;signature&gt;N0_V1_nbt+xeTS|v1_E+f9OYCS&lt;/signature&gt;\n\t&lt;tmp_node&gt;\n\t\t&lt;publisher-id&gt;&lt;/publisher-id&gt;\n\t&lt;/tmp_node&gt;\n&lt;/msgsource&gt;\n</msgsource>\n\t\t\t<content>\n武大图书馆是啥</content>\n\t\t\t<strid />\n\t\t\t<createtime>1753787719</createtime>\n\t\t</refermsg>\n\t\t<appattach>\n\t\t\t<totallen>0</totallen>\n\t\t\t<attachid />\n\t\t\t<cdnattachurl />\n\t\t\t<emoticonmd5 />\n\t\t\t<aeskey />\n\t\t\t<fileext />\n\t\t\t<islargefilemsg>0</islargefilemsg>\n\t\t</appattach>\n\t\t<extinfo />\n\t\t<androidsource>0</androidsource>\n\t\t<thumburl />\n\t\t<mediatagname />\n\t\t<messageaction><![CDATA[]]></messageaction>\n\t\t<messageext><![CDATA[]]></messageext>\n\t\t<emoticongift>\n\t\t\t<packageflag>0</packageflag>\n\t\t\t<packageid />\n\t\t</emoticongift>\n\t\t<emoticonshared>\n\t\t\t<packageflag>0</packageflag>\n\t\t\t<packageid />\n\t\t</emoticonshared>\n\t\t<designershared>\n\t\t\t<designeruin>0</designeruin>\n\t\t\t<designername>null</designername>\n\t\t\t<designerrediretcturl><![CDATA[null]]></designerrediretcturl>\n\t\t</designershared>\n\t\t<emotionpageshared>\n\t\t\t<tid>0</tid>\n\t\t\t<title>null</title>\n\t\t\t<desc>null</desc>\n\t\t\t<iconUrl><![CDATA[null]]></iconUrl>\n\t\t\t<secondUrl>null</secondUrl>\n\t\t\t<pageType>0</pageType>\n\t\t\t<setKey>null</setKey>\n\t\t</emotionpageshared>\n\t\t<webviewshared>\n\t\t\t<shareUrlOriginal />\n\t\t\t<shareUrlOpen />\n\t\t\t<jsAppId />\n\t\t\t<publisherId />\n\t\t\t<publisherReqId />\n\t\t</webviewshared>\n\t\t<template_id />\n\t\t<md5 />\n\t\t<websearch>\n\t\t\t<rec_category>0</rec_category>\n\t\t\t<channelId>0</channelId>\n\t\t</websearch>\n\t\t<weappinfo>\n\t\t\t<username />\n\t\t\t<appid />\n\t\t\t<appservicetype>0</appservicetype>\n\t\t\t<secflagforsinglepagemode>0</secflagforsinglepagemode>\n\t\t\t<videopageinfo>\n\t\t\t\t<thumbwidth>0</thumbwidth>\n\t\t\t\t<thumbheight>0</thumbheight>\n\t\t\t\t<fromopensdk>0</fromopensdk>\n\t\t\t</videopageinfo>\n\t\t</weappinfo>\n\t\t<statextstr />\n\t\t<musicShareItem>\n\t\t\t<musicDuration>0</musicDuration>\n\t\t</musicShareItem>\n\t\t<finderLiveProductShare>\n\t\t\t<finderLiveID><![CDATA[]]></finderLiveID>\n\t\t\t<finderUsername><![CDATA[]]></finderUsername>\n\t\t\t<finderObjectID><![CDATA[]]></finderObjectID>\n\t\t\t<finderNonceID><![CDATA[]]></finderNonceID>\n\t\t\t<liveStatus><![CDATA[]]></liveStatus>\n\t\t\t<appId><![CDATA[]]></appId>\n\t\t\t<pagePath><![CDATA[]]></pagePath>\n\t\t\t<productId><![CDATA[]]></productId>\n\t\t\t<coverUrl><![CDATA[]]></coverUrl>\n\t\t\t<productTitle><![CDATA[]]></productTitle>\n\t\t\t<marketPrice><![CDATA[0]]></marketPrice>\n\t\t\t<sellingPrice><![CDATA[0]]></sellingPrice>\n\t\t\t<platformHeadImg><![CDATA[]]></platformHeadImg>\n\t\t\t<platformName><![CDATA[]]></platformName>\n\t\t\t<shopWindowId><![CDATA[]]></shopWindowId>\n\t\t\t<flashSalePrice><![CDATA[0]]></flashSalePrice>\n\t\t\t<flashSaleEndTime><![CDATA[0]]></flashSaleEndTime>\n\t\t\t<ecSource><![CDATA[]]></ecSource>\n\t\t\t<sellingPriceWording><![CDATA[]]></sellingPriceWording>\n\t\t\t<platformIconURL><![CDATA[]]></platformIconURL>\n\t\t\t<firstProductTagURL><![CDATA[]]></firstProductTagURL>\n\t\t\t<firstProductTagAspectRatioString><![CDATA[0.0]]></firstProductTagAspectRatioString>\n\t\t\t<secondProductTagURL><![CDATA[]]></secondProductTagURL>\n\t\t\t<secondProductTagAspectRatioString><![CDATA[0.0]]></secondProductTagAspectRatioString>\n\t\t\t<firstGuaranteeWording><![CDATA[]]></firstGuaranteeWording>\n\t\t\t<secondGuaranteeWording><![CDATA[]]></secondGuaranteeWording>\n\t\t\t<thirdGuaranteeWording><![CDATA[]]></thirdGuaranteeWording>\n\t\t\t<isPriceBeginShow>false</isPriceBeginShow>\n\t\t\t<lastGMsgID><![CDATA[]]></lastGMsgID>\n\t\t\t<promoterKey><![CDATA[]]></promoterKey>\n\t\t\t<discountWording><![CDATA[]]></discountWording>\n\t\t\t<priceSuffixDescription><![CDATA[]]></priceSuffixDescription>\n\t\t\t<productCardKey><![CDATA[]]></productCardKey>\n\t\t\t<isWxShop><![CDATA[]]></isWxShop>\n\t\t\t<brandIconUrl><![CDATA[]]></brandIconUrl>\n\t\t\t<rIconUrl><![CDATA[]]></rIconUrl>\n\t\t\t<rIconUrlDarkMode><![CDATA[]]></rIconUrlDarkMode>\n\t\t\t<topShopIconUrl><![CDATA[]]></topShopIconUrl>\n\t\t\t<topShopIconUrlDarkMode><![CDATA[]]></topShopIconUrlDarkMode>\n\t\t\t<simplifyTopShopIconUrl><![CDATA[]]></simplifyTopShopIconUrl>\n\t\t\t<simplifyTopShopIconUrlDarkmode><![CDATA[]]></simplifyTopShopIconUrlDarkmode>\n\t\t\t<topShopIconWidth><![CDATA[0]]></topShopIconWidth>\n\t\t\t<topShopIconHeight><![CDATA[0]]></topShopIconHeight>\n\t\t\t<simplifyTopShopIconWidth><![CDATA[0]]></simplifyTopShopIconWidth>\n\t\t\t<simplifyTopShopIconHeight><![CDATA[0]]></simplifyTopShopIconHeight>\n\t\t\t<showBoxItemStringList />\n\t\t</finderLiveProductShare>\n\t\t<finderOrder>\n\t\t\t<appID><![CDATA[]]></appID>\n\t\t\t<orderID><![CDATA[]]></orderID>\n\t\t\t<path><![CDATA[]]></path>\n\t\t\t<priceWording><![CDATA[]]></priceWording>\n\t\t\t<stateWording><![CDATA[]]></stateWording>\n\t\t\t<productImageURL><![CDATA[]]></productImageURL>\n\t\t\t<products><![CDATA[]]></products>\n\t\t\t<productsCount><![CDATA[0]]></productsCount>\n\t\t\t<orderType><![CDATA[0]]></orderType>\n\t\t\t<newPriceWording><![CDATA[]]></newPriceWording>\n\t\t\t<newStateWording><![CDATA[]]></newStateWording>\n\t\t\t<useNewWording><![CDATA[0]]></useNewWording>\n\t\t</finderOrder>\n\t\t<finderShopWindowShare>\n\t\t\t<finderUsername><![CDATA[]]></finderUsername>\n\t\t\t<avatar><![CDATA[]]></avatar>\n\t\t\t<nickname><![CDATA[]]></nickname>\n\t\t\t<commodityInStockCount><![CDATA[]]></commodityInStockCount>\n\t\t\t<appId><![CDATA[]]></appId>\n\t\t\t<path><![CDATA[]]></path>\n\t\t\t<appUsername><![CDATA[]]></appUsername>\n\t\t\t<query><![CDATA[]]></query>\n\t\t\t<liteAppId><![CDATA[]]></liteAppId>\n\t\t\t<liteAppPath><![CDATA[]]></liteAppPath>\n\t\t\t<liteAppQuery><![CDATA[]]></liteAppQuery>\n\t\t\t<platformTagURL><![CDATA[]]></platformTagURL>\n\t\t\t<saleWording><![CDATA[]]></saleWording>\n\t\t\t<lastGMsgID><![CDATA[]]></lastGMsgID>\n\t\t\t<profileTypeWording><![CDATA[]]></profileTypeWording>\n\t\t\t<saleWordingExtra><![CDATA[]]></saleWordingExtra>\n\t\t\t<isWxShop><![CDATA[]]></isWxShop>\n\t\t\t<platformIconUrl><![CDATA[]]></platformIconUrl>\n\t\t\t<brandIconUrl><![CDATA[]]></brandIconUrl>\n\t\t\t<description><![CDATA[]]></description>\n\t\t\t<backgroundUrl><![CDATA[]]></backgroundUrl>\n\t\t\t<darkModePlatformIconUrl><![CDATA[]]></darkModePlatformIconUrl>\n\t\t\t<rIconUrl><![CDATA[]]></rIconUrl>\n\t\t\t<rIconUrlDarkMode><![CDATA[]]></rIconUrlDarkMode>\n\t\t\t<rWords><![CDATA[]]></rWords>\n\t\t\t<topShopIconUrl><![CDATA[]]></topShopIconUrl>\n\t\t\t<topShopIconUrlDarkMode><![CDATA[]]></topShopIconUrlDarkMode>\n\t\t\t<simplifyTopShopIconUrl><![CDATA[]]></simplifyTopShopIconUrl>\n\t\t\t<simplifyTopShopIconUrlDarkmode><![CDATA[]]></simplifyTopShopIconUrlDarkmode>\n\t\t\t<topShopIconWidth><![CDATA[0]]></topShopIconWidth>\n\t\t\t<topShopIconHeight><![CDATA[0]]></topShopIconHeight>\n\t\t\t<simplifyTopShopIconWidth><![CDATA[0]]></simplifyTopShopIconWidth>\n\t\t\t<simplifyTopShopIconHeight><![CDATA[0]]></simplifyTopShopIconHeight>\n\t\t\t<reputationInfo>\n\t\t\t\t<hasReputationInfo>0</hasReputationInfo>\n\t\t\t\t<reputationScore>0</reputationScore>\n\t\t\t\t<reputationWording />\n\t\t\t\t<reputationTextColor />\n\t\t\t\t<reputationLevelWording />\n\t\t\t\t<reputationBackgroundColor />\n\t\t\t</reputationInfo>\n\t\t\t<productImageURLList />\n\t\t</finderShopWindowShare>\n\t\t<findernamecard>\n\t\t\t<username />\n\t\t\t<avatar><![CDATA[]]></avatar>\n\t\t\t<nickname />\n\t\t\t<auth_job />\n\t\t\t<auth_icon>0</auth_icon>\n\t\t\t<auth_icon_url />\n\t\t\t<ecSource><![CDATA[]]></ecSource>\n\t\t\t<lastGMsgID><![CDATA[]]></lastGMsgID>\n\t\t</findernamecard>\n\t\t<finderGuarantee>\n\t\t\t<scene><![CDATA[0]]></scene>\n\t\t</finderGuarantee>\n\t\t<directshare>0</directshare>\n\t\t<gamecenter>\n\t\t\t<namecard>\n\t\t\t\t<iconUrl />\n\t\t\t\t<name />\n\t\t\t\t<desc />\n\t\t\t\t<tail />\n\t\t\t\t<jumpUrl />\n\t\t\t\t<liteappId />\n\t\t\t\t<liteappPath />\n\t\t\t\t<liteappQuery />\n\t\t\t\t<liteappMinVersion />\n\t\t\t</namecard>\n\t\t</gamecenter>\n\t\t<patMsg>\n\t\t\t<chatUser />\n\t\t\t<records>\n\t\t\t\t<recordNum>0</recordNum>\n\t\t\t</records>\n\t\t</patMsg>\n\t\t<secretmsg>\n\t\t\t<issecretmsg>0</issecretmsg>\n\t\t</secretmsg>\n\t\t<referfromscene>0</referfromscene>\n\t\t<gameshare>\n\t\t\t<liteappext>\n\t\t\t\t<liteappbizdata />\n\t\t\t\t<priority>0</priority>\n\t\t\t</liteappext>\n\t\t\t<appbrandext>\n\t\t\t\t<litegameinfo />\n\t\t\t\t<priority>-1</priority>\n\t\t\t</appbrandext>\n\t\t\t<gameshareid />\n\t\t\t<sharedata />\n\t\t\t<isvideo>0</isvideo>\n\t\t\t<duration>-1</duration>\n\t\t\t<isexposed>0</isexposed>\n\t\t\t<readtext />\n\t\t</gameshare>\n\t\t<tingChatRoomItem>\n\t\t\t<type>0</type>\n\t\t\t<categoryItem>null</categoryItem>\n\t\t\t<categoryId />\n\t\t\t<listenItem>null</listenItem>\n\t\t</tingChatRoomItem>\n\t\t<mpsharetrace>\n\t\t\t<hasfinderelement>0</hasfinderelement>\n\t\t\t<lastgmsgid />\n\t\t</mpsharetrace>\n\t\t<wxgamecard>\n\t\t\t<framesetname />\n\t\t\t<mbcarddata />\n\t\t\t<minpkgversion />\n\t\t\t<clientextinfo />\n\t\t\t<mbcardheight>0</mbcardheight>\n\t\t\t<isoldversion>0</isoldversion>\n\t\t</wxgamecard>\n\t\t<ecskfcard>\n\t\t\t<framesetname />\n\t\t\t<mbcarddata />\n\t\t\t<minupdateunixtimestamp>0</minupdateunixtimestamp>\n\t\t\t<needheader>false</needheader>\n\t\t\t<summary />\n\t\t</ecskfcard>\n\t\t<liteapp>\n\t\t\t<id>null</id>\n\t\t\t<path />\n\t\t\t<query />\n\t\t\t<istransparent>0</istransparent>\n\t\t\t<hideicon>0</hideicon>\n\t\t\t<forbidforward>0</forbidforward>\n\t\t</liteapp>\n\t\t<opensdk_share_is_modified>0</opensdk_share_is_modified>\n\t</appmsg>\n\t<fromusername>wxid_laurnst5xn0q22</fromusername>\n\t<scene>0</scene>\n\t<appinfo>\n\t\t<version>1</version>\n\t\t<appname></appname>\n\t</appinfo>\n\t<commenturl></commenturl>\n</msg>\n'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1753787737, 'MsgSource': '<msgsource>\n\t<alnode>\n\t\t<fr>4</fr>\n\t</alnode>\n\t<eggIncluded>1</eggIncluded>\n\t<sec_msg_node>\n\t\t<uuid>59a559b5d3c86ab1fe6c11c761a667c3_</uuid>\n\t\t<risk-file-flag />\n\t\t<risk-file-md5-list />\n\t</sec_msg_node>\n\t<silence>0</silence>\n\t<membercount>73</membercount>\n\t<signature>N0_V1_HDL2NLR7|v1_VFcCJ43R</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'PushContent': 'متسول暗 : 性骚扰事件', 'NewMsgId': 9044541417979140292, 'MsgSeq': 871409244}
2025-07-29 19:15:41 | DEBUG | 从群聊消息中提取发送者: wxid_laurnst5xn0q22
2025-07-29 19:15:41 | DEBUG | 使用已解析的XML处理引用消息
2025-07-29 19:15:41 | INFO | 收到引用消息: 消息ID:1951796948 来自:48097389945@chatroom 发送人:wxid_laurnst5xn0q22 内容:性骚扰事件 引用类型:1
2025-07-29 19:15:41 | INFO | [DouBaoImageToImage] 收到引用消息: 性骚扰事件
2025-07-29 19:15:41 | INFO | [TimerTask] 收到引用消息调试信息:
2025-07-29 19:15:41 | INFO |   - 消息内容: 性骚扰事件
2025-07-29 19:15:41 | INFO |   - 群组ID: 48097389945@chatroom
2025-07-29 19:15:41 | INFO |   - 发送人: wxid_laurnst5xn0q22
2025-07-29 19:15:41 | INFO |   - 引用信息: {'MsgType': 1, 'Content': '\n武大图书馆是啥', 'Msgid': '1447805752468222475', 'NewMsgId': '1447805752468222475', 'ToWxid': 'wxid_4usgcju5ey9q29', 'FromWxid': '48097389945@chatroom', 'Nickname': '锦岚', 'MsgSource': '<msgsource>\n\t<pua>1</pua>\n\t<eggIncluded>1</eggIncluded>\n\t<silence>1</silence>\n\t<membercount>73</membercount>\n\t<signature>N0_V1_nbt+xeTS|v1_E+f9OYCS</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'Createtime': '1753787719', 'SenderWxid': 'wxid_laurnst5xn0q22'}
2025-07-29 19:15:41 | INFO |   - 引用消息ID: 
2025-07-29 19:15:41 | INFO |   - 引用消息类型: 
2025-07-29 19:15:41 | INFO |   - 引用消息内容: 
武大图书馆是啥
2025-07-29 19:15:41 | INFO |   - 引用消息发送人: wxid_laurnst5xn0q22
2025-07-29 19:15:41 | DEBUG | 收到消息: {'MsgId': 347018233, 'FromUserName': {'string': '47325400669@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 1, 'Content': {'string': 'wxid_16fck6g1b7ea22:\n你没有权限使用此命令'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1753787737, 'MsgSource': '<msgsource>\n\t<silence>0</silence>\n\t<membercount>222</membercount>\n\t<signature>N0_V1_gX4QOp+R|v1_Pbjyz9m9</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'PushContent': '阿天米德 : 你没有权限使用此命令', 'NewMsgId': 2957513888160655599, 'MsgSeq': 871409245}
2025-07-29 19:15:41 | INFO | 收到文本消息: 消息ID:347018233 来自:47325400669@chatroom 发送人:wxid_16fck6g1b7ea22 @:[] 内容:你没有权限使用此命令
2025-07-29 19:15:41 | DEBUG | 处理消息内容: '你没有权限使用此命令'
2025-07-29 19:15:41 | DEBUG | 消息内容 '你没有权限使用此命令' 不匹配任何命令，忽略
2025-07-29 19:15:44 | DEBUG | 收到消息: {'MsgId': 1982219971, 'FromUserName': {'string': '48097389945@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 1, 'Content': {'string': 'wxid_laurnst5xn0q22:\n女方称男方对她打'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1753787752, 'MsgSource': '<msgsource>\n\t<pua>1</pua>\n\t<eggIncluded>1</eggIncluded>\n\t<silence>0</silence>\n\t<membercount>73</membercount>\n\t<signature>N0_V1_IIX/Mtsg|v1_hOrNlwBp</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'PushContent': 'متسول暗 : 女方称男方对她打', 'NewMsgId': 787141201291050953, 'MsgSeq': 871409248}
2025-07-29 19:15:44 | INFO | 收到文本消息: 消息ID:1982219971 来自:48097389945@chatroom 发送人:wxid_laurnst5xn0q22 @:[] 内容:女方称男方对她打
2025-07-29 19:15:45 | DEBUG | 处理消息内容: '女方称男方对她打'
2025-07-29 19:15:45 | DEBUG | 消息内容 '女方称男方对她打' 不匹配任何命令，忽略
2025-07-29 19:15:48 | DEBUG | 收到消息: {'MsgId': 2104005900, 'FromUserName': {'string': '48097389945@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 49, 'Content': {'string': 'wxid_w0rik7wwsvcy22:\n<?xml version="1.0"?>\n<msg>\n\t<appmsg appid="" sdkver="0">\n\t\t<title>豆包 给图片美腿加上渔网裤</title>\n\t\t<type>57</type>\n\t\t<appattach>\n\t\t\t<cdnthumbaeskey />\n\t\t\t<aeskey />\n\t\t</appattach>\n\t\t<refermsg>\n\t\t\t<type>3</type>\n\t\t\t<svrid>503301028514496684</svrid>\n\t\t\t<fromusr>wxid_w0rik7wwsvcy22</fromusr>\n\t\t\t<chatusr />\n\t\t\t<displayname>一罐小可乐</displayname>\n\t\t\t<content>&lt;msg&gt;&lt;img hdlength="0" length="92633" aeskey="bbaaee49eb503e7ff8e6ad98c4bc6b3a" encryver="1" md5="e6768d13f064ccd2cbbbd950b06de0dc" originsourcemd5="35ecf90ad0ef854eede0ff95257993c4" filekey="48097389945@chatroom_160514_1753787700" uploadcontinuecount="0" imgsourceurl="" hevc_mid_size="92633" cdnbigimgurl="" cdnmidimgurl="3057020100044b3049020100020473d808fb02032f53a3020448752d7002046888ac8a042439613766366238362d376333342d346432372d383933652d666262633736393037636538020405290a020201000405004c4cd300" cdnthumburl="3057020100044b3049020100020473d808fb02032f53a3020448752d7002046888ac8a042439613766366238362d376333342d346432372d383933652d666262633736393037636538020405290a020201000405004c4cd300" cdnthumblength="7138" cdnthumbwidth="136" cdnthumbheight="180" cdnthumbaeskey="bbaaee49eb503e7ff8e6ad98c4bc6b3a"&gt;&lt;secHashInfoBase64&gt;eyJwaGFzaCI6Ijc0MjViMDE1MTAwMDAwMDAiLCJwZHFoYXNoIjoiY2ZjNjU0ZTYxMDkyMzQ2MDc5MjZjNjY2MzNiMmVjOGZjYjYzOGU4YjUyM2IzMWVjYzU2MzJkOWJiNjc4Y2IyNSJ9&lt;/secHashInfoBase64&gt;&lt;/img&gt;&lt;appinfo&gt;&lt;appid&gt;&lt;/appid&gt;&lt;appname&gt;&lt;/appname&gt;&lt;version&gt;0&lt;/version&gt;&lt;isforceupdate&gt;1&lt;/isforceupdate&gt;&lt;mediatagname&gt;&lt;/mediatagname&gt;&lt;messageext&gt;&lt;/messageext&gt;&lt;messageaction&gt;&lt;/messageaction&gt;&lt;/appinfo&gt;&lt;/msg&gt;</content>\n\t\t\t<msgsource>&lt;msgsource&gt;&lt;svr_create_time&gt;1753787701&lt;/svr_create_time&gt;&lt;bizflag&gt;0&lt;/bizflag&gt;&lt;sec_msg_node&gt;&lt;uuid&gt;&lt;![CDATA[20c8c5f06aee83b199de1181d5c45240_]]&gt;&lt;/uuid&gt;&lt;/sec_msg_node&gt;&lt;tmp_node&gt;&lt;publisher-id&gt;&lt;![CDATA[msg_3497823020907248142|wxid_90s9h2i2gcb622|53435110965@chatroom]]&gt;&lt;/publisher-id&gt;&lt;/tmp_node&gt;&lt;alnode&gt;&lt;fr&gt;4&lt;/fr&gt;&lt;/alnode&gt;&lt;/msgsource&gt;</msgsource>\n\t\t\t<createtime>1753787701</createtime>\n\t\t</refermsg>\n\t</appmsg>\n\t<fromusername>wxid_w0rik7wwsvcy22</fromusername>\n\t<scene>0</scene>\n\t<appinfo>\n\t\t<version>1</version>\n\t\t<appname></appname>\n\t</appinfo>\n\t<commenturl></commenturl>\n</msg>\n'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1753787757, 'MsgSource': '<msgsource>\n\t<bizflag>0</bizflag>\n\t<pua>1</pua>\n\t<eggIncluded>1</eggIncluded>\n\t<sec_msg_node>\n\t\t<uuid>7229b52e0af2fd45a16130693fc26289_</uuid>\n\t\t<risk-file-flag />\n\t\t<risk-file-md5-list />\n\t</sec_msg_node>\n\t<silence>0</silence>\n\t<membercount>73</membercount>\n\t<signature>N0_V1_2QrrYIjC|v1_yBb4AscV</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'PushContent': '一罐小可乐 : 豆包 给图片美腿加上渔网裤', 'NewMsgId': 4741399411092861340, 'MsgSeq': 871409249}
2025-07-29 19:15:48 | DEBUG | 从群聊消息中提取发送者: wxid_w0rik7wwsvcy22
2025-07-29 19:15:48 | DEBUG | 使用已解析的XML处理引用消息
2025-07-29 19:15:48 | INFO | 收到引用消息: 消息ID:2104005900 来自:48097389945@chatroom 发送人:wxid_w0rik7wwsvcy22 内容:豆包 给图片美腿加上渔网裤 引用类型:3
2025-07-29 19:15:48 | INFO | [DouBaoImageToImage] 收到引用消息: 豆包 给图片美腿加上渔网裤
2025-07-29 19:15:48 | INFO | [DouBaoImageToImage] 引用图片图生图 - 提示词: '给图片美腿加上渔网裤，比例「2:3」', 比例: 832x1248, 风格: None
2025-07-29 19:15:48 | INFO | [DouBaoImageToImage] 被引用消息类型: 3
2025-07-29 19:15:49 | INFO | [DouBaoImageToImage] 成功下载引用的图片并保存到: temp\doubao_image_to_image\quoted_image_1753787749.jpg
2025-07-29 19:15:49 | INFO | [DouBaoImageToImage] 开始豆包AI处理流程(无通知)，用户: wxid_w0rik7wwsvcy22, 提示词: 给图片美腿加上渔网裤，比例「2:3」, 比例: 832x1248
2025-07-29 19:15:49 | INFO | [DouBaoImageToImage] 步骤1: 验证图片文件...
2025-07-29 19:15:49 | INFO | [DouBaoImageToImage] 图片验证成功，大小: 542.3KB
2025-07-29 19:15:49 | INFO | [DouBaoImageToImage] 步骤2: 调用豆包AI接口...
2025-07-29 19:15:49 | INFO | [DouBaoImageToImage] 开始豆包AI图生图处理，提示词: 给图片美腿加上渔网裤，比例「2:3」
2025-07-29 19:15:49 | INFO | [DouBaoImageToImage] 使用比例: 2:3 (832x1248)
2025-07-29 19:15:53 | ERROR | [DouBaoImageToImage] 豆包AI处理失败
2025-07-29 19:15:53 | WARNING | [DouBaoImageToImage] 豆包AI处理失败
2025-07-29 19:15:53 | INFO | 发送文字消息: 对方wxid:48097389945@chatroom at:['wxid_w0rik7wwsvcy22'] 内容:@一罐小可乐 豆包AI处理失败，等会再试试吧
2025-07-29 19:15:53 | INFO | [TimerTask] 收到引用消息调试信息:
2025-07-29 19:15:53 | INFO |   - 消息内容: 豆包 给图片美腿加上渔网裤
2025-07-29 19:15:53 | INFO |   - 群组ID: 48097389945@chatroom
2025-07-29 19:15:53 | INFO |   - 发送人: wxid_w0rik7wwsvcy22
2025-07-29 19:15:53 | INFO |   - 引用信息: {'MsgType': 3, 'Content': '<?xml version="1.0"?>\n<msg>\n\t<appmsg appid="" sdkver="0">\n\t\t<title>豆包 给图片美腿加上渔网裤</title>\n\t\t<type>57</type>\n\t\t<appattach>\n\t\t\t<cdnthumbaeskey />\n\t\t\t<aeskey />\n\t\t</appattach>\n\t\t<refermsg>\n\t\t\t<type>3</type>\n\t\t\t<svrid>503301028514496684</svrid>\n\t\t\t<fromusr>wxid_w0rik7wwsvcy22</fromusr>\n\t\t\t<chatusr />\n\t\t\t<displayname>一罐小可乐</displayname>\n\t\t\t<content>&lt;msg&gt;&lt;img hdlength="0" length="92633" aeskey="bbaaee49eb503e7ff8e6ad98c4bc6b3a" encryver="1" md5="e6768d13f064ccd2cbbbd950b06de0dc" originsourcemd5="35ecf90ad0ef854eede0ff95257993c4" filekey="48097389945@chatroom_160514_1753787700" uploadcontinuecount="0" imgsourceurl="" hevc_mid_size="92633" cdnbigimgurl="" cdnmidimgurl="3057020100044b3049020100020473d808fb02032f53a3020448752d7002046888ac8a042439613766366238362d376333342d346432372d383933652d666262633736393037636538020405290a020201000405004c4cd300" cdnthumburl="3057020100044b3049020100020473d808fb02032f53a3020448752d7002046888ac8a042439613766366238362d376333342d346432372d383933652d666262633736393037636538020405290a020201000405004c4cd300" cdnthumblength="7138" cdnthumbwidth="136" cdnthumbheight="180" cdnthumbaeskey="bbaaee49eb503e7ff8e6ad98c4bc6b3a"&gt;&lt;secHashInfoBase64&gt;eyJwaGFzaCI6Ijc0MjViMDE1MTAwMDAwMDAiLCJwZHFoYXNoIjoiY2ZjNjU0ZTYxMDkyMzQ2MDc5MjZjNjY2MzNiMmVjOGZjYjYzOGU4YjUyM2IzMWVjYzU2MzJkOWJiNjc4Y2IyNSJ9&lt;/secHashInfoBase64&gt;&lt;/img&gt;&lt;appinfo&gt;&lt;appid&gt;&lt;/appid&gt;&lt;appname&gt;&lt;/appname&gt;&lt;version&gt;0&lt;/version&gt;&lt;isforceupdate&gt;1&lt;/isforceupdate&gt;&lt;mediatagname&gt;&lt;/mediatagname&gt;&lt;messageext&gt;&lt;/messageext&gt;&lt;messageaction&gt;&lt;/messageaction&gt;&lt;/appinfo&gt;&lt;/msg&gt;</content>\n\t\t\t<msgsource>&lt;msgsource&gt;&lt;svr_create_time&gt;1753787701&lt;/svr_create_time&gt;&lt;bizflag&gt;0&lt;/bizflag&gt;&lt;sec_msg_node&gt;&lt;uuid&gt;&lt;![CDATA[20c8c5f06aee83b199de1181d5c45240_]]&gt;&lt;/uuid&gt;&lt;/sec_msg_node&gt;&lt;tmp_node&gt;&lt;publisher-id&gt;&lt;![CDATA[msg_3497823020907248142|wxid_90s9h2i2gcb622|53435110965@chatroom]]&gt;&lt;/publisher-id&gt;&lt;/tmp_node&gt;&lt;alnode&gt;&lt;fr&gt;4&lt;/fr&gt;&lt;/alnode&gt;&lt;/msgsource&gt;</msgsource>\n\t\t\t<createtime>1753787701</createtime>\n\t\t</refermsg>\n\t</appmsg>\n\t<fromusername>wxid_w0rik7wwsvcy22</fromusername>\n\t<scene>0</scene>\n\t<appinfo>\n\t\t<version>1</version>\n\t\t<appname></appname>\n\t</appinfo>\n\t<commenturl></commenturl>\n</msg>\n', 'Msgid': '503301028514496684', 'NewMsgId': '503301028514496684', 'ToWxid': 'wxid_4usgcju5ey9q29', 'FromWxid': '48097389945@chatroom', 'Nickname': '一罐小可乐', 'MsgSource': '<msgsource><svr_create_time>1753787701</svr_create_time><bizflag>0</bizflag><sec_msg_node><uuid><![CDATA[20c8c5f06aee83b199de1181d5c45240_]]></uuid></sec_msg_node><tmp_node><publisher-id><![CDATA[msg_3497823020907248142|wxid_90s9h2i2gcb622|53435110965@chatroom]]></publisher-id></tmp_node><alnode><fr>4</fr></alnode></msgsource>', 'Createtime': '1753787701', 'SenderWxid': 'wxid_w0rik7wwsvcy22'}
2025-07-29 19:15:53 | INFO |   - 引用消息ID: 
2025-07-29 19:15:53 | INFO |   - 引用消息类型: 
2025-07-29 19:15:53 | INFO |   - 引用消息内容: <?xml version="1.0"?>
<msg>
	<appmsg appid="" sdkver="0">
		<title>豆包 给图片美腿加上渔网裤</title>
		<type>57</type>
		<appattach>
			<cdnthumbaeskey />
			<aeskey />
		</appattach>
		<refermsg>
			<type>3</type>
			<svrid>503301028514496684</svrid>
			<fromusr>wxid_w0rik7wwsvcy22</fromusr>
			<chatusr />
			<displayname>一罐小可乐</displayname>
			<content>&lt;msg&gt;&lt;img hdlength="0" length="92633" aeskey="bbaaee49eb503e7ff8e6ad98c4bc6b3a" encryver="1" md5="e6768d13f064ccd2cbbbd950b06de0dc" originsourcemd5="35ecf90ad0ef854eede0ff95257993c4" filekey="48097389945@chatroom_160514_1753787700" uploadcontinuecount="0" imgsourceurl="" hevc_mid_size="92633" cdnbigimgurl="" cdnmidimgurl="3057020100044b3049020100020473d808fb02032f53a3020448752d7002046888ac8a042439613766366238362d376333342d346432372d383933652d666262633736393037636538020405290a020201000405004c4cd300" cdnthumburl="3057020100044b3049020100020473d808fb02032f53a3020448752d7002046888ac8a042439613766366238362d376333342d346432372d383933652d666262633736393037636538020405290a020201000405004c4cd300" cdnthumblength="7138" cdnthumbwidth="136" cdnthumbheight="180" cdnthumbaeskey="bbaaee49eb503e7ff8e6ad98c4bc6b3a"&gt;&lt;secHashInfoBase64&gt;eyJwaGFzaCI6Ijc0MjViMDE1MTAwMDAwMDAiLCJwZHFoYXNoIjoiY2ZjNjU0ZTYxMDkyMzQ2MDc5MjZjNjY2MzNiMmVjOGZjYjYzOGU4YjUyM2IzMWVjYzU2MzJkOWJiNjc4Y2IyNSJ9&lt;/secHashInfoBase64&gt;&lt;/img&gt;&lt;appinfo&gt;&lt;appid&gt;&lt;/appid&gt;&lt;appname&gt;&lt;/appname&gt;&lt;version&gt;0&lt;/version&gt;&lt;isforceupdate&gt;1&lt;/isforceupdate&gt;&lt;mediatagname&gt;&lt;/mediatagname&gt;&lt;messageext&gt;&lt;/messageext&gt;&lt;messageaction&gt;&lt;/messageaction&gt;&lt;/appinfo&gt;&lt;/msg&gt;</content>
			<msgsource>&lt;msgsource&gt;&lt;svr_create_time&gt;1753787701&lt;/svr_create_time&gt;&lt;bizflag&gt;0&lt;/bizflag&gt;&lt;sec_msg_node&gt;&lt;uuid&gt;&lt;![CDATA[20c8c5f06aee83b199de1181d5c45240_]]&gt;&lt;/uuid&gt;&lt;/sec_msg_node&gt;&lt;tmp_node&gt;&lt;publisher-id&gt;&lt;![CDATA[msg_3497823020907248142|wxid_90s9h2i2gcb622|53435110965@chatroom]]&gt;&lt;/publisher-id&gt;&lt;/tmp_node&gt;&lt;alnode&gt;&lt;fr&gt;4&lt;/fr&gt;&lt;/alnode&gt;&lt;/msgsource&gt;</msgsource>
			<createtime>1753787701</createtime>
		</refermsg>
	</appmsg>
	<fromusername>wxid_w0rik7wwsvcy22</fromusername>
	<scene>0</scene>
	<appinfo>
		<version>1</version>
		<appname></appname>
	</appinfo>
	<commenturl></commenturl>
</msg>

2025-07-29 19:15:53 | INFO |   - 引用消息发送人: wxid_w0rik7wwsvcy22
2025-07-29 19:15:54 | DEBUG | 收到消息: {'MsgId': 1378397307, 'FromUserName': {'string': '48097389945@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 1, 'Content': {'string': 'wxid_laurnst5xn0q22:\n男方说只是瘙痒'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1753787759, 'MsgSource': '<msgsource>\n\t<pua>1</pua>\n\t<eggIncluded>1</eggIncluded>\n\t<silence>0</silence>\n\t<membercount>73</membercount>\n\t<signature>N0_V1_Jl4tMSpF|v1_5FEdIpxo</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'PushContent': 'متسول暗 : 男方说只是瘙痒', 'NewMsgId': 5111353477035033808, 'MsgSeq': 871409250}
2025-07-29 19:15:54 | INFO | 收到文本消息: 消息ID:1378397307 来自:48097389945@chatroom 发送人:wxid_laurnst5xn0q22 @:[] 内容:男方说只是瘙痒
2025-07-29 19:15:54 | DEBUG | 处理消息内容: '男方说只是瘙痒'
2025-07-29 19:15:54 | DEBUG | 消息内容 '男方说只是瘙痒' 不匹配任何命令，忽略
2025-07-29 19:15:57 | DEBUG | 收到消息: {'MsgId': 547853747, 'FromUserName': {'string': '48097389945@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 1, 'Content': {'string': 'wxid_laurnst5xn0q22:\n就看证据喽'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1753787766, 'MsgSource': '<msgsource>\n\t<pua>1</pua>\n\t<eggIncluded>1</eggIncluded>\n\t<silence>0</silence>\n\t<membercount>73</membercount>\n\t<signature>N0_V1_JZWUq/Cs|v1_ZzvqbsO/</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'PushContent': 'متسول暗 : 就看证据喽', 'NewMsgId': 5466455726875734365, 'MsgSeq': 871409253}
2025-07-29 19:15:57 | INFO | 收到文本消息: 消息ID:547853747 来自:48097389945@chatroom 发送人:wxid_laurnst5xn0q22 @:[] 内容:就看证据喽
2025-07-29 19:15:58 | DEBUG | 处理消息内容: '就看证据喽'
2025-07-29 19:15:58 | DEBUG | 消息内容 '就看证据喽' 不匹配任何命令，忽略
2025-07-29 19:16:03 | DEBUG | [TempFileManager] 已清理文件: temp\doubao_image_to_image\quoted_image_1753787749.jpg
2025-07-29 19:16:06 | DEBUG | 收到消息: {'MsgId': 1530639193, 'FromUserName': {'string': '48097389945@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 1, 'Content': {'string': 'wxid_w0rik7wwsvcy22:\n垃圾豆包'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1753787776, 'MsgSource': '<msgsource>\n\t<bizflag>0</bizflag>\n\t<pua>1</pua>\n\t<eggIncluded>1</eggIncluded>\n\t<silence>0</silence>\n\t<membercount>73</membercount>\n\t<signature>N0_V1_eNg1oC9y|v1_KVv1b/g/</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'PushContent': '一罐小可乐 : 垃圾豆包', 'NewMsgId': 733722934799563368, 'MsgSeq': 871409254}
2025-07-29 19:16:06 | INFO | 收到文本消息: 消息ID:1530639193 来自:48097389945@chatroom 发送人:wxid_w0rik7wwsvcy22 @:[] 内容:垃圾豆包
2025-07-29 19:16:07 | DEBUG | 处理消息内容: '垃圾豆包'
2025-07-29 19:16:07 | DEBUG | 消息内容 '垃圾豆包' 不匹配任何命令，忽略
2025-07-29 19:16:10 | DEBUG | 收到消息: {'MsgId': 1432873440, 'FromUserName': {'string': '48097389945@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 47, 'Content': {'string': 'wxid_w0rik7wwsvcy22:\n<msg><emoji fromusername = "wxid_w0rik7wwsvcy22" tousername = "48097389945@chatroom" type="2" idbuffer="media:0_0" md5="8eabeb6fb6145d5fd16c6bf0b767d2d0" len = "41476" productid="" androidmd5="8eabeb6fb6145d5fd16c6bf0b767d2d0" androidlen="41476" s60v3md5 = "8eabeb6fb6145d5fd16c6bf0b767d2d0" s60v3len="41476" s60v5md5 = "8eabeb6fb6145d5fd16c6bf0b767d2d0" s60v5len="41476" cdnurl = "http://vweixinf.tc.qq.com/110/20401/stodownload?m=8eabeb6fb6145d5fd16c6bf0b767d2d0&amp;filekey=30440201010430302e02016e0402534804203865616265623666623631343564356664313663366266306237363764326430020300a204040d00000004627466730000000132&amp;hy=SH&amp;storeid=265b2a7e90009d35f5acd2d3b0000006e01004fb1534822d4b1b1567563d6c&amp;ef=1&amp;bizid=1022" designerid = "" thumburl = "" encrypturl = "http://vweixinf.tc.qq.com/110/20402/stodownload?m=e9e890eb0cf341cc713b1ecb4a5e234f&amp;filekey=30440201010430302e02016e0402534804206539653839306562306366333431636337313362316563623461356532333466020300a210040d00000004627466730000000132&amp;hy=SH&amp;storeid=265b2a7e9000a8cda5acd2d3b0000006e02004fb2534822d4b1b1567563d73&amp;ef=2&amp;bizid=1022" aeskey= "115584bc243545e2b108c8cd2aa5fe95" externurl = "http://vweixinf.tc.qq.com/110/20403/stodownload?m=a9a74eea573c56f0b4f0cb702f5babc2&amp;filekey=3043020101042f302d02016e040253480420613961373465656135373363353666306234663063623730326635626162633202021e50040d00000004627466730000000132&amp;hy=SH&amp;storeid=265b2a7e9000b112b5acd2d3b0000006e03004fb3534822d4b1b1567563d7a&amp;ef=3&amp;bizid=1022" externmd5 = "58d089571654d083808da6e62cff1a62" width= "559" height= "560" tpurl= "" tpauthkey= "" attachedtext= "" attachedtextcolor= "" lensid= "" emojiattr= "" linkid= "" desc= "" ></emoji> <gameext type="0" content="0" ></gameext> <extcommoninfo></extcommoninfo></msg>'}, 'Status': 3, 'ImgStatus': 2, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1753787779, 'MsgSource': '<msgsource>\n\t<silence>0</silence>\n\t<membercount>73</membercount>\n\t<signature>N0_V1_CJixaNhk|v1_O/pMPmHs</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'PushContent': '一罐小可乐在群聊中发了一个表情', 'NewMsgId': 1967163338300641352, 'MsgSeq': 871409255}
2025-07-29 19:16:10 | INFO | 收到表情消息: 消息ID:1432873440 来自:48097389945@chatroom 发送人:wxid_w0rik7wwsvcy22 MD5:8eabeb6fb6145d5fd16c6bf0b767d2d0 大小:41476
2025-07-29 19:16:11 | DEBUG | [DoubaoImageRecognition] 缓存表情消息: 1967163338300641352
2025-07-29 19:16:27 | DEBUG | 收到消息: {'MsgId': 1519088831, 'FromUserName': {'string': '47325400669@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 1, 'Content': {'string': 'seraph333:\n萝莉'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1753787797, 'MsgSource': '<msgsource>\n\t<alnode>\n\t\t<fr>7</fr>\n\t</alnode>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n\t<silence>0</silence>\n\t<membercount>222</membercount>\n\t<signature>N0_V1_vb160Kz/|v1_QAH5o5Z/</signature>\n</msgsource>\n', 'PushContent': '酱爆\ue147 : 萝莉', 'NewMsgId': 5755417865584547467, 'MsgSeq': 871409256}
2025-07-29 19:16:27 | INFO | 收到文本消息: 消息ID:1519088831 来自:47325400669@chatroom 发送人:seraph333 @:[] 内容:萝莉
2025-07-29 19:16:27 | DEBUG | 处理消息内容: '萝莉'
2025-07-29 19:16:27 | DEBUG | 消息内容 '萝莉' 不匹配任何命令，忽略
2025-07-29 19:16:30 | DEBUG | 收到消息: {'MsgId': 1650990445, 'FromUserName': {'string': '47325400669@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 1, 'Content': {'string': 'yihuacheng:\n以为是有人复制，结果全是机器人'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1753787798, 'MsgSource': '<msgsource>\n\t<sec_msg_node>\n\t\t<alnode>\n\t\t\t<fr>1</fr>\n\t\t</alnode>\n\t</sec_msg_node>\n\t<pua>1</pua>\n\t<silence>0</silence>\n\t<membercount>222</membercount>\n\t<signature>N0_V1_Paezrw7Z|v1_gh7Bsv5T</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'PushContent': '回忆 : 以为是有人复制，结果全是机器人', 'NewMsgId': 5937382650098302905, 'MsgSeq': 871409257}
2025-07-29 19:16:30 | INFO | 收到文本消息: 消息ID:1650990445 来自:47325400669@chatroom 发送人:yihuacheng @:[] 内容:以为是有人复制，结果全是机器人
2025-07-29 19:16:30 | DEBUG | 处理消息内容: '以为是有人复制，结果全是机器人'
2025-07-29 19:16:30 | DEBUG | 消息内容 '以为是有人复制，结果全是机器人' 不匹配任何命令，忽略
2025-07-29 19:16:50 | DEBUG | 收到消息: {'MsgId': 333702163, 'FromUserName': {'string': '47325400669@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 1, 'Content': {'string': 'wxid_ubbh6q832tcs21:\n啊'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1753787820, 'MsgSource': '<msgsource>\n\t<pua>1</pua>\n\t<eggIncluded>1</eggIncluded>\n\t<silence>0</silence>\n\t<membercount>222</membercount>\n\t<signature>N0_V1_yjj3uPFK|v1_A6f5fdjp</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'PushContent': '郭 : 啊', 'NewMsgId': 6779420111404101266, 'MsgSeq': 871409258}
2025-07-29 19:16:50 | INFO | 收到文本消息: 消息ID:333702163 来自:47325400669@chatroom 发送人:wxid_ubbh6q832tcs21 @:[] 内容:啊
2025-07-29 19:16:51 | INFO | 发送表情消息: 对方wxid:47325400669@chatroom md5:5a1e188c79cc0c66e11cf127f264b68f 总长度:9992069
2025-07-29 19:16:51 | DEBUG | 处理消息内容: '啊'
2025-07-29 19:16:51 | DEBUG | 消息内容 '啊' 不匹配任何命令，忽略
2025-07-29 19:16:53 | DEBUG | 收到消息: {'MsgId': 1070108637, 'FromUserName': {'string': '47325400669@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 1, 'Content': {'string': 'seraph333:\n玉足'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1753787823, 'MsgSource': '<msgsource>\n\t<alnode>\n\t\t<fr>7</fr>\n\t</alnode>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n\t<silence>0</silence>\n\t<membercount>222</membercount>\n\t<signature>N0_V1_G2fWwmd4|v1_zU7E9QBL</signature>\n</msgsource>\n', 'PushContent': '酱爆\ue147 : 玉足', 'NewMsgId': 822439056189683478, 'MsgSeq': 871409261}
2025-07-29 19:16:53 | INFO | 收到文本消息: 消息ID:1070108637 来自:47325400669@chatroom 发送人:seraph333 @:[] 内容:玉足
2025-07-29 19:16:53 | DEBUG | 处理消息内容: '玉足'
2025-07-29 19:16:53 | DEBUG | 消息内容 '玉足' 不匹配任何命令，忽略
2025-07-29 19:17:03 | DEBUG | 收到消息: {'MsgId': 1813901467, 'FromUserName': {'string': '47325400669@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 1, 'Content': {'string': 'seraph333:\n黑丝'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1753787833, 'MsgSource': '<msgsource>\n\t<alnode>\n\t\t<fr>1</fr>\n\t</alnode>\n\t<silence>0</silence>\n\t<membercount>222</membercount>\n\t<signature>N0_V1_4ErBGyG+|v1_Fq9SylAn</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'PushContent': '酱爆\ue147 : 黑丝', 'NewMsgId': 8428614527524512725, 'MsgSeq': 871409262}
2025-07-29 19:17:03 | INFO | 收到文本消息: 消息ID:1813901467 来自:47325400669@chatroom 发送人:seraph333 @:[] 内容:黑丝
2025-07-29 19:17:03 | DEBUG | 处理消息内容: '黑丝'
2025-07-29 19:17:03 | DEBUG | 消息内容 '黑丝' 不匹配任何命令，忽略
2025-07-29 19:17:11 | DEBUG | 收到消息: {'MsgId': 1092563584, 'FromUserName': {'string': '48097389945@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 1, 'Content': {'string': 'wxid_w0rik7wwsvcy22:\n可灵'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1753787841, 'MsgSource': '<msgsource>\n\t<bizflag>0</bizflag>\n\t<pua>1</pua>\n\t<eggIncluded>1</eggIncluded>\n\t<silence>0</silence>\n\t<membercount>73</membercount>\n\t<signature>N0_V1_oVjCx83y|v1_mzaJKV/0</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'PushContent': '一罐小可乐 : 可灵', 'NewMsgId': 6220924590323888535, 'MsgSeq': 871409263}
2025-07-29 19:17:11 | INFO | 收到文本消息: 消息ID:1092563584 来自:48097389945@chatroom 发送人:wxid_w0rik7wwsvcy22 @:[] 内容:可灵
2025-07-29 19:17:12 | INFO | 发送文字消息: 对方wxid:48097389945@chatroom at:['wxid_w0rik7wwsvcy22'] 内容:@一罐小可乐 🎨 可灵AI绘图助手 🎨

使用方法:
  可灵 [提示词]

示例:
  可灵 穿着汉服的少女在樱花树下

参数说明:
  提示词 - 描述你想要生成的图像内容

支持的比例:
  默认为9:16竖图

2025-07-29 19:17:12 | DEBUG | 处理消息内容: '可灵'
2025-07-29 19:17:12 | DEBUG | 消息内容 '可灵' 不匹配任何命令，忽略
2025-07-29 19:17:17 | DEBUG | 收到消息: {'MsgId': 1207979937, 'FromUserName': {'string': '47325400669@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 3, 'Content': {'string': 'wxid_fh84okl6f5wp22:\n<?xml version="1.0"?>\n<msg>\n\t<img aeskey="7a71666d6c626667796d767676727a68" encryver="0" cdnthumbaeskey="7a71666d6c626667796d767676727a68" cdnthumburl="3057020100044b30490201000204d6e1bab602033d14ba0204a926949d020468883692042431646431383931332d303766622d343533642d613534612d3739613539313131393766340204052828010201000405004c511e0054381738" cdnthumblength="3363" cdnthumbheight="66" cdnthumbwidth="100" cdnmidheight="0" cdnmidwidth="0" cdnhdheight="0" cdnhdwidth="0" cdnmidimgurl="3057020100044b30490201000204d6e1bab602033d14ba0204a926949d020468883692042431646431383931332d303766622d343533642d613534612d3739613539313131393766340204052828010201000405004c511e0054381738" length="25235" cdnbigimgurl="3057020100044b30490201000204d6e1bab602033d14ba0204a926949d020468883692042431646431383931332d303766622d343533642d613534612d3739613539313131393766340204052828010201000405004c511e0054381738" hdlength="59573" md5="d403db7e0629c7767c66c2ae38c8683f">\n\t\t<secHashInfoBase64 />\n\t\t<live>\n\t\t\t<duration>0</duration>\n\t\t\t<size>0</size>\n\t\t\t<md5 />\n\t\t\t<fileid />\n\t\t\t<hdsize>0</hdsize>\n\t\t\t<hdmd5 />\n\t\t\t<hdfileid />\n\t\t\t<stillimagetimems>0</stillimagetimems>\n\t\t</live>\n\t</img>\n\t<platform_signature />\n\t<imgdatahash />\n\t<ImgSourceInfo>\n\t\t<ImgSourceUrl />\n\t\t<BizType>0</BizType>\n\t</ImgSourceInfo>\n</msg>\n'}, 'Status': 3, 'ImgStatus': 2, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1753787846, 'MsgSource': '<msgsource>\n\t<sec_msg_node>\n\t\t<uuid>cf7b75c3dc45f99331f1e191de7fe692_</uuid>\n\t\t<risk-file-flag />\n\t\t<risk-file-md5-list />\n\t</sec_msg_node>\n\t<silence>0</silence>\n\t<membercount>222</membercount>\n\t<signature>N0_V1_oJ+uaOWE|v1_aMWcAqP2</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'PushContent': '阿猪米德在群聊中发了一张图片', 'NewMsgId': 3543230163972063457, 'MsgSeq': 871409266}
2025-07-29 19:17:17 | INFO | 收到图片消息: 消息ID:1207979937 来自:47325400669@chatroom 发送人:wxid_fh84okl6f5wp22 XML:<?xml version="1.0"?><msg><img aeskey="7a71666d6c626667796d767676727a68" encryver="0" cdnthumbaeskey="7a71666d6c626667796d767676727a68" cdnthumburl="3057020100044b30490201000204d6e1bab602033d14ba0204a926949d020468883692042431646431383931332d303766622d343533642d613534612d3739613539313131393766340204052828010201000405004c511e0054381738" cdnthumblength="3363" cdnthumbheight="66" cdnthumbwidth="100" cdnmidheight="0" cdnmidwidth="0" cdnhdheight="0" cdnhdwidth="0" cdnmidimgurl="3057020100044b30490201000204d6e1bab602033d14ba0204a926949d020468883692042431646431383931332d303766622d343533642d613534612d3739613539313131393766340204052828010201000405004c511e0054381738" length="25235" cdnbigimgurl="3057020100044b30490201000204d6e1bab602033d14ba0204a926949d020468883692042431646431383931332d303766622d343533642d613534612d3739613539313131393766340204052828010201000405004c511e0054381738" hdlength="59573" md5="d403db7e0629c7767c66c2ae38c8683f"><secHashInfoBase64 /><live><duration>0</duration><size>0</size><md5 /><fileid /><hdsize>0</hdsize><hdmd5 /><hdfileid /><stillimagetimems>0</stillimagetimems></live></img><platform_signature /><imgdatahash /><ImgSourceInfo><ImgSourceUrl /><BizType>0</BizType></ImgSourceInfo></msg>
2025-07-29 19:17:17 | INFO | [ImageEcho] 保存图片信息成功，当前群 47325400669@chatroom 已存储 5 张图片
2025-07-29 19:17:17 | INFO | [TimerTask] 缓存图片消息: 1207979937
2025-07-29 19:17:21 | DEBUG | 收到消息: {'MsgId': 179017096, 'FromUserName': {'string': '47325400669@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 1, 'Content': {'string': 'wxid_ubbh6q832tcs21:\n好'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1753787851, 'MsgSource': '<msgsource>\n\t<pua>1</pua>\n\t<eggIncluded>1</eggIncluded>\n\t<silence>0</silence>\n\t<membercount>222</membercount>\n\t<signature>N0_V1_3O9d1x5N|v1_mOKJWyKS</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'PushContent': '郭 : 好', 'NewMsgId': 3551673622462283842, 'MsgSeq': 871409267}
2025-07-29 19:17:21 | INFO | 收到文本消息: 消息ID:179017096 来自:47325400669@chatroom 发送人:wxid_ubbh6q832tcs21 @:[] 内容:好
2025-07-29 19:17:21 | INFO | 发送表情消息: 对方wxid:47325400669@chatroom md5:ec659f94db17b05434544a0c377ddfc0 总长度:9992069
2025-07-29 19:17:21 | DEBUG | 处理消息内容: '好'
2025-07-29 19:17:21 | DEBUG | 消息内容 '好' 不匹配任何命令，忽略
2025-07-29 19:17:30 | DEBUG | 收到消息: {'MsgId': 1248677725, 'FromUserName': {'string': '47325400669@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 1, 'Content': {'string': 'seraph333:\n黑丝'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1753787859, 'MsgSource': '<msgsource>\n\t<alnode>\n\t\t<fr>1</fr>\n\t</alnode>\n\t<silence>0</silence>\n\t<membercount>222</membercount>\n\t<signature>N0_V1_NdmFxws7|v1_vJDPRb0B</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'PushContent': '酱爆\ue147 : 黑丝', 'NewMsgId': 2947268881753636075, 'MsgSeq': 871409270}
2025-07-29 19:17:30 | INFO | 收到文本消息: 消息ID:1248677725 来自:47325400669@chatroom 发送人:seraph333 @:[] 内容:黑丝
2025-07-29 19:17:30 | DEBUG | 处理消息内容: '黑丝'
2025-07-29 19:17:30 | DEBUG | 消息内容 '黑丝' 不匹配任何命令，忽略
2025-07-29 19:17:33 | DEBUG | 收到消息: {'MsgId': 2138297506, 'FromUserName': {'string': '48097389945@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 47, 'Content': {'string': 'wxid_ubbh6q832tcs21:\n<msg><emoji fromusername = "wxid_ubbh6q832tcs21" tousername = "48097389945@chatroom" type="2" idbuffer="media:0_0" md5="3c5d9173b23ecc025e1ec8c858142b57" len = "1005541" productid="" androidmd5="3c5d9173b23ecc025e1ec8c858142b57" androidlen="1005541" s60v3md5 = "3c5d9173b23ecc025e1ec8c858142b57" s60v3len="1005541" s60v5md5 = "3c5d9173b23ecc025e1ec8c858142b57" s60v5len="1005541" cdnurl = "http://vweixinf.tc.qq.com/110/20401/stodownload?m=3c5d9173b23ecc025e1ec8c858142b57&amp;filekey=30440201010430302e02016e040253480420336335643931373362323365636330323565316563386338353831343262353702030f57e5040d00000004627466730000000132&amp;hy=SH&amp;storeid=2683a773b00015f7c687175f00000006e01004fb153480fd65b01e02592370&amp;ef=1&amp;bizid=1022" designerid = "" thumburl = "" encrypturl = "http://vweixinf.tc.qq.com/110/20402/stodownload?m=4bab97cabbe1706f0a603389b3529eeb&amp;filekey=30440201010430302e02016e040253480420346261623937636162626531373036663061363033333839623335323965656202030f57f0040d00000004627466730000000132&amp;hy=SH&amp;storeid=2683a773b0003cd1f687175f00000006e02004fb253480fd65b01e02592390&amp;ef=2&amp;bizid=1022" aeskey= "0d82b033625c4bf18bc5c338aeb7333e" externurl = "http://vweixinf.tc.qq.com/110/20403/stodownload?m=0064acb5c83b81eef262bb4c27a223c8&amp;filekey=30440201010430302e02016e04025348042030303634616362356338336238316565663236326262346332376132323363380203020430040d00000004627466730000000132&amp;hy=SH&amp;storeid=2683a773b0005d930687175f00000006e03004fb353480fd65b01e025923ad&amp;ef=3&amp;bizid=1022" externmd5 = "ef10f433c759c4681e3dcaecf0b0a32d" width= "640" height= "640" tpurl= "" tpauthkey= "" attachedtext= "" attachedtextcolor= "" lensid= "" emojiattr= "" linkid= "" desc= "" ></emoji>  </msg>'}, 'Status': 3, 'ImgStatus': 2, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1753787862, 'MsgSource': '<msgsource>\n\t<silence>0</silence>\n\t<membercount>73</membercount>\n\t<signature>N0_V1_+H5wk8RX|v1_9PyiwPph</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'PushContent': '郭在群聊中发了一个表情', 'NewMsgId': 2304970793443557481, 'MsgSeq': 871409271}
2025-07-29 19:17:33 | INFO | 收到表情消息: 消息ID:2138297506 来自:48097389945@chatroom 发送人:wxid_ubbh6q832tcs21 MD5:3c5d9173b23ecc025e1ec8c858142b57 大小:1005541
2025-07-29 19:17:33 | DEBUG | [DoubaoImageRecognition] 缓存表情消息: 2304970793443557481
2025-07-29 19:17:37 | DEBUG | 收到消息: {'MsgId': 2040889254, 'FromUserName': {'string': '48097389945@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 49, 'Content': {'string': 'wxid_w0rik7wwsvcy22:\n<?xml version="1.0"?>\n<msg>\n\t<appmsg appid="" sdkver="0">\n\t\t<title>可灵 给图中的腿加个渔网袜</title>\n\t\t<type>57</type>\n\t\t<appattach>\n\t\t\t<cdnthumbaeskey />\n\t\t\t<aeskey />\n\t\t</appattach>\n\t\t<refermsg>\n\t\t\t<type>3</type>\n\t\t\t<svrid>503301028514496684</svrid>\n\t\t\t<fromusr>wxid_w0rik7wwsvcy22</fromusr>\n\t\t\t<chatusr />\n\t\t\t<displayname>一罐小可乐</displayname>\n\t\t\t<content>&lt;msg&gt;&lt;img hdlength="0" length="92633" aeskey="bbaaee49eb503e7ff8e6ad98c4bc6b3a" encryver="1" md5="e6768d13f064ccd2cbbbd950b06de0dc" originsourcemd5="35ecf90ad0ef854eede0ff95257993c4" filekey="48097389945@chatroom_160514_1753787700" uploadcontinuecount="0" imgsourceurl="" hevc_mid_size="92633" cdnbigimgurl="" cdnmidimgurl="3057020100044b3049020100020473d808fb02032f53a3020448752d7002046888ac8a042439613766366238362d376333342d346432372d383933652d666262633736393037636538020405290a020201000405004c4cd300" cdnthumburl="3057020100044b3049020100020473d808fb02032f53a3020448752d7002046888ac8a042439613766366238362d376333342d346432372d383933652d666262633736393037636538020405290a020201000405004c4cd300" cdnthumblength="7138" cdnthumbwidth="136" cdnthumbheight="180" cdnthumbaeskey="bbaaee49eb503e7ff8e6ad98c4bc6b3a"&gt;&lt;secHashInfoBase64&gt;eyJwaGFzaCI6Ijc0MjViMDE1MTAwMDAwMDAiLCJwZHFoYXNoIjoiY2ZjNjU0ZTYxMDkyMzQ2MDc5MjZjNjY2MzNiMmVjOGZjYjYzOGU4YjUyM2IzMWVjYzU2MzJkOWJiNjc4Y2IyNSJ9&lt;/secHashInfoBase64&gt;&lt;/img&gt;&lt;appinfo&gt;&lt;appid&gt;&lt;/appid&gt;&lt;appname&gt;&lt;/appname&gt;&lt;version&gt;0&lt;/version&gt;&lt;isforceupdate&gt;1&lt;/isforceupdate&gt;&lt;mediatagname&gt;&lt;/mediatagname&gt;&lt;messageext&gt;&lt;/messageext&gt;&lt;messageaction&gt;&lt;/messageaction&gt;&lt;/appinfo&gt;&lt;/msg&gt;</content>\n\t\t\t<msgsource>&lt;msgsource&gt;&lt;svr_create_time&gt;1753787701&lt;/svr_create_time&gt;&lt;bizflag&gt;0&lt;/bizflag&gt;&lt;sec_msg_node&gt;&lt;uuid&gt;&lt;![CDATA[20c8c5f06aee83b199de1181d5c45240_]]&gt;&lt;/uuid&gt;&lt;/sec_msg_node&gt;&lt;tmp_node&gt;&lt;publisher-id&gt;&lt;![CDATA[msg_3497823020907248142|wxid_90s9h2i2gcb622|53435110965@chatroom]]&gt;&lt;/publisher-id&gt;&lt;/tmp_node&gt;&lt;alnode&gt;&lt;fr&gt;4&lt;/fr&gt;&lt;/alnode&gt;&lt;/msgsource&gt;</msgsource>\n\t\t\t<createtime>1753787701</createtime>\n\t\t</refermsg>\n\t</appmsg>\n\t<fromusername>wxid_w0rik7wwsvcy22</fromusername>\n\t<scene>0</scene>\n\t<appinfo>\n\t\t<version>1</version>\n\t\t<appname></appname>\n\t</appinfo>\n\t<commenturl></commenturl>\n</msg>\n'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1753787867, 'MsgSource': '<msgsource>\n\t<bizflag>0</bizflag>\n\t<pua>1</pua>\n\t<eggIncluded>1</eggIncluded>\n\t<sec_msg_node>\n\t\t<uuid>8e5204a7b8b44b3dac2edbbe3aa95771_</uuid>\n\t\t<risk-file-flag />\n\t\t<risk-file-md5-list />\n\t</sec_msg_node>\n\t<silence>0</silence>\n\t<membercount>73</membercount>\n\t<signature>N0_V1_YvXqzooR|v1_/1ZMNj9V</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'PushContent': '一罐小可乐 : 可灵 给图中的腿加个渔网袜', 'NewMsgId': 4438289226141178979, 'MsgSeq': 871409272}
2025-07-29 19:17:37 | DEBUG | 从群聊消息中提取发送者: wxid_w0rik7wwsvcy22
2025-07-29 19:17:37 | DEBUG | 使用已解析的XML处理引用消息
2025-07-29 19:17:37 | INFO | 收到引用消息: 消息ID:2040889254 来自:48097389945@chatroom 发送人:wxid_w0rik7wwsvcy22 内容:可灵 给图中的腿加个渔网袜 引用类型:3
2025-07-29 19:17:38 | INFO | [DouBaoImageToImage] 收到引用消息: 可灵 给图中的腿加个渔网袜
2025-07-29 19:17:38 | INFO | 发送文字消息: 对方wxid:48097389945@chatroom at: 内容:嗯
