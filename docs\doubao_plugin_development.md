# 豆包插件开发文档

## 1. 插件概述

豆包插件是一个多功能AI聊天插件，支持以下功能：
- 文本对话（支持语音和图片卡片输出）
- 图片生成
- 视频搜索

## 2. 技术架构

### 2.1 核心组件
- `<PERSON><PERSON><PERSON>` 类：继承自 `PluginBase`，实现插件的核心功能
- `TextCardService`：处理文本到图片的转换
- `WechatAPIClient`：处理微信消息的发送

### 2.2 关键依赖
```python
import aiohttp  # 异步HTTP请求
import tomllib  # TOML配置文件解析
from loguru import logger  # 日志管理
from PIL import Image  # 图片处理
```

## 3. 开发经验总结

### 3.1 配置管理
1. 使用TOML格式管理配置，便于读取和维护
2. 关键配置项：
   - API密钥和接口地址
   - 命令前缀
   - 功能开关

### 3.2 消息处理流程
1. 文本消息：
   - 150字以内：转换为语音
   - 超过150字：生成图片卡片
2. 图片消息：
   - 过滤AVIF格式
   - 使用临时文件处理
3. 视频消息：
   - 使用链接卡片展示
   - 动态获取缩略图

### 3.3 最佳实践

#### 3.3.1 错误处理
```python
try:
    # 业务逻辑
except Exception as e:
    logger.error(f"错误详情: {str(e)}")
    await bot.send_at_message(
        message["FromWxid"],
        "❌处理失败，请稍后重试",
        [message["SenderWxid"]]
    )
```

#### 3.3.2 资源管理
1. 临时文件处理：
```python
try:
    # 文件操作
finally:
    if os.path.exists(temp_path):
        os.remove(temp_path)
```

2. 会话管理：
```python
async def get_session(self):
    if self._session is None:
        self._session = aiohttp.ClientSession(
            timeout=aiohttp.ClientTimeout(total=300)
        )
    return self._session
```

#### 3.3.3 文本预处理
1. 移除特殊前缀：
```python
if text.startswith("正在阅读"):
    text = text[4:]
```

2. 格式化处理：
```python
text = re.sub(r'[\U0001F300-\U0001F9FF]', '', text)  # 移除表情
text = re.sub(r'\s+', ' ', text)  # 处理空白字符
```

## 4. 问题解决记录

### 4.1 图片处理优化
1. 问题：头部图片不显示
   - 解决：更换更稳定的图片API
   - 实现：使用 `https://api.317ak.com/API/tp/dmdntp.php`

### 4.2 视频链接处理
1. 问题：缩略图重复
   - 解决：为每个视频请求新的缩略图
   - 实现：添加时间戳参数

### 4.3 文本处理优化
1. 问题：特殊前缀影响显示
   - 解决：统一处理"正在阅读"和"正在搜索"前缀
   - 实现：在文本处理开始时移除

## 5. 性能优化

### 5.1 会话管理
- 使用单例会话
- 设置合理的超时时间
- 及时清理资源

### 5.2 文件处理
- 使用临时文件
- 确保文件及时清理
- 避免内存泄漏

### 5.3 并发处理
- 使用异步操作
- 合理设置超时
- 避免阻塞操作

## 6. 后续优化建议

1. 缓存优化
   - 实现图片缓存
   - 优化会话管理

2. 错误重试
   - 添加重试机制
   - 优化错误提示

3. 性能监控
   - 添加性能日志
   - 监控关键指标

4. 用户体验
   - 优化提示信息
   - 添加进度反馈

## 7. 维护建议

1. 日志管理
   - 记录关键操作
   - 便于问题定位

2. 配置管理
   - 统一配置文件
   - 便于维护更新

3. 代码结构
   - 模块化设计
   - 便于扩展

4. 文档更新
   - 及时更新文档
   - 记录重要变更

## 8. 参考资料

1. API文档
   - 豆包API接口文档
   - 微信API文档

2. 相关项目
   - TextCardService
   - WechatAPIClient

## 9. 更新日志

### v1.0.0
- 实现基础功能
- 支持文本对话
- 支持图片生成
- 支持视频搜索

### v1.0.1
- 优化图片处理
- 优化视频缩略图
- 优化文本处理 