[DouBaoImageToImage]
enable = true  # 图生图功能保留，识图功能已移植到DoubaoImageRecognition插件
command = ["豆包图生图"]
command-format = """
🎨 豆包AI图生图功能

📝 使用方法：
• 引用图片 + 豆包 [提示词] - 处理引用的图片
• 豆包图生图 [提示词] [图片路径] - 使用指定图片路径

⚠️ 注意：识图功能已移至DoubaoImageRecognition插件

⚙️ 处理步骤：
1. 上传图片到豆包AI
2. 提交AI处理任务
3. 等待处理结果
4. 返回处理结果（图生图返回图片）
"""

# 识图功能已移植到DoubaoImageRecognition插件
# [DouBaoImageToImage.recognition]
# command = ["识图"]
# command-format = "识图 [提示词] [图片路径] 或 引用图片并发送: 识图 [提示词]"

[DouBaoImageToImage.quote]
command = ["豆包"]
command-format = "引用图片并发送: 豆包 [提示词]"

[DouBaoImageToImage.api]
# 豆包AI相关配置
base_url = "https://www.doubao.com"  # 豆包AI网站地址
api_key = "d_ticket=c387aa7a25690088d32e0f295a9faa5828090; odin_tt=adc3604237fa735eef5d9b2726da716e7ea5dd617e5184147618251da72231aa77815405b56975c824529748a009f1f85cf25c9d17928d11725b0ef3d9e34d33; uid_tt=71dd29fd50dbf8f1a1d7b3090d765234; sessionid=9b6a807194da06d0c111bb246ff92247; sid_guard=9b6a807194da06d0c111bb246ff92247%7C1750354436%7C5184000%7CMon%2C+18-Aug-2025+17%3A33%3A56+GMT"  # 豆包AI的Cookie字符串
model = "doubao-image-generation"  # 豆包AI的图生图模型名称

[DouBaoImageToImage.rate_limit]
cooldown = 15  # 豆包AI可能需要更长的冷却时间

# 自然化响应设置
natural_response = true  # 启用自然化响应，避免机械化回复
