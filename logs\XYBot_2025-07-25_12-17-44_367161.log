2025-07-25 12:17:45 | SUCCESS | 读取主设置成功
2025-07-25 12:17:45 | DEBUG | 最终使用的 Redis 主机地址: 127.0.0.1
2025-07-25 12:17:45 | INFO | 2025/07/25 12:17:45 GetRedisAddr: 127.0.0.1:6379
2025-07-25 12:17:45 | INFO | 2025/07/25 12:17:45 Redis连接成功 | 地址:127.0.0.1:6379 DB:0
2025-07-25 12:17:45 | INFO | 2025/07/25 12:17:45 Server start at :9000
2025-07-25 12:17:46 | SUCCESS | WechatAPI服务已启动
2025-07-25 12:17:46 | INFO | 登录账号信息: wxid: wxid_4usgcju5ey9q29  昵称: 瑶瑶  微信号: Today-KFC  手机号: None
2025-07-25 12:17:46 | INFO | 登录设备信息: device_name: <PERSON>'s Pad  device_id: 49d050a7e94d1ecc40790d74b1c4369f
2025-07-25 12:17:46 | SUCCESS | 登录成功
2025-07-25 12:17:46 | SUCCESS | 已开启自动心跳
2025-07-25 12:17:46 | INFO | 成功加载表情映射文件，共 538 条记录
2025-07-25 12:17:46 | SUCCESS | 数据库初始化成功
2025-07-25 12:17:46 | SUCCESS | 定时任务已启动
2025-07-25 12:17:46 | SUCCESS | 插件 AISignIn 已加载定时任务: {'plugins.AISignIn.main.AISignIn.auto_signin_task'}
2025-07-25 12:17:46 | INFO | 已加载管理员列表: ['wxid_ubbh6q832tcs21', 'admin-wxid']
2025-07-25 12:17:47 | INFO | 播客API初始化成功
2025-07-25 12:17:47 | INFO | 加载配置完成，转换后的数据: {'gh_7057516c9e71': {'name': '唱舞全明星', 'groups': ['27852221909@chatroom', '55878994168@chatroom', '51891329927@chatroom']}, 'gh_d4d21a0e4623': {'name': '两颗桃', 'groups': ['48097389945@chatroom']}, 'gh_410ff2fa6ec9': {'name': 'NGC660 Ai研究院', 'groups': ['47442567074@chatroom']}, 'gh_f78c53c507c0': {'name': '不正常吐槽君', 'groups': ['48097389945@chatroom']}, 'gh_ba3381c847b3': {'name': '唱舞星计划', 'groups': ['51891329927@chatroom']}, 'gh_1a102fb4cad9': {'name': '我在娱乐圈的唯一人脉', 'groups': ['48097389945@chatroom', '27852221909@chatroom']}}
2025-07-25 12:17:47 | INFO | ArticleForwarder插件初始化完成 - 监控配置: {'gh_7057516c9e71': {'name': '唱舞全明星', 'groups': ['27852221909@chatroom', '55878994168@chatroom', '51891329927@chatroom']}, 'gh_d4d21a0e4623': {'name': '两颗桃', 'groups': ['48097389945@chatroom']}, 'gh_410ff2fa6ec9': {'name': 'NGC660 Ai研究院', 'groups': ['47442567074@chatroom']}, 'gh_f78c53c507c0': {'name': '不正常吐槽君', 'groups': ['48097389945@chatroom']}, 'gh_ba3381c847b3': {'name': '唱舞星计划', 'groups': ['51891329927@chatroom']}, 'gh_1a102fb4cad9': {'name': '我在娱乐圈的唯一人脉', 'groups': ['48097389945@chatroom', '27852221909@chatroom']}}
2025-07-25 12:17:47 | DEBUG | [TempFileManager] 添加清理规则: default
2025-07-25 12:17:47 | DEBUG | [TempFileManager] 添加清理规则: images
2025-07-25 12:17:47 | DEBUG | [TempFileManager] 添加清理规则: videos
2025-07-25 12:17:47 | DEBUG | [TempFileManager] 添加清理规则: audio
2025-07-25 12:17:47 | DEBUG | [TempFileManager] 添加清理规则: temp
2025-07-25 12:17:47 | DEBUG | [TempFileManager] 添加清理规则: large_files
2025-07-25 12:17:47 | INFO | [TempFileManager] 临时文件清理任务已启动
2025-07-25 12:17:48 | INFO | [ChatSummary] 数据库初始化成功
2025-07-25 12:17:48 | SUCCESS | 插件 GoodMorning 已加载定时任务: {'plugins.GoodMorning.main.GoodMorning.daily_task'}
2025-07-25 12:17:48 | SUCCESS | 插件 News 已加载定时任务: {'plugins.News.main.News.night_news', 'plugins.News.main.News.noon_news'}
2025-07-25 12:17:48 | INFO | [PatReply] AMR文件已存在且是最新的: C:\XYBotV2\data\paiyipai\1_BFB5258B-2B13-4CF4-A40C-5BCCE74E5414_converted.amr
2025-07-25 12:17:48 | DEBUG | [PatReply] 从缓存加载base64: 18228 字符
2025-07-25 12:17:48 | SUCCESS | 插件 QuarkSignIn 已加载定时任务: {'plugins.QuarkSignIn.main.QuarkSignIn.auto_signin_task'}
2025-07-25 12:17:48 | INFO | [QuarkSignIn] 已设置自动签到时间: 08:00
2025-07-25 12:17:48 | INFO | 插件状态: 启用, 检查间隔: 3600秒, 最大重试次数: 3, 重试延迟: 5秒
2025-07-25 12:17:48 | INFO | 已加载管理员列表: ['wxid_ubbh6q832tcs21', 'admin-wxid']
2025-07-25 12:17:48 | INFO | 已加载 2 个改名监控群和 1 个退群监控群
2025-07-25 12:17:48 | INFO | [RenameReminder] 开始启用插件...
2025-07-25 12:17:48 | SUCCESS | 插件 RenameReminder 已加载定时任务: {'plugins.RenameReminder.main.RenameReminder.check_name_changes'}
2025-07-25 12:17:48 | INFO | [RenameReminder] 插件已启用，开始初始化...
2025-07-25 12:17:48 | INFO | [RenameReminder] 支持的命令: 开启改名提醒, 关闭改名提醒, 开启退群提醒, 关闭退群提醒, 查看提醒状态
2025-07-25 12:17:48 | INFO | 已设置检查间隔为 3600 秒
2025-07-25 12:17:48 | INFO | 已加载 2 个监控群，开始更新成员列表
2025-07-25 12:17:48 | DEBUG | 已更新群 51891329927@chatroom 的成员列表
2025-07-25 12:17:49 | DEBUG | 已更新群 27852221909@chatroom 的成员列表
2025-07-25 12:17:49 | SUCCESS | 插件 TimerTask 已加载定时任务: {'plugins.TimerTask.main.TimerTask.check_tasks'}
2025-07-25 12:17:49 | INFO | [VideoDemand] 加载了 128 个视频类别配置
2025-07-25 12:17:49 | INFO | [YaoyaoPlugin] 私聊黑名单用户: ['gh_a5cf07d474f1', 'gh_ba3381c847b3']
2025-07-25 12:17:49 | INFO | [YaoyaoPlugin] 已加载管理员列表: ['wxid_ubbh6q832tcs21', 'admin-wxid']
2025-07-25 12:17:49 | INFO | [yuanbao] 插件初始化完成
2025-07-25 12:17:49 | DEBUG | [yuanbao] 指令: ['元宝', 'yuanbao']
2025-07-25 12:17:49 | DEBUG | [yuanbao] 绘图模型: gpt_175B_0404
2025-07-25 12:17:50 | DEBUG | [yuanbao] 认证信息加载状态: hy_token=True, hy_user=True, agent_id=True, x_uskey=True
2025-07-25 12:17:50 | SUCCESS | 已加载插件: ['AdminPoint', 'AdminSignInReset', 'AdminWhitelist', 'AISignIn', 'AppMessageTester', 'ArticleForwarder', 'BaiduAgentsPlugin', 'BaiduDraw', 'BotStatus', 'ChatSummary', 'DanceSignInPlugin', 'DeepseekPlugin', 'Doubao', 'DoubaoDrawing', 'DoubaoImageRecognition', 'DouBaoImageToImage', 'DouBaoImageToVideo', 'DoubaoVideoSearch', 'DouHui', 'EmojiTestPlugin', 'FixedQuoteTest', 'GetContact', 'Gomoku', 'GoodMorning', 'HunyuanDraw', 'ImageEcho', 'ImageToImage', 'JiemengDraw', 'KeLingImageToImage', 'KlingAI', 'Leaderboard', 'LuckyDraw', 'ManagePlugin', 'MeituAI', 'Menu', 'MiniProgramTester', 'Music', 'News', 'PatReply', 'PointTrade', 'QuarkSignIn', 'QueryPoint', 'RandomMember', 'RandomPicture', 'RenameReminder', 'RevokePlugin', 'RoboNeo', 'SignIn', 'TempFileManagerPlugin', 'TencentLke', 'TimerTask', 'TongyiDraw', 'URLShortener', 'VideoDemand', 'VideoParserPlugin', 'VideoTest', 'VivoAgentsPlugin', 'VoiceTest', 'Warthunder', 'WeatherQuery', 'WelcomePlugin', 'Xunfei', 'YaoyaoPlugin', 'Yuanbao', '抽签', '造梦次元']
2025-07-25 12:17:50 | INFO | 处理堆积消息中
2025-07-25 12:17:50 | DEBUG | 接受到 1 条消息
2025-07-25 12:17:51 | SUCCESS | 处理堆积消息完毕
2025-07-25 12:17:51 | SUCCESS | 开始处理消息
2025-07-25 12:17:59 | DEBUG | 收到消息: {'MsgId': 1273018268, 'FromUserName': {'string': '48097389945@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 1, 'Content': {'string': 'wxid_wlnzvr8ivgd422:\n瑶瑶你现在速度不行了'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1753417101, 'MsgSource': '<msgsource>\n\t<pua>1</pua>\n\t<eggIncluded>1</eggIncluded>\n\t<silence>0</silence>\n\t<membercount>63</membercount>\n\t<signature>N0_V1_J/QK1TSI|v1_me712z8c</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'PushContent': '锦岚（开玩笑就退群版） : 瑶瑶你现在速度不行了', 'NewMsgId': 2562381458998658570, 'MsgSeq': 871398376}
2025-07-25 12:17:59 | INFO | 收到文本消息: 消息ID:1273018268 来自:48097389945@chatroom 发送人:wxid_wlnzvr8ivgd422 @:[] 内容:瑶瑶你现在速度不行了
2025-07-25 12:17:59 | INFO | 成功加载表情映射文件，共 538 条记录
2025-07-25 12:17:59 | DEBUG | 处理消息内容: '瑶瑶你现在速度不行了'
2025-07-25 12:17:59 | DEBUG | 消息内容 '瑶瑶你现在速度不行了' 不匹配任何命令，忽略
2025-07-25 12:18:11 | INFO | 发送语音消息: 对方wxid:48097389945@chatroom 时长:11400 格式:mp3 音频base64略
2025-07-25 12:18:12 | DEBUG | 收到消息: {'MsgId': 103273978, 'FromUserName': {'string': '48097389945@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 1, 'Content': {'string': 'wxid_wlnzvr8ivgd422:\n瑶瑶你不行'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1753417105, 'MsgSource': '<msgsource>\n\t<pua>1</pua>\n\t<eggIncluded>1</eggIncluded>\n\t<silence>0</silence>\n\t<membercount>63</membercount>\n\t<signature>N0_V1_tOreZimg|v1_BnSHpPDc</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'PushContent': '锦岚（开玩笑就退群版） : 瑶瑶你不行', 'NewMsgId': 7385735235778781961, 'MsgSeq': 871398377}
2025-07-25 12:18:12 | INFO | 收到文本消息: 消息ID:103273978 来自:48097389945@chatroom 发送人:wxid_wlnzvr8ivgd422 @:[] 内容:瑶瑶你不行
2025-07-25 12:18:12 | DEBUG | 处理消息内容: '瑶瑶你不行'
2025-07-25 12:18:12 | DEBUG | 消息内容 '瑶瑶你不行' 不匹配任何命令，忽略
2025-07-25 12:18:26 | INFO | 发送语音消息: 对方wxid:48097389945@chatroom 时长:15888 格式:mp3 音频base64略
2025-07-25 12:18:26 | DEBUG | 收到消息: {'MsgId': 755853619, 'FromUserName': {'string': '48097389945@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 1, 'Content': {'string': 'xiaomaochong:\n瑶瑶你现在速度不行了'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1753417107, 'MsgSource': '<msgsource>\n\t<pua>1</pua>\n\t<silence>0</silence>\n\t<membercount>63</membercount>\n\t<signature>N0_V1_TKGEddZS|v1_WLSJYqJL</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'PushContent': '小爱 : 瑶瑶你现在速度不行了', 'NewMsgId': 7472778644554374230, 'MsgSeq': 871398378}
2025-07-25 12:18:26 | INFO | 收到文本消息: 消息ID:755853619 来自:48097389945@chatroom 发送人:xiaomaochong @:[] 内容:瑶瑶你现在速度不行了
2025-07-25 12:18:27 | DEBUG | 处理消息内容: '瑶瑶你现在速度不行了'
2025-07-25 12:18:27 | DEBUG | 消息内容 '瑶瑶你现在速度不行了' 不匹配任何命令，忽略
2025-07-25 12:18:41 | INFO | 发送语音消息: 对方wxid:48097389945@chatroom 时长:20088 格式:mp3 音频base64略
2025-07-25 12:18:41 | DEBUG | 收到消息: {'MsgId': 1500759474, 'FromUserName': {'string': '48097389945@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 1, 'Content': {'string': 'xiaomaochong:\n瑶瑶你不行'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1753417109, 'MsgSource': '<msgsource>\n\t<pua>1</pua>\n\t<silence>0</silence>\n\t<membercount>63</membercount>\n\t<signature>N0_V1_35utgzd5|v1_LIxqKyk4</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'PushContent': '小爱 : 瑶瑶你不行', 'NewMsgId': 670613867939350600, 'MsgSeq': 871398379}
2025-07-25 12:18:41 | INFO | 收到文本消息: 消息ID:1500759474 来自:48097389945@chatroom 发送人:xiaomaochong @:[] 内容:瑶瑶你不行
2025-07-25 12:18:42 | DEBUG | 处理消息内容: '瑶瑶你不行'
2025-07-25 12:18:42 | DEBUG | 消息内容 '瑶瑶你不行' 不匹配任何命令，忽略
2025-07-25 12:18:54 | INFO | 发送语音消息: 对方wxid:48097389945@chatroom 时长:16248 格式:mp3 音频base64略
2025-07-25 12:18:54 | DEBUG | 收到消息: {'MsgId': 1862443201, 'FromUserName': {'string': '48097389945@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 1, 'Content': {'string': 'wxid_wlnzvr8ivgd422:\n是不是男人啊'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1753417110, 'MsgSource': '<msgsource>\n\t<pua>1</pua>\n\t<eggIncluded>1</eggIncluded>\n\t<silence>0</silence>\n\t<membercount>63</membercount>\n\t<signature>N0_V1_IWpU86gM|v1_+WxyVFcO</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'PushContent': '锦岚（开玩笑就退群版） : 是不是男人啊', 'NewMsgId': 5012981930855931599, 'MsgSeq': 871398380}
2025-07-25 12:18:54 | INFO | 收到文本消息: 消息ID:1862443201 来自:48097389945@chatroom 发送人:wxid_wlnzvr8ivgd422 @:[] 内容:是不是男人啊
2025-07-25 12:18:55 | DEBUG | 处理消息内容: '是不是男人啊'
2025-07-25 12:18:55 | DEBUG | 消息内容 '是不是男人啊' 不匹配任何命令，忽略
2025-07-25 12:18:56 | DEBUG | 收到消息: {'MsgId': 969050158, 'FromUserName': {'string': '48097389945@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 1, 'Content': {'string': 'rongcheng6630606:\n@喵小叶\u2005说我妈宝男的时候'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1753417118, 'MsgSource': '<msgsource>\n\t<atuserlist><![CDATA[wxid_84mmq4cu7ita22]]></atuserlist>\n\t<pua>1</pua>\n\t<eggIncluded>1</eggIncluded>\n\t<silence>0</silence>\n\t<membercount>63</membercount>\n\t<signature>N0_V1_qEQ8mwva|v1_3uXmNAdl</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'PushContent': '× : @喵小叶\u2005说我妈宝男的时候', 'NewMsgId': 3224483991928605807, 'MsgSeq': 871398383}
2025-07-25 12:18:56 | INFO | 收到文本消息: 消息ID:969050158 来自:48097389945@chatroom 发送人:rongcheng6630606 @:['wxid_84mmq4cu7ita22'] 内容:@喵小叶 说我妈宝男的时候
2025-07-25 12:18:57 | DEBUG | 处理消息内容: '@喵小叶 说我妈宝男的时候'
2025-07-25 12:18:57 | DEBUG | 消息内容 '@喵小叶 说我妈宝男的时候' 不匹配任何命令，忽略
2025-07-25 12:18:59 | DEBUG | 收到消息: {'MsgId': 792824371, 'FromUserName': {'string': '48097389945@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 1, 'Content': {'string': 'rongcheng6630606:\n咱俩的关系就掰了'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1753417125, 'MsgSource': '<msgsource>\n\t<pua>1</pua>\n\t<eggIncluded>1</eggIncluded>\n\t<silence>0</silence>\n\t<membercount>63</membercount>\n\t<signature>N0_V1_MrHTw9er|v1_NG61gUnA</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'PushContent': '× : 咱俩的关系就掰了', 'NewMsgId': 1382940190055152404, 'MsgSeq': 871398384}
2025-07-25 12:18:59 | INFO | 收到文本消息: 消息ID:792824371 来自:48097389945@chatroom 发送人:rongcheng6630606 @:[] 内容:咱俩的关系就掰了
2025-07-25 12:18:59 | DEBUG | 处理消息内容: '咱俩的关系就掰了'
2025-07-25 12:18:59 | DEBUG | 消息内容 '咱俩的关系就掰了' 不匹配任何命令，忽略
2025-07-25 12:19:01 | DEBUG | 收到消息: {'MsgId': 59493223, 'FromUserName': {'string': '48097389945@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 1, 'Content': {'string': 'wxid_ubbh6q832tcs21:\n好像是有个同步操作导致消息堵塞了'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1753417137, 'MsgSource': '<msgsource>\n\t<pua>1</pua>\n\t<eggIncluded>1</eggIncluded>\n\t<silence>0</silence>\n\t<membercount>63</membercount>\n\t<signature>N0_V1_EozJoWMC|v1_TZVLtl2I</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'PushContent': '郭 : 好像是有个同步操作导致消息堵塞了', 'NewMsgId': 1728897968733182941, 'MsgSeq': 871398387}
2025-07-25 12:19:01 | INFO | 收到文本消息: 消息ID:59493223 来自:48097389945@chatroom 发送人:wxid_ubbh6q832tcs21 @:[] 内容:好像是有个同步操作导致消息堵塞了
2025-07-25 12:19:01 | DEBUG | 处理消息内容: '好像是有个同步操作导致消息堵塞了'
2025-07-25 12:19:01 | DEBUG | 消息内容 '好像是有个同步操作导致消息堵塞了' 不匹配任何命令，忽略
2025-07-25 12:19:01 | DEBUG | 收到消息: {'MsgId': 623826490, 'FromUserName': {'string': '48097389945@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 1, 'Content': {'string': 'rongcheng6630606:\n不是朋友更不是微信好友了'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1753417138, 'MsgSource': '<msgsource>\n\t<pua>1</pua>\n\t<eggIncluded>1</eggIncluded>\n\t<silence>0</silence>\n\t<membercount>63</membercount>\n\t<signature>N0_V1_Six940gW|v1_fKaMan4z</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'PushContent': '× : 不是朋友更不是微信好友了', 'NewMsgId': 903288397920584509, 'MsgSeq': 871398388}
2025-07-25 12:19:01 | INFO | 收到文本消息: 消息ID:623826490 来自:48097389945@chatroom 发送人:rongcheng6630606 @:[] 内容:不是朋友更不是微信好友了
2025-07-25 12:19:02 | DEBUG | 处理消息内容: '不是朋友更不是微信好友了'
2025-07-25 12:19:02 | DEBUG | 消息内容 '不是朋友更不是微信好友了' 不匹配任何命令，忽略
2025-07-25 12:19:03 | DEBUG | 收到消息: {'MsgId': 1098978037, 'FromUserName': {'string': '48097389945@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 47, 'Content': {'string': 'rongcheng6630606:\n<msg><emoji fromusername="rongcheng6630606" tousername="48097389945@chatroom" type="2" idbuffer="media:0_0" md5="52804f814294095825883dd3b1733217" len="1070206" productid="" androidmd5="52804f814294095825883dd3b1733217" androidlen="1070206" s60v3md5="52804f814294095825883dd3b1733217" s60v3len="1070206" s60v5md5="52804f814294095825883dd3b1733217" s60v5len="1070206" cdnurl="http://wxapp.tc.qq.com/262/20304/stodownload?m=52804f814294095825883dd3b1733217&amp;filekey=30350201010421301f020201060402535a041052804f814294095825883dd3b1733217020310547e040d00000004627466730000000132&amp;hy=SZ&amp;storeid=26806fdc10006cdab7fb146720000010600004f50535a219d9151571919d50&amp;bizid=1023" designerid="" thumburl="" encrypturl="http://wxapp.tc.qq.com/262/20304/stodownload?m=7f63c38ad7aa715a288e29c6b91d1357&amp;filekey=30350201010421301f020201060402535a04107f63c38ad7aa715a288e29c6b91d13570203105480040d00000004627466730000000132&amp;hy=SZ&amp;storeid=26806fdc1000a96707fb146720000010600004f50535a17865bc1e783f3fd4&amp;bizid=1023" aeskey="e316dd42c0629e9ec65bf49f7183e15a" externurl="http://wxapp.tc.qq.com/262/20304/stodownload?m=bffa4423717095f72d4d4d10c4728d1e&amp;filekey=30350201010421301f020201060402535a0410bffa4423717095f72d4d4d10c4728d1e02030497a0040d00000004627466730000000132&amp;hy=SZ&amp;storeid=26806fdca0003b3c07fb146720000010600004f50535a29e63bc1e7c36acda&amp;bizid=1023" externmd5="53f80d28cc15e5d84d392f6d8a711776" width="300" height="305" tpurl="" tpauthkey="" attachedtext="" attachedtextcolor="" lensid="" emojiattr="" linkid="" desc=""></emoji></msg>'}, 'Status': 3, 'ImgStatus': 2, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1753417142, 'MsgSource': '<msgsource>\n\t<silence>0</silence>\n\t<membercount>63</membercount>\n\t<signature>N0_V1_OacpTooQ|v1_82mminih</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'PushContent': '×在群聊中发了一个表情', 'NewMsgId': 610336745317687187, 'MsgSeq': 871398389}
2025-07-25 12:19:03 | INFO | 收到表情消息: 消息ID:1098978037 来自:48097389945@chatroom 发送人:rongcheng6630606 MD5:52804f814294095825883dd3b1733217 大小:1070206
2025-07-25 12:19:04 | DEBUG | [DoubaoImageRecognition] 缓存表情消息: 610336745317687187
2025-07-25 12:19:04 | DEBUG | 收到消息: {'MsgId': 1518360095, 'FromUserName': {'string': '48097389945@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 1, 'Content': {'string': 'wxid_ubbh6q832tcs21:\n瑶瑶再见'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1753417154, 'MsgSource': '<msgsource>\n\t<pua>1</pua>\n\t<eggIncluded>1</eggIncluded>\n\t<silence>0</silence>\n\t<membercount>63</membercount>\n\t<signature>N0_V1_QPt00Pc+|v1_YtLf+gZd</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'PushContent': '郭 : 瑶瑶再见', 'NewMsgId': 106985452689575574, 'MsgSeq': 871398392}
2025-07-25 12:19:04 | INFO | 收到文本消息: 消息ID:1518360095 来自:48097389945@chatroom 发送人:wxid_ubbh6q832tcs21 @:[] 内容:瑶瑶再见
2025-07-25 12:19:05 | DEBUG | 处理消息内容: '瑶瑶再见'
2025-07-25 12:19:05 | DEBUG | 消息内容 '瑶瑶再见' 不匹配任何命令，忽略
2025-07-25 12:19:11 | INFO | 发送语音消息: 对方wxid:48097389945@chatroom 时长:3864 格式:mp3 音频base64略
2025-07-25 12:19:11 | DEBUG | 收到消息: {'MsgId': 2000522529, 'FromUserName': {'string': '27852221909@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 1, 'Content': {'string': 'wxid_ubbh6q832tcs21:\n走'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1753417169, 'MsgSource': '<msgsource>\n\t<pua>1</pua>\n\t<eggIncluded>1</eggIncluded>\n\t<silence>1</silence>\n\t<membercount>146</membercount>\n\t<signature>N0_V1_nqFeo49x|v1_8oipUVKu</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'NewMsgId': 8499104681840484096, 'MsgSeq': 871398395}
2025-07-25 12:19:11 | INFO | 收到文本消息: 消息ID:2000522529 来自:27852221909@chatroom 发送人:wxid_ubbh6q832tcs21 @:[] 内容:走
2025-07-25 12:19:12 | INFO | 发送表情消息: 对方wxid:27852221909@chatroom md5:479d9683141301494753f07fd93dff19 总长度:9992069
2025-07-25 12:19:12 | DEBUG | 处理消息内容: '走'
2025-07-25 12:19:12 | DEBUG | 消息内容 '走' 不匹配任何命令，忽略
2025-07-25 12:19:23 | DEBUG | 收到消息: {'MsgId': 1664090968, 'FromUserName': {'string': '48097389945@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 1, 'Content': {'string': 'xiaomaochong:\n小爱再见'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1753417185, 'MsgSource': '<msgsource>\n\t<pua>1</pua>\n\t<silence>0</silence>\n\t<membercount>63</membercount>\n\t<signature>N0_V1_8tBT3KRJ|v1_rb5hSMyV</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'PushContent': '小爱 : 小爱再见', 'NewMsgId': 4704635320960624306, 'MsgSeq': 871398400}
2025-07-25 12:19:23 | INFO | 收到文本消息: 消息ID:1664090968 来自:48097389945@chatroom 发送人:xiaomaochong @:[] 内容:小爱再见
2025-07-25 12:19:23 | DEBUG | 处理消息内容: '小爱再见'
2025-07-25 12:19:23 | DEBUG | 消息内容 '小爱再见' 不匹配任何命令，忽略
2025-07-25 12:19:31 | DEBUG | 收到消息: {'MsgId': 462755227, 'FromUserName': {'string': '48097389945@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 1, 'Content': {'string': 'wxid_ubbh6q832tcs21:\n瑶瑶再见'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1753417193, 'MsgSource': '<msgsource>\n\t<pua>1</pua>\n\t<eggIncluded>1</eggIncluded>\n\t<silence>0</silence>\n\t<membercount>63</membercount>\n\t<signature>N0_V1_lzpuLT56|v1_01wx+tGn</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'PushContent': '郭 : 瑶瑶再见', 'NewMsgId': 1618138149186087949, 'MsgSeq': 871398401}
2025-07-25 12:19:31 | INFO | 收到文本消息: 消息ID:462755227 来自:48097389945@chatroom 发送人:wxid_ubbh6q832tcs21 @:[] 内容:瑶瑶再见
2025-07-25 12:19:31 | DEBUG | 处理消息内容: '瑶瑶再见'
2025-07-25 12:19:31 | DEBUG | 消息内容 '瑶瑶再见' 不匹配任何命令，忽略
2025-07-25 12:19:32 | INFO | 发送文字消息: 对方wxid:48097389945@chatroom at: 内容:⚠️您提问太频繁啦~建议休息一下再来哦
（10分钟内最多提问5次）
2025-07-25 12:19:33 | DEBUG | 收到消息: {'MsgId': 480991089, 'FromUserName': {'string': '48097389945@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 1, 'Content': {'string': 'wxid_wlnzvr8ivgd422:\n小爱再见'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1753417195, 'MsgSource': '<msgsource>\n\t<pua>1</pua>\n\t<eggIncluded>1</eggIncluded>\n\t<silence>0</silence>\n\t<membercount>63</membercount>\n\t<signature>N0_V1_I6vdCaXr|v1_WKhpfpFd</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'PushContent': '锦岚（开玩笑就退群版） : 小爱再见', 'NewMsgId': 8886185867529920768, 'MsgSeq': 871398404}
2025-07-25 12:19:33 | INFO | 收到文本消息: 消息ID:480991089 来自:48097389945@chatroom 发送人:wxid_wlnzvr8ivgd422 @:[] 内容:小爱再见
2025-07-25 12:19:33 | DEBUG | 处理消息内容: '小爱再见'
2025-07-25 12:19:33 | DEBUG | 消息内容 '小爱再见' 不匹配任何命令，忽略
2025-07-25 12:19:35 | DEBUG | 收到消息: {'MsgId': 253949347, 'FromUserName': {'string': '48097389945@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 1, 'Content': {'string': 'wxid_ubbh6q832tcs21:\n再见'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1753417198, 'MsgSource': '<msgsource>\n\t<pua>1</pua>\n\t<eggIncluded>1</eggIncluded>\n\t<silence>0</silence>\n\t<membercount>63</membercount>\n\t<signature>N0_V1_NyXCp2wZ|v1_n+EHujWK</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'PushContent': '郭 : 再见', 'NewMsgId': 7150951356798618148, 'MsgSeq': 871398405}
2025-07-25 12:19:35 | INFO | 收到文本消息: 消息ID:253949347 来自:48097389945@chatroom 发送人:wxid_ubbh6q832tcs21 @:[] 内容:再见
2025-07-25 12:19:36 | DEBUG | 处理消息内容: '再见'
2025-07-25 12:19:36 | DEBUG | 消息内容 '再见' 不匹配任何命令，忽略
2025-07-25 12:19:37 | DEBUG | 收到消息: {'MsgId': 890901888, 'FromUserName': {'string': '48097389945@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 34, 'Content': {'string': 'xiaomaochong:\n<msg><voicemsg endflag="1" cancelflag="0" forwardflag="0" voiceformat="4" voicelength="17000" length="49322" bufid="0" aeskey="716e676f61646170757a686f67616f67" voiceurl="3052020100044b304902010002049363814102033d14ba0204fa02ff9d0204688305f0042435613533316562382d383238652d346434312d386633652d65323035393261306438383702040528000f02010004001dc74187" voicemd5="1a28a3c85d8d899108ad31bd38267282" clientmsgid="494cd5716bb4ce7e67f76aaff6aa3e5648097389945@chatroom_47740_1753417198" fromusername="xiaomaochong" /></msg>'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1753417200, 'MsgSource': '<msgsource>\n\t<silence>0</silence>\n\t<membercount>63</membercount>\n\t<signature>N0_V1_v/pku5jM|v1_g5MdkS9a</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'PushContent': '小爱在群聊中发了一段语音', 'NewMsgId': 4629196491094859062, 'MsgSeq': 871398406}
2025-07-25 12:19:37 | INFO | 收到语音消息: 消息ID:890901888 来自:48097389945@chatroom 发送人:xiaomaochong XML:
<msg><voicemsg endflag="1" cancelflag="0" forwardflag="0" voiceformat="4" voicelength="17000" length="49322" bufid="0" aeskey="716e676f61646170757a686f67616f67" voiceurl="3052020100044b304902010002049363814102033d14ba0204fa02ff9d0204688305f0042435613533316562382d383238652d346434312d386633652d65323035393261306438383702040528000f02010004001dc74187" voicemd5="1a28a3c85d8d899108ad31bd38267282" clientmsgid="494cd5716bb4ce7e67f76aaff6aa3e5648097389945@chatroom_47740_1753417198" fromusername="xiaomaochong" /></msg>
2025-07-25 12:19:38 | DEBUG | 收到消息: {'MsgId': 362107992, 'FromUserName': {'string': '48097389945@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 1, 'Content': {'string': 'wxid_wlnzvr8ivgd422:\n瑶瑶再见'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1753417200, 'MsgSource': '<msgsource>\n\t<pua>1</pua>\n\t<eggIncluded>1</eggIncluded>\n\t<silence>0</silence>\n\t<membercount>63</membercount>\n\t<signature>N0_V1_5qXNQ7px|v1_Ssl1SjCO</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'PushContent': '锦岚（开玩笑就退群版） : 瑶瑶再见', 'NewMsgId': 382523403971407799, 'MsgSeq': 871398407}
2025-07-25 12:19:38 | INFO | 收到文本消息: 消息ID:362107992 来自:48097389945@chatroom 发送人:wxid_wlnzvr8ivgd422 @:[] 内容:瑶瑶再见
2025-07-25 12:19:38 | DEBUG | 处理消息内容: '瑶瑶再见'
2025-07-25 12:19:38 | DEBUG | 消息内容 '瑶瑶再见' 不匹配任何命令，忽略
2025-07-25 12:19:49 | DEBUG | 收到消息: {'MsgId': 989127629, 'FromUserName': {'string': '48097389945@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 1, 'Content': {'string': 'wxid_ubbh6q832tcs21:\n再见'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1753417211, 'MsgSource': '<msgsource>\n\t<pua>1</pua>\n\t<eggIncluded>1</eggIncluded>\n\t<silence>0</silence>\n\t<membercount>63</membercount>\n\t<signature>N0_V1_yjkckvc5|v1_htFTNdig</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'PushContent': '郭 : 再见', 'NewMsgId': 4161772480633674910, 'MsgSeq': 871398408}
2025-07-25 12:19:49 | INFO | 收到文本消息: 消息ID:989127629 来自:48097389945@chatroom 发送人:wxid_ubbh6q832tcs21 @:[] 内容:再见
2025-07-25 12:19:49 | DEBUG | 处理消息内容: '再见'
2025-07-25 12:19:49 | DEBUG | 消息内容 '再见' 不匹配任何命令，忽略
2025-07-25 12:19:56 | DEBUG | 收到消息: {'MsgId': 1150264964, 'FromUserName': {'string': '48097389945@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 1, 'Content': {'string': 'wxid_ubbh6q832tcs21:\n特码的'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1753417219, 'MsgSource': '<msgsource>\n\t<pua>1</pua>\n\t<eggIncluded>1</eggIncluded>\n\t<silence>0</silence>\n\t<membercount>63</membercount>\n\t<signature>N0_V1_M7czPWxn|v1_2xbcVdQq</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'PushContent': '郭 : 特码的', 'NewMsgId': 6424521678317921297, 'MsgSeq': 871398409}
2025-07-25 12:19:56 | INFO | 收到文本消息: 消息ID:1150264964 来自:48097389945@chatroom 发送人:wxid_ubbh6q832tcs21 @:[] 内容:特码的
2025-07-25 12:19:57 | DEBUG | 处理消息内容: '特码的'
2025-07-25 12:19:57 | DEBUG | 消息内容 '特码的' 不匹配任何命令，忽略
2025-07-25 12:20:05 | DEBUG | 收到消息: {'MsgId': 1374786107, 'FromUserName': {'string': '55878994168@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 1, 'Content': {'string': 'wxid_rwfb9vuy93jn22:\n[吃瓜]'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1753417227, 'MsgSource': '<msgsource>\n\t<pua>1</pua>\n\t<eggIncluded>1</eggIncluded>\n\t<silence>0</silence>\n\t<membercount>3</membercount>\n\t<signature>N0_V1_aw5GABYU|v1_5VjL7ZK1</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'PushContent': '黹 : [吃瓜]', 'NewMsgId': 6323552950527484279, 'MsgSeq': 871398410}
2025-07-25 12:20:05 | INFO | 收到表情消息: 消息ID:1374786107 来自:55878994168@chatroom 发送人:wxid_rwfb9vuy93jn22 @:[] 内容:[吃瓜]
2025-07-25 12:20:05 | DEBUG | [DoubaoImageRecognition] 缓存表情消息: 6323552950527484279
2025-07-25 12:20:05 | DEBUG | 收到消息: {'MsgId': 319548033, 'FromUserName': {'string': '48097389945@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 1, 'Content': {'string': 'xiaomaochong:\n我的只能自己回复'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1753417228, 'MsgSource': '<msgsource>\n\t<pua>1</pua>\n\t<silence>0</silence>\n\t<membercount>63</membercount>\n\t<signature>N0_V1_eWukPVrY|v1_qNTd6o8r</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'PushContent': '小爱 : 我的只能自己回复', 'NewMsgId': 6489552111952934376, 'MsgSeq': 871398411}
2025-07-25 12:20:05 | INFO | 收到文本消息: 消息ID:319548033 来自:48097389945@chatroom 发送人:xiaomaochong @:[] 内容:我的只能自己回复
2025-07-25 12:20:06 | DEBUG | 处理消息内容: '我的只能自己回复'
2025-07-25 12:20:06 | DEBUG | 消息内容 '我的只能自己回复' 不匹配任何命令，忽略
2025-07-25 12:20:10 | DEBUG | 收到消息: {'MsgId': 1813541468, 'FromUserName': {'string': '48097389945@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 1, 'Content': {'string': 'xiaomaochong:\n小爱现在几点'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1753417232, 'MsgSource': '<msgsource>\n\t<pua>1</pua>\n\t<silence>0</silence>\n\t<membercount>63</membercount>\n\t<signature>N0_V1_CsTK9yzR|v1_Qyt/lcNE</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'PushContent': '小爱 : 小爱现在几点', 'NewMsgId': 923600609331548020, 'MsgSeq': 871398412}
2025-07-25 12:20:10 | INFO | 收到文本消息: 消息ID:1813541468 来自:48097389945@chatroom 发送人:xiaomaochong @:[] 内容:小爱现在几点
2025-07-25 12:20:10 | DEBUG | 处理消息内容: '小爱现在几点'
2025-07-25 12:20:10 | DEBUG | 消息内容 '小爱现在几点' 不匹配任何命令，忽略
2025-07-25 12:20:12 | DEBUG | 收到消息: {'MsgId': 590001276, 'FromUserName': {'string': '55878994168@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 1, 'Content': {'string': 'wxid_ubbh6q832tcs21:\n走'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1753417234, 'MsgSource': '<msgsource>\n\t<pua>1</pua>\n\t<eggIncluded>1</eggIncluded>\n\t<silence>0</silence>\n\t<membercount>3</membercount>\n\t<signature>N0_V1_RvHJnTzK|v1_OHtZOXMx</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'PushContent': '郭 : 走', 'NewMsgId': 4375261700100508247, 'MsgSeq': 871398413}
2025-07-25 12:20:12 | INFO | 收到文本消息: 消息ID:590001276 来自:55878994168@chatroom 发送人:wxid_ubbh6q832tcs21 @:[] 内容:走
2025-07-25 12:20:13 | INFO | 发送表情消息: 对方wxid:55878994168@chatroom md5:479d9683141301494753f07fd93dff19 总长度:9992069
2025-07-25 12:20:13 | DEBUG | 处理消息内容: '走'
2025-07-25 12:20:13 | DEBUG | 消息内容 '走' 不匹配任何命令，忽略
2025-07-25 12:20:18 | DEBUG | 收到消息: {'MsgId': 773051247, 'FromUserName': {'string': '48097389945@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 47, 'Content': {'string': 'xiaomaochong:\n<msg><emoji fromusername="xiaomaochong" tousername="48097389945@chatroom" type="2" idbuffer="media:0_0" md5="fe3cd0728ea249c42967eca1a923ca19" len="1150760" productid="" androidmd5="fe3cd0728ea249c42967eca1a923ca19" androidlen="1150760" s60v3md5="fe3cd0728ea249c42967eca1a923ca19" s60v3len="1150760" s60v5md5="fe3cd0728ea249c42967eca1a923ca19" s60v5len="1150760" cdnurl="http://vweixinf.tc.qq.com/110/20401/stodownload?m=fe3cd0728ea249c42967eca1a923ca19&amp;filekey=30440201010430302e02016e0402535a042066653363643037323865613234396334323936376563613161393233636131390203118f28040d00000004627466730000000132&amp;hy=SZ&amp;storeid=26858df95000dcfdc7103394e0000006e01004fb1535a0936fbc1e687df469&amp;ef=1&amp;bizid=1022" designerid="" thumburl="" encrypturl="http://vweixinf.tc.qq.com/110/20402/stodownload?m=6d785e3b755e8221d8aad4d2c8a19e23&amp;filekey=30440201010430302e02016e0402535a042036643738356533623735356538323231643861616434643263386131396532330203118f30040d00000004627466730000000132&amp;hy=SZ&amp;storeid=26858df96000068647103394e0000006e02004fb2535a0936fbc1e687df488&amp;ef=2&amp;bizid=1022" aeskey="e0490f083cea41c788c8a2ed7b667733" externurl="http://vweixinf.tc.qq.com/110/20403/stodownload?m=7de25068710123dc65a4010ac6cea00c&amp;filekey=30440201010430302e02016e0402535a042037646532353036383731303132336463363561343031306163366365613030630203016060040d00000004627466730000000132&amp;hy=SZ&amp;storeid=26858df96000200e27103394e0000006e03004fb3535a0936fbc1e687df4a5&amp;ef=3&amp;bizid=1022" externmd5="a37906648cde2858dc252fd8ebd0af7d" width="400" height="400" tpurl="" tpauthkey="" attachedtext="" attachedtextcolor="" lensid="" emojiattr="" linkid="" desc=""></emoji></msg>'}, 'Status': 3, 'ImgStatus': 2, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1753417240, 'MsgSource': '<msgsource>\n\t<silence>0</silence>\n\t<membercount>63</membercount>\n\t<signature>N0_V1_j+urCm+7|v1_1gSemQaE</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'PushContent': '小爱在群聊中发了一个表情', 'NewMsgId': 1160690056071643760, 'MsgSeq': 871398416}
2025-07-25 12:20:18 | INFO | 收到表情消息: 消息ID:773051247 来自:48097389945@chatroom 发送人:xiaomaochong MD5:fe3cd0728ea249c42967eca1a923ca19 大小:1150760
2025-07-25 12:20:18 | DEBUG | [DoubaoImageRecognition] 缓存表情消息: 1160690056071643760
2025-07-25 12:20:21 | DEBUG | 收到消息: {'MsgId': 761419870, 'FromUserName': {'string': '48097389945@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 34, 'Content': {'string': 'xiaomaochong:\n<msg><voicemsg endflag="1" cancelflag="0" forwardflag="0" voiceformat="4" voicelength="10000" length="29978" bufid="0" aeskey="6d7768627a66716b6a726a646f6d7376" voiceurl="3052020100044b304902010002049363814102033d14ba02042a31949d02046883061c042435663036326638662d376364302d346334362d393037322d30633933333330306265386402040528000f02010004001dc74187" voicemd5="cc60817a7d3117ba913055ff348f9f9e" clientmsgid="494cd5716bb4ce7e67f76aaff6aa3e5648097389945@chatroom_47748_1753417243" fromusername="xiaomaochong" /></msg>'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1753417244, 'MsgSource': '<msgsource>\n\t<silence>0</silence>\n\t<membercount>63</membercount>\n\t<signature>N0_V1_GEZl95Dk|v1_+OoC9SzS</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'PushContent': '小爱在群聊中发了一段语音', 'NewMsgId': 6015024257807809620, 'MsgSeq': 871398417}
2025-07-25 12:20:21 | INFO | 收到语音消息: 消息ID:761419870 来自:48097389945@chatroom 发送人:xiaomaochong XML:
<msg><voicemsg endflag="1" cancelflag="0" forwardflag="0" voiceformat="4" voicelength="10000" length="29978" bufid="0" aeskey="6d7768627a66716b6a726a646f6d7376" voiceurl="3052020100044b304902010002049363814102033d14ba02042a31949d02046883061c042435663036326638662d376364302d346334362d393037322d30633933333330306265386402040528000f02010004001dc74187" voicemd5="cc60817a7d3117ba913055ff348f9f9e" clientmsgid="494cd5716bb4ce7e67f76aaff6aa3e5648097389945@chatroom_47748_1753417243" fromusername="xiaomaochong" /></msg>
2025-07-25 12:20:26 | DEBUG | 收到消息: {'MsgId': 681201919, 'FromUserName': {'string': '48097389945@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 47, 'Content': {'string': 'wxid_wlnzvr8ivgd422:\n<msg><emoji fromusername = "wxid_wlnzvr8ivgd422" tousername = "48097389945@chatroom" type="2" idbuffer="media:0_0" md5="403c690725b3e4400f25e5eb4ad8d1a6" len = "144487" productid="com.tencent.xin.emoticon.person.stiker_1750830993d5d9116eac8f8fdd" androidmd5="403c690725b3e4400f25e5eb4ad8d1a6" androidlen="144487" s60v3md5 = "403c690725b3e4400f25e5eb4ad8d1a6" s60v3len="144487" s60v5md5 = "403c690725b3e4400f25e5eb4ad8d1a6" s60v5len="144487" cdnurl = "http://wxapp.tc.qq.com/275/20304/stodownload?m=403c690725b3e4400f25e5eb4ad8d1a6&amp;filekey=30350201010421301f02020113040253480410403c690725b3e4400f25e5eb4ad8d1a60203023467040d00000004627466730000000132&amp;hy=SH&amp;storeid=2685b7cfe00008d65b41db9ce0000011300004f505348067c217156873c4ab&amp;bizid=1023" designerid = "" thumburl = "http://wxapp.tc.qq.com/275/20304/stodownload?m=c3a818da393ffe9402cf0ffc5b9cfdc7&amp;filekey=30340201010420301e02020113040253480410c3a818da393ffe9402cf0ffc5b9cfdc702025b06040d00000004627466730000000132&amp;hy=SH&amp;storeid=2685b8f540008f233b41db9ce0000011300004f50534822d321f1573f8160a&amp;bizid=1023" encrypturl = "http://wxapp.tc.qq.com/262/20304/stodownload?m=f9aea8131db635c70cd197aeb8c98887&amp;filekey=30350201010421301f02020106040253480410f9aea8131db635c70cd197aeb8c988870203023470040d00000004627466730000000132&amp;hy=SH&amp;storeid=2685cd7c900085bd1b41db9ce0000010600004f5053482bba3171579fd07e8&amp;bizid=1023" aeskey= "b32990995adede72ff23437171505406" externurl = "http://wxapp.tc.qq.com/262/20304/stodownload?m=3adef9347a027484bfc94db1da6dbd20&amp;filekey=30340201010420301e020201060402534804103adef9347a027484bfc94db1da6dbd2002023000040d00000004627466730000000132&amp;hy=SH&amp;storeid=2685cd7f100013492efd668680000010600004f50534816d8abc1e68827ab1&amp;bizid=1023" externmd5 = "8e864596aa4fba7cd3d8d8869a55a61f" width= "240" height= "240" tpurl= "" tpauthkey= "" attachedtext= "" attachedtextcolor= "" lensid= "" emojiattr= "" linkid= "" desc= "ChEKB2RlZmF1bHQSBuaRh+aRhg==" ></emoji>  </msg>'}, 'Status': 3, 'ImgStatus': 2, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1753417248, 'MsgSource': '<msgsource>\n\t<silence>0</silence>\n\t<membercount>63</membercount>\n\t<signature>N0_V1_evgQQhrS|v1_5Xv97rRH</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'PushContent': '锦岚（开玩笑就退群版）在群聊中发了一个表情', 'NewMsgId': 4182962433627665045, 'MsgSeq': 871398418}
2025-07-25 12:20:26 | INFO | 收到表情消息: 消息ID:681201919 来自:48097389945@chatroom 发送人:wxid_wlnzvr8ivgd422 MD5:403c690725b3e4400f25e5eb4ad8d1a6 大小:144487
2025-07-25 12:20:26 | DEBUG | [DoubaoImageRecognition] 缓存表情消息: 4182962433627665045
2025-07-25 12:20:56 | DEBUG | 收到消息: {'MsgId': 455050342, 'FromUserName': {'string': '48097389945@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 1, 'Content': {'string': 'wxid_ubbh6q832tcs21:\n打'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1753417278, 'MsgSource': '<msgsource>\n\t<pua>1</pua>\n\t<eggIncluded>1</eggIncluded>\n\t<silence>0</silence>\n\t<membercount>63</membercount>\n\t<signature>N0_V1_ea4JXzoz|v1_qUSWwX2M</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'PushContent': '郭 : 打', 'NewMsgId': 1949154985966399501, 'MsgSeq': 871398419}
2025-07-25 12:20:56 | INFO | 收到文本消息: 消息ID:455050342 来自:48097389945@chatroom 发送人:wxid_ubbh6q832tcs21 @:[] 内容:打
2025-07-25 12:20:57 | INFO | 发送表情消息: 对方wxid:48097389945@chatroom md5:fe3cd0728ea249c42967eca1a923ca19 总长度:9992069
2025-07-25 12:20:57 | DEBUG | 处理消息内容: '打'
2025-07-25 12:20:57 | DEBUG | 消息内容 '打' 不匹配任何命令，忽略
2025-07-25 12:20:59 | DEBUG | 收到消息: {'MsgId': 888896313, 'FromUserName': {'string': '48097389945@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 47, 'Content': {'string': 'wxid_lneb7n23o4lg12:\n<msg><emoji fromusername = "wxid_lneb7n23o4lg12" tousername = "48097389945@chatroom" type="2" idbuffer="media:0_0" md5="bd2e93ed3a82565aeff8b88f55c5dd2d" len = "545449" productid="" androidmd5="bd2e93ed3a82565aeff8b88f55c5dd2d" androidlen="545449" s60v3md5 = "bd2e93ed3a82565aeff8b88f55c5dd2d" s60v3len="545449" s60v5md5 = "bd2e93ed3a82565aeff8b88f55c5dd2d" s60v5len="545449" cdnurl = "http://vweixinf.tc.qq.com/110/20401/stodownload?m=bd2e93ed3a82565aeff8b88f55c5dd2d&amp;filekey=30440201010430302e02016e040253480420626432653933656433613832353635616566663862383866353563356464326402030852a9040d00000004627466730000000132&amp;hy=SH&amp;storeid=268830641000ae6ed7d2d13e40000006e01004fb1534812e9f17156899b124&amp;ef=1&amp;bizid=1022" designerid = "" thumburl = "" encrypturl = "http://vweixinf.tc.qq.com/110/20402/stodownload?m=5057fab18ea8329120be4f4088442568&amp;filekey=30440201010430302e02016e040253480420353035376661623138656138333239313230626534663430383834343235363802030852b0040d00000004627466730000000132&amp;hy=SH&amp;storeid=268830641000c4f737d2d13e40000006e02004fb2534812e9f17156899b13d&amp;ef=2&amp;bizid=1022" aeskey= "4889a54ba22c4c04af367bd651b182b7" externurl = "http://vweixinf.tc.qq.com/110/20403/stodownload?m=22aedf992be940b720eafa39698914de&amp;filekey=30440201010430302e02016e040253480420323261656466393932626539343062373230656166613339363938393134646502030247d0040d00000004627466730000000132&amp;hy=SH&amp;storeid=268830641000da1897d2d13e40000006e03004fb3534812e9f17156899b15a&amp;ef=3&amp;bizid=1022" externmd5 = "ed5ec00c5674be01b7875c7189d941c5" width= "240" height= "240" tpurl= "" tpauthkey= "" attachedtext= "" attachedtextcolor= "" lensid= "" emojiattr= "" linkid= "" desc= "" ></emoji>  </msg>'}, 'Status': 3, 'ImgStatus': 2, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1753417282, 'MsgSource': '<msgsource>\n\t<silence>0</silence>\n\t<membercount>63</membercount>\n\t<signature>N0_V1_YQOuAtL5|v1_L+PpHWfl</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'PushContent': 'ᡣ在群聊中发了一个表情', 'NewMsgId': 4937422001927168060, 'MsgSeq': 871398422}
2025-07-25 12:20:59 | INFO | 收到表情消息: 消息ID:888896313 来自:48097389945@chatroom 发送人:wxid_lneb7n23o4lg12 MD5:bd2e93ed3a82565aeff8b88f55c5dd2d 大小:545449
2025-07-25 12:21:00 | DEBUG | [DoubaoImageRecognition] 缓存表情消息: 4937422001927168060
2025-07-25 12:21:05 | DEBUG | 收到消息: {'MsgId': 173629339, 'FromUserName': {'string': '48097389945@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 47, 'Content': {'string': 'xiaomaochong:\n<msg><emoji fromusername="xiaomaochong" tousername="48097389945@chatroom" type="2" idbuffer="media:0_0" md5="bd2e93ed3a82565aeff8b88f55c5dd2d" len="545449" productid="" androidmd5="bd2e93ed3a82565aeff8b88f55c5dd2d" androidlen="545449" s60v3md5="bd2e93ed3a82565aeff8b88f55c5dd2d" s60v3len="545449" s60v5md5="bd2e93ed3a82565aeff8b88f55c5dd2d" s60v5len="545449" cdnurl="http://vweixinf.tc.qq.com/110/20401/stodownload?m=bd2e93ed3a82565aeff8b88f55c5dd2d&amp;filekey=30440201010430302e02016e040253480420626432653933656433613832353635616566663862383866353563356464326402030852a9040d00000004627466730000000132&amp;hy=SH&amp;storeid=268830641000ae6ed7d2d13e40000006e01004fb1534812e9f17156899b124&amp;ef=1&amp;bizid=1022" designerid="" thumburl="" encrypturl="http://vweixinf.tc.qq.com/110/20402/stodownload?m=5057fab18ea8329120be4f4088442568&amp;filekey=30440201010430302e02016e040253480420353035376661623138656138333239313230626534663430383834343235363802030852b0040d00000004627466730000000132&amp;hy=SH&amp;storeid=268830641000c4f737d2d13e40000006e02004fb2534812e9f17156899b13d&amp;ef=2&amp;bizid=1022" aeskey="4889a54ba22c4c04af367bd651b182b7" externurl="http://vweixinf.tc.qq.com/110/20403/stodownload?m=22aedf992be940b720eafa39698914de&amp;filekey=30440201010430302e02016e040253480420323261656466393932626539343062373230656166613339363938393134646502030247d0040d00000004627466730000000132&amp;hy=SH&amp;storeid=268830641000da1897d2d13e40000006e03004fb3534812e9f17156899b15a&amp;ef=3&amp;bizid=1022" externmd5="ed5ec00c5674be01b7875c7189d941c5" width="240" height="240" tpurl="" tpauthkey="" attachedtext="" attachedtextcolor="" lensid="" emojiattr="" linkid="" desc=""></emoji></msg>'}, 'Status': 3, 'ImgStatus': 2, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1753417288, 'MsgSource': '<msgsource>\n\t<silence>0</silence>\n\t<membercount>63</membercount>\n\t<signature>N0_V1_Dx7GoEih|v1_MRP1wxQg</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'PushContent': '小爱在群聊中发了一个表情', 'NewMsgId': 5590246934350752717, 'MsgSeq': 871398423}
2025-07-25 12:21:05 | INFO | 收到表情消息: 消息ID:173629339 来自:48097389945@chatroom 发送人:xiaomaochong MD5:bd2e93ed3a82565aeff8b88f55c5dd2d 大小:545449
2025-07-25 12:21:06 | DEBUG | [DoubaoImageRecognition] 缓存表情消息: 5590246934350752717
2025-07-25 12:21:17 | DEBUG | 收到消息: {'MsgId': 1242001698, 'FromUserName': {'string': '48097389945@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 1, 'Content': {'string': 'wxid_ubbh6q832tcs21:\n打'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1753417300, 'MsgSource': '<msgsource>\n\t<pua>1</pua>\n\t<eggIncluded>1</eggIncluded>\n\t<silence>0</silence>\n\t<membercount>63</membercount>\n\t<signature>N0_V1_yxzkhJFX|v1_wYz2kyoS</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'PushContent': '郭 : 打', 'NewMsgId': 2647135487250931153, 'MsgSeq': 871398424}
2025-07-25 12:21:17 | INFO | 收到文本消息: 消息ID:1242001698 来自:48097389945@chatroom 发送人:wxid_ubbh6q832tcs21 @:[] 内容:打
2025-07-25 12:21:18 | INFO | 发送表情消息: 对方wxid:48097389945@chatroom md5:fe3cd0728ea249c42967eca1a923ca19 总长度:9992069
2025-07-25 12:21:18 | DEBUG | 处理消息内容: '打'
2025-07-25 12:21:18 | DEBUG | 消息内容 '打' 不匹配任何命令，忽略
2025-07-25 12:21:20 | DEBUG | 收到消息: {'MsgId': 1614701812, 'FromUserName': {'string': '48097389945@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 1, 'Content': {'string': 'wxid_ubbh6q832tcs21:\n打'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1753417302, 'MsgSource': '<msgsource>\n\t<pua>1</pua>\n\t<eggIncluded>1</eggIncluded>\n\t<silence>0</silence>\n\t<membercount>63</membercount>\n\t<signature>N0_V1_eROZxuJE|v1_J6E7FKIs</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'PushContent': '郭 : 打', 'NewMsgId': 3491149957120252189, 'MsgSeq': 871398427}
2025-07-25 12:21:20 | INFO | 收到文本消息: 消息ID:1614701812 来自:48097389945@chatroom 发送人:wxid_ubbh6q832tcs21 @:[] 内容:打
2025-07-25 12:21:20 | INFO | 发送表情消息: 对方wxid:48097389945@chatroom md5:fe3cd0728ea249c42967eca1a923ca19 总长度:9992069
2025-07-25 12:21:20 | DEBUG | 处理消息内容: '打'
2025-07-25 12:21:20 | DEBUG | 消息内容 '打' 不匹配任何命令，忽略
2025-07-25 12:21:21 | DEBUG | 收到消息: {'MsgId': 1971461057, 'FromUserName': {'string': '48097389945@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 47, 'Content': {'string': 'wxid_lneb7n23o4lg12:\n<msg><emoji fromusername = "wxid_lneb7n23o4lg12" tousername = "48097389945@chatroom" type="2" idbuffer="media:0_0" md5="6fbc722e86b083bf724c5d5195710515" len = "545449" productid="" androidmd5="6fbc722e86b083bf724c5d5195710515" androidlen="545449" s60v3md5 = "6fbc722e86b083bf724c5d5195710515" s60v3len="545449" s60v5md5 = "6fbc722e86b083bf724c5d5195710515" s60v5len="545449" cdnurl = "http://vweixinf.tc.qq.com/110/20401/stodownload?m=6fbc722e86b083bf724c5d5195710515&amp;filekey=30440201010430302e02016e040253480420366662633732326538366230383362663732346335643531393537313035313502030852a9040d00000004627466730000000132&amp;hy=SH&amp;storeid=268830656000d60ea7d2d13e40000006e01004fb1534807860b01e6952f8a3&amp;ef=1&amp;bizid=1022" designerid = "" thumburl = "" encrypturl = "http://vweixinf.tc.qq.com/110/20402/stodownload?m=f43d04a7cb9ca226e6a6485b2e9e4e4b&amp;filekey=30440201010430302e02016e040253480420663433643034613763623963613232366536613634383562326539653465346202030852b0040d00000004627466730000000132&amp;hy=SH&amp;storeid=268830657000071207d2d13e40000006e02004fb2534807860b01e6952f8ce&amp;ef=2&amp;bizid=1022" aeskey= "a7dbd5c0aaa04700b452e6a701ca0af7" externurl = "http://vweixinf.tc.qq.com/110/20403/stodownload?m=0a1acf8d1858351691bdfefd56ca0346&amp;filekey=30440201010430302e02016e04025348042030613161636638643138353833353136393162646665666435366361303334360203024790040d00000004627466730000000132&amp;hy=SH&amp;storeid=2688306570002a29d7d2d13e40000006e03004fb3534807860b01e6952f8ea&amp;ef=3&amp;bizid=1022" externmd5 = "0031451175f5afad87d486a68f1d33e4" width= "240" height= "240" tpurl= "" tpauthkey= "" attachedtext= "" attachedtextcolor= "" lensid= "" emojiattr= "" linkid= "" desc= "" ></emoji>  </msg>'}, 'Status': 3, 'ImgStatus': 2, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1753417303, 'MsgSource': '<msgsource>\n\t<silence>0</silence>\n\t<membercount>63</membercount>\n\t<signature>N0_V1_rE4p5xhj|v1_+buks0f+</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'PushContent': 'ᡣ在群聊中发了一个表情', 'NewMsgId': 5168791257693719427, 'MsgSeq': 871398430}
2025-07-25 12:21:21 | INFO | 收到表情消息: 消息ID:1971461057 来自:48097389945@chatroom 发送人:wxid_lneb7n23o4lg12 MD5:6fbc722e86b083bf724c5d5195710515 大小:545449
2025-07-25 12:21:21 | DEBUG | [DoubaoImageRecognition] 缓存表情消息: 5168791257693719427
2025-07-25 12:21:23 | DEBUG | 收到消息: {'MsgId': 671326507, 'FromUserName': {'string': '48097389945@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 47, 'Content': {'string': 'wxid_lneb7n23o4lg12:\n<msg><emoji fromusername = "wxid_lneb7n23o4lg12" tousername = "48097389945@chatroom" type="2" idbuffer="media:0_0" md5="7ae480fc31acb2976063c4843756a7ff" len = "545449" productid="" androidmd5="7ae480fc31acb2976063c4843756a7ff" androidlen="545449" s60v3md5 = "7ae480fc31acb2976063c4843756a7ff" s60v3len="545449" s60v5md5 = "7ae480fc31acb2976063c4843756a7ff" s60v5len="545449" cdnurl = "http://vweixinf.tc.qq.com/110/20401/stodownload?m=7ae480fc31acb2976063c4843756a7ff&amp;filekey=30440201010430302e02016e040253480420376165343830666333316163623239373630363363343834333735366137666602030852a9040d00000004627466730000000132&amp;hy=SH&amp;storeid=2688306590002e1917d2d13e40000006e01004fb153482fd9e1715689993d9&amp;ef=1&amp;bizid=1022" designerid = "" thumburl = "" encrypturl = "http://vweixinf.tc.qq.com/110/20402/stodownload?m=f6e9b973d29efb0a250971df963352af&amp;filekey=30440201010430302e02016e040253480420663665396239373364323965666230613235303937316466393633333532616602030852b0040d00000004627466730000000132&amp;hy=SH&amp;storeid=2688306590004e5567d2d13e40000006e02004fb253482fd9e1715689993f4&amp;ef=2&amp;bizid=1022" aeskey= "347f299152e14a67b093e3fc5da3f862" externurl = "http://vweixinf.tc.qq.com/110/20403/stodownload?m=e4e9a29e7e6dc34729d5f4a53176ef11&amp;filekey=30440201010430302e02016e040253480420653465396132396537653664633334373239643566346135333137366566313102030247d0040d00000004627466730000000132&amp;hy=SH&amp;storeid=268830659000676d47d2d13e40000006e03004fb353482fd9e171568999405&amp;ef=3&amp;bizid=1022" externmd5 = "d1d5e1111f946e82c60dc4a071678f3c" width= "240" height= "240" tpurl= "" tpauthkey= "" attachedtext= "" attachedtextcolor= "" lensid= "" emojiattr= "" linkid= "" desc= "" ></emoji>  </msg>'}, 'Status': 3, 'ImgStatus': 2, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1753417305, 'MsgSource': '<msgsource>\n\t<silence>0</silence>\n\t<membercount>63</membercount>\n\t<signature>N0_V1_zDemQxMh|v1_8t7V3IMy</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'PushContent': 'ᡣ在群聊中发了一个表情', 'NewMsgId': 1551521815176647888, 'MsgSeq': 871398431}
2025-07-25 12:21:23 | INFO | 收到表情消息: 消息ID:671326507 来自:48097389945@chatroom 发送人:wxid_lneb7n23o4lg12 MD5:7ae480fc31acb2976063c4843756a7ff 大小:545449
2025-07-25 12:21:23 | DEBUG | [DoubaoImageRecognition] 缓存表情消息: 1551521815176647888
2025-07-25 12:21:25 | DEBUG | 收到消息: {'MsgId': 371973270, 'FromUserName': {'string': '48097389945@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 1, 'Content': {'string': 'wxid_ubbh6q832tcs21:\n打'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1753417307, 'MsgSource': '<msgsource>\n\t<pua>1</pua>\n\t<eggIncluded>1</eggIncluded>\n\t<silence>0</silence>\n\t<membercount>63</membercount>\n\t<signature>N0_V1_i1D6buvm|v1_O4i7hVKV</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'PushContent': '郭 : 打', 'NewMsgId': 2959849578219983136, 'MsgSeq': 871398432}
2025-07-25 12:21:25 | INFO | 收到文本消息: 消息ID:371973270 来自:48097389945@chatroom 发送人:wxid_ubbh6q832tcs21 @:[] 内容:打
2025-07-25 12:21:26 | INFO | 发送表情消息: 对方wxid:48097389945@chatroom md5:fe3cd0728ea249c42967eca1a923ca19 总长度:9992069
2025-07-25 12:21:26 | DEBUG | 处理消息内容: '打'
2025-07-25 12:21:26 | DEBUG | 消息内容 '打' 不匹配任何命令，忽略
2025-07-25 12:21:26 | DEBUG | 收到消息: {'MsgId': 222008614, 'FromUserName': {'string': '48097389945@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 1, 'Content': {'string': 'wxid_ubbh6q832tcs21:\n打'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1753417309, 'MsgSource': '<msgsource>\n\t<pua>1</pua>\n\t<eggIncluded>1</eggIncluded>\n\t<silence>0</silence>\n\t<membercount>63</membercount>\n\t<signature>N0_V1_Pz5hTJuZ|v1_El0lscUV</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'PushContent': '郭 : 打', 'NewMsgId': 3630118692062746976, 'MsgSeq': 871398435}
2025-07-25 12:21:26 | INFO | 收到文本消息: 消息ID:222008614 来自:48097389945@chatroom 发送人:wxid_ubbh6q832tcs21 @:[] 内容:打
2025-07-25 12:21:27 | INFO | 发送表情消息: 对方wxid:48097389945@chatroom md5:fe3cd0728ea249c42967eca1a923ca19 总长度:9992069
2025-07-25 12:21:27 | DEBUG | 处理消息内容: '打'
2025-07-25 12:21:27 | DEBUG | 消息内容 '打' 不匹配任何命令，忽略
2025-07-25 12:21:28 | DEBUG | 收到消息: {'MsgId': 169543229, 'FromUserName': {'string': '48097389945@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 1, 'Content': {'string': 'wxid_ubbh6q832tcs21:\n打'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1753417309, 'MsgSource': '<msgsource>\n\t<pua>1</pua>\n\t<eggIncluded>1</eggIncluded>\n\t<silence>0</silence>\n\t<membercount>63</membercount>\n\t<signature>N0_V1_Pz5hTJuZ|v1_El0lscUV</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'PushContent': '郭 : 打', 'NewMsgId': 6360198207820739546, 'MsgSeq': 871398436}
2025-07-25 12:21:28 | INFO | 收到文本消息: 消息ID:169543229 来自:48097389945@chatroom 发送人:wxid_ubbh6q832tcs21 @:[] 内容:打
2025-07-25 12:21:29 | INFO | 发送表情消息: 对方wxid:48097389945@chatroom md5:fe3cd0728ea249c42967eca1a923ca19 总长度:9992069
2025-07-25 12:21:29 | DEBUG | 处理消息内容: '打'
2025-07-25 12:21:29 | DEBUG | 消息内容 '打' 不匹配任何命令，忽略
2025-07-25 12:21:29 | DEBUG | 收到消息: {'MsgId': 970649677, 'FromUserName': {'string': '48097389945@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 47, 'Content': {'string': 'wxid_lneb7n23o4lg12:\n<msg><emoji fromusername = "wxid_lneb7n23o4lg12" tousername = "48097389945@chatroom" type="2" idbuffer="media:0_0" md5="c0bf2ad0efbbf88849524c83976bd25a" len = "545449" productid="" androidmd5="c0bf2ad0efbbf88849524c83976bd25a" androidlen="545449" s60v3md5 = "c0bf2ad0efbbf88849524c83976bd25a" s60v3len="545449" s60v5md5 = "c0bf2ad0efbbf88849524c83976bd25a" s60v5len="545449" cdnurl = "http://vweixinf.tc.qq.com/110/20401/stodownload?m=c0bf2ad0efbbf88849524c83976bd25a&amp;filekey=30440201010430302e02016e040253480420633062663261643065666262663838383439353234633833393736626432356102030852a9040d00000004627466730000000132&amp;hy=SH&amp;storeid=268564f2d000bfcf2005a663f0000006e01004fb153482f43c1f156eab2259&amp;ef=1&amp;bizid=1022" designerid = "" thumburl = "" encrypturl = "http://vweixinf.tc.qq.com/110/20402/stodownload?m=6071e4f43001658215f7e1b937f5ffa6&amp;filekey=30440201010430302e02016e040253480420363037316534663433303031363538323135663765316239333766356666613602030852b0040d00000004627466730000000132&amp;hy=SH&amp;storeid=268564f2d000d5f12005a663f0000006e02004fb253482f43c1f156eab2273&amp;ef=2&amp;bizid=1022" aeskey= "95a3375eda664eddb06f122639dd68ad" externurl = "http://vweixinf.tc.qq.com/110/20403/stodownload?m=638f4ae816482404d6610cb59eb42334&amp;filekey=30440201010430302e02016e040253480420363338663461653831363438323430346436363130636235396562343233333402030247d0040d00000004627466730000000132&amp;hy=SH&amp;storeid=268564f2d000ecfcf005a663f0000006e03004fb353482f43c1f156eab2289&amp;ef=3&amp;bizid=1022" externmd5 = "3a29040ffe88b5f7ddbb188262eefd7b" width= "240" height= "240" tpurl= "" tpauthkey= "" attachedtext= "" attachedtextcolor= "" lensid= "" emojiattr= "" linkid= "" desc= "" ></emoji>  </msg>'}, 'Status': 3, 'ImgStatus': 2, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1753417309, 'MsgSource': '<msgsource>\n\t<silence>0</silence>\n\t<membercount>63</membercount>\n\t<signature>N0_V1_0m+Sq03N|v1_HdaCB7am</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'PushContent': 'ᡣ在群聊中发了一个表情', 'NewMsgId': 6545353503599908265, 'MsgSeq': 871398437}
2025-07-25 12:21:29 | INFO | 收到表情消息: 消息ID:970649677 来自:48097389945@chatroom 发送人:wxid_lneb7n23o4lg12 MD5:c0bf2ad0efbbf88849524c83976bd25a 大小:545449
2025-07-25 12:21:29 | DEBUG | [DoubaoImageRecognition] 缓存表情消息: 6545353503599908265
2025-07-25 12:21:30 | DEBUG | 收到消息: {'MsgId': 571247890, 'FromUserName': {'string': '48097389945@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 1, 'Content': {'string': 'wxid_ubbh6q832tcs21:\n打'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1753417309, 'MsgSource': '<msgsource>\n\t<pua>1</pua>\n\t<eggIncluded>1</eggIncluded>\n\t<silence>0</silence>\n\t<membercount>63</membercount>\n\t<signature>N0_V1_Pz5hTJuZ|v1_El0lscUV</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'PushContent': '郭 : 打', 'NewMsgId': 2374298367642574179, 'MsgSeq': 871398438}
2025-07-25 12:21:30 | INFO | 收到文本消息: 消息ID:571247890 来自:48097389945@chatroom 发送人:wxid_ubbh6q832tcs21 @:[] 内容:打
2025-07-25 12:21:30 | INFO | 发送表情消息: 对方wxid:48097389945@chatroom md5:fe3cd0728ea249c42967eca1a923ca19 总长度:9992069
2025-07-25 12:21:30 | DEBUG | 处理消息内容: '打'
2025-07-25 12:21:30 | DEBUG | 消息内容 '打' 不匹配任何命令，忽略
2025-07-25 12:21:31 | DEBUG | 收到消息: {'MsgId': 637984414, 'FromUserName': {'string': '48097389945@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 1, 'Content': {'string': 'wxid_ubbh6q832tcs21:\n打'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1753417310, 'MsgSource': '<msgsource>\n\t<pua>1</pua>\n\t<eggIncluded>1</eggIncluded>\n\t<silence>0</silence>\n\t<membercount>63</membercount>\n\t<signature>N0_V1_yXLvgG3f|v1_/Wnjj18v</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'PushContent': '郭 : 打', 'NewMsgId': 4954032793887537821, 'MsgSeq': 871398441}
2025-07-25 12:21:31 | INFO | 收到文本消息: 消息ID:637984414 来自:48097389945@chatroom 发送人:wxid_ubbh6q832tcs21 @:[] 内容:打
2025-07-25 12:21:32 | INFO | 发送表情消息: 对方wxid:48097389945@chatroom md5:fe3cd0728ea249c42967eca1a923ca19 总长度:9992069
2025-07-25 12:21:32 | DEBUG | 处理消息内容: '打'
2025-07-25 12:21:32 | DEBUG | 消息内容 '打' 不匹配任何命令，忽略
2025-07-25 12:21:32 | DEBUG | 收到消息: {'MsgId': 390374458, 'FromUserName': {'string': '48097389945@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 47, 'Content': {'string': 'wxid_lneb7n23o4lg12:\n<msg><emoji fromusername = "wxid_lneb7n23o4lg12" tousername = "48097389945@chatroom" type="2" idbuffer="media:0_0" md5="657390c8ea4880c2d985cec6b4c3ec92" len = "545449" productid="" androidmd5="657390c8ea4880c2d985cec6b4c3ec92" androidlen="545449" s60v3md5 = "657390c8ea4880c2d985cec6b4c3ec92" s60v3len="545449" s60v5md5 = "657390c8ea4880c2d985cec6b4c3ec92" s60v5len="545449" cdnurl = "http://vweixinf.tc.qq.com/110/20401/stodownload?m=657390c8ea4880c2d985cec6b4c3ec92&amp;filekey=30440201010430302e02016e040253480420363537333930633865613438383063326439383563656336623463336563393202030852a9040d00000004627466730000000132&amp;hy=SH&amp;storeid=2687f0e390002331f005a663f0000006e01004fb153482c187bc1e7148e2dd&amp;ef=1&amp;bizid=1022" designerid = "" thumburl = "" encrypturl = "http://vweixinf.tc.qq.com/110/20402/stodownload?m=ef013afe82c59b0ae5c9941a31c6af3b&amp;filekey=30440201010430302e02016e040253480420656630313361666538326335396230616535633939343161333163366166336202030852b0040d00000004627466730000000132&amp;hy=SH&amp;storeid=2687f0e390003c31b005a663f0000006e02004fb253482c187bc1e7148e304&amp;ef=2&amp;bizid=1022" aeskey= "64eae7b38e514b58943c151371fc4660" externurl = "http://vweixinf.tc.qq.com/110/20403/stodownload?m=18cec31eedd1624eee2f671ac32f30c1&amp;filekey=30440201010430302e02016e04025348042031386365633331656564643136323465656532663637316163333266333063310203024790040d00000004627466730000000132&amp;hy=SH&amp;storeid=2687f0e390004d864005a663f0000006e03004fb353482c187bc1e7148e313&amp;ef=3&amp;bizid=1022" externmd5 = "07ae13f2bd91d9472dff2606635145ed" width= "240" height= "240" tpurl= "" tpauthkey= "" attachedtext= "" attachedtextcolor= "" lensid= "" emojiattr= "" linkid= "" desc= "" ></emoji>  </msg>'}, 'Status': 3, 'ImgStatus': 2, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1753417310, 'MsgSource': '<msgsource>\n\t<silence>0</silence>\n\t<membercount>63</membercount>\n\t<signature>N0_V1_Nyh3DQlt|v1_BiEXgkGx</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'PushContent': 'ᡣ在群聊中发了一个表情', 'NewMsgId': 3496282642593324947, 'MsgSeq': 871398442}
2025-07-25 12:21:32 | INFO | 收到表情消息: 消息ID:390374458 来自:48097389945@chatroom 发送人:wxid_lneb7n23o4lg12 MD5:657390c8ea4880c2d985cec6b4c3ec92 大小:545449
2025-07-25 12:21:32 | DEBUG | [DoubaoImageRecognition] 缓存表情消息: 3496282642593324947
2025-07-25 12:21:33 | DEBUG | 收到消息: {'MsgId': 1390077656, 'FromUserName': {'string': '48097389945@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 1, 'Content': {'string': 'wxid_ubbh6q832tcs21:\n打'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1753417310, 'MsgSource': '<msgsource>\n\t<pua>1</pua>\n\t<eggIncluded>1</eggIncluded>\n\t<silence>0</silence>\n\t<membercount>63</membercount>\n\t<signature>N0_V1_yXLvgG3f|v1_/Wnjj18v</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'PushContent': '郭 : 打', 'NewMsgId': 5879831163733038233, 'MsgSeq': 871398443}
2025-07-25 12:21:33 | INFO | 收到文本消息: 消息ID:1390077656 来自:48097389945@chatroom 发送人:wxid_ubbh6q832tcs21 @:[] 内容:打
2025-07-25 12:21:33 | INFO | 发送表情消息: 对方wxid:48097389945@chatroom md5:fe3cd0728ea249c42967eca1a923ca19 总长度:9992069
2025-07-25 12:21:33 | DEBUG | 处理消息内容: '打'
2025-07-25 12:21:33 | DEBUG | 消息内容 '打' 不匹配任何命令，忽略
2025-07-25 12:21:34 | DEBUG | 收到消息: {'MsgId': 2125153604, 'FromUserName': {'string': '48097389945@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 1, 'Content': {'string': 'wxid_ubbh6q832tcs21:\n打'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1753417311, 'MsgSource': '<msgsource>\n\t<pua>1</pua>\n\t<eggIncluded>1</eggIncluded>\n\t<silence>0</silence>\n\t<membercount>63</membercount>\n\t<signature>N0_V1_eYqyw91F|v1_i21XPKdC</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'PushContent': '郭 : 打', 'NewMsgId': 1519958274918363565, 'MsgSeq': 871398444}
2025-07-25 12:21:34 | INFO | 收到文本消息: 消息ID:2125153604 来自:48097389945@chatroom 发送人:wxid_ubbh6q832tcs21 @:[] 内容:打
2025-07-25 12:21:35 | INFO | 发送表情消息: 对方wxid:48097389945@chatroom md5:fe3cd0728ea249c42967eca1a923ca19 总长度:9992069
2025-07-25 12:21:35 | DEBUG | 处理消息内容: '打'
2025-07-25 12:21:35 | DEBUG | 消息内容 '打' 不匹配任何命令，忽略
2025-07-25 12:21:35 | DEBUG | 收到消息: {'MsgId': 252890494, 'FromUserName': {'string': '48097389945@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 1, 'Content': {'string': 'wxid_ubbh6q832tcs21:\n打'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1753417311, 'MsgSource': '<msgsource>\n\t<pua>1</pua>\n\t<eggIncluded>1</eggIncluded>\n\t<silence>0</silence>\n\t<membercount>63</membercount>\n\t<signature>N0_V1_eYqyw91F|v1_i21XPKdC</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'PushContent': '郭 : 打', 'NewMsgId': 3345694208833760507, 'MsgSeq': 871398446}
2025-07-25 12:21:35 | INFO | 收到文本消息: 消息ID:252890494 来自:48097389945@chatroom 发送人:wxid_ubbh6q832tcs21 @:[] 内容:打
2025-07-25 12:21:36 | INFO | 发送表情消息: 对方wxid:48097389945@chatroom md5:fe3cd0728ea249c42967eca1a923ca19 总长度:9992069
2025-07-25 12:21:36 | DEBUG | 处理消息内容: '打'
2025-07-25 12:21:36 | DEBUG | 消息内容 '打' 不匹配任何命令，忽略
2025-07-25 12:21:36 | DEBUG | 收到消息: {'MsgId': 987300682, 'FromUserName': {'string': '48097389945@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 1, 'Content': {'string': 'wxid_ubbh6q832tcs21:\n打'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1753417312, 'MsgSource': '<msgsource>\n\t<pua>1</pua>\n\t<eggIncluded>1</eggIncluded>\n\t<silence>0</silence>\n\t<membercount>63</membercount>\n\t<signature>N0_V1_grfl+BQU|v1_JkWMcoBh</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'PushContent': '郭 : 打', 'NewMsgId': 3031097102069578380, 'MsgSeq': 871398448}
2025-07-25 12:21:36 | INFO | 收到文本消息: 消息ID:987300682 来自:48097389945@chatroom 发送人:wxid_ubbh6q832tcs21 @:[] 内容:打
2025-07-25 12:21:37 | INFO | 发送表情消息: 对方wxid:48097389945@chatroom md5:fe3cd0728ea249c42967eca1a923ca19 总长度:9992069
2025-07-25 12:21:37 | DEBUG | 处理消息内容: '打'
2025-07-25 12:21:37 | DEBUG | 消息内容 '打' 不匹配任何命令，忽略
2025-07-25 12:21:38 | DEBUG | 收到消息: {'MsgId': 1255224020, 'FromUserName': {'string': '48097389945@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 1, 'Content': {'string': 'wxid_ubbh6q832tcs21:\n打'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1753417312, 'MsgSource': '<msgsource>\n\t<pua>1</pua>\n\t<eggIncluded>1</eggIncluded>\n\t<silence>0</silence>\n\t<membercount>63</membercount>\n\t<signature>N0_V1_grfl+BQU|v1_JkWMcoBh</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'PushContent': '郭 : 打', 'NewMsgId': 1080513588009093683, 'MsgSeq': 871398449}
2025-07-25 12:21:38 | INFO | 收到文本消息: 消息ID:1255224020 来自:48097389945@chatroom 发送人:wxid_ubbh6q832tcs21 @:[] 内容:打
2025-07-25 12:21:39 | INFO | 发送表情消息: 对方wxid:48097389945@chatroom md5:fe3cd0728ea249c42967eca1a923ca19 总长度:9992069
2025-07-25 12:21:39 | DEBUG | 处理消息内容: '打'
2025-07-25 12:21:39 | DEBUG | 消息内容 '打' 不匹配任何命令，忽略
2025-07-25 12:21:39 | DEBUG | 收到消息: {'MsgId': 1147876645, 'FromUserName': {'string': '48097389945@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 47, 'Content': {'string': 'wxid_lneb7n23o4lg12:\n<msg><emoji fromusername = "wxid_lneb7n23o4lg12" tousername = "48097389945@chatroom" type="2" idbuffer="media:0_0" md5="8fadc55b4ddd6569d1cbe035f210bc92" len = "545449" productid="" androidmd5="8fadc55b4ddd6569d1cbe035f210bc92" androidlen="545449" s60v3md5 = "8fadc55b4ddd6569d1cbe035f210bc92" s60v3len="545449" s60v5md5 = "8fadc55b4ddd6569d1cbe035f210bc92" s60v5len="545449" cdnurl = "http://vweixinf.tc.qq.com/110/20401/stodownload?m=8fadc55b4ddd6569d1cbe035f210bc92&amp;filekey=30440201010430302e02016e040253480420386661646335356234646464363536396431636265303335663231306263393202030852a9040d00000004627466730000000132&amp;hy=SH&amp;storeid=26883065f000d09677d2d13e40000006e01004fb1534811ddf1715688bd919&amp;ef=1&amp;bizid=1022" designerid = "" thumburl = "" encrypturl = "http://vweixinf.tc.qq.com/110/20402/stodownload?m=fac413043d7152034c1974690b38eaab&amp;filekey=30440201010430302e02016e040253480420666163343133303433643731353230333463313937343639306233386561616202030852b0040d00000004627466730000000132&amp;hy=SH&amp;storeid=26883065f000e845e7d2d13e40000006e02004fb2534811ddf1715688bd933&amp;ef=2&amp;bizid=1022" aeskey= "14c0a0bcb70849b8bfd90623a1259187" externurl = "http://vweixinf.tc.qq.com/110/20403/stodownload?m=5edb59bfd88164547f4c39b7570b1308&amp;filekey=30440201010430302e02016e040253480420356564623539626664383831363435343766346333396237353730623133303802030247d0040d00000004627466730000000132&amp;hy=SH&amp;storeid=2688306600000cff17d2d13e40000006e03004fb3534811ddf1715688bd947&amp;ef=3&amp;bizid=1022" externmd5 = "d937884357e58e2ed3afd29bc2603949" width= "240" height= "240" tpurl= "" tpauthkey= "" attachedtext= "" attachedtextcolor= "" lensid= "" emojiattr= "" linkid= "" desc= "" ></emoji>  </msg>'}, 'Status': 3, 'ImgStatus': 2, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1753417312, 'MsgSource': '<msgsource>\n\t<silence>0</silence>\n\t<membercount>63</membercount>\n\t<signature>N0_V1_6ALFMBk4|v1_ltkVLe7R</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'PushContent': 'ᡣ在群聊中发了一个表情', 'NewMsgId': 6555483703317994974, 'MsgSeq': 871398450}
2025-07-25 12:21:39 | INFO | 收到表情消息: 消息ID:1147876645 来自:48097389945@chatroom 发送人:wxid_lneb7n23o4lg12 MD5:8fadc55b4ddd6569d1cbe035f210bc92 大小:545449
2025-07-25 12:21:39 | DEBUG | [DoubaoImageRecognition] 缓存表情消息: 6555483703317994974
2025-07-25 12:21:40 | DEBUG | 收到消息: {'MsgId': 1141896860, 'FromUserName': {'string': '48097389945@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 1, 'Content': {'string': 'wxid_ubbh6q832tcs21:\n打'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1753417312, 'MsgSource': '<msgsource>\n\t<pua>1</pua>\n\t<eggIncluded>1</eggIncluded>\n\t<silence>0</silence>\n\t<membercount>63</membercount>\n\t<signature>N0_V1_grfl+BQU|v1_JkWMcoBh</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'PushContent': '郭 : 打', 'NewMsgId': 7190553246734831852, 'MsgSeq': 871398451}
2025-07-25 12:21:40 | INFO | 收到文本消息: 消息ID:1141896860 来自:48097389945@chatroom 发送人:wxid_ubbh6q832tcs21 @:[] 内容:打
2025-07-25 12:21:41 | INFO | 发送表情消息: 对方wxid:48097389945@chatroom md5:fe3cd0728ea249c42967eca1a923ca19 总长度:9992069
2025-07-25 12:21:41 | DEBUG | 处理消息内容: '打'
2025-07-25 12:21:41 | DEBUG | 消息内容 '打' 不匹配任何命令，忽略
2025-07-25 12:21:41 | DEBUG | 收到消息: {'MsgId': 1154147903, 'FromUserName': {'string': '48097389945@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 47, 'Content': {'string': 'wxid_lneb7n23o4lg12:\n<msg><emoji fromusername = "wxid_lneb7n23o4lg12" tousername = "48097389945@chatroom" type="2" idbuffer="media:0_0" md5="85923a189ecfdeebb64943933c36df99" len = "545449" productid="" androidmd5="85923a189ecfdeebb64943933c36df99" androidlen="545449" s60v3md5 = "85923a189ecfdeebb64943933c36df99" s60v3len="545449" s60v5md5 = "85923a189ecfdeebb64943933c36df99" s60v5len="545449" cdnurl = "http://vweixinf.tc.qq.com/110/20401/stodownload?m=85923a189ecfdeebb64943933c36df99&amp;filekey=30440201010430302e02016e040253480420383539323361313839656366646565626236343934333933336333366466393902030852a9040d00000004627466730000000132&amp;hy=SH&amp;storeid=2687f653800097686005a663f0000006e01004fb1534803dee1b156edfd176&amp;ef=1&amp;bizid=1022" designerid = "" thumburl = "" encrypturl = "http://vweixinf.tc.qq.com/110/20402/stodownload?m=44fcfb716e508912ef0ea01d87751439&amp;filekey=30440201010430302e02016e040253480420343466636662373136653530383931326566306561303164383737353134333902030852b0040d00000004627466730000000132&amp;hy=SH&amp;storeid=2687f6538000b8530005a663f0000006e02004fb2534803dee1b156edfd18b&amp;ef=2&amp;bizid=1022" aeskey= "d6237749459f4f89a4e671de47b97000" externurl = "http://vweixinf.tc.qq.com/110/20403/stodownload?m=bbf3634a23d9e6bb71e272cfa50bd1f6&amp;filekey=30440201010430302e02016e04025348042062626633363334613233643965366262373165323732636661353062643166360203024790040d00000004627466730000000132&amp;hy=SH&amp;storeid=2687f6538000ca7f8005a663f0000006e03004fb3534803dee1b156edfd19a&amp;ef=3&amp;bizid=1022" externmd5 = "0031451175f5afad87d486a68f1d33e4" width= "240" height= "240" tpurl= "" tpauthkey= "" attachedtext= "" attachedtextcolor= "" lensid= "" emojiattr= "" linkid= "" desc= "" ></emoji>  </msg>'}, 'Status': 3, 'ImgStatus': 2, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1753417312, 'MsgSource': '<msgsource>\n\t<silence>0</silence>\n\t<membercount>63</membercount>\n\t<signature>N0_V1_B3k8mW3s|v1_MDEvz446</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'PushContent': 'ᡣ在群聊中发了一个表情', 'NewMsgId': 7629223470117163276, 'MsgSeq': 871398452}
2025-07-25 12:21:41 | INFO | 收到表情消息: 消息ID:1154147903 来自:48097389945@chatroom 发送人:wxid_lneb7n23o4lg12 MD5:85923a189ecfdeebb64943933c36df99 大小:545449
2025-07-25 12:21:41 | DEBUG | [DoubaoImageRecognition] 缓存表情消息: 7629223470117163276
2025-07-25 12:21:42 | DEBUG | 收到消息: {'MsgId': 2088122777, 'FromUserName': {'string': '48097389945@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 1, 'Content': {'string': 'wxid_ubbh6q832tcs21:\n打'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1753417312, 'MsgSource': '<msgsource>\n\t<pua>1</pua>\n\t<eggIncluded>1</eggIncluded>\n\t<silence>0</silence>\n\t<membercount>63</membercount>\n\t<signature>N0_V1_grfl+BQU|v1_JkWMcoBh</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'PushContent': '郭 : 打', 'NewMsgId': 8101064975619421076, 'MsgSeq': 871398453}
2025-07-25 12:21:42 | INFO | 收到文本消息: 消息ID:2088122777 来自:48097389945@chatroom 发送人:wxid_ubbh6q832tcs21 @:[] 内容:打
2025-07-25 12:21:42 | INFO | 发送表情消息: 对方wxid:48097389945@chatroom md5:fe3cd0728ea249c42967eca1a923ca19 总长度:9992069
2025-07-25 12:21:42 | DEBUG | 处理消息内容: '打'
2025-07-25 12:21:42 | DEBUG | 消息内容 '打' 不匹配任何命令，忽略
2025-07-25 12:21:43 | DEBUG | 收到消息: {'MsgId': 993350869, 'FromUserName': {'string': '48097389945@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 1, 'Content': {'string': 'wxid_ubbh6q832tcs21:\n打'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1753417313, 'MsgSource': '<msgsource>\n\t<pua>1</pua>\n\t<eggIncluded>1</eggIncluded>\n\t<silence>0</silence>\n\t<membercount>63</membercount>\n\t<signature>N0_V1_gXlfRcTz|v1_BIUagYyg</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'PushContent': '郭 : 打', 'NewMsgId': 886600030868902404, 'MsgSeq': 871398456}
2025-07-25 12:21:43 | INFO | 收到文本消息: 消息ID:993350869 来自:48097389945@chatroom 发送人:wxid_ubbh6q832tcs21 @:[] 内容:打
2025-07-25 12:21:44 | INFO | 发送表情消息: 对方wxid:48097389945@chatroom md5:fe3cd0728ea249c42967eca1a923ca19 总长度:9992069
2025-07-25 12:21:44 | DEBUG | 处理消息内容: '打'
2025-07-25 12:21:44 | DEBUG | 消息内容 '打' 不匹配任何命令，忽略
2025-07-25 12:21:44 | DEBUG | 收到消息: {'MsgId': 1944293897, 'FromUserName': {'string': '48097389945@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 1, 'Content': {'string': 'wxid_ubbh6q832tcs21:\n打'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1753417313, 'MsgSource': '<msgsource>\n\t<pua>1</pua>\n\t<eggIncluded>1</eggIncluded>\n\t<silence>0</silence>\n\t<membercount>63</membercount>\n\t<signature>N0_V1_gXlfRcTz|v1_BIUagYyg</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'PushContent': '郭 : 打', 'NewMsgId': 6623268341123179263, 'MsgSeq': 871398457}
2025-07-25 12:21:44 | INFO | 收到文本消息: 消息ID:1944293897 来自:48097389945@chatroom 发送人:wxid_ubbh6q832tcs21 @:[] 内容:打
2025-07-25 12:21:45 | INFO | 发送表情消息: 对方wxid:48097389945@chatroom md5:fe3cd0728ea249c42967eca1a923ca19 总长度:9992069
2025-07-25 12:21:45 | DEBUG | 处理消息内容: '打'
2025-07-25 12:21:45 | DEBUG | 消息内容 '打' 不匹配任何命令，忽略
2025-07-25 12:21:45 | DEBUG | 收到消息: {'MsgId': 303342839, 'FromUserName': {'string': '48097389945@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 47, 'Content': {'string': 'wxid_lneb7n23o4lg12:\n<msg><emoji fromusername = "wxid_lneb7n23o4lg12" tousername = "48097389945@chatroom" type="2" idbuffer="media:0_0" md5="4ed9bdf8ec8627c67aa12ffef62749f2" len = "545449" productid="" androidmd5="4ed9bdf8ec8627c67aa12ffef62749f2" androidlen="545449" s60v3md5 = "4ed9bdf8ec8627c67aa12ffef62749f2" s60v3len="545449" s60v5md5 = "4ed9bdf8ec8627c67aa12ffef62749f2" s60v5len="545449" cdnurl = "http://vweixinf.tc.qq.com/110/20401/stodownload?m=4ed9bdf8ec8627c67aa12ffef62749f2&amp;filekey=30440201010430302e02016e040253480420346564396264663865633836323763363761613132666665663632373439663202030852a9040d00000004627466730000000132&amp;hy=SH&amp;storeid=268830664000211b47d2d13e40000006e01004fb153480f0ee171568a3e8ae&amp;ef=1&amp;bizid=1022" designerid = "" thumburl = "" encrypturl = "http://vweixinf.tc.qq.com/110/20402/stodownload?m=21b60abccbf073b40551a943b34a6c53&amp;filekey=30440201010430302e02016e040253480420323162363061626363626630373362343035353161393433623334613663353302030852b0040d00000004627466730000000132&amp;hy=SH&amp;storeid=2688306640003e6c57d2d13e40000006e02004fb253480f0ee171568a3e8d7&amp;ef=2&amp;bizid=1022" aeskey= "a1f46f6db66a4498936b3dff6fb7d6df" externurl = "http://vweixinf.tc.qq.com/110/20403/stodownload?m=6c54ebe5b9eb5d2e419a299fcaeb4d14&amp;filekey=30440201010430302e02016e04025348042036633534656265356239656235643265343139613239396663616562346431340203024790040d00000004627466730000000132&amp;hy=SH&amp;storeid=2688306640005830d7d2d13e40000006e03004fb353480f0ee171568a3e901&amp;ef=3&amp;bizid=1022" externmd5 = "fe2d313d8ebb025b304fc443739218c7" width= "240" height= "240" tpurl= "" tpauthkey= "" attachedtext= "" attachedtextcolor= "" lensid= "" emojiattr= "" linkid= "" desc= "" ></emoji>  </msg>'}, 'Status': 3, 'ImgStatus': 2, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1753417316, 'MsgSource': '<msgsource>\n\t<silence>0</silence>\n\t<membercount>63</membercount>\n\t<signature>N0_V1_Jw11BO7m|v1_DjqIgG08</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'PushContent': 'ᡣ在群聊中发了一个表情', 'NewMsgId': 4251124551662529976, 'MsgSeq': 871398462}
2025-07-25 12:21:45 | INFO | 收到表情消息: 消息ID:303342839 来自:48097389945@chatroom 发送人:wxid_lneb7n23o4lg12 MD5:4ed9bdf8ec8627c67aa12ffef62749f2 大小:545449
2025-07-25 12:21:46 | DEBUG | [DoubaoImageRecognition] 缓存表情消息: 4251124551662529976
2025-07-25 12:21:46 | DEBUG | 收到消息: {'MsgId': 637132345, 'FromUserName': {'string': '48097389945@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 47, 'Content': {'string': 'wxid_lneb7n23o4lg12:\n<msg><emoji fromusername = "wxid_lneb7n23o4lg12" tousername = "48097389945@chatroom" type="2" idbuffer="media:0_0" md5="87053d513ffb78f076ea8e57bfaca074" len = "545449" productid="" androidmd5="87053d513ffb78f076ea8e57bfaca074" androidlen="545449" s60v3md5 = "87053d513ffb78f076ea8e57bfaca074" s60v3len="545449" s60v5md5 = "87053d513ffb78f076ea8e57bfaca074" s60v5len="545449" cdnurl = "http://vweixinf.tc.qq.com/110/20401/stodownload?m=87053d513ffb78f076ea8e57bfaca074&amp;filekey=30440201010430302e02016e040253480420383730353364353133666662373866303736656138653537626661636130373402030852a9040d00000004627466730000000132&amp;hy=SH&amp;storeid=268830665000ed2f87d2d13e40000006e01004fb153481d8ee1715689614c3&amp;ef=1&amp;bizid=1022" designerid = "" thumburl = "" encrypturl = "http://vweixinf.tc.qq.com/110/20402/stodownload?m=a59958afa575baf6ceed94f788ccc906&amp;filekey=30440201010430302e02016e040253480420613539393538616661353735626166366365656439346637383863636339303602030852b0040d00000004627466730000000132&amp;hy=SH&amp;storeid=268830666000153667d2d13e40000006e02004fb253481d8ee1715689614e1&amp;ef=2&amp;bizid=1022" aeskey= "0e13e5af194f4ce287cc98e69f8f77b3" externurl = "http://vweixinf.tc.qq.com/110/20403/stodownload?m=867c66cee22bd791faec14804b1cd344&amp;filekey=30440201010430302e02016e040253480420383637633636636565323262643739316661656331343830346231636433343402030247a0040d00000004627466730000000132&amp;hy=SH&amp;storeid=26883066600035b2a7d2d13e40000006e03004fb353481d8ee17156896150c&amp;ef=3&amp;bizid=1022" externmd5 = "b527253b62907cd37aeeec04132eb789" width= "240" height= "240" tpurl= "" tpauthkey= "" attachedtext= "" attachedtextcolor= "" lensid= "" emojiattr= "" linkid= "" desc= "" ></emoji>  </msg>'}, 'Status': 3, 'ImgStatus': 2, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1753417318, 'MsgSource': '<msgsource>\n\t<silence>0</silence>\n\t<membercount>63</membercount>\n\t<signature>N0_V1_/rqR2SAT|v1_2LLjoJer</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'PushContent': 'ᡣ在群聊中发了一个表情', 'NewMsgId': 4940233966941031372, 'MsgSeq': 871398465}
2025-07-25 12:21:46 | INFO | 收到表情消息: 消息ID:637132345 来自:48097389945@chatroom 发送人:wxid_lneb7n23o4lg12 MD5:87053d513ffb78f076ea8e57bfaca074 大小:545449
2025-07-25 12:21:46 | DEBUG | [DoubaoImageRecognition] 缓存表情消息: 4940233966941031372
2025-07-25 12:21:47 | DEBUG | 收到消息: {'MsgId': 670816997, 'FromUserName': {'string': '48097389945@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 47, 'Content': {'string': 'wxid_lneb7n23o4lg12:\n<msg><emoji fromusername = "wxid_lneb7n23o4lg12" tousername = "48097389945@chatroom" type="2" idbuffer="media:0_0" md5="550dea33a1aa016817c16c4ea0dc7ba7" len = "545449" productid="" androidmd5="550dea33a1aa016817c16c4ea0dc7ba7" androidlen="545449" s60v3md5 = "550dea33a1aa016817c16c4ea0dc7ba7" s60v3len="545449" s60v5md5 = "550dea33a1aa016817c16c4ea0dc7ba7" s60v5len="545449" cdnurl = "http://vweixinf.tc.qq.com/110/20401/stodownload?m=550dea33a1aa016817c16c4ea0dc7ba7&amp;filekey=30440201010430302e02016e040253480420353530646561333361316161303136383137633136633465613064633762613702030852a9040d00000004627466730000000132&amp;hy=SH&amp;storeid=2676de895000cb5607d2d13e40000006e01004fb153481e935b00b677c0387&amp;ef=1&amp;bizid=1022" designerid = "" thumburl = "" encrypturl = "http://vweixinf.tc.qq.com/110/20402/stodownload?m=2635bb458afd2fdea82627ca027cc3b5&amp;filekey=30440201010430302e02016e040253480420323633356262343538616664326664656138323632376361303237636333623502030852b0040d00000004627466730000000132&amp;hy=SH&amp;storeid=2676de895000e18da7d2d13e40000006e02004fb253481e935b00b677c0392&amp;ef=2&amp;bizid=1022" aeskey= "50dfdc76725f4fcaa1a78c07a7f62583" externurl = "http://vweixinf.tc.qq.com/110/20403/stodownload?m=d7252773db0bfbf411863d3ba5f7d59e&amp;filekey=30440201010430302e02016e04025348042064373235323737336462306266626634313138363364336261356637643539650203024790040d00000004627466730000000132&amp;hy=SH&amp;storeid=2676de89600006f997d2d13e40000006e03004fb353481e935b00b677c0399&amp;ef=3&amp;bizid=1022" externmd5 = "fe2d313d8ebb025b304fc443739218c7" width= "240" height= "240" tpurl= "" tpauthkey= "" attachedtext= "" attachedtextcolor= "" lensid= "" emojiattr= "" linkid= "" desc= "" ></emoji>  </msg>'}, 'Status': 3, 'ImgStatus': 2, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1753417319, 'MsgSource': '<msgsource>\n\t<silence>0</silence>\n\t<membercount>63</membercount>\n\t<signature>N0_V1_k+zH3xAG|v1_j4LPZw7/</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'PushContent': 'ᡣ在群聊中发了一个表情', 'NewMsgId': 4781938434861284132, 'MsgSeq': 871398468}
2025-07-25 12:21:47 | INFO | 收到表情消息: 消息ID:670816997 来自:48097389945@chatroom 发送人:wxid_lneb7n23o4lg12 MD5:550dea33a1aa016817c16c4ea0dc7ba7 大小:545449
2025-07-25 12:21:47 | DEBUG | [DoubaoImageRecognition] 缓存表情消息: 4781938434861284132
2025-07-25 12:21:47 | DEBUG | 收到消息: {'MsgId': 440523933, 'FromUserName': {'string': '48097389945@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 47, 'Content': {'string': 'wxid_lneb7n23o4lg12:\n<msg><emoji fromusername = "wxid_lneb7n23o4lg12" tousername = "48097389945@chatroom" type="2" idbuffer="media:0_0" md5="4f716b5523414fd4f460eee24afb2e37" len = "545449" productid="" androidmd5="4f716b5523414fd4f460eee24afb2e37" androidlen="545449" s60v3md5 = "4f716b5523414fd4f460eee24afb2e37" s60v3len="545449" s60v5md5 = "4f716b5523414fd4f460eee24afb2e37" s60v5len="545449" cdnurl = "http://vweixinf.tc.qq.com/110/20401/stodownload?m=4f716b5523414fd4f460eee24afb2e37&amp;filekey=30440201010430302e02016e040253480420346637313662353532333431346664346634363065656532346166623265333702030852a9040d00000004627466730000000132&amp;hy=SH&amp;storeid=268830668000583d37d2d13e40000006e01004fb153480d73d1f156921c95a&amp;ef=1&amp;bizid=1022" designerid = "" thumburl = "" encrypturl = "http://vweixinf.tc.qq.com/110/20402/stodownload?m=e0f296bf3d6f1a16465cb118664b3a95&amp;filekey=30440201010430302e02016e040253480420653066323936626633643666316131363436356362313138363634623361393502030852b0040d00000004627466730000000132&amp;hy=SH&amp;storeid=2688306680006c7557d2d13e40000006e02004fb253480d73d1f156921c96b&amp;ef=2&amp;bizid=1022" aeskey= "6975144ef7f342a6bfcd533f69944666" externurl = "http://vweixinf.tc.qq.com/110/20403/stodownload?m=4d8281232e01c2e7bd1920f1a8136d86&amp;filekey=30440201010430302e02016e040253480420346438323831323332653031633265376264313932306631613831333664383602030247d0040d00000004627466730000000132&amp;hy=SH&amp;storeid=26883066800086de37d2d13e40000006e03004fb353480d73d1f156921c981&amp;ef=3&amp;bizid=1022" externmd5 = "3a29040ffe88b5f7ddbb188262eefd7b" width= "240" height= "240" tpurl= "" tpauthkey= "" attachedtext= "" attachedtextcolor= "" lensid= "" emojiattr= "" linkid= "" desc= "" ></emoji>  </msg>'}, 'Status': 3, 'ImgStatus': 2, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1753417320, 'MsgSource': '<msgsource>\n\t<silence>0</silence>\n\t<membercount>63</membercount>\n\t<signature>N0_V1_c3Z4tdSA|v1_c0KQ3YM0</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'PushContent': 'ᡣ在群聊中发了一个表情', 'NewMsgId': 7848398051228932463, 'MsgSeq': 871398471}
2025-07-25 12:21:47 | INFO | 收到表情消息: 消息ID:440523933 来自:48097389945@chatroom 发送人:wxid_lneb7n23o4lg12 MD5:4f716b5523414fd4f460eee24afb2e37 大小:545449
2025-07-25 12:21:48 | DEBUG | [DoubaoImageRecognition] 缓存表情消息: 7848398051228932463
2025-07-25 12:21:48 | DEBUG | 收到消息: {'MsgId': 747335020, 'FromUserName': {'string': '48097389945@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 47, 'Content': {'string': 'wxid_ubbh6q832tcs21:\n<msg><emoji fromusername = "wxid_ubbh6q832tcs21" tousername = "48097389945@chatroom" type="2" idbuffer="media:0_0" md5="99ad225a3338891041c4c40162f5ff6d" len = "32290" productid="" androidmd5="99ad225a3338891041c4c40162f5ff6d" androidlen="32290" s60v3md5 = "99ad225a3338891041c4c40162f5ff6d" s60v3len="32290" s60v5md5 = "99ad225a3338891041c4c40162f5ff6d" s60v5len="32290" cdnurl = "http://vweixinf.tc.qq.com/110/20401/stodownload?m=99ad225a3338891041c4c40162f5ff6d&amp;filekey=3043020101042f302d02016e040253480420393961643232356133333338383931303431633463343031363266356666366402027e22040d00000004627466730000000131&amp;hy=SH&amp;storeid=323032303130313231323339343030303035303266336134356561663133373031623964303930303030303036653031303034666231&amp;ef=1&amp;bizid=1022" designerid = "" thumburl = "" encrypturl = "http://vweixinf.tc.qq.com/110/20402/stodownload?m=4c70bb97fd9b1c39b381ed32fbe63d5a&amp;filekey=3043020101042f302d02016e040253480420346337306262393766643962316333396233383165643332666265363364356102027e30040d00000004627466730000000131&amp;hy=SH&amp;storeid=323032303130313231323339343030303036343833376134356561663133373031623964303930303030303036653032303034666232&amp;ef=2&amp;bizid=1022" aeskey= "4da9e16429cb4c829199298e061c5af3" externurl = "http://vweixinf.tc.qq.com/110/20403/stodownload?m=396f13334a7ecfd217049b94d83a79d5&amp;filekey=3043020101042f302d02016e0402534804203339366631333333346137656366643231373034396239346438336137396435020214f0040d00000004627466730000000131&amp;hy=SH&amp;storeid=323032303130313231323339343030303037343730376134356561663133373031623964303930303030303036653033303034666233&amp;ef=3&amp;bizid=1022" externmd5 = "38c3f69d542f90c0646668d13a26fe5a" width= "227" height= "231" tpurl= "" tpauthkey= "" attachedtext= "" attachedtextcolor= "" lensid= "" emojiattr= "CgblnY/msLQ=" linkid= "" desc= "" ></emoji>  </msg>'}, 'Status': 3, 'ImgStatus': 2, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1753417321, 'MsgSource': '<msgsource>\n\t<silence>0</silence>\n\t<membercount>63</membercount>\n\t<signature>N0_V1_bk8yym+S|v1_A2XcvS6s</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'PushContent': '郭在群聊中发了一个表情', 'NewMsgId': 1524645882239868800, 'MsgSeq': 871398472}
2025-07-25 12:21:48 | INFO | 收到表情消息: 消息ID:747335020 来自:48097389945@chatroom 发送人:wxid_ubbh6q832tcs21 MD5:99ad225a3338891041c4c40162f5ff6d 大小:32290
2025-07-25 12:21:48 | DEBUG | [DoubaoImageRecognition] 缓存表情消息: 1524645882239868800
2025-07-25 12:21:49 | DEBUG | 收到消息: {'MsgId': 652195578, 'FromUserName': {'string': '48097389945@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 47, 'Content': {'string': 'wxid_lneb7n23o4lg12:\n<msg><emoji fromusername = "wxid_lneb7n23o4lg12" tousername = "48097389945@chatroom" type="2" idbuffer="media:0_0" md5="1bb302921a673909738f76926fbe83e4" len = "545449" productid="" androidmd5="1bb302921a673909738f76926fbe83e4" androidlen="545449" s60v3md5 = "1bb302921a673909738f76926fbe83e4" s60v3len="545449" s60v5md5 = "1bb302921a673909738f76926fbe83e4" s60v5len="545449" cdnurl = "http://vweixinf.tc.qq.com/110/20401/stodownload?m=1bb302921a673909738f76926fbe83e4&amp;filekey=30440201010430302e02016e040253480420316262333032393231613637333930393733386637363932366662653833653402030852a9040d00000004627466730000000132&amp;hy=SH&amp;storeid=268830669000d4ddf7d2d13e40000006e01004fb1534823de11b1568ae891e&amp;ef=1&amp;bizid=1022" designerid = "" thumburl = "" encrypturl = "http://vweixinf.tc.qq.com/110/20402/stodownload?m=1f5b648b6a2b4fc6a3e70d9335067092&amp;filekey=30440201010430302e02016e040253480420316635623634386236613262346663366133653730643933333530363730393202030852b0040d00000004627466730000000132&amp;hy=SH&amp;storeid=268830669000f33367d2d13e40000006e02004fb2534823de11b1568ae8937&amp;ef=2&amp;bizid=1022" aeskey= "6a8f518c521d49acb11b481c43eae3bd" externurl = "http://vweixinf.tc.qq.com/110/20403/stodownload?m=325bfc8850de452bdc8b4de5c225c998&amp;filekey=30440201010430302e02016e040253480420333235626663383835306465343532626463386234646535633232356339393802030247d0040d00000004627466730000000132&amp;hy=SH&amp;storeid=26883066a0001ea5a7d2d13e40000006e03004fb3534823de11b1568ae8957&amp;ef=3&amp;bizid=1022" externmd5 = "aab76c5d6081258d955376631c5f0056" width= "240" height= "240" tpurl= "" tpauthkey= "" attachedtext= "" attachedtextcolor= "" lensid= "" emojiattr= "" linkid= "" desc= "" ></emoji>  </msg>'}, 'Status': 3, 'ImgStatus': 2, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1753417322, 'MsgSource': '<msgsource>\n\t<silence>0</silence>\n\t<membercount>63</membercount>\n\t<signature>N0_V1_R0BZvEhe|v1_aNMrcgFg</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'PushContent': 'ᡣ在群聊中发了一个表情', 'NewMsgId': 832056148513332448, 'MsgSeq': 871398475}
2025-07-25 12:21:49 | INFO | 收到表情消息: 消息ID:652195578 来自:48097389945@chatroom 发送人:wxid_lneb7n23o4lg12 MD5:1bb302921a673909738f76926fbe83e4 大小:545449
2025-07-25 12:21:49 | DEBUG | [DoubaoImageRecognition] 缓存表情消息: 832056148513332448
2025-07-25 12:21:49 | DEBUG | 收到消息: {'MsgId': 2099723706, 'FromUserName': {'string': '48097389945@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 47, 'Content': {'string': 'wxid_lneb7n23o4lg12:\n<msg><emoji fromusername = "wxid_lneb7n23o4lg12" tousername = "48097389945@chatroom" type="2" idbuffer="media:0_0" md5="934720e44fe88dcf3c091fb6acb99dc6" len = "545449" productid="" androidmd5="934720e44fe88dcf3c091fb6acb99dc6" androidlen="545449" s60v3md5 = "934720e44fe88dcf3c091fb6acb99dc6" s60v3len="545449" s60v5md5 = "934720e44fe88dcf3c091fb6acb99dc6" s60v5len="545449" cdnurl = "http://vweixinf.tc.qq.com/110/20401/stodownload?m=934720e44fe88dcf3c091fb6acb99dc6&amp;filekey=30440201010430302e02016e040253480420393334373230653434666538386463663363303931666236616362393964633602030852a9040d00000004627466730000000132&amp;hy=SH&amp;storeid=2677a5a45000031df7d2d13e40000006e01004fb15348287e2171568f49469&amp;ef=1&amp;bizid=1022" designerid = "" thumburl = "" encrypturl = "http://vweixinf.tc.qq.com/110/20402/stodownload?m=e43e868d972dcf7fd5d21a9b3e5e6d2e&amp;filekey=30440201010430302e02016e040253480420653433653836386439373264636637666435643231613962336535653664326502030852b0040d00000004627466730000000132&amp;hy=SH&amp;storeid=2677a5a450001a93a7d2d13e40000006e02004fb25348287e2171568f49474&amp;ef=2&amp;bizid=1022" aeskey= "3fc3420cca8c4340a0c495f35328b96c" externurl = "http://vweixinf.tc.qq.com/110/20403/stodownload?m=5c2081722aead7668c060b1c22d4d599&amp;filekey=30440201010430302e02016e040253480420356332303831373232616561643736363863303630623163323264346435393902030247d0040d00000004627466730000000132&amp;hy=SH&amp;storeid=2677a5a45000346267d2d13e40000006e03004fb35348287e2171568f49483&amp;ef=3&amp;bizid=1022" externmd5 = "51785b638cf76136c66ef381435c9a43" width= "240" height= "240" tpurl= "" tpauthkey= "" attachedtext= "" attachedtextcolor= "" lensid= "" emojiattr= "" linkid= "" desc= "" ></emoji>  </msg>'}, 'Status': 3, 'ImgStatus': 2, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1753417322, 'MsgSource': '<msgsource>\n\t<silence>0</silence>\n\t<membercount>63</membercount>\n\t<signature>N0_V1_+5a3swNT|v1_BoPJI4Xs</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'PushContent': 'ᡣ在群聊中发了一个表情', 'NewMsgId': 1488317287498049011, 'MsgSeq': 871398476}
2025-07-25 12:21:49 | INFO | 收到表情消息: 消息ID:2099723706 来自:48097389945@chatroom 发送人:wxid_lneb7n23o4lg12 MD5:934720e44fe88dcf3c091fb6acb99dc6 大小:545449
2025-07-25 12:21:50 | DEBUG | [DoubaoImageRecognition] 缓存表情消息: 1488317287498049011
2025-07-25 12:21:50 | DEBUG | 收到消息: {'MsgId': 1786031042, 'FromUserName': {'string': '48097389945@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 47, 'Content': {'string': 'wxid_lneb7n23o4lg12:\n<msg><emoji fromusername = "wxid_lneb7n23o4lg12" tousername = "48097389945@chatroom" type="2" idbuffer="media:0_0" md5="595282469e52e9cb49cc241d74e4a980" len = "545449" productid="" androidmd5="595282469e52e9cb49cc241d74e4a980" androidlen="545449" s60v3md5 = "595282469e52e9cb49cc241d74e4a980" s60v3len="545449" s60v5md5 = "595282469e52e9cb49cc241d74e4a980" s60v5len="545449" cdnurl = "http://vweixinf.tc.qq.com/110/20401/stodownload?m=595282469e52e9cb49cc241d74e4a980&amp;filekey=30440201010430302e02016e040253480420353935323832343639653532653963623439636332343164373465346139383002030852a9040d00000004627466730000000132&amp;hy=SH&amp;storeid=2681a250d0004212b005a663f0000006e01004fb153480bf3e03156ff66eaa&amp;ef=1&amp;bizid=1022" designerid = "" thumburl = "" encrypturl = "http://vweixinf.tc.qq.com/110/20402/stodownload?m=dd24f2c17399f0524b0081b25a28a0ee&amp;filekey=30440201010430302e02016e040253480420646432346632633137333939663035323462303038316232356132386130656502030852b0040d00000004627466730000000132&amp;hy=SH&amp;storeid=2681a250d0005b9cd005a663f0000006e02004fb253480bf3e03156ff66ec8&amp;ef=2&amp;bizid=1022" aeskey= "eb3328612ed044c596149f2cbbe7cc90" externurl = "http://vweixinf.tc.qq.com/110/20403/stodownload?m=c5967c07a9f29a6bf64090e55fefc1d0&amp;filekey=30440201010430302e02016e04025348042063353936376330376139663239613662663634303930653535666566633164300203024790040d00000004627466730000000132&amp;hy=SH&amp;storeid=2681a250d00073df9005a663f0000006e03004fb353480bf3e03156ff66ed7&amp;ef=3&amp;bizid=1022" externmd5 = "0efe2e56d55c32bd4e2ee4e568fbbc96" width= "240" height= "240" tpurl= "" tpauthkey= "" attachedtext= "" attachedtextcolor= "" lensid= "" emojiattr= "" linkid= "" desc= "" ></emoji>  </msg>'}, 'Status': 3, 'ImgStatus': 2, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1753417323, 'MsgSource': '<msgsource>\n\t<silence>0</silence>\n\t<membercount>63</membercount>\n\t<signature>N0_V1_EsAhEbBD|v1_hVx9nrkE</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'PushContent': 'ᡣ在群聊中发了一个表情', 'NewMsgId': 7797618189296552963, 'MsgSeq': 871398477}
2025-07-25 12:21:50 | INFO | 收到表情消息: 消息ID:1786031042 来自:48097389945@chatroom 发送人:wxid_lneb7n23o4lg12 MD5:595282469e52e9cb49cc241d74e4a980 大小:545449
2025-07-25 12:21:51 | DEBUG | [DoubaoImageRecognition] 缓存表情消息: 7797618189296552963
2025-07-25 12:21:51 | DEBUG | 收到消息: {'MsgId': 1830402446, 'FromUserName': {'string': '48097389945@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 47, 'Content': {'string': 'wxid_lneb7n23o4lg12:\n<msg><emoji fromusername = "wxid_lneb7n23o4lg12" tousername = "48097389945@chatroom" type="2" idbuffer="media:0_0" md5="d8c919e2f685ae572964bee99ac2e2dc" len = "545449" productid="" androidmd5="d8c919e2f685ae572964bee99ac2e2dc" androidlen="545449" s60v3md5 = "d8c919e2f685ae572964bee99ac2e2dc" s60v3len="545449" s60v5md5 = "d8c919e2f685ae572964bee99ac2e2dc" s60v5len="545449" cdnurl = "http://vweixinf.tc.qq.com/110/20401/stodownload?m=d8c919e2f685ae572964bee99ac2e2dc&amp;filekey=30440201010430302e02016e040253480420643863393139653266363835616535373239363462656539396163326532646302030852a9040d00000004627466730000000132&amp;hy=SH&amp;storeid=26775f1b80004629d7d2d13e40000006e01004fb153480c630031568529ebf&amp;ef=1&amp;bizid=1022" designerid = "" thumburl = "" encrypturl = "http://vweixinf.tc.qq.com/110/20402/stodownload?m=d1bbdf2720b514314eda47188042dc15&amp;filekey=30440201010430302e02016e040253480420643162626466323732306235313433313465646134373138383034326463313502030852b0040d00000004627466730000000132&amp;hy=SH&amp;storeid=26775f1b80005d8ab7d2d13e40000006e02004fb253480c630031568529ecb&amp;ef=2&amp;bizid=1022" aeskey= "73c59334c1db411cb258d791267e47cb" externurl = "http://vweixinf.tc.qq.com/110/20403/stodownload?m=bb521d8c9a29b0a89bd37b51f4e8439f&amp;filekey=30440201010430302e02016e04025348042062623532316438633961323962306138396264333762353166346538343339660203024790040d00000004627466730000000132&amp;hy=SH&amp;storeid=26775f1b80007054c7d2d13e40000006e03004fb353480c630031568529ed2&amp;ef=3&amp;bizid=1022" externmd5 = "0031451175f5afad87d486a68f1d33e4" width= "240" height= "240" tpurl= "" tpauthkey= "" attachedtext= "" attachedtextcolor= "" lensid= "" emojiattr= "" linkid= "" desc= "" ></emoji>  </msg>'}, 'Status': 3, 'ImgStatus': 2, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1753417324, 'MsgSource': '<msgsource>\n\t<silence>0</silence>\n\t<membercount>63</membercount>\n\t<signature>N0_V1_Dy0b2GEo|v1_xG5mnsik</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'PushContent': 'ᡣ在群聊中发了一个表情', 'NewMsgId': 3848411373093687226, 'MsgSeq': 871398480}
2025-07-25 12:21:51 | INFO | 收到表情消息: 消息ID:1830402446 来自:48097389945@chatroom 发送人:wxid_lneb7n23o4lg12 MD5:d8c919e2f685ae572964bee99ac2e2dc 大小:545449
2025-07-25 12:21:51 | DEBUG | [DoubaoImageRecognition] 缓存表情消息: 3848411373093687226
2025-07-25 12:21:52 | DEBUG | 收到消息: {'MsgId': 549608574, 'FromUserName': {'string': '48097389945@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 47, 'Content': {'string': 'wxid_lneb7n23o4lg12:\n<msg><emoji fromusername = "wxid_lneb7n23o4lg12" tousername = "48097389945@chatroom" type="2" idbuffer="media:0_0" md5="306167d178d0f63ebdc4f529e1c9f607" len = "545449" productid="" androidmd5="306167d178d0f63ebdc4f529e1c9f607" androidlen="545449" s60v3md5 = "306167d178d0f63ebdc4f529e1c9f607" s60v3len="545449" s60v5md5 = "306167d178d0f63ebdc4f529e1c9f607" s60v5len="545449" cdnurl = "http://vweixinf.tc.qq.com/110/20401/stodownload?m=306167d178d0f63ebdc4f529e1c9f607&amp;filekey=30440201010430302e02016e040253480420333036313637643137386430663633656264633466353239653163396636303702030852a9040d00000004627466730000000132&amp;hy=SH&amp;storeid=268303017000330d8005a663f0000006e01004fb1534812aee17157b70b3d9&amp;ef=1&amp;bizid=1022" designerid = "" thumburl = "" encrypturl = "http://vweixinf.tc.qq.com/110/20402/stodownload?m=0dfca21144a65f1d7b7469ca3bfe5c87&amp;filekey=30440201010430302e02016e040253480420306466636132313134346136356631643762373436396361336266653563383702030852b0040d00000004627466730000000132&amp;hy=SH&amp;storeid=26830301700051789005a663f0000006e02004fb2534812aee17157b70b3f3&amp;ef=2&amp;bizid=1022" aeskey= "c3e45d22d6cc48e69bffa9839c05efad" externurl = "http://vweixinf.tc.qq.com/110/20403/stodownload?m=97210af7e0f526855e56152aebbd774d&amp;filekey=30440201010430302e02016e04025348042039373231306166376530663532363835356535363135326165626264373734640203024790040d00000004627466730000000132&amp;hy=SH&amp;storeid=2683030170006bf1e005a663f0000006e03004fb3534812aee17157b70b414&amp;ef=3&amp;bizid=1022" externmd5 = "0efe2e56d55c32bd4e2ee4e568fbbc96" width= "240" height= "240" tpurl= "" tpauthkey= "" attachedtext= "" attachedtextcolor= "" lensid= "" emojiattr= "" linkid= "" desc= "" ></emoji>  </msg>'}, 'Status': 3, 'ImgStatus': 2, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1753417329, 'MsgSource': '<msgsource>\n\t<silence>0</silence>\n\t<membercount>63</membercount>\n\t<signature>N0_V1_jCqf8kVJ|v1_7GitxkKQ</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'PushContent': 'ᡣ在群聊中发了一个表情', 'NewMsgId': 8774156020921529654, 'MsgSeq': 871398487}
2025-07-25 12:21:52 | INFO | 收到表情消息: 消息ID:549608574 来自:48097389945@chatroom 发送人:wxid_lneb7n23o4lg12 MD5:306167d178d0f63ebdc4f529e1c9f607 大小:545449
2025-07-25 12:21:52 | DEBUG | [DoubaoImageRecognition] 缓存表情消息: 8774156020921529654
2025-07-25 12:21:52 | DEBUG | 收到消息: {'MsgId': 752712553, 'FromUserName': {'string': '48097389945@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 47, 'Content': {'string': 'wxid_lneb7n23o4lg12:\n<msg><emoji fromusername = "wxid_lneb7n23o4lg12" tousername = "48097389945@chatroom" type="2" idbuffer="media:0_0" md5="655282cdc86531c92d36093624aaed8c" len = "545449" productid="" androidmd5="655282cdc86531c92d36093624aaed8c" androidlen="545449" s60v3md5 = "655282cdc86531c92d36093624aaed8c" s60v3len="545449" s60v5md5 = "655282cdc86531c92d36093624aaed8c" s60v5len="545449" cdnurl = "http://vweixinf.tc.qq.com/110/20401/stodownload?m=655282cdc86531c92d36093624aaed8c&amp;filekey=30440201010430302e02016e040253480420363535323832636463383635333163393264333630393336323461616564386302030852a9040d00000004627466730000000132&amp;hy=SH&amp;storeid=26883067300056d337d2d13e40000006e01004fb153480a360b01e694a06c8&amp;ef=1&amp;bizid=1022" designerid = "" thumburl = "" encrypturl = "http://vweixinf.tc.qq.com/110/20402/stodownload?m=1c026e074b9126b9a7ba6c7d060677d4&amp;filekey=30440201010430302e02016e040253480420316330323665303734623931323662396137626136633764303630363737643402030852b0040d00000004627466730000000132&amp;hy=SH&amp;storeid=268830673000700eb7d2d13e40000006e02004fb253480a360b01e694a06df&amp;ef=2&amp;bizid=1022" aeskey= "bb053fd5033c410ca55a513206cf31b4" externurl = "http://vweixinf.tc.qq.com/110/20403/stodownload?m=cdeea9f43c5f5af9dd815534022b8056&amp;filekey=30440201010430302e02016e040253480420636465656139663433633566356166396464383135353334303232623830353602030247d0040d00000004627466730000000132&amp;hy=SH&amp;storeid=2688306730008b17d7d2d13e40000006e03004fb353480a360b01e694a06f8&amp;ef=3&amp;bizid=1022" externmd5 = "3a29040ffe88b5f7ddbb188262eefd7b" width= "240" height= "240" tpurl= "" tpauthkey= "" attachedtext= "" attachedtextcolor= "" lensid= "" emojiattr= "" linkid= "" desc= "" ></emoji>  </msg>'}, 'Status': 3, 'ImgStatus': 2, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1753417332, 'MsgSource': '<msgsource>\n\t<silence>0</silence>\n\t<membercount>63</membercount>\n\t<signature>N0_V1_encHS5tq|v1_wHWdyFuW</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'PushContent': 'ᡣ在群聊中发了一个表情', 'NewMsgId': 2952505602247863284, 'MsgSeq': 871398488}
2025-07-25 12:21:52 | INFO | 收到表情消息: 消息ID:752712553 来自:48097389945@chatroom 发送人:wxid_lneb7n23o4lg12 MD5:655282cdc86531c92d36093624aaed8c 大小:545449
2025-07-25 12:21:53 | DEBUG | [DoubaoImageRecognition] 缓存表情消息: 2952505602247863284
2025-07-25 12:22:16 | DEBUG | 收到消息: {'MsgId': 61561566, 'FromUserName': {'string': '48097389945@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 1, 'Content': {'string': 'rongcheng6630606:\n@喵小叶\u2005你的话好假'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1753417359, 'MsgSource': '<msgsource>\n\t<atuserlist><![CDATA[wxid_84mmq4cu7ita22]]></atuserlist>\n\t<pua>1</pua>\n\t<eggIncluded>1</eggIncluded>\n\t<silence>0</silence>\n\t<membercount>63</membercount>\n\t<signature>N0_V1_Uc94GdBW|v1_363E2VMe</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'PushContent': '× : @喵小叶\u2005你的话好假', 'NewMsgId': 4201237955559751844, 'MsgSeq': 871398489}
2025-07-25 12:22:16 | INFO | 收到文本消息: 消息ID:61561566 来自:48097389945@chatroom 发送人:rongcheng6630606 @:['wxid_84mmq4cu7ita22'] 内容:@喵小叶 你的话好假
2025-07-25 12:22:17 | DEBUG | 处理消息内容: '@喵小叶 你的话好假'
2025-07-25 12:22:17 | DEBUG | 消息内容 '@喵小叶 你的话好假' 不匹配任何命令，忽略
2025-07-25 12:22:20 | DEBUG | 收到消息: {'MsgId': 829026660, 'FromUserName': {'string': '48097389945@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 47, 'Content': {'string': 'rongcheng6630606:\n<msg><emoji fromusername="rongcheng6630606" tousername="48097389945@chatroom" type="3" idbuffer="media:0_0" md5="935bfa9dd90e1cbf65a859c7bad1e1f5" len="35076" productid="" androidmd5="935bfa9dd90e1cbf65a859c7bad1e1f5" androidlen="35076" s60v3md5="935bfa9dd90e1cbf65a859c7bad1e1f5" s60v3len="35076" s60v5md5="935bfa9dd90e1cbf65a859c7bad1e1f5" s60v5len="35076" cdnurl="http://vweixinf.tc.qq.com/110/20401/stodownload?m=935bfa9dd90e1cbf65a859c7bad1e1f5&amp;filekey=30440201010430302e02016e04025348042039333562666139646439306531636266363561383539633762616431653166350203008904040d00000004627466730000000132&amp;hy=SH&amp;storeid=2643cfddb000526d867b6dad00000006e01004fb1534827f34b00b6effff74&amp;ef=1&amp;bizid=1022" designerid="" thumburl="" encrypturl="http://vweixinf.tc.qq.com/110/20402/stodownload?m=c8e08c36a1e51722c70c5ed7544bb349&amp;filekey=30440201010430302e02016e04025348042063386530386333366131653531373232633730633565643735343462623334390203008910040d00000004627466730000000132&amp;hy=SH&amp;storeid=2643cfddb0006166d67b6dad00000006e02004fb2534827f34b00b6effff8f&amp;ef=2&amp;bizid=1022" aeskey="5fc81eca71c943a2af344a9c2327e999" externurl="http://vweixinf.tc.qq.com/110/20403/stodownload?m=028c605c82c02d1af0f93e797cbd3582&amp;filekey=3043020101042f302d02016e0402534804203032386336303563383263303264316166306639336537393763626433353832020252a0040d00000004627466730000000132&amp;hy=SH&amp;storeid=2643cfddb00067fe467b6dad00000006e03004fb3534827f34b00b6effff9a&amp;ef=3&amp;bizid=1022" externmd5="84825538da67c6ada615636c9d9045c3" width="300" height="300" tpurl="" tpauthkey="" attachedtext="" attachedtextcolor="" lensid="" emojiattr="" linkid="" desc=""></emoji></msg>'}, 'Status': 3, 'ImgStatus': 2, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1753417362, 'MsgSource': '<msgsource>\n\t<silence>0</silence>\n\t<membercount>63</membercount>\n\t<signature>N0_V1_DwncKRMg|v1_PCBSUN5m</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'PushContent': '×在群聊中发了一个表情', 'NewMsgId': 7602280137657812089, 'MsgSeq': 871398490}
2025-07-25 12:22:20 | INFO | 收到表情消息: 消息ID:829026660 来自:48097389945@chatroom 发送人:rongcheng6630606 MD5:935bfa9dd90e1cbf65a859c7bad1e1f5 大小:35076
2025-07-25 12:22:20 | DEBUG | [DoubaoImageRecognition] 缓存表情消息: 7602280137657812089
2025-07-25 12:22:25 | DEBUG | 收到消息: {'MsgId': 10578365, 'FromUserName': {'string': '55878994168@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 1, 'Content': {'string': 'wxid_ubbh6q832tcs21:\n打'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1753417367, 'MsgSource': '<msgsource>\n\t<pua>1</pua>\n\t<eggIncluded>1</eggIncluded>\n\t<silence>0</silence>\n\t<membercount>3</membercount>\n\t<signature>N0_V1_nyemwZbG|v1_vUISXr13</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'PushContent': '郭 : 打', 'NewMsgId': 8297037324565900670, 'MsgSeq': 871398491}
2025-07-25 12:22:25 | INFO | 收到文本消息: 消息ID:10578365 来自:55878994168@chatroom 发送人:wxid_ubbh6q832tcs21 @:[] 内容:打
2025-07-25 12:22:25 | INFO | 发送表情消息: 对方wxid:55878994168@chatroom md5:fe3cd0728ea249c42967eca1a923ca19 总长度:9992069
2025-07-25 12:22:25 | DEBUG | 处理消息内容: '打'
2025-07-25 12:22:25 | DEBUG | 消息内容 '打' 不匹配任何命令，忽略
2025-07-25 12:22:26 | DEBUG | 收到消息: {'MsgId': 1285914739, 'FromUserName': {'string': '55878994168@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 1, 'Content': {'string': 'wxid_ubbh6q832tcs21:\n打'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1753417367, 'MsgSource': '<msgsource>\n\t<pua>1</pua>\n\t<eggIncluded>1</eggIncluded>\n\t<silence>0</silence>\n\t<membercount>3</membercount>\n\t<signature>N0_V1_nyemwZbG|v1_vUISXr13</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'PushContent': '郭 : 打', 'NewMsgId': 3661407475824745400, 'MsgSeq': 871398492}
2025-07-25 12:22:26 | INFO | 收到文本消息: 消息ID:1285914739 来自:55878994168@chatroom 发送人:wxid_ubbh6q832tcs21 @:[] 内容:打
2025-07-25 12:22:27 | INFO | 发送表情消息: 对方wxid:55878994168@chatroom md5:fe3cd0728ea249c42967eca1a923ca19 总长度:9992069
2025-07-25 12:22:27 | DEBUG | 处理消息内容: '打'
2025-07-25 12:22:27 | DEBUG | 消息内容 '打' 不匹配任何命令，忽略
2025-07-25 12:22:32 | DEBUG | 收到消息: {'MsgId': 1681119049, 'FromUserName': {'string': '48097389945@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 1, 'Content': {'string': 'rongcheng6630606:\n不可信'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1753417373, 'MsgSource': '<msgsource>\n\t<pua>1</pua>\n\t<eggIncluded>1</eggIncluded>\n\t<silence>0</silence>\n\t<membercount>63</membercount>\n\t<signature>N0_V1_TtSb29sW|v1_U3D68Wyf</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'PushContent': '× : 不可信', 'NewMsgId': 3862262213452389394, 'MsgSeq': 871398497}
2025-07-25 12:22:32 | INFO | 收到文本消息: 消息ID:1681119049 来自:48097389945@chatroom 发送人:rongcheng6630606 @:[] 内容:不可信
2025-07-25 12:22:32 | DEBUG | 处理消息内容: '不可信'
2025-07-25 12:22:32 | DEBUG | 消息内容 '不可信' 不匹配任何命令，忽略
2025-07-25 12:22:35 | DEBUG | 收到消息: {'MsgId': 1430750027, 'FromUserName': {'string': '55878994168@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 1, 'Content': {'string': 'wxid_ubbh6q832tcs21:\n打'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1753417378, 'MsgSource': '<msgsource>\n\t<pua>1</pua>\n\t<eggIncluded>1</eggIncluded>\n\t<silence>0</silence>\n\t<membercount>3</membercount>\n\t<signature>N0_V1_dJXh3UCA|v1_XXSVgBvT</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'PushContent': '郭 : 打', 'NewMsgId': 7672144629268843034, 'MsgSeq': 871398498}
2025-07-25 12:22:35 | INFO | 收到文本消息: 消息ID:1430750027 来自:55878994168@chatroom 发送人:wxid_ubbh6q832tcs21 @:[] 内容:打
2025-07-25 12:22:36 | INFO | 发送表情消息: 对方wxid:55878994168@chatroom md5:fe3cd0728ea249c42967eca1a923ca19 总长度:9992069
2025-07-25 12:22:36 | DEBUG | 处理消息内容: '打'
2025-07-25 12:22:36 | DEBUG | 消息内容 '打' 不匹配任何命令，忽略
2025-07-25 12:22:36 | DEBUG | 收到消息: {'MsgId': 1984412504, 'FromUserName': {'string': '55878994168@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 1, 'Content': {'string': 'wxid_ubbh6q832tcs21:\n打'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1753417379, 'MsgSource': '<msgsource>\n\t<pua>1</pua>\n\t<eggIncluded>1</eggIncluded>\n\t<silence>0</silence>\n\t<membercount>3</membercount>\n\t<signature>N0_V1_Hvm5799g|v1_a+9x4WRD</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'PushContent': '郭 : 打', 'NewMsgId': 2939073427464506622, 'MsgSeq': 871398501}
2025-07-25 12:22:36 | INFO | 收到文本消息: 消息ID:1984412504 来自:55878994168@chatroom 发送人:wxid_ubbh6q832tcs21 @:[] 内容:打
2025-07-25 12:22:38 | INFO | 发送表情消息: 对方wxid:55878994168@chatroom md5:fe3cd0728ea249c42967eca1a923ca19 总长度:9992069
2025-07-25 12:22:38 | DEBUG | 处理消息内容: '打'
2025-07-25 12:22:38 | DEBUG | 消息内容 '打' 不匹配任何命令，忽略
2025-07-25 12:22:38 | DEBUG | 收到消息: {'MsgId': 122405594, 'FromUserName': {'string': '55878994168@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 1, 'Content': {'string': 'wxid_ubbh6q832tcs21:\n打'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1753417380, 'MsgSource': '<msgsource>\n\t<pua>1</pua>\n\t<eggIncluded>1</eggIncluded>\n\t<silence>0</silence>\n\t<membercount>3</membercount>\n\t<signature>N0_V1_9T+QzrCw|v1_fSGzA44I</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'PushContent': '郭 : 打', 'NewMsgId': 456108366315505431, 'MsgSeq': 871398502}
2025-07-25 12:22:38 | INFO | 收到文本消息: 消息ID:122405594 来自:55878994168@chatroom 发送人:wxid_ubbh6q832tcs21 @:[] 内容:打
2025-07-25 12:22:39 | INFO | 发送表情消息: 对方wxid:55878994168@chatroom md5:fe3cd0728ea249c42967eca1a923ca19 总长度:9992069
2025-07-25 12:22:39 | DEBUG | 处理消息内容: '打'
2025-07-25 12:22:39 | DEBUG | 消息内容 '打' 不匹配任何命令，忽略
2025-07-25 12:22:39 | DEBUG | 收到消息: {'MsgId': 1061035903, 'FromUserName': {'string': '55878994168@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 1, 'Content': {'string': 'wxid_ubbh6q832tcs21:\n打'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1753417380, 'MsgSource': '<msgsource>\n\t<pua>1</pua>\n\t<eggIncluded>1</eggIncluded>\n\t<silence>0</silence>\n\t<membercount>3</membercount>\n\t<signature>N0_V1_9T+QzrCw|v1_fSGzA44I</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'PushContent': '郭 : 打', 'NewMsgId': 6246035018684268989, 'MsgSeq': 871398505}
2025-07-25 12:22:39 | INFO | 收到文本消息: 消息ID:1061035903 来自:55878994168@chatroom 发送人:wxid_ubbh6q832tcs21 @:[] 内容:打
2025-07-25 12:22:40 | INFO | 发送表情消息: 对方wxid:55878994168@chatroom md5:fe3cd0728ea249c42967eca1a923ca19 总长度:9992069
2025-07-25 12:22:40 | DEBUG | 处理消息内容: '打'
2025-07-25 12:22:40 | DEBUG | 消息内容 '打' 不匹配任何命令，忽略
2025-07-25 12:22:41 | DEBUG | 收到消息: {'MsgId': 1959943101, 'FromUserName': {'string': '55878994168@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 1, 'Content': {'string': 'wxid_ubbh6q832tcs21:\n打'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1753417381, 'MsgSource': '<msgsource>\n\t<pua>1</pua>\n\t<eggIncluded>1</eggIncluded>\n\t<silence>0</silence>\n\t<membercount>3</membercount>\n\t<signature>N0_V1_X81e6lZk|v1_i0HYeH1D</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'PushContent': '郭 : 打', 'NewMsgId': 5249455678019691972, 'MsgSeq': 871398506}
2025-07-25 12:22:41 | INFO | 收到文本消息: 消息ID:1959943101 来自:55878994168@chatroom 发送人:wxid_ubbh6q832tcs21 @:[] 内容:打
2025-07-25 12:22:42 | INFO | 发送表情消息: 对方wxid:55878994168@chatroom md5:fe3cd0728ea249c42967eca1a923ca19 总长度:9992069
2025-07-25 12:22:42 | DEBUG | 处理消息内容: '打'
2025-07-25 12:22:42 | DEBUG | 消息内容 '打' 不匹配任何命令，忽略
2025-07-25 12:22:42 | DEBUG | 收到消息: {'MsgId': 699586906, 'FromUserName': {'string': '55878994168@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 1, 'Content': {'string': 'wxid_ubbh6q832tcs21:\n打'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1753417381, 'MsgSource': '<msgsource>\n\t<pua>1</pua>\n\t<eggIncluded>1</eggIncluded>\n\t<silence>0</silence>\n\t<membercount>3</membercount>\n\t<signature>N0_V1_X81e6lZk|v1_i0HYeH1D</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'PushContent': '郭 : 打', 'NewMsgId': 5511950039609697319, 'MsgSeq': 871398507}
2025-07-25 12:22:42 | INFO | 收到文本消息: 消息ID:699586906 来自:55878994168@chatroom 发送人:wxid_ubbh6q832tcs21 @:[] 内容:打
2025-07-25 12:22:43 | INFO | 发送表情消息: 对方wxid:55878994168@chatroom md5:fe3cd0728ea249c42967eca1a923ca19 总长度:9992069
2025-07-25 12:22:43 | DEBUG | 处理消息内容: '打'
2025-07-25 12:22:43 | DEBUG | 消息内容 '打' 不匹配任何命令，忽略
2025-07-25 12:22:43 | DEBUG | 收到消息: {'MsgId': 786007379, 'FromUserName': {'string': '55878994168@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 1, 'Content': {'string': 'wxid_ubbh6q832tcs21:\n打打'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1753417382, 'MsgSource': '<msgsource>\n\t<pua>1</pua>\n\t<eggIncluded>1</eggIncluded>\n\t<silence>0</silence>\n\t<membercount>3</membercount>\n\t<signature>N0_V1_m9QwMR9z|v1_HujQ290z</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'PushContent': '郭 : 打打', 'NewMsgId': 6804483463488942581, 'MsgSeq': 871398510}
2025-07-25 12:22:43 | INFO | 收到文本消息: 消息ID:786007379 来自:55878994168@chatroom 发送人:wxid_ubbh6q832tcs21 @:[] 内容:打打
2025-07-25 12:22:44 | DEBUG | 处理消息内容: '打打'
2025-07-25 12:22:44 | DEBUG | 消息内容 '打打' 不匹配任何命令，忽略
2025-07-25 12:23:01 | DEBUG | 收到消息: {'MsgId': 1182309940, 'FromUserName': {'string': '48097389945@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 1, 'Content': {'string': 'wxid_5909219096412:\n打'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1753417403, 'MsgSource': '<msgsource>\n\t<bizflag>0</bizflag>\n\t<pua>1</pua>\n\t<eggIncluded>1</eggIncluded>\n\t<silence>0</silence>\n\t<membercount>63</membercount>\n\t<signature>N0_V1_QR+nPVKk|v1_Fum6t9Ny</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'PushContent': '꯭橙꯭子꯭\ue346 : 打', 'NewMsgId': 4050477132397011144, 'MsgSeq': 871398517}
2025-07-25 12:23:01 | INFO | 收到文本消息: 消息ID:1182309940 来自:48097389945@chatroom 发送人:wxid_5909219096412 @:[] 内容:打
2025-07-25 12:23:02 | INFO | 发送表情消息: 对方wxid:48097389945@chatroom md5:fe3cd0728ea249c42967eca1a923ca19 总长度:9992069
2025-07-25 12:23:02 | DEBUG | 处理消息内容: '打'
2025-07-25 12:23:02 | DEBUG | 消息内容 '打' 不匹配任何命令，忽略
2025-07-25 12:23:04 | DEBUG | 收到消息: {'MsgId': 1449947654, 'FromUserName': {'string': '48097389945@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 47, 'Content': {'string': 'wxid_lneb7n23o4lg12:\n<msg><emoji fromusername = "wxid_lneb7n23o4lg12" tousername = "48097389945@chatroom" type="2" idbuffer="media:0_0" md5="6845fbbd32355fabb5bd3a8160d69680" len = "514368" productid="" androidmd5="6845fbbd32355fabb5bd3a8160d69680" androidlen="514368" s60v3md5 = "6845fbbd32355fabb5bd3a8160d69680" s60v3len="514368" s60v5md5 = "6845fbbd32355fabb5bd3a8160d69680" s60v5len="514368" cdnurl = "http://vweixinf.tc.qq.com/110/20401/stodownload?m=6845fbbd32355fabb5bd3a8160d69680&amp;filekey=30440201010430302e02016e0402534804203638343566626264333233353566616262356264336138313630643639363830020307d940040d00000004627466730000000132&amp;hy=SH&amp;storeid=2688306be0005b55e7d2d13e40000006e01004fb15348049be171568ad729b&amp;ef=1&amp;bizid=1022" designerid = "" thumburl = "" encrypturl = "http://vweixinf.tc.qq.com/110/20402/stodownload?m=0bfbd33ce069acb0213ba0f44178bc89&amp;filekey=30440201010430302e02016e0402534804203062666264333363653036396163623032313362613066343431373862633839020307d950040d00000004627466730000000132&amp;hy=SH&amp;storeid=2688306be00073aa87d2d13e40000006e02004fb25348049be171568ad72bc&amp;ef=2&amp;bizid=1022" aeskey= "48240205d63646698c332f1b0ce8f15f" externurl = "http://vweixinf.tc.qq.com/110/20403/stodownload?m=54ef0f7e35572eec411b8f29f73ad0a0&amp;filekey=30440201010430302e02016e04025348042035346566306637653335353732656563343131623866323966373361643061300203023c10040d00000004627466730000000132&amp;hy=SH&amp;storeid=2688306be000893ab7d2d13e40000006e03004fb35348049be171568ad72d7&amp;ef=3&amp;bizid=1022" externmd5 = "be0743574b5f5eaf3e1108a5f6325f8f" width= "240" height= "240" tpurl= "" tpauthkey= "" attachedtext= "" attachedtextcolor= "" lensid= "" emojiattr= "" linkid= "" desc= "" ></emoji>  </msg>'}, 'Status': 3, 'ImgStatus': 2, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1753417406, 'MsgSource': '<msgsource>\n\t<silence>0</silence>\n\t<membercount>63</membercount>\n\t<signature>N0_V1_wzDvqru+|v1_TEOrls07</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'PushContent': 'ᡣ在群聊中发了一个表情', 'NewMsgId': 424826794110483413, 'MsgSeq': 871398520}
2025-07-25 12:23:04 | INFO | 收到表情消息: 消息ID:1449947654 来自:48097389945@chatroom 发送人:wxid_lneb7n23o4lg12 MD5:6845fbbd32355fabb5bd3a8160d69680 大小:514368
2025-07-25 12:23:04 | DEBUG | [DoubaoImageRecognition] 缓存表情消息: 424826794110483413
2025-07-25 12:23:31 | DEBUG | 收到消息: {'MsgId': 1868781364, 'FromUserName': {'string': '48097389945@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 1, 'Content': {'string': 'wxid_ubbh6q832tcs21:\n走'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1753417434, 'MsgSource': '<msgsource>\n\t<pua>1</pua>\n\t<eggIncluded>1</eggIncluded>\n\t<silence>0</silence>\n\t<membercount>63</membercount>\n\t<signature>N0_V1_EdLzOaQ+|v1_p28d1qS9</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'PushContent': '郭 : 走', 'NewMsgId': 3069855993755632785, 'MsgSeq': 871398521}
2025-07-25 12:23:31 | INFO | 收到文本消息: 消息ID:1868781364 来自:48097389945@chatroom 发送人:wxid_ubbh6q832tcs21 @:[] 内容:走
2025-07-25 12:23:32 | INFO | 发送表情消息: 对方wxid:48097389945@chatroom md5:479d9683141301494753f07fd93dff19 总长度:9992069
2025-07-25 12:23:32 | DEBUG | 处理消息内容: '走'
2025-07-25 12:23:32 | DEBUG | 消息内容 '走' 不匹配任何命令，忽略
2025-07-25 12:24:05 | DEBUG | 收到消息: {'MsgId': 1035050791, 'FromUserName': {'string': '48097389945@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 1, 'Content': {'string': 'wxid_84mmq4cu7ita22:\n别和我说话'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1753417467, 'MsgSource': '<msgsource>\n\t<sec_msg_node>\n\t\t<alnode>\n\t\t\t<fr>1</fr>\n\t\t</alnode>\n\t</sec_msg_node>\n\t<pua>1</pua>\n\t<silence>0</silence>\n\t<membercount>63</membercount>\n\t<signature>N0_V1_NqmhMeLQ|v1_UlcM7XBI</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'PushContent': '喵小叶 : 别和我说话', 'NewMsgId': 587654215870014072, 'MsgSeq': 871398524}
2025-07-25 12:24:05 | INFO | 收到文本消息: 消息ID:1035050791 来自:48097389945@chatroom 发送人:wxid_84mmq4cu7ita22 @:[] 内容:别和我说话
2025-07-25 12:24:05 | DEBUG | 处理消息内容: '别和我说话'
2025-07-25 12:24:05 | DEBUG | 消息内容 '别和我说话' 不匹配任何命令，忽略
2025-07-25 12:24:10 | DEBUG | 收到消息: {'MsgId': 1119012497, 'FromUserName': {'string': '48097389945@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 1, 'Content': {'string': 'wxid_84mmq4cu7ita22:\n不就行了'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1753417472, 'MsgSource': '<msgsource>\n\t<sec_msg_node>\n\t\t<alnode>\n\t\t\t<fr>1</fr>\n\t\t</alnode>\n\t</sec_msg_node>\n\t<pua>1</pua>\n\t<silence>0</silence>\n\t<membercount>63</membercount>\n\t<signature>N0_V1_7M6Hgsal|v1_+gS2fldt</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'PushContent': '喵小叶 : 不就行了', 'NewMsgId': 6396456196116204883, 'MsgSeq': 871398525}
2025-07-25 12:24:10 | INFO | 收到文本消息: 消息ID:1119012497 来自:48097389945@chatroom 发送人:wxid_84mmq4cu7ita22 @:[] 内容:不就行了
2025-07-25 12:24:10 | DEBUG | 处理消息内容: '不就行了'
2025-07-25 12:24:10 | DEBUG | 消息内容 '不就行了' 不匹配任何命令，忽略
2025-07-25 12:24:14 | DEBUG | 收到消息: {'MsgId': 1805971231, 'FromUserName': {'string': '48097389945@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 1, 'Content': {'string': 'rongcheng6630606:\n@喵小叶\u2005我退不退群不关你事，不碍着你。你管不着'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1753417477, 'MsgSource': '<msgsource>\n\t<atuserlist><![CDATA[wxid_84mmq4cu7ita22]]></atuserlist>\n\t<pua>1</pua>\n\t<eggIncluded>1</eggIncluded>\n\t<silence>0</silence>\n\t<membercount>63</membercount>\n\t<signature>N0_V1_uUk+Vqrt|v1_hColbiH3</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'PushContent': '× : @喵小叶\u2005我退不退群不关你事，不碍着你。你管不着', 'NewMsgId': 3217587707817236928, 'MsgSeq': 871398526}
2025-07-25 12:24:14 | INFO | 收到文本消息: 消息ID:1805971231 来自:48097389945@chatroom 发送人:rongcheng6630606 @:['wxid_84mmq4cu7ita22'] 内容:@喵小叶 我退不退群不关你事，不碍着你。你管不着
2025-07-25 12:24:15 | DEBUG | 处理消息内容: '@喵小叶 我退不退群不关你事，不碍着你。你管不着'
2025-07-25 12:24:15 | DEBUG | 消息内容 '@喵小叶 我退不退群不关你事，不碍着你。你管不着' 不匹配任何命令，忽略
2025-07-25 12:24:23 | DEBUG | 收到消息: {'MsgId': 1096192694, 'FromUserName': {'string': '48097389945@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 1, 'Content': {'string': 'wxid_84mmq4cu7ita22:\n别艾特我'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1753417485, 'MsgSource': '<msgsource>\n\t<sec_msg_node>\n\t\t<alnode>\n\t\t\t<fr>1</fr>\n\t\t</alnode>\n\t</sec_msg_node>\n\t<pua>1</pua>\n\t<silence>0</silence>\n\t<membercount>63</membercount>\n\t<signature>N0_V1_x8bmo9og|v1_CCyaGR2g</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'PushContent': '喵小叶 : 别艾特我', 'NewMsgId': 5188038063719024900, 'MsgSeq': 871398527}
2025-07-25 12:24:23 | INFO | 收到文本消息: 消息ID:1096192694 来自:48097389945@chatroom 发送人:wxid_84mmq4cu7ita22 @:[] 内容:别艾特我
2025-07-25 12:24:23 | DEBUG | 处理消息内容: '别艾特我'
2025-07-25 12:24:23 | DEBUG | 消息内容 '别艾特我' 不匹配任何命令，忽略
2025-07-25 12:24:31 | DEBUG | 收到消息: {'MsgId': 1893913429, 'FromUserName': {'string': '55878994168@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 1, 'Content': {'string': 'wxid_ubbh6q832tcs21:\n开启美图模式'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1753417493, 'MsgSource': '<msgsource>\n\t<pua>1</pua>\n\t<eggIncluded>1</eggIncluded>\n\t<silence>0</silence>\n\t<membercount>3</membercount>\n\t<signature>N0_V1_dwhvpYtE|v1_NhA+nJB0</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'PushContent': '郭 : 开启美图模式', 'NewMsgId': 4952031842843232015, 'MsgSeq': 871398528}
2025-07-25 12:24:31 | INFO | 收到文本消息: 消息ID:1893913429 来自:55878994168@chatroom 发送人:wxid_ubbh6q832tcs21 @:[] 内容:开启美图模式
2025-07-25 12:24:32 | INFO | 发送文字消息: 对方wxid:55878994168@chatroom at: 内容:✅ 已进入美图AI模式，正在初始化...
2025-07-25 12:24:41 | INFO | 发送文字消息: 对方wxid:55878994168@chatroom at: 内容:嗨嗨~我是你的影像创作小助手RoboNeo！✨ 无论是修图、做设计还是剪视频，只要你说出想法，我就能帮你轻松实现~今天想创作点什么呢？(◕‿◕✿)
2025-07-25 12:24:41 | DEBUG | 收到消息: {'MsgId': 474450361, 'FromUserName': {'string': '48097389945@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 1, 'Content': {'string': 'rongcheng6630606:\n@喵小叶\u2005你好假[微笑]'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1753417497, 'MsgSource': '<msgsource>\n\t<atuserlist><![CDATA[wxid_84mmq4cu7ita22]]></atuserlist>\n\t<pua>1</pua>\n\t<eggIncluded>1</eggIncluded>\n\t<silence>0</silence>\n\t<membercount>63</membercount>\n\t<signature>N0_V1_h41FzQHm|v1_TNS8RJBT</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'PushContent': '× : @喵小叶\u2005你好假[微笑]', 'NewMsgId': 996612426438806472, 'MsgSeq': 871398531}
2025-07-25 12:24:41 | INFO | 收到文本消息: 消息ID:474450361 来自:48097389945@chatroom 发送人:rongcheng6630606 @:['wxid_84mmq4cu7ita22'] 内容:@喵小叶 你好假[微笑]
2025-07-25 12:24:42 | DEBUG | 处理消息内容: '@喵小叶 你好假[微笑]'
2025-07-25 12:24:42 | DEBUG | 消息内容 '@喵小叶 你好假[微笑]' 不匹配任何命令，忽略
2025-07-25 12:24:44 | DEBUG | 收到消息: {'MsgId': 798328626, 'FromUserName': {'string': '48097389945@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 1, 'Content': {'string': 'wxid_84mmq4cu7ita22:\n别委屈了'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1753417506, 'MsgSource': '<msgsource>\n\t<sec_msg_node>\n\t\t<alnode>\n\t\t\t<fr>1</fr>\n\t\t</alnode>\n\t</sec_msg_node>\n\t<pua>1</pua>\n\t<silence>0</silence>\n\t<membercount>63</membercount>\n\t<signature>N0_V1_sgiA0bTA|v1_ZjVkk7LA</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'PushContent': '喵小叶 : 别委屈了', 'NewMsgId': 5993489047125848415, 'MsgSeq': 871398534}
2025-07-25 12:24:44 | INFO | 收到文本消息: 消息ID:798328626 来自:48097389945@chatroom 发送人:wxid_84mmq4cu7ita22 @:[] 内容:别委屈了
2025-07-25 12:24:44 | DEBUG | 处理消息内容: '别委屈了'
2025-07-25 12:24:44 | DEBUG | 消息内容 '别委屈了' 不匹配任何命令，忽略
2025-07-25 12:24:48 | DEBUG | 收到消息: {'MsgId': 223023571, 'FromUserName': {'string': '55878994168@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 1, 'Content': {'string': 'wxid_ubbh6q832tcs21:\n画一个人'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1753417511, 'MsgSource': '<msgsource>\n\t<pua>1</pua>\n\t<eggIncluded>1</eggIncluded>\n\t<silence>0</silence>\n\t<membercount>3</membercount>\n\t<signature>N0_V1_9bRPpOgv|v1_VviNfDx9</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'PushContent': '郭 : 画一个人', 'NewMsgId': 873400858378966016, 'MsgSeq': 871398535}
2025-07-25 12:24:48 | INFO | 收到文本消息: 消息ID:223023571 来自:55878994168@chatroom 发送人:wxid_ubbh6q832tcs21 @:[] 内容:画一个人
2025-07-25 12:25:04 | INFO | 发送文字消息: 对方wxid:55878994168@chatroom at: 内容:🤖 AI询问: 你希望这个角色是什么风格？（例如：二次元动漫、写实风、Q版卡通、赛博朋克等）
这个角色有什么特别的特征或身份背景吗？（例如：魔法师、未来战士、学生等）
角色需要展示特定姿势或表情吗？（例如：战斗姿态、微笑表情等）
2025-07-25 12:25:04 | DEBUG | 收到消息: {'MsgId': 441407745, 'FromUserName': {'string': '48097389945@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 1, 'Content': {'string': 'rongcheng6630606:\n@喵小叶\u2005你好假[微笑]'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1753417513, 'MsgSource': '<msgsource>\n\t<pua>1</pua>\n\t<alnode>\n\t\t<cf>2</cf>\n\t\t<inlenlist>12</inlenlist>\n\t</alnode>\n\t<eggIncluded>1</eggIncluded>\n\t<silence>0</silence>\n\t<membercount>63</membercount>\n\t<signature>N0_V1_RrqjMsOF|v1_AlesucDR</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'PushContent': '× : @喵小叶\u2005你好假[微笑]', 'NewMsgId': 6626621110979444400, 'MsgSeq': 871398536}
2025-07-25 12:25:04 | INFO | 收到文本消息: 消息ID:441407745 来自:48097389945@chatroom 发送人:rongcheng6630606 @:[] 内容:@喵小叶 你好假[微笑]
2025-07-25 12:25:05 | DEBUG | 处理消息内容: '@喵小叶 你好假[微笑]'
2025-07-25 12:25:05 | DEBUG | 消息内容 '@喵小叶 你好假[微笑]' 不匹配任何命令，忽略
2025-07-25 12:25:07 | DEBUG | 收到消息: {'MsgId': 935047243, 'FromUserName': {'string': '48097389945@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 1, 'Content': {'string': 'rongcheng6630606:\n@喵小叶\u2005你好假[微笑]'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1753417515, 'MsgSource': '<msgsource>\n\t<pua>1</pua>\n\t<alnode>\n\t\t<cf>2</cf>\n\t\t<inlenlist>12</inlenlist>\n\t</alnode>\n\t<eggIncluded>1</eggIncluded>\n\t<silence>0</silence>\n\t<membercount>63</membercount>\n\t<signature>N0_V1_DgTeRgl6|v1_B/l6eJBn</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'PushContent': '× : @喵小叶\u2005你好假[微笑]', 'NewMsgId': 8479208393671593265, 'MsgSeq': 871398537}
2025-07-25 12:25:07 | INFO | 收到文本消息: 消息ID:935047243 来自:48097389945@chatroom 发送人:rongcheng6630606 @:[] 内容:@喵小叶 你好假[微笑]
2025-07-25 12:25:07 | DEBUG | 处理消息内容: '@喵小叶 你好假[微笑]'
2025-07-25 12:25:07 | DEBUG | 消息内容 '@喵小叶 你好假[微笑]' 不匹配任何命令，忽略
2025-07-25 12:25:09 | DEBUG | 收到消息: {'MsgId': 283227760, 'FromUserName': {'string': '48097389945@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 1, 'Content': {'string': 'rongcheng6630606:\n@喵小叶\u2005你好假[微笑]'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1753417516, 'MsgSource': '<msgsource>\n\t<pua>1</pua>\n\t<alnode>\n\t\t<cf>2</cf>\n\t\t<inlenlist>12</inlenlist>\n\t</alnode>\n\t<eggIncluded>1</eggIncluded>\n\t<silence>0</silence>\n\t<membercount>63</membercount>\n\t<signature>N0_V1_y2TP42pS|v1_0z42ja8y</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'PushContent': '× : @喵小叶\u2005你好假[微笑]', 'NewMsgId': 2118472764038669150, 'MsgSeq': 871398538}
2025-07-25 12:25:09 | INFO | 收到文本消息: 消息ID:283227760 来自:48097389945@chatroom 发送人:rongcheng6630606 @:[] 内容:@喵小叶 你好假[微笑]
2025-07-25 12:25:09 | DEBUG | 处理消息内容: '@喵小叶 你好假[微笑]'
2025-07-25 12:25:09 | DEBUG | 消息内容 '@喵小叶 你好假[微笑]' 不匹配任何命令，忽略
2025-07-25 12:25:24 | DEBUG | 收到消息: {'MsgId': 2030942757, 'FromUserName': {'string': '55878994168@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 1, 'Content': {'string': 'wxid_ubbh6q832tcs21:\n二次元，学生，少女'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1753417546, 'MsgSource': '<msgsource>\n\t<pua>1</pua>\n\t<eggIncluded>1</eggIncluded>\n\t<silence>0</silence>\n\t<membercount>3</membercount>\n\t<signature>N0_V1_j2CNnw8I|v1_XaIc4XFR</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'PushContent': '郭 : 二次元，学生，少女', 'NewMsgId': 1156609242465809821, 'MsgSeq': 871398541}
2025-07-25 12:25:24 | INFO | 收到文本消息: 消息ID:2030942757 来自:55878994168@chatroom 发送人:wxid_ubbh6q832tcs21 @:[] 内容:二次元，学生，少女
2025-07-25 12:27:25 | INFO | 发送文字消息: 对方wxid:55878994168@chatroom at: 内容:正在为你设计一个二次元风格的少女学生形象，她会穿着校服，有着黑长发和大眼睛，表情阳光可爱~
2025-07-25 12:27:25 | DEBUG | 收到消息: {'MsgId': 285010202, 'FromUserName': {'string': '48097389945@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 1, 'Content': {'string': 'wxid_84mmq4cu7ita22:\n你想想你说的那些信息'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1753417564, 'MsgSource': '<msgsource>\n\t<sec_msg_node>\n\t\t<alnode>\n\t\t\t<fr>1</fr>\n\t\t</alnode>\n\t</sec_msg_node>\n\t<pua>1</pua>\n\t<silence>0</silence>\n\t<membercount>63</membercount>\n\t<signature>N0_V1_khmg1u9o|v1_8wkO/jX9</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'PushContent': '喵小叶 : 你想想你说的那些信息', 'NewMsgId': 4730866131180002158, 'MsgSeq': 871398542}
2025-07-25 12:27:25 | INFO | 收到文本消息: 消息ID:285010202 来自:48097389945@chatroom 发送人:wxid_84mmq4cu7ita22 @:[] 内容:你想想你说的那些信息
2025-07-25 12:27:25 | DEBUG | 处理消息内容: '你想想你说的那些信息'
2025-07-25 12:27:25 | DEBUG | 消息内容 '你想想你说的那些信息' 不匹配任何命令，忽略
2025-07-25 12:27:27 | DEBUG | 收到消息: {'MsgId': 739958300, 'FromUserName': {'string': '48097389945@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 1, 'Content': {'string': 'wxid_84mmq4cu7ita22:\n是不是伤害了我'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1753417568, 'MsgSource': '<msgsource>\n\t<sec_msg_node>\n\t\t<alnode>\n\t\t\t<fr>1</fr>\n\t\t</alnode>\n\t</sec_msg_node>\n\t<pua>1</pua>\n\t<silence>0</silence>\n\t<membercount>63</membercount>\n\t<signature>N0_V1_k2Eay+Rj|v1_fZ+8PBMK</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'PushContent': '喵小叶 : 是不是伤害了我', 'NewMsgId': 7936092846619846045, 'MsgSeq': 871398543}
2025-07-25 12:27:27 | INFO | 收到文本消息: 消息ID:739958300 来自:48097389945@chatroom 发送人:wxid_84mmq4cu7ita22 @:[] 内容:是不是伤害了我
2025-07-25 12:27:28 | DEBUG | 处理消息内容: '是不是伤害了我'
2025-07-25 12:27:28 | DEBUG | 消息内容 '是不是伤害了我' 不匹配任何命令，忽略
2025-07-25 12:27:30 | DEBUG | 收到消息: {'MsgId': 1365852116, 'FromUserName': {'string': '48097389945@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 1, 'Content': {'string': 'rongcheng6630606:\n去年3月到今年5月请你难么多茶饮喂狗了@喵小叶\u2005'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1753417579, 'MsgSource': '<msgsource>\n\t<atuserlist><![CDATA[wxid_84mmq4cu7ita22]]></atuserlist>\n\t<pua>1</pua>\n\t<eggIncluded>1</eggIncluded>\n\t<silence>0</silence>\n\t<membercount>63</membercount>\n\t<signature>N0_V1_AejCk86s|v1_YLIRzdOK</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'PushContent': '× : 去年3月到今年5月请你难么多茶饮喂狗了@喵小叶\u2005', 'NewMsgId': 2789198516953336580, 'MsgSeq': 871398544}
2025-07-25 12:27:30 | INFO | 收到文本消息: 消息ID:1365852116 来自:48097389945@chatroom 发送人:rongcheng6630606 @:['wxid_84mmq4cu7ita22'] 内容:去年3月到今年5月请你难么多茶饮喂狗了@喵小叶 
2025-07-25 12:27:30 | DEBUG | 处理消息内容: '去年3月到今年5月请你难么多茶饮喂狗了@喵小叶'
2025-07-25 12:27:30 | DEBUG | 消息内容 '去年3月到今年5月请你难么多茶饮喂狗了@喵小叶' 不匹配任何命令，忽略
2025-07-25 12:27:32 | DEBUG | 收到消息: {'MsgId': 2055198875, 'FromUserName': {'string': '48097389945@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 1, 'Content': {'string': 'rongcheng6630606:\n去年3月到今年5月请你难么多茶饮喂狗了@喵小叶\u2005'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1753417582, 'MsgSource': '<msgsource>\n\t<pua>1</pua>\n\t<alnode>\n\t\t<cf>4</cf>\n\t\t<inlenlist>24</inlenlist>\n\t</alnode>\n\t<eggIncluded>1</eggIncluded>\n\t<silence>0</silence>\n\t<membercount>63</membercount>\n\t<signature>N0_V1_xCfHHhx+|v1_9qo6isLv</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'PushContent': '× : 去年3月到今年5月请你难么多茶饮喂狗了@喵小叶\u2005', 'NewMsgId': 5711653166150691390, 'MsgSeq': 871398545}
2025-07-25 12:27:32 | INFO | 收到文本消息: 消息ID:2055198875 来自:48097389945@chatroom 发送人:rongcheng6630606 @:[] 内容:去年3月到今年5月请你难么多茶饮喂狗了@喵小叶 
2025-07-25 12:27:32 | DEBUG | 处理消息内容: '去年3月到今年5月请你难么多茶饮喂狗了@喵小叶'
2025-07-25 12:27:32 | DEBUG | 消息内容 '去年3月到今年5月请你难么多茶饮喂狗了@喵小叶' 不匹配任何命令，忽略
2025-07-25 12:27:34 | DEBUG | 收到消息: {'MsgId': 844354732, 'FromUserName': {'string': '48097389945@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 1, 'Content': {'string': 'rongcheng6630606:\n去年3月到今年5月请你难么多茶饮喂狗了@喵小叶\u2005'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1753417583, 'MsgSource': '<msgsource>\n\t<pua>1</pua>\n\t<alnode>\n\t\t<cf>2</cf>\n\t\t<inlenlist>24</inlenlist>\n\t</alnode>\n\t<eggIncluded>1</eggIncluded>\n\t<silence>0</silence>\n\t<membercount>63</membercount>\n\t<signature>N0_V1_VXpTBI6L|v1_i62YBPyg</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'PushContent': '× : 去年3月到今年5月请你难么多茶饮喂狗了@喵小叶\u2005', 'NewMsgId': 3916060545953592713, 'MsgSeq': 871398546}
2025-07-25 12:27:34 | INFO | 收到文本消息: 消息ID:844354732 来自:48097389945@chatroom 发送人:rongcheng6630606 @:[] 内容:去年3月到今年5月请你难么多茶饮喂狗了@喵小叶 
2025-07-25 12:27:34 | DEBUG | 处理消息内容: '去年3月到今年5月请你难么多茶饮喂狗了@喵小叶'
2025-07-25 12:27:34 | DEBUG | 消息内容 '去年3月到今年5月请你难么多茶饮喂狗了@喵小叶' 不匹配任何命令，忽略
2025-07-25 12:27:36 | DEBUG | 收到消息: {'MsgId': 26285859, 'FromUserName': {'string': '48097389945@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 1, 'Content': {'string': 'wxid_84mmq4cu7ita22:\n你撤回什么我都看见了'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1753417618, 'MsgSource': '<msgsource>\n\t<sec_msg_node>\n\t\t<alnode>\n\t\t\t<fr>1</fr>\n\t\t</alnode>\n\t</sec_msg_node>\n\t<pua>1</pua>\n\t<silence>0</silence>\n\t<membercount>63</membercount>\n\t<signature>N0_V1_pz27aA3D|v1_X16icHIv</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'PushContent': '喵小叶 : 你撤回什么我都看见了', 'NewMsgId': 946672591493569767, 'MsgSeq': 871398547}
2025-07-25 12:27:36 | INFO | 收到文本消息: 消息ID:26285859 来自:48097389945@chatroom 发送人:wxid_84mmq4cu7ita22 @:[] 内容:你撤回什么我都看见了
2025-07-25 12:27:36 | DEBUG | 处理消息内容: '你撤回什么我都看见了'
2025-07-25 12:27:36 | DEBUG | 消息内容 '你撤回什么我都看见了' 不匹配任何命令，忽略
2025-07-25 12:27:38 | DEBUG | 收到消息: {'MsgId': 34687699, 'FromUserName': {'string': '48097389945@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 1, 'Content': {'string': 'rongcheng6630606:\n王鹏叫人把咱两踢出二群'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1753417620, 'MsgSource': '<msgsource>\n\t<pua>1</pua>\n\t<eggIncluded>1</eggIncluded>\n\t<silence>0</silence>\n\t<membercount>63</membercount>\n\t<signature>N0_V1_x5E8/uCT|v1_0ieJOwR0</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'PushContent': '× : 王鹏叫人把咱两踢出二群', 'NewMsgId': 8628896686500236005, 'MsgSeq': 871398548}
2025-07-25 12:27:38 | INFO | 收到文本消息: 消息ID:34687699 来自:48097389945@chatroom 发送人:rongcheng6630606 @:[] 内容:王鹏叫人把咱两踢出二群
2025-07-25 12:27:38 | DEBUG | 处理消息内容: '王鹏叫人把咱两踢出二群'
2025-07-25 12:27:38 | DEBUG | 消息内容 '王鹏叫人把咱两踢出二群' 不匹配任何命令，忽略
2025-07-25 12:27:40 | DEBUG | 收到消息: {'MsgId': 1613623560, 'FromUserName': {'string': '48097389945@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 1, 'Content': {'string': 'wxid_84mmq4cu7ita22:\n让你说，你又不说'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1753417623, 'MsgSource': '<msgsource>\n\t<sec_msg_node>\n\t\t<alnode>\n\t\t\t<fr>1</fr>\n\t\t</alnode>\n\t</sec_msg_node>\n\t<pua>1</pua>\n\t<silence>0</silence>\n\t<membercount>63</membercount>\n\t<signature>N0_V1_zbtYyfzu|v1_o8Dm59aZ</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'PushContent': '喵小叶 : 让你说，你又不说', 'NewMsgId': 2424691182753637575, 'MsgSeq': 871398549}
2025-07-25 12:27:40 | INFO | 收到文本消息: 消息ID:1613623560 来自:48097389945@chatroom 发送人:wxid_84mmq4cu7ita22 @:[] 内容:让你说，你又不说
2025-07-25 12:27:40 | DEBUG | 处理消息内容: '让你说，你又不说'
2025-07-25 12:27:40 | DEBUG | 消息内容 '让你说，你又不说' 不匹配任何命令，忽略
2025-07-25 12:27:42 | DEBUG | 收到消息: {'MsgId': 2127482581, 'FromUserName': {'string': '48097389945@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 1, 'Content': {'string': 'wxid_84mmq4cu7ita22:\n你现在说又干啥'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1753417631, 'MsgSource': '<msgsource>\n\t<sec_msg_node>\n\t\t<alnode>\n\t\t\t<fr>1</fr>\n\t\t</alnode>\n\t</sec_msg_node>\n\t<pua>1</pua>\n\t<silence>0</silence>\n\t<membercount>63</membercount>\n\t<signature>N0_V1_gsx+ZSKW|v1_YcX9xRpS</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'PushContent': '喵小叶 : 你现在说又干啥', 'NewMsgId': 1051932641262244594, 'MsgSeq': 871398550}
2025-07-25 12:27:42 | INFO | 收到文本消息: 消息ID:2127482581 来自:48097389945@chatroom 发送人:wxid_84mmq4cu7ita22 @:[] 内容:你现在说又干啥
2025-07-25 12:27:43 | DEBUG | 处理消息内容: '你现在说又干啥'
2025-07-25 12:27:43 | DEBUG | 消息内容 '你现在说又干啥' 不匹配任何命令，忽略
2025-07-25 12:27:44 | DEBUG | 收到消息: {'MsgId': 1645798250, 'FromUserName': {'string': '48097389945@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 1, 'Content': {'string': 'rongcheng6630606:\n我还一个劲提别人说你好话'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1753417637, 'MsgSource': '<msgsource>\n\t<pua>1</pua>\n\t<eggIncluded>1</eggIncluded>\n\t<silence>0</silence>\n\t<membercount>63</membercount>\n\t<signature>N0_V1_4qAjo8D9|v1_n6EmIREy</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'PushContent': '× : 我还一个劲提别人说你好话', 'NewMsgId': 2248224515417726627, 'MsgSeq': 871398551}
2025-07-25 12:27:44 | INFO | 收到文本消息: 消息ID:1645798250 来自:48097389945@chatroom 发送人:rongcheng6630606 @:[] 内容:我还一个劲提别人说你好话
2025-07-25 12:27:45 | DEBUG | 处理消息内容: '我还一个劲提别人说你好话'
2025-07-25 12:27:45 | DEBUG | 消息内容 '我还一个劲提别人说你好话' 不匹配任何命令，忽略
2025-07-25 12:27:47 | DEBUG | 收到消息: {'MsgId': 495409218, 'FromUserName': {'string': '48097389945@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 49, 'Content': {'string': 'wxid_84mmq4cu7ita22:\n<?xml version="1.0"?>\n<msg>\n\t<appmsg appid="" sdkver="0">\n\t\t<title>我不是跟你说了，我猜到了</title>\n\t\t<des />\n\t\t<action>view</action>\n\t\t<type>57</type>\n\t\t<showtype>0</showtype>\n\t\t<content />\n\t\t<url />\n\t\t<dataurl />\n\t\t<lowurl />\n\t\t<lowdataurl />\n\t\t<recorditem />\n\t\t<thumburl />\n\t\t<messageaction />\n\t\t<laninfo />\n\t\t<refermsg>\n\t\t\t<type>1</type>\n\t\t\t<svrid>8628896686500236005</svrid>\n\t\t\t<fromusr>48097389945@chatroom</fromusr>\n\t\t\t<chatusr>rongcheng6630606</chatusr>\n\t\t\t<createtime>1753417620</createtime>\n\t\t\t<msgsource>&lt;msgsource&gt;\n    &lt;pua&gt;1&lt;/pua&gt;\n    &lt;eggIncluded&gt;1&lt;/eggIncluded&gt;\n    &lt;silence&gt;1&lt;/silence&gt;\n    &lt;membercount&gt;63&lt;/membercount&gt;\n    &lt;signature&gt;N0_V1_DQBDYeIS|v1_CGUL81x9&lt;/signature&gt;\n    &lt;tmp_node&gt;\n        &lt;publisher-id /&gt;\n    &lt;/tmp_node&gt;\n    &lt;sec_msg_node&gt;\n        &lt;alnode&gt;\n            &lt;fr&gt;1&lt;/fr&gt;\n        &lt;/alnode&gt;\n    &lt;/sec_msg_node&gt;\n&lt;/msgsource&gt;\n</msgsource>\n\t\t\t<content>王鹏叫人把咱两踢出二群</content>\n\t\t\t<displayname>×</displayname>\n\t\t</refermsg>\n\t\t<extinfo />\n\t\t<sourceusername />\n\t\t<sourcedisplayname />\n\t\t<commenturl />\n\t\t<appattach>\n\t\t\t<totallen>0</totallen>\n\t\t\t<attachid />\n\t\t\t<emoticonmd5 />\n\t\t\t<fileext />\n\t\t\t<aeskey />\n\t\t</appattach>\n\t\t<webviewshared>\n\t\t\t<publisherId />\n\t\t\t<publisherReqId>0</publisherReqId>\n\t\t</webviewshared>\n\t\t<weappinfo>\n\t\t\t<pagepath />\n\t\t\t<username />\n\t\t\t<appid />\n\t\t\t<appservicetype>0</appservicetype>\n\t\t</weappinfo>\n\t\t<websearch />\n\t</appmsg>\n\t<fromusername>wxid_84mmq4cu7ita22</fromusername>\n\t<scene>0</scene>\n\t<appinfo>\n\t\t<version>1</version>\n\t\t<appname></appname>\n\t</appinfo>\n\t<commenturl></commenturl>\n</msg>\n'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1753417645, 'MsgSource': '<msgsource>\n\t<pua>1</pua>\n\t<sec_msg_node>\n\t\t<uuid>d048402b6ba939bb20fbd9937d0cfe9b_</uuid>\n\t\t<risk-file-flag />\n\t\t<risk-file-md5-list />\n\t</sec_msg_node>\n\t<silence>0</silence>\n\t<membercount>63</membercount>\n\t<signature>N0_V1_XuS+1cT8|v1_p3AHdsiF</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'PushContent': '喵小叶 : 我不是跟你说了，我猜到了', 'NewMsgId': 353537985176612640, 'MsgSeq': 871398552}
2025-07-25 12:27:47 | DEBUG | 从群聊消息中提取发送者: wxid_84mmq4cu7ita22
2025-07-25 12:27:47 | DEBUG | 使用已解析的XML处理引用消息
2025-07-25 12:27:47 | INFO | 收到引用消息: 消息ID:495409218 来自:48097389945@chatroom 发送人:wxid_84mmq4cu7ita22 内容:我不是跟你说了，我猜到了 引用类型:1
2025-07-25 12:27:47 | INFO | [DouBaoImageToImage] 收到引用消息: 我不是跟你说了，我猜到了
2025-07-25 12:27:47 | INFO | [TimerTask] 收到引用消息调试信息:
2025-07-25 12:27:47 | INFO |   - 消息内容: 我不是跟你说了，我猜到了
2025-07-25 12:27:47 | INFO |   - 群组ID: 48097389945@chatroom
2025-07-25 12:27:47 | INFO |   - 发送人: wxid_84mmq4cu7ita22
2025-07-25 12:27:47 | INFO |   - 引用信息: {'MsgType': 1, 'Content': '王鹏叫人把咱两踢出二群', 'Msgid': '8628896686500236005', 'NewMsgId': '8628896686500236005', 'ToWxid': 'wxid_4usgcju5ey9q29', 'FromWxid': '48097389945@chatroom', 'Nickname': '×', 'MsgSource': '<msgsource>\n    <pua>1</pua>\n    <eggIncluded>1</eggIncluded>\n    <silence>1</silence>\n    <membercount>63</membercount>\n    <signature>N0_V1_DQBDYeIS|v1_CGUL81x9</signature>\n    <tmp_node>\n        <publisher-id />\n    </tmp_node>\n    <sec_msg_node>\n        <alnode>\n            <fr>1</fr>\n        </alnode>\n    </sec_msg_node>\n</msgsource>\n', 'Createtime': '1753417620', 'SenderWxid': 'wxid_84mmq4cu7ita22'}
2025-07-25 12:27:47 | INFO |   - 引用消息ID: 
2025-07-25 12:27:47 | INFO |   - 引用消息类型: 
2025-07-25 12:27:47 | INFO |   - 引用消息内容: 王鹏叫人把咱两踢出二群
2025-07-25 12:27:47 | INFO |   - 引用消息发送人: wxid_84mmq4cu7ita22
2025-07-25 12:27:47 | DEBUG | 收到消息: {'MsgId': 847435549, 'FromUserName': {'string': '48097389945@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 1, 'Content': {'string': 'rongcheng6630606:\n最后两头没讨好，变成我是你的舔狗'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1753417652, 'MsgSource': '<msgsource>\n\t<pua>1</pua>\n\t<eggIncluded>1</eggIncluded>\n\t<silence>0</silence>\n\t<membercount>63</membercount>\n\t<signature>N0_V1_gZNcva4Z|v1_78OQtbId</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'PushContent': '× : 最后两头没讨好，变成我是你的舔狗', 'NewMsgId': 4180310098855546579, 'MsgSeq': 871398553}
2025-07-25 12:27:47 | INFO | 收到文本消息: 消息ID:847435549 来自:48097389945@chatroom 发送人:rongcheng6630606 @:[] 内容:最后两头没讨好，变成我是你的舔狗
2025-07-25 12:27:48 | DEBUG | 处理消息内容: '最后两头没讨好，变成我是你的舔狗'
2025-07-25 12:27:48 | DEBUG | 消息内容 '最后两头没讨好，变成我是你的舔狗' 不匹配任何命令，忽略
2025-07-25 12:27:49 | DEBUG | 收到消息: {'MsgId': 1418643244, 'FromUserName': {'string': '48097389945@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 49, 'Content': {'string': 'wxid_84mmq4cu7ita22:\n<?xml version="1.0"?>\n<msg>\n\t<appmsg appid="" sdkver="0">\n\t\t<title>不是，你为啥要讨好人</title>\n\t\t<des />\n\t\t<action>view</action>\n\t\t<type>57</type>\n\t\t<showtype>0</showtype>\n\t\t<content />\n\t\t<url />\n\t\t<dataurl />\n\t\t<lowurl />\n\t\t<lowdataurl />\n\t\t<recorditem />\n\t\t<thumburl />\n\t\t<messageaction />\n\t\t<laninfo />\n\t\t<refermsg>\n\t\t\t<type>1</type>\n\t\t\t<svrid>4180310098855546579</svrid>\n\t\t\t<fromusr>48097389945@chatroom</fromusr>\n\t\t\t<chatusr>rongcheng6630606</chatusr>\n\t\t\t<createtime>1753417652</createtime>\n\t\t\t<msgsource>&lt;msgsource&gt;\n    &lt;pua&gt;1&lt;/pua&gt;\n    &lt;eggIncluded&gt;1&lt;/eggIncluded&gt;\n    &lt;silence&gt;1&lt;/silence&gt;\n    &lt;membercount&gt;63&lt;/membercount&gt;\n    &lt;signature&gt;N0_V1_H0ztynqc|v1_6fYJJ2BD&lt;/signature&gt;\n    &lt;tmp_node&gt;\n        &lt;publisher-id /&gt;\n    &lt;/tmp_node&gt;\n    &lt;sec_msg_node&gt;\n        &lt;alnode&gt;\n            &lt;fr&gt;1&lt;/fr&gt;\n        &lt;/alnode&gt;\n    &lt;/sec_msg_node&gt;\n&lt;/msgsource&gt;\n</msgsource>\n\t\t\t<content>最后两头没讨好，变成我是你的舔狗</content>\n\t\t\t<displayname>×</displayname>\n\t\t</refermsg>\n\t\t<extinfo />\n\t\t<sourceusername />\n\t\t<sourcedisplayname />\n\t\t<commenturl />\n\t\t<appattach>\n\t\t\t<totallen>0</totallen>\n\t\t\t<attachid />\n\t\t\t<emoticonmd5 />\n\t\t\t<fileext />\n\t\t\t<aeskey />\n\t\t</appattach>\n\t\t<webviewshared>\n\t\t\t<publisherId />\n\t\t\t<publisherReqId>0</publisherReqId>\n\t\t</webviewshared>\n\t\t<weappinfo>\n\t\t\t<pagepath />\n\t\t\t<username />\n\t\t\t<appid />\n\t\t\t<appservicetype>0</appservicetype>\n\t\t</weappinfo>\n\t\t<websearch />\n\t</appmsg>\n\t<fromusername>wxid_84mmq4cu7ita22</fromusername>\n\t<scene>0</scene>\n\t<appinfo>\n\t\t<version>1</version>\n\t\t<appname></appname>\n\t</appinfo>\n\t<commenturl></commenturl>\n</msg>\n'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1753417673, 'MsgSource': '<msgsource>\n\t<pua>1</pua>\n\t<sec_msg_node>\n\t\t<uuid>45b41c5dcb92e0c38b6b9254e6054681_</uuid>\n\t\t<risk-file-flag />\n\t\t<risk-file-md5-list />\n\t</sec_msg_node>\n\t<silence>0</silence>\n\t<membercount>63</membercount>\n\t<signature>N0_V1_7xn1bUed|v1_q8c/aYF5</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'PushContent': '喵小叶 : 不是，你为啥要讨好人', 'NewMsgId': 2556529629496715298, 'MsgSeq': 871398556}
2025-07-25 12:27:49 | DEBUG | 从群聊消息中提取发送者: wxid_84mmq4cu7ita22
2025-07-25 12:27:49 | DEBUG | 使用已解析的XML处理引用消息
2025-07-25 12:27:49 | INFO | 收到引用消息: 消息ID:1418643244 来自:48097389945@chatroom 发送人:wxid_84mmq4cu7ita22 内容:不是，你为啥要讨好人 引用类型:1
2025-07-25 12:27:50 | INFO | [DouBaoImageToImage] 收到引用消息: 不是，你为啥要讨好人
2025-07-25 12:27:50 | INFO | [TimerTask] 收到引用消息调试信息:
2025-07-25 12:27:50 | INFO |   - 消息内容: 不是，你为啥要讨好人
2025-07-25 12:27:50 | INFO |   - 群组ID: 48097389945@chatroom
2025-07-25 12:27:50 | INFO |   - 发送人: wxid_84mmq4cu7ita22
2025-07-25 12:27:50 | INFO |   - 引用信息: {'MsgType': 1, 'Content': '最后两头没讨好，变成我是你的舔狗', 'Msgid': '4180310098855546579', 'NewMsgId': '4180310098855546579', 'ToWxid': 'wxid_4usgcju5ey9q29', 'FromWxid': '48097389945@chatroom', 'Nickname': '×', 'MsgSource': '<msgsource>\n    <pua>1</pua>\n    <eggIncluded>1</eggIncluded>\n    <silence>1</silence>\n    <membercount>63</membercount>\n    <signature>N0_V1_H0ztynqc|v1_6fYJJ2BD</signature>\n    <tmp_node>\n        <publisher-id />\n    </tmp_node>\n    <sec_msg_node>\n        <alnode>\n            <fr>1</fr>\n        </alnode>\n    </sec_msg_node>\n</msgsource>\n', 'Createtime': '1753417652', 'SenderWxid': 'wxid_84mmq4cu7ita22'}
2025-07-25 12:27:50 | INFO |   - 引用消息ID: 
2025-07-25 12:27:50 | INFO |   - 引用消息类型: 
2025-07-25 12:27:50 | INFO |   - 引用消息内容: 最后两头没讨好，变成我是你的舔狗
2025-07-25 12:27:50 | INFO |   - 引用消息发送人: wxid_84mmq4cu7ita22
2025-07-25 12:27:50 | DEBUG | 收到消息: {'MsgId': 1607016910, 'FromUserName': {'string': '48097389945@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 1, 'Content': {'string': 'wxid_84mmq4cu7ita22:\n你完全可以做你自己，不需要讨好任何人'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1753417685, 'MsgSource': '<msgsource>\n\t<sec_msg_node>\n\t\t<alnode>\n\t\t\t<fr>1</fr>\n\t\t</alnode>\n\t</sec_msg_node>\n\t<pua>1</pua>\n\t<silence>0</silence>\n\t<membercount>63</membercount>\n\t<signature>N0_V1_oiVKJOx9|v1_ci55v7Ym</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'PushContent': '喵小叶 : 你完全可以做你自己，不需要讨好任何人', 'NewMsgId': 2378497650402126905, 'MsgSeq': 871398557}
2025-07-25 12:27:50 | INFO | 收到文本消息: 消息ID:1607016910 来自:48097389945@chatroom 发送人:wxid_84mmq4cu7ita22 @:[] 内容:你完全可以做你自己，不需要讨好任何人
2025-07-25 12:27:50 | DEBUG | 处理消息内容: '你完全可以做你自己，不需要讨好任何人'
2025-07-25 12:27:50 | DEBUG | 消息内容 '你完全可以做你自己，不需要讨好任何人' 不匹配任何命令，忽略
2025-07-25 12:27:52 | DEBUG | 收到消息: {'MsgId': 799289187, 'FromUserName': {'string': '48097389945@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 47, 'Content': {'string': 'wxid_l9koi6kli78i22:\n<msg><emoji fromusername="wxid_l9koi6kli78i22" tousername="48097389945@chatroom" type="2" idbuffer="media:0_0" md5="26cff38f7ef8e30f70f8efa99e253d40" len="1603" productid="" androidmd5="26cff38f7ef8e30f70f8efa99e253d40" androidlen="1603" s60v3md5="26cff38f7ef8e30f70f8efa99e253d40" s60v3len="1603" s60v5md5="26cff38f7ef8e30f70f8efa99e253d40" s60v5len="1603" cdnurl="http://wxapp.tc.qq.com/262/20304/stodownload?m=26cff38f7ef8e30f70f8efa99e253d40&amp;filekey=30340201010420301e020201060402535a041026cff38f7ef8e30f70f8efa99e253d4002020643040d00000004627466730000000131&amp;hy=SZ&amp;storeid=32303232303430323137303831333030306565653432323537333238303233643938386530623030303030313036&amp;bizid=1023" designerid="" thumburl="" encrypturl="http://wxapp.tc.qq.com/262/20304/stodownload?m=e20001063ca7ddf06e91a6250ef1408e&amp;filekey=30340201010420301e020201060402535a0410e20001063ca7ddf06e91a6250ef1408e02020650040d00000004627466730000000131&amp;hy=SZ&amp;storeid=32303232303430323137303831343030303135333262323537333238303261303938386530623030303030313036&amp;bizid=1023" aeskey="d452090adf6b177a02d117db254fd225" externurl="http://wxapp.tc.qq.com/262/20304/stodownload?m=ab895de8340dba32ff49ca58c1fe48d9&amp;filekey=30340201010420301e020201060402535a0410ab895de8340dba32ff49ca58c1fe48d9020201f0040d00000004627466730000000131&amp;hy=SZ&amp;storeid=32303232303430323137303831343030303237353636323537333238303230613939386530623030303030313036&amp;bizid=1023" externmd5="60e16e2f672f8c53cf5a7659e4309654" width="150" height="44" tpurl="" tpauthkey="" attachedtext="" attachedtextcolor="" lensid="" emojiattr="" linkid="" desc=""></emoji></msg>'}, 'Status': 3, 'ImgStatus': 2, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1753417690, 'MsgSource': '<msgsource>\n\t<silence>0</silence>\n\t<membercount>63</membercount>\n\t<signature>N0_V1_rPETFZxe|v1_xhmF5wdp</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'PushContent': '十五在群聊中发了一个表情', 'NewMsgId': 6917242342463782985, 'MsgSeq': 871398558}
2025-07-25 12:27:52 | INFO | 收到表情消息: 消息ID:799289187 来自:48097389945@chatroom 发送人:wxid_l9koi6kli78i22 MD5:26cff38f7ef8e30f70f8efa99e253d40 大小:1603
2025-07-25 12:27:53 | DEBUG | [DoubaoImageRecognition] 缓存表情消息: 6917242342463782985
2025-07-25 12:27:53 | DEBUG | 收到消息: {'MsgId': 1592776020, 'FromUserName': {'string': '48097389945@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 1, 'Content': {'string': 'wxid_84mmq4cu7ita22:\n我有时候觉得和你好难沟通'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1753417691, 'MsgSource': '<msgsource>\n\t<sec_msg_node>\n\t\t<alnode>\n\t\t\t<fr>1</fr>\n\t\t</alnode>\n\t</sec_msg_node>\n\t<pua>1</pua>\n\t<silence>0</silence>\n\t<membercount>63</membercount>\n\t<signature>N0_V1_FxFqtIqL|v1_1V1RWazV</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'PushContent': '喵小叶 : 我有时候觉得和你好难沟通', 'NewMsgId': 2936673321450129703, 'MsgSeq': 871398559}
2025-07-25 12:27:53 | INFO | 收到文本消息: 消息ID:1592776020 来自:48097389945@chatroom 发送人:wxid_84mmq4cu7ita22 @:[] 内容:我有时候觉得和你好难沟通
2025-07-25 12:27:53 | DEBUG | 处理消息内容: '我有时候觉得和你好难沟通'
2025-07-25 12:27:53 | DEBUG | 消息内容 '我有时候觉得和你好难沟通' 不匹配任何命令，忽略
2025-07-25 12:28:00 | DEBUG | 收到消息: {'MsgId': 826665138, 'FromUserName': {'string': '48097389945@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 1, 'Content': {'string': 'rongcheng6630606:\n我请你喝那么多茶饮，6月随口说让你请我喝瑞幸。却变成我怎么老是找你要钱'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1753417703, 'MsgSource': '<msgsource>\n\t<pua>1</pua>\n\t<eggIncluded>1</eggIncluded>\n\t<silence>0</silence>\n\t<membercount>63</membercount>\n\t<signature>N0_V1_99qIgOrh|v1_WKdBhNt/</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'PushContent': '× : 我请你喝那么多茶饮，6月随口说让你请我喝瑞幸。却变成我怎么老...', 'NewMsgId': 2944966610981693069, 'MsgSeq': 871398560}
2025-07-25 12:28:00 | INFO | 收到文本消息: 消息ID:826665138 来自:48097389945@chatroom 发送人:rongcheng6630606 @:[] 内容:我请你喝那么多茶饮，6月随口说让你请我喝瑞幸。却变成我怎么老是找你要钱
2025-07-25 12:28:01 | DEBUG | 处理消息内容: '我请你喝那么多茶饮，6月随口说让你请我喝瑞幸。却变成我怎么老是找你要钱'
2025-07-25 12:28:01 | DEBUG | 消息内容 '我请你喝那么多茶饮，6月随口说让你请我喝瑞幸。却变成我怎么老是找你要钱' 不匹配任何命令，忽略
2025-07-25 12:28:09 | DEBUG | 收到消息: {'MsgId': 730962298, 'FromUserName': {'string': '48097389945@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 1, 'Content': {'string': 'wxid_84mmq4cu7ita22:\n你的每句话，我都想吵架'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1753417712, 'MsgSource': '<msgsource>\n\t<sec_msg_node>\n\t\t<alnode>\n\t\t\t<fr>1</fr>\n\t\t</alnode>\n\t</sec_msg_node>\n\t<pua>1</pua>\n\t<silence>0</silence>\n\t<membercount>63</membercount>\n\t<signature>N0_V1_UDH2TfIl|v1_ojmiulWT</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'PushContent': '喵小叶 : 你的每句话，我都想吵架', 'NewMsgId': 495146048677870430, 'MsgSeq': 871398561}
2025-07-25 12:28:09 | INFO | 收到文本消息: 消息ID:730962298 来自:48097389945@chatroom 发送人:wxid_84mmq4cu7ita22 @:[] 内容:你的每句话，我都想吵架
2025-07-25 12:28:10 | DEBUG | 处理消息内容: '你的每句话，我都想吵架'
2025-07-25 12:28:10 | DEBUG | 消息内容 '你的每句话，我都想吵架' 不匹配任何命令，忽略
2025-07-25 12:28:35 | DEBUG | 收到消息: {'MsgId': 412604499, 'FromUserName': {'string': '48097389945@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 1, 'Content': {'string': 'wxid_84mmq4cu7ita22:\n你随口那么多句，有时候给，有时候就不乐意给'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1753417738, 'MsgSource': '<msgsource>\n\t<sec_msg_node>\n\t\t<alnode>\n\t\t\t<fr>1</fr>\n\t\t</alnode>\n\t</sec_msg_node>\n\t<pua>1</pua>\n\t<silence>0</silence>\n\t<membercount>63</membercount>\n\t<signature>N0_V1_nhkzbmxe|v1_CaHAenMF</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'PushContent': '喵小叶 : 你随口那么多句，有时候给，有时候就不乐意给', 'NewMsgId': 1375443815064775201, 'MsgSeq': 871398562}
2025-07-25 12:28:35 | INFO | 收到文本消息: 消息ID:412604499 来自:48097389945@chatroom 发送人:wxid_84mmq4cu7ita22 @:[] 内容:你随口那么多句，有时候给，有时候就不乐意给
2025-07-25 12:28:36 | DEBUG | 处理消息内容: '你随口那么多句，有时候给，有时候就不乐意给'
2025-07-25 12:28:36 | DEBUG | 消息内容 '你随口那么多句，有时候给，有时候就不乐意给' 不匹配任何命令，忽略
2025-07-25 12:28:39 | DEBUG | 收到消息: {'MsgId': 942799842, 'FromUserName': {'string': '48097389945@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 1, 'Content': {'string': 'wxid_l9koi6kli78i22:\n格局打开'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1753417741, 'MsgSource': '<msgsource>\n\t<pua>1</pua>\n\t<silence>0</silence>\n\t<membercount>63</membercount>\n\t<signature>N0_V1_7ALDnC4C|v1_ZFbrZ3KL</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'PushContent': '十五 : 格局打开', 'NewMsgId': 1360955081051309583, 'MsgSeq': 871398563}
2025-07-25 12:28:39 | INFO | 收到文本消息: 消息ID:942799842 来自:48097389945@chatroom 发送人:wxid_l9koi6kli78i22 @:[] 内容:格局打开
2025-07-25 12:28:39 | DEBUG | 处理消息内容: '格局打开'
2025-07-25 12:28:39 | DEBUG | 消息内容 '格局打开' 不匹配任何命令，忽略
2025-07-25 12:28:54 | DEBUG | 收到消息: {'MsgId': 1947762752, 'FromUserName': {'string': '48097389945@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 49, 'Content': {'string': 'rongcheng6630606:\n<?xml version="1.0"?>\n<msg>\n\t<appmsg appid="" sdkver="0">\n\t\t<title>说我妈宝男，我有打扰你生活？越界嘲笑我</title>\n\t\t<des />\n\t\t<username />\n\t\t<action>view</action>\n\t\t<type>57</type>\n\t\t<showtype>0</showtype>\n\t\t<content />\n\t\t<url />\n\t\t<lowurl />\n\t\t<forwardflag>0</forwardflag>\n\t\t<dataurl />\n\t\t<lowdataurl />\n\t\t<contentattr>0</contentattr>\n\t\t<streamvideo>\n\t\t\t<streamvideourl />\n\t\t\t<streamvideototaltime>0</streamvideototaltime>\n\t\t\t<streamvideotitle />\n\t\t\t<streamvideowording />\n\t\t\t<streamvideoweburl />\n\t\t\t<streamvideothumburl />\n\t\t\t<streamvideoaduxinfo />\n\t\t\t<streamvideopublishid />\n\t\t</streamvideo>\n\t\t<canvasPageItem>\n\t\t\t<canvasPageXml><![CDATA[]]></canvasPageXml>\n\t\t</canvasPageItem>\n\t\t<refermsg>\n\t\t\t<type>1</type>\n\t\t\t<svrid>2944966610981693069</svrid>\n\t\t\t<fromusr>48097389945@chatroom</fromusr>\n\t\t\t<chatusr>rongcheng6630606</chatusr>\n\t\t\t<displayname>×</displayname>\n\t\t\t<msgsource>&lt;msgsource&gt;&lt;pua&gt;1&lt;/pua&gt;&lt;eggSeed&gt;1430687232&lt;/eggSeed&gt;&lt;/msgsource&gt;</msgsource>\n\t\t\t<content>我请你喝那么多茶饮，6月随口说让你请我喝瑞幸。却变成我怎么老是找你要钱</content>\n\t\t\t<strid />\n\t\t\t<createtime>1753417703</createtime>\n\t\t</refermsg>\n\t\t<appattach>\n\t\t\t<totallen>0</totallen>\n\t\t\t<attachid />\n\t\t\t<cdnattachurl />\n\t\t\t<emoticonmd5></emoticonmd5>\n\t\t\t<aeskey></aeskey>\n\t\t\t<fileext />\n\t\t\t<islargefilemsg>0</islargefilemsg>\n\t\t</appattach>\n\t\t<extinfo />\n\t\t<androidsource>0</androidsource>\n\t\t<thumburl />\n\t\t<mediatagname />\n\t\t<messageaction><![CDATA[]]></messageaction>\n\t\t<messageext><![CDATA[]]></messageext>\n\t\t<emoticongift>\n\t\t\t<packageflag>0</packageflag>\n\t\t\t<packageid />\n\t\t</emoticongift>\n\t\t<emoticonshared>\n\t\t\t<packageflag>0</packageflag>\n\t\t\t<packageid />\n\t\t</emoticonshared>\n\t\t<designershared>\n\t\t\t<designeruin>0</designeruin>\n\t\t\t<designername>null</designername>\n\t\t\t<designerrediretcturl><![CDATA[null]]></designerrediretcturl>\n\t\t</designershared>\n\t\t<emotionpageshared>\n\t\t\t<tid>0</tid>\n\t\t\t<title>null</title>\n\t\t\t<desc>null</desc>\n\t\t\t<iconUrl><![CDATA[null]]></iconUrl>\n\t\t\t<secondUrl>null</secondUrl>\n\t\t\t<pageType>0</pageType>\n\t\t\t<setKey>null</setKey>\n\t\t</emotionpageshared>\n\t\t<webviewshared>\n\t\t\t<shareUrlOriginal />\n\t\t\t<shareUrlOpen />\n\t\t\t<jsAppId />\n\t\t\t<publisherId />\n\t\t\t<publisherReqId />\n\t\t</webviewshared>\n\t\t<template_id />\n\t\t<md5 />\n\t\t<websearch>\n\t\t\t<rec_category>0</rec_category>\n\t\t\t<channelId>0</channelId>\n\t\t</websearch>\n\t\t<weappinfo>\n\t\t\t<username />\n\t\t\t<appid />\n\t\t\t<appservicetype>0</appservicetype>\n\t\t\t<secflagforsinglepagemode>0</secflagforsinglepagemode>\n\t\t\t<videopageinfo>\n\t\t\t\t<thumbwidth>0</thumbwidth>\n\t\t\t\t<thumbheight>0</thumbheight>\n\t\t\t\t<fromopensdk>0</fromopensdk>\n\t\t\t</videopageinfo>\n\t\t</weappinfo>\n\t\t<statextstr />\n\t\t<musicShareItem>\n\t\t\t<musicDuration>0</musicDuration>\n\t\t</musicShareItem>\n\t\t<finderLiveProductShare>\n\t\t\t<finderLiveID><![CDATA[]]></finderLiveID>\n\t\t\t<finderUsername><![CDATA[]]></finderUsername>\n\t\t\t<finderObjectID><![CDATA[]]></finderObjectID>\n\t\t\t<finderNonceID><![CDATA[]]></finderNonceID>\n\t\t\t<liveStatus><![CDATA[]]></liveStatus>\n\t\t\t<appId><![CDATA[]]></appId>\n\t\t\t<pagePath><![CDATA[]]></pagePath>\n\t\t\t<productId><![CDATA[]]></productId>\n\t\t\t<coverUrl><![CDATA[]]></coverUrl>\n\t\t\t<productTitle><![CDATA[]]></productTitle>\n\t\t\t<marketPrice><![CDATA[0]]></marketPrice>\n\t\t\t<sellingPrice><![CDATA[0]]></sellingPrice>\n\t\t\t<platformHeadImg><![CDATA[]]></platformHeadImg>\n\t\t\t<platformName><![CDATA[]]></platformName>\n\t\t\t<shopWindowId><![CDATA[]]></shopWindowId>\n\t\t\t<flashSalePrice><![CDATA[0]]></flashSalePrice>\n\t\t\t<flashSaleEndTime><![CDATA[0]]></flashSaleEndTime>\n\t\t\t<ecSource><![CDATA[]]></ecSource>\n\t\t\t<sellingPriceWording><![CDATA[]]></sellingPriceWording>\n\t\t\t<platformIconURL><![CDATA[]]></platformIconURL>\n\t\t\t<firstProductTagURL><![CDATA[]]></firstProductTagURL>\n\t\t\t<firstProductTagAspectRatioString><![CDATA[0.0]]></firstProductTagAspectRatioString>\n\t\t\t<secondProductTagURL><![CDATA[]]></secondProductTagURL>\n\t\t\t<secondProductTagAspectRatioString><![CDATA[0.0]]></secondProductTagAspectRatioString>\n\t\t\t<firstGuaranteeWording><![CDATA[]]></firstGuaranteeWording>\n\t\t\t<secondGuaranteeWording><![CDATA[]]></secondGuaranteeWording>\n\t\t\t<thirdGuaranteeWording><![CDATA[]]></thirdGuaranteeWording>\n\t\t\t<isPriceBeginShow>false</isPriceBeginShow>\n\t\t\t<lastGMsgID><![CDATA[]]></lastGMsgID>\n\t\t\t<promoterKey><![CDATA[]]></promoterKey>\n\t\t\t<discountWording><![CDATA[]]></discountWording>\n\t\t\t<priceSuffixDescription><![CDATA[]]></priceSuffixDescription>\n\t\t\t<productCardKey><![CDATA[]]></productCardKey>\n\t\t\t<isWxShop><![CDATA[]]></isWxShop>\n\t\t\t<brandIconUrl><![CDATA[]]></brandIconUrl>\n\t\t\t<rIconUrl><![CDATA[]]></rIconUrl>\n\t\t\t<rIconUrlDarkMode><![CDATA[]]></rIconUrlDarkMode>\n\t\t\t<topShopIconUrl><![CDATA[]]></topShopIconUrl>\n\t\t\t<topShopIconUrlDarkMode><![CDATA[]]></topShopIconUrlDarkMode>\n\t\t\t<simplifyTopShopIconUrl><![CDATA[]]></simplifyTopShopIconUrl>\n\t\t\t<simplifyTopShopIconUrlDarkmode><![CDATA[]]></simplifyTopShopIconUrlDarkmode>\n\t\t\t<topShopIconWidth><![CDATA[0]]></topShopIconWidth>\n\t\t\t<topShopIconHeight><![CDATA[0]]></topShopIconHeight>\n\t\t\t<simplifyTopShopIconWidth><![CDATA[0]]></simplifyTopShopIconWidth>\n\t\t\t<simplifyTopShopIconHeight><![CDATA[0]]></simplifyTopShopIconHeight>\n\t\t\t<showBoxItemStringList />\n\t\t</finderLiveProductShare>\n\t\t<finderOrder>\n\t\t\t<appID><![CDATA[]]></appID>\n\t\t\t<orderID><![CDATA[]]></orderID>\n\t\t\t<path><![CDATA[]]></path>\n\t\t\t<priceWording><![CDATA[]]></priceWording>\n\t\t\t<stateWording><![CDATA[]]></stateWording>\n\t\t\t<productImageURL><![CDATA[]]></productImageURL>\n\t\t\t<products><![CDATA[]]></products>\n\t\t\t<productsCount><![CDATA[0]]></productsCount>\n\t\t\t<orderType><![CDATA[0]]></orderType>\n\t\t\t<newPriceWording><![CDATA[]]></newPriceWording>\n\t\t\t<newStateWording><![CDATA[]]></newStateWording>\n\t\t\t<useNewWording><![CDATA[0]]></useNewWording>\n\t\t</finderOrder>\n\t\t<finderShopWindowShare>\n\t\t\t<finderUsername><![CDATA[]]></finderUsername>\n\t\t\t<avatar><![CDATA[]]></avatar>\n\t\t\t<nickname><![CDATA[]]></nickname>\n\t\t\t<commodityInStockCount><![CDATA[]]></commodityInStockCount>\n\t\t\t<appId><![CDATA[]]></appId>\n\t\t\t<path><![CDATA[]]></path>\n\t\t\t<appUsername><![CDATA[]]></appUsername>\n\t\t\t<query><![CDATA[]]></query>\n\t\t\t<liteAppId><![CDATA[]]></liteAppId>\n\t\t\t<liteAppPath><![CDATA[]]></liteAppPath>\n\t\t\t<liteAppQuery><![CDATA[]]></liteAppQuery>\n\t\t\t<platformTagURL><![CDATA[]]></platformTagURL>\n\t\t\t<saleWording><![CDATA[]]></saleWording>\n\t\t\t<lastGMsgID><![CDATA[]]></lastGMsgID>\n\t\t\t<profileTypeWording><![CDATA[]]></profileTypeWording>\n\t\t\t<saleWordingExtra><![CDATA[]]></saleWordingExtra>\n\t\t\t<isWxShop><![CDATA[]]></isWxShop>\n\t\t\t<platformIconUrl><![CDATA[]]></platformIconUrl>\n\t\t\t<brandIconUrl><![CDATA[]]></brandIconUrl>\n\t\t\t<description><![CDATA[]]></description>\n\t\t\t<backgroundUrl><![CDATA[]]></backgroundUrl>\n\t\t\t<darkModePlatformIconUrl><![CDATA[]]></darkModePlatformIconUrl>\n\t\t\t<rIconUrl><![CDATA[]]></rIconUrl>\n\t\t\t<rIconUrlDarkMode><![CDATA[]]></rIconUrlDarkMode>\n\t\t\t<rWords><![CDATA[]]></rWords>\n\t\t\t<topShopIconUrl><![CDATA[]]></topShopIconUrl>\n\t\t\t<topShopIconUrlDarkMode><![CDATA[]]></topShopIconUrlDarkMode>\n\t\t\t<simplifyTopShopIconUrl><![CDATA[]]></simplifyTopShopIconUrl>\n\t\t\t<simplifyTopShopIconUrlDarkmode><![CDATA[]]></simplifyTopShopIconUrlDarkmode>\n\t\t\t<topShopIconWidth><![CDATA[0]]></topShopIconWidth>\n\t\t\t<topShopIconHeight><![CDATA[0]]></topShopIconHeight>\n\t\t\t<simplifyTopShopIconWidth><![CDATA[0]]></simplifyTopShopIconWidth>\n\t\t\t<simplifyTopShopIconHeight><![CDATA[0]]></simplifyTopShopIconHeight>\n\t\t\t<reputationInfo>\n\t\t\t\t<hasReputationInfo>0</hasReputationInfo>\n\t\t\t\t<reputationScore>0</reputationScore>\n\t\t\t\t<reputationWording />\n\t\t\t\t<reputationTextColor />\n\t\t\t\t<reputationLevelWording />\n\t\t\t\t<reputationBackgroundColor />\n\t\t\t</reputationInfo>\n\t\t\t<productImageURLList />\n\t\t</finderShopWindowShare>\n\t\t<findernamecard>\n\t\t\t<username />\n\t\t\t<avatar><![CDATA[]]></avatar>\n\t\t\t<nickname />\n\t\t\t<auth_job />\n\t\t\t<auth_icon>0</auth_icon>\n\t\t\t<auth_icon_url />\n\t\t\t<ecSource><![CDATA[]]></ecSource>\n\t\t\t<lastGMsgID><![CDATA[]]></lastGMsgID>\n\t\t</findernamecard>\n\t\t<finderGuarantee>\n\t\t\t<scene><![CDATA[0]]></scene>\n\t\t</finderGuarantee>\n\t\t<directshare>0</directshare>\n\t\t<gamecenter>\n\t\t\t<namecard>\n\t\t\t\t<iconUrl />\n\t\t\t\t<name />\n\t\t\t\t<desc />\n\t\t\t\t<tail />\n\t\t\t\t<jumpUrl />\n\t\t\t\t<liteappId />\n\t\t\t\t<liteappPath />\n\t\t\t\t<liteappQuery />\n\t\t\t\t<liteappMinVersion />\n\t\t\t</namecard>\n\t\t</gamecenter>\n\t\t<patMsg>\n\t\t\t<chatUser />\n\t\t\t<records>\n\t\t\t\t<recordNum>0</recordNum>\n\t\t\t</records>\n\t\t</patMsg>\n\t\t<secretmsg>\n\t\t\t<issecretmsg>0</issecretmsg>\n\t\t</secretmsg>\n\t\t<referfromscene>0</referfromscene>\n\t\t<gameshare>\n\t\t\t<liteappext>\n\t\t\t\t<liteappbizdata />\n\t\t\t\t<priority>0</priority>\n\t\t\t</liteappext>\n\t\t\t<appbrandext>\n\t\t\t\t<litegameinfo />\n\t\t\t\t<priority>-1</priority>\n\t\t\t</appbrandext>\n\t\t\t<gameshareid />\n\t\t\t<sharedata />\n\t\t\t<isvideo>0</isvideo>\n\t\t\t<duration>-1</duration>\n\t\t\t<isexposed>0</isexposed>\n\t\t\t<readtext />\n\t\t</gameshare>\n\t\t<tingChatRoomItem>\n\t\t\t<type>0</type>\n\t\t\t<categoryItem>null</categoryItem>\n\t\t\t<categoryId />\n\t\t</tingChatRoomItem>\n\t\t<mpsharetrace>\n\t\t\t<hasfinderelement>0</hasfinderelement>\n\t\t\t<lastgmsgid />\n\t\t</mpsharetrace>\n\t\t<wxgamecard>\n\t\t\t<framesetname />\n\t\t\t<mbcarddata />\n\t\t\t<minpkgversion />\n\t\t\t<clientextinfo />\n\t\t\t<mbcardheight>0</mbcardheight>\n\t\t\t<isoldversion>0</isoldversion>\n\t\t</wxgamecard>\n\t\t<ecskfcard>\n\t\t\t<framesetname />\n\t\t\t<mbcarddata />\n\t\t\t<minupdateunixtimestamp>0</minupdateunixtimestamp>\n\t\t\t<needheader>false</needheader>\n\t\t\t<summary />\n\t\t</ecskfcard>\n\t\t<liteapp>\n\t\t\t<id>null</id>\n\t\t\t<path />\n\t\t\t<query />\n\t\t\t<istransparent>0</istransparent>\n\t\t\t<hideicon>0</hideicon>\n\t\t\t<forbidforward>0</forbidforward>\n\t\t</liteapp>\n\t\t<opensdk_share_is_modified>0</opensdk_share_is_modified>\n\t</appmsg>\n\t<fromusername>rongcheng6630606</fromusername>\n\t<scene>0</scene>\n\t<appinfo>\n\t\t<version>1</version>\n\t\t<appname />\n\t</appinfo>\n\t<commenturl />\n</msg>\n'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1753417757, 'MsgSource': '<msgsource>\n\t<alnode>\n\t\t<fr>4</fr>\n\t</alnode>\n\t<eggIncluded>1</eggIncluded>\n\t<sec_msg_node>\n\t\t<uuid>6e172f8fbb6b7eb1cbe8ac4a09f49049_</uuid>\n\t\t<risk-file-flag />\n\t\t<risk-file-md5-list />\n\t</sec_msg_node>\n\t<silence>0</silence>\n\t<membercount>63</membercount>\n\t<signature>N0_V1_eXKWuX+w|v1_Mt788LWz</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'PushContent': '× : 说我妈宝男，我有打扰你生活？越界嘲笑我', 'NewMsgId': 3427694672937260711, 'MsgSeq': 871398564}
2025-07-25 12:28:54 | DEBUG | 从群聊消息中提取发送者: rongcheng6630606
2025-07-25 12:28:54 | DEBUG | 使用已解析的XML处理引用消息
2025-07-25 12:28:54 | INFO | 收到引用消息: 消息ID:1947762752 来自:48097389945@chatroom 发送人:rongcheng6630606 内容:说我妈宝男，我有打扰你生活？越界嘲笑我 引用类型:1
2025-07-25 12:28:55 | INFO | [DouBaoImageToImage] 收到引用消息: 说我妈宝男，我有打扰你生活？越界嘲笑我
2025-07-25 12:28:55 | INFO | [TimerTask] 收到引用消息调试信息:
2025-07-25 12:28:55 | INFO |   - 消息内容: 说我妈宝男，我有打扰你生活？越界嘲笑我
2025-07-25 12:28:55 | INFO |   - 群组ID: 48097389945@chatroom
2025-07-25 12:28:55 | INFO |   - 发送人: rongcheng6630606
2025-07-25 12:28:55 | INFO |   - 引用信息: {'MsgType': 1, 'Content': '我请你喝那么多茶饮，6月随口说让你请我喝瑞幸。却变成我怎么老是找你要钱', 'Msgid': '2944966610981693069', 'NewMsgId': '2944966610981693069', 'ToWxid': 'wxid_4usgcju5ey9q29', 'FromWxid': '48097389945@chatroom', 'Nickname': '×', 'MsgSource': '<msgsource><pua>1</pua><eggSeed>1430687232</eggSeed></msgsource>', 'Createtime': '1753417703', 'SenderWxid': 'rongcheng6630606'}
2025-07-25 12:28:55 | INFO |   - 引用消息ID: 
2025-07-25 12:28:55 | INFO |   - 引用消息类型: 
2025-07-25 12:28:55 | INFO |   - 引用消息内容: 我请你喝那么多茶饮，6月随口说让你请我喝瑞幸。却变成我怎么老是找你要钱
2025-07-25 12:28:55 | INFO |   - 引用消息发送人: rongcheng6630606
2025-07-25 12:29:01 | DEBUG | 收到消息: {'MsgId': 1564214473, 'FromUserName': {'string': '48097389945@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 1, 'Content': {'string': 'wxid_84mmq4cu7ita22:\n你别这委屈了，你整的大家很不体面'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1753417763, 'MsgSource': '<msgsource>\n\t<sec_msg_node>\n\t\t<alnode>\n\t\t\t<fr>1</fr>\n\t\t</alnode>\n\t</sec_msg_node>\n\t<pua>1</pua>\n\t<silence>0</silence>\n\t<membercount>63</membercount>\n\t<signature>N0_V1_adisB4t8|v1_AEsA55CA</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'PushContent': '喵小叶 : 你别这委屈了，你整的大家很不体面', 'NewMsgId': 8339857596298831153, 'MsgSeq': 871398565}
2025-07-25 12:29:01 | INFO | 收到文本消息: 消息ID:1564214473 来自:48097389945@chatroom 发送人:wxid_84mmq4cu7ita22 @:[] 内容:你别这委屈了，你整的大家很不体面
2025-07-25 12:29:01 | DEBUG | 处理消息内容: '你别这委屈了，你整的大家很不体面'
2025-07-25 12:29:01 | DEBUG | 消息内容 '你别这委屈了，你整的大家很不体面' 不匹配任何命令，忽略
2025-07-25 12:29:04 | DEBUG | 收到消息: {'MsgId': 699794822, 'FromUserName': {'string': '48097389945@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 34, 'Content': {'string': 'wxid_lneb7n23o4lg12:\n<msg><voicemsg endflag="1" cancelflag="0" forwardflag="0" voiceformat="4" voicelength="6792" length="20480" bufid="0" aeskey="7375796865646972736b766c66667074" voiceurl="3052020100044b304902010002046b3e4bb802033d11fe020459f70399020468830824042432396266623362392d333964382d346534362d626134302d31613236643332376339643702040524000f02010004002a5cd9b3" voicemd5="db5dc8fa9f21fbe3538c9ed530274fc3" clientmsgid="41653132633234663932353735336100231229072525a7b9a8c0ddc104" fromusername="wxid_lneb7n23o4lg12" /></msg>'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1753417764, 'MsgSource': '<msgsource>\n\t<silence>0</silence>\n\t<membercount>63</membercount>\n\t<signature>N0_V1_BucEsEyu|v1_rj9wzPkM</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'PushContent': 'ᡣ在群聊中发了一段语音', 'NewMsgId': 1280839010080890982, 'MsgSeq': 871398566}
2025-07-25 12:29:04 | INFO | 收到语音消息: 消息ID:699794822 来自:48097389945@chatroom 发送人:wxid_lneb7n23o4lg12 XML:
<msg><voicemsg endflag="1" cancelflag="0" forwardflag="0" voiceformat="4" voicelength="6792" length="20480" bufid="0" aeskey="7375796865646972736b766c66667074" voiceurl="3052020100044b304902010002046b3e4bb802033d11fe020459f70399020468830824042432396266623362392d333964382d346534362d626134302d31613236643332376339643702040524000f02010004002a5cd9b3" voicemd5="db5dc8fa9f21fbe3538c9ed530274fc3" clientmsgid="41653132633234663932353735336100231229072525a7b9a8c0ddc104" fromusername="wxid_lneb7n23o4lg12" /></msg>
2025-07-25 12:29:10 | DEBUG | 收到消息: {'MsgId': 373097311, 'FromUserName': {'string': '48097389945@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 1, 'Content': {'string': 'wxid_84mmq4cu7ita22:\n这个群谁知道你是妈宝男'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1753417773, 'MsgSource': '<msgsource>\n\t<sec_msg_node>\n\t\t<alnode>\n\t\t\t<fr>1</fr>\n\t\t</alnode>\n\t</sec_msg_node>\n\t<pua>1</pua>\n\t<silence>0</silence>\n\t<membercount>63</membercount>\n\t<signature>N0_V1_sNs4JK7T|v1_v+0BrJq/</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'PushContent': '喵小叶 : 这个群谁知道你是妈宝男', 'NewMsgId': 7423535631370798875, 'MsgSeq': 871398567}
2025-07-25 12:29:10 | INFO | 收到文本消息: 消息ID:373097311 来自:48097389945@chatroom 发送人:wxid_84mmq4cu7ita22 @:[] 内容:这个群谁知道你是妈宝男
2025-07-25 12:29:11 | DEBUG | 处理消息内容: '这个群谁知道你是妈宝男'
2025-07-25 12:29:11 | DEBUG | 消息内容 '这个群谁知道你是妈宝男' 不匹配任何命令，忽略
2025-07-25 12:29:13 | DEBUG | 收到消息: {'MsgId': 1200417203, 'FromUserName': {'string': '48097389945@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 1, 'Content': {'string': 'rongcheng6630606:\n体面个屁'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1753417775, 'MsgSource': '<msgsource>\n\t<pua>1</pua>\n\t<eggIncluded>1</eggIncluded>\n\t<silence>0</silence>\n\t<membercount>63</membercount>\n\t<signature>N0_V1_YXVzdtIS|v1_ZPYHlArk</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'PushContent': '× : 体面个屁', 'NewMsgId': 2173564149991857776, 'MsgSeq': 871398568}
2025-07-25 12:29:13 | INFO | 收到文本消息: 消息ID:1200417203 来自:48097389945@chatroom 发送人:rongcheng6630606 @:[] 内容:体面个屁
2025-07-25 12:29:13 | DEBUG | 处理消息内容: '体面个屁'
2025-07-25 12:29:13 | DEBUG | 消息内容 '体面个屁' 不匹配任何命令，忽略
2025-07-25 12:29:17 | DEBUG | 收到消息: {'MsgId': 497207556, 'FromUserName': {'string': '47325400669@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 49, 'Content': {'string': 'heaventt:\n<?xml version="1.0"?>\n<msg>\n\t<appmsg appid="" sdkver="0">\n\t\t<title>用逆向的就不怕了</title>\n\t\t<des />\n\t\t<username />\n\t\t<action>view</action>\n\t\t<type>57</type>\n\t\t<showtype>0</showtype>\n\t\t<content />\n\t\t<url />\n\t\t<lowurl />\n\t\t<forwardflag>0</forwardflag>\n\t\t<dataurl />\n\t\t<lowdataurl />\n\t\t<contentattr>0</contentattr>\n\t\t<streamvideo>\n\t\t\t<streamvideourl />\n\t\t\t<streamvideototaltime>0</streamvideototaltime>\n\t\t\t<streamvideotitle />\n\t\t\t<streamvideowording />\n\t\t\t<streamvideoweburl />\n\t\t\t<streamvideothumburl />\n\t\t\t<streamvideoaduxinfo />\n\t\t\t<streamvideopublishid />\n\t\t</streamvideo>\n\t\t<canvasPageItem>\n\t\t\t<canvasPageXml><![CDATA[]]></canvasPageXml>\n\t\t</canvasPageItem>\n\t\t<refermsg>\n\t\t\t<type>49</type>\n\t\t\t<svrid>2451994746920798421</svrid>\n\t\t\t<fromusr>47325400669@chatroom</fromusr>\n\t\t\t<chatusr>wxid_9uwska6u4yzm22</chatusr>\n\t\t\t<displayname>Garson</displayname>\n\t\t\t<msgsource>&lt;msgsource&gt;\n\t&lt;bizflag&gt;0&lt;/bizflag&gt;\n\t&lt;pua&gt;1&lt;/pua&gt;\n\t&lt;eggIncluded&gt;1&lt;/eggIncluded&gt;\n\t&lt;sec_msg_node&gt;\n\t\t&lt;uuid&gt;16d1a012b658186b70c35c6458db7217_&lt;/uuid&gt;\n\t\t&lt;risk-file-flag /&gt;\n\t\t&lt;risk-file-md5-list /&gt;\n\t&lt;/sec_msg_node&gt;\n\t&lt;silence&gt;1&lt;/silence&gt;\n\t&lt;membercount&gt;221&lt;/membercount&gt;\n\t&lt;signature&gt;N0_V1_tAUXux8i|v1_CkMPeX58&lt;/signature&gt;\n\t&lt;tmp_node&gt;\n\t\t&lt;publisher-id&gt;&lt;/publisher-id&gt;\n\t&lt;/tmp_node&gt;\n&lt;/msgsource&gt;\n</msgsource>\n\t\t\t<content>&lt;?xml version="1.0"?&gt;\n&lt;msg&gt;\n\t&lt;appmsg appid="" sdkver="0"&gt;\n\t\t&lt;title&gt;国产模型应该都是吧？[Smirk]&lt;/title&gt;\n\t\t&lt;type&gt;57&lt;/type&gt;\n\t\t&lt;appattach&gt;\n\t\t\t&lt;cdnthumbaeskey /&gt;\n\t\t\t&lt;aeskey /&gt;\n\t\t&lt;/appattach&gt;\n\t\t&lt;refermsg&gt;&lt;/refermsg&gt;\n\t&lt;/appmsg&gt;\n\t&lt;fromusername&gt;wxid_9uwska6u4yzm22&lt;/fromusername&gt;\n\t&lt;scene&gt;0&lt;/scene&gt;\n\t&lt;appinfo&gt;\n\t\t&lt;version&gt;1&lt;/version&gt;\n\t\t&lt;appname&gt;&lt;/appname&gt;\n\t&lt;/appinfo&gt;\n\t&lt;commenturl&gt;&lt;/commenturl&gt;\n&lt;/msg&gt;\n</content>\n\t\t\t<strid />\n\t\t\t<createtime>1753415602</createtime>\n\t\t</refermsg>\n\t\t<appattach>\n\t\t\t<totallen>0</totallen>\n\t\t\t<attachid />\n\t\t\t<cdnattachurl />\n\t\t\t<emoticonmd5 />\n\t\t\t<aeskey />\n\t\t\t<fileext />\n\t\t\t<islargefilemsg>0</islargefilemsg>\n\t\t</appattach>\n\t\t<extinfo />\n\t\t<androidsource>0</androidsource>\n\t\t<thumburl />\n\t\t<mediatagname />\n\t\t<messageaction><![CDATA[]]></messageaction>\n\t\t<messageext><![CDATA[]]></messageext>\n\t\t<emoticongift>\n\t\t\t<packageflag>0</packageflag>\n\t\t\t<packageid />\n\t\t</emoticongift>\n\t\t<emoticonshared>\n\t\t\t<packageflag>0</packageflag>\n\t\t\t<packageid />\n\t\t</emoticonshared>\n\t\t<designershared>\n\t\t\t<designeruin>0</designeruin>\n\t\t\t<designername>null</designername>\n\t\t\t<designerrediretcturl><![CDATA[null]]></designerrediretcturl>\n\t\t</designershared>\n\t\t<emotionpageshared>\n\t\t\t<tid>0</tid>\n\t\t\t<title>null</title>\n\t\t\t<desc>null</desc>\n\t\t\t<iconUrl><![CDATA[null]]></iconUrl>\n\t\t\t<secondUrl>null</secondUrl>\n\t\t\t<pageType>0</pageType>\n\t\t\t<setKey>null</setKey>\n\t\t</emotionpageshared>\n\t\t<webviewshared>\n\t\t\t<shareUrlOriginal />\n\t\t\t<shareUrlOpen />\n\t\t\t<jsAppId />\n\t\t\t<publisherId />\n\t\t\t<publisherReqId />\n\t\t</webviewshared>\n\t\t<template_id />\n\t\t<md5 />\n\t\t<websearch>\n\t\t\t<rec_category>0</rec_category>\n\t\t\t<channelId>0</channelId>\n\t\t</websearch>\n\t\t<weappinfo>\n\t\t\t<username />\n\t\t\t<appid />\n\t\t\t<appservicetype>0</appservicetype>\n\t\t\t<secflagforsinglepagemode>0</secflagforsinglepagemode>\n\t\t\t<videopageinfo>\n\t\t\t\t<thumbwidth>0</thumbwidth>\n\t\t\t\t<thumbheight>0</thumbheight>\n\t\t\t\t<fromopensdk>0</fromopensdk>\n\t\t\t</videopageinfo>\n\t\t</weappinfo>\n\t\t<statextstr />\n\t\t<musicShareItem>\n\t\t\t<musicDuration>0</musicDuration>\n\t\t</musicShareItem>\n\t\t<finderLiveProductShare>\n\t\t\t<finderLiveID><![CDATA[]]></finderLiveID>\n\t\t\t<finderUsername><![CDATA[]]></finderUsername>\n\t\t\t<finderObjectID><![CDATA[]]></finderObjectID>\n\t\t\t<finderNonceID><![CDATA[]]></finderNonceID>\n\t\t\t<liveStatus><![CDATA[]]></liveStatus>\n\t\t\t<appId><![CDATA[]]></appId>\n\t\t\t<pagePath><![CDATA[]]></pagePath>\n\t\t\t<productId><![CDATA[]]></productId>\n\t\t\t<coverUrl><![CDATA[]]></coverUrl>\n\t\t\t<productTitle><![CDATA[]]></productTitle>\n\t\t\t<marketPrice><![CDATA[0]]></marketPrice>\n\t\t\t<sellingPrice><![CDATA[0]]></sellingPrice>\n\t\t\t<platformHeadImg><![CDATA[]]></platformHeadImg>\n\t\t\t<platformName><![CDATA[]]></platformName>\n\t\t\t<shopWindowId><![CDATA[]]></shopWindowId>\n\t\t\t<flashSalePrice><![CDATA[0]]></flashSalePrice>\n\t\t\t<flashSaleEndTime><![CDATA[0]]></flashSaleEndTime>\n\t\t\t<ecSource><![CDATA[]]></ecSource>\n\t\t\t<sellingPriceWording><![CDATA[]]></sellingPriceWording>\n\t\t\t<platformIconURL><![CDATA[]]></platformIconURL>\n\t\t\t<firstProductTagURL><![CDATA[]]></firstProductTagURL>\n\t\t\t<firstProductTagAspectRatioString><![CDATA[0.0]]></firstProductTagAspectRatioString>\n\t\t\t<secondProductTagURL><![CDATA[]]></secondProductTagURL>\n\t\t\t<secondProductTagAspectRatioString><![CDATA[0.0]]></secondProductTagAspectRatioString>\n\t\t\t<firstGuaranteeWording><![CDATA[]]></firstGuaranteeWording>\n\t\t\t<secondGuaranteeWording><![CDATA[]]></secondGuaranteeWording>\n\t\t\t<thirdGuaranteeWording><![CDATA[]]></thirdGuaranteeWording>\n\t\t\t<isPriceBeginShow>false</isPriceBeginShow>\n\t\t\t<lastGMsgID><![CDATA[]]></lastGMsgID>\n\t\t\t<promoterKey><![CDATA[]]></promoterKey>\n\t\t\t<discountWording><![CDATA[]]></discountWording>\n\t\t\t<priceSuffixDescription><![CDATA[]]></priceSuffixDescription>\n\t\t\t<productCardKey><![CDATA[]]></productCardKey>\n\t\t\t<isWxShop><![CDATA[]]></isWxShop>\n\t\t\t<brandIconUrl><![CDATA[]]></brandIconUrl>\n\t\t\t<rIconUrl><![CDATA[]]></rIconUrl>\n\t\t\t<rIconUrlDarkMode><![CDATA[]]></rIconUrlDarkMode>\n\t\t\t<topShopIconUrl><![CDATA[]]></topShopIconUrl>\n\t\t\t<topShopIconUrlDarkMode><![CDATA[]]></topShopIconUrlDarkMode>\n\t\t\t<simplifyTopShopIconUrl><![CDATA[]]></simplifyTopShopIconUrl>\n\t\t\t<simplifyTopShopIconUrlDarkmode><![CDATA[]]></simplifyTopShopIconUrlDarkmode>\n\t\t\t<topShopIconWidth><![CDATA[0]]></topShopIconWidth>\n\t\t\t<topShopIconHeight><![CDATA[0]]></topShopIconHeight>\n\t\t\t<simplifyTopShopIconWidth><![CDATA[0]]></simplifyTopShopIconWidth>\n\t\t\t<simplifyTopShopIconHeight><![CDATA[0]]></simplifyTopShopIconHeight>\n\t\t\t<showBoxItemStringList />\n\t\t</finderLiveProductShare>\n\t\t<finderOrder>\n\t\t\t<appID><![CDATA[]]></appID>\n\t\t\t<orderID><![CDATA[]]></orderID>\n\t\t\t<path><![CDATA[]]></path>\n\t\t\t<priceWording><![CDATA[]]></priceWording>\n\t\t\t<stateWording><![CDATA[]]></stateWording>\n\t\t\t<productImageURL><![CDATA[]]></productImageURL>\n\t\t\t<products><![CDATA[]]></products>\n\t\t\t<productsCount><![CDATA[0]]></productsCount>\n\t\t\t<orderType><![CDATA[0]]></orderType>\n\t\t\t<newPriceWording><![CDATA[]]></newPriceWording>\n\t\t\t<newStateWording><![CDATA[]]></newStateWording>\n\t\t\t<useNewWording><![CDATA[0]]></useNewWording>\n\t\t</finderOrder>\n\t\t<finderShopWindowShare>\n\t\t\t<finderUsername><![CDATA[]]></finderUsername>\n\t\t\t<avatar><![CDATA[]]></avatar>\n\t\t\t<nickname><![CDATA[]]></nickname>\n\t\t\t<commodityInStockCount><![CDATA[]]></commodityInStockCount>\n\t\t\t<appId><![CDATA[]]></appId>\n\t\t\t<path><![CDATA[]]></path>\n\t\t\t<appUsername><![CDATA[]]></appUsername>\n\t\t\t<query><![CDATA[]]></query>\n\t\t\t<liteAppId><![CDATA[]]></liteAppId>\n\t\t\t<liteAppPath><![CDATA[]]></liteAppPath>\n\t\t\t<liteAppQuery><![CDATA[]]></liteAppQuery>\n\t\t\t<platformTagURL><![CDATA[]]></platformTagURL>\n\t\t\t<saleWording><![CDATA[]]></saleWording>\n\t\t\t<lastGMsgID><![CDATA[]]></lastGMsgID>\n\t\t\t<profileTypeWording><![CDATA[]]></profileTypeWording>\n\t\t\t<saleWordingExtra><![CDATA[]]></saleWordingExtra>\n\t\t\t<isWxShop><![CDATA[]]></isWxShop>\n\t\t\t<platformIconUrl><![CDATA[]]></platformIconUrl>\n\t\t\t<brandIconUrl><![CDATA[]]></brandIconUrl>\n\t\t\t<description><![CDATA[]]></description>\n\t\t\t<backgroundUrl><![CDATA[]]></backgroundUrl>\n\t\t\t<darkModePlatformIconUrl><![CDATA[]]></darkModePlatformIconUrl>\n\t\t\t<rIconUrl><![CDATA[]]></rIconUrl>\n\t\t\t<rIconUrlDarkMode><![CDATA[]]></rIconUrlDarkMode>\n\t\t\t<rWords><![CDATA[]]></rWords>\n\t\t\t<topShopIconUrl><![CDATA[]]></topShopIconUrl>\n\t\t\t<topShopIconUrlDarkMode><![CDATA[]]></topShopIconUrlDarkMode>\n\t\t\t<simplifyTopShopIconUrl><![CDATA[]]></simplifyTopShopIconUrl>\n\t\t\t<simplifyTopShopIconUrlDarkmode><![CDATA[]]></simplifyTopShopIconUrlDarkmode>\n\t\t\t<topShopIconWidth><![CDATA[0]]></topShopIconWidth>\n\t\t\t<topShopIconHeight><![CDATA[0]]></topShopIconHeight>\n\t\t\t<simplifyTopShopIconWidth><![CDATA[0]]></simplifyTopShopIconWidth>\n\t\t\t<simplifyTopShopIconHeight><![CDATA[0]]></simplifyTopShopIconHeight>\n\t\t\t<reputationInfo>\n\t\t\t\t<hasReputationInfo>0</hasReputationInfo>\n\t\t\t\t<reputationScore>0</reputationScore>\n\t\t\t\t<reputationWording />\n\t\t\t\t<reputationTextColor />\n\t\t\t\t<reputationLevelWording />\n\t\t\t\t<reputationBackgroundColor />\n\t\t\t</reputationInfo>\n\t\t\t<productImageURLList />\n\t\t</finderShopWindowShare>\n\t\t<findernamecard>\n\t\t\t<username />\n\t\t\t<avatar><![CDATA[]]></avatar>\n\t\t\t<nickname />\n\t\t\t<auth_job />\n\t\t\t<auth_icon>0</auth_icon>\n\t\t\t<auth_icon_url />\n\t\t\t<ecSource><![CDATA[]]></ecSource>\n\t\t\t<lastGMsgID><![CDATA[]]></lastGMsgID>\n\t\t</findernamecard>\n\t\t<finderGuarantee>\n\t\t\t<scene><![CDATA[0]]></scene>\n\t\t</finderGuarantee>\n\t\t<directshare>0</directshare>\n\t\t<gamecenter>\n\t\t\t<namecard>\n\t\t\t\t<iconUrl />\n\t\t\t\t<name />\n\t\t\t\t<desc />\n\t\t\t\t<tail />\n\t\t\t\t<jumpUrl />\n\t\t\t\t<liteappId />\n\t\t\t\t<liteappPath />\n\t\t\t\t<liteappQuery />\n\t\t\t\t<liteappMinVersion />\n\t\t\t</namecard>\n\t\t</gamecenter>\n\t\t<patMsg>\n\t\t\t<chatUser />\n\t\t\t<records>\n\t\t\t\t<recordNum>0</recordNum>\n\t\t\t</records>\n\t\t</patMsg>\n\t\t<secretmsg>\n\t\t\t<issecretmsg>0</issecretmsg>\n\t\t</secretmsg>\n\t\t<referfromscene>0</referfromscene>\n\t\t<gameshare>\n\t\t\t<liteappext>\n\t\t\t\t<liteappbizdata />\n\t\t\t\t<priority>0</priority>\n\t\t\t</liteappext>\n\t\t\t<appbrandext>\n\t\t\t\t<litegameinfo />\n\t\t\t\t<priority>-1</priority>\n\t\t\t</appbrandext>\n\t\t\t<gameshareid />\n\t\t\t<sharedata />\n\t\t\t<isvideo>0</isvideo>\n\t\t\t<duration>-1</duration>\n\t\t\t<isexposed>0</isexposed>\n\t\t\t<readtext />\n\t\t</gameshare>\n\t\t<tingChatRoomItem>\n\t\t\t<type>0</type>\n\t\t\t<categoryItem>null</categoryItem>\n\t\t\t<categoryId />\n\t\t\t<listenItem>null</listenItem>\n\t\t</tingChatRoomItem>\n\t\t<mpsharetrace>\n\t\t\t<hasfinderelement>0</hasfinderelement>\n\t\t\t<lastgmsgid />\n\t\t</mpsharetrace>\n\t\t<wxgamecard>\n\t\t\t<framesetname />\n\t\t\t<mbcarddata />\n\t\t\t<minpkgversion />\n\t\t\t<clientextinfo />\n\t\t\t<mbcardheight>0</mbcardheight>\n\t\t\t<isoldversion>0</isoldversion>\n\t\t</wxgamecard>\n\t\t<ecskfcard>\n\t\t\t<framesetname />\n\t\t\t<mbcarddata />\n\t\t\t<minupdateunixtimestamp>0</minupdateunixtimestamp>\n\t\t\t<needheader>false</needheader>\n\t\t\t<summary />\n\t\t</ecskfcard>\n\t\t<liteapp>\n\t\t\t<id>null</id>\n\t\t\t<path />\n\t\t\t<query />\n\t\t\t<istransparent>0</istransparent>\n\t\t\t<hideicon>0</hideicon>\n\t\t\t<forbidforward>0</forbidforward>\n\t\t</liteapp>\n\t\t<opensdk_share_is_modified>0</opensdk_share_is_modified>\n\t</appmsg>\n\t<fromusername>heaventt</fromusername>\n\t<scene>0</scene>\n\t<appinfo>\n\t\t<version>1</version>\n\t\t<appname></appname>\n\t</appinfo>\n\t<commenturl></commenturl>\n</msg>\n'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1753417779, 'MsgSource': '<msgsource>\n\t<alnode>\n\t\t<fr>4</fr>\n\t</alnode>\n\t<eggIncluded>1</eggIncluded>\n\t<sec_msg_node>\n\t\t<uuid>16d1a012b658186b70c35c6458db7217_</uuid>\n\t\t<risk-file-flag />\n\t\t<risk-file-md5-list />\n\t</sec_msg_node>\n\t<silence>0</silence>\n\t<membercount>221</membercount>\n\t<signature>N0_V1_y5ARG/ZD|v1_o4kbczvT</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'PushContent': 'heaven : 用逆向的就不怕了', 'NewMsgId': 6227824416252010915, 'MsgSeq': 871398569}
2025-07-25 12:29:17 | DEBUG | 从群聊消息中提取发送者: heaventt
2025-07-25 12:29:17 | DEBUG | 使用已解析的XML处理引用消息
2025-07-25 12:29:17 | INFO | 收到引用消息: 消息ID:497207556 来自:47325400669@chatroom 发送人:heaventt 内容:用逆向的就不怕了 引用类型:49
2025-07-25 12:29:17 | INFO | [DouBaoImageToImage] 收到引用消息: 用逆向的就不怕了
2025-07-25 12:29:17 | INFO | [TimerTask] 收到引用消息调试信息:
2025-07-25 12:29:17 | INFO |   - 消息内容: 用逆向的就不怕了
2025-07-25 12:29:17 | INFO |   - 群组ID: 47325400669@chatroom
2025-07-25 12:29:17 | INFO |   - 发送人: heaventt
2025-07-25 12:29:17 | INFO |   - 引用信息: {'MsgType': 49, 'Content': '<?xml version="1.0"?>\n<msg>\n\t<appmsg appid="" sdkver="0">\n\t\t<title>国产模型应该都是吧？[Smirk]</title>\n\t\t<type>57</type>\n\t\t<appattach>\n\t\t\t<cdnthumbaeskey />\n\t\t\t<aeskey />\n\t\t</appattach>\n\t\t<refermsg></refermsg>\n\t</appmsg>\n\t<fromusername>wxid_9uwska6u4yzm22</fromusername>\n\t<scene>0</scene>\n\t<appinfo>\n\t\t<version>1</version>\n\t\t<appname></appname>\n\t</appinfo>\n\t<commenturl></commenturl>\n</msg>\n', 'Msgid': '2451994746920798421', 'NewMsgId': '2451994746920798421', 'ToWxid': 'wxid_4usgcju5ey9q29', 'FromWxid': '47325400669@chatroom', 'Nickname': 'Garson', 'MsgSource': '<msgsource>\n\t<bizflag>0</bizflag>\n\t<pua>1</pua>\n\t<eggIncluded>1</eggIncluded>\n\t<sec_msg_node>\n\t\t<uuid>16d1a012b658186b70c35c6458db7217_</uuid>\n\t\t<risk-file-flag />\n\t\t<risk-file-md5-list />\n\t</sec_msg_node>\n\t<silence>1</silence>\n\t<membercount>221</membercount>\n\t<signature>N0_V1_tAUXux8i|v1_CkMPeX58</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'Createtime': '1753415602', 'SenderWxid': 'heaventt'}
2025-07-25 12:29:17 | INFO |   - 引用消息ID: 
2025-07-25 12:29:17 | INFO |   - 引用消息类型: 
2025-07-25 12:29:17 | INFO |   - 引用消息内容: <?xml version="1.0"?>
<msg>
	<appmsg appid="" sdkver="0">
		<title>国产模型应该都是吧？[Smirk]</title>
		<type>57</type>
		<appattach>
			<cdnthumbaeskey />
			<aeskey />
		</appattach>
		<refermsg></refermsg>
	</appmsg>
	<fromusername>wxid_9uwska6u4yzm22</fromusername>
	<scene>0</scene>
	<appinfo>
		<version>1</version>
		<appname></appname>
	</appinfo>
	<commenturl></commenturl>
</msg>

2025-07-25 12:29:17 | INFO |   - 引用消息发送人: heaventt
2025-07-25 12:29:18 | DEBUG | 收到消息: {'MsgId': 2121739330, 'FromUserName': {'string': '48097389945@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 1, 'Content': {'string': 'wxid_84mmq4cu7ita22:\n你自己在这一遍又一遍说'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1753417780, 'MsgSource': '<msgsource>\n\t<sec_msg_node>\n\t\t<alnode>\n\t\t\t<fr>1</fr>\n\t\t</alnode>\n\t</sec_msg_node>\n\t<pua>1</pua>\n\t<silence>0</silence>\n\t<membercount>63</membercount>\n\t<signature>N0_V1_m0KfTRTL|v1_gaySGiou</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'PushContent': '喵小叶 : 你自己在这一遍又一遍说', 'NewMsgId': 1062767198029700255, 'MsgSeq': 871398570}
2025-07-25 12:29:18 | INFO | 收到文本消息: 消息ID:2121739330 来自:48097389945@chatroom 发送人:wxid_84mmq4cu7ita22 @:[] 内容:你自己在这一遍又一遍说
2025-07-25 12:29:18 | DEBUG | 处理消息内容: '你自己在这一遍又一遍说'
2025-07-25 12:29:18 | DEBUG | 消息内容 '你自己在这一遍又一遍说' 不匹配任何命令，忽略
2025-07-25 12:29:21 | DEBUG | 收到消息: {'MsgId': 937840311, 'FromUserName': {'string': '48097389945@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 1, 'Content': {'string': 'wxid_84mmq4cu7ita22:\n你说说'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1753417784, 'MsgSource': '<msgsource>\n\t<sec_msg_node>\n\t\t<alnode>\n\t\t\t<fr>1</fr>\n\t\t</alnode>\n\t</sec_msg_node>\n\t<pua>1</pua>\n\t<silence>0</silence>\n\t<membercount>63</membercount>\n\t<signature>N0_V1_sI0G0+rh|v1_DiJ3tZD4</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'PushContent': '喵小叶 : 你说说', 'NewMsgId': 8379185768812704185, 'MsgSeq': 871398571}
2025-07-25 12:29:21 | INFO | 收到文本消息: 消息ID:937840311 来自:48097389945@chatroom 发送人:wxid_84mmq4cu7ita22 @:[] 内容:你说说
2025-07-25 12:29:21 | DEBUG | 处理消息内容: '你说说'
2025-07-25 12:29:21 | DEBUG | 消息内容 '你说说' 不匹配任何命令，忽略
2025-07-25 12:29:30 | DEBUG | 收到消息: {'MsgId': 2136493837, 'FromUserName': {'string': '48097389945@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 1, 'Content': {'string': 'rongcheng6630606:\n我只知道你好假'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1753417792, 'MsgSource': '<msgsource>\n\t<pua>1</pua>\n\t<eggIncluded>1</eggIncluded>\n\t<silence>0</silence>\n\t<membercount>63</membercount>\n\t<signature>N0_V1_uTji/PGP|v1_0yKUtUTV</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'PushContent': '× : 我只知道你好假', 'NewMsgId': 7456833598643243854, 'MsgSeq': 871398572}
2025-07-25 12:29:30 | INFO | 收到文本消息: 消息ID:2136493837 来自:48097389945@chatroom 发送人:rongcheng6630606 @:[] 内容:我只知道你好假
2025-07-25 12:29:30 | DEBUG | 处理消息内容: '我只知道你好假'
2025-07-25 12:29:30 | DEBUG | 消息内容 '我只知道你好假' 不匹配任何命令，忽略
2025-07-25 12:29:32 | DEBUG | 收到消息: {'MsgId': 409262682, 'FromUserName': {'string': '48097389945@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 1, 'Content': {'string': 'wxid_84mmq4cu7ita22:\n你说说，你已经是30岁的人了，'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1753417795, 'MsgSource': '<msgsource>\n\t<sec_msg_node>\n\t\t<alnode>\n\t\t\t<fr>1</fr>\n\t\t</alnode>\n\t</sec_msg_node>\n\t<pua>1</pua>\n\t<silence>0</silence>\n\t<membercount>63</membercount>\n\t<signature>N0_V1_bHKxPGU9|v1_CjC201aJ</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'PushContent': '喵小叶 : 你说说，你已经是30岁的人了，', 'NewMsgId': 1833355495983918119, 'MsgSeq': 871398573}
2025-07-25 12:29:32 | INFO | 收到文本消息: 消息ID:409262682 来自:48097389945@chatroom 发送人:wxid_84mmq4cu7ita22 @:[] 内容:你说说，你已经是30岁的人了，
2025-07-25 12:29:33 | DEBUG | 处理消息内容: '你说说，你已经是30岁的人了，'
2025-07-25 12:29:33 | DEBUG | 消息内容 '你说说，你已经是30岁的人了，' 不匹配任何命令，忽略
2025-07-25 12:29:34 | DEBUG | 收到消息: {'MsgId': 1910622970, 'FromUserName': {'string': '48097389945@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 1, 'Content': {'string': 'zll953369865:\n@×\u2005问一下 您是不是追求喵老板？'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1753417796, 'MsgSource': '<msgsource>\n\t<atuserlist><![CDATA[,rongcheng6630606]]></atuserlist>\n\t<pua>1</pua>\n\t<silence>0</silence>\n\t<membercount>63</membercount>\n\t<signature>N0_V1_lLc0XJI/|v1_xl6rHBY3</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'PushContent': '【微信红包】收到一个微信红包 : @×\u2005问一下 您是不是追求喵老板？', 'NewMsgId': 1187368858107624842, 'MsgSeq': 871398574}
2025-07-25 12:29:34 | INFO | 收到文本消息: 消息ID:1910622970 来自:48097389945@chatroom 发送人:zll953369865 @:['rongcheng6630606'] 内容:@× 问一下 您是不是追求喵老板？
2025-07-25 12:29:35 | DEBUG | 处理消息内容: '@× 问一下 您是不是追求喵老板？'
2025-07-25 12:29:35 | DEBUG | 消息内容 '@× 问一下 您是不是追求喵老板？' 不匹配任何命令，忽略
2025-07-25 12:29:36 | DEBUG | 收到消息: {'MsgId': 420062935, 'FromUserName': {'string': '48097389945@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 1, 'Content': {'string': 'rongcheng6630606:\n本想憋着不说的'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1753417799, 'MsgSource': '<msgsource>\n\t<pua>1</pua>\n\t<eggIncluded>1</eggIncluded>\n\t<silence>0</silence>\n\t<membercount>63</membercount>\n\t<signature>N0_V1_gOdNJfZ5|v1_l5z5CKbh</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'PushContent': '× : 本想憋着不说的', 'NewMsgId': 3590338063092585574, 'MsgSeq': 871398575}
2025-07-25 12:29:36 | INFO | 收到文本消息: 消息ID:420062935 来自:48097389945@chatroom 发送人:rongcheng6630606 @:[] 内容:本想憋着不说的
2025-07-25 12:29:37 | DEBUG | 处理消息内容: '本想憋着不说的'
2025-07-25 12:29:37 | DEBUG | 消息内容 '本想憋着不说的' 不匹配任何命令，忽略
2025-07-25 12:29:52 | DEBUG | 收到消息: {'MsgId': 1438830916, 'FromUserName': {'string': '48097389945@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 1, 'Content': {'string': 'wxid_84mmq4cu7ita22:\n你还要把工资给你妈，还要给你弟谈女朋友的钱'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1753417814, 'MsgSource': '<msgsource>\n\t<sec_msg_node>\n\t\t<alnode>\n\t\t\t<fr>1</fr>\n\t\t</alnode>\n\t</sec_msg_node>\n\t<pua>1</pua>\n\t<silence>0</silence>\n\t<membercount>63</membercount>\n\t<signature>N0_V1_QUhTst/4|v1_ehuZ9ZSC</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'PushContent': '喵小叶 : 你还要把工资给你妈，还要给你弟谈女朋友的钱', 'NewMsgId': 7731483488983906354, 'MsgSeq': 871398576}
2025-07-25 12:29:52 | INFO | 收到文本消息: 消息ID:1438830916 来自:48097389945@chatroom 发送人:wxid_84mmq4cu7ita22 @:[] 内容:你还要把工资给你妈，还要给你弟谈女朋友的钱
2025-07-25 12:29:52 | DEBUG | 处理消息内容: '你还要把工资给你妈，还要给你弟谈女朋友的钱'
2025-07-25 12:29:52 | DEBUG | 消息内容 '你还要把工资给你妈，还要给你弟谈女朋友的钱' 不匹配任何命令，忽略
2025-07-25 12:29:54 | DEBUG | 收到消息: {'MsgId': 943771647, 'FromUserName': {'string': '48097389945@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 1, 'Content': {'string': 'zll953369865:\n说说你和她的故事'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1753417815, 'MsgSource': '<msgsource>\n\t<sec_msg_node>\n\t\t<alnode>\n\t\t\t<fr>1</fr>\n\t\t</alnode>\n\t</sec_msg_node>\n\t<pua>1</pua>\n\t<silence>0</silence>\n\t<membercount>63</membercount>\n\t<signature>N0_V1_AervtF3G|v1_UYl0/pqQ</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'PushContent': '【微信红包】收到一个微信红包 : 说说你和她的故事', 'NewMsgId': 1880042098813225542, 'MsgSeq': 871398577}
2025-07-25 12:29:54 | INFO | 收到文本消息: 消息ID:943771647 来自:48097389945@chatroom 发送人:zll953369865 @:[] 内容:说说你和她的故事
2025-07-25 12:29:55 | DEBUG | 处理消息内容: '说说你和她的故事'
2025-07-25 12:29:55 | DEBUG | 消息内容 '说说你和她的故事' 不匹配任何命令，忽略
2025-07-25 12:29:56 | DEBUG | 收到消息: {'MsgId': 80700156, 'FromUserName': {'string': '48097389945@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 1, 'Content': {'string': 'wxid_l9koi6kli78i22:\n你情我愿的没有必要'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1753417815, 'MsgSource': '<msgsource>\n\t<pua>1</pua>\n\t<silence>0</silence>\n\t<membercount>63</membercount>\n\t<signature>N0_V1_8pirUTtW|v1_pBcYxBnB</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'PushContent': '十五 : 你情我愿的没有必要', 'NewMsgId': 125380383799104484, 'MsgSeq': 871398578}
2025-07-25 12:29:56 | INFO | 收到文本消息: 消息ID:80700156 来自:48097389945@chatroom 发送人:wxid_l9koi6kli78i22 @:[] 内容:你情我愿的没有必要
2025-07-25 12:29:57 | DEBUG | 处理消息内容: '你情我愿的没有必要'
2025-07-25 12:29:57 | DEBUG | 消息内容 '你情我愿的没有必要' 不匹配任何命令，忽略
2025-07-25 12:29:58 | DEBUG | 收到消息: {'MsgId': 725830672, 'FromUserName': {'string': '48097389945@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 47, 'Content': {'string': 'wxid_lneb7n23o4lg12:\n<msg><emoji fromusername = "wxid_lneb7n23o4lg12" tousername = "48097389945@chatroom" type="2" idbuffer="media:0_0" md5="9e85fd7e985765034abd3d01f14bdc01" len = "52225" productid="" androidmd5="9e85fd7e985765034abd3d01f14bdc01" androidlen="52225" s60v3md5 = "9e85fd7e985765034abd3d01f14bdc01" s60v3len="52225" s60v5md5 = "9e85fd7e985765034abd3d01f14bdc01" s60v5len="52225" cdnurl = "http://vweixinf.tc.qq.com/110/20401/stodownload?m=9e85fd7e985765034abd3d01f14bdc01&amp;filekey=30440201010430302e02016e0402534804203965383566643765393835373635303334616264336430316631346264633031020300cc01040d00000004627466730000000132&amp;hy=SH&amp;storeid=2688308590001978b7d2d13e40000006e01004fb1534823ff6171568b33e08&amp;ef=1&amp;bizid=1022" designerid = "" thumburl = "" encrypturl = "http://vweixinf.tc.qq.com/110/20402/stodownload?m=042a239e27021b3e157b0afda5ea9a15&amp;filekey=30440201010430302e02016e0402534804203034326132333965323730323162336531353762306166646135656139613135020300cc10040d00000004627466730000000132&amp;hy=SH&amp;storeid=268830859000250267d2d13e40000006e02004fb2534823ff6171568b33e1b&amp;ef=2&amp;bizid=1022" aeskey= "c808cd943a274208a95cf6b7c31b30ed" externurl = "http://vweixinf.tc.qq.com/110/20403/stodownload?m=7ba8b71894fa7ed252816a044c33e68b&amp;filekey=3043020101042f302d02016e040253480420376261386237313839346661376564323532383136613034346333336536386202026e20040d00000004627466730000000132&amp;hy=SH&amp;storeid=268830859000323557d2d13e40000006e03004fb3534823ff6171568b33e38&amp;ef=3&amp;bizid=1022" externmd5 = "aa6582ed44b1c0a380ac047ee3d40722" width= "360" height= "300" tpurl= "" tpauthkey= "" attachedtext= "" attachedtextcolor= "" lensid= "" emojiattr= "" linkid= "" desc= "" ></emoji>  </msg>'}, 'Status': 3, 'ImgStatus': 2, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1753417817, 'MsgSource': '<msgsource>\n\t<silence>0</silence>\n\t<membercount>63</membercount>\n\t<signature>N0_V1_J3kxvjOJ|v1_d9hw3oAr</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'PushContent': 'ᡣ在群聊中发了一个表情', 'NewMsgId': 5148213000559810738, 'MsgSeq': 871398579}
2025-07-25 12:29:58 | INFO | 收到表情消息: 消息ID:725830672 来自:48097389945@chatroom 发送人:wxid_lneb7n23o4lg12 MD5:9e85fd7e985765034abd3d01f14bdc01 大小:52225
2025-07-25 12:29:59 | DEBUG | [DoubaoImageRecognition] 缓存表情消息: 5148213000559810738
2025-07-25 12:29:59 | DEBUG | 收到消息: {'MsgId': 889129374, 'FromUserName': {'string': '48097389945@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 34, 'Content': {'string': 'wxid_lneb7n23o4lg12:\n<msg><voicemsg endflag="1" cancelflag="0" forwardflag="0" voiceformat="4" voicelength="1944" length="6144" bufid="0" aeskey="746e727a616e746b726d676c76616b67" voiceurl="3052020100044b304902010002046b3e4bb802033d11fe0204ed81507002046883085a042436373439346339642d643363352d346164302d383863352d37366361343431356235333502040524000f02010004002a5cd9b3" voicemd5="aeca3377e888f5912d8a548271bfabbd" clientmsgid="41653132633234663932353735336100181230072525a7b9a8ce17c100" fromusername="wxid_lneb7n23o4lg12" /></msg>'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1753417818, 'MsgSource': '<msgsource>\n\t<silence>0</silence>\n\t<membercount>63</membercount>\n\t<signature>N0_V1_F0gyVdBx|v1_4dmqkjQN</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'PushContent': 'ᡣ在群聊中发了一段语音', 'NewMsgId': 4018606893306361647, 'MsgSeq': 871398580}
2025-07-25 12:29:59 | INFO | 收到语音消息: 消息ID:889129374 来自:48097389945@chatroom 发送人:wxid_lneb7n23o4lg12 XML:
<msg><voicemsg endflag="1" cancelflag="0" forwardflag="0" voiceformat="4" voicelength="1944" length="6144" bufid="0" aeskey="746e727a616e746b726d676c76616b67" voiceurl="3052020100044b304902010002046b3e4bb802033d11fe0204ed81507002046883085a042436373439346339642d643363352d346164302d383863352d37366361343431356235333502040524000f02010004002a5cd9b3" voicemd5="aeca3377e888f5912d8a548271bfabbd" clientmsgid="41653132633234663932353735336100181230072525a7b9a8ce17c100" fromusername="wxid_lneb7n23o4lg12" /></msg>
2025-07-25 12:30:00 | DEBUG | 收到消息: {'MsgId': 1867442477, 'FromUserName': {'string': '48097389945@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 1, 'Content': {'string': 'rongcheng6630606:\n但不说我觉得对不起自己心里'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1753417819, 'MsgSource': '<msgsource>\n\t<pua>1</pua>\n\t<eggIncluded>1</eggIncluded>\n\t<silence>0</silence>\n\t<membercount>63</membercount>\n\t<signature>N0_V1_OTwC+Oma|v1_m9qnHHTi</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'PushContent': '× : 但不说我觉得对不起自己心里', 'NewMsgId': 7004869041068247727, 'MsgSeq': 871398581}
2025-07-25 12:30:00 | INFO | 收到文本消息: 消息ID:1867442477 来自:48097389945@chatroom 发送人:rongcheng6630606 @:[] 内容:但不说我觉得对不起自己心里
2025-07-25 12:30:00 | DEBUG | 处理消息内容: '但不说我觉得对不起自己心里'
2025-07-25 12:30:00 | DEBUG | 消息内容 '但不说我觉得对不起自己心里' 不匹配任何命令，忽略
2025-07-25 12:30:06 | DEBUG | 收到消息: {'MsgId': 840178082, 'FromUserName': {'string': '48097389945@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 1, 'Content': {'string': 'xiaomaochong:\n私事私下说哈'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1753417829, 'MsgSource': '<msgsource>\n\t<pua>1</pua>\n\t<silence>0</silence>\n\t<membercount>63</membercount>\n\t<signature>N0_V1_8fc257tc|v1_rQkTtvWY</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'PushContent': '小爱 : 私事私下说哈', 'NewMsgId': 2320455051612069397, 'MsgSeq': 871398582}
2025-07-25 12:30:06 | INFO | 收到文本消息: 消息ID:840178082 来自:48097389945@chatroom 发送人:xiaomaochong @:[] 内容:私事私下说哈
2025-07-25 12:30:07 | DEBUG | 处理消息内容: '私事私下说哈'
2025-07-25 12:30:07 | DEBUG | 消息内容 '私事私下说哈' 不匹配任何命令，忽略
2025-07-25 12:30:15 | DEBUG | 收到消息: {'MsgId': 951590741, 'FromUserName': {'string': '48097389945@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 1, 'Content': {'string': 'wxid_84mmq4cu7ita22:\n然后又在群里分享'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1753417838, 'MsgSource': '<msgsource>\n\t<sec_msg_node>\n\t\t<alnode>\n\t\t\t<fr>1</fr>\n\t\t</alnode>\n\t</sec_msg_node>\n\t<pua>1</pua>\n\t<silence>0</silence>\n\t<membercount>63</membercount>\n\t<signature>N0_V1_uoLDFTqn|v1_BH9x/SmX</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'PushContent': '喵小叶 : 然后又在群里分享', 'NewMsgId': 6710316899688238754, 'MsgSeq': 871398583}
2025-07-25 12:30:15 | INFO | 收到文本消息: 消息ID:951590741 来自:48097389945@chatroom 发送人:wxid_84mmq4cu7ita22 @:[] 内容:然后又在群里分享
2025-07-25 12:30:16 | DEBUG | 处理消息内容: '然后又在群里分享'
2025-07-25 12:30:16 | DEBUG | 消息内容 '然后又在群里分享' 不匹配任何命令，忽略
2025-07-25 12:30:24 | DEBUG | 收到消息: {'MsgId': 1538575924, 'FromUserName': {'string': '48097389945@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 1, 'Content': {'string': 'zll953369865:\n@小爱\u2005他俩私底下肯定是闹翻了'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1753417846, 'MsgSource': '<msgsource>\n\t<atuserlist><![CDATA[,xiaomaochong]]></atuserlist>\n\t<pua>1</pua>\n\t<silence>0</silence>\n\t<membercount>63</membercount>\n\t<signature>N0_V1_d93WMpMR|v1_ovbsEUFF</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'PushContent': '【微信红包】收到一个微信红包 : @小爱\u2005他俩私底下肯定是闹翻了', 'NewMsgId': 6718318798155954084, 'MsgSeq': 871398584}
2025-07-25 12:30:24 | INFO | 收到文本消息: 消息ID:1538575924 来自:48097389945@chatroom 发送人:zll953369865 @:['xiaomaochong'] 内容:@小爱 他俩私底下肯定是闹翻了
2025-07-25 12:30:24 | DEBUG | 处理消息内容: '@小爱 他俩私底下肯定是闹翻了'
2025-07-25 12:30:24 | DEBUG | 消息内容 '@小爱 他俩私底下肯定是闹翻了' 不匹配任何命令，忽略
2025-07-25 12:30:26 | DEBUG | 收到消息: {'MsgId': 2101776384, 'FromUserName': {'string': '48097389945@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 1, 'Content': {'string': 'zll953369865:\n兄弟'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1753417847, 'MsgSource': '<msgsource>\n\t<sec_msg_node>\n\t\t<alnode>\n\t\t\t<fr>1</fr>\n\t\t</alnode>\n\t</sec_msg_node>\n\t<pua>1</pua>\n\t<silence>0</silence>\n\t<membercount>63</membercount>\n\t<signature>N0_V1_KwAnWoce|v1_2VUmKk84</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'PushContent': '【微信红包】收到一个微信红包 : 兄弟', 'NewMsgId': 1337262790828325660, 'MsgSeq': 871398585}
2025-07-25 12:30:26 | INFO | 收到文本消息: 消息ID:2101776384 来自:48097389945@chatroom 发送人:zll953369865 @:[] 内容:兄弟
2025-07-25 12:30:26 | DEBUG | 处理消息内容: '兄弟'
2025-07-25 12:30:26 | DEBUG | 消息内容 '兄弟' 不匹配任何命令，忽略
2025-07-25 12:30:28 | DEBUG | 收到消息: {'MsgId': 2037117022, 'FromUserName': {'string': '48097389945@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 43, 'Content': {'string': 'xiaomaochong:\n<?xml version="1.0"?>\n<msg>\n\t<videomsg aeskey="afccd9fb218a5c106fff2c96772fbfc0" cdnvideourl="3057020100044b30490201000204aac0a3dd02032f841102045591587d02046881d147042466653665326563332d336637612d346261612d393466342d3463343535393931613263650204051408040201000405004c543d00" cdnthumbaeskey="afccd9fb218a5c106fff2c96772fbfc0" cdnthumburl="3057020100044b30490201000204aac0a3dd02032f841102045591587d02046881d147042466653665326563332d336637612d346261612d393466342d3463343535393931613263650204051408040201000405004c543d00" length="3160413" playlength="15" cdnthumblength="8553" cdnthumbwidth="203" cdnthumbheight="360" fromusername="xiaomaochong" md5="5ec8c3ef42b4a1d8922163e1b92d0e9e" newmd5="73dd661669a5f2162046f3a5a31d2776" isplaceholder="0" rawmd5="" rawlength="0" cdnrawvideourl="" cdnrawvideoaeskey="" overwritenewmsgid="0" originsourcemd5="" isad="0" />\n</msg>\n'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1753417847, 'MsgSource': '<msgsource>\n\t<alnode>\n\t\t<fr>5</fr>\n\t</alnode>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n\t<sec_msg_node>\n\t\t<uuid>5127412bd4815641292c939615ad9b37_</uuid>\n\t\t<risk-file-flag />\n\t\t<risk-file-md5-list />\n\t</sec_msg_node>\n\t<silence>0</silence>\n\t<membercount>63</membercount>\n\t<signature>N0_V1_X/Ao7hzu|v1_ugKV9m1S</signature>\n</msgsource>\n', 'PushContent': '小爱在群聊中发了一段视频', 'NewMsgId': 773838491259552134, 'MsgSeq': 871398586}
2025-07-25 12:30:28 | INFO | 收到视频消息: 消息ID:2037117022 来自:48097389945@chatroom 发送人:xiaomaochong XML:
<?xml version="1.0"?>
<msg>
	<videomsg aeskey="afccd9fb218a5c106fff2c96772fbfc0" cdnvideourl="3057020100044b30490201000204aac0a3dd02032f841102045591587d02046881d147042466653665326563332d336637612d346261612d393466342d3463343535393931613263650204051408040201000405004c543d00" cdnthumbaeskey="afccd9fb218a5c106fff2c96772fbfc0" cdnthumburl="3057020100044b30490201000204aac0a3dd02032f841102045591587d02046881d147042466653665326563332d336637612d346261612d393466342d3463343535393931613263650204051408040201000405004c543d00" length="3160413" playlength="15" cdnthumblength="8553" cdnthumbwidth="203" cdnthumbheight="360" fromusername="xiaomaochong" md5="5ec8c3ef42b4a1d8922163e1b92d0e9e" newmd5="73dd661669a5f2162046f3a5a31d2776" isplaceholder="0" rawmd5="" rawlength="0" cdnrawvideourl="" cdnrawvideoaeskey="" overwritenewmsgid="0" originsourcemd5="" isad="0" />
</msg>

2025-07-25 12:30:29 | DEBUG | 收到消息: {'MsgId': 633333541, 'FromUserName': {'string': '48097389945@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 1, 'Content': {'string': 'rongcheng6630606:\n真心把你当朋友，最后删除好友装不认识？'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1753417847, 'MsgSource': '<msgsource>\n\t<pua>1</pua>\n\t<eggIncluded>1</eggIncluded>\n\t<silence>0</silence>\n\t<membercount>63</membercount>\n\t<signature>N0_V1_IMWNJuHw|v1_Xj0eyUS/</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'PushContent': '× : 真心把你当朋友，最后删除好友装不认识？', 'NewMsgId': 1652311701004461652, 'MsgSeq': 871398587}
2025-07-25 12:30:29 | INFO | 收到文本消息: 消息ID:633333541 来自:48097389945@chatroom 发送人:rongcheng6630606 @:[] 内容:真心把你当朋友，最后删除好友装不认识？
2025-07-25 12:30:29 | DEBUG | 处理消息内容: '真心把你当朋友，最后删除好友装不认识？'
2025-07-25 12:30:29 | DEBUG | 消息内容 '真心把你当朋友，最后删除好友装不认识？' 不匹配任何命令，忽略
2025-07-25 12:30:31 | DEBUG | 收到消息: {'MsgId': 485253863, 'FromUserName': {'string': '48097389945@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 1, 'Content': {'string': 'wxid_84mmq4cu7ita22:\n你把钱给他们了，自己喜欢的东西没钱买'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1753417849, 'MsgSource': '<msgsource>\n\t<sec_msg_node>\n\t\t<alnode>\n\t\t\t<fr>1</fr>\n\t\t</alnode>\n\t</sec_msg_node>\n\t<pua>1</pua>\n\t<silence>0</silence>\n\t<membercount>63</membercount>\n\t<signature>N0_V1_Bfi5LPU0|v1_XsFrMf2q</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'PushContent': '喵小叶 : 你把钱给他们了，自己喜欢的东西没钱买', 'NewMsgId': 1045015097095677247, 'MsgSeq': 871398588}
2025-07-25 12:30:31 | INFO | 收到文本消息: 消息ID:485253863 来自:48097389945@chatroom 发送人:wxid_84mmq4cu7ita22 @:[] 内容:你把钱给他们了，自己喜欢的东西没钱买
2025-07-25 12:30:31 | DEBUG | 处理消息内容: '你把钱给他们了，自己喜欢的东西没钱买'
2025-07-25 12:30:31 | DEBUG | 消息内容 '你把钱给他们了，自己喜欢的东西没钱买' 不匹配任何命令，忽略
2025-07-25 12:30:33 | DEBUG | 收到消息: {'MsgId': 1401450442, 'FromUserName': {'string': '48097389945@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 1, 'Content': {'string': 'wxid_cwxauba42ki122:\n都吃饭去吧，好不好'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1753417852, 'MsgSource': '<msgsource>\n\t<pua>1</pua>\n\t<silence>0</silence>\n\t<membercount>63</membercount>\n\t<signature>N0_V1_wzuJEWan|v1_dkbUYe06</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'PushContent': 'ن初心ᯤ⁶ᴳ คิดถึงن : 都吃饭去吧，好不好', 'NewMsgId': 560169627679571640, 'MsgSeq': 871398589}
2025-07-25 12:30:33 | INFO | 收到文本消息: 消息ID:1401450442 来自:48097389945@chatroom 发送人:wxid_cwxauba42ki122 @:[] 内容:都吃饭去吧，好不好
2025-07-25 12:30:33 | DEBUG | 处理消息内容: '都吃饭去吧，好不好'
2025-07-25 12:30:33 | DEBUG | 消息内容 '都吃饭去吧，好不好' 不匹配任何命令，忽略
2025-07-25 12:30:35 | DEBUG | 收到消息: {'MsgId': 2039724930, 'FromUserName': {'string': '48097389945@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 1, 'Content': {'string': 'rongcheng6630606:\n你不假是什么'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1753417852, 'MsgSource': '<msgsource>\n\t<pua>1</pua>\n\t<eggIncluded>1</eggIncluded>\n\t<silence>0</silence>\n\t<membercount>63</membercount>\n\t<signature>N0_V1_bnqzGNVr|v1_bXgpzVFf</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'PushContent': '× : 你不假是什么', 'NewMsgId': 510067945007341389, 'MsgSeq': 871398590}
2025-07-25 12:30:35 | INFO | 收到文本消息: 消息ID:2039724930 来自:48097389945@chatroom 发送人:rongcheng6630606 @:[] 内容:你不假是什么
2025-07-25 12:30:35 | DEBUG | 处理消息内容: '你不假是什么'
2025-07-25 12:30:35 | DEBUG | 消息内容 '你不假是什么' 不匹配任何命令，忽略
2025-07-25 12:30:37 | DEBUG | 收到消息: {'MsgId': 504176833, 'FromUserName': {'string': '48097389945@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 47, 'Content': {'string': 'rongcheng6630606:\n<msg><emoji fromusername="rongcheng6630606" tousername="48097389945@chatroom" type="3" idbuffer="media:0_0" md5="935bfa9dd90e1cbf65a859c7bad1e1f5" len="35076" productid="" androidmd5="935bfa9dd90e1cbf65a859c7bad1e1f5" androidlen="35076" s60v3md5="935bfa9dd90e1cbf65a859c7bad1e1f5" s60v3len="35076" s60v5md5="935bfa9dd90e1cbf65a859c7bad1e1f5" s60v5len="35076" cdnurl="http://vweixinf.tc.qq.com/110/20401/stodownload?m=935bfa9dd90e1cbf65a859c7bad1e1f5&amp;filekey=30440201010430302e02016e04025348042039333562666139646439306531636266363561383539633762616431653166350203008904040d00000004627466730000000132&amp;hy=SH&amp;storeid=2643cfddb000526d867b6dad00000006e01004fb1534827f34b00b6effff74&amp;ef=1&amp;bizid=1022" designerid="" thumburl="" encrypturl="http://vweixinf.tc.qq.com/110/20402/stodownload?m=c8e08c36a1e51722c70c5ed7544bb349&amp;filekey=30440201010430302e02016e04025348042063386530386333366131653531373232633730633565643735343462623334390203008910040d00000004627466730000000132&amp;hy=SH&amp;storeid=2643cfddb0006166d67b6dad00000006e02004fb2534827f34b00b6effff8f&amp;ef=2&amp;bizid=1022" aeskey="5fc81eca71c943a2af344a9c2327e999" externurl="http://vweixinf.tc.qq.com/110/20403/stodownload?m=028c605c82c02d1af0f93e797cbd3582&amp;filekey=3043020101042f302d02016e0402534804203032386336303563383263303264316166306639336537393763626433353832020252a0040d00000004627466730000000132&amp;hy=SH&amp;storeid=2643cfddb00067fe467b6dad00000006e03004fb3534827f34b00b6effff9a&amp;ef=3&amp;bizid=1022" externmd5="84825538da67c6ada615636c9d9045c3" width="300" height="300" tpurl="" tpauthkey="" attachedtext="" attachedtextcolor="" lensid="" emojiattr="" linkid="" desc=""></emoji></msg>'}, 'Status': 3, 'ImgStatus': 2, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1753417857, 'MsgSource': '<msgsource>\n\t<silence>0</silence>\n\t<membercount>63</membercount>\n\t<signature>N0_V1_VUkT4Cmq|v1_poVTJ3h6</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'PushContent': '×在群聊中发了一个表情', 'NewMsgId': 6706286777003035038, 'MsgSeq': 871398591}
2025-07-25 12:30:37 | INFO | 收到表情消息: 消息ID:504176833 来自:48097389945@chatroom 发送人:rongcheng6630606 MD5:935bfa9dd90e1cbf65a859c7bad1e1f5 大小:35076
2025-07-25 12:30:38 | DEBUG | [DoubaoImageRecognition] 缓存表情消息: 6706286777003035038
2025-07-25 12:30:38 | DEBUG | 收到消息: {'MsgId': 963017217, 'FromUserName': {'string': '48097389945@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 47, 'Content': {'string': 'rongcheng6630606:\n<msg><emoji fromusername="rongcheng6630606" tousername="48097389945@chatroom" type="3" idbuffer="media:0_0" md5="935bfa9dd90e1cbf65a859c7bad1e1f5" len="35076" productid="" androidmd5="935bfa9dd90e1cbf65a859c7bad1e1f5" androidlen="35076" s60v3md5="935bfa9dd90e1cbf65a859c7bad1e1f5" s60v3len="35076" s60v5md5="935bfa9dd90e1cbf65a859c7bad1e1f5" s60v5len="35076" cdnurl="http://vweixinf.tc.qq.com/110/20401/stodownload?m=935bfa9dd90e1cbf65a859c7bad1e1f5&amp;filekey=30440201010430302e02016e04025348042039333562666139646439306531636266363561383539633762616431653166350203008904040d00000004627466730000000132&amp;hy=SH&amp;storeid=2643cfddb000526d867b6dad00000006e01004fb1534827f34b00b6effff74&amp;ef=1&amp;bizid=1022" designerid="" thumburl="" encrypturl="http://vweixinf.tc.qq.com/110/20402/stodownload?m=c8e08c36a1e51722c70c5ed7544bb349&amp;filekey=30440201010430302e02016e04025348042063386530386333366131653531373232633730633565643735343462623334390203008910040d00000004627466730000000132&amp;hy=SH&amp;storeid=2643cfddb0006166d67b6dad00000006e02004fb2534827f34b00b6effff8f&amp;ef=2&amp;bizid=1022" aeskey="5fc81eca71c943a2af344a9c2327e999" externurl="http://vweixinf.tc.qq.com/110/20403/stodownload?m=028c605c82c02d1af0f93e797cbd3582&amp;filekey=3043020101042f302d02016e0402534804203032386336303563383263303264316166306639336537393763626433353832020252a0040d00000004627466730000000132&amp;hy=SH&amp;storeid=2643cfddb00067fe467b6dad00000006e03004fb3534827f34b00b6effff9a&amp;ef=3&amp;bizid=1022" externmd5="84825538da67c6ada615636c9d9045c3" width="300" height="300" tpurl="" tpauthkey="" attachedtext="" attachedtextcolor="" lensid="" emojiattr="" linkid="" desc=""></emoji></msg>'}, 'Status': 3, 'ImgStatus': 2, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1753417858, 'MsgSource': '<msgsource>\n\t<silence>0</silence>\n\t<membercount>63</membercount>\n\t<signature>N0_V1_K1tCXfaj|v1_LjExx8iL</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'PushContent': '×在群聊中发了一个表情', 'NewMsgId': 7387475933720053129, 'MsgSeq': 871398592}
2025-07-25 12:30:38 | INFO | 收到表情消息: 消息ID:963017217 来自:48097389945@chatroom 发送人:rongcheng6630606 MD5:935bfa9dd90e1cbf65a859c7bad1e1f5 大小:35076
2025-07-25 12:30:38 | DEBUG | [DoubaoImageRecognition] 缓存表情消息: 7387475933720053129
2025-07-25 12:30:39 | DEBUG | 收到消息: {'MsgId': 1190004987, 'FromUserName': {'string': '48097389945@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 47, 'Content': {'string': 'rongcheng6630606:\n<msg><emoji fromusername="rongcheng6630606" tousername="48097389945@chatroom" type="3" idbuffer="media:0_0" md5="935bfa9dd90e1cbf65a859c7bad1e1f5" len="35076" productid="" androidmd5="935bfa9dd90e1cbf65a859c7bad1e1f5" androidlen="35076" s60v3md5="935bfa9dd90e1cbf65a859c7bad1e1f5" s60v3len="35076" s60v5md5="935bfa9dd90e1cbf65a859c7bad1e1f5" s60v5len="35076" cdnurl="http://vweixinf.tc.qq.com/110/20401/stodownload?m=935bfa9dd90e1cbf65a859c7bad1e1f5&amp;filekey=30440201010430302e02016e04025348042039333562666139646439306531636266363561383539633762616431653166350203008904040d00000004627466730000000132&amp;hy=SH&amp;storeid=2643cfddb000526d867b6dad00000006e01004fb1534827f34b00b6effff74&amp;ef=1&amp;bizid=1022" designerid="" thumburl="" encrypturl="http://vweixinf.tc.qq.com/110/20402/stodownload?m=c8e08c36a1e51722c70c5ed7544bb349&amp;filekey=30440201010430302e02016e04025348042063386530386333366131653531373232633730633565643735343462623334390203008910040d00000004627466730000000132&amp;hy=SH&amp;storeid=2643cfddb0006166d67b6dad00000006e02004fb2534827f34b00b6effff8f&amp;ef=2&amp;bizid=1022" aeskey="5fc81eca71c943a2af344a9c2327e999" externurl="http://vweixinf.tc.qq.com/110/20403/stodownload?m=028c605c82c02d1af0f93e797cbd3582&amp;filekey=3043020101042f302d02016e0402534804203032386336303563383263303264316166306639336537393763626433353832020252a0040d00000004627466730000000132&amp;hy=SH&amp;storeid=2643cfddb00067fe467b6dad00000006e03004fb3534827f34b00b6effff9a&amp;ef=3&amp;bizid=1022" externmd5="84825538da67c6ada615636c9d9045c3" width="300" height="300" tpurl="" tpauthkey="" attachedtext="" attachedtextcolor="" lensid="" emojiattr="" linkid="" desc=""></emoji></msg>'}, 'Status': 3, 'ImgStatus': 2, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1753417858, 'MsgSource': '<msgsource>\n\t<silence>0</silence>\n\t<membercount>63</membercount>\n\t<signature>N0_V1_K1tCXfaj|v1_LjExx8iL</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'PushContent': '×在群聊中发了一个表情', 'NewMsgId': 4633719551635858139, 'MsgSeq': 871398593}
2025-07-25 12:30:39 | INFO | 收到表情消息: 消息ID:1190004987 来自:48097389945@chatroom 发送人:rongcheng6630606 MD5:935bfa9dd90e1cbf65a859c7bad1e1f5 大小:35076
2025-07-25 12:30:39 | DEBUG | [DoubaoImageRecognition] 缓存表情消息: 4633719551635858139
2025-07-25 12:30:39 | DEBUG | 收到消息: {'MsgId': 2115813286, 'FromUserName': {'string': '48097389945@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 49, 'Content': {'string': 'wxid_84mmq4cu7ita22:\n<?xml version="1.0"?>\n<msg>\n\t<appmsg appid="" sdkver="0">\n\t\t<title>你换头像换名字</title>\n\t\t<des />\n\t\t<action>view</action>\n\t\t<type>57</type>\n\t\t<showtype>0</showtype>\n\t\t<content />\n\t\t<url />\n\t\t<dataurl />\n\t\t<lowurl />\n\t\t<lowdataurl />\n\t\t<recorditem />\n\t\t<thumburl />\n\t\t<messageaction />\n\t\t<laninfo />\n\t\t<refermsg>\n\t\t\t<type>1</type>\n\t\t\t<svrid>1652311701004461652</svrid>\n\t\t\t<fromusr>48097389945@chatroom</fromusr>\n\t\t\t<chatusr>rongcheng6630606</chatusr>\n\t\t\t<createtime>1753417847</createtime>\n\t\t\t<msgsource>&lt;msgsource&gt;\n    &lt;pua&gt;1&lt;/pua&gt;\n    &lt;eggIncluded&gt;1&lt;/eggIncluded&gt;\n    &lt;silence&gt;1&lt;/silence&gt;\n    &lt;membercount&gt;63&lt;/membercount&gt;\n    &lt;signature&gt;N0_V1_iw6q82Us|v1_3DP5tk1J&lt;/signature&gt;\n    &lt;tmp_node&gt;\n        &lt;publisher-id /&gt;\n    &lt;/tmp_node&gt;\n    &lt;sec_msg_node&gt;\n        &lt;alnode&gt;\n            &lt;fr&gt;1&lt;/fr&gt;\n        &lt;/alnode&gt;\n    &lt;/sec_msg_node&gt;\n&lt;/msgsource&gt;\n</msgsource>\n\t\t\t<content>真心把你当朋友，最后删除好友装不认识？</content>\n\t\t\t<displayname>×</displayname>\n\t\t</refermsg>\n\t\t<extinfo />\n\t\t<sourceusername />\n\t\t<sourcedisplayname />\n\t\t<commenturl />\n\t\t<appattach>\n\t\t\t<totallen>0</totallen>\n\t\t\t<attachid />\n\t\t\t<emoticonmd5 />\n\t\t\t<fileext />\n\t\t\t<aeskey />\n\t\t</appattach>\n\t\t<webviewshared>\n\t\t\t<publisherId />\n\t\t\t<publisherReqId>0</publisherReqId>\n\t\t</webviewshared>\n\t\t<weappinfo>\n\t\t\t<pagepath />\n\t\t\t<username />\n\t\t\t<appid />\n\t\t\t<appservicetype>0</appservicetype>\n\t\t</weappinfo>\n\t\t<websearch />\n\t</appmsg>\n\t<fromusername>wxid_84mmq4cu7ita22</fromusername>\n\t<scene>0</scene>\n\t<appinfo>\n\t\t<version>1</version>\n\t\t<appname></appname>\n\t</appinfo>\n\t<commenturl></commenturl>\n</msg>\n'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1753417858, 'MsgSource': '<msgsource>\n\t<pua>1</pua>\n\t<sec_msg_node>\n\t\t<uuid>3ef9f588055968561bd3c93ce6459a9e_</uuid>\n\t\t<risk-file-flag />\n\t\t<risk-file-md5-list />\n\t</sec_msg_node>\n\t<silence>0</silence>\n\t<membercount>63</membercount>\n\t<signature>N0_V1_f9mMDBRO|v1_pQiCstvD</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'PushContent': '喵小叶 : 你换头像换名字', 'NewMsgId': 2715799549750399583, 'MsgSeq': 871398594}
2025-07-25 12:30:39 | DEBUG | 从群聊消息中提取发送者: wxid_84mmq4cu7ita22
2025-07-25 12:30:39 | DEBUG | 使用已解析的XML处理引用消息
2025-07-25 12:30:39 | INFO | 收到引用消息: 消息ID:2115813286 来自:48097389945@chatroom 发送人:wxid_84mmq4cu7ita22 内容:你换头像换名字 引用类型:1
2025-07-25 12:30:40 | INFO | [DouBaoImageToImage] 收到引用消息: 你换头像换名字
2025-07-25 12:30:40 | INFO | [TimerTask] 收到引用消息调试信息:
2025-07-25 12:30:40 | INFO |   - 消息内容: 你换头像换名字
2025-07-25 12:30:40 | INFO |   - 群组ID: 48097389945@chatroom
2025-07-25 12:30:40 | INFO |   - 发送人: wxid_84mmq4cu7ita22
2025-07-25 12:30:40 | INFO |   - 引用信息: {'MsgType': 1, 'Content': '真心把你当朋友，最后删除好友装不认识？', 'Msgid': '1652311701004461652', 'NewMsgId': '1652311701004461652', 'ToWxid': 'wxid_4usgcju5ey9q29', 'FromWxid': '48097389945@chatroom', 'Nickname': '×', 'MsgSource': '<msgsource>\n    <pua>1</pua>\n    <eggIncluded>1</eggIncluded>\n    <silence>1</silence>\n    <membercount>63</membercount>\n    <signature>N0_V1_iw6q82Us|v1_3DP5tk1J</signature>\n    <tmp_node>\n        <publisher-id />\n    </tmp_node>\n    <sec_msg_node>\n        <alnode>\n            <fr>1</fr>\n        </alnode>\n    </sec_msg_node>\n</msgsource>\n', 'Createtime': '1753417847', 'SenderWxid': 'wxid_84mmq4cu7ita22'}
2025-07-25 12:30:40 | INFO |   - 引用消息ID: 
2025-07-25 12:30:40 | INFO |   - 引用消息类型: 
2025-07-25 12:30:40 | INFO |   - 引用消息内容: 真心把你当朋友，最后删除好友装不认识？
2025-07-25 12:30:40 | INFO |   - 引用消息发送人: wxid_84mmq4cu7ita22
2025-07-25 12:30:40 | DEBUG | 收到消息: {'MsgId': 770774826, 'FromUserName': {'string': '48097389945@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 3, 'Content': {'string': 'wxid_lneb7n23o4lg12:\n<?xml version="1.0"?>\n<msg>\n\t<img aeskey="7c5eb1d75f6d1180249b16249808ef8e" encryver="1" cdnthumbaeskey="7c5eb1d75f6d1180249b16249808ef8e" cdnthumburl="3057020100044b304902010002046b3e4bb802032f5149020498312275020468830884042463646664303238612d616637322d343533302d613737382d343936656238613963396131020405292a010201000405004c537500" cdnthumblength="7804" cdnthumbheight="120" cdnthumbwidth="120" cdnmidheight="0" cdnmidwidth="0" cdnhdheight="0" cdnhdwidth="0" cdnmidimgurl="3057020100044b304902010002046b3e4bb802032f5149020498312275020468830884042463646664303238612d616637322d343533302d613737382d343936656238613963396131020405292a010201000405004c537500" length="30583" cdnbigimgurl="3057020100044b304902010002046b3e4bb802032f5149020498312275020468830884042463646664303238612d616637322d343533302d613737382d343936656238613963396131020405292a010201000405004c537500" hdlength="70219" md5="e87c69418b67f13bc428b35d96ae0141" hevc_mid_size="30583" originsourcemd5="e87c69418b67f13bc428b35d96ae0141">\n\t\t<secHashInfoBase64 />\n\t\t<live>\n\t\t\t<duration>0</duration>\n\t\t\t<size>0</size>\n\t\t\t<md5 />\n\t\t\t<fileid />\n\t\t\t<hdsize>0</hdsize>\n\t\t\t<hdmd5 />\n\t\t\t<hdfileid />\n\t\t\t<stillimagetimems>0</stillimagetimems>\n\t\t</live>\n\t</img>\n\t<appinfo>\n\t\t<appid>wxe3ad19e142df87b3</appid>\n\t\t<version>5</version>\n\t\t<appname>麻豆约拍</appname>\n\t\t<isforceupdate>0</isforceupdate>\n\t\t<messageaction />\n\t\t<messageext />\n\t\t<mediatagname />\n\t</appinfo>\n\t<platform_signature />\n\t<imgdatahash />\n\t<ImgSourceInfo>\n\t\t<ImgSourceUrl />\n\t\t<BizType>0</BizType>\n\t</ImgSourceInfo>\n</msg>\n'}, 'Status': 3, 'ImgStatus': 2, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1753417860, 'MsgSource': '<msgsource>\n\t<alnode>\n\t\t<fr>2</fr>\n\t</alnode>\n\t<sec_msg_node>\n\t\t<uuid>78c0f37a4a8745849e55c71a1a91aa94_</uuid>\n\t\t<risk-file-flag />\n\t\t<risk-file-md5-list />\n\t</sec_msg_node>\n\t<silence>0</silence>\n\t<membercount>63</membercount>\n\t<signature>N0_V1_Gh/7G7D0|v1_65Wh69PO</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'PushContent': 'ᡣ在群聊中发了一张图片', 'NewMsgId': 191344666743083329, 'MsgSeq': 871398595}
2025-07-25 12:30:40 | INFO | 收到图片消息: 消息ID:770774826 来自:48097389945@chatroom 发送人:wxid_lneb7n23o4lg12 XML:<?xml version="1.0"?><msg><img aeskey="7c5eb1d75f6d1180249b16249808ef8e" encryver="1" cdnthumbaeskey="7c5eb1d75f6d1180249b16249808ef8e" cdnthumburl="3057020100044b304902010002046b3e4bb802032f5149020498312275020468830884042463646664303238612d616637322d343533302d613737382d343936656238613963396131020405292a010201000405004c537500" cdnthumblength="7804" cdnthumbheight="120" cdnthumbwidth="120" cdnmidheight="0" cdnmidwidth="0" cdnhdheight="0" cdnhdwidth="0" cdnmidimgurl="3057020100044b304902010002046b3e4bb802032f5149020498312275020468830884042463646664303238612d616637322d343533302d613737382d343936656238613963396131020405292a010201000405004c537500" length="30583" cdnbigimgurl="3057020100044b304902010002046b3e4bb802032f5149020498312275020468830884042463646664303238612d616637322d343533302d613737382d343936656238613963396131020405292a010201000405004c537500" hdlength="70219" md5="e87c69418b67f13bc428b35d96ae0141" hevc_mid_size="30583" originsourcemd5="e87c69418b67f13bc428b35d96ae0141"><secHashInfoBase64 /><live><duration>0</duration><size>0</size><md5 /><fileid /><hdsize>0</hdsize><hdmd5 /><hdfileid /><stillimagetimems>0</stillimagetimems></live></img><appinfo><appid>wxe3ad19e142df87b3</appid><version>5</version><appname>麻豆约拍</appname><isforceupdate>0</isforceupdate><messageaction /><messageext /><mediatagname /></appinfo><platform_signature /><imgdatahash /><ImgSourceInfo><ImgSourceUrl /><BizType>0</BizType></ImgSourceInfo></msg>
2025-07-25 12:30:40 | INFO | [ImageEcho] 保存图片信息成功，当前群 48097389945@chatroom 已存储 5 张图片
2025-07-25 12:30:40 | INFO | [TimerTask] 缓存图片消息: 770774826
2025-07-25 12:30:41 | DEBUG | 收到消息: {'MsgId': 1657090709, 'FromUserName': {'string': '48097389945@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 1, 'Content': {'string': 'wxid_84mmq4cu7ita22:\n我为啥记得住'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1753417862, 'MsgSource': '<msgsource>\n\t<sec_msg_node>\n\t\t<alnode>\n\t\t\t<fr>1</fr>\n\t\t</alnode>\n\t</sec_msg_node>\n\t<pua>1</pua>\n\t<silence>0</silence>\n\t<membercount>63</membercount>\n\t<signature>N0_V1_wugWtNI+|v1_Vgq+4/kk</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'PushContent': '喵小叶 : 我为啥记得住', 'NewMsgId': 7580629583718387878, 'MsgSeq': 871398596}
2025-07-25 12:30:41 | INFO | 收到文本消息: 消息ID:1657090709 来自:48097389945@chatroom 发送人:wxid_84mmq4cu7ita22 @:[] 内容:我为啥记得住
2025-07-25 12:30:41 | DEBUG | 处理消息内容: '我为啥记得住'
2025-07-25 12:30:41 | DEBUG | 消息内容 '我为啥记得住' 不匹配任何命令，忽略
2025-07-25 12:30:43 | DEBUG | 收到消息: {'MsgId': 984246822, 'FromUserName': {'string': '48097389945@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 34, 'Content': {'string': 'xiaomaochong:\n<msg><voicemsg endflag="1" cancelflag="0" forwardflag="0" voiceformat="4" voicelength="34000" length="99027" bufid="0" aeskey="667a6d6d616a666b7a61656d666d6368" voiceurl="3052020100044b304902010002049363814102033d14ba02046e3a949d020468830889042438653061383465372d386665632d343939652d383138312d64313035336537383932636202040528000f02010004001dc74187" voicemd5="63eb4d1fc1d67e895eadfb222d629e29" clientmsgid="494cd5716bb4ce7e67f76aaff6aa3e5648097389945@chatroom_47873_1753417863" fromusername="xiaomaochong" /></msg>'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1753417865, 'MsgSource': '<msgsource>\n\t<silence>0</silence>\n\t<membercount>63</membercount>\n\t<signature>N0_V1_M9Iq3Ak4|v1_kQh7O70l</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'PushContent': '小爱在群聊中发了一段语音', 'NewMsgId': 8655746133419833011, 'MsgSeq': 871398597}
2025-07-25 12:30:43 | INFO | 收到语音消息: 消息ID:984246822 来自:48097389945@chatroom 发送人:xiaomaochong XML:
<msg><voicemsg endflag="1" cancelflag="0" forwardflag="0" voiceformat="4" voicelength="34000" length="99027" bufid="0" aeskey="667a6d6d616a666b7a61656d666d6368" voiceurl="3052020100044b304902010002049363814102033d14ba02046e3a949d020468830889042438653061383465372d386665632d343939652d383138312d64313035336537383932636202040528000f02010004001dc74187" voicemd5="63eb4d1fc1d67e895eadfb222d629e29" clientmsgid="494cd5716bb4ce7e67f76aaff6aa3e5648097389945@chatroom_47873_1753417863" fromusername="xiaomaochong" /></msg>
2025-07-25 12:30:56 | DEBUG | 收到消息: {'MsgId': 349455196, 'FromUserName': {'string': '48097389945@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 1, 'Content': {'string': 'zll953369865:\n@小爱\u2005他俩肯定有故事'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1753417879, 'MsgSource': '<msgsource>\n\t<atuserlist><![CDATA[,xiaomaochong]]></atuserlist>\n\t<pua>1</pua>\n\t<silence>0</silence>\n\t<membercount>63</membercount>\n\t<signature>N0_V1_nm28zSyj|v1_IGGiPXdg</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'PushContent': '【微信红包】收到一个微信红包 : @小爱\u2005他俩肯定有故事', 'NewMsgId': 6960786960769805394, 'MsgSeq': 871398598}
2025-07-25 12:30:56 | INFO | 收到文本消息: 消息ID:349455196 来自:48097389945@chatroom 发送人:zll953369865 @:['xiaomaochong'] 内容:@小爱 他俩肯定有故事
2025-07-25 12:30:57 | DEBUG | 处理消息内容: '@小爱 他俩肯定有故事'
2025-07-25 12:30:57 | DEBUG | 消息内容 '@小爱 他俩肯定有故事' 不匹配任何命令，忽略
2025-07-25 12:31:08 | DEBUG | 收到消息: {'MsgId': 356226346, 'FromUserName': {'string': '48097389945@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 1, 'Content': {'string': 'wxid_cwxauba42ki122:\n[擦汗]'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1753417891, 'MsgSource': '<msgsource>\n\t<pua>1</pua>\n\t<silence>0</silence>\n\t<membercount>63</membercount>\n\t<signature>N0_V1_dde7hNxA|v1_pSDTA4px</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'PushContent': 'ن初心ᯤ⁶ᴳ คิดถึงن : [擦汗]', 'NewMsgId': 1908734385385704686, 'MsgSeq': 871398599}
2025-07-25 12:31:08 | INFO | 收到表情消息: 消息ID:356226346 来自:48097389945@chatroom 发送人:wxid_cwxauba42ki122 @:[] 内容:[擦汗]
2025-07-25 12:31:09 | DEBUG | [DoubaoImageRecognition] 缓存表情消息: 1908734385385704686
2025-07-25 12:31:11 | DEBUG | 收到消息: {'MsgId': 1010338662, 'FromUserName': {'string': '48097389945@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 49, 'Content': {'string': 'wxid_84mmq4cu7ita22:\n<?xml version="1.0"?>\n<msg>\n\t<appmsg appid="" sdkver="0">\n\t\t<title>看笑话把你就</title>\n\t\t<des />\n\t\t<action>view</action>\n\t\t<type>57</type>\n\t\t<showtype>0</showtype>\n\t\t<content />\n\t\t<url />\n\t\t<dataurl />\n\t\t<lowurl />\n\t\t<lowdataurl />\n\t\t<recorditem />\n\t\t<thumburl />\n\t\t<messageaction />\n\t\t<laninfo />\n\t\t<refermsg>\n\t\t\t<type>1</type>\n\t\t\t<svrid>6960786960769805394</svrid>\n\t\t\t<fromusr>48097389945@chatroom</fromusr>\n\t\t\t<chatusr>zll953369865</chatusr>\n\t\t\t<createtime>1753417879</createtime>\n\t\t\t<msgsource>&lt;msgsource&gt;\n    &lt;atuserlist&gt;\n        &lt;![CDATA[,xiaomaochong]]&gt;\n    &lt;/atuserlist&gt;\n    &lt;pua&gt;1&lt;/pua&gt;\n    &lt;silence&gt;1&lt;/silence&gt;\n    &lt;membercount&gt;63&lt;/membercount&gt;\n    &lt;signature&gt;N0_V1_+OV0RgUI|v1_Lz4bgFWn&lt;/signature&gt;\n    &lt;tmp_node&gt;\n        &lt;publisher-id /&gt;\n    &lt;/tmp_node&gt;\n    &lt;sec_msg_node&gt;\n        &lt;alnode&gt;\n            &lt;fr&gt;1&lt;/fr&gt;\n        &lt;/alnode&gt;\n    &lt;/sec_msg_node&gt;\n&lt;/msgsource&gt;\n</msgsource>\n\t\t\t<content>@小爱\u2005他俩肯定有故事</content>\n\t\t\t<displayname>【微信红包】收到一个微信红包</displayname>\n\t\t</refermsg>\n\t\t<extinfo />\n\t\t<sourceusername />\n\t\t<sourcedisplayname />\n\t\t<commenturl />\n\t\t<appattach>\n\t\t\t<totallen>0</totallen>\n\t\t\t<attachid />\n\t\t\t<emoticonmd5 />\n\t\t\t<fileext />\n\t\t\t<aeskey />\n\t\t</appattach>\n\t\t<webviewshared>\n\t\t\t<publisherId />\n\t\t\t<publisherReqId>0</publisherReqId>\n\t\t</webviewshared>\n\t\t<weappinfo>\n\t\t\t<pagepath />\n\t\t\t<username />\n\t\t\t<appid />\n\t\t\t<appservicetype>0</appservicetype>\n\t\t</weappinfo>\n\t\t<websearch />\n\t</appmsg>\n\t<fromusername>wxid_84mmq4cu7ita22</fromusername>\n\t<scene>0</scene>\n\t<appinfo>\n\t\t<version>1</version>\n\t\t<appname></appname>\n\t</appinfo>\n\t<commenturl></commenturl>\n</msg>\n'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1753417894, 'MsgSource': '<msgsource>\n\t<pua>1</pua>\n\t<sec_msg_node>\n\t\t<uuid>530e2541349f0c382783547429ebfc8d_</uuid>\n\t\t<risk-file-flag />\n\t\t<risk-file-md5-list />\n\t</sec_msg_node>\n\t<silence>0</silence>\n\t<membercount>63</membercount>\n\t<signature>N0_V1_+u1LdNZs|v1_FTyx2/j4</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'PushContent': '喵小叶 : 看笑话把你就', 'NewMsgId': 6129563850721970210, 'MsgSeq': 871398600}
2025-07-25 12:31:11 | DEBUG | 从群聊消息中提取发送者: wxid_84mmq4cu7ita22
2025-07-25 12:31:11 | DEBUG | 使用已解析的XML处理引用消息
2025-07-25 12:31:11 | INFO | 收到引用消息: 消息ID:1010338662 来自:48097389945@chatroom 发送人:wxid_84mmq4cu7ita22 内容:看笑话把你就 引用类型:1
2025-07-25 12:31:12 | INFO | [DouBaoImageToImage] 收到引用消息: 看笑话把你就
2025-07-25 12:31:12 | INFO | [TimerTask] 收到引用消息调试信息:
2025-07-25 12:31:12 | INFO |   - 消息内容: 看笑话把你就
2025-07-25 12:31:12 | INFO |   - 群组ID: 48097389945@chatroom
2025-07-25 12:31:12 | INFO |   - 发送人: wxid_84mmq4cu7ita22
2025-07-25 12:31:12 | INFO |   - 引用信息: {'MsgType': 1, 'Content': '@小爱\u2005他俩肯定有故事', 'Msgid': '6960786960769805394', 'NewMsgId': '6960786960769805394', 'ToWxid': 'wxid_4usgcju5ey9q29', 'FromWxid': '48097389945@chatroom', 'Nickname': '【微信红包】收到一个微信红包', 'MsgSource': '<msgsource>\n    <atuserlist>\n        <![CDATA[,xiaomaochong]]>\n    </atuserlist>\n    <pua>1</pua>\n    <silence>1</silence>\n    <membercount>63</membercount>\n    <signature>N0_V1_+OV0RgUI|v1_Lz4bgFWn</signature>\n    <tmp_node>\n        <publisher-id />\n    </tmp_node>\n    <sec_msg_node>\n        <alnode>\n            <fr>1</fr>\n        </alnode>\n    </sec_msg_node>\n</msgsource>\n', 'Createtime': '1753417879', 'SenderWxid': 'wxid_84mmq4cu7ita22'}
2025-07-25 12:31:12 | INFO |   - 引用消息ID: 
2025-07-25 12:31:12 | INFO |   - 引用消息类型: 
2025-07-25 12:31:12 | INFO |   - 引用消息内容: @小爱 他俩肯定有故事
2025-07-25 12:31:12 | INFO |   - 引用消息发送人: wxid_84mmq4cu7ita22
2025-07-25 12:31:13 | DEBUG | 收到消息: {'MsgId': 1138443852, 'FromUserName': {'string': '48097389945@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 1, 'Content': {'string': 'wxid_l9koi6kli78i22:\n@37.2℃\u2005'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1753417896, 'MsgSource': '<msgsource>\n\t<atuserlist><![CDATA[wxid_mwil20w1e1j422]]></atuserlist>\n\t<pua>1</pua>\n\t<silence>0</silence>\n\t<membercount>63</membercount>\n\t<signature>N0_V1_RrrdRjc+|v1_RKyDULcL</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'PushContent': '十五 : @37.2℃\u2005', 'NewMsgId': 2204754211328193046, 'MsgSeq': 871398601}
2025-07-25 12:31:13 | INFO | 收到文本消息: 消息ID:1138443852 来自:48097389945@chatroom 发送人:wxid_l9koi6kli78i22 @:['wxid_mwil20w1e1j422'] 内容:@37.2℃ 
2025-07-25 12:31:14 | DEBUG | 处理消息内容: '@37.2℃'
2025-07-25 12:31:14 | DEBUG | 消息内容 '@37.2℃' 不匹配任何命令，忽略
2025-07-25 12:31:17 | DEBUG | 收到消息: {'MsgId': 1541388091, 'FromUserName': {'string': '48097389945@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 10002, 'Content': {'string': '48097389945@chatroom:\n<sysmsg type="pat">\n<pat>\n  <fromusername>wxid_cwxauba42ki122</fromusername>\n  <chatusername>48097389945@chatroom</chatusername>\n  <pattedusername>zll953369865</pattedusername>\n  <patsuffix><![CDATA[的脸，亲了一口[炸弹]]]></patsuffix>\n  <patsuffixversion>2</patsuffixversion>\n\n\n\n\n\n\n  <template><![CDATA["${wxid_cwxauba42ki122}" 拍了拍 "${zll953369865}" 的脸，亲了一口[炸弹]]]></template>\n\n\n\n\n\n\n\n</pat>\n</sysmsg>'}, 'Status': 4, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1753417899, 'MsgSource': '<msgsource>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'NewMsgId': 7003864709280960473, 'MsgSeq': 871398602}
2025-07-25 12:31:17 | DEBUG | 系统消息类型: pat
2025-07-25 12:31:17 | INFO | 收到拍一拍消息: 消息ID:1541388091 来自:48097389945@chatroom 发送人:48097389945@chatroom 拍者:wxid_cwxauba42ki122 被拍:zll953369865 后缀:的脸，亲了一口[炸弹]
2025-07-25 12:31:18 | DEBUG | [PatReply] 被拍者 zll953369865 不是目标用户 wxid_4usgcju5ey9q29，跳过
2025-07-25 12:31:24 | DEBUG | 收到消息: {'MsgId': 1500712950, 'FromUserName': {'string': '48097389945@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 34, 'Content': {'string': 'xiaomaochong:\n<msg><voicemsg endflag="1" cancelflag="0" forwardflag="0" voiceformat="4" voicelength="45000" length="134939" bufid="0" aeskey="7671797865756c6a7169716a71736a6a" voiceurl="3052020100044b304902010002049363814102033d14ba0204a739949d0204688308b3042433363739303062352d613264312d343739392d626333312d38626566633731393764353002040528000f02010004001dc74187" voicemd5="d268b1c775808216844fb264c62c89b4" clientmsgid="494cd5716bb4ce7e67f76aaff6aa3e5648097389945@chatroom_47880_1753417905" fromusername="xiaomaochong" /></msg>'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1753417907, 'MsgSource': '<msgsource>\n\t<silence>0</silence>\n\t<membercount>63</membercount>\n\t<signature>N0_V1_jeMS9R5X|v1_4hqcDIvy</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'PushContent': '小爱在群聊中发了一段语音', 'NewMsgId': 3810106552453401215, 'MsgSeq': 871398603}
2025-07-25 12:31:24 | INFO | 收到语音消息: 消息ID:1500712950 来自:48097389945@chatroom 发送人:xiaomaochong XML:
<msg><voicemsg endflag="1" cancelflag="0" forwardflag="0" voiceformat="4" voicelength="45000" length="134939" bufid="0" aeskey="7671797865756c6a7169716a71736a6a" voiceurl="3052020100044b304902010002049363814102033d14ba0204a739949d0204688308b3042433363739303062352d613264312d343739392d626333312d38626566633731393764353002040528000f02010004001dc74187" voicemd5="d268b1c775808216844fb264c62c89b4" clientmsgid="494cd5716bb4ce7e67f76aaff6aa3e5648097389945@chatroom_47880_1753417905" fromusername="xiaomaochong" /></msg>
2025-07-25 12:31:30 | DEBUG | 收到消息: {'MsgId': 1432024912, 'FromUserName': {'string': '48097389945@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 1, 'Content': {'string': 'rongcheng6630606:\n@喵小叶\u2005你好假[微笑]'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1753417912, 'MsgSource': '<msgsource>\n\t<atuserlist><![CDATA[wxid_84mmq4cu7ita22]]></atuserlist>\n\t<pua>1</pua>\n\t<eggIncluded>1</eggIncluded>\n\t<silence>0</silence>\n\t<membercount>63</membercount>\n\t<signature>N0_V1_52dgD1UJ|v1_Ef0k6dg3</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'PushContent': '× : @喵小叶\u2005你好假[微笑]', 'NewMsgId': 9214531499638726515, 'MsgSeq': 871398604}
2025-07-25 12:31:30 | INFO | 收到文本消息: 消息ID:1432024912 来自:48097389945@chatroom 发送人:rongcheng6630606 @:['wxid_84mmq4cu7ita22'] 内容:@喵小叶 你好假[微笑]
2025-07-25 12:31:31 | DEBUG | 处理消息内容: '@喵小叶 你好假[微笑]'
2025-07-25 12:31:31 | DEBUG | 消息内容 '@喵小叶 你好假[微笑]' 不匹配任何命令，忽略
2025-07-25 12:31:35 | DEBUG | 收到消息: {'MsgId': 678441019, 'FromUserName': {'string': '48097389945@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 1, 'Content': {'string': 'wxid_l9koi6kli78i22:\n好聚好散'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1753417917, 'MsgSource': '<msgsource>\n\t<pua>1</pua>\n\t<silence>0</silence>\n\t<membercount>63</membercount>\n\t<signature>N0_V1_yz5capG/|v1_79kOLCcc</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'PushContent': '十五 : 好聚好散', 'NewMsgId': 8152883368335938936, 'MsgSeq': 871398605}
2025-07-25 12:31:35 | INFO | 收到文本消息: 消息ID:678441019 来自:48097389945@chatroom 发送人:wxid_l9koi6kli78i22 @:[] 内容:好聚好散
2025-07-25 12:31:35 | DEBUG | 处理消息内容: '好聚好散'
2025-07-25 12:31:35 | DEBUG | 消息内容 '好聚好散' 不匹配任何命令，忽略
2025-07-25 12:31:37 | DEBUG | 收到消息: {'MsgId': 2068383349, 'FromUserName': {'string': '48097389945@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 1, 'Content': {'string': 'wxid_l9koi6kli78i22:\n你情我愿'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1753417919, 'MsgSource': '<msgsource>\n\t<pua>1</pua>\n\t<silence>0</silence>\n\t<membercount>63</membercount>\n\t<signature>N0_V1_0caja+CU|v1_U0+QFFBL</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'PushContent': '十五 : 你情我愿', 'NewMsgId': 6827559519345167586, 'MsgSeq': 871398606}
2025-07-25 12:31:37 | INFO | 收到文本消息: 消息ID:2068383349 来自:48097389945@chatroom 发送人:wxid_l9koi6kli78i22 @:[] 内容:你情我愿
2025-07-25 12:31:37 | DEBUG | 处理消息内容: '你情我愿'
2025-07-25 12:31:37 | DEBUG | 消息内容 '你情我愿' 不匹配任何命令，忽略
2025-07-25 12:31:48 | DEBUG | 收到消息: {'MsgId': 939808706, 'FromUserName': {'string': '48097389945@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 1, 'Content': {'string': 'zll953369865:\n肯定是之前谈过吧'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1753417930, 'MsgSource': '<msgsource>\n\t<sec_msg_node>\n\t\t<alnode>\n\t\t\t<fr>1</fr>\n\t\t</alnode>\n\t</sec_msg_node>\n\t<pua>1</pua>\n\t<silence>0</silence>\n\t<membercount>63</membercount>\n\t<signature>N0_V1_phjfnhvZ|v1_brxOAShf</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'PushContent': '【微信红包】收到一个微信红包 : 肯定是之前谈过吧', 'NewMsgId': 9199779553959446368, 'MsgSeq': 871398607}
2025-07-25 12:31:48 | INFO | 收到文本消息: 消息ID:939808706 来自:48097389945@chatroom 发送人:zll953369865 @:[] 内容:肯定是之前谈过吧
2025-07-25 12:31:48 | DEBUG | 处理消息内容: '肯定是之前谈过吧'
2025-07-25 12:31:48 | DEBUG | 消息内容 '肯定是之前谈过吧' 不匹配任何命令，忽略
2025-07-25 12:31:51 | DEBUG | 收到消息: {'MsgId': 2100883005, 'FromUserName': {'string': '48097389945@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 1, 'Content': {'string': 'rongcheng6630606:\n简称:普信女'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1753417934, 'MsgSource': '<msgsource>\n\t<pua>1</pua>\n\t<eggIncluded>1</eggIncluded>\n\t<silence>0</silence>\n\t<membercount>63</membercount>\n\t<signature>N0_V1_YfXwx0F8|v1_ZDDApl3t</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'PushContent': '× : 简称:普信女', 'NewMsgId': 8596879019262545259, 'MsgSeq': 871398608}
2025-07-25 12:31:51 | INFO | 收到文本消息: 消息ID:2100883005 来自:48097389945@chatroom 发送人:rongcheng6630606 @:[] 内容:简称:普信女
2025-07-25 12:31:52 | DEBUG | 处理消息内容: '简称:普信女'
2025-07-25 12:31:52 | DEBUG | 消息内容 '简称:普信女' 不匹配任何命令，忽略
2025-07-25 12:31:55 | DEBUG | 收到消息: {'MsgId': 1301866352, 'FromUserName': {'string': '48097389945@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 47, 'Content': {'string': 'rongcheng6630606:\n<msg><emoji fromusername="rongcheng6630606" tousername="48097389945@chatroom" type="3" idbuffer="media:0_0" md5="935bfa9dd90e1cbf65a859c7bad1e1f5" len="35076" productid="" androidmd5="935bfa9dd90e1cbf65a859c7bad1e1f5" androidlen="35076" s60v3md5="935bfa9dd90e1cbf65a859c7bad1e1f5" s60v3len="35076" s60v5md5="935bfa9dd90e1cbf65a859c7bad1e1f5" s60v5len="35076" cdnurl="http://vweixinf.tc.qq.com/110/20401/stodownload?m=935bfa9dd90e1cbf65a859c7bad1e1f5&amp;filekey=30440201010430302e02016e04025348042039333562666139646439306531636266363561383539633762616431653166350203008904040d00000004627466730000000132&amp;hy=SH&amp;storeid=2643cfddb000526d867b6dad00000006e01004fb1534827f34b00b6effff74&amp;ef=1&amp;bizid=1022" designerid="" thumburl="" encrypturl="http://vweixinf.tc.qq.com/110/20402/stodownload?m=c8e08c36a1e51722c70c5ed7544bb349&amp;filekey=30440201010430302e02016e04025348042063386530386333366131653531373232633730633565643735343462623334390203008910040d00000004627466730000000132&amp;hy=SH&amp;storeid=2643cfddb0006166d67b6dad00000006e02004fb2534827f34b00b6effff8f&amp;ef=2&amp;bizid=1022" aeskey="5fc81eca71c943a2af344a9c2327e999" externurl="http://vweixinf.tc.qq.com/110/20403/stodownload?m=028c605c82c02d1af0f93e797cbd3582&amp;filekey=3043020101042f302d02016e0402534804203032386336303563383263303264316166306639336537393763626433353832020252a0040d00000004627466730000000132&amp;hy=SH&amp;storeid=2643cfddb00067fe467b6dad00000006e03004fb3534827f34b00b6effff9a&amp;ef=3&amp;bizid=1022" externmd5="84825538da67c6ada615636c9d9045c3" width="300" height="300" tpurl="" tpauthkey="" attachedtext="" attachedtextcolor="" lensid="" emojiattr="" linkid="" desc=""></emoji></msg>'}, 'Status': 3, 'ImgStatus': 2, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1753417937, 'MsgSource': '<msgsource>\n\t<silence>0</silence>\n\t<membercount>63</membercount>\n\t<signature>N0_V1_AfymuPL0|v1_FjLrOt5I</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'PushContent': '×在群聊中发了一个表情', 'NewMsgId': 6922615599277522960, 'MsgSeq': 871398609}
2025-07-25 12:31:55 | INFO | 收到表情消息: 消息ID:1301866352 来自:48097389945@chatroom 发送人:rongcheng6630606 MD5:935bfa9dd90e1cbf65a859c7bad1e1f5 大小:35076
2025-07-25 12:31:55 | DEBUG | [DoubaoImageRecognition] 缓存表情消息: 6922615599277522960
2025-07-25 12:32:03 | DEBUG | 收到消息: {'MsgId': 173179774, 'FromUserName': {'string': '47325400669@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 1, 'Content': {'string': 'heaventt:\n又快又稳'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1753417945, 'MsgSource': '<msgsource>\n\t<pua>1</pua>\n\t<eggIncluded>1</eggIncluded>\n\t<silence>0</silence>\n\t<membercount>221</membercount>\n\t<signature>N0_V1_yL4k/YS1|v1_yNvU7LOO</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'PushContent': 'heaven : 又快又稳', 'NewMsgId': 5452603839594027739, 'MsgSeq': 871398610}
2025-07-25 12:32:03 | INFO | 收到文本消息: 消息ID:173179774 来自:47325400669@chatroom 发送人:heaventt @:[] 内容:又快又稳
2025-07-25 12:32:03 | DEBUG | 处理消息内容: '又快又稳'
2025-07-25 12:32:03 | DEBUG | 消息内容 '又快又稳' 不匹配任何命令，忽略
2025-07-25 12:32:11 | DEBUG | 收到消息: {'MsgId': 1796408017, 'FromUserName': {'string': '48097389945@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 1, 'Content': {'string': 'wxid_84mmq4cu7ita22:\n我就是普信女'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1753417954, 'MsgSource': '<msgsource>\n\t<sec_msg_node>\n\t\t<alnode>\n\t\t\t<fr>1</fr>\n\t\t</alnode>\n\t</sec_msg_node>\n\t<pua>1</pua>\n\t<silence>0</silence>\n\t<membercount>63</membercount>\n\t<signature>N0_V1_3Xwn4WlQ|v1_DqB6Q706</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'PushContent': '喵小叶 : 我就是普信女', 'NewMsgId': 1704904632575999264, 'MsgSeq': 871398611}
2025-07-25 12:32:11 | INFO | 收到文本消息: 消息ID:1796408017 来自:48097389945@chatroom 发送人:wxid_84mmq4cu7ita22 @:[] 内容:我就是普信女
2025-07-25 12:32:12 | DEBUG | 处理消息内容: '我就是普信女'
2025-07-25 12:32:12 | DEBUG | 消息内容 '我就是普信女' 不匹配任何命令，忽略
2025-07-25 12:32:13 | DEBUG | 收到消息: {'MsgId': 662946234, 'FromUserName': {'string': '48097389945@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 1, 'Content': {'string': 'wxid_84mmq4cu7ita22:\n啦啦啦'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1753417956, 'MsgSource': '<msgsource>\n\t<sec_msg_node>\n\t\t<alnode>\n\t\t\t<fr>1</fr>\n\t\t</alnode>\n\t</sec_msg_node>\n\t<pua>1</pua>\n\t<silence>0</silence>\n\t<membercount>63</membercount>\n\t<signature>N0_V1_eH5mAQqR|v1_6u+5JMST</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'PushContent': '喵小叶 : 啦啦啦', 'NewMsgId': 4291777483150305976, 'MsgSeq': 871398612}
2025-07-25 12:32:13 | INFO | 收到文本消息: 消息ID:662946234 来自:48097389945@chatroom 发送人:wxid_84mmq4cu7ita22 @:[] 内容:啦啦啦
2025-07-25 12:32:14 | DEBUG | 处理消息内容: '啦啦啦'
2025-07-25 12:32:14 | DEBUG | 消息内容 '啦啦啦' 不匹配任何命令，忽略
2025-07-25 12:32:17 | DEBUG | 收到消息: {'MsgId': 591675781, 'FromUserName': {'string': '48097389945@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 1, 'Content': {'string': 'wxid_84mmq4cu7ita22:\n好了吗'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1753417959, 'MsgSource': '<msgsource>\n\t<sec_msg_node>\n\t\t<alnode>\n\t\t\t<fr>1</fr>\n\t\t</alnode>\n\t</sec_msg_node>\n\t<pua>1</pua>\n\t<silence>0</silence>\n\t<membercount>63</membercount>\n\t<signature>N0_V1_GhbV2yNa|v1_UrSIua4I</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'PushContent': '喵小叶 : 好了吗', 'NewMsgId': 3195858800575850075, 'MsgSeq': 871398613}
2025-07-25 12:32:17 | INFO | 收到文本消息: 消息ID:591675781 来自:48097389945@chatroom 发送人:wxid_84mmq4cu7ita22 @:[] 内容:好了吗
2025-07-25 12:32:17 | DEBUG | 处理消息内容: '好了吗'
2025-07-25 12:32:17 | DEBUG | 消息内容 '好了吗' 不匹配任何命令，忽略
2025-07-25 12:32:19 | DEBUG | 收到消息: {'MsgId': 1676091205, 'FromUserName': {'string': '48097389945@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 1, 'Content': {'string': 'rongcheng6630606:\n群折叠了，打扰了小爱@小爱\u2005'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1753417960, 'MsgSource': '<msgsource>\n\t<atuserlist><![CDATA[xiaomaochong]]></atuserlist>\n\t<pua>1</pua>\n\t<eggIncluded>1</eggIncluded>\n\t<silence>0</silence>\n\t<membercount>63</membercount>\n\t<signature>N0_V1_bv8LztFK|v1_bkZhj4UE</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'PushContent': '× : 群折叠了，打扰了小爱@小爱\u2005', 'NewMsgId': 7282083461035062003, 'MsgSeq': 871398614}
2025-07-25 12:32:19 | INFO | 收到文本消息: 消息ID:1676091205 来自:48097389945@chatroom 发送人:rongcheng6630606 @:['xiaomaochong'] 内容:群折叠了，打扰了小爱@小爱 
2025-07-25 12:32:19 | DEBUG | 处理消息内容: '群折叠了，打扰了小爱@小爱'
2025-07-25 12:32:19 | DEBUG | 消息内容 '群折叠了，打扰了小爱@小爱' 不匹配任何命令，忽略
2025-07-25 12:32:22 | DEBUG | 收到消息: {'MsgId': 702574213, 'FromUserName': {'string': '48097389945@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 47, 'Content': {'string': 'rongcheng6630606:\n<msg><emoji fromusername="rongcheng6630606" tousername="48097389945@chatroom" type="2" idbuffer="media:0_0" md5="6c97caa28d17dc52dd40e130cd0429b9" len="29619" productid="com.tencent.xin.emoticon.person.stiker_1647392931411681d2f5388ee2" androidmd5="6c97caa28d17dc52dd40e130cd0429b9" androidlen="29619" s60v3md5="6c97caa28d17dc52dd40e130cd0429b9" s60v3len="29619" s60v5md5="6c97caa28d17dc52dd40e130cd0429b9" s60v5len="29619" cdnurl="http://wxapp.tc.qq.com/262/20304/stodownload?m=6c97caa28d17dc52dd40e130cd0429b9&amp;filekey=30340201010420301e020201060402535a04106c97caa28d17dc52dd40e130cd0429b9020273b3040d00000004627466730000000132&amp;hy=SZ&amp;storeid=266170452000ce0221efa36b00000010600004f50535a2d8498809693295aa&amp;bizid=1023" designerid="" thumburl="http://mmbiz.qpic.cn/mmemoticon/ajNVdqHZLLA4XqclcticIpGReHiaXruaib34pDVmobcEU1PlB1XPxQle0JubtbSVRgr/0" encrypturl="http://wxapp.tc.qq.com/262/20304/stodownload?m=666ac21b80630fe56a7f2f9b7a8dfc1f&amp;filekey=30340201010420301e020201060402535a0410666ac21b80630fe56a7f2f9b7a8dfc1f020273c0040d00000004627466730000000132&amp;hy=SZ&amp;storeid=2661704530001a2261efa36b00000010600004f50535a173908e0b69004cef&amp;bizid=1023" aeskey="972a3919ef5bb7269cc9bb972492072c" externurl="http://wxapp.tc.qq.com/262/20304/stodownload?m=1cb31c0b6b457b1796bc07ca991a0242&amp;filekey=30340201010420301e020201060402535a04101cb31c0b6b457b1796bc07ca991a024202021440040d00000004627466730000000132&amp;hy=SZ&amp;storeid=2661704530004bbfd1efa36b00000010600004f50535a0df6bbc1e67574c92&amp;bizid=1023" externmd5="3e4f655490ed6b390997411a1d67ca05" width="240" height="240" tpurl="" tpauthkey="" attachedtext="" attachedtextcolor="" lensid="" emojiattr="" linkid="" desc="ChEKB2RlZmF1bHQSBua6nOS6hg=="></emoji></msg>'}, 'Status': 3, 'ImgStatus': 2, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1753417965, 'MsgSource': '<msgsource>\n\t<silence>0</silence>\n\t<membercount>63</membercount>\n\t<signature>N0_V1_BEkvaliW|v1_UpmY2JSl</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'PushContent': '×在群聊中发了一个表情', 'NewMsgId': 5810094214397933518, 'MsgSeq': 871398615}
2025-07-25 12:32:22 | INFO | 收到表情消息: 消息ID:702574213 来自:48097389945@chatroom 发送人:rongcheng6630606 MD5:6c97caa28d17dc52dd40e130cd0429b9 大小:29619
2025-07-25 12:32:23 | DEBUG | [DoubaoImageRecognition] 缓存表情消息: 5810094214397933518
2025-07-25 12:32:33 | DEBUG | 收到消息: {'MsgId': 1756799714, 'FromUserName': {'string': '48097389945@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 34, 'Content': {'string': 'xiaomaochong:\n<msg><voicemsg endflag="1" cancelflag="0" forwardflag="0" voiceformat="4" voicelength="17000" length="47346" bufid="0" aeskey="656e79656872786a6a7373687377616e" voiceurl="3052020100044b304902010002049363814102033d14ba02044424949d0204688308f8042439373935373361392d343363662d343065392d613835332d36623535363738666639663402040528000f02010004001dc74187" voicemd5="dbd580e425a77e6a39314abcd9475ff1" clientmsgid="494cd5716bb4ce7e67f76aaff6aa3e5648097389945@chatroom_47893_1753417975" fromusername="xiaomaochong" /></msg>'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1753417976, 'MsgSource': '<msgsource>\n\t<silence>0</silence>\n\t<membercount>63</membercount>\n\t<signature>N0_V1_d5iS026T|v1_uDCX5LcA</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'PushContent': '小爱在群聊中发了一段语音', 'NewMsgId': 570734929086872621, 'MsgSeq': 871398616}
2025-07-25 12:32:33 | INFO | 收到语音消息: 消息ID:1756799714 来自:48097389945@chatroom 发送人:xiaomaochong XML:
<msg><voicemsg endflag="1" cancelflag="0" forwardflag="0" voiceformat="4" voicelength="17000" length="47346" bufid="0" aeskey="656e79656872786a6a7373687377616e" voiceurl="3052020100044b304902010002049363814102033d14ba02044424949d0204688308f8042439373935373361392d343363662d343065392d613835332d36623535363738666639663402040528000f02010004001dc74187" voicemd5="dbd580e425a77e6a39314abcd9475ff1" clientmsgid="494cd5716bb4ce7e67f76aaff6aa3e5648097389945@chatroom_47893_1753417975" fromusername="xiaomaochong" /></msg>
2025-07-25 12:32:37 | DEBUG | 收到消息: {'MsgId': 536990247, 'FromUserName': {'string': '48097389945@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 1, 'Content': {'string': 'wxid_84mmq4cu7ita22:\n追过来找我说话'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1753417980, 'MsgSource': '<msgsource>\n\t<sec_msg_node>\n\t\t<alnode>\n\t\t\t<fr>1</fr>\n\t\t</alnode>\n\t</sec_msg_node>\n\t<pua>1</pua>\n\t<silence>0</silence>\n\t<membercount>63</membercount>\n\t<signature>N0_V1_37dnZh4j|v1_vF3Q5bM6</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'PushContent': '喵小叶 : 追过来找我说话', 'NewMsgId': 961698493281424402, 'MsgSeq': 871398617}
2025-07-25 12:32:37 | INFO | 收到文本消息: 消息ID:536990247 来自:48097389945@chatroom 发送人:wxid_84mmq4cu7ita22 @:[] 内容:追过来找我说话
2025-07-25 12:32:37 | DEBUG | 处理消息内容: '追过来找我说话'
2025-07-25 12:32:37 | DEBUG | 消息内容 '追过来找我说话' 不匹配任何命令，忽略
2025-07-25 12:32:39 | DEBUG | 收到消息: {'MsgId': 1254223886, 'FromUserName': {'string': '48097389945@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 1, 'Content': {'string': 'wxid_84mmq4cu7ita22:\n要命'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1753417982, 'MsgSource': '<msgsource>\n\t<sec_msg_node>\n\t\t<alnode>\n\t\t\t<fr>1</fr>\n\t\t</alnode>\n\t</sec_msg_node>\n\t<pua>1</pua>\n\t<silence>0</silence>\n\t<membercount>63</membercount>\n\t<signature>N0_V1_4MAtekCz|v1_bHfOnuOL</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'PushContent': '喵小叶 : 要命', 'NewMsgId': 936466007808357065, 'MsgSeq': 871398618}
2025-07-25 12:32:39 | INFO | 收到文本消息: 消息ID:1254223886 来自:48097389945@chatroom 发送人:wxid_84mmq4cu7ita22 @:[] 内容:要命
2025-07-25 12:32:40 | DEBUG | 处理消息内容: '要命'
2025-07-25 12:32:40 | DEBUG | 消息内容 '要命' 不匹配任何命令，忽略
2025-07-25 12:32:43 | DEBUG | 收到消息: {'MsgId': 1765203196, 'FromUserName': {'string': '48097389945@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 1, 'Content': {'string': 'wxid_84mmq4cu7ita22:\n太可怕了'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1753417986, 'MsgSource': '<msgsource>\n\t<sec_msg_node>\n\t\t<alnode>\n\t\t\t<fr>1</fr>\n\t\t</alnode>\n\t</sec_msg_node>\n\t<pua>1</pua>\n\t<silence>0</silence>\n\t<membercount>63</membercount>\n\t<signature>N0_V1_8T1zYE8+|v1_Y8ATwgw7</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'PushContent': '喵小叶 : 太可怕了', 'NewMsgId': 5651693168754312605, 'MsgSeq': 871398619}
2025-07-25 12:32:43 | INFO | 收到文本消息: 消息ID:1765203196 来自:48097389945@chatroom 发送人:wxid_84mmq4cu7ita22 @:[] 内容:太可怕了
2025-07-25 12:32:44 | DEBUG | 处理消息内容: '太可怕了'
2025-07-25 12:32:44 | DEBUG | 消息内容 '太可怕了' 不匹配任何命令，忽略
2025-07-25 12:33:04 | DEBUG | 收到消息: {'MsgId': 1586706487, 'FromUserName': {'string': '48097389945@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 1, 'Content': {'string': 'rongcheng6630606:\n@喵小叶\u2005因为憋着心里难受'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1753418006, 'MsgSource': '<msgsource>\n\t<atuserlist><![CDATA[wxid_84mmq4cu7ita22]]></atuserlist>\n\t<pua>1</pua>\n\t<eggIncluded>1</eggIncluded>\n\t<silence>0</silence>\n\t<membercount>63</membercount>\n\t<signature>N0_V1_0h1cqNgQ|v1_SIseTuLU</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'PushContent': '× : @喵小叶\u2005因为憋着心里难受', 'NewMsgId': 2759529321815963972, 'MsgSeq': 871398620}
2025-07-25 12:33:04 | INFO | 收到文本消息: 消息ID:1586706487 来自:48097389945@chatroom 发送人:rongcheng6630606 @:['wxid_84mmq4cu7ita22'] 内容:@喵小叶 因为憋着心里难受
2025-07-25 12:33:04 | DEBUG | 处理消息内容: '@喵小叶 因为憋着心里难受'
2025-07-25 12:33:04 | DEBUG | 消息内容 '@喵小叶 因为憋着心里难受' 不匹配任何命令，忽略
2025-07-25 12:33:15 | DEBUG | 收到消息: {'MsgId': 479487454, 'FromUserName': {'string': '48097389945@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 1, 'Content': {'string': 'wxid_84mmq4cu7ita22:\n你是个坏人'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1753418017, 'MsgSource': '<msgsource>\n\t<sec_msg_node>\n\t\t<alnode>\n\t\t\t<fr>1</fr>\n\t\t</alnode>\n\t</sec_msg_node>\n\t<pua>1</pua>\n\t<silence>0</silence>\n\t<membercount>63</membercount>\n\t<signature>N0_V1_0I/yo8bw|v1_sQrGZ0w3</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'PushContent': '喵小叶 : 你是个坏人', 'NewMsgId': 7416743869151575673, 'MsgSeq': 871398621}
2025-07-25 12:33:15 | INFO | 收到文本消息: 消息ID:479487454 来自:48097389945@chatroom 发送人:wxid_84mmq4cu7ita22 @:[] 内容:你是个坏人
2025-07-25 12:33:15 | DEBUG | 处理消息内容: '你是个坏人'
2025-07-25 12:33:15 | DEBUG | 消息内容 '你是个坏人' 不匹配任何命令，忽略
2025-07-25 12:33:18 | DEBUG | 收到消息: {'MsgId': 1069619850, 'FromUserName': {'string': '48097389945@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 1, 'Content': {'string': 'wxid_84mmq4cu7ita22:\n伤害了我'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1753418020, 'MsgSource': '<msgsource>\n\t<sec_msg_node>\n\t\t<alnode>\n\t\t\t<fr>1</fr>\n\t\t</alnode>\n\t</sec_msg_node>\n\t<pua>1</pua>\n\t<silence>0</silence>\n\t<membercount>63</membercount>\n\t<signature>N0_V1_tcFHEnd+|v1_DF4BuYC2</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'PushContent': '喵小叶 : 伤害了我', 'NewMsgId': 7544353404878757131, 'MsgSeq': 871398622}
2025-07-25 12:33:18 | INFO | 收到文本消息: 消息ID:1069619850 来自:48097389945@chatroom 发送人:wxid_84mmq4cu7ita22 @:[] 内容:伤害了我
2025-07-25 12:33:18 | DEBUG | 处理消息内容: '伤害了我'
2025-07-25 12:33:18 | DEBUG | 消息内容 '伤害了我' 不匹配任何命令，忽略
2025-07-25 12:33:22 | DEBUG | 收到消息: {'MsgId': 975458066, 'FromUserName': {'string': '48097389945@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 1, 'Content': {'string': 'wxid_84mmq4cu7ita22:\n你做墙头草'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1753418024, 'MsgSource': '<msgsource>\n\t<sec_msg_node>\n\t\t<alnode>\n\t\t\t<fr>1</fr>\n\t\t</alnode>\n\t</sec_msg_node>\n\t<pua>1</pua>\n\t<silence>0</silence>\n\t<membercount>63</membercount>\n\t<signature>N0_V1_STXLFenK|v1_FEwbOpou</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'PushContent': '喵小叶 : 你做墙头草', 'NewMsgId': 5087431216549773061, 'MsgSeq': 871398623}
2025-07-25 12:33:22 | INFO | 收到文本消息: 消息ID:975458066 来自:48097389945@chatroom 发送人:wxid_84mmq4cu7ita22 @:[] 内容:你做墙头草
2025-07-25 12:33:22 | DEBUG | 处理消息内容: '你做墙头草'
2025-07-25 12:33:22 | DEBUG | 消息内容 '你做墙头草' 不匹配任何命令，忽略
2025-07-25 12:33:27 | DEBUG | 收到消息: {'MsgId': 315958565, 'FromUserName': {'string': '48097389945@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 1, 'Content': {'string': 'wxid_84mmq4cu7ita22:\n既要又要'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1753418030, 'MsgSource': '<msgsource>\n\t<sec_msg_node>\n\t\t<alnode>\n\t\t\t<fr>1</fr>\n\t\t</alnode>\n\t</sec_msg_node>\n\t<pua>1</pua>\n\t<silence>0</silence>\n\t<membercount>63</membercount>\n\t<signature>N0_V1_UwAcG5bT|v1_LoPdsX65</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'PushContent': '喵小叶 : 既要又要', 'NewMsgId': 3307473083724418620, 'MsgSeq': 871398624}
2025-07-25 12:33:27 | INFO | 收到文本消息: 消息ID:315958565 来自:48097389945@chatroom 发送人:wxid_84mmq4cu7ita22 @:[] 内容:既要又要
2025-07-25 12:33:27 | DEBUG | 处理消息内容: '既要又要'
2025-07-25 12:33:27 | DEBUG | 消息内容 '既要又要' 不匹配任何命令，忽略
2025-07-25 12:33:36 | DEBUG | 收到消息: {'MsgId': 1585326162, 'FromUserName': {'string': '48097389945@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 1, 'Content': {'string': 'rongcheng6630606:\n咱俩没什么关系何来伤害'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1753418039, 'MsgSource': '<msgsource>\n\t<pua>1</pua>\n\t<eggIncluded>1</eggIncluded>\n\t<silence>0</silence>\n\t<membercount>63</membercount>\n\t<signature>N0_V1_8L7OeNUv|v1_3rHMrBpD</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'PushContent': '× : 咱俩没什么关系何来伤害', 'NewMsgId': 5867456402930324418, 'MsgSeq': 871398625}
2025-07-25 12:33:36 | INFO | 收到文本消息: 消息ID:1585326162 来自:48097389945@chatroom 发送人:rongcheng6630606 @:[] 内容:咱俩没什么关系何来伤害
2025-07-25 12:33:37 | DEBUG | 处理消息内容: '咱俩没什么关系何来伤害'
2025-07-25 12:33:37 | DEBUG | 消息内容 '咱俩没什么关系何来伤害' 不匹配任何命令，忽略
2025-07-25 12:33:39 | DEBUG | 收到消息: {'MsgId': 1320685406, 'FromUserName': {'string': '48097389945@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 1, 'Content': {'string': 'rongcheng6630606:\n笑话'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1753418041, 'MsgSource': '<msgsource>\n\t<pua>1</pua>\n\t<eggIncluded>1</eggIncluded>\n\t<silence>0</silence>\n\t<membercount>63</membercount>\n\t<signature>N0_V1_aJ11SkvY|v1_xfRxhzpO</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'PushContent': '× : 笑话', 'NewMsgId': 1561950164343331583, 'MsgSeq': 871398626}
2025-07-25 12:33:39 | INFO | 收到文本消息: 消息ID:1320685406 来自:48097389945@chatroom 发送人:rongcheng6630606 @:[] 内容:笑话
2025-07-25 12:33:39 | DEBUG | 处理消息内容: '笑话'
2025-07-25 12:33:39 | DEBUG | 消息内容 '笑话' 不匹配任何命令，忽略
2025-07-25 12:33:41 | DEBUG | 收到消息: {'MsgId': 1439432810, 'FromUserName': {'string': '48097389945@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 49, 'Content': {'string': 'xiaomaochong:\n<?xml version="1.0"?>\n<msg>\n\t<appmsg appid="" sdkver="0">\n\t\t<title>初三语文考，作文半命题题目：____ 初三。大家都写的“拼搏初三”、“热血初三”什么的，我和他们不一样，我定的是“大年初三”。——佚名</title>\n\t\t<des />\n\t\t<username />\n\t\t<action>view</action>\n\t\t<type>53</type>\n\t\t<showtype>0</showtype>\n\t\t<content />\n\t\t<url />\n\t\t<lowurl />\n\t\t<forwardflag>0</forwardflag>\n\t\t<dataurl />\n\t\t<lowdataurl />\n\t\t<contentattr>0</contentattr>\n\t\t<streamvideo>\n\t\t\t<streamvideourl />\n\t\t\t<streamvideototaltime>0</streamvideototaltime>\n\t\t\t<streamvideotitle />\n\t\t\t<streamvideowording />\n\t\t\t<streamvideoweburl />\n\t\t\t<streamvideothumburl />\n\t\t\t<streamvideoaduxinfo />\n\t\t\t<streamvideopublishid />\n\t\t</streamvideo>\n\t\t<canvasPageItem>\n\t\t\t<canvasPageXml><![CDATA[]]></canvasPageXml>\n\t\t</canvasPageItem>\n\t\t<appattach>\n\t\t\t<totallen>0</totallen>\n\t\t\t<attachid />\n\t\t\t<cdnattachurl />\n\t\t\t<emoticonmd5></emoticonmd5>\n\t\t\t<aeskey></aeskey>\n\t\t\t<fileext />\n\t\t\t<islargefilemsg>0</islargefilemsg>\n\t\t</appattach>\n\t\t<extinfo>\n\t\t\t<solitaire_info><![CDATA[]]></solitaire_info>\n\t\t</extinfo>\n\t\t<androidsource>0</androidsource>\n\t\t<thumburl />\n\t\t<mediatagname />\n\t\t<messageaction><![CDATA[]]></messageaction>\n\t\t<messageext><![CDATA[]]></messageext>\n\t\t<emoticongift>\n\t\t\t<packageflag>0</packageflag>\n\t\t\t<packageid />\n\t\t</emoticongift>\n\t\t<emoticonshared>\n\t\t\t<packageflag>0</packageflag>\n\t\t\t<packageid />\n\t\t</emoticonshared>\n\t\t<designershared>\n\t\t\t<designeruin>0</designeruin>\n\t\t\t<designername>null</designername>\n\t\t\t<designerrediretcturl><![CDATA[null]]></designerrediretcturl>\n\t\t</designershared>\n\t\t<emotionpageshared>\n\t\t\t<tid>0</tid>\n\t\t\t<title>null</title>\n\t\t\t<desc>null</desc>\n\t\t\t<iconUrl><![CDATA[null]]></iconUrl>\n\t\t\t<secondUrl />\n\t\t\t<pageType>0</pageType>\n\t\t\t<setKey>null</setKey>\n\t\t</emotionpageshared>\n\t\t<webviewshared>\n\t\t\t<shareUrlOriginal />\n\t\t\t<shareUrlOpen />\n\t\t\t<jsAppId />\n\t\t\t<publisherId />\n\t\t\t<publisherReqId />\n\t\t</webviewshared>\n\t\t<template_id />\n\t\t<md5 />\n\t\t<websearch />\n\t\t<weappinfo>\n\t\t\t<username />\n\t\t\t<appid />\n\t\t\t<appservicetype>0</appservicetype>\n\t\t\t<secflagforsinglepagemode>0</secflagforsinglepagemode>\n\t\t\t<videopageinfo>\n\t\t\t\t<thumbwidth>0</thumbwidth>\n\t\t\t\t<thumbheight>0</thumbheight>\n\t\t\t\t<fromopensdk>0</fromopensdk>\n\t\t\t</videopageinfo>\n\t\t</weappinfo>\n\t\t<statextstr />\n\t\t<musicShareItem>\n\t\t\t<musicDuration>0</musicDuration>\n\t\t</musicShareItem>\n\t\t<finderLiveProductShare>\n\t\t\t<finderLiveID><![CDATA[]]></finderLiveID>\n\t\t\t<finderUsername><![CDATA[]]></finderUsername>\n\t\t\t<finderObjectID><![CDATA[]]></finderObjectID>\n\t\t\t<finderNonceID><![CDATA[]]></finderNonceID>\n\t\t\t<liveStatus><![CDATA[]]></liveStatus>\n\t\t\t<appId><![CDATA[]]></appId>\n\t\t\t<pagePath><![CDATA[]]></pagePath>\n\t\t\t<productId><![CDATA[]]></productId>\n\t\t\t<coverUrl><![CDATA[]]></coverUrl>\n\t\t\t<productTitle><![CDATA[]]></productTitle>\n\t\t\t<marketPrice><![CDATA[0]]></marketPrice>\n\t\t\t<sellingPrice><![CDATA[0]]></sellingPrice>\n\t\t\t<platformHeadImg><![CDATA[]]></platformHeadImg>\n\t\t\t<platformName><![CDATA[]]></platformName>\n\t\t\t<shopWindowId><![CDATA[]]></shopWindowId>\n\t\t\t<flashSalePrice><![CDATA[0]]></flashSalePrice>\n\t\t\t<flashSaleEndTime><![CDATA[0]]></flashSaleEndTime>\n\t\t\t<ecSource><![CDATA[]]></ecSource>\n\t\t\t<sellingPriceWording><![CDATA[]]></sellingPriceWording>\n\t\t\t<platformIconURL><![CDATA[]]></platformIconURL>\n\t\t\t<firstProductTagURL><![CDATA[]]></firstProductTagURL>\n\t\t\t<firstProductTagAspectRatioString><![CDATA[0.0]]></firstProductTagAspectRatioString>\n\t\t\t<secondProductTagURL><![CDATA[]]></secondProductTagURL>\n\t\t\t<secondProductTagAspectRatioString><![CDATA[0.0]]></secondProductTagAspectRatioString>\n\t\t\t<firstGuaranteeWording><![CDATA[]]></firstGuaranteeWording>\n\t\t\t<secondGuaranteeWording><![CDATA[]]></secondGuaranteeWording>\n\t\t\t<thirdGuaranteeWording><![CDATA[]]></thirdGuaranteeWording>\n\t\t\t<isPriceBeginShow>false</isPriceBeginShow>\n\t\t\t<lastGMsgID><![CDATA[]]></lastGMsgID>\n\t\t\t<promoterKey><![CDATA[]]></promoterKey>\n\t\t\t<discountWording><![CDATA[]]></discountWording>\n\t\t\t<priceSuffixDescription><![CDATA[]]></priceSuffixDescription>\n\t\t\t<showBoxItemStringList />\n\t\t</finderLiveProductShare>\n\t\t<finderOrder>\n\t\t\t<appID><![CDATA[]]></appID>\n\t\t\t<orderID><![CDATA[]]></orderID>\n\t\t\t<path><![CDATA[]]></path>\n\t\t\t<priceWording><![CDATA[]]></priceWording>\n\t\t\t<stateWording><![CDATA[]]></stateWording>\n\t\t\t<productImageURL><![CDATA[]]></productImageURL>\n\t\t\t<products><![CDATA[]]></products>\n\t\t\t<productsCount><![CDATA[0]]></productsCount>\n\t\t</finderOrder>\n\t\t<finderShopWindowShare>\n\t\t\t<finderUsername><![CDATA[]]></finderUsername>\n\t\t\t<avatar><![CDATA[]]></avatar>\n\t\t\t<nickname><![CDATA[]]></nickname>\n\t\t\t<commodityInStockCount><![CDATA[]]></commodityInStockCount>\n\t\t\t<appId><![CDATA[]]></appId>\n\t\t\t<path><![CDATA[]]></path>\n\t\t\t<appUsername><![CDATA[]]></appUsername>\n\t\t\t<query><![CDATA[]]></query>\n\t\t\t<liteAppId><![CDATA[]]></liteAppId>\n\t\t\t<liteAppPath><![CDATA[]]></liteAppPath>\n\t\t\t<liteAppQuery><![CDATA[]]></liteAppQuery>\n\t\t\t<platformTagURL><![CDATA[]]></platformTagURL>\n\t\t\t<saleWording><![CDATA[]]></saleWording>\n\t\t\t<lastGMsgID><![CDATA[]]></lastGMsgID>\n\t\t\t<profileTypeWording><![CDATA[]]></profileTypeWording>\n\t\t\t<reputationInfo>\n\t\t\t\t<hasReputationInfo>0</hasReputationInfo>\n\t\t\t\t<reputationScore>0</reputationScore>\n\t\t\t\t<reputationWording />\n\t\t\t\t<reputationTextColor />\n\t\t\t\t<reputationLevelWording />\n\t\t\t\t<reputationBackgroundColor />\n\t\t\t</reputationInfo>\n\t\t\t<productImageURLList />\n\t\t</finderShopWindowShare>\n\t\t<findernamecard>\n\t\t\t<username />\n\t\t\t<avatar><![CDATA[]]></avatar>\n\t\t\t<nickname />\n\t\t\t<auth_job />\n\t\t\t<auth_icon>0</auth_icon>\n\t\t\t<auth_icon_url />\n\t\t\t<ecSource><![CDATA[]]></ecSource>\n\t\t\t<lastGMsgID><![CDATA[]]></lastGMsgID>\n\t\t</findernamecard>\n\t\t<finderGuarantee>\n\t\t\t<scene><![CDATA[0]]></scene>\n\t\t</finderGuarantee>\n\t\t<directshare>0</directshare>\n\t\t<gamecenter>\n\t\t\t<namecard>\n\t\t\t\t<iconUrl />\n\t\t\t\t<name />\n\t\t\t\t<desc />\n\t\t\t\t<tail />\n\t\t\t\t<jumpUrl />\n\t\t\t</namecard>\n\t\t</gamecenter>\n\t\t<patMsg>\n\t\t\t<chatUser />\n\t\t\t<records>\n\t\t\t\t<recordNum>0</recordNum>\n\t\t\t</records>\n\t\t</patMsg>\n\t\t<secretmsg>\n\t\t\t<issecretmsg>0</issecretmsg>\n\t\t</secretmsg>\n\t\t<referfromscene>0</referfromscene>\n\t\t<gameshare>\n\t\t\t<liteappext>\n\t\t\t\t<liteappbizdata />\n\t\t\t\t<priority>0</priority>\n\t\t\t</liteappext>\n\t\t\t<appbrandext>\n\t\t\t\t<litegameinfo />\n\t\t\t\t<priority>-1</priority>\n\t\t\t</appbrandext>\n\t\t\t<gameshareid />\n\t\t\t<sharedata />\n\t\t\t<isvideo>0</isvideo>\n\t\t\t<duration>0</duration>\n\t\t\t<isexposed>0</isexposed>\n\t\t\t<readtext />\n\t\t</gameshare>\n\t\t<mpsharetrace>\n\t\t\t<hasfinderelement>0</hasfinderelement>\n\t\t\t<lastgmsgid />\n\t\t</mpsharetrace>\n\t\t<wxgamecard>\n\t\t\t<framesetname />\n\t\t\t<mbcarddata />\n\t\t\t<minpkgversion />\n\t\t\t<mbcardheight>0</mbcardheight>\n\t\t\t<isoldversion>0</isoldversion>\n\t\t</wxgamecard>\n\t\t<liteapp>\n\t\t\t<id>null</id>\n\t\t\t<path />\n\t\t\t<query />\n\t\t</liteapp>\n\t</appmsg>\n\t<fromusername>xiaomaochong</fromusername>\n\t<scene>0</scene>\n\t<appinfo>\n\t\t<version>1</version>\n\t\t<appname />\n\t</appinfo>\n\t<commenturl />\n</msg>\n'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1753418042, 'MsgSource': '<msgsource>\n\t<alnode>\n\t\t<fr>2</fr>\n\t</alnode>\n\t<sec_msg_node>\n\t\t<uuid>409abb479277b2274c1181f7f9bd4d7f_</uuid>\n\t\t<risk-file-flag />\n\t\t<risk-file-md5-list />\n\t</sec_msg_node>\n\t<silence>0</silence>\n\t<membercount>63</membercount>\n\t<signature>N0_V1_4dPa2X5d|v1_gxmZ95kM</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'PushContent': '你收到了一条消息', 'NewMsgId': 6664685709548099809, 'MsgSeq': 871398627}
2025-07-25 12:33:41 | DEBUG | 从群聊消息中提取发送者: xiaomaochong
2025-07-25 12:33:41 | DEBUG | XML消息完整内容:
<?xml version="1.0"?>
<msg>
	<appmsg appid="" sdkver="0">
		<title>初三语文考，作文半命题题目：____ 初三。大家都写的“拼搏初三”、“热血初三”什么的，我和他们不一样，我定的是“大年初三”。——佚名</title>
		<des />
		<username />
		<action>view</action>
		<type>53</type>
		<showtype>0</showtype>
		<content />
		<url />
		<lowurl />
		<forwardflag>0</forwardflag>
		<dataurl />
		<lowdataurl />
		<contentattr>0</contentattr>
		<streamvideo>
			<streamvideourl />
			<streamvideototaltime>0</streamvideototaltime>
			<streamvideotitle />
			<streamvideowording />
			<streamvideoweburl />
			<streamvideothumburl />
			<streamvideoaduxinfo />
			<streamvideopublishid />
		</streamvideo>
		<canvasPageItem>
			<canvasPageXml><![CDATA[]]></canvasPageXml>
		</canvasPageItem>
		<appattach>
			<totallen>0</totallen>
			<attachid />
			<cdnattachurl />
			<emoticonmd5></emoticonmd5>
			<aeskey></aeskey>
			<fileext />
			<islargefilemsg>0</islargefilemsg>
		</appattach>
		<extinfo>
			<solitaire_info><![CDATA[]]></solitaire_info>
		</extinfo>
		<androidsource>0</androidsource>
		<thumburl />
		<mediatagname />
		<messageaction><![CDATA[]]></messageaction>
		<messageext><![CDATA[]]></messageext>
		<emoticongift>
			<packageflag>0</packageflag>
			<packageid />
		</emoticongift>
		<emoticonshared>
			<packageflag>0</packageflag>
			<packageid />
		</emoticonshared>
		<designershared>
			<designeruin>0</designeruin>
			<designername>null</designername>
			<designerrediretcturl><![CDATA[null]]></designerrediretcturl>
		</designershared>
		<emotionpageshared>
			<tid>0</tid>
			<title>null</title>
			<desc>null</desc>
			<iconUrl><![CDATA[null]]></iconUrl>
			<secondUrl />
			<pageType>0</pageType>
			<setKey>null</setKey>
		</emotionpageshared>
		<webviewshared>
			<shareUrlOriginal />
			<shareUrlOpen />
			<jsAppId />
			<publisherId />
			<publisherReqId />
		</webviewshared>
		<template_id />
		<md5 />
		<websearch />
		<weappinfo>
			<username />
			<appid />
			<appservicetype>0</appservicetype>
			<secflagforsinglepagemode>0</secflagforsinglepagemode>
			<videopageinfo>
				<thumbwidth>0</thumbwidth>
				<thumbheight>0</thumbheight>
				<fromopensdk>0</fromopensdk>
			</videopageinfo>
		</weappinfo>
		<statextstr />
		<musicShareItem>
			<musicDuration>0</musicDuration>
		</musicShareItem>
		<finderLiveProductShare>
			<finderLiveID><![CDATA[]]></finderLiveID>
			<finderUsername><![CDATA[]]></finderUsername>
			<finderObjectID><![CDATA[]]></finderObjectID>
			<finderNonceID><![CDATA[]]></finderNonceID>
			<liveStatus><![CDATA[]]></liveStatus>
			<appId><![CDATA[]]></appId>
			<pagePath><![CDATA[]]></pagePath>
			<productId><![CDATA[]]></productId>
			<coverUrl><![CDATA[]]></coverUrl>
			<productTitle><![CDATA[]]></productTitle>
			<marketPrice><![CDATA[0]]></marketPrice>
			<sellingPrice><![CDATA[0]]></sellingPrice>
			<platformHeadImg><![CDATA[]]></platformHeadImg>
			<platformName><![CDATA[]]></platformName>
			<shopWindowId><![CDATA[]]></shopWindowId>
			<flashSalePrice><![CDATA[0]]></flashSalePrice>
			<flashSaleEndTime><![CDATA[0]]></flashSaleEndTime>
			<ecSource><![CDATA[]]></ecSource>
			<sellingPriceWording><![CDATA[]]></sellingPriceWording>
			<platformIconURL><![CDATA[]]></platformIconURL>
			<firstProductTagURL><![CDATA[]]></firstProductTagURL>
			<firstProductTagAspectRatioString><![CDATA[0.0]]></firstProductTagAspectRatioString>
			<secondProductTagURL><![CDATA[]]></secondProductTagURL>
			<secondProductTagAspectRatioString><![CDATA[0.0]]></secondProductTagAspectRatioString>
			<firstGuaranteeWording><![CDATA[]]></firstGuaranteeWording>
			<secondGuaranteeWording><![CDATA[]]></secondGuaranteeWording>
			<thirdGuaranteeWording><![CDATA[]]></thirdGuaranteeWording>
			<isPriceBeginShow>false</isPriceBeginShow>
			<lastGMsgID><![CDATA[]]></lastGMsgID>
			<promoterKey><![CDATA[]]></promoterKey>
			<discountWording><![CDATA[]]></discountWording>
			<priceSuffixDescription><![CDATA[]]></priceSuffixDescription>
			<showBoxItemStringList />
		</finderLiveProductShare>
		<finderOrder>
			<appID><![CDATA[]]></appID>
			<orderID><![CDATA[]]></orderID>
			<path><![CDATA[]]></path>
			<priceWording><![CDATA[]]></priceWording>
			<stateWording><![CDATA[]]></stateWording>
			<productImageURL><![CDATA[]]></productImageURL>
			<products><![CDATA[]]></products>
			<productsCount><![CDATA[0]]></productsCount>
		</finderOrder>
		<finderShopWindowShare>
			<finderUsername><![CDATA[]]></finderUsername>
			<avatar><![CDATA[]]></avatar>
			<nickname><![CDATA[]]></nickname>
			<commodityInStockCount><![CDATA[]]></commodityInStockCount>
			<appId><![CDATA[]]></appId>
			<path><![CDATA[]]></path>
			<appUsername><![CDATA[]]></appUsername>
			<query><![CDATA[]]></query>
			<liteAppId><![CDATA[]]></liteAppId>
			<liteAppPath><![CDATA[]]></liteAppPath>
			<liteAppQuery><![CDATA[]]></liteAppQuery>
			<platformTagURL><![CDATA[]]></platformTagURL>
			<saleWording><![CDATA[]]></saleWording>
			<lastGMsgID><![CDATA[]]></lastGMsgID>
			<profileTypeWording><![CDATA[]]></profileTypeWording>
			<reputationInfo>
				<hasReputationInfo>0</hasReputationInfo>
				<reputationScore>0</reputationScore>
				<reputationWording />
				<reputationTextColor />
				<reputationLevelWording />
				<reputationBackgroundColor />
			</reputationInfo>
			<productImageURLList />
		</finderShopWindowShare>
		<findernamecard>
			<username />
			<avatar><![CDATA[]]></avatar>
			<nickname />
			<auth_job />
			<auth_icon>0</auth_icon>
			<auth_icon_url />
			<ecSource><![CDATA[]]></ecSource>
			<lastGMsgID><![CDATA[]]></lastGMsgID>
		</findernamecard>
		<finderGuarantee>
			<scene><![CDATA[0]]></scene>
		</finderGuarantee>
		<directshare>0</directshare>
		<gamecenter>
			<namecard>
				<iconUrl />
				<name />
				<desc />
				<tail />
				<jumpUrl />
			</namecard>
		</gamecenter>
		<patMsg>
			<chatUser />
			<records>
				<recordNum>0</recordNum>
			</records>
		</patMsg>
		<secretmsg>
			<issecretmsg>0</issecretmsg>
		</secretmsg>
		<referfromscene>0</referfromscene>
		<gameshare>
			<liteappext>
				<liteappbizdata />
				<priority>0</priority>
			</liteappext>
			<appbrandext>
				<litegameinfo />
				<priority>-1</priority>
			</appbrandext>
			<gameshareid />
			<sharedata />
			<isvideo>0</isvideo>
			<duration>0</duration>
			<isexposed>0</isexposed>
			<readtext />
		</gameshare>
		<mpsharetrace>
			<hasfinderelement>0</hasfinderelement>
			<lastgmsgid />
		</mpsharetrace>
		<wxgamecard>
			<framesetname />
			<mbcarddata />
			<minpkgversion />
			<mbcardheight>0</mbcardheight>
			<isoldversion>0</isoldversion>
		</wxgamecard>
		<liteapp>
			<id>null</id>
			<path />
			<query />
		</liteapp>
	</appmsg>
	<fromusername>xiaomaochong</fromusername>
	<scene>0</scene>
	<appinfo>
		<version>1</version>
		<appname />
	</appinfo>
	<commenturl />
</msg>

2025-07-25 12:33:41 | DEBUG | XML消息类型: 53
2025-07-25 12:33:41 | DEBUG | XML消息标题: 初三语文考，作文半命题题目：____ 初三。大家都写的“拼搏初三”、“热血初三”什么的，我和他们不一样，我定的是“大年初三”。——佚名
2025-07-25 12:33:41 | DEBUG | XML消息描述: None
2025-07-25 12:33:41 | DEBUG | 附件信息 totallen: 0
2025-07-25 12:33:41 | DEBUG | 附件信息 islargefilemsg: 0
2025-07-25 12:33:41 | INFO | 未知的XML消息类型: 53
2025-07-25 12:33:41 | INFO | 消息标题: 初三语文考，作文半命题题目：____ 初三。大家都写的“拼搏初三”、“热血初三”什么的，我和他们不一样，我定的是“大年初三”。——佚名
2025-07-25 12:33:41 | INFO | 消息描述: None
2025-07-25 12:33:41 | INFO | 消息URL: N/A
2025-07-25 12:33:41 | INFO | 完整XML内容:
<?xml version="1.0"?>
<msg>
	<appmsg appid="" sdkver="0">
		<title>初三语文考，作文半命题题目：____ 初三。大家都写的“拼搏初三”、“热血初三”什么的，我和他们不一样，我定的是“大年初三”。——佚名</title>
		<des />
		<username />
		<action>view</action>
		<type>53</type>
		<showtype>0</showtype>
		<content />
		<url />
		<lowurl />
		<forwardflag>0</forwardflag>
		<dataurl />
		<lowdataurl />
		<contentattr>0</contentattr>
		<streamvideo>
			<streamvideourl />
			<streamvideototaltime>0</streamvideototaltime>
			<streamvideotitle />
			<streamvideowording />
			<streamvideoweburl />
			<streamvideothumburl />
			<streamvideoaduxinfo />
			<streamvideopublishid />
		</streamvideo>
		<canvasPageItem>
			<canvasPageXml><![CDATA[]]></canvasPageXml>
		</canvasPageItem>
		<appattach>
			<totallen>0</totallen>
			<attachid />
			<cdnattachurl />
			<emoticonmd5></emoticonmd5>
			<aeskey></aeskey>
			<fileext />
			<islargefilemsg>0</islargefilemsg>
		</appattach>
		<extinfo>
			<solitaire_info><![CDATA[]]></solitaire_info>
		</extinfo>
		<androidsource>0</androidsource>
		<thumburl />
		<mediatagname />
		<messageaction><![CDATA[]]></messageaction>
		<messageext><![CDATA[]]></messageext>
		<emoticongift>
			<packageflag>0</packageflag>
			<packageid />
		</emoticongift>
		<emoticonshared>
			<packageflag>0</packageflag>
			<packageid />
		</emoticonshared>
		<designershared>
			<designeruin>0</designeruin>
			<designername>null</designername>
			<designerrediretcturl><![CDATA[null]]></designerrediretcturl>
		</designershared>
		<emotionpageshared>
			<tid>0</tid>
			<title>null</title>
			<desc>null</desc>
			<iconUrl><![CDATA[null]]></iconUrl>
			<secondUrl />
			<pageType>0</pageType>
			<setKey>null</setKey>
		</emotionpageshared>
		<webviewshared>
			<shareUrlOriginal />
			<shareUrlOpen />
			<jsAppId />
			<publisherId />
			<publisherReqId />
		</webviewshared>
		<template_id />
		<md5 />
		<websearch />
		<weappinfo>
			<username />
			<appid />
			<appservicetype>0</appservicetype>
			<secflagforsinglepagemode>0</secflagforsinglepagemode>
			<videopageinfo>
				<thumbwidth>0</thumbwidth>
				<thumbheight>0</thumbheight>
				<fromopensdk>0</fromopensdk>
			</videopageinfo>
		</weappinfo>
		<statextstr />
		<musicShareItem>
			<musicDuration>0</musicDuration>
		</musicShareItem>
		<finderLiveProductShare>
			<finderLiveID><![CDATA[]]></finderLiveID>
			<finderUsername><![CDATA[]]></finderUsername>
			<finderObjectID><![CDATA[]]></finderObjectID>
			<finderNonceID><![CDATA[]]></finderNonceID>
			<liveStatus><![CDATA[]]></liveStatus>
			<appId><![CDATA[]]></appId>
			<pagePath><![CDATA[]]></pagePath>
			<productId><![CDATA[]]></productId>
			<coverUrl><![CDATA[]]></coverUrl>
			<productTitle><![CDATA[]]></productTitle>
			<marketPrice><![CDATA[0]]></marketPrice>
			<sellingPrice><![CDATA[0]]></sellingPrice>
			<platformHeadImg><![CDATA[]]></platformHeadImg>
			<platformName><![CDATA[]]></platformName>
			<shopWindowId><![CDATA[]]></shopWindowId>
			<flashSalePrice><![CDATA[0]]></flashSalePrice>
			<flashSaleEndTime><![CDATA[0]]></flashSaleEndTime>
			<ecSource><![CDATA[]]></ecSource>
			<sellingPriceWording><![CDATA[]]></sellingPriceWording>
			<platformIconURL><![CDATA[]]></platformIconURL>
			<firstProductTagURL><![CDATA[]]></firstProductTagURL>
			<firstProductTagAspectRatioString><![CDATA[0.0]]></firstProductTagAspectRatioString>
			<secondProductTagURL><![CDATA[]]></secondProductTagURL>
			<secondProductTagAspectRatioString><![CDATA[0.0]]></secondProductTagAspectRatioString>
			<firstGuaranteeWording><![CDATA[]]></firstGuaranteeWording>
			<secondGuaranteeWording><![CDATA[]]></secondGuaranteeWording>
			<thirdGuaranteeWording><![CDATA[]]></thirdGuaranteeWording>
			<isPriceBeginShow>false</isPriceBeginShow>
			<lastGMsgID><![CDATA[]]></lastGMsgID>
			<promoterKey><![CDATA[]]></promoterKey>
			<discountWording><![CDATA[]]></discountWording>
			<priceSuffixDescription><![CDATA[]]></priceSuffixDescription>
			<showBoxItemStringList />
		</finderLiveProductShare>
		<finderOrder>
			<appID><![CDATA[]]></appID>
			<orderID><![CDATA[]]></orderID>
			<path><![CDATA[]]></path>
			<priceWording><![CDATA[]]></priceWording>
			<stateWording><![CDATA[]]></stateWording>
			<productImageURL><![CDATA[]]></productImageURL>
			<products><![CDATA[]]></products>
			<productsCount><![CDATA[0]]></productsCount>
		</finderOrder>
		<finderShopWindowShare>
			<finderUsername><![CDATA[]]></finderUsername>
			<avatar><![CDATA[]]></avatar>
			<nickname><![CDATA[]]></nickname>
			<commodityInStockCount><![CDATA[]]></commodityInStockCount>
			<appId><![CDATA[]]></appId>
			<path><![CDATA[]]></path>
			<appUsername><![CDATA[]]></appUsername>
			<query><![CDATA[]]></query>
			<liteAppId><![CDATA[]]></liteAppId>
			<liteAppPath><![CDATA[]]></liteAppPath>
			<liteAppQuery><![CDATA[]]></liteAppQuery>
			<platformTagURL><![CDATA[]]></platformTagURL>
			<saleWording><![CDATA[]]></saleWording>
			<lastGMsgID><![CDATA[]]></lastGMsgID>
			<profileTypeWording><![CDATA[]]></profileTypeWording>
			<reputationInfo>
				<hasReputationInfo>0</hasReputationInfo>
				<reputationScore>0</reputationScore>
				<reputationWording />
				<reputationTextColor />
				<reputationLevelWording />
				<reputationBackgroundColor />
			</reputationInfo>
			<productImageURLList />
		</finderShopWindowShare>
		<findernamecard>
			<username />
			<avatar><![CDATA[]]></avatar>
			<nickname />
			<auth_job />
			<auth_icon>0</auth_icon>
			<auth_icon_url />
			<ecSource><![CDATA[]]></ecSource>
			<lastGMsgID><![CDATA[]]></lastGMsgID>
		</findernamecard>
		<finderGuarantee>
			<scene><![CDATA[0]]></scene>
		</finderGuarantee>
		<directshare>0</directshare>
		<gamecenter>
			<namecard>
				<iconUrl />
				<name />
				<desc />
				<tail />
				<jumpUrl />
			</namecard>
		</gamecenter>
		<patMsg>
			<chatUser />
			<records>
				<recordNum>0</recordNum>
			</records>
		</patMsg>
		<secretmsg>
			<issecretmsg>0</issecretmsg>
		</secretmsg>
		<referfromscene>0</referfromscene>
		<gameshare>
			<liteappext>
				<liteappbizdata />
				<priority>0</priority>
			</liteappext>
			<appbrandext>
				<litegameinfo />
				<priority>-1</priority>
			</appbrandext>
			<gameshareid />
			<sharedata />
			<isvideo>0</isvideo>
			<duration>0</duration>
			<isexposed>0</isexposed>
			<readtext />
		</gameshare>
		<mpsharetrace>
			<hasfinderelement>0</hasfinderelement>
			<lastgmsgid />
		</mpsharetrace>
		<wxgamecard>
			<framesetname />
			<mbcarddata />
			<minpkgversion />
			<mbcardheight>0</mbcardheight>
			<isoldversion>0</isoldversion>
		</wxgamecard>
		<liteapp>
			<id>null</id>
			<path />
			<query />
		</liteapp>
	</appmsg>
	<fromusername>xiaomaochong</fromusername>
	<scene>0</scene>
	<appinfo>
		<version>1</version>
		<appname />
	</appinfo>
	<commenturl />
</msg>

2025-07-25 12:33:42 | DEBUG | 收到消息: {'MsgId': 1153617953, 'FromUserName': {'string': '48097389945@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 1, 'Content': {'string': 'wxid_8l9ymg1mafud12:\n有艺人经纪在吗？求推荐一位女明星，年轻漂亮玩得开就行，身材一定要好，肤白貌美胸大腿长那种，长发尤佳，混血最好，对酒量有一定要求，演技倒无所谓，我拿来当手机屏保。——佚名'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1753418043, 'MsgSource': '<msgsource>\n\t<silence>0</silence>\n\t<membercount>63</membercount>\n\t<signature>N0_V1_M3bXjGZI|v1_QF5oiHK4</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'PushContent': '不吃香菜\ue110 : 有艺人经纪在吗？求推荐一位女明星，年轻漂亮玩得开就行，身材一...', 'NewMsgId': 7299721316974501452, 'MsgSeq': 871398628}
2025-07-25 12:33:42 | INFO | 收到文本消息: 消息ID:1153617953 来自:48097389945@chatroom 发送人:wxid_8l9ymg1mafud12 @:[] 内容:有艺人经纪在吗？求推荐一位女明星，年轻漂亮玩得开就行，身材一定要好，肤白貌美胸大腿长那种，长发尤佳，混血最好，对酒量有一定要求，演技倒无所谓，我拿来当手机屏保。——佚名
2025-07-25 12:33:42 | DEBUG | 处理消息内容: '有艺人经纪在吗？求推荐一位女明星，年轻漂亮玩得开就行，身材一定要好，肤白貌美胸大腿长那种，长发尤佳，混血最好，对酒量有一定要求，演技倒无所谓，我拿来当手机屏保。——佚名'
2025-07-25 12:33:42 | DEBUG | 消息内容 '有艺人经纪在吗？求推荐一位女明星，年轻漂亮玩得开就行，身材一定要好，肤白貌美胸大腿长那种，长发尤佳，混血最好，对酒量有一定要求，演技倒无所谓，我拿来当手机屏保。——佚名' 不匹配任何命令，忽略
2025-07-25 12:33:44 | DEBUG | 收到消息: {'MsgId': 975221698, 'FromUserName': {'string': '48097389945@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 1, 'Content': {'string': 'wxid_lneb7n23o4lg12:\n今天在女友宿舍，正巧碰到宿管大妈了。我很有礼貌的说了句，“6个女的我1个男的不会有事。”宿管大妈居然来了句：“废话，我是怕你有事”。'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1753418043, 'MsgSource': '<msgsource>\n\t<pua>1</pua>\n\t<silence>0</silence>\n\t<membercount>63</membercount>\n\t<signature>N0_V1_VjrZk6i+|v1_1bxt0Iki</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'PushContent': 'ᡣ : 今天在女友宿舍，正巧碰到宿管大妈了。我很有礼貌的说了句，“6...', 'NewMsgId': 588154495607959403, 'MsgSeq': 871398629}
2025-07-25 12:33:44 | INFO | 收到文本消息: 消息ID:975221698 来自:48097389945@chatroom 发送人:wxid_lneb7n23o4lg12 @:[] 内容:今天在女友宿舍，正巧碰到宿管大妈了。我很有礼貌的说了句，“6个女的我1个男的不会有事。”宿管大妈居然来了句：“废话，我是怕你有事”。
2025-07-25 12:33:44 | DEBUG | 处理消息内容: '今天在女友宿舍，正巧碰到宿管大妈了。我很有礼貌的说了句，“6个女的我1个男的不会有事。”宿管大妈居然来了句：“废话，我是怕你有事”。'
2025-07-25 12:33:44 | DEBUG | 消息内容 '今天在女友宿舍，正巧碰到宿管大妈了。我很有礼貌的说了句，“6个女的我1个男的不会有事。”宿管大妈居然来了句：“废话，我是怕你有事”。' 不匹配任何命令，忽略
2025-07-25 12:33:46 | DEBUG | 收到消息: {'MsgId': 1117874023, 'FromUserName': {'string': '48097389945@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 1, 'Content': {'string': 'wxid_mwil20w1e1j422:\n放生什么了'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1753418043, 'MsgSource': '<msgsource>\n\t<pua>1</pua>\n\t<silence>0</silence>\n\t<membercount>63</membercount>\n\t<signature>N0_V1_gQAguoYE|v1_W9XRNFnR</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'PushContent': '37.2℃ : 放生什么了', 'NewMsgId': 4052011389307274988, 'MsgSeq': 871398630}
2025-07-25 12:33:46 | INFO | 收到文本消息: 消息ID:1117874023 来自:48097389945@chatroom 发送人:wxid_mwil20w1e1j422 @:[] 内容:放生什么了
2025-07-25 12:33:46 | DEBUG | 处理消息内容: '放生什么了'
2025-07-25 12:33:46 | DEBUG | 消息内容 '放生什么了' 不匹配任何命令，忽略
2025-07-25 12:33:48 | DEBUG | 收到消息: {'MsgId': 436445515, 'FromUserName': {'string': '48097389945@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 1, 'Content': {'string': 'rongcheng6630606:\n天大的笑话😂'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1753418044, 'MsgSource': '<msgsource>\n\t<pua>1</pua>\n\t<eggIncluded>1</eggIncluded>\n\t<silence>0</silence>\n\t<membercount>63</membercount>\n\t<signature>N0_V1_XZIl4xv6|v1_Q5FQM+xg</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'PushContent': '× : 天大的笑话\ue412', 'NewMsgId': 1479131706463250770, 'MsgSeq': 871398631}
2025-07-25 12:33:48 | INFO | 收到文本消息: 消息ID:436445515 来自:48097389945@chatroom 发送人:rongcheng6630606 @:[] 内容:天大的笑话😂
2025-07-25 12:33:48 | DEBUG | 处理消息内容: '天大的笑话😂'
2025-07-25 12:33:48 | DEBUG | 消息内容 '天大的笑话😂' 不匹配任何命令，忽略
2025-07-25 12:33:50 | DEBUG | 收到消息: {'MsgId': 710934267, 'FromUserName': {'string': '48097389945@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 1, 'Content': {'string': 'wxid_mwil20w1e1j422:\n@小爱\u2005'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1753418048, 'MsgSource': '<msgsource>\n\t<atuserlist><![CDATA[xiaomaochong]]></atuserlist>\n\t<pua>1</pua>\n\t<silence>0</silence>\n\t<membercount>63</membercount>\n\t<signature>N0_V1_z8bEjvXz|v1_Q0okPeUv</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'PushContent': '37.2℃ : @小爱\u2005', 'NewMsgId': 7936946883315752332, 'MsgSeq': 871398632}
2025-07-25 12:33:50 | INFO | 收到文本消息: 消息ID:710934267 来自:48097389945@chatroom 发送人:wxid_mwil20w1e1j422 @:['xiaomaochong'] 内容:@小爱 
2025-07-25 12:33:50 | DEBUG | 处理消息内容: '@小爱'
2025-07-25 12:33:50 | DEBUG | 消息内容 '@小爱' 不匹配任何命令，忽略
2025-07-25 12:33:52 | DEBUG | 收到消息: {'MsgId': 954959671, 'FromUserName': {'string': '48097389945@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 1, 'Content': {'string': 'wxid_srknxij3jka022:\n从小时候开始我就觉得自己是个做大事的人，只是后来老是听人说大事不好了，就放弃了。——佚名'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1753418052, 'MsgSource': '<msgsource>\n\t<pua>1</pua>\n\t<eggIncluded>1</eggIncluded>\n\t<silence>0</silence>\n\t<membercount>63</membercount>\n\t<signature>N0_V1_pJtNRQlA|v1_t8LGG4uS</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'PushContent': '她 : 从小时候开始我就觉得自己是个做大事的人，只是后来老是听人说大...', 'NewMsgId': 5568932902549134740, 'MsgSeq': 871398633}
2025-07-25 12:33:52 | INFO | 收到文本消息: 消息ID:954959671 来自:48097389945@chatroom 发送人:wxid_srknxij3jka022 @:[] 内容:从小时候开始我就觉得自己是个做大事的人，只是后来老是听人说大事不好了，就放弃了。——佚名
2025-07-25 12:33:52 | DEBUG | 处理消息内容: '从小时候开始我就觉得自己是个做大事的人，只是后来老是听人说大事不好了，就放弃了。——佚名'
2025-07-25 12:33:52 | DEBUG | 消息内容 '从小时候开始我就觉得自己是个做大事的人，只是后来老是听人说大事不好了，就放弃了。——佚名' 不匹配任何命令，忽略
2025-07-25 12:33:54 | DEBUG | 收到消息: {'MsgId': 451893941, 'FromUserName': {'string': '48097389945@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 1, 'Content': {'string': 'wxid_mwil20w1e1j422:\n给我说说'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1753418053, 'MsgSource': '<msgsource>\n\t<pua>1</pua>\n\t<silence>0</silence>\n\t<membercount>63</membercount>\n\t<signature>N0_V1_BQOpf9Np|v1_MvUr/mXa</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'PushContent': '37.2℃ : 给我说说', 'NewMsgId': 6656129780048847893, 'MsgSeq': 871398634}
2025-07-25 12:33:54 | INFO | 收到文本消息: 消息ID:451893941 来自:48097389945@chatroom 发送人:wxid_mwil20w1e1j422 @:[] 内容:给我说说
2025-07-25 12:33:55 | DEBUG | 处理消息内容: '给我说说'
2025-07-25 12:33:55 | DEBUG | 消息内容 '给我说说' 不匹配任何命令，忽略
2025-07-25 12:33:56 | DEBUG | 收到消息: {'MsgId': 119043698, 'FromUserName': {'string': '48097389945@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 1, 'Content': {'string': 'wxid_84mmq4cu7ita22:\n所以删了，别联系了'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1753418053, 'MsgSource': '<msgsource>\n\t<sec_msg_node>\n\t\t<alnode>\n\t\t\t<fr>1</fr>\n\t\t</alnode>\n\t</sec_msg_node>\n\t<pua>1</pua>\n\t<silence>0</silence>\n\t<membercount>63</membercount>\n\t<signature>N0_V1_y9Qwx3AF|v1_1sUn40Ue</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'PushContent': '喵小叶 : 所以删了，别联系了', 'NewMsgId': 3391264758342474958, 'MsgSeq': 871398635}
2025-07-25 12:33:56 | INFO | 收到文本消息: 消息ID:119043698 来自:48097389945@chatroom 发送人:wxid_84mmq4cu7ita22 @:[] 内容:所以删了，别联系了
2025-07-25 12:33:57 | DEBUG | 处理消息内容: '所以删了，别联系了'
2025-07-25 12:33:57 | DEBUG | 消息内容 '所以删了，别联系了' 不匹配任何命令，忽略
2025-07-25 12:33:58 | DEBUG | 收到消息: {'MsgId': 166489830, 'FromUserName': {'string': '48097389945@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 1, 'Content': {'string': 'rongcheng6630606:\n搞得以为你是我女友似的'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1753418053, 'MsgSource': '<msgsource>\n\t<pua>1</pua>\n\t<eggIncluded>1</eggIncluded>\n\t<silence>0</silence>\n\t<membercount>63</membercount>\n\t<signature>N0_V1_U6MN2ez7|v1_QLow65a+</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'PushContent': '× : 搞得以为你是我女友似的', 'NewMsgId': 5614764592405688484, 'MsgSeq': 871398636}
2025-07-25 12:33:58 | INFO | 收到文本消息: 消息ID:166489830 来自:48097389945@chatroom 发送人:rongcheng6630606 @:[] 内容:搞得以为你是我女友似的
2025-07-25 12:33:59 | DEBUG | 处理消息内容: '搞得以为你是我女友似的'
2025-07-25 12:33:59 | DEBUG | 消息内容 '搞得以为你是我女友似的' 不匹配任何命令，忽略
2025-07-25 12:34:00 | DEBUG | 收到消息: {'MsgId': 1501118391, 'FromUserName': {'string': '48097389945@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 34, 'Content': {'string': 'xiaomaochong:\n<msg><voicemsg endflag="1" cancelflag="0" forwardflag="0" voiceformat="4" voicelength="10000" length="30636" bufid="0" aeskey="636776636e79796f657467727a6f6570" voiceurl="3052020100044b304902010002049363814102033d14ba0204b139949d02046883094a042466643566386334622d613065332d343664372d613631612d33303831666564353166356502040528000f02010004001dc74187" voicemd5="5da943f208feb50c218879a2f3517e5f" clientmsgid="494cd5716bb4ce7e67f76aaff6aa3e5648097389945@chatroom_47915_1753418057" fromusername="xiaomaochong" /></msg>'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1753418058, 'MsgSource': '<msgsource>\n\t<silence>0</silence>\n\t<membercount>63</membercount>\n\t<signature>N0_V1_XIU2sl+2|v1_V6bxqfWz</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'PushContent': '小爱在群聊中发了一段语音', 'NewMsgId': 5536363441915742544, 'MsgSeq': 871398637}
2025-07-25 12:34:00 | INFO | 收到语音消息: 消息ID:1501118391 来自:48097389945@chatroom 发送人:xiaomaochong XML:
<msg><voicemsg endflag="1" cancelflag="0" forwardflag="0" voiceformat="4" voicelength="10000" length="30636" bufid="0" aeskey="636776636e79796f657467727a6f6570" voiceurl="3052020100044b304902010002049363814102033d14ba0204b139949d02046883094a042466643566386334622d613065332d343664372d613631612d33303831666564353166356502040528000f02010004001dc74187" voicemd5="5da943f208feb50c218879a2f3517e5f" clientmsgid="494cd5716bb4ce7e67f76aaff6aa3e5648097389945@chatroom_47915_1753418057" fromusername="xiaomaochong" /></msg>
2025-07-25 12:34:01 | DEBUG | 收到消息: {'MsgId': 99821977, 'FromUserName': {'string': '48097389945@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 1, 'Content': {'string': 'wxid_84mmq4cu7ita22:\n也别和我在一个群'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1753418062, 'MsgSource': '<msgsource>\n\t<sec_msg_node>\n\t\t<alnode>\n\t\t\t<fr>1</fr>\n\t\t</alnode>\n\t</sec_msg_node>\n\t<pua>1</pua>\n\t<silence>0</silence>\n\t<membercount>63</membercount>\n\t<signature>N0_V1_jjPLwEBi|v1_Tfu3fQli</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'PushContent': '喵小叶 : 也别和我在一个群', 'NewMsgId': 759307224091803024, 'MsgSeq': 871398638}
2025-07-25 12:34:01 | INFO | 收到文本消息: 消息ID:99821977 来自:48097389945@chatroom 发送人:wxid_84mmq4cu7ita22 @:[] 内容:也别和我在一个群
2025-07-25 12:34:01 | DEBUG | 处理消息内容: '也别和我在一个群'
2025-07-25 12:34:01 | DEBUG | 消息内容 '也别和我在一个群' 不匹配任何命令，忽略
2025-07-25 12:34:03 | DEBUG | 收到消息: {'MsgId': 1409850899, 'FromUserName': {'string': '48097389945@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 1, 'Content': {'string': 'wxid_l9koi6kli78i22:\n@37.2℃\u2005'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1753418064, 'MsgSource': '<msgsource>\n\t<atuserlist><![CDATA[wxid_mwil20w1e1j422]]></atuserlist>\n\t<pua>1</pua>\n\t<silence>0</silence>\n\t<membercount>63</membercount>\n\t<signature>N0_V1_HMDehKO8|v1_y7KcCE7x</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'PushContent': '十五 : @37.2℃\u2005', 'NewMsgId': 821801313285025142, 'MsgSeq': 871398639}
2025-07-25 12:34:03 | INFO | 收到文本消息: 消息ID:1409850899 来自:48097389945@chatroom 发送人:wxid_l9koi6kli78i22 @:['wxid_mwil20w1e1j422'] 内容:@37.2℃ 
2025-07-25 12:34:04 | DEBUG | 处理消息内容: '@37.2℃'
2025-07-25 12:34:04 | DEBUG | 消息内容 '@37.2℃' 不匹配任何命令，忽略
2025-07-25 12:34:09 | DEBUG | 收到消息: {'MsgId': 1863601160, 'FromUserName': {'string': '48097389945@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 49, 'Content': {'string': 'rongcheng6630606:\n<?xml version="1.0"?>\n<msg>\n\t<appmsg appid="" sdkver="0">\n\t\t<title>你管不着</title>\n\t\t<des />\n\t\t<username />\n\t\t<action>view</action>\n\t\t<type>57</type>\n\t\t<showtype>0</showtype>\n\t\t<content />\n\t\t<url />\n\t\t<lowurl />\n\t\t<forwardflag>0</forwardflag>\n\t\t<dataurl />\n\t\t<lowdataurl />\n\t\t<contentattr>0</contentattr>\n\t\t<streamvideo>\n\t\t\t<streamvideourl />\n\t\t\t<streamvideototaltime>0</streamvideototaltime>\n\t\t\t<streamvideotitle />\n\t\t\t<streamvideowording />\n\t\t\t<streamvideoweburl />\n\t\t\t<streamvideothumburl />\n\t\t\t<streamvideoaduxinfo />\n\t\t\t<streamvideopublishid />\n\t\t</streamvideo>\n\t\t<canvasPageItem>\n\t\t\t<canvasPageXml><![CDATA[]]></canvasPageXml>\n\t\t</canvasPageItem>\n\t\t<refermsg>\n\t\t\t<type>1</type>\n\t\t\t<svrid>759307224091803024</svrid>\n\t\t\t<fromusr>48097389945@chatroom</fromusr>\n\t\t\t<chatusr>wxid_84mmq4cu7ita22</chatusr>\n\t\t\t<displayname>喵小叶</displayname>\n\t\t\t<msgsource>&lt;msgsource&gt;\n\t&lt;sec_msg_node&gt;\n\t\t&lt;alnode&gt;\n\t\t\t&lt;fr&gt;1&lt;/fr&gt;\n\t\t&lt;/alnode&gt;\n\t&lt;/sec_msg_node&gt;\n\t&lt;pua&gt;1&lt;/pua&gt;\n\t&lt;silence&gt;1&lt;/silence&gt;\n\t&lt;membercount&gt;63&lt;/membercount&gt;\n\t&lt;signature&gt;N0_V1_uvGjeHFI|v1_Zy013r34&lt;/signature&gt;\n\t&lt;tmp_node&gt;\n\t\t&lt;publisher-id&gt;&lt;/publisher-id&gt;\n\t&lt;/tmp_node&gt;\n&lt;/msgsource&gt;\n</msgsource>\n\t\t\t<content>\n也别和我在一个群</content>\n\t\t\t<strid />\n\t\t\t<createtime>1753418062</createtime>\n\t\t</refermsg>\n\t\t<appattach>\n\t\t\t<totallen>0</totallen>\n\t\t\t<attachid />\n\t\t\t<cdnattachurl />\n\t\t\t<emoticonmd5></emoticonmd5>\n\t\t\t<aeskey></aeskey>\n\t\t\t<fileext />\n\t\t\t<islargefilemsg>0</islargefilemsg>\n\t\t</appattach>\n\t\t<extinfo />\n\t\t<androidsource>0</androidsource>\n\t\t<thumburl />\n\t\t<mediatagname />\n\t\t<messageaction><![CDATA[]]></messageaction>\n\t\t<messageext><![CDATA[]]></messageext>\n\t\t<emoticongift>\n\t\t\t<packageflag>0</packageflag>\n\t\t\t<packageid />\n\t\t</emoticongift>\n\t\t<emoticonshared>\n\t\t\t<packageflag>0</packageflag>\n\t\t\t<packageid />\n\t\t</emoticonshared>\n\t\t<designershared>\n\t\t\t<designeruin>0</designeruin>\n\t\t\t<designername>null</designername>\n\t\t\t<designerrediretcturl><![CDATA[null]]></designerrediretcturl>\n\t\t</designershared>\n\t\t<emotionpageshared>\n\t\t\t<tid>0</tid>\n\t\t\t<title>null</title>\n\t\t\t<desc>null</desc>\n\t\t\t<iconUrl><![CDATA[null]]></iconUrl>\n\t\t\t<secondUrl>null</secondUrl>\n\t\t\t<pageType>0</pageType>\n\t\t\t<setKey>null</setKey>\n\t\t</emotionpageshared>\n\t\t<webviewshared>\n\t\t\t<shareUrlOriginal />\n\t\t\t<shareUrlOpen />\n\t\t\t<jsAppId />\n\t\t\t<publisherId />\n\t\t\t<publisherReqId />\n\t\t</webviewshared>\n\t\t<template_id />\n\t\t<md5 />\n\t\t<websearch>\n\t\t\t<rec_category>0</rec_category>\n\t\t\t<channelId>0</channelId>\n\t\t</websearch>\n\t\t<weappinfo>\n\t\t\t<username />\n\t\t\t<appid />\n\t\t\t<appservicetype>0</appservicetype>\n\t\t\t<secflagforsinglepagemode>0</secflagforsinglepagemode>\n\t\t\t<videopageinfo>\n\t\t\t\t<thumbwidth>0</thumbwidth>\n\t\t\t\t<thumbheight>0</thumbheight>\n\t\t\t\t<fromopensdk>0</fromopensdk>\n\t\t\t</videopageinfo>\n\t\t</weappinfo>\n\t\t<statextstr />\n\t\t<musicShareItem>\n\t\t\t<musicDuration>0</musicDuration>\n\t\t</musicShareItem>\n\t\t<finderLiveProductShare>\n\t\t\t<finderLiveID><![CDATA[]]></finderLiveID>\n\t\t\t<finderUsername><![CDATA[]]></finderUsername>\n\t\t\t<finderObjectID><![CDATA[]]></finderObjectID>\n\t\t\t<finderNonceID><![CDATA[]]></finderNonceID>\n\t\t\t<liveStatus><![CDATA[]]></liveStatus>\n\t\t\t<appId><![CDATA[]]></appId>\n\t\t\t<pagePath><![CDATA[]]></pagePath>\n\t\t\t<productId><![CDATA[]]></productId>\n\t\t\t<coverUrl><![CDATA[]]></coverUrl>\n\t\t\t<productTitle><![CDATA[]]></productTitle>\n\t\t\t<marketPrice><![CDATA[0]]></marketPrice>\n\t\t\t<sellingPrice><![CDATA[0]]></sellingPrice>\n\t\t\t<platformHeadImg><![CDATA[]]></platformHeadImg>\n\t\t\t<platformName><![CDATA[]]></platformName>\n\t\t\t<shopWindowId><![CDATA[]]></shopWindowId>\n\t\t\t<flashSalePrice><![CDATA[0]]></flashSalePrice>\n\t\t\t<flashSaleEndTime><![CDATA[0]]></flashSaleEndTime>\n\t\t\t<ecSource><![CDATA[]]></ecSource>\n\t\t\t<sellingPriceWording><![CDATA[]]></sellingPriceWording>\n\t\t\t<platformIconURL><![CDATA[]]></platformIconURL>\n\t\t\t<firstProductTagURL><![CDATA[]]></firstProductTagURL>\n\t\t\t<firstProductTagAspectRatioString><![CDATA[0.0]]></firstProductTagAspectRatioString>\n\t\t\t<secondProductTagURL><![CDATA[]]></secondProductTagURL>\n\t\t\t<secondProductTagAspectRatioString><![CDATA[0.0]]></secondProductTagAspectRatioString>\n\t\t\t<firstGuaranteeWording><![CDATA[]]></firstGuaranteeWording>\n\t\t\t<secondGuaranteeWording><![CDATA[]]></secondGuaranteeWording>\n\t\t\t<thirdGuaranteeWording><![CDATA[]]></thirdGuaranteeWording>\n\t\t\t<isPriceBeginShow>false</isPriceBeginShow>\n\t\t\t<lastGMsgID><![CDATA[]]></lastGMsgID>\n\t\t\t<promoterKey><![CDATA[]]></promoterKey>\n\t\t\t<discountWording><![CDATA[]]></discountWording>\n\t\t\t<priceSuffixDescription><![CDATA[]]></priceSuffixDescription>\n\t\t\t<productCardKey><![CDATA[]]></productCardKey>\n\t\t\t<isWxShop><![CDATA[]]></isWxShop>\n\t\t\t<brandIconUrl><![CDATA[]]></brandIconUrl>\n\t\t\t<rIconUrl><![CDATA[]]></rIconUrl>\n\t\t\t<rIconUrlDarkMode><![CDATA[]]></rIconUrlDarkMode>\n\t\t\t<topShopIconUrl><![CDATA[]]></topShopIconUrl>\n\t\t\t<topShopIconUrlDarkMode><![CDATA[]]></topShopIconUrlDarkMode>\n\t\t\t<simplifyTopShopIconUrl><![CDATA[]]></simplifyTopShopIconUrl>\n\t\t\t<simplifyTopShopIconUrlDarkmode><![CDATA[]]></simplifyTopShopIconUrlDarkmode>\n\t\t\t<topShopIconWidth><![CDATA[0]]></topShopIconWidth>\n\t\t\t<topShopIconHeight><![CDATA[0]]></topShopIconHeight>\n\t\t\t<simplifyTopShopIconWidth><![CDATA[0]]></simplifyTopShopIconWidth>\n\t\t\t<simplifyTopShopIconHeight><![CDATA[0]]></simplifyTopShopIconHeight>\n\t\t\t<showBoxItemStringList />\n\t\t</finderLiveProductShare>\n\t\t<finderOrder>\n\t\t\t<appID><![CDATA[]]></appID>\n\t\t\t<orderID><![CDATA[]]></orderID>\n\t\t\t<path><![CDATA[]]></path>\n\t\t\t<priceWording><![CDATA[]]></priceWording>\n\t\t\t<stateWording><![CDATA[]]></stateWording>\n\t\t\t<productImageURL><![CDATA[]]></productImageURL>\n\t\t\t<products><![CDATA[]]></products>\n\t\t\t<productsCount><![CDATA[0]]></productsCount>\n\t\t\t<orderType><![CDATA[0]]></orderType>\n\t\t\t<newPriceWording><![CDATA[]]></newPriceWording>\n\t\t\t<newStateWording><![CDATA[]]></newStateWording>\n\t\t\t<useNewWording><![CDATA[0]]></useNewWording>\n\t\t</finderOrder>\n\t\t<finderShopWindowShare>\n\t\t\t<finderUsername><![CDATA[]]></finderUsername>\n\t\t\t<avatar><![CDATA[]]></avatar>\n\t\t\t<nickname><![CDATA[]]></nickname>\n\t\t\t<commodityInStockCount><![CDATA[]]></commodityInStockCount>\n\t\t\t<appId><![CDATA[]]></appId>\n\t\t\t<path><![CDATA[]]></path>\n\t\t\t<appUsername><![CDATA[]]></appUsername>\n\t\t\t<query><![CDATA[]]></query>\n\t\t\t<liteAppId><![CDATA[]]></liteAppId>\n\t\t\t<liteAppPath><![CDATA[]]></liteAppPath>\n\t\t\t<liteAppQuery><![CDATA[]]></liteAppQuery>\n\t\t\t<platformTagURL><![CDATA[]]></platformTagURL>\n\t\t\t<saleWording><![CDATA[]]></saleWording>\n\t\t\t<lastGMsgID><![CDATA[]]></lastGMsgID>\n\t\t\t<profileTypeWording><![CDATA[]]></profileTypeWording>\n\t\t\t<saleWordingExtra><![CDATA[]]></saleWordingExtra>\n\t\t\t<isWxShop><![CDATA[]]></isWxShop>\n\t\t\t<platformIconUrl><![CDATA[]]></platformIconUrl>\n\t\t\t<brandIconUrl><![CDATA[]]></brandIconUrl>\n\t\t\t<description><![CDATA[]]></description>\n\t\t\t<backgroundUrl><![CDATA[]]></backgroundUrl>\n\t\t\t<darkModePlatformIconUrl><![CDATA[]]></darkModePlatformIconUrl>\n\t\t\t<rIconUrl><![CDATA[]]></rIconUrl>\n\t\t\t<rIconUrlDarkMode><![CDATA[]]></rIconUrlDarkMode>\n\t\t\t<rWords><![CDATA[]]></rWords>\n\t\t\t<topShopIconUrl><![CDATA[]]></topShopIconUrl>\n\t\t\t<topShopIconUrlDarkMode><![CDATA[]]></topShopIconUrlDarkMode>\n\t\t\t<simplifyTopShopIconUrl><![CDATA[]]></simplifyTopShopIconUrl>\n\t\t\t<simplifyTopShopIconUrlDarkmode><![CDATA[]]></simplifyTopShopIconUrlDarkmode>\n\t\t\t<topShopIconWidth><![CDATA[0]]></topShopIconWidth>\n\t\t\t<topShopIconHeight><![CDATA[0]]></topShopIconHeight>\n\t\t\t<simplifyTopShopIconWidth><![CDATA[0]]></simplifyTopShopIconWidth>\n\t\t\t<simplifyTopShopIconHeight><![CDATA[0]]></simplifyTopShopIconHeight>\n\t\t\t<reputationInfo>\n\t\t\t\t<hasReputationInfo>0</hasReputationInfo>\n\t\t\t\t<reputationScore>0</reputationScore>\n\t\t\t\t<reputationWording />\n\t\t\t\t<reputationTextColor />\n\t\t\t\t<reputationLevelWording />\n\t\t\t\t<reputationBackgroundColor />\n\t\t\t</reputationInfo>\n\t\t\t<productImageURLList />\n\t\t</finderShopWindowShare>\n\t\t<findernamecard>\n\t\t\t<username />\n\t\t\t<avatar><![CDATA[]]></avatar>\n\t\t\t<nickname />\n\t\t\t<auth_job />\n\t\t\t<auth_icon>0</auth_icon>\n\t\t\t<auth_icon_url />\n\t\t\t<ecSource><![CDATA[]]></ecSource>\n\t\t\t<lastGMsgID><![CDATA[]]></lastGMsgID>\n\t\t</findernamecard>\n\t\t<finderGuarantee>\n\t\t\t<scene><![CDATA[0]]></scene>\n\t\t</finderGuarantee>\n\t\t<directshare>0</directshare>\n\t\t<gamecenter>\n\t\t\t<namecard>\n\t\t\t\t<iconUrl />\n\t\t\t\t<name />\n\t\t\t\t<desc />\n\t\t\t\t<tail />\n\t\t\t\t<jumpUrl />\n\t\t\t\t<liteappId />\n\t\t\t\t<liteappPath />\n\t\t\t\t<liteappQuery />\n\t\t\t\t<liteappMinVersion />\n\t\t\t</namecard>\n\t\t</gamecenter>\n\t\t<patMsg>\n\t\t\t<chatUser />\n\t\t\t<records>\n\t\t\t\t<recordNum>0</recordNum>\n\t\t\t</records>\n\t\t</patMsg>\n\t\t<secretmsg>\n\t\t\t<issecretmsg>0</issecretmsg>\n\t\t</secretmsg>\n\t\t<referfromscene>0</referfromscene>\n\t\t<gameshare>\n\t\t\t<liteappext>\n\t\t\t\t<liteappbizdata />\n\t\t\t\t<priority>0</priority>\n\t\t\t</liteappext>\n\t\t\t<appbrandext>\n\t\t\t\t<litegameinfo />\n\t\t\t\t<priority>-1</priority>\n\t\t\t</appbrandext>\n\t\t\t<gameshareid />\n\t\t\t<sharedata />\n\t\t\t<isvideo>0</isvideo>\n\t\t\t<duration>-1</duration>\n\t\t\t<isexposed>0</isexposed>\n\t\t\t<readtext />\n\t\t</gameshare>\n\t\t<tingChatRoomItem>\n\t\t\t<type>0</type>\n\t\t\t<categoryItem>null</categoryItem>\n\t\t\t<categoryId />\n\t\t</tingChatRoomItem>\n\t\t<mpsharetrace>\n\t\t\t<hasfinderelement>0</hasfinderelement>\n\t\t\t<lastgmsgid />\n\t\t</mpsharetrace>\n\t\t<wxgamecard>\n\t\t\t<framesetname />\n\t\t\t<mbcarddata />\n\t\t\t<minpkgversion />\n\t\t\t<clientextinfo />\n\t\t\t<mbcardheight>0</mbcardheight>\n\t\t\t<isoldversion>0</isoldversion>\n\t\t</wxgamecard>\n\t\t<ecskfcard>\n\t\t\t<framesetname />\n\t\t\t<mbcarddata />\n\t\t\t<minupdateunixtimestamp>0</minupdateunixtimestamp>\n\t\t\t<needheader>false</needheader>\n\t\t\t<summary />\n\t\t</ecskfcard>\n\t\t<liteapp>\n\t\t\t<id>null</id>\n\t\t\t<path />\n\t\t\t<query />\n\t\t\t<istransparent>0</istransparent>\n\t\t\t<hideicon>0</hideicon>\n\t\t\t<forbidforward>0</forbidforward>\n\t\t</liteapp>\n\t\t<opensdk_share_is_modified>0</opensdk_share_is_modified>\n\t</appmsg>\n\t<fromusername>rongcheng6630606</fromusername>\n\t<scene>0</scene>\n\t<appinfo>\n\t\t<version>1</version>\n\t\t<appname />\n\t</appinfo>\n\t<commenturl />\n</msg>\n'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1753418071, 'MsgSource': '<msgsource>\n\t<alnode>\n\t\t<fr>4</fr>\n\t</alnode>\n\t<eggIncluded>1</eggIncluded>\n\t<sec_msg_node>\n\t\t<uuid>37345b4f806b062d5a8f1e7ec11b3538_</uuid>\n\t\t<risk-file-flag />\n\t\t<risk-file-md5-list />\n\t</sec_msg_node>\n\t<silence>0</silence>\n\t<membercount>63</membercount>\n\t<signature>N0_V1_hO+9uzoE|v1_GfrQS9qZ</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'PushContent': '× : 你管不着', 'NewMsgId': 6884419469901285367, 'MsgSeq': 871398640}
2025-07-25 12:34:09 | DEBUG | 从群聊消息中提取发送者: rongcheng6630606
2025-07-25 12:34:09 | DEBUG | 使用已解析的XML处理引用消息
2025-07-25 12:34:09 | INFO | 收到引用消息: 消息ID:1863601160 来自:48097389945@chatroom 发送人:rongcheng6630606 内容:你管不着 引用类型:1
2025-07-25 12:34:10 | INFO | [DouBaoImageToImage] 收到引用消息: 你管不着
2025-07-25 12:34:10 | INFO | [TimerTask] 收到引用消息调试信息:
2025-07-25 12:34:10 | INFO |   - 消息内容: 你管不着
2025-07-25 12:34:10 | INFO |   - 群组ID: 48097389945@chatroom
2025-07-25 12:34:10 | INFO |   - 发送人: rongcheng6630606
2025-07-25 12:34:10 | INFO |   - 引用信息: {'MsgType': 1, 'Content': '\n也别和我在一个群', 'Msgid': '759307224091803024', 'NewMsgId': '759307224091803024', 'ToWxid': 'wxid_4usgcju5ey9q29', 'FromWxid': '48097389945@chatroom', 'Nickname': '喵小叶', 'MsgSource': '<msgsource>\n\t<sec_msg_node>\n\t\t<alnode>\n\t\t\t<fr>1</fr>\n\t\t</alnode>\n\t</sec_msg_node>\n\t<pua>1</pua>\n\t<silence>1</silence>\n\t<membercount>63</membercount>\n\t<signature>N0_V1_uvGjeHFI|v1_Zy013r34</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'Createtime': '1753418062', 'SenderWxid': 'rongcheng6630606'}
2025-07-25 12:34:10 | INFO |   - 引用消息ID: 
2025-07-25 12:34:10 | INFO |   - 引用消息类型: 
2025-07-25 12:34:10 | INFO |   - 引用消息内容: 
也别和我在一个群
2025-07-25 12:34:10 | INFO |   - 引用消息发送人: rongcheng6630606
2025-07-25 12:34:12 | DEBUG | 收到消息: {'MsgId': 1571071271, 'FromUserName': {'string': '48097389945@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 1, 'Content': {'string': 'rongcheng6630606:\n我乐意'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1753418074, 'MsgSource': '<msgsource>\n\t<pua>1</pua>\n\t<eggIncluded>1</eggIncluded>\n\t<silence>0</silence>\n\t<membercount>63</membercount>\n\t<signature>N0_V1_5hzDhsmX|v1_Z5rDF2Od</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'PushContent': '× : 我乐意', 'NewMsgId': 5830362701757747044, 'MsgSeq': 871398641}
2025-07-25 12:34:12 | INFO | 收到文本消息: 消息ID:1571071271 来自:48097389945@chatroom 发送人:rongcheng6630606 @:[] 内容:我乐意
2025-07-25 12:34:12 | DEBUG | 处理消息内容: '我乐意'
2025-07-25 12:34:12 | DEBUG | 消息内容 '我乐意' 不匹配任何命令，忽略
2025-07-25 12:34:18 | DEBUG | 收到消息: {'MsgId': 991359534, 'FromUserName': {'string': '48097389945@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 1, 'Content': {'string': 'wxid_l9koi6kli78i22:\n@37.2℃\u2005小宝贝'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1753418081, 'MsgSource': '<msgsource>\n\t<atuserlist><![CDATA[wxid_mwil20w1e1j422]]></atuserlist>\n\t<pua>1</pua>\n\t<silence>0</silence>\n\t<membercount>63</membercount>\n\t<signature>N0_V1_bmhWP4i3|v1_bZDK3ffe</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'PushContent': '十五 : @37.2℃\u2005小宝贝', 'NewMsgId': 526678001530484009, 'MsgSeq': 871398642}
2025-07-25 12:34:18 | INFO | 收到文本消息: 消息ID:991359534 来自:48097389945@chatroom 发送人:wxid_l9koi6kli78i22 @:['wxid_mwil20w1e1j422'] 内容:@37.2℃ 小宝贝
2025-07-25 12:34:19 | DEBUG | 处理消息内容: '@37.2℃ 小宝贝'
2025-07-25 12:34:19 | DEBUG | 消息内容 '@37.2℃ 小宝贝' 不匹配任何命令，忽略
2025-07-25 12:34:21 | DEBUG | 收到消息: {'MsgId': 1987755975, 'FromUserName': {'string': '48097389945@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 1, 'Content': {'string': 'rongcheng6630606:\n除非小爱把我踢了'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1753418084, 'MsgSource': '<msgsource>\n\t<pua>1</pua>\n\t<eggIncluded>1</eggIncluded>\n\t<silence>0</silence>\n\t<membercount>63</membercount>\n\t<signature>N0_V1_A0KR4KIg|v1_QOJg5/WP</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'PushContent': '× : 除非小爱把我踢了', 'NewMsgId': 3696928899701979753, 'MsgSeq': 871398643}
2025-07-25 12:34:21 | INFO | 收到文本消息: 消息ID:1987755975 来自:48097389945@chatroom 发送人:rongcheng6630606 @:[] 内容:除非小爱把我踢了
2025-07-25 12:34:22 | DEBUG | 处理消息内容: '除非小爱把我踢了'
2025-07-25 12:34:22 | DEBUG | 消息内容 '除非小爱把我踢了' 不匹配任何命令，忽略
2025-07-25 12:34:38 | DEBUG | 收到消息: {'MsgId': 1264165027, 'FromUserName': {'string': '48097389945@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 47, 'Content': {'string': 'wxid_ptkyw926tmjn22:\n<msg><emoji fromusername = "wxid_ptkyw926tmjn22" tousername = "48097389945@chatroom" type="2" idbuffer="media:0_0" md5="37130c8188cabacec30abff253808e30" len = "22783" productid="com.tencent.xin.emoticon.person.stiker_1517552722a1dc31e1c56a2caa" androidmd5="37130c8188cabacec30abff253808e30" androidlen="22783" s60v3md5 = "37130c8188cabacec30abff253808e30" s60v3len="22783" s60v5md5 = "37130c8188cabacec30abff253808e30" s60v5len="22783" cdnurl = "http://wxapp.tc.qq.com/275/20304/stodownload?m=37130c8188cabacec30abff253808e30&amp;filekey=30340201010420301e0202011304025348041037130c8188cabacec30abff253808e30020258ff040d00000004627466730000000132&amp;hy=SH&amp;storeid=26479cd3e00055914000000000000011300004f50534808b178e0b68078e80&amp;bizid=1023" designerid = "" thumburl = "http://wxapp.tc.qq.com/275/20304/stodownload?m=bee18dd651f958c192645f72ebf06ae9&amp;filekey=30340201010420301e02020113040253480410bee18dd651f958c192645f72ebf06ae902026b6a040d00000004627466730000000132&amp;hy=SH&amp;storeid=26479cd3e00070a64000000000000011300004f5053480786eb40b6ff54271&amp;bizid=1023" encrypturl = "http://wxapp.tc.qq.com/262/20304/stodownload?m=0b31129a99bf48557ae6f36e1c2fe32d&amp;filekey=30340201010420301e020201060402534804100b31129a99bf48557ae6f36e1c2fe32d02025900040d00000004627466730000000132&amp;hy=SH&amp;storeid=26309b4d6000bfedf000000000000010600004f50534829c65b40b70228586&amp;bizid=1023" aeskey= "12df21b9187ce3d9a881f631ba0f3426" externurl = "" externmd5 = "" width= "240" height= "240" tpurl= "" tpauthkey= "" attachedtext= "" attachedtextcolor= "" lensid= "" emojiattr= "" linkid= "" desc= "Cg8KBXpoX2NuEgbmi5Tmr5sKCQoFemhfdHcSAAoLCgdkZWZhdWx0EgA=" ></emoji>  </msg>'}, 'Status': 3, 'ImgStatus': 2, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1753418100, 'MsgSource': '<msgsource>\n\t<silence>0</silence>\n\t<membercount>63</membercount>\n\t<signature>N0_V1_AvZH/qK6|v1_AfstOftL</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'PushContent': 'متسول小主在群聊中发了一个表情', 'NewMsgId': 8108469626440495227, 'MsgSeq': 871398644}
2025-07-25 12:34:38 | INFO | 收到表情消息: 消息ID:1264165027 来自:48097389945@chatroom 发送人:wxid_ptkyw926tmjn22 MD5:37130c8188cabacec30abff253808e30 大小:22783
2025-07-25 12:34:38 | DEBUG | [DoubaoImageRecognition] 缓存表情消息: 8108469626440495227
2025-07-25 12:35:21 | DEBUG | 收到消息: {'MsgId': 972132155, 'FromUserName': {'string': '48097389945@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 1, 'Content': {'string': 'wxid_cwxauba42ki122:\n成天抱根山药，不扎人？'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1753418143, 'MsgSource': '<msgsource>\n\t<pua>1</pua>\n\t<silence>0</silence>\n\t<membercount>63</membercount>\n\t<signature>N0_V1_t2rxmOv+|v1_K0uVqXeO</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'PushContent': 'ن初心ᯤ⁶ᴳ คิดถึงن : 成天抱根山药，不扎人？', 'NewMsgId': 409494086194260385, 'MsgSeq': 871398645}
2025-07-25 12:35:21 | INFO | 收到文本消息: 消息ID:972132155 来自:48097389945@chatroom 发送人:wxid_cwxauba42ki122 @:[] 内容:成天抱根山药，不扎人？
2025-07-25 12:35:21 | DEBUG | 处理消息内容: '成天抱根山药，不扎人？'
2025-07-25 12:35:21 | DEBUG | 消息内容 '成天抱根山药，不扎人？' 不匹配任何命令，忽略
2025-07-25 12:35:54 | DEBUG | 收到消息: {'MsgId': 1081550193, 'FromUserName': {'string': '48006068290@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 43, 'Content': {'string': 'wxid_ubbh6q832tcs21:\n<?xml version="1.0"?>\n<msg>\n\t<videomsg aeskey="afccd9fb218a5c106fff2c96772fbfc0" cdnvideourl="3057020100044b30490201000204aac0a3dd02032f841102045591587d02046881d147042466653665326563332d336637612d346261612d393466342d3463343535393931613263650204051408040201000405004c543d00" cdnthumbaeskey="afccd9fb218a5c106fff2c96772fbfc0" cdnthumburl="3057020100044b30490201000204aac0a3dd02032f841102045591587d02046881d147042466653665326563332d336637612d346261612d393466342d3463343535393931613263650204051408040201000405004c543d00" length="3160413" playlength="15" cdnthumblength="8553" cdnthumbwidth="203" cdnthumbheight="360" fromusername="wxid_ubbh6q832tcs21" md5="5ec8c3ef42b4a1d8922163e1b92d0e9e" newmd5="73dd661669a5f2162046f3a5a31d2776" isplaceholder="0" rawmd5="" rawlength="0" cdnrawvideourl="" cdnrawvideoaeskey="" overwritenewmsgid="0" originsourcemd5="" isad="0" />\n</msg>\n'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1753418177, 'MsgSource': '<msgsource>\n\t<alnode>\n\t\t<fr>4</fr>\n\t</alnode>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n\t<sec_msg_node>\n\t\t<uuid>5127412bd4815641292c939615ad9b37_</uuid>\n\t\t<risk-file-flag />\n\t\t<risk-file-md5-list />\n\t</sec_msg_node>\n\t<silence>0</silence>\n\t<membercount>12</membercount>\n\t<signature>N0_V1_ooMLhpwf|v1_VhyHu+Dp</signature>\n</msgsource>\n', 'PushContent': '郭在群聊中发了一段视频', 'NewMsgId': 4062964969735981683, 'MsgSeq': 871398646}
2025-07-25 12:35:54 | INFO | 收到视频消息: 消息ID:1081550193 来自:48006068290@chatroom 发送人:wxid_ubbh6q832tcs21 XML:
<?xml version="1.0"?>
<msg>
	<videomsg aeskey="afccd9fb218a5c106fff2c96772fbfc0" cdnvideourl="3057020100044b30490201000204aac0a3dd02032f841102045591587d02046881d147042466653665326563332d336637612d346261612d393466342d3463343535393931613263650204051408040201000405004c543d00" cdnthumbaeskey="afccd9fb218a5c106fff2c96772fbfc0" cdnthumburl="3057020100044b30490201000204aac0a3dd02032f841102045591587d02046881d147042466653665326563332d336637612d346261612d393466342d3463343535393931613263650204051408040201000405004c543d00" length="3160413" playlength="15" cdnthumblength="8553" cdnthumbwidth="203" cdnthumbheight="360" fromusername="wxid_ubbh6q832tcs21" md5="5ec8c3ef42b4a1d8922163e1b92d0e9e" newmd5="73dd661669a5f2162046f3a5a31d2776" isplaceholder="0" rawmd5="" rawlength="0" cdnrawvideourl="" cdnrawvideoaeskey="" overwritenewmsgid="0" originsourcemd5="" isad="0" />
</msg>

2025-07-25 12:35:56 | DEBUG | 收到消息: {'MsgId': 74771903, 'FromUserName': {'string': '48097389945@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 47, 'Content': {'string': 'wxid_mwil20w1e1j422:\n<msg><emoji fromusername="wxid_mwil20w1e1j422" tousername="48097389945@chatroom" type="2" idbuffer="media:0_0" md5="2e584bb192dcd957a2f0577df00fef17" len="1872169" productid="" androidmd5="2e584bb192dcd957a2f0577df00fef17" androidlen="1872169" s60v3md5="2e584bb192dcd957a2f0577df00fef17" s60v3len="1872169" s60v5md5="2e584bb192dcd957a2f0577df00fef17" s60v5len="1872169" cdnurl="http://wxapp.tc.qq.com/262/20304/stodownload?m=2e584bb192dcd957a2f0577df00fef17&amp;filekey=30350201010421301f020201060402535a04102e584bb192dcd957a2f0577df00fef1702031c9129040d00000004627466730000000131&amp;hy=SZ&amp;storeid=32303231303632363232353731343030303634343130383463666339386263653539356630393030303030313036&amp;bizid=1023" designerid="" thumburl="" encrypturl="http://wxapp.tc.qq.com/262/20304/stodownload?m=b39480b6d702e15142a39a5fbab6d1a3&amp;filekey=30350201010421301f020201060402535a0410b39480b6d702e15142a39a5fbab6d1a302031c9130040d00000004627466730000000131&amp;hy=SZ&amp;storeid=32303231303632363232353731353030303939666636383463666339386230663539356630393030303030313036&amp;bizid=1023" aeskey="a56ae04b63e9cb0dbc9e7a76f247e645" externurl="http://wxapp.tc.qq.com/262/20304/stodownload?m=535e6d3675590228b03b25a1d91fe97a&amp;filekey=30350201010421301f020201060402535a0410535e6d3675590228b03b25a1d91fe97a020301a1e0040d00000004627466730000000131&amp;hy=SZ&amp;storeid=32303231303632363232353731363030303064323337383463666339386231393537353830393030303030313036&amp;bizid=1023" externmd5="d03120eb569c534445d460debd5a57d1" width="388" height="388" tpurl="" tpauthkey="" attachedtext="" attachedtextcolor="" lensid="" emojiattr="" linkid="" desc=""></emoji></msg>'}, 'Status': 3, 'ImgStatus': 2, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1753418179, 'MsgSource': '<msgsource>\n\t<silence>0</silence>\n\t<membercount>63</membercount>\n\t<signature>N0_V1_p6kfwojQ|v1_cYOFdpkt</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'PushContent': '37.2℃在群聊中发了一个表情', 'NewMsgId': 284333831881910238, 'MsgSeq': 871398647}
2025-07-25 12:35:56 | INFO | 收到表情消息: 消息ID:74771903 来自:48097389945@chatroom 发送人:wxid_mwil20w1e1j422 MD5:2e584bb192dcd957a2f0577df00fef17 大小:1872169
2025-07-25 12:35:57 | DEBUG | [DoubaoImageRecognition] 缓存表情消息: 284333831881910238
2025-07-25 12:35:57 | DEBUG | 收到消息: {'MsgId': 2041532488, 'FromUserName': {'string': '48097389945@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 1, 'Content': {'string': 'wxid_ptkyw926tmjn22:\n拔了。怎么扎人'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1753418179, 'MsgSource': '<msgsource>\n\t<pua>1</pua>\n\t<eggIncluded>1</eggIncluded>\n\t<silence>0</silence>\n\t<membercount>63</membercount>\n\t<signature>N0_V1_+2RB4L8R|v1_TzBctlrt</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'PushContent': 'متسول小主 : 拔了。怎么扎人', 'NewMsgId': 1260890890450524024, 'MsgSeq': 871398648}
2025-07-25 12:35:57 | INFO | 收到文本消息: 消息ID:2041532488 来自:48097389945@chatroom 发送人:wxid_ptkyw926tmjn22 @:[] 内容:拔了。怎么扎人
2025-07-25 12:35:57 | DEBUG | 处理消息内容: '拔了。怎么扎人'
2025-07-25 12:35:57 | DEBUG | 消息内容 '拔了。怎么扎人' 不匹配任何命令，忽略
2025-07-25 12:36:08 | DEBUG | 收到消息: {'MsgId': 1414498538, 'FromUserName': {'string': '48097389945@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 1, 'Content': {'string': 'wxid_cwxauba42ki122:\n[捂脸]'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1753418190, 'MsgSource': '<msgsource>\n\t<pua>1</pua>\n\t<silence>0</silence>\n\t<membercount>63</membercount>\n\t<signature>N0_V1_+/2s/o/8|v1_bZuhjdaz</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'PushContent': 'ن初心ᯤ⁶ᴳ คิดถึงن : [捂脸]', 'NewMsgId': 1629558990417728084, 'MsgSeq': 871398649}
2025-07-25 12:36:08 | INFO | 收到表情消息: 消息ID:1414498538 来自:48097389945@chatroom 发送人:wxid_cwxauba42ki122 @:[] 内容:[捂脸]
2025-07-25 12:36:08 | DEBUG | [DoubaoImageRecognition] 缓存表情消息: 1629558990417728084
2025-07-25 12:36:09 | DEBUG | 收到消息: {'MsgId': 916973104, 'FromUserName': {'string': '48097389945@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 1, 'Content': {'string': 'xiaomaochong:\n@×\u2005请'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1753418191, 'MsgSource': '<msgsource>\n\t<atuserlist><![CDATA[rongcheng6630606]]></atuserlist>\n\t<pua>1</pua>\n\t<silence>0</silence>\n\t<membercount>63</membercount>\n\t<signature>N0_V1_uMS4T3Pr|v1_btppTtsD</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'PushContent': '小爱 : @×\u2005请', 'NewMsgId': 1643338250879784274, 'MsgSeq': 871398650}
2025-07-25 12:36:09 | INFO | 收到文本消息: 消息ID:916973104 来自:48097389945@chatroom 发送人:xiaomaochong @:['rongcheng6630606'] 内容:@× 请
2025-07-25 12:36:10 | DEBUG | 处理消息内容: '@× 请'
2025-07-25 12:36:10 | DEBUG | 消息内容 '@× 请' 不匹配任何命令，忽略
2025-07-25 12:36:12 | DEBUG | 收到消息: {'MsgId': 394058225, 'FromUserName': {'string': '48097389945@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 49, 'Content': {'string': 'xiaomaochong:\n<?xml version="1.0"?>\n<msg>\n\t<appmsg appid="" sdkver="0">\n\t\t<title>已处理</title>\n\t\t<type>57</type>\n\t\t<appattach>\n\t\t\t<cdnthumbaeskey />\n\t\t\t<aeskey></aeskey>\n\t\t</appattach>\n\t\t<refermsg>\n\t\t\t<type>1</type>\n\t\t\t<svrid>1643338250879784274</svrid>\n\t\t\t<fromusr>xiaomaochong</fromusr>\n\t\t\t<chatusr />\n\t\t\t<displayname>小爱</displayname>\n\t\t\t<content>@×\u2005请</content>\n\t\t\t<msgsource>&lt;msgsource&gt;&lt;sequence_id&gt;844471756&lt;/sequence_id&gt;\n\t&lt;atuserlist&gt;&lt;![CDATA[rongcheng6630606]]&gt;&lt;/atuserlist&gt;\n\t&lt;pua&gt;1&lt;/pua&gt;\n\t&lt;signature&gt;N0_V1_nrCEvweI|v1_xxJHNq2I&lt;/signature&gt;\n\t&lt;tmp_node&gt;\n\t\t&lt;publisher-id&gt;&lt;/publisher-id&gt;\n\t&lt;/tmp_node&gt;\n&lt;/msgsource&gt;\n</msgsource>\n\t\t\t<createtime>1753418191</createtime>\n\t\t</refermsg>\n\t</appmsg>\n\t<fromusername>xiaomaochong</fromusername>\n\t<scene>0</scene>\n\t<appinfo>\n\t\t<version>1</version>\n\t\t<appname />\n\t</appinfo>\n\t<commenturl />\n</msg>\n'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1753418193, 'MsgSource': '<msgsource>\n\t<sec_msg_node>\n\t\t<uuid>8101310a4513135e61410406a0811fa6_</uuid>\n\t\t<risk-file-flag />\n\t\t<risk-file-md5-list />\n\t</sec_msg_node>\n\t<silence>0</silence>\n\t<membercount>63</membercount>\n\t<signature>N0_V1_+NotuSPy|v1_Xn7c+wRC</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'PushContent': '小爱 : 已处理', 'NewMsgId': 6759682033400979568, 'MsgSeq': 871398651}
2025-07-25 12:36:12 | DEBUG | 从群聊消息中提取发送者: xiaomaochong
2025-07-25 12:36:12 | DEBUG | 使用已解析的XML处理引用消息
2025-07-25 12:36:12 | INFO | 收到引用消息: 消息ID:394058225 来自:48097389945@chatroom 发送人:xiaomaochong 内容:已处理 引用类型:1
2025-07-25 12:36:12 | INFO | [DouBaoImageToImage] 收到引用消息: 已处理
2025-07-25 12:36:12 | INFO | [TimerTask] 收到引用消息调试信息:
2025-07-25 12:36:12 | INFO |   - 消息内容: 已处理
2025-07-25 12:36:12 | INFO |   - 群组ID: 48097389945@chatroom
2025-07-25 12:36:12 | INFO |   - 发送人: xiaomaochong
2025-07-25 12:36:12 | INFO |   - 引用信息: {'MsgType': 1, 'Content': '@×\u2005请', 'Msgid': '1643338250879784274', 'NewMsgId': '1643338250879784274', 'ToWxid': 'wxid_4usgcju5ey9q29', 'FromWxid': '48097389945@chatroom', 'Nickname': '小爱', 'MsgSource': '<msgsource><sequence_id>844471756</sequence_id>\n\t<atuserlist><![CDATA[rongcheng6630606]]></atuserlist>\n\t<pua>1</pua>\n\t<signature>N0_V1_nrCEvweI|v1_xxJHNq2I</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'Createtime': '1753418191', 'SenderWxid': 'xiaomaochong'}
2025-07-25 12:36:12 | INFO |   - 引用消息ID: 
2025-07-25 12:36:12 | INFO |   - 引用消息类型: 
2025-07-25 12:36:12 | INFO |   - 引用消息内容: @× 请
2025-07-25 12:36:12 | INFO |   - 引用消息发送人: xiaomaochong
2025-07-25 12:36:12 | DEBUG | 收到消息: {'MsgId': 1485697013, 'FromUserName': {'string': '48097389945@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 49, 'Content': {'string': 'wxid_bqljhmmoqlqm12:\n<?xml version="1.0"?>\n<msg>\n\t<appmsg appid="" sdkver="0">\n\t\t<title>已处理</title>\n\t\t<type>57</type>\n\t\t<appattach>\n\t\t\t<cdnthumbaeskey />\n\t\t\t<aeskey></aeskey>\n\t\t</appattach>\n\t\t<refermsg>\n\t\t\t<type>1</type>\n\t\t\t<svrid>1643338250879784274</svrid>\n\t\t\t<fromusr>48097389945@chatroom</fromusr>\n\t\t\t<chatusr>xiaomaochong</chatusr>\n\t\t\t<displayname>小爱</displayname>\n\t\t\t<content>@×\u2005请</content>\n\t\t\t<msgsource>&lt;msgsource&gt;&lt;sequence_id&gt;851405015&lt;/sequence_id&gt;\n\t&lt;atuserlist&gt;&lt;![CDATA[rongcheng6630606]]&gt;&lt;/atuserlist&gt;\n\t&lt;pua&gt;1&lt;/pua&gt;\n\t&lt;silence&gt;1&lt;/silence&gt;\n\t&lt;membercount&gt;63&lt;/membercount&gt;\n\t&lt;signature&gt;N0_V1_t23dz6GR|v1_of+4EqxA&lt;/signature&gt;\n\t&lt;tmp_node&gt;\n\t\t&lt;publisher-id&gt;&lt;/publisher-id&gt;\n\t&lt;/tmp_node&gt;\n&lt;/msgsource&gt;\n</msgsource>\n\t\t\t<createtime>1753418191</createtime>\n\t\t</refermsg>\n\t</appmsg>\n\t<fromusername>wxid_bqljhmmoqlqm12</fromusername>\n\t<scene>0</scene>\n\t<appinfo>\n\t\t<version>1</version>\n\t\t<appname />\n\t</appinfo>\n\t<commenturl />\n</msg>\n'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1753418193, 'MsgSource': '<msgsource>\n\t<sec_msg_node>\n\t\t<uuid>4ed185e51ce12fa1ca8b0ac1814c3771_</uuid>\n\t\t<risk-file-flag />\n\t\t<risk-file-md5-list />\n\t</sec_msg_node>\n\t<silence>0</silence>\n\t<membercount>63</membercount>\n\t<signature>N0_V1_yq3/gUCu|v1_fuodm/9Q</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'PushContent': '小爱同学 : 已处理', 'NewMsgId': 5966336975702764869, 'MsgSeq': 871398652}
2025-07-25 12:36:12 | DEBUG | 从群聊消息中提取发送者: wxid_bqljhmmoqlqm12
2025-07-25 12:36:12 | DEBUG | 使用已解析的XML处理引用消息
2025-07-25 12:36:12 | INFO | 收到引用消息: 消息ID:1485697013 来自:48097389945@chatroom 发送人:wxid_bqljhmmoqlqm12 内容:已处理 引用类型:1
2025-07-25 12:36:13 | INFO | [DouBaoImageToImage] 收到引用消息: 已处理
2025-07-25 12:36:13 | INFO | [TimerTask] 收到引用消息调试信息:
2025-07-25 12:36:13 | INFO |   - 消息内容: 已处理
2025-07-25 12:36:13 | INFO |   - 群组ID: 48097389945@chatroom
2025-07-25 12:36:13 | INFO |   - 发送人: wxid_bqljhmmoqlqm12
2025-07-25 12:36:13 | INFO |   - 引用信息: {'MsgType': 1, 'Content': '@×\u2005请', 'Msgid': '1643338250879784274', 'NewMsgId': '1643338250879784274', 'ToWxid': 'wxid_4usgcju5ey9q29', 'FromWxid': '48097389945@chatroom', 'Nickname': '小爱', 'MsgSource': '<msgsource><sequence_id>851405015</sequence_id>\n\t<atuserlist><![CDATA[rongcheng6630606]]></atuserlist>\n\t<pua>1</pua>\n\t<silence>1</silence>\n\t<membercount>63</membercount>\n\t<signature>N0_V1_t23dz6GR|v1_of+4EqxA</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'Createtime': '1753418191', 'SenderWxid': 'wxid_bqljhmmoqlqm12'}
2025-07-25 12:36:13 | INFO |   - 引用消息ID: 
2025-07-25 12:36:13 | INFO |   - 引用消息类型: 
2025-07-25 12:36:13 | INFO |   - 引用消息内容: @× 请
2025-07-25 12:36:13 | INFO |   - 引用消息发送人: wxid_bqljhmmoqlqm12
2025-07-25 12:36:13 | DEBUG | 收到消息: {'MsgId': 500344715, 'FromUserName': {'string': '48097389945@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 49, 'Content': {'string': 'wxid_nlmjroes6ot322:\n<?xml version="1.0"?>\n<msg>\n\t<appmsg appid="" sdkver="0">\n\t\t<title>已处理</title>\n\t\t<type>57</type>\n\t\t<appattach>\n\t\t\t<cdnthumbaeskey />\n\t\t\t<aeskey></aeskey>\n\t\t</appattach>\n\t\t<refermsg>\n\t\t\t<type>1</type>\n\t\t\t<svrid>1643338250879784274</svrid>\n\t\t\t<fromusr>48097389945@chatroom</fromusr>\n\t\t\t<chatusr>xiaomaochong</chatusr>\n\t\t\t<displayname>小爱</displayname>\n\t\t\t<content>@×\u2005请</content>\n\t\t\t<msgsource>&lt;msgsource&gt;&lt;sequence_id&gt;847582015&lt;/sequence_id&gt;\n\t&lt;atuserlist&gt;&lt;![CDATA[rongcheng6630606]]&gt;&lt;/atuserlist&gt;\n\t&lt;pua&gt;1&lt;/pua&gt;\n\t&lt;silence&gt;1&lt;/silence&gt;\n\t&lt;membercount&gt;63&lt;/membercount&gt;\n\t&lt;signature&gt;N0_V1_pelDQhte|v1_tI7cmSxL&lt;/signature&gt;\n\t&lt;tmp_node&gt;\n\t\t&lt;publisher-id&gt;&lt;/publisher-id&gt;\n\t&lt;/tmp_node&gt;\n&lt;/msgsource&gt;\n</msgsource>\n\t\t\t<createtime>1753418191</createtime>\n\t\t</refermsg>\n\t</appmsg>\n\t<fromusername>wxid_nlmjroes6ot322</fromusername>\n\t<scene>0</scene>\n\t<appinfo>\n\t\t<version>1</version>\n\t\t<appname />\n\t</appinfo>\n\t<commenturl />\n</msg>\n'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1753418194, 'MsgSource': '<msgsource>\n\t<sec_msg_node>\n\t\t<uuid>5224fe4148a07ba86d98b91afa312d93_</uuid>\n\t\t<risk-file-flag />\n\t\t<risk-file-md5-list />\n\t</sec_msg_node>\n\t<silence>0</silence>\n\t<membercount>63</membercount>\n\t<signature>N0_V1_8BKscjNh|v1_bqVa4S7u</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'PushContent': 'ㅤ : 已处理', 'NewMsgId': 4644046704322165768, 'MsgSeq': 871398653}
2025-07-25 12:36:13 | DEBUG | 从群聊消息中提取发送者: wxid_nlmjroes6ot322
2025-07-25 12:36:13 | DEBUG | 使用已解析的XML处理引用消息
2025-07-25 12:36:13 | INFO | 收到引用消息: 消息ID:500344715 来自:48097389945@chatroom 发送人:wxid_nlmjroes6ot322 内容:已处理 引用类型:1
2025-07-25 12:36:13 | INFO | [DouBaoImageToImage] 收到引用消息: 已处理
2025-07-25 12:36:13 | INFO | [TimerTask] 收到引用消息调试信息:
2025-07-25 12:36:13 | INFO |   - 消息内容: 已处理
2025-07-25 12:36:13 | INFO |   - 群组ID: 48097389945@chatroom
2025-07-25 12:36:13 | INFO |   - 发送人: wxid_nlmjroes6ot322
2025-07-25 12:36:13 | INFO |   - 引用信息: {'MsgType': 1, 'Content': '@×\u2005请', 'Msgid': '1643338250879784274', 'NewMsgId': '1643338250879784274', 'ToWxid': 'wxid_4usgcju5ey9q29', 'FromWxid': '48097389945@chatroom', 'Nickname': '小爱', 'MsgSource': '<msgsource><sequence_id>847582015</sequence_id>\n\t<atuserlist><![CDATA[rongcheng6630606]]></atuserlist>\n\t<pua>1</pua>\n\t<silence>1</silence>\n\t<membercount>63</membercount>\n\t<signature>N0_V1_pelDQhte|v1_tI7cmSxL</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'Createtime': '1753418191', 'SenderWxid': 'wxid_nlmjroes6ot322'}
2025-07-25 12:36:13 | INFO |   - 引用消息ID: 
2025-07-25 12:36:13 | INFO |   - 引用消息类型: 
2025-07-25 12:36:13 | INFO |   - 引用消息内容: @× 请
2025-07-25 12:36:13 | INFO |   - 引用消息发送人: wxid_nlmjroes6ot322
2025-07-25 12:36:14 | DEBUG | 收到消息: {'MsgId': 1281158629, 'FromUserName': {'string': '48097389945@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 47, 'Content': {'string': 'wxid_ptkyw926tmjn22:\n<msg><emoji fromusername = "wxid_ptkyw926tmjn22" tousername = "48097389945@chatroom" type="1" idbuffer="media:0_0" md5="504ddb44cc3e7473e188a6f29e9a39da" len = "3553771" productid="" androidmd5="504ddb44cc3e7473e188a6f29e9a39da" androidlen="3553771" s60v3md5 = "504ddb44cc3e7473e188a6f29e9a39da" s60v3len="3553771" s60v5md5 = "504ddb44cc3e7473e188a6f29e9a39da" s60v5len="3553771" cdnurl = "http://vweixinf.tc.qq.com/110/20401/stodownload?m=504ddb44cc3e7473e188a6f29e9a39da&amp;filekey=30440201010430302e02016e040253480420353034646462343463633365373437336531383861366632396539613339646102033639eb040d00000004627466730000000132&amp;hy=SH&amp;storeid=265ccfec9000b70e4099434f20000006e01004fb153482a384bc1e66113c3b&amp;ef=1&amp;bizid=1022" designerid = "" thumburl = "" encrypturl = "http://vweixinf.tc.qq.com/110/20402/stodownload?m=72e87d60b6e6100a38f1497740b2fe34&amp;filekey=30440201010430302e02016e040253480420373265383764363062366536313030613338663134393737343062326665333402033639f0040d00000004627466730000000132&amp;hy=SH&amp;storeid=265ccfec9000f1188099434f20000006e02004fb253482a384bc1e66113c4e&amp;ef=2&amp;bizid=1022" aeskey= "7eb455f073764690aaac61f3df148fe4" externurl = "http://vweixinf.tc.qq.com/110/20403/stodownload?m=ba4ee17d8ab5d2919250f1dde295e5c7&amp;filekey=30440201010430302e02016e0402534804206261346565313764386162356432393139323530663164646532393565356337020300c800040d00000004627466730000000132&amp;hy=SH&amp;storeid=265ccfeca000398d7099434f20000006e03004fb353482a384bc1e66113c55&amp;ef=3&amp;bizid=1022" externmd5 = "a81ac81050f5cc271d5e763b75d1cae5" width= "512" height= "512" tpurl= "" tpauthkey= "" attachedtext= "" attachedtextcolor= "" lensid= "" emojiattr= "" linkid= "" desc= "" ></emoji>  </msg>'}, 'Status': 3, 'ImgStatus': 2, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1753418195, 'MsgSource': '<msgsource>\n\t<silence>0</silence>\n\t<membercount>62</membercount>\n\t<signature>N0_V1_+04FW8qi|v1_cM0wTj1r</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'PushContent': 'متسول小主在群聊中发了一个表情', 'NewMsgId': 3106284397953742756, 'MsgSeq': 871398654}
2025-07-25 12:36:14 | INFO | 收到表情消息: 消息ID:1281158629 来自:48097389945@chatroom 发送人:wxid_ptkyw926tmjn22 MD5:504ddb44cc3e7473e188a6f29e9a39da 大小:3553771
2025-07-25 12:36:14 | DEBUG | [DoubaoImageRecognition] 缓存表情消息: 3106284397953742756
2025-07-25 12:36:14 | DEBUG | 收到消息: {'MsgId': 1099305602, 'FromUserName': {'string': '48097389945@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 49, 'Content': {'string': 'wxid_z2oepgiz8qcs22:\n<?xml version="1.0"?>\n<msg>\n\t<appmsg appid="" sdkver="0">\n\t\t<title>已处理</title>\n\t\t<type>57</type>\n\t\t<appattach>\n\t\t\t<cdnthumbaeskey />\n\t\t\t<aeskey></aeskey>\n\t\t</appattach>\n\t\t<refermsg>\n\t\t\t<type>1</type>\n\t\t\t<svrid>1643338250879784274</svrid>\n\t\t\t<fromusr>48097389945@chatroom</fromusr>\n\t\t\t<chatusr>xiaomaochong</chatusr>\n\t\t\t<displayname>小爱</displayname>\n\t\t\t<content>@×\u2005请</content>\n\t\t\t<msgsource>&lt;msgsource&gt;&lt;sequence_id&gt;838529965&lt;/sequence_id&gt;\n\t&lt;atuserlist&gt;&lt;![CDATA[rongcheng6630606]]&gt;&lt;/atuserlist&gt;\n\t&lt;pua&gt;1&lt;/pua&gt;\n\t&lt;silence&gt;1&lt;/silence&gt;\n\t&lt;membercount&gt;63&lt;/membercount&gt;\n\t&lt;signature&gt;N0_V1_g9p84Rm3|v1_L3/k3QOf&lt;/signature&gt;\n\t&lt;tmp_node&gt;\n\t\t&lt;publisher-id&gt;&lt;/publisher-id&gt;\n\t&lt;/tmp_node&gt;\n&lt;/msgsource&gt;\n</msgsource>\n\t\t\t<createtime>1753418191</createtime>\n\t\t</refermsg>\n\t</appmsg>\n\t<fromusername>wxid_z2oepgiz8qcs22</fromusername>\n\t<scene>0</scene>\n\t<appinfo>\n\t\t<version>1</version>\n\t\t<appname />\n\t</appinfo>\n\t<commenturl />\n</msg>\n'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1753418195, 'MsgSource': '<msgsource>\n\t<sec_msg_node>\n\t\t<uuid>89c1cc9277a9ee4acd9e9c6f79cf33ec_</uuid>\n\t\t<risk-file-flag />\n\t\t<risk-file-md5-list />\n\t</sec_msg_node>\n\t<silence>0</silence>\n\t<membercount>62</membercount>\n\t<signature>N0_V1_qh1nL3gx|v1_YBxIVOW8</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'PushContent': '小爱同学 : 已处理', 'NewMsgId': 2537058475373215154, 'MsgSeq': 871398655}
2025-07-25 12:36:14 | DEBUG | 从群聊消息中提取发送者: wxid_z2oepgiz8qcs22
2025-07-25 12:36:14 | DEBUG | 使用已解析的XML处理引用消息
2025-07-25 12:36:14 | INFO | 收到引用消息: 消息ID:1099305602 来自:48097389945@chatroom 发送人:wxid_z2oepgiz8qcs22 内容:已处理 引用类型:1
2025-07-25 12:36:15 | INFO | [DouBaoImageToImage] 收到引用消息: 已处理
2025-07-25 12:36:15 | INFO | [TimerTask] 收到引用消息调试信息:
2025-07-25 12:36:15 | INFO |   - 消息内容: 已处理
2025-07-25 12:36:15 | INFO |   - 群组ID: 48097389945@chatroom
2025-07-25 12:36:15 | INFO |   - 发送人: wxid_z2oepgiz8qcs22
2025-07-25 12:36:15 | INFO |   - 引用信息: {'MsgType': 1, 'Content': '@×\u2005请', 'Msgid': '1643338250879784274', 'NewMsgId': '1643338250879784274', 'ToWxid': 'wxid_4usgcju5ey9q29', 'FromWxid': '48097389945@chatroom', 'Nickname': '小爱', 'MsgSource': '<msgsource><sequence_id>838529965</sequence_id>\n\t<atuserlist><![CDATA[rongcheng6630606]]></atuserlist>\n\t<pua>1</pua>\n\t<silence>1</silence>\n\t<membercount>63</membercount>\n\t<signature>N0_V1_g9p84Rm3|v1_L3/k3QOf</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'Createtime': '1753418191', 'SenderWxid': 'wxid_z2oepgiz8qcs22'}
2025-07-25 12:36:15 | INFO |   - 引用消息ID: 
2025-07-25 12:36:15 | INFO |   - 引用消息类型: 
2025-07-25 12:36:15 | INFO |   - 引用消息内容: @× 请
2025-07-25 12:36:15 | INFO |   - 引用消息发送人: wxid_z2oepgiz8qcs22
2025-07-25 12:36:15 | DEBUG | 收到消息: {'MsgId': 2052268576, 'FromUserName': {'string': '48097389945@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 49, 'Content': {'string': 'wxid_lneb7n23o4lg12:\n<?xml version="1.0"?>\n<msg>\n\t<appmsg appid="wx485a97c844086dc9" sdkver="0">\n\t\t<title>跑路了</title>\n\t\t<des>rongcheng6630606</des>\n\t\t<username />\n\t\t<action>view</action>\n\t\t<type>3</type>\n\t\t<showtype>0</showtype>\n\t\t<content />\n\t\t<url />\n\t\t<lowurl />\n\t\t<forwardflag>0</forwardflag>\n\t\t<dataurl>https://v3-luna.douyinvod.com/c82caab25426c3b8dede5d9b6f162b0b/68845733/video/tos/cn/tos-cn-ve-2774/okIZSZBorABwfixl8pYBAgQtNCAPCfKsPMDqib/</dataurl>\n\t\t<lowdataurl />\n\t\t<contentattr>0</contentattr>\n\t\t<streamvideo>\n\t\t\t<streamvideourl />\n\t\t\t<streamvideototaltime>0</streamvideototaltime>\n\t\t\t<streamvideotitle />\n\t\t\t<streamvideowording />\n\t\t\t<streamvideoweburl />\n\t\t\t<streamvideothumburl />\n\t\t\t<streamvideoaduxinfo />\n\t\t\t<streamvideopublishid />\n\t\t</streamvideo>\n\t\t<canvasPageItem>\n\t\t\t<canvasPageXml><![CDATA[]]></canvasPageXml>\n\t\t</canvasPageItem>\n\t\t<appattach>\n\t\t\t<totallen>0</totallen>\n\t\t\t<attachid />\n\t\t\t<cdnattachurl />\n\t\t\t<emoticonmd5 />\n\t\t\t<aeskey />\n\t\t\t<fileext />\n\t\t\t<islargefilemsg>0</islargefilemsg>\n\t\t</appattach>\n\t\t<extinfo />\n\t\t<androidsource>2</androidsource>\n\t\t<thumburl />\n\t\t<mediatagname />\n\t\t<messageaction><![CDATA[]]></messageaction>\n\t\t<messageext><![CDATA[]]></messageext>\n\t\t<emoticongift>\n\t\t\t<packageflag>0</packageflag>\n\t\t\t<packageid />\n\t\t</emoticongift>\n\t\t<emoticonshared>\n\t\t\t<packageflag>0</packageflag>\n\t\t\t<packageid />\n\t\t</emoticonshared>\n\t\t<designershared>\n\t\t\t<designeruin>0</designeruin>\n\t\t\t<designername>null</designername>\n\t\t\t<designerrediretcturl><![CDATA[null]]></designerrediretcturl>\n\t\t</designershared>\n\t\t<emotionpageshared>\n\t\t\t<tid>0</tid>\n\t\t\t<title>null</title>\n\t\t\t<desc>null</desc>\n\t\t\t<iconUrl><![CDATA[null]]></iconUrl>\n\t\t\t<secondUrl>null</secondUrl>\n\t\t\t<pageType>0</pageType>\n\t\t\t<setKey>null</setKey>\n\t\t</emotionpageshared>\n\t\t<webviewshared>\n\t\t\t<shareUrlOriginal />\n\t\t\t<shareUrlOpen />\n\t\t\t<jsAppId />\n\t\t\t<publisherId />\n\t\t\t<publisherReqId />\n\t\t</webviewshared>\n\t\t<template_id />\n\t\t<md5 />\n\t\t<websearch>\n\t\t\t<rec_category>0</rec_category>\n\t\t\t<channelId>0</channelId>\n\t\t</websearch>\n\t\t<weappinfo>\n\t\t\t<username />\n\t\t\t<appid />\n\t\t\t<appservicetype>0</appservicetype>\n\t\t\t<secflagforsinglepagemode>0</secflagforsinglepagemode>\n\t\t\t<videopageinfo>\n\t\t\t\t<thumbwidth>0</thumbwidth>\n\t\t\t\t<thumbheight>0</thumbheight>\n\t\t\t\t<fromopensdk>0</fromopensdk>\n\t\t\t</videopageinfo>\n\t\t</weappinfo>\n\t\t<statextstr>GhQKEnd4NDg1YTk3Yzg0NDA4NmRjOQ==</statextstr>\n\t\t<songalbumurl>https://wx.qlogo.cn/mmhead/ver_1/cncq8uic8uEfDhkkcMppv78WvOZsjglvMM7wgb4rBds1eJicPib90dTqZ5WAicgOwIE7GwTVE307roTiaPVQhPs49ibh44XY8sXEhNUZgo9hlXqdW05JzqiaJnicJ0QFd3I3jnwSkMNlU2T4qiclp98y6ztPTicQ/0</songalbumurl>\n\t\t<songlyric>[99:99.99]小爱提醒您该歌曲暂无歌词！</songlyric>\n\t\t<musicShareItem>\n\t\t\t<musicDuration>0</musicDuration>\n\t\t</musicShareItem>\n\t\t<finderLiveProductShare>\n\t\t\t<finderLiveID><![CDATA[]]></finderLiveID>\n\t\t\t<finderUsername><![CDATA[]]></finderUsername>\n\t\t\t<finderObjectID><![CDATA[]]></finderObjectID>\n\t\t\t<finderNonceID><![CDATA[]]></finderNonceID>\n\t\t\t<liveStatus><![CDATA[]]></liveStatus>\n\t\t\t<appId><![CDATA[]]></appId>\n\t\t\t<pagePath><![CDATA[]]></pagePath>\n\t\t\t<productId><![CDATA[]]></productId>\n\t\t\t<coverUrl><![CDATA[]]></coverUrl>\n\t\t\t<productTitle><![CDATA[]]></productTitle>\n\t\t\t<marketPrice><![CDATA[0]]></marketPrice>\n\t\t\t<sellingPrice><![CDATA[0]]></sellingPrice>\n\t\t\t<platformHeadImg><![CDATA[]]></platformHeadImg>\n\t\t\t<platformName><![CDATA[]]></platformName>\n\t\t\t<shopWindowId><![CDATA[]]></shopWindowId>\n\t\t\t<flashSalePrice><![CDATA[0]]></flashSalePrice>\n\t\t\t<flashSaleEndTime><![CDATA[0]]></flashSaleEndTime>\n\t\t\t<ecSource><![CDATA[]]></ecSource>\n\t\t\t<sellingPriceWording><![CDATA[]]></sellingPriceWording>\n\t\t\t<platformIconURL><![CDATA[]]></platformIconURL>\n\t\t\t<firstProductTagURL><![CDATA[]]></firstProductTagURL>\n\t\t\t<firstProductTagAspectRatioString><![CDATA[0.0]]></firstProductTagAspectRatioString>\n\t\t\t<secondProductTagURL><![CDATA[]]></secondProductTagURL>\n\t\t\t<secondProductTagAspectRatioString><![CDATA[0.0]]></secondProductTagAspectRatioString>\n\t\t\t<firstGuaranteeWording><![CDATA[]]></firstGuaranteeWording>\n\t\t\t<secondGuaranteeWording><![CDATA[]]></secondGuaranteeWording>\n\t\t\t<thirdGuaranteeWording><![CDATA[]]></thirdGuaranteeWording>\n\t\t\t<isPriceBeginShow>false</isPriceBeginShow>\n\t\t\t<lastGMsgID><![CDATA[]]></lastGMsgID>\n\t\t\t<promoterKey><![CDATA[]]></promoterKey>\n\t\t\t<discountWording><![CDATA[]]></discountWording>\n\t\t\t<priceSuffixDescription><![CDATA[]]></priceSuffixDescription>\n\t\t\t<showBoxItemStringList />\n\t\t</finderLiveProductShare>\n\t\t<finderOrder>\n\t\t\t<appID><![CDATA[]]></appID>\n\t\t\t<orderID><![CDATA[]]></orderID>\n\t\t\t<path><![CDATA[]]></path>\n\t\t\t<priceWording><![CDATA[]]></priceWording>\n\t\t\t<stateWording><![CDATA[]]></stateWording>\n\t\t\t<productImageURL><![CDATA[]]></productImageURL>\n\t\t\t<products><![CDATA[]]></products>\n\t\t\t<productsCount><![CDATA[0]]></productsCount>\n\t\t</finderOrder>\n\t\t<finderShopWindowShare>\n\t\t\t<finderUsername><![CDATA[]]></finderUsername>\n\t\t\t<avatar><![CDATA[]]></avatar>\n\t\t\t<nickname><![CDATA[]]></nickname>\n\t\t\t<commodityInStockCount><![CDATA[]]></commodityInStockCount>\n\t\t\t<appId><![CDATA[]]></appId>\n\t\t\t<path><![CDATA[]]></path>\n\t\t\t<appUsername><![CDATA[]]></appUsername>\n\t\t\t<query><![CDATA[]]></query>\n\t\t\t<liteAppId><![CDATA[]]></liteAppId>\n\t\t\t<liteAppPath><![CDATA[]]></liteAppPath>\n\t\t\t<liteAppQuery><![CDATA[]]></liteAppQuery>\n\t\t\t<platformTagURL><![CDATA[]]></platformTagURL>\n\t\t\t<saleWording><![CDATA[]]></saleWording>\n\t\t\t<lastGMsgID><![CDATA[]]></lastGMsgID>\n\t\t\t<profileTypeWording><![CDATA[]]></profileTypeWording>\n\t\t\t<reputationInfo>\n\t\t\t\t<hasReputationInfo>0</hasReputationInfo>\n\t\t\t\t<reputationScore>0</reputationScore>\n\t\t\t\t<reputationWording />\n\t\t\t\t<reputationTextColor />\n\t\t\t\t<reputationLevelWording />\n\t\t\t\t<reputationBackgroundColor />\n\t\t\t</reputationInfo>\n\t\t\t<productImageURLList />\n\t\t</finderShopWindowShare>\n\t\t<findernamecard>\n\t\t\t<username />\n\t\t\t<avatar><![CDATA[]]></avatar>\n\t\t\t<nickname />\n\t\t\t<auth_job />\n\t\t\t<auth_icon>0</auth_icon>\n\t\t\t<auth_icon_url />\n\t\t\t<ecSource><![CDATA[]]></ecSource>\n\t\t\t<lastGMsgID><![CDATA[]]></lastGMsgID>\n\t\t</findernamecard>\n\t\t<finderGuarantee>\n\t\t\t<scene><![CDATA[0]]></scene>\n\t\t</finderGuarantee>\n\t\t<directshare>0</directshare>\n\t\t<gamecenter>\n\t\t\t<namecard>\n\t\t\t\t<iconUrl />\n\t\t\t\t<name />\n\t\t\t\t<desc />\n\t\t\t\t<tail />\n\t\t\t\t<jumpUrl />\n\t\t\t</namecard>\n\t\t</gamecenter>\n\t\t<patMsg>\n\t\t\t<chatUser />\n\t\t\t<records>\n\t\t\t\t<recordNum>0</recordNum>\n\t\t\t</records>\n\t\t</patMsg>\n\t\t<secretmsg>\n\t\t\t<issecretmsg>0</issecretmsg>\n\t\t</secretmsg>\n\t\t<referfromscene>0</referfromscene>\n\t\t<gameshare>\n\t\t\t<liteappext>\n\t\t\t\t<liteappbizdata />\n\t\t\t\t<priority>0</priority>\n\t\t\t</liteappext>\n\t\t\t<appbrandext>\n\t\t\t\t<litegameinfo />\n\t\t\t\t<priority>-1</priority>\n\t\t\t</appbrandext>\n\t\t\t<gameshareid />\n\t\t\t<sharedata />\n\t\t\t<isvideo>0</isvideo>\n\t\t\t<duration>-1</duration>\n\t\t\t<isexposed>0</isexposed>\n\t\t\t<readtext />\n\t\t</gameshare>\n\t\t<mpsharetrace>\n\t\t\t<hasfinderelement>0</hasfinderelement>\n\t\t\t<lastgmsgid />\n\t\t</mpsharetrace>\n\t\t<wxgamecard>\n\t\t\t<framesetname />\n\t\t\t<mbcarddata />\n\t\t\t<minpkgversion />\n\t\t\t<mbcardheight>0</mbcardheight>\n\t\t\t<isoldversion>0</isoldversion>\n\t\t</wxgamecard>\n\t\t<liteapp>\n\t\t\t<id>null</id>\n\t\t\t<path />\n\t\t\t<query />\n\t\t</liteapp>\n\t</appmsg>\n\t<fromusername>wxid_lneb7n23o4lg12</fromusername>\n\t<scene>0</scene>\n\t<appinfo>\n\t\t<version>29</version>\n\t\t<appname>摇一摇搜歌</appname>\n\t</appinfo>\n\t<commenturl></commenturl>\n</msg>\n'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1753418197, 'MsgSource': '<msgsource>\n\t<sec_msg_node>\n\t\t<uuid>dc86130d6443b396a1609a971d65747a_</uuid>\n\t\t<risk-file-flag />\n\t\t<risk-file-md5-list />\n\t</sec_msg_node>\n\t<silence>0</silence>\n\t<membercount>62</membercount>\n\t<signature>N0_V1_NLFlZZqp|v1_xJRLI/cE</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'PushContent': '你收到了一条消息', 'NewMsgId': 5719905469631104165, 'MsgSeq': 871398656}
2025-07-25 12:36:15 | DEBUG | 从群聊消息中提取发送者: wxid_lneb7n23o4lg12
2025-07-25 12:36:15 | DEBUG | XML消息完整内容:
<?xml version="1.0"?>
<msg>
	<appmsg appid="wx485a97c844086dc9" sdkver="0">
		<title>跑路了</title>
		<des>rongcheng6630606</des>
		<username />
		<action>view</action>
		<type>3</type>
		<showtype>0</showtype>
		<content />
		<url />
		<lowurl />
		<forwardflag>0</forwardflag>
		<dataurl>https://v3-luna.douyinvod.com/c82caab25426c3b8dede5d9b6f162b0b/68845733/video/tos/cn/tos-cn-ve-2774/okIZSZBorABwfixl8pYBAgQtNCAPCfKsPMDqib/</dataurl>
		<lowdataurl />
		<contentattr>0</contentattr>
		<streamvideo>
			<streamvideourl />
			<streamvideototaltime>0</streamvideototaltime>
			<streamvideotitle />
			<streamvideowording />
			<streamvideoweburl />
			<streamvideothumburl />
			<streamvideoaduxinfo />
			<streamvideopublishid />
		</streamvideo>
		<canvasPageItem>
			<canvasPageXml><![CDATA[]]></canvasPageXml>
		</canvasPageItem>
		<appattach>
			<totallen>0</totallen>
			<attachid />
			<cdnattachurl />
			<emoticonmd5 />
			<aeskey />
			<fileext />
			<islargefilemsg>0</islargefilemsg>
		</appattach>
		<extinfo />
		<androidsource>2</androidsource>
		<thumburl />
		<mediatagname />
		<messageaction><![CDATA[]]></messageaction>
		<messageext><![CDATA[]]></messageext>
		<emoticongift>
			<packageflag>0</packageflag>
			<packageid />
		</emoticongift>
		<emoticonshared>
			<packageflag>0</packageflag>
			<packageid />
		</emoticonshared>
		<designershared>
			<designeruin>0</designeruin>
			<designername>null</designername>
			<designerrediretcturl><![CDATA[null]]></designerrediretcturl>
		</designershared>
		<emotionpageshared>
			<tid>0</tid>
			<title>null</title>
			<desc>null</desc>
			<iconUrl><![CDATA[null]]></iconUrl>
			<secondUrl>null</secondUrl>
			<pageType>0</pageType>
			<setKey>null</setKey>
		</emotionpageshared>
		<webviewshared>
			<shareUrlOriginal />
			<shareUrlOpen />
			<jsAppId />
			<publisherId />
			<publisherReqId />
		</webviewshared>
		<template_id />
		<md5 />
		<websearch>
			<rec_category>0</rec_category>
			<channelId>0</channelId>
		</websearch>
		<weappinfo>
			<username />
			<appid />
			<appservicetype>0</appservicetype>
			<secflagforsinglepagemode>0</secflagforsinglepagemode>
			<videopageinfo>
				<thumbwidth>0</thumbwidth>
				<thumbheight>0</thumbheight>
				<fromopensdk>0</fromopensdk>
			</videopageinfo>
		</weappinfo>
		<statextstr>GhQKEnd4NDg1YTk3Yzg0NDA4NmRjOQ==</statextstr>
		<songalbumurl>https://wx.qlogo.cn/mmhead/ver_1/cncq8uic8uEfDhkkcMppv78WvOZsjglvMM7wgb4rBds1eJicPib90dTqZ5WAicgOwIE7GwTVE307roTiaPVQhPs49ibh44XY8sXEhNUZgo9hlXqdW05JzqiaJnicJ0QFd3I3jnwSkMNlU2T4qiclp98y6ztPTicQ/0</songalbumurl>
		<songlyric>[99:99.99]小爱提醒您该歌曲暂无歌词！</songlyric>
		<musicShareItem>
			<musicDuration>0</musicDuration>
		</musicShareItem>
		<finderLiveProductShare>
			<finderLiveID><![CDATA[]]></finderLiveID>
			<finderUsername><![CDATA[]]></finderUsername>
			<finderObjectID><![CDATA[]]></finderObjectID>
			<finderNonceID><![CDATA[]]></finderNonceID>
			<liveStatus><![CDATA[]]></liveStatus>
			<appId><![CDATA[]]></appId>
			<pagePath><![CDATA[]]></pagePath>
			<productId><![CDATA[]]></productId>
			<coverUrl><![CDATA[]]></coverUrl>
			<productTitle><![CDATA[]]></productTitle>
			<marketPrice><![CDATA[0]]></marketPrice>
			<sellingPrice><![CDATA[0]]></sellingPrice>
			<platformHeadImg><![CDATA[]]></platformHeadImg>
			<platformName><![CDATA[]]></platformName>
			<shopWindowId><![CDATA[]]></shopWindowId>
			<flashSalePrice><![CDATA[0]]></flashSalePrice>
			<flashSaleEndTime><![CDATA[0]]></flashSaleEndTime>
			<ecSource><![CDATA[]]></ecSource>
			<sellingPriceWording><![CDATA[]]></sellingPriceWording>
			<platformIconURL><![CDATA[]]></platformIconURL>
			<firstProductTagURL><![CDATA[]]></firstProductTagURL>
			<firstProductTagAspectRatioString><![CDATA[0.0]]></firstProductTagAspectRatioString>
			<secondProductTagURL><![CDATA[]]></secondProductTagURL>
			<secondProductTagAspectRatioString><![CDATA[0.0]]></secondProductTagAspectRatioString>
			<firstGuaranteeWording><![CDATA[]]></firstGuaranteeWording>
			<secondGuaranteeWording><![CDATA[]]></secondGuaranteeWording>
			<thirdGuaranteeWording><![CDATA[]]></thirdGuaranteeWording>
			<isPriceBeginShow>false</isPriceBeginShow>
			<lastGMsgID><![CDATA[]]></lastGMsgID>
			<promoterKey><![CDATA[]]></promoterKey>
			<discountWording><![CDATA[]]></discountWording>
			<priceSuffixDescription><![CDATA[]]></priceSuffixDescription>
			<showBoxItemStringList />
		</finderLiveProductShare>
		<finderOrder>
			<appID><![CDATA[]]></appID>
			<orderID><![CDATA[]]></orderID>
			<path><![CDATA[]]></path>
			<priceWording><![CDATA[]]></priceWording>
			<stateWording><![CDATA[]]></stateWording>
			<productImageURL><![CDATA[]]></productImageURL>
			<products><![CDATA[]]></products>
			<productsCount><![CDATA[0]]></productsCount>
		</finderOrder>
		<finderShopWindowShare>
			<finderUsername><![CDATA[]]></finderUsername>
			<avatar><![CDATA[]]></avatar>
			<nickname><![CDATA[]]></nickname>
			<commodityInStockCount><![CDATA[]]></commodityInStockCount>
			<appId><![CDATA[]]></appId>
			<path><![CDATA[]]></path>
			<appUsername><![CDATA[]]></appUsername>
			<query><![CDATA[]]></query>
			<liteAppId><![CDATA[]]></liteAppId>
			<liteAppPath><![CDATA[]]></liteAppPath>
			<liteAppQuery><![CDATA[]]></liteAppQuery>
			<platformTagURL><![CDATA[]]></platformTagURL>
			<saleWording><![CDATA[]]></saleWording>
			<lastGMsgID><![CDATA[]]></lastGMsgID>
			<profileTypeWording><![CDATA[]]></profileTypeWording>
			<reputationInfo>
				<hasReputationInfo>0</hasReputationInfo>
				<reputationScore>0</reputationScore>
				<reputationWording />
				<reputationTextColor />
				<reputationLevelWording />
				<reputationBackgroundColor />
			</reputationInfo>
			<productImageURLList />
		</finderShopWindowShare>
		<findernamecard>
			<username />
			<avatar><![CDATA[]]></avatar>
			<nickname />
			<auth_job />
			<auth_icon>0</auth_icon>
			<auth_icon_url />
			<ecSource><![CDATA[]]></ecSource>
			<lastGMsgID><![CDATA[]]></lastGMsgID>
		</findernamecard>
		<finderGuarantee>
			<scene><![CDATA[0]]></scene>
		</finderGuarantee>
		<directshare>0</directshare>
		<gamecenter>
			<namecard>
				<iconUrl />
				<name />
				<desc />
				<tail />
				<jumpUrl />
			</namecard>
		</gamecenter>
		<patMsg>
			<chatUser />
			<records>
				<recordNum>0</recordNum>
			</records>
		</patMsg>
		<secretmsg>
			<issecretmsg>0</issecretmsg>
		</secretmsg>
		<referfromscene>0</referfromscene>
		<gameshare>
			<liteappext>
				<liteappbizdata />
				<priority>0</priority>
			</liteappext>
			<appbrandext>
				<litegameinfo />
				<priority>-1</priority>
			</appbrandext>
			<gameshareid />
			<sharedata />
			<isvideo>0</isvideo>
			<duration>-1</duration>
			<isexposed>0</isexposed>
			<readtext />
		</gameshare>
		<mpsharetrace>
			<hasfinderelement>0</hasfinderelement>
			<lastgmsgid />
		</mpsharetrace>
		<wxgamecard>
			<framesetname />
			<mbcarddata />
			<minpkgversion />
			<mbcardheight>0</mbcardheight>
			<isoldversion>0</isoldversion>
		</wxgamecard>
		<liteapp>
			<id>null</id>
			<path />
			<query />
		</liteapp>
	</appmsg>
	<fromusername>wxid_lneb7n23o4lg12</fromusername>
	<scene>0</scene>
	<appinfo>
		<version>29</version>
		<appname>摇一摇搜歌</appname>
	</appinfo>
	<commenturl></commenturl>
</msg>

2025-07-25 12:36:15 | DEBUG | XML消息类型: 3
2025-07-25 12:36:15 | DEBUG | XML消息标题: 跑路了
2025-07-25 12:36:15 | DEBUG | XML消息描述: rongcheng6630606
2025-07-25 12:36:15 | DEBUG | 附件信息 totallen: 0
2025-07-25 12:36:15 | DEBUG | 附件信息 islargefilemsg: 0
2025-07-25 12:36:15 | INFO | 收到红包消息: 标题:跑路了 描述:rongcheng6630606 来自:48097389945@chatroom 发送人:wxid_4usgcju5ey9q29
2025-07-25 12:36:35 | DEBUG | 收到消息: {'MsgId': 962164664, 'FromUserName': {'string': '48097389945@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 47, 'Content': {'string': 'wxid_mwil20w1e1j422:\n<msg><emoji fromusername="wxid_mwil20w1e1j422" tousername="48097389945@chatroom" type="1" idbuffer="media:0_0" md5="8c64bc68b8be8db62a3357994429fa51" len="6702575" productid="" androidmd5="8c64bc68b8be8db62a3357994429fa51" androidlen="6702575" s60v3md5="8c64bc68b8be8db62a3357994429fa51" s60v3len="6702575" s60v5md5="8c64bc68b8be8db62a3357994429fa51" s60v5len="6702575" cdnurl="http://wxapp.tc.qq.com/262/20304/stodownload?m=8c64bc68b8be8db62a3357994429fa51&amp;filekey=30350201010421301f020201060402535a04108c64bc68b8be8db62a3357994429fa5102036645ef040d00000004627466730000000131&amp;hy=SZ&amp;storeid=32303231303632363231303831363030303362333630383235633739373938623338356630393030303030313036&amp;bizid=1023" designerid="" thumburl="" encrypturl="http://wxapp.tc.qq.com/262/20304/stodownload?m=1e8ab009fb261dc5990dfc97c944efa9&amp;filekey=30350201010421301f020201060402535a04101e8ab009fb261dc5990dfc97c944efa902036645f0040d00000004627466730000000131&amp;hy=SZ&amp;storeid=32303231303632363231303831393030306231623530383235633739373961313536353830393030303030313036&amp;bizid=1023" aeskey="6326e3d2860a28da9bcedda073ba2b24" externurl="http://wxapp.tc.qq.com/262/20304/stodownload?m=89b530ddd215dcaa02afef959a83e5a1&amp;filekey=30350201010421301f020201060402535a041089b530ddd215dcaa02afef959a83e5a1020303bfd0040d00000004627466730000000131&amp;hy=SZ&amp;storeid=32303231303632363231303832303030306363353831383235633739373934643461356630393030303030313036&amp;bizid=1023" externmd5="f58be750820839aff3fd1a3cf3ab1e88" width="300" height="300" tpurl="" tpauthkey="" attachedtext="在忙" attachedtextcolor="FFFFFF" lensid="" emojiattr="CgblnKjlv5k=" linkid="" desc="" activityid="Selfie:bc466285059c0e000e438a42d6489ed1"></emoji></msg>'}, 'Status': 3, 'ImgStatus': 2, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1753418217, 'MsgSource': '<msgsource>\n\t<silence>0</silence>\n\t<membercount>62</membercount>\n\t<signature>N0_V1_YCFSYixk|v1_aS8ydhQT</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'PushContent': '37.2℃在群聊中发了一个表情', 'NewMsgId': 3645942747704821724, 'MsgSeq': 871398657}
2025-07-25 12:36:35 | INFO | 收到表情消息: 消息ID:962164664 来自:48097389945@chatroom 发送人:wxid_mwil20w1e1j422 MD5:8c64bc68b8be8db62a3357994429fa51 大小:6702575
2025-07-25 12:36:35 | DEBUG | [DoubaoImageRecognition] 缓存表情消息: 3645942747704821724
2025-07-25 12:36:40 | DEBUG | 收到消息: {'MsgId': 345894656, 'FromUserName': {'string': '48097389945@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 1, 'Content': {'string': 'wxid_cwxauba42ki122:\n不见了'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1753418222, 'MsgSource': '<msgsource>\n\t<pua>1</pua>\n\t<silence>0</silence>\n\t<membercount>62</membercount>\n\t<signature>N0_V1_J6I8kVV/|v1_yzrvgErC</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'PushContent': 'ن初心ᯤ⁶ᴳ คิดถึงن : 不见了', 'NewMsgId': 3958923426762836860, 'MsgSeq': 871398658}
2025-07-25 12:36:40 | INFO | 收到文本消息: 消息ID:345894656 来自:48097389945@chatroom 发送人:wxid_cwxauba42ki122 @:[] 内容:不见了
2025-07-25 12:36:40 | DEBUG | 处理消息内容: '不见了'
2025-07-25 12:36:40 | DEBUG | 消息内容 '不见了' 不匹配任何命令，忽略
2025-07-25 12:36:42 | DEBUG | 收到消息: {'MsgId': 1664639842, 'FromUserName': {'string': '48097389945@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 1, 'Content': {'string': 'wxid_mwil20w1e1j422:\n@十五\u2005sb'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1753418223, 'MsgSource': '<msgsource>\n\t<atuserlist><![CDATA[wxid_l9koi6kli78i22]]></atuserlist>\n\t<pua>1</pua>\n\t<silence>0</silence>\n\t<membercount>62</membercount>\n\t<signature>N0_V1_mkLu6xRg|v1_6ptBYjQE</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'PushContent': '37.2℃ : @十五\u2005sb', 'NewMsgId': 2605826354318987218, 'MsgSeq': 871398659}
2025-07-25 12:36:42 | INFO | 收到文本消息: 消息ID:1664639842 来自:48097389945@chatroom 发送人:wxid_mwil20w1e1j422 @:['wxid_l9koi6kli78i22'] 内容:@十五 sb
2025-07-25 12:36:42 | DEBUG | 处理消息内容: '@十五 sb'
2025-07-25 12:36:42 | DEBUG | 消息内容 '@十五 sb' 不匹配任何命令，忽略
2025-07-25 12:36:47 | DEBUG | 收到消息: {'MsgId': 443726104, 'FromUserName': {'string': '48097389945@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 1, 'Content': {'string': 'wxid_l9koi6kli78i22:\n@37.2℃\u2005吃你妈'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1753418230, 'MsgSource': '<msgsource>\n\t<atuserlist><![CDATA[wxid_mwil20w1e1j422]]></atuserlist>\n\t<pua>1</pua>\n\t<silence>0</silence>\n\t<membercount>62</membercount>\n\t<signature>N0_V1_IuObYTib|v1_eAoxdvwP</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'PushContent': '十五 : @37.2℃\u2005吃你妈', 'NewMsgId': 8326368944933529223, 'MsgSeq': 871398660}
2025-07-25 12:36:47 | INFO | 收到文本消息: 消息ID:443726104 来自:48097389945@chatroom 发送人:wxid_l9koi6kli78i22 @:['wxid_mwil20w1e1j422'] 内容:@37.2℃ 吃你妈
2025-07-25 12:36:48 | DEBUG | 处理消息内容: '@37.2℃ 吃你妈'
2025-07-25 12:36:48 | DEBUG | 消息内容 '@37.2℃ 吃你妈' 不匹配任何命令，忽略
2025-07-25 12:37:03 | DEBUG | 收到消息: {'MsgId': 398171705, 'FromUserName': {'string': '48097389945@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 1, 'Content': {'string': 'wxid_l9koi6kli78i22:\n@37.2℃\u2005cfm'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1753418246, 'MsgSource': '<msgsource>\n\t<atuserlist><![CDATA[wxid_mwil20w1e1j422]]></atuserlist>\n\t<pua>1</pua>\n\t<silence>0</silence>\n\t<membercount>62</membercount>\n\t<signature>N0_V1_4Jdt7APs|v1_c8o4cCQj</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'PushContent': '十五 : @37.2℃\u2005cfm', 'NewMsgId': 2906753782027703278, 'MsgSeq': 871398661}
2025-07-25 12:37:03 | INFO | 收到文本消息: 消息ID:398171705 来自:48097389945@chatroom 发送人:wxid_l9koi6kli78i22 @:['wxid_mwil20w1e1j422'] 内容:@37.2℃ cfm
2025-07-25 12:37:04 | DEBUG | 处理消息内容: '@37.2℃ cfm'
2025-07-25 12:37:04 | DEBUG | 消息内容 '@37.2℃ cfm' 不匹配任何命令，忽略
2025-07-25 12:37:05 | DEBUG | 收到消息: {'MsgId': 1114499038, 'FromUserName': {'string': '48097389945@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 1, 'Content': {'string': 'wxid_l9koi6kli78i22:\n快点'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1753418248, 'MsgSource': '<msgsource>\n\t<pua>1</pua>\n\t<silence>0</silence>\n\t<membercount>62</membercount>\n\t<signature>N0_V1_l4YCly8f|v1_hl5KMlov</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'PushContent': '十五 : 快点', 'NewMsgId': 4419305020033341618, 'MsgSeq': 871398662}
2025-07-25 12:37:05 | INFO | 收到文本消息: 消息ID:1114499038 来自:48097389945@chatroom 发送人:wxid_l9koi6kli78i22 @:[] 内容:快点
2025-07-25 12:37:06 | DEBUG | 处理消息内容: '快点'
2025-07-25 12:37:06 | DEBUG | 消息内容 '快点' 不匹配任何命令，忽略
2025-07-25 12:37:08 | DEBUG | 收到消息: {'MsgId': 149390341, 'FromUserName': {'string': '48097389945@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 1, 'Content': {'string': 'wxid_wlnzvr8ivgd422:\n6'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1753418249, 'MsgSource': '<msgsource>\n\t<pua>1</pua>\n\t<eggIncluded>1</eggIncluded>\n\t<silence>0</silence>\n\t<membercount>62</membercount>\n\t<signature>N0_V1_fU109MIH|v1_yT/r/Zg5</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'PushContent': '锦岚（开玩笑就退群版） : 6', 'NewMsgId': 8131678126134914092, 'MsgSeq': 871398663}
2025-07-25 12:37:08 | INFO | 收到文本消息: 消息ID:149390341 来自:48097389945@chatroom 发送人:wxid_wlnzvr8ivgd422 @:[] 内容:6
2025-07-25 12:37:08 | DEBUG | 处理消息内容: '6'
2025-07-25 12:37:08 | DEBUG | 消息内容 '6' 不匹配任何命令，忽略
2025-07-25 12:37:10 | DEBUG | 收到消息: {'MsgId': 830130621, 'FromUserName': {'string': '48097389945@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 34, 'Content': {'string': 'wxid_lneb7n23o4lg12:\n<msg><voicemsg endflag="1" cancelflag="0" forwardflag="0" voiceformat="4" voicelength="1" length="15360" bufid="0" aeskey="69786b6e6b696f6374776b7670637878" voiceurl="3052020100044b304902010002046b3e4bb802033d11fe020466f765b4020468830a0b042463353239363439302d313666632d343564612d386135612d36333662336234353531393402040524000f02010004002a5cd9b3" voicemd5="04ec4753020d41a2f807786678dba78d" clientmsgid="41653132633234663932353735336100301237072525a7b9a8c7ac1100" fromusername="wxid_lneb7n23o4lg12" /></msg>'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1753418251, 'MsgSource': '<msgsource>\n\t<silence>0</silence>\n\t<membercount>62</membercount>\n\t<signature>N0_V1_eUKbgDHC|v1_1HXt03in</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'PushContent': 'ᡣ在群聊中发了一段语音', 'NewMsgId': 6382297484901717189, 'MsgSeq': 871398664}
2025-07-25 12:37:10 | INFO | 收到语音消息: 消息ID:830130621 来自:48097389945@chatroom 发送人:wxid_lneb7n23o4lg12 XML:
<msg><voicemsg endflag="1" cancelflag="0" forwardflag="0" voiceformat="4" voicelength="1" length="15360" bufid="0" aeskey="69786b6e6b696f6374776b7670637878" voiceurl="3052020100044b304902010002046b3e4bb802033d11fe020466f765b4020468830a0b042463353239363439302d313666632d343564612d386135612d36333662336234353531393402040524000f02010004002a5cd9b3" voicemd5="04ec4753020d41a2f807786678dba78d" clientmsgid="41653132633234663932353735336100301237072525a7b9a8c7ac1100" fromusername="wxid_lneb7n23o4lg12" /></msg>
2025-07-25 12:37:13 | DEBUG | 收到消息: {'MsgId': 665157133, 'FromUserName': {'string': '48097389945@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 47, 'Content': {'string': 'wxid_ptkyw926tmjn22:\n<msg><emoji fromusername = "wxid_ptkyw926tmjn22" tousername = "48097389945@chatroom" type="1" idbuffer="media:0_0" md5="e883ade284d3b337a9a56ad38b2c1dee" len = "15417" productid="" androidmd5="e883ade284d3b337a9a56ad38b2c1dee" androidlen="15417" s60v3md5 = "e883ade284d3b337a9a56ad38b2c1dee" s60v3len="15417" s60v5md5 = "e883ade284d3b337a9a56ad38b2c1dee" s60v5len="15417" cdnurl = "http://wxapp.tc.qq.com/262/20304/stodownload?m=e883ade284d3b337a9a56ad38b2c1dee&amp;filekey=30340201010420301e02020106040253480410e883ade284d3b337a9a56ad38b2c1dee02023c39040d00000004627466730000000132&amp;hy=SH&amp;storeid=26321ffa60001e500000000000000010600004f505348132c596097a98cb08&amp;bizid=1023" designerid = "" thumburl = "" encrypturl = "http://wxapp.tc.qq.com/262/20304/stodownload?m=e4e465e770a9fd5ed40440a503b88177&amp;filekey=30340201010420301e02020106040253480410e4e465e770a9fd5ed40440a503b8817702023c40040d00000004627466730000000132&amp;hy=SH&amp;storeid=26321ffa600045716000000000000010600004f5053481cd67b40b7b4bb67a&amp;bizid=1023" aeskey= "b2e0830325e8030005f383dd932e74b9" externurl = "http://wxapp.tc.qq.com/262/20304/stodownload?m=8b87a7922b1e13a0ed27e5aae579aaaf&amp;filekey=30340201010420301e020201060402534804108b87a7922b1e13a0ed27e5aae579aaaf02022a50040d00000004627466730000000132&amp;hy=SH&amp;storeid=26321ffa60007468f000000000000010600004f5053482a8c596097aac135f&amp;bizid=1023" externmd5 = "5e5083623248c1e17fc62d6b2012d45b" width= "300" height= "300" tpurl= "" tpauthkey= "" attachedtext= "" attachedtextcolor= "" lensid= "" emojiattr= "" linkid= "" desc= "" ></emoji>  </msg>'}, 'Status': 3, 'ImgStatus': 2, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1753418255, 'MsgSource': '<msgsource>\n\t<silence>0</silence>\n\t<membercount>62</membercount>\n\t<signature>N0_V1_N766nv+h|v1_5+AtoRUb</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'PushContent': 'متسول小主在群聊中发了一个表情', 'NewMsgId': 9007258168211078417, 'MsgSeq': 871398665}
2025-07-25 12:37:13 | INFO | 收到表情消息: 消息ID:665157133 来自:48097389945@chatroom 发送人:wxid_ptkyw926tmjn22 MD5:e883ade284d3b337a9a56ad38b2c1dee 大小:15417
2025-07-25 12:37:13 | DEBUG | [DoubaoImageRecognition] 缓存表情消息: 9007258168211078417
2025-07-25 12:37:22 | DEBUG | 收到消息: {'MsgId': 1890250112, 'FromUserName': {'string': '48097389945@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 1, 'Content': {'string': 'wxid_cwxauba42ki122:\n[擦汗]'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1753418264, 'MsgSource': '<msgsource>\n\t<pua>1</pua>\n\t<silence>0</silence>\n\t<membercount>62</membercount>\n\t<signature>N0_V1_6EZAWigd|v1_1AMNGnnY</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'PushContent': 'ن初心ᯤ⁶ᴳ คิดถึงن : [擦汗]', 'NewMsgId': 1956860007527700520, 'MsgSeq': 871398666}
2025-07-25 12:37:22 | INFO | 收到表情消息: 消息ID:1890250112 来自:48097389945@chatroom 发送人:wxid_cwxauba42ki122 @:[] 内容:[擦汗]
2025-07-25 12:37:22 | DEBUG | [DoubaoImageRecognition] 缓存表情消息: 1956860007527700520
2025-07-25 12:37:32 | DEBUG | 收到消息: {'MsgId': 802069715, 'FromUserName': {'string': '48097389945@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 1, 'Content': {'string': 'wxid_wlnzvr8ivgd422:\n好久没喝奶茶了，有没有人请'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1753418274, 'MsgSource': '<msgsource>\n\t<pua>1</pua>\n\t<eggIncluded>1</eggIncluded>\n\t<silence>0</silence>\n\t<membercount>62</membercount>\n\t<signature>N0_V1_Ukq1ALxr|v1_Vf+hDVL8</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'PushContent': '锦岚（开玩笑就退群版） : 好久没喝奶茶了，有没有人请', 'NewMsgId': 1928358490057423077, 'MsgSeq': 871398667}
2025-07-25 12:37:32 | INFO | 收到文本消息: 消息ID:802069715 来自:48097389945@chatroom 发送人:wxid_wlnzvr8ivgd422 @:[] 内容:好久没喝奶茶了，有没有人请
2025-07-25 12:37:32 | DEBUG | 处理消息内容: '好久没喝奶茶了，有没有人请'
2025-07-25 12:37:32 | DEBUG | 消息内容 '好久没喝奶茶了，有没有人请' 不匹配任何命令，忽略
2025-07-25 12:37:34 | DEBUG | 收到消息: {'MsgId': 1371480695, 'FromUserName': {'string': '48097389945@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 47, 'Content': {'string': 'wxid_wlnzvr8ivgd422:\n<msg><emoji fromusername = "wxid_wlnzvr8ivgd422" tousername = "48097389945@chatroom" type="2" idbuffer="media:0_0" md5="3c5d9173b23ecc025e1ec8c858142b57" len = "1005541" productid="" androidmd5="3c5d9173b23ecc025e1ec8c858142b57" androidlen="1005541" s60v3md5 = "3c5d9173b23ecc025e1ec8c858142b57" s60v3len="1005541" s60v5md5 = "3c5d9173b23ecc025e1ec8c858142b57" s60v5len="1005541" cdnurl = "http://vweixinf.tc.qq.com/110/20401/stodownload?m=3c5d9173b23ecc025e1ec8c858142b57&amp;filekey=30440201010430302e02016e040253480420336335643931373362323365636330323565316563386338353831343262353702030f57e5040d00000004627466730000000132&amp;hy=SH&amp;storeid=2683a773b00015f7c687175f00000006e01004fb153480fd65b01e02592370&amp;ef=1&amp;bizid=1022" designerid = "" thumburl = "" encrypturl = "http://vweixinf.tc.qq.com/110/20402/stodownload?m=4bab97cabbe1706f0a603389b3529eeb&amp;filekey=30440201010430302e02016e040253480420346261623937636162626531373036663061363033333839623335323965656202030f57f0040d00000004627466730000000132&amp;hy=SH&amp;storeid=2683a773b0003cd1f687175f00000006e02004fb253480fd65b01e02592390&amp;ef=2&amp;bizid=1022" aeskey= "0d82b033625c4bf18bc5c338aeb7333e" externurl = "http://vweixinf.tc.qq.com/110/20403/stodownload?m=0064acb5c83b81eef262bb4c27a223c8&amp;filekey=30440201010430302e02016e04025348042030303634616362356338336238316565663236326262346332376132323363380203020430040d00000004627466730000000132&amp;hy=SH&amp;storeid=2683a773b0005d930687175f00000006e03004fb353480fd65b01e025923ad&amp;ef=3&amp;bizid=1022" externmd5 = "ef10f433c759c4681e3dcaecf0b0a32d" width= "640" height= "640" tpurl= "" tpauthkey= "" attachedtext= "" attachedtextcolor= "" lensid= "" emojiattr= "" linkid= "" desc= "" ></emoji>  </msg>'}, 'Status': 3, 'ImgStatus': 2, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1753418277, 'MsgSource': '<msgsource>\n\t<silence>0</silence>\n\t<membercount>62</membercount>\n\t<signature>N0_V1_va6WbeMi|v1_qUaJ85dZ</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'PushContent': '锦岚（开玩笑就退群版）在群聊中发了一个表情', 'NewMsgId': 3575243145472997453, 'MsgSeq': 871398668}
2025-07-25 12:37:34 | INFO | 收到表情消息: 消息ID:1371480695 来自:48097389945@chatroom 发送人:wxid_wlnzvr8ivgd422 MD5:3c5d9173b23ecc025e1ec8c858142b57 大小:1005541
2025-07-25 12:37:35 | DEBUG | [DoubaoImageRecognition] 缓存表情消息: 3575243145472997453
2025-07-25 12:37:43 | DEBUG | 收到消息: {'MsgId': 1533279671, 'FromUserName': {'string': '48097389945@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 49, 'Content': {'string': 'wxid_ubbh6q832tcs21:\n<?xml version="1.0"?>\n<msg>\n\t<appmsg appid="" sdkver="0">\n\t\t<title>命名 在忙</title>\n\t\t<des />\n\t\t<username />\n\t\t<action>view</action>\n\t\t<type>57</type>\n\t\t<showtype>0</showtype>\n\t\t<content />\n\t\t<url />\n\t\t<lowurl />\n\t\t<forwardflag>0</forwardflag>\n\t\t<dataurl />\n\t\t<lowdataurl />\n\t\t<contentattr>0</contentattr>\n\t\t<streamvideo>\n\t\t\t<streamvideourl />\n\t\t\t<streamvideototaltime>0</streamvideototaltime>\n\t\t\t<streamvideotitle />\n\t\t\t<streamvideowording />\n\t\t\t<streamvideoweburl />\n\t\t\t<streamvideothumburl />\n\t\t\t<streamvideoaduxinfo />\n\t\t\t<streamvideopublishid />\n\t\t</streamvideo>\n\t\t<canvasPageItem>\n\t\t\t<canvasPageXml><![CDATA[]]></canvasPageXml>\n\t\t</canvasPageItem>\n\t\t<refermsg>\n\t\t\t<type>47</type>\n\t\t\t<svrid>3645942747704821724</svrid>\n\t\t\t<fromusr>48097389945@chatroom</fromusr>\n\t\t\t<chatusr>wxid_mwil20w1e1j422</chatusr>\n\t\t\t<displayname>37.2℃</displayname>\n\t\t\t<msgsource>&lt;msgsource&gt;\n\t&lt;silence&gt;1&lt;/silence&gt;\n\t&lt;membercount&gt;62&lt;/membercount&gt;\n\t&lt;signature&gt;N0_V1_xXl8bmqz|v1_aq3DJzB0&lt;/signature&gt;\n\t&lt;tmp_node&gt;\n\t\t&lt;publisher-id&gt;&lt;/publisher-id&gt;\n\t&lt;/tmp_node&gt;\n&lt;/msgsource&gt;\n</msgsource>\n\t\t\t<content>&lt;msg&gt;&lt;emoji fromusername="wxid_mwil20w1e1j422" tousername="48097389945@chatroom" type="1" idbuffer="media*#*0_0" md5="8c64bc68b8be8db62a3357994429fa51" len="6702575" productid="" androidmd5="8c64bc68b8be8db62a3357994429fa51" androidlen="6702575" s60v3md5="8c64bc68b8be8db62a3357994429fa51" s60v3len="6702575" s60v5md5="8c64bc68b8be8db62a3357994429fa51" s60v5len="6702575" cdnurl="http*#*//wxapp.tc.qq.com/262/20304/stodownload?m=8c64bc68b8be8db62a3357994429fa51&amp;amp;filekey=30350201010421301f020201060402535a04108c64bc68b8be8db62a3357994429fa5102036645ef040d00000004627466730000000131&amp;amp;hy=SZ&amp;amp;storeid=32303231303632363231303831363030303362333630383235633739373938623338356630393030303030313036&amp;amp;bizid=1023" designerid="" thumburl="" encrypturl="http*#*//wxapp.tc.qq.com/262/20304/stodownload?m=1e8ab009fb261dc5990dfc97c944efa9&amp;amp;filekey=30350201010421301f020201060402535a04101e8ab009fb261dc5990dfc97c944efa902036645f0040d00000004627466730000000131&amp;amp;hy=SZ&amp;amp;storeid=32303231303632363231303831393030306231623530383235633739373961313536353830393030303030313036&amp;amp;bizid=1023" aeskey="6326e3d2860a28da9bcedda073ba2b24" externurl="http*#*//wxapp.tc.qq.com/262/20304/stodownload?m=89b530ddd215dcaa02afef959a83e5a1&amp;amp;filekey=30350201010421301f020201060402535a041089b530ddd215dcaa02afef959a83e5a1020303bfd0040d00000004627466730000000131&amp;amp;hy=SZ&amp;amp;storeid=32303231303632363231303832303030306363353831383235633739373934643461356630393030303030313036&amp;amp;bizid=1023" externmd5="f58be750820839aff3fd1a3cf3ab1e88" width="300" height="300" tpurl="" tpauthkey="" attachedtext="在忙" attachedtextcolor="FFFFFF" lensid="" emojiattr="CgblnKjlv5k=" linkid="" desc="" activityid="Selfie*#*bc466285059c0e000e438a42d6489ed1"&gt;&lt;/emoji&gt;&lt;/msg&gt;:0\n</content>\n\t\t\t<strid />\n\t\t\t<createtime>1753418217</createtime>\n\t\t</refermsg>\n\t\t<appattach>\n\t\t\t<totallen>0</totallen>\n\t\t\t<attachid />\n\t\t\t<cdnattachurl />\n\t\t\t<emoticonmd5 />\n\t\t\t<aeskey />\n\t\t\t<fileext />\n\t\t\t<islargefilemsg>0</islargefilemsg>\n\t\t</appattach>\n\t\t<extinfo />\n\t\t<androidsource>0</androidsource>\n\t\t<thumburl />\n\t\t<mediatagname />\n\t\t<messageaction><![CDATA[]]></messageaction>\n\t\t<messageext><![CDATA[]]></messageext>\n\t\t<emoticongift>\n\t\t\t<packageflag>0</packageflag>\n\t\t\t<packageid />\n\t\t</emoticongift>\n\t\t<emoticonshared>\n\t\t\t<packageflag>0</packageflag>\n\t\t\t<packageid />\n\t\t</emoticonshared>\n\t\t<designershared>\n\t\t\t<designeruin>0</designeruin>\n\t\t\t<designername>null</designername>\n\t\t\t<designerrediretcturl><![CDATA[null]]></designerrediretcturl>\n\t\t</designershared>\n\t\t<emotionpageshared>\n\t\t\t<tid>0</tid>\n\t\t\t<title>null</title>\n\t\t\t<desc>null</desc>\n\t\t\t<iconUrl><![CDATA[null]]></iconUrl>\n\t\t\t<secondUrl>null</secondUrl>\n\t\t\t<pageType>0</pageType>\n\t\t\t<setKey>null</setKey>\n\t\t</emotionpageshared>\n\t\t<webviewshared>\n\t\t\t<shareUrlOriginal />\n\t\t\t<shareUrlOpen />\n\t\t\t<jsAppId />\n\t\t\t<publisherId />\n\t\t\t<publisherReqId />\n\t\t</webviewshared>\n\t\t<template_id />\n\t\t<md5 />\n\t\t<websearch>\n\t\t\t<rec_category>0</rec_category>\n\t\t\t<channelId>0</channelId>\n\t\t</websearch>\n\t\t<weappinfo>\n\t\t\t<username />\n\t\t\t<appid />\n\t\t\t<appservicetype>0</appservicetype>\n\t\t\t<secflagforsinglepagemode>0</secflagforsinglepagemode>\n\t\t\t<videopageinfo>\n\t\t\t\t<thumbwidth>0</thumbwidth>\n\t\t\t\t<thumbheight>0</thumbheight>\n\t\t\t\t<fromopensdk>0</fromopensdk>\n\t\t\t</videopageinfo>\n\t\t</weappinfo>\n\t\t<statextstr />\n\t\t<musicShareItem>\n\t\t\t<musicDuration>0</musicDuration>\n\t\t</musicShareItem>\n\t\t<finderLiveProductShare>\n\t\t\t<finderLiveID><![CDATA[]]></finderLiveID>\n\t\t\t<finderUsername><![CDATA[]]></finderUsername>\n\t\t\t<finderObjectID><![CDATA[]]></finderObjectID>\n\t\t\t<finderNonceID><![CDATA[]]></finderNonceID>\n\t\t\t<liveStatus><![CDATA[]]></liveStatus>\n\t\t\t<appId><![CDATA[]]></appId>\n\t\t\t<pagePath><![CDATA[]]></pagePath>\n\t\t\t<productId><![CDATA[]]></productId>\n\t\t\t<coverUrl><![CDATA[]]></coverUrl>\n\t\t\t<productTitle><![CDATA[]]></productTitle>\n\t\t\t<marketPrice><![CDATA[0]]></marketPrice>\n\t\t\t<sellingPrice><![CDATA[0]]></sellingPrice>\n\t\t\t<platformHeadImg><![CDATA[]]></platformHeadImg>\n\t\t\t<platformName><![CDATA[]]></platformName>\n\t\t\t<shopWindowId><![CDATA[]]></shopWindowId>\n\t\t\t<flashSalePrice><![CDATA[0]]></flashSalePrice>\n\t\t\t<flashSaleEndTime><![CDATA[0]]></flashSaleEndTime>\n\t\t\t<ecSource><![CDATA[]]></ecSource>\n\t\t\t<sellingPriceWording><![CDATA[]]></sellingPriceWording>\n\t\t\t<platformIconURL><![CDATA[]]></platformIconURL>\n\t\t\t<firstProductTagURL><![CDATA[]]></firstProductTagURL>\n\t\t\t<firstProductTagAspectRatioString><![CDATA[0.0]]></firstProductTagAspectRatioString>\n\t\t\t<secondProductTagURL><![CDATA[]]></secondProductTagURL>\n\t\t\t<secondProductTagAspectRatioString><![CDATA[0.0]]></secondProductTagAspectRatioString>\n\t\t\t<firstGuaranteeWording><![CDATA[]]></firstGuaranteeWording>\n\t\t\t<secondGuaranteeWording><![CDATA[]]></secondGuaranteeWording>\n\t\t\t<thirdGuaranteeWording><![CDATA[]]></thirdGuaranteeWording>\n\t\t\t<isPriceBeginShow>false</isPriceBeginShow>\n\t\t\t<lastGMsgID><![CDATA[]]></lastGMsgID>\n\t\t\t<promoterKey><![CDATA[]]></promoterKey>\n\t\t\t<discountWording><![CDATA[]]></discountWording>\n\t\t\t<priceSuffixDescription><![CDATA[]]></priceSuffixDescription>\n\t\t\t<productCardKey><![CDATA[]]></productCardKey>\n\t\t\t<isWxShop><![CDATA[]]></isWxShop>\n\t\t\t<brandIconUrl><![CDATA[]]></brandIconUrl>\n\t\t\t<showBoxItemStringList />\n\t\t</finderLiveProductShare>\n\t\t<finderOrder>\n\t\t\t<appID><![CDATA[]]></appID>\n\t\t\t<orderID><![CDATA[]]></orderID>\n\t\t\t<path><![CDATA[]]></path>\n\t\t\t<priceWording><![CDATA[]]></priceWording>\n\t\t\t<stateWording><![CDATA[]]></stateWording>\n\t\t\t<productImageURL><![CDATA[]]></productImageURL>\n\t\t\t<products><![CDATA[]]></products>\n\t\t\t<productsCount><![CDATA[0]]></productsCount>\n\t\t\t<orderType><![CDATA[0]]></orderType>\n\t\t\t<newPriceWording><![CDATA[]]></newPriceWording>\n\t\t\t<newStateWording><![CDATA[]]></newStateWording>\n\t\t\t<useNewWording><![CDATA[0]]></useNewWording>\n\t\t</finderOrder>\n\t\t<finderShopWindowShare>\n\t\t\t<finderUsername><![CDATA[]]></finderUsername>\n\t\t\t<avatar><![CDATA[]]></avatar>\n\t\t\t<nickname><![CDATA[]]></nickname>\n\t\t\t<commodityInStockCount><![CDATA[]]></commodityInStockCount>\n\t\t\t<appId><![CDATA[]]></appId>\n\t\t\t<path><![CDATA[]]></path>\n\t\t\t<appUsername><![CDATA[]]></appUsername>\n\t\t\t<query><![CDATA[]]></query>\n\t\t\t<liteAppId><![CDATA[]]></liteAppId>\n\t\t\t<liteAppPath><![CDATA[]]></liteAppPath>\n\t\t\t<liteAppQuery><![CDATA[]]></liteAppQuery>\n\t\t\t<platformTagURL><![CDATA[]]></platformTagURL>\n\t\t\t<saleWording><![CDATA[]]></saleWording>\n\t\t\t<lastGMsgID><![CDATA[]]></lastGMsgID>\n\t\t\t<profileTypeWording><![CDATA[]]></profileTypeWording>\n\t\t\t<saleWordingExtra><![CDATA[]]></saleWordingExtra>\n\t\t\t<isWxShop><![CDATA[]]></isWxShop>\n\t\t\t<platformIconUrl><![CDATA[]]></platformIconUrl>\n\t\t\t<brandIconUrl><![CDATA[]]></brandIconUrl>\n\t\t\t<description><![CDATA[]]></description>\n\t\t\t<backgroundUrl><![CDATA[]]></backgroundUrl>\n\t\t\t<darkModePlatformIconUrl><![CDATA[]]></darkModePlatformIconUrl>\n\t\t\t<reputationInfo>\n\t\t\t\t<hasReputationInfo>0</hasReputationInfo>\n\t\t\t\t<reputationScore>0</reputationScore>\n\t\t\t\t<reputationWording />\n\t\t\t\t<reputationTextColor />\n\t\t\t\t<reputationLevelWording />\n\t\t\t\t<reputationBackgroundColor />\n\t\t\t</reputationInfo>\n\t\t\t<productImageURLList />\n\t\t</finderShopWindowShare>\n\t\t<findernamecard>\n\t\t\t<username />\n\t\t\t<avatar><![CDATA[]]></avatar>\n\t\t\t<nickname />\n\t\t\t<auth_job />\n\t\t\t<auth_icon>0</auth_icon>\n\t\t\t<auth_icon_url />\n\t\t\t<ecSource><![CDATA[]]></ecSource>\n\t\t\t<lastGMsgID><![CDATA[]]></lastGMsgID>\n\t\t</findernamecard>\n\t\t<finderGuarantee>\n\t\t\t<scene><![CDATA[0]]></scene>\n\t\t</finderGuarantee>\n\t\t<directshare>0</directshare>\n\t\t<gamecenter>\n\t\t\t<namecard>\n\t\t\t\t<iconUrl />\n\t\t\t\t<name />\n\t\t\t\t<desc />\n\t\t\t\t<tail />\n\t\t\t\t<jumpUrl />\n\t\t\t</namecard>\n\t\t</gamecenter>\n\t\t<patMsg>\n\t\t\t<chatUser />\n\t\t\t<records>\n\t\t\t\t<recordNum>0</recordNum>\n\t\t\t</records>\n\t\t</patMsg>\n\t\t<secretmsg>\n\t\t\t<issecretmsg>0</issecretmsg>\n\t\t</secretmsg>\n\t\t<referfromscene>0</referfromscene>\n\t\t<gameshare>\n\t\t\t<liteappext>\n\t\t\t\t<liteappbizdata />\n\t\t\t\t<priority>0</priority>\n\t\t\t</liteappext>\n\t\t\t<appbrandext>\n\t\t\t\t<litegameinfo />\n\t\t\t\t<priority>-1</priority>\n\t\t\t</appbrandext>\n\t\t\t<gameshareid />\n\t\t\t<sharedata />\n\t\t\t<isvideo>0</isvideo>\n\t\t\t<duration>-1</duration>\n\t\t\t<isexposed>0</isexposed>\n\t\t\t<readtext />\n\t\t</gameshare>\n\t\t<mpsharetrace>\n\t\t\t<hasfinderelement>0</hasfinderelement>\n\t\t\t<lastgmsgid />\n\t\t</mpsharetrace>\n\t\t<wxgamecard>\n\t\t\t<framesetname />\n\t\t\t<mbcarddata />\n\t\t\t<minpkgversion />\n\t\t\t<clientextinfo />\n\t\t\t<mbcardheight>0</mbcardheight>\n\t\t\t<isoldversion>0</isoldversion>\n\t\t</wxgamecard>\n\t\t<liteapp>\n\t\t\t<id>null</id>\n\t\t\t<path />\n\t\t\t<query />\n\t\t\t<istransparent>0</istransparent>\n\t\t\t<hideicon>0</hideicon>\n\t\t</liteapp>\n\t\t<opensdk_share_is_modified>0</opensdk_share_is_modified>\n\t</appmsg>\n\t<fromusername>wxid_ubbh6q832tcs21</fromusername>\n\t<scene>0</scene>\n\t<appinfo>\n\t\t<version>1</version>\n\t\t<appname></appname>\n\t</appinfo>\n\t<commenturl></commenturl>\n</msg>\n'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1753418285, 'MsgSource': '<msgsource>\n\t<alnode>\n\t\t<fr>4</fr>\n\t</alnode>\n\t<eggIncluded>1</eggIncluded>\n\t<sec_msg_node>\n\t\t<uuid>3386a68b3e55c46ad8dac3e766b53e0e_</uuid>\n\t\t<risk-file-flag />\n\t\t<risk-file-md5-list />\n\t</sec_msg_node>\n\t<silence>0</silence>\n\t<membercount>62</membercount>\n\t<signature>N0_V1_GAH0a61B|v1_nhAT2V4j</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'PushContent': '郭 : 命名 在忙', 'NewMsgId': 3929929768222984354, 'MsgSeq': 871398669}
2025-07-25 12:37:43 | DEBUG | 从群聊消息中提取发送者: wxid_ubbh6q832tcs21
2025-07-25 12:37:43 | DEBUG | 使用已解析的XML处理引用消息
2025-07-25 12:37:43 | INFO | 收到引用消息: 消息ID:1533279671 来自:48097389945@chatroom 发送人:wxid_ubbh6q832tcs21 内容:命名 在忙 引用类型:47
2025-07-25 12:37:43 | INFO | [DouBaoImageToImage] 收到引用消息: 命名 在忙
2025-07-25 12:37:43 | ERROR | 解析表情大小失败: junk after document element: line 1, column 1738
2025-07-25 12:37:43 | INFO | 成功保存表情映射文件，共 539 条记录
2025-07-25 12:37:43 | INFO | 成功保存表情映射文件，共 539 条记录
2025-07-25 12:37:43 | INFO | 发送文字消息: 对方wxid:48097389945@chatroom at: 内容:表情已添加触发词：在忙
2025-07-25 12:37:43 | INFO | [TimerTask] 收到引用消息调试信息:
2025-07-25 12:37:43 | INFO |   - 消息内容: 命名 在忙
2025-07-25 12:37:43 | INFO |   - 群组ID: 48097389945@chatroom
2025-07-25 12:37:43 | INFO |   - 发送人: wxid_ubbh6q832tcs21
2025-07-25 12:37:43 | INFO |   - 引用信息: {'MsgType': 47, 'Content': '<msg><emoji fromusername="wxid_mwil20w1e1j422" tousername="48097389945@chatroom" type="1" idbuffer="media*#*0_0" md5="8c64bc68b8be8db62a3357994429fa51" len="6702575" productid="" androidmd5="8c64bc68b8be8db62a3357994429fa51" androidlen="6702575" s60v3md5="8c64bc68b8be8db62a3357994429fa51" s60v3len="6702575" s60v5md5="8c64bc68b8be8db62a3357994429fa51" s60v5len="6702575" cdnurl="http*#*//wxapp.tc.qq.com/262/20304/stodownload?m=8c64bc68b8be8db62a3357994429fa51&amp;filekey=30350201010421301f020201060402535a04108c64bc68b8be8db62a3357994429fa5102036645ef040d00000004627466730000000131&amp;hy=SZ&amp;storeid=32303231303632363231303831363030303362333630383235633739373938623338356630393030303030313036&amp;bizid=1023" designerid="" thumburl="" encrypturl="http*#*//wxapp.tc.qq.com/262/20304/stodownload?m=1e8ab009fb261dc5990dfc97c944efa9&amp;filekey=30350201010421301f020201060402535a04101e8ab009fb261dc5990dfc97c944efa902036645f0040d00000004627466730000000131&amp;hy=SZ&amp;storeid=32303231303632363231303831393030306231623530383235633739373961313536353830393030303030313036&amp;bizid=1023" aeskey="6326e3d2860a28da9bcedda073ba2b24" externurl="http*#*//wxapp.tc.qq.com/262/20304/stodownload?m=89b530ddd215dcaa02afef959a83e5a1&amp;filekey=30350201010421301f020201060402535a041089b530ddd215dcaa02afef959a83e5a1020303bfd0040d00000004627466730000000131&amp;hy=SZ&amp;storeid=32303231303632363231303832303030306363353831383235633739373934643461356630393030303030313036&amp;bizid=1023" externmd5="f58be750820839aff3fd1a3cf3ab1e88" width="300" height="300" tpurl="" tpauthkey="" attachedtext="在忙" attachedtextcolor="FFFFFF" lensid="" emojiattr="CgblnKjlv5k=" linkid="" desc="" activityid="Selfie*#*bc466285059c0e000e438a42d6489ed1"></emoji></msg>:0\n', 'Msgid': '3645942747704821724', 'NewMsgId': '3645942747704821724', 'ToWxid': 'wxid_4usgcju5ey9q29', 'FromWxid': '48097389945@chatroom', 'Nickname': '37.2℃', 'MsgSource': '<msgsource>\n\t<silence>1</silence>\n\t<membercount>62</membercount>\n\t<signature>N0_V1_xXl8bmqz|v1_aq3DJzB0</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'Createtime': '1753418217', 'SenderWxid': 'wxid_ubbh6q832tcs21'}
2025-07-25 12:37:43 | INFO |   - 引用消息ID: 
2025-07-25 12:37:43 | INFO |   - 引用消息类型: 
2025-07-25 12:37:43 | INFO |   - 引用消息内容: <msg><emoji fromusername="wxid_mwil20w1e1j422" tousername="48097389945@chatroom" type="1" idbuffer="media*#*0_0" md5="8c64bc68b8be8db62a3357994429fa51" len="6702575" productid="" androidmd5="8c64bc68b8be8db62a3357994429fa51" androidlen="6702575" s60v3md5="8c64bc68b8be8db62a3357994429fa51" s60v3len="6702575" s60v5md5="8c64bc68b8be8db62a3357994429fa51" s60v5len="6702575" cdnurl="http*#*//wxapp.tc.qq.com/262/20304/stodownload?m=8c64bc68b8be8db62a3357994429fa51&amp;filekey=30350201010421301f020201060402535a04108c64bc68b8be8db62a3357994429fa5102036645ef040d00000004627466730000000131&amp;hy=SZ&amp;storeid=32303231303632363231303831363030303362333630383235633739373938623338356630393030303030313036&amp;bizid=1023" designerid="" thumburl="" encrypturl="http*#*//wxapp.tc.qq.com/262/20304/stodownload?m=1e8ab009fb261dc5990dfc97c944efa9&amp;filekey=30350201010421301f020201060402535a04101e8ab009fb261dc5990dfc97c944efa902036645f0040d00000004627466730000000131&amp;hy=SZ&amp;storeid=32303231303632363231303831393030306231623530383235633739373961313536353830393030303030313036&amp;bizid=1023" aeskey="6326e3d2860a28da9bcedda073ba2b24" externurl="http*#*//wxapp.tc.qq.com/262/20304/stodownload?m=89b530ddd215dcaa02afef959a83e5a1&amp;filekey=30350201010421301f020201060402535a041089b530ddd215dcaa02afef959a83e5a1020303bfd0040d00000004627466730000000131&amp;hy=SZ&amp;storeid=32303231303632363231303832303030306363353831383235633739373934643461356630393030303030313036&amp;bizid=1023" externmd5="f58be750820839aff3fd1a3cf3ab1e88" width="300" height="300" tpurl="" tpauthkey="" attachedtext="在忙" attachedtextcolor="FFFFFF" lensid="" emojiattr="CgblnKjlv5k=" linkid="" desc="" activityid="Selfie*#*bc466285059c0e000e438a42d6489ed1"></emoji></msg>:0

2025-07-25 12:37:43 | INFO |   - 引用消息发送人: wxid_ubbh6q832tcs21
2025-07-25 12:37:45 | DEBUG | 收到消息: {'MsgId': 2147284755, 'FromUserName': {'string': '48097389945@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 1, 'Content': {'string': 'wxid_cwxauba42ki122:\n好久没喝奶茶了，有没有人请'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1753418288, 'MsgSource': '<msgsource>\n\t<pua>1</pua>\n\t<alnode>\n\t\t<cf>2</cf>\n\t\t<inlenlist>13</inlenlist>\n\t</alnode>\n\t<silence>0</silence>\n\t<membercount>62</membercount>\n\t<signature>N0_V1_UC4laB9/|v1_ShP7iyrQ</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'PushContent': 'ن初心ᯤ⁶ᴳ คิดถึงن : 好久没喝奶茶了，有没有人请', 'NewMsgId': 7646242783691892356, 'MsgSeq': 871398672}
2025-07-25 12:37:45 | INFO | 收到文本消息: 消息ID:2147284755 来自:48097389945@chatroom 发送人:wxid_cwxauba42ki122 @:[] 内容:好久没喝奶茶了，有没有人请
2025-07-25 12:37:46 | DEBUG | 处理消息内容: '好久没喝奶茶了，有没有人请'
2025-07-25 12:37:46 | DEBUG | 消息内容 '好久没喝奶茶了，有没有人请' 不匹配任何命令，忽略
2025-07-25 12:37:50 | DEBUG | 收到消息: {'MsgId': 336704137, 'FromUserName': {'string': '48097389945@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 1, 'Content': {'string': 'wxid_ubbh6q832tcs21:\n在忙'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1753418292, 'MsgSource': '<msgsource>\n\t<pua>1</pua>\n\t<eggIncluded>1</eggIncluded>\n\t<silence>0</silence>\n\t<membercount>62</membercount>\n\t<signature>N0_V1_LBbvlghT|v1_ab/jGKte</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'PushContent': '郭 : 在忙', 'NewMsgId': 204428306395959721, 'MsgSeq': 871398673}
2025-07-25 12:37:50 | INFO | 收到文本消息: 消息ID:336704137 来自:48097389945@chatroom 发送人:wxid_ubbh6q832tcs21 @:[] 内容:在忙
2025-07-25 12:37:51 | INFO | 发送表情消息: 对方wxid:48097389945@chatroom md5:8c64bc68b8be8db62a3357994429fa51 总长度:9992069
2025-07-25 12:37:51 | DEBUG | 处理消息内容: '在忙'
2025-07-25 12:37:51 | DEBUG | 消息内容 '在忙' 不匹配任何命令，忽略
2025-07-25 12:37:55 | DEBUG | 收到消息: {'MsgId': 1678045947, 'FromUserName': {'string': '48097389945@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 1, 'Content': {'string': 'wxid_l9koi6kli78i22:\n在忙'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1753418298, 'MsgSource': '<msgsource>\n\t<pua>1</pua>\n\t<silence>0</silence>\n\t<membercount>62</membercount>\n\t<signature>N0_V1_UcBAUhun|v1_kPy6PO7j</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'PushContent': '十五 : 在忙', 'NewMsgId': 3466302315639959051, 'MsgSeq': 871398676}
2025-07-25 12:37:55 | INFO | 收到文本消息: 消息ID:1678045947 来自:48097389945@chatroom 发送人:wxid_l9koi6kli78i22 @:[] 内容:在忙
2025-07-25 12:37:56 | INFO | 发送表情消息: 对方wxid:48097389945@chatroom md5:8c64bc68b8be8db62a3357994429fa51 总长度:9992069
2025-07-25 12:37:56 | DEBUG | 处理消息内容: '在忙'
2025-07-25 12:37:56 | DEBUG | 消息内容 '在忙' 不匹配任何命令，忽略
2025-07-25 12:38:00 | DEBUG | 收到消息: {'MsgId': 1601153296, 'FromUserName': {'string': '48097389945@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 1, 'Content': {'string': 'wxid_ubbh6q832tcs21:\n删除表情 在忙'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1753418303, 'MsgSource': '<msgsource>\n\t<pua>1</pua>\n\t<eggIncluded>1</eggIncluded>\n\t<silence>0</silence>\n\t<membercount>62</membercount>\n\t<signature>N0_V1_KgnwnBO/|v1_RioLtg5t</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'PushContent': '郭 : 删除表情 在忙', 'NewMsgId': 3932797105666471974, 'MsgSeq': 871398679}
2025-07-25 12:38:01 | INFO | 收到文本消息: 消息ID:1601153296 来自:48097389945@chatroom 发送人:wxid_ubbh6q832tcs21 @:[] 内容:删除表情 在忙
2025-07-25 12:38:01 | INFO | 成功保存表情映射文件，共 539 条记录
2025-07-25 12:38:01 | INFO | 发送文字消息: 对方wxid:48097389945@chatroom at: 内容:已删除表情触发词：在忙
2025-07-25 12:38:01 | DEBUG | 处理消息内容: '删除表情 在忙'
2025-07-25 12:38:01 | DEBUG | 消息内容 '删除表情 在忙' 不匹配任何命令，忽略
2025-07-25 12:38:04 | DEBUG | 收到消息: {'MsgId': 852172293, 'FromUserName': {'string': '48097389945@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 1, 'Content': {'string': 'wxid_ubbh6q832tcs21:\n[捂脸]'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1753418306, 'MsgSource': '<msgsource>\n\t<pua>1</pua>\n\t<eggIncluded>1</eggIncluded>\n\t<silence>0</silence>\n\t<membercount>62</membercount>\n\t<signature>N0_V1_iCyzWEUZ|v1_2zX1PeBC</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'PushContent': '郭 : [捂脸]', 'NewMsgId': 2064759476218033862, 'MsgSeq': 871398682}
2025-07-25 12:38:04 | INFO | 收到表情消息: 消息ID:852172293 来自:48097389945@chatroom 发送人:wxid_ubbh6q832tcs21 @:[] 内容:[捂脸]
2025-07-25 12:38:04 | DEBUG | [DoubaoImageRecognition] 缓存表情消息: 2064759476218033862
2025-07-25 12:38:14 | DEBUG | 收到消息: {'MsgId': 1958645820, 'FromUserName': {'string': '48097389945@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 1, 'Content': {'string': 'wxid_wlnzvr8ivgd422:\n我的表情'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1753418316, 'MsgSource': '<msgsource>\n\t<pua>1</pua>\n\t<eggIncluded>1</eggIncluded>\n\t<silence>0</silence>\n\t<membercount>62</membercount>\n\t<signature>N0_V1_G1DGT/Cv|v1_j50TNOHT</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'PushContent': '锦岚（开玩笑就退群版） : 我的表情', 'NewMsgId': 5033860387977738947, 'MsgSeq': 871398683}
2025-07-25 12:38:14 | INFO | 收到文本消息: 消息ID:1958645820 来自:48097389945@chatroom 发送人:wxid_wlnzvr8ivgd422 @:[] 内容:我的表情
2025-07-25 12:38:14 | INFO | 使用默认头部图片: data\default picture\wodebiaoqing.jpg
2025-07-25 12:38:27 | INFO | 发送图片消息: 对方wxid:48097389945@chatroom 图片base64略
2025-07-25 12:39:27 | DEBUG | 处理消息内容: '我的表情'
2025-07-25 12:39:27 | DEBUG | 消息内容 '我的表情' 不匹配任何命令，忽略
2025-07-25 12:39:28 | DEBUG | 收到消息: {'MsgId': 469574238, 'FromUserName': {'string': '48097389945@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 1, 'Content': {'string': 'wxid_l9koi6kli78i22:\n在忙'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1753418324, 'MsgSource': '<msgsource>\n\t<pua>1</pua>\n\t<silence>0</silence>\n\t<membercount>62</membercount>\n\t<signature>N0_V1_M0+MVj//|v1_HW1N1/xE</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'PushContent': '十五 : 在忙', 'NewMsgId': 966555958863980531, 'MsgSeq': 871398684}
2025-07-25 12:39:28 | INFO | 收到文本消息: 消息ID:469574238 来自:48097389945@chatroom 发送人:wxid_l9koi6kli78i22 @:[] 内容:在忙
2025-07-25 12:39:29 | DEBUG | 处理消息内容: '在忙'
2025-07-25 12:39:29 | DEBUG | 消息内容 '在忙' 不匹配任何命令，忽略
2025-07-25 12:39:31 | DEBUG | 收到消息: {'MsgId': 487139922, 'FromUserName': {'string': '48097389945@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 47, 'Content': {'string': 'wxid_l9koi6kli78i22:\n<msg><emoji fromusername="wxid_l9koi6kli78i22" tousername="48097389945@chatroom" type="1" idbuffer="media:0_0" md5="5d4393c47fcbbd39d215e1595682279a" len="4592" productid="" androidmd5="5d4393c47fcbbd39d215e1595682279a" androidlen="4592" s60v3md5="5d4393c47fcbbd39d215e1595682279a" s60v3len="4592" s60v5md5="5d4393c47fcbbd39d215e1595682279a" s60v5len="4592" cdnurl="http://wxapp.tc.qq.com/262/20304/stodownload?m=5d4393c47fcbbd39d215e1595682279a&amp;filekey=30340201010420301e020201060402534804105d4393c47fcbbd39d215e1595682279a020211f0040d00000004627466730000000132&amp;hy=SH&amp;storeid=263113b7f0001f142000000000000010600004f5053482ec67b40b69408507&amp;bizid=1023" designerid="" thumburl="" encrypturl="http://wxapp.tc.qq.com/262/20304/stodownload?m=22ec94802490ba6a234ff7a2c64980d2&amp;filekey=30340201010420301e0202010604025348041022ec94802490ba6a234ff7a2c64980d202021200040d00000004627466730000000132&amp;hy=SH&amp;storeid=263113b7f00041dac000000000000010600004f5053481f267b40b69317c49&amp;bizid=1023" aeskey="71f906d3550df302ed1af247cb823ee5" externurl="" externmd5="" width="170" height="72" tpurl="" tpauthkey="" attachedtext="" attachedtextcolor="" lensid="" emojiattr="" linkid="" desc=""></emoji></msg>'}, 'Status': 3, 'ImgStatus': 2, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1753418329, 'MsgSource': '<msgsource>\n\t<silence>0</silence>\n\t<membercount>62</membercount>\n\t<signature>N0_V1_IpNk2+T6|v1_3Dw7tVin</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'PushContent': '十五在群聊中发了一个表情', 'NewMsgId': 2702038452135399724, 'MsgSeq': 871398686}
2025-07-25 12:39:31 | INFO | 收到表情消息: 消息ID:487139922 来自:48097389945@chatroom 发送人:wxid_l9koi6kli78i22 MD5:5d4393c47fcbbd39d215e1595682279a 大小:4592
2025-07-25 12:39:31 | DEBUG | [DoubaoImageRecognition] 缓存表情消息: 2702038452135399724
2025-07-25 12:39:31 | DEBUG | 收到消息: {'MsgId': 967990539, 'FromUserName': {'string': '48097389945@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 1, 'Content': {'string': 'wxid_ubbh6q832tcs21:\n别发我的表情了，还没修'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1753418329, 'MsgSource': '<msgsource>\n\t<pua>1</pua>\n\t<eggIncluded>1</eggIncluded>\n\t<silence>0</silence>\n\t<membercount>62</membercount>\n\t<signature>N0_V1_f3tiZp4D|v1_VrvK1B4g</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'PushContent': '郭 : 别发我的表情了，还没修', 'NewMsgId': 6762222825107673802, 'MsgSeq': 871398688}
2025-07-25 12:39:31 | INFO | 收到文本消息: 消息ID:967990539 来自:48097389945@chatroom 发送人:wxid_ubbh6q832tcs21 @:[] 内容:别发我的表情了，还没修
2025-07-25 12:39:32 | DEBUG | 处理消息内容: '别发我的表情了，还没修'
2025-07-25 12:39:32 | DEBUG | 消息内容 '别发我的表情了，还没修' 不匹配任何命令，忽略
2025-07-25 12:39:32 | DEBUG | 收到消息: {'MsgId': 1945570779, 'FromUserName': {'string': '48097389945@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 1, 'Content': {'string': 'wxid_ubbh6q832tcs21:\n又堵塞了'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1753418335, 'MsgSource': '<msgsource>\n\t<pua>1</pua>\n\t<eggIncluded>1</eggIncluded>\n\t<silence>0</silence>\n\t<membercount>62</membercount>\n\t<signature>N0_V1_Vm2R/eJe|v1_pylv6IHp</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'PushContent': '郭 : 又堵塞了', 'NewMsgId': 8667851236048216753, 'MsgSeq': 871398689}
2025-07-25 12:39:32 | INFO | 收到文本消息: 消息ID:1945570779 来自:48097389945@chatroom 发送人:wxid_ubbh6q832tcs21 @:[] 内容:又堵塞了
2025-07-25 12:39:32 | DEBUG | 处理消息内容: '又堵塞了'
2025-07-25 12:39:32 | DEBUG | 消息内容 '又堵塞了' 不匹配任何命令，忽略
2025-07-25 12:39:33 | DEBUG | 收到消息: {'MsgId': 960205104, 'FromUserName': {'string': '48097389945@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 1, 'Content': {'string': 'wxid_wlnzvr8ivgd422:\n亮不亮'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1753418342, 'MsgSource': '<msgsource>\n\t<pua>1</pua>\n\t<eggIncluded>1</eggIncluded>\n\t<silence>0</silence>\n\t<membercount>62</membercount>\n\t<signature>N0_V1_mdOqxGa9|v1_l3hvkfJd</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'PushContent': '锦岚（开玩笑就退群版） : 亮不亮', 'NewMsgId': 3405451962316517408, 'MsgSeq': 871398690}
2025-07-25 12:39:33 | INFO | 收到文本消息: 消息ID:960205104 来自:48097389945@chatroom 发送人:wxid_wlnzvr8ivgd422 @:[] 内容:亮不亮
2025-07-25 12:39:33 | INFO | 发送表情消息: 对方wxid:48097389945@chatroom md5:3dbafb066d773b2c3f41dd01649f0187 总长度:9992069
2025-07-25 12:39:33 | DEBUG | 处理消息内容: '亮不亮'
2025-07-25 12:39:33 | DEBUG | 消息内容 '亮不亮' 不匹配任何命令，忽略
2025-07-25 12:39:35 | DEBUG | 收到消息: {'MsgId': 2077455327, 'FromUserName': {'string': '48097389945@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 47, 'Content': {'string': 'wxid_wlnzvr8ivgd422:\n<msg><emoji fromusername = "wxid_wlnzvr8ivgd422" tousername = "48097389945@chatroom" type="1" idbuffer="media:0_0" md5="d344c9d48f4ae172e055d7ee4d7200e3" len = "47846" productid="com.tencent.xin.emoticon.person.stiker_1550953644153db8b79ef30087" androidmd5="d344c9d48f4ae172e055d7ee4d7200e3" androidlen="47846" s60v3md5 = "d344c9d48f4ae172e055d7ee4d7200e3" s60v3len="47846" s60v5md5 = "d344c9d48f4ae172e055d7ee4d7200e3" s60v5len="47846" cdnurl = "http://wxapp.tc.qq.com/262/20304/stodownload?m=d344c9d48f4ae172e055d7ee4d7200e3&amp;filekey=30350201010421301f02020106040253480410d344c9d48f4ae172e055d7ee4d7200e3020300bae6040d00000004627466730000000132&amp;hy=SH&amp;storeid=2631fce340003e2b2000000000000010600004f5053482d664b40b78bb313f&amp;bizid=1023" designerid = "" thumburl = "http://wxapp.tc.qq.com/275/20304/stodownload?m=519e3c6861ad06f59d43b2cce8eb519e&amp;filekey=30340201010420301e02020113040253480410519e3c6861ad06f59d43b2cce8eb519e02023723040d00000004627466730000000132&amp;hy=SH&amp;storeid=26479bd13000bc984000000000000011300004f5053480188db01e6f5529a0&amp;bizid=1023" encrypturl = "http://wxapp.tc.qq.com/262/20304/stodownload?m=e52316dec65af5db50a4d115b3ffc357&amp;filekey=30350201010421301f02020106040253480410e52316dec65af5db50a4d115b3ffc357020300baf0040d00000004627466730000000132&amp;hy=SH&amp;storeid=2631fce340005ddcb000000000000010600004f50534808e67b40b78b018e5&amp;bizid=1023" aeskey= "f02dde6e76a82460b5154d8b960ae5d1" externurl = "http://wxapp.tc.qq.com/262/20304/stodownload?m=d1a008dfaca8b8adf2c5b829978bbcd3&amp;filekey=30340201010420301e02020106040253480410d1a008dfaca8b8adf2c5b829978bbcd302022420040d00000004627466730000000132&amp;hy=SH&amp;storeid=2631fce3400089606000000000000010600004f5053480c267b40b78b453d6&amp;bizid=1023" externmd5 = "e5aafa132fb56b74edcd0bfb867d77cf" width= "240" height= "240" tpurl= "" tpauthkey= "" attachedtext= "" attachedtextcolor= "" lensid= "" emojiattr= "" linkid= "" desc= "CgwKBXpoX2NuEgPlk6YKCQoFemhfdHcSAAoLCgdkZWZhdWx0EgA=" ></emoji>  </msg>'}, 'Status': 3, 'ImgStatus': 2, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1753418345, 'MsgSource': '<msgsource>\n\t<silence>0</silence>\n\t<membercount>62</membercount>\n\t<signature>N0_V1_fBjqKplh|v1_TJ37ZNNa</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'PushContent': '锦岚（开玩笑就退群版）在群聊中发了一个表情', 'NewMsgId': 6941526649635240199, 'MsgSeq': 871398691}
2025-07-25 12:39:35 | INFO | 收到表情消息: 消息ID:2077455327 来自:48097389945@chatroom 发送人:wxid_wlnzvr8ivgd422 MD5:d344c9d48f4ae172e055d7ee4d7200e3 大小:47846
2025-07-25 12:39:35 | DEBUG | [DoubaoImageRecognition] 缓存表情消息: 6941526649635240199
2025-07-25 12:39:36 | DEBUG | 收到消息: {'MsgId': 874376352, 'FromUserName': {'string': '48097389945@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 1, 'Content': {'string': 'wxid_l9koi6kli78i22:\nCPDD'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1753418347, 'MsgSource': '<msgsource>\n\t<pua>1</pua>\n\t<silence>0</silence>\n\t<membercount>62</membercount>\n\t<signature>N0_V1_7JrN8AJS|v1_fmlYWm6s</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'PushContent': '十五 : CPDD', 'NewMsgId': 8460661048342375180, 'MsgSeq': 871398692}
2025-07-25 12:39:36 | INFO | 收到文本消息: 消息ID:874376352 来自:48097389945@chatroom 发送人:wxid_l9koi6kli78i22 @:[] 内容:CPDD
