# RoboNeo插件AI询问功能优化

## 优化背景

通过对比MeituAI插件和RoboNeo插件，发现RoboNeo插件缺少处理AI询问的机制。当AI需要更多细节时（如询问具体风格、颜色等），原版插件无法正确处理用户的回答，导致用户体验不佳。

## 主要问题

1. **缺少AI询问状态管理**：无法记录AI是否在询问用户
2. **无法处理用户回答**：用户回答AI询问时，插件无法识别这是对询问的回应
3. **请求信息丢失**：AI询问时，原始请求信息会丢失，无法继续处理

## 优化方案

### 1. 添加AI询问状态管理

```python
# AI询问状态管理
self._ai_asking = {}  # 记录AI是否在询问用户
self._ai_questions = {}  # 记录AI的问题
self._pending_requests = {}  # 记录待处理的请求信息
```

### 2. 增强消息处理逻辑

在`handle_text`和`handle_quote`方法中添加AI询问状态检查：

```python
# 检查是否是AI询问状态下的回答
if self._ai_asking.get(wxid, False) and self._ai_asking[wxid] == user_wxid:
    await self._handle_ai_question_response(bot, wxid, user_wxid, content)
    return
```

### 3. 添加AI询问处理方法

新增`_handle_ai_question_response`方法：
- 获取待处理的请求信息
- 将用户回答作为补充提示词
- 重新处理图像生成请求
- 清理询问状态

### 4. 修改流式响应处理

在`_submit_generation_task`方法中添加对`request`类型消息的处理：

```python
elif message_type == 'request':
    # AI询问更多细节
    question = data.get('question', '')
    if question:
        logger.info(f"[{self.plugin_name}] AI询问: {question}")
        return {'ai_question': question, 'text_response': text_blocks}
```

### 5. 增强图像生成处理

在`_process_image_generation_without_notification`方法中添加AI询问处理：
- 检测到AI询问时，设置询问状态
- 保存待处理请求信息
- 向用户发送询问消息

## 功能特性

### 智能询问处理
- **自动检测**：自动识别AI的询问消息
- **状态管理**：准确跟踪询问状态和用户身份
- **上下文保持**：保留原始请求信息，支持增强提示词

### 用户体验优化
- **友好提示**：清晰告知用户AI正在询问
- **简单回答**：用户只需直接回答，无需特殊格式
- **状态清理**：自动清理过期或异常状态

### 错误处理
- **异常恢复**：处理过程中出现异常时自动清理状态
- **超时处理**：避免长时间占用询问状态
- **状态一致性**：确保状态管理的一致性

## 使用示例

### 基本流程
1. 用户：`美图换成泳衣` (引用图片)
2. AI：`🤖 AI询问: 您希望是什么风格的泳衣？比基尼还是连体泳衣？`
3. 用户：`比基尼`
4. 系统：自动将"换成泳衣 比基尼"作为增强提示词重新处理

### 复杂询问
1. 用户：`美图换成古装`
2. AI：`🤖 AI询问: 您希望是什么朝代的古装？唐装、汉服还是清朝服饰？`
3. 用户：`汉服，要红色的`
4. 系统：使用"换成古装 汉服，要红色的"进行处理

## 配置更新

更新了配置文件说明，添加AI交互功能介绍：

```toml
🤖 AI交互功能：
• AI可能会询问更多细节（如风格、颜色等）
• 收到AI询问时，直接回答即可
• 例如：AI问"什么风格的泳衣？" 你回答"比基尼"
```

## 技术实现

### 关键改进点
1. **状态管理**：使用字典管理多群组的询问状态
2. **消息路由**：优先处理AI询问回答，避免误触发其他功能
3. **提示词增强**：智能合并原始提示词和用户回答
4. **资源清理**：在cleanup方法中清理所有相关状态

### 兼容性
- **向后兼容**：不影响原有功能的正常使用
- **渐进增强**：新功能作为原有功能的增强，不破坏现有逻辑
- **错误隔离**：AI询问功能异常不会影响基础图像处理功能

## 总结

这次优化显著提升了RoboNeo插件的用户体验，使其能够像MeituAI插件一样智能处理AI询问。用户现在可以享受更自然、更智能的AI图像处理交互体验，AI能够主动询问细节以生成更符合用户期望的图像。
