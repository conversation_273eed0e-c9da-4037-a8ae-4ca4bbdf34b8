from WechatAPI.Server.WechatAPIServer import *
from WechatAPI.Client import *
from WechatAPI.errors import *

def is_running_in_docker():
    """
    检查当前程序是否在 Docker 容器中运行
    """
    try:
        with open('/proc/1/cgroup', 'r') as f:
            return any('docker' in line for line in f)
    except:
        # 如果无法读取文件或发生其他错误，假定不在 Docker 中运行
        return False

__name__ = "WechatAPI"
__version__ = "1.0.0"
__description__ = "Wechat API for XYBot"
__author__ = "HenryXiaoYang"

# 确保函数可以被导入
__all__ = [
    'is_running_in_docker',
    # 保留原有的其他导出
    '*'
]