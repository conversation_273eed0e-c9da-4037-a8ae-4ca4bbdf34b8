import os
import time
import json
import base64
import httpx
import hashlib
from collections import defaultdict, deque
import random
from typing import Dict, Optional, Tu<PERSON>
try:
    import tomllib
except ModuleNotFoundError:
    import tomli as tomllib
from loguru import logger
from pathlib import Path

from WechatAPI import WechatAPIClient
from utils.decorators import on_text_message, on_image_message, on_quote_message
from utils.plugin_base import PluginBase

class ImageEcho(PluginBase):
    description = "捕获并引用图片消息的插件"
    author = "Claude"
    version = "1.1.0"
    plugin_name = "ImageEcho"

    def __init__(self):
        # 必须先调用父类初始化
        super().__init__()

        # 初始化临时目录
        self.temp_dir = Path("plugins/ImageEcho/temp")
        self.temp_dir.mkdir(parents=True, exist_ok=True)

        # 初始化数据存储目录
        self.data_dir = Path("plugins/ImageEcho/data")
        self.data_dir.mkdir(parents=True, exist_ok=True)

        # 读取配置
        config = {}
        try:
            config_path = f"plugins/{self.plugin_name}/config.toml"
            if not os.path.exists(config_path):
                logger.warning(f"[{self.plugin_name}] 配置文件 {config_path} 不存在，将使用默认配置")
                config = {}
            else:
                with open(config_path, "rb") as f:
                    plugin_config = tomllib.load(f)
                config = plugin_config.get(self.plugin_name, {})
        except Exception as e:
            logger.error(f"[{self.plugin_name}] 配置文件读取失败: {str(e)}")
            config = {}

        # 基本配置初始化
        self.enable = config.get("enable", True)
        self.command = config.get("command", ["引用图片", "回显图片", "图片引用"])
        self.command_format = config.get("command-format", "引用图片 [索引号] - 引用最近保存的图片")

        # 下载图片命令
        self.download_command = config.get("download-command", ["下载图片"])

        # 测试命令
        self.test_command = config.get("test-command", ["测试下载图片"])

        # 存储设置
        storage_config = config.get("storage", {})
        self.max_images = storage_config.get("max_images", 10)

        # 图片分析配置
        analysis_config = config.get("analysis", {})
        self.analysis_enable = analysis_config.get("enable", False)
        self.analysis_command = analysis_config.get("command", ["分析图片", "解析图片"])
        self.chatglm_api = analysis_config.get("chatglm_api", {})
        self.chatglm_api_url = self.chatglm_api.get("api_url", "https://chatglm.cn/chatglm/backend-api")

        # 更新token为最新值
        self.chatglm_token = self.chatglm_api.get("token", "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJzdWIiOiI2NDI4ZDhjZWY4ZTg0NWEyOWMzMGQ5OWViZTZmN2Q2NiIsImV4cCI6MTc0Mjk3ODE0NiwibmJmIjoxNzQyODkxNzQ2LCJpYXQiOjE3NDI4OTE3NDYsImp0aSI6IjY1OGRkYjg5YzBmZjRkMWY5OTI3NTk5Nzc0MTk3N2JjIiwidWlkIjoiNjdkMTY5MWYxYTdlNjY3Yjg1YTI3YzMwIiwidHlwZSI6ImFjY2VzcyJ9.QMWRXmzcZEVYAdvEt0J_INLH2_clq9BJRkD8k0QKHA8")
        self.chatglm_assistant_id = self.chatglm_api.get("assistant_id", "65940acff94777010aa6b796")

        # 存储图片信息的数据结构
        # 格式: {群聊ID: deque([图片信息1, 图片信息2, ...], maxlen=max_images)}
        self.image_store = defaultdict(lambda: deque(maxlen=self.max_images))

        # 用户限流字典
        self.user_last_request = {}

        # 自然化响应配置
        self.natural_response = config.get("natural_response", True)
        self._init_natural_responses()

        # 加载持久化数据
        self._load_image_data()

    def _init_natural_responses(self):
        """初始化自然化回复词库"""
        self.confirm_msgs = [
            "好的", "收到", "明白了", "嗯", "知道了", "行", "OK"
        ]

        self.error_msgs = [
            "这可把我难住了", "搞不定了", "这个有点难", "弄不了",
            "出问题了", "搞不出来", "这个不行"
        ]

        self.rate_limit_msgs = [
            "你搁这刷bug呢", "慢点慢点", "别这么急", "一个一个来",
            "太快了太快了", "歇会儿", "别刷了"
        ]

    async def _simple_confirm(self, bot, wxid):
        """简单确认回复，不艾特人"""
        if self.natural_response:
            confirm_msg = random.choice(self.confirm_msgs)
            await bot.send_text_message(wxid, confirm_msg)

    def _get_storage_path(self, wxid: str) -> Path:
        """获取群聊图片数据的存储路径"""
        # 使用MD5生成稳定的文件名，避免特殊字符问题
        filename = hashlib.md5(wxid.encode('utf-8')).hexdigest() + ".json"
        return self.data_dir / filename

    def _load_image_data(self):
        """从持久化存储加载图片数据"""
        try:
            # 遍历数据目录中的所有JSON文件
            for file_path in self.data_dir.glob("*.json"):
                try:
                    with open(file_path, "r", encoding="utf-8") as f:
                        data = json.load(f)

                    if not isinstance(data, dict) or "wxid" not in data or "images" not in data:
                        logger.warning(f"[{self.plugin_name}] 数据文件格式错误: {file_path}")
                        continue

                    wxid = data["wxid"]
                    images = data["images"]

                    # 将加载的图片数据添加到内存中
                    self.image_store[wxid] = deque(images, maxlen=self.max_images)

                except Exception as e:
                    logger.error(f"[{self.plugin_name}] 加载图片数据失败: {str(e)}")
        except Exception as e:
            logger.error(f"[{self.plugin_name}] 加载图片数据目录异常: {str(e)}")

    def _save_image_data(self, wxid: str):
        """保存图片数据到持久化存储"""
        try:
            file_path = self._get_storage_path(wxid)

            # 准备要保存的数据
            data = {
                "wxid": wxid,
                "images": list(self.image_store[wxid])
            }

            # 保存到文件
            with open(file_path, "w", encoding="utf-8") as f:
                json.dump(data, f, ensure_ascii=False, indent=2)

        except Exception as e:
            logger.error(f"[{self.plugin_name}] 保存图片数据失败: {str(e)}")

    @on_image_message
    async def handle_image(self, bot: WechatAPIClient, message: dict):
        """处理图片消息"""
        if not self.enable:
            return

        try:
            wxid = message.get("FromWxid", "")
            sender_wxid = message.get("SenderWxid", "")
            xml_content = message.get("Content", "")
            msg_id = message.get("MsgId", "")

            # 存储图片信息
            image_info = {
                "msg_id": msg_id,
                "xml_content": xml_content,
                "from_wxid": wxid,
                "sender_wxid": sender_wxid,
                "timestamp": int(message.get("CreateTime", time.time()))  # 使用消息的CreateTime
            }

            # 将图片信息存储到对应群聊的队列中
            self.image_store[wxid].appendleft(image_info)

            # 持久化保存图片信息
            self._save_image_data(wxid)

            logger.info(f"[{self.plugin_name}] 保存图片信息成功，当前群 {wxid} 已存储 {len(self.image_store[wxid])} 张图片")

        except Exception as e:
            logger.error(f"[{self.plugin_name}] 处理图片消息异常: {str(e)}")

    @on_quote_message
    async def handle_quote(self, bot: WechatAPIClient, message: dict):
        """处理引用消息，响应引用图片的请求或分析图片"""
        if not self.enable:
            return

        content = str(message.get("Content", "")).strip()
        wxid = message.get("FromWxid", "")
        user_wxid = message.get("SenderWxid", "")

        # 检查是否是分析图片命令
        if self.analysis_enable and content in self.analysis_command:
            # 获取引用的消息信息
            quote_info = message.get("Quote", {})

            # 初始化变量
            found_image = None
            found_index = 0

            # 尝试解析Quote.Content中的XML
            try:
                quote_content = quote_info.get("Content", "")
                if quote_content:
                    import xml.etree.ElementTree as ET
                    quote_xml = ET.fromstring(quote_content)

                    # 查找refermsg节点
                    refermsg = quote_xml.find(".//refermsg")
                    if refermsg is not None:
                        # 获取createtime
                        createtime = refermsg.find("createtime")
                        if createtime is not None and createtime.text:
                            quoted_createtime = int(createtime.text)

                            # 使用时间戳查找最接近的图片
                            closest_img = None
                            closest_diff = float('inf')

                            for i, img in enumerate(self.image_store[wxid]):
                                # 假设我们在image_info中存储了timestamp
                                img_time = int(img.get("timestamp", 0))
                                time_diff = abs(quoted_createtime - img_time)

                                if time_diff < closest_diff:
                                    closest_diff = time_diff
                                    closest_img = img
                                    found_index = i

                            if closest_img and closest_diff < 10:  # 允许10秒的误差
                                found_image = closest_img

            except Exception as e:
                logger.error(f"[{self.plugin_name}] 解析引用消息XML失败: {str(e)}")

            # 如果找到了引用的图片，进行分析
            if found_image:
                await self.analyze_image(bot, wxid, user_wxid, found_image)
                return
            else:
                if self.natural_response:
                    error_msg = random.choice(self.error_msgs)
                    await bot.send_text_message(wxid, error_msg)
                else:
                    await bot.send_at_message(wxid, "⚠️ 未找到引用的图片，请确保引用的是图片消息", [user_wxid])
                return

        # 检查是否是引用图片命令
        if not content.split(maxsplit=1)[0] in self.command:
            return

        # 处理引用图片的逻辑（原有代码）
        try:
            # 处理没有存储图片的情况
            if wxid not in self.image_store or not self.image_store[wxid]:
                if self.natural_response:
                    error_msg = random.choice(self.error_msgs)
                    await bot.send_text_message(wxid, error_msg)
                else:
                    await bot.send_at_message(wxid, "⚠️ 当前群聊没有存储任何图片", [user_wxid])
                return

            # 获取引用的消息信息
            quote_info = message.get("Quote", {})

            # 初始化变量
            found_image = None
            found_index = 0

            # 尝试解析Quote.Content中的XML
            try:
                quote_content = quote_info.get("Content", "")
                if quote_content:
                    import xml.etree.ElementTree as ET
                    quote_xml = ET.fromstring(quote_content)

                    # 查找refermsg节点
                    refermsg = quote_xml.find(".//refermsg")
                    if refermsg is not None:
                        # 获取createtime
                        createtime = refermsg.find("createtime")
                        if createtime is not None and createtime.text:
                            quoted_createtime = int(createtime.text)

                            # 使用时间戳查找最接近的图片
                            closest_img = None
                            closest_diff = float('inf')

                            for i, img in enumerate(self.image_store[wxid]):
                                # 假设我们在image_info中存储了timestamp
                                img_time = int(img.get("timestamp", 0))
                                time_diff = abs(quoted_createtime - img_time)

                                if time_diff < closest_diff:
                                    closest_diff = time_diff
                                    closest_img = img
                                    found_index = i

                            if closest_img and closest_diff < 10:  # 允许10秒的误差
                                found_image = closest_img

            except Exception as e:
                logger.error(f"[{self.plugin_name}] 解析引用消息XML失败: {str(e)}")

            # 如果没有找到引用的图片，按照原来的逻辑处理
            if not found_image:
                # 获取索引参数（如果有）
                index = 0  # 默认使用最新的图片（队列末尾）
                if len(content.split(maxsplit=1)) > 1:
                    try:
                        index_param = int(content.split(maxsplit=1)[1].strip())
                        # 将用户输入的索引转换为队列索引（从1开始到N）
                        index = index_param - 1  # 用户输入1代表第一张图片
                        if index < 0 or index >= len(self.image_store[wxid]):
                            if self.natural_response:
                                error_msg = random.choice(self.error_msgs)
                                await bot.send_text_message(wxid, error_msg)
                            else:
                                await bot.send_at_message(wxid, f"⚠️ 索引超出范围，当前共有 {len(self.image_store[wxid])} 张图片", [user_wxid])
                            return
                    except ValueError:
                        if self.natural_response:
                            error_msg = random.choice(self.error_msgs)
                            await bot.send_text_message(wxid, error_msg)
                        else:
                            await bot.send_at_message(wxid, "⚠️ 索引必须是数字", [user_wxid])
                        return

                found_index = index
                found_image = self.image_store[wxid][found_index]

            # 简单确认
            await self._simple_confirm(bot, wxid)

            # 发送图片
            await self._send_image(bot, wxid, user_wxid, found_image)

        except Exception as e:
            logger.error(f"[{self.plugin_name}] 处理引用命令异常: {str(e)}")
            if self.natural_response:
                error_msg = random.choice(self.error_msgs)
                await bot.send_text_message(wxid, error_msg)
            else:
                await bot.send_at_message(wxid, "❌ 处理过程中出现错误，请稍后重试", [user_wxid])

    async def download_image(self, bot: WechatAPIClient, aeskey: str, cdnmidimgurl: str) -> str:
        """CDN下载高清图片。

        Args:
            bot (WechatAPIClient): 微信API客户端实例
            aeskey (str): 图片的AES密钥
            cdnmidimgurl (str): 图片的CDN URL

        Returns:
            str: 图片的base64编码字符串
        """
        try:
            # 检查参数格式
            if not aeskey:
                logger.error(f"[{self.plugin_name}] aeskey为空")
                return None

            if not cdnmidimgurl:
                logger.error(f"[{self.plugin_name}] cdnmidimgurl为空")
                return None

            # 使用API下载图片
            try:
                image_base64 = await bot.download_image(aeskey, cdnmidimgurl)

                # 检查返回值
                if not image_base64:
                    logger.error(f"[{self.plugin_name}] 下载图片失败: API返回为空")
                    return None

                return image_base64
            except Exception as api_error:
                logger.error(f"[{self.plugin_name}] 调用API下载图片时出错: {str(api_error)}")
                # 重新抛出异常，让调用者处理
                raise
        except Exception as e:
            logger.error(f"[{self.plugin_name}] 下载图片异常: {str(e)}")
            # 重新抛出异常，让调用者处理
            raise

    async def download_and_save_image(self, bot: WechatAPIClient, aeskey: str, cdnurl: str, msg_id: str = None, is_thumb: bool = False) -> Optional[Tuple[str, str]]:
        """下载并保存图片，使用 WechatAPIClient 的 download_image 和 base64_to_file 方法

        Args:
            bot (WechatAPIClient): 微信API客户端实例
            aeskey (str): 图片的AES密钥
            cdnurl (str): 图片的CDN URL (可以是cdnmidimgurl或cdnthumburl)
            msg_id (str, optional): 消息ID，用于生成文件名。如果为None，则使用时间戳
            is_thumb (bool, optional): 是否将参数视为缩略图参数。如果为True，则使用不同的API方法

        Returns:
            Tuple[str, str]: (图片的base64编码字符串, 图片的本地路径)，如果失败则返回 (None, None)
        """
        try:
            # 使用 WechatAPIClient 的 download_image 方法下载图片
            if is_thumb:
                # 尝试使用构造的HTTP请求下载缩略图
                try:
                    # 构造请求参数
                    json_param = {"Wxid": bot.wxid, "AesKey": aeskey, "Cdnthumburl": cdnurl}

                    # 获取API服务器地址和端口
                    api_url = f'http://{bot.ip}:{bot.port}/CdnDownloadThumbImg'

                    # 使用httpx发送请求
                    async with httpx.AsyncClient() as client:
                        response = await client.post(api_url, json=json_param)

                        # 检查响应状态码
                        if response.status_code != 200:
                            logger.error(f"[{self.plugin_name}] HTTP请求失败: 状态码 {response.status_code}")
                            return None, None

                        # 解析响应JSON
                        json_resp = response.json()

                        # 检查响应是否成功
                        if not json_resp.get("Success"):
                            logger.error(f"[{self.plugin_name}] API返回失败: {json_resp}")
                            return None, None

                        # 获取图片数据
                        image_base64 = json_resp.get("Data")

                        if not image_base64:
                            logger.error(f"[{self.plugin_name}] 下载图片失败: API返回数据为空")
                            return None, None

                except Exception as http_error:
                    logger.error(f"[{self.plugin_name}] HTTP请求异常: {str(http_error)}")
                    return None, None
            else:
                # 直接调用 download_image 方法
                image_base64 = await bot.download_image(aeskey, cdnurl)

                if not image_base64:
                    logger.error(f"[{self.plugin_name}] 下载图片失败: API返回为空")
                    return None, None

            # 确保临时目录存在
            os.makedirs(self.temp_dir, exist_ok=True)

            # 生成文件名
            if not msg_id:
                msg_id = str(int(time.time()))

            # 使用 base64_to_file 方法保存图片
            file_name = f"{msg_id}.jpg"
            file_path = self.temp_dir

            # 使用 base64_to_file 方法保存图片
            save_result = bot.base64_to_file(image_base64, file_name, file_path)

            if save_result:
                image_path = os.path.join(file_path, file_name)
                return image_base64, image_path
            else:
                logger.error(f"[{self.plugin_name}] 保存图片失败: base64_to_file 返回 False")
                return image_base64, None

        except Exception as e:
            logger.error(f"[{self.plugin_name}] 下载并保存图片异常: {str(e)}")
            return None, None

    async def _send_image(self, bot: WechatAPIClient, wxid: str, user_wxid: str, image_info: dict):
        """发送图片的通用方法"""
        try:
            # 从XML中提取图片信息
            xml_content = image_info["xml_content"]
            import xml.etree.ElementTree as ET

            # 解析XML内容
            root = ET.fromstring(xml_content)
            img_node = root.find('img')

            if img_node is not None:
                # 提取aeskey和cdnmidimgurl
                aeskey = img_node.get('aeskey')
                cdnmidimgurl = img_node.get('cdnmidimgurl')

                if aeskey and cdnmidimgurl:
                    # 使用API下载图片
                    try:
                        image_base64 = await self.download_image(bot, aeskey, cdnmidimgurl)
                        if image_base64:
                            # 发送图片
                            await bot.send_image_message(wxid, image_base64)
                            return
                    except Exception as e:
                        logger.error(f"[{self.plugin_name}] 下载图片失败: {str(e)}")

                # 如果上面的方法失败，尝试提取cdnthumburl
                cdnthumbaeskey = img_node.get('cdnthumbaeskey')
                cdnthumburl = img_node.get('cdnthumburl')

                if cdnthumbaeskey and cdnthumburl:
                    try:
                        thumb_base64 = await self.download_image(bot, cdnthumbaeskey, cdnthumburl)
                        if thumb_base64:
                            # 发送缩略图
                            await bot.send_image_message(wxid, thumb_base64)
                            return
                    except Exception as e:
                        logger.error(f"[{self.plugin_name}] 下载缩略图失败: {str(e)}")

            # 如果所有方法都失败
            if self.natural_response:
                error_msg = random.choice(self.error_msgs)
                await bot.send_text_message(wxid, error_msg)
            else:
                await bot.send_at_message(wxid, "❌ 无法提取图片信息，请检查API实现或图片是否过期", [user_wxid])

        except Exception as e:
            logger.error(f"[{self.plugin_name}] 发送图片失败: {str(e)}")
            if self.natural_response:
                error_msg = random.choice(self.error_msgs)
                await bot.send_text_message(wxid, error_msg)
            else:
                await bot.send_at_message(wxid, "❌ 图片发送失败，可能是图片已过期或系统限制", [user_wxid])

    async def test_download_image(self, bot: WechatAPIClient, wxid: str, user_wxid: str):
        """测试下载图片功能"""
        try:
            # 使用示例参数
            aeskey = "a765e84a63d7605d74c72d07d410f9b1"
            cdnmidimgurl = "3057020100044b30490201000204a95c809d02032df98b0204500093240204681a21e1042437663261663362312d616463372d343038642d383734362d3639333165363230626635370204051408030201000405004c550700"

            # 简单确认
            await self._simple_confirm(bot, wxid)

            # 直接下载图片，不使用XML转发

            # 尝试使用不同的参数组合下载图片
            try:
                # 首先尝试使用原始参数
                image_base64, image_path = await self.download_and_save_image(bot, aeskey, cdnmidimgurl, "test_image")

                # 如果失败，尝试将aeskey用作cdnthumbaeskey，cdnmidimgurl用作cdnthumburl
                if not image_base64:
                    image_base64, image_path = await self.download_and_save_image(bot, aeskey, cdnmidimgurl, "test_image", is_thumb=True)

            except Exception as e:
                logger.error(f"[{self.plugin_name}] 测试下载图片异常: {str(e)}")
                if self.natural_response:
                    error_msg = random.choice(self.error_msgs)
                    await bot.send_text_message(wxid, error_msg)
                else:
                    await bot.send_at_message(wxid, f"❌ 测试失败: {str(e)}", [user_wxid])
                return

            if not image_base64:
                if self.natural_response:
                    error_msg = random.choice(self.error_msgs)
                    await bot.send_text_message(wxid, error_msg)
                else:
                    await bot.send_at_message(wxid, "❌ 测试失败: 下载图片返回为空", [user_wxid])
                return

            # 发送图片
            await bot.send_image_message(wxid, image_base64)

            # 发送图片保存路径
            if image_path:
                await bot.send_at_message(
                    wxid,
                    f"✅ 测试成功: 图片已下载并保存到 {image_path}",
                    [user_wxid]
                )
            else:
                await bot.send_at_message(
                    wxid,
                    "⚠️ 图片下载成功但保存失败",
                    [user_wxid]
                )

        except Exception as e:
            logger.error(f"[{self.plugin_name}] 测试下载图片异常: {str(e)}")
            if self.natural_response:
                error_msg = random.choice(self.error_msgs)
                await bot.send_text_message(wxid, error_msg)
            else:
                await bot.send_at_message(wxid, f"❌ 测试过程中出现错误: {str(e)}", [user_wxid])

    @on_text_message
    async def handle_text(self, bot: WechatAPIClient, message: dict):
        """处理文本消息，响应图片列表请求和分析图片命令"""
        if not self.enable:
            return

        content = str(message.get("Content", "")).strip()
        wxid = message.get("FromWxid", "")
        user_wxid = message.get("SenderWxid", "")

        # 检查是否是查询图片列表的命令
        if content == "图片列表" or content == "列出图片":
            try:
                # 处理没有存储图片的情况
                if wxid not in self.image_store or not self.image_store[wxid]:
                    if self.natural_response:
                        error_msg = random.choice(self.error_msgs)
                        await bot.send_text_message(wxid, error_msg)
                    else:
                        await bot.send_at_message(wxid, "⚠️ 当前群聊没有存储任何图片", [user_wxid])
                    return

                # 构建图片列表信息
                image_count = len(self.image_store[wxid])
                message_text = f"📋 当前群聊共存储了 {image_count} 张图片\n\n"
                message_text += "使用方法：\n"
                message_text += "- 发送「引用图片 序号」查看指定图片\n"
                message_text += "- 引用图片消息并发送「引用图片」命令\n"
                message_text += "- 发送「下载图片 aeskey cdnmidimgurl」下载指定图片\n"
                if self.analysis_enable:
                    message_text += "- 发送「分析图片 序号」分析指定图片内容\n"
                message_text += "\n最近的5张图片：\n"

                # 列出最近的5张图片信息
                for i, img in enumerate(list(self.image_store[wxid])[:5]):
                    timestamp = img.get("timestamp", 0)
                    time_str = time.strftime("%m-%d %H:%M:%S", time.localtime(timestamp))
                    message_text += f"{i+1}. {time_str}\n"

                await bot.send_at_message(
                    wxid,
                    message_text,
                    [user_wxid]
                )

            except Exception as e:
                logger.error(f"[{self.plugin_name}] 处理图片列表命令异常: {str(e)}")
                if self.natural_response:
                    error_msg = random.choice(self.error_msgs)
                    await bot.send_text_message(wxid, error_msg)
                else:
                    await bot.send_at_message(wxid, "❌ 处理过程中出现错误，请稍后重试", [user_wxid])

        # 处理下载图片命令
        elif content.startswith(tuple(cmd + " " for cmd in self.download_command)):
            try:
                # 获取命令参数
                command_parts = content.split(maxsplit=1)
                if len(command_parts) < 2:
                    await bot.send_at_message(
                        wxid,
                        "⚠️ 请提供正确的参数，格式：下载图片 aeskey cdnmidimgurl",
                        [user_wxid]
                    )
                    return

                # 尝试解析参数
                params_text = command_parts[1].strip()

                # 检查是否是完整的XML
                if "<msg>" in params_text and "</msg>" in params_text:
                    # 尝试解析XML
                    try:
                        import xml.etree.ElementTree as ET
                        # 确保XML格式正确
                        if not params_text.startswith("<msg>"):
                            params_text = "<msg>" + params_text.split("<msg>", 1)[1]

                        root = ET.fromstring(params_text)

                        # 尝试从appattach中提取参数
                        appattach = root.find('appattach')
                        if appattach is not None:
                            cdnthumbaeskey = appattach.find('cdnthumbaeskey')
                            cdnthumburl = appattach.find('cdnthumburl')

                            if cdnthumbaeskey is not None and cdnthumburl is not None:
                                aeskey = cdnthumbaeskey.text
                                cdnmidimgurl = cdnthumburl.text
                                logger.info(f"[{self.plugin_name}] 从XML中提取参数: cdnthumbaeskey={aeskey}, cdnthumburl={cdnmidimgurl}")
                            else:
                                # 尝试提取aeskey
                                aeskey_elem = appattach.find('aeskey')
                                if aeskey_elem is not None:
                                    aeskey = aeskey_elem.text

                                # 如果没有找到cdnthumburl，尝试其他字段
                                if cdnthumburl is None:
                                    cdnmidimgurl_elem = appattach.find('cdnmidimgurl')
                                    if cdnmidimgurl_elem is not None:
                                        cdnmidimgurl = cdnmidimgurl_elem.text

                        # 如果没有从appattach中找到，尝试从img标签中提取
                        if not aeskey or not cdnmidimgurl:
                            img = root.find('img')
                            if img is not None:
                                if not aeskey:
                                    aeskey_attr = img.get('aeskey')
                                    if aeskey_attr:
                                        aeskey = aeskey_attr

                                if not cdnmidimgurl:
                                    cdnmidimgurl_attr = img.get('cdnmidimgurl')
                                    if cdnmidimgurl_attr:
                                        cdnmidimgurl = cdnmidimgurl_attr
                    except Exception as xml_error:
                        logger.error(f"[{self.plugin_name}] 解析XML异常: {str(xml_error)}")

                # 如果XML解析失败或不是XML，尝试其他方法
                if not aeskey or not cdnmidimgurl:
                    # 检查是否包含cdnthumburl标记
                    if "<cdnthumburl>" in params_text and "</cdnthumburl>" in params_text:
                        # 从XML格式中提取cdnthumburl
                        cdnthumburl_start = params_text.find("<cdnthumburl>") + len("<cdnthumburl>")
                        cdnthumburl_end = params_text.find("</cdnthumburl>")
                        cdnmidimgurl = params_text[cdnthumburl_start:cdnthumburl_end].strip()

                        # 查找cdnthumbaeskey
                        aeskey_parts = params_text.split("cdnthumbaeskey:", 1)
                        if len(aeskey_parts) > 1:
                            aeskey = aeskey_parts[1].strip().split()[0].strip()
                        else:
                            # 尝试其他格式
                            aeskey_parts = params_text.split("aeskey:", 1)
                            if len(aeskey_parts) > 1:
                                aeskey = aeskey_parts[1].strip().split()[0].strip()
                    else:
                        # 常规空格分隔的参数
                        params = params_text.split(maxsplit=1)
                        if len(params) < 2:
                            await bot.send_at_message(
                                wxid,
                                "⚠️ 缺少参数，格式：下载图片 aeskey cdnmidimgurl",
                                [user_wxid]
                            )
                            return

                        aeskey = params[0].strip()
                        cdnmidimgurl = params[1].strip()

                # 检查是否成功提取了参数
                if not aeskey or not cdnmidimgurl:
                    await bot.send_at_message(
                        wxid,
                        "⚠️ 无法解析参数，请使用格式：下载图片 aeskey cdnmidimgurl",
                        [user_wxid]
                    )
                    return

                # 简单确认
                await self._simple_confirm(bot, wxid)

                # 直接下载图片，不使用XML转发

                # 尝试使用不同的参数组合下载图片
                msg_id = f"download_{int(time.time())}"

                # 首先尝试使用原始参数
                image_base64, image_path = await self.download_and_save_image(bot, aeskey, cdnmidimgurl, msg_id)

                # 如果失败，尝试将aeskey用作cdnthumbaeskey，cdnmidimgurl用作cdnthumburl
                if not image_base64:
                    image_base64, image_path = await self.download_and_save_image(bot, aeskey, cdnmidimgurl, msg_id, is_thumb=True)

                if not image_base64:
                    # 提供更详细的错误信息
                    error_message = "❌ 下载图片失败，可能的原因：\n"
                    error_message += "1. 参数格式不正确\n"
                    error_message += "2. 图片已过期或被删除\n"
                    error_message += "3. 服务器暂时不可用\n\n"
                    error_message += "请确认：\n"
                    error_message += f"- aeskey 长度应为32字符，当前长度: {len(aeskey)}\n"
                    error_message += f"- cdnmidimgurl 应以3057开头，当前值: {cdnmidimgurl[:10]}...\n"
                    error_message += "如果是从微信消息中复制的参数，请确保完整复制"

                    await bot.send_at_message(
                        wxid,
                        error_message,
                        [user_wxid]
                    )
                    return

                # 发送图片
                await bot.send_image_message(wxid, image_base64)

                # 如果图片保存成功，发送保存路径
                if image_path:
                    await bot.send_at_message(
                        wxid,
                        f"✅ 图片已下载并保存到: {image_path}",
                        [user_wxid]
                    )

            except Exception as e:
                logger.error(f"[{self.plugin_name}] 处理下载图片命令异常: {str(e)}")
                await bot.send_at_message(
                    wxid,
                    f"❌ 处理过程中出现错误: {str(e)}",
                    [user_wxid]
                )

        # 处理分析图片命令
        elif self.analysis_enable and content.startswith(tuple(cmd + " " for cmd in self.analysis_command)):
            try:
                # 处理没有存储图片的情况
                if wxid not in self.image_store or not self.image_store[wxid]:
                    await bot.send_at_message(
                        wxid,
                        "⚠️ 当前群聊没有存储任何图片",
                        [user_wxid]
                    )
                    return

                # 获取索引参数
                command_parts = content.split(maxsplit=1)
                if len(command_parts) < 2:
                    await bot.send_at_message(
                        wxid,
                        "⚠️ 请指定要分析的图片索引，例如：分析图片 1",
                        [user_wxid]
                    )
                    return

                try:
                    index_param = int(command_parts[1].strip())
                    # 将用户输入的索引转换为队列索引（从1开始到N）
                    index = index_param - 1
                    if index < 0 or index >= len(self.image_store[wxid]):
                        await bot.send_at_message(
                            wxid,
                            f"⚠️ 索引超出范围，当前共有 {len(self.image_store[wxid])} 张图片",
                            [user_wxid]
                        )
                        return
                except ValueError:
                    await bot.send_at_message(
                        wxid,
                        "⚠️ 索引必须是数字",
                        [user_wxid]
                    )
                    return

                # 获取图片信息
                image_info = self.image_store[wxid][index]

                # 分析图片
                await self.analyze_image(bot, wxid, user_wxid, image_info)

            except Exception as e:
                logger.error(f"[{self.plugin_name}] 处理分析图片命令异常: {str(e)}")
                await bot.send_at_message(
                    wxid,
                    "❌ 处理过程中出现错误，请稍后重试",
                    [user_wxid]
                )

        # 处理测试下载图片命令
        elif content in self.test_command:
            await self.test_download_image(bot, wxid, user_wxid)

        # 处理引用图片命令
        elif content.startswith(tuple(cmd + " " for cmd in self.command)) or content in self.command:
            await self._process_command(bot, content, wxid, user_wxid)

    async def _process_command(self, bot: WechatAPIClient, content: str, wxid: str, user_wxid: str):
        """处理引用图片命令的共享逻辑"""
        # 检查是否是插件命令
        command_parts = content.split(maxsplit=1)
        if not command_parts or command_parts[0] not in self.command:
            return

        try:
            # 处理没有存储图片的情况
            if wxid not in self.image_store or not self.image_store[wxid]:
                await bot.send_at_message(
                    wxid,
                    "⚠️ 当前群聊没有存储任何图片",
                    [user_wxid]
                )
                return

            # 获取索引参数（如果有）
            index = 0  # 默认使用最新的图片
            if len(command_parts) > 1:
                try:
                    index_param = int(command_parts[1].strip())
                    # 将用户输入的索引转换为队列索引（从1开始到N）
                    index = index_param - 1
                    if index < 0 or index >= len(self.image_store[wxid]):
                        await bot.send_at_message(
                            wxid,
                            f"⚠️ 索引超出范围，当前共有 {len(self.image_store[wxid])} 张图片",
                            [user_wxid]
                        )
                        return
                except ValueError:
                    await bot.send_at_message(
                        wxid,
                        "⚠️ 索引必须是数字",
                        [user_wxid]
                    )
                    return

            # 获取图片信息
            image_info = self.image_store[wxid][index]

            # 发送引用图片消息
            await bot.send_at_message(
                wxid,
                f"🖼️ 引用图片（索引: {index+1}/{len(self.image_store[wxid])}）：",
                [user_wxid]
            )

            # 发送图片
            await self._send_image(bot, wxid, user_wxid, image_info)

        except Exception as e:
            logger.error(f"[{self.plugin_name}] 处理命令异常: {str(e)}")
            await bot.send_at_message(
                wxid,
                "❌ 处理过程中出现错误，请稍后重试",
                [user_wxid]
            )

    async def _upload_image_to_chatglm(self, image_base64: str, filename: str = "image.jpg") -> Optional[Dict]:
        """上传图片到ChatGLM API"""
        if not self.chatglm_token:
            logger.error(f"[{self.plugin_name}] ChatGLM token未配置，无法上传图片")
            return None

        try:
            # 检查base64数据是否有效
            if not image_base64 or len(image_base64) < 100:  # 简单检查数据是否太短
                logger.error(f"[{self.plugin_name}] 图片base64数据无效或为空")
                return None

            # 解码base64图片数据
            try:
                # 检查是否包含base64前缀，如果有则去除
                if ',' in image_base64:
                    image_base64 = image_base64.split(',', 1)[1]
                image_data = base64.b64decode(image_base64)
            except Exception as e:
                logger.error(f"[{self.plugin_name}] base64解码失败: {str(e)}")
                return None

            # 检查解码后的数据是否为空
            if not image_data or len(image_data) < 100:
                logger.error(f"[{self.plugin_name}] 解码后的图片数据无效或为空")
                return None

            # 准备上传请求
            upload_url = f"{self.chatglm_api_url}/assistant/file_upload"

            # 生成时间戳和随机数
            current_timestamp = str(int(time.time() * 1000))
            nonce = "".join([str(random.randint(0, 9)) for _ in range(32)])

            # 计算签名
            sign_content = f"{self.chatglm_token}{current_timestamp}{nonce}"
            import hashlib
            sign = hashlib.md5(sign_content.encode()).hexdigest()

            headers = {
                "Authorization": f"Bearer {self.chatglm_token}",
                "Accept": "application/json",
                "X-App-Platform": "pc",
                "X-Timestamp": current_timestamp,
                "X-Nonce": nonce,
                "X-Sign": sign,
                "X-Device-Id": "83b89578f30c4fa598733d7bf4a0c9a8"
            }

            # 添加Cookie信息
            cookies = {
                "sensorsdata2015jssdkchannel": "%7B%22prop%22%3A%7B%22_sa_channel_landing_url%22%3A%22%22%7D%7D",
                "chatglm_refresh_token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJzdWIiOiI2NDI4ZDhjZWY4ZTg0NWEyOWMzMGQ5OWViZTZmN2Q2NiIsImV4cCI6MTc1NzMyOTE4MywibmJmIjoxNzQxNzc3MTgzLCJpYXQiOjE3NDE3NzcxODMsImp0aSI6IjcyYThhYzk5NjI0NzQwYzBiYzk3Mzk4YTY4NmE1MDUzIiwidWlkIjoiNjdkMTY5MWYxYTdlNjY3Yjg1YTI3YzMwIiwidHlwZSI6InJlZnJlc2gifQ.L2Z2ecxjWbJadfxUgRbybikks8K60PtIkxE5tgq4fuU",
                "chatglm_user_id": "67d1691f1a7e667b85a27c30",
                "sensorsdata2015jssdkcross": "%7B%22distinct_id%22%3A%2267d1691f1a7e667b85a27c30%22%2C%22first_id%22%3A%221958a02468840-012ccf0c267cdfe-26031f51-280800-1958a024689b2%22%2C%22props%22%3A%7B%22%24latest_traffic_source_type%22%3A%22%E7%9B%B4%E6%8E%A5%E6%B5%81%E9%87%8F%22%2C%22%24latest_search_keyword%22%3A%22%E6%9C%AA%E5%8F%96%E5%88%B0%E5%80%BC_%E7%9B%B4%E6%8E%A5%E6%89%93%E5%BC%80%22%2C%22%24latest_referrer%22%3A%22%22%2C%22_latest_wx_ad_click_id%22%3A%22%22%2C%22_latest_wx_ad_hash_key%22%3A%22%22%2C%22_latest_wx_ad_callbacks%22%3A%22%22%7D%2C%22identities%22%3A%22eyIkaWRlbnRpdHlfY29va2llX2lkIjoiMTk1OGEwMjQ2ODg0MC0wMTJjY2YwYzI2N2NkZmUtMjYwMzFmNTEtMjgwODAwLTE5NThhMDI0Njg5YjIiLCIkaWRlbnRpdHlfbG9naW5faWQiOiI2N2QxNjkxZjFhN2U2NjdiODVhMjdjMzAifQ%3D%3D%22%2C%22history_login_id%22%3A%7B%22name%22%3A%22%24identity_login_id%22%2C%22value%22%3A%2267d1691f1a7e667b85a27c30%22%7D%2C%22%24device_id%22%3A%221958a02468840-012ccf0c267cdfe-26031f51-280800-1958a024689b2%22%7D",
                "chatglm_token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJzdWIiOiI2NDI4ZDhjZWY4ZTg0NWEyOWMzMGQ5OWViZTZmN2Q2NiIsImV4cCI6MTc0Mjk3ODE0NiwibmJmIjoxNzQyODkxNzQ2LCJpYXQiOjE3NDI4OTE3NDYsImp0aSI6IjY1OGRkYjg5YzBmZjRkMWY5OTI3NTk5Nzc0MTk3N2JjIiwidWlkIjoiNjdkMTY5MWYxYTdlNjY3Yjg1YTI3YzMwIiwidHlwZSI6ImFjY2VzcyJ9.QMWRXmzcZEVYAdvEt0J_INLH2_clq9BJRkD8k0QKHA8",
                "chatglm_token_expires": "2025-03-25%2018:35:46",
                "acw_tc": "ac11000117428969886441721e00bff08e4306608e2442dc639370ad7ada9d"
            }

            # 准备文件数据
            files = {'file': (filename, image_data, 'image/jpeg')}

            # 发送请求，添加cookies参数
            async with httpx.AsyncClient() as client:
                response = await client.post(upload_url, headers=headers, files=files, cookies=cookies)

                if response.status_code != 200:
                    logger.error(f"[{self.plugin_name}] 上传图片失败: HTTP {response.status_code}")
                    return None

                result = response.json()
                if result.get("status") != 0 or "result" not in result:
                    logger.error(f"[{self.plugin_name}] 上传图片API返回错误: {result}")
                    return None

                return result["result"]

        except Exception as e:
            logger.error(f"[{self.plugin_name}] 上传图片异常: {str(e)}")
            return None

    async def _analyze_image_with_chatglm(self, file_info: Dict) -> Optional[str]:
        """使用ChatGLM API分析图片内容"""
        if not self.chatglm_token or not self.chatglm_assistant_id:
            logger.error(f"[{self.plugin_name}] ChatGLM token或assistant_id未配置，无法分析图片")
            return None

        try:
            # 准备分析请求
            analyze_url = f"{self.chatglm_api_url}/assistant/stream"

            # 使用最新的请求头信息
            current_timestamp = str(int(time.time() * 1000))
            nonce = "".join([str(random.randint(0, 9)) for _ in range(32)])

            # 更新 X-Sign 头部
            sign_content = f"{self.chatglm_token}{current_timestamp}{nonce}"
            import hashlib
            sign = hashlib.md5(sign_content.encode()).hexdigest()

            headers = {
                "Connection": "keep-alive",
                "Authorization": f"Bearer {self.chatglm_token}",
                "X-App-Platform": "pc",
                "X-Device-Brand": "",
                "X-Exp-Groups": "na_android_config:exp:NA,mainchat_funcall:exp:A,chat_aisearch:exp:A,mainchat_rag:exp:B,mainchat_searchrateboost:exp:A,mainchat_searchengine:exp:sogou,na_4o_config:exp:4o_A,mainchat_32b:exp:D,chat_live_4o:exp:A,na_glm4plus_config:exp:open,mainchat_server:exp:A,mainchat_browser:exp:new,mainchat_server_app:exp:A,chat_glms_models_202412:exp:A,mobile_history_daycheck:exp:a,mainchat_sug:exp:A,desktop_toolbar:exp:A,chat_drawing_server:exp:A,chat_search_rag:exp:B,drawing_server_cogview:exp:cogview4,app_welcome_v2:exp:A,chat_assistant_server:exp:A,chat_drawing_streamv2:exp:A,chat_assistant_multi_recall:exp:A",
                "X-Sign": sign,  # 添加签名
                "X-Lang": "zh",
                "X-Request-Id": f"{nonce[:8]}-{nonce[8:12]}-{nonce[12:16]}-{nonce[16:20]}-{nonce[20:]}",
                "X-App-Version": "0.0.1",
                "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/116.0.0.0 Safari/537.36",
                "App-Name": "chatglm",
                "Content-Type": "application/json",
                "accept": "text/event-stream",
                "X-Timestamp": current_timestamp,
                "X-Device-Model": "",
                "X-Nonce": nonce,
                "X-Device-Id": "83b89578f30c4fa598733d7bf4a0c9a8",
                "Origin": "https://chatglm.cn",
                "X-Requested-With": "mark.via",
                "Referer": "https://chatglm.cn/"
            }

            # 添加Cookie信息
            cookies = {
                "sensorsdata2015jssdkchannel": "%7B%22prop%22%3A%7B%22_sa_channel_landing_url%22%3A%22%22%7D%7D",
                "chatglm_refresh_token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJzdWIiOiI2NDI4ZDhjZWY4ZTg0NWEyOWMzMGQ5OWViZTZmN2Q2NiIsImV4cCI6MTc1NzMyOTE4MywibmJmIjoxNzQxNzc3MTgzLCJpYXQiOjE3NDE3NzcxODMsImp0aSI6IjcyYThhYzk5NjI0NzQwYzBiYzk3Mzk4YTY4NmE1MDUzIiwidWlkIjoiNjdkMTY5MWYxYTdlNjY3Yjg1YTI3YzMwIiwidHlwZSI6InJlZnJlc2gifQ.L2Z2ecxjWbJadfxUgRbybikks8K60PtIkxE5tgq4fuU",
                "chatglm_user_id": "67d1691f1a7e667b85a27c30",
                "sensorsdata2015jssdkcross": "%7B%22distinct_id%22%3A%2267d1691f1a7e667b85a27c30%22%2C%22first_id%22%3A%221958a02468840-012ccf0c267cdfe-26031f51-280800-1958a024689b2%22%2C%22props%22%3A%7B%22%24latest_traffic_source_type%22%3A%22%E7%9B%B4%E6%8E%A5%E6%B5%81%E9%87%8F%22%2C%22%24latest_search_keyword%22%3A%22%E6%9C%AA%E5%8F%96%E5%88%B0%E5%80%BC_%E7%9B%B4%E6%8E%A5%E6%89%93%E5%BC%80%22%2C%22%24latest_referrer%22%3A%22%22%2C%22_latest_wx_ad_click_id%22%3A%22%22%2C%22_latest_wx_ad_hash_key%22%3A%22%22%2C%22_latest_wx_ad_callbacks%22%3A%22%22%7D%2C%22identities%22%3A%22eyIkaWRlbnRpdHlfY29va2llX2lkIjoiMTk1OGEwMjQ2ODg0MC0wMTJjY2YwYzI2N2NkZmUtMjYwMzFmNTEtMjgwODAwLTE5NThhMDI0Njg5YjIiLCIkaWRlbnRpdHlfbG9naW5faWQiOiI2N2QxNjkxZjFhN2U2NjdiODVhMjdjMzAifQ%3D%3D%22%2C%22history_login_id%22%3A%7B%22name%22%3A%22%24identity_login_id%22%2C%22value%22%3A%2267d1691f1a7e667b85a27c30%22%7D%2C%22%24device_id%22%3A%221958a02468840-012ccf0c267cdfe-26031f51-280800-1958a024689b2%22%7D",
                "chatglm_token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJzdWIiOiI2NDI4ZDhjZWY4ZTg0NWEyOWMzMGQ5OWViZTZmN2Q2NiIsImV4cCI6MTc0Mjk3ODE0NiwibmJmIjoxNzQyODkxNzQ2LCJpYXQiOjE3NDI4OTE3NDYsImp0aSI6IjY1OGRkYjg5YzBmZjRkMWY5OTI3NTk5Nzc0MTk3N2JjIiwidWlkIjoiNjdkMTY5MWYxYTdlNjY3Yjg1YTI3YzMwIiwidHlwZSI6ImFjY2VzcyJ9.QMWRXmzcZEVYAdvEt0J_INLH2_clq9BJRkD8k0QKHA8",
                "chatglm_token_expires": "2025-03-25%2018:35:46",
                "acw_tc": "ac11000117428969886441721e00bff08e4306608e2442dc639370ad7ada9d"
            }

            # 准备请求数据
            payload = {
                "assistant_id": self.chatglm_assistant_id,
                "conversation_id": "",
                "meta_data": {
                    "if_plus_model": False,
                    "is_test": False,
                    "input_question_type": "xxxx",
                    "channel": "",
                    "draft_id": "",
                    "quote_log_id": "",
                    "platform": "pc"
                },
                "messages": [
                    {
                        "role": "user",
                        "content": [
                            {
                                "type": "text",
                                "text": "整理图片核心内容"
                            },
                            {
                                "type": "image",
                                "image": [
                                    {
                                        "file_name": file_info.get("file_name", "image.jpg"),
                                        "file_id": file_info.get("file_id", ""),
                                        "image_url": file_info.get("file_url", ""),
                                        "file_size": int(file_info.get("file_size", 0)),
                                        "order": 0
                                    }
                                ]
                            }
                        ]
                    }
                ]
            }

            # 发送请求并处理流式响应
            full_response = ""
            response_text = ""

            # 使用httpx处理流式响应，添加cookies参数
            async with httpx.AsyncClient(timeout=60) as client:
                try:
                    response = await client.post(analyze_url, headers=headers, json=payload, cookies=cookies)
                    response_status = response.status_code

                    if response_status != 200:
                        logger.error(f"[{self.plugin_name}] 分析图片失败: HTTP {response_status}")
                        return None

                    # 直接读取整个响应内容
                    response_text = response.text

                    # 处理SSE格式的响应
                    data_lines = [line.strip() for line in response_text.split('\n') if line.strip().startswith('data: ')]

                    # 尝试从每一行提取内容
                    for data_line in data_lines:
                        try:
                            data_json = data_line[6:]  # 去掉'data: '前缀
                            if not data_json or data_json == "":
                                continue

                            data = json.loads(data_json)
                            parts = data.get('parts', [])
                            if parts and len(parts) > 0:
                                content = parts[0].get('content', [])
                                if content and len(content) > 0:
                                    text = content[0].get('text', '')
                                    if text and len(text) > len(full_response):
                                        full_response = text
                        except Exception as e:
                            logger.error(f"[{self.plugin_name}] 解析响应行异常: {str(e)}")

                    # 如果没有提取到内容，尝试使用最后一行
                    if not full_response and data_lines:
                        try:
                            last_data = data_lines[-1][6:]
                            data = json.loads(last_data)
                            parts = data.get('parts', [])
                            if parts and len(parts) > 0:
                                content = parts[0].get('content', [])
                                if content and len(content) > 0:
                                    text = content[0].get('text', '')
                                    if text:
                                        full_response = text
                        except Exception as e:
                            logger.error(f"[{self.plugin_name}] 尝试提取最后响应异常: {str(e)}")
                except httpx.TimeoutException:
                    logger.error(f"[{self.plugin_name}] 请求超时")
                    return "分析图片超时，请稍后重试。"
                except Exception as e:
                    logger.error(f"[{self.plugin_name}] 请求异常: {str(e)}")
                    return None

            if not full_response:
                logger.error(f"[{self.plugin_name}] 未能从响应中提取有效内容")
                return "无法分析图片内容，请稍后重试。"

            return full_response

        except Exception as e:
            logger.error(f"[{self.plugin_name}] 分析图片异常: {str(e)}")
            return None

    async def analyze_image(self, bot: WechatAPIClient, wxid: str, user_wxid: str, image_info: Dict) -> None:
        """分析图片并发送结果"""
        if not self.analysis_enable:
            await bot.send_at_message(
                wxid,
                "⚠️ 图片分析功能未启用，请在配置文件中开启",
                [user_wxid]
            )
            return

        try:
            # 发送处理中提示
            await bot.send_at_message(
                wxid,
                "🔍 正在分析图片，请稍候...",
                [user_wxid]
            )

            # 使用硬编码的最新token，跳过刷新token步骤
            # await self._refresh_chatglm_token()
            self.chatglm_token = "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJzdWIiOiI2NDI4ZDhjZWY4ZTg0NWEyOWMzMGQ5OWViZTZmN2Q2NiIsImV4cCI6MTc0Mjk3ODE0NiwibmJmIjoxNzQyODkxNzQ2LCJpYXQiOjE3NDI4OTE3NDYsImp0aSI6IjY1OGRkYjg5YzBmZjRkMWY5OTI3NTk5Nzc0MTk3N2JjIiwidWlkIjoiNjdkMTY5MWYxYTdlNjY3Yjg1YTI3YzMwIiwidHlwZSI6ImFjY2VzcyJ9.QMWRXmzcZEVYAdvEt0J_INLH2_clq9BJRkD8k0QKHA8"

            # 从XML中提取图片信息并下载
            xml_content = image_info["xml_content"]
            import xml.etree.ElementTree as ET

            # 解析XML内容
            root = ET.fromstring(xml_content)
            img_node = root.find('img')

            if img_node is not None:
                # 尝试下载原图
                aeskey = img_node.get('aeskey')
                cdnmidimgurl = img_node.get('cdnmidimgurl')

                image_base64 = None
                if aeskey and cdnmidimgurl:
                    try:
                        image_base64 = await bot.download_image(aeskey, cdnmidimgurl)
                    except Exception as e:
                        logger.error(f"[{self.plugin_name}] 下载原图失败: {str(e)}")

                # 如果原图下载失败，尝试下载缩略图
                if not image_base64:
                    cdnthumbaeskey = img_node.get('cdnthumbaeskey')
                    cdnthumburl = img_node.get('cdnthumburl')

                    if cdnthumbaeskey and cdnthumburl:
                        try:
                            image_base64 = await bot.download_image(cdnthumbaeskey, cdnthumburl)
                        except Exception as e:
                            logger.error(f"[{self.plugin_name}] 下载缩略图失败: {str(e)}")

                # 如果成功获取图片
                if image_base64:
                    # 上传图片到ChatGLM
                    file_info = await self._upload_image_to_chatglm(image_base64)
                    if not file_info:
                        await bot.send_at_message(
                            wxid,
                            "❌ 上传图片失败，无法进行分析",
                            [user_wxid]
                        )
                        return

                    # 分析图片
                    analysis_result = await self._analyze_image_with_chatglm(file_info)
                    if not analysis_result:
                        await bot.send_at_message(
                            wxid,
                            "❌ 分析图片失败，请稍后重试",
                            [user_wxid]
                        )
                        return

                    # 发送分析结果
                    await bot.send_at_message(
                        wxid,
                        f"📊 图片分析结果：\n\n{analysis_result}",
                        [user_wxid]
                    )
                    return

            # 如果无法获取图片
            await bot.send_at_message(
                wxid,
                "❌ 无法提取图片信息，请检查图片是否有效",
                [user_wxid]
            )

        except Exception as e:
            logger.error(f"[{self.plugin_name}] 分析图片过程中出现异常: {str(e)}")
            await bot.send_at_message(
                wxid,
                "❌ 分析过程中出现错误，请稍后重试",
                [user_wxid]
            )

    async def _refresh_chatglm_token(self) -> bool:
        """刷新ChatGLM API的token"""
        try:
            # 修正刷新token的URL
            refresh_url = "https://chatglm.cn/chatglm/backend-api/auth/token/refresh"

            # 从Cookie中获取refresh_token
            refresh_token = "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJzdWIiOiI2NDI4ZDhjZWY4ZTg0NWEyOWMzMGQ5OWViZTZmN2Q2NiIsImV4cCI6MTc1NzMyOTE4MywibmJmIjoxNzQxNzc3MTgzLCJpYXQiOjE3NDE3NzcxODMsImp0aSI6IjcyYThhYzk5NjI0NzQwYzBiYzk3Mzk4YTY4NmE1MDUzIiwidWlkIjoiNjdkMTY5MWYxYTdlNjY3Yjg1YTI3YzMwIiwidHlwZSI6InJlZnJlc2gifQ.L2Z2ecxjWbJadfxUgRbybikks8K60PtIkxE5tgq4fuU"

            # 添加更多必要的头部
            current_timestamp = str(int(time.time() * 1000))
            nonce = "".join([str(random.randint(0, 9)) for _ in range(32)])

            headers = {
                "Content-Type": "application/json",
                "X-App-Platform": "pc",
                "X-Timestamp": current_timestamp,
                "X-Nonce": nonce,
                "X-Device-Id": "83b89578f30c4fa598733d7bf4a0c9a8",
                "User-Agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/116.0.0.0 Safari/537.36",
                "Origin": "https://chatglm.cn",
                "Referer": "https://chatglm.cn/"
            }

            # 添加Cookie
            cookies = {
                "sensorsdata2015jssdkchannel": "%7B%22prop%22%3A%7B%22_sa_channel_landing_url%22%3A%22%22%7D%7D",
                "chatglm_refresh_token": refresh_token,
                "chatglm_user_id": "67d1691f1a7e667b85a27c30",
                "sensorsdata2015jssdkcross": "%7B%22distinct_id%22%3A%2267d1691f1a7e667b85a27c30%22%2C%22first_id%22%3A%221958a02468840-012ccf0c267cdfe-26031f51-280800-1958a024689b2%22%2C%22props%22%3A%7B%22%24latest_traffic_source_type%22%3A%22%E7%9B%B4%E6%8E%A5%E6%B5%81%E9%87%8F%22%2C%22%24latest_search_keyword%22%3A%22%E6%9C%AA%E5%8F%96%E5%88%B0%E5%80%BC_%E7%9B%B4%E6%8E%A5%E6%89%93%E5%BC%80%22%2C%22%24latest_referrer%22%3A%22%22%2C%22_latest_wx_ad_click_id%22%3A%22%22%2C%22_latest_wx_ad_hash_key%22%3A%22%22%2C%22_latest_wx_ad_callbacks%22%3A%22%22%7D%2C%22identities%22%3A%22eyIkaWRlbnRpdHlfY29va2llX2lkIjoiMTk1OGEwMjQ2ODg0MC0wMTJjY2YwYzI2N2NkZmUtMjYwMzFmNTEtMjgwODAwLTE5NThhMDI0Njg5YjIiLCIkaWRlbnRpdHlfbG9naW5faWQiOiI2N2QxNjkxZjFhN2U2NjdiODVhMjdjMzAifQ%3D%3D%22%2C%22history_login_id%22%3A%7B%22name%22%3A%22%24identity_login_id%22%2C%22value%22%3A%2267d1691f1a7e667b85a27c30%22%7D%2C%22%24device_id%22%3A%221958a02468840-012ccf0c267cdfe-26031f51-280800-1958a024689b2%22%7D",
                "chatglm_token": self.chatglm_token,
                "chatglm_token_expires": "2025-03-25%2018:35:46",
                "acw_tc": "ac11000117428969886441721e00bff08e4306608e2442dc639370ad7ada9d"
            }

            data = {
                "refresh_token": refresh_token
            }

            async with httpx.AsyncClient() as client:
                response = await client.post(refresh_url, headers=headers, json=data, cookies=cookies)

                if response.status_code == 200:
                    result = response.json()
                    if result.get("status") == 0 and "result" in result:
                        # 更新token
                        self.chatglm_token = result["result"]["access_token"]
                        return True

                logger.error(f"[{self.plugin_name}] 刷新token失败")
                return False

        except Exception as e:
            logger.error(f"[{self.plugin_name}] 刷新token异常: {str(e)}")
            return False