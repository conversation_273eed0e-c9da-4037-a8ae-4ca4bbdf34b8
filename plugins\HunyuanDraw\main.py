import os
import json
import httpx
import asyncio
import time
import traceback
import uuid
try:
    import tomllib
except ModuleNotFoundError:
    import tomli as tomllib
from loguru import logger
from pathlib import Path
from typing import Optional, Dict, Any, List
import aiofiles

from WechatAPI import WechatAPIClient
from utils.decorators import on_text_message
from utils.plugin_base import PluginBase

class HunyuanDraw(PluginBase):
    description = "腾讯混元AI绘画插件"
    author = "XYBot"
    version = "1.0.0"
    plugin_name = "HunyuanDraw"

    def __init__(self):
        super().__init__()

        # 初始化临时目录
        self.temp_dir = Path("plugins/HunyuanDraw/temp")
        self.temp_dir.mkdir(parents=True, exist_ok=True)

        # 读取配置
        self._load_config()

        # 用户限流字典
        self.user_last_request = {}

    def _load_config(self):
        """加载配置文件"""
        config = {}
        try:
            config_path = f"plugins/{self.plugin_name}/config.toml"
            if not os.path.exists(config_path):
                logger.warning(f"[{self.plugin_name}] 配置文件 {config_path} 不存在，将使用默认配置")
                config = {}
            else:
                with open(config_path, "rb") as f:
                    plugin_config = tomllib.load(f)
                config = plugin_config.get(self.plugin_name, {})
        except Exception as e:
            logger.error(f"[{self.plugin_name}] 配置文件读取失败: {str(e)}")
            config = {}

        # 基本配置初始化
        self.enable = config.get("enable", True)
        self.command = config.get("command", ["混元画画"])
        self.command_format = config.get("command-format", "请输入：混元画画 <描述文本>")

        # API配置
        api_config = config.get("API", {})
        self.base_url = api_config.get("base_url", "https://hunyuan.tencent.com")
        self.cookies = api_config.get("cookies", "")

        # 初始化用户限流
        rate_limit_config = config.get("rate_limit", {})
        self.cooldown = rate_limit_config.get("cooldown", 30)

    async def _submit_generation_task(self, prompt: str) -> Optional[str]:
        """提交图像生成任务"""
        try:
            # 生成会话ID
            cid = str(uuid.uuid4())

            # 请求数据
            data = {
                "cid": cid,
                "modelId": 136,
                "appId": 136,
                "modelPath": "/openapi/v1/images/portrait/generations",
                "modelName": "hunyuan-image-portrait",
                "n": 1,
                "size": "1024x1024",
                "prompt": prompt,
                "model": "hunyuan-image-portrait"
            }

            # 请求头
            headers = {
                "Accept": "application/json",
                "Content-Type": "application/json",
                "Cookie": self.cookies,
                "Origin": self.base_url,
                "Referer": f"{self.base_url}/modelSquare/home/<USER>",
                "User-Agent": "Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/136.0.0.0 Safari/537.36 Edg/136.0.3240.50",
                "X-Source": "web",
                "X-Requested-With": "XMLHttpRequest"
            }

            async with httpx.AsyncClient(timeout=30.0) as client:
                response = await client.post(
                    f"{self.base_url}/api/vision_platform/generation",
                    json=data,
                    headers=headers
                )

                if response.status_code != 200:
                    logger.error(f"[{self.plugin_name}] 提交任务失败: {response.status_code}")
                    return None

                result = response.json()
                task_id = result.get("taskId")

                if not task_id:
                    logger.error(f"[{self.plugin_name}] 未获取到taskId: {result}")
                    return None


                return task_id

        except Exception as e:
            logger.error(f"[{self.plugin_name}] 提交任务异常: {e}")
            logger.error(traceback.format_exc())
            return None

    async def _query_task_progress(self, task_id: str) -> Optional[Dict[str, Any]]:
        """查询任务进度"""
        try:
            data = {"taskId": task_id}

            headers = {
                "Accept": "application/json",
                "Content-Type": "application/json",
                "Cookie": self.cookies,
                "Origin": self.base_url,
                "Referer": f"{self.base_url}/modelSquare/home/<USER>",
                "User-Agent": "Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/136.0.0.0 Safari/537.36 Edg/136.0.3240.50",
                "X-Source": "web",
                "X-Requested-With": "XMLHttpRequest"
            }

            async with httpx.AsyncClient(timeout=30.0) as client:
                response = await client.post(
                    f"{self.base_url}/api/vision_platform/query_task",
                    json=data,
                    headers=headers
                )

                if response.status_code != 200:
                    logger.error(f"[{self.plugin_name}] 查询任务失败: {response.status_code}")
                    return None

                result = response.json()
                return result

        except Exception as e:
            logger.error(f"[{self.plugin_name}] 查询任务异常: {e}")
            logger.error(traceback.format_exc())
            return None

    async def _poll_task_completion(self, task_id: str) -> Optional[str]:
        """轮询任务直到完成"""
        max_attempts = 60  # 最多轮询60次
        interval = 3  # 每3秒查询一次

        for attempt in range(max_attempts):
            result = await self._query_task_progress(task_id)

            if not result:
                continue

            status = result.get("status")
            progress = result.get("progressValue", 0)



            if result.get("type") == "finish" and status == "succeeded":
                # 任务完成，解析结果
                result_data = result.get("result")
                if result_data:
                    try:
                        if isinstance(result_data, str):
                            result_json = json.loads(result_data)
                        else:
                            result_json = result_data

                        data_list = result_json.get("data", [])
                        if data_list and len(data_list) > 0:
                            image_url = data_list[0].get("url")
                            if image_url:
                                return image_url
                    except Exception as e:
                        logger.error(f"[{self.plugin_name}] 解析结果异常: {e}")

            elif status == "failed":
                logger.error(f"[{self.plugin_name}] 任务失败: {result.get('message', '未知错误')}")
                return None

            # 等待下次查询
            await asyncio.sleep(interval)

        logger.error(f"[{self.plugin_name}] 任务超时")
        return None

    async def _download_image(self, image_url: str) -> Optional[str]:
        """下载生成的图片"""
        try:
            async with httpx.AsyncClient(timeout=60.0) as client:
                response = await client.get(image_url)

                if response.status_code != 200:
                    logger.error(f"[{self.plugin_name}] 下载图片失败: {response.status_code}")
                    return None

                # 保存图片
                filename = f"hunyuan_{int(time.time())}.png"
                image_path = self.temp_dir / filename

                async with aiofiles.open(image_path, "wb") as f:
                    await f.write(response.content)

                logger.info(f"[{self.plugin_name}] 图片保存成功: {image_path}")
                return str(image_path)

        except Exception as e:
            logger.error(f"[{self.plugin_name}] 下载图片异常: {e}")
            logger.error(traceback.format_exc())
            return None

    @on_text_message
    async def handle_text(self, bot: WechatAPIClient, message: dict):
        """处理文本消息"""
        if not self.enable:
            return

        content = str(message["Content"]).strip()
        wxid = message["FromWxid"]
        user_wxid = message["SenderWxid"]

        # 检查是否是插件命令
        command = content.split(" ", 1)
        if command[0] not in self.command:
            return

        # 如果没有提供参数
        if len(command) == 1:
            await bot.send_at_message(
                wxid,
                self.command_format,
                [user_wxid]
            )
            return

        try:
            # 检查用户请求限制
            wait_time = self._check_user_limit(wxid, user_wxid)
            if wait_time > 0:
                await bot.send_at_message(
                    wxid,
                    f"⏳ 请等待 {wait_time:.1f} 秒后再试",
                    [user_wxid]
                )
                return

            # 发送开始提示
            await bot.send_at_message(
                wxid,
                "🎨 正在生成中...",
                [user_wxid]
            )

            # 获取参数
            prompt = command[1].strip()
            logger.info(f"[{self.plugin_name}] 收到请求，用户: {user_wxid}, 提示词: {prompt[:50]}...")

            # 提交生成任务
            task_id = await self._submit_generation_task(prompt)
            if not task_id:
                await bot.send_at_message(
                    wxid,
                    "❌ 生成失败，请稍后重试",
                    [user_wxid]
                )
                return

            # 轮询任务完成
            image_url = await self._poll_task_completion(task_id)
            if not image_url:
                await bot.send_at_message(
                    wxid,
                    "❌ 生成失败，请稍后重试",
                    [user_wxid]
                )
                return

            # 下载图片
            image_path = await self._download_image(image_url)
            if not image_path:
                await bot.send_at_message(
                    wxid,
                    "❌ 生成失败，请稍后重试",
                    [user_wxid]
                )
                return

            # 发送图片
            try:
                async with aiofiles.open(image_path, "rb") as f:
                    image_data = await f.read()

                await bot.send_image_message(wxid, image_data)

            except Exception as e:
                logger.error(f"[{self.plugin_name}] 发送图片失败: {e}")
                await bot.send_at_message(
                    wxid,
                    "❌ 发送失败，请稍后重试",
                    [user_wxid]
                )
            finally:
                # 删除临时文件
                try:
                    if os.path.exists(image_path):
                        os.remove(image_path)
                        logger.info(f"[{self.plugin_name}] 临时文件已删除: {image_path}")
                except Exception as e:
                    logger.warning(f"[{self.plugin_name}] 删除临时文件失败: {e}")

        except Exception as e:
            logger.error(f"[{self.plugin_name}] 处理异常: {str(e)}")
            logger.error(traceback.format_exc())
            await bot.send_at_message(
                wxid,
                "处理失败，等会再试试",
                [user_wxid]
            )

    def _check_user_limit(self, wxid: str, user_wxid: str) -> float:
        """检查用户请求限制，返回需要等待的时间（秒）"""
        user_key = f"{wxid}_{user_wxid}"
        last_request = self.user_last_request.get(user_key, 0)
        current_time = time.time()

        elapsed = current_time - last_request
        self.user_last_request[user_key] = current_time

        if elapsed < self.cooldown:
            return self.cooldown - elapsed

        return 0
