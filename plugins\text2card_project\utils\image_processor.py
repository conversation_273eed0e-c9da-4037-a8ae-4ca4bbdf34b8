from PIL import Image, ImageFilter, ImageEnhance, ImageDraw
from typing import Optional

class ImageProcessor:
    """独立的图片处理工具类
    
    复制并调整自原有的图片处理逻辑，确保不影响原有功能
    """
    
    @staticmethod
    def add_corners(image: Image.Image, radius: int) -> Image.Image:
        """添加圆角
        复制自原有实现，确保不影响原代码
        """
        circle = Image.new('L', (radius * 2, radius * 2), 0)
        draw = ImageDraw.Draw(circle)
        draw.ellipse((0, 0, radius * 2, radius * 2), fill=255)
        
        output = Image.new('RGBA', image.size, (255, 255, 255, 0))
        w, h = image.size
        
        alpha = Image.new('L', image.size, 255)
        alpha.paste(circle.crop((0, 0, radius, radius)), (0, 0))
        alpha.paste(circle.crop((radius, 0, radius * 2, radius)), (w - radius, 0))
        alpha.paste(circle.crop((0, radius, radius, radius * 2)), (0, h - radius))
        alpha.paste(circle.crop((radius, radius, radius * 2, radius * 2)),
                   (w - radius, h - radius))
        
        output.paste(image, (0, 0))
        output.putalpha(alpha)
        
        return output

    @staticmethod
    def enhance_image(image: Image.Image, 
                     contrast: float = 1.2,
                     sharpen: bool = True) -> Image.Image:
        """增强图片
        复制并简化自原有实现
        """
        if contrast != 1.0:
            enhancer = ImageEnhance.Contrast(image)
            image = enhancer.enhance(contrast)
        
        if sharpen:
            image = image.filter(ImageFilter.SHARPEN)
            
        return image

    @staticmethod
    def resize_image(image: Image.Image, 
                    target_width: Optional[int] = None,
                    target_height: Optional[int] = None,
                    maintain_aspect: bool = True) -> Image.Image:
        """调整图片大小
        新增的独立方法
        """
        if not target_width and not target_height:
            return image
            
        if maintain_aspect:
            if target_width:
                w_percent = target_width / float(image.size[0])
                target_height = int(float(image.size[1]) * float(w_percent))
            else:
                h_percent = target_height / float(image.size[1])
                target_width = int(float(image.size[0]) * float(h_percent))
                
        return image.resize((target_width, target_height), 
                          Image.Resampling.LANCZOS) 