[KlingAI]
enable = true
command = ["可灵", "可灵ai", "kl", "KL"]
command-format = """
🎨 可灵AI绘图助手 🎨

使用方法:
  可灵 [提示词]

示例:
  可灵 穿着汉服的少女在樱花树下

参数说明:
  提示词 - 描述你想要生成的图像内容

支持的比例:
  默认为9:16竖图
"""

# 开启国际版支持
international_enable = true
international_command = ["可灵国际版", "可灵国际", "kli", "KLI"]

# 自然化响应设置
natural_response = true  # 启用自然化响应，避免机械化回复

# 自定义配置
[KlingAI.config]
aspect_ratio = "9:16"  # 默认图像比例
image_count = "1"      # 默认生成图片数量
kolors_version = "1.5" # 可灵AI版本
style = "默认"         # 默认风格

# API相关配置
[KlingAI.api]
base_url = "https://klingai.kuaishou.com"
submit_endpoint = "/app/task/submit"
status_endpoint = "/app/task/status"

# Cookie设置 (与示例完全相同)
[KlingAI.cookie]
app_st = "ChdrdWFpc2hvdS5rbGluZ2FpLmFwcC5zdBKgAfHY5GXt5x5t5ddCum2JTCYQZ-mSd7wZCqWEj0DEcwAPNZ1Em4j2KUMi3PGFqvmeD1975B3NbhMFmWTxgryKrO8gFuKBboznivMzXbwMeQPJy4oeToIYU1Xf_pBFy4xaXRergfu0pK0qkRt6S1W8ttbbWrZRyw5VsYqDIAIMADf6TN--L3WMS5yxm9q9dA8UKXuLOstSIrxMZ8dSUY1gd3AaEh6xNdvLseLdsoSfkl86N-L9VCIgXz3-krsNBiYnHhoR2CA5Dk0Nha3-TFpRGzkD_gl6TS4oBTAB"
user_id = "6217807"

# 国际版API配置
[KlingAI.international_api]
base_url = "https://klingai.com"
submit_endpoint = "/api/task/submit"
status_endpoint = "/api/task/status"

# 国际版认证信息
[KlingAI.international_auth]
ksi18n_ai_portal_st = "ChNrc2kxOG4uYWkucG9ydGFsLnN0EqABfHqYZuS3Rvka-4EfJP-hy_AuM4f_NQKk79t-DnbfJI6-Q6TpUVxp0vxVSBxxHuLyjLl2_DnBqY1CICmG7x87Uq-WRpD96qkCB62uQYA9-0UzPjLMJzZaVDVJZy7Y5i-H90pRQBgkFVZ5f9q4sA-YQHRHEr5xsVAFDpIB6D_Jj5BO3VMcdxXlxjEu3AiPLLhOZ5kYx24q9jyoYmPFBoSaEiDMfGuvOYPdBNmLvrXm4jJKeCIgVxD8U-jwI5IzM5CyX-rBcIhNJnxYSzvQeB_Yx7GUdNEoBTAB"
ksi18n_ai_portal_ph = "987bfdcd9e3a6554a3cd2321d90baa29351d"
user_id = "21349856"