2025-07-22 01:50:51 | SUCCESS | 读取主设置成功
2025-07-22 01:50:51 | DEBUG | 最终使用的 Redis 主机地址: 127.0.0.1
2025-07-22 01:50:51 | INFO | 2025/07/22 01:50:51 GetRedisAddr: 127.0.0.1:6379
2025-07-22 01:50:51 | INFO | 2025/07/22 01:50:51 Redis连接成功 | 地址:127.0.0.1:6379 DB:0
2025-07-22 01:50:51 | INFO | 2025/07/22 01:50:51 Server start at :9000
2025-07-22 01:50:51 | SUCCESS | WechatAPI服务已启动
2025-07-22 01:50:52 | INFO | 登录账号信息: wxid: wxid_4usgcju5ey9q29  昵称: 瑶瑶  微信号: Today-KFC  手机号: None
2025-07-22 01:50:52 | INFO | 登录设备信息: device_name: <PERSON>'s Pad  device_id: 49d050a7e94d1ecc40790d74b1c4369f
2025-07-22 01:50:52 | SUCCESS | 登录成功
2025-07-22 01:50:52 | SUCCESS | 已开启自动心跳
2025-07-22 01:50:52 | INFO | 成功加载表情映射文件，共 521 条记录
2025-07-22 01:50:52 | SUCCESS | 数据库初始化成功
2025-07-22 01:50:52 | SUCCESS | 定时任务已启动
2025-07-22 01:50:52 | SUCCESS | 插件 AISignIn 已加载定时任务: {'plugins.AISignIn.main.AISignIn.auto_signin_task'}
2025-07-22 01:50:52 | INFO | 已加载管理员列表: ['wxid_ubbh6q832tcs21', 'admin-wxid']
2025-07-22 01:50:53 | INFO | 播客API初始化成功
2025-07-22 01:50:53 | INFO | 加载配置完成，转换后的数据: {'gh_7057516c9e71': {'name': '唱舞全明星', 'groups': ['27852221909@chatroom', '55878994168@chatroom', '51891329927@chatroom']}, 'gh_d4d21a0e4623': {'name': '两颗桃', 'groups': ['48097389945@chatroom']}, 'gh_410ff2fa6ec9': {'name': 'NGC660 Ai研究院', 'groups': ['47442567074@chatroom']}, 'gh_f78c53c507c0': {'name': '不正常吐槽君', 'groups': ['48097389945@chatroom']}, 'gh_ba3381c847b3': {'name': '唱舞星计划', 'groups': ['51891329927@chatroom']}, 'gh_1a102fb4cad9': {'name': '我在娱乐圈的唯一人脉', 'groups': ['48097389945@chatroom', '27852221909@chatroom']}}
2025-07-22 01:50:53 | INFO | ArticleForwarder插件初始化完成 - 监控配置: {'gh_7057516c9e71': {'name': '唱舞全明星', 'groups': ['27852221909@chatroom', '55878994168@chatroom', '51891329927@chatroom']}, 'gh_d4d21a0e4623': {'name': '两颗桃', 'groups': ['48097389945@chatroom']}, 'gh_410ff2fa6ec9': {'name': 'NGC660 Ai研究院', 'groups': ['47442567074@chatroom']}, 'gh_f78c53c507c0': {'name': '不正常吐槽君', 'groups': ['48097389945@chatroom']}, 'gh_ba3381c847b3': {'name': '唱舞星计划', 'groups': ['51891329927@chatroom']}, 'gh_1a102fb4cad9': {'name': '我在娱乐圈的唯一人脉', 'groups': ['48097389945@chatroom', '27852221909@chatroom']}}
2025-07-22 01:50:53 | DEBUG | [TempFileManager] 添加清理规则: default
2025-07-22 01:50:53 | DEBUG | [TempFileManager] 添加清理规则: images
2025-07-22 01:50:53 | DEBUG | [TempFileManager] 添加清理规则: videos
2025-07-22 01:50:53 | DEBUG | [TempFileManager] 添加清理规则: audio
2025-07-22 01:50:53 | DEBUG | [TempFileManager] 添加清理规则: temp
2025-07-22 01:50:53 | DEBUG | [TempFileManager] 添加清理规则: large_files
2025-07-22 01:50:53 | INFO | [TempFileManager] 临时文件清理任务已启动
2025-07-22 01:50:53 | INFO | [ChatSummary] 数据库初始化成功
2025-07-22 01:50:53 | INFO | 成功加载表情映射文件，共 521 条记录
2025-07-22 01:50:53 | SUCCESS | 插件 GoodMorning 已加载定时任务: {'plugins.GoodMorning.main.GoodMorning.daily_task'}
2025-07-22 01:50:54 | SUCCESS | 插件 News 已加载定时任务: {'plugins.News.main.News.night_news', 'plugins.News.main.News.noon_news'}
2025-07-22 01:50:54 | INFO | [PatReply] AMR文件已存在且是最新的: C:\XYBotV2\data\paiyipai\1_BFB5258B-2B13-4CF4-A40C-5BCCE74E5414_converted.amr
2025-07-22 01:50:54 | DEBUG | [PatReply] 从缓存加载base64: 18228 字符
2025-07-22 01:50:54 | SUCCESS | 插件 QuarkSignIn 已加载定时任务: {'plugins.QuarkSignIn.main.QuarkSignIn.auto_signin_task'}
2025-07-22 01:50:54 | INFO | [QuarkSignIn] 已设置自动签到时间: 08:00
2025-07-22 01:50:54 | INFO | 插件状态: 启用, 检查间隔: 3600秒, 最大重试次数: 3, 重试延迟: 5秒
2025-07-22 01:50:54 | INFO | 已加载管理员列表: ['wxid_ubbh6q832tcs21', 'admin-wxid']
2025-07-22 01:50:54 | INFO | 已加载 2 个改名监控群和 1 个退群监控群
2025-07-22 01:50:54 | INFO | [RenameReminder] 开始启用插件...
2025-07-22 01:50:54 | SUCCESS | 插件 RenameReminder 已加载定时任务: {'plugins.RenameReminder.main.RenameReminder.check_name_changes'}
2025-07-22 01:50:54 | INFO | [RenameReminder] 插件已启用，开始初始化...
2025-07-22 01:50:54 | INFO | [RenameReminder] 支持的命令: 开启改名提醒, 关闭改名提醒, 开启退群提醒, 关闭退群提醒, 查看提醒状态
2025-07-22 01:50:54 | INFO | 已设置检查间隔为 3600 秒
2025-07-22 01:50:54 | INFO | 已加载 2 个监控群，开始更新成员列表
2025-07-22 01:50:54 | DEBUG | 已更新群 51891329927@chatroom 的成员列表
2025-07-22 01:50:54 | DEBUG | 已更新群 27852221909@chatroom 的成员列表
2025-07-22 01:50:54 | SUCCESS | 插件 TimerTask 已加载定时任务: {'plugins.TimerTask.main.TimerTask.check_tasks'}
2025-07-22 01:50:55 | INFO | [VideoDemand] 加载了 128 个视频类别配置
2025-07-22 01:50:55 | INFO | [YaoyaoPlugin] 私聊黑名单用户: ['gh_a5cf07d474f1', 'gh_ba3381c847b3']
2025-07-22 01:50:55 | INFO | [YaoyaoPlugin] 已加载管理员列表: ['wxid_ubbh6q832tcs21', 'admin-wxid']
2025-07-22 01:50:55 | INFO | [yuanbao] 插件初始化完成
2025-07-22 01:50:55 | DEBUG | [yuanbao] 指令: ['元宝', 'yuanbao']
2025-07-22 01:50:55 | DEBUG | [yuanbao] 绘图模型: gpt_175B_0404
2025-07-22 01:50:55 | DEBUG | [yuanbao] 认证信息加载状态: hy_token=True, hy_user=True, agent_id=True, x_uskey=True
2025-07-22 01:50:55 | SUCCESS | 已加载插件: ['AdminPoint', 'AdminSignInReset', 'AdminWhitelist', 'AISignIn', 'AppMessageTester', 'ArticleForwarder', 'BaiduAgentsPlugin', 'BaiduDraw', 'BotStatus', 'ChatSummary', 'DanceSignInPlugin', 'DeepseekPlugin', 'Doubao', 'DoubaoDrawing', 'DoubaoImageRecognition', 'DouBaoImageToImage', 'DouBaoImageToVideo', 'DoubaoVideoSearch', 'DouHui', 'EmojiTestPlugin', 'FixedQuoteTest', 'GetContact', 'Gomoku', 'GoodMorning', 'HunyuanDraw', 'ImageEcho', 'ImageToImage', 'JiemengDraw', 'KeLingImageToImage', 'KlingAI', 'Leaderboard', 'LuckyDraw', 'ManagePlugin', 'MeituAI', 'Menu', 'MiniProgramTester', 'Music', 'News', 'PatReply', 'PointTrade', 'QuarkSignIn', 'QueryPoint', 'RandomMember', 'RandomPicture', 'RenameReminder', 'RevokePlugin', 'RoboNeo', 'SignIn', 'TempFileManagerPlugin', 'TencentLke', 'TimerTask', 'TongyiDraw', 'URLShortener', 'VideoDemand', 'VideoParserPlugin', 'VideoTest', 'VivoAgentsPlugin', 'VoiceTest', 'Warthunder', 'WeatherQuery', 'WelcomePlugin', 'Xunfei', 'YaoyaoPlugin', 'Yuanbao', '抽签', '造梦次元']
2025-07-22 01:50:55 | INFO | 处理堆积消息中
2025-07-22 01:50:55 | SUCCESS | 处理堆积消息完毕
2025-07-22 01:50:55 | SUCCESS | 开始处理消息
2025-07-22 02:02:15 | DEBUG | 收到消息: {'MsgId': 596234638, 'FromUserName': {'string': '55878994168@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 1, 'Content': {'string': 'wxid_ubbh6q832tcs21:\n开启画图模式'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1753120945, 'MsgSource': '<msgsource>\n\t<pua>1</pua>\n\t<alnode>\n\t\t<cf>5</cf>\n\t\t<inlenlist>44</inlenlist>\n\t</alnode>\n\t<eggIncluded>1</eggIncluded>\n\t<silence>0</silence>\n\t<membercount>3</membercount>\n\t<signature>N0_V1_cZh2eoJj|v1_akgGLBUr</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'PushContent': '郭 : 开启画图模式', 'NewMsgId': 3706187926669090849, 'MsgSeq': 871389802}
2025-07-22 02:02:15 | INFO | 收到文本消息: 消息ID:596234638 来自:55878994168@chatroom 发送人:wxid_ubbh6q832tcs21 @:[] 内容:开启画图模式
2025-07-22 02:02:15 | DEBUG | 处理消息内容: '开启画图模式'
2025-07-22 02:02:15 | DEBUG | 消息内容 '开启画图模式' 不匹配任何命令，忽略
2025-07-22 02:02:27 | DEBUG | 收到消息: {'MsgId': 1750105308, 'FromUserName': {'string': '55878994168@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 10002, 'Content': {'string': 'wxid_ubbh6q832tcs21:\n<sysmsg type="revokemsg"><revokemsg><session>55878994168@chatroom</session><msgid>42285184</msgid><newmsgid>3706187926669090849</newmsgid><replacemsg><![CDATA["郭" 撤回了一条消息]]></replacemsg><announcement_id><![CDATA[]]></announcement_id></revokemsg></sysmsg>'}, 'Status': 4, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1753120957, 'MsgSource': '<msgsource>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'NewMsgId': 5074706903673831699, 'MsgSeq': 871389803}
2025-07-22 02:02:27 | DEBUG | 系统消息类型: revokemsg
2025-07-22 02:02:27 | INFO | 未知的系统消息类型: {'MsgId': 1750105308, 'ToWxid': 'wxid_4usgcju5ey9q29', 'MsgType': 10002, 'Content': '\n<sysmsg type="revokemsg"><revokemsg><session>55878994168@chatroom</session><msgid>42285184</msgid><newmsgid>3706187926669090849</newmsgid><replacemsg><![CDATA["郭" 撤回了一条消息]]></replacemsg><announcement_id><![CDATA[]]></announcement_id></revokemsg></sysmsg>', 'Status': 4, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1753120957, 'MsgSource': '<msgsource>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'NewMsgId': 5074706903673831699, 'MsgSeq': 871389803, 'FromWxid': '55878994168@chatroom', 'IsGroup': True, 'SenderWxid': 'wxid_ubbh6q832tcs21'}
2025-07-22 02:02:34 | DEBUG | 收到消息: {'MsgId': 111775588, 'FromUserName': {'string': '55878994168@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 1, 'Content': {'string': 'wxid_ubbh6q832tcs21:\n开启美图模式'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1753120964, 'MsgSource': '<msgsource>\n\t<pua>1</pua>\n\t<eggIncluded>1</eggIncluded>\n\t<silence>0</silence>\n\t<membercount>3</membercount>\n\t<signature>N0_V1_vet+Nt3z|v1_ktB8Jf8B</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'PushContent': '郭 : 开启美图模式', 'NewMsgId': 2216340852641184597, 'MsgSeq': 871389804}
2025-07-22 02:02:34 | INFO | 收到文本消息: 消息ID:111775588 来自:55878994168@chatroom 发送人:wxid_ubbh6q832tcs21 @:[] 内容:开启美图模式
2025-07-22 02:02:35 | INFO | 发送文字消息: 对方wxid:55878994168@chatroom at: 内容:✅ 已进入美图AI模式，正在初始化...
2025-07-22 02:02:43 | INFO | 发送文字消息: 对方wxid:55878994168@chatroom at: 内容:你好呀！我是你的影像创作小助手RoboNeo~ 无论是修图、做设计还是视频创作，只要你说出想法，我就能帮你轻松实现！今天有什么创意想实现吗？✨
2025-07-22 02:02:43 | DEBUG | 处理消息内容: '开启美图模式'
2025-07-22 02:02:43 | DEBUG | 消息内容 '开启美图模式' 不匹配任何命令，忽略
2025-07-22 02:02:45 | DEBUG | 收到消息: {'MsgId': 1291694872, 'FromUserName': {'string': '55878994168@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 1, 'Content': {'string': 'wxid_ubbh6q832tcs21:\n画一个人'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1753120976, 'MsgSource': '<msgsource>\n\t<pua>1</pua>\n\t<eggIncluded>1</eggIncluded>\n\t<silence>0</silence>\n\t<membercount>3</membercount>\n\t<signature>N0_V1_syhr+y8r|v1_dPESJeKv</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'PushContent': '郭 : 画一个人', 'NewMsgId': 8938359868361543912, 'MsgSeq': 871389809}
2025-07-22 02:02:45 | INFO | 收到文本消息: 消息ID:1291694872 来自:55878994168@chatroom 发送人:wxid_ubbh6q832tcs21 @:[] 内容:画一个人
2025-07-22 02:02:58 | INFO | 发送文字消息: 对方wxid:55878994168@chatroom at: 内容:🤖 AI询问: 你希望这个角色是什么风格？（例如：二次元动漫、写实风、Q版卡通、赛博朋克等）
这个角色需要突出哪些特征？（例如：年龄、性别、职业身份、特殊能力等）
角色将用于什么场景？（例如：游戏立绘、虚拟主播形象、社交媒体头像等）
2025-07-22 02:02:58 | DEBUG | 处理消息内容: '画一个人'
2025-07-22 02:02:58 | DEBUG | 消息内容 '画一个人' 不匹配任何命令，忽略
2025-07-22 02:03:05 | DEBUG | 收到消息: {'MsgId': 641947680, 'FromUserName': {'string': '55878994168@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 1, 'Content': {'string': 'wxid_ubbh6q832tcs21:\n汉服美女'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1753120996, 'MsgSource': '<msgsource>\n\t<pua>1</pua>\n\t<eggIncluded>1</eggIncluded>\n\t<silence>0</silence>\n\t<membercount>3</membercount>\n\t<signature>N0_V1_DdxeCM67|v1_89nn+XzQ</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'PushContent': '郭 : 汉服美女', 'NewMsgId': 8810870972796008961, 'MsgSeq': 871389812}
2025-07-22 02:03:05 | INFO | 收到文本消息: 消息ID:641947680 来自:55878994168@chatroom 发送人:wxid_ubbh6q832tcs21 @:[] 内容:汉服美女
2025-07-22 02:03:13 | INFO | 发送文字消息: 对方wxid:55878994168@chatroom at: 内容:🤖 AI询问: 你希望汉服美女采用什么艺术风格？（例如：古风水墨、工笔重彩、二次元动漫等）
需要表现什么具体场景或动作？（例如：执扇赏花、抚琴吟诗、撑伞漫步等）
对服饰朝代有特别要求吗？（例如：唐制齐胸襦裙、明制马面裙等）
2025-07-22 02:03:13 | DEBUG | 处理消息内容: '汉服美女'
2025-07-22 02:03:13 | DEBUG | 消息内容 '汉服美女' 不匹配任何命令，忽略
2025-07-22 02:03:37 | DEBUG | 收到消息: {'MsgId': 1886229755, 'FromUserName': {'string': '55878994168@chatroom'}, 'ToWxid': {'string': 'wxid_4usgcju5ey9q29'}, 'MsgType': 1, 'Content': {'string': 'wxid_ubbh6q832tcs21:\n真人形象'}, 'Status': 3, 'ImgStatus': 1, 'ImgBuf': {'iLen': 0}, 'CreateTime': 1753121028, 'MsgSource': '<msgsource>\n\t<pua>1</pua>\n\t<eggIncluded>1</eggIncluded>\n\t<silence>0</silence>\n\t<membercount>3</membercount>\n\t<signature>N0_V1_xYf+v5R/|v1_dZsTdJ34</signature>\n\t<tmp_node>\n\t\t<publisher-id></publisher-id>\n\t</tmp_node>\n</msgsource>\n', 'PushContent': '郭 : 真人形象', 'NewMsgId': 2330659840687568392, 'MsgSeq': 871389815}
2025-07-22 02:03:37 | INFO | 收到文本消息: 消息ID:1886229755 来自:55878994168@chatroom 发送人:wxid_ubbh6q832tcs21 @:[] 内容:真人形象
2025-07-22 02:04:00 | INFO | 发送文字消息: 对方wxid:55878994168@chatroom at: 内容:正在创作写风格的汉服美女形象，将突出传统服饰细节和古典氛围。
2025-07-22 02:04:02 | INFO | 发送图片消息: 对方wxid:55878994168@chatroom 图片base64略
2025-07-22 02:04:03 | INFO | 发送文字消息: 对方wxid:55878994168@chatroom at: 内容:✅ 图片已生成
2025-07-22 02:04:05 | INFO | 发送文字消息: 对方wxid:55878994168@chatroom at: 内容:已经为你创作完成写实风格的汉服美女形象！她身着精致的明制马面裙，在古典园林中执扇赏花，光影效果展现了传统服饰的华美质感。这个形象既保留了传统韵味，又具有现代摄影的写实美感。如果需要调整任何节，比如服饰款式、场景或表情，随时告诉我哦~
2025-07-22 02:04:05 | DEBUG | 处理消息内容: '真人形象'
2025-07-22 02:04:05 | DEBUG | 消息内容 '真人形象' 不匹配任何命令，忽略
